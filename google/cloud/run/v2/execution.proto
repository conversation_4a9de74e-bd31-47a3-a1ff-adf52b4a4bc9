// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/launch_stage.proto";
import "google/api/resource.proto";
import "google/cloud/run/v2/condition.proto";
import "google/cloud/run/v2/task_template.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "ExecutionProto";
option java_package = "com.google.cloud.run.v2";

// Cloud Run Execution Control Plane API.
service Executions {
  option (google.api.default_host) = "run.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets information about an Execution.
  rpc GetExecution(GetExecutionRequest) returns (Execution) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/jobs/*/executions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Executions from a Job. Results are sorted by creation time,
  // descending.
  rpc ListExecutions(ListExecutionsRequest) returns (ListExecutionsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*/jobs/*}/executions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes an Execution.
  rpc DeleteExecution(DeleteExecutionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/jobs/*/executions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Execution"
      metadata_type: "Execution"
    };
  }

  // Cancels an Execution.
  rpc CancelExecution(CancelExecutionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/jobs/*/executions/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Execution"
      metadata_type: "Execution"
    };
  }
}

// Request message for obtaining a Execution by its full name.
message GetExecutionRequest {
  // Required. The full name of the Execution.
  // Format:
  // `projects/{project}/locations/{location}/jobs/{job}/executions/{execution}`,
  // where `{project}` can be project id or number.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Execution" }
  ];
}

// Request message for retrieving a list of Executions.
message ListExecutionsRequest {
  // Required. The Execution from which the Executions should be listed.
  // To list all Executions across Jobs, use "-" instead of Job name.
  // Format: `projects/{project}/locations/{location}/jobs/{job}`, where
  // `{project}` can be project id or number.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "run.googleapis.com/Execution"
    }
  ];

  // Maximum number of Executions to return in this call.
  int32 page_size = 2;

  // A page token received from a previous call to ListExecutions.
  // All other parameters must match.
  string page_token = 3;

  // If true, returns deleted (but unexpired) resources along with active ones.
  bool show_deleted = 4;
}

// Response message containing a list of Executions.
message ListExecutionsResponse {
  // The resulting list of Executions.
  repeated Execution executions = 1;

  // A token indicating there are more items than page_size. Use it in the next
  // ListExecutions request to continue.
  string next_page_token = 2;
}

// Request message for deleting an Execution.
message DeleteExecutionRequest {
  // Required. The name of the Execution to delete.
  // Format:
  // `projects/{project}/locations/{location}/jobs/{job}/executions/{execution}`,
  // where `{project}` can be project id or number.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Execution" }
  ];

  // Indicates that the request should be validated without actually
  // deleting any resources.
  bool validate_only = 2;

  // A system-generated fingerprint for this version of the resource.
  // This may be used to detect modification conflict during updates.
  string etag = 3;
}

// Request message for deleting an Execution.
message CancelExecutionRequest {
  // Required. The name of the Execution to cancel.
  // Format:
  // `projects/{project}/locations/{location}/jobs/{job}/executions/{execution}`,
  // where `{project}` can be project id or number.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Execution" }
  ];

  // Indicates that the request should be validated without actually
  // cancelling any resources.
  bool validate_only = 2;

  // A system-generated fingerprint for this version of the resource.
  // This may be used to detect modification conflict during updates.
  string etag = 3;
}

// Execution represents the configuration of a single execution. A execution an
// immutable resource that references a container image which is run to
// completion.
message Execution {
  option (google.api.resource) = {
    type: "run.googleapis.com/Execution"
    pattern: "projects/{project}/locations/{location}/jobs/{job}/executions/{execution}"
    style: DECLARATIVE_FRIENDLY
  };

  // Output only. The unique name of this Execution.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server assigned unique identifier for the Execution. The value
  // is a UUID4 string and guaranteed to remain unchanged until the resource is
  // deleted.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A number that monotonically increases every time the user
  // modifies the desired state.
  int64 generation = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unstructured key value map that can be used to organize and
  // categorize objects. User-provided labels are shared with Google's billing
  // system, so they can be used to filter, or break down billing charges by
  // team, component, environment, state, etc. For more information, visit
  // https://cloud.google.com/resource-manager/docs/creating-managing-labels or
  // https://cloud.google.com/run/docs/configuring/labels
  map<string, string> labels = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unstructured key value map that may
  // be set by external tools to store and arbitrary metadata.
  // They are not queryable and should be preserved
  // when modifying objects.
  map<string, string> annotations = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Represents time when the execution was acknowledged by the
  // execution controller. It is not guaranteed to be set in happens-before
  // order across separate operations.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Represents time when the execution started to run.
  // It is not guaranteed to be set in happens-before order across separate
  // operations.
  google.protobuf.Timestamp start_time = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Represents time when the execution was completed. It is not
  // guaranteed to be set in happens-before order across separate operations.
  google.protobuf.Timestamp completion_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last-modified time.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For a deleted resource, the deletion time. It is only
  // populated as a response to a Delete request.
  google.protobuf.Timestamp delete_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For a deleted resource, the time after which it will be
  // permamently deleted. It is only populated as a response to a Delete
  // request.
  google.protobuf.Timestamp expire_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The least stable launch stage needed to create this resource, as defined by
  // [Google Cloud Platform Launch
  // Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports
  // `ALPHA`, `BETA`, and `GA`.
  // <p>Note that this value might not be what was used
  // as input. For example, if ALPHA was provided as input in the parent
  // resource, but only BETA and GA-level features are were, this field will be
  // BETA.
  google.api.LaunchStage launch_stage = 11;

  // Output only. The name of the parent Job.
  string job = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "run.googleapis.com/Job" }
  ];

  // Output only. Specifies the maximum desired number of tasks the execution
  // should run at any given time. Must be <= task_count. The actual number of
  // tasks running in steady state will be less than this number when
  // ((.spec.task_count - .status.successful) < .spec.parallelism), i.e. when
  // the work left to do is less than max parallelism.
  int32 parallelism = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Specifies the desired number of tasks the execution should
  // run. Setting to 1 means that parallelism is limited to 1 and the success of
  // that task signals the success of the execution.
  int32 task_count = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The template used to create tasks for this execution.
  TaskTemplate template = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether the resource's reconciliation is still in
  // progress. See comments in `Job.reconciling` for additional information on
  // reconciliation process in Cloud Run.
  bool reconciling = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Condition of this Execution, containing its readiness
  // status, and detailed error information in case it did not reach the desired
  // state.
  repeated Condition conditions = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The generation of this Execution. See comments in
  // `reconciling` for additional information on reconciliation process in Cloud
  // Run.
  int64 observed_generation = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of actively running tasks.
  int32 running_count = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of tasks which reached phase Succeeded.
  int32 succeeded_count = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of tasks which reached phase Failed.
  int32 failed_count = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of tasks which reached phase Cancelled.
  int32 cancelled_count = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of tasks which have retried at least once.
  int32 retried_count = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URI where logs for this execution can be found in Cloud
  // Console.
  string log_uri = 26 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 27 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A system-generated fingerprint for this version of the
  // resource. May be used to detect modification conflict during updates.
  string etag = 99 [(google.api.field_behavior) = OUTPUT_ONLY];
}
