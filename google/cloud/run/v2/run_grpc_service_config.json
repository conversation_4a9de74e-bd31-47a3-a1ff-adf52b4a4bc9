{"methodConfig": [{"name": [{"service": "google.cloud.run.v2.Services", "method": "ListServices"}, {"service": "google.cloud.run.v2.Services", "method": "GetService"}], "timeout": "10s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.run.v2.Services", "method": "CreateService"}, {"service": "google.cloud.run.v2.Services", "method": "UpdateService"}], "timeout": "15s"}, {"name": [{"service": "google.cloud.run.v2.Services", "method": "DeleteService"}], "timeout": "10s"}]}