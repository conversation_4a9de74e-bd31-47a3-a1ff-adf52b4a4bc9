// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/launch_stage.proto";
import "google/api/resource.proto";
import "google/api/routing.proto";
import "google/cloud/run/v2/condition.proto";
import "google/cloud/run/v2/execution.proto";
import "google/cloud/run/v2/execution_template.proto";
import "google/cloud/run/v2/k8s.min.proto";
import "google/cloud/run/v2/vendor_settings.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "JobProto";
option java_package = "com.google.cloud.run.v2";

// Cloud Run Job Control Plane API.
service Jobs {
  option (google.api.default_host) = "run.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a Job.
  rpc CreateJob(CreateJobRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/jobs"
      body: "job"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "parent,job,job_id";
    option (google.longrunning.operation_info) = {
      response_type: "Job"
      metadata_type: "Job"
    };
  }

  // Gets information about a Job.
  rpc GetJob(GetJobRequest) returns (Job) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/jobs/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/**"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Jobs. Results are sorted by creation time, descending.
  rpc ListJobs(ListJobsRequest) returns (ListJobsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/jobs"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a Job.
  rpc UpdateJob(UpdateJobRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{job.name=projects/*/locations/*/jobs/*}"
      body: "job"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "job.name"
        path_template: "projects/*/locations/{location=*}/**"
      }
    };
    option (google.api.method_signature) = "job";
    option (google.longrunning.operation_info) = {
      response_type: "Job"
      metadata_type: "Job"
    };
  }

  // Deletes a Job.
  rpc DeleteJob(DeleteJobRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/jobs/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/**"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Job"
      metadata_type: "Job"
    };
  }

  // Triggers creation of a new Execution of this Job.
  rpc RunJob(RunJobRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/jobs/*}:run"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/**"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Execution"
      metadata_type: "Execution"
    };
  }

  // Gets the IAM Access Control policy currently in effect for the given Job.
  // This result does not include any inherited policies.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      get: "/v2/{resource=projects/*/locations/*/jobs/*}:getIamPolicy"
    };
  }

  // Sets the IAM Access control policy for the specified Job. Overwrites
  // any existing policy.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v2/{resource=projects/*/locations/*/jobs/*}:setIamPolicy"
      body: "*"
    };
  }

  // Returns permissions that a caller has on the specified Project.
  //
  // There are no permissions required for making this API call.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v2/{resource=projects/*/locations/*/jobs/*}:testIamPermissions"
      body: "*"
    };
  }
}

// Request message for creating a Job.
message CreateJobRequest {
  // Required. The location and project in which this Job should be created.
  // Format: projects/{project}/locations/{location}, where {project} can be
  // project id or number.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "run.googleapis.com/Job" }
  ];

  // Required. The Job instance to create.
  Job job = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The unique identifier for the Job. The name of the job becomes
  // {parent}/jobs/{job_id}.
  string job_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Indicates that the request should be validated and default values
  // populated, without persisting the request or creating any resources.
  bool validate_only = 4;
}

// Request message for obtaining a Job by its full name.
message GetJobRequest {
  // Required. The full name of the Job.
  // Format: projects/{project}/locations/{location}/jobs/{job}, where {project}
  // can be project id or number.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Job" }
  ];
}

// Request message for updating a Job.
message UpdateJobRequest {
  // Required. The Job to be updated.
  Job job = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates that the request should be validated and default values
  // populated, without persisting the request or updating any resources.
  bool validate_only = 3;

  // Optional. If set to true, and if the Job does not exist, it will create a
  // new one. Caller must have both create and update permissions for this call
  // if this is set to true.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for retrieving a list of Jobs.
message ListJobsRequest {
  // Required. The location and project to list resources on.
  // Format: projects/{project}/locations/{location}, where {project} can be
  // project id or number.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "run.googleapis.com/Job" }
  ];

  // Maximum number of Jobs to return in this call.
  int32 page_size = 2;

  // A page token received from a previous call to ListJobs.
  // All other parameters must match.
  string page_token = 3;

  // If true, returns deleted (but unexpired) resources along with active ones.
  bool show_deleted = 4;
}

// Response message containing a list of Jobs.
message ListJobsResponse {
  // The resulting list of Jobs.
  repeated Job jobs = 1;

  // A token indicating there are more items than page_size. Use it in the next
  // ListJobs request to continue.
  string next_page_token = 2;
}

// Request message to delete a Job by its full name.
message DeleteJobRequest {
  // Required. The full name of the Job.
  // Format: projects/{project}/locations/{location}/jobs/{job}, where {project}
  // can be project id or number.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Job" }
  ];

  // Indicates that the request should be validated without actually
  // deleting any resources.
  bool validate_only = 3;

  // A system-generated fingerprint for this version of the
  // resource. May be used to detect modification conflict during updates.
  string etag = 4;
}

// Request message to create a new Execution of a Job.
message RunJobRequest {
  // RunJob Overrides that contains Execution fields to be overridden.
  message Overrides {
    // Per-container override specification.
    message ContainerOverride {
      // The name of the container specified as a DNS_LABEL.
      string name = 1;

      // Optional. Arguments to the entrypoint. Will replace existing args for
      // override.
      repeated string args = 2 [(google.api.field_behavior) = OPTIONAL];

      // List of environment variables to set in the container. Will be merged
      // with existing env for override.
      repeated EnvVar env = 3;

      // Optional. True if the intention is to clear out existing args list.
      bool clear_args = 4 [(google.api.field_behavior) = OPTIONAL];
    }

    // Per container override specification.
    repeated ContainerOverride container_overrides = 1;

    // Optional. The desired number of tasks the execution should run. Will
    // replace existing task_count value.
    int32 task_count = 2 [(google.api.field_behavior) = OPTIONAL];

    // Duration in seconds the task may be active before the system will
    // actively try to mark it failed and kill associated containers. Will
    // replace existing timeout_seconds value.
    google.protobuf.Duration timeout = 4;
  }

  // Required. The full name of the Job.
  // Format: projects/{project}/locations/{location}/jobs/{job}, where {project}
  // can be project id or number.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Job" }
  ];

  // Indicates that the request should be validated without actually
  // deleting any resources.
  bool validate_only = 2;

  // A system-generated fingerprint for this version of the
  // resource. May be used to detect modification conflict during updates.
  string etag = 3;

  // Overrides specification for a given execution of a job. If provided,
  // overrides will be applied to update the execution or task spec.
  Overrides overrides = 4;
}

// Job represents the configuration of a single job, which references a
// container image that is run to completion.
message Job {
  option (google.api.resource) = {
    type: "run.googleapis.com/Job"
    pattern: "projects/{project}/locations/{location}/jobs/{job}"
    style: DECLARATIVE_FRIENDLY
  };

  // The fully qualified name of this Job.
  //
  // Format:
  // projects/{project}/locations/{location}/jobs/{job}
  string name = 1;

  // Output only. Server assigned unique identifier for the Execution. The value
  // is a UUID4 string and guaranteed to remain unchanged until the resource is
  // deleted.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A number that monotonically increases every time the user
  // modifies the desired state.
  int64 generation = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Unstructured key value map that can be used to organize and categorize
  // objects.
  // User-provided labels are shared with Google's billing system, so they can
  // be used to filter, or break down billing charges by team, component,
  // environment, state, etc. For more information, visit
  // https://cloud.google.com/resource-manager/docs/creating-managing-labels or
  // https://cloud.google.com/run/docs/configuring/labels.
  //
  // <p>Cloud Run API v2 does not support labels with `run.googleapis.com`,
  // `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev`
  // namespaces, and they will be rejected. All system labels in v1 now have a
  // corresponding field in v2 Job.
  map<string, string> labels = 4;

  // Unstructured key value map that may
  // be set by external tools to store and arbitrary metadata.
  // They are not queryable and should be preserved
  // when modifying objects.
  //
  // <p>Cloud Run API v2 does not support annotations with `run.googleapis.com`,
  // `cloud.googleapis.com`, `serving.knative.dev`, or `autoscaling.knative.dev`
  // namespaces, and they will be rejected on new resources. All system
  // annotations in v1 now have a corresponding field in v2 Job.
  //
  // <p>This field follows Kubernetes annotations' namespacing, limits, and
  // rules.
  map<string, string> annotations = 5;

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last-modified time.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The deletion time. It is only populated as a response to a
  // Delete request.
  google.protobuf.Timestamp delete_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. For a deleted resource, the time after which it will be
  // permamently deleted.
  google.protobuf.Timestamp expire_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the authenticated creator.
  string creator = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the last authenticated modifier.
  string last_modifier = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Arbitrary identifier for the API client.
  string client = 12;

  // Arbitrary version identifier for the API client.
  string client_version = 13;

  // The launch stage as defined by [Google Cloud Platform
  // Launch Stages](https://cloud.google.com/terms/launch-stages).
  // Cloud Run supports `ALPHA`, `BETA`, and `GA`. If no value is specified, GA
  // is assumed.
  // Set the launch stage to a preview stage on input to allow use of preview
  // features in that stage. On read (or output), describes whether the resource
  // uses preview features.
  // <p>
  // For example, if ALPHA is provided as input, but only BETA and GA-level
  // features are used, this field will be BETA on output.
  google.api.LaunchStage launch_stage = 14;

  // Settings for the Binary Authorization feature.
  BinaryAuthorization binary_authorization = 15;

  // Required. The template used to create executions for this Job.
  ExecutionTemplate template = 16 [(google.api.field_behavior) = REQUIRED];

  // Output only. The generation of this Job. See comments in `reconciling` for
  // additional information on reconciliation process in Cloud Run.
  int64 observed_generation = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Condition of this Job, containing its readiness status,
  // and detailed error information in case it did not reach the desired state.
  Condition terminal_condition = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Conditions of all other associated sub-resources. They
  // contain additional diagnostics information in case the Job does not reach
  // its desired state. See comments in `reconciling` for additional information
  // on reconciliation process in Cloud Run.
  repeated Condition conditions = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of executions created for this job.
  int32 execution_count = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the last created execution.
  ExecutionReference latest_created_execution = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Returns true if the Job is currently being acted upon by the
  // system to bring it into the desired state.
  //
  // When a new Job is created, or an existing one is updated, Cloud Run
  // will asynchronously perform all necessary steps to bring the Job to the
  // desired state. This process is called reconciliation.
  // While reconciliation is in process, `observed_generation` and
  // `latest_succeeded_execution`, will have transient values that might
  // mismatch the intended state: Once reconciliation is over (and this field is
  // false), there are two possible outcomes: reconciliation succeeded and the
  // state matches the Job, or there was an error,  and reconciliation failed.
  // This state can be found in `terminal_condition.state`.
  //
  // If reconciliation succeeded, the following fields will match:
  // `observed_generation` and `generation`, `latest_succeeded_execution` and
  // `latest_created_execution`.
  //
  // If reconciliation failed, `observed_generation` and
  // `latest_succeeded_execution` will have the state of the last succeeded
  // execution or empty for newly created Job. Additional information on the
  // failure can be found in `terminal_condition` and `conditions`.
  bool reconciling = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 25 [(google.api.field_behavior) = OUTPUT_ONLY];

  oneof create_execution {
    // A unique string used as a suffix creating a new execution. The Job will
    // become ready when the execution is successfully started.
    // The sum of job name and token length must be fewer than 63 characters.
    string start_execution_token = 26;

    // A unique string used as a suffix for creating a new execution. The Job
    // will become ready when the execution is successfully completed.
    // The sum of job name and token length must be fewer than 63 characters.
    string run_execution_token = 27;
  }

  // Output only. A system-generated fingerprint for this version of the
  // resource. May be used to detect modification conflict during updates.
  string etag = 99 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Reference to an Execution. Use /Executions.GetExecution with the given name
// to get full execution including the latest status.
message ExecutionReference {
  // Possible execution completion status.
  enum CompletionStatus {
    // The default value. This value is used if the state is omitted.
    COMPLETION_STATUS_UNSPECIFIED = 0;

    // Job execution has succeeded.
    EXECUTION_SUCCEEDED = 1;

    // Job execution has failed.
    EXECUTION_FAILED = 2;

    // Job execution is running normally.
    EXECUTION_RUNNING = 3;

    // Waiting for backing resources to be provisioned.
    EXECUTION_PENDING = 4;

    // Job execution has been cancelled by the user.
    EXECUTION_CANCELLED = 5;
  }

  // Name of the execution.
  string name = 1 [
    (google.api.resource_reference) = { type: "run.googleapis.com/Execution" }
  ];

  // Creation timestamp of the execution.
  google.protobuf.Timestamp create_time = 2;

  // Creation timestamp of the execution.
  google.protobuf.Timestamp completion_time = 3;

  // The deletion time of the execution. It is only
  // populated as a response to a Delete request.
  google.protobuf.Timestamp delete_time = 5;

  // Status for the execution completion.
  CompletionStatus completion_status = 4;
}
