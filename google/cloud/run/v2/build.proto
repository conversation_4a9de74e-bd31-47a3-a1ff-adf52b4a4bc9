// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "BuildProto";
option java_package = "com.google.cloud.run.v2";
option (google.api.resource_definition) = {
  type: "cloudbuild.googleapis.com/WorkerPool"
  pattern: "projects/{project}/locations/{location}/workerPools/{worker_pool}"
};

// Cloud Run Build Control Plane API
service Builds {
  option (google.api.default_host) = "run.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Submits a build in a given project.
  rpc SubmitBuild(SubmitBuildRequest) returns (SubmitBuildResponse) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/builds:submit"
      body: "*"
    };
  }
}

// Request message for submitting a Build.
message SubmitBuildRequest {
  // Build the source using Docker. This means the source has a Dockerfile.
  message DockerBuild {}

  // Build the source using Buildpacks.
  message BuildpacksBuild {
    // The runtime name, e.g. 'go113'. Leave blank for generic builds.
    string runtime = 1 [deprecated = true];

    // Optional. Name of the function target if the source is a function source.
    // Required for function builds.
    string function_target = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. cache_image_uri is the GCR/AR URL where the cache image will be
    // stored. cache_image_uri is optional and omitting it will disable caching.
    // This URL must be stable across builds. It is used to derive a
    // build-specific temporary URL by substituting the tag with the build ID.
    // The build will clean up the temporary image on a best-effort basis.
    string cache_image_uri = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The base image used to opt into automatic base image updates.
    string base_image = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. User-provided build-time environment variables.
    map<string, string> environment_variables = 5
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Whether or not the application container will be enrolled in
    // automatic base image updates. When true, the application will be built on
    // a scratch base image, so the base layers can be appended at run time.
    bool enable_automatic_updates = 6 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The project and location to build in. Location must be a region,
  // e.g., 'us-central1' or 'global' if the global builder is to be used.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Location of source.
  oneof source {
    // Required. Source for the build.
    StorageSource storage_source = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. Artifact Registry URI to store the built image.
  string image_uri = 3 [(google.api.field_behavior) = REQUIRED];

  // Build type must be one of the following.
  oneof build_type {
    // Build the source using Buildpacks.
    BuildpacksBuild buildpack_build = 4;

    // Build the source using Docker. This means the source has a Dockerfile.
    DockerBuild docker_build = 5;
  }

  // Optional. The service account to use for the build. If not set, the default
  // Cloud Build service account for the project will be used.
  string service_account = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Name of the Cloud Build Custom Worker Pool that should be used to
  // build the function. The format of this field is
  // `projects/{project}/locations/{region}/workerPools/{workerPool}` where
  // `{project}` and `{region}` are the project id and region respectively where
  // the worker pool is defined and `{workerPool}` is the short name of the
  // worker pool.
  string worker_pool = 7 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/WorkerPool"
    }
  ];

  // Optional. Additional tags to annotate the build.
  repeated string tags = 8 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for submitting a Build.
message SubmitBuildResponse {
  // Cloud Build operation to be polled via CloudBuild API.
  google.longrunning.Operation build_operation = 1;

  // URI of the base builder image in Artifact Registry being used in the build.
  // Used to opt into automatic base image updates.
  string base_image_uri = 2;

  // Warning message for the base image.
  string base_image_warning = 3;
}

// Location of the source in an archive file in Google Cloud Storage.
message StorageSource {
  // Required. Google Cloud Storage bucket containing the source (see
  // [Bucket Name
  // Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Google Cloud Storage object containing the source.
  //
  // This object must be a gzipped archive file (`.tar.gz`) containing source to
  // build.
  string object = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Google Cloud Storage generation for the object. If the generation
  // is omitted, the latest generation will be used.
  int64 generation = 3 [(google.api.field_behavior) = OPTIONAL];
}
