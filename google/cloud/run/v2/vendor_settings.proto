// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "VendorSettingsProto";
option java_package = "com.google.cloud.run.v2";
option (google.api.resource_definition) = {
  type: "binaryauthorization.googleapis.com/Policy"
  pattern: "projects/{project}/policy"
  pattern: "locations/{location}/policy"
};
option (google.api.resource_definition) = {
  type: "networkservices.googleapis.com/Mesh"
  pattern: "projects/{project}/locations/{location}/meshes/{mesh}"
};

// VPC Access settings. For more information on sending traffic to a VPC
// network, visit https://cloud.google.com/run/docs/configuring/connecting-vpc.
message VpcAccess {
  // Egress options for VPC access.
  enum VpcEgress {
    // Unspecified
    VPC_EGRESS_UNSPECIFIED = 0;

    // All outbound traffic is routed through the VPC connector.
    ALL_TRAFFIC = 1;

    // Only private IP ranges are routed through the VPC connector.
    PRIVATE_RANGES_ONLY = 2;
  }

  // Direct VPC egress settings.
  message NetworkInterface {
    // Optional. The VPC network that the Cloud Run resource will be able to
    // send traffic to. At least one of network or subnetwork must be specified.
    // If both network and subnetwork are specified, the given VPC subnetwork
    // must belong to the given VPC network. If network is not specified, it
    // will be looked up from the subnetwork.
    string network = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The VPC subnetwork that the Cloud Run resource will get IPs
    // from. At least one of network or subnetwork must be specified. If both
    // network and subnetwork are specified, the given VPC subnetwork must
    // belong to the given VPC network. If subnetwork is not specified, the
    // subnetwork with the same name with the network will be used.
    string subnetwork = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Network tags applied to this Cloud Run resource.
    repeated string tags = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // VPC Access connector name.
  // Format: `projects/{project}/locations/{location}/connectors/{connector}`,
  // where `{project}` can be project id or number.
  // For more information on sending traffic to a VPC network via a connector,
  // visit https://cloud.google.com/run/docs/configuring/vpc-connectors.
  string connector = 1 [(google.api.resource_reference) = {
    type: "vpcaccess.googleapis.com/Connector"
  }];

  // Optional. Traffic VPC egress settings. If not provided, it defaults to
  // PRIVATE_RANGES_ONLY.
  VpcEgress egress = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Direct VPC egress settings. Currently only single network
  // interface is supported.
  repeated NetworkInterface network_interfaces = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Settings for Binary Authorization feature.
message BinaryAuthorization {
  oneof binauthz_method {
    // Optional. If True, indicates to use the default project's binary
    // authorization policy. If False, binary authorization will be disabled.
    bool use_default = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The path to a binary authorization policy.
    // Format: `projects/{project}/platforms/cloudRun/{policy-name}`
    string policy = 3 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "binaryauthorization.googleapis.com/Policy"
      }
    ];
  }

  // Optional. If present, indicates to use Breakglass using this justification.
  // If use_default is False, then it must be empty.
  // For more information on breakglass, see
  // https://cloud.google.com/binary-authorization/docs/using-breakglass
  string breakglass_justification = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Settings for revision-level scaling settings.
message RevisionScaling {
  // Optional. Minimum number of serving instances that this resource should
  // have.
  int32 min_instance_count = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum number of serving instances that this resource should
  // have. When unspecified, the field is set to the server default value of
  // 100. For more information see
  // https://cloud.google.com/run/docs/configuring/max-instances
  int32 max_instance_count = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Settings for Cloud Service Mesh. For more information see
// https://cloud.google.com/service-mesh/docs/overview.
message ServiceMesh {
  // The Mesh resource name. Format:
  // `projects/{project}/locations/global/meshes/{mesh}`, where `{project}` can
  // be project id or number.
  string mesh = 1 [(google.api.resource_reference) = {
    type: "networkservices.googleapis.com/Mesh"
  }];
}

// Scaling settings applied at the service level rather than
// at the revision level.
message ServiceScaling {
  // The scaling mode for the service. If not provided, it defaults to
  // AUTOMATIC.
  enum ScalingMode {
    // Unspecified.
    SCALING_MODE_UNSPECIFIED = 0;

    // Scale based on traffic between min and max instances.
    AUTOMATIC = 1;

    // Scale to exactly min instances and ignore max instances.
    MANUAL = 2;
  }

  // Optional. total min instances for the service. This number of instances is
  // divided among all revisions with specified traffic based on the percent
  // of traffic they are receiving.
  int32 min_instance_count = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The scaling mode for the service.
  ScalingMode scaling_mode = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. total instance count for the service in manual scaling mode. This
  // number of instances is divided among all revisions with specified traffic
  // based on the percent of traffic they are receiving.
  optional int32 manual_instance_count = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Hardware constraints configuration.
message NodeSelector {
  // Required. GPU accelerator type to attach to an instance.
  string accelerator = 1 [(google.api.field_behavior) = REQUIRED];
}

// Allowed ingress traffic for the Container.
enum IngressTraffic {
  // Unspecified
  INGRESS_TRAFFIC_UNSPECIFIED = 0;

  // All inbound traffic is allowed.
  INGRESS_TRAFFIC_ALL = 1;

  // Only internal traffic is allowed.
  INGRESS_TRAFFIC_INTERNAL_ONLY = 2;

  // Both internal and Google Cloud Load Balancer traffic is allowed.
  INGRESS_TRAFFIC_INTERNAL_LOAD_BALANCER = 3;

  // No ingress traffic is allowed.
  INGRESS_TRAFFIC_NONE = 4;
}

// Alternatives for execution environments.
enum ExecutionEnvironment {
  // Unspecified
  EXECUTION_ENVIRONMENT_UNSPECIFIED = 0;

  // Uses the First Generation environment.
  EXECUTION_ENVIRONMENT_GEN1 = 1;

  // Uses Second Generation environment.
  EXECUTION_ENVIRONMENT_GEN2 = 2;
}

// Specifies behavior if an encryption key used by a resource is revoked.
enum EncryptionKeyRevocationAction {
  // Unspecified
  ENCRYPTION_KEY_REVOCATION_ACTION_UNSPECIFIED = 0;

  // Prevents the creation of new instances.
  PREVENT_NEW = 1;

  // Shuts down existing instances, and prevents creation of new ones.
  SHUTDOWN = 2;
}
