// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.run.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/run/v2/k8s.min.proto";
import "google/cloud/run/v2/vendor_settings.proto";
import "google/protobuf/duration.proto";

option go_package = "cloud.google.com/go/run/apiv2/runpb;runpb";
option java_multiple_files = true;
option java_outer_classname = "TaskTemplateProto";
option java_package = "com.google.cloud.run.v2";

// TaskTemplate describes the data a task should have when created
// from a template.
message TaskTemplate {
  // Holds the single container that defines the unit of execution for this
  // task.
  repeated Container containers = 1;

  // Optional. A list of Volumes to make available to containers.
  repeated Volume volumes = 2 [(google.api.field_behavior) = OPTIONAL];

  oneof retries {
    // Number of retries allowed per Task, before marking this Task failed.
    // Defaults to 3.
    int32 max_retries = 3;
  }

  // Optional. Max allowed time duration the Task may be active before the
  // system will actively try to mark it failed and kill associated containers.
  // This applies per attempt of a task, meaning each retry can run for the full
  // timeout. Defaults to 600 seconds.
  google.protobuf.Duration timeout = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Email address of the IAM service account associated with the Task
  // of a Job. The service account represents the identity of the running task,
  // and determines what permissions the task has. If not provided, the task
  // will use the project's default service account.
  string service_account = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The execution environment being used to host this Task.
  ExecutionEnvironment execution_environment = 6
      [(google.api.field_behavior) = OPTIONAL];

  // A reference to a customer managed encryption key (CMEK) to use to encrypt
  // this container image. For more information, go to
  // https://cloud.google.com/run/docs/securing/using-cmek
  string encryption_key = 7 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];

  // Optional. VPC Access configuration to use for this Task. For more
  // information, visit
  // https://cloud.google.com/run/docs/configuring/connecting-vpc.
  VpcAccess vpc_access = 8 [(google.api.field_behavior) = OPTIONAL];
}
