# This build file includes a target for the Ruby wrapper library for
# google-cloud-run.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for run.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "run_ruby_wrapper",
    srcs = ["//google/cloud/run/v2:run_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-run",
        "ruby-cloud-wrapper-of=v2:0.17",
        "ruby-cloud-product-url=https://cloud.google.com/run",
        "ruby-cloud-api-id=run.googleapis.com",
        "ruby-cloud-api-shortname=run",
    ],
    ruby_cloud_description = "Cloud Run deploys and manages user provided container images that scale automatically based on incoming requests.",
    ruby_cloud_title = "Cloud Run",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-run-ruby",
    deps = [
        ":run_ruby_wrapper",
    ],
)
