# This build file includes a target for the Ruby wrapper library for
# google-cloud-eventarc-publishing.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for eventarc-publishing.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "publishing_ruby_wrapper",
    srcs = ["//google/cloud/eventarc/publishing/v1:publishing_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-eventarc-publishing",
        "ruby-cloud-env-prefix=EVENTARC",
        "ruby-cloud-wrapper-of=v1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/eventarc/",
        "ruby-cloud-api-id=eventarcpublishing.googleapis.com",
        "ruby-cloud-api-shortname=eventarcpublishing",
    ],
    ruby_cloud_description = "Eventarc lets you asynchronously deliver events from Google services, SaaS, and your own apps using loosely coupled services that react to state changes. Eventarc requires no infrastructure management — you can optimize productivity and costs while building a modern, event-driven solution.",
    ruby_cloud_title = "Eventarc Publishing",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-eventarc-publishing-ruby",
    deps = [
        ":publishing_ruby_wrapper",
    ],
)
