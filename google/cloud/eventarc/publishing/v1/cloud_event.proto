// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.publishing.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Eventarc.Publishing.V1";
option go_package = "cloud.google.com/go/eventarc/publishing/apiv1/publishingpb;publishingpb";
option java_multiple_files = true;
option java_outer_classname = "CloudEventProto";
option java_package = "com.google.cloud.eventarc.publishing.v1";
option php_namespace = "Google\\Cloud\\Eventarc\\Publishing\\V1";
option ruby_package = "Google::Cloud::Eventarc::Publishing::V1";

// CloudEvent represents a vendor-neutral specification for defining the format
// of event data.
message CloudEvent {
  // The following abstract data types are available for use in attributes.
  message CloudEventAttributeValue {
    // The value of the attribute.
    oneof attr {
      // Boolean value.
      bool ce_boolean = 1;

      // Integer value.
      int32 ce_integer = 2;

      // String value.
      string ce_string = 3;

      // Bytes value.
      bytes ce_bytes = 4;

      // URI value.
      string ce_uri = 5;

      // URI-reference value.
      string ce_uri_ref = 6;

      // Timestamp value.
      google.protobuf.Timestamp ce_timestamp = 7;
    }
  }

  // Required. Identifies the event. Producers MUST ensure that source + id is
  // unique for each distinct event.
  string id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Identifies the context in which an event happened.
  // URI-reference
  string source = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The version of the CloudEvents specification which the event
  // uses.
  string spec_version = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. This attribute contains a value describing the type of event
  // related to the originating occurrence.
  string type = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. Used for Optional & Extension Attributes
  map<string, CloudEventAttributeValue> attributes = 5
      [(google.api.field_behavior) = OPTIONAL];

  // The event payload. It should be encoded into a media format which is
  // specified by the 'datacontenttype' attribute (e.g. application/json), and
  // adheres to the dataschema format when those respective attributes are
  // present.
  oneof data {
    // Optional. Binary data.
    bytes binary_data = 6 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Text data.
    string text_data = 7 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Proto data.
    google.protobuf.Any proto_data = 8 [(google.api.field_behavior) = OPTIONAL];
  }
}
