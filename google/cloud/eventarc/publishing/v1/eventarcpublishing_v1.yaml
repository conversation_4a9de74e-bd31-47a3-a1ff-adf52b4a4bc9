type: google.api.Service
config_version: 3
name: eventarcpublishing.googleapis.com
title: Eventarc Publishing API

apis:
- name: google.cloud.eventarc.publishing.v1.Publisher

documentation:
  summary: |-
    Processes events generated by an event provider and delivers them to a
    subscriber.
  overview: |-
    The Eventarc Publishing API allows services to define and raise events
    which are routed based on the filtering configured through an Eventarc
    trigger. The code that raises these events can run on Google Cloud compute
    platforms or in other cloud environments. Eventarc acts as an intermediary
    between producers and consumers of these events, asynchronously delivering
    events, and supporting the orchestration of these decoupled
    services.\n\n\n\n\n\n\n\n

authentication:
  rules:
  - selector: 'google.cloud.eventarc.publishing.v1.Publisher.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
