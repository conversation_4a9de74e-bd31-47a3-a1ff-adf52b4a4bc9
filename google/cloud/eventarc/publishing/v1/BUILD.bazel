# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "publishing_proto",
    srcs = [
        "cloud_event.proto",
        "publisher.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "publishing_proto_with_info",
    deps = [
        ":publishing_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "publishing_java_proto",
    deps = [":publishing_proto"],
)

java_grpc_library(
    name = "publishing_java_grpc",
    srcs = [":publishing_proto"],
    deps = [":publishing_java_proto"],
)

java_gapic_library(
    name = "publishing_java_gapic",
    srcs = [":publishing_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "eventarcpublishing_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "eventarcpublishing_v1.yaml",
    test_deps = [
        ":publishing_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":publishing_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "publishing_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.eventarc.publishing.v1.PublisherClientHttpJsonTest",
        "com.google.cloud.eventarc.publishing.v1.PublisherClientTest",
    ],
    runtime_deps = [":publishing_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-eventarc-publishing-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":publishing_java_gapic",
        ":publishing_java_grpc",
        ":publishing_java_proto",
        ":publishing_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "publishing_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/eventarc/publishing/apiv1/publishingpb",
    protos = [":publishing_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "publishing_go_gapic",
    srcs = [":publishing_proto_with_info"],
    grpc_service_config = "eventarcpublishing_grpc_service_config.json",
    importpath = "cloud.google.com/go/eventarc/publishing/apiv1;publishing",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "eventarcpublishing_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":publishing_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-eventarc-publishing-v1-go",
    deps = [
        ":publishing_go_gapic",
        ":publishing_go_gapic_srcjar-metadata.srcjar",
        ":publishing_go_gapic_srcjar-snippets.srcjar",
        ":publishing_go_gapic_srcjar-test.srcjar",
        ":publishing_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "publishing_py_gapic",
    srcs = [":publishing_proto"],
    grpc_service_config = "eventarcpublishing_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=eventarc_publishing",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-eventarc-publishing",
    ],
    rest_numeric_enums = True,
    service_yaml = "eventarcpublishing_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "publishing_py_gapic_test",
    srcs = [
        "publishing_py_gapic_pytest.py",
        "publishing_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":publishing_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "eventarc-publishing-v1-py",
    deps = [
        ":publishing_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "publishing_php_proto",
    deps = [":publishing_proto"],
)

php_gapic_library(
    name = "publishing_php_gapic",
    srcs = [":publishing_proto_with_info"],
    grpc_service_config = "eventarcpublishing_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "eventarcpublishing_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":publishing_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-eventarc-publishing-v1-php",
    deps = [
        ":publishing_php_gapic",
        ":publishing_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "publishing_nodejs_gapic",
    package_name = "@google-cloud/eventarc-publishing",
    src = ":publishing_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "eventarcpublishing_grpc_service_config.json",
    package = "google.cloud.eventarc.publishing.v1",
    rest_numeric_enums = True,
    service_yaml = "eventarcpublishing_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "eventarc-publishing-v1-nodejs",
    deps = [
        ":publishing_nodejs_gapic",
        ":publishing_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "publishing_ruby_proto",
    deps = [":publishing_proto"],
)

ruby_grpc_library(
    name = "publishing_ruby_grpc",
    srcs = [":publishing_proto"],
    deps = [":publishing_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "publishing_ruby_gapic",
    srcs = [":publishing_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=eventarcpublishing.googleapis.com",
        "ruby-cloud-api-shortname=eventarcpublishing",
        "ruby-cloud-env-prefix=EVENTARC",
        "ruby-cloud-gem-name=google-cloud-eventarc-publishing-v1",
        "ruby-cloud-product-url=https://cloud.google.com/eventarc/",
    ],
    grpc_service_config = "eventarcpublishing_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Eventarc lets you asynchronously deliver events from Google services, SaaS, and your own apps using loosely coupled services that react to state changes. Eventarc requires no infrastructure management — you can optimize productivity and costs while building a modern, event-driven solution.",
    ruby_cloud_title = "Eventarc Publishing V1",
    service_yaml = "eventarcpublishing_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":publishing_ruby_grpc",
        ":publishing_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-eventarc-publishing-v1-ruby",
    deps = [
        ":publishing_ruby_gapic",
        ":publishing_ruby_grpc",
        ":publishing_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "publishing_csharp_proto",
    extra_opts = [],
    deps = [":publishing_proto"],
)

csharp_grpc_library(
    name = "publishing_csharp_grpc",
    srcs = [":publishing_proto"],
    deps = [":publishing_csharp_proto"],
)

csharp_gapic_library(
    name = "publishing_csharp_gapic",
    srcs = [":publishing_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "eventarcpublishing_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "eventarcpublishing_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":publishing_csharp_grpc",
        ":publishing_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-eventarc-publishing-v1-csharp",
    deps = [
        ":publishing_csharp_gapic",
        ":publishing_csharp_grpc",
        ":publishing_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "publishing_cc_proto",
    deps = [":publishing_proto"],
)

cc_grpc_library(
    name = "publishing_cc_grpc",
    srcs = [":publishing_proto"],
    grpc_only = True,
    deps = [":publishing_cc_proto"],
)
