{"methodConfig": [{"name": [{"service": "google.cloud.eventarc.v1.Eventarc", "method": "GetTrigger"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "ListTriggers"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "GetChannel"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "ListChannels"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "GetProvider"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "ListProviders"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "GetChannelConnection"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "ListChannelConnections"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "GetGoogleChannelConfig"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "GetMessageBus"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "ListMessageBuses"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "ListMessageBusEnrollments"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "GetEnrollment"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "ListEnrollments"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "ListPipelines"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "GetGoogleApiSource"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "ListGoogleApiSources"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "UNKNOWN"]}}, {"name": [{"service": "google.cloud.eventarc.v1.Eventarc", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "UpdateTrigger"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "DeleteTrigger"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "CreateChannel"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "UpdateChannel"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "DeleteChannel"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "CreateChannelConnection"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "DeleteChannelConnection"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "UpdateGoogleChannelConfig"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "CreateMessageBus"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "UpdateMessageBus"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "DeleteMessageBus"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "CreateEnrollment"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "UpdateEnrollment"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "DeleteEnrollment"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "Create<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "Update<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "DeletePipeline"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "CreateGoogleApiSource"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "UpdateGoogleApiSource"}, {"service": "google.cloud.eventarc.v1.Eventarc", "method": "DeleteGoogleApiSource"}], "timeout": "60s"}]}