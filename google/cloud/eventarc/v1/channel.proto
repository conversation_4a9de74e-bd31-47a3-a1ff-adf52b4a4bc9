// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Eventarc.V1";
option go_package = "cloud.google.com/go/eventarc/apiv1/eventarcpb;eventarcpb";
option java_multiple_files = true;
option java_outer_classname = "ChannelProto";
option java_package = "com.google.cloud.eventarc.v1";
option php_namespace = "Google\\Cloud\\Eventarc\\V1";
option ruby_package = "Google::Cloud::Eventarc::V1";

// A representation of the Channel resource.
// A Channel is a resource on which event providers publish their events.
// The published events are delivered through the transport associated with the
// channel. Note that a channel is associated with exactly one event provider.
message Channel {
  option (google.api.resource) = {
    type: "eventarc.googleapis.com/Channel"
    pattern: "projects/{project}/locations/{location}/channels/{channel}"
    plural: "channels"
    singular: "channel"
  };

  // State lists all the possible states of a Channel
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // The PENDING state indicates that a Channel has been created successfully
    // and there is a new activation token available for the subscriber to use
    // to convey the Channel to the provider in order to create a Connection.
    PENDING = 1;

    // The ACTIVE state indicates that a Channel has been successfully
    // connected with the event provider.
    // An ACTIVE Channel is ready to receive and route events from the
    // event provider.
    ACTIVE = 2;

    // The INACTIVE state indicates that the Channel cannot receive events
    // permanently. There are two possible cases this state can happen:
    //
    // 1. The SaaS provider disconnected from this Channel.
    // 2. The Channel activation token has expired but the SaaS provider
    //    wasn't connected.
    //
    // To re-establish a Connection with a provider, the subscriber
    // should create a new Channel and give it to the provider.
    INACTIVE = 3;
  }

  // Required. The resource name of the channel. Must be unique within the
  // location on the project and must be in
  // `projects/{project}/locations/{location}/channels/{channel_id}` format.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Server assigned unique identifier for the channel. The value
  // is a UUID4 string and guaranteed to remain unchanged until the resource is
  // deleted.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last-modified time.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the event provider (e.g. Eventarc SaaS partner) associated
  // with the channel. This provider will be granted permissions to publish
  // events to the channel. Format:
  // `projects/{project}/locations/{location}/providers/{provider_id}`.
  string provider = 7;

  oneof transport {
    // Output only. The name of the Pub/Sub topic created and managed by
    // Eventarc system as a transport for the event delivery. Format:
    // `projects/{project}/topics/{topic_id}`.
    string pubsub_topic = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The state of a Channel.
  State state = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The activation token for the channel. The token must be used
  // by the provider to register the channel for publishing.
  string activation_token = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource name of a KMS crypto key (managed by the user) used to
  // encrypt/decrypt their event data.
  //
  // It must match the pattern
  // `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
  string crypto_key_name = 11 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];

  // Output only. Whether or not this Channel satisfies the requirements of
  // physical zone separation
  bool satisfies_pzs = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}
