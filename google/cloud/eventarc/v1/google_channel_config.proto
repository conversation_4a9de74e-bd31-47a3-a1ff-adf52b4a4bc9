// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Eventarc.V1";
option go_package = "cloud.google.com/go/eventarc/apiv1/eventarcpb;eventarcpb";
option java_multiple_files = true;
option java_outer_classname = "GoogleChannelConfigProto";
option java_package = "com.google.cloud.eventarc.v1";
option php_namespace = "Google\\Cloud\\Eventarc\\V1";
option ruby_package = "Google::Cloud::Eventarc::V1";
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};

// A GoogleChannelConfig is a resource that stores the custom settings
// respected by Eventarc first-party triggers in the matching region.
// Once configured, first-party event data will be protected
// using the specified custom managed encryption key instead of Google-managed
// encryption keys.
message GoogleChannelConfig {
  option (google.api.resource) = {
    type: "eventarc.googleapis.com/GoogleChannelConfig"
    pattern: "projects/{project}/locations/{location}/googleChannelConfig"
    singular: "googleChannelConfig"
  };

  // Required. The resource name of the config. Must be in the format of,
  // `projects/{project}/locations/{location}/googleChannelConfig`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The last-modified time.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource name of a KMS crypto key (managed by the user) used to
  // encrypt/decrypt their event data.
  //
  // It must match the pattern
  // `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
  string crypto_key_name = 7 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];
}
