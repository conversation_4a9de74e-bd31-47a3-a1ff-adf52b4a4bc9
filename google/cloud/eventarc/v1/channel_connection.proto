// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Eventarc.V1";
option go_package = "cloud.google.com/go/eventarc/apiv1/eventarcpb;eventarcpb";
option java_multiple_files = true;
option java_outer_classname = "ChannelConnectionProto";
option java_package = "com.google.cloud.eventarc.v1";
option php_namespace = "Google\\Cloud\\Eventarc\\V1";
option ruby_package = "Google::Cloud::Eventarc::V1";

// A representation of the ChannelConnection resource.
// A ChannelConnection is a resource which event providers create during the
// activation process to establish a connection between the provider and the
// subscriber channel.
message ChannelConnection {
  option (google.api.resource) = {
    type: "eventarc.googleapis.com/ChannelConnection"
    pattern: "projects/{project}/locations/{location}/channelConnections/{channel_connection}"
    plural: "channelConnections"
    singular: "channelConnection"
  };

  // Required. The name of the connection.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Server assigned ID of the resource.
  // The server guarantees uniqueness and immutability until deleted.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The name of the connected subscriber Channel.
  // This is a weak reference to avoid cross project and cross accounts
  // references. This must be in
  // `projects/{project}/location/{location}/channels/{channel_id}` format.
  string channel = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Channel"
    }
  ];

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last-modified time.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. Activation token for the channel. The token will be used
  // during the creation of ChannelConnection to bind the channel with the
  // provider project. This field will not be stored in the provider resource.
  string activation_token = 8 [(google.api.field_behavior) = INPUT_ONLY];
}
