# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "eventarc_proto",
    srcs = [
        "channel.proto",
        "channel_connection.proto",
        "discovery.proto",
        "enrollment.proto",
        "eventarc.proto",
        "google_api_source.proto",
        "google_channel_config.proto",
        "logging_config.proto",
        "message_bus.proto",
        "network_config.proto",
        "pipeline.proto",
        "trigger.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:code_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "eventarc_proto_with_info",
    deps = [
        ":eventarc_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "eventarc_java_proto",
    deps = [":eventarc_proto"],
)

java_grpc_library(
    name = "eventarc_java_grpc",
    srcs = [":eventarc_proto"],
    deps = [":eventarc_java_proto"],
)

java_gapic_library(
    name = "eventarc_java_gapic",
    srcs = [":eventarc_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "eventarc_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "eventarc_v1.yaml",
    test_deps = [
        ":eventarc_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":eventarc_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "eventarc_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.eventarc.v1.EventarcClientHttpJsonTest",
        "com.google.cloud.eventarc.v1.EventarcClientTest",
    ],
    runtime_deps = [":eventarc_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-eventarc-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":eventarc_java_gapic",
        ":eventarc_java_grpc",
        ":eventarc_java_proto",
        ":eventarc_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "eventarc_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/eventarc/apiv1/eventarcpb",
    protos = [":eventarc_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:code_go_proto",
    ],
)

go_gapic_library(
    name = "eventarc_go_gapic",
    srcs = [":eventarc_proto_with_info"],
    grpc_service_config = "eventarc_grpc_service_config.json",
    importpath = "cloud.google.com/go/eventarc/apiv1;eventarc",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "eventarc_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":eventarc_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-eventarc-v1-go",
    deps = [
        ":eventarc_go_gapic",
        ":eventarc_go_gapic_srcjar-metadata.srcjar",
        ":eventarc_go_gapic_srcjar-snippets.srcjar",
        ":eventarc_go_gapic_srcjar-test.srcjar",
        ":eventarc_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "eventarc_py_gapic",
    srcs = [":eventarc_proto"],
    grpc_service_config = "eventarc_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "eventarc_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "eventarc_py_gapic_test",
    srcs = [
        "eventarc_py_gapic_pytest.py",
        "eventarc_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":eventarc_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "eventarc-v1-py",
    deps = [
        ":eventarc_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "eventarc_php_proto",
    deps = [":eventarc_proto"],
)

php_gapic_library(
    name = "eventarc_php_gapic",
    srcs = [":eventarc_proto_with_info"],
    grpc_service_config = "eventarc_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "eventarc_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":eventarc_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-eventarc-v1-php",
    deps = [
        ":eventarc_php_gapic",
        ":eventarc_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "eventarc_nodejs_gapic",
    package_name = "@google-cloud/eventarc",
    src = ":eventarc_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "eventarc_grpc_service_config.json",
    package = "google.cloud.eventarc.v1",
    rest_numeric_enums = True,
    service_yaml = "eventarc_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "eventarc-v1-nodejs",
    deps = [
        ":eventarc_nodejs_gapic",
        ":eventarc_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "eventarc_ruby_proto",
    deps = [":eventarc_proto"],
)

ruby_grpc_library(
    name = "eventarc_ruby_grpc",
    srcs = [":eventarc_proto"],
    deps = [":eventarc_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "eventarc_ruby_gapic",
    srcs = [":eventarc_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=eventarc.googleapis.com",
        "ruby-cloud-api-shortname=eventarc",
        "ruby-cloud-env-prefix=EVENTARC",
        "ruby-cloud-gem-name=google-cloud-eventarc-v1",
        "ruby-cloud-product-url=https://cloud.google.com/eventarc/",
    ],
    grpc_service_config = "eventarc_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Eventarc lets you asynchronously deliver events from Google services, SaaS, and your own apps using loosely coupled services that react to state changes. Eventarc requires no infrastructure management — you can optimize productivity and costs while building a modern, event-driven solution.",
    ruby_cloud_title = "Eventarc V1",
    service_yaml = "eventarc_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":eventarc_ruby_grpc",
        ":eventarc_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-eventarc-v1-ruby",
    deps = [
        ":eventarc_ruby_gapic",
        ":eventarc_ruby_grpc",
        ":eventarc_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "eventarc_csharp_proto",
    extra_opts = [],
    deps = [":eventarc_proto"],
)

csharp_grpc_library(
    name = "eventarc_csharp_grpc",
    srcs = [":eventarc_proto"],
    deps = [":eventarc_csharp_proto"],
)

csharp_gapic_library(
    name = "eventarc_csharp_gapic",
    srcs = [":eventarc_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "eventarc_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "eventarc_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":eventarc_csharp_grpc",
        ":eventarc_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-eventarc-v1-csharp",
    deps = [
        ":eventarc_csharp_gapic",
        ":eventarc_csharp_grpc",
        ":eventarc_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "eventarc_cc_proto",
    deps = [":eventarc_proto"],
)

cc_grpc_library(
    name = "eventarc_cc_grpc",
    srcs = [":eventarc_proto"],
    grpc_only = True,
    deps = [":eventarc_cc_proto"],
)
