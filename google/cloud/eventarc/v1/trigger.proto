// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/eventarc/v1/network_config.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/code.proto";

option go_package = "cloud.google.com/go/eventarc/apiv1/eventarcpb;eventarcpb";
option java_multiple_files = true;
option java_outer_classname = "TriggerProto";
option java_package = "com.google.cloud.eventarc.v1";
option (google.api.resource_definition) = {
  type: "cloudfunctions.googleapis.com/CloudFunction"
  pattern: "projects/{project}/locations/{location}/functions/{function}"
};
option (google.api.resource_definition) = {
  type: "iam.googleapis.com/ServiceAccount"
  pattern: "projects/{project}/serviceAccounts/{service_account}"
};
option (google.api.resource_definition) = {
  type: "run.googleapis.com/Service"
  pattern: "*"
};
option (google.api.resource_definition) = {
  type: "workflows.googleapis.com/Workflow"
  pattern: "projects/{project}/locations/{location}/workflows/{workflow}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/NetworkAttachment"
  pattern: "projects/{project}/regions/{region}/networkAttachments/{networkattachment}"
};

// A representation of the trigger resource.
message Trigger {
  option (google.api.resource) = {
    type: "eventarc.googleapis.com/Trigger"
    pattern: "projects/{project}/locations/{location}/triggers/{trigger}"
    plural: "triggers"
    singular: "trigger"
  };

  // Required. The resource name of the trigger. Must be unique within the
  // location of the project and must be in
  // `projects/{project}/locations/{location}/triggers/{trigger}` format.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Server-assigned unique identifier for the trigger. The value
  // is a UUID4 string and guaranteed to remain unchanged until the resource is
  // deleted.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last-modified time.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Unordered list. The list of filters that applies to event
  // attributes. Only events that match all the provided filters are sent to the
  // destination.
  repeated EventFilter event_filters = 8 [
    (google.api.field_behavior) = UNORDERED_LIST,
    (google.api.field_behavior) = REQUIRED
  ];

  // Optional. The IAM service account email associated with the trigger. The
  // service account represents the identity of the trigger.
  //
  // The `iam.serviceAccounts.actAs` permission must be granted on the service
  // account to allow a principal to impersonate the service account. For more
  // information, see the
  // [Roles and permissions](/eventarc/docs/all-roles-permissions) page specific
  // to the trigger destination.
  string service_account = 9 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // Required. Destination specifies where the events should be sent to.
  Destination destination = 10 [(google.api.field_behavior) = REQUIRED];

  // Optional. To deliver messages, Eventarc might use other Google Cloud
  // products as a transport intermediary. This field contains a reference to
  // that transport intermediary. This information can be used for debugging
  // purposes.
  Transport transport = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User labels attached to the triggers that can be used to group
  // resources.
  map<string, string> labels = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the channel associated with the trigger in
  // `projects/{project}/locations/{location}/channels/{channel}` format.
  // You must provide a channel to receive events from Eventarc SaaS partners.
  string channel = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The reason(s) why a trigger is in FAILED state.
  map<string, StateCondition> conditions = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. EventDataContentType specifies the type of payload in MIME
  // format that is expected from the CloudEvent data field. This is set to
  // `application/json` if the value is not defined.
  string event_data_content_type = 16 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Whether or not this Trigger satisfies the requirements of
  // physical zone separation
  bool satisfies_pzs = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This checksum is computed by the server based on the value of
  // other fields, and might be sent only on create requests to ensure that the
  // client has an up-to-date value before proceeding.
  string etag = 99 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Filters events based on exact matches on the CloudEvents attributes.
message EventFilter {
  // Required. The name of a CloudEvents attribute. Currently, only a subset of
  // attributes are supported for filtering. You can [retrieve a specific
  // provider's supported event
  // types](/eventarc/docs/list-providers#describe-provider).
  //
  // All triggers MUST provide a filter for the 'type' attribute.
  string attribute = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The value for the attribute.
  string value = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The operator used for matching the events with the value of the
  // filter. If not specified, only events that have an exact key-value pair
  // specified in the filter are matched. The allowed values are `path_pattern`
  // and `match-path-pattern`. `path_pattern` is only allowed for GCFv1
  // triggers.
  string operator = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A condition that is part of the trigger state computation.
message StateCondition {
  // The canonical code of the condition.
  google.rpc.Code code = 1;

  // Human-readable message.
  string message = 2;
}

// Represents a target of an invocation over HTTP.
message Destination {
  oneof descriptor {
    // Cloud Run fully-managed resource that receives the events. The resource
    // should be in the same project as the trigger.
    CloudRun cloud_run = 1;

    // The Cloud Function resource name. Cloud Functions V1 and V2 are
    // supported.
    // Format: `projects/{project}/locations/{location}/functions/{function}`
    //
    // This is a read-only field. Creating Cloud Functions V1/V2 triggers is
    // only supported via the Cloud Functions product. An error will be returned
    // if the user sets this value.
    string cloud_function = 2 [(google.api.resource_reference) = {
      type: "cloudfunctions.googleapis.com/CloudFunction"
    }];

    // A GKE service capable of receiving events. The service should be running
    // in the same project as the trigger.
    GKE gke = 3;

    // The resource name of the Workflow whose Executions are triggered by
    // the events. The Workflow resource should be deployed in the same project
    // as the trigger.
    // Format: `projects/{project}/locations/{location}/workflows/{workflow}`
    string workflow = 4 [(google.api.resource_reference) = {
      type: "workflows.googleapis.com/Workflow"
    }];

    // An HTTP endpoint destination described by an URI.
    HttpEndpoint http_endpoint = 5;
  }

  // Optional. Network config is used to configure how Eventarc resolves and
  // connect to a destination.
  // This should only be used with HttpEndpoint destination type.
  NetworkConfig network_config = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the transport intermediaries created for the trigger to
// deliver events.
message Transport {
  oneof intermediary {
    // The Pub/Sub topic and subscription used by Eventarc as a transport
    // intermediary.
    Pubsub pubsub = 1;
  }
}

// Represents a Cloud Run destination.
message CloudRun {
  // Required. The name of the Cloud Run service being addressed. See
  // https://cloud.google.com/run/docs/reference/rest/v1/namespaces.services.
  //
  // Only services located in the same project as the trigger object
  // can be addressed.
  string service = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "run.googleapis.com/Service" }
  ];

  // Optional. The relative path on the Cloud Run service the events should be
  // sent to.
  //
  // The value must conform to the definition of a URI path segment (section 3.3
  // of RFC2396). Examples: "/route", "route", "route/subroute".
  string path = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The region the Cloud Run service is deployed in.
  string region = 3 [(google.api.field_behavior) = REQUIRED];
}

// Represents a GKE destination.
message GKE {
  // Required. The name of the cluster the GKE service is running in. The
  // cluster must be running in the same project as the trigger being created.
  string cluster = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the Google Compute Engine in which the cluster
  // resides, which can either be compute zone (for example, us-central1-a) for
  // the zonal clusters or region (for example, us-central1) for regional
  // clusters.
  string location = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The namespace the GKE service is running in.
  string namespace = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the GKE service.
  string service = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. The relative path on the GKE service the events should be sent
  // to.
  //
  // The value must conform to the definition of a URI path segment (section 3.3
  // of RFC2396). Examples: "/route", "route", "route/subroute".
  string path = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Represents a Pub/Sub transport.
message Pubsub {
  // Optional. The name of the Pub/Sub topic created and managed by Eventarc as
  // a transport for the event delivery. Format:
  // `projects/{PROJECT_ID}/topics/{TOPIC_NAME}`.
  //
  // You can set an existing topic for triggers of the type
  // `google.cloud.pubsub.topic.v1.messagePublished`. The topic you provide
  // here is not deleted by Eventarc at trigger deletion.
  string topic = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The name of the Pub/Sub subscription created and managed by
  // Eventarc as a transport for the event delivery. Format:
  // `projects/{PROJECT_ID}/subscriptions/{SUBSCRIPTION_NAME}`.
  string subscription = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents a HTTP endpoint destination.
message HttpEndpoint {
  // Required. The URI of the HTTP enpdoint.
  //
  // The value must be a RFC2396 URI string.
  // Examples: `http://**********:80/route`,
  // `http://svc.us-central1.p.local:8080/`.
  // Only HTTP and HTTPS protocols are supported. The host can be either a
  // static IP addressable from the VPC specified by the network config, or
  // an internal DNS hostname of the service resolvable via Cloud DNS.
  string uri = 1 [(google.api.field_behavior) = REQUIRED];
}
