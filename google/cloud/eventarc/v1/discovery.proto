// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/eventarc/apiv1/eventarcpb;eventarcpb";
option java_multiple_files = true;
option java_outer_classname = "DiscoveryProto";
option java_package = "com.google.cloud.eventarc.v1";

// A representation of the Provider resource.
message Provider {
  option (google.api.resource) = {
    type: "eventarc.googleapis.com/Provider"
    pattern: "projects/{project}/locations/{location}/providers/{provider}"
    plural: "providers"
    singular: "provider"
  };

  // Output only. In
  // `projects/{project}/locations/{location}/providers/{provider_id}` format.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human friendly name for the Provider. For example "Cloud
  // Storage".
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Event types for this provider.
  repeated EventType event_types = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A representation of the event type resource.
message EventType {
  // Output only. The full name of the event type (for example,
  // "google.cloud.storage.object.v1.finalized"). In the form of
  // {provider-specific-prefix}.{resource}.{version}.{verb}. Types MUST be
  // versioned and event schemas are guaranteed to remain backward compatible
  // within one version. Note that event type versions and API versions do not
  // need to match.
  string type = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human friendly description of what the event type is about.
  // For example "Bucket created in Cloud Storage".
  string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Filtering attributes for the event type.
  repeated FilteringAttribute filtering_attributes = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URI for the event schema.
  // For example
  // "https://github.com/googleapis/google-cloudevents/blob/master/proto/google/events/cloud/storage/v1/events.proto"
  string event_schema_uri = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A representation of the FilteringAttribute resource.
// Filtering attributes are per event type.
message FilteringAttribute {
  // Output only. Attribute used for filtering the event type.
  string attribute = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Description of the purpose of the attribute.
  string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If true, the triggers for this provider should always specify
  // a filter on these attributes. Trigger creation will fail otherwise.
  bool required = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If true, the attribute accepts matching expressions in the
  // Eventarc PathPattern format.
  bool path_pattern_supported = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}
