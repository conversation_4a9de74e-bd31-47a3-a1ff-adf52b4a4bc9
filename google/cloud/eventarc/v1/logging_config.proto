// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Eventarc.V1";
option go_package = "cloud.google.com/go/eventarc/apiv1/eventarcpb;eventarcpb";
option java_multiple_files = true;
option java_outer_classname = "LoggingConfigProto";
option java_package = "com.google.cloud.eventarc.v1";
option php_namespace = "Google\\Cloud\\Eventarc\\V1";
option ruby_package = "Google::Cloud::Eventarc::V1";

// The configuration for Platform Telemetry logging for Eventarc Advanced
// resources.
message LoggingConfig {
  // The different severities for logging supported by Eventarc Advanced
  // resources.
  // This enum is an exhaustive list of log severities and is FROZEN. Do not
  // expect new values to be added.
  enum LogSeverity {
    // Log severity is not specified. This value is treated the same as NONE,
    // but is used to distinguish between no update and update to NONE in
    // update_masks.
    LOG_SEVERITY_UNSPECIFIED = 0;

    // Default value at resource creation, presence of this value must be
    // treated as no logging/disable logging.
    NONE = 1;

    // Debug or trace level logging.
    DEBUG = 2;

    // Routine information, such as ongoing status or performance.
    INFO = 3;

    // Normal but significant events, such as start up, shut down, or a
    // configuration change.
    NOTICE = 4;

    // Warning events might cause problems.
    WARNING = 5;

    // Error events are likely to cause problems.
    ERROR = 6;

    // Critical events cause more severe problems or outages.
    CRITICAL = 7;

    // A person must take action immediately.
    ALERT = 8;

    // One or more systems are unusable.
    EMERGENCY = 9;
  }

  // Optional. The minimum severity of logs that will be sent to
  // Stackdriver/Platform Telemetry. Logs at severitiy ≥ this value will be
  // sent, unless it is NONE.
  LogSeverity log_severity = 1 [(google.api.field_behavior) = OPTIONAL];
}
