// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/eventarc/v1/channel.proto";
import "google/cloud/eventarc/v1/channel_connection.proto";
import "google/cloud/eventarc/v1/discovery.proto";
import "google/cloud/eventarc/v1/enrollment.proto";
import "google/cloud/eventarc/v1/google_api_source.proto";
import "google/cloud/eventarc/v1/google_channel_config.proto";
import "google/cloud/eventarc/v1/message_bus.proto";
import "google/cloud/eventarc/v1/pipeline.proto";
import "google/cloud/eventarc/v1/trigger.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Eventarc.V1";
option go_package = "cloud.google.com/go/eventarc/apiv1/eventarcpb;eventarcpb";
option java_multiple_files = true;
option java_outer_classname = "EventarcProto";
option java_package = "com.google.cloud.eventarc.v1";
option php_namespace = "Google\\Cloud\\Eventarc\\V1";
option ruby_package = "Google::Cloud::Eventarc::V1";

// Eventarc allows users to subscribe to various events that are provided by
// Google Cloud services and forward them to supported destinations.
service Eventarc {
  option (google.api.default_host) = "eventarc.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Get a single trigger.
  rpc GetTrigger(GetTriggerRequest) returns (Trigger) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/triggers/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List triggers.
  rpc ListTriggers(ListTriggersRequest) returns (ListTriggersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/triggers"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create a new trigger in a particular project and location.
  rpc CreateTrigger(CreateTriggerRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/triggers"
      body: "trigger"
    };
    option (google.api.method_signature) = "parent,trigger,trigger_id";
    option (google.longrunning.operation_info) = {
      response_type: "Trigger"
      metadata_type: "OperationMetadata"
    };
  }

  // Update a single trigger.
  rpc UpdateTrigger(UpdateTriggerRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{trigger.name=projects/*/locations/*/triggers/*}"
      body: "trigger"
    };
    option (google.api.method_signature) = "trigger,update_mask,allow_missing";
    option (google.longrunning.operation_info) = {
      response_type: "Trigger"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete a single trigger.
  rpc DeleteTrigger(DeleteTriggerRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/triggers/*}"
    };
    option (google.api.method_signature) = "name,allow_missing";
    option (google.longrunning.operation_info) = {
      response_type: "Trigger"
      metadata_type: "OperationMetadata"
    };
  }

  // Get a single Channel.
  rpc GetChannel(GetChannelRequest) returns (Channel) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/channels/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List channels.
  rpc ListChannels(ListChannelsRequest) returns (ListChannelsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/channels"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create a new channel in a particular project and location.
  rpc CreateChannel(CreateChannelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/channels"
      body: "channel"
    };
    option (google.api.method_signature) = "parent,channel,channel_id";
    option (google.longrunning.operation_info) = {
      response_type: "Channel"
      metadata_type: "OperationMetadata"
    };
  }

  // Update a single channel.
  rpc UpdateChannel(UpdateChannelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{channel.name=projects/*/locations/*/channels/*}"
      body: "channel"
    };
    option (google.api.method_signature) = "channel,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Channel"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete a single channel.
  rpc DeleteChannel(DeleteChannelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/channels/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Channel"
      metadata_type: "OperationMetadata"
    };
  }

  // Get a single Provider.
  rpc GetProvider(GetProviderRequest) returns (Provider) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/providers/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List providers.
  rpc ListProviders(ListProvidersRequest) returns (ListProvidersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/providers"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get a single ChannelConnection.
  rpc GetChannelConnection(GetChannelConnectionRequest)
      returns (ChannelConnection) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/channelConnections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List channel connections.
  rpc ListChannelConnections(ListChannelConnectionsRequest)
      returns (ListChannelConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/channelConnections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create a new ChannelConnection in a particular project and location.
  rpc CreateChannelConnection(CreateChannelConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/channelConnections"
      body: "channel_connection"
    };
    option (google.api.method_signature) =
        "parent,channel_connection,channel_connection_id";
    option (google.longrunning.operation_info) = {
      response_type: "ChannelConnection"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete a single ChannelConnection.
  rpc DeleteChannelConnection(DeleteChannelConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/channelConnections/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "ChannelConnection"
      metadata_type: "OperationMetadata"
    };
  }

  // Get a GoogleChannelConfig
  rpc GetGoogleChannelConfig(GetGoogleChannelConfigRequest)
      returns (GoogleChannelConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/googleChannelConfig}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update a single GoogleChannelConfig
  rpc UpdateGoogleChannelConfig(UpdateGoogleChannelConfigRequest)
      returns (GoogleChannelConfig) {
    option (google.api.http) = {
      patch: "/v1/{google_channel_config.name=projects/*/locations/*/googleChannelConfig}"
      body: "google_channel_config"
    };
    option (google.api.method_signature) = "google_channel_config,update_mask";
  }

  // Get a single MessageBus.
  rpc GetMessageBus(GetMessageBusRequest) returns (MessageBus) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/messageBuses/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List message buses.
  rpc ListMessageBuses(ListMessageBusesRequest)
      returns (ListMessageBusesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/messageBuses"
    };
    option (google.api.method_signature) = "parent";
  }

  // List message bus enrollments.
  rpc ListMessageBusEnrollments(ListMessageBusEnrollmentsRequest)
      returns (ListMessageBusEnrollmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/messageBuses/*}:listEnrollments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create a new MessageBus in a particular project and location.
  rpc CreateMessageBus(CreateMessageBusRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/messageBuses"
      body: "message_bus"
    };
    option (google.api.method_signature) = "parent,message_bus,message_bus_id";
    option (google.longrunning.operation_info) = {
      response_type: "MessageBus"
      metadata_type: "OperationMetadata"
    };
  }

  // Update a single message bus.
  rpc UpdateMessageBus(UpdateMessageBusRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{message_bus.name=projects/*/locations/*/messageBuses/*}"
      body: "message_bus"
    };
    option (google.api.method_signature) = "message_bus,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "MessageBus"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete a single message bus.
  rpc DeleteMessageBus(DeleteMessageBusRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/messageBuses/*}"
    };
    option (google.api.method_signature) = "name,etag";
    option (google.longrunning.operation_info) = {
      response_type: "MessageBus"
      metadata_type: "OperationMetadata"
    };
  }

  // Get a single Enrollment.
  rpc GetEnrollment(GetEnrollmentRequest) returns (Enrollment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/enrollments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List Enrollments.
  rpc ListEnrollments(ListEnrollmentsRequest)
      returns (ListEnrollmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/enrollments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create a new Enrollment in a particular project and location.
  rpc CreateEnrollment(CreateEnrollmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/enrollments"
      body: "enrollment"
    };
    option (google.api.method_signature) = "parent,enrollment,enrollment_id";
    option (google.longrunning.operation_info) = {
      response_type: "Enrollment"
      metadata_type: "OperationMetadata"
    };
  }

  // Update a single Enrollment.
  rpc UpdateEnrollment(UpdateEnrollmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{enrollment.name=projects/*/locations/*/enrollments/*}"
      body: "enrollment"
    };
    option (google.api.method_signature) = "enrollment,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Enrollment"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete a single Enrollment.
  rpc DeleteEnrollment(DeleteEnrollmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/enrollments/*}"
    };
    option (google.api.method_signature) = "name,etag";
    option (google.longrunning.operation_info) = {
      response_type: "Enrollment"
      metadata_type: "OperationMetadata"
    };
  }

  // Get a single Pipeline.
  rpc GetPipeline(GetPipelineRequest) returns (Pipeline) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/pipelines/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List pipelines.
  rpc ListPipelines(ListPipelinesRequest) returns (ListPipelinesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/pipelines"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create a new Pipeline in a particular project and location.
  rpc CreatePipeline(CreatePipelineRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/pipelines"
      body: "pipeline"
    };
    option (google.api.method_signature) = "parent,pipeline,pipeline_id";
    option (google.longrunning.operation_info) = {
      response_type: "Pipeline"
      metadata_type: "OperationMetadata"
    };
  }

  // Update a single pipeline.
  rpc UpdatePipeline(UpdatePipelineRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{pipeline.name=projects/*/locations/*/pipelines/*}"
      body: "pipeline"
    };
    option (google.api.method_signature) = "pipeline,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Pipeline"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete a single pipeline.
  rpc DeletePipeline(DeletePipelineRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/pipelines/*}"
    };
    option (google.api.method_signature) = "name,etag";
    option (google.longrunning.operation_info) = {
      response_type: "Pipeline"
      metadata_type: "OperationMetadata"
    };
  }

  // Get a single GoogleApiSource.
  rpc GetGoogleApiSource(GetGoogleApiSourceRequest) returns (GoogleApiSource) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/googleApiSources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List GoogleApiSources.
  rpc ListGoogleApiSources(ListGoogleApiSourcesRequest)
      returns (ListGoogleApiSourcesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/googleApiSources"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create a new GoogleApiSource in a particular project and location.
  rpc CreateGoogleApiSource(CreateGoogleApiSourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/googleApiSources"
      body: "google_api_source"
    };
    option (google.api.method_signature) =
        "parent,google_api_source,google_api_source_id";
    option (google.longrunning.operation_info) = {
      response_type: "GoogleApiSource"
      metadata_type: "OperationMetadata"
    };
  }

  // Update a single GoogleApiSource.
  rpc UpdateGoogleApiSource(UpdateGoogleApiSourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{google_api_source.name=projects/*/locations/*/googleApiSources/*}"
      body: "google_api_source"
    };
    option (google.api.method_signature) = "google_api_source,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "GoogleApiSource"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete a single GoogleApiSource.
  rpc DeleteGoogleApiSource(DeleteGoogleApiSourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/googleApiSources/*}"
    };
    option (google.api.method_signature) = "name,etag";
    option (google.longrunning.operation_info) = {
      response_type: "GoogleApiSource"
      metadata_type: "OperationMetadata"
    };
  }
}

// The request message for the GetTrigger method.
message GetTriggerRequest {
  // Required. The name of the trigger to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Trigger"
    }
  ];
}

// The request message for the ListTriggers method.
message ListTriggersRequest {
  // Required. The parent collection to list triggers on.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/Trigger"
    }
  ];

  // The maximum number of triggers to return on each page.
  //
  // Note: The service may send fewer.
  int32 page_size = 2;

  // The page token; provide the value from the `next_page_token` field in a
  // previous `ListTriggers` call to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListTriggers` must match
  // the call that provided the page token.
  string page_token = 3;

  // The sorting order of the resources returned. Value should be a
  // comma-separated list of fields. The default sorting order is ascending. To
  // specify descending order for a field, append a `desc` suffix; for example:
  // `name desc, trigger_id`.
  string order_by = 4;

  // Filter field. Used to filter the Triggers to be listed. Possible filters
  // are described in https://google.aip.dev/160. For example, using
  // "?filter=destination:gke" would list only Triggers with a gke destination.
  string filter = 5;
}

// The response message for the `ListTriggers` method.
message ListTriggersResponse {
  // The requested triggers, up to the number specified in `page_size`.
  repeated Trigger triggers = 1;

  // A page token that can be sent to `ListTriggers` to request the next page.
  // If this is empty, then there are no more pages.
  string next_page_token = 2;

  // Unreachable resources, if any.
  repeated string unreachable = 3;
}

// The request message for the CreateTrigger method.
message CreateTriggerRequest {
  // Required. The parent collection in which to add this trigger.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/Trigger"
    }
  ];

  // Required. The trigger to create.
  Trigger trigger = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided ID to be assigned to the trigger.
  string trigger_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the UpdateTrigger method.
message UpdateTriggerRequest {
  // The trigger to be updated.
  Trigger trigger = 1;

  // The fields to be updated; only fields explicitly provided are updated.
  // If no field mask is provided, all provided fields in the request are
  // updated. To update all fields, provide a field mask of "*".
  google.protobuf.FieldMask update_mask = 2;

  // If set to true, and the trigger is not found, a new trigger will be
  // created. In this situation, `update_mask` is ignored.
  bool allow_missing = 3;

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the DeleteTrigger method.
message DeleteTriggerRequest {
  // Required. The name of the trigger to be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Trigger"
    }
  ];

  // If provided, the trigger will only be deleted if the etag matches the
  // current etag on the resource.
  string etag = 2;

  // If set to true, and the trigger is not found, the request will succeed
  // but no action will be taken on the server.
  bool allow_missing = 3;

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the GetChannel method.
message GetChannelRequest {
  // Required. The name of the channel to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Channel"
    }
  ];
}

// The request message for the ListChannels method.
message ListChannelsRequest {
  // Required. The parent collection to list channels on.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/Channel"
    }
  ];

  // The maximum number of channels to return on each page.
  //
  // Note: The service may send fewer.
  int32 page_size = 2;

  // The page token; provide the value from the `next_page_token` field in a
  // previous `ListChannels` call to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListChannels` must
  // match the call that provided the page token.
  string page_token = 3;

  // The sorting order of the resources returned. Value should be a
  // comma-separated list of fields. The default sorting order is ascending. To
  // specify descending order for a field, append a `desc` suffix; for example:
  // `name desc, channel_id`.
  string order_by = 4;
}

// The response message for the `ListChannels` method.
message ListChannelsResponse {
  // The requested channels, up to the number specified in `page_size`.
  repeated Channel channels = 1;

  // A page token that can be sent to `ListChannels` to request the next page.
  // If this is empty, then there are no more pages.
  string next_page_token = 2;

  // Unreachable resources, if any.
  repeated string unreachable = 3;
}

// The request message for the CreateChannel method.
message CreateChannelRequest {
  // Required. The parent collection in which to add this channel.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/Channel"
    }
  ];

  // Required. The channel to create.
  Channel channel = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided ID to be assigned to the channel.
  string channel_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the UpdateChannel method.
message UpdateChannelRequest {
  // The channel to be updated.
  Channel channel = 1;

  // The fields to be updated; only fields explicitly provided are updated.
  // If no field mask is provided, all provided fields in the request are
  // updated. To update all fields, provide a field mask of "*".
  google.protobuf.FieldMask update_mask = 2;

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the DeleteChannel method.
message DeleteChannelRequest {
  // Required. The name of the channel to be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Channel"
    }
  ];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the GetProvider method.
message GetProviderRequest {
  // Required. The name of the provider to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Provider"
    }
  ];
}

// The request message for the ListProviders method.
message ListProvidersRequest {
  // Required. The parent of the provider to get.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/Provider"
    }
  ];

  // The maximum number of providers to return on each page.
  int32 page_size = 2;

  // The page token; provide the value from the `next_page_token` field in a
  // previous `ListProviders` call to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListProviders` must
  // match the call that provided the page token.
  string page_token = 3;

  // The sorting order of the resources returned. Value should be a
  // comma-separated list of fields. The default sorting oder is ascending. To
  // specify descending order for a field, append a `desc` suffix; for example:
  // `name desc, _id`.
  string order_by = 4;

  // The filter field that the list request will filter on.
  string filter = 5;
}

// The response message for the `ListProviders` method.
message ListProvidersResponse {
  // The requested providers, up to the number specified in `page_size`.
  repeated Provider providers = 1;

  // A page token that can be sent to `ListProviders` to request the next page.
  // If this is empty, then there are no more pages.
  string next_page_token = 2;

  // Unreachable resources, if any.
  repeated string unreachable = 3;
}

// The request message for the GetChannelConnection method.
message GetChannelConnectionRequest {
  // Required. The name of the channel connection to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/ChannelConnection"
    }
  ];
}

// The request message for the ListChannelConnections method.
message ListChannelConnectionsRequest {
  // Required. The parent collection from which to list channel connections.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/ChannelConnection"
    }
  ];

  // The maximum number of channel connections to return on each page.
  //
  // Note: The service may send fewer responses.
  int32 page_size = 2;

  // The page token; provide the value from the `next_page_token` field in a
  // previous `ListChannelConnections` call to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListChannelConnetions`
  // match the call that provided the page token.
  string page_token = 3;
}

// The response message for the `ListChannelConnections` method.
message ListChannelConnectionsResponse {
  // The requested channel connections, up to the number specified in
  // `page_size`.
  repeated ChannelConnection channel_connections = 1;

  // A page token that can be sent to `ListChannelConnections` to request the
  // next page.
  // If this is empty, then there are no more pages.
  string next_page_token = 2;

  // Unreachable resources, if any.
  repeated string unreachable = 3;
}

// The request message for the CreateChannelConnection method.
message CreateChannelConnectionRequest {
  // Required. The parent collection in which to add this channel connection.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/ChannelConnection"
    }
  ];

  // Required. Channel connection to create.
  ChannelConnection channel_connection = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided ID to be assigned to the channel connection.
  string channel_connection_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// The request message for the DeleteChannelConnection method.
message DeleteChannelConnectionRequest {
  // Required. The name of the channel connection to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/ChannelConnection"
    }
  ];
}

// The request message for the UpdateGoogleChannelConfig method.
message UpdateGoogleChannelConfigRequest {
  // Required. The config to be updated.
  GoogleChannelConfig google_channel_config = 1
      [(google.api.field_behavior) = REQUIRED];

  // The fields to be updated; only fields explicitly provided are updated.
  // If no field mask is provided, all provided fields in the request are
  // updated. To update all fields, provide a field mask of "*".
  google.protobuf.FieldMask update_mask = 2;
}

// The request message for the GetGoogleChannelConfig method.
message GetGoogleChannelConfigRequest {
  // Required. The name of the config to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/GoogleChannelConfig"
    }
  ];
}

// The request message for the GetMessageBus method.
message GetMessageBusRequest {
  // Required. The name of the message bus to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/MessageBus"
    }
  ];
}

// The request message for the ListMessageBuses method.
message ListMessageBusesRequest {
  // Required. The parent collection to list triggers on.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/MessageBus"
    }
  ];

  // Optional. The maximum number of results to return on each page.
  //
  // Note: The service may send fewer.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token; provide the value from the `next_page_token`
  // field in a previous call to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided must match
  // the previous call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The sorting order of the resources returned. Value should be a
  // comma-separated list of fields. The default sorting order is ascending. To
  // specify descending order for a field, append a `desc` suffix; for example:
  // `name desc, update_time`.
  string order_by = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter field that the list request will filter on.
  // Possible filtersare described in https://google.aip.dev/160.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for the `ListMessageBuses` method.
message ListMessageBusesResponse {
  // The requested message buses, up to the number specified in `page_size`.
  repeated MessageBus message_buses = 1;

  // A page token that can be sent to `ListMessageBuses` to request the next
  // page. If this is empty, then there are no more pages.
  string next_page_token = 2;

  // Unreachable resources, if any.
  repeated string unreachable = 3;
}

// The request message for the `ListMessageBusEnrollments` method.
message ListMessageBusEnrollmentsRequest {
  // Required. The parent message bus to list enrollments on.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/MessageBus"
    }
  ];

  // Optional. The maximum number of results to return on each page.
  //
  // Note: The service may send fewer.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token; provide the value from the `next_page_token`
  // field in a previous call to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided must match
  // the previous call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for the `ListMessageBusEnrollments` method.`
message ListMessageBusEnrollmentsResponse {
  // The requested enrollments, up to the number specified in `page_size`.
  repeated string enrollments = 1;

  // A page token that can be sent to `ListMessageBusEnrollments` to request the
  // next page. If this is empty, then there are no more pages.
  string next_page_token = 2;

  // Unreachable resources, if any.
  repeated string unreachable = 3;
}

// The request message for the CreateMessageBus method.
message CreateMessageBusRequest {
  // Required. The parent collection in which to add this message bus.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/MessageBus"
    }
  ];

  // Required. The message bus to create.
  MessageBus message_bus = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided ID to be assigned to the MessageBus. It should
  // match the format (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$)
  string message_bus_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the UpdateMessageBus method.
message UpdateMessageBusRequest {
  // Required. The MessageBus to be updated.
  MessageBus message_bus = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The fields to be updated; only fields explicitly provided are
  // updated. If no field mask is provided, all provided fields in the request
  // are updated. To update all fields, provide a field mask of "*".
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, and the MessageBus is not found, a new MessageBus
  // will be created. In this situation, `update_mask` is ignored.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the DeleteMessageBus method.
message DeleteMessageBusRequest {
  // Required. The name of the MessageBus to be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/MessageBus"
    }
  ];

  // Optional. If provided, the MessageBus will only be deleted if the etag
  // matches the current etag on the resource.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, and the MessageBus is not found, the request will
  // succeed but no action will be taken on the server.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the GetEnrollment method.
message GetEnrollmentRequest {
  // Required. The name of the Enrollment to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Enrollment"
    }
  ];
}

// The request message for the ListEnrollments method.
message ListEnrollmentsRequest {
  // Required. The parent collection to list triggers on.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/Enrollment"
    }
  ];

  // Optional. The maximum number of results to return on each page.
  //
  // Note: The service may send fewer.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token; provide the value from the `next_page_token`
  // field in a previous call to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided must match
  // the previous call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The sorting order of the resources returned. Value should be a
  // comma-separated list of fields. The default sorting order is ascending. To
  // specify descending order for a field, append a `desc` suffix; for example:
  // `name desc, update_time`.
  string order_by = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter field that the list request will filter on.
  // Possible filtersare described in https://google.aip.dev/160.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for the `ListEnrollments` method.
message ListEnrollmentsResponse {
  // The requested Enrollments, up to the number specified in `page_size`.
  repeated Enrollment enrollments = 1;

  // A page token that can be sent to `ListEnrollments` to request the next
  // page. If this is empty, then there are no more pages.
  string next_page_token = 2;

  // Unreachable resources, if any.
  repeated string unreachable = 3;
}

// The request message for the CreateEnrollment method.
message CreateEnrollmentRequest {
  // Required. The parent collection in which to add this enrollment.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/Enrollment"
    }
  ];

  // Required. The enrollment to create.
  Enrollment enrollment = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided ID to be assigned to the Enrollment. It should
  // match the format (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
  string enrollment_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the UpdateEnrollment method.
message UpdateEnrollmentRequest {
  // Required. The Enrollment to be updated.
  Enrollment enrollment = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The fields to be updated; only fields explicitly provided are
  // updated. If no field mask is provided, all provided fields in the request
  // are updated. To update all fields, provide a field mask of "*".
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, and the Enrollment is not found, a new Enrollment
  // will be created. In this situation, `update_mask` is ignored.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the DeleteEnrollment method.
message DeleteEnrollmentRequest {
  // Required. The name of the Enrollment to be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Enrollment"
    }
  ];

  // Optional. If provided, the Enrollment will only be deleted if the etag
  // matches the current etag on the resource.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, and the Enrollment is not found, the request will
  // succeed but no action will be taken on the server.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the GetPipeline method.
message GetPipelineRequest {
  // Required. The name of the pipeline to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Pipeline"
    }
  ];
}

// The request message for the ListPipelines method.
message ListPipelinesRequest {
  // Required. The parent collection to list pipelines on.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/Pipeline"
    }
  ];

  // Optional. The maximum number of results to return on each page.
  //
  // Note: The service may send fewer.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token; provide the value from the `next_page_token`
  // field in a previous call to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided must match
  // the previous call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The sorting order of the resources returned. Value should be a
  // comma-separated list of fields. The default sorting order is ascending. To
  // specify descending order for a field, append a `desc` suffix; for example:
  // `name desc, update_time`.
  string order_by = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter field that the list request will filter on.
  // Possible filters are described in https://google.aip.dev/160.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for the ListPipelines method.
message ListPipelinesResponse {
  // The requested pipelines, up to the number specified in `page_size`.
  repeated Pipeline pipelines = 1;

  // A page token that can be sent to `ListPipelines` to request the next
  // page. If this is empty, then there are no more pages.
  string next_page_token = 2;

  // Unreachable resources, if any.
  repeated string unreachable = 3;
}

// The request message for the CreatePipeline method.
message CreatePipelineRequest {
  // Required. The parent collection in which to add this pipeline.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/Pipeline"
    }
  ];

  // Required. The pipeline to create.
  Pipeline pipeline = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided ID to be assigned to the Pipeline.
  string pipeline_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the UpdatePipeline method.
message UpdatePipelineRequest {
  // Required. The Pipeline to be updated.
  Pipeline pipeline = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The fields to be updated; only fields explicitly provided are
  // updated. If no field mask is provided, all provided fields in the request
  // are updated. To update all fields, provide a field mask of "*".
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, and the Pipeline is not found, a new Pipeline
  // will be created. In this situation, `update_mask` is ignored.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the DeletePipeline method.
message DeletePipelineRequest {
  // Required. The name of the Pipeline to be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Pipeline"
    }
  ];

  // Optional. If provided, the Pipeline will only be deleted if the etag
  // matches the current etag on the resource.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, and the Pipeline is not found, the request will
  // succeed but no action will be taken on the server.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the GetGoogleApiSource method.
message GetGoogleApiSourceRequest {
  // Required. The name of the google api source to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/GoogleApiSource"
    }
  ];
}

// The request message for the ListGoogleApiSources method.
message ListGoogleApiSourcesRequest {
  // Required. The parent collection to list GoogleApiSources on.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/GoogleApiSource"
    }
  ];

  // Optional. The maximum number of results to return on each page.
  //
  // Note: The service may send fewer.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page token; provide the value from the `next_page_token`
  // field in a previous call to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided must match
  // the previous call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The sorting order of the resources returned. Value should be a
  // comma-separated list of fields. The default sorting order is ascending. To
  // specify descending order for a field, append a `desc` suffix; for example:
  // `name desc, update_time`.
  string order_by = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter field that the list request will filter on.
  // Possible filtersare described in https://google.aip.dev/160.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for the `ListGoogleApiSources` method.
message ListGoogleApiSourcesResponse {
  // The requested GoogleApiSources, up to the number specified in `page_size`.
  repeated GoogleApiSource google_api_sources = 1;

  // A page token that can be sent to `ListMessageBusEnrollments` to request the
  // next page. If this is empty, then there are no more pages.
  string next_page_token = 2;

  // Unreachable resources, if any.
  repeated string unreachable = 3;
}

// The request message for the CreateGoogleApiSource method.
message CreateGoogleApiSourceRequest {
  // Required. The parent collection in which to add this google api source.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "eventarc.googleapis.com/GoogleApiSource"
    }
  ];

  // Required. The google api source to create.
  GoogleApiSource google_api_source = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided ID to be assigned to the GoogleApiSource. It
  // should match the format (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
  string google_api_source_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the UpdateGoogleApiSource method.
message UpdateGoogleApiSourceRequest {
  // Required. The GoogleApiSource to be updated.
  GoogleApiSource google_api_source = 1
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The fields to be updated; only fields explicitly provided are
  // updated. If no field mask is provided, all provided fields in the request
  // are updated. To update all fields, provide a field mask of "*".
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, and the GoogleApiSource is not found, a new
  // GoogleApiSource will be created. In this situation, `update_mask` is
  // ignored.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The request message for the DeleteGoogleApiSource method.
message DeleteGoogleApiSourceRequest {
  // Required. The name of the GoogleApiSource to be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/GoogleApiSource"
    }
  ];

  // Optional. If provided, the MessageBus will only be deleted if the etag
  // matches the current etag on the resource.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, and the MessageBus is not found, the request will
  // succeed but no action will be taken on the server.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request and preview the review, but do not
  // post it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
