// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.logging.v1;

import "google/api/field_info.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Eventarc.Logging.V1";
option go_package = "cloud.google.com/go/eventarc/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "PipelineActivityProto";
option java_package = "com.google.cloud.eventarc.logging.v1";
option php_namespace = "Google\\Cloud\\Eventarc\\Logging\\V1";
option ruby_package = "Google::Cloud::Eventarc::Logging::V1";

// Logged during the processing of a message in a pipeline.
message PipelineActivity {
  // Format of the payload.
  enum PayloadFormat {
    // Default value. This value is unused.
    PAYLOAD_FORMAT_UNSPECIFIED = 0;

    // JSON payload format.
    JSON = 1;

    // Proto payload format.
    PROTO = 2;

    // Avro payload format.
    AVRO = 3;
  }

  // Structured log that is emitted when a message is received in this Pipeline
  // (or an error encountered).
  message MessageReceived {
    // Informational details when a message is received from a source.
    string details = 1;

    // Input payload format of the message.
    PayloadFormat input_payload_format = 2;

    // Error encountered when receiving a message from a Channel, or if the
    // message is malformed.
    google.rpc.Status error = 3;
  }

  // Structured log that is emitted when a message is transformed during
  // mediation (or an error encountered) in a Pipeline.
  message MessageTransformed {
    // Informational details when a message is transformed during mediation.
    string details = 1;

    // Error encountered when transforming a message.
    google.rpc.Status error = 2;
  }

  // Structured log that is emitted when a message is converted during
  // mediation (or an error encountered) in a Pipeline.
  message MessageConverted {
    // Informational details when a message is converted during mediation.
    string details = 1;

    // Input payload format of the message.
    PayloadFormat input_payload_format = 2;

    // Output payload format of the message.
    PayloadFormat output_payload_format = 3;

    // Error encountered when converting a message.
    google.rpc.Status error = 4;
  }

  // Structured log that is emitted when a message request is dispatched to a
  // destination (or an error encountered) in a Pipeline.
  message MessageRequestDispatched {
    // Informational details when a message is dispatched to a destination.
    string details = 1;

    // The destination where the event is sent to.
    string destination = 2;

    // Error encountered before dispatching a message, e.g., malformed
    // destination.
    google.rpc.Status error = 3;
  }

  // Structured log that is emitted when a message response (or error) is
  // received from a destination for a message request that was dispatched
  // earlier in a Pipeline.
  message MessageResponseReceived {
    // Enum to encode the retry decision for the message after the response is
    // received.
    enum RetryStatus {
      // Default value. This value is unused.
      RETRY_STATUS_UNSPECIFIED = 0;

      // The Pipeline will retry dispatching the message to the destination.
      WILL_RETRY = 1;

      // The Pipeline will not retry this message anymore.
      RETRY_EXHAUSTED = 2;
    }

    // Informational details when a message response was received by the target,
    // or when the request failed.
    string details = 1;

    // The retry decision for the message after the response is received.
    RetryStatus retry_status = 2;

    // The future time when the message request will be retried. Present if and
    // only if the `retry_status` is `WILL_RETRY`.
    google.protobuf.Timestamp retry_time = 3;

    // The HTTP response code received with the message response.
    int32 http_response_code = 4;

    // Status of the message response received.
    google.rpc.Status error = 5;
  }

  // The unique system generated ID associated with the event passed from the
  // message bus.
  string message_uid = 1 [(google.api.field_info).format = UUID4];

  // The CloudEvent ID and source from the attributes of the event.
  map<string, string> attributes = 2;

  // The point in time when the activity occurred.
  google.protobuf.Timestamp activity_time = 3;

  // Oneof for the different logging activities in a pipeline.
  oneof activity {
    // Logging when a message is received in this Pipeline (or an error
    // encountered).
    MessageReceived message_received = 4;

    // Logging when a message is transformed during mediation (or an error
    // encountered).
    MessageTransformed message_transformed = 5;

    // Logging when a message is converted during mediation (or an error
    // encountered).
    MessageConverted message_converted = 6;

    // Logging when a message request is dispatched to a destination (or an
    // error encountered).
    MessageRequestDispatched message_request_dispatched = 7;

    // Logging when a message response (or error) is received from a
    // destination.
    MessageResponseReceived message_response_received = 8;
  }
}
