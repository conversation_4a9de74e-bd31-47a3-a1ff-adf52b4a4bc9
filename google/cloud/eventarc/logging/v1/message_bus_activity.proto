// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.logging.v1;

import "google/api/field_info.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Eventarc.Logging.V1";
option go_package = "cloud.google.com/go/eventarc/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "MessageBusActivityProto";
option java_package = "com.google.cloud.eventarc.logging.v1";
option php_namespace = "Google\\Cloud\\Eventarc\\Logging\\V1";
option ruby_package = "Google::Cloud::Eventarc::Logging::V1";

// Logged during the processing of an event published to a message bus.
message MessageBusActivity {
  // Structured log message that is emitted when an event is received by a
  // message bus.
  message Received {
    // Informational details about the event received.
    string details = 4;

    // Logged when the enrollment encounters an error.
    google.rpc.Status error = 5;
  }

  // The unique system generated ID when a message bus receives an event. This
  // ID is propagated throughout the request.
  string message_uid = 1 [(google.api.field_info).format = UUID4];

  // The CloudEvent ID and source from the attributes of the event.
  map<string, string> attributes = 2;

  // The point in time when the activity occurred.
  google.protobuf.Timestamp activity_time = 3;

  // One of the activities that can be logged during the processing of an event
  // published to a message bus.
  oneof activity {
    // Logged when an event is received by a message bus.
    Received received = 4;
  }
}
