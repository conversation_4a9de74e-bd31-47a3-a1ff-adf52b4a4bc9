# This build file includes a target for the Ruby wrapper library for
# google-cloud-eventarc.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for eventarc.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "eventarc_ruby_wrapper",
    srcs = ["//google/cloud/eventarc/v1:eventarc_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-eventarc",
        "ruby-cloud-env-prefix=EVENTARC",
        "ruby-cloud-wrapper-of=v1:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/eventarc/",
        "ruby-cloud-api-id=eventarc.googleapis.com",
        "ruby-cloud-api-shortname=eventarc",
    ],
    ruby_cloud_description = "Eventarc lets you asynchronously deliver events from Google services, SaaS, and your own apps using loosely coupled services that react to state changes. Eventarc requires no infrastructure management — you can optimize productivity and costs while building a modern, event-driven solution.",
    ruby_cloud_title = "Eventarc",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-eventarc-ruby",
    deps = [
        ":eventarc_ruby_wrapper",
    ],
)
