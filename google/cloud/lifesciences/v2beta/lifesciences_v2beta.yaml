type: google.api.Service
config_version: 3
name: lifesciences.googleapis.com
title: Cloud Life Sciences API

apis:
- name: google.cloud.lifesciences.v2beta.WorkflowsServiceV2Beta
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.lifesciences.v2beta.ContainerKilledEvent
- name: google.cloud.lifesciences.v2beta.ContainerStartedEvent
- name: google.cloud.lifesciences.v2beta.ContainerStoppedEvent
- name: google.cloud.lifesciences.v2beta.DelayedEvent
- name: google.cloud.lifesciences.v2beta.Event
- name: google.cloud.lifesciences.v2beta.FailedEvent
- name: google.cloud.lifesciences.v2beta.Metadata
- name: google.cloud.lifesciences.v2beta.PullStartedEvent
- name: google.cloud.lifesciences.v2beta.PullStoppedEvent
- name: google.cloud.lifesciences.v2beta.RunPipelineResponse
- name: google.cloud.lifesciences.v2beta.UnexpectedExitStatusEvent
- name: google.cloud.lifesciences.v2beta.WorkerAssignedEvent
- name: google.cloud.lifesciences.v2beta.WorkerReleasedEvent

documentation:
  summary: |-
    Cloud Life Sciences is a suite of services and tools for managing,
    processing, and transforming life sciences data.
  overview: |-
    Cloud Life Sciences is a suite of services and tools for managing,
    processing, and transforming life sciences data.

    ## Getting Started

    For information on setting up a Google Cloud Platform project and using
    Cloud Life Sciences,
    see
    [Quickstart](https://cloud.google.com/life-sciences/docs/quickstart).
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.longrunning.Operations.CancelOperation
    description: |-
      Starts asynchronous cancellation on a long-running operation. The
      server makes a best effort to cancel the operation, but success is
      not guaranteed. Clients may use
      [Operations.GetOperation][google.longrunning.Operations.GetOperation] or
      [Operations.ListOperations][google.longrunning.Operations.ListOperations] to
      check whether the cancellation succeeded or the operation completed
      despite cancellation.
      Authorization requires the following [Google
      IAM](https://cloud.google.com/iam) permission&#58;

      * `lifesciences.operations.cancel`

  - selector: google.longrunning.Operations.GetOperation
    description: |-
      Gets the latest state of a long-running operation. Clients can use this
      method to poll the operation result at intervals as recommended by the
      API service.
      Authorization requires the following [Google
      IAM](https://cloud.google.com/iam) permission&#58;

      * `lifesciences.operations.get`

  - selector: google.longrunning.Operations.ListOperations
    description: |-
      Lists operations that match the specified filter in the
      request. Authorization requires the following [Google
      IAM](https://cloud.google.com/iam) permission&#58;

      * `lifesciences.operations.list`

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v2beta/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v2beta/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v2beta/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2beta/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2beta/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.lifesciences.v2beta.WorkflowsServiceV2Beta.RunPipeline
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
