// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.lifesciences.v2beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/code.proto";

option csharp_namespace = "Google.Cloud.LifeSciences.V2Beta";
option go_package = "cloud.google.com/go/lifesciences/apiv2beta/lifesciencespb;lifesciencespb";
option java_multiple_files = true;
option java_outer_classname = "WorkflowsProto";
option java_package = "com.google.cloud.lifesciences.v2beta";
option objc_class_prefix = "CLSW";
option php_namespace = "Google\\Cloud\\LifeSciences\\V2beta";
option ruby_package = "Google::Cloud::LifeSciences::V2beta";

// A service for running workflows, such as pipelines consisting of Docker
// containers.
service WorkflowsServiceV2Beta {
  option (google.api.default_host) = "lifesciences.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Runs a pipeline.  The returned Operation's [metadata]
  // [google.longrunning.Operation.metadata] field will contain a
  // [google.cloud.lifesciences.v2beta.Metadata][google.cloud.lifesciences.v2beta.Metadata]
  // object describing the status of the pipeline execution. The
  // [response][google.longrunning.Operation.response] field will contain a
  // [google.cloud.lifesciences.v2beta.RunPipelineResponse][google.cloud.lifesciences.v2beta.RunPipelineResponse]
  // object if the pipeline completes successfully.
  //
  // **Note:** Before you can use this method, the *Life Sciences Service Agent*
  // must have access to your project. This is done automatically when the
  // Cloud Life Sciences API is first enabled, but if you delete this permission
  // you must disable and re-enable the API to grant the Life Sciences
  // Service Agent the required permissions.
  // Authorization requires the following [Google
  // IAM](https://cloud.google.com/iam/) permission:
  //
  // * `lifesciences.workflows.run`
  rpc RunPipeline(RunPipelineRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta/{parent=projects/*/locations/*}/pipelines:run"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "RunPipelineResponse"
      metadata_type: "Metadata"
    };
  }
}

// The arguments to the `RunPipeline` method. The requesting user must have
// the `iam.serviceAccounts.actAs` permission for the Cloud Life Sciences
// service account or the request will fail.
message RunPipelineRequest {
  // The project and location that this request should be executed against.
  string parent = 4;

  // Required. The description of the pipeline to run.
  Pipeline pipeline = 1 [(google.api.field_behavior) = REQUIRED];

  // User-defined labels to associate with the returned operation. These
  // labels are not propagated to any Google Cloud Platform resources used by
  // the operation, and can be modified at any time.
  //
  // To associate labels with resources created while executing the operation,
  // see the appropriate resource message (for example, `VirtualMachine`).
  map<string, string> labels = 2;

  // The name of an existing Pub/Sub topic.  The server will publish
  // messages to this topic whenever the status of the operation changes.
  // The Life Sciences Service Agent account must have publisher permissions to
  // the specified topic or notifications will not be sent.
  string pub_sub_topic = 3;
}

// The response to the RunPipeline method, returned in the operation's result
// field on success.
message RunPipelineResponse {}

// Specifies a series of actions to execute, expressed as Docker containers.
message Pipeline {
  // The list of actions to execute, in the order they are specified.
  repeated Action actions = 1;

  // The resources required for execution.
  Resources resources = 2;

  // The environment to pass into every action. Each action can also specify
  // additional environment variables but cannot delete an entry from this map
  // (though they can overwrite it with a different value).
  map<string, string> environment = 3;

  // The encrypted environment to pass into every action. Each action can also
  // specify its own encrypted environment.
  //
  // The secret must decrypt to a JSON-encoded dictionary where key-value pairs
  // serve as environment variable names and their values. The decoded
  // environment variables can overwrite the values specified by the
  // `environment` field.
  Secret encrypted_environment = 5;

  // The maximum amount of time to give the pipeline to complete.  This includes
  // the time spent waiting for a worker to be allocated.  If the pipeline fails
  // to complete before the timeout, it will be cancelled and the error code
  // will be set to DEADLINE_EXCEEDED.
  //
  // If unspecified, it will default to 7 days.
  google.protobuf.Duration timeout = 4;
}

// Specifies a single action that runs a Docker container.
message Action {
  // An optional name for the container. The container hostname will be set to
  // this name, making it useful for inter-container communication. The name
  // must contain only upper and lowercase alphanumeric characters and hyphens
  // and cannot start with a hyphen.
  string container_name = 1;

  // Required. The URI to pull the container image from. Note that all images
  // referenced by actions in the pipeline are pulled before the first action
  // runs. If multiple actions reference the same image, it is only pulled once,
  // ensuring that the same image is used for all actions in a single pipeline.
  //
  // The image URI can be either a complete host and image specification (e.g.,
  // quay.io/biocontainers/samtools), a library and image name (e.g.,
  // google/cloud-sdk) or a bare image name ('bash') to pull from the default
  // library.  No schema is required in any of these cases.
  //
  // If the specified image is not public, the service account specified for
  // the Virtual Machine must have access to pull the images from GCR, or
  // appropriate credentials must be specified in the
  // [google.cloud.lifesciences.v2beta.Action.credentials][google.cloud.lifesciences.v2beta.Action.credentials]
  // field.
  string image_uri = 2 [(google.api.field_behavior) = REQUIRED];

  // If specified, overrides the `CMD` specified in the container. If the
  // container also has an `ENTRYPOINT` the values are used as entrypoint
  // arguments. Otherwise, they are used as a command and arguments to run
  // inside the container.
  repeated string commands = 3;

  // If specified, overrides the `ENTRYPOINT` specified in the container.
  string entrypoint = 4;

  // The environment to pass into the container. This environment is merged
  // with values specified in the
  // [google.cloud.lifesciences.v2beta.Pipeline][google.cloud.lifesciences.v2beta.Pipeline]
  // message, overwriting any duplicate values.
  //
  // In addition to the values passed here, a few other values are
  // automatically injected into the environment. These cannot be hidden or
  // overwritten.
  //
  // `GOOGLE_PIPELINE_FAILED` will be set to "1" if the pipeline failed
  // because an action has exited with a non-zero status (and did not have the
  // `IGNORE_EXIT_STATUS` flag set). This can be used to determine if additional
  // debug or logging actions should execute.
  //
  // `GOOGLE_LAST_EXIT_STATUS` will be set to the exit status of the last
  // non-background action that executed. This can be used by workflow engine
  // authors to determine whether an individual action has succeeded or failed.
  map<string, string> environment = 5;

  // The encrypted environment to pass into the container. This environment is
  // merged with values specified in the
  // [google.cloud.lifesciences.v2beta.Pipeline][google.cloud.lifesciences.v2beta.Pipeline]
  // message, overwriting any duplicate values.
  //
  // The secret must decrypt to a JSON-encoded dictionary where key-value pairs
  // serve as environment variable names and their values. The decoded
  // environment variables can overwrite the values specified by the
  // `environment` field.
  Secret encrypted_environment = 21;

  // An optional identifier for a PID namespace to run the action inside.
  // Multiple actions should use the same string to share a namespace.  If
  // unspecified, a separate isolated namespace is used.
  string pid_namespace = 6;

  // A map of containers to host port mappings for this container. If the
  // container already specifies exposed ports, use the
  // `PUBLISH_EXPOSED_PORTS` flag instead.
  //
  // The host port number must be less than 65536. If it is zero, an unused
  // random port is assigned. To determine the resulting port number, consult
  // the `ContainerStartedEvent` in the operation metadata.
  map<int32, int32> port_mappings = 8;

  // A list of mounts to make available to the action.
  //
  // In addition to the values specified here, every action has a special
  // virtual disk mounted under `/google` that contains log files and other
  // operational components.
  //
  // <ul>
  //   <li><code>/google/logs</code> All logs written during the pipeline
  //   execution.</li>
  //   <li><code>/google/logs/output</code> The combined standard output and
  //   standard error of all actions run as part of the pipeline
  //   execution.</li>
  //   <li><code>/google/logs/action/*/stdout</code> The complete contents of
  //   each individual action's standard output.</li>
  //   <li><code>/google/logs/action/*/stderr</code> The complete contents of
  //   each individual action's standard error output.</li>
  // </ul>
  repeated Mount mounts = 9;

  // Labels to associate with the action. This field is provided to assist
  // workflow engine authors in identifying actions (for example, to indicate
  // what sort of action they perform, such as localization or debugging).
  // They are returned in the operation metadata, but are otherwise ignored.
  map<string, string> labels = 10;

  // If the specified image is hosted on a private registry other than Google
  // Container Registry, the credentials required to pull the image must be
  // specified here as an encrypted secret.
  //
  // The secret must decrypt to a JSON-encoded dictionary containing both
  // `username` and `password` keys.
  Secret credentials = 11;

  // The maximum amount of time to give the action to complete. If the action
  // fails to complete before the timeout, it will be terminated and the exit
  // status will be non-zero. The pipeline will continue or terminate based
  // on the rules defined by the `ALWAYS_RUN` and `IGNORE_EXIT_STATUS` flags.
  google.protobuf.Duration timeout = 12;

  // Normally, a non-zero exit status causes the pipeline to fail. This flag
  // allows execution of other actions to continue instead.
  bool ignore_exit_status = 13;

  // This flag allows an action to continue running in the background while
  // executing subsequent actions. This is useful to provide services to
  // other actions (or to provide debugging support tools like SSH servers).
  bool run_in_background = 14;

  // By default, after an action fails, no further actions are run. This flag
  // indicates that this action must be run even if the pipeline has already
  // failed. This is useful for actions that copy output files off of the VM
  // or for debugging. Note that no actions will be run if image prefetching
  // fails.
  bool always_run = 15;

  // Enable access to the FUSE device for this action. Filesystems can then
  // be mounted into disks shared with other actions. The other actions do
  // not need the `enable_fuse` flag to access the mounted filesystem.
  //
  // This has the effect of causing the container to be executed with
  // `CAP_SYS_ADMIN` and exposes `/dev/fuse` to the container, so use it only
  // for containers you trust.
  bool enable_fuse = 16;

  // Exposes all ports specified by `EXPOSE` statements in the container. To
  // discover the host side port numbers, consult the `ACTION_STARTED` event
  // in the operation metadata.
  bool publish_exposed_ports = 17;

  // All container images are typically downloaded before any actions are
  // executed. This helps prevent typos in URIs or issues like lack of disk
  // space from wasting large amounts of compute resources.
  //
  // If set, this flag prevents the worker from downloading the image until
  // just before the action is executed.
  bool disable_image_prefetch = 18;

  // A small portion of the container's standard error stream is typically
  // captured and returned inside the `ContainerStoppedEvent`. Setting this
  // flag disables this functionality.
  bool disable_standard_error_capture = 19;

  // Prevents the container from accessing the external network.
  bool block_external_network = 20;
}

// Holds encrypted information that is only decrypted and stored in RAM
// by the worker VM when running the pipeline.
message Secret {
  // The name of the Cloud KMS key that will be used to decrypt the secret
  // value. The VM service account must have the required permissions and
  // authentication scopes to invoke the `decrypt` method on the specified key.
  string key_name = 1;

  // The value of the cipherText response from the `encrypt` method. This field
  // is intentionally unaudited.
  string cipher_text = 2;
}

// Carries information about a particular disk mount inside a container.
message Mount {
  // The name of the disk to mount, as specified in the resources section.
  string disk = 1;

  // The path to mount the disk inside the container.
  string path = 2;

  // If true, the disk is mounted read-only inside the container.
  bool read_only = 3;
}

// The system resources for the pipeline run.
//
// At least one zone or region must be specified or the pipeline run will fail.
message Resources {
  // The list of regions allowed for VM allocation. If set, the `zones` field
  // must not be set.
  repeated string regions = 2;

  // The list of zones allowed for VM allocation. If set, the `regions` field
  // must not be set.
  repeated string zones = 3;

  // The virtual machine specification.
  VirtualMachine virtual_machine = 4;
}

// Carries information about a Compute Engine VM resource.
message VirtualMachine {
  // Required. The machine type of the virtual machine to create. Must be the
  // short name of a standard machine type (such as "n1-standard-1") or a custom
  // machine type (such as "custom-1-4096", where "1" indicates the number of
  // vCPUs and "4096" indicates the memory in MB). See [Creating an instance
  // with a custom machine
  // type](https://cloud.google.com/compute/docs/instances/creating-instance-with-custom-machine-type#create)
  // for more specifications on creating a custom machine type.
  string machine_type = 1 [(google.api.field_behavior) = REQUIRED];

  // If true, allocate a preemptible VM.
  bool preemptible = 2;

  // Optional set of labels to apply to the VM and any attached disk resources.
  // These labels must adhere to the [name and value
  // restrictions](https://cloud.google.com/compute/docs/labeling-resources) on
  // VM labels imposed by Compute Engine.
  //
  // Labels keys with the prefix 'google-' are reserved for use by Google.
  //
  // Labels applied at creation time to the VM. Applied on a best-effort basis
  // to attached disk resources shortly after VM creation.
  map<string, string> labels = 3;

  // The list of disks to create and attach to the VM.
  //
  // Specify either the `volumes[]` field or the `disks[]` field, but not both.
  repeated Disk disks = 4;

  // The VM network configuration.
  Network network = 5;

  // The list of accelerators to attach to the VM.
  repeated Accelerator accelerators = 6;

  // The service account to install on the VM. This account does not need
  // any permissions other than those required by the pipeline.
  ServiceAccount service_account = 7;

  // The size of the boot disk, in GB. The boot disk must be large
  // enough to accommodate all of the Docker images from each action in the
  // pipeline at the same time. If not specified, a small but reasonable
  // default value is used.
  int32 boot_disk_size_gb = 8;

  // The CPU platform to request. An instance based on a newer platform can be
  // allocated, but never one with fewer capabilities. The value of this
  // parameter must be a valid Compute Engine CPU platform name (such as "Intel
  // Skylake"). This parameter is only useful for carefully optimized work
  // loads where the CPU platform has a significant impact.
  //
  // For more information about the effect of this parameter, see
  // https://cloud.google.com/compute/docs/instances/specify-min-cpu-platform.
  string cpu_platform = 9;

  // The host operating system image to use.
  //
  // Currently, only Container-Optimized OS images can be used.
  //
  // The default value is `projects/cos-cloud/global/images/family/cos-stable`,
  // which selects the latest stable release of Container-Optimized OS.
  //
  // This option is provided to allow testing against the beta release of the
  // operating system to ensure that the new version does not interact
  // negatively with production pipelines.
  //
  // To test a pipeline against the beta release of Container-Optimized OS,
  // use the value `projects/cos-cloud/global/images/family/cos-beta`.
  string boot_image = 10;

  // The NVIDIA driver version to use when attaching an NVIDIA GPU accelerator.
  // The version specified here must be compatible with the GPU libraries
  // contained in the container being executed, and must be one of the drivers
  // hosted in the `nvidia-drivers-us-public` bucket on Google Cloud Storage.
  string nvidia_driver_version = 11 [deprecated = true];

  // Whether Stackdriver monitoring should be enabled on the VM.
  bool enable_stackdriver_monitoring = 12;

  // The Compute Engine Disk Images to use as a Docker cache. The disks will be
  // mounted into the Docker folder in a way that the images present in the
  // cache will not need to be pulled. The digests of the cached images must
  // match those of the tags used or the latest version will still be pulled.
  // The root directory of the ext4 image must contain `image` and `overlay2`
  // directories copied from the Docker directory of a VM where the desired
  // Docker images have already been pulled. Any images pulled that are not
  // cached will be stored on the first cache disk instead of the boot disk.
  // Only a single image is supported.
  repeated string docker_cache_images = 13;

  // The list of disks and other storage to create or attach to the VM.
  //
  // Specify either the `volumes[]` field or the `disks[]` field, but not both.
  repeated Volume volumes = 14;

  // If specified, the VM will only be allocated inside the matching
  // reservation. It will fail if the VM parameters don't match the reservation.
  string reservation = 15;
}

// Carries information about a Google Cloud service account.
message ServiceAccount {
  // Email address of the service account. If not specified, the default
  // Compute Engine service account for the project will be used.
  string email = 1;

  // List of scopes to be enabled for this service account on the VM, in
  // addition to the cloud-platform API scope that will be added by default.
  repeated string scopes = 2;
}

// Carries information about an accelerator that can be attached to a VM.
message Accelerator {
  // The accelerator type string (for example, "nvidia-tesla-t4").
  //
  // Only NVIDIA GPU accelerators are currently supported. If an NVIDIA GPU is
  // attached, the required runtime libraries will be made available to all
  // containers under `/usr/local/nvidia`. The driver version to install must
  // be specified using the NVIDIA driver version parameter on the virtual
  // machine specification. Note that attaching a GPU increases the worker VM
  // startup time by a few minutes.
  string type = 1;

  // How many accelerators of this type to attach.
  int64 count = 2;
}

// VM networking options.
message Network {
  // The network name to attach the VM's network interface to. The value will
  // be prefixed with `global/networks/` unless it contains a `/`, in which
  // case it is assumed to be a fully specified network resource URL.
  //
  // If unspecified, the global default network is used.
  string network = 1;

  // If set to true, do not attach a public IP address to the VM. Note that
  // without a public IP address, additional configuration is required to
  // allow the VM to access Google services.
  //
  // See https://cloud.google.com/vpc/docs/configure-private-google-access
  // for more information.
  bool use_private_address = 2;

  // If the specified network is configured for custom subnet creation, the
  // name of the subnetwork to attach the instance to must be specified here.
  //
  // The value is prefixed with `regions/*/subnetworks/` unless it contains a
  // `/`, in which case it is assumed to be a fully specified subnetwork
  // resource URL.
  //
  // If the `*` character appears in the value, it is replaced with the region
  // that the virtual machine has been allocated in.
  string subnetwork = 3;
}

// Carries information about a disk that can be attached to a VM.
//
// See https://cloud.google.com/compute/docs/disks/performance for more
// information about disk type, size, and performance considerations.
//
// Specify either [`Volume`][google.cloud.lifesciences.v2beta.Volume] or
// [`Disk`][google.cloud.lifesciences.v2beta.Disk], but not both.
message Disk {
  // A user-supplied name for the disk. Used when mounting the disk into
  // actions. The name must contain only upper and lowercase alphanumeric
  // characters and hyphens and cannot start with a hyphen.
  string name = 1;

  // The size, in GB, of the disk to attach. If the size is not
  // specified, a default is chosen to ensure reasonable I/O performance.
  //
  // If the disk type is specified as `local-ssd`, multiple local drives are
  // automatically combined to provide the requested size. Note, however, that
  // each physical SSD is 375GB in size, and no more than 8 drives can be
  // attached to a single instance.
  int32 size_gb = 2;

  // The Compute Engine disk type. If unspecified, `pd-standard` is used.
  string type = 3;

  // An optional image to put on the disk before attaching it to the VM.
  string source_image = 4;
}

// Carries information about storage that can be attached to a VM.
//
// Specify either [`Volume`][google.cloud.lifesciences.v2beta.Volume] or
// [`Disk`][google.cloud.lifesciences.v2beta.Disk], but not both.
message Volume {
  // A user-supplied name for the volume. Used when mounting the volume into
  // [`Actions`][google.cloud.lifesciences.v2beta.Action]. The name must contain
  // only upper and lowercase alphanumeric characters and hyphens and cannot
  // start with a hyphen.
  string volume = 1;

  oneof storage {
    // Configuration for a persistent disk.
    PersistentDisk persistent_disk = 2;

    // Configuration for a existing disk.
    ExistingDisk existing_disk = 3;

    // Configuration for an NFS mount.
    NFSMount nfs_mount = 4;
  }
}

// Configuration for a persistent disk to be attached to the VM.
//
// See https://cloud.google.com/compute/docs/disks/performance for more
// information about disk type, size, and performance considerations.
message PersistentDisk {
  // The size, in GB, of the disk to attach. If the size is not
  // specified, a default is chosen to ensure reasonable I/O performance.
  //
  // If the disk type is specified as `local-ssd`, multiple local drives are
  // automatically combined to provide the requested size. Note, however, that
  // each physical SSD is 375GB in size, and no more than 8 drives can be
  // attached to a single instance.
  int32 size_gb = 1;

  // The Compute Engine disk type. If unspecified, `pd-standard` is used.
  string type = 2;

  // An image to put on the disk before attaching it to the VM.
  string source_image = 3;
}

// Configuration for an existing disk to be attached to the VM.
message ExistingDisk {
  // If `disk` contains slashes, the Cloud Life Sciences API assumes that it is
  // a complete URL for the disk.  If `disk` does not contain slashes, the Cloud
  // Life Sciences API assumes that the disk is a zonal disk and a URL will be
  // generated of the form `zones/<zone>/disks/<disk>`, where `<zone>` is the
  // zone in which the instance is allocated. The disk must be ext4 formatted.
  //
  // If all `Mount` references to this disk have the `read_only` flag set to
  // true, the disk will be attached in `read-only` mode and can be shared with
  // other instances. Otherwise, the disk will be available for writing but
  // cannot be shared.
  string disk = 1;
}

// Configuration for an `NFSMount` to be attached to the VM.
message NFSMount {
  // A target NFS mount. The target must be specified as `address:/mount".
  string target = 1;
}

// Carries information about the pipeline execution that is returned
// in the long running operation's metadata field.
message Metadata {
  // The pipeline this operation represents.
  Pipeline pipeline = 1;

  // The user-defined labels associated with this operation.
  map<string, string> labels = 2;

  // The list of events that have happened so far during the execution of this
  // operation.
  repeated Event events = 3;

  // The time at which the operation was created by the API.
  google.protobuf.Timestamp create_time = 4;

  // The first time at which resources were allocated to execute the pipeline.
  google.protobuf.Timestamp start_time = 5;

  // The time at which execution was completed and resources were cleaned up.
  google.protobuf.Timestamp end_time = 6;

  // The name of the Cloud Pub/Sub topic where notifications of operation status
  // changes are sent.
  string pub_sub_topic = 7;
}

// Carries information about events that occur during pipeline execution.
message Event {
  // The time at which the event occurred.
  google.protobuf.Timestamp timestamp = 1;

  // A human-readable description of the event. Note that these strings can
  // change at any time without notice. Any application logic must use the
  // information in the `details` field.
  string description = 2;

  // Machine-readable details about the event.
  oneof details {
    // See
    // [google.cloud.lifesciences.v2beta.DelayedEvent][google.cloud.lifesciences.v2beta.DelayedEvent].
    DelayedEvent delayed = 17;

    // See
    // [google.cloud.lifesciences.v2beta.WorkerAssignedEvent][google.cloud.lifesciences.v2beta.WorkerAssignedEvent].
    WorkerAssignedEvent worker_assigned = 18;

    // See
    // [google.cloud.lifesciences.v2beta.WorkerReleasedEvent][google.cloud.lifesciences.v2beta.WorkerReleasedEvent].
    WorkerReleasedEvent worker_released = 19;

    // See
    // [google.cloud.lifesciences.v2beta.PullStartedEvent][google.cloud.lifesciences.v2beta.PullStartedEvent].
    PullStartedEvent pull_started = 20;

    // See
    // [google.cloud.lifesciences.v2beta.PullStoppedEvent][google.cloud.lifesciences.v2beta.PullStoppedEvent].
    PullStoppedEvent pull_stopped = 21;

    // See
    // [google.cloud.lifesciences.v2beta.ContainerStartedEvent][google.cloud.lifesciences.v2beta.ContainerStartedEvent].
    ContainerStartedEvent container_started = 22;

    // See
    // [google.cloud.lifesciences.v2beta.ContainerStoppedEvent][google.cloud.lifesciences.v2beta.ContainerStoppedEvent].
    ContainerStoppedEvent container_stopped = 23;

    // See
    // [google.cloud.lifesciences.v2beta.ContainerKilledEvent][google.cloud.lifesciences.v2beta.ContainerKilledEvent].
    ContainerKilledEvent container_killed = 24;

    // See
    // [google.cloud.lifesciences.v2beta.UnexpectedExitStatusEvent][google.cloud.lifesciences.v2beta.UnexpectedExitStatusEvent].
    UnexpectedExitStatusEvent unexpected_exit_status = 25;

    // See
    // [google.cloud.lifesciences.v2beta.FailedEvent][google.cloud.lifesciences.v2beta.FailedEvent].
    FailedEvent failed = 26;
  }
}

// An event generated whenever a resource limitation or transient error
// delays execution of a pipeline that was otherwise ready to run.
message DelayedEvent {
  // A textual description of the cause of the delay. The string can change
  // without notice because it is often generated by another service (such as
  // Compute Engine).
  string cause = 1;

  // If the delay was caused by a resource shortage, this field lists the
  // Compute Engine metrics that are preventing this operation from running
  // (for example, `CPUS` or `INSTANCES`). If the particular metric is not
  // known, a single `UNKNOWN` metric will be present.
  repeated string metrics = 2;
}

// An event generated after a worker VM has been assigned to run the
// pipeline.
message WorkerAssignedEvent {
  // The zone the worker is running in.
  string zone = 1;

  // The worker's instance name.
  string instance = 2;

  // The machine type that was assigned for the worker.
  string machine_type = 3;
}

// An event generated when the worker VM that was assigned to the pipeline
// has been released (deleted).
message WorkerReleasedEvent {
  // The zone the worker was running in.
  string zone = 1;

  // The worker's instance name.
  string instance = 2;
}

// An event generated when the worker starts pulling an image.
message PullStartedEvent {
  // The URI of the image that was pulled.
  string image_uri = 1;
}

// An event generated when the worker stops pulling an image.
message PullStoppedEvent {
  // The URI of the image that was pulled.
  string image_uri = 1;
}

// An event generated when a container starts.
message ContainerStartedEvent {
  // The numeric ID of the action that started this container.
  int32 action_id = 1;

  // The container-to-host port mappings installed for this container. This
  // set will contain any ports exposed using the `PUBLISH_EXPOSED_PORTS` flag
  // as well as any specified in the `Action` definition.
  map<int32, int32> port_mappings = 2;

  // The public IP address that can be used to connect to the container. This
  // field is only populated when at least one port mapping is present. If the
  // instance was created with a private address, this field will be empty even
  // if port mappings exist.
  string ip_address = 3;
}

// An event generated when a container exits.
message ContainerStoppedEvent {
  // The numeric ID of the action that started this container.
  int32 action_id = 1;

  // The exit status of the container.
  int32 exit_status = 2;

  // The tail end of any content written to standard error by the container.
  // If the content emits large amounts of debugging noise or contains
  // sensitive information, you can prevent the content from being printed by
  // setting the `DISABLE_STANDARD_ERROR_CAPTURE` flag.
  //
  // Note that only a small amount of the end of the stream is captured here.
  // The entire stream is stored in the `/google/logs` directory mounted into
  // each action, and can be copied off the machine as described elsewhere.
  string stderr = 3;
}

// An event generated when the execution of a container results in a
// non-zero exit status that was not otherwise ignored. Execution will
// continue, but only actions that are flagged as `ALWAYS_RUN` will be
// executed. Other actions will be skipped.
message UnexpectedExitStatusEvent {
  // The numeric ID of the action that started the container.
  int32 action_id = 1;

  // The exit status of the container.
  int32 exit_status = 2;
}

// An event generated when a container is forcibly terminated by the
// worker. Currently, this only occurs when the container outlives the
// timeout specified by the user.
message ContainerKilledEvent {
  // The numeric ID of the action that started the container.
  int32 action_id = 1;
}

// An event generated when the execution of a pipeline has failed. Note
// that other events can continue to occur after this event.
message FailedEvent {
  // The Google standard error code that best describes this failure.
  google.rpc.Code code = 1;

  // The human-readable description of the cause of the failure.
  string cause = 2;
}
