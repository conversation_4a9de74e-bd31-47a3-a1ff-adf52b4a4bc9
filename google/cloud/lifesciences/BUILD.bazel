# This build file includes a target for the Ruby wrapper library for
# google-cloud-life_sciences.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for lifesciences.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2beta in this case.
ruby_cloud_gapic_library(
    name = "lifesciences_ruby_wrapper",
    srcs = ["//google/cloud/lifesciences/v2beta:lifesciences_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-life_sciences",
        "ruby-cloud-env-prefix=LIFE_SCIENCES",
        "ruby-cloud-wrapper-of=v2beta:0.7",
        "ruby-cloud-product-url=https://cloud.google.com/life-sciences/",
        "ruby-cloud-api-id=lifesciences.googleapis.com",
        "ruby-cloud-api-shortname=lifesciences",
        "ruby-cloud-service-override=WorkflowsServiceV2Beta=WorkflowsService",
    ],
    ruby_cloud_description = "Cloud Life Sciences is a suite of services and tools for managing, processing, and transforming life sciences data. It also enables advanced insights and operational workflows using highly scalable and compliant infrastructure.",
    ruby_cloud_title = "Cloud Life Sciences",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-lifesciences-ruby",
    deps = [
        ":lifesciences_ruby_wrapper",
    ],
)
