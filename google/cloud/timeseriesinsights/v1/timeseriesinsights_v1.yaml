type: google.api.Service
config_version: 3
name: timeseriesinsights.googleapis.com
title: Timeseries Insights API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.timeseriesinsights.v1.TimeseriesInsightsController

documentation:
  summary: |-
    Provides a Timeseries Insights service which operates over time series
    data. Users can perform time series spike, trend, and anomaly detection.
    With a straightforward API and easy to understand results, the service
    makes it simple to gather insights from large amounts of time series data
    (e.g. monitoring datasets) and integrate these insights in their
    applications.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 10.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 10.0
  - selector: 'google.cloud.timeseriesinsights.v1.TimeseriesInsightsController.*'
    deadline: 10.0
  - selector: google.cloud.timeseriesinsights.v1.TimeseriesInsightsController.QueryDataSet
    deadline: 300.0

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.timeseriesinsights.v1.TimeseriesInsightsController.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
