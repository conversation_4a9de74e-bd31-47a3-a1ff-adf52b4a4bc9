{"methodConfig": [{"name": [{"service": "google.cloud.timeseriesinsights.v1.TimeseriesInsightsController", "method": "ListDataSets"}, {"service": "google.cloud.timeseriesinsights.v1.TimeseriesInsightsController", "method": "QueryDataSet"}, {"service": "google.cloud.timeseriesinsights.v1.TimeseriesInsightsController", "method": "EvaluateSlice"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.timeseriesinsights.v1.TimeseriesInsightsController", "method": "CreateDataSet"}, {"service": "google.cloud.timeseriesinsights.v1.TimeseriesInsightsController", "method": "DeleteDataSet"}, {"service": "google.cloud.timeseriesinsights.v1.TimeseriesInsightsController", "method": "AppendEvents"}], "timeout": "60s"}]}