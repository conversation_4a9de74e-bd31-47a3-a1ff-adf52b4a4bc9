# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "timeseriesinsights_proto",
    srcs = [
        "timeseries_insights.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "timeseriesinsights_proto_with_info",
    deps = [
        ":timeseriesinsights_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "timeseriesinsights_java_proto",
    deps = [":timeseriesinsights_proto"],
)

java_grpc_library(
    name = "timeseriesinsights_java_grpc",
    srcs = [":timeseriesinsights_proto"],
    deps = [":timeseriesinsights_java_proto"],
)

java_gapic_library(
    name = "timeseriesinsights_java_gapic",
    srcs = [":timeseriesinsights_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "timeseries_insights_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "timeseriesinsights_v1.yaml",
    test_deps = [
        ":timeseriesinsights_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":timeseriesinsights_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "timeseriesinsights_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.timeseriesinsights.v1.TimeseriesInsightsControllerClientHttpJsonTest",
        "com.google.cloud.timeseriesinsights.v1.TimeseriesInsightsControllerClientTest",
    ],
    runtime_deps = [":timeseriesinsights_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-timeseriesinsights-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":timeseriesinsights_java_gapic",
        ":timeseriesinsights_java_grpc",
        ":timeseriesinsights_java_proto",
        ":timeseriesinsights_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "timeseriesinsights_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/timeseriesinsights/apiv1/timeseriesinsightspb",
    protos = [":timeseriesinsights_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "timeseriesinsights_go_gapic",
    srcs = [":timeseriesinsights_proto_with_info"],
    grpc_service_config = "timeseries_insights_grpc_service_config.json",
    importpath = "cloud.google.com/go/timeseriesinsights/apiv1;timeseriesinsights",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "timeseriesinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":timeseriesinsights_go_proto",
        "//google/cloud/location:location_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-timeseriesinsights-v1-go",
    deps = [
        ":timeseriesinsights_go_gapic",
        ":timeseriesinsights_go_gapic_srcjar-metadata.srcjar",
        ":timeseriesinsights_go_gapic_srcjar-snippets.srcjar",
        ":timeseriesinsights_go_gapic_srcjar-test.srcjar",
        ":timeseriesinsights_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "timeseriesinsights_py_gapic",
    srcs = [":timeseriesinsights_proto"],
    grpc_service_config = "timeseries_insights_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "timeseriesinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "timeseriesinsights_py_gapic_test",
    srcs = [
        "timeseriesinsights_py_gapic_pytest.py",
        "timeseriesinsights_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":timeseriesinsights_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "timeseriesinsights-v1-py",
    deps = [
        ":timeseriesinsights_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "timeseriesinsights_php_proto",
    deps = [":timeseriesinsights_proto"],
)

php_gapic_library(
    name = "timeseriesinsights_php_gapic",
    srcs = [":timeseriesinsights_proto_with_info"],
    grpc_service_config = "timeseries_insights_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "timeseriesinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [":timeseriesinsights_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-timeseriesinsights-v1-php",
    deps = [
        ":timeseriesinsights_php_gapic",
        ":timeseriesinsights_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "timeseriesinsights_nodejs_gapic",
    package_name = "@google-cloud/timeseriesinsights",
    src = ":timeseriesinsights_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "timeseries_insights_grpc_service_config.json",
    package = "google.cloud.timeseriesinsights.v1",
    rest_numeric_enums = True,
    service_yaml = "timeseriesinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "timeseriesinsights-v1-nodejs",
    deps = [
        ":timeseriesinsights_nodejs_gapic",
        ":timeseriesinsights_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "timeseriesinsights_ruby_proto",
    deps = [":timeseriesinsights_proto"],
)

ruby_grpc_library(
    name = "timeseriesinsights_ruby_grpc",
    srcs = [":timeseriesinsights_proto"],
    deps = [":timeseriesinsights_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "timeseriesinsights_ruby_gapic",
    srcs = [":timeseriesinsights_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-timeseriesinsights-v1"],
    grpc_service_config = "timeseries_insights_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "timeseriesinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":timeseriesinsights_ruby_grpc",
        ":timeseriesinsights_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-timeseriesinsights-v1-ruby",
    deps = [
        ":timeseriesinsights_ruby_gapic",
        ":timeseriesinsights_ruby_grpc",
        ":timeseriesinsights_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# C# generation is currently commented out as the protos don't contain all the
# required comments, and we want to be able to validate comments in the C#
# generator. See b/336699966.
# load(
#     "@com_google_googleapis_imports//:imports.bzl",
#     "csharp_gapic_assembly_pkg",
#     "csharp_gapic_library",
#     "csharp_grpc_library",
#     "csharp_proto_library",
# )
#
# csharp_proto_library(
#     name = "timeseriesinsights_csharp_proto",
#     deps = [":timeseriesinsights_proto"],
# )
#
# csharp_grpc_library(
#     name = "timeseriesinsights_csharp_grpc",
#     srcs = [":timeseriesinsights_proto"],
#     deps = [":timeseriesinsights_csharp_proto"],
# )
#
# csharp_gapic_library(
#     name = "timeseriesinsights_csharp_gapic",
#     srcs = [":timeseriesinsights_proto_with_info"],
#     common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
#     grpc_service_config = "timeseries_insights_grpc_service_config.json",
#     rest_numeric_enums = True,
#     service_yaml = "timeseriesinsights_v1.yaml",
#     transport = "grpc+rest",
#     deps = [
#         ":timeseriesinsights_csharp_grpc",
#         ":timeseriesinsights_csharp_proto",
#     ],
# )
#
# # Open Source Packages
# csharp_gapic_assembly_pkg(
#     name = "google-cloud-timeseriesinsights-v1-csharp",
#     deps = [
#         ":timeseriesinsights_csharp_gapic",
#         ":timeseriesinsights_csharp_grpc",
#         ":timeseriesinsights_csharp_proto",
#     ],
# )

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "timeseriesinsights_cc_proto",
    deps = [":timeseriesinsights_proto"],
)

cc_grpc_library(
    name = "timeseriesinsights_cc_grpc",
    srcs = [":timeseriesinsights_proto"],
    grpc_only = True,
    deps = [":timeseriesinsights_cc_proto"],
)
