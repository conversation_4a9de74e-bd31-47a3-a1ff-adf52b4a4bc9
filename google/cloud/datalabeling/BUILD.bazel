# This build file includes a target for the Ruby wrapper library for
# google-cloud-data_labeling.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for datalabeling.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "datalabeling_ruby_wrapper",
    srcs = ["//google/cloud/datalabeling/v1beta1:datalabeling_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-data_labeling",
        "ruby-cloud-env-prefix=DATA_LABELING",
        "ruby-cloud-wrapper-of=v1beta1:0.7",
        "ruby-cloud-product-url=https://cloud.google.com/ai-platform/data-labeling/docs",
        "ruby-cloud-api-id=datalabeling.googleapis.com",
        "ruby-cloud-api-shortname=datalabeling",
    ],
    ruby_cloud_description = "AI Platform Data Labeling Service lets you work with human labelers to generate highly accurate labels for a collection of data that you can use in machine learning models.",
    ruby_cloud_title = "AI Platform Data Labeling Service",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-datalabeling-ruby",
    deps = [
        ":datalabeling_ruby_wrapper",
    ],
)
