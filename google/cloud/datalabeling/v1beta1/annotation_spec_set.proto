// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.datalabeling.v1beta1;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.DataLabeling.V1Beta1";
option go_package = "cloud.google.com/go/datalabeling/apiv1beta1/datalabelingpb;datalabelingpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datalabeling.v1beta1";
option php_namespace = "Google\\Cloud\\DataLabeling\\V1beta1";
option ruby_package = "Google::Cloud::DataLabeling::V1beta1";

// An AnnotationSpecSet is a collection of label definitions. For example, in
// image classification tasks, you define a set of possible labels for images as
// an AnnotationSpecSet. An AnnotationSpecSet is immutable upon creation.
message AnnotationSpecSet {
  option (google.api.resource) = {
    type: "datalabeling.googleapis.com/AnnotationSpecSet"
    pattern: "projects/{project}/annotationSpecSets/{annotation_spec_set}"
  };

  // Output only. The AnnotationSpecSet resource name in the following format:
  //
  // "projects/<var>{project_id}</var>/annotationSpecSets/<var>{annotation_spec_set_id}</var>"
  string name = 1;

  // Required. The display name for AnnotationSpecSet that you define when you
  // create it. Maximum of 64 characters.
  string display_name = 2;

  // Optional. User-provided description of the annotation specification set.
  // The description can be up to 10,000 characters long.
  string description = 3;

  // Required. The array of AnnotationSpecs that you define when you create the
  // AnnotationSpecSet. These are the possible labels for the labeling task.
  repeated AnnotationSpec annotation_specs = 4;

  // Output only. The names of any related resources that are blocking changes
  // to the annotation spec set.
  repeated string blocking_resources = 5;
}

// Container of information related to one possible annotation that can be used
// in a labeling task. For example, an image classification task where images
// are labeled as `dog` or `cat` must reference an AnnotationSpec for `dog` and
// an AnnotationSpec for `cat`.
message AnnotationSpec {
  // Required. The display name of the AnnotationSpec. Maximum of 64 characters.
  string display_name = 1;

  // Optional. User-provided description of the annotation specification.
  // The description can be up to 10,000 characters long.
  string description = 2;
}
