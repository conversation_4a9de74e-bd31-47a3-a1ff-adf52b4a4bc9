# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "datalabeling_proto",
    srcs = [
        "annotation.proto",
        "annotation_spec_set.proto",
        "data_labeling_service.proto",
        "data_payloads.proto",
        "dataset.proto",
        "evaluation.proto",
        "evaluation_job.proto",
        "human_annotation_config.proto",
        "instruction.proto",
        "operations.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "datalabeling_proto_with_info",
    deps = [
        ":datalabeling_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "datalabeling_java_proto",
    deps = [":datalabeling_proto"],
)

java_grpc_library(
    name = "datalabeling_java_grpc",
    srcs = [":datalabeling_proto"],
    deps = [":datalabeling_java_proto"],
)

java_gapic_library(
    name = "datalabeling_java_gapic",
    srcs = [":datalabeling_proto_with_info"],
    grpc_service_config = "datalabeling_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datalabeling_v1beta1.yaml",
    test_deps = [
        ":datalabeling_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":datalabeling_java_proto",
    ],
)

java_gapic_test(
    name = "datalabeling_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.datalabeling.v1beta1.DataLabelingServiceClientTest",
    ],
    runtime_deps = [":datalabeling_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-datalabeling-v1beta1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":datalabeling_java_gapic",
        ":datalabeling_java_grpc",
        ":datalabeling_java_proto",
        ":datalabeling_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "datalabeling_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/datalabeling/apiv1beta1/datalabelingpb",
    protos = [":datalabeling_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "datalabeling_go_gapic",
    srcs = [":datalabeling_proto_with_info"],
    grpc_service_config = "datalabeling_grpc_service_config.json",
    importpath = "cloud.google.com/go/datalabeling/apiv1beta1;datalabeling",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "datalabeling_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datalabeling_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-datalabeling-v1beta1-go",
    deps = [
        ":datalabeling_go_gapic",
        ":datalabeling_go_gapic_srcjar-snippets.srcjar",
        ":datalabeling_go_gapic_srcjar-test.srcjar",
        ":datalabeling_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "datalabeling_py_gapic",
    srcs = [":datalabeling_proto"],
    grpc_service_config = "datalabeling_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datalabeling_v1beta1.yaml",
    transport = "grpc",
)

py_test(
    name = "datalabeling_py_gapic_test",
    srcs = [
        "datalabeling_py_gapic_pytest.py",
        "datalabeling_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":datalabeling_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "datalabeling-v1beta1-py",
    deps = [
        ":datalabeling_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "datalabeling_php_proto",
    deps = [":datalabeling_proto"],
)

php_gapic_library(
    name = "datalabeling_php_gapic",
    srcs = [":datalabeling_proto_with_info"],
    grpc_service_config = "datalabeling_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "datalabeling_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":datalabeling_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-datalabeling-v1beta1-php",
    deps = [
        ":datalabeling_php_gapic",
        ":datalabeling_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "datalabeling_nodejs_gapic",
    package_name = "@google-cloud/datalabeling",
    src = ":datalabeling_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "datalabeling_grpc_service_config.json",
    main_service = "datalabeling",
    package = "google.cloud.datalabeling.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "datalabeling_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "datalabeling-v1beta1-nodejs",
    deps = [
        ":datalabeling_nodejs_gapic",
        ":datalabeling_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "datalabeling_ruby_proto",
    deps = [":datalabeling_proto"],
)

ruby_grpc_library(
    name = "datalabeling_ruby_grpc",
    srcs = [":datalabeling_proto"],
    deps = [":datalabeling_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "datalabeling_ruby_gapic",
    srcs = [":datalabeling_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-data_labeling-v1beta1",
        "ruby-cloud-env-prefix=DATA_LABELING",
        "ruby-cloud-product-url=https://cloud.google.com/ai-platform/data-labeling/docs",
        "ruby-cloud-api-id=datalabeling.googleapis.com",
        "ruby-cloud-api-shortname=datalabeling",
    ],
    grpc_service_config = "datalabeling_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "AI Platform Data Labeling Service lets you work with human labelers to generate highly accurate labels for a collection of data that you can use in machine learning models.",
    ruby_cloud_title = "AI Platform Data Labeling Service V1beta1",
    service_yaml = "datalabeling_v1beta1.yaml",
    transport = "grpc",
    deps = [
        ":datalabeling_ruby_grpc",
        ":datalabeling_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-datalabeling-v1beta1-ruby",
    deps = [
        ":datalabeling_ruby_gapic",
        ":datalabeling_ruby_grpc",
        ":datalabeling_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "datalabeling_csharp_proto",
    deps = [":datalabeling_proto"],
)

csharp_grpc_library(
    name = "datalabeling_csharp_grpc",
    srcs = [":datalabeling_proto"],
    deps = [":datalabeling_csharp_proto"],
)

csharp_gapic_library(
    name = "datalabeling_csharp_gapic",
    srcs = [":datalabeling_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "datalabeling_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datalabeling_v1beta1.yaml",
    transport = "grpc",
    deps = [
        ":datalabeling_csharp_grpc",
        ":datalabeling_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-datalabeling-v1beta1-csharp",
    deps = [
        ":datalabeling_csharp_gapic",
        ":datalabeling_csharp_grpc",
        ":datalabeling_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
