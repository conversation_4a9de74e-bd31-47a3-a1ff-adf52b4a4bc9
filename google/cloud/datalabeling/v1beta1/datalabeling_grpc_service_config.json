{"methodConfig": [{"name": [{"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "CreateDataset"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "ImportData"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "LabelImage"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "LabelVideo"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "LabelText"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "CreateAnnotationSpecSet"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "CreateInstruction"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "SearchExampleComparisons"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "CreateEvaluationJob"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "UpdateEvaluationJob"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "PauseEvaluationJob"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "ResumeEvaluationJob"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "GetDataset"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "ListDatasets"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "DeleteDataset"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "ExportData"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "GetDataItem"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "ListDataItems"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "GetAnnotatedDataset"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "ListAnnotatedDatasets"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "ListExamples"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "GetAnnotationSpecSet"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "ListAnnotationSpecSets"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "DeleteAnnotationSpecSet"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "GetInstruction"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "ListInstructions"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "DeleteInstruction"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "GetEvaluation"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "SearchEvaluations"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "GetEvaluationJob"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "DeleteEvaluationJob"}, {"service": "google.cloud.datalabeling.v1beta1.DataLabelingService", "method": "ListEvaluationJobs"}], "timeout": "30s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}