// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.datalabeling.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/datalabeling/v1beta1/annotation_spec_set.proto";
import "google/cloud/datalabeling/v1beta1/dataset.proto";
import "google/cloud/datalabeling/v1beta1/evaluation.proto";
import "google/cloud/datalabeling/v1beta1/evaluation_job.proto";
import "google/cloud/datalabeling/v1beta1/human_annotation_config.proto";
import "google/cloud/datalabeling/v1beta1/instruction.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.DataLabeling.V1Beta1";
option go_package = "cloud.google.com/go/datalabeling/apiv1beta1/datalabelingpb;datalabelingpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datalabeling.v1beta1";
option php_namespace = "Google\\Cloud\\DataLabeling\\V1beta1";
option ruby_package = "Google::Cloud::DataLabeling::V1beta1";

// Service for the AI Platform Data Labeling API.
service DataLabelingService {
  option (google.api.default_host) = "datalabeling.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Creates dataset. If success return a Dataset resource.
  rpc CreateDataset(CreateDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*}/datasets"
      body: "*"
    };
    option (google.api.method_signature) = "parent,dataset";
  }

  // Gets dataset by resource name.
  rpc GetDataset(GetDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/datasets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists datasets under a project. Pagination is supported.
  rpc ListDatasets(ListDatasetsRequest) returns (ListDatasetsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*}/datasets"
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // Deletes a dataset by resource name.
  rpc DeleteDataset(DeleteDatasetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/datasets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Imports data into dataset based on source locations defined in request.
  // It can be called multiple times for the same dataset. Each dataset can
  // only have one long running operation running on it. For example, no
  // labeling task (also long running operation) can be started while
  // importing is still ongoing. Vice versa.
  rpc ImportData(ImportDataRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/datasets/*}:importData"
      body: "*"
    };
    option (google.api.method_signature) = "name,input_config";
    option (google.longrunning.operation_info) = {
      response_type: "ImportDataOperationResponse"
      metadata_type: "ImportDataOperationMetadata"
    };
  }

  // Exports data and annotations from dataset.
  rpc ExportData(ExportDataRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/datasets/*}:exportData"
      body: "*"
    };
    option (google.api.method_signature) = "name,annotated_dataset,filter,output_config";
    option (google.longrunning.operation_info) = {
      response_type: "ExportDataOperationResponse"
      metadata_type: "ExportDataOperationMetadata"
    };
  }

  // Gets a data item in a dataset by resource name. This API can be
  // called after data are imported into dataset.
  rpc GetDataItem(GetDataItemRequest) returns (DataItem) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/datasets/*/dataItems/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists data items in a dataset. This API can be called after data
  // are imported into dataset. Pagination is supported.
  rpc ListDataItems(ListDataItemsRequest) returns (ListDataItemsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/datasets/*}/dataItems"
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // Gets an annotated dataset by resource name.
  rpc GetAnnotatedDataset(GetAnnotatedDatasetRequest) returns (AnnotatedDataset) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/datasets/*/annotatedDatasets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists annotated datasets for a dataset. Pagination is supported.
  rpc ListAnnotatedDatasets(ListAnnotatedDatasetsRequest) returns (ListAnnotatedDatasetsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/datasets/*}/annotatedDatasets"
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // Deletes an annotated dataset by resource name.
  rpc DeleteAnnotatedDataset(DeleteAnnotatedDatasetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/datasets/*/annotatedDatasets/*}"
    };
  }

  // Starts a labeling task for image. The type of image labeling task is
  // configured by feature in the request.
  rpc LabelImage(LabelImageRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/datasets/*}/image:label"
      body: "*"
    };
    option (google.api.method_signature) = "parent,basic_config,feature";
    option (google.longrunning.operation_info) = {
      response_type: "AnnotatedDataset"
      metadata_type: "LabelOperationMetadata"
    };
  }

  // Starts a labeling task for video. The type of video labeling task is
  // configured by feature in the request.
  rpc LabelVideo(LabelVideoRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/datasets/*}/video:label"
      body: "*"
    };
    option (google.api.method_signature) = "parent,basic_config,feature";
    option (google.longrunning.operation_info) = {
      response_type: "AnnotatedDataset"
      metadata_type: "LabelOperationMetadata"
    };
  }

  // Starts a labeling task for text. The type of text labeling task is
  // configured by feature in the request.
  rpc LabelText(LabelTextRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/datasets/*}/text:label"
      body: "*"
    };
    option (google.api.method_signature) = "parent,basic_config,feature";
    option (google.longrunning.operation_info) = {
      response_type: "AnnotatedDataset"
      metadata_type: "LabelOperationMetadata"
    };
  }

  // Gets an example by resource name, including both data and annotation.
  rpc GetExample(GetExampleRequest) returns (Example) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/datasets/*/annotatedDatasets/*/examples/*}"
    };
    option (google.api.method_signature) = "name,filter";
  }

  // Lists examples in an annotated dataset. Pagination is supported.
  rpc ListExamples(ListExamplesRequest) returns (ListExamplesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/datasets/*/annotatedDatasets/*}/examples"
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // Creates an annotation spec set by providing a set of labels.
  rpc CreateAnnotationSpecSet(CreateAnnotationSpecSetRequest) returns (AnnotationSpecSet) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*}/annotationSpecSets"
      body: "*"
    };
    option (google.api.method_signature) = "parent,annotation_spec_set";
  }

  // Gets an annotation spec set by resource name.
  rpc GetAnnotationSpecSet(GetAnnotationSpecSetRequest) returns (AnnotationSpecSet) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/annotationSpecSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists annotation spec sets for a project. Pagination is supported.
  rpc ListAnnotationSpecSets(ListAnnotationSpecSetsRequest) returns (ListAnnotationSpecSetsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*}/annotationSpecSets"
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // Deletes an annotation spec set by resource name.
  rpc DeleteAnnotationSpecSet(DeleteAnnotationSpecSetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/annotationSpecSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an instruction for how data should be labeled.
  rpc CreateInstruction(CreateInstructionRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*}/instructions"
      body: "*"
    };
    option (google.api.method_signature) = "parent,instruction";
    option (google.longrunning.operation_info) = {
      response_type: "Instruction"
      metadata_type: "CreateInstructionMetadata"
    };
  }

  // Gets an instruction by resource name.
  rpc GetInstruction(GetInstructionRequest) returns (Instruction) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/instructions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists instructions for a project. Pagination is supported.
  rpc ListInstructions(ListInstructionsRequest) returns (ListInstructionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*}/instructions"
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // Deletes an instruction object by resource name.
  rpc DeleteInstruction(DeleteInstructionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/instructions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets an evaluation by resource name (to search, use
  // [projects.evaluations.search][google.cloud.datalabeling.v1beta1.DataLabelingService.SearchEvaluations]).
  rpc GetEvaluation(GetEvaluationRequest) returns (Evaluation) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/datasets/*/evaluations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Searches [evaluations][google.cloud.datalabeling.v1beta1.Evaluation] within a project.
  rpc SearchEvaluations(SearchEvaluationsRequest) returns (SearchEvaluationsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*}/evaluations:search"
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // Searches example comparisons from an evaluation. The return format is a
  // list of example comparisons that show ground truth and prediction(s) for
  // a single input. Search by providing an evaluation ID.
  rpc SearchExampleComparisons(SearchExampleComparisonsRequest) returns (SearchExampleComparisonsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/datasets/*/evaluations/*}/exampleComparisons:search"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates an evaluation job.
  rpc CreateEvaluationJob(CreateEvaluationJobRequest) returns (EvaluationJob) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*}/evaluationJobs"
      body: "*"
    };
    option (google.api.method_signature) = "parent,job";
  }

  // Updates an evaluation job. You can only update certain fields of the job's
  // [EvaluationJobConfig][google.cloud.datalabeling.v1beta1.EvaluationJobConfig]: `humanAnnotationConfig.instruction`,
  // `exampleCount`, and `exampleSamplePercentage`.
  //
  // If you want to change any other aspect of the evaluation job, you must
  // delete the job and create a new one.
  rpc UpdateEvaluationJob(UpdateEvaluationJobRequest) returns (EvaluationJob) {
    option (google.api.http) = {
      patch: "/v1beta1/{evaluation_job.name=projects/*/evaluationJobs/*}"
      body: "evaluation_job"
    };
    option (google.api.method_signature) = "evaluation_job,update_mask";
  }

  // Gets an evaluation job by resource name.
  rpc GetEvaluationJob(GetEvaluationJobRequest) returns (EvaluationJob) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/evaluationJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Pauses an evaluation job. Pausing an evaluation job that is already in a
  // `PAUSED` state is a no-op.
  rpc PauseEvaluationJob(PauseEvaluationJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/evaluationJobs/*}:pause"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Resumes a paused evaluation job. A deleted evaluation job can't be resumed.
  // Resuming a running or scheduled evaluation job is a no-op.
  rpc ResumeEvaluationJob(ResumeEvaluationJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/evaluationJobs/*}:resume"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Stops and deletes an evaluation job.
  rpc DeleteEvaluationJob(DeleteEvaluationJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/evaluationJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all evaluation jobs within a project with possible filters.
  // Pagination is supported.
  rpc ListEvaluationJobs(ListEvaluationJobsRequest) returns (ListEvaluationJobsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*}/evaluationJobs"
    };
    option (google.api.method_signature) = "parent,filter";
  }
}

// Request message for CreateDataset.
message CreateDatasetRequest {
  // Required. Dataset resource parent, format:
  // projects/{project_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The dataset to be created.
  Dataset dataset = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetDataSet.
message GetDatasetRequest {
  // Required. Dataset resource name, format:
  // projects/{project_id}/datasets/{dataset_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Dataset"
    }
  ];
}

// Request message for ListDataset.
message ListDatasetsRequest {
  // Required. Dataset resource parent, format:
  // projects/{project_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Optional. Filter on dataset is not supported at this moment.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Requested page size. Server may return fewer results than
  // requested. Default value is 100.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained by
  // [ListDatasetsResponse.next_page_token][google.cloud.datalabeling.v1beta1.ListDatasetsResponse.next_page_token] of the previous
  // [DataLabelingService.ListDatasets] call.
  // Returns the first page if empty.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Results of listing datasets within a project.
message ListDatasetsResponse {
  // The list of datasets to return.
  repeated Dataset datasets = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;
}

// Request message for DeleteDataset.
message DeleteDatasetRequest {
  // Required. Dataset resource name, format:
  // projects/{project_id}/datasets/{dataset_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Dataset"
    }
  ];
}

// Request message for ImportData API.
message ImportDataRequest {
  // Required. Dataset resource name, format:
  // projects/{project_id}/datasets/{dataset_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Dataset"
    }
  ];

  // Required. Specify the input source of the data.
  InputConfig input_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Email of the user who started the import task and should be notified by
  // email. If empty no notification will be sent.
  string user_email_address = 3;
}

// Request message for ExportData API.
message ExportDataRequest {
  // Required. Dataset resource name, format:
  // projects/{project_id}/datasets/{dataset_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Dataset"
    }
  ];

  // Required. Annotated dataset resource name. DataItem in
  // Dataset and their annotations in specified annotated dataset will be
  // exported. It's in format of
  // projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/
  // {annotated_dataset_id}
  string annotated_dataset = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/AnnotatedDataset"
    }
  ];

  // Optional. Filter is not supported at this moment.
  string filter = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. Specify the output destination.
  OutputConfig output_config = 4 [(google.api.field_behavior) = REQUIRED];

  // Email of the user who started the export task and should be notified by
  // email. If empty no notification will be sent.
  string user_email_address = 5;
}

// Request message for GetDataItem.
message GetDataItemRequest {
  // Required. The name of the data item to get, format:
  // projects/{project_id}/datasets/{dataset_id}/dataItems/{data_item_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/DataItem"
    }
  ];
}

// Request message for ListDataItems.
message ListDataItemsRequest {
  // Required. Name of the dataset to list data items, format:
  // projects/{project_id}/datasets/{dataset_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Dataset"
    }
  ];

  // Optional. Filter is not supported at this moment.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Requested page size. Server may return fewer results than
  // requested. Default value is 100.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained by
  // [ListDataItemsResponse.next_page_token][google.cloud.datalabeling.v1beta1.ListDataItemsResponse.next_page_token] of the previous
  // [DataLabelingService.ListDataItems] call.
  // Return first page if empty.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Results of listing data items in a dataset.
message ListDataItemsResponse {
  // The list of data items to return.
  repeated DataItem data_items = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;
}

// Request message for GetAnnotatedDataset.
message GetAnnotatedDatasetRequest {
  // Required. Name of the annotated dataset to get, format:
  // projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/
  // {annotated_dataset_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/AnnotatedDataset"
    }
  ];
}

// Request message for ListAnnotatedDatasets.
message ListAnnotatedDatasetsRequest {
  // Required. Name of the dataset to list annotated datasets, format:
  // projects/{project_id}/datasets/{dataset_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Dataset"
    }
  ];

  // Optional. Filter is not supported at this moment.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Requested page size. Server may return fewer results than
  // requested. Default value is 100.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained by
  // [ListAnnotatedDatasetsResponse.next_page_token][google.cloud.datalabeling.v1beta1.ListAnnotatedDatasetsResponse.next_page_token] of the previous
  // [DataLabelingService.ListAnnotatedDatasets] call.
  // Return first page if empty.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Results of listing annotated datasets for a dataset.
message ListAnnotatedDatasetsResponse {
  // The list of annotated datasets to return.
  repeated AnnotatedDataset annotated_datasets = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;
}

// Request message for DeleteAnnotatedDataset.
message DeleteAnnotatedDatasetRequest {
  // Required. Name of the annotated dataset to delete, format:
  // projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/
  // {annotated_dataset_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/AnnotatedDataset"
    }
  ];
}

// Request message for starting an image labeling task.
message LabelImageRequest {
  // Image labeling task feature.
  enum Feature {
    FEATURE_UNSPECIFIED = 0;

    // Label whole image with one or more of labels.
    CLASSIFICATION = 1;

    // Label image with bounding boxes for labels.
    BOUNDING_BOX = 2;

    // Label oriented bounding box. The box does not have to be parallel to
    // horizontal line.
    ORIENTED_BOUNDING_BOX = 6;

    // Label images with bounding poly. A bounding poly is a plane figure that
    // is bounded by a finite chain of straight line segments closing in a loop.
    BOUNDING_POLY = 3;

    // Label images with polyline. Polyline is formed by connected line segments
    // which are not in closed form.
    POLYLINE = 4;

    // Label images with segmentation. Segmentation is different from bounding
    // poly since it is more fine-grained, pixel level annotation.
    SEGMENTATION = 5;
  }

  // Required. Config for labeling tasks. The type of request config must
  // match the selected feature.
  oneof request_config {
    // Configuration for image classification task.
    // One of image_classification_config, bounding_poly_config,
    // polyline_config and segmentation_config are required.
    ImageClassificationConfig image_classification_config = 4;

    // Configuration for bounding box and bounding poly task.
    // One of image_classification_config, bounding_poly_config,
    // polyline_config and segmentation_config are required.
    BoundingPolyConfig bounding_poly_config = 5;

    // Configuration for polyline task.
    // One of image_classification_config, bounding_poly_config,
    // polyline_config and segmentation_config are required.
    PolylineConfig polyline_config = 6;

    // Configuration for segmentation task.
    // One of image_classification_config, bounding_poly_config,
    // polyline_config and segmentation_config are required.
    SegmentationConfig segmentation_config = 7;
  }

  // Required. Name of the dataset to request labeling task, format:
  // projects/{project_id}/datasets/{dataset_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Dataset"
    }
  ];

  // Required. Basic human annotation config.
  HumanAnnotationConfig basic_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of image labeling task.
  Feature feature = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for LabelVideo.
message LabelVideoRequest {
  // Video labeling task feature.
  enum Feature {
    FEATURE_UNSPECIFIED = 0;

    // Label whole video or video segment with one or more labels.
    CLASSIFICATION = 1;

    // Label objects with bounding box on image frames extracted from the video.
    OBJECT_DETECTION = 2;

    // Label and track objects in video.
    OBJECT_TRACKING = 3;

    // Label the range of video for the specified events.
    EVENT = 4;
  }

  // Required. Config for labeling tasks. The type of request config must
  // match the selected feature.
  oneof request_config {
    // Configuration for video classification task.
    // One of video_classification_config, object_detection_config,
    // object_tracking_config and event_config is required.
    VideoClassificationConfig video_classification_config = 4;

    // Configuration for video object detection task.
    // One of video_classification_config, object_detection_config,
    // object_tracking_config and event_config is required.
    ObjectDetectionConfig object_detection_config = 5;

    // Configuration for video object tracking task.
    // One of video_classification_config, object_detection_config,
    // object_tracking_config and event_config is required.
    ObjectTrackingConfig object_tracking_config = 6;

    // Configuration for video event task.
    // One of video_classification_config, object_detection_config,
    // object_tracking_config and event_config is required.
    EventConfig event_config = 7;
  }

  // Required. Name of the dataset to request labeling task, format:
  // projects/{project_id}/datasets/{dataset_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Dataset"
    }
  ];

  // Required. Basic human annotation config.
  HumanAnnotationConfig basic_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of video labeling task.
  Feature feature = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for LabelText.
message LabelTextRequest {
  // Text labeling task feature.
  enum Feature {
    FEATURE_UNSPECIFIED = 0;

    // Label text content to one of more labels.
    TEXT_CLASSIFICATION = 1;

    // Label entities and their span in text.
    TEXT_ENTITY_EXTRACTION = 2;
  }

  // Required. Config for labeling tasks. The type of request config must
  // match the selected feature.
  oneof request_config {
    // Configuration for text classification task.
    // One of text_classification_config and text_entity_extraction_config
    // is required.
    TextClassificationConfig text_classification_config = 4;

    // Configuration for entity extraction task.
    // One of text_classification_config and text_entity_extraction_config
    // is required.
    TextEntityExtractionConfig text_entity_extraction_config = 5;
  }

  // Required. Name of the data set to request labeling task, format:
  // projects/{project_id}/datasets/{dataset_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Dataset"
    }
  ];

  // Required. Basic human annotation config.
  HumanAnnotationConfig basic_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of text labeling task.
  Feature feature = 6 [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetExample
message GetExampleRequest {
  // Required. Name of example, format:
  // projects/{project_id}/datasets/{dataset_id}/annotatedDatasets/
  // {annotated_dataset_id}/examples/{example_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Example"
    }
  ];

  // Optional. An expression for filtering Examples. Filter by
  // annotation_spec.display_name is supported. Format
  // "annotation_spec.display_name = {display_name}"
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for ListExamples.
message ListExamplesRequest {
  // Required. Example resource parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/AnnotatedDataset"
    }
  ];

  // Optional. An expression for filtering Examples. For annotated datasets that
  // have annotation spec set, filter by
  // annotation_spec.display_name is supported. Format
  // "annotation_spec.display_name = {display_name}"
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Requested page size. Server may return fewer results than
  // requested. Default value is 100.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained by
  // [ListExamplesResponse.next_page_token][google.cloud.datalabeling.v1beta1.ListExamplesResponse.next_page_token] of the previous
  // [DataLabelingService.ListExamples] call.
  // Return first page if empty.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Results of listing Examples in and annotated dataset.
message ListExamplesResponse {
  // The list of examples to return.
  repeated Example examples = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;
}

// Request message for CreateAnnotationSpecSet.
message CreateAnnotationSpecSetRequest {
  // Required. AnnotationSpecSet resource parent, format:
  // projects/{project_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. Annotation spec set to create. Annotation specs must be included.
  // Only one annotation spec will be accepted for annotation specs with same
  // display_name.
  AnnotationSpecSet annotation_spec_set = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetAnnotationSpecSet.
message GetAnnotationSpecSetRequest {
  // Required. AnnotationSpecSet resource name, format:
  // projects/{project_id}/annotationSpecSets/{annotation_spec_set_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/AnnotationSpecSet"
    }
  ];
}

// Request message for ListAnnotationSpecSets.
message ListAnnotationSpecSetsRequest {
  // Required. Parent of AnnotationSpecSet resource, format:
  // projects/{project_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Optional. Filter is not supported at this moment.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Requested page size. Server may return fewer results than
  // requested. Default value is 100.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained by
  // [ListAnnotationSpecSetsResponse.next_page_token][google.cloud.datalabeling.v1beta1.ListAnnotationSpecSetsResponse.next_page_token] of the previous
  // [DataLabelingService.ListAnnotationSpecSets] call.
  // Return first page if empty.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Results of listing annotation spec set under a project.
message ListAnnotationSpecSetsResponse {
  // The list of annotation spec sets.
  repeated AnnotationSpecSet annotation_spec_sets = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;
}

// Request message for DeleteAnnotationSpecSet.
message DeleteAnnotationSpecSetRequest {
  // Required. AnnotationSpec resource name, format:
  // `projects/{project_id}/annotationSpecSets/{annotation_spec_set_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/AnnotationSpecSet"
    }
  ];
}

// Request message for CreateInstruction.
message CreateInstructionRequest {
  // Required. Instruction resource parent, format:
  // projects/{project_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. Instruction of how to perform the labeling task.
  Instruction instruction = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetInstruction.
message GetInstructionRequest {
  // Required. Instruction resource name, format:
  // projects/{project_id}/instructions/{instruction_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Instruction"
    }
  ];
}

// Request message for DeleteInstruction.
message DeleteInstructionRequest {
  // Required. Instruction resource name, format:
  // projects/{project_id}/instructions/{instruction_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Instruction"
    }
  ];
}

// Request message for ListInstructions.
message ListInstructionsRequest {
  // Required. Instruction resource parent, format:
  // projects/{project_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Optional. Filter is not supported at this moment.
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Requested page size. Server may return fewer results than
  // requested. Default value is 100.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained by
  // [ListInstructionsResponse.next_page_token][google.cloud.datalabeling.v1beta1.ListInstructionsResponse.next_page_token] of the previous
  // [DataLabelingService.ListInstructions] call.
  // Return first page if empty.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Results of listing instructions under a project.
message ListInstructionsResponse {
  // The list of Instructions to return.
  repeated Instruction instructions = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;
}

// Request message for GetEvaluation.
message GetEvaluationRequest {
  // Required. Name of the evaluation. Format:
  //
  // "projects/<var>{project_id}</var>/datasets/<var>{dataset_id}</var>/evaluations/<var>{evaluation_id}</var>'
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Evaluation"
    }
  ];
}

// Request message for SearchEvaluation.
message SearchEvaluationsRequest {
  // Required. Evaluation search parent (project ID). Format:
  // "projects/<var>{project_id}</var>"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Evaluation"
    }
  ];

  // Optional. To search evaluations, you can filter by the following:
  //
  // * evaluation<span>_</span>job.evaluation_job_id (the last part of
  //   [EvaluationJob.name][google.cloud.datalabeling.v1beta1.EvaluationJob.name])
  // * evaluation<span>_</span>job.model_id (the <var>{model_name}</var> portion
  //   of [EvaluationJob.modelVersion][google.cloud.datalabeling.v1beta1.EvaluationJob.model_version])
  // * evaluation<span>_</span>job.evaluation_job_run_time_start (Minimum
  //   threshold for the
  //   [evaluationJobRunTime][google.cloud.datalabeling.v1beta1.Evaluation.evaluation_job_run_time] that created
  //   the evaluation)
  // * evaluation<span>_</span>job.evaluation_job_run_time_end (Maximum
  //   threshold for the
  //   [evaluationJobRunTime][google.cloud.datalabeling.v1beta1.Evaluation.evaluation_job_run_time] that created
  //   the evaluation)
  // * evaluation<span>_</span>job.job_state ([EvaluationJob.state][google.cloud.datalabeling.v1beta1.EvaluationJob.state])
  // * annotation<span>_</span>spec.display_name (the Evaluation contains a
  //   metric for the annotation spec with this
  //   [displayName][google.cloud.datalabeling.v1beta1.AnnotationSpec.display_name])
  //
  // To filter by multiple critiera, use the `AND` operator or the `OR`
  // operator. The following examples shows a string that filters by several
  // critiera:
  //
  // "evaluation<span>_</span>job.evaluation_job_id =
  // <var>{evaluation_job_id}</var> AND evaluation<span>_</span>job.model_id =
  // <var>{model_name}</var> AND
  // evaluation<span>_</span>job.evaluation_job_run_time_start =
  // <var>{timestamp_1}</var> AND
  // evaluation<span>_</span>job.evaluation_job_run_time_end =
  // <var>{timestamp_2}</var> AND annotation<span>_</span>spec.display_name =
  // <var>{display_name}</var>"
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Requested page size. Server may return fewer results than
  // requested. Default value is 100.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained by the
  // [nextPageToken][google.cloud.datalabeling.v1beta1.SearchEvaluationsResponse.next_page_token] of the response
  // to a previous search request.
  //
  // If you don't specify this field, the API call requests the first page of
  // the search.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Results of searching evaluations.
message SearchEvaluationsResponse {
  // The list of evaluations matching the search.
  repeated Evaluation evaluations = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;
}

// Request message of SearchExampleComparisons.
message SearchExampleComparisonsRequest {
  // Required. Name of the [Evaluation][google.cloud.datalabeling.v1beta1.Evaluation] resource to search for example
  // comparisons from. Format:
  //
  // "projects/<var>{project_id}</var>/datasets/<var>{dataset_id}</var>/evaluations/<var>{evaluation_id}</var>"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/Evaluation"
    }
  ];

  // Optional. Requested page size. Server may return fewer results than
  // requested. Default value is 100.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained by the
  // [nextPageToken][SearchExampleComparisons.next_page_token] of the response
  // to a previous search rquest.
  //
  // If you don't specify this field, the API call requests the first page of
  // the search.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Results of searching example comparisons.
message SearchExampleComparisonsResponse {
  // Example comparisons comparing ground truth output and predictions for a
  // specific input.
  message ExampleComparison {
    // The ground truth output for the input.
    Example ground_truth_example = 1;

    // Predictions by the model for the input.
    repeated Example model_created_examples = 2;
  }

  // A list of example comparisons matching the search criteria.
  repeated ExampleComparison example_comparisons = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;
}

// Request message for CreateEvaluationJob.
message CreateEvaluationJobRequest {
  // Required. Evaluation job resource parent. Format:
  // "projects/<var>{project_id}</var>"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The evaluation job to create.
  EvaluationJob job = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for UpdateEvaluationJob.
message UpdateEvaluationJobRequest {
  // Required. Evaluation job that is going to be updated.
  EvaluationJob evaluation_job = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Mask for which fields to update. You can only provide the
  // following fields:
  //
  // * `evaluationJobConfig.humanAnnotationConfig.instruction`
  // * `evaluationJobConfig.exampleCount`
  // * `evaluationJobConfig.exampleSamplePercentage`
  //
  // You can provide more than one of these fields by separating them with
  // commas.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for GetEvaluationJob.
message GetEvaluationJobRequest {
  // Required. Name of the evaluation job. Format:
  //
  // "projects/<var>{project_id}</var>/evaluationJobs/<var>{evaluation_job_id}</var>"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/EvaluationJob"
    }
  ];
}

// Request message for PauseEvaluationJob.
message PauseEvaluationJobRequest {
  // Required. Name of the evaluation job that is going to be paused. Format:
  //
  // "projects/<var>{project_id}</var>/evaluationJobs/<var>{evaluation_job_id}</var>"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/EvaluationJob"
    }
  ];
}

// Request message ResumeEvaluationJob.
message ResumeEvaluationJobRequest {
  // Required. Name of the evaluation job that is going to be resumed. Format:
  //
  // "projects/<var>{project_id}</var>/evaluationJobs/<var>{evaluation_job_id}</var>"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/EvaluationJob"
    }
  ];
}

// Request message DeleteEvaluationJob.
message DeleteEvaluationJobRequest {
  // Required. Name of the evaluation job that is going to be deleted. Format:
  //
  // "projects/<var>{project_id}</var>/evaluationJobs/<var>{evaluation_job_id}</var>"
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datalabeling.googleapis.com/EvaluationJob"
    }
  ];
}

// Request message for ListEvaluationJobs.
message ListEvaluationJobsRequest {
  // Required. Evaluation job resource parent. Format:
  // "projects/<var>{project_id}</var>"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Optional. You can filter the jobs to list by model_id (also known as
  // model_name, as described in
  // [EvaluationJob.modelVersion][google.cloud.datalabeling.v1beta1.EvaluationJob.model_version]) or by
  // evaluation job state (as described in [EvaluationJob.state][google.cloud.datalabeling.v1beta1.EvaluationJob.state]). To filter
  // by both criteria, use the `AND` operator or the `OR` operator. For example,
  // you can use the following string for your filter:
  // "evaluation<span>_</span>job.model_id = <var>{model_name}</var> AND
  // evaluation<span>_</span>job.state = <var>{evaluation_job_state}</var>"
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Requested page size. Server may return fewer results than
  // requested. Default value is 100.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained by the
  // [nextPageToken][google.cloud.datalabeling.v1beta1.ListEvaluationJobsResponse.next_page_token] in the response
  // to the previous request. The request returns the first page if this is
  // empty.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Results for listing evaluation jobs.
message ListEvaluationJobsResponse {
  // The list of evaluation jobs to return.
  repeated EvaluationJob evaluation_jobs = 1;

  // A token to retrieve next page of results.
  string next_page_token = 2;
}
