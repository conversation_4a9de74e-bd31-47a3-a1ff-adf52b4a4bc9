// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.datalabeling.v1beta1;

import "google/api/resource.proto";
import "google/cloud/datalabeling/v1beta1/dataset.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DataLabeling.V1Beta1";
option go_package = "cloud.google.com/go/datalabeling/apiv1beta1/datalabelingpb;datalabelingpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datalabeling.v1beta1";
option php_namespace = "Google\\Cloud\\DataLabeling\\V1beta1";
option ruby_package = "Google::Cloud::DataLabeling::V1beta1";

// Instruction of how to perform the labeling task for human operators.
// Currently only PDF instruction is supported.
message Instruction {
  option (google.api.resource) = {
    type: "datalabeling.googleapis.com/Instruction"
    pattern: "projects/{project}/instructions/{instruction}"
  };

  // Output only. Instruction resource name, format:
  // projects/{project_id}/instructions/{instruction_id}
  string name = 1;

  // Required. The display name of the instruction. Maximum of 64 characters.
  string display_name = 2;

  // Optional. User-provided description of the instruction.
  // The description can be up to 10000 characters long.
  string description = 3;

  // Output only. Creation time of instruction.
  google.protobuf.Timestamp create_time = 4;

  // Output only. Last update time of instruction.
  google.protobuf.Timestamp update_time = 5;

  // Required. The data type of this instruction.
  DataType data_type = 6;

  // Deprecated: this instruction format is not supported any more.
  // Instruction from a CSV file, such as for classification task.
  // The CSV file should have exact two columns, in the following format:
  //
  // * The first column is labeled data, such as an image reference, text.
  // * The second column is comma separated labels associated with data.
  CsvInstruction csv_instruction = 7 [deprecated = true];

  // Instruction from a PDF document. The PDF should be in a Cloud Storage
  // bucket.
  PdfInstruction pdf_instruction = 9;

  // Output only. The names of any related resources that are blocking changes
  // to the instruction.
  repeated string blocking_resources = 10;
}

// Deprecated: this instruction format is not supported any more.
// Instruction from a CSV file.
message CsvInstruction {
  // CSV file for the instruction. Only gcs path is allowed.
  string gcs_file_uri = 1;
}

// Instruction from a PDF file.
message PdfInstruction {
  // PDF file for the instruction. Only gcs path is allowed.
  string gcs_file_uri = 1;
}
