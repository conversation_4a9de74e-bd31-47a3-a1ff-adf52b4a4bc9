# This build file includes a target for the Ruby wrapper library for
# google-cloud-vm_migration.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for VMMigration.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "vmmigration_ruby_wrapper",
    srcs = ["//google/cloud/vmmigration/v1:vmmigration_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-vm_migration",
        "ruby-cloud-wrapper-of=v1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/migrate/compute-engine/",
        "ruby-cloud-api-id=vmmigration.googleapis.com",
        "ruby-cloud-api-shortname=vmmigration",
        "ruby-cloud-namespace-override=VmMigration=VMMigration",
    ],
    ruby_cloud_description = "Migrate for Compute Engine enables you to migrate (Lift and Shift) your virtual machines (VMs), with minor automatic modifications, from your source environment to Google Compute Engine.",
    ruby_cloud_title = "Migrate for Compute Engine",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-vmmigration-ruby",
    deps = [
        ":vmmigration_ruby_wrapper",
    ],
)
