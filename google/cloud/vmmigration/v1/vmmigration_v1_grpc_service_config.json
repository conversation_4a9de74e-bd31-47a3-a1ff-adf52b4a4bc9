{"methodConfig": [{"name": [{"service": "google.cloud.vmmigration.v1.VmMigration"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.vmmigration.v1.VmMigration", "method": "FetchInventory"}, {"service": "google.cloud.vmmigration.v1.VmMigration", "method": "CreateUtilizationReport"}], "timeout": "300s"}, {"name": [{"service": "google.cloud.vmmigration.v1.VmMigration", "method": "CreateSource"}], "timeout": "900s"}]}