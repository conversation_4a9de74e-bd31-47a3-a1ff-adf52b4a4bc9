// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataplex.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dataplex/v1/analyze.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/dataplex/apiv1/dataplexpb;dataplexpb";
option java_multiple_files = true;
option java_outer_classname = "ContentProto";
option java_package = "com.google.cloud.dataplex.v1";

// ContentService manages Notebook and SQL Scripts for Dataplex.
service ContentService {
  option (google.api.default_host) = "dataplex.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create a content.
  rpc CreateContent(CreateContentRequest) returns (Content) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/lakes/*}/contentitems"
      body: "content"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/lakes/*}/content"
        body: "content"
      }
    };
    option (google.api.method_signature) = "parent,content";
  }

  // Update a content. Only supports full resource update.
  rpc UpdateContent(UpdateContentRequest) returns (Content) {
    option (google.api.http) = {
      patch: "/v1/{content.name=projects/*/locations/*/lakes/*/contentitems/**}"
      body: "content"
      additional_bindings {
        patch: "/v1/{content.name=projects/*/locations/*/lakes/*/content/**}"
        body: "content"
      }
    };
    option (google.api.method_signature) = "content,update_mask";
  }

  // Delete a content.
  rpc DeleteContent(DeleteContentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/lakes/*/contentitems/**}"
      additional_bindings {
        delete: "/v1/{name=projects/*/locations/*/lakes/*/content/**}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Get a content resource.
  rpc GetContent(GetContentRequest) returns (Content) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/lakes/*/contentitems/**}"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/lakes/*/content/**}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the access control policy for a contentitem resource. A `NOT_FOUND`
  // error is returned if the resource does not exist. An empty policy is
  // returned if the resource exists but does not have a policy set on it.
  //
  // Caller must have Google IAM `dataplex.content.getIamPolicy` permission
  // on the resource.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      get: "/v1/{resource=projects/*/locations/*/lakes/*/contentitems/**}:getIamPolicy"
      additional_bindings {
        get: "/v1/{resource=projects/*/locations/*/lakes/*/content/**}:getIamPolicy"
      }
    };
    option (google.api.method_signature) = "resource";
  }

  // Sets the access control policy on the specified contentitem resource.
  // Replaces any existing policy.
  //
  // Caller must have Google IAM `dataplex.content.setIamPolicy` permission
  // on the resource.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/lakes/*/contentitems/**}:setIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1/{resource=projects/*/locations/*/lakes/*/content/**}:setIamPolicy"
        body: "*"
      }
    };
  }

  // Returns the caller's permissions on a resource.
  // If the resource does not exist, an empty set of
  // permissions is returned (a `NOT_FOUND` error is not returned).
  //
  // A caller is not required to have Google IAM permission to make this
  // request.
  //
  // Note: This operation is designed to be used for building permission-aware
  // UIs and command-line tools, not for authorization checking. This operation
  // may "fail open" without warning.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/lakes/*/contentitems/**}:testIamPermissions"
      body: "*"
      additional_bindings {
        post: "/v1/{resource=projects/*/locations/*/lakes/*/content/**}:testIamPermissions"
        body: "*"
      }
    };
  }

  // List content.
  rpc ListContent(ListContentRequest) returns (ListContentResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/lakes/*}/contentitems"
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/lakes/*}/content"
      }
    };
    option (google.api.method_signature) = "parent";
  }
}

// Create content request.
message CreateContentRequest {
  // Required. The resource name of the parent lake:
  // projects/{project_id}/locations/{location_id}/lakes/{lake_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];

  // Required. Content resource.
  Content content = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Update content request.
message UpdateContentRequest {
  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Update description.
  // Only fields specified in `update_mask` are updated.
  Content content = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Delete content request.
message DeleteContentRequest {
  // Required. The resource name of the content:
  // projects/{project_id}/locations/{location_id}/lakes/{lake_id}/content/{content_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/Content"
    }
  ];
}

// List content request. Returns the BASIC Content view.
message ListContentRequest {
  // Required. The resource name of the parent lake:
  // projects/{project_id}/locations/{location_id}/lakes/{lake_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];

  // Optional. Maximum number of content to return. The service may return fewer
  // than this value. If unspecified, at most 10 content will be returned. The
  // maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListContent` call. Provide
  // this to retrieve the subsequent page. When paginating, all other parameters
  // provided to `ListContent` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter request. Filters are case-sensitive.
  // The following formats are supported:
  //
  // labels.key1 = "value1"
  // labels:key1
  // type = "NOTEBOOK"
  // type = "SQL_SCRIPT"
  //
  // These restrictions can be coinjoined with AND, OR and NOT conjunctions.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// List content response.
message ListContentResponse {
  // Content under the given parent lake.
  repeated Content content = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Get content request.
message GetContentRequest {
  // Specifies whether the request should return the full or the partial
  // representation.
  enum ContentView {
    // Content view not specified. Defaults to BASIC.
    // The API will default to the BASIC view.
    CONTENT_VIEW_UNSPECIFIED = 0;

    // Will not return the `data_text` field.
    BASIC = 1;

    // Returns the complete proto.
    FULL = 2;
  }

  // Required. The resource name of the content:
  // projects/{project_id}/locations/{location_id}/lakes/{lake_id}/content/{content_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/Content"
    }
  ];

  // Optional. Specify content view to make a partial request.
  ContentView view = 2 [(google.api.field_behavior) = OPTIONAL];
}
