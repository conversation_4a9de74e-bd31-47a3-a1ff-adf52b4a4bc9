// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataplex.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dataplex/v1/security.proto";
import "google/cloud/dataplex/v1/service.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/dataplex/apiv1/dataplexpb;dataplexpb";
option java_multiple_files = true;
option java_outer_classname = "DataTaxonomyProto";
option java_package = "com.google.cloud.dataplex.v1";

// DataTaxonomyService enables attribute-based governance. The resources
// currently offered include DataTaxonomy and DataAttribute.
service DataTaxonomyService {
  option (google.api.default_host) = "dataplex.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create a DataTaxonomy resource.
  rpc CreateDataTaxonomy(CreateDataTaxonomyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/dataTaxonomies"
      body: "data_taxonomy"
    };
    option (google.api.method_signature) =
        "parent,data_taxonomy,data_taxonomy_id";
    option (google.longrunning.operation_info) = {
      response_type: "DataTaxonomy"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a DataTaxonomy resource.
  rpc UpdateDataTaxonomy(UpdateDataTaxonomyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{data_taxonomy.name=projects/*/locations/*/dataTaxonomies/*}"
      body: "data_taxonomy"
    };
    option (google.api.method_signature) = "data_taxonomy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "DataTaxonomy"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a DataTaxonomy resource. All attributes within the DataTaxonomy
  // must be deleted before the DataTaxonomy can be deleted.
  rpc DeleteDataTaxonomy(DeleteDataTaxonomyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/dataTaxonomies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists DataTaxonomy resources in a project and location.
  rpc ListDataTaxonomies(ListDataTaxonomiesRequest)
      returns (ListDataTaxonomiesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/dataTaxonomies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a DataTaxonomy resource.
  rpc GetDataTaxonomy(GetDataTaxonomyRequest) returns (DataTaxonomy) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/dataTaxonomies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create a DataAttributeBinding resource.
  rpc CreateDataAttributeBinding(CreateDataAttributeBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/dataAttributeBindings"
      body: "data_attribute_binding"
    };
    option (google.api.method_signature) =
        "parent,data_attribute_binding,data_attribute_binding_id";
    option (google.longrunning.operation_info) = {
      response_type: "DataAttributeBinding"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a DataAttributeBinding resource.
  rpc UpdateDataAttributeBinding(UpdateDataAttributeBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{data_attribute_binding.name=projects/*/locations/*/dataAttributeBindings/*}"
      body: "data_attribute_binding"
    };
    option (google.api.method_signature) = "data_attribute_binding,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "DataAttributeBinding"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a DataAttributeBinding resource. All attributes within the
  // DataAttributeBinding must be deleted before the DataAttributeBinding can be
  // deleted.
  rpc DeleteDataAttributeBinding(DeleteDataAttributeBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/dataAttributeBindings/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists DataAttributeBinding resources in a project and location.
  rpc ListDataAttributeBindings(ListDataAttributeBindingsRequest)
      returns (ListDataAttributeBindingsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/dataAttributeBindings"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a DataAttributeBinding resource.
  rpc GetDataAttributeBinding(GetDataAttributeBindingRequest)
      returns (DataAttributeBinding) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/dataAttributeBindings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create a DataAttribute resource.
  rpc CreateDataAttribute(CreateDataAttributeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/dataTaxonomies/*}/attributes"
      body: "data_attribute"
    };
    option (google.api.method_signature) =
        "parent,data_attribute,data_attribute_id";
    option (google.longrunning.operation_info) = {
      response_type: "DataAttribute"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a DataAttribute resource.
  rpc UpdateDataAttribute(UpdateDataAttributeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{data_attribute.name=projects/*/locations/*/dataTaxonomies/*/attributes/*}"
      body: "data_attribute"
    };
    option (google.api.method_signature) = "data_attribute,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "DataAttribute"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a Data Attribute resource.
  rpc DeleteDataAttribute(DeleteDataAttributeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/dataTaxonomies/*/attributes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Data Attribute resources in a DataTaxonomy.
  rpc ListDataAttributes(ListDataAttributesRequest)
      returns (ListDataAttributesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/dataTaxonomies/*}/attributes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a Data Attribute resource.
  rpc GetDataAttribute(GetDataAttributeRequest) returns (DataAttribute) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/dataTaxonomies/*/attributes/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// DataTaxonomy represents a set of hierarchical DataAttributes resources,
// grouped with a common theme Eg: 'SensitiveDataTaxonomy' can have attributes
// to manage PII data. It is defined at project level.
message DataTaxonomy {
  option (google.api.resource) = {
    type: "dataplex.googleapis.com/DataTaxonomy"
    pattern: "projects/{project}/locations/{location}/dataTaxonomies/{data_taxonomy_id}"
  };

  // Output only. The relative resource name of the DataTaxonomy, of the form:
  // projects/{project_number}/locations/{location_id}/dataTaxonomies/{data_taxonomy_id}.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataTaxonomy"
    }
  ];

  // Output only. System generated globally unique ID for the dataTaxonomy. This
  // ID will be different if the DataTaxonomy is deleted and re-created with the
  // same name.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the DataTaxonomy was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the DataTaxonomy was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Description of the DataTaxonomy.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User friendly display name.
  string display_name = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User-defined labels for the DataTaxonomy.
  map<string, string> labels = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The number of attributes in the DataTaxonomy.
  int32 attribute_count = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 10;

  // Output only. The number of classes in the DataTaxonomy.
  int32 class_count = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Denotes one dataAttribute in a dataTaxonomy, for example, PII.
// DataAttribute resources can be defined in a hierarchy.
// A single dataAttribute resource can contain specs of multiple types
//
// ```
// PII
//   - ResourceAccessSpec :
//                 - readers :<EMAIL>
//   - DataAccessSpec :
//                 - readers :<EMAIL>
// ```
message DataAttribute {
  option (google.api.resource) = {
    type: "dataplex.googleapis.com/DataAttribute"
    pattern: "projects/{project}/locations/{location}/dataTaxonomies/{dataTaxonomy}/attributes/{data_attribute_id}"
  };

  // Output only. The relative resource name of the dataAttribute, of the form:
  // projects/{project_number}/locations/{location_id}/dataTaxonomies/{dataTaxonomy}/attributes/{data_attribute_id}.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataAttribute"
    }
  ];

  // Output only. System generated globally unique ID for the DataAttribute.
  // This ID will be different if the DataAttribute is deleted and re-created
  // with the same name.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the DataAttribute was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the DataAttribute was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Description of the DataAttribute.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User friendly display name.
  string display_name = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User-defined labels for the DataAttribute.
  map<string, string> labels = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The ID of the parent DataAttribute resource, should belong to the
  // same data taxonomy. Circular dependency in parent chain is not valid.
  // Maximum depth of the hierarchy allowed is 4.
  // [a -> b -> c -> d -> e, depth = 4]
  string parent_id = 8 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataAttribute"
    }
  ];

  // Output only. The number of child attributes present for this attribute.
  int32 attribute_count = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 10;

  // Optional. Specified when applied to a resource (eg: Cloud Storage bucket,
  // BigQuery dataset, BigQuery table).
  ResourceAccessSpec resource_access_spec = 100
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specified when applied to data stored on the resource (eg: rows,
  // columns in BigQuery Tables).
  DataAccessSpec data_access_spec = 101
      [(google.api.field_behavior) = OPTIONAL];
}

// DataAttributeBinding represents binding of attributes to resources. Eg: Bind
// 'CustomerInfo' entity with 'PII' attribute.
message DataAttributeBinding {
  option (google.api.resource) = {
    type: "dataplex.googleapis.com/DataAttributeBinding"
    pattern: "projects/{project}/locations/{location}/dataAttributeBindings/{data_attribute_binding_id}"
  };

  // Represents a subresource of the given resource, and associated bindings
  // with it. Currently supported subresources are column and partition schema
  // fields within a table.
  message Path {
    // Required. The name identifier of the path.
    // Nested columns should be of the form: 'address.city'.
    string name = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. List of attributes to be associated with the path of the
    // resource, provided in the form:
    // projects/{project}/locations/{location}/dataTaxonomies/{dataTaxonomy}/attributes/{data_attribute_id}
    repeated string attributes = 2 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "dataplex.googleapis.com/DataAttribute"
      }
    ];
  }

  // Output only. The relative resource name of the Data Attribute Binding, of
  // the form:
  // projects/{project_number}/locations/{location}/dataAttributeBindings/{data_attribute_binding_id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataAttributeBinding"
    }
  ];

  // Output only. System generated globally unique ID for the
  // DataAttributeBinding. This ID will be different if the DataAttributeBinding
  // is deleted and re-created with the same name.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the DataAttributeBinding was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the DataAttributeBinding was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Description of the DataAttributeBinding.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User friendly display name.
  string display_name = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User-defined labels for the DataAttributeBinding.
  map<string, string> labels = 7 [(google.api.field_behavior) = OPTIONAL];

  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  // Etags must be used when calling the DeleteDataAttributeBinding and the
  // UpdateDataAttributeBinding method.
  string etag = 8;

  // The reference to the resource that is associated to attributes, or
  // the query to match resources and associate attributes.
  oneof resource_reference {
    // Optional. Immutable. The resource name of the resource that is associated
    // to attributes. Presently, only entity resource is supported in the form:
    // projects/{project}/locations/{location}/lakes/{lake}/zones/{zone}/entities/{entity_id}
    // Must belong in the same project and region as the attribute binding, and
    // there can only exist one active binding for a resource.
    string resource = 100 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.field_behavior) = IMMUTABLE
    ];
  }

  // Optional. List of attributes to be associated with the resource, provided
  // in the form:
  // projects/{project}/locations/{location}/dataTaxonomies/{dataTaxonomy}/attributes/{data_attribute_id}
  repeated string attributes = 110 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataAttribute"
    }
  ];

  // Optional. The list of paths for items within the associated resource (eg.
  // columns and partitions within a table) along with attribute bindings.
  repeated Path paths = 120 [(google.api.field_behavior) = OPTIONAL];
}

// Create DataTaxonomy request.
message CreateDataTaxonomyRequest {
  // Required. The resource name of the data taxonomy location, of the form:
  // projects/{project_number}/locations/{location_id}
  // where `location_id` refers to a GCP region.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. DataTaxonomy identifier.
  // * Must contain only lowercase letters, numbers and hyphens.
  // * Must start with a letter.
  // * Must be between 1-63 characters.
  // * Must end with a number or a letter.
  // * Must be unique within the Project.
  string data_taxonomy_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. DataTaxonomy resource.
  DataTaxonomy data_taxonomy = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Update DataTaxonomy request.
message UpdateDataTaxonomyRequest {
  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Only fields specified in `update_mask` are updated.
  DataTaxonomy data_taxonomy = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Get DataTaxonomy request.
message GetDataTaxonomyRequest {
  // Required. The resource name of the DataTaxonomy:
  // projects/{project_number}/locations/{location_id}/dataTaxonomies/{data_taxonomy_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataTaxonomy"
    }
  ];
}

// List DataTaxonomies request.
message ListDataTaxonomiesRequest {
  // Required. The resource name of the DataTaxonomy location, of the form:
  // projects/{project_number}/locations/{location_id}
  // where `location_id` refers to a GCP region.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Maximum number of DataTaxonomies to return. The service may
  // return fewer than this value. If unspecified, at most 10 DataTaxonomies
  // will be returned. The maximum value is 1000; values above 1000 will be
  // coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous ` ListDataTaxonomies` call.
  // Provide this to retrieve the subsequent page. When paginating, all other
  // parameters provided to ` ListDataTaxonomies` must match the call that
  // provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// List DataTaxonomies response.
message ListDataTaxonomiesResponse {
  // DataTaxonomies under the given parent location.
  repeated DataTaxonomy data_taxonomies = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable_locations = 3;
}

// Delete DataTaxonomy request.
message DeleteDataTaxonomyRequest {
  // Required. The resource name of the DataTaxonomy:
  // projects/{project_number}/locations/{location_id}/dataTaxonomies/{data_taxonomy_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataTaxonomy"
    }
  ];

  // Optional. If the client provided etag value does not match the current etag
  // value,the DeleteDataTaxonomy method returns an ABORTED error.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Create DataAttribute request.
message CreateDataAttributeRequest {
  // Required. The resource name of the parent data taxonomy
  // projects/{project_number}/locations/{location_id}/dataTaxonomies/{data_taxonomy_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataTaxonomy"
    }
  ];

  // Required. DataAttribute identifier.
  // * Must contain only lowercase letters, numbers and hyphens.
  // * Must start with a letter.
  // * Must be between 1-63 characters.
  // * Must end with a number or a letter.
  // * Must be unique within the DataTaxonomy.
  string data_attribute_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. DataAttribute resource.
  DataAttribute data_attribute = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Update DataAttribute request.
message UpdateDataAttributeRequest {
  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Only fields specified in `update_mask` are updated.
  DataAttribute data_attribute = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Get DataAttribute request.
message GetDataAttributeRequest {
  // Required. The resource name of the dataAttribute:
  // projects/{project_number}/locations/{location_id}/dataTaxonomies/{dataTaxonomy}/attributes/{data_attribute_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataAttribute"
    }
  ];
}

// List DataAttributes request.
message ListDataAttributesRequest {
  // Required. The resource name of the DataTaxonomy:
  // projects/{project_number}/locations/{location_id}/dataTaxonomies/{data_taxonomy_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataTaxonomy"
    }
  ];

  // Optional. Maximum number of DataAttributes to return. The service may
  // return fewer than this value. If unspecified, at most 10 dataAttributes
  // will be returned. The maximum value is 1000; values above 1000 will be
  // coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListDataAttributes` call.
  // Provide this to retrieve the subsequent page. When paginating, all other
  // parameters provided to `ListDataAttributes` must match the call that
  // provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// List DataAttributes response.
message ListDataAttributesResponse {
  // DataAttributes under the given parent DataTaxonomy.
  repeated DataAttribute data_attributes = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable_locations = 3;
}

// Delete DataAttribute request.
message DeleteDataAttributeRequest {
  // Required. The resource name of the DataAttribute:
  // projects/{project_number}/locations/{location_id}/dataTaxonomies/{dataTaxonomy}/attributes/{data_attribute_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataAttribute"
    }
  ];

  // Optional. If the client provided etag value does not match the current etag
  // value, the DeleteDataAttribute method returns an ABORTED error response.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Create DataAttributeBinding request.
message CreateDataAttributeBindingRequest {
  // Required. The resource name of the parent data taxonomy
  // projects/{project_number}/locations/{location_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. DataAttributeBinding identifier.
  // * Must contain only lowercase letters, numbers and hyphens.
  // * Must start with a letter.
  // * Must be between 1-63 characters.
  // * Must end with a number or a letter.
  // * Must be unique within the Location.
  string data_attribute_binding_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. DataAttributeBinding resource.
  DataAttributeBinding data_attribute_binding = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Update DataAttributeBinding request.
message UpdateDataAttributeBindingRequest {
  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Only fields specified in `update_mask` are updated.
  DataAttributeBinding data_attribute_binding = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Get DataAttributeBinding request.
message GetDataAttributeBindingRequest {
  // Required. The resource name of the DataAttributeBinding:
  // projects/{project_number}/locations/{location_id}/dataAttributeBindings/{data_attribute_binding_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataAttributeBinding"
    }
  ];
}

// List DataAttributeBindings request.
message ListDataAttributeBindingsRequest {
  // Required. The resource name of the Location:
  // projects/{project_number}/locations/{location_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Maximum number of DataAttributeBindings to return. The service
  // may return fewer than this value. If unspecified, at most 10
  // DataAttributeBindings will be returned. The maximum value is 1000; values
  // above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListDataAttributeBindings`
  // call. Provide this to retrieve the subsequent page. When paginating, all
  // other parameters provided to `ListDataAttributeBindings` must match the
  // call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter request.
  // Filter using resource: filter=resource:"resource-name"
  // Filter using attribute: filter=attributes:"attribute-name"
  // Filter using attribute in paths list:
  // filter=paths.attributes:"attribute-name"
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// List DataAttributeBindings response.
message ListDataAttributeBindingsResponse {
  // DataAttributeBindings under the given parent Location.
  repeated DataAttributeBinding data_attribute_bindings = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable_locations = 3;
}

// Delete DataAttributeBinding request.
message DeleteDataAttributeBindingRequest {
  // Required. The resource name of the DataAttributeBinding:
  // projects/{project_number}/locations/{location_id}/dataAttributeBindings/{data_attribute_binding_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/DataAttributeBinding"
    }
  ];

  // Required. If the client provided etag value does not match the current etag
  // value, the DeleteDataAttributeBindingRequest method returns an ABORTED
  // error response. Etags must be used when calling the
  // DeleteDataAttributeBinding.
  string etag = 2 [(google.api.field_behavior) = REQUIRED];
}
