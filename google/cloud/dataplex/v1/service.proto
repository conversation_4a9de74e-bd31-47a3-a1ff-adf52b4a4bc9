// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataplex.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dataplex/v1/analyze.proto";
import "google/cloud/dataplex/v1/resources.proto";
import "google/cloud/dataplex/v1/tasks.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/dataplex/apiv1/dataplexpb;dataplexpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.dataplex.v1";

// Dataplex service provides data lakes as a service. The primary resources
// offered by this service are Lakes, Zones and Assets which collectively allow
// a data administrator to organize, manage, secure and catalog data across
// their organization located across cloud projects in a variety of storage
// systems including Cloud Storage and BigQuery.
service DataplexService {
  option (google.api.default_host) = "dataplex.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a lake resource.
  rpc CreateLake(CreateLakeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/lakes"
      body: "lake"
    };
    option (google.api.method_signature) = "parent,lake,lake_id";
    option (google.longrunning.operation_info) = {
      response_type: "Lake"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a lake resource.
  rpc UpdateLake(UpdateLakeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{lake.name=projects/*/locations/*/lakes/*}"
      body: "lake"
    };
    option (google.api.method_signature) = "lake,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Lake"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a lake resource. All zones within the lake must be deleted before
  // the lake can be deleted.
  rpc DeleteLake(DeleteLakeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/lakes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists lake resources in a project and location.
  rpc ListLakes(ListLakesRequest) returns (ListLakesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/lakes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a lake resource.
  rpc GetLake(GetLakeRequest) returns (Lake) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/lakes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists action resources in a lake.
  rpc ListLakeActions(ListLakeActionsRequest) returns (ListActionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/lakes/*}/actions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a zone resource within a lake.
  rpc CreateZone(CreateZoneRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/lakes/*}/zones"
      body: "zone"
    };
    option (google.api.method_signature) = "parent,zone,zone_id";
    option (google.longrunning.operation_info) = {
      response_type: "Zone"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a zone resource.
  rpc UpdateZone(UpdateZoneRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{zone.name=projects/*/locations/*/lakes/*/zones/*}"
      body: "zone"
    };
    option (google.api.method_signature) = "zone,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Zone"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a zone resource. All assets within a zone must be deleted before
  // the zone can be deleted.
  rpc DeleteZone(DeleteZoneRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/lakes/*/zones/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists zone resources in a lake.
  rpc ListZones(ListZonesRequest) returns (ListZonesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/lakes/*}/zones"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a zone resource.
  rpc GetZone(GetZoneRequest) returns (Zone) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/lakes/*/zones/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists action resources in a zone.
  rpc ListZoneActions(ListZoneActionsRequest) returns (ListActionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/lakes/*/zones/*}/actions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates an asset resource.
  rpc CreateAsset(CreateAssetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/lakes/*/zones/*}/assets"
      body: "asset"
    };
    option (google.api.method_signature) = "parent,asset,asset_id";
    option (google.longrunning.operation_info) = {
      response_type: "Asset"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an asset resource.
  rpc UpdateAsset(UpdateAssetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{asset.name=projects/*/locations/*/lakes/*/zones/*/assets/*}"
      body: "asset"
    };
    option (google.api.method_signature) = "asset,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Asset"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes an asset resource. The referenced storage resource is detached
  // (default) or deleted based on the associated Lifecycle policy.
  rpc DeleteAsset(DeleteAssetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/lakes/*/zones/*/assets/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists asset resources in a zone.
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/lakes/*/zones/*}/assets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves an asset resource.
  rpc GetAsset(GetAssetRequest) returns (Asset) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/lakes/*/zones/*/assets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists action resources in an asset.
  rpc ListAssetActions(ListAssetActionsRequest) returns (ListActionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/lakes/*/zones/*/assets/*}/actions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a task resource within a lake.
  rpc CreateTask(CreateTaskRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/lakes/*}/tasks"
      body: "task"
    };
    option (google.api.method_signature) = "parent,task,task_id";
    option (google.longrunning.operation_info) = {
      response_type: "Task"
      metadata_type: "OperationMetadata"
    };
  }

  // Update the task resource.
  rpc UpdateTask(UpdateTaskRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{task.name=projects/*/locations/*/lakes/*/tasks/*}"
      body: "task"
    };
    option (google.api.method_signature) = "task,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Task"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete the task resource.
  rpc DeleteTask(DeleteTaskRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/lakes/*/tasks/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists tasks under the given lake.
  rpc ListTasks(ListTasksRequest) returns (ListTasksResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/lakes/*}/tasks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get task resource.
  rpc GetTask(GetTaskRequest) returns (Task) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/lakes/*/tasks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Jobs under the given task.
  rpc ListJobs(ListJobsRequest) returns (ListJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/lakes/*/tasks/*}/jobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Run an on demand execution of a Task.
  rpc RunTask(RunTaskRequest) returns (RunTaskResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/lakes/*/tasks/*}:run"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Get job resource.
  rpc GetJob(GetJobRequest) returns (Job) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/lakes/*/tasks/*/jobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Cancel jobs running for the task resource.
  rpc CancelJob(CancelJobRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/lakes/*/tasks/*/jobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Create an environment resource.
  rpc CreateEnvironment(CreateEnvironmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/lakes/*}/environments"
      body: "environment"
    };
    option (google.api.method_signature) = "parent,environment,environment_id";
    option (google.longrunning.operation_info) = {
      response_type: "Environment"
      metadata_type: "OperationMetadata"
    };
  }

  // Update the environment resource.
  rpc UpdateEnvironment(UpdateEnvironmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{environment.name=projects/*/locations/*/lakes/*/environments/*}"
      body: "environment"
    };
    option (google.api.method_signature) = "environment,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Environment"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete the environment resource. All the child resources must have been
  // deleted before environment deletion can be initiated.
  rpc DeleteEnvironment(DeleteEnvironmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/lakes/*/environments/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists environments under the given lake.
  rpc ListEnvironments(ListEnvironmentsRequest)
      returns (ListEnvironmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/lakes/*}/environments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get environment resource.
  rpc GetEnvironment(GetEnvironmentRequest) returns (Environment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/lakes/*/environments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists session resources in an environment.
  rpc ListSessions(ListSessionsRequest) returns (ListSessionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/lakes/*/environments/*}/sessions"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Create lake request.
message CreateLakeRequest {
  // Required. The resource name of the lake location, of the form:
  // projects/{project_number}/locations/{location_id}
  // where `location_id` refers to a GCP region.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Lake identifier.
  // This ID will be used to generate names such as database and dataset names
  // when publishing metadata to Hive Metastore and BigQuery.
  // * Must contain only lowercase letters, numbers and hyphens.
  // * Must start with a letter.
  // * Must end with a number or a letter.
  // * Must be between 1-63 characters.
  // * Must be unique within the customer project / location.
  string lake_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Lake resource
  Lake lake = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Update lake request.
message UpdateLakeRequest {
  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Update description.
  // Only fields specified in `update_mask` are updated.
  Lake lake = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Delete lake request.
message DeleteLakeRequest {
  // Required. The resource name of the lake:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];
}

// List lakes request.
message ListLakesRequest {
  // Required. The resource name of the lake location, of the form:
  // `projects/{project_number}/locations/{location_id}`
  // where `location_id` refers to a GCP region.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Maximum number of Lakes to return. The service may return fewer
  // than this value. If unspecified, at most 10 lakes will be returned. The
  // maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListLakes` call. Provide
  // this to retrieve the subsequent page. When paginating, all other parameters
  // provided to `ListLakes` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// List lakes response.
message ListLakesResponse {
  // Lakes under the given parent location.
  repeated Lake lakes = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable_locations = 3;
}

// List lake actions request.
message ListLakeActionsRequest {
  // Required. The resource name of the parent lake:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];

  // Optional. Maximum number of actions to return. The service may return fewer
  // than this value. If unspecified, at most 10 actions will be returned. The
  // maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListLakeActions` call.
  // Provide this to retrieve the subsequent page. When paginating, all other
  // parameters provided to `ListLakeActions` must match the call that provided
  // the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// List actions response.
message ListActionsResponse {
  // Actions under the given parent lake/zone/asset.
  repeated Action actions = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Get lake request.
message GetLakeRequest {
  // Required. The resource name of the lake:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];
}

// Create zone request.
message CreateZoneRequest {
  // Required. The resource name of the parent lake:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];

  // Required. Zone identifier.
  // This ID will be used to generate names such as database and dataset names
  // when publishing metadata to Hive Metastore and BigQuery.
  // * Must contain only lowercase letters, numbers and hyphens.
  // * Must start with a letter.
  // * Must end with a number or a letter.
  // * Must be between 1-63 characters.
  // * Must be unique across all lakes from all locations in a project.
  // * Must not be one of the reserved IDs (i.e. "default", "global-temp")
  string zone_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Zone resource.
  Zone zone = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Update zone request.
message UpdateZoneRequest {
  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Update description.
  // Only fields specified in `update_mask` are updated.
  Zone zone = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Delete zone request.
message DeleteZoneRequest {
  // Required. The resource name of the zone:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Zone" }
  ];
}

// List zones request.
message ListZonesRequest {
  // Required. The resource name of the parent lake:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];

  // Optional. Maximum number of zones to return. The service may return fewer
  // than this value. If unspecified, at most 10 zones will be returned. The
  // maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListZones` call. Provide
  // this to retrieve the subsequent page. When paginating, all other parameters
  // provided to `ListZones` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// List zones response.
message ListZonesResponse {
  // Zones under the given parent lake.
  repeated Zone zones = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// List zone actions request.
message ListZoneActionsRequest {
  // Required. The resource name of the parent zone:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Zone" }
  ];

  // Optional. Maximum number of actions to return. The service may return fewer
  // than this value. If unspecified, at most 10 actions will be returned. The
  // maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListZoneActions` call.
  // Provide this to retrieve the subsequent page. When paginating, all other
  // parameters provided to `ListZoneActions` must match the call that provided
  // the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Get zone request.
message GetZoneRequest {
  // Required. The resource name of the zone:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Zone" }
  ];
}

// Create asset request.
message CreateAssetRequest {
  // Required. The resource name of the parent zone:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Zone" }
  ];

  // Required. Asset identifier.
  // This ID will be used to generate names such as table names when publishing
  // metadata to Hive Metastore and BigQuery.
  // * Must contain only lowercase letters, numbers and hyphens.
  // * Must start with a letter.
  // * Must end with a number or a letter.
  // * Must be between 1-63 characters.
  // * Must be unique within the zone.
  string asset_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Asset resource.
  Asset asset = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Update asset request.
message UpdateAssetRequest {
  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Update description.
  // Only fields specified in `update_mask` are updated.
  Asset asset = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Delete asset request.
message DeleteAssetRequest {
  // Required. The resource name of the asset:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/assets/{asset_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Asset" }
  ];
}

// List assets request.
message ListAssetsRequest {
  // Required. The resource name of the parent zone:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Zone" }
  ];

  // Optional. Maximum number of asset to return. The service may return fewer
  // than this value. If unspecified, at most 10 assets will be returned. The
  // maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListAssets` call. Provide
  // this to retrieve the subsequent page. When paginating, all other parameters
  // provided to `ListAssets` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// List assets response.
message ListAssetsResponse {
  // Asset under the given parent zone.
  repeated Asset assets = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// List asset actions request.
message ListAssetActionsRequest {
  // Required. The resource name of the parent asset:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/assets/{asset_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Asset" }
  ];

  // Optional. Maximum number of actions to return. The service may return fewer
  // than this value. If unspecified, at most 10 actions will be returned. The
  // maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListAssetActions` call.
  // Provide this to retrieve the subsequent page. When paginating, all other
  // parameters provided to `ListAssetActions` must match the call that provided
  // the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Get asset request.
message GetAssetRequest {
  // Required. The resource name of the asset:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/zones/{zone_id}/assets/{asset_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Asset" }
  ];
}

// Represents the metadata of a long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Create task request.
message CreateTaskRequest {
  // Required. The resource name of the parent lake:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];

  // Required. Task identifier.
  string task_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Task resource.
  Task task = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Update task request.
message UpdateTaskRequest {
  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Update description.
  // Only fields specified in `update_mask` are updated.
  Task task = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Delete task request.
message DeleteTaskRequest {
  // Required. The resource name of the task:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/task/{task_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Task" }
  ];
}

// List tasks request.
message ListTasksRequest {
  // Required. The resource name of the parent lake:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];

  // Optional. Maximum number of tasks to return. The service may return fewer
  // than this value. If unspecified, at most 10 tasks will be returned. The
  // maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListZones` call. Provide
  // this to retrieve the subsequent page. When paginating, all other parameters
  // provided to `ListZones` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// List tasks response.
message ListTasksResponse {
  // Tasks under the given parent lake.
  repeated Task tasks = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable_locations = 3;
}

// Get task request.
message GetTaskRequest {
  // Required. The resource name of the task:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/tasks/{tasks_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Task" }
  ];
}

// Get job request.
message GetJobRequest {
  // Required. The resource name of the job:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/tasks/{task_id}/jobs/{job_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Job" }
  ];
}

message RunTaskRequest {
  // Required. The resource name of the task:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/tasks/{task_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Task" }
  ];

  // Optional. User-defined labels for the task. If the map is left empty, the
  // task will run with existing labels from task definition. If the map
  // contains an entry with a new key, the same will be added to existing set of
  // labels. If the map contains an entry with an existing label key in task
  // definition, the task will run with new label value for that entry. Clearing
  // an existing label will require label value to be explicitly set to a hyphen
  // "-". The label value cannot be empty.
  map<string, string> labels = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Execution spec arguments. If the map is left empty, the task will
  // run with existing execution spec args from task definition. If the map
  // contains an entry with a new key, the same will be added to existing set of
  // args. If the map contains an entry with an existing arg key in task
  // definition, the task will run with new arg value for that entry. Clearing
  // an existing arg will require arg value to be explicitly set to a hyphen
  // "-". The arg value cannot be empty.
  map<string, string> args = 4 [(google.api.field_behavior) = OPTIONAL];
}

message RunTaskResponse {
  // Jobs created by RunTask API.
  Job job = 1;
}

// List jobs request.
message ListJobsRequest {
  // Required. The resource name of the parent environment:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/tasks/{task_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Task" }
  ];

  // Optional. Maximum number of jobs to return. The service may return fewer
  // than this value. If unspecified, at most 10 jobs will be returned. The
  // maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListJobs` call. Provide this
  // to retrieve the subsequent page. When paginating, all other parameters
  // provided to `ListJobs` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// List jobs response.
message ListJobsResponse {
  // Jobs under a given task.
  repeated Job jobs = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Cancel task jobs.
message CancelJobRequest {
  // Required. The resource name of the job:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/task/{task_id}/job/{job_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Job" }
  ];
}

// Create environment request.
message CreateEnvironmentRequest {
  // Required. The resource name of the parent lake:
  // `projects/{project_id}/locations/{location_id}/lakes/{lake_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];

  // Required. Environment identifier.
  // * Must contain only lowercase letters, numbers and hyphens.
  // * Must start with a letter.
  // * Must be between 1-63 characters.
  // * Must end with a number or a letter.
  // * Must be unique within the lake.
  string environment_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Environment resource.
  Environment environment = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Update environment request.
message UpdateEnvironmentRequest {
  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Update description.
  // Only fields specified in `update_mask` are updated.
  Environment environment = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is false.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Delete environment request.
message DeleteEnvironmentRequest {
  // Required. The resource name of the environment:
  // `projects/{project_id}/locations/{location_id}/lakes/{lake_id}/environments/{environment_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/Environment"
    }
  ];
}

// List environments request.
message ListEnvironmentsRequest {
  // Required. The resource name of the parent lake:
  // `projects/{project_id}/locations/{location_id}/lakes/{lake_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];

  // Optional. Maximum number of environments to return. The service may return
  // fewer than this value. If unspecified, at most 10 environments will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListEnvironments` call.
  // Provide this to retrieve the subsequent page. When paginating, all other
  // parameters provided to `ListEnvironments` must match the call that provided
  // the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// List environments response.
message ListEnvironmentsResponse {
  // Environments under the given parent lake.
  repeated Environment environments = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Get environment request.
message GetEnvironmentRequest {
  // Required. The resource name of the environment:
  // `projects/{project_id}/locations/{location_id}/lakes/{lake_id}/environments/{environment_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/Environment"
    }
  ];
}

// List sessions request.
message ListSessionsRequest {
  // Required. The resource name of the parent environment:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}/environment/{environment_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/Environment"
    }
  ];

  // Optional. Maximum number of sessions to return. The service may return
  // fewer than this value. If unspecified, at most 10 sessions will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListSessions` call. Provide
  // this to retrieve the subsequent page. When paginating, all other parameters
  // provided to `ListSessions` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter request. The following `mode` filter is supported to
  // return only the sessions belonging to the requester when the mode is USER
  // and return sessions of all the users when the mode is ADMIN. When no filter
  // is sent default to USER mode. NOTE: When the mode is ADMIN, the requester
  // should have `dataplex.environments.listAllSessions` permission to list all
  // sessions, in absence of the permission, the request fails.
  //
  // mode = ADMIN | USER
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// List sessions response.
message ListSessionsResponse {
  // Sessions under a given environment.
  repeated Session sessions = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}
