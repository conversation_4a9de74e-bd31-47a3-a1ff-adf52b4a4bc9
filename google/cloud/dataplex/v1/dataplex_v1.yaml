type: google.api.Service
config_version: 3
name: dataplex.googleapis.com
title: Cloud Dataplex API

apis:
- name: google.cloud.dataplex.v1.CatalogService
- name: google.cloud.dataplex.v1.ContentService
- name: google.cloud.dataplex.v1.DataScanService
- name: google.cloud.dataplex.v1.DataTaxonomyService
- name: google.cloud.dataplex.v1.DataplexService
- name: google.cloud.dataplex.v1.MetadataService
- name: google.cloud.location.Locations
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.dataplex.v1.DataQualityScanRuleResult
- name: google.cloud.dataplex.v1.DataScanEvent
- name: google.cloud.dataplex.v1.DiscoveryEvent
- name: google.cloud.dataplex.v1.GovernanceEvent
- name: google.cloud.dataplex.v1.ImportItem
- name: google.cloud.dataplex.v1.JobEvent
- name: google.cloud.dataplex.v1.OperationMetadata
- name: google.cloud.dataplex.v1.SessionEvent

documentation:
  summary: Dataplex API is used to manage the lifecycle of data lakes.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1/{resource=projects/*/locations/*/lakes/*}:getIamPolicy'
    additional_bindings:
    - get: '/v1/{resource=projects/*/locations/*/lakes/*/zones/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/lakes/*/zones/*/assets/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/lakes/*/tasks/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/lakes/*/environments/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/dataScans/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/dataTaxonomies/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/dataTaxonomies/*/attributes/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/dataAttributeBindings/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/entryTypes/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/entryLinkTypes/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/aspectTypes/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/entryGroups/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/governanceRules/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/glossaries/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/glossaries/*/categories/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/glossaries/*/terms/*}:getIamPolicy'
    - get: '/v1/{resource=organizations/*/locations/*/encryptionConfigs/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1/{resource=projects/*/locations/*/lakes/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/lakes/*/zones/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/lakes/*/zones/*/assets/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/lakes/*/tasks/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/lakes/*/environments/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/dataScans/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/dataTaxonomies/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/dataTaxonomies/*/attributes/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/dataAttributeBindings/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/entryTypes/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/entryLinkTypes/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/aspectTypes/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/entryGroups/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/governanceRules/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/glossaries/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/glossaries/*/categories/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/glossaries/*/terms/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=organizations/*/locations/*/encryptionConfigs/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1/{resource=projects/*/locations/*/lakes/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/lakes/*/zones/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/lakes/*/zones/*/assets/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/lakes/*/tasks/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/lakes/*/environments/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/dataScans/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/dataTaxonomies/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/dataTaxonomies/*/attributes/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/dataAttributeBindings/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/entryTypes/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/entryLinkTypes/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/aspectTypes/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/entryGroups/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/governanceRules/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/glossaries/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/glossaries/*/categories/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/glossaries/*/terms/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=organizations/*/locations/*/encryptionConfigs/*}:testIamPermissions'
      body: '*'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
    additional_bindings:
    - post: '/v1/{name=organizations/*/locations/*/operations/*}:cancel'
      body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - delete: '/v1/{name=organizations/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/v1/{name=organizations/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'
    additional_bindings:
    - get: '/v1/{name=organizations/*/locations/*/operations/*}'

authentication:
  rules:
  - selector: 'google.cloud.dataplex.v1.CatalogService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataplex.v1.ContentService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataplex.v1.DataScanService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataplex.v1.DataTaxonomyService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataplex.v1.DataplexService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataplex.v1.MetadataService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1155079&template=1656695
  documentation_uri: https://cloud.google.com/dataplex/docs/overview
  api_short_name: dataplex
  github_label: 'api: dataplex'
  doc_tag_prefix: dataplex
  organization: CLOUD
  library_settings:
  - version: google.cloud.dataplex.v1
    launch_stage: ALPHA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/dataplex/docs/reference/rpc
