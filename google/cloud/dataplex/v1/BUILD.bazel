# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "dataplex_proto",
    srcs = [
        "analyze.proto",
        "catalog.proto",
        "content.proto",
        "data_discovery.proto",
        "data_profile.proto",
        "data_quality.proto",
        "data_taxonomy.proto",
        "datascans.proto",
        "logs.proto",
        "metadata.proto",
        "processing.proto",
        "resources.proto",
        "security.proto",
        "service.proto",
        "tasks.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "dataplex_proto_with_info",
    deps = [
        ":dataplex_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "dataplex_java_proto",
    deps = [":dataplex_proto"],
)

java_grpc_library(
    name = "dataplex_java_grpc",
    srcs = [":dataplex_proto"],
    deps = [":dataplex_java_proto"],
)

java_gapic_library(
    name = "dataplex_java_gapic",
    srcs = [":dataplex_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "dataplex_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataplex_v1.yaml",
    test_deps = [
        ":dataplex_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":dataplex_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "dataplex_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.dataplex.v1.CatalogServiceClientHttpJsonTest",
        "com.google.cloud.dataplex.v1.CatalogServiceClientTest",
        "com.google.cloud.dataplex.v1.ContentServiceClientHttpJsonTest",
        "com.google.cloud.dataplex.v1.ContentServiceClientTest",
        "com.google.cloud.dataplex.v1.DataScanServiceClientHttpJsonTest",
        "com.google.cloud.dataplex.v1.DataScanServiceClientTest",
        "com.google.cloud.dataplex.v1.DataTaxonomyServiceClientHttpJsonTest",
        "com.google.cloud.dataplex.v1.DataTaxonomyServiceClientTest",
        "com.google.cloud.dataplex.v1.DataplexServiceClientHttpJsonTest",
        "com.google.cloud.dataplex.v1.DataplexServiceClientTest",
        "com.google.cloud.dataplex.v1.MetadataServiceClientHttpJsonTest",
        "com.google.cloud.dataplex.v1.MetadataServiceClientTest",
    ],
    runtime_deps = [":dataplex_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-dataplex-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":dataplex_java_gapic",
        ":dataplex_java_grpc",
        ":dataplex_java_proto",
        ":dataplex_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "dataplex_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/dataplex/apiv1/dataplexpb",
    protos = [":dataplex_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "dataplex_go_gapic",
    srcs = [":dataplex_proto_with_info"],
    grpc_service_config = "dataplex_grpc_service_config.json",
    importpath = "cloud.google.com/go/dataplex/apiv1;dataplex",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "dataplex_v1.yaml",
    transport = "grpc",
    deps = [
        ":dataplex_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-dataplex-v1-go",
    deps = [
        ":dataplex_go_gapic",
        ":dataplex_go_gapic_srcjar-metadata.srcjar",
        ":dataplex_go_gapic_srcjar-snippets.srcjar",
        ":dataplex_go_gapic_srcjar-test.srcjar",
        ":dataplex_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "dataplex_py_gapic",
    srcs = [":dataplex_proto"],
    grpc_service_config = "dataplex_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataplex_v1.yaml",
    transport = "grpc",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "dataplex_py_gapic_test",
    srcs = [
        "dataplex_py_gapic_pytest.py",
        "dataplex_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":dataplex_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "dataplex-v1-py",
    deps = [
        ":dataplex_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "dataplex_php_proto",
    deps = [":dataplex_proto"],
)

php_gapic_library(
    name = "dataplex_php_gapic",
    srcs = [":dataplex_proto_with_info"],
    grpc_service_config = "dataplex_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "dataplex_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataplex_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-dataplex-v1-php",
    deps = [
        ":dataplex_php_gapic",
        ":dataplex_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "dataplex_nodejs_gapic",
    package_name = "@google-cloud/dataplex",
    src = ":dataplex_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "dataplex_grpc_service_config.json",
    mixins = "google.cloud.location.Locations;google.longrunning.Operations",
    package = "google.cloud.dataplex.v1",
    rest_numeric_enums = True,
    service_yaml = "dataplex_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "dataplex-v1-nodejs",
    deps = [
        ":dataplex_nodejs_gapic",
        ":dataplex_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "dataplex_ruby_proto",
    deps = [":dataplex_proto"],
)

ruby_grpc_library(
    name = "dataplex_ruby_grpc",
    srcs = [":dataplex_proto"],
    deps = [":dataplex_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "dataplex_ruby_gapic",
    srcs = [":dataplex_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=dataplex.googleapis.com",
        "ruby-cloud-api-shortname=dataplex",
        "ruby-cloud-gem-name=google-cloud-dataplex-v1",
        "ruby-cloud-product-url=https://cloud.google.com/dataplex/",
    ],
    grpc_service_config = "dataplex_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Dataplex is an intelligent data fabric that provides a way to centrally manage, monitor, and govern your data across data lakes, data warehouses and data marts, and make this data securely accessible to a variety of analytics and data science tools.",
    ruby_cloud_title = "Dataplex V1",
    service_yaml = "dataplex_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataplex_ruby_grpc",
        ":dataplex_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataplex-v1-ruby",
    deps = [
        ":dataplex_ruby_gapic",
        ":dataplex_ruby_grpc",
        ":dataplex_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "dataplex_csharp_proto",
    deps = [":dataplex_proto"],
)

csharp_grpc_library(
    name = "dataplex_csharp_grpc",
    srcs = [":dataplex_proto"],
    deps = [":dataplex_csharp_proto"],
)

csharp_gapic_library(
    name = "dataplex_csharp_gapic",
    srcs = [":dataplex_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "dataplex_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataplex_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataplex_csharp_grpc",
        ":dataplex_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-dataplex-v1-csharp",
    deps = [
        ":dataplex_csharp_gapic",
        ":dataplex_csharp_grpc",
        ":dataplex_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "dataplex_cc_proto",
    deps = [":dataplex_proto"],
)

cc_grpc_library(
    name = "dataplex_cc_grpc",
    srcs = [":dataplex_proto"],
    grpc_only = True,
    deps = [":dataplex_cc_proto"],
)
