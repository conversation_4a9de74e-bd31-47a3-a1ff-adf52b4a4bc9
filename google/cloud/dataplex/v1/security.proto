// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataplex.v1;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/dataplex/apiv1/dataplexpb;dataplexpb";
option java_multiple_files = true;
option java_outer_classname = "SecurityProto";
option java_package = "com.google.cloud.dataplex.v1";

// ResourceAccessSpec holds the access control configuration to be enforced
// on the resources, for example, Cloud Storage bucket, BigQuery dataset,
// BigQuery table.
message ResourceAccessSpec {
  // Optional. The format of strings follows the pattern followed by IAM in the
  // bindings. user:{email}, serviceAccount:{email} group:{email}.
  // The set of principals to be granted reader role on the resource.
  repeated string readers = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The set of principals to be granted writer role on the resource.
  repeated string writers = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The set of principals to be granted owner role on the resource.
  repeated string owners = 3 [(google.api.field_behavior) = OPTIONAL];
}

// DataAccessSpec holds the access control configuration to be enforced on data
// stored within resources (eg: rows, columns in BigQuery Tables). When
// associated with data, the data is only accessible to
// principals explicitly granted access through the DataAccessSpec. Principals
// with access to the containing resource are not implicitly granted access.
message DataAccessSpec {
  // Optional. The format of strings follows the pattern followed by IAM in the
  // bindings. user:{email}, serviceAccount:{email} group:{email}.
  // The set of principals to be granted reader role on data
  // stored within resources.
  repeated string readers = 1 [(google.api.field_behavior) = OPTIONAL];
}
