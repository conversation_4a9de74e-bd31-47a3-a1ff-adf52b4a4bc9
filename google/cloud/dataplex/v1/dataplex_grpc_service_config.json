{"methodConfig": [{"name": [{"service": "google.cloud.dataplex.v1.DataplexService", "method": "GetLake"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "GetZone"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "GetAsset"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "GetTask"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "Get<PERSON>ob"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "GetEnvironment"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataplex.v1.DataplexService", "method": "ListLakes"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "ListLakeActions"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "ListZones"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "ListZoneActions"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "ListAssets"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "ListAssetActions"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "ListTasks"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "ListJobs"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "ListEnvironments"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataplex.v1.MetadataService", "method": "GetEntity"}, {"service": "google.cloud.dataplex.v1.MetadataService", "method": "GetPartition"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataplex.v1.MetadataService", "method": "ListEntities"}, {"service": "google.cloud.dataplex.v1.MetadataService", "method": "ListPartitions"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataplex.v1.ContentService", "method": "Get<PERSON>ontent"}, {"service": "google.cloud.dataplex.v1.ContentService", "method": "GetIamPolicy"}, {"service": "google.cloud.dataplex.v1.ContentService", "method": "TestIamPermissions"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataplex.v1.ContentService", "method": "ListContent"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataplex.v1.DataplexService", "method": "CreateLake"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "UpdateLake"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "DeleteLake"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "CreateZone"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "UpdateZone"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "DeleteZone"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "CreateAsset"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "UpdateAsset"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "DeleteAsset"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "CreateTask"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "UpdateTask"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "DeleteTask"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "CreateEnvironment"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "UpdateEnvironment"}, {"service": "google.cloud.dataplex.v1.DataplexService", "method": "DeleteEnvironment"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.dataplex.v1.MetadataService", "method": "CreateEntity"}, {"service": "google.cloud.dataplex.v1.MetadataService", "method": "DeleteEntity"}, {"service": "google.cloud.dataplex.v1.MetadataService", "method": "UpdateEntity"}, {"service": "google.cloud.dataplex.v1.MetadataService", "method": "CreatePartition"}, {"service": "google.cloud.dataplex.v1.MetadataService", "method": "DeletePartition"}, {"service": "google.cloud.dataplex.v1.MetadataService", "method": "UpdatePartition"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.dataplex.v1.ContentService", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.dataplex.v1.ContentService", "method": "Delete<PERSON><PERSON>nt"}, {"service": "google.cloud.dataplex.v1.ContentService", "method": "Update<PERSON><PERSON>nt"}, {"service": "google.cloud.dataplex.v1.ContentService", "method": "SetIamPolicy"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.dataplex.v1.CatalogService", "method": "ListEntries"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "GetEntry"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "LookupEntry"}], "timeout": "20s", "retryPolicy": {"maxAttempts": 3, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}, {"name": [{"service": "google.cloud.dataplex.v1.CatalogService", "method": "UpdateEntry"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 3, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}, {"name": [{"service": "google.cloud.dataplex.v1.CatalogService", "method": "CreateEntry"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "DeleteEntry"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.dataplex.v1.CatalogService", "method": "GetEntryGroup"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "ListEntryGroups"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "GetAspectType"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "ListAspectTypes"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "GetEntryType"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "ListEntryTypes"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "SearchEntries"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "RESOURCE_EXHAUSTED"]}}, {"name": [{"service": "google.cloud.dataplex.v1.CatalogService", "method": "CreateEntryGroup"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "UpdateEntryGroup"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "DeleteEntryGroup"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "CreateAspectType"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "UpdateAspectType"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "DeleteAspectType"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "CreateEntryType"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "UpdateEntryType"}, {"service": "google.cloud.dataplex.v1.CatalogService", "method": "DeleteEntryType"}], "timeout": "60s"}]}