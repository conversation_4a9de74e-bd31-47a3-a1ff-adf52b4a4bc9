# This build file includes a target for the Ruby wrapper library for
# google-cloud-dataplex.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for dataplex.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "dataplex_ruby_wrapper",
    srcs = ["//google/cloud/dataplex/v1:dataplex_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-dataplex",
        "ruby-cloud-wrapper-of=v1:0.18",
        "ruby-cloud-product-url=https://cloud.google.com/dataplex/",
        "ruby-cloud-api-id=dataplex.googleapis.com",
        "ruby-cloud-api-shortname=dataplex",
    ],
    ruby_cloud_description = "Dataplex is an intelligent data fabric that provides a way to centrally manage, monitor, and govern your data across data lakes, data warehouses and data marts, and make this data securely accessible to a variety of analytics and data science tools.",
    ruby_cloud_title = "Dataplex",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataplex-ruby",
    deps = [
        ":dataplex_ruby_wrapper",
    ],
)
