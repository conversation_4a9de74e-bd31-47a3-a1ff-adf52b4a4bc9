// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.visionai.v1alpha1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/visionai/v1alpha1/lva.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.VisionAI.V1Alpha1";
option go_package = "cloud.google.com/go/visionai/apiv1alpha1/visionaipb;visionaipb";
option java_multiple_files = true;
option java_outer_classname = "LvaResourcesProto";
option java_package = "com.google.cloud.visionai.v1alpha1";
option php_namespace = "Google\\Cloud\\VisionAI\\V1alpha1";
option ruby_package = "Google::Cloud::VisionAI::V1alpha1";

// Message describing the Analysis object.
message Analysis {
  option (google.api.resource) = {
    type: "visionai.googleapis.com/Analysis"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/analyses/{analysis}"
  };

  // The name of resource.
  string name = 1;

  // Output only. The create timestamp.
  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // The definition of the analysis.
  AnalysisDefinition analysis_definition = 5;

  // Map from the input parameter in the definition to the real stream.
  // E.g., suppose you had a stream source operator named "input-0" and you try
  // to receive from the real stream "stream-0". You can add the following
  // mapping: [input-0: stream-0].
  map<string, string> input_streams_mapping = 6;

  // Map from the output parameter in the definition to the real stream.
  // E.g., suppose you had a stream sink operator named "output-0" and you try
  // to send to the real stream "stream-0". You can add the following
  // mapping: [output-0: stream-0].
  map<string, string> output_streams_mapping = 7;
}
