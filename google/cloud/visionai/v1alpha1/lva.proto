// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.visionai.v1alpha1;

option csharp_namespace = "Google.Cloud.VisionAI.V1Alpha1";
option go_package = "cloud.google.com/go/visionai/apiv1alpha1/visionaipb;visionaipb";
option java_multiple_files = true;
option java_outer_classname = "LvaProto";
option java_package = "com.google.cloud.visionai.v1alpha1";
option php_namespace = "Google\\Cloud\\VisionAI\\V1alpha1";
option ruby_package = "Google::Cloud::VisionAI::V1alpha1";

// Represents an actual value of an operator attribute.
message AttributeValue {
  // Attribute value.
  oneof value {
    // int.
    int64 i = 1;

    // float.
    float f = 2;

    // bool.
    bool b = 3;

    // string.
    bytes s = 4;
  }
}

// Defines an Analyzer.
//
// An analyzer processes data from its input streams using the logic defined in
// the Operator that it represents. Of course, it produces data for the output
// streams declared in the Operator.
message AnalyzerDefinition {
  // The inputs to this analyzer.
  //
  // We accept input name references of the following form:
  // <analyzer-name>:<output-argument-name>
  //
  // Example:
  //
  // Suppose you had an operator named "SomeOp" that has 2 output
  // arguments, the first of which is named "foo" and the second of which is
  // named "bar", and an operator named "MyOp" that accepts 2 inputs.
  //
  // Also suppose that there is an analyzer named "some-analyzer" that is
  // running "SomeOp" and another analyzer named "my-analyzer" running "MyOp".
  //
  // To indicate that "my-analyzer" is to consume "some-analyzer"'s "foo"
  // output as its first input and "some-analyzer"'s "bar" output as its
  // second input, you can set this field to the following:
  // input = ["some-analyzer:foo", "some-analyzer:bar"]
  message StreamInput {
    // The name of the stream input (as discussed above).
    string input = 1;
  }

  // Options available for debugging purposes only.
  message DebugOptions {
    // Environment variables.
    map<string, string> environment_variables = 1;
  }

  // The name of this analyzer.
  //
  // Tentatively [a-z][a-z0-9]*(_[a-z0-9]+)*.
  string analyzer = 1;

  // The name of the operator that this analyzer runs.
  //
  // Must match the name of a supported operator.
  string operator = 2;

  // Input streams.
  repeated StreamInput inputs = 3;

  // The attribute values that this analyzer applies to the operator.
  //
  // Supply a mapping between the attribute names and the actual value you wish
  // to apply. If an attribute name is omitted, then it will take a
  // preconfigured default value.
  map<string, AttributeValue> attrs = 4;

  // Debug options.
  DebugOptions debug_options = 5;
}

// Defines a full analysis.
//
// This is a description of the overall live analytics pipeline.
// You may think of this as an edge list representation of a multigraph.
//
// This may be directly authored by a human in protobuf textformat, or it may be
// generated by a programming API (perhaps Python or JavaScript depending on
// context).
message AnalysisDefinition {
  // Analyzer definitions.
  repeated AnalyzerDefinition analyzers = 1;
}
