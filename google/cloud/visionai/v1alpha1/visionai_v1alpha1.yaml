type: google.api.Service
config_version: 3
name: visionai.googleapis.com
title: Vision AI API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.visionai.v1alpha1.AppPlatform
- name: google.cloud.visionai.v1alpha1.LiveVideoAnalytics
- name: google.cloud.visionai.v1alpha1.StreamingService
- name: google.cloud.visionai.v1alpha1.StreamsService
- name: google.cloud.visionai.v1alpha1.Warehouse
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.visionai.v1alpha1.AddApplicationStreamInputResponse
- name: google.cloud.visionai.v1alpha1.AppPlatformCloudFunctionRequest
- name: google.cloud.visionai.v1alpha1.AppPlatformCloudFunctionResponse
- name: google.cloud.visionai.v1alpha1.AppPlatformMetadata
- name: google.cloud.visionai.v1alpha1.ClassificationPredictionResult
- name: google.cloud.visionai.v1alpha1.CreateApplicationInstancesResponse
- name: google.cloud.visionai.v1alpha1.CreateCorpusMetadata
- name: google.cloud.visionai.v1alpha1.DeleteApplicationInstancesResponse
- name: google.cloud.visionai.v1alpha1.DeleteAssetMetadata
- name: google.cloud.visionai.v1alpha1.DeployApplicationResponse
- name: google.cloud.visionai.v1alpha1.GetStreamThumbnailResponse
- name: google.cloud.visionai.v1alpha1.ImageObjectDetectionPredictionResult
- name: google.cloud.visionai.v1alpha1.ImageSegmentationPredictionResult
- name: google.cloud.visionai.v1alpha1.ObjectDetectionPredictionResult
- name: google.cloud.visionai.v1alpha1.OccupancyCountingPredictionResult
- name: google.cloud.visionai.v1alpha1.OperationMetadata
- name: google.cloud.visionai.v1alpha1.PersonalProtectiveEquipmentDetectionOutput
- name: google.cloud.visionai.v1alpha1.RemoveApplicationStreamInputResponse
- name: google.cloud.visionai.v1alpha1.StreamAnnotations
- name: google.cloud.visionai.v1alpha1.UndeployApplicationResponse
- name: google.cloud.visionai.v1alpha1.UpdateApplicationInstancesResponse
- name: google.cloud.visionai.v1alpha1.UpdateApplicationStreamInputResponse
- name: google.cloud.visionai.v1alpha1.VideoActionRecognitionPredictionResult
- name: google.cloud.visionai.v1alpha1.VideoClassificationPredictionResult
- name: google.cloud.visionai.v1alpha1.VideoObjectTrackingPredictionResult

documentation:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: 'google.cloud.visionai.v1alpha1.AppPlatform.*'
    deadline: 60.0
  - selector: 'google.cloud.visionai.v1alpha1.LiveVideoAnalytics.*'
    deadline: 60.0
  - selector: 'google.cloud.visionai.v1alpha1.StreamsService.*'
    deadline: 60.0
  - selector: 'google.cloud.visionai.v1alpha1.Warehouse.*'
    deadline: 60.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1alpha1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1alpha1/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1alpha1/{resource=projects/*/locations/*/clusters/*}:getIamPolicy'
    additional_bindings:
    - get: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/streams/*}:getIamPolicy'
    - get: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/events/*}:getIamPolicy'
    - get: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/series/*}:getIamPolicy'
    - get: '/v1alpha1/{resource=projects/*/locations/*/operators/*}:getIamPolicy'
    - get: '/v1alpha1/{resource=projects/*/locations/*/operators/*/versions/*}:getIamPolicy'
    - get: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/analyses/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1alpha1/{resource=projects/*/locations/*/clusters/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/streams/*}:setIamPolicy'
      body: '*'
    - post: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/events/*}:setIamPolicy'
      body: '*'
    - post: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/series/*}:setIamPolicy'
      body: '*'
    - post: '/v1alpha1/{resource=projects/*/locations/*/operators/*}:setIamPolicy'
      body: '*'
    - post: '/v1alpha1/{resource=projects/*/locations/*/operators/*/versions/*}:setIamPolicy'
      body: '*'
    - post: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/analyses/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1alpha1/{resource=projects/*/locations/*/clusters/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/streams/*}:testIamPermissions'
      body: '*'
    - post: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/events/*}:testIamPermissions'
      body: '*'
    - post: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/series/*}:testIamPermissions'
      body: '*'
    - post: '/v1alpha1/{resource=projects/*/locations/*/operators/*}:testIamPermissions'
      body: '*'
    - post: '/v1alpha1/{resource=projects/*/locations/*/operators/*/versions/*}:testIamPermissions'
      body: '*'
    - post: '/v1alpha1/{resource=projects/*/locations/*/clusters/*/analyses/*}:testIamPermissions'
      body: '*'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1alpha1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1alpha1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1alpha1/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/v1alpha1/{name=projects/*/locations/*/warehouseOperations/*}'
    - get: '/v1alpha1/{name=projects/*/locations/*/corpora/*/assets/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1alpha1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.visionai.v1alpha1.AppPlatform.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.visionai.v1alpha1.LiveVideoAnalytics.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.visionai.v1alpha1.StreamingService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.visionai.v1alpha1.StreamsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.visionai.v1alpha1.Warehouse.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
