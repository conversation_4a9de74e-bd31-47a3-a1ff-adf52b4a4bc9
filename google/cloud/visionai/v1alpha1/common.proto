// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.visionai.v1alpha1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.VisionAI.V1Alpha1";
option go_package = "cloud.google.com/go/visionai/apiv1alpha1/visionaipb;visionaipb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.cloud.visionai.v1alpha1";
option php_namespace = "Google\\Cloud\\VisionAI\\V1alpha1";
option ruby_package = "Google::Cloud::VisionAI::V1alpha1";

// Message describing the Cluster object.
message Cluster {
  option (google.api.resource) = {
    type: "visionai.googleapis.com/Cluster"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}"
  };

  // The current state of the cluster.
  enum State {
    // Not set.
    STATE_UNSPECIFIED = 0;

    // The PROVISIONING state indicates the cluster is being created.
    PROVISIONING = 1;

    // The RUNNING state indicates the cluster has been created and is fully
    // usable.
    RUNNING = 2;

    // The STOPPING state indicates the cluster is being deleted.
    STOPPING = 3;

    // The ERROR state indicates the cluster is unusable. It will be
    // automatically deleted.
    ERROR = 4;
  }

  // Output only. Name of the resource.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The create timestamp.
  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs
  map<string, string> labels = 4;

  // Annotations to allow clients to store small amounts of arbitrary data.
  map<string, string> annotations = 5;

  // Output only. The DNS name of the data plane service
  string dataplane_service_endpoint = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the cluster.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The private service connection service target name.
  string psc_target = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The Google Cloud Storage location for the input content.
message GcsSource {
  // Required. References to a Google Cloud Storage paths.
  repeated string uris = 1 [(google.api.field_behavior) = REQUIRED];
}
