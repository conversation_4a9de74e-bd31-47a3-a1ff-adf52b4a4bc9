{"methodConfig": [{"name": [{"service": "google.cloud.visionai.v1alpha1.Streams"}, {"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.visionai.v1alpha1.Streams", "method": "CreateCluster"}], "timeout": "600s"}, {"name": [{"service": "google.cloud.visionai.v1alpha1.Streams", "method": "CreateStream"}], "timeout": "300s"}, {"name": [{"service": "google.cloud.visionai.v1alpha1.Streams", "method": "CreateEvent"}], "timeout": "300s"}, {"name": [{"service": "google.cloud.visionai.v1alpha1.Streams", "method": "CreateSeries"}], "timeout": "300s"}, {"name": [{"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "CreateOperator"}], "timeout": "300s"}, {"name": [{"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "CreateVersion"}], "timeout": "300s"}, {"name": [{"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "CreateAnalysis"}], "timeout": "300s"}, {"name": [{"service": "google.cloud.visionai.v1alpha1.Streams", "method": "GetCluster"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "ListClusters"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "UpdateCluster"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "DeleteCluster"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "ListStreams"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "GetStream"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "UpdateStream"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "DeleteStream"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "ListEvents"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "GetEvent"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "UpdateEvent"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "DeleteEvent"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "ListSeries"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "GetSeries"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "UpdateSeries"}, {"service": "google.cloud.visionai.v1alpha1.Streams", "method": "DeleteSeries"}, {"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "ListOperators"}, {"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "GetOperator"}, {"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "UpdateOperator"}, {"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "DeleteOperator"}, {"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "ListAnalyses"}, {"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "GetAnalysis"}, {"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "UpdateAnalysis"}, {"service": "google.cloud.visionai.v1alpha1.LiveVideoAnalytics", "method": "DeleteAnalysis"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.visionai.v1alpha1.Warehouse", "method": "CreateCorpus"}, {"service": "google.cloud.visionai.v1alpha1.Warehouse", "method": "CreateAsset"}, {"service": "google.cloud.visionai.v1alpha1.Warehouse", "method": "CreateDataSchema"}, {"service": "google.cloud.visionai.v1alpha1.Warehouse", "method": "CreateAnnotation"}, {"service": "google.cloud.visionai.v1alpha1.Warehouse", "method": "IngestAsset"}], "timeout": "120s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "120s", "backoffMultiplier": 2.5, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}