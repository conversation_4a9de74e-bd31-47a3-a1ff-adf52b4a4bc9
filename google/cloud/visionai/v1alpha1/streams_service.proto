// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.visionai.v1alpha1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/visionai/v1alpha1/common.proto";
import "google/cloud/visionai/v1alpha1/streams_resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.VisionAI.V1Alpha1";
option go_package = "cloud.google.com/go/visionai/apiv1alpha1/visionaipb;visionaipb";
option java_multiple_files = true;
option java_outer_classname = "StreamsServiceProto";
option java_package = "com.google.cloud.visionai.v1alpha1";
option php_namespace = "Google\\Cloud\\VisionAI\\V1alpha1";
option ruby_package = "Google::Cloud::VisionAI::V1alpha1";

// Service describing handlers for resources.
// Vision API and Vision AI API are two independent APIs developed by the same
// team. Vision API is for people to annotate their image while Vision AI is an
// e2e solution for customer to build their own computer vision application.
service StreamsService {
  option (google.api.default_host) = "visionai.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists Clusters in a given project and location.
  rpc ListClusters(ListClustersRequest) returns (ListClustersResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*}/clusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Cluster.
  rpc GetCluster(GetClusterRequest) returns (Cluster) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Cluster in a given project and location.
  rpc CreateCluster(CreateClusterRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*}/clusters"
      body: "cluster"
    };
    option (google.api.method_signature) = "parent,cluster,cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Cluster.
  rpc UpdateCluster(UpdateClusterRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha1/{cluster.name=projects/*/locations/*/clusters/*}"
      body: "cluster"
    };
    option (google.api.method_signature) = "cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Cluster.
  rpc DeleteCluster(DeleteClusterRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Streams in a given project and location.
  rpc ListStreams(ListStreamsRequest) returns (ListStreamsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/clusters/*}/streams"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Stream.
  rpc GetStream(GetStreamRequest) returns (Stream) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/clusters/*/streams/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Stream in a given project and location.
  rpc CreateStream(CreateStreamRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*/clusters/*}/streams"
      body: "stream"
    };
    option (google.api.method_signature) = "parent,stream,stream_id";
    option (google.longrunning.operation_info) = {
      response_type: "Stream"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Stream.
  rpc UpdateStream(UpdateStreamRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha1/{stream.name=projects/*/locations/*/clusters/*/streams/*}"
      body: "stream"
    };
    option (google.api.method_signature) = "stream,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Stream"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Stream.
  rpc DeleteStream(DeleteStreamRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/clusters/*/streams/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Generate the JWT auth token required to get the stream HLS contents.
  rpc GenerateStreamHlsToken(GenerateStreamHlsTokenRequest) returns (GenerateStreamHlsTokenResponse) {
    option (google.api.http) = {
      post: "/v1alpha1/{stream=projects/*/locations/*/clusters/*/streams/*}:generateStreamHlsToken"
      body: "*"
    };
    option (google.api.method_signature) = "stream";
  }

  // Lists Events in a given project and location.
  rpc ListEvents(ListEventsRequest) returns (ListEventsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/clusters/*}/events"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Event.
  rpc GetEvent(GetEventRequest) returns (Event) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/clusters/*/events/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Event in a given project and location.
  rpc CreateEvent(CreateEventRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*/clusters/*}/events"
      body: "event"
    };
    option (google.api.method_signature) = "parent,event,event_id";
    option (google.longrunning.operation_info) = {
      response_type: "Event"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Event.
  rpc UpdateEvent(UpdateEventRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha1/{event.name=projects/*/locations/*/clusters/*/events/*}"
      body: "event"
    };
    option (google.api.method_signature) = "event,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Event"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Event.
  rpc DeleteEvent(DeleteEventRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/clusters/*/events/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Series in a given project and location.
  rpc ListSeries(ListSeriesRequest) returns (ListSeriesResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/clusters/*}/series"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Series.
  rpc GetSeries(GetSeriesRequest) returns (Series) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/clusters/*/series/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Series in a given project and location.
  rpc CreateSeries(CreateSeriesRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*/clusters/*}/series"
      body: "series"
    };
    option (google.api.method_signature) = "parent,series,series_id";
    option (google.longrunning.operation_info) = {
      response_type: "Series"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Event.
  rpc UpdateSeries(UpdateSeriesRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha1/{series.name=projects/*/locations/*/clusters/*/series/*}"
      body: "series"
    };
    option (google.api.method_signature) = "series,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Series"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Series.
  rpc DeleteSeries(DeleteSeriesRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/clusters/*/series/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Materialize a channel.
  rpc MaterializeChannel(MaterializeChannelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*/clusters/*}/channels"
      body: "channel"
    };
    option (google.api.method_signature) = "parent,channel,channel_id";
    option (google.longrunning.operation_info) = {
      response_type: "Channel"
      metadata_type: "OperationMetadata"
    };
  }
}

// Message for requesting list of Clusters.
message ListClustersRequest {
  // Required. Parent value for ListClustersRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Hint for how to order the results.
  string order_by = 5;
}

// Message for response to listing Clusters.
message ListClustersResponse {
  // The list of Cluster.
  repeated Cluster clusters = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Cluster.
message GetClusterRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];
}

// Message for creating a Cluster.
message CreateClusterRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Required. Id of the requesting object.
  string cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Cluster cluster = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Cluster.
message UpdateClusterRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Cluster resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  Cluster cluster = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Cluster.
message DeleteClusterRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Streams.
message ListStreamsRequest {
  // Required. Parent value for ListStreamsRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Hint for how to order the results.
  string order_by = 5;
}

// Message for response to listing Streams.
message ListStreamsResponse {
  // The list of Stream.
  repeated Stream streams = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Stream.
message GetStreamRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Stream"
    }
  ];
}

// Message for creating a Stream.
message CreateStreamRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Required. Id of the requesting object.
  string stream_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Stream stream = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Stream.
message UpdateStreamRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Stream resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  Stream stream = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Stream.
message DeleteStreamRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Stream"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for the response of GetStreamThumbnail. The empty response message
// indicates the thumbnail image has been uploaded to GCS successfully.
message GetStreamThumbnailResponse {

}

// Request message for getting the auth token to access the stream HLS contents.
message GenerateStreamHlsTokenRequest {
  // Required. The name of the stream.
  string stream = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response message for GenerateStreamHlsToken.
message GenerateStreamHlsTokenResponse {
  // The generated JWT token.
  //
  // The caller should insert this token to the authorization header of the HTTP
  // requests to get the HLS playlist manifest and the video chunks.
  // eg: curl -H "Authorization: Bearer $TOKEN"
  //     https://domain.com/test-stream.playback/master.m3u8
  string token = 1;

  // The expiration time of the token.
  google.protobuf.Timestamp expiration_time = 2;
}

// Message for requesting list of Events.
message ListEventsRequest {
  // Required. Parent value for ListEventsRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Hint for how to order the results.
  string order_by = 5;
}

// Message for response to listing Events.
message ListEventsResponse {
  // The list of Event.
  repeated Event events = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Event.
message GetEventRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Event"
    }
  ];
}

// Message for creating a Event.
message CreateEventRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Required. Id of the requesting object.
  string event_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Event event = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Event.
message UpdateEventRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Event resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  Event event = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Event.
message DeleteEventRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Event"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Series.
message ListSeriesRequest {
  // Required. Parent value for ListSeriesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Hint for how to order the results.
  string order_by = 5;
}

// Message for response to listing Series.
message ListSeriesResponse {
  // The list of Series.
  repeated Series series = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Series.
message GetSeriesRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Series"
    }
  ];
}

// Message for creating a Series.
message CreateSeriesRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Required. Id of the requesting object.
  string series_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Series series = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Series.
message UpdateSeriesRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the Series
  // resource by the update. The fields specified in the update_mask are
  // relative to the resource, not the full request. A field will be overwritten
  // if it is in the mask. If the user does not provide a mask then all fields
  // will be overwritten.
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  Series series = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Series.
message DeleteSeriesRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Series"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for materializing a channel.
message MaterializeChannelRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Required. Id of the channel.
  string channel_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Channel channel = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}
