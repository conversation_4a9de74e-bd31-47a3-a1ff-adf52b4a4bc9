// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.visionai.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/visionai/v1/lva.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.VisionAI.V1";
option go_package = "cloud.google.com/go/visionai/apiv1/visionaipb;visionaipb";
option java_multiple_files = true;
option java_outer_classname = "LvaResourcesProto";
option java_package = "com.google.cloud.visionai.v1";
option php_namespace = "Google\\Cloud\\VisionAI\\V1";
option ruby_package = "Google::Cloud::VisionAI::V1";

// Message describing the Operator object.
message Operator {
  option (google.api.resource) = {
    type: "visionai.googleapis.com/Operator"
    pattern: "projects/{project}/locations/{location}/operators/{operator}"
  };

  // Name of the resource.
  string name = 1;

  // Output only. The create timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // The definition of the operator.
  OperatorDefinition operator_definition = 5;

  // The link to the docker image of the operator.
  string docker_image = 6;
}

// Message describing the Analysis object.
message Analysis {
  option (google.api.resource) = {
    type: "visionai.googleapis.com/Analysis"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/analyses/{analysis}"
  };

  // The name of resource.
  string name = 1;

  // Output only. The create timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // The definition of the analysis.
  AnalysisDefinition analysis_definition = 5;

  // Map from the input parameter in the definition to the real stream.
  // E.g., suppose you had a stream source operator named "input-0" and you try
  // to receive from the real stream "stream-0". You can add the following
  // mapping: [input-0: stream-0].
  map<string, string> input_streams_mapping = 6;

  // Map from the output parameter in the definition to the real stream.
  // E.g., suppose you had a stream sink operator named "output-0" and you try
  // to send to the real stream "stream-0". You can add the following
  // mapping: [output-0: stream-0].
  map<string, string> output_streams_mapping = 7;

  // Boolean flag to indicate whether you would like to disable the ability
  // to automatically start a Process when new event happening in the input
  // Stream. If you would like to start a Process manually, the field needs
  // to be set to true.
  bool disable_event_watch = 8;
}

// Message describing the Process object.
message Process {
  option (google.api.resource) = {
    type: "visionai.googleapis.com/Process"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/processes/{process}"
  };

  // The name of resource.
  string name = 1;

  // Output only. The create timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Reference to an existing Analysis resource.
  string analysis = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Analysis"
    }
  ];

  // Optional. Attribute overrides of the Analyzers.
  // Format for each single override item:
  // "{analyzer_name}:{attribute_key}={value}"
  repeated string attribute_overrides = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Status of the Process.
  RunStatus run_status = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Run mode of the Process.
  RunMode run_mode = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Event ID of the input/output streams.
  // This is useful when you have a StreamSource/StreamSink operator in the
  // Analysis, and you want to manually specify the Event to read from/write to.
  string event_id = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional: Batch ID of the Process.
  string batch_id = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional: The number of retries for a process in submission mode
  // the system should try before declaring failure. By default, no retry will
  // be performed.
  int32 retry_count = 10 [(google.api.field_behavior) = OPTIONAL];
}
