// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.visionai.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/visionai/v1/common.proto";
import "google/cloud/visionai/v1/lva_resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.VisionAI.V1";
option go_package = "cloud.google.com/go/visionai/apiv1/visionaipb;visionaipb";
option java_multiple_files = true;
option java_outer_classname = "LvaServiceProto";
option java_package = "com.google.cloud.visionai.v1";
option php_namespace = "Google\\Cloud\\VisionAI\\V1";
option ruby_package = "Google::Cloud::VisionAI::V1";

// Service describing handlers for resources. The service enables clients to run
// Live Video Analytics (LVA) on the streaming inputs.
service LiveVideoAnalytics {
  option (google.api.default_host) = "visionai.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // ListPublicOperators returns all the operators in public registry.
  rpc ListPublicOperators(ListPublicOperatorsRequest)
      returns (ListPublicOperatorsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}:listPublicOperators"
    };
    option (google.api.method_signature) = "parent";
  }

  // ResolveOperatorInfo returns the operator information based on the request.
  rpc ResolveOperatorInfo(ResolveOperatorInfoRequest)
      returns (ResolveOperatorInfoResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}:resolveOperatorInfo"
      body: "*"
    };
    option (google.api.method_signature) = "parent,queries";
  }

  // Lists Operators in a given project and location.
  rpc ListOperators(ListOperatorsRequest) returns (ListOperatorsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/operators"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Operator.
  rpc GetOperator(GetOperatorRequest) returns (Operator) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/operators/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Operator in a given project and location.
  rpc CreateOperator(CreateOperatorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/operators"
      body: "operator"
    };
    option (google.api.method_signature) = "parent,operator,operator_id";
    option (google.longrunning.operation_info) = {
      response_type: "Operator"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Operator.
  rpc UpdateOperator(UpdateOperatorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{operator.name=projects/*/locations/*/operators/*}"
      body: "operator"
    };
    option (google.api.method_signature) = "operator,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Operator"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Operator.
  rpc DeleteOperator(DeleteOperatorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/operators/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Analyses in a given project and location.
  rpc ListAnalyses(ListAnalysesRequest) returns (ListAnalysesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/clusters/*}/analyses"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Analysis.
  rpc GetAnalysis(GetAnalysisRequest) returns (Analysis) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clusters/*/analyses/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Analysis in a given project and location.
  rpc CreateAnalysis(CreateAnalysisRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/clusters/*}/analyses"
      body: "analysis"
    };
    option (google.api.method_signature) = "parent,analysis,analysis_id";
    option (google.longrunning.operation_info) = {
      response_type: "Analysis"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Analysis.
  rpc UpdateAnalysis(UpdateAnalysisRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{analysis.name=projects/*/locations/*/clusters/*/analyses/*}"
      body: "analysis"
    };
    option (google.api.method_signature) = "analysis,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Analysis"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Analysis.
  rpc DeleteAnalysis(DeleteAnalysisRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clusters/*/analyses/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Processes in a given project and location.
  rpc ListProcesses(ListProcessesRequest) returns (ListProcessesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/clusters/*}/processes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Process.
  rpc GetProcess(GetProcessRequest) returns (Process) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clusters/*/processes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Process in a given project and location.
  rpc CreateProcess(CreateProcessRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/clusters/*}/processes"
      body: "process"
    };
    option (google.api.method_signature) = "parent,process,process_id";
    option (google.longrunning.operation_info) = {
      response_type: "Process"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Process.
  rpc UpdateProcess(UpdateProcessRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{process.name=projects/*/locations/*/clusters/*/processes/*}"
      body: "process"
    };
    option (google.api.method_signature) = "process,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Process"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Process.
  rpc DeleteProcess(DeleteProcessRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clusters/*/processes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Run all of the processes to "completion". Max time for each process is
  // the LRO time limit.
  rpc BatchRunProcess(BatchRunProcessRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/clusters/*}/processes:batchRun"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
    option (google.longrunning.operation_info) = {
      response_type: "BatchRunProcessResponse"
      metadata_type: "OperationMetadata"
    };
  }
}

// The enum of the types of the Registry.
enum Registry {
  // Registry is unspecified.
  REGISTRY_UNSPECIFIED = 0;

  // Public Registry containing the public Operators released by Google.
  PUBLIC = 1;

  // Private Registry containing the local registered operators.
  PRIVATE = 2;
}

// Message for requesting list of Operators.
message ListOperatorsRequest {
  // Required. Parent value for ListOperatorsRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Hint for how to order the results.
  string order_by = 5;
}

// Message for response to listing Operators.
message ListOperatorsResponse {
  // The list of Operator
  repeated Operator operators = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Operator.
message GetOperatorRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Operator"
    }
  ];
}

// Message for creating a Operator.
message CreateOperatorRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Id of the requesting object.
  string operator_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Operator operator = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Operator.
message UpdateOperatorRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Operator resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  Operator operator = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Operator
message DeleteOperatorRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Operator"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Analyses
message ListAnalysesRequest {
  // Required. Parent value for ListAnalysesRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing Analyses
message ListAnalysesResponse {
  // The list of Analysis
  repeated Analysis analyses = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting an Analysis.
message GetAnalysisRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Analysis"
    }
  ];
}

// Message for creating an Analysis.
message CreateAnalysisRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Required. Id of the requesting object.
  string analysis_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Analysis analysis = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating an Analysis.
message UpdateAnalysisRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Analysis resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  Analysis analysis = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting an Analysis.
message DeleteAnalysisRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Analysis"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Processes.
message ListProcessesRequest {
  // Required. Parent value for ListProcessesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing Processes.
message ListProcessesResponse {
  // The list of Processes.
  repeated Process processes = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Process.
message GetProcessRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Process"
    }
  ];
}

// Message for creating a Process.
message CreateProcessRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Required. Id of the requesting object.
  string process_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Process process = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Process.
message UpdateProcessRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Process resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  Process process = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Process.
message DeleteProcessRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Process"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for running the processes in a batch.
message BatchRunProcessRequest {
  // Options for batch processes.
  message BatchRunProcessOptions {
    // The retry counts per process. Default: 3.
    int32 retry_count = 1;

    // The batch size. Default: 5, maximum: 100.
    int32 batch_size = 2;
  }

  // Required. The parent resource shared by all processes being created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "visionai.googleapis.com/Cluster"
    }
  ];

  // Required. The create process requests.
  repeated CreateProcessRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Options for batch processes.
  BatchRunProcessOptions options = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The batch ID.
  string batch_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Response message for running the processes in a batch.
message BatchRunProcessResponse {
  // The batch ID.
  string batch_id = 1;

  // Processes created.
  repeated Process processes = 2;
}

// Request message for querying operator info.
message ResolveOperatorInfoRequest {
  // Required. Parent value for ResolveOperatorInfoRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The operator queries.
  repeated OperatorQuery queries = 2 [(google.api.field_behavior) = REQUIRED];
}

// OperatorQuery represents one query to a Operator.
message OperatorQuery {
  // Required. The canonical Name of the operator. e.g. OccupancyCounting.
  string operator = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Tag of the operator.
  string tag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Registry of the operator.
  Registry registry = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message of ResolveOperatorInfo API.
message ResolveOperatorInfoResponse {
  // Operators with detailed information.
  repeated Operator operators = 1;
}

// Request message of ListPublicOperatorsRequest API.
message ListPublicOperatorsRequest {
  // Required. Parent value for ListPublicOperatorsRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Hint for how to order the results.
  string order_by = 5;
}

// Response message of ListPublicOperators API.
message ListPublicOperatorsResponse {
  // The list of Operator
  repeated Operator operators = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}
