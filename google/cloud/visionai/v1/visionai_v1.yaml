type: google.api.Service
config_version: 3
name: visionai.googleapis.com
title: Vision AI API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.visionai.v1.AppPlatform
- name: google.cloud.visionai.v1.HealthCheckService
- name: google.cloud.visionai.v1.LiveVideoAnalytics
- name: google.cloud.visionai.v1.StreamingService
- name: google.cloud.visionai.v1.StreamsService
- name: google.cloud.visionai.v1.Warehouse
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.visionai.v1.AddApplicationStreamInputResponse
- name: google.cloud.visionai.v1.AnalyzeAssetMetadata
- name: google.cloud.visionai.v1.AnalyzeAssetResponse
- name: google.cloud.visionai.v1.AnalyzeCorpusMetadata
- name: google.cloud.visionai.v1.AnalyzeCorpusResponse
- name: google.cloud.visionai.v1.AppPlatformCloudFunctionRequest
- name: google.cloud.visionai.v1.AppPlatformCloudFunctionResponse
- name: google.cloud.visionai.v1.AppPlatformMetadata
- name: google.cloud.visionai.v1.BatchOperationStatus
- name: google.cloud.visionai.v1.BatchRunProcessResponse
- name: google.cloud.visionai.v1.ClassificationPredictionResult
- name: google.cloud.visionai.v1.CreateApplicationInstancesResponse
- name: google.cloud.visionai.v1.CreateCollectionMetadata
- name: google.cloud.visionai.v1.CreateCorpusMetadata
- name: google.cloud.visionai.v1.CreateIndexEndpointMetadata
- name: google.cloud.visionai.v1.CreateIndexMetadata
- name: google.cloud.visionai.v1.DeleteApplicationInstancesResponse
- name: google.cloud.visionai.v1.DeleteAssetMetadata
- name: google.cloud.visionai.v1.DeleteCollectionMetadata
- name: google.cloud.visionai.v1.DeleteIndexEndpointMetadata
- name: google.cloud.visionai.v1.DeleteIndexMetadata
- name: google.cloud.visionai.v1.DeployApplicationResponse
- name: google.cloud.visionai.v1.DeployIndexMetadata
- name: google.cloud.visionai.v1.DeployIndexResponse
- name: google.cloud.visionai.v1.GetStreamThumbnailResponse
- name: google.cloud.visionai.v1.ImageObjectDetectionPredictionResult
- name: google.cloud.visionai.v1.ImageSegmentationPredictionResult
- name: google.cloud.visionai.v1.ImportAssetsMetadata
- name: google.cloud.visionai.v1.ImportAssetsResponse
- name: google.cloud.visionai.v1.IndexAssetMetadata
- name: google.cloud.visionai.v1.IndexAssetResponse
- name: google.cloud.visionai.v1.ObjectDetectionPredictionResult
- name: google.cloud.visionai.v1.OccupancyCountingPredictionResult
- name: google.cloud.visionai.v1.OperationMetadata
- name: google.cloud.visionai.v1.PersonalProtectiveEquipmentDetectionOutput
- name: google.cloud.visionai.v1.RemoveApplicationStreamInputResponse
- name: google.cloud.visionai.v1.RemoveIndexAssetMetadata
- name: google.cloud.visionai.v1.RemoveIndexAssetResponse
- name: google.cloud.visionai.v1.StreamAnnotations
- name: google.cloud.visionai.v1.UndeployApplicationResponse
- name: google.cloud.visionai.v1.UndeployIndexMetadata
- name: google.cloud.visionai.v1.UndeployIndexResponse
- name: google.cloud.visionai.v1.UpdateApplicationInstancesResponse
- name: google.cloud.visionai.v1.UpdateApplicationStreamInputResponse
- name: google.cloud.visionai.v1.UpdateIndexEndpointMetadata
- name: google.cloud.visionai.v1.UpdateIndexMetadata
- name: google.cloud.visionai.v1.UploadAssetMetadata
- name: google.cloud.visionai.v1.UploadAssetResponse
- name: google.cloud.visionai.v1.VideoActionRecognitionPredictionResult
- name: google.cloud.visionai.v1.VideoClassificationPredictionResult
- name: google.cloud.visionai.v1.VideoObjectTrackingPredictionResult

documentation:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: 'google.cloud.visionai.v1.AppPlatform.*'
    deadline: 60.0
  - selector: 'google.cloud.visionai.v1.LiveVideoAnalytics.*'
    deadline: 60.0
  - selector: 'google.cloud.visionai.v1.StreamsService.*'
    deadline: 60.0
  - selector: 'google.cloud.visionai.v1.Warehouse.*'
    deadline: 60.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/v1/{name=projects/*/locations/*/warehouseOperations/*}'
    - get: '/v1/{name=projects/*/locations/*/corpora/*/assets/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/corpora/*/collections/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/corpora/*/imageIndexes/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/corpora/*/indexes/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/corpora/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.visionai.v1.AppPlatform.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.visionai.v1.HealthCheckService.HealthCheck
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.visionai.v1.LiveVideoAnalytics.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.visionai.v1.StreamingService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.visionai.v1.StreamsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.visionai.v1.Warehouse.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=187174&template=1161261
  documentation_uri: https://cloud.google.com/vision-ai/docs
  api_short_name: visionai
  github_label: 'api: visionai'
  doc_tag_prefix: visionai
  organization: CLOUD
  library_settings:
  - version: google.cloud.visionai.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/vision-ai/docs/reference/rest
