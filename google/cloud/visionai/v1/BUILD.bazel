# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "visionai_proto",
    srcs = [
        "annotations.proto",
        "common.proto",
        "lva.proto",
        "lva_resources.proto",
        "lva_service.proto",
        "platform.proto",
        "streaming_resources.proto",
        "streaming_service.proto",
        "streams_resources.proto",
        "streams_service.proto",
        "warehouse.proto",
        "health_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:datetime_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "visionai_proto_with_info",
    deps = [
        ":visionai_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "visionai_java_proto",
    deps = [":visionai_proto"],
)

java_grpc_library(
    name = "visionai_java_grpc",
    srcs = [":visionai_proto"],
    deps = [":visionai_java_proto"],
)

java_gapic_library(
    name = "visionai_java_gapic",
    srcs = [":visionai_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "visionai_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "visionai_v1.yaml",
    test_deps = [
        ":visionai_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":visionai_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "visionai_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.visionai.v1.AppPlatformClientHttpJsonTest",
        "com.google.cloud.visionai.v1.AppPlatformClientTest",
        "com.google.cloud.visionai.v1.LiveVideoAnalyticsClientHttpJsonTest",
        "com.google.cloud.visionai.v1.LiveVideoAnalyticsClientTest",
        "com.google.cloud.visionai.v1.StreamingServiceClientHttpJsonTest",
        "com.google.cloud.visionai.v1.StreamingServiceClientTest",
        "com.google.cloud.visionai.v1.StreamsServiceClientHttpJsonTest",
        "com.google.cloud.visionai.v1.StreamsServiceClientTest",
        "com.google.cloud.visionai.v1.WarehouseClientHttpJsonTest",
        "com.google.cloud.visionai.v1.WarehouseClientTest",
    ],
    runtime_deps = [":visionai_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-visionai-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":visionai_java_gapic",
        ":visionai_java_grpc",
        ":visionai_java_proto",
        ":visionai_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "visionai_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/visionai/apiv1/visionaipb",
    protos = [":visionai_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:datetime_go_proto",
    ],
)

go_gapic_library(
    name = "visionai_go_gapic",
    srcs = [":visionai_proto_with_info"],
    grpc_service_config = "visionai_grpc_service_config.json",
    importpath = "cloud.google.com/go/visionai/apiv1;visionai",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "visionai_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":visionai_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-visionai-v1-go",
    deps = [
        ":visionai_go_gapic",
        ":visionai_go_gapic_srcjar-metadata.srcjar",
        ":visionai_go_gapic_srcjar-snippets.srcjar",
        ":visionai_go_gapic_srcjar-test.srcjar",
        ":visionai_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "visionai_py_gapic",
    srcs = [":visionai_proto"],
    grpc_service_config = "visionai_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "visionai_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "visionai_py_gapic_test",
    srcs = [
        "visionai_py_gapic_pytest.py",
        "visionai_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":visionai_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "visionai-v1-py",
    deps = [
        ":visionai_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "visionai_php_proto",
    deps = [":visionai_proto"],
)

php_gapic_library(
    name = "visionai_php_gapic",
    srcs = [":visionai_proto_with_info"],
    grpc_service_config = "visionai_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "visionai_v1.yaml",
    transport = "grpc+rest",
    deps = [":visionai_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-visionai-v1-php",
    deps = [
        ":visionai_php_gapic",
        ":visionai_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "visionai_nodejs_gapic",
    package_name = "@google-cloud/visionai",
    src = ":visionai_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "visionai_grpc_service_config.json",
    package = "google.cloud.visionai.v1",
    rest_numeric_enums = True,
    service_yaml = "visionai_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "visionai-v1-nodejs",
    deps = [
        ":visionai_nodejs_gapic",
        ":visionai_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "visionai_ruby_proto",
    deps = [":visionai_proto"],
)

ruby_grpc_library(
    name = "visionai_ruby_grpc",
    srcs = [":visionai_proto"],
    deps = [":visionai_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "visionai_ruby_gapic",
    srcs = [":visionai_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-vision_ai-v1",
        "ruby-cloud-gem-namespace=Google::Cloud::VisionAI::V1",
    ],
    grpc_service_config = "visionai_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "visionai_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":visionai_ruby_grpc",
        ":visionai_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-visionai-v1-ruby",
    deps = [
        ":visionai_ruby_gapic",
        ":visionai_ruby_grpc",
        ":visionai_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "visionai_csharp_proto",
    deps = [":visionai_proto"],
)

csharp_grpc_library(
    name = "visionai_csharp_grpc",
    srcs = [":visionai_proto"],
    deps = [":visionai_csharp_proto"],
)

csharp_gapic_library(
    name = "visionai_csharp_gapic",
    srcs = [":visionai_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "visionai_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "visionai_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":visionai_csharp_grpc",
        ":visionai_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-visionai-v1-csharp",
    deps = [
        ":visionai_csharp_gapic",
        ":visionai_csharp_grpc",
        ":visionai_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "visionai_cc_proto",
    deps = [":visionai_proto"],
)

cc_grpc_library(
    name = "visionai_cc_grpc",
    srcs = [":visionai_proto"],
    generate_mocks = True,
    grpc_only = True,
    deps = [":visionai_cc_proto"],
)
