// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.visionai.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.VisionAI.V1";
option go_package = "cloud.google.com/go/visionai/apiv1/visionaipb;visionaipb";
option java_multiple_files = true;
option java_outer_classname = "StreamsResourcesProto";
option java_package = "com.google.cloud.visionai.v1";
option php_namespace = "Google\\Cloud\\VisionAI\\V1";
option ruby_package = "Google::Cloud::VisionAI::V1";

// Message describing the Stream object. The Stream and the Event resources are
// many to many; i.e., each Stream resource can associate to many Event
// resources and each Event resource can associate to many Stream resources.
message Stream {
  option (google.api.resource) = {
    type: "visionai.googleapis.com/Stream"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/streams/{stream}"
  };

  // Name of the resource.
  string name = 1;

  // Output only. The create timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // Annotations to allow clients to store small amounts of arbitrary data.
  map<string, string> annotations = 5;

  // The display name for the stream resource.
  string display_name = 6;

  // Whether to enable the HLS playback service on this stream.
  bool enable_hls_playback = 7;

  // The name of the media warehouse asset for long term storage of stream data.
  // Format: projects/${p_id}/locations/${l_id}/corpora/${c_id}/assets/${a_id}
  // Remain empty if the media warehouse storage is not needed for the stream.
  string media_warehouse_asset = 8;
}

// Message describing the Event object.
message Event {
  option (google.api.resource) = {
    type: "visionai.googleapis.com/Event"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/events/{event}"
  };

  // Clock that will be used for joining streams.
  enum Clock {
    // Clock is not specified.
    CLOCK_UNSPECIFIED = 0;

    // Use the timestamp when the data is captured. Clients need to sync the
    // clock.
    CAPTURE = 1;

    // Use the timestamp when the data is received.
    INGEST = 2;
  }

  // Name of the resource.
  string name = 1;

  // Output only. The create timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // Annotations to allow clients to store small amounts of arbitrary data.
  map<string, string> annotations = 5;

  // The clock used for joining streams.
  Clock alignment_clock = 6;

  // Grace period for cleaning up the event. This is the time the controller
  // waits for before deleting the event. During this period, if there is any
  // active channel on the event. The deletion of the event after grace_period
  // will be ignored.
  google.protobuf.Duration grace_period = 7;
}

// Message describing the Series object.
message Series {
  option (google.api.resource) = {
    type: "visionai.googleapis.com/Series"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/series/{series}"
  };

  // Name of the resource.
  string name = 1;

  // Output only. The create timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // Annotations to allow clients to store small amounts of arbitrary data.
  map<string, string> annotations = 5;

  // Required. Stream that is associated with this series.
  string stream = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "visionai.googleapis.com/Stream" }
  ];

  // Required. Event that is associated with this series.
  string event = 7 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "visionai.googleapis.com/Event" }
  ];
}

// Message describing the Channel object.
message Channel {
  option (google.api.resource) = {
    type: "visionai.googleapis.com/Channel"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/channels/{channel}"
  };

  // Name of the resource.
  string name = 1;

  // Output only. The create timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // Annotations to allow clients to store small amounts of arbitrary data.
  map<string, string> annotations = 5;

  // Required. Stream that is associated with this series.
  string stream = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "visionai.googleapis.com/Stream" }
  ];

  // Required. Event that is associated with this series.
  string event = 7 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "visionai.googleapis.com/Event" }
  ];
}
