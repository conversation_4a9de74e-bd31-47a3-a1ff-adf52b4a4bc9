// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.visionai.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.VisionAI.V1";
option go_package = "cloud.google.com/go/visionai/apiv1/visionaipb;visionaipb";
option java_multiple_files = true;
option java_outer_classname = "HealthCheckServiceProto";
option java_package = "com.google.cloud.visionai.v1";
option php_namespace = "Google\\Cloud\\VisionAI\\V1";
option ruby_package = "Google::Cloud::VisionAI::V1";

// HealthCheckService provides an interface for Vertex AI Vision Cluster Health
// Check.
service HealthCheckService {
  option (google.api.default_host) = "visionai.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // HealthCheck method checks the health status of the cluster.
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse) {
    option (google.api.http) = {
      get: "/v1/{cluster=projects/*/locations/*/clusters/*}:healthCheck"
    };
  }
}

// HealthCheckRequest is the request message for Check.
message HealthCheckRequest {
  // The parent of the resource.
  string cluster = 1 [(google.api.resource_reference) = {
    type: "visionai.googleapis.com/Cluster"
  }];
}

// HealthCheckResponse is the response message for Check.
message HealthCheckResponse {
  // Indicates whether the cluster is in healthy state or not.
  bool healthy = 1;

  // Reason of why the cluster is in unhealthy state.
  string reason = 2;

  // Other information of the cluster client may be interested.
  ClusterInfo cluster_info = 3;
}

message ClusterInfo {
  // The number of active streams in the cluster.
  int32 streams_count = 1;

  // The number of active processes in the cluster.
  int32 processes_count = 2;
}
