// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataproc.logging;

import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.Dataproc.Logging";
option go_package = "cloud.google.com/go/dataproc/logging/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "ReconcilerLogProto";
option java_package = "com.google.cloud.dataproc.logging";

// Reconciliation log for session ttl event.
message ReconciliationLog {
  // The input values for the Reconciler recommendation algorithm.
  // We could add more details in future if required.
  message Inputs {
    // Idle duration
    google.protobuf.Duration idle_duration = 1;

    // Configured idle TTL
    google.protobuf.Duration idle_ttl = 2;

    // Total session lifetime
    google.protobuf.Duration session_lifetime = 3;

    // Configured ttl
    google.protobuf.Duration ttl = 4;
  }

  // Reconciler recommendations.
  message Outputs {
    // The high-level reconciliation decision.
    ReconciliationDecisionType decision = 1;

    // Human readable context messages which explain the reconciler decision.
    string decision_details = 2;
  }

  // The reconciliation algorithm inputs.
  Inputs inputs = 1;

  // The algorithm outputs for the recommended reconciliation operation.
  Outputs outputs = 2;
}

// Reconciliation log for cluster heal event.
message ReconciliationClusterHealLog {
  // Autohealer decision.
  message Outputs {
    // The repair operation id triggered by Autohealer if any.
    string repair_operation_id = 1;

    // Human readable context messages which explain the autohealer decision.
    string decision_details = 2;
  }

  // The algorithm outputs for the recommended reconciliation operation.
  Outputs outputs = 1;
}

// Decision type
enum ReconciliationDecisionType {
  // Unspecified type
  RECONCILIATION_DECISION_TYPE_UNSPECIFIED = 0;

  // Terminate session
  RECONCILIATION_TERMINATE_SESSION = 1;
}
