type: google.api.Service
config_version: 3
name: dataproc.googleapis.com
title: Cloud Dataproc API

apis:
- name: google.cloud.dataproc.v1.AutoscalingPolicyService
- name: google.cloud.dataproc.v1.BatchController
- name: google.cloud.dataproc.v1.ClusterController
- name: google.cloud.dataproc.v1.JobController
- name: google.cloud.dataproc.v1.NodeGroupController
- name: google.cloud.dataproc.v1.SessionController
- name: google.cloud.dataproc.v1.SessionTemplateController
- name: google.cloud.dataproc.v1.WorkflowTemplateService
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.dataproc.v1.BatchOperationMetadata
- name: google.cloud.dataproc.v1.ClusterOperationMetadata
- name: google.cloud.dataproc.v1.DiagnoseClusterResults
- name: google.cloud.dataproc.v1.JobMetadata
- name: google.cloud.dataproc.v1.NodeGroupOperationMetadata
- name: google.cloud.dataproc.v1.SessionOperationMetadata
- name: google.cloud.dataproc.v1.WorkflowMetadata

documentation:
  summary: 'Manages Hadoop-based clusters and jobs on Google Cloud Platform.'
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

http:
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    post: '/v1/{resource=projects/*/regions/*/clusters/*}:getIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/regions/*/jobs/*}:getIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/regions/*/operations/*}:getIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/regions/*/workflowTemplates/*}:getIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/workflowTemplates/*}:getIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/regions/*/autoscalingPolicies/*}:getIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/autoscalingPolicies/*}:getIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1/{resource=projects/*/regions/*/clusters/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/regions/*/jobs/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/regions/*/operations/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/regions/*/workflowTemplates/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/workflowTemplates/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/regions/*/autoscalingPolicies/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/autoscalingPolicies/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1/{resource=projects/*/regions/*/clusters/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/regions/*/jobs/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/regions/*/operations/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/regions/*/workflowTemplates/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/workflowTemplates/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/regions/*/autoscalingPolicies/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/autoscalingPolicies/*}:testIamPermissions'
      body: '*'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/regions/*/operations/*}:cancel'
    additional_bindings:
    - post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/regions/*/operations/*}'
    additional_bindings:
    - delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/regions/*/operations/*}'
    additional_bindings:
    - get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/regions/*/operations}'
    additional_bindings:
    - get: '/v1/{name=projects/*/locations/*/operations}'

authentication:
  rules:
  - selector: 'google.cloud.dataproc.v1.AutoscalingPolicyService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataproc.v1.BatchController.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataproc.v1.ClusterController.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataproc.v1.JobController.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataproc.v1.NodeGroupController.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataproc.v1.SessionController.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataproc.v1.SessionTemplateController.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.dataproc.v1.WorkflowTemplateService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
