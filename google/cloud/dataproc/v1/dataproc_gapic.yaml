type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
interfaces:
- name: google.cloud.dataproc.v1.ClusterController
  methods:
  - name: CreateCluster
    long_running:
      initial_poll_delay_millis: 1000
      poll_delay_multiplier: 2
      max_poll_delay_millis: 10000
      total_poll_timeout_millis: 900000
  - name: UpdateCluster
    long_running:
      initial_poll_delay_millis: 1000
      poll_delay_multiplier: 2
      max_poll_delay_millis: 10000
      total_poll_timeout_millis: 900000
  - name: DeleteCluster
    long_running:
      initial_poll_delay_millis: 1000
      poll_delay_multiplier: 2
      max_poll_delay_millis: 10000
      total_poll_timeout_millis: 900000
  - name: DiagnoseCluster
    long_running:
      initial_poll_delay_millis: 1000
      poll_delay_multiplier: 2
      max_poll_delay_millis: 10000
      total_poll_timeout_millis: 30000
- name: google.cloud.dataproc.v1.WorkflowTemplateService
  methods:
  - name: InstantiateWorkflowTemplate
    long_running:
      initial_poll_delay_millis: 1000
      poll_delay_multiplier: 2
      max_poll_delay_millis: 10000
      total_poll_timeout_millis: 43200000
  - name: InstantiateInlineWorkflowTemplate
    long_running:
      initial_poll_delay_millis: 1000
      poll_delay_multiplier: 2
      max_poll_delay_millis: 10000
      total_poll_timeout_millis: 43200000
