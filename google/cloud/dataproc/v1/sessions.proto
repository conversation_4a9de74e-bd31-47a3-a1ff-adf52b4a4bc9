// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataproc.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dataproc/v1/shared.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/dataproc/v2/apiv1/dataprocpb;dataprocpb";
option java_multiple_files = true;
option java_outer_classname = "SessionsProto";
option java_package = "com.google.cloud.dataproc.v1";

// The `SessionController` provides methods to manage interactive sessions.
service SessionController {
  option (google.api.default_host) = "dataproc.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create an interactive session asynchronously.
  rpc CreateSession(CreateSessionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/sessions"
      body: "session"
    };
    option (google.api.method_signature) = "parent,session,session_id";
    option (google.longrunning.operation_info) = {
      response_type: "Session"
      metadata_type: "google.cloud.dataproc.v1.SessionOperationMetadata"
    };
  }

  // Gets the resource representation for an interactive session.
  rpc GetSession(GetSessionRequest) returns (Session) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sessions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists interactive sessions.
  rpc ListSessions(ListSessionsRequest) returns (ListSessionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/sessions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Terminates the interactive session.
  rpc TerminateSession(TerminateSessionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/sessions/*}:terminate"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Session"
      metadata_type: "google.cloud.dataproc.v1.SessionOperationMetadata"
    };
  }

  // Deletes the interactive session resource. If the session is not in terminal
  // state, it is terminated, and then deleted.
  rpc DeleteSession(DeleteSessionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/sessions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Session"
      metadata_type: "google.cloud.dataproc.v1.SessionOperationMetadata"
    };
  }
}

// A request to create a session.
message CreateSessionRequest {
  // Required. The parent resource where this session will be created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dataproc.googleapis.com/Session"
    }
  ];

  // Required. The interactive session to create.
  Session session = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the session, which becomes the final component
  // of the session's resource name.
  //
  // This value must be 4-63 characters. Valid characters
  // are /[a-z][0-9]-/.
  string session_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A unique ID used to identify the request. If the service
  // receives two
  // [CreateSessionRequests](https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.CreateSessionRequest)s
  // with the same ID, the second request is ignored, and the
  // first [Session][google.cloud.dataproc.v1.Session] is created and stored in
  // the backend.
  //
  // Recommendation: Set this value to a
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier).
  //
  // The value must contain only letters (a-z, A-Z), numbers (0-9),
  // underscores (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to get the resource representation for a session.
message GetSessionRequest {
  // Required. The name of the session to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataproc.googleapis.com/Session"
    }
  ];
}

// A request to list sessions in a project.
message ListSessionsRequest {
  // Required. The parent, which owns this collection of sessions.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dataproc.googleapis.com/Session"
    }
  ];

  // Optional. The maximum number of sessions to return in each response.
  // The service may return fewer than this value.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token received from a previous `ListSessions` call.
  // Provide this token to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter for the sessions to return in the response.
  //
  // A filter is a logical expression constraining the values of various fields
  // in each session resource. Filters are case sensitive, and may contain
  // multiple clauses combined with logical operators (AND, OR).
  // Supported fields are `session_id`, `session_uuid`, `state`, `create_time`,
  // and `labels`.
  //
  // Example: `state = ACTIVE and create_time < "2023-01-01T00:00:00Z"`
  // is a filter for sessions in an ACTIVE state that were created before
  // 2023-01-01. `state = ACTIVE and labels.environment=production` is a filter
  // for sessions in an ACTIVE state that have a production environment label.
  //
  // See https://google.aip.dev/assets/misc/ebnf-filtering.txt for a detailed
  // description of the filter syntax and a list of supported comparators.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A list of interactive sessions.
message ListSessionsResponse {
  // Output only. The sessions from the specified collection.
  repeated Session sessions = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A token, which can be sent as `page_token`, to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// A request to terminate an interactive session.
message TerminateSessionRequest {
  // Required. The name of the session resource to terminate.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataproc.googleapis.com/Session"
    }
  ];

  // Optional. A unique ID used to identify the request. If the service
  // receives two
  // [TerminateSessionRequest](https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.TerminateSessionRequest)s
  // with the same ID, the second request is ignored.
  //
  // Recommendation: Set this value to a
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier).
  //
  // The value must contain only letters (a-z, A-Z), numbers (0-9),
  // underscores (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete a session.
message DeleteSessionRequest {
  // Required. The name of the session resource to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataproc.googleapis.com/Session"
    }
  ];

  // Optional. A unique ID used to identify the request. If the service
  // receives two
  // [DeleteSessionRequest](https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.DeleteSessionRequest)s
  // with the same ID, the second request is ignored.
  //
  // Recommendation: Set this value to a
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier).
  //
  // The value must contain only letters (a-z, A-Z), numbers (0-9),
  // underscores (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A representation of a session.
message Session {
  option (google.api.resource) = {
    type: "dataproc.googleapis.com/Session"
    pattern: "projects/{project}/locations/{location}/sessions/{session}"
  };

  // The session state.
  enum State {
    // The session state is unknown.
    STATE_UNSPECIFIED = 0;

    // The session is created prior to running.
    CREATING = 1;

    // The session is running.
    ACTIVE = 2;

    // The session is terminating.
    TERMINATING = 3;

    // The session is terminated successfully.
    TERMINATED = 4;

    // The session is no longer running due to an error.
    FAILED = 5;
  }

  // Historical state information.
  message SessionStateHistory {
    // Output only. The state of the session at this point in the session
    // history.
    State state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Details about the state at this point in the session
    // history.
    string state_message = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time when the session entered the historical state.
    google.protobuf.Timestamp state_start_time = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Required. The resource name of the session.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. A session UUID (Unique Universal Identifier). The service
  // generates this value when it creates the session.
  string uuid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the session was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The session configuration.
  oneof session_config {
    // Optional. Jupyter session config.
    JupyterConfig jupyter_session = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Spark Connect session config.
    SparkConnectConfig spark_connect_session = 17
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. Runtime information about session execution.
  RuntimeInfo runtime_info = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A state of the session.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Session state details, such as the failure
  // description if the state is `FAILED`.
  string state_message = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the session entered the current state.
  google.protobuf.Timestamp state_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The email address of the user who created the session.
  string creator = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The labels to associate with the session.
  // Label **keys** must contain 1 to 63 characters, and must conform to
  // [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt).
  // Label **values** may be empty, but, if present, must contain 1 to 63
  // characters, and must conform to [RFC
  // 1035](https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be
  // associated with a session.
  map<string, string> labels = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Runtime configuration for the session execution.
  RuntimeConfig runtime_config = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Environment configuration for the session execution.
  EnvironmentConfig environment_config = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The email address of the user who owns the session.
  string user = 14 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Historical state information for the session.
  repeated SessionStateHistory state_history = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The session template used by the session.
  //
  // Only resource names, including project ID and location, are valid.
  //
  // Example:
  // * `https://www.googleapis.com/compute/v1/projects/[project_id]/locations/[dataproc_region]/sessionTemplates/[template_id]`
  // * `projects/[project_id]/locations/[dataproc_region]/sessionTemplates/[template_id]`
  //
  // The template must be in the same project and Dataproc region as the
  // session.
  string session_template = 16 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dataproc.googleapis.com/SessionTemplate"
    }
  ];
}

// Jupyter configuration for an interactive session.
message JupyterConfig {
  // Jupyter kernel types.
  enum Kernel {
    // The kernel is unknown.
    KERNEL_UNSPECIFIED = 0;

    // Python kernel.
    PYTHON = 1;

    // Scala kernel.
    SCALA = 2;
  }

  // Optional. Kernel
  Kernel kernel = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Display name, shown in the Jupyter kernelspec card.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Spark Connect configuration for an interactive session.
message SparkConnectConfig {}
