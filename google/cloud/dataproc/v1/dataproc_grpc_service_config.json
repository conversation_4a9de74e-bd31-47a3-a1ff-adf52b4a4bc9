{"methodConfig": [{"name": [{"service": "google.cloud.dataproc.v1.AutoscalingPolicyService", "method": "CreateAutoscalingPolicy"}, {"service": "google.cloud.dataproc.v1.AutoscalingPolicyService", "method": "DeleteAutoscalingPolicy"}], "timeout": "600s"}, {"name": [{"service": "google.cloud.dataproc.v1.AutoscalingPolicyService", "method": "UpdateAutoscalingPolicy"}, {"service": "google.cloud.dataproc.v1.AutoscalingPolicyService", "method": "GetAutoscalingPolicy"}, {"service": "google.cloud.dataproc.v1.AutoscalingPolicyService", "method": "ListAutoscalingPolicies"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataproc.v1.JobController", "method": "Get<PERSON>ob"}, {"service": "google.cloud.dataproc.v1.JobController", "method": "ListJobs"}, {"service": "google.cloud.dataproc.v1.JobController", "method": "<PERSON><PERSON><PERSON><PERSON>"}], "timeout": "900s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "INTERNAL", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataproc.v1.WorkflowTemplateService", "method": "CreateWorkflowTemplate"}, {"service": "google.cloud.dataproc.v1.WorkflowTemplateService", "method": "InstantiateWorkflowTemplate"}, {"service": "google.cloud.dataproc.v1.WorkflowTemplateService", "method": "InstantiateInlineWorkflowTemplate"}, {"service": "google.cloud.dataproc.v1.WorkflowTemplateService", "method": "UpdateWorkflowTemplate"}, {"service": "google.cloud.dataproc.v1.WorkflowTemplateService", "method": "DeleteWorkflowTemplate"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataproc.v1.WorkflowTemplateService", "method": "GetWorkflowTemplate"}, {"service": "google.cloud.dataproc.v1.WorkflowTemplateService", "method": "ListWorkflowTemplates"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "INTERNAL", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataproc.v1.ClusterController", "method": "CreateCluster"}, {"service": "google.cloud.dataproc.v1.ClusterController", "method": "UpdateCluster"}, {"service": "google.cloud.dataproc.v1.ClusterController", "method": "DeleteCluster"}, {"service": "google.cloud.dataproc.v1.ClusterController", "method": "DiagnoseCluster"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataproc.v1.ClusterController", "method": "GetCluster"}, {"service": "google.cloud.dataproc.v1.ClusterController", "method": "ListClusters"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["INTERNAL", "DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.dataproc.v1.JobController", "method": "SubmitJob"}, {"service": "google.cloud.dataproc.v1.JobController", "method": "SubmitJobAsOperation"}, {"service": "google.cloud.dataproc.v1.JobController", "method": "Update<PERSON><PERSON>"}, {"service": "google.cloud.dataproc.v1.JobController", "method": "DeleteJob"}], "timeout": "900s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}