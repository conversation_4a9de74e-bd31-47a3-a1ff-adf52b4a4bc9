// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataproc.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dataproc/v1/sessions.proto";
import "google/cloud/dataproc/v1/shared.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/dataproc/v2/apiv1/dataprocpb;dataprocpb";
option java_multiple_files = true;
option java_outer_classname = "SessionTemplatesProto";
option java_package = "com.google.cloud.dataproc.v1";

// The SessionTemplateController provides methods to manage session templates.
service SessionTemplateController {
  option (google.api.default_host) = "dataproc.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create a session template synchronously.
  rpc CreateSessionTemplate(CreateSessionTemplateRequest)
      returns (SessionTemplate) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/sessionTemplates"
      body: "session_template"
    };
    option (google.api.method_signature) = "parent,session_template";
  }

  // Updates the session template synchronously.
  rpc UpdateSessionTemplate(UpdateSessionTemplateRequest)
      returns (SessionTemplate) {
    option (google.api.http) = {
      patch: "/v1/{session_template.name=projects/*/locations/*/sessionTemplates/*}"
      body: "session_template"
    };
    option (google.api.method_signature) = "session_template";
  }

  // Gets the resource representation for a session template.
  rpc GetSessionTemplate(GetSessionTemplateRequest) returns (SessionTemplate) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sessionTemplates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists session templates.
  rpc ListSessionTemplates(ListSessionTemplatesRequest)
      returns (ListSessionTemplatesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/sessionTemplates"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a session template.
  rpc DeleteSessionTemplate(DeleteSessionTemplateRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/sessionTemplates/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// A request to create a session template.
message CreateSessionTemplateRequest {
  // Required. The parent resource where this session template will be created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dataproc.googleapis.com/SessionTemplate"
    }
  ];

  // Required. The session template to create.
  SessionTemplate session_template = 3 [(google.api.field_behavior) = REQUIRED];
}

// A request to update a session template.
message UpdateSessionTemplateRequest {
  // Required. The updated session template.
  SessionTemplate session_template = 1 [(google.api.field_behavior) = REQUIRED];
}

// A request to get the resource representation for a session template.
message GetSessionTemplateRequest {
  // Required. The name of the session template to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataproc.googleapis.com/SessionTemplate"
    }
  ];
}

// A request to list session templates in a project.
message ListSessionTemplatesRequest {
  // Required. The parent that owns this collection of session templates.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dataproc.googleapis.com/SessionTemplate"
    }
  ];

  // Optional. The maximum number of sessions to return in each response.
  // The service may return fewer than this value.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token received from a previous `ListSessions` call.
  // Provide this token to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter for the session templates to return in the response.
  // Filters are case sensitive and have the following syntax:
  //
  // [field = value] AND [field [= value]] ...
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A list of session templates.
message ListSessionTemplatesResponse {
  // Output only. Session template list
  repeated SessionTemplate session_templates = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// A request to delete a session template.
message DeleteSessionTemplateRequest {
  // Required. The name of the session template resource to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataproc.googleapis.com/SessionTemplate"
    }
  ];
}

// A representation of a session template.
message SessionTemplate {
  option (google.api.resource) = {
    type: "dataproc.googleapis.com/SessionTemplate"
    pattern: "projects/{project}/locations/{location}/sessionTemplates/{template}"
  };

  // Required. The resource name of the session template.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Brief description of the template.
  string description = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time when the template was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The session configuration.
  oneof session_config {
    // Optional. Jupyter session config.
    JupyterConfig jupyter_session = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Spark Connect session config.
    SparkConnectConfig spark_connect_session = 11
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. The email address of the user who created the template.
  string creator = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels to associate with sessions created using this template.
  // Label **keys** must contain 1 to 63 characters, and must conform to
  // [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt).
  // Label **values** can be empty, but, if present, must contain 1 to 63
  // characters and conform to [RFC
  // 1035](https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be
  // associated with a session.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Runtime configuration for session execution.
  RuntimeConfig runtime_config = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Environment configuration for session execution.
  EnvironmentConfig environment_config = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time the template was last updated.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A session template UUID (Unique Universal Identifier). The
  // service generates this value when it creates the session template.
  string uuid = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}
