# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "dataproc_proto",
    srcs = [
        "autoscaling_policies.proto",
        "batches.proto",
        "clusters.proto",
        "jobs.proto",
        "node_groups.proto",
        "operations.proto",
        "session_templates.proto",
        "sessions.proto",
        "shared.proto",
        "workflow_templates.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:interval_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "dataproc_proto_with_info",
    deps = [
        ":dataproc_proto",
        "//google/cloud:common_resources_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "dataproc_java_proto",
    deps = [":dataproc_proto"],
)

java_grpc_library(
    name = "dataproc_java_grpc",
    srcs = [":dataproc_proto"],
    deps = [":dataproc_java_proto"],
)

java_gapic_library(
    name = "dataproc_java_gapic",
    srcs = [":dataproc_proto_with_info"],
    gapic_yaml = "dataproc_gapic.yaml",
    grpc_service_config = "dataproc_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataproc_v1.yaml",
    test_deps = [
        "//google/iam/v1:iam_java_grpc",
        ":dataproc_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":dataproc_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "dataproc_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.dataproc.v1.AutoscalingPolicyServiceClientHttpJsonTest",
        "com.google.cloud.dataproc.v1.AutoscalingPolicyServiceClientTest",
        "com.google.cloud.dataproc.v1.BatchControllerClientHttpJsonTest",
        "com.google.cloud.dataproc.v1.BatchControllerClientTest",
        "com.google.cloud.dataproc.v1.ClusterControllerClientHttpJsonTest",
        "com.google.cloud.dataproc.v1.ClusterControllerClientTest",
        "com.google.cloud.dataproc.v1.JobControllerClientHttpJsonTest",
        "com.google.cloud.dataproc.v1.JobControllerClientTest",
        "com.google.cloud.dataproc.v1.NodeGroupControllerClientHttpJsonTest",
        "com.google.cloud.dataproc.v1.NodeGroupControllerClientTest",
        "com.google.cloud.dataproc.v1.WorkflowTemplateServiceClientHttpJsonTest",
        "com.google.cloud.dataproc.v1.WorkflowTemplateServiceClientTest",
    ],
    runtime_deps = [":dataproc_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-dataproc-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":dataproc_java_gapic",
        ":dataproc_java_grpc",
        ":dataproc_java_proto",
        ":dataproc_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "dataproc_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/dataproc/v2/apiv1/dataprocpb",
    protos = [":dataproc_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:interval_go_proto",
    ],
)

go_gapic_library(
    name = "dataproc_go_gapic",
    srcs = [":dataproc_proto_with_info"],
    grpc_service_config = "dataproc_grpc_service_config.json",
    importpath = "cloud.google.com/go/dataproc/v2/apiv1;dataproc",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "dataproc_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataproc_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-dataproc-v1-go",
    deps = [
        ":dataproc_go_gapic",
        ":dataproc_go_gapic_srcjar-metadata.srcjar",
        ":dataproc_go_gapic_srcjar-snippets.srcjar",
        ":dataproc_go_gapic_srcjar-test.srcjar",
        ":dataproc_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "dataproc_py_gapic",
    srcs = [":dataproc_proto"],
    grpc_service_config = "dataproc_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataproc_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "dataproc_py_gapic_test",
    srcs = [
        "dataproc_py_gapic_pytest.py",
        "dataproc_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":dataproc_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "dataproc-v1-py",
    deps = [
        ":dataproc_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "dataproc_php_proto",
    deps = [":dataproc_proto"],
)

php_gapic_library(
    name = "dataproc_php_gapic",
    srcs = [":dataproc_proto_with_info"],
    grpc_service_config = "dataproc_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "dataproc_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataproc_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-dataproc-v1-php",
    deps = [
        ":dataproc_php_gapic",
        ":dataproc_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "dataproc_nodejs_gapic",
    package_name = "@google-cloud/dataproc",
    src = ":dataproc_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "dataproc_grpc_service_config.json",
    main_service = "dataproc",
    package = "google.cloud.dataproc.v1",
    rest_numeric_enums = True,
    service_yaml = "dataproc_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "dataproc-v1-nodejs",
    deps = [
        ":dataproc_nodejs_gapic",
        ":dataproc_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "dataproc_ruby_proto",
    deps = [":dataproc_proto"],
)

ruby_grpc_library(
    name = "dataproc_ruby_grpc",
    srcs = [":dataproc_proto"],
    deps = [":dataproc_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "dataproc_ruby_gapic",
    srcs = [":dataproc_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=dataproc.googleapis.com",
        "ruby-cloud-api-shortname=dataproc",
        "ruby-cloud-env-prefix=DATAPROC",
        "ruby-cloud-gem-name=google-cloud-dataproc-v1",
        "ruby-cloud-product-url=https://cloud.google.com/dataproc",
    ],
    grpc_service_config = "dataproc_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Manages Hadoop-based clusters and jobs on Google Cloud Platform.",
    ruby_cloud_title = "Cloud Dataproc V1",
    service_yaml = "dataproc_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataproc_ruby_grpc",
        ":dataproc_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataproc-v1-ruby",
    deps = [
        ":dataproc_ruby_gapic",
        ":dataproc_ruby_grpc",
        ":dataproc_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "dataproc_csharp_proto",
    deps = [":dataproc_proto"],
)

csharp_grpc_library(
    name = "dataproc_csharp_grpc",
    srcs = [":dataproc_proto"],
    deps = [":dataproc_csharp_proto"],
)

csharp_gapic_library(
    name = "dataproc_csharp_gapic",
    srcs = [":dataproc_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "dataproc_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataproc_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataproc_csharp_grpc",
        ":dataproc_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-dataproc-v1-csharp",
    deps = [
        ":dataproc_csharp_gapic",
        ":dataproc_csharp_grpc",
        ":dataproc_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "dataproc_cc_proto",
    deps = [":dataproc_proto"],
)

cc_grpc_library(
    name = "dataproc_cc_grpc",
    srcs = [":dataproc_proto"],
    grpc_only = True,
    deps = [":dataproc_cc_proto"],
)
