// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataproc.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/dataproc/v2/apiv1/dataprocpb;dataprocpb";
option java_multiple_files = true;
option java_outer_classname = "OperationsProto";
option java_package = "com.google.cloud.dataproc.v1";

// Metadata describing the Batch operation.
message BatchOperationMetadata {
  // Operation type for Batch resources
  enum BatchOperationType {
    // Batch operation type is unknown.
    BATCH_OPERATION_TYPE_UNSPECIFIED = 0;

    // Batch operation type.
    BATCH = 1;
  }

  // Name of the batch for the operation.
  string batch = 1;

  // Batch UUID for the operation.
  string batch_uuid = 2;

  // The time when the operation was created.
  google.protobuf.Timestamp create_time = 3;

  // The time when the operation finished.
  google.protobuf.Timestamp done_time = 4;

  // The operation type.
  BatchOperationType operation_type = 6;

  // Short description of the operation.
  string description = 7;

  // Labels associated with the operation.
  map<string, string> labels = 8;

  // Warnings encountered during operation execution.
  repeated string warnings = 9;
}

// Metadata describing the Session operation.
message SessionOperationMetadata {
  // Operation type for Session resources
  enum SessionOperationType {
    // Session operation type is unknown.
    SESSION_OPERATION_TYPE_UNSPECIFIED = 0;

    // Create Session operation type.
    CREATE = 1;

    // Terminate Session operation type.
    TERMINATE = 2;

    // Delete Session operation type.
    DELETE = 3;
  }

  // Name of the session for the operation.
  string session = 1;

  // Session UUID for the operation.
  string session_uuid = 2;

  // The time when the operation was created.
  google.protobuf.Timestamp create_time = 3;

  // The time when the operation was finished.
  google.protobuf.Timestamp done_time = 4;

  // The operation type.
  SessionOperationType operation_type = 6;

  // Short description of the operation.
  string description = 7;

  // Labels associated with the operation.
  map<string, string> labels = 8;

  // Warnings encountered during operation execution.
  repeated string warnings = 9;
}

// The status of the operation.
message ClusterOperationStatus {
  // The operation state.
  enum State {
    // Unused.
    UNKNOWN = 0;

    // The operation has been created.
    PENDING = 1;

    // The operation is running.
    RUNNING = 2;

    // The operation is done; either cancelled or completed.
    DONE = 3;
  }

  // Output only. A message containing the operation state.
  State state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A message containing the detailed operation state.
  string inner_state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A message containing any operation metadata details.
  string details = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this state was entered.
  google.protobuf.Timestamp state_start_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata describing the operation.
message ClusterOperationMetadata {
  // Output only. Name of the cluster for the operation.
  string cluster_name = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cluster UUID for the operation.
  string cluster_uuid = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current operation status.
  ClusterOperationStatus status = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The previous operation status.
  repeated ClusterOperationStatus status_history = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The operation type.
  string operation_type = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Short description of operation.
  string description = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Labels associated with the operation
  map<string, string> labels = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Errors encountered during operation execution.
  repeated string warnings = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Child operation ids
  repeated string child_operation_ids = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata describing the node group operation.
message NodeGroupOperationMetadata {
  // Operation type for node group resources.
  enum NodeGroupOperationType {
    // Node group operation type is unknown.
    NODE_GROUP_OPERATION_TYPE_UNSPECIFIED = 0;

    // Create node group operation type.
    CREATE = 1;

    // Update node group operation type.
    UPDATE = 2;

    // Delete node group operation type.
    DELETE = 3;

    // Resize node group operation type.
    RESIZE = 4;
  }

  // Output only. Node group ID for the operation.
  string node_group_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cluster UUID associated with the node group operation.
  string cluster_uuid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current operation status.
  ClusterOperationStatus status = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The previous operation status.
  repeated ClusterOperationStatus status_history = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The operation type.
  NodeGroupOperationType operation_type = 5;

  // Output only. Short description of operation.
  string description = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Labels associated with the operation.
  map<string, string> labels = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Errors encountered during operation execution.
  repeated string warnings = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}
