// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataproc.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dataproc/v1/clusters.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";

option go_package = "cloud.google.com/go/dataproc/v2/apiv1/dataprocpb;dataprocpb";
option java_multiple_files = true;
option java_outer_classname = "NodeGroupsProto";
option java_package = "com.google.cloud.dataproc.v1";
option (google.api.resource_definition) = {
  type: "dataproc.googleapis.com/ClusterRegion"
  pattern: "projects/{project}/regions/{region}/clusters/{cluster}"
};

// The `NodeGroupControllerService` provides methods to manage node groups
// of Compute Engine managed instances.
service NodeGroupController {
  option (google.api.default_host) = "dataproc.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a node group in a cluster. The returned
  // [Operation.metadata][google.longrunning.Operation.metadata] is
  // [NodeGroupOperationMetadata](https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#nodegroupoperationmetadata).
  rpc CreateNodeGroup(CreateNodeGroupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/regions/*/clusters/*}/nodeGroups"
      body: "node_group"
    };
    option (google.api.method_signature) = "parent,node_group,node_group_id";
    option (google.longrunning.operation_info) = {
      response_type: "NodeGroup"
      metadata_type: "google.cloud.dataproc.v1.NodeGroupOperationMetadata"
    };
  }

  // Resizes a node group in a cluster. The returned
  // [Operation.metadata][google.longrunning.Operation.metadata] is
  // [NodeGroupOperationMetadata](https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#nodegroupoperationmetadata).
  rpc ResizeNodeGroup(ResizeNodeGroupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/regions/*/clusters/*/nodeGroups/*}:resize"
      body: "*"
    };
    option (google.api.method_signature) = "name,size";
    option (google.longrunning.operation_info) = {
      response_type: "NodeGroup"
      metadata_type: "google.cloud.dataproc.v1.NodeGroupOperationMetadata"
    };
  }

  // Gets the resource representation for a node group in a
  // cluster.
  rpc GetNodeGroup(GetNodeGroupRequest) returns (NodeGroup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/regions/*/clusters/*/nodeGroups/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// A request to create a node group.
message CreateNodeGroupRequest {
  // Required. The parent resource where this node group will be created.
  // Format: `projects/{project}/regions/{region}/clusters/{cluster}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dataproc.googleapis.com/NodeGroup"
    }
  ];

  // Required. The node group to create.
  NodeGroup node_group = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional node group ID. Generated if not specified.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9),
  // underscores (_), and hyphens (-). Cannot begin or end with underscore
  // or hyphen. Must consist of from 3 to 33 characters.
  string node_group_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A unique ID used to identify the request. If the server receives
  // two
  // [CreateNodeGroupRequest](https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.CreateNodeGroupRequests)
  // with the same ID, the second request is ignored and the
  // first [google.longrunning.Operation][google.longrunning.Operation] created
  // and stored in the backend is returned.
  //
  // Recommendation: Set this value to a
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier).
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9),
  // underscores (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to resize a node group.
message ResizeNodeGroupRequest {
  // Required. The name of the node group to resize.
  // Format:
  // `projects/{project}/regions/{region}/clusters/{cluster}/nodeGroups/{nodeGroup}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The number of running instances for the node group to maintain.
  // The group adds or removes instances to maintain the number of instances
  // specified by this parameter.
  int32 size = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A unique ID used to identify the request. If the server receives
  // two
  // [ResizeNodeGroupRequest](https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.ResizeNodeGroupRequests)
  // with the same ID, the second request is ignored and the
  // first [google.longrunning.Operation][google.longrunning.Operation] created
  // and stored in the backend is returned.
  //
  // Recommendation: Set this value to a
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier).
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9),
  // underscores (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Timeout for graceful YARN decommissioning. [Graceful
  // decommissioning]
  // (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/scaling-clusters#graceful_decommissioning)
  // allows the removal of nodes from the Compute Engine node group
  // without interrupting jobs in progress. This timeout specifies how long to
  // wait for jobs in progress to finish before forcefully removing nodes (and
  // potentially interrupting jobs). Default timeout is 0 (for forceful
  // decommission), and the maximum allowed timeout is 1 day. (see JSON
  // representation of
  // [Duration](https://developers.google.com/protocol-buffers/docs/proto3#json)).
  //
  // Only supported on Dataproc image versions 1.2 and higher.
  google.protobuf.Duration graceful_decommission_timeout = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// A request to get a node group .
message GetNodeGroupRequest {
  // Required. The name of the node group to retrieve.
  // Format:
  // `projects/{project}/regions/{region}/clusters/{cluster}/nodeGroups/{nodeGroup}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataproc.googleapis.com/NodeGroup"
    }
  ];
}
