# This build file includes a target for the Ruby wrapper library for
# google-cloud-dataproc.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for dataproc.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "dataproc_ruby_wrapper",
    srcs = ["//google/cloud/dataproc/v1:dataproc_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-dataproc",
        "ruby-cloud-env-prefix=DATAPROC",
        "ruby-cloud-wrapper-of=v1:0.24",
        "ruby-cloud-product-url=https://cloud.google.com/dataproc",
        "ruby-cloud-api-id=dataproc.googleapis.com",
        "ruby-cloud-api-shortname=dataproc",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Manages Hadoop-based clusters and jobs on Google Cloud Platform.",
    ruby_cloud_title = "Cloud Dataproc",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataproc-ruby",
    deps = [
        ":dataproc_ruby_wrapper",
    ],
)
