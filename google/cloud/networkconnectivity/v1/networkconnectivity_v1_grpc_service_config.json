{"methodConfig": [{"name": [{"service": "google.cloud.networkconnectivity.v1.HubService"}, {"service": "google.cloud.networkconnectivity.v1.PolicyBasedRoutingService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.networkconnectivity.v1.HubService", "method": "CreateHub"}, {"service": "google.cloud.networkconnectivity.v1.HubService", "method": "UpdateHub"}, {"service": "google.cloud.networkconnectivity.v1.HubService", "method": "DeleteHub"}, {"service": "google.cloud.networkconnectivity.v1.HubService", "method": "CreateSpoke"}, {"service": "google.cloud.networkconnectivity.v1.HubService", "method": "UpdateSpoke"}, {"service": "google.cloud.networkconnectivity.v1.HubService", "method": "DeactivateSpoke"}, {"service": "google.cloud.networkconnectivity.v1.HubService", "method": "ActivateSpoke"}, {"service": "google.cloud.networkconnectivity.v1.HubService", "method": "DeleteSpoke"}, {"service": "google.cloud.networkconnectivity.v1.PolicyBasedRoutingService", "method": "CreatePolicyBasedRoute"}, {"service": "google.cloud.networkconnectivity.v1.PolicyBasedRoutingService", "method": "DeletePolicyBasedRoute"}], "timeout": "60s"}]}