type: google.api.Service
config_version: 3
name: networkconnectivity.googleapis.com
title: Network Connectivity API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.networkconnectivity.v1.HubService
- name: google.cloud.networkconnectivity.v1.PolicyBasedRoutingService
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.networkconnectivity.v1.AcceptHubSpokeResponse
- name: google.cloud.networkconnectivity.v1.LocationMetadata
- name: google.cloud.networkconnectivity.v1.OperationMetadata
- name: google.cloud.networkconnectivity.v1.RejectHubSpokeResponse

documentation:
  summary: This API enables connectivity with and between Google Cloud resources.
  overview: This API enables connectivity with and between Google Cloud resources.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1/{resource=projects/*/locations/global/hubs/*}:getIamPolicy'
    additional_bindings:
    - get: '/v1/{resource=projects/*/locations/global/hubs/*/groups/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/spokes/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/global/policyBasedRoutes/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1/{resource=projects/*/locations/global/hubs/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/global/hubs/*/groups/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/spokes/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/global/policyBasedRoutes/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1/{resource=projects/*/locations/global/hubs/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/global/hubs/*/groups/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/spokes/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/global/policyBasedRoutes/*}:testIamPermissions'
      body: '*'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.networkconnectivity.v1.HubService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.networkconnectivity.v1.PolicyBasedRoutingService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
