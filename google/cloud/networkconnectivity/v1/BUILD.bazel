# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "networkconnectivity_proto",
    srcs = [
        "common.proto",
        "hub.proto",
        "policy_based_routing.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "networkconnectivity_proto_with_info",
    deps = [
        ":networkconnectivity_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "networkconnectivity_java_proto",
    deps = [":networkconnectivity_proto"],
)

java_grpc_library(
    name = "networkconnectivity_java_grpc",
    srcs = [":networkconnectivity_proto"],
    deps = [":networkconnectivity_java_proto"],
)

java_gapic_library(
    name = "networkconnectivity_java_gapic",
    srcs = [":networkconnectivity_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "networkconnectivity_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networkconnectivity_v1.yaml",
    test_deps = [
        ":networkconnectivity_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":networkconnectivity_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "networkconnectivity_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.networkconnectivity.v1.HubServiceClientTest",
        "com.google.cloud.networkconnectivity.v1.PolicyBasedRoutingServiceClientTest",
    ],
    runtime_deps = [":networkconnectivity_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-networkconnectivity-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":networkconnectivity_java_gapic",
        ":networkconnectivity_java_grpc",
        ":networkconnectivity_java_proto",
        ":networkconnectivity_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "networkconnectivity_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/networkconnectivity/apiv1/networkconnectivitypb",
    protos = [":networkconnectivity_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "networkconnectivity_go_gapic",
    srcs = [":networkconnectivity_proto_with_info"],
    grpc_service_config = "networkconnectivity_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/networkconnectivity/apiv1;networkconnectivity",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "networkconnectivity_v1.yaml",
    transport = "grpc",
    deps = [
        ":networkconnectivity_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-networkconnectivity-v1-go",
    deps = [
        ":networkconnectivity_go_gapic",
        ":networkconnectivity_go_gapic_srcjar-metadata.srcjar",
        ":networkconnectivity_go_gapic_srcjar-snippets.srcjar",
        ":networkconnectivity_go_gapic_srcjar-test.srcjar",
        ":networkconnectivity_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "networkconnectivity_py_gapic",
    srcs = [":networkconnectivity_proto"],
    grpc_service_config = "networkconnectivity_v1_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-network-connectivity"],
    rest_numeric_enums = True,
    service_yaml = "networkconnectivity_v1.yaml",
    transport = "grpc",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "networkconnectivity_py_gapic_test",
    srcs = [
        "networkconnectivity_py_gapic_pytest.py",
        "networkconnectivity_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":networkconnectivity_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "networkconnectivity-v1-py",
    deps = [
        ":networkconnectivity_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "networkconnectivity_php_proto",
    deps = [":networkconnectivity_proto"],
)

php_gapic_library(
    name = "networkconnectivity_php_gapic",
    srcs = [":networkconnectivity_proto_with_info"],
    grpc_service_config = "networkconnectivity_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "networkconnectivity_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networkconnectivity_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-networkconnectivity-v1-php",
    deps = [
        ":networkconnectivity_php_gapic",
        ":networkconnectivity_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "networkconnectivity_nodejs_gapic",
    package_name = "@google-cloud/network-connectivity",
    src = ":networkconnectivity_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "networkconnectivity_v1_grpc_service_config.json",
    package = "google.cloud.networkconnectivity.v1",
    rest_numeric_enums = True,
    service_yaml = "networkconnectivity_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "networkconnectivity-v1-nodejs",
    deps = [
        ":networkconnectivity_nodejs_gapic",
        ":networkconnectivity_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "networkconnectivity_ruby_proto",
    deps = [":networkconnectivity_proto"],
)

ruby_grpc_library(
    name = "networkconnectivity_ruby_grpc",
    srcs = [":networkconnectivity_proto"],
    deps = [":networkconnectivity_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "networkconnectivity_ruby_gapic",
    srcs = [":networkconnectivity_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=networkconnectivity.googleapis.com",
        "ruby-cloud-api-shortname=networkconnectivity",
        "ruby-cloud-env-prefix=NETWORK_CONNECTIVITY",
        "ruby-cloud-gem-name=google-cloud-network_connectivity-v1",
        "ruby-cloud-product-url=https://cloud.google.com/network-connectivity/docs",
    ],
    grpc_service_config = "networkconnectivity_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Network Connectivity is Google's suite of products that provide enterprise connectivity from your on-premises network or from another cloud provider to your Virtual Private Cloud (VPC) network.",
    ruby_cloud_title = "Network Connectivity V1",
    service_yaml = "networkconnectivity_v1.yaml",
    transport = "grpc",
    deps = [
        ":networkconnectivity_ruby_grpc",
        ":networkconnectivity_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-networkconnectivity-v1-ruby",
    deps = [
        ":networkconnectivity_ruby_gapic",
        ":networkconnectivity_ruby_grpc",
        ":networkconnectivity_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "networkconnectivity_csharp_proto",
    extra_opts = [],
    deps = [":networkconnectivity_proto"],
)

csharp_grpc_library(
    name = "networkconnectivity_csharp_grpc",
    srcs = [":networkconnectivity_proto"],
    deps = [":networkconnectivity_csharp_proto"],
)

csharp_gapic_library(
    name = "networkconnectivity_csharp_gapic",
    srcs = [":networkconnectivity_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "networkconnectivity_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networkconnectivity_v1.yaml",
    transport = "grpc",
    deps = [
        ":networkconnectivity_csharp_grpc",
        ":networkconnectivity_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-networkconnectivity-v1-csharp",
    deps = [
        ":networkconnectivity_csharp_gapic",
        ":networkconnectivity_csharp_grpc",
        ":networkconnectivity_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "networkconnectivity_cc_proto",
    deps = [":networkconnectivity_proto"],
)

cc_grpc_library(
    name = "networkconnectivity_cc_grpc",
    srcs = [":networkconnectivity_proto"],
    grpc_only = True,
    deps = [":networkconnectivity_cc_proto"],
)
