# This build file includes a target for the Ruby wrapper library for
# google-cloud-network_connectivity.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for networkconnectivity.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "networkconnectivity_ruby_wrapper",
    srcs = ["//google/cloud/networkconnectivity/v1:networkconnectivity_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-network_connectivity",
        "ruby-cloud-env-prefix=NETWORK_CONNECTIVITY",
        "ruby-cloud-wrapper-of=v1:0.9;v1alpha1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/network-connectivity/docs",
        "ruby-cloud-api-id=networkconnectivity.googleapis.com",
        "ruby-cloud-api-shortname=networkconnectivity",
    ],
    ruby_cloud_description = "Network Connectivity is Google's suite of products that provide enterprise connectivity from your on-premises network or from another cloud provider to your Virtual Private Cloud (VPC) network.",
    ruby_cloud_title = "Network Connectivity",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-networkconnectivity-ruby",
    deps = [
        ":networkconnectivity_ruby_wrapper",
    ],
)
