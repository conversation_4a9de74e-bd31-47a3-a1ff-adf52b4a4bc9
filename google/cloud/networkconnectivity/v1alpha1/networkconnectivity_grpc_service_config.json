{"methodConfig": [{"name": [{"service": "google.cloud.networkconnectivity.v1alpha1.HubService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.networkconnectivity.v1alpha1.HubService", "method": "CreateHub"}, {"service": "google.cloud.networkconnectivity.v1alpha1.HubService", "method": "UpdateHub"}, {"service": "google.cloud.networkconnectivity.v1alpha1.HubService", "method": "DeleteHub"}, {"service": "google.cloud.networkconnectivity.v1alpha1.HubService", "method": "CreateSpoke"}, {"service": "google.cloud.networkconnectivity.v1alpha1.HubService", "method": "UpdateSpoke"}, {"service": "google.cloud.networkconnectivity.v1alpha1.HubService", "method": "DeleteSpoke"}], "timeout": "60s"}]}