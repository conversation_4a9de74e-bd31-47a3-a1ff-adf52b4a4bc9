// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkconnectivity.v1alpha1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkConnectivity.V1Alpha1";
option go_package = "cloud.google.com/go/networkconnectivity/apiv1alpha1/networkconnectivitypb;networkconnectivitypb";
option java_multiple_files = true;
option java_outer_classname = "HubProto";
option java_package = "com.google.cloud.networkconnectivity.v1alpha1";
option php_namespace = "Google\\Cloud\\NetworkConnectivity\\V1alpha1";
option ruby_package = "Google::Cloud::NetworkConnectivity::V1alpha1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/VpnTunnel"
  pattern: "projects/{project}/regions/{region}/vpnTunnels/{resource_id}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/InterconnectAttachment"
  pattern: "projects/{project}/regions/{region}/interconnectAttachments/{resource_id}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Instance"
  pattern: "projects/{project}/zones/{zone}/instances/{instance}"
};

// Network Connectivity Center is a hub-and-spoke abstraction for
// network connectivity management in Google Cloud. It reduces
// operational complexity through a simple, centralized connectivity management
// model.
service HubService {
  option (google.api.default_host) = "networkconnectivity.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists Hubs in a given project and location.
  rpc ListHubs(ListHubsRequest) returns (ListHubsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/global}/hubs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Hub.
  rpc GetHub(GetHubRequest) returns (Hub) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/global/hubs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Hub in a given project and location.
  rpc CreateHub(CreateHubRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/global}/hubs"
      body: "hub"
    };
    option (google.api.method_signature) = "parent,hub,hub_id";
    option (google.longrunning.operation_info) = {
      response_type: "Hub"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Hub.
  rpc UpdateHub(UpdateHubRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha1/{hub.name=projects/*/locations/global/hubs/*}"
      body: "hub"
    };
    option (google.api.method_signature) = "hub,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Hub"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Hub.
  rpc DeleteHub(DeleteHubRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/global/hubs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Spokes in a given project and location.
  rpc ListSpokes(ListSpokesRequest) returns (ListSpokesResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*}/spokes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Spoke.
  rpc GetSpoke(GetSpokeRequest) returns (Spoke) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/spokes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Spoke in a given project and location.
  rpc CreateSpoke(CreateSpokeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*}/spokes"
      body: "spoke"
    };
    option (google.api.method_signature) = "parent,spoke,spoke_id";
    option (google.longrunning.operation_info) = {
      response_type: "Spoke"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Spoke.
  rpc UpdateSpoke(UpdateSpokeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha1/{spoke.name=projects/*/locations/*/spokes/*}"
      body: "spoke"
    };
    option (google.api.method_signature) = "spoke,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Spoke"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Spoke.
  rpc DeleteSpoke(DeleteSpokeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/spokes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Network Connectivity Center is a hub-and-spoke abstraction for
// network connectivity management in Google Cloud. It reduces
// operational complexity through a simple, centralized connectivity management
// model. Following is the resource message of a hub.
message Hub {
  option (google.api.resource) = {
    type: "networkconnectivity.googleapis.com/Hub"
    pattern: "projects/{project}/locations/global/hubs/{hub}"
  };

  // Immutable. The name of a Hub resource.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Time when the Hub was created.
  google.protobuf.Timestamp create_time = 2;

  // Time when the Hub was updated.
  google.protobuf.Timestamp update_time = 3;

  // User-defined labels.
  map<string, string> labels = 4;

  // Short description of the hub resource.
  string description = 5;

  // Output only. A list of the URIs of all attached spokes
  repeated string spokes = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "networkconnectivity.googleapis.com/Spoke"
    }
  ];

  // Output only. Google-generated UUID for this resource. This is unique across all Hub
  // resources. If a Hub resource is deleted and another with the same name is
  // created, it gets a different unique_id.
  string unique_id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current lifecycle state of this Hub.
  State state = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A Spoke is an  abstraction of a network attachment being attached
// to a Hub. A Spoke can be underlying a VPN tunnel, a
// VLAN (interconnect) attachment, a Router appliance, etc.
message Spoke {
  option (google.api.resource) = {
    type: "networkconnectivity.googleapis.com/Spoke"
    pattern: "projects/{project}/locations/{location}/spokes/{spoke}"
  };

  // Immutable. The name of a Spoke resource.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // The time when the Spoke was created.
  google.protobuf.Timestamp create_time = 2;

  // The time when the Spoke was updated.
  google.protobuf.Timestamp update_time = 3;

  // User-defined labels.
  map<string, string> labels = 4;

  // Short description of the spoke resource
  string description = 5;

  // The resource URL of the hub resource that the spoke is attached to
  string hub = 6 [(google.api.resource_reference) = {
                    type: "networkconnectivity.googleapis.com/Hub"
                  }];

  // The URIs of linked VPN tunnel resources
  repeated string linked_vpn_tunnels = 12 [(google.api.resource_reference) = {
                                             type: "compute.googleapis.com/VpnTunnel"
                                           }];

  // The URIs of linked interconnect attachment resources
  repeated string linked_interconnect_attachments = 13 [(google.api.resource_reference) = {
                                                          type: "compute.googleapis.com/InterconnectAttachment"
                                                        }];

  // The URIs of linked Router appliance resources
  repeated RouterApplianceInstance linked_router_appliance_instances = 14;

  // Output only. Google-generated UUID for this resource. This is unique across all Spoke
  // resources. If a Spoke resource is deleted and another with the same name is
  // created, it gets a different unique_id.
  string unique_id = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current lifecycle state of this Hub.
  State state = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request for [HubService.ListHubs][google.cloud.networkconnectivity.v1alpha1.HubService.ListHubs] method.
message ListHubsRequest {
  // Required. The parent resource's name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of results per page that should be returned.
  int32 page_size = 2;

  // The page token.
  string page_token = 3;

  // A filter expression that filters the results listed in the response.
  string filter = 4;

  // Sort the results by a certain order.
  string order_by = 5;
}

// Response for [HubService.ListHubs][google.cloud.networkconnectivity.v1alpha1.HubService.ListHubs] method.
message ListHubsResponse {
  // Hubs to be returned.
  repeated Hub hubs = 1;

  // The next pagination token in the List response. It should be used as
  // page_token for the following request. An empty value means no more result.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for [HubService.GetHub][google.cloud.networkconnectivity.v1alpha1.HubService.GetHub] method.
message GetHubRequest {
  // Required. Name of the Hub resource to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkconnectivity.googleapis.com/Hub"
    }
  ];
}

// Request for [HubService.CreateHub][google.cloud.networkconnectivity.v1alpha1.HubService.CreateHub] method.
message CreateHubRequest {
  // Required. The parent resource's name of the Hub.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Unique id for the Hub to create.
  string hub_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. Initial values for a new Hub.
  Hub hub = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request for [HubService.UpdateHub][google.cloud.networkconnectivity.v1alpha1.HubService.UpdateHub] method.
message UpdateHubRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // Hub resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The state that the Hub should be in after the update.
  Hub hub = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request for [HubService.DeleteHub][google.cloud.networkconnectivity.v1alpha1.HubService.DeleteHub].
message DeleteHubRequest {
  // Required. The name of the Hub to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkconnectivity.googleapis.com/Hub"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The request for [HubService.ListSpokes][google.cloud.networkconnectivity.v1alpha1.HubService.ListSpokes].
message ListSpokesRequest {
  // Required. The parent's resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of results per page that should be returned.
  int32 page_size = 2;

  // The page token.
  string page_token = 3;

  // A filter expression that filters the results listed in the response.
  string filter = 4;

  // Sort the results by a certain order.
  string order_by = 5;
}

// The response for [HubService.ListSpokes][google.cloud.networkconnectivity.v1alpha1.HubService.ListSpokes].
message ListSpokesResponse {
  // Spokes to be returned.
  repeated Spoke spokes = 1;

  // The next pagination token in the List response. It should be used as
  // page_token for the following request. An empty value means no more result.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// The request for [HubService.GetSpoke][google.cloud.networkconnectivity.v1alpha1.HubService.GetSpoke].
message GetSpokeRequest {
  // Required. The name of Spoke resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkconnectivity.googleapis.com/Spoke"
    }
  ];
}

// The request for [HubService.CreateSpoke][google.cloud.networkconnectivity.v1alpha1.HubService.CreateSpoke].
message CreateSpokeRequest {
  // Required. The parent's resource name of the Spoke.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Unique id for the Spoke to create.
  string spoke_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. Initial values for a new Hub.
  Spoke spoke = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request for [HubService.UpdateSpoke][google.cloud.networkconnectivity.v1alpha1.HubService.UpdateSpoke] method.
message UpdateSpokeRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // Spoke resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The state that the Spoke should be in after the update.
  Spoke spoke = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request for [HubService.DeleteSpoke][google.cloud.networkconnectivity.v1alpha1.HubService.DeleteSpoke].
message DeleteSpokeRequest {
  // Required. The name of the Spoke to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkconnectivity.googleapis.com/Spoke"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// RouterAppliance represents a Router appliance which is specified by a VM URI
// and a NIC address.
message RouterApplianceInstance {
  // The URI of the virtual machine resource
  string virtual_machine = 1 [(google.api.resource_reference) = {
                                type: "compute.googleapis.com/Instance"
                              }];

  // The IP address of the network interface to use for peering.
  string ip_address = 3;

  string network_interface = 2 [deprecated = true];
}

// The State enum represents the lifecycle of a Network Connectivity Center
// resource.
enum State {
  // No state information available
  STATE_UNSPECIFIED = 0;

  // The resource's create operation is in progress
  CREATING = 1;

  // The resource is active
  ACTIVE = 2;

  // The resource's Delete operation is in progress
  DELETING = 3;
}
