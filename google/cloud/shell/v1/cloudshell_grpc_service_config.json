{"methodConfig": [{"name": [{"service": "google.cloud.shell.v1.CloudShellService", "method": "GetEnvironment"}, {"service": "google.cloud.shell.v1.CloudShellService", "method": "ListEnvironments"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "UNKNOWN"]}}, {"name": [{"service": "google.cloud.shell.v1.CloudShellService", "method": "AddPublicKey"}, {"service": "google.cloud.shell.v1.CloudShellService", "method": "AuthorizeEnvironment"}, {"service": "google.cloud.shell.v1.CloudShellService", "method": "CreateEnvironment"}, {"service": "google.cloud.shell.v1.CloudShellService", "method": "DeleteEnvironment"}, {"service": "google.cloud.shell.v1.CloudShellService", "method": "RemovePublicKey"}, {"service": "google.cloud.shell.v1.CloudShellService", "method": "StartEnvironment"}, {"service": "google.cloud.shell.v1.CloudShellService", "method": "UpdateEnvironment"}], "timeout": "60s"}]}