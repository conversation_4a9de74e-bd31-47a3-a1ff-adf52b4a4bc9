type: google.api.Service
config_version: 3
name: cloudshell.googleapis.com
title: Cloud Shell API

apis:
- name: google.cloud.shell.v1.CloudShellService
- name: google.longrunning.Operations

types:
- name: google.cloud.shell.v1.AddPublicKeyMetadata
- name: google.cloud.shell.v1.AddPublicKeyResponse
- name: google.cloud.shell.v1.AuthorizeEnvironmentMetadata
- name: google.cloud.shell.v1.AuthorizeEnvironmentResponse
- name: google.cloud.shell.v1.CreateEnvironmentMetadata
- name: google.cloud.shell.v1.DeleteEnvironmentMetadata
- name: google.cloud.shell.v1.RemovePublicKeyMetadata
- name: google.cloud.shell.v1.RemovePublicKeyResponse
- name: google.cloud.shell.v1.StartEnvironmentMetadata
- name: google.cloud.shell.v1.StartEnvironmentResponse

documentation:
  summary: |-
    Allows users to start, configure, and connect to interactive shell sessions
    running in the cloud.

backend:
  rules:
  - selector: 'google.cloud.shell.v1.CloudShellService.*'
    deadline: 30.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 30.0

authentication:
  rules:
  - selector: 'google.cloud.shell.v1.CloudShellService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
