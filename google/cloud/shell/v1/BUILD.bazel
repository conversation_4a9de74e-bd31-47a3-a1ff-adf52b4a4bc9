# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "shell_proto",
    srcs = [
        "cloudshell.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "shell_proto_with_info",
    deps = [
        ":shell_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "shell_java_proto",
    deps = [":shell_proto"],
)

java_grpc_library(
    name = "shell_java_grpc",
    srcs = [":shell_proto"],
    deps = [":shell_java_proto"],
)

java_gapic_library(
    name = "shell_java_gapic",
    srcs = [":shell_proto_with_info"],
    grpc_service_config = "cloudshell_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudshell_v1.yaml",
    test_deps = [
        ":shell_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":shell_java_proto",
    ],
)

java_gapic_test(
    name = "shell_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.shell.v1.CloudShellServiceClientHttpJsonTest",
        "com.google.cloud.shell.v1.CloudShellServiceClientTest",
    ],
    runtime_deps = [":shell_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-shell-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":shell_java_gapic",
        ":shell_java_grpc",
        ":shell_java_proto",
        ":shell_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "shell_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/shell/apiv1/shellpb",
    protos = [":shell_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "shell_go_gapic",
    srcs = [":shell_proto_with_info"],
    grpc_service_config = "cloudshell_grpc_service_config.json",
    importpath = "cloud.google.com/go/shell/apiv1;shell",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudshell_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":shell_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-shell-v1-go",
    deps = [
        ":shell_go_gapic",
        ":shell_go_gapic_srcjar-metadata.srcjar",
        ":shell_go_gapic_srcjar-snippets.srcjar",
        ":shell_go_gapic_srcjar-test.srcjar",
        ":shell_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "shell_py_gapic",
    srcs = [":shell_proto"],
    grpc_service_config = "cloudshell_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudshell_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "shell_py_gapic_test",
    srcs = [
        "shell_py_gapic_pytest.py",
        "shell_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":shell_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "shell-v1-py",
    deps = [
        ":shell_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "shell_php_proto",
    deps = [":shell_proto"],
)

php_gapic_library(
    name = "shell_php_gapic",
    srcs = [":shell_proto_with_info"],
    grpc_service_config = "cloudshell_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudshell_v1.yaml",
    transport = "grpc+rest",
    deps = [":shell_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-shell-v1-php",
    deps = [
        ":shell_php_gapic",
        ":shell_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "shell_nodejs_gapic",
    package_name = "@google-cloud/shell",
    src = ":shell_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudshell_grpc_service_config.json",
    package = "google.cloud.shell.v1",
    rest_numeric_enums = True,
    service_yaml = "cloudshell_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "shell-v1-nodejs",
    deps = [
        ":shell_nodejs_gapic",
        ":shell_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "shell_ruby_proto",
    deps = [":shell_proto"],
)

ruby_grpc_library(
    name = "shell_ruby_grpc",
    srcs = [":shell_proto"],
    deps = [":shell_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "shell_ruby_gapic",
    srcs = [":shell_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-shell-v1",
        "ruby-cloud-env-prefix=CLOUD_SHELL",
        "ruby-cloud-product-url=https://cloud.google.com/shell/",
        "ruby-cloud-api-id=cloudshell.googleapis.com",
        "ruby-cloud-api-shortname=cloudshell",
    ],
    grpc_service_config = "cloudshell_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Shell is an interactive shell environment for Google Cloud that makes it easy for you to learn and experiment with Google Cloud and manage your projects and resources from your web browser.",
    ruby_cloud_title = "Cloud Shell V1",
    service_yaml = "cloudshell_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":shell_ruby_grpc",
        ":shell_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-shell-v1-ruby",
    deps = [
        ":shell_ruby_gapic",
        ":shell_ruby_grpc",
        ":shell_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "shell_csharp_proto",
    deps = [":shell_proto"],
)

csharp_grpc_library(
    name = "shell_csharp_grpc",
    srcs = [":shell_proto"],
    deps = [":shell_csharp_proto"],
)

csharp_gapic_library(
    name = "shell_csharp_gapic",
    srcs = [":shell_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudshell_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudshell_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":shell_csharp_grpc",
        ":shell_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-shell-v1-csharp",
    deps = [
        ":shell_csharp_gapic",
        ":shell_csharp_grpc",
        ":shell_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "shell_cc_proto",
    deps = [":shell_proto"],
)

cc_grpc_library(
    name = "shell_cc_grpc",
    srcs = [":shell_proto"],
    grpc_only = True,
    deps = [":shell_cc_proto"],
)
