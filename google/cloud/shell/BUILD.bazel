# This build file includes a target for the Ruby wrapper library for
# google-cloud-shell.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudshell.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "cloudshell_ruby_wrapper",
    srcs = ["//google/cloud/shell/v1:shell_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-shell",
        "ruby-cloud-env-prefix=CLOUD_SHELL",
        "ruby-cloud-wrapper-of=v1:0.7",
        "ruby-cloud-product-url=https://cloud.google.com/shell/",
        "ruby-cloud-api-id=cloudshell.googleapis.com",
        "ruby-cloud-api-shortname=cloudshell",
    ],
    ruby_cloud_description = "Cloud Shell is an interactive shell environment for Google Cloud that makes it easy for you to learn and experiment with Google Cloud and manage your projects and resources from your web browser.",
    ruby_cloud_title = "Cloud Shell",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-shell-ruby",
    deps = [
        ":cloudshell_ruby_wrapper",
    ],
)
