# This build file includes a target for the Ruby wrapper library for
# google-cloud-functions.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudfunctions.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "cloudfunctions_ruby_wrapper",
    srcs = ["//google/cloud/functions/v1:functions_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-functions",
        "ruby-cloud-env-prefix=FUNCTIONS",
        "ruby-cloud-wrapper-of=v1:0.13",
        "ruby-cloud-product-url=https://cloud.google.com/functions",
        "ruby-cloud-api-id=cloudfunctions.googleapis.com",
        "ruby-cloud-api-shortname=cloudfunctions",
    ],
    ruby_cloud_description = "The Cloud Functions API manages lightweight user-provided functions executed in response to events.",
    ruby_cloud_title = "Cloud Functions",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-functions-ruby",
    deps = [
        ":cloudfunctions_ruby_wrapper",
    ],
)
