{"methodConfig": [{"name": [{"service": "google.cloud.functions.v2beta.CloudFunctionsService", "method": "ListFunctions"}, {"service": "google.cloud.functions.v2beta.CloudFunctionsService", "method": "GetFunction"}, {"service": "google.cloud.functions.v2beta.CloudFunctionsService", "method": "UpdateFunction"}, {"service": "google.cloud.functions.v2beta.CloudFunctionsService", "method": "DeleteFunction"}, {"service": "google.cloud.functions.v2beta.CloudFunctionsService", "method": "ListRuntimes"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.functions.v2beta.CloudFunctionsService", "method": "CreateFunction"}], "timeout": "600s"}]}