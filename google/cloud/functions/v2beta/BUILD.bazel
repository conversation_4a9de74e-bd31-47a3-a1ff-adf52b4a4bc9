# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "functions_proto",
    srcs = [
        "functions.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "functions_proto_with_info",
    deps = [
        ":functions_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

java_proto_library(
    name = "functions_java_proto",
    deps = [":functions_proto"],
)

java_grpc_library(
    name = "functions_java_grpc",
    srcs = [":functions_proto"],
    deps = [":functions_java_proto"],
)

java_gapic_library(
    name = "functions_java_gapic",
    srcs = [":functions_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "functions_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudfunctions_v2beta.yaml",
    test_deps = [
        ":functions_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":functions_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "functions_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.functions.v2beta.FunctionServiceClientHttpJsonTest",
        "com.google.cloud.functions.v2beta.FunctionServiceClientTest",
    ],
    runtime_deps = [":functions_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-functions-v2beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":functions_java_gapic",
        ":functions_java_grpc",
        ":functions_java_proto",
        ":functions_proto",
    ],
)

go_proto_library(
    name = "functions_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/functions/apiv2beta/functionspb",
    protos = [":functions_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "functions_go_gapic",
    srcs = [":functions_proto_with_info"],
    grpc_service_config = "functions_grpc_service_config.json",
    importpath = "cloud.google.com/go/functions/apiv2beta;functions",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "cloudfunctions_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":functions_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-functions-v2beta-go",
    deps = [
        ":functions_go_gapic",
        ":functions_go_gapic_srcjar-metadata.srcjar",
        ":functions_go_gapic_srcjar-snippets.srcjar",
        ":functions_go_gapic_srcjar-test.srcjar",
        ":functions_go_proto",
    ],
)

py_gapic_library(
    name = "functions_py_gapic",
    srcs = [":functions_proto"],
    grpc_service_config = "functions_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudfunctions_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "functions_py_gapic_test",
    srcs = [
        "functions_py_gapic_pytest.py",
        "functions_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":functions_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "functions-v2beta-py",
    deps = [
        ":functions_py_gapic",
    ],
)

php_proto_library(
    name = "functions_php_proto",
    deps = [":functions_proto"],
)

php_gapic_library(
    name = "functions_php_gapic",
    srcs = [":functions_proto_with_info"],
    grpc_service_config = "functions_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudfunctions_v2beta.yaml",
    transport = "grpc+rest",
    deps = [":functions_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-functions-v2beta-php",
    deps = [
        ":functions_php_gapic",
        ":functions_php_proto",
    ],
)

nodejs_gapic_library(
    name = "functions_nodejs_gapic",
    package_name = "@google-cloud/functions",
    src = ":functions_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "functions_grpc_service_config.json",
    package = "google.cloud.functions.v2beta",
    rest_numeric_enums = True,
    service_yaml = "cloudfunctions_v2beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "functions-v2beta-nodejs",
    deps = [
        ":functions_nodejs_gapic",
        ":functions_proto",
    ],
)

ruby_proto_library(
    name = "functions_ruby_proto",
    deps = [":functions_proto"],
)

ruby_grpc_library(
    name = "functions_ruby_grpc",
    srcs = [":functions_proto"],
    deps = [":functions_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "functions_ruby_gapic",
    srcs = [":functions_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-functions-v2beta",
    ],
    grpc_service_config = "functions_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudfunctions_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":functions_ruby_grpc",
        ":functions_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-functions-v2beta-ruby",
    deps = [
        ":functions_ruby_gapic",
        ":functions_ruby_grpc",
        ":functions_ruby_proto",
    ],
)

csharp_proto_library(
    name = "functions_csharp_proto",
    deps = [":functions_proto"],
)

csharp_grpc_library(
    name = "functions_csharp_grpc",
    srcs = [":functions_proto"],
    deps = [":functions_csharp_proto"],
)

csharp_gapic_library(
    name = "functions_csharp_gapic",
    srcs = [":functions_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "functions_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudfunctions_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":functions_csharp_grpc",
        ":functions_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-functions-v2beta-csharp",
    deps = [
        ":functions_csharp_gapic",
        ":functions_csharp_grpc",
        ":functions_csharp_proto",
    ],
)

cc_proto_library(
    name = "functions_cc_proto",
    deps = [":functions_proto"],
)

cc_grpc_library(
    name = "functions_cc_grpc",
    srcs = [":functions_proto"],
    grpc_only = True,
    deps = [":functions_cc_proto"],
)
