type: google.api.Service
config_version: 3
name: cloudfunctions.googleapis.com
title: Cloud Functions API

apis:
- name: google.cloud.functions.v2.FunctionService
- name: google.cloud.location.Locations
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.functions.v2.LocationMetadata
- name: google.cloud.functions.v2.OperationMetadata

documentation:
  summary: 'Manages lightweight user-provided functions executed in response to events.'
  overview: 'Manages lightweight user-provided functions executed in response to
events.'
  rules:
  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: 'google.cloud.functions.v2.FunctionService.*'
    deadline: 30.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 30.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 30.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 30.0

http:
  rules:
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v2/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v2/{resource=projects/*/locations/*/functions/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v2/{resource=projects/*/locations/*/functions/*}:setIamPolicy'
    body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v2/{resource=projects/*/locations/*/functions/*}:testIamPermissions'
    body: '*'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.functions.v2.FunctionService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
