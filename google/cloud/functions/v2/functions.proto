// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.functions.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/any.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "cloud.google.com/go/functions/apiv2/functionspb;functionspb";
option java_multiple_files = true;
option java_outer_classname = "FunctionsProto";
option java_package = "com.google.cloud.functions.v2";
option objc_class_prefix = "GCF";
option (google.api.resource_definition) = {
  type: "artifactregistry.googleapis.com/Repository"
  pattern: "projects/{project}/locations/{location}/repositories/{repository}"
};
option (google.api.resource_definition) = {
  type: "cloudbuild.googleapis.com/Build"
  pattern: "projects/{project}/locations/{location}/builds/{build}"
};
option (google.api.resource_definition) = {
  type: "cloudbuild.googleapis.com/WorkerPool"
  pattern: "projects/{project}/locations/{location}/workerPools/{worker_pool}"
};
option (google.api.resource_definition) = {
  type: "run.googleapis.com/Service"
  pattern: "projects/{project}/locations/{location}/services/{service}"
};
option (google.api.resource_definition) = {
  type: "vpcaccess.googleapis.com/Connector"
  pattern: "projects/{project}/locations/{location}/connectors/{connector}"
};
option (google.api.resource_definition) = {
  type: "eventarc.googleapis.com/Trigger"
  pattern: "projects/{project}/locations/{location}/triggers/{trigger}"
};
option (google.api.resource_definition) = {
  type: "eventarc.googleapis.com/Channel"
  pattern: "projects/{project}/locations/{location}/channels/{channel}"
};
option (google.api.resource_definition) = {
  type: "pubsub.googleapis.com/Topic"
  pattern: "projects/{project}/topics/{topic}"
};
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};

// Google Cloud Functions is used to deploy functions that are executed by
// Google in response to various events. Data connected with that event is
// passed to a function as the input data.
//
// A **function** is a resource which describes a function that should be
// executed and how it is triggered.
service FunctionService {
  option (google.api.default_host) = "cloudfunctions.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Returns a function with the given name from the requested project.
  rpc GetFunction(GetFunctionRequest) returns (Function) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/functions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of functions that belong to the requested project.
  rpc ListFunctions(ListFunctionsRequest) returns (ListFunctionsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/functions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new function. If a function with the given name already exists in
  // the specified project, the long running operation will return
  // `ALREADY_EXISTS` error.
  rpc CreateFunction(CreateFunctionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/functions"
      body: "function"
    };
    option (google.api.method_signature) = "parent,function,function_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.functions.v2.Function"
      metadata_type: "google.cloud.functions.v2.OperationMetadata"
    };
  }

  // Updates existing function.
  rpc UpdateFunction(UpdateFunctionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{function.name=projects/*/locations/*/functions/*}"
      body: "function"
    };
    option (google.api.method_signature) = "function,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.functions.v2.Function"
      metadata_type: "google.cloud.functions.v2.OperationMetadata"
    };
  }

  // Deletes a function with the given name from the specified project. If the
  // given function is used by some trigger, the trigger will be updated to
  // remove this function.
  rpc DeleteFunction(DeleteFunctionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/functions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.functions.v2.OperationMetadata"
    };
  }

  // Returns a signed URL for uploading a function source code.
  // For more information about the signed URL usage see:
  // https://cloud.google.com/storage/docs/access-control/signed-urls.
  // Once the function source code upload is complete, the used signed
  // URL should be provided in CreateFunction or UpdateFunction request
  // as a reference to the function source code.
  //
  // When uploading source code to the generated signed URL, please follow
  // these restrictions:
  //
  // * Source file type should be a zip file.
  // * No credentials should be attached - the signed URLs provide access to the
  //   target bucket using internal service identity; if credentials were
  //   attached, the identity from the credentials would be used, but that
  //   identity does not have permissions to upload files to the URL.
  //
  // When making a HTTP PUT request, specify this header:
  //
  // * `content-type: application/zip`
  //
  // Do not specify this header:
  //
  // * `Authorization: Bearer YOUR_TOKEN`
  rpc GenerateUploadUrl(GenerateUploadUrlRequest)
      returns (GenerateUploadUrlResponse) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/functions:generateUploadUrl"
      body: "*"
    };
  }

  // Returns a signed URL for downloading deployed function source code.
  // The URL is only valid for a limited period and should be used within
  // 30 minutes of generation.
  // For more information about the signed URL usage see:
  // https://cloud.google.com/storage/docs/access-control/signed-urls
  rpc GenerateDownloadUrl(GenerateDownloadUrlRequest)
      returns (GenerateDownloadUrlResponse) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/functions/*}:generateDownloadUrl"
      body: "*"
    };
  }

  // Returns a list of runtimes that are supported for the requested project.
  rpc ListRuntimes(ListRuntimesRequest) returns (ListRuntimesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/runtimes"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The type of the long running operation.
enum OperationType {
  // Unspecified
  OPERATIONTYPE_UNSPECIFIED = 0;

  // CreateFunction
  CREATE_FUNCTION = 1;

  // UpdateFunction
  UPDATE_FUNCTION = 2;

  // DeleteFunction
  DELETE_FUNCTION = 3;
}

// The environment the function is hosted on.
enum Environment {
  // Unspecified
  ENVIRONMENT_UNSPECIFIED = 0;

  // Gen 1
  GEN_1 = 1;

  // Gen 2
  GEN_2 = 2;
}

// Describes a Cloud Function that contains user computation executed in
// response to an event. It encapsulates function and trigger configurations.
message Function {
  option (google.api.resource) = {
    type: "cloudfunctions.googleapis.com/Function"
    pattern: "projects/{project}/locations/{location}/functions/{function}"
    plural: "functions"
    singular: "function"
  };

  // Describes the current state of the function.
  enum State {
    // Not specified. Invalid state.
    STATE_UNSPECIFIED = 0;

    // Function has been successfully deployed and is serving.
    ACTIVE = 1;

    // Function deployment failed and the function is not serving.
    FAILED = 2;

    // Function is being created or updated.
    DEPLOYING = 3;

    // Function is being deleted.
    DELETING = 4;

    // Function deployment failed and the function serving state is undefined.
    // The function should be updated or deleted to move it out of this state.
    UNKNOWN = 5;
  }

  // A user-defined name of the function. Function names must be unique
  // globally and match pattern `projects/*/locations/*/functions/*`
  string name = 1;

  // User-provided description of a function.
  string description = 2;

  // Describes the Build step of the function that builds a container from the
  // given source.
  BuildConfig build_config = 3;

  // Describes the Service being deployed. Currently deploys services to Cloud
  // Run (fully managed).
  ServiceConfig service_config = 4;

  // An Eventarc trigger managed by Google Cloud Functions that fires events in
  // response to a condition in another service.
  EventTrigger event_trigger = 5;

  // Output only. State of the function.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of a Cloud Function.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels associated with this Cloud Function.
  map<string, string> labels = 8;

  // Output only. State Messages for this Cloud Function.
  repeated StateMessage state_messages = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Describe whether the function is 1st Gen or 2nd Gen.
  Environment environment = 10;

  // Output only. The deployed url for the function.
  string url = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [Preview] Resource name of a KMS crypto key (managed by the user) used to
  // encrypt/decrypt function resources.
  //
  // It must match the pattern
  // `projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}`.
  string kms_key_name = 25 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 27 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The create timestamp of a Cloud Function. This is only
  // applicable to 2nd Gen functions.
  google.protobuf.Timestamp create_time = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Informational messages about the state of the Cloud Function or Operation.
message StateMessage {
  // Severity of the state message.
  enum Severity {
    // Not specified. Invalid severity.
    SEVERITY_UNSPECIFIED = 0;

    // ERROR-level severity.
    ERROR = 1;

    // WARNING-level severity.
    WARNING = 2;

    // INFO-level severity.
    INFO = 3;
  }

  // Severity of the state message.
  Severity severity = 1;

  // One-word CamelCase type of the state message.
  string type = 2;

  // The message.
  string message = 3;
}

// Location of the source in an archive file in Google Cloud Storage.
message StorageSource {
  // Google Cloud Storage bucket containing the source (see
  // [Bucket Name
  // Requirements](https://cloud.google.com/storage/docs/bucket-naming#requirements)).
  string bucket = 1;

  // Google Cloud Storage object containing the source.
  //
  // This object must be a gzipped archive file (`.tar.gz`) containing source to
  // build.
  string object = 2;

  // Google Cloud Storage generation for the object. If the generation is
  // omitted, the latest generation will be used.
  int64 generation = 3;

  // When the specified storage bucket is a 1st gen function uploard url bucket,
  // this field should be set as the generated upload url for 1st gen
  // deployment.
  string source_upload_url = 4;
}

// Location of the source in a Google Cloud Source Repository.
message RepoSource {
  // A revision within the Cloud Source Repository must be specified in
  // one of these ways.
  oneof revision {
    // Regex matching branches to build.
    //
    // The syntax of the regular expressions accepted is the syntax accepted by
    // RE2 and described at https://github.com/google/re2/wiki/Syntax
    string branch_name = 3;

    // Regex matching tags to build.
    //
    // The syntax of the regular expressions accepted is the syntax accepted by
    // RE2 and described at https://github.com/google/re2/wiki/Syntax
    string tag_name = 4;

    // Explicit commit SHA to build.
    string commit_sha = 5;
  }

  // ID of the project that owns the Cloud Source Repository. If omitted, the
  // project ID requesting the build is assumed.
  string project_id = 1;

  // Name of the Cloud Source Repository.
  string repo_name = 2;

  // Directory, relative to the source root, in which to run the build.
  //
  // This must be a relative path. If a step's `dir` is specified and is an
  // absolute path, this value is ignored for that step's execution.
  // eg. helloworld (no leading slash allowed)
  string dir = 6;

  // Only trigger a build if the revision regex does NOT match the revision
  // regex.
  bool invert_regex = 7;
}

// The location of the function source code.
message Source {
  // Location of the source.
  // At least one source needs to be provided for the deployment to succeed.
  oneof source {
    // If provided, get the source from this location in Google Cloud Storage.
    StorageSource storage_source = 1;

    // If provided, get the source from this location in a Cloud Source
    // Repository.
    RepoSource repo_source = 2;

    // If provided, get the source from GitHub repository. This option is valid
    // only for GCF 1st Gen function.
    // Example: https://github.com/<user>/<repo>/blob/<commit>/<path-to-code>
    string git_uri = 3;
  }
}

// Provenance of the source. Ways to find the original source, or verify that
// some source was used for this build.
message SourceProvenance {
  // A copy of the build's `source.storage_source`, if exists, with any
  // generations resolved.
  StorageSource resolved_storage_source = 1;

  // A copy of the build's `source.repo_source`, if exists, with any
  // revisions resolved.
  RepoSource resolved_repo_source = 2;

  // A copy of the build's `source.git_uri`, if exists, with any commits
  // resolved.
  string git_uri = 3;
}

// Describes the Build step of the function that builds a container from the
// given source.
message BuildConfig {
  // Docker Registry to use for storing function Docker images.
  enum DockerRegistry {
    // Unspecified.
    DOCKER_REGISTRY_UNSPECIFIED = 0;

    // Docker images will be stored in multi-regional Container Registry
    // repositories named `gcf`.
    CONTAINER_REGISTRY = 1;

    // Docker images will be stored in regional Artifact Registry repositories.
    // By default, GCF will create and use repositories named `gcf-artifacts`
    // in every region in which a function is deployed. But the repository to
    // use can also be specified by the user using the `docker_repository`
    // field.
    ARTIFACT_REGISTRY = 2;
  }

  // This controls when security patches are applied to the runtime environment.
  oneof runtime_update_policy {
    AutomaticUpdatePolicy automatic_update_policy = 40;

    OnDeployUpdatePolicy on_deploy_update_policy = 41;
  }

  // Output only. The Cloud Build name of the latest successful deployment of
  // the function.
  string build = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudbuild.googleapis.com/Build"
    }
  ];

  // The runtime in which to run the function. Required when deploying a new
  // function, optional when updating an existing function. For a complete
  // list of possible choices, see the
  // [`gcloud` command
  // reference](https://cloud.google.com/sdk/gcloud/reference/functions/deploy#--runtime).
  string runtime = 2;

  // The name of the function (as defined in source code) that will be
  // executed. Defaults to the resource name suffix, if not specified. For
  // backward compatibility, if function with given name is not found, then the
  // system will try to use function named "function".
  // For Node.js this is name of a function exported by the module specified
  // in `source_location`.
  string entry_point = 3;

  // The location of the function source code.
  Source source = 4;

  // Output only. A permanent fixed identifier for source.
  SourceProvenance source_provenance = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Name of the Cloud Build Custom Worker Pool that should be used to build the
  // function. The format of this field is
  // `projects/{project}/locations/{region}/workerPools/{workerPool}` where
  // {project} and {region} are the project id and region respectively where the
  // worker pool is defined and {workerPool} is the short name of the worker
  // pool.
  //
  // If the project id is not the same as the function, then the Cloud
  // Functions Service Agent
  // (service-<project_number>@gcf-admin-robot.iam.gserviceaccount.com) must be
  // granted the role Cloud Build Custom Workers Builder
  // (roles/cloudbuild.customworkers.builder) in the project.
  string worker_pool = 5 [(google.api.resource_reference) = {
    type: "cloudbuild.googleapis.com/WorkerPool"
  }];

  // User-provided build-time environment variables for the function
  map<string, string> environment_variables = 6;

  // Docker Registry to use for this deployment. This configuration is only
  // applicable to 1st Gen functions, 2nd Gen functions can only use Artifact
  // Registry.
  //
  // If unspecified, it defaults to `ARTIFACT_REGISTRY`.
  // If `docker_repository` field is specified, this field should either be left
  // unspecified or set to `ARTIFACT_REGISTRY`.
  DockerRegistry docker_registry = 10;

  // Repository in Artifact Registry to which the function docker image will be
  // pushed after it is built by Cloud Build. If specified by user, it is
  // created and managed by user with a customer managed encryption key.
  // Otherwise, GCF will create and use a repository named 'gcf-artifacts'
  // for every deployed region.
  //
  // It must match the pattern
  // `projects/{project}/locations/{location}/repositories/{repository}`.
  //
  // Cross-project repositories are not supported.
  // Cross-location repositories are not supported.
  // Repository format must be 'DOCKER'.
  string docker_repository = 7 [(google.api.resource_reference) = {
    type: "artifactregistry.googleapis.com/Repository"
  }];

  // Service account to be used for building the container. The format of this
  // field is `projects/{projectId}/serviceAccounts/{serviceAccountEmail}`.
  string service_account = 27;
}

// Describes the Service being deployed.
// Currently Supported : Cloud Run (fully managed).
message ServiceConfig {
  // Available egress settings.
  //
  // This controls what traffic is diverted through the VPC Access Connector
  // resource. By default PRIVATE_RANGES_ONLY will be used.
  enum VpcConnectorEgressSettings {
    // Unspecified.
    VPC_CONNECTOR_EGRESS_SETTINGS_UNSPECIFIED = 0;

    // Use the VPC Access Connector only for private IP space from RFC1918.
    PRIVATE_RANGES_ONLY = 1;

    // Force the use of VPC Access Connector for all egress traffic from the
    // function.
    ALL_TRAFFIC = 2;
  }

  // Available ingress settings.
  //
  // This controls what traffic can reach the function.
  //
  // If unspecified, ALLOW_ALL will be used.
  enum IngressSettings {
    // Unspecified.
    INGRESS_SETTINGS_UNSPECIFIED = 0;

    // Allow HTTP traffic from public and private sources.
    ALLOW_ALL = 1;

    // Allow HTTP traffic from only private VPC sources.
    ALLOW_INTERNAL_ONLY = 2;

    // Allow HTTP traffic from private VPC sources and through GCLB.
    ALLOW_INTERNAL_AND_GCLB = 3;
  }

  // Available security level settings.
  //
  // This enforces security protocol on function URL.
  //
  // Security level is only configurable for 1st Gen functions, If unspecified,
  // SECURE_OPTIONAL will be used. 2nd Gen functions are SECURE_ALWAYS ONLY.
  enum SecurityLevel {
    // Unspecified.
    SECURITY_LEVEL_UNSPECIFIED = 0;

    // Requests for a URL that match this handler that do not use HTTPS are
    // automatically redirected to the HTTPS URL with the same path. Query
    // parameters are reserved for the redirect.
    SECURE_ALWAYS = 1;

    // Both HTTP and HTTPS requests with URLs that match the handler succeed
    // without redirects. The application can examine the request to determine
    // which protocol was used and respond accordingly.
    SECURE_OPTIONAL = 2;
  }

  // Output only. Name of the service associated with a Function.
  // The format of this field is
  // `projects/{project}/locations/{region}/services/{service}`
  string service = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "run.googleapis.com/Service" }
  ];

  // The function execution timeout. Execution is considered failed and
  // can be terminated if the function is not completed at the end of the
  // timeout period. Defaults to 60 seconds.
  int32 timeout_seconds = 2;

  // The amount of memory available for a function.
  // Defaults to 256M. Supported units are k, M, G, Mi, Gi. If no unit is
  // supplied the value is interpreted as bytes.
  // See
  // https://github.com/kubernetes/kubernetes/blob/master/staging/src/k8s.io/apimachinery/pkg/api/resource/quantity.go
  // a full description.
  string available_memory = 13;

  // The number of CPUs used in a single container instance.
  // Default value is calculated from available memory.
  // Supports the same values as Cloud Run, see
  // https://cloud.google.com/run/docs/reference/rest/v1/Container#resourcerequirements
  // Example: "1" indicates 1 vCPU
  string available_cpu = 22;

  // Environment variables that shall be available during function execution.
  map<string, string> environment_variables = 4;

  // The limit on the maximum number of function instances that may coexist at a
  // given time.
  //
  // In some cases, such as rapid traffic surges, Cloud Functions may, for a
  // short period of time, create more instances than the specified max
  // instances limit. If your function cannot tolerate this temporary behavior,
  // you may want to factor in a safety margin and set a lower max instances
  // value than your function can tolerate.
  //
  // See the [Max
  // Instances](https://cloud.google.com/functions/docs/max-instances) Guide for
  // more details.
  int32 max_instance_count = 5;

  // The limit on the minimum number of function instances that may coexist at a
  // given time.
  //
  // Function instances are kept in idle state for a short period after they
  // finished executing the request to reduce cold start time for subsequent
  // requests. Setting a minimum instance count will ensure that the given
  // number of instances are kept running in idle state always. This can help
  // with cold start times when jump in incoming request count occurs after the
  // idle instance would have been stopped in the default case.
  int32 min_instance_count = 12;

  // The Serverless VPC Access connector that this cloud function can connect
  // to. The format of this field is `projects/*/locations/*/connectors/*`.
  string vpc_connector = 6 [(google.api.resource_reference) = {
    type: "vpcaccess.googleapis.com/Connector"
  }];

  // The egress settings for the connector, controlling what traffic is diverted
  // through it.
  VpcConnectorEgressSettings vpc_connector_egress_settings = 7;

  // The ingress settings for the function, controlling what traffic can reach
  // it.
  IngressSettings ingress_settings = 8;

  // Output only. URI of the Service deployed.
  string uri = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The email of the service's service account. If empty, defaults to
  // `{project_number}-<EMAIL>`.
  string service_account_email = 10;

  // Whether 100% of traffic is routed to the latest revision.
  // On CreateFunction and UpdateFunction, when set to true, the revision being
  // deployed will serve 100% of traffic, ignoring any traffic split settings,
  // if any. On GetFunction, true will be returned if the latest revision is
  // serving 100% of traffic.
  bool all_traffic_on_latest_revision = 16;

  // Secret environment variables configuration.
  repeated SecretEnvVar secret_environment_variables = 17;

  // Secret volumes configuration.
  repeated SecretVolume secret_volumes = 19;

  // Output only. The name of service revision.
  string revision = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Sets the maximum number of concurrent requests that each instance
  // can receive. Defaults to 1.
  int32 max_instance_request_concurrency = 20;

  // Security level configure whether the function only accepts https.
  // This configuration is only applicable to 1st Gen functions with Http
  // trigger. By default https is optional for 1st Gen functions; 2nd Gen
  // functions are https ONLY.
  SecurityLevel security_level = 21;

  // Optional. The binary authorization policy to be checked when deploying the
  // Cloud Run service.
  string binary_authorization_policy = 23
      [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for a secret environment variable. It has the information
// necessary to fetch the secret value from secret manager and expose it as an
// environment variable.
message SecretEnvVar {
  // Name of the environment variable.
  string key = 1;

  // Project identifier (preferably project number but can also be the
  // project ID) of the project that contains the secret. If not set, it is
  // assumed that the secret is in the same project as the function.
  string project_id = 2;

  // Name of the secret in secret manager (not the full resource name).
  string secret = 3;

  // Version of the secret (version number or the string 'latest'). It is
  // recommended to use a numeric version for secret environment variables as
  // any updates to the secret value is not reflected until new instances
  // start.
  string version = 4;
}

// Configuration for a secret volume. It has the information necessary to fetch
// the secret value from secret manager and make it available as files mounted
// at the requested paths within the application container.
message SecretVolume {
  // Configuration for a single version.
  message SecretVersion {
    // Version of the secret (version number or the string 'latest'). It is
    // preferable to use `latest` version with secret volumes as secret value
    // changes are reflected immediately.
    string version = 1;

    // Relative path of the file under the mount path where the secret value for
    // this version will be fetched and made available. For example, setting the
    // mount_path as '/etc/secrets' and path as `secret_foo` would mount the
    // secret value file at `/etc/secrets/secret_foo`.
    string path = 2;
  }

  // The path within the container to mount the secret volume. For example,
  // setting the mount_path as `/etc/secrets` would mount the secret value files
  // under the `/etc/secrets` directory. This directory will also be completely
  // shadowed and unavailable to mount any other secrets.
  // Recommended mount path: /etc/secrets
  string mount_path = 1;

  // Project identifier (preferably project number but can also be the project
  // ID) of the project that contains the secret. If not set, it is
  // assumed that the secret is in the same project as the function.
  string project_id = 2;

  // Name of the secret in secret manager (not the full resource name).
  string secret = 3;

  // List of secret versions to mount for this secret. If empty, the `latest`
  // version of the secret will be made available in a file named after the
  // secret under the mount point.
  repeated SecretVersion versions = 4;
}

// Describes EventTrigger, used to request events to be sent from another
// service.
message EventTrigger {
  // Describes the retry policy in case of function's execution failure.
  // Retried execution is charged as any other execution.
  enum RetryPolicy {
    // Not specified.
    RETRY_POLICY_UNSPECIFIED = 0;

    // Do not retry.
    RETRY_POLICY_DO_NOT_RETRY = 1;

    // Retry on any failure, retry up to 7 days with an exponential backoff
    // (capped at 10 seconds).
    RETRY_POLICY_RETRY = 2;
  }

  // Output only. The resource name of the Eventarc trigger. The format of this
  // field is `projects/{project}/locations/{region}/triggers/{trigger}`.
  string trigger = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Trigger"
    }
  ];

  // The region that the trigger will be in. The trigger will only receive
  // events originating in this region. It can be the same
  // region as the function, a different region or multi-region, or the global
  // region. If not provided, defaults to the same region as the function.
  string trigger_region = 2;

  // Required. The type of event to observe. For example:
  // `google.cloud.audit.log.v1.written` or
  // `google.cloud.pubsub.topic.v1.messagePublished`.
  string event_type = 3 [(google.api.field_behavior) = REQUIRED];

  // Criteria used to filter events.
  repeated EventFilter event_filters = 4;

  // Optional. The name of a Pub/Sub topic in the same project that will be used
  // as the transport topic for the event delivery. Format:
  // `projects/{project}/topics/{topic}`.
  //
  // This is only valid for events of type
  // `google.cloud.pubsub.topic.v1.messagePublished`. The topic provided here
  // will not be deleted at function deletion.
  string pubsub_topic = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
  ];

  // Optional. The email of the trigger's service account. The service account
  // must have permission to invoke Cloud Run services, the permission is
  // `run.routes.invoke`.
  // If empty, defaults to the Compute Engine default service account:
  // `{project_number}-<EMAIL>`.
  string service_account_email = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If unset, then defaults to ignoring failures (i.e. not retrying
  // them).
  RetryPolicy retry_policy = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the channel associated with the trigger in
  // `projects/{project}/locations/{location}/channels/{channel}` format.
  // You must provide a channel to receive events from Eventarc SaaS partners.
  string channel = 8 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/Channel"
    }
  ];

  // Optional. The hostname of the service that 1st Gen function should be
  // observed.
  //
  // If no string is provided, the default service implementing the API will
  // be used. For example, `storage.googleapis.com` is the default for all
  // event types in the `google.storage` namespace.
  //
  // The field is only applicable to 1st Gen functions.
  string service = 9 [(google.api.field_behavior) = OPTIONAL];
}

// Filters events based on exact matches on the CloudEvents attributes.
message EventFilter {
  // Required. The name of a CloudEvents attribute.
  string attribute = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The value for the attribute.
  string value = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The operator used for matching the events with the value of the
  // filter. If not specified, only events that have an exact key-value pair
  // specified in the filter are matched. The only allowed value is
  // `match-path-pattern`.
  string operator = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request for the `GetFunction` method.
message GetFunctionRequest {
  // Required. The name of the function which details should be obtained.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudfunctions.googleapis.com/Function"
    }
  ];

  // Optional. The version of the 1st gen function whose details should
  // be obtained. The version of a 1st gen function is an integer that starts
  // from 1 and gets incremented on redeployments. GCF may keep historical
  // configs for old versions of 1st gen function. This field can be specified
  // to fetch the historical configs. This field is valid only for GCF 1st gen
  // function.
  string revision = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for the `ListFunctions` method.
message ListFunctionsRequest {
  // Required. The project and location from which the function should be
  // listed, specified in the format `projects/*/locations/*` If you want to
  // list functions in all locations, use "-" in place of a location. When
  // listing functions in all locations, if one or more location(s) are
  // unreachable, the response will contain functions from all reachable
  // locations along with the names of any unreachable locations.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudfunctions.googleapis.com/Function"
    }
  ];

  // Maximum number of functions to return per call. The largest allowed
  // page_size is 1,000, if the page_size is omitted or specified as greater
  // than 1,000 then it will be replaced as 1,000. The size of the list
  // response can be less than specified when used with filters.
  int32 page_size = 2;

  // The value returned by the last
  // `ListFunctionsResponse`; indicates that
  // this is a continuation of a prior `ListFunctions` call, and that the
  // system should return the next page of data.
  string page_token = 3;

  // The filter for Functions that match the filter expression,
  // following the syntax outlined in https://google.aip.dev/160.
  string filter = 4;

  // The sorting order of the resources returned. Value should be a comma
  // separated list of fields. The default sorting oder is ascending.
  // See https://google.aip.dev/132#ordering.
  string order_by = 5;
}

// Response for the `ListFunctions` method.
message ListFunctionsResponse {
  // The functions that match the request.
  repeated Function functions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached. The response does not include any
  // functions from these locations.
  repeated string unreachable = 3;
}

// Request for the `CreateFunction` method.
message CreateFunctionRequest {
  // Required. The project and location in which the function should be created,
  // specified in the format `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Function to be created.
  Function function = 2 [(google.api.field_behavior) = REQUIRED];

  // The ID to use for the function, which will become the final component of
  // the function's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][0-9]-/.
  string function_id = 3;
}

// Request for the `UpdateFunction` method.
message UpdateFunctionRequest {
  // Required. New version of the function.
  Function function = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  // If no field mask is provided, all fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request for the `DeleteFunction` method.
message DeleteFunctionRequest {
  // Required. The name of the function which should be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudfunctions.googleapis.com/Function"
    }
  ];
}

// Request of `GenerateSourceUploadUrl` method.
message GenerateUploadUrlRequest {
  // Required. The project and location in which the Google Cloud Storage signed
  // URL should be generated, specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // [Preview] Resource name of a KMS crypto key (managed by the user) used to
  // encrypt/decrypt function source code objects in intermediate Cloud Storage
  // buckets. When you generate an upload url and upload your source code, it
  // gets copied to an intermediate Cloud Storage bucket. The source code is
  // then copied to a versioned directory in the sources bucket in the consumer
  // project during the function deployment.
  //
  // It must match the pattern
  // `projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}`.
  //
  // The Google Cloud Functions service account
  // (service-{project_number}@gcf-admin-robot.iam.gserviceaccount.com) must be
  // granted the role 'Cloud KMS CryptoKey Encrypter/Decrypter
  // (roles/cloudkms.cryptoKeyEncrypterDecrypter)' on the
  // Key/KeyRing/Project/Organization (least access preferred).
  string kms_key_name = 2 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];

  // The function environment the generated upload url will be used for.
  // The upload url for 2nd Gen functions can also be used for 1st gen
  // functions, but not vice versa. If not specified, 2nd generation-style
  // upload URLs are generated.
  Environment environment = 3;
}

// Response of `GenerateSourceUploadUrl` method.
message GenerateUploadUrlResponse {
  // The generated Google Cloud Storage signed URL that should be used for a
  // function source code upload. The uploaded file should be a zip archive
  // which contains a function.
  string upload_url = 1;

  // The location of the source code in the upload bucket.
  //
  // Once the archive is uploaded using the `upload_url` use this field to
  // set the `function.build_config.source.storage_source`
  // during CreateFunction and UpdateFunction.
  //
  // Generation defaults to 0, as Cloud Storage provides a new generation only
  // upon uploading a new object or version of an object.
  StorageSource storage_source = 2;
}

// Request of `GenerateDownloadUrl` method.
message GenerateDownloadUrlRequest {
  // Required. The name of function for which source code Google Cloud Storage
  // signed URL should be generated.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudfunctions.googleapis.com/Function"
    }
  ];
}

// Response of `GenerateDownloadUrl` method.
message GenerateDownloadUrlResponse {
  // The generated Google Cloud Storage signed URL that should be used for
  // function source code download.
  string download_url = 1;
}

// Request for the `ListRuntimes` method.
message ListRuntimesRequest {
  // Required. The project and location from which the runtimes should be
  // listed, specified in the format `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The filter for Runtimes that match the filter expression,
  // following the syntax outlined in https://google.aip.dev/160.
  string filter = 2;
}

// Response for the `ListRuntimes` method.
message ListRuntimesResponse {
  // Describes a runtime and any special information (e.g., deprecation status)
  // related to it.
  message Runtime {
    // The name of the runtime, e.g., 'go113', 'nodejs12', etc.
    string name = 1;

    // The user facing name, eg 'Go 1.13', 'Node.js 12', etc.
    string display_name = 5;

    // The stage of life this runtime is in, e.g., BETA, GA, etc.
    RuntimeStage stage = 2;

    // Warning messages, e.g., a deprecation warning.
    repeated string warnings = 3;

    // The environment for the runtime.
    Environment environment = 4;

    // Deprecation date for the runtime.
    google.type.Date deprecation_date = 6;

    // Decommission date for the runtime.
    google.type.Date decommission_date = 7;
  }

  // The various stages that a runtime can be in.
  enum RuntimeStage {
    // Not specified.
    RUNTIME_STAGE_UNSPECIFIED = 0;

    // The runtime is in development.
    DEVELOPMENT = 1;

    // The runtime is in the Alpha stage.
    ALPHA = 2;

    // The runtime is in the Beta stage.
    BETA = 3;

    // The runtime is generally available.
    GA = 4;

    // The runtime is deprecated.
    DEPRECATED = 5;

    // The runtime is no longer supported.
    DECOMMISSIONED = 6;
  }

  // The runtimes that match the request.
  repeated Runtime runtimes = 1;
}

// Security patches are applied automatically to the runtime without requiring
// the function to be redeployed.
message AutomaticUpdatePolicy {}

// Security patches are only applied when a function is redeployed.
message OnDeployUpdatePolicy {
  // Output only. contains the runtime version which was used during latest
  // function deployment.
  string runtime_version = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation, if any.
  string status_detail = 5;

  // Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have
  // [google.longrunning.Operation.error][google.longrunning.Operation.error]
  // value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool cancel_requested = 6;

  // API version used to start the operation.
  string api_version = 7;

  // The original request that started the operation.
  google.protobuf.Any request_resource = 8;

  // Mechanism for reporting in-progress stages
  repeated Stage stages = 9;

  // An identifier for Firebase function sources. Disclaimer: This field is only
  // supported for Firebase function deployments.
  string source_token = 10;

  // The build name of the function for create and update operations.
  string build_name = 13;

  // The operation type.
  OperationType operation_type = 11;
}

// Extra GCF specific location information.
message LocationMetadata {
  // The Cloud Function environments this location supports.
  repeated Environment environments = 1;
}

// Each Stage of the deployment process
message Stage {
  // Possible names for a Stage
  enum Name {
    // Not specified. Invalid name.
    NAME_UNSPECIFIED = 0;

    // Artifact Regsitry Stage
    ARTIFACT_REGISTRY = 1;

    // Build Stage
    BUILD = 2;

    // Service Stage
    SERVICE = 3;

    // Trigger Stage
    TRIGGER = 4;

    // Service Rollback Stage
    SERVICE_ROLLBACK = 5;

    // Trigger Rollback Stage
    TRIGGER_ROLLBACK = 6;
  }

  // Possible states for a Stage
  enum State {
    // Not specified. Invalid state.
    STATE_UNSPECIFIED = 0;

    // Stage has not started.
    NOT_STARTED = 1;

    // Stage is in progress.
    IN_PROGRESS = 2;

    // Stage has completed.
    COMPLETE = 3;
  }

  // Name of the Stage. This will be unique for each Stage.
  Name name = 1;

  // Message describing the Stage
  string message = 2;

  // Current state of the Stage
  State state = 3;

  // Resource of the Stage
  string resource = 4;

  // Link to the current Stage resource
  string resource_uri = 5;

  // State messages from the current Stage.
  repeated StateMessage state_messages = 6;
}
