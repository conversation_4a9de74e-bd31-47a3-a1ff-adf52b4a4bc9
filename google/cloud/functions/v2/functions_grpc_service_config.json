{"methodConfig": [{"name": [{"service": "google.cloud.functions.v2.CloudFunctionsService", "method": "ListFunctions"}, {"service": "google.cloud.functions.v2.CloudFunctionsService", "method": "GetFunction"}, {"service": "google.cloud.functions.v2.CloudFunctionsService", "method": "UpdateFunction"}, {"service": "google.cloud.functions.v2.CloudFunctionsService", "method": "DeleteFunction"}, {"service": "google.cloud.functions.v2.CloudFunctionsService", "method": "ListRuntimes"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.functions.v2.CloudFunctionsService", "method": "CreateFunction"}], "timeout": "600s"}]}