// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.functions.v1;

import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/functions/apiv1/functionspb;functionspb";
option java_multiple_files = true;
option java_outer_classname = "FunctionsOperationsProto";
option java_package = "com.google.cloud.functions.v1";

// A type of an operation.
enum OperationType {
  // Unknown operation type.
  OPERATION_UNSPECIFIED = 0;

  // Triggered by <PERSON>reateFunction call
  CREATE_FUNCTION = 1;

  // Triggered by UpdateFunction call
  UPDATE_FUNCTION = 2;

  // Triggered by DeleteFunction call.
  DELETE_FUNCTION = 3;
}

// Metadata describing an [Operation][google.longrunning.Operation]
message OperationMetadataV1 {
  // Target of the operation - for example
  // `projects/project-1/locations/region-1/functions/function-1`
  string target = 1;

  // Type of operation.
  OperationType type = 2;

  // The original request that started the operation.
  google.protobuf.Any request = 3;

  // Version id of the function created or updated by an API call.
  // This field is only populated for Create and Update operations.
  int64 version_id = 4;

  // The last update timestamp of the operation.
  google.protobuf.Timestamp update_time = 5;

  // The Cloud Build ID of the function created or updated by an API call.
  // This field is only populated for Create and Update operations.
  string build_id = 6;

  // An identifier for Firebase function sources. Disclaimer: This field is only
  // supported for Firebase function deployments.
  string source_token = 7;

  // The Cloud Build Name of the function deployment.
  // This field is only populated for Create and Update operations.
  // `projects/<project-number>/locations/<region>/builds/<build-id>`.
  string build_name = 8;
}
