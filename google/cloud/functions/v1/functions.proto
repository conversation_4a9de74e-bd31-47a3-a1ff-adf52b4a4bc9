// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.functions.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/functions/v1/operations.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/functions/apiv1/functionspb;functionspb";
option java_multiple_files = true;
option java_outer_classname = "FunctionsProto";
option java_package = "com.google.cloud.functions.v1";
option objc_class_prefix = "GCF";
option (google.api.resource_definition) = {
  type: "artifactregistry.googleapis.com/Repository"
  pattern: "projects/{project}/locations/{location}/repositories/{repository}"
};
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};

// A service that application uses to manipulate triggers and functions.
service CloudFunctionsService {
  option (google.api.default_host) = "cloudfunctions.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Returns a list of functions that belong to the requested project.
  rpc ListFunctions(ListFunctionsRequest) returns (ListFunctionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/functions"
    };
  }

  // Returns a function with the given name from the requested project.
  rpc GetFunction(GetFunctionRequest) returns (CloudFunction) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/functions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new function. If a function with the given name already exists in
  // the specified project, the long running operation will return
  // `ALREADY_EXISTS` error.
  rpc CreateFunction(CreateFunctionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{location=projects/*/locations/*}/functions"
      body: "function"
    };
    option (google.api.method_signature) = "location,function";
    option (google.longrunning.operation_info) = {
      response_type: "CloudFunction"
      metadata_type: "OperationMetadataV1"
    };
  }

  // Updates existing function.
  rpc UpdateFunction(UpdateFunctionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{function.name=projects/*/locations/*/functions/*}"
      body: "function"
    };
    option (google.api.method_signature) = "function";
    option (google.longrunning.operation_info) = {
      response_type: "CloudFunction"
      metadata_type: "OperationMetadataV1"
    };
  }

  // Deletes a function with the given name from the specified project. If the
  // given function is used by some trigger, the trigger will be updated to
  // remove this function.
  rpc DeleteFunction(DeleteFunctionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/functions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadataV1"
    };
  }

  // Synchronously invokes a deployed Cloud Function. To be used for testing
  // purposes as very limited traffic is allowed. For more information on
  // the actual limits, refer to
  // [Rate Limits](https://cloud.google.com/functions/quotas#rate_limits).
  rpc CallFunction(CallFunctionRequest) returns (CallFunctionResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/functions/*}:call"
      body: "*"
    };
    option (google.api.method_signature) = "name,data";
  }

  // Returns a signed URL for uploading a function source code.
  // For more information about the signed URL usage see:
  // https://cloud.google.com/storage/docs/access-control/signed-urls.
  // Once the function source code upload is complete, the used signed
  // URL should be provided in CreateFunction or UpdateFunction request
  // as a reference to the function source code.
  //
  // When uploading source code to the generated signed URL, please follow
  // these restrictions:
  //
  // * Source file type should be a zip file.
  // * Source file size should not exceed 100MB limit.
  // * No credentials should be attached - the signed URLs provide access to the
  //   target bucket using internal service identity; if credentials were
  //   attached, the identity from the credentials would be used, but that
  //   identity does not have permissions to upload files to the URL.
  //
  // When making a HTTP PUT request, these two headers need to be specified:
  //
  // * `content-type: application/zip`
  // * `x-goog-content-length-range: 0,104857600`
  //
  // And this header SHOULD NOT be specified:
  //
  // * `Authorization: Bearer YOUR_TOKEN`
  rpc GenerateUploadUrl(GenerateUploadUrlRequest)
      returns (GenerateUploadUrlResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/functions:generateUploadUrl"
      body: "*"
    };
  }

  // Returns a signed URL for downloading deployed function source code.
  // The URL is only valid for a limited period and should be used within
  // minutes after generation.
  // For more information about the signed URL usage see:
  // https://cloud.google.com/storage/docs/access-control/signed-urls
  rpc GenerateDownloadUrl(GenerateDownloadUrlRequest)
      returns (GenerateDownloadUrlResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/functions/*}:generateDownloadUrl"
      body: "*"
    };
  }

  // Sets the IAM access control policy on the specified function.
  // Replaces any existing policy.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/functions/*}:setIamPolicy"
      body: "*"
    };
  }

  // Gets the IAM access control policy for a function.
  // Returns an empty policy if the function exists and does not have a policy
  // set.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      get: "/v1/{resource=projects/*/locations/*/functions/*}:getIamPolicy"
    };
  }

  // Tests the specified permissions against the IAM access control policy
  // for a function.
  // If the function does not exist, this will return an empty set of
  // permissions, not a NOT_FOUND error.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/functions/*}:testIamPermissions"
      body: "*"
    };
  }
}

// Describes a Cloud Function that contains user computation executed in
// response to an event. It encapsulate function and triggers configurations.
message CloudFunction {
  option (google.api.resource) = {
    type: "cloudfunctions.googleapis.com/CloudFunction"
    pattern: "projects/{project}/locations/{location}/functions/{function}"
  };

  // Available egress settings.
  //
  // This controls what traffic is diverted through the VPC Access Connector
  // resource. By default PRIVATE_RANGES_ONLY will be used.
  enum VpcConnectorEgressSettings {
    // Unspecified.
    VPC_CONNECTOR_EGRESS_SETTINGS_UNSPECIFIED = 0;

    // Use the VPC Access Connector only for private IP space from RFC1918.
    PRIVATE_RANGES_ONLY = 1;

    // Force the use of VPC Access Connector for all egress traffic from the
    // function.
    ALL_TRAFFIC = 2;
  }

  // Available ingress settings.
  //
  // This controls what traffic can reach the function.
  //
  // If unspecified, ALLOW_ALL will be used.
  enum IngressSettings {
    // Unspecified.
    INGRESS_SETTINGS_UNSPECIFIED = 0;

    // Allow HTTP traffic from public and private sources.
    ALLOW_ALL = 1;

    // Allow HTTP traffic from only private VPC sources.
    ALLOW_INTERNAL_ONLY = 2;

    // Allow HTTP traffic from private VPC sources and through GCLB.
    ALLOW_INTERNAL_AND_GCLB = 3;
  }

  // Docker Registry to use for storing function Docker images.
  enum DockerRegistry {
    // Unspecified.
    DOCKER_REGISTRY_UNSPECIFIED = 0;

    // Docker images will be stored in multi-regional Container Registry
    // repositories named `gcf`.
    CONTAINER_REGISTRY = 1;

    // Docker images will be stored in regional Artifact Registry repositories.
    // By default, GCF will create and use repositories named `gcf-artifacts`
    // in every region in which a function is deployed. But the repository to
    // use can also be specified by the user using the `docker_repository`
    // field.
    ARTIFACT_REGISTRY = 2;
  }

  // Security patches are applied automatically to the runtime without requiring
  // the function to be redeployed.
  message AutomaticUpdatePolicy {}

  // Security patches are only applied when a function is redeployed.
  message OnDeployUpdatePolicy {
    // Output only. Contains the runtime version which was used during latest
    // function deployment.
    string runtime_version = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // A user-defined name of the function. Function names must be unique
  // globally and match pattern `projects/*/locations/*/functions/*`
  string name = 1;

  // User-provided description of a function.
  string description = 2;

  // The location of the function source code.
  oneof source_code {
    // The Google Cloud Storage URL, starting with `gs://`, pointing to the zip
    // archive which contains the function.
    string source_archive_url = 3;

    // **Beta Feature**
    //
    // The source repository where a function is hosted.
    SourceRepository source_repository = 4;

    // The Google Cloud Storage signed URL used for source uploading, generated
    // by calling [google.cloud.functions.v1.GenerateUploadUrl].
    //
    // The signature is validated on write methods (Create, Update)
    // The signature is stripped from the Function object on read methods (Get,
    // List)
    string source_upload_url = 16;
  }

  // An event that triggers the function.
  oneof trigger {
    // An HTTPS endpoint type of source that can be triggered via URL.
    HttpsTrigger https_trigger = 5;

    // A source that fires events in response to a condition in another service.
    EventTrigger event_trigger = 6;
  }

  // Output only. Status of the function deployment.
  CloudFunctionStatus status = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The name of the function (as defined in source code) that will be
  // executed. Defaults to the resource name suffix (ID of the function), if not
  // specified.
  string entry_point = 8;

  // The runtime in which to run the function. Required when deploying a new
  // function, optional when updating an existing function. For a complete
  // list of possible choices, see the
  // [`gcloud` command
  // reference](https://cloud.google.com/sdk/gcloud/reference/functions/deploy#--runtime).
  string runtime = 19;

  // The function execution timeout. Execution is considered failed and
  // can be terminated if the function is not completed at the end of the
  // timeout period. Defaults to 60 seconds.
  google.protobuf.Duration timeout = 9;

  // The amount of memory in MB available for a function.
  // Defaults to 256MB.
  int32 available_memory_mb = 10;

  // The email of the function's service account. If empty, defaults to
  // `{project_id}@appspot.gserviceaccount.com`.
  string service_account_email = 11;

  // Output only. The last update timestamp of a Cloud Function.
  google.protobuf.Timestamp update_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The version identifier of the Cloud Function. Each deployment
  // attempt results in a new version of a function being created.
  int64 version_id = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels associated with this Cloud Function.
  map<string, string> labels = 15;

  // Environment variables that shall be available during function execution.
  map<string, string> environment_variables = 17;

  // Build environment variables that shall be available during build time.
  map<string, string> build_environment_variables = 28;

  // Deprecated: use vpc_connector
  string network = 18 [deprecated = true];

  // The limit on the maximum number of function instances that may coexist at a
  // given time.
  //
  // In some cases, such as rapid traffic surges, Cloud Functions may, for a
  // short period of time, create more instances than the specified max
  // instances limit. If your function cannot tolerate this temporary behavior,
  // you may want to factor in a safety margin and set a lower max instances
  // value than your function can tolerate.
  //
  // See the [Max
  // Instances](https://cloud.google.com/functions/docs/max-instances) Guide for
  // more details.
  int32 max_instances = 20;

  // A lower bound for the number function instances that may coexist at a
  // given time.
  int32 min_instances = 32;

  // The VPC Network Connector that this cloud function can connect to. It can
  // be either the fully-qualified URI, or the short name of the network
  // connector resource. The format of this field is
  // `projects/*/locations/*/connectors/*`
  //
  // This field is mutually exclusive with `network` field and will eventually
  // replace it.
  //
  // See [the VPC documentation](https://cloud.google.com/compute/docs/vpc) for
  // more information on connecting Cloud projects.
  string vpc_connector = 22;

  // The egress settings for the connector, controlling what traffic is diverted
  // through it.
  VpcConnectorEgressSettings vpc_connector_egress_settings = 23;

  // The ingress settings for the function, controlling what traffic can reach
  // it.
  IngressSettings ingress_settings = 24;

  // Resource name of a KMS crypto key (managed by the user) used to
  // encrypt/decrypt function resources.
  //
  // It must match the pattern
  // `projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}`.
  //
  // If specified, you must also provide an artifact registry repository using
  // the `docker_repository` field that was created with the same KMS crypto
  // key.
  //
  // The following service accounts need to be granted the role 'Cloud KMS
  // CryptoKey Encrypter/Decrypter (roles/cloudkms.cryptoKeyEncrypterDecrypter)'
  // on the Key/KeyRing/Project/Organization (least access preferred).
  //
  // 1. Google Cloud Functions service account
  //    (service-{project_number}@gcf-admin-robot.iam.gserviceaccount.com) -
  //    Required to protect the function's image.
  // 2. Google Storage service account
  //    (service-{project_number}@gs-project-accounts.iam.gserviceaccount.com) -
  //    Required to protect the function's source code.
  //    If this service account does not exist, deploying a function without a
  //    KMS key or retrieving the service agent name provisions it. For more
  //    information, see
  //    https://cloud.google.com/storage/docs/projects#service-agents and
  //    https://cloud.google.com/storage/docs/getting-service-agent#gsutil.
  //
  // Google Cloud Functions delegates access to service agents to protect
  // function resources in internal projects that are not accessible by the
  // end user.
  string kms_key_name = 25 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];

  // Name of the Cloud Build Custom Worker Pool that should be used to build the
  // function. The format of this field is
  // `projects/{project}/locations/{region}/workerPools/{workerPool}` where
  // `{project}` and `{region}` are the project id and region respectively where
  // the worker pool is defined and `{workerPool}` is the short name of the
  // worker pool.
  //
  // If the project id is not the same as the function, then the Cloud
  // Functions Service Agent
  // (`service-<project_number>@gcf-admin-robot.iam.gserviceaccount.com`) must
  // be granted the role Cloud Build Custom Workers Builder
  // (`roles/cloudbuild.customworkers.builder`) in the project.
  string build_worker_pool = 26;

  // Output only. The Cloud Build ID of the latest successful deployment of the
  // function.
  string build_id = 27 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Cloud Build Name of the function deployment.
  // `projects/<project-number>/locations/<region>/builds/<build-id>`.
  string build_name = 33 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Secret environment variables configuration.
  repeated SecretEnvVar secret_environment_variables = 29;

  // Secret volumes configuration.
  repeated SecretVolume secret_volumes = 30;

  // Input only. An identifier for Firebase function sources. Disclaimer: This
  // field is only supported for Firebase function deployments.
  string source_token = 31 [(google.api.field_behavior) = INPUT_ONLY];

  // User-managed repository created in Artifact Registry to which the
  // function's Docker image will be pushed after it is built by Cloud Build.
  // May optionally be encrypted with a customer-managed encryption key (CMEK).
  // If unspecified and `docker_registry` is not explicitly set to
  // `CONTAINER_REGISTRY`, GCF will create and use a default Artifact Registry
  // repository named 'gcf-artifacts' in the region.
  //
  // It must match the pattern
  // `projects/{project}/locations/{location}/repositories/{repository}`.
  //
  // Cross-project repositories are not supported.
  // Cross-location repositories are not supported.
  // Repository format must be 'DOCKER'.
  string docker_repository = 34 [(google.api.resource_reference) = {
    type: "artifactregistry.googleapis.com/Repository"
  }];

  // Docker Registry to use for this deployment.
  //
  // If unspecified, it defaults to `ARTIFACT_REGISTRY`.
  // If `docker_repository` field is specified, this field should either be left
  // unspecified or set to `ARTIFACT_REGISTRY`.
  DockerRegistry docker_registry = 35;

  // This controls when security patches are applied to the runtime environment.
  oneof runtime_update_policy {
    AutomaticUpdatePolicy automatic_update_policy = 40;

    OnDeployUpdatePolicy on_deploy_update_policy = 41;
  }

  // A service account the user provides for use with Cloud Build. The format of
  // this field is
  // `projects/{projectId}/serviceAccounts/{serviceAccountEmail}`.
  string build_service_account = 43;
}

// Describes SourceRepository, used to represent parameters related to
// source repository where a function is hosted.
message SourceRepository {
  // The URL pointing to the hosted repository where the function is defined.
  // There are supported Cloud Source Repository URLs in the following
  // formats:
  //
  // To refer to a specific commit:
  // `https://source.developers.google.com/projects/*/repos/*/revisions/*/paths/*`
  // To refer to a moveable alias (branch):
  // `https://source.developers.google.com/projects/*/repos/*/moveable-aliases/*/paths/*`
  // In particular, to refer to HEAD use `master` moveable alias.
  // To refer to a specific fixed alias (tag):
  // `https://source.developers.google.com/projects/*/repos/*/fixed-aliases/*/paths/*`
  //
  // You may omit `paths/*` if you want to use the main directory. The function
  // response may add an empty `/paths/` to the URL.
  string url = 1;

  // Output only. The URL pointing to the hosted repository where the function
  // were defined at the time of deployment. It always points to a specific
  // commit in the format described above.
  string deployed_url = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Describes HttpsTrigger, could be used to connect web hooks to function.
message HttpsTrigger {
  // Available security level settings.
  //
  // This controls the methods to enforce security (HTTPS) on a URL.
  //
  // If unspecified, SECURE_OPTIONAL will be used.
  enum SecurityLevel {
    // Unspecified.
    SECURITY_LEVEL_UNSPECIFIED = 0;

    // Requests for a URL that match this handler that do not use HTTPS are
    // automatically redirected to the HTTPS URL with the same path. Query
    // parameters are reserved for the redirect.
    SECURE_ALWAYS = 1;

    // Both HTTP and HTTPS requests with URLs that match the handler succeed
    // without redirects. The application can examine the request to determine
    // which protocol was used and respond accordingly.
    SECURE_OPTIONAL = 2;
  }

  // Output only. The deployed url for the function.
  string url = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The security level for the function.
  SecurityLevel security_level = 2;
}

// Describes EventTrigger, used to request events be sent from another
// service.
message EventTrigger {
  // Required. The type of event to observe. For example:
  // `providers/cloud.storage/eventTypes/object.change` and
  // `providers/cloud.pubsub/eventTypes/topic.publish`.
  //
  // Event types match pattern `providers/*/eventTypes/*.*`.
  // The pattern contains:
  //
  // 1. namespace: For example, `cloud.storage` and
  //    `google.firebase.analytics`.
  // 2. resource type: The type of resource on which event occurs. For
  //    example, the Google Cloud Storage API includes the type `object`.
  // 3. action: The action that generates the event. For example, action for
  //    a Google Cloud Storage Object is 'change'.
  // These parts are lower case.
  string event_type = 1;

  // Required. The resource(s) from which to observe events, for example,
  // `projects/_/buckets/myBucket`.
  //
  // Not all syntactically correct values are accepted by all services. For
  // example:
  //
  // 1. The authorization model must support it. Google Cloud Functions
  //    only allows EventTriggers to be deployed that observe resources in the
  //    same project as the `CloudFunction`.
  // 2. The resource type must match the pattern expected for an
  //    `event_type`. For example, an `EventTrigger` that has an
  //    `event_type` of "google.pubsub.topic.publish" should have a resource
  //    that matches Google Cloud Pub/Sub topics.
  //
  // Additionally, some services may support short names when creating an
  // `EventTrigger`. These will always be returned in the normalized "long"
  // format.
  //
  // See each *service's* documentation for supported formats.
  string resource = 2;

  // The hostname of the service that should be observed.
  //
  // If no string is provided, the default service implementing the API will
  // be used. For example, `storage.googleapis.com` is the default for all
  // event types in the `google.storage` namespace.
  string service = 3;

  // Specifies policy for failed executions.
  FailurePolicy failure_policy = 5;
}

// Describes the policy in case of function's execution failure.
// If empty, then defaults to ignoring failures (i.e. not retrying them).
message FailurePolicy {
  // Describes the retry policy in case of function's execution failure.
  // A function execution will be retried on any failure.
  // A failed execution will be retried up to 7 days with an exponential backoff
  // (capped at 10 seconds).
  // Retried execution is charged as any other execution.
  message Retry {}

  // Defines the action taken in case of a function execution failure.
  oneof action {
    // If specified, then the function will be retried in case of a failure.
    Retry retry = 1;
  }
}

// Describes the current stage of a deployment.
enum CloudFunctionStatus {
  // Not specified. Invalid state.
  CLOUD_FUNCTION_STATUS_UNSPECIFIED = 0;

  // Function has been successfully deployed and is serving.
  ACTIVE = 1;

  // Function deployment failed and the function isn’t serving.
  OFFLINE = 2;

  // Function is being created or updated.
  DEPLOY_IN_PROGRESS = 3;

  // Function is being deleted.
  DELETE_IN_PROGRESS = 4;

  // Function deployment failed and the function serving state is undefined.
  // The function should be updated or deleted to move it out of this state.
  UNKNOWN = 5;
}

// Configuration for a secret environment variable. It has the information
// necessary to fetch the secret value from secret manager and expose it as an
// environment variable.
message SecretEnvVar {
  // Name of the environment variable.
  string key = 1;

  // Project identifier (preferrably project number but can also be the project
  // ID) of the project that contains the secret. If not set, it will be
  // populated with the function's project assuming that the secret exists in
  // the same project as of the function.
  string project_id = 2;

  // Name of the secret in secret manager (not the full resource name).
  string secret = 3;

  // Version of the secret (version number or the string 'latest'). It is
  // recommended to use a numeric version for secret environment variables as
  // any updates to the secret value is not reflected until new instances start.
  string version = 4;
}

// Configuration for a secret volume. It has the information necessary to fetch
// the secret value from secret manager and make it available as files mounted
// at the requested paths within the application container. Secret value is not
// a part of the configuration. Every filesystem read operation performs a
// lookup in secret manager to retrieve the secret value.
message SecretVolume {
  // Configuration for a single version.
  message SecretVersion {
    // Version of the secret (version number or the string 'latest'). It is
    // preferable to use `latest` version with secret volumes as secret value
    // changes are reflected immediately.
    string version = 1;

    // Relative path of the file under the mount path where the secret value for
    // this version will be fetched and made available. For example, setting the
    // mount_path as '/etc/secrets' and path as `/secret_foo` would mount the
    // secret value file at `/etc/secrets/secret_foo`.
    string path = 2;
  }

  // The path within the container to mount the secret volume. For example,
  // setting the mount_path as `/etc/secrets` would mount the secret value files
  // under the `/etc/secrets` directory. This directory will also be completely
  // shadowed and unavailable to mount any other secrets.
  //
  // Recommended mount paths: /etc/secrets
  // Restricted mount paths: /cloudsql, /dev/log, /pod, /proc, /var/log
  string mount_path = 1;

  // Project identifier (preferrably project number but can also be the project
  // ID) of the project that contains the secret. If not set, it will be
  // populated with the function's project assuming that the secret exists in
  // the same project as of the function.
  string project_id = 2;

  // Name of the secret in secret manager (not the full resource name).
  string secret = 3;

  // List of secret versions to mount for this secret. If empty, the `latest`
  // version of the secret will be made available in a file named after the
  // secret under the mount point.
  repeated SecretVersion versions = 4;
}

// Request for the `CreateFunction` method.
message CreateFunctionRequest {
  // Required. The project and location in which the function should be created,
  // specified in the format `projects/*/locations/*`
  string location = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Function to be created.
  CloudFunction function = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `UpdateFunction` method.
message UpdateFunctionRequest {
  // Required. New version of the function.
  CloudFunction function = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields in `CloudFunction` that have to be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request for the `GetFunction` method.
message GetFunctionRequest {
  // Required. The name of the function which details should be obtained.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudfunctions.googleapis.com/CloudFunction"
    }
  ];

  // Optional. The optional version of the function whose details should be
  // obtained. The version of a 1st Gen function is an integer that starts from
  // 1 and gets incremented on redeployments. Each deployment creates a config
  // version of the underlying function. GCF may keep historical configs for old
  // versions. This field can be specified to fetch the historical configs.
  // Leave it blank or set to 0 to get the latest version of the function.
  int64 version_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for the `ListFunctions` method.
message ListFunctionsRequest {
  // The project and location from which the function should be listed,
  // specified in the format `projects/*/locations/*`
  // If you want to list functions in all locations, use "-" in place of a
  // location. When listing functions in all locations, if one or more
  // location(s) are unreachable, the response will contain functions from all
  // reachable locations along with the names of any unreachable locations.
  string parent = 1 [(google.api.resource_reference) = {
    type: "locations.googleapis.com/Location"
  }];

  // Maximum number of functions to return per call.
  int32 page_size = 2;

  // The value returned by the last
  // `ListFunctionsResponse`; indicates that
  // this is a continuation of a prior `ListFunctions` call, and that the
  // system should return the next page of data.
  string page_token = 3;
}

// Response for the `ListFunctions` method.
message ListFunctionsResponse {
  // The functions that match the request.
  repeated CloudFunction functions = 1;

  // If not empty, indicates that there may be more functions that match
  // the request; this value should be passed in a new
  // [google.cloud.functions.v1.ListFunctionsRequest][google.cloud.functions.v1.ListFunctionsRequest]
  // to get more functions.
  string next_page_token = 2;

  // Locations that could not be reached. The response does not include any
  // functions from these locations.
  repeated string unreachable = 3;
}

// Request for the `DeleteFunction` method.
message DeleteFunctionRequest {
  // Required. The name of the function which should be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudfunctions.googleapis.com/CloudFunction"
    }
  ];
}

// Request for the `CallFunction` method.
message CallFunctionRequest {
  // Required. The name of the function to be called.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudfunctions.googleapis.com/CloudFunction"
    }
  ];

  // Required. Input to be passed to the function.
  string data = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response of `CallFunction` method.
message CallFunctionResponse {
  // Execution id of function invocation.
  string execution_id = 1;

  // Result populated for successful execution of synchronous function. Will
  // not be populated if function does not return a result through context.
  string result = 2;

  // Either system or user-function generated error. Set if execution
  // was not successful.
  string error = 3;
}

// Request of `GenerateSourceUploadUrl` method.
message GenerateUploadUrlRequest {
  // The project and location in which the Google Cloud Storage signed URL
  // should be generated, specified in the format `projects/*/locations/*`.
  string parent = 1;

  // Resource name of a KMS crypto key (managed by the user) used to
  // encrypt/decrypt function source code objects in intermediate Cloud Storage
  // buckets. When you generate an upload url and upload your source code, it
  // gets copied to an intermediate Cloud Storage bucket. The source code is
  // then copied to a versioned directory in the sources bucket in the consumer
  // project during the function deployment.
  //
  // It must match the pattern
  // `projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}`.
  //
  // The Google Cloud Functions service account
  // (service-{project_number}@gcf-admin-robot.iam.gserviceaccount.com) must be
  // granted the role 'Cloud KMS CryptoKey Encrypter/Decrypter
  // (roles/cloudkms.cryptoKeyEncrypterDecrypter)' on the
  // Key/KeyRing/Project/Organization (least access preferred). GCF will
  // delegate access to the Google Storage service account in the internal
  // project.
  string kms_key_name = 2 [(google.api.resource_reference) = {
    type: "cloudkms.googleapis.com/CryptoKey"
  }];
}

// Response of `GenerateSourceUploadUrl` method.
message GenerateUploadUrlResponse {
  // The generated Google Cloud Storage signed URL that should be used for a
  // function source code upload. The uploaded file should be a zip archive
  // which contains a function.
  string upload_url = 1;
}

// Request of `GenerateDownloadUrl` method.
message GenerateDownloadUrlRequest {
  // The name of function for which source code Google Cloud Storage signed
  // URL should be generated.
  string name = 1;

  // The optional version of function. If not set, default, current version
  // is used.
  uint64 version_id = 2;
}

// Response of `GenerateDownloadUrl` method.
message GenerateDownloadUrlResponse {
  // The generated Google Cloud Storage signed URL that should be used for
  // function source code download.
  string download_url = 1;
}
