# This build file includes a target for the Ruby wrapper library for
# google-cloud-contact_center_insights.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for contactcenterinsights.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "contactcenterinsights_ruby_wrapper",
    srcs = ["//google/cloud/contactcenterinsights/v1:contactcenterinsights_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-contact_center_insights",
        "ruby-cloud-wrapper-of=v1:0.20",
        "ruby-cloud-product-url=https://cloud.google.com/contact-center/insights/docs",
        "ruby-cloud-api-id=contactcenterinsights.googleapis.com",
        "ruby-cloud-api-shortname=contactcenterinsights",
    ],
    ruby_cloud_description = "Contact Center AI Insights helps users detect and visualize patterns in their contact center data. Understanding conversational data drives business value, improves operational efficiency, and provides a voice for customer feedback.",
    ruby_cloud_title = "Contact Center AI Insights",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-contactcenterinsights-ruby",
    deps = [
        ":contactcenterinsights_ruby_wrapper",
    ],
)
