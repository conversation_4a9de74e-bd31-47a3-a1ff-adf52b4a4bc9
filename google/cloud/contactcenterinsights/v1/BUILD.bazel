# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "contactcenterinsights_proto",
    srcs = [
        "contact_center_insights.proto",
        "resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:interval_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "contactcenterinsights_proto_with_info",
    deps = [
        ":contactcenterinsights_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "contactcenterinsights_java_proto",
    deps = [":contactcenterinsights_proto"],
)

java_grpc_library(
    name = "contactcenterinsights_java_grpc",
    srcs = [":contactcenterinsights_proto"],
    deps = [":contactcenterinsights_java_proto"],
)

java_gapic_library(
    name = "contactcenterinsights_java_gapic",
    srcs = [":contactcenterinsights_proto_with_info"],
    grpc_service_config = "contactcenterinsights_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "contactcenterinsights_v1.yaml",
    test_deps = [
        ":contactcenterinsights_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":contactcenterinsights_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "contactcenterinsights_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.contactcenterinsights.v1.ContactCenterInsightsClientHttpJsonTest",
        "com.google.cloud.contactcenterinsights.v1.ContactCenterInsightsClientTest",
    ],
    runtime_deps = [":contactcenterinsights_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-contactcenterinsights-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":contactcenterinsights_java_gapic",
        ":contactcenterinsights_java_grpc",
        ":contactcenterinsights_java_proto",
        ":contactcenterinsights_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "contactcenterinsights_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/contactcenterinsights/apiv1/contactcenterinsightspb",
    protos = [":contactcenterinsights_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:interval_go_proto",
    ],
)

go_gapic_library(
    name = "contactcenterinsights_go_gapic",
    srcs = [":contactcenterinsights_proto_with_info"],
    grpc_service_config = "contactcenterinsights_grpc_service_config.json",
    importpath = "cloud.google.com/go/contactcenterinsights/apiv1;contactcenterinsights",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "contactcenterinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":contactcenterinsights_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-contactcenterinsights-v1-go",
    deps = [
        ":contactcenterinsights_go_gapic",
        ":contactcenterinsights_go_gapic_srcjar-metadata.srcjar",
        ":contactcenterinsights_go_gapic_srcjar-snippets.srcjar",
        ":contactcenterinsights_go_gapic_srcjar-test.srcjar",
        ":contactcenterinsights_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "contactcenterinsights_py_gapic",
    srcs = [":contactcenterinsights_proto"],
    grpc_service_config = "contactcenterinsights_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-contact-center-insights",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=contact_center_insights",
    ],
    rest_numeric_enums = True,
    service_yaml = "contactcenterinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "contactcenterinsights_py_gapic_test",
    srcs = [
        "contactcenterinsights_py_gapic_pytest.py",
        "contactcenterinsights_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":contactcenterinsights_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "contactcenterinsights-v1-py",
    deps = [
        ":contactcenterinsights_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "contactcenterinsights_php_proto",
    deps = [":contactcenterinsights_proto"],
)

php_gapic_library(
    name = "contactcenterinsights_php_gapic",
    srcs = [":contactcenterinsights_proto_with_info"],
    grpc_service_config = "contactcenterinsights_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "contactcenterinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [":contactcenterinsights_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-contactcenterinsights-v1-php",
    deps = [
        ":contactcenterinsights_php_gapic",
        ":contactcenterinsights_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "contactcenterinsights_nodejs_gapic",
    package_name = "@google-cloud/contact-center-insights",
    src = ":contactcenterinsights_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "contactcenterinsights_grpc_service_config.json",
    package = "google.cloud.contactcenterinsights.v1",
    rest_numeric_enums = True,
    service_yaml = "contactcenterinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "contactcenterinsights-v1-nodejs",
    deps = [
        ":contactcenterinsights_nodejs_gapic",
        ":contactcenterinsights_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "contactcenterinsights_ruby_proto",
    deps = [":contactcenterinsights_proto"],
)

ruby_grpc_library(
    name = "contactcenterinsights_ruby_grpc",
    srcs = [":contactcenterinsights_proto"],
    deps = [":contactcenterinsights_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "contactcenterinsights_ruby_gapic",
    srcs = [":contactcenterinsights_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-contact_center_insights-v1",
        "ruby-cloud-product-url=https://cloud.google.com/contact-center/insights/docs",
        "ruby-cloud-api-id=contactcenterinsights.googleapis.com",
        "ruby-cloud-api-shortname=contactcenterinsights",
    ],
    grpc_service_config = "contactcenterinsights_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Contact Center AI Insights helps users detect and visualize patterns in their contact center data. Understanding conversational data drives business value, improves operational efficiency, and provides a voice for customer feedback.",
    ruby_cloud_title = "Contact Center AI Insights V1",
    service_yaml = "contactcenterinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":contactcenterinsights_ruby_grpc",
        ":contactcenterinsights_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-contactcenterinsights-v1-ruby",
    deps = [
        ":contactcenterinsights_ruby_gapic",
        ":contactcenterinsights_ruby_grpc",
        ":contactcenterinsights_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "contactcenterinsights_csharp_proto",
    deps = [":contactcenterinsights_proto"],
)

csharp_grpc_library(
    name = "contactcenterinsights_csharp_grpc",
    srcs = [":contactcenterinsights_proto"],
    deps = [":contactcenterinsights_csharp_proto"],
)

csharp_gapic_library(
    name = "contactcenterinsights_csharp_gapic",
    srcs = [":contactcenterinsights_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "contactcenterinsights_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "contactcenterinsights_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":contactcenterinsights_csharp_grpc",
        ":contactcenterinsights_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-contactcenterinsights-v1-csharp",
    deps = [
        ":contactcenterinsights_csharp_gapic",
        ":contactcenterinsights_csharp_grpc",
        ":contactcenterinsights_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "contactcenterinsights_cc_proto",
    deps = [":contactcenterinsights_proto"],
)

cc_grpc_library(
    name = "contactcenterinsights_cc_grpc",
    srcs = [":contactcenterinsights_proto"],
    grpc_only = True,
    deps = [":contactcenterinsights_cc_proto"],
)
