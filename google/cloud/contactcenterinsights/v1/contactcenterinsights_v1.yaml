type: google.api.Service
config_version: 3
name: contactcenterinsights.googleapis.com
title: Contact Center AI Insights API

apis:
- name: google.cloud.contactcenterinsights.v1.ContactCenterInsights
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.contactcenterinsights.v1.BulkAnalyzeConversationsMetadata
- name: google.cloud.contactcenterinsights.v1.BulkAnalyzeConversationsResponse
- name: google.cloud.contactcenterinsights.v1.BulkDeleteConversationsMetadata
- name: google.cloud.contactcenterinsights.v1.BulkDeleteConversationsResponse
- name: google.cloud.contactcenterinsights.v1.BulkDownloadFeedbackLabelsMetadata
- name: google.cloud.contactcenterinsights.v1.BulkDownloadFeedbackLabelsResponse
- name: google.cloud.contactcenterinsights.v1.CreateAnalysisOperationMetadata
- name: google.cloud.contactcenterinsights.v1.CreateIssueModelMetadata
- name: google.cloud.contactcenterinsights.v1.DeleteIssueModelMetadata
- name: google.cloud.contactcenterinsights.v1.DeployIssueModelMetadata
- name: google.cloud.contactcenterinsights.v1.DeployIssueModelResponse
- name: google.cloud.contactcenterinsights.v1.ExportInsightsDataMetadata
- name: google.cloud.contactcenterinsights.v1.ExportInsightsDataResponse
- name: google.cloud.contactcenterinsights.v1.ExportIssueModelMetadata
- name: google.cloud.contactcenterinsights.v1.ExportIssueModelResponse
- name: google.cloud.contactcenterinsights.v1.ImportIssueModelMetadata
- name: google.cloud.contactcenterinsights.v1.ImportIssueModelResponse
- name: google.cloud.contactcenterinsights.v1.IngestConversationsMetadata
- name: google.cloud.contactcenterinsights.v1.IngestConversationsResponse
- name: google.cloud.contactcenterinsights.v1.InitializeEncryptionSpecMetadata
- name: google.cloud.contactcenterinsights.v1.InitializeEncryptionSpecResponse
- name: google.cloud.contactcenterinsights.v1.ListAllFeedbackLabelsResponse
- name: google.cloud.contactcenterinsights.v1.ListFeedbackLabelsResponse
- name: google.cloud.contactcenterinsights.v1.QueryMetricsMetadata
- name: google.cloud.contactcenterinsights.v1.QueryMetricsResponse
- name: google.cloud.contactcenterinsights.v1.UndeployIssueModelMetadata
- name: google.cloud.contactcenterinsights.v1.UndeployIssueModelResponse
- name: google.cloud.contactcenterinsights.v1.UploadConversationMetadata

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.contactcenterinsights.v1.ContactCenterInsights.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
