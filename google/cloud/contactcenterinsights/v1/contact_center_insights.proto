// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contactcenterinsights.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/contactcenterinsights/v1/resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/interval.proto";

option csharp_namespace = "Google.Cloud.ContactCenterInsights.V1";
option go_package = "cloud.google.com/go/contactcenterinsights/apiv1/contactcenterinsightspb;contactcenterinsightspb";
option java_multiple_files = true;
option java_outer_classname = "ContactCenterInsightsProto";
option java_package = "com.google.cloud.contactcenterinsights.v1";
option php_namespace = "Google\\Cloud\\ContactCenterInsights\\V1";
option ruby_package = "Google::Cloud::ContactCenterInsights::V1";

// An API that lets users analyze and explore their business conversation data.
service ContactCenterInsights {
  option (google.api.default_host) = "contactcenterinsights.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a conversation.
  // Note that this method does not support audio transcription or redaction.
  // Use `conversations.upload` instead.
  rpc CreateConversation(CreateConversationRequest) returns (Conversation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/conversations"
      body: "conversation"
    };
    option (google.api.method_signature) =
        "parent,conversation,conversation_id";
  }

  // Create a long-running conversation upload operation. This method differs
  // from `CreateConversation` by allowing audio transcription and optional DLP
  // redaction.
  rpc UploadConversation(UploadConversationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/conversations:upload"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Conversation"
      metadata_type: "UploadConversationMetadata"
    };
  }

  // Updates a conversation.
  rpc UpdateConversation(UpdateConversationRequest) returns (Conversation) {
    option (google.api.http) = {
      patch: "/v1/{conversation.name=projects/*/locations/*/conversations/*}"
      body: "conversation"
    };
    option (google.api.method_signature) = "conversation,update_mask";
  }

  // Gets a conversation.
  rpc GetConversation(GetConversationRequest) returns (Conversation) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/conversations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists conversations.
  rpc ListConversations(ListConversationsRequest)
      returns (ListConversationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/conversations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a conversation.
  rpc DeleteConversation(DeleteConversationRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/conversations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an analysis. The long running operation is done when the analysis
  // has completed.
  rpc CreateAnalysis(CreateAnalysisRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/conversations/*}/analyses"
      body: "analysis"
    };
    option (google.api.method_signature) = "parent,analysis";
    option (google.longrunning.operation_info) = {
      response_type: "Analysis"
      metadata_type: "CreateAnalysisOperationMetadata"
    };
  }

  // Gets an analysis.
  rpc GetAnalysis(GetAnalysisRequest) returns (Analysis) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/conversations/*/analyses/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists analyses.
  rpc ListAnalyses(ListAnalysesRequest) returns (ListAnalysesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/conversations/*}/analyses"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes an analysis.
  rpc DeleteAnalysis(DeleteAnalysisRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/conversations/*/analyses/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Analyzes multiple conversations in a single request.
  rpc BulkAnalyzeConversations(BulkAnalyzeConversationsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/conversations:bulkAnalyze"
      body: "*"
    };
    option (google.api.method_signature) = "parent,filter,analysis_percentage";
    option (google.longrunning.operation_info) = {
      response_type: "BulkAnalyzeConversationsResponse"
      metadata_type: "BulkAnalyzeConversationsMetadata"
    };
  }

  // Deletes multiple conversations in a single request.
  rpc BulkDeleteConversations(BulkDeleteConversationsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/conversations:bulkDelete"
      body: "*"
    };
    option (google.api.method_signature) = "parent,filter";
    option (google.longrunning.operation_info) = {
      response_type: "BulkDeleteConversationsResponse"
      metadata_type: "BulkDeleteConversationsMetadata"
    };
  }

  // Imports conversations and processes them according to the user's
  // configuration.
  rpc IngestConversations(IngestConversationsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/conversations:ingest"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "IngestConversationsResponse"
      metadata_type: "IngestConversationsMetadata"
    };
  }

  // Export insights data to a destination defined in the request body.
  rpc ExportInsightsData(ExportInsightsDataRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/insightsdata:export"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "ExportInsightsDataResponse"
      metadata_type: "ExportInsightsDataMetadata"
    };
  }

  // Creates an issue model.
  rpc CreateIssueModel(CreateIssueModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/issueModels"
      body: "issue_model"
    };
    option (google.api.method_signature) = "parent,issue_model";
    option (google.longrunning.operation_info) = {
      response_type: "IssueModel"
      metadata_type: "CreateIssueModelMetadata"
    };
  }

  // Updates an issue model.
  rpc UpdateIssueModel(UpdateIssueModelRequest) returns (IssueModel) {
    option (google.api.http) = {
      patch: "/v1/{issue_model.name=projects/*/locations/*/issueModels/*}"
      body: "issue_model"
    };
    option (google.api.method_signature) = "issue_model,update_mask";
  }

  // Gets an issue model.
  rpc GetIssueModel(GetIssueModelRequest) returns (IssueModel) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/issueModels/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists issue models.
  rpc ListIssueModels(ListIssueModelsRequest)
      returns (ListIssueModelsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/issueModels"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes an issue model.
  rpc DeleteIssueModel(DeleteIssueModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/issueModels/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteIssueModelMetadata"
    };
  }

  // Deploys an issue model. Returns an error if a model is already deployed.
  // An issue model can only be used in analysis after it has been deployed.
  rpc DeployIssueModel(DeployIssueModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/issueModels/*}:deploy"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "DeployIssueModelResponse"
      metadata_type: "DeployIssueModelMetadata"
    };
  }

  // Undeploys an issue model.
  // An issue model can not be used in analysis after it has been undeployed.
  rpc UndeployIssueModel(UndeployIssueModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/issueModels/*}:undeploy"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "UndeployIssueModelResponse"
      metadata_type: "UndeployIssueModelMetadata"
    };
  }

  // Exports an issue model to the provided destination.
  rpc ExportIssueModel(ExportIssueModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/issueModels/*}:export"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "ExportIssueModelResponse"
      metadata_type: "ExportIssueModelMetadata"
    };
  }

  // Imports an issue model from a Cloud Storage bucket.
  rpc ImportIssueModel(ImportIssueModelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/issueModels:import"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "ImportIssueModelResponse"
      metadata_type: "ImportIssueModelMetadata"
    };
  }

  // Gets an issue.
  rpc GetIssue(GetIssueRequest) returns (Issue) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/issueModels/*/issues/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists issues.
  rpc ListIssues(ListIssuesRequest) returns (ListIssuesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/issueModels/*}/issues"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates an issue.
  rpc UpdateIssue(UpdateIssueRequest) returns (Issue) {
    option (google.api.http) = {
      patch: "/v1/{issue.name=projects/*/locations/*/issueModels/*/issues/*}"
      body: "issue"
    };
    option (google.api.method_signature) = "issue,update_mask";
  }

  // Deletes an issue.
  rpc DeleteIssue(DeleteIssueRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/issueModels/*/issues/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets an issue model's statistics.
  rpc CalculateIssueModelStats(CalculateIssueModelStatsRequest)
      returns (CalculateIssueModelStatsResponse) {
    option (google.api.http) = {
      get: "/v1/{issue_model=projects/*/locations/*/issueModels/*}:calculateIssueModelStats"
    };
    option (google.api.method_signature) = "issue_model";
  }

  // Creates a phrase matcher.
  rpc CreatePhraseMatcher(CreatePhraseMatcherRequest) returns (PhraseMatcher) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/phraseMatchers"
      body: "phrase_matcher"
    };
    option (google.api.method_signature) = "parent,phrase_matcher";
  }

  // Gets a phrase matcher.
  rpc GetPhraseMatcher(GetPhraseMatcherRequest) returns (PhraseMatcher) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/phraseMatchers/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists phrase matchers.
  rpc ListPhraseMatchers(ListPhraseMatchersRequest)
      returns (ListPhraseMatchersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/phraseMatchers"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a phrase matcher.
  rpc DeletePhraseMatcher(DeletePhraseMatcherRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/phraseMatchers/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a phrase matcher.
  rpc UpdatePhraseMatcher(UpdatePhraseMatcherRequest) returns (PhraseMatcher) {
    option (google.api.http) = {
      patch: "/v1/{phrase_matcher.name=projects/*/locations/*/phraseMatchers/*}"
      body: "phrase_matcher"
    };
    option (google.api.method_signature) = "phrase_matcher,update_mask";
  }

  // Gets conversation statistics.
  rpc CalculateStats(CalculateStatsRequest) returns (CalculateStatsResponse) {
    option (google.api.http) = {
      get: "/v1/{location=projects/*/locations/*}/conversations:calculateStats"
      additional_bindings {
        get: "/v1/{location=projects/*/locations/*/authorizedViewSet/*/authorizedView/*}:calculateStats"
      }
    };
    option (google.api.method_signature) = "location";
  }

  // Gets project-level settings.
  rpc GetSettings(GetSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/settings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates project-level settings.
  rpc UpdateSettings(UpdateSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      patch: "/v1/{settings.name=projects/*/locations/*/settings}"
      body: "settings"
    };
    option (google.api.method_signature) = "settings,update_mask";
  }

  // Creates a analysis rule.
  rpc CreateAnalysisRule(CreateAnalysisRuleRequest) returns (AnalysisRule) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/analysisRules"
      body: "analysis_rule"
    };
    option (google.api.method_signature) = "parent,analysis_rule";
  }

  // Get a analysis rule.
  rpc GetAnalysisRule(GetAnalysisRuleRequest) returns (AnalysisRule) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/analysisRules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists analysis rules.
  rpc ListAnalysisRules(ListAnalysisRulesRequest)
      returns (ListAnalysisRulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/analysisRules"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a analysis rule.
  rpc UpdateAnalysisRule(UpdateAnalysisRuleRequest) returns (AnalysisRule) {
    option (google.api.http) = {
      patch: "/v1/{analysis_rule.name=projects/*/locations/*/analysisRules/*}"
      body: "analysis_rule"
    };
    option (google.api.method_signature) = "analysis_rule,update_mask";
  }

  // Deletes a analysis rule.
  rpc DeleteAnalysisRule(DeleteAnalysisRuleRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/analysisRules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets location-level encryption key specification.
  rpc GetEncryptionSpec(GetEncryptionSpecRequest) returns (EncryptionSpec) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/encryptionSpec}"
    };
    option (google.api.method_signature) = "name";
  }

  // Initializes a location-level encryption key specification. An error will
  // result if the location has resources already created before the
  // initialization. After the encryption specification is initialized at a
  // location, it is immutable and all newly created resources under the
  // location will be encrypted with the existing specification.
  rpc InitializeEncryptionSpec(InitializeEncryptionSpecRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{encryption_spec.name=projects/*/locations/*/encryptionSpec}:initialize"
      body: "*"
    };
    option (google.api.method_signature) = "encryption_spec";
    option (google.longrunning.operation_info) = {
      response_type: "InitializeEncryptionSpecResponse"
      metadata_type: "InitializeEncryptionSpecMetadata"
    };
  }

  // Creates a view.
  rpc CreateView(CreateViewRequest) returns (View) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/views"
      body: "view"
    };
    option (google.api.method_signature) = "parent,view";
  }

  // Gets a view.
  rpc GetView(GetViewRequest) returns (View) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/views/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists views.
  rpc ListViews(ListViewsRequest) returns (ListViewsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/views"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a view.
  rpc UpdateView(UpdateViewRequest) returns (View) {
    option (google.api.http) = {
      patch: "/v1/{view.name=projects/*/locations/*/views/*}"
      body: "view"
    };
    option (google.api.method_signature) = "view,update_mask";
  }

  // Deletes a view.
  rpc DeleteView(DeleteViewRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/views/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Query metrics.
  rpc QueryMetrics(QueryMetricsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{location=projects/*/locations/*}:queryMetrics"
      body: "*"
      additional_bindings {
        post: "/v1/{location=projects/*/locations/*/authorizedViewSet/*/authorizedView/*}:queryMetrics"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "QueryMetricsResponse"
      metadata_type: "QueryMetricsMetadata"
    };
  }

  // Create a QaQuestion.
  rpc CreateQaQuestion(CreateQaQuestionRequest) returns (QaQuestion) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/qaScorecards/*/revisions/*}/qaQuestions"
      body: "qa_question"
    };
    option (google.api.method_signature) = "parent,qa_question, qa_question_id";
  }

  // Gets a QaQuestion.
  rpc GetQaQuestion(GetQaQuestionRequest) returns (QaQuestion) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/qaScorecards/*/revisions/*/qaQuestions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a QaQuestion.
  rpc UpdateQaQuestion(UpdateQaQuestionRequest) returns (QaQuestion) {
    option (google.api.http) = {
      patch: "/v1/{qa_question.name=projects/*/locations/*/qaScorecards/*/revisions/*/qaQuestions/*}"
      body: "qa_question"
    };
    option (google.api.method_signature) = "qa_question,update_mask";
  }

  // Deletes a QaQuestion.
  rpc DeleteQaQuestion(DeleteQaQuestionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/qaScorecards/*/revisions/*/qaQuestions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists QaQuestions.
  rpc ListQaQuestions(ListQaQuestionsRequest)
      returns (ListQaQuestionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/qaScorecards/*/revisions/*}/qaQuestions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create a QaScorecard.
  rpc CreateQaScorecard(CreateQaScorecardRequest) returns (QaScorecard) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/qaScorecards"
      body: "qa_scorecard"
    };
    option (google.api.method_signature) =
        "parent,qa_scorecard,qa_scorecard_id";
  }

  // Gets a QaScorecard.
  rpc GetQaScorecard(GetQaScorecardRequest) returns (QaScorecard) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/qaScorecards/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a QaScorecard.
  rpc UpdateQaScorecard(UpdateQaScorecardRequest) returns (QaScorecard) {
    option (google.api.http) = {
      patch: "/v1/{qa_scorecard.name=projects/*/locations/*/qaScorecards/*}"
      body: "qa_scorecard"
    };
    option (google.api.method_signature) = "qa_scorecard,update_mask";
  }

  // Deletes a QaScorecard.
  rpc DeleteQaScorecard(DeleteQaScorecardRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/qaScorecards/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists QaScorecards.
  rpc ListQaScorecards(ListQaScorecardsRequest)
      returns (ListQaScorecardsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/qaScorecards"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a QaScorecardRevision.
  rpc CreateQaScorecardRevision(CreateQaScorecardRevisionRequest)
      returns (QaScorecardRevision) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/qaScorecards/*}/revisions"
      body: "qa_scorecard_revision"
    };
    option (google.api.method_signature) =
        "parent,qa_scorecard_revision,qa_scorecard_revision_id";
  }

  // Gets a QaScorecardRevision.
  rpc GetQaScorecardRevision(GetQaScorecardRevisionRequest)
      returns (QaScorecardRevision) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/qaScorecards/*/revisions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Fine tune one or more QaModels.
  rpc TuneQaScorecardRevision(TuneQaScorecardRevisionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/qaScorecards/*/revisions/*}:tuneQaScorecardRevision"
      body: "*"
    };
    option (google.api.method_signature) = "parent,filter,validate_only";
    option (google.longrunning.operation_info) = {
      response_type: "TuneQaScorecardRevisionResponse"
      metadata_type: "TuneQaScorecardRevisionMetadata"
    };
  }

  // Deploy a QaScorecardRevision.
  rpc DeployQaScorecardRevision(DeployQaScorecardRevisionRequest)
      returns (QaScorecardRevision) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/qaScorecards/*/revisions/*}:deploy"
      body: "*"
    };
  }

  // Undeploy a QaScorecardRevision.
  rpc UndeployQaScorecardRevision(UndeployQaScorecardRevisionRequest)
      returns (QaScorecardRevision) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/qaScorecards/*/revisions/*}:undeploy"
      body: "*"
    };
  }

  // Deletes a QaScorecardRevision.
  rpc DeleteQaScorecardRevision(DeleteQaScorecardRevisionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/qaScorecards/*/revisions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all revisions under the parent QaScorecard.
  rpc ListQaScorecardRevisions(ListQaScorecardRevisionsRequest)
      returns (ListQaScorecardRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/qaScorecards/*}/revisions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create feedback label.
  rpc CreateFeedbackLabel(CreateFeedbackLabelRequest) returns (FeedbackLabel) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/conversations/*}/feedbackLabels"
      body: "feedback_label"
    };
    option (google.api.method_signature) =
        "parent,feedback_label,feedback_label_id";
  }

  // List feedback labels.
  rpc ListFeedbackLabels(ListFeedbackLabelsRequest)
      returns (ListFeedbackLabelsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/conversations/*}/feedbackLabels"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get feedback label.
  rpc GetFeedbackLabel(GetFeedbackLabelRequest) returns (FeedbackLabel) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/conversations/*/feedbackLabels/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update feedback label.
  rpc UpdateFeedbackLabel(UpdateFeedbackLabelRequest) returns (FeedbackLabel) {
    option (google.api.http) = {
      patch: "/v1/{feedback_label.name=projects/*/locations/*/conversations/*/feedbackLabels/*}"
      body: "feedback_label"
    };
    option (google.api.method_signature) = "feedback_label,update_mask";
  }

  // Delete feedback label.
  rpc DeleteFeedbackLabel(DeleteFeedbackLabelRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/conversations/*/feedbackLabels/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List all feedback labels by project number.
  rpc ListAllFeedbackLabels(ListAllFeedbackLabelsRequest)
      returns (ListAllFeedbackLabelsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}:listAllFeedbackLabels"
    };
    option (google.api.method_signature) = "parent";
  }

  // Upload feedback labels in bulk.
  rpc BulkUploadFeedbackLabels(BulkUploadFeedbackLabelsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}:bulkUploadFeedbackLabels"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "BulkUploadFeedbackLabelsResponse"
      metadata_type: "BulkUploadFeedbackLabelsMetadata"
    };
  }

  // Download feedback labels in bulk.
  rpc BulkDownloadFeedbackLabels(BulkDownloadFeedbackLabelsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}:bulkDownloadFeedbackLabels"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "BulkDownloadFeedbackLabelsResponse"
      metadata_type: "BulkDownloadFeedbackLabelsMetadata"
    };
  }
}

// Represents the options for viewing a conversation.
enum ConversationView {
  // The conversation view is not specified.
  //
  // * Defaults to `FULL` in `GetConversationRequest`.
  // * Defaults to `BASIC` in `ListConversationsRequest`.
  CONVERSATION_VIEW_UNSPECIFIED = 0;

  // Populates all fields in the conversation.
  FULL = 2;

  // Populates all fields in the conversation except the transcript.
  BASIC = 1;
}

// The request for calculating conversation statistics.
message CalculateStatsRequest {
  // Required. The location of the conversations.
  string location = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // A filter to reduce results to a specific subset. This field is useful for
  // getting statistics about conversations with specific properties.
  string filter = 2;
}

// The response for calculating conversation statistics.
message CalculateStatsResponse {
  // A time series representing conversations over time.
  message TimeSeries {
    // A single interval in a time series.
    message Interval {
      // The start time of this interval.
      google.protobuf.Timestamp start_time = 1;

      // The number of conversations created in this interval.
      int32 conversation_count = 2;
    }

    // The duration of each interval.
    google.protobuf.Duration interval_duration = 1;

    // An ordered list of intervals from earliest to latest, where each interval
    // represents the number of conversations that transpired during the time
    // window.
    repeated Interval points = 2;
  }

  // The average duration of all conversations. The average is calculated using
  // only conversations that have a time duration.
  google.protobuf.Duration average_duration = 1;

  // The average number of turns per conversation.
  int32 average_turn_count = 2;

  // The total number of conversations.
  int32 conversation_count = 3;

  // A map associating each smart highlighter display name with its respective
  // number of matches in the set of conversations.
  map<string, int32> smart_highlighter_matches = 4;

  // A map associating each custom highlighter resource name with its respective
  // number of matches in the set of conversations.
  map<string, int32> custom_highlighter_matches = 5;

  // A map associating each issue resource name with its respective number of
  // matches in the set of conversations. Key has the format:
  // `projects/<Project-ID>/locations/<Location-ID>/issueModels/<Issue-Model-ID>/issues/<Issue-ID>`
  // Deprecated, use `issue_matches_stats` field instead.
  map<string, int32> issue_matches = 6 [deprecated = true];

  // A map associating each issue resource name with its respective number of
  // matches in the set of conversations. Key has the format:
  // `projects/<Project-ID>/locations/<Location-ID>/issueModels/<Issue-Model-ID>/issues/<Issue-ID>`
  map<string, IssueModelLabelStats.IssueStats> issue_matches_stats = 8;

  // A time series representing the count of conversations created over time
  // that match that requested filter criteria.
  TimeSeries conversation_count_time_series = 7;
}

// Metadata for a create analysis operation.
message CreateAnalysisOperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Conversation that this Analysis Operation belongs to.
  string conversation = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Conversation"
    }
  ];

  // Output only. The annotator selector used for the analysis (if any).
  AnnotatorSelector annotator_selector = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request to create a conversation.
message CreateConversationRequest {
  // Required. The parent resource of the conversation.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The conversation resource to create.
  Conversation conversation = 2 [(google.api.field_behavior) = REQUIRED];

  // A unique ID for the new conversation. This ID will become the final
  // component of the conversation's resource name. If no ID is specified, a
  // server-generated ID will be used.
  //
  // This value should be 4-64 characters and must match the regular
  // expression `^[a-z0-9-]{4,64}$`. Valid characters are `[a-z][0-9]-`
  string conversation_id = 3;
}

// Request to upload a conversation.
message UploadConversationRequest {
  // Required. The parent resource of the conversation.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The conversation resource to create.
  Conversation conversation = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A unique ID for the new conversation. This ID will become the
  // final component of the conversation's resource name. If no ID is specified,
  // a server-generated ID will be used.
  //
  // This value should be 4-64 characters and must match the regular
  // expression `^[a-z0-9-]{4,64}$`. Valid characters are `[a-z][0-9]-`
  string conversation_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. DLP settings for transcript redaction. Will default to the config
  // specified in Settings.
  RedactionConfig redaction_config = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Speech-to-Text configuration. Will default to the config
  // specified in Settings.
  SpeechConfig speech_config = 11 [(google.api.field_behavior) = OPTIONAL];
}

// The metadata for an `UploadConversation` operation.
message UploadConversationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The original request.
  UploadConversationRequest request = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The operation name for a successfully created analysis
  // operation, if any.
  string analysis_operation = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The redaction config applied to the uploaded conversation.
  RedactionConfig applied_redaction_config = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request to list conversations.
message ListConversationsRequest {
  // Required. The parent resource of the conversation.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of conversations to return in the response. A valid page
  // size ranges from 0 to 100,000 inclusive. If the page size is zero or
  // unspecified, a default page size of 100 will be chosen. Note that a call
  // might return fewer results than the requested page size.
  int32 page_size = 2;

  // The value returned by the last `ListConversationsResponse`. This value
  // indicates that this is a continuation of a prior `ListConversations` call
  // and that the system should return the next page of data.
  string page_token = 3;

  // A filter to reduce results to a specific subset. Useful for querying
  // conversations with specific properties.
  string filter = 4;

  // Optional. The attribute by which to order conversations in the response.
  // If empty, conversations will be ordered by descending creation time.
  // Supported values are one of the following:
  //
  // * create_time
  // * customer_satisfaction_rating
  // * duration
  // * latest_analysis
  // * start_time
  // * turn_count
  //
  // The default sort order is ascending. To specify order, append `asc` or
  // `desc` (`create_time desc`).
  // For more details, see [Google AIPs
  // Ordering](https://google.aip.dev/132#ordering).
  string order_by = 7 [(google.api.field_behavior) = OPTIONAL];

  // The level of details of the conversation. Default is `BASIC`.
  ConversationView view = 5;
}

// The response of listing conversations.
message ListConversationsResponse {
  // The conversations that match the request.
  repeated Conversation conversations = 1;

  // A token which can be sent as `page_token` to retrieve the next page. If
  // this field is set, it means there is another page available. If it is not
  // set, it means no other pages are available.
  string next_page_token = 2;
}

// The request to get a conversation.
message GetConversationRequest {
  // Required. The name of the conversation to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Conversation"
    }
  ];

  // The level of details of the conversation. Default is `FULL`.
  ConversationView view = 2;
}

// The request to update a conversation.
message UpdateConversationRequest {
  // Required. The new values for the conversation.
  Conversation conversation = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated. All possible fields can be updated by
  // passing `*`, or a subset of the following updateable fields can be
  // provided:
  //
  // * `agent_id`
  // * `language_code`
  // * `labels`
  // * `metadata`
  // * `quality_metadata`
  // * `call_metadata`
  // * `start_time`
  // * `expire_time` or `ttl`
  // * `data_source.gcs_source.audio_uri` or
  // `data_source.dialogflow_source.audio_uri`
  google.protobuf.FieldMask update_mask = 2;
}

// The request to delete a conversation.
message DeleteConversationRequest {
  // Required. The name of the conversation to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Conversation"
    }
  ];

  // If set to true, all of this conversation's analyses will also be deleted.
  // Otherwise, the request will only succeed if the conversation has no
  // analyses.
  bool force = 2;
}

// The request to ingest conversations.
message IngestConversationsRequest {
  // Configuration for Cloud Storage bucket sources.
  message GcsSource {
    enum BucketObjectType {
      // The object type is unspecified and will default to `TRANSCRIPT`.
      BUCKET_OBJECT_TYPE_UNSPECIFIED = 0;

      // The object is a transcript.
      TRANSCRIPT = 1;

      // The object is an audio file.
      AUDIO = 2;
    }

    // Required. The Cloud Storage bucket containing source objects.
    string bucket_uri = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. Specifies the type of the objects in `bucket_uri`.
    BucketObjectType bucket_object_type = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The Cloud Storage path to the conversation metadata. Note that:
    // [1] Metadata files are expected to be in JSON format.
    // [2] Metadata and source files (transcripts or audio) must be in
    //     separate buckets.
    // [3] A source file and its corresponding metadata file must share the same
    // name to
    //     be properly ingested, E.g. `gs://bucket/audio/conversation1.mp3` and
    //     `gs://bucket/metadata/conversation1.json`.
    optional string metadata_bucket_uri = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Custom keys to extract as conversation labels from metadata
    // files in `metadata_bucket_uri`. Keys not included in this field will be
    // ignored. Note that there is a limit of 100 labels per conversation.
    repeated string custom_metadata_keys = 12
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Configuration for processing transcript objects.
  message TranscriptObjectConfig {
    // Required. The medium transcript objects represent.
    Conversation.Medium medium = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Configuration that applies to all conversations.
  message ConversationConfig {
    // Optional. An opaque, user-specified string representing a human agent who
    // handled all conversations in the import. Note that this will be
    // overridden if per-conversation metadata is provided through the
    // `metadata_bucket_uri`.
    string agent_id = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Indicates which of the channels, 1 or 2, contains the agent.
    // Note that this must be set for conversations to be properly displayed and
    // analyzed.
    int32 agent_channel = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Indicates which of the channels, 1 or 2, contains the agent.
    // Note that this must be set for conversations to be properly displayed and
    // analyzed.
    int32 customer_channel = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Configuration for an external data store containing objects that will
  // be converted to conversations.
  oneof source {
    // A cloud storage bucket source. Note that any previously ingested objects
    // from the source will be skipped to avoid duplication.
    GcsSource gcs_source = 2;
  }

  // Configuration for converting individual `source` objects to conversations.
  oneof object_config {
    // Configuration for when `source` contains conversation transcripts.
    TranscriptObjectConfig transcript_object_config = 3;
  }

  // Required. The parent resource for new conversations.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Configuration that applies to all conversations.
  ConversationConfig conversation_config = 4;

  // Optional. DLP settings for transcript redaction. Optional, will default to
  // the config specified in Settings.
  RedactionConfig redaction_config = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Default Speech-to-Text configuration. Optional, will default to
  // the config specified in Settings.
  SpeechConfig speech_config = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, this fields indicates the number of objects to ingest
  // from the Cloud Storage bucket. If empty, the entire bucket will be
  // ingested. Unless they are first deleted, conversations produced through
  // sampling won't be ingested by subsequent ingest requests.
  optional int32 sample_size = 7 [(google.api.field_behavior) = OPTIONAL];
}

// The metadata for an IngestConversations operation.
message IngestConversationsMetadata {
  // Statistics for IngestConversations operation.
  message IngestConversationsStats {
    // Output only. The number of objects processed during the ingest operation.
    int32 processed_object_count = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The number of objects skipped because another conversation
    // with the same transcript uri had already been ingested.
    int32 duplicates_skipped_count = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The number of new conversations added during this ingest
    // operation.
    int32 successful_ingest_count = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The number of objects which were unable to be ingested due
    // to errors. The errors are populated in the partial_errors field.
    int32 failed_ingest_count = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The original request for ingest.
  IngestConversationsRequest request = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Partial errors during ingest operation that might cause the
  // operation output to be incomplete.
  repeated google.rpc.Status partial_errors = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Statistics for IngestConversations operation.
  IngestConversationsStats ingest_conversations_stats = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The response to an IngestConversations operation.
message IngestConversationsResponse {}

// The request to create an analysis.
message CreateAnalysisRequest {
  // Required. The parent resource of the analysis.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Conversation"
    }
  ];

  // Required. The analysis to create.
  Analysis analysis = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request to list analyses.
message ListAnalysesRequest {
  // Required. The parent resource of the analyses.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Conversation"
    }
  ];

  // The maximum number of analyses to return in the response. If this
  // value is zero, the service will select a default size. A call might return
  // fewer objects than requested. A non-empty `next_page_token` in the response
  // indicates that more data is available.
  int32 page_size = 2;

  // The value returned by the last `ListAnalysesResponse`; indicates
  // that this is a continuation of a prior `ListAnalyses` call and
  // the system should return the next page of data.
  string page_token = 3;

  // A filter to reduce results to a specific subset. Useful for querying
  // conversations with specific properties.
  string filter = 4;
}

// The response to list analyses.
message ListAnalysesResponse {
  // The analyses that match the request.
  repeated Analysis analyses = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request to get an analysis.
message GetAnalysisRequest {
  // Required. The name of the analysis to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Analysis"
    }
  ];
}

// The request to delete an analysis.
message DeleteAnalysisRequest {
  // Required. The name of the analysis to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Analysis"
    }
  ];
}

// The request to analyze conversations in bulk.
message BulkAnalyzeConversationsRequest {
  // Required. The parent resource to create analyses in.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Filter used to select the subset of conversations to analyze.
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Percentage of selected conversation to analyze, between
  // [0, 100].
  float analysis_percentage = 3 [(google.api.field_behavior) = REQUIRED];

  // To select the annotators to run and the phrase matchers to use
  // (if any). If not specified, all annotators will be run.
  AnnotatorSelector annotator_selector = 8;
}

// The metadata for a bulk analyze conversations operation.
message BulkAnalyzeConversationsMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // The original request for bulk analyze.
  BulkAnalyzeConversationsRequest request = 3;

  // The number of requested analyses that have completed successfully so far.
  int32 completed_analyses_count = 4;

  // The number of requested analyses that have failed so far.
  int32 failed_analyses_count = 5;

  // Total number of analyses requested. Computed by the number of conversations
  // returned by `filter` multiplied by `analysis_percentage` in the request.
  int32 total_requested_analyses_count = 6;

  // Output only. Partial errors during bulk analyze operation that might cause
  // the operation output to be incomplete.
  repeated google.rpc.Status partial_errors = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The response for a bulk analyze conversations operation.
message BulkAnalyzeConversationsResponse {
  // Count of successful analyses.
  int32 successful_analysis_count = 1;

  // Count of failed analyses.
  int32 failed_analysis_count = 2;
}

// The request to delete conversations in bulk.
message BulkDeleteConversationsRequest {
  // Required. The parent resource to delete conversations from.
  // Format:
  // projects/{project}/locations/{location}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Filter used to select the subset of conversations to delete.
  string filter = 2;

  // Maximum number of conversations to delete.
  int32 max_delete_count = 3;

  // If set to true, all of this conversation's analyses will also be deleted.
  // Otherwise, the request will only succeed if the conversation has no
  // analyses.
  bool force = 4;
}

// The metadata for a bulk delete conversations operation.
message BulkDeleteConversationsMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // The original request for bulk delete.
  BulkDeleteConversationsRequest request = 3;

  // Partial errors during bulk delete conversations operation that might cause
  // the operation output to be incomplete.
  repeated google.rpc.Status partial_errors = 4;
}

// The response for a bulk delete conversations operation.
message BulkDeleteConversationsResponse {}

// The request to export insights.
message ExportInsightsDataRequest {
  // A BigQuery Table Reference.
  message BigQueryDestination {
    // A project ID or number. If specified, then export will attempt to
    // write data to this project instead of the resource project. Otherwise,
    // the resource project will be used.
    string project_id = 3;

    // Required. The name of the BigQuery dataset that the snapshot result
    // should be exported to. If this dataset does not exist, the export call
    // returns an INVALID_ARGUMENT error.
    string dataset = 1 [(google.api.field_behavior) = REQUIRED];

    // The BigQuery table name to which the insights data should be written.
    // If this table does not exist, the export call returns an INVALID_ARGUMENT
    // error.
    string table = 2;
  }

  // Specifies the action that occurs if the destination table already exists.
  enum WriteDisposition {
    // Write disposition is not specified. Defaults to WRITE_TRUNCATE.
    WRITE_DISPOSITION_UNSPECIFIED = 0;

    // If the table already exists, BigQuery will overwrite the table data and
    // use the schema from the load.
    WRITE_TRUNCATE = 1;

    // If the table already exists, BigQuery will append data to the table.
    WRITE_APPEND = 2;
  }

  // Exporter destination.
  oneof destination {
    // Specified if sink is a BigQuery table.
    BigQueryDestination big_query_destination = 2;
  }

  // Required. The parent resource to export data from.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // A filter to reduce results to a specific subset. Useful for exporting
  // conversations with specific properties.
  string filter = 3;

  // A fully qualified KMS key name for BigQuery tables protected by CMEK.
  // Format:
  // projects/{project}/locations/{location}/keyRings/{keyring}/cryptoKeys/{key}/cryptoKeyVersions/{version}
  string kms_key = 4;

  // Options for what to do if the destination table already exists.
  WriteDisposition write_disposition = 5;
}

// Metadata for an export insights operation.
message ExportInsightsDataMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The original request for export.
  ExportInsightsDataRequest request = 3;

  // Partial errors during export operation that might cause the operation
  // output to be incomplete.
  repeated google.rpc.Status partial_errors = 4;
}

// Response for an export insights operation.
message ExportInsightsDataResponse {}

// The request to create an issue model.
message CreateIssueModelRequest {
  // Required. The parent resource of the issue model.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The issue model to create.
  IssueModel issue_model = 2 [(google.api.field_behavior) = REQUIRED];
}

// Metadata for creating an issue model.
message CreateIssueModelMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The original request for creation.
  CreateIssueModelRequest request = 3;
}

// The request to update an issue model.
message UpdateIssueModelRequest {
  // Required. The new values for the issue model.
  IssueModel issue_model = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request to list issue models.
message ListIssueModelsRequest {
  // Required. The parent resource of the issue model.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// The response of listing issue models.
message ListIssueModelsResponse {
  // The issue models that match the request.
  repeated IssueModel issue_models = 1;
}

// The request to get an issue model.
message GetIssueModelRequest {
  // Required. The name of the issue model to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/IssueModel"
    }
  ];
}

// The request to delete an issue model.
message DeleteIssueModelRequest {
  // Required. The name of the issue model to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/IssueModel"
    }
  ];
}

// Metadata for deleting an issue model.
message DeleteIssueModelMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The original request for deletion.
  DeleteIssueModelRequest request = 3;
}

// The request to deploy an issue model.
message DeployIssueModelRequest {
  // Required. The issue model to deploy.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/IssueModel"
    }
  ];
}

// The response to deploy an issue model.
message DeployIssueModelResponse {}

// Metadata for deploying an issue model.
message DeployIssueModelMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The original request for deployment.
  DeployIssueModelRequest request = 3;
}

// The request to undeploy an issue model.
message UndeployIssueModelRequest {
  // Required. The issue model to undeploy.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/IssueModel"
    }
  ];
}

// The response to undeploy an issue model.
message UndeployIssueModelResponse {}

// Metadata for undeploying an issue model.
message UndeployIssueModelMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The original request for undeployment.
  UndeployIssueModelRequest request = 3;
}

// Request to export an issue model.
message ExportIssueModelRequest {
  // Google Cloud Storage Object URI to save the issue model to.
  message GcsDestination {
    // Required. Format: `gs://<bucket-name>/<object-name>`
    string object_uri = 1 [(google.api.field_behavior) = REQUIRED];
  }

  oneof Destination {
    // Google Cloud Storage URI to export the issue model to.
    GcsDestination gcs_destination = 2;
  }

  // Required. The issue model to export.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/IssueModel"
    }
  ];
}

// Response from export issue model
message ExportIssueModelResponse {}

// Metadata used for export issue model.
message ExportIssueModelMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // The original export request.
  ExportIssueModelRequest request = 3;
}

// Request to import an issue model.
message ImportIssueModelRequest {
  // Google Cloud Storage Object URI to get the issue model file from.
  message GcsSource {
    // Required. Format: `gs://<bucket-name>/<object-name>`
    string object_uri = 1 [(google.api.field_behavior) = REQUIRED];
  }

  oneof Source {
    // Google Cloud Storage source message.
    GcsSource gcs_source = 2;
  }

  // Required. The parent resource of the issue model.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. If set to true, will create an issue model from the imported file
  // with randomly generated IDs for the issue model and corresponding issues.
  // Otherwise, replaces an existing model with the same ID as the file.
  bool create_new_model = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response from import issue model
message ImportIssueModelResponse {}

// Metadata used for import issue model.
message ImportIssueModelMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // The original import request.
  ImportIssueModelRequest request = 3;
}

// The request to get an issue.
message GetIssueRequest {
  // Required. The name of the issue to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Issue"
    }
  ];
}

// Request to list issues.
message ListIssuesRequest {
  // Required. The parent resource of the issue.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/IssueModel"
    }
  ];
}

// The response of listing issues.
message ListIssuesResponse {
  // The issues that match the request.
  repeated Issue issues = 1;
}

// The request to update an issue.
message UpdateIssueRequest {
  // Required. The new values for the issue.
  Issue issue = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// The request to delete an issue.
message DeleteIssueRequest {
  // Required. The name of the issue to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Issue"
    }
  ];
}

// Request to get statistics of an issue model.
message CalculateIssueModelStatsRequest {
  // Required. The resource name of the issue model to query against.
  string issue_model = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/IssueModel"
    }
  ];
}

// Response of querying an issue model's statistics.
message CalculateIssueModelStatsResponse {
  // The latest label statistics for the queried issue model. Includes results
  // on both training data and data labeled after deployment.
  IssueModelLabelStats current_stats = 4;
}

// Request to create a phrase matcher.
message CreatePhraseMatcherRequest {
  // Required. The parent resource of the phrase matcher. Required. The location
  // to create a phrase matcher for. Format: `projects/<Project
  // ID>/locations/<Location ID>` or `projects/<Project
  // Number>/locations/<Location ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The phrase matcher resource to create.
  PhraseMatcher phrase_matcher = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request to list phrase matchers.
message ListPhraseMatchersRequest {
  // Required. The parent resource of the phrase matcher.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of phrase matchers to return in the response. If this
  // value is zero, the service will select a default size. A call might return
  // fewer objects than requested. A non-empty `next_page_token` in the response
  // indicates that more data is available.
  int32 page_size = 2;

  // The value returned by the last `ListPhraseMatchersResponse`. This value
  // indicates that this is a continuation of a prior `ListPhraseMatchers` call
  // and that the system should return the next page of data.
  string page_token = 3;

  // A filter to reduce results to a specific subset. Useful for querying
  // phrase matchers with specific properties.
  string filter = 4;
}

// The response of listing phrase matchers.
message ListPhraseMatchersResponse {
  // The phrase matchers that match the request.
  repeated PhraseMatcher phrase_matchers = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request to get a a phrase matcher.
message GetPhraseMatcherRequest {
  // Required. The name of the phrase matcher to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/PhraseMatcher"
    }
  ];
}

// The request to delete a phrase matcher.
message DeletePhraseMatcherRequest {
  // Required. The name of the phrase matcher to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/PhraseMatcher"
    }
  ];
}

// The request to update a phrase matcher.
message UpdatePhraseMatcherRequest {
  // Required. The new values for the phrase matcher.
  PhraseMatcher phrase_matcher = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// The request to get project-level settings.
message GetSettingsRequest {
  // Required. The name of the settings resource to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Settings"
    }
  ];
}

// The request to update project-level settings.
message UpdateSettingsRequest {
  // Required. The new settings values.
  Settings settings = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request to create a analysis rule.
// analysis_rule_id will be generated by the server.
message CreateAnalysisRuleRequest {
  // Required. The parent resource of the analysis rule. Required. The location
  // to create a analysis rule for. Format: `projects/<Project
  // ID>/locations/<Location ID>` or `projects/<Project
  // Number>/locations/<Location ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The analysis rule resource to create.
  AnalysisRule analysis_rule = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request for getting a analysis rule.
message GetAnalysisRuleRequest {
  // Required. The name of the AnalysisRule to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/AnalysisRule"
    }
  ];
}

// The request to update a analysis rule.
message UpdateAnalysisRuleRequest {
  // Required. The new analysis rule.
  AnalysisRule analysis_rule = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The list of fields to be updated.
  // If the update_mask is not provided, the update will be applied to all
  // fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// The request to delete a analysis rule.
message DeleteAnalysisRuleRequest {
  // Required. The name of the analysis rule to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/AnalysisRule"
    }
  ];
}

// The request to list analysis rules.
message ListAnalysisRulesRequest {
  // Required. The parent resource of the analysis rules.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The maximum number of analysis rule to return in the response. If
  // this value is zero, the service will select a default size. A call may
  // return fewer objects than requested. A non-empty `next_page_token` in the
  // response indicates that more data is available.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value returned by the last `ListAnalysisRulesResponse`;
  // indicates that this is a continuation of a prior `ListAnalysisRules` call
  // and the system should return the next page of data.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response of listing views.
message ListAnalysisRulesResponse {
  // The analysis_rule that match the request.
  repeated AnalysisRule analysis_rules = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request to get location-level encryption specification.
message GetEncryptionSpecRequest {
  // Required. The name of the encryption spec resource to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/EncryptionSpec"
    }
  ];
}

// The request to initialize a location-level encryption specification.
message InitializeEncryptionSpecRequest {
  // Required. The encryption spec used for CMEK encryption. It is required that
  // the kms key is in the same region as the endpoint. The same key will be
  // used for all provisioned resources, if encryption is available. If the
  // `kms_key_name` field is left empty, no encryption will be enforced.
  EncryptionSpec encryption_spec = 1 [(google.api.field_behavior) = REQUIRED];
}

// The response to initialize a location-level encryption specification.
message InitializeEncryptionSpecResponse {}

// Metadata for initializing a location-level encryption specification.
message InitializeEncryptionSpecMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The original request for initialization.
  InitializeEncryptionSpecRequest request = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Partial errors during initializing operation that might cause the operation
  // output to be incomplete.
  repeated google.rpc.Status partial_errors = 4;
}

// The request to create a view.
message CreateViewRequest {
  // Required. The parent resource of the view. Required. The location to create
  // a view for.
  // Format: `projects/<Project ID>/locations/<Location ID>` or
  // `projects/<Project Number>/locations/<Location ID>`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The view resource to create.
  View view = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request to get a view.
message GetViewRequest {
  // Required. The name of the view to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/View"
    }
  ];
}

// The request to list views.
message ListViewsRequest {
  // Required. The parent resource of the views.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of views to return in the response. If this
  // value is zero, the service will select a default size. A call may return
  // fewer objects than requested. A non-empty `next_page_token` in the response
  // indicates that more data is available.
  int32 page_size = 2;

  // The value returned by the last `ListViewsResponse`; indicates
  // that this is a continuation of a prior `ListViews` call and
  // the system should return the next page of data.
  string page_token = 3;
}

// The response of listing views.
message ListViewsResponse {
  // The views that match the request.
  repeated View views = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request to update a view.
message UpdateViewRequest {
  // Required. The new view.
  View view = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// The request to delete a view.
message DeleteViewRequest {
  // Required. The name of the view to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/View"
    }
  ];
}

// A dimension determines the grouping key for the query. In SQL terms, these
// would be part of both the "SELECT" and "GROUP BY" clauses.
message Dimension {
  // Metadata about the issue dimension.
  message IssueDimensionMetadata {
    // The issue ID.
    string issue_id = 1 [(google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Issue"
    }];

    // The issue display name.
    string issue_display_name = 2;

    // The parent issue model ID.
    string issue_model_id = 3 [(google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/IssueModel"
    }];
  }

  // Metadata about the agent dimension.
  message AgentDimensionMetadata {
    // Optional. A user-specified string representing the agent.
    string agent_id = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The agent's name
    string agent_display_name = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. A user-specified string representing the agent's team.
    string agent_team = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Metadata about the QA question dimension.
  message QaQuestionDimensionMetadata {
    // Optional. The QA scorecard ID.
    string qa_scorecard_id = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The QA question ID.
    string qa_question_id = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The full body of the question.
    string question_body = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Metadata about the QA question-answer dimension.
  // This is useful for showing the answer distribution for questions for a
  // given scorecard.
  message QaQuestionAnswerDimensionMetadata {
    // Optional. The QA scorecard ID.
    string qa_scorecard_id = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The QA question ID.
    string qa_question_id = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The full body of the question.
    string question_body = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The full body of the question.
    string answer_value = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // The key of the dimension.
  enum DimensionKey {
    // The key of the dimension is unspecified.
    DIMENSION_KEY_UNSPECIFIED = 0;

    // The dimension is keyed by issues.
    ISSUE = 1;

    // The dimension is keyed by agents.
    AGENT = 2;

    // The dimension is keyed by agent teams.
    AGENT_TEAM = 3;

    // The dimension is keyed by QaQuestionIds.
    // Note that: We only group by the QuestionId and not the revision-id of the
    // scorecard this question is a part of. This allows for showing stats for
    // the same question across different scorecard revisions.
    QA_QUESTION_ID = 4;

    // The dimension is keyed by QaQuestionIds-Answer value pairs.
    // Note that: We only group by the QuestionId and not the revision-id of the
    // scorecard this question is a part of. This allows for showing
    // distribution of answers per question across different scorecard
    // revisions.
    QA_QUESTION_ANSWER_VALUE = 5;

    // The dimension is keyed by the conversation profile ID.
    CONVERSATION_PROFILE_ID = 6;
  }

  // Output-only metadata about the dimension.
  oneof dimension_metadata {
    // Output only. Metadata about the issue dimension.
    IssueDimensionMetadata issue_dimension_metadata = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Metadata about the agent dimension.
    AgentDimensionMetadata agent_dimension_metadata = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Metadata about the QA question dimension.
    QaQuestionDimensionMetadata qa_question_dimension_metadata = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Metadata about the QA question-answer dimension.
    QaQuestionAnswerDimensionMetadata qa_question_answer_dimension_metadata = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The key of the dimension.
  DimensionKey dimension_key = 1;
}

// The request for querying metrics.
message QueryMetricsRequest {
  // A time granularity divides the time line into discrete time periods.
  // This is useful for defining buckets over which filtering and aggregation
  // should be performed.
  enum TimeGranularity {
    // The time granularity is unspecified and will default to NONE.
    TIME_GRANULARITY_UNSPECIFIED = 0;

    // No time granularity. The response won't contain a time series.
    // This is the default value if no time granularity is specified.
    NONE = 1;

    // Data points in the time series will aggregate at a daily granularity.
    // 1 day means [midnight to midnight).
    DAILY = 2;

    // Data points in the time series will aggregate at a daily granularity.
    // 1 HOUR means [01:00 to 02:00).
    HOURLY = 3;

    // Data points in the time series will aggregate at a daily granularity.
    // PER_MINUTE means [01:00 to 01:01).
    PER_MINUTE = 4;

    // Data points in the time series will aggregate at a 1 minute  granularity.
    // PER_5_MINUTES means [01:00 to 01:05).
    PER_5_MINUTES = 5;

    // Data points in the time series will aggregate at a monthly granularity.
    // 1 MONTH means [01st of the month to 1st of the next month).
    MONTHLY = 6;
  }

  // Required. The location of the data.
  // "projects/{project}/locations/{location}"
  string location = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Filter to select a subset of conversations to compute the
  // metrics. Must specify a window of the conversation create time to compute
  // the metrics. The returned metrics will be from the range [DATE(starting
  // create time), DATE(ending create time)).
  //
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // The time granularity of each data point in the time series.
  // Defaults to NONE if this field is unspecified.
  TimeGranularity time_granularity = 3;

  // The dimensions that determine the grouping key for the query. Defaults to
  // no dimension if this field is unspecified. If a dimension is specified,
  // its key must also be specified. Each dimension's key must be unique.
  //
  // If a time granularity is also specified, metric values in the dimension
  // will be bucketed by this granularity.
  //
  // Up to one dimension is supported for now.
  repeated Dimension dimensions = 4;

  // Measures to return. Defaults to all measures if this field is unspecified.
  // A valid mask should traverse from the `measure` field from the response.
  // For example, a path from a measure mask to get the conversation count is
  // "conversation_measure.count".
  google.protobuf.FieldMask measure_mask = 5;
}

// The response for querying metrics.
message QueryMetricsResponse {
  // A slice contains a total and (if the request specified a time granularity)
  // a time series of metric values. Each slice contains a unique combination of
  // the cardinality of dimensions from the request.
  //
  // For example, if the request specifies a single ISSUE dimension and it has a
  // cardinality of 2 (i.e. the data used to compute the metrics has 2 issues in
  // total), the response will have 2 slices:
  //
  // * Slice 1 -> dimensions=[Issue 1]
  // * Slice 2 -> dimensions=[Issue 2]
  message Slice {
    // A data point contains the metric values mapped to an interval.
    message DataPoint {
      // The measure related to conversations.
      message ConversationMeasure {
        // Average QA normalized score for the tag.
        message QaTagScore {
          // Tag name.
          string tag = 1;

          // Average tag normalized score per tag.
          double average_tag_normalized_score = 2;
        }

        // The conversation count.
        optional int32 conversation_count = 1;

        // The average silence percentage.
        optional float average_silence_percentage = 2;

        // The average duration.
        optional google.protobuf.Duration average_duration = 3;

        // The average turn count.
        optional float average_turn_count = 4;

        // The average agent's sentiment score.
        optional float average_agent_sentiment_score = 5;

        // The average client's sentiment score.
        optional float average_client_sentiment_score = 6;

        // The average customer satisfaction rating.
        optional double average_customer_satisfaction_rating = 8;

        // Average QA normalized score.
        // Will exclude 0's in average calculation.
        optional double average_qa_normalized_score = 7;

        // Average QA normalized score for all the tags.
        repeated QaTagScore qa_tag_scores = 9;

        // Average QA normalized score averaged for questions averaged across
        // all revisions of the parent scorecard.
        // Will be only populated if the request specifies a dimension of
        // QA_QUESTION_ID.
        optional double average_qa_question_normalized_score = 10;
      }

      // The measure included in this data point.
      oneof measure {
        // The measure related to conversations.
        ConversationMeasure conversation_measure = 2;
      }

      // The interval that this data point represents.
      //
      // * If this is the total data point, the interval is
      // [starting create time, ending create time) from the request.
      // * If this a data point from the time series, the interval is
      // [time, time + time granularity from the request).
      google.type.Interval interval = 1;
    }

    // A time series of metric values.
    message TimeSeries {
      // The data points that make up the time series .
      repeated DataPoint data_points = 4;
    }

    // A unique combination of dimensions that this slice represents.
    repeated Dimension dimensions = 1;

    // The total metric value. The interval of this data point is
    // [starting create time, ending create time) from the request.
    DataPoint total = 2;

    // A time series of metric values. This is only populated if the request
    // specifies a time granularity other than NONE.
    TimeSeries time_series = 3;
  }

  // Required. The location of the data.
  // "projects/{project}/locations/{location}"
  string location = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The metrics last update time.
  google.protobuf.Timestamp update_time = 3;

  // A slice contains a total and (if the request specified a time granularity)
  // a time series of metric values. Each slice contains a unique combination of
  // the cardinality of dimensions from the request.
  repeated Slice slices = 2;

  // The macro average slice contains aggregated averages across the selected
  // dimension. i.e. if group_by agent is specified this field will contain the
  // average across all agents.
  // This field is only populated if the request specifies a Dimension.
  optional Slice macro_average_slice = 4;
}

// The metadata from querying metrics.
message QueryMetricsMetadata {}

// The request for creating a QaQuestion.
message CreateQaQuestionRequest {
  // Required. The parent resource of the QaQuestion.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecardRevision"
    }
  ];

  // Required. The QaQuestion to create.
  QaQuestion qa_question = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A unique ID for the new question. This ID will become the final
  // component of the question's resource name. If no ID is specified, a
  // server-generated ID will be used.
  //
  // This value should be 4-64 characters and must match the regular
  // expression `^[a-z0-9-]{4,64}$`. Valid characters are `[a-z][0-9]-`.
  string qa_question_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request for a QaQuestion.
message GetQaQuestionRequest {
  // Required. The name of the QaQuestion to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaQuestion"
    }
  ];
}

// Request to list QaQuestions.
message ListQaQuestionsRequest {
  // Required. The parent resource of the questions.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecardRevision"
    }
  ];

  // Optional. The maximum number of questions to return in the response. If the
  // value is zero, the service will select a default size. A call might return
  // fewer objects than requested. A non-empty `next_page_token` in the response
  // indicates that more data is available.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value returned by the last `ListQaQuestionsResponse`. This
  // value indicates that this is a continuation of a prior `ListQaQuestions`
  // call and that the system should return the next page of data.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response from a ListQaQuestions request.
message ListQaQuestionsResponse {
  // The QaQuestions under the parent.
  repeated QaQuestion qa_questions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request for updating a QaQuestion.
message UpdateQaQuestionRequest {
  // Required. The QaQuestion to update.
  QaQuestion qa_question = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. All possible fields can be
  // updated by passing `*`, or a subset of the following updateable fields can
  // be provided:
  //
  // * `abbreviation`
  // * `answer_choices`
  // * `answer_instructions`
  // * `order`
  // * `question_body`
  // * `tags`
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request for deleting a QaQuestion.
message DeleteQaQuestionRequest {
  // Required. The name of the QaQuestion to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaQuestion"
    }
  ];
}

// The request for creating a QaScorecard.
message CreateQaScorecardRequest {
  // Required. The parent resource of the QaScorecard.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The QaScorecard to create.
  QaScorecard qa_scorecard = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A unique ID for the new QaScorecard. This ID will become the
  // final component of the QaScorecard's resource name. If no ID is specified,
  // a server-generated ID will be used.
  //
  // This value should be 4-64 characters and must match the regular
  // expression `^[a-z0-9-]{4,64}$`. Valid characters are `[a-z][0-9]-`.
  string qa_scorecard_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request for a QaScorecard. By default, returns the latest revision.
message GetQaScorecardRequest {
  // Required. The name of the QaScorecard to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecard"
    }
  ];
}

// The request for updating a QaScorecard.
message UpdateQaScorecardRequest {
  // Required. The QaScorecard to update.
  QaScorecard qa_scorecard = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated. All possible fields can be
  // updated by passing `*`, or a subset of the following updateable fields can
  // be provided:
  //
  // * `description`
  // * `display_name`
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request for deleting a QaScorecard.
message DeleteQaScorecardRequest {
  // Required. The name of the QaScorecard to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecard"
    }
  ];

  // Optional. If set to true, all of this QaScorecard's child resources will
  // also be deleted. Otherwise, the request will only succeed if it has none.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The request for creating a QaScorecardRevision.
message CreateQaScorecardRevisionRequest {
  // Required. The parent resource of the QaScorecardRevision.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecard"
    }
  ];

  // Required. The QaScorecardRevision to create.
  QaScorecardRevision qa_scorecard_revision = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A unique ID for the new QaScorecardRevision. This ID will become
  // the final component of the QaScorecardRevision's resource name. If no ID is
  // specified, a server-generated ID will be used.
  //
  // This value should be 4-64 characters and must match the regular
  // expression `^[a-z0-9-]{4,64}$`. Valid characters are `[a-z][0-9]-`.
  string qa_scorecard_revision_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request for a QaScorecardRevision.
message GetQaScorecardRevisionRequest {
  // Required. The name of the QaScorecardRevision to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecardRevision"
    }
  ];
}

// Request for TuneQaScorecardRevision endpoint.
message TuneQaScorecardRevisionRequest {
  // Required. The parent resource for new fine tuning job instance.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecardRevision"
    }
  ];

  // Required. Filter for selecting the feedback labels that needs to be
  // used for training.
  // This filter can be used to limit the feedback labels used for tuning to a
  // feedback labels created or updated for a specific time-window etc.
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Run in validate only mode, no fine tuning will actually run.
  // Data quality validations like training data distributions will run.
  // Even when set to false, the data quality validations will still run but
  // once the validations complete we will proceed with the fine tune, if
  // applicable.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response for TuneQaScorecardRevision endpoint.
message TuneQaScorecardRevisionResponse {}

// Metadata for TuneQaScorecardRevision endpoint.
message TuneQaScorecardRevisionMetadata {
  // Contains validation results for a question in the tuning request.
  message QaQuestionDatasetValidationResult {
    // Output only. The resource path of the question whose dataset was
    // evaluated for tuning.
    string question = 1 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "contactcenterinsights.googleapis.com/QaQuestion"
      }
    ];

    // A list of any applicable data validation warnings about the question's
    // feedback labels.
    repeated DatasetValidationWarning dataset_validation_warnings = 2;

    // The number of valid feedback labels in the question's dataset.
    int32 valid_feedback_labels_count = 3;
  }

  // Contains performance metrics for each QaQuestion in the
  // TuneScorecardRevision request.
  message QaQuestionDatasetTuningMetrics {
    // Performance metrics for the question's dataset calculated over the tuned
    // model.
    message Metrics {
      // Accuracy of the question's dataset.
      double accuracy = 1;
    }

    // Output only. The resource path of the question whose dataset was
    // evaluated for tuning.
    string question = 1 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "contactcenterinsights.googleapis.com/QaQuestion"
      }
    ];

    // Output only. The metrics for the question's dataset.
    Metrics metrics = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The original request.
  TuneQaScorecardRevisionRequest request = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The results of data validation per question in the request.
  repeated QaQuestionDatasetValidationResult
      qa_question_dataset_validation_results = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The metrics for each QaQuestion in the TuneScorecardRevision
  // request.
  repeated QaQuestionDatasetTuningMetrics qa_question_dataset_tuning_metrics = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The percentage of the tuning job that has completed. Always
  // between 0 and 1 where 0 indicates the job has not started i.e. 0% and 1
  // indicates the job has completed i.e. 100%.
  double tuning_completion_ratio = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request to deploy a QaScorecardRevision
message DeployQaScorecardRevisionRequest {
  // Required. The name of the QaScorecardRevision to deploy.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecardRevision"
    }
  ];
}

// The request to undeploy a QaScorecardRevision
message UndeployQaScorecardRevisionRequest {
  // Required. The name of the QaScorecardRevision to undeploy.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecardRevision"
    }
  ];
}

// The request to delete a QaScorecardRevision.
message DeleteQaScorecardRevisionRequest {
  // Required. The name of the QaScorecardRevision to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecardRevision"
    }
  ];

  // Optional. If set to true, all of this QaScorecardRevision's child resources
  // will also be deleted. Otherwise, the request will only succeed if it has
  // none.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request to list QaScorecards.
message ListQaScorecardsRequest {
  // Required. The parent resource of the scorecards.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The maximum number of scorecards to return in the response. If
  // the value is zero, the service will select a default size. A call might
  // return fewer objects than requested. A non-empty `next_page_token` in the
  // response indicates that more data is available.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value returned by the last `ListQaScorecardsResponse`. This
  // value indicates that this is a continuation of a prior `ListQaScorecards`
  // call and that the system should return the next page of data.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response from a ListQaScorecards request.
message ListQaScorecardsResponse {
  // The QaScorecards under the parent.
  repeated QaScorecard qa_scorecards = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request to list QaScorecardRevisions
message ListQaScorecardRevisionsRequest {
  // Required. The parent resource of the scorecard revisions. To list all
  // revisions of all scorecards, substitute the QaScorecard ID with a '-'
  // character.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/QaScorecard"
    }
  ];

  // Optional. The maximum number of scorecard revisions to return in the
  // response. If the value is zero, the service will select a default size. A
  // call might return fewer objects than requested. A non-empty
  // `next_page_token` in the response indicates that more data is available.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value returned by the last
  // `ListQaScorecardRevisionsResponse`. This value indicates that this is a
  // continuation of a prior `ListQaScorecardRevisions` call and that the system
  // should return the next page of data.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter to reduce results to a specific subset. Useful for
  // querying scorecard revisions with specific properties.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response from a ListQaScorecardRevisions request.
message ListQaScorecardRevisionsResponse {
  // The QaScorecards under the parent.
  repeated QaScorecardRevision qa_scorecard_revisions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request for creating a feedback label.
message CreateFeedbackLabelRequest {
  // Required. The parent resource of the feedback label.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Conversation"
    }
  ];

  // Optional. The ID of the feedback label to create.
  // If one is not specified it will be generated by the server.
  string feedback_label_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The feedback label to create.
  FeedbackLabel feedback_label = 3 [(google.api.field_behavior) = REQUIRED];
}

// The request for listing feedback labels.
message ListFeedbackLabelsRequest {
  // Required. The parent resource of the feedback labels.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/Conversation"
    }
  ];

  // Optional. A filter to reduce results to a specific subset. Supports
  // disjunctions (OR) and conjunctions (AND). Automatically sorts by
  // conversation ID. To sort by all feedback labels in a project see
  // ListAllFeedbackLabels.
  //
  // Supported fields:
  //
  // * `issue_model_id`
  // * `qa_question_id`
  // * `qa_scorecard_id`
  // * `min_create_time`
  // * `max_create_time`
  // * `min_update_time`
  // * `max_update_time`
  // * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of feedback labels to return in the response.
  // A valid page size ranges from 0 to 100,000 inclusive. If the page size is
  // zero or unspecified, a default page size of 100 will be chosen. Note that a
  // call might return fewer results than the requested page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value returned by the last `ListFeedbackLabelsResponse`. This
  // value indicates that this is a continuation of a prior `ListFeedbackLabels`
  // call and that the system should return the next page of data.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response for listing feedback labels.
message ListFeedbackLabelsResponse {
  // The feedback labels that match the request.
  repeated FeedbackLabel feedback_labels = 1;

  // The next page token.
  string next_page_token = 2;
}

// The request for getting a feedback label.
message GetFeedbackLabelRequest {
  // Required. The name of the feedback label to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/FeedbackLabel"
    }
  ];
}

// The request for updating a feedback label.
message UpdateFeedbackLabelRequest {
  // Required. The feedback label to update.
  FeedbackLabel feedback_label = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request for deleting a feedback label.
message DeleteFeedbackLabelRequest {
  // Required. The name of the feedback label to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contactcenterinsights.googleapis.com/FeedbackLabel"
    }
  ];
}

// The request for listing all feedback labels.
message ListAllFeedbackLabelsRequest {
  // Required. The parent resource of all feedback labels per project.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The maximum number of feedback labels to return in the response.
  // A valid page size ranges from 0 to 100,000 inclusive. If the page size is
  // zero or unspecified, a default page size of 100 will be chosen. Note that a
  // call might return fewer results than the requested page size.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value returned by the last `ListAllFeedbackLabelsResponse`.
  // This value indicates that this is a continuation of a prior
  // `ListAllFeedbackLabels` call and that the system should return the next
  // page of data.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter to reduce results to a specific subset in the entire
  // project. Supports disjunctions (OR) and conjunctions (AND).
  //
  // Supported fields:
  //
  // * `issue_model_id`
  // * `qa_question_id`
  // * `min_create_time`
  // * `max_create_time`
  // * `min_update_time`
  // * `max_update_time`
  // * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response for listing all feedback labels.
message ListAllFeedbackLabelsResponse {
  // The feedback labels that match the request.
  repeated FeedbackLabel feedback_labels = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request for bulk uploading feedback labels.
message BulkUploadFeedbackLabelsRequest {
  // Google Cloud Storage Object details to get the feedback label file from.
  message GcsSource {
    // All permissible file formats.
    enum Format {
      // Unspecified format.
      FORMAT_UNSPECIFIED = 0;

      // CSV format.
      CSV = 1;

      // JSON format.
      JSON = 2;
    }

    // Required. File format which will be ingested.
    Format format = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The Google Cloud Storage URI of the file to import.
    // Format: `gs://bucket_name/object_name`
    string object_uri = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Configuration for an external data store containing objects that will
  // be converted to FeedbackLabels.
  oneof source {
    // A cloud storage bucket source.
    GcsSource gcs_source = 2;
  }

  // Required. The parent resource for new feedback labels.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. If set, upload will not happen and the labels will be validated.
  // If not set, then default behavior will be to upload the labels after
  // validation is complete.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response for the Bulk Upload Feedback Labels API.
message BulkUploadFeedbackLabelsResponse {}

// Metadata for the Bulk Upload Feedback Labels API.
message BulkUploadFeedbackLabelsMetadata {
  // Statistics for BulkUploadFeedbackLabels operation.
  message UploadStats {
    // The number of objects processed during the upload operation.
    int32 processed_object_count = 1;

    // The number of objects skipped because of failed validation
    int32 failed_validation_count = 2;

    // The number of new feedback labels added during this ingest operation.
    int32 successful_upload_count = 3;
  }

  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The original request for ingest.
  BulkUploadFeedbackLabelsRequest request = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Partial errors during ingest operation that might cause the operation
  // output to be incomplete.
  repeated google.rpc.Status partial_errors = 4;

  // Output only. Statistics for BulkUploadFeedbackLabels operation.
  UploadStats upload_stats = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request for the BulkDownloadFeedbackLabel endpoint.
message BulkDownloadFeedbackLabelsRequest {
  // Google Cloud Storage Object details to write the feedback labels to.
  message GcsDestination {
    // All permissible file formats.
    // See `records_per_file_count` to override the default number of records
    // per file.
    enum Format {
      // Unspecified format.
      FORMAT_UNSPECIFIED = 0;

      // CSV format.
      // 1,000 labels are stored per CSV file by default.
      CSV = 1;

      // JSON format.
      // 1 label stored per JSON file by default.
      JSON = 2;
    }

    // Required. File format in which the labels will be exported.
    Format format = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The Google Cloud Storage URI to write the feedback labels to.
    // The file name will be used as a prefix for the files written to the
    // bucket if the output needs to be split across multiple files, otherwise
    // it will be used as is. The file extension will be appended to the file
    // name based on the format selected.
    //  E.g. `gs://bucket_name/object_uri_prefix`
    string object_uri = 2 [(google.api.field_behavior) = REQUIRED];

    // Optional. Add whitespace to the JSON file. Makes easier to read, but
    // increases file size. Only applicable for JSON format.
    bool add_whitespace = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Always print fields with no presence.
    // This is useful for printing fields that are not set, like implicit 0
    // value or empty lists/maps. Only applicable for JSON format.
    bool always_print_empty_fields = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The number of records per file. Applicable for either format.
    int64 records_per_file_count = 5 [(google.api.field_behavior) = OPTIONAL];
  }

  // Possible feedback label types that will be downloaded.
  enum FeedbackLabelType {
    // Unspecified format
    FEEDBACK_LABEL_TYPE_UNSPECIFIED = 0;

    // Downloaded file will contain all Quality AI labels from the latest
    // scorecard revision.
    QUALITY_AI = 1;

    // Downloaded file will contain only Topic Modeling labels.
    TOPIC_MODELING = 2;
  }

  // Configuration for an external data store to which the feedback labels
  // will be written to.
  oneof destination {
    // A cloud storage bucket destination.
    GcsDestination gcs_destination = 3;
  }

  // Required. The parent resource for new feedback labels.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. A filter to reduce results to a specific subset. Supports
  // disjunctions (OR) and conjunctions (AND).
  //
  // Supported fields:
  //
  // * `issue_model_id`
  // * `qa_question_id`
  // * `qa_scorecard_id`
  // * `min_create_time`
  // * `max_create_time`
  // * `min_update_time`
  // * `max_update_time`
  // * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Limits the maximum number of feedback labels that will be
  // downloaded. The first `N` feedback labels will be downloaded.
  int32 max_download_count = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The type of feedback labels that will be downloaded.
  FeedbackLabelType feedback_label_type = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter parent conversations to download feedback labels for.
  // When specified, the feedback labels will be downloaded for the
  // conversations that match the filter.
  // If `template_qa_scorecard_id` is set, all the conversations that match the
  // filter will be paired with the questions under the scorecard for labeling.
  string conversation_filter = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, a template for labeling conversations and scorecard
  // questions will be created from the conversation_filter and the questions
  // under the scorecard(s). The feedback label `filter` will be ignored.
  repeated string template_qa_scorecard_id = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// Response for the BulkDownloadFeedbackLabel endpoint.
message BulkDownloadFeedbackLabelsResponse {}

// Metadata for the BulkDownloadFeedbackLabel endpoint.
message BulkDownloadFeedbackLabelsMetadata {
  // Statistics for BulkDownloadFeedbackLabels operation.
  message DownloadStats {
    // The number of objects processed during the download operation.
    int32 processed_object_count = 1;

    // The number of new feedback labels downloaded during this operation.
    // Different from "processed" because some labels might not be downloaded
    // because an error.
    int32 successful_download_count = 2;

    // Total number of files written to the provided Cloud Storage bucket.
    int32 total_files_written = 3;

    // Output only. Full name of the files written to Cloud storage.
    repeated string file_names = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The original request for download.
  BulkDownloadFeedbackLabelsRequest request = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Partial errors during ingest operation that might cause the operation
  // output to be incomplete.
  repeated google.rpc.Status partial_errors = 4;

  // Output only. Statistics for BulkDownloadFeedbackLabels operation.
  DownloadStats download_stats = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
