# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "gateway_proto",
    srcs = [
        "control.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
    ],
)

proto_library_with_info(
    name = "gateway_proto_with_info",
    deps = [
        ":gateway_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_proto_library",
)

java_proto_library(
    name = "gateway_java_proto",
    deps = [":gateway_proto"],
)

java_gapic_library(
    name = "gateway_java_gapic",
    srcs = [":gateway_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "connectgateway_v1beta1_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "connectgateway_v1beta1.yaml",
    test_deps = [
    ],
    transport = "rest",
    deps = [
        ":gateway_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "gateway_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.gkeconnect.gateway.v1beta1.GatewayControlClientTest",
    ],
    runtime_deps = [":gateway_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-gkeconnect-gateway-v1beta1-java",
    include_samples = True,
    transport = "rest",
    deps = [
        ":gateway_java_gapic",
        ":gateway_java_proto",
        ":gateway_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "gateway_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/gkeconnect/gateway/apiv1beta1/gatewaypb",
    protos = [":gateway_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "gateway_go_gapic",
    srcs = [":gateway_proto_with_info"],
    grpc_service_config = "connectgateway_v1beta1_grpc_service_config.json",
    importpath = "cloud.google.com/go/gkeconnect/gateway/apiv1beta1;gateway",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = False,
    service_yaml = "connectgateway_v1beta1.yaml",
    transport = "rest",
    deps = [
        ":gateway_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-gkeconnect-gateway-v1beta1-go",
    deps = [
        ":gateway_go_gapic",
        ":gateway_go_gapic_srcjar-metadata.srcjar",
        ":gateway_go_gapic_srcjar-snippets.srcjar",
        ":gateway_go_gapic_srcjar-test.srcjar",
        ":gateway_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "gateway_py_gapic",
    srcs = [":gateway_proto"],
    grpc_service_config = "connectgateway_v1beta1_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-gke-connect-gateway"],
    rest_numeric_enums = False,
    service_yaml = "connectgateway_v1beta1.yaml",
    transport = "rest",
    deps = [
    ],
)

py_test(
    name = "gateway_py_gapic_test",
    srcs = [
        "gateway_py_gapic_pytest.py",
        "gateway_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":gateway_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "gkeconnect-gateway-v1beta1-py",
    deps = [
        ":gateway_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "gateway_php_proto",
    deps = [":gateway_proto"],
)

php_gapic_library(
    name = "gateway_php_gapic",
    srcs = [":gateway_proto_with_info"],
    grpc_service_config = "connectgateway_v1beta1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = False,
    service_yaml = "connectgateway_v1beta1.yaml",
    transport = "rest",
    deps = [
        ":gateway_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-gkeconnect-gateway-v1beta1-php",
    deps = [
        ":gateway_php_gapic",
        ":gateway_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "gateway_nodejs_gapic",
    package_name = "@google-cloud/gke-connect-gateway",
    src = ":gateway_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "connectgateway_v1beta1_grpc_service_config.json",
    package = "google.cloud.gkeconnect.gateway.v1beta1",
    rest_numeric_enums = False,
    service_yaml = "connectgateway_v1beta1.yaml",
    transport = "rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "gkeconnect-gateway-v1beta1-nodejs",
    deps = [
        ":gateway_nodejs_gapic",
        ":gateway_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "gateway_ruby_proto",
    deps = [":gateway_proto"],
)

ruby_grpc_library(
    name = "gateway_ruby_grpc",
    srcs = [":gateway_proto"],
    deps = [":gateway_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "gateway_ruby_gapic",
    srcs = [":gateway_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=connectgateway.googleapis.com",
        "ruby-cloud-api-shortname=connectgateway",
        "ruby-cloud-env-prefix=GKE_CONNECT_GATEWAY",
        "ruby-cloud-gem-name=google-cloud-gke_connect-gateway-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/anthos/multicluster-management/gateway/",
    ],
    grpc_service_config = "connectgateway_v1beta1_grpc_service_config.json",
    rest_numeric_enums = False,
    ruby_cloud_description = "The Connect gateway builds on the power of fleets to let Anthos users connect to and run commands against registered Anthos clusters in a simple, consistent, and secured way, whether the clusters are on Google Cloud, other public clouds, or on premises, and makes it easier to automate DevOps processes across all your clusters.",
    ruby_cloud_title = "Connect Gateway V1beta1",
    service_yaml = "connectgateway_v1beta1.yaml",
    transport = "rest",
    deps = [
        ":gateway_ruby_grpc",
        ":gateway_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-gkeconnect-gateway-v1beta1-ruby",
    deps = [
        ":gateway_ruby_gapic",
        ":gateway_ruby_grpc",
        ":gateway_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "gateway_csharp_proto",
    extra_opts = [],
    deps = [":gateway_proto"],
)

csharp_grpc_library(
    name = "gateway_csharp_grpc",
    srcs = [":gateway_proto"],
    deps = [":gateway_csharp_proto"],
)

csharp_gapic_library(
    name = "gateway_csharp_gapic",
    srcs = [":gateway_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "connectgateway_v1beta1_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "connectgateway_v1beta1.yaml",
    transport = "rest",
    deps = [
        ":gateway_csharp_grpc",
        ":gateway_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-gkeconnect-gateway-v1beta1-csharp",
    deps = [
        ":gateway_csharp_gapic",
        ":gateway_csharp_grpc",
        ":gateway_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "gateway_cc_proto",
    deps = [":gateway_proto"],
)

cc_grpc_library(
    name = "gateway_cc_grpc",
    srcs = [":gateway_proto"],
    grpc_only = True,
    deps = [":gateway_cc_proto"],
)
