// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkeconnect.gateway.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.GkeConnect.Gateway.V1Beta1";
option go_package = "cloud.google.com/go/gkeconnect/gateway/apiv1beta1/gatewaypb;gatewaypb";
option java_multiple_files = true;
option java_outer_classname = "ControlProto";
option java_package = "com.google.cloud.gkeconnect.gateway.v1beta1";
option php_namespace = "Google\\Cloud\\GkeConnect\\Gateway\\V1beta1";
option ruby_package = "Google::Cloud::GkeConnect::Gateway::V1beta1";

// GatewayControl is the control plane API for Connect Gateway.
service GatewayControl {
  option (google.api.default_host) = "connectgateway.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // GenerateCredentials provides connection information that allows a user to
  // access the specified membership using Connect Gateway.
  rpc GenerateCredentials(GenerateCredentialsRequest)
      returns (GenerateCredentialsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/memberships/*}:generateCredentials"
    };
  }
}

// A request for connection information for a particular membership.
message GenerateCredentialsRequest {
  // Operating systems requiring specialized kubeconfigs.
  enum OperatingSystem {
    // Generates a kubeconfig that works for all operating systems not defined
    // below.
    OPERATING_SYSTEM_UNSPECIFIED = 0;

    // Generates a kubeconfig that is specifically designed to work with
    // Windows.
    OPERATING_SYSTEM_WINDOWS = 1;
  }

  // Required. The Fleet membership resource.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Whether to force the use of Connect Agent-based transport.
  //
  // This will return a configuration that uses Connect Agent as the underlying
  // transport mechanism for cluster types that would otherwise have used a
  // different transport. Requires that Connect Agent be installed on the
  // cluster. Setting this field to false is equivalent to not setting it.
  bool force_use_agent = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Connect Gateway version to be used in the resulting
  // configuration.
  //
  // Leave this field blank to let the server choose the version (recommended).
  string version = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The namespace to use in the kubeconfig context.
  //
  // If this field is specified, the server will set the `namespace` field in
  // kubeconfig context. If not specified, the `namespace` field is omitted.
  string kubernetes_namespace = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The operating system where the kubeconfig will be used.
  OperatingSystem operating_system = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Connection information for a particular membership.
message GenerateCredentialsResponse {
  // A full YAML kubeconfig in serialized format.
  bytes kubeconfig = 1;

  // The generated URI of the cluster as accessed through the Connect Gateway
  // API.
  string endpoint = 2;
}
