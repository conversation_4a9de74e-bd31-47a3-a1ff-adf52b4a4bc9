# This build file includes a target for the Ruby wrapper library for
# google-cloud-gke_connect-gateway.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for connectgateway.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "connectgateway_ruby_wrapper",
    srcs = ["//google/cloud/gkeconnect/gateway/v1:gateway_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-gke_connect-gateway",
        "ruby-cloud-env-prefix=GKE_CONNECT_GATEWAY",
        "ruby-cloud-wrapper-of=v1:0.2",
        "ruby-cloud-product-url=https://cloud.google.com/anthos/multicluster-management/gateway/",
        "ruby-cloud-api-id=connectgateway.googleapis.com",
        "ruby-cloud-api-shortname=connectgateway",
    ],
    transport = "rest",
    ruby_cloud_description = "The Connect gateway builds on the power of fleets to let Anthos users connect to and run commands against registered Anthos clusters in a simple, consistent, and secured way, whether the clusters are on Google Cloud, other public clouds, or on premises, and makes it easier to automate DevOps processes across all your clusters.",
    ruby_cloud_title = "Connect Gateway",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-gkeconnect-gateway-ruby",
    deps = [
        ":connectgateway_ruby_wrapper",
    ],
)
