# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "migrationcenter_proto",
    srcs = [
        "migrationcenter.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:date_proto",
        "//google/type:money_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "migrationcenter_proto_with_info",
    deps = [
        ":migrationcenter_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "migrationcenter_java_proto",
    deps = [":migrationcenter_proto"],
)

java_grpc_library(
    name = "migrationcenter_java_grpc",
    srcs = [":migrationcenter_proto"],
    deps = [":migrationcenter_java_proto"],
)

java_gapic_library(
    name = "migrationcenter_java_gapic",
    srcs = [":migrationcenter_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "migrationcenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "migrationcenter_v1.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        ":migrationcenter_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":migrationcenter_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "migrationcenter_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.migrationcenter.v1.MigrationCenterClientHttpJsonTest",
        "com.google.cloud.migrationcenter.v1.MigrationCenterClientTest",
    ],
    runtime_deps = [":migrationcenter_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-migrationcenter-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":migrationcenter_java_gapic",
        ":migrationcenter_java_grpc",
        ":migrationcenter_java_proto",
        ":migrationcenter_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "migrationcenter_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/migrationcenter/apiv1/migrationcenterpb",
    protos = [":migrationcenter_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:date_go_proto",
        "//google/type:money_go_proto",
    ],
)

go_gapic_library(
    name = "migrationcenter_go_gapic",
    srcs = [":migrationcenter_proto_with_info"],
    grpc_service_config = "migrationcenter_grpc_service_config.json",
    importpath = "cloud.google.com/go/migrationcenter/apiv1;migrationcenter",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "migrationcenter_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":migrationcenter_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-migrationcenter-v1-go",
    deps = [
        ":migrationcenter_go_gapic",
        ":migrationcenter_go_gapic_srcjar-metadata.srcjar",
        ":migrationcenter_go_gapic_srcjar-snippets.srcjar",
        ":migrationcenter_go_gapic_srcjar-test.srcjar",
        ":migrationcenter_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "migrationcenter_py_gapic",
    srcs = [":migrationcenter_proto"],
    grpc_service_config = "migrationcenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "migrationcenter_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "migrationcenter_py_gapic_test",
    srcs = [
        "migrationcenter_py_gapic_pytest.py",
        "migrationcenter_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":migrationcenter_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "migrationcenter-v1-py",
    deps = [
        ":migrationcenter_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "migrationcenter_php_proto",
    deps = [":migrationcenter_proto"],
)

php_gapic_library(
    name = "migrationcenter_php_gapic",
    srcs = [":migrationcenter_proto_with_info"],
    grpc_service_config = "migrationcenter_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "migrationcenter_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":migrationcenter_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-migrationcenter-v1-php",
    deps = [
        ":migrationcenter_php_gapic",
        ":migrationcenter_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "migrationcenter_nodejs_gapic",
    package_name = "@google-cloud/migrationcenter",
    src = ":migrationcenter_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "migrationcenter_grpc_service_config.json",
    package = "google.cloud.migrationcenter.v1",
    rest_numeric_enums = True,
    service_yaml = "migrationcenter_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "migrationcenter-v1-nodejs",
    deps = [
        ":migrationcenter_nodejs_gapic",
        ":migrationcenter_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "migrationcenter_ruby_proto",
    deps = [":migrationcenter_proto"],
)

ruby_grpc_library(
    name = "migrationcenter_ruby_grpc",
    srcs = [":migrationcenter_proto"],
    deps = [":migrationcenter_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "migrationcenter_ruby_gapic",
    srcs = [":migrationcenter_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-migration_center-v1"],
    grpc_service_config = "migrationcenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "migrationcenter_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":migrationcenter_ruby_grpc",
        ":migrationcenter_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-migrationcenter-v1-ruby",
    deps = [
        ":migrationcenter_ruby_gapic",
        ":migrationcenter_ruby_grpc",
        ":migrationcenter_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "migrationcenter_csharp_proto",
    deps = [":migrationcenter_proto"],
)

csharp_grpc_library(
    name = "migrationcenter_csharp_grpc",
    srcs = [":migrationcenter_proto"],
    deps = [":migrationcenter_csharp_proto"],
)

csharp_gapic_library(
    name = "migrationcenter_csharp_gapic",
    srcs = [":migrationcenter_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "migrationcenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "migrationcenter_v1.yaml",
    deps = [
        ":migrationcenter_csharp_grpc",
        ":migrationcenter_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-migrationcenter-v1-csharp",
    deps = [
        ":migrationcenter_csharp_gapic",
        ":migrationcenter_csharp_grpc",
        ":migrationcenter_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "migrationcenter_cc_proto",
    deps = [":migrationcenter_proto"],
)

cc_grpc_library(
    name = "migrationcenter_cc_grpc",
    srcs = [":migrationcenter_proto"],
    grpc_only = True,
    deps = [":migrationcenter_cc_proto"],
)
