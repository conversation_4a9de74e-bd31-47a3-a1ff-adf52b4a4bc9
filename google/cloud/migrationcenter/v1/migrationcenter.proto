// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.migrationcenter.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option csharp_namespace = "Google.Cloud.MigrationCenter.V1";
option go_package = "cloud.google.com/go/migrationcenter/apiv1/migrationcenterpb;migrationcenterpb";
option java_multiple_files = true;
option java_outer_classname = "MigrationCenterProto";
option java_package = "com.google.cloud.migrationcenter.v1";
option php_namespace = "Google\\Cloud\\MigrationCenter\\V1";
option ruby_package = "Google::Cloud::MigrationCenter::V1";

// Service describing handlers for resources.
service MigrationCenter {
  option (google.api.default_host) = "migrationcenter.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all the assets in a given project and location.
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/assets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of an asset.
  rpc GetAsset(GetAssetRequest) returns (Asset) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/assets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the parameters of an asset.
  rpc UpdateAsset(UpdateAssetRequest) returns (Asset) {
    option (google.api.http) = {
      patch: "/v1/{asset.name=projects/*/locations/*/assets/*}"
      body: "asset"
    };
    option (google.api.method_signature) = "asset,update_mask";
  }

  // Updates the parameters of a list of assets.
  rpc BatchUpdateAssets(BatchUpdateAssetsRequest)
      returns (BatchUpdateAssetsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/assets:batchUpdate"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
  }

  // Deletes an asset.
  rpc DeleteAsset(DeleteAssetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/assets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes list of Assets.
  rpc BatchDeleteAssets(BatchDeleteAssetsRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/assets:batchDelete"
      body: "*"
    };
    option (google.api.method_signature) = "parent,names";
  }

  // Reports a set of frames.
  rpc ReportAssetFrames(ReportAssetFramesRequest)
      returns (ReportAssetFramesResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/assets:reportAssetFrames"
      body: "frames"
    };
  }

  // Aggregates the requested fields based on provided function.
  rpc AggregateAssetsValues(AggregateAssetsValuesRequest)
      returns (AggregateAssetsValuesResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/assets:aggregateValues"
      body: "*"
    };
  }

  // Creates an import job.
  rpc CreateImportJob(CreateImportJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/importJobs"
      body: "import_job"
    };
    option (google.api.method_signature) = "parent,import_job,import_job_id";
    option (google.longrunning.operation_info) = {
      response_type: "ImportJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists all import jobs.
  rpc ListImportJobs(ListImportJobsRequest) returns (ListImportJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/importJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of an import job.
  rpc GetImportJob(GetImportJobRequest) returns (ImportJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/importJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes an import job.
  rpc DeleteImportJob(DeleteImportJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/importJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an import job.
  rpc UpdateImportJob(UpdateImportJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{import_job.name=projects/*/locations/*/importJobs/*}"
      body: "import_job"
    };
    option (google.api.method_signature) = "import_job,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ImportJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Validates an import job.
  rpc ValidateImportJob(ValidateImportJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/importJobs/*}:validate"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Runs an import job.
  rpc RunImportJob(RunImportJobRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/importJobs/*}:run"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets an import data file.
  rpc GetImportDataFile(GetImportDataFileRequest) returns (ImportDataFile) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/importJobs/*/importDataFiles/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List import data files.
  rpc ListImportDataFiles(ListImportDataFilesRequest)
      returns (ListImportDataFilesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/importJobs/*}/importDataFiles"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates an import data file.
  rpc CreateImportDataFile(CreateImportDataFileRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/importJobs/*}/importDataFiles"
      body: "import_data_file"
    };
    option (google.api.method_signature) =
        "parent,import_data_file,import_data_file_id";
    option (google.longrunning.operation_info) = {
      response_type: "ImportDataFile"
      metadata_type: "OperationMetadata"
    };
  }

  // Delete an import data file.
  rpc DeleteImportDataFile(DeleteImportDataFileRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/importJobs/*/importDataFiles/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists all groups in a given project and location.
  rpc ListGroups(ListGroupsRequest) returns (ListGroupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/groups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a group.
  rpc GetGroup(GetGroupRequest) returns (Group) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/groups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new group in a given project and location.
  rpc CreateGroup(CreateGroupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/groups"
      body: "group"
    };
    option (google.api.method_signature) = "parent,group,group_id";
    option (google.longrunning.operation_info) = {
      response_type: "Group"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a group.
  rpc UpdateGroup(UpdateGroupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{group.name=projects/*/locations/*/groups/*}"
      body: "group"
    };
    option (google.api.method_signature) = "group,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Group"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a group.
  rpc DeleteGroup(DeleteGroupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/groups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Adds assets to a group.
  rpc AddAssetsToGroup(AddAssetsToGroupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{group=projects/*/locations/*/groups/*}:addAssets"
      body: "*"
    };
    option (google.api.method_signature) = "group";
    option (google.longrunning.operation_info) = {
      response_type: "Group"
      metadata_type: "OperationMetadata"
    };
  }

  // Removes assets from a group.
  rpc RemoveAssetsFromGroup(RemoveAssetsFromGroupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{group=projects/*/locations/*/groups/*}:removeAssets"
      body: "*"
    };
    option (google.api.method_signature) = "group";
    option (google.longrunning.operation_info) = {
      response_type: "Group"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists all error frames in a given source and location.
  rpc ListErrorFrames(ListErrorFramesRequest)
      returns (ListErrorFramesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/sources/*}/errorFrames"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of an error frame.
  rpc GetErrorFrame(GetErrorFrameRequest) returns (ErrorFrame) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sources/*/errorFrames/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all the sources in a given project and location.
  rpc ListSources(ListSourcesRequest) returns (ListSourcesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/sources"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a source.
  rpc GetSource(GetSourceRequest) returns (Source) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new source in a given project and location.
  rpc CreateSource(CreateSourceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/sources"
      body: "source"
    };
    option (google.api.method_signature) = "parent,source,source_id";
    option (google.longrunning.operation_info) = {
      response_type: "Source"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a source.
  rpc UpdateSource(UpdateSourceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{source.name=projects/*/locations/*/sources/*}"
      body: "source"
    };
    option (google.api.method_signature) = "source,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Source"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a source.
  rpc DeleteSource(DeleteSourceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/sources/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists all the preference sets in a given project and location.
  rpc ListPreferenceSets(ListPreferenceSetsRequest)
      returns (ListPreferenceSetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/preferenceSets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a preference set.
  rpc GetPreferenceSet(GetPreferenceSetRequest) returns (PreferenceSet) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/preferenceSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new preference set in a given project and location.
  rpc CreatePreferenceSet(CreatePreferenceSetRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/preferenceSets"
      body: "preference_set"
    };
    option (google.api.method_signature) =
        "parent,preference_set,preference_set_id";
    option (google.longrunning.operation_info) = {
      response_type: "PreferenceSet"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a preference set.
  rpc UpdatePreferenceSet(UpdatePreferenceSetRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{preference_set.name=projects/*/locations/*/preferenceSets/*}"
      body: "preference_set"
    };
    option (google.api.method_signature) = "preference_set,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "PreferenceSet"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a preference set.
  rpc DeletePreferenceSet(DeletePreferenceSetRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/preferenceSets/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets the details of regional settings.
  rpc GetSettings(GetSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/settings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the regional-level project settings.
  rpc UpdateSettings(UpdateSettingsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{settings.name=projects/*/locations/*/settings}"
      body: "settings"
    };
    option (google.api.method_signature) = "settings,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Settings"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a report configuration.
  rpc CreateReportConfig(CreateReportConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/reportConfigs"
      body: "report_config"
    };
    option (google.api.method_signature) =
        "parent,report_config,report_config_id";
    option (google.longrunning.operation_info) = {
      response_type: "ReportConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of a single ReportConfig.
  rpc GetReportConfig(GetReportConfigRequest) returns (ReportConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/reportConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ReportConfigs in a given project and location.
  rpc ListReportConfigs(ListReportConfigsRequest)
      returns (ListReportConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/reportConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a ReportConfig.
  rpc DeleteReportConfig(DeleteReportConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/reportConfigs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a report.
  rpc CreateReport(CreateReportRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/reportConfigs/*}/reports"
      body: "report"
    };
    option (google.api.method_signature) = "parent,report,report_id";
    option (google.longrunning.operation_info) = {
      response_type: "Report"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of a single Report.
  rpc GetReport(GetReportRequest) returns (Report) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/reportConfigs/*/reports/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Reports in a given ReportConfig.
  rpc ListReports(ListReportsRequest) returns (ListReportsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/reportConfigs/*}/reports"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a Report.
  rpc DeleteReport(DeleteReportRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/reportConfigs/*/reports/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// An asset represents a resource in your environment. Asset types include
// virtual machines and databases.
message Asset {
  option (google.api.resource) = {
    type: "migrationcenter.googleapis.com/Asset"
    pattern: "projects/{project}/locations/{location}/assets/{asset}"
  };

  // Output only. The full name of the asset.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the asset was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the asset was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // Generic asset attributes.
  map<string, string> attributes = 5;

  // The internal information of an asset. Only one field can be specified
  // with different attributes based on the type of the asset.
  oneof AssetDetails {
    // Output only. Asset information specific for virtual and physical
    // machines.
    MachineDetails machine_details = 6
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The list of insights associated with the asset.
  InsightList insight_list = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Performance data for the asset.
  AssetPerformanceData performance_data = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of sources contributing to the asset.
  repeated string sources = 22 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Source"
    }
  ];

  // Output only. The list of groups that the asset is assigned to.
  repeated string assigned_groups = 23 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Group"
    }
  ];
}

// The preferences that apply to all assets in a given context.
message PreferenceSet {
  option (google.api.resource) = {
    type: "migrationcenter.googleapis.com/PreferenceSet"
    pattern: "projects/{project}/locations/{location}/preferenceSets/{preference_set}"
  };

  // Output only. Name of the preference set.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the preference set was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the preference set was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-friendly display name. Maximum length is 63 characters.
  string display_name = 4;

  // A description of the preference set.
  string description = 5;

  // A set of preferences that applies to all virtual machines in the context.
  VirtualMachinePreferences virtual_machine_preferences = 6;
}

// A resource that represents the background job that imports asset frames.
message ImportJob {
  option (google.api.resource) = {
    type: "migrationcenter.googleapis.com/ImportJob"
    pattern: "projects/{project}/locations/{location}/importJobs/{import_job}"
  };

  // Enumerates possible states of an import job.
  enum ImportJobState {
    // Default value.
    IMPORT_JOB_STATE_UNSPECIFIED = 0;

    // The import job is pending.
    IMPORT_JOB_STATE_PENDING = 1;

    // The processing of the import job is ongoing.
    IMPORT_JOB_STATE_RUNNING = 2;

    // The import job processing has completed.
    IMPORT_JOB_STATE_COMPLETED = 3;

    // The import job failed to be processed.
    IMPORT_JOB_STATE_FAILED = 4;

    // The import job is being validated.
    IMPORT_JOB_STATE_VALIDATING = 5;

    // The import job contains blocking errors.
    IMPORT_JOB_STATE_FAILED_VALIDATION = 6;

    // The validation of the job completed with no blocking errors.
    IMPORT_JOB_STATE_READY = 7;
  }

  // Output only. The full name of the import job.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-friendly display name. Maximum length is 63 characters.
  string display_name = 2;

  // Output only. The timestamp when the import job was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the import job was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the import job was completed.
  google.protobuf.Timestamp complete_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the import job.
  ImportJobState state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 7;

  // Required. Reference to a source.
  string asset_source = 8 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Source"
    }
  ];

  // Holds the various report types of an import job.
  oneof report {
    // Output only. The report with the validation results of the import job.
    ValidationReport validation_report = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The report with the results of running the import job.
    ExecutionReport execution_report = 11
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// A resource that represents a payload file in an import job.
message ImportDataFile {
  option (google.api.resource) = {
    type: "migrationcenter.googleapis.com/ImportDataFile"
    pattern: "projects/{project}/locations/{location}/importJobs/{import_job}/importDataFiles/{import_data_file}"
  };

  // Enumerates possible states of an import data file.
  enum State {
    // Default value.
    STATE_UNSPECIFIED = 0;

    // The data file is being created.
    CREATING = 1;

    // The data file completed initialization.
    ACTIVE = 2;
  }

  // Output only. The name of the file.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-friendly display name. Maximum length is 63 characters.
  string display_name = 6;

  // Required. The payload format.
  ImportJobFormat format = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The timestamp when the file was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the import data file.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  oneof file_info {
    // Information about a file that is uploaded to a storage service.
    UploadFileInfo upload_file_info = 5;
  }
}

// A resource that represents an asset group.
// The purpose of an asset group is to bundle a set of assets that have
// something in common, while allowing users to add annotations to the group.
// An asset can belong to multiple groups.
message Group {
  option (google.api.resource) = {
    type: "migrationcenter.googleapis.com/Group"
    pattern: "projects/{project}/locations/{location}/groups/{group}"
  };

  // Output only. The name of the group.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the group was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the group was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs.
  map<string, string> labels = 4;

  // User-friendly display name.
  string display_name = 5;

  // The description of the resource.
  string description = 6;
}

// Message representing a frame which failed to be processed due to an error.
message ErrorFrame {
  option (google.api.resource) = {
    type: "migrationcenter.googleapis.com/ErrorFrame"
    pattern: "projects/{project}/locations/{location}/sources/{source}/errorFrames/{error_frame}"
  };

  // Output only. The identifier of the ErrorFrame.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. All the violations that were detected for the frame.
  repeated FrameViolationEntry violations = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The frame that was originally reported.
  AssetFrame original_frame = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Frame ingestion time.
  google.protobuf.Timestamp ingestion_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Source represents an object from which asset information is
// streamed to Migration Center.
message Source {
  option (google.api.resource) = {
    type: "migrationcenter.googleapis.com/Source"
    pattern: "projects/{project}/locations/{location}/sources/{source}"
  };

  enum SourceType {
    // Unspecified
    SOURCE_TYPE_UNKNOWN = 0;

    // Manually uploaded file (e.g. CSV)
    SOURCE_TYPE_UPLOAD = 1;

    // Guest-level info
    SOURCE_TYPE_GUEST_OS_SCAN = 2;

    // Inventory-level scan
    SOURCE_TYPE_INVENTORY_SCAN = 3;

    // Third-party owned sources.
    SOURCE_TYPE_CUSTOM = 4;
  }

  // Enumerates possible states of a source.
  enum State {
    // Unspecified.
    STATE_UNSPECIFIED = 0;

    // The source is active and ready to be used.
    ACTIVE = 1;

    // In the process of being deleted.
    DELETING = 2;

    // Source is in an invalid state. Asset frames reported to it will be
    // ignored.
    INVALID = 3;
  }

  // Output only. The full name of the source.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the source was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the source was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-friendly display name.
  string display_name = 4;

  // Free-text description.
  string description = 5;

  // Data source type.
  SourceType type = 6;

  // The information confidence of the source.
  // The higher the value, the higher the confidence.
  int32 priority = 7;

  // If `true`, the source is managed by other service(s).
  bool managed = 8;

  // Output only. Number of frames that are still being processed.
  int32 pending_frame_count = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of frames that were reported by the source and
  // contained errors.
  int32 error_frame_count = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the source.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The groups and associated preference sets on which
// we can generate reports.
message ReportConfig {
  option (google.api.resource) = {
    type: "migrationcenter.googleapis.com/ReportConfig"
    pattern: "projects/{project}/locations/{location}/reportConfigs/{report_config}"
  };

  // Represents a combination of a group with a preference set.
  message GroupPreferenceSetAssignment {
    // Required. Name of the group.
    string group = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "migrationcenter.googleapis.com/Group"
      }
    ];

    // Required. Name of the Preference Set.
    string preference_set = 2 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "migrationcenter.googleapis.com/PreferenceSet"
      }
    ];
  }

  // Output only. Name of resource.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-friendly display name. Maximum length is 63 characters.
  string display_name = 4;

  // Free-text description.
  string description = 5;

  // Required. Collection of combinations of groups and preference sets.
  repeated GroupPreferenceSetAssignment group_preferenceset_assignments = 6
      [(google.api.field_behavior) = REQUIRED];
}

// Report represents a point-in-time rendering of the ReportConfig results.
message Report {
  option (google.api.resource) = {
    type: "migrationcenter.googleapis.com/Report"
    pattern: "projects/{project}/locations/{location}/reportConfigs/{report_config}/reports/{report}"
  };

  // Report type.
  enum Type {
    // Default Report type.
    TYPE_UNSPECIFIED = 0;

    // Total cost of ownership Report type.
    TOTAL_COST_OF_OWNERSHIP = 1;
  }

  // Report creation state.
  enum State {
    // Default Report creation state.
    STATE_UNSPECIFIED = 0;

    // Creating Report.
    PENDING = 1;

    // Successfully created Report.
    SUCCEEDED = 2;

    // Failed to create Report.
    FAILED = 3;
  }

  // Output only. Name of resource.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-friendly display name. Maximum length is 63 characters.
  string display_name = 4;

  // Free-text description.
  string description = 5;

  // Report type.
  Type type = 6;

  // Report creation state.
  State state = 7;

  // Output only. Summary view of the Report.
  ReportSummary summary = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting a list of assets.
message ListAssetsRequest {
  // Required. Parent value for `ListAssetsRequest`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/Asset"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Field to sort by. See https://google.aip.dev/132#ordering for more details.
  string order_by = 5;

  // View of the assets. Defaults to BASIC.
  AssetView view = 6;
}

// Response message for listing assets.
message ListAssetsResponse {
  // A list of assets.
  repeated Asset assets = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Asset.
message GetAssetRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Asset"
    }
  ];

  // View of the assets. Defaults to BASIC.
  AssetView view = 2;
}

// A request to update an asset.
message UpdateAssetRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `Asset` resource by the update.
  // The values specified in the `update_mask` field are relative to the
  // resource, not the full request.
  // A field will be overwritten if it is in the mask.
  // A single * value in the mask lets you to overwrite all fields.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  Asset asset = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to update a list of assets.
message BatchUpdateAssetsRequest {
  // Required. Parent value for batch asset update.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/Asset"
    }
  ];

  // Required. The request message specifying the resources to update.
  // A maximum of 1000 assets can be modified in a batch.
  repeated UpdateAssetRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response for updating a list of assets.
message BatchUpdateAssetsResponse {
  // Update asset content.
  // The content only includes values after field mask being applied.
  repeated Asset assets = 1;
}

// A request to delete an asset.
message DeleteAssetRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Asset"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete a list of  asset.
message BatchDeleteAssetsRequest {
  // Required. Parent value for batch asset delete.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/Asset"
    }
  ];

  // Required. The IDs of the assets to delete.
  // A maximum of 1000 assets can be deleted in a batch.
  // Format: projects/{project}/locations/{location}/assets/{name}.
  repeated string names = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Asset"
    }
  ];

  // Optional. When this value is set to `true` the request is a no-op for
  // non-existing assets. See https://google.aip.dev/135#delete-if-existing for
  // additional details. Default value is `false`.
  bool allow_missing = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to report a set of asset frames.
message ReportAssetFramesRequest {
  // Required. Parent of the resource.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Collection of frames data.
  Frames frames = 2;

  // Required. Reference to a source.
  string source = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Source"
    }
  ];
}

// A response to a call to `ReportAssetFrame`.
message ReportAssetFramesResponse {}

// A request to aggregate one or more values.
message AggregateAssetsValuesRequest {
  // Required. Parent value for `AggregateAssetsValuesRequest`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Array of aggregations to perform.
  // Up to 25 aggregations can be defined.
  repeated Aggregation aggregations = 2;

  // The aggregation will be performed on assets that match the provided filter.
  string filter = 3;
}

// A response to a request to aggregated assets values.
message AggregateAssetsValuesResponse {
  // The aggregation results.
  repeated AggregationResult results = 1;
}

// A request to create an import job.
message CreateImportJobRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/ImportJob"
    }
  ];

  // Required. ID of the import job.
  string import_job_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  ImportJob import_job = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to list import jobs.
message ListImportJobsRequest {
  // Required. Parent value for `ListImportJobsRequest`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/ImportJob"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Field to sort by. See https://google.aip.dev/132#ordering for more details.
  string order_by = 5;

  // Optional. The level of details of each import job.
  // Default value is BASIC.
  ImportJobView view = 6 [(google.api.field_behavior) = OPTIONAL];
}

// A response for listing import jobs.
message ListImportJobsResponse {
  // The list of import jobs.
  repeated ImportJob import_jobs = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to get an import job.
message GetImportJobRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/ImportJob"
    }
  ];

  // Optional. The level of details of the import job.
  // Default value is FULL.
  ImportJobView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete an import job.
message DeleteImportJobRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/ImportJob"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to `true`, any `ImportDataFiles` of this job will also be
  // deleted If set to `false`, the request only works if the job has no data
  // files.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to update an import job.
message UpdateImportJobRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `Asset` resource by the update.
  // The values specified in the `update_mask` field are relative to the
  // resource, not the full request.
  // A field will be overwritten if it is in the mask.
  // A single * value in the mask lets you to overwrite all fields.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  ImportJob import_job = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to validate an import job.
message ValidateImportJobRequest {
  // Required. The name of the import job to validate.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/ImportJob"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to run an import job.
message RunImportJobRequest {
  // Required. The name of the import job to run.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/ImportJob"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to get an import data file.
message GetImportDataFileRequest {
  // Required. Name of the ImportDataFile.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/ImportDataFile"
    }
  ];
}

// A request to list import data files of an import job.
message ListImportDataFilesRequest {
  // Required. Name of the parent of the `ImportDataFiles` resource.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/ImportDataFile"
    }
  ];

  // The maximum number of data files to return. The service may return
  // fewer than this value. If unspecified, at most 500 data files will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListImportDataFiles` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListImportDataFiles`
  // must match the call that provided the page token.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Field to sort by. See https://google.aip.dev/132#ordering for more details.
  string order_by = 5;
}

// Response for listing payload files of an import job.
message ListImportDataFilesResponse {
  // The list of import data files.
  repeated ImportDataFile import_data_files = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to create an `ImportDataFile` resource.
message CreateImportDataFileRequest {
  // Required. Name of the parent of the ImportDataFile.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/ImportDataFile"
    }
  ];

  // Required. The ID of the new data file.
  string import_data_file_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  ImportDataFile import_data_file = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete an `ImportDataFile` resource.
message DeleteImportDataFileRequest {
  // Required. Name of the ImportDataFile to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/ImportDataFile"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to list groups.
message ListGroupsRequest {
  // Required. Parent value for `ListGroupsRequest`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/Group"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Field to sort by. See https://google.aip.dev/132#ordering for more details.
  string order_by = 5;
}

// A response for listing groups.
message ListGroupsResponse {
  // The list of Group
  repeated Group groups = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to get a group.
message GetGroupRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Group"
    }
  ];
}

// A request to create a group.
message CreateGroupRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/Group"
    }
  ];

  // Required. User specified ID for the group. It will become the last
  // component of the group name. The ID must be unique within the project, must
  // conform with RFC-1034, is restricted to lower-cased letters, and has a
  // maximum length of 63 characters. The ID must match the regular expression:
  // `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
  string group_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The group resource being created.
  Group group = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to update a group.
message UpdateGroupRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `Group` resource by the update.
  // The values specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask.
  // A single * value in the mask lets you to overwrite all fields.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The group resource being updated.
  Group group = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete a group.
message DeleteGroupRequest {
  // Required. Name of the group resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Group"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to add assets to a group.
message AddAssetsToGroupRequest {
  // Required. Group reference.
  string group = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Group"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. List of assets to be added.
  // The maximum number of assets that can be added in a single request is
  // 1000.
  AssetList assets = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. When this value is set to `false` and one of the given assets is
  // already an existing member of the group, the operation fails with an
  // `Already Exists` error. When set to `true` this situation is silently
  // ignored by the server.
  //
  // Default value is `false`.
  bool allow_existing = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to remove assets from a group.
message RemoveAssetsFromGroupRequest {
  // Required. Group reference.
  string group = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Group"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. List of assets to be removed.
  // The maximum number of assets that can be removed in a single request is
  // 1000.
  AssetList assets = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. When this value is set to `false` and one of the given assets is
  // not an existing member of the group, the operation fails with a `Not Found`
  // error. When set to `true` this situation is silently ignored by the server.
  //
  // Default value is `false`.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to list error frames for a source.
message ListErrorFramesRequest {
  // Required. Parent value (the source) for `ListErrorFramesRequest`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/ErrorFrame"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Optional. An optional view mode to control the level of details of each
  // error frame. The default is a BASIC frame view.
  ErrorFrameView view = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A response for listing error frames.
message ListErrorFramesResponse {
  // The list of error frames.
  repeated ErrorFrame error_frames = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

message GetErrorFrameRequest {
  // Required. The name of the frame to retrieve.
  // Format:
  // projects/{project}/locations/{location}/sources/{source}/errorFrames/{error_frame}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/ErrorFrame"
    }
  ];

  // Optional. An optional view mode to control the level of details for the
  // frame. The default is a basic frame view.
  ErrorFrameView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request for a list of sources.
message ListSourcesRequest {
  // Required. Parent value for `ListSourcesRequest`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/Source"
    }
  ];

  // Requested page size. The server may return fewer items than requested.
  // If unspecified, the server will pick an appropriate default value.
  int32 page_size = 2;

  // A token identifying a page of results that the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Field to sort by. See https://google.aip.dev/132#ordering for more details.
  string order_by = 5;
}

// Response message for listing sources.
message ListSourcesResponse {
  // The list of sources.
  repeated Source sources = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to get a source.
message GetSourceRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Source"
    }
  ];
}

// A request to create a source.
message CreateSourceRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/Source"
    }
  ];

  // Required. User specified ID for the source. It will become the last
  // component of the source name. The ID must be unique within the project,
  // must conform with RFC-1034, is restricted to lower-cased letters, and has a
  // maximum length of 63 characters. The ID must match the regular expression:
  // `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
  string source_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Source source = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to update a source.
message UpdateSourceRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `Source` resource by the update.
  // The values specified in the `update_mask` field are relative to the
  // resource, not the full request.
  // A field will be overwritten if it is in the mask.
  // A single * value in the mask lets you to overwrite all fields.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  Source source = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete a source.
message DeleteSourceRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Source"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for listing preference sets.
message ListPreferenceSetsRequest {
  // Required. Parent value for `ListPreferenceSetsRequest`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/PreferenceSet"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, at most 500 preference sets will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Field to sort by. See https://google.aip.dev/132#ordering for more details.
  string order_by = 5;
}

// Response message for listing preference sets.
message ListPreferenceSetsResponse {
  // The list of PreferenceSets
  repeated PreferenceSet preference_sets = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to get a preference set.
message GetPreferenceSetRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/PreferenceSet"
    }
  ];
}

// A request to create a preference set.
message CreatePreferenceSetRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/PreferenceSet"
    }
  ];

  // Required. User specified ID for the preference set. It will become the last
  // component of the preference set name. The ID must be unique within the
  // project, must conform with RFC-1034, is restricted to lower-cased letters,
  // and has a maximum length of 63 characters. The ID must match the regular
  // expression
  // `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.
  string preference_set_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The preference set resource being created.
  PreferenceSet preference_set = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to update a preference set.
message UpdatePreferenceSetRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `PreferenceSet` resource by the update.
  // The values specified in the `update_mask` field are relative to the
  // resource, not the full request.
  // A field will be overwritten if it is in the mask.
  // A single * value in the mask lets you to overwrite all fields.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The preference set resource being updated.
  PreferenceSet preference_set = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete a preference set.
message DeletePreferenceSetRequest {
  // Required. Name of the group resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/PreferenceSet"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to get the settings.
message GetSettingsRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Settings"
    }
  ];
}

// A request to update the settings.
message UpdateSettingsRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `Settings` resource by the update.
  // The values specified in the `update_mask` field are relative to the
  // resource, not the full request.
  // A field will be overwritten if it is in the mask.
  // A single * value in the mask lets you to overwrite all fields.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The project settings resource being updated.
  Settings settings = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to create a `ReportConfig` resource.
message CreateReportConfigRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/ReportConfig"
    }
  ];

  // Required. User specified ID for the report config. It will become the last
  // component of the report config name. The ID must be unique within the
  // project, must conform with RFC-1034, is restricted to lower-cased letters,
  // and has a maximum length of 63 characters. The ID must match the regular
  // expression: [a-z]([a-z0-9-]{0,61}[a-z0-9])?.
  string report_config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The report config set resource being created.
  ReportConfig report_config = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete a ReportConfig.
message DeleteReportConfigRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/ReportConfig"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to `true`, any child `Reports` of this entity will also be
  // deleted. If set to `false`, the request only works if the resource has no
  // children.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to get a Report.
message GetReportRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Report"
    }
  ];

  // Determines what information to retrieve for the Report.
  ReportView view = 6;
}

// A request for a list of Reports.
message ListReportsRequest {
  // Required. Parent value for `ListReportsRequest`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/Report"
    }
  ];

  // Requested page size. The server may return fewer items than requested.
  // If unspecified, the server will pick an appropriate default value.
  int32 page_size = 2;

  // A token identifying a page of results that the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Field to sort by. See https://google.aip.dev/132#ordering for more details.
  string order_by = 5;

  // Determines what information to retrieve for each Report.
  ReportView view = 6;
}

// Response message for listing Reports.
message ListReportsResponse {
  // The list of Reports.
  repeated Report reports = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to delete a Report.
message DeleteReportRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Report"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to get a `ReportConfig` resource.
message GetReportConfigRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/ReportConfig"
    }
  ];
}

// A request to get a list of `ReportConfig` resources.
message ListReportConfigsRequest {
  // Required. Parent value for `ListReportConfigsRequest`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/ReportConfig"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Field to sort by. See https://google.aip.dev/132#ordering for more details.
  string order_by = 5;
}

// Response message for listing report configs.
message ListReportConfigsResponse {
  // A list of report configs.
  repeated ReportConfig report_configs = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for creating a Report.
message CreateReportRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "migrationcenter.googleapis.com/Report"
    }
  ];

  // Required. User specified id for the report. It will become the last
  // component of the report name. The id must be unique within the project,
  // must conform with RFC-1034, is restricted to lower-cased letters, and has a
  // maximum length of 63 characters. The id must match the regular expression:
  // [a-z]([a-z0-9-]{0,61}[a-z0-9])?.
  string report_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The report resource being created.
  Report report = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Collection of frame data.
message Frames {
  // A repeated field of asset data.
  repeated AssetFrame frames_data = 1;
}

// Contains data reported from an inventory source on an asset.
message AssetFrame {
  // The internal data of the frame is a oneof field depending on the type
  // of asset information in the frame.
  oneof FrameData {
    // Asset information specific for virtual machines.
    MachineDetails machine_details = 1;
  }

  // The time the data was reported.
  google.protobuf.Timestamp report_time = 10;

  // Labels as key value pairs.
  map<string, string> labels = 11;

  // Generic asset attributes.
  map<string, string> attributes = 12;

  // Asset performance data samples.
  // Samples that are from more than 40 days ago or after tomorrow are ignored.
  repeated PerformanceSample performance_samples = 13;

  // Optional. Trace token is optionally provided to assist with debugging and
  // traceability.
  string trace_token = 14 [(google.api.field_behavior) = OPTIONAL];
}

// Details of a machine.
message MachineDetails {
  // Machine power state.
  enum PowerState {
    // Power state is unknown.
    POWER_STATE_UNSPECIFIED = 0;

    // The machine is preparing to enter the ACTIVE state. An instance may enter
    // the PENDING state when it launches for the first time, or when it is
    // started after being in the SUSPENDED state.
    PENDING = 1;

    // The machine is active.
    ACTIVE = 2;

    // The machine is being turned off.
    SUSPENDING = 3;

    // The machine is off.
    SUSPENDED = 4;

    // The machine is being deleted from the hosting platform.
    DELETING = 5;

    // The machine is deleted from the hosting platform.
    DELETED = 6;
  }

  // Machine unique identifier.
  string uuid = 1;

  // Machine name.
  string machine_name = 2;

  // Machine creation time.
  google.protobuf.Timestamp create_time = 3;

  // Number of CPU cores in the machine. Must be non-negative.
  int32 core_count = 4;

  // The amount of memory in the machine. Must be non-negative.
  int32 memory_mb = 5;

  // Power state of the machine.
  PowerState power_state = 6;

  // Architecture details (vendor, CPU architecture).
  MachineArchitectureDetails architecture = 7;

  // Guest OS information.
  GuestOsDetails guest_os = 8;

  // Network details.
  MachineNetworkDetails network = 9;

  // Disk details.
  MachineDiskDetails disks = 10;

  // Platform specific information.
  PlatformDetails platform = 11;
}

// Details of the machine architecture.
message MachineArchitectureDetails {
  // Firmware type.
  enum FirmwareType {
    // Unspecified or unknown.
    FIRMWARE_TYPE_UNSPECIFIED = 0;

    // BIOS firmware.
    BIOS = 1;

    // EFI firmware.
    EFI = 2;
  }

  // CPU hyper-threading support.
  enum CpuHyperThreading {
    // Unspecified or unknown.
    CPU_HYPER_THREADING_UNSPECIFIED = 0;

    // Hyper-threading is disabled.
    DISABLED = 1;

    // Hyper-threading is enabled.
    ENABLED = 2;
  }

  // CPU architecture, e.g., "x64-based PC", "x86_64", "i686" etc.
  string cpu_architecture = 1;

  // CPU name, e.g., "Intel Xeon E5-2690", "AMD EPYC 7571" etc.
  string cpu_name = 2;

  // Hardware vendor.
  string vendor = 3;

  // Number of CPU threads allocated to the machine.
  int32 cpu_thread_count = 4;

  // Number of processor sockets allocated to the machine.
  int32 cpu_socket_count = 5;

  // BIOS Details.
  BiosDetails bios = 6;

  // Firmware type.
  FirmwareType firmware_type = 7;

  // CPU hyper-threading support.
  CpuHyperThreading hyperthreading = 8;
}

// Details about the BIOS.
message BiosDetails {
  // BIOS name.
  // This fields is deprecated. Please use the `id` field instead.
  string bios_name = 1 [deprecated = true];

  // BIOS ID.
  string id = 2;

  // BIOS manufacturer.
  string manufacturer = 3;

  // BIOS version.
  string version = 4;

  // BIOS release date.
  google.type.Date release_date = 5;

  // SMBIOS UUID.
  string smbios_uuid = 6;
}

// Details of network adapters and settings.
message MachineNetworkDetails {
  // The primary IP address of the machine.
  string primary_ip_address = 1;

  // The public IP address of the machine.
  string public_ip_address = 2;

  // MAC address of the machine.
  // This property is used to uniqly identify the machine.
  string primary_mac_address = 3;

  // List of network adapters.
  NetworkAdapterList adapters = 4;
}

// List of network adapters.
message NetworkAdapterList {
  // Network adapter entries.
  repeated NetworkAdapterDetails entries = 1;
}

// Details of network adapter.
message NetworkAdapterDetails {
  // Network adapter type (e.g. VMXNET3).
  string adapter_type = 1;

  // MAC address.
  string mac_address = 2;

  // NetworkAddressList
  NetworkAddressList addresses = 3;
}

// List of allocated/assigned network addresses.
message NetworkAddressList {
  // Network address entries.
  repeated NetworkAddress entries = 1;
}

// Details of network address.
message NetworkAddress {
  // Network address assignment.
  enum AddressAssignment {
    // Unknown (default value).
    ADDRESS_ASSIGNMENT_UNSPECIFIED = 0;

    // Staticly assigned IP.
    ADDRESS_ASSIGNMENT_STATIC = 1;

    // Dynamically assigned IP (DHCP).
    ADDRESS_ASSIGNMENT_DHCP = 2;
  }

  // Assigned or configured IP Address.
  string ip_address = 1;

  // Subnet mask.
  string subnet_mask = 2;

  // Broadcast address.
  string bcast = 3;

  // Fully qualified domain name.
  string fqdn = 4;

  // Whether DHCP is used to assign addresses.
  AddressAssignment assignment = 5;
}

// Details of machine disks.
message MachineDiskDetails {
  // Disk total Capacity.
  int64 total_capacity_bytes = 1;

  // Total disk free space.
  int64 total_free_bytes = 2;

  // List of disks.
  DiskEntryList disks = 3;
}

// VM disks.
message DiskEntryList {
  // Disk entries.
  repeated DiskEntry entries = 1;
}

// Single disk entry.
message DiskEntry {
  // Disks interface type.
  enum InterfaceType {
    // Interface type unknown or unspecified.
    INTERFACE_TYPE_UNSPECIFIED = 0;

    // IDE interface type.
    IDE = 1;

    // SATA interface type.
    SATA = 2;

    // SAS interface type.
    SAS = 3;

    // SCSI interface type.
    SCSI = 4;

    // NVME interface type.
    NVME = 5;

    // FC interface type.
    FC = 6;

    // iSCSI interface type.
    ISCSI = 7;
  }

  // Disk capacity.
  int64 capacity_bytes = 1;

  // Disk free space.
  int64 free_bytes = 2;

  // Disk label.
  string disk_label = 3;

  // Disk label type (e.g. BIOS/GPT)
  string disk_label_type = 4;

  // Disks interface type.
  InterfaceType interface_type = 5;

  // Partition layout.
  DiskPartitionList partitions = 6;

  // Disk hardware address (e.g. 0:1 for SCSI).
  string hw_address = 7;

  // Additional details for specific platforms.
  oneof platform_specific {
    // VMware disk details.
    VmwareDiskConfig vmware = 20;
  }
}

// Disk partition list.
message DiskPartitionList {
  // Partition entries.
  repeated DiskPartition entries = 1;
}

// Disk Partition details.
message DiskPartition {
  // Partition type.
  string type = 1;

  // Partition file system.
  string file_system = 2;

  // Mount pount (Linux/Windows) or drive letter (Windows).
  string mount_point = 3;

  // Partition capacity.
  int64 capacity_bytes = 4;

  // Partition free space.
  int64 free_bytes = 5;

  // Partition UUID.
  string uuid = 6;

  // Sub-partitions.
  DiskPartitionList sub_partitions = 7;
}

// VMware disk config details.
message VmwareDiskConfig {
  // VMDK backing type possible values.
  enum BackingType {
    // Default value.
    BACKING_TYPE_UNSPECIFIED = 0;

    // Flat v1.
    BACKING_TYPE_FLAT_V1 = 1;

    // Flat v2.
    BACKING_TYPE_FLAT_V2 = 2;

    // Persistent memory, also known as Non-Volatile Memory (NVM).
    BACKING_TYPE_PMEM = 3;

    // Raw Disk Memory v1.
    BACKING_TYPE_RDM_V1 = 4;

    // Raw Disk Memory v2.
    BACKING_TYPE_RDM_V2 = 5;

    // SEsparse is a snapshot format introduced in vSphere 5.5 for large disks.
    BACKING_TYPE_SESPARSE = 6;

    // SEsparse v1.
    BACKING_TYPE_SESPARSE_V1 = 7;

    // SEsparse v1.
    BACKING_TYPE_SESPARSE_V2 = 8;
  }

  // VMDK disk mode.
  enum VmdkMode {
    // VMDK disk mode unspecified or unknown.
    VMDK_MODE_UNSPECIFIED = 0;

    // Dependent disk mode.
    DEPENDENT = 1;

    // Independent - Persistent disk mode.
    INDEPENDENT_PERSISTENT = 2;

    // Independent - Nonpersistent disk mode.
    INDEPENDENT_NONPERSISTENT = 3;
  }

  // RDM compatibility mode.
  enum RdmCompatibility {
    // Compatibility mode unspecified or unknown.
    RDM_COMPATIBILITY_UNSPECIFIED = 0;

    // Physical compatibility mode.
    PHYSICAL_COMPATIBILITY = 1;

    // Virtual compatibility mode.
    VIRTUAL_COMPATIBILITY = 2;
  }

  // VMDK backing type.
  BackingType backing_type = 1;

  // Is VMDK shared with other VMs.
  bool shared = 2;

  // VMDK disk mode.
  VmdkMode vmdk_mode = 3;

  // RDM compatibility mode.
  RdmCompatibility rdm_compatibility = 4;
}

// Information from Guest-level collections.
message GuestOsDetails {
  // The name of the operating system.
  string os_name = 1;

  // What family the OS belong to, if known.
  OperatingSystemFamily family = 2;

  // The version of the operating system.
  string version = 3;

  // OS and app configuration.
  GuestConfigDetails config = 4;

  // Runtime information.
  GuestRuntimeDetails runtime = 5;
}

// Guest OS config information.
message GuestConfigDetails {
  // Security-Enhanced Linux (SELinux) mode.
  enum SeLinuxMode {
    // SELinux mode unknown or unspecified.
    SE_LINUX_MODE_UNSPECIFIED = 0;

    // SELinux is disabled.
    SE_LINUX_MODE_DISABLED = 1;

    // SELinux permissive mode.
    SE_LINUX_MODE_PERMISSIVE = 2;

    // SELinux enforcing mode.
    SE_LINUX_MODE_ENFORCING = 3;
  }

  // OS issue (typically /etc/issue in Linux).
  string issue = 1;

  // Mount list (Linux fstab).
  FstabEntryList fstab = 2;

  // Hosts file (/etc/hosts).
  HostsEntryList hosts = 3;

  // NFS exports.
  NfsExportList nfs_exports = 4;

  // Security-Enhanced Linux (SELinux) mode.
  SeLinuxMode selinux_mode = 5;
}

// Fstab content.
message FstabEntryList {
  // Fstab entries.
  repeated FstabEntry entries = 1;
}

// Single fstab entry.
message FstabEntry {
  // The block special device or remote filesystem to be mounted.
  string spec = 1;

  // The mount point for the filesystem.
  string file = 2;

  // The type of the filesystem.
  string vfstype = 3;

  // Mount options associated with the filesystem.
  string mntops = 4;

  // Used by dump to determine which filesystems need to be dumped.
  int32 freq = 5;

  // Used by the fsck(8) program to determine the order in which filesystem
  // checks are done at reboot time.
  int32 passno = 6;
}

// Hosts content.
message HostsEntryList {
  // Hosts entries.
  repeated HostsEntry entries = 1;
}

// Single /etc/hosts entry.
message HostsEntry {
  // IP (raw, IPv4/6 agnostic).
  string ip = 1;

  // List of host names / aliases.
  repeated string host_names = 2;
}

// NFS exports.
message NfsExportList {
  // NFS export entries.
  repeated NfsExport entries = 1;
}

// NFS export.
message NfsExport {
  // The directory being exported.
  string export_directory = 1;

  // The hosts or networks to which the export is being shared.
  repeated string hosts = 2;
}

// Guest OS runtime information.
message GuestRuntimeDetails {
  // Running background services.
  RunningServiceList services = 1;

  // Running processes.
  RunningProcessList processes = 2;

  // Runtime network information (connections, ports).
  RuntimeNetworkInfo network = 3;

  // Last time the OS was booted.
  google.protobuf.Timestamp last_boot_time = 4;

  // Domain, e.g. c.stratozone-development.internal.
  string domain = 5;

  // Machine name.
  string machine_name = 6;

  // Installed applications information.
  GuestInstalledApplicationList installed_apps = 7;

  // Open files information.
  OpenFileList open_file_list = 8;
}

// List of running guest OS services.
message RunningServiceList {
  // Running service entries.
  repeated RunningService entries = 1;
}

// Guest OS running service details.
message RunningService {
  // Service state (OS-agnostic).
  enum State {
    // Service state unspecified.
    STATE_UNSPECIFIED = 0;

    // Service is active.
    ACTIVE = 1;

    // Service is paused.
    PAUSED = 2;

    // Service is stopped.
    STOPPED = 3;
  }

  // Service start mode (OS-agnostic).
  enum StartMode {
    // Start mode unspecified.
    START_MODE_UNSPECIFIED = 0;

    // The service is a device driver started by the system loader.
    BOOT = 1;

    // The service is a device driver started by the IOInitSystem function.
    SYSTEM = 2;

    // The service is started by the operating system, at system start-up
    AUTO = 3;

    // The service is started only manually, by a user.
    MANUAL = 4;

    // The service is disabled.
    DISABLED = 5;
  }

  // Service name.
  string service_name = 1;

  // Service state (OS-agnostic).
  State state = 2;

  // Service start mode (OS-agnostic).
  StartMode start_mode = 3;

  // Service binary path.
  string exe_path = 4;

  // Service command line.
  string cmdline = 5;

  // Service pid.
  int64 pid = 6;
}

// List of running guest OS processes.
message RunningProcessList {
  // Running process entries.
  repeated RunningProcess entries = 1;
}

// Guest OS running process details.
message RunningProcess {
  // Process ID.
  int64 pid = 1;

  // Process binary path.
  string exe_path = 2;

  // Process full command line.
  string cmdline = 3;

  // User running the process.
  string user = 4;

  // Process extended attributes.
  map<string, string> attributes = 100;
}

// Runtime networking information.
message RuntimeNetworkInfo {
  // Time of the last network scan.
  google.protobuf.Timestamp scan_time = 1;

  // Network connections.
  NetworkConnectionList connections = 2;
}

// Network connection list.
message NetworkConnectionList {
  // Network connection entries.
  repeated NetworkConnection entries = 1;
}

message NetworkConnection {
  // Network connection state.
  enum State {
    // Connection state is unknown or unspecified.
    STATE_UNSPECIFIED = 0;

    // The connection is being opened.
    OPENING = 1;

    // The connection is open.
    OPEN = 2;

    // Listening for incoming connections.
    LISTEN = 3;

    // The connection is being closed.
    CLOSING = 4;

    // The connection is closed.
    CLOSED = 5;
  }

  // Connection protocol (e.g. TCP/UDP).
  string protocol = 1;

  // Local IP address.
  string local_ip_address = 2;

  // Local port.
  int32 local_port = 3;

  // Remote IP address.
  string remote_ip_address = 4;

  // Remote port.
  int32 remote_port = 5;

  // Network connection state.
  State state = 6;

  // Process ID.
  int64 pid = 7;

  // Process or service name.
  string process_name = 8;
}

// Guest installed application list.
message GuestInstalledApplicationList {
  // Application entries.
  repeated GuestInstalledApplication entries = 1;
}

// Guest installed application information.
message GuestInstalledApplication {
  // Installed application name.
  string application_name = 1;

  // Installed application vendor.
  string vendor = 2;

  // The time when the application was installed.
  google.protobuf.Timestamp install_time = 3;

  // Source path.
  string path = 4;

  // Installed application version.
  string version = 5;
}

// Open file list.
message OpenFileList {
  // Open file details entries.
  repeated OpenFileDetails entries = 1;
}

// Open file Information.
message OpenFileDetails {
  // Opened file command.
  string command = 1;

  // Opened file user.
  string user = 2;

  // Opened file file type.
  string file_type = 3;

  // Opened file file path.
  string file_path = 4;
}

// Information about the platform.
message PlatformDetails {
  oneof vendor_details {
    // VMware specific details.
    VmwarePlatformDetails vmware_details = 1;

    // AWS EC2 specific details.
    AwsEc2PlatformDetails aws_ec2_details = 2;

    // Azure VM specific details.
    AzureVmPlatformDetails azure_vm_details = 3;

    // Generic platform details.
    GenericPlatformDetails generic_details = 4;

    // Physical machines platform details.
    PhysicalPlatformDetails physical_details = 5;
  }
}

// VMware specific details.
message VmwarePlatformDetails {
  // vCenter version.
  string vcenter_version = 1;

  // ESX version.
  string esx_version = 2;

  // VMware os enum -
  // https://vdc-repo.vmware.com/vmwb-repository/dcr-public/da47f910-60ac-438b-8b9b-6122f4d14524/16b7274a-bf8b-4b4c-a05e-746f2aa93c8c/doc/vim.vm.GuestOsDescriptor.GuestOsIdentifier.html.
  string osid = 3;

  // Folder name in vCenter where asset resides.
  string vcenter_folder = 4;

  // vCenter URI used in collection.
  string vcenter_uri = 5;

  // vCenter VM ID.
  string vcenter_vm_id = 6;
}

// AWS EC2 specific details.
message AwsEc2PlatformDetails {
  // AWS platform's machine type label.
  string machine_type_label = 1;

  // The location of the machine in the AWS format.
  string location = 2;
}

// Azure VM specific details.
message AzureVmPlatformDetails {
  // Azure platform's machine type label.
  string machine_type_label = 1;

  // The location of the machine in the Azure format.
  string location = 2;

  // Azure platform's provisioning state.
  string provisioning_state = 3;
}

// Generic platform details.
message GenericPlatformDetails {
  // Free text representation of the machine location.
  // The format of this field should not be relied on. Different VMs in the same
  // location may have different string values for this field.
  string location = 1;
}

// Platform specific details for Physical Machines.
message PhysicalPlatformDetails {
  // Free text representation of the machine location.
  // The format of this field should not be relied on. Different machines in the
  // same location may have different string values for this field.
  string location = 1;
}

// Memory usage sample.
message MemoryUsageSample {
  // Percentage of system memory utilized. Must be in the interval [0, 100].
  float utilized_percentage = 1;
}

// CPU usage sample.
message CpuUsageSample {
  // Percentage of total CPU capacity utilized. Must be in the interval [0,
  // 100]. On most systems can be calculated using 100 - idle percentage.
  float utilized_percentage = 1;
}

// Network usage sample. Values are across all network interfaces.
message NetworkUsageSample {
  // Average network ingress in B/s sampled over a short window.
  // Must be non-negative.
  float average_ingress_bps = 1;

  // Average network egress in B/s sampled over a short window.
  // Must be non-negative.
  float average_egress_bps = 2;
}

// Disk usage sample. Values are across all disks.
message DiskUsageSample {
  // Average IOPS sampled over a short window. Must be non-negative.
  float average_iops = 1;
}

// Performance data sample.
message PerformanceSample {
  // Time the sample was collected.
  // If omitted, the frame report time will be used.
  google.protobuf.Timestamp sample_time = 1;

  // Memory usage sample.
  MemoryUsageSample memory = 2;

  // CPU usage sample.
  CpuUsageSample cpu = 3;

  // Network usage sample.
  NetworkUsageSample network = 4;

  // Disk usage sample.
  DiskUsageSample disk = 5;
}

// Performance data for an asset.
message AssetPerformanceData {
  // Daily resource usage aggregations.
  // Contains all of the data available for an asset, up to the last 420 days.
  // Aggregations are sorted from oldest to most recent.
  repeated DailyResourceUsageAggregation daily_resource_usage_aggregations = 1;
}

// Usage data aggregation for a single day.
message DailyResourceUsageAggregation {
  // Statistical aggregation of samples for a single resource usage.
  message Stats {
    // Average usage value.
    float average = 1;

    // Median usage value.
    float median = 2;

    // 95th percentile usage value.
    float nintey_fifth_percentile = 3;

    // Peak usage value.
    float peak = 4;
  }

  // Statistical aggregation of CPU usage.
  message CPU {
    // CPU utilization percentage.
    Stats utilization_percentage = 1;
  }

  // Statistical aggregation of memory usage.
  message Memory {
    // Memory utilization percentage.
    Stats utilization_percentage = 1;
  }

  // Statistical aggregation of network usage.
  message Network {
    // Network ingress in B/s.
    Stats ingress_bps = 1;

    // Network egress in B/s.
    Stats egress_bps = 2;
  }

  // Statistical aggregation of disk usage.
  message Disk {
    // Disk I/O operations per second.
    Stats iops = 1;
  }

  // Aggregation date. Day boundaries are at midnight UTC.
  google.type.Date date = 1;

  // CPU usage.
  CPU cpu = 2;

  // Memory usage.
  Memory memory = 3;

  // Network usage.
  Network network = 4;

  // Disk usage.
  Disk disk = 5;
}

// Message containing insights list.
message InsightList {
  // Output only. Insights of the list.
  repeated Insight insights = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update timestamp.
  google.protobuf.Timestamp update_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An insight about an asset.
message Insight {
  oneof insight {
    // Output only. An insight about potential migrations for an asset.
    MigrationInsight migration_insight = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A generic insight about an asset
    GenericInsight generic_insight = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// A generic insight about an asset.
message GenericInsight {
  // Output only. Represents a globally unique message id for
  // this insight, can be used for localization purposes, in case message_code
  // is not yet known by the client use default_message instead.
  int64 message_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. In case message_code is not yet known by the client
  // default_message will be the message to be used instead.
  string default_message = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the insight, each entry can be a
  // logical entry and must make sense if it is displayed with line breaks
  // between each entry. Text can contain md style links.
  repeated string additional_information = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An insight about potential migrations for an asset.
message MigrationInsight {
  // Output only. Description of how well the asset this insight is associated
  // with fits the proposed migration.
  FitDescriptor fit = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A target for the migration.
  oneof migration_target {
    // Output only. A Google Compute Engine target.
    ComputeEngineMigrationTarget compute_engine_target = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// Compute engine migration target.
message ComputeEngineMigrationTarget {
  // Description of the suggested shape for the migration target.
  ComputeEngineShapeDescriptor shape = 1;
}

// Compute Engine target shape descriptor.
message ComputeEngineShapeDescriptor {
  // Memory in mebibytes.
  int32 memory_mb = 1;

  // Number of physical cores.
  int32 physical_core_count = 2;

  // Number of logical cores.
  int32 logical_core_count = 3;

  // Compute Engine machine series.
  string series = 4;

  // Compute Engine machine type.
  string machine_type = 5;

  // Compute Engine storage. Never empty.
  repeated ComputeStorageDescriptor storage = 6;
}

// Compute Engine storage option descriptor.
message ComputeStorageDescriptor {
  // Disk type backing the storage.
  PersistentDiskType type = 1;

  // Disk size in GiB.
  int32 size_gb = 2;
}

// Describes the fit level of an asset for migration to a specific target.
message FitDescriptor {
  // Fit level.
  enum FitLevel {
    // Not enough information.
    FIT_LEVEL_UNSPECIFIED = 0;

    // Fit.
    FIT = 1;

    // No Fit.
    NO_FIT = 2;

    // Fit with effort.
    REQUIRES_EFFORT = 3;
  }

  // Fit level.
  FitLevel fit_level = 1;
}

// Message describing an aggregation. The message includes the aggregation type,
// parameters, and the field on which to perform the aggregation.
message Aggregation {
  // Object count.
  message Count {}

  // Sum of field values.
  message Sum {}

  // Histogram of bucketed assets counts by field value.
  message Histogram {
    // Lower bounds of buckets.
    // The response will contain `n+1` buckets for `n` bounds.
    // The first bucket will count all assets for which the field value is
    // smaller than the first bound.
    // Subsequent buckets will count assets for which the field value is
    // greater or equal to a lower bound and smaller than the next one.
    // The last bucket will count assets for which the field value is greater or
    // equal to the final lower bound.
    // You can define up to 20 lower bounds.
    repeated double lower_bounds = 1;
  }

  // Frequency distribution of all field values.
  message Frequency {}

  // The name of the field on which to aggregate.
  string field = 1;

  oneof aggregation_function {
    // Count the number of matching objects.
    Count count = 2;

    // Sum over a numeric field.
    Sum sum = 3;

    // Creates a bucketed histogram of field values.
    Histogram histogram = 4;

    // Creates a frequency distribution of all field values.
    Frequency frequency = 5;
  }
}

// Message describing a result of an aggregation.
message AggregationResult {
  // The result of a count aggregation.
  message Count {
    int64 value = 1;
  }

  // The result of a sum aggregation.
  message Sum {
    double value = 1;
  }

  // The result of a bucketed histogram aggregation.
  message Histogram {
    // A histogram bucket with a lower and upper bound, and a count of items
    // with a field value between those bounds.
    // The lower bound is inclusive and the upper bound is exclusive.
    // Lower bound may be -infinity and upper bound may be infinity.
    message Bucket {
      // Lower bound - inclusive.
      double lower_bound = 1;

      // Upper bound - exclusive.
      double upper_bound = 2;

      // Count of items in the bucket.
      int64 count = 3;
    }

    // Buckets in the histogram.
    // There will be `n+1` buckets matching `n` lower bounds in the request.
    // The first bucket will be from -infinity to the first bound.
    // Subsequent buckets will be between one bound and the next.
    // The final bucket will be from the final bound to infinity.
    repeated Bucket buckets = 1;
  }

  // The result of a frequency distribution aggregation.
  message Frequency {
    map<string, int64> values = 1;
  }

  string field = 1;

  oneof result {
    Count count = 2;

    Sum sum = 3;

    Histogram histogram = 4;

    Frequency frequency = 5;
  }
}

// A resource that aggregates the validation errors found in an import job file.
message FileValidationReport {
  // The name of the file.
  string file_name = 1;

  // Partial list of rows that encountered validation error.
  repeated ImportRowError row_errors = 2;

  // Flag indicating that processing was aborted due to maximum number of
  // errors.
  bool partial_report = 3;

  // List of file level errors.
  repeated ImportError file_errors = 4;
}

// A resource that aggregates errors across import job files.
message ValidationReport {
  // List of errors found in files.
  repeated FileValidationReport file_validations = 1;

  // List of job level errors.
  repeated ImportError job_errors = 2;
}

// A resource that reports result of the import job execution.
message ExecutionReport {
  // Total number of asset frames reported for the import job.
  int32 frames_reported = 1;

  // Validation errors encountered during the execution of the import job.
  ValidationReport execution_errors = 2;

  // Output only. Total number of rows in the import job.
  int32 total_rows_count = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A resource that reports the errors encountered while processing an
// import job.
message ImportError {
  // Enumerate possible error severity.
  enum Severity {
    SEVERITY_UNSPECIFIED = 0;

    ERROR = 1;

    WARNING = 2;

    INFO = 3;
  }

  // The error information.
  string error_details = 1;

  // The severity of the error.
  Severity severity = 2;
}

// A resource that reports the import job errors at row level.
message ImportRowError {
  // The row number where the error was detected.
  int32 row_number = 1 [deprecated = true];

  // The name of the VM in the row.
  string vm_name = 2;

  // The VM UUID.
  string vm_uuid = 3;

  // The list of errors detected in the row.
  repeated ImportError errors = 4;
}

// A resource that contains a URI to which a data file can be uploaded.
message UploadFileInfo {
  // Output only. Upload URI for the file.
  string signed_uri = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The headers that were used to sign the URI.
  map<string, string> headers = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Expiration time of the upload URI.
  google.protobuf.Timestamp uri_expiration_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Lists the asset IDs of all assets.
message AssetList {
  // Required. A list of asset IDs
  repeated string asset_ids = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "migrationcenter.googleapis.com/Asset"
    }
  ];
}

// A resource that contains a single violation of a reported `AssetFrame`
// resource.
message FrameViolationEntry {
  // The field of the original frame where the violation occurred.
  string field = 1;

  // A message describing the violation.
  string violation = 2;
}

// VirtualMachinePreferences enables you to create sets of assumptions, for
// example, a geographical location and pricing track, for your migrated virtual
// machines. The set of preferences influence recommendations for migrating
// virtual machine assets.
message VirtualMachinePreferences {
  // Target product for assets using this preference set.
  // Specify either target product or business goal, but
  // not both.
  ComputeMigrationTargetProduct target_product = 2;

  // Region preferences for assets using this preference set.
  // If you are unsure which value to set, the migration service API region is
  // often a good value to start with.
  RegionPreferences region_preferences = 3;

  // Commitment plan to consider when calculating costs for virtual machine
  // insights and recommendations.
  // If you are unsure which value to set, a 3 year commitment plan is often a
  // good value to start with.
  CommitmentPlan commitment_plan = 4;

  // Sizing optimization strategy specifies the preferred strategy used when
  // extrapolating usage data to calculate insights and recommendations for a
  // virtual machine.
  // If you are unsure which value to set, a moderate sizing optimization
  // strategy is often a good value to start with.
  SizingOptimizationStrategy sizing_optimization_strategy = 5;

  // Compute Engine preferences concern insights and recommendations for Compute
  // Engine target.
  ComputeEnginePreferences compute_engine_preferences = 6;

  // Preferences concerning insights and recommendations for
  // Google Cloud VMware Engine.
  VmwareEnginePreferences vmware_engine_preferences = 7;

  // Preferences concerning Sole Tenant nodes and virtual machines.
  SoleTenancyPreferences sole_tenancy_preferences = 8;
}

// The user preferences relating to Compute Engine target platform.
message ComputeEnginePreferences {
  // Preferences concerning the machine types to consider on Compute Engine.
  MachinePreferences machine_preferences = 2;

  // License type to consider when calculating costs for virtual machine
  // insights and recommendations. If unspecified, costs are calculated
  // based on the default licensing plan.
  LicenseType license_type = 3;
}

// The type of machines to consider when calculating virtual machine migration
// insights and recommendations.
// Not all machine types are available in all zones and regions.
message MachinePreferences {
  // Compute Engine machine series to consider for insights and recommendations.
  // If empty, no restriction is applied on the machine series.
  repeated MachineSeries allowed_machine_series = 1;
}

// A Compute Engine machine series.
message MachineSeries {
  // Code to identify a Compute Engine machine series. Consult
  // https://cloud.google.com/compute/docs/machine-resource#machine_type_comparison
  // for more details on the available series.
  string code = 1;
}

// The user preferences relating to Google Cloud VMware Engine target platform.
message VmwareEnginePreferences {
  // Type of committed use discount.
  enum CommitmentPlan {
    // Unspecified commitment plan.
    COMMITMENT_PLAN_UNSPECIFIED = 0;

    // No commitment plan (on-demand usage).
    ON_DEMAND = 1;

    // 1 year commitment (monthly payments).
    COMMITMENT_1_YEAR_MONTHLY_PAYMENTS = 2;

    // 3 year commitment (monthly payments).
    COMMITMENT_3_YEAR_MONTHLY_PAYMENTS = 3;

    // 1 year commitment (upfront payment).
    COMMITMENT_1_YEAR_UPFRONT_PAYMENT = 4;

    // 3 years commitment (upfront payment).
    COMMITMENT_3_YEAR_UPFRONT_PAYMENT = 5;
  }

  // CPU overcommit ratio.
  // Acceptable values are between 1.0 and 8.0, with 0.1 increment.
  double cpu_overcommit_ratio = 1;

  // Memory overcommit ratio.
  // Acceptable values are 1.0, 1.25, 1.5, 1.75 and 2.0.
  double memory_overcommit_ratio = 2;

  // The Deduplication and Compression ratio is based on the logical (Used
  // Before) space required to store data before applying deduplication and
  // compression, in relation to the physical (Used After) space required after
  // applying deduplication and compression. Specifically, the ratio is the Used
  // Before space divided by the Used After space. For example, if the Used
  // Before space is 3 GB, but the physical Used After space is 1 GB, the
  // deduplication and compression ratio is 3x. Acceptable values are
  // between 1.0 and 4.0.
  double storage_deduplication_compression_ratio = 3;

  // Commitment plan to consider when calculating costs for virtual machine
  // insights and recommendations.
  // If you are unsure which value to set, a 3 year commitment plan is often a
  // good value to start with.
  CommitmentPlan commitment_plan = 4;
}

// Preferences concerning Sole Tenancy nodes and VMs.
message SoleTenancyPreferences {
  // Sole Tenancy nodes maintenance policy.
  enum HostMaintenancePolicy {
    // Unspecified host maintenance policy.
    HOST_MAINTENANCE_POLICY_UNSPECIFIED = 0;

    // Default host maintenance policy.
    HOST_MAINTENANCE_POLICY_DEFAULT = 1;

    // Restart in place host maintenance policy.
    HOST_MAINTENANCE_POLICY_RESTART_IN_PLACE = 2;

    // Migrate within node group host maintenance policy.
    HOST_MAINTENANCE_POLICY_MIGRATE_WITHIN_NODE_GROUP = 3;
  }

  // Type of committed use discount.
  enum CommitmentPlan {
    // Unspecified commitment plan.
    COMMITMENT_PLAN_UNSPECIFIED = 0;

    // No commitment plan (on-demand usage).
    ON_DEMAND = 1;

    // 1 year commitment.
    COMMITMENT_1_YEAR = 2;

    // 3 years commitment.
    COMMITMENT_3_YEAR = 3;
  }

  // CPU overcommit ratio.
  // Acceptable values are between 1.0 and 2.0 inclusive.
  double cpu_overcommit_ratio = 1;

  // Sole Tenancy nodes maintenance policy.
  HostMaintenancePolicy host_maintenance_policy = 2;

  // Commitment plan to consider when calculating costs for virtual machine
  // insights and recommendations.
  // If you are unsure which value to set, a 3 year commitment plan is often a
  // good value to start with.
  CommitmentPlan commitment_plan = 3;

  // A list of sole tenant node types.
  // An empty list means that all possible node types will be considered.
  repeated SoleTenantNodeType node_types = 4;
}

// A Sole Tenant node type.
message SoleTenantNodeType {
  // Name of the Sole Tenant node. Consult
  // https://cloud.google.com/compute/docs/nodes/sole-tenant-nodes
  string node_name = 1;
}

// The user preferences relating to target regions.
message RegionPreferences {
  // A list of preferred regions,
  // ordered by the most preferred region first.
  // Set only valid Google Cloud region names.
  // See https://cloud.google.com/compute/docs/regions-zones
  // for available regions.
  repeated string preferred_regions = 1;
}

// Describes the Migration Center settings related to the project.
message Settings {
  option (google.api.resource) = {
    type: "migrationcenter.googleapis.com/Settings"
    pattern: "projects/{project}/locations/{location}/settings"
  };

  // Output only. The name of the resource.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The preference set used by default for a project.
  string preference_set = 2 [(google.api.resource_reference) = {
    type: "migrationcenter.googleapis.com/PreferenceSet"
  }];
}

// Describes the Summary view of a Report, which contains aggregated values
// for all the groups and preference sets included in this Report.
message ReportSummary {
  // Describes a collection of data points rendered as a Chart.
  message ChartData {
    // Describes a single data point in the Chart.
    message DataPoint {
      // The X-axis label for this data point.
      string label = 1;

      // The Y-axis value for this data point.
      double value = 2;
    }

    // Each data point in the chart is represented as a name-value pair
    // with the name being the x-axis label, and the value being the y-axis
    // value.
    repeated DataPoint data_points = 1;
  }

  // Utilization Chart is a specific type of visualization which displays
  // a metric classified into "Used" and "Free" buckets.
  message UtilizationChartData {
    // Aggregate value which falls into the "Used" bucket.
    int64 used = 1;

    // Aggregate value which falls into the "Free" bucket.
    int64 free = 2;
  }

  // A Histogram Chart shows a distribution of values into buckets, showing
  // a count of values which fall into a bucket.
  message HistogramChartData {
    // A histogram bucket with a lower and upper bound, and a count of items
    // with a field value between those bounds.
    // The lower bound is inclusive and the upper bound is exclusive.
    // Lower bound may be -infinity and upper bound may be infinity.
    message Bucket {
      // Lower bound - inclusive.
      int64 lower_bound = 1;

      // Upper bound - exclusive.
      int64 upper_bound = 2;

      // Count of items in the bucket.
      int64 count = 3;
    }

    // Buckets in the histogram.
    // There will be `n+1` buckets matching `n` lower bounds in the request.
    // The first bucket will be from -infinity to the first bound.
    // Subsequent buckets will be between one bound and the next.
    // The final bucket will be from the final bound to infinity.
    repeated Bucket buckets = 1;
  }

  // Aggregate statistics for a collection of assets.
  message AssetAggregateStats {
    // Sum of the memory in bytes of all the assets in this collection.
    int64 total_memory_bytes = 1;

    // Sum of persistent storage in bytes of all the assets in this collection.
    int64 total_storage_bytes = 2;

    // Sum of the CPU core count of all the assets in this collection.
    int64 total_cores = 3;

    // Count of the number of unique assets in this collection.
    int64 total_assets = 4;

    // Total memory split into Used/Free buckets.
    UtilizationChartData memory_utilization_chart = 5;

    // Total memory split into Used/Free buckets.
    UtilizationChartData storage_utilization_chart = 6;

    // Count of assets grouped by Operating System families.
    ChartData operating_system = 7;

    // Histogram showing a distribution of CPU core counts.
    HistogramChartData core_count_histogram = 8;

    // Histogram showing a distribution of memory sizes.
    HistogramChartData memory_bytes_histogram = 9;

    // Histogram showing a distribution of memory sizes.
    HistogramChartData storage_bytes_histogram = 10;
  }

  // Represents a data point tracking the count of assets allocated for a
  // specific Machine Series.
  message MachineSeriesAllocation {
    // The Machine Series (e.g. "E2", "N2")
    MachineSeries machine_series = 1;

    // Count of assets allocated to this machine series.
    int64 allocated_asset_count = 2;
  }

  // A set of findings that applies to assets destined for Compute Engine.
  message ComputeEngineFinding {
    // Set of regions in which the assets were allocated.
    repeated string allocated_regions = 1;

    // Count of assets which were allocated.
    int64 allocated_asset_count = 2;

    // Distribution of assets based on the Machine Series.
    repeated MachineSeriesAllocation machine_series_allocations = 3;

    // Set of disk types allocated to assets.
    repeated PersistentDiskType allocated_disk_types = 4;
  }

  // A set of findings that applies to assets destined for VMWare Engine.
  message VmwareEngineFinding {
    // Set of regions in which the assets were allocated
    repeated string allocated_regions = 1;

    // Count of assets which are allocated
    int64 allocated_asset_count = 2;

    // Set of per-nodetype allocation records
    repeated VmwareNodeAllocation node_allocations = 3;
  }

  // Represents assets allocated to a specific VMWare Node type.
  message VmwareNodeAllocation {
    // VMWare node type, e.g. "ve1-standard-72"
    VmwareNode vmware_node = 1;

    // Count of this node type to be provisioned
    int64 node_count = 2;

    // Count of assets allocated to these nodes
    int64 allocated_asset_count = 3;
  }

  // A VMWare Engine Node
  message VmwareNode {
    // Code to identify VMware Engine node series, e.g. "ve1-standard-72". Based
    // on the displayName of
    // cloud.google.com/vmware-engine/docs/reference/rest/v1/projects.locations.nodeTypes
    string code = 1;
  }

  // A set of findings that applies to assets destined for Sole-Tenant nodes.
  message SoleTenantFinding {
    // Set of regions in which the assets are allocated
    repeated string allocated_regions = 1;

    // Count of assets which are allocated
    int64 allocated_asset_count = 2;

    // Set of per-nodetype allocation records
    repeated SoleTenantNodeAllocation node_allocations = 3;
  }

  // Represents the assets allocated to a specific Sole-Tenant node type.
  message SoleTenantNodeAllocation {
    // Sole Tenant node type, e.g. "m3-node-128-3904"
    SoleTenantNodeType node = 1;

    // Count of this node type to be provisioned
    int64 node_count = 2;

    // Count of assets allocated to these nodes
    int64 allocated_asset_count = 3;
  }

  // Summary Findings for a specific Group/PreferenceSet combination.
  message GroupPreferenceSetFinding {
    // Display Name of the Preference Set
    string display_name = 1;

    // Description for the Preference Set.
    string description = 2;

    // A set of preferences that applies to all machines in the context.
    VirtualMachinePreferences machine_preferences = 3;

    // Total monthly cost for this preference set.
    google.type.Money monthly_cost_total = 4;

    // Compute monthly cost for this preference set.
    google.type.Money monthly_cost_compute = 5;

    // Licensing monthly cost for this preference set.
    google.type.Money monthly_cost_os_license = 6;

    // Network Egress monthly cost for this preference set.
    google.type.Money monthly_cost_network_egress = 7;

    // Storage monthly cost for this preference set.
    google.type.Money monthly_cost_storage = 8;

    // Miscellaneous monthly cost for this preference set.
    google.type.Money monthly_cost_other = 9;

    // A set of findings that applies to Compute Engine machines in the input.
    ComputeEngineFinding compute_engine_finding = 10;

    // A set of findings that applies to VMWare machines in the input.
    VmwareEngineFinding vmware_engine_finding = 11;

    // A set of findings that applies to Sole-Tenant machines in the input.
    SoleTenantFinding sole_tenant_finding = 12;
  }

  // Summary Findings for a specific Group.
  message GroupFinding {
    // Display Name for the Group.
    string display_name = 1;

    // Description for the Group.
    string description = 2;

    // Summary statistics for all the assets in this group.
    AssetAggregateStats asset_aggregate_stats = 3;

    // This field is deprecated, do not rely on it having a value.
    int64 overlapping_asset_count = 4 [deprecated = true];

    // Findings for each of the PreferenceSets for this group.
    repeated GroupPreferenceSetFinding preference_set_findings = 5;
  }

  // Aggregate statistics for all the assets across all the groups.
  AssetAggregateStats all_assets_stats = 1;

  // Findings for each Group included in this report.
  repeated GroupFinding group_findings = 2;
}

// Specifies the types of asset views that provide complete or partial details
// of an asset.
enum AssetView {
  // The asset view is not specified. The API displays the basic view by
  // default.
  ASSET_VIEW_UNSPECIFIED = 0;

  // The asset view includes only basic metadata of the asset.
  ASSET_VIEW_BASIC = 1;

  // The asset view includes all the metadata of an asset and performance data.
  ASSET_VIEW_FULL = 2;
}

// Known categories of operating systems.
enum OperatingSystemFamily {
  OS_FAMILY_UNKNOWN = 0;

  // Microsoft Windows Server and Desktop.
  OS_FAMILY_WINDOWS = 1;

  // Various Linux flavors.
  OS_FAMILY_LINUX = 2;

  // Non-Linux Unix flavors.
  OS_FAMILY_UNIX = 3;
}

// Specifies the data formats supported by Migration Center.
enum ImportJobFormat {
  // Default value.
  IMPORT_JOB_FORMAT_UNSPECIFIED = 0;

  // RVTools format (XLSX).
  IMPORT_JOB_FORMAT_RVTOOLS_XLSX = 1;

  // RVTools format (CSV).
  IMPORT_JOB_FORMAT_RVTOOLS_CSV = 2;

  // CSV format exported from AWS using the
  // [AWS collection
  // script][https://github.com/GoogleCloudPlatform/aws-to-stratozone-export].
  IMPORT_JOB_FORMAT_EXPORTED_AWS_CSV = 4;

  // CSV format exported from Azure using the
  // [Azure collection
  // script][https://github.com/GoogleCloudPlatform/azure-to-stratozone-export].
  IMPORT_JOB_FORMAT_EXPORTED_AZURE_CSV = 5;

  // CSV format created manually and following the StratoZone format. For more
  // information, see [Manually create and upload data
  // tables][https://cloud.google.com/migrate/stratozone/docs/import-data-portal].
  IMPORT_JOB_FORMAT_STRATOZONE_CSV = 6;
}

// Specifies the types of import job views that provide complete or partial
// details of an import job.
enum ImportJobView {
  // The import job view is not specified. The API displays the basic view by
  // default.
  IMPORT_JOB_VIEW_UNSPECIFIED = 0;

  // The import job view includes basic metadata of an import job.
  // This view does not include payload information.
  IMPORT_JOB_VIEW_BASIC = 1;

  // The import job view includes all metadata of an import job.
  IMPORT_JOB_VIEW_FULL = 2;
}

// ErrorFrameView can be specified in ErrorFrames List and Get requests to
// control the level of details that is returned for the original frame.
enum ErrorFrameView {
  // Value is unset. The system will fallback to the default value.
  ERROR_FRAME_VIEW_UNSPECIFIED = 0;

  // Include basic frame data, but not the full contents.
  ERROR_FRAME_VIEW_BASIC = 1;

  // Include everything.
  ERROR_FRAME_VIEW_FULL = 2;
}

// The persistent disk (PD) types of Compute Engine virtual machines.
enum PersistentDiskType {
  // Unspecified (default value).
  // Selecting this value allows the system to use any disk type according
  // to reported usage. This a good value to start with.
  PERSISTENT_DISK_TYPE_UNSPECIFIED = 0;

  // Standard HDD Persistent Disk.
  PERSISTENT_DISK_TYPE_STANDARD = 1;

  // Balanced Persistent Disk.
  PERSISTENT_DISK_TYPE_BALANCED = 2;

  // SSD Persistent Disk.
  PERSISTENT_DISK_TYPE_SSD = 3;
}

// The License type for premium images (RHEL, RHEL for SAP, SLES, SLES for SAP,
// Windows Server).
enum LicenseType {
  // Unspecified (default value).
  LICENSE_TYPE_UNSPECIFIED = 0;

  // Default Google Cloud licensing plan. Licensing is charged per usage.
  // This a good value to start with.
  LICENSE_TYPE_DEFAULT = 1;

  // Bring-your-own-license (BYOL) plan. User provides the OS license.
  LICENSE_TYPE_BRING_YOUR_OWN_LICENSE = 2;
}

// The sizing optimization strategy preferences of a virtual machine. This
// strategy, in addition to actual usage data of the virtual machine, can help
// determine the recommended shape on the target platform.
enum SizingOptimizationStrategy {
  // Unspecified (default value).
  SIZING_OPTIMIZATION_STRATEGY_UNSPECIFIED = 0;

  // No optimization applied. Virtual machine sizing matches as closely as
  // possible the machine shape on the source site, not considering any actual
  // performance data.
  SIZING_OPTIMIZATION_STRATEGY_SAME_AS_SOURCE = 1;

  // Virtual machine sizing will match the reported usage and shape, with some
  // slack. This a good value to start with.
  SIZING_OPTIMIZATION_STRATEGY_MODERATE = 2;

  // Virtual machine sizing will match the reported usage, with little slack.
  // Using this option can help reduce costs.
  SIZING_OPTIMIZATION_STRATEGY_AGGRESSIVE = 3;
}

// The plan of commitments for VM resource-based committed use discount (CUD).
enum CommitmentPlan {
  // Unspecified commitment plan.
  COMMITMENT_PLAN_UNSPECIFIED = 0;

  // No commitment plan.
  COMMITMENT_PLAN_NONE = 1;

  // 1 year commitment.
  COMMITMENT_PLAN_ONE_YEAR = 2;

  // 3 years commitment.
  COMMITMENT_PLAN_THREE_YEARS = 3;
}

// The preference for a specific Google Cloud product platform.
enum ComputeMigrationTargetProduct {
  // Unspecified (default value).
  COMPUTE_MIGRATION_TARGET_PRODUCT_UNSPECIFIED = 0;

  // Prefer to migrate to Google Cloud Compute Engine.
  COMPUTE_MIGRATION_TARGET_PRODUCT_COMPUTE_ENGINE = 1;

  // Prefer to migrate to Google Cloud VMware Engine.
  COMPUTE_MIGRATION_TARGET_PRODUCT_VMWARE_ENGINE = 2;

  // Prefer to migrate to Google Cloud Sole Tenant Nodes.
  COMPUTE_MIGRATION_TARGET_PRODUCT_SOLE_TENANCY = 3;
}

// Specifies the types of views that provide complete or partial details
// of a Report.
enum ReportView {
  // The report view is not specified. The API displays the basic view by
  // default.
  REPORT_VIEW_UNSPECIFIED = 0;

  // The report view includes only basic metadata of the Report. Useful for
  // list views.
  REPORT_VIEW_BASIC = 1;

  // The report view includes all the metadata of the Report. Useful for
  // preview.
  REPORT_VIEW_FULL = 2;

  // The report view includes the standard metadata of an report. Useful for
  // detail view.
  REPORT_VIEW_STANDARD = 3;
}
