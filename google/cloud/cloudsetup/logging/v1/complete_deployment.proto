// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudsetup.logging.v1;

import "google/api/resource.proto";
import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/cloudsetup/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "CompleteDeploymentProto";
option java_package = "com.google.cloud.cloudsetup.logging.v1";
option csharp_namespace = "Google.Cloud.CloudSetup.Logging.V1";
option php_namespace = "Google\\Cloud\\CloudSetup\\Logging\\V1";
option ruby_package = "Google::Cloud::CloudSetup::Logging::V1";

// JSON payload for the Cloud Logging event:
// `organizations/[organizationId]/logs/cloudsetup.googleapis.com%2Fcomplete_deployment`.
// This event gets emitted upon completion of a config deployment as part of the
// Cloud Setup Checklist.
//
// The deployment can fail even if it returns a
// `config.googleapis.com/Deployment`. The state of that message will be
// `FAILED`. Hence, if there is a `value` present, the `state` can still be,
// `FAILED`. The message for the error or failure will be on the `error` or the
// `value` if the Operation results in an error or if the `state` of the
// Deployment is `FAILED`, respectively.
message CompleteDeploymentEvent {
  // State of the completed deployment.
  enum State {
    // The zero value. It is applied when `state` is unset. Do not use.
    STATE_UNSPECIFIED = 0;

    // Indicates that a Deployment value was present when the config deployment
    // finished and the State was anything other than FAILED.
    SUCCEEDED = 1;

    // Indicates that the Operation result was an error or the Deployment
    // `state` was FAILED.
    FAILED = 2;
  }

  oneof result {
    // Result of the Deployment recorded upon completion.
    CompleteDeploymentResult value = 1;

    // Result of the Deployment if the `Operation` results in an error.
    google.rpc.Status error = 2;
  }

  // The `state` of this deployment completion event.
  State state = 3;

  // Flag to indicate if deployment is preview only.
  bool preview_only = 4;
}

// This message is used when the CompleteDeploymentEvent has a value.
message CompleteDeploymentResult {
  // This is the Deployment that completed.
  // Format is projects/{project}/locations/{location}/deployments/{name}.
  string deployment = 1 [(google.api.resource_reference) = {
    type: "config.googleapis.com/Deployment"
  }];

  // This is the Preview that completed.
  // Format is projects/{project}/locations/{location}/previews/{preview}
  string preview = 3 [
    (google.api.resource_reference) = { type: "config.googleapis.com/Preview" }
  ];

  // The message that is returned when a deployment completes. This **can** be
  // an error message if the `Deployment` `state` is `FAILED`.
  string message = 2;
}
