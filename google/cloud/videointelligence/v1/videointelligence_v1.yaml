type: google.api.Service
config_version: 3
name: videointelligence.googleapis.com
title: Cloud Video Intelligence API

apis:
- name: google.cloud.videointelligence.v1.VideoIntelligenceService

types:
- name: google.cloud.videointelligence.v1.AnnotateVideoProgress
- name: google.cloud.videointelligence.v1.AnnotateVideoResponse
- name: google.cloud.videointelligence.v1.VideoSegment

documentation:
  summary: |-
    Detects objects, explicit content, and scene changes in videos. It also
    specifies the region for annotation and transcribes speech to text.
    Supports both asynchronous API and streaming API.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
    additional_bindings:
    - post: '/v1/operations/{name=projects/*/locations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - delete: '/v1/operations/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/v1/operations/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.videointelligence.v1.VideoIntelligenceService.AnnotateVideo
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
