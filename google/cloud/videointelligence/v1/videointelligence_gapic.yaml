type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  python:
    package_name: google.cloud.videointelligence_v1.gapic
  go:
    package_name: cloud.google.com/go/videointelligence/apiv1
  csharp:
    package_name: Google.Cloud.VideoIntelligence.V1
  ruby:
    package_name: Google::Cloud::VideoIntelligence::V1
    release_level: GA
  php:
    package_name: Google\Cloud\VideoIntelligence\V1
  nodejs:
    package_name: video-intelligence.v1
    domain_layer_location: google-cloud
interfaces:
- name: google.cloud.videointelligence.v1.VideoIntelligenceService
  smoke_test:
    method: AnnotateVideo
    init_fields:
    - input_uri=gs://cloud-samples-data/video/cat.mp4
    - features[0]=LABEL_DETECTION
  methods:
  - name: AnnotateVideo
    long_running:
      initial_poll_delay_millis: 20000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 86400000
    sample_code_init_fields:
    - input_uri=gs://cloud-samples-data/video/cat.mp4
    - features[0]=LABEL_DETECTION
