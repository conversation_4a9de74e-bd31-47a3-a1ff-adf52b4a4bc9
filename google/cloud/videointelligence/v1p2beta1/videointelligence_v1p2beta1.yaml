type: google.api.Service
config_version: 3
name: videointelligence.googleapis.com
title: Cloud Video Intelligence API

apis:
- name: google.cloud.videointelligence.v1p2beta1.VideoIntelligenceService

types:
- name: google.cloud.videointelligence.v1p2beta1.AnnotateVideoProgress
- name: google.cloud.videointelligence.v1p2beta1.AnnotateVideoResponse
- name: google.cloud.videointelligence.v1p2beta1.VideoSegment
- name: google.rpc.Status

documentation:
  summary: |-
    Detects objects, explicit content, and scene changes in videos. It also
    specifies the region for annotation and transcribes speech to text.
    Supports both asynchronous API and streaming API.

backend:
  rules:
  - selector: google.longrunning.Operations.ListOperations
    deadline: 600.0
  - selector: google.longrunning.Operations.GetOperation
    deadline: 600.0
  - selector: google.longrunning.Operations.DeleteOperation
    deadline: 600.0
  - selector: google.longrunning.Operations.CancelOperation
    deadline: 600.0
  - selector: google.cloud.videointelligence.v1p2beta1.VideoIntelligenceService.AnnotateVideo
    deadline: 600.0

http:
  rules:
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1p2beta1/{name=projects/*/locations/*}/operations'

  - selector: google.longrunning.Operations.GetOperation
    get: '/v1p2beta1/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/v1p2beta1/operations/{name=projects/*/locations/*/operations/*}'

  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1p2beta1/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - delete: '/v1p2beta1/operations/{name=projects/*/locations/*/operations/*}'

  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1p2beta1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
    additional_bindings:
    - post: '/v1p2beta1/operations/{name=projects/*/locations/*/operations/*}:cancel'

authentication:
  rules:
  - selector: '*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
