# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "videointelligence_proto",
    srcs = [
        "video_intelligence.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "videointelligence_proto_with_info",
    deps = [
        ":videointelligence_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "videointelligence_java_proto",
    deps = [":videointelligence_proto"],
)

java_grpc_library(
    name = "videointelligence_java_grpc",
    srcs = [":videointelligence_proto"],
    deps = [":videointelligence_java_proto"],
)

java_gapic_library(
    name = "videointelligence_java_gapic",
    srcs = [":videointelligence_proto_with_info"],
    gapic_yaml = "videointelligence_gapic.yaml",
    grpc_service_config = "videointelligence_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "videointelligence_v1p1beta1.yaml",
    test_deps = [
        ":videointelligence_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":videointelligence_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "videointelligence_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.videointelligence.v1p1beta1.VideoIntelligenceServiceClientHttpJsonTest",
        "com.google.cloud.videointelligence.v1p1beta1.VideoIntelligenceServiceClientTest",
    ],
    runtime_deps = [":videointelligence_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-videointelligence-v1p1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":videointelligence_java_gapic",
        ":videointelligence_java_grpc",
        ":videointelligence_java_proto",
        ":videointelligence_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "videointelligence_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/videointelligence/apiv1p1beta1/videointelligencepb",
    protos = [":videointelligence_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "videointelligence_go_gapic",
    srcs = [":videointelligence_proto_with_info"],
    grpc_service_config = "videointelligence_grpc_service_config.json",
    importpath = "cloud.google.com/go/videointelligence/apiv1p1beta1;videointelligence",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "videointelligence_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":videointelligence_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-videointelligence-v1p1beta1-go",
    deps = [
        ":videointelligence_go_gapic",
        ":videointelligence_go_gapic_srcjar-snippets.srcjar",
        ":videointelligence_go_gapic_srcjar-test.srcjar",
        ":videointelligence_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "videointelligence_py_gapic",
    srcs = [":videointelligence_proto"],
    grpc_service_config = "videointelligence_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "videointelligence_v1p1beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "videointelligence_py_gapic_test",
    srcs = [
        "videointelligence_py_gapic_pytest.py",
        "videointelligence_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":videointelligence_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "videointelligence-v1p1beta1-py",
    deps = [
        ":videointelligence_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "videointelligence_php_proto",
    deps = [":videointelligence_proto"],
)

php_gapic_library(
    name = "videointelligence_php_gapic",
    srcs = [":videointelligence_proto_with_info"],
    gapic_yaml = "videointelligence_gapic.yaml",
    grpc_service_config = "videointelligence_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "videointelligence_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [":videointelligence_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-videointelligence-v1p1beta1-php",
    deps = [
        ":videointelligence_php_gapic",
        ":videointelligence_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "videointelligence_nodejs_gapic",
    package_name = "@google-cloud/video-intelligence",
    src = ":videointelligence_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "videointelligence_grpc_service_config.json",
    main_service = "videointelligence",
    package = "google.cloud.videointelligence.v1p1beta1",
    rest_numeric_enums = True,
    service_yaml = "videointelligence_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "videointelligence-v1p1beta1-nodejs",
    deps = [
        ":videointelligence_nodejs_gapic",
        ":videointelligence_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "videointelligence_ruby_proto",
    deps = [":videointelligence_proto"],
)

ruby_grpc_library(
    name = "videointelligence_ruby_grpc",
    srcs = [":videointelligence_proto"],
    deps = [":videointelligence_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "videointelligence_ruby_gapic",
    srcs = [":videointelligence_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-video_intelligence-v1p1beta1",
        "ruby-cloud-env-prefix=VIDEO_INTELLIGENCE",
        "ruby-cloud-product-url=https://cloud.google.com/video-intelligence",
        "ruby-cloud-api-id=videointelligence.googleapis.com",
        "ruby-cloud-api-shortname=videointelligence",
    ],
    grpc_service_config = "videointelligence_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Detects objects, explicit content, and scene changes in videos. It also specifies the region for annotation and transcribes speech to text. Supports both asynchronous API and streaming API.",
    ruby_cloud_title = "Cloud Video Intelligence V1p1beta1",
    service_yaml = "videointelligence_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":videointelligence_ruby_grpc",
        ":videointelligence_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-videointelligence-v1p1beta1-ruby",
    deps = [
        ":videointelligence_ruby_gapic",
        ":videointelligence_ruby_grpc",
        ":videointelligence_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "videointelligence_csharp_proto",
    deps = [":videointelligence_proto"],
)

csharp_grpc_library(
    name = "videointelligence_csharp_grpc",
    srcs = [":videointelligence_proto"],
    deps = [":videointelligence_csharp_proto"],
)

csharp_gapic_library(
    name = "videointelligence_csharp_gapic",
    srcs = [":videointelligence_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "videointelligence_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "videointelligence_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":videointelligence_csharp_grpc",
        ":videointelligence_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-videointelligence-v1p1beta1-csharp",
    deps = [
        ":videointelligence_csharp_gapic",
        ":videointelligence_csharp_grpc",
        ":videointelligence_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
