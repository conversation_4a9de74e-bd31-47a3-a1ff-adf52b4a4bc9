{"methodConfig": [{"name": [{"service": "google.cloud.videointelligence.v1p3beta1.VideoIntelligenceService", "method": "AnnotateVideo"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "120s", "backoffMultiplier": 2.5, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.videointelligence.v1p3beta1.StreamingVideoIntelligenceService", "method": "StreamingAnnotateVideo"}], "timeout": "10800s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}