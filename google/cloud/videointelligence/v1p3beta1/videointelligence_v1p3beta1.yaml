type: google.api.Service
config_version: 3
name: videointelligence.googleapis.com
title: Cloud Video Intelligence API

apis:
- name: google.cloud.videointelligence.v1p3beta1.StreamingVideoIntelligenceService
- name: google.cloud.videointelligence.v1p3beta1.VideoIntelligenceService

types:
- name: google.cloud.videointelligence.v1p3beta1.AnnotateVideoProgress
- name: google.cloud.videointelligence.v1p3beta1.AnnotateVideoResponse
- name: google.cloud.videointelligence.v1p3beta1.StreamingAnnotateVideoResponse
- name: google.cloud.videointelligence.v1p3beta1.VideoSegment

documentation:
  summary: |-
    Detects objects, explicit content, and scene changes in videos. It also
    specifies the region for annotation and transcribes speech to text.
    Supports both asynchronous API and streaming API.

authentication:
  rules:
  - selector: google.cloud.videointelligence.v1p3beta1.StreamingVideoIntelligenceService.StreamingAnnotateVideo
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.videointelligence.v1p3beta1.VideoIntelligenceService.AnnotateVideo
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
