# This build file includes a target for the Ruby wrapper library for
# google-cloud-video_intelligence.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for videointelligence.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "videointelligence_ruby_wrapper",
    srcs = ["//google/cloud/videointelligence/v1:videointelligence_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-video_intelligence",
        "ruby-cloud-env-prefix=VIDEO_INTELLIGENCE",
        "ruby-cloud-wrapper-of=v1:0.11;v1beta2:0.9;v1p1beta1:0.9;v1p2beta1:0.10",
        "ruby-cloud-product-url=https://cloud.google.com/video-intelligence",
        "ruby-cloud-api-id=videointelligence.googleapis.com",
        "ruby-cloud-api-shortname=videointelligence",
        "ruby-cloud-migration-version=3.0",
    ],
    ruby_cloud_description = "Detects objects, explicit content, and scene changes in videos. It also specifies the region for annotation and transcribes speech to text. Supports both asynchronous API and streaming API.",
    ruby_cloud_title = "Cloud Video Intelligence",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-videointelligence-ruby",
    deps = [
        ":videointelligence_ruby_wrapper",
    ],
)
