package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "common_proto",
    srcs = [
        "operation_metadata.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "common_proto_with_info",
    deps = [
        ":common_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "common_java_proto",
    deps = [":common_proto"],
)

java_grpc_library(
    name = "common_java_grpc",
    srcs = [":common_proto"],
    deps = [":common_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "common_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/cloud/common",
    protos = [":common_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "common_php_proto",
    deps = [":common_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-common-php",
    deps = [
        ":common_php_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_proto_library",
)

py_gapic_library(
    name = "common_py_gapic",
    srcs = [":common_proto"],
    rest_numeric_enums = False,
    transport = "grpc",
)

py_proto_library(
    name = "common_py_proto",
    deps = [":common_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "common-py",
    deps = [
        ":common_py_gapic",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "common_ruby_proto",
    deps = [":common_proto"],
)

ruby_grpc_library(
    name = "common_ruby_grpc",
    srcs = [":common_proto"],
    deps = [":common_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "common_csharp_proto",
    deps = [":common_proto"],
)

csharp_grpc_library(
    name = "common_csharp_grpc",
    srcs = [":common_proto"],
    deps = [":common_csharp_proto"],
)

csharp_gapic_assembly_pkg(
    name = "google-cloud-common-csharp",
    package_name = "Google.Cloud.Common",
    generate_nongapic_package = True,
    deps = [
        ":common_csharp_grpc",
        ":common_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "common_cc_proto",
    deps = [":common_proto"],
)

cc_grpc_library(
    name = "common_cc_grpc",
    srcs = [":common_proto"],
    grpc_only = True,
    deps = [":common_cc_proto"],
)
