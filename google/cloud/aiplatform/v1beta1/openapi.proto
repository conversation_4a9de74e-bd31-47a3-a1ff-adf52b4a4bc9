// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "OpenApiProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Type contains the list of OpenAPI data types as defined by
// https://swagger.io/docs/specification/data-models/data-types/
enum Type {
  // Not specified, should not be used.
  TYPE_UNSPECIFIED = 0;

  // OpenAPI string type
  STRING = 1;

  // OpenAPI number type
  NUMBER = 2;

  // OpenAPI integer type
  INTEGER = 3;

  // OpenAPI boolean type
  BOOLEAN = 4;

  // OpenAPI array type
  ARRAY = 5;

  // OpenAPI object type
  OBJECT = 6;
}

// Schema is used to define the format of input/output data. Represents a select
// subset of an [OpenAPI 3.0 schema
// object](https://spec.openapis.org/oas/v3.0.3#schema-object). More fields may
// be added in the future as needed.
message Schema {
  // Optional. The type of the data.
  Type type = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The format of the data.
  // Supported formats:
  //  for NUMBER type: "float", "double"
  //  for INTEGER type: "int32", "int64"
  //  for STRING type: "email", "byte", etc
  string format = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The title of the Schema.
  string title = 24 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The description of the data.
  string description = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates if the value may be null.
  bool nullable = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Default value of the data.
  google.protobuf.Value default = 23 [(google.api.field_behavior) = OPTIONAL];

  // Optional. SCHEMA FIELDS FOR TYPE ARRAY
  // Schema of the elements of Type.ARRAY.
  Schema items = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum number of the elements for Type.ARRAY.
  int64 min_items = 21 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum number of the elements for Type.ARRAY.
  int64 max_items = 22 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Possible values of the element of Type.STRING with enum format.
  // For example we can define an Enum Direction as :
  // {type:STRING, format:enum, enum:["EAST", NORTH", "SOUTH", "WEST"]}
  repeated string enum = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. SCHEMA FIELDS FOR TYPE OBJECT
  // Properties of Type.OBJECT.
  map<string, Schema> properties = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The order of the properties.
  // Not a standard field in open api spec. Only used to support the order of
  // the properties.
  repeated string property_ordering = 25
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Required properties of Type.OBJECT.
  repeated string required = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum number of the properties for Type.OBJECT.
  int64 min_properties = 14 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum number of the properties for Type.OBJECT.
  int64 max_properties = 15 [(google.api.field_behavior) = OPTIONAL];

  // Optional. SCHEMA FIELDS FOR TYPE INTEGER and NUMBER
  // Minimum value of the Type.INTEGER and Type.NUMBER
  double minimum = 16 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum value of the Type.INTEGER and Type.NUMBER
  double maximum = 17 [(google.api.field_behavior) = OPTIONAL];

  // Optional. SCHEMA FIELDS FOR TYPE STRING
  // Minimum length of the Type.STRING
  int64 min_length = 18 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum length of the Type.STRING
  int64 max_length = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pattern of the Type.STRING to restrict a string to a regular
  // expression.
  string pattern = 20 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Example of the object. Will only populated when the object is the
  // root.
  google.protobuf.Value example = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value should be validated against any (one or more) of the
  // subschemas in the list.
  repeated Schema any_of = 11 [(google.api.field_behavior) = OPTIONAL];
}
