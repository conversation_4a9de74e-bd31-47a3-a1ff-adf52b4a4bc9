// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/io.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeatureGroupProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Vertex AI Feature Group.
message FeatureGroup {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/FeatureGroup"
    pattern: "projects/{project}/locations/{location}/featureGroups/{feature_group}"
    plural: "featureGroups"
    singular: "featureGroup"
  };

  // Input source type for BigQuery Tables and Views.
  message BigQuery {
    message TimeSeries {
      // Optional. Column hosting timestamp values for a time-series source.
      // Will be used to determine the latest `feature_values` for each entity.
      // Optional. If not provided, column named `feature_timestamp` of
      // type `TIMESTAMP` will be used.
      string timestamp_column = 1 [(google.api.field_behavior) = OPTIONAL];
    }

    // Required. Immutable. The BigQuery source URI that points to either a
    // BigQuery Table or View.
    BigQuerySource big_query_source = 1 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.field_behavior) = REQUIRED
    ];

    // Optional. Columns to construct entity_id / row keys.
    // If not provided defaults to `entity_id`.
    repeated string entity_id_columns = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Set if the data source is not a time-series.
    bool static_data_source = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If the source is a time-series source, this can be set to
    // control how downstream sources (ex:
    // [FeatureView][google.cloud.aiplatform.v1beta1.FeatureView] ) will treat
    // time-series sources. If not set, will treat the source as a time-series
    // source with `feature_timestamp` as timestamp column and no scan boundary.
    TimeSeries time_series = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If set, all feature values will be fetched
    // from a single row per unique entityId including nulls.
    // If not set, will collapse all rows for each unique entityId into a singe
    // row with any non-null values if present, if no non-null values are
    // present will sync null.
    // ex: If source has schema
    // `(entity_id, feature_timestamp, f0, f1)` and the following rows:
    // `(e1, 2020-01-01T10:00:00.123Z, 10, 15)`
    // `(e1, 2020-02-01T10:00:00.123Z, 20, null)`
    // If dense is set, `(e1, 20, null)` is synced to online stores. If dense is
    // not set, `(e1, 20, 15)` is synced to online stores.
    bool dense = 5 [(google.api.field_behavior) = OPTIONAL];
  }

  // Service agent type used during jobs under a FeatureGroup.
  enum ServiceAgentType {
    // By default, the project-level Vertex AI Service Agent is enabled.
    SERVICE_AGENT_TYPE_UNSPECIFIED = 0;

    // Specifies the project-level Vertex AI Service Agent
    // (https://cloud.google.com/vertex-ai/docs/general/access-control#service-agents).
    SERVICE_AGENT_TYPE_PROJECT = 1;

    // Enable a FeatureGroup service account to be created by Vertex AI and
    // output in the field `service_account_email`. This service account will
    // be used to read from the source BigQuery table during jobs under a
    // FeatureGroup.
    SERVICE_AGENT_TYPE_FEATURE_GROUP = 2;
  }

  oneof source {
    // Indicates that features for this group come from BigQuery Table/View.
    // By default treats the source as a sparse time series source. The BigQuery
    // source table or view must have at least one entity ID column and a column
    // named `feature_timestamp`.
    BigQuery big_query = 7;
  }

  // Identifier. Name of the FeatureGroup. Format:
  // `projects/{project}/locations/{location}/featureGroups/{featureGroup}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Timestamp when this FeatureGroup was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this FeatureGroup was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Used to perform consistent read-modify-write updates. If not set,
  // a blind "overwrite" update happens.
  string etag = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The labels with user-defined metadata to organize your
  // FeatureGroup.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  //
  // See https://goo.gl/xmQnxf for more information on and examples of labels.
  // No more than 64 user labels can be associated with one
  // FeatureGroup(System labels are excluded)." System reserved label keys
  // are prefixed with "aiplatform.googleapis.com/" and are immutable.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of the FeatureGroup.
  string description = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Service agent type used during jobs under a FeatureGroup. By
  // default, the Vertex AI Service Agent is used. When using an IAM Policy to
  // isolate this FeatureGroup within a project, a separate service account
  // should be provisioned by setting this field to
  // `SERVICE_AGENT_TYPE_FEATURE_GROUP`. This will generate a separate service
  // account to access the BigQuery source table.
  ServiceAgentType service_agent_type = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. A Service Account unique to this FeatureGroup. The role
  // bigquery.dataViewer should be granted to this service account to allow
  // Vertex AI Feature Store to access source data while running jobs under this
  // FeatureGroup.
  string service_account_email = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}
