// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/model_monitor.proto";
import "google/cloud/aiplatform/v1beta1/model_monitoring_alert.proto";
import "google/cloud/aiplatform/v1beta1/model_monitoring_job.proto";
import "google/cloud/aiplatform/v1beta1/model_monitoring_stats.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/type/interval.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ModelMonitoringServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service for creating and managing Vertex AI Model moitoring. This includes
// `ModelMonitor` resources, `ModelMonitoringJob` resources.
service ModelMonitoringService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a ModelMonitor.
  rpc CreateModelMonitor(CreateModelMonitorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/modelMonitors"
      body: "model_monitor"
    };
    option (google.api.method_signature) = "parent,model_monitor";
    option (google.longrunning.operation_info) = {
      response_type: "ModelMonitor"
      metadata_type: "CreateModelMonitorOperationMetadata"
    };
  }

  // Updates a ModelMonitor.
  rpc UpdateModelMonitor(UpdateModelMonitorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{model_monitor.name=projects/*/locations/*/modelMonitors/*}"
      body: "model_monitor"
    };
    option (google.api.method_signature) = "model_monitor,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ModelMonitor"
      metadata_type: "UpdateModelMonitorOperationMetadata"
    };
  }

  // Gets a ModelMonitor.
  rpc GetModelMonitor(GetModelMonitorRequest) returns (ModelMonitor) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/modelMonitors/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ModelMonitors in a Location.
  rpc ListModelMonitors(ListModelMonitorsRequest)
      returns (ListModelMonitorsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/modelMonitors"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a ModelMonitor.
  rpc DeleteModelMonitor(DeleteModelMonitorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/modelMonitors/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Creates a ModelMonitoringJob.
  rpc CreateModelMonitoringJob(CreateModelMonitoringJobRequest)
      returns (ModelMonitoringJob) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/modelMonitors/*}/modelMonitoringJobs"
      body: "model_monitoring_job"
    };
    option (google.api.method_signature) = "parent,model_monitoring_job";
  }

  // Gets a ModelMonitoringJob.
  rpc GetModelMonitoringJob(GetModelMonitoringJobRequest)
      returns (ModelMonitoringJob) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/modelMonitors/*/modelMonitoringJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ModelMonitoringJobs.
  // Callers may choose to read across multiple Monitors as per
  // [AIP-159](https://google.aip.dev/159) by using '-' (the hyphen or dash
  // character) as a wildcard character instead of modelMonitor id in the
  // parent. Format
  // `projects/{project_id}/locations/{location}/moodelMonitors/-/modelMonitoringJobs`
  rpc ListModelMonitoringJobs(ListModelMonitoringJobsRequest)
      returns (ListModelMonitoringJobsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/modelMonitors/*}/modelMonitoringJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a ModelMonitoringJob.
  rpc DeleteModelMonitoringJob(DeleteModelMonitoringJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/modelMonitors/*/modelMonitoringJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Searches Model Monitoring Stats generated within a given time window.
  rpc SearchModelMonitoringStats(SearchModelMonitoringStatsRequest)
      returns (SearchModelMonitoringStatsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{model_monitor=projects/*/locations/*/modelMonitors/*}:searchModelMonitoringStats"
      body: "*"
    };
    option (google.api.method_signature) = "model_monitor";
  }

  // Returns the Model Monitoring alerts.
  rpc SearchModelMonitoringAlerts(SearchModelMonitoringAlertsRequest)
      returns (SearchModelMonitoringAlertsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{model_monitor=projects/*/locations/*/modelMonitors/*}:searchModelMonitoringAlerts"
      body: "*"
    };
    option (google.api.method_signature) = "model_monitor";
  }
}

// Request message for
// [ModelMonitoringService.CreateModelMonitor][google.cloud.aiplatform.v1beta1.ModelMonitoringService.CreateModelMonitor].
message CreateModelMonitorRequest {
  // Required. The resource name of the Location to create the ModelMonitor in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The ModelMonitor to create.
  ModelMonitor model_monitor = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The ID to use for the Model Monitor, which will become the final
  // component of the model monitor resource name.
  //
  // The maximum length is 63 characters, and valid characters are
  // `/^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$/`.
  string model_monitor_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Runtime operation information for
// [ModelMonitoringService.CreateModelMonitor][google.cloud.aiplatform.v1beta1.ModelMonitoringService.CreateModelMonitor].
message CreateModelMonitorOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [ModelMonitoringService.UpdateModelMonitor][google.cloud.aiplatform.v1beta1.ModelMonitoringService.UpdateModelMonitor].
message UpdateModelMonitorRequest {
  // Required. The model monitoring configuration which replaces the resource on
  // the server.
  ModelMonitor model_monitor = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Mask specifying which fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Runtime operation information for
// [ModelMonitoringService.UpdateModelMonitor][google.cloud.aiplatform.v1beta1.ModelMonitoringService.UpdateModelMonitor].
message UpdateModelMonitorOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [ModelMonitoringService.GetModelMonitor][google.cloud.aiplatform.v1beta1.ModelMonitoringService.GetModelMonitor].
message GetModelMonitorRequest {
  // Required. The name of the ModelMonitor resource.
  // Format:
  // `projects/{project}/locations/{location}/modelMonitors/{model_monitor}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelMonitor"
    }
  ];
}

// Request message for
// [ModelMonitoringService.ListModelMonitors][google.cloud.aiplatform.v1beta1.ModelMonitoringService.ListModelMonitors].
message ListModelMonitorsRequest {
  // Required. The resource name of the Location to list the ModelMonitors from.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The standard list filter.
  // More detail in [AIP-160](https://google.aip.dev/160).
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [ModelMonitoringService.ListModelMonitors][google.cloud.aiplatform.v1beta1.ModelMonitoringService.ListModelMonitors]
message ListModelMonitorsResponse {
  // List of ModelMonitor in the requested page.
  repeated ModelMonitor model_monitors = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListModelMonitorsRequest.page_token][google.cloud.aiplatform.v1beta1.ListModelMonitorsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [ModelMonitoringService.DeleteModelMonitor][google.cloud.aiplatform.v1beta1.ModelMonitoringService.DeleteModelMonitor].
message DeleteModelMonitorRequest {
  // Required. The name of the ModelMonitor resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/modelMonitords/{model_monitor}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelMonitor"
    }
  ];

  // Optional. Force delete the model monitor with schedules.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [ModelMonitoringService.CreateModelMonitoringJob][google.cloud.aiplatform.v1beta1.ModelMonitoringService.CreateModelMonitoringJob].
message CreateModelMonitoringJobRequest {
  // Required. The parent of the ModelMonitoringJob.
  // Format:
  // `projects/{project}/locations/{location}/modelMoniitors/{model_monitor}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelMonitor"
    }
  ];

  // Required. The ModelMonitoringJob to create
  ModelMonitoringJob model_monitoring_job = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The ID to use for the Model Monitoring Job, which will become the
  // final component of the model monitoring job resource name.
  //
  // The maximum length is 63 characters, and valid characters are
  // `/^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$/`.
  string model_monitoring_job_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [ModelMonitoringService.GetModelMonitoringJob][google.cloud.aiplatform.v1beta1.ModelMonitoringService.GetModelMonitoringJob].
message GetModelMonitoringJobRequest {
  // Required. The resource name of the ModelMonitoringJob.
  // Format:
  // `projects/{project}/locations/{location}/modelMonitors/{model_monitor}/modelMonitoringJobs/{model_monitoring_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelMonitoringJob"
    }
  ];
}

// Request message for
// [ModelMonitoringService.ListModelMonitoringJobs][google.cloud.aiplatform.v1beta1.ModelMonitoringService.ListModelMonitoringJobs].
message ListModelMonitoringJobsRequest {
  // Required. The parent of the ModelMonitoringJob.
  // Format:
  // `projects/{project}/locations/{location}/modelMonitors/{model_monitor}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelMonitor"
    }
  ];

  // The standard list filter.
  // More detail in [AIP-160](https://google.aip.dev/160).
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  string page_token = 4;

  // Mask specifying which fields to read
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [ModelMonitoringService.ListModelMonitoringJobs][google.cloud.aiplatform.v1beta1.ModelMonitoringService.ListModelMonitoringJobs].
message ListModelMonitoringJobsResponse {
  // A list of ModelMonitoringJobs that matches the specified filter
  // in the request.
  repeated ModelMonitoringJob model_monitoring_jobs = 1;

  // The standard List next-page token.
  string next_page_token = 2;
}

// Request message for
// [ModelMonitoringService.DeleteModelMonitoringJob][google.cloud.aiplatform.v1beta1.ModelMonitoringService.DeleteModelMonitoringJob].
message DeleteModelMonitoringJobRequest {
  // Required. The resource name of the model monitoring job to delete.
  // Format:
  // `projects/{project}/locations/{location}/modelMonitors/{model_monitor}/modelMonitoringJobs/{model_monitoring_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelMonitoringJob"
    }
  ];
}

// Request message for
// [ModelMonitoringService.SearchModelMonitoringStats][google.cloud.aiplatform.v1beta1.ModelMonitoringService.SearchModelMonitoringStats].
message SearchModelMonitoringStatsRequest {
  // Required. ModelMonitor resource name.
  // Format:
  // `projects/{project}/locations/{location}/modelMonitors/{model_monitor}`
  string model_monitor = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelMonitor"
    }
  ];

  // Filter for search different stats.
  SearchModelMonitoringStatsFilter stats_filter = 2;

  // The time interval for which results should be returned.
  google.type.Interval time_interval = 3;

  // The standard list page size.
  int32 page_size = 4;

  // A page token received from a previous
  // [ModelMonitoringService.SearchModelMonitoringStats][google.cloud.aiplatform.v1beta1.ModelMonitoringService.SearchModelMonitoringStats]
  // call.
  string page_token = 5;
}

// Response message for
// [ModelMonitoringService.SearchModelMonitoringStats][google.cloud.aiplatform.v1beta1.ModelMonitoringService.SearchModelMonitoringStats].
message SearchModelMonitoringStatsResponse {
  // Stats retrieved for requested objectives.
  repeated ModelMonitoringStats monitoring_stats = 1;

  // The page token that can be used by the next
  // [ModelMonitoringService.SearchModelMonitoringStats][google.cloud.aiplatform.v1beta1.ModelMonitoringService.SearchModelMonitoringStats]
  // call.
  string next_page_token = 2;
}

// Request message for
// [ModelMonitoringService.SearchModelMonitoringAlerts][google.cloud.aiplatform.v1beta1.ModelMonitoringService.SearchModelMonitoringAlerts].
message SearchModelMonitoringAlertsRequest {
  // Required. ModelMonitor resource name.
  // Format:
  // `projects/{project}/locations/{location}/modelMonitors/{model_monitor}`
  string model_monitor = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelMonitor"
    }
  ];

  // If non-empty, returns the alerts of this model monitoring job.
  string model_monitoring_job = 2;

  // If non-empty, returns the alerts in this time interval.
  google.type.Interval alert_time_interval = 3;

  // If non-empty, returns the alerts of this stats_name.
  string stats_name = 4;

  // If non-empty, returns the alerts of this objective type.
  // Supported monitoring objectives:
  // `raw-feature-drift`
  // `prediction-output-drift`
  // `feature-attribution`
  string objective_type = 5;

  // The standard list page size.
  int32 page_size = 6;

  // A page token received from a previous
  // [ModelMonitoringService.SearchModelMonitoringAlerts][google.cloud.aiplatform.v1beta1.ModelMonitoringService.SearchModelMonitoringAlerts]
  // call.
  string page_token = 7;
}

// Response message for
// [ModelMonitoringService.SearchModelMonitoringAlerts][google.cloud.aiplatform.v1beta1.ModelMonitoringService.SearchModelMonitoringAlerts].
message SearchModelMonitoringAlertsResponse {
  // Alerts retrieved for the requested objectives. Sorted by alert time
  // descendingly.
  repeated ModelMonitoringAlert model_monitoring_alerts = 1;

  // The total number of alerts retrieved by the requested objectives.
  int64 total_number_alerts = 2;

  // The page token that can be used by the next
  // [ModelMonitoringService.SearchModelMonitoringAlerts][google.cloud.aiplatform.v1beta1.ModelMonitoringService.SearchModelMonitoringAlerts]
  // call.
  string next_page_token = 3;
}
