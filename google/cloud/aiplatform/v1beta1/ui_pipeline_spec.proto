// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/value.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "UiPipelineSpecProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// The definition of a artifact type in MLMD.
message ArtifactTypeSchema {
  oneof kind {
    // The name of the type. The format of the title must be:
    // `<namespace>.<title>`.
    // Examples:
    //  - `aiplatform.Model`
    //  - `acme.CustomModel`
    // When this field is set, the type must be pre-registered in the MLMD
    // store.
    string schema_title = 1;

    // Points to a YAML file stored on Cloud Storage describing the
    // format.
    // Deprecated. Use [PipelineArtifactTypeSchema.schema_title][] or
    // [PipelineArtifactTypeSchema.instance_schema][] instead.
    string schema_uri = 2 [deprecated = true];

    // Contains a raw YAML string, describing the format of
    // the properties of the type.
    string instance_schema = 3;
  }

  // The schema version of the artifact. If the value is not set, it defaults
  // to the latest version in the system.
  string schema_version = 4;
}

// The definition of a runtime artifact.
message RuntimeArtifact {
  // The name of an artifact.
  string name = 1;

  // The type of the artifact.
  ArtifactTypeSchema type = 2;

  // The URI of the artifact.
  string uri = 3;

  // The properties of the artifact.
  // Deprecated. Use
  // [RuntimeArtifact.metadata][google.cloud.aiplatform.v1beta1.RuntimeArtifact.metadata]
  // instead.
  map<string, Value> properties = 4 [deprecated = true];

  // The custom properties of the artifact.
  // Deprecated. Use
  // [RuntimeArtifact.metadata][google.cloud.aiplatform.v1beta1.RuntimeArtifact.metadata]
  // instead.
  map<string, Value> custom_properties = 5 [deprecated = true];

  // Properties of the Artifact.
  google.protobuf.Struct metadata = 6;
}
