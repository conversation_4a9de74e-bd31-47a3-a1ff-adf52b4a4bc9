// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "TensorboardTimeSeriesProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// TensorboardTimeSeries maps to times series produced in training runs
message TensorboardTimeSeries {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/TensorboardTimeSeries"
    pattern: "projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}/timeSeries/{time_series}"
  };

  // Describes metadata for a TensorboardTimeSeries.
  message Metadata {
    // Output only. Max step index of all data points within a
    // TensorboardTimeSeries.
    int64 max_step = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Max wall clock timestamp of all data points within a
    // TensorboardTimeSeries.
    google.protobuf.Timestamp max_wall_time = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The largest blob sequence length (number of blobs) of all
    // data points in this time series, if its ValueType is BLOB_SEQUENCE.
    int64 max_blob_sequence_length = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // An enum representing the value type of a TensorboardTimeSeries.
  enum ValueType {
    // The value type is unspecified.
    VALUE_TYPE_UNSPECIFIED = 0;

    // Used for TensorboardTimeSeries that is a list of scalars.
    // E.g. accuracy of a model over epochs/time.
    SCALAR = 1;

    // Used for TensorboardTimeSeries that is a list of tensors.
    // E.g. histograms of weights of layer in a model over epoch/time.
    TENSOR = 2;

    // Used for TensorboardTimeSeries that is a list of blob sequences.
    // E.g. set of sample images with labels over epochs/time.
    BLOB_SEQUENCE = 3;
  }

  // Output only. Name of the TensorboardTimeSeries.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. User provided name of this TensorboardTimeSeries.
  // This value should be unique among all TensorboardTimeSeries resources
  // belonging to the same TensorboardRun resource (parent resource).
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Description of this TensorboardTimeSeries.
  string description = 3;

  // Required. Immutable. Type of TensorboardTimeSeries value.
  ValueType value_type = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Timestamp when this TensorboardTimeSeries was created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this TensorboardTimeSeries was last updated.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Used to perform a consistent read-modify-write updates. If not set, a blind
  // "overwrite" update happens.
  string etag = 7;

  // Immutable. Name of the plugin this time series pertain to. Such as Scalar,
  // Tensor, Blob
  string plugin_name = 8 [(google.api.field_behavior) = IMMUTABLE];

  // Data of the current plugin, with the size limited to 65KB.
  bytes plugin_data = 9;

  // Output only. Scalar, Tensor, or Blob metadata for this
  // TensorboardTimeSeries.
  Metadata metadata = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}
