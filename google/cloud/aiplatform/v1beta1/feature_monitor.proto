// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/type/interval.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeatureMonitorProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Vertex AI Feature Monitor.
message FeatureMonitor {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/FeatureMonitor"
    pattern: "projects/{project}/locations/{location}/featureGroups/{feature_group}/featureMonitors/{feature_monitor}"
    plural: "featureMonitors"
    singular: "featureMonitor"
  };

  // Identifier. Name of the FeatureMonitor. Format:
  // `projects/{project}/locations/{location}/featureGroups/{featureGroup}/featureMonitors/{featureMonitor}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Timestamp when this FeatureMonitor was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this FeatureMonitor was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Used to perform consistent read-modify-write updates. If not set,
  // a blind "overwrite" update happens.
  string etag = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The labels with user-defined metadata to organize your
  // FeatureMonitor.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  //
  // See https://goo.gl/xmQnxf for more information on and examples of labels.
  // No more than 64 user labels can be associated with one
  // FeatureMonitor(System labels are excluded)." System reserved label keys
  // are prefixed with "aiplatform.googleapis.com/" and are immutable.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of the FeatureMonitor.
  string description = 6 [(google.api.field_behavior) = OPTIONAL];

  // Required. Schedule config for the FeatureMonitor.
  ScheduleConfig schedule_config = 7 [(google.api.field_behavior) = REQUIRED];

  // Required. Feature selection config for the FeatureMonitor.
  FeatureSelectionConfig feature_selection_config = 8
      [(google.api.field_behavior) = REQUIRED];
}

// Schedule configuration for the FeatureMonitor.
message ScheduleConfig {
  // Cron schedule (https://en.wikipedia.org/wiki/Cron) to launch scheduled
  // runs. To explicitly set a timezone to the cron tab, apply a prefix in
  // the cron tab: "CRON_TZ=${IANA_TIME_ZONE}" or "TZ=${IANA_TIME_ZONE}".
  // The ${IANA_TIME_ZONE} may only be a valid string from IANA time zone
  // database. For example, "CRON_TZ=America/New_York 1 * * * *", or
  // "TZ=America/New_York 1 * * * *".
  string cron = 1;
}

// Feature selection configuration for the FeatureMonitor.
message FeatureSelectionConfig {
  // Feature configuration.
  message FeatureConfig {
    // Required. The ID of the feature resource. Final component of the
    // Feature's resource name.
    string feature_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. Drift threshold. If calculated difference with baseline data
    // larger than threshold, it will be considered as the feature has drift. If
    // not present, the threshold will be default to 0.3.
    double drift_threshold = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. A list of features to be monitored and each feature's drift
  // threshold.
  repeated FeatureConfig feature_configs = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// Stats and Anomaly generated by FeatureMonitorJobs. Anomaly only includes
// Drift.
message FeatureStatsAndAnomaly {
  // Feature Id.
  string feature_id = 1;

  // Feature stats. e.g. histogram buckets.
  // In the format of tensorflow.metadata.v0.DatasetFeatureStatistics.
  google.protobuf.Value feature_stats = 2;

  // Deviation from the current stats to baseline stats.
  //   1. For categorical feature, the distribution distance is calculated by
  //      L-inifinity norm.
  //   2. For numerical feature, the distribution distance is calculated by
  //      Jensen–Shannon divergence.
  double distribution_deviation = 3;

  // This is the threshold used when detecting drifts, which is set in
  // FeatureMonitor.FeatureSelectionConfig.FeatureConfig.drift_threshold
  double drift_detection_threshold = 4;

  // If set to true, indicates current stats is detected as and comparing
  // with baseline stats.
  bool drift_detected = 5;

  // The timestamp we take snapshot for feature values to generate stats.
  google.protobuf.Timestamp stats_time = 6;

  // The ID of the FeatureMonitorJob that generated this FeatureStatsAndAnomaly.
  int64 feature_monitor_job_id = 7;

  // The ID of the FeatureMonitor that this FeatureStatsAndAnomaly generated
  // according to.
  string feature_monitor_id = 8;
}

// Defines how to select FeatureStatsAndAnomaly to be populated in response.
// If set, retrieves FeatureStatsAndAnomaly generated by FeatureMonitors based
// on this spec.
message FeatureStatsAndAnomalySpec {
  // Optional. If set, returns the most recent count of stats. Valid value is
  // [0, 100]. If stats_time_range is set, return most recent count of stats
  // within the stats_time_range.
  optional int32 latest_stats_count = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, return all stats generated between [start_time,
  // end_time). If latest_stats_count is set, return the most recent count of
  // stats within the stats_time_range.
  google.type.Interval stats_time_range = 2
      [(google.api.field_behavior) = OPTIONAL];
}
