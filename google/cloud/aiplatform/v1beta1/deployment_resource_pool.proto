// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/encryption_spec.proto";
import "google/cloud/aiplatform/v1beta1/machine_resources.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "DeploymentResourcePoolProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A description of resources that can be shared by multiple DeployedModels,
// whose underlying specification consists of a DedicatedResources.
message DeploymentResourcePool {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/DeploymentResourcePool"
    pattern: "projects/{project}/locations/{location}/deploymentResourcePools/{deployment_resource_pool}"
  };

  // Immutable. The resource name of the DeploymentResourcePool.
  // Format:
  // `projects/{project}/locations/{location}/deploymentResourcePools/{deployment_resource_pool}`
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Required. The underlying DedicatedResources that the DeploymentResourcePool
  // uses.
  DedicatedResources dedicated_resources = 2
      [(google.api.field_behavior) = REQUIRED];

  // Customer-managed encryption key spec for a DeploymentResourcePool. If set,
  // this DeploymentResourcePool will be secured by this key. Endpoints and the
  // DeploymentResourcePool they deploy in need to have the same EncryptionSpec.
  EncryptionSpec encryption_spec = 5;

  // The service account that the DeploymentResourcePool's container(s) run as.
  // Specify the email address of the service account. If this service account
  // is not specified, the container(s) run as a service account that doesn't
  // have access to the resource project.
  //
  // Users deploying the Models to this DeploymentResourcePool must have the
  // `iam.serviceAccounts.actAs` permission on this service account.
  string service_account = 6;

  // If the DeploymentResourcePool is deployed with custom-trained Models or
  // AutoML Tabular Models, the container(s) of the DeploymentResourcePool will
  // send `stderr` and `stdout` streams to Cloud Logging by default.
  // Please note that the logs incur cost, which are subject to [Cloud Logging
  // pricing](https://cloud.google.com/logging/pricing).
  //
  // User can disable container logging by setting this flag to true.
  bool disable_container_logging = 7;

  // Output only. Timestamp when this DeploymentResourcePool was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}
