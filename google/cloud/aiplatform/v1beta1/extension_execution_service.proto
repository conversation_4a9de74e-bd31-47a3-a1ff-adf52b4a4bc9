// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/content.proto";
import "google/cloud/aiplatform/v1beta1/extension.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ExtensionExecutionServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service for Extension execution.
service ExtensionExecutionService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Executes the request against a given extension.
  rpc ExecuteExtension(ExecuteExtensionRequest)
      returns (ExecuteExtensionResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/extensions/*}:execute"
      body: "*"
    };
    option (google.api.method_signature) = "name,operation_id";
  }

  // Queries an extension with a default controller.
  rpc QueryExtension(QueryExtensionRequest) returns (QueryExtensionResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/extensions/*}:query"
      body: "*"
    };
    option (google.api.method_signature) = "name,contents";
  }
}

// Request message for
// [ExtensionExecutionService.ExecuteExtension][google.cloud.aiplatform.v1beta1.ExtensionExecutionService.ExecuteExtension].
message ExecuteExtensionRequest {
  // Required. Name (identifier) of the extension;
  // Format:
  // `projects/{project}/locations/{location}/extensions/{extension}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Extension"
    }
  ];

  // Required. The desired ID of the operation to be executed in this extension
  //  as defined in
  //  [ExtensionOperation.operation_id][google.cloud.aiplatform.v1beta1.ExtensionOperation.operation_id].
  string operation_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Request parameters that will be used for executing this
  // operation.
  //
  // The struct should be in a form of map with param name as the key and actual
  // param value as the value.
  // E.g. If this operation requires a param "name" to be set to "abc". you can
  // set this to something like {"name": "abc"}.
  google.protobuf.Struct operation_params = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Auth config provided at runtime to override the default value in
  // [Extension.manifest.auth_config][].
  // The AuthConfig.auth_type should match the value in
  // [Extension.manifest.auth_config][].
  AuthConfig runtime_auth_config = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [ExtensionExecutionService.ExecuteExtension][google.cloud.aiplatform.v1beta1.ExtensionExecutionService.ExecuteExtension].
message ExecuteExtensionResponse {
  // Response content from the extension. The content should be conformant to
  // the response.content schema in the extension's manifest/OpenAPI spec.
  string content = 2;
}

// Request message for
// [ExtensionExecutionService.QueryExtension][google.cloud.aiplatform.v1beta1.ExtensionExecutionService.QueryExtension].
message QueryExtensionRequest {
  // Required. Name (identifier) of the extension;
  // Format:
  // `projects/{project}/locations/{location}/extensions/{extension}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Extension"
    }
  ];

  // Required. The content of the current conversation with the model.
  //
  // For single-turn queries, this is a single instance. For multi-turn queries,
  // this is a repeated field that contains conversation history + latest
  // request.
  repeated Content contents = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [ExtensionExecutionService.QueryExtension][google.cloud.aiplatform.v1beta1.ExtensionExecutionService.QueryExtension].
message QueryExtensionResponse {
  // Steps of extension or LLM interaction, can contain function call,
  // function response, or text response. The last step contains the final
  // response to the query.
  repeated Content steps = 1;

  // Failure message if any.
  string failure_message = 2;
}
