// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/encryption_spec.proto";
import "google/cloud/aiplatform/v1beta1/evaluated_annotation.proto";
import "google/cloud/aiplatform/v1beta1/explanation.proto";
import "google/cloud/aiplatform/v1beta1/io.proto";
import "google/cloud/aiplatform/v1beta1/model.proto";
import "google/cloud/aiplatform/v1beta1/model_evaluation.proto";
import "google/cloud/aiplatform/v1beta1/model_evaluation_slice.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ModelServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service for managing Vertex AI's machine learning Models.
service ModelService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Uploads a Model artifact into Vertex AI.
  rpc UploadModel(UploadModelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/models:upload"
      body: "*"
    };
    option (google.api.method_signature) = "parent,model";
    option (google.longrunning.operation_info) = {
      response_type: "UploadModelResponse"
      metadata_type: "UploadModelOperationMetadata"
    };
  }

  // Gets a Model.
  rpc GetModel(GetModelRequest) returns (Model) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/models/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Models in a Location.
  rpc ListModels(ListModelsRequest) returns (ListModelsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/models"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists versions of the specified model.
  rpc ListModelVersions(ListModelVersionsRequest)
      returns (ListModelVersionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/models/*}:listVersions"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a Model.
  rpc UpdateModel(UpdateModelRequest) returns (Model) {
    option (google.api.http) = {
      patch: "/v1beta1/{model.name=projects/*/locations/*/models/*}"
      body: "model"
    };
    option (google.api.method_signature) = "model,update_mask";
  }

  // Incrementally update the dataset used for an examples model.
  rpc UpdateExplanationDataset(UpdateExplanationDatasetRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{model=projects/*/locations/*/models/*}:updateExplanationDataset"
      body: "*"
    };
    option (google.api.method_signature) = "model";
    option (google.longrunning.operation_info) = {
      response_type: "UpdateExplanationDatasetResponse"
      metadata_type: "UpdateExplanationDatasetOperationMetadata"
    };
  }

  // Deletes a Model.
  //
  // A model cannot be deleted if any
  // [Endpoint][google.cloud.aiplatform.v1beta1.Endpoint] resource has a
  // [DeployedModel][google.cloud.aiplatform.v1beta1.DeployedModel] based on the
  // model in its
  // [deployed_models][google.cloud.aiplatform.v1beta1.Endpoint.deployed_models]
  // field.
  rpc DeleteModel(DeleteModelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/models/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Deletes a Model version.
  //
  // Model version can only be deleted if there are no
  // [DeployedModels][google.cloud.aiplatform.v1beta1.DeployedModel] created
  // from it. Deleting the only version in the Model is not allowed. Use
  // [DeleteModel][google.cloud.aiplatform.v1beta1.ModelService.DeleteModel] for
  // deleting the Model instead.
  rpc DeleteModelVersion(DeleteModelVersionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/models/*}:deleteVersion"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Merges a set of aliases for a Model version.
  rpc MergeVersionAliases(MergeVersionAliasesRequest) returns (Model) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/models/*}:mergeVersionAliases"
      body: "*"
    };
    option (google.api.method_signature) = "name,version_aliases";
  }

  // Exports a trained, exportable Model to a location specified by the
  // user. A Model is considered to be exportable if it has at least one
  // [supported export
  // format][google.cloud.aiplatform.v1beta1.Model.supported_export_formats].
  rpc ExportModel(ExportModelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/models/*}:export"
      body: "*"
    };
    option (google.api.method_signature) = "name,output_config";
    option (google.longrunning.operation_info) = {
      response_type: "ExportModelResponse"
      metadata_type: "ExportModelOperationMetadata"
    };
  }

  // Copies an already existing Vertex AI Model into the specified Location.
  // The source Model must exist in the same Project.
  // When copying custom Models, the users themselves are responsible for
  // [Model.metadata][google.cloud.aiplatform.v1beta1.Model.metadata] content to
  // be region-agnostic, as well as making sure that any resources (e.g. files)
  // it depends on remain accessible.
  rpc CopyModel(CopyModelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/models:copy"
      body: "*"
    };
    option (google.api.method_signature) = "parent,source_model";
    option (google.longrunning.operation_info) = {
      response_type: "CopyModelResponse"
      metadata_type: "CopyModelOperationMetadata"
    };
  }

  // Imports an externally generated ModelEvaluation.
  rpc ImportModelEvaluation(ImportModelEvaluationRequest)
      returns (ModelEvaluation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/models/*}/evaluations:import"
      body: "*"
    };
    option (google.api.method_signature) = "parent,model_evaluation";
  }

  // Imports a list of externally generated ModelEvaluationSlice.
  rpc BatchImportModelEvaluationSlices(BatchImportModelEvaluationSlicesRequest)
      returns (BatchImportModelEvaluationSlicesResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/models/*/evaluations/*}/slices:batchImport"
      body: "*"
    };
    option (google.api.method_signature) = "parent,model_evaluation_slices";
  }

  // Imports a list of externally generated EvaluatedAnnotations.
  rpc BatchImportEvaluatedAnnotations(BatchImportEvaluatedAnnotationsRequest)
      returns (BatchImportEvaluatedAnnotationsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/models/*/evaluations/*/slices/*}:batchImport"
      body: "*"
    };
    option (google.api.method_signature) = "parent,evaluated_annotations";
  }

  // Gets a ModelEvaluation.
  rpc GetModelEvaluation(GetModelEvaluationRequest) returns (ModelEvaluation) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/models/*/evaluations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ModelEvaluations in a Model.
  rpc ListModelEvaluations(ListModelEvaluationsRequest)
      returns (ListModelEvaluationsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/models/*}/evaluations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a ModelEvaluationSlice.
  rpc GetModelEvaluationSlice(GetModelEvaluationSliceRequest)
      returns (ModelEvaluationSlice) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/models/*/evaluations/*/slices/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ModelEvaluationSlices in a ModelEvaluation.
  rpc ListModelEvaluationSlices(ListModelEvaluationSlicesRequest)
      returns (ListModelEvaluationSlicesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/models/*/evaluations/*}/slices"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [ModelService.UploadModel][google.cloud.aiplatform.v1beta1.ModelService.UploadModel].
message UploadModelRequest {
  // Required. The resource name of the Location into which to upload the Model.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The resource name of the model into which to upload the version.
  // Only specify this field when uploading a new version.
  string parent_model = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The ID to use for the uploaded Model, which will become the final
  // component of the model resource name.
  //
  // This value may be up to 63 characters, and valid characters are
  // `[a-z0-9_-]`. The first character cannot be a number or hyphen.
  string model_id = 5 [(google.api.field_behavior) = OPTIONAL];

  // Required. The Model to create.
  Model model = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The user-provided custom service account to use to do the model
  // upload. If empty, [Vertex AI Service
  // Agent](https://cloud.google.com/vertex-ai/docs/general/access-control#service-agents)
  // will be used to access resources needed to upload the model. This account
  // must belong to the target project where the model is uploaded to, i.e., the
  // project specified in the `parent` field of this request and have necessary
  // read permissions (to Google Cloud Storage, Artifact Registry, etc.).
  string service_account = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Details of
// [ModelService.UploadModel][google.cloud.aiplatform.v1beta1.ModelService.UploadModel]
// operation.
message UploadModelOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}

// Response message of
// [ModelService.UploadModel][google.cloud.aiplatform.v1beta1.ModelService.UploadModel]
// operation.
message UploadModelResponse {
  // The name of the uploaded Model resource.
  // Format: `projects/{project}/locations/{location}/models/{model}`
  string model = 1 [(google.api.resource_reference) = {
    type: "aiplatform.googleapis.com/Model"
  }];

  // Output only. The version ID of the model that is uploaded.
  string model_version_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [ModelService.GetModel][google.cloud.aiplatform.v1beta1.ModelService.GetModel].
message GetModelRequest {
  // Required. The name of the Model resource.
  // Format: `projects/{project}/locations/{location}/models/{model}`
  //
  // In order to retrieve a specific version of the model, also provide
  // the version ID or version alias.
  //   Example: `projects/{project}/locations/{location}/models/{model}@2`
  //              or
  //            `projects/{project}/locations/{location}/models/{model}@golden`
  // If no version ID or alias is specified, the "default" version will be
  // returned. The "default" version alias is created for the first version of
  // the model, and can be moved to other versions later on. There will be
  // exactly one default version.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];
}

// Request message for
// [ModelService.ListModels][google.cloud.aiplatform.v1beta1.ModelService.ListModels].
message ListModelsRequest {
  // Required. The resource name of the Location to list the Models from.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // An expression for filtering the results of the request. For field names
  // both snake_case and camelCase are supported.
  //
  //   * `model` supports = and !=. `model` represents the Model ID,
  //     i.e. the last segment of the Model's [resource
  //     name][google.cloud.aiplatform.v1beta1.Model.name].
  //   * `display_name` supports = and !=
  //   * `labels` supports general map functions that is:
  //     * `labels.key=value` - key:value equality
  //     * `labels.key:* or labels:key - key existence
  //     * A key including a space must be quoted. `labels."a key"`.
  //   * `base_model_name` only supports =
  //
  // Some examples:
  //
  //   * `model=1234`
  //   * `displayName="myDisplayName"`
  //   * `labels.myKey="myValue"`
  //   * `baseModelName="text-bison"`
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  // Typically obtained via
  // [ListModelsResponse.next_page_token][google.cloud.aiplatform.v1beta1.ListModelsResponse.next_page_token]
  // of the previous
  // [ModelService.ListModels][google.cloud.aiplatform.v1beta1.ModelService.ListModels]
  // call.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [ModelService.ListModels][google.cloud.aiplatform.v1beta1.ModelService.ListModels]
message ListModelsResponse {
  // List of Models in the requested page.
  repeated Model models = 1;

  // A token to retrieve next page of results.
  // Pass to
  // [ListModelsRequest.page_token][google.cloud.aiplatform.v1beta1.ListModelsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [ModelService.ListModelVersions][google.cloud.aiplatform.v1beta1.ModelService.ListModelVersions].
message ListModelVersionsRequest {
  // Required. The name of the model to list versions for.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];

  // The standard list page size.
  int32 page_size = 2;

  // The standard list page token.
  // Typically obtained via
  // [next_page_token][google.cloud.aiplatform.v1beta1.ListModelVersionsResponse.next_page_token]
  // of the previous
  // [ListModelVersions][google.cloud.aiplatform.v1beta1.ModelService.ListModelVersions]
  // call.
  string page_token = 3;

  // An expression for filtering the results of the request. For field names
  // both snake_case and camelCase are supported.
  //
  //   * `labels` supports general map functions that is:
  //     * `labels.key=value` - key:value equality
  //     * `labels.key:* or labels:key - key existence
  //     * A key including a space must be quoted. `labels."a key"`.
  //
  // Some examples:
  //
  //   * `labels.myKey="myValue"`
  string filter = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;

  // A comma-separated list of fields to order by, sorted in ascending order.
  // Use "desc" after a field name for descending.
  // Supported fields:
  //
  //   * `create_time`
  //   * `update_time`
  //
  // Example: `update_time asc, create_time desc`.
  string order_by = 6;
}

// Response message for
// [ModelService.ListModelVersions][google.cloud.aiplatform.v1beta1.ModelService.ListModelVersions]
message ListModelVersionsResponse {
  // List of Model versions in the requested page.
  // In the returned Model name field, version ID instead of regvision tag will
  // be included.
  repeated Model models = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListModelVersionsRequest.page_token][google.cloud.aiplatform.v1beta1.ListModelVersionsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [ModelService.UpdateModel][google.cloud.aiplatform.v1beta1.ModelService.UpdateModel].
message UpdateModelRequest {
  // Required. The Model which replaces the resource on the server.
  // When Model Versioning is enabled, the model.name will be used to determine
  // whether to update the model or model version.
  // 1. model.name with the @ value, e.g. models/123@1, refers to a version
  // specific update.
  // 2. model.name without the @ value, e.g. models/123, refers to a model
  // update.
  // 3. model.name with @-, e.g. models/123@-, refers to a model update.
  // 4. Supported model fields: display_name, description; supported
  // version-specific fields: version_description. Labels are supported in both
  // scenarios. Both the model labels and the version labels are merged when a
  // model is returned. When updating labels, if the request is for
  // model-specific update, model label gets updated. Otherwise, version labels
  // get updated.
  // 5. A model name or model version name fields update mismatch will cause a
  // precondition error.
  // 6. One request cannot update both the model and the version fields. You
  // must update them separately.
  Model model = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource.
  // For the `FieldMask` definition, see
  // [google.protobuf.FieldMask][google.protobuf.FieldMask].
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ModelService.UpdateExplanationDataset][google.cloud.aiplatform.v1beta1.ModelService.UpdateExplanationDataset].
message UpdateExplanationDatasetRequest {
  // Required. The resource name of the Model to update.
  // Format: `projects/{project}/locations/{location}/models/{model}`
  string model = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];

  // The example config containing the location of the dataset.
  Examples examples = 2;
}

// Runtime operation information for
// [ModelService.UpdateExplanationDataset][google.cloud.aiplatform.v1beta1.ModelService.UpdateExplanationDataset].
message UpdateExplanationDatasetOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [ModelService.DeleteModel][google.cloud.aiplatform.v1beta1.ModelService.DeleteModel].
message DeleteModelRequest {
  // Required. The name of the Model resource to be deleted.
  // Format: `projects/{project}/locations/{location}/models/{model}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];
}

// Request message for
// [ModelService.DeleteModelVersion][google.cloud.aiplatform.v1beta1.ModelService.DeleteModelVersion].
message DeleteModelVersionRequest {
  // Required. The name of the model version to be deleted, with a version ID
  // explicitly included.
  //
  // Example: `projects/{project}/locations/{location}/models/{model}@1234`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];
}

// Request message for
// [ModelService.MergeVersionAliases][google.cloud.aiplatform.v1beta1.ModelService.MergeVersionAliases].
message MergeVersionAliasesRequest {
  // Required. The name of the model version to merge aliases, with a version ID
  // explicitly included.
  //
  // Example: `projects/{project}/locations/{location}/models/{model}@1234`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];

  // Required. The set of version aliases to merge.
  // The alias should be at most 128 characters, and match
  // `[a-z][a-zA-Z0-9-]{0,126}[a-z-0-9]`.
  // Add the `-` prefix to an alias means removing that alias from the version.
  // `-` is NOT counted in the 128 characters. Example: `-golden` means removing
  // the `golden` alias from the version.
  //
  // There is NO ordering in aliases, which means
  // 1) The aliases returned from GetModel API might not have the exactly same
  // order from this MergeVersionAliases API. 2) Adding and deleting the same
  // alias in the request is not recommended, and the 2 operations will be
  // cancelled out.
  repeated string version_aliases = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ModelService.ExportModel][google.cloud.aiplatform.v1beta1.ModelService.ExportModel].
message ExportModelRequest {
  // Output configuration for the Model export.
  message OutputConfig {
    // The ID of the format in which the Model must be exported. Each Model
    // lists the [export formats it
    // supports][google.cloud.aiplatform.v1beta1.Model.supported_export_formats].
    // If no value is provided here, then the first from the list of the Model's
    // supported formats is used by default.
    string export_format_id = 1;

    // The Cloud Storage location where the Model artifact is to be
    // written to. Under the directory given as the destination a new one with
    // name "`model-export-<model-display-name>-<timestamp-of-export-call>`",
    // where timestamp is in YYYY-MM-DDThh:mm:ss.sssZ ISO-8601 format,
    // will be created. Inside, the Model and any of its supporting files
    // will be written.
    // This field should only be set when the `exportableContent` field of the
    // [Model.supported_export_formats] object contains `ARTIFACT`.
    GcsDestination artifact_destination = 3;

    // The Google Container Registry or Artifact Registry uri where the
    // Model container image will be copied to.
    // This field should only be set when the `exportableContent` field of the
    // [Model.supported_export_formats] object contains `IMAGE`.
    ContainerRegistryDestination image_destination = 4;
  }

  // Required. The resource name of the Model to export.
  // The resource name may contain version id or version alias to specify the
  // version, if no version is specified, the default version will be exported.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];

  // Required. The desired output location and configuration.
  OutputConfig output_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// Details of
// [ModelService.ExportModel][google.cloud.aiplatform.v1beta1.ModelService.ExportModel]
// operation.
message ExportModelOperationMetadata {
  // Further describes the output of the ExportModel. Supplements
  // [ExportModelRequest.OutputConfig][google.cloud.aiplatform.v1beta1.ExportModelRequest.OutputConfig].
  message OutputInfo {
    // Output only. If the Model artifact is being exported to Google Cloud
    // Storage this is the full path of the directory created, into which the
    // Model files are being written to.
    string artifact_output_uri = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. If the Model image is being exported to Google Container
    // Registry or Artifact Registry this is the full path of the image created.
    string image_output_uri = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;

  // Output only. Information further describing the output of this Model
  // export.
  OutputInfo output_info = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Response message of
// [ModelService.UpdateExplanationDataset][google.cloud.aiplatform.v1beta1.ModelService.UpdateExplanationDataset]
// operation.
message UpdateExplanationDatasetResponse {}

// Response message of
// [ModelService.ExportModel][google.cloud.aiplatform.v1beta1.ModelService.ExportModel]
// operation.
message ExportModelResponse {}

// Request message for
// [ModelService.CopyModel][google.cloud.aiplatform.v1beta1.ModelService.CopyModel].
message CopyModelRequest {
  // If both fields are unset, a new Model will be created with a generated ID.
  oneof destination_model {
    // Optional. Copy source_model into a new Model with this ID. The ID will
    // become the final component of the model resource name.
    //
    // This value may be up to 63 characters, and valid characters are
    // `[a-z0-9_-]`. The first character cannot be a number or hyphen.
    string model_id = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Specify this field to copy source_model into this existing
    // Model as a new version. Format:
    // `projects/{project}/locations/{location}/models/{model}`
    string parent_model = 5 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "aiplatform.googleapis.com/Model"
      }
    ];
  }

  // Required. The resource name of the Location into which to copy the Model.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The resource name of the Model to copy. That Model must be in the
  // same Project. Format:
  // `projects/{project}/locations/{location}/models/{model}`
  string source_model = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];

  // Customer-managed encryption key options. If this is set,
  // then the Model copy will be encrypted with the provided encryption key.
  EncryptionSpec encryption_spec = 3;
}

// Details of
// [ModelService.CopyModel][google.cloud.aiplatform.v1beta1.ModelService.CopyModel]
// operation.
message CopyModelOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}

// Response message of
// [ModelService.CopyModel][google.cloud.aiplatform.v1beta1.ModelService.CopyModel]
// operation.
message CopyModelResponse {
  // The name of the copied Model resource.
  // Format: `projects/{project}/locations/{location}/models/{model}`
  string model = 1 [(google.api.resource_reference) = {
    type: "aiplatform.googleapis.com/Model"
  }];

  // Output only. The version ID of the model that is copied.
  string model_version_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [ModelService.ImportModelEvaluation][google.cloud.aiplatform.v1beta1.ModelService.ImportModelEvaluation]
message ImportModelEvaluationRequest {
  // Required. The name of the parent model resource.
  // Format: `projects/{project}/locations/{location}/models/{model}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];

  // Required. Model evaluation resource to be imported.
  ModelEvaluation model_evaluation = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ModelService.BatchImportModelEvaluationSlices][google.cloud.aiplatform.v1beta1.ModelService.BatchImportModelEvaluationSlices]
message BatchImportModelEvaluationSlicesRequest {
  // Required. The name of the parent ModelEvaluation resource.
  // Format:
  // `projects/{project}/locations/{location}/models/{model}/evaluations/{evaluation}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelEvaluation"
    }
  ];

  // Required. Model evaluation slice resource to be imported.
  repeated ModelEvaluationSlice model_evaluation_slices = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [ModelService.BatchImportModelEvaluationSlices][google.cloud.aiplatform.v1beta1.ModelService.BatchImportModelEvaluationSlices]
message BatchImportModelEvaluationSlicesResponse {
  // Output only. List of imported
  // [ModelEvaluationSlice.name][google.cloud.aiplatform.v1beta1.ModelEvaluationSlice.name].
  repeated string imported_model_evaluation_slices = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [ModelService.BatchImportEvaluatedAnnotations][google.cloud.aiplatform.v1beta1.ModelService.BatchImportEvaluatedAnnotations]
message BatchImportEvaluatedAnnotationsRequest {
  // Required. The name of the parent ModelEvaluationSlice resource.
  // Format:
  // `projects/{project}/locations/{location}/models/{model}/evaluations/{evaluation}/slices/{slice}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelEvaluationSlice"
    }
  ];

  // Required. Evaluated annotations resource to be imported.
  repeated EvaluatedAnnotation evaluated_annotations = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [ModelService.BatchImportEvaluatedAnnotations][google.cloud.aiplatform.v1beta1.ModelService.BatchImportEvaluatedAnnotations]
message BatchImportEvaluatedAnnotationsResponse {
  // Output only. Number of EvaluatedAnnotations imported.
  int32 imported_evaluated_annotations_count = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [ModelService.GetModelEvaluation][google.cloud.aiplatform.v1beta1.ModelService.GetModelEvaluation].
message GetModelEvaluationRequest {
  // Required. The name of the ModelEvaluation resource.
  // Format:
  // `projects/{project}/locations/{location}/models/{model}/evaluations/{evaluation}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelEvaluation"
    }
  ];
}

// Request message for
// [ModelService.ListModelEvaluations][google.cloud.aiplatform.v1beta1.ModelService.ListModelEvaluations].
message ListModelEvaluationsRequest {
  // Required. The resource name of the Model to list the ModelEvaluations from.
  // Format: `projects/{project}/locations/{location}/models/{model}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Model"
    }
  ];

  // The standard list filter.
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  // Typically obtained via
  // [ListModelEvaluationsResponse.next_page_token][google.cloud.aiplatform.v1beta1.ListModelEvaluationsResponse.next_page_token]
  // of the previous
  // [ModelService.ListModelEvaluations][google.cloud.aiplatform.v1beta1.ModelService.ListModelEvaluations]
  // call.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [ModelService.ListModelEvaluations][google.cloud.aiplatform.v1beta1.ModelService.ListModelEvaluations].
message ListModelEvaluationsResponse {
  // List of ModelEvaluations in the requested page.
  repeated ModelEvaluation model_evaluations = 1;

  // A token to retrieve next page of results.
  // Pass to
  // [ListModelEvaluationsRequest.page_token][google.cloud.aiplatform.v1beta1.ListModelEvaluationsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [ModelService.GetModelEvaluationSlice][google.cloud.aiplatform.v1beta1.ModelService.GetModelEvaluationSlice].
message GetModelEvaluationSliceRequest {
  // Required. The name of the ModelEvaluationSlice resource.
  // Format:
  // `projects/{project}/locations/{location}/models/{model}/evaluations/{evaluation}/slices/{slice}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelEvaluationSlice"
    }
  ];
}

// Request message for
// [ModelService.ListModelEvaluationSlices][google.cloud.aiplatform.v1beta1.ModelService.ListModelEvaluationSlices].
message ListModelEvaluationSlicesRequest {
  // Required. The resource name of the ModelEvaluation to list the
  // ModelEvaluationSlices from. Format:
  // `projects/{project}/locations/{location}/models/{model}/evaluations/{evaluation}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ModelEvaluation"
    }
  ];

  // The standard list filter.
  //
  //   * `slice.dimension` - for =.
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  // Typically obtained via
  // [ListModelEvaluationSlicesResponse.next_page_token][google.cloud.aiplatform.v1beta1.ListModelEvaluationSlicesResponse.next_page_token]
  // of the previous
  // [ModelService.ListModelEvaluationSlices][google.cloud.aiplatform.v1beta1.ModelService.ListModelEvaluationSlices]
  // call.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [ModelService.ListModelEvaluationSlices][google.cloud.aiplatform.v1beta1.ModelService.ListModelEvaluationSlices].
message ListModelEvaluationSlicesResponse {
  // List of ModelEvaluations in the requested page.
  repeated ModelEvaluationSlice model_evaluation_slices = 1;

  // A token to retrieve next page of results.
  // Pass to
  // [ListModelEvaluationSlicesRequest.page_token][google.cloud.aiplatform.v1beta1.ListModelEvaluationSlicesRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}
