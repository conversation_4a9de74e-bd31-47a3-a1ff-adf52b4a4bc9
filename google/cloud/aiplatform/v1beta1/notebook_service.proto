// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/notebook_execution_job.proto";
import "google/cloud/aiplatform/v1beta1/notebook_runtime.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "NotebookServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// The interface for Vertex Notebook service (a.k.a. Colab on Workbench).
service NotebookService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a NotebookRuntimeTemplate.
  rpc CreateNotebookRuntimeTemplate(CreateNotebookRuntimeTemplateRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/notebookRuntimeTemplates"
      body: "notebook_runtime_template"
    };
    option (google.api.method_signature) =
        "parent,notebook_runtime_template,notebook_runtime_template_id";
    option (google.longrunning.operation_info) = {
      response_type: "NotebookRuntimeTemplate"
      metadata_type: "CreateNotebookRuntimeTemplateOperationMetadata"
    };
  }

  // Gets a NotebookRuntimeTemplate.
  rpc GetNotebookRuntimeTemplate(GetNotebookRuntimeTemplateRequest)
      returns (NotebookRuntimeTemplate) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/notebookRuntimeTemplates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists NotebookRuntimeTemplates in a Location.
  rpc ListNotebookRuntimeTemplates(ListNotebookRuntimeTemplatesRequest)
      returns (ListNotebookRuntimeTemplatesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/notebookRuntimeTemplates"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a NotebookRuntimeTemplate.
  rpc DeleteNotebookRuntimeTemplate(DeleteNotebookRuntimeTemplateRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/notebookRuntimeTemplates/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Updates a NotebookRuntimeTemplate.
  rpc UpdateNotebookRuntimeTemplate(UpdateNotebookRuntimeTemplateRequest)
      returns (NotebookRuntimeTemplate) {
    option (google.api.http) = {
      patch: "/v1beta1/{notebook_runtime_template.name=projects/*/locations/*/notebookRuntimeTemplates/*}"
      body: "notebook_runtime_template"
    };
    option (google.api.method_signature) =
        "notebook_runtime_template,update_mask";
  }

  // Assigns a NotebookRuntime to a user for a particular Notebook file. This
  // method will either returns an existing assignment or generates a new one.
  rpc AssignNotebookRuntime(AssignNotebookRuntimeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/notebookRuntimes:assign"
      body: "*"
    };
    option (google.api.method_signature) =
        "parent,notebook_runtime_template,notebook_runtime,notebook_runtime_id";
    option (google.longrunning.operation_info) = {
      response_type: "NotebookRuntime"
      metadata_type: "AssignNotebookRuntimeOperationMetadata"
    };
  }

  // Gets a NotebookRuntime.
  rpc GetNotebookRuntime(GetNotebookRuntimeRequest) returns (NotebookRuntime) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/notebookRuntimes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists NotebookRuntimes in a Location.
  rpc ListNotebookRuntimes(ListNotebookRuntimesRequest)
      returns (ListNotebookRuntimesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/notebookRuntimes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a NotebookRuntime.
  rpc DeleteNotebookRuntime(DeleteNotebookRuntimeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/notebookRuntimes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Upgrades a NotebookRuntime.
  rpc UpgradeNotebookRuntime(UpgradeNotebookRuntimeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/notebookRuntimes/*}:upgrade"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "UpgradeNotebookRuntimeResponse"
      metadata_type: "UpgradeNotebookRuntimeOperationMetadata"
    };
  }

  // Starts a NotebookRuntime.
  rpc StartNotebookRuntime(StartNotebookRuntimeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/notebookRuntimes/*}:start"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "StartNotebookRuntimeResponse"
      metadata_type: "StartNotebookRuntimeOperationMetadata"
    };
  }

  // Stops a NotebookRuntime.
  rpc StopNotebookRuntime(StopNotebookRuntimeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/notebookRuntimes/*}:stop"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "StopNotebookRuntimeResponse"
      metadata_type: "StopNotebookRuntimeOperationMetadata"
    };
  }

  // Creates a NotebookExecutionJob.
  rpc CreateNotebookExecutionJob(CreateNotebookExecutionJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/notebookExecutionJobs"
      body: "notebook_execution_job"
    };
    option (google.api.method_signature) =
        "parent,notebook_execution_job,notebook_execution_job_id";
    option (google.longrunning.operation_info) = {
      response_type: "NotebookExecutionJob"
      metadata_type: "CreateNotebookExecutionJobOperationMetadata"
    };
  }

  // Gets a NotebookExecutionJob.
  rpc GetNotebookExecutionJob(GetNotebookExecutionJobRequest)
      returns (NotebookExecutionJob) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/notebookExecutionJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists NotebookExecutionJobs in a Location.
  rpc ListNotebookExecutionJobs(ListNotebookExecutionJobsRequest)
      returns (ListNotebookExecutionJobsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/notebookExecutionJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a NotebookExecutionJob.
  rpc DeleteNotebookExecutionJob(DeleteNotebookExecutionJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/notebookExecutionJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }
}

// Views for Get/List NotebookExecutionJob
enum NotebookExecutionJobView {
  // When unspecified, the API defaults to the BASIC view.
  NOTEBOOK_EXECUTION_JOB_VIEW_UNSPECIFIED = 0;

  // Includes all fields except for direct notebook inputs.
  NOTEBOOK_EXECUTION_JOB_VIEW_BASIC = 1;

  // Includes all fields.
  NOTEBOOK_EXECUTION_JOB_VIEW_FULL = 2;
}

// Request message for
// [NotebookService.CreateNotebookRuntimeTemplate][google.cloud.aiplatform.v1beta1.NotebookService.CreateNotebookRuntimeTemplate].
message CreateNotebookRuntimeTemplateRequest {
  // Required. The resource name of the Location to create the
  // NotebookRuntimeTemplate. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The NotebookRuntimeTemplate to create.
  NotebookRuntimeTemplate notebook_runtime_template = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. User specified ID for the notebook runtime template.
  string notebook_runtime_template_id = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Metadata information for
// [NotebookService.CreateNotebookRuntimeTemplate][google.cloud.aiplatform.v1beta1.NotebookService.CreateNotebookRuntimeTemplate].
message CreateNotebookRuntimeTemplateOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [NotebookService.GetNotebookRuntimeTemplate][google.cloud.aiplatform.v1beta1.NotebookService.GetNotebookRuntimeTemplate]
message GetNotebookRuntimeTemplateRequest {
  // Required. The name of the NotebookRuntimeTemplate resource.
  // Format:
  // `projects/{project}/locations/{location}/notebookRuntimeTemplates/{notebook_runtime_template}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NotebookRuntimeTemplate"
    }
  ];
}

// Request message for
// [NotebookService.ListNotebookRuntimeTemplates][google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimeTemplates].
message ListNotebookRuntimeTemplatesRequest {
  // Required. The resource name of the Location from which to list the
  // NotebookRuntimeTemplates.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. An expression for filtering the results of the request. For field
  // names both snake_case and camelCase are supported.
  //
  //   * `notebookRuntimeTemplate` supports = and !=. `notebookRuntimeTemplate`
  //     represents the NotebookRuntimeTemplate ID,
  //     i.e. the last segment of the NotebookRuntimeTemplate's [resource name]
  //     [google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate.name].
  //   * `display_name` supports = and !=
  //   * `labels` supports general map functions that is:
  //     * `labels.key=value` - key:value equality
  //     * `labels.key:* or labels:key - key existence
  //     * A key including a space must be quoted. `labels."a key"`.
  //   * `notebookRuntimeType` supports = and !=. notebookRuntimeType enum:
  //   [USER_DEFINED, ONE_CLICK].
  //
  // Some examples:
  //
  //   * `notebookRuntimeTemplate=notebookRuntimeTemplate123`
  //   * `displayName="myDisplayName"`
  //   * `labels.myKey="myValue"`
  //   * `notebookRuntimeType=USER_DEFINED`
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  // Typically obtained via
  // [ListNotebookRuntimeTemplatesResponse.next_page_token][google.cloud.aiplatform.v1beta1.ListNotebookRuntimeTemplatesResponse.next_page_token]
  // of the previous
  // [NotebookService.ListNotebookRuntimeTemplates][google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimeTemplates]
  // call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A comma-separated list of fields to order by, sorted in ascending
  // order. Use "desc" after a field name for descending. Supported fields:
  //
  //   * `display_name`
  //   * `create_time`
  //   * `update_time`
  //
  // Example: `display_name, create_time desc`.
  string order_by = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [NotebookService.ListNotebookRuntimeTemplates][google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimeTemplates].
message ListNotebookRuntimeTemplatesResponse {
  // List of NotebookRuntimeTemplates in the requested page.
  repeated NotebookRuntimeTemplate notebook_runtime_templates = 1;

  // A token to retrieve next page of results.
  // Pass to
  // [ListNotebookRuntimeTemplatesRequest.page_token][google.cloud.aiplatform.v1beta1.ListNotebookRuntimeTemplatesRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [NotebookService.DeleteNotebookRuntimeTemplate][google.cloud.aiplatform.v1beta1.NotebookService.DeleteNotebookRuntimeTemplate].
message DeleteNotebookRuntimeTemplateRequest {
  // Required. The name of the NotebookRuntimeTemplate resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/notebookRuntimeTemplates/{notebook_runtime_template}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NotebookRuntimeTemplate"
    }
  ];
}

// Request message for
// [NotebookService.UpdateNotebookRuntimeTemplate][google.cloud.aiplatform.v1beta1.NotebookService.UpdateNotebookRuntimeTemplate].
message UpdateNotebookRuntimeTemplateRequest {
  // Required. The NotebookRuntimeTemplate to update.
  NotebookRuntimeTemplate notebook_runtime_template = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource.
  // For the `FieldMask` definition, see
  // [google.protobuf.FieldMask][google.protobuf.FieldMask]. Input format:
  // `{paths: "${updated_filed}"}` Updatable fields:
  //
  //   * `encryption_spec.kms_key_name`
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [NotebookService.AssignNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.AssignNotebookRuntime].
message AssignNotebookRuntimeRequest {
  // Required. The resource name of the Location to get the NotebookRuntime
  // assignment. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The resource name of the NotebookRuntimeTemplate based on which a
  // NotebookRuntime will be assigned (reuse or create a new one).
  string notebook_runtime_template = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NotebookRuntimeTemplate"
    }
  ];

  // Required. Provide runtime specific information (e.g. runtime owner,
  // notebook id) used for NotebookRuntime assignment.
  NotebookRuntime notebook_runtime = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. User specified ID for the notebook runtime.
  string notebook_runtime_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Metadata information for
// [NotebookService.AssignNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.AssignNotebookRuntime].
message AssignNotebookRuntimeOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;

  // A human-readable message that shows the intermediate progress details of
  // NotebookRuntime.
  string progress_message = 2;
}

// Request message for
// [NotebookService.GetNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.GetNotebookRuntime]
message GetNotebookRuntimeRequest {
  // Required. The name of the NotebookRuntime resource.
  // Instead of checking whether the name is in valid NotebookRuntime resource
  // name format, directly throw NotFound exception if there is no such
  // NotebookRuntime in spanner.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NotebookRuntime"
    }
  ];
}

// Request message for
// [NotebookService.ListNotebookRuntimes][google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimes].
message ListNotebookRuntimesRequest {
  // Required. The resource name of the Location from which to list the
  // NotebookRuntimes.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. An expression for filtering the results of the request. For field
  // names both snake_case and camelCase are supported.
  //
  //   * `notebookRuntime` supports = and !=. `notebookRuntime` represents the
  //     NotebookRuntime ID,
  //     i.e. the last segment of the NotebookRuntime's [resource name]
  //     [google.cloud.aiplatform.v1beta1.NotebookRuntime.name].
  //   * `displayName` supports = and != and regex.
  //   * `notebookRuntimeTemplate` supports = and !=. `notebookRuntimeTemplate`
  //     represents the NotebookRuntimeTemplate ID,
  //     i.e. the last segment of the NotebookRuntimeTemplate's [resource name]
  //     [google.cloud.aiplatform.v1beta1.NotebookRuntimeTemplate.name].
  //   * `healthState` supports = and !=. healthState enum: [HEALTHY, UNHEALTHY,
  //   HEALTH_STATE_UNSPECIFIED].
  //   * `runtimeState` supports = and !=. runtimeState enum:
  //   [RUNTIME_STATE_UNSPECIFIED, RUNNING, BEING_STARTED, BEING_STOPPED,
  //   STOPPED, BEING_UPGRADED, ERROR, INVALID].
  //   * `runtimeUser` supports = and !=.
  //   * API version is UI only: `uiState` supports = and !=. uiState enum:
  //   [UI_RESOURCE_STATE_UNSPECIFIED, UI_RESOURCE_STATE_BEING_CREATED,
  //   UI_RESOURCE_STATE_ACTIVE, UI_RESOURCE_STATE_BEING_DELETED,
  //   UI_RESOURCE_STATE_CREATION_FAILED].
  //   * `notebookRuntimeType` supports = and !=. notebookRuntimeType enum:
  //   [USER_DEFINED, ONE_CLICK].
  //
  // Some examples:
  //
  //   * `notebookRuntime="notebookRuntime123"`
  //   * `displayName="myDisplayName"` and `displayName=~"myDisplayNameRegex"`
  //   * `notebookRuntimeTemplate="notebookRuntimeTemplate321"`
  //   * `healthState=HEALTHY`
  //   * `runtimeState=RUNNING`
  //   * `runtimeUser="<EMAIL>"`
  //   * `uiState=UI_RESOURCE_STATE_BEING_DELETED`
  //   * `notebookRuntimeType=USER_DEFINED`
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  // Typically obtained via
  // [ListNotebookRuntimesResponse.next_page_token][google.cloud.aiplatform.v1beta1.ListNotebookRuntimesResponse.next_page_token]
  // of the previous
  // [NotebookService.ListNotebookRuntimes][google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimes]
  // call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A comma-separated list of fields to order by, sorted in ascending
  // order. Use "desc" after a field name for descending. Supported fields:
  //
  //   * `display_name`
  //   * `create_time`
  //   * `update_time`
  //
  // Example: `display_name, create_time desc`.
  string order_by = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [NotebookService.ListNotebookRuntimes][google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookRuntimes].
message ListNotebookRuntimesResponse {
  // List of NotebookRuntimes in the requested page.
  repeated NotebookRuntime notebook_runtimes = 1;

  // A token to retrieve next page of results.
  // Pass to
  // [ListNotebookRuntimesRequest.page_token][google.cloud.aiplatform.v1beta1.ListNotebookRuntimesRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [NotebookService.DeleteNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.DeleteNotebookRuntime].
message DeleteNotebookRuntimeRequest {
  // Required. The name of the NotebookRuntime resource to be deleted.
  // Instead of checking whether the name is in valid NotebookRuntime resource
  // name format, directly throw NotFound exception if there is no such
  // NotebookRuntime in spanner.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NotebookRuntime"
    }
  ];
}

// Request message for
// [NotebookService.UpgradeNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.UpgradeNotebookRuntime].
message UpgradeNotebookRuntimeRequest {
  // Required. The name of the NotebookRuntime resource to be upgrade.
  // Instead of checking whether the name is in valid NotebookRuntime resource
  // name format, directly throw NotFound exception if there is no such
  // NotebookRuntime in spanner.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NotebookRuntime"
    }
  ];
}

// Metadata information for
// [NotebookService.UpgradeNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.UpgradeNotebookRuntime].
message UpgradeNotebookRuntimeOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;

  // A human-readable message that shows the intermediate progress details of
  // NotebookRuntime.
  string progress_message = 2;
}

// Response message for
// [NotebookService.UpgradeNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.UpgradeNotebookRuntime].
message UpgradeNotebookRuntimeResponse {}

// Request message for
// [NotebookService.StartNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.StartNotebookRuntime].
message StartNotebookRuntimeRequest {
  // Required. The name of the NotebookRuntime resource to be started.
  // Instead of checking whether the name is in valid NotebookRuntime resource
  // name format, directly throw NotFound exception if there is no such
  // NotebookRuntime in spanner.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NotebookRuntime"
    }
  ];
}

// Metadata information for
// [NotebookService.StartNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.StartNotebookRuntime].
message StartNotebookRuntimeOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;

  // A human-readable message that shows the intermediate progress details of
  // NotebookRuntime.
  string progress_message = 2;
}

// Response message for
// [NotebookService.StartNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.StartNotebookRuntime].
message StartNotebookRuntimeResponse {}

// Request message for
// [NotebookService.StopNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.StopNotebookRuntime].
message StopNotebookRuntimeRequest {
  // Required. The name of the NotebookRuntime resource to be stopped.
  // Instead of checking whether the name is in valid NotebookRuntime resource
  // name format, directly throw NotFound exception if there is no such
  // NotebookRuntime in spanner.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NotebookRuntime"
    }
  ];
}

// Metadata information for
// [NotebookService.StopNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.StopNotebookRuntime].
message StopNotebookRuntimeOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Response message for
// [NotebookService.StopNotebookRuntime][google.cloud.aiplatform.v1beta1.NotebookService.StopNotebookRuntime].
message StopNotebookRuntimeResponse {}

// Request message for [NotebookService.CreateNotebookExecutionJob]
message CreateNotebookExecutionJobRequest {
  // Required. The resource name of the Location to create the
  // NotebookExecutionJob. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The NotebookExecutionJob to create.
  NotebookExecutionJob notebook_execution_job = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. User specified ID for the NotebookExecutionJob.
  string notebook_execution_job_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Metadata information for
// [NotebookService.CreateNotebookExecutionJob][google.cloud.aiplatform.v1beta1.NotebookService.CreateNotebookExecutionJob].
message CreateNotebookExecutionJobOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;

  // A human-readable message that shows the intermediate progress details of
  // NotebookRuntime.
  string progress_message = 2;
}

// Request message for [NotebookService.GetNotebookExecutionJob]
message GetNotebookExecutionJobRequest {
  // Required. The name of the NotebookExecutionJob resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NotebookExecutionJob"
    }
  ];

  // Optional. The NotebookExecutionJob view. Defaults to BASIC.
  NotebookExecutionJobView view = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for [NotebookService.ListNotebookExecutionJobs]
message ListNotebookExecutionJobsRequest {
  // Required. The resource name of the Location from which to list the
  // NotebookExecutionJobs.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. An expression for filtering the results of the request. For field
  // names both snake_case and camelCase are supported.
  //
  //   * `notebookExecutionJob` supports = and !=. `notebookExecutionJob`
  //   represents the NotebookExecutionJob ID.
  //   * `displayName` supports = and != and regex.
  //   * `schedule` supports = and != and regex.
  //
  // Some examples:
  //   * `notebookExecutionJob="123"`
  //   * `notebookExecutionJob="my-execution-job"`
  //   * `displayName="myDisplayName"` and `displayName=~"myDisplayNameRegex"`
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  // Typically obtained via
  // [ListNotebookExecutionJobs.next_page_token][] of the previous
  // [NotebookService.ListNotebookExecutionJobs][google.cloud.aiplatform.v1beta1.NotebookService.ListNotebookExecutionJobs]
  // call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A comma-separated list of fields to order by, sorted in ascending
  // order. Use "desc" after a field name for descending. Supported fields:
  //
  //   * `display_name`
  //   * `create_time`
  //   * `update_time`
  //
  // Example: `display_name, create_time desc`.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The NotebookExecutionJob view. Defaults to BASIC.
  NotebookExecutionJobView view = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for [NotebookService.CreateNotebookExecutionJob]
message ListNotebookExecutionJobsResponse {
  // List of NotebookExecutionJobs in the requested page.
  repeated NotebookExecutionJob notebook_execution_jobs = 1;

  // A token to retrieve next page of results.
  // Pass to [ListNotebookExecutionJobs.page_token][] to obtain that
  // page.
  string next_page_token = 2;
}

// Request message for [NotebookService.DeleteNotebookExecutionJob]
message DeleteNotebookExecutionJobRequest {
  // Required. The name of the NotebookExecutionJob resource to be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/NotebookExecutionJob"
    }
  ];
}
