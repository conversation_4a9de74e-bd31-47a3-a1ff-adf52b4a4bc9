// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/user_action_reference.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "AnnotationProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Used to assign specific AnnotationSpec to a particular area of a DataItem or
// the whole part of the DataItem.
message Annotation {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/Annotation"
    pattern: "projects/{project}/locations/{location}/datasets/{dataset}/dataItems/{data_item}/annotations/{annotation}"
  };

  // Output only. Resource name of the Annotation.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Google Cloud Storage URI points to a YAML file describing
  // [payload][google.cloud.aiplatform.v1beta1.Annotation.payload]. The schema
  // is defined as an [OpenAPI 3.0.2 Schema
  // Object](https://github.com/OAI/OpenAPI-Specification/blob/main/versions/3.0.2.md#schemaObject).
  // The schema files that can be used here are found in
  // gs://google-cloud-aiplatform/schema/dataset/annotation/, note that the
  // chosen schema must be consistent with the parent Dataset's
  // [metadata][google.cloud.aiplatform.v1beta1.Dataset.metadata_schema_uri].
  string payload_schema_uri = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The schema of the payload can be found in
  // [payload_schema][google.cloud.aiplatform.v1beta1.Annotation.payload_schema_uri].
  google.protobuf.Value payload = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp when this Annotation was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Annotation was last updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Used to perform consistent read-modify-write updates. If not set,
  // a blind "overwrite" update happens.
  string etag = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The source of the Annotation.
  UserActionReference annotation_source = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The labels with user-defined metadata to organize your
  // Annotations.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one Annotation(System
  // labels are excluded).
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with "aiplatform.googleapis.com/"
  // and are immutable. Following system labels exist for each Annotation:
  //
  // * "aiplatform.googleapis.com/annotation_set_name":
  //   optional, name of the UI's annotation set this Annotation belongs to.
  //   If not set, the Annotation is not visible in the UI.
  //
  // * "aiplatform.googleapis.com/payload_schema":
  //   output only, its value is the
  //   [payload_schema's][google.cloud.aiplatform.v1beta1.Annotation.payload_schema_uri]
  //   title.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];
}
