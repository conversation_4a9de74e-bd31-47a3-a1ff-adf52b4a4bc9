// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/artifact.proto";
import "google/cloud/aiplatform/v1beta1/context.proto";
import "google/cloud/aiplatform/v1beta1/event.proto";
import "google/cloud/aiplatform/v1beta1/execution.proto";
import "google/cloud/aiplatform/v1beta1/lineage_subgraph.proto";
import "google/cloud/aiplatform/v1beta1/metadata_schema.proto";
import "google/cloud/aiplatform/v1beta1/metadata_store.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "MetadataServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Service for reading and writing metadata entries.
service MetadataService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Initializes a MetadataStore, including allocation of resources.
  rpc CreateMetadataStore(CreateMetadataStoreRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/metadataStores"
      body: "metadata_store"
    };
    option (google.api.method_signature) =
        "parent,metadata_store,metadata_store_id";
    option (google.longrunning.operation_info) = {
      response_type: "MetadataStore"
      metadata_type: "CreateMetadataStoreOperationMetadata"
    };
  }

  // Retrieves a specific MetadataStore.
  rpc GetMetadataStore(GetMetadataStoreRequest) returns (MetadataStore) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/metadataStores/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists MetadataStores for a Location.
  rpc ListMetadataStores(ListMetadataStoresRequest)
      returns (ListMetadataStoresResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/metadataStores"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a single MetadataStore and all its child resources (Artifacts,
  // Executions, and Contexts).
  rpc DeleteMetadataStore(DeleteMetadataStoreRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/metadataStores/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteMetadataStoreOperationMetadata"
    };
  }

  // Creates an Artifact associated with a MetadataStore.
  rpc CreateArtifact(CreateArtifactRequest) returns (Artifact) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/artifacts"
      body: "artifact"
    };
    option (google.api.method_signature) = "parent,artifact,artifact_id";
  }

  // Retrieves a specific Artifact.
  rpc GetArtifact(GetArtifactRequest) returns (Artifact) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/metadataStores/*/artifacts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Artifacts in the MetadataStore.
  rpc ListArtifacts(ListArtifactsRequest) returns (ListArtifactsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/artifacts"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a stored Artifact.
  rpc UpdateArtifact(UpdateArtifactRequest) returns (Artifact) {
    option (google.api.http) = {
      patch: "/v1beta1/{artifact.name=projects/*/locations/*/metadataStores/*/artifacts/*}"
      body: "artifact"
    };
    option (google.api.method_signature) = "artifact,update_mask";
  }

  // Deletes an Artifact.
  rpc DeleteArtifact(DeleteArtifactRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/metadataStores/*/artifacts/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Purges Artifacts.
  rpc PurgeArtifacts(PurgeArtifactsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/artifacts:purge"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "PurgeArtifactsResponse"
      metadata_type: "PurgeArtifactsMetadata"
    };
  }

  // Creates a Context associated with a MetadataStore.
  rpc CreateContext(CreateContextRequest) returns (Context) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/contexts"
      body: "context"
    };
    option (google.api.method_signature) = "parent,context,context_id";
  }

  // Retrieves a specific Context.
  rpc GetContext(GetContextRequest) returns (Context) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/metadataStores/*/contexts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Contexts on the MetadataStore.
  rpc ListContexts(ListContextsRequest) returns (ListContextsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/contexts"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a stored Context.
  rpc UpdateContext(UpdateContextRequest) returns (Context) {
    option (google.api.http) = {
      patch: "/v1beta1/{context.name=projects/*/locations/*/metadataStores/*/contexts/*}"
      body: "context"
    };
    option (google.api.method_signature) = "context,update_mask";
  }

  // Deletes a stored Context.
  rpc DeleteContext(DeleteContextRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/metadataStores/*/contexts/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Purges Contexts.
  rpc PurgeContexts(PurgeContextsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/contexts:purge"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "PurgeContextsResponse"
      metadata_type: "PurgeContextsMetadata"
    };
  }

  // Adds a set of Artifacts and Executions to a Context. If any of the
  // Artifacts or Executions have already been added to a Context, they are
  // simply skipped.
  rpc AddContextArtifactsAndExecutions(AddContextArtifactsAndExecutionsRequest)
      returns (AddContextArtifactsAndExecutionsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{context=projects/*/locations/*/metadataStores/*/contexts/*}:addContextArtifactsAndExecutions"
      body: "*"
    };
    option (google.api.method_signature) = "context,artifacts,executions";
  }

  // Adds a set of Contexts as children to a parent Context. If any of the
  // child Contexts have already been added to the parent Context, they are
  // simply skipped. If this call would create a cycle or cause any Context to
  // have more than 10 parents, the request will fail with an INVALID_ARGUMENT
  // error.
  rpc AddContextChildren(AddContextChildrenRequest)
      returns (AddContextChildrenResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{context=projects/*/locations/*/metadataStores/*/contexts/*}:addContextChildren"
      body: "*"
    };
    option (google.api.method_signature) = "context,child_contexts";
  }

  // Remove a set of children contexts from a parent Context. If any of the
  // child Contexts were NOT added to the parent Context, they are
  // simply skipped.
  rpc RemoveContextChildren(RemoveContextChildrenRequest)
      returns (RemoveContextChildrenResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{context=projects/*/locations/*/metadataStores/*/contexts/*}:removeContextChildren"
      body: "*"
    };
    option (google.api.method_signature) = "context,child_contexts";
  }

  // Retrieves Artifacts and Executions within the specified Context, connected
  // by Event edges and returned as a LineageSubgraph.
  rpc QueryContextLineageSubgraph(QueryContextLineageSubgraphRequest)
      returns (LineageSubgraph) {
    option (google.api.http) = {
      get: "/v1beta1/{context=projects/*/locations/*/metadataStores/*/contexts/*}:queryContextLineageSubgraph"
    };
    option (google.api.method_signature) = "context";
  }

  // Creates an Execution associated with a MetadataStore.
  rpc CreateExecution(CreateExecutionRequest) returns (Execution) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/executions"
      body: "execution"
    };
    option (google.api.method_signature) = "parent,execution,execution_id";
  }

  // Retrieves a specific Execution.
  rpc GetExecution(GetExecutionRequest) returns (Execution) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/metadataStores/*/executions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Executions in the MetadataStore.
  rpc ListExecutions(ListExecutionsRequest) returns (ListExecutionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/executions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a stored Execution.
  rpc UpdateExecution(UpdateExecutionRequest) returns (Execution) {
    option (google.api.http) = {
      patch: "/v1beta1/{execution.name=projects/*/locations/*/metadataStores/*/executions/*}"
      body: "execution"
    };
    option (google.api.method_signature) = "execution,update_mask";
  }

  // Deletes an Execution.
  rpc DeleteExecution(DeleteExecutionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/metadataStores/*/executions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Purges Executions.
  rpc PurgeExecutions(PurgeExecutionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/executions:purge"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "PurgeExecutionsResponse"
      metadata_type: "PurgeExecutionsMetadata"
    };
  }

  // Adds Events to the specified Execution. An Event indicates whether an
  // Artifact was used as an input or output for an Execution. If an Event
  // already exists between the Execution and the Artifact, the Event is
  // skipped.
  rpc AddExecutionEvents(AddExecutionEventsRequest)
      returns (AddExecutionEventsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{execution=projects/*/locations/*/metadataStores/*/executions/*}:addExecutionEvents"
      body: "*"
    };
    option (google.api.method_signature) = "execution,events";
  }

  // Obtains the set of input and output Artifacts for this Execution, in the
  // form of LineageSubgraph that also contains the Execution and connecting
  // Events.
  rpc QueryExecutionInputsAndOutputs(QueryExecutionInputsAndOutputsRequest)
      returns (LineageSubgraph) {
    option (google.api.http) = {
      get: "/v1beta1/{execution=projects/*/locations/*/metadataStores/*/executions/*}:queryExecutionInputsAndOutputs"
    };
    option (google.api.method_signature) = "execution";
  }

  // Creates a MetadataSchema.
  rpc CreateMetadataSchema(CreateMetadataSchemaRequest)
      returns (MetadataSchema) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/metadataSchemas"
      body: "metadata_schema"
    };
    option (google.api.method_signature) =
        "parent,metadata_schema,metadata_schema_id";
  }

  // Retrieves a specific MetadataSchema.
  rpc GetMetadataSchema(GetMetadataSchemaRequest) returns (MetadataSchema) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/metadataStores/*/metadataSchemas/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists MetadataSchemas.
  rpc ListMetadataSchemas(ListMetadataSchemasRequest)
      returns (ListMetadataSchemasResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/metadataStores/*}/metadataSchemas"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves lineage of an Artifact represented through Artifacts and
  // Executions connected by Event edges and returned as a LineageSubgraph.
  rpc QueryArtifactLineageSubgraph(QueryArtifactLineageSubgraphRequest)
      returns (LineageSubgraph) {
    option (google.api.http) = {
      get: "/v1beta1/{artifact=projects/*/locations/*/metadataStores/*/artifacts/*}:queryArtifactLineageSubgraph"
    };
    option (google.api.method_signature) = "artifact";
  }
}

// Request message for
// [MetadataService.CreateMetadataStore][google.cloud.aiplatform.v1beta1.MetadataService.CreateMetadataStore].
message CreateMetadataStoreRequest {
  // Required. The resource name of the Location where the MetadataStore should
  // be created.
  // Format: `projects/{project}/locations/{location}/`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The MetadataStore to create.
  MetadataStore metadata_store = 2 [(google.api.field_behavior) = REQUIRED];

  // The {metadatastore} portion of the resource name with the format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  // If not provided, the MetadataStore's ID will be a UUID generated by the
  // service.
  // Must be 4-128 characters in length. Valid characters are `/[a-z][0-9]-/`.
  // Must be unique across all MetadataStores in the parent Location.
  // (Otherwise the request will fail with ALREADY_EXISTS, or PERMISSION_DENIED
  // if the caller can't view the preexisting MetadataStore.)
  string metadata_store_id = 3;
}

// Details of operations that perform
// [MetadataService.CreateMetadataStore][google.cloud.aiplatform.v1beta1.MetadataService.CreateMetadataStore].
message CreateMetadataStoreOperationMetadata {
  // Operation metadata for creating a MetadataStore.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [MetadataService.GetMetadataStore][google.cloud.aiplatform.v1beta1.MetadataService.GetMetadataStore].
message GetMetadataStoreRequest {
  // Required. The resource name of the MetadataStore to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/MetadataStore"
    }
  ];
}

// Request message for
// [MetadataService.ListMetadataStores][google.cloud.aiplatform.v1beta1.MetadataService.ListMetadataStores].
message ListMetadataStoresRequest {
  // Required. The Location whose MetadataStores should be listed.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of Metadata Stores to return. The service may return
  // fewer.
  // Must be in range 1-1000, inclusive. Defaults to 100.
  int32 page_size = 2;

  // A page token, received from a previous
  // [MetadataService.ListMetadataStores][google.cloud.aiplatform.v1beta1.MetadataService.ListMetadataStores]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters must match the call that
  // provided the page token. (Otherwise the request will fail with
  // INVALID_ARGUMENT error.)
  string page_token = 3;
}

// Response message for
// [MetadataService.ListMetadataStores][google.cloud.aiplatform.v1beta1.MetadataService.ListMetadataStores].
message ListMetadataStoresResponse {
  // The MetadataStores found for the Location.
  repeated MetadataStore metadata_stores = 1;

  // A token, which can be sent as
  // [ListMetadataStoresRequest.page_token][google.cloud.aiplatform.v1beta1.ListMetadataStoresRequest.page_token]
  // to retrieve the next page. If this field is not populated, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [MetadataService.DeleteMetadataStore][google.cloud.aiplatform.v1beta1.MetadataService.DeleteMetadataStore].
message DeleteMetadataStoreRequest {
  // Required. The resource name of the MetadataStore to delete.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/MetadataStore"
    }
  ];

  // Deprecated: Field is no longer supported.
  bool force = 2 [deprecated = true];
}

// Details of operations that perform
// [MetadataService.DeleteMetadataStore][google.cloud.aiplatform.v1beta1.MetadataService.DeleteMetadataStore].
message DeleteMetadataStoreOperationMetadata {
  // Operation metadata for deleting a MetadataStore.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [MetadataService.CreateArtifact][google.cloud.aiplatform.v1beta1.MetadataService.CreateArtifact].
message CreateArtifactRequest {
  // Required. The resource name of the MetadataStore where the Artifact should
  // be created.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/MetadataStore"
    }
  ];

  // Required. The Artifact to create.
  Artifact artifact = 2 [(google.api.field_behavior) = REQUIRED];

  // The {artifact} portion of the resource name with the format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
  // If not provided, the Artifact's ID will be a UUID generated by the service.
  // Must be 4-128 characters in length. Valid characters are `/[a-z][0-9]-/`.
  // Must be unique across all Artifacts in the parent MetadataStore. (Otherwise
  // the request will fail with ALREADY_EXISTS, or PERMISSION_DENIED if the
  // caller can't view the preexisting Artifact.)
  string artifact_id = 3;
}

// Request message for
// [MetadataService.GetArtifact][google.cloud.aiplatform.v1beta1.MetadataService.GetArtifact].
message GetArtifactRequest {
  // Required. The resource name of the Artifact to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Artifact"
    }
  ];
}

// Request message for
// [MetadataService.ListArtifacts][google.cloud.aiplatform.v1beta1.MetadataService.ListArtifacts].
message ListArtifactsRequest {
  // Required. The MetadataStore whose Artifacts should be listed.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/Artifact"
    }
  ];

  // The maximum number of Artifacts to return. The service may return fewer.
  // Must be in range 1-1000, inclusive. Defaults to 100.
  int32 page_size = 2;

  // A page token, received from a previous
  // [MetadataService.ListArtifacts][google.cloud.aiplatform.v1beta1.MetadataService.ListArtifacts]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters must match the call that
  // provided the page token. (Otherwise the request will fail with
  // INVALID_ARGUMENT error.)
  string page_token = 3;

  // Filter specifying the boolean condition for the Artifacts to satisfy in
  // order to be part of the result set.
  // The syntax to define filter query is based on https://google.aip.dev/160.
  // The supported set of filters include the following:
  //
  // *   **Attribute filtering**:
  //     For example: `display_name = "test"`.
  //     Supported fields include: `name`, `display_name`, `uri`, `state`,
  //     `schema_title`, `create_time`, and `update_time`.
  //     Time fields, such as `create_time` and `update_time`, require values
  //     specified in RFC-3339 format.
  //     For example: `create_time = "2020-11-19T11:30:00-04:00"`
  // *   **Metadata field**:
  //     To filter on metadata fields use traversal operation as follows:
  //     `metadata.<field_name>.<type_value>`.
  //     For example: `metadata.field_1.number_value = 10.0`
  //     In case the field name contains special characters (such as colon), one
  //     can embed it inside double quote.
  //     For example: `metadata."field:1".number_value = 10.0`
  // *   **Context based filtering**:
  //     To filter Artifacts based on the contexts to which they belong, use the
  //     function operator with the full resource name
  //     `in_context(<context-name>)`.
  //     For example:
  //     `in_context("projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context-id>")`
  //
  // Each of the above supported filter types can be combined together using
  // logical operators (`AND` & `OR`). Maximum nested expression depth allowed
  // is 5.
  //
  // For example: `display_name = "test" AND metadata.field1.bool_value = true`.
  string filter = 4;

  // How the list of messages is ordered. Specify the values to order by and an
  // ordering operation. The default sorting order is ascending. To specify
  // descending order for a field, users append a " desc" suffix; for example:
  // "foo desc, bar".
  // Subfields are specified with a `.` character, such as foo.bar.
  // see https://google.aip.dev/132#ordering for more details.
  string order_by = 5;
}

// Response message for
// [MetadataService.ListArtifacts][google.cloud.aiplatform.v1beta1.MetadataService.ListArtifacts].
message ListArtifactsResponse {
  // The Artifacts retrieved from the MetadataStore.
  repeated Artifact artifacts = 1;

  // A token, which can be sent as
  // [ListArtifactsRequest.page_token][google.cloud.aiplatform.v1beta1.ListArtifactsRequest.page_token]
  // to retrieve the next page.
  // If this field is not populated, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [MetadataService.UpdateArtifact][google.cloud.aiplatform.v1beta1.MetadataService.UpdateArtifact].
message UpdateArtifactRequest {
  // Required. The Artifact containing updates.
  // The Artifact's
  // [Artifact.name][google.cloud.aiplatform.v1beta1.Artifact.name] field is
  // used to identify the Artifact to be updated. Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
  Artifact artifact = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. A FieldMask indicating which fields should be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // If set to true, and the
  // [Artifact][google.cloud.aiplatform.v1beta1.Artifact] is not found, a new
  // [Artifact][google.cloud.aiplatform.v1beta1.Artifact] is created.
  bool allow_missing = 3;
}

// Request message for
// [MetadataService.DeleteArtifact][google.cloud.aiplatform.v1beta1.MetadataService.DeleteArtifact].
message DeleteArtifactRequest {
  // Required. The resource name of the Artifact to delete.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Artifact"
    }
  ];

  // Optional. The etag of the Artifact to delete.
  // If this is provided, it must match the server's etag. Otherwise, the
  // request will fail with a FAILED_PRECONDITION.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [MetadataService.PurgeArtifacts][google.cloud.aiplatform.v1beta1.MetadataService.PurgeArtifacts].
message PurgeArtifactsRequest {
  // Required. The metadata store to purge Artifacts from.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/Artifact"
    }
  ];

  // Required. A required filter matching the Artifacts to be purged.
  // E.g., `update_time <= 2020-11-19T11:30:00-04:00`.
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Flag to indicate to actually perform the purge.
  // If `force` is set to false, the method will return a sample of
  // Artifact names that would be deleted.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [MetadataService.PurgeArtifacts][google.cloud.aiplatform.v1beta1.MetadataService.PurgeArtifacts].
message PurgeArtifactsResponse {
  // The number of Artifacts that this request deleted (or, if `force` is false,
  // the number of Artifacts that will be deleted). This can be an estimate.
  int64 purge_count = 1;

  // A sample of the Artifact names that will be deleted.
  // Only populated if `force` is set to false. The maximum number of samples is
  // 100 (it is possible to return fewer).
  repeated string purge_sample = 2 [(google.api.resource_reference) = {
    type: "aiplatform.googleapis.com/Artifact"
  }];
}

// Details of operations that perform
// [MetadataService.PurgeArtifacts][google.cloud.aiplatform.v1beta1.MetadataService.PurgeArtifacts].
message PurgeArtifactsMetadata {
  // Operation metadata for purging Artifacts.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [MetadataService.CreateContext][google.cloud.aiplatform.v1beta1.MetadataService.CreateContext].
message CreateContextRequest {
  // Required. The resource name of the MetadataStore where the Context should
  // be created. Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/MetadataStore"
    }
  ];

  // Required. The Context to create.
  Context context = 2 [(google.api.field_behavior) = REQUIRED];

  // The {context} portion of the resource name with the format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`.
  // If not provided, the Context's ID will be a UUID generated by the service.
  // Must be 4-128 characters in length. Valid characters are `/[a-z][0-9]-/`.
  // Must be unique across all Contexts in the parent MetadataStore. (Otherwise
  // the request will fail with ALREADY_EXISTS, or PERMISSION_DENIED if the
  // caller can't view the preexisting Context.)
  string context_id = 3;
}

// Request message for
// [MetadataService.GetContext][google.cloud.aiplatform.v1beta1.MetadataService.GetContext].
message GetContextRequest {
  // Required. The resource name of the Context to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Context"
    }
  ];
}

// Request message for
// [MetadataService.ListContexts][google.cloud.aiplatform.v1beta1.MetadataService.ListContexts]
message ListContextsRequest {
  // Required. The MetadataStore whose Contexts should be listed.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/Context"
    }
  ];

  // The maximum number of Contexts to return. The service may return fewer.
  // Must be in range 1-1000, inclusive. Defaults to 100.
  int32 page_size = 2;

  // A page token, received from a previous
  // [MetadataService.ListContexts][google.cloud.aiplatform.v1beta1.MetadataService.ListContexts]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters must match the call that
  // provided the page token. (Otherwise the request will fail with
  // INVALID_ARGUMENT error.)
  string page_token = 3;

  // Filter specifying the boolean condition for the Contexts to satisfy in
  // order to be part of the result set.
  // The syntax to define filter query is based on https://google.aip.dev/160.
  // Following are the supported set of filters:
  //
  // *  **Attribute filtering**:
  //    For example: `display_name = "test"`.
  //    Supported fields include: `name`, `display_name`, `schema_title`,
  //    `create_time`, and `update_time`.
  //    Time fields, such as `create_time` and `update_time`, require values
  //    specified in RFC-3339 format.
  //    For example: `create_time = "2020-11-19T11:30:00-04:00"`.
  // *  **Metadata field**:
  //    To filter on metadata fields use traversal operation as follows:
  //    `metadata.<field_name>.<type_value>`.
  //    For example: `metadata.field_1.number_value = 10.0`.
  //    In case the field name contains special characters (such as colon), one
  //    can embed it inside double quote.
  //    For example: `metadata."field:1".number_value = 10.0`
  // *  **Parent Child filtering**:
  //    To filter Contexts based on parent-child relationship use the HAS
  //    operator as follows:
  //
  //    ```
  //    parent_contexts:
  //    "projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context_id>"
  //    child_contexts:
  //    "projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context_id>"
  //    ```
  //
  // Each of the above supported filters can be combined together using
  // logical operators (`AND` & `OR`). Maximum nested expression depth allowed
  // is 5.
  //
  // For example: `display_name = "test" AND metadata.field1.bool_value = true`.
  string filter = 4;

  // How the list of messages is ordered. Specify the values to order by and an
  // ordering operation. The default sorting order is ascending. To specify
  // descending order for a field, users append a " desc" suffix; for example:
  // "foo desc, bar".
  // Subfields are specified with a `.` character, such as foo.bar.
  // see https://google.aip.dev/132#ordering for more details.
  string order_by = 5;
}

// Response message for
// [MetadataService.ListContexts][google.cloud.aiplatform.v1beta1.MetadataService.ListContexts].
message ListContextsResponse {
  // The Contexts retrieved from the MetadataStore.
  repeated Context contexts = 1;

  // A token, which can be sent as
  // [ListContextsRequest.page_token][google.cloud.aiplatform.v1beta1.ListContextsRequest.page_token]
  // to retrieve the next page.
  // If this field is not populated, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [MetadataService.UpdateContext][google.cloud.aiplatform.v1beta1.MetadataService.UpdateContext].
message UpdateContextRequest {
  // Required. The Context containing updates.
  // The Context's [Context.name][google.cloud.aiplatform.v1beta1.Context.name]
  // field is used to identify the Context to be updated. Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
  Context context = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. A FieldMask indicating which fields should be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // If set to true, and the [Context][google.cloud.aiplatform.v1beta1.Context]
  // is not found, a new [Context][google.cloud.aiplatform.v1beta1.Context] is
  // created.
  bool allow_missing = 3;
}

// Request message for
// [MetadataService.DeleteContext][google.cloud.aiplatform.v1beta1.MetadataService.DeleteContext].
message DeleteContextRequest {
  // Required. The resource name of the Context to delete.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Context"
    }
  ];

  // The force deletion semantics is still undefined.
  // Users should not use this field.
  bool force = 2;

  // Optional. The etag of the Context to delete.
  // If this is provided, it must match the server's etag. Otherwise, the
  // request will fail with a FAILED_PRECONDITION.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [MetadataService.PurgeContexts][google.cloud.aiplatform.v1beta1.MetadataService.PurgeContexts].
message PurgeContextsRequest {
  // Required. The metadata store to purge Contexts from.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/Context"
    }
  ];

  // Required. A required filter matching the Contexts to be purged.
  // E.g., `update_time <= 2020-11-19T11:30:00-04:00`.
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Flag to indicate to actually perform the purge.
  // If `force` is set to false, the method will return a sample of
  // Context names that would be deleted.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [MetadataService.PurgeContexts][google.cloud.aiplatform.v1beta1.MetadataService.PurgeContexts].
message PurgeContextsResponse {
  // The number of Contexts that this request deleted (or, if `force` is false,
  // the number of Contexts that will be deleted). This can be an estimate.
  int64 purge_count = 1;

  // A sample of the Context names that will be deleted.
  // Only populated if `force` is set to false. The maximum number of samples is
  // 100 (it is possible to return fewer).
  repeated string purge_sample = 2 [(google.api.resource_reference) = {
    type: "aiplatform.googleapis.com/Context"
  }];
}

// Details of operations that perform
// [MetadataService.PurgeContexts][google.cloud.aiplatform.v1beta1.MetadataService.PurgeContexts].
message PurgeContextsMetadata {
  // Operation metadata for purging Contexts.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [MetadataService.AddContextArtifactsAndExecutions][google.cloud.aiplatform.v1beta1.MetadataService.AddContextArtifactsAndExecutions].
message AddContextArtifactsAndExecutionsRequest {
  // Required. The resource name of the Context that the Artifacts and
  // Executions belong to. Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
  string context = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Context"
    }
  ];

  // The resource names of the Artifacts to attribute to the Context.
  //
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
  repeated string artifacts = 2 [(google.api.resource_reference) = {
    type: "aiplatform.googleapis.com/Artifact"
  }];

  // The resource names of the Executions to associate with the
  // Context.
  //
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
  repeated string executions = 3 [(google.api.resource_reference) = {
    type: "aiplatform.googleapis.com/Execution"
  }];
}

// Response message for
// [MetadataService.AddContextArtifactsAndExecutions][google.cloud.aiplatform.v1beta1.MetadataService.AddContextArtifactsAndExecutions].
message AddContextArtifactsAndExecutionsResponse {}

// Request message for
// [MetadataService.AddContextChildren][google.cloud.aiplatform.v1beta1.MetadataService.AddContextChildren].
message AddContextChildrenRequest {
  // Required. The resource name of the parent Context.
  //
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
  string context = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Context"
    }
  ];

  // The resource names of the child Contexts.
  repeated string child_contexts = 2 [(google.api.resource_reference) = {
    type: "aiplatform.googleapis.com/Context"
  }];
}

// Response message for
// [MetadataService.AddContextChildren][google.cloud.aiplatform.v1beta1.MetadataService.AddContextChildren].
message AddContextChildrenResponse {}

// Request message for
// [MetadataService.DeleteContextChildrenRequest][].
message RemoveContextChildrenRequest {
  // Required. The resource name of the parent Context.
  //
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
  string context = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Context"
    }
  ];

  // The resource names of the child Contexts.
  repeated string child_contexts = 2 [(google.api.resource_reference) = {
    type: "aiplatform.googleapis.com/Context"
  }];
}

// Response message for
// [MetadataService.RemoveContextChildren][google.cloud.aiplatform.v1beta1.MetadataService.RemoveContextChildren].
message RemoveContextChildrenResponse {}

// Request message for
// [MetadataService.QueryContextLineageSubgraph][google.cloud.aiplatform.v1beta1.MetadataService.QueryContextLineageSubgraph].
message QueryContextLineageSubgraphRequest {
  // Required. The resource name of the Context whose Artifacts and Executions
  // should be retrieved as a LineageSubgraph.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/contexts/{context}`
  //
  // The request may error with FAILED_PRECONDITION if the number of Artifacts,
  // the number of Executions, or the number of Events that would be returned
  // for the Context exceeds 1000.
  string context = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Context"
    }
  ];
}

// Request message for
// [MetadataService.CreateExecution][google.cloud.aiplatform.v1beta1.MetadataService.CreateExecution].
message CreateExecutionRequest {
  // Required. The resource name of the MetadataStore where the Execution should
  // be created.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/MetadataStore"
    }
  ];

  // Required. The Execution to create.
  Execution execution = 2 [(google.api.field_behavior) = REQUIRED];

  // The {execution} portion of the resource name with the format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
  // If not provided, the Execution's ID will be a UUID generated by the
  // service.
  // Must be 4-128 characters in length. Valid characters are `/[a-z][0-9]-/`.
  // Must be unique across all Executions in the parent MetadataStore.
  // (Otherwise the request will fail with ALREADY_EXISTS, or PERMISSION_DENIED
  // if the caller can't view the preexisting Execution.)
  string execution_id = 3;
}

// Request message for
// [MetadataService.GetExecution][google.cloud.aiplatform.v1beta1.MetadataService.GetExecution].
message GetExecutionRequest {
  // Required. The resource name of the Execution to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Execution"
    }
  ];
}

// Request message for
// [MetadataService.ListExecutions][google.cloud.aiplatform.v1beta1.MetadataService.ListExecutions].
message ListExecutionsRequest {
  // Required. The MetadataStore whose Executions should be listed.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/Execution"
    }
  ];

  // The maximum number of Executions to return. The service may return fewer.
  // Must be in range 1-1000, inclusive. Defaults to 100.
  int32 page_size = 2;

  // A page token, received from a previous
  // [MetadataService.ListExecutions][google.cloud.aiplatform.v1beta1.MetadataService.ListExecutions]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other provided parameters must match the call that
  // provided the page token. (Otherwise the request will fail with an
  // INVALID_ARGUMENT error.)
  string page_token = 3;

  // Filter specifying the boolean condition for the Executions to satisfy in
  // order to be part of the result set.
  // The syntax to define filter query is based on https://google.aip.dev/160.
  // Following are the supported set of filters:
  //
  // *  **Attribute filtering**:
  //    For example: `display_name = "test"`.
  //    Supported fields include: `name`, `display_name`, `state`,
  //    `schema_title`, `create_time`, and `update_time`.
  //    Time fields, such as `create_time` and `update_time`, require values
  //    specified in RFC-3339 format.
  //    For example: `create_time = "2020-11-19T11:30:00-04:00"`.
  // *  **Metadata field**:
  //    To filter on metadata fields use traversal operation as follows:
  //    `metadata.<field_name>.<type_value>`
  //    For example: `metadata.field_1.number_value = 10.0`
  //    In case the field name contains special characters (such as colon), one
  //    can embed it inside double quote.
  //    For example: `metadata."field:1".number_value = 10.0`
  // *  **Context based filtering**:
  //    To filter Executions based on the contexts to which they belong use
  //    the function operator with the full resource name:
  //    `in_context(<context-name>)`.
  //    For example:
  //    `in_context("projects/<project_number>/locations/<location>/metadataStores/<metadatastore_name>/contexts/<context-id>")`
  //
  // Each of the above supported filters can be combined together using
  // logical operators (`AND` & `OR`). Maximum nested expression depth allowed
  // is 5.
  //
  // For example: `display_name = "test" AND metadata.field1.bool_value = true`.
  string filter = 4;

  // How the list of messages is ordered. Specify the values to order by and an
  // ordering operation. The default sorting order is ascending. To specify
  // descending order for a field, users append a " desc" suffix; for example:
  // "foo desc, bar".
  // Subfields are specified with a `.` character, such as foo.bar.
  // see https://google.aip.dev/132#ordering for more details.
  string order_by = 5;
}

// Response message for
// [MetadataService.ListExecutions][google.cloud.aiplatform.v1beta1.MetadataService.ListExecutions].
message ListExecutionsResponse {
  // The Executions retrieved from the MetadataStore.
  repeated Execution executions = 1;

  // A token, which can be sent as
  // [ListExecutionsRequest.page_token][google.cloud.aiplatform.v1beta1.ListExecutionsRequest.page_token]
  // to retrieve the next page.
  // If this field is not populated, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [MetadataService.UpdateExecution][google.cloud.aiplatform.v1beta1.MetadataService.UpdateExecution].
message UpdateExecutionRequest {
  // Required. The Execution containing updates.
  // The Execution's
  // [Execution.name][google.cloud.aiplatform.v1beta1.Execution.name] field is
  // used to identify the Execution to be updated. Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
  Execution execution = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. A FieldMask indicating which fields should be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // If set to true, and the
  // [Execution][google.cloud.aiplatform.v1beta1.Execution] is not found, a new
  // [Execution][google.cloud.aiplatform.v1beta1.Execution] is created.
  bool allow_missing = 3;
}

// Request message for
// [MetadataService.DeleteExecution][google.cloud.aiplatform.v1beta1.MetadataService.DeleteExecution].
message DeleteExecutionRequest {
  // Required. The resource name of the Execution to delete.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Execution"
    }
  ];

  // Optional. The etag of the Execution to delete.
  // If this is provided, it must match the server's etag. Otherwise, the
  // request will fail with a FAILED_PRECONDITION.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [MetadataService.PurgeExecutions][google.cloud.aiplatform.v1beta1.MetadataService.PurgeExecutions].
message PurgeExecutionsRequest {
  // Required. The metadata store to purge Executions from.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/Execution"
    }
  ];

  // Required. A required filter matching the Executions to be purged.
  // E.g., `update_time <= 2020-11-19T11:30:00-04:00`.
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Flag to indicate to actually perform the purge.
  // If `force` is set to false, the method will return a sample of
  // Execution names that would be deleted.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [MetadataService.PurgeExecutions][google.cloud.aiplatform.v1beta1.MetadataService.PurgeExecutions].
message PurgeExecutionsResponse {
  // The number of Executions that this request deleted (or, if `force` is
  // false, the number of Executions that will be deleted). This can be an
  // estimate.
  int64 purge_count = 1;

  // A sample of the Execution names that will be deleted.
  // Only populated if `force` is set to false. The maximum number of samples is
  // 100 (it is possible to return fewer).
  repeated string purge_sample = 2 [(google.api.resource_reference) = {
    type: "aiplatform.googleapis.com/Execution"
  }];
}

// Details of operations that perform
// [MetadataService.PurgeExecutions][google.cloud.aiplatform.v1beta1.MetadataService.PurgeExecutions].
message PurgeExecutionsMetadata {
  // Operation metadata for purging Executions.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [MetadataService.AddExecutionEvents][google.cloud.aiplatform.v1beta1.MetadataService.AddExecutionEvents].
message AddExecutionEventsRequest {
  // Required. The resource name of the Execution that the Events connect
  // Artifacts with.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
  string execution = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Execution"
    }
  ];

  // The Events to create and add.
  repeated Event events = 2;
}

// Response message for
// [MetadataService.AddExecutionEvents][google.cloud.aiplatform.v1beta1.MetadataService.AddExecutionEvents].
message AddExecutionEventsResponse {}

// Request message for
// [MetadataService.QueryExecutionInputsAndOutputs][google.cloud.aiplatform.v1beta1.MetadataService.QueryExecutionInputsAndOutputs].
message QueryExecutionInputsAndOutputsRequest {
  // Required. The resource name of the Execution whose input and output
  // Artifacts should be retrieved as a LineageSubgraph. Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/executions/{execution}`
  string execution = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Execution"
    }
  ];
}

// Request message for
// [MetadataService.CreateMetadataSchema][google.cloud.aiplatform.v1beta1.MetadataService.CreateMetadataSchema].
message CreateMetadataSchemaRequest {
  // Required. The resource name of the MetadataStore where the MetadataSchema
  // should be created. Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/MetadataStore"
    }
  ];

  // Required. The MetadataSchema to create.
  MetadataSchema metadata_schema = 2 [(google.api.field_behavior) = REQUIRED];

  // The {metadata_schema} portion of the resource name with the format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/metadataSchemas/{metadataschema}`
  // If not provided, the MetadataStore's ID will be a UUID generated by the
  // service.
  // Must be 4-128 characters in length. Valid characters are `/[a-z][0-9]-/`.
  // Must be unique across all MetadataSchemas in the parent Location.
  // (Otherwise the request will fail with ALREADY_EXISTS, or PERMISSION_DENIED
  // if the caller can't view the preexisting MetadataSchema.)
  string metadata_schema_id = 3;
}

// Request message for
// [MetadataService.GetMetadataSchema][google.cloud.aiplatform.v1beta1.MetadataService.GetMetadataSchema].
message GetMetadataSchemaRequest {
  // Required. The resource name of the MetadataSchema to retrieve.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/metadataSchemas/{metadataschema}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/MetadataSchema"
    }
  ];
}

// Request message for
// [MetadataService.ListMetadataSchemas][google.cloud.aiplatform.v1beta1.MetadataService.ListMetadataSchemas].
message ListMetadataSchemasRequest {
  // Required. The MetadataStore whose MetadataSchemas should be listed.
  // Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/MetadataSchema"
    }
  ];

  // The maximum number of MetadataSchemas to return. The service may return
  // fewer.
  // Must be in range 1-1000, inclusive. Defaults to 100.
  int32 page_size = 2;

  // A page token, received from a previous
  // [MetadataService.ListMetadataSchemas][google.cloud.aiplatform.v1beta1.MetadataService.ListMetadataSchemas]
  // call. Provide this to retrieve the next page.
  //
  // When paginating, all other provided parameters must match the call that
  // provided the page token. (Otherwise the request will fail with
  // INVALID_ARGUMENT error.)
  string page_token = 3;

  // A query to filter available MetadataSchemas for matching results.
  string filter = 4;
}

// Response message for
// [MetadataService.ListMetadataSchemas][google.cloud.aiplatform.v1beta1.MetadataService.ListMetadataSchemas].
message ListMetadataSchemasResponse {
  // The MetadataSchemas found for the MetadataStore.
  repeated MetadataSchema metadata_schemas = 1;

  // A token, which can be sent as
  // [ListMetadataSchemasRequest.page_token][google.cloud.aiplatform.v1beta1.ListMetadataSchemasRequest.page_token]
  // to retrieve the next page. If this field is not populated, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [MetadataService.QueryArtifactLineageSubgraph][google.cloud.aiplatform.v1beta1.MetadataService.QueryArtifactLineageSubgraph].
message QueryArtifactLineageSubgraphRequest {
  // Required. The resource name of the Artifact whose Lineage needs to be
  // retrieved as a LineageSubgraph. Format:
  // `projects/{project}/locations/{location}/metadataStores/{metadatastore}/artifacts/{artifact}`
  //
  // The request may error with FAILED_PRECONDITION if the number of Artifacts,
  // the number of Executions, or the number of Events that would be returned
  // for the Context exceeds 1000.
  string artifact = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Artifact"
    }
  ];

  // Specifies the size of the lineage graph in terms of number of hops from the
  // specified artifact.
  // Negative Value: INVALID_ARGUMENT error is returned
  // 0: Only input artifact is returned.
  // No value: Transitive closure is performed to return the complete graph.
  int32 max_hops = 2;

  // Filter specifying the boolean condition for the Artifacts to satisfy in
  // order to be part of the Lineage Subgraph.
  // The syntax to define filter query is based on https://google.aip.dev/160.
  // The supported set of filters include the following:
  //
  // *  **Attribute filtering**:
  //    For example: `display_name = "test"`
  //    Supported fields include: `name`, `display_name`, `uri`, `state`,
  //    `schema_title`, `create_time`, and `update_time`.
  //    Time fields, such as `create_time` and `update_time`, require values
  //    specified in RFC-3339 format.
  //    For example: `create_time = "2020-11-19T11:30:00-04:00"`
  // *  **Metadata field**:
  //    To filter on metadata fields use traversal operation as follows:
  //    `metadata.<field_name>.<type_value>`.
  //    For example: `metadata.field_1.number_value = 10.0`
  //    In case the field name contains special characters (such as colon), one
  //    can embed it inside double quote.
  //    For example: `metadata."field:1".number_value = 10.0`
  //
  // Each of the above supported filter types can be combined together using
  // logical operators (`AND` & `OR`). Maximum nested expression depth allowed
  // is 5.
  //
  // For example: `display_name = "test" AND metadata.field1.bool_value = true`.
  string filter = 3;
}
