// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/cloud/aiplatform/v1beta1/tensorboard.proto";
import "google/cloud/aiplatform/v1beta1/tensorboard_data.proto";
import "google/cloud/aiplatform/v1beta1/tensorboard_experiment.proto";
import "google/cloud/aiplatform/v1beta1/tensorboard_run.proto";
import "google/cloud/aiplatform/v1beta1/tensorboard_time_series.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "TensorboardServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// TensorboardService
service TensorboardService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a Tensorboard.
  rpc CreateTensorboard(CreateTensorboardRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/tensorboards"
      body: "tensorboard"
    };
    option (google.api.method_signature) = "parent,tensorboard";
    option (google.longrunning.operation_info) = {
      response_type: "Tensorboard"
      metadata_type: "CreateTensorboardOperationMetadata"
    };
  }

  // Gets a Tensorboard.
  rpc GetTensorboard(GetTensorboardRequest) returns (Tensorboard) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/tensorboards/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a Tensorboard.
  rpc UpdateTensorboard(UpdateTensorboardRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{tensorboard.name=projects/*/locations/*/tensorboards/*}"
      body: "tensorboard"
    };
    option (google.api.method_signature) = "tensorboard,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Tensorboard"
      metadata_type: "UpdateTensorboardOperationMetadata"
    };
  }

  // Lists Tensorboards in a Location.
  rpc ListTensorboards(ListTensorboardsRequest)
      returns (ListTensorboardsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/tensorboards"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a Tensorboard.
  rpc DeleteTensorboard(DeleteTensorboardRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/tensorboards/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Returns a list of monthly active users for a given TensorBoard instance.
  rpc ReadTensorboardUsage(ReadTensorboardUsageRequest)
      returns (ReadTensorboardUsageResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{tensorboard=projects/*/locations/*/tensorboards/*}:readUsage"
    };
    option (google.api.method_signature) = "tensorboard";
  }

  // Returns the storage size for a given TensorBoard instance.
  rpc ReadTensorboardSize(ReadTensorboardSizeRequest)
      returns (ReadTensorboardSizeResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{tensorboard=projects/*/locations/*/tensorboards/*}:readSize"
    };
    option (google.api.method_signature) = "tensorboard";
  }

  // Creates a TensorboardExperiment.
  rpc CreateTensorboardExperiment(CreateTensorboardExperimentRequest)
      returns (TensorboardExperiment) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/tensorboards/*}/experiments"
      body: "tensorboard_experiment"
    };
    option (google.api.method_signature) =
        "parent,tensorboard_experiment,tensorboard_experiment_id";
  }

  // Gets a TensorboardExperiment.
  rpc GetTensorboardExperiment(GetTensorboardExperimentRequest)
      returns (TensorboardExperiment) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a TensorboardExperiment.
  rpc UpdateTensorboardExperiment(UpdateTensorboardExperimentRequest)
      returns (TensorboardExperiment) {
    option (google.api.http) = {
      patch: "/v1beta1/{tensorboard_experiment.name=projects/*/locations/*/tensorboards/*/experiments/*}"
      body: "tensorboard_experiment"
    };
    option (google.api.method_signature) = "tensorboard_experiment,update_mask";
  }

  // Lists TensorboardExperiments in a Location.
  rpc ListTensorboardExperiments(ListTensorboardExperimentsRequest)
      returns (ListTensorboardExperimentsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/tensorboards/*}/experiments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a TensorboardExperiment.
  rpc DeleteTensorboardExperiment(DeleteTensorboardExperimentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Creates a TensorboardRun.
  rpc CreateTensorboardRun(CreateTensorboardRunRequest)
      returns (TensorboardRun) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/tensorboards/*/experiments/*}/runs"
      body: "tensorboard_run"
    };
    option (google.api.method_signature) =
        "parent,tensorboard_run,tensorboard_run_id";
  }

  // Batch create TensorboardRuns.
  rpc BatchCreateTensorboardRuns(BatchCreateTensorboardRunsRequest)
      returns (BatchCreateTensorboardRunsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/tensorboards/*/experiments/*}/runs:batchCreate"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
  }

  // Gets a TensorboardRun.
  rpc GetTensorboardRun(GetTensorboardRunRequest) returns (TensorboardRun) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a TensorboardRun.
  rpc UpdateTensorboardRun(UpdateTensorboardRunRequest)
      returns (TensorboardRun) {
    option (google.api.http) = {
      patch: "/v1beta1/{tensorboard_run.name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}"
      body: "tensorboard_run"
    };
    option (google.api.method_signature) = "tensorboard_run,update_mask";
  }

  // Lists TensorboardRuns in a Location.
  rpc ListTensorboardRuns(ListTensorboardRunsRequest)
      returns (ListTensorboardRunsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/tensorboards/*/experiments/*}/runs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a TensorboardRun.
  rpc DeleteTensorboardRun(DeleteTensorboardRunRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Batch create TensorboardTimeSeries that belong to a TensorboardExperiment.
  rpc BatchCreateTensorboardTimeSeries(BatchCreateTensorboardTimeSeriesRequest)
      returns (BatchCreateTensorboardTimeSeriesResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/tensorboards/*/experiments/*}:batchCreate"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
  }

  // Creates a TensorboardTimeSeries.
  rpc CreateTensorboardTimeSeries(CreateTensorboardTimeSeriesRequest)
      returns (TensorboardTimeSeries) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}/timeSeries"
      body: "tensorboard_time_series"
    };
    option (google.api.method_signature) = "parent,tensorboard_time_series";
  }

  // Gets a TensorboardTimeSeries.
  rpc GetTensorboardTimeSeries(GetTensorboardTimeSeriesRequest)
      returns (TensorboardTimeSeries) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a TensorboardTimeSeries.
  rpc UpdateTensorboardTimeSeries(UpdateTensorboardTimeSeriesRequest)
      returns (TensorboardTimeSeries) {
    option (google.api.http) = {
      patch: "/v1beta1/{tensorboard_time_series.name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}"
      body: "tensorboard_time_series"
    };
    option (google.api.method_signature) =
        "tensorboard_time_series,update_mask";
  }

  // Lists TensorboardTimeSeries in a Location.
  rpc ListTensorboardTimeSeries(ListTensorboardTimeSeriesRequest)
      returns (ListTensorboardTimeSeriesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}/timeSeries"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a TensorboardTimeSeries.
  rpc DeleteTensorboardTimeSeries(DeleteTensorboardTimeSeriesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Reads multiple TensorboardTimeSeries' data. The data point number limit is
  // 1000 for scalars, 100 for tensors and blob references. If the number of
  // data points stored is less than the limit, all data is returned.
  // Otherwise, the number limit of data points is randomly selected from
  // this time series and returned.
  rpc BatchReadTensorboardTimeSeriesData(
      BatchReadTensorboardTimeSeriesDataRequest)
      returns (BatchReadTensorboardTimeSeriesDataResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{tensorboard=projects/*/locations/*/tensorboards/*}:batchRead"
    };
    option (google.api.method_signature) = "tensorboard";
  }

  // Reads a TensorboardTimeSeries' data. By default, if the number of data
  // points stored is less than 1000, all data is returned. Otherwise, 1000
  // data points is randomly selected from this time series and returned.
  // This value can be changed by changing max_data_points, which can't be
  // greater than 10k.
  rpc ReadTensorboardTimeSeriesData(ReadTensorboardTimeSeriesDataRequest)
      returns (ReadTensorboardTimeSeriesDataResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{tensorboard_time_series=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}:read"
    };
    option (google.api.method_signature) = "tensorboard_time_series";
  }

  // Gets bytes of TensorboardBlobs.
  // This is to allow reading blob data stored in consumer project's Cloud
  // Storage bucket without users having to obtain Cloud Storage access
  // permission.
  rpc ReadTensorboardBlobData(ReadTensorboardBlobDataRequest)
      returns (stream ReadTensorboardBlobDataResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{time_series=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}:readBlobData"
    };
    option (google.api.method_signature) = "time_series";
  }

  // Write time series data points of multiple TensorboardTimeSeries in multiple
  // TensorboardRun's. If any data fail to be ingested, an error is returned.
  rpc WriteTensorboardExperimentData(WriteTensorboardExperimentDataRequest)
      returns (WriteTensorboardExperimentDataResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{tensorboard_experiment=projects/*/locations/*/tensorboards/*/experiments/*}:write"
      body: "*"
    };
    option (google.api.method_signature) =
        "tensorboard_experiment,write_run_data_requests";
  }

  // Write time series data points into multiple TensorboardTimeSeries under
  // a TensorboardRun. If any data fail to be ingested, an error is returned.
  rpc WriteTensorboardRunData(WriteTensorboardRunDataRequest)
      returns (WriteTensorboardRunDataResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{tensorboard_run=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}:write"
      body: "*"
    };
    option (google.api.method_signature) = "tensorboard_run,time_series_data";
  }

  // Exports a TensorboardTimeSeries' data. Data is returned in paginated
  // responses.
  rpc ExportTensorboardTimeSeriesData(ExportTensorboardTimeSeriesDataRequest)
      returns (ExportTensorboardTimeSeriesDataResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{tensorboard_time_series=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}:exportTensorboardTimeSeries"
      body: "*"
    };
    option (google.api.method_signature) = "tensorboard_time_series";
  }
}

// Request message for
// [TensorboardService.CreateTensorboard][google.cloud.aiplatform.v1beta1.TensorboardService.CreateTensorboard].
message CreateTensorboardRequest {
  // Required. The resource name of the Location to create the Tensorboard in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Tensorboard"
    }
  ];

  // Required. The Tensorboard to create.
  Tensorboard tensorboard = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [TensorboardService.GetTensorboard][google.cloud.aiplatform.v1beta1.TensorboardService.GetTensorboard].
message GetTensorboardRequest {
  // Required. The name of the Tensorboard resource.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Tensorboard"
    }
  ];
}

// Request message for
// [TensorboardService.ListTensorboards][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboards].
message ListTensorboardsRequest {
  // Required. The resource name of the Location to list Tensorboards.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/Tensorboard"
    }
  ];

  // Lists the Tensorboards that match the filter expression.
  string filter = 2;

  // The maximum number of Tensorboards to return. The service may return
  // fewer than this value. If unspecified, at most 100 Tensorboards are
  // returned. The maximum value is 100; values above 100 are coerced to
  // 100.
  int32 page_size = 3;

  // A page token, received from a previous
  // [TensorboardService.ListTensorboards][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboards]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [TensorboardService.ListTensorboards][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboards]
  // must match the call that provided the page token.
  string page_token = 4;

  // Field to use to sort the list.
  string order_by = 5;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 6;
}

// Response message for
// [TensorboardService.ListTensorboards][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboards].
message ListTensorboardsResponse {
  // The Tensorboards mathching the request.
  repeated Tensorboard tensorboards = 1;

  // A token, which can be sent as
  // [ListTensorboardsRequest.page_token][google.cloud.aiplatform.v1beta1.ListTensorboardsRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [TensorboardService.UpdateTensorboard][google.cloud.aiplatform.v1beta1.TensorboardService.UpdateTensorboard].
message UpdateTensorboardRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Tensorboard resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field is overwritten if it's in the mask. If the
  // user does not provide a mask then all fields are overwritten if new
  // values are specified.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The Tensorboard's `name` field is used to identify the
  // Tensorboard to be updated. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}`
  Tensorboard tensorboard = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [TensorboardService.DeleteTensorboard][google.cloud.aiplatform.v1beta1.TensorboardService.DeleteTensorboard].
message DeleteTensorboardRequest {
  // Required. The name of the Tensorboard to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Tensorboard"
    }
  ];
}

// Request message for
// [TensorboardService.ReadTensorboardUsage][google.cloud.aiplatform.v1beta1.TensorboardService.ReadTensorboardUsage].
message ReadTensorboardUsageRequest {
  // Required. The name of the Tensorboard resource.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}`
  string tensorboard = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Tensorboard"
    }
  ];
}

// Response message for
// [TensorboardService.ReadTensorboardUsage][google.cloud.aiplatform.v1beta1.TensorboardService.ReadTensorboardUsage].
message ReadTensorboardUsageResponse {
  // Per user usage data.
  message PerUserUsageData {
    // User's username
    string username = 1;

    // Number of times the user has read data within the Tensorboard.
    int64 view_count = 2;
  }

  // Per month usage data
  message PerMonthUsageData {
    // Usage data for each user in the given month.
    repeated PerUserUsageData user_usage_data = 1;
  }

  // Maps year-month (YYYYMM) string to per month usage data.
  map<string, PerMonthUsageData> monthly_usage_data = 1;
}

// Request message for
// [TensorboardService.ReadTensorboardSize][google.cloud.aiplatform.v1beta1.TensorboardService.ReadTensorboardSize].
message ReadTensorboardSizeRequest {
  // Required. The name of the Tensorboard resource.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}`
  string tensorboard = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Tensorboard"
    }
  ];
}

// Response message for
// [TensorboardService.ReadTensorboardSize][google.cloud.aiplatform.v1beta1.TensorboardService.ReadTensorboardSize].
message ReadTensorboardSizeResponse {
  // Payload storage size for the TensorBoard
  int64 storage_size_byte = 1;
}

// Request message for
// [TensorboardService.CreateTensorboardExperiment][google.cloud.aiplatform.v1beta1.TensorboardService.CreateTensorboardExperiment].
message CreateTensorboardExperimentRequest {
  // Required. The resource name of the Tensorboard to create the
  // TensorboardExperiment in. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardExperiment"
    }
  ];

  // The TensorboardExperiment to create.
  TensorboardExperiment tensorboard_experiment = 2;

  // Required. The ID to use for the Tensorboard experiment, which becomes the
  // final component of the Tensorboard experiment's resource name.
  //
  // This value should be 1-128 characters, and valid characters
  // are `/[a-z][0-9]-/`.
  string tensorboard_experiment_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [TensorboardService.GetTensorboardExperiment][google.cloud.aiplatform.v1beta1.TensorboardService.GetTensorboardExperiment].
message GetTensorboardExperimentRequest {
  // Required. The name of the TensorboardExperiment resource.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardExperiment"
    }
  ];
}

// Request message for
// [TensorboardService.ListTensorboardExperiments][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardExperiments].
message ListTensorboardExperimentsRequest {
  // Required. The resource name of the Tensorboard to list
  // TensorboardExperiments. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/TensorboardExperiment"
    }
  ];

  // Lists the TensorboardExperiments that match the filter expression.
  string filter = 2;

  // The maximum number of TensorboardExperiments to return. The service may
  // return fewer than this value. If unspecified, at most 50
  // TensorboardExperiments are returned. The maximum value is 1000; values
  // above 1000 are coerced to 1000.
  int32 page_size = 3;

  // A page token, received from a previous
  // [TensorboardService.ListTensorboardExperiments][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardExperiments]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [TensorboardService.ListTensorboardExperiments][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardExperiments]
  // must match the call that provided the page token.
  string page_token = 4;

  // Field to use to sort the list.
  string order_by = 5;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 6;
}

// Response message for
// [TensorboardService.ListTensorboardExperiments][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardExperiments].
message ListTensorboardExperimentsResponse {
  // The TensorboardExperiments mathching the request.
  repeated TensorboardExperiment tensorboard_experiments = 1;

  // A token, which can be sent as
  // [ListTensorboardExperimentsRequest.page_token][google.cloud.aiplatform.v1beta1.ListTensorboardExperimentsRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [TensorboardService.UpdateTensorboardExperiment][google.cloud.aiplatform.v1beta1.TensorboardService.UpdateTensorboardExperiment].
message UpdateTensorboardExperimentRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // TensorboardExperiment resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field is overwritten if it's in the mask. If the
  // user does not provide a mask then all fields are overwritten if new
  // values are specified.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The TensorboardExperiment's `name` field is used to identify the
  // TensorboardExperiment to be updated. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}`
  TensorboardExperiment tensorboard_experiment = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [TensorboardService.DeleteTensorboardExperiment][google.cloud.aiplatform.v1beta1.TensorboardService.DeleteTensorboardExperiment].
message DeleteTensorboardExperimentRequest {
  // Required. The name of the TensorboardExperiment to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardExperiment"
    }
  ];
}

// Request message for
// [TensorboardService.BatchCreateTensorboardRuns][google.cloud.aiplatform.v1beta1.TensorboardService.BatchCreateTensorboardRuns].
message BatchCreateTensorboardRunsRequest {
  // Required. The resource name of the TensorboardExperiment to create the
  // TensorboardRuns in. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}`
  // The parent field in the CreateTensorboardRunRequest messages must match
  // this field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardExperiment"
    }
  ];

  // Required. The request message specifying the TensorboardRuns to create.
  // A maximum of 1000 TensorboardRuns can be created in a batch.
  repeated CreateTensorboardRunRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [TensorboardService.BatchCreateTensorboardRuns][google.cloud.aiplatform.v1beta1.TensorboardService.BatchCreateTensorboardRuns].
message BatchCreateTensorboardRunsResponse {
  // The created TensorboardRuns.
  repeated TensorboardRun tensorboard_runs = 1;
}

// Request message for
// [TensorboardService.CreateTensorboardRun][google.cloud.aiplatform.v1beta1.TensorboardService.CreateTensorboardRun].
message CreateTensorboardRunRequest {
  // Required. The resource name of the TensorboardExperiment to create the
  // TensorboardRun in. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardRun"
    }
  ];

  // Required. The TensorboardRun to create.
  TensorboardRun tensorboard_run = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the Tensorboard run, which becomes the final
  // component of the Tensorboard run's resource name.
  //
  // This value should be 1-128 characters, and valid characters
  // are `/[a-z][0-9]-/`.
  string tensorboard_run_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [TensorboardService.GetTensorboardRun][google.cloud.aiplatform.v1beta1.TensorboardService.GetTensorboardRun].
message GetTensorboardRunRequest {
  // Required. The name of the TensorboardRun resource.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardRun"
    }
  ];
}

// Request message for
// [TensorboardService.ReadTensorboardBlobData][google.cloud.aiplatform.v1beta1.TensorboardService.ReadTensorboardBlobData].
message ReadTensorboardBlobDataRequest {
  // Required. The resource name of the TensorboardTimeSeries to list Blobs.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}/timeSeries/{time_series}`
  string time_series = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardTimeSeries"
    }
  ];

  // IDs of the blobs to read.
  repeated string blob_ids = 2;
}

// Response message for
// [TensorboardService.ReadTensorboardBlobData][google.cloud.aiplatform.v1beta1.TensorboardService.ReadTensorboardBlobData].
message ReadTensorboardBlobDataResponse {
  // Blob messages containing blob bytes.
  repeated TensorboardBlob blobs = 1;
}

// Request message for
// [TensorboardService.ListTensorboardRuns][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardRuns].
message ListTensorboardRunsRequest {
  // Required. The resource name of the TensorboardExperiment to list
  // TensorboardRuns. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/TensorboardRun"
    }
  ];

  // Lists the TensorboardRuns that match the filter expression.
  string filter = 2;

  // The maximum number of TensorboardRuns to return. The service may return
  // fewer than this value. If unspecified, at most 50 TensorboardRuns are
  // returned. The maximum value is 1000; values above 1000 are coerced to
  // 1000.
  int32 page_size = 3;

  // A page token, received from a previous
  // [TensorboardService.ListTensorboardRuns][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardRuns]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [TensorboardService.ListTensorboardRuns][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardRuns]
  // must match the call that provided the page token.
  string page_token = 4;

  // Field to use to sort the list.
  string order_by = 5;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 6;
}

// Response message for
// [TensorboardService.ListTensorboardRuns][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardRuns].
message ListTensorboardRunsResponse {
  // The TensorboardRuns mathching the request.
  repeated TensorboardRun tensorboard_runs = 1;

  // A token, which can be sent as
  // [ListTensorboardRunsRequest.page_token][google.cloud.aiplatform.v1beta1.ListTensorboardRunsRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [TensorboardService.UpdateTensorboardRun][google.cloud.aiplatform.v1beta1.TensorboardService.UpdateTensorboardRun].
message UpdateTensorboardRunRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // TensorboardRun resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field is overwritten if it's in the mask. If the
  // user does not provide a mask then all fields are overwritten if new
  // values are specified.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The TensorboardRun's `name` field is used to identify the
  // TensorboardRun to be updated. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}`
  TensorboardRun tensorboard_run = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [TensorboardService.DeleteTensorboardRun][google.cloud.aiplatform.v1beta1.TensorboardService.DeleteTensorboardRun].
message DeleteTensorboardRunRequest {
  // Required. The name of the TensorboardRun to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardRun"
    }
  ];
}

// Request message for
// [TensorboardService.BatchCreateTensorboardTimeSeries][google.cloud.aiplatform.v1beta1.TensorboardService.BatchCreateTensorboardTimeSeries].
message BatchCreateTensorboardTimeSeriesRequest {
  // Required. The resource name of the TensorboardExperiment to create the
  // TensorboardTimeSeries in.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}`
  // The TensorboardRuns referenced by the parent fields in the
  // CreateTensorboardTimeSeriesRequest messages must be sub resources of this
  // TensorboardExperiment.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardExperiment"
    }
  ];

  // Required. The request message specifying the TensorboardTimeSeries to
  // create. A maximum of 1000 TensorboardTimeSeries can be created in a batch.
  repeated CreateTensorboardTimeSeriesRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [TensorboardService.BatchCreateTensorboardTimeSeries][google.cloud.aiplatform.v1beta1.TensorboardService.BatchCreateTensorboardTimeSeries].
message BatchCreateTensorboardTimeSeriesResponse {
  // The created TensorboardTimeSeries.
  repeated TensorboardTimeSeries tensorboard_time_series = 1;
}

// Request message for
// [TensorboardService.CreateTensorboardTimeSeries][google.cloud.aiplatform.v1beta1.TensorboardService.CreateTensorboardTimeSeries].
message CreateTensorboardTimeSeriesRequest {
  // Required. The resource name of the TensorboardRun to create the
  // TensorboardTimeSeries in.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardTimeSeries"
    }
  ];

  // Optional. The user specified unique ID to use for the
  // TensorboardTimeSeries, which becomes the final component of the
  // TensorboardTimeSeries's resource name. This value should match
  // "[a-z0-9][a-z0-9-]{0, 127}"
  string tensorboard_time_series_id = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The TensorboardTimeSeries to create.
  TensorboardTimeSeries tensorboard_time_series = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [TensorboardService.GetTensorboardTimeSeries][google.cloud.aiplatform.v1beta1.TensorboardService.GetTensorboardTimeSeries].
message GetTensorboardTimeSeriesRequest {
  // Required. The name of the TensorboardTimeSeries resource.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}/timeSeries/{time_series}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardTimeSeries"
    }
  ];
}

// Request message for
// [TensorboardService.ListTensorboardTimeSeries][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardTimeSeries].
message ListTensorboardTimeSeriesRequest {
  // Required. The resource name of the TensorboardRun to list
  // TensorboardTimeSeries. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/TensorboardTimeSeries"
    }
  ];

  // Lists the TensorboardTimeSeries that match the filter expression.
  string filter = 2;

  // The maximum number of TensorboardTimeSeries to return. The service may
  // return fewer than this value. If unspecified, at most 50
  // TensorboardTimeSeries are returned. The maximum value is 1000; values
  // above 1000 are coerced to 1000.
  int32 page_size = 3;

  // A page token, received from a previous
  // [TensorboardService.ListTensorboardTimeSeries][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardTimeSeries]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [TensorboardService.ListTensorboardTimeSeries][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardTimeSeries]
  // must match the call that provided the page token.
  string page_token = 4;

  // Field to use to sort the list.
  string order_by = 5;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 6;
}

// Response message for
// [TensorboardService.ListTensorboardTimeSeries][google.cloud.aiplatform.v1beta1.TensorboardService.ListTensorboardTimeSeries].
message ListTensorboardTimeSeriesResponse {
  // The TensorboardTimeSeries mathching the request.
  repeated TensorboardTimeSeries tensorboard_time_series = 1;

  // A token, which can be sent as
  // [ListTensorboardTimeSeriesRequest.page_token][google.cloud.aiplatform.v1beta1.ListTensorboardTimeSeriesRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [TensorboardService.UpdateTensorboardTimeSeries][google.cloud.aiplatform.v1beta1.TensorboardService.UpdateTensorboardTimeSeries].
message UpdateTensorboardTimeSeriesRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // TensorboardTimeSeries resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field is overwritten if it's in the mask. If the
  // user does not provide a mask then all fields are overwritten if new
  // values are specified.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The TensorboardTimeSeries' `name` field is used to identify the
  // TensorboardTimeSeries to be updated.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}/timeSeries/{time_series}`
  TensorboardTimeSeries tensorboard_time_series = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [TensorboardService.DeleteTensorboardTimeSeries][google.cloud.aiplatform.v1beta1.TensorboardService.DeleteTensorboardTimeSeries].
message DeleteTensorboardTimeSeriesRequest {
  // Required. The name of the TensorboardTimeSeries to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}/timeSeries/{time_series}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardTimeSeries"
    }
  ];
}

// Request message for
// [TensorboardService.BatchReadTensorboardTimeSeriesData][google.cloud.aiplatform.v1beta1.TensorboardService.BatchReadTensorboardTimeSeriesData].
message BatchReadTensorboardTimeSeriesDataRequest {
  // Required. The resource name of the Tensorboard containing
  // TensorboardTimeSeries to read data from. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}`.
  // The TensorboardTimeSeries referenced by
  // [time_series][google.cloud.aiplatform.v1beta1.BatchReadTensorboardTimeSeriesDataRequest.time_series]
  // must be sub resources of this Tensorboard.
  string tensorboard = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Tensorboard"
    }
  ];

  // Required. The resource names of the TensorboardTimeSeries to read data
  // from. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}/timeSeries/{time_series}`
  repeated string time_series = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardTimeSeries"
    }
  ];
}

// Response message for
// [TensorboardService.BatchReadTensorboardTimeSeriesData][google.cloud.aiplatform.v1beta1.TensorboardService.BatchReadTensorboardTimeSeriesData].
message BatchReadTensorboardTimeSeriesDataResponse {
  // The returned time series data.
  repeated TimeSeriesData time_series_data = 1;
}

// Request message for
// [TensorboardService.ReadTensorboardTimeSeriesData][google.cloud.aiplatform.v1beta1.TensorboardService.ReadTensorboardTimeSeriesData].
message ReadTensorboardTimeSeriesDataRequest {
  // Required. The resource name of the TensorboardTimeSeries to read data from.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}/timeSeries/{time_series}`
  string tensorboard_time_series = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardTimeSeries"
    }
  ];

  // The maximum number of TensorboardTimeSeries' data to return.
  //
  // This value should be a positive integer.
  // This value can be set to -1 to return all data.
  int32 max_data_points = 2;

  // Reads the TensorboardTimeSeries' data that match the filter expression.
  string filter = 3;
}

// Response message for
// [TensorboardService.ReadTensorboardTimeSeriesData][google.cloud.aiplatform.v1beta1.TensorboardService.ReadTensorboardTimeSeriesData].
message ReadTensorboardTimeSeriesDataResponse {
  // The returned time series data.
  TimeSeriesData time_series_data = 1;
}

// Request message for
// [TensorboardService.WriteTensorboardExperimentData][google.cloud.aiplatform.v1beta1.TensorboardService.WriteTensorboardExperimentData].
message WriteTensorboardExperimentDataRequest {
  // Required. The resource name of the TensorboardExperiment to write data to.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}`
  string tensorboard_experiment = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardExperiment"
    }
  ];

  // Required. Requests containing per-run TensorboardTimeSeries data to write.
  repeated WriteTensorboardRunDataRequest write_run_data_requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [TensorboardService.WriteTensorboardExperimentData][google.cloud.aiplatform.v1beta1.TensorboardService.WriteTensorboardExperimentData].
message WriteTensorboardExperimentDataResponse {}

// Request message for
// [TensorboardService.WriteTensorboardRunData][google.cloud.aiplatform.v1beta1.TensorboardService.WriteTensorboardRunData].
message WriteTensorboardRunDataRequest {
  // Required. The resource name of the TensorboardRun to write data to.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}`
  string tensorboard_run = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardRun"
    }
  ];

  // Required. The TensorboardTimeSeries data to write.
  // Values with in a time series are indexed by their step value.
  // Repeated writes to the same step will overwrite the existing value for that
  // step.
  // The upper limit of data points per write request is 5000.
  repeated TimeSeriesData time_series_data = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [TensorboardService.WriteTensorboardRunData][google.cloud.aiplatform.v1beta1.TensorboardService.WriteTensorboardRunData].
message WriteTensorboardRunDataResponse {}

// Request message for
// [TensorboardService.ExportTensorboardTimeSeriesData][google.cloud.aiplatform.v1beta1.TensorboardService.ExportTensorboardTimeSeriesData].
message ExportTensorboardTimeSeriesDataRequest {
  // Required. The resource name of the TensorboardTimeSeries to export data
  // from. Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}/timeSeries/{time_series}`
  string tensorboard_time_series = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TensorboardTimeSeries"
    }
  ];

  // Exports the TensorboardTimeSeries' data that match the filter expression.
  string filter = 2;

  // The maximum number of data points to return per page.
  // The default page_size is 1000. Values must be between 1 and 10000.
  // Values above 10000 are coerced to 10000.
  int32 page_size = 3;

  // A page token, received from a previous
  // [ExportTensorboardTimeSeriesData][google.cloud.aiplatform.v1beta1.TensorboardService.ExportTensorboardTimeSeriesData]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [ExportTensorboardTimeSeriesData][google.cloud.aiplatform.v1beta1.TensorboardService.ExportTensorboardTimeSeriesData]
  // must match the call that provided the page token.
  string page_token = 4;

  // Field to use to sort the TensorboardTimeSeries' data.
  // By default, TensorboardTimeSeries' data is returned in a pseudo random
  // order.
  string order_by = 5;
}

// Response message for
// [TensorboardService.ExportTensorboardTimeSeriesData][google.cloud.aiplatform.v1beta1.TensorboardService.ExportTensorboardTimeSeriesData].
message ExportTensorboardTimeSeriesDataResponse {
  // The returned time series data points.
  repeated TimeSeriesDataPoint time_series_data_points = 1;

  // A token, which can be sent as
  // [page_token][google.cloud.aiplatform.v1beta1.ExportTensorboardTimeSeriesDataRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Details of operations that perform create Tensorboard.
message CreateTensorboardOperationMetadata {
  // Operation metadata for Tensorboard.
  GenericOperationMetadata generic_metadata = 1;
}

// Details of operations that perform update Tensorboard.
message UpdateTensorboardOperationMetadata {
  // Operation metadata for Tensorboard.
  GenericOperationMetadata generic_metadata = 1;
}
