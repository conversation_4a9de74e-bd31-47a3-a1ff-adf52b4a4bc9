// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/cloud/aiplatform/v1beta1/schedule.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ScheduleServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service for creating and managing Vertex AI's Schedule resources to
// periodically launch shceudled runs to make API calls.
service ScheduleService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a Schedule.
  rpc CreateSchedule(CreateScheduleRequest) returns (Schedule) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/schedules"
      body: "schedule"
    };
    option (google.api.method_signature) = "parent,schedule";
  }

  // Deletes a Schedule.
  rpc DeleteSchedule(DeleteScheduleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/schedules/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Gets a Schedule.
  rpc GetSchedule(GetScheduleRequest) returns (Schedule) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/schedules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Schedules in a Location.
  rpc ListSchedules(ListSchedulesRequest) returns (ListSchedulesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/schedules"
    };
    option (google.api.method_signature) = "parent";
  }

  // Pauses a Schedule. Will mark
  // [Schedule.state][google.cloud.aiplatform.v1beta1.Schedule.state] to
  // 'PAUSED'. If the schedule is paused, no new runs will be created. Already
  // created runs will NOT be paused or canceled.
  rpc PauseSchedule(PauseScheduleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/schedules/*}:pause"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Resumes a paused Schedule to start scheduling new runs. Will mark
  // [Schedule.state][google.cloud.aiplatform.v1beta1.Schedule.state] to
  // 'ACTIVE'. Only paused Schedule can be resumed.
  //
  // When the Schedule is resumed, new runs will be scheduled starting from the
  // next execution time after the current time based on the time_specification
  // in the Schedule. If [Schedule.catchUp][] is set up true, all
  // missed runs will be scheduled for backfill first.
  rpc ResumeSchedule(ResumeScheduleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/schedules/*}:resume"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.api.method_signature) = "name,catch_up";
  }

  // Updates an active or paused Schedule.
  //
  // When the Schedule is updated, new runs will be scheduled starting from the
  // updated next execution time after the update time based on the
  // time_specification in the updated Schedule. All unstarted runs before the
  // update time will be skipped while already created runs will NOT be paused
  // or canceled.
  rpc UpdateSchedule(UpdateScheduleRequest) returns (Schedule) {
    option (google.api.http) = {
      patch: "/v1beta1/{schedule.name=projects/*/locations/*/schedules/*}"
      body: "schedule"
    };
    option (google.api.method_signature) = "schedule,update_mask";
  }
}

// Request message for
// [ScheduleService.CreateSchedule][google.cloud.aiplatform.v1beta1.ScheduleService.CreateSchedule].
message CreateScheduleRequest {
  // Required. The resource name of the Location to create the Schedule in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The Schedule to create.
  Schedule schedule = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ScheduleService.GetSchedule][google.cloud.aiplatform.v1beta1.ScheduleService.GetSchedule].
message GetScheduleRequest {
  // Required. The name of the Schedule resource.
  // Format:
  // `projects/{project}/locations/{location}/schedules/{schedule}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Schedule"
    }
  ];
}

// Request message for
// [ScheduleService.ListSchedules][google.cloud.aiplatform.v1beta1.ScheduleService.ListSchedules].
message ListSchedulesRequest {
  // Required. The resource name of the Location to list the Schedules from.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Lists the Schedules that match the filter expression. The following
  // fields are supported:
  //
  // * `display_name`: Supports `=`, `!=` comparisons, and `:` wildcard.
  // * `state`: Supports `=` and `!=` comparisons.
  // * `request`: Supports existence of the <request_type> check.
  //       (e.g. `create_pipeline_job_request:*` --> Schedule has
  //       create_pipeline_job_request).
  // * `create_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
  //       Values must be in RFC 3339 format.
  // * `start_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
  //       Values must be in RFC 3339 format.
  // * `end_time`: Supports `=`, `!=`, `<`, `>`, `<=`, `>=` comparisons and `:*`
  //       existence check. Values must be in RFC 3339 format.
  // * `next_run_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=`
  //       comparisons. Values must be in RFC 3339 format.
  //
  //
  // Filter expressions can be combined together using logical operators
  // (`NOT`, `AND` & `OR`).
  // The syntax to define filter expression is based on
  // https://google.aip.dev/160.
  //
  // Examples:
  //
  // * `state="ACTIVE" AND display_name:"my_schedule_*"`
  // * `NOT display_name="my_schedule"`
  // * `create_time>"2021-05-18T00:00:00Z"`
  // * `end_time>"2021-05-18T00:00:00Z" OR NOT end_time:*`
  // * `create_pipeline_job_request:*`
  string filter = 2;

  // The standard list page size.
  // Default to 100 if not specified.
  int32 page_size = 3;

  // The standard list page token.
  // Typically obtained via
  // [ListSchedulesResponse.next_page_token][google.cloud.aiplatform.v1beta1.ListSchedulesResponse.next_page_token]
  // of the previous
  // [ScheduleService.ListSchedules][google.cloud.aiplatform.v1beta1.ScheduleService.ListSchedules]
  // call.
  string page_token = 4;

  // A comma-separated list of fields to order by. The default sort order is in
  // ascending order. Use "desc" after a field name for descending. You can have
  // multiple order_by fields provided.
  //
  // For example, using "create_time desc, end_time" will order results by
  // create time in descending order, and if there are multiple schedules having
  // the same create time, order them by the end time in ascending order.
  //
  // If order_by is not specified, it will order by default with create_time in
  // descending order.
  //
  // Supported fields:
  //
  //   * `create_time`
  //   * `start_time`
  //   * `end_time`
  //   * `next_run_time`
  string order_by = 5;
}

// Response message for
// [ScheduleService.ListSchedules][google.cloud.aiplatform.v1beta1.ScheduleService.ListSchedules]
message ListSchedulesResponse {
  // List of Schedules in the requested page.
  repeated Schedule schedules = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListSchedulesRequest.page_token][google.cloud.aiplatform.v1beta1.ListSchedulesRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [ScheduleService.DeleteSchedule][google.cloud.aiplatform.v1beta1.ScheduleService.DeleteSchedule].
message DeleteScheduleRequest {
  // Required. The name of the Schedule resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/schedules/{schedule}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Schedule"
    }
  ];
}

// Request message for
// [ScheduleService.PauseSchedule][google.cloud.aiplatform.v1beta1.ScheduleService.PauseSchedule].
message PauseScheduleRequest {
  // Required. The name of the Schedule resource to be paused.
  // Format:
  // `projects/{project}/locations/{location}/schedules/{schedule}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Schedule"
    }
  ];
}

// Request message for
// [ScheduleService.ResumeSchedule][google.cloud.aiplatform.v1beta1.ScheduleService.ResumeSchedule].
message ResumeScheduleRequest {
  // Required. The name of the Schedule resource to be resumed.
  // Format:
  // `projects/{project}/locations/{location}/schedules/{schedule}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Schedule"
    }
  ];

  // Optional. Whether to backfill missed runs when the schedule is resumed from
  // PAUSED state. If set to true, all missed runs will be scheduled. New runs
  // will be scheduled after the backfill is complete. This will also update
  // [Schedule.catch_up][google.cloud.aiplatform.v1beta1.Schedule.catch_up]
  // field. Default to false.
  bool catch_up = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [ScheduleService.UpdateSchedule][google.cloud.aiplatform.v1beta1.ScheduleService.UpdateSchedule].
message UpdateScheduleRequest {
  // Required. The Schedule which replaces the resource on the server.
  // The following restrictions will be applied:
  //
  //   * The scheduled request type cannot be changed.
  //   * The non-empty fields cannot be unset.
  //   * The output_only fields will be ignored if specified.
  Schedule schedule = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource. See
  // [google.protobuf.FieldMask][google.protobuf.FieldMask].
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}
