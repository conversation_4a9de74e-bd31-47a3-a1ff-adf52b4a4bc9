// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/explanation.proto";
import "google/cloud/aiplatform/v1beta1/model_monitoring_spec.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ModelMonitorProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Vertex AI Model Monitoring Service serves as a central hub for the analysis
// and visualization of data quality and performance related to models.
// ModelMonitor stands as a top level resource for overseeing your model
// monitoring tasks.
message ModelMonitor {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/ModelMonitor"
    pattern: "projects/{project}/locations/{location}/modelMonitors/{model_monitor}"
  };

  // The monitoring target refers to the entity that is subject to analysis.
  // e.g. Vertex AI Model version.
  message ModelMonitoringTarget {
    // Model in Vertex AI Model Registry.
    message VertexModelSource {
      // Model resource name. Format:
      // projects/{project}/locations/{location}/models/{model}.
      string model = 1 [(google.api.resource_reference) = {
        type: "aiplatform.googleapis.com/Model"
      }];

      // Model version id.
      string model_version_id = 2;
    }

    oneof source {
      // Model in Vertex AI Model Registry.
      VertexModelSource vertex_model = 1;
    }
  }

  // Optional default monitoring objective, it can be overridden in the
  // ModelMonitoringJob objective spec.
  oneof default_objective {
    // Optional default tabular model monitoring objective.
    ModelMonitoringObjectiveSpec.TabularObjective tabular_objective = 11;
  }

  // Immutable. Resource name of the ModelMonitor. Format:
  // `projects/{project}/locations/{location}/modelMonitors/{model_monitor}`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // The display name of the ModelMonitor.
  // The name can be up to 128 characters long and can consist of any UTF-8.
  string display_name = 2;

  // The entity that is subject to analysis.
  // Currently only models in Vertex AI Model Registry are supported. If you
  // want to analyze the model which is outside the Vertex AI, you could
  // register a model in Vertex AI Model Registry using just a display name.
  ModelMonitoringTarget model_monitoring_target = 3;

  // Optional training dataset used to train the model.
  // It can serve as a reference dataset to identify changes in production.
  ModelMonitoringInput training_dataset = 10;

  // Optional default notification spec, it can be overridden in the
  // ModelMonitoringJob notification spec.
  ModelMonitoringNotificationSpec notification_spec = 12;

  // Optional default monitoring metrics/logs export spec, it can be overridden
  // in the ModelMonitoringJob output spec.
  // If not specified, a default Google Cloud Storage bucket will be created
  // under your project.
  ModelMonitoringOutputSpec output_spec = 13;

  // Optional model explanation spec. It is used for feature attribution
  // monitoring.
  ExplanationSpec explanation_spec = 16;

  // Monitoring Schema is to specify the model's features, prediction outputs
  // and ground truth properties. It is used to extract pertinent data from the
  // dataset and to process features based on their properties.
  // Make sure that the schema aligns with your dataset, if it does not, we will
  // be unable to extract data from the dataset.
  // It is required for most models, but optional for Vertex AI AutoML Tables
  // unless the schem information is not available.
  ModelMonitoringSchema model_monitoring_schema = 9;

  // Output only. Timestamp when this ModelMonitor was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this ModelMonitor was updated most recently.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 18 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The Model Monitoring Schema definition.
message ModelMonitoringSchema {
  // Schema field definition.
  message FieldSchema {
    // Field name.
    string name = 1;

    // Supported data types are:
    // `float`
    // `integer`
    // `boolean`
    // `string`
    // `categorical`
    string data_type = 2;

    // Describes if the schema field is an array of given data type.
    bool repeated = 3;
  }

  // Feature names of the model. Vertex AI will try to match the features from
  // your dataset as follows:
  //  * For 'csv' files, the header names are required, and we will extract the
  //    corresponding feature values when the header names align with the
  //    feature names.
  //  * For 'jsonl' files, we will extract the corresponding feature values if
  //    the key names match the feature names.
  //    Note: Nested features are not supported, so please ensure your features
  //    are flattened. Ensure the feature values are scalar or an array of
  //    scalars.
  //  * For 'bigquery' dataset, we will extract the corresponding feature values
  //    if the column names match the feature names.
  //    Note: The column type can be a scalar or an array of scalars. STRUCT or
  //    JSON types are not supported. You may use SQL queries to select or
  //    aggregate the relevant features from your original table. However,
  //    ensure that the 'schema' of the query results meets our requirements.
  //  * For the Vertex AI Endpoint Request Response Logging table or Vertex AI
  //    Batch Prediction Job results. If the
  //    [instance_type][google.cloud.aiplatform.v1beta1.ModelMonitoringSchema.instance_type]
  //    is an array, ensure that the sequence in
  //    [feature_fields][google.cloud.aiplatform.v1beta1.ModelMonitoringSchema.feature_fields]
  //    matches the order of features in the prediction instance. We will match
  //    the feature with the array in the order specified in [feature_fields].
  repeated FieldSchema feature_fields = 1;

  // Prediction output names of the model. The requirements are the same as the
  // [feature_fields][google.cloud.aiplatform.v1beta1.ModelMonitoringSchema.feature_fields].
  // For AutoML Tables, the prediction output name presented in schema will be:
  // `predicted_{target_column}`, the `target_column` is the one you specified
  // when you train the model.
  // For Prediction output drift analysis:
  //  * AutoML Classification, the distribution of the argmax label will be
  //    analyzed.
  //  * AutoML Regression, the distribution of the value will be analyzed.
  repeated FieldSchema prediction_fields = 2;

  // Target /ground truth names of the model.
  repeated FieldSchema ground_truth_fields = 3;
}
