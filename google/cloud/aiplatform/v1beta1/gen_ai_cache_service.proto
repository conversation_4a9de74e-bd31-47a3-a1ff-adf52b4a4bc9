// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/cached_content.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "GenAiCacheServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Service for managing Vertex AI's CachedContent resource.
service GenAiCacheService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates cached content, this call will initialize the cached content in the
  // data storage, and users need to pay for the cache data storage.
  rpc CreateCachedContent(CreateCachedContentRequest) returns (CachedContent) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/cachedContents"
      body: "cached_content"
    };
    option (google.api.method_signature) = "parent,cached_content";
  }

  // Gets cached content configurations
  rpc GetCachedContent(GetCachedContentRequest) returns (CachedContent) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/cachedContents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates cached content configurations
  rpc UpdateCachedContent(UpdateCachedContentRequest) returns (CachedContent) {
    option (google.api.http) = {
      patch: "/v1beta1/{cached_content.name=projects/*/locations/*/cachedContents/*}"
      body: "cached_content"
    };
    option (google.api.method_signature) = "cached_content,update_mask";
  }

  // Deletes cached content
  rpc DeleteCachedContent(DeleteCachedContentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/cachedContents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists cached contents in a project
  rpc ListCachedContents(ListCachedContentsRequest)
      returns (ListCachedContentsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/cachedContents"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [GenAiCacheService.CreateCachedContent][google.cloud.aiplatform.v1beta1.GenAiCacheService.CreateCachedContent].
message CreateCachedContentRequest {
  // Required. The parent resource where the cached content will be created
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/CachedContent"
    }
  ];

  // Required. The cached content to create
  CachedContent cached_content = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [GenAiCacheService.GetCachedContent][google.cloud.aiplatform.v1beta1.GenAiCacheService.GetCachedContent].
message GetCachedContentRequest {
  // Required. The resource name referring to the cached content
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/CachedContent"
    }
  ];
}

// Request message for
// [GenAiCacheService.UpdateCachedContent][google.cloud.aiplatform.v1beta1.GenAiCacheService.UpdateCachedContent].
// Only expire_time or ttl can be updated.
message UpdateCachedContentRequest {
  // Required. The cached content to update
  CachedContent cached_content = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [GenAiCacheService.DeleteCachedContent][google.cloud.aiplatform.v1beta1.GenAiCacheService.DeleteCachedContent].
message DeleteCachedContentRequest {
  // Required. The resource name referring to the cached content
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/CachedContent"
    }
  ];
}

// Request to list CachedContents.
message ListCachedContentsRequest {
  // Required. The parent, which owns this collection of cached contents.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/CachedContent"
    }
  ];

  // Optional. The maximum number of cached contents to return. The service may
  // return fewer than this value. If unspecified, some default (under maximum)
  // number of items will be returned. The maximum value is 1000; values above
  // 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListCachedContents` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCachedContents` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response with a list of CachedContents.
message ListCachedContentsResponse {
  // List of cached contents.
  repeated CachedContent cached_contents = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
