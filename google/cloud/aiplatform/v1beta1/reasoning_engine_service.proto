// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/cloud/aiplatform/v1beta1/reasoning_engine.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ReasoningEngineServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service for managing Vertex AI's Reasoning Engines.
service ReasoningEngineService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a reasoning engine.
  rpc CreateReasoningEngine(CreateReasoningEngineRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/reasoningEngines"
      body: "reasoning_engine"
    };
    option (google.api.method_signature) = "parent,reasoning_engine";
    option (google.longrunning.operation_info) = {
      response_type: "ReasoningEngine"
      metadata_type: "CreateReasoningEngineOperationMetadata"
    };
  }

  // Gets a reasoning engine.
  rpc GetReasoningEngine(GetReasoningEngineRequest) returns (ReasoningEngine) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/reasoningEngines/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists reasoning engines in a location.
  rpc ListReasoningEngines(ListReasoningEnginesRequest)
      returns (ListReasoningEnginesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/reasoningEngines"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a reasoning engine.
  rpc UpdateReasoningEngine(UpdateReasoningEngineRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{reasoning_engine.name=projects/*/locations/*/reasoningEngines/*}"
      body: "reasoning_engine"
    };
    option (google.api.method_signature) = "reasoning_engine,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ReasoningEngine"
      metadata_type: "UpdateReasoningEngineOperationMetadata"
    };
  }

  // Deletes a reasoning engine.
  rpc DeleteReasoningEngine(DeleteReasoningEngineRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/reasoningEngines/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }
}

// Request message for
// [ReasoningEngineService.CreateReasoningEngine][google.cloud.aiplatform.v1beta1.ReasoningEngineService.CreateReasoningEngine].
message CreateReasoningEngineRequest {
  // Required. The resource name of the Location to create the ReasoningEngine
  // in. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The ReasoningEngine to create.
  ReasoningEngine reasoning_engine = 2 [(google.api.field_behavior) = REQUIRED];
}

// Details of
// [ReasoningEngineService.CreateReasoningEngine][google.cloud.aiplatform.v1beta1.ReasoningEngineService.CreateReasoningEngine]
// operation.
message CreateReasoningEngineOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [ReasoningEngineService.GetReasoningEngine][google.cloud.aiplatform.v1beta1.ReasoningEngineService.GetReasoningEngine].
message GetReasoningEngineRequest {
  // Required. The name of the ReasoningEngine resource.
  // Format:
  // `projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ReasoningEngine"
    }
  ];
}

// Request message for
// [ReasoningEngineService.UpdateReasoningEngine][google.cloud.aiplatform.v1beta1.ReasoningEngineService.UpdateReasoningEngine].
message UpdateReasoningEngineRequest {
  // Required. The ReasoningEngine which replaces the resource on the server.
  ReasoningEngine reasoning_engine = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Mask specifying which fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Details of
// [ReasoningEngineService.UpdateReasoningEngine][google.cloud.aiplatform.v1beta1.ReasoningEngineService.UpdateReasoningEngine]
// operation.
message UpdateReasoningEngineOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [ReasoningEngineService.ListReasoningEngines][google.cloud.aiplatform.v1beta1.ReasoningEngineService.ListReasoningEngines].
message ListReasoningEnginesRequest {
  // Required. The resource name of the Location to list the ReasoningEngines
  // from. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The standard list filter.
  // More detail in [AIP-160](https://google.aip.dev/160).
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page size.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [ReasoningEngineService.ListReasoningEngines][google.cloud.aiplatform.v1beta1.ReasoningEngineService.ListReasoningEngines]
message ListReasoningEnginesResponse {
  // List of ReasoningEngines in the requested page.
  repeated ReasoningEngine reasoning_engines = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListReasoningEnginesRequest.page_token][google.cloud.aiplatform.v1beta1.ListReasoningEnginesRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [ReasoningEngineService.DeleteReasoningEngine][google.cloud.aiplatform.v1beta1.ReasoningEngineService.DeleteReasoningEngine].
message DeleteReasoningEngineRequest {
  // Required. The name of the ReasoningEngine resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/reasoningEngines/{reasoning_engine}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/ReasoningEngine"
    }
  ];
}
