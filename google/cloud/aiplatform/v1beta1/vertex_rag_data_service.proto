// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1beta1/operation.proto";
import "google/cloud/aiplatform/v1beta1/vertex_rag_data.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "VertexRagDataServiceProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// A service for managing user data for RAG.
service VertexRagDataService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a RagCorpus.
  rpc CreateRagCorpus(CreateRagCorpusRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/ragCorpora"
      body: "rag_corpus"
    };
    option (google.api.method_signature) = "parent,rag_corpus";
    option (google.longrunning.operation_info) = {
      response_type: "RagCorpus"
      metadata_type: "CreateRagCorpusOperationMetadata"
    };
  }

  // Updates a RagCorpus.
  rpc UpdateRagCorpus(UpdateRagCorpusRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{rag_corpus.name=projects/*/locations/*/ragCorpora/*}"
      body: "rag_corpus"
    };
    option (google.api.method_signature) = "rag_corpus";
    option (google.longrunning.operation_info) = {
      response_type: "RagCorpus"
      metadata_type: "UpdateRagCorpusOperationMetadata"
    };
  }

  // Gets a RagCorpus.
  rpc GetRagCorpus(GetRagCorpusRequest) returns (RagCorpus) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/ragCorpora/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists RagCorpora in a Location.
  rpc ListRagCorpora(ListRagCorporaRequest) returns (ListRagCorporaResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/ragCorpora"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a RagCorpus.
  rpc DeleteRagCorpus(DeleteRagCorpusRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/ragCorpora/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Upload a file into a RagCorpus.
  rpc UploadRagFile(UploadRagFileRequest) returns (UploadRagFileResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/ragCorpora/*}/ragFiles:upload"
      body: "*"
    };
    option (google.api.method_signature) =
        "parent,rag_file,upload_rag_file_config";
  }

  // Import files from Google Cloud Storage or Google Drive into a RagCorpus.
  rpc ImportRagFiles(ImportRagFilesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/ragCorpora/*}/ragFiles:import"
      body: "*"
    };
    option (google.api.method_signature) = "parent,import_rag_files_config";
    option (google.longrunning.operation_info) = {
      response_type: "ImportRagFilesResponse"
      metadata_type: "ImportRagFilesOperationMetadata"
    };
  }

  // Gets a RagFile.
  rpc GetRagFile(GetRagFileRequest) returns (RagFile) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists RagFiles in a RagCorpus.
  rpc ListRagFiles(ListRagFilesRequest) returns (ListRagFilesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/ragCorpora/*}/ragFiles"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a RagFile.
  rpc DeleteRagFile(DeleteRagFileRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }
}

// Request message for
// [VertexRagDataService.CreateRagCorpus][google.cloud.aiplatform.v1beta1.VertexRagDataService.CreateRagCorpus].
message CreateRagCorpusRequest {
  // Required. The resource name of the Location to create the RagCorpus in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The RagCorpus to create.
  RagCorpus rag_corpus = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [VertexRagDataService.GetRagCorpus][google.cloud.aiplatform.v1beta1.VertexRagDataService.GetRagCorpus]
message GetRagCorpusRequest {
  // Required. The name of the RagCorpus resource.
  // Format:
  // `projects/{project}/locations/{location}/ragCorpora/{rag_corpus}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/RagCorpus"
    }
  ];
}

// Request message for
// [VertexRagDataService.ListRagCorpora][google.cloud.aiplatform.v1beta1.VertexRagDataService.ListRagCorpora].
message ListRagCorporaRequest {
  // Required. The resource name of the Location from which to list the
  // RagCorpora. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The standard list page size.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  // Typically obtained via
  // [ListRagCorporaResponse.next_page_token][google.cloud.aiplatform.v1beta1.ListRagCorporaResponse.next_page_token]
  // of the previous
  // [VertexRagDataService.ListRagCorpora][google.cloud.aiplatform.v1beta1.VertexRagDataService.ListRagCorpora]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [VertexRagDataService.ListRagCorpora][google.cloud.aiplatform.v1beta1.VertexRagDataService.ListRagCorpora].
message ListRagCorporaResponse {
  // List of RagCorpora in the requested page.
  repeated RagCorpus rag_corpora = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListRagCorporaRequest.page_token][google.cloud.aiplatform.v1beta1.ListRagCorporaRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [VertexRagDataService.DeleteRagCorpus][google.cloud.aiplatform.v1beta1.VertexRagDataService.DeleteRagCorpus].
message DeleteRagCorpusRequest {
  // Required. The name of the RagCorpus resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/ragCorpora/{rag_corpus}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/RagCorpus"
    }
  ];

  // Optional. If set to true, any RagFiles in this RagCorpus will also be
  // deleted. Otherwise, the request will only work if the RagCorpus has no
  // RagFiles.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VertexRagDataService.UploadRagFile][google.cloud.aiplatform.v1beta1.VertexRagDataService.UploadRagFile].
message UploadRagFileRequest {
  // Required. The name of the RagCorpus resource into which to upload the file.
  // Format:
  // `projects/{project}/locations/{location}/ragCorpora/{rag_corpus}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/RagCorpus"
    }
  ];

  // Required. The RagFile to upload.
  RagFile rag_file = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The config for the RagFiles to be uploaded into the RagCorpus.
  // [VertexRagDataService.UploadRagFile][google.cloud.aiplatform.v1beta1.VertexRagDataService.UploadRagFile].
  UploadRagFileConfig upload_rag_file_config = 5
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [VertexRagDataService.UploadRagFile][google.cloud.aiplatform.v1beta1.VertexRagDataService.UploadRagFile].
message UploadRagFileResponse {
  // The result of the upload.
  oneof result {
    // The RagFile that had been uploaded into the RagCorpus.
    RagFile rag_file = 1;

    // The error that occurred while processing the RagFile.
    google.rpc.Status error = 4;
  }
}

// Request message for
// [VertexRagDataService.ImportRagFiles][google.cloud.aiplatform.v1beta1.VertexRagDataService.ImportRagFiles].
message ImportRagFilesRequest {
  // Required. The name of the RagCorpus resource into which to import files.
  // Format:
  // `projects/{project}/locations/{location}/ragCorpora/{rag_corpus}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/RagCorpus"
    }
  ];

  // Required. The config for the RagFiles to be synced and imported into the
  // RagCorpus.
  // [VertexRagDataService.ImportRagFiles][google.cloud.aiplatform.v1beta1.VertexRagDataService.ImportRagFiles].
  ImportRagFilesConfig import_rag_files_config = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [VertexRagDataService.ImportRagFiles][google.cloud.aiplatform.v1beta1.VertexRagDataService.ImportRagFiles].
message ImportRagFilesResponse {
  // The location into which the partial failures were written.
  oneof partial_failure_sink {
    // The Google Cloud Storage path into which the partial failures were
    // written.
    string partial_failures_gcs_path = 4;

    // The BigQuery table into which the partial failures were written.
    string partial_failures_bigquery_table = 5;
  }

  // The number of RagFiles that had been imported into the RagCorpus.
  int64 imported_rag_files_count = 1;

  // The number of RagFiles that had failed while importing into the RagCorpus.
  int64 failed_rag_files_count = 2;

  // The number of RagFiles that was skipped while importing into the RagCorpus.
  int64 skipped_rag_files_count = 3;
}

// Request message for
// [VertexRagDataService.GetRagFile][google.cloud.aiplatform.v1beta1.VertexRagDataService.GetRagFile]
message GetRagFileRequest {
  // Required. The name of the RagFile resource.
  // Format:
  // `projects/{project}/locations/{location}/ragCorpora/{rag_corpus}/ragFiles/{rag_file}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/RagFile"
    }
  ];
}

// Request message for
// [VertexRagDataService.ListRagFiles][google.cloud.aiplatform.v1beta1.VertexRagDataService.ListRagFiles].
message ListRagFilesRequest {
  // Required. The resource name of the RagCorpus from which to list the
  // RagFiles. Format:
  // `projects/{project}/locations/{location}/ragCorpora/{rag_corpus}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/RagCorpus"
    }
  ];

  // Optional. The standard list page size.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The standard list page token.
  // Typically obtained via
  // [ListRagFilesResponse.next_page_token][google.cloud.aiplatform.v1beta1.ListRagFilesResponse.next_page_token]
  // of the previous
  // [VertexRagDataService.ListRagFiles][google.cloud.aiplatform.v1beta1.VertexRagDataService.ListRagFiles]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [VertexRagDataService.ListRagFiles][google.cloud.aiplatform.v1beta1.VertexRagDataService.ListRagFiles].
message ListRagFilesResponse {
  // List of RagFiles in the requested page.
  repeated RagFile rag_files = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListRagFilesRequest.page_token][google.cloud.aiplatform.v1beta1.ListRagFilesRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [VertexRagDataService.DeleteRagFile][google.cloud.aiplatform.v1beta1.VertexRagDataService.DeleteRagFile].
message DeleteRagFileRequest {
  // Required. The name of the RagFile resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/ragCorpora/{rag_corpus}/ragFiles/{rag_file}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/RagFile"
    }
  ];
}

// Runtime operation information for
// [VertexRagDataService.CreateRagCorpus][google.cloud.aiplatform.v1beta1.VertexRagDataService.CreateRagCorpus].
message CreateRagCorpusOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [VertexRagDataService.UpdateRagCorpus][google.cloud.aiplatform.v1beta1.VertexRagDataService.UpdateRagCorpus].
message UpdateRagCorpusRequest {
  // Required. The RagCorpus which replaces the resource on the server.
  RagCorpus rag_corpus = 1 [(google.api.field_behavior) = REQUIRED];
}

// Runtime operation information for
// [VertexRagDataService.UpdateRagCorpus][google.cloud.aiplatform.v1beta1.VertexRagDataService.UpdateRagCorpus].
message UpdateRagCorpusOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;
}

// Runtime operation information for
// [VertexRagDataService.ImportRagFiles][google.cloud.aiplatform.v1beta1.VertexRagDataService.ImportRagFiles].
message ImportRagFilesOperationMetadata {
  // The operation generic information.
  GenericOperationMetadata generic_metadata = 1;

  // The resource ID of RagCorpus that this operation is executed on.
  int64 rag_corpus_id = 2;

  // Output only. The config that was passed in the ImportRagFilesRequest.
  ImportRagFilesConfig import_rag_files_config = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The progress percentage of the operation. Value is in the range [0, 100].
  // This percentage is calculated as follows:
  //    progress_percentage = 100 * (successes + failures + skips) / total
  int32 progress_percentage = 4;
}
