// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ModelMonitoringAlertProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Monitoring alert triggered condition.
message ModelMonitoringAlertCondition {
  // Alert triggered condition.
  oneof condition {
    // A condition that compares a stats value against a threshold. Alert will
    // be triggered if value above the threshold.
    double threshold = 1;
  }
}

// Represents a single model monitoring anomaly.
message ModelMonitoringAnomaly {
  // Tabular anomaly details.
  message TabularAnomaly {
    // Additional anomaly information. e.g. Google Cloud Storage uri.
    string anomaly_uri = 1;

    // Overview of this anomaly.
    string summary = 2;

    // Anomaly body.
    google.protobuf.Value anomaly = 3;

    // The time the anomaly was triggered.
    google.protobuf.Timestamp trigger_time = 4;

    // The alert condition associated with this anomaly.
    ModelMonitoringAlertCondition condition = 5;
  }

  oneof anomaly {
    // Tabular anomaly.
    TabularAnomaly tabular_anomaly = 1;
  }

  // Model monitoring job resource name.
  string model_monitoring_job = 2;

  // Algorithm used to calculated the metrics, eg: jensen_shannon_divergence,
  // l_infinity.
  string algorithm = 3;
}

// Represents a single monitoring alert. This is currently used in the
// SearchModelMonitoringAlerts api, thus the alert wrapped in this message
// belongs to the resource asked in the request.
message ModelMonitoringAlert {
  // The stats name.
  string stats_name = 1;

  // One of the supported monitoring objectives:
  // `raw-feature-drift`
  // `prediction-output-drift`
  // `feature-attribution`
  string objective_type = 2;

  // Alert creation time.
  google.protobuf.Timestamp alert_time = 3;

  // Anomaly details.
  ModelMonitoringAnomaly anomaly = 4;
}
