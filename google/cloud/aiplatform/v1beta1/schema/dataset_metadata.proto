// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema;


option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/schemapb;schemapb";
option java_multiple_files = true;
option java_outer_classname = "DatasetMetadataProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema";

// The metadata of Datasets that contain Image DataItems.
message ImageDatasetMetadata {
  // Points to a YAML file stored on Google Cloud Storage describing payload of
  // the Image DataItems that belong to this Dataset.
  string data_item_schema_uri = 1;

  // Google Cloud Storage Bucket name that contains the blob data of this
  // Dataset.
  string gcs_bucket = 2;
}

// The metadata of Datasets that contain Text DataItems.
message TextDatasetMetadata {
  // Points to a YAML file stored on Google Cloud Storage describing payload of
  // the Text DataItems that belong to this Dataset.
  string data_item_schema_uri = 1;

  // Google Cloud Storage Bucket name that contains the blob data of this
  // Dataset.
  string gcs_bucket = 2;
}

// The metadata of Datasets that contain Video DataItems.
message VideoDatasetMetadata {
  // Points to a YAML file stored on Google Cloud Storage describing payload of
  // the Video DataItems that belong to this Dataset.
  string data_item_schema_uri = 1;

  // Google Cloud Storage Bucket name that contains the blob data of this
  // Dataset.
  string gcs_bucket = 2;
}

// The metadata of Datasets that contain tables data.
message TablesDatasetMetadata {
  // The tables Dataset's data source. The Dataset doesn't store the data
  // directly, but only pointer(s) to its data.
  message InputConfig {
    oneof source {
      GcsSource gcs_source = 1;

      BigQuerySource bigquery_source = 2;
    }
  }

  message GcsSource {
    // Cloud Storage URI of one or more files. Only CSV files are supported.
    // The first line of the CSV file is used as the header.
    // If there are multiple files, the header is the first line of
    // the lexicographically first file, the other files must either
    // contain the exact same header or omit the header.
    repeated string uri = 1;
  }

  message BigQuerySource {
    // The URI of a BigQuery table.
    // e.g. bq://projectId.bqDatasetId.bqTableId
    string uri = 1;
  }

  InputConfig input_config = 1;
}

// The metadata of Datasets that contain time series data.
message TimeSeriesDatasetMetadata {
  // The time series Dataset's data source. The Dataset doesn't store the data
  // directly, but only pointer(s) to its data.
  message InputConfig {
    oneof source {
      GcsSource gcs_source = 1;

      BigQuerySource bigquery_source = 2;
    }
  }

  message GcsSource {
    // Cloud Storage URI of one or more files. Only CSV files are supported.
    // The first line of the CSV file is used as the header.
    // If there are multiple files, the header is the first line of
    // the lexicographically first file, the other files must either
    // contain the exact same header or omit the header.
    repeated string uri = 1;
  }

  message BigQuerySource {
    // The URI of a BigQuery table.
    string uri = 1;
  }

  InputConfig input_config = 1;

  // The column name of the time series identifier column that identifies the
  // time series.
  string time_series_identifier_column = 2;

  // The column name of the time column that identifies time order in the time
  // series.
  string time_column = 3;
}
