# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "definition_proto",
    srcs = [
        "automl_image_classification.proto",
        "automl_image_object_detection.proto",
        "automl_image_segmentation.proto",
        "automl_tables.proto",
        "automl_text_classification.proto",
        "automl_text_extraction.proto",
        "automl_text_sentiment.proto",
        "automl_time_series_forecasting.proto",
        "automl_video_action_recognition.proto",
        "automl_video_classification.proto",
        "automl_video_object_tracking.proto",
        "export_evaluated_data_items_config.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:field_behavior_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "definition_java_proto",
    deps = [":definition_proto"],
)

java_grpc_library(
    name = "definition_java_grpc",
    srcs = [":definition_proto"],
    deps = [":definition_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "definition_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/aiplatform/apiv1beta1/schema/trainingjob/definition/definitionpb",
    protos = [":definition_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:field_behavior_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "definition_moved_proto",
    srcs = [":definition_proto"],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:field_behavior_proto",
    ],
)

py_proto_library(
    name = "definition_py_proto",
    deps = [":definition_moved_proto"],
)

py_grpc_library(
    name = "definition_py_grpc",
    srcs = [":definition_moved_proto"],
    deps = [":definition_py_proto"],
)

py_gapic_library(
    name = "definition_py_gapic",
    srcs = [":definition_proto"],
    opt_args = [
        "python-gapic-namespace=google.cloud.aiplatform.v1beta1.schema.trainingjob",
        "python-gapic-name=definition",
    ],
    rest_numeric_enums = False,
    transport = "grpc",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "definition_py",
    deps = [
        ":definition_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "definition_php_proto",
    deps = [":definition_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "definition_ruby_proto",
    deps = [":definition_proto"],
)

ruby_grpc_library(
    name = "definition_ruby_grpc",
    srcs = [":definition_proto"],
    deps = [":definition_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "definition_csharp_proto",
    deps = [":definition_proto"],
)

csharp_grpc_library(
    name = "definition_csharp_grpc",
    srcs = [":definition_proto"],
    deps = [":definition_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
