// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema;

import "google/cloud/aiplatform/v1beta1/schema/annotation_spec_color.proto";
import "google/cloud/aiplatform/v1beta1/schema/geometry.proto";
import "google/cloud/aiplatform/v1beta1/schema/predict/instance/text_sentiment.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/struct.proto";
import "google/rpc/code.proto";
import "google/api/annotations.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/schemapb;schemapb";
option java_multiple_files = true;
option java_outer_classname = "IoFormatProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema";

// Represents a line of JSONL in the batch prediction output file.
message PredictionResult {
  message Error {
    // Error status. This will be serialized into the enum name e.g.
    // "NOT_FOUND".
    google.rpc.Code status = 1;

    // Error message with additional details.
    string message = 2;
  }

  // Some identifier from the input so that the prediction can be mapped back to
  // the input instance.
  oneof input {
    // User's input instance.
    // Struct is used here instead of Any so that JsonFormat does not append an
    // extra "@type" field when we convert the proto to JSON.
    google.protobuf.Struct instance = 1;

    // Optional user-provided key from the input instance.
    string key = 2;
  }

  // The prediction result.
  // Value is used here instead of Any so that JsonFormat does not append an
  // extra "@type" field when we convert the proto to JSON and so we can
  // represent array of objects.
  // Do not set error if this is set.
  google.protobuf.Value prediction = 3;

  // The error result.
  // Do not set prediction if this is set.
  Error error = 4;
}
