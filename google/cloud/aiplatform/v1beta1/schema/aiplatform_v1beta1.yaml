type: google.api.Service
config_version: 3
name: aiplatform.googleapis.com
title: Vertex AI API

types:
- name: google.cloud.aiplatform.v1beta1.schema.ImageBoundingBoxAnnotation
- name: google.cloud.aiplatform.v1beta1.schema.ImageClassificationAnnotation
- name: google.cloud.aiplatform.v1beta1.schema.ImageDataItem
- name: google.cloud.aiplatform.v1beta1.schema.ImageDatasetMetadata
- name: google.cloud.aiplatform.v1beta1.schema.ImageSegmentationAnnotation
- name: google.cloud.aiplatform.v1beta1.schema.PredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.TablesDatasetMetadata
- name: google.cloud.aiplatform.v1beta1.schema.TextClassificationAnnotation
- name: google.cloud.aiplatform.v1beta1.schema.TextDataItem
- name: google.cloud.aiplatform.v1beta1.schema.TextDatasetMetadata
- name: google.cloud.aiplatform.v1beta1.schema.TextExtractionAnnotation
- name: google.cloud.aiplatform.v1beta1.schema.TextSentimentAnnotation
- name: google.cloud.aiplatform.v1beta1.schema.TimeSeriesDatasetMetadata
- name: google.cloud.aiplatform.v1beta1.schema.VideoActionRecognitionAnnotation
- name: google.cloud.aiplatform.v1beta1.schema.VideoClassificationAnnotation
- name: google.cloud.aiplatform.v1beta1.schema.VideoDataItem
- name: google.cloud.aiplatform.v1beta1.schema.VideoDatasetMetadata
- name: google.cloud.aiplatform.v1beta1.schema.VideoObjectTrackingAnnotation
- name: google.cloud.aiplatform.v1beta1.schema.predict.instance.ImageClassificationPredictionInstance
- name: google.cloud.aiplatform.v1beta1.schema.predict.instance.ImageObjectDetectionPredictionInstance
- name: google.cloud.aiplatform.v1beta1.schema.predict.instance.ImageSegmentationPredictionInstance
- name: google.cloud.aiplatform.v1beta1.schema.predict.instance.TextClassificationPredictionInstance
- name: google.cloud.aiplatform.v1beta1.schema.predict.instance.TextExtractionPredictionInstance
- name: google.cloud.aiplatform.v1beta1.schema.predict.instance.TextSentimentPredictionInstance
- name: google.cloud.aiplatform.v1beta1.schema.predict.instance.VideoActionRecognitionPredictionInstance
- name: google.cloud.aiplatform.v1beta1.schema.predict.instance.VideoClassificationPredictionInstance
- name: google.cloud.aiplatform.v1beta1.schema.predict.instance.VideoObjectTrackingPredictionInstance
- name: google.cloud.aiplatform.v1beta1.schema.predict.params.ImageClassificationPredictionParams
- name: google.cloud.aiplatform.v1beta1.schema.predict.params.ImageObjectDetectionPredictionParams
- name: google.cloud.aiplatform.v1beta1.schema.predict.params.ImageSegmentationPredictionParams
- name: google.cloud.aiplatform.v1beta1.schema.predict.params.VideoActionRecognitionPredictionParams
- name: google.cloud.aiplatform.v1beta1.schema.predict.params.VideoClassificationPredictionParams
- name: google.cloud.aiplatform.v1beta1.schema.predict.params.VideoObjectTrackingPredictionParams
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.ClassificationPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.ImageObjectDetectionPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.ImageSegmentationPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.TabularClassificationPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.TabularRegressionPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.TextExtractionPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.TextSentimentPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.TimeSeriesForecastingPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.VideoActionRecognitionPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.VideoClassificationPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.predict.prediction.VideoObjectTrackingPredictionResult
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlForecasting
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlForecastingInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlForecastingMetadata
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlImageClassification
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlImageClassificationInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlImageClassificationMetadata
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlImageObjectDetection
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlImageObjectDetectionInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlImageObjectDetectionMetadata
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlImageSegmentation
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlImageSegmentationInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlImageSegmentationMetadata
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlTables
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlTablesInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlTablesMetadata
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlTextClassification
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlTextClassificationInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlTextExtraction
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlTextExtractionInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlTextSentiment
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlTextSentimentInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlVideoActionRecognition
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlVideoActionRecognitionInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlVideoClassification
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlVideoClassificationInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlVideoObjectTracking
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.AutoMlVideoObjectTrackingInputs
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.CustomJobMetadata
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.CustomTask
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.ExportEvaluatedDataItemsConfig
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.HyperparameterTuningJobMetadata
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.HyperparameterTuningJobSpec
- name: google.cloud.aiplatform.v1beta1.schema.trainingjob.definition.HyperparameterTuningTask

documentation:
  summary: |-
    Train high-quality custom machine learning models with minimal machine
    learning expertise and effort.
  overview: |-
    Vertex AI enables data scientists, developers, and AI newcomers to create
    custom machine learning models specific to their business needs by
    leveraging Google's state-of-the-art transfer learning and innovative AI
    research.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 30.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 30.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/ui/{name=projects/*/locations/*}'
    additional_bindings:
    - get: '/v1beta1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/ui/{name=projects/*}/locations'
    additional_bindings:
    - get: '/v1beta1/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/ui/{name=projects/*/locations/*/operations/*}:cancel'
    additional_bindings:
    - post: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/models/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/studies/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/datasets/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/endpoints/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/featurestores/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/customJobs/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/indexes/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/migratableResources/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/models/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/studies/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/specialistPools/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:cancel'
    - post: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/ui/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - delete: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/models/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/studies/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/datasets/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/endpoints/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/featurestores/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/customJobs/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/indexes/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/migratableResources/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/models/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/studies/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/specialistPools/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}'
    - delete: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/ui/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/models/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/studies/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/datasets/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/endpoints/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/featurestores/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/customJobs/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/indexes/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/migratableResources/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/models/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/studies/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/specialistPools/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}'
    - get: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/ui/{name=projects/*/locations/*}/operations'
    additional_bindings:
    - get: '/ui/{name=projects/*/locations/*/datasets/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/endpoints/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/customJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/indexes/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/indexEndpoints/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/migratableResources/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/models/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/models/*/evaluations/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/studies/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/studies/*/trials/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/trainingPipelines/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/pipelineJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/specialistPools/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/datasets/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/datasets/*/dataItems/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/datasets/*/savedQueries/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/endpoints/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/featurestores/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/featurestores/*/entityTypes/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/customJobs/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/dataLabelingJobs/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/hyperparameterTuningJobs/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/indexes/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/indexEndpoints/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/migratableResources/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/models/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/models/*/evaluations/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/studies/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/studies/*/trials/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/trainingPipelines/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/pipelineJobs/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/specialistPools/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/tensorboards/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}/operations'
    - get: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}/operations'
  - selector: google.longrunning.Operations.WaitOperation
    post: '/ui/{name=projects/*/locations/*/operations/*}:wait'
    additional_bindings:
    - post: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/models/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/studies/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/datasets/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/endpoints/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/featurestores/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/customJobs/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/indexes/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/migratableResources/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/models/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/studies/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/specialistPools/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:wait'
    - post: '/v1beta1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:wait'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
