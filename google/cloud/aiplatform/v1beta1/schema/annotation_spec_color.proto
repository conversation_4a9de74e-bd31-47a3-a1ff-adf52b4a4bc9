// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema;

import "google/type/color.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/schemapb;schemapb";
option java_multiple_files = true;
option java_outer_classname = "AnnotationSpecColorProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema";

// An entry of mapping between color and AnnotationSpec. The mapping is used in
// segmentation mask.
message AnnotationSpecColor {
  // The color of the AnnotationSpec in a segmentation mask.
  google.type.Color color = 1;

  // The display name of the AnnotationSpec represented by the color in the
  // segmentation mask.
  string display_name = 2;

  // The ID of the AnnotationSpec represented by the color in the segmentation
  // mask.
  string id = 3;
}
