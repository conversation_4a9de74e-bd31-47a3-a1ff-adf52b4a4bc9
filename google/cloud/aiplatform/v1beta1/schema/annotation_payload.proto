// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1.schema;

import "google/cloud/aiplatform/v1beta1/schema/annotation_spec_color.proto";
import "google/cloud/aiplatform/v1beta1/schema/geometry.proto";
import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1.Schema";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/schema/schemapb;schemapb";
option java_multiple_files = true;
option java_outer_classname = "AnnotationPayloadProto";
option java_package = "com.google.cloud.aiplatform.v1beta1.schema";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1\\Schema";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1::Schema";

// Annotation details specific to image classification.
message ImageClassificationAnnotation {
  // The resource Id of the AnnotationSpec that this Annotation pertains to.
  string annotation_spec_id = 1;

  // The display name of the AnnotationSpec that this Annotation pertains to.
  string display_name = 2;
}

// Annotation details specific to image object detection.
message ImageBoundingBoxAnnotation {
  // The resource Id of the AnnotationSpec that this Annotation pertains to.
  string annotation_spec_id = 1;

  // The display name of the AnnotationSpec that this Annotation pertains to.
  string display_name = 2;

  // The leftmost coordinate of the bounding box.
  double x_min = 3;

  // The rightmost coordinate of the bounding box.
  double x_max = 4;

  // The topmost coordinate of the bounding box.
  double y_min = 5;

  // The bottommost coordinate of the bounding box.
  double y_max = 6;
}

// Annotation details specific to image segmentation.
message ImageSegmentationAnnotation {
  // The mask based segmentation annotation.
  message MaskAnnotation {
    // Google Cloud Storage URI that points to the mask image. The image must be
    // in PNG format. It must have the same size as the DataItem's image. Each
    // pixel in the image mask represents the AnnotationSpec which the pixel in
    // the image DataItem belong to. Each color is mapped to one AnnotationSpec
    // based on annotation_spec_colors.
    string mask_gcs_uri = 1;

    // The mapping between color and AnnotationSpec for this Annotation.
    repeated AnnotationSpecColor annotation_spec_colors = 2;
  }

  // Represents a polygon in image.
  message PolygonAnnotation {
    // The vertexes are connected one by one and the last vertex is connected to
    // the first one to represent a polygon.
    repeated Vertex vertexes = 1;

    // The resource Id of the AnnotationSpec that this Annotation pertains to.
    string annotation_spec_id = 2;

    // The display name of the AnnotationSpec that this Annotation pertains to.
    string display_name = 3;
  }

  // Represents a polyline in image.
  message PolylineAnnotation {
    // The vertexes are connected one by one and the last vertex in not
    // connected to the first one.
    repeated Vertex vertexes = 1;

    // The resource Id of the AnnotationSpec that this Annotation pertains to.
    string annotation_spec_id = 2;

    // The display name of the AnnotationSpec that this Annotation pertains to.
    string display_name = 3;
  }

  oneof annotation {
    // Mask based segmentation annotation. Only one mask annotation can exist
    // for one image.
    MaskAnnotation mask_annotation = 3;

    // Polygon annotation.
    PolygonAnnotation polygon_annotation = 4;

    // Polyline annotation.
    PolylineAnnotation polyline_annotation = 5;
  }
}

// Annotation details specific to text classification.
message TextClassificationAnnotation {
  // The resource Id of the AnnotationSpec that this Annotation pertains to.
  string annotation_spec_id = 1;

  // The display name of the AnnotationSpec that this Annotation pertains to.
  string display_name = 2;
}

// Annotation details specific to text extraction.
message TextExtractionAnnotation {
  // The segment of the text content.
  TextSegment text_segment = 1;

  // The resource Id of the AnnotationSpec that this Annotation pertains to.
  string annotation_spec_id = 2;

  // The display name of the AnnotationSpec that this Annotation pertains to.
  string display_name = 3;
}

// The text segment inside of DataItem.
message TextSegment {
  // Zero-based character index of the first character of the text
  // segment (counting characters from the beginning of the text).
  uint64 start_offset = 1;

  // Zero-based character index of the first character past the end of
  // the text segment (counting character from the beginning of the text).
  // The character at the end_offset is NOT included in the text segment.
  uint64 end_offset = 2;

  // The text content in the segment for output only.
  string content = 3;
}

// Annotation details specific to text sentiment.
message TextSentimentAnnotation {
  // The sentiment score for text.
  int32 sentiment = 1;

  // The sentiment max score for text.
  int32 sentiment_max = 2;

  // The resource Id of the AnnotationSpec that this Annotation pertains to.
  string annotation_spec_id = 3;

  // The display name of the AnnotationSpec that this Annotation pertains to.
  string display_name = 4;
}

// Annotation details specific to video classification.
message VideoClassificationAnnotation {
  // This Annotation applies to the time period represented by the TimeSegment.
  // If it's not set, the Annotation applies to the whole video.
  TimeSegment time_segment = 1;

  // The resource Id of the AnnotationSpec that this Annotation pertains to.
  string annotation_spec_id = 2;

  // The display name of the AnnotationSpec that this Annotation pertains to.
  string display_name = 3;
}

// A time period inside of a DataItem that has a time dimension (e.g. video).
message TimeSegment {
  // Start of the time segment (inclusive), represented as the duration since
  // the start of the DataItem.
  google.protobuf.Duration start_time_offset = 1;

  // End of the time segment (exclusive), represented as the duration since the
  // start of the DataItem.
  google.protobuf.Duration end_time_offset = 2;
}

// Annotation details specific to video object tracking.
message VideoObjectTrackingAnnotation {
  // A time (frame) of a video to which this annotation pertains.
  // Represented as the duration since the video's start.
  google.protobuf.Duration time_offset = 1;

  // The leftmost coordinate of the bounding box.
  double x_min = 2;

  // The rightmost coordinate of the bounding box.
  double x_max = 3;

  // The topmost coordinate of the bounding box.
  double y_min = 4;

  // The bottommost coordinate of the bounding box.
  double y_max = 5;

  // The instance of the object, expressed as a positive integer. Used to track
  // the same object across different frames.
  int64 instance_id = 6;

  // The resource Id of the AnnotationSpec that this Annotation pertains to.
  string annotation_spec_id = 7;

  // The display name of the AnnotationSpec that this Annotation pertains to.
  string display_name = 8;
}

// Annotation details specific to video action recognition.
message VideoActionRecognitionAnnotation {
  // This Annotation applies to the time period represented by the TimeSegment.
  // If it's not set, the Annotation applies to the whole video.
  TimeSegment time_segment = 1;

  // The resource Id of the AnnotationSpec that this Annotation pertains to.
  string annotation_spec_id = 2;

  // The display name of the AnnotationSpec that this Annotation pertains to.
  string display_name = 3;
}
