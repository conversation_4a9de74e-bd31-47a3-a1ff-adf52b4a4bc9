// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ModelMonitoringStatsProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// Represents the collection of statistics for a metric.
message ModelMonitoringStats {
  oneof stats {
    // Generated tabular statistics.
    ModelMonitoringTabularStats tabular_stats = 1;
  }
}

// Represents a single statistics data point.
message ModelMonitoringStatsDataPoint {
  // Typed value of the statistics.
  message TypedValue {
    // Summary statistics for a population of values.
    message DistributionDataValue {
      // Predictive monitoring drift distribution in
      // `tensorflow.metadata.v0.DatasetFeatureStatistics` format.
      google.protobuf.Value distribution = 1;

      // Distribution distance deviation from the current dataset's statistics
      // to baseline dataset's statistics.
      //   * For categorical feature, the distribution distance is calculated
      //     by L-inifinity norm or Jensen–Shannon divergence.
      //   * For numerical feature, the distribution distance is calculated by
      //     Jensen–Shannon divergence.
      double distribution_deviation = 2;
    }

    // The typed value.
    oneof value {
      // Double.
      double double_value = 1;

      // Distribution.
      DistributionDataValue distribution_value = 2;
    }
  }

  // Statistics from current dataset.
  TypedValue current_stats = 1;

  // Statistics from baseline dataset.
  TypedValue baseline_stats = 2;

  // Threshold value.
  double threshold_value = 3;

  // Indicate if the statistics has anomaly.
  bool has_anomaly = 4;

  // Model monitoring job resource name.
  string model_monitoring_job = 5;

  // Schedule resource name.
  string schedule = 6;

  // Statistics create time.
  google.protobuf.Timestamp create_time = 7;

  // Algorithm used to calculated the metrics, eg: jensen_shannon_divergence,
  // l_infinity.
  string algorithm = 8;
}

// A collection of data points that describes the time-varying values of a
// tabular metric.
message ModelMonitoringTabularStats {
  // The stats name.
  string stats_name = 1;

  // One of the supported monitoring objectives:
  // `raw-feature-drift`
  // `prediction-output-drift`
  // `feature-attribution`
  string objective_type = 2;

  // The data points of this time series. When listing time series, points are
  // returned in reverse time order.
  repeated ModelMonitoringStatsDataPoint data_points = 3;
}

// Filter for searching ModelMonitoringStats.
message SearchModelMonitoringStatsFilter {
  // Tabular statistics filter.
  message TabularStatsFilter {
    // If not specified, will return all the stats_names.
    string stats_name = 1;

    // One of the supported monitoring objectives:
    // `raw-feature-drift`
    // `prediction-output-drift`
    // `feature-attribution`
    string objective_type = 2;

    // From a particular monitoring job.
    string model_monitoring_job = 3;

    // From a particular monitoring schedule.
    string model_monitoring_schedule = 4;

    // Specify the algorithm type used for distance calculation, eg:
    // jensen_shannon_divergence, l_infinity.
    string algorithm = 5;
  }

  oneof filter {
    // Tabular statistics filter.
    TabularStatsFilter tabular_stats_filter = 1;
  }
}
