// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1Beta1";
option go_package = "cloud.google.com/go/aiplatform/apiv1beta1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "TensorboardRunProto";
option java_package = "com.google.cloud.aiplatform.v1beta1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1beta1";
option ruby_package = "Google::Cloud::AIPlatform::V1beta1";

// TensorboardRun maps to a specific execution of a training job with a given
// set of hyperparameter values, model definition, dataset, etc
message TensorboardRun {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/TensorboardRun"
    pattern: "projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}"
  };

  // Output only. Name of the TensorboardRun.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}/runs/{run}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. User provided name of this TensorboardRun.
  // This value must be unique among all TensorboardRuns
  // belonging to the same parent TensorboardExperiment.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Description of this TensorboardRun.
  string description = 3;

  // Output only. Timestamp when this TensorboardRun was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this TensorboardRun was last updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels with user-defined metadata to organize your TensorboardRuns.
  //
  // This field will be used to filter and visualize Runs in the Tensorboard UI.
  // For example, a Vertex AI training job can set a label
  // aiplatform.googleapis.com/training_job_id=xxxxx to all the runs created
  // within that job. An end user can set a label experiment_id=xxxxx for all
  // the runs produced in a Jupyter notebook. These runs can be grouped by a
  // label value and visualized together in the Tensorboard UI.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one TensorboardRun
  // (System labels are excluded).
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with "aiplatform.googleapis.com/"
  // and are immutable.
  map<string, string> labels = 8;

  // Used to perform a consistent read-modify-write updates. If not set, a blind
  // "overwrite" update happens.
  string etag = 9;
}
