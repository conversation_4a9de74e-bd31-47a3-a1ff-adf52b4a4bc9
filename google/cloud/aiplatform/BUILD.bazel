# This build file includes a target for the Ruby wrapper library for
# google-cloud-ai_platform.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for aiplatform.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "aiplatform_ruby_wrapper",
    srcs = ["//google/cloud/aiplatform/v1:aiplatform_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-ai_platform",
        "ruby-cloud-wrapper-of=v1:0.59",
        "ruby-cloud-product-url=https://cloud.google.com/vertex-ai/docs/",
        "ruby-cloud-api-id=aiplatform.googleapis.com",
        "ruby-cloud-api-shortname=aiplatform",
        "ruby-cloud-gem-namespace=Google::Cloud::AIPlatform",
        "ruby-cloud-service-override=AiPlatform=AIPlatform",
    ],
    transport = "grpc+rest",
    ruby_cloud_description = "Vertex AI enables data scientists, developers, and AI newcomers to create custom machine learning models specific to their business needs by leveraging Google's state-of-the-art transfer learning and innovative AI research.",
    ruby_cloud_title = "Vertex AI",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-aiplatform-ruby",
    deps = [
        ":aiplatform_ruby_wrapper",
    ],
)
