// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "NetworkSpecProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Subnetwork"
  pattern: "projects/{project}/regions/{region}/subnetworks/{subnetwork}"
};

// Network spec.
message NetworkSpec {
  // Whether to enable public internet access. Default false.
  bool enable_internet_access = 1;

  // The full name of the Google Compute Engine
  // [network](https://cloud.google.com//compute/docs/networks-and-firewalls#networks)
  string network = 2 [
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // The name of the subnet that this instance is in.
  // Format:
  // `projects/{project_id_or_number}/regions/{region}/subnetworks/{subnetwork_id}`
  string subnetwork = 3 [(google.api.resource_reference) = {
    type: "compute.googleapis.com/Subnetwork"
  }];
}
