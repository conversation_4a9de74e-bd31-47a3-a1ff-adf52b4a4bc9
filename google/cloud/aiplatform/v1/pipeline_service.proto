// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/operation.proto";
import "google/cloud/aiplatform/v1/pipeline_job.proto";
import "google/cloud/aiplatform/v1/training_pipeline.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "PipelineServiceProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// A service for creating and managing Vertex AI's pipelines. This includes both
// `TrainingPipeline` resources (used for AutoML and custom training) and
// `PipelineJob` resources (used for Vertex AI Pipelines).
service PipelineService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a TrainingPipeline. A created TrainingPipeline right away will be
  // attempted to be run.
  rpc CreateTrainingPipeline(CreateTrainingPipelineRequest)
      returns (TrainingPipeline) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/trainingPipelines"
      body: "training_pipeline"
    };
    option (google.api.method_signature) = "parent,training_pipeline";
  }

  // Gets a TrainingPipeline.
  rpc GetTrainingPipeline(GetTrainingPipelineRequest)
      returns (TrainingPipeline) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/trainingPipelines/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists TrainingPipelines in a Location.
  rpc ListTrainingPipelines(ListTrainingPipelinesRequest)
      returns (ListTrainingPipelinesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/trainingPipelines"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a TrainingPipeline.
  rpc DeleteTrainingPipeline(DeleteTrainingPipelineRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/trainingPipelines/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Cancels a TrainingPipeline.
  // Starts asynchronous cancellation on the TrainingPipeline. The server
  // makes a best effort to cancel the pipeline, but success is not
  // guaranteed. Clients can use
  // [PipelineService.GetTrainingPipeline][google.cloud.aiplatform.v1.PipelineService.GetTrainingPipeline]
  // or other methods to check whether the cancellation succeeded or whether the
  // pipeline completed despite cancellation. On successful cancellation,
  // the TrainingPipeline is not deleted; instead it becomes a pipeline with
  // a
  // [TrainingPipeline.error][google.cloud.aiplatform.v1.TrainingPipeline.error]
  // value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`, and
  // [TrainingPipeline.state][google.cloud.aiplatform.v1.TrainingPipeline.state]
  // is set to `CANCELLED`.
  rpc CancelTrainingPipeline(CancelTrainingPipelineRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/trainingPipelines/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a PipelineJob. A PipelineJob will run immediately when created.
  rpc CreatePipelineJob(CreatePipelineJobRequest) returns (PipelineJob) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/pipelineJobs"
      body: "pipeline_job"
    };
    option (google.api.method_signature) =
        "parent,pipeline_job,pipeline_job_id";
  }

  // Gets a PipelineJob.
  rpc GetPipelineJob(GetPipelineJobRequest) returns (PipelineJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/pipelineJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists PipelineJobs in a Location.
  rpc ListPipelineJobs(ListPipelineJobsRequest)
      returns (ListPipelineJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/pipelineJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a PipelineJob.
  rpc DeletePipelineJob(DeletePipelineJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/pipelineJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Batch deletes PipelineJobs
  // The Operation is atomic. If it fails, none of the PipelineJobs are deleted.
  // If it succeeds, all of the PipelineJobs are deleted.
  rpc BatchDeletePipelineJobs(BatchDeletePipelineJobsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/pipelineJobs:batchDelete"
      body: "*"
    };
    option (google.api.method_signature) = "parent,names";
    option (google.longrunning.operation_info) = {
      response_type: "BatchDeletePipelineJobsResponse"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Cancels a PipelineJob.
  // Starts asynchronous cancellation on the PipelineJob. The server
  // makes a best effort to cancel the pipeline, but success is not
  // guaranteed. Clients can use
  // [PipelineService.GetPipelineJob][google.cloud.aiplatform.v1.PipelineService.GetPipelineJob]
  // or other methods to check whether the cancellation succeeded or whether the
  // pipeline completed despite cancellation. On successful cancellation,
  // the PipelineJob is not deleted; instead it becomes a pipeline with
  // a [PipelineJob.error][google.cloud.aiplatform.v1.PipelineJob.error] value
  // with a [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding
  // to `Code.CANCELLED`, and
  // [PipelineJob.state][google.cloud.aiplatform.v1.PipelineJob.state] is set to
  // `CANCELLED`.
  rpc CancelPipelineJob(CancelPipelineJobRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/pipelineJobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Batch cancel PipelineJobs.
  // Firstly the server will check if all the jobs are in non-terminal states,
  // and skip the jobs that are already terminated.
  // If the operation failed, none of the pipeline jobs are cancelled.
  // The server will poll the states of all the pipeline jobs periodically
  // to check the cancellation status.
  // This operation will return an LRO.
  rpc BatchCancelPipelineJobs(BatchCancelPipelineJobsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/pipelineJobs:batchCancel"
      body: "*"
    };
    option (google.api.method_signature) = "parent,names";
    option (google.longrunning.operation_info) = {
      response_type: "BatchCancelPipelineJobsResponse"
      metadata_type: "BatchCancelPipelineJobsOperationMetadata"
    };
  }
}

// Runtime operation information for
// [PipelineService.BatchCancelPipelineJobs][google.cloud.aiplatform.v1.PipelineService.BatchCancelPipelineJobs].
message BatchCancelPipelineJobsOperationMetadata {
  // The common part of the operation metadata.
  GenericOperationMetadata generic_metadata = 1;
}

// Request message for
// [PipelineService.CreateTrainingPipeline][google.cloud.aiplatform.v1.PipelineService.CreateTrainingPipeline].
message CreateTrainingPipelineRequest {
  // Required. The resource name of the Location to create the TrainingPipeline
  // in. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The TrainingPipeline to create.
  TrainingPipeline training_pipeline = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [PipelineService.GetTrainingPipeline][google.cloud.aiplatform.v1.PipelineService.GetTrainingPipeline].
message GetTrainingPipelineRequest {
  // Required. The name of the TrainingPipeline resource.
  // Format:
  // `projects/{project}/locations/{location}/trainingPipelines/{training_pipeline}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TrainingPipeline"
    }
  ];
}

// Request message for
// [PipelineService.ListTrainingPipelines][google.cloud.aiplatform.v1.PipelineService.ListTrainingPipelines].
message ListTrainingPipelinesRequest {
  // Required. The resource name of the Location to list the TrainingPipelines
  // from. Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The standard list filter.
  //
  // Supported fields:
  //
  //   * `display_name` supports `=`, `!=` comparisons, and `:` wildcard.
  //   * `state` supports `=`, `!=` comparisons.
  //   * `training_task_definition` `=`, `!=` comparisons, and `:` wildcard.
  //   * `create_time` supports `=`, `!=`,`<`, `<=`,`>`, `>=` comparisons.
  //     `create_time` must be in RFC 3339 format.
  //   * `labels` supports general map functions that is:
  //     `labels.key=value` - key:value equality
  //     `labels.key:* - key existence
  //
  // Some examples of using the filter are:
  //
  //   * `state="PIPELINE_STATE_SUCCEEDED" AND display_name:"my_pipeline_*"`
  //   * `state!="PIPELINE_STATE_FAILED" OR display_name="my_pipeline"`
  //   * `NOT display_name="my_pipeline"`
  //   * `create_time>"2021-05-18T00:00:00Z"`
  //   * `training_task_definition:"*automl_text_classification*"`
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  // Typically obtained via
  // [ListTrainingPipelinesResponse.next_page_token][google.cloud.aiplatform.v1.ListTrainingPipelinesResponse.next_page_token]
  // of the previous
  // [PipelineService.ListTrainingPipelines][google.cloud.aiplatform.v1.PipelineService.ListTrainingPipelines]
  // call.
  string page_token = 4;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 5;
}

// Response message for
// [PipelineService.ListTrainingPipelines][google.cloud.aiplatform.v1.PipelineService.ListTrainingPipelines]
message ListTrainingPipelinesResponse {
  // List of TrainingPipelines in the requested page.
  repeated TrainingPipeline training_pipelines = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListTrainingPipelinesRequest.page_token][google.cloud.aiplatform.v1.ListTrainingPipelinesRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [PipelineService.DeleteTrainingPipeline][google.cloud.aiplatform.v1.PipelineService.DeleteTrainingPipeline].
message DeleteTrainingPipelineRequest {
  // Required. The name of the TrainingPipeline resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/trainingPipelines/{training_pipeline}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TrainingPipeline"
    }
  ];
}

// Request message for
// [PipelineService.CancelTrainingPipeline][google.cloud.aiplatform.v1.PipelineService.CancelTrainingPipeline].
message CancelTrainingPipelineRequest {
  // Required. The name of the TrainingPipeline to cancel.
  // Format:
  // `projects/{project}/locations/{location}/trainingPipelines/{training_pipeline}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/TrainingPipeline"
    }
  ];
}

// Request message for
// [PipelineService.CreatePipelineJob][google.cloud.aiplatform.v1.PipelineService.CreatePipelineJob].
message CreatePipelineJobRequest {
  // Required. The resource name of the Location to create the PipelineJob in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The PipelineJob to create.
  PipelineJob pipeline_job = 2 [(google.api.field_behavior) = REQUIRED];

  // The ID to use for the PipelineJob, which will become the final component of
  // the PipelineJob name. If not provided, an ID will be automatically
  // generated.
  //
  // This value should be less than 128 characters, and valid characters
  // are `/[a-z][0-9]-/`.
  string pipeline_job_id = 3;
}

// Request message for
// [PipelineService.GetPipelineJob][google.cloud.aiplatform.v1.PipelineService.GetPipelineJob].
message GetPipelineJobRequest {
  // Required. The name of the PipelineJob resource.
  // Format:
  // `projects/{project}/locations/{location}/pipelineJobs/{pipeline_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PipelineJob"
    }
  ];
}

// Request message for
// [PipelineService.ListPipelineJobs][google.cloud.aiplatform.v1.PipelineService.ListPipelineJobs].
message ListPipelineJobsRequest {
  // Required. The resource name of the Location to list the PipelineJobs from.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Lists the PipelineJobs that match the filter expression. The following
  // fields are supported:
  //
  // * `pipeline_name`: Supports `=` and `!=` comparisons.
  // * `display_name`: Supports `=`, `!=` comparisons, and `:` wildcard.
  // * `pipeline_job_user_id`: Supports `=`, `!=` comparisons, and `:` wildcard.
  //   for example, can check if pipeline's display_name contains *step* by
  //   doing display_name:\"*step*\"
  // * `state`: Supports `=` and `!=` comparisons.
  // * `create_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
  //   Values must be in RFC 3339 format.
  // * `update_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
  //   Values must be in RFC 3339 format.
  // * `end_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
  //   Values must be in RFC 3339 format.
  // * `labels`: Supports key-value equality and key presence.
  // * `template_uri`: Supports `=`, `!=` comparisons, and `:` wildcard.
  // * `template_metadata.version`: Supports `=`, `!=` comparisons, and `:`
  //   wildcard.
  //
  // Filter expressions can be combined together using logical operators
  // (`AND` & `OR`).
  // For example: `pipeline_name="test" AND create_time>"2020-05-18T13:30:00Z"`.
  //
  // The syntax to define filter expression is based on
  // https://google.aip.dev/160.
  //
  // Examples:
  //
  // * `create_time>"2021-05-18T00:00:00Z" OR
  //   update_time>"2020-05-18T00:00:00Z"` PipelineJobs created or updated
  //   after 2020-05-18 00:00:00 UTC.
  // * `labels.env = "prod"`
  //   PipelineJobs with label "env" set to "prod".
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  // Typically obtained via
  // [ListPipelineJobsResponse.next_page_token][google.cloud.aiplatform.v1.ListPipelineJobsResponse.next_page_token]
  // of the previous
  // [PipelineService.ListPipelineJobs][google.cloud.aiplatform.v1.PipelineService.ListPipelineJobs]
  // call.
  string page_token = 4;

  // A comma-separated list of fields to order by. The default sort order is in
  // ascending order. Use "desc" after a field name for descending. You can have
  // multiple order_by fields provided e.g. "create_time desc, end_time",
  // "end_time, start_time, update_time" For example, using "create_time desc,
  // end_time" will order results by create time in descending order, and if
  // there are multiple jobs having the same create time, order them by the end
  // time in ascending order. if order_by is not specified, it will order by
  // default order is create time in descending order. Supported fields:
  //
  //   * `create_time`
  //   * `update_time`
  //   * `end_time`
  //   * `start_time`
  string order_by = 6;

  // Mask specifying which fields to read.
  google.protobuf.FieldMask read_mask = 7;
}

// Response message for
// [PipelineService.ListPipelineJobs][google.cloud.aiplatform.v1.PipelineService.ListPipelineJobs]
message ListPipelineJobsResponse {
  // List of PipelineJobs in the requested page.
  repeated PipelineJob pipeline_jobs = 1;

  // A token to retrieve the next page of results.
  // Pass to
  // [ListPipelineJobsRequest.page_token][google.cloud.aiplatform.v1.ListPipelineJobsRequest.page_token]
  // to obtain that page.
  string next_page_token = 2;
}

// Request message for
// [PipelineService.DeletePipelineJob][google.cloud.aiplatform.v1.PipelineService.DeletePipelineJob].
message DeletePipelineJobRequest {
  // Required. The name of the PipelineJob resource to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/pipelineJobs/{pipeline_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PipelineJob"
    }
  ];
}

// Request message for
// [PipelineService.BatchDeletePipelineJobs][google.cloud.aiplatform.v1.PipelineService.BatchDeletePipelineJobs].
message BatchDeletePipelineJobsRequest {
  // Required. The name of the PipelineJobs' parent resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/PipelineJob"
    }
  ];

  // Required. The names of the PipelineJobs to delete.
  // A maximum of 32 PipelineJobs can be deleted in a batch.
  // Format:
  // `projects/{project}/locations/{location}/pipelineJobs/{pipelineJob}`
  repeated string names = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PipelineJob"
    }
  ];
}

// Response message for
// [PipelineService.BatchDeletePipelineJobs][google.cloud.aiplatform.v1.PipelineService.BatchDeletePipelineJobs].
message BatchDeletePipelineJobsResponse {
  // PipelineJobs deleted.
  repeated PipelineJob pipeline_jobs = 1;
}

// Request message for
// [PipelineService.CancelPipelineJob][google.cloud.aiplatform.v1.PipelineService.CancelPipelineJob].
message CancelPipelineJobRequest {
  // Required. The name of the PipelineJob to cancel.
  // Format:
  // `projects/{project}/locations/{location}/pipelineJobs/{pipeline_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PipelineJob"
    }
  ];
}

// Request message for
// [PipelineService.BatchCancelPipelineJobs][google.cloud.aiplatform.v1.PipelineService.BatchCancelPipelineJobs].
message BatchCancelPipelineJobsRequest {
  // Required. The name of the PipelineJobs' parent resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/PipelineJob"
    }
  ];

  // Required. The names of the PipelineJobs to cancel.
  // A maximum of 32 PipelineJobs can be cancelled in a batch.
  // Format:
  // `projects/{project}/locations/{location}/pipelineJobs/{pipelineJob}`
  repeated string names = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PipelineJob"
    }
  ];
}

// Response message for
// [PipelineService.BatchCancelPipelineJobs][google.cloud.aiplatform.v1.PipelineService.BatchCancelPipelineJobs].
message BatchCancelPipelineJobsResponse {
  // PipelineJobs cancelled.
  repeated PipelineJob pipeline_jobs = 1;
}
