// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "TensorboardExperimentProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// A TensorboardExperiment is a group of TensorboardRuns, that are typically the
// results of a training job run, in a Tensorboard.
message TensorboardExperiment {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/TensorboardExperiment"
    pattern: "projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}"
  };

  // Output only. Name of the TensorboardExperiment.
  // Format:
  // `projects/{project}/locations/{location}/tensorboards/{tensorboard}/experiments/{experiment}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User provided name of this TensorboardExperiment.
  string display_name = 2;

  // Description of this TensorboardExperiment.
  string description = 3;

  // Output only. Timestamp when this TensorboardExperiment was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this TensorboardExperiment was last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels with user-defined metadata to organize your
  // TensorboardExperiment.
  //
  // Label keys and values cannot be longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one Dataset (System
  // labels are excluded).
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with `aiplatform.googleapis.com/`
  // and are immutable. The following system labels exist for each Dataset:
  //
  // * `aiplatform.googleapis.com/dataset_metadata_schema`: output only. Its
  //    value is the
  //    [metadata_schema's][google.cloud.aiplatform.v1.Dataset.metadata_schema_uri]
  //    title.
  map<string, string> labels = 6;

  // Used to perform consistent read-modify-write updates. If not set, a blind
  // "overwrite" update happens.
  string etag = 7;

  // Immutable. Source of the TensorboardExperiment. Example: a custom training
  // job.
  string source = 8 [(google.api.field_behavior) = IMMUTABLE];
}
