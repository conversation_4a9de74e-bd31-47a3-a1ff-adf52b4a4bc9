// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "CompletionStatsProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Success and error statistics of processing multiple entities
// (for example, DataItems or structured data rows) in batch.
message CompletionStats {
  // Output only. The number of entities that had been processed successfully.
  int64 successful_count = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of entities for which any error was encountered.
  int64 failed_count = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. In cases when enough errors are encountered a job, pipeline,
  // or operation may be failed as a whole. Below is the number of entities for
  // which the processing had not been finished (either in successful or failed
  // state). Set to -1 if the number is unknown (for example, the operation
  // failed before the total entity number could be collected).
  int64 incomplete_count = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of the successful forecast points that are
  // generated by the forecasting model. This is ONLY used by the forecasting
  // batch prediction.
  int64 successful_forecast_point_count = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
