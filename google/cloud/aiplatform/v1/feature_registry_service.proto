// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/feature.proto";
import "google/cloud/aiplatform/v1/feature_group.proto";
import "google/cloud/aiplatform/v1/featurestore_service.proto";
import "google/cloud/aiplatform/v1/operation.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeatureRegistryServiceProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// The service that handles CRUD and List for resources for
// FeatureRegistry.
service FeatureRegistryService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new FeatureGroup in a given project and location.
  rpc CreateFeatureGroup(CreateFeatureGroupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/featureGroups"
      body: "feature_group"
    };
    option (google.api.method_signature) =
        "parent,feature_group,feature_group_id";
    option (google.longrunning.operation_info) = {
      response_type: "FeatureGroup"
      metadata_type: "CreateFeatureGroupOperationMetadata"
    };
  }

  // Gets details of a single FeatureGroup.
  rpc GetFeatureGroup(GetFeatureGroupRequest) returns (FeatureGroup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/featureGroups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists FeatureGroups in a given project and location.
  rpc ListFeatureGroups(ListFeatureGroupsRequest)
      returns (ListFeatureGroupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/featureGroups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the parameters of a single FeatureGroup.
  rpc UpdateFeatureGroup(UpdateFeatureGroupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{feature_group.name=projects/*/locations/*/featureGroups/*}"
      body: "feature_group"
    };
    option (google.api.method_signature) = "feature_group,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "FeatureGroup"
      metadata_type: "UpdateFeatureGroupOperationMetadata"
    };
  }

  // Deletes a single FeatureGroup.
  rpc DeleteFeatureGroup(DeleteFeatureGroupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/featureGroups/*}"
    };
    option (google.api.method_signature) = "name,force";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }

  // Creates a new Feature in a given FeatureGroup.
  rpc CreateFeature(CreateFeatureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/featureGroups/*}/features"
      body: "feature"
    };
    option (google.api.method_signature) = "parent,feature,feature_id";
    option (google.longrunning.operation_info) = {
      response_type: "Feature"
      metadata_type: "CreateFeatureOperationMetadata"
    };
  }

  // Creates a batch of Features in a given FeatureGroup.
  rpc BatchCreateFeatures(BatchCreateFeaturesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/featureGroups/*}/features:batchCreate"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
    option (google.longrunning.operation_info) = {
      response_type: "BatchCreateFeaturesResponse"
      metadata_type: "BatchCreateFeaturesOperationMetadata"
    };
  }

  // Gets details of a single Feature.
  rpc GetFeature(GetFeatureRequest) returns (Feature) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/featureGroups/*/features/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Features in a given FeatureGroup.
  rpc ListFeatures(ListFeaturesRequest) returns (ListFeaturesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/featureGroups/*}/features"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the parameters of a single Feature.
  rpc UpdateFeature(UpdateFeatureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{feature.name=projects/*/locations/*/featureGroups/*/features/*}"
      body: "feature"
    };
    option (google.api.method_signature) = "feature,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Feature"
      metadata_type: "UpdateFeatureOperationMetadata"
    };
  }

  // Deletes a single Feature.
  rpc DeleteFeature(DeleteFeatureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/featureGroups/*/features/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "DeleteOperationMetadata"
    };
  }
}

// Request message for
// [FeatureRegistryService.CreateFeatureGroup][google.cloud.aiplatform.v1.FeatureRegistryService.CreateFeatureGroup].
message CreateFeatureGroupRequest {
  // Required. The resource name of the Location to create FeatureGroups.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/FeatureGroup"
    }
  ];

  // Required. The FeatureGroup to create.
  FeatureGroup feature_group = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for this FeatureGroup, which will become the final
  // component of the FeatureGroup's resource name.
  //
  // This value may be up to 128 characters, and valid characters are
  // `[a-z0-9_]`. The first character cannot be a number.
  //
  // The value must be unique within the project and location.
  string feature_group_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [FeatureRegistryService.GetFeatureGroup][google.cloud.aiplatform.v1.FeatureRegistryService.GetFeatureGroup].
message GetFeatureGroupRequest {
  // Required. The name of the FeatureGroup resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/FeatureGroup"
    }
  ];
}

// Request message for
// [FeatureRegistryService.ListFeatureGroups][google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatureGroups].
message ListFeatureGroupsRequest {
  // Required. The resource name of the Location to list FeatureGroups.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "aiplatform.googleapis.com/FeatureGroup"
    }
  ];

  // Lists the FeatureGroups that match the filter expression. The
  // following fields are supported:
  //
  // * `create_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
  // Values must be
  //   in RFC 3339 format.
  // * `update_time`: Supports `=`, `!=`, `<`, `>`, `<=`, and `>=` comparisons.
  // Values must be
  //   in RFC 3339 format.
  // * `labels`: Supports key-value equality and key presence.
  //
  // Examples:
  //
  // * `create_time > "2020-01-01" OR update_time > "2020-01-01"`
  //    FeatureGroups created or updated after 2020-01-01.
  // * `labels.env = "prod"`
  //    FeatureGroups with label "env" set to "prod".
  string filter = 2;

  // The maximum number of FeatureGroups to return. The service may return
  // fewer than this value. If unspecified, at most 100 FeatureGroups will
  // be returned. The maximum value is 100; any value greater than 100 will be
  // coerced to 100.
  int32 page_size = 3;

  // A page token, received from a previous
  // [FeatureRegistryService.ListFeatureGroups][google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatureGroups]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [FeatureRegistryService.ListFeatureGroups][google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatureGroups]
  // must match the call that provided the page token.
  string page_token = 4;

  // A comma-separated list of fields to order by, sorted in ascending order.
  // Use "desc" after a field name for descending.
  // Supported Fields:
  //
  //   * `create_time`
  //   * `update_time`
  string order_by = 5;
}

// Response message for
// [FeatureRegistryService.ListFeatureGroups][google.cloud.aiplatform.v1.FeatureRegistryService.ListFeatureGroups].
message ListFeatureGroupsResponse {
  // The FeatureGroups matching the request.
  repeated FeatureGroup feature_groups = 1;

  // A token, which can be sent as
  // [ListFeatureGroupsRequest.page_token][google.cloud.aiplatform.v1.ListFeatureGroupsRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [FeatureRegistryService.UpdateFeatureGroup][google.cloud.aiplatform.v1.FeatureRegistryService.UpdateFeatureGroup].
message UpdateFeatureGroupRequest {
  // Required. The FeatureGroup's `name` field is used to identify the
  // FeatureGroup to be updated. Format:
  // `projects/{project}/locations/{location}/featureGroups/{feature_group}`
  FeatureGroup feature_group = 1 [(google.api.field_behavior) = REQUIRED];

  // Field mask is used to specify the fields to be overwritten in the
  // FeatureGroup resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then only the non-empty fields present in the
  // request will be overwritten. Set the update_mask to `*` to override all
  // fields.
  //
  // Updatable fields:
  //
  //   * `labels`
  //   * `description`
  //   * `big_query`
  //   * `big_query.entity_id_columns`
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [FeatureRegistryService.DeleteFeatureGroup][google.cloud.aiplatform.v1.FeatureRegistryService.DeleteFeatureGroup].
message DeleteFeatureGroupRequest {
  // Required. The name of the FeatureGroup to be deleted.
  // Format:
  // `projects/{project}/locations/{location}/featureGroups/{feature_group}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/FeatureGroup"
    }
  ];

  // If set to true, any Features under this FeatureGroup
  // will also be deleted. (Otherwise, the request will only work if the
  // FeatureGroup has no Features.)
  bool force = 2;
}

// Details of operations that perform create FeatureGroup.
message CreateFeatureGroupOperationMetadata {
  // Operation metadata for FeatureGroup.
  GenericOperationMetadata generic_metadata = 1;
}

// Details of operations that perform update FeatureGroup.
message UpdateFeatureGroupOperationMetadata {
  // Operation metadata for FeatureGroup.
  GenericOperationMetadata generic_metadata = 1;
}

// Details of operations that perform create FeatureGroup.
message CreateRegistryFeatureOperationMetadata {
  // Operation metadata for Feature.
  GenericOperationMetadata generic_metadata = 1;
}

// Details of operations that perform update Feature.
message UpdateFeatureOperationMetadata {
  // Operation metadata for Feature Update.
  GenericOperationMetadata generic_metadata = 1;
}
