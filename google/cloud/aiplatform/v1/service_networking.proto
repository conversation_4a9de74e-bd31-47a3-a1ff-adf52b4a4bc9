// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceNetworkingProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/NetworkAttachment"
  pattern: "projects/{project}/regions/{region}/networkAttachments/{networkattachment}"
};

// PSC config that is used to automatically create forwarding rule via
// ServiceConnectionMap.
message PSCAutomationConfig {
  // Required. Project id used to create forwarding rule.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The full name of the Google Compute Engine
  // [network](https://cloud.google.com/compute/docs/networks-and-firewalls#networks).
  // [Format](https://cloud.google.com/compute/docs/reference/rest/v1/networks/insert):
  // `projects/{project}/global/networks/{network}`.
  // Where {project} is a project number, as in '12345', and {network} is
  // network name.
  string network = 2 [(google.api.field_behavior) = REQUIRED];
}

// Represents configuration for private service connect.
message PrivateServiceConnectConfig {
  // Required. If true, expose the IndexEndpoint via private service connect.
  bool enable_private_service_connect = 1
      [(google.api.field_behavior) = REQUIRED];

  // A list of Projects from which the forwarding rule will target the service
  // attachment.
  repeated string project_allowlist = 2;

  // Output only. The name of the generated service attachment resource.
  // This is only populated if the endpoint is deployed with
  // PrivateServiceConnect.
  string service_attachment = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// PscAutomatedEndpoints defines the output of the forwarding rule
// automatically created by each PscAutomationConfig.
message PscAutomatedEndpoints {
  // Corresponding project_id in pscAutomationConfigs
  string project_id = 1;

  // Corresponding network in pscAutomationConfigs.
  string network = 2;

  // Ip Address created by the automated forwarding rule.
  string match_address = 3;
}
