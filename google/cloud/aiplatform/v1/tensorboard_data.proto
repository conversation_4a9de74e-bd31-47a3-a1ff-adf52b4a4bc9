// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/cloud/aiplatform/v1/tensorboard_time_series.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "TensorboardDataProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// All the data stored in a TensorboardTimeSeries.
message TimeSeriesData {
  // Required. The ID of the TensorboardTimeSeries, which will become the final
  // component of the TensorboardTimeSeries' resource name
  string tensorboard_time_series_id = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. The value type of this time series. All the values in
  // this time series data must match this value type.
  TensorboardTimeSeries.ValueType value_type = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Data points in this time series.
  repeated TimeSeriesDataPoint values = 3
      [(google.api.field_behavior) = REQUIRED];
}

// A TensorboardTimeSeries data point.
message TimeSeriesDataPoint {
  // Value of this time series data point.
  oneof value {
    // A scalar value.
    Scalar scalar = 3;

    // A tensor value.
    TensorboardTensor tensor = 4;

    // A blob sequence value.
    TensorboardBlobSequence blobs = 5;
  }

  // Wall clock timestamp when this data point is generated by the end user.
  google.protobuf.Timestamp wall_time = 1;

  // Step index of this data point within the run.
  int64 step = 2;
}

// One point viewable on a scalar metric plot.
message Scalar {
  // Value of the point at this step / timestamp.
  double value = 1;
}

// One point viewable on a tensor metric plot.
message TensorboardTensor {
  // Required. Serialized form of
  // https://github.com/tensorflow/tensorflow/blob/master/tensorflow/core/framework/tensor.proto
  bytes value = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Version number of TensorProto used to serialize
  // [value][google.cloud.aiplatform.v1.TensorboardTensor.value].
  int32 version_number = 2 [(google.api.field_behavior) = OPTIONAL];
}

// One point viewable on a blob metric plot, but mostly just a wrapper message
// to work around repeated fields can't be used directly within `oneof` fields.
message TensorboardBlobSequence {
  // List of blobs contained within the sequence.
  repeated TensorboardBlob values = 1;
}

// One blob (e.g, image, graph) viewable on a blob metric plot.
message TensorboardBlob {
  // Output only. A URI safe key uniquely identifying a blob. Can be used to
  // locate the blob stored in the Cloud Storage bucket of the consumer project.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The bytes of the blob is not present unless it's returned by the
  // ReadTensorboardBlobData endpoint.
  bytes data = 2 [(google.api.field_behavior) = OPTIONAL];
}
