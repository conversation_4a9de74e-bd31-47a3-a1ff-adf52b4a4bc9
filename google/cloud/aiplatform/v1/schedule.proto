// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/notebook_service.proto";
import "google/cloud/aiplatform/v1/pipeline_service.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ScheduleProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// An instance of a Schedule periodically schedules runs to make API calls based
// on user specified time specification and API request type.
message Schedule {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/Schedule"
    pattern: "projects/{project}/locations/{location}/schedules/{schedule}"
  };

  // Status of a scheduled run.
  message RunResponse {
    // The scheduled run time based on the user-specified schedule.
    google.protobuf.Timestamp scheduled_run_time = 1;

    // The response of the scheduled run.
    string run_response = 2;
  }

  // Possible state of the schedule.
  enum State {
    // Unspecified.
    STATE_UNSPECIFIED = 0;

    // The Schedule is active. Runs are being scheduled on the user-specified
    // timespec.
    ACTIVE = 1;

    // The schedule is paused. No new runs will be created until the schedule
    // is resumed. Already started runs will be allowed to complete.
    PAUSED = 2;

    // The Schedule is completed. No new runs will be scheduled. Already started
    // runs will be allowed to complete. Schedules in completed state cannot be
    // paused or resumed.
    COMPLETED = 3;
  }

  // Required.
  // The time specification to launch scheduled runs.
  oneof time_specification {
    // Cron schedule (https://en.wikipedia.org/wiki/Cron) to launch scheduled
    // runs. To explicitly set a timezone to the cron tab, apply a prefix in the
    // cron tab: "CRON_TZ=${IANA_TIME_ZONE}" or "TZ=${IANA_TIME_ZONE}".
    // The ${IANA_TIME_ZONE} may only be a valid string from IANA time zone
    // database. For example, "CRON_TZ=America/New_York 1 * * * *", or
    // "TZ=America/New_York 1 * * * *".
    string cron = 10;
  }

  // Required.
  // The API request template to launch the scheduled runs.
  // User-specified ID is not supported in the request template.
  oneof request {
    // Request for
    // [PipelineService.CreatePipelineJob][google.cloud.aiplatform.v1.PipelineService.CreatePipelineJob].
    // CreatePipelineJobRequest.parent field is required (format:
    // projects/{project}/locations/{location}).
    CreatePipelineJobRequest create_pipeline_job_request = 14;

    // Request for
    // [NotebookService.CreateNotebookExecutionJob][google.cloud.aiplatform.v1.NotebookService.CreateNotebookExecutionJob].
    CreateNotebookExecutionJobRequest create_notebook_execution_job_request =
        20;
  }

  // Immutable. The resource name of the Schedule.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Required. User provided name of the Schedule.
  // The name can be up to 128 characters long and can consist of any UTF-8
  // characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Timestamp after which the first run can be scheduled.
  // Default to Schedule create time if not specified.
  google.protobuf.Timestamp start_time = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Timestamp after which no new runs can be scheduled.
  // If specified, The schedule will be completed when either
  // end_time is reached or when scheduled_run_count >= max_run_count.
  // If not specified, new runs will keep getting scheduled until this Schedule
  // is paused or deleted. Already scheduled runs will be allowed to complete.
  // Unset if not specified.
  google.protobuf.Timestamp end_time = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum run count of the schedule.
  // If specified, The schedule will be completed when either
  // started_run_count >= max_run_count or when end_time is reached.
  // If not specified, new runs will keep getting scheduled until this Schedule
  // is paused or deleted. Already scheduled runs will be allowed to complete.
  // Unset if not specified.
  int64 max_run_count = 16 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The number of runs started by this schedule.
  int64 started_run_count = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of this Schedule.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Schedule was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Schedule was updated.
  google.protobuf.Timestamp update_time = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Schedule should schedule the next run.
  // Having a next_run_time in the past means the runs are being started
  // behind schedule.
  google.protobuf.Timestamp next_run_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Schedule was last paused.
  // Unset if never paused.
  google.protobuf.Timestamp last_pause_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Schedule was last resumed.
  // Unset if never resumed from pause.
  google.protobuf.Timestamp last_resume_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Maximum number of runs that can be started concurrently for this
  // Schedule. This is the limit for starting the scheduled requests and not the
  // execution of the operations/jobs created by the requests (if applicable).
  int64 max_concurrent_run_count = 11 [(google.api.field_behavior) = REQUIRED];

  // Optional. Whether new scheduled runs can be queued when max_concurrent_runs
  // limit is reached. If set to true, new runs will be queued instead of
  // skipped. Default to false.
  bool allow_queueing = 12 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Whether to backfill missed runs when the schedule is resumed
  // from PAUSED state. If set to true, all missed runs will be scheduled. New
  // runs will be scheduled after the backfill is complete. Default to false.
  bool catch_up = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Response of the last scheduled run.
  // This is the response for starting the scheduled requests and not the
  // execution of the operations/jobs created by the requests (if applicable).
  // Unset if no run has been scheduled yet.
  RunResponse last_scheduled_run_response = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
