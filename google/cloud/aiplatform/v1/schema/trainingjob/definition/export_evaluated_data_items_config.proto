// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1.schema.trainingjob.definition;


option csharp_namespace = "Google.Cloud.AIPlatform.V1.Schema.TrainingJob.Definition";
option go_package = "cloud.google.com/go/aiplatform/apiv1/schema/trainingjob/definition/definitionpb;definitionpb";
option java_multiple_files = true;
option java_outer_classname = "ExportEvaluatedDataItemsConfigProto";
option java_package = "com.google.cloud.aiplatform.v1.schema.trainingjob.definition";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1\\Schema\\TrainingJob\\Definition";
option ruby_package = "Google::Cloud::AIPlatform::V1::Schema::TrainingJob::Definition";

// Configuration for exporting test set predictions to a BigQuery table.
message ExportEvaluatedDataItemsConfig {
  // URI of desired destination BigQuery table. Expected format:
  // bq://<project_id>:<dataset_id>:<table>
  //
  // If not specified, then results are exported to the following auto-created
  // BigQuery table:
  // <project_id>:export_evaluated_examples_<model_name>_<yyyy_MM_dd'T'HH_mm_ss_SSS'Z'>.evaluated_examples
  string destination_bigquery_uri = 1;

  // If true and an export destination is specified, then the contents of the
  // destination are overwritten. Otherwise, if the export destination already
  // exists, then the export operation fails.
  bool override_existing_table = 2;
}
