// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1.schema.trainingjob.definition;


option csharp_namespace = "Google.Cloud.AIPlatform.V1.Schema.TrainingJob.Definition";
option go_package = "cloud.google.com/go/aiplatform/apiv1/schema/trainingjob/definition/definitionpb;definitionpb";
option java_multiple_files = true;
option java_outer_classname = "AutoMLTextClassificationProto";
option java_package = "com.google.cloud.aiplatform.v1.schema.trainingjob.definition";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1\\Schema\\TrainingJob\\Definition";
option ruby_package = "Google::Cloud::AIPlatform::V1::Schema::TrainingJob::Definition";

// A TrainingJob that trains and uploads an AutoML Text Classification Model.
message AutoMlTextClassification {
  // The input parameters of this TrainingJob.
  AutoMlTextClassificationInputs inputs = 1;
}

message AutoMlTextClassificationInputs {
  bool multi_label = 1;
}
