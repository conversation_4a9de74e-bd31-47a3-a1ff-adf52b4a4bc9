// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1.schema.trainingjob.definition;


option csharp_namespace = "Google.Cloud.AIPlatform.V1.Schema.TrainingJob.Definition";
option go_package = "cloud.google.com/go/aiplatform/apiv1/schema/trainingjob/definition/definitionpb;definitionpb";
option java_multiple_files = true;
option java_outer_classname = "AutoMLTextSentimentProto";
option java_package = "com.google.cloud.aiplatform.v1.schema.trainingjob.definition";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1\\Schema\\TrainingJob\\Definition";
option ruby_package = "Google::Cloud::AIPlatform::V1::Schema::TrainingJob::Definition";

// A TrainingJob that trains and uploads an AutoML Text Sentiment Model.
message AutoMlTextSentiment {
  // The input parameters of this TrainingJob.
  AutoMlTextSentimentInputs inputs = 1;
}

message AutoMlTextSentimentInputs {
  // A sentiment is expressed as an integer ordinal, where higher value
  // means a more positive sentiment. The range of sentiments that will be used
  // is between 0 and sentimentMax (inclusive on both ends), and all the values
  // in the range must be represented in the dataset before a model can be
  // created.
  // Only the Annotations with this sentimentMax will be used for training.
  // sentimentMax value must be between 1 and 10 (inclusive).
  int32 sentiment_max = 1;
}
