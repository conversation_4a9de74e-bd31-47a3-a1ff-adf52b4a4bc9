// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1.schema.predict.prediction;

import "google/protobuf/duration.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1.Schema.Predict.Prediction";
option go_package = "cloud.google.com/go/aiplatform/apiv1/schema/predict/prediction/predictionpb;predictionpb";
option java_multiple_files = true;
option java_outer_classname = "VideoClassificationPredictionResultProto";
option java_package = "com.google.cloud.aiplatform.v1.schema.predict.prediction";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1\\Schema\\Predict\\Prediction";
option ruby_package = "Google::Cloud::AIPlatform::V1::Schema::Predict::Prediction";

// Prediction output format for Video Classification.
message VideoClassificationPredictionResult {
  // The resource ID of the AnnotationSpec that had been identified.
  string id = 1;

  // The display name of the AnnotationSpec that had been identified.
  string display_name = 2;

  // The type of the prediction. The requested types can be configured
  // via parameters. This will be one of
  // - segment-classification
  // - shot-classification
  // - one-sec-interval-classification
  string type = 3;

  // The beginning, inclusive, of the video's time segment in which the
  // AnnotationSpec has been identified. Expressed as a number of seconds as
  // measured from the start of the video, with fractions up to a microsecond
  // precision, and with "s" appended at the end. Note that for
  // 'segment-classification' prediction type, this equals the original
  // 'timeSegmentStart' from the input instance, for other types it is the
  // start of a shot or a 1 second interval respectively.
  google.protobuf.Duration time_segment_start = 4;

  // The end, exclusive, of the video's time segment in which the
  // AnnotationSpec has been identified. Expressed as a number of seconds as
  // measured from the start of the video, with fractions up to a microsecond
  // precision, and with "s" appended at the end. Note that for
  // 'segment-classification' prediction type, this equals the original
  // 'timeSegmentEnd' from the input instance, for other types it is the end
  // of a shot or a 1 second interval respectively.
  google.protobuf.Duration time_segment_end = 5;

  // The Model's confidence in correction of this prediction, higher
  // value means higher confidence.
  google.protobuf.FloatValue confidence = 6;
}
