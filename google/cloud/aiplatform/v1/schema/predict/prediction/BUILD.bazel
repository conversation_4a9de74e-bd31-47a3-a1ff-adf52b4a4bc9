# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "prediction_proto",
    srcs = [
        "classification.proto",
        "image_object_detection.proto",
        "image_segmentation.proto",
        "tabular_classification.proto",
        "tabular_regression.proto",
        "text_extraction.proto",
        "text_sentiment.proto",
        "video_action_recognition.proto",
        "video_classification.proto",
        "video_object_tracking.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "prediction_java_proto",
    deps = [":prediction_proto"],
)

java_grpc_library(
    name = "prediction_java_grpc",
    srcs = [":prediction_proto"],
    deps = [":prediction_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "prediction_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/aiplatform/apiv1/schema/predict/prediction/predictionpb",
    protos = [":prediction_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/cloud/aiplatform/v1/schema/predict/instance:instance_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "prediction_moved_proto",
    srcs = [":prediction_proto"],
    deps = [
        "//google/api:annotations_proto",
        "//google/cloud/aiplatform/v1/schema/predict/instance:instance_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

py_proto_library(
    name = "prediction_py_proto",
    deps = [":prediction_moved_proto"],
)

py_grpc_library(
    name = "prediction_py_grpc",
    srcs = [":prediction_moved_proto"],
    deps = [":prediction_py_proto"],
)

py_gapic_library(
    name = "prediction_py_gapic",
    srcs = [":prediction_proto"],
    opt_args = [
        "python-gapic-namespace=google.cloud.aiplatform.v1.schema.predict",  # Replace with the current version
        "python-gapic-name=prediction",
    ],
    rest_numeric_enums = False,
    transport = "grpc",
)

py_gapic_assembly_pkg(
    name = "prediction-py",
    deps = [
        ":prediction_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "prediction_php_proto",
    deps = [":prediction_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "prediction_ruby_proto",
    deps = [":prediction_proto"],
)

ruby_grpc_library(
    name = "prediction_ruby_grpc",
    srcs = [":prediction_proto"],
    deps = [":prediction_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "prediction_csharp_proto",
    extra_opts = [
        "base_namespace=Google.Cloud.AIPlatform.V1",
    ],
    deps = [":prediction_proto"],
)

csharp_grpc_library(
    name = "prediction_csharp_grpc",
    srcs = [":prediction_proto"],
    deps = [":prediction_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "prediction_cc_proto",
    deps = [":prediction_proto"],
)

cc_grpc_library(
    name = "prediction_cc_grpc",
    srcs = [":prediction_proto"],
    grpc_only = True,
    deps = [":prediction_cc_proto"],
)
