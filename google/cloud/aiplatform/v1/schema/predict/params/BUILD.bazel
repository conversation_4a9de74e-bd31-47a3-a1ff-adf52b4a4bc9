# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "params_proto",
    srcs = [
        "image_classification.proto",
        "image_object_detection.proto",
        "image_segmentation.proto",
        "video_action_recognition.proto",
        "video_classification.proto",
        "video_object_tracking.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "params_java_proto",
    deps = [":params_proto"],
)

java_grpc_library(
    name = "params_java_grpc",
    srcs = [":params_proto"],
    deps = [":params_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "params_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/aiplatform/apiv1/schema/predict/params/paramspb",
    protos = [":params_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "params_moved_proto",
    srcs = [":params_proto"],
    deps = [
        "//google/api:annotations_proto",
    ],
)

py_proto_library(
    name = "params_py_proto",
    deps = [":params_moved_proto"],
)

py_grpc_library(
    name = "params_py_grpc",
    srcs = [":params_moved_proto"],
    deps = [":params_py_proto"],
)

py_gapic_library(
    name = "params_py_gapic",
    srcs = [":params_proto"],
    opt_args = [
        "python-gapic-namespace=google.cloud.aiplatform.v1.schema.predict",
        "python-gapic-name=params",
    ],
    rest_numeric_enums = False,
    transport = "grpc",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "params-py",
    deps = [
        ":params_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "params_php_proto",
    deps = [":params_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "params_ruby_proto",
    deps = [":params_proto"],
)

ruby_grpc_library(
    name = "params_ruby_grpc",
    srcs = [":params_proto"],
    deps = [":params_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "params_csharp_proto",
    extra_opts = [
        "base_namespace=Google.Cloud.AIPlatform.V1",
    ],
    deps = [":params_proto"],
)

csharp_grpc_library(
    name = "params_csharp_grpc",
    srcs = [":params_proto"],
    deps = [":params_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "params_cc_proto",
    deps = [":params_proto"],
)

cc_grpc_library(
    name = "params_cc_grpc",
    srcs = [":params_proto"],
    grpc_only = True,
    deps = [":params_cc_proto"],
)
