// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1.schema.predict.instance;


option csharp_namespace = "Google.Cloud.AIPlatform.V1.Schema.Predict.Instance";
option go_package = "cloud.google.com/go/aiplatform/apiv1/schema/predict/instance/instancepb;instancepb";
option java_multiple_files = true;
option java_outer_classname = "TextExtractionPredictionInstanceProto";
option java_package = "com.google.cloud.aiplatform.v1.schema.predict.instance";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1\\Schema\\Predict\\Instance";
option ruby_package = "Google::Cloud::AIPlatform::V1::Schema::Predict::Instance";

// Prediction input format for Text Extraction.
message TextExtractionPredictionInstance {
  // The text snippet to make the predictions on.
  string content = 1;

  // The MIME type of the text snippet. The supported MIME types are listed
  // below.
  // - text/plain
  string mime_type = 2;

  // This field is only used for batch prediction. If a key is provided, the
  // batch prediction result will by mapped to this key. If omitted, then the
  // batch prediction result will contain the entire input instance. Vertex AI
  // will not check if keys in the request are duplicates, so it is up to the
  // caller to ensure the keys are unique.
  string key = 3;
}
