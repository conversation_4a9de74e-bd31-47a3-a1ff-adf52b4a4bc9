// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/encryption_spec.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeaturestoreProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Vertex AI Feature Store provides a centralized repository for organizing,
// storing, and serving ML features. The Featurestore is a top-level container
// for your features and their values.
message Featurestore {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/Featurestore"
    pattern: "projects/{project}/locations/{location}/featurestores/{featurestore}"
  };

  // OnlineServingConfig specifies the details for provisioning online serving
  // resources.
  message OnlineServingConfig {
    // Online serving scaling configuration. If min_node_count and
    // max_node_count are set to the same value, the cluster will be configured
    // with the fixed number of node (no auto-scaling).
    message Scaling {
      // Required. The minimum number of nodes to scale down to. Must be greater
      // than or equal to 1.
      int32 min_node_count = 1 [(google.api.field_behavior) = REQUIRED];

      // The maximum number of nodes to scale up to. Must be greater than
      // min_node_count, and less than or equal to 10 times of 'min_node_count'.
      int32 max_node_count = 2;

      // Optional. The cpu utilization that the Autoscaler should be trying to
      // achieve. This number is on a scale from 0 (no utilization) to 100
      // (total utilization), and is limited between 10 and 80. When a cluster's
      // CPU utilization exceeds the target that you have set, Bigtable
      // immediately adds nodes to the cluster. When CPU utilization is
      // substantially lower than the target, Bigtable removes nodes. If not set
      // or set to 0, default to 50.
      int32 cpu_utilization_target = 3 [(google.api.field_behavior) = OPTIONAL];
    }

    // The number of nodes for the online store. The number of nodes doesn't
    // scale automatically, but you can manually update the number of
    // nodes. If set to 0, the featurestore will not have an
    // online store and cannot be used for online serving.
    int32 fixed_node_count = 2;

    // Online serving scaling configuration.
    // Only one of `fixed_node_count` and `scaling` can be set. Setting one will
    // reset the other.
    Scaling scaling = 4;
  }

  // Possible states a featurestore can have.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // State when the featurestore configuration is not being updated and the
    // fields reflect the current configuration of the featurestore. The
    // featurestore is usable in this state.
    STABLE = 1;

    // The state of the featurestore configuration when it is being updated.
    // During an update, the fields reflect either the original configuration
    // or the updated configuration of the featurestore. For example,
    // `online_serving_config.fixed_node_count` can take minutes to update.
    // While the update is in progress, the featurestore is in the UPDATING
    // state, and the value of `fixed_node_count` can be the original value or
    // the updated value, depending on the progress of the operation. Until the
    // update completes, the actual number of nodes can still be the original
    // value of `fixed_node_count`. The featurestore is still usable in this
    // state.
    UPDATING = 2;
  }

  // Output only. Name of the Featurestore. Format:
  // `projects/{project}/locations/{location}/featurestores/{featurestore}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Featurestore was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this Featurestore was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Used to perform consistent read-modify-write updates. If not set,
  // a blind "overwrite" update happens.
  string etag = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The labels with user-defined metadata to organize your
  // Featurestore.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  //
  // See https://goo.gl/xmQnxf for more information on and examples of labels.
  // No more than 64 user labels can be associated with one Featurestore(System
  // labels are excluded)."
  // System reserved label keys are prefixed with "aiplatform.googleapis.com/"
  // and are immutable.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Config for online storage resources. The field should not
  // co-exist with the field of `OnlineStoreReplicationConfig`. If both of it
  // and OnlineStoreReplicationConfig are unset, the feature store will not have
  // an online store and cannot be used for online serving.
  OnlineServingConfig online_serving_config = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. State of the featurestore.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. TTL in days for feature values that will be stored in online
  // serving storage. The Feature Store online storage periodically removes
  // obsolete feature values older than `online_storage_ttl_days` since the
  // feature generation time. Note that `online_storage_ttl_days` should be less
  // than or equal to `offline_storage_ttl_days` for each EntityType under a
  // featurestore. If not set, default to 4000 days
  int32 online_storage_ttl_days = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Customer-managed encryption key spec for data storage. If set,
  // both of the online and offline data storage will be secured by this key.
  EncryptionSpec encryption_spec = 10 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}
