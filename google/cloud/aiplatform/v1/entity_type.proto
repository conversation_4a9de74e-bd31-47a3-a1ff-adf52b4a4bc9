// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/featurestore_monitoring.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "EntityTypeProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// An entity type is a type of object in a system that needs to be modeled and
// have stored information about. For example, driver is an entity type, and
// driver0 is an instance of an entity type driver.
message EntityType {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/EntityType"
    pattern: "projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}"
  };

  // Immutable. Name of the EntityType.
  // Format:
  // `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}`
  //
  // The last part entity_type is assigned by the client. The entity_type can be
  // up to 64 characters long and can consist only of ASCII Latin letters A-Z
  // and a-z and underscore(_), and ASCII digits 0-9 starting with a letter. The
  // value will be unique given a featurestore.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Optional. Description of the EntityType.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Timestamp when this EntityType was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this EntityType was most recently updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The labels with user-defined metadata to organize your
  // EntityTypes.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  //
  // See https://goo.gl/xmQnxf for more information on and examples of labels.
  // No more than 64 user labels can be associated with one EntityType (System
  // labels are excluded)."
  // System reserved label keys are prefixed with "aiplatform.googleapis.com/"
  // and are immutable.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Used to perform a consistent read-modify-write updates. If not
  // set, a blind "overwrite" update happens.
  string etag = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The default monitoring configuration for all Features with value
  // type
  // ([Feature.ValueType][google.cloud.aiplatform.v1.Feature.ValueType]) BOOL,
  // STRING, DOUBLE or INT64 under this EntityType.
  //
  // If this is populated with
  // [FeaturestoreMonitoringConfig.monitoring_interval] specified, snapshot
  // analysis monitoring is enabled. Otherwise, snapshot analysis monitoring is
  // disabled.
  FeaturestoreMonitoringConfig monitoring_config = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Config for data retention policy in offline storage.
  // TTL in days for feature values that will be stored in offline storage.
  // The Feature Store offline storage periodically removes obsolete feature
  // values older than `offline_storage_ttl_days` since the feature generation
  // time. If unset (or explicitly set to 0), default to 4000 days TTL.
  int32 offline_storage_ttl_days = 10 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}
