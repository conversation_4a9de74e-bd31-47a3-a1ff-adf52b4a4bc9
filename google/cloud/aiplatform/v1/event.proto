// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "EventProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// An edge describing the relationship between an Artifact and an Execution in
// a lineage graph.
message Event {
  // Describes whether an Event's Artifact is the Execution's input or output.
  enum Type {
    // Unspecified whether input or output of the Execution.
    TYPE_UNSPECIFIED = 0;

    // An input of the Execution.
    INPUT = 1;

    // An output of the Execution.
    OUTPUT = 2;
  }

  // Required. The relative resource name of the Artifact in the Event.
  string artifact = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Artifact"
    }
  ];

  // Output only. The relative resource name of the Execution in the Event.
  string execution = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/Execution"
    }
  ];

  // Output only. Time the Event occurred.
  google.protobuf.Timestamp event_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The type of the Event.
  Type type = 4 [(google.api.field_behavior) = REQUIRED];

  // The labels with user-defined metadata to annotate Events.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  // No more than 64 user labels can be associated with one Event (System
  // labels are excluded).
  //
  // See https://goo.gl/xmQnxf for more information and examples of labels.
  // System reserved label keys are prefixed with "aiplatform.googleapis.com/"
  // and are immutable.
  map<string, string> labels = 5;
}
