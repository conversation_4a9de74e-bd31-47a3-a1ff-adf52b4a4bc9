// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/cloud/aiplatform/v1/model.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "UnmanagedContainerModelProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Contains model information necessary to perform batch prediction without
// requiring a full model import.
message UnmanagedContainerModel {
  // The path to the directory containing the Model artifact and any of its
  // supporting files.
  string artifact_uri = 1;

  // Contains the schemata used in Model's predictions and explanations
  PredictSchemata predict_schemata = 2;

  // Input only. The specification of the container that is to be used when
  // deploying this Model.
  ModelContainerSpec container_spec = 3
      [(google.api.field_behavior) = INPUT_ONLY];
}
