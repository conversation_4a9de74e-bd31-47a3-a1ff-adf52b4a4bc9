// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "UserActionReferenceProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// References an API call. It contains more information about long running
// operation and Jobs that are triggered by the API call.
message UserActionReference {
  oneof reference {
    // For API calls that return a long running operation.
    // Resource name of the long running operation.
    // Format:
    // `projects/{project}/locations/{location}/operations/{operation}`
    string operation = 1;

    // For API calls that start a LabelingJob.
    // Resource name of the LabelingJob.
    // Format:
    // `projects/{project}/locations/{location}/dataLabelingJobs/{data_labeling_job}`
    string data_labeling_job = 2;
  }

  // The method name of the API RPC call. For example,
  // "/google.cloud.aiplatform.{apiVersion}.DatasetService.CreateDataset"
  string method = 3;
}
