// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/publisher_model.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ModelGardenServiceProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// The interface of Model Garden Service.
service ModelGardenService {
  option (google.api.default_host) = "aiplatform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets a Model Garden publisher model.
  rpc GetPublisherModel(GetPublisherModelRequest) returns (PublisherModel) {
    option (google.api.http) = {
      get: "/v1/{name=publishers/*/models/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// View enumeration of PublisherModel.
enum PublisherModelView {
  // The default / unset value. The API will default to the BASIC view.
  PUBLISHER_MODEL_VIEW_UNSPECIFIED = 0;

  // Include basic metadata about the publisher model, but not the full
  // contents.
  PUBLISHER_MODEL_VIEW_BASIC = 1;

  // Include everything.
  PUBLISHER_MODEL_VIEW_FULL = 2;

  // Include: VersionId, ModelVersionExternalName, and SupportedActions.
  PUBLISHER_MODEL_VERSION_VIEW_BASIC = 3;
}

// Request message for
// [ModelGardenService.GetPublisherModel][google.cloud.aiplatform.v1.ModelGardenService.GetPublisherModel]
message GetPublisherModelRequest {
  // Required. The name of the PublisherModel resource.
  // Format:
  // `publishers/{publisher}/models/{publisher_model}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aiplatform.googleapis.com/PublisherModel"
    }
  ];

  // Optional. The IETF BCP-47 language code representing the language in which
  // the publisher model's text information should be written in.
  string language_code = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. PublisherModel view specifying which fields to read.
  PublisherModelView view = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Boolean indicates whether the requested model is a Hugging Face
  // model.
  bool is_hugging_face_model = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Token used to access Hugging Face gated models.
  string hugging_face_token = 6 [(google.api.field_behavior) = OPTIONAL];
}
