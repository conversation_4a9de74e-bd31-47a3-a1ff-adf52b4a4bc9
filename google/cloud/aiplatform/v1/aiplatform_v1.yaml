type: google.api.Service
config_version: 3
name: aiplatform.googleapis.com
title: Vertex AI API

apis:
- name: google.cloud.aiplatform.v1.DatasetService
- name: google.cloud.aiplatform.v1.DeploymentResourcePoolService
- name: google.cloud.aiplatform.v1.EndpointService
- name: google.cloud.aiplatform.v1.EvaluationService
- name: google.cloud.aiplatform.v1.FeatureOnlineStoreAdminService
- name: google.cloud.aiplatform.v1.FeatureOnlineStoreService
- name: google.cloud.aiplatform.v1.FeatureRegistryService
- name: google.cloud.aiplatform.v1.FeaturestoreOnlineServingService
- name: google.cloud.aiplatform.v1.FeaturestoreService
- name: google.cloud.aiplatform.v1.GenAiCacheService
- name: google.cloud.aiplatform.v1.GenAiTuningService
- name: google.cloud.aiplatform.v1.IndexEndpointService
- name: google.cloud.aiplatform.v1.IndexService
- name: google.cloud.aiplatform.v1.JobService
- name: google.cloud.aiplatform.v1.LlmUtilityService
- name: google.cloud.aiplatform.v1.MatchService
- name: google.cloud.aiplatform.v1.MetadataService
- name: google.cloud.aiplatform.v1.MigrationService
- name: google.cloud.aiplatform.v1.ModelGardenService
- name: google.cloud.aiplatform.v1.ModelService
- name: google.cloud.aiplatform.v1.NotebookService
- name: google.cloud.aiplatform.v1.PersistentResourceService
- name: google.cloud.aiplatform.v1.PipelineService
- name: google.cloud.aiplatform.v1.PredictionService
- name: google.cloud.aiplatform.v1.ReasoningEngineExecutionService
- name: google.cloud.aiplatform.v1.ReasoningEngineService
- name: google.cloud.aiplatform.v1.ScheduleService
- name: google.cloud.aiplatform.v1.SpecialistPoolService
- name: google.cloud.aiplatform.v1.TensorboardService
- name: google.cloud.aiplatform.v1.VizierService
- name: google.cloud.location.Locations
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.aiplatform.v1.AssignNotebookRuntimeOperationMetadata
- name: google.cloud.aiplatform.v1.BatchCreateFeaturesOperationMetadata
- name: google.cloud.aiplatform.v1.BatchCreateFeaturesResponse
- name: google.cloud.aiplatform.v1.BatchMigrateResourcesOperationMetadata
- name: google.cloud.aiplatform.v1.BatchMigrateResourcesResponse
- name: google.cloud.aiplatform.v1.BatchReadFeatureValuesOperationMetadata
- name: google.cloud.aiplatform.v1.BatchReadFeatureValuesResponse
- name: google.cloud.aiplatform.v1.CheckTrialEarlyStoppingStateMetatdata
- name: google.cloud.aiplatform.v1.CheckTrialEarlyStoppingStateResponse
- name: google.cloud.aiplatform.v1.CopyModelOperationMetadata
- name: google.cloud.aiplatform.v1.CopyModelResponse
- name: google.cloud.aiplatform.v1.CreateDatasetOperationMetadata
- name: google.cloud.aiplatform.v1.CreateDatasetVersionOperationMetadata
- name: google.cloud.aiplatform.v1.CreateDeploymentResourcePoolOperationMetadata
- name: google.cloud.aiplatform.v1.CreateEndpointOperationMetadata
- name: google.cloud.aiplatform.v1.CreateEntityTypeOperationMetadata
- name: google.cloud.aiplatform.v1.CreateFeatureGroupOperationMetadata
- name: google.cloud.aiplatform.v1.CreateFeatureOnlineStoreOperationMetadata
- name: google.cloud.aiplatform.v1.CreateFeatureOperationMetadata
- name: google.cloud.aiplatform.v1.CreateFeatureViewOperationMetadata
- name: google.cloud.aiplatform.v1.CreateFeaturestoreOperationMetadata
- name: google.cloud.aiplatform.v1.CreateIndexEndpointOperationMetadata
- name: google.cloud.aiplatform.v1.CreateIndexOperationMetadata
- name: google.cloud.aiplatform.v1.CreateMetadataStoreOperationMetadata
- name: google.cloud.aiplatform.v1.CreateNotebookExecutionJobOperationMetadata
- name: google.cloud.aiplatform.v1.CreateNotebookRuntimeTemplateOperationMetadata
- name: google.cloud.aiplatform.v1.CreatePersistentResourceOperationMetadata
- name: google.cloud.aiplatform.v1.CreateRegistryFeatureOperationMetadata
- name: google.cloud.aiplatform.v1.CreateSpecialistPoolOperationMetadata
- name: google.cloud.aiplatform.v1.CreateTensorboardOperationMetadata
- name: google.cloud.aiplatform.v1.DeleteFeatureValuesOperationMetadata
- name: google.cloud.aiplatform.v1.DeleteFeatureValuesResponse
- name: google.cloud.aiplatform.v1.DeleteMetadataStoreOperationMetadata
- name: google.cloud.aiplatform.v1.DeleteOperationMetadata
- name: google.cloud.aiplatform.v1.DeployIndexOperationMetadata
- name: google.cloud.aiplatform.v1.DeployIndexResponse
- name: google.cloud.aiplatform.v1.DeployModelOperationMetadata
- name: google.cloud.aiplatform.v1.DeployModelResponse
- name: google.cloud.aiplatform.v1.ExportDataOperationMetadata
- name: google.cloud.aiplatform.v1.ExportDataResponse
- name: google.cloud.aiplatform.v1.ExportFeatureValuesOperationMetadata
- name: google.cloud.aiplatform.v1.ExportFeatureValuesResponse
- name: google.cloud.aiplatform.v1.ExportModelOperationMetadata
- name: google.cloud.aiplatform.v1.ExportModelResponse
- name: google.cloud.aiplatform.v1.ImportDataOperationMetadata
- name: google.cloud.aiplatform.v1.ImportDataResponse
- name: google.cloud.aiplatform.v1.ImportFeatureValuesOperationMetadata
- name: google.cloud.aiplatform.v1.ImportFeatureValuesResponse
- name: google.cloud.aiplatform.v1.MutateDeployedIndexOperationMetadata
- name: google.cloud.aiplatform.v1.MutateDeployedIndexResponse
- name: google.cloud.aiplatform.v1.MutateDeployedModelOperationMetadata
- name: google.cloud.aiplatform.v1.MutateDeployedModelResponse
- name: google.cloud.aiplatform.v1.PurgeArtifactsMetadata
- name: google.cloud.aiplatform.v1.PurgeArtifactsResponse
- name: google.cloud.aiplatform.v1.PurgeContextsMetadata
- name: google.cloud.aiplatform.v1.PurgeContextsResponse
- name: google.cloud.aiplatform.v1.PurgeExecutionsMetadata
- name: google.cloud.aiplatform.v1.PurgeExecutionsResponse
- name: google.cloud.aiplatform.v1.RebootPersistentResourceOperationMetadata
- name: google.cloud.aiplatform.v1.RestoreDatasetVersionOperationMetadata
- name: google.cloud.aiplatform.v1.SpecialistPool
- name: google.cloud.aiplatform.v1.StartNotebookRuntimeOperationMetadata
- name: google.cloud.aiplatform.v1.SuggestTrialsMetadata
- name: google.cloud.aiplatform.v1.SuggestTrialsResponse
- name: google.cloud.aiplatform.v1.UndeployIndexOperationMetadata
- name: google.cloud.aiplatform.v1.UndeployIndexResponse
- name: google.cloud.aiplatform.v1.UndeployModelOperationMetadata
- name: google.cloud.aiplatform.v1.UndeployModelResponse
- name: google.cloud.aiplatform.v1.UpdateDeploymentResourcePoolOperationMetadata
- name: google.cloud.aiplatform.v1.UpdateExplanationDatasetOperationMetadata
- name: google.cloud.aiplatform.v1.UpdateExplanationDatasetResponse
- name: google.cloud.aiplatform.v1.UpdateFeatureGroupOperationMetadata
- name: google.cloud.aiplatform.v1.UpdateFeatureOnlineStoreOperationMetadata
- name: google.cloud.aiplatform.v1.UpdateFeatureOperationMetadata
- name: google.cloud.aiplatform.v1.UpdateFeatureViewOperationMetadata
- name: google.cloud.aiplatform.v1.UpdateFeaturestoreOperationMetadata
- name: google.cloud.aiplatform.v1.UpdateIndexOperationMetadata
- name: google.cloud.aiplatform.v1.UpdateModelDeploymentMonitoringJobOperationMetadata
- name: google.cloud.aiplatform.v1.UpdatePersistentResourceOperationMetadata
- name: google.cloud.aiplatform.v1.UpdateSpecialistPoolOperationMetadata
- name: google.cloud.aiplatform.v1.UpdateTensorboardOperationMetadata
- name: google.cloud.aiplatform.v1.UpgradeNotebookRuntimeOperationMetadata
- name: google.cloud.aiplatform.v1.UploadModelOperationMetadata
- name: google.cloud.aiplatform.v1.UploadModelResponse
- name: google.cloud.aiplatform.v1.schema.ImageBoundingBoxAnnotation
- name: google.cloud.aiplatform.v1.schema.ImageClassificationAnnotation
- name: google.cloud.aiplatform.v1.schema.ImageDataItem
- name: google.cloud.aiplatform.v1.schema.ImageSegmentationAnnotation
- name: google.cloud.aiplatform.v1.schema.TextClassificationAnnotation
- name: google.cloud.aiplatform.v1.schema.TextDataItem
- name: google.cloud.aiplatform.v1.schema.TextExtractionAnnotation
- name: google.cloud.aiplatform.v1.schema.TextSentimentAnnotation
- name: google.cloud.aiplatform.v1.schema.VideoActionRecognitionAnnotation
- name: google.cloud.aiplatform.v1.schema.VideoClassificationAnnotation
- name: google.cloud.aiplatform.v1.schema.VideoDataItem
- name: google.cloud.aiplatform.v1.schema.VideoObjectTrackingAnnotation

documentation:
  summary: |-
    Train high-quality custom machine learning models with minimal machine
    learning expertise and effort.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/ui/{name=projects/*/locations/*}'
    additional_bindings:
    - get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/ui/{name=projects/*}/locations'
    additional_bindings:
    - get: '/v1/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    post: '/v1/{resource=projects/*/locations/*/featurestores/*}:getIamPolicy'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:getIamPolicy'
    - post: '/v1/{resource=projects/*/locations/*/models/*}:getIamPolicy'
    - post: '/v1/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:getIamPolicy'
    - post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*}:getIamPolicy'
    - post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:getIamPolicy'
    - post: '/ui/{resource=projects/*/locations/*/featurestores/*}:getIamPolicy'
    - post: '/ui/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:getIamPolicy'
    - post: '/ui/{resource=projects/*/locations/*/models/*}:getIamPolicy'
    - post: '/ui/{resource=projects/*/locations/*/endpoints/*}:getIamPolicy'
    - post: '/ui/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:getIamPolicy'
    - post: '/ui/{resource=projects/*/locations/*/publishers/*/models/*}:getIamPolicy'
    - post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*}:getIamPolicy'
    - post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1/{resource=projects/*/locations/*/featurestores/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/models/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:setIamPolicy'
      body: '*'
    - post: '/ui/{resource=projects/*/locations/*/featurestores/*}:setIamPolicy'
      body: '*'
    - post: '/ui/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:setIamPolicy'
      body: '*'
    - post: '/ui/{resource=projects/*/locations/*/models/*}:setIamPolicy'
      body: '*'
    - post: '/ui/{resource=projects/*/locations/*/endpoints/*}:setIamPolicy'
      body: '*'
    - post: '/ui/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:setIamPolicy'
      body: '*'
    - post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*}:setIamPolicy'
      body: '*'
    - post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1/{resource=projects/*/locations/*/featurestores/*}:testIamPermissions'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:testIamPermissions'
    - post: '/v1/{resource=projects/*/locations/*/models/*}:testIamPermissions'
    - post: '/v1/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:testIamPermissions'
    - post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*}:testIamPermissions'
    - post: '/v1/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:testIamPermissions'
    - post: '/ui/{resource=projects/*/locations/*/featurestores/*}:testIamPermissions'
    - post: '/ui/{resource=projects/*/locations/*/featurestores/*/entityTypes/*}:testIamPermissions'
    - post: '/ui/{resource=projects/*/locations/*/models/*}:testIamPermissions'
    - post: '/ui/{resource=projects/*/locations/*/endpoints/*}:testIamPermissions'
    - post: '/ui/{resource=projects/*/locations/*/notebookRuntimeTemplates/*}:testIamPermissions'
    - post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*}:testIamPermissions'
    - post: '/ui/{resource=projects/*/locations/*/featureOnlineStores/*/featureViews/*}:testIamPermissions'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/ui/{name=projects/*/locations/*/operations/*}:cancel'
    additional_bindings:
    - post: '/ui/{name=projects/*/locations/*/agents/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/apps/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/edgeDevices/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/extensionControllers/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/extensions/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/tuningJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/metadataStores/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/modelMonitors/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/models/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/persistentResources/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/studies/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/schedules/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:cancel'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/datasets/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/endpoints/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/featurestores/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/customJobs/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/tuningJobs/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/indexes/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/metadataStores/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/migratableResources/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/models/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/persistentResources/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/ragCorpora/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/reasoningEngines/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/studies/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/schedules/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/specialistPools/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/tensorboards/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:cancel'
    - post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/ui/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - delete: '/ui/{name=projects/*/locations/*/agents/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/apps/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/edgeDevices/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/extensionControllers/*}/operations'
    - delete: '/ui/{name=projects/*/locations/*/extensions/*}/operations'
    - delete: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/metadataStores/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/modelMonitors/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/models/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/persistentResources/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/studies/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/schedules/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/featureGroups/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}'
    - delete: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/datasets/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/endpoints/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/featurestores/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/customJobs/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/indexes/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/metadataStores/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/migratableResources/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/models/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/ragCorpora/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/reasoningEngines/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/studies/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/persistentResources/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/schedules/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/specialistPools/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/tensorboards/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/featureGroups/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}'
    - delete: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/ui/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/ui/{name=projects/*/locations/*/agents/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/apps/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/edgeDeploymentJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/edgeDevices/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/extensionControllers/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/extensions/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/tuningJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/metadataStores/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/modelMonitors/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/models/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/persistentResources/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/studies/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/schedules/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/featureGroups/*/operations/*}'
    - get: '/ui/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/datasets/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/endpoints/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/featurestores/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/customJobs/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/tuningJobs/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/indexes/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/metadataStores/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/migratableResources/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/models/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/ragCorpora/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/reasoningEngines/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/studies/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/persistentResources/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/schedules/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/specialistPools/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/tensorboards/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/featureGroups/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/ui/{name=projects/*/locations/*}/operations'
    additional_bindings:
    - get: '/ui/{name=projects/*/locations/*/agents/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/apps/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/datasets/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/deploymentResourcePools/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/edgeDevices/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/endpoints/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/extensionControllers/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/extensions/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/customJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/tuningJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/indexes/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/indexEndpoints/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/metadataStores/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/metadataStores/*/artifacts/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/metadataStores/*/contexts/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/metadataStores/*/executions/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/modelMonitors/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/migratableResources/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/models/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/models/*/evaluations/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/notebookExecutionJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/notebookRuntimes/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/notebookRuntimeTemplates/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/studies/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/studies/*/trials/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/trainingPipelines/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/persistentResources/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/pipelineJobs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/schedules/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/specialistPools/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}/operations'
    - get: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}:wait'
    - get: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}:wait'
    - get: '/ui/{name=projects/*/locations/*/featureGroups/*/operations/*}:wait'
    - get: '/ui/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}:wait'
    - get: '/v1/{name=projects/*/locations/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/datasets/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/deploymentResourcePools/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/endpoints/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/featurestores/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/customJobs/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/dataLabelingJobs/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/tuningJobs/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/indexes/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/indexEndpoints/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/metadataStores/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/metadataStores/*/artifacts/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/metadataStores/*/contexts/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/metadataStores/*/executions/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/migratableResources/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/models/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/models/*/evaluations/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/notebookExecutionJobs/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/notebookRuntimes/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/notebookRuntimeTemplates/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/reasoningEngines/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/studies/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/studies/*/trials/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/trainingPipelines/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/persistentResources/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/pipelineJobs/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/ragCorpora/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/schedules/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/specialistPools/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/tensorboards/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}:wait'
    - get: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}:wait'
    - get: '/v1/{name=projects/*/locations/*/featureGroups/*/operations/*}:wait'
    - get: '/v1/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}:wait'
  - selector: google.longrunning.Operations.WaitOperation
    post: '/ui/{name=projects/*/locations/*/operations/*}:wait'
    additional_bindings:
    - post: '/ui/{name=projects/*/locations/*/agents/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/apps/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/edgeDevices/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/endpoints/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/extensionControllers/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/extensions/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/customJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/tuningJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/indexes/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/metadataStores/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/modelMonitors/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/migratableResources/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/models/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/studies/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/persistentResources/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/schedules/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/specialistPools/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/featureGroups/*/operations/*}:wait'
    - post: '/ui/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/datasets/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/datasets/*/savedQueries/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/datasets/*/annotationSpecs/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/datasets/*/dataItems/*/annotations/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/deploymentResourcePools/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/endpoints/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/featurestores/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/featurestores/*/entityTypes/*/features/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/customJobs/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/dataLabelingJobs/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/hyperparameterTuningJobs/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/indexes/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/indexEndpoints/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/metadataStores/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/metadataStores/*/artifacts/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/metadataStores/*/contexts/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/metadataStores/*/executions/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/modelDeploymentMonitoringJobs/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/migratableResources/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/models/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/models/*/evaluations/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/notebookExecutionJobs/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/notebookRuntimes/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/notebookRuntimeTemplates/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/ragCorpora/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/ragCorpora/*/ragFiles/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/reasoningEngines/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/studies/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/studies/*/trials/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/trainingPipelines/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/persistentResources/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/pipelineJobs/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/schedules/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/specialistPools/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/tensorboards/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/tensorboards/*/experiments/*/runs/*/timeSeries/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/featureOnlineStores/*/featureViews/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/featureGroups/*/operations/*}:wait'
    - post: '/v1/{name=projects/*/locations/*/featureGroups/*/features/*/operations/*}:wait'

authentication:
  rules:
  - selector: 'google.cloud.aiplatform.v1.DatasetService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.DeploymentResourcePoolService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.EndpointService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.EvaluationService.EvaluateInstances
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.FeatureOnlineStoreAdminService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.FeatureOnlineStoreService.FetchFeatureValues
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.FeatureOnlineStoreService.SearchNearestEntities
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.FeatureRegistryService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.FeaturestoreOnlineServingService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.FeaturestoreService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.GenAiCacheService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.GenAiTuningService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.IndexEndpointService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.IndexService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.JobService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.JobService.GetNasJob
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.LlmUtilityService.ComputeTokens
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.LlmUtilityService.CountTokens
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.MatchService.FindNeighbors
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.MatchService.ReadIndexDatapoints
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.MetadataService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.MigrationService.BatchMigrateResources
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.MigrationService.SearchMigratableResources
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.ModelGardenService.GetPublisherModel
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.ModelService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.NotebookService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.PersistentResourceService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.PipelineService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.PredictionService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.ReasoningEngineExecutionService.QueryReasoningEngine
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.ReasoningEngineExecutionService.StreamQueryReasoningEngine
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.ReasoningEngineService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.ScheduleService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.SpecialistPoolService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.TensorboardService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.aiplatform.v1.TensorboardService.GetTensorboard
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.TensorboardService.GetTensorboardExperiment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.TensorboardService.GetTensorboardRun
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.TensorboardService.GetTensorboardTimeSeries
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.TensorboardService.ListTensorboardExperiments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.TensorboardService.ListTensorboardRuns
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.TensorboardService.ListTensorboardTimeSeries
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.TensorboardService.ListTensorboards
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.TensorboardService.ReadTensorboardBlobData
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.aiplatform.v1.TensorboardService.ReadTensorboardTimeSeriesData
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.cloud.aiplatform.v1.VertexRagDataService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.VertexRagService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.aiplatform.v1.VizierService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1130925&template=1637248
  documentation_uri: https://cloud.google.com/ai-platform/docs
  github_label: 'api: aiplatform'
  organization: CLOUD
  library_settings:
  - version: google.cloud.aiplatform.v1
    java_settings:
      common: {}
    cpp_settings:
      common: {}
    php_settings:
      common: {}
    python_settings:
      common: {}
      experimental_features:
        rest_async_io_enabled: true
    node_settings:
      common: {}
    dotnet_settings:
      common: {}
      renamed_resources:
        datalabeling.googleapis.com/Dataset: DataLabelingDataset
        automl.googleapis.com/Dataset: AutoMLDataset
        automl.googleapis.com/Model: AutoMLModel
    ruby_settings:
      common: {}
    go_settings:
      common: {}
