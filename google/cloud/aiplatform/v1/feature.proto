// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/aiplatform/v1/feature_monitoring_stats.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "FeatureProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Feature Metadata information.
// For example, color is a feature that describes an apple.
message Feature {
  option (google.api.resource) = {
    type: "aiplatform.googleapis.com/Feature"
    pattern: "projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}/features/{feature}"
    pattern: "projects/{project}/locations/{location}/featureGroups/{feature_group}/features/{feature}"
    plural: "features"
    singular: "feature"
  };

  // A list of historical
  // [SnapshotAnalysis][google.cloud.aiplatform.v1.FeaturestoreMonitoringConfig.SnapshotAnalysis]
  // or
  // [ImportFeaturesAnalysis][google.cloud.aiplatform.v1.FeaturestoreMonitoringConfig.ImportFeaturesAnalysis]
  // stats requested by user, sorted by
  // [FeatureStatsAnomaly.start_time][google.cloud.aiplatform.v1.FeatureStatsAnomaly.start_time]
  // descending.
  message MonitoringStatsAnomaly {
    // If the objective in the request is both
    // Import Feature Analysis and Snapshot Analysis, this objective could be
    // one of them. Otherwise, this objective should be the same as the
    // objective in the request.
    enum Objective {
      // If it's OBJECTIVE_UNSPECIFIED, monitoring_stats will be empty.
      OBJECTIVE_UNSPECIFIED = 0;

      // Stats are generated by Import Feature Analysis.
      IMPORT_FEATURE_ANALYSIS = 1;

      // Stats are generated by Snapshot Analysis.
      SNAPSHOT_ANALYSIS = 2;
    }

    // Output only. The objective for each stats.
    Objective objective = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The stats and anomalies generated at specific timestamp.
    FeatureStatsAnomaly feature_stats_anomaly = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Only applicable for Vertex AI Legacy Feature Store.
  // An enum representing the value type of a feature.
  enum ValueType {
    // The value type is unspecified.
    VALUE_TYPE_UNSPECIFIED = 0;

    // Used for Feature that is a boolean.
    BOOL = 1;

    // Used for Feature that is a list of boolean.
    BOOL_ARRAY = 2;

    // Used for Feature that is double.
    DOUBLE = 3;

    // Used for Feature that is a list of double.
    DOUBLE_ARRAY = 4;

    // Used for Feature that is INT64.
    INT64 = 9;

    // Used for Feature that is a list of INT64.
    INT64_ARRAY = 10;

    // Used for Feature that is string.
    STRING = 11;

    // Used for Feature that is a list of String.
    STRING_ARRAY = 12;

    // Used for Feature that is bytes.
    BYTES = 13;

    // Used for Feature that is struct.
    STRUCT = 14;
  }

  // Immutable. Name of the Feature.
  // Format:
  // `projects/{project}/locations/{location}/featurestores/{featurestore}/entityTypes/{entity_type}/features/{feature}`
  // `projects/{project}/locations/{location}/featureGroups/{feature_group}/features/{feature}`
  //
  // The last part feature is assigned by the client. The feature can be up to
  // 64 characters long and can consist only of ASCII Latin letters A-Z and a-z,
  // underscore(_), and ASCII digits 0-9 starting with a letter. The value will
  // be unique given an entity type.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Description of the Feature.
  string description = 2;

  // Immutable. Only applicable for Vertex AI Feature Store (Legacy).
  // Type of Feature value.
  ValueType value_type = 3 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Only applicable for Vertex AI Feature Store (Legacy).
  // Timestamp when this EntityType was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Only applicable for Vertex AI Feature Store (Legacy).
  // Timestamp when this EntityType was most recently updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The labels with user-defined metadata to organize your Features.
  //
  // Label keys and values can be no longer than 64 characters
  // (Unicode codepoints), can only contain lowercase letters, numeric
  // characters, underscores and dashes. International characters are allowed.
  //
  // See https://goo.gl/xmQnxf for more information on and examples of labels.
  // No more than 64 user labels can be associated with one Feature (System
  // labels are excluded)."
  // System reserved label keys are prefixed with "aiplatform.googleapis.com/"
  // and are immutable.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Used to perform a consistent read-modify-write updates. If not set, a blind
  // "overwrite" update happens.
  string etag = 7;

  // Optional. Only applicable for Vertex AI Feature Store (Legacy).
  // If not set, use the monitoring_config defined for the EntityType this
  // Feature belongs to.
  // Only Features with type
  // ([Feature.ValueType][google.cloud.aiplatform.v1.Feature.ValueType]) BOOL,
  // STRING, DOUBLE or INT64 can enable monitoring.
  //
  // If set to true, all types of data monitoring are disabled despite the
  // config on EntityType.
  bool disable_monitoring = 12 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Only applicable for Vertex AI Feature Store (Legacy).
  // The list of historical stats and anomalies with specified objectives.
  repeated MonitoringStatsAnomaly monitoring_stats_anomalies = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Only applicable for Vertex AI Feature Store.
  // The name of the BigQuery Table/View column hosting data for this version.
  // If no value is provided, will use feature_id.
  string version_column_name = 106;

  // Entity responsible for maintaining this feature. Can be comma separated
  // list of email addresses or URIs.
  string point_of_contact = 107;
}
