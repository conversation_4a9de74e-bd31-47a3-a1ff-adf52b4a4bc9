// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AIPlatform.V1";
option go_package = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb;aiplatformpb";
option java_multiple_files = true;
option java_outer_classname = "ManualBatchTuningParametersProto";
option java_package = "com.google.cloud.aiplatform.v1";
option php_namespace = "Google\\Cloud\\AIPlatform\\V1";
option ruby_package = "Google::Cloud::AIPlatform::V1";

// Manual batch tuning parameters.
message ManualBatchTuningParameters {
  // Immutable. The number of the records (e.g. instances) of the operation
  // given in each batch to a machine replica. Machine type, and size of a
  // single record should be considered when setting this parameter, higher
  // value speeds up the batch operation's execution, but too high value will
  // result in a whole batch not fitting in a machine's memory, and the whole
  // operation will fail.
  // The default value is 64.
  int32 batch_size = 1 [(google.api.field_behavior) = IMMUTABLE];
}
