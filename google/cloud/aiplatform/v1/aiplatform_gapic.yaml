type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  java:
    package_name: com.google.cloud.aiplatform.v1
  python:
    package_name: google.cloud.aiplatform_v1.gapic
  go:
    package_name: cloud.google.com/go/aiplatform/apiv1
  csharp:
    package_name: Google.Aiplatform.V1
  ruby:
    package_name: Google::Cloud::Aiplatform::V1
  php:
    package_name: Google\Cloud\Aiplatform\V1
  nodejs:
    package_name: aiplatform.v1
    domain_layer_location: google-cloud
