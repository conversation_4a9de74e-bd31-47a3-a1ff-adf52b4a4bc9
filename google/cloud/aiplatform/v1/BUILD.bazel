# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

load("@com_google_googleapis_imports//:imports.bzl", "cc_grpc_library", "cc_proto_library", "csharp_gapic_assembly_pkg", "csharp_gapic_library", "csharp_grpc_library", "csharp_proto_library", "go_gapic_assembly_pkg", "go_gapic_library", "go_proto_library", "java_gapic_assembly_gradle_pkg", "java_gapic_library", "java_gapic_test", "java_grpc_library", "java_proto_library", "nodejs_gapic_assembly_pkg", "nodejs_gapic_library", "php_gapic_assembly_pkg", "php_gapic_library", "php_proto_library", "proto_library_with_info", "py_gapic_assembly_pkg", "py_gapic_library", "py_test", "ruby_cloud_gapic_library", "ruby_gapic_assembly_pkg", "ruby_grpc_library", "ruby_proto_library")

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

_PROTO_SUBPACKAGE_DEPS = [
    "//google/cloud/aiplatform/v1/schema/predict/instance:instance_proto",
    "//google/cloud/aiplatform/v1/schema/predict/params:params_proto",
    "//google/cloud/aiplatform/v1/schema/predict/prediction:prediction_proto",
    "//google/cloud/aiplatform/v1/schema/trainingjob/definition:definition_proto",
]

proto_library(
    name = "aiplatform_proto",
    srcs = [
        "accelerator_type.proto",
        "annotation.proto",
        "annotation_spec.proto",
        "api_auth.proto",
        "artifact.proto",
        "batch_prediction_job.proto",
        "cached_content.proto",
        "completion_stats.proto",
        "content.proto",
        "context.proto",
        "custom_job.proto",
        "data_item.proto",
        "data_labeling_job.proto",
        "dataset.proto",
        "dataset_service.proto",
        "dataset_version.proto",
        "deployed_index_ref.proto",
        "deployed_model_ref.proto",
        "deployment_resource_pool.proto",
        "deployment_resource_pool_service.proto",
        "encryption_spec.proto",
        "endpoint.proto",
        "endpoint_service.proto",
        "entity_type.proto",
        "env_var.proto",
        "evaluated_annotation.proto",
        "evaluation_service.proto",
        "event.proto",
        "execution.proto",
        "explanation.proto",
        "explanation_metadata.proto",
        "feature.proto",
        "feature_group.proto",
        "feature_monitoring_stats.proto",
        "feature_online_store.proto",
        "feature_online_store_admin_service.proto",
        "feature_online_store_service.proto",
        "feature_registry_service.proto",
        "feature_selector.proto",
        "feature_view.proto",
        "feature_view_sync.proto",
        "featurestore.proto",
        "featurestore_monitoring.proto",
        "featurestore_online_service.proto",
        "featurestore_service.proto",
        "gen_ai_cache_service.proto",
        "genai_tuning_service.proto",
        "hyperparameter_tuning_job.proto",
        "index.proto",
        "index_endpoint.proto",
        "index_endpoint_service.proto",
        "index_service.proto",
        "io.proto",
        "job_service.proto",
        "job_state.proto",
        "lineage_subgraph.proto",
        "llm_utility_service.proto",
        "machine_resources.proto",
        "manual_batch_tuning_parameters.proto",
        "match_service.proto",
        "metadata_schema.proto",
        "metadata_service.proto",
        "metadata_store.proto",
        "migratable_resource.proto",
        "migration_service.proto",
        "model.proto",
        "model_deployment_monitoring_job.proto",
        "model_evaluation.proto",
        "model_evaluation_slice.proto",
        "model_garden_service.proto",
        "model_monitoring.proto",
        "model_service.proto",
        "nas_job.proto",
        "network_spec.proto",
        "notebook_euc_config.proto",
        "notebook_execution_job.proto",
        "notebook_idle_shutdown_config.proto",
        "notebook_runtime.proto",
        "notebook_runtime_template_ref.proto",
        "notebook_service.proto",
        "openapi.proto",
        "operation.proto",
        "persistent_resource.proto",
        "persistent_resource_service.proto",
        "pipeline_failure_policy.proto",
        "pipeline_job.proto",
        "pipeline_service.proto",
        "pipeline_state.proto",
        "prediction_service.proto",
        "publisher_model.proto",
        "reasoning_engine.proto",
        "reasoning_engine_execution_service.proto",
        "reasoning_engine_service.proto",
        "reservation_affinity.proto",
        "saved_query.proto",
        "schedule.proto",
        "schedule_service.proto",
        "service_networking.proto",
        "specialist_pool.proto",
        "specialist_pool_service.proto",
        "study.proto",
        "tensorboard.proto",
        "tensorboard_data.proto",
        "tensorboard_experiment.proto",
        "tensorboard_run.proto",
        "tensorboard_service.proto",
        "tensorboard_time_series.proto",
        "tool.proto",
        "training_pipeline.proto",
        "tuning_job.proto",
        "types.proto",
        "unmanaged_container_model.proto",
        "user_action_reference.proto",
        "value.proto",
        "vertex_rag_data.proto",
        "vertex_rag_data_service.proto",
        "vertex_rag_service.proto",
        "vizier_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:httpbody_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:date_proto",
        "//google/type:interval_proto",
        "//google/type:latlng_proto",
        "//google/type:money_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ] + _PROTO_SUBPACKAGE_DEPS,
)

proto_library_with_info(
    name = "aiplatform_proto_with_info",
    deps = [
        ":aiplatform_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ] + _PROTO_SUBPACKAGE_DEPS,
)

_JAVA_PROTO_SUBPACKAGE_DEPS = [
    "//google/cloud/aiplatform/v1/schema/predict/instance:instance_java_proto",
    "//google/cloud/aiplatform/v1/schema/predict/params:params_java_proto",
    "//google/cloud/aiplatform/v1/schema/predict/prediction:prediction_java_proto",
    "//google/cloud/aiplatform/v1/schema/trainingjob/definition:definition_java_proto",
]

_JAVA_GRPC_SUBPACKAGE_DEPS = [
    "//google/cloud/aiplatform/v1/schema/predict/instance:instance_java_grpc",
    "//google/cloud/aiplatform/v1/schema/predict/params:params_java_grpc",
    "//google/cloud/aiplatform/v1/schema/predict/prediction:prediction_java_grpc",
    "//google/cloud/aiplatform/v1/schema/trainingjob/definition:definition_java_grpc",
]

java_proto_library(
    name = "aiplatform_java_proto",
    deps = [":aiplatform_proto"],
)

java_grpc_library(
    name = "aiplatform_java_grpc",
    srcs = [":aiplatform_proto"],
    deps = [":aiplatform_java_proto"] + _JAVA_PROTO_SUBPACKAGE_DEPS,
)

java_gapic_library(
    name = "aiplatform_java_gapic",
    srcs = [":aiplatform_proto_with_info"],
    gapic_yaml = "aiplatform_gapic.yaml",
    grpc_service_config = "aiplatform_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "aiplatform_v1.yaml",
    test_deps = [
        ":aiplatform_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ] + _JAVA_PROTO_SUBPACKAGE_DEPS,
    transport = "grpc",
    deps = [
        ":aiplatform_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ] + _JAVA_PROTO_SUBPACKAGE_DEPS,
)

java_gapic_test(
    name = "aiplatform_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.aiplatform.v1.DatasetServiceClientTest",
        "com.google.cloud.aiplatform.v1.DeploymentResourcePoolServiceClientTest",
        "com.google.cloud.aiplatform.v1.EndpointServiceClientTest",
        "com.google.cloud.aiplatform.v1.EvaluationServiceClientTest",
        "com.google.cloud.aiplatform.v1.FeatureOnlineStoreAdminServiceClientTest",
        "com.google.cloud.aiplatform.v1.FeatureOnlineStoreServiceClientTest",
        "com.google.cloud.aiplatform.v1.FeatureRegistryServiceClientTest",
        "com.google.cloud.aiplatform.v1.FeaturestoreOnlineServingServiceClientTest",
        "com.google.cloud.aiplatform.v1.FeaturestoreServiceClientTest",
        "com.google.cloud.aiplatform.v1.GenAiTuningServiceClientTest",
        "com.google.cloud.aiplatform.v1.IndexEndpointServiceClientTest",
        "com.google.cloud.aiplatform.v1.IndexServiceClientTest",
        "com.google.cloud.aiplatform.v1.JobServiceClientTest",
        "com.google.cloud.aiplatform.v1.LlmUtilityServiceClientTest",
        "com.google.cloud.aiplatform.v1.MatchServiceClientTest",
        "com.google.cloud.aiplatform.v1.MetadataServiceClientTest",
        "com.google.cloud.aiplatform.v1.MigrationServiceClientTest",
        "com.google.cloud.aiplatform.v1.ModelGardenServiceClientTest",
        "com.google.cloud.aiplatform.v1.ModelServiceClientTest",
        "com.google.cloud.aiplatform.v1.NotebookServiceClientTest",
        "com.google.cloud.aiplatform.v1.PersistentResourceServiceClientTest",
        "com.google.cloud.aiplatform.v1.PipelineServiceClientTest",
        "com.google.cloud.aiplatform.v1.PredictionServiceClientTest",
        "com.google.cloud.aiplatform.v1.ScheduleServiceClientTest",
        "com.google.cloud.aiplatform.v1.SpecialistPoolServiceClientTest",
        "com.google.cloud.aiplatform.v1.TensorboardServiceClientTest",
        "com.google.cloud.aiplatform.v1.VizierServiceClientTest",
    ],
    runtime_deps = [":aiplatform_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-aiplatform-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":aiplatform_java_gapic",
        ":aiplatform_java_grpc",
        ":aiplatform_java_proto",
        ":aiplatform_proto",
    ] + _JAVA_PROTO_SUBPACKAGE_DEPS + _PROTO_SUBPACKAGE_DEPS + _JAVA_GRPC_SUBPACKAGE_DEPS,
)

go_proto_library(
    name = "aiplatform_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/aiplatform/apiv1/aiplatformpb",
    protos = [":aiplatform_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:date_go_proto",
        "//google/type:interval_go_proto",
        "//google/type:latlng_go_proto",
        "//google/type:money_go_proto",
    ],
)

go_gapic_library(
    name = "aiplatform_go_gapic",
    srcs = [":aiplatform_proto_with_info"],
    grpc_service_config = "aiplatform_grpc_service_config.json",
    importpath = "cloud.google.com/go/aiplatform/apiv1;aiplatform",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "aiplatform_v1.yaml",
    transport = "grpc",
    deps = [
        ":aiplatform_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-aiplatform-v1-go",
    deps = [
        ":aiplatform_go_gapic",
        ":aiplatform_go_gapic_srcjar-metadata.srcjar",
        ":aiplatform_go_gapic_srcjar-snippets.srcjar",
        ":aiplatform_go_gapic_srcjar-test.srcjar",
        ":aiplatform_go_proto",
    ],
)

_PY_GAPIC_SUBPACKAGE_DEPS = [
    "//google/cloud/aiplatform/v1/schema/predict/instance:instance_py_gapic",
    "//google/cloud/aiplatform/v1/schema/predict/params:params_py_gapic",
    "//google/cloud/aiplatform/v1/schema/predict/prediction:prediction_py_gapic",
    "//google/cloud/aiplatform/v1/schema/trainingjob/definition:definition_py_gapic",
]

py_gapic_library(
    name = "aiplatform_py_gapic",
    srcs = [":aiplatform_proto"],
    grpc_service_config = "aiplatform_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "aiplatform_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "aiplatform_py_gapic_test",
    srcs = [
        "aiplatform_py_gapic_pytest.py",
        "aiplatform_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":aiplatform_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "aiplatform-v1-py",
    deps = [
        ":aiplatform_py_gapic",
    ] + _PROTO_SUBPACKAGE_DEPS + _PY_GAPIC_SUBPACKAGE_DEPS,
)

php_proto_library(
    name = "aiplatform_php_proto",
    deps = [":aiplatform_proto"],
)

php_gapic_library(
    name = "aiplatform_php_gapic",
    srcs = [":aiplatform_proto_with_info"],
    grpc_service_config = "aiplatform_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "aiplatform_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":aiplatform_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-aiplatform-v1-php",
    deps = [
        ":aiplatform_php_gapic",
        ":aiplatform_php_proto",
    ],
)

nodejs_gapic_library(
    name = "aiplatform_nodejs_gapic",
    package_name = "@google-cloud/aiplatform",
    src = ":aiplatform_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "aiplatform_grpc_service_config.json",
    package = "google.cloud.aiplatform.v1",
    rest_numeric_enums = True,
    service_yaml = "aiplatform_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "aiplatform-v1-nodejs",
    deps = [
        ":aiplatform_nodejs_gapic",
        ":aiplatform_proto",
    ] + _PROTO_SUBPACKAGE_DEPS,
)

ruby_proto_library(
    name = "aiplatform_ruby_proto",
    deps = [":aiplatform_proto"],
)

ruby_grpc_library(
    name = "aiplatform_ruby_grpc",
    srcs = [":aiplatform_proto"],
    deps = [":aiplatform_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "aiplatform_ruby_gapic",
    srcs = [":aiplatform_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=aiplatform.googleapis.com",
        "ruby-cloud-api-shortname=aiplatform",
        "ruby-cloud-gem-name=google-cloud-ai_platform-v1",
        "ruby-cloud-gem-namespace=Google::Cloud::AIPlatform::V1",
        "ruby-cloud-product-url=https://cloud.google.com/vertex-ai/docs/",
        "ruby-cloud-service-override=AiPlatform=AIPlatform",
    ],
    grpc_service_config = "aiplatform_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Vertex AI enables data scientists, developers, and AI newcomers to create custom machine learning models specific to their business needs by leveraging Google's state-of-the-art transfer learning and innovative AI research.",
    ruby_cloud_title = "Vertex AI V1",
    service_yaml = "aiplatform_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":aiplatform_ruby_grpc",
        ":aiplatform_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-aiplatform-v1-ruby",
    deps = [
        ":aiplatform_ruby_gapic",
        ":aiplatform_ruby_grpc",
        ":aiplatform_ruby_proto",
    ],
)

_CSHARP_PROTO_SUBPACKAGE_DEPS = [
    "//google/cloud/aiplatform/v1/schema/predict/instance:instance_csharp_proto",
    "//google/cloud/aiplatform/v1/schema/predict/params:params_csharp_proto",
    "//google/cloud/aiplatform/v1/schema/predict/prediction:prediction_csharp_proto",
    "//google/cloud/aiplatform/v1/schema/trainingjob/definition:definition_csharp_proto",
]

csharp_proto_library(
    name = "aiplatform_csharp_proto",
    extra_opts = [],
    deps = [":aiplatform_proto"],
)

csharp_grpc_library(
    name = "aiplatform_csharp_grpc",
    srcs = [":aiplatform_proto"],
    deps = [":aiplatform_csharp_proto"],
)

csharp_gapic_library(
    name = "aiplatform_csharp_gapic",
    srcs = [":aiplatform_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "aiplatform_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "aiplatform_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":aiplatform_csharp_grpc",
        ":aiplatform_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-aiplatform-v1-csharp",
    deps = [
        ":aiplatform_csharp_gapic",
        ":aiplatform_csharp_grpc",
        ":aiplatform_csharp_proto",
    ] + _CSHARP_PROTO_SUBPACKAGE_DEPS,
)

cc_proto_library(
    name = "aiplatform_cc_proto",
    deps = [":aiplatform_proto"],
)

cc_grpc_library(
    name = "aiplatform_cc_grpc",
    srcs = [":aiplatform_proto"],
    grpc_only = True,
    deps = [":aiplatform_cc_proto"],
)
