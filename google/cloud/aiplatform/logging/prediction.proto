// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.aiplatform.logging;

import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/aiplatform/logging/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "PredictionProto";
option java_package = "com.google.cloud.aiplatform.logging";

// The access log entry definition of online prediction.
message OnlinePredictionLogEntry {
  // The resource name of the endpoint as referred to in the original request.
  // For example, projects/12323/locations/us-central1/endpoints/123.
  string endpoint = 1;

  // The ID of the deployed model used to serve this predict request.
  string deployed_model_id = 2;

  // The number of instances in the prediction request.
  int64 instance_count = 3;

  // The number of successfully predicted instances in the response.
  // Populated when prediction succeeds.
  int64 prediction_count = 4;

  // The error code and message.
  // Populated when prediction fails.
  google.rpc.Status error = 5;
}
