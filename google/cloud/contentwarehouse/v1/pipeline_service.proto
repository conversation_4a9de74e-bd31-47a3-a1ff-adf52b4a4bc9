// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/contentwarehouse/v1/common.proto";
import "google/cloud/contentwarehouse/v1/pipelines.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "PipelineServiceProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// This service lets you manage pipelines.
service PipelineService {
  option (google.api.default_host) = "contentwarehouse.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Run a predefined pipeline.
  rpc RunPipeline(RunPipelineRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*}:runPipeline"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "RunPipelineResponse"
      metadata_type: "RunPipelineMetadata"
    };
  }
}

// Request message for DocumentService.RunPipeline.
message RunPipelineRequest {
  // Required. The resource name which owns the resources of the pipeline.
  // Format: projects/{project_number}/locations/{location}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Location"
    }
  ];

  // The predefined pipelines.
  oneof pipeline {
    // Cloud Storage ingestion pipeline.
    GcsIngestPipeline gcs_ingest_pipeline = 2;

    // Use DocAI processors to process documents in Cloud Storage and ingest
    // them to Document Warehouse.
    GcsIngestWithDocAiProcessorsPipeline
        gcs_ingest_with_doc_ai_processors_pipeline = 3;

    // Export docuemnts from Document Warehouse to CDW for training purpose.
    ExportToCdwPipeline export_cdw_pipeline = 4;

    // Use a DocAI processor to process documents in Document Warehouse, and
    // re-ingest the updated results into Document Warehouse.
    ProcessWithDocAiPipeline process_with_doc_ai_pipeline = 5;
  }

  // The meta information collected about the end user, used to enforce access
  // control for the service.
  RequestMetadata request_metadata = 6;
}
