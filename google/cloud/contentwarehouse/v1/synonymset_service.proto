// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/contentwarehouse/v1/synonymset.proto";
import "google/cloud/contentwarehouse/v1/synonymset_service_request.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "SynonymSetServiceProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// A Service that manage/custom customer specified SynonymSets.
service SynonymSetService {
  option (google.api.default_host) = "contentwarehouse.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a SynonymSet for a single context.
  // Throws an ALREADY_EXISTS exception if a synonymset already exists
  // for the context.
  rpc CreateSynonymSet(CreateSynonymSetRequest) returns (SynonymSet) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/synonymSets"
      body: "synonym_set"
    };
    option (google.api.method_signature) = "parent,synonym_set";
  }

  // Gets a SynonymSet for a particular context.
  // Throws a NOT_FOUND exception if the Synonymset
  // does not exist
  rpc GetSynonymSet(GetSynonymSetRequest) returns (SynonymSet) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/synonymSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Remove the existing SynonymSet for the context and replaces it
  // with a new one.
  // Throws a NOT_FOUND exception if the SynonymSet is not found.
  rpc UpdateSynonymSet(UpdateSynonymSetRequest) returns (SynonymSet) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/synonymSets/*}"
      body: "synonym_set"
    };
    option (google.api.method_signature) = "name,synonym_set";
  }

  // Deletes a SynonymSet for a given context.
  // Throws a NOT_FOUND exception if the SynonymSet is not found.
  rpc DeleteSynonymSet(DeleteSynonymSetRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/synonymSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns all SynonymSets (for all contexts) for the specified location.
  rpc ListSynonymSets(ListSynonymSetsRequest)
      returns (ListSynonymSetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/synonymSets"
    };
    option (google.api.method_signature) = "parent";
  }
}
