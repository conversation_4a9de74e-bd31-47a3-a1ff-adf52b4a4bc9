// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/contentwarehouse/v1/common.proto";
import "google/cloud/contentwarehouse/v1/document.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "DocumentLinkServiceProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// This service lets you manage document-links.
// Document-Links are treated as sub-resources under source documents.
service DocumentLinkService {
  option (google.api.default_host) = "contentwarehouse.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Return all target document-links from the document.
  rpc ListLinkedTargets(ListLinkedTargetsRequest)
      returns (ListLinkedTargetsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/documents/*}/linkedTargets"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }

  // Return all source document-links from the document.
  rpc ListLinkedSources(ListLinkedSourcesRequest)
      returns (ListLinkedSourcesResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/documents/*}/linkedSources"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create a link between a source document and a target document.
  rpc CreateDocumentLink(CreateDocumentLinkRequest) returns (DocumentLink) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/documents/*}/documentLinks"
      body: "*"
    };
    option (google.api.method_signature) = "parent,document_link";
  }

  // Remove the link between the source and target documents.
  rpc DeleteDocumentLink(DeleteDocumentLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/documents/*/documentLinks/*}:delete"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }
}

// Response message for DocumentLinkService.ListLinkedTargets.
message ListLinkedTargetsResponse {
  // Target document-links.
  repeated DocumentLink document_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for DocumentLinkService.ListLinkedTargets.
message ListLinkedTargetsRequest {
  // Required. The name of the document, for which all target links are
  // returned. Format:
  // projects/{project_number}/locations/{location}/documents/{target_document_id}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Document"
    }
  ];

  // The meta information collected about the document creator, used to enforce
  // access control for the service.
  RequestMetadata request_metadata = 2;
}

// Response message for DocumentLinkService.ListLinkedSources.
message ListLinkedSourcesResponse {
  // Source document-links.
  repeated DocumentLink document_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Response message for DocumentLinkService.ListLinkedSources.
message ListLinkedSourcesRequest {
  // Required. The name of the document, for which all source links are
  // returned. Format:
  // projects/{project_number}/locations/{location}/documents/{source_document_id}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Document"
    }
  ];

  // The maximum number of document-links to return. The service may return
  // fewer than this value.
  //
  // If unspecified, at most 50 document-links will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 3;

  // A page token, received from a previous `ListLinkedSources` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListLinkedSources`
  // must match the call that provided the page token.
  string page_token = 4;

  // The meta information collected about the document creator, used to enforce
  // access control for the service.
  RequestMetadata request_metadata = 2;
}

// A document-link between source and target document.
message DocumentLink {
  option (google.api.resource) = {
    type: "contentwarehouse.googleapis.com/DocumentLink"
    pattern: "projects/{project}/locations/{location}/documents/{document}/documentLinks/{document_link}"
  };

  // The state of a document-link.
  enum State {
    // Unknown state of documentlink.
    STATE_UNSPECIFIED = 0;

    // The documentlink has both source and target documents detected.
    ACTIVE = 1;

    // Target document is deleted, and mark the documentlink as soft-deleted.
    SOFT_DELETED = 2;
  }

  // Name of this document-link.
  // It is required that the parent derived form the name to be consistent with
  // the source document reference. Otherwise an exception will be thrown.
  // Format:
  // projects/{project_number}/locations/{location}/documents/{source_document_id}/documentLinks/{document_link_id}.
  string name = 1;

  // Document references of the source document.
  DocumentReference source_document_reference = 2;

  // Document references of the target document.
  DocumentReference target_document_reference = 3;

  // Description of this document-link.
  string description = 4;

  // Output only. The time when the documentLink is last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the documentLink is created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The state of the documentlink. If target node has been deleted, the
  // link is marked as invalid. Removing a source node will result in removal
  // of all associated links.
  State state = 7;
}

// Request message for DocumentLinkService.CreateDocumentLink.
message CreateDocumentLinkRequest {
  // Required. Parent of the document-link to be created.
  // parent of document-link should be a document.
  // Format:
  // projects/{project_number}/locations/{location}/documents/{source_document_id}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Document"
    }
  ];

  // Required. Document links associated with the source documents
  // (source_document_id).
  DocumentLink document_link = 2 [(google.api.field_behavior) = REQUIRED];

  // The meta information collected about the document creator, used to enforce
  // access control for the service.
  RequestMetadata request_metadata = 3;
}

// Request message for DocumentLinkService.DeleteDocumentLink.
message DeleteDocumentLinkRequest {
  // Required. The name of the document-link to be deleted.
  // Format:
  // projects/{project_number}/locations/{location}/documents/{source_document_id}/documentLinks/{document_link_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/DocumentLink"
    }
  ];

  // The meta information collected about the document creator, used to enforce
  // access control for the service.
  RequestMetadata request_metadata = 2;
}
