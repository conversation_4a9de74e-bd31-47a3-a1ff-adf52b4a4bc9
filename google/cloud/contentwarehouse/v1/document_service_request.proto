// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/contentwarehouse/v1/common.proto";
import "google/cloud/contentwarehouse/v1/document.proto";
import "google/cloud/contentwarehouse/v1/filters.proto";
import "google/cloud/contentwarehouse/v1/histogram.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "DocumentServiceRequestProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// Request Option for processing Cloud AI Document in CW Document.
message CloudAIDocumentOption {
  // Whether to convert all the entities to properties.
  bool enable_entities_conversions = 1;

  // If set, only selected entities will be converted to properties.
  map<string, string> customized_entities_properties_conversions = 2;
}

// Request message for DocumentService.CreateDocument.
message CreateDocumentRequest {
  // Required. The parent name.
  // Format: projects/{project_number}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Location"
    }
  ];

  // Required. The document to create.
  Document document = 2 [(google.api.field_behavior) = REQUIRED];

  // The meta information collected about the end user, used to enforce access
  // control for the service.
  RequestMetadata request_metadata = 3;

  // Default document policy during creation.
  // This refers to an Identity and Access (IAM) policy, which specifies access
  // controls for the Document.
  // Conditions defined in the policy will be ignored.
  google.iam.v1.Policy policy = 4;

  // Request Option for processing Cloud AI Document in Document Warehouse.
  // This field offers limited support for mapping entities from Cloud AI
  // Document to Warehouse Document. Please consult with product team before
  // using this field and other available options.
  CloudAIDocumentOption cloud_ai_document_option = 5;

  // Field mask for creating Document fields. If mask path is empty,
  // it means all fields are masked.
  // For the `FieldMask` definition,
  // see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
  google.protobuf.FieldMask create_mask = 6;
}

// Request message for DocumentService.GetDocument.
message GetDocumentRequest {
  // Required. The name of the document to retrieve.
  // Format:
  // projects/{project_number}/locations/{location}/documents/{document_id} or
  // projects/{project_number}/locations/{location}/documents/referenceId/{reference_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Document"
    }
  ];

  // The meta information collected about the end user, used to enforce access
  // control for the service.
  RequestMetadata request_metadata = 2;
}

// Request message for DocumentService.UpdateDocument.
message UpdateDocumentRequest {
  // Required. The name of the document to update.
  // Format:
  // projects/{project_number}/locations/{location}/documents/{document_id}
  // or
  // projects/{project_number}/locations/{location}/documents/referenceId/{reference_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Document"
    }
  ];

  // Required. The document to update.
  Document document = 2 [(google.api.field_behavior) = REQUIRED];

  // The meta information collected about the end user, used to enforce access
  // control for the service.
  RequestMetadata request_metadata = 3;

  // Request Option for processing Cloud AI Document in Document Warehouse.
  // This field offers limited support for mapping entities from Cloud AI
  // Document to Warehouse Document. Please consult with product team before
  // using this field and other available options.
  CloudAIDocumentOption cloud_ai_document_option = 5;

  // Options for the update operation.
  UpdateOptions update_options = 6;
}

// Request message for DocumentService.DeleteDocument.
message DeleteDocumentRequest {
  // Required. The name of the document to delete.
  // Format:
  // projects/{project_number}/locations/{location}/documents/{document_id}
  // or
  // projects/{project_number}/locations/{location}/documents/referenceId/{reference_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Document"
    }
  ];

  // The meta information collected about the end user, used to enforce access
  // control for the service.
  RequestMetadata request_metadata = 2;
}

// Request message for DocumentService.SearchDocuments.
message SearchDocumentsRequest {
  // The total number of matching documents.
  enum TotalResultSize {
    // Total number calculation will be skipped.
    TOTAL_RESULT_SIZE_UNSPECIFIED = 0;

    // Estimate total number. The total result size will be accurated up to
    // 10,000. This option will add cost and latency to your request.
    ESTIMATED_SIZE = 1;

    // It may adversely impact performance. The limit is 1000,000.
    ACTUAL_SIZE = 2;
  }

  // Required. The parent, which owns this collection of documents.
  // Format: projects/{project_number}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Location"
    }
  ];

  // The meta information collected about the end user, used to enforce access
  // control and improve the search quality of the service.
  RequestMetadata request_metadata = 3;

  // Query used to search against documents (keyword, filters, etc.).
  DocumentQuery document_query = 4;

  // An integer that specifies the current offset (that is, starting result
  // location, amongst the documents deemed by the API as relevant) in search
  // results. This field is only considered if
  // [page_token][google.cloud.contentwarehouse.v1.SearchDocumentsRequest.page_token]
  // is unset.
  //
  // The maximum allowed value is 5000. Otherwise an error is thrown.
  //
  // For example, 0 means to  return results starting from the first matching
  // document, and 10 means to return from the 11th document. This can be used
  // for pagination, (for example, pageSize = 10 and offset = 10 means to return
  // from the second page).
  int32 offset = 5;

  // A limit on the number of documents returned in the search results.
  // Increasing this value above the default value of 10 can increase search
  // response time. The value can be between 1 and 100.
  int32 page_size = 6;

  // The token specifying the current offset within search results.
  // See
  // [SearchDocumentsResponse.next_page_token][google.cloud.contentwarehouse.v1.SearchDocumentsResponse.next_page_token]
  // for an explanation of how to obtain the next set of query results.
  string page_token = 7;

  // The criteria determining how search results are sorted. For non-empty
  // query, default is `"relevance desc"`. For empty query, default is
  // `"upload_date desc"`.
  //
  // Supported options are:
  //
  // * `"relevance desc"`: By relevance descending, as determined by the API
  //   algorithms.
  // * `"upload_date desc"`: By upload date descending.
  // * `"upload_date"`: By upload date ascending.
  // * `"update_date desc"`: By last updated date descending.
  // * `"update_date"`: By last updated date ascending.
  // * `"retrieval_importance desc"`: By retrieval importance of properties
  //   descending. This feature is still under development, please do not use
  //   unless otherwise instructed to do so.
  string order_by = 8;

  // An expression specifying a histogram request against matching
  // documents. Expression syntax is an aggregation function call with
  // histogram facets and other options.
  //
  // The following aggregation functions are supported:
  //
  // * `count(string_histogram_facet)`: Count the number of matching entities
  // for each distinct attribute value.
  //
  // Data types:
  //
  // * Histogram facet (aka filterable properties): Facet names with format
  // &lt;schema id&gt;.&lt;facet&gt;. Facets will have the
  // format of: `[a-zA-Z][a-zA-Z0-9_:/-.]`. If the facet is a child
  // facet, then the parent hierarchy needs to be specified separated by
  // dots in the prefix after the schema id. Thus, the format for a multi-
  // level facet is: &lt;schema id&gt;.&lt;parent facet name&gt;.
  // &lt;child facet name&gt;. Example:
  // schema123.root_parent_facet.middle_facet.child_facet
  // * DocumentSchemaId: (with no schema id prefix) to get
  // histograms for each document type (returns the schema id path, e.g.
  // projects/12345/locations/us-west/documentSchemas/abc123).
  //
  // Example expression:
  //
  // * Document type counts:
  //   count('DocumentSchemaId')
  //
  // * For schema id, abc123, get the counts for MORTGAGE_TYPE:
  //   count('abc123.MORTGAGE_TYPE')
  repeated HistogramQuery histogram_queries = 9;

  // Controls if the search document request requires the return of a total size
  // of matched documents. See
  // [SearchDocumentsResponse.total_size][google.cloud.contentwarehouse.v1.SearchDocumentsResponse.total_size].
  //
  // Enabling this flag may adversely impact performance. Hint: If this is
  // used with pagination, set this flag on the initial query but set this
  // to false on subsequent page calls (keep the total count locally).
  //
  // Defaults to false.
  bool require_total_size = 10;

  // Controls if the search document request requires the return of a total size
  // of matched documents. See
  // [SearchDocumentsResponse.total_size][google.cloud.contentwarehouse.v1.SearchDocumentsResponse.total_size].
  TotalResultSize total_result_size = 12;

  // Experimental, do not use.
  // The limit on the number of documents returned for the question-answering
  // feature. To enable the question-answering feature, set
  // [DocumentQuery].[is_nl_query][] to true.
  int32 qa_size_limit = 11;
}

// Request message for DocumentService.LockDocument.
message LockDocumentRequest {
  // Required. The name of the document to lock.
  // Format:
  // projects/{project_number}/locations/{location}/documents/{document}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Document"
    }
  ];

  // The collection the document connects to.
  string collection_id = 2;

  // The user information who locks the document.
  UserInfo locking_user = 3;
}

// Request message for DocumentService.FetchAcl
message FetchAclRequest {
  // Required. REQUIRED: The resource for which the policy is being requested.
  // Format for document:
  // projects/{project_number}/locations/{location}/documents/{document_id}.
  // Format for collection:
  // projects/{project_number}/locations/{location}/collections/{collection_id}.
  // Format for project: projects/{project_number}.
  string resource = 1 [(google.api.field_behavior) = REQUIRED];

  // The meta information collected about the end user, used to enforce access
  // control for the service.
  RequestMetadata request_metadata = 2;

  // For Get Project ACL only. Authorization check for end user will be ignored
  // when project_owner=true.
  bool project_owner = 3;
}

// Request message for DocumentService.SetAcl.
message SetAclRequest {
  // Required. REQUIRED: The resource for which the policy is being requested.
  // Format for document:
  // projects/{project_number}/locations/{location}/documents/{document_id}.
  // Format for collection:
  // projects/{project_number}/locations/{location}/collections/{collection_id}.
  // Format for project: projects/{project_number}.
  string resource = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. REQUIRED: The complete policy to be applied to the `resource`.
  // The size of the policy is limited to a few 10s of KB. This refers to an
  // Identity and Access (IAM) policy, which specifies access controls for the
  // Document.
  //
  // You can set ACL with condition for projects only.
  //
  // Supported operators are: `=`, `!=`, `<`, `<=`, `>`, and `>=` where
  // the left of the operator is `DocumentSchemaId` or property name and the
  // right of the operator is a number or a quoted string. You must escape
  // backslash (\\) and quote (\") characters.
  //
  // Boolean expressions (AND/OR) are supported up to 3 levels of nesting (for
  // example, "((A AND B AND C) OR D) AND E"), a maximum of 10 comparisons are
  // allowed in the expression. The expression must be < 6000 bytes in length.
  //
  // Sample condition:
  //     `"DocumentSchemaId = \"some schema id\" OR SchemaId.floatPropertyName
  //     >= 10"`
  google.iam.v1.Policy policy = 2 [(google.api.field_behavior) = REQUIRED];

  // The meta information collected about the end user, used to enforce access
  // control for the service.
  RequestMetadata request_metadata = 3;

  // For Set Project ACL only. Authorization check for end user will be ignored
  // when project_owner=true.
  bool project_owner = 4;
}
