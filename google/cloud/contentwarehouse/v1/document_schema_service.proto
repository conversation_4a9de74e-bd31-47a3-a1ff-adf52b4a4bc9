// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/contentwarehouse/v1/document_schema.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "DocumentSchemaServiceProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// This service lets you manage document schema.
service DocumentSchemaService {
  option (google.api.default_host) = "contentwarehouse.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a document schema.
  rpc CreateDocumentSchema(CreateDocumentSchemaRequest)
      returns (DocumentSchema) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/documentSchemas"
      body: "document_schema"
    };
    option (google.api.method_signature) = "parent,document_schema";
  }

  // Updates a Document Schema. Returns INVALID_ARGUMENT if the name of the
  // Document Schema is non-empty and does not equal the existing name.
  // Supports only appending new properties, adding new ENUM possible values,
  // and updating the
  // [EnumTypeOptions.validation_check_disabled][google.cloud.contentwarehouse.v1.EnumTypeOptions.validation_check_disabled]
  // flag for ENUM possible values. Updating existing properties will result
  // into INVALID_ARGUMENT.
  rpc UpdateDocumentSchema(UpdateDocumentSchemaRequest)
      returns (DocumentSchema) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/documentSchemas/*}"
      body: "*"
    };
    option (google.api.method_signature) = "name,document_schema";
  }

  // Gets a document schema. Returns NOT_FOUND if the document schema does not
  // exist.
  rpc GetDocumentSchema(GetDocumentSchemaRequest) returns (DocumentSchema) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/documentSchemas/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a document schema. Returns NOT_FOUND if the document schema does
  // not exist. Returns BAD_REQUEST if the document schema has documents
  // depending on it.
  rpc DeleteDocumentSchema(DeleteDocumentSchemaRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/documentSchemas/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists document schemas.
  rpc ListDocumentSchemas(ListDocumentSchemasRequest)
      returns (ListDocumentSchemasResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/documentSchemas"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for DocumentSchemaService.CreateDocumentSchema.
message CreateDocumentSchemaRequest {
  // Required. The parent name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Location"
    }
  ];

  // Required. The document schema to create.
  DocumentSchema document_schema = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DocumentSchemaService.GetDocumentSchema.
message GetDocumentSchemaRequest {
  // Required. The name of the document schema to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/DocumentSchema"
    }
  ];
}

// Request message for DocumentSchemaService.UpdateDocumentSchema.
message UpdateDocumentSchemaRequest {
  // Required. The name of the document schema to update.
  // Format:
  // projects/{project_number}/locations/{location}/documentSchemas/{document_schema_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/DocumentSchema"
    }
  ];

  // Required. The document schema to update with.
  DocumentSchema document_schema = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for DocumentSchemaService.DeleteDocumentSchema.
message DeleteDocumentSchemaRequest {
  // Required. The name of the document schema to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/DocumentSchema"
    }
  ];
}

// Request message for DocumentSchemaService.ListDocumentSchemas.
message ListDocumentSchemasRequest {
  // Required. The parent, which owns this collection of document schemas.
  // Format: projects/{project_number}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Location"
    }
  ];

  // The maximum number of document schemas to return. The service may return
  // fewer than this value.
  // If unspecified, at most 50 document schemas will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListDocumentSchemas` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListDocumentSchemas`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for DocumentSchemaService.ListDocumentSchemas.
message ListDocumentSchemasResponse {
  // The document schemas from the specified parent.
  repeated DocumentSchema document_schemas = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
