// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";
option (google.api.resource_definition) = {
  type: "contentwarehouse.googleapis.com/Location"
  pattern: "projects/{project}/locations/{location}"
};

// Meta information is used to improve the performance of the service.
message RequestMetadata {
  // Provides user unique identification and groups information.
  UserInfo user_info = 1;
}

// Additional information returned to client, such as debugging information.
message ResponseMetadata {
  // A unique id associated with this call. This id is logged for tracking
  // purpose.
  string request_id = 1;
}

// The user information.
message UserInfo {
  // A unique user identification string, as determined by the client.
  // The maximum number of allowed characters is 255.
  // Allowed characters include numbers 0 to 9, uppercase and lowercase letters,
  // and restricted special symbols (:, @, +, -, _, ~)
  // The format is "user:<EMAIL>";
  string id = 1;

  // The unique group identifications which the user is belong to.
  // The format is "group:<EMAIL>";
  repeated string group_ids = 2;
}

// Options for Update operations.
message UpdateOptions {
  // Type for update.
  UpdateType update_type = 1;

  // Field mask for merging Document fields.
  // For the `FieldMask` definition,
  // see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 2;

  // Options for merging.
  MergeFieldsOptions merge_fields_options = 3;
}

// Options for merging updated fields.
message MergeFieldsOptions {
  // When merging message fields, the default behavior is to merge
  // the content of two message fields together. If you instead want to use
  // the field from the source message to replace the corresponding field in
  // the destination message, set this flag to true. When this flag is set,
  // specified submessage fields that are missing in source will be cleared in
  // destination.
  optional bool replace_message_fields = 1;

  // When merging repeated fields, the default behavior is to append
  // entries from the source repeated field to the destination repeated field.
  // If you instead want to keep only the entries from the source repeated
  // field, set this flag to true.
  //
  // If you want to replace a repeated field within a message field on the
  // destination message, you must set both replace_repeated_fields and
  // replace_message_fields to true, otherwise the repeated fields will be
  // appended.
  optional bool replace_repeated_fields = 2;
}

// Update type of the requests.
enum UpdateType {
  // Defaults to full replace behavior, ie. FULL_REPLACE.
  UPDATE_TYPE_UNSPECIFIED = 0;

  // Fully replace all the fields (including previously linked raw document).
  // Any field masks will be ignored.
  UPDATE_TYPE_REPLACE = 1;

  // Merge the fields into the existing entities.
  UPDATE_TYPE_MERGE = 2;

  // Inserts the properties by names.
  UPDATE_TYPE_INSERT_PROPERTIES_BY_NAMES = 3;

  // Replace the properties by names.
  UPDATE_TYPE_REPLACE_PROPERTIES_BY_NAMES = 4;

  // Delete the properties by names.
  UPDATE_TYPE_DELETE_PROPERTIES_BY_NAMES = 5;

  // For each of the property, replaces the property if the it exists, otherwise
  // inserts a new property. And for the rest of the fields, merge them based on
  // update mask and merge fields options.
  UPDATE_TYPE_MERGE_AND_REPLACE_OR_INSERT_PROPERTIES_BY_NAMES = 6;
}

// Type of database used by the customer
enum DatabaseType {
  // This value is required by protobuf best practices
  DB_UNKNOWN = 0;

  // Internal Spanner
  DB_INFRA_SPANNER = 1;

  // Cloud Sql with a Postgres Sql instance
  DB_CLOUD_SQL_POSTGRES = 2 [deprecated = true];
}

// Access Control Mode.
enum AccessControlMode {
  // This value is required by protobuf best practices
  ACL_MODE_UNKNOWN = 0;

  // Universal Access: No document level access control.
  ACL_MODE_UNIVERSAL_ACCESS = 1;

  // Document level access control with customer own Identity Service.
  ACL_MODE_DOCUMENT_LEVEL_ACCESS_CONTROL_BYOID = 2;

  // Document level access control using Google Cloud Identity.
  ACL_MODE_DOCUMENT_LEVEL_ACCESS_CONTROL_GCI = 3;
}

// The default role of the document creator.
enum DocumentCreatorDefaultRole {
  // Unspecified, will be default to document admin role.
  DOCUMENT_CREATOR_DEFAULT_ROLE_UNSPECIFIED = 0;

  // Document Admin, same as contentwarehouse.googleapis.com/documentAdmin.
  DOCUMENT_ADMIN = 1;

  // Document Editor, same as contentwarehouse.googleapis.com/documentEditor.
  DOCUMENT_EDITOR = 2;

  // Document Viewer, same as contentwarehouse.googleapis.com/documentViewer.
  DOCUMENT_VIEWER = 3;
}
