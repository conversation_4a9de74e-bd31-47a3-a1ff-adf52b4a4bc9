# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "contentwarehouse_proto",
    srcs = [
        "async_document_service_request.proto",
        "common.proto",
        "document.proto",
        "document_link_service.proto",
        "document_schema.proto",
        "document_schema_service.proto",
        "document_service.proto",
        "document_service_request.proto",
        "filters.proto",
        "histogram.proto",
        "pipeline_service.proto",
        "pipelines.proto",
        "rule_engine.proto",
        "ruleset_service.proto",
        "ruleset_service_request.proto",
        "synonymset.proto",
        "synonymset_service.proto",
        "synonymset_service_request.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/cloud/documentai/v1:documentai_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:datetime_proto",
        "//google/type:interval_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "contentwarehouse_proto_with_info",
    deps = [
        ":contentwarehouse_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "contentwarehouse_java_proto",
    deps = [":contentwarehouse_proto"],
)

java_grpc_library(
    name = "contentwarehouse_java_grpc",
    srcs = [":contentwarehouse_proto"],
    deps = [":contentwarehouse_java_proto"],
)

java_gapic_library(
    name = "contentwarehouse_java_gapic",
    srcs = [":contentwarehouse_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "contentwarehouse_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "contentwarehouse_v1.yaml",
    test_deps = [
        "//google/iam/v1:iam_java_grpc",
        ":contentwarehouse_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":contentwarehouse_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "contentwarehouse_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.contentwarehouse.v1.DocumentLinkServiceClientHttpJsonTest",
        "com.google.cloud.contentwarehouse.v1.DocumentLinkServiceClientTest",
        "com.google.cloud.contentwarehouse.v1.DocumentSchemaServiceClientHttpJsonTest",
        "com.google.cloud.contentwarehouse.v1.DocumentSchemaServiceClientTest",
        "com.google.cloud.contentwarehouse.v1.DocumentServiceClientHttpJsonTest",
        "com.google.cloud.contentwarehouse.v1.DocumentServiceClientTest",
        "com.google.cloud.contentwarehouse.v1.PipelineServiceClientHttpJsonTest",
        "com.google.cloud.contentwarehouse.v1.PipelineServiceClientTest",
        "com.google.cloud.contentwarehouse.v1.RuleSetServiceClientHttpJsonTest",
        "com.google.cloud.contentwarehouse.v1.RuleSetServiceClientTest",
        "com.google.cloud.contentwarehouse.v1.SynonymSetServiceClientHttpJsonTest",
        "com.google.cloud.contentwarehouse.v1.SynonymSetServiceClientTest",
    ],
    runtime_deps = [":contentwarehouse_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-contentwarehouse-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":contentwarehouse_java_gapic",
        ":contentwarehouse_java_grpc",
        ":contentwarehouse_java_proto",
        ":contentwarehouse_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "contentwarehouse_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb",
    protos = [":contentwarehouse_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/cloud/documentai/v1:documentai_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:datetime_go_proto",
        "//google/type:interval_go_proto",
    ],
)

go_gapic_library(
    name = "contentwarehouse_go_gapic",
    srcs = [":contentwarehouse_proto_with_info"],
    grpc_service_config = "contentwarehouse_grpc_service_config.json",
    importpath = "cloud.google.com/go/contentwarehouse/apiv1;contentwarehouse",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "contentwarehouse_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":contentwarehouse_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-contentwarehouse-v1-go",
    deps = [
        ":contentwarehouse_go_gapic",
        ":contentwarehouse_go_gapic_srcjar-metadata.srcjar",
        ":contentwarehouse_go_gapic_srcjar-snippets.srcjar",
        ":contentwarehouse_go_gapic_srcjar-test.srcjar",
        ":contentwarehouse_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
    "py_test",
)

py_import(
    name = "documentai",
    srcs = [
        "//google/cloud/documentai/v1:documentai_py_gapic",
    ],
)

py_gapic_library(
    name = "contentwarehouse_py_gapic",
    srcs = [":contentwarehouse_proto"],
    grpc_service_config = "contentwarehouse_grpc_service_config.json",
    opt_args = ["proto-plus-deps=google.cloud.documentai.v1"],
    rest_numeric_enums = True,
    service_yaml = "contentwarehouse_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":documentai",
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "contentwarehouse_py_gapic_test",
    srcs = [
        "contentwarehouse_py_gapic_pytest.py",
        "contentwarehouse_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":contentwarehouse_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "contentwarehouse-v1-py",
    deps = [
        ":contentwarehouse_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "contentwarehouse_php_proto",
    deps = [":contentwarehouse_proto"],
)

php_gapic_library(
    name = "contentwarehouse_php_gapic",
    srcs = [":contentwarehouse_proto_with_info"],
    grpc_service_config = "contentwarehouse_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "contentwarehouse_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":contentwarehouse_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-contentwarehouse-v1-php",
    deps = [
        ":contentwarehouse_php_gapic",
        ":contentwarehouse_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "contentwarehouse_nodejs_gapic",
    package_name = "@google-cloud/contentwarehouse",
    src = ":contentwarehouse_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "contentwarehouse_grpc_service_config.json",
    package = "google.cloud.contentwarehouse.v1",
    rest_numeric_enums = True,
    service_yaml = "contentwarehouse_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "contentwarehouse-v1-nodejs",
    deps = [
        ":contentwarehouse_nodejs_gapic",
        ":contentwarehouse_proto",
        "//google/cloud/documentai/v1:documentai_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "contentwarehouse_ruby_proto",
    deps = [":contentwarehouse_proto"],
)

ruby_grpc_library(
    name = "contentwarehouse_ruby_grpc",
    srcs = [":contentwarehouse_proto"],
    deps = [":contentwarehouse_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "contentwarehouse_ruby_gapic",
    srcs = [":contentwarehouse_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-contentwarehouse-v1"],
    grpc_service_config = "contentwarehouse_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "contentwarehouse_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":contentwarehouse_ruby_grpc",
        ":contentwarehouse_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-contentwarehouse-v1-ruby",
    deps = [
        ":contentwarehouse_ruby_gapic",
        ":contentwarehouse_ruby_grpc",
        ":contentwarehouse_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "contentwarehouse_csharp_proto",
    extra_opts = [],
    deps = [":contentwarehouse_proto"],
)

csharp_grpc_library(
    name = "contentwarehouse_csharp_grpc",
    srcs = [":contentwarehouse_proto"],
    deps = [":contentwarehouse_csharp_proto"],
)

csharp_gapic_library(
    name = "contentwarehouse_csharp_gapic",
    srcs = [":contentwarehouse_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "contentwarehouse_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "contentwarehouse_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":contentwarehouse_csharp_grpc",
        ":contentwarehouse_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-contentwarehouse-v1-csharp",
    deps = [
        ":contentwarehouse_csharp_gapic",
        ":contentwarehouse_csharp_grpc",
        ":contentwarehouse_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "contentwarehouse_cc_proto",
    deps = [":contentwarehouse_proto"],
)

cc_grpc_library(
    name = "contentwarehouse_cc_grpc",
    srcs = [":contentwarehouse_proto"],
    grpc_only = True,
    deps = [":contentwarehouse_cc_proto"],
)
