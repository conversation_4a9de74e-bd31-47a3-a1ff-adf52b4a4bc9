// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/contentwarehouse/v1/rule_engine.proto";
import "google/cloud/contentwarehouse/v1/ruleset_service_request.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "RuleSetServiceProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// Service to manage customer specific RuleSets.
service RuleSetService {
  option (google.api.default_host) = "contentwarehouse.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a ruleset.
  rpc CreateRuleSet(CreateRuleSetRequest) returns (RuleSet) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/ruleSets"
      body: "rule_set"
    };
    option (google.api.method_signature) = "parent,rule_set";
  }

  // Gets a ruleset. Returns NOT_FOUND if the ruleset does not exist.
  rpc GetRuleSet(GetRuleSetRequest) returns (RuleSet) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/ruleSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a ruleset. Returns INVALID_ARGUMENT if the name of the ruleset
  // is non-empty and does not equal the existing name.
  rpc UpdateRuleSet(UpdateRuleSetRequest) returns (RuleSet) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/ruleSets/*}"
      body: "*"
    };
    option (google.api.method_signature) = "name,rule_set";
  }

  // Deletes a ruleset. Returns NOT_FOUND if the document does not exist.
  rpc DeleteRuleSet(DeleteRuleSetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/ruleSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists rulesets.
  rpc ListRuleSets(ListRuleSetsRequest) returns (ListRuleSetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/ruleSets"
    };
    option (google.api.method_signature) = "parent";
  }
}
