{"methodConfig": [{"name": [{"service": "google.cloud.contentwarehouse.v1.DocumentSchemaService", "method": "GetDocumentSchema"}, {"service": "google.cloud.contentwarehouse.v1.DocumentSchemaService", "method": "ListDocumentSchemas"}, {"service": "google.cloud.contentwarehouse.v1.DocumentService", "method": "GetDocument"}, {"service": "google.cloud.contentwarehouse.v1.DocumentService", "method": "FetchAcl"}, {"service": "google.cloud.contentwarehouse.v1.RuleSetService", "method": "GetRuleSet"}, {"service": "google.cloud.contentwarehouse.v1.RuleSetService", "method": "ListRuleSets"}, {"service": "google.cloud.contentwarehouse.v1.SynonymSetService", "method": "GetSynonymSet"}, {"service": "google.cloud.contentwarehouse.v1.SynonymSetService", "method": "ListSynonymSets"}, {"service": "google.longrunning.Operations", "method": "GetOperation"}, {"service": "google.longrunning.Operations", "method": "ListOperations"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.contentwarehouse.v1.DocumentSchemaService", "method": "CreateDocumentSchema"}, {"service": "google.cloud.contentwarehouse.v1.DocumentSchemaService", "method": "UpdateDocumentSchema"}, {"service": "google.cloud.contentwarehouse.v1.DocumentSchemaService", "method": "DeleteDocumentSchema"}, {"service": "google.cloud.contentwarehouse.v1.DocumentService", "method": "DeleteDocument"}, {"service": "google.cloud.contentwarehouse.v1.DocumentService", "method": "DeleteRawDocument"}, {"service": "google.cloud.contentwarehouse.v1.DocumentService", "method": "SetAcl"}, {"service": "google.cloud.contentwarehouse.v1.RuleSetService", "method": "CreateRuleSet"}, {"service": "google.cloud.contentwarehouse.v1.RuleSetService", "method": "DeleteRuleSet"}, {"service": "google.cloud.contentwarehouse.v1.RuleSetService", "method": "UpdateRuleSet"}, {"service": "google.cloud.contentwarehouse.v1.SynonymSetService", "method": "CreateSynonymSet"}, {"service": "google.cloud.contentwarehouse.v1.SynonymSetService", "method": "DeleteSynonymSet"}, {"service": "google.cloud.contentwarehouse.v1.SynonymSetService", "method": "UpdateSynonymSet"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.contentwarehouse.v1.DocumentService", "method": "CreateDocument"}, {"service": "google.cloud.contentwarehouse.v1.DocumentService", "method": "UpdateDocument"}, {"service": "google.cloud.contentwarehouse.v1.DocumentService", "method": "SearchDocuments"}, {"service": "google.cloud.contentwarehouse.v1.DocumentLinkService", "method": "ListLinkedSources"}, {"service": "google.cloud.contentwarehouse.v1.DocumentLinkService", "method": "ListLinkedTargets"}, {"service": "google.cloud.contentwarehouse.v1.DocumentLinkService", "method": "CreateDocumentLink"}, {"service": "google.cloud.contentwarehouse.v1.DocumentLinkService", "method": "DeleteDocumentLink"}], "timeout": "180s"}, {"name": [{"service": "google.cloud.contentwarehouse.v1.PipelineService", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "timeout": "120s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}