// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "DocumentSchemaProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// A document schema used to define document structure.
message DocumentSchema {
  option (google.api.resource) = {
    type: "contentwarehouse.googleapis.com/DocumentSchema"
    pattern: "projects/{project}/locations/{location}/documentSchemas/{document_schema}"
  };

  // The resource name of the document schema.
  // Format:
  // projects/{project_number}/locations/{location}/documentSchemas/{document_schema_id}.
  //
  // The name is ignored when creating a document schema.
  string name = 1;

  // Required. Name of the schema given by the user. Must be unique per project.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Document details.
  repeated PropertyDefinition property_definitions = 3;

  // Document Type, true refers the document is a folder, otherwise it is
  // a typical document.
  bool document_is_folder = 4;

  // Output only. The time when the document schema is last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the document schema is created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Schema description.
  string description = 7;
}

// Defines the metadata for a schema property.
message PropertyDefinition {
  // Stores the retrieval importance.
  enum RetrievalImportance {
    // No importance specified. Default medium importance.
    RETRIEVAL_IMPORTANCE_UNSPECIFIED = 0;

    // Highest importance.
    HIGHEST = 1;

    // Higher importance.
    HIGHER = 2;

    // High importance.
    HIGH = 3;

    // Medium importance.
    MEDIUM = 4;

    // Low importance (negative).
    LOW = 5;

    // Lowest importance (negative).
    LOWEST = 6;
  }

  // The schema source information.
  message SchemaSource {
    // The schema name in the source.
    string name = 1;

    // The Doc AI processor type name.
    string processor_type = 2;
  }

  // Required. The name of the metadata property.
  // Must be unique within a document schema and is case insensitive.
  // Names must be non-blank, start with a letter, and can contain alphanumeric
  // characters and: /, :, -, _, and .
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // The display-name for the property, used for front-end.
  string display_name = 12;

  // Whether the property can have multiple values.
  bool is_repeatable = 2;

  // Whether the property can be filtered. If this is a sub-property, all the
  // parent properties must be marked filterable.
  bool is_filterable = 3;

  // Indicates that the property should be included in a global search.
  bool is_searchable = 4;

  // Whether the property is user supplied metadata.
  // This out-of-the box placeholder setting can be used to tag derived
  // properties. Its value and interpretation logic should be implemented by API
  // user.
  bool is_metadata = 5;

  // Whether the property is mandatory.
  // Default is 'false', i.e. populating property value can be skipped.
  // If 'true' then user must populate the value for this property.
  bool is_required = 14;

  // The retrieval importance of the property during search.
  RetrievalImportance retrieval_importance = 18;

  // Type of the property.
  oneof value_type_options {
    // Integer property.
    IntegerTypeOptions integer_type_options = 7;

    // Float property.
    FloatTypeOptions float_type_options = 8;

    // Text/string property.
    TextTypeOptions text_type_options = 9;

    // Nested structured data property.
    PropertyTypeOptions property_type_options = 10;

    // Enum/categorical property.
    EnumTypeOptions enum_type_options = 11;

    // Date time property.
    // It is not supported by CMEK compliant deployment.
    DateTimeTypeOptions date_time_type_options = 13;

    // Map property.
    MapTypeOptions map_type_options = 15;

    // Timestamp property.
    // It is not supported by CMEK compliant deployment.
    TimestampTypeOptions timestamp_type_options = 16;
  }

  // The mapping information between this property to another schema source.
  repeated SchemaSource schema_sources = 19;
}

// Configurations for an integer property.
message IntegerTypeOptions {}

// Configurations for a float property.
message FloatTypeOptions {}

// Configurations for a text property.
message TextTypeOptions {}

// Configurations for a date time property.
message DateTimeTypeOptions {}

// Configurations for a Map property.
message MapTypeOptions {}

// Configurations for a timestamp property.
message TimestampTypeOptions {}

// Configurations for a nested structured data property.
message PropertyTypeOptions {
  // Required. List of property definitions.
  repeated PropertyDefinition property_definitions = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Configurations for an enum/categorical property.
message EnumTypeOptions {
  // Required. List of possible enum values.
  repeated string possible_values = 1 [(google.api.field_behavior) = REQUIRED];

  // Make sure the Enum property value provided in the document is in the
  // possile value list during document creation. The validation check runs by
  // default.
  bool validation_check_disabled = 2;
}
