// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "SynonymSetProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// Represents a list of synonyms for a given context.
// For example a context "sales" could contain:
// Synonym 1: sale, invoice, bill, order
// Synonym 2: money, credit, finance, payment
// Synonym 3: shipping, freight, transport
// Each SynonymSets should be disjoint
message SynonymSet {
  option (google.api.resource) = {
    type: "contentwarehouse.googleapis.com/SynonymSet"
    pattern: "projects/{project}/locations/{location}/synonymSets/{context}"
  };

  // Represents a list of words given by the customer
  // All these words are synonyms of each other.
  message Synonym {
    // For example: sale, invoice, bill, order
    repeated string words = 1;
  }

  // The resource name of the SynonymSet
  // This is mandatory for google.api.resource.
  // Format:
  // projects/{project_number}/locations/{location}/synonymSets/{context}.
  string name = 1;

  // This is a freeform field. Example contexts can be "sales," "engineering,"
  // "real estate," "accounting," etc.
  // The context can be supplied during search requests.
  string context = 2;

  // List of Synonyms for the context.
  repeated Synonym synonyms = 3;
}
