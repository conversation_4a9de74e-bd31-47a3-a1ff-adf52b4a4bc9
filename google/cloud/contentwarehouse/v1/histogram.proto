// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "HistogramProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// The histogram request.
message HistogramQuery {
  // An expression specifies a histogram request against matching documents for
  // searches.
  //
  // See
  // [SearchDocumentsRequest.histogram_queries][google.cloud.contentwarehouse.v1.SearchDocumentsRequest.histogram_queries]
  // for details about syntax.
  string histogram_query = 1;

  // Controls if the histogram query requires the return of a precise count.
  // Enable this flag may adversely impact performance.
  //
  // Defaults to true.
  bool require_precise_result_size = 2;

  // Optional. Filter the result of histogram query by the property names. It
  // only works with histogram query count('FilterableProperties'). It is an
  // optional. It will perform histogram on all the property names for all the
  // document schemas. Setting this field will have a better performance.
  HistogramQueryPropertyNameFilter filters = 3
      [(google.api.field_behavior) = OPTIONAL];
}

message HistogramQueryPropertyNameFilter {
  // The result of the histogram query count('FilterableProperties') using
  // HISTOGRAM_YAXIS_DOCUMENT will be:
  // invoice_id: 2
  // address: 1
  // payment_method: 2
  // line_item_description: 1
  enum HistogramYAxis {
    // Count the documents per property name.
    HISTOGRAM_YAXIS_DOCUMENT = 0;

    // Count the properties per property name.
    HISTOGRAM_YAXIS_PROPERTY = 1;
  }

  // This filter specifies the exact document schema(s)
  // [Document.document_schema_name][google.cloud.contentwarehouse.v1.Document.document_schema_name]
  // to run histogram query against. It is optional. It will perform histogram
  // for property names for all the document schemas if it is not set.
  //
  // At most 10 document schema names are allowed.
  // Format:
  // projects/{project_number}/locations/{location}/documentSchemas/{document_schema_id}.
  repeated string document_schemas = 1 [(google.api.resource_reference) = {
    type: "contentwarehouse.googleapis.com/DocumentSchema"
  }];

  // It is optional. It will perform histogram for all the property names if it
  // is not set.
  // The properties need to be defined with the is_filterable flag set to
  // true and the name of the property should be in the format:
  // "schemaId.propertyName". The property needs to be defined in the schema.
  // Example: the schema id is abc. Then the name of property for property
  // MORTGAGE_TYPE will be "abc.MORTGAGE_TYPE".
  repeated string property_names = 2;

  // By default, the y_axis is HISTOGRAM_YAXIS_DOCUMENT if this field is not
  // set.
  HistogramYAxis y_axis = 3;
}

// Histogram result that matches
// [HistogramQuery][google.cloud.contentwarehouse.v1.HistogramQuery] specified
// in searches.
message HistogramQueryResult {
  // Requested histogram expression.
  string histogram_query = 1;

  // A map from the values of the facet associated with distinct values to the
  // number of matching entries with corresponding value.
  //
  // The key format is:
  //
  // * (for string histogram) string values stored in the field.
  map<string, int64> histogram = 2;
}
