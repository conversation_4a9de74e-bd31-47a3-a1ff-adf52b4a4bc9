type: google.api.Service
config_version: 3
name: contentwarehouse.googleapis.com
title: Document AI Warehouse API

apis:
- name: google.cloud.contentwarehouse.v1.DocumentLinkService
- name: google.cloud.contentwarehouse.v1.DocumentSchemaService
- name: google.cloud.contentwarehouse.v1.DocumentService
- name: google.cloud.contentwarehouse.v1.PipelineService
- name: google.cloud.contentwarehouse.v1.RuleSetService
- name: google.cloud.contentwarehouse.v1.SynonymSetService
- name: google.longrunning.Operations

types:
- name: google.cloud.contentwarehouse.v1.CreateDocumentMetadata
- name: google.cloud.contentwarehouse.v1.RunPipelineMetadata
- name: google.cloud.contentwarehouse.v1.UpdateDocumentMetadata

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'

authentication:
  rules:
  - selector: 'google.cloud.contentwarehouse.v1.DocumentLinkService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.contentwarehouse.v1.DocumentSchemaService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.contentwarehouse.v1.DocumentService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.contentwarehouse.v1.PipelineService.RunPipeline
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.contentwarehouse.v1.RuleSetService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.contentwarehouse.v1.SynonymSetService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
