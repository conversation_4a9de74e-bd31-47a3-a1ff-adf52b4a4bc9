// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.contentwarehouse.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/contentwarehouse/v1/synonymset.proto";

option csharp_namespace = "Google.Cloud.ContentWarehouse.V1";
option go_package = "cloud.google.com/go/contentwarehouse/apiv1/contentwarehousepb;contentwarehousepb";
option java_multiple_files = true;
option java_outer_classname = "SynonymSetServiceRequestProto";
option java_package = "com.google.cloud.contentwarehouse.v1";
option php_namespace = "Google\\Cloud\\ContentWarehouse\\V1";
option ruby_package = "Google::Cloud::ContentWarehouse::V1";

// Request message for SynonymSetService.CreateSynonymSet.
message CreateSynonymSetRequest {
  // Required. The parent name.
  // Format: projects/{project_number}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Location"
    }
  ];

  // Required. The synonymSet to be created for a context
  SynonymSet synonym_set = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for SynonymSetService.GetSynonymSet.
// Will return synonymSet for a certain context.
message GetSynonymSetRequest {
  // Required. The name of the synonymSet to retrieve
  // Format:
  // projects/{project_number}/locations/{location}/synonymSets/{context}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/SynonymSet"
    }
  ];
}

// Request message for SynonymSetService.ListSynonymSets.
// Will return all synonymSets belonging to the customer project.
message ListSynonymSetsRequest {
  // Required. The parent name.
  // Format: projects/{project_number}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/Location"
    }
  ];

  // The maximum number of synonymSets to return. The service may return
  // fewer than this value.
  // If unspecified, at most 50 rule sets will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListSynonymSets` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListSynonymSets`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for SynonymSetService.ListSynonymSets.
message ListSynonymSetsResponse {
  // The synonymSets from the specified parent.
  repeated SynonymSet synonym_sets = 1;

  // A page token, received from a previous `ListSynonymSets` call.
  // Provide this to retrieve the subsequent page.
  string next_page_token = 2;
}

// Request message for SynonymSetService.UpdateSynonymSet.
// Removes the SynonymSet for the specified context and replaces
// it with the SynonymSet in this request.
message UpdateSynonymSetRequest {
  // Required. The name of the synonymSet to update
  // Format:
  // projects/{project_number}/locations/{location}/synonymSets/{context}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/SynonymSet"
    }
  ];

  // Required. The synonymSet to be updated for the customer
  SynonymSet synonym_set = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for SynonymSetService.DeleteSynonymSet.
message DeleteSynonymSetRequest {
  // Required. The name of the synonymSet to delete
  // Format:
  // projects/{project_number}/locations/{location}/synonymSets/{context}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "contentwarehouse.googleapis.com/SynonymSet"
    }
  ];
}
