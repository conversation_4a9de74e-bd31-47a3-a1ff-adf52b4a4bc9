type: google.api.Service
config_version: 3
name: cloudcontrolspartner.googleapis.com
title: Cloud Controls Partner API

apis:
- name: google.cloud.cloudcontrolspartner.v1beta.CloudControlsPartnerCore
- name: google.cloud.cloudcontrolspartner.v1beta.CloudControlsPartnerMonitoring

types:
- name: google.cloud.cloudcontrolspartner.v1beta.OperationMetadata

documentation:
  summary: |-
    Provides insights about your customers and their Assured Workloads based on
    your Sovereign Controls by Partners offering.

authentication:
  rules:
  - selector: 'google.cloud.cloudcontrolspartner.v1beta.CloudControlsPartnerCore.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.cloudcontrolspartner.v1beta.CloudControlsPartnerMonitoring.GetViolation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.cloudcontrolspartner.v1beta.CloudControlsPartnerMonitoring.ListViolations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1504051
  documentation_uri: https://cloud.google.com/sovereign-controls-by-partners/docs/sovereign-partners/reference/rest
  api_short_name: cloudcontrolspartner
  github_label: 'api: cloudcontrolspartner'
  doc_tag_prefix: cloudcontrolspartner
  organization: CLOUD
  library_settings:
  - version: google.cloud.cloudcontrolspartner.v1beta
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common: {}
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
