// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudcontrolspartner.v1beta;

option csharp_namespace = "Google.Cloud.CloudControlsPartner.V1Beta";
option go_package = "cloud.google.com/go/cloudcontrolspartner/apiv1beta/cloudcontrolspartnerpb;cloudcontrolspartnerpb";
option java_multiple_files = true;
option java_outer_classname = "CompletionStateProto";
option java_package = "com.google.cloud.cloudcontrolspartner.v1beta";
option php_namespace = "Google\\Cloud\\CloudControlsPartner\\V1beta";
option ruby_package = "Google::Cloud::CloudControlsPartner::V1beta";

// Enum for possible completion states.
enum CompletionState {
  // Unspecified completion state.
  COMPLETION_STATE_UNSPECIFIED = 0;

  // Task started (has start date) but not yet completed.
  PENDING = 1;

  // Succeeded state.
  SUCCEEDED = 2;

  // Failed state.
  FAILED = 3;

  // Not applicable state.
  NOT_APPLICABLE = 4;
}
