// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudcontrolspartner.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/cloudcontrolspartner/v1beta/violations.proto";

option csharp_namespace = "Google.Cloud.CloudControlsPartner.V1Beta";
option go_package = "cloud.google.com/go/cloudcontrolspartner/apiv1beta/cloudcontrolspartnerpb;cloudcontrolspartnerpb";
option java_multiple_files = true;
option java_outer_classname = "MonitoringProto";
option java_package = "com.google.cloud.cloudcontrolspartner.v1beta";
option php_namespace = "Google\\Cloud\\CloudControlsPartner\\V1beta";
option ruby_package = "Google::Cloud::CloudControlsPartner::V1beta";

// Service describing handlers for resources
service CloudControlsPartnerMonitoring {
  option (google.api.default_host) = "cloudcontrolspartner.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Violations for a workload
  // Callers may also choose to read across multiple Customers or for a single
  // customer as per
  // [AIP-159](https://google.aip.dev/159) by using '-' (the hyphen or dash
  // character) as a wildcard character instead of {customer} & {workload}.
  // Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}`
  rpc ListViolations(ListViolationsRequest) returns (ListViolationsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=organizations/*/locations/*/customers/*/workloads/*}/violations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Violation.
  rpc GetViolation(GetViolationRequest) returns (Violation) {
    option (google.api.http) = {
      get: "/v1beta/{name=organizations/*/locations/*/customers/*/workloads/*/violations/*}"
    };
    option (google.api.method_signature) = "name";
  }
}
