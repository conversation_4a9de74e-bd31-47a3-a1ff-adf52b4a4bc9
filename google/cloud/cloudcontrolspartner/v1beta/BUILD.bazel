# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "cloudcontrolspartner_proto",
    srcs = [
        "access_approval_requests.proto",
        "completion_state.proto",
        "core.proto",
        "customer_workloads.proto",
        "customers.proto",
        "ekm_connections.proto",
        "monitoring.proto",
        "partner_permissions.proto",
        "partners.proto",
        "violations.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/type:interval_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "cloudcontrolspartner_proto_with_info",
    deps = [
        ":cloudcontrolspartner_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "cloudcontrolspartner_java_proto",
    deps = [":cloudcontrolspartner_proto"],
)

java_grpc_library(
    name = "cloudcontrolspartner_java_grpc",
    srcs = [":cloudcontrolspartner_proto"],
    deps = [":cloudcontrolspartner_java_proto"],
)

java_gapic_library(
    name = "cloudcontrolspartner_java_gapic",
    srcs = [":cloudcontrolspartner_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "cloudcontrolspartner_v1beta_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudcontrolspartner_v1beta.yaml",
    test_deps = [
        ":cloudcontrolspartner_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":cloudcontrolspartner_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "cloudcontrolspartner_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.cloudcontrolspartner.v1beta.CloudControlsPartnerCoreClientHttpJsonTest",
        "com.google.cloud.cloudcontrolspartner.v1beta.CloudControlsPartnerCoreClientTest",
        "com.google.cloud.cloudcontrolspartner.v1beta.CloudControlsPartnerMonitoringClientHttpJsonTest",
        "com.google.cloud.cloudcontrolspartner.v1beta.CloudControlsPartnerMonitoringClientTest",
    ],
    runtime_deps = [":cloudcontrolspartner_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-cloudcontrolspartner-v1beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":cloudcontrolspartner_java_gapic",
        ":cloudcontrolspartner_java_grpc",
        ":cloudcontrolspartner_java_proto",
        ":cloudcontrolspartner_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "cloudcontrolspartner_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/cloudcontrolspartner/apiv1beta/cloudcontrolspartnerpb",
    protos = [":cloudcontrolspartner_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:interval_go_proto",
    ],
)

go_gapic_library(
    name = "cloudcontrolspartner_go_gapic",
    srcs = [":cloudcontrolspartner_proto_with_info"],
    grpc_service_config = "cloudcontrolspartner_v1beta_grpc_service_config.json",
    importpath = "cloud.google.com/go/cloudcontrolspartner/apiv1beta;cloudcontrolspartner",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "cloudcontrolspartner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudcontrolspartner_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-cloudcontrolspartner-v1beta-go",
    deps = [
        ":cloudcontrolspartner_go_gapic",
        ":cloudcontrolspartner_go_gapic_srcjar-metadata.srcjar",
        ":cloudcontrolspartner_go_gapic_srcjar-snippets.srcjar",
        ":cloudcontrolspartner_go_gapic_srcjar-test.srcjar",
        ":cloudcontrolspartner_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "cloudcontrolspartner_py_gapic",
    srcs = [":cloudcontrolspartner_proto"],
    grpc_service_config = "cloudcontrolspartner_v1beta_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudcontrolspartner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "cloudcontrolspartner_py_gapic_test",
    srcs = [
        "cloudcontrolspartner_py_gapic_pytest.py",
        "cloudcontrolspartner_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":cloudcontrolspartner_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "cloudcontrolspartner-v1beta-py",
    deps = [
        ":cloudcontrolspartner_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "cloudcontrolspartner_php_proto",
    deps = [":cloudcontrolspartner_proto"],
)

php_gapic_library(
    name = "cloudcontrolspartner_php_gapic",
    srcs = [":cloudcontrolspartner_proto_with_info"],
    grpc_service_config = "cloudcontrolspartner_v1beta_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudcontrolspartner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudcontrolspartner_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-cloudcontrolspartner-v1beta-php",
    deps = [
        ":cloudcontrolspartner_php_gapic",
        ":cloudcontrolspartner_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "cloudcontrolspartner_nodejs_gapic",
    package_name = "@google-cloud/cloudcontrolspartner",
    src = ":cloudcontrolspartner_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudcontrolspartner_v1beta_grpc_service_config.json",
    package = "google.cloud.cloudcontrolspartner.v1beta",
    rest_numeric_enums = True,
    service_yaml = "cloudcontrolspartner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "cloudcontrolspartner-v1beta-nodejs",
    deps = [
        ":cloudcontrolspartner_nodejs_gapic",
        ":cloudcontrolspartner_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "cloudcontrolspartner_ruby_proto",
    deps = [":cloudcontrolspartner_proto"],
)

ruby_grpc_library(
    name = "cloudcontrolspartner_ruby_grpc",
    srcs = [":cloudcontrolspartner_proto"],
    deps = [":cloudcontrolspartner_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "cloudcontrolspartner_ruby_gapic",
    srcs = [":cloudcontrolspartner_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-cloud_controls_partner-v1beta"],
    grpc_service_config = "cloudcontrolspartner_v1beta_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudcontrolspartner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudcontrolspartner_ruby_grpc",
        ":cloudcontrolspartner_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-cloudcontrolspartner-v1beta-ruby",
    deps = [
        ":cloudcontrolspartner_ruby_gapic",
        ":cloudcontrolspartner_ruby_grpc",
        ":cloudcontrolspartner_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "cloudcontrolspartner_csharp_proto",
    extra_opts = [],
    deps = [":cloudcontrolspartner_proto"],
)

csharp_grpc_library(
    name = "cloudcontrolspartner_csharp_grpc",
    srcs = [":cloudcontrolspartner_proto"],
    deps = [":cloudcontrolspartner_csharp_proto"],
)

csharp_gapic_library(
    name = "cloudcontrolspartner_csharp_gapic",
    srcs = [":cloudcontrolspartner_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudcontrolspartner_v1beta_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudcontrolspartner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudcontrolspartner_csharp_grpc",
        ":cloudcontrolspartner_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-cloudcontrolspartner-v1beta-csharp",
    deps = [
        ":cloudcontrolspartner_csharp_gapic",
        ":cloudcontrolspartner_csharp_grpc",
        ":cloudcontrolspartner_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "cloudcontrolspartner_cc_proto",
    deps = [":cloudcontrolspartner_proto"],
)

cc_grpc_library(
    name = "cloudcontrolspartner_cc_grpc",
    srcs = [":cloudcontrolspartner_proto"],
    grpc_only = True,
    deps = [":cloudcontrolspartner_cc_proto"],
)
