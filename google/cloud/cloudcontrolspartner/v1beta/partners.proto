// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudcontrolspartner.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CloudControlsPartner.V1Beta";
option go_package = "cloud.google.com/go/cloudcontrolspartner/apiv1beta/cloudcontrolspartnerpb;cloudcontrolspartnerpb";
option java_multiple_files = true;
option java_outer_classname = "PartnersProto";
option java_package = "com.google.cloud.cloudcontrolspartner.v1beta";
option php_namespace = "Google\\Cloud\\CloudControlsPartner\\V1beta";
option ruby_package = "Google::Cloud::CloudControlsPartner::V1beta";

// Message describing Partner resource
message Partner {
  option (google.api.resource) = {
    type: "cloudcontrolspartner.googleapis.com/Partner"
    pattern: "organizations/{organization}/locations/{location}/partner"
    singular: "partner"
  };

  // Identifier. The resource name of the partner.
  // Format: `organizations/{organization}/locations/{location}/partner`
  // Example: "organizations/123456/locations/us-central1/partner"
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // List of SKUs the partner is offering
  repeated Sku skus = 3;

  // List of Google Cloud supported EKM partners supported by the partner
  repeated EkmMetadata ekm_solutions = 4;

  // List of Google Cloud regions that the partner sells services to customers.
  // Valid Google Cloud regions found here:
  // https://cloud.google.com/compute/docs/regions-zones
  repeated string operated_cloud_regions = 5;

  // Google Cloud project ID in the partner's Google Cloud organization for
  // receiving enhanced Logs for Partners.
  string partner_project_id = 7;

  // Output only. Time the resource was created
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last time the resource was updated
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for getting a Partner
message GetPartnerRequest {
  // Required. Format:
  // `organizations/{organization}/locations/{location}/partner`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudcontrolspartner.googleapis.com/Partner"
    }
  ];
}

// Represents the SKU a partner owns inside Google Cloud to sell to customers.
message Sku {
  // Argentum product SKU, that is associated with the partner offerings to
  // customers used by Syntro for billing purposes. SKUs can represent resold
  // Google products or support services.
  string id = 1;

  // Display name of the product identified by the SKU. A partner may want to
  // show partner branded names for their offerings such as local sovereign
  // cloud solutions.
  string display_name = 2;
}

// Holds information needed by Mudbray to use partner EKMs for workloads.
message EkmMetadata {
  // Represents Google Cloud supported external key management partners
  // [Google Cloud EKM partners
  // docs](https://cloud.google.com/kms/docs/ekm#supported_partners).
  enum EkmSolution {
    // Unspecified EKM solution
    EKM_SOLUTION_UNSPECIFIED = 0;

    // EKM Partner Fortanix
    FORTANIX = 1;

    // EKM Partner FutureX
    FUTUREX = 2;

    // EKM Partner Thales
    THALES = 3;

    // EKM Partner Virtu
    VIRTRU = 4;
  }

  // The Cloud EKM partner.
  EkmSolution ekm_solution = 1;

  // Endpoint for sending requests to the EKM for key provisioning during
  // Assured Workload creation.
  string ekm_endpoint_uri = 2;
}
