// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudcontrolspartner.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/cloudcontrolspartner/v1beta/completion_state.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CloudControlsPartner.V1Beta";
option go_package = "cloud.google.com/go/cloudcontrolspartner/apiv1beta/cloudcontrolspartnerpb;cloudcontrolspartnerpb";
option java_multiple_files = true;
option java_outer_classname = "CustomersProto";
option java_package = "com.google.cloud.cloudcontrolspartner.v1beta";
option php_namespace = "Google\\Cloud\\CloudControlsPartner\\V1beta";
option ruby_package = "Google::Cloud::CloudControlsPartner::V1beta";

// Contains metadata around a Cloud Controls Partner Customer
message Customer {
  option (google.api.resource) = {
    type: "cloudcontrolspartner.googleapis.com/Customer"
    pattern: "organizations/{organization}/locations/{location}/customers/{customer}"
    plural: "customers"
    singular: "customer"
  };

  // Identifier. Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Display name for the customer
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Container for customer onboarding steps
  CustomerOnboardingState customer_onboarding_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether a customer is fully onboarded
  bool is_onboarded = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request to list customers
message ListCustomersRequest {
  // Required. Parent resource
  // Format: `organizations/{organization}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudcontrolspartner.googleapis.com/Customer"
    }
  ];

  // The maximum number of Customers to return. The service may return fewer
  // than this value. If unspecified, at most 500 Customers will be returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListCustomers` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3;

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for list customer Customers requests
message ListCustomersResponse {
  // List of customers
  repeated Customer customers = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a customer
message GetCustomerRequest {
  // Required. Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudcontrolspartner.googleapis.com/Customer"
    }
  ];
}

// Container for customer onboarding steps
message CustomerOnboardingState {
  // List of customer onboarding steps
  repeated CustomerOnboardingStep onboarding_steps = 1;
}

// Container for customer onboarding information
message CustomerOnboardingStep {
  // Enum for possible onboarding steps
  enum Step {
    // Unspecified step
    STEP_UNSPECIFIED = 0;

    // KAJ Enrollment
    KAJ_ENROLLMENT = 1;

    // Customer Environment
    CUSTOMER_ENVIRONMENT = 2;
  }

  // The onboarding step
  Step step = 1;

  // The starting time of the onboarding step
  google.protobuf.Timestamp start_time = 2;

  // The completion time of the onboarding step
  google.protobuf.Timestamp completion_time = 3;

  // Output only. Current state of the step
  CompletionState completion_state = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
