{"methodConfig": [{"name": [{"service": "google.cloud.cloudcontrolspartner.v1.CloudControlsPartnerCore", "method": "GetCustomer"}, {"service": "google.cloud.cloudcontrolspartner.v1.CloudControlsPartnerCore", "method": "GetEkmConnections"}, {"service": "google.cloud.cloudcontrolspartner.v1.CloudControlsPartnerCore", "method": "GetPartnerPermissions"}, {"service": "google.cloud.cloudcontrolspartner.v1.CloudControlsPartnerCore", "method": "GetWorkload"}, {"service": "google.cloud.cloudcontrolspartner.v1.CloudControlsPartnerCore", "method": "ListAccessApprovalRequests"}, {"service": "google.cloud.cloudcontrolspartner.v1.CloudControlsPartnerCore", "method": "ListCustomers"}, {"service": "google.cloud.cloudcontrolspartner.v1.CloudControlsPartnerCore", "method": "ListWorkloads"}, {"service": "google.cloud.cloudcontrolspartner.v1.CloudControlsPartnerMonitoring", "method": "GetViolation"}, {"service": "google.cloud.cloudcontrolspartner.v1.CloudControlsPartnerMonitoring", "method": "ListViolations"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}