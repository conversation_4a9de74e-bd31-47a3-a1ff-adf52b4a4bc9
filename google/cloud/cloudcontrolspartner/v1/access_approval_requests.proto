// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudcontrolspartner.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CloudControlsPartner.V1";
option go_package = "cloud.google.com/go/cloudcontrolspartner/apiv1/cloudcontrolspartnerpb;cloudcontrolspartnerpb";
option java_multiple_files = true;
option java_outer_classname = "AccessApprovalRequestsProto";
option java_package = "com.google.cloud.cloudcontrolspartner.v1";
option php_namespace = "Google\\Cloud\\CloudControlsPartner\\V1";
option ruby_package = "Google::Cloud::CloudControlsPartner::V1";

// Details about the Access request.
message AccessApprovalRequest {
  option (google.api.resource) = {
    type: "cloudcontrolspartner.googleapis.com/AccessApprovalRequest"
    pattern: "organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/accessApprovalRequests/{access_approval_request}"
    plural: "accessApprovalRequests"
    singular: "accessApprovalRequest"
  };

  // Identifier. Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/accessApprovalRequests/{access_approval_request}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The time at which approval was requested.
  google.protobuf.Timestamp request_time = 2;

  // The justification for which approval is being requested.
  AccessReason requested_reason = 3;

  // The requested expiration for the approval. If the request is approved,
  // access will be granted from the time of approval until the expiration time.
  google.protobuf.Timestamp requested_expiration_time = 4;
}

// Request for getting the access requests associated with a workload.
message ListAccessApprovalRequestsRequest {
  // Required. Parent resource
  // Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudcontrolspartner.googleapis.com/AccessApprovalRequest"
    }
  ];

  // Optional. The maximum number of access requests to return. The service may
  // return fewer than this value. If unspecified, at most 500 access requests
  // will be returned.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `ListAccessApprovalRequests` call. Provide this to retrieve the subsequent
  // page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for list access requests.
message ListAccessApprovalRequestsResponse {
  // List of access approval requests
  repeated AccessApprovalRequest access_approval_requests = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Reason for the access.
message AccessReason {
  // Type of access justification.
  enum Type {
    // Default value for proto, shouldn't be used.
    TYPE_UNSPECIFIED = 0;

    // Customer made a request or raised an issue that required the principal to
    // access customer data. `detail` is of the form ("#####" is the issue ID):
    //
    // - "Feedback Report: #####"
    // - "Case Number: #####"
    // - "Case ID: #####"
    // - "E-PIN Reference: #####"
    // - "Google-#####"
    // - "T-#####"
    CUSTOMER_INITIATED_SUPPORT = 1;

    // The principal accessed customer data in order to diagnose or resolve a
    // suspected issue in services. Often this access is used to confirm that
    // customers are not affected by a suspected service issue or to remediate a
    // reversible system issue.
    GOOGLE_INITIATED_SERVICE = 2;

    // Google initiated service for security, fraud, abuse, or compliance
    // purposes.
    GOOGLE_INITIATED_REVIEW = 3;

    // The principal was compelled to access customer data in order to respond
    // to a legal third party data request or process, including legal processes
    // from customers themselves.
    THIRD_PARTY_DATA_REQUEST = 4;

    // The principal accessed customer data in order to diagnose or resolve a
    // suspected issue in services or a known outage.
    GOOGLE_RESPONSE_TO_PRODUCTION_ALERT = 5;

    // Similar to 'GOOGLE_INITIATED_SERVICE' or 'GOOGLE_INITIATED_REVIEW', but
    // with universe agnostic naming. The principal accessed customer data in
    // order to diagnose or resolve a suspected issue in services or a known
    // outage, or for security, fraud, abuse, or compliance review purposes.
    CLOUD_INITIATED_ACCESS = 6;
  }

  // Type of access justification.
  Type type = 1;

  // More detail about certain reason types. See comments for each type above.
  string detail = 2;
}
