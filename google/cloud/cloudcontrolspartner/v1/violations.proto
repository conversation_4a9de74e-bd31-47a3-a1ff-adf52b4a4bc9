// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudcontrolspartner.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/type/interval.proto";

option csharp_namespace = "Google.Cloud.CloudControlsPartner.V1";
option go_package = "cloud.google.com/go/cloudcontrolspartner/apiv1/cloudcontrolspartnerpb;cloudcontrolspartnerpb";
option java_multiple_files = true;
option java_outer_classname = "ViolationsProto";
option java_package = "com.google.cloud.cloudcontrolspartner.v1";
option php_namespace = "Google\\Cloud\\CloudControlsPartner\\V1";
option ruby_package = "Google::Cloud::CloudControlsPartner::V1";

// Details of resource Violation
message Violation {
  option (google.api.resource) = {
    type: "cloudcontrolspartner.googleapis.com/Violation"
    pattern: "organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/violations/{violation}"
    plural: "violations"
    singular: "violation"
  };

  // Represents remediation guidance to resolve compliance violation for
  // AssuredWorkload
  message Remediation {
    // Instructions to remediate violation
    message Instructions {
      // Remediation instructions to resolve violation via gcloud cli
      message Gcloud {
        // Gcloud command to resolve violation
        repeated string gcloud_commands = 1;

        // Steps to resolve violation via gcloud cli
        repeated string steps = 2;

        // Additional urls for more information about steps
        repeated string additional_links = 3;
      }

      // Remediation instructions to resolve violation via cloud console
      message Console {
        // Link to console page where violations can be resolved
        repeated string console_uris = 1;

        // Steps to resolve violation via cloud console
        repeated string steps = 2;

        // Additional urls for more information about steps
        repeated string additional_links = 3;
      }

      // Remediation instructions to resolve violation via gcloud cli
      Gcloud gcloud_instructions = 1;

      // Remediation instructions to resolve violation via cloud console
      Console console_instructions = 2;
    }

    // Classifying remediation into various types based on the kind of
    // violation. For example, violations caused due to changes in boolean org
    // policy requires different remediation instructions compared to violation
    // caused due to changes in allowed values of list org policy.
    enum RemediationType {
      // Unspecified remediation type
      REMEDIATION_TYPE_UNSPECIFIED = 0;

      // Remediation type for boolean org policy
      REMEDIATION_BOOLEAN_ORG_POLICY_VIOLATION = 1;

      // Remediation type for list org policy which have allowed values in the
      // monitoring rule
      REMEDIATION_LIST_ALLOWED_VALUES_ORG_POLICY_VIOLATION = 2;

      // Remediation type for list org policy which have denied values in the
      // monitoring rule
      REMEDIATION_LIST_DENIED_VALUES_ORG_POLICY_VIOLATION = 3;

      // Remediation type for gcp.restrictCmekCryptoKeyProjects
      REMEDIATION_RESTRICT_CMEK_CRYPTO_KEY_PROJECTS_ORG_POLICY_VIOLATION = 4;

      // Remediation type for resource violation.
      REMEDIATION_RESOURCE_VIOLATION = 5;
    }

    // Required. Remediation instructions to resolve violations
    Instructions instructions = 1 [(google.api.field_behavior) = REQUIRED];

    // Values that can resolve the violation
    // For example: for list org policy violations, this will either be the list
    // of allowed or denied values
    repeated string compliant_values = 2;

    // Output only. Remediation type based on the type of org policy values
    // violated
    RemediationType remediation_type = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Violation State Values
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // Violation is resolved.
    RESOLVED = 1;

    // Violation is Unresolved
    UNRESOLVED = 2;

    // Violation is Exception
    EXCEPTION = 3;
  }

  // Identifier. Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/violations/{violation}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Description for the Violation.
  // e.g. OrgPolicy gcp.resourceLocations has non compliant value.
  string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time of the event which triggered the Violation.
  google.protobuf.Timestamp begin_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last time when the Violation record was updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time of the event which fixed the Violation.
  // If the violation is ACTIVE this will be empty.
  google.protobuf.Timestamp resolve_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Category under which this violation is mapped.
  // e.g. Location, Service Usage, Access, Encryption, etc.
  string category = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the violation
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Immutable. Name of the OrgPolicy which was modified with
  // non-compliant change and resulted this violation. Format:
  //  `projects/{project_number}/policies/{constraint_name}`
  //  `folders/{folder_id}/policies/{constraint_name}`
  //  `organizations/{organization_id}/policies/{constraint_name}`
  string non_compliant_org_policy = 8 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The folder_id of the violation
  int64 folder_id = 9;

  // Output only. Compliance violation remediation
  Remediation remediation = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting list of Violations
message ListViolationsRequest {
  // Required. Parent resource
  // Format
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudcontrolspartner.googleapis.com/Violation"
    }
  ];

  // Optional. The maximum number of customers row to return. The service may
  // return fewer than this value. If unspecified, at most 10 customers will be
  // returned.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListViolations` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the interval for retrieving violations.
  // if unspecified, all violations will be returned.
  google.type.Interval interval = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for list customer violation requests
message ListViolationsResponse {
  // List of violation
  repeated Violation violations = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Workloads that could not be reached due to permission errors or any other
  // error. Ref: https://google.aip.dev/217
  repeated string unreachable = 3;
}

// Message for getting a Violation
message GetViolationRequest {
  // Required. Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/violations/{violation}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudcontrolspartner.googleapis.com/Violation"
    }
  ];
}
