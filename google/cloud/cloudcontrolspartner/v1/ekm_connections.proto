// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudcontrolspartner.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.CloudControlsPartner.V1";
option go_package = "cloud.google.com/go/cloudcontrolspartner/apiv1/cloudcontrolspartnerpb;cloudcontrolspartnerpb";
option java_multiple_files = true;
option java_outer_classname = "EkmConnectionsProto";
option java_package = "com.google.cloud.cloudcontrolspartner.v1";
option php_namespace = "Google\\Cloud\\CloudControlsPartner\\V1";
option ruby_package = "Google::Cloud::CloudControlsPartner::V1";

// The EKM connections associated with a workload
message EkmConnections {
  option (google.api.resource) = {
    type: "cloudcontrolspartner.googleapis.com/EkmConnections"
    pattern: "organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/ekmConnections"
  };

  // Identifier. Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/ekmConnections`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The EKM connections associated with the workload
  repeated EkmConnection ekm_connections = 2;
}

// Request for getting the EKM connections associated with a workload
message GetEkmConnectionsRequest {
  // Required. Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/ekmConnections`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudcontrolspartner.googleapis.com/EkmConnections"
    }
  ];
}

// Details about the EKM connection
message EkmConnection {
  // Information around the error that occurred if the connection state is
  // anything other than available or unspecified
  message ConnectionError {
    // The error domain for the error
    string error_domain = 1;

    // The error message for the error
    string error_message = 2;
  }

  // The EKM connection state.
  enum ConnectionState {
    // Unspecified EKM connection state
    CONNECTION_STATE_UNSPECIFIED = 0;

    // Available EKM connection state
    AVAILABLE = 1;

    // Not available EKM connection state
    NOT_AVAILABLE = 2;

    // Error EKM connection state
    ERROR = 3;

    // Permission denied EKM connection state
    PERMISSION_DENIED = 4;
  }

  // Resource name of the EKM connection in the format:
  // projects/{project}/locations/{location}/ekmConnections/{ekm_connection}
  string connection_name = 1;

  // Output only. The connection state
  ConnectionState connection_state = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The connection error that occurred if any
  ConnectionError connection_error = 3;
}
