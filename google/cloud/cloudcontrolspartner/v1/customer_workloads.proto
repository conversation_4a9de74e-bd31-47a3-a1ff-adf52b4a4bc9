// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudcontrolspartner.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/cloudcontrolspartner/v1/completion_state.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CloudControlsPartner.V1";
option go_package = "cloud.google.com/go/cloudcontrolspartner/apiv1/cloudcontrolspartnerpb;cloudcontrolspartnerpb";
option java_multiple_files = true;
option java_outer_classname = "CustomerWorkloadsProto";
option java_package = "com.google.cloud.cloudcontrolspartner.v1";
option php_namespace = "Google\\Cloud\\CloudControlsPartner\\V1";
option ruby_package = "Google::Cloud::CloudControlsPartner::V1";

// Contains metadata around the [Workload
// resource](https://cloud.google.com/assured-workloads/docs/reference/rest/Shared.Types/Workload)
// in the Assured Workloads API.
message Workload {
  option (google.api.resource) = {
    type: "cloudcontrolspartner.googleapis.com/Workload"
    pattern: "organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}"
    plural: "workloads"
    singular: "workload"
  };

  // Supported Assured Workloads Partners.
  enum Partner {
    // Unknown Partner.
    PARTNER_UNSPECIFIED = 0;

    // Enum representing S3NS (Thales) partner.
    PARTNER_LOCAL_CONTROLS_BY_S3NS = 1;

    // Enum representing T_SYSTEM (TSI) partner.
    PARTNER_SOVEREIGN_CONTROLS_BY_T_SYSTEMS = 2;

    // Enum representing SIA_MINSAIT (Indra) partner.
    PARTNER_SOVEREIGN_CONTROLS_BY_SIA_MINSAIT = 3;

    // Enum representing PSN (TIM) partner.
    PARTNER_SOVEREIGN_CONTROLS_BY_PSN = 4;

    // Enum representing CNTXT (Kingdom of Saudi Arabia) partner.
    PARTNER_SOVEREIGN_CONTROLS_BY_CNTXT = 6;

    // Enum representing CNXT (Kingdom of Saudi Arabia) partner offering without
    // EKM provisioning.
    PARTNER_SOVEREIGN_CONTROLS_BY_CNTXT_NO_EKM = 7;
  }

  // Identifier. Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Folder id this workload is associated with
  int64 folder_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time the resource was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of container folder of the assured workload
  string folder = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Container for workload onboarding steps.
  WorkloadOnboardingState workload_onboarding_state = 5;

  // Indicates whether a workload is fully onboarded.
  bool is_onboarded = 6;

  // The project id of the key management project for the workload
  string key_management_project_id = 7;

  // The Google Cloud location of the workload
  string location = 8;

  // Partner associated with this workload.
  Partner partner = 9;
}

// Request to list customer workloads.
message ListWorkloadsRequest {
  // Required. Parent resource
  // Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudcontrolspartner.googleapis.com/Workload"
    }
  ];

  // The maximum number of workloads to return. The service may return fewer
  // than this value. If unspecified, at most 500 workloads will be returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListWorkloads` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3;

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for list customer workloads requests.
message ListWorkloadsResponse {
  // List of customer workloads
  repeated Workload workloads = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a customer workload.
message GetWorkloadRequest {
  // Required. Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudcontrolspartner.googleapis.com/Workload"
    }
  ];
}

// Container for workload onboarding steps.
message WorkloadOnboardingState {
  // List of workload onboarding steps.
  repeated WorkloadOnboardingStep onboarding_steps = 1;
}

// Container for workload onboarding information.
message WorkloadOnboardingStep {
  // Enum for possible onboarding steps.
  enum Step {
    // Unspecified step.
    STEP_UNSPECIFIED = 0;

    // EKM Provisioned step.
    EKM_PROVISIONED = 1;

    // Signed Access Approval step.
    SIGNED_ACCESS_APPROVAL_CONFIGURED = 2;
  }

  // The onboarding step.
  Step step = 1;

  // The starting time of the onboarding step.
  google.protobuf.Timestamp start_time = 2;

  // The completion time of the onboarding step.
  google.protobuf.Timestamp completion_time = 3;

  // Output only. The completion state of the onboarding step.
  CompletionState completion_state = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
