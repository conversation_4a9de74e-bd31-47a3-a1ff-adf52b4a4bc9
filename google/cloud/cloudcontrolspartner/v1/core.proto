// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudcontrolspartner.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/cloudcontrolspartner/v1/access_approval_requests.proto";
import "google/cloud/cloudcontrolspartner/v1/customer_workloads.proto";
import "google/cloud/cloudcontrolspartner/v1/customers.proto";
import "google/cloud/cloudcontrolspartner/v1/ekm_connections.proto";
import "google/cloud/cloudcontrolspartner/v1/partner_permissions.proto";
import "google/cloud/cloudcontrolspartner/v1/partners.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CloudControlsPartner.V1";
option go_package = "cloud.google.com/go/cloudcontrolspartner/apiv1/cloudcontrolspartnerpb;cloudcontrolspartnerpb";
option java_multiple_files = true;
option java_outer_classname = "CoreProto";
option java_package = "com.google.cloud.cloudcontrolspartner.v1";
option php_namespace = "Google\\Cloud\\CloudControlsPartner\\V1";
option ruby_package = "Google::Cloud::CloudControlsPartner::V1";
option (google.api.resource_definition) = {
  type: "cloudcontrolspartner.googleapis.com/OrganizationLocation"
  pattern: "organizations/{organization}/locations/{location}"
};

// Service describing handlers for resources
service CloudControlsPartnerCore {
  option (google.api.default_host) = "cloudcontrolspartner.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets details of a single workload
  rpc GetWorkload(GetWorkloadRequest) returns (Workload) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/customers/*/workloads/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists customer workloads for a given customer org id
  rpc ListWorkloads(ListWorkloadsRequest) returns (ListWorkloadsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/locations/*/customers/*}/workloads"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single customer
  rpc GetCustomer(GetCustomerRequest) returns (Customer) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/customers/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists customers of a partner identified by its Google Cloud organization ID
  rpc ListCustomers(ListCustomersRequest) returns (ListCustomersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/locations/*}/customers"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the EKM connections associated with a workload
  rpc GetEkmConnections(GetEkmConnectionsRequest) returns (EkmConnections) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/customers/*/workloads/*/ekmConnections}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the partner permissions granted for a workload
  rpc GetPartnerPermissions(GetPartnerPermissionsRequest)
      returns (PartnerPermissions) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/customers/*/workloads/*/partnerPermissions}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deprecated: Only returns access approval requests directly associated with
  // an assured workload folder.
  rpc ListAccessApprovalRequests(ListAccessApprovalRequestsRequest)
      returns (ListAccessApprovalRequestsResponse) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/locations/*/customers/*/workloads/*}/accessApprovalRequests"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get details of a Partner.
  rpc GetPartner(GetPartnerRequest) returns (Partner) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/partner}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
