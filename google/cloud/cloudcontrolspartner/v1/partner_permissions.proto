// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.cloudcontrolspartner.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.CloudControlsPartner.V1";
option go_package = "cloud.google.com/go/cloudcontrolspartner/apiv1/cloudcontrolspartnerpb;cloudcontrolspartnerpb";
option java_multiple_files = true;
option java_outer_classname = "PartnerPermissionsProto";
option java_package = "com.google.cloud.cloudcontrolspartner.v1";
option php_namespace = "Google\\Cloud\\CloudControlsPartner\\V1";
option ruby_package = "Google::Cloud::CloudControlsPartner::V1";

// The permissions granted to the partner for a workload
message PartnerPermissions {
  option (google.api.resource) = {
    type: "cloudcontrolspartner.googleapis.com/PartnerPermissions"
    pattern: "organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/partnerPermissions"
  };

  enum Permission {
    // Unspecified partner permission
    PERMISSION_UNSPECIFIED = 0;

    // Permission for Access Transparency and emergency logs
    ACCESS_TRANSPARENCY_AND_EMERGENCY_ACCESS_LOGS = 1;

    // Permission for Assured Workloads monitoring violations
    ASSURED_WORKLOADS_MONITORING = 2;

    // Permission for Access Approval requests
    ACCESS_APPROVAL_REQUESTS = 3;

    // Permission for External Key Manager connection status
    ASSURED_WORKLOADS_EKM_CONNECTION_STATUS = 4;

    // Permission for support case details for Access Transparency log entries
    ACCESS_TRANSPARENCY_LOGS_SUPPORT_CASE_VIEWER = 5;
  }

  // Identifier. Format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/partnerPermissions`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The partner permissions granted for the workload
  repeated Permission partner_permissions = 2;
}

// Request for getting the partner permissions granted for a workload
message GetPartnerPermissionsRequest {
  // Required. Name of the resource to get in the format:
  // `organizations/{organization}/locations/{location}/customers/{customer}/workloads/{workload}/partnerPermissions`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudcontrolspartner.googleapis.com/PartnerPermissions"
    }
  ];
}
