# This build file includes a target for the Ruby wrapper library for
# google-cloud-bare_metal_solution.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for baremetalsolution.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "baremetalsolution_ruby_wrapper",
    srcs = ["//google/cloud/baremetalsolution/v2:baremetalsolution_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=baremetalsolution.googleapis.com",
        "ruby-cloud-api-shortname=baremetalsolution",
        "ruby-cloud-gem-name=google-cloud-bare_metal_solution",
        "ruby-cloud-product-url=https://cloud.google.com/bare-metal/",
        "ruby-cloud-wrapper-of=v2:0.7",
    ],
    ruby_cloud_description = "Bare Metal Solution is a managed solution that provides purpose-built HPE or Atos bare-metal servers in regional extensions that are connected to Google Cloud by a managed, high-performance connection with a low-latency network fabric.",
    ruby_cloud_title = "Bare Metal Solution",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-baremetalsolution-ruby",
    deps = [
        ":baremetalsolution_ruby_wrapper",
    ],
)
