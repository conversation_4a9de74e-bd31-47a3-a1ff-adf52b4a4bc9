// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.baremetalsolution.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BareMetalSolution.V2";
option go_package = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb;baremetalsolutionpb";
option java_multiple_files = true;
option java_outer_classname = "LunProto";
option java_package = "com.google.cloud.baremetalsolution.v2";
option php_namespace = "Google\\Cloud\\BareMetalSolution\\V2";
option ruby_package = "Google::Cloud::BareMetalSolution::V2";

// A storage volume logical unit number (LUN).
message Lun {
  option (google.api.resource) = {
    type: "baremetalsolution.googleapis.com/Lun"
    pattern: "projects/{project}/locations/{location}/volumes/{volume}/luns/{lun}"
  };

  // The possible states for the LUN.
  enum State {
    // The LUN is in an unknown state.
    STATE_UNSPECIFIED = 0;

    // The LUN is being created.
    CREATING = 1;

    // The LUN is being updated.
    UPDATING = 2;

    // The LUN is ready for use.
    READY = 3;

    // The LUN has been requested to be deleted.
    DELETING = 4;

    // The LUN is in cool off state. It will be deleted after `expire_time`.
    COOL_OFF = 5;
  }

  // Display the operating systems present for the LUN multiprotocol type.
  enum MultiprotocolType {
    // Server has no OS specified.
    MULTIPROTOCOL_TYPE_UNSPECIFIED = 0;

    // Server with Linux OS.
    LINUX = 1;
  }

  // The storage types for a LUN.
  enum StorageType {
    // The storage type for this LUN is unknown.
    STORAGE_TYPE_UNSPECIFIED = 0;

    // This storage type for this LUN is SSD.
    SSD = 1;

    // This storage type for this LUN is HDD.
    HDD = 2;
  }

  // Output only. The name of the LUN.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // An identifier for the LUN, generated by the backend.
  string id = 10;

  // The state of this storage volume.
  State state = 2;

  // The size of this LUN, in gigabytes.
  int64 size_gb = 3;

  // The LUN multiprotocol type ensures the characteristics of the LUN are
  // optimized for each operating system.
  MultiprotocolType multiprotocol_type = 4;

  // Display the storage volume for this LUN.
  string storage_volume = 5 [(google.api.resource_reference) = {
    type: "baremetalsolution.googleapis.com/Volume"
  }];

  // Display if this LUN can be shared between multiple physical servers.
  bool shareable = 6;

  // Display if this LUN is a boot LUN.
  bool boot_lun = 7;

  // The storage type for this LUN.
  StorageType storage_type = 8;

  // The WWID for this LUN.
  string wwid = 9;

  // Output only. Time after which LUN will be fully deleted.
  // It is filled only for LUNs in COOL_OFF state.
  google.protobuf.Timestamp expire_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Instances this Lun is attached to.
  repeated string instances = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Instance"
    }
  ];
}

// Message for requesting storage lun information.
message GetLunRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Lun"
    }
  ];
}

// Message for requesting a list of storage volume luns.
message ListLunsRequest {
  // Required. Parent value for ListLunsRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Volume"
    }
  ];

  // Requested page size. The server might return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results from the server.
  string page_token = 3;
}

// Response message containing the list of storage volume luns.
message ListLunsResponse {
  // The list of luns.
  repeated Lun luns = 1;

  // A token identifying a page of results from the server.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for skip lun cooloff and delete it.
message EvictLunRequest {
  // Required. The name of the lun.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Lun"
    }
  ];
}
