type: google.api.Service
config_version: 3
name: baremetalsolution.googleapis.com
title: Bare Metal Solution API

apis:
- name: google.cloud.baremetalsolution.v2.BareMetalSolution
- name: google.cloud.location.Locations
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.baremetalsolution.v2.DisableInteractiveSerialConsoleResponse
- name: google.cloud.baremetalsolution.v2.EnableInteractiveSerialConsoleResponse
- name: google.cloud.baremetalsolution.v2.OperationMetadata
- name: google.cloud.baremetalsolution.v2.ResetInstanceResponse
- name: google.cloud.baremetalsolution.v2.ServerNetworkTemplate
- name: google.cloud.baremetalsolution.v2.StartInstanceResponse
- name: google.cloud.baremetalsolution.v2.StopInstanceResponse

documentation:
  summary: |-
    Provides ways to manage Bare Metal Solution hardware installed in a
    regional extension located near a Google Cloud data center.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: 'google.cloud.baremetalsolution.v2.BareMetalSolution.*'
    deadline: 60.0
  - selector: google.cloud.baremetalsolution.v2.BareMetalSolution.UpdateInstance
    deadline: 120.0
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v2/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v2/{name=projects/*}/locations'

authentication:
  rules:
  - selector: 'google.cloud.baremetalsolution.v2.BareMetalSolution.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
