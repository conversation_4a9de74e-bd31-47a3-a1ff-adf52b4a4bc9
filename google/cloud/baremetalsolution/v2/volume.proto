// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.baremetalsolution.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/baremetalsolution/v2/common.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BareMetalSolution.V2";
option go_package = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb;baremetalsolutionpb";
option java_multiple_files = true;
option java_outer_classname = "VolumeProto";
option java_package = "com.google.cloud.baremetalsolution.v2";
option php_namespace = "Google\\Cloud\\BareMetalSolution\\V2";
option ruby_package = "Google::Cloud::BareMetalSolution::V2";

// A storage volume.
message Volume {
  option (google.api.resource) = {
    type: "baremetalsolution.googleapis.com/Volume"
    pattern: "projects/{project}/locations/{location}/volumes/{volume}"
  };

  // The storage type for a volume.
  enum StorageType {
    // The storage type for this volume is unknown.
    STORAGE_TYPE_UNSPECIFIED = 0;

    // The storage type for this volume is SSD.
    SSD = 1;

    // This storage type for this volume is HDD.
    HDD = 2;
  }

  // The possible states for a storage volume.
  enum State {
    // The storage volume is in an unknown state.
    STATE_UNSPECIFIED = 0;

    // The storage volume is being created.
    CREATING = 1;

    // The storage volume is ready for use.
    READY = 2;

    // The storage volume has been requested to be deleted.
    DELETING = 3;

    // The storage volume is being updated.
    UPDATING = 4;

    // The storage volume is in cool off state. It will be deleted after
    // `expire_time`.
    COOL_OFF = 5;
  }

  // Details about snapshot space reservation and usage on the storage volume.
  message SnapshotReservationDetail {
    // The space on this storage volume reserved for snapshots, shown in GiB.
    int64 reserved_space_gib = 1;

    // The percent of snapshot space on this storage volume actually being used
    // by the snapshot copies. This value might be higher than 100% if the
    // snapshot copies have overflowed into the data portion of the storage
    // volume.
    int32 reserved_space_used_percent = 2;

    // The amount, in GiB, of available space in this storage volume's reserved
    // snapshot space.
    int64 reserved_space_remaining_gib = 3;

    // Percent of the total Volume size reserved for snapshot copies.
    // Enabling snapshots requires reserving 20% or more of
    // the storage volume space for snapshots. Maximum reserved space for
    // snapshots is 40%.
    // Setting this field will effectively set snapshot_enabled to true.
    int32 reserved_space_percent = 4;
  }

  // The kinds of auto delete behavior to use when snapshot reserved space is
  // full.
  enum SnapshotAutoDeleteBehavior {
    // The unspecified behavior.
    SNAPSHOT_AUTO_DELETE_BEHAVIOR_UNSPECIFIED = 0;

    // Don't delete any snapshots. This disables new snapshot creation, as
    // long as the snapshot reserved space is full.
    DISABLED = 1;

    // Delete the oldest snapshots first.
    OLDEST_FIRST = 2;

    // Delete the newest snapshots first.
    NEWEST_FIRST = 3;
  }

  // Storage protocol.
  enum Protocol {
    // Value is not specified.
    PROTOCOL_UNSPECIFIED = 0;

    // Fibre Channel protocol.
    FIBRE_CHANNEL = 1;

    // NFS protocol means Volume is a NFS Share volume.
    // Such volumes cannot be manipulated via Volumes API.
    NFS = 2;
  }

  // The possible values for a workload profile.
  enum WorkloadProfile {
    // The workload profile is in an unknown state.
    WORKLOAD_PROFILE_UNSPECIFIED = 0;

    // The workload profile is generic.
    GENERIC = 1;

    // The workload profile is hana.
    HANA = 2;
  }

  // Output only. The resource name of this `Volume`.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // Format:
  // `projects/{project}/locations/{location}/volumes/{volume}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // An identifier for the `Volume`, generated by the backend.
  string id = 11;

  // The storage type for this volume.
  StorageType storage_type = 2;

  // The state of this storage volume.
  State state = 3;

  // The requested size of this storage volume, in GiB.
  int64 requested_size_gib = 4;

  // Originally requested size, in GiB.
  int64 originally_requested_size_gib = 16;

  // The current size of this storage volume, in GiB, including space reserved
  // for snapshots. This size might be different than the requested size if the
  // storage volume has been configured with auto grow or auto shrink.
  int64 current_size_gib = 5;

  // Additional emergency size that was requested for this Volume, in GiB.
  // current_size_gib includes this value.
  int64 emergency_size_gib = 14;

  // Maximum size volume can be expanded to in case of evergency, in GiB.
  int64 max_size_gib = 17;

  // The size, in GiB, that this storage volume has expanded as a result of an
  // auto grow policy. In the absence of auto-grow, the value is 0.
  int64 auto_grown_size_gib = 6;

  // The space remaining in the storage volume for new LUNs, in GiB, excluding
  // space reserved for snapshots.
  int64 remaining_space_gib = 7;

  // Details about snapshot space reservation and usage on the storage volume.
  SnapshotReservationDetail snapshot_reservation_detail = 8;

  // The behavior to use when snapshot reserved space is full.
  SnapshotAutoDeleteBehavior snapshot_auto_delete_behavior = 9;

  // Labels as key value pairs.
  map<string, string> labels = 12;

  // Whether snapshots are enabled.
  bool snapshot_enabled = 13;

  // Immutable. Pod name.
  string pod = 15 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Storage protocol for the Volume.
  Protocol protocol = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether this volume is a boot volume. A boot volume is one
  // which contains a boot LUN.
  bool boot_volume = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Performance tier of the Volume.
  // Default is SHARED.
  VolumePerformanceTier performance_tier = 20
      [(google.api.field_behavior) = IMMUTABLE];

  // Input only. User-specified notes for new Volume.
  // Used to provision Volumes that require manual intervention.
  string notes = 21 [(google.api.field_behavior) = INPUT_ONLY];

  // The workload profile for the volume.
  WorkloadProfile workload_profile = 22;

  // Output only. Time after which volume will be fully deleted.
  // It is filled only for volumes in COOLOFF state.
  google.protobuf.Timestamp expire_time = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Instances this Volume is attached to.
  // This field is set only in Get requests.
  repeated string instances = 25 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Instance"
    }
  ];

  // Output only. Is the Volume attached at at least one instance.
  // This field is a lightweight counterpart of `instances` field.
  // It is filled in List responses as well.
  bool attached = 26 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting storage volume information.
message GetVolumeRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Volume"
    }
  ];
}

// Message for requesting a list of storage volumes.
message ListVolumesRequest {
  // Required. Parent value for ListVolumesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Requested page size. The server might return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results from the server.
  string page_token = 3;

  // List filter.
  string filter = 4;
}

// Response message containing the list of storage volumes.
message ListVolumesResponse {
  // The list of storage volumes.
  repeated Volume volumes = 1;

  // A token identifying a page of results from the server.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for updating a volume.
message UpdateVolumeRequest {
  // Required. The volume to update.
  //
  // The `name` field is used to identify the volume to update.
  // Format: projects/{project}/locations/{location}/volumes/{volume}
  Volume volume = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  // The only currently supported fields are:
  //   'labels'
  google.protobuf.FieldMask update_mask = 2;
}

// Message requesting rename of a server.
message RenameVolumeRequest {
  // Required. The `name` field is used to identify the volume.
  // Format: projects/{project}/locations/{location}/volumes/{volume}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Volume"
    }
  ];

  // Required. The new `id` of the volume.
  string new_volume_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for skip volume cooloff and delete it.
message EvictVolumeRequest {
  // Required. The name of the Volume.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Volume"
    }
  ];
}

// Request for emergency resize Volume.
message ResizeVolumeRequest {
  // Required. Volume to resize.
  string volume = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Volume"
    }
  ];

  // New Volume size, in GiB.
  int64 size_gib = 2;
}
