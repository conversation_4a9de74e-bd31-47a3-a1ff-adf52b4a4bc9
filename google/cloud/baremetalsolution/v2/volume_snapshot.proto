// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.baremetalsolution.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BareMetalSolution.V2";
option go_package = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb;baremetalsolutionpb";
option java_multiple_files = true;
option java_outer_classname = "VolumeSnapshotProto";
option java_package = "com.google.cloud.baremetalsolution.v2";
option php_namespace = "Google\\Cloud\\BareMetalSolution\\V2";
option ruby_package = "Google::Cloud::BareMetalSolution::V2";

// A snapshot of a volume. Only boot volumes can have snapshots.
message VolumeSnapshot {
  option (google.api.resource) = {
    type: "baremetalsolution.googleapis.com/VolumeSnapshot"
    pattern: "projects/{project}/locations/{location}/volumes/{volume}/snapshots/{snapshot}"
  };

  // Represents the type of a snapshot.
  enum SnapshotType {
    // Type is not specified.
    SNAPSHOT_TYPE_UNSPECIFIED = 0;

    // Snapshot was taken manually by user.
    AD_HOC = 1;

    // Snapshot was taken automatically as a part of a snapshot schedule.
    SCHEDULED = 2;
  }

  // The name of the snapshot.
  string name = 1;

  // Output only. An identifier for the snapshot, generated by the backend.
  string id = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The description of the snapshot.
  string description = 2;

  // Output only. The creation time of the snapshot.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the volume which this snapshot belongs to.
  string storage_volume = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Volume"
    }
  ];

  // Output only. The type of the snapshot which indicates whether it was
  // scheduled or manual/ad-hoc.
  SnapshotType type = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting volume snapshot information.
message GetVolumeSnapshotRequest {
  // Required. The name of the snapshot.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/VolumeSnapshot"
    }
  ];
}

// Message for requesting a list of volume snapshots.
message ListVolumeSnapshotsRequest {
  // Required. Parent value for ListVolumesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Volume"
    }
  ];

  // Requested page size. The server might return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results from the server.
  string page_token = 3;
}

// Response message containing the list of volume snapshots.
message ListVolumeSnapshotsResponse {
  // The list of snapshots.
  repeated VolumeSnapshot volume_snapshots = 1;

  // A token identifying a page of results from the server.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for deleting named Volume snapshot.
message DeleteVolumeSnapshotRequest {
  // Required. The name of the snapshot to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/VolumeSnapshot"
    }
  ];
}

// Message for creating a volume snapshot.
message CreateVolumeSnapshotRequest {
  // Required. The volume to snapshot.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Volume"
    }
  ];

  // Required. The snapshot to create.
  VolumeSnapshot volume_snapshot = 2 [(google.api.field_behavior) = REQUIRED];
}

// Message for restoring a volume snapshot.
message RestoreVolumeSnapshotRequest {
  // Required. Name of the snapshot which will be used to restore its parent
  // volume.
  string volume_snapshot = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/VolumeSnapshot"
    }
  ];
}
