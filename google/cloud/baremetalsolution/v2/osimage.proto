// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.baremetalsolution.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.BareMetalSolution.V2";
option go_package = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb;baremetalsolutionpb";
option java_multiple_files = true;
option java_outer_classname = "OsImageProto";
option java_package = "com.google.cloud.baremetalsolution.v2";
option php_namespace = "Google\\Cloud\\BareMetalSolution\\V2";
option ruby_package = "Google::Cloud::BareMetalSolution::V2";

// Operation System image.
message OSImage {
  option (google.api.resource) = {
    type: "baremetalsolution.googleapis.com/OsImage"
    pattern: "projects/{project}/locations/{location}/osImages/{os_image}"
    plural: "osImages"
    singular: "osImage"
  };

  // Output only. OS Image's unique name.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // OS Image code.
  string code = 2;

  // OS Image description.
  string description = 3;

  // Instance types this image is applicable to.
  // [Available
  // types](https://cloud.google.com/bare-metal/docs/bms-planning#server_configurations)
  repeated string applicable_instance_types = 4;

  // Network templates that can be used with this OS Image.
  repeated string supported_network_templates = 5;
}

// Request for getting all available OS images.
message ListOSImagesRequest {
  // Required. Parent value for ListProvisioningQuotasRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Requested page size. The server might return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  // Notice that page_size field is not supported and won't be respected in
  // the API request for now, will be updated when pagination is supported.
  int32 page_size = 2;

  // A token identifying a page of results from the server.
  string page_token = 3;
}

// Request for getting all available OS images.
message ListOSImagesResponse {
  // The OS images available.
  repeated OSImage os_images = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}
