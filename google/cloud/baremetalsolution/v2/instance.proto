// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.baremetalsolution.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/baremetalsolution/v2/common.proto";
import "google/cloud/baremetalsolution/v2/lun.proto";
import "google/cloud/baremetalsolution/v2/network.proto";
import "google/cloud/baremetalsolution/v2/volume.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BareMetalSolution.V2";
option go_package = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb;baremetalsolutionpb";
option java_multiple_files = true;
option java_outer_classname = "InstanceProto";
option java_package = "com.google.cloud.baremetalsolution.v2";
option php_namespace = "Google\\Cloud\\BareMetalSolution\\V2";
option ruby_package = "Google::Cloud::BareMetalSolution::V2";

// A server.
message Instance {
  option (google.api.resource) = {
    type: "baremetalsolution.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/instances/{instance}"
  };

  // The possible states for this server.
  enum State {
    // The server is in an unknown state.
    STATE_UNSPECIFIED = 0;

    // The server is being provisioned.
    PROVISIONING = 1;

    // The server is running.
    RUNNING = 2;

    // The server has been deleted.
    DELETED = 3;

    // The server is being updated.
    UPDATING = 4;

    // The server is starting.
    STARTING = 5;

    // The server is stopping.
    STOPPING = 6;

    // The server is shutdown.
    SHUTDOWN = 7;
  }

  // Immutable. The resource name of this `Instance`.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // Format:
  // `projects/{project}/locations/{location}/instances/{instance}`
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. An identifier for the `Instance`, generated by the backend.
  string id = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create a time stamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update a time stamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The server type.
  // [Available server
  // types](https://cloud.google.com/bare-metal/docs/bms-planning#server_configurations)
  string machine_type = 4 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The state of the server.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // True if you enable hyperthreading for the server, otherwise false.
  // The default value is false.
  bool hyperthreading_enabled = 6;

  // Labels as key value pairs.
  map<string, string> labels = 7;

  // Immutable. List of LUNs associated with this server.
  repeated Lun luns = 8 [(google.api.field_behavior) = IMMUTABLE];

  // Input only. List of Volumes to attach to this Instance on creation.
  // This field won't be populated in Get/List responses.
  repeated Volume volumes = 16 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. List of networks associated with this server.
  repeated Network networks = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if the interactive serial console feature is enabled for
  // the instance, false otherwise. The default value is false.
  bool interactive_serial_console_enabled = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The OS image currently installed on the server.
  string os_image = 12;

  // Immutable. Pod name.
  // Pod is an independent part of infrastructure.
  // Instance can be connected to the assets (networks, volumes) allocated
  // in the same pod only.
  string pod = 13 [(google.api.field_behavior) = IMMUTABLE];

  // Instance network template name. For eg, bondaa-bondaa, bondab-nic, etc.
  // Generally, the template name follows the syntax of
  // "bond<bond_mode>" or "nic".
  string network_template = 14 [(google.api.resource_reference) = {
    type: "baremetalsolution.googleapis.com/ServerNetworkTemplate"
  }];

  // List of logical interfaces for the instance. The number of logical
  // interfaces will be the same as number of hardware bond/nic on the chosen
  // network template. For the non-multivlan configurations (for eg, existing
  // servers) that use existing default network template (bondaa-bondaa), both
  // the Instance.networks field and the Instance.logical_interfaces fields will
  // be filled to ensure backward compatibility. For the others, only
  // Instance.logical_interfaces will be filled.
  repeated LogicalInterface logical_interfaces = 15;

  // Output only. Text field about info for logging in.
  string login_info = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The workload profile for the instance.
  WorkloadProfile workload_profile = 18;

  // Output only. The firmware version for the instance.
  string firmware_version = 19 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting server information.
message GetInstanceRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Instance"
    }
  ];
}

// Message for requesting the list of servers.
message ListInstancesRequest {
  // Required. Parent value for ListInstancesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, the server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results from the server.
  string page_token = 3;

  // List filter.
  string filter = 4;
}

// Response message for the list of servers.
message ListInstancesResponse {
  // The list of servers.
  repeated Instance instances = 1;

  // A token identifying a page of results from the server.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message requesting to updating a server.
message UpdateInstanceRequest {
  // Required. The server to update.
  //
  // The `name` field is used to identify the instance to update.
  // Format: projects/{project}/locations/{location}/instances/{instance}
  Instance instance = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  // The currently supported fields are:
  //   `labels`
  //   `hyperthreading_enabled`
  //   `os_image`
  google.protobuf.FieldMask update_mask = 2;
}

// Message requesting rename of a server.
message RenameInstanceRequest {
  // Required. The `name` field is used to identify the instance.
  // Format: projects/{project}/locations/{location}/instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Instance"
    }
  ];

  // Required. The new `id` of the instance.
  string new_instance_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Message requesting to reset a server.
message ResetInstanceRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Instance"
    }
  ];
}

// Message requesting to start a server.
message StartInstanceRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Instance"
    }
  ];
}

// Response message from starting a server.
message StartInstanceResponse {}

// Message requesting to stop a server.
message StopInstanceRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Instance"
    }
  ];
}

// Response message from stopping a server.
message StopInstanceResponse {}

// Message for enabling the interactive serial console on an instance.
message EnableInteractiveSerialConsoleRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Instance"
    }
  ];
}

// Message for response of EnableInteractiveSerialConsole.
message EnableInteractiveSerialConsoleResponse {}

// Message for disabling the interactive serial console on an instance.
message DisableInteractiveSerialConsoleRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Instance"
    }
  ];
}

// Message for response of DisableInteractiveSerialConsole.
message DisableInteractiveSerialConsoleResponse {}

// Message for detach specific LUN from an Instance.
message DetachLunRequest {
  // Required. Name of the instance.
  string instance = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Instance"
    }
  ];

  // Required. Name of the Lun to detach.
  string lun = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Lun"
    }
  ];

  // If true, performs lun unmapping without instance reboot.
  bool skip_reboot = 3;
}

// Network template.
message ServerNetworkTemplate {
  option (google.api.resource) = {
    type: "baremetalsolution.googleapis.com/ServerNetworkTemplate"
    pattern: "projects/{project}/locations/{location}/serverNetworkTemplate/{server_network_template}"
  };

  // Logical interface.
  message LogicalInterface {
    // Interface type.
    enum InterfaceType {
      // Unspecified value.
      INTERFACE_TYPE_UNSPECIFIED = 0;

      // Bond interface type.
      BOND = 1;

      // NIC interface type.
      NIC = 2;
    }

    // Interface name.
    // This is not a globally unique identifier.
    // Name is unique only inside the ServerNetworkTemplate. This is of syntax
    // <bond><interface_type_index><bond_mode> or <nic><interface_type_index>
    // and forms part of the network template name.
    string name = 1;

    // Interface type.
    InterfaceType type = 2;

    // If true, interface must have network connected.
    bool required = 3;
  }

  // Output only. Template's unique name. The full resource name follows the
  // pattern:
  // `projects/{project}/locations/{location}/serverNetworkTemplate/{server_network_template}`
  // Generally, the {server_network_template} follows the syntax of
  // "bond<interface_type_index><bond_mode>" or "nic<interface_type_index>".
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Instance types this template is applicable to.
  repeated string applicable_instance_types = 2;

  // Logical interfaces.
  repeated LogicalInterface logical_interfaces = 3;
}
