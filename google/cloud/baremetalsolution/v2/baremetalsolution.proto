// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.baremetalsolution.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/baremetalsolution/v2/instance.proto";
import "google/cloud/baremetalsolution/v2/lun.proto";
import "google/cloud/baremetalsolution/v2/network.proto";
import "google/cloud/baremetalsolution/v2/nfs_share.proto";
import "google/cloud/baremetalsolution/v2/osimage.proto";
import "google/cloud/baremetalsolution/v2/provisioning.proto";
import "google/cloud/baremetalsolution/v2/ssh_key.proto";
import "google/cloud/baremetalsolution/v2/volume.proto";
import "google/cloud/baremetalsolution/v2/volume_snapshot.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BareMetalSolution.V2";
option go_package = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb;baremetalsolutionpb";
option java_multiple_files = true;
option java_outer_classname = "BareMetalSolutionProto";
option java_package = "com.google.cloud.baremetalsolution.v2";
option php_namespace = "Google\\Cloud\\BareMetalSolution\\V2";
option ruby_package = "Google::Cloud::BareMetalSolution::V2";

// Performs management operations on Bare Metal Solution servers.
//
// The `baremetalsolution.googleapis.com` service provides management
// capabilities for Bare Metal Solution servers. To access the API methods, you
// must assign Bare Metal Solution IAM roles containing the desired permissions
// to your staff in your Google Cloud project. You must also enable the Bare
// Metal Solution API. Once enabled, the methods act
// upon specific servers in your Bare Metal Solution environment.
service BareMetalSolution {
  option (google.api.default_host) = "baremetalsolution.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // List servers in a given project and location.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get details about a single server.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update details of a single server.
  rpc UpdateInstance(UpdateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{instance.name=projects/*/locations/*/instances/*}"
      body: "instance"
    };
    option (google.api.method_signature) = "instance,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // RenameInstance sets a new name for an instance.
  // Use with caution, previous names become immediately invalidated.
  rpc RenameInstance(RenameInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:rename"
      body: "*"
    };
    option (google.api.method_signature) = "name,new_instance_id";
  }

  // Perform an ungraceful, hard reset on a server. Equivalent to shutting the
  // power off and then turning it back on.
  rpc ResetInstance(ResetInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:reset"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "ResetInstanceResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Starts a server that was shutdown.
  rpc StartInstance(StartInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:start"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "StartInstanceResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Stop a running server.
  rpc StopInstance(StopInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:stop"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "StopInstanceResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Enable the interactive serial console feature on an instance.
  rpc EnableInteractiveSerialConsole(EnableInteractiveSerialConsoleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:enableInteractiveSerialConsole"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "EnableInteractiveSerialConsoleResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Disable the interactive serial console feature on an instance.
  rpc DisableInteractiveSerialConsole(DisableInteractiveSerialConsoleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:disableInteractiveSerialConsole"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "DisableInteractiveSerialConsoleResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Detach LUN from Instance.
  rpc DetachLun(DetachLunRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{instance=projects/*/locations/*/instances/*}:detachLun"
      body: "*"
    };
    option (google.api.method_signature) = "instance,lun";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the public SSH keys registered for the specified project.
  // These SSH keys are used only for the interactive serial console feature.
  rpc ListSSHKeys(ListSSHKeysRequest) returns (ListSSHKeysResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/sshKeys"
    };
    option (google.api.method_signature) = "parent";
  }

  // Register a public SSH key in the specified project for use with the
  // interactive serial console feature.
  rpc CreateSSHKey(CreateSSHKeyRequest) returns (SSHKey) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/sshKeys"
      body: "ssh_key"
    };
    option (google.api.method_signature) = "parent,ssh_key,ssh_key_id";
  }

  // Deletes a public SSH key registered in the specified project.
  rpc DeleteSSHKey(DeleteSSHKeyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/sshKeys/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List storage volumes in a given project and location.
  rpc ListVolumes(ListVolumesRequest) returns (ListVolumesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/volumes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get details of a single storage volume.
  rpc GetVolume(GetVolumeRequest) returns (Volume) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/volumes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update details of a single storage volume.
  rpc UpdateVolume(UpdateVolumeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{volume.name=projects/*/locations/*/volumes/*}"
      body: "volume"
    };
    option (google.api.method_signature) = "volume,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Volume"
      metadata_type: "OperationMetadata"
    };
  }

  // RenameVolume sets a new name for a volume.
  // Use with caution, previous names become immediately invalidated.
  rpc RenameVolume(RenameVolumeRequest) returns (Volume) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/volumes/*}:rename"
      body: "*"
    };
    option (google.api.method_signature) = "name,new_volume_id";
  }

  // Skips volume's cooloff and deletes it now.
  // Volume must be in cooloff state.
  rpc EvictVolume(EvictVolumeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/volumes/*}:evict"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Emergency Volume resize.
  rpc ResizeVolume(ResizeVolumeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{volume=projects/*/locations/*/volumes/*}:resize"
      body: "*"
    };
    option (google.api.method_signature) = "volume,size_gib";
    option (google.longrunning.operation_info) = {
      response_type: "Volume"
      metadata_type: "OperationMetadata"
    };
  }

  // List network in a given project and location.
  rpc ListNetworks(ListNetworksRequest) returns (ListNetworksResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/networks"
    };
    option (google.api.method_signature) = "parent";
  }

  // List all Networks (and used IPs for each Network) in the vendor account
  // associated with the specified project.
  rpc ListNetworkUsage(ListNetworkUsageRequest)
      returns (ListNetworkUsageResponse) {
    option (google.api.http) = {
      get: "/v2/{location=projects/*/locations/*}/networks:listNetworkUsage"
    };
    option (google.api.method_signature) = "location";
  }

  // Get details of a single network.
  rpc GetNetwork(GetNetworkRequest) returns (Network) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/networks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update details of a single network.
  rpc UpdateNetwork(UpdateNetworkRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{network.name=projects/*/locations/*/networks/*}"
      body: "network"
    };
    option (google.api.method_signature) = "network,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Network"
      metadata_type: "OperationMetadata"
    };
  }

  // Takes a snapshot of a boot volume.
  // Returns INVALID_ARGUMENT if called for a non-boot volume.
  rpc CreateVolumeSnapshot(CreateVolumeSnapshotRequest)
      returns (VolumeSnapshot) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*/volumes/*}/snapshots"
      body: "volume_snapshot"
    };
    option (google.api.method_signature) = "parent,volume_snapshot";
  }

  // Uses the specified snapshot to restore its parent volume.
  // Returns INVALID_ARGUMENT if called for a non-boot volume.
  rpc RestoreVolumeSnapshot(RestoreVolumeSnapshotRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{volume_snapshot=projects/*/locations/*/volumes/*/snapshots/*}:restoreVolumeSnapshot"
      body: "*"
    };
    option (google.api.method_signature) = "volume_snapshot";
    option (google.longrunning.operation_info) = {
      response_type: "VolumeSnapshot"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a volume snapshot.
  // Returns INVALID_ARGUMENT if called for a non-boot volume.
  rpc DeleteVolumeSnapshot(DeleteVolumeSnapshotRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/volumes/*/snapshots/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns the specified snapshot resource.
  // Returns INVALID_ARGUMENT if called for a non-boot volume.
  rpc GetVolumeSnapshot(GetVolumeSnapshotRequest) returns (VolumeSnapshot) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/volumes/*/snapshots/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves the list of snapshots for the specified volume.
  // Returns a response with an empty list of snapshots if called
  // for a non-boot volume.
  rpc ListVolumeSnapshots(ListVolumeSnapshotsRequest)
      returns (ListVolumeSnapshotsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*/volumes/*}/snapshots"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get details of a single storage logical unit number(LUN).
  rpc GetLun(GetLunRequest) returns (Lun) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/volumes/*/luns/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List storage volume luns for given storage volume.
  rpc ListLuns(ListLunsRequest) returns (ListLunsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*/volumes/*}/luns"
    };
    option (google.api.method_signature) = "parent";
  }

  // Skips lun's cooloff and deletes it now.
  // Lun must be in cooloff state.
  rpc EvictLun(EvictLunRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/volumes/*/luns/*}:evict"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Get details of a single NFS share.
  rpc GetNfsShare(GetNfsShareRequest) returns (NfsShare) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/nfsShares/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List NFS shares.
  rpc ListNfsShares(ListNfsSharesRequest) returns (ListNfsSharesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/nfsShares"
    };
    option (google.api.method_signature) = "parent";
  }

  // Update details of a single NFS share.
  rpc UpdateNfsShare(UpdateNfsShareRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{nfs_share.name=projects/*/locations/*/nfsShares/*}"
      body: "nfs_share"
    };
    option (google.api.method_signature) = "nfs_share,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "NfsShare"
      metadata_type: "OperationMetadata"
    };
  }

  // Create an NFS share.
  rpc CreateNfsShare(CreateNfsShareRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/nfsShares"
      body: "nfs_share"
    };
    option (google.api.method_signature) = "parent,nfs_share";
    option (google.longrunning.operation_info) = {
      response_type: "NfsShare"
      metadata_type: "OperationMetadata"
    };
  }

  // RenameNfsShare sets a new name for an nfsshare.
  // Use with caution, previous names become immediately invalidated.
  rpc RenameNfsShare(RenameNfsShareRequest) returns (NfsShare) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/nfsShares/*}:rename"
      body: "*"
    };
    option (google.api.method_signature) = "name,new_nfsshare_id";
  }

  // Delete an NFS share. The underlying volume is automatically deleted.
  rpc DeleteNfsShare(DeleteNfsShareRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/nfsShares/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // List the budget details to provision resources on a given project.
  rpc ListProvisioningQuotas(ListProvisioningQuotasRequest)
      returns (ListProvisioningQuotasResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/provisioningQuotas"
    };
    option (google.api.method_signature) = "parent";
  }

  // Submit a provisiong configuration for a given project.
  rpc SubmitProvisioningConfig(SubmitProvisioningConfigRequest)
      returns (SubmitProvisioningConfigResponse) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/provisioningConfigs:submit"
      body: "*"
    };
    option (google.api.method_signature) = "parent,provisioning_config";
  }

  // Get ProvisioningConfig by name.
  rpc GetProvisioningConfig(GetProvisioningConfigRequest)
      returns (ProvisioningConfig) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/provisioningConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create new ProvisioningConfig.
  rpc CreateProvisioningConfig(CreateProvisioningConfigRequest)
      returns (ProvisioningConfig) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/provisioningConfigs"
      body: "provisioning_config"
    };
    option (google.api.method_signature) = "parent,provisioning_config";
  }

  // Update existing ProvisioningConfig.
  rpc UpdateProvisioningConfig(UpdateProvisioningConfigRequest)
      returns (ProvisioningConfig) {
    option (google.api.http) = {
      patch: "/v2/{provisioning_config.name=projects/*/locations/*/provisioningConfigs/*}"
      body: "provisioning_config"
    };
    option (google.api.method_signature) = "provisioning_config,update_mask";
  }

  // RenameNetwork sets a new name for a network.
  // Use with caution, previous names become immediately invalidated.
  rpc RenameNetwork(RenameNetworkRequest) returns (Network) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/networks/*}:rename"
      body: "*"
    };
    option (google.api.method_signature) = "name,new_network_id";
  }

  // Retrieves the list of OS images which are currently approved.
  rpc ListOSImages(ListOSImagesRequest) returns (ListOSImagesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/osImages"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Represents the metadata from a long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the action executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user requested the cancellation
  // of the operation. Operations that have been successfully cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used with the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Response message from resetting a server.
message ResetInstanceResponse {}
