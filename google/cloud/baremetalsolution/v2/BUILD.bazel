# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "baremetalsolution_proto",
    srcs = [
        "baremetalsolution.proto",
        "common.proto",
        "instance.proto",
        "lun.proto",
        "network.proto",
        "nfs_share.proto",
        "osimage.proto",
        "provisioning.proto",
        "ssh_key.proto",
        "volume.proto",
        "volume_snapshot.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "baremetalsolution_proto_with_info",
    deps = [
        ":baremetalsolution_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "baremetalsolution_java_proto",
    deps = [":baremetalsolution_proto"],
)

java_grpc_library(
    name = "baremetalsolution_java_grpc",
    srcs = [":baremetalsolution_proto"],
    deps = [":baremetalsolution_java_proto"],
)

java_gapic_library(
    name = "baremetalsolution_java_gapic",
    srcs = [":baremetalsolution_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "baremetalsolution_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "baremetalsolution_v2.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
        ":baremetalsolution_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":baremetalsolution_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "baremetalsolution_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.baremetalsolution.v2.BareMetalSolutionClientHttpJsonTest",
        "com.google.cloud.baremetalsolution.v2.BareMetalSolutionClientTest",
    ],
    runtime_deps = [":baremetalsolution_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-baremetalsolution-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":baremetalsolution_java_gapic",
        ":baremetalsolution_java_grpc",
        ":baremetalsolution_java_proto",
        ":baremetalsolution_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "baremetalsolution_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb",
    protos = [":baremetalsolution_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "baremetalsolution_go_gapic",
    srcs = [":baremetalsolution_proto_with_info"],
    grpc_service_config = "baremetalsolution_grpc_service_config.json",
    importpath = "cloud.google.com/go/baremetalsolution/apiv2;baremetalsolution",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "baremetalsolution_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":baremetalsolution_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-baremetalsolution-v2-go",
    deps = [
        ":baremetalsolution_go_gapic",
        ":baremetalsolution_go_gapic_srcjar-metadata.srcjar",
        ":baremetalsolution_go_gapic_srcjar-snippets.srcjar",
        ":baremetalsolution_go_gapic_srcjar-test.srcjar",
        ":baremetalsolution_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "baremetalsolution_py_gapic",
    srcs = [":baremetalsolution_proto"],
    grpc_service_config = "baremetalsolution_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=bare_metal_solution",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-bare-metal-solution",
    ],
    rest_numeric_enums = True,
    service_yaml = "baremetalsolution_v2.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "baremetalsolution_py_gapic_test",
    srcs = [
        "baremetalsolution_py_gapic_pytest.py",
        "baremetalsolution_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":baremetalsolution_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "baremetalsolution-v2-py",
    deps = [
        ":baremetalsolution_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "baremetalsolution_php_proto",
    deps = [":baremetalsolution_proto"],
)

php_gapic_library(
    name = "baremetalsolution_php_gapic",
    srcs = [":baremetalsolution_proto_with_info"],
    grpc_service_config = "baremetalsolution_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "baremetalsolution_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":baremetalsolution_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-baremetalsolution-v2-php",
    deps = [
        ":baremetalsolution_php_gapic",
        ":baremetalsolution_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "baremetalsolution_nodejs_gapic",
    package_name = "@google-cloud/bare-metal-solution",
    src = ":baremetalsolution_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "baremetalsolution_grpc_service_config.json",
    package = "google.cloud.baremetalsolution.v2",
    rest_numeric_enums = True,
    service_yaml = "baremetalsolution_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "baremetalsolution-v2-nodejs",
    deps = [
        ":baremetalsolution_nodejs_gapic",
        ":baremetalsolution_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "baremetalsolution_ruby_proto",
    deps = [":baremetalsolution_proto"],
)

ruby_grpc_library(
    name = "baremetalsolution_ruby_grpc",
    srcs = [":baremetalsolution_proto"],
    deps = [":baremetalsolution_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "baremetalsolution_ruby_gapic",
    srcs = [":baremetalsolution_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=baremetalsolution.googleapis.com",
        "ruby-cloud-api-shortname=baremetalsolution",
        "ruby-cloud-gem-name=google-cloud-bare_metal_solution-v2",
        "ruby-cloud-product-url=https://cloud.google.com/bare-metal/",
    ],
    grpc_service_config = "baremetalsolution_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Bare Metal Solution is a managed solution that provides purpose-built HPE or Atos bare-metal servers in regional extensions that are connected to Google Cloud by a managed, high-performance connection with a low-latency network fabric.",
    ruby_cloud_title = "Bare Metal Solution V2",
    service_yaml = "baremetalsolution_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":baremetalsolution_ruby_grpc",
        ":baremetalsolution_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-baremetalsolution-v2-ruby",
    deps = [
        ":baremetalsolution_ruby_gapic",
        ":baremetalsolution_ruby_grpc",
        ":baremetalsolution_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "baremetalsolution_csharp_proto",
    deps = [":baremetalsolution_proto"],
)

csharp_grpc_library(
    name = "baremetalsolution_csharp_grpc",
    srcs = [":baremetalsolution_proto"],
    deps = [":baremetalsolution_csharp_proto"],
)

csharp_gapic_library(
    name = "baremetalsolution_csharp_gapic",
    srcs = [":baremetalsolution_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "baremetalsolution_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "baremetalsolution_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":baremetalsolution_csharp_grpc",
        ":baremetalsolution_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-baremetalsolution-v2-csharp",
    deps = [
        ":baremetalsolution_csharp_gapic",
        ":baremetalsolution_csharp_grpc",
        ":baremetalsolution_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "baremetalsolution_cc_proto",
    deps = [":baremetalsolution_proto"],
)

cc_grpc_library(
    name = "baremetalsolution_cc_grpc",
    srcs = [":baremetalsolution_proto"],
    grpc_only = True,
    deps = [":baremetalsolution_cc_proto"],
)
