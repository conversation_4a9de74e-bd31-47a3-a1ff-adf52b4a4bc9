// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.baremetalsolution.v2;

option csharp_namespace = "Google.Cloud.BareMetalSolution.V2";
option go_package = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb;baremetalsolutionpb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.cloud.baremetalsolution.v2";
option php_namespace = "Google\\Cloud\\BareMetalSolution\\V2";
option ruby_package = "Google::Cloud::BareMetalSolution::V2";

// Performance tier of the Volume.
enum VolumePerformanceTier {
  // Value is not specified.
  VOLUME_PERFORMANCE_TIER_UNSPECIFIED = 0;

  // Regular volumes, shared aggregates.
  VOLUME_PERFORMANCE_TIER_SHARED = 1;

  // Assigned aggregates.
  VOLUME_PERFORMANCE_TIER_ASSIGNED = 2;

  // High throughput aggregates.
  VOLUME_PERFORMANCE_TIER_HT = 3;
}

// The possible values for a workload profile.
enum WorkloadProfile {
  // The workload profile is in an unknown state.
  WORKLOAD_PROFILE_UNSPECIFIED = 0;

  // The workload profile is generic.
  WORKLOAD_PROFILE_GENERIC = 1;

  // The workload profile is hana.
  WORKLOAD_PROFILE_HANA = 2;
}
