// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.baremetalsolution.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.BareMetalSolution.V2";
option go_package = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb;baremetalsolutionpb";
option java_multiple_files = true;
option java_outer_classname = "NfsShareProto";
option java_package = "com.google.cloud.baremetalsolution.v2";
option php_namespace = "Google\\Cloud\\BareMetalSolution\\V2";
option ruby_package = "Google::Cloud::BareMetalSolution::V2";

// An NFS share.
message NfsShare {
  option (google.api.resource) = {
    type: "baremetalsolution.googleapis.com/NFSShare"
    pattern: "projects/{project}/locations/{location}/nfsShares/{nfs_share}"
  };

  // The possible states for this NFS share.
  enum State {
    // The share is in an unknown state.
    STATE_UNSPECIFIED = 0;

    // The share has been provisioned.
    PROVISIONED = 1;

    // The NFS Share is being created.
    CREATING = 2;

    // The NFS Share is being updated.
    UPDATING = 3;

    // The NFS Share has been requested to be deleted.
    DELETING = 4;
  }

  // The possible mount permissions.
  enum MountPermissions {
    // Permissions were not specified.
    MOUNT_PERMISSIONS_UNSPECIFIED = 0;

    // NFS share can be mount with read-only permissions.
    READ = 1;

    // NFS share can be mount with read-write permissions.
    READ_WRITE = 2;
  }

  // Represents an 'access point' for the share.
  message AllowedClient {
    // The network the access point sits on.
    string network = 1 [(google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Network"
    }];

    // Output only. The IP address of the share on this network. Assigned
    // automatically during provisioning based on the network's services_cidr.
    string share_ip = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // The subnet of IP addresses permitted to access the share.
    string allowed_clients_cidr = 3;

    // Mount permissions.
    MountPermissions mount_permissions = 4;

    // Allow dev flag.  Which controls whether to allow creation of devices.
    bool allow_dev = 5;

    // Allow the setuid flag.
    bool allow_suid = 6;

    // Disable root squashing, which is a feature of NFS.
    // Root squash is a special mapping of the remote superuser (root) identity
    // when using identity authentication.
    bool no_root_squash = 7;

    // Output only. The path to access NFS, in format shareIP:/InstanceID
    // InstanceID is the generated ID instead of customer provided name.
    // example like "10.0.0.0:/g123456789-nfs001"
    string nfs_path = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The storage type for a volume.
  enum StorageType {
    // The storage type for this volume is unknown.
    STORAGE_TYPE_UNSPECIFIED = 0;

    // The storage type for this volume is SSD.
    SSD = 1;

    // This storage type for this volume is HDD.
    HDD = 2;
  }

  // Immutable. The name of the NFS share.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. An identifier for the NFS share, generated by the backend.
  // This field will be deprecated in the future, use `id` instead.
  string nfs_share_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An identifier for the NFS share, generated by the backend.
  // This is the same value as nfs_share_id and will replace it in the future.
  string id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the NFS share.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The underlying volume of the share. Created automatically
  // during provisioning.
  string volume = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Volume"
    }
  ];

  // List of allowed access points.
  repeated AllowedClient allowed_clients = 5;

  // Labels as key value pairs.
  map<string, string> labels = 6;

  // The requested size, in GiB.
  int64 requested_size_gib = 7;

  // Immutable. The storage type of the underlying volume.
  StorageType storage_type = 9 [(google.api.field_behavior) = IMMUTABLE];
}

// Message for requesting NFS share information.
message GetNfsShareRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/NFSShare"
    }
  ];
}

// Message for requesting a list of NFS shares.
message ListNfsSharesRequest {
  // Required. Parent value for ListNfsSharesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Requested page size. The server might return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results from the server.
  string page_token = 3;

  // List filter.
  string filter = 4;
}

// Response message containing the list of NFS shares.
message ListNfsSharesResponse {
  // The list of NFS shares.
  repeated NfsShare nfs_shares = 1;

  // A token identifying a page of results from the server.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message requesting to updating an NFS share.
message UpdateNfsShareRequest {
  // Required. The NFS share to update.
  //
  // The `name` field is used to identify the NFS share to update.
  // Format: projects/{project}/locations/{location}/nfsShares/{nfs_share}
  NfsShare nfs_share = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  // The only currently supported fields are:
  //   `labels`
  //   `allowed_clients`
  google.protobuf.FieldMask update_mask = 2;
}

// Message requesting rename of a server.
message RenameNfsShareRequest {
  // Required. The `name` field is used to identify the nfsshare.
  // Format: projects/{project}/locations/{location}/nfsshares/{nfsshare}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/NFSShare"
    }
  ];

  // Required. The new `id` of the nfsshare.
  string new_nfsshare_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Message for creating an NFS share.
message CreateNfsShareRequest {
  // Required. The parent project and location.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The NfsShare to create.
  NfsShare nfs_share = 2 [(google.api.field_behavior) = REQUIRED];
}

// Message for deleting an NFS share.
message DeleteNfsShareRequest {
  // Required. The name of the NFS share to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/NFSShare"
    }
  ];
}
