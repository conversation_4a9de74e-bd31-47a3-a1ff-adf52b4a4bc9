// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.baremetalsolution.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.BareMetalSolution.V2";
option go_package = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb;baremetalsolutionpb";
option java_multiple_files = true;
option java_outer_classname = "NetworkProto";
option java_package = "com.google.cloud.baremetalsolution.v2";
option php_namespace = "Google\\Cloud\\BareMetalSolution\\V2";
option ruby_package = "Google::Cloud::BareMetalSolution::V2";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/InterconnectAttachment"
  pattern: "projects/{project}/regions/{region}/interconnectAttachments/{interconnect_attachment}"
};

// A Network.
message Network {
  option (google.api.resource) = {
    type: "baremetalsolution.googleapis.com/Network"
    pattern: "projects/{project}/locations/{location}/networks/{network}"
  };

  // Network type.
  enum Type {
    // Unspecified value.
    TYPE_UNSPECIFIED = 0;

    // Client network, a network peered to a Google Cloud VPC.
    CLIENT = 1;

    // Private network, a network local to the Bare Metal Solution environment.
    PRIVATE = 2;
  }

  // The possible states for this Network.
  enum State {
    // The Network is in an unknown state.
    STATE_UNSPECIFIED = 0;

    // The Network is provisioning.
    PROVISIONING = 1;

    // The Network has been provisioned.
    PROVISIONED = 2;

    // The Network is being deprovisioned.
    DEPROVISIONING = 3;

    // The Network is being updated.
    UPDATING = 4;
  }

  // Output only. The resource name of this `Network`.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // Format:
  // `projects/{project}/locations/{location}/networks/{network}`
  string name = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // An identifier for the `Network`, generated by the backend.
  string id = 10;

  // The type of this network.
  Type type = 2;

  // IP address configured.
  string ip_address = 3;

  // List of physical interfaces.
  repeated string mac_address = 4;

  // The Network state.
  State state = 6;

  // The vlan id of the Network.
  string vlan_id = 7;

  // The cidr of the Network.
  string cidr = 8;

  // The vrf for the Network.
  VRF vrf = 9;

  // Labels as key value pairs.
  map<string, string> labels = 11;

  // IP range for reserved for services (e.g. NFS).
  string services_cidr = 12;

  // List of IP address reservations in this network.
  // When updating this field, an error will be generated if a reservation
  // conflicts with an IP address already allocated to a physical server.
  repeated NetworkAddressReservation reservations = 13;

  // Output only. Pod name.
  string pod = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. List of mount points to attach the network to.
  repeated NetworkMountPoint mount_points = 15
      [(google.api.field_behavior) = INPUT_ONLY];

  // Whether network uses standard frames or jumbo ones.
  bool jumbo_frames_enabled = 16;

  // Output only. Gateway ip address.
  string gateway_ip = 17 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A reservation of one or more addresses in a network.
message NetworkAddressReservation {
  // The first address of this reservation block.
  // Must be specified as a single IPv4 address, e.g. ********.
  string start_address = 1;

  // The last address of this reservation block, inclusive. I.e., for cases when
  // reservations are only single addresses, end_address and start_address will
  // be the same.
  // Must be specified as a single IPv4 address, e.g. ********.
  string end_address = 2;

  // A note about this reservation, intended for human consumption.
  string note = 3;
}

// A network VRF.
message VRF {
  // The possible states for this VRF.
  enum State {
    // The unspecified state.
    STATE_UNSPECIFIED = 0;

    // The vrf is provisioning.
    PROVISIONING = 1;

    // The vrf is provisioned.
    PROVISIONED = 2;
  }

  // QOS policy parameters.
  message QosPolicy {
    // The bandwidth permitted by the QOS policy, in gbps.
    double bandwidth_gbps = 1;
  }

  // VLAN attachment details.
  message VlanAttachment {
    // The peer vlan ID of the attachment.
    int64 peer_vlan_id = 1;

    // The peer IP of the attachment.
    string peer_ip = 2;

    // The router IP of the attachment.
    string router_ip = 3;

    // Input only. Pairing key.
    string pairing_key = 4 [(google.api.field_behavior) = INPUT_ONLY];

    // The QOS policy applied to this VLAN attachment.
    // This value should be preferred to using qos at vrf level.
    QosPolicy qos_policy = 5;

    // Immutable. The identifier of the attachment within vrf.
    string id = 6 [(google.api.field_behavior) = IMMUTABLE];

    // Optional. The name of the vlan attachment within vrf. This is of the form
    // projects/{project_number}/regions/{region}/interconnectAttachments/{interconnect_attachment}
    string interconnect_attachment = 7 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "compute.googleapis.com/InterconnectAttachment"
      }
    ];
  }

  // The name of the VRF.
  string name = 1;

  // The possible state of VRF.
  State state = 5;

  // The QOS policy applied to this VRF.
  // The value is only meaningful when all the vlan attachments have the same
  // QoS. This field should not be used for new integrations, use vlan
  // attachment level qos instead. The field is left for backward-compatibility.
  QosPolicy qos_policy = 6;

  // The list of VLAN attachments for the VRF.
  repeated VlanAttachment vlan_attachments = 7;
}

// Each logical interface represents a logical abstraction of the underlying
// physical interface (for eg. bond, nic) of the instance. Each logical
// interface can effectively map to multiple network-IP pairs and still be
// mapped to one underlying physical interface.
message LogicalInterface {
  // Each logical network interface is effectively a network and IP pair.
  message LogicalNetworkInterface {
    // Name of the network
    string network = 1;

    // IP address in the network
    string ip_address = 2;

    // Whether this interface is the default gateway for the instance. Only
    // one interface can be the default gateway for the instance.
    bool default_gateway = 3;

    // Type of network.
    Network.Type network_type = 4;

    // An identifier for the `Network`, generated by the backend.
    string id = 5;
  }

  // List of logical network interfaces within a logical interface.
  repeated LogicalNetworkInterface logical_network_interfaces = 1;

  // Interface name. This is of syntax <bond><bond_mode> or <nic> and
  // forms part of the network template name.
  string name = 2;

  // The index of the logical interface mapping to the index of the hardware
  // bond or nic on the chosen network template. This field is deprecated.
  int32 interface_index = 3 [deprecated = true];
}

// Message for requesting network information.
message GetNetworkRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Network"
    }
  ];
}

// Message for requesting a list of networks.
message ListNetworksRequest {
  // Required. Parent value for ListNetworksRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Requested page size. The server might return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results from the server.
  string page_token = 3;

  // List filter.
  string filter = 4;
}

// Response message containing the list of networks.
message ListNetworksResponse {
  // The list of networks.
  repeated Network networks = 1;

  // A token identifying a page of results from the server.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message requesting to updating a network.
message UpdateNetworkRequest {
  // Required. The network to update.
  //
  // The `name` field is used to identify the instance to update.
  // Format: projects/{project}/locations/{location}/networks/{network}
  Network network = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  // The only currently supported fields are:
  //   `labels`, `reservations`, `vrf.vlan_attachments`
  google.protobuf.FieldMask update_mask = 2;
}

// Network with all used IP addresses.
message NetworkUsage {
  // Network.
  Network network = 1;

  // All used IP addresses in this network.
  repeated string used_ips = 2;
}

// Request to get networks with IPs.
message ListNetworkUsageRequest {
  // Required. Parent value (project and location).
  string location = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// Response with Networks with IPs
message ListNetworkUsageResponse {
  // Networks with IPs.
  repeated NetworkUsage networks = 1;
}

// Mount point for a network.
message NetworkMountPoint {
  // Instance to attach network to.
  string instance = 1 [(google.api.resource_reference) = {
    type: "baremetalsolution.googleapis.com/Instance"
  }];

  // Logical interface to detach from.
  string logical_interface = 2;

  // Network should be a default gateway.
  bool default_gateway = 3;

  // Ip address of the server.
  string ip_address = 4;
}

// Message requesting rename of a server.
message RenameNetworkRequest {
  // Required. The `name` field is used to identify the network.
  // Format: projects/{project}/locations/{location}/networks/{network}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/Network"
    }
  ];

  // Required. The new `id` of the network.
  string new_network_id = 2 [(google.api.field_behavior) = REQUIRED];
}
