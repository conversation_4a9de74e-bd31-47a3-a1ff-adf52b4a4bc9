// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.baremetalsolution.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.BareMetalSolution.V2";
option go_package = "cloud.google.com/go/baremetalsolution/apiv2/baremetalsolutionpb;baremetalsolutionpb";
option java_multiple_files = true;
option java_outer_classname = "SshKeyProto";
option java_package = "com.google.cloud.baremetalsolution.v2";
option php_namespace = "Google\\Cloud\\BareMetalSolution\\V2";
option ruby_package = "Google::Cloud::BareMetalSolution::V2";

// An SSH key, used for authorizing with the interactive serial console feature.
message SSHKey {
  option (google.api.resource) = {
    type: "baremetalsolution.googleapis.com/SshKey"
    pattern: "projects/{project}/locations/{location}/sshKeys/{ssh_key}"
  };

  // Output only. The name of this SSH key.
  // Currently, the only valid value for the location is "global".
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The public SSH key. This must be in OpenSSH .authorized_keys format.
  string public_key = 2;
}

// Message for listing the public SSH keys in a project.
message ListSSHKeysRequest {
  // Required. The parent containing the SSH keys.
  // Currently, the only valid value for the location is "global".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;
}

// Message for response of ListSSHKeys.
message ListSSHKeysResponse {
  // The SSH keys registered in the project.
  repeated SSHKey ssh_keys = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 90;
}

// Message for registering a public SSH key in a project.
message CreateSSHKeyRequest {
  // Required. The parent containing the SSH keys.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The SSH key to register.
  SSHKey ssh_key = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the key, which will become the final component
  // of the key's resource name.
  //
  // This value must match the regex:
  //   [a-zA-Z0-9@.\-_]{1,64}
  string ssh_key_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Message for deleting an SSH key from a project.
message DeleteSSHKeyRequest {
  // Required. The name of the SSH key to delete.
  // Currently, the only valid value for the location is "global".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "baremetalsolution.googleapis.com/SshKey"
    }
  ];
}
