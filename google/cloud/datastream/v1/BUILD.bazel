# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "datastream_proto",
    srcs = [
        "datastream.proto",
        "datastream_resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "datastream_proto_with_info",
    deps = [
        ":datastream_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "datastream_java_proto",
    deps = [":datastream_proto"],
)

java_grpc_library(
    name = "datastream_java_grpc",
    srcs = [":datastream_proto"],
    deps = [":datastream_java_proto"],
)

java_gapic_library(
    name = "datastream_java_gapic",
    srcs = [":datastream_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "datastream_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datastream_v1.yaml",
    test_deps = [
        ":datastream_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":datastream_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "datastream_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.datastream.v1.DatastreamClientHttpJsonTest",
        "com.google.cloud.datastream.v1.DatastreamClientTest",
    ],
    runtime_deps = [":datastream_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-datastream-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":datastream_java_gapic",
        ":datastream_java_grpc",
        ":datastream_java_proto",
        ":datastream_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "datastream_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/datastream/apiv1/datastreampb",
    protos = [":datastream_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "datastream_go_gapic",
    srcs = [":datastream_proto_with_info"],
    grpc_service_config = "datastream_grpc_service_config.json",
    importpath = "cloud.google.com/go/datastream/apiv1;datastream",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "datastream_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datastream_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-datastream-v1-go",
    deps = [
        ":datastream_go_gapic",
        ":datastream_go_gapic_srcjar-metadata.srcjar",
        ":datastream_go_gapic_srcjar-snippets.srcjar",
        ":datastream_go_gapic_srcjar-test.srcjar",
        ":datastream_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "datastream_py_gapic",
    srcs = [":datastream_proto"],
    grpc_service_config = "datastream_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datastream_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "datastream_py_gapic_test",
    srcs = [
        "datastream_py_gapic_pytest.py",
        "datastream_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":datastream_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "datastream-v1-py",
    deps = [
        ":datastream_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "datastream_php_proto",
    deps = [":datastream_proto"],
)

php_gapic_library(
    name = "datastream_php_gapic",
    srcs = [":datastream_proto_with_info"],
    grpc_service_config = "datastream_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "datastream_v1.yaml",
    transport = "grpc+rest",
    deps = [":datastream_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-datastream-v1-php",
    deps = [
        ":datastream_php_gapic",
        ":datastream_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "datastream_nodejs_gapic",
    package_name = "@google-cloud/datastream",
    src = ":datastream_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "datastream_grpc_service_config.json",
    package = "google.cloud.datastream.v1",
    rest_numeric_enums = True,
    service_yaml = "datastream_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "datastream-v1-nodejs",
    deps = [
        ":datastream_nodejs_gapic",
        ":datastream_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "datastream_ruby_proto",
    deps = [":datastream_proto"],
)

ruby_grpc_library(
    name = "datastream_ruby_grpc",
    srcs = [":datastream_proto"],
    deps = [":datastream_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "datastream_ruby_gapic",
    srcs = [":datastream_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=datastream.googleapis.com",
        "ruby-cloud-api-shortname=datastream",
        "ruby-cloud-gem-name=google-cloud-datastream-v1",
        "ruby-cloud-product-url=https://cloud.google.com/datastream/",
    ],
    grpc_service_config = "datastream_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Datastream is a serverless and easy-to-use change data capture (CDC) and replication service. It allows you to synchronize data across heterogeneous databases and applications reliably, and with minimal latency and downtime.",
    ruby_cloud_title = "Datastream V1",
    service_yaml = "datastream_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datastream_ruby_grpc",
        ":datastream_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-datastream-v1-ruby",
    deps = [
        ":datastream_ruby_gapic",
        ":datastream_ruby_grpc",
        ":datastream_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "datastream_csharp_proto",
    deps = [":datastream_proto"],
)

csharp_grpc_library(
    name = "datastream_csharp_grpc",
    srcs = [":datastream_proto"],
    deps = [":datastream_csharp_proto"],
)

csharp_gapic_library(
    name = "datastream_csharp_gapic",
    srcs = [":datastream_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "datastream_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datastream_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datastream_csharp_grpc",
        ":datastream_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-datastream-v1-csharp",
    deps = [
        ":datastream_csharp_gapic",
        ":datastream_csharp_grpc",
        ":datastream_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "datastream_cc_proto",
    deps = [":datastream_proto"],
)

cc_grpc_library(
    name = "datastream_cc_grpc",
    srcs = [":datastream_proto"],
    grpc_only = True,
    deps = [":datastream_cc_proto"],
)
