{"methodConfig": [{"name": [{"service": "google.cloud.datastream.v1.Datastream"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.datastream.v1.Datastream", "method": "CreateConnectionProfile"}, {"service": "google.cloud.datastream.v1.Datastream", "method": "UpdateConnectionProfile"}, {"service": "google.cloud.datastream.v1.Datastream", "method": "GenerateConnectionProfileReport"}, {"service": "google.cloud.datastream.v1.Datastream", "method": "DeleteConnectionProfile"}, {"service": "google.cloud.datastream.v1.Datastream", "method": "CreateStream"}, {"service": "google.cloud.datastream.v1.Datastream", "method": "UpdateStream"}, {"service": "google.cloud.datastream.v1.Datastream", "method": "DeleteStream"}, {"service": "google.cloud.datastream.v1.Datastream", "method": "CreatePrivateConnection"}, {"service": "google.cloud.datastream.v1.Datastream", "method": "DeletePrivateConnection"}, {"service": "google.cloud.datastream.v1.Datastream", "method": "CreateRoute"}, {"service": "google.cloud.datastream.v1.Datastream", "method": "DeleteRoute"}], "timeout": "60s"}]}