// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datastream.logging.v1;

import "google/api/field_behavior.proto";
import "google/cloud/datastream/v1/datastream_resources.proto";

option csharp_namespace = "Google.Cloud.Datastream.Logging.V1";
option go_package = "cloud.google.com/go/datastream/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "DatastreamLogsProto";
option java_package = "com.google.cloud.datastream.logging.v1";
option php_namespace = "Google\\Cloud\\Datastream\\Logging\\V1";
option ruby_package = "Google::Cloud::Datastream::Logging::V1";

// Log definition for activities related to a stream.
message StreamActivityLogEntry {
  // Payload for a change in the state of a stream.
  message StreamStateChange {
    // Output only. The new stream state.
    google.cloud.datastream.v1.Stream.State new_state = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // A code representing the event.
  string event_code = 1;

  // A free-text message describing the event.
  string event_message = 2;

  oneof event_payload {
    // A payload for a change in the state of a stream.
    StreamStateChange stream_state_change = 100;
  }
}
