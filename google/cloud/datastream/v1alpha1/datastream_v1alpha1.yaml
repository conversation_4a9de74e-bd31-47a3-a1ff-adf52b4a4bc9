type: google.api.Service
config_version: 3
name: datastream.googleapis.com
title: Datastream API

apis:
- name: google.cloud.datastream.v1alpha1.Datastream

types:
- name: google.cloud.datastream.v1alpha1.Error
- name: google.cloud.datastream.v1alpha1.FetchErrorsResponse
- name: google.cloud.datastream.v1alpha1.OperationMetadata
- name: google.cloud.datastream.v1alpha1.ValidationResult

documentation:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: 'google.cloud.datastream.v1alpha1.Datastream.*'
    deadline: 60.0
  - selector: google.cloud.datastream.v1alpha1.Datastream.DiscoverConnectionProfile
    deadline: 120.0
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1alpha1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1alpha1/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1alpha1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1alpha1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1alpha1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1alpha1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.datastream.v1alpha1.Datastream.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
