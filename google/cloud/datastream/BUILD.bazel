# This build file includes a target for the Ruby wrapper library for
# google-cloud-datastream.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for datastream.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "datastream_ruby_wrapper",
    srcs = ["//google/cloud/datastream/v1:datastream_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-datastream",
        "ruby-cloud-wrapper-of=v1:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/datastream/",
        "ruby-cloud-api-id=datastream.googleapis.com",
        "ruby-cloud-api-shortname=datastream",
    ],
    ruby_cloud_description = "Datastream is a serverless and easy-to-use change data capture (CDC) and replication service. It allows you to synchronize data across heterogeneous databases and applications reliably, and with minimal latency and downtime.",
    ruby_cloud_title = "Datastream",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-datastream-ruby",
    deps = [
        ":datastream_ruby_wrapper",
    ],
)
