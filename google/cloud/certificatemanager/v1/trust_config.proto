// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.certificatemanager.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CertificateManager.V1";
option go_package = "cloud.google.com/go/certificatemanager/apiv1/certificatemanagerpb;certificatemanagerpb";
option java_multiple_files = true;
option java_outer_classname = "TrustConifgProto";
option java_package = "com.google.cloud.certificatemanager.v1";
option php_namespace = "Google\\Cloud\\CertificateManager\\V1";
option ruby_package = "Google::Cloud::CertificateManager::V1";

// Request for the `ListTrustConfigs` method.
message ListTrustConfigsRequest {
  // Required. The project and location from which the TrustConfigs should be
  // listed, specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of TrustConfigs to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListTrustConfigsResponse`. Indicates
  // that this is a continuation of a prior `ListTrustConfigs` call, and that
  // the system should return the next page of data.
  string page_token = 3;

  // Filter expression to restrict the TrustConfigs returned.
  string filter = 4;

  // A list of TrustConfig field names used to specify the order of the
  // returned results. The default sorting order is ascending. To specify
  // descending order for a field, add a suffix `" desc"`.
  string order_by = 5;
}

// Response for the `ListTrustConfigs` method.
message ListTrustConfigsResponse {
  // A list of TrustConfigs for the parent resource.
  repeated TrustConfig trust_configs = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for the `GetTrustConfig` method.
message GetTrustConfigRequest {
  // Required. A name of the TrustConfig to describe. Must be in the format
  // `projects/*/locations/*/trustConfigs/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/TrustConfig"
    }
  ];
}

// Request for the `CreateTrustConfig` method.
message CreateTrustConfigRequest {
  // Required. The parent resource of the TrustConfig. Must be in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. A user-provided name of the TrustConfig. Must match the regexp
  // `[a-z0-9-]{1,63}`.
  string trust_config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A definition of the TrustConfig to create.
  TrustConfig trust_config = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `UpdateTrustConfig` method.
message UpdateTrustConfigRequest {
  // Required. A definition of the TrustConfig to update.
  TrustConfig trust_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource. For the `FieldMask`
  // definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `DeleteTrustConfig` method.
message DeleteTrustConfigRequest {
  // Required. A name of the TrustConfig to delete. Must be in the format
  // `projects/*/locations/*/trustConfigs/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/TrustConfig"
    }
  ];

  // The current etag of the TrustConfig.
  // If an etag is provided and does not match the current etag of the resource,
  // deletion will be blocked and an ABORTED error will be returned.
  string etag = 2;
}

// Defines a trust config.
message TrustConfig {
  option (google.api.resource) = {
    type: "certificatemanager.googleapis.com/TrustConfig"
    pattern: "projects/{project}/locations/{location}/trustConfigs/{trust_config}"
  };

  // Defines a trust anchor.
  message TrustAnchor {
    oneof kind {
      // PEM root certificate of the PKI used for validation.
      //
      // Each certificate provided in PEM format may occupy up to 5kB.
      string pem_certificate = 1;
    }
  }

  // Defines an intermediate CA.
  message IntermediateCA {
    oneof kind {
      // PEM intermediate certificate used for building up paths
      // for validation.
      //
      // Each certificate provided in PEM format may occupy up to 5kB.
      string pem_certificate = 1;
    }
  }

  // Defines a trust store.
  message TrustStore {
    // List of Trust Anchors to be used while performing validation
    // against a given TrustStore.
    repeated TrustAnchor trust_anchors = 1;

    // Set of intermediate CA certificates used for the path building
    // phase of chain validation.
    //
    // The field is currently not supported if TrustConfig is used for the
    // workload certificate feature.
    repeated IntermediateCA intermediate_cas = 2;
  }

  // A user-defined name of the trust config. TrustConfig names must be
  // unique globally and match pattern
  // `projects/*/locations/*/trustConfigs/*`.
  string name = 1;

  // Output only. The creation timestamp of a TrustConfig.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of a TrustConfig.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Set of labels associated with a TrustConfig.
  map<string, string> labels = 4;

  // One or more paragraphs of text description of a TrustConfig.
  string description = 5;

  // This checksum is computed by the server based on the value of other
  // fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 6;

  // Set of trust stores to perform validation against.
  //
  // This field is supported when TrustConfig is configured with Load Balancers,
  // currently not supported for SPIFFE certificate validation.
  //
  // Only one TrustStore specified is currently allowed.
  repeated TrustStore trust_stores = 8;
}
