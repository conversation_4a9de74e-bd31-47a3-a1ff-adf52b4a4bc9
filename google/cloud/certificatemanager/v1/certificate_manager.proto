// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.certificatemanager.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/certificatemanager/v1/certificate_issuance_config.proto";
import "google/cloud/certificatemanager/v1/trust_config.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CertificateManager.V1";
option go_package = "cloud.google.com/go/certificatemanager/apiv1/certificatemanagerpb;certificatemanagerpb";
option java_multiple_files = true;
option java_outer_classname = "CertificateManagerProto";
option java_package = "com.google.cloud.certificatemanager.v1";
option php_namespace = "Google\\Cloud\\CertificateManager\\V1";
option ruby_package = "Google::Cloud::CertificateManager::V1";

// API Overview
//
// Certificates Manager API allows customers to see and manage all their TLS
// certificates.
//
// Certificates Manager API service provides methods to manage certificates,
// group them into collections, and create serving configuration that can be
// easily applied to other Cloud resources e.g. Target Proxies.
//
// Data Model
//
// The Certificates Manager service exposes the following resources:
//
// * `Certificate` that describes a single TLS certificate.
// * `CertificateMap` that describes a collection of certificates that can be
// attached to a target resource.
// * `CertificateMapEntry` that describes a single configuration entry that
// consists of a SNI and a group of certificates. It's a subresource of
// CertificateMap.
//
// Certificate, CertificateMap and CertificateMapEntry IDs
// have to fully match the regexp `[a-z0-9-]{1,63}`. In other words,
// - only lower case letters, digits, and hyphen are allowed
// - length of the resource ID has to be in [1,63] range.
//
// Provides methods to manage Cloud Certificate Manager entities.
service CertificateManager {
  option (google.api.default_host) = "certificatemanager.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Certificates in a given project and location.
  rpc ListCertificates(ListCertificatesRequest)
      returns (ListCertificatesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/certificates"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Certificate.
  rpc GetCertificate(GetCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/certificates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Certificate in a given project and location.
  rpc CreateCertificate(CreateCertificateRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/certificates"
      body: "certificate"
    };
    option (google.api.method_signature) = "parent,certificate,certificate_id";
    option (google.longrunning.operation_info) = {
      response_type: "Certificate"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a Certificate.
  rpc UpdateCertificate(UpdateCertificateRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{certificate.name=projects/*/locations/*/certificates/*}"
      body: "certificate"
    };
    option (google.api.method_signature) = "certificate,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Certificate"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Certificate.
  rpc DeleteCertificate(DeleteCertificateRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/certificates/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists CertificateMaps in a given project and location.
  rpc ListCertificateMaps(ListCertificateMapsRequest)
      returns (ListCertificateMapsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/certificateMaps"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single CertificateMap.
  rpc GetCertificateMap(GetCertificateMapRequest) returns (CertificateMap) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/certificateMaps/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new CertificateMap in a given project and location.
  rpc CreateCertificateMap(CreateCertificateMapRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/certificateMaps"
      body: "certificate_map"
    };
    option (google.api.method_signature) =
        "parent,certificate_map,certificate_map_id";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateMap"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a CertificateMap.
  rpc UpdateCertificateMap(UpdateCertificateMapRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{certificate_map.name=projects/*/locations/*/certificateMaps/*}"
      body: "certificate_map"
    };
    option (google.api.method_signature) = "certificate_map,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateMap"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single CertificateMap. A Certificate Map can't be deleted
  // if it contains Certificate Map Entries. Remove all the entries from
  // the map before calling this method.
  rpc DeleteCertificateMap(DeleteCertificateMapRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/certificateMaps/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists CertificateMapEntries in a given project and location.
  rpc ListCertificateMapEntries(ListCertificateMapEntriesRequest)
      returns (ListCertificateMapEntriesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/certificateMaps/*}/certificateMapEntries"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single CertificateMapEntry.
  rpc GetCertificateMapEntry(GetCertificateMapEntryRequest)
      returns (CertificateMapEntry) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/certificateMaps/*/certificateMapEntries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new CertificateMapEntry in a given project and location.
  rpc CreateCertificateMapEntry(CreateCertificateMapEntryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/certificateMaps/*}/certificateMapEntries"
      body: "certificate_map_entry"
    };
    option (google.api.method_signature) =
        "parent,certificate_map_entry,certificate_map_entry_id";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateMapEntry"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a CertificateMapEntry.
  rpc UpdateCertificateMapEntry(UpdateCertificateMapEntryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{certificate_map_entry.name=projects/*/locations/*/certificateMaps/*/certificateMapEntries/*}"
      body: "certificate_map_entry"
    };
    option (google.api.method_signature) = "certificate_map_entry,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateMapEntry"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single CertificateMapEntry.
  rpc DeleteCertificateMapEntry(DeleteCertificateMapEntryRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/certificateMaps/*/certificateMapEntries/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists DnsAuthorizations in a given project and location.
  rpc ListDnsAuthorizations(ListDnsAuthorizationsRequest)
      returns (ListDnsAuthorizationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/dnsAuthorizations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single DnsAuthorization.
  rpc GetDnsAuthorization(GetDnsAuthorizationRequest)
      returns (DnsAuthorization) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/dnsAuthorizations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new DnsAuthorization in a given project and location.
  rpc CreateDnsAuthorization(CreateDnsAuthorizationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/dnsAuthorizations"
      body: "dns_authorization"
    };
    option (google.api.method_signature) =
        "parent,dns_authorization,dns_authorization_id";
    option (google.longrunning.operation_info) = {
      response_type: "DnsAuthorization"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a DnsAuthorization.
  rpc UpdateDnsAuthorization(UpdateDnsAuthorizationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{dns_authorization.name=projects/*/locations/*/dnsAuthorizations/*}"
      body: "dns_authorization"
    };
    option (google.api.method_signature) = "dns_authorization,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "DnsAuthorization"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single DnsAuthorization.
  rpc DeleteDnsAuthorization(DeleteDnsAuthorizationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/dnsAuthorizations/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists CertificateIssuanceConfigs in a given project and location.
  rpc ListCertificateIssuanceConfigs(ListCertificateIssuanceConfigsRequest)
      returns (ListCertificateIssuanceConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/certificateIssuanceConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single CertificateIssuanceConfig.
  rpc GetCertificateIssuanceConfig(GetCertificateIssuanceConfigRequest)
      returns (CertificateIssuanceConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/certificateIssuanceConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new CertificateIssuanceConfig in a given project and location.
  rpc CreateCertificateIssuanceConfig(CreateCertificateIssuanceConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/certificateIssuanceConfigs"
      body: "certificate_issuance_config"
    };
    option (google.api.method_signature) =
        "parent,certificate_issuance_config,certificate_issuance_config_id";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateIssuanceConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single CertificateIssuanceConfig.
  rpc DeleteCertificateIssuanceConfig(DeleteCertificateIssuanceConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/certificateIssuanceConfigs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists TrustConfigs in a given project and location.
  rpc ListTrustConfigs(ListTrustConfigsRequest)
      returns (ListTrustConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/trustConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single TrustConfig.
  rpc GetTrustConfig(GetTrustConfigRequest) returns (TrustConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/trustConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new TrustConfig in a given project and location.
  rpc CreateTrustConfig(CreateTrustConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/trustConfigs"
      body: "trust_config"
    };
    option (google.api.method_signature) =
        "parent,trust_config,trust_config_id";
    option (google.longrunning.operation_info) = {
      response_type: "TrustConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a TrustConfig.
  rpc UpdateTrustConfig(UpdateTrustConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{trust_config.name=projects/*/locations/*/trustConfigs/*}"
      body: "trust_config"
    };
    option (google.api.method_signature) = "trust_config,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "TrustConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single TrustConfig.
  rpc DeleteTrustConfig(DeleteTrustConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/trustConfigs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Request for the `ListCertificates` method.
message ListCertificatesRequest {
  // Required. The project and location from which the certificate should be
  // listed, specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of certificates to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListCertificatesResponse`. Indicates that
  // this is a continuation of a prior `ListCertificates` call, and that the
  // system should return the next page of data.
  string page_token = 3;

  // Filter expression to restrict the Certificates returned.
  string filter = 4;

  // A list of Certificate field names used to specify the order of the returned
  // results. The default sorting order is ascending. To specify descending
  // order for a field, add a suffix `" desc"`.
  string order_by = 5;
}

// Response for the `ListCertificates` method.
message ListCertificatesResponse {
  // A list of certificates for the parent resource.
  repeated Certificate certificates = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;

  // A list of locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for the `GetCertificate` method.
message GetCertificateRequest {
  // Required. A name of the certificate to describe. Must be in the format
  // `projects/*/locations/*/certificates/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/Certificate"
    }
  ];
}

// Request for the `CreateCertificate` method.
message CreateCertificateRequest {
  // Required. The parent resource of the certificate. Must be in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. A user-provided name of the certificate.
  string certificate_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A definition of the certificate to create.
  Certificate certificate = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `UpdateCertificate` method.
message UpdateCertificateRequest {
  // Required. A definition of the certificate to update.
  Certificate certificate = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource. For the `FieldMask`
  // definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `DeleteCertificate` method.
message DeleteCertificateRequest {
  // Required. A name of the certificate to delete. Must be in the format
  // `projects/*/locations/*/certificates/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/Certificate"
    }
  ];
}

// Request for the `ListCertificateMaps` method.
message ListCertificateMapsRequest {
  // Required. The project and location from which the certificate maps should
  // be listed, specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of certificate maps to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListCertificateMapsResponse`. Indicates
  // that this is a continuation of a prior `ListCertificateMaps` call, and that
  // the system should return the next page of data.
  string page_token = 3;

  // Filter expression to restrict the Certificates Maps returned.
  string filter = 4;

  // A list of Certificate Map field names used to specify the order of the
  // returned results. The default sorting order is ascending. To specify
  // descending order for a field, add a suffix `" desc"`.
  string order_by = 5;
}

// Response for the `ListCertificateMaps` method.
message ListCertificateMapsResponse {
  // A list of certificate maps for the parent resource.
  repeated CertificateMap certificate_maps = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for the `GetCertificateMap` method.
message GetCertificateMapRequest {
  // Required. A name of the certificate map to describe. Must be in the format
  // `projects/*/locations/*/certificateMaps/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/CertificateMap"
    }
  ];
}

// Request for the `CreateCertificateMap` method.
message CreateCertificateMapRequest {
  // Required. The parent resource of the certificate map. Must be in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. A user-provided name of the certificate map.
  string certificate_map_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A definition of the certificate map to create.
  CertificateMap certificate_map = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `UpdateCertificateMap` method.
message UpdateCertificateMapRequest {
  // Required. A definition of the certificate map to update.
  CertificateMap certificate_map = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource. For the `FieldMask`
  // definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `DeleteCertificateMap` method.
message DeleteCertificateMapRequest {
  // Required. A name of the certificate map to delete. Must be in the format
  // `projects/*/locations/*/certificateMaps/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/CertificateMap"
    }
  ];
}

// Request for the `ListCertificateMapEntries` method.
message ListCertificateMapEntriesRequest {
  // Required. The project, location and certificate map from which the
  // certificate map entries should be listed, specified in the format
  // `projects/*/locations/*/certificateMaps/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/CertificateMap"
    }
  ];

  // Maximum number of certificate map entries to return. The service may return
  // fewer than this value.
  // If unspecified, at most 50 certificate map entries will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // The value returned by the last `ListCertificateMapEntriesResponse`.
  // Indicates that this is a continuation of a prior
  // `ListCertificateMapEntries` call, and that the system should return the
  // next page of data.
  string page_token = 3;

  // Filter expression to restrict the returned Certificate Map Entries.
  string filter = 4;

  // A list of Certificate Map Entry field names used to specify
  // the order of the returned results. The default sorting order is ascending.
  // To specify descending order for a field, add a suffix `" desc"`.
  string order_by = 5;
}

// Response for the `ListCertificateMapEntries` method.
message ListCertificateMapEntriesResponse {
  // A list of certificate map entries for the parent resource.
  repeated CertificateMapEntry certificate_map_entries = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for the `GetCertificateMapEntry` method.
message GetCertificateMapEntryRequest {
  // Required. A name of the certificate map entry to describe. Must be in the
  // format `projects/*/locations/*/certificateMaps/*/certificateMapEntries/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/CertificateMapEntry"
    }
  ];
}

// Request for the `CreateCertificateMapEntry` method.
message CreateCertificateMapEntryRequest {
  // Required. The parent resource of the certificate map entry. Must be in the
  // format `projects/*/locations/*/certificateMaps/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/CertificateMap"
    }
  ];

  // Required. A user-provided name of the certificate map entry.
  string certificate_map_entry_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A definition of the certificate map entry to create.
  CertificateMapEntry certificate_map_entry = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `UpdateCertificateMapEntry` method.
message UpdateCertificateMapEntryRequest {
  // Required. A definition of the certificate map entry to create map entry.
  CertificateMapEntry certificate_map_entry = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource. For the `FieldMask`
  // definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `DeleteCertificateMapEntry` method.
message DeleteCertificateMapEntryRequest {
  // Required. A name of the certificate map entry to delete. Must be in the
  // format `projects/*/locations/*/certificateMaps/*/certificateMapEntries/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/CertificateMapEntry"
    }
  ];
}

// Request for the `ListDnsAuthorizations` method.
message ListDnsAuthorizationsRequest {
  // Required. The project and location from which the dns authorizations should
  // be listed, specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of dns authorizations to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListDnsAuthorizationsResponse`. Indicates
  // that this is a continuation of a prior `ListDnsAuthorizations` call, and
  // that the system should return the next page of data.
  string page_token = 3;

  // Filter expression to restrict the Dns Authorizations returned.
  string filter = 4;

  // A list of Dns Authorization field names used to specify the order of the
  // returned results. The default sorting order is ascending. To specify
  // descending order for a field, add a suffix `" desc"`.
  string order_by = 5;
}

// Response for the `ListDnsAuthorizations` method.
message ListDnsAuthorizationsResponse {
  // A list of dns authorizations for the parent resource.
  repeated DnsAuthorization dns_authorizations = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for the `GetDnsAuthorization` method.
message GetDnsAuthorizationRequest {
  // Required. A name of the dns authorization to describe. Must be in the
  // format `projects/*/locations/*/dnsAuthorizations/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/DnsAuthorization"
    }
  ];
}

// Request for the `CreateDnsAuthorization` method.
message CreateDnsAuthorizationRequest {
  // Required. The parent resource of the dns authorization. Must be in the
  // format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. A user-provided name of the dns authorization.
  string dns_authorization_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A definition of the dns authorization to create.
  DnsAuthorization dns_authorization = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `UpdateDnsAuthorization` method.
message UpdateDnsAuthorizationRequest {
  // Required. A definition of the dns authorization to update.
  DnsAuthorization dns_authorization = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource. For the `FieldMask`
  // definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `DeleteDnsAuthorization` method.
message DeleteDnsAuthorizationRequest {
  // Required. A name of the dns authorization to delete. Must be in the format
  // `projects/*/locations/*/dnsAuthorizations/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/DnsAuthorization"
    }
  ];
}

// Represents the metadata of the long-running operation. Output only.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation, if any.
  string status_message = 5;

  // Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6;

  // API version used to start the operation.
  string api_version = 7;
}

// Defines TLS certificate.
message Certificate {
  option (google.api.resource) = {
    type: "certificatemanager.googleapis.com/Certificate"
    pattern: "projects/{project}/locations/{location}/certificates/{certificate}"
  };

  // Certificate data for a SelfManaged Certificate.
  // SelfManaged Certificates are uploaded by the user. Updating such
  // certificates before they expire remains the user's responsibility.
  message SelfManagedCertificate {
    // Input only. The PEM-encoded certificate chain.
    // Leaf certificate comes first, followed by intermediate ones if any.
    string pem_certificate = 1 [(google.api.field_behavior) = INPUT_ONLY];

    // Input only. The PEM-encoded private key of the leaf certificate.
    string pem_private_key = 2 [(google.api.field_behavior) = INPUT_ONLY];
  }

  // Configuration and state of a Managed Certificate.
  // Certificate Manager provisions and renews Managed Certificates
  // automatically, for as long as it's authorized to do so.
  message ManagedCertificate {
    // State of the managed certificate resource.
    enum State {
      // State is unspecified.
      STATE_UNSPECIFIED = 0;

      // Certificate Manager attempts to provision or renew the certificate.
      // If the process takes longer than expected, consult the
      // `provisioning_issue` field.
      PROVISIONING = 1;

      // Multiple certificate provisioning attempts failed and Certificate
      // Manager gave up. To try again, delete and create a new managed
      // Certificate resource.
      // For details see the `provisioning_issue` field.
      FAILED = 2;

      // The certificate management is working, and a certificate has been
      // provisioned.
      ACTIVE = 3;
    }

    // Information about issues with provisioning a Managed Certificate.
    message ProvisioningIssue {
      // Reason for provisioning failures.
      enum Reason {
        // Reason is unspecified.
        REASON_UNSPECIFIED = 0;

        // Certificate provisioning failed due to an issue with one or more of
        // the domains on the certificate.
        // For details of which domains failed, consult the
        // `authorization_attempt_info` field.
        AUTHORIZATION_ISSUE = 1;

        // Exceeded Certificate Authority quotas or internal rate limits of the
        // system. Provisioning may take longer to complete.
        RATE_LIMITED = 2;
      }

      // Output only. Reason for provisioning failures.
      Reason reason = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Human readable explanation about the issue. Provided to
      // help address the configuration issues. Not guaranteed to be stable. For
      // programmatic access use Reason enum.
      string details = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // State of the latest attempt to authorize a domain for certificate
    // issuance.
    message AuthorizationAttemptInfo {
      // State of the domain for managed certificate issuance.
      enum State {
        // State is unspecified.
        STATE_UNSPECIFIED = 0;

        // Certificate provisioning for this domain is under way. Google Cloud
        // will attempt to authorize the domain.
        AUTHORIZING = 1;

        // A managed certificate can be provisioned, no issues for this domain.
        AUTHORIZED = 6;

        // Attempt to authorize the domain failed. This prevents the Managed
        // Certificate from being issued.
        // See `failure_reason` and `details` fields for more information.
        FAILED = 7;
      }

      // Reason for failure of the authorization attempt for the domain.
      enum FailureReason {
        // FailureReason is unspecified.
        FAILURE_REASON_UNSPECIFIED = 0;

        // There was a problem with the user's DNS or load balancer
        // configuration for this domain.
        CONFIG = 1;

        // Certificate issuance forbidden by an explicit CAA record for the
        // domain or a failure to check CAA records for the domain.
        CAA = 2;

        // Reached a CA or internal rate-limit for the domain,
        // e.g. for certificates per top-level private domain.
        RATE_LIMITED = 3;
      }

      // Domain name of the authorization attempt.
      string domain = 1;

      // Output only. State of the domain for managed certificate issuance.
      State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Reason for failure of the authorization attempt for the
      // domain.
      FailureReason failure_reason = 3
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Human readable explanation for reaching the state.
      // Provided to help address the configuration issues. Not guaranteed to be
      // stable. For programmatic access use FailureReason enum.
      string details = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Immutable. The domains for which a managed SSL certificate will be
    // generated. Wildcard domains are only supported with DNS challenge
    // resolution.
    repeated string domains = 1 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Authorizations that will be used for performing domain
    // authorization.
    repeated string dns_authorizations = 2 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.resource_reference) = {
        type: "certificatemanager.googleapis.com/DnsAuthorization"
      }
    ];

    // Immutable. The resource name for a
    // [CertificateIssuanceConfig][google.cloud.certificatemanager.v1.CertificateIssuanceConfig]
    // used to configure private PKI certificates in the format
    // `projects/*/locations/*/certificateIssuanceConfigs/*`.
    // If this field is not set, the certificates will instead be publicly
    // signed as documented at
    // https://cloud.google.com/load-balancing/docs/ssl-certificates/google-managed-certs#caa.
    string issuance_config = 6 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.resource_reference) = {
        type: "certificatemanager.googleapis.com/CertificateIssuanceConfig"
      }
    ];

    // Output only. State of the managed certificate resource.
    State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Information about issues with provisioning a Managed
    // Certificate.
    ProvisioningIssue provisioning_issue = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Detailed state of the latest authorization attempt for each
    // domain specified for managed certificate resource.
    repeated AuthorizationAttemptInfo authorization_attempt_info = 5
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Certificate scope.
  enum Scope {
    // Certificates with default scope are served from core Google data centers.
    // If unsure, choose this option.
    DEFAULT = 0;

    // Certificates with scope EDGE_CACHE are special-purposed certificates,
    // served from Edge Points of Presence.
    // See https://cloud.google.com/vpc/docs/edge-locations.
    EDGE_CACHE = 1;

    // Certificates with ALL_REGIONS scope are served from all Google Cloud
    // regions. See https://cloud.google.com/compute/docs/regions-zones.
    ALL_REGIONS = 2;
  }

  // A user-defined name of the certificate. Certificate names must be unique
  // globally and match pattern `projects/*/locations/*/certificates/*`.
  string name = 1;

  // One or more paragraphs of text description of a certificate.
  string description = 8;

  // Output only. The creation timestamp of a Certificate.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of a Certificate.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Set of labels associated with a Certificate.
  map<string, string> labels = 4;

  oneof type {
    // If set, defines data of a self-managed certificate.
    SelfManagedCertificate self_managed = 5;

    // If set, contains configuration and state of a managed certificate.
    ManagedCertificate managed = 11;
  }

  // Output only. The list of Subject Alternative Names of dnsName type defined
  // in the certificate (see RFC 5280 *******). Managed certificates that
  // haven't been provisioned yet have this field populated with a value of the
  // managed.domains field.
  repeated string san_dnsnames = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The PEM-encoded certificate chain.
  string pem_certificate = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The expiry timestamp of a Certificate.
  google.protobuf.Timestamp expire_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The scope of the certificate.
  Scope scope = 12 [(google.api.field_behavior) = IMMUTABLE];
}

// Defines a collection of certificate configurations.
message CertificateMap {
  option (google.api.resource) = {
    type: "certificatemanager.googleapis.com/CertificateMap"
    pattern: "projects/{project}/locations/{location}/certificateMaps/{certificate_map}"
  };

  // Describes a Target Proxy that uses this Certificate Map.
  message GclbTarget {
    // Defines IP configuration where this Certificate Map is serving.
    message IpConfig {
      // Output only. An external IP address.
      string ip_address = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Ports.
      repeated uint32 ports = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // A Target Proxy to which this map is attached to.
    oneof target_proxy {
      // Output only. This field returns the resource name in the following
      // format:
      // `//compute.googleapis.com/projects/*/global/targetHttpsProxies/*`.
      string target_https_proxy = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. This field returns the resource name in the following
      // format:
      // `//compute.googleapis.com/projects/*/global/targetSslProxies/*`.
      string target_ssl_proxy = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Output only. IP configurations for this Target Proxy where the
    // Certificate Map is serving.
    repeated IpConfig ip_configs = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // A user-defined name of the Certificate Map. Certificate Map names must be
  // unique globally and match pattern
  // `projects/*/locations/*/certificateMaps/*`.
  string name = 1;

  // One or more paragraphs of text description of a certificate map.
  string description = 5;

  // Output only. The creation timestamp of a Certificate Map.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp of a Certificate Map.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Set of labels associated with a Certificate Map.
  map<string, string> labels = 3;

  // Output only. A list of GCLB targets that use this Certificate Map.
  // A Target Proxy is only present on this list if it's attached to a
  // Forwarding Rule.
  repeated GclbTarget gclb_targets = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines a certificate map entry.
message CertificateMapEntry {
  option (google.api.resource) = {
    type: "certificatemanager.googleapis.com/CertificateMapEntry"
    pattern: "projects/{project}/locations/{location}/certificateMaps/{certificate_map}/certificateMapEntries/{certificate_map_entry}"
  };

  // Defines predefined cases other than SNI-hostname match when this
  // configuration should be applied.
  enum Matcher {
    // A matcher has't been recognized.
    MATCHER_UNSPECIFIED = 0;

    // A primary certificate that is served when SNI wasn't specified in the
    // request or SNI couldn't be found in the map.
    PRIMARY = 1;
  }

  // A user-defined name of the Certificate Map Entry. Certificate Map Entry
  // names must be unique globally and match pattern
  // `projects/*/locations/*/certificateMaps/*/certificateMapEntries/*`.
  string name = 1;

  // One or more paragraphs of text description of a certificate map entry.
  string description = 9;

  // Output only. The creation timestamp of a Certificate Map Entry.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp of a Certificate Map Entry.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Set of labels associated with a Certificate Map Entry.
  map<string, string> labels = 4;

  oneof match {
    // A Hostname (FQDN, e.g. `example.com`) or a wildcard hostname expression
    // (`*.example.com`) for a set of hostnames with common suffix. Used as
    // Server Name Indication (SNI) for selecting a proper certificate.
    string hostname = 5;

    // A predefined matcher for particular cases, other than SNI selection.
    Matcher matcher = 10;
  }

  // A set of Certificates defines for the given `hostname`. There can be
  // defined up to four certificates in each Certificate Map Entry. Each
  // certificate must match pattern `projects/*/locations/*/certificates/*`.
  repeated string certificates = 7 [(google.api.resource_reference) = {
    type: "certificatemanager.googleapis.com/Certificate"
  }];

  // Output only. A serving state of this Certificate Map Entry.
  ServingState state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A DnsAuthorization resource describes a way to perform domain authorization
// for certificate issuance.
message DnsAuthorization {
  option (google.api.resource) = {
    type: "certificatemanager.googleapis.com/DnsAuthorization"
    pattern: "projects/{project}/locations/{location}/dnsAuthorizations/{dns_authorization}"
  };

  // The structure describing the DNS Resource Record that needs to be added
  // to DNS configuration for the authorization to be usable by
  // certificate.
  message DnsResourceRecord {
    // Output only. Fully qualified name of the DNS Resource Record.
    // e.g. `_acme-challenge.example.com`
    string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Type of the DNS Resource Record.
    // Currently always set to "CNAME".
    string type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Data of the DNS Resource Record.
    string data = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // DnsAuthorization type.
  enum Type {
    // Type is unspecified.
    TYPE_UNSPECIFIED = 0;

    // FIXED_RECORD DNS authorization uses DNS-01 validation method.
    FIXED_RECORD = 1;

    // PER_PROJECT_RECORD DNS authorization allows for independent management
    // of Google-managed certificates with DNS authorization across multiple
    // projects.
    PER_PROJECT_RECORD = 2;
  }

  // A user-defined name of the dns authorization. DnsAuthorization names must
  // be unique globally and match pattern
  // `projects/*/locations/*/dnsAuthorizations/*`.
  string name = 1;

  // Output only. The creation timestamp of a DnsAuthorization.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of a DnsAuthorization.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Set of labels associated with a DnsAuthorization.
  map<string, string> labels = 4;

  // One or more paragraphs of text description of a DnsAuthorization.
  string description = 5;

  // Required. Immutable. A domain that is being authorized. A DnsAuthorization
  // resource covers a single domain and its wildcard, e.g. authorization for
  // `example.com` can be used to issue certificates for `example.com` and
  // `*.example.com`.
  string domain = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. DNS Resource Record that needs to be added to DNS
  // configuration.
  DnsResourceRecord dns_resource_record = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Type of DnsAuthorization. If unset during resource creation the
  // following default will be used:
  // - in location global: FIXED_RECORD.
  Type type = 11 [(google.api.field_behavior) = IMMUTABLE];
}

// Defines set of serving states associated with a resource.
enum ServingState {
  // The status is undefined.
  SERVING_STATE_UNSPECIFIED = 0;

  // The configuration is serving.
  ACTIVE = 1;

  // Update is in progress. Some frontends may serve this configuration.
  PENDING = 2;
}
