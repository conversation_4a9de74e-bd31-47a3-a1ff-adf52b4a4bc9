{"methodConfig": [{"name": [{"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "ListCertificates"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "GetCertificate"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "CreateCertificate"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "UpdateCertificate"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "DeleteCertificate"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "ListCertificateMaps"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "GetCertificateMap"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "CreateCertificateMap"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "UpdateCertificateMap"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "DeleteCertificateMap"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "ListCertificateMapEntries"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "GetCertificateMapEntry"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "CreateCertificateMapEntry"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "UpdateCertificateMapEntry"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "DeleteCertificateMapEntry"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "ListDnsAuthorizations"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "GetDnsAuthorization"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "CreateDnsAuthorization"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "UpdateDnsAuthorization"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "DeleteDnsAuthorization"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "GetCertificateIssuanceConfig"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "ListCertificateIssuanceConfigs"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "CreateCertificateIssuanceConfig"}, {"service": "google.cloud.certificatemanager.v1.CertificateManager", "method": "DeleteCertificateIssuanceConfig"}, {"service": "google.longrunning.Operations", "method": "ListOperations"}, {"service": "google.longrunning.Operations", "method": "CancelOperation"}, {"service": "google.longrunning.Operations", "method": "DeleteOperation"}, {"service": "google.cloud.location.Locations", "method": "GetLocation"}, {"service": "google.cloud.location.Locations", "method": "ListLocations"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.longrunning.Operations", "method": "GetOperation"}], "timeout": "5s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.200s", "maxBackoff": "3s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}