// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.certificatemanager.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CertificateManager.V1";
option go_package = "cloud.google.com/go/certificatemanager/apiv1/certificatemanagerpb;certificatemanagerpb";
option java_multiple_files = true;
option java_outer_classname = "CertificateIssuanceConfigProto";
option java_package = "com.google.cloud.certificatemanager.v1";
option php_namespace = "Google\\Cloud\\CertificateManager\\V1";
option ruby_package = "Google::Cloud::CertificateManager::V1";
option (google.api.resource_definition) = {
  type: "privateca.googleapis.com/CaPool"
  pattern: "projects/{project}/locations/{location}/caPools/{ca_pool}"
};

// Request for the `ListCertificateIssuanceConfigs` method.
message ListCertificateIssuanceConfigsRequest {
  // Required. The project and location from which the certificate should be
  // listed, specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of certificate configs to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListCertificateIssuanceConfigsResponse`.
  // Indicates that this is a continuation of a prior
  // `ListCertificateIssuanceConfigs` call, and that the system should return
  // the next page of data.
  string page_token = 3;

  // Filter expression to restrict the Certificates Configs returned.
  string filter = 4;

  // A list of Certificate Config field names used to specify the order of the
  // returned results. The default sorting order is ascending. To specify
  // descending order for a field, add a suffix `" desc"`.
  string order_by = 5;
}

// Response for the `ListCertificateIssuanceConfigs` method.
message ListCertificateIssuanceConfigsResponse {
  // A list of certificate configs for the parent resource.
  repeated CertificateIssuanceConfig certificate_issuance_configs = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for the `GetCertificateIssuanceConfig` method.
message GetCertificateIssuanceConfigRequest {
  // Required. A name of the certificate issuance config to describe. Must be in
  // the format `projects/*/locations/*/certificateIssuanceConfigs/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/CertificateIssuanceConfig"
    }
  ];
}

// Request for the `CreateCertificateIssuanceConfig` method.
message CreateCertificateIssuanceConfigRequest {
  // Required. The parent resource of the certificate issuance config. Must be
  // in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. A user-provided name of the certificate config.
  string certificate_issuance_config_id = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. A definition of the certificate issuance config to create.
  CertificateIssuanceConfig certificate_issuance_config = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `DeleteCertificateIssuanceConfig` method.
message DeleteCertificateIssuanceConfigRequest {
  // Required. A name of the certificate issuance config to delete. Must be in
  // the format `projects/*/locations/*/certificateIssuanceConfigs/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "certificatemanager.googleapis.com/CertificateIssuanceConfig"
    }
  ];
}

// CertificateIssuanceConfig specifies how to issue and manage a certificate.
message CertificateIssuanceConfig {
  option (google.api.resource) = {
    type: "certificatemanager.googleapis.com/CertificateIssuanceConfig"
    pattern: "projects/{project}/locations/{location}/certificateIssuanceConfigs/{certificate_issuance_config}"
  };

  // The CA that issues the workload certificate. It includes CA address, type,
  // authentication to CA service, etc.
  message CertificateAuthorityConfig {
    // Contains information required to contact CA service.
    message CertificateAuthorityServiceConfig {
      // Required. A CA pool resource used to issue a certificate.
      // The CA pool string has a relative resource path following the form
      // "projects/{project}/locations/{location}/caPools/{ca_pool}".
      string ca_pool = 1 [
        (google.api.field_behavior) = REQUIRED,
        (google.api.resource_reference) = {
          type: "privateca.googleapis.com/CaPool"
        }
      ];
    }

    oneof kind {
      // Defines a CertificateAuthorityServiceConfig.
      CertificateAuthorityServiceConfig certificate_authority_service_config =
          1;
    }
  }

  // The type of keypair to generate.
  enum KeyAlgorithm {
    // Unspecified key algorithm.
    KEY_ALGORITHM_UNSPECIFIED = 0;

    // Specifies RSA with a 2048-bit modulus.
    RSA_2048 = 1;

    // Specifies ECDSA with curve P256.
    ECDSA_P256 = 4;
  }

  // A user-defined name of the certificate issuance config.
  // CertificateIssuanceConfig names must be unique globally and match pattern
  // `projects/*/locations/*/certificateIssuanceConfigs/*`.
  string name = 1;

  // Output only. The creation timestamp of a CertificateIssuanceConfig.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of a CertificateIssuanceConfig.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Set of labels associated with a CertificateIssuanceConfig.
  map<string, string> labels = 4;

  // One or more paragraphs of text description of a CertificateIssuanceConfig.
  string description = 5;

  // Required. The CA that issues the workload certificate. It includes the CA
  // address, type, authentication to CA service, etc.
  CertificateAuthorityConfig certificate_authority_config = 6
      [(google.api.field_behavior) = REQUIRED];

  // Required. Workload certificate lifetime requested.
  google.protobuf.Duration lifetime = 7
      [(google.api.field_behavior) = REQUIRED];

  // Required. Specifies the percentage of elapsed time of the certificate
  // lifetime to wait before renewing the certificate. Must be a number between
  // 1-99, inclusive.
  int32 rotation_window_percentage = 8 [(google.api.field_behavior) = REQUIRED];

  // Required. The key algorithm to use when generating the private key.
  KeyAlgorithm key_algorithm = 9 [(google.api.field_behavior) = REQUIRED];
}
