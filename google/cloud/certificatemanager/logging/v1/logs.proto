// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.certificatemanager.logging.v1;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CertificateManager.Logging.V1";
option go_package = "cloud.google.com/go/certificatemanager/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LogsProto";
option java_package = "com.google.cloud.certificatemanager.logging.v1";
option php_namespace = "Google\\Cloud\\CertificateManager\\Logging\\V1";
option ruby_package = "Google::Cloud::CertificateManager::Logging::V1";

// Log message which notifies about expired or close to
// expiry certificates.
message CertificatesExpiry {
  // Expiration state of the certificate.
  enum State {
    // Unspecified state, should never be reported.
    STATE_UNSPECIFIED = 0;

    // Certificate will expire soon.
    CLOSE_TO_EXPIRY = 1;

    // Certificate is expired.
    EXPIRED = 2;
  }

  // Number of reported certificates.
  int64 count = 1;

  // Names of reported certificates. If there are too many, the list is sampled.
  repeated string certificates = 2;

  // State of reported certificates.
  State state = 3;

  // Approximated expire time of reported certificates.
  // Multiple certificates with close expire time are batched together in a
  // single log, so the timestamp is not precise.
  google.protobuf.Timestamp expire_time = 4;
}
