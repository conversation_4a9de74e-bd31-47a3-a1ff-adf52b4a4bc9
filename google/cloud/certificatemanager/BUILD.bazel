# This build file includes a target for the Ruby wrapper library for
# google-cloud-certificate_manager.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for certificatemanager.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "certificatemanager_ruby_wrapper",
    srcs = ["//google/cloud/certificatemanager/v1:certificatemanager_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-certificate_manager",
        "ruby-cloud-wrapper-of=v1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/certificate-manager",
        "ruby-cloud-api-id=certificatemanager.googleapis.com",
        "ruby-cloud-api-shortname=certificatemanager",
    ],
    ruby_cloud_description = "Certificate Manager lets you acquire and manage Transport Layer Security (TLS) (SSL) certificates for use with classic external HTTP(S) load balancers in Google Cloud.",
    ruby_cloud_title = "Certificate Manager",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-certificatemanager-ruby",
    deps = [
        ":certificatemanager_ruby_wrapper",
    ],
)
