// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkemulticloud.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeMultiCloud.V1";
option go_package = "cloud.google.com/go/gkemulticloud/apiv1/gkemulticloudpb;gkemulticloudpb";
option java_multiple_files = true;
option java_outer_classname = "CommonResourcesProto";
option java_package = "com.google.cloud.gkemulticloud.v1";
option php_namespace = "Google\\Cloud\\GkeMultiCloud\\V1";
option ruby_package = "Google::Cloud::GkeMultiCloud::V1";

// Jwk is a JSON Web Key as specified in RFC 7517.
message Jwk {
  // Key Type.
  string kty = 1;

  // Algorithm.
  string alg = 2;

  // Permitted uses for the public keys.
  string use = 3;

  // Key ID.
  string kid = 4;

  // Used for RSA keys.
  string n = 5;

  // Used for RSA keys.
  string e = 6;

  // Used for ECDSA keys.
  string x = 7;

  // Used for ECDSA keys.
  string y = 8;

  // Used for ECDSA keys.
  string crv = 9;
}

// Workload Identity settings.
message WorkloadIdentityConfig {
  // The OIDC issuer URL for this cluster.
  string issuer_uri = 1;

  // The Workload Identity Pool associated to the cluster.
  string workload_pool = 2;

  // The ID of the OIDC Identity Provider (IdP) associated to the Workload
  // Identity Pool.
  string identity_provider = 3;
}

// Constraints applied to pods.
message MaxPodsConstraint {
  // Required. The maximum number of pods to schedule on a single node.
  int64 max_pods_per_node = 1 [(google.api.field_behavior) = REQUIRED];
}

// Metadata about a long-running operation.
message OperationMetadata {
  // Output only. The time at which this operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this operation was completed.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the resource associated to this operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_detail = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of any error that occurred during the
  // operation.
  string error_detail = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The verb associated with the API method which triggered this
  // operation. Possible values are "create", "delete", "update" and "import".
  string verb = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether it has been requested cancellation
  // for the operation. Operations that have successfully been cancelled
  // have
  // [google.longrunning.Operation.error][google.longrunning.Operation.error]
  // value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The taint content for the node taint.
message NodeTaint {
  // The taint effect.
  enum Effect {
    // Not set.
    EFFECT_UNSPECIFIED = 0;

    // Do not allow new pods to schedule onto the node unless they tolerate the
    // taint, but allow all pods submitted to Kubelet without going through the
    // scheduler to start, and allow all already-running pods to continue
    // running. Enforced by the scheduler.
    NO_SCHEDULE = 1;

    // Like TaintEffectNoSchedule, but the scheduler tries not to schedule
    // new pods onto the node, rather than prohibiting new pods from scheduling
    // onto the node entirely. Enforced by the scheduler.
    PREFER_NO_SCHEDULE = 2;

    // Evict any already-running pods that do not tolerate the taint.
    // Currently enforced by NodeController.
    NO_EXECUTE = 3;
  }

  // Required. Key for the taint.
  string key = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Value for the taint.
  string value = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The taint effect.
  Effect effect = 3 [(google.api.field_behavior) = REQUIRED];
}

// Configuration for node pool kubelet options.
message NodeKubeletConfig {
  // Optional. Enable the insecure kubelet read only port.
  bool insecure_kubelet_readonly_port_enabled = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Control the CPU management policy on the node.
  // See
  // https://kubernetes.io/docs/tasks/administer-cluster/cpu-management-policies/
  //
  // The following values are allowed.
  // * "none": the default, which represents the existing scheduling behavior.
  // * "static": allows pods with certain resource characteristics to be granted
  // increased CPU affinity and exclusivity on the node.
  // The default value is 'none' if unspecified.
  optional string cpu_manager_policy = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Enable CPU CFS quota enforcement for containers that specify CPU
  // limits.
  //
  // This option is enabled by default which makes kubelet use CFS quota
  // (https://www.kernel.org/doc/Documentation/scheduler/sched-bwc.txt) to
  // enforce container CPU limits. Otherwise, CPU limits will not be enforced at
  // all.
  //
  // Disable this option to mitigate CPU throttling problems while still having
  // your pods to be in Guaranteed QoS class by specifying the CPU limits.
  //
  // The default value is 'true' if unspecified.
  optional bool cpu_cfs_quota = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set the CPU CFS quota period value 'cpu.cfs_period_us'.
  //
  // The string must be a sequence of decimal numbers, each with optional
  // fraction and a unit suffix, such as "300ms".
  // Valid time units are "ns", "us" (or "µs"), "ms", "s", "m", "h".
  // The value must be a positive duration.
  //
  // The default value is '100ms' if unspecified.
  optional string cpu_cfs_quota_period = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set the Pod PID limits. See
  // https://kubernetes.io/docs/concepts/policy/pid-limiting/#pod-pid-limits
  //
  // Controls the maximum number of processes allowed to run in a pod. The value
  // must be greater than or equal to 1024 and less than 4194304.
  optional int64 pod_pids_limit = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Fleet related configuration.
//
// Fleets are a Google Cloud concept for logically organizing clusters,
// letting you use and manage multi-cluster capabilities and apply
// consistent policies across your systems.
//
// See [Anthos
// Fleets](https://cloud.google.com/anthos/multicluster-management/fleets) for
// more details on Anthos multi-cluster capabilities using Fleets.
message Fleet {
  // Required. The name of the Fleet host project where this cluster will be
  // registered.
  //
  // Project names are formatted as
  // `projects/<project-number>`.
  string project = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The name of the managed Hub Membership resource associated to
  // this cluster.
  //
  // Membership names are formatted as
  // `projects/<project-number>/locations/global/membership/<cluster-id>`.
  string membership = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Parameters that describe the Logging configuration in a cluster.
message LoggingConfig {
  // The configuration of the logging components;
  LoggingComponentConfig component_config = 1;
}

// Parameters that describe the Logging component configuration in a cluster.
message LoggingComponentConfig {
  // The components of the logging configuration;
  enum Component {
    // No component is specified
    COMPONENT_UNSPECIFIED = 0;

    // This indicates that system logging components is enabled.
    SYSTEM_COMPONENTS = 1;

    // This indicates that user workload logging component is enabled.
    WORKLOADS = 2;
  }

  // The components to be enabled.
  repeated Component enable_components = 1;
}

// Parameters that describe the Monitoring configuration in a cluster.
message MonitoringConfig {
  // Enable Google Cloud Managed Service for Prometheus in the cluster.
  ManagedPrometheusConfig managed_prometheus_config = 2;

  // Optionally enable GKE metrics.
  // Only for Attached Clusters.
  CloudMonitoringConfig cloud_monitoring_config = 4;
}

// ManagedPrometheusConfig defines the configuration for
// Google Cloud Managed Service for Prometheus.
message ManagedPrometheusConfig {
  // Enable Managed Collection.
  bool enabled = 1;
}

// CloudMonitoringConfig defines the configuration for
// built-in Cloud Logging and Monitoring.
// Only for Attached Clusters.
message CloudMonitoringConfig {
  // Enable GKE-native logging and metrics.
  // Only for Attached Clusters.
  optional bool enabled = 1;
}

// Configuration for Binary Authorization.
message BinaryAuthorization {
  // Binary Authorization mode of operation.
  enum EvaluationMode {
    // Default value
    EVALUATION_MODE_UNSPECIFIED = 0;

    // Disable BinaryAuthorization
    DISABLED = 1;

    // Enforce Kubernetes admission requests with BinaryAuthorization using the
    // project's singleton policy.
    PROJECT_SINGLETON_POLICY_ENFORCE = 2;
  }

  // Mode of operation for binauthz policy evaluation. If unspecified, defaults
  // to DISABLED.
  EvaluationMode evaluation_mode = 1;
}

// SecurityPostureConfig defines the flags needed to enable/disable features for
// the Security Posture API.
message SecurityPostureConfig {
  // VulnerabilityMode defines enablement mode for vulnerability scanning.
  enum VulnerabilityMode {
    // Default value not specified.
    VULNERABILITY_MODE_UNSPECIFIED = 0;

    // Disables vulnerability scanning on the cluster.
    VULNERABILITY_DISABLED = 1;

    // Applies the Security Posture's vulnerability on cluster Enterprise level
    // features.
    VULNERABILITY_ENTERPRISE = 2;
  }

  // Sets which mode to use for vulnerability scanning.
  VulnerabilityMode vulnerability_mode = 1;
}
