// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkemulticloud.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/gkemulticloud/v1/common_resources.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option csharp_namespace = "Google.Cloud.GkeMultiCloud.V1";
option go_package = "cloud.google.com/go/gkemulticloud/apiv1/gkemulticloudpb;gkemulticloudpb";
option java_multiple_files = true;
option java_outer_classname = "AzureResourcesProto";
option java_package = "com.google.cloud.gkemulticloud.v1";
option php_namespace = "Google\\Cloud\\GkeMultiCloud\\V1";
option ruby_package = "Google::Cloud::GkeMultiCloud::V1";

// An Anthos cluster running on Azure.
message AzureCluster {
  option (google.api.resource) = {
    type: "gkemulticloud.googleapis.com/AzureCluster"
    pattern: "projects/{project}/locations/{location}/azureClusters/{azure_cluster}"
  };

  // The lifecycle state of the cluster.
  enum State {
    // Not set.
    STATE_UNSPECIFIED = 0;

    // The PROVISIONING state indicates the cluster is being created.
    PROVISIONING = 1;

    // The RUNNING state indicates the cluster has been created and is fully
    // usable.
    RUNNING = 2;

    // The RECONCILING state indicates that some work is actively being done on
    // the cluster, such as upgrading the control plane replicas.
    RECONCILING = 3;

    // The STOPPING state indicates the cluster is being deleted.
    STOPPING = 4;

    // The ERROR state indicates the cluster is in a broken unrecoverable
    // state.
    ERROR = 5;

    // The DEGRADED state indicates the cluster requires user action to
    // restore full functionality.
    DEGRADED = 6;
  }

  // The name of this resource.
  //
  // Cluster names are formatted as
  // `projects/<project-number>/locations/<region>/azureClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string name = 1;

  // Optional. A human readable description of this cluster.
  // Cannot be longer than 255 UTF-8 encoded bytes.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The Azure region where the cluster runs.
  //
  // Each Google Cloud region supports a subset of nearby Azure regions.
  // You can call
  // [GetAzureServerConfig][google.cloud.gkemulticloud.v1.AzureClusters.GetAzureServerConfig]
  // to list all supported Azure regions within a given Google Cloud region.
  string azure_region = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The ARM ID of the resource group where the cluster resources are
  // deployed. For example:
  // `/subscriptions/<subscription-id>/resourceGroups/<resource-group-name>`
  string resource_group_id = 17 [(google.api.field_behavior) = REQUIRED];

  // Optional. Name of the
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] that contains
  // authentication configuration for how the Anthos Multi-Cloud API connects to
  // Azure APIs.
  //
  // Either azure_client or azure_services_authentication should be provided.
  //
  // The `AzureClient` resource must reside on the same Google Cloud Platform
  // project and region as the `AzureCluster`.
  //
  // `AzureClient` names are formatted as
  // `projects/<project-number>/locations/<region>/azureClients/<client-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string azure_client = 16 [(google.api.field_behavior) = OPTIONAL];

  // Required. Cluster-wide networking configuration.
  AzureClusterNetworking networking = 4
      [(google.api.field_behavior) = REQUIRED];

  // Required. Configuration related to the cluster control plane.
  AzureControlPlane control_plane = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. Configuration related to the cluster RBAC settings.
  AzureAuthorization authorization = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. Authentication configuration for management of Azure resources.
  //
  // Either azure_client or azure_services_authentication should be provided.
  AzureServicesAuthentication azure_services_authentication = 22
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The current state of the cluster.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The endpoint of the cluster's API server.
  string endpoint = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A globally unique identifier for the cluster.
  string uid = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set, there are currently changes in flight to the cluster.
  bool reconciling = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this cluster was created.
  google.protobuf.Timestamp create_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this cluster was last updated.
  google.protobuf.Timestamp update_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Allows clients to perform consistent read-modify-writes
  // through optimistic concurrency control.
  //
  // Can be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 13;

  // Optional. Annotations on the cluster.
  //
  // This field has the same restrictions as Kubernetes annotations.
  // The total size of all keys and values combined is limited to 256k.
  // Keys can have 2 segments: prefix (optional) and name (required),
  // separated by a slash (/).
  // Prefix must be a DNS subdomain.
  // Name must be 63 characters or less, begin and end with alphanumerics,
  // with dashes (-), underscores (_), dots (.), and alphanumerics between.
  map<string, string> annotations = 14 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Workload Identity settings.
  WorkloadIdentityConfig workload_identity_config = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. PEM encoded x509 certificate of the cluster root of trust.
  string cluster_ca_certificate = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Fleet configuration.
  Fleet fleet = 20 [(google.api.field_behavior) = REQUIRED];

  // Output only. Managed Azure resources for this cluster.
  AzureClusterResources managed_resources = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Logging configuration for this cluster.
  LoggingConfig logging_config = 23 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A set of errors found in the cluster.
  repeated AzureClusterError errors = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Monitoring configuration for this cluster.
  MonitoringConfig monitoring_config = 25
      [(google.api.field_behavior) = OPTIONAL];
}

// ClusterNetworking contains cluster-wide networking configuration.
message AzureClusterNetworking {
  // Required. The Azure Resource Manager (ARM) ID of the VNet associated with
  // your cluster.
  //
  // All components in the cluster (i.e. control plane and node pools) run on a
  // single VNet.
  //
  // Example:
  // `/subscriptions/<subscription-id>/resourceGroups/<resource-group-id>/providers/Microsoft.Network/virtualNetworks/<vnet-id>`
  //
  // This field cannot be changed after creation.
  string virtual_network_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The IP address range of the pods in this cluster, in CIDR
  // notation (e.g. `*********/14`).
  //
  // All pods in the cluster get assigned a unique IPv4 address from these
  // ranges. Only a single range is supported.
  //
  // This field cannot be changed after creation.
  repeated string pod_address_cidr_blocks = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The IP address range for services in this cluster, in CIDR
  // notation (e.g. `*********/14`).
  //
  // All services in the cluster get assigned a unique IPv4 address from these
  // ranges. Only a single range is supported.
  //
  // This field cannot be changed after creating a cluster.
  repeated string service_address_cidr_blocks = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The ARM ID of the subnet where Kubernetes private service type
  // load balancers are deployed. When unspecified, it defaults to
  // AzureControlPlane.subnet_id.
  //
  // Example:
  // "/subscriptions/d00494d6-6f3c-4280-bbb2-899e163d1d30/resourceGroups/anthos_cluster_gkeust4/providers/Microsoft.Network/virtualNetworks/gke-vnet-gkeust4/subnets/subnetid456"
  string service_load_balancer_subnet_id = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// AzureControlPlane represents the control plane configurations.
message AzureControlPlane {
  // Required. The Kubernetes version to run on control plane replicas
  // (e.g. `1.19.10-gke.1000`).
  //
  // You can list all supported versions on a given Google Cloud region by
  // calling
  // [GetAzureServerConfig][google.cloud.gkemulticloud.v1.AzureClusters.GetAzureServerConfig].
  string version = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The ARM ID of the default subnet for the control plane. The
  // control plane VMs are deployed in this subnet, unless
  // `AzureControlPlane.replica_placements` is specified. This subnet will also
  // be used as default for `AzureControlPlane.endpoint_subnet_id` if
  // `AzureControlPlane.endpoint_subnet_id` is not specified. Similarly it will
  // be used as default for
  // `AzureClusterNetworking.service_load_balancer_subnet_id`.
  //
  // Example:
  // `/subscriptions/<subscription-id>/resourceGroups/<resource-group-id>/providers/Microsoft.Network/virtualNetworks/<vnet-id>/subnets/default`.
  string subnet_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Azure VM size name. Example: `Standard_DS2_v2`.
  //
  // For available VM sizes, see
  // https://docs.microsoft.com/en-us/azure/virtual-machines/vm-naming-conventions.
  //
  // When unspecified, it defaults to `Standard_DS2_v2`.
  string vm_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. SSH configuration for how to access the underlying control plane
  // machines.
  AzureSshConfig ssh_config = 11 [(google.api.field_behavior) = REQUIRED];

  // Optional. Configuration related to the root volume provisioned for each
  // control plane replica.
  //
  // When unspecified, it defaults to 32-GiB Azure Disk.
  AzureDiskTemplate root_volume = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration related to the main volume provisioned for each
  // control plane replica.
  // The main volume is in charge of storing all of the cluster's etcd state.
  //
  // When unspecified, it defaults to a 8-GiB Azure Disk.
  AzureDiskTemplate main_volume = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration related to application-layer secrets encryption.
  AzureDatabaseEncryption database_encryption = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Proxy configuration for outbound HTTP(S) traffic.
  AzureProxyConfig proxy_config = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration related to vm config encryption.
  AzureConfigEncryption config_encryption = 14
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A set of tags to apply to all underlying control plane Azure
  // resources.
  map<string, string> tags = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration for where to place the control plane replicas.
  //
  // Up to three replica placement instances can be specified. If
  // replica_placements is set, the replica placement instances will be applied
  // to the three control plane replicas as evenly as possible.
  repeated ReplicaPlacement replica_placements = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The ARM ID of the subnet where the control plane load balancer is
  // deployed. When unspecified, it defaults to AzureControlPlane.subnet_id.
  //
  // Example:
  // "/subscriptions/d00494d6-6f3c-4280-bbb2-899e163d1d30/resourceGroups/anthos_cluster_gkeust4/providers/Microsoft.Network/virtualNetworks/gke-vnet-gkeust4/subnets/subnetid123"
  string endpoint_subnet_id = 15 [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for the placement of a control plane replica.
message ReplicaPlacement {
  // Required. For a given replica, the ARM ID of the subnet where the control
  // plane VM is deployed. Make sure it's a subnet under the virtual network in
  // the cluster configuration.
  string subnet_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. For a given replica, the Azure availability zone where to
  // provision the control plane VM and the ETCD disk.
  string azure_availability_zone = 2 [(google.api.field_behavior) = REQUIRED];
}

// Details of a proxy config stored in Azure Key Vault.
message AzureProxyConfig {
  // The ARM ID the of the resource group containing proxy keyvault.
  //
  // Resource group ids are formatted as
  // `/subscriptions/<subscription-id>/resourceGroups/<resource-group-name>`.
  string resource_group_id = 1;

  // The URL the of the proxy setting secret with its version.
  //
  // The secret must be a JSON encoded proxy configuration
  // as described in
  // https://cloud.google.com/kubernetes-engine/multi-cloud/docs/azure/how-to/use-a-proxy#create_a_proxy_configuration_file
  //
  // Secret ids are formatted as
  // `https://<key-vault-name>.vault.azure.net/secrets/<secret-name>/<secret-version>`.
  string secret_id = 2;
}

// Configuration related to application-layer secrets encryption.
//
// Anthos clusters on Azure encrypts your Kubernetes data at rest
// in etcd using Azure Key Vault.
message AzureDatabaseEncryption {
  // Required. The ARM ID of the Azure Key Vault key to encrypt / decrypt data.
  //
  // For example:
  // `/subscriptions/<subscription-id>/resourceGroups/<resource-group-id>/providers/Microsoft.KeyVault/vaults/<key-vault-id>/keys/<key-name>`
  // Encryption will always take the latest version of the key and hence
  // specific version is not supported.
  string key_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Configuration related to config data encryption.
//
// Azure VM bootstrap secret is envelope encrypted with the provided key vault
// key.
message AzureConfigEncryption {
  // Required. The ARM ID of the Azure Key Vault key to encrypt / decrypt config
  // data.
  //
  // For example:
  // `/subscriptions/<subscription-id>/resourceGroups/<resource-group-id>/providers/Microsoft.KeyVault/vaults/<key-vault-id>/keys/<key-name>`
  string key_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. RSA key of the Azure Key Vault public key to use for encrypting
  // the data.
  //
  // This key must be formatted as a PEM-encoded SubjectPublicKeyInfo (RFC 5280)
  // in ASN.1 DER form. The string must be comprised of a single PEM block of
  // type "PUBLIC KEY".
  string public_key = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for Azure Disks.
message AzureDiskTemplate {
  // Optional. The size of the disk, in GiBs.
  //
  // When unspecified, a default value is provided. See the specific reference
  // in the parent resource.
  int32 size_gib = 1 [(google.api.field_behavior) = OPTIONAL];
}

// `AzureClient` resources hold client authentication information needed by the
// Anthos Multi-Cloud API to manage Azure resources on your Azure subscription.
//
// When an [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] is
// created, an `AzureClient` resource needs to be provided and all operations on
// Azure resources associated to that cluster will authenticate to Azure
// services using the given client.
//
// `AzureClient` resources are immutable and cannot be modified upon creation.
//
// Each `AzureClient` resource is bound to a single Azure Active Directory
// Application and tenant.
message AzureClient {
  option (google.api.resource) = {
    type: "gkemulticloud.googleapis.com/AzureClient"
    pattern: "projects/{project}/locations/{location}/azureClients/{azure_client}"
  };

  // The name of this resource.
  //
  // `AzureClient` resource names are formatted as
  // `projects/<project-number>/locations/<region>/azureClients/<client-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1;

  // Required. The Azure Active Directory Tenant ID.
  string tenant_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Azure Active Directory Application ID.
  string application_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. If set, there are currently pending changes to the client.
  bool reconciling = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Annotations on the resource.
  //
  // This field has the same restrictions as Kubernetes annotations.
  // The total size of all keys and values combined is limited to 256k.
  // Keys can have 2 segments: prefix (optional) and name (required),
  // separated by a slash (/).
  // Prefix must be a DNS subdomain.
  // Name must be 63 characters or less, begin and end with alphanumerics,
  // with dashes (-), underscores (_), dots (.), and alphanumerics between.
  map<string, string> annotations = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The PEM encoded x509 certificate.
  string pem_certificate = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A globally unique identifier for the client.
  string uid = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this resource was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this client was last updated.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration related to the cluster RBAC settings.
message AzureAuthorization {
  // Optional. Users that can perform operations as a cluster admin. A managed
  // ClusterRoleBinding will be created to grant the `cluster-admin` ClusterRole
  // to the users. Up to ten admin users can be provided.
  //
  // For more info on RBAC, see
  // https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-facing-roles
  repeated AzureClusterUser admin_users = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Groups of users that can perform operations as a cluster admin. A
  // managed ClusterRoleBinding will be created to grant the `cluster-admin`
  // ClusterRole to the groups. Up to ten admin groups can be provided.
  //
  // For more info on RBAC, see
  // https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-facing-roles
  repeated AzureClusterGroup admin_groups = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Authentication configuration for the management of Azure resources.
message AzureServicesAuthentication {
  // Required. The Azure Active Directory Tenant ID.
  string tenant_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The Azure Active Directory Application ID.
  string application_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Identities of a user-type subject for Azure clusters.
message AzureClusterUser {
  // Required. The name of the user, e.g. `<EMAIL>`.
  string username = 1 [(google.api.field_behavior) = REQUIRED];
}

// Identities of a group-type subject for Azure clusters.
message AzureClusterGroup {
  // Required. The name of the group, e.g. `<EMAIL>`.
  string group = 1 [(google.api.field_behavior) = REQUIRED];
}

// An Anthos node pool running on Azure.
message AzureNodePool {
  option (google.api.resource) = {
    type: "gkemulticloud.googleapis.com/AzureNodePool"
    pattern: "projects/{project}/locations/{location}/azureClusters/{azure_cluster}/azureNodePools/{azure_node_pool}"
  };

  // The lifecycle state of the node pool.
  enum State {
    // Not set.
    STATE_UNSPECIFIED = 0;

    // The PROVISIONING state indicates the node pool is being created.
    PROVISIONING = 1;

    // The RUNNING state indicates the node pool has been created and is fully
    // usable.
    RUNNING = 2;

    // The RECONCILING state indicates that the node pool is being reconciled.
    RECONCILING = 3;

    // The STOPPING state indicates the node pool is being deleted.
    STOPPING = 4;

    // The ERROR state indicates the node pool is in a broken unrecoverable
    // state.
    ERROR = 5;

    // The DEGRADED state indicates the node pool requires user action to
    // restore full functionality.
    DEGRADED = 6;
  }

  // The name of this resource.
  //
  // Node pool names are formatted as
  // `projects/<project-number>/locations/<region>/azureClusters/<cluster-id>/azureNodePools/<node-pool-id>`.
  //
  // For more details on Google Cloud resource names,
  // see [Resource Names](https://cloud.google.com/apis/design/resource_names)
  string name = 1;

  // Required. The Kubernetes version (e.g. `1.19.10-gke.1000`) running on this
  // node pool.
  string version = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The node configuration of the node pool.
  AzureNodeConfig config = 22 [(google.api.field_behavior) = REQUIRED];

  // Required. The ARM ID of the subnet where the node pool VMs run. Make sure
  // it's a subnet under the virtual network in the cluster configuration.
  string subnet_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Autoscaler configuration for this node pool.
  AzureNodePoolAutoscaling autoscaling = 4
      [(google.api.field_behavior) = REQUIRED];

  // Output only. The current state of the node pool.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A globally unique identifier for the node pool.
  string uid = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If set, there are currently pending changes to the node
  // pool.
  bool reconciling = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this node pool was created.
  google.protobuf.Timestamp create_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this node pool was last updated.
  google.protobuf.Timestamp update_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Allows clients to perform consistent read-modify-writes
  // through optimistic concurrency control.
  //
  // Can be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 12;

  // Optional. Annotations on the node pool.
  //
  // This field has the same restrictions as Kubernetes annotations.
  // The total size of all keys and values combined is limited to 256k.
  // Keys can have 2 segments: prefix (optional) and name (required),
  // separated by a slash (/).
  // Prefix must be a DNS subdomain.
  // Name must be 63 characters or less, begin and end with alphanumerics,
  // with dashes (-), underscores (_), dots (.), and alphanumerics between.
  map<string, string> annotations = 13 [(google.api.field_behavior) = OPTIONAL];

  // Required. The constraint on the maximum number of pods that can be run
  // simultaneously on a node in the node pool.
  MaxPodsConstraint max_pods_constraint = 21
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The Azure availability zone of the nodes in this nodepool.
  //
  // When unspecified, it defaults to `1`.
  string azure_availability_zone = 23 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A set of errors found in the node pool.
  repeated AzureNodePoolError errors = 29
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The Management configuration for this node pool.
  AzureNodeManagement management = 30 [(google.api.field_behavior) = OPTIONAL];
}

// AzureNodeManagement defines the set of node management features turned on for
// an Azure node pool.
message AzureNodeManagement {
  // Optional. Whether or not the nodes will be automatically repaired. When set
  // to true, the nodes in this node pool will be monitored and if they fail
  // health checks consistently over a period of time, an automatic repair
  // action will be triggered to replace them with new nodes.
  bool auto_repair = 1 [(google.api.field_behavior) = OPTIONAL];
}

// Parameters that describe the configuration of all node machines
// on a given node pool.
message AzureNodeConfig {
  // Optional. The Azure VM size name. Example: `Standard_DS2_v2`.
  //
  // See [Supported VM
  // sizes](/anthos/clusters/docs/azure/reference/supported-vms) for options.
  //
  // When unspecified, it defaults to `Standard_DS2_v2`.
  string vm_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration related to the root volume provisioned for each
  // node pool machine.
  //
  // When unspecified, it defaults to a 32-GiB Azure Disk.
  AzureDiskTemplate root_volume = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A set of tags to apply to all underlying Azure resources for this
  // node pool. This currently only includes Virtual Machine Scale Sets.
  //
  // Specify at most 50 pairs containing alphanumerics, spaces, and symbols
  // (.+-=_:@/). Keys can be up to 127 Unicode characters. Values can be up to
  // 255 Unicode characters.
  map<string, string> tags = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The OS image type to use on node pool instances.
  // Can be unspecified, or have a value of `ubuntu`.
  //
  // When unspecified, it defaults to `ubuntu`.
  string image_type = 8 [(google.api.field_behavior) = OPTIONAL];

  // Required. SSH configuration for how to access the node pool machines.
  AzureSshConfig ssh_config = 7 [(google.api.field_behavior) = REQUIRED];

  // Optional. Proxy configuration for outbound HTTP(S) traffic.
  AzureProxyConfig proxy_config = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration related to vm config encryption.
  AzureConfigEncryption config_encryption = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The initial taints assigned to nodes of this node pool.
  repeated NodeTaint taints = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The initial labels assigned to nodes of this node pool. An object
  // containing a list of "key": value pairs. Example: { "name": "wrench",
  // "mass": "1.3kg", "count": "3" }.
  map<string, string> labels = 11 [(google.api.field_behavior) = OPTIONAL];
}

// Configuration related to Kubernetes cluster autoscaler.
//
// The Kubernetes cluster autoscaler will automatically adjust the
// size of the node pool based on the cluster load.
message AzureNodePoolAutoscaling {
  // Required. Minimum number of nodes in the node pool. Must be greater than or
  // equal to 1 and less than or equal to max_node_count.
  int32 min_node_count = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Maximum number of nodes in the node pool. Must be greater than or
  // equal to min_node_count and less than or equal to 50.
  int32 max_node_count = 2 [(google.api.field_behavior) = REQUIRED];
}

// AzureOpenIdConfig is an OIDC discovery document for the cluster.
// See the OpenID Connect Discovery 1.0 specification for details.
message AzureOpenIdConfig {
  // OIDC Issuer.
  string issuer = 1;

  // JSON Web Key uri.
  string jwks_uri = 2;

  // Supported response types.
  repeated string response_types_supported = 3;

  // Supported subject types.
  repeated string subject_types_supported = 4;

  // supported ID Token signing Algorithms.
  repeated string id_token_signing_alg_values_supported = 5;

  // Supported claims.
  repeated string claims_supported = 6;

  // Supported grant types.
  repeated string grant_types = 7;
}

// AzureJsonWebKeys is a valid JSON Web Key Set as specififed in RFC 7517.
message AzureJsonWebKeys {
  // The public component of the keys used by the cluster to sign token
  // requests.
  repeated Jwk keys = 1;
}

// AzureServerConfig contains information about a Google Cloud location, such as
// supported Azure regions and Kubernetes versions.
message AzureServerConfig {
  option (google.api.resource) = {
    type: "gkemulticloud.googleapis.com/AzureServerConfig"
    pattern: "projects/{project}/locations/{location}/azureServerConfig"
  };

  // The `AzureServerConfig` resource name.
  //
  // `AzureServerConfig` names are formatted as
  // `projects/<project-number>/locations/<region>/azureServerConfig`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string name = 1;

  // List of all released Kubernetes versions, including ones which are end of
  // life and can no longer be used.  Filter by the `enabled`
  // property to limit to currently available versions.
  // Valid versions supported for both create and update operations
  repeated AzureK8sVersionInfo valid_versions = 2;

  // The list of supported Azure regions.
  repeated string supported_azure_regions = 3;
}

// Kubernetes version information of GKE cluster on Azure.
message AzureK8sVersionInfo {
  // Kubernetes version name (for example, `1.19.10-gke.1000`)
  string version = 1;

  // Optional. True if the version is available for cluster creation. If a
  // version is enabled for creation, it can be used to create new clusters.
  // Otherwise, cluster creation will fail. However, cluster upgrade operations
  // may succeed, even if the version is not enabled.
  bool enabled = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. True if this cluster version belongs to a minor version that has
  // reached its end of life and is no longer in scope to receive security and
  // bug fixes.
  bool end_of_life = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The estimated date (in Pacific Time) when this cluster version
  // will reach its end of life. Or if this version is no longer supported (the
  // `end_of_life` field is true), this is the actual date (in Pacific time)
  // when the version reached its end of life.
  google.type.Date end_of_life_date = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The date (in Pacific Time) when the cluster version was released.
  google.type.Date release_date = 6 [(google.api.field_behavior) = OPTIONAL];
}

// SSH configuration for Azure resources.
message AzureSshConfig {
  // Required. The SSH public key data for VMs managed by Anthos. This accepts
  // the authorized_keys file format used in OpenSSH according to the sshd(8)
  // manual page.
  string authorized_key = 1 [(google.api.field_behavior) = REQUIRED];
}

// Managed Azure resources for the cluster.
//
// The values could change and be empty, depending on the state of the cluster.
message AzureClusterResources {
  // Output only. The ARM ID of the cluster network security group.
  string network_security_group_id = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ARM ID of the control plane application security group.
  string control_plane_application_security_group_id = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// AzureClusterError describes errors found on Azure clusters.
message AzureClusterError {
  // Human-friendly description of the error.
  string message = 1;
}

// AzureNodePoolError describes errors found on Azure node pools.
message AzureNodePoolError {
  // Human-friendly description of the error.
  string message = 1;
}
