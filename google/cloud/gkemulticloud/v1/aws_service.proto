// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkemulticloud.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/gkemulticloud/v1/aws_resources.proto";
import "google/cloud/gkemulticloud/v1/common_resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeMultiCloud.V1";
option go_package = "cloud.google.com/go/gkemulticloud/apiv1/gkemulticloudpb;gkemulticloudpb";
option java_multiple_files = true;
option java_outer_classname = "AwsServiceProto";
option java_package = "com.google.cloud.gkemulticloud.v1";
option php_namespace = "Google\\Cloud\\GkeMultiCloud\\V1";
option ruby_package = "Google::Cloud::GkeMultiCloud::V1";

// The AwsClusters API provides a single centrally managed service
// to create and manage Anthos clusters that run on AWS infrastructure.
service AwsClusters {
  option (google.api.default_host) = "gkemulticloud.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster]
  // resource on a given Google Cloud Platform project and region.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc CreateAwsCluster(CreateAwsClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/awsClusters"
      body: "aws_cluster"
    };
    option (google.api.method_signature) = "parent,aws_cluster,aws_cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "AwsCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster].
  rpc UpdateAwsCluster(UpdateAwsClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{aws_cluster.name=projects/*/locations/*/awsClusters/*}"
      body: "aws_cluster"
    };
    option (google.api.method_signature) = "aws_cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AwsCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Describes a specific [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster]
  // resource.
  rpc GetAwsCluster(GetAwsClusterRequest) returns (AwsCluster) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/awsClusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] resources
  // on a given Google Cloud project and region.
  rpc ListAwsClusters(ListAwsClustersRequest)
      returns (ListAwsClustersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/awsClusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a specific [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster]
  // resource.
  //
  // Fails if the cluster has one or more associated
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] resources.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc DeleteAwsCluster(DeleteAwsClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/awsClusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Generates an access token for a cluster agent.
  rpc GenerateAwsClusterAgentToken(GenerateAwsClusterAgentTokenRequest)
      returns (GenerateAwsClusterAgentTokenResponse) {
    option (google.api.http) = {
      post: "/v1/{aws_cluster=projects/*/locations/*/awsClusters/*}:generateAwsClusterAgentToken"
      body: "*"
    };
  }

  // Generates a short-lived access token to authenticate to a given
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] resource.
  rpc GenerateAwsAccessToken(GenerateAwsAccessTokenRequest)
      returns (GenerateAwsAccessTokenResponse) {
    option (google.api.http) = {
      get: "/v1/{aws_cluster=projects/*/locations/*/awsClusters/*}:generateAwsAccessToken"
    };
  }

  // Creates a new [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool],
  // attached to a given [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster].
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc CreateAwsNodePool(CreateAwsNodePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/awsClusters/*}/awsNodePools"
      body: "aws_node_pool"
    };
    option (google.api.method_signature) =
        "parent,aws_node_pool,aws_node_pool_id";
    option (google.longrunning.operation_info) = {
      response_type: "AwsNodePool"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool].
  rpc UpdateAwsNodePool(UpdateAwsNodePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{aws_node_pool.name=projects/*/locations/*/awsClusters/*/awsNodePools/*}"
      body: "aws_node_pool"
    };
    option (google.api.method_signature) = "aws_node_pool,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AwsNodePool"
      metadata_type: "OperationMetadata"
    };
  }

  // Rolls back a previously aborted or failed
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] update request.
  // Makes no changes if the last update request successfully finished.
  // If an update request is in progress, you cannot rollback the update.
  // You must first cancel or let it finish unsuccessfully before you can
  // rollback.
  rpc RollbackAwsNodePoolUpdate(RollbackAwsNodePoolUpdateRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/awsClusters/*/awsNodePools/*}:rollback"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "AwsNodePool"
      metadata_type: "OperationMetadata"
    };
  }

  // Describes a specific
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] resource.
  rpc GetAwsNodePool(GetAwsNodePoolRequest) returns (AwsNodePool) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/awsClusters/*/awsNodePools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool]
  // resources on a given
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster].
  rpc ListAwsNodePools(ListAwsNodePoolsRequest)
      returns (ListAwsNodePoolsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/awsClusters/*}/awsNodePools"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a specific [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool]
  // resource.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc DeleteAwsNodePool(DeleteAwsNodePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/awsClusters/*/awsNodePools/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets the OIDC discovery document for the cluster.
  // See the
  // [OpenID Connect Discovery 1.0
  // specification](https://openid.net/specs/openid-connect-discovery-1_0.html)
  // for details.
  rpc GetAwsOpenIdConfig(GetAwsOpenIdConfigRequest) returns (AwsOpenIdConfig) {
    option (google.api.http) = {
      get: "/v1/{aws_cluster=projects/*/locations/*/awsClusters/*}/.well-known/openid-configuration"
    };
  }

  // Gets the public component of the cluster signing keys in
  // JSON Web Key format.
  rpc GetAwsJsonWebKeys(GetAwsJsonWebKeysRequest) returns (AwsJsonWebKeys) {
    option (google.api.http) = {
      get: "/v1/{aws_cluster=projects/*/locations/*/awsClusters/*}/jwks"
    };
  }

  // Returns information, such as supported AWS regions and Kubernetes
  // versions, on a given Google Cloud location.
  rpc GetAwsServerConfig(GetAwsServerConfigRequest) returns (AwsServerConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/awsServerConfig}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for `AwsClusters.CreateAwsCluster` method.
message CreateAwsClusterRequest {
  // Required. The parent location where this
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] resource will be
  // created.
  //
  // Location names are formatted as `projects/<project-id>/locations/<region>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AwsCluster"
    }
  ];

  // Required. The specification of the
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] to create.
  AwsCluster aws_cluster = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A client provided ID the resource. Must be unique within the
  // parent resource.
  //
  // The provided ID will be part of the
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] resource name
  // formatted as
  // `projects/<project-id>/locations/<region>/awsClusters/<cluster-id>`.
  //
  // Valid characters are `/[a-z][0-9]-/`. Cannot be longer than 63 characters.
  string aws_cluster_id = 3 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but do not actually create the cluster.
  bool validate_only = 4;
}

// Request message for `AwsClusters.UpdateAwsCluster` method.
message UpdateAwsClusterRequest {
  // Required. The [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster]
  // resource to update.
  AwsCluster aws_cluster = 1 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but do not actually update the cluster.
  bool validate_only = 2;

  // Required. Mask of fields to update. At least one path must be supplied in
  // this field. The elements of the repeated paths field can only include these
  // fields from [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster]:
  //
  //  *   `description`.
  //  *   `annotations`.
  //  *   `control_plane.version`.
  //  *   `authorization.admin_users`.
  //  *   `authorization.admin_groups`.
  //  *   `binary_authorization.evaluation_mode`.
  //  *   `control_plane.aws_services_authentication.role_arn`.
  //  *   `control_plane.aws_services_authentication.role_session_name`.
  //  *   `control_plane.config_encryption.kms_key_arn`.
  //  *   `control_plane.instance_type`.
  //  *   `control_plane.security_group_ids`.
  //  *   `control_plane.proxy_config`.
  //  *   `control_plane.proxy_config.secret_arn`.
  //  *   `control_plane.proxy_config.secret_version`.
  //  *   `control_plane.root_volume.size_gib`.
  //  *   `control_plane.root_volume.volume_type`.
  //  *   `control_plane.root_volume.iops`.
  //  *   `control_plane.root_volume.throughput`.
  //  *   `control_plane.root_volume.kms_key_arn`.
  //  *   `control_plane.ssh_config`.
  //  *   `control_plane.ssh_config.ec2_key_pair`.
  //  *   `control_plane.instance_placement.tenancy`.
  //  *   `control_plane.iam_instance_profile`.
  //  *   `logging_config.component_config.enable_components`.
  //  *   `control_plane.tags`.
  //  *   `monitoring_config.managed_prometheus_config.enabled`.
  //  *   `networking.per_node_pool_sg_rules_disabled`.
  google.protobuf.FieldMask update_mask = 4
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for `AwsClusters.GetAwsCluster` method.
message GetAwsClusterRequest {
  // Required. The name of the
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] resource to
  // describe.
  //
  // `AwsCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/awsClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AwsCluster"
    }
  ];
}

// Request message for `AwsClusters.ListAwsClusters` method.
message ListAwsClustersRequest {
  // Required. The parent location which owns this collection of
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] resources.
  //
  // Location names are formatted as `projects/<project-id>/locations/<region>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AwsCluster"
    }
  ];

  // The maximum number of items to return.
  //
  // If not specified, a default value of 50 will be used by the service.
  // Regardless of the pageSize value, the response can include a partial list
  // and a caller should only rely on response's
  // [nextPageToken][google.cloud.gkemulticloud.v1.ListAwsClustersResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2;

  // The `nextPageToken` value returned from a previous
  // [awsClusters.list][google.cloud.gkemulticloud.v1.AwsClusters.ListAwsClusters]
  // request, if any.
  string page_token = 3;
}

// Response message for `AwsClusters.ListAwsClusters` method.
message ListAwsClustersResponse {
  // A list of [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] resources
  // in the specified Google Cloud Platform project and region region.
  repeated AwsCluster aws_clusters = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Request message for `AwsClusters.DeleteAwsCluster` method.
message DeleteAwsClusterRequest {
  // Required. The resource name the
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] to delete.
  //
  // `AwsCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/awsClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AwsCluster"
    }
  ];

  // If set, only validate the request, but do not actually delete the resource.
  bool validate_only = 2;

  // If set to true, and the
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] resource is not
  // found, the request will succeed but no action will be taken on the server
  // and a completed [Operation][google.longrunning.Operation] will be returned.
  //
  // Useful for idempotent deletion.
  bool allow_missing = 3;

  // Optional. If set to true, the deletion of
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] resource will
  // succeed even if errors occur during deleting in cluster resources. Using
  // this parameter may result in orphaned resources in the cluster.
  bool ignore_errors = 5 [(google.api.field_behavior) = OPTIONAL];

  // The current etag of the
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster].
  //
  // Allows clients to perform deletions through optimistic concurrency control.
  //
  // If the provided etag does not match the current etag of the cluster,
  // the request will fail and an ABORTED error will be returned.
  string etag = 4;
}

// Response message for `AwsClusters.CreateAwsNodePool` method.
message CreateAwsNodePoolRequest {
  // Required. The [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster]
  // resource where this node pool will be created.
  //
  // `AwsCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/awsClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AwsNodePool"
    }
  ];

  // Required. The specification of the
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] to create.
  AwsNodePool aws_node_pool = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A client provided ID the resource. Must be unique within the
  // parent resource.
  //
  // The provided ID will be part of the
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] resource name
  // formatted as
  // `projects/<project-id>/locations/<region>/awsClusters/<cluster-id>/awsNodePools/<node-pool-id>`.
  //
  // Valid characters are `/[a-z][0-9]-/`. Cannot be longer than 63 characters.
  string aws_node_pool_id = 3 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but do not actually create the node
  // pool.
  bool validate_only = 4;
}

// Request message for `AwsClusters.UpdateAwsNodePool` method.
message UpdateAwsNodePoolRequest {
  // Required. The [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool]
  // resource to update.
  AwsNodePool aws_node_pool = 1 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but don't actually update the node pool.
  bool validate_only = 2;

  // Required. Mask of fields to update. At least one path must be supplied in
  // this field. The elements of the repeated paths field can only include these
  // fields from [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool]:
  //
  //  *   `annotations`.
  //  *   `version`.
  //  *   `autoscaling.min_node_count`.
  //  *   `autoscaling.max_node_count`.
  //  *   `config.config_encryption.kms_key_arn`.
  //  *   `config.security_group_ids`.
  //  *   `config.root_volume.iops`.
  //  *   `config.root_volume.throughput`.
  //  *   `config.root_volume.kms_key_arn`.
  //  *   `config.root_volume.volume_type`.
  //  *   `config.root_volume.size_gib`.
  //  *   `config.proxy_config`.
  //  *   `config.proxy_config.secret_arn`.
  //  *   `config.proxy_config.secret_version`.
  //  *   `config.ssh_config`.
  //  *   `config.ssh_config.ec2_key_pair`.
  //  *   `config.instance_placement.tenancy`.
  //  *   `config.iam_instance_profile`.
  //  *   `config.labels`.
  //  *   `config.tags`.
  //  *   `config.autoscaling_metrics_collection`.
  //  *   `config.autoscaling_metrics_collection.granularity`.
  //  *   `config.autoscaling_metrics_collection.metrics`.
  //  *   `config.instance_type`.
  //  *   `management.auto_repair`.
  //  *   `management`.
  //  *   `update_settings`.
  //  *   `update_settings.surge_settings`.
  //  *   `update_settings.surge_settings.max_surge`.
  //  *   `update_settings.surge_settings.max_unavailable`.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for `AwsClusters.RollbackAwsNodePoolUpdate` method.
message RollbackAwsNodePoolUpdateRequest {
  // Required. The name of the
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] resource to
  // rollback.
  //
  // `AwsNodePool` names are formatted as
  // `projects/<project-id>/locations/<region>/awsClusters/<cluster-id>/awsNodePools/<node-pool-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AwsNodePool"
    }
  ];

  // Optional. Option for rollback to ignore the PodDisruptionBudget when
  // draining the node pool nodes. Default value is false.
  bool respect_pdb = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for `AwsClusters.GetAwsNodePool` method.
message GetAwsNodePoolRequest {
  // Required. The name of the
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] resource to
  // describe.
  //
  // `AwsNodePool` names are formatted as
  // `projects/<project-id>/locations/<region>/awsClusters/<cluster-id>/awsNodePools/<node-pool-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AwsNodePool"
    }
  ];
}

// Request message for `AwsClusters.ListAwsNodePools` method.
message ListAwsNodePoolsRequest {
  // Required. The parent `AwsCluster` which owns this collection of
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] resources.
  //
  // `AwsCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/awsClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AwsNodePool"
    }
  ];

  // The maximum number of items to return.
  //
  // If not specified, a default value of 50 will be used by the service.
  // Regardless of the pageSize value, the response can include a partial list
  // and a caller should only rely on response's
  // [nextPageToken][google.cloud.gkemulticloud.v1.ListAwsNodePoolsResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2;

  // The `nextPageToken` value returned from a previous
  // [awsNodePools.list][google.cloud.gkemulticloud.v1.AwsClusters.ListAwsNodePools]
  // request, if any.
  string page_token = 3;
}

// Response message for `AwsClusters.ListAwsNodePools` method.
message ListAwsNodePoolsResponse {
  // A list of [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool]
  // resources in the specified `AwsCluster`.
  repeated AwsNodePool aws_node_pools = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Request message for `AwsClusters.DeleteAwsNodePool` method.
message DeleteAwsNodePoolRequest {
  // Required. The resource name the
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] to delete.
  //
  // `AwsNodePool` names are formatted as
  // `projects/<project-id>/locations/<region>/awsClusters/<cluster-id>/awsNodePools/<node-pool-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AwsNodePool"
    }
  ];

  // If set, only validate the request, but do not actually delete the node
  // pool.
  bool validate_only = 2;

  // If set to true, and the
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] resource is not
  // found, the request will succeed but no action will be taken on the server
  // and a completed [Operation][google.longrunning.Operation] will be returned.
  //
  // Useful for idempotent deletion.
  bool allow_missing = 3;

  // Optional. If set to true, the deletion of
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool] resource will
  // succeed even if errors occur during deleting in node pool resources. Using
  // this parameter may result in orphaned resources in the node pool.
  bool ignore_errors = 5 [(google.api.field_behavior) = OPTIONAL];

  // The current ETag of the
  // [AwsNodePool][google.cloud.gkemulticloud.v1.AwsNodePool].
  //
  // Allows clients to perform deletions through optimistic concurrency control.
  //
  // If the provided ETag does not match the current etag of the node pool,
  // the request will fail and an ABORTED error will be returned.
  string etag = 4;
}

// GetAwsOpenIdConfigRequest gets the OIDC discovery document for the
// cluster. See the OpenID Connect Discovery 1.0 specification for details.
message GetAwsOpenIdConfigRequest {
  // Required. The AwsCluster, which owns the OIDC discovery document.
  // Format:
  // projects/{project}/locations/{location}/awsClusters/{cluster}
  string aws_cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AwsCluster"
    }
  ];
}

// GetAwsJsonWebKeysRequest gets the public component of the keys used by the
// cluster to sign token requests. This will be the jwks_uri for the discover
// document returned by getOpenIDConfig. See the OpenID Connect
// Discovery 1.0 specification for details.
message GetAwsJsonWebKeysRequest {
  // Required. The AwsCluster, which owns the JsonWebKeys.
  // Format:
  // projects/{project}/locations/{location}/awsClusters/{cluster}
  string aws_cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AwsCluster"
    }
  ];
}

// GetAwsServerConfigRequest gets the server config of GKE cluster on AWS.
message GetAwsServerConfigRequest {
  // Required. The name of the
  // [AwsServerConfig][google.cloud.gkemulticloud.v1.AwsServerConfig] resource
  // to describe.
  //
  // `AwsServerConfig` names are formatted as
  // `projects/<project-id>/locations/<region>/awsServerConfig`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AwsServerConfig"
    }
  ];
}

// Request message for `AwsClusters.GenerateAwsAccessToken` method.
message GenerateAwsAccessTokenRequest {
  // Required. The name of the
  // [AwsCluster][google.cloud.gkemulticloud.v1.AwsCluster] resource to
  // authenticate to.
  //
  // `AwsCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/awsClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string aws_cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AwsCluster"
    }
  ];
}

// Response message for `AwsClusters.GenerateAwsAccessToken` method.
message GenerateAwsAccessTokenResponse {
  // Output only. Access token to authenticate to k8s api-server.
  string access_token = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp at which the token will expire.
  google.protobuf.Timestamp expiration_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

message GenerateAwsClusterAgentTokenRequest {
  // Required.
  string aws_cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AwsCluster"
    }
  ];

  // Required.
  string subject_token = 2 [(google.api.field_behavior) = REQUIRED];

  // Required.
  string subject_token_type = 3 [(google.api.field_behavior) = REQUIRED];

  // Required.
  string version = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional.
  string node_pool_id = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string grant_type = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string audience = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string scope = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string requested_token_type = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string options = 10 [(google.api.field_behavior) = OPTIONAL];
}

message GenerateAwsClusterAgentTokenResponse {
  string access_token = 1;

  int32 expires_in = 2;

  string token_type = 3;
}
