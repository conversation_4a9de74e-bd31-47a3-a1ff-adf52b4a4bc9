{"methodConfig": [{"name": [{"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "GetAwsCluster"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "ListAwsClusters"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "GetAwsNodePool"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "ListAwsNodePools"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "GenerateAwsAccessToken"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "GenerateAwsClusterAgentToken"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "GetAwsServerConfig"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "GetAwsOpenIdConfig"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "GetAwsJsonWebKeys"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "GetAzureClient"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "ListAzureClients"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "GetAzureCluster"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "ListAzureClusters"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "GetAzureNodePool"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "ListAzureNodePools"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "GenerateAzureAccessToken"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "GenerateAzureClusterAgentToken"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "GetAzureServerConfig"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "GetAzureOpenIdConfig"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "GetAzureJsonWebKeys"}, {"service": "google.cloud.gkemulticloud.v1.AttachedClusters", "method": "GetAttachedCluster"}, {"service": "google.cloud.gkemulticloud.v1.AttachedClusters", "method": "ListAttachedClusters"}, {"service": "google.cloud.gkemulticloud.v1.AttachedClusters", "method": "GetAttachedServerConfig"}, {"service": "google.cloud.gkemulticloud.v1.AttachedClusters", "method": "GenerateAttachedClusterAgentToken"}, {"service": "google.cloud.gkemulticloud.v1.AttachedClusters", "method": "GenerateAttachedClusterInstallManifest"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "CreateAwsCluster"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "DeleteAwsCluster"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "UpdateAwsCluster"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "CreateAwsNodePool"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "DeleteAwsNodePool"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "UpdateAwsNodePool"}, {"service": "google.cloud.gkemulticloud.v1.AwsClusters", "method": "RollbackAwsNodePoolUpdate"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "CreateAzureClient"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "DeleteAzureClient"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "CreateAzureCluster"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "DeleteAzureCluster"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "UpdateAzureCluster"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "CreateAzureNodePool"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "DeleteAzureNodePool"}, {"service": "google.cloud.gkemulticloud.v1.AzureClusters", "method": "UpdateAzureNodePool"}, {"service": "google.cloud.gkemulticloud.v1.AttachedClusters", "method": "CreateAttachedCluster"}, {"service": "google.cloud.gkemulticloud.v1.AttachedClusters", "method": "ImportAttachedCluster"}, {"service": "google.cloud.gkemulticloud.v1.AttachedClusters", "method": "UpdateAttachedCluster"}, {"service": "google.cloud.gkemulticloud.v1.AttachedClusters", "method": "DeleteAttachedCluster"}], "timeout": "60s"}]}