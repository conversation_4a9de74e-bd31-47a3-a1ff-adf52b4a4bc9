// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkemulticloud.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/gkemulticloud/v1/azure_resources.proto";
import "google/cloud/gkemulticloud/v1/common_resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeMultiCloud.V1";
option go_package = "cloud.google.com/go/gkemulticloud/apiv1/gkemulticloudpb;gkemulticloudpb";
option java_multiple_files = true;
option java_outer_classname = "AzureServiceProto";
option java_package = "com.google.cloud.gkemulticloud.v1";
option php_namespace = "Google\\Cloud\\GkeMultiCloud\\V1";
option ruby_package = "Google::Cloud::GkeMultiCloud::V1";

// The AzureClusters API provides a single centrally managed service
// to create and manage Anthos clusters that run on Azure infrastructure.
service AzureClusters {
  option (google.api.default_host) = "gkemulticloud.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new [AzureClient][google.cloud.gkemulticloud.v1.AzureClient]
  // resource on a given Google Cloud project and region.
  //
  // `AzureClient` resources hold client authentication
  // information needed by the Anthos Multicloud API to manage Azure resources
  // on your Azure subscription on your behalf.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc CreateAzureClient(CreateAzureClientRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/azureClients"
      body: "azure_client"
    };
    option (google.api.method_signature) =
        "parent,azure_client,azure_client_id";
    option (google.longrunning.operation_info) = {
      response_type: "AzureClient"
      metadata_type: "OperationMetadata"
    };
  }

  // Describes a specific
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] resource.
  rpc GetAzureClient(GetAzureClientRequest) returns (AzureClient) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/azureClients/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all [AzureClient][google.cloud.gkemulticloud.v1.AzureClient]
  // resources on a given Google Cloud project and region.
  rpc ListAzureClients(ListAzureClientsRequest)
      returns (ListAzureClientsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/azureClients"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a specific [AzureClient][google.cloud.gkemulticloud.v1.AzureClient]
  // resource.
  //
  // If the client is used by one or more clusters, deletion will
  // fail and a `FAILED_PRECONDITION` error will be returned.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc DeleteAzureClient(DeleteAzureClientRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/azureClients/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster]
  // resource on a given Google Cloud Platform project and region.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc CreateAzureCluster(CreateAzureClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/azureClusters"
      body: "azure_cluster"
    };
    option (google.api.method_signature) =
        "parent,azure_cluster,azure_cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "AzureCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster].
  rpc UpdateAzureCluster(UpdateAzureClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{azure_cluster.name=projects/*/locations/*/azureClusters/*}"
      body: "azure_cluster"
    };
    option (google.api.method_signature) = "azure_cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AzureCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Describes a specific
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] resource.
  rpc GetAzureCluster(GetAzureClusterRequest) returns (AzureCluster) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/azureClusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster]
  // resources on a given Google Cloud project and region.
  rpc ListAzureClusters(ListAzureClustersRequest)
      returns (ListAzureClustersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/azureClusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a specific
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] resource.
  //
  // Fails if the cluster has one or more associated
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool] resources.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc DeleteAzureCluster(DeleteAzureClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/azureClusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Generates an access token for a cluster agent.
  rpc GenerateAzureClusterAgentToken(GenerateAzureClusterAgentTokenRequest)
      returns (GenerateAzureClusterAgentTokenResponse) {
    option (google.api.http) = {
      post: "/v1/{azure_cluster=projects/*/locations/*/azureClusters/*}:generateAzureClusterAgentToken"
      body: "*"
    };
  }

  // Generates a short-lived access token to authenticate to a given
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] resource.
  rpc GenerateAzureAccessToken(GenerateAzureAccessTokenRequest)
      returns (GenerateAzureAccessTokenResponse) {
    option (google.api.http) = {
      get: "/v1/{azure_cluster=projects/*/locations/*/azureClusters/*}:generateAzureAccessToken"
    };
  }

  // Creates a new [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool],
  // attached to a given
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster].
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc CreateAzureNodePool(CreateAzureNodePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/azureClusters/*}/azureNodePools"
      body: "azure_node_pool"
    };
    option (google.api.method_signature) =
        "parent,azure_node_pool,azure_node_pool_id";
    option (google.longrunning.operation_info) = {
      response_type: "AzureNodePool"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool].
  rpc UpdateAzureNodePool(UpdateAzureNodePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{azure_node_pool.name=projects/*/locations/*/azureClusters/*/azureNodePools/*}"
      body: "azure_node_pool"
    };
    option (google.api.method_signature) = "azure_node_pool,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AzureNodePool"
      metadata_type: "OperationMetadata"
    };
  }

  // Describes a specific
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool] resource.
  rpc GetAzureNodePool(GetAzureNodePoolRequest) returns (AzureNodePool) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/azureClusters/*/azureNodePools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool]
  // resources on a given
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster].
  rpc ListAzureNodePools(ListAzureNodePoolsRequest)
      returns (ListAzureNodePoolsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/azureClusters/*}/azureNodePools"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a specific
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool] resource.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc DeleteAzureNodePool(DeleteAzureNodePoolRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/azureClusters/*/azureNodePools/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets the OIDC discovery document for the cluster.
  // See the
  // [OpenID Connect Discovery 1.0
  // specification](https://openid.net/specs/openid-connect-discovery-1_0.html)
  // for details.
  rpc GetAzureOpenIdConfig(GetAzureOpenIdConfigRequest)
      returns (AzureOpenIdConfig) {
    option (google.api.http) = {
      get: "/v1/{azure_cluster=projects/*/locations/*/azureClusters/*}/.well-known/openid-configuration"
    };
    option (google.api.method_signature) = "azure_cluster";
  }

  // Gets the public component of the cluster signing keys in
  // JSON Web Key format.
  rpc GetAzureJsonWebKeys(GetAzureJsonWebKeysRequest)
      returns (AzureJsonWebKeys) {
    option (google.api.http) = {
      get: "/v1/{azure_cluster=projects/*/locations/*/azureClusters/*}/jwks"
    };
    option (google.api.method_signature) = "azure_cluster";
  }

  // Returns information, such as supported Azure regions and Kubernetes
  // versions, on a given Google Cloud location.
  rpc GetAzureServerConfig(GetAzureServerConfigRequest)
      returns (AzureServerConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/azureServerConfig}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for `AzureClusters.CreateAzureCluster` method.
message CreateAzureClusterRequest {
  // Required. The parent location where this
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] resource will be
  // created.
  //
  // Location names are formatted as `projects/<project-id>/locations/<region>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AzureCluster"
    }
  ];

  // Required. The specification of the
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] to create.
  AzureCluster azure_cluster = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A client provided ID the resource. Must be unique within the
  // parent resource.
  //
  // The provided ID will be part of the
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] resource name
  // formatted as
  // `projects/<project-id>/locations/<region>/azureClusters/<cluster-id>`.
  //
  // Valid characters are `/[a-z][0-9]-/`. Cannot be longer than 63 characters.
  string azure_cluster_id = 3 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but do not actually create the cluster.
  bool validate_only = 4;
}

// Request message for `AzureClusters.UpdateAzureCluster` method.
message UpdateAzureClusterRequest {
  // Required. The [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster]
  // resource to update.
  AzureCluster azure_cluster = 1 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but do not actually update the cluster.
  bool validate_only = 2;

  // Required. Mask of fields to update. At least one path must be supplied in
  // this field. The elements of the repeated paths field can only include these
  // fields from [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster]:
  //
  //  *   `description`.
  //  *   `azureClient`.
  //  *   `control_plane.version`.
  //  *   `control_plane.vm_size`.
  //  *   `annotations`.
  //  *   `authorization.admin_users`.
  //  *   `authorization.admin_groups`.
  //  *   `control_plane.root_volume.size_gib`.
  //  *   `azure_services_authentication`.
  //  *   `azure_services_authentication.tenant_id`.
  //  *   `azure_services_authentication.application_id`.
  //  *   `control_plane.proxy_config`.
  //  *   `control_plane.proxy_config.resource_group_id`.
  //  *   `control_plane.proxy_config.secret_id`.
  //  *   `control_plane.ssh_config.authorized_key`.
  //  *   `logging_config.component_config.enable_components`
  //  *   `monitoring_config.managed_prometheus_config.enabled`.
  google.protobuf.FieldMask update_mask = 4
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for `AzureClusters.GetAzureCluster` method.
message GetAzureClusterRequest {
  // Required. The name of the
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] resource to
  // describe.
  //
  // `AzureCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/azureClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureCluster"
    }
  ];
}

// Request message for `AzureClusters.ListAzureClusters` method.
message ListAzureClustersRequest {
  // Required. The parent location which owns this collection of
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] resources.
  //
  // Location names are formatted as `projects/<project-id>/locations/<region>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AzureCluster"
    }
  ];

  // The maximum number of items to return.
  //
  // If not specified, a default value of 50 will be used by the service.
  // Regardless of the pageSize value, the response can include a partial list
  // and a caller should only rely on response's
  // [nextPageToken][google.cloud.gkemulticloud.v1.ListAzureClustersResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2;

  // The `nextPageToken` value returned from a previous
  // [azureClusters.list][google.cloud.gkemulticloud.v1.AzureClusters.ListAzureClusters]
  // request, if any.
  string page_token = 3;
}

// Response message for `AzureClusters.ListAzureClusters` method.
message ListAzureClustersResponse {
  // A list of [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster]
  // resources in the specified Google Cloud Platform project and region region.
  repeated AzureCluster azure_clusters = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Request message for `AzureClusters.DeleteAzureCluster` method.
message DeleteAzureClusterRequest {
  // Required. The resource name the
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] to delete.
  //
  // `AzureCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/azureClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureCluster"
    }
  ];

  // If set to true, and the
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] resource is not
  // found, the request will succeed but no action will be taken on the server
  // and a completed [Operation][google.longrunning.Operation] will be returned.
  //
  // Useful for idempotent deletion.
  bool allow_missing = 2;

  // If set, only validate the request, but do not actually delete the resource.
  bool validate_only = 3;

  // The current etag of the
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster].
  //
  // Allows clients to perform deletions through optimistic concurrency control.
  //
  // If the provided etag does not match the current etag of the cluster,
  // the request will fail and an ABORTED error will be returned.
  string etag = 4;

  // Optional. If set to true, the deletion of
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] resource will
  // succeed even if errors occur during deleting in cluster resources. Using
  // this parameter may result in orphaned resources in the cluster.
  bool ignore_errors = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for `AzureClusters.CreateAzureNodePool` method.
message CreateAzureNodePoolRequest {
  // Required. The [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster]
  // resource where this node pool will be created.
  //
  // `AzureCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/azureClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AzureNodePool"
    }
  ];

  // Required. The specification of the
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool] to create.
  AzureNodePool azure_node_pool = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A client provided ID the resource. Must be unique within the
  // parent resource.
  //
  // The provided ID will be part of the
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool] resource name
  // formatted as
  // `projects/<project-id>/locations/<region>/azureClusters/<cluster-id>/azureNodePools/<node-pool-id>`.
  //
  // Valid characters are `/[a-z][0-9]-/`. Cannot be longer than 63 characters.
  string azure_node_pool_id = 3 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but do not actually create the node
  // pool.
  bool validate_only = 4;
}

// Request message for `AzureClusters.UpdateAzureNodePool` method.
message UpdateAzureNodePoolRequest {
  // Required. The [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool]
  // resource to update.
  AzureNodePool azure_node_pool = 1 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but don't actually update the node pool.
  bool validate_only = 2;

  // Required. Mask of fields to update. At least one path must be supplied in
  // this field. The elements of the repeated paths field can only include these
  // fields from [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool]:
  //
  //  *.  `annotations`.
  //  *   `version`.
  //  *   `autoscaling.min_node_count`.
  //  *   `autoscaling.max_node_count`.
  //  *   `config.ssh_config.authorized_key`.
  //  *   `management.auto_repair`.
  //  *   `management`.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for `AzureClusters.GetAzureNodePool` method.
message GetAzureNodePoolRequest {
  // Required. The name of the
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool] resource to
  // describe.
  //
  // `AzureNodePool` names are formatted as
  // `projects/<project-id>/locations/<region>/azureClusters/<cluster-id>/azureNodePools/<node-pool-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureNodePool"
    }
  ];
}

// Request message for `AzureClusters.ListAzureNodePools` method.
message ListAzureNodePoolsRequest {
  // Required. The parent `AzureCluster` which owns this collection of
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool] resources.
  //
  // `AzureCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/azureClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AzureNodePool"
    }
  ];

  // The maximum number of items to return.
  //
  // If not specified, a default value of 50 will be used by the service.
  // Regardless of the pageSize value, the response can include a partial list
  // and a caller should only rely on response's
  // [nextPageToken][google.cloud.gkemulticloud.v1.ListAzureNodePoolsResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2;

  // The `nextPageToken` value returned from a previous
  // [azureNodePools.list][google.cloud.gkemulticloud.v1.AzureClusters.ListAzureNodePools]
  // request, if any.
  string page_token = 3;
}

// Response message for `AzureClusters.ListAzureNodePools` method.
message ListAzureNodePoolsResponse {
  // A list of [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool]
  // resources in the specified `AzureCluster`.
  repeated AzureNodePool azure_node_pools = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Request message for `AzureClusters.DeleteAzureNodePool` method.
message DeleteAzureNodePoolRequest {
  // Required. The resource name the
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool] to delete.
  //
  // `AzureNodePool` names are formatted as
  // `projects/<project-id>/locations/<region>/azureClusters/<cluster-id>/azureNodePools/<node-pool-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureNodePool"
    }
  ];

  // If set, only validate the request, but do not actually delete the node
  // pool.
  bool validate_only = 2;

  // If set to true, and the
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool] resource is
  // not found, the request will succeed but no action will be taken on the
  // server and a completed [Operation][google.longrunning.Operation] will be
  // returned.
  //
  // Useful for idempotent deletion.
  bool allow_missing = 3;

  // The current ETag of the
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool].
  //
  // Allows clients to perform deletions through optimistic concurrency control.
  //
  // If the provided ETag does not match the current etag of the node pool,
  // the request will fail and an ABORTED error will be returned.
  string etag = 4;

  // Optional. If set to true, the deletion of
  // [AzureNodePool][google.cloud.gkemulticloud.v1.AzureNodePool] resource will
  // succeed even if errors occur during deleting in node pool resources. Using
  // this parameter may result in orphaned resources in the node pool.
  bool ignore_errors = 5 [(google.api.field_behavior) = OPTIONAL];
}

// GetAzureOpenIdConfigRequest gets the OIDC discovery document for the
// cluster. See the OpenID Connect Discovery 1.0 specification for details.
message GetAzureOpenIdConfigRequest {
  // Required. The AzureCluster, which owns the OIDC discovery document.
  // Format:
  // projects/<project-id>/locations/<region>/azureClusters/<cluster-id>
  string azure_cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureCluster"
    }
  ];
}

// GetAzureJsonWebKeysRequest gets the public component of the keys used by the
// cluster to sign token requests. This will be the jwks_uri for the discover
// document returned by getOpenIDConfig. See the OpenID Connect
// Discovery 1.0 specification for details.
message GetAzureJsonWebKeysRequest {
  // Required. The AzureCluster, which owns the JsonWebKeys.
  // Format:
  // `projects/<project-id>/locations/<region>/azureClusters/<cluster-id>`
  string azure_cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureCluster"
    }
  ];
}

// GetAzureServerConfigRequest gets the server config of GKE cluster on Azure.
message GetAzureServerConfigRequest {
  // Required. The name of the
  // [AzureServerConfig][google.cloud.gkemulticloud.v1.AzureServerConfig]
  // resource to describe.
  //
  // `AzureServerConfig` names are formatted as
  // `projects/<project-id>/locations/<region>/azureServerConfig`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureServerConfig"
    }
  ];
}

// Request message for `AzureClusters.CreateAzureClient` method.
message CreateAzureClientRequest {
  // Required. The parent location where this
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] resource will be
  // created.
  //
  // Location names are formatted as `projects/<project-id>/locations/<region>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AzureClient"
    }
  ];

  // Required. The specification of the
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] to create.
  AzureClient azure_client = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A client provided ID the resource. Must be unique within the
  // parent resource.
  //
  // The provided ID will be part of the
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] resource name
  // formatted as
  // `projects/<project-id>/locations/<region>/azureClients/<client-id>`.
  //
  // Valid characters are `/[a-z][0-9]-/`. Cannot be longer than 63 characters.
  string azure_client_id = 4 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but do not actually create the client.
  bool validate_only = 3;
}

// Request message for `AzureClusters.GetAzureClient` method.
message GetAzureClientRequest {
  // Required. The name of the
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] resource to
  // describe.
  //
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] names are
  // formatted as
  // `projects/<project-id>/locations/<region>/azureClients/<client-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureClient"
    }
  ];
}

// Request message for `AzureClusters.ListAzureClients` method.
message ListAzureClientsRequest {
  // Required. The parent location which owns this collection of
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] resources.
  //
  // Location names are formatted as `projects/<project-id>/locations/<region>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AzureClient"
    }
  ];

  // The maximum number of items to return.
  //
  // If not specified, a default value of 50 will be used by the service.
  // Regardless of the pageSize value, the response can include a partial list
  // and a caller should only rely on response's
  // [nextPageToken][google.cloud.gkemulticloud.v1.ListAzureClientsResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2;

  // The `nextPageToken` value returned from a previous
  // [azureClients.list][google.cloud.gkemulticloud.v1.AzureClusters.ListAzureClients]
  // request, if any.
  string page_token = 3;
}

// Response message for `AzureClusters.ListAzureClients` method.
message ListAzureClientsResponse {
  // A list of [AzureClient][google.cloud.gkemulticloud.v1.AzureClient]
  // resources in the specified Google Cloud project and region region.
  repeated AzureClient azure_clients = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Request message for `AzureClusters.DeleteAzureClient` method.
message DeleteAzureClientRequest {
  // Required. The resource name the
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] to delete.
  //
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] names are
  // formatted as
  // `projects/<project-id>/locations/<region>/azureClients/<client-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureClient"
    }
  ];

  // If set to true, and the
  // [AzureClient][google.cloud.gkemulticloud.v1.AzureClient] resource is not
  // found, the request will succeed but no action will be taken on the server
  // and a completed [Operation][google.longrunning.Operation] will be returned.
  //
  // Useful for idempotent deletion.
  bool allow_missing = 2;

  // If set, only validate the request, but do not actually delete the resource.
  bool validate_only = 3;
}

// Request message for `AzureClusters.GenerateAzureAccessToken` method.
message GenerateAzureAccessTokenRequest {
  // Required. The name of the
  // [AzureCluster][google.cloud.gkemulticloud.v1.AzureCluster] resource to
  // authenticate to.
  //
  // `AzureCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/azureClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string azure_cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureCluster"
    }
  ];
}

// Response message for `AzureClusters.GenerateAzureAccessToken` method.
message GenerateAzureAccessTokenResponse {
  // Output only. Access token to authenticate to k8s api-server.
  string access_token = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp at which the token will expire.
  google.protobuf.Timestamp expiration_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

message GenerateAzureClusterAgentTokenRequest {
  // Required.
  string azure_cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AzureCluster"
    }
  ];

  // Required.
  string subject_token = 2 [(google.api.field_behavior) = REQUIRED];

  // Required.
  string subject_token_type = 3 [(google.api.field_behavior) = REQUIRED];

  // Required.
  string version = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional.
  string node_pool_id = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string grant_type = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string audience = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string scope = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string requested_token_type = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string options = 10 [(google.api.field_behavior) = OPTIONAL];
}

message GenerateAzureClusterAgentTokenResponse {
  string access_token = 1;

  int32 expires_in = 2;

  string token_type = 3;
}
