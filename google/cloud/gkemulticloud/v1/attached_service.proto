// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkemulticloud.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/gkemulticloud/v1/attached_resources.proto";
import "google/cloud/gkemulticloud/v1/common_resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.GkeMultiCloud.V1";
option go_package = "cloud.google.com/go/gkemulticloud/apiv1/gkemulticloudpb;gkemulticloudpb";
option java_multiple_files = true;
option java_outer_classname = "AttachedServiceProto";
option java_package = "com.google.cloud.gkemulticloud.v1";
option php_namespace = "Google\\Cloud\\GkeMultiCloud\\V1";
option ruby_package = "Google::Cloud::GkeMultiCloud::V1";

// The AttachedClusters API provides a single centrally managed service
// to register and manage Anthos attached clusters that run on customer's owned
// infrastructure.
service AttachedClusters {
  option (google.api.default_host) = "gkemulticloud.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // on a given Google Cloud Platform project and region.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc CreateAttachedCluster(CreateAttachedClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/attachedClusters"
      body: "attached_cluster"
    };
    option (google.api.method_signature) =
        "parent,attached_cluster,attached_cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "AttachedCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster].
  rpc UpdateAttachedCluster(UpdateAttachedClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{attached_cluster.name=projects/*/locations/*/attachedClusters/*}"
      body: "attached_cluster"
    };
    option (google.api.method_signature) = "attached_cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AttachedCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Imports creates a new
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // by importing an existing Fleet Membership resource.
  //
  // Attached Clusters created before the introduction of the Anthos Multi-Cloud
  // API can be imported through this method.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc ImportAttachedCluster(ImportAttachedClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/attachedClusters:import"
      body: "*"
    };
    option (google.api.method_signature) = "parent,fleet_membership";
    option (google.longrunning.operation_info) = {
      response_type: "AttachedCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Describes a specific
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource.
  rpc GetAttachedCluster(GetAttachedClusterRequest) returns (AttachedCluster) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/attachedClusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster]
  // resources on a given Google Cloud project and region.
  rpc ListAttachedClusters(ListAttachedClustersRequest)
      returns (ListAttachedClustersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/attachedClusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a specific
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource.
  //
  // If successful, the response contains a newly created
  // [Operation][google.longrunning.Operation] resource that can be
  // described to track the status of the operation.
  rpc DeleteAttachedCluster(DeleteAttachedClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/attachedClusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns information, such as supported Kubernetes versions, on a given
  // Google Cloud location.
  rpc GetAttachedServerConfig(GetAttachedServerConfigRequest)
      returns (AttachedServerConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/attachedServerConfig}"
    };
    option (google.api.method_signature) = "name";
  }

  // Generates the install manifest to be installed on the target cluster.
  rpc GenerateAttachedClusterInstallManifest(
      GenerateAttachedClusterInstallManifestRequest)
      returns (GenerateAttachedClusterInstallManifestResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}:generateAttachedClusterInstallManifest"
    };
    option (google.api.method_signature) = "parent,attached_cluster_id";
  }

  // Generates an access token for a cluster agent.
  rpc GenerateAttachedClusterAgentToken(
      GenerateAttachedClusterAgentTokenRequest)
      returns (GenerateAttachedClusterAgentTokenResponse) {
    option (google.api.http) = {
      post: "/v1/{attached_cluster=projects/*/locations/*/attachedClusters/*}:generateAttachedClusterAgentToken"
      body: "*"
    };
  }
}

// Request message for `AttachedClusters.GenerateAttachedClusterInstallManifest`
// method.
message GenerateAttachedClusterInstallManifestRequest {
  // Required. The parent location where this
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // will be created.
  //
  // Location names are formatted as `projects/<project-id>/locations/<region>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AttachedCluster"
    }
  ];

  // Required. A client provided ID of the resource. Must be unique within the
  // parent resource.
  //
  // The provided ID will be part of the
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // name formatted as
  // `projects/<project-id>/locations/<region>/attachedClusters/<cluster-id>`.
  //
  // Valid characters are `/[a-z][0-9]-/`. Cannot be longer than 63 characters.
  //
  // When generating an install manifest for importing an existing Membership
  // resource, the attached_cluster_id field must be the Membership id.
  //
  // Membership names are formatted as
  // `projects/<project-id>/locations/<region>/memberships/<membership-id>`.
  string attached_cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The platform version for the cluster (e.g. `1.19.0-gke.1000`).
  //
  // You can list all supported versions on a given Google Cloud region by
  // calling
  // [GetAttachedServerConfig][google.cloud.gkemulticloud.v1.AttachedClusters.GetAttachedServerConfig].
  string platform_version = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Proxy configuration for outbound HTTP(S) traffic.
  AttachedProxyConfig proxy_config = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// `AttachedClusters.GenerateAttachedClusterInstallManifest` method.
message GenerateAttachedClusterInstallManifestResponse {
  // A set of Kubernetes resources (in YAML format) to be applied
  // to the cluster to be attached.
  string manifest = 1;
}

// Request message for `AttachedClusters.CreateAttachedCluster` method.
message CreateAttachedClusterRequest {
  // Required. The parent location where this
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // will be created.
  //
  // Location names are formatted as `projects/<project-id>/locations/<region>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AttachedCluster"
    }
  ];

  // Required. The specification of the
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] to create.
  AttachedCluster attached_cluster = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A client provided ID the resource. Must be unique within the
  // parent resource.
  //
  // The provided ID will be part of the
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // name formatted as
  // `projects/<project-id>/locations/<region>/attachedClusters/<cluster-id>`.
  //
  // Valid characters are `/[a-z][0-9]-/`. Cannot be longer than 63 characters.
  string attached_cluster_id = 3 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but do not actually create the cluster.
  bool validate_only = 4;
}

// Request message for `AttachedClusters.ImportAttachedCluster` method.
message ImportAttachedClusterRequest {
  // Required. The parent location where this
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // will be created.
  //
  // Location names are formatted as `projects/<project-id>/locations/<region>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AttachedCluster"
    }
  ];

  // If set, only validate the request, but do not actually import the cluster.
  bool validate_only = 2;

  // Required. The name of the fleet membership resource to import.
  string fleet_membership = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The platform version for the cluster (e.g. `1.19.0-gke.1000`).
  //
  // You can list all supported versions on a given Google Cloud region by
  // calling
  // [GetAttachedServerConfig][google.cloud.gkemulticloud.v1.AttachedClusters.GetAttachedServerConfig].
  string platform_version = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The Kubernetes distribution of the underlying attached cluster.
  //
  // Supported values: ["eks", "aks", "generic"].
  string distribution = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. Proxy configuration for outbound HTTP(S) traffic.
  AttachedProxyConfig proxy_config = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for `AttachedClusters.UpdateAttachedCluster` method.
message UpdateAttachedClusterRequest {
  // Required. The
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // to update.
  AttachedCluster attached_cluster = 1 [(google.api.field_behavior) = REQUIRED];

  // If set, only validate the request, but do not actually update the cluster.
  bool validate_only = 2;

  // Required. Mask of fields to update. At least one path must be supplied in
  // this field. The elements of the repeated paths field can only include these
  // fields from
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster]:
  //
  //  *   `annotations`.
  //  *   `authorization.admin_groups`.
  //  *   `authorization.admin_users`.
  //  *   `binary_authorization.evaluation_mode`.
  //  *   `description`.
  //  *   `logging_config.component_config.enable_components`.
  //  *   `monitoring_config.managed_prometheus_config.enabled`.
  //  *   `platform_version`.
  //  *   `proxy_config.kubernetes_secret.name`.
  //  *   `proxy_config.kubernetes_secret.namespace`.
  //  *   `security_posture_config.vulnerability_mode`
  //  *   `monitoring_config.cloud_monitoring_config.enabled`
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for `AttachedClusters.GetAttachedCluster` method.
message GetAttachedClusterRequest {
  // Required. The name of the
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // to describe.
  //
  // `AttachedCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/attachedClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AttachedCluster"
    }
  ];
}

// Request message for `AttachedClusters.ListAttachedClusters` method.
message ListAttachedClustersRequest {
  // Required. The parent location which owns this collection of
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resources.
  //
  // Location names are formatted as `projects/<project-id>/locations/<region>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkemulticloud.googleapis.com/AttachedCluster"
    }
  ];

  // The maximum number of items to return.
  //
  // If not specified, a default value of 50 will be used by the service.
  // Regardless of the pageSize value, the response can include a partial list
  // and a caller should only rely on response's
  // [nextPageToken][google.cloud.gkemulticloud.v1.ListAttachedClustersResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2;

  // The `nextPageToken` value returned from a previous
  // [attachedClusters.list][google.cloud.gkemulticloud.v1.AttachedClusters.ListAttachedClusters]
  // request, if any.
  string page_token = 3;
}

// Response message for `AttachedClusters.ListAttachedClusters` method.
message ListAttachedClustersResponse {
  // A list of [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster]
  // resources in the specified Google Cloud Platform project and region region.
  repeated AttachedCluster attached_clusters = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Request message for `AttachedClusters.DeleteAttachedCluster` method.
message DeleteAttachedClusterRequest {
  // Required. The resource name the
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] to delete.
  //
  // `AttachedCluster` names are formatted as
  // `projects/<project-id>/locations/<region>/attachedClusters/<cluster-id>`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud Platform resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AttachedCluster"
    }
  ];

  // If set, only validate the request, but do not actually delete the resource.
  bool validate_only = 2;

  // If set to true, and the
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // is not found, the request will succeed but no action will be taken on the
  // server and a completed [Operation][google.longrunning.Operation] will be
  // returned.
  //
  // Useful for idempotent deletion.
  bool allow_missing = 3;

  // If set to true, the deletion of
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster] resource
  // will succeed even if errors occur during deleting in cluster resources.
  // Using this parameter may result in orphaned resources in the cluster.
  bool ignore_errors = 5;

  // The current etag of the
  // [AttachedCluster][google.cloud.gkemulticloud.v1.AttachedCluster].
  //
  // Allows clients to perform deletions through optimistic concurrency control.
  //
  // If the provided etag does not match the current etag of the cluster,
  // the request will fail and an ABORTED error will be returned.
  string etag = 4;
}

// GetAttachedServerConfigRequest gets the server config for attached
// clusters.
message GetAttachedServerConfigRequest {
  // Required. The name of the
  // [AttachedServerConfig][google.cloud.gkemulticloud.v1.AttachedServerConfig]
  // resource to describe.
  //
  // `AttachedServerConfig` names are formatted as
  // `projects/<project-id>/locations/<region>/attachedServerConfig`.
  //
  // See [Resource Names](https://cloud.google.com/apis/design/resource_names)
  // for more details on Google Cloud resource names.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AttachedServerConfig"
    }
  ];
}

message GenerateAttachedClusterAgentTokenRequest {
  // Required.
  string attached_cluster = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkemulticloud.googleapis.com/AttachedCluster"
    }
  ];

  // Required.
  string subject_token = 2 [(google.api.field_behavior) = REQUIRED];

  // Required.
  string subject_token_type = 3 [(google.api.field_behavior) = REQUIRED];

  // Required.
  string version = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional.
  string grant_type = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string audience = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string scope = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string requested_token_type = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  string options = 10 [(google.api.field_behavior) = OPTIONAL];
}

message GenerateAttachedClusterAgentTokenResponse {
  string access_token = 1;

  int32 expires_in = 2;

  string token_type = 3;
}
