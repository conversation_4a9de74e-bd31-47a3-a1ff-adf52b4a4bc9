# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "gkemulticloud_proto",
    srcs = [
        "attached_resources.proto",
        "attached_service.proto",
        "aws_resources.proto",
        "aws_service.proto",
        "azure_resources.proto",
        "azure_service.proto",
        "common_resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "gkemulticloud_proto_with_info",
    deps = [
        ":gkemulticloud_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "gkemulticloud_java_proto",
    deps = [":gkemulticloud_proto"],
)

java_grpc_library(
    name = "gkemulticloud_java_grpc",
    srcs = [":gkemulticloud_proto"],
    deps = [":gkemulticloud_java_proto"],
)

java_gapic_library(
    name = "gkemulticloud_java_gapic",
    srcs = [":gkemulticloud_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "gkemulticloud_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "gkemulticloud_v1.yaml",
    test_deps = [
        ":gkemulticloud_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":gkemulticloud_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "gkemulticloud_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.gkemulticloud.v1.AttachedClustersClientHttpJsonTest",
        "com.google.cloud.gkemulticloud.v1.AttachedClustersClientTest",
        "com.google.cloud.gkemulticloud.v1.AwsClustersClientHttpJsonTest",
        "com.google.cloud.gkemulticloud.v1.AwsClustersClientTest",
        "com.google.cloud.gkemulticloud.v1.AzureClustersClientHttpJsonTest",
        "com.google.cloud.gkemulticloud.v1.AzureClustersClientTest",
    ],
    runtime_deps = [":gkemulticloud_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-gkemulticloud-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":gkemulticloud_java_gapic",
        ":gkemulticloud_java_grpc",
        ":gkemulticloud_java_proto",
        ":gkemulticloud_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "gkemulticloud_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/gkemulticloud/apiv1/gkemulticloudpb",
    protos = [":gkemulticloud_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "gkemulticloud_go_gapic",
    srcs = [":gkemulticloud_proto_with_info"],
    grpc_service_config = "gkemulticloud_grpc_service_config.json",
    importpath = "cloud.google.com/go/gkemulticloud/apiv1;gkemulticloud",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = False,
    service_yaml = "gkemulticloud_v1.yaml",
    transport = "grpc",
    deps = [
        ":gkemulticloud_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-gkemulticloud-v1-go",
    deps = [
        ":gkemulticloud_go_gapic",
        ":gkemulticloud_go_gapic_srcjar-metadata.srcjar",
        ":gkemulticloud_go_gapic_srcjar-snippets.srcjar",
        ":gkemulticloud_go_gapic_srcjar-test.srcjar",
        ":gkemulticloud_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "gkemulticloud_py_gapic",
    srcs = [":gkemulticloud_proto"],
    grpc_service_config = "gkemulticloud_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=gke_multicloud",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-gke-multicloud",
    ],
    rest_numeric_enums = False,
    service_yaml = "gkemulticloud_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "gkemulticloud_py_gapic_test",
    srcs = [
        "gkemulticloud_py_gapic_pytest.py",
        "gkemulticloud_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":gkemulticloud_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "gkemulticloud-v1-py",
    deps = [
        ":gkemulticloud_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "gkemulticloud_php_proto",
    deps = [":gkemulticloud_proto"],
)

php_gapic_library(
    name = "gkemulticloud_php_gapic",
    srcs = [":gkemulticloud_proto_with_info"],
    grpc_service_config = "gkemulticloud_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = False,
    service_yaml = "gkemulticloud_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gkemulticloud_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-gkemulticloud-v1-php",
    deps = [
        ":gkemulticloud_php_gapic",
        ":gkemulticloud_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "gkemulticloud_nodejs_gapic",
    package_name = "@google-cloud/gkemulticloud",
    src = ":gkemulticloud_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "gkemulticloud_grpc_service_config.json",
    package = "google.cloud.gkemulticloud.v1",
    rest_numeric_enums = False,
    service_yaml = "gkemulticloud_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "gkemulticloud-v1-nodejs",
    deps = [
        ":gkemulticloud_nodejs_gapic",
        ":gkemulticloud_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "gkemulticloud_ruby_proto",
    deps = [":gkemulticloud_proto"],
)

ruby_grpc_library(
    name = "gkemulticloud_ruby_grpc",
    srcs = [":gkemulticloud_proto"],
    deps = [":gkemulticloud_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "gkemulticloud_ruby_gapic",
    srcs = [":gkemulticloud_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=gkemulticloud.googleapis.com",
        "ruby-cloud-api-shortname=gkemulticloud",
        "ruby-cloud-gem-name=google-cloud-gke_multi_cloud-v1",
        "ruby-cloud-product-url=https://cloud.google.com/anthos/clusters/docs/multi-cloud",
    ],
    grpc_service_config = "gkemulticloud_grpc_service_config.json",
    rest_numeric_enums = False,
    ruby_cloud_description = "Anthos Multi-Cloud provides a way to manage Kubernetes clusters that run on AWS and Azure infrastructure using the Anthos Multi-Cloud API. Combined with Connect, you can manage Kubernetes clusters on Google Cloud, AWS, and Azure from the Google Cloud Console.",
    ruby_cloud_title = "Anthos Multi-Cloud V1",
    service_yaml = "gkemulticloud_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gkemulticloud_ruby_grpc",
        ":gkemulticloud_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-gkemulticloud-v1-ruby",
    deps = [
        ":gkemulticloud_ruby_gapic",
        ":gkemulticloud_ruby_grpc",
        ":gkemulticloud_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "gkemulticloud_csharp_proto",
    extra_opts = [],
    deps = [":gkemulticloud_proto"],
)

csharp_grpc_library(
    name = "gkemulticloud_csharp_grpc",
    srcs = [":gkemulticloud_proto"],
    deps = [":gkemulticloud_csharp_proto"],
)

csharp_gapic_library(
    name = "gkemulticloud_csharp_gapic",
    srcs = [":gkemulticloud_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "gkemulticloud_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "gkemulticloud_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gkemulticloud_csharp_grpc",
        ":gkemulticloud_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-gkemulticloud-v1-csharp",
    deps = [
        ":gkemulticloud_csharp_gapic",
        ":gkemulticloud_csharp_grpc",
        ":gkemulticloud_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "gkemulticloud_cc_proto",
    deps = [":gkemulticloud_proto"],
)

cc_grpc_library(
    name = "gkemulticloud_cc_grpc",
    srcs = [":gkemulticloud_proto"],
    grpc_only = True,
    deps = [":gkemulticloud_cc_proto"],
)
