type: google.api.Service
config_version: 3
name: gkemulticloud.googleapis.com
title: GKE Multi-Cloud API

apis:
- name: google.cloud.gkemulticloud.v1.AttachedClusters
- name: google.cloud.gkemulticloud.v1.AwsClusters
- name: google.cloud.gkemulticloud.v1.AzureClusters
- name: google.longrunning.Operations

types:
- name: google.cloud.gkemulticloud.v1.OperationMetadata

documentation:
  summary: |-
    GKE Multi-Cloud provides a way to manage Kubernetes clusters that run on
    AWS and Azure infrastructure using the GKE Multi-Cloud API.  Combined with
    Connect, you can manage Kubernetes clusters on Google Cloud, AWS, and
    Azure from the Google Cloud Console.

    When you create a cluster with GKE Multi-Cloud, Google creates the
    resources needed and brings up a cluster on your behalf.  You can deploy
    workloads with the GKE Multi-Cloud API or the gcloud and kubectl
    command-line tools.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.gkemulticloud.v1.AttachedClusters.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.gkemulticloud.v1.AwsClusters.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.gkemulticloud.v1.AzureClusters.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=997904&template=1807166
  documentation_uri: https://cloud.google.com/kubernetes-engine/multi-cloud/docs
  api_short_name: gkemulticloud
  github_label: 'api: gkemulticloud'
  doc_tag_prefix: gkemulticloud
  organization: CLOUD
  library_settings:
  - version: google.cloud.gkemulticloud.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/kubernetes-engine/multi-cloud/docs/reference/rest/v1/projects.locations.awsClusters
