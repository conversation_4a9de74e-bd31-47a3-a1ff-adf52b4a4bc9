# This build file includes a target for the Ruby wrapper library for
# google-cloud-gke_multi_cloud.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for gkemulticloud.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "gkemulticloud_ruby_wrapper",
    srcs = ["//google/cloud/gkemulticloud/v1:gkemulticloud_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=gkemulticloud.googleapis.com",
        "ruby-cloud-api-shortname=gkemulticloud",
        "ruby-cloud-gem-name=google-cloud-gke_multi_cloud",
        "ruby-cloud-product-url=https://cloud.google.com/anthos/clusters/docs/multi-cloud",
        "ruby-cloud-wrapper-of=v1:0.8",
    ],
    ruby_cloud_description = "Anthos Multi-Cloud provides a way to manage Kubernetes clusters that run on AWS and Azure infrastructure using the Anthos Multi-Cloud API. Combined with Connect, you can manage Kubernetes clusters on Google Cloud, AWS, and Azure from the Google Cloud Console.",
    ruby_cloud_title = "Anthos Multi-Cloud",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-gkemulticloud-ruby",
    deps = [
        ":gkemulticloud_ruby_wrapper",
    ],
)
