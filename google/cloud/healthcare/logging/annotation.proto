// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.healthcare.logging;

import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/healthcare/logging/loggingpb;loggingpb";
option java_package = "com.google.cloud.healthcare.logging";

// A log entry for an Annotation import long-running operation.
message ImportAnnotationLogEntry {
  // The source in Cloud Storage. For example,
  // `gs://{bucket_id}/{path/to/file}`.
  string source = 1;

  // The error code and message.
  google.rpc.Status error = 2;
}

// A log entry for an Annotation export long-running operation.
message ExportAnnotationLogEntry {
  // The destination in Cloud Storage or BigQuery.
  string destination = 1;

  // The annotation record being exported. For example:
  // `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/annotationStores/{store_id}/annotations/{annotation_id}`.
  string annotation_name = 2;

  // The error code and message.
  google.rpc.Status error = 3;
}

// A log entry for an Annotation evaluate long-running operation.
message EvaluateAnnotationLogEntry {
  // The report destination in BigQuery.
  string destination = 1;

  // The eval annotation record being evaluated. For example:
  // `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/annotationStores/{eval_store_id}/annotations/{eval_annotation_id}`.
  string eval_annotation_name = 2;

  // The golden annotation record being evaluated. For example:
  // `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/annotationStores/{golden_store_id}/annotations/{golden_annotation_id}`.
  string golden_annotation_name = 3;

  // The error code and message.
  google.rpc.Status error = 4;
}
