// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.healthcare.logging;

import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/healthcare/logging/loggingpb;loggingpb";
option java_package = "com.google.cloud.healthcare.logging";

// A log entry for a FHIR import long-running operation.
message ImportFhirLogEntry {
  // The source in Cloud Storage (for example,
  // `gs://{bucket_id}/{path/to/file}`) or BigQuery (for example,
  // `bq://{projectId}.{bqDatasetId}`).
  string source = 1;

  // The ID in the source file of the FHIR resource being imported.
  string resource_id = 2;

  // The error code and message.
  google.rpc.Status error = 3;
}

// A log entry for a FHIR export long-running operation.
message ExportFhirLogEntry {
  // The destination in Cloud Storage (for example,
  // `gs://{bucket_id}/{path/to/file}`) or BigQuery (for example,
  // `bq://{projectId}.{bqDatasetId}`).
  string destination = 1;

  // The resource being exported (e.g.
  // `projects/{projectId}/locations/{locationId}/datasets/{datasetId}/fhirStores/{fhirStoreId}/fhir/Patient/{patientId}`).
  string resource_name = 3;

  // The error code and message.
  google.rpc.Status error = 4;
}

// A log entry for a FHIR configure search long-running operation
message FhirConfigureSearchLogEntry {
  // The ID of the resource being reindexed.
  string resource_id = 1;

  // The error code and message.
  google.rpc.Status error = 2;
}

// A log entry for a FHIR store Pub/Sub notification.
message FhirNotificationLogEntry {
  // The resource being changed (for example,
  // `projects/{projectId}/locations/{locationId}/datasets/{datasetId}/fhirStores/{fhirStoreId}/fhir/Patient/{patientId}`).
  string resource_name = 1;

  // The Pub/Sub topic that the notification is published on.
  string pubsub_topic = 2;

  // The error code and message.
  google.rpc.Status error = 3;
}

// A log entry for a FHIR streaming export notification.
message FhirStreamLogEntry {
  // The resource being changed (for example,
  // `projects/{projectId}/locations/{locationId}/datasets/{datasetId}/fhirStores/{fhirStoreId}/fhir/Patient/{patientId}`).
  string resource_name = 1;

  // The destination in BigQuery (for example,
  // `bq://{projectId}.{bqDatasetId}.{bqTableId}`).
  string destination = 2;

  // The error code and message.
  google.rpc.Status error = 3;
}

// A log entry for a FHIR streaming deidentification notification.
message FhirDeidentifyStreamToStoreLogEntry {
  // The resource that changed (for example,
  // `projects/{projectId}/locations/{locationId}/datasets/{datasetId}/fhirStores/{fhirStoreId}/fhir/Patient/{patientId}`).
  string resource_name = 1;

  // The destination FHIR store name. (for example,
  // `projects/{projectId}/locations/{locationId}/datasets/{datasetId}/fhirStores/{fhirStoreId}`).
  string destination = 2;

  // The error code and message.
  google.rpc.Status error = 3;
}
