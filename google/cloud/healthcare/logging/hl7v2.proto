// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.healthcare.logging;

import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/healthcare/logging/loggingpb;loggingpb";
option java_package = "com.google.cloud.healthcare.logging";

// A log entry for a HL7v2 import long-running operation.
message ImportHl7V2LogEntry {
  // The source in Cloud Storage (for example,
  // `gs://{bucket_id}/{path/to/file}`).
  string source = 1;

  // The error code and message.
  google.rpc.Status error = 2;
}

// A log entry for a HL7v2 store Pub/Sub notification.
message Hl7V2NotificationLogEntry {
  // The HL7v2 message being created (for example,
  // `projects/{projectId}/locations/{locationId}/datasets/{datasetId}/hl7V2Stores/{hl7v2StoreId}/messages/{hl7v2MessageId}`).
  string resource_name = 1;

  // The Pub/Sub topic that the notification is published on.
  string pubsub_topic = 2;

  // The error code and message.
  google.rpc.Status error = 3;
}
