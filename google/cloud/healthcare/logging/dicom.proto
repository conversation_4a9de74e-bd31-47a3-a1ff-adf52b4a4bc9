// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.healthcare.logging;

import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/healthcare/logging/loggingpb;loggingpb";
option java_package = "com.google.cloud.healthcare.logging";

// A log entry for a DICOM import long-running operation.
message ImportDicomLogEntry {
  // The source file, in the format `gs://{bucket-id}/{path/to/file}`.
  string source = 1;

  // The error code and message.
  google.rpc.Status error = 2;
}

// A log entry for a DICOM export long-running operation.
message ExportDicomLogEntry {
  // The DICOM resource being exported.
  string resource_name = 1;

  // The error code and message.
  google.rpc.Status error = 2;
}

// A log entry for a DICOM store Pub/Sub notification.
message DicomNotificationLogEntry {
  // The DICOM resource being created.
  string resource_name = 1;

  // The Pub/Sub topic that the notification is published on.
  string pubsub_topic = 2;

  // The error code and message.
  google.rpc.Status error = 3;
}

// A log entry for a DICOM streaming export notification.
message DicomStreamLogEntry {
  // The DICOM resource being exported.
  string resource_name = 1;

  // The destination in BigQuery, in the format
  // `bq://{projectId}.{bqDatasetId}.{bqTableId}`.
  string destination = 2;

  // The error code and message.
  google.rpc.Status error = 3;
}
