// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.healthcare.logging;

import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/healthcare/logging/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "ConsentProto";
option java_package = "com.google.cloud.healthcare.logging";

// A log entry for a Consent store QueryAccessibleData long-running operation.
message QueryAccessibleDataLogEntry {
  // The resource being processed.
  string resource_name = 1;

  // The error code and message.
  google.rpc.Status error = 2;
}

// A log entry for a User Data Mapping indexing notification.
message ConsentUserDataMappingLogEntry {
  // The User Data Mapping being indexed (for example,
  // `projects/{projectId}/locations/{locationId}/datasets/{datasetId}/consentStores/{storeId}/userDataMappings/{messageId}`).
  string resource_name = 1;

  // The error code and message.
  google.rpc.Status error = 2;
}
