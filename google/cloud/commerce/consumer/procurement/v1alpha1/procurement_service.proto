// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.commerce.consumer.procurement.v1alpha1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/commerce/consumer/procurement/v1alpha1/order.proto";
import "google/longrunning/operations.proto";

option go_package = "cloud.google.com/go/commerce/consumer/procurement/apiv1alpha1/procurementpb;procurementpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.commerce.consumer.procurement.v1alpha1";

// ConsumerProcurementService allows customers to make purchases of products
// served by the Cloud Commerce platform.
//
//
// When purchases are made, the
// [ConsumerProcurementService][google.cloud.commerce.consumer.procurement.v1alpha1.ConsumerProcurementService]
// programs the appropriate backends, including both Google's own
// infrastructure, as well as third-party systems, and to enable billing setup
// for charging for the procured item.
//
service ConsumerProcurementService {
  option (google.api.default_host) =
      "cloudcommerceconsumerprocurement.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new
  // [Order][google.cloud.commerce.consumer.procurement.v1alpha1.Order].
  //
  // This API only supports GCP spend-based committed use
  // discounts specified by GCP documentation.
  //
  // The returned long-running operation is in-progress until the backend
  // completes the creation of the resource. Once completed, the order is
  // in
  // [OrderState.ORDER_STATE_ACTIVE][google.cloud.commerce.consumer.procurement.v1alpha1.OrderState.ORDER_STATE_ACTIVE].
  // In case of failure, the order resource will be removed.
  rpc PlaceOrder(PlaceOrderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=billingAccounts/*}/orders:place"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Order"
      metadata_type: "PlaceOrderMetadata"
    };
  }

  // Returns the requested
  // [Order][google.cloud.commerce.consumer.procurement.v1alpha1.Order]
  // resource.
  rpc GetOrder(GetOrderRequest) returns (Order) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=billingAccounts/*/orders/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [Order][google.cloud.commerce.consumer.procurement.v1alpha1.Order]
  // resources that the user has access to, within the scope of the parent
  // resource.
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=billingAccounts/*}/orders"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [ConsumerProcurementService.PlaceOrder][google.cloud.commerce.consumer.procurement.v1alpha1.ConsumerProcurementService.PlaceOrder].
message PlaceOrderRequest {
  // Required. The resource name of the parent resource.
  // This field has the form  `billingAccounts/{billing-account-id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbilling.googleapis.com/BillingAccount"
    }
  ];

  // Required. The user-specified name of the order being placed.
  string display_name = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. Places order for offer. Required when an offer-based order is
  // being placed.
  repeated LineItemInfo line_item_info = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A unique identifier for this request.
  // The server will ignore subsequent requests that provide a duplicate request
  // ID for at least 120 minutes after the first request.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format).
  string request_id = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Message stored in the metadata field of the Operation returned by
// [ConsumerProcurementService.PlaceOrder][google.cloud.commerce.consumer.procurement.v1alpha1.ConsumerProcurementService.PlaceOrder].
message PlaceOrderMetadata {}

// Request message for
// [ConsumerProcurementService.GetOrder][google.cloud.commerce.consumer.procurement.v1alpha1.ConsumerProcurementService.GetOrder]
message GetOrderRequest {
  // Required. The name of the order to retrieve.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ConsumerProcurementService.ListOrders][google.cloud.commerce.consumer.procurement.v1alpha1.ConsumerProcurementService.ListOrders].
message ListOrdersRequest {
  // Required. The parent resource to query for orders.
  // This field has the form `billingAccounts/{billing-account-id}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of entries requested.
  // The default page size is 25 and the maximum page size is 200.
  int32 page_size = 2;

  // The token for fetching the next page.
  string page_token = 3;

  // Filter that you can use to limit the list request.
  //
  // A query string that can match a selected set of attributes
  // with string values. For example, `display_name=abc`.
  // Supported query attributes are
  //
  // * `display_name`
  //
  // If the query contains special characters other than letters,
  // underscore, or digits, the phrase must be quoted with double quotes. For
  // example, `display_name="foo:bar"`, where the display name needs to be
  // quoted because it contains special character colon.
  //
  // Queries can be combined with `OR`, and `NOT` to form more complex queries.
  // You can also group them to force a desired evaluation order.
  // For example, `display_name=abc OR display_name=def`.
  string filter = 4;
}

// Response message for
// [ConsumerProcurementService.ListOrders][google.cloud.commerce.consumer.procurement.v1alpha1.ConsumerProcurementService.ListOrders].
message ListOrdersResponse {
  // The list of orders in this response.
  repeated Order orders = 1;

  // The token for fetching the next page.
  string next_page_token = 2;
}
