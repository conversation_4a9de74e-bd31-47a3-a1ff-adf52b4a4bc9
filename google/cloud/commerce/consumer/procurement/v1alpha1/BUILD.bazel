# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "procurement_proto",
    srcs = [
        "order.proto",
        "procurement_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "procurement_proto_with_info",
    deps = [
        ":procurement_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "procurement_java_proto",
    deps = [":procurement_proto"],
)

java_grpc_library(
    name = "procurement_java_grpc",
    srcs = [":procurement_proto"],
    deps = [":procurement_java_proto"],
)

java_gapic_library(
    name = "procurement_java_gapic",
    srcs = [":procurement_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "commerceconsumerprocurement_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudcommerceconsumerprocurement_v1alpha1.yaml",
    test_deps = [
        ":procurement_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":procurement_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "procurement_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.commerce.consumer.procurement.v1alpha1.ConsumerProcurementServiceClientHttpJsonTest",
        "com.google.cloud.commerce.consumer.procurement.v1alpha1.ConsumerProcurementServiceClientTest",
    ],
    runtime_deps = [":procurement_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-consumer-procurement-v1alpha1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":procurement_java_gapic",
        ":procurement_java_grpc",
        ":procurement_java_proto",
        ":procurement_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "procurement_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/commerce/consumer/procurement/apiv1alpha1/procurementpb",
    protos = [":procurement_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "procurement_go_gapic",
    srcs = [":procurement_proto_with_info"],
    grpc_service_config = "commerceconsumerprocurement_grpc_service_config.json",
    importpath = "cloud.google.com/go/commerce/consumer/procurement/apiv1alpha1;procurement",
    metadata = True,
    release_level = "alpha",
    rest_numeric_enums = True,
    service_yaml = "cloudcommerceconsumerprocurement_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":procurement_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-consumer-procurement-v1alpha1-go",
    deps = [
        ":procurement_go_gapic",
        ":procurement_go_gapic_srcjar-metadata.srcjar",
        ":procurement_go_gapic_srcjar-snippets.srcjar",
        ":procurement_go_gapic_srcjar-test.srcjar",
        ":procurement_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "procurement_py_gapic",
    srcs = [":procurement_proto"],
    grpc_service_config = "commerceconsumerprocurement_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=commerce_consumer_procurement",
        "python-gapic-namespace=google.cloud",
    ],
    rest_numeric_enums = True,
    service_yaml = "cloudcommerceconsumerprocurement_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "procurement_py_gapic_test",
    srcs = [
        "procurement_py_gapic_pytest.py",
        "procurement_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":procurement_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "consumer-procurement-v1alpha1-py",
    deps = [
        ":procurement_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "procurement_php_proto",
    deps = [":procurement_proto"],
)

php_gapic_library(
    name = "procurement_php_gapic",
    srcs = [":procurement_proto_with_info"],
    grpc_service_config = "commerceconsumerprocurement_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudcommerceconsumerprocurement_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":procurement_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-consumer-procurement-v1alpha1-php",
    deps = [
        ":procurement_php_gapic",
        ":procurement_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "procurement_nodejs_gapic",
    package_name = "@google-cloud/procurement",
    src = ":procurement_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "commerceconsumerprocurement_grpc_service_config.json",
    package = "google.cloud.commerce.consumer.procurement.v1alpha1",
    rest_numeric_enums = True,
    service_yaml = "cloudcommerceconsumerprocurement_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "consumer-procurement-v1alpha1-nodejs",
    deps = [
        ":procurement_nodejs_gapic",
        ":procurement_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "procurement_ruby_proto",
    deps = [":procurement_proto"],
)

ruby_grpc_library(
    name = "procurement_ruby_grpc",
    srcs = [":procurement_proto"],
    deps = [":procurement_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "procurement_ruby_gapic",
    srcs = [":procurement_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-consumer-procurement-v1alpha1"],
    grpc_service_config = "commerceconsumerprocurement_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudcommerceconsumerprocurement_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":procurement_ruby_grpc",
        ":procurement_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-consumer-procurement-v1alpha1-ruby",
    deps = [
        ":procurement_ruby_gapic",
        ":procurement_ruby_grpc",
        ":procurement_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "procurement_csharp_proto",
    extra_opts = [],
    deps = [":procurement_proto"],
)

csharp_grpc_library(
    name = "procurement_csharp_grpc",
    srcs = [":procurement_proto"],
    deps = [":procurement_csharp_proto"],
)

csharp_gapic_library(
    name = "procurement_csharp_gapic",
    srcs = [":procurement_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "commerceconsumerprocurement_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudcommerceconsumerprocurement_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":procurement_csharp_grpc",
        ":procurement_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-consumer-procurement-v1alpha1-csharp",
    deps = [
        ":procurement_csharp_gapic",
        ":procurement_csharp_grpc",
        ":procurement_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "procurement_cc_proto",
    deps = [":procurement_proto"],
)

cc_grpc_library(
    name = "procurement_cc_grpc",
    srcs = [":procurement_proto"],
    grpc_only = True,
    deps = [":procurement_cc_proto"],
)
