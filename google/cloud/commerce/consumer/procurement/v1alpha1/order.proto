// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.commerce.consumer.procurement.v1alpha1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/commerce/consumer/procurement/apiv1alpha1/procurementpb;procurementpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.commerce.consumer.procurement.v1alpha1";
option (google.api.resource_definition) = {
  type: "commerceoffercatalog.googleapis.com/Offer"
  pattern: "services/{service}/standardOffers/{offer}"
  pattern: "billingAccounts/{consumer_billing_account}/offers/{offer}"
};

// Type of a line item change.
enum LineItemChangeType {
  // Sentinel value. Do not use.
  LINE_ITEM_CHANGE_TYPE_UNSPECIFIED = 0;

  // The change is to create a new line item.
  LINE_ITEM_CHANGE_TYPE_CREATE = 1;

  // The change is to update an existing line item.
  LINE_ITEM_CHANGE_TYPE_UPDATE = 2;

  // The change is to cancel an existing line item.
  LINE_ITEM_CHANGE_TYPE_CANCEL = 3;

  // The change is to revert a cancellation.
  LINE_ITEM_CHANGE_TYPE_REVERT_CANCELLATION = 4;
}

// State of a change.
enum LineItemChangeState {
  // Sentinel value. Do not use.
  LINE_ITEM_CHANGE_STATE_UNSPECIFIED = 0;

  // Change is in this state when a change is initiated and waiting for
  // partner approval.
  LINE_ITEM_CHANGE_STATE_PENDING_APPROVAL = 1;

  // Change is in this state after it's approved by the partner or auto-approved
  // but before it takes effect. The change can be overwritten
  // or cancelled depending on the new line item info property (pending Private
  // Offer change cannot be cancelled and can only be overwritten by another
  // Private Offer).
  LINE_ITEM_CHANGE_STATE_APPROVED = 2;

  // Change is in this state after it's been activated.
  LINE_ITEM_CHANGE_STATE_COMPLETED = 3;

  // Change is in this state if it was rejected by the partner.
  LINE_ITEM_CHANGE_STATE_REJECTED = 4;

  // Change is in this state if it was abandoned by the user.
  LINE_ITEM_CHANGE_STATE_ABANDONED = 5;

  // Change is in this state if it's currently being provisioned downstream. The
  // change can't be overwritten or cancelled when it's in this state.
  LINE_ITEM_CHANGE_STATE_ACTIVATING = 6;
}

// Predefined types for line item change state reason.
enum LineItemChangeStateReasonType {
  // Default value, indicating there's no predefined type for change state
  // reason.
  LINE_ITEM_CHANGE_STATE_REASON_TYPE_UNSPECIFIED = 0;

  // Change is in current state due to term expiration.
  LINE_ITEM_CHANGE_STATE_REASON_TYPE_EXPIRED = 1;

  // Change is in current state due to user-initiated cancellation.
  LINE_ITEM_CHANGE_STATE_REASON_TYPE_USER_CANCELLED = 2;

  // Change is in current state due to system-initiated cancellation.
  LINE_ITEM_CHANGE_STATE_REASON_TYPE_SYSTEM_CANCELLED = 3;
}

// Represents a purchase made by a customer on Cloud Marketplace.
// Creating an order makes sure that both the Google backend systems
// as well as external service provider's systems (if needed) allow use of
// purchased products and ensures the appropriate billing events occur.
//
// An Order can be made against one Product with multiple add-ons (optional) or
// one Quote which might reference multiple products.
//
// Customers typically choose a price plan for each Product purchased when
// they create an order and can change their plan later, if the product allows.
message Order {
  option (google.api.resource) = {
    type: "cloudcommerceconsumerprocurement.googleapis.com/Order"
    pattern: "billingAccounts/{billing_account}/orders/{order}"
  };

  // Output only. The resource name of the order.
  // Has the form
  // `billingAccounts/{billing_account}/orders/{order}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The user-specified name of the order.
  string display_name = 10 [(google.api.field_behavior) = REQUIRED];

  // Output only. The items being purchased.
  repeated LineItem line_items = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Line items that were cancelled.
  repeated LineItem cancelled_line_items = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The creation timestamp.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The weak etag of the order.
  string etag = 11;
}

// A single item within an order.
message LineItem {
  // Output only. Line item ID.
  string line_item_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state and information of this item. It tells what,
  // e.g. which offer, is currently effective.
  LineItemInfo line_item_info = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A change made on the item which is pending and not yet
  // effective. Absence of this field indicates the line item is not undergoing
  // a change.
  LineItemChange pending_change = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Changes made on the item that are not pending anymore which
  // might be because they already took effect, were reverted by the customer,
  // or were rejected by the partner. No more operations are allowed on these
  // changes.
  repeated LineItemChange change_history = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A change made on a line item.
message LineItemChange {
  // Output only. Change ID.
  // All changes made within one order update operation have the same change_id.
  string change_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Type of the change to make.
  LineItemChangeType change_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Line item info before the change.
  LineItemInfo old_line_item_info = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Line item info after the change.
  LineItemInfo new_line_item_info = 4;

  // Output only. State of the change.
  LineItemChangeState change_state = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provider-supplied message explaining the LineItemChange's
  // state. Mainly used to communicate progress and ETA for provisioning in the
  // case of `PENDING_APPROVAL`, and to explain why the change request was
  // denied or canceled in the case of `REJECTED` and `CANCELED` states.
  string state_reason = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Predefined enum types for why this line item change is in
  // current state. For example, a line item change's state could be
  // `LINE_ITEM_CHANGE_STATE_COMPLETED` because of end-of-term expiration,
  // immediate cancellation initiated by the user, or system-initiated
  // cancellation.
  LineItemChangeStateReasonType change_state_reason_type = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A time at which the change became or will become (in case of
  // pending change) effective.
  google.protobuf.Timestamp change_effective_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when change was initiated.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when change was updated, e.g. approved/rejected by
  // partners or cancelled by the user.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Line item information.
message LineItemInfo {
  // Optional. The name of the offer can have either of these formats:
  // 'billingAccounts/{billing_account}/offers/{offer}',
  // or 'services/{service}/standardOffers/{offer}'.
  string offer = 13 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "commerceoffercatalog.googleapis.com/Offer"
    }
  ];

  // Optional. User-provided parameters.
  repeated Parameter parameters = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Information about the subscription created, if applicable.
  Subscription subscription = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// User-provided Parameters.
message Parameter {
  message Value {
    // The kind of value.
    oneof kind {
      // Represents an int64 value.
      int64 int64_value = 3;

      // Represents a string value.
      string string_value = 4;

      // Represents a double value.
      double double_value = 5;
    }
  }

  // Name of the parameter.
  string name = 1;

  // Value of parameter.
  Value value = 2;
}

// Subscription information.
message Subscription {
  // The timestamp when the subscription begins, if applicable.
  google.protobuf.Timestamp start_time = 3;

  // The timestamp when the subscription ends, if applicable.
  google.protobuf.Timestamp end_time = 1;

  // Whether auto renewal is enabled by user choice on current subscription.
  // This field indicates order/subscription status after pending plan change is
  // cancelled or rejected.
  bool auto_renewal_enabled = 2;
}
