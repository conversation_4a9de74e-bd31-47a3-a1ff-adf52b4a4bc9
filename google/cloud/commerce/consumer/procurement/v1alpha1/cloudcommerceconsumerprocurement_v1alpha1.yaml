type: google.api.Service
config_version: 3
name: cloudcommerceconsumerprocurement.googleapis.com
title: Cloud Commerce Consumer Procurement API

apis:
- name: google.cloud.commerce.consumer.procurement.v1alpha1.ConsumerProcurementService
- name: google.longrunning.Operations

types:
- name: google.cloud.commerce.consumer.procurement.v1alpha1.PlaceOrderMetadata

documentation:
  summary: Enables consumers to procure products served by Cloud Marketplace platform

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1alpha1/{name=billingAccounts/*/orders/*/operations/*}'
    additional_bindings:
    - get: '/v1alpha1/{name=billingAccounts/*/orders/*/orderAttributions/*/operations/*}'

authentication:
  rules:
  - selector: 'google.cloud.commerce.consumer.procurement.v1alpha1.ConsumerProcurementService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
