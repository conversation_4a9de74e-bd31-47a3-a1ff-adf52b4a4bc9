type: google.api.Service
config_version: 3
name: cloudcommerceconsumerprocurement.googleapis.com
title: Cloud Commerce Consumer Procurement API

apis:
- name: google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService
- name: google.cloud.commerce.consumer.procurement.v1.LicenseManagementService
- name: google.longrunning.Operations

types:
- name: google.cloud.commerce.consumer.procurement.v1.PlaceOrderMetadata

documentation:
  summary: Enables consumers to procure products served by Cloud Marketplace platform

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=billingAccounts/*/orders/*/operations/*}'

authentication:
  rules:
  - selector: 'google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.commerce.consumer.procurement.v1.LicenseManagementService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1396141
  documentation_uri: https://cloud.google.com/marketplace/docs/
  api_short_name: cloudcommerceconsumerprocurement
  github_label: 'api: cloudcommerceconsumerprocurement'
  doc_tag_prefix: cloudcommerceconsumerprocurement
  organization: CLOUD
  library_settings:
  - version: google.cloud.commerce.consumer.procurement.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
