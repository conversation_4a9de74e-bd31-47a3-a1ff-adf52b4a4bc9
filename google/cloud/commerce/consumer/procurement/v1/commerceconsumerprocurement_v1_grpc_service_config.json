{"methodConfig": [{"name": [{"service": "google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService", "method": "GetOrder"}, {"service": "google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService", "method": "ListOrders"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService", "method": "PlaceOrder"}], "timeout": "60s"}]}