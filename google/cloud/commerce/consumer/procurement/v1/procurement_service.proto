// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.commerce.consumer.procurement.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/commerce/consumer/procurement/v1/order.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.Commerce.Consumer.Procurement.V1";
option go_package = "cloud.google.com/go/commerce/consumer/procurement/apiv1/procurementpb;procurementpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.commerce.consumer.procurement.v1";
option php_namespace = "Google\\Cloud\\Commerce\\Consumer\\Procurement\\V1";
option ruby_package = "Google::Cloud::Commerce::Consumer::Procurement::V1";

// ConsumerProcurementService allows customers to make purchases of products
// served by the Cloud Commerce platform.
//
//
// When purchases are made, the
// [ConsumerProcurementService][google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService]
// programs the appropriate backends, including both Google's own
// infrastructure, as well as third-party systems, and to enable billing setup
// for charging for the procured item.
//
service ConsumerProcurementService {
  option (google.api.default_host) =
      "cloudcommerceconsumerprocurement.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new [Order][google.cloud.commerce.consumer.procurement.v1.Order].
  //
  // This API only supports GCP spend-based committed use
  // discounts specified by GCP documentation.
  //
  // The returned long-running operation is in-progress until the backend
  // completes the creation of the resource. Once completed, the order is
  // in
  // [OrderState.ORDER_STATE_ACTIVE][google.cloud.commerce.consumer.procurement.v1.OrderState.ORDER_STATE_ACTIVE].
  // In case of failure, the order resource will be removed.
  rpc PlaceOrder(PlaceOrderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=billingAccounts/*}/orders:place"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Order"
      metadata_type: "PlaceOrderMetadata"
    };
  }

  // Returns the requested
  // [Order][google.cloud.commerce.consumer.procurement.v1.Order] resource.
  rpc GetOrder(GetOrderRequest) returns (Order) {
    option (google.api.http) = {
      get: "/v1/{name=billingAccounts/*/orders/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [Order][google.cloud.commerce.consumer.procurement.v1.Order]
  // resources that the user has access to, within the scope of the parent
  // resource.
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=billingAccounts/*}/orders"
    };
    option (google.api.method_signature) = "parent";
  }

  // Modifies an existing
  // [Order][google.cloud.commerce.consumer.procurement.v1.Order] resource.
  rpc ModifyOrder(ModifyOrderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=billingAccounts/*/orders/*}:modify"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Order"
      metadata_type: "ModifyOrderMetadata"
    };
  }

  // Cancels an existing
  // [Order][google.cloud.commerce.consumer.procurement.v1.Order]. Every product
  // procured in the Order will be cancelled.
  rpc CancelOrder(CancelOrderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=billingAccounts/*/orders/*}:cancel"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Order"
      metadata_type: "CancelOrderMetadata"
    };
  }
}

// Indicates the auto renewal behavior customer specifies on subscription.
enum AutoRenewalBehavior {
  // If unspecified, the auto renewal behavior will follow the default config.
  AUTO_RENEWAL_BEHAVIOR_UNSPECIFIED = 0;

  // Auto Renewal will be enabled on subscription.
  AUTO_RENEWAL_BEHAVIOR_ENABLE = 1;

  // Auto Renewal will be disabled on subscription.
  AUTO_RENEWAL_BEHAVIOR_DISABLE = 2;
}

// Request message for
// [ConsumerProcurementService.PlaceOrder][google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService.PlaceOrder].
message PlaceOrderRequest {
  // Required. The resource name of the parent resource.
  // This field has the form  `billingAccounts/{billing-account-id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudbilling.googleapis.com/BillingAccount"
    }
  ];

  // Required. The user-specified name of the order being placed.
  string display_name = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. Places order for offer. Required when an offer-based order is
  // being placed.
  repeated LineItemInfo line_item_info = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A unique identifier for this request.
  // The server will ignore subsequent requests that provide a duplicate request
  // ID for at least 24 hours after the first request.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format).
  string request_id = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Message stored in the metadata field of the Operation returned by
// [ConsumerProcurementService.PlaceOrder][google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService.PlaceOrder].
message PlaceOrderMetadata {}

// Request message for
// [ConsumerProcurementService.GetOrder][google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService.GetOrder]
message GetOrderRequest {
  // Required. The name of the order to retrieve.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ConsumerProcurementService.ListOrders][google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService.ListOrders].
message ListOrdersRequest {
  // Required. The parent resource to query for orders.
  // This field has the form `billingAccounts/{billing-account-id}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of entries requested.
  // The default page size is 25 and the maximum page size is 200.
  int32 page_size = 2;

  // The token for fetching the next page.
  string page_token = 3;

  // Filter that you can use to limit the list request.
  //
  // A query string that can match a selected set of attributes
  // with string values. For example, `display_name=abc`.
  // Supported query attributes are
  //
  // * `display_name`
  //
  //
  // If the query contains special characters other than letters,
  // underscore, or digits, the phrase must be quoted with double quotes. For
  // example, `display_name="foo:bar"`, where the display name needs to be
  // quoted because it contains special character colon.
  //
  // Queries can be combined with `OR`, and `NOT` to form more complex queries.
  // You can also group them to force a desired evaluation order.
  // For example, `display_name=abc OR display_name=def`.
  string filter = 4;
}

// Response message for
// [ConsumerProcurementService.ListOrders][google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService.ListOrders].
message ListOrdersResponse {
  // The list of orders in this response.
  repeated Order orders = 1;

  // The token for fetching the next page.
  string next_page_token = 2;
}

// Request message for
// [ConsumerProcurementService.ModifyOrder][google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService.ModifyOrder].
message ModifyOrderRequest {
  // Modifications to make on the order.
  message Modification {
    // Required. ID of the existing line item to make change to.
    // Required when change type is
    // [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE] or
    // [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_CANCEL].
    string line_item_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Type of change to make.
    LineItemChangeType change_type = 2 [(google.api.field_behavior) = REQUIRED];

    // Optional. The line item to update to.
    // Required when change_type is
    // [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_CREATE] or
    // [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE].
    LineItemInfo new_line_item_info = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Auto renewal behavior of the subscription for the update.
    // Applied when change_type is
    // [LineItemChangeType.LINE_ITEM_CHANGE_TYPE_UPDATE]. Follows plan default
    // config when this field is not specified.
    AutoRenewalBehavior auto_renewal_behavior = 4
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. Name of the order to update.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Modifications for an existing Order created by an Offer.
  // Required when Offer based Order is being modified, except for when going
  // from an offer to a public plan.
  repeated Modification modifications = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Updated display name of the order, leave as empty if you do not
  // want to update current display name.
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The weak etag, which can be optionally populated, of the order
  // that this modify request is based on. Validation checking will only happen
  // if the invoker supplies this field.
  string etag = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message stored in the metadata field of the Operation returned by
// [ConsumerProcurementService.ModifyOrder][google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService.ModifyOrder].
message ModifyOrderMetadata {}

// Request message for
// [ConsumerProcurementService.CancelOrder][google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService.CancelOrder].
message CancelOrderRequest {
  // Indicates the cancellation policy the customer uses to cancel the order.
  enum CancellationPolicy {
    // If unspecified, cancellation will try to cancel the order, if order
    // cannot be immediately cancelled, auto renewal will be turned off.
    // However, caller should avoid using the value as it will yield a
    // non-deterministic result. This is still supported mainly to maintain
    // existing integrated usages and ensure backwards compatibility.
    CANCELLATION_POLICY_UNSPECIFIED = 0;

    // Request will cancel the whole order immediately, if order cannot be
    // immediately cancelled, the request will fail.
    CANCELLATION_POLICY_CANCEL_IMMEDIATELY = 1;

    // Request will cancel the auto renewal, if order is not subscription based,
    // the request will fail.
    CANCELLATION_POLICY_CANCEL_AT_TERM_END = 2;
  }

  // Required. The resource name of the order.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The weak etag, which can be optionally populated, of the order
  // that this cancel request is based on. Validation checking will only happen
  // if the invoker supplies this field.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Cancellation policy of this request.
  CancellationPolicy cancellation_policy = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Message stored in the metadata field of the Operation returned by
// [ConsumerProcurementService.CancelOrder][google.cloud.commerce.consumer.procurement.v1.ConsumerProcurementService.CancelOrder].
message CancelOrderMetadata {}
