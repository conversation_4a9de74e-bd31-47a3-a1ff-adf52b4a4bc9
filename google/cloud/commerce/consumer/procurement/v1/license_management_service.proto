// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.commerce.consumer.procurement.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Commerce.Consumer.Procurement.V1";
option go_package = "cloud.google.com/go/commerce/consumer/procurement/apiv1/procurementpb;procurementpb";
option java_multiple_files = true;
option java_outer_classname = "LicenseManagementServiceProto";
option java_package = "com.google.cloud.commerce.consumer.procurement.v1";
option php_namespace = "Google\\Cloud\\Commerce\\Consumer\\Procurement\\V1";
option ruby_package = "Google::Cloud::Commerce::Consumer::Procurement::V1";

// Service for managing licenses.
service LicenseManagementService {
  option (google.api.default_host) =
      "cloudcommerceconsumerprocurement.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets the license pool.
  rpc GetLicensePool(GetLicensePoolRequest) returns (LicensePool) {
    option (google.api.http) = {
      get: "/v1/{name=billingAccounts/*/orders/*/licensePool}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the license pool if one exists for this Order.
  rpc UpdateLicensePool(UpdateLicensePoolRequest) returns (LicensePool) {
    option (google.api.http) = {
      patch: "/v1/{license_pool.name=billingAccounts/*/orders/*/licensePool/*}"
      body: "license_pool"
    };
    option (google.api.method_signature) = "license_pool,update_mask";
  }

  // Assigns a license to a user.
  rpc Assign(AssignRequest) returns (AssignResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=billingAccounts/*/orders/*/licensePool}:assign"
      body: "*"
    };
    option (google.api.method_signature) = "parent,usernames";
  }

  // Unassigns a license from a user.
  rpc Unassign(UnassignRequest) returns (UnassignResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=billingAccounts/*/orders/*/licensePool}:unassign"
      body: "*"
    };
    option (google.api.method_signature) = "parent,usernames";
  }

  // Enumerates all users assigned a license.
  rpc EnumerateLicensedUsers(EnumerateLicensedUsersRequest)
      returns (EnumerateLicensedUsersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=billingAccounts/*/orders/*/licensePool}:enumerateLicensedUsers"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Assignment protocol for a license pool.
message AssignmentProtocol {
  // Allow manual assignments triggered by administrative operations only.
  message ManualAssignmentType {}

  // Configuration for automatic assignments handled by data plane operations.
  message AutoAssignmentType {
    // Optional. The time to live for an inactive license. After this time has
    // passed, the license will be automatically unassigned from the user. Must
    // be at least 7 days, if set. If unset, the license will never expire.
    google.protobuf.Duration inactive_license_ttl = 1
        [(google.api.field_behavior) = OPTIONAL];
  }

  // The type of assignment protocol.
  oneof assignment_type {
    // Allow manual assignments triggered by administrative operations only.
    ManualAssignmentType manual_assignment_type = 2;

    // Allow automatic assignments triggered by data plane operations.
    AutoAssignmentType auto_assignment_type = 3;
  }
}

// A license pool represents a pool of licenses that can be assigned to users.
message LicensePool {
  option (google.api.resource) = {
    type: "cloudcommerceconsumerprocurement.googleapis.com/LicensePool"
    pattern: "billingAccounts/{billing_account}/orders/{order}/licensePool"
    plural: "licensePools"
    singular: "licensePool"
  };

  // Identifier. Format:
  // `billingAccounts/{billing_account}/orders/{order}/licensePool`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Assignment protocol for the license pool.
  AssignmentProtocol license_assignment_protocol = 2
      [(google.api.field_behavior) = REQUIRED];

  // Output only. Licenses count that are available to be assigned.
  int32 available_license_count = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total number of licenses in the pool.
  int32 total_license_count = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for getting a license pool.
message GetLicensePoolRequest {
  // Required. The name of the license pool to get.
  // Format: `billingAccounts/{billing_account}/orders/{order}/licensePool`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for updating a license pool.
message UpdateLicensePoolRequest {
  // Required. The license pool to update.
  //
  // The license pool's name field is used to identify the license pool to
  // update. Format:
  // `billingAccounts/{billing_account}/orders/{order}/licensePool`.
  LicensePool license_pool = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [LicenseManagementService.Assign][google.cloud.commerce.consumer.procurement.v1.LicenseManagementService.Assign].
message AssignRequest {
  // Required. License pool name.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Username.
  // Format: `<EMAIL>`.
  repeated string usernames = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [LicenseManagementService.Assign][google.cloud.commerce.consumer.procurement.v1.LicenseManagementService.Assign].
message AssignResponse {}

// Request message for
// [LicenseManagementService.Unassign][google.cloud.commerce.consumer.procurement.v1.LicenseManagementService.Unassign].
message UnassignRequest {
  // Required. License pool name.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Username.
  // Format: `<EMAIL>`.
  repeated string usernames = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [LicenseManagementService.Unassign][google.cloud.commerce.consumer.procurement.v1.LicenseManagementService.Unassign].
message UnassignResponse {}

// Request message for
// [LicenseManagementService.EnumerateLicensedUsers][google.cloud.commerce.consumer.procurement.v1.LicenseManagementService.EnumerateLicensedUsers].
message EnumerateLicensedUsersRequest {
  // Required. License pool name.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The maximum number of users to return. The service may return
  // fewer than this value.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `EnumerateLicensedUsers`
  // call. Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A licensed user.
message LicensedUser {
  // Username.
  // Format: `<EMAIL>`.
  string username = 1;

  // Output only. Timestamp when the license was assigned.
  google.protobuf.Timestamp assign_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the license was recently used. This may not be
  // the most recent usage time, and will be updated regularly (within 24
  // hours).
  google.protobuf.Timestamp recent_usage_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Response message for
// [LicenseManagementService.EnumerateLicensedUsers][google.cloud.commerce.consumer.procurement.v1.LicenseManagementService.EnumerateLicensedUsers].
message EnumerateLicensedUsersResponse {
  // The list of licensed users.
  repeated LicensedUser licensed_users = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
