{"methodConfig": [{"name": [{"service": "google.cloud.connectors.v1.Connectors", "method": "ListConnections"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "GetConnection"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "ListProviders"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "GetProvider"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "ListConnectors"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "GetConnector"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "ListConnectorVersions"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "GetConnectorVersion"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "ListProviders"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "GetProvider"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "GetConnectionSchemaMetadata"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "ListRuntimeEntitySchemas"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "ListRuntimeActionSchemas"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "GetRuntimeConfig"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.connectors.v1.Connectors", "method": "CreateConnection"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "UpdateConnection"}, {"service": "google.cloud.connectors.v1.Connectors", "method": "DeleteConnection"}], "timeout": "60s"}]}