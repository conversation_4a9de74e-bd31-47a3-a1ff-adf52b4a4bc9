// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.connectors.v1;

import "google/cloud/connectors/v1/common.proto";

option go_package = "cloud.google.com/go/connectors/apiv1/connectorspb;connectorspb";
option java_multiple_files = true;
option java_outer_classname = "AuthConfigProto";
option java_package = "com.google.cloud.connectors.v1";

// AuthConfig defines details of a authentication type.
message AuthConfig {
  // Parameters to support Username and Password Authentication.
  message UserPassword {
    // Username.
    string username = 1;

    // Secret version reference containing the password.
    Secret password = 2;
  }

  // Parameters to support JSON Web Token (JWT) Profile for Oauth 2.0
  // Authorization Grant based authentication.
  // See https://tools.ietf.org/html/rfc7523 for more details.
  message Oauth2JwtBearer {
    // JWT claims used for the jwt-bearer authorization grant.
    message JwtClaims {
      // Value for the "iss" claim.
      string issuer = 1;

      // Value for the "sub" claim.
      string subject = 2;

      // Value for the "aud" claim.
      string audience = 3;
    }

    // Secret version reference containing a PKCS#8 PEM-encoded private
    // key associated with the Client Certificate. This private key will be
    // used to sign JWTs used for the jwt-bearer authorization grant.
    // Specified in the form as: `projects/*/secrets/*/versions/*`.
    Secret client_key = 1;

    // JwtClaims providers fields to generate the token.
    JwtClaims jwt_claims = 2;
  }

  // Parameters to support Oauth 2.0 Client Credentials Grant Authentication.
  // See https://tools.ietf.org/html/rfc6749#section-1.3.4 for more details.
  message Oauth2ClientCredentials {
    // The client identifier.
    string client_id = 1;

    // Secret version reference containing the client secret.
    Secret client_secret = 2;
  }

  // Parameters to support Ssh public key Authentication.
  message SshPublicKey {
    // The user account used to authenticate.
    string username = 1;

    // SSH Client Cert. It should contain both public and private key.
    Secret ssh_client_cert = 3;

    // Format of SSH Client cert.
    string cert_type = 4;

    // Password (passphrase) for ssh client certificate if it has one.
    Secret ssh_client_cert_pass = 5;
  }

  // The type of authentication configured.
  AuthType auth_type = 1;

  // Supported auth types.
  oneof type {
    // UserPassword.
    UserPassword user_password = 2;

    // Oauth2JwtBearer.
    Oauth2JwtBearer oauth2_jwt_bearer = 3;

    // Oauth2ClientCredentials.
    Oauth2ClientCredentials oauth2_client_credentials = 4;

    // SSH Public Key.
    SshPublicKey ssh_public_key = 6;
  }

  // List containing additional auth configs.
  repeated ConfigVariable additional_variables = 5;
}

// AuthConfigTemplate defines required field over an authentication type.
message AuthConfigTemplate {
  // The type of authentication configured.
  AuthType auth_type = 1;

  // Config variables to describe an `AuthConfig` for a `Connection`.
  repeated ConfigVariableTemplate config_variable_templates = 2;

  // Display name for authentication template.
  string display_name = 3;

  // Connector specific description for an authentication template.
  string description = 4;
}

// AuthType defines different authentication types.
enum AuthType {
  // Authentication type not specified.
  AUTH_TYPE_UNSPECIFIED = 0;

  // Username and Password Authentication.
  USER_PASSWORD = 1;

  // JSON Web Token (JWT) Profile for Oauth 2.0
  // Authorization Grant based authentication
  OAUTH2_JWT_BEARER = 2;

  // Oauth 2.0 Client Credentials Grant Authentication
  OAUTH2_CLIENT_CREDENTIALS = 3;

  // SSH Public Key Authentication
  SSH_PUBLIC_KEY = 4;

  // Oauth 2.0 Authorization Code Flow
  OAUTH2_AUTH_CODE_FLOW = 5;
}
