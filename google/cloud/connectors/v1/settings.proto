// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.connectors.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/connectors/apiv1/connectorspb;connectorspb";
option java_multiple_files = true;
option java_outer_classname = "SettingsProto";
option java_package = "com.google.cloud.connectors.v1";

// Request for [GetGlobalSettingsRequest].
message GetGlobalSettingsRequest {
  // Required. The resource name of the Settings.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/Settings"
    }
  ];
}

// Global Settings details.
message Settings {
  option (google.api.resource) = {
    type: "connectors.googleapis.com/Settings"
    pattern: "projects/{project}/locations/global/settings"
  };

  // Output only. Resource name of the Connection.
  // Format: projects/{project}/locations/global/settings}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Flag indicates whether vpc-sc is enabled.
  bool vpcsc = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Flag indicates if user is in PayG model
  bool payg = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
