// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.connectors.v1;

option go_package = "cloud.google.com/go/connectors/apiv1/connectorspb;connectorspb";
option java_multiple_files = true;

// Define the Connectors target endpoint.
message DestinationConfig {
  // The key is the destination identifier that is supported by the Connector.
  string key = 1;

  // The destinations for the key.
  repeated Destination destinations = 2;
}

message Destination {
  oneof destination {
    // PSC service attachments.
    // Format: projects/*/regions/*/serviceAttachments/*
    string service_attachment = 1;

    // For publicly routable host.
    string host = 2;
  }

  // The port is the target port number that is accepted by the destination.
  int32 port = 3;
}
