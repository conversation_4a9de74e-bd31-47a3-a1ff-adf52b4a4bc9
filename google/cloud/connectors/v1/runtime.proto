// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.connectors.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/connectors/apiv1/connectorspb;connectorspb";
option java_multiple_files = true;
option java_outer_classname = "RuntimeProto";
option java_package = "com.google.cloud.connectors.v1";

// Request message for Connectors.GetRuntimeConfig.
message GetRuntimeConfigRequest {
  // Required. Resource name of the form:
  // `projects/*/locations/*/runtimeConfig`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/RuntimeConfig"
    }
  ];
}

// RuntimeConfig is the singleton resource of each location.
// It includes generic resource configs consumed by control plane and runtime
// plane like: pub/sub topic/subscription resource name, Cloud Storage location
// storing schema etc.
message RuntimeConfig {
  option (google.api.resource) = {
    type: "connectors.googleapis.com/RuntimeConfig"
    pattern: "projects/{project}/locations/{location}/runtimeConfig"
  };

  // State of the location.
  enum State {
    // STATE_UNSPECIFIED.
    STATE_UNSPECIFIED = 0;

    // INACTIVE.
    INACTIVE = 1 [deprecated = true];

    // ACTIVATING.
    ACTIVATING = 2 [deprecated = true];

    // ACTIVE.
    ACTIVE = 3;

    // CREATING.
    CREATING = 4;

    // DELETING.
    DELETING = 5;

    // UPDATING.
    UPDATING = 6;
  }

  // Output only. location_id of the runtime location. E.g. "us-west1".
  string location_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Pub/Sub topic for connd to send message.
  // E.g. projects/{project-id}/topics/{topic-id}
  string connd_topic = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Pub/Sub subscription for connd to receive message.
  // E.g. projects/{project-id}/subscriptions/{topic-id}
  string connd_subscription = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Pub/Sub topic for control plne to send message.
  // communication.
  // E.g. projects/{project-id}/topics/{topic-id}
  string control_plane_topic = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Pub/Sub subscription for control plane to receive message.
  // E.g. projects/{project-id}/subscriptions/{topic-id}
  string control_plane_subscription = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The endpoint of the connectors runtime ingress.
  string runtime_endpoint = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the location.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Cloud Storage bucket that stores connector's schema
  // reports.
  string schema_gcs_bucket = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the Service Directory service name.
  string service_directory = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the runtimeConfig resource.
  // Format: projects/{project}/locations/{location}/runtimeConfig
  string name = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}
