// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.connectors.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/connectors/apiv1/connectorspb;connectorspb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.cloud.connectors.v1";

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ConfigVariableTemplate provides metadata about a `ConfigVariable` that is
// used in a Connection.
message ConfigVariableTemplate {
  // ValueType indicates the data type of the value.
  enum ValueType {
    // Value type is not specified.
    VALUE_TYPE_UNSPECIFIED = 0;

    // Value type is string.
    STRING = 1;

    // Value type is integer.
    INT = 2;

    // Value type is boolean.
    BOOL = 3;

    // Value type is secret.
    SECRET = 4;

    // Value type is enum.
    ENUM = 5;

    // Value type is authorization code.
    AUTHORIZATION_CODE = 6;
  }

  // Indicates the state of the config variable.
  enum State {
    // Status is unspecified.
    STATE_UNSPECIFIED = 0;

    // Config variable is active
    ACTIVE = 1;

    // Config variable is deprecated.
    DEPRECATED = 2;
  }

  // Key of the config variable.
  string key = 1;

  // Type of the parameter: string, int, bool etc.
  // consider custom type for the benefit for the validation.
  ValueType value_type = 2;

  // Display name of the parameter.
  string display_name = 3;

  // Description.
  string description = 4;

  // Regular expression in RE2 syntax used for validating the `value` of a
  // `ConfigVariable`.
  string validation_regex = 5;

  // Flag represents that this `ConfigVariable` must be provided for a
  // connection.
  bool required = 6;

  // Role grant configuration for the config variable.
  RoleGrant role_grant = 7;

  // Enum options. To be populated if `ValueType` is `ENUM`
  repeated EnumOption enum_options = 8;

  // Authorization code link options. To be populated if `ValueType` is
  // `AUTHORIZATION_CODE`
  AuthorizationCodeLink authorization_code_link = 9;

  // State of the config variable.
  State state = 10;

  // Indicates if current template is part of advanced settings
  bool is_advanced = 11;
}

// Secret provides a reference to entries in Secret Manager.
message Secret {
  // The resource name of the secret version in the format,
  // format as: `projects/*/secrets/*/versions/*`.
  string secret_version = 1;
}

// EnumOption definition
message EnumOption {
  // Id of the option.
  string id = 1;

  // Display name of the option.
  string display_name = 2;
}

// ConfigVariable represents a configuration variable present in a Connection.
// or AuthConfig.
message ConfigVariable {
  // Key of the config variable.
  string key = 1;

  // Value type of the config variable.
  oneof value {
    // Value is an integer
    int64 int_value = 2;

    // Value is a bool.
    bool bool_value = 3;

    // Value is a string.
    string string_value = 4;

    // Value is a secret.
    Secret secret_value = 5;
  }
}

// This configuration defines all the Cloud IAM roles that needs to be granted
// to a particular GCP resource for the selected prinicpal like service
// account. These configurations will let UI display to customers what
// IAM roles need to be granted by them. Or these configurations can be used
// by the UI to render a 'grant' button to do the same on behalf of the user.
message RoleGrant {
  // Supported Principal values.
  enum Principal {
    // Value type is not specified.
    PRINCIPAL_UNSPECIFIED = 0;

    // Service Account used for Connector workload identity
    // This is either the default service account if unspecified or Service
    // Account provided by Customers through BYOSA.
    CONNECTOR_SA = 1;
  }

  // Resource definition
  message Resource {
    // Resource Type definition.
    enum Type {
      // Value type is not specified.
      TYPE_UNSPECIFIED = 0;

      // GCP Project Resource.
      GCP_PROJECT = 1;

      // Any GCP Resource which is identified uniquely by IAM.
      GCP_RESOURCE = 2;

      // GCP Secret Resource.
      GCP_SECRETMANAGER_SECRET = 3;

      // GCP Secret Version Resource.
      GCP_SECRETMANAGER_SECRET_VERSION = 4;
    }

    // Different types of resource supported.
    Type type = 1;

    // Template to uniquely represent a GCP resource in a format IAM expects
    // This is a template that can have references to other values provided in
    // the config variable template.
    string path_template = 3;
  }

  // Prinicipal/Identity for whom the role need to assigned.
  Principal principal = 1;

  // List of roles that need to be granted.
  repeated string roles = 2;

  // Resource on which the roles needs to be granted for the principal.
  Resource resource = 3;

  // Template that UI can use to provide helper text to customers.
  string helper_text_template = 4;
}

// This configuration captures the details required to render an authorization
// link for the OAuth Authorization Code Flow.
message AuthorizationCodeLink {
  // The base URI the user must click to trigger the authorization code login
  // flow.
  string uri = 1;

  // The scopes for which the user will authorize GCP Connectors on the
  // connector data source.
  repeated string scopes = 2;

  // The client ID assigned to the GCP Connectors OAuth app for the connector
  // data source.
  string client_id = 3;

  // Whether to enable PKCE for the auth code flow.
  bool enable_pkce = 4;
}

// LaunchStage is a enum to indicate launch stage:
// PREVIEW, GA, DEPRECATED, PRIVATE_PREVIEW.
enum LaunchStage {
  // LAUNCH_STAGE_UNSPECIFIED.
  LAUNCH_STAGE_UNSPECIFIED = 0;

  // PREVIEW.
  PREVIEW = 1;

  // GA.
  GA = 2;

  // DEPRECATED.
  DEPRECATED = 3;

  // PRIVATE_PREVIEW.
  PRIVATE_PREVIEW = 5;
}
