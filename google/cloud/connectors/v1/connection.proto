// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.connectors.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/connectors/v1/authconfig.proto";
import "google/cloud/connectors/v1/common.proto";
import "google/cloud/connectors/v1/destination_config.proto";
import "google/cloud/connectors/v1/ssl_config.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/connectors/apiv1/connectorspb;connectorspb";
option java_multiple_files = true;
option java_outer_classname = "ConnectionProto";
option java_package = "com.google.cloud.connectors.v1";

// Connection represents an instance of connector.
message Connection {
  option (google.api.resource) = {
    type: "connectors.googleapis.com/Connection"
    pattern: "projects/{project}/locations/{location}/connections/{connection}"
  };

  // Output only. Resource name of the Connection.
  // Format: projects/{project}/locations/{location}/connections/{connection}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Created time.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Updated time.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels to represent user-provided metadata.
  // Refer to cloud documentation on labels for more details.
  // https://cloud.google.com/compute/docs/labeling-resources
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of the resource.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Required. Connector version on which the connection is created.
  // The format is:
  // projects/*/locations/*/providers/*/connectors/*/versions/*
  // Only global location is supported for ConnectorVersion resource.
  string connector_version = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/ConnectorVersion"
    }
  ];

  // Output only. Current status of the connection.
  ConnectionStatus status = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Configuration for configuring the connection with an external
  // system.
  repeated ConfigVariable config_variables = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration for establishing the connection's authentication
  // with an external system.
  AuthConfig auth_config = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration that indicates whether or not the Connection can be
  // edited.
  LockConfig lock_config = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration of the Connector's destination. Only accepted for
  // Connectors that accepts user defined destination(s).
  repeated DestinationConfig destination_configs = 18
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. GCR location where the runtime image is stored.
  // formatted like: gcr.io/{bucketName}/{imageName}
  string image_location = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Service account needed for runtime plane to access GCP resources.
  string service_account = 12 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The name of the Service Directory service name. Used for
  // Private Harpoon to resolve the ILB address.
  // e.g.
  // "projects/cloud-connectors-e2e-testing/locations/us-central1/namespaces/istio-system/services/istio-ingressgateway-connectors"
  string service_directory = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. GCR location where the envoy image is stored.
  // formatted like: gcr.io/{bucketName}/{imageName}
  string envoy_image_location = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Suspended indicates if a user has suspended a connection or not.
  bool suspended = 17 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Node configuration for the connection.
  NodeConfig node_config = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Ssl config of a connection
  SslConfig ssl_config = 21 [(google.api.field_behavior) = OPTIONAL];
}

// Node configuration for the connection.
message NodeConfig {
  // Minimum number of nodes in the runtime nodes.
  int32 min_node_count = 1;

  // Maximum number of nodes in the runtime nodes.
  int32 max_node_count = 2;
}

// ConnectionSchemaMetadata is the singleton resource of each connection.
// It includes the entity and action names of runtime resources exposed
// by a connection backend.
message ConnectionSchemaMetadata {
  option (google.api.resource) = {
    type: "connectors.googleapis.com/ConnectionSchemaMetadata"
    pattern: "projects/{project}/locations/{location}/connections/{connection}/connectionSchemaMetadata"
  };

  // State of connection runtime schema.
  enum State {
    // Default state.
    STATE_UNSPECIFIED = 0;

    // Schema refresh is in progress.
    REFRESHING = 1;

    // Schema has been updated.
    UPDATED = 2;
  }

  // Output only. List of entity names.
  repeated string entities = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of actions.
  repeated string actions = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource name.
  // Format:
  // projects/{project}/locations/{location}/connections/{connection}/connectionSchemaMetadata
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the connection runtime schema was updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the connection runtime schema refresh was
  // triggered.
  google.protobuf.Timestamp refresh_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of runtime schema.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Schema of a runtime entity.
message RuntimeEntitySchema {
  // Metadata of an entity field.
  message Field {
    // Name of the Field.
    string field = 1;

    // A brief description of the Field.
    string description = 2;

    // The data type of the Field.
    DataType data_type = 3;

    // The following boolean field specifies if the current Field acts
    // as a primary key or id if the parent is of type entity.
    bool key = 4;

    // Specifies if the Field is readonly.
    bool readonly = 5;

    // Specifies whether a null value is allowed.
    bool nullable = 6;

    // The following field specifies the default value of the Field provided
    // by the external system if a value is not provided.
    google.protobuf.Value default_value = 7;

    // The following map contains fields that are not explicitly mentioned
    // above,this give connectors the flexibility to add new metadata
    // fields.
    google.protobuf.Struct additional_details = 8;
  }

  // Output only. Name of the entity.
  string entity = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of fields in the entity.
  repeated Field fields = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Schema of a runtime action.
message RuntimeActionSchema {
  // Metadata of an input parameter.
  message InputParameter {
    // Name of the Parameter.
    string parameter = 1;

    // A brief description of the Parameter.
    string description = 2;

    // The data type of the Parameter.
    DataType data_type = 3;

    // Specifies whether a null value is allowed.
    bool nullable = 4;

    // The following field specifies the default value of the Parameter
    // provided by the external system if a value is not provided.
    google.protobuf.Value default_value = 5;
  }

  // Metadata of result field.
  message ResultMetadata {
    // Name of the result field.
    string field = 1;

    // A brief description of the field.
    string description = 2;

    // The data type of the field.
    DataType data_type = 3;
  }

  // Output only. Name of the action.
  string action = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of input parameter metadata for the action.
  repeated InputParameter input_parameters = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of result field metadata.
  repeated ResultMetadata result_metadata = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Determines whether or no a connection is locked. If locked, a reason must be
// specified.
message LockConfig {
  // Indicates whether or not the connection is locked.
  bool locked = 1;

  // Describes why a connection is locked.
  string reason = 2;
}

// Request message for ConnectorsService.ListConnections
message ListConnectionsRequest {
  // Required. Parent resource of the Connection, of the form:
  // `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "connectors.googleapis.com/Connection"
    }
  ];

  // Page size.
  int32 page_size = 2;

  // Page token.
  string page_token = 3;

  // Filter.
  string filter = 4;

  // Order by parameters.
  string order_by = 5;

  // Specifies which fields of the Connection are returned in the response.
  // Defaults to `BASIC` view.
  ConnectionView view = 6;
}

// Response message for ConnectorsService.ListConnections
message ListConnectionsResponse {
  // Connections.
  repeated Connection connections = 1;

  // Next page token.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for ConnectorsService.GetConnection
message GetConnectionRequest {
  // Required. Resource name of the form:
  // `projects/*/locations/*/connections/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/Connection"
    }
  ];

  // Specifies which fields of the Connection are returned in the response.
  // Defaults to `BASIC` view.
  ConnectionView view = 2;
}

// Request message for ConnectorsService.CreateConnection
message CreateConnectionRequest {
  // Required. Parent resource of the Connection, of the form:
  // `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "connectors.googleapis.com/Connection"
    }
  ];

  // Required. Identifier to assign to the Connection. Must be unique within
  // scope of the parent resource.
  string connection_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Connection resource.
  Connection connection = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ConnectorsService.UpdateConnection
message UpdateConnectionRequest {
  // Required. Connection resource.
  Connection connection = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. You can modify only the fields listed below.
  //
  // To lock/unlock a connection:
  // * `lock_config`
  //
  // To suspend/resume a connection:
  // * `suspended`
  //
  // To update the connection details:
  // * `description`
  // * `labels`
  // * `connector_version`
  // * `config_variables`
  // * `auth_config`
  // * `destination_configs`
  // * `node_config`
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for ConnectorsService.DeleteConnection.
message DeleteConnectionRequest {
  // Required. Resource name of the form:
  // `projects/*/locations/*/connections/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/Connection"
    }
  ];
}

// Request message for ConnectorsService.GetConnectionSchemaMetadata.
message GetConnectionSchemaMetadataRequest {
  // Required. Connection name
  // Format:
  // projects/{project}/locations/{location}/connections/{connection}/connectionSchemaMetadata
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/ConnectionSchemaMetadata"
    }
  ];
}

// Request message for ConnectorsService.RefreshConnectionSchemaMetadata.
message RefreshConnectionSchemaMetadataRequest {
  // Required. Resource name.
  // Format:
  // projects/{project}/locations/{location}/connections/{connection}/connectionSchemaMetadata
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/ConnectionSchemaMetadata"
    }
  ];
}

// Request message for ConnectorsService.ListRuntimeEntitySchemas.
// For filter, only entity field is supported with literal equality operator.
// Accepted filter example: entity="Order"
// Wildcards are not supported in the filter currently.
message ListRuntimeEntitySchemasRequest {
  // Required. Parent resource of RuntimeEntitySchema
  // Format:
  // projects/{project}/locations/{location}/connections/{connection}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/Connection"
    }
  ];

  // Page size.
  int32 page_size = 2;

  // Page token.
  string page_token = 3;

  // Required. Filter
  // Format:
  // entity="{entityId}"
  // Only entity field is supported with literal equality operator.
  // Accepted filter example: entity="Order"
  // Wildcards are not supported in the filter currently.
  string filter = 4 [(google.api.field_behavior) = REQUIRED];
}

// Response message for ConnectorsService.ListRuntimeEntitySchemas.
message ListRuntimeEntitySchemasResponse {
  // Runtime entity schemas.
  repeated RuntimeEntitySchema runtime_entity_schemas = 1;

  // Next page token.
  string next_page_token = 2;
}

// Request message for ConnectorsService.ListRuntimeActionSchemas.
// For filter, only action field is supported with literal equality operator.
// Accepted filter example: action="approveOrder"
// Wildcards are not supported in the filter currently.
message ListRuntimeActionSchemasRequest {
  // Required. Parent resource of RuntimeActionSchema
  // Format:
  // projects/{project}/locations/{location}/connections/{connection}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/Connection"
    }
  ];

  // Page size.
  int32 page_size = 2;

  // Page token.
  string page_token = 3;

  // Required. Filter
  // Format:
  // action="{actionId}"
  // Only action field is supported with literal equality operator.
  // Accepted filter example: action="CancelOrder"
  // Wildcards are not supported in the filter currently.
  string filter = 4 [(google.api.field_behavior) = REQUIRED];
}

// Response message for ConnectorsService.ListRuntimeActionSchemas.
message ListRuntimeActionSchemasResponse {
  // Runtime action schemas.
  repeated RuntimeActionSchema runtime_action_schemas = 1;

  // Next page token.
  string next_page_token = 2;
}

// ConnectionStatus indicates the state of the connection.
message ConnectionStatus {
  // All the possible Connection State.
  enum State {
    // Connection does not have a state yet.
    STATE_UNSPECIFIED = 0;

    // Connection is being created.
    CREATING = 1;

    // Connection is running and ready for requests.
    ACTIVE = 2;

    // Connection is stopped.
    INACTIVE = 3;

    // Connection is being deleted.
    DELETING = 4;

    // Connection is being updated.
    UPDATING = 5;

    // Connection is not running due to an error.
    ERROR = 6;

    // Connection is not running due to an auth error for the Oauth2 Auth Code
    // based connector.
    AUTHORIZATION_REQUIRED = 7;
  }

  // State.
  State state = 1;

  // Description.
  string description = 2;

  // Status provides detailed information for the state.
  string status = 3;
}

// All possible data types of a entity or action field.
enum DataType {
  // Data type is not specified.
  DATA_TYPE_UNSPECIFIED = 0;

  // DEPRECATED! Use DATA_TYPE_INTEGER.
  DATA_TYPE_INT = 1 [deprecated = true];

  // Short integer(int16) data type.
  DATA_TYPE_SMALLINT = 2;

  // Double data type.
  DATA_TYPE_DOUBLE = 3;

  // Date data type.
  DATA_TYPE_DATE = 4;

  // DEPRECATED! Use DATA_TYPE_TIMESTAMP.
  DATA_TYPE_DATETIME = 5 [deprecated = true];

  // Time data type.
  DATA_TYPE_TIME = 6;

  // DEPRECATED! Use DATA_TYPE_VARCHAR.
  DATA_TYPE_STRING = 7 [deprecated = true];

  // DEPRECATED! Use DATA_TYPE_BIGINT.
  DATA_TYPE_LONG = 8 [deprecated = true];

  // Boolean data type.
  DATA_TYPE_BOOLEAN = 9;

  // Decimal data type.
  DATA_TYPE_DECIMAL = 10;

  // DEPRECATED! Use DATA_TYPE_VARCHAR.
  DATA_TYPE_UUID = 11 [deprecated = true];

  // UNSUPPORTED! Binary data type.
  DATA_TYPE_BLOB = 12;

  // Bit data type.
  DATA_TYPE_BIT = 13;

  // Small integer(int8) data type.
  DATA_TYPE_TINYINT = 14;

  // Integer(int32) data type.
  DATA_TYPE_INTEGER = 15;

  // Long integer(int64) data type.
  DATA_TYPE_BIGINT = 16;

  // Float data type.
  DATA_TYPE_FLOAT = 17;

  // Real data type.
  DATA_TYPE_REAL = 18;

  // Numeric data type.
  DATA_TYPE_NUMERIC = 19;

  // Char data type.
  DATA_TYPE_CHAR = 20;

  // Varchar data type.
  DATA_TYPE_VARCHAR = 21;

  // Longvarchar data type.
  DATA_TYPE_LONGVARCHAR = 22;

  // Timestamp data type.
  DATA_TYPE_TIMESTAMP = 23;

  // Nchar data type.
  DATA_TYPE_NCHAR = 24;

  // Nvarchar data type.
  DATA_TYPE_NVARCHAR = 25;

  // Longnvarchar data type.
  DATA_TYPE_LONGNVARCHAR = 26;

  // Null data type.
  DATA_TYPE_NULL = 27;

  // UNSUPPORTED! Binary data type.
  DATA_TYPE_OTHER = 28;

  // UNSUPPORTED! Binary data type.
  DATA_TYPE_JAVA_OBJECT = 29;

  // UNSUPPORTED! Binary data type.
  DATA_TYPE_DISTINCT = 30;

  // UNSUPPORTED! Binary data type.
  DATA_TYPE_STRUCT = 31;

  // UNSUPPORTED! Binary data type.
  DATA_TYPE_ARRAY = 32;

  // UNSUPPORTED! Binary data type.
  DATA_TYPE_CLOB = 33;

  // UNSUPPORTED! Binary data type.
  DATA_TYPE_REF = 34;

  // UNSUPPORTED! Binary data type.
  DATA_TYPE_DATALINK = 35;

  // UNSUPPORTED! Row id data type.
  DATA_TYPE_ROWID = 36;

  // UNSUPPORTED! Binary data type.
  DATA_TYPE_BINARY = 37;

  // UNSUPPORTED! Variable binary data type.
  DATA_TYPE_VARBINARY = 38;

  // UNSUPPORTED! Long variable binary data type.
  DATA_TYPE_LONGVARBINARY = 39;

  // UNSUPPORTED! NCLOB data type.
  DATA_TYPE_NCLOB = 40;

  // UNSUPPORTED! SQL XML data type is not supported.
  DATA_TYPE_SQLXML = 41;

  // UNSUPPORTED! Cursor reference type is not supported.
  DATA_TYPE_REF_CURSOR = 42;

  // UNSUPPORTED! Use TIME or TIMESTAMP instead.
  DATA_TYPE_TIME_WITH_TIMEZONE = 43;

  // UNSUPPORTED! Use TIMESTAMP instead.
  DATA_TYPE_TIMESTAMP_WITH_TIMEZONE = 44;
}

// Enum to control which fields should be included in the response.
enum ConnectionView {
  // CONNECTION_UNSPECIFIED.
  CONNECTION_VIEW_UNSPECIFIED = 0;

  // Do not include runtime required configs.
  BASIC = 1;

  // Include runtime required configs.
  FULL = 2;
}
