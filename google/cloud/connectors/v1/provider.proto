// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.connectors.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/connectors/v1/common.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/connectors/apiv1/connectorspb;connectorspb";
option java_multiple_files = true;
option java_outer_classname = "ProviderProto";
option java_package = "com.google.cloud.connectors.v1";

// Provider indicates the owner who provides the connectors.
message Provider {
  option (google.api.resource) = {
    type: "connectors.googleapis.com/Provider"
    pattern: "projects/{project}/locations/{location}/providers/{provider}"
  };

  // Output only. Resource name of the Provider.
  // Format: projects/{project}/locations/{location}/providers/{provider}
  // Only global location is supported for Provider resource.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Created time.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Updated time.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource labels to represent user-provided metadata.
  // Refer to cloud documentation on labels for more details.
  // https://cloud.google.com/compute/docs/labeling-resources
  map<string, string> labels = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Link to documentation page.
  string documentation_uri = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Link to external page.
  string external_uri = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Description of the resource.
  string description = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cloud storage location of icons etc consumed by UI.
  string web_assets_location = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Display name.
  string display_name = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Flag to mark the version indicating the launch stage.
  LaunchStage launch_stage = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for Connectors.GetProvider.
message GetProviderRequest {
  // Required. Resource name of the form:
  // `projects/*/locations/*/providers/*`
  // Only global location is supported for Provider resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/Provider"
    }
  ];
}

// Request message for Connectors.ListProviders.
message ListProvidersRequest {
  // Required. Parent resource of the API, of the form:
  // `projects/*/locations/*`
  // Only global location is supported for Provider resource.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "connectors.googleapis.com/Provider"
    }
  ];

  // Page size.
  int32 page_size = 2;

  // Page token.
  string page_token = 3;
}

// Response message for Connectors.ListProviders.
message ListProvidersResponse {
  // A list of providers.
  repeated Provider providers = 1;

  // Next page token.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}
