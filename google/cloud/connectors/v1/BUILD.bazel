# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "connectors_proto",
    srcs = [
        "authconfig.proto",
        "common.proto",
        "connection.proto",
        "connector.proto",
        "connector_version.proto",
        "connectors_service.proto",
        "destination_config.proto",
        "provider.proto",
        "runtime.proto",
        "settings.proto",
        "ssl_config.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "connectors_proto_with_info",
    deps = [
        ":connectors_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "connectors_java_proto",
    deps = [":connectors_proto"],
)

java_grpc_library(
    name = "connectors_java_grpc",
    srcs = [":connectors_proto"],
    deps = [":connectors_java_proto"],
)

java_gapic_library(
    name = "connectors_java_gapic",
    srcs = [":connectors_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "connectors_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "connectors_v1.yaml",
    test_deps = [
        ":connectors_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":connectors_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "connectors_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.connectors.v1.ConnectorsClientHttpJsonTest",
        "com.google.cloud.connectors.v1.ConnectorsClientTest",
    ],
    runtime_deps = [":connectors_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-connectors-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":connectors_java_gapic",
        ":connectors_java_grpc",
        ":connectors_java_proto",
        ":connectors_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "connectors_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/connectors/apiv1/connectorspb",
    protos = [":connectors_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "connectors_go_gapic",
    srcs = [":connectors_proto_with_info"],
    grpc_service_config = "connectors_grpc_service_config.json",
    importpath = "cloud.google.com/go/connectors/apiv1;connectors",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "connectors_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":connectors_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-connectors-v1-go",
    deps = [
        ":connectors_go_gapic",
        ":connectors_go_gapic_srcjar-metadata.srcjar",
        ":connectors_go_gapic_srcjar-snippets.srcjar",
        ":connectors_go_gapic_srcjar-test.srcjar",
        ":connectors_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "connectors_py_gapic",
    srcs = [":connectors_proto"],
    grpc_service_config = "connectors_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "connectors_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "connectors_py_gapic_test",
    srcs = [
        "connectors_py_gapic_pytest.py",
        "connectors_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":connectors_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "connectors-v1-py",
    deps = [
        ":connectors_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "connectors_php_proto",
    deps = [":connectors_proto"],
)

php_gapic_library(
    name = "connectors_php_gapic",
    srcs = [":connectors_proto_with_info"],
    grpc_service_config = "connectors_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "connectors_v1.yaml",
    transport = "grpc+rest",
    deps = [":connectors_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-connectors-v1-php",
    deps = [
        ":connectors_php_gapic",
        ":connectors_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "connectors_nodejs_gapic",
    package_name = "@google-cloud/connectors",
    src = ":connectors_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "connectors_grpc_service_config.json",
    package = "google.cloud.connectors.v1",
    rest_numeric_enums = True,
    service_yaml = "connectors_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "connectors-v1-nodejs",
    deps = [
        ":connectors_nodejs_gapic",
        ":connectors_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "connectors_ruby_proto",
    deps = [":connectors_proto"],
)

ruby_grpc_library(
    name = "connectors_ruby_grpc",
    srcs = [":connectors_proto"],
    deps = [":connectors_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "connectors_ruby_gapic",
    srcs = [":connectors_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-connectors-v1"],
    grpc_service_config = "connectors_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "connectors_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":connectors_ruby_grpc",
        ":connectors_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-connectors-v1-ruby",
    deps = [
        ":connectors_ruby_gapic",
        ":connectors_ruby_grpc",
        ":connectors_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "connectors_csharp_proto",
    deps = [":connectors_proto"],
)

csharp_grpc_library(
    name = "connectors_csharp_grpc",
    srcs = [":connectors_proto"],
    deps = [":connectors_csharp_proto"],
)

csharp_gapic_library(
    name = "connectors_csharp_gapic",
    srcs = [":connectors_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "connectors_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "connectors_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":connectors_csharp_grpc",
        ":connectors_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-connectors-v1-csharp",
    deps = [
        ":connectors_csharp_gapic",
        ":connectors_csharp_grpc",
        ":connectors_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "connectors_cc_proto",
    deps = [":connectors_proto"],
)

cc_grpc_library(
    name = "connectors_cc_grpc",
    srcs = [":connectors_proto"],
    grpc_only = True,
    deps = [":connectors_cc_proto"],
)
