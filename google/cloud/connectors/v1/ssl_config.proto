// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.connectors.v1;

import "google/cloud/connectors/v1/common.proto";

option go_package = "cloud.google.com/go/connectors/apiv1/connectorspb;connectorspb";
option java_multiple_files = true;
option java_outer_classname = "SslConfigProto";
option java_package = "com.google.cloud.connectors.v1";

// Ssl config details of a connector version
message SslConfigTemplate {
  // Controls the ssl type for the given connector version
  SslType ssl_type = 1;

  // Boolean for determining if the connector version mandates TLS.
  bool is_tls_mandatory = 2;

  // List of supported Server Cert Types
  repeated CertType server_cert_type = 3;

  // List of supported Client Cert Types
  repeated CertType client_cert_type = 4;

  // Any additional fields that need to be rendered
  repeated ConfigVariableTemplate additional_variables = 5;
}

// SSL Configuration of a connection
message SslConfig {
  // Enum for Ttust Model
  enum TrustModel {
    // Public Trust Model. Takes the Default Java trust store.
    PUBLIC = 0;

    // Private Trust Model. Takes custom/private trust store.
    PRIVATE = 1;

    // Insecure Trust Model. Accept all certificates.
    INSECURE = 2;
  }

  // Controls the ssl type for the given connector version.
  SslType type = 1;

  // Trust Model of the SSL connection
  TrustModel trust_model = 2;

  // Private Server Certificate. Needs to be specified if trust model is
  // `PRIVATE`.
  Secret private_server_certificate = 3;

  // Client Certificate
  Secret client_certificate = 4;

  // Client Private Key
  Secret client_private_key = 5;

  // Secret containing the passphrase protecting the Client Private Key
  Secret client_private_key_pass = 6;

  // Type of Server Cert (PEM/JKS/.. etc.)
  CertType server_cert_type = 7;

  // Type of Client Cert (PEM/JKS/.. etc.)
  CertType client_cert_type = 8;

  // Bool for enabling SSL
  bool use_ssl = 9;

  // Additional SSL related field values
  repeated ConfigVariable additional_variables = 10;
}

// Enum for controlling the SSL Type (TLS/MTLS)
enum SslType {
  // No SSL configuration required.
  SSL_TYPE_UNSPECIFIED = 0;

  // TLS Handshake
  TLS = 1;

  // mutual TLS (MTLS) Handshake
  MTLS = 2;
}

// Enum for Cert Types
enum CertType {
  // Cert type unspecified.
  CERT_TYPE_UNSPECIFIED = 0;

  // Privacy Enhanced Mail (PEM) Type
  PEM = 1;
}
