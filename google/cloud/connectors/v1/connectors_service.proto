// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.connectors.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/connectors/v1/common.proto";
import "google/cloud/connectors/v1/connection.proto";
import "google/cloud/connectors/v1/connector.proto";
import "google/cloud/connectors/v1/connector_version.proto";
import "google/cloud/connectors/v1/provider.proto";
import "google/cloud/connectors/v1/runtime.proto";
import "google/cloud/connectors/v1/settings.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";

option go_package = "cloud.google.com/go/connectors/apiv1/connectorspb;connectorspb";
option java_multiple_files = true;
option java_outer_classname = "ConnectorsServiceProto";
option java_package = "com.google.cloud.connectors.v1";

// Connectors is the interface for managing Connectors.
service Connectors {
  option (google.api.default_host) = "connectors.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Connections in a given project and location.
  rpc ListConnections(ListConnectionsRequest)
      returns (ListConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/connections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Connection.
  rpc GetConnection(GetConnectionRequest) returns (Connection) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/connections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Connection in a given project and location.
  rpc CreateConnection(CreateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/connections"
      body: "connection"
    };
    option (google.api.method_signature) = "parent,connection,connection_id";
    option (google.longrunning.operation_info) = {
      response_type: "Connection"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Connection.
  rpc UpdateConnection(UpdateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{connection.name=projects/*/locations/*/connections/*}"
      body: "connection"
    };
    option (google.api.method_signature) = "connection,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Connection"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Connection.
  rpc DeleteConnection(DeleteConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/connections/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Providers in a given project and location.
  rpc ListProviders(ListProvidersRequest) returns (ListProvidersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/providers"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a provider.
  rpc GetProvider(GetProviderRequest) returns (Provider) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/providers/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Connectors in a given project and location.
  rpc ListConnectors(ListConnectorsRequest) returns (ListConnectorsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/providers/*}/connectors"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Connector.
  rpc GetConnector(GetConnectorRequest) returns (Connector) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/providers/*/connectors/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Connector Versions in a given project and location.
  rpc ListConnectorVersions(ListConnectorVersionsRequest)
      returns (ListConnectorVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/providers/*/connectors/*}/versions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single connector version.
  rpc GetConnectorVersion(GetConnectorVersionRequest)
      returns (ConnectorVersion) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/providers/*/connectors/*/versions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets schema metadata of a connection.
  // SchemaMetadata is a singleton resource for each connection.
  rpc GetConnectionSchemaMetadata(GetConnectionSchemaMetadataRequest)
      returns (ConnectionSchemaMetadata) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/connections/*/connectionSchemaMetadata}"
    };
    option (google.api.method_signature) = "name";
  }

  // Refresh runtime schema of a connection.
  rpc RefreshConnectionSchemaMetadata(RefreshConnectionSchemaMetadataRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/connections/*/connectionSchemaMetadata}:refresh"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "ConnectionSchemaMetadata"
      metadata_type: "OperationMetadata"
    };
  }

  // List schema of a runtime entities filtered by entity name.
  rpc ListRuntimeEntitySchemas(ListRuntimeEntitySchemasRequest)
      returns (ListRuntimeEntitySchemasResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/connections/*}/runtimeEntitySchemas"
    };
    option (google.api.method_signature) = "parent";
  }

  // List schema of a runtime actions filtered by action name.
  rpc ListRuntimeActionSchemas(ListRuntimeActionSchemasRequest)
      returns (ListRuntimeActionSchemasResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/connections/*}/runtimeActionSchemas"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the runtimeConfig of a location.
  // RuntimeConfig is a singleton resource for each location.
  rpc GetRuntimeConfig(GetRuntimeConfigRequest) returns (RuntimeConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/runtimeConfig}"
    };
    option (google.api.method_signature) = "name";
  }

  // GetGlobalSettings gets settings of a project.
  // GlobalSettings is a singleton resource.
  rpc GetGlobalSettings(GetGlobalSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/global/settings}"
    };
    option (google.api.method_signature) = "name";
  }
}
