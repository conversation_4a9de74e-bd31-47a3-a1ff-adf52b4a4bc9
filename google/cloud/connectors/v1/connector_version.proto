// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.connectors.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/connectors/v1/authconfig.proto";
import "google/cloud/connectors/v1/common.proto";
import "google/cloud/connectors/v1/ssl_config.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/connectors/apiv1/connectorspb;connectorspb";
option java_multiple_files = true;
option java_outer_classname = "ConnectorVersionProto";
option java_package = "com.google.cloud.connectors.v1";

// ConnectorVersion indicates a specific version of a connector.
message ConnectorVersion {
  option (google.api.resource) = {
    type: "connectors.googleapis.com/ConnectorVersion"
    pattern: "projects/{project}/locations/{location}/providers/{provider}/connectors/{connector}/versions/{version}"
  };

  // Output only. Resource name of the Version.
  // Format:
  // projects/{project}/locations/{location}/providers/{provider}/connectors/{connector}/versions/{version}
  // Only global location is supported for Connector resource.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Created time.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Updated time.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource labels to represent user-provided metadata.
  // Refer to cloud documentation on labels for more details.
  // https://cloud.google.com/compute/docs/labeling-resources
  map<string, string> labels = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Flag to mark the version indicating the launch stage.
  LaunchStage launch_stage = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ReleaseVersion of the connector, for example: "1.0.1-alpha".
  string release_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of auth configs supported by the Connector Version.
  repeated AuthConfigTemplate auth_config_templates = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of config variables needed to create a connection.
  repeated ConfigVariableTemplate config_variable_templates = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Information about the runtime features supported by the
  // Connector.
  SupportedRuntimeFeatures supported_runtime_features = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Display name.
  string display_name = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Configuration for Egress Control.
  EgressControlConfig egress_control_config = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Role grant configurations for this connector version.
  repeated RoleGrant role_grants = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Role grant configuration for this config variable. It will be
  // DEPRECATED soon.
  RoleGrant role_grant = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Ssl configuration supported by the Connector.
  SslConfigTemplate ssl_config_template = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for Connectors.GetConnectorVersion.
message GetConnectorVersionRequest {
  // Required. Resource name of the form:
  // `projects/*/locations/*/providers/*/connectors/*/versions/*`
  // Only global location is supported for ConnectorVersion resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/ConnectorVersion"
    }
  ];

  // Specifies which fields of the ConnectorVersion are returned in the
  // response. Defaults to `CUSTOMER` view.
  ConnectorVersionView view = 2;
}

// Request message for Connectors.ListConnectorVersions.
message ListConnectorVersionsRequest {
  // Required. Parent resource of the connectors, of the form:
  // `projects/*/locations/*/providers/*/connectors/*`
  // Only global location is supported for ConnectorVersion resource.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "connectors.googleapis.com/Connector"
    }
  ];

  // Page size.
  int32 page_size = 2;

  // Page token.
  string page_token = 3;

  // Specifies which fields of the ConnectorVersion are returned in the
  // response. Defaults to `BASIC` view.
  ConnectorVersionView view = 4;
}

// Response message for Connectors.ListConnectorVersions.
message ListConnectorVersionsResponse {
  // A list of connector versions.
  repeated ConnectorVersion connector_versions = 1;

  // Next page token.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Supported runtime features of a connector version. This is passed to the
// management layer to add a new connector version by the connector developer.
// Details about how this proto is passed to the management layer is covered in
// this doc - go/runtime-manifest.
message SupportedRuntimeFeatures {
  // Specifies if the connector supports entity apis like 'createEntity'.
  bool entity_apis = 1;

  // Specifies if the connector supports action apis like 'executeAction'.
  bool action_apis = 2;

  // Specifies if the connector supports 'ExecuteSqlQuery' operation.
  bool sql_query = 3;
}

// Egress control config for connector runtime. These configurations define the
// rules to identify which outbound domains/hosts needs to be whitelisted. It
// may be a static information for a particular connector version or it is
// derived from the configurations provided by the customer in Connection
// resource.
message EgressControlConfig {
  oneof oneof_backends {
    // Static Comma separated backends which are common for all Connection
    // resources. Supported formats for each backend are host:port or just
    // host (host can be ip address or domain name).
    string backends = 1;

    // Extractions Rules to extract the backends from customer provided
    // configuration.
    ExtractionRules extraction_rules = 2;
  }
}

// Extraction Rules to identity the backends from customer provided
// configuration in Connection resource.
message ExtractionRules {
  // Collection of Extraction Rule.
  repeated ExtractionRule extraction_rule = 1;
}

// Extraction Rule.
message ExtractionRule {
  // Source to extract the backend from.
  message Source {
    // Type of the source.
    SourceType source_type = 1;

    // Field identifier. For example config vaiable name.
    string field_id = 2;
  }

  // Supported Source types for extraction.
  enum SourceType {
    // Default SOURCE.
    SOURCE_TYPE_UNSPECIFIED = 0;

    // Config Variable source type.
    CONFIG_VARIABLE = 1;
  }

  // Source on which the rule is applied.
  Source source = 1;

  // Regex used to extract backend details from source. If empty, whole source
  // value will be used.
  string extraction_regex = 2;
}

// Enum to control which fields should be included in the response.
enum ConnectorVersionView {
  // CONNECTOR_VERSION_VIEW_UNSPECIFIED.
  CONNECTOR_VERSION_VIEW_UNSPECIFIED = 0;

  // Do not include role grant configs.
  CONNECTOR_VERSION_VIEW_BASIC = 1;

  // Include role grant configs.
  CONNECTOR_VERSION_VIEW_FULL = 2;
}
