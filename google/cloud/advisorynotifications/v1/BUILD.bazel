# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "advisorynotifications_proto",
    srcs = [
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "advisorynotifications_proto_with_info",
    deps = [
        ":advisorynotifications_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "advisorynotifications_java_proto",
    deps = [":advisorynotifications_proto"],
)

java_grpc_library(
    name = "advisorynotifications_java_grpc",
    srcs = [":advisorynotifications_proto"],
    deps = [":advisorynotifications_java_proto"],
)

java_gapic_library(
    name = "advisorynotifications_java_gapic",
    srcs = [":advisorynotifications_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "advisorynotifications_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "advisorynotifications_v1.yaml",
    test_deps = [
        ":advisorynotifications_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":advisorynotifications_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "advisorynotifications_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.advisorynotifications.v1.AdvisoryNotificationsServiceClientHttpJsonTest",
        "com.google.cloud.advisorynotifications.v1.AdvisoryNotificationsServiceClientTest",
    ],
    runtime_deps = [":advisorynotifications_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-advisorynotifications-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":advisorynotifications_java_gapic",
        ":advisorynotifications_java_grpc",
        ":advisorynotifications_java_proto",
        ":advisorynotifications_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "advisorynotifications_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/advisorynotifications/apiv1/advisorynotificationspb",
    protos = [":advisorynotifications_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "advisorynotifications_go_gapic",
    srcs = [":advisorynotifications_proto_with_info"],
    grpc_service_config = "advisorynotifications_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/advisorynotifications/apiv1;advisorynotifications",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "advisorynotifications_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":advisorynotifications_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-advisorynotifications-v1-go",
    deps = [
        ":advisorynotifications_go_gapic",
        ":advisorynotifications_go_gapic_srcjar-metadata.srcjar",
        ":advisorynotifications_go_gapic_srcjar-snippets.srcjar",
        ":advisorynotifications_go_gapic_srcjar-test.srcjar",
        ":advisorynotifications_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "advisorynotifications_py_gapic",
    srcs = [":advisorynotifications_proto"],
    grpc_service_config = "advisorynotifications_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "advisorynotifications_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "advisorynotifications_py_gapic_test",
    srcs = [
        "advisorynotifications_py_gapic_pytest.py",
        "advisorynotifications_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":advisorynotifications_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "advisorynotifications-v1-py",
    deps = [
        ":advisorynotifications_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "advisorynotifications_php_proto",
    deps = [":advisorynotifications_proto"],
)

php_gapic_library(
    name = "advisorynotifications_php_gapic",
    srcs = [":advisorynotifications_proto_with_info"],
    grpc_service_config = "advisorynotifications_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "advisorynotifications_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":advisorynotifications_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-advisorynotifications-v1-php",
    deps = [
        ":advisorynotifications_php_gapic",
        ":advisorynotifications_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "advisorynotifications_nodejs_gapic",
    package_name = "@google-cloud/advisorynotifications",
    src = ":advisorynotifications_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "advisorynotifications_v1_grpc_service_config.json",
    package = "google.cloud.advisorynotifications.v1",
    rest_numeric_enums = True,
    service_yaml = "advisorynotifications_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "advisorynotifications-v1-nodejs",
    deps = [
        ":advisorynotifications_nodejs_gapic",
        ":advisorynotifications_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "advisorynotifications_ruby_proto",
    deps = [":advisorynotifications_proto"],
)

ruby_grpc_library(
    name = "advisorynotifications_ruby_grpc",
    srcs = [":advisorynotifications_proto"],
    deps = [":advisorynotifications_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "advisorynotifications_ruby_gapic",
    srcs = [":advisorynotifications_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-advisory_notifications-v1"],
    grpc_service_config = "advisorynotifications_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "advisorynotifications_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":advisorynotifications_ruby_grpc",
        ":advisorynotifications_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-advisorynotifications-v1-ruby",
    deps = [
        ":advisorynotifications_ruby_gapic",
        ":advisorynotifications_ruby_grpc",
        ":advisorynotifications_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "advisorynotifications_csharp_proto",
    deps = [":advisorynotifications_proto"],
)

csharp_grpc_library(
    name = "advisorynotifications_csharp_grpc",
    srcs = [":advisorynotifications_proto"],
    deps = [":advisorynotifications_csharp_proto"],
)

csharp_gapic_library(
    name = "advisorynotifications_csharp_gapic",
    srcs = [":advisorynotifications_proto_with_info"],
    grpc_service_config = "advisorynotifications_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "advisorynotifications_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":advisorynotifications_csharp_grpc",
        ":advisorynotifications_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-advisorynotifications-v1-csharp",
    deps = [
        ":advisorynotifications_csharp_gapic",
        ":advisorynotifications_csharp_grpc",
        ":advisorynotifications_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "advisorynotifications_cc_proto",
    deps = [":advisorynotifications_proto"],
)

cc_grpc_library(
    name = "advisorynotifications_cc_grpc",
    srcs = [":advisorynotifications_proto"],
    grpc_only = True,
    deps = [":advisorynotifications_cc_proto"],
)
