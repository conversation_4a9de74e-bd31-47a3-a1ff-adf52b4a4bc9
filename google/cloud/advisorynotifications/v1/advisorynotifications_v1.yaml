type: google.api.Service
config_version: 3
name: advisorynotifications.googleapis.com
title: Advisory Notifications API

apis:
- name: google.cloud.advisorynotifications.v1.AdvisoryNotificationsService

documentation:
  summary: An API for accessing Advisory Notifications in Google Cloud

authentication:
  rules:
  - selector: 'google.cloud.advisorynotifications.v1.AdvisoryNotificationsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1009495
  documentation_uri: https://cloud.google.com/advisory-notifications/docs/overview
  api_short_name: advisorynotifications
  github_label: 'api: advisorynotifications'
  doc_tag_prefix: advisorynotifications
  organization: CLOUD
  library_settings:
  - version: google.cloud.advisorynotifications.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
