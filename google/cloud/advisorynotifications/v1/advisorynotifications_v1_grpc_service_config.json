{"methodConfig": [{"name": [{"service": "google.cloud.advisorynotifications.v1.AdvisoryNotificationsService", "method": "ListNotifications"}, {"service": "google.cloud.advisorynotifications.v1.AdvisoryNotificationsService", "method": "GetNotification"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}