// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.advisorynotifications.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AdvisoryNotifications.V1";
option go_package = "cloud.google.com/go/advisorynotifications/apiv1/advisorynotificationspb;advisorynotificationspb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.advisorynotifications.v1";
option php_namespace = "Google\\Cloud\\AdvisoryNotifications\\V1";
option ruby_package = "Google::Cloud::AdvisoryNotifications::V1";
option (google.api.resource_definition) = {
  type: "advisorynotifications.googleapis.com/Location"
  pattern: "organizations/{organization}/locations/{location}"
  pattern: "projects/{project}/locations/{location}"
};

// Service to manage Security and Privacy Notifications.
service AdvisoryNotificationsService {
  option (google.api.default_host) = "advisorynotifications.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists notifications under a given parent.
  rpc ListNotifications(ListNotificationsRequest)
      returns (ListNotificationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/locations/*}/notifications"
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*}/notifications"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a notification.
  rpc GetNotification(GetNotificationRequest) returns (Notification) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/notifications/*}"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/notifications/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Get notification settings.
  rpc GetSettings(GetSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/settings}"
      additional_bindings { get: "/v1/{name=projects/*/locations/*/settings}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Update notification settings.
  rpc UpdateSettings(UpdateSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      patch: "/v1/{settings.name=organizations/*/locations/*/settings}"
      body: "settings"
      additional_bindings {
        patch: "/v1/{settings.name=projects/*/locations/*/settings}"
        body: "settings"
      }
    };
    option (google.api.method_signature) = "settings";
  }
}

// Notification view.
enum NotificationView {
  // Not specified, equivalent to BASIC.
  NOTIFICATION_VIEW_UNSPECIFIED = 0;

  // Server responses only include title, creation time and Notification ID.
  // Note: for internal use responses also include the last update time,
  // the latest message text and whether notification has attachments.
  BASIC = 1;

  // Include everything.
  FULL = 2;
}

// Status of localized text.
enum LocalizationState {
  // Not used.
  LOCALIZATION_STATE_UNSPECIFIED = 0;

  // Localization is not applicable for requested language. This can happen
  // when:
  // - The requested language was not supported by Advisory Notifications at the
  // time of localization (including notifications created before the
  // localization feature was launched).
  // - The requested language is English, so only the English text is returned.
  LOCALIZATION_STATE_NOT_APPLICABLE = 1;

  // Localization for requested language is in progress, and not ready yet.
  LOCALIZATION_STATE_PENDING = 2;

  // Localization for requested language is completed.
  LOCALIZATION_STATE_COMPLETED = 3;
}

// Type of notification
enum NotificationType {
  // Default type
  NOTIFICATION_TYPE_UNSPECIFIED = 0;

  // Security and privacy advisory notifications
  NOTIFICATION_TYPE_SECURITY_PRIVACY_ADVISORY = 1;

  // Sensitive action notifications
  NOTIFICATION_TYPE_SENSITIVE_ACTIONS = 2;

  // General security MSA
  NOTIFICATION_TYPE_SECURITY_MSA = 3;

  // Threat horizons MSA
  NOTIFICATION_TYPE_THREAT_HORIZONS = 4;
}

// A notification object for notifying customers about security and privacy
// issues.
message Notification {
  option (google.api.resource) = {
    type: "advisorynotifications.googleapis.com/Notification"
    pattern: "organizations/{organization}/locations/{location}/notifications/{notification}"
    pattern: "projects/{project}/locations/{location}/notifications/{notification}"
    plural: "notifications"
    singular: "notification"
  };

  // The resource name of the notification.
  // Format:
  // organizations/{organization}/locations/{location}/notifications/{notification}
  // or projects/{project}/locations/{location}/notifications/{notification}.
  string name = 1;

  // The subject line of the notification.
  Subject subject = 2;

  // A list of messages in the notification.
  repeated Message messages = 3;

  // Output only. Time the notification was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Type of notification
  NotificationType notification_type = 12;
}

// A text object containing the English text and its localized copies.
message Text {
  // The English copy.
  string en_text = 1;

  // The requested localized copy (if applicable).
  string localized_text = 2;

  // Status of the localization.
  LocalizationState localization_state = 3;
}

// A subject line of a notification.
message Subject {
  // The text content.
  Text text = 1;
}

// A message which contains notification details.
message Message {
  // A message body containing text.
  message Body {
    // The text content of the message body.
    Text text = 1;
  }

  // The message content.
  Body body = 1;

  // The attachments to download.
  repeated Attachment attachments = 2;

  // The Message creation timestamp.
  google.protobuf.Timestamp create_time = 3;

  // Time when Message was localized
  google.protobuf.Timestamp localization_time = 4;
}

// Attachment with specific information about the issue.
message Attachment {
  // Data type of the attachment.
  oneof data {
    // A CSV file attachment. Max size is 10 MB.
    Csv csv = 2;
  }

  // The title of the attachment.
  string display_name = 1;
}

// A representation of a CSV file attachment, as a list of column headers and
// a list of data rows.
message Csv {
  // A representation of a single data row in a CSV file.
  message CsvRow {
    // The data entries in a CSV file row, as a string array rather than a
    // single comma-separated string.
    repeated string entries = 1;
  }

  // The list of headers for data columns in a CSV file.
  repeated string headers = 1;

  // The list of data rows in a CSV file, as string arrays rather than as a
  // single comma-separated string.
  repeated CsvRow data_rows = 2;
}

// Request for fetching all notifications for a given parent.
message ListNotificationsRequest {
  // Required. The parent, which owns this collection of notifications.
  // Must be of the form "organizations/{organization}/locations/{location}"
  // or "projects/{project}/locations/{location}".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "advisorynotifications.googleapis.com/Notification"
    }
  ];

  // The maximum number of notifications to return. The service may return
  // fewer than this value. If unspecified or equal to 0, at most 50
  // notifications will be returned. The maximum value is 50; values above 50
  // will be coerced to 50.
  int32 page_size = 2;

  // A page token returned from a previous request.
  // When paginating, all other parameters provided in the request
  // must match the call that returned the page token.
  string page_token = 3;

  // Specifies which parts of the notification resource should be returned
  // in the response.
  NotificationView view = 4;

  // ISO code for requested localization language.  If unset, will be
  // interpereted as "en". If the requested language is valid, but not supported
  // for this notification, English will be returned with an "Not applicable"
  // LocalizationState. If the ISO code is invalid (i.e. not a real language),
  // this RPC will throw an error.
  string language_code = 5;
}

// Response of ListNotifications endpoint.
message ListNotificationsResponse {
  // List of notifications under a given parent.
  repeated Notification notifications = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Estimation of a total number of notifications.
  int32 total_size = 3;
}

// Request for fetching a notification.
message GetNotificationRequest {
  // Required. A name of the notification to retrieve.
  // Format:
  // organizations/{organization}/locations/{location}/notifications/{notification}
  // or projects/{projects}/locations/{location}/notifications/{notification}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "advisorynotifications.googleapis.com/Notification"
    }
  ];

  // ISO code for requested localization language. If unset, will be
  // interpereted as "en". If the requested language is valid, but not supported
  // for this notification, English will be returned with an "Not applicable"
  // LocalizationState. If the ISO code is invalid (i.e. not a real language),
  // this RPC will throw an error.
  string language_code = 5;
}

// Settings for Advisory Notifications.
message Settings {
  option (google.api.resource) = {
    type: "advisorynotifications.googleapis.com/Settings"
    pattern: "organizations/{organization}/locations/{location}/settings"
    pattern: "projects/{project}/locations/{location}/settings"
    plural: "settings"
    singular: "settings"
  };

  // Identifier. The resource name of the settings to retrieve.
  // Format:
  // organizations/{organization}/locations/{location}/settings or
  // projects/{projects}/locations/{location}/settings.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Map of each notification type and its settings to get/set all
  // settings at once. The server will validate the value for each notification
  // type.
  map<string, NotificationSettings> notification_settings = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. Fingerprint for optimistic concurrency returned in Get requests.
  // Must be provided for Update requests. If the value provided does not match
  // the value known to the server, ABORTED will be thrown, and the client
  // should retry the read-modify-write cycle.
  string etag = 3 [(google.api.field_behavior) = REQUIRED];
}

// Settings for each NotificationType.
message NotificationSettings {
  // Whether the associated NotificationType is enabled.
  bool enabled = 1;
}

// Request of GetSettings endpoint.
message GetSettingsRequest {
  // Required. The resource name of the settings to retrieve.
  // Format:
  // organizations/{organization}/locations/{location}/settings or
  // projects/{projects}/locations/{location}/settings.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "advisorynotifications.googleapis.com/Settings"
    }
  ];
}

// Request of UpdateSettings endpoint.
message UpdateSettingsRequest {
  // Required. New settings.
  Settings settings = 1 [(google.api.field_behavior) = REQUIRED];
}
