// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.backupdr.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/backupdr/v1/backupplan.proto";
import "google/cloud/backupdr/v1/backupplanassociation.proto";
import "google/cloud/backupdr/v1/backupvault.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Cloud.BackupDR.V1";
option go_package = "cloud.google.com/go/backupdr/apiv1/backupdrpb;backupdrpb";
option java_multiple_files = true;
option java_outer_classname = "BackupDRProto";
option java_package = "com.google.cloud.backupdr.v1";
option php_namespace = "Google\\Cloud\\BackupDR\\V1";
option ruby_package = "Google::Cloud::BackupDR::V1";

// The BackupDR Service
service BackupDR {
  option (google.api.default_host) = "backupdr.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists ManagementServers in a given project and location.
  rpc ListManagementServers(ListManagementServersRequest)
      returns (ListManagementServersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/managementServers"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ManagementServer.
  rpc GetManagementServer(GetManagementServerRequest)
      returns (ManagementServer) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/managementServers/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new ManagementServer in a given project and location.
  rpc CreateManagementServer(CreateManagementServerRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/managementServers"
      body: "management_server"
    };
    option (google.api.method_signature) =
        "parent,management_server,management_server_id";
    option (google.longrunning.operation_info) = {
      response_type: "ManagementServer"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single ManagementServer.
  rpc DeleteManagementServer(DeleteManagementServerRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/managementServers/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new BackupVault in a given project and location.
  rpc CreateBackupVault(CreateBackupVaultRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/backupVaults"
      body: "backup_vault"
    };
    option (google.api.method_signature) =
        "parent,backup_vault,backup_vault_id";
    option (google.longrunning.operation_info) = {
      response_type: "BackupVault"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists BackupVaults in a given project and location.
  rpc ListBackupVaults(ListBackupVaultsRequest)
      returns (ListBackupVaultsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/backupVaults"
    };
    option (google.api.method_signature) = "parent";
  }

  // FetchUsableBackupVaults lists usable BackupVaults in a given project and
  // location. Usable BackupVault are the ones that user has
  // backupdr.backupVaults.get permission.
  rpc FetchUsableBackupVaults(FetchUsableBackupVaultsRequest)
      returns (FetchUsableBackupVaultsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/backupVaults:fetchUsable"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a BackupVault.
  rpc GetBackupVault(GetBackupVaultRequest) returns (BackupVault) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupVaults/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the settings of a BackupVault.
  rpc UpdateBackupVault(UpdateBackupVaultRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{backup_vault.name=projects/*/locations/*/backupVaults/*}"
      body: "backup_vault"
    };
    option (google.api.method_signature) = "backup_vault,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "BackupVault"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a BackupVault.
  rpc DeleteBackupVault(DeleteBackupVaultRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backupVaults/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists DataSources in a given project and location.
  rpc ListDataSources(ListDataSourcesRequest)
      returns (ListDataSourcesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/backupVaults/*}/dataSources"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a DataSource.
  rpc GetDataSource(GetDataSourceRequest) returns (DataSource) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupVaults/*/dataSources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the settings of a DataSource.
  rpc UpdateDataSource(UpdateDataSourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{data_source.name=projects/*/locations/*/backupVaults/*/dataSources/*}"
      body: "data_source"
    };
    option (google.api.method_signature) = "data_source,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "DataSource"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Backups in a given project and location.
  rpc ListBackups(ListBackupsRequest) returns (ListBackupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/backupVaults/*/dataSources/*}/backups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a Backup.
  rpc GetBackup(GetBackupRequest) returns (Backup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupVaults/*/dataSources/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the settings of a Backup.
  rpc UpdateBackup(UpdateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{backup.name=projects/*/locations/*/backupVaults/*/dataSources/*/backups/*}"
      body: "backup"
    };
    option (google.api.method_signature) = "backup,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a Backup.
  rpc DeleteBackup(DeleteBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backupVaults/*/dataSources/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "OperationMetadata"
    };
  }

  // Restore from a Backup
  rpc RestoreBackup(RestoreBackupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/backupVaults/*/dataSources/*/backups/*}:restore"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "RestoreBackupResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Create a BackupPlan
  rpc CreateBackupPlan(CreateBackupPlanRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/backupPlans"
      body: "backup_plan"
    };
    option (google.api.method_signature) = "parent,backup_plan,backup_plan_id";
    option (google.longrunning.operation_info) = {
      response_type: "BackupPlan"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of a single BackupPlan.
  rpc GetBackupPlan(GetBackupPlanRequest) returns (BackupPlan) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupPlans/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists BackupPlans in a given project and location.
  rpc ListBackupPlans(ListBackupPlansRequest)
      returns (ListBackupPlansResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/backupPlans"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a single BackupPlan.
  rpc DeleteBackupPlan(DeleteBackupPlanRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backupPlans/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Create a BackupPlanAssociation
  rpc CreateBackupPlanAssociation(CreateBackupPlanAssociationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/backupPlanAssociations"
      body: "backup_plan_association"
    };
    option (google.api.method_signature) =
        "parent,backup_plan_association,backup_plan_association_id";
    option (google.longrunning.operation_info) = {
      response_type: "BackupPlanAssociation"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of a single BackupPlanAssociation.
  rpc GetBackupPlanAssociation(GetBackupPlanAssociationRequest)
      returns (BackupPlanAssociation) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupPlanAssociations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists BackupPlanAssociations in a given project and location.
  rpc ListBackupPlanAssociations(ListBackupPlanAssociationsRequest)
      returns (ListBackupPlanAssociationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/backupPlanAssociations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a single BackupPlanAssociation.
  rpc DeleteBackupPlanAssociation(DeleteBackupPlanAssociationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backupPlanAssociations/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Triggers a new Backup.
  rpc TriggerBackup(TriggerBackupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/backupPlanAssociations/*}:triggerBackup"
      body: "*"
    };
    option (google.api.method_signature) = "name,rule_id";
    option (google.longrunning.operation_info) = {
      response_type: "BackupPlanAssociation"
      metadata_type: "OperationMetadata"
    };
  }

  // Initializes the service related config for a project.
  rpc InitializeService(InitializeServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/serviceConfig}:initialize"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "InitializeServiceResponse"
      metadata_type: "OperationMetadata"
    };
  }
}

// Network configuration for ManagementServer instance.
message NetworkConfig {
  // VPC peering modes supported by Cloud BackupDR.
  enum PeeringMode {
    // Peering mode not set.
    PEERING_MODE_UNSPECIFIED = 0;

    // Connect using Private Service Access to the Management Server. Private
    // services access provides an IP address range for multiple Google Cloud
    // services, including Cloud BackupDR.
    PRIVATE_SERVICE_ACCESS = 1;
  }

  // Optional. The resource name of the Google Compute Engine VPC network to
  // which the ManagementServer instance is connected.
  string network = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The network connect mode of the ManagementServer instance. For
  // this version, only PRIVATE_SERVICE_ACCESS is supported.
  PeeringMode peering_mode = 2 [(google.api.field_behavior) = OPTIONAL];
}

// ManagementURI for the Management Server resource.
message ManagementURI {
  // Output only. The ManagementServer AGM/RD WebUI URL.
  string web_ui = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ManagementServer AGM/RD API URL.
  string api = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ManagementURI depending on the Workforce Identity i.e. either 1p or 3p.
message WorkforceIdentityBasedManagementURI {
  // Output only. First party Management URI for Google Identities.
  string first_party_management_uri = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Third party Management URI for External Identity Providers.
  string third_party_management_uri = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// OAuth Client ID depending on the Workforce Identity i.e. either 1p or 3p,
message WorkforceIdentityBasedOAuth2ClientID {
  // Output only. First party OAuth Client ID for Google Identities.
  string first_party_oauth2_client_id = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Third party OAuth Client ID for External Identity Providers.
  string third_party_oauth2_client_id = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ManagementServer describes a single BackupDR ManagementServer instance.
message ManagementServer {
  option (google.api.resource) = {
    type: "backupdr.googleapis.com/ManagementServer"
    pattern: "projects/{project}/locations/{location}/managementServers/{managementserver}"
    plural: "managementServers"
    singular: "managementServer"
  };

  // Type of backup service resource.
  enum InstanceType {
    // Instance type is not mentioned.
    INSTANCE_TYPE_UNSPECIFIED = 0;

    // Instance for backup and restore management (i.e., AGM).
    BACKUP_RESTORE = 1;
  }

  // State of Management server instance.
  enum InstanceState {
    // State not set.
    INSTANCE_STATE_UNSPECIFIED = 0;

    // The instance is being created.
    CREATING = 1;

    // The instance has been created and is fully usable.
    READY = 2;

    // The instance configuration is being updated. Certain kinds of updates
    // may cause the instance to become unusable while the update is in
    // progress.
    UPDATING = 3;

    // The instance is being deleted.
    DELETING = 4;

    // The instance is being repaired and may be unstable.
    REPAIRING = 5;

    // Maintenance is being performed on this instance.
    MAINTENANCE = 6;

    // The instance is experiencing an issue and might be unusable. You can get
    // further details from the statusMessage field of Instance resource.
    ERROR = 7;
  }

  // Output only. Identifier. The resource name.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Optional. The description of the ManagementServer instance (2048 characters
  // or less).
  string description = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Resource labels to represent user provided metadata.
  // Labels currently defined:
  // 1. migrate_from_go=<false|true>
  //    If set to true, the MS is created in migration ready mode.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time when the instance was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the instance was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The type of the ManagementServer resource.
  InstanceType type = 14 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The hostname or ip address of the exposed AGM endpoints, used
  // by clients to connect to AGM/RD graphical user interface and APIs.
  ManagementURI management_uri = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The hostnames of the exposed AGM endpoints for both types of
  // user i.e. 1p and 3p, used to connect AGM/RM UI.
  WorkforceIdentityBasedManagementURI workforce_identity_based_management_uri =
      16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ManagementServer state.
  InstanceState state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. VPC networks to which the ManagementServer instance is connected.
  // For this version, only a single network is supported. This field is
  // optional if MS is created without PSA
  repeated NetworkConfig networks = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Server specified ETag for the ManagementServer resource to
  // prevent simultaneous updates from overwiting each other.
  string etag = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The OAuth 2.0 client id is required to make API calls to the
  // BackupDR instance API of this ManagementServer. This is the value that
  // should be provided in the 'aud' field of the OIDC ID Token (see openid
  // specification
  // https://openid.net/specs/openid-connect-core-1_0.html#IDToken).
  string oauth2_client_id = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The OAuth client IDs for both types of user i.e. 1p and 3p.
  WorkforceIdentityBasedOAuth2ClientID
      workforce_identity_based_oauth2_client_id = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The hostname or ip address of the exposed AGM endpoints, used
  // by BAs to connect to BA proxy.
  repeated string ba_proxy_uri = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  google.protobuf.BoolValue satisfies_pzs = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 20 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for listing management servers.
message ListManagementServersRequest {
  // Required. The project and location for which to retrieve management servers
  // information, in the format 'projects/{project_id}/locations/{location}'. In
  // Cloud BackupDR, locations map to Google Cloud regions, for example
  // **us-central1**. To retrieve management servers for all locations, use "-"
  // for the
  // '{location}' value.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "backupdr.googleapis.com/ManagementServer"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  optional string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  optional string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for listing management servers.
message ListManagementServersResponse {
  // The list of ManagementServer instances in the project for the specified
  // location.
  //
  // If the '{location}' value in the request is "-", the response contains a
  // list of instances from all locations. In case any location is unreachable,
  // the response will only return management servers in reachable locations and
  // the 'unreachable' field will be populated with a list of unreachable
  // locations.
  repeated ManagementServer management_servers = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for getting a management server instance.
message GetManagementServerRequest {
  // Required. Name of the management server resource name, in the format
  // 'projects/{project_id}/locations/{location}/managementServers/{resource_name}'
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "backupdr.googleapis.com/ManagementServer"
    }
  ];
}

// Request message for creating a management server instance.
message CreateManagementServerRequest {
  // Required. The management server project and location in the format
  // 'projects/{project_id}/locations/{location}'. In Cloud Backup and DR
  // locations map to Google Cloud regions, for example **us-central1**.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "backupdr.googleapis.com/ManagementServer"
    }
  ];

  // Required. The name of the management server to create. The name must be
  // unique for the specified project and location.
  string management_server_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A [management server
  // resource][google.cloud.backupdr.v1.ManagementServer]
  ManagementServer management_server = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for deleting a management server instance.
message DeleteManagementServerRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "backupdr.googleapis.com/ManagementServer"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for initializing the service.
message InitializeServiceRequest {
  // Required. The resource name of the serviceConfig used to initialize the
  // service. Format:
  // `projects/{project_id}/locations/{location}/serviceConfig`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource type to which the default service config will be
  // applied. Examples include, "compute.googleapis.com/Instance" and
  // "storage.googleapis.com/Bucket".
  string resource_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Response message for initializing the service.
message InitializeServiceResponse {
  // The resource name of the default `BackupVault` created.
  // Format:
  // `projects/{project_id}/locations/{location}/backupVaults/{backup_vault_id}`.
  string backup_vault_name = 1;

  // The resource name of the default `BackupPlan` created.
  // Format:
  // `projects/{project_id}/locations/{location}/backupPlans/{backup_plan_id}`.
  string backup_plan_name = 2;
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have
  // [google.longrunning.Operation.error][google.longrunning.Operation.error]
  // value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to 'Code.CANCELLED'.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. AdditionalInfo contains additional Info related to backup plan
  // association resource.
  map<string, string> additional_info = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
