{"methodConfig": [{"name": [{"service": "google.cloud.backupdr.v1.BackupDR", "method": "ListManagementServers"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "GetManagementServer"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "ListBackupVaults"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "FetchUsableBackupVaults"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "CreateBackupVault"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "DeleteBackupVault"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "GetBackupVault"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "GetDataSource"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "ListDataSources"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "ListBackups"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "GetBackup"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "InitializeService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.backupdr.v1.BackupDR", "method": "CreateManagementServer"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "DeleteManagementServer"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "UpdateManagementServer"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "UpdateBackupVault"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "UpdateDataSource"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "UpdateBackup"}, {"service": "google.cloud.backupdr.v1.BackupDR", "method": "RestoreBackup"}], "timeout": "60s"}]}