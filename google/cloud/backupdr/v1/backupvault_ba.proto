// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.backupdr.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BackupDR.V1";
option go_package = "cloud.google.com/go/backupdr/apiv1/backupdrpb;backupdrpb";
option java_multiple_files = true;
option java_outer_classname = "BackupvaultBaProto";
option java_package = "com.google.cloud.backupdr.v1";
option php_namespace = "Google\\Cloud\\BackupDR\\V1";
option ruby_package = "Google::Cloud::BackupDR::V1";

// BackupApplianceBackupProperties represents BackupDR backup appliance's
// properties.
message BackupApplianceBackupProperties {
  // Output only. The numeric generation ID of the backup (monotonically
  // increasing).
  optional int32 generation_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when this backup object was finalized (if none,
  // backup is not finalized).
  optional google.protobuf.Timestamp finalize_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The earliest timestamp of data available in this Backup.
  optional google.protobuf.Timestamp recovery_range_start_time = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The latest timestamp of data available in this Backup.
  optional google.protobuf.Timestamp recovery_range_end_time = 4
      [(google.api.field_behavior) = OPTIONAL];
}
