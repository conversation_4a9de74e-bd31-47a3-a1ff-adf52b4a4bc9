// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.backupdr.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/backupdr/v1/backupvault_ba.proto";
import "google/cloud/backupdr/v1/backupvault_gce.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.BackupDR.V1";
option go_package = "cloud.google.com/go/backupdr/apiv1/backupdrpb;backupdrpb";
option java_multiple_files = true;
option java_outer_classname = "BackupVaultProto";
option java_package = "com.google.cloud.backupdr.v1";
option php_namespace = "Google\\Cloud\\BackupDR\\V1";
option ruby_package = "Google::Cloud::BackupDR::V1";

// Message describing a BackupVault object.
message BackupVault {
  option (google.api.resource) = {
    type: "backupdr.googleapis.com/BackupVault"
    pattern: "projects/{project}/locations/{location}/backupVaults/{backupvault}"
    plural: "backupVaults"
    singular: "backupVault"
  };

  // Holds the state of the backup vault resource.
  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // The backup vault is being created.
    CREATING = 1;

    // The backup vault has been created and is fully usable.
    ACTIVE = 2;

    // The backup vault is being deleted.
    DELETING = 3;

    // The backup vault is experiencing an issue and might be unusable.
    ERROR = 4;
  }

  // Holds the access restriction for the backup vault.
  enum AccessRestriction {
    // Access restriction not set. If user does not provide any value or pass
    // this value, it will be changed to WITHIN_ORGANIZATION.
    ACCESS_RESTRICTION_UNSPECIFIED = 0;

    // Access to or from resources outside your current project will be denied.
    WITHIN_PROJECT = 1;

    // Access to or from resources outside your current organization will be
    // denied.
    WITHIN_ORGANIZATION = 2;

    // No access restriction.
    UNRESTRICTED = 3;

    // Access to or from resources outside your current organization will be
    // denied except for backup appliance.
    WITHIN_ORG_BUT_UNRESTRICTED_FOR_BA = 4;
  }

  // Output only. Identifier. Name of the backup vault to create. It must have
  // the
  // format`"projects/{project}/locations/{location}/backupVaults/{backupvault}"`.
  // `{backupvault}` cannot be changed after creation. It must be between 3-63
  // characters long and must be unique within the project and location.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Optional. The description of the BackupVault instance (2048 characters or
  // less).
  optional string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Resource labels to represent user provided metadata.
  // No labels currently defined:
  map<string, string> labels = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time when the instance was created.
  optional google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the instance was updated.
  optional google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The default and minimum enforced retention for each backup within
  // the backup vault.  The enforced retention for each backup can be extended.
  optional google.protobuf.Duration backup_minimum_enforced_retention_duration =
      20 [(google.api.field_behavior) = REQUIRED];

  // Output only. Set to true when there are no backups nested under this
  // resource.
  optional bool deletable = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Server specified ETag for the backup vault resource to
  // prevent simultaneous updates from overwiting each other.
  optional string etag = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The BackupVault resource instance state.
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Time after which the BackupVault resource is locked.
  optional google.protobuf.Timestamp effective_time = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The number of backups in this backup vault.
  int64 backup_count = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Service account used by the BackupVault Service for this
  // BackupVault.  The user should grant this account permissions in their
  // workload project to enable the service to run backups and restores there.
  string service_account = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total size of the storage used by all backup resources.
  int64 total_stored_bytes = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Immutable after resource creation until resource deletion.
  string uid = 21 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. User annotations. See https://google.aip.dev/128#annotations
  // Stores small amounts of arbitrary data.
  map<string, string> annotations = 22 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Note: This field is added for future use case and will not be
  // supported in the current release.
  //
  // Access restriction for the backup vault.
  // Default value is WITHIN_ORGANIZATION if not provided during creation.
  AccessRestriction access_restriction = 24
      [(google.api.field_behavior) = OPTIONAL];
}

// Message describing a DataSource object.
// Datasource object used to represent Datasource details for both admin and
// basic view.
message DataSource {
  option (google.api.resource) = {
    type: "backupdr.googleapis.com/DataSource"
    pattern: "projects/{project}/locations/{location}/backupVaults/{backupvault}/dataSources/{datasource}"
    plural: "dataSources"
    singular: "dataSource"
  };

  // Holds the state of the data source resource.
  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // The data source is being created.
    CREATING = 1;

    // The data source has been created and is fully usable.
    ACTIVE = 2;

    // The data source is being deleted.
    DELETING = 3;

    // The data source is experiencing an issue and might be unusable.
    ERROR = 4;
  }

  // Output only. Identifier. Name of the datasource to create.
  // It must have the
  // format`"projects/{project}/locations/{location}/backupVaults/{backupvault}/dataSources/{datasource}"`.
  // `{datasource}` cannot be changed after creation. It must be between 3-63
  // characters long and must be unique within the backup vault.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. The DataSource resource instance state.
  State state = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels to represent user provided metadata.
  // No labels currently defined:
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time when the instance was created.
  optional google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the instance was updated.
  optional google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Number of backups in the data source.
  optional int64 backup_count = 7;

  // Server specified ETag for the ManagementServer resource to prevent
  // simultaneous updates from overwiting each other.
  optional string etag = 14;

  // The number of bytes (metadata and data) stored in this datasource.
  optional int64 total_stored_bytes = 23;

  // Output only. The backup configuration state.
  BackupConfigState config_state = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Details of how the resource is configured for backup.
  BackupConfigInfo backup_config_info = 25
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The source resource that is represented by this DataSource. It can be a
  // Google Cloud resource, or one backed up by a Backup Appliance.
  oneof source_resource {
    // The backed up resource is a Google Cloud resource.
    // The word 'DataSource' was included in the names to indicate that this is
    // the representation of the Google Cloud resource used within the
    // DataSource object.
    DataSourceGcpResource data_source_gcp_resource = 26;

    // The backed up resource is a backup appliance application.
    DataSourceBackupApplianceApplication
        data_source_backup_appliance_application = 27;
  }
}

// BackupConfigInfo has information about how the resource is configured
// for Backup and about the most recent backup to this vault.
message BackupConfigInfo {
  // LastBackupstate tracks whether the last backup was not yet started,
  // successful, failed, or could not be run because of the lack of permissions.
  enum LastBackupState {
    // Status not set.
    LAST_BACKUP_STATE_UNSPECIFIED = 0;

    // The first backup has not yet completed
    FIRST_BACKUP_PENDING = 1;

    // The most recent backup was successful
    SUCCEEDED = 2;

    // The most recent backup failed
    FAILED = 3;

    // The most recent backup could not be run/failed because of the lack of
    // permissions
    PERMISSION_DENIED = 4;
  }

  // Output only. The status of the last backup to this BackupVault
  LastBackupState last_backup_state = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If the last backup were successful, this field has the
  // consistency date.
  google.protobuf.Timestamp last_successful_backup_consistency_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If the last backup failed, this field has the error message.
  google.rpc.Status last_backup_error = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Configuration Info has the resource format-specific configuration.
  oneof backup_config {
    // Configuration for a Google Cloud resource.
    GcpBackupConfig gcp_backup_config = 4;

    // Configuration for an application backed up by a Backup Appliance.
    BackupApplianceBackupConfig backup_appliance_backup_config = 5;
  }
}

// GcpBackupConfig captures the Backup configuration details for Google Cloud
// resources. All Google Cloud resources regardless of type are protected with
// backup plan associations.
message GcpBackupConfig {
  // The name of the backup plan.
  string backup_plan = 1 [(google.api.resource_reference) = {
    type: "backupdr.googleapis.com/BackupPlan"
  }];

  // The description of the backup plan.
  string backup_plan_description = 2;

  // The name of the backup plan association.
  string backup_plan_association = 3 [(google.api.resource_reference) = {
    type: "backupdr.googleapis.com/BackupPlanAssociation"
  }];

  // The names of the backup plan rules which point to this backupvault
  repeated string backup_plan_rules = 4;
}

// BackupApplianceBackupConfig captures the backup configuration for
// applications that are protected by Backup Appliances.
message BackupApplianceBackupConfig {
  // The name of the backup appliance.
  string backup_appliance_name = 1;

  // The ID of the backup appliance.
  int64 backup_appliance_id = 2;

  // The ID of the SLA of this application.
  int64 sla_id = 3;

  // The name of the application.
  string application_name = 4;

  // The name of the host where the application is running.
  string host_name = 5;

  // The name of the SLT associated with the application.
  string slt_name = 6;

  // The name of the SLP associated with the application.
  string slp_name = 7;
}

// DataSourceGcpResource is used for protected resources that are Google Cloud
// Resources. This name is easeier to understand than GcpResourceDataSource or
// GcpDataSourceResource
message DataSourceGcpResource {
  // Output only. Full resource pathname URL of the source Google Cloud
  // resource.
  string gcp_resourcename = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Location of the resource: <region>/<zone>/"global"/"unspecified".
  string location = 2;

  // The type of the Google Cloud resource. Use the Unified Resource Type,
  // eg. compute.googleapis.com/Instance.
  string type = 3;

  // gcp_Properties has properties of the Google Cloud Resource.
  oneof gcp_resource_properties {
    // ComputeInstanceDataSourceProperties has a subset of Compute Instance
    // properties that are useful at the Datasource level.
    ComputeInstanceDataSourceProperties compute_instance_datasource_properties =
        4;
  }
}

// BackupApplianceApplication describes a Source Resource when it is an
// application backed up by a BackupAppliance.
message DataSourceBackupApplianceApplication {
  // The name of the Application as known to the Backup Appliance.
  string application_name = 1;

  // Appliance name.
  string backup_appliance = 2;

  // Appliance Id of the Backup Appliance.
  int64 appliance_id = 3;

  // The type of the application. e.g. VMBackup
  string type = 4;

  // The appid field of the application within the Backup Appliance.
  int64 application_id = 8;

  // Hostname of the host where the application is running.
  string hostname = 6;

  // Hostid of the application host.
  int64 host_id = 7;
}

// ServiceLockInfo represents the details of a lock taken by the service on a
// Backup resource.
message ServiceLockInfo {
  // Output only. The name of the operation that created this lock.
  // The lock will automatically be released when the operation completes.
  string operation = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// BackupApplianceLockInfo contains metadata about the backupappliance that
// created the lock.
message BackupApplianceLockInfo {
  // Required. The ID of the backup/recovery appliance that created this lock.
  int64 backup_appliance_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the backup/recovery appliance that created this lock.
  string backup_appliance_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The reason for the lock: e.g. MOUNT/RESTORE/BACKUP/etc.  The
  // value of this string is only meaningful to the client and it is not
  // interpreted by the BackupVault service.
  string lock_reason = 5 [(google.api.field_behavior) = REQUIRED];

  // The information about this lock.
  oneof lock_source {
    // The job name on the backup/recovery appliance that created this lock.
    string job_name = 6;

    // The image name that depends on this Backup.
    string backup_image = 7;

    // The SLA on the backup/recovery appliance that owns the lock.
    int64 sla_id = 8;
  }
}

// BackupLock represents a single lock on a Backup resource.  An unexpired
// lock on a Backup prevents the Backup from being deleted.
message BackupLock {
  // Required. The time after which this lock is not considered valid and will
  // no longer protect the Backup from deletion.
  google.protobuf.Timestamp lock_until_time = 1
      [(google.api.field_behavior) = REQUIRED];

  // Metadata about the owner and reason for the lock.
  oneof ClientLockInfo {
    // If the client is a backup and recovery appliance, this
    // contains metadata about why the lock exists.
    BackupApplianceLockInfo backup_appliance_lock_info = 3;

    // Output only. Contains metadata about the lock exist for Google Cloud
    // native backups.
    ServiceLockInfo service_lock_info = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// Message describing a Backup object.
message Backup {
  option (google.api.resource) = {
    type: "backupdr.googleapis.com/Backup"
    pattern: "projects/{project}/locations/{location}/backupVaults/{backupvault}/dataSources/{datasource}/backups/{backup}"
    plural: "backups"
    singular: "backup"
  };

  // Holds the state of the backup resource.
  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // The backup is being created.
    CREATING = 1;

    // The backup has been created and is fully usable.
    ACTIVE = 2;

    // The backup is being deleted.
    DELETING = 3;

    // The backup is experiencing an issue and might be unusable.
    ERROR = 4;
  }

  // Type of the backup, scheduled or ondemand.
  enum BackupType {
    // Backup type is unspecified.
    BACKUP_TYPE_UNSPECIFIED = 0;

    // Scheduled backup.
    SCHEDULED = 1;

    // On demand backup.
    ON_DEMAND = 2;
  }

  // GCPBackupPlanInfo captures the plan configuration details of Google Cloud
  // resources at the time of backup.
  message GCPBackupPlanInfo {
    // Resource name of backup plan by which workload is protected at the time
    // of the backup.
    // Format:
    // projects/{project}/locations/{location}/backupPlans/{backupPlanId}
    string backup_plan = 1 [(google.api.resource_reference) = {
      type: "backupdr.googleapis.com/BackupPlan"
    }];

    // The rule id of the backup plan which triggered this backup in case of
    // scheduled backup or used for
    string backup_plan_rule_id = 2;
  }

  // Output only. Identifier. Name of the backup to create. It must have the
  // format`"projects/<project>/locations/<location>/backupVaults/<backupvault>/dataSources/{datasource}/backups/{backup}"`.
  // `{backup}` cannot be changed after creation. It must be between 3-63
  // characters long and must be unique within the datasource.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. The description of the Backup instance (2048 characters or
  // less).
  optional string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the instance was created.
  optional google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the instance was updated.
  optional google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels to represent user provided metadata.
  // No labels currently defined.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The backup can not be deleted before this time.
  optional google.protobuf.Timestamp enforced_retention_end_time = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. When this backup is automatically expired.
  optional google.protobuf.Timestamp expire_time = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The point in time when this backup was captured from the
  // source.
  optional google.protobuf.Timestamp consistency_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Server specified ETag to prevent updates from overwriting each
  // other.
  optional string etag = 11 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The Backup resource instance state.
  State state = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of BackupLocks taken by the service to prevent the
  // deletion of the backup.
  repeated BackupLock service_locks = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The list of BackupLocks taken by the accessor Backup Appliance.
  repeated BackupLock backup_appliance_locks = 18
      [(google.api.field_behavior) = OPTIONAL];

  // Workload specific backup properties.
  oneof backup_properties {
    // Output only. Compute Engine specific backup properties.
    ComputeInstanceBackupProperties compute_instance_backup_properties = 19
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Backup Appliance specific backup properties.
    BackupApplianceBackupProperties backup_appliance_backup_properties = 21
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Type of the backup, unspecified, scheduled or ondemand.
  BackupType backup_type = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Configuration Info has the resource format-specific configuration.
  oneof plan_info {
    // Output only. Configuration for a Google Cloud resource.
    GCPBackupPlanInfo gcp_backup_plan_info = 22
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. source resource size in bytes at the time of the backup.
  int64 resource_size_bytes = 23 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for creating a BackupVault.
message CreateBackupVaultRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "backupdr.googleapis.com/BackupVault"
    }
  ];

  // Required. ID of the requesting object
  // If auto-generating ID server-side, remove this field and
  // backup_vault_id from the method_signature of Create RPC
  string backup_vault_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  BackupVault backup_vault = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is 'false'.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for listing backupvault stores.
message ListBackupVaultsRequest {
  // Required. The project and location for which to retrieve backupvault stores
  // information, in the format 'projects/{project_id}/locations/{location}'. In
  // Cloud Backup and DR, locations map to Google Cloud regions, for example
  // **us-central1**.
  // To retrieve backupvault stores for all locations, use "-" for the
  // '{location}' value.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "backupdr.googleapis.com/BackupVault"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Reserved for future use to provide a BASIC & FULL view of Backup
  // Vault.
  BackupVaultView view = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for listing BackupVaults.
message ListBackupVaultsResponse {
  // The list of BackupVault instances in the project for the specified
  // location.
  //
  // If the '{location}' value in the request is "-", the response contains a
  // list of instances from all locations. In case any location is unreachable,
  // the response will only return backup vaults in reachable locations and
  // the 'unreachable' field will be populated with a list of unreachable
  // locations.
  repeated BackupVault backup_vaults = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for fetching usable BackupVaults.
message FetchUsableBackupVaultsRequest {
  // Required. The project and location for which to retrieve backupvault stores
  // information, in the format 'projects/{project_id}/locations/{location}'. In
  // Cloud Backup and DR, locations map to Google Cloud regions, for example
  // **us-central1**.
  // To retrieve backupvault stores for all locations, use "-" for the
  // '{location}' value.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "backupdr.googleapis.com/BackupVault"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for fetching usable BackupVaults.
message FetchUsableBackupVaultsResponse {
  // The list of BackupVault instances in the project for the specified
  // location.
  //
  // If the '{location}' value in the request is "-", the response contains a
  // list of instances from all locations. In case any location is unreachable,
  // the response will only return backup vaults in reachable locations and
  // the 'unreachable' field will be populated with a list of unreachable
  // locations.
  repeated BackupVault backup_vaults = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for getting a BackupVault.
message GetBackupVaultRequest {
  // Required. Name of the backupvault store resource name, in the format
  // 'projects/{project_id}/locations/{location}/backupVaults/{resource_name}'
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "backupdr.googleapis.com/BackupVault"
    }
  ];

  // Optional. Reserved for future use to provide a BASIC & FULL view of Backup
  // Vault
  BackupVaultView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for updating a BackupVault.
message UpdateBackupVaultRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // BackupVault resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then the request will fail.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  BackupVault backup_vault = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Only validate the request, but do not perform mutations.
  // The default is 'false'.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, will not check plan duration against backup vault
  // enforcement duration.
  bool force = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a BackupVault.
message DeleteBackupVaultRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "backupdr.googleapis.com/BackupVault"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. If set to true, any data source from this backup vault will also
  // be deleted.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];

  // The current etag of the backup vault.
  // If an etag is provided and does not match the current etag of the
  // connection, deletion will be blocked.
  string etag = 4;

  // Optional. Only validate the request, but do not perform mutations.
  // The default is 'false'.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true and the BackupVault is not found, the request will
  // succeed but no action will be taken.
  bool allow_missing = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, backupvault deletion will proceed even if there
  // are backup plans referencing the backupvault. The default is 'false'.
  bool ignore_backup_plan_references = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for listing DataSources.
message ListDataSourcesRequest {
  // Required. The project and location for which to retrieve data
  // sources information, in the format
  // 'projects/{project_id}/locations/{location}'. In Cloud Backup and DR,
  // locations map to Google Cloud regions, for example **us-central1**.
  // To retrieve data sources for all locations, use "-" for the
  // '{location}' value.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "backupdr.googleapis.com/DataSource"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for listing DataSources.
message ListDataSourcesResponse {
  // The list of DataSource instances in the project for the specified
  // location.
  //
  // If the '{location}' value in the request is "-", the response contains a
  // list of instances from all locations. In case any location is unreachable,
  // the response will only return data sources in reachable locations
  // and the 'unreachable' field will be populated with a list of unreachable
  // locations.
  repeated DataSource data_sources = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for getting a DataSource instance.
message GetDataSourceRequest {
  // Required. Name of the data source resource name, in the format
  // 'projects/{project_id}/locations/{location}/backupVaults/{resource_name}/dataSource/{resource_name}'
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "backupdr.googleapis.com/DataSource"
    }
  ];
}

// Request message for updating a data source instance.
message UpdateDataSourceRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // DataSource resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then the request will fail.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  DataSource data_source = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Enable upsert.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for listing Backups.
message ListBackupsRequest {
  // Required. The project and location for which to retrieve backup
  // information, in the format
  // 'projects/{project_id}/locations/{location}'. In Cloud Backup and DR,
  // locations map to Google Cloud regions, for example **us-central1**.
  // To retrieve data sources for all locations, use "-" for the
  // '{location}' value.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "backupdr.googleapis.com/Backup"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Reserved for future use to provide a BASIC & FULL view of Backup
  // resource.
  BackupView view = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for listing Backups.
message ListBackupsResponse {
  // The list of Backup instances in the project for the specified
  // location.
  //
  // If the '{location}' value in the request is "-", the response contains a
  // list of instances from all locations. In case any location is unreachable,
  // the response will only return data sources in reachable locations
  // and the 'unreachable' field will be populated with a list of unreachable
  // locations.
  repeated Backup backups = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for getting a Backup.
message GetBackupRequest {
  // Required. Name of the data source resource name, in the format
  // 'projects/{project_id}/locations/{location}/backupVaults/{backupVault}/dataSources/{datasource}/backups/{backup}'
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "backupdr.googleapis.com/Backup" }
  ];

  // Optional. Reserved for future use to provide a BASIC & FULL view of Backup
  // resource.
  BackupView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for updating a Backup.
message UpdateBackupRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Backup resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then the request will fail.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  Backup backup = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for deleting a Backup.
message DeleteBackupRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "backupdr.googleapis.com/Backup" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Request message for restoring from a Backup.
message RestoreBackupRequest {
  // Required. The resource name of the Backup instance, in the format
  // 'projects/*/locations/*/backupVaults/*/dataSources/*/backups/'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "backupdr.googleapis.com/Backup" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // The target environment for the restore operation.
  oneof target_environment {
    // Compute Engine target environment to be used during restore.
    ComputeInstanceTargetEnvironment compute_instance_target_environment = 3;
  }

  // The property overrides for the instance being restored.
  oneof instance_properties {
    // Compute Engine instance properties to be overridden during restore.
    ComputeInstanceRestoreProperties compute_instance_restore_properties = 4;
  }
}

// Response message for restoring from a Backup.
message RestoreBackupResponse {
  // Details of the target resource created/modified as part of restore.
  TargetResource target_resource = 1;
}

// Details of the target resource created/modified as part of restore.
message TargetResource {
  // Minimum details to identify the restored resource.
  oneof target_resource_info {
    // Details of the native Google Cloud resource created as part of restore.
    GcpResource gcp_resource = 1;
  }
}

// Minimum details to identify a Google Cloud resource
message GcpResource {
  // Name of the Google Cloud resource.
  string gcp_resourcename = 1;

  // Location of the resource: <region>/<zone>/"global"/"unspecified".
  string location = 2;

  // Type of the resource. Use the Unified Resource Type,
  // eg. compute.googleapis.com/Instance.
  string type = 3;
}

// Backup configuration state. Is the resource configured for backup?
enum BackupConfigState {
  // The possible states of backup configuration.
  // Status not set.
  BACKUP_CONFIG_STATE_UNSPECIFIED = 0;

  // The data source is actively protected (i.e. there is a
  // BackupPlanAssociation or Appliance SLA pointing to it)
  ACTIVE = 1;

  // The data source is no longer protected (but may have backups under it)
  PASSIVE = 2;
}

// BackupView contains enum options for Partial and Full view.
enum BackupView {
  // If the value is not set, the default 'FULL' view is used.
  BACKUP_VIEW_UNSPECIFIED = 0;

  // Includes basic data about the Backup, but not the full contents.
  BACKUP_VIEW_BASIC = 1;

  // Includes all data about the Backup.
  // This is the default value (for both ListBackups and GetBackup).
  BACKUP_VIEW_FULL = 2;
}

// BackupVaultView contains enum options for Partial and Full view.
enum BackupVaultView {
  // If the value is not set, the default 'FULL' view is used.
  BACKUP_VAULT_VIEW_UNSPECIFIED = 0;

  // Includes basic data about the Backup Vault, but not the full contents.
  BACKUP_VAULT_VIEW_BASIC = 1;

  // Includes all data about the Backup Vault.
  // This is the default value (for both ListBackupVaults and GetBackupVault).
  BACKUP_VAULT_VIEW_FULL = 2;
}
