// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.backupdr.logging.v1;

import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/backupdr/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;

// Log entry for Backup and Restore Job for resources using BackupPlan based
// protection.
message BDRBackupRestoreJobLog {
  // The job_id field displays the identifier of the job being logged.
  optional string job_id = 1;

  // The category field displays the category of the job.
  optional string job_category = 2;

  // The status field displays the status of the job.
  optional string job_status = 3;

  // Full resource name of the protected resource.
  optional string source_resource_name = 4;

  // The source resource ID.
  optional string source_resource_id = 5;

  // Full resource name of the restore resource. Only populated in
  // restore jobs.
  optional string restore_resource_name = 6;

  // Full resource name of the backup created in backup jobs and used in restore
  // jobs.
  optional string backup_name = 7;

  // The resource_type field displays the type of the protected resource.
  optional string resource_type = 8;

  // Start time of the job.
  optional google.protobuf.Timestamp start_time = 9;

  // End time of the job.
  optional google.protobuf.Timestamp end_time = 10;

  // Full resource name for Backup Plan of the job. Only populated for Scheduled
  // Backup and Adhoc Backup.
  optional string backup_plan_name = 11;

  // Name of the backup rule. Only populated for Scheduled Backup and Adhoc
  // Backup.
  optional string backup_rule = 12;

  // Full resource name backup vault name
  optional string backup_vault_name = 13;

  // The amount of incremental backup data copied.
  optional double incremental_backup_size_gib = 14;

  // The error code. Only populated in error scenarios.
  optional int32 error_code = 15;

  // The name of the error type eg. PERMISSION_DENIED. Only populated in error
  // scenarios.
  optional string error_type = 16;

  // The user readable error message. Only populated in error scenarios.
  optional string error_message = 17;

  // Backup consistency time.
  optional google.protobuf.Timestamp backup_consistency_time = 18;

  // Source resource location.
  optional string source_resource_location = 19;

  // Restore resource location.
  optional string restore_resource_location = 20;
}

// Log entry for BDRProtectedResource Log for resources using BackupPlan based
// protection.
message BDRProtectedResourceLog {
  // Full canonical source resource name.
  optional string source_resource_name = 1;

  // The source resource ID. Instance Id for GCE.
  optional string source_resource_id = 2;

  // Type of the resource. For eg. “Compute Engine”.
  optional string resource_type = 3;

  // The size of the protected source resource in GiB, rounded to 2 decimal
  // places.
  optional double source_resource_data_size_gib = 4;

  // Complete canonical name of the Backup plan currently associated with the
  // source resource.
  optional string current_backup_plan_name = 5;

  // Backup Rule details of the backup plan currently associated with the source
  // resource.
  repeated BackupRuleDetail current_backup_rule_details = 6;

  // Timestamp when the resource was last protected.
  optional google.protobuf.Timestamp last_protected_on = 7;

  // Full Resource Name of the backup vault currently associated with the source
  // resource.
  optional string current_backup_vault_name = 8;

  // Source resource_location
  optional string source_resource_location = 9;
}

message BackupRuleDetail {
  // Backup Rule Name.
  optional string rule_name = 1;

  // Backup Retention in Days.
  optional int32 retention_days = 2;

  // Recurrence Type. For Eg. “Weekly”,” Monthly” or “Daily”.
  optional string recurrence = 3;

  // Recurrence Repeat Schedule. For Eg. “1st and 25th day of the month.”
  optional string recurrence_schedule = 4;

  // Backup Window For Eg. “00:00 to 06:00”
  optional string backup_window = 5;

  // Backup Window Timezone in IANA format. For Eg. “America/Los_Angeles”
  optional string backup_window_timezone = 6;
}

// Log entry for BDRBackupVaultDetails Log for resources using BackupPlan based
// protection.
message BDRBackupVaultDetailsLog {
  // Relative resource name of Backup Vault
  optional string backup_vault_name = 1;

  // Relative source resource name
  optional string source_resource_name = 2;

  // Type of the resource For eg. “Compute Engine”
  optional string resource_type = 3;

  // The name of the Backup Plan applied on the resource to protect.
  optional string current_backup_plan_name = 4;

  // Timestamp when the first (oldest) backup was taken.
  optional google.protobuf.Timestamp first_available_restore_point = 5;

  // Timestamp when the latest backup was taken.
  optional google.protobuf.Timestamp last_available_restore_point = 6;

  // Amount of storage space consumed by backups in GiB.
  optional double stored_bytes_gib = 7;

  // The minimum timeframe in days that backups in that vault are being
  // protected from deletion.
  optional int32 minimum_enforced_retention_days = 8;

  // Source resource_location
  optional string source_resource_location = 9;
}
