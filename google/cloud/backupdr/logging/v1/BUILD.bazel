# This file was automatically generated by BuildFileGenerator

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "logging_proto",
    srcs = [
        "bdr_log.proto",
        "eventlog.proto",
        "reportlog.proto",
    ],
    deps = [
        "@com_google_protobuf//:timestamp_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "logging_java_proto",
    deps = [":logging_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-backupdr-logging-v1-java",
    deps = [
        ":logging_java_proto",
        ":logging_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_proto_library",
)

go_proto_library(
    name = "logging_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/backupdr/logging/apiv1/loggingpb",
    protos = [":logging_proto"],
    deps = [
    ],
)

go_gapic_assembly_pkg(
    name = "google-cloud-backupdr-logging-v1-go",
    deps = [
        ":logging_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "logging_moved_proto",
    srcs = [":logging_proto"],
    deps = [
        "@com_google_protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "logging_py_proto",
    deps = [":logging_moved_proto"],
)

py_grpc_library(
    name = "logging_py_grpc",
    srcs = [":logging_moved_proto"],
    deps = [":logging_py_proto"],
)

py_gapic_library(
    name = "logging_py_gapic",
    srcs = [":logging_proto"],
    rest_numeric_enums = False,
    transport = "grpc+rest",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-backupdr-logging-v1-py",
    deps = [
        ":logging_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "logging_php_proto",
    deps = [":logging_proto"],
)

php_gapic_assembly_pkg(
    name = "google-cloud-backupdr-logging-v1-php",
    deps = [
        ":logging_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "logging_ruby_proto",
    deps = [":logging_proto"],
)

ruby_grpc_library(
    name = "logging_ruby_grpc",
    srcs = [":logging_proto"],
    deps = [":logging_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "logging_csharp_proto",
    deps = [":logging_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-backupdr-logging-v1-csharp",
    package_name = "Google.Cloud.BackupDR.Logging.V1",
    generate_nongapic_package = True,
    deps = [
        ":logging_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "logging_cc_proto",
    deps = [":logging_proto"],
)

cc_grpc_library(
    name = "logging_cc_grpc",
    srcs = [":logging_proto"],
    grpc_only = True,
    deps = [":logging_cc_proto"],
)
