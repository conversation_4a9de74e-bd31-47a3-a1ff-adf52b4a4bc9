// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.backupdr.logging.v1;

option go_package = "cloud.google.com/go/backupdr/logging/apiv1/loggingpb;loggingpb";

// This is an event
message Event {
  // The event_time field displays the time when the event was reported
  string event_time = 1;

  // The srcid field displays unique id of the event occurred in the backup
  // appliance
  int64 srcid = 2;

  // The errormessage field describes the detailed error associated with the
  // event
  string error_message = 3;

  // The event_id field displays unique id associated with the error
  int32 event_id = 4;

  // The component field displays the source of the event
  string component = 5;

  // The appliance_id field displays unique id of the appliance on which event
  // occurred
  int64 appliance_name = 6;

  // The appname field displays name of the application associated with the
  // event
  string app_name = 7;

  // The apptype field displays type of the application associated with the
  // event
  string app_type = 8;

  // The jobname field displays name of the job associated with the event
  string job_name = 9;
}
