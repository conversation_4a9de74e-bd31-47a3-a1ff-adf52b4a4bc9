// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.backupdr.logging.v1;

option go_package = "cloud.google.com/go/backupdr/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "ReportLogProto";
option java_package = "com.google.cloud.backupdr.logging.v1";
option csharp_namespace = "Google.Cloud.BackupDR.Logging.V1";
option php_namespace = "Google\\Cloud\\BackupDR\\Logging\\V1";
option ruby_package = "Google::Cloud::BackupDR::Logging::V1";

// This is a BackupRecoveryJobReportLog published as part of GCBDR Reporting.
message BackupRecoveryJobReportLog {
  // The job_name field displays the name of the job being reported.
  string job_name = 1;

  // The job_name field displays the category of the job whether it is Backup or
  // Recovery Job.
  string job_category = 2;

  // The job_type field displays the type of the job.
  string job_type = 3;

  // The log_backup field displays whether the backup taken is only for logs, DB
  // or both.
  string log_backup = 4;

  // The job_status field displays the status of the job.
  string job_status = 5;

  // The resource_name field displays the name of the resource.
  string resource_name = 6;

  // The resource_type field displays the type of the resource.
  string resource_type = 7;

  // The error_code field displays the error code.
  int32 error_code = 8;

  // The error_message field displays the error message if the job is not
  // successful.
  string error_message = 9;

  // The job_initiation_failure_reason field displays the reason for failure,
  // if the job was not run.
  string job_initiation_failure_reason = 10;

  // The job_start_time field displays the timestamp when the job started.
  string job_start_time = 11;

  // The job_end_time field displays the timestamp when the job ended.
  string job_end_time = 12;

  // The job_queued_time field displays the timestamp when the job was
  // queued for running.
  string job_queued_time = 13;

  // The job_duration_in_hours field displays the duration in hours which the
  // job took to complete.
  double job_duration_in_hours = 14;

  // The hostname field displays the name of the host.
  string hostname = 15;

  // The appliance_name field displays the name of the backup appliance.
  string appliance_name = 16;

  // The backup_rule_policy_name field displays the policy name which is
  // associated with this job.
  string backup_rule_policy_name = 17;

  // The backup_plan_policy_template field displays the name of the backup plan
  // for this application.
  string backup_plan_policy_template = 18;

  // The backup_type field displays the type of backup taken: Log, Incremental
  // or Full Copy.
  string backup_type = 19;

  // The recovery_point field displays the timestamp of recovery point.
  string recovery_point = 20;

  // The backup_consistency field displays whether the backup is crash
  // consistent or application consistent.
  string backup_consistency = 21;

  // The target_host_name field displays the target host name.
  string target_host_name = 22;

  // The target_appliance_name field displays the target appliance name.
  string target_appliance_name = 23;

  // The target_pool_name field displays target pool name.
  string target_pool_name = 24;

  // The resource_data_size_in_gib field displays resource data size in Gib.
  double resource_data_size_in_gib = 25;

  // The data_copied_in_gib field displays the amount of the data copied
  // during backup in Gib.
  double data_copied_in_gib = 26;

  // The onvault_pool_storage_consumed_in_gib field displays the amount of
  // onvault pool storage consumed in Gib.
  double onvault_pool_storage_consumed_in_gib = 27;

  // The pre_compress_in_gib field displays the size before compression in Gib.
  double pre_compress_in_gib = 28;

  // The compression_ratio field displays the ratio of post compression size to
  // pre compression size.
  double compression_ratio = 29;

  // The data_change_rate field displays the percentage of data copied during
  // backup to application size.
  double data_change_rate = 30;

  // The snapshot_disk_size_in_gib field displays the snapshot disk size in Gib.
  double snapshot_disk_size_in_gib = 31;

  // The data_written_in_gib field displays the amount of
  // remote data written in Gib.
  double data_written_in_gib = 32;

  // The data_sent_in_gib field displays the amount of
  // network data sent in Gib.
  double data_sent_in_gib = 33;

  // The job_id field displays the id of the job being reported.
  string job_id = 34;

  // The host_id field displays the host id.
  string host_id = 35;

  // The backup_rule_policy_id field displays the policy id.
  string backup_rule_policy_id = 36;

  // The resource_id field displays the resource id.
  string resource_id = 37;

  // The target_pool_id field displays the target pool id.
  string target_pool_id = 38;

  // The target_host_id field displays the target host id.
  string target_host_id = 39;

  // The target_appliance_id field displays the target appliance id.
  string target_appliance_id = 40;
}

// This is a UnprotectedResourceLogReport published as part of GCBDR
// Reporting.
message UnprotectedResourceReportLog {
  // Required. Name of the host where the application/resource resides.
  string host_name = 1;

  // Required. Name of the application/resource.
  string resource_name = 2;

  // Required. Type of the application/resource.
  string resource_type = 3;

  // Optional. Name of the database instance for instance members.
  string instance_name = 4;

  // Required. Date when the Appliance was Discovered first.
  string discovered_on = 5;

  // Required. Name of the appliance on which it was discovered.
  string discovered_by = 6;

  // Required. Id of the Appliance
  string appliance_id = 7;

  // Required. Id of the application/resource
  string resource_id = 8;

  // Required. Id of the Host where the application/resource resides.
  string host_id = 9;
}

// This is a DailyScheduleComplianceReportLog published as part of GCBDR
// Reporting.
message DailyScheduleComplianceReportLog {
  // Required. Resource/App Name.
  string resource_name = 1;

  // Required. Resource/App Type.
  string resource_type = 2;

  // Required. Policy Name.
  string backup_rule_policy_name = 3;

  // Required. Policy Template Name.
  string backup_plan_policy_template = 4;

  // Required. Name of the host where the app/resource resides.
  string host_name = 5;

  // Required. Appliance Name.
  string appliance_name = 6;

  // Required. Date for which compliance is being reported.
  string date = 7;

  // Required. Backup Window Start time.
  string backup_window_start_time = 8;

  // Required. JobType of the policy.
  string job_type = 9;

  // Required. Compliance status for the policy.
  string status = 10;

  // Required. Description for the status reason.
  string comment = 11;

  // Required. Resource/App Id.
  string resource_id = 12;

  // Required. Host Id.
  string host_id = 13;

  // Required. Policy Template Id
  string backup_plan_policy_template_id = 14;

  // Required. Policy Id.
  string backup_rule_policy_id = 15;

  // Required. Appliance Id.
  string appliance_id = 16;
}

// This is a BackupStorageUtilizationReportLog published as part of GCBDR
// Reporting.
message BackupStorageUtilizationReportLog {
  // Optional. The appliance name.
  string appliance_name = 1;

  // Required. The storage resource type.
  string storage_type = 2;

  // Required. The storage pool name.
  string pool_name = 3;

  // Required. Total capacity of the pool in GiB.
  double total_capacity_in_gib = 4;

  // Required. Used capacity of the pool in GiB.
  double used_capacity_in_gib = 5;

  // Required. Utilization percentage of a storage pool.
  double utilization_percentage = 6;

  // Required. Appliance id.
  string appliance_id = 7;
}

// Holds information for the Protected Resource
message ProtectedResource {
  // Required. Resource name.
  string resource_name = 1;
  // Required. Resource Type.
  string resource_type = 2;
  // Required. Resource ID.
  string resource_id = 3;
  // Optional. Backup Inclusion/Exclusion.
  string backup_inclusion_or_exclusion = 4;
  // Required. Host Id.
  string host_id = 5;
  // Required. Host Name.
  string host_name = 6;
  // Required. Backup Template ID.
  string backup_plan_policy_template_id = 7;
  // Required. Backup Template.
  string backup_plan_policy_template = 8;
  // Required. Sla Id.
  string sla_id = 9;
  // Required. Backup Plan restrictions.
  string backup_plan_restrictions = 10;
  // Required. Protected On.
  string protected_on = 11;
  // Optional. Policy Overrides.
  string policy_overrides = 12;
  // Optional. Source Appliance in case of streamsnap.
  string source_appliance = 13;
  // Optional. Source Appliance Id in case of streamsnap.
  string source_appliance_id = 14;
  // Required. Protected Data (GiB).
  double protected_data_in_gib = 15;
  // Optional. Onvault (GiB) .
  double onvault_in_gib = 16;
  // Optional. Originating Appliance in case of streamsnap.
  string appliance_name = 17;
  // Optional. Originating Appliance id in case of streamsnap.
  string appliance_id = 18;
  // Optional. Remote Appliance in case of streamsnap.
  string remote_appliance = 19;
  // Optional. Remote Appliance id in case of streamsnap.
  string remote_appliance_id = 20;
  // Optional. Recovery Point.
  string recovery_point = 21;
}

// Holds information for the Mounted Images log
message MountedImage {
  // Required. Source Resource name.
  string source_resource_name = 1;
  // Required. Source Resource id.
  string source_resource_id = 2;
  // Required. Cluster Name.
  string appliance_name = 3;
  // Required. Cluster Id.
  string appliance_id = 4;
  // Required. Mounted Image Name.
  string mounted_image_name = 5;
  // Required. Source Image Name.
  string source_image_name = 6;
  // Required.Source Image Type.
  string source_image_type = 7;
  // Required. Recovery point Date.
  string recovery_point_date = 8;
  // Required. Last Mount Date
  string last_mount_date = 9;
  // Required. Resource type.
  string resource_type = 10;
  // Required. Source Host Name.
  string source_host_name = 11;
  // Required. Source Host Id.
  string source_host_id = 12;
  // Required. Mounted Host Name.
  string mounted_host_name = 13;
  // Required. Mounted Host Id.
  string mounted_host_id = 14;
  // Required. Mounted Resource Name.
  string mounted_resource_name = 15;
  // Required. Resource Virtual size(in GiB)
  double resource_virtual_size_in_gib = 16;
  // Required. Storage Consumed(in GiB).
  double storage_consumed_in_gib = 17;
  // Optional. label.
  string mounted_resource_label = 18;
  // Optional. Restorable Objects.
  string restorable_object = 19;
  // Required. Mounted Image Age(in days).
  int32 mounted_image_age_in_days = 20;
  // Required. User Name.
  string user_name = 21;
  // Optional. Read Mode.
  string read_mode = 22;
  // Required. Resource size(in GiB)
  double resource_size_in_gib = 23;
  // Optional. Image Expiration Date
  string image_expiration_date = 24;
}

// Holds information for the Connector Versions log
message ConnectorVersionReportLog {
  // Required. Appliance Name.
  string appliance_name = 1;

  // Required. Appliance Id.
  string appliance_id = 2;

  // Required. Host Name.
  string host_name = 3;

  // Required. Host Id.
  string host_id = 4;

  // Optional. Os Type.
  string host_os_type = 5;

  // Required. Host Ip Address.
  string host_ip_address = 6;

  // Optional. Db Authentication.
  string db_authentication = 7;

  // Required. Installed version.
  string installed_version = 8;

  // Optional. Available version.
  string available_version = 9;

  // Required. Version Check.
  string version_check = 10;

  // Required. Disk Preference.
  string disk_preference = 11;

  // Required. Transport
  string transport = 12;
}
