# This build file includes a target for the Ruby wrapper library for
# google-cloud-backupdr.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for google-cloud-backupdr.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "backupdr_ruby_wrapper",
    srcs = ["//google/cloud/backupdr/v1:backupdr_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-backupdr",
        "ruby-cloud-wrapper-of=v1:0.0",
        "ruby-cloud-namespace-override=Backupdr=BackupDR",
        "ruby-cloud-path-override=backup_dr=backupdr"
    ],
    service_yaml = "//google/cloud/backupdr/v1:backupdr_v1.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-backupdr-ruby",
    deps = [
        ":backupdr_ruby_wrapper",
    ],
)
