// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.memcache.v1beta2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "google/type/timeofday.proto";

option go_package = "cloud.google.com/go/memcache/apiv1beta2/memcachepb;memcachepb";
option java_multiple_files = true;
option java_outer_classname = "CloudMemcacheProto";
option java_package = "com.google.cloud.memcache.v1beta2";

// Configures and manages Cloud Memorystore for Memcached instances.
//
//
// The `memcache.googleapis.com` service implements the Google Cloud Memorystore
// for Memcached API and defines the following resource model for managing
// Memorystore Memcached (also called Memcached below) instances:
// * The service works with a collection of cloud projects, named: `/projects/*`
// * Each project has a collection of available locations, named: `/locations/*`
// * Each location has a collection of Memcached instances, named:
// `/instances/*`
// * As such, Memcached instances are resources of the form:
//   `/projects/{project_id}/locations/{location_id}/instances/{instance_id}`
//
// Note that location_id must be a GCP `region`; for example:
// * `projects/my-memcached-project/locations/us-central1/instances/my-memcached`
service CloudMemcache {
  option (google.api.default_host) = "memcache.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists Instances in a given location.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1beta2/{parent=projects/*/locations/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1beta2/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Instance in a given location.
  rpc CreateInstance(CreateInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta2/{parent=projects/*/locations/*}/instances"
      body: "resource"
    };
    option (google.api.method_signature) = "parent,instance_id,resource";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.memcache.v1beta2.Instance"
      metadata_type: "google.cloud.memcache.v1beta2.OperationMetadata"
    };
  }

  // Updates an existing Instance in a given project and location.
  rpc UpdateInstance(UpdateInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta2/{resource.name=projects/*/locations/*/instances/*}"
      body: "resource"
    };
    option (google.api.method_signature) = "update_mask,resource";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.memcache.v1beta2.Instance"
      metadata_type: "google.cloud.memcache.v1beta2.OperationMetadata"
    };
  }

  // Updates the defined Memcached parameters for an existing instance.
  // This method only stages the parameters, it must be followed by
  // `ApplyParameters` to apply the parameters to nodes of the Memcached
  // instance.
  rpc UpdateParameters(UpdateParametersRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta2/{name=projects/*/locations/*/instances/*}:updateParameters"
      body: "*"
    };
    option (google.api.method_signature) = "name,update_mask,parameters";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.memcache.v1beta2.Instance"
      metadata_type: "google.cloud.memcache.v1beta2.OperationMetadata"
    };
  }

  // Deletes a single Instance.
  rpc DeleteInstance(DeleteInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta2/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.memcache.v1beta2.OperationMetadata"
    };
  }

  // `ApplyParameters` restarts the set of specified nodes in order to update
  // them to the current set of parameters for the Memcached Instance.
  rpc ApplyParameters(ApplyParametersRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta2/{name=projects/*/locations/*/instances/*}:applyParameters"
      body: "*"
    };
    option (google.api.method_signature) = "name,node_ids,apply_all";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.memcache.v1beta2.Instance"
      metadata_type: "google.cloud.memcache.v1beta2.OperationMetadata"
    };
  }

  // Updates software on the selected nodes of the Instance.
  rpc ApplySoftwareUpdate(ApplySoftwareUpdateRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta2/{instance=projects/*/locations/*/instances/*}:applySoftwareUpdate"
      body: "*"
    };
    option (google.api.method_signature) = "instance,node_ids,apply_all";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.memcache.v1beta2.Instance"
      metadata_type: "google.cloud.memcache.v1beta2.OperationMetadata"
    };
  }

  // Performs the apply phase of the RescheduleMaintenance verb.
  rpc RescheduleMaintenance(RescheduleMaintenanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta2/{instance=projects/*/locations/*/instances/*}:rescheduleMaintenance"
      body: "*"
    };
    option (google.api.method_signature) = "instance, reschedule_type, schedule_time";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.memcache.v1beta2.Instance"
      metadata_type: "google.cloud.memcache.v1beta2.OperationMetadata"
    };
  }
}

// A Memorystore for Memcached instance
message Instance {
  option (google.api.resource) = {
    type: "memcache.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/instances/{instance}"
  };

  // Different states of a Memcached instance.
  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // Memcached instance is being created.
    CREATING = 1;

    // Memcached instance has been created and ready to be used.
    READY = 2;

    // Memcached instance is updating configuration such as maintenance policy
    // and schedule.
    UPDATING = 3;

    // Memcached instance is being deleted.
    DELETING = 4;

    // Memcached instance is going through maintenance, e.g. data plane rollout.
    PERFORMING_MAINTENANCE = 5;
  }

  // Configuration for a Memcached Node.
  message NodeConfig {
    // Required. Number of cpus per Memcached node.
    int32 cpu_count = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Memory size in MiB for each Memcached node.
    int32 memory_size_mb = 2 [(google.api.field_behavior) = REQUIRED];
  }

  message Node {
    // Different states of a Memcached node.
    enum State {
      // Node state is not set.
      STATE_UNSPECIFIED = 0;

      // Node is being created.
      CREATING = 1;

      // Node has been created and ready to be used.
      READY = 2;

      // Node is being deleted.
      DELETING = 3;

      // Node is being updated.
      UPDATING = 4;
    }

    // Output only. Identifier of the Memcached node. The node id does not
    // include project or location like the Memcached instance name.
    string node_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Location (GCP Zone) for the Memcached node.
    string zone = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Current state of the Memcached node.
    State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Hostname or IP address of the Memcached node used by the
    // clients to connect to the Memcached server on this node.
    string host = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The port number of the Memcached server on this node.
    int32 port = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // User defined parameters currently applied to the node.
    MemcacheParameters parameters = 6;

    // Output only. Returns true if there is an update waiting to be applied
    bool update_available = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  message InstanceMessage {
    enum Code {
      // Message Code not set.
      CODE_UNSPECIFIED = 0;

      // Memcached nodes are distributed unevenly.
      ZONE_DISTRIBUTION_UNBALANCED = 1;
    }

    // A code that correspond to one type of user-facing message.
    Code code = 1;

    // Message on memcached instance which will be exposed to users.
    string message = 2;
  }

  // Required. Unique name of the resource in this scope including project and
  // location using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  //
  // Note: Memcached instances are managed and addressed at the regional level
  // so `location_id` here refers to a Google Cloud region; however, users may
  // choose which zones Memcached nodes should be provisioned in within an
  // instance. Refer to [zones][google.cloud.memcache.v1beta2.Instance.zones] field for more details.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // User provided name for the instance, which is only used for display
  // purposes. Cannot be more than 80 characters.
  string display_name = 2;

  // Resource labels to represent user-provided metadata.
  // Refer to cloud documentation on labels for more details.
  // https://cloud.google.com/compute/docs/labeling-resources
  map<string, string> labels = 3;

  // The full name of the Google Compute Engine
  // [network](https://cloud.google.com/vpc/docs/vpc) to which the
  // instance is connected. If left unspecified, the `default` network
  // will be used.
  string authorized_network = 4;

  // Zones in which Memcached nodes should be provisioned.
  // Memcached nodes will be equally distributed across these zones. If not
  // provided, the service will by default create nodes in all zones in the
  // region for the instance.
  repeated string zones = 5;

  // Required. Number of nodes in the Memcached instance.
  int32 node_count = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. Configuration for Memcached nodes.
  NodeConfig node_config = 7 [(google.api.field_behavior) = REQUIRED];

  // The major version of Memcached software.
  // If not provided, latest supported version will be used. Currently the
  // latest supported major version is `MEMCACHE_1_5`.
  // The minor version will be automatically determined by our system based on
  // the latest supported minor version.
  MemcacheVersion memcache_version = 9;

  // User defined parameters to apply to the memcached process
  // on each node.
  MemcacheParameters parameters = 11;

  // Output only. List of Memcached nodes.
  // Refer to [Node][google.cloud.memcache.v1beta2.Instance.Node] message for more details.
  repeated Node memcache_nodes = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the instance was created.
  google.protobuf.Timestamp create_time = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the instance was updated.
  google.protobuf.Timestamp update_time = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of this Memcached instance.
  State state = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The full version of memcached server running on this instance.
  // System automatically determines the full memcached version for an instance
  // based on the input MemcacheVersion.
  // The full version format will be "memcached-1.5.16".
  string memcache_full_version = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // List of messages that describe the current state of the Memcached instance.
  repeated InstanceMessage instance_messages = 19;

  // Output only. Endpoint for the Discovery API.
  string discovery_endpoint = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Returns true if there is an update waiting to be applied
  bool update_available = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The maintenance policy for the instance. If not provided,
  // the maintenance event will be performed based on Memorystore
  // internal rollout schedule.
  MaintenancePolicy maintenance_policy = 22;

  // Output only. Published maintenance schedule.
  MaintenanceSchedule maintenance_schedule = 23 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Maintenance policy per instance.
message MaintenancePolicy {
  // Output only. The time when the policy was created.
  google.protobuf.Timestamp create_time = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the policy was updated.
  google.protobuf.Timestamp update_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Description of what this policy is for. Create/Update methods
  // return INVALID_ARGUMENT if the length is greater than 512.
  string description = 3;

  // Required. Maintenance window that is applied to resources covered by this
  // policy. Minimum 1. For the current version, the maximum number of
  // weekly_maintenance_windows is expected to be one.
  repeated WeeklyMaintenanceWindow weekly_maintenance_window = 4 [(google.api.field_behavior) = REQUIRED];
}

// Time window specified for weekly operations.
message WeeklyMaintenanceWindow {
  // Required. Allows to define schedule that runs specified day of the week.
  google.type.DayOfWeek day = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Start time of the window in UTC.
  google.type.TimeOfDay start_time = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Duration of the time window.
  google.protobuf.Duration duration = 3 [(google.api.field_behavior) = REQUIRED];
}

// Upcoming maintenance schedule.
message MaintenanceSchedule {
  // Output only. The start time of any upcoming scheduled maintenance for this instance.
  google.protobuf.Timestamp start_time = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The end time of any upcoming scheduled maintenance for this instance.
  google.protobuf.Timestamp end_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The deadline that the maintenance schedule start time can not go beyond,
  // including reschedule.
  google.protobuf.Timestamp schedule_deadline_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request for [ListInstances][google.cloud.memcache.v1beta2.CloudMemcache.ListInstances].
message ListInstancesRequest {
  // Required. The resource name of the instance location using the form:
  //     `projects/{project_id}/locations/{location_id}`
  // where `location_id` refers to a GCP region
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return.
  //
  // If not specified, a default value of 1000 will be used by the service.
  // Regardless of the `page_size` value, the response may include a partial
  // list and a caller should only rely on response's
  // [`next_page_token`][google.cloud.memcache.v1beta2.ListInstancesResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2;

  // The `next_page_token` value returned from a previous List request, if any.
  string page_token = 3;

  // List filter. For example, exclude all Memcached instances with name as
  // my-instance by specifying `"name != my-instance"`.
  string filter = 4;

  // Sort results. Supported values are "name", "name desc" or "" (unsorted).
  string order_by = 5;
}

// Response for [ListInstances][google.cloud.memcache.v1beta2.CloudMemcache.ListInstances].
message ListInstancesResponse {
  // A list of Memcached instances in the project in the specified location,
  // or across all locations.
  //
  // If the `location_id` in the parent field of the request is "-", all regions
  // available to the project are queried, and the results aggregated.
  repeated Instance resources = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for [GetInstance][google.cloud.memcache.v1beta2.CloudMemcache.GetInstance].
message GetInstanceRequest {
  // Required. Memcached instance resource name in the format:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "memcache.googleapis.com/Instance"
    }
  ];
}

// Request for [CreateInstance][google.cloud.memcache.v1beta2.CloudMemcache.CreateInstance].
message CreateInstanceRequest {
  // Required. The resource name of the instance location using the form:
  //     `projects/{project_id}/locations/{location_id}`
  // where `location_id` refers to a GCP region
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The logical name of the Memcached instance in the user
  // project with the following restrictions:
  //
  // * Must contain only lowercase letters, numbers, and hyphens.
  // * Must start with a letter.
  // * Must be between 1-40 characters.
  // * Must end with a number or a letter.
  // * Must be unique within the user project / location.
  //
  // If any of the above are not met, the API raises an invalid argument error.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A Memcached [Instance] resource
  Instance resource = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for [UpdateInstance][google.cloud.memcache.v1beta2.CloudMemcache.UpdateInstance].
message UpdateInstanceRequest {
  // Required. Mask of fields to update.
  //
  //  *  `displayName`
  google.protobuf.FieldMask update_mask = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A Memcached [Instance] resource.
  // Only fields specified in update_mask are updated.
  Instance resource = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for [DeleteInstance][google.cloud.memcache.v1beta2.CloudMemcache.DeleteInstance].
message DeleteInstanceRequest {
  // Required. Memcached instance resource name in the format:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "memcache.googleapis.com/Instance"
    }
  ];
}

// Request for [RescheduleMaintenance][google.cloud.memcache.v1beta2.CloudMemcache.RescheduleMaintenance].
message RescheduleMaintenanceRequest {
  // Reschedule options.
  enum RescheduleType {
    // Not set.
    RESCHEDULE_TYPE_UNSPECIFIED = 0;

    // If the user wants to schedule the maintenance to happen now.
    IMMEDIATE = 1;

    // If the user wants to use the existing maintenance policy to find the
    // next available window.
    NEXT_AVAILABLE_WINDOW = 2;

    // If the user wants to reschedule the maintenance to a specific time.
    SPECIFIC_TIME = 3;
  }

  // Required. Memcache instance resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region.
  string instance = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "memcache.googleapis.com/Instance"
    }
  ];

  // Required. If reschedule type is SPECIFIC_TIME, must set up schedule_time as well.
  RescheduleType reschedule_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Timestamp when the maintenance shall be rescheduled to if
  // reschedule_type=SPECIFIC_TIME, in RFC 3339 format, for
  // example `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp schedule_time = 3;
}

// Request for [ApplyParameters][google.cloud.memcache.v1beta2.CloudMemcache.ApplyParameters].
message ApplyParametersRequest {
  // Required. Resource name of the Memcached instance for which parameter group updates
  // should be applied.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "memcache.googleapis.com/Instance"
    }
  ];

  // Nodes to which the instance-level parameter group is applied.
  repeated string node_ids = 2;

  // Whether to apply instance-level parameter group to all nodes. If set to
  // true, users are restricted from specifying individual nodes, and
  // `ApplyParameters` updates all nodes within the instance.
  bool apply_all = 3;
}

// Request for [UpdateParameters][google.cloud.memcache.v1beta2.CloudMemcache.UpdateParameters].
message UpdateParametersRequest {
  // Required. Resource name of the Memcached instance for which the parameters should be
  // updated.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "memcache.googleapis.com/Instance"
    }
  ];

  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];

  // The parameters to apply to the instance.
  MemcacheParameters parameters = 3;
}

// Request for [ApplySoftwareUpdate][google.cloud.memcache.v1beta2.CloudMemcache.ApplySoftwareUpdate].
message ApplySoftwareUpdateRequest {
  // Required. Resource name of the Memcached instance for which software update should be
  // applied.
  string instance = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "memcache.googleapis.com/Instance"
    }
  ];

  // Nodes to which we should apply the update to. Note all the selected nodes
  // are updated in parallel.
  repeated string node_ids = 2;

  // Whether to apply the update to all nodes. If set to
  // true, will explicitly restrict users from specifying any nodes, and apply
  // software update to all nodes (where applicable) within the instance.
  bool apply_all = 3;
}

// Memcached versions supported by our service.
enum MemcacheVersion {
  MEMCACHE_VERSION_UNSPECIFIED = 0;

  // Memcached 1.5 version.
  MEMCACHE_1_5 = 1;
}

message MemcacheParameters {
  // Output only. The unique ID associated with this set of parameters. Users
  // can use this id to determine if the parameters associated with the instance
  // differ from the parameters associated with the nodes. A discrepancy between
  // parameter ids can inform users that they may need to take action to apply
  // parameters on nodes.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User defined set of parameters to use in the memcached process.
  map<string, string> params = 3;
}

// Represents the metadata of a long-running operation.
message OperationMetadata {
  // Output only. Time when the operation was created.
  google.protobuf.Timestamp create_time = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the operation finished running.
  google.protobuf.Timestamp end_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_detail = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool cancel_requested = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata for the given [google.cloud.location.Location][google.cloud.location.Location].
message LocationMetadata {
  // Output only. The set of available zones in the location. The map is keyed
  // by the lowercase ID of each zone, as defined by GCE. These keys can be
  // specified in the `zones` field when creating a Memcached instance.
  map<string, ZoneMetadata> available_zones = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message ZoneMetadata {

}
