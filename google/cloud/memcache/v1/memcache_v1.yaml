type: google.api.Service
config_version: 3
name: memcache.googleapis.com
title: Cloud Memorystore for Memcached API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.memcache.v1.CloudMemcache
- name: google.longrunning.Operations

types:
- name: google.cloud.memcache.v1.LocationMetadata
- name: google.cloud.memcache.v1.OperationMetadata

documentation:
  summary: |-
    Google Cloud Memorystore for Memcached API is used for creating and
    managing Memcached instances in GCP.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: google.cloud.memcache.v1.CloudMemcache.GetInstance
    deadline: 10.0
  - selector: google.cloud.memcache.v1.CloudMemcache.ListInstances
    deadline: 10.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0
  - selector: google.longrunning.Operations.GetOperation
    deadline: 5.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.memcache.v1.CloudMemcache.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
