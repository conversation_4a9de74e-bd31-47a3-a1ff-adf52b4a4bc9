# This build file includes a target for the Ruby wrapper library for
# google-cloud-memcache.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for memcache.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta2 in this case.
ruby_cloud_gapic_library(
    name = "memcache_ruby_wrapper",
    srcs = ["//google/cloud/memcache/v1beta2:memcache_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-memcache",
        "ruby-cloud-env-prefix=MEMCACHE",
        "ruby-cloud-wrapper-of=v1:0.8;v1beta2:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/memorystore/docs/memcached/",
        "ruby-cloud-api-id=memcache.googleapis.com",
        "ruby-cloud-api-shortname=memcache",
    ],
    ruby_cloud_description = "Google Cloud Memorystore for Memcached API is used for creating and managing Memcached instances in GCP.",
    ruby_cloud_title = "Google Cloud Memorystore for Memcached",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-memcache-ruby",
    deps = [
        ":memcache_ruby_wrapper",
    ],
)
