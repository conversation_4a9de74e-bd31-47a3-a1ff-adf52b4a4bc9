# This build file includes a target for the Ruby wrapper library for
# google-cloud-privileged_access_manager.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for privilegedaccessmanager.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "privilegedaccessmanager_ruby_wrapper",
    srcs = ["//google/cloud/privilegedaccessmanager/v1:privilegedaccessmanager_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-privileged_access_manager",
        "ruby-cloud-wrapper-of=v1:0.0",
    ],
    service_yaml = "//google/cloud/privilegedaccessmanager/v1:privilegedaccessmanager_v1.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-privileged_access_manager-ruby",
    deps = [
        ":privilegedaccessmanager_ruby_wrapper",
    ],
)
