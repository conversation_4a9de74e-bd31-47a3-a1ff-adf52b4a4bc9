type: google.api.Service
config_version: 3
name: privilegedaccessmanager.googleapis.com
title: Privileged Access Manager API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.privilegedaccessmanager.v1.PrivilegedAccessManager
- name: google.longrunning.Operations

types:
- name: google.cloud.privilegedaccessmanager.v1.OperationMetadata

documentation:
  summary: |-
    Privileged Access Manager (PAM) helps you on your journey towards least
    privilege and helps mitigate risks tied to privileged access misuse or
    abuse. PAM allows you to shift from always-on standing privileges towards
    on-demand access with just-in-time, time-bound, and approval-based access
    elevations. PAM allows IAM administrators to create entitlements that can
    grant just-in-time, temporary access to any resource scope. Requesters can
    explore eligible entitlements and request the access needed for their
    task. Approvers are notified when approvals await their
    decision. Streamlined workflows facilitated by using PAM can support
    various use cases, including emergency access for incident responders,
    time-boxed access for developers for critical deployment or maintenance,
    temporary access for operators for data ingestion and audits, JIT access
    to service accounts for automated tasks, and more.
  overview: |-
    ## Overview

    Privileged Access Manager (PAM) is a Google Cloud native, managed solution
    to secure, manage and audit privileged access while ensuring operational
    velocity and developer productivity.

    PAM enables just-in-time, time-bound, approval-based access elevations,
    and auditing of privileged access elevations and activity. PAM lets you
    define the rules of who can request access, what they can request access
    to, and if they should be granted access with or without approvals based
    on the sensitivity of the access and emergency of the situation.

    ## Concepts

    ### Entitlement

    An entitlement is an eligibility or license that allows specified users
    (requesters) to request and obtain access to specified resources subject
    to a set of conditions such as duration, etc. entitlements can be granted
    to both human and non-human principals.

    ### Grant

    A grant is an instance of active usage against the entitlement. A user can
    place a request for a grant against an entitlement. The request may be
    forwarded to an approver for their decision. Once approved, the grant is
    activated, ultimately giving the user access (roles/permissions) on a
    resource per the criteria specified in entitlement.

    ### How does PAM work

    PAM creates and uses a service agent (Google-managed service account) to
    perform the required IAM policy changes for granting access at a
    specific
    resource/access scope. The service agent requires getIAMPolicy and
    setIAMPolicy permissions at the appropriate (or higher) access scope
    -
    Organization/Folder/Project to make policy changes on the resources listed
    in PAM entitlements.

    When enabling PAM for a resource scope, the user/ principal performing
    that action should have the appropriate permissions at that resource
    scope
    (resourcemanager.{projects|folders|organizations}.setIamPolicy,
    resourcemanager.{projects|folders|organizations}.getIamPolicy, and
    resourcemanager.{projects|folders|organizations}.get) to list and grant
    the service agent/account the required access to perform IAM policy
    changes.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
    additional_bindings:
    - get: '/v1/{name=organizations/*/locations/*}'
    - get: '/v1/{name=folders/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
    additional_bindings:
    - get: '/v1/{name=organizations/*}/locations'
    - get: '/v1/{name=folders/*}/locations'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - delete: '/v1/{name=organizations/*/locations/*/operations/*}'
    - delete: '/v1/{name=folders/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/v1/{name=organizations/*/locations/*/operations/*}'
    - get: '/v1/{name=folders/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'
    additional_bindings:
    - get: '/v1/{name=organizations/*/locations/*}/operations'
    - get: '/v1/{name=folders/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.privilegedaccessmanager.v1.PrivilegedAccessManager.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1587284&template=2001118
  documentation_uri: https://cloud.google.com/iam/docs/pam-overview
  api_short_name: privilegedaccessmanager
  github_label: 'api: privilegedaccessmanager'
  doc_tag_prefix: privilegedaccessmanager
  organization: CLOUD
  library_settings:
  - version: google.cloud.privilegedaccessmanager.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/iam/docs/reference/pam/rpc
