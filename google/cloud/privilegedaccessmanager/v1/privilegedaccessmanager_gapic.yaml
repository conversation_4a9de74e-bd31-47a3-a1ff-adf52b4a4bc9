type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
interfaces:
- name: google.cloud.privilegedaccessmanager.v1.PrivilegedAccessManager
  methods:
  - name: CreateEntitlement
    long_running:
      initial_poll_delay_millis: 5000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 900000
  - name: UpdateEntitlement
    long_running:
      initial_poll_delay_millis: 5000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 900000
  - name: DeleteEntitlement
    long_running:
      initial_poll_delay_millis: 5000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 900000
  - name: RevokeGrant
    long_running:
      initial_poll_delay_millis: 5000
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 45000
      total_poll_timeout_millis: 900000
