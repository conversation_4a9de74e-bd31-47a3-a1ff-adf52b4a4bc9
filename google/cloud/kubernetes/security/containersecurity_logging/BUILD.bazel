# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "containersecurity_logging_proto",
    srcs = [
        "logging.proto",
    ],
    deps = [
        "@com_google_protobuf//:timestamp_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "containersecurity_logging_java_proto",
    deps = [":containersecurity_logging_proto"],
)

java_grpc_library(
    name = "containersecurity_logging_java_grpc",
    srcs = [":containersecurity_logging_proto"],
    deps = [":containersecurity_logging_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "containersecurity_logging_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/cloud/kubernetes/security/containersecurity_logging/containersecurity_loggingpb",
    protos = [":containersecurity_logging_proto"],
    deps = [],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "containersecurity_logging_moved_proto",
    srcs = [":containersecurity_logging_proto"],
    deps = [
        "@com_google_protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "containersecurity_logging_py_proto",
    deps = [":containersecurity_logging_moved_proto"],
)

py_grpc_library(
    name = "containersecurity_logging_py_grpc",
    srcs = [":containersecurity_logging_moved_proto"],
    deps = [":containersecurity_logging_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "containersecurity_logging_php_proto",
    deps = [":containersecurity_logging_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "containersecurity_logging_ruby_proto",
    deps = [":containersecurity_logging_proto"],
)

ruby_grpc_library(
    name = "containersecurity_logging_ruby_grpc",
    srcs = [":containersecurity_logging_proto"],
    deps = [":containersecurity_logging_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "containersecurity_logging_csharp_proto",
    deps = [":containersecurity_logging_proto"],
)

csharp_grpc_library(
    name = "containersecurity_logging_csharp_grpc",
    srcs = [":containersecurity_logging_proto"],
    deps = [":containersecurity_logging_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "containersecurity_logging_cc_proto",
    deps = [":containersecurity_logging_proto"],
)

cc_grpc_library(
    name = "containersecurity_logging_cc_grpc",
    srcs = [":containersecurity_logging_proto"],
    grpc_only = True,
    deps = [":containersecurity_logging_cc_proto"],
)
