// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cloud.kubernetes.security.containersecurity_logging;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Kubernetes.Security.ContainerSecurity.Logging";
option go_package = "cloud.google.com/go/cloud/kubernetes/security/containersecurity_logging/containersecurity_loggingpb;containersecurity_loggingpb";
option java_multiple_files = true;
option java_outer_classname = "ContainerSecurityLoggingProto";
option java_package = "com.google.cloud.kubernetes.security.containersecurity.logging";
option php_namespace = "Google\\Cloud\\Kubernetes\\Security\\ContainerSecurity\\Logging";
option ruby_package = "Google::Cloud::Kubernetes::Security::ContainerSecurity::Logging";

// Identifies a package vulnerability found within a workload.
message Vulnerability {
  // package name where vulnerability detected
  string package_name = 1;

  // affected package version
  string affected_package_version = 2;

  // title of vulnerability assigned by CVE
  string cve_id = 3;

  // cpe_uri where vulnerability detected
  string cpe_uri = 4;

  // assigned severity for vulnerability
  Severity severity = 5;

  // overall CVSS score
  float cvss_score = 6;

  // detailed CVSS score, format `CVSS:3.1/AV:A/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N`
  string cvss_vector = 7;

  // cpe_uri where vulnerability is fixed
  string fixed_cpe_uri = 8;

  // type of package (os, maven, go)
  string package_type = 9;

  // package name where vulnerability is fixed
  string fixed_package = 10;

  // fixed package version
  string fixed_package_version = 11;

  // detailed description
  string description = 12;

  // reference URL for source CVE database
  repeated string related_urls = 13;

  // affected images
  repeated string affected_images = 14;
}

// A security concern for an asset(i.e cluster, workload, etc). Each finding
// corresponds to a type of security concern. A finding is created during the
// scan of an asset by any one of the GKE Security Posture features that are
// enabled.
message Finding {
  // The current state of the finding(e.g still active, has been fixed etc).
  enum State {
    // Default value, only used to determine that nothing was specified.
    STATE_UNSPECIFIED = 0;

    // Active state means that the finding exists on the asset.
    ACTIVE = 1;

    // Remediated means that the finding has been fixed on the asset.
    REMEDIATED = 2;
  }

  // Fully qualified resource name of the k8s resource, e.g.:
  // {api}/{version}/namespaces/{namespace}/{kind}/{workload name}
  string resource_name = 1;

  // The type of security finding this is.
  FindingType type = 2;

  // State determines whether the finding still exists or has been resolved.
  State state = 3;

  // The human readable representation of the specific security finding.
  // e.g. RUN_AS_NONROOT, CVE_ID_0 etc depending on the type.
  string finding = 4;

  // Severity determines the recommended actions for this finding.
  Severity severity = 5;

  // The time this finding was found/remediated.
  google.protobuf.Timestamp event_time = 6;

  // Specific details about the security finding if there are any.
  oneof details {
    Vulnerability vulnerability = 7;
  }
}

// FindingType is an enumeration of all possible finding types in GKE Security
// Posture.
enum FindingType {
  // Default value, unspecified.
  FINDING_TYPE_UNSPECIFIED = 0;

  // Workload misconfiguration policy audit.
  FINDING_TYPE_MISCONFIG = 1;

  // Workload vulnerabilities scanning.
  FINDING_TYPE_VULNERABILITY = 2;
}

// Severity is an enumeration of all the possible severities of a violation.
enum Severity {
  // Default value, only used to determine that nothing was specified.
  SEVERITY_UNSPECIFIED = 0;

  // SEVERITY_CRITICAL recommends taking action immediately.
  SEVERITY_CRITICAL = 1;

  // SEVERITY_HIGH recommends taking action if possible.
  SEVERITY_HIGH = 2;

  // SEVERITY_MEDIUM recommends investigation.
  SEVERITY_MEDIUM = 3;

  // SEVERITY_LOW recommends being aware of the problem.
  SEVERITY_LOW = 4;
}
