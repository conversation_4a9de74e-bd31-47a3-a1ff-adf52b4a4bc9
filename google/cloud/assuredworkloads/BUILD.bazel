# This build file includes a target for the Ruby wrapper library for
# google-cloud-assured_workloads.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for assuredworkloads.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "assuredworkloads_ruby_wrapper",
    srcs = ["//google/cloud/assuredworkloads/v1:assuredworkloads_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-assured_workloads",
        "ruby-cloud-env-prefix=ASSURED_WORKLOADS",
        "ruby-cloud-wrapper-of=v1:0.9;v1beta1:0.17",
        "ruby-cloud-product-url=https://cloud.google.com/assured-workloads/",
        "ruby-cloud-api-id=assuredworkloads.googleapis.com",
        "ruby-cloud-api-shortname=assuredworkloads",
    ],
    ruby_cloud_description = "Assured Workloads for Government secures government workloads and accelerates the path to running compliant workloads on Google Cloud.",
    ruby_cloud_title = "Assured Workloads for Government",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-assuredworkloads-ruby",
    deps = [
        ":assuredworkloads_ruby_wrapper",
    ],
)
