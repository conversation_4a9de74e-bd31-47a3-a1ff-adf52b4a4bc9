type: google.api.Service
config_version: 3
name: assuredworkloads.googleapis.com
title: Assured Workloads API

apis:
- name: google.cloud.assuredworkloads.v1.AssuredWorkloadsService
- name: google.longrunning.Operations

types:
- name: google.cloud.assuredworkloads.v1.CreateWorkloadOperationMetadata
- name: google.cloud.assuredworkloads.v1.Workload

backend:
  rules:
  - selector: 'google.cloud.assuredworkloads.v1.AssuredWorkloadsService.*'
    deadline: 15.0
  - selector: google.longrunning.Operations.GetOperation
    deadline: 60.0
  - selector: google.longrunning.Operations.ListOperations
    deadline: 60.0

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=organizations/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=organizations/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.assuredworkloads.v1.AssuredWorkloadsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.ListOperations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
