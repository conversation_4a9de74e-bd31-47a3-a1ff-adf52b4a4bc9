// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.assuredworkloads.regulatoryintercept.logging.v1;

option go_package = "cloud.google.com/go/assuredworkloads/regulatoryintercept/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "RegulatoryInterceptAckLogEntryProto";
option java_package = "com.google.cloud.assuredworkloads.regulatoryintercept.logging.v1";

message RegulatoryInterceptAckLogEntry {
  // The id of the user that triggered the Regulatory Intercept.
  string user_id = 1;

  // The id of the GCP resource associated with the Assured Workload applicable
  // to the request. Must be of the format
  // //cloudresourcemanager.googleapis.com/{type}/{id}
  string assured_workload_resource_id = 2;
}
