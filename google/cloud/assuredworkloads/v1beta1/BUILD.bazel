# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "assuredworkloads_proto",
    srcs = [
        "assuredworkloads.proto",
        "assuredworkloads_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "assuredworkloads_proto_with_info",
    deps = [
        ":assuredworkloads_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "assuredworkloads_java_proto",
    deps = [":assuredworkloads_proto"],
)

java_grpc_library(
    name = "assuredworkloads_java_grpc",
    srcs = [":assuredworkloads_proto"],
    deps = [":assuredworkloads_java_proto"],
)

java_gapic_library(
    name = "assuredworkloads_java_gapic",
    srcs = [":assuredworkloads_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "assuredworkloads_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "assuredworkloads_v1beta1.yaml",
    test_deps = [
        ":assuredworkloads_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":assuredworkloads_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "assuredworkloads_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.assuredworkloads.v1beta1.AssuredWorkloadsServiceClientHttpJsonTest",
        "com.google.cloud.assuredworkloads.v1beta1.AssuredWorkloadsServiceClientTest",
    ],
    runtime_deps = [":assuredworkloads_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-assuredworkloads-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":assuredworkloads_java_gapic",
        ":assuredworkloads_java_grpc",
        ":assuredworkloads_java_proto",
        ":assuredworkloads_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "assuredworkloads_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/assuredworkloads/apiv1beta1/assuredworkloadspb",
    protos = [":assuredworkloads_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "assuredworkloads_go_gapic",
    srcs = [":assuredworkloads_proto_with_info"],
    grpc_service_config = "assuredworkloads_grpc_service_config.json",
    importpath = "cloud.google.com/go/assuredworkloads/apiv1beta1;assuredworkloads",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "assuredworkloads_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":assuredworkloads_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-assuredworkloads-v1beta1-go",
    deps = [
        ":assuredworkloads_go_gapic",
        ":assuredworkloads_go_gapic_srcjar-metadata.srcjar",
        ":assuredworkloads_go_gapic_srcjar-snippets.srcjar",
        ":assuredworkloads_go_gapic_srcjar-test.srcjar",
        ":assuredworkloads_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "assuredworkloads_py_gapic",
    srcs = [":assuredworkloads_proto"],
    grpc_service_config = "assuredworkloads_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-assured-workloads"],
    rest_numeric_enums = True,
    service_yaml = "assuredworkloads_v1beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "assuredworkloads_py_gapic_test",
    srcs = [
        "assuredworkloads_py_gapic_pytest.py",
        "assuredworkloads_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":assuredworkloads_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "assuredworkloads-v1beta1-py",
    deps = [
        ":assuredworkloads_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "assuredworkloads_php_proto",
    deps = [":assuredworkloads_proto"],
)

php_gapic_library(
    name = "assuredworkloads_php_gapic",
    srcs = [":assuredworkloads_proto_with_info"],
    grpc_service_config = "assuredworkloads_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "assuredworkloads_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":assuredworkloads_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-assuredworkloads-v1beta1-php",
    deps = [
        ":assuredworkloads_php_gapic",
        ":assuredworkloads_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "assuredworkloads_nodejs_gapic",
    package_name = "@google-cloud/assured-workloads",
    src = ":assuredworkloads_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "assuredworkloads_grpc_service_config.json",
    package = "google.cloud.assuredworkloads.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "assuredworkloads_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "assuredworkloads-v1beta1-nodejs",
    deps = [
        ":assuredworkloads_nodejs_gapic",
        ":assuredworkloads_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "assuredworkloads_ruby_proto",
    deps = [":assuredworkloads_proto"],
)

ruby_grpc_library(
    name = "assuredworkloads_ruby_grpc",
    srcs = [":assuredworkloads_proto"],
    deps = [":assuredworkloads_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "assuredworkloads_ruby_gapic",
    srcs = [":assuredworkloads_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=assuredworkloads.googleapis.com",
        "ruby-cloud-api-shortname=assuredworkloads",
        "ruby-cloud-env-prefix=ASSURED_WORKLOADS",
        "ruby-cloud-gem-name=google-cloud-assured_workloads-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/assured-workloads/",
    ],
    grpc_service_config = "assuredworkloads_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Assured Workloads for Government secures government workloads and accelerates the path to running compliant workloads on Google Cloud.",
    ruby_cloud_title = "Assured Workloads for Government V1beta1",
    service_yaml = "assuredworkloads_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":assuredworkloads_ruby_grpc",
        ":assuredworkloads_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-assuredworkloads-v1beta1-ruby",
    deps = [
        ":assuredworkloads_ruby_gapic",
        ":assuredworkloads_ruby_grpc",
        ":assuredworkloads_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "assuredworkloads_csharp_proto",
    deps = [":assuredworkloads_proto"],
)

csharp_grpc_library(
    name = "assuredworkloads_csharp_grpc",
    srcs = [":assuredworkloads_proto"],
    deps = [":assuredworkloads_csharp_proto"],
)

csharp_gapic_library(
    name = "assuredworkloads_csharp_gapic",
    srcs = [":assuredworkloads_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "assuredworkloads_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "assuredworkloads_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":assuredworkloads_csharp_grpc",
        ":assuredworkloads_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-assuredworkloads-v1beta1-csharp",
    deps = [
        ":assuredworkloads_csharp_gapic",
        ":assuredworkloads_csharp_grpc",
        ":assuredworkloads_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "assuredworkloads_cc_proto",
    deps = [":assuredworkloads_proto"],
)

cc_grpc_library(
    name = "assuredworkloads_cc_grpc",
    srcs = [":assuredworkloads_proto"],
    grpc_only = True,
    deps = [":assuredworkloads_cc_proto"],
)
