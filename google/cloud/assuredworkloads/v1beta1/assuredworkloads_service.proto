// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.assuredworkloads.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/assuredworkloads/v1beta1/assuredworkloads.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.AssuredWorkloads.V1Beta1";
option go_package = "cloud.google.com/go/assuredworkloads/apiv1beta1/assuredworkloadspb;assuredworkloadspb";
option java_multiple_files = true;
option java_outer_classname = "AssuredworkloadsServiceProto";
option java_package = "com.google.cloud.assuredworkloads.v1beta1";
option php_namespace = "Google\\Cloud\\AssuredWorkloads\\V1beta1";
option ruby_package = "Google::Cloud::AssuredWorkloads::V1beta1";

// Service to manage AssuredWorkloads.
service AssuredWorkloadsService {
  option (google.api.default_host) = "assuredworkloads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Creates Assured Workload.
  rpc CreateWorkload(CreateWorkloadRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=organizations/*/locations/*}/workloads"
      body: "workload"
    };
    option (google.api.method_signature) = "parent,workload";
    option (google.longrunning.operation_info) = {
      response_type: "Workload"
      metadata_type: "CreateWorkloadOperationMetadata"
    };
  }

  // Updates an existing workload.
  // Currently allows updating of workload display_name and labels.
  // For force updates don't set etag field in the Workload.
  // Only one update operation per workload can be in progress.
  rpc UpdateWorkload(UpdateWorkloadRequest) returns (Workload) {
    option (google.api.method_signature) = "workload,update_mask";
  }

  // Restrict the list of resources allowed in the Workload environment.
  // The current list of allowed products can be found at
  // https://cloud.google.com/assured-workloads/docs/supported-products
  // In addition to assuredworkloads.workload.update permission, the user should
  // also have orgpolicy.policy.set permission on the folder resource
  // to use this functionality.
  rpc RestrictAllowedResources(RestrictAllowedResourcesRequest) returns (RestrictAllowedResourcesResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{name=organizations/*/locations/*/workloads/*}:restrictAllowedResources"
      body: "*"
    };
  }

  // Deletes the workload. Make sure that workload's direct children are already
  // in a deleted state, otherwise the request will fail with a
  // FAILED_PRECONDITION error.
  // In addition to assuredworkloads.workload.delete permission, the user should
  // also have orgpolicy.policy.set permission on the deleted folder to remove
  // Assured Workloads OrgPolicies.
  rpc DeleteWorkload(DeleteWorkloadRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=organizations/*/locations/*/workloads/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets Assured Workload associated with a CRM Node
  rpc GetWorkload(GetWorkloadRequest) returns (Workload) {
    option (google.api.method_signature) = "name";
  }

  // Analyze if the source Assured Workloads can be moved to the target Assured
  // Workload
  rpc AnalyzeWorkloadMove(AnalyzeWorkloadMoveRequest) returns (AnalyzeWorkloadMoveResponse) {
    option (google.api.method_signature) = "project,target";
  }

  // Lists Assured Workloads under a CRM Node.
  rpc ListWorkloads(ListWorkloadsRequest) returns (ListWorkloadsResponse) {
    option (google.api.method_signature) = "parent";
  }
}
