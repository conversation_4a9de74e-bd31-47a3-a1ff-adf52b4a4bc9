// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.assuredworkloads.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.AssuredWorkloads.V1Beta1";
option go_package = "cloud.google.com/go/assuredworkloads/apiv1beta1/assuredworkloadspb;assuredworkloadspb";
option java_multiple_files = true;
option java_outer_classname = "AssuredworkloadsProto";
option java_package = "com.google.cloud.assuredworkloads.v1beta1";
option php_namespace = "Google\\Cloud\\AssuredWorkloads\\V1beta1";
option ruby_package = "Google::Cloud::AssuredWorkloads::V1beta1";
option (google.api.resource_definition) = {
  type: "assuredworkloads.googleapis.com/Location"
  pattern: "organizations/{organization}/locations/{location}"
};

// Request for creating a workload.
message CreateWorkloadRequest {
  // Required. The resource name of the new Workload's parent.
  // Must be of the form `organizations/{org_id}/locations/{location_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "assuredworkloads.googleapis.com/Workload"
    }
  ];

  // Required. Assured Workload to create
  Workload workload = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A identifier associated with the workload and underlying projects which
  // allows for the break down of billing costs for a workload. The value
  // provided for the identifier will add a label to the workload and contained
  // projects with the identifier as the value.
  string external_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request for Updating a workload.
message UpdateWorkloadRequest {
  // Required. The workload to update.
  // The workload's `name` field is used to identify the workload to be updated.
  // Format:
  // organizations/{org_id}/locations/{location_id}/workloads/{workload_id}
  Workload workload = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to be updated.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for restricting list of available resources in Workload environment.
message RestrictAllowedResourcesRequest {
  // The type of restriction.
  enum RestrictionType {
    // Unknown restriction type.
    RESTRICTION_TYPE_UNSPECIFIED = 0;

    // Allow the use all of all gcp products, irrespective of the compliance
    // posture. This effectively removes gcp.restrictServiceUsage OrgPolicy
    // on the AssuredWorkloads Folder.
    ALLOW_ALL_GCP_RESOURCES = 1;

    // Based on Workload's compliance regime, allowed list changes.
    // See - https://cloud.google.com/assured-workloads/docs/supported-products
    // for the list of supported resources.
    ALLOW_COMPLIANT_RESOURCES = 2;
  }

  // Required. The resource name of the Workload. This is the workloads's
  // relative path in the API, formatted as
  // "organizations/{organization_id}/locations/{location_id}/workloads/{workload_id}".
  // For example,
  // "organizations/123/locations/us-east1/workloads/assured-workload-1".
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of restriction for using gcp products in the Workload environment.
  RestrictionType restriction_type = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response for restricting the list of allowed resources.
message RestrictAllowedResourcesResponse {

}

// Request for deleting a Workload.
message DeleteWorkloadRequest {
  // Required. The `name` field is used to identify the workload.
  // Format:
  // organizations/{org_id}/locations/{location_id}/workloads/{workload_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "assuredworkloads.googleapis.com/Workload"
    }
  ];

  // Optional. The etag of the workload.
  // If this is provided, it must match the server's etag.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for fetching a workload.
message GetWorkloadRequest {
  // Required. The resource name of the Workload to fetch. This is the workloads's
  // relative path in the API, formatted as
  // "organizations/{organization_id}/locations/{location_id}/workloads/{workload_id}".
  // For example,
  // "organizations/123/locations/us-east1/workloads/assured-workload-1".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "assuredworkloads.googleapis.com/Workload"
    }
  ];
}

// A request to analyze a hypothetical move of a source project or project-based
// workload to a target (destination) folder-based workload.
message AnalyzeWorkloadMoveRequest {
  // The resource type to be moved to the destination workload. It can be either
  // an existing project or a project-based workload.
  oneof projectOrWorkloadResource {
    // The source type is a project-based workload. Specify the workloads's
    // relative resource name, formatted as:
    // "organizations/{ORGANIZATION_ID}/locations/{LOCATION_ID}/workloads/{WORKLOAD_ID}"
    // For example:
    // "organizations/123/locations/us-east1/workloads/assured-workload-1"
    string source = 1;

    // The source type is a project. Specify the project's relative resource
    // name, formatted as either a project number or a project ID:
    // "projects/{PROJECT_NUMBER}" or "projects/{PROJECT_ID}"
    // For example:
    // "projects/951040570662" when specifying a project number, or
    // "projects/my-project-123" when specifying a project ID.
    string project = 3;
  }

  // Required. The resource ID of the folder-based destination workload. This workload is
  // where the source project will hypothetically be moved to. Specify the
  // workload's relative resource name, formatted as:
  // "organizations/{ORGANIZATION_ID}/locations/{LOCATION_ID}/workloads/{WORKLOAD_ID}"
  // For example:
  // "organizations/123/locations/us-east1/workloads/assured-workload-2"
  string target = 2 [(google.api.field_behavior) = REQUIRED];
}

// A response that includes the analysis of the hypothetical resource move.
message AnalyzeWorkloadMoveResponse {
  // A list of blockers that should be addressed before moving the source
  // project or project-based workload to the destination folder-based workload.
  repeated string blockers = 1;
}

// Request for fetching workloads in an organization.
message ListWorkloadsRequest {
  // Required. Parent Resource to list workloads from.
  // Must be of the form `organizations/{org_id}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "assuredworkloads.googleapis.com/Workload"
    }
  ];

  // Page size.
  int32 page_size = 2;

  // Page token returned from previous request. Page token contains context from
  // previous request. Page token needs to be passed in the second and following
  // requests.
  string page_token = 3;

  // A custom filter for filtering by properties of a workload. At this time,
  // only filtering by labels is supported.
  string filter = 4;
}

// Response of ListWorkloads endpoint.
message ListWorkloadsResponse {
  // List of Workloads under a given parent.
  repeated Workload workloads = 1;

  // The next page token. Return empty if reached the last page.
  string next_page_token = 2;
}

// An Workload object for managing highly regulated workloads of cloud
// customers.
message Workload {
  option (google.api.resource) = {
    type: "assuredworkloads.googleapis.com/Workload"
    pattern: "organizations/{organization}/locations/{location}/workloads/{workload}"
  };

  // Represent the resources that are children of this Workload.
  message ResourceInfo {
    // The type of resource.
    enum ResourceType {
      // Unknown resource type.
      RESOURCE_TYPE_UNSPECIFIED = 0;

      // Deprecated. Existing workloads will continue to support this, but new
      // CreateWorkloadRequests should not specify this as an input value.
      CONSUMER_PROJECT = 1 [deprecated = true];

      // Consumer Folder.
      CONSUMER_FOLDER = 4;

      // Consumer project containing encryption keys.
      ENCRYPTION_KEYS_PROJECT = 2;

      // Keyring resource that hosts encryption keys.
      KEYRING = 3;
    }

    // Resource identifier.
    // For a project this represents project_number.
    int64 resource_id = 1;

    // Indicates the type of resource.
    ResourceType resource_type = 2;
  }

  // Supported Compliance Regimes.
  enum ComplianceRegime {
    // Unknown compliance regime.
    COMPLIANCE_REGIME_UNSPECIFIED = 0;

    // Information protection as per DoD IL4 requirements.
    IL4 = 1;

    // Criminal Justice Information Services (CJIS) Security policies.
    CJIS = 2;

    // FedRAMP High data protection controls
    FEDRAMP_HIGH = 3;

    // FedRAMP Moderate data protection controls
    FEDRAMP_MODERATE = 4;

    // Assured Workloads For US Regions data protection controls
    US_REGIONAL_ACCESS = 5;

    // Health Insurance Portability and Accountability Act controls
    HIPAA = 6;

    // Health Information Trust Alliance controls
    HITRUST = 7;

    // Assured Workloads For EU Regions and Support controls
    EU_REGIONS_AND_SUPPORT = 8;

    // Assured Workloads For Canada Regions and Support controls
    CA_REGIONS_AND_SUPPORT = 9;

    // International Traffic in Arms Regulations
    ITAR = 10;

    // Assured Workloads for Australia Regions and Support controls
    AU_REGIONS_AND_US_SUPPORT = 11;
  }

  // Settings specific to the Key Management Service.
  message KMSSettings {
    option deprecated = true;

    // Required. Input only. Immutable. The time at which the Key Management Service will automatically create a
    // new version of the crypto key and mark it as the primary.
    google.protobuf.Timestamp next_rotation_time = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];

    // Required. Input only. Immutable. [next_rotation_time] will be advanced by this period when the Key
    // Management Service automatically rotates a key. Must be at least 24 hours
    // and at most 876,000 hours.
    google.protobuf.Duration rotation_period = 2 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];
  }

  // Settings specific to resources needed for IL4.
  message IL4Settings {
    option deprecated = true;

    // Input only. Immutable. Settings used to create a CMEK crypto key.
    KMSSettings kms_settings = 1 [
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];
  }

  // Settings specific to resources needed for CJIS.
  message CJISSettings {
    option deprecated = true;

    // Input only. Immutable. Settings used to create a CMEK crypto key.
    KMSSettings kms_settings = 1 [
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];
  }

  // Settings specific to resources needed for FedRAMP High.
  message FedrampHighSettings {
    option deprecated = true;

    // Input only. Immutable. Settings used to create a CMEK crypto key.
    KMSSettings kms_settings = 1 [
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];
  }

  // Settings specific to resources needed for FedRAMP Moderate.
  message FedrampModerateSettings {
    option deprecated = true;

    // Input only. Immutable. Settings used to create a CMEK crypto key.
    KMSSettings kms_settings = 1 [
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];
  }

  // Represent the custom settings for the resources to be created.
  message ResourceSettings {
    // Resource identifier.
    // For a project this represents project_id. If the project is already
    // taken, the workload creation will fail.
    // For KeyRing, this represents the keyring_id.
    // For a folder, don't set this value as folder_id is assigned by Google.
    string resource_id = 1;

    // Indicates the type of resource. This field should be specified to
    // correspond the id to the right project type (CONSUMER_PROJECT or
    // ENCRYPTION_KEYS_PROJECT)
    ResourceInfo.ResourceType resource_type = 2;

    // User-assigned resource display name.
    // If not empty it will be used to create a resource with the specified
    // name.
    string display_name = 3;
  }

  // Key Access Justifications(KAJ) Enrollment State.
  enum KajEnrollmentState {
    // Default State for KAJ Enrollment.
    KAJ_ENROLLMENT_STATE_UNSPECIFIED = 0;

    // Pending State for KAJ Enrollment.
    KAJ_ENROLLMENT_STATE_PENDING = 1;

    // Complete State for KAJ Enrollment.
    KAJ_ENROLLMENT_STATE_COMPLETE = 2;
  }

  // Signed Access Approvals (SAA) enrollment response.
  message SaaEnrollmentResponse {
    // Setup state of SAA enrollment.
    enum SetupState {
      // Unspecified.
      SETUP_STATE_UNSPECIFIED = 0;

      // SAA enrollment pending.
      STATUS_PENDING = 1;

      // SAA enrollment comopleted.
      STATUS_COMPLETE = 2;
    }

    // Setup error of SAA enrollment.
    enum SetupError {
      // Unspecified.
      SETUP_ERROR_UNSPECIFIED = 0;

      // Invalid states for all customers, to be redirected to AA UI for
      // additional details.
      ERROR_INVALID_BASE_SETUP = 1;

      // Returned when there is not an EKM key configured.
      ERROR_MISSING_EXTERNAL_SIGNING_KEY = 2;

      // Returned when there are no enrolled services or the customer is
      // enrolled in CAA only for a subset of services.
      ERROR_NOT_ALL_SERVICES_ENROLLED = 3;

      // Returned when exception was encountered during evaluation of other
      // criteria.
      ERROR_SETUP_CHECK_FAILED = 4;
    }

    // Indicates SAA enrollment status of a given workload.
    optional SetupState setup_status = 1;

    // Indicates SAA enrollment setup error if any.
    repeated SetupError setup_errors = 2;
  }

  // Optional. The resource name of the workload.
  // Format:
  // organizations/{organization}/locations/{location}/workloads/{workload}
  //
  // Read-only.
  string name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The user-assigned display name of the Workload.
  // When present it must be between 4 to 30 characters.
  // Allowed characters are: lowercase and uppercase letters, numbers,
  // hyphen, and spaces.
  //
  // Example: My Workload
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The resources associated with this workload.
  // These resources will be created when creating the workload.
  // If any of the projects already exist, the workload creation will fail.
  // Always read only.
  repeated ResourceInfo resources = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. Compliance Regime associated with this workload.
  ComplianceRegime compliance_regime = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Immutable. The Workload creation timestamp.
  google.protobuf.Timestamp create_time = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. The billing account used for the resources which are
  // direct children of workload. This billing account is initially associated
  // with the resources created as part of Workload creation.
  // After the initial creation of these resources, the customer can change
  // the assigned billing account.
  // The resource name has the form
  // `billingAccounts/{billing_account_id}`. For example,
  // `billingAccounts/012345-567890-ABCDEF`.
  string billing_account = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Settings specific to the selected [compliance_regime]
  oneof compliance_regime_settings {
    // Input only. Immutable. Settings specific to resources needed for IL4.
    IL4Settings il4_settings = 7 [
      deprecated = true,
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];

    // Input only. Immutable. Settings specific to resources needed for CJIS.
    CJISSettings cjis_settings = 8 [
      deprecated = true,
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];

    // Input only. Immutable. Settings specific to resources needed for FedRAMP High.
    FedrampHighSettings fedramp_high_settings = 11 [
      deprecated = true,
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];

    // Input only. Immutable. Settings specific to resources needed for FedRAMP Moderate.
    FedrampModerateSettings fedramp_moderate_settings = 12 [
      deprecated = true,
      (google.api.field_behavior) = INPUT_ONLY,
      (google.api.field_behavior) = IMMUTABLE
    ];
  }

  // Optional. ETag of the workload, it is calculated on the basis
  // of the Workload contents. It will be used in Update & Delete operations.
  string etag = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels applied to the workload.
  map<string, string> labels = 10 [(google.api.field_behavior) = OPTIONAL];

  // Input only. The parent resource for the resources managed by this Assured Workload. May
  // be either empty or a folder resource which is a child of the
  // Workload parent. If not specified all resources are created under the
  // parent organization.
  // Format:
  // folders/{folder_id}
  string provisioned_resources_parent = 13 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. Settings used to create a CMEK crypto key. When set, a project with a KMS
  // CMEK key is provisioned.
  // This field is deprecated as of Feb 28, 2022.
  // In order to create a Keyring, callers should specify,
  // ENCRYPTION_KEYS_PROJECT or KEYRING in ResourceSettings.resource_type field.
  KMSSettings kms_settings = 14 [
    deprecated = true,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Input only. Resource properties that are used to customize workload resources.
  // These properties (such as custom project id) will be used to create
  // workload resources if possible. This field is optional.
  repeated ResourceSettings resource_settings = 15 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Represents the KAJ enrollment state of the given workload.
  KajEnrollmentState kaj_enrollment_state = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Indicates the sovereignty status of the given workload.
  // Currently meant to be used by Europe/Canada customers.
  bool enable_sovereign_controls = 18 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Represents the SAA enrollment response of the given workload.
  // SAA enrollment response is queried during GetWorkload call.
  // In failure cases, user friendly error message is shown in SAA details page.
  SaaEnrollmentResponse saa_enrollment_response = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Urls for services which are compliant for this Assured Workload, but which
  // are currently disallowed by the ResourceUsageRestriction org policy.
  // Invoke RestrictAllowedResources endpoint to allow your project developers
  // to use these services in their environment."
  repeated string compliant_but_disallowed_services = 24 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Operation metadata to give request details of CreateWorkload.
message CreateWorkloadOperationMetadata {
  // Optional. Time when the operation was created.
  google.protobuf.Timestamp create_time = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The display name of the workload.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The parent of the workload.
  string parent = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Compliance controls that should be applied to the resources managed by
  // the workload.
  Workload.ComplianceRegime compliance_regime = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Resource properties in the input that are used for creating/customizing
  // workload resources.
  repeated Workload.ResourceSettings resource_settings = 5 [(google.api.field_behavior) = OPTIONAL];
}
