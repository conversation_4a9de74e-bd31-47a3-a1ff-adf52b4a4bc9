{"methodConfig": [{"name": [{"service": "google.cloud.assuredworkloads.v1beta1.AssuredWorkloadsService", "method": "CreateWorkload"}, {"service": "google.cloud.assuredworkloads.v1beta1.AssuredWorkloadsService", "method": "UpdateWorkload"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.assuredworkloads.v1beta1.AssuredWorkloadsService", "method": "GetWorkload"}, {"service": "google.cloud.assuredworkloads.v1beta1.AssuredWorkloadsService", "method": "ListWorkloads"}, {"service": "google.cloud.assuredworkloads.v1beta1.AssuredWorkloadsService", "method": "DeleteWorkload"}, {"service": "google.cloud.assuredworkloads.v1beta1.AssuredWorkloadsService", "method": "AnalyzeWorkloadMove"}, {"service": "google.longrunning.Operations", "method": "GetOperation"}, {"service": "google.longrunning.Operations", "method": "ListOperations"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.2s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}