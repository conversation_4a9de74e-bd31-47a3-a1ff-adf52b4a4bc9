# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "audit_proto",
    srcs = [
        "audit_log.proto",
        "bigquery_audit_metadata.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
        "//google/iam/v1:policy_proto",
        "//google/rpc:status_proto",
        "//google/rpc/context:attribute_context_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "audit_proto_with_info",
    deps = [
        ":audit_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "audit_java_proto",
    deps = [":audit_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-audit-java",
    transport = "grpc+rest",
    deps = [
        ":audit_java_proto",
        ":audit_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "audit_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/cloud/audit",
    protos = [":audit_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/rpc:status_go_proto",
        "//google/rpc/context:attribute_context_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_proto_library",
)

py_proto_library(
    name = "audit_py_proto",
    deps = [":audit_proto"],
)


# Open Source Packages
py_gapic_assembly_pkg(
    name = "audit-py",
    deps = [
        ":audit_py_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "audit_php_proto",
    deps = [":audit_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-audit-php",
    deps = [":audit_php_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "audit_ruby_proto",
    deps = [":audit_proto"],
)

ruby_grpc_library(
    name = "audit_ruby_grpc",
    srcs = [":audit_proto"],
    deps = [":audit_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "audit_csharp_proto",
    deps = [":audit_proto"],
)

csharp_grpc_library(
    name = "audit_csharp_grpc",
    srcs = [":audit_proto"],
    deps = [":audit_csharp_proto"],
)

csharp_gapic_assembly_pkg(
    name = "google-cloud-audit-csharp",
    package_name = "Google.Cloud.Audit",
    generate_nongapic_package = True,
    deps = [
        ":audit_csharp_grpc",
        ":audit_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "audit_cc_proto",
    deps = [":audit_proto"],
)

cc_grpc_library(
    name = "audit_cc_grpc",
    srcs = [":audit_proto"],
    grpc_only = True,
    deps = [":audit_cc_proto"],
)
