# This build file includes a target for the Ruby wrapper library for
# google-cloud-recommendation_engine.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for recommendationengine.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "recommendationengine_ruby_wrapper",
    srcs = ["//google/cloud/recommendationengine/v1beta1:recommendationengine_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-recommendation_engine",
        "ruby-cloud-env-prefix=RECOMMENDATION_ENGINE",
        "ruby-cloud-wrapper-of=v1beta1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/recommendations",
        "ruby-cloud-api-id=recommendationengine.googleapis.com",
        "ruby-cloud-api-shortname=recommendationengine",
    ],
    ruby_cloud_description = "Recommendations AI enables you to build an end-to-end personalized recommendation system based on state-of-the-art deep learning ML models, without a need for expertise in ML or recommendation systems.",
    ruby_cloud_title = "Recommendations AI",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-recommendationengine-ruby",
    deps = [
        ":recommendationengine_ruby_wrapper",
    ],
)
