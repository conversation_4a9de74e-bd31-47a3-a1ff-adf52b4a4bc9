// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommendationengine.v1beta1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/recommendationengine/v1beta1/user_event.proto";
import "google/protobuf/struct.proto";
import "google/api/client.proto";

option csharp_namespace = "Google.Cloud.RecommendationEngine.V1Beta1";
option go_package = "cloud.google.com/go/recommendationengine/apiv1beta1/recommendationenginepb;recommendationenginepb";
option java_multiple_files = true;
option java_package = "com.google.cloud.recommendationengine.v1beta1";
option objc_class_prefix = "RECAI";
option php_namespace = "Google\\Cloud\\RecommendationEngine\\V1beta1";
option ruby_package = "Google::Cloud::RecommendationEngine::V1beta1";

// Service for making recommendation prediction.
service PredictionService {
  option (google.api.default_host) = "recommendationengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Makes a recommendation prediction. If using API Key based authentication,
  // the API Key must be registered using the
  // [PredictionApiKeyRegistry][google.cloud.recommendationengine.v1beta1.PredictionApiKeyRegistry]
  // service. [Learn more](/recommendations-ai/docs/setting-up#register-key).
  rpc Predict(PredictRequest) returns (PredictResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/catalogs/*/eventStores/*/placements/*}:predict"
      body: "*"
    };
    option (google.api.method_signature) = "name,user_event";
  }
}

// Request message for Predict method.
message PredictRequest {
  // Required. Full resource name of the format:
  // `{name=projects/*/locations/global/catalogs/default_catalog/eventStores/default_event_store/placements/*}`
  // The id of the recommendation engine placement. This id is used to identify
  // the set of models that will be used to make the prediction.
  //
  // We currently support three placements with the following IDs by default:
  //
  // * `shopping_cart`: Predicts items frequently bought together with one or
  //   more catalog items in the same shopping session. Commonly displayed after
  //   `add-to-cart` events, on product detail pages, or on the shopping cart
  //   page.
  //
  // * `home_page`: Predicts the next product that a user will most likely
  //   engage with or purchase based on the shopping or viewing history of the
  //   specified `userId` or `visitorId`. For example - Recommendations for you.
  //
  // * `product_detail`: Predicts the next product that a user will most likely
  //   engage with or purchase. The prediction is based on the shopping or
  //   viewing history of the specified `userId` or `visitorId` and its
  //   relevance to a specified `CatalogItem`. Typically used on product detail
  //   pages. For example - More items like this.
  //
  // * `recently_viewed_default`: Returns up to 75 items recently viewed by the
  //   specified `userId` or `visitorId`, most recent ones first. Returns
  //   nothing if neither of them has viewed any items yet. For example -
  //   Recently viewed.
  //
  // The full list of available placements can be seen at
  // https://console.cloud.google.com/recommendation/datafeeds/default_catalog/dashboard
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/Placement"
    }
  ];

  // Required. Context about the user, what they are looking at and what action
  // they took to trigger the predict request. Note that this user event detail
  // won't be ingested to userEvent logs. Thus, a separate userEvent write
  // request is required for event logging.
  UserEvent user_event = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Maximum number of results to return per page. Set this property
  // to the number of prediction results required. If zero, the service will
  // choose a reasonable default.
  int32 page_size = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The previous PredictResponse.next_page_token.
  string page_token = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter for restricting prediction results. Accepts values for
  // tags and the `filterOutOfStockItems` flag.
  //
  //  * Tag expressions. Restricts predictions to items that match all of the
  //    specified tags. Boolean operators `OR` and `NOT` are supported if the
  //    expression is enclosed in parentheses, and must be separated from the
  //    tag values by a space. `-"tagA"` is also supported and is equivalent to
  //    `NOT "tagA"`. Tag values must be double quoted UTF-8 encoded strings
  //    with a size limit of 1 KiB.
  //
  //  * filterOutOfStockItems. Restricts predictions to items that do not have a
  //    stockState value of OUT_OF_STOCK.
  //
  // Examples:
  //
  //  * tag=("Red" OR "Blue") tag="New-Arrival" tag=(NOT "promotional")
  //  * filterOutOfStockItems  tag=(-"promotional")
  //  * filterOutOfStockItems
  string filter = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Use dryRun mode for this prediction query. If set to true, a
  // dummy model will be used that returns arbitrary catalog items.
  // Note that the dryRun mode should only be used for testing the API, or if
  // the model is not ready.
  bool dry_run = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Additional domain specific parameters for the predictions.
  //
  // Allowed values:
  //
  // * `returnCatalogItem`: Boolean. If set to true, the associated catalogItem
  //    object will be returned in the
  //   `PredictResponse.PredictionResult.itemMetadata` object in the method
  //    response.
  // * `returnItemScore`: Boolean. If set to true, the prediction 'score'
  //    corresponding to each returned item will be set in the `metadata`
  //    field in the prediction response. The given 'score' indicates the
  //    probability of an item being clicked/purchased given the user's context
  //    and history.
  map<string, google.protobuf.Value> params = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The labels for the predict request.
  //
  //  * Label keys can contain lowercase letters, digits and hyphens, must start
  //    with a letter, and must end with a letter or digit.
  //  * Non-zero label values can contain lowercase letters, digits and hyphens,
  //    must start with a letter, and must end with a letter or digit.
  //  * No more than 64 labels can be associated with a given request.
  //
  // See https://goo.gl/xmQnxf for more information on and examples of labels.
  map<string, string> labels = 9 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for predict method.
message PredictResponse {
  // PredictionResult represents the recommendation prediction results.
  message PredictionResult {
    // ID of the recommended catalog item
    string id = 1;

    // Additional item metadata / annotations.
    //
    // Possible values:
    //
    // * `catalogItem`: JSON representation of the catalogItem. Will be set if
    //   `returnCatalogItem` is set to true in `PredictRequest.params`.
    // * `score`: Prediction score in double value. Will be set if
    //   `returnItemScore` is set to true in `PredictRequest.params`.
    map<string, google.protobuf.Value> item_metadata = 2;
  }

  // A list of recommended items. The order represents the ranking (from the
  // most relevant item to the least).
  repeated PredictionResult results = 1;

  // A unique recommendation token. This should be included in the user event
  // logs resulting from this recommendation, which enables accurate attribution
  // of recommendation model performance.
  string recommendation_token = 2;

  // IDs of items in the request that were missing from the catalog.
  repeated string items_missing_in_catalog = 3;

  // True if the dryRun property was set in the request.
  bool dry_run = 4;

  // Additional domain specific prediction response metadata.
  map<string, google.protobuf.Value> metadata = 5;

  // If empty, the list is complete. If nonempty, the token to pass to the next
  // request's PredictRequest.page_token.
  string next_page_token = 6;
}
