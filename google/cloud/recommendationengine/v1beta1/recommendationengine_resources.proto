// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Contains common resources for recommendationengine/v1beta1.

syntax = "proto3";

package google.cloud.recommendationengine.v1beta1;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.RecommendationEngine.V1Beta1";
option go_package = "cloud.google.com/go/recommendationengine/apiv1beta1/recommendationenginepb;recommendationenginepb";
option java_multiple_files = true;
option java_package = "com.google.cloud.recommendationengine.v1beta1";
option objc_class_prefix = "RECAI";
option php_namespace = "Google\\Cloud\\RecommendationEngine\\V1beta1";
option ruby_package = "Google::Cloud::RecommendationEngine::V1beta1";
option (google.api.resource_definition) = {
  type: "recommendationengine.googleapis.com/Catalog"
  pattern: "projects/{project}/locations/{location}/catalogs/{catalog}"
};
option (google.api.resource_definition) = {
  type: "recommendationengine.googleapis.com/CatalogItemPath"
  pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/catalogItems/{catalog_item_path}"
};
option (google.api.resource_definition) = {
  type: "recommendationengine.googleapis.com/EventStore"
  pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/eventStores/{event_store}"
};
option (google.api.resource_definition) = {
  type: "recommendationengine.googleapis.com/PredictionApiKeyRegistration"
  pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/eventStores/{event_store}/predictionApiKeyRegistrations/{prediction_api_key_registration}"
};
option (google.api.resource_definition) = {
  type: "recommendationengine.googleapis.com/Placement"
  pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/eventStores/{event_store}/placements/{placement}"
};
