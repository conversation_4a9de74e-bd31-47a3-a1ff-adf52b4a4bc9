// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommendationengine.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/recommendationengine/v1beta1/catalog.proto";
import "google/cloud/recommendationengine/v1beta1/import.proto";
import "google/cloud/recommendationengine/v1beta1/recommendationengine_resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.RecommendationEngine.V1Beta1";
option go_package = "cloud.google.com/go/recommendationengine/apiv1beta1/recommendationenginepb;recommendationenginepb";
option java_multiple_files = true;
option java_package = "com.google.cloud.recommendationengine.v1beta1";
option objc_class_prefix = "RECAI";
option php_namespace = "Google\\Cloud\\RecommendationEngine\\V1beta1";
option ruby_package = "Google::Cloud::RecommendationEngine::V1beta1";

// Service for ingesting catalog information of the customer's website.
service CatalogService {
  option (google.api.default_host) = "recommendationengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a catalog item.
  rpc CreateCatalogItem(CreateCatalogItemRequest) returns (CatalogItem) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/catalogs/*}/catalogItems"
      body: "catalog_item"
    };
    option (google.api.method_signature) = "parent,catalog_item";
  }

  // Gets a specific catalog item.
  rpc GetCatalogItem(GetCatalogItemRequest) returns (CatalogItem) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/catalogs/*/catalogItems/**}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a list of catalog items.
  rpc ListCatalogItems(ListCatalogItemsRequest)
      returns (ListCatalogItemsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/catalogs/*}/catalogItems"
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // Updates a catalog item. Partial updating is supported. Non-existing
  // items will be created.
  rpc UpdateCatalogItem(UpdateCatalogItemRequest) returns (CatalogItem) {
    option (google.api.http) = {
      patch: "/v1beta1/{name=projects/*/locations/*/catalogs/*/catalogItems/**}"
      body: "catalog_item"
    };
    option (google.api.method_signature) = "name,catalog_item,update_mask";
  }

  // Deletes a catalog item.
  rpc DeleteCatalogItem(DeleteCatalogItemRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/catalogs/*/catalogItems/**}"
    };
    option (google.api.method_signature) = "name";
  }

  // Bulk import of multiple catalog items. Request processing may be
  // synchronous. No partial updating supported. Non-existing items will be
  // created.
  //
  // Operation.response is of type ImportResponse. Note that it is
  // possible for a subset of the items to be successfully updated.
  rpc ImportCatalogItems(ImportCatalogItemsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/catalogs/*}/catalogItems:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.recommendationengine.v1beta1.ImportCatalogItemsResponse"
      metadata_type: "google.cloud.recommendationengine.v1beta1.ImportMetadata"
    };
    option (google.api.method_signature) =
        "parent,request_id,input_config,errors_config";
  }
}

// Request message for CreateCatalogItem method.
message CreateCatalogItemRequest {
  // Required. The parent catalog resource name, such as
  // `projects/*/locations/global/catalogs/default_catalog`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/Catalog"
    }
  ];

  // Required. The catalog item to create.
  CatalogItem catalog_item = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetCatalogItem method.
message GetCatalogItemRequest {
  // Required. Full resource name of catalog item, such as
  // `projects/*/locations/global/catalogs/default_catalog/catalogitems/some_catalog_item_id`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/CatalogItemPath"
    }
  ];
}

// Request message for ListCatalogItems method.
message ListCatalogItemsRequest {
  // Required. The parent catalog resource name, such as
  // `projects/*/locations/global/catalogs/default_catalog`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/Catalog"
    }
  ];

  // Optional. Maximum number of results to return per page. If zero, the
  // service will choose a reasonable default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The previous ListCatalogItemsResponse.next_page_token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter to apply on the list results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListCatalogItems method.
message ListCatalogItemsResponse {
  // The catalog items.
  repeated CatalogItem catalog_items = 1;

  // If empty, the list is complete. If nonempty, the token to pass to the next
  // request's ListCatalogItemRequest.page_token.
  string next_page_token = 2;
}

// Request message for UpdateCatalogItem method.
message UpdateCatalogItemRequest {
  // Required. Full resource name of catalog item, such as
  // `projects/*/locations/global/catalogs/default_catalog/catalogItems/some_catalog_item_id`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/CatalogItemPath"
    }
  ];

  // Required. The catalog item to update/create. The 'catalog_item_id' field
  // has to match that in the 'name'.
  CatalogItem catalog_item = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Indicates which fields in the provided 'item' to update. If not
  // set, will by default update all fields.
  google.protobuf.FieldMask update_mask = 3;
}

// Request message for DeleteCatalogItem method.
message DeleteCatalogItemRequest {
  // Required. Full resource name of catalog item, such as
  // `projects/*/locations/global/catalogs/default_catalog/catalogItems/some_catalog_item_id`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/CatalogItemPath"
    }
  ];
}
