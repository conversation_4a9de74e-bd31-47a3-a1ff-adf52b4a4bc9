// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommendationengine.v1beta1;

import "google/api/field_behavior.proto";
import "google/cloud/recommendationengine/v1beta1/common.proto";

option csharp_namespace = "Google.Cloud.RecommendationEngine.V1Beta1";
option go_package = "cloud.google.com/go/recommendationengine/apiv1beta1/recommendationenginepb;recommendationenginepb";
option java_multiple_files = true;
option java_package = "com.google.cloud.recommendationengine.v1beta1";
option objc_class_prefix = "RECAI";
option php_namespace = "Google\\Cloud\\RecommendationEngine\\V1beta1";
option ruby_package = "Google::Cloud::RecommendationEngine::V1beta1";

// CatalogItem captures all metadata information of items to be recommended.
message CatalogItem {
  // Category represents catalog item category hierarchy.
  message CategoryHierarchy {
    // Required. Catalog item categories. Each category should be a UTF-8
    // encoded string with a length limit of 2 KiB.
    //
    // Note that the order in the list denotes the specificity (from least to
    // most specific).
    repeated string categories = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. Catalog item identifier. UTF-8 encoded string with a length limit
  // of 128 bytes.
  //
  // This id must be unique among all catalog items within the same catalog. It
  // should also be used when logging user events in order for the user events
  // to be joined with the Catalog.
  string id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Catalog item categories. This field is repeated for supporting
  // one catalog item belonging to several parallel category hierarchies.
  //
  // For example, if a shoes product belongs to both
  // ["Shoes & Accessories" -> "Shoes"] and
  // ["Sports & Fitness" -> "Athletic Clothing" -> "Shoes"], it could be
  // represented as:
  //
  //      "categoryHierarchies": [
  //        { "categories": ["Shoes & Accessories", "Shoes"]},
  //        { "categories": ["Sports & Fitness", "Athletic Clothing", "Shoes"] }
  //      ]
  repeated CategoryHierarchy category_hierarchies = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Catalog item title. UTF-8 encoded string with a length limit of 1
  // KiB.
  string title = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Catalog item description. UTF-8 encoded string with a length
  // limit of 5 KiB.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Highly encouraged. Extra catalog item attributes to be
  // included in the recommendation model. For example, for retail products,
  // this could include the store name, vendor, style, color, etc. These are
  // very strong signals for recommendation model, thus we highly recommend
  // providing the item attributes here.
  FeatureMap item_attributes = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Language of the title/description/item_attributes. Use language
  // tags defined by BCP 47. https://www.rfc-editor.org/rfc/bcp/bcp47.txt. Our
  // supported language codes include 'en', 'es', 'fr', 'de', 'ar', 'fa', 'zh',
  // 'ja', 'ko', 'sv', 'ro', 'nl'. For other languages, contact
  // your Google account manager.
  string language_code = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering tags associated with the catalog item. Each tag should
  // be a UTF-8 encoded string with a length limit of 1 KiB.
  //
  // This tag can be used for filtering recommendation results by passing the
  // tag as part of the predict request filter.
  repeated string tags = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Variant group identifier for prediction results. UTF-8 encoded
  // string with a length limit of 128 bytes.
  //
  // This field must be enabled before it can be used. [Learn
  // more](/recommendations-ai/docs/catalog#item-group-id).
  string item_group_id = 9 [(google.api.field_behavior) = OPTIONAL];

  // Extra catalog item metadata for different recommendation types.
  oneof recommendation_type {
    // Optional. Metadata specific to retail products.
    ProductCatalogItem product_metadata = 10 [(google.api.field_behavior) = OPTIONAL];
  }
}

// ProductCatalogItem captures item metadata specific to retail products.
message ProductCatalogItem {
  // Exact product price.
  message ExactPrice {
    // Optional. Display price of the product.
    float display_price = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Price of the product without any discount. If zero, by default
    // set to be the 'displayPrice'.
    float original_price = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Product price range when there are a range of prices for different
  // variations of the same product.
  message PriceRange {
    // Required. The minimum product price.
    float min = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The maximum product price.
    float max = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Item stock state. If this field is unspecified, the item is
  // assumed to be in stock.
  enum StockState {
    option allow_alias = true;

    // Default item stock status. Should never be used.
    STOCK_STATE_UNSPECIFIED = 0;

    // Item in stock.
    IN_STOCK = 0;

    // Item out of stock.
    OUT_OF_STOCK = 1;

    // Item that is in pre-order state.
    PREORDER = 2;

    // Item that is back-ordered (i.e. temporarily out of stock).
    BACKORDER = 3;
  }

  // Product price. Only one of 'exactPrice'/'priceRange' can be provided.
  oneof price {
    // Optional. The exact product price.
    ExactPrice exact_price = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The product price range.
    PriceRange price_range = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. A map to pass the costs associated with the product.
  //
  // For example:
  // {"manufacturing": 45.5} The profit of selling this item is computed like
  // so:
  //
  // * If 'exactPrice' is provided, profit = displayPrice - sum(costs)
  // * If 'priceRange' is provided, profit = minPrice - sum(costs)
  map<string, float> costs = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only required if the price is set. Currency code for price/costs. Use
  // three-character ISO-4217 code.
  string currency_code = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Online stock state of the catalog item. Default is `IN_STOCK`.
  StockState stock_state = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The available quantity of the item.
  int64 available_quantity = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Canonical URL directly linking to the item detail page with a
  // length limit of 5 KiB..
  string canonical_product_uri = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Product images for the catalog item.
  repeated Image images = 8 [(google.api.field_behavior) = OPTIONAL];
}

// Catalog item thumbnail/detail image.
message Image {
  // Required. URL of the image with a length limit of 5 KiB.
  string uri = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Height of the image in number of pixels.
  int32 height = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Width of the image in number of pixels.
  int32 width = 3 [(google.api.field_behavior) = OPTIONAL];
}
