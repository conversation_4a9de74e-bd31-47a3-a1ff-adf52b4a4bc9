{"methodConfig": [{"name": [{"service": "google.cloud.recommendationengine.v1beta1.CatalogService", "method": "CreateCatalogItem"}, {"service": "google.cloud.recommendationengine.v1beta1.CatalogService", "method": "GetCatalogItem"}, {"service": "google.cloud.recommendationengine.v1beta1.CatalogService", "method": "ListCatalogItems"}, {"service": "google.cloud.recommendationengine.v1beta1.CatalogService", "method": "UpdateCatalogItem"}, {"service": "google.cloud.recommendationengine.v1beta1.CatalogService", "method": "DeleteCatalogItem"}, {"service": "google.cloud.recommendationengine.v1beta1.CatalogService", "method": "ImportCatalogItems"}, {"service": "google.cloud.recommendationengine.v1beta1.PredictionApiKeyRegistry", "method": "CreatePredictionApiKeyRegistration"}, {"service": "google.cloud.recommendationengine.v1beta1.PredictionApiKeyRegistry", "method": "ListPredictionApiKeyRegistrations"}, {"service": "google.cloud.recommendationengine.v1beta1.PredictionApiKeyRegistry", "method": "DeletePredictionApiKeyRegistration"}, {"service": "google.cloud.recommendationengine.v1beta1.PredictionService", "method": "Predict"}, {"service": "google.cloud.recommendationengine.v1beta1.UserEventService", "method": "WriteUserEvent"}, {"service": "google.cloud.recommendationengine.v1beta1.UserEventService", "method": "CollectUserEvent"}, {"service": "google.cloud.recommendationengine.v1beta1.UserEventService", "method": "ListUserEvents"}, {"service": "google.cloud.recommendationengine.v1beta1.UserEventService", "method": "PurgeUserEvents"}, {"service": "google.cloud.recommendationengine.v1beta1.UserEventService", "method": "ImportUserEvents"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}