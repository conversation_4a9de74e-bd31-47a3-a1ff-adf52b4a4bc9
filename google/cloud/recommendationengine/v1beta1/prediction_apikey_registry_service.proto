// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommendationengine.v1beta1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/api/client.proto";
import "google/cloud/recommendationengine/v1beta1/recommendationengine_resources.proto";

option csharp_namespace = "Google.Cloud.RecommendationEngine.V1Beta1";
option go_package = "cloud.google.com/go/recommendationengine/apiv1beta1/recommendationenginepb;recommendationenginepb";
option java_multiple_files = true;
option java_package = "com.google.cloud.recommendationengine.v1beta1";
option objc_class_prefix = "RECAI";
option php_namespace = "Google\\Cloud\\RecommendationEngine\\V1beta1";
option ruby_package = "Google::Cloud::RecommendationEngine::V1beta1";

// Service for registering API keys for use with the `predict` method. If you
// use an API key to request predictions, you must first register the API key.
// Otherwise, your prediction request is rejected. If you use OAuth to
// authenticate your `predict` method call, you do not need to register an API
// key. You can register up to 20 API keys per project.
service PredictionApiKeyRegistry {
  option (google.api.default_host) = "recommendationengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Register an API key for use with predict method.
  rpc CreatePredictionApiKeyRegistration(
      CreatePredictionApiKeyRegistrationRequest)
      returns (PredictionApiKeyRegistration) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/catalogs/*/eventStores/*}/predictionApiKeyRegistrations"
      body: "*"
    };
    option (google.api.method_signature) =
        "parent,prediction_api_key_registration";
  }

  // List the registered apiKeys for use with predict method.
  rpc ListPredictionApiKeyRegistrations(
      ListPredictionApiKeyRegistrationsRequest)
      returns (ListPredictionApiKeyRegistrationsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/catalogs/*/eventStores/*}/predictionApiKeyRegistrations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Unregister an apiKey from using for predict method.
  rpc DeletePredictionApiKeyRegistration(
      DeletePredictionApiKeyRegistrationRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/catalogs/*/eventStores/*/predictionApiKeyRegistrations/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Registered Api Key.
message PredictionApiKeyRegistration {
  // The API key.
  string api_key = 1;
}

// Request message for the `CreatePredictionApiKeyRegistration` method.
message CreatePredictionApiKeyRegistrationRequest {
  // Required. The parent resource path.
  // `projects/*/locations/global/catalogs/default_catalog/eventStores/default_event_store`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/EventStore"
    }
  ];

  // Required. The prediction API key registration.
  PredictionApiKeyRegistration prediction_api_key_registration = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `ListPredictionApiKeyRegistrations`.
message ListPredictionApiKeyRegistrationsRequest {
  // Required. The parent placement resource name such as
  // `projects/1234/locations/global/catalogs/default_catalog/eventStores/default_event_store`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/EventStore"
    }
  ];

  // Optional. Maximum number of results to return per page. If unset, the
  // service will choose a reasonable default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The previous `ListPredictionApiKeyRegistration.nextPageToken`.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `ListPredictionApiKeyRegistrations`.
message ListPredictionApiKeyRegistrationsResponse {
  // The list of registered API keys.
  repeated PredictionApiKeyRegistration prediction_api_key_registrations = 1;

  // If empty, the list is complete. If nonempty, pass the token to the next
  // request's `ListPredictionApiKeysRegistrationsRequest.pageToken`.
  string next_page_token = 2;
}

// Request message for `DeletePredictionApiKeyRegistration` method.
message DeletePredictionApiKeyRegistrationRequest {
  // Required. The API key to unregister including full resource path.
  // `projects/*/locations/global/catalogs/default_catalog/eventStores/default_event_store/predictionApiKeyRegistrations/<YOUR_API_KEY>`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/PredictionApiKeyRegistration"
    }
  ];
}
