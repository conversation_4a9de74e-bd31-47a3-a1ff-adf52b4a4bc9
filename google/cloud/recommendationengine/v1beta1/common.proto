// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommendationengine.v1beta1;


option csharp_namespace = "Google.Cloud.RecommendationEngine.V1Beta1";
option go_package = "cloud.google.com/go/recommendationengine/apiv1beta1/recommendationenginepb;recommendationenginepb";
option java_multiple_files = true;
option java_package = "com.google.cloud.recommendationengine.v1beta1";
option objc_class_prefix = "RECAI";
option php_namespace = "Google\\Cloud\\RecommendationEngine\\V1beta1";
option ruby_package = "Google::Cloud::RecommendationEngine::V1beta1";

// FeatureMap represents extra features that customers want to include in the
// recommendation model for catalogs/user events as categorical/numerical
// features.
message FeatureMap {
  // A list of string features.
  message StringList {
    // String feature value with a length limit of 128 bytes.
    repeated string value = 1;
  }

  // A list of float features.
  message FloatList {
    // Float feature value.
    repeated float value = 1;
  }

  // Categorical features that can take on one of a limited number of possible
  // values. Some examples would be the brand/maker of a product, or country of
  // a customer.
  //
  // Feature names and values must be UTF-8 encoded strings.
  //
  // For example: `{ "colors": {"value": ["yellow", "green"]},
  //                 "sizes": {"value":["S", "M"]}`
  map<string, StringList> categorical_features = 1;

  // Numerical features. Some examples would be the height/weight of a product,
  // or age of a customer.
  //
  // Feature names must be UTF-8 encoded strings.
  //
  // For example: `{ "lengths_cm": {"value":[2.3, 15.4]},
  //                 "heights_cm": {"value":[8.1, 6.4]} }`
  map<string, FloatList> numerical_features = 2;
}
