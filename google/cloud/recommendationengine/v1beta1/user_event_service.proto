// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommendationengine.v1beta1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/api/httpbody.proto";
import "google/api/resource.proto";
import "google/cloud/recommendationengine/v1beta1/import.proto";
import "google/cloud/recommendationengine/v1beta1/user_event.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";
import "google/api/client.proto";

option csharp_namespace = "Google.Cloud.RecommendationEngine.V1Beta1";
option go_package = "cloud.google.com/go/recommendationengine/apiv1beta1/recommendationenginepb;recommendationenginepb";
option java_multiple_files = true;
option java_package = "com.google.cloud.recommendationengine.v1beta1";
option objc_class_prefix = "RECAI";
option php_namespace = "Google\\Cloud\\RecommendationEngine\\V1beta1";
option ruby_package = "Google::Cloud::RecommendationEngine::V1beta1";

// Service for ingesting end user actions on the customer website.
service UserEventService {
  option (google.api.default_host) = "recommendationengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Writes a single user event.
  rpc WriteUserEvent(WriteUserEventRequest) returns (UserEvent) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/catalogs/*/eventStores/*}/userEvents:write"
      body: "user_event"
    };
    option (google.api.method_signature) = "parent,user_event";
  }

  // Writes a single user event from the browser. This uses a GET request to
  // due to browser restriction of POST-ing to a 3rd party domain.
  //
  // This method is used only by the Recommendations AI JavaScript pixel.
  // Users should not call this method directly.
  rpc CollectUserEvent(CollectUserEventRequest) returns (google.api.HttpBody) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/catalogs/*/eventStores/*}/userEvents:collect"
    };
    option (google.api.method_signature) = "parent,user_event,uri,ets";
  }

  // Gets a list of user events within a time range, with potential filtering.
  rpc ListUserEvents(ListUserEventsRequest) returns (ListUserEventsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/catalogs/*/eventStores/*}/userEvents"
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // Deletes permanently all user events specified by the filter provided.
  // Depending on the number of events specified by the filter, this operation
  // could take hours or days to complete. To test a filter, use the list
  // command first.
  rpc PurgeUserEvents(PurgeUserEventsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/catalogs/*/eventStores/*}/userEvents:purge"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.recommendationengine.v1beta1.PurgeUserEventsResponse"
      metadata_type: "google.cloud.recommendationengine.v1beta1.PurgeUserEventsMetadata"
    };
    option (google.api.method_signature) = "parent,filter,force";
  }

  // Bulk import of User events. Request processing might be
  // synchronous. Events that already exist are skipped.
  // Use this method for backfilling historical user events.
  //
  // Operation.response is of type ImportResponse. Note that it is
  // possible for a subset of the items to be successfully inserted.
  // Operation.metadata is of type ImportMetadata.
  rpc ImportUserEvents(ImportUserEventsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/catalogs/*/eventStores/*}/userEvents:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.recommendationengine.v1beta1.ImportUserEventsResponse"
      metadata_type: "google.cloud.recommendationengine.v1beta1.ImportMetadata"
    };
    option (google.api.method_signature) =
        "parent,request_id,input_config,errors_config";
  }
}

// Request message for PurgeUserEvents method.
message PurgeUserEventsRequest {
  // Required. The resource name of the event_store under which the events are
  // created. The format is
  // `projects/${projectId}/locations/global/catalogs/${catalogId}/eventStores/${eventStoreId}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/EventStore"
    }
  ];

  // Required. The filter string to specify the events to be deleted. Empty
  // string filter is not allowed. This filter can also be used with
  // ListUserEvents API to list events that will be deleted. The eligible fields
  // for filtering are:
  // * eventType - UserEvent.eventType field of type string.
  // * eventTime - in ISO 8601 "zulu" format.
  // * visitorId - field of type string. Specifying this will delete all events
  // associated with a visitor.
  // * userId - field of type string. Specifying this will delete all events
  // associated with a user.
  // Example 1: Deleting all events in a time range.
  // `eventTime > "2012-04-23T18:25:43.511Z" eventTime <
  // "2012-04-23T18:30:43.511Z"`
  // Example 2: Deleting specific eventType in time range.
  // `eventTime > "2012-04-23T18:25:43.511Z" eventType = "detail-page-view"`
  // Example 3: Deleting all events for a specific visitor
  // `visitorId = visitor1024`
  // The filtering fields are assumed to have an implicit AND.
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The default value is false. Override this flag to true to
  // actually perform the purge. If the field is not set to true, a sampling of
  // events to be deleted will be returned.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Metadata related to the progress of the PurgeUserEvents operation.
// This will be returned by the google.longrunning.Operation.metadata field.
message PurgeUserEventsMetadata {
  // The ID of the request / operation.
  string operation_name = 1;

  // Operation create time.
  google.protobuf.Timestamp create_time = 2;
}

// Response of the PurgeUserEventsRequest. If the long running operation is
// successfully done, then this message is returned by the
// google.longrunning.Operations.response field.
message PurgeUserEventsResponse {
  // The total count of events purged as a result of the operation.
  int64 purged_events_count = 1;

  // A sampling of events deleted (or will be deleted) depending on the `force`
  // property in the request. Max of 500 items will be returned.
  repeated UserEvent user_events_sample = 2;
}

// Request message for WriteUserEvent method.
message WriteUserEventRequest {
  // Required. The parent eventStore resource name, such as
  // `projects/1234/locations/global/catalogs/default_catalog/eventStores/default_event_store`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/EventStore"
    }
  ];

  // Required. User event to write.
  UserEvent user_event = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for CollectUserEvent method.
message CollectUserEventRequest {
  // Required. The parent eventStore name, such as
  // `projects/1234/locations/global/catalogs/default_catalog/eventStores/default_event_store`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/EventStore"
    }
  ];

  // Required. URL encoded UserEvent proto.
  string user_event = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The url including cgi-parameters but excluding the hash fragment.
  // The URL must be truncated to 1.5K bytes to conservatively be under the 2K
  // bytes. This is often more useful than the referer url, because many
  // browsers only send the domain for 3rd party requests.
  string uri = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The event timestamp in milliseconds. This prevents browser
  // caching of otherwise identical get requests. The name is abbreviated to
  // reduce the payload bytes.
  int64 ets = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for ListUserEvents method.
message ListUserEventsRequest {
  // Required. The parent eventStore resource name, such as
  // `projects/*/locations/*/catalogs/default_catalog/eventStores/default_event_store`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/EventStore"
    }
  ];

  // Optional. Maximum number of results to return per page. If zero, the
  // service will choose a reasonable default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The previous ListUserEventsResponse.next_page_token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering expression to specify restrictions over
  // returned events. This is a sequence of terms, where each term applies some
  // kind of a restriction to the returned user events. Use this expression to
  // restrict results to a specific time range, or filter events by eventType.
  //    eg: eventTime > "2012-04-23T18:25:43.511Z" eventsMissingCatalogItems
  //    eventTime<"2012-04-23T18:25:43.511Z" eventType=search
  //
  //   We expect only 3 types of fields:
  //
  //    * eventTime: this can be specified a maximum of 2 times, once with a
  //      less than operator and once with a greater than operator. The
  //      eventTime restrict should result in one contiguous valid eventTime
  //      range.
  //
  //    * eventType: only 1 eventType restriction can be specified.
  //
  //    * eventsMissingCatalogItems: specififying this will restrict results
  //      to events for which catalog items were not found in the catalog. The
  //      default behavior is to return only those events for which catalog
  //      items were found.
  //
  //   Some examples of valid filters expressions:
  //
  //   * Example 1: eventTime > "2012-04-23T18:25:43.511Z"
  //             eventTime < "2012-04-23T18:30:43.511Z"
  //   * Example 2: eventTime > "2012-04-23T18:25:43.511Z"
  //             eventType = detail-page-view
  //   * Example 3: eventsMissingCatalogItems
  //             eventType = search eventTime < "2018-04-23T18:30:43.511Z"
  //   * Example 4: eventTime > "2012-04-23T18:25:43.511Z"
  //   * Example 5: eventType = search
  //   * Example 6: eventsMissingCatalogItems
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListUserEvents method.
message ListUserEventsResponse {
  // The user events.
  repeated UserEvent user_events = 1;

  // If empty, the list is complete. If nonempty, the token to pass to the next
  // request's ListUserEvents.page_token.
  string next_page_token = 2;
}
