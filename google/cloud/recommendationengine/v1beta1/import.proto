// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.recommendationengine.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/recommendationengine/v1beta1/catalog.proto";
import "google/cloud/recommendationengine/v1beta1/user_event.proto";
import "google/cloud/recommendationengine/v1beta1/recommendationengine_resources.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.RecommendationEngine.V1Beta1";
option go_package = "cloud.google.com/go/recommendationengine/apiv1beta1/recommendationenginepb;recommendationenginepb";
option java_multiple_files = true;
option java_package = "com.google.cloud.recommendationengine.v1beta1";
option objc_class_prefix = "RECAI";
option php_namespace = "Google\\Cloud\\RecommendationEngine\\V1beta1";
option ruby_package = "Google::Cloud::RecommendationEngine::V1beta1";

// Google Cloud Storage location for input content.
// format.
message GcsSource {
  // Required. Google Cloud Storage URIs to input files. URI can be up to
  // 2000 characters long. URIs can match the full object path (for example,
  // `gs://bucket/directory/object.json`) or a pattern matching one or more
  // files, such as `gs://bucket/directory/*.json`. A request can
  // contain at most 100 files, and each file can be up to 2 GB. See
  // [Importing catalog information](/recommendations-ai/docs/upload-catalog)
  // for the expected file format and setup instructions.
  repeated string input_uris = 1 [(google.api.field_behavior) = REQUIRED];
}

// The inline source for the input config for ImportCatalogItems method.
message CatalogInlineSource {
  // Optional. A list of catalog items to update/create. Recommended max of 10k
  // items.
  repeated CatalogItem catalog_items = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// The inline source for the input config for ImportUserEvents method.
message UserEventInlineSource {
  // Optional. A list of user events to import. Recommended max of 10k items.
  repeated UserEvent user_events = 1 [(google.api.field_behavior) = OPTIONAL];
}

// Configuration of destination for Import related errors.
message ImportErrorsConfig {
  // Required. Errors destination.
  oneof destination {
    // Google Cloud Storage path for import errors. This must be an empty,
    // existing Cloud Storage bucket. Import errors will be written to a file in
    // this bucket, one per line, as a JSON-encoded
    // `google.rpc.Status` message.
    string gcs_prefix = 1;
  }
}

// Request message for Import methods.
message ImportCatalogItemsRequest {
  // Required. `projects/1234/locations/global/catalogs/default_catalog`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/Catalog"
    }
  ];

  // Optional. Unique identifier provided by client, within the ancestor
  // dataset scope. Ensures idempotency and used for request deduplication.
  // Server-generated if unspecified. Up to 128 characters long. This is
  // returned as google.longrunning.Operation.name in the response.
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The desired input location of the data.
  InputConfig input_config = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The desired location of errors incurred during the Import.
  ImportErrorsConfig errors_config = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the ImportUserEvents request.
message ImportUserEventsRequest {
  // Required.
  // `projects/1234/locations/global/catalogs/default_catalog/eventStores/default_event_store`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "recommendationengine.googleapis.com/EventStore"
    }
  ];

  // Optional. Unique identifier provided by client, within the ancestor
  // dataset scope. Ensures idempotency for expensive long running operations.
  // Server-generated if unspecified. Up to 128 characters long. This is
  // returned as google.longrunning.Operation.name in the response. Note that
  // this field must not be set if the desired input config is
  // catalog_inline_source.
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The desired input location of the data.
  InputConfig input_config = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The desired location of errors incurred during the Import.
  ImportErrorsConfig errors_config = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The input config source.
message InputConfig {
  // Required. The source of the input.
  oneof source {
    // The Inline source for the input content for Catalog items.
    CatalogInlineSource catalog_inline_source = 1;

    // Google Cloud Storage location for the input content.
    GcsSource gcs_source = 2;

    // The Inline source for the input content for UserEvents.
    UserEventInlineSource user_event_inline_source = 3;
  }
}

// Metadata related to the progress of the Import operation. This will be
// returned by the google.longrunning.Operation.metadata field.
message ImportMetadata {
  // Name of the operation.
  string operation_name = 5;

  // Id of the request / operation. This is parroting back the requestId that
  // was passed in the request.
  string request_id = 3;

  // Operation create time.
  google.protobuf.Timestamp create_time = 4;

  // Count of entries that were processed successfully.
  int64 success_count = 1;

  // Count of entries that encountered errors while processing.
  int64 failure_count = 2;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 6;
}

// Response of the ImportCatalogItemsRequest. If the long running
// operation is done, then this message is returned by the
// google.longrunning.Operations.response field if the operation was successful.
message ImportCatalogItemsResponse {
  // A sample of errors encountered while processing the request.
  repeated google.rpc.Status error_samples = 1;

  // Echoes the destination for the complete errors in the request if set.
  ImportErrorsConfig errors_config = 2;
}

// Response of the ImportUserEventsRequest. If the long running
// operation was successful, then this message is returned by the
// google.longrunning.Operations.response field if the operation was successful.
message ImportUserEventsResponse {
  // A sample of errors encountered while processing the request.
  repeated google.rpc.Status error_samples = 1;

  // Echoes the destination for the complete errors if this field was set in
  // the request.
  ImportErrorsConfig errors_config = 2;

  // Aggregated statistics of user event import status.
  UserEventImportSummary import_summary = 3;
}

// A summary of import result. The UserEventImportSummary summarizes
// the import status for user events.
message UserEventImportSummary {
  // Count of user events imported with complete existing catalog information.
  int64 joined_events_count = 1;

  // Count of user events imported, but with catalog information not found
  // in the imported catalog.
  int64 unjoined_events_count = 2;
}
