# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "recommendationengine_proto",
    srcs = [
        "catalog.proto",
        "catalog_service.proto",
        "common.proto",
        "import.proto",
        "prediction_apikey_registry_service.proto",
        "prediction_service.proto",
        "recommendationengine_resources.proto",
        "user_event.proto",
        "user_event_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:httpbody_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "recommendationengine_proto_with_info",
    deps = [
        ":recommendationengine_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "recommendationengine_java_proto",
    deps = [
        ":recommendationengine_proto",
    ],
)

java_grpc_library(
    name = "recommendationengine_java_grpc",
    srcs = [":recommendationengine_proto"],
    deps = [":recommendationengine_java_proto"],
)

java_gapic_library(
    name = "recommendationengine_java_gapic",
    srcs = [":recommendationengine_proto_with_info"],
    gapic_yaml = "recommendationengine_gapic.yaml",
    grpc_service_config = "recommendationengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "recommendationengine_v1beta1.yaml",
    test_deps = [
        ":recommendationengine_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":recommendationengine_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "recommendationengine_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.recommendationengine.v1beta1.CatalogServiceClientHttpJsonTest",
        "com.google.cloud.recommendationengine.v1beta1.CatalogServiceClientTest",
        "com.google.cloud.recommendationengine.v1beta1.PredictionApiKeyRegistryClientHttpJsonTest",
        "com.google.cloud.recommendationengine.v1beta1.PredictionApiKeyRegistryClientTest",
        "com.google.cloud.recommendationengine.v1beta1.PredictionServiceClientHttpJsonTest",
        "com.google.cloud.recommendationengine.v1beta1.PredictionServiceClientTest",
        "com.google.cloud.recommendationengine.v1beta1.UserEventServiceClientHttpJsonTest",
        "com.google.cloud.recommendationengine.v1beta1.UserEventServiceClientTest",
    ],
    runtime_deps = [":recommendationengine_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-recommendationengine-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":recommendationengine_java_gapic",
        ":recommendationengine_java_grpc",
        ":recommendationengine_java_proto",
        ":recommendationengine_proto",
    ],
)

go_proto_library(
    name = "recommendationengine_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/recommendationengine/apiv1beta1/recommendationenginepb",
    protos = [":recommendationengine_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "recommendationengine_go_gapic",
    srcs = [":recommendationengine_proto_with_info"],
    grpc_service_config = "recommendationengine_grpc_service_config.json",
    importpath = "cloud.google.com/go/recommendationengine/apiv1beta1;recommendationengine",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "recommendationengine_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":recommendationengine_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-recommendationengine-v1beta1-go",
    deps = [
        ":recommendationengine_go_gapic",
        ":recommendationengine_go_gapic_srcjar-snippets.srcjar",
        ":recommendationengine_go_gapic_srcjar-test.srcjar",
        ":recommendationengine_go_proto",
    ],
)

py_gapic_library(
    name = "recommendationengine_py_gapic",
    srcs = [":recommendationengine_proto"],
    grpc_service_config = "recommendationengine_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-recommendations-ai"],
    rest_numeric_enums = True,
    service_yaml = "recommendationengine_v1beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "recommendationengine_py_gapic_test",
    srcs = [
        "recommendationengine_py_gapic_pytest.py",
        "recommendationengine_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":recommendationengine_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "recommendationengine-v1beta1-py",
    deps = [
        ":recommendationengine_py_gapic",
    ],
)

php_proto_library(
    name = "recommendationengine_php_proto",
    deps = [":recommendationengine_proto"],
)

php_gapic_library(
    name = "recommendationengine_php_gapic",
    srcs = [":recommendationengine_proto_with_info"],
    gapic_yaml = "recommendationengine_gapic.yaml",
    grpc_service_config = "recommendationengine_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "recommendationengine_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":recommendationengine_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-recommendationengine-v1beta1-php",
    deps = [
        ":recommendationengine_php_gapic",
        ":recommendationengine_php_proto",
    ],
)

nodejs_gapic_library(
    name = "recommendationengine_nodejs_gapic",
    src = ":recommendationengine_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "recommendationengine_grpc_service_config.json",
    package = "google.cloud.recommendationengine.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "recommendationengine_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "recommendationengine-v1beta1-nodejs",
    deps = [
        ":recommendationengine_nodejs_gapic",
        ":recommendationengine_proto",
    ],
)

ruby_proto_library(
    name = "recommendationengine_ruby_proto",
    deps = [":recommendationengine_proto"],
)

ruby_grpc_library(
    name = "recommendationengine_ruby_grpc",
    srcs = [":recommendationengine_proto"],
    deps = [":recommendationengine_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "recommendationengine_ruby_gapic",
    srcs = [":recommendationengine_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-recommendation_engine-v1beta1",
        "ruby-cloud-env-prefix=RECOMMENDATION_ENGINE",
        "ruby-cloud-product-url=https://cloud.google.com/recommendations",
        "ruby-cloud-api-id=recommendationengine.googleapis.com",
        "ruby-cloud-api-shortname=recommendationengine",
    ],
    grpc_service_config = "recommendationengine_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Recommendations AI enables you to build an end-to-end personalized recommendation system based on state-of-the-art deep learning ML models, without a need for expertise in ML or recommendation systems.",
    ruby_cloud_title = "Recommendations AI V1beta1",
    service_yaml = "recommendationengine_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":recommendationengine_ruby_grpc",
        ":recommendationengine_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-recommendationengine-v1beta1-ruby",
    deps = [
        ":recommendationengine_ruby_gapic",
        ":recommendationengine_ruby_grpc",
        ":recommendationengine_ruby_proto",
    ],
)

csharp_proto_library(
    name = "recommendationengine_csharp_proto",
    deps = [":recommendationengine_proto"],
)

csharp_grpc_library(
    name = "recommendationengine_csharp_grpc",
    srcs = [":recommendationengine_proto"],
    deps = [":recommendationengine_csharp_proto"],
)

csharp_gapic_library(
    name = "recommendationengine_csharp_gapic",
    srcs = [":recommendationengine_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "recommendationengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "recommendationengine_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":recommendationengine_csharp_grpc",
        ":recommendationengine_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-recommendationengine-v1beta1-csharp",
    deps = [
        ":recommendationengine_csharp_gapic",
        ":recommendationengine_csharp_grpc",
        ":recommendationengine_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
