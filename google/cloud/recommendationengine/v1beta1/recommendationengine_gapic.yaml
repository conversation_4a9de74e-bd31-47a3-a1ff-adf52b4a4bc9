type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
# A list of API interface configurations.
interfaces:
# The fully qualified name of the API interface.
- name: google.cloud.recommendationengine.v1beta1.CatalogService
  methods:
  - name: ImportCatalogItems
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 300000

- name: google.cloud.recommendationengine.v1beta1.UserEventService
  methods:
  - name: PurgeUserEvents
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 300000
  - name: ImportUserEvents
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 5000
      total_poll_timeout_millis: 300000
