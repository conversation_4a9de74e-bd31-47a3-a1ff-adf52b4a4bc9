// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkservices.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkServices.V1";
option go_package = "cloud.google.com/go/networkservices/apiv1/networkservicespb;networkservicespb";
option java_multiple_files = true;
option java_outer_classname = "MeshProto";
option java_package = "com.google.cloud.networkservices.v1";
option php_namespace = "Google\\Cloud\\NetworkServices\\V1";
option ruby_package = "Google::Cloud::NetworkServices::V1";

// Mesh represents a logical configuration grouping for workload to workload
// communication within a service mesh. Routes that point to mesh dictate how
// requests are routed within this logical mesh boundary.
message Mesh {
  option (google.api.resource) = {
    type: "networkservices.googleapis.com/Mesh"
    pattern: "projects/{project}/locations/{location}/meshes/{mesh}"
  };

  // Required. Name of the Mesh resource. It matches pattern
  // `projects/*/locations/global/meshes/<mesh_name>`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Server-defined URL of this resource
  string self_link = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Set of label tags associated with the Mesh resource.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A free-text description of the resource. Max length 1024
  // characters.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to a valid TCP port (1-65535), instructs the SIDECAR proxy
  // to listen on the specified port of localhost (127.0.0.1) address. The
  // SIDECAR proxy will expect all traffic to be redirected to this port
  // regardless of its actual ip:port destination. If unset, a port '15001' is
  // used as the interception port. This is applicable only for sidecar proxy
  // deployments.
  int32 interception_port = 8 [(google.api.field_behavior) = OPTIONAL];
}

// Request used with the ListMeshes method.
message ListMeshesRequest {
  // Required. The project and location from which the Meshes should be
  // listed, specified in the format `projects/*/locations/global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/Mesh"
    }
  ];

  // Maximum number of Meshes to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListMeshesResponse`
  // Indicates that this is a continuation of a prior `ListMeshes` call,
  // and that the system should return the next page of data.
  string page_token = 3;
}

// Response returned by the ListMeshes method.
message ListMeshesResponse {
  // List of Mesh resources.
  repeated Mesh meshes = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Request used by the GetMesh method.
message GetMeshRequest {
  // Required. A name of the Mesh to get. Must be in the format
  // `projects/*/locations/global/meshes/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/Mesh"
    }
  ];
}

// Request used by the CreateMesh method.
message CreateMeshRequest {
  // Required. The parent resource of the Mesh. Must be in the
  // format `projects/*/locations/global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/Mesh"
    }
  ];

  // Required. Short name of the Mesh resource to be created.
  string mesh_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Mesh resource to be created.
  Mesh mesh = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the UpdateMesh method.
message UpdateMeshRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // Mesh resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Updated Mesh resource.
  Mesh mesh = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the DeleteMesh method.
message DeleteMeshRequest {
  // Required. A name of the Mesh to delete. Must be in the format
  // `projects/*/locations/global/meshes/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/Mesh"
    }
  ];
}
