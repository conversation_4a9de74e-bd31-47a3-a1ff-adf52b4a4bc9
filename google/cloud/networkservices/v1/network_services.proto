// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkservices.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/networkservices/v1/endpoint_policy.proto";
import "google/cloud/networkservices/v1/gateway.proto";
import "google/cloud/networkservices/v1/grpc_route.proto";
import "google/cloud/networkservices/v1/http_route.proto";
import "google/cloud/networkservices/v1/mesh.proto";
import "google/cloud/networkservices/v1/service_binding.proto";
import "google/cloud/networkservices/v1/tcp_route.proto";
import "google/cloud/networkservices/v1/tls_route.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.NetworkServices.V1";
option go_package = "cloud.google.com/go/networkservices/apiv1/networkservicespb;networkservicespb";
option java_multiple_files = true;
option java_package = "com.google.cloud.networkservices.v1";
option php_namespace = "Google\\Cloud\\NetworkServices\\V1";
option ruby_package = "Google::Cloud::NetworkServices::V1";

// Service describing handlers for resources.
service NetworkServices {
  option (google.api.default_host) = "networkservices.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists EndpointPolicies in a given project and location.
  rpc ListEndpointPolicies(ListEndpointPoliciesRequest)
      returns (ListEndpointPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/endpointPolicies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single EndpointPolicy.
  rpc GetEndpointPolicy(GetEndpointPolicyRequest) returns (EndpointPolicy) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/endpointPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new EndpointPolicy in a given project and location.
  rpc CreateEndpointPolicy(CreateEndpointPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/endpointPolicies"
      body: "endpoint_policy"
    };
    option (google.api.method_signature) =
        "parent,endpoint_policy,endpoint_policy_id";
    option (google.longrunning.operation_info) = {
      response_type: "EndpointPolicy"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Updates the parameters of a single EndpointPolicy.
  rpc UpdateEndpointPolicy(UpdateEndpointPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{endpoint_policy.name=projects/*/locations/*/endpointPolicies/*}"
      body: "endpoint_policy"
    };
    option (google.api.method_signature) = "endpoint_policy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "EndpointPolicy"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Deletes a single EndpointPolicy.
  rpc DeleteEndpointPolicy(DeleteEndpointPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/endpointPolicies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Lists Gateways in a given project and location.
  rpc ListGateways(ListGatewaysRequest) returns (ListGatewaysResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/gateways"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Gateway.
  rpc GetGateway(GetGatewayRequest) returns (Gateway) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/gateways/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Gateway in a given project and location.
  rpc CreateGateway(CreateGatewayRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/gateways"
      body: "gateway"
    };
    option (google.api.method_signature) = "parent,gateway,gateway_id";
    option (google.longrunning.operation_info) = {
      response_type: "Gateway"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Updates the parameters of a single Gateway.
  rpc UpdateGateway(UpdateGatewayRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{gateway.name=projects/*/locations/*/gateways/*}"
      body: "gateway"
    };
    option (google.api.method_signature) = "gateway,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Gateway"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Deletes a single Gateway.
  rpc DeleteGateway(DeleteGatewayRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/gateways/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Lists GrpcRoutes in a given project and location.
  rpc ListGrpcRoutes(ListGrpcRoutesRequest) returns (ListGrpcRoutesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/grpcRoutes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single GrpcRoute.
  rpc GetGrpcRoute(GetGrpcRouteRequest) returns (GrpcRoute) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/grpcRoutes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new GrpcRoute in a given project and location.
  rpc CreateGrpcRoute(CreateGrpcRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/grpcRoutes"
      body: "grpc_route"
    };
    option (google.api.method_signature) = "parent,grpc_route,grpc_route_id";
    option (google.longrunning.operation_info) = {
      response_type: "GrpcRoute"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Updates the parameters of a single GrpcRoute.
  rpc UpdateGrpcRoute(UpdateGrpcRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{grpc_route.name=projects/*/locations/*/grpcRoutes/*}"
      body: "grpc_route"
    };
    option (google.api.method_signature) = "grpc_route,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "GrpcRoute"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Deletes a single GrpcRoute.
  rpc DeleteGrpcRoute(DeleteGrpcRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/grpcRoutes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Lists HttpRoute in a given project and location.
  rpc ListHttpRoutes(ListHttpRoutesRequest) returns (ListHttpRoutesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/httpRoutes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single HttpRoute.
  rpc GetHttpRoute(GetHttpRouteRequest) returns (HttpRoute) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/httpRoutes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new HttpRoute in a given project and location.
  rpc CreateHttpRoute(CreateHttpRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/httpRoutes"
      body: "http_route"
    };
    option (google.api.method_signature) = "parent,http_route,http_route_id";
    option (google.longrunning.operation_info) = {
      response_type: "HttpRoute"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Updates the parameters of a single HttpRoute.
  rpc UpdateHttpRoute(UpdateHttpRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{http_route.name=projects/*/locations/*/httpRoutes/*}"
      body: "http_route"
    };
    option (google.api.method_signature) = "http_route,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "HttpRoute"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Deletes a single HttpRoute.
  rpc DeleteHttpRoute(DeleteHttpRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/httpRoutes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Lists TcpRoute in a given project and location.
  rpc ListTcpRoutes(ListTcpRoutesRequest) returns (ListTcpRoutesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/tcpRoutes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single TcpRoute.
  rpc GetTcpRoute(GetTcpRouteRequest) returns (TcpRoute) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/tcpRoutes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new TcpRoute in a given project and location.
  rpc CreateTcpRoute(CreateTcpRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/tcpRoutes"
      body: "tcp_route"
    };
    option (google.api.method_signature) = "parent,tcp_route,tcp_route_id";
    option (google.longrunning.operation_info) = {
      response_type: "TcpRoute"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Updates the parameters of a single TcpRoute.
  rpc UpdateTcpRoute(UpdateTcpRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{tcp_route.name=projects/*/locations/*/tcpRoutes/*}"
      body: "tcp_route"
    };
    option (google.api.method_signature) = "tcp_route,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "TcpRoute"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Deletes a single TcpRoute.
  rpc DeleteTcpRoute(DeleteTcpRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/tcpRoutes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Lists TlsRoute in a given project and location.
  rpc ListTlsRoutes(ListTlsRoutesRequest) returns (ListTlsRoutesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/tlsRoutes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single TlsRoute.
  rpc GetTlsRoute(GetTlsRouteRequest) returns (TlsRoute) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/tlsRoutes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new TlsRoute in a given project and location.
  rpc CreateTlsRoute(CreateTlsRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/tlsRoutes"
      body: "tls_route"
    };
    option (google.api.method_signature) = "parent,tls_route,tls_route_id";
    option (google.longrunning.operation_info) = {
      response_type: "TlsRoute"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Updates the parameters of a single TlsRoute.
  rpc UpdateTlsRoute(UpdateTlsRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{tls_route.name=projects/*/locations/*/tlsRoutes/*}"
      body: "tls_route"
    };
    option (google.api.method_signature) = "tls_route,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "TlsRoute"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Deletes a single TlsRoute.
  rpc DeleteTlsRoute(DeleteTlsRouteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/tlsRoutes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Lists ServiceBinding in a given project and location.
  rpc ListServiceBindings(ListServiceBindingsRequest)
      returns (ListServiceBindingsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/serviceBindings"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ServiceBinding.
  rpc GetServiceBinding(GetServiceBindingRequest) returns (ServiceBinding) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/serviceBindings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new ServiceBinding in a given project and location.
  rpc CreateServiceBinding(CreateServiceBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/serviceBindings"
      body: "service_binding"
    };
    option (google.api.method_signature) =
        "parent,service_binding,service_binding_id";
    option (google.longrunning.operation_info) = {
      response_type: "ServiceBinding"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Deletes a single ServiceBinding.
  rpc DeleteServiceBinding(DeleteServiceBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/serviceBindings/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Lists Meshes in a given project and location.
  rpc ListMeshes(ListMeshesRequest) returns (ListMeshesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/meshes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Mesh.
  rpc GetMesh(GetMeshRequest) returns (Mesh) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/meshes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Mesh in a given project and location.
  rpc CreateMesh(CreateMeshRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/meshes"
      body: "mesh"
    };
    option (google.api.method_signature) = "parent,mesh,mesh_id";
    option (google.longrunning.operation_info) = {
      response_type: "Mesh"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Updates the parameters of a single Mesh.
  rpc UpdateMesh(UpdateMeshRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{mesh.name=projects/*/locations/*/meshes/*}"
      body: "mesh"
    };
    option (google.api.method_signature) = "mesh,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Mesh"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }

  // Deletes a single Mesh.
  rpc DeleteMesh(DeleteMeshRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/meshes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networkservices.v1.OperationMetadata"
    };
  }
}
