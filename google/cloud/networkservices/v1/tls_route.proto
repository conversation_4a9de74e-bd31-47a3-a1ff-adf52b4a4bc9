// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkservices.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkServices.V1";
option go_package = "cloud.google.com/go/networkservices/apiv1/networkservicespb;networkservicespb";
option java_multiple_files = true;
option java_outer_classname = "TlsRouteProto";
option java_package = "com.google.cloud.networkservices.v1";
option php_namespace = "Google\\Cloud\\NetworkServices\\V1";
option ruby_package = "Google::Cloud::NetworkServices::V1";

// TlsRoute defines how traffic should be routed based on SNI and other matching
// L3 attributes.
message TlsRoute {
  option (google.api.resource) = {
    type: "networkservices.googleapis.com/TlsRoute"
    pattern: "projects/{project}/locations/{location}/tlsRoutes/{tls_route}"
  };

  // Specifies how to match traffic and how to route traffic when traffic is
  // matched.
  message RouteRule {
    // Required. RouteMatch defines the predicate used to match requests to a
    // given action. Multiple match types are "OR"ed for evaluation.
    repeated RouteMatch matches = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The detailed rule defining how to route matched traffic.
    RouteAction action = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // RouteMatch defines the predicate used to match requests to a given action.
  // Multiple match types are "AND"ed for evaluation.
  // If no routeMatch field is specified, this rule will unconditionally match
  // traffic.
  message RouteMatch {
    // Optional. SNI (server name indicator) to match against.
    // SNI will be matched against all wildcard domains, i.e. `www.example.com`
    // will be first matched against `www.example.com`, then `*.example.com`,
    // then `*.com.`
    // Partial wildcards are not supported, and values like *w.example.com are
    // invalid.
    // At least one of sni_host and alpn is required.
    // Up to 5 sni hosts across all matches can be set.
    repeated string sni_host = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. ALPN (Application-Layer Protocol Negotiation) to match against.
    // Examples: "http/1.1", "h2".
    // At least one of sni_host and alpn is required.
    // Up to 5 alpns across all matches can be set.
    repeated string alpn = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // The specifications for routing traffic and applying associated policies.
  message RouteAction {
    // Required. The destination services to which traffic should be forwarded.
    // At least one destination service is required.
    repeated RouteDestination destinations = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // Describe the destination for traffic to be routed to.
  message RouteDestination {
    // Required. The URL of a BackendService to route traffic to.
    string service_name = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "compute.googleapis.com/BackendService"
      }
    ];

    // Optional. Specifies the proportion of requests forwareded to the backend
    // referenced by the service_name field. This is computed as:
    // - weight/Sum(weights in destinations)
    // Weights in all destinations does not need to sum up to 100.
    int32 weight = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. Name of the TlsRoute resource. It matches pattern
  // `projects/*/locations/global/tlsRoutes/tls_route_name>`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Server-defined URL of this resource
  string self_link = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A free-text description of the resource. Max length 1024
  // characters.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. Rules that define how traffic is routed and handled. At least one
  // RouteRule must be supplied. If there are multiple rules then the action
  // taken will be the first rule to match.
  repeated RouteRule rules = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. Meshes defines a list of meshes this TlsRoute is attached to, as
  // one of the routing rules to route the requests served by the mesh.
  //
  // Each mesh reference should match the pattern:
  // `projects/*/locations/global/meshes/<mesh_name>`
  //
  // The attached Mesh should be of a type SIDECAR
  repeated string meshes = 6 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/Mesh"
    }
  ];

  // Optional. Gateways defines a list of gateways this TlsRoute is attached to,
  // as one of the routing rules to route the requests served by the gateway.
  //
  // Each gateway reference should match the pattern:
  // `projects/*/locations/global/gateways/<gateway_name>`
  repeated string gateways = 7 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/Gateway"
    }
  ];
}

// Request used with the ListTlsRoutes method.
message ListTlsRoutesRequest {
  // Required. The project and location from which the TlsRoutes should be
  // listed, specified in the format `projects/*/locations/global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/TlsRoute"
    }
  ];

  // Maximum number of TlsRoutes to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListTlsRoutesResponse`
  // Indicates that this is a continuation of a prior `ListTlsRoutes` call,
  // and that the system should return the next page of data.
  string page_token = 3;
}

// Response returned by the ListTlsRoutes method.
message ListTlsRoutesResponse {
  // List of TlsRoute resources.
  repeated TlsRoute tls_routes = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Request used by the GetTlsRoute method.
message GetTlsRouteRequest {
  // Required. A name of the TlsRoute to get. Must be in the format
  // `projects/*/locations/global/tlsRoutes/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/TlsRoute"
    }
  ];
}

// Request used by the TlsRoute method.
message CreateTlsRouteRequest {
  // Required. The parent resource of the TlsRoute. Must be in the
  // format `projects/*/locations/global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/TlsRoute"
    }
  ];

  // Required. Short name of the TlsRoute resource to be created.
  string tls_route_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. TlsRoute resource to be created.
  TlsRoute tls_route = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the UpdateTlsRoute method.
message UpdateTlsRouteRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // TlsRoute resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Updated TlsRoute resource.
  TlsRoute tls_route = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the DeleteTlsRoute method.
message DeleteTlsRouteRequest {
  // Required. A name of the TlsRoute to delete. Must be in the format
  // `projects/*/locations/global/tlsRoutes/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/TlsRoute"
    }
  ];
}
