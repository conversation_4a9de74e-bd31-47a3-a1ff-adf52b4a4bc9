// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkservices.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkServices.V1";
option go_package = "cloud.google.com/go/networkservices/apiv1/networkservicespb;networkservicespb";
option java_multiple_files = true;
option java_outer_classname = "GatewayProto";
option java_package = "com.google.cloud.networkservices.v1";
option php_namespace = "Google\\Cloud\\NetworkServices\\V1";
option ruby_package = "Google::Cloud::NetworkServices::V1";

// Gateway represents the configuration for a proxy, typically a load balancer.
// It captures the ip:port over which the services are exposed by the proxy,
// along with any policy configurations. Routes have reference to to Gateways to
// dictate how requests should be routed by this Gateway.
message Gateway {
  option (google.api.resource) = {
    type: "networkservices.googleapis.com/Gateway"
    pattern: "projects/{project}/locations/{location}/gateways/{gateway}"
  };

  // The type of the customer-managed gateway.
  // Possible values are:
  // * OPEN_MESH
  // * SECURE_WEB_GATEWAY
  enum Type {
    // The type of the customer managed gateway is unspecified.
    TYPE_UNSPECIFIED = 0;

    // The type of the customer managed gateway is TrafficDirector Open
    // Mesh.
    OPEN_MESH = 1;

    // The type of the customer managed gateway is SecureWebGateway (SWG).
    SECURE_WEB_GATEWAY = 2;
  }

  // Required. Name of the Gateway resource. It matches pattern
  // `projects/*/locations/*/gateways/<gateway_name>`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Server-defined URL of this resource
  string self_link = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Set of label tags associated with the Gateway resource.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A free-text description of the resource. Max length 1024
  // characters.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Immutable. The type of the customer managed gateway.
  // This field is required. If unspecified, an error is returned.
  Type type = 6 [(google.api.field_behavior) = IMMUTABLE];

  // Required. One or more ports that the Gateway must receive traffic on. The
  // proxy binds to the ports specified. Gateway listen on 0.0.0.0 on the ports
  // specified below.
  repeated int32 ports = 11 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. Scope determines how configuration across multiple
  // Gateway instances are merged. The configuration for multiple Gateway
  // instances with the same scope will be merged as presented as a single
  // coniguration to the proxy/load balancer.
  //
  // Max length 64 characters.
  // Scope should start with a letter and can only have letters, numbers,
  // hyphens.
  string scope = 8 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. A fully-qualified ServerTLSPolicy URL reference. Specifies how
  // TLS traffic is terminated. If empty, TLS termination is disabled.
  string server_tls_policy = 9 [(google.api.field_behavior) = OPTIONAL];
}

// Request used with the ListGateways method.
message ListGatewaysRequest {
  // Required. The project and location from which the Gateways should be
  // listed, specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/Gateway"
    }
  ];

  // Maximum number of Gateways to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListGatewaysResponse`
  // Indicates that this is a continuation of a prior `ListGateways` call,
  // and that the system should return the next page of data.
  string page_token = 3;
}

// Response returned by the ListGateways method.
message ListGatewaysResponse {
  // List of Gateway resources.
  repeated Gateway gateways = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Request used by the GetGateway method.
message GetGatewayRequest {
  // Required. A name of the Gateway to get. Must be in the format
  // `projects/*/locations/*/gateways/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/Gateway"
    }
  ];
}

// Request used by the CreateGateway method.
message CreateGatewayRequest {
  // Required. The parent resource of the Gateway. Must be in the
  // format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/Gateway"
    }
  ];

  // Required. Short name of the Gateway resource to be created.
  string gateway_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Gateway resource to be created.
  Gateway gateway = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the UpdateGateway method.
message UpdateGatewayRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // Gateway resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Updated Gateway resource.
  Gateway gateway = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the DeleteGateway method.
message DeleteGatewayRequest {
  // Required. A name of the Gateway to delete. Must be in the format
  // `projects/*/locations/*/gateways/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/Gateway"
    }
  ];
}
