// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkservices.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkServices.V1";
option go_package = "cloud.google.com/go/networkservices/apiv1/networkservicespb;networkservicespb";
option java_multiple_files = true;
option java_outer_classname = "TcpRouteProto";
option java_package = "com.google.cloud.networkservices.v1";
option php_namespace = "Google\\Cloud\\NetworkServices\\V1";
option ruby_package = "Google::Cloud::NetworkServices::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/BackendService"
  pattern: "projects/{project}/locations/{location}/backendServices/{backend_service}"
};

// TcpRoute is the resource defining how TCP traffic should be routed by a
// Mesh/Gateway resource.
message TcpRoute {
  option (google.api.resource) = {
    type: "networkservices.googleapis.com/TcpRoute"
    pattern: "projects/{project}/locations/{location}/tcpRoutes/{tcp_route}"
  };

  // Specifies how to match traffic and how to route traffic when traffic is
  // matched.
  message RouteRule {
    // Optional. RouteMatch defines the predicate used to match requests to a
    // given action. Multiple match types are "OR"ed for evaluation. If no
    // routeMatch field is specified, this rule will unconditionally match
    // traffic.
    repeated RouteMatch matches = 1 [(google.api.field_behavior) = OPTIONAL];

    // Required. The detailed rule defining how to route matched traffic.
    RouteAction action = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // RouteMatch defines the predicate used to match requests to a given action.
  // Multiple match types are "OR"ed for evaluation.
  // If no routeMatch field is specified, this rule will unconditionally match
  // traffic.
  message RouteMatch {
    // Required. Must be specified in the CIDR range format. A CIDR range
    // consists of an IP Address and a prefix length to construct the subnet
    // mask. By default, the prefix length is 32 (i.e. matches a single IP
    // address). Only IPV4 addresses are supported.
    // Examples:
    // "********" - matches against this exact IP address.
    // "10.0.0.0/8" - matches against any IP address within the 10.0.0.0 subnet
    // and ************* mask.
    // "0.0.0.0/0" - matches against any IP address'.
    string address = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Specifies the destination port to match against.
    string port = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // The specifications for routing traffic and applying associated policies.
  message RouteAction {
    // Optional. The destination services to which traffic should be forwarded.
    // At least one destination service is required. Only one of route
    // destination or original destination can be set.
    repeated RouteDestination destinations = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true, Router will use the destination IP and port of the
    // original connection as the destination of the request. Default is false.
    // Only one of route destinations or original destination can be set.
    bool original_destination = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Describe the destination for traffic to be routed to.
  message RouteDestination {
    // Required. The URL of a BackendService to route traffic to.
    string service_name = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "compute.googleapis.com/BackendService"
      }
    ];

    // Optional. Specifies the proportion of requests forwarded to the backend
    // referenced by the serviceName field. This is computed as:
    // - weight/Sum(weights in this destination list).
    // For non-zero values, there may be some epsilon from the exact proportion
    // defined here depending on the precision an implementation supports.
    //
    // If only one serviceName is specified and it has a weight greater than 0,
    // 100% of the traffic is forwarded to that backend.
    //
    // If weights are specified for any one service name, they need to be
    // specified for all of them.
    //
    // If weights are unspecified for all services, then, traffic is distributed
    // in equal proportions to all of them.
    int32 weight = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. Name of the TcpRoute resource. It matches pattern
  // `projects/*/locations/global/tcpRoutes/tcp_route_name>`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Server-defined URL of this resource
  string self_link = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A free-text description of the resource. Max length 1024
  // characters.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. Rules that define how traffic is routed and handled. At least one
  // RouteRule must be supplied. If there are multiple rules then the action
  // taken will be the first rule to match.
  repeated RouteRule rules = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. Meshes defines a list of meshes this TcpRoute is attached to, as
  // one of the routing rules to route the requests served by the mesh.
  //
  // Each mesh reference should match the pattern:
  // `projects/*/locations/global/meshes/<mesh_name>`
  //
  // The attached Mesh should be of a type SIDECAR
  repeated string meshes = 8 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/Mesh"
    }
  ];

  // Optional. Gateways defines a list of gateways this TcpRoute is attached to,
  // as one of the routing rules to route the requests served by the gateway.
  //
  // Each gateway reference should match the pattern:
  // `projects/*/locations/global/gateways/<gateway_name>`
  repeated string gateways = 9 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/Gateway"
    }
  ];

  // Optional. Set of label tags associated with the TcpRoute resource.
  map<string, string> labels = 10 [(google.api.field_behavior) = OPTIONAL];
}

// Request used with the ListTcpRoutes method.
message ListTcpRoutesRequest {
  // Required. The project and location from which the TcpRoutes should be
  // listed, specified in the format `projects/*/locations/global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/TcpRoute"
    }
  ];

  // Maximum number of TcpRoutes to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListTcpRoutesResponse`
  // Indicates that this is a continuation of a prior `ListTcpRoutes` call,
  // and that the system should return the next page of data.
  string page_token = 3;
}

// Response returned by the ListTcpRoutes method.
message ListTcpRoutesResponse {
  // List of TcpRoute resources.
  repeated TcpRoute tcp_routes = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Request used by the GetTcpRoute method.
message GetTcpRouteRequest {
  // Required. A name of the TcpRoute to get. Must be in the format
  // `projects/*/locations/global/tcpRoutes/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/TcpRoute"
    }
  ];
}

// Request used by the TcpRoute method.
message CreateTcpRouteRequest {
  // Required. The parent resource of the TcpRoute. Must be in the
  // format `projects/*/locations/global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/TcpRoute"
    }
  ];

  // Required. Short name of the TcpRoute resource to be created.
  string tcp_route_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. TcpRoute resource to be created.
  TcpRoute tcp_route = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the UpdateTcpRoute method.
message UpdateTcpRouteRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // TcpRoute resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Updated TcpRoute resource.
  TcpRoute tcp_route = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the DeleteTcpRoute method.
message DeleteTcpRouteRequest {
  // Required. A name of the TcpRoute to delete. Must be in the format
  // `projects/*/locations/global/tcpRoutes/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/TcpRoute"
    }
  ];
}
