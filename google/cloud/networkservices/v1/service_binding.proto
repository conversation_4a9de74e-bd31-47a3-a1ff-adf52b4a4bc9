// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkservices.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkServices.V1";
option go_package = "cloud.google.com/go/networkservices/apiv1/networkservicespb;networkservicespb";
option java_multiple_files = true;
option java_outer_classname = "ServiceBindingProto";
option java_package = "com.google.cloud.networkservices.v1";
option php_namespace = "Google\\Cloud\\NetworkServices\\V1";
option ruby_package = "Google::Cloud::NetworkServices::V1";

// ServiceBinding is the resource that defines a Service Directory Service to
// be used in a BackendService resource.
message ServiceBinding {
  option (google.api.resource) = {
    type: "networkservices.googleapis.com/ServiceBinding"
    pattern: "projects/{project}/locations/{location}/serviceBindings/{service_binding}"
  };

  // Required. Name of the ServiceBinding resource. It matches pattern
  // `projects/*/locations/global/serviceBindings/service_binding_name`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. A free-text description of the resource. Max length 1024
  // characters.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The full service directory service name of the format
  // /projects/*/locations/*/namespaces/*/services/*
  string service = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. Set of label tags associated with the ServiceBinding resource.
  map<string, string> labels = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Request used with the ListServiceBindings method.
message ListServiceBindingsRequest {
  // Required. The project and location from which the ServiceBindings should be
  // listed, specified in the format `projects/*/locations/global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/ServiceBinding"
    }
  ];

  // Maximum number of ServiceBindings to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListServiceBindingsResponse`
  // Indicates that this is a continuation of a prior `ListRouters` call,
  // and that the system should return the next page of data.
  string page_token = 3;
}

// Response returned by the ListServiceBindings method.
message ListServiceBindingsResponse {
  // List of ServiceBinding resources.
  repeated ServiceBinding service_bindings = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Request used by the GetServiceBinding method.
message GetServiceBindingRequest {
  // Required. A name of the ServiceBinding to get. Must be in the format
  // `projects/*/locations/global/serviceBindings/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/ServiceBinding"
    }
  ];
}

// Request used by the ServiceBinding method.
message CreateServiceBindingRequest {
  // Required. The parent resource of the ServiceBinding. Must be in the
  // format `projects/*/locations/global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/ServiceBinding"
    }
  ];

  // Required. Short name of the ServiceBinding resource to be created.
  string service_binding_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. ServiceBinding resource to be created.
  ServiceBinding service_binding = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request used by the DeleteServiceBinding method.
message DeleteServiceBindingRequest {
  // Required. A name of the ServiceBinding to delete. Must be in the format
  // `projects/*/locations/global/serviceBindings/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/ServiceBinding"
    }
  ];
}
