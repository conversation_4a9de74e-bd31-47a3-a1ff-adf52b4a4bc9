type: google.api.Service
config_version: 3
name: networkservices.googleapis.com
title: Network Services API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.networkservices.v1.DepService
- name: google.cloud.networkservices.v1.NetworkServices
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.networkservices.v1.OperationMetadata

documentation:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: 'google.cloud.networkservices.v1.NetworkServices.*'
    deadline: 60.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1/{resource=projects/*/locations/*/edgeCacheKeysets/*}:getIamPolicy'
    additional_bindings:
    - get: '/v1/{resource=projects/*/locations/*/edgeCacheOrigins/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/edgeCacheServices/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/endpointPolicies/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/serviceBindings/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/meshes/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/gateways/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1/{resource=projects/*/locations/*/edgeCacheKeysets/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/edgeCacheOrigins/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/edgeCacheServices/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/endpointPolicies/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/serviceBindings/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/meshes/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/gateways/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1/{resource=projects/*/locations/*/edgeCacheKeysets/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/edgeCacheOrigins/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/edgeCacheServices/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/endpointPolicies/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/serviceBindings/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/meshes/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/gateways/*}:testIamPermissions'
      body: '*'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.networkservices.v1.DepService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.networkservices.v1.NetworkServices.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1132189&template=1639113
  documentation_uri: https://cloud.google.com/products/networking
  api_short_name: networkservices
  github_label: 'api: networkservices'
  doc_tag_prefix: networkservices
  organization: CLOUD
  library_settings:
  - version: google.cloud.networkservices.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
