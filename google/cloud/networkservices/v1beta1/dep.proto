// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkservices.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/networkservices/v1beta1/common.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkServices.V1Beta1";
option go_package = "cloud.google.com/go/networkservices/apiv1beta1/networkservicespb;networkservicespb";
option java_multiple_files = true;
option java_outer_classname = "DepProto";
option java_package = "com.google.cloud.networkservices.v1beta1";
option php_namespace = "Google\\Cloud\\NetworkServices\\V1beta1";
option ruby_package = "Google::Cloud::NetworkServices::V1beta1";

// Service describing handlers for resources.
service DepService {
  option (google.api.default_host) = "networkservices.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists `LbTrafficExtension` resources in a given project and location.
  rpc ListLbTrafficExtensions(ListLbTrafficExtensionsRequest)
      returns (ListLbTrafficExtensionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/lbTrafficExtensions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of the specified `LbTrafficExtension` resource.
  rpc GetLbTrafficExtension(GetLbTrafficExtensionRequest)
      returns (LbTrafficExtension) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/lbTrafficExtensions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new `LbTrafficExtension` resource in a given project and
  // location.
  rpc CreateLbTrafficExtension(CreateLbTrafficExtensionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/lbTrafficExtensions"
      body: "lb_traffic_extension"
    };
    option (google.api.method_signature) =
        "parent,lb_traffic_extension,lb_traffic_extension_id";
    option (google.longrunning.operation_info) = {
      response_type: "LbTrafficExtension"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of the specified `LbTrafficExtension` resource.
  rpc UpdateLbTrafficExtension(UpdateLbTrafficExtensionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{lb_traffic_extension.name=projects/*/locations/*/lbTrafficExtensions/*}"
      body: "lb_traffic_extension"
    };
    option (google.api.method_signature) = "lb_traffic_extension,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "LbTrafficExtension"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes the specified `LbTrafficExtension` resource.
  rpc DeleteLbTrafficExtension(DeleteLbTrafficExtensionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/lbTrafficExtensions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists `LbRouteExtension` resources in a given project and location.
  rpc ListLbRouteExtensions(ListLbRouteExtensionsRequest)
      returns (ListLbRouteExtensionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/lbRouteExtensions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of the specified `LbRouteExtension` resource.
  rpc GetLbRouteExtension(GetLbRouteExtensionRequest)
      returns (LbRouteExtension) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/lbRouteExtensions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new `LbRouteExtension` resource in a given project and location.
  rpc CreateLbRouteExtension(CreateLbRouteExtensionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/lbRouteExtensions"
      body: "lb_route_extension"
    };
    option (google.api.method_signature) =
        "parent,lb_route_extension,lb_route_extension_id";
    option (google.longrunning.operation_info) = {
      response_type: "LbRouteExtension"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of the specified `LbRouteExtension` resource.
  rpc UpdateLbRouteExtension(UpdateLbRouteExtensionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{lb_route_extension.name=projects/*/locations/*/lbRouteExtensions/*}"
      body: "lb_route_extension"
    };
    option (google.api.method_signature) = "lb_route_extension,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "LbRouteExtension"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes the specified `LbRouteExtension` resource.
  rpc DeleteLbRouteExtension(DeleteLbRouteExtensionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/lbRouteExtensions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// The part of the request or response for which the extension is called.
enum EventType {
  // Unspecified value. Do not use.
  EVENT_TYPE_UNSPECIFIED = 0;

  // If included in `supported_events`,
  // the extension is called when the HTTP request headers arrive.
  REQUEST_HEADERS = 1;

  // If included in `supported_events`,
  // the extension is called when the HTTP request body arrives.
  REQUEST_BODY = 2;

  // If included in `supported_events`,
  // the extension is called when the HTTP response headers arrive.
  RESPONSE_HEADERS = 3;

  // If included in `supported_events`,
  // the extension is called when the HTTP response body arrives.
  RESPONSE_BODY = 4;

  // If included in `supported_events`,
  // the extension is called when the HTTP request trailers arrives.
  REQUEST_TRAILERS = 5;

  // If included in `supported_events`,
  // the extension is called when the HTTP response trailers arrives.
  RESPONSE_TRAILERS = 6;
}

// Load balancing schemes supported by the `LbTrafficExtension` resource and
// `LbRouteExtension` resource.
// For more information, refer to [Choosing a load
// balancer](https://cloud.google.com/load-balancing/docs/backend-service).
enum LoadBalancingScheme {
  // Default value. Do not use.
  LOAD_BALANCING_SCHEME_UNSPECIFIED = 0;

  // Signifies that this is used for Internal HTTP(S) Load Balancing.
  INTERNAL_MANAGED = 1;

  // Signifies that this is used for External Managed HTTP(S) Load
  // Balancing.
  EXTERNAL_MANAGED = 2;
}

// A single extension chain wrapper that contains the match conditions and
// extensions to execute.
message ExtensionChain {
  // Conditions under which this chain is invoked for a request.
  message MatchCondition {
    // Required. A Common Expression Language (CEL) expression that is used to
    // match requests for which the extension chain is executed.
    //
    // For more information, see [CEL matcher language
    // reference](https://cloud.google.com/service-extensions/docs/cel-matcher-language-reference).
    string cel_expression = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // A single extension in the chain to execute for the matching request.
  message Extension {
    // Required. The name for this extension.
    // The name is logged as part of the HTTP request logs.
    // The name must conform with RFC-1034, is restricted to lower-cased
    // letters, numbers and hyphens, and can have a maximum length of 63
    // characters. Additionally, the first character must be a letter and the
    // last a letter or a number.
    string name = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. The `:authority` header in the gRPC request sent from Envoy
    // to the extension service.
    // Required for Callout extensions.
    string authority = 2 [(google.api.field_behavior) = OPTIONAL];

    // Required. The reference to the service that runs the extension.
    //
    // Currently only callout extensions are supported here.
    //
    // To configure a callout extension, `service` must be a fully-qualified
    // reference
    // to a [backend
    // service](https://cloud.google.com/compute/docs/reference/rest/v1/backendServices)
    // in the format:
    // `https://www.googleapis.com/compute/v1/projects/{project}/regions/{region}/backendServices/{backendService}`
    // or
    // `https://www.googleapis.com/compute/v1/projects/{project}/global/backendServices/{backendService}`.
    string service = 3 [(google.api.field_behavior) = REQUIRED];

    // Optional. A set of events during request or response processing for which
    // this extension is called. This field is required for the
    // `LbTrafficExtension` resource. It's not relevant for the
    // `LbRouteExtension` resource.
    repeated EventType supported_events = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Specifies the timeout for each individual message on the
    // stream. The timeout must be between 10-1000 milliseconds. Required for
    // Callout extensions.
    google.protobuf.Duration timeout = 5
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Determines how the proxy behaves if the call to the extension
    // fails or times out.
    //
    // When set to `TRUE`, request or response processing continues without
    // error. Any subsequent extensions in the extension chain are also
    // executed. When set to `FALSE` or the default setting of `FALSE` is used,
    // one of the following happens:
    // * If response headers have not been delivered to the downstream client,
    // a generic 500 error is returned to the client. The error response can be
    // tailored by configuring a custom error response in the load balancer.
    //
    // * If response headers have been delivered, then the HTTP stream to the
    // downstream client is reset.
    bool fail_open = 6 [(google.api.field_behavior) = OPTIONAL];

    // Optional. List of the HTTP headers to forward to the extension
    // (from the client or backend). If omitted, all headers are sent.
    // Each element is a string indicating the header name.
    repeated string forward_headers = 7
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The name for this extension chain.
  // The name is logged as part of the HTTP request logs.
  // The name must conform with RFC-1034, is restricted to lower-cased letters,
  // numbers and hyphens, and can have a maximum length of 63 characters.
  // Additionally, the first character must be a letter and the last a letter or
  // a number.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Conditions under which this chain is invoked for a request.
  MatchCondition match_condition = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A set of extensions to execute for the matching request.
  // At least one extension is required.
  // Up to 3 extensions can be defined for each extension chain
  // for `LbTrafficExtension` resource.
  // `LbRouteExtension` chains are limited to 1 extension per extension chain.
  repeated Extension extensions = 3 [(google.api.field_behavior) = REQUIRED];
}

// `LbTrafficExtension` is a resource that lets the extension service modify the
// headers and payloads of both requests and responses without impacting the
// choice of backend services or any other security policies associated with the
// backend service.
message LbTrafficExtension {
  option (google.api.resource) = {
    type: "networkservices.googleapis.com/LbTrafficExtension"
    pattern: "projects/{project}/locations/{location}/lbTrafficExtensions/{lb_traffic_extension}"
    plural: "lbTrafficExtensions"
    singular: "lbTrafficExtension"
  };

  // Required. Identifier. Name of the `LbTrafficExtension` resource in the
  // following format:
  // `projects/{project}/locations/{location}/lbTrafficExtensions/{lb_traffic_extension}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A human-readable description of the resource.
  string description = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set of labels associated with the `LbTrafficExtension` resource.
  //
  // The format must comply with [the requirements for
  // labels](https://cloud.google.com/compute/docs/labeling-resources#requirements)
  // for Google Cloud resources.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. A list of references to the forwarding rules to which this
  // service extension is attached to. At least one forwarding rule is required.
  // There can be only one `LBTrafficExtension` resource per forwarding rule.
  repeated string forwarding_rules = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. A set of ordered extension chains that contain the match
  // conditions and extensions to execute. Match conditions for each extension
  // chain are evaluated in sequence for a given request. The first extension
  // chain that has a condition that matches the request is executed.
  // Any subsequent extension chains do not execute.
  // Limited to 5 extension chains per resource.
  repeated ExtensionChain extension_chains = 7
      [(google.api.field_behavior) = REQUIRED];

  // Required. All backend services and forwarding rules referenced by this
  // extension must share the same load balancing scheme. Supported values:
  // `INTERNAL_MANAGED`, `EXTERNAL_MANAGED`. For more information, refer to
  // [Choosing a load
  // balancer](https://cloud.google.com/load-balancing/docs/backend-service).
  LoadBalancingScheme load_balancing_scheme = 8
      [(google.api.field_behavior) = REQUIRED];
}

// Message for requesting list of `LbTrafficExtension` resources.
message ListLbTrafficExtensionsRequest {
  // Required. The project and location from which the `LbTrafficExtension`
  // resources are listed, specified in the following format:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/LbTrafficExtension"
    }
  ];

  // Optional. Requested page size. The server might return fewer items than
  // requested. If unspecified, the server picks an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results that the server returns.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing `LbTrafficExtension` resources.
message ListLbTrafficExtensionsResponse {
  // The list of `LbTrafficExtension` resources.
  repeated LbTrafficExtension lb_traffic_extensions = 1;

  // A token identifying a page of results that the server returns.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a `LbTrafficExtension` resource.
message GetLbTrafficExtensionRequest {
  // Required. A name of the `LbTrafficExtension` resource to get. Must be in
  // the format
  // `projects/{project}/locations/{location}/lbTrafficExtensions/{lb_traffic_extension}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/LbTrafficExtension"
    }
  ];
}

// Message for creating a `LbTrafficExtension` resource.
message CreateLbTrafficExtensionRequest {
  // Required. The parent resource of the `LbTrafficExtension` resource. Must be
  // in the format `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/LbTrafficExtension"
    }
  ];

  // Required. User-provided ID of the `LbTrafficExtension` resource to be
  // created.
  string lb_traffic_extension_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. `LbTrafficExtension` resource to be created.
  LbTrafficExtension lb_traffic_extension = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server can ignore
  // the request if it has already been completed. The server guarantees
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for updating a `LbTrafficExtension` resource.
message UpdateLbTrafficExtensionRequest {
  // Required. Used to specify the fields to be overwritten in the
  // `LbTrafficExtension` resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field is overwritten if it is in the mask. If the
  // user does not specify a mask, then all fields are overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. `LbTrafficExtension` resource being updated.
  LbTrafficExtension lb_traffic_extension = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server can ignore
  // the request if it has already been completed. The server guarantees
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for deleting a `LbTrafficExtension` resource.
message DeleteLbTrafficExtensionRequest {
  // Required. The name of the `LbTrafficExtension` resource to delete. Must be
  // in the format
  // `projects/{project}/locations/{location}/lbTrafficExtensions/{lb_traffic_extension}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/LbTrafficExtension"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server can ignore
  // the request if it has already been completed. The server guarantees
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// `LbRouteExtension` is a resource that lets you control where traffic is
// routed to for a given request.
message LbRouteExtension {
  option (google.api.resource) = {
    type: "networkservices.googleapis.com/LbRouteExtension"
    pattern: "projects/{project}/locations/{location}/lbRouteExtensions/{lb_route_extension}"
    plural: "lbRouteExtensions"
    singular: "lbRouteExtension"
  };

  // Required. Identifier. Name of the `LbRouteExtension` resource in the
  // following format:
  // `projects/{project}/locations/{location}/lbRouteExtensions/{lb_route_extension}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A human-readable description of the resource.
  string description = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set of labels associated with the `LbRouteExtension` resource.
  //
  // The format must comply with [the requirements for
  // labels](https://cloud.google.com/compute/docs/labeling-resources#requirements)
  // for Google Cloud resources.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. A list of references to the forwarding rules to which this
  // service extension is attached to. At least one forwarding rule is required.
  // There can be only one `LbRouteExtension` resource per forwarding rule.
  repeated string forwarding_rules = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. A set of ordered extension chains that contain the match
  // conditions and extensions to execute. Match conditions for each extension
  // chain are evaluated in sequence for a given request. The first extension
  // chain that has a condition that matches the request is executed.
  // Any subsequent extension chains do not execute.
  // Limited to 5 extension chains per resource.
  repeated ExtensionChain extension_chains = 7
      [(google.api.field_behavior) = REQUIRED];

  // Required. All backend services and forwarding rules referenced by this
  // extension must share the same load balancing scheme. Supported values:
  // `INTERNAL_MANAGED`, `EXTERNAL_MANAGED`. For more information, refer to
  // [Choosing a load
  // balancer](https://cloud.google.com/load-balancing/docs/backend-service).
  LoadBalancingScheme load_balancing_scheme = 8
      [(google.api.field_behavior) = REQUIRED];
}

// Message for requesting list of `LbRouteExtension` resources.
message ListLbRouteExtensionsRequest {
  // Required. The project and location from which the `LbRouteExtension`
  // resources are listed, specified in the following format:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/LbRouteExtension"
    }
  ];

  // Optional. Requested page size. The server might return fewer items than
  // requested. If unspecified, the server picks an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results that the server returns.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing `LbRouteExtension` resources.
message ListLbRouteExtensionsResponse {
  // The list of `LbRouteExtension` resources.
  repeated LbRouteExtension lb_route_extensions = 1;

  // A token identifying a page of results that the server returns.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a `LbRouteExtension` resource.
message GetLbRouteExtensionRequest {
  // Required. A name of the `LbRouteExtension` resource to get. Must be in the
  // format
  // `projects/{project}/locations/{location}/lbRouteExtensions/{lb_route_extension}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/LbRouteExtension"
    }
  ];
}

// Message for creating a `LbRouteExtension` resource.
message CreateLbRouteExtensionRequest {
  // Required. The parent resource of the `LbRouteExtension` resource. Must be
  // in the format `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/LbRouteExtension"
    }
  ];

  // Required. User-provided ID of the `LbRouteExtension` resource to be
  // created.
  string lb_route_extension_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. `LbRouteExtension` resource to be created.
  LbRouteExtension lb_route_extension = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server can ignore
  // the request if it has already been completed. The server guarantees
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for updating a `LbRouteExtension` resource.
message UpdateLbRouteExtensionRequest {
  // Required. Used to specify the fields to be overwritten in the
  // `LbRouteExtension` resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field is overwritten if it is in the mask. If the
  // user does not specify a mask, then all fields are overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. `LbRouteExtension` resource being updated.
  LbRouteExtension lb_route_extension = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server can ignore
  // the request if it has already been completed. The server guarantees
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for deleting a `LbRouteExtension` resource.
message DeleteLbRouteExtensionRequest {
  // Required. The name of the `LbRouteExtension` resource to delete. Must be in
  // the format
  // `projects/{project}/locations/{location}/lbRouteExtensions/{lb_route_extension}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/LbRouteExtension"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server can ignore
  // the request if it has already been completed. The server guarantees
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}
