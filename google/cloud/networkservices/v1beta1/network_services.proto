// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkservices.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/networkservices/v1beta1/endpoint_policy.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.NetworkServices.V1Beta1";
option go_package = "cloud.google.com/go/networkservices/apiv1beta1/networkservicespb;networkservicespb";
option java_multiple_files = true;
option java_package = "com.google.cloud.networkservices.v1beta1";
option php_namespace = "Google\\Cloud\\NetworkServices\\V1beta1";
option ruby_package = "Google::Cloud::NetworkServices::V1beta1";

// Service describing handlers for resources.
service NetworkServices {
  option (google.api.default_host) = "networkservices.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists EndpointPolicies in a given project and location.
  rpc ListEndpointPolicies(ListEndpointPoliciesRequest)
      returns (ListEndpointPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/endpointPolicies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single EndpointPolicy.
  rpc GetEndpointPolicy(GetEndpointPolicyRequest) returns (EndpointPolicy) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/endpointPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new EndpointPolicy in a given project and location.
  rpc CreateEndpointPolicy(CreateEndpointPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/endpointPolicies"
      body: "endpoint_policy"
    };
    option (google.api.method_signature) =
        "parent,endpoint_policy,endpoint_policy_id";
    option (google.longrunning.operation_info) = {
      response_type: "EndpointPolicy"
      metadata_type: "google.cloud.networkservices.v1beta1.OperationMetadata"
    };
  }

  // Updates the parameters of a single EndpointPolicy.
  rpc UpdateEndpointPolicy(UpdateEndpointPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{endpoint_policy.name=projects/*/locations/*/endpointPolicies/*}"
      body: "endpoint_policy"
    };
    option (google.api.method_signature) = "endpoint_policy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "EndpointPolicy"
      metadata_type: "google.cloud.networkservices.v1beta1.OperationMetadata"
    };
  }

  // Deletes a single EndpointPolicy.
  rpc DeleteEndpointPolicy(DeleteEndpointPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/endpointPolicies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networkservices.v1beta1.OperationMetadata"
    };
  }
}
