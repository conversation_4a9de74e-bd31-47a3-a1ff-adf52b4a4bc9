// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkservices.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/networkservices/v1beta1/common.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkServices.V1Beta1";
option go_package = "cloud.google.com/go/networkservices/apiv1beta1/networkservicespb;networkservicespb";
option java_multiple_files = true;
option java_outer_classname = "EndpointPolicyProto";
option java_package = "com.google.cloud.networkservices.v1beta1";
option php_namespace = "Google\\Cloud\\NetworkServices\\V1beta1";
option ruby_package = "Google::Cloud::NetworkServices::V1beta1";
option (google.api.resource_definition) = {
  type: "networksecurity.googleapis.com/AuthorizationPolicy"
  pattern: "projects/{project}/locations/{location}/authorizationPolicies/{authorization_policy}"
};
option (google.api.resource_definition) = {
  type: "networksecurity.googleapis.com/ServerTlsPolicy"
  pattern: "projects/{project}/locations/{location}/serverTlsPolicies/{server_tls_policy}"
};
option (google.api.resource_definition) = {
  type: "networksecurity.googleapis.com/ClientTlsPolicy"
  pattern: "projects/{project}/locations/{location}/clientTlsPolicies/{client_tls_policy}"
};

// EndpointPolicy is a resource that helps apply desired configuration
// on the endpoints that match specific criteria.
// For example, this resource can be used to apply "authentication config"
// an all endpoints that serve on port 8080.
message EndpointPolicy {
  option (google.api.resource) = {
    type: "networkservices.googleapis.com/EndpointPolicy"
    pattern: "projects/{project}/locations/{location}/endpointPolicies/{endpoint_policy}"
  };

  // The type of endpoint policy.
  enum EndpointPolicyType {
    // Default value. Must not be used.
    ENDPOINT_POLICY_TYPE_UNSPECIFIED = 0;

    // Represents a proxy deployed as a sidecar.
    SIDECAR_PROXY = 1;

    // Represents a proxyless gRPC backend.
    GRPC_SERVER = 2;
  }

  // Required. Name of the EndpointPolicy resource. It matches pattern
  // `projects/{project}/locations/global/endpointPolicies/{endpoint_policy}`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the resource was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Set of label tags associated with the EndpointPolicy resource.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. The type of endpoint policy. This is primarily used to validate
  // the configuration.
  EndpointPolicyType type = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. This field specifies the URL of AuthorizationPolicy resource that
  // applies authorization policies to the inbound traffic at the
  // matched endpoints. Refer to Authorization. If this field is not
  // specified, authorization is disabled(no authz checks) for this
  // endpoint.
  string authorization_policy = 7 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "networksecurity.googleapis.com/AuthorizationPolicy"
    }
  ];

  // Required. A matcher that selects endpoints to which the policies should be
  // applied.
  EndpointMatcher endpoint_matcher = 9 [(google.api.field_behavior) = REQUIRED];

  // Optional. Port selector for the (matched) endpoints. If no port selector is
  // provided, the matched config is applied to all ports.
  TrafficPortSelector traffic_port_selector = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A free-text description of the resource. Max length 1024
  // characters.
  string description = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A URL referring to ServerTlsPolicy resource. ServerTlsPolicy is
  // used to determine the authentication policy to be applied to terminate the
  // inbound traffic at the identified backends. If this field is not set,
  // authentication is disabled(open) for this endpoint.
  string server_tls_policy = 12 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "networksecurity.googleapis.com/ServerTlsPolicy"
    }
  ];

  // Optional. A URL referring to a ClientTlsPolicy resource. ClientTlsPolicy
  // can be set to specify the authentication for traffic from the proxy to the
  // actual endpoints. More specifically, it is applied to the outgoing traffic
  // from the proxy to the endpoint. This is typically used for sidecar model
  // where the proxy identifies itself as endpoint to the control plane, with
  // the connection between sidecar and endpoint requiring authentication. If
  // this field is not set, authentication is disabled(open). Applicable only
  // when EndpointPolicyType is SIDECAR_PROXY.
  string client_tls_policy = 13 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "networksecurity.googleapis.com/ClientTlsPolicy"
    }
  ];
}

// Request used with the ListEndpointPolicies method.
message ListEndpointPoliciesRequest {
  // Required. The project and location from which the EndpointPolicies should
  // be listed, specified in the format `projects/*/locations/global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/EndpointPolicy"
    }
  ];

  // Maximum number of EndpointPolicies to return per call.
  int32 page_size = 2;

  // The value returned by the last `ListEndpointPoliciesResponse`
  // Indicates that this is a continuation of a prior
  // `ListEndpointPolicies` call, and that the system should return the
  // next page of data.
  string page_token = 3;
}

// Response returned by the ListEndpointPolicies method.
message ListEndpointPoliciesResponse {
  // List of EndpointPolicy resources.
  repeated EndpointPolicy endpoint_policies = 1;

  // If there might be more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Request used with the GetEndpointPolicy method.
message GetEndpointPolicyRequest {
  // Required. A name of the EndpointPolicy to get. Must be in the format
  // `projects/*/locations/global/endpointPolicies/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/EndpointPolicy"
    }
  ];
}

// Request used with the CreateEndpointPolicy method.
message CreateEndpointPolicyRequest {
  // Required. The parent resource of the EndpointPolicy. Must be in the
  // format `projects/*/locations/global`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkservices.googleapis.com/EndpointPolicy"
    }
  ];

  // Required. Short name of the EndpointPolicy resource to be created.
  // E.g. "CustomECS".
  string endpoint_policy_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. EndpointPolicy resource to be created.
  EndpointPolicy endpoint_policy = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request used with the UpdateEndpointPolicy method.
message UpdateEndpointPolicyRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // EndpointPolicy resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Updated EndpointPolicy resource.
  EndpointPolicy endpoint_policy = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request used with the DeleteEndpointPolicy method.
message DeleteEndpointPolicyRequest {
  // Required. A name of the EndpointPolicy to delete. Must be in the format
  // `projects/*/locations/global/endpointPolicies/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkservices.googleapis.com/EndpointPolicy"
    }
  ];
}
