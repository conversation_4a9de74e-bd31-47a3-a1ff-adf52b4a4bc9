// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkservices.v1beta1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkServices.V1Beta1";
option go_package = "cloud.google.com/go/networkservices/apiv1beta1/networkservicespb;networkservicespb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.cloud.networkservices.v1beta1";
option php_namespace = "Google\\Cloud\\NetworkServices\\V1beta1";
option ruby_package = "Google::Cloud::NetworkServices::V1beta1";

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Specification of a port-based selector.
message TrafficPortSelector {
  // Optional. A list of ports. Can be port numbers or port range
  // (example, [80-90] specifies all ports from 80 to 90, including
  // 80 and 90) or named ports or * to specify all ports. If the
  // list is empty, all ports are selected.
  repeated string ports = 1 [(google.api.field_behavior) = OPTIONAL];
}

// A definition of a matcher that selects endpoints to which the policies
// should be applied.
message EndpointMatcher {
  // The matcher that is based on node metadata presented by xDS clients.
  message MetadataLabelMatcher {
    // Defines a name-pair value for a single label.
    message MetadataLabels {
      // Required. Label name presented as key in xDS Node Metadata.
      string label_name = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. Label value presented as value corresponding to the above
      // key, in xDS Node Metadata.
      string label_value = 2 [(google.api.field_behavior) = REQUIRED];
    }

    // Possible criteria values that define logic of how matching is made.
    enum MetadataLabelMatchCriteria {
      // Default value. Should not be used.
      METADATA_LABEL_MATCH_CRITERIA_UNSPECIFIED = 0;

      // At least one of the Labels specified in the matcher should match the
      // metadata presented by xDS client.
      MATCH_ANY = 1;

      // The metadata presented by the xDS client should contain all of the
      // labels specified here.
      MATCH_ALL = 2;
    }

    // Specifies how matching should be done.
    //
    // Supported values are:
    // MATCH_ANY: At least one of the Labels specified in the
    //   matcher should match the metadata presented by xDS client.
    // MATCH_ALL: The metadata presented by the xDS client should
    //   contain all of the labels specified here.
    //
    // The selection is determined based on the best match. For
    // example, suppose there are three EndpointPolicy
    // resources P1, P2 and P3 and if P1 has a the matcher as
    // MATCH_ANY <A:1, B:1>, P2 has MATCH_ALL <A:1,B:1>, and P3 has
    // MATCH_ALL <A:1,B:1,C:1>.
    //
    // If a client with label <A:1> connects, the config from P1
    // will be selected.
    //
    // If a client with label <A:1,B:1> connects, the config from P2
    // will be selected.
    //
    // If a client with label <A:1,B:1,C:1> connects, the config
    // from P3 will be selected.
    //
    // If there is more than one best match, (for example, if a
    // config P4 with selector <A:1,D:1> exists and if a client with
    // label <A:1,B:1,D:1> connects), an error will be thrown.
    MetadataLabelMatchCriteria metadata_label_match_criteria = 1;

    // The list of label value pairs that must match labels in the
    // provided metadata based on filterMatchCriteria This list can
    // have at most 64 entries. The list can be empty if the match
    // criteria is MATCH_ANY, to specify a wildcard match (i.e this
    // matches any client).
    repeated MetadataLabels metadata_labels = 2;
  }

  // Specifies type of the matcher used for this endpoint matcher.
  oneof matcher_type {
    // The matcher is based on node metadata presented by xDS clients.
    MetadataLabelMatcher metadata_label_matcher = 1;
  }
}
