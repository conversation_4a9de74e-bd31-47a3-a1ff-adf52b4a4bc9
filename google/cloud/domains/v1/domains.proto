// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.domains.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "google/type/postal_address.proto";

option go_package = "cloud.google.com/go/domains/apiv1/domainspb;domainspb";
option java_multiple_files = true;
option java_outer_classname = "DomainsProto";
option java_package = "com.google.cloud.domains.v1";

// The Cloud Domains API enables management and configuration of domain names.
service Domains {
  option (google.api.default_host) = "domains.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Searches for available domain names similar to the provided query.
  //
  // Availability results from this method are approximate; call
  // `RetrieveRegisterParameters` on a domain before registering to confirm
  // availability.
  rpc SearchDomains(SearchDomainsRequest) returns (SearchDomainsResponse) {
    option (google.api.http) = {
      get: "/v1/{location=projects/*/locations/*}/registrations:searchDomains"
    };
    option (google.api.method_signature) = "location,query";
  }

  // Gets parameters needed to register a new domain name, including price and
  // up-to-date availability. Use the returned values to call `RegisterDomain`.
  rpc RetrieveRegisterParameters(RetrieveRegisterParametersRequest) returns (RetrieveRegisterParametersResponse) {
    option (google.api.http) = {
      get: "/v1/{location=projects/*/locations/*}/registrations:retrieveRegisterParameters"
    };
    option (google.api.method_signature) = "location,domain_name";
  }

  // Registers a new domain name and creates a corresponding `Registration`
  // resource.
  //
  // Call `RetrieveRegisterParameters` first to check availability of the domain
  // name and determine parameters like price that are needed to build a call to
  // this method.
  //
  // A successful call creates a `Registration` resource in state
  // `REGISTRATION_PENDING`, which resolves to `ACTIVE` within 1-2
  // minutes, indicating that the domain was successfully registered. If the
  // resource ends up in state `REGISTRATION_FAILED`, it indicates that the
  // domain was not registered successfully, and you can safely delete the
  // resource and retry registration.
  rpc RegisterDomain(RegisterDomainRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/registrations:register"
      body: "*"
    };
    option (google.api.method_signature) = "parent,registration,yearly_price";
    option (google.longrunning.operation_info) = {
      response_type: "Registration"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets parameters needed to transfer a domain name from another registrar to
  // Cloud Domains. For domains managed by Google Domains, transferring to Cloud
  // Domains is not supported.
  //
  //
  // Use the returned values to call `TransferDomain`.
  rpc RetrieveTransferParameters(RetrieveTransferParametersRequest) returns (RetrieveTransferParametersResponse) {
    option (google.api.http) = {
      get: "/v1/{location=projects/*/locations/*}/registrations:retrieveTransferParameters"
    };
    option (google.api.method_signature) = "location,domain_name";
  }

  // Transfers a domain name from another registrar to Cloud Domains.  For
  // domains managed by Google Domains, transferring to Cloud Domains is not
  // supported.
  //
  //
  // Before calling this method, go to the domain's current registrar to unlock
  // the domain for transfer and retrieve the domain's transfer authorization
  // code. Then call `RetrieveTransferParameters` to confirm that the domain is
  // unlocked and to get values needed to build a call to this method.
  //
  // A successful call creates a `Registration` resource in state
  // `TRANSFER_PENDING`. It can take several days to complete the transfer
  // process. The registrant can often speed up this process by approving the
  // transfer through the current registrar, either by clicking a link in an
  // email from the registrar or by visiting the registrar's website.
  //
  // A few minutes after transfer approval, the resource transitions to state
  // `ACTIVE`, indicating that the transfer was successful. If the transfer is
  // rejected or the request expires without being approved, the resource can
  // end up in state `TRANSFER_FAILED`. If transfer fails, you can safely delete
  // the resource and retry the transfer.
  rpc TransferDomain(TransferDomainRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/registrations:transfer"
      body: "*"
    };
    option (google.api.method_signature) = "parent,registration,yearly_price,authorization_code";
    option (google.longrunning.operation_info) = {
      response_type: "Registration"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the `Registration` resources in a project.
  rpc ListRegistrations(ListRegistrationsRequest) returns (ListRegistrationsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/registrations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a `Registration` resource.
  rpc GetRegistration(GetRegistrationRequest) returns (Registration) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/registrations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates select fields of a `Registration` resource, notably `labels`. To
  // update other fields, use the appropriate custom update method:
  //
  // * To update management settings, see `ConfigureManagementSettings`
  // * To update DNS configuration, see `ConfigureDnsSettings`
  // * To update contact information, see `ConfigureContactSettings`
  rpc UpdateRegistration(UpdateRegistrationRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{registration.name=projects/*/locations/*/registrations/*}"
      body: "registration"
    };
    option (google.api.method_signature) = "registration,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Registration"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a `Registration`'s management settings.
  rpc ConfigureManagementSettings(ConfigureManagementSettingsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{registration=projects/*/locations/*/registrations/*}:configureManagementSettings"
      body: "*"
    };
    option (google.api.method_signature) = "registration,management_settings,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Registration"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a `Registration`'s DNS settings.
  rpc ConfigureDnsSettings(ConfigureDnsSettingsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{registration=projects/*/locations/*/registrations/*}:configureDnsSettings"
      body: "*"
    };
    option (google.api.method_signature) = "registration,dns_settings,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Registration"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a `Registration`'s contact settings. Some changes require
  // confirmation by the domain's registrant contact .
  rpc ConfigureContactSettings(ConfigureContactSettingsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{registration=projects/*/locations/*/registrations/*}:configureContactSettings"
      body: "*"
    };
    option (google.api.method_signature) = "registration,contact_settings,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Registration"
      metadata_type: "OperationMetadata"
    };
  }

  // Exports a `Registration` resource, such that it is no longer managed by
  // Cloud Domains.
  //
  // When an active domain is successfully exported, you can continue to use the
  // domain in [Google Domains](https://domains.google/) until it expires. The
  // calling user becomes the domain's sole owner in Google Domains, and
  // permissions for the domain are subsequently managed there. The domain does
  // not renew automatically unless the new owner sets up billing in Google
  // Domains.
  rpc ExportRegistration(ExportRegistrationRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/registrations/*}:export"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Registration"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a `Registration` resource.
  //
  // This method works on any `Registration` resource using [Subscription or
  // Commitment billing](/domains/pricing#billing-models), provided that the
  // resource was created at least 1 day in the past.
  //
  // For `Registration` resources using
  // [Monthly billing](/domains/pricing#billing-models), this method works if:
  //
  // * `state` is `EXPORTED` with `expire_time` in the past
  // * `state` is `REGISTRATION_FAILED`
  // * `state` is `TRANSFER_FAILED`
  //
  // When an active registration is successfully deleted, you can continue to
  // use the domain in [Google Domains](https://domains.google/) until it
  // expires. The calling user becomes the domain's sole owner in Google
  // Domains, and permissions for the domain are subsequently managed there. The
  // domain does not renew automatically unless the new owner sets up billing in
  // Google Domains.
  rpc DeleteRegistration(DeleteRegistrationRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/registrations/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets the authorization code of the `Registration` for the purpose of
  // transferring the domain to another registrar.
  //
  // You can call this method only after 60 days have elapsed since the initial
  // domain registration.
  rpc RetrieveAuthorizationCode(RetrieveAuthorizationCodeRequest) returns (AuthorizationCode) {
    option (google.api.http) = {
      get: "/v1/{registration=projects/*/locations/*/registrations/*}:retrieveAuthorizationCode"
    };
    option (google.api.method_signature) = "registration";
  }

  // Resets the authorization code of the `Registration` to a new random string.
  //
  // You can call this method only after 60 days have elapsed since the initial
  // domain registration.
  rpc ResetAuthorizationCode(ResetAuthorizationCodeRequest) returns (AuthorizationCode) {
    option (google.api.http) = {
      post: "/v1/{registration=projects/*/locations/*/registrations/*}:resetAuthorizationCode"
      body: "*"
    };
    option (google.api.method_signature) = "registration";
  }
}

// The `Registration` resource facilitates managing and configuring domain name
// registrations.
//
// There are several ways to create a new `Registration` resource:
//
// To create a new `Registration` resource, find a suitable domain name by
// calling the `SearchDomains` method with a query to see available domain name
// options. After choosing a name, call `RetrieveRegisterParameters` to
// ensure availability and obtain information like pricing, which is needed to
// build a call to `RegisterDomain`.
//
// Another way to create a new `Registration` is to transfer an existing
// domain from another registrar. First, go to the current registrar to unlock
// the domain for transfer and retrieve the domain's transfer authorization
// code. Then call `RetrieveTransferParameters` to confirm that the domain is
// unlocked and to get values needed to build a call to `TransferDomain`.
message Registration {
  option (google.api.resource) = {
    type: "domains.googleapis.com/Registration"
    pattern: "projects/{project}/locations/{location}/registrations/{registration}"
  };

  // Possible states of a `Registration`.
  enum State {
    // The state is undefined.
    STATE_UNSPECIFIED = 0;

    // The domain is being registered.
    REGISTRATION_PENDING = 1;

    // The domain registration failed. You can delete resources in this state
    // to allow registration to be retried.
    REGISTRATION_FAILED = 2;

    // The domain is being transferred from another registrar to Cloud Domains.
    TRANSFER_PENDING = 3;

    // The attempt to transfer the domain from another registrar to
    // Cloud Domains failed. You can delete resources in this state and retry
    // the transfer.
    TRANSFER_FAILED = 4;

    // The domain is registered and operational. The domain renews automatically
    // as long as it remains in this state.
    ACTIVE = 6;

    // The domain is suspended and inoperative. For more details, see the
    // `issues` field.
    SUSPENDED = 7;

    // The domain is no longer managed with Cloud Domains. It may have been
    // transferred to another registrar or exported for management in
    // [Google Domains](https://domains.google/). You can no longer update it
    // with this API, and information shown about it may be stale. Domains in
    // this state are not automatically renewed by Cloud Domains.
    EXPORTED = 8;
  }

  // Possible issues with a `Registration` that require attention.
  enum Issue {
    // The issue is undefined.
    ISSUE_UNSPECIFIED = 0;

    // Contact the Cloud Support team to resolve a problem with this domain.
    CONTACT_SUPPORT = 1;

    // [ICANN](https://icann.org/) requires verification of the email address
    // in the `Registration`'s `contact_settings.registrant_contact` field. To
    // verify the email address, follow the
    // instructions in the email the `registrant_contact` receives following
    // registration. If you do not complete email verification within
    // 15 days of registration, the domain is suspended. To resend the
    // verification email, call ConfigureContactSettings and provide the current
    // `registrant_contact.email`.
    UNVERIFIED_EMAIL = 2;
  }

  // Output only. Name of the `Registration` resource, in the format
  // `projects/*/locations/*/registrations/<domain_name>`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. The domain name. Unicode domain names must be expressed in Punycode format.
  string domain_name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. The creation timestamp of the `Registration` resource.
  google.protobuf.Timestamp create_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The expiration timestamp of the `Registration`.
  google.protobuf.Timestamp expire_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the `Registration`
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The set of issues with the `Registration` that require attention.
  repeated Issue issues = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Set of labels associated with the `Registration`.
  map<string, string> labels = 9;

  // Settings for management of the `Registration`, including renewal, billing,
  // and transfer. You cannot update these with the `UpdateRegistration`
  // method. To update these settings, use the `ConfigureManagementSettings`
  // method.
  ManagementSettings management_settings = 10;

  // Settings controlling the DNS configuration of the `Registration`. You
  // cannot update these with the `UpdateRegistration` method. To update these
  // settings, use the `ConfigureDnsSettings` method.
  DnsSettings dns_settings = 11;

  // Required. Settings for contact information linked to the `Registration`. You cannot
  // update these with the `UpdateRegistration` method. To update these
  // settings, use the `ConfigureContactSettings` method.
  ContactSettings contact_settings = 12 [(google.api.field_behavior) = REQUIRED];

  // Output only. Pending contact settings for the `Registration`. Updates to the
  // `contact_settings` field that change its `registrant_contact` or `privacy`
  // fields require email confirmation by the `registrant_contact`
  // before taking effect. This field is set only if there are pending updates
  // to the `contact_settings` that have not been confirmed. To confirm the
  // changes, the `registrant_contact` must follow the instructions in the
  // email they receive.
  ContactSettings pending_contact_settings = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Set of options for the `contact_settings.privacy` field that this
  // `Registration` supports.
  repeated ContactPrivacy supported_privacy = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines renewal, billing, and transfer settings for a `Registration`.
message ManagementSettings {
  // Defines how the `Registration` is renewed.
  enum RenewalMethod {
    // The renewal method is undefined.
    RENEWAL_METHOD_UNSPECIFIED = 0;

    // The domain is automatically renewed each year .
    //
    // To disable automatic renewals, delete the resource by calling
    // `DeleteRegistration` or export it by calling `ExportRegistration`.
    AUTOMATIC_RENEWAL = 1;

    // The domain must be explicitly renewed each year before its
    // `expire_time`. This option is only available when the `Registration`
    // is in state `EXPORTED`.
    //
    // To manage the domain's current billing and
    // renewal settings, go to [Google Domains](https://domains.google/).
    MANUAL_RENEWAL = 2;
  }

  // Output only. The renewal method for this `Registration`.
  RenewalMethod renewal_method = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Controls whether the domain can be transferred to another registrar.
  TransferLockState transfer_lock_state = 4;
}

// Defines the DNS configuration of a `Registration`, including name servers,
// DNSSEC, and glue records.
message DnsSettings {
  // Configuration for an arbitrary DNS provider.
  message CustomDns {
    // Required. A list of name servers that store the DNS zone for this domain. Each name
    // server is a domain name, with Unicode domain names expressed in
    // Punycode format.
    repeated string name_servers = 1 [(google.api.field_behavior) = REQUIRED];

    // The list of DS records for this domain, which are used to enable DNSSEC.
    // The domain's DNS provider can provide the values to set here. If this
    // field is empty, DNSSEC is disabled.
    repeated DsRecord ds_records = 2;
  }

  // Configuration for using the free DNS zone provided by Google Domains as a
  // `Registration`'s `dns_provider`. You cannot configure the DNS zone itself
  // using the API. To configure the DNS zone, go to
  // [Google Domains](https://domains.google/).
  message GoogleDomainsDns {
    // Output only. A list of name servers that store the DNS zone for this domain. Each name
    // server is a domain name, with Unicode domain names expressed in
    // Punycode format. This field is automatically populated with the name
    // servers assigned to the Google Domains DNS zone.
    repeated string name_servers = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Required. The state of DS records for this domain. Used to enable or disable
    // automatic DNSSEC.
    DsState ds_state = 2 [(google.api.field_behavior) = REQUIRED];

    // Output only. The list of DS records published for this domain. The list is
    // automatically populated when `ds_state` is `DS_RECORDS_PUBLISHED`,
    // otherwise it remains empty.
    repeated DsRecord ds_records = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Defines a Delegation Signer (DS) record, which is needed to enable DNSSEC
  // for a domain. It contains a digest (hash) of a DNSKEY record that must be
  // present in the domain's DNS zone.
  message DsRecord {
    // List of algorithms used to create a DNSKEY. Certain
    // algorithms are not supported for particular domains.
    enum Algorithm {
      // The algorithm is unspecified.
      ALGORITHM_UNSPECIFIED = 0;

      // RSA/MD5. Cannot be used for new deployments.
      RSAMD5 = 1;

      // Diffie-Hellman. Cannot be used for new deployments.
      DH = 2;

      // DSA/SHA1. Not recommended for new deployments.
      DSA = 3;

      // ECC. Not recommended for new deployments.
      ECC = 4;

      // RSA/SHA-1. Not recommended for new deployments.
      RSASHA1 = 5;

      // DSA-NSEC3-SHA1. Not recommended for new deployments.
      DSANSEC3SHA1 = 6;

      // RSA/SHA1-NSEC3-SHA1. Not recommended for new deployments.
      RSASHA1NSEC3SHA1 = 7;

      // RSA/SHA-256.
      RSASHA256 = 8;

      // RSA/SHA-512.
      RSASHA512 = 10;

      // GOST R 34.10-2001.
      ECCGOST = 12;

      // ECDSA Curve P-256 with SHA-256.
      ECDSAP256SHA256 = 13;

      // ECDSA Curve P-384 with SHA-384.
      ECDSAP384SHA384 = 14;

      // Ed25519.
      ED25519 = 15;

      // Ed448.
      ED448 = 16;

      // Reserved for Indirect Keys. Cannot be used for new deployments.
      INDIRECT = 252;

      // Private algorithm. Cannot be used for new deployments.
      PRIVATEDNS = 253;

      // Private algorithm OID. Cannot be used for new deployments.
      PRIVATEOID = 254;
    }

    // List of hash functions that may have been used to generate a digest of a
    // DNSKEY.
    enum DigestType {
      // The DigestType is unspecified.
      DIGEST_TYPE_UNSPECIFIED = 0;

      // SHA-1. Not recommended for new deployments.
      SHA1 = 1;

      // SHA-256.
      SHA256 = 2;

      // GOST R 34.11-94.
      GOST3411 = 3;

      // SHA-384.
      SHA384 = 4;
    }

    // The key tag of the record. Must be set in range 0 -- 65535.
    int32 key_tag = 1;

    // The algorithm used to generate the referenced DNSKEY.
    Algorithm algorithm = 2;

    // The hash function used to generate the digest of the referenced DNSKEY.
    DigestType digest_type = 3;

    // The digest generated from the referenced DNSKEY.
    string digest = 4;
  }

  // Defines a host on your domain that is a DNS name server for your domain
  // and/or other domains. Glue records are a way of making the IP address of a
  // name server known, even when it serves DNS queries for its parent domain.
  // For example, when `ns.example.com` is a name server for `example.com`, the
  // host `ns.example.com` must have a glue record to break the circular DNS
  // reference.
  message GlueRecord {
    // Required. Domain name of the host in Punycode format.
    string host_name = 1 [(google.api.field_behavior) = REQUIRED];

    // List of IPv4 addresses corresponding to this host in the standard decimal
    // format (e.g. `************`). At least one of `ipv4_address` and
    // `ipv6_address` must be set.
    repeated string ipv4_addresses = 2;

    // List of IPv6 addresses corresponding to this host in the standard
    // hexadecimal format (e.g. `2001:db8::`). At least one of
    // `ipv4_address` and `ipv6_address` must be set.
    repeated string ipv6_addresses = 3;
  }

  // The publication state of DS records for a `Registration`.
  enum DsState {
    // DS state is unspecified.
    DS_STATE_UNSPECIFIED = 0;

    // DNSSEC is disabled for this domain. No DS records for this domain are
    // published in the parent DNS zone.
    DS_RECORDS_UNPUBLISHED = 1;

    // DNSSEC is enabled for this domain. Appropriate DS records for this domain
    // are published in the parent DNS zone. This option is valid only if the
    // DNS zone referenced in the `Registration`'s `dns_provider` field is
    // already DNSSEC-signed.
    DS_RECORDS_PUBLISHED = 2;
  }

  // The DNS provider of the registration.
  oneof dns_provider {
    // An arbitrary DNS provider identified by its name servers.
    CustomDns custom_dns = 1;

    // The free DNS zone provided by
    // [Google Domains](https://domains.google/).
    GoogleDomainsDns google_domains_dns = 2;
  }

  // The list of glue records for this `Registration`. Commonly empty.
  repeated GlueRecord glue_records = 4;
}

// Defines the contact information associated with a `Registration`.
//
// [ICANN](https://icann.org/) requires all domain names to have associated
// contact information. The `registrant_contact` is considered the
// domain's legal owner, and often the other contacts are identical.
message ContactSettings {
  // Details required for a contact associated with a `Registration`.
  message Contact {
    // Required. Postal address of the contact.
    google.type.PostalAddress postal_address = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Email address of the contact.
    string email = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. Phone number of the contact in international format. For example,
    // `"******-555-0123"`.
    string phone_number = 3 [(google.api.field_behavior) = REQUIRED];

    // Fax number of the contact in international format. For example,
    // `"******-555-0123"`.
    string fax_number = 4;
  }

  // Required. Privacy setting for the contacts associated with the `Registration`.
  ContactPrivacy privacy = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The registrant contact for the `Registration`.
  //
  // *Caution: Anyone with access to this email address, phone number,
  // and/or postal address can take control of the domain.*
  //
  // *Warning: For new `Registration`s, the registrant receives an email
  // confirmation that they must complete within 15 days to avoid domain
  // suspension.*
  Contact registrant_contact = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The administrative contact for the `Registration`.
  Contact admin_contact = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The technical contact for the `Registration`.
  Contact technical_contact = 4 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `SearchDomains` method.
message SearchDomainsRequest {
  // Required. String used to search for available domain names.
  string query = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The location. Must be in the format `projects/*/locations/*`.
  string location = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// Response for the `SearchDomains` method.
message SearchDomainsResponse {
  // Results of the domain name search.
  repeated RegisterParameters register_parameters = 1;
}

// Request for the `RetrieveRegisterParameters` method.
message RetrieveRegisterParametersRequest {
  // Required. The domain name. Unicode domain names must be expressed in Punycode format.
  string domain_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The location. Must be in the format `projects/*/locations/*`.
  string location = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// Response for the `RetrieveRegisterParameters` method.
message RetrieveRegisterParametersResponse {
  // Parameters to use when calling the `RegisterDomain` method.
  RegisterParameters register_parameters = 1;
}

// Request for the `RegisterDomain` method.
message RegisterDomainRequest {
  // Required. The parent resource of the `Registration`. Must be in the
  // format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The complete `Registration` resource to be created.
  Registration registration = 2 [(google.api.field_behavior) = REQUIRED];

  // The list of domain notices that you acknowledge. Call
  // `RetrieveRegisterParameters` to see the notices that need acknowledgement.
  repeated DomainNotice domain_notices = 3;

  // The list of contact notices that the caller acknowledges. The notices
  // needed here depend on the values specified in
  // `registration.contact_settings`.
  repeated ContactNotice contact_notices = 4;

  // Required. Yearly price to register or renew the domain.
  // The value that should be put here can be obtained from
  // RetrieveRegisterParameters or SearchDomains calls.
  google.type.Money yearly_price = 5 [(google.api.field_behavior) = REQUIRED];

  // When true, only validation is performed, without actually registering
  // the domain. Follows:
  // https://cloud.google.com/apis/design/design_patterns#request_validation
  bool validate_only = 6;
}

// Request for the `RetrieveTransferParameters` method.
message RetrieveTransferParametersRequest {
  // Required. The domain name. Unicode domain names must be expressed in Punycode format.
  string domain_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The location. Must be in the format `projects/*/locations/*`.
  string location = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// Response for the `RetrieveTransferParameters` method.
message RetrieveTransferParametersResponse {
  // Parameters to use when calling the `TransferDomain` method.
  TransferParameters transfer_parameters = 1;
}

// Request for the `TransferDomain` method.
message TransferDomainRequest {
  // Required. The parent resource of the `Registration`. Must be in the
  // format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The complete `Registration` resource to be created.
  //
  // You can leave `registration.dns_settings` unset to import the
  // domain's current DNS configuration from its current registrar. Use this
  // option only if you are sure that the domain's current DNS service
  // does not cease upon transfer, as is often the case for DNS services
  // provided for free by the registrar.
  Registration registration = 2 [(google.api.field_behavior) = REQUIRED];

  // The list of contact notices that you acknowledge. The notices
  // needed here depend on the values specified in
  // `registration.contact_settings`.
  repeated ContactNotice contact_notices = 3;

  // Required. Acknowledgement of the price to transfer or renew the domain for one year.
  // Call `RetrieveTransferParameters` to obtain the price, which you must
  // acknowledge.
  google.type.Money yearly_price = 4 [(google.api.field_behavior) = REQUIRED];

  // The domain's transfer authorization code. You can obtain this from the
  // domain's current registrar.
  AuthorizationCode authorization_code = 5;

  // Validate the request without actually transferring the domain.
  bool validate_only = 6;
}

// Request for the `ListRegistrations` method.
message ListRegistrationsRequest {
  // Required. The project and location from which to list `Registration`s, specified in
  // the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of results to return.
  int32 page_size = 2;

  // When set to the `next_page_token` from a prior response, provides the next
  // page of results.
  string page_token = 3;

  // Filter expression to restrict the `Registration`s returned.
  //
  // The expression must specify the field name, a comparison operator, and the
  // value that you want to use for filtering. The value must be a string, a
  // number, a boolean, or an enum value. The comparison operator should be one
  // of =, !=, >, <, >=, <=, or : for prefix or wildcard matches.
  //
  // For example, to filter to a specific domain name, use an expression like
  // `domainName="example.com"`. You can also check for the existence of a
  // field; for example, to find domains using custom DNS settings, use an
  // expression like `dnsSettings.customDns:*`.
  //
  // You can also create compound filters by combining expressions with the
  // `AND` and `OR` operators. For example, to find domains that are suspended
  // or have specific issues flagged, use an expression like
  // `(state=SUSPENDED) OR (issue:*)`.
  string filter = 4;
}

// Response for the `ListRegistrations` method.
message ListRegistrationsResponse {
  // A list of `Registration`s.
  repeated Registration registrations = 1;

  // When present, there are more results to retrieve. Set `page_token` to this
  // value on a subsequent call to get the next page of results.
  string next_page_token = 2;
}

// Request for the `GetRegistration` method.
message GetRegistrationRequest {
  // Required. The name of the `Registration` to get, in the format
  // `projects/*/locations/*/registrations/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "domains.googleapis.com/Registration"
    }
  ];
}

// Request for the `UpdateRegistration` method.
message UpdateRegistrationRequest {
  // Fields of the `Registration` to update.
  Registration registration = 1;

  // Required. The field mask describing which fields to update as a comma-separated list.
  // For example, if only the labels are being updated, the `update_mask` is
  // `"labels"`.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `ConfigureManagementSettings` method.
message ConfigureManagementSettingsRequest {
  // Required. The name of the `Registration` whose management settings are being updated,
  // in the format `projects/*/locations/*/registrations/*`.
  string registration = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "domains.googleapis.com/Registration"
    }
  ];

  // Fields of the `ManagementSettings` to update.
  ManagementSettings management_settings = 2;

  // Required. The field mask describing which fields to update as a comma-separated list.
  // For example, if only the transfer lock is being updated, the `update_mask`
  // is `"transfer_lock_state"`.
  google.protobuf.FieldMask update_mask = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `ConfigureDnsSettings` method.
message ConfigureDnsSettingsRequest {
  // Required. The name of the `Registration` whose DNS settings are being updated,
  // in the format `projects/*/locations/*/registrations/*`.
  string registration = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "domains.googleapis.com/Registration"
    }
  ];

  // Fields of the `DnsSettings` to update.
  DnsSettings dns_settings = 2;

  // Required. The field mask describing which fields to update as a comma-separated list.
  // For example, if only the name servers are being updated for an existing
  // Custom DNS configuration, the `update_mask` is
  // `"custom_dns.name_servers"`.
  //
  // When changing the DNS provider from one type to another, pass the new
  // provider's field name as part of the field mask. For example, when changing
  // from a Google Domains DNS configuration to a Custom DNS configuration, the
  // `update_mask` is `"custom_dns"`. //
  google.protobuf.FieldMask update_mask = 3 [(google.api.field_behavior) = REQUIRED];

  // Validate the request without actually updating the DNS settings.
  bool validate_only = 4;
}

// Request for the `ConfigureContactSettings` method.
message ConfigureContactSettingsRequest {
  // Required. The name of the `Registration` whose contact settings are being updated,
  // in the format `projects/*/locations/*/registrations/*`.
  string registration = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "domains.googleapis.com/Registration"
    }
  ];

  // Fields of the `ContactSettings` to update.
  ContactSettings contact_settings = 2;

  // Required. The field mask describing which fields to update as a comma-separated list.
  // For example, if only the registrant contact is being updated, the
  // `update_mask` is `"registrant_contact"`.
  google.protobuf.FieldMask update_mask = 3 [(google.api.field_behavior) = REQUIRED];

  // The list of contact notices that the caller acknowledges. The notices
  // needed here depend on the values specified in `contact_settings`.
  repeated ContactNotice contact_notices = 4;

  // Validate the request without actually updating the contact settings.
  bool validate_only = 5;
}

// Request for the `ExportRegistration` method.
message ExportRegistrationRequest {
  // Required. The name of the `Registration` to export,
  // in the format `projects/*/locations/*/registrations/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "domains.googleapis.com/Registration"
    }
  ];
}

// Request for the `DeleteRegistration` method.
message DeleteRegistrationRequest {
  // Required. The name of the `Registration` to delete,
  // in the format `projects/*/locations/*/registrations/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "domains.googleapis.com/Registration"
    }
  ];
}

// Request for the `RetrieveAuthorizationCode` method.
message RetrieveAuthorizationCodeRequest {
  // Required. The name of the `Registration` whose authorization code is being retrieved,
  // in the format `projects/*/locations/*/registrations/*`.
  string registration = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "domains.googleapis.com/Registration"
    }
  ];
}

// Request for the `ResetAuthorizationCode` method.
message ResetAuthorizationCodeRequest {
  // Required. The name of the `Registration` whose authorization code is being reset,
  // in the format `projects/*/locations/*/registrations/*`.
  string registration = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "domains.googleapis.com/Registration"
    }
  ];
}

// Parameters required to register a new domain.
message RegisterParameters {
  // Possible availability states of a domain name.
  enum Availability {
    // The availability is unspecified.
    AVAILABILITY_UNSPECIFIED = 0;

    // The domain is available for registration.
    AVAILABLE = 1;

    // The domain is not available for registration. Generally this means it is
    // already registered to another party.
    UNAVAILABLE = 2;

    // The domain is not currently supported by Cloud Domains, but may
    // be available elsewhere.
    UNSUPPORTED = 3;

    // Cloud Domains is unable to determine domain availability, generally
    // due to system maintenance at the domain name registry.
    UNKNOWN = 4;
  }

  // The domain name. Unicode domain names are expressed in Punycode format.
  string domain_name = 1;

  // Indicates whether the domain is available for registration. This value is
  // accurate when obtained by calling `RetrieveRegisterParameters`, but is
  // approximate when obtained by calling `SearchDomains`.
  Availability availability = 2;

  // Contact privacy options that the domain supports.
  repeated ContactPrivacy supported_privacy = 3;

  // Notices about special properties of the domain.
  repeated DomainNotice domain_notices = 4;

  // Price to register or renew the domain for one year.
  google.type.Money yearly_price = 5;
}

// Parameters required to transfer a domain from another registrar.
message TransferParameters {
  // The domain name. Unicode domain names are expressed in Punycode format.
  string domain_name = 1;

  // The registrar that currently manages the domain.
  string current_registrar = 2;

  // The name servers that currently store the configuration of the domain.
  repeated string name_servers = 3;

  // Indicates whether the domain is protected by a transfer lock. For a
  // transfer to succeed, this must show `UNLOCKED`. To unlock a domain,
  // go to its current registrar.
  TransferLockState transfer_lock_state = 4;

  // Contact privacy options that the domain supports.
  repeated ContactPrivacy supported_privacy = 5;

  // Price to transfer or renew the domain for one year.
  google.type.Money yearly_price = 6;
}

// Defines an authorization code.
message AuthorizationCode {
  // The Authorization Code in ASCII. It can be used to transfer the domain
  // to or from another registrar.
  string code = 1;
}

// Represents the metadata of the long-running operation. Output only.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation, if any.
  string status_detail = 5;

  // API version used to start the operation.
  string api_version = 6;
}

// Defines a set of possible contact privacy settings for a `Registration`.
//
// [ICANN](https://icann.org/) maintains the WHOIS database, a publicly
// accessible mapping from domain name to contact information, and requires that
// each domain name have an entry. Choose from these options to control how much
// information in your `ContactSettings` is published.
enum ContactPrivacy {
  // The contact privacy settings are undefined.
  CONTACT_PRIVACY_UNSPECIFIED = 0;

  // All the data from `ContactSettings` is publicly available. When setting
  // this option, you must also provide a
  // `PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT` in the `contact_notices` field of the
  // request.
  PUBLIC_CONTACT_DATA = 1;

  // None of the data from `ContactSettings` is publicly available. Instead,
  // proxy contact data is published for your domain. Email sent to the proxy
  // email address is forwarded to the registrant's email address. Cloud Domains
  // provides this privacy proxy service at no additional cost.
  PRIVATE_CONTACT_DATA = 2;

  // Some data from `ContactSettings` is publicly available. The actual
  // information redacted depends on the domain. For details, see [the
  // registration privacy
  // article](https://support.google.com/domains/answer/3251242).
  REDACTED_CONTACT_DATA = 3;
}

// Notices about special properties of certain domains.
enum DomainNotice {
  // The notice is undefined.
  DOMAIN_NOTICE_UNSPECIFIED = 0;

  // Indicates that the domain is preloaded on the HTTP Strict Transport
  // Security list in browsers. Serving a website on such domain requires
  // an SSL certificate. For details, see
  // [how to get an SSL
  // certificate](https://support.google.com/domains/answer/7638036).
  HSTS_PRELOADED = 1;
}

// Notices related to contact information.
enum ContactNotice {
  // The notice is undefined.
  CONTACT_NOTICE_UNSPECIFIED = 0;

  // Required when setting the `privacy` field of `ContactSettings` to
  // `PUBLIC_CONTACT_DATA`, which exposes contact data publicly.
  PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT = 1;
}

// Possible states of a `Registration`'s transfer lock.
enum TransferLockState {
  // The state is unspecified.
  TRANSFER_LOCK_STATE_UNSPECIFIED = 0;

  // The domain is unlocked and can be transferred to another registrar.
  UNLOCKED = 1;

  // The domain is locked and cannot be transferred to another registrar.
  LOCKED = 2;
}
