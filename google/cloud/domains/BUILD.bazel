# This build file includes a target for the Ruby wrapper library for
# google-cloud-domains.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for domains.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "domains_ruby_wrapper",
    srcs = ["//google/cloud/domains/v1beta1:domains_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-domains",
        "ruby-cloud-env-prefix=DOMAINS",
        "ruby-cloud-wrapper-of=v1:0.6;v1beta1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/domains",
        "ruby-cloud-api-id=domains.googleapis.com",
        "ruby-cloud-api-shortname=domains",
    ],
    ruby_cloud_description = "The Cloud Domains API provides registration, management and configuration of domain names.",
    ruby_cloud_title = "Cloud Domains",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-domains-ruby",
    deps = [
        ":domains_ruby_wrapper",
    ],
)
