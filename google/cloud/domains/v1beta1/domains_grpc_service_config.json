{"methodConfig": [{"name": [{"service": "google.cloud.domains.v1beta1.DomainsService", "method": "SearchDomains"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "RetrieveRegisterParameters"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "RetrieveTransferParameters"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "ListRegistrations"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "GetRegistration"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "RetrieveAuthorizationCode"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.domains.v1beta1.DomainsService", "method": "RegisterDomain"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "TransferDomain"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "UpdateRegistration"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "ConfigureManagementSettings"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "ConfigureDnsSettings"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "ConfigureContactSettings"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "ExportRegistration"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "DeleteRegistration"}, {"service": "google.cloud.domains.v1beta1.DomainsService", "method": "ResetAuthorizationCode"}], "timeout": "60s"}]}