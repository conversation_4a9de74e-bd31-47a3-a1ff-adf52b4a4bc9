# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "domains_proto",
    srcs = [
        "domains.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:money_proto",
        "//google/type:postal_address_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "domains_proto_with_info",
    deps = [
        ":domains_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "domains_java_proto",
    deps = [":domains_proto"],
)

java_grpc_library(
    name = "domains_java_grpc",
    srcs = [":domains_proto"],
    deps = [":domains_java_proto"],
)

java_gapic_library(
    name = "domains_java_gapic",
    srcs = [":domains_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "domains_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "domains_v1beta1.yaml",
    test_deps = [
        ":domains_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":domains_java_proto",
        "//google/api:api_java_proto",
        "//google/type:type_java_proto",
    ],
)

java_gapic_test(
    name = "domains_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.domains.v1beta1.DomainsClientHttpJsonTest",
        "com.google.cloud.domains.v1beta1.DomainsClientTest",
    ],
    runtime_deps = [":domains_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-domains-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":domains_java_gapic",
        ":domains_java_grpc",
        ":domains_java_proto",
        ":domains_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "domains_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/domains/apiv1beta1/domainspb",
    protos = [":domains_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:money_go_proto",
        "//google/type:postaladdress_go_proto",
    ],
)

go_gapic_library(
    name = "domains_go_gapic",
    srcs = [":domains_proto_with_info"],
    grpc_service_config = "domains_grpc_service_config.json",
    importpath = "cloud.google.com/go/domains/apiv1beta1;domains",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "domains_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":domains_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-domains-v1beta1-go",
    deps = [
        ":domains_go_gapic",
        ":domains_go_gapic_srcjar-metadata.srcjar",
        ":domains_go_gapic_srcjar-snippets.srcjar",
        ":domains_go_gapic_srcjar-test.srcjar",
        ":domains_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "domains_py_gapic",
    srcs = [":domains_proto"],
    grpc_service_config = "domains_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "domains_v1beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "domains_py_gapic_test",
    srcs = [
        "domains_py_gapic_pytest.py",
        "domains_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":domains_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "domains-v1beta1-py",
    deps = [
        ":domains_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "domains_php_proto",
    deps = [":domains_proto"],
)

php_gapic_library(
    name = "domains_php_gapic",
    srcs = [":domains_proto_with_info"],
    grpc_service_config = "domains_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "domains_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":domains_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-domains-v1beta1-php",
    deps = [
        ":domains_php_gapic",
        ":domains_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "domains_nodejs_gapic",
    package_name = "@google-cloud/domains",
    src = ":domains_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "domains_grpc_service_config.json",
    package = "google.cloud.domains.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "domains_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "domains-v1beta1-nodejs",
    deps = [
        ":domains_nodejs_gapic",
        ":domains_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "domains_ruby_proto",
    deps = [":domains_proto"],
)

ruby_grpc_library(
    name = "domains_ruby_grpc",
    srcs = [":domains_proto"],
    deps = [":domains_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "domains_ruby_gapic",
    srcs = [":domains_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=domains.googleapis.com",
        "ruby-cloud-api-shortname=domains",
        "ruby-cloud-env-prefix=DOMAINS",
        "ruby-cloud-gem-name=google-cloud-domains-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/domains",
    ],
    grpc_service_config = "domains_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Cloud Domains API provides registration, management and configuration of domain names.",
    ruby_cloud_title = "Cloud Domains V1beta1",
    service_yaml = "domains_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":domains_ruby_grpc",
        ":domains_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-domains-v1beta1-ruby",
    deps = [
        ":domains_ruby_gapic",
        ":domains_ruby_grpc",
        ":domains_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "domains_csharp_proto",
    deps = [":domains_proto"],
)

csharp_grpc_library(
    name = "domains_csharp_grpc",
    srcs = [":domains_proto"],
    deps = [":domains_csharp_proto"],
)

csharp_gapic_library(
    name = "domains_csharp_gapic",
    srcs = [":domains_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "domains_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "domains_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":domains_csharp_grpc",
        ":domains_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-domains-v1beta1-csharp",
    deps = [
        ":domains_csharp_gapic",
        ":domains_csharp_grpc",
        ":domains_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
