type: google.api.Service
config_version: 3
name: speech.googleapis.com
title: Cloud Speech-to-Text API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.speech.v2.Speech
- name: google.longrunning.Operations

types:
- name: google.cloud.speech.v2.AutoDetectDecodingConfig
- name: google.cloud.speech.v2.BatchRecognizeResponse
- name: google.cloud.speech.v2.Config
- name: google.cloud.speech.v2.CustomClass
- name: google.cloud.speech.v2.ExplicitDecodingConfig
- name: google.cloud.speech.v2.LocationsMetadata
- name: google.cloud.speech.v2.OperationMetadata
- name: google.cloud.speech.v2.PhraseSet
- name: google.cloud.speech.v2.Recognizer
- name: google.cloud.speech.v2.StreamingRecognitionResult

documentation:
  summary: Converts audio to text by applying powerful neural network models.
  overview: |-
    # Introduction

    Google Cloud Speech API provides speech recognition as a service.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v2/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v2/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v2/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v2/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.speech.v2.Speech.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
