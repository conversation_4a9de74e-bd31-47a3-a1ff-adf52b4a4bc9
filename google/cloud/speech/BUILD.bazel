# This build file includes a target for the Ruby wrapper library for
# google-cloud-speech.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for speech.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "speech_ruby_wrapper",
    srcs = ["//google/cloud/speech/v1:speech_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-speech",
        "ruby-cloud-env-prefix=SPEECH",
        "ruby-cloud-wrapper-of=v1:0.16;v1p1beta1:0.20",
        "ruby-cloud-product-url=https://cloud.google.com/speech-to-text",
        "ruby-cloud-api-id=speech.googleapis.com",
        "ruby-cloud-api-shortname=speech",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Google Speech-to-Text enables developers to convert audio to text by applying powerful neural network models in an easy-to-use API. The API recognizes more than 120 languages and variants to support your global user base. You can enable voice command-and-control, transcribe audio from call centers, and more. It can process real-time streaming or prerecorded audio, using Google's machine learning technology.",
    ruby_cloud_title = "Cloud Speech-to-Text",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-speech-ruby",
    deps = [
        ":speech_ruby_wrapper",
    ],
)
