# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "speech_proto",
    srcs = [
        "cloud_speech.proto",
        "cloud_speech_adaptation.proto",
        "resource.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "speech_proto_with_info",
    deps = [
        ":speech_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "speech_java_proto",
    deps = [":speech_proto"],
)

java_grpc_library(
    name = "speech_java_grpc",
    srcs = [":speech_proto"],
    deps = [":speech_java_proto"],
)

java_gapic_library(
    name = "speech_java_gapic",
    srcs = [":speech_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "speech_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "speech_v1p1beta1.yaml",
    test_deps = [
        ":speech_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":speech_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "speech_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.speech.v1p1beta1.AdaptationClientHttpJsonTest",
        "com.google.cloud.speech.v1p1beta1.AdaptationClientTest",
        "com.google.cloud.speech.v1p1beta1.SpeechClientHttpJsonTest",
        "com.google.cloud.speech.v1p1beta1.SpeechClientTest",
    ],
    runtime_deps = [":speech_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-speech-v1p1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":speech_java_gapic",
        ":speech_java_grpc",
        ":speech_java_proto",
        ":speech_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "speech_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/speech/apiv1p1beta1/speechpb",
    protos = [":speech_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "speech_go_gapic",
    srcs = [":speech_proto_with_info"],
    grpc_service_config = "speech_grpc_service_config.json",
    importpath = "cloud.google.com/go/speech/apiv1p1beta1;speech",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "speech_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":speech_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-speech-v1p1beta1-go",
    deps = [
        ":speech_go_gapic",
        ":speech_go_gapic_srcjar-metadata.srcjar",
        ":speech_go_gapic_srcjar-snippets.srcjar",
        ":speech_go_gapic_srcjar-test.srcjar",
        ":speech_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "speech_py_gapic",
    srcs = [":speech_proto"],
    grpc_service_config = "speech_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "speech_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "speech_py_gapic_test",
    srcs = [
        "speech_py_gapic_pytest.py",
        "speech_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":speech_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "speech-v1p1beta1-py",
    deps = [
        ":speech_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "speech_php_proto",
    deps = [":speech_proto"],
)

php_gapic_library(
    name = "speech_php_gapic",
    srcs = [":speech_proto_with_info"],
    grpc_service_config = "speech_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "speech_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":speech_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-speech-v1p1beta1-php",
    deps = [
        ":speech_php_gapic",
        ":speech_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "speech_nodejs_gapic",
    package_name = "@google-cloud/speech",
    src = ":speech_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "speech_grpc_service_config.json",
    package = "google.cloud.speech.v1p1beta1",
    rest_numeric_enums = True,
    service_yaml = "speech_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "speech-v1p1beta1-nodejs",
    deps = [
        ":speech_nodejs_gapic",
        ":speech_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "speech_ruby_proto",
    deps = [":speech_proto"],
)

ruby_grpc_library(
    name = "speech_ruby_grpc",
    srcs = [":speech_proto"],
    deps = [":speech_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "speech_ruby_gapic",
    srcs = [":speech_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=speech.googleapis.com",
        "ruby-cloud-api-shortname=speech",
        "ruby-cloud-env-prefix=SPEECH",
        "ruby-cloud-gem-name=google-cloud-speech-v1p1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/speech-to-text",
    ],
    grpc_service_config = "speech_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Google Speech-to-Text enables developers to convert audio to text by applying powerful neural network models in an easy-to-use API. The API recognizes more than 120 languages and variants to support your global user base. You can enable voice command-and-control, transcribe audio from call centers, and more. It can process real-time streaming or prerecorded audio, using Google's machine learning technology.",
    ruby_cloud_title = "Cloud Speech-to-Text V1p1beta1",
    service_yaml = "speech_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":speech_ruby_grpc",
        ":speech_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-speech-v1p1beta1-ruby",
    deps = [
        ":speech_ruby_gapic",
        ":speech_ruby_grpc",
        ":speech_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "speech_csharp_proto",
    extra_opts = [],
    deps = [":speech_proto"],
)

csharp_grpc_library(
    name = "speech_csharp_grpc",
    srcs = [":speech_proto"],
    deps = [":speech_csharp_proto"],
)

csharp_gapic_library(
    name = "speech_csharp_gapic",
    srcs = [":speech_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "speech_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "speech_v1p1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":speech_csharp_grpc",
        ":speech_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-speech-v1p1beta1-csharp",
    deps = [
        ":speech_csharp_gapic",
        ":speech_csharp_grpc",
        ":speech_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "speech_cc_proto",
    deps = [":speech_proto"],
)

cc_grpc_library(
    name = "speech_cc_grpc",
    srcs = [":speech_proto"],
    grpc_only = True,
    deps = [":speech_cc_proto"],
)
