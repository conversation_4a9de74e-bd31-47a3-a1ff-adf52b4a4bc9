{"methodConfig": [{"name": [{"service": "google.cloud.speech.v1p1beta1.Speech", "method": "Recognize"}, {"service": "google.cloud.speech.v1p1beta1.Speech", "method": "StreamingRecognize"}], "timeout": "5000s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.speech.v1p1beta1.Speech", "method": "LongRunningRecognize"}], "timeout": "5000s"}]}