type: google.api.Service
config_version: 3
name: policysimulator.googleapis.com
title: Policy Simulator API

apis:
- name: google.cloud.policysimulator.v1.Simulator
- name: google.longrunning.Operations

types:
- name: google.cloud.policysimulator.v1.Replay
- name: google.cloud.policysimulator.v1.ReplayOperationMetadata

documentation:
  summary: |-
    Policy Simulator is a collection of endpoints for creating, running, and
    viewing a [Replay][google.cloud.policysimulator.v1.Replay]. A `Replay` is
    a type of simulation that lets you see how your members' access to
    resources might change if you changed your IAM policy.

    During a `Replay`, Policy Simulator re-evaluates, or replays, past access
    attempts under both the current policy and your proposed policy, and
    compares those results to determine how your members' access might change
    under the
    proposed policy.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=operations/**}'
    additional_bindings:
    - get: '/v1/{name=projects/*/locations/*/replays/*/operations/**}'
    - get: '/v1/{name=folders/*/locations/*/replays/*/operations/**}'
    - get: '/v1/{name=organizations/*/locations/*/replays/*/operations/**}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=operations}'
    additional_bindings:
    - get: '/v1/{name=projects/*/locations/*/replays/*/operations}'
    - get: '/v1/{name=folders/*/locations/*/replays/*/operations}'
    - get: '/v1/{name=organizations/*/locations/*/replays/*/operations}'

authentication:
  rules:
  - selector: 'google.cloud.policysimulator.v1.Simulator.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.ListOperations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=190865&template=1161103
  documentation_uri: https://cloud.google.com/policy-intelligence/docs/iam-simulator-overview
  api_short_name: policysimulator
  github_label: 'api: policysimulator'
  doc_tag_prefix: policysimulator
  organization: CLOUD
  library_settings:
  - version: google.cloud.policysimulator.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/policysimulator/docs/reference/rpc
