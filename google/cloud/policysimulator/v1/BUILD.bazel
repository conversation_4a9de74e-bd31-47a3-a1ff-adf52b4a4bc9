# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "policysimulator_proto",
    srcs = [
        "explanations.proto",
        "simulator.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:date_proto",
        "//google/type:expr_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "policysimulator_proto_with_info",
    deps = [
        ":policysimulator_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "policysimulator_java_proto",
    deps = [":policysimulator_proto"],
)

java_grpc_library(
    name = "policysimulator_java_grpc",
    srcs = [":policysimulator_proto"],
    deps = [":policysimulator_java_proto"],
)

java_gapic_library(
    name = "policysimulator_java_gapic",
    srcs = [":policysimulator_proto_with_info"],
    gapic_yaml = "policysimulator_gapic.yaml",
    grpc_service_config = "policysimulator_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "policysimulator_v1.yaml",
    test_deps = [
        "//google/iam/v1:iam_java_grpc",
        ":policysimulator_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":policysimulator_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "policysimulator_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.policysimulator.v1.SimulatorClientHttpJsonTest",
        "com.google.cloud.policysimulator.v1.SimulatorClientTest",
    ],
    runtime_deps = [":policysimulator_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-policysimulator-v1-java",
    transport = "grpc+rest",
    deps = [
        ":policysimulator_java_gapic",
        ":policysimulator_java_grpc",
        ":policysimulator_java_proto",
        ":policysimulator_proto",
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "policysimulator_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/policysimulator/apiv1/policysimulatorpb",
    protos = [":policysimulator_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:date_go_proto",
        "//google/type:expr_go_proto",
    ],
)

go_gapic_library(
    name = "policysimulator_go_gapic",
    srcs = [":policysimulator_proto_with_info"],
    grpc_service_config = "policysimulator_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/policysimulator/apiv1;policysimulator",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "policysimulator_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":policysimulator_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-policysimulator-v1-go",
    deps = [
        ":policysimulator_go_gapic",
        ":policysimulator_go_gapic_srcjar-test.srcjar",
        ":policysimulator_go_gapic_srcjar-metadata.srcjar",
        ":policysimulator_go_gapic_srcjar-snippets.srcjar",
        ":policysimulator_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "policysimulator_py_gapic",
    srcs = [":policysimulator_proto"],
    grpc_service_config = "policysimulator_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "policysimulator_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "policysimulator_py_gapic_test",
    srcs = [
        "policysimulator_py_gapic_pytest.py",
        "policysimulator_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":policysimulator_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "policysimulator-v1-py",
    deps = [
        ":policysimulator_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "policysimulator_php_proto",
    deps = [":policysimulator_proto"],
)

php_gapic_library(
    name = "policysimulator_php_gapic",
    srcs = [":policysimulator_proto_with_info"],
    grpc_service_config = "policysimulator_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "policysimulator_v1.yaml",
    gapic_yaml = "policysimulator_gapic.yaml",
    transport = "grpc+rest",
    deps = [
        ":policysimulator_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-policysimulator-v1-php",
    deps = [
        ":policysimulator_php_gapic",
        ":policysimulator_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "policysimulator_nodejs_gapic",
    package_name = "@google-cloud/policysimulator",
    src = ":policysimulator_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "policysimulator_v1_grpc_service_config.json",
    package = "google.cloud.policysimulator.v1",
    rest_numeric_enums = True,
    service_yaml = "policysimulator_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "policysimulator-v1-nodejs",
    deps = [
        ":policysimulator_nodejs_gapic",
        ":policysimulator_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "policysimulator_ruby_proto",
    deps = [":policysimulator_proto"],
)

ruby_grpc_library(
    name = "policysimulator_ruby_grpc",
    srcs = [":policysimulator_proto"],
    deps = [":policysimulator_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "policysimulator_ruby_gapic",
    srcs = [":policysimulator_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-policy_simulator-v1",
    ],
    grpc_service_config = "policysimulator_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "policysimulator_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":policysimulator_ruby_grpc",
        ":policysimulator_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-policysimulator-v1-ruby",
    deps = [
        ":policysimulator_ruby_gapic",
        ":policysimulator_ruby_grpc",
        ":policysimulator_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "policysimulator_csharp_proto",
    extra_opts = [],
    deps = [":policysimulator_proto"],
)

csharp_grpc_library(
    name = "policysimulator_csharp_grpc",
    srcs = [":policysimulator_proto"],
    deps = [":policysimulator_csharp_proto"],
)

csharp_gapic_library(
    name = "policysimulator_csharp_gapic",
    srcs = [":policysimulator_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "policysimulator_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "policysimulator_v1.yaml",
    deps = [
        ":policysimulator_csharp_grpc",
        ":policysimulator_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-policysimulator-v1-csharp",
    deps = [
        ":policysimulator_csharp_gapic",
        ":policysimulator_csharp_grpc",
        ":policysimulator_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "policysimulator_cc_proto",
    deps = [":policysimulator_proto"],
)

cc_grpc_library(
    name = "policysimulator_cc_grpc",
    srcs = [":policysimulator_proto"],
    grpc_only = True,
    deps = [":policysimulator_cc_proto"],
)
