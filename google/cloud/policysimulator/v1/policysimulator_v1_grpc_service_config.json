{"methodConfig": [{"name": [{"service": "google.cloud.policysimulator.v1.Simulator", "method": "GetReplay"}, {"service": "google.cloud.policysimulator.v1.Simulator", "method": "ListReplayResults"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.policysimulator.v1.Simulator", "method": "CreateReplay"}], "timeout": "60s"}]}