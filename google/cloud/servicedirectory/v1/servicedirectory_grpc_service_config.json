{"methodConfig": [{"name": [{"service": "google.cloud.servicedirectory.v1.RegistrationService"}], "timeout": "15s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "UNKNOWN"]}}, {"name": [{"service": "google.cloud.servicedirectory.v1.LookupService"}], "timeout": "15s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "UNKNOWN"]}}]}