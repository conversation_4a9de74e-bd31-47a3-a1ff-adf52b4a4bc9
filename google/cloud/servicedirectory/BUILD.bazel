# This build file includes a target for the Ruby wrapper library for
# google-cloud-service_directory.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for servicedirectory.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "servicedirectory_ruby_wrapper",
    srcs = ["//google/cloud/servicedirectory/v1:servicedirectory_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-service_directory",
        "ruby-cloud-env-prefix=SERVICE_DIRECTORY",
        "ruby-cloud-wrapper-of=v1:0.10;v1beta1:0.14",
        "ruby-cloud-product-url=https://cloud.google.com/service-directory",
        "ruby-cloud-api-id=servicedirectory.googleapis.com",
        "ruby-cloud-api-shortname=servicedirectory",
    ],
    ruby_cloud_description = "Service Directory is the single place to register, browse, and resolve application services.",
    ruby_cloud_title = "Service Directory",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-servicedirectory-ruby",
    deps = [
        ":servicedirectory_ruby_wrapper",
    ],
)
