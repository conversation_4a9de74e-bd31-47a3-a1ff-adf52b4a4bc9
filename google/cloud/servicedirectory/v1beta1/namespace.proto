// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.servicedirectory.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ServiceDirectory.V1Beta1";
option go_package = "cloud.google.com/go/servicedirectory/apiv1beta1/servicedirectorypb;servicedirectorypb";
option java_multiple_files = true;
option java_outer_classname = "NamespaceProto";
option java_package = "com.google.cloud.servicedirectory.v1beta1";
option php_namespace = "Google\\Cloud\\ServiceDirectory\\V1beta1";
option ruby_package = "Google::Cloud::ServiceDirectory::V1beta1";

// A container for [services][google.cloud.servicedirectory.v1beta1.Service].
// Namespaces allow administrators to group services together and define
// permissions for a collection of services.
message Namespace {
  option (google.api.resource) = {
    type: "servicedirectory.googleapis.com/Namespace"
    pattern: "projects/{project}/locations/{location}/namespaces/{namespace}"
  };

  // Immutable. The resource name for the namespace in the format
  // `projects/*/locations/*/namespaces/*`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Optional. Resource labels associated with this namespace.
  // No more than 64 user labels can be associated with a given resource. Label
  // keys and values can be no longer than 63 characters.
  map<string, string> labels = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The timestamp when the namespace was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the namespace was last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A globally unique identifier (in UUID4 format) for this
  // namespace.
  string uid = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}
