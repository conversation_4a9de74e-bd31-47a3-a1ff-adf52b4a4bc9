// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.servicedirectory.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/servicedirectory/v1beta1/service.proto";

option csharp_namespace = "Google.Cloud.ServiceDirectory.V1Beta1";
option go_package = "cloud.google.com/go/servicedirectory/apiv1beta1/servicedirectorypb;servicedirectorypb";
option java_multiple_files = true;
option java_outer_classname = "LookupServiceProto";
option java_package = "com.google.cloud.servicedirectory.v1beta1";
option php_namespace = "Google\\Cloud\\ServiceDirectory\\V1beta1";
option ruby_package = "Google::Cloud::ServiceDirectory::V1beta1";

// Service Directory API for looking up service data at runtime.
service LookupService {
  option (google.api.default_host) = "servicedirectory.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Returns a [service][google.cloud.servicedirectory.v1beta1.Service] and its
  // associated endpoints.
  // Resolving a service is not considered an active developer method.
  rpc ResolveService(ResolveServiceRequest) returns (ResolveServiceResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/namespaces/*/services/*}:resolve"
      body: "*"
    };
  }
}

// The request message for
// [LookupService.ResolveService][google.cloud.servicedirectory.v1beta1.LookupService.ResolveService].
// Looks up a service by its name, returns the service and its endpoints.
message ResolveServiceRequest {
  // Required. The name of the service to resolve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "servicedirectory.googleapis.com/Service"
    }
  ];

  // Optional. The maximum number of endpoints to return. Defaults to 25.
  // Maximum is 100. If a value less than one is specified, the Default is used.
  // If a value greater than the Maximum is specified, the Maximum is used.
  int32 max_endpoints = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter applied to the endpoints of the resolved service.
  //
  // General `filter` string syntax:
  // `<field> <operator> <value> (<logical connector>)`
  //
  // *   `<field>` can be `name`, `address`, `port`, or `metadata.<key>` for
  //     map field
  // *   `<operator>` can be `<`, `>`, `<=`, `>=`, `!=`, `=`, `:`. Of which `:`
  //     means `HAS`, and is roughly the same as `=`
  // *   `<value>` must be the same data type as field
  // *   `<logical connector>` can be `AND`, `OR`, `NOT`
  //
  // Examples of valid filters:
  //
  // *   `metadata.owner` returns endpoints that have a annotation with the key
  //     `owner`, this is the same as `metadata:owner`
  // *   `metadata.protocol=gRPC` returns endpoints that have key/value
  //     `protocol=gRPC`
  // *   `address=*************` returns endpoints that have this address
  // *   `port>8080` returns endpoints that have port number larger than 8080
  // *
  // `name>projects/my-project/locations/us-east1/namespaces/my-namespace/services/my-service/endpoints/endpoint-c`
  //     returns endpoints that have name that is alphabetically later than the
  //     string, so "endpoint-e" is returned but "endpoint-a" is not
  // *
  // `name=projects/my-project/locations/us-central1/namespaces/my-namespace/services/my-service/endpoints/ep-1`
  //      returns the endpoint that has an endpoint_id equal to `ep-1`
  // *   `metadata.owner!=sd AND metadata.foo=bar` returns endpoints that have
  //     `owner` in annotation key but value is not `sd` AND have key/value
  //      `foo=bar`
  // *   `doesnotexist.foo=bar` returns an empty list. Note that endpoint
  //     doesn't have a field called "doesnotexist". Since the filter does not
  //     match any endpoint, it returns no results
  //
  // For more information about filtering, see
  // [API Filtering](https://aip.dev/160).
  string endpoint_filter = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for
// [LookupService.ResolveService][google.cloud.servicedirectory.v1beta1.LookupService.ResolveService].
message ResolveServiceResponse {
  Service service = 1;
}
