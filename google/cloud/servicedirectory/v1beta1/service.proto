// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.servicedirectory.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/servicedirectory/v1beta1/endpoint.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ServiceDirectory.V1Beta1";
option go_package = "cloud.google.com/go/servicedirectory/apiv1beta1/servicedirectorypb;servicedirectorypb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.servicedirectory.v1beta1";
option php_namespace = "Google\\Cloud\\ServiceDirectory\\V1beta1";
option ruby_package = "Google::Cloud::ServiceDirectory::V1beta1";

// An individual service. A service contains a name and optional metadata.
// A service must exist before
// [endpoints][google.cloud.servicedirectory.v1beta1.Endpoint] can be
// added to it.
message Service {
  option (google.api.resource) = {
    type: "servicedirectory.googleapis.com/Service"
    pattern: "projects/{project}/locations/{location}/namespaces/{namespace}/services/{service}"
  };

  // Immutable. The resource name for the service in the format
  // `projects/*/locations/*/namespaces/*/services/*`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Optional. Metadata for the service. This data can be consumed by service
  // clients.
  //
  // Restrictions:
  //
  // *   The entire metadata dictionary may contain up to 2000 characters,
  //     spread accoss all key-value pairs. Metadata that goes beyond this
  //     limit are rejected
  // *   Valid metadata keys have two segments: an optional prefix and name,
  //     separated by a slash (/). The name segment is required and must be 63
  //     characters or less, beginning and ending with an alphanumeric character
  //     ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
  //     alphanumerics between. The prefix is optional. If specified, the prefix
  //     must be a DNS subdomain: a series of DNS labels separated by dots (.),
  //     not longer than 253 characters in total, followed by a slash (/).
  //     Metadata that fails to meet these requirements are rejected
  //
  // Note: This field is equivalent to the `annotations` field in the v1 API.
  // They have the same syntax and read/write to the same location in Service
  // Directory.
  map<string, string> metadata = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Endpoints associated with this service. Returned on
  // [LookupService.ResolveService][google.cloud.servicedirectory.v1beta1.LookupService.ResolveService].
  // Control plane clients should use
  // [RegistrationService.ListEndpoints][google.cloud.servicedirectory.v1beta1.RegistrationService.ListEndpoints].
  repeated Endpoint endpoints = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the service was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the service was last updated. Note:
  // endpoints being created/deleted/updated within the service are not
  // considered service updates for the purpose of this timestamp.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A globally unique identifier (in UUID4 format) for this
  // service.
  string uid = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}
