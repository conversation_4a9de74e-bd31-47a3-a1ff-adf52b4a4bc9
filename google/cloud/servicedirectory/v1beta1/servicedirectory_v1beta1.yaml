type: google.api.Service
config_version: 3
name: servicedirectory.googleapis.com
title: Service Directory API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.servicedirectory.v1beta1.LookupService
- name: google.cloud.servicedirectory.v1beta1.RegistrationService

documentation:
  summary: |-
    Service Directory is a platform for discovering, publishing, and connecting
    services.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1beta1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1beta1/{name=projects/*}/locations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.servicedirectory.v1beta1.LookupService.ResolveService
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.servicedirectory.v1beta1.RegistrationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
