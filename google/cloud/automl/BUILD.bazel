# This build file includes a target for the Ruby wrapper library for
# google-cloud-automl.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for automl.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "automl_ruby_wrapper",
    srcs = ["//google/cloud/automl/v1:automl_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-automl",
        "ruby-cloud-gem-namespace=Google::Cloud::AutoML",
        "ruby-cloud-env-prefix=AUTOML",
        "ruby-cloud-wrapper-of=v1:0.9;v1beta1:0.10",
        "ruby-cloud-product-url=https://cloud.google.com/automl",
        "ruby-cloud-api-id=automl.googleapis.com",
        "ruby-cloud-api-shortname=automl",
        "ruby-cloud-migration-version=1.0",
        "ruby-cloud-path-override=auto_ml=automl",
        "ruby-cloud-namespace-override=AutoMl=AutoML",
    ],
    ruby_cloud_description = "AutoML makes the power of machine learning available to you even if you have limited knowledge of machine learning. You can use AutoML to build on Google's machine learning capabilities to create your own custom machine learning models that are tailored to your business needs, and then integrate those models into your applications and web sites.",
    ruby_cloud_title = "Cloud AutoML",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-automl-ruby",
    deps = [
        ":automl_ruby_wrapper",
    ],
)
