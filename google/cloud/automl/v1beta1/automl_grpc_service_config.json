{"methodConfig": [{"name": [{"service": "google.cloud.automl.v1beta1.PredictionService", "method": "Predict"}, {"service": "google.cloud.automl.v1beta1.PredictionService", "method": "BatchPredict"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.automl.v1beta1.AutoMl", "method": "CreateDataset"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "UpdateDataset"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "ImportData"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "ExportData"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "CreateModel"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "DeployModel"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "UndeployModel"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "ExportModel"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "ExportEvaluatedExamples"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "ListModelEvaluations"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "UpdateTableSpec"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "UpdateColumnSpec"}], "timeout": "5s"}, {"name": [{"service": "google.cloud.automl.v1beta1.AutoMl", "method": "GetDataset"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "ListDatasets"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "DeleteDataset"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "GetModel"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "ListModels"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "DeleteModel"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "GetModelEvaluation"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "GetAnnotationSpec"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "GetTableSpec"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "ListTableSpecs"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "GetColumnSpec"}, {"service": "google.cloud.automl.v1beta1.AutoMl", "method": "ListColumnSpecs"}], "timeout": "5s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}