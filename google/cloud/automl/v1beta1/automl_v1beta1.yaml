type: google.api.Service
config_version: 3
name: automl.googleapis.com
title: Cloud AutoML API

apis:
- name: google.cloud.automl.v1beta1.AutoMl
- name: google.cloud.automl.v1beta1.PredictionService

types:
- name: google.cloud.automl.v1beta1.BatchPredictResult
- name: google.cloud.automl.v1beta1.OperationMetadata

documentation:
  summary: |-
    Train high-quality custom machine learning models with minimum effort and
    machine learning expertise.
  overview: |-
    Cloud AutoML is a suite of machine learning products that enables
    developers with limited machine learning expertise to train high-quality
    models specific to their business needs, by leveraging Google's
    state-of-the-art transfer learning, and Neural Architecture Search
    technology.
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: 'google.cloud.automl.v1beta1.AutoMl.*'
    deadline: 5.0
  - selector: google.cloud.automl.v1beta1.AutoMl.CreateModel
    deadline: 20.0
  - selector: google.cloud.automl.v1beta1.AutoMl.DeployModel
    deadline: 20.0
  - selector: google.cloud.automl.v1beta1.AutoMl.ImportData
    deadline: 20.0
  - selector: google.cloud.automl.v1beta1.AutoMl.ListDatasets
    deadline: 50.0
  - selector: google.cloud.automl.v1beta1.AutoMl.ListModelEvaluations
    deadline: 50.0
  - selector: google.cloud.automl.v1beta1.AutoMl.ListModels
    deadline: 50.0
  - selector: google.cloud.automl.v1beta1.PredictionService.BatchPredict
    deadline: 20.0
  - selector: google.cloud.automl.v1beta1.PredictionService.Predict
    deadline: 60.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 5.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 5.0
  - selector: google.longrunning.Operations.ListOperations
    deadline: 50.0
  - selector: google.longrunning.Operations.WaitOperation
    deadline: 300.0

http:
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1beta1/{resource=projects/*/locations/*}:getIamPolicy'
    additional_bindings:
    - get: '/v1beta1/{resource=projects/*/locations/*/datasets/*}:getIamPolicy'
    - get: '/v1beta1/{resource=projects/*/locations/*/models/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1beta1/{resource=projects/*/locations/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1beta1/{resource=projects/*/locations/*/datasets/*}:setIamPolicy'
      body: '*'
    - post: '/v1beta1/{resource=projects/*/locations/*/models/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1beta1/{resource=projects/*/locations/*/**}:testIamPermissions'
    body: '*'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1beta1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1beta1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1beta1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1beta1/{name=projects/*/locations/*}/operations'
  - selector: google.longrunning.Operations.WaitOperation
    post: '/v1beta1/{name=projects/*/locations/*/operations/*}:wait'
    body: '*'

authentication:
  rules:
  - selector: 'google.cloud.automl.v1beta1.AutoMl.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.automl.v1beta1.PredictionService.BatchPredict
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.automl.v1beta1.PredictionService.Predict
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
