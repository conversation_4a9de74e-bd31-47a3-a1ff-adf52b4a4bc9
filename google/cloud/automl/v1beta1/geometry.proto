// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.automl.v1beta1;


option go_package = "cloud.google.com/go/automl/apiv1beta1/automlpb;automlpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.automl.v1beta1";
option php_namespace = "Google\\Cloud\\AutoMl\\V1beta1";
option ruby_package = "Google::Cloud::AutoML::V1beta1";

// A vertex represents a 2D point in the image.
// The normalized vertex coordinates are between 0 to 1 fractions relative to
// the original plane (image, video). E.g. if the plane (e.g. whole image) would
// have size 10 x 20 then a point with normalized coordinates (0.1, 0.3) would
// be at the position (1, 6) on that plane.
message NormalizedVertex {
  // Required. Horizontal coordinate.
  float x = 1;

  // Required. Vertical coordinate.
  float y = 2;
}

// A bounding polygon of a detected object on a plane.
// On output both vertices and normalized_vertices are provided.
// The polygon is formed by connecting vertices in the order they are listed.
message BoundingPoly {
  // Output only . The bounding polygon normalized vertices.
  repeated NormalizedVertex normalized_vertices = 2;
}
