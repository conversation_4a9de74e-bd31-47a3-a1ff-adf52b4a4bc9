// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.automl.v1beta1;

import "google/cloud/automl/v1beta1/classification.proto";

option go_package = "cloud.google.com/go/automl/apiv1beta1/automlpb;automlpb";
option java_outer_classname = "TextSentimentProto";
option java_package = "com.google.cloud.automl.v1beta1";
option php_namespace = "Google\\Cloud\\AutoMl\\V1beta1";
option ruby_package = "Google::Cloud::AutoML::V1beta1";

// Contains annotation details specific to text sentiment.
message TextSentimentAnnotation {
  // Output only. The sentiment with the semantic, as given to the
  // [AutoMl.ImportData][google.cloud.automl.v1beta1.AutoMl.ImportData] when populating the dataset from which the model used
  // for the prediction had been trained.
  // The sentiment values are between 0 and
  // Dataset.text_sentiment_dataset_metadata.sentiment_max (inclusive),
  // with higher value meaning more positive sentiment. They are completely
  // relative, i.e. 0 means least positive sentiment and sentiment_max means
  // the most positive from the sentiments present in the train data. Therefore
  //  e.g. if train data had only negative sentiment, then sentiment_max, would
  // be still negative (although least negative).
  // The sentiment shouldn't be confused with "score" or "magnitude"
  // from the previous Natural Language Sentiment Analysis API.
  int32 sentiment = 1;
}

// Model evaluation metrics for text sentiment problems.
message TextSentimentEvaluationMetrics {
  // Output only. Precision.
  float precision = 1;

  // Output only. Recall.
  float recall = 2;

  // Output only. The harmonic mean of recall and precision.
  float f1_score = 3;

  // Output only. Mean absolute error. Only set for the overall model
  // evaluation, not for evaluation of a single annotation spec.
  float mean_absolute_error = 4;

  // Output only. Mean squared error. Only set for the overall model
  // evaluation, not for evaluation of a single annotation spec.
  float mean_squared_error = 5;

  // Output only. Linear weighted kappa. Only set for the overall model
  // evaluation, not for evaluation of a single annotation spec.
  float linear_kappa = 6;

  // Output only. Quadratic weighted kappa. Only set for the overall model
  // evaluation, not for evaluation of a single annotation spec.
  float quadratic_kappa = 7;

  // Output only. Confusion matrix of the evaluation.
  // Only set for the overall model evaluation, not for evaluation of a single
  // annotation spec.
  ClassificationEvaluationMetrics.ConfusionMatrix confusion_matrix = 8;

  // Output only. The annotation spec ids used for this evaluation.
  // Deprecated .
  repeated string annotation_spec_id = 9 [deprecated = true];
}
