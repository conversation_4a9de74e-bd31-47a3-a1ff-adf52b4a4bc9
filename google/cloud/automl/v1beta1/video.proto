// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.automl.v1beta1;

import "google/cloud/automl/v1beta1/classification.proto";

option go_package = "cloud.google.com/go/automl/apiv1beta1/automlpb;automlpb";
option java_multiple_files = true;
option java_outer_classname = "VideoProto";
option java_package = "com.google.cloud.automl.v1beta1";
option php_namespace = "Google\\Cloud\\AutoMl\\V1beta1";
option ruby_package = "Google::Cloud::AutoML::V1beta1";

// Dataset metadata specific to video classification.
// All Video Classification datasets are treated as multi label.
message VideoClassificationDatasetMetadata {

}

// Dataset metadata specific to video object tracking.
message VideoObjectTrackingDatasetMetadata {

}

// Model metadata specific to video classification.
message VideoClassificationModelMetadata {

}

// Model metadata specific to video object tracking.
message VideoObjectTrackingModelMetadata {

}
