// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.automl.v1beta1;


option go_package = "cloud.google.com/go/automl/apiv1beta1/automlpb;automlpb";
option java_multiple_files = true;
option java_outer_classname = "TextSegmentProto";
option java_package = "com.google.cloud.automl.v1beta1";
option php_namespace = "Google\\Cloud\\AutoMl\\V1beta1";
option ruby_package = "Google::Cloud::AutoML::V1beta1";

// A contiguous part of a text (string), assuming it has an UTF-8 NFC encoding.
message TextSegment {
  // Output only. The content of the TextSegment.
  string content = 3;

  // Required. Zero-based character index of the first character of the text
  // segment (counting characters from the beginning of the text).
  int64 start_offset = 1;

  // Required. Zero-based character index of the first character past the end of
  // the text segment (counting character from the beginning of the text).
  // The character at the end_offset is NOT included in the text segment.
  int64 end_offset = 2;
}
