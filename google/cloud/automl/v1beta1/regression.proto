// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.automl.v1beta1;


option go_package = "cloud.google.com/go/automl/apiv1beta1/automlpb;automlpb";
option java_outer_classname = "RegressionProto";
option java_package = "com.google.cloud.automl.v1beta1";
option php_namespace = "Google\\Cloud\\AutoMl\\V1beta1";
option ruby_package = "Google::Cloud::AutoML::V1beta1";

// Metrics for regression problems.
message RegressionEvaluationMetrics {
  // Output only. Root Mean Squared Error (RMSE).
  float root_mean_squared_error = 1;

  // Output only. Mean Absolute Error (MAE).
  float mean_absolute_error = 2;

  // Output only. Mean absolute percentage error. Only set if all ground truth
  // values are are positive.
  float mean_absolute_percentage_error = 3;

  // Output only. R squared.
  float r_squared = 4;

  // Output only. Root mean squared log error.
  float root_mean_squared_log_error = 5;
}
