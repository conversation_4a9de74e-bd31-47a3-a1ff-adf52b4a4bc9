// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.automl.v1beta1;

import "google/api/field_behavior.proto";
import "google/cloud/automl/v1beta1/data_items.proto";

option go_package = "cloud.google.com/go/automl/apiv1beta1/automlpb;automlpb";
option java_multiple_files = true;
option java_outer_classname = "TranslationProto";
option java_package = "com.google.cloud.automl.v1beta1";
option php_namespace = "Google\\Cloud\\AutoMl\\V1beta1";
option ruby_package = "Google::Cloud::AutoML::V1beta1";

// Dataset metadata that is specific to translation.
message TranslationDatasetMetadata {
  // Required. The BCP-47 language code of the source language.
  string source_language_code = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The BCP-47 language code of the target language.
  string target_language_code = 2 [(google.api.field_behavior) = REQUIRED];
}

// Evaluation metrics for the dataset.
message TranslationEvaluationMetrics {
  // Output only. BLEU score.
  double bleu_score = 1;

  // Output only. BLEU score for base model.
  double base_bleu_score = 2;
}

// Model metadata that is specific to translation.
message TranslationModelMetadata {
  // The resource name of the model to use as a baseline to train the custom
  // model. If unset, we use the default base model provided by Google
  // Translate. Format:
  // `projects/{project_id}/locations/{location_id}/models/{model_id}`
  string base_model = 1;

  // Output only. Inferred from the dataset.
  // The source languge (The BCP-47 language code) that is used for training.
  string source_language_code = 2;

  // Output only. The target languge (The BCP-47 language code) that is used for
  // training.
  string target_language_code = 3;
}

// Annotation details specific to translation.
message TranslationAnnotation {
  // Output only . The translated content.
  TextSnippet translated_content = 1;
}
