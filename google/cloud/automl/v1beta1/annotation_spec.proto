// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.automl.v1beta1;

import "google/api/resource.proto";

option go_package = "cloud.google.com/go/automl/apiv1beta1/automlpb;automlpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.automl.v1beta1";
option php_namespace = "Google\\Cloud\\AutoMl\\V1beta1";
option ruby_package = "Google::Cloud::AutoML::V1beta1";

// A definition of an annotation spec.
message AnnotationSpec {
  option (google.api.resource) = {
    type: "automl.googleapis.com/AnnotationSpec"
    pattern: "projects/{project}/locations/{location}/datasets/{dataset}/annotationSpecs/{annotation_spec}"
  };

  // Output only. Resource name of the annotation spec.
  // Form:
  //
  // 'projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/annotationSpecs/{annotation_spec_id}'
  string name = 1;

  // Required. The name of the annotation spec to show in the interface. The name can be
  // up to 32 characters long and must match the regexp `[a-zA-Z0-9_]+`.
  string display_name = 2;

  // Output only. The number of examples in the parent dataset
  // labeled by the annotation spec.
  int32 example_count = 9;
}
