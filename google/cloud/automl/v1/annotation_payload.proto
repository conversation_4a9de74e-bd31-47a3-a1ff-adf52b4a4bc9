// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.automl.v1;

import "google/cloud/automl/v1/classification.proto";
import "google/cloud/automl/v1/detection.proto";
import "google/cloud/automl/v1/text_extraction.proto";
import "google/cloud/automl/v1/text_sentiment.proto";
import "google/cloud/automl/v1/translation.proto";

option csharp_namespace = "Google.Cloud.AutoML.V1";
option go_package = "cloud.google.com/go/automl/apiv1/automlpb;automlpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.automl.v1";
option php_namespace = "Google\\Cloud\\AutoMl\\V1";
option ruby_package = "Google::Cloud::AutoML::V1";

// Contains annotation information that is relevant to AutoML.
message AnnotationPayload {
  // Output only . Additional information about the annotation
  // specific to the AutoML domain.
  oneof detail {
    // Annotation details for translation.
    TranslationAnnotation translation = 2;

    // Annotation details for content or image classification.
    ClassificationAnnotation classification = 3;

    // Annotation details for image object detection.
    ImageObjectDetectionAnnotation image_object_detection = 4;

    // Annotation details for text extraction.
    TextExtractionAnnotation text_extraction = 6;

    // Annotation details for text sentiment.
    TextSentimentAnnotation text_sentiment = 7;
  }

  // Output only . The resource ID of the annotation spec that
  // this annotation pertains to. The annotation spec comes from either an
  // ancestor dataset, or the dataset that was used to train the model in use.
  string annotation_spec_id = 1;

  // Output only. The value of
  // [display_name][google.cloud.automl.v1.AnnotationSpec.display_name]
  // when the model was trained. Because this field returns a value at model
  // training time, for different models trained using the same dataset, the
  // returned value could be different as model owner could update the
  // `display_name` between any two model training.
  string display_name = 5;
}
