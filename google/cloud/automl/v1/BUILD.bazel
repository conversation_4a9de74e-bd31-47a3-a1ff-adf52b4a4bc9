# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "automl_proto",
    srcs = [
        "annotation_payload.proto",
        "annotation_spec.proto",
        "classification.proto",
        "data_items.proto",
        "dataset.proto",
        "detection.proto",
        "geometry.proto",
        "image.proto",
        "io.proto",
        "model.proto",
        "model_evaluation.proto",
        "operations.proto",
        "prediction_service.proto",
        "service.proto",
        "text.proto",
        "text_extraction.proto",
        "text_segment.proto",
        "text_sentiment.proto",
        "translation.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "automl_proto_with_info",
    deps = [
        ":automl_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "automl_java_proto",
    deps = [":automl_proto"],
)

java_grpc_library(
    name = "automl_java_grpc",
    srcs = [":automl_proto"],
    deps = [":automl_java_proto"],
)

java_gapic_library(
    name = "automl_java_gapic",
    srcs = [":automl_proto_with_info"],
    grpc_service_config = "automl_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "automl_v1.yaml",
    test_deps = [
        ":automl_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":automl_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "automl_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.automl.v1.AutoMlClientHttpJsonTest",
        "com.google.cloud.automl.v1.AutoMlClientTest",
        "com.google.cloud.automl.v1.PredictionServiceClientHttpJsonTest",
        "com.google.cloud.automl.v1.PredictionServiceClientTest",
    ],
    runtime_deps = [":automl_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-automl-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":automl_java_gapic",
        ":automl_java_grpc",
        ":automl_java_proto",
        ":automl_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "automl_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/automl/apiv1/automlpb",
    protos = [":automl_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "automl_go_gapic",
    srcs = [":automl_proto_with_info"],
    grpc_service_config = "automl_grpc_service_config.json",
    importpath = "cloud.google.com/go/automl/apiv1;automl",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "automl_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":automl_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-automl-v1-go",
    deps = [
        ":automl_go_gapic",
        ":automl_go_gapic_srcjar-snippets.srcjar",
        ":automl_go_gapic_srcjar-test.srcjar",
        ":automl_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "automl_py_gapic",
    srcs = [":automl_proto"],
    grpc_service_config = "automl_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "automl_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "automl_py_gapic_test",
    srcs = [
        "automl_py_gapic_pytest.py",
        "automl_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":automl_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "automl-v1-py",
    deps = [
        ":automl_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "automl_php_proto",
    deps = [":automl_proto"],
)

php_gapic_library(
    name = "automl_php_gapic",
    srcs = [":automl_proto_with_info"],
    grpc_service_config = "automl_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "automl_v1.yaml",
    transport = "grpc+rest",
    deps = [":automl_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-automl-v1-php",
    deps = [
        ":automl_php_gapic",
        ":automl_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "automl_nodejs_gapic",
    package_name = "@google-cloud/automl",
    src = ":automl_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "automl_grpc_service_config.json",
    main_service = "automl",
    package = "google.cloud.automl.v1",
    rest_numeric_enums = True,
    service_yaml = "automl_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "automl-v1-nodejs",
    deps = [
        ":automl_nodejs_gapic",
        ":automl_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "automl_ruby_proto",
    deps = [":automl_proto"],
)

ruby_grpc_library(
    name = "automl_ruby_grpc",
    srcs = [":automl_proto"],
    deps = [":automl_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "automl_ruby_gapic",
    srcs = [":automl_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-automl-v1",
        "ruby-cloud-env-prefix=AUTOML",
        "ruby-cloud-path-override=auto_ml=automl",
        "ruby-cloud-namespace-override=AutoMl=AutoML;Automl=AutoML",
        "ruby-cloud-yard-strict=false",
        "ruby-cloud-product-url=https://cloud.google.com/automl",
        "ruby-cloud-api-id=automl.googleapis.com",
        "ruby-cloud-api-shortname=automl",
    ],
    grpc_service_config = "automl_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "AutoML makes the power of machine learning available to you even if you have limited knowledge of machine learning. You can use AutoML to build on Google's machine learning capabilities to create your own custom machine learning models that are tailored to your business needs, and then integrate those models into your applications and web sites.",
    ruby_cloud_title = "Cloud AutoML V1",
    service_yaml = "automl_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":automl_ruby_grpc",
        ":automl_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-automl-v1-ruby",
    deps = [
        ":automl_ruby_gapic",
        ":automl_ruby_grpc",
        ":automl_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "automl_csharp_proto",
    deps = [":automl_proto"],
)

csharp_grpc_library(
    name = "automl_csharp_grpc",
    srcs = [":automl_proto"],
    deps = [":automl_csharp_proto"],
)

csharp_gapic_library(
    name = "automl_csharp_gapic",
    srcs = [":automl_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "automl_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "automl_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":automl_csharp_grpc",
        ":automl_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-automl-v1-csharp",
    deps = [
        ":automl_csharp_gapic",
        ":automl_csharp_grpc",
        ":automl_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "automl_cc_proto",
    deps = [":automl_proto"],
)

cc_grpc_library(
    name = "automl_cc_grpc",
    srcs = [":automl_proto"],
    grpc_only = True,
    deps = [":automl_cc_proto"],
)
