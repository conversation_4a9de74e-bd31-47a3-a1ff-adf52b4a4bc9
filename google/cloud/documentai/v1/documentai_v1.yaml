type: google.api.Service
config_version: 3
name: documentai.googleapis.com
title: Cloud Document AI API

apis:
- name: google.cloud.documentai.v1.DocumentProcessorService
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.documentai.v1.BatchProcessMetadata
- name: google.cloud.documentai.v1.BatchProcessResponse
- name: google.cloud.documentai.v1.DeleteProcessorMetadata
- name: google.cloud.documentai.v1.DeleteProcessorVersionMetadata
- name: google.cloud.documentai.v1.DeployProcessorVersionMetadata
- name: google.cloud.documentai.v1.DeployProcessorVersionResponse
- name: google.cloud.documentai.v1.DisableProcessorMetadata
- name: google.cloud.documentai.v1.DisableProcessorResponse
- name: google.cloud.documentai.v1.EnableProcessorMetadata
- name: google.cloud.documentai.v1.EnableProcessorResponse
- name: google.cloud.documentai.v1.EvaluateProcessorVersionMetadata
- name: google.cloud.documentai.v1.EvaluateProcessorVersionResponse
- name: google.cloud.documentai.v1.ReviewDocumentOperationMetadata
- name: google.cloud.documentai.v1.ReviewDocumentResponse
- name: google.cloud.documentai.v1.SetDefaultProcessorVersionMetadata
- name: google.cloud.documentai.v1.SetDefaultProcessorVersionResponse
- name: google.cloud.documentai.v1.TrainProcessorVersionMetadata
- name: google.cloud.documentai.v1.TrainProcessorVersionResponse
- name: google.cloud.documentai.v1.UndeployProcessorVersionMetadata
- name: google.cloud.documentai.v1.UndeployProcessorVersionResponse

documentation:
  summary: |-
    Service to parse structured information from unstructured or
    semi-structured documents using state-of-the-art Google AI such as natural
    language, computer vision, translation, and AutoML.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
    additional_bindings:
    - get: '/uiv1beta3/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
    additional_bindings:
    - get: '/uiv1beta3/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    additional_bindings:
    - post: '/uiv1beta3/{name=projects/*/locations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/operations/*}'
    additional_bindings:
    - get: '/v1/{name=projects/*/locations/*/operations/*}'
    - get: '/uiv1beta3/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*/operations}'
    additional_bindings:
    - get: '/uiv1beta3/{name=projects/*/locations/*/operations}'

authentication:
  rules:
  - selector: 'google.cloud.documentai.v1.DocumentProcessorService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1132231&template=1639002
  documentation_uri: https://cloud.google.com/document-ai/docs
  api_short_name: documentai
  github_label: 'api: documentai'
  organization: CLOUD
  library_settings:
  - version: google.cloud.documentai.v1
    java_settings:
      common: {}
    cpp_settings:
      common: {}
    php_settings:
      common: {}
    python_settings:
      common: {}
    node_settings:
      common: {}
    dotnet_settings:
      common: {}
      ignored_resources:
      - documentai.googleapis.com/Location
    ruby_settings:
      common: {}
    go_settings:
      common: {}
  proto_reference_documentation_uri: https://cloud.google.com/document-ai/docs/reference/rpc
