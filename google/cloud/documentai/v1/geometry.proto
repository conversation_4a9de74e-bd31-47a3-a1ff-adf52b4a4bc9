// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.documentai.v1;

option csharp_namespace = "Google.Cloud.DocumentAI.V1";
option go_package = "cloud.google.com/go/documentai/apiv1/documentaipb;documentaipb";
option java_multiple_files = true;
option java_outer_classname = "GeometryProto";
option java_package = "com.google.cloud.documentai.v1";
option php_namespace = "Google\\Cloud\\DocumentAI\\V1";
option ruby_package = "Google::Cloud::DocumentAI::V1";

// A vertex represents a 2D point in the image.
// NOTE: the vertex coordinates are in the same scale as the original image.
message Vertex {
  // X coordinate.
  int32 x = 1;

  // Y coordinate (starts from the top of the image).
  int32 y = 2;
}

// A vertex represents a 2D point in the image.
// NOTE: the normalized vertex coordinates are relative to the original image
// and range from 0 to 1.
message NormalizedVertex {
  // X coordinate.
  float x = 1;

  // Y coordinate (starts from the top of the image).
  float y = 2;
}

// A bounding polygon for the detected image annotation.
message BoundingPoly {
  // The bounding polygon vertices.
  repeated Vertex vertices = 1;

  // The bounding polygon normalized vertices.
  repeated NormalizedVertex normalized_vertices = 2;
}
