// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.documentai.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/documentai/v1/document_schema.proto";
import "google/cloud/documentai/v1/evaluation.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DocumentAI.V1";
option go_package = "cloud.google.com/go/documentai/apiv1/documentaipb;documentaipb";
option java_multiple_files = true;
option java_outer_classname = "DocumentAiProcessor";
option java_package = "com.google.cloud.documentai.v1";
option php_namespace = "Google\\Cloud\\DocumentAI\\V1";
option ruby_package = "Google::Cloud::DocumentAI::V1";

// A processor version is an implementation of a processor. Each processor
// can have multiple versions, pretrained by Google internally or uptrained
// by the customer. A processor can only have one default version at a time.
// Its document-processing behavior is defined by that version.
message ProcessorVersion {
  option (google.api.resource) = {
    type: "documentai.googleapis.com/ProcessorVersion"
    pattern: "projects/{project}/locations/{location}/processors/{processor}/processorVersions/{processor_version}"
  };

  // Information about the upcoming deprecation of this processor version.
  message DeprecationInfo {
    // The time at which this processor version will be deprecated.
    google.protobuf.Timestamp deprecation_time = 1;

    // If set, the processor version that will be used as a replacement.
    string replacement_processor_version = 2
        [(google.api.resource_reference) = {
          type: "documentai.googleapis.com/ProcessorVersion"
        }];
  }

  // Information about Generative AI model-based processor versions.
  message GenAiModelInfo {
    // Information for a pretrained Google-managed foundation model.
    message FoundationGenAiModelInfo {
      // Whether finetuning is allowed for this base processor version.
      bool finetuning_allowed = 1;

      // The minimum number of labeled documents in the training dataset
      // required for finetuning.
      int32 min_train_labeled_documents = 2;
    }

    // Information for a custom Generative AI model created by the user. These
    // are created with `Create New Version` in either the `Call foundation
    // model` or `Fine tuning` tabs.
    message CustomGenAiModelInfo {
      // The type of custom model created by the user.
      enum CustomModelType {
        // The model type is unspecified.
        CUSTOM_MODEL_TYPE_UNSPECIFIED = 0;

        // The model is a versioned foundation model.
        VERSIONED_FOUNDATION = 1;

        // The model is a finetuned foundation model.
        FINE_TUNED = 2;
      }

      // The type of custom model created by the user.
      CustomModelType custom_model_type = 1;

      // The base processor version ID for the custom model.
      string base_processor_version_id = 2;
    }

    // The processor version is either a pretrained Google-managed foundation
    // model or a custom Generative AI model created by the user.
    oneof model_info {
      // Information for a pretrained Google-managed foundation model.
      FoundationGenAiModelInfo foundation_gen_ai_model_info = 1;

      // Information for a custom Generative AI model created by the user.
      CustomGenAiModelInfo custom_gen_ai_model_info = 2;
    }
  }

  // The possible states of the processor version.
  enum State {
    // The processor version is in an unspecified state.
    STATE_UNSPECIFIED = 0;

    // The processor version is deployed and can be used for processing.
    DEPLOYED = 1;

    // The processor version is being deployed.
    DEPLOYING = 2;

    // The processor version is not deployed and cannot be used for processing.
    UNDEPLOYED = 3;

    // The processor version is being undeployed.
    UNDEPLOYING = 4;

    // The processor version is being created.
    CREATING = 5;

    // The processor version is being deleted.
    DELETING = 6;

    // The processor version failed and is in an indeterminate state.
    FAILED = 7;

    // The processor version is being imported.
    IMPORTING = 8;
  }

  // The possible model types of the processor version.
  enum ModelType {
    // The processor version has unspecified model type.
    MODEL_TYPE_UNSPECIFIED = 0;

    // The processor version has generative model type.
    MODEL_TYPE_GENERATIVE = 1;

    // The processor version has custom model type.
    MODEL_TYPE_CUSTOM = 2;
  }

  // Identifier. The resource name of the processor version.
  // Format:
  // `projects/{project}/locations/{location}/processors/{processor}/processorVersions/{processor_version}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The display name of the processor version.
  string display_name = 2;

  // The schema of the processor version. Describes the output.
  DocumentSchema document_schema = 12;

  // Output only. The state of the processor version.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The time the processor version was created.
  google.protobuf.Timestamp create_time = 7;

  // The most recently invoked evaluation for the processor version.
  EvaluationReference latest_evaluation = 8;

  // The KMS key name used for encryption.
  string kms_key_name = 9;

  // The KMS key version with which data is encrypted.
  string kms_key_version_name = 10;

  // Output only. Denotes that this `ProcessorVersion` is managed by Google.
  bool google_managed = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // If set, information about the eventual deprecation of this version.
  DeprecationInfo deprecation_info = 13;

  // Output only. The model type of this processor version.
  ModelType model_type = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Information about Generative AI model-based processor
  // versions.
  GenAiModelInfo gen_ai_model_info = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains the alias and the aliased resource name of processor version.
message ProcessorVersionAlias {
  // The alias in the form of `processor_version` resource name.
  string alias = 1;

  // The resource name of aliased processor version.
  string processor_version = 2 [(google.api.resource_reference) = {
    type: "documentai.googleapis.com/ProcessorVersion"
  }];
}

// The first-class citizen for Document AI. Each processor defines how to
// extract structural information from a document.
message Processor {
  option (google.api.resource) = {
    type: "documentai.googleapis.com/Processor"
    pattern: "projects/{project}/locations/{location}/processors/{processor}"
  };

  // The possible states of the processor.
  enum State {
    // The processor is in an unspecified state.
    STATE_UNSPECIFIED = 0;

    // The processor is enabled, i.e., has an enabled version which can
    // currently serve processing requests and all the feature dependencies have
    // been successfully initialized.
    ENABLED = 1;

    // The processor is disabled.
    DISABLED = 2;

    // The processor is being enabled, will become `ENABLED` if successful.
    ENABLING = 3;

    // The processor is being disabled, will become `DISABLED` if successful.
    DISABLING = 4;

    // The processor is being created, will become either `ENABLED` (for
    // successful creation) or `FAILED` (for failed ones).
    // Once a processor is in this state, it can then be used for document
    // processing, but the feature dependencies of the processor might not be
    // fully created yet.
    CREATING = 5;

    // The processor failed during creation or initialization of feature
    // dependencies. The user should delete the processor and recreate one as
    // all the functionalities of the processor are disabled.
    FAILED = 6;

    // The processor is being deleted, will be removed if successful.
    DELETING = 7;
  }

  // Output only. Immutable. The resource name of the processor.
  // Format: `projects/{project}/locations/{location}/processors/{processor}`
  string name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // The processor type, such as: `OCR_PROCESSOR`, `INVOICE_PROCESSOR`.
  // To get a list of processor types, see
  // [FetchProcessorTypes][google.cloud.documentai.v1.DocumentProcessorService.FetchProcessorTypes].
  string type = 2;

  // The display name of the processor.
  string display_name = 3;

  // Output only. The state of the processor.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The default processor version.
  string default_processor_version = 9 [(google.api.resource_reference) = {
    type: "documentai.googleapis.com/ProcessorVersion"
  }];

  // Output only. The processor version aliases.
  repeated ProcessorVersionAlias processor_version_aliases = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Immutable. The http endpoint that can be called to invoke
  // processing.
  string process_endpoint = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The time the processor was created.
  google.protobuf.Timestamp create_time = 7;

  // The [KMS key](https://cloud.google.com/security-key-management) used for
  // encryption and decryption in CMEK scenarios.
  string kms_key_name = 8;

  // Output only. Reserved for future use.
  bool satisfies_pzs = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzi = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}
