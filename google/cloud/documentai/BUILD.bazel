# This build file includes a target for the Ruby wrapper library for
# google-cloud-document_ai.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for documentai.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "documentai_ruby_wrapper",
    srcs = ["//google/cloud/documentai/v1:documentai_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-document_ai",
        "ruby-cloud-env-prefix=DOCUMENT_AI",
        "ruby-cloud-wrapper-of=v1:0.17;v1beta3:0.30",
        "ruby-cloud-product-url=https://cloud.google.com/document-ai/docs",
        "ruby-cloud-api-id=documentai.googleapis.com",
        "ruby-cloud-api-shortname=documentai",
        "ruby-cloud-namespace-override=DocumentAi=DocumentAI",
    ],
    ruby_cloud_description = "Document AI uses machine learning on a single cloud-based platform to automatically classify, extract, and enrich data within your documents to unlock insights.",
    ruby_cloud_title = "Document AI",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-documentai-ruby",
    deps = [
        ":documentai_ruby_wrapper",
    ],
)
