// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.documentai.v1beta3;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DocumentAI.V1Beta3";
option go_package = "cloud.google.com/go/documentai/apiv1beta3/documentaipb;documentaipb";
option java_multiple_files = true;
option java_outer_classname = "OperationMetadataProto";
option java_package = "com.google.cloud.documentai.v1beta3";
option php_namespace = "Google\\Cloud\\DocumentAI\\V1beta3";
option ruby_package = "Google::Cloud::DocumentAI::V1beta3";

// The common metadata for long running operations.
message CommonOperationMetadata {
  // State of the longrunning operation.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // Operation is still running.
    RUNNING = 1;

    // Operation is being cancelled.
    CANCELLING = 2;

    // Operation succeeded.
    SUCCEEDED = 3;

    // Operation failed.
    FAILED = 4;

    // Operation is cancelled.
    CANCELLED = 5;
  }

  // The state of the operation.
  State state = 1;

  // A message providing more details about the current state of processing.
  string state_message = 2;

  // A related resource to this operation.
  string resource = 5;

  // The creation time of the operation.
  google.protobuf.Timestamp create_time = 3;

  // The last update time of the operation.
  google.protobuf.Timestamp update_time = 4;
}
