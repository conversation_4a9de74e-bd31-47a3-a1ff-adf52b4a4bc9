# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "documentai_proto",
    srcs = [
        "barcode.proto",
        "dataset.proto",
        "document.proto",
        "document_io.proto",
        "document_processor_service.proto",
        "document_schema.proto",
        "document_service.proto",
        "evaluation.proto",
        "geometry.proto",
        "operation_metadata.proto",
        "processor.proto",
        "processor_type.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:launch_stage_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:color_proto",
        "//google/type:date_proto",
        "//google/type:datetime_proto",
        "//google/type:money_proto",
        "//google/type:postal_address_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "documentai_proto_with_info",
    deps = [
        ":documentai_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "documentai_java_proto",
    deps = [":documentai_proto"],
)

java_grpc_library(
    name = "documentai_java_grpc",
    srcs = [":documentai_proto"],
    deps = [":documentai_java_proto"],
)

java_gapic_library(
    name = "documentai_java_gapic",
    srcs = [":documentai_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "documentai_v1beta3_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "documentai_v1beta3.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        ":documentai_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":documentai_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "documentai_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.documentai.v1beta3.DocumentProcessorServiceClientHttpJsonTest",
        "com.google.cloud.documentai.v1beta3.DocumentProcessorServiceClientTest",
        "com.google.cloud.documentai.v1beta3.DocumentServiceClientHttpJsonTest",
        "com.google.cloud.documentai.v1beta3.DocumentServiceClientTest",
    ],
    runtime_deps = [":documentai_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-documentai-v1beta3-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":documentai_java_gapic",
        ":documentai_java_grpc",
        ":documentai_java_proto",
        ":documentai_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "documentai_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/documentai/apiv1beta3/documentaipb",
    protos = [":documentai_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:api_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:color_go_proto",
        "//google/type:date_go_proto",
        "//google/type:datetime_go_proto",
        "//google/type:money_go_proto",
        "//google/type:postaladdress_go_proto",
    ],
)

go_gapic_library(
    name = "documentai_go_gapic",
    srcs = [":documentai_proto_with_info"],
    grpc_service_config = "documentai_v1beta3_grpc_service_config.json",
    importpath = "cloud.google.com/go/documentai/apiv1beta3;documentai",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "documentai_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [
        ":documentai_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-documentai-v1beta3-go",
    deps = [
        ":documentai_go_gapic",
        ":documentai_go_gapic_srcjar-metadata.srcjar",
        ":documentai_go_gapic_srcjar-snippets.srcjar",
        ":documentai_go_gapic_srcjar-test.srcjar",
        ":documentai_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "documentai_py_gapic",
    srcs = [":documentai_proto"],
    grpc_service_config = "documentai_v1beta3_grpc_service_config.json",
    opt_args = ["autogen-snippets"],
    rest_numeric_enums = True,
    service_yaml = "documentai_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "documentai_py_gapic_test",
    srcs = [
        "documentai_py_gapic_pytest.py",
        "documentai_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":documentai_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "documentai-v1beta3-py",
    deps = [
        ":documentai_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "documentai_php_proto",
    deps = [":documentai_proto"],
)

php_gapic_library(
    name = "documentai_php_gapic",
    srcs = [":documentai_proto_with_info"],
    grpc_service_config = "documentai_v1beta3_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "documentai_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [
        ":documentai_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-documentai-v1beta3-php",
    deps = [
        ":documentai_php_gapic",
        ":documentai_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "documentai_nodejs_gapic",
    package_name = "@google-cloud/documentai",
    src = ":documentai_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "documentai_v1beta3_grpc_service_config.json",
    package = "google.cloud.documentai.v1beta3",
    rest_numeric_enums = True,
    service_yaml = "documentai_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "documentai-v1beta3-nodejs",
    deps = [
        ":documentai_nodejs_gapic",
        ":documentai_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "documentai_ruby_proto",
    deps = [":documentai_proto"],
)

ruby_grpc_library(
    name = "documentai_ruby_grpc",
    srcs = [":documentai_proto"],
    deps = [":documentai_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "documentai_ruby_gapic",
    srcs = [":documentai_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-env-prefix=DOCUMENT_AI",
        "ruby-cloud-gem-name=google-cloud-document_ai-v1beta3",
        "ruby-cloud-namespace-override=DocumentAi=DocumentAI",
    ],
    grpc_service_config = "documentai_v1beta3_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Document AI uses machine learning on a single cloud-based platform to automatically classify, extract, and enrich data within your documents to unlock insights.",
    ruby_cloud_title = "Document AI V1beta3",
    service_yaml = "documentai_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [
        ":documentai_ruby_grpc",
        ":documentai_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-documentai-v1beta3-ruby",
    deps = [
        ":documentai_ruby_gapic",
        ":documentai_ruby_grpc",
        ":documentai_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "documentai_csharp_proto",
    extra_opts = [""],
    deps = [":documentai_proto"],
)

csharp_grpc_library(
    name = "documentai_csharp_grpc",
    srcs = [":documentai_proto"],
    deps = [":documentai_csharp_proto"],
)

csharp_gapic_library(
    name = "documentai_csharp_gapic",
    srcs = [":documentai_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "documentai_v1beta3_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "documentai_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [
        ":documentai_csharp_grpc",
        ":documentai_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-documentai-v1beta3-csharp",
    deps = [
        ":documentai_csharp_gapic",
        ":documentai_csharp_grpc",
        ":documentai_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "documentai_cc_proto",
    deps = [":documentai_proto"],
)

cc_grpc_library(
    name = "documentai_cc_grpc",
    srcs = [":documentai_proto"],
    grpc_only = True,
    deps = [":documentai_cc_proto"],
)
