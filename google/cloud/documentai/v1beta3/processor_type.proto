// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.documentai.v1beta3;

import "google/api/launch_stage.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.DocumentAI.V1Beta3";
option go_package = "cloud.google.com/go/documentai/apiv1beta3/documentaipb;documentaipb";
option java_multiple_files = true;
option java_outer_classname = "DocumentAiProcessorType";
option java_package = "com.google.cloud.documentai.v1beta3";
option php_namespace = "Google\\Cloud\\DocumentAI\\V1beta3";
option ruby_package = "Google::Cloud::DocumentAI::V1beta3";

// A processor type is responsible for performing a certain document
// understanding task on a certain type of document.
message ProcessorType {
  option (google.api.resource) = {
    type: "documentai.googleapis.com/ProcessorType"
    pattern: "projects/{project}/locations/{location}/processorTypes/{processor_type}"
  };

  // The location information about where the processor is available.
  message LocationInfo {
    // The location ID. For supported locations, refer to [regional and
    // multi-regional support](/document-ai/docs/regions).
    string location_id = 1;
  }

  // The resource name of the processor type.
  // Format: `projects/{project}/processorTypes/{processor_type}`
  string name = 1;

  // The processor type, such as: `OCR_PROCESSOR`, `INVOICE_PROCESSOR`.
  string type = 2;

  // The processor category, used by UI to group processor types.
  string category = 3;

  // The locations in which this processor is available.
  repeated LocationInfo available_locations = 4;

  // Whether the processor type allows creation. If true, users can create a
  // processor of this processor type. Otherwise, users need to request access.
  bool allow_creation = 6;

  // Launch stage of the processor type
  google.api.LaunchStage launch_stage = 8;

  // A set of Cloud Storage URIs of sample documents for this processor.
  repeated string sample_document_uris = 9;
}
