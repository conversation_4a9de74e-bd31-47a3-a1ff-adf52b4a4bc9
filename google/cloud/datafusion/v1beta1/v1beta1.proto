// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datafusion.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.DataFusion.V1Beta1";
option go_package = "cloud.google.com/go/datafusion/apiv1beta1/datafusionpb;datafusionpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datafusion.v1beta1";
option php_namespace = "Google\\Cloud\\DataFusion\\V1beta1";
option ruby_package = "Google::Cloud::DataFusion::V1beta1";
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};

// Service for creating and managing Data Fusion instances.
// Data Fusion enables ETL developers to build code-free, data integration
// pipelines via a point-and-click UI.
service DataFusion {
  option (google.api.default_host) = "datafusion.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists possible versions for Data Fusion instances in the specified project
  // and location.
  rpc ListAvailableVersions(ListAvailableVersionsRequest) returns (ListAvailableVersionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/versions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists Data Fusion instances in the specified project and location.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/instances"
    };
  }

  // Gets details of a single Data Fusion instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/instances/*}"
    };
  }

  // Creates a new Data Fusion instance in the specified project and location.
  rpc CreateInstance(CreateInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance,instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Data Fusion instance.
  rpc DeleteInstance(DeleteInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a single Data Fusion instance.
  rpc UpdateInstance(UpdateInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{instance.name=projects/*/locations/*/instances/*}"
      body: "instance"
    };
    option (google.api.method_signature) = "instance,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Restart a single Data Fusion instance.
  // At the end of an operation instance is fully restarted.
  rpc RestartInstance(RestartInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/instances/*}:restart"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Upgrade a single Data Fusion instance.
  // At the end of an operation instance is fully upgraded.
  rpc UpgradeInstance(UpgradeInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/instances/*}:upgrade"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Remove IAM policy that is currently set on the given resource.
  rpc RemoveIamPolicy(RemoveIamPolicyRequest) returns (RemoveIamPolicyResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/locations/*/**}:removeIamPolicy"
      body: "*"
    };
  }

  // List namespaces in a given instance
  rpc ListNamespaces(ListNamespacesRequest) returns (ListNamespacesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/instances/*}/namespaces"
    };
    option (google.api.method_signature) = "parent";
  }

  // Add DNS peering on the given resource.
  rpc AddDnsPeering(AddDnsPeeringRequest) returns (AddDnsPeeringResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/instances/*}/dnsPeerings:add"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }

  // Remove DNS peering on the given resource.
  rpc RemoveDnsPeering(RemoveDnsPeeringRequest) returns (RemoveDnsPeeringResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/instances/*}/dnsPeerings:remove"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }

  // List DNS peering for a given resource.
  rpc ListDnsPeerings(ListDnsPeeringsRequest) returns (ListDnsPeeringsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/instances/*}/dnsPeerings:list"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Network configuration for a Data Fusion instance. These configurations
// are used for peering with the customer network. Configurations are optional
// when a public Data Fusion instance is to be created. However, providing
// these configurations allows several benefits, such as reduced network latency
// while accessing the customer resources from managed Data Fusion instance
// nodes, as well as access to the customer on-prem resources.
message NetworkConfig {
  // Name of the network in the customer project with which the Tenant Project
  // will be peered for executing pipelines. In case of shared VPC where the
  // network resides in another host project the network should specified in
  // the form of projects/{host-project-id}/global/networks/{network}
  string network = 1;

  // The IP range in CIDR notation to use for the managed Data Fusion instance
  // nodes. This range must not overlap with any other ranges used in the Data
  // Fusion instance network.
  string ip_allocation = 2;
}

// The Data Fusion version.
message Version {
  // Each type represents the release availability of a CDF version
  enum Type {
    // Version does not have availability yet
    TYPE_UNSPECIFIED = 0;

    // Version is under development and not considered stable
    TYPE_PREVIEW = 1;

    // Version is available for public use
    TYPE_GENERAL_AVAILABILITY = 2;
  }

  // The version number of the Data Fusion instance, such as '*******'.
  string version_number = 1;

  // Whether this is currently the default version for Cloud Data Fusion
  bool default_version = 2;

  // Represents a list of available feature names for a given version.
  repeated string available_features = 3;

  // Type represents the release availability of the version
  Type type = 4;
}

// Identifies Data Fusion accelerators for an instance.
message Accelerator {
  // Each type represents an Accelerator (Add-On) supported by Cloud Data Fusion
  // service.
  enum AcceleratorType {
    // Default value, if unspecified.
    ACCELERATOR_TYPE_UNSPECIFIED = 0;

    // Change Data Capture accelerator for CDF.
    CDC = 1;

    // Cloud Healthcare accelerator for CDF. This accelerator is to enable Cloud
    // Healthcare specific CDF plugins developed by Healthcare team.
    HEALTHCARE = 2;
  }

  // The type of an accelator for a CDF instance.
  AcceleratorType accelerator_type = 1;
}

// The crypto key configuration. This field is used by the Customer-managed
// encryption keys (CMEK) feature.
message CryptoKeyConfig {
  // The name of the key which is used to encrypt/decrypt customer data. For key
  // in Cloud KMS, the key should be in the format of
  // `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
  string key_reference = 1 [(google.api.resource_reference) = {
                              type: "cloudkms.googleapis.com/CryptoKey"
                            }];
}

// Represents a Data Fusion instance.
message Instance {
  option (google.api.resource) = {
    type: "datafusion.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/instances/{instance}"
  };

  // Represents the type of Data Fusion instance. Each type is configured with
  // the default settings for processing and memory.
  enum Type {
    // No type specified. The instance creation will fail.
    TYPE_UNSPECIFIED = 0;

    // Basic Data Fusion instance. In Basic type, the user will be able to
    // create data pipelines using point and click UI. However, there are
    // certain limitations, such as fewer number of concurrent pipelines, no
    // support for streaming pipelines, etc.
    BASIC = 1;

    // Enterprise Data Fusion instance. In Enterprise type, the user will have
    // all features available, such as support for streaming pipelines,
    // unlimited number of concurrent pipelines, etc.
    ENTERPRISE = 2;

    // Developer Data Fusion instance. In Developer type, the user will have all
    // features available but with restrictive capabilities. This is to help
    // enterprises design and develop their data ingestion and integration
    // pipelines at low cost.
    DEVELOPER = 3;
  }

  // Represents the state of a Data Fusion instance
  enum State {
    // Instance does not have a state yet
    STATE_UNSPECIFIED = 0;

    // Instance is being created
    CREATING = 1;

    // Instance is running and ready for requests
    RUNNING = 2;

    // Instance creation failed
    FAILED = 3;

    // Instance is being deleted
    DELETING = 4;

    // Instance is being upgraded
    UPGRADING = 5;

    // Instance is being restarted
    RESTARTING = 6;

    // Instance is being updated on customer request
    UPDATING = 7;

    // Instance is being auto-updated
    AUTO_UPDATING = 8;

    // Instance is being auto-upgraded
    AUTO_UPGRADING = 9;

    // Instance is disabled
    DISABLED = 10;
  }

  // The reason for disabling the instance if the state is DISABLED.
  enum DisabledReason {
    // This is an unknown reason for disabling.
    DISABLED_REASON_UNSPECIFIED = 0;

    // The KMS key used by the instance is either revoked or denied access to
    KMS_KEY_ISSUE = 1;
  }

  // Output only. The name of this instance is in the form of
  // projects/{project}/locations/{location}/instances/{instance}.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "datafusion.googleapis.com/Instance"
    }
  ];

  // A description of this instance.
  string description = 2;

  // Required. Instance type.
  Type type = 3 [(google.api.field_behavior) = REQUIRED];

  // Option to enable Stackdriver Logging.
  bool enable_stackdriver_logging = 4;

  // Option to enable Stackdriver Monitoring.
  bool enable_stackdriver_monitoring = 5;

  // Specifies whether the Data Fusion instance should be private. If set to
  // true, all Data Fusion nodes will have private IP addresses and will not be
  // able to access the public internet.
  bool private_instance = 6;

  // Network configuration options. These are required when a private Data
  // Fusion instance is to be created.
  NetworkConfig network_config = 7;

  // The resource labels for instance to use to annotate any related underlying
  // resources such as Compute Engine VMs. The character '=' is not allowed to
  // be used within the labels.
  map<string, string> labels = 8;

  // Map of additional options used to configure the behavior of
  // Data Fusion instance.
  map<string, string> options = 9;

  // Output only. The time the instance was created.
  google.protobuf.Timestamp create_time = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the instance was last updated.
  google.protobuf.Timestamp update_time = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of this Data Fusion instance.
  State state = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the current state of this Data
  // Fusion instance if available.
  string state_message = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Endpoint on which the Data Fusion UI is accessible.
  string service_endpoint = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Name of the zone in which the Data Fusion instance will be created. Only
  // DEVELOPER instances use this field.
  string zone = 15;

  // Current version of Data Fusion.
  string version = 16;

  // Output only. Deprecated. Use tenant_project_id instead to extract the tenant project ID.
  string service_account = 17 [
    deprecated = true,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Display name for an instance.
  string display_name = 18;

  // Available versions that the instance can be upgraded to using
  // UpdateInstanceRequest.
  repeated Version available_version = 19;

  // Output only. Endpoint on which the REST APIs is accessible.
  string api_endpoint = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cloud Storage bucket generated by Data Fusion in the customer project.
  string gcs_bucket = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // List of accelerators enabled for this CDF instance.
  repeated Accelerator accelerators = 22;

  // Output only. P4 service account for the customer project.
  string p4_service_account = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the tenant project.
  string tenant_project_id = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-managed service account to set on Dataproc when Cloud Data Fusion
  // creates Dataproc to run data processing pipelines.
  //
  // This allows users to have fine-grained access control on Dataproc's
  // accesses to cloud resources.
  string dataproc_service_account = 25;

  // Option to enable granular role-based access control.
  bool enable_rbac = 26;

  // The crypto key configuration. This field is used by the Customer-Managed
  // Encryption Keys (CMEK) feature.
  CryptoKeyConfig crypto_key_config = 27;

  // Output only. If the instance state is DISABLED, the reason for disabling the instance.
  repeated DisabledReason disabled_reason = 28 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for listing Data Fusion instances.
message ListInstancesRequest {
  // Required. The project and location for which to retrieve instance information
  // in the format projects/{project}/locations/{location}. If the location is
  // specified as '-' (wildcard), then all regions available to the project
  // are queried, and the results are aggregated.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // List filter.
  string filter = 4;

  // Sort results. Supported values are "name", "name desc",  or "" (unsorted).
  string order_by = 5;
}

// Response message for the list instance request.
message ListInstancesResponse {
  // Represents a list of Data Fusion instances.
  repeated Instance instances = 1;

  // Token to retrieve the next page of results or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for the list available versions request.
message ListAvailableVersionsRequest {
  // Required. The project and location for which to retrieve instance information
  // in the format projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // Whether or not to return the latest patch of every available minor version.
  // If true, only the latest patch will be returned. Ex. if allowed versions is
  // [6.1.1, 6.1.2, 6.2.0] then response will be [6.1.2, 6.2.0]
  bool latest_patch_only = 4;
}

// Response message for the list available versions request.
message ListAvailableVersionsResponse {
  // Represents a list of versions that are supported.
  repeated Version available_versions = 1;

  // Token to retrieve the next page of results or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Request message for getting details about a Data Fusion instance.
message GetInstanceRequest {
  // Required. The instance resource name in the format
  // projects/{project}/locations/{location}/instances/{instance}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datafusion.googleapis.com/Instance"
    }
  ];
}

// Request message for creating a Data Fusion instance.
message CreateInstanceRequest {
  // Required. The instance's project and location in the format
  // projects/{project}/locations/{location}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The name of the instance to create.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // An instance resource.
  Instance instance = 3;
}

// Request message for deleting a Data Fusion instance.
message DeleteInstanceRequest {
  // Required. The instance resource name in the format
  // projects/{project}/locations/{location}/instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datafusion.googleapis.com/Instance"
    }
  ];
}

// Request message for updating a Data Fusion instance.
// Data Fusion only allows updating the labels, options, and stack driver
// settings.
message UpdateInstanceRequest {
  // Required. The instance resource that replaces the resource on the server. Currently,
  // Data Fusion only allows replacing labels, options, and stack driver
  // settings. All other fields will be ignored.
  Instance instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Field mask is used to specify the fields that the update will overwrite
  // in an instance resource. The fields specified in the update_mask are
  // relative to the resource, not the full request.
  // A field will be overwritten if it is in the mask.
  // If the user does not provide a mask, all the supported fields (labels and
  // options currently) will be overwritten.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for restarting a Data Fusion instance.
message RestartInstanceRequest {
  // Required. Name of the Data Fusion instance which need to be restarted in the form of
  // projects/{project}/locations/{location}/instances/{instance}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datafusion.googleapis.com/Instance"
    }
  ];
}

// Request message for upgrading a Data Fusion instance.
// To change the instance properties, instance update should be used.
message UpgradeInstanceRequest {
  // Required. Name of the Data Fusion instance which need to be upgraded in the form of
  // projects/{project}/locations/{location}/instances/{instance}
  // Instance will be upgraded with the latest stable version of the Data
  // Fusion.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datafusion.googleapis.com/Instance"
    }
  ];
}

// Represents the metadata of a long-running operation.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation if any.
  string status_detail = 5;

  // Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6;

  // API version used to start the operation.
  string api_version = 7;
}

// Request message for RemoveIamPolicy method.
message RemoveIamPolicyRequest {
  // Required. The resource on which IAM policy to be removed is attached to.
  string resource = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response message for RemoveIamPolicy method.
message RemoveIamPolicyResponse {

}

// List namespaces request.
message ListNamespacesRequest {
  // Required. The instance to list its namespaces.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datafusion.googleapis.com/Instance"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;

  // By default, only basic information about a namespace is returned
  // (e.g. name). When `NAMESPACE_VIEW_FULL` is specified, additional
  // information associated with a namespace gets returned
  // (e.g. IAM policy set on the namespace)
  NamespaceView view = 4;
}

// IAMPolicy encapsulates the IAM policy name, definition and status of
// policy fetching.
message IAMPolicy {
  // Policy definition if IAM policy fetching is successful,
  // otherwise empty.
  google.iam.v1.Policy policy = 1;

  // Status of iam policy fetching.
  google.rpc.Status status = 2;
}

// Represents the information of a namespace
message Namespace {
  option (google.api.resource) = {
    type: "datafusion.googleapis.com/Namespace"
    pattern: "projects/{project}/locations/{location}/instances/{instance}/namespaces/{namespace}"
  };

  // Name of the given namespace.
  string name = 1;

  // IAM policy associated with this namespace.
  IAMPolicy iam_policy = 2;
}

// List namespaces response.
message ListNamespacesResponse {
  // List of namespaces
  repeated Namespace namespaces = 1;

  // Token to retrieve the next page of results or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// DNS peering configuration. These configurations are used to create
// DNS peering with the customer Cloud DNS.
message DnsPeering {
  // Required. Name of the zone.
  string zone = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the dns.
  string domain = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Optional description of the dns zone.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional target project to which dns peering should happen.
  string target_project = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional target network to which dns peering should happen.
  string target_network = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message to create dns peering.
message AddDnsPeeringRequest {
  // Required. The resource on which DNS peering will be created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datafusion.googleapis.com/Instance"
    }
  ];

  // Dns peering config.
  DnsPeering dns_peering = 2;
}

// Response message for set dns peering method.
message AddDnsPeeringResponse {

}

// A view for Namespace
enum NamespaceView {
  // Default/unset value, which will use BASIC view.
  NAMESPACE_VIEW_UNSPECIFIED = 0;

  // Show the most basic metadata of a namespace
  NAMESPACE_VIEW_BASIC = 1;

  // Returns all metadata of a namespace
  NAMESPACE_VIEW_FULL = 2;
}

// Request message to remove dns peering.
message RemoveDnsPeeringRequest {
  // Required. The resource on which DNS peering will be removed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datafusion.googleapis.com/Instance"
    }
  ];

  // Required. The zone to be removed.
  string zone = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for set dns peering method.
message RemoveDnsPeeringResponse {

}

// List dns peering request.
message ListDnsPeeringsRequest {
  // Required. The resource on which dns peering will be listed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datafusion.googleapis.com/Instance"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value to use if there are additional
  // results to retrieve for this list request.
  string page_token = 3;
}

// List dns peering response.
message ListDnsPeeringsResponse {
  // List of dns peering configs.
  repeated DnsPeering dns_peerings = 1;

  // Token to retrieve the next page of results or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}
