# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "datafusion_proto",
    srcs = [
        "v1beta1.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "datafusion_proto_with_info",
    deps = [
        ":datafusion_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "datafusion_java_proto",
    deps = [":datafusion_proto"],
)

java_grpc_library(
    name = "datafusion_java_grpc",
    srcs = [":datafusion_proto"],
    deps = [":datafusion_java_proto"],
)

java_gapic_library(
    name = "datafusion_java_gapic",
    srcs = [":datafusion_proto_with_info"],
    grpc_service_config = "datafusion_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datafusion_v1beta1.yaml",
    test_deps = [
        ":datafusion_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":datafusion_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "datafusion_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.datafusion.v1beta1.DataFusionClientHttpJsonTest",
        "com.google.cloud.datafusion.v1beta1.DataFusionClientTest",
    ],
    runtime_deps = [":datafusion_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-datafusion-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":datafusion_java_gapic",
        ":datafusion_java_grpc",
        ":datafusion_java_proto",
        ":datafusion_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "datafusion_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/datafusion/apiv1beta1/datafusionpb",
    protos = [":datafusion_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "datafusion_go_gapic",
    srcs = [":datafusion_proto_with_info"],
    grpc_service_config = "datafusion_grpc_service_config.json",
    importpath = "cloud.google.com/go/datafusion/apiv1beta1;datafusion",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "datafusion_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datafusion_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-datafusion-v1beta1-go",
    deps = [
        ":datafusion_go_gapic",
        ":datafusion_go_gapic_srcjar-metadata.srcjar",
        ":datafusion_go_gapic_srcjar-snippets.srcjar",
        ":datafusion_go_gapic_srcjar-test.srcjar",
        ":datafusion_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "datafusion_py_gapic",
    srcs = [":datafusion_proto"],
    grpc_service_config = "datafusion_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datafusion_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "datafusion_py_gapic_test",
    srcs = [
        "datafusion_py_gapic_pytest.py",
        "datafusion_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":datafusion_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "datafusion-v1beta1-py",
    deps = [
        ":datafusion_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "datafusion_php_proto",
    deps = [":datafusion_proto"],
)

php_gapic_library(
    name = "datafusion_php_gapic",
    srcs = [":datafusion_proto_with_info"],
    grpc_service_config = "datafusion_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datafusion_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":datafusion_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-datafusion-v1beta1-php",
    deps = [
        ":datafusion_php_gapic",
        ":datafusion_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "datafusion_nodejs_gapic",
    package_name = "@google-cloud/data-fusion",
    src = ":datafusion_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "datafusion_grpc_service_config.json",
    package = "google.cloud.datafusion.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "datafusion_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "datafusion-v1beta1-nodejs",
    deps = [
        ":datafusion_nodejs_gapic",
        ":datafusion_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "datafusion_ruby_proto",
    deps = [":datafusion_proto"],
)

ruby_grpc_library(
    name = "datafusion_ruby_grpc",
    srcs = [":datafusion_proto"],
    deps = [":datafusion_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "datafusion_ruby_gapic",
    srcs = [":datafusion_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-data_fusion-v1beta1",
        "ruby-cloud-api-shortname=datafusion",
        "ruby-cloud-api-id=datafusion.googleapis.com",
        "ruby-cloud-product-url=https://cloud.google.com/data-fusion",
    ],
    grpc_service_config = "datafusion_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Data Fusion is a fully managed, cloud-native, enterprise data integration service for quickly building and managing data pipelines.",
    ruby_cloud_title = "Cloud Data Fusion V1beta1",
    service_yaml = "datafusion_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datafusion_ruby_grpc",
        ":datafusion_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-datafusion-v1beta1-ruby",
    deps = [
        ":datafusion_ruby_gapic",
        ":datafusion_ruby_grpc",
        ":datafusion_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "datafusion_csharp_proto",
    deps = [":datafusion_proto"],
)

csharp_grpc_library(
    name = "datafusion_csharp_grpc",
    srcs = [":datafusion_proto"],
    deps = [":datafusion_csharp_proto"],
)

csharp_gapic_library(
    name = "datafusion_csharp_gapic",
    srcs = [":datafusion_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "datafusion_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datafusion_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datafusion_csharp_grpc",
        ":datafusion_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-datafusion-v1beta1-csharp",
    deps = [
        ":datafusion_csharp_gapic",
        ":datafusion_csharp_grpc",
        ":datafusion_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
