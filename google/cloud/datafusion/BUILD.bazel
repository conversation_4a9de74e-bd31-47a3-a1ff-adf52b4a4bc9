# This build file includes a target for the Ruby wrapper library for
# google-cloud-data_fusion.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for datafusion.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "datafusion_ruby_wrapper",
    srcs = ["//google/cloud/datafusion/v1:datafusion_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-data_fusion",
        "ruby-cloud-wrapper-of=v1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/data-fusion",
        "ruby-cloud-api-id=datafusion.googleapis.com",
        "ruby-cloud-api-shortname=datafusion",
    ],
    ruby_cloud_description = "Cloud Data Fusion is a fully managed, cloud-native, enterprise data integration service for quickly building and managing data pipelines.",
    ruby_cloud_title = "Cloud Data Fusion",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-datafusion-ruby",
    deps = [
        ":datafusion_ruby_wrapper",
    ],
)
