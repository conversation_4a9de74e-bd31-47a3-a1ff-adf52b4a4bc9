# This build file includes a target for the Ruby wrapper library for
# google-cloud-os_config.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for osconfig.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "osconfig_ruby_wrapper",
    srcs = ["//google/cloud/osconfig/v1:osconfig_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-os_config",
        "ruby-cloud-env-prefix=OS_CONFIG",
        "ruby-cloud-wrapper-of=v1:0.15",
        "ruby-cloud-product-url=https://cloud.google.com/compute/docs/manage-os",
        "ruby-cloud-api-id=osconfig.googleapis.com",
        "ruby-cloud-api-shortname=osconfig",
    ],
    ruby_cloud_description = "Cloud OS Config provides OS management tools that can be used for patch management, patch compliance, and configuration management on VM instances.",
    ruby_cloud_title = "Cloud OS Config",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-osconfig-ruby",
    deps = [
        ":osconfig_ruby_wrapper",
    ],
)
