// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.v1alpha;


option csharp_namespace = "Google.Cloud.OsConfig.V1Alpha";
option go_package = "cloud.google.com/go/osconfig/apiv1alpha/osconfigpb;osconfigpb";
option java_multiple_files = true;
option java_outer_classname = "ConfigCommonProto";
option java_package = "com.google.cloud.osconfig.v1alpha";
option php_namespace = "Google\\Cloud\\OsConfig\\V1alpha";
option ruby_package = "Google::Cloud::OsConfig::V1alpha";

// Step performed by the OS Config agent for configuring an `OSPolicyResource`
// to its desired state.
message OSPolicyResourceConfigStep {
  option deprecated = true;

  // Supported configuration step types
  enum Type {
    option deprecated = true;

    // Default value. This value is unused.
    TYPE_UNSPECIFIED = 0;

    // Validation to detect resource conflicts, schema errors, etc.
    VALIDATION = 1;

    // Check the current desired state status of the resource.
    DESIRED_STATE_CHECK = 2;

    // Enforce the desired state for a resource that is not in desired state.
    DESIRED_STATE_ENFORCEMENT = 3;

    // Re-check desired state status for a resource after enforcement of all
    // resources in the current configuration run.
    //
    // This step is used to determine the final desired state status for the
    // resource. It accounts for any resources that might have drifted from
    // their desired state due to side effects from configuring other resources
    // during the current configuration run.
    DESIRED_STATE_CHECK_POST_ENFORCEMENT = 4;
  }

  // Supported outcomes for a configuration step.
  enum Outcome {
    option deprecated = true;

    // Default value. This value is unused.
    OUTCOME_UNSPECIFIED = 0;

    // The step succeeded.
    SUCCEEDED = 1;

    // The step failed.
    FAILED = 2;
  }

  // Configuration step type.
  Type type = 1;

  // Outcome of the configuration step.
  Outcome outcome = 2;

  // An error message recorded during the execution of this step.
  // Only populated when outcome is FAILED.
  string error_message = 3;
}

// Compliance data for an OS policy resource.
message OSPolicyResourceCompliance {
  option deprecated = true;

  // ExecResource specific output.
  message ExecResourceOutput {
    option deprecated = true;

    // Output from Enforcement phase output file (if run).
    // Output size is limited to 100K bytes.
    bytes enforcement_output = 2;
  }

  // The id of the OS policy resource.
  string os_policy_resource_id = 1;

  // Ordered list of configuration steps taken by the agent for the OS policy
  // resource.
  repeated OSPolicyResourceConfigStep config_steps = 2;

  // Compliance state of the OS policy resource.
  OSPolicyComplianceState state = 3;

  // Resource specific output.
  oneof output {
    // ExecResource specific output.
    ExecResourceOutput exec_resource_output = 4;
  }
}

// Supported OSPolicy compliance states.
enum OSPolicyComplianceState {
  option deprecated = true;

  // Default value. This value is unused.
  OS_POLICY_COMPLIANCE_STATE_UNSPECIFIED = 0;

  // Compliant state.
  COMPLIANT = 1;

  // Non-compliant state
  NON_COMPLIANT = 2;

  // Unknown compliance state.
  UNKNOWN = 3;

  // No applicable OS policies were found for the instance.
  // This state is only applicable to the instance.
  NO_OS_POLICIES_APPLICABLE = 4;
}
