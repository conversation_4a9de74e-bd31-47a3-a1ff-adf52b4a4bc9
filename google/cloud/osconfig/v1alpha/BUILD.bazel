# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "osconfig_proto",
    srcs = [
        "config_common.proto",
        "instance_os_policies_compliance.proto",
        "inventory.proto",
        "os_policy.proto",
        "os_policy_assignment_reports.proto",
        "os_policy_assignments.proto",
        "osconfig_common.proto",
        "osconfig_zonal_service.proto",
        "vulnerability.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "osconfig_proto_with_info",
    deps = [
        ":osconfig_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "osconfig_java_proto",
    deps = [":osconfig_proto"],
)

java_grpc_library(
    name = "osconfig_java_grpc",
    srcs = [":osconfig_proto"],
    deps = [":osconfig_java_proto"],
)

java_gapic_library(
    name = "osconfig_java_gapic",
    srcs = [":osconfig_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "osconfig_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1alpha.yaml",
    test_deps = [
        ":osconfig_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":osconfig_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "osconfig_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.osconfig.v1alpha.OsConfigZonalServiceClientHttpJsonTest",
        "com.google.cloud.osconfig.v1alpha.OsConfigZonalServiceClientTest",
    ],
    runtime_deps = [":osconfig_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-osconfig-v1alpha-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":osconfig_java_gapic",
        ":osconfig_java_grpc",
        ":osconfig_java_proto",
        ":osconfig_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "osconfig_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/osconfig/apiv1alpha/osconfigpb",
    protos = [":osconfig_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "osconfig_go_gapic",
    srcs = [":osconfig_proto_with_info"],
    grpc_service_config = "osconfig_grpc_service_config.json",
    importpath = "cloud.google.com/go/osconfig/apiv1alpha;osconfig",
    metadata = True,
    release_level = "alpha",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":osconfig_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-osconfig-v1alpha-go",
    deps = [
        ":osconfig_go_gapic",
        ":osconfig_go_gapic_srcjar-metadata.srcjar",
        ":osconfig_go_gapic_srcjar-snippets.srcjar",
        ":osconfig_go_gapic_srcjar-test.srcjar",
        ":osconfig_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "osconfig_py_gapic",
    srcs = [":osconfig_proto"],
    grpc_service_config = "osconfig_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-os-config"],
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1alpha.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "osconfig_py_gapic_test",
    srcs = [
        "osconfig_py_gapic_pytest.py",
        "osconfig_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":osconfig_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "osconfig-v1alpha-py",
    deps = [
        ":osconfig_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "osconfig_php_proto",
    deps = [":osconfig_proto"],
)

php_gapic_library(
    name = "osconfig_php_gapic",
    srcs = [":osconfig_proto_with_info"],
    grpc_service_config = "osconfig_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [":osconfig_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-osconfig-v1alpha-php",
    deps = [
        ":osconfig_php_gapic",
        ":osconfig_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "osconfig_nodejs_gapic",
    package_name = "@google-cloud/os-config",
    src = ":osconfig_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "osconfig_grpc_service_config.json",
    package = "google.cloud.osconfig.v1alpha",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "osconfig-v1alpha-nodejs",
    deps = [
        ":osconfig_nodejs_gapic",
        ":osconfig_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "osconfig_ruby_proto",
    deps = [":osconfig_proto"],
)

ruby_grpc_library(
    name = "osconfig_ruby_grpc",
    srcs = [":osconfig_proto"],
    deps = [":osconfig_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "osconfig_ruby_gapic",
    srcs = [":osconfig_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=osconfig.googleapis.com",
        "ruby-cloud-api-shortname=osconfig",
        "ruby-cloud-env-prefix=OS_CONFIG",
        "ruby-cloud-gem-name=google-cloud-os_config-v1alpha",
        "ruby-cloud-product-url=https://cloud.google.com/compute/docs/manage-os",
    ],
    grpc_service_config = "osconfig_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud OS Config provides OS management tools that can be used for patch management, patch compliance, and configuration management on VM instances.",
    ruby_cloud_title = "Cloud OS Config V1alpha",
    service_yaml = "osconfig_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":osconfig_ruby_grpc",
        ":osconfig_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-osconfig-v1alpha-ruby",
    deps = [
        ":osconfig_ruby_gapic",
        ":osconfig_ruby_grpc",
        ":osconfig_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "osconfig_csharp_proto",
    deps = [":osconfig_proto"],
)

csharp_grpc_library(
    name = "osconfig_csharp_grpc",
    srcs = [":osconfig_proto"],
    deps = [":osconfig_csharp_proto"],
)

csharp_gapic_library(
    name = "osconfig_csharp_gapic",
    srcs = [":osconfig_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "osconfig_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":osconfig_csharp_grpc",
        ":osconfig_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-osconfig-v1alpha-csharp",
    deps = [
        ":osconfig_csharp_gapic",
        ":osconfig_csharp_grpc",
        ":osconfig_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "osconfig_cc_proto",
    deps = [":osconfig_proto"],
)

cc_grpc_library(
    name = "osconfig_cc_grpc",
    srcs = [":osconfig_proto"],
    grpc_only = True,
    deps = [":osconfig_cc_proto"],
)
