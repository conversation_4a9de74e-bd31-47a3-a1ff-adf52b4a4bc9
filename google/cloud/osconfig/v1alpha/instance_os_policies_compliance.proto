// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/osconfig/v1alpha/config_common.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.OsConfig.V1Alpha";
option go_package = "cloud.google.com/go/osconfig/apiv1alpha/osconfigpb;osconfigpb";
option java_multiple_files = true;
option java_outer_classname = "InstanceOSPoliciesComplianceProto";
option java_package = "com.google.cloud.osconfig.v1alpha";
option php_namespace = "Google\\Cloud\\OsConfig\\V1alpha";
option ruby_package = "Google::Cloud::OsConfig::V1alpha";

// This API resource represents the OS policies compliance data for a Compute
// Engine virtual machine (VM) instance at a given point in time.
//
// A Compute Engine VM can have multiple OS policy assignments, and each
// assignment can have multiple OS policies. As a result, multiple OS policies
// could be applied to a single VM.
//
// You can use this API resource to determine both the compliance state of your
// VM as well as the compliance state of an individual OS policy.
//
// For more information, see [View
// compliance](https://cloud.google.com/compute/docs/os-configuration-management/view-compliance).
message InstanceOSPoliciesCompliance {
  option deprecated = true;
  option (google.api.resource) = {
    type: "osconfig.googleapis.com/InstanceOSPoliciesCompliance"
    pattern: "projects/{project}/locations/{location}/instanceOSPoliciesCompliances/{instance}"
  };

  // Compliance data for an OS policy
  message OSPolicyCompliance {
    option deprecated = true;

    // The OS policy id
    string os_policy_id = 1;

    // Reference to the `OSPolicyAssignment` API resource that the `OSPolicy`
    // belongs to.
    //
    // Format:
    // `projects/{project_number}/locations/{location}/osPolicyAssignments/{os_policy_assignment_id@revision_id}`
    string os_policy_assignment = 2 [(google.api.resource_reference) = {
                                       type: "osconfig.googleapis.com/OSPolicyAssignment"
                                     }];

    // Compliance state of the OS policy.
    OSPolicyComplianceState state = 4;

    // Compliance data for each `OSPolicyResource` that is applied to the
    // VM.
    repeated OSPolicyResourceCompliance os_policy_resource_compliances = 5;
  }

  // Output only. The `InstanceOSPoliciesCompliance` API resource name.
  //
  // Format:
  // `projects/{project_number}/locations/{location}/instanceOSPoliciesCompliances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Compute Engine VM instance name.
  string instance = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Compliance state of the VM.
  OSPolicyComplianceState state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Detailed compliance state of the VM.
  // This field is populated only when compliance state is `UNKNOWN`.
  //
  // It may contain one of the following values:
  //
  // * `no-compliance-data`: Compliance data is not available for this VM.
  // * `no-agent-detected`: OS Config agent is not detected for this VM.
  // * `config-not-supported-by-agent`: The version of the OS Config agent
  // running on this VM does not support configuration management.
  // * `inactive`: VM is not running.
  // * `internal-service-errors`: There were internal service errors encountered
  // while enforcing compliance.
  // * `agent-errors`: OS config agent encountered errors while enforcing
  // compliance.
  string detailed_state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The reason for the `detailed_state` of the VM (if any).
  string detailed_state_reason = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Compliance data for each `OSPolicy` that is applied to the VM.
  repeated OSPolicyCompliance os_policy_compliances = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp of the last compliance check for the VM.
  google.protobuf.Timestamp last_compliance_check_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unique identifier for the last compliance run.
  // This id will be logged by the OS config agent during a compliance run and
  // can be used for debugging and tracing purpose.
  string last_compliance_run_id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A request message for getting OS policies compliance data for the given
// Compute Engine VM instance.
message GetInstanceOSPoliciesComplianceRequest {
  option deprecated = true;

  // Required. API resource name for instance OS policies compliance resource.
  //
  // Format:
  // `projects/{project}/locations/{location}/instanceOSPoliciesCompliances/{instance}`
  //
  // For `{project}`, either Compute Engine project-number or project-id can be
  // provided.
  // For `{instance}`, either Compute Engine VM instance-id or instance-name can
  // be provided.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "osconfig.googleapis.com/InstanceOSPoliciesCompliance"
    }
  ];
}

// A request message for listing OS policies compliance data for all Compute
// Engine VMs in the given location.
message ListInstanceOSPoliciesCompliancesRequest {
  option deprecated = true;

  // Required. The parent resource name.
  //
  // Format: `projects/{project}/locations/{location}`
  //
  // For `{project}`, either Compute Engine project-number or project-id can be
  // provided.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of results to return.
  int32 page_size = 2;

  // A pagination token returned from a previous call to
  // `ListInstanceOSPoliciesCompliances` that indicates where this listing
  // should continue from.
  string page_token = 3;

  // If provided, this field specifies the criteria that must be met by a
  // `InstanceOSPoliciesCompliance` API resource to be included in the response.
  string filter = 4;
}

// A response message for listing OS policies compliance data for all Compute
// Engine VMs in the given location.
message ListInstanceOSPoliciesCompliancesResponse {
  option deprecated = true;

  // List of instance OS policies compliance objects.
  repeated InstanceOSPoliciesCompliance instance_os_policies_compliances = 1;

  // The pagination token to retrieve the next page of instance OS policies
  // compliance objects.
  string next_page_token = 2;
}
