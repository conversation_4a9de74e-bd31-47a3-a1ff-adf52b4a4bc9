type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
# The settings of generated code in a specific language.
language_settings:
  python:
    package_name: google.cloud.osconfig_v1beta.gapic
  go:
    package_name: cloud.google.com/go/osconfig/apiv1beta
  csharp:
    package_name: Google.Cloud.OsConfig.V1beta
  ruby:
    package_name: Google::Cloud::OsConfig::V1beta
  php:
    package_name: Google\Cloud\OsConfig\V1beta
  nodejs:
    package_name: osconfig.v1beta
# A list of API interface configurations.
interfaces:
# The fully qualified name of the API interface.
- name: google.cloud.osconfig.v1beta.OsConfigService
  # A list of method configurations.
  #   retry_codes_name - Specifies the configuration for retryable codes. The
  #   name must be defined in interfaces.retry_codes_def.
  #
  #   retry_params_name - Specifies the configuration for retry/backoff
  #   parameters. The name must be defined in interfaces.retry_params_def.
  methods:
  - name: DeletePatchDeployment
    retry_codes_name: idempotent
  - name: DeleteGuestPolicy
    retry_codes_name: idempotent
