// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.logging;

import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/osconfig/logging/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "PatchJobLogProto";
option java_package = "com.google.cloud.osconfig.logging";

message PatchJobCompletedLog {
  // Enumeration of the various states a patch job passes through as it
  // executes.
  enum State {
    // State must be specified.
    STATE_UNSPECIFIED = 0;

    // The patch job was successfully initiated.
    STARTED = 1;

    // The patch job is looking up instances to run the patch on.
    INSTANCE_LOOKUP = 2;

    // Instances are being patched.
    PATCHING = 3;

    // Patch job completed successfully.
    SUCCEEDED = 4;

    // Patch job completed but there were errors.
    COMPLETED_WITH_ERRORS = 5;

    // The patch job was canceled.
    CANCELED = 6;

    // The patch job has timed out.
    TIMED_OUT = 7;
  }

  // A summary of the current patch state across all instances this patch job
  // affects. Contains counts of instances in different states. These states map
  // to InstancePatchState. List patch job instance details to see the specific
  // states of each instance.
  message InstanceDetailsSummary {
    // Number of instances pending patch job.
    int64 instances_pending = 1;

    // Number of instances that are inactive.
    int64 instances_inactive = 2;

    // Number of instances notified about patch job.
    int64 instances_notified = 3;

    // Number of instances that have started.
    int64 instances_started = 4;

    // Number of instances that are downloading patches.
    int64 instances_downloading_patches = 5;

    // Number of instances that are applying patches.
    int64 instances_applying_patches = 6;

    // Number of instances rebooting.
    int64 instances_rebooting = 7;

    // Number of instances that have completed successfully.
    int64 instances_succeeded = 8;

    // Number of instances that require reboot.
    int64 instances_succeeded_reboot_required = 9;

    // Number of instances that failed.
    int64 instances_failed = 10;

    // Number of instances that have acked and will start shortly.
    int64 instances_acked = 11;

    // Number of instances that exceeded the time out while applying the patch.
    int64 instances_timed_out = 12;

    // Number of instances that are running the pre-patch step.
    int64 instances_running_pre_patch_step = 13;

    // Number of instances that are running the post-patch step.
    int64 instances_running_post_patch_step = 14;
  }

  // The patch job name. For example:
  // projects/PROJECT_ID/patchJobs/PATCH_JOB_ID
  string patch_job = 1;

  // The current state of the PatchJob.
  State state = 2;

  // Summary of instance details.
  InstanceDetailsSummary instance_details_summary = 3;

  // If this patch job is a dry run, the agent will report that it has
  // finished without running any updates on the VM.
  bool dry_run = 4;

  // If this patch job failed, this message will provide information about the
  // failure.
  string error_message = 5;

  // Time this PatchJob was created.
  google.protobuf.Timestamp create_time = 6;

  // Last time this PatchJob was updated.
  google.protobuf.Timestamp update_time = 7;
}
