// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.OsConfig.V1";
option go_package = "cloud.google.com/go/osconfig/apiv1/osconfigpb;osconfigpb";
option java_multiple_files = true;
option java_outer_classname = "OSPolicyAssignmentReportsProto";
option java_package = "com.google.cloud.osconfig.v1";
option php_namespace = "Google\\Cloud\\OsConfig\\V1";
option ruby_package = "Google::Cloud::OsConfig::V1";
option (google.api.resource_definition) = {
  type: "osconfig.googleapis.com/InstanceOSPolicyAssignment"
  pattern: "projects/{project}/locations/{location}/instances/{instance}/osPolicyAssignments/{assignment}"
};

// Get a report of the OS policy assignment for a VM instance.
message GetOSPolicyAssignmentReportRequest {
  // Required. API resource name for OS policy assignment report.
  //
  // Format:
  // `/projects/{project}/locations/{location}/instances/{instance}/osPolicyAssignments/{assignment}/report`
  //
  // For `{project}`, either `project-number` or `project-id` can be provided.
  // For `{instance_id}`, either Compute Engine `instance-id` or `instance-name`
  // can be provided.
  // For `{assignment_id}`, the OSPolicyAssignment id must be provided.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "osconfig.googleapis.com/OSPolicyAssignmentReport"
    }
  ];
}

// List the OS policy assignment reports for VM instances.
message ListOSPolicyAssignmentReportsRequest {
  // Required. The parent resource name.
  //
  // Format:
  // `projects/{project}/locations/{location}/instances/{instance}/osPolicyAssignments/{assignment}/reports`
  //
  // For `{project}`, either `project-number` or `project-id` can be provided.
  // For `{instance}`, either `instance-name`, `instance-id`, or `-` can be
  // provided. If '-' is provided, the response will include
  // OSPolicyAssignmentReports for all instances in the project/location.
  // For `{assignment}`, either `assignment-id` or `-` can be provided. If '-'
  // is provided, the response will include OSPolicyAssignmentReports for all
  // OSPolicyAssignments in the project/location.
  // Either {instance} or {assignment} must be `-`.
  //
  // For example:
  // `projects/{project}/locations/{location}/instances/{instance}/osPolicyAssignments/-/reports`
  //  returns all reports for the instance
  // `projects/{project}/locations/{location}/instances/-/osPolicyAssignments/{assignment-id}/reports`
  //  returns all the reports for the given assignment across all instances.
  // `projects/{project}/locations/{location}/instances/-/osPolicyAssignments/-/reports`
  //  returns all the reports for all assignments across all instances.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "osconfig.googleapis.com/InstanceOSPolicyAssignment"
    }
  ];

  // The maximum number of results to return.
  int32 page_size = 2;

  // If provided, this field specifies the criteria that must be met by the
  // `OSPolicyAssignmentReport` API resource that is included in the response.
  string filter = 3;

  // A pagination token returned from a previous call to the
  // `ListOSPolicyAssignmentReports` method that indicates where this listing
  // should continue from.
  string page_token = 4;
}

// A response message for listing OS Policy assignment reports including the
// page of results and page token.
message ListOSPolicyAssignmentReportsResponse {
  // List of OS policy assignment reports.
  repeated OSPolicyAssignmentReport os_policy_assignment_reports = 1;

  // The pagination token to retrieve the next page of OS policy assignment
  // report objects.
  string next_page_token = 2;
}

// A report of the OS policy assignment status for a given instance.
message OSPolicyAssignmentReport {
  option (google.api.resource) = {
    type: "osconfig.googleapis.com/OSPolicyAssignmentReport"
    pattern: "projects/{project}/locations/{location}/instances/{instance}/osPolicyAssignments/{assignment}/report"
  };

  // Compliance data for an OS policy
  message OSPolicyCompliance {
    // Possible compliance states for an os policy.
    enum ComplianceState {
      // The policy is in an unknown compliance state.
      //
      // Refer to the field `compliance_state_reason` to learn the exact reason
      // for the policy to be in this compliance state.
      UNKNOWN = 0;

      // Policy is compliant.
      //
      // The policy is compliant if all the underlying resources are also
      // compliant.
      COMPLIANT = 1;

      // Policy is non-compliant.
      //
      // The policy is non-compliant if one or more underlying resources are
      // non-compliant.
      NON_COMPLIANT = 2;
    }

    // Compliance data for an OS policy resource.
    message OSPolicyResourceCompliance {
      // Step performed by the OS Config agent for configuring an
      // `OSPolicy` resource to its desired state.
      message OSPolicyResourceConfigStep {
        // Supported configuration step types
        enum Type {
          // Default value. This value is unused.
          TYPE_UNSPECIFIED = 0;

          // Checks for resource conflicts such as schema errors.
          VALIDATION = 1;

          // Checks the current status of the desired state for a resource.
          DESIRED_STATE_CHECK = 2;

          // Enforces the desired state for a resource that is not in desired
          // state.
          DESIRED_STATE_ENFORCEMENT = 3;

          // Re-checks the status of the desired state. This check is done
          // for a resource after the enforcement of all OS policies.
          //
          // This step is used to determine the final desired state status for
          // the resource. It accounts for any resources that might have drifted
          // from their desired state due to side effects from executing other
          // resources.
          DESIRED_STATE_CHECK_POST_ENFORCEMENT = 4;
        }

        // Configuration step type.
        Type type = 1;

        // An error message recorded during the execution of this step.
        // Only populated if errors were encountered during this step execution.
        string error_message = 2;
      }

      // Possible compliance states for a resource.
      enum ComplianceState {
        // The resource is in an unknown compliance state.
        //
        // To get more details about why the policy is in this state, review
        // the output of the `compliance_state_reason` field.
        UNKNOWN = 0;

        // Resource is compliant.
        COMPLIANT = 1;

        // Resource is non-compliant.
        NON_COMPLIANT = 2;
      }

      // ExecResource specific output.
      message ExecResourceOutput {
        // Output from enforcement phase output file (if run).
        // Output size is limited to 100K bytes.
        bytes enforcement_output = 2;
      }

      // The ID of the OS policy resource.
      string os_policy_resource_id = 1;

      // Ordered list of configuration completed by the agent for the OS policy
      // resource.
      repeated OSPolicyResourceConfigStep config_steps = 2;

      // The compliance state of the resource.
      ComplianceState compliance_state = 3;

      // A reason for the resource to be in the given compliance state.
      // This field is always populated when `compliance_state` is `UNKNOWN`.
      //
      // The following values are supported when `compliance_state == UNKNOWN`
      //
      // * `execution-errors`: Errors were encountered by the agent while
      // executing the resource and the compliance state couldn't be
      // determined.
      // * `execution-skipped-by-agent`: Resource execution was skipped by the
      // agent because errors were encountered while executing prior resources
      // in the OS policy.
      // * `os-policy-execution-attempt-failed`: The execution of the OS policy
      // containing this resource failed and the compliance state couldn't be
      // determined.
      string compliance_state_reason = 4;

      // Resource specific output.
      oneof output {
        // ExecResource specific output.
        ExecResourceOutput exec_resource_output = 5;
      }
    }

    // The OS policy id
    string os_policy_id = 1;

    // The compliance state of the OS policy.
    ComplianceState compliance_state = 2;

    // The reason for the OS policy to be in an unknown compliance state.
    // This field is always populated when `compliance_state` is `UNKNOWN`.
    //
    // If populated, the field can contain one of the following values:
    //
    // * `vm-not-running`: The VM was not running.
    // * `os-policies-not-supported-by-agent`: The version of the OS Config
    // agent running on the VM does not support running OS policies.
    // * `no-agent-detected`: The OS Config agent is not detected for the VM.
    // * `resource-execution-errors`: The OS Config agent encountered errors
    // while executing one or more resources in the policy. See
    // `os_policy_resource_compliances` for details.
    // * `task-timeout`: The task sent to the agent to apply the policy timed
    // out.
    // * `unexpected-agent-state`: The OS Config agent did not report the final
    // status of the task that attempted to apply the policy. Instead, the agent
    // unexpectedly started working on a different task. This mostly happens
    // when the agent or VM unexpectedly restarts while applying OS policies.
    // * `internal-service-errors`: Internal service errors were encountered
    // while attempting to apply the policy.
    string compliance_state_reason = 3;

    // Compliance data for each resource within the policy that is applied to
    // the VM.
    repeated OSPolicyResourceCompliance os_policy_resource_compliances = 4;
  }

  // The `OSPolicyAssignmentReport` API resource name.
  //
  // Format:
  // `projects/{project_number}/locations/{location}/instances/{instance_id}/osPolicyAssignments/{os_policy_assignment_id}/report`
  string name = 1;

  // The Compute Engine VM instance name.
  string instance = 2;

  // Reference to the `OSPolicyAssignment` API resource that the `OSPolicy`
  // belongs to.
  //
  // Format:
  // `projects/{project_number}/locations/{location}/osPolicyAssignments/{os_policy_assignment_id@revision_id}`
  string os_policy_assignment = 3 [(google.api.resource_reference) = {
    type: "osconfig.googleapis.com/OSPolicyAssignment"
  }];

  // Compliance data for each `OSPolicy` that is applied to the VM.
  repeated OSPolicyCompliance os_policy_compliances = 4;

  // Timestamp for when the report was last generated.
  google.protobuf.Timestamp update_time = 5;

  // Unique identifier of the last attempted run to apply the OS policies
  // associated with this assignment on the VM.
  //
  // This ID is logged by the OS Config agent while applying the OS
  // policies associated with this assignment on the VM.
  // NOTE: If the service is unable to successfully connect to the agent for
  // this run, then this id will not be available in the agent logs.
  string last_run_id = 6;
}
