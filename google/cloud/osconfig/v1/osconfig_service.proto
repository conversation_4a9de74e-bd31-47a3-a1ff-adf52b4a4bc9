// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.v1;

import "google/api/client.proto";
import "google/api/resource.proto";
import "google/cloud/osconfig/v1/patch_deployments.proto";
import "google/cloud/osconfig/v1/patch_jobs.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";

option csharp_namespace = "Google.Cloud.OsConfig.V1";
option go_package = "cloud.google.com/go/osconfig/apiv1/osconfigpb;osconfigpb";
option java_outer_classname = "OsConfigProto";
option java_package = "com.google.cloud.osconfig.v1";
option php_namespace = "Google\\Cloud\\OsConfig\\V1";
option ruby_package = "Google::Cloud::OsConfig::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Instance"
  pattern: "projects/{project}/zones/{zone}/instances/{instance}"
  pattern: "projects/{project}/locations/{location}/instances/{instance}"
};

// OS Config API
//
// The OS Config service is a server-side component that you can use to
// manage package installations and patch jobs for virtual machine instances.
service OsConfigService {
  option (google.api.default_host) = "osconfig.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Patch VM instances by creating and running a patch job.
  rpc ExecutePatchJob(ExecutePatchJobRequest) returns (PatchJob) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/patchJobs:execute"
      body: "*"
    };
  }

  // Get the patch job. This can be used to track the progress of an
  // ongoing patch job or review the details of completed jobs.
  rpc GetPatchJob(GetPatchJobRequest) returns (PatchJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/patchJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Cancel a patch job. The patch job must be active. Canceled patch jobs
  // cannot be restarted.
  rpc CancelPatchJob(CancelPatchJobRequest) returns (PatchJob) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/patchJobs/*}:cancel"
      body: "*"
    };
  }

  // Get a list of patch jobs.
  rpc ListPatchJobs(ListPatchJobsRequest) returns (ListPatchJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/patchJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get a list of instance details for a given patch job.
  rpc ListPatchJobInstanceDetails(ListPatchJobInstanceDetailsRequest)
      returns (ListPatchJobInstanceDetailsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/patchJobs/*}/instanceDetails"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create an OS Config patch deployment.
  rpc CreatePatchDeployment(CreatePatchDeploymentRequest)
      returns (PatchDeployment) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/patchDeployments"
      body: "patch_deployment"
    };
    option (google.api.method_signature) =
        "parent,patch_deployment,patch_deployment_id";
  }

  // Get an OS Config patch deployment.
  rpc GetPatchDeployment(GetPatchDeploymentRequest) returns (PatchDeployment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/patchDeployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Get a page of OS Config patch deployments.
  rpc ListPatchDeployments(ListPatchDeploymentsRequest)
      returns (ListPatchDeploymentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/patchDeployments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Delete an OS Config patch deployment.
  rpc DeletePatchDeployment(DeletePatchDeploymentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/patchDeployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update an OS Config patch deployment.
  rpc UpdatePatchDeployment(UpdatePatchDeploymentRequest)
      returns (PatchDeployment) {
    option (google.api.http) = {
      patch: "/v1/{patch_deployment.name=projects/*/patchDeployments/*}"
      body: "patch_deployment"
    };
    option (google.api.method_signature) = "patch_deployment,update_mask";
  }

  // Change state of patch deployment to "PAUSED".
  // Patch deployment in paused state doesn't generate patch jobs.
  rpc PausePatchDeployment(PausePatchDeploymentRequest)
      returns (PatchDeployment) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/patchDeployments/*}:pause"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Change state of patch deployment back to "ACTIVE".
  // Patch deployment in active state continues to generate patch jobs.
  rpc ResumePatchDeployment(ResumePatchDeploymentRequest)
      returns (PatchDeployment) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/patchDeployments/*}:resume"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }
}
