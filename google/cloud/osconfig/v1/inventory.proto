// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option csharp_namespace = "Google.Cloud.OsConfig.V1";
option go_package = "cloud.google.com/go/osconfig/apiv1/osconfigpb;osconfigpb";
option java_multiple_files = true;
option java_outer_classname = "Inventories";
option java_package = "com.google.cloud.osconfig.v1";
option php_namespace = "Google\\Cloud\\OsConfig\\V1";
option ruby_package = "Google::Cloud::OsConfig::V1";

// OS Config Inventory is a service for collecting and reporting operating
// system and package information on VM instances.

// This API resource represents the available inventory data for a
// Compute Engine virtual machine (VM) instance at a given point in time.
//
// You can use this API resource to determine the inventory data of your VM.
//
// For more information, see [Information provided by OS inventory
// management](https://cloud.google.com/compute/docs/instances/os-inventory-management#data-collected).
message Inventory {
  option (google.api.resource) = {
    type: "osconfig.googleapis.com/Inventory"
    pattern: "projects/{project}/locations/{location}/instances/{instance}/inventory"
  };

  // Operating system information for the VM.
  message OsInfo {
    // The VM hostname.
    string hostname = 9;

    // The operating system long name.
    // For example 'Debian GNU/Linux 9' or 'Microsoft Window Server 2019
    // Datacenter'.
    string long_name = 2;

    // The operating system short name.
    // For example, 'windows' or 'debian'.
    string short_name = 3;

    // The version of the operating system.
    string version = 4;

    // The system architecture of the operating system.
    string architecture = 5;

    // The kernel version of the operating system.
    string kernel_version = 6;

    // The kernel release of the operating system.
    string kernel_release = 7;

    // The current version of the OS Config agent running on the VM.
    string osconfig_agent_version = 8;
  }

  // A single piece of inventory on a VM.
  message Item {
    // The origin of a specific inventory item.
    enum OriginType {
      // Invalid. An origin type must be specified.
      ORIGIN_TYPE_UNSPECIFIED = 0;

      // This inventory item was discovered as the result of the agent
      // reporting inventory via the reporting API.
      INVENTORY_REPORT = 1;
    }

    // The different types of inventory that are tracked on a VM.
    enum Type {
      // Invalid. An type must be specified.
      TYPE_UNSPECIFIED = 0;

      // This represents a package that is installed on the VM.
      INSTALLED_PACKAGE = 1;

      // This represents an update that is available for a package.
      AVAILABLE_PACKAGE = 2;
    }

    // Identifier for this item, unique across items for this VM.
    string id = 1;

    // The origin of this inventory item.
    OriginType origin_type = 2;

    // When this inventory item was first detected.
    google.protobuf.Timestamp create_time = 8;

    // When this inventory item was last modified.
    google.protobuf.Timestamp update_time = 9;

    // The specific type of inventory, correlating to its specific details.
    Type type = 5;

    // Specific details of this inventory item based on its type.
    oneof details {
      // Software package present on the VM instance.
      SoftwarePackage installed_package = 6;

      // Software package available to be installed on the VM instance.
      SoftwarePackage available_package = 7;
    }
  }

  // Software package information of the operating system.
  message SoftwarePackage {
    // Information about the different types of software packages.
    oneof details {
      // Yum package info.
      // For details about the yum package manager, see
      // https://access.redhat.com/documentation/en-us/red_hat_enterprise_linux/6/html/deployment_guide/ch-yum.
      VersionedPackage yum_package = 1;

      // Details of an APT package.
      // For details about the apt package manager, see
      // https://wiki.debian.org/Apt.
      VersionedPackage apt_package = 2;

      // Details of a Zypper package.
      // For details about the Zypper package manager, see
      // https://en.opensuse.org/SDB:Zypper_manual.
      VersionedPackage zypper_package = 3;

      // Details of a Googet package.
      //  For details about the googet package manager, see
      //  https://github.com/google/googet.
      VersionedPackage googet_package = 4;

      // Details of a Zypper patch.
      // For details about the Zypper package manager, see
      // https://en.opensuse.org/SDB:Zypper_manual.
      ZypperPatch zypper_patch = 5;

      // Details of a Windows Update package.
      // See https://docs.microsoft.com/en-us/windows/win32/api/_wua/ for
      // information about Windows Update.
      WindowsUpdatePackage wua_package = 6;

      // Details of a Windows Quick Fix engineering package.
      // See
      // https://docs.microsoft.com/en-us/windows/win32/cimwin32prov/win32-quickfixengineering
      // for info in Windows Quick Fix Engineering.
      WindowsQuickFixEngineeringPackage qfe_package = 7;

      // Details of a COS package.
      VersionedPackage cos_package = 8;

      // Details of Windows Application.
      WindowsApplication windows_application = 9;
    }
  }

  // Information related to the a standard versioned package.  This includes
  // package info for APT, Yum, Zypper, and Googet package managers.
  message VersionedPackage {
    // The name of the package.
    string package_name = 4;

    // The system architecture this package is intended for.
    string architecture = 2;

    // The version of the package.
    string version = 3;
  }

  // Details related to a Zypper Patch.
  message ZypperPatch {
    // The name of the patch.
    string patch_name = 5;

    // The category of the patch.
    string category = 2;

    // The severity specified for this patch
    string severity = 3;

    // Any summary information provided about this patch.
    string summary = 4;
  }

  // Details related to a Windows Update package.
  // Field data and names are taken from Windows Update API IUpdate Interface:
  // https://docs.microsoft.com/en-us/windows/win32/api/_wua/
  // Descriptive fields like title, and description are localized based on
  // the locale of the VM being updated.
  message WindowsUpdatePackage {
    // Categories specified by the Windows Update.
    message WindowsUpdateCategory {
      // The identifier of the windows update category.
      string id = 1;

      // The name of the windows update category.
      string name = 2;
    }

    // The localized title of the update package.
    string title = 1;

    // The localized description of the update package.
    string description = 2;

    // The categories that are associated with this update package.
    repeated WindowsUpdateCategory categories = 3;

    // A collection of Microsoft Knowledge Base article IDs that are associated
    // with the update package.
    repeated string kb_article_ids = 4;

    // A hyperlink to the language-specific support information for the update.
    string support_url = 11;

    // A collection of URLs that provide more information about the update
    // package.
    repeated string more_info_urls = 5;

    // Gets the identifier of an update package.  Stays the same across
    // revisions.
    string update_id = 6;

    // The revision number of this update package.
    int32 revision_number = 7;

    // The last published date of the update, in (UTC) date and time.
    google.protobuf.Timestamp last_deployment_change_time = 10;
  }

  // Information related to a Quick Fix Engineering package.
  // Fields are taken from Windows QuickFixEngineering Interface and match
  // the source names:
  // https://docs.microsoft.com/en-us/windows/win32/cimwin32prov/win32-quickfixengineering
  message WindowsQuickFixEngineeringPackage {
    // A short textual description of the QFE update.
    string caption = 1;

    // A textual description of the QFE update.
    string description = 2;

    // Unique identifier associated with a particular QFE update.
    string hot_fix_id = 3;

    // Date that the QFE update was installed.  Mapped from installed_on field.
    google.protobuf.Timestamp install_time = 5;
  }

  // Contains information about a Windows application that is retrieved from the
  // Windows Registry. For more information about these fields, see:
  // https://docs.microsoft.com/en-us/windows/win32/msi/uninstall-registry-key
  message WindowsApplication {
    // The name of the application or product.
    string display_name = 1;

    // The version of the product or application in string format.
    string display_version = 2;

    // The name of the manufacturer for the product or application.
    string publisher = 3;

    // The last time this product received service. The value of this property
    // is replaced each time a patch is applied or removed from the product or
    // the command-line option is used to repair the product.
    google.type.Date install_date = 4;

    // The internet address for technical support.
    string help_link = 5;
  }

  // Output only. The `Inventory` API resource name.
  //
  // Format:
  // `projects/{project_number}/locations/{location}/instances/{instance_id}/inventory`
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Base level operating system information for the VM.
  OsInfo os_info = 1;

  // Inventory items related to the VM keyed by an opaque unique identifier for
  // each inventory item.  The identifier is unique to each distinct and
  // addressable inventory item and will change, when there is a new package
  // version.
  map<string, Item> items = 2;

  // Output only. Timestamp of the last reported inventory for the VM.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A request message for getting inventory data for the specified VM.
message GetInventoryRequest {
  // Required. API resource name for inventory resource.
  //
  // Format:
  // `projects/{project}/locations/{location}/instances/{instance}/inventory`
  //
  // For `{project}`, either `project-number` or `project-id` can be provided.
  // For `{instance}`, either Compute Engine  `instance-id` or `instance-name`
  // can be provided.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "osconfig.googleapis.com/Inventory"
    }
  ];

  // Inventory view indicating what information should be included in the
  // inventory resource. If unspecified, the default view is BASIC.
  InventoryView view = 2;
}

// A request message for listing inventory data for all VMs in the specified
// location.
message ListInventoriesRequest {
  // Required. The parent resource name.
  //
  // Format: `projects/{project}/locations/{location}/instances/-`
  //
  // For `{project}`, either `project-number` or `project-id` can be provided.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/Instance"
    }
  ];

  // Inventory view indicating what information should be included in the
  // inventory resource. If unspecified, the default view is BASIC.
  InventoryView view = 2;

  // The maximum number of results to return.
  int32 page_size = 3;

  // A pagination token returned from a previous call to
  // `ListInventories` that indicates where this listing
  // should continue from.
  string page_token = 4;

  // If provided, this field specifies the criteria that must be met by a
  // `Inventory` API resource to be included in the response.
  string filter = 5;
}

// A response message for listing inventory data for all VMs in a specified
// location.
message ListInventoriesResponse {
  // List of inventory objects.
  repeated Inventory inventories = 1;

  // The pagination token to retrieve the next page of inventory objects.
  string next_page_token = 2;
}

// The view for inventory objects.
enum InventoryView {
  // The default value.
  // The API defaults to the BASIC view.
  INVENTORY_VIEW_UNSPECIFIED = 0;

  // Returns the basic inventory information that includes `os_info`.
  BASIC = 1;

  // Returns all fields.
  FULL = 2;
}
