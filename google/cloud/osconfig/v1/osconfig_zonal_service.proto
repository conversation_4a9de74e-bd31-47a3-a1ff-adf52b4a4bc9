// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/osconfig/v1/inventory.proto";
import "google/cloud/osconfig/v1/os_policy_assignment_reports.proto";
import "google/cloud/osconfig/v1/os_policy_assignments.proto";
import "google/cloud/osconfig/v1/vulnerability.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.OsConfig.V1";
option go_package = "cloud.google.com/go/osconfig/apiv1/osconfigpb;osconfigpb";
option java_multiple_files = true;
option java_outer_classname = "OsConfigZonalServiceProto";
option java_package = "com.google.cloud.osconfig.v1";
option php_namespace = "Google\\Cloud\\OsConfig\\V1";
option ruby_package = "Google::Cloud::OsConfig::V1";

// Zonal OS Config API
//
// The OS Config service is the server-side component that allows users to
// manage package installations and patch jobs for Compute Engine VM instances.
service OsConfigZonalService {
  option (google.api.default_host) = "osconfig.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Create an OS policy assignment.
  //
  // This method also creates the first revision of the OS policy assignment.
  //
  // This method returns a long running operation (LRO) that contains the
  // rollout details. The rollout can be cancelled by cancelling the LRO.
  //
  // For more information, see [Method:
  // projects.locations.osPolicyAssignments.operations.cancel](https://cloud.google.com/compute/docs/osconfig/rest/v1/projects.locations.osPolicyAssignments.operations/cancel).
  rpc CreateOSPolicyAssignment(CreateOSPolicyAssignmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/osPolicyAssignments"
      body: "os_policy_assignment"
    };
    option (google.api.method_signature) =
        "parent,os_policy_assignment,os_policy_assignment_id";
    option (google.longrunning.operation_info) = {
      response_type: "OSPolicyAssignment"
      metadata_type: "OSPolicyAssignmentOperationMetadata"
    };
  }

  // Update an existing OS policy assignment.
  //
  // This method creates a new revision of the OS policy assignment.
  //
  // This method returns a long running operation (LRO) that contains the
  // rollout details. The rollout can be cancelled by cancelling the LRO.
  //
  // For more information, see [Method:
  // projects.locations.osPolicyAssignments.operations.cancel](https://cloud.google.com/compute/docs/osconfig/rest/v1/projects.locations.osPolicyAssignments.operations/cancel).
  rpc UpdateOSPolicyAssignment(UpdateOSPolicyAssignmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{os_policy_assignment.name=projects/*/locations/*/osPolicyAssignments/*}"
      body: "os_policy_assignment"
    };
    option (google.api.method_signature) = "os_policy_assignment,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "OSPolicyAssignment"
      metadata_type: "OSPolicyAssignmentOperationMetadata"
    };
  }

  // Retrieve an existing OS policy assignment.
  //
  // This method always returns the latest revision. In order to retrieve a
  // previous revision of the assignment, also provide the revision ID in the
  // `name` parameter.
  rpc GetOSPolicyAssignment(GetOSPolicyAssignmentRequest)
      returns (OSPolicyAssignment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/osPolicyAssignments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List the OS policy assignments under the parent resource.
  //
  // For each OS policy assignment, the latest revision is returned.
  rpc ListOSPolicyAssignments(ListOSPolicyAssignmentsRequest)
      returns (ListOSPolicyAssignmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/osPolicyAssignments"
    };
    option (google.api.method_signature) = "parent";
  }

  // List the OS policy assignment revisions for a given OS policy assignment.
  rpc ListOSPolicyAssignmentRevisions(ListOSPolicyAssignmentRevisionsRequest)
      returns (ListOSPolicyAssignmentRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/osPolicyAssignments/*}:listRevisions"
    };
    option (google.api.method_signature) = "name";
  }

  // Delete the OS policy assignment.
  //
  // This method creates a new revision of the OS policy assignment.
  //
  // This method returns a long running operation (LRO) that contains the
  // rollout details. The rollout can be cancelled by cancelling the LRO.
  //
  // If the LRO completes and is not cancelled, all revisions associated with
  // the OS policy assignment are deleted.
  //
  // For more information, see [Method:
  // projects.locations.osPolicyAssignments.operations.cancel](https://cloud.google.com/compute/docs/osconfig/rest/v1/projects.locations.osPolicyAssignments.operations/cancel).
  rpc DeleteOSPolicyAssignment(DeleteOSPolicyAssignmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/osPolicyAssignments/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OSPolicyAssignmentOperationMetadata"
    };
  }

  // Get the OS policy asssignment report for the specified Compute Engine VM
  // instance.
  rpc GetOSPolicyAssignmentReport(GetOSPolicyAssignmentReportRequest)
      returns (OSPolicyAssignmentReport) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*/osPolicyAssignments/*/report}"
    };
    option (google.api.method_signature) = "name";
  }

  // List OS policy asssignment reports for all Compute Engine VM instances in
  // the specified zone.
  rpc ListOSPolicyAssignmentReports(ListOSPolicyAssignmentReportsRequest)
      returns (ListOSPolicyAssignmentReportsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/instances/*/osPolicyAssignments/*}/reports"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get inventory data for the specified VM instance. If the VM has no
  // associated inventory, the message `NOT_FOUND` is returned.
  rpc GetInventory(GetInventoryRequest) returns (Inventory) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*/inventory}"
    };
    option (google.api.method_signature) = "name";
  }

  // List inventory data for all VM instances in the specified zone.
  rpc ListInventories(ListInventoriesRequest)
      returns (ListInventoriesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/instances/*}/inventories"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the vulnerability report for the specified VM instance. Only VMs with
  // inventory data have vulnerability reports associated with them.
  rpc GetVulnerabilityReport(GetVulnerabilityReportRequest)
      returns (VulnerabilityReport) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*/vulnerabilityReport}"
    };
    option (google.api.method_signature) = "name";
  }

  // List vulnerability reports for all VM instances in the specified zone.
  rpc ListVulnerabilityReports(ListVulnerabilityReportsRequest)
      returns (ListVulnerabilityReportsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/instances/*}/vulnerabilityReports"
    };
    option (google.api.method_signature) = "parent";
  }
}
