// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.agentendpoint.v1beta;

import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/osconfig/agentendpoint/v1beta/guest_policies.proto";
import "google/cloud/osconfig/agentendpoint/v1beta/tasks.proto";

option go_package = "cloud.google.com/go/osconfig/agentendpoint/apiv1beta/agentendpointpb;agentendpointpb";
option java_multiple_files = true;
option java_outer_classname = "AgentEndpointProto";
option java_package = "com.google.cloud.osconfig.agentendpoint.v1beta";
option php_namespace = "Google\\Cloud\\OsConfig\\V1beta";

// OS Config agent endpoint API.
service AgentEndpointService {
  option (google.api.default_host) = "osconfig.googleapis.com";

  // Stream established by client to receive Task notifications.
  rpc ReceiveTaskNotification(ReceiveTaskNotificationRequest) returns (stream ReceiveTaskNotificationResponse) {
    option (google.api.method_signature) = "instance_id_token,agent_version";
  }

  // Signals the start of a task execution and returns the task info.
  rpc StartNextTask(StartNextTaskRequest) returns (StartNextTaskResponse) {
    option (google.api.method_signature) = "instance_id_token";
  }

  // Signals an intermediary progress checkpoint in task execution.
  rpc ReportTaskProgress(ReportTaskProgressRequest) returns (ReportTaskProgressResponse) {
    option (google.api.method_signature) = "instance_id_token,task_id,task_type";
  }

  // Signals that the task execution is complete and optionally returns the next
  // task.
  rpc ReportTaskComplete(ReportTaskCompleteRequest) returns (ReportTaskCompleteResponse) {
    option (google.api.method_signature) = "instance_id_token,task_id,task_type,error_message";
  }

  // Lookup the effective guest policy that applies to a VM instance. This
  // lookup merges all policies that are assigned to the instance ancestry.
  rpc LookupEffectiveGuestPolicy(LookupEffectiveGuestPolicyRequest) returns (EffectiveGuestPolicy) {
    option (google.api.method_signature) = "instance_id_token,os_short_name,os_version,os_architecture";
  }

  // Registers the agent running on the VM.
  rpc RegisterAgent(RegisterAgentRequest) returns (RegisterAgentResponse) {
    option (google.api.method_signature) = "instance_id_token,agent_version,supported_capabilities";
  }
}

// A request message to receive task notifications.
message ReceiveTaskNotificationRequest {
  // Required. This is the Compute Engine instance identity token described in
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  // where the audience is 'osconfig.googleapis.com' and the format is 'full'.
  string instance_id_token = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The version of the agent making the request.
  string agent_version = 2 [(google.api.field_behavior) = REQUIRED];
}

// The streaming rpc message that notifies the agent when it has a task
// that it needs to perform on the VM instance.
message ReceiveTaskNotificationResponse {

}

// A request message for signaling the start of a task execution.
message StartNextTaskRequest {
  // Required. This is the Compute Engine instance identity token described in
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  // where the audience is 'osconfig.googleapis.com' and the format is 'full'.
  string instance_id_token = 1 [(google.api.field_behavior) = REQUIRED];
}

// A response message that contains the details of the task to work on.
message StartNextTaskResponse {
  // The details of the task that should be worked on.  Can be empty if there
  // is no new task to work on.
  Task task = 1;
}

// A request message for reporting the progress of current task.
message ReportTaskProgressRequest {
  // Required. This is the Compute Engine instance identity token described in
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  // where the audience is 'osconfig.googleapis.com' and the format is 'full'.
  string instance_id_token = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Unique identifier of the task this applies to.
  string task_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of task to report progress on.
  //
  // Progress must include the appropriate message based on this enum as
  // specified below:
  // APPLY_PATCHES = ApplyPatchesTaskProgress
  // EXEC_STEP = Progress not supported for this type.
  // APPLY_CONFIG_TASK = ApplyConfigTaskProgress
  TaskType task_type = 3 [(google.api.field_behavior) = REQUIRED];

  // Intermediate progress of the current task.
  oneof progress {
    // Details about the progress of the apply patches task.
    ApplyPatchesTaskProgress apply_patches_task_progress = 4;

    // Details about the progress of the exec step task.
    ExecStepTaskProgress exec_step_task_progress = 5;
  }
}

// The response message after the agent reported the current task progress.
message ReportTaskProgressResponse {
  // Instructs agent to continue or not.
  TaskDirective task_directive = 1;
}

// A request message for signaling the completion of a task execution.
message ReportTaskCompleteRequest {
  // Required. This is the Compute Engine instance identity token described in
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  // where the audience is 'osconfig.googleapis.com' and the format is 'full'.
  string instance_id_token = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Unique identifier of the task this applies to.
  string task_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of task to report completed.
  //
  // The output must include the appropriate message based on the following
  // enum values:
  // APPLY_PATCHES = ApplyPatchesTaskOutput
  // EXEC_STEP = ExecStepTaskOutput
  // APPLY_CONFIG_TASK = ApplyConfigTaskOutput
  TaskType task_type = 3 [(google.api.field_behavior) = REQUIRED];

  // Descriptive error message if the task execution ended in error.
  string error_message = 4;

  // Final output details of the current task.
  oneof output {
    // Final output details of the apply patches task;
    ApplyPatchesTaskOutput apply_patches_task_output = 5;

    // Final output details of the exec step task;
    ExecStepTaskOutput exec_step_task_output = 6;
  }
}

// The response message after the agent signaled the current task complete.
message ReportTaskCompleteResponse {

}

// The request message for registering the agent.
message RegisterAgentRequest {
  // Required. This is the Compute Engine instance identity token described in
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  // where the audience is 'osconfig.googleapis.com' and the format is 'full'.
  string instance_id_token = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The version of the agent.
  string agent_version = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The capabilities supported by the agent. Supported values are:
  // PATCH_GA
  // GUEST_POLICY_BETA
  // CONFIG_V1
  repeated string supported_capabilities = 3 [(google.api.field_behavior) = REQUIRED];

  // The operating system long name.
  // For example 'Debian GNU/Linux 9' or 'Microsoft Window Server 2019
  // Datacenter'.
  string os_long_name = 4;

  // The operating system short name.
  // For example, 'windows' or 'debian'.
  string os_short_name = 5;

  // The version of the operating system.
  string os_version = 6;

  // The system architecture of the operating system.
  string os_architecture = 7;
}

// The response message after the agent registered.
message RegisterAgentResponse {

}
