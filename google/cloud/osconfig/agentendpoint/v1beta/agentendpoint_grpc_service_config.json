{"methodConfig": [{"name": [{"service": "google.cloud.osconfig.agentendpoint.v1beta.AgentEndpointService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.osconfig.agentendpoint.v1beta.AgentEndpointService", "method": "ReceiveTaskNotification"}], "timeout": "3600s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "CANCELLED", "ABORTED", "INTERNAL", "UNAVAILABLE"]}}]}