type: google.api.Service
config_version: 3
name: osconfig.googleapis.com
title: OS Config API

apis:
- name: google.cloud.osconfig.agentendpoint.v1.AgentEndpointService

documentation:
  summary: |-
    OS management tools that can be used for patch management, patch
    compliance, and configuration management on VM instances.

backend:
  rules:
  - selector: 'google.cloud.osconfig.agentendpoint.v1.AgentEndpointService.*'
    deadline: 30.0
  - selector: google.cloud.osconfig.agentendpoint.v1.AgentEndpointService.ReceiveTaskNotification
    deadline: 3600.0

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/osPolicyAssignments/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/osPolicyAssignments/*/operations/*}'

authentication:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
