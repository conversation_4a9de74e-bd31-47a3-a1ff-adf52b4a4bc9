# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "agentendpoint_proto",
    srcs = [
        "agentendpoint.proto",
        "config_common.proto",
        "inventory.proto",
        "os_policy.proto",
        "patch_jobs.proto",
        "tasks.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "agentendpoint_proto_with_info",
    deps = [
        ":agentendpoint_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "agentendpoint_java_proto",
    deps = [":agentendpoint_proto"],
)

java_grpc_library(
    name = "agentendpoint_java_grpc",
    srcs = [":agentendpoint_proto"],
    deps = [":agentendpoint_java_proto"],
)

java_gapic_library(
    name = "agentendpoint_java_gapic",
    srcs = [":agentendpoint_proto_with_info"],
    gapic_yaml = "osconfig_gapic.yaml",
    grpc_service_config = "agentendpoint_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1.yaml",
    test_deps = [
        ":agentendpoint_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":agentendpoint_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "agentendpoint_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.osconfig.agentendpoint.v1.AgentEndpointServiceClientTest",
    ],
    runtime_deps = [":agentendpoint_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-osconfig-agentendpoint-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":agentendpoint_java_gapic",
        ":agentendpoint_java_grpc",
        ":agentendpoint_java_proto",
        ":agentendpoint_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "agentendpoint_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/osconfig/agentendpoint/apiv1/agentendpointpb",
    protos = [":agentendpoint_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "agentendpoint_go_gapic",
    srcs = [":agentendpoint_proto_with_info"],
    grpc_service_config = "agentendpoint_grpc_service_config.json",
    importpath = "cloud.google.com/go/osconfig/agentendpoint/apiv1;agentendpoint",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":agentendpoint_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-osconfig-agentendpoint-v1-go",
    deps = [
        ":agentendpoint_go_gapic",
        ":agentendpoint_go_gapic_srcjar-metadata.srcjar",
        ":agentendpoint_go_gapic_srcjar-snippets.srcjar",
        ":agentendpoint_go_gapic_srcjar-test.srcjar",
        ":agentendpoint_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "agentendpoint_py_gapic",
    srcs = [":agentendpoint_proto"],
    grpc_service_config = "agentendpoint_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1.yaml",
    transport = "grpc",
)

py_test(
    name = "agentendpoint_py_gapic_test",
    srcs = [
        "agentendpoint_py_gapic_pytest.py",
        "agentendpoint_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":agentendpoint_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "osconfig-agentendpoint-v1-py",
    deps = [
        ":agentendpoint_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "agentendpoint_php_proto",
    deps = [":agentendpoint_proto"],
)

php_gapic_library(
    name = "agentendpoint_php_gapic",
    srcs = [":agentendpoint_proto_with_info"],
    grpc_service_config = "agentendpoint_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1.yaml",
    transport = "grpc+rest",
    deps = [":agentendpoint_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-osconfig-agentendpoint-v1-php",
    deps = [
        ":agentendpoint_php_gapic",
        ":agentendpoint_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "agentendpoint_nodejs_gapic",
    package_name = "@google-cloud/agentendpoint",
    src = ":agentendpoint_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "agentendpoint_grpc_service_config.json",
    package = "google.cloud.osconfig.agentendpoint.v1",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "osconfig-agentendpoint-v1-nodejs",
    deps = [
        ":agentendpoint_nodejs_gapic",
        ":agentendpoint_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "agentendpoint_ruby_proto",
    deps = [":agentendpoint_proto"],
)

ruby_grpc_library(
    name = "agentendpoint_ruby_grpc",
    srcs = [":agentendpoint_proto"],
    deps = [":agentendpoint_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "agentendpoint_ruby_gapic",
    srcs = [":agentendpoint_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-agentendpoint-v1"],
    grpc_service_config = "agentendpoint_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1.yaml",
    transport = "grpc",
    deps = [
        ":agentendpoint_ruby_grpc",
        ":agentendpoint_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-osconfig-agentendpoint-v1-ruby",
    deps = [
        ":agentendpoint_ruby_gapic",
        ":agentendpoint_ruby_grpc",
        ":agentendpoint_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "agentendpoint_csharp_proto",
    deps = [":agentendpoint_proto"],
)

csharp_grpc_library(
    name = "agentendpoint_csharp_grpc",
    srcs = [":agentendpoint_proto"],
    deps = [":agentendpoint_csharp_proto"],
)

csharp_gapic_library(
    name = "agentendpoint_csharp_gapic",
    srcs = [":agentendpoint_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "agentendpoint_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "osconfig_v1.yaml",
    transport = "grpc",
    deps = [
        ":agentendpoint_csharp_grpc",
        ":agentendpoint_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-osconfig-agentendpoint-v1-csharp",
    deps = [
        ":agentendpoint_csharp_gapic",
        ":agentendpoint_csharp_grpc",
        ":agentendpoint_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "agentendpoint_cc_proto",
    deps = [":agentendpoint_proto"],
)

cc_grpc_library(
    name = "agentendpoint_cc_grpc",
    srcs = [":agentendpoint_proto"],
    grpc_only = True,
    deps = [":agentendpoint_cc_proto"],
)
