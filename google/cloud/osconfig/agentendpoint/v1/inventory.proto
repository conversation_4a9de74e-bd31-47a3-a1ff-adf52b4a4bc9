// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.agentendpoint.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "cloud.google.com/go/osconfig/agentendpoint/apiv1/agentendpointpb;agentendpointpb";
option java_multiple_files = true;
option java_outer_classname = "InventoryProto";
option java_package = "com.google.cloud.osconfig.agentendpoint.v1";

// OS Config Inventory is a service for collecting and reporting operating
// system and package information on VM instances.

// The inventory details of a VM.
message Inventory {
  // Operating system information for the VM.
  message OsInfo {
    // The VM hostname.
    string hostname = 1;

    // The operating system long name.
    // For example 'Debian GNU/Linux 9' or 'Microsoft Window Server 2019
    // Datacenter'.
    string long_name = 2;

    // The operating system short name.
    // For example, 'windows' or 'debian'.
    string short_name = 3;

    // The version of the operating system.
    string version = 4;

    // The system architecture of the operating system.
    string architecture = 5;

    // The kernel version of the operating system.
    string kernel_version = 6;

    // The kernel release of the operating system.
    string kernel_release = 7;

    // The current version of the OS Config agent running on the VM.
    string osconfig_agent_version = 8;
  }

  // Software package information of the operating system.
  message SoftwarePackage {
    // Information about the different types of software packages.
    oneof details {
      // Yum package info.
      // For details about the yum package manager, see
      // https://access.redhat.com/documentation/en-us/red_hat_enterprise_linux/6/html/deployment_guide/ch-yum.
      VersionedPackage yum_package = 1;

      // Details of an APT package.
      // For details about the apt package manager, see
      // https://wiki.debian.org/Apt.
      VersionedPackage apt_package = 2;

      // Details of a Zypper package.
      // For details about the Zypper package manager, see
      // https://en.opensuse.org/SDB:Zypper_manual.
      VersionedPackage zypper_package = 3;

      // Details of a Googet package.
      //  For details about the googet package manager, see
      //  https://github.com/google/googet.
      VersionedPackage googet_package = 4;

      // Details of a Zypper patch.
      // For details about the Zypper package manager, see
      // https://en.opensuse.org/SDB:Zypper_manual.
      ZypperPatch zypper_patch = 5;

      // Details of a Windows Update package.
      // See https://docs.microsoft.com/en-us/windows/win32/api/_wua/ for
      // information about Windows Update.
      WindowsUpdatePackage wua_package = 6;

      // Details of a Windows Quick Fix engineering package.
      // See
      // https://docs.microsoft.com/en-us/windows/win32/cimwin32prov/win32-quickfixengineering
      // for info in Windows Quick Fix Engineering.
      WindowsQuickFixEngineeringPackage qfe_package = 7;

      // Details of a COS package.
      VersionedPackage cos_package = 8;

      // Details of Windows Application.
      WindowsApplication windows_application = 9;
    }
  }

  // Information related to the a standard versioned package.  This includes
  // package info for APT, Yum, Zypper, and Googet package managers.
  message VersionedPackage {
    // Information related to the actuall source of the versioned package. This
    // includes source package name and version if available.
    message Source {
      // Required. The name of the source package.
      string name = 1 [(google.api.field_behavior) = REQUIRED];

      // Optional. The version of the source package.
      string version = 2 [(google.api.field_behavior) = OPTIONAL];
    }

    // The name of the package.
    string package_name = 1;

    // The system architecture this package is intended for.
    string architecture = 2;

    // The version of the package.
    string version = 3;

    // Optional. The source of the package.
    Source source = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // Details related to a Zypper Patch.
  message ZypperPatch {
    // The name of the patch.
    string patch_name = 1;

    // The category of the patch.
    string category = 2;

    // The severity specified for this patch
    string severity = 3;

    // Any summary information provided about this patch.
    string summary = 4;
  }

  // Details related to a Windows Update package.
  // Field data and names are taken from Windows Update API IUpdate Interface:
  // https://docs.microsoft.com/en-us/windows/win32/api/_wua/
  // Descriptive fields like title, and description are localized based on
  // the locale of the VM being updated.
  message WindowsUpdatePackage {
    // Categories specified by the Windows Update.
    message WindowsUpdateCategory {
      // The identifier of the windows update category.
      string id = 1;

      // The name of the windows update category.
      string name = 2;
    }

    // The localized title of the update package.
    string title = 1;

    // The localized description of the update package.
    string description = 2;

    // The categories that are associated with this update package.
    repeated WindowsUpdateCategory categories = 3;

    // A collection of Microsoft Knowledge Base article IDs that are associated
    // with the update package.
    repeated string kb_article_ids = 4;

    // A hyperlink to the language-specific support information for the update.
    string support_url = 5;

    // A collection of URLs that provide more information about the update
    // package.
    repeated string more_info_urls = 6;

    // Gets the identifier of an update package.  Stays the same across
    // revisions.
    string update_id = 7;

    // The revision number of this update package.
    int32 revision_number = 8;

    // The last published date of the update, in (UTC) date and time.
    google.protobuf.Timestamp last_deployment_change_time = 9;
  }

  // Information related to a Quick Fix Engineering package.
  // Fields are taken from Windows QuickFixEngineering Interface and match
  // the source names:
  // https://docs.microsoft.com/en-us/windows/win32/cimwin32prov/win32-quickfixengineering
  message WindowsQuickFixEngineeringPackage {
    // A short textual description of the QFE update.
    string caption = 1;

    // A textual description of the QFE update.
    string description = 2;

    // Unique identifier associated with a particular QFE update.
    string hot_fix_id = 3;

    // Date that the QFE update was installed.  Mapped from installed_on field.
    google.protobuf.Timestamp install_time = 4;
  }

  // Details about Windows Application - based on Windows Registry.
  // All fields in this message are taken from:
  // https://docs.microsoft.com/en-us/windows/win32/msi/uninstall-registry-key
  message WindowsApplication {
    // DisplayName field from Windows Registry.
    string display_name = 1;

    // DisplayVersion field from Windows Registry.
    string display_version = 2;

    // Publisher field from Windows Registry.
    string publisher = 3;

    // Installation date field from Windows Registry.
    google.type.Date install_date = 4;

    // HelpLink field from Windows Registry.
    string help_link = 5;
  }

  // Base level operating system information for the VM.
  OsInfo os_info = 1;

  // A list of installed packages currently on the VM.
  repeated SoftwarePackage installed_packages = 2;

  // A list of software updates available for the VM as reported by the update
  // managers.
  repeated SoftwarePackage available_packages = 3;
}
