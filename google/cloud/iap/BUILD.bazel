# This build file includes a target for the Ruby wrapper library for
# google-cloud-iap.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for iap.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "iap_ruby_wrapper",
    srcs = ["//google/cloud/iap/v1:iap_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-iap",
        "ruby-cloud-wrapper-of=v1:0.11",
        "ruby-cloud-env-prefix=IAP",
        "ruby-cloud-product-url=https://cloud.google.com/iap/",
        "ruby-cloud-api-id=iap.googleapis.com",
        "ruby-cloud-api-shortname=iap",
    ],
    ruby_cloud_description = "IAP lets you establish a central authorization layer for applications accessed by HTTPS, so you can use an application-level access control model instead of relying on network-level firewalls.",
    ruby_cloud_title = "Identity-Aware Proxy",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-iap-ruby",
    deps = [
        ":iap_ruby_wrapper",
    ],
)
