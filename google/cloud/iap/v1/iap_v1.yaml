type: google.api.Service
config_version: 3
name: iap.googleapis.com
title: Cloud Identity-Aware Proxy API

apis:
- name: google.cloud.iap.v1.IdentityAwareProxyAdminService
- name: google.cloud.iap.v1.IdentityAwareProxyOAuthService

documentation:
  summary: Controls access to cloud applications running on Google Cloud Platform.

authentication:
  rules:
  - selector: 'google.cloud.iap.v1.IdentityAwareProxyAdminService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.iap.v1.IdentityAwareProxyOAuthService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
