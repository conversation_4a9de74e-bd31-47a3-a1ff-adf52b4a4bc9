// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.iap.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Cloud.Iap.V1";
option go_package = "cloud.google.com/go/iap/apiv1/iappb;iappb";
option java_multiple_files = true;
option java_package = "com.google.cloud.iap.v1";
option php_namespace = "Google\\Cloud\\Iap\\V1";
option ruby_package = "Google::Cloud::Iap::V1";
option (google.api.resource_definition) = {
  type: "iap.googleapis.com/TunnelLocation"
  pattern: "projects/{project}/iap_tunnel/locations/{location}"
};

// The Cloud Identity-Aware Proxy API.

// APIs for Identity-Aware Proxy Admin configurations.
service IdentityAwareProxyAdminService {
  option (google.api.default_host) = "iap.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Sets the access control policy for an Identity-Aware Proxy protected
  // resource. Replaces any existing policy.
  // More information about managing access via IAP can be found at:
  // https://cloud.google.com/iap/docs/managing-access#managing_access_via_the_api
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=**}:setIamPolicy"
      body: "*"
    };
  }

  // Gets the access control policy for an Identity-Aware Proxy protected
  // resource.
  // More information about managing access via IAP can be found at:
  // https://cloud.google.com/iap/docs/managing-access#managing_access_via_the_api
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=**}:getIamPolicy"
      body: "*"
    };
  }

  // Returns permissions that a caller has on the Identity-Aware Proxy protected
  // resource.
  // More information about managing access via IAP can be found at:
  // https://cloud.google.com/iap/docs/managing-access#managing_access_via_the_api
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=**}:testIamPermissions"
      body: "*"
    };
  }

  // Gets the IAP settings on a particular IAP protected resource.
  rpc GetIapSettings(GetIapSettingsRequest) returns (IapSettings) {
    option (google.api.http) = {
      get: "/v1/{name=**}:iapSettings"
    };
  }

  // Updates the IAP settings on a particular IAP protected resource. It
  // replaces all fields unless the `update_mask` is set.
  rpc UpdateIapSettings(UpdateIapSettingsRequest) returns (IapSettings) {
    option (google.api.http) = {
      patch: "/v1/{iap_settings.name=**}:iapSettings"
      body: "iap_settings"
    };
  }

  // Lists the existing TunnelDestGroups. To group across all locations, use a
  // `-` as the location ID. For example:
  // `/v1/projects/123/iap_tunnel/locations/-/destGroups`
  rpc ListTunnelDestGroups(ListTunnelDestGroupsRequest)
      returns (ListTunnelDestGroupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/iap_tunnel/locations/*}/destGroups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new TunnelDestGroup.
  rpc CreateTunnelDestGroup(CreateTunnelDestGroupRequest)
      returns (TunnelDestGroup) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/iap_tunnel/locations/*}/destGroups"
      body: "tunnel_dest_group"
    };
    option (google.api.method_signature) =
        "parent,tunnel_dest_group,tunnel_dest_group_id";
  }

  // Retrieves an existing TunnelDestGroup.
  rpc GetTunnelDestGroup(GetTunnelDestGroupRequest) returns (TunnelDestGroup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/iap_tunnel/locations/*/destGroups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a TunnelDestGroup.
  rpc DeleteTunnelDestGroup(DeleteTunnelDestGroupRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/iap_tunnel/locations/*/destGroups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a TunnelDestGroup.
  rpc UpdateTunnelDestGroup(UpdateTunnelDestGroupRequest)
      returns (TunnelDestGroup) {
    option (google.api.http) = {
      patch: "/v1/{tunnel_dest_group.name=projects/*/iap_tunnel/locations/*/destGroups/*}"
      body: "tunnel_dest_group"
    };
    option (google.api.method_signature) = "tunnel_dest_group,update_mask";
  }
}

// API to programmatically create, list and retrieve Identity Aware Proxy (IAP)
// OAuth brands; and create, retrieve, delete and reset-secret of IAP OAuth
// clients.
service IdentityAwareProxyOAuthService {
  option (google.api.default_host) = "iap.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists the existing brands for the project.
  rpc ListBrands(ListBrandsRequest) returns (ListBrandsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/brands"
    };
  }

  // Constructs a new OAuth brand for the project if one does not exist.
  // The created brand is "internal only", meaning that OAuth clients created
  // under it only accept requests from users who belong to the same Google
  // Workspace organization as the project. The brand is created in an
  // un-reviewed status. NOTE: The "internal only" status can be manually
  // changed in the Google Cloud Console. Requires that a brand does not already
  // exist for the project, and that the specified support email is owned by the
  // caller.
  rpc CreateBrand(CreateBrandRequest) returns (Brand) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/brands"
      body: "brand"
    };
  }

  // Retrieves the OAuth brand of the project.
  rpc GetBrand(GetBrandRequest) returns (Brand) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/brands/*}"
    };
  }

  // Creates an Identity Aware Proxy (IAP) OAuth client. The client is owned
  // by IAP. Requires that the brand for the project exists and that it is
  // set for internal-only use.
  rpc CreateIdentityAwareProxyClient(CreateIdentityAwareProxyClientRequest)
      returns (IdentityAwareProxyClient) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/brands/*}/identityAwareProxyClients"
      body: "identity_aware_proxy_client"
    };
  }

  // Lists the existing clients for the brand.
  rpc ListIdentityAwareProxyClients(ListIdentityAwareProxyClientsRequest)
      returns (ListIdentityAwareProxyClientsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/brands/*}/identityAwareProxyClients"
    };
  }

  // Retrieves an Identity Aware Proxy (IAP) OAuth client.
  // Requires that the client is owned by IAP.
  rpc GetIdentityAwareProxyClient(GetIdentityAwareProxyClientRequest)
      returns (IdentityAwareProxyClient) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/brands/*/identityAwareProxyClients/*}"
    };
  }

  // Resets an Identity Aware Proxy (IAP) OAuth client secret. Useful if the
  // secret was compromised. Requires that the client is owned by IAP.
  rpc ResetIdentityAwareProxyClientSecret(
      ResetIdentityAwareProxyClientSecretRequest)
      returns (IdentityAwareProxyClient) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/brands/*/identityAwareProxyClients/*}:resetSecret"
      body: "*"
    };
  }

  // Deletes an Identity Aware Proxy (IAP) OAuth client. Useful for removing
  // obsolete clients, managing the number of clients in a given project, and
  // cleaning up after tests. Requires that the client is owned by IAP.
  rpc DeleteIdentityAwareProxyClient(DeleteIdentityAwareProxyClientRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/brands/*/identityAwareProxyClients/*}"
    };
  }
}

// The request to ListTunnelDestGroups.
message ListTunnelDestGroupsRequest {
  // Required. Google Cloud Project ID and location.
  // In the following format:
  // `projects/{project_number/id}/iap_tunnel/locations/{location}`.
  // A `-` can be used for the location to group across all locations.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iap.googleapis.com/TunnelLocation"
    }
  ];

  // The maximum number of groups to return. The service might return fewer than
  // this value.
  // If unspecified, at most 100 groups are returned.
  // The maximum value is 1000; values above 1000 are coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListTunnelDestGroups`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListTunnelDestGroups` must match the call that provided the page
  // token.
  string page_token = 3;
}

// The response from ListTunnelDestGroups.
message ListTunnelDestGroupsResponse {
  // TunnelDestGroup existing in the project.
  repeated TunnelDestGroup tunnel_dest_groups = 1;

  // A token that you can send as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request to CreateTunnelDestGroup.
message CreateTunnelDestGroupRequest {
  // Required. Google Cloud Project ID and location.
  // In the following format:
  // `projects/{project_number/id}/iap_tunnel/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "iap.googleapis.com/TunnelDestGroup"
    }
  ];

  // Required. The TunnelDestGroup to create.
  TunnelDestGroup tunnel_dest_group = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the TunnelDestGroup, which becomes the final
  // component of the resource name.
  //
  // This value must be 4-63 characters, and valid characters
  // are `[a-z]-`.
  string tunnel_dest_group_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// The request to GetTunnelDestGroup.
message GetTunnelDestGroupRequest {
  // Required. Name of the TunnelDestGroup to be fetched.
  // In the following format:
  // `projects/{project_number/id}/iap_tunnel/locations/{location}/destGroups/{dest_group}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iap.googleapis.com/TunnelDestGroup"
    }
  ];
}

// The request to DeleteTunnelDestGroup.
message DeleteTunnelDestGroupRequest {
  // Required. Name of the TunnelDestGroup to delete.
  // In the following format:
  // `projects/{project_number/id}/iap_tunnel/locations/{location}/destGroups/{dest_group}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "iap.googleapis.com/TunnelDestGroup"
    }
  ];
}

// The request to UpdateTunnelDestGroup.
message UpdateTunnelDestGroupRequest {
  // Required. The new values for the TunnelDestGroup.
  TunnelDestGroup tunnel_dest_group = 1
      [(google.api.field_behavior) = REQUIRED];

  // A field mask that specifies which IAP settings to update.
  // If omitted, then all of the settings are updated. See
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 2;
}

// A TunnelDestGroup.
message TunnelDestGroup {
  option (google.api.resource) = {
    type: "iap.googleapis.com/TunnelDestGroup"
    pattern: "projects/{project}/iap_tunnel/locations/{location}/destGroups/{dest_group}"
  };

  // Required. Immutable. Identifier for the TunnelDestGroup. Must be unique
  // within the project and contain only lower case letters (a-z) and dashes
  // (-).
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Unordered list. List of CIDRs that this group applies to.
  repeated string cidrs = 2 [(google.api.field_behavior) = UNORDERED_LIST];

  // Unordered list. List of FQDNs that this group applies to.
  repeated string fqdns = 3 [(google.api.field_behavior) = UNORDERED_LIST];
}

// The request sent to GetIapSettings.
message GetIapSettingsRequest {
  // Required. The resource name for which to retrieve the settings.
  // Authorization: Requires the `getSettings` permission for the associated
  // resource.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The request sent to UpdateIapSettings.
message UpdateIapSettingsRequest {
  // Required. The new values for the IAP settings to be updated.
  // Authorization: Requires the `updateSettings` permission for the associated
  // resource.
  IapSettings iap_settings = 1 [(google.api.field_behavior) = REQUIRED];

  // The field mask specifying which IAP settings should be updated.
  // If omitted, then all of the settings are updated. See
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.
  //
  // Note: All IAP reauth settings must always be set together, using the
  // field mask: `iapSettings.accessSettings.reauthSettings`.
  google.protobuf.FieldMask update_mask = 2;
}

// The IAP configurable settings.
message IapSettings {
  // Required. The resource name of the IAP protected resource.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Top level wrapper for all access related setting in IAP
  AccessSettings access_settings = 5;

  // Top level wrapper for all application related settings in IAP
  ApplicationSettings application_settings = 6;
}

// Access related settings for IAP protected apps.
message AccessSettings {
  // GCIP claims and endpoint configurations for 3p identity providers.
  GcipSettings gcip_settings = 1;

  // Configuration to allow cross-origin requests via IAP.
  CorsSettings cors_settings = 2;

  // Settings to configure IAP's OAuth behavior.
  OAuthSettings oauth_settings = 3;

  // Settings to configure reauthentication policies in IAP.
  ReauthSettings reauth_settings = 6;

  // Settings to configure and enable allowed domains.
  AllowedDomainsSettings allowed_domains_settings = 7;
}

// Allows customers to configure tenant_id for GCIP instance per-app.
message GcipSettings {
  // GCIP tenant ids that are linked to the IAP resource.
  // tenant_ids could be a string beginning with a number character to indicate
  // authenticating with GCIP tenant flow, or in the format of _<ProjectNumber>
  // to indicate authenticating with GCIP agent flow.
  // If agent flow is used, tenant_ids should only contain one single element,
  // while for tenant flow, tenant_ids can contain multiple elements.
  repeated string tenant_ids = 1;

  // Login page URI associated with the GCIP tenants.
  // Typically, all resources within the same project share the same login page,
  // though it could be overridden at the sub resource level.
  google.protobuf.StringValue login_page_uri = 2;
}

// Allows customers to configure HTTP request paths that'll allow HTTP OPTIONS
// call to bypass authentication and authorization.
message CorsSettings {
  // Configuration to allow HTTP OPTIONS calls to skip authorization. If
  // undefined, IAP will not apply any special logic to OPTIONS requests.
  google.protobuf.BoolValue allow_http_options = 1;
}

// Configuration for OAuth login&consent flow behavior as well as for OAuth
// Credentials.
message OAuthSettings {
  // Domain hint to send as hd=? parameter in OAuth request flow. Enables
  // redirect to primary IDP by skipping Google's login screen.
  // https://developers.google.com/identity/protocols/OpenIDConnect#hd-param
  // Note: IAP does not verify that the id token's hd claim matches this value
  // since access behavior is managed by IAM policies.
  google.protobuf.StringValue login_hint = 2;

  // List of OAuth client IDs allowed to programmatically authenticate with IAP.
  repeated string programmatic_clients = 5;
}

// Configuration for IAP reauthentication policies.
message ReauthSettings {
  // Types of reauthentication methods supported by IAP.
  enum Method {
    // Reauthentication disabled.
    METHOD_UNSPECIFIED = 0;

    // Prompts the user to log in again.
    LOGIN = 1;

    PASSWORD = 2 [deprecated = true];

    // User must use their secure key 2nd factor device.
    SECURE_KEY = 3;

    // User can use any enabled 2nd factor.
    ENROLLED_SECOND_FACTORS = 4;
  }

  // Type of policy in the case of hierarchial policies.
  enum PolicyType {
    // Default value. This value is unused.
    POLICY_TYPE_UNSPECIFIED = 0;

    // This policy acts as a minimum to other policies, lower in the hierarchy.
    // Effective policy may only be the same or stricter.
    MINIMUM = 1;

    // This policy acts as a default if no other reauth policy is set.
    DEFAULT = 2;
  }

  // Reauth method requested.
  Method method = 1;

  // Reauth session lifetime, how long before a user has to reauthenticate
  // again.
  google.protobuf.Duration max_age = 2;

  // How IAP determines the effective policy in cases of hierarchial policies.
  // Policies are merged from higher in the hierarchy to lower in the hierarchy.
  PolicyType policy_type = 3;
}

// Configuration for IAP allowed domains. Lets you to restrict access to an app
// and allow access to only the domains that you list.
message AllowedDomainsSettings {
  // Configuration for customers to opt in for the feature.
  optional bool enable = 1;

  // List of trusted domains.
  repeated string domains = 2;
}

// Wrapper over application specific settings for IAP.
message ApplicationSettings {
  // Settings to configure IAP's behavior for a service mesh.
  CsmSettings csm_settings = 1;

  // Customization for Access Denied page.
  AccessDeniedPageSettings access_denied_page_settings = 2;

  // The Domain value to set for cookies generated by IAP. This value is not
  // validated by the API, but will be ignored at runtime if invalid.
  google.protobuf.StringValue cookie_domain = 3;

  // Settings to configure attribute propagation.
  AttributePropagationSettings attribute_propagation_settings = 4;
}

// Configuration for RCToken generated for service mesh workloads protected by
// IAP. RCToken are IAP generated JWTs that can be verified at the application.
// The RCToken is primarily used for service mesh deployments, and can be scoped
// to a single mesh by configuring the audience field accordingly.
message CsmSettings {
  // Audience claim set in the generated RCToken. This value is not validated by
  // IAP.
  google.protobuf.StringValue rctoken_aud = 1;
}

// Custom content configuration for access denied page.
// IAP allows customers to define a custom URI to use as the error page when
// access is denied to users. If IAP prevents access to this page, the default
// IAP error page will be displayed instead.
message AccessDeniedPageSettings {
  // The URI to be redirected to when access is denied.
  google.protobuf.StringValue access_denied_page_uri = 1;

  // Whether to generate a troubleshooting URL on access denied events to this
  // application.
  google.protobuf.BoolValue generate_troubleshooting_uri = 2;

  // Whether to generate remediation token on access denied events to this
  // application.
  optional google.protobuf.BoolValue remediation_token_generation_enabled = 3;
}

// Configuration for propagating attributes to applications protected
// by IAP.
message AttributePropagationSettings {
  // Supported output credentials for attribute propagation. Each output
  // credential maps to a "field" in the response. For example, selecting JWT
  // will propagate all attributes in the IAP JWT, header in the headers, etc.
  enum OutputCredentials {
    // An output credential is required.
    OUTPUT_CREDENTIALS_UNSPECIFIED = 0;

    // Propagate attributes in the headers with "x-goog-iap-attr-" prefix.
    HEADER = 1;

    // Propagate attributes in the JWT of the form: `"additional_claims": {
    // "my_attribute": ["value1", "value2"] }`
    JWT = 2;

    // Propagate attributes in the RCToken of the form: `"additional_claims": {
    // "my_attribute": ["value1", "value2"] }`
    RCTOKEN = 3;
  }

  // Raw string CEL expression. Must return a list of attributes. A maximum of
  // 45 attributes can be selected. Expressions can select different attribute
  // types from `attributes`: `attributes.saml_attributes`,
  // `attributes.iap_attributes`. The following functions are supported:
  //
  //  - filter `<list>.filter(<iter_var>, <predicate>)`: Returns a subset of
  //  `<list>` where `<predicate>` is true for every item.
  //
  //  - in `<var> in <list>`: Returns true if `<list>` contains `<var>`.
  //
  //  - selectByName `<list>.selectByName(<string>)`: Returns the attribute
  //  in
  //  `<list>` with the given `<string>` name, otherwise returns empty.
  //
  //  - emitAs `<attribute>.emitAs(<string>)`: Sets the `<attribute>` name
  //  field to the given `<string>` for propagation in selected output
  //  credentials.
  //
  //  - strict `<attribute>.strict()`: Ignores the `x-goog-iap-attr-` prefix
  //  for the provided `<attribute>` when propagating with the `HEADER` output
  //  credential, such as request headers.
  //
  //  - append `<target_list>.append(<attribute>)` OR
  //  `<target_list>.append(<list>)`: Appends the provided `<attribute>` or
  //  `<list>` to the end of `<target_list>`.
  //
  // Example expression: `attributes.saml_attributes.filter(x, x.name in
  // ['test']).append(attributes.iap_attributes.selectByName('exact').emitAs('custom').strict())`
  optional string expression = 1;

  // Which output credentials attributes selected by the CEL expression should
  // be propagated in. All attributes will be fully duplicated in each selected
  // output credential.
  repeated OutputCredentials output_credentials = 2;

  // Whether the provided attribute propagation settings should be evaluated on
  // user requests. If set to true, attributes returned from the expression will
  // be propagated in the set output credentials.
  optional bool enable = 3;
}

// The request sent to ListBrands.
message ListBrandsRequest {
  // Required. GCP Project number/id.
  // In the following format: projects/{project_number/id}.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response message for ListBrands.
message ListBrandsResponse {
  // Brands existing in the project.
  repeated Brand brands = 1;
}

// The request sent to CreateBrand.
message CreateBrandRequest {
  // Required. GCP Project number/id under which the brand is to be created.
  // In the following format: projects/{project_number/id}.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The brand to be created.
  Brand brand = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request sent to GetBrand.
message GetBrandRequest {
  // Required. Name of the brand to be fetched.
  // In the following format: projects/{project_number/id}/brands/{brand}.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The request sent to ListIdentityAwareProxyClients.
message ListIdentityAwareProxyClientsRequest {
  // Required. Full brand path.
  // In the following format: projects/{project_number/id}/brands/{brand}.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of clients to return. The service may return fewer than
  // this value.
  // If unspecified, at most 100 clients will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListIdentityAwareProxyClients`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListIdentityAwareProxyClients` must match the call that provided the page
  // token.
  string page_token = 3;
}

// Response message for ListIdentityAwareProxyClients.
message ListIdentityAwareProxyClientsResponse {
  // Clients existing in the brand.
  repeated IdentityAwareProxyClient identity_aware_proxy_clients = 1;

  // A token, which can be send as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request sent to CreateIdentityAwareProxyClient.
message CreateIdentityAwareProxyClientRequest {
  // Required. Path to create the client in.
  // In the following format:
  // projects/{project_number/id}/brands/{brand}.
  // The project must belong to a G Suite account.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Identity Aware Proxy Client to be created.
  IdentityAwareProxyClient identity_aware_proxy_client = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request sent to GetIdentityAwareProxyClient.
message GetIdentityAwareProxyClientRequest {
  // Required. Name of the Identity Aware Proxy client to be fetched.
  // In the following format:
  // projects/{project_number/id}/brands/{brand}/identityAwareProxyClients/{client_id}.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The request sent to ResetIdentityAwareProxyClientSecret.
message ResetIdentityAwareProxyClientSecretRequest {
  // Required. Name of the Identity Aware Proxy client to that will have its
  // secret reset. In the following format:
  // projects/{project_number/id}/brands/{brand}/identityAwareProxyClients/{client_id}.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// The request sent to DeleteIdentityAwareProxyClient.
message DeleteIdentityAwareProxyClientRequest {
  // Required. Name of the Identity Aware Proxy client to be deleted.
  // In the following format:
  // projects/{project_number/id}/brands/{brand}/identityAwareProxyClients/{client_id}.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// OAuth brand data.
// NOTE: Only contains a portion of the data that describes a brand.
message Brand {
  // Output only. Identifier of the brand.
  // NOTE: GCP project number achieves the same brand identification purpose as
  // only one brand per project can be created.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Support email displayed on the OAuth consent screen.
  string support_email = 2;

  // Application name displayed on OAuth consent screen.
  string application_title = 3;

  // Output only. Whether the brand is only intended for usage inside the
  // G Suite organization only.
  bool org_internal_only = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains the data that describes an Identity Aware Proxy owned client.
message IdentityAwareProxyClient {
  // Output only. Unique identifier of the OAuth client.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Client secret of the OAuth client.
  string secret = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Human-friendly name given to the OAuth client.
  string display_name = 3;
}
