# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "iap_proto",
    srcs = [
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "iap_proto_with_info",
    deps = [
        ":iap_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "iap_java_proto",
    deps = [":iap_proto"],
)

java_grpc_library(
    name = "iap_java_grpc",
    srcs = [":iap_proto"],
    deps = [":iap_java_proto"],
)

java_gapic_library(
    name = "iap_java_gapic",
    srcs = [":iap_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "iap_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "iap_v1.yaml",
    test_deps = [
        ":iap_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":iap_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "iap_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.iap.v1.IdentityAwareProxyAdminServiceClientHttpJsonTest",
        "com.google.cloud.iap.v1.IdentityAwareProxyAdminServiceClientTest",
        "com.google.cloud.iap.v1.IdentityAwareProxyOAuthServiceClientHttpJsonTest",
        "com.google.cloud.iap.v1.IdentityAwareProxyOAuthServiceClientTest",
    ],
    runtime_deps = [":iap_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-iap-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":iap_java_gapic",
        ":iap_java_grpc",
        ":iap_java_proto",
        ":iap_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "iap_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/iap/apiv1/iappb",
    protos = [":iap_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

go_gapic_library(
    name = "iap_go_gapic",
    srcs = [":iap_proto_with_info"],
    grpc_service_config = "iap_grpc_service_config.json",
    importpath = "cloud.google.com/go/iap/apiv1;iap",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "iap_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":iap_go_proto",
        "//google/iam/v1:iam_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-iap-v1-go",
    deps = [
        ":iap_go_gapic",
        ":iap_go_gapic_srcjar-metadata.srcjar",
        ":iap_go_gapic_srcjar-snippets.srcjar",
        ":iap_go_gapic_srcjar-test.srcjar",
        ":iap_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "iap_py_gapic",
    srcs = [":iap_proto"],
    grpc_service_config = "iap_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "iap_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "iap_py_gapic_test",
    srcs = [
        "iap_py_gapic_pytest.py",
        "iap_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":iap_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "iap-v1-py",
    deps = [
        ":iap_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "iap_php_proto",
    deps = [":iap_proto"],
)

php_gapic_library(
    name = "iap_php_gapic",
    srcs = [":iap_proto_with_info"],
    grpc_service_config = "iap_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "iap_v1.yaml",
    transport = "grpc+rest",
    deps = [":iap_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-iap-v1-php",
    deps = [
        ":iap_php_gapic",
        ":iap_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "iap_nodejs_gapic",
    package_name = "@google-cloud/iap",
    src = ":iap_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "iap_grpc_service_config.json",
    package = "google.cloud.iap.v1",
    rest_numeric_enums = True,
    service_yaml = "iap_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "iap-v1-nodejs",
    deps = [
        ":iap_nodejs_gapic",
        ":iap_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "iap_ruby_proto",
    deps = [":iap_proto"],
)

ruby_grpc_library(
    name = "iap_ruby_grpc",
    srcs = [":iap_proto"],
    deps = [":iap_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "iap_ruby_gapic",
    srcs = [":iap_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=iap.googleapis.com",
        "ruby-cloud-api-shortname=iap",
        "ruby-cloud-env-prefix=IAP",
        "ruby-cloud-gem-name=google-cloud-iap-v1",
        "ruby-cloud-product-url=https://cloud.google.com/iap/",
    ],
    grpc_service_config = "iap_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "IAP lets you establish a central authorization layer for applications accessed by HTTPS, so you can use an application-level access control model instead of relying on network-level firewalls.",
    ruby_cloud_title = "Identity-Aware Proxy V1",
    service_yaml = "iap_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":iap_ruby_grpc",
        ":iap_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-iap-v1-ruby",
    deps = [
        ":iap_ruby_gapic",
        ":iap_ruby_grpc",
        ":iap_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "iap_csharp_proto",
    deps = [":iap_proto"],
)

csharp_grpc_library(
    name = "iap_csharp_grpc",
    srcs = [":iap_proto"],
    deps = [":iap_csharp_proto"],
)

csharp_gapic_library(
    name = "iap_csharp_gapic",
    srcs = [":iap_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "iap_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "iap_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":iap_csharp_grpc",
        ":iap_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-iap-v1-csharp",
    deps = [
        ":iap_csharp_gapic",
        ":iap_csharp_grpc",
        ":iap_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "iap_cc_proto",
    deps = [":iap_proto"],
)

cc_grpc_library(
    name = "iap_cc_grpc",
    srcs = [":iap_proto"],
    grpc_only = True,
    deps = [":iap_cc_proto"],
)
