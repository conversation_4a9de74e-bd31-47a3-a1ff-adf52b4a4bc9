// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.iap.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";

option go_package = "cloud.google.com/go/iap/apiv1beta1/iappb;iappb";
option java_multiple_files = true;
option java_package = "com.google.cloud.iap.v1beta1";

// APIs for Identity-Aware Proxy Admin configurations.
service IdentityAwareProxyAdminV1Beta1 {
  option (google.api.default_host) = "iap.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Sets the access control policy for an Identity-Aware Proxy protected
  // resource. Replaces any existing policy.
  // More information about managing access via IAP can be found at:
  // https://cloud.google.com/iap/docs/managing-access#managing_access_via_the_api
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=**}:setIamPolicy"
      body: "*"
    };
  }

  // Gets the access control policy for an Identity-Aware Proxy protected
  // resource.
  // More information about managing access via IAP can be found at:
  // https://cloud.google.com/iap/docs/managing-access#managing_access_via_the_api
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=**}:getIamPolicy"
      body: "*"
    };
  }

  // Returns permissions that a caller has on the Identity-Aware Proxy protected
  // resource. If the resource does not exist or the caller does not have
  // Identity-Aware Proxy permissions a [google.rpc.Code.PERMISSION_DENIED]
  // will be returned.
  // More information about managing access via IAP can be found at:
  // https://cloud.google.com/iap/docs/managing-access#managing_access_via_the_api
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=**}:testIamPermissions"
      body: "*"
    };
  }
}
