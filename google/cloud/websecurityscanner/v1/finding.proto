// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.websecurityscanner.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/websecurityscanner/v1/finding_addon.proto";

option csharp_namespace = "Google.Cloud.WebSecurityScanner.V1";
option go_package = "cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "FindingProto";
option java_package = "com.google.cloud.websecurityscanner.v1";
option php_namespace = "Google\\Cloud\\WebSecurityScanner\\V1";
option ruby_package = "Google::Cloud::WebSecurityScanner::V1";

// A Finding resource represents a vulnerability instance identified during a
// ScanRun.
message Finding {
  option (google.api.resource) = {
    type: "websecurityscanner.googleapis.com/Finding"
    pattern: "projects/{project}/scanConfigs/{scan_config}/scanRuns/{scan_run}/findings/{finding}"
  };

  // The severity level of a vulnerability.
  enum Severity {
    // No severity specified. The default value.
    SEVERITY_UNSPECIFIED = 0;

    // Critical severity.
    CRITICAL = 1;

    // High severity.
    HIGH = 2;

    // Medium severity.
    MEDIUM = 3;

    // Low severity.
    LOW = 4;
  }

  // Output only. The resource name of the Finding. The name follows the format of
  // 'projects/{projectId}/scanConfigs/{scanConfigId}/scanruns/{scanRunId}/findings/{findingId}'.
  // The finding IDs are generated by the system.
  string name = 1;

  // Output only. The type of the Finding.
  // Detailed and up-to-date information on findings can be found here:
  // https://cloud.google.com/security-command-center/docs/how-to-remediate-web-security-scanner-findings
  string finding_type = 2;

  // Output only. The severity level of the reported vulnerability.
  Severity severity = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The http method of the request that triggered the vulnerability, in
  // uppercase.
  string http_method = 3;

  // Output only. The URL produced by the server-side fuzzer and used in the request that
  // triggered the vulnerability.
  string fuzzed_url = 4;

  // Output only. The body of the request that triggered the vulnerability.
  string body = 5;

  // Output only. The description of the vulnerability.
  string description = 6;

  // Output only. The URL containing human-readable payload that user can leverage to
  // reproduce the vulnerability.
  string reproduction_url = 7;

  // Output only. If the vulnerability was originated from nested IFrame, the immediate
  // parent IFrame is reported.
  string frame_url = 8;

  // Output only. The URL where the browser lands when the vulnerability is detected.
  string final_url = 9;

  // Output only. The tracking ID uniquely identifies a vulnerability instance across
  // multiple ScanRuns.
  string tracking_id = 10;

  // Output only. An addon containing information reported for a vulnerability with an HTML
  // form, if any.
  Form form = 16;

  // Output only. An addon containing information about outdated libraries.
  OutdatedLibrary outdated_library = 11;

  // Output only. An addon containing detailed information regarding any resource causing the
  // vulnerability such as JavaScript sources, image, audio files, etc.
  ViolatingResource violating_resource = 12;

  // Output only. An addon containing information about vulnerable or missing HTTP headers.
  VulnerableHeaders vulnerable_headers = 15;

  // Output only. An addon containing information about request parameters which were found
  // to be vulnerable.
  VulnerableParameters vulnerable_parameters = 13;

  // Output only. An addon containing information reported for an XSS, if any.
  Xss xss = 14;

  // Output only. An addon containing information reported for an XXE, if any.
  Xxe xxe = 18 [(google.api.field_behavior) = OUTPUT_ONLY];
}
