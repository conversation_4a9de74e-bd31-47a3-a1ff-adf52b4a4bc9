// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.websecurityscanner.v1;

import "google/cloud/websecurityscanner/v1/scan_run_error_trace.proto";
import "google/cloud/websecurityscanner/v1/scan_run_warning_trace.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.WebSecurityScanner.V1";
option go_package = "cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "ScanRunProto";
option java_package = "com.google.cloud.websecurityscanner.v1";
option php_namespace = "Google\\Cloud\\WebSecurityScanner\\V1";
option ruby_package = "Google::Cloud::WebSecurityScanner::V1";

// A ScanRun is a output-only resource representing an actual run of the scan.
// Next id: 12
message ScanRun {
  // Types of ScanRun execution state.
  enum ExecutionState {
    // Represents an invalid state caused by internal server error. This value
    // should never be returned.
    EXECUTION_STATE_UNSPECIFIED = 0;

    // The scan is waiting in the queue.
    QUEUED = 1;

    // The scan is in progress.
    SCANNING = 2;

    // The scan is either finished or stopped by user.
    FINISHED = 3;
  }

  // Types of ScanRun result state.
  enum ResultState {
    // Default value. This value is returned when the ScanRun is not yet
    // finished.
    RESULT_STATE_UNSPECIFIED = 0;

    // The scan finished without errors.
    SUCCESS = 1;

    // The scan finished with errors.
    ERROR = 2;

    // The scan was terminated by user.
    KILLED = 3;
  }

  // Output only. The resource name of the ScanRun. The name follows the format of
  // 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
  // The ScanRun IDs are generated by the system.
  string name = 1;

  // Output only. The execution state of the ScanRun.
  ExecutionState execution_state = 2;

  // Output only. The result state of the ScanRun. This field is only available after the
  // execution state reaches "FINISHED".
  ResultState result_state = 3;

  // Output only. The time at which the ScanRun started.
  google.protobuf.Timestamp start_time = 4;

  // Output only. The time at which the ScanRun reached termination state - that the ScanRun
  // is either finished or stopped by user.
  google.protobuf.Timestamp end_time = 5;

  // Output only. The number of URLs crawled during this ScanRun. If the scan is in progress,
  // the value represents the number of URLs crawled up to now.
  int64 urls_crawled_count = 6;

  // Output only. The number of URLs tested during this ScanRun. If the scan is in progress,
  // the value represents the number of URLs tested up to now. The number of
  // URLs tested is usually larger than the number URLS crawled because
  // typically a crawled URL is tested with multiple test payloads.
  int64 urls_tested_count = 7;

  // Output only. Whether the scan run has found any vulnerabilities.
  bool has_vulnerabilities = 8;

  // Output only. The percentage of total completion ranging from 0 to 100.
  // If the scan is in queue, the value is 0.
  // If the scan is running, the value ranges from 0 to 100.
  // If the scan is finished, the value is 100.
  int32 progress_percent = 9;

  // Output only. If result_state is an ERROR, this field provides the primary reason for
  // scan's termination and more details, if such are available.
  ScanRunErrorTrace error_trace = 10;

  // Output only. A list of warnings, if such are encountered during this scan run.
  repeated ScanRunWarningTrace warning_traces = 11;
}
