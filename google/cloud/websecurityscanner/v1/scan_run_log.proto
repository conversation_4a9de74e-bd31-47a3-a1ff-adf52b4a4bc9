// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.websecurityscanner.v1;

import "google/cloud/websecurityscanner/v1/scan_run.proto";
import "google/cloud/websecurityscanner/v1/scan_run_error_trace.proto";

option csharp_namespace = "Google.Cloud.WebSecurityScanner.V1";
option go_package = "cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "ScanRunLogProto";
option java_package = "com.google.cloud.websecurityscanner.v1";
option php_namespace = "Google\\Cloud\\WebSecurityScanner\\V1";
option ruby_package = "Google::Cloud::WebSecurityScanner::V1";

// A ScanRunLog is an output-only proto used for Stackdriver customer logging.
// It is used for logs covering the start and end of scan pipelines.
// Other than an added summary, this is a subset of the ScanRun.
// Representation in logs is either a proto Struct, or converted to JSON.
// Next id: 9
message ScanRunLog {
  // Human friendly message about the event.
  string summary = 1;

  // The resource name of the ScanRun being logged.
  string name = 2;

  // The execution state of the ScanRun.
  google.cloud.websecurityscanner.v1.ScanRun.ExecutionState execution_state = 3;

  // The result state of the ScanRun.
  google.cloud.websecurityscanner.v1.ScanRun.ResultState result_state = 4;

  int64 urls_crawled_count = 5;

  int64 urls_tested_count = 6;

  bool has_findings = 7;

  google.cloud.websecurityscanner.v1.ScanRunErrorTrace error_trace = 8;
}
