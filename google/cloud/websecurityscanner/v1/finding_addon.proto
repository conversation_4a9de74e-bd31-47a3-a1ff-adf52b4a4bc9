// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.websecurityscanner.v1;

option csharp_namespace = "Google.Cloud.WebSecurityScanner.V1";
option go_package = "cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "FindingAddonProto";
option java_package = "com.google.cloud.websecurityscanner.v1";
option php_namespace = "Google\\Cloud\\WebSecurityScanner\\V1";
option ruby_package = "Google::Cloud::WebSecurityScanner::V1";

// ! Information about a vulnerability with an HTML.
message Form {
  // ! The URI where to send the form when it's submitted.
  string action_uri = 1;

  // ! The names of form fields related to the vulnerability.
  repeated string fields = 2;
}

// Information reported for an outdated library.
message OutdatedLibrary {
  // The name of the outdated library.
  string library_name = 1;

  // The version number.
  string version = 2;

  // URLs to learn more information about the vulnerabilities in the library.
  repeated string learn_more_urls = 3;
}

// Information regarding any resource causing the vulnerability such
// as JavaScript sources, image, audio files, etc.
message ViolatingResource {
  // The MIME type of this resource.
  string content_type = 1;

  // URL of this violating resource.
  string resource_url = 2;
}

// Information about vulnerable request parameters.
message VulnerableParameters {
  // The vulnerable parameter names.
  repeated string parameter_names = 1;
}

// Information about vulnerable or missing HTTP Headers.
message VulnerableHeaders {
  // Describes a HTTP Header.
  message Header {
    // Header name.
    string name = 1;

    // Header value.
    string value = 2;
  }

  // List of vulnerable headers.
  repeated Header headers = 1;

  // List of missing headers.
  repeated Header missing_headers = 2;
}

// Information reported for an XSS.
message Xss {
  // Types of XSS attack vector.
  enum AttackVector {
    // Unknown attack vector.
    ATTACK_VECTOR_UNSPECIFIED = 0;

    // The attack comes from fuzzing the browser's localStorage.
    LOCAL_STORAGE = 1;

    // The attack comes from fuzzing the browser's sessionStorage.
    SESSION_STORAGE = 2;

    // The attack comes from fuzzing the window's name property.
    WINDOW_NAME = 3;

    // The attack comes from fuzzing the referrer property.
    REFERRER = 4;

    // The attack comes from fuzzing an input element.
    FORM_INPUT = 5;

    // The attack comes from fuzzing the browser's cookies.
    COOKIE = 6;

    // The attack comes from hijacking the post messaging mechanism.
    POST_MESSAGE = 7;

    // The attack comes from fuzzing parameters in the url.
    GET_PARAMETERS = 8;

    // The attack comes from fuzzing the fragment in the url.
    URL_FRAGMENT = 9;

    // The attack comes from fuzzing the HTML comments.
    HTML_COMMENT = 10;

    // The attack comes from fuzzing the POST parameters.
    POST_PARAMETERS = 11;

    // The attack comes from fuzzing the protocol.
    PROTOCOL = 12;

    // The attack comes from the server side and is stored.
    STORED_XSS = 13;

    // The attack is a Same-Origin Method Execution attack via a GET parameter.
    SAME_ORIGIN = 14;

    // The attack payload is received from a third-party host via a URL that is
    // user-controllable
    USER_CONTROLLABLE_URL = 15;
  }

  // Stack traces leading to the point where the XSS occurred.
  repeated string stack_traces = 1;

  // An error message generated by a javascript breakage.
  string error_message = 2;

  // The attack vector of the payload triggering this XSS.
  AttackVector attack_vector = 3;

  // The reproduction url for the seeding POST request of a Stored XSS.
  string stored_xss_seeding_url = 4;
}

// Information reported for an XXE.
message Xxe {
  // Locations within a request where XML was substituted.
  enum Location {
    // Unknown Location.
    LOCATION_UNSPECIFIED = 0;

    // The XML payload replaced the complete request body.
    COMPLETE_REQUEST_BODY = 1;
  }

  // The XML string that triggered the XXE vulnerability. Non-payload values
  // might be redacted.
  string payload_value = 1;

  // Location within the request where the payload was placed.
  Location payload_location = 2;
}
