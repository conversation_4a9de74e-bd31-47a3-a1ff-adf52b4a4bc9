{"methodConfig": [{"name": [{"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "CreateScanConfig"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "UpdateScanConfig"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "StartScanRun"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "StopScanRun"}], "timeout": "600s"}, {"name": [{"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "DeleteScanConfig"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "GetScanConfig"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "ListScanConfigs"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "GetScanRun"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "ListScanRuns"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "GetFinding"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "ListFindings"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "ListFindingTypeStats"}, {"service": "google.cloud.websecurityscanner.v1.WebSecurityScanner", "method": "ListCrawledUrls"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}