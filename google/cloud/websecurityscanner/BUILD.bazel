# This build file includes a target for the Ruby wrapper library for
# google-cloud-web_security_scanner.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for websecurityscanner.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "websecurityscanner_ruby_wrapper",
    srcs = ["//google/cloud/websecurityscanner/v1:websecurityscanner_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-web_security_scanner",
        "ruby-cloud-env-prefix=WEB_SECURITY_SCANNER",
        "ruby-cloud-wrapper-of=v1:0.9;v1beta:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/security-command-center/docs/concepts-web-security-scanner-overview/",
        "ruby-cloud-api-id=websecurityscanner.googleapis.com",
        "ruby-cloud-api-shortname=websecurityscanner",
    ],
    ruby_cloud_description = "Web Security Scanner scans your Compute and App Engine apps for common web vulnerabilities.",
    ruby_cloud_title = "Web Security Scanner",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-websecurityscanner-ruby",
    deps = [
        ":websecurityscanner_ruby_wrapper",
    ],
)
