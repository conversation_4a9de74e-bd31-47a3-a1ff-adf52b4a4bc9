// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.websecurityscanner.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/websecurityscanner/v1alpha/crawled_url.proto";
import "google/cloud/websecurityscanner/v1alpha/finding.proto";
import "google/cloud/websecurityscanner/v1alpha/finding_type_stats.proto";
import "google/cloud/websecurityscanner/v1alpha/scan_config.proto";
import "google/cloud/websecurityscanner/v1alpha/scan_run.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/websecurityscanner/apiv1alpha/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "WebSecurityScannerProto";
option java_package = "com.google.cloud.websecurityscanner.v1alpha";

// Cloud Web Security Scanner Service identifies security vulnerabilities in web
// applications hosted on Google Cloud Platform. It crawls your application, and
// attempts to exercise as many user inputs and event handlers as possible.
service WebSecurityScanner {
  option (google.api.default_host) = "websecurityscanner.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new ScanConfig.
  rpc CreateScanConfig(CreateScanConfigRequest) returns (ScanConfig) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*}/scanConfigs"
      body: "scan_config"
    };
    option (google.api.method_signature) = "parent,scan_config";
  }

  // Deletes an existing ScanConfig and its child resources.
  rpc DeleteScanConfig(DeleteScanConfigRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=projects/*/scanConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a ScanConfig.
  rpc GetScanConfig(GetScanConfigRequest) returns (ScanConfig) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/scanConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ScanConfigs under a given project.
  rpc ListScanConfigs(ListScanConfigsRequest) returns (ListScanConfigsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*}/scanConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a ScanConfig. This method support partial update of a ScanConfig.
  rpc UpdateScanConfig(UpdateScanConfigRequest) returns (ScanConfig) {
    option (google.api.http) = {
      patch: "/v1alpha/{scan_config.name=projects/*/scanConfigs/*}"
      body: "scan_config"
    };
    option (google.api.method_signature) = "scan_config,update_mask";
  }

  // Start a ScanRun according to the given ScanConfig.
  rpc StartScanRun(StartScanRunRequest) returns (ScanRun) {
    option (google.api.http) = {
      post: "/v1alpha/{name=projects/*/scanConfigs/*}:start"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a ScanRun.
  rpc GetScanRun(GetScanRunRequest) returns (ScanRun) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/scanConfigs/*/scanRuns/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists ScanRuns under a given ScanConfig, in descending order of ScanRun
  // stop time.
  rpc ListScanRuns(ListScanRunsRequest) returns (ListScanRunsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/scanConfigs/*}/scanRuns"
    };
    option (google.api.method_signature) = "parent";
  }

  // Stops a ScanRun. The stopped ScanRun is returned.
  rpc StopScanRun(StopScanRunRequest) returns (ScanRun) {
    option (google.api.http) = {
      post: "/v1alpha/{name=projects/*/scanConfigs/*/scanRuns/*}:stop"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // List CrawledUrls under a given ScanRun.
  rpc ListCrawledUrls(ListCrawledUrlsRequest) returns (ListCrawledUrlsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/scanConfigs/*/scanRuns/*}/crawledUrls"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a Finding.
  rpc GetFinding(GetFindingRequest) returns (Finding) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/scanConfigs/*/scanRuns/*/findings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List Findings under a given ScanRun.
  rpc ListFindings(ListFindingsRequest) returns (ListFindingsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/scanConfigs/*/scanRuns/*}/findings"
    };
    option (google.api.method_signature) = "parent,filter";
  }

  // List all FindingTypeStats under a given ScanRun.
  rpc ListFindingTypeStats(ListFindingTypeStatsRequest) returns (ListFindingTypeStatsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/scanConfigs/*/scanRuns/*}/findingTypeStats"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request for the `CreateScanConfig` method.
message CreateScanConfigRequest {
  // Required. The parent resource name where the scan is created, which should be a
  // project resource name in the format 'projects/{projectId}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The ScanConfig to be created.
  ScanConfig scan_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `DeleteScanConfig` method.
message DeleteScanConfigRequest {
  // Required. The resource name of the ScanConfig to be deleted. The name follows the
  // format of 'projects/{projectId}/scanConfigs/{scanConfigId}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "websecurityscanner.googleapis.com/ScanConfig"
    }
  ];
}

// Request for the `GetScanConfig` method.
message GetScanConfigRequest {
  // Required. The resource name of the ScanConfig to be returned. The name follows the
  // format of 'projects/{projectId}/scanConfigs/{scanConfigId}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "websecurityscanner.googleapis.com/ScanConfig"
    }
  ];
}

// Request for the `ListScanConfigs` method.
message ListScanConfigsRequest {
  // Required. The parent resource name, which should be a project resource name in the
  // format 'projects/{projectId}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // A token identifying a page of results to be returned. This should be a
  // `next_page_token` value returned from a previous List request.
  // If unspecified, the first page of results is returned.
  string page_token = 2;

  // The maximum number of ScanConfigs to return, can be limited by server.
  // If not specified or not positive, the implementation will select a
  // reasonable value.
  int32 page_size = 3;
}

// Request for the `UpdateScanConfigRequest` method.
message UpdateScanConfigRequest {
  // Required. The ScanConfig to be updated. The name field must be set to identify the
  // resource to be updated. The values of fields not covered by the mask
  // will be ignored.
  ScanConfig scan_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource. For the `FieldMask` definition,
  // see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response for the `ListScanConfigs` method.
message ListScanConfigsResponse {
  // The list of ScanConfigs returned.
  repeated ScanConfig scan_configs = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request for the `StartScanRun` method.
message StartScanRunRequest {
  // Required. The resource name of the ScanConfig to be used. The name follows the
  // format of 'projects/{projectId}/scanConfigs/{scanConfigId}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "websecurityscanner.googleapis.com/ScanConfig"
    }
  ];
}

// Request for the `GetScanRun` method.
message GetScanRunRequest {
  // Required. The resource name of the ScanRun to be returned. The name follows the
  // format of
  // 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "websecurityscanner.googleapis.com/ScanRun"
    }
  ];
}

// Request for the `ListScanRuns` method.
message ListScanRunsRequest {
  // Required. The parent resource name, which should be a scan resource name in the
  // format 'projects/{projectId}/scanConfigs/{scanConfigId}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "websecurityscanner.googleapis.com/ScanConfig"
    }
  ];

  // A token identifying a page of results to be returned. This should be a
  // `next_page_token` value returned from a previous List request.
  // If unspecified, the first page of results is returned.
  string page_token = 2;

  // The maximum number of ScanRuns to return, can be limited by server.
  // If not specified or not positive, the implementation will select a
  // reasonable value.
  int32 page_size = 3;
}

// Response for the `ListScanRuns` method.
message ListScanRunsResponse {
  // The list of ScanRuns returned.
  repeated ScanRun scan_runs = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request for the `StopScanRun` method.
message StopScanRunRequest {
  // Required. The resource name of the ScanRun to be stopped. The name follows the
  // format of
  // 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "websecurityscanner.googleapis.com/ScanRun"
    }
  ];
}

// Request for the `ListCrawledUrls` method.
message ListCrawledUrlsRequest {
  // Required. The parent resource name, which should be a scan run resource name in the
  // format
  // 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "websecurityscanner.googleapis.com/ScanRun"
    }
  ];

  // A token identifying a page of results to be returned. This should be a
  // `next_page_token` value returned from a previous List request.
  // If unspecified, the first page of results is returned.
  string page_token = 2;

  // The maximum number of CrawledUrls to return, can be limited by server.
  // If not specified or not positive, the implementation will select a
  // reasonable value.
  int32 page_size = 3;
}

// Response for the `ListCrawledUrls` method.
message ListCrawledUrlsResponse {
  // The list of CrawledUrls returned.
  repeated CrawledUrl crawled_urls = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request for the `GetFinding` method.
message GetFindingRequest {
  // Required. The resource name of the Finding to be returned. The name follows the
  // format of
  // 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}/findings/{findingId}'.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "websecurityscanner.googleapis.com/Finding"
    }
  ];
}

// Request for the `ListFindings` method.
message ListFindingsRequest {
  // Required. The parent resource name, which should be a scan run resource name in the
  // format
  // 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "websecurityscanner.googleapis.com/ScanRun"
    }
  ];

  // Required. The filter expression. The expression must be in the format: <field>
  // <operator> <value>.
  // Supported field: 'finding_type'.
  // Supported operator: '='.
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // A token identifying a page of results to be returned. This should be a
  // `next_page_token` value returned from a previous List request.
  // If unspecified, the first page of results is returned.
  string page_token = 3;

  // The maximum number of Findings to return, can be limited by server.
  // If not specified or not positive, the implementation will select a
  // reasonable value.
  int32 page_size = 4;
}

// Response for the `ListFindings` method.
message ListFindingsResponse {
  // The list of Findings returned.
  repeated Finding findings = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// Request for the `ListFindingTypeStats` method.
message ListFindingTypeStatsRequest {
  // Required. The parent resource name, which should be a scan run resource name in the
  // format
  // 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "websecurityscanner.googleapis.com/ScanRun"
    }
  ];
}

// Response for the `ListFindingTypeStats` method.
message ListFindingTypeStatsResponse {
  // The list of FindingTypeStats returned.
  repeated FindingTypeStats finding_type_stats = 1;
}
