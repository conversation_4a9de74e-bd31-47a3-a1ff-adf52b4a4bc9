// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.websecurityscanner.v1alpha;

import "google/api/resource.proto";
import "google/cloud/websecurityscanner/v1alpha/finding_addon.proto";

option go_package = "cloud.google.com/go/websecurityscanner/apiv1alpha/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "FindingProto";
option java_package = "com.google.cloud.websecurityscanner.v1alpha";

// A Finding resource represents a vulnerability instance identified during a
// ScanRun.
message Finding {
  option (google.api.resource) = {
    type: "websecurityscanner.googleapis.com/Finding"
    pattern: "projects/{project}/scanConfigs/{scan_config}/scanRuns/{scan_run}/findings/{finding}"
  };

  // Types of Findings.
  enum FindingType {
    // The invalid finding type.
    FINDING_TYPE_UNSPECIFIED = 0;

    // A page that was served over HTTPS also resources over HTTP. A
    // man-in-the-middle attacker could tamper with the HTTP resource and gain
    // full access to the website that loads the resource or to monitor the
    // actions taken by the user.
    MIXED_CONTENT = 1;

    // The version of an included library is known to contain a security issue.
    // The scanner checks the version of library in use against a known list of
    // vulnerable libraries. False positives are possible if the version
    // detection fails or if the library has been manually patched.
    OUTDATED_LIBRARY = 2;

    // This type of vulnerability occurs when the value of a request parameter
    // is reflected at the beginning of the response, for example, in requests
    // using JSONP. Under certain circumstances, an attacker may be able to
    // supply an alphanumeric-only Flash file in the vulnerable parameter
    // causing the browser to execute the Flash file as if it originated on the
    // vulnerable server.
    ROSETTA_FLASH = 5;

    // A cross-site scripting (XSS) bug is found via JavaScript callback. For
    // detailed explanations on XSS, see
    // https://www.google.com/about/appsecurity/learning/xss/.
    XSS_CALLBACK = 3;

    // A potential cross-site scripting (XSS) bug due to JavaScript breakage.
    // In some circumstances, the application under test might modify the test
    // string before it is parsed by the browser. When the browser attempts to
    // runs this modified test string, it will likely break and throw a
    // JavaScript execution error, thus an injection issue is occurring.
    // However, it may not be exploitable. Manual verification is needed to see
    // if the test string modifications can be evaded and confirm that the issue
    // is in fact an XSS vulnerability. For detailed explanations on XSS, see
    // https://www.google.com/about/appsecurity/learning/xss/.
    XSS_ERROR = 4;

    // An application appears to be transmitting a password field in clear text.
    // An attacker can eavesdrop network traffic and sniff the password field.
    CLEAR_TEXT_PASSWORD = 6;

    // An application returns sensitive content with an invalid content type,
    // or without an 'X-Content-Type-Options: nosniff' header.
    INVALID_CONTENT_TYPE = 7;

    // A cross-site scripting (XSS) vulnerability in AngularJS module that
    // occurs when a user-provided string is interpolated by Angular.
    XSS_ANGULAR_CALLBACK = 8;

    // A malformed or invalid valued header.
    INVALID_HEADER = 9;

    // Misspelled security header name.
    MISSPELLED_SECURITY_HEADER_NAME = 10;

    // Mismatching values in a duplicate security header.
    MISMATCHING_SECURITY_HEADER_VALUES = 11;
  }

  // The resource name of the Finding. The name follows the format of
  // 'projects/{projectId}/scanConfigs/{scanConfigId}/scanruns/{scanRunId}/findings/{findingId}'.
  // The finding IDs are generated by the system.
  string name = 1;

  // The type of the Finding.
  FindingType finding_type = 2;

  // The http method of the request that triggered the vulnerability, in
  // uppercase.
  string http_method = 3;

  // The URL produced by the server-side fuzzer and used in the request that
  // triggered the vulnerability.
  string fuzzed_url = 4;

  // The body of the request that triggered the vulnerability.
  string body = 5;

  // The description of the vulnerability.
  string description = 6;

  // The URL containing human-readable payload that user can leverage to
  // reproduce the vulnerability.
  string reproduction_url = 7;

  // If the vulnerability was originated from nested IFrame, the immediate
  // parent IFrame is reported.
  string frame_url = 8;

  // The URL where the browser lands when the vulnerability is detected.
  string final_url = 9;

  // The tracking ID uniquely identifies a vulnerability instance across
  // multiple ScanRuns.
  string tracking_id = 10;

  // An addon containing information about outdated libraries.
  OutdatedLibrary outdated_library = 11;

  // An addon containing detailed information regarding any resource causing the
  // vulnerability such as JavaScript sources, image, audio files, etc.
  ViolatingResource violating_resource = 12;

  // An addon containing information about vulnerable or missing HTTP headers.
  VulnerableHeaders vulnerable_headers = 15;

  // An addon containing information about request parameters which were found
  // to be vulnerable.
  VulnerableParameters vulnerable_parameters = 13;

  // An addon containing information reported for an XSS, if any.
  Xss xss = 14;
}
