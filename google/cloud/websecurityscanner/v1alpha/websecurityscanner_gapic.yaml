type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  python:
    package_name: google.cloud.websecurityscanner_v1alpha.gapic
  go:
    package_name: cloud.google.com/go/cloud/websecurityscanner/apiv1alpha
  csharp:
    package_name: Google.Cloud.Websecurityscanner.V1Alpha
  ruby:
    package_name: Google::Cloud::Websecurityscanner::V1alpha
  php:
    package_name: Google\Cloud\Websecurityscanner\V1alpha
  nodejs:
    package_name: websecurityscanner.v1alpha
interfaces:
- name: google.cloud.websecurityscanner.v1alpha.WebSecurityScanner
