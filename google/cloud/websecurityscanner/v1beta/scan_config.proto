// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.websecurityscanner.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/websecurityscanner/v1beta/scan_run.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.WebSecurityScanner.V1Beta";
option go_package = "cloud.google.com/go/websecurityscanner/apiv1beta/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "ScanConfigProto";
option java_package = "com.google.cloud.websecurityscanner.v1beta";
option php_namespace = "Google\\Cloud\\WebSecurityScanner\\V1beta";
option ruby_package = "Google::Cloud::WebSecurityScanner::V1beta";

// A ScanConfig resource contains the configurations to launch a scan.
message ScanConfig {
  option (google.api.resource) = {
    type: "websecurityscanner.googleapis.com/ScanConfig"
    pattern: "projects/{project}/scanConfigs/{scan_config}"
  };

  // Scan authentication configuration.
  message Authentication {
    // Describes authentication configuration that uses a Google account.
    message GoogleAccount {
      // Required. The user name of the Google account.
      string username = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. Input only. The password of the Google account. The credential is stored encrypted
      // and not returned in any response nor included in audit logs.
      string password = 2 [
        (google.api.field_behavior) = REQUIRED,
        (google.api.field_behavior) = INPUT_ONLY
      ];
    }

    // Describes authentication configuration that uses a custom account.
    message CustomAccount {
      // Required. The user name of the custom account.
      string username = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. Input only. The password of the custom account. The credential is stored encrypted
      // and not returned in any response nor included in audit logs.
      string password = 2 [
        (google.api.field_behavior) = REQUIRED,
        (google.api.field_behavior) = INPUT_ONLY
      ];

      // Required. The login form URL of the website.
      string login_url = 3 [(google.api.field_behavior) = REQUIRED];
    }

    // Required.
    // Authentication configuration
    oneof authentication {
      // Authentication using a Google account.
      GoogleAccount google_account = 1;

      // Authentication using a custom account.
      CustomAccount custom_account = 2;
    }
  }

  // Scan schedule configuration.
  message Schedule {
    // A timestamp indicates when the next run will be scheduled. The value is
    // refreshed by the server after each run. If unspecified, it will default
    // to current server time, which means the scan will be scheduled to start
    // immediately.
    google.protobuf.Timestamp schedule_time = 1;

    // Required. The duration of time between executions in days.
    int32 interval_duration_days = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Type of user agents used for scanning.
  enum UserAgent {
    // The user agent is unknown. Service will default to CHROME_LINUX.
    USER_AGENT_UNSPECIFIED = 0;

    // Chrome on Linux. This is the service default if unspecified.
    CHROME_LINUX = 1;

    // Chrome on Android.
    CHROME_ANDROID = 2;

    // Safari on IPhone.
    SAFARI_IPHONE = 3;
  }

  // Cloud platforms supported by Cloud Web Security Scanner.
  enum TargetPlatform {
    // The target platform is unknown. Requests with this enum value will be
    // rejected with INVALID_ARGUMENT error.
    TARGET_PLATFORM_UNSPECIFIED = 0;

    // Google App Engine service.
    APP_ENGINE = 1;

    // Google Compute Engine service.
    COMPUTE = 2;
  }

  // Scan risk levels supported by Cloud Web Security Scanner. LOW impact
  // scanning will minimize requests with the potential to modify data. To
  // achieve the maximum scan coverage, NORMAL risk level is recommended.
  enum RiskLevel {
    // Use default, which is NORMAL.
    RISK_LEVEL_UNSPECIFIED = 0;

    // Normal scanning (Recommended)
    NORMAL = 1;

    // Lower impact scanning
    LOW = 2;
  }

  // Controls export of scan configurations and results to Cloud Security
  // Command Center.
  enum ExportToSecurityCommandCenter {
    // Use default, which is ENABLED.
    EXPORT_TO_SECURITY_COMMAND_CENTER_UNSPECIFIED = 0;

    // Export results of this scan to Cloud Security Command Center.
    ENABLED = 1;

    // Do not export results of this scan to Cloud Security Command Center.
    DISABLED = 2;
  }

  // The resource name of the ScanConfig. The name follows the format of
  // 'projects/{projectId}/scanConfigs/{scanConfigId}'. The ScanConfig IDs are
  // generated by the system.
  string name = 1;

  // Required. The user provided display name of the ScanConfig.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // The maximum QPS during scanning. A valid value ranges from 5 to 20
  // inclusively. If the field is unspecified or its value is set 0, server will
  // default to 15. Other values outside of [5, 20] range will be rejected with
  // INVALID_ARGUMENT error.
  int32 max_qps = 3;

  // Required. The starting URLs from which the scanner finds site pages.
  repeated string starting_urls = 4 [(google.api.field_behavior) = REQUIRED];

  // The authentication configuration. If specified, service will use the
  // authentication configuration during scanning.
  Authentication authentication = 5;

  // The user agent used during scanning.
  UserAgent user_agent = 6;

  // The blacklist URL patterns as described in
  // https://cloud.google.com/security-scanner/docs/excluded-urls
  repeated string blacklist_patterns = 7;

  // The schedule of the ScanConfig.
  Schedule schedule = 8;

  // Set of Cloud Platforms targeted by the scan. If empty, APP_ENGINE will be
  // used as a default.
  repeated TargetPlatform target_platforms = 9;

  // Controls export of scan configurations and results to Cloud Security
  // Command Center.
  ExportToSecurityCommandCenter export_to_security_command_center = 10;

  // Latest ScanRun if available.
  ScanRun latest_run = 11;

  // The risk level selected for the scan
  RiskLevel risk_level = 12;
}
