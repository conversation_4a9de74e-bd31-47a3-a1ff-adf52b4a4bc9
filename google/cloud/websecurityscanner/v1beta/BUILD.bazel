# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "websecurityscanner_proto",
    srcs = [
        "crawled_url.proto",
        "finding.proto",
        "finding_addon.proto",
        "finding_type_stats.proto",
        "scan_config.proto",
        "scan_config_error.proto",
        "scan_run.proto",
        "scan_run_error_trace.proto",
        "scan_run_warning_trace.proto",
        "web_security_scanner.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "websecurityscanner_proto_with_info",
    deps = [
        ":websecurityscanner_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "websecurityscanner_java_proto",
    deps = [":websecurityscanner_proto"],
)

java_grpc_library(
    name = "websecurityscanner_java_grpc",
    srcs = [":websecurityscanner_proto"],
    deps = [":websecurityscanner_java_proto"],
)

java_gapic_library(
    name = "websecurityscanner_java_gapic",
    srcs = [":websecurityscanner_proto_with_info"],
    grpc_service_config = "websecurityscanner_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "websecurityscanner_v1beta.yaml",
    test_deps = [
        ":websecurityscanner_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":websecurityscanner_java_proto",
    ],
)

java_gapic_test(
    name = "websecurityscanner_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.websecurityscanner.v1beta.WebSecurityScannerClientHttpJsonTest",
        "com.google.cloud.websecurityscanner.v1beta.WebSecurityScannerClientTest",
    ],
    runtime_deps = [":websecurityscanner_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-websecurityscanner-v1beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":websecurityscanner_java_gapic",
        ":websecurityscanner_java_grpc",
        ":websecurityscanner_java_proto",
        ":websecurityscanner_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "websecurityscanner_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/websecurityscanner/apiv1beta/websecurityscannerpb",
    protos = [":websecurityscanner_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "websecurityscanner_go_gapic",
    srcs = [":websecurityscanner_proto_with_info"],
    grpc_service_config = "websecurityscanner_grpc_service_config.json",
    importpath = "cloud.google.com/go/websecurityscanner/apiv1beta;websecurityscanner",
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "websecurityscanner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":websecurityscanner_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-websecurityscanner-v1beta-go",
    deps = [
        ":websecurityscanner_go_gapic",
        ":websecurityscanner_go_gapic_srcjar-snippets.srcjar",
        ":websecurityscanner_go_gapic_srcjar-test.srcjar",
        ":websecurityscanner_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "websecurityscanner_py_gapic",
    srcs = [":websecurityscanner_proto"],
    grpc_service_config = "websecurityscanner_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "websecurityscanner_v1beta.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "websecurityscanner_py_gapic_test",
    srcs = [
        "websecurityscanner_py_gapic_pytest.py",
        "websecurityscanner_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":websecurityscanner_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "websecurityscanner-v1beta-py",
    deps = [
        ":websecurityscanner_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "websecurityscanner_php_proto",
    deps = [":websecurityscanner_proto"],
)

php_gapic_library(
    name = "websecurityscanner_php_gapic",
    srcs = [":websecurityscanner_proto_with_info"],
    grpc_service_config = "websecurityscanner_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "websecurityscanner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [":websecurityscanner_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-websecurityscanner-v1beta-php",
    deps = [
        ":websecurityscanner_php_gapic",
        ":websecurityscanner_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "websecurityscanner_nodejs_gapic",
    package_name = "@google-cloud/web-security-scanner",
    src = ":websecurityscanner_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "websecurityscanner_grpc_service_config.json",
    package = "google.cloud.websecurityscanner.v1beta",
    rest_numeric_enums = True,
    service_yaml = "websecurityscanner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "websecurityscanner-v1beta-nodejs",
    deps = [
        ":websecurityscanner_nodejs_gapic",
        ":websecurityscanner_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "websecurityscanner_ruby_proto",
    deps = [":websecurityscanner_proto"],
)

ruby_grpc_library(
    name = "websecurityscanner_ruby_grpc",
    srcs = [":websecurityscanner_proto"],
    deps = [":websecurityscanner_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "websecurityscanner_ruby_gapic",
    srcs = [":websecurityscanner_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-web_security_scanner-v1beta",
        "ruby-cloud-env-prefix=WEB_SECURITY_SCANNER",
        "ruby-cloud-product-url=https://cloud.google.com/security-command-center/docs/concepts-web-security-scanner-overview/",
        "ruby-cloud-api-id=websecurityscanner.googleapis.com",
        "ruby-cloud-api-shortname=websecurityscanner",
    ],
    grpc_service_config = "websecurityscanner_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Web Security Scanner scans your Compute and App Engine apps for common web vulnerabilities.",
    ruby_cloud_title = "Web Security Scanner V1beta",
    service_yaml = "websecurityscanner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":websecurityscanner_ruby_grpc",
        ":websecurityscanner_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-websecurityscanner-v1beta-ruby",
    deps = [
        ":websecurityscanner_ruby_gapic",
        ":websecurityscanner_ruby_grpc",
        ":websecurityscanner_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "websecurityscanner_csharp_proto",
    deps = [":websecurityscanner_proto"],
)

csharp_grpc_library(
    name = "websecurityscanner_csharp_grpc",
    srcs = [":websecurityscanner_proto"],
    deps = [":websecurityscanner_csharp_proto"],
)

csharp_gapic_library(
    name = "websecurityscanner_csharp_gapic",
    srcs = [":websecurityscanner_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "websecurityscanner_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "websecurityscanner_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":websecurityscanner_csharp_grpc",
        ":websecurityscanner_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-websecurityscanner-v1beta-csharp",
    deps = [
        ":websecurityscanner_csharp_gapic",
        ":websecurityscanner_csharp_grpc",
        ":websecurityscanner_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
