// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.websecurityscanner.v1beta;

import "google/api/resource.proto";
import "google/cloud/websecurityscanner/v1beta/finding_addon.proto";

option csharp_namespace = "Google.Cloud.WebSecurityScanner.V1Beta";
option go_package = "cloud.google.com/go/websecurityscanner/apiv1beta/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "FindingProto";
option java_package = "com.google.cloud.websecurityscanner.v1beta";
option php_namespace = "Google\\Cloud\\WebSecurityScanner\\V1beta";
option ruby_package = "Google::Cloud::WebSecurityScanner::V1beta";

// A Finding resource represents a vulnerability instance identified during a
// ScanRun.
message Finding {
  option (google.api.resource) = {
    type: "websecurityscanner.googleapis.com/Finding"
    pattern: "projects/{project}/scanConfigs/{scan_config}/scanRuns/{scan_run}/findings/{finding}"
  };

  // The resource name of the Finding. The name follows the format of
  // 'projects/{projectId}/scanConfigs/{scanConfigId}/scanruns/{scanRunId}/findings/{findingId}'.
  // The finding IDs are generated by the system.
  string name = 1;

  // The type of the Finding.
  // Detailed and up-to-date information on findings can be found here:
  // https://cloud.google.com/security-scanner/docs/scan-result-details
  string finding_type = 2;

  // The http method of the request that triggered the vulnerability, in
  // uppercase.
  string http_method = 3;

  // The URL produced by the server-side fuzzer and used in the request that
  // triggered the vulnerability.
  string fuzzed_url = 4;

  // The body of the request that triggered the vulnerability.
  string body = 5;

  // The description of the vulnerability.
  string description = 6;

  // The URL containing human-readable payload that user can leverage to
  // reproduce the vulnerability.
  string reproduction_url = 7;

  // If the vulnerability was originated from nested IFrame, the immediate
  // parent IFrame is reported.
  string frame_url = 8;

  // The URL where the browser lands when the vulnerability is detected.
  string final_url = 9;

  // The tracking ID uniquely identifies a vulnerability instance across
  // multiple ScanRuns.
  string tracking_id = 10;

  // An addon containing information reported for a vulnerability with an HTML
  // form, if any.
  Form form = 16;

  // An addon containing information about outdated libraries.
  OutdatedLibrary outdated_library = 11;

  // An addon containing detailed information regarding any resource causing the
  // vulnerability such as JavaScript sources, image, audio files, etc.
  ViolatingResource violating_resource = 12;

  // An addon containing information about vulnerable or missing HTTP headers.
  VulnerableHeaders vulnerable_headers = 15;

  // An addon containing information about request parameters which were found
  // to be vulnerable.
  VulnerableParameters vulnerable_parameters = 13;

  // An addon containing information reported for an XSS, if any.
  Xss xss = 14;
}
