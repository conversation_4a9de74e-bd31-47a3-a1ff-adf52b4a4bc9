// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.websecurityscanner.v1beta;

option csharp_namespace = "Google.Cloud.WebSecurityScanner.V1Beta";
option go_package = "cloud.google.com/go/websecurityscanner/apiv1beta/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "FindingAddonProto";
option java_package = "com.google.cloud.websecurityscanner.v1beta";
option php_namespace = "Google\\Cloud\\WebSecurityScanner\\V1beta";
option ruby_package = "Google::Cloud::WebSecurityScanner::V1beta";

// ! Information about a vulnerability with an HTML.
message Form {
  // ! The URI where to send the form when it's submitted.
  string action_uri = 1;

  // ! The names of form fields related to the vulnerability.
  repeated string fields = 2;
}

// Information reported for an outdated library.
message OutdatedLibrary {
  // The name of the outdated library.
  string library_name = 1;

  // The version number.
  string version = 2;

  // URLs to learn more information about the vulnerabilities in the library.
  repeated string learn_more_urls = 3;
}

// Information regarding any resource causing the vulnerability such
// as JavaScript sources, image, audio files, etc.
message ViolatingResource {
  // The MIME type of this resource.
  string content_type = 1;

  // URL of this violating resource.
  string resource_url = 2;
}

// Information about vulnerable request parameters.
message VulnerableParameters {
  // The vulnerable parameter names.
  repeated string parameter_names = 1;
}

// Information about vulnerable or missing HTTP Headers.
message VulnerableHeaders {
  // Describes a HTTP Header.
  message Header {
    // Header name.
    string name = 1;

    // Header value.
    string value = 2;
  }

  // List of vulnerable headers.
  repeated Header headers = 1;

  // List of missing headers.
  repeated Header missing_headers = 2;
}

// Information reported for an XSS.
message Xss {
  // Stack traces leading to the point where the XSS occurred.
  repeated string stack_traces = 1;

  // An error message generated by a javascript breakage.
  string error_message = 2;
}
