// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.websecurityscanner.v1beta;

option csharp_namespace = "Google.Cloud.WebSecurityScanner.V1Beta";
option go_package = "cloud.google.com/go/websecurityscanner/apiv1beta/websecurityscannerpb;websecurityscannerpb";
option java_multiple_files = true;
option java_outer_classname = "ScanConfigErrorProto";
option java_package = "com.google.cloud.websecurityscanner.v1beta";
option php_namespace = "Google\\Cloud\\WebSecurityScanner\\V1beta";
option ruby_package = "Google::Cloud::WebSecurityScanner::V1beta";

// Defines a custom error message used by CreateScanConfig and UpdateScanConfig
// APIs when scan configuration validation fails. It is also reported as part of
// a ScanRunErrorTrace message if scan validation fails due to a scan
// configuration error.
message ScanConfigError {
  // Output only.
  // Defines an error reason code.
  // Next id: 44
  enum Code {
    option allow_alias = true;

    // There is no error.
    CODE_UNSPECIFIED = 0;

    // There is no error.
    OK = 0;

    // Indicates an internal server error.
    // Please DO NOT USE THIS ERROR CODE unless the root cause is truly unknown.
    INTERNAL_ERROR = 1;

    // One of the seed URLs is an App Engine URL but we cannot validate the scan
    // settings due to an App Engine API backend error.
    APPENGINE_API_BACKEND_ERROR = 2;

    // One of the seed URLs is an App Engine URL but we cannot access the
    // App Engine API to validate scan settings.
    APPENGINE_API_NOT_ACCESSIBLE = 3;

    // One of the seed URLs is an App Engine URL but the Default Host of the
    // App Engine is not set.
    APPENGINE_DEFAULT_HOST_MISSING = 4;

    // Google corporate accounts can not be used for scanning.
    CANNOT_USE_GOOGLE_COM_ACCOUNT = 6;

    // The account of the scan creator can not be used for scanning.
    CANNOT_USE_OWNER_ACCOUNT = 7;

    // This scan targets Compute Engine, but we cannot validate scan settings
    // due to a Compute Engine API backend error.
    COMPUTE_API_BACKEND_ERROR = 8;

    // This scan targets Compute Engine, but we cannot access the Compute Engine
    // API to validate the scan settings.
    COMPUTE_API_NOT_ACCESSIBLE = 9;

    // The Custom Login URL does not belong to the current project.
    CUSTOM_LOGIN_URL_DOES_NOT_BELONG_TO_CURRENT_PROJECT = 10;

    // The Custom Login URL is malformed (can not be parsed).
    CUSTOM_LOGIN_URL_MALFORMED = 11;

    // The Custom Login URL is mapped to a non-routable IP address in DNS.
    CUSTOM_LOGIN_URL_MAPPED_TO_NON_ROUTABLE_ADDRESS = 12;

    // The Custom Login URL is mapped to an IP address which is not reserved for
    // the current project.
    CUSTOM_LOGIN_URL_MAPPED_TO_UNRESERVED_ADDRESS = 13;

    // The Custom Login URL has a non-routable IP address.
    CUSTOM_LOGIN_URL_HAS_NON_ROUTABLE_IP_ADDRESS = 14;

    // The Custom Login URL has an IP address which is not reserved for the
    // current project.
    CUSTOM_LOGIN_URL_HAS_UNRESERVED_IP_ADDRESS = 15;

    // Another scan with the same name (case-sensitive) already exists.
    DUPLICATE_SCAN_NAME = 16;

    // A field is set to an invalid value.
    INVALID_FIELD_VALUE = 18;

    // There was an error trying to authenticate to the scan target.
    FAILED_TO_AUTHENTICATE_TO_TARGET = 19;

    // Finding type value is not specified in the list findings request.
    FINDING_TYPE_UNSPECIFIED = 20;

    // Scan targets Compute Engine, yet current project was not whitelisted for
    // Google Compute Engine Scanning Alpha access.
    FORBIDDEN_TO_SCAN_COMPUTE = 21;

    // User tries to update managed scan
    FORBIDDEN_UPDATE_TO_MANAGED_SCAN = 43;

    // The supplied filter is malformed. For example, it can not be parsed, does
    // not have a filter type in expression, or the same filter type appears
    // more than once.
    MALFORMED_FILTER = 22;

    // The supplied resource name is malformed (can not be parsed).
    MALFORMED_RESOURCE_NAME = 23;

    // The current project is not in an active state.
    PROJECT_INACTIVE = 24;

    // A required field is not set.
    REQUIRED_FIELD = 25;

    // Project id, scanconfig id, scanrun id, or finding id are not consistent
    // with each other in resource name.
    RESOURCE_NAME_INCONSISTENT = 26;

    // The scan being requested to start is already running.
    SCAN_ALREADY_RUNNING = 27;

    // The scan that was requested to be stopped is not running.
    SCAN_NOT_RUNNING = 28;

    // One of the seed URLs does not belong to the current project.
    SEED_URL_DOES_NOT_BELONG_TO_CURRENT_PROJECT = 29;

    // One of the seed URLs is malformed (can not be parsed).
    SEED_URL_MALFORMED = 30;

    // One of the seed URLs is mapped to a non-routable IP address in DNS.
    SEED_URL_MAPPED_TO_NON_ROUTABLE_ADDRESS = 31;

    // One of the seed URLs is mapped to an IP address which is not reserved
    // for the current project.
    SEED_URL_MAPPED_TO_UNRESERVED_ADDRESS = 32;

    // One of the seed URLs has on-routable IP address.
    SEED_URL_HAS_NON_ROUTABLE_IP_ADDRESS = 33;

    // One of the seed URLs has an IP address that is not reserved
    // for the current project.
    SEED_URL_HAS_UNRESERVED_IP_ADDRESS = 35;

    // The Cloud Security Scanner service account is not configured under the
    // project.
    SERVICE_ACCOUNT_NOT_CONFIGURED = 36;

    // A project has reached the maximum number of scans.
    TOO_MANY_SCANS = 37;

    // Resolving the details of the current project fails.
    UNABLE_TO_RESOLVE_PROJECT_INFO = 38;

    // One or more blacklist patterns were in the wrong format.
    UNSUPPORTED_BLACKLIST_PATTERN_FORMAT = 39;

    // The supplied filter is not supported.
    UNSUPPORTED_FILTER = 40;

    // The supplied finding type is not supported. For example, we do not
    // provide findings of the given finding type.
    UNSUPPORTED_FINDING_TYPE = 41;

    // The URL scheme of one or more of the supplied URLs is not supported.
    UNSUPPORTED_URL_SCHEME = 42;
  }

  // Indicates the reason code for a configuration failure.
  Code code = 1;

  // Indicates the full name of the ScanConfig field that triggers this error,
  // for example "scan_config.max_qps". This field is provided for
  // troubleshooting purposes only and its actual value can change in the
  // future.
  string field_name = 2;
}
