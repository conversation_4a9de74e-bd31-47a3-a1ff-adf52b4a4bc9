# This build file includes a target for the Ruby wrapper library for
# google-cloud-vpc_access.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for vpcaccess.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "vpcaccess_ruby_wrapper",
    srcs = ["//google/cloud/vpcaccess/v1:vpcaccess_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-vpc_access",
        "ruby-cloud-env-prefix=VPC_ACCESS",
        "ruby-cloud-wrapper-of=v1:0.7",
        "ruby-cloud-product-url=https://cloud.google.com/vpc/docs/serverless-vpc-access",
        "ruby-cloud-api-id=vpcaccess.googleapis.com",
        "ruby-cloud-api-shortname=vpcaccess",
    ],
    ruby_cloud_description = "Serverless VPC Access enables you to connect from a serverless environment on Google Cloud (Cloud Run, Cloud Functions, or the App Engine standard environment) directly to your VPC network. This connection makes it possible for your serverless environment to access Compute Engine VM instances, Memorystore instances, and any other resources with an internal IP address.",
    ruby_cloud_title = "Serverless VPC Access",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-vpcaccess-ruby",
    deps = [
        ":vpcaccess_ruby_wrapper",
    ],
)
