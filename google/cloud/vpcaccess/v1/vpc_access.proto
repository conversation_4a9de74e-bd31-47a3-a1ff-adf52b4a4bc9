// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.vpcaccess.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.VpcAccess.V1";
option go_package = "cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb;vpcaccesspb";
option java_multiple_files = true;
option java_outer_classname = "VpcAccessProto";
option java_package = "com.google.cloud.vpcaccess.v1";
option php_namespace = "Google\\Cloud\\VpcAccess\\V1";
option ruby_package = "Google::Cloud::VpcAccess::V1";

// Serverless VPC Access API allows users to create and manage connectors for
// App Engine, Cloud Functions and Cloud Run to have internal connections to
// Virtual Private Cloud networks.
service VpcAccessService {
  option (google.api.default_host) = "vpcaccess.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Creates a Serverless VPC Access connector, returns an operation.
  rpc CreateConnector(CreateConnectorRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/connectors"
      body: "connector"
    };
    option (google.api.method_signature) = "parent,connector_id,connector";
    option (google.longrunning.operation_info) = {
      response_type: "Connector"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets a Serverless VPC Access connector. Returns NOT_FOUND if the resource
  // does not exist.
  rpc GetConnector(GetConnectorRequest) returns (Connector) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/connectors/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Serverless VPC Access connectors.
  rpc ListConnectors(ListConnectorsRequest) returns (ListConnectorsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/connectors"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a Serverless VPC Access connector. Returns NOT_FOUND if the
  // resource does not exist.
  rpc DeleteConnector(DeleteConnectorRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/connectors/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Definition of a Serverless VPC Access connector.
message Connector {
  option (google.api.resource) = {
    type: "vpcaccess.googleapis.com/Connector"
    pattern: "projects/{project}/locations/{location}/connectors/{connector}"
  };

  // State of a connector.
  enum State {
    // Invalid state.
    STATE_UNSPECIFIED = 0;

    // Connector is deployed and ready to receive traffic.
    READY = 1;

    // An Insert operation is in progress. Transient condition.
    CREATING = 2;

    // A Delete operation is in progress. Transient condition.
    DELETING = 3;

    // Connector is in a bad state, manual deletion recommended.
    ERROR = 4;

    // The connector is being updated.
    UPDATING = 5;
  }

  // The subnet in which to house the connector
  message Subnet {
    // Subnet name (relative, not fully qualified).
    // E.g. if the full subnet selfLink is
    // https://compute.googleapis.com/compute/v1/projects/{project}/regions/{region}/subnetworks/{subnetName}
    // the correct input for this field would be {subnetName}
    string name = 1;

    // Project in which the subnet exists.
    // If not set, this project is assumed to be the project for which
    // the connector create request was issued.
    string project_id = 2;
  }

  // The resource name in the format `projects/*/locations/*/connectors/*`.
  string name = 1;

  // Name of a VPC network.
  string network = 2;

  // The range of internal addresses that follows RFC 4632 notation.
  // Example: `**********/28`.
  string ip_cidr_range = 3;

  // Output only. State of the VPC access connector.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Minimum throughput of the connector in Mbps. Default and min is 200.
  int32 min_throughput = 5;

  // Maximum throughput of the connector in Mbps. Default is 300, max is 1000.
  int32 max_throughput = 6;

  // Output only. List of projects using the connector.
  repeated string connected_projects = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The subnet in which to house the VPC Access Connector.
  Subnet subnet = 8;

  // Machine type of VM Instance underlying connector. Default is e2-micro
  string machine_type = 10;

  // Minimum value of instances in autoscaling group underlying the connector.
  int32 min_instances = 11;

  // Maximum value of instances in autoscaling group underlying the connector.
  int32 max_instances = 12;
}

// Request for creating a Serverless VPC Access connector.
message CreateConnectorRequest {
  // Required. The project and location in which the configuration should be created,
  // specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The ID to use for this connector.
  string connector_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Resource to create.
  Connector connector = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for getting a Serverless VPC Access connector.
message GetConnectorRequest {
  // Required. Name of a Serverless VPC Access connector to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vpcaccess.googleapis.com/Connector"
    }
  ];
}

// Request for listing Serverless VPC Access connectors in a location.
message ListConnectorsRequest {
  // Required. The project and location from which the routes should be listed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of functions to return per call.
  int32 page_size = 2;

  // Continuation token.
  string page_token = 3;
}

// Response for listing Serverless VPC Access connectors.
message ListConnectorsResponse {
  // List of Serverless VPC Access connectors.
  repeated Connector connectors = 1;

  // Continuation token.
  string next_page_token = 2;
}

// Request for deleting a Serverless VPC Access connector.
message DeleteConnectorRequest {
  // Required. Name of a Serverless VPC Access connector to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vpcaccess.googleapis.com/Connector"
    }
  ];
}

// Metadata for google.longrunning.Operation.
message OperationMetadata {
  // Output only. Method that initiated the operation e.g.
  // google.cloud.vpcaccess.v1.Connectors.CreateConnector.
  string method = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the operation was created.
  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the operation completed.
  google.protobuf.Timestamp end_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the resource that this operation is acting on e.g.
  // projects/my-project/locations/us-central1/connectors/v1.
  string target = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "vpcaccess.googleapis.com/Connector"
    }
  ];
}
