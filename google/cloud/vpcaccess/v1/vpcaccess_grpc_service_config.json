{"methodConfig": [{"name": [{"service": "google.cloud.vpcaccess.v1.VpcAccessService"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "UNKNOWN"]}}, {"name": [{"service": "google.cloud.vpcaccess.v1.VpcAccessService", "method": "CreateConnector"}, {"service": "google.cloud.vpcaccess.v1.VpcAccessService", "method": "GetConnector"}, {"service": "google.cloud.vpcaccess.v1.VpcAccessService", "method": "ListConnectors"}, {"service": "google.cloud.vpcaccess.v1.VpcAccessService", "method": "DeleteConnector"}], "timeout": "60s"}]}