type: google.api.Service
config_version: 3
name: vpcaccess.googleapis.com
title: Serverless VPC Access API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.vpcaccess.v1.VpcAccessService
- name: google.longrunning.Operations

types:
- name: google.cloud.vpcaccess.v1.OperationMetadata

documentation:
  summary: API for managing VPC access connectors.
  rules:
  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 30.0
  - selector: google.cloud.vpcaccess.v1.VpcAccessService.GetConnector
    deadline: 30.0
  - selector: google.cloud.vpcaccess.v1.VpcAccessService.ListConnectors
    deadline: 30.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 30.0

http:
  rules:
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.vpcaccess.v1.VpcAccessService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
