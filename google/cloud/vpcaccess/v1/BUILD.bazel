# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "vpcaccess_proto",
    srcs = [
        "vpc_access.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "vpcaccess_proto_with_info",
    deps = [
        ":vpcaccess_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "vpcaccess_java_proto",
    deps = [":vpcaccess_proto"],
)

java_grpc_library(
    name = "vpcaccess_java_grpc",
    srcs = [":vpcaccess_proto"],
    deps = [":vpcaccess_java_proto"],
)

java_gapic_library(
    name = "vpcaccess_java_gapic",
    srcs = [":vpcaccess_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "vpcaccess_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "vpcaccess_v1.yaml",
    test_deps = [
        ":vpcaccess_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":vpcaccess_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "vpcaccess_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.vpcaccess.v1.VpcAccessServiceClientHttpJsonTest",
        "com.google.cloud.vpcaccess.v1.VpcAccessServiceClientTest",
    ],
    runtime_deps = [":vpcaccess_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-vpcaccess-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":vpcaccess_java_gapic",
        ":vpcaccess_java_grpc",
        ":vpcaccess_java_proto",
        ":vpcaccess_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "vpcaccess_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb",
    protos = [":vpcaccess_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "vpcaccess_go_gapic",
    srcs = [":vpcaccess_proto_with_info"],
    grpc_service_config = "vpcaccess_grpc_service_config.json",
    importpath = "cloud.google.com/go/vpcaccess/apiv1;vpcaccess",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "vpcaccess_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":vpcaccess_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-vpcaccess-v1-go",
    deps = [
        ":vpcaccess_go_gapic",
        ":vpcaccess_go_gapic_srcjar-metadata.srcjar",
        ":vpcaccess_go_gapic_srcjar-snippets.srcjar",
        ":vpcaccess_go_gapic_srcjar-test.srcjar",
        ":vpcaccess_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "vpcaccess_py_gapic",
    srcs = [":vpcaccess_proto"],
    grpc_service_config = "vpcaccess_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-vpc-access"],
    rest_numeric_enums = True,
    service_yaml = "vpcaccess_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "vpcaccess_py_gapic_test",
    srcs = [
        "vpcaccess_py_gapic_pytest.py",
        "vpcaccess_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":vpcaccess_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "vpcaccess-v1-py",
    deps = [
        ":vpcaccess_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "vpcaccess_php_proto",
    deps = [":vpcaccess_proto"],
)

php_gapic_library(
    name = "vpcaccess_php_gapic",
    srcs = [":vpcaccess_proto_with_info"],
    grpc_service_config = "vpcaccess_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "vpcaccess_v1.yaml",
    transport = "grpc+rest",
    deps = [":vpcaccess_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-vpcaccess-v1-php",
    deps = [
        ":vpcaccess_php_gapic",
        ":vpcaccess_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "vpcaccess_nodejs_gapic",
    package_name = "@google-cloud/vpc-access",
    src = ":vpcaccess_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "vpcaccess_grpc_service_config.json",
    package = "google.cloud.vpcaccess.v1",
    rest_numeric_enums = True,
    service_yaml = "vpcaccess_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "vpcaccess-v1-nodejs",
    deps = [
        ":vpcaccess_nodejs_gapic",
        ":vpcaccess_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "vpcaccess_ruby_proto",
    deps = [":vpcaccess_proto"],
)

ruby_grpc_library(
    name = "vpcaccess_ruby_grpc",
    srcs = [":vpcaccess_proto"],
    deps = [":vpcaccess_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "vpcaccess_ruby_gapic",
    srcs = [":vpcaccess_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=vpcaccess.googleapis.com",
        "ruby-cloud-api-shortname=vpcaccess",
        "ruby-cloud-env-prefix=VPC_ACCESS",
        "ruby-cloud-gem-name=google-cloud-vpc_access-v1",
        "ruby-cloud-product-url=https://cloud.google.com/vpc/docs/serverless-vpc-access",
    ],
    grpc_service_config = "vpcaccess_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Serverless VPC Access enables you to connect from a serverless environment on Google Cloud (Cloud Run, Cloud Functions, or the App Engine standard environment) directly to your VPC network. This connection makes it possible for your serverless environment to access Compute Engine VM instances, Memorystore instances, and any other resources with an internal IP address.",
    ruby_cloud_title = "Serverless VPC Access V1",
    service_yaml = "vpcaccess_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":vpcaccess_ruby_grpc",
        ":vpcaccess_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-vpcaccess-v1-ruby",
    deps = [
        ":vpcaccess_ruby_gapic",
        ":vpcaccess_ruby_grpc",
        ":vpcaccess_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "vpcaccess_csharp_proto",
    deps = [":vpcaccess_proto"],
)

csharp_grpc_library(
    name = "vpcaccess_csharp_grpc",
    srcs = [":vpcaccess_proto"],
    deps = [":vpcaccess_csharp_proto"],
)

csharp_gapic_library(
    name = "vpcaccess_csharp_gapic",
    srcs = [":vpcaccess_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "vpcaccess_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "vpcaccess_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":vpcaccess_csharp_grpc",
        ":vpcaccess_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-vpcaccess-v1-csharp",
    deps = [
        ":vpcaccess_csharp_gapic",
        ":vpcaccess_csharp_grpc",
        ":vpcaccess_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "vpcaccess_cc_proto",
    deps = [":vpcaccess_proto"],
)

cc_grpc_library(
    name = "vpcaccess_cc_grpc",
    srcs = [":vpcaccess_proto"],
    grpc_only = True,
    deps = [":vpcaccess_cc_proto"],
)
