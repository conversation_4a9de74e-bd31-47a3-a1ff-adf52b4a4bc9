// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.workstations.logging.v1;

option go_package = "cloud.google.com/go/workstations/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "PlatformLogsProto";
option java_package = "com.google.cloud.workstations.logging.v1";

// JSON payload for the Cloud Logging event:
// `workstations.googleapis.com%2Fworkstation_events`
message WorkstationEvent {
  oneof event_type {
    // Vm assignment event.
    VmAssignmentEvent vm_assignment_event = 1;

    // Disk assignment event.
    DiskAssignmentEvent disk_assignment_event = 2;
  }
}

// Vm assignment event.
message VmAssignmentEvent {
  // Name of the VM assigned to this workstation.
  string vm = 1;
}

// Disk assignment event.
message DiskAssignmentEvent {
  // Name of the disk assigned to this workstation.
  string disk = 1;
}
