# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "workstations_proto",
    srcs = [
        "workstations.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "workstations_proto_with_info",
    deps = [
        ":workstations_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "workstations_java_proto",
    deps = [":workstations_proto"],
)

java_grpc_library(
    name = "workstations_java_grpc",
    srcs = [":workstations_proto"],
    deps = [":workstations_java_proto"],
)

java_gapic_library(
    name = "workstations_java_gapic",
    srcs = [":workstations_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "workstations_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "workstations_v1.yaml",
    test_deps = [
        ":workstations_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":workstations_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "workstations_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.workstations.v1.WorkstationsClientHttpJsonTest",
        "com.google.cloud.workstations.v1.WorkstationsClientTest",
    ],
    runtime_deps = [":workstations_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-workstations-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":workstations_java_gapic",
        ":workstations_java_grpc",
        ":workstations_java_proto",
        ":workstations_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "workstations_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/workstations/apiv1/workstationspb",
    protos = [":workstations_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "workstations_go_gapic",
    srcs = [":workstations_proto_with_info"],
    grpc_service_config = "workstations_grpc_service_config.json",
    importpath = "cloud.google.com/go/workstations/apiv1;workstations",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "workstations_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":workstations_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-workstations-v1-go",
    deps = [
        ":workstations_go_gapic",
        ":workstations_go_gapic_srcjar-metadata.srcjar",
        ":workstations_go_gapic_srcjar-snippets.srcjar",
        ":workstations_go_gapic_srcjar-test.srcjar",
        ":workstations_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "workstations_py_gapic",
    srcs = [":workstations_proto"],
    grpc_service_config = "workstations_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "workstations_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "workstations_py_gapic_test",
    srcs = [
        "workstations_py_gapic_pytest.py",
        "workstations_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":workstations_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "workstations-v1-py",
    deps = [
        ":workstations_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "workstations_php_proto",
    deps = [":workstations_proto"],
)

php_gapic_library(
    name = "workstations_php_gapic",
    srcs = [":workstations_proto_with_info"],
    grpc_service_config = "workstations_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "workstations_v1.yaml",
    transport = "grpc+rest",
    deps = [":workstations_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-workstations-v1-php",
    deps = [
        ":workstations_php_gapic",
        ":workstations_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "workstations_nodejs_gapic",
    package_name = "@google-cloud/workstations",
    src = ":workstations_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "workstations_grpc_service_config.json",
    package = "google.cloud.workstations.v1",
    rest_numeric_enums = True,
    service_yaml = "workstations_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "workstations-v1-nodejs",
    deps = [
        ":workstations_nodejs_gapic",
        ":workstations_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "workstations_ruby_proto",
    deps = [":workstations_proto"],
)

ruby_grpc_library(
    name = "workstations_ruby_grpc",
    srcs = [":workstations_proto"],
    deps = [":workstations_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "workstations_ruby_gapic",
    srcs = [":workstations_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-workstations-v1",
    ],
    grpc_service_config = "workstations_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "workstations_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":workstations_ruby_grpc",
        ":workstations_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-workstations-v1-ruby",
    deps = [
        ":workstations_ruby_gapic",
        ":workstations_ruby_grpc",
        ":workstations_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "workstations_csharp_proto",
    deps = [":workstations_proto"],
)

csharp_grpc_library(
    name = "workstations_csharp_grpc",
    srcs = [":workstations_proto"],
    deps = [":workstations_csharp_proto"],
)

csharp_gapic_library(
    name = "workstations_csharp_gapic",
    srcs = [":workstations_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "workstations_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "workstations_v1.yaml",
    deps = [
        ":workstations_csharp_grpc",
        ":workstations_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-workstations-v1-csharp",
    deps = [
        ":workstations_csharp_gapic",
        ":workstations_csharp_grpc",
        ":workstations_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "workstations_cc_proto",
    deps = [":workstations_proto"],
)

cc_grpc_library(
    name = "workstations_cc_grpc",
    srcs = [":workstations_proto"],
    grpc_only = True,
    deps = [":workstations_cc_proto"],
)
