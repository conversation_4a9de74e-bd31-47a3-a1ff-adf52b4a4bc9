// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.workstations.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/workstations/apiv1beta/workstationspb;workstationspb";
option java_multiple_files = true;
option java_outer_classname = "WorkstationsProto";
option java_package = "com.google.cloud.workstations.v1beta";

// Service for interacting with Cloud Workstations.
service Workstations {
  option (google.api.default_host) = "workstations.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Returns the requested workstation cluster.
  rpc GetWorkstationCluster(GetWorkstationClusterRequest)
      returns (WorkstationCluster) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/workstationClusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns all workstation clusters in the specified location.
  rpc ListWorkstationClusters(ListWorkstationClustersRequest)
      returns (ListWorkstationClustersResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*}/workstationClusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new workstation cluster.
  rpc CreateWorkstationCluster(CreateWorkstationClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*}/workstationClusters"
      body: "workstation_cluster"
    };
    option (google.api.method_signature) =
        "parent,workstation_cluster,workstation_cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "WorkstationCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an existing workstation cluster.
  rpc UpdateWorkstationCluster(UpdateWorkstationClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{workstation_cluster.name=projects/*/locations/*/workstationClusters/*}"
      body: "workstation_cluster"
    };
    option (google.api.method_signature) = "workstation_cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "WorkstationCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes the specified workstation cluster.
  rpc DeleteWorkstationCluster(DeleteWorkstationClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/workstationClusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "WorkstationCluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns the requested workstation configuration.
  rpc GetWorkstationConfig(GetWorkstationConfigRequest)
      returns (WorkstationConfig) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/workstationClusters/*/workstationConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns all workstation configurations in the specified cluster.
  rpc ListWorkstationConfigs(ListWorkstationConfigsRequest)
      returns (ListWorkstationConfigsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/workstationClusters/*}/workstationConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns all workstation configurations in the specified cluster on which
  // the caller has the "workstations.workstation.create" permission.
  rpc ListUsableWorkstationConfigs(ListUsableWorkstationConfigsRequest)
      returns (ListUsableWorkstationConfigsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/workstationClusters/*}/workstationConfigs:listUsable"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new workstation configuration.
  rpc CreateWorkstationConfig(CreateWorkstationConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/workstationClusters/*}/workstationConfigs"
      body: "workstation_config"
    };
    option (google.api.method_signature) =
        "parent,workstation_config,workstation_config_id";
    option (google.longrunning.operation_info) = {
      response_type: "WorkstationConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an existing workstation configuration.
  rpc UpdateWorkstationConfig(UpdateWorkstationConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{workstation_config.name=projects/*/locations/*/workstationClusters/*/workstationConfigs/*}"
      body: "workstation_config"
    };
    option (google.api.method_signature) = "workstation_config,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "WorkstationConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes the specified workstation configuration.
  rpc DeleteWorkstationConfig(DeleteWorkstationConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/workstationClusters/*/workstationConfigs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "WorkstationConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns the requested workstation.
  rpc GetWorkstation(GetWorkstationRequest) returns (Workstation) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/workstationClusters/*/workstationConfigs/*/workstations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns all Workstations using the specified workstation configuration.
  rpc ListWorkstations(ListWorkstationsRequest)
      returns (ListWorkstationsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/workstationClusters/*/workstationConfigs/*}/workstations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns all workstations using the specified workstation configuration
  // on which the caller has the "workstations.workstations.use" permission.
  rpc ListUsableWorkstations(ListUsableWorkstationsRequest)
      returns (ListUsableWorkstationsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/workstationClusters/*/workstationConfigs/*}/workstations:listUsable"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new workstation.
  rpc CreateWorkstation(CreateWorkstationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/workstationClusters/*/workstationConfigs/*}/workstations"
      body: "workstation"
    };
    option (google.api.method_signature) = "parent,workstation,workstation_id";
    option (google.longrunning.operation_info) = {
      response_type: "Workstation"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an existing workstation.
  rpc UpdateWorkstation(UpdateWorkstationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{workstation.name=projects/*/locations/*/workstationClusters/*/workstationConfigs/*/workstations/*}"
      body: "workstation"
    };
    option (google.api.method_signature) = "workstation,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Workstation"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes the specified workstation.
  rpc DeleteWorkstation(DeleteWorkstationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/workstationClusters/*/workstationConfigs/*/workstations/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Workstation"
      metadata_type: "OperationMetadata"
    };
  }

  // Starts running a workstation so that users can connect to it.
  rpc StartWorkstation(StartWorkstationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{name=projects/*/locations/*/workstationClusters/*/workstationConfigs/*/workstations/*}:start"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Workstation"
      metadata_type: "OperationMetadata"
    };
  }

  // Stops running a workstation, reducing costs.
  rpc StopWorkstation(StopWorkstationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{name=projects/*/locations/*/workstationClusters/*/workstationConfigs/*/workstations/*}:stop"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Workstation"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns a short-lived credential that can be used to send authenticated and
  // authorized traffic to a workstation.
  rpc GenerateAccessToken(GenerateAccessTokenRequest)
      returns (GenerateAccessTokenResponse) {
    option (google.api.http) = {
      post: "/v1beta/{workstation=projects/*/locations/*/workstationClusters/*/workstationConfigs/*/workstations/*}:generateAccessToken"
      body: "*"
    };
    option (google.api.method_signature) = "workstation";
  }
}

// A workstation cluster resource in the Cloud Workstations API.
//
// Defines a group of workstations in a particular region and the
// VPC network they're attached to.
message WorkstationCluster {
  option (google.api.resource) = {
    type: "workstations.googleapis.com/WorkstationCluster"
    pattern: "projects/{project}/locations/{location}/workstationClusters/{workstation_cluster}"
    plural: "workstationClusters"
    singular: "workstationCluster"
    style: DECLARATIVE_FRIENDLY
  };

  // Configuration options for private workstation clusters.
  message PrivateClusterConfig {
    // Immutable. Whether Workstations endpoint is private.
    bool enable_private_endpoint = 1 [(google.api.field_behavior) = IMMUTABLE];

    // Output only. Hostname for the workstation cluster. This field will be
    // populated only when private endpoint is enabled. To access workstations
    // in the workstation cluster, create a new DNS zone mapping this domain
    // name to an internal IP address and a forwarding rule mapping that address
    // to the service attachment.
    string cluster_hostname = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Service attachment URI for the workstation cluster. The
    // service attachemnt is created when private endpoint is enabled. To access
    // workstations in the workstation cluster, configure access to the managed
    // service using [Private Service
    // Connect](https://cloud.google.com/vpc/docs/configure-private-service-connect-services).
    string service_attachment_uri = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Optional. Additional projects that are allowed to attach to the
    // workstation cluster's service attachment. By default, the workstation
    // cluster's project and the VPC host project (if different) are allowed.
    repeated string allowed_projects = 4
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Full name of this workstation cluster.
  string name = 1;

  // Optional. Human-readable name for this workstation cluster.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A system-assigned unique identifier for this workstation
  // cluster.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether this workstation cluster is currently being
  // updated to match its intended state.
  bool reconciling = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Client-specified annotations.
  map<string, string> annotations = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  // [Labels](https://cloud.google.com/workstations/docs/label-resources) that
  // are applied to the workstation cluster and that are also propagated to the
  // underlying Compute Engine resources.
  map<string, string> labels = 15 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Time when this workstation cluster was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this workstation cluster was most recently updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this workstation cluster was soft-deleted.
  google.protobuf.Timestamp delete_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Checksum computed by the server. May be sent on update and delete
  // requests to make sure that the client has an up-to-date value before
  // proceeding.
  string etag = 9 [(google.api.field_behavior) = OPTIONAL];

  // Immutable. Name of the Compute Engine network in which instances associated
  // with this workstation cluster will be created.
  string network = 10 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Name of the Compute Engine subnetwork in which instances
  // associated with this workstation cluster will be created. Must be part of
  // the subnetwork specified for this workstation cluster.
  string subnetwork = 11 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The private IP address of the control plane for this
  // workstation cluster. Workstation VMs need access to this IP address to work
  // with the service, so make sure that your firewall rules allow egress from
  // the workstation VMs to this address.
  string control_plane_ip = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Configuration for private workstation cluster.
  PrivateClusterConfig private_cluster_config = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Whether this workstation cluster is in degraded mode, in which
  // case it may require user action to restore full functionality. Details can
  // be found in
  // [conditions][google.cloud.workstations.v1beta.WorkstationCluster.conditions].
  bool degraded = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status conditions describing the workstation cluster's current
  // state.
  repeated google.rpc.Status conditions = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A workstation configuration resource in the Cloud Workstations API.
//
// Workstation configurations act as templates for workstations. The workstation
// configuration defines details such as the workstation virtual machine (VM)
// instance type, persistent storage, container image defining environment,
// which IDE or Code Editor to use, and more. Administrators and platform teams
// can also use [Identity and Access Management
// (IAM)](https://cloud.google.com/iam/docs/overview) rules to grant access to
// teams or to individual developers.
message WorkstationConfig {
  option (google.api.resource) = {
    type: "workstations.googleapis.com/WorkstationConfig"
    pattern: "projects/{project}/locations/{location}/workstationClusters/{workstation_cluster}/workstationConfigs/{workstation_config}"
    plural: "workstationConfigs"
    singular: "workstationConfig"
    style: DECLARATIVE_FRIENDLY
  };

  // Runtime host for a workstation.
  message Host {
    // A runtime using a Compute Engine instance.
    message GceInstance {
      // A set of Compute Engine Shielded instance options.
      message GceShieldedInstanceConfig {
        // Optional. Whether the instance has Secure Boot enabled.
        bool enable_secure_boot = 1 [(google.api.field_behavior) = OPTIONAL];

        // Optional. Whether the instance has the vTPM enabled.
        bool enable_vtpm = 2 [(google.api.field_behavior) = OPTIONAL];

        // Optional. Whether the instance has integrity monitoring enabled.
        bool enable_integrity_monitoring = 3
            [(google.api.field_behavior) = OPTIONAL];
      }

      // A set of Compute Engine Confidential VM instance options.
      message GceConfidentialInstanceConfig {
        // Optional. Whether the instance has confidential compute enabled.
        bool enable_confidential_compute = 1
            [(google.api.field_behavior) = OPTIONAL];
      }

      // An accelerator card attached to the instance.
      message Accelerator {
        // Optional. Type of accelerator resource to attach to the instance, for
        // example,
        // `"nvidia-tesla-p100"`.
        string type = 1 [(google.api.field_behavior) = OPTIONAL];

        // Optional. Number of accelerator cards exposed to the instance.
        int32 count = 2 [(google.api.field_behavior) = OPTIONAL];
      }

      // Optional. The type of machine to use for VM instances—for example,
      // `"e2-standard-4"`. For more information about machine types that
      // Cloud Workstations supports, see the list of
      // [available machine
      // types](https://cloud.google.com/workstations/docs/available-machine-types).
      string machine_type = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The email address of the service account for Cloud
      // Workstations VMs created with this configuration. When specified, be
      // sure that the service account has `logginglogEntries.create` permission
      // on the project so it can write logs out to Cloud Logging. If using a
      // custom container image, the service account must have permissions to
      // pull the specified image.
      //
      // If you as the administrator want to be able to `ssh` into the
      // underlying VM, you need to set this value to a service account
      // for which you have the `iam.serviceAccounts.actAs` permission.
      // Conversely, if you don't want anyone to be able to `ssh` into the
      // underlying VM, use a service account where no one has that
      // permission.
      //
      // If not set, VMs run with a service account provided by the
      // Cloud Workstations service, and the image must be publicly
      // accessible.
      string service_account = 2 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Scopes to grant to the
      // [service_account][google.cloud.workstations.v1beta.WorkstationConfig.Host.GceInstance.service_account].
      // Various scopes are automatically added based on feature usage. When
      // specified, users of workstations under this configuration must have
      // `iam.serviceAccounts.actAs` on the service account.
      repeated string service_account_scopes = 3
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. Network tags to add to the Compute Engine VMs backing the
      // workstations. This option applies
      // [network
      // tags](https://cloud.google.com/vpc/docs/add-remove-network-tags) to VMs
      // created with this configuration. These network tags enable the creation
      // of [firewall
      // rules](https://cloud.google.com/workstations/docs/configure-firewall-rules).
      repeated string tags = 4 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The number of VMs that the system should keep idle so that
      // new workstations can be started quickly for new users. Defaults to `0`
      // in the API.
      int32 pool_size = 5 [(google.api.field_behavior) = OPTIONAL];

      // Output only. Number of instances currently available in the pool for
      // faster workstation startup.
      int32 pooled_instances = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Optional. When set to true, disables public IP addresses for VMs. If
      // you disable public IP addresses, you must set up Private Google Access
      // or Cloud NAT on your network. If you use Private Google Access and you
      // use `private.googleapis.com` or `restricted.googleapis.com` for
      // Container Registry and Artifact Registry, make sure that you set
      // up DNS records for domains `*.gcr.io` and `*.pkg.dev`.
      // Defaults to false (VMs have public IP addresses).
      bool disable_public_ip_addresses = 6
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. Whether to enable nested virtualization on Cloud Workstations
      // VMs created under this workstation configuration.
      //
      // Nested virtualization lets you run virtual machine (VM) instances
      // inside your workstation. Before enabling nested virtualization,
      // consider the following important considerations. Cloud Workstations
      // instances are subject to the [same restrictions as Compute Engine
      // instances](https://cloud.google.com/compute/docs/instances/nested-virtualization/overview#restrictions):
      //
      // * **Organization policy**: projects, folders, or
      // organizations may be restricted from creating nested VMs if the
      // **Disable VM nested virtualization** constraint is enforced in
      // the organization policy. For more information, see the
      // Compute Engine section,
      // [Checking whether nested virtualization is
      // allowed](https://cloud.google.com/compute/docs/instances/nested-virtualization/managing-constraint#checking_whether_nested_virtualization_is_allowed).
      // * **Performance**: nested VMs might experience a 10% or greater
      // decrease in performance for workloads that are CPU-bound and
      // possibly greater than a 10% decrease for workloads that are
      // input/output bound.
      // * **Machine Type**: nested virtualization can only be enabled on
      // workstation configurations that specify a
      // [machine_type][google.cloud.workstations.v1beta.WorkstationConfig.Host.GceInstance.machine_type]
      // in the N1 or N2 machine series.
      // * **GPUs**: nested virtualization may not be enabled on workstation
      // configurations with accelerators.
      // * **Operating System**: Because
      // [Container-Optimized
      // OS](https://cloud.google.com/compute/docs/images/os-details#container-optimized_os_cos)
      // does not support nested virtualization, when nested virtualization is
      // enabled, the underlying Compute Engine VM instances boot from an
      // [Ubuntu
      // LTS](https://cloud.google.com/compute/docs/images/os-details#ubuntu_lts)
      // image.
      bool enable_nested_virtualization = 7
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. A set of Compute Engine Shielded instance options.
      GceShieldedInstanceConfig shielded_instance_config = 8
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. A set of Compute Engine Confidential VM instance options.
      GceConfidentialInstanceConfig confidential_instance_config = 10
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. The size of the boot disk for the VM in gigabytes (GB).
      // The minimum boot disk size is `30` GB. Defaults to `50` GB.
      int32 boot_disk_size_gb = 9 [(google.api.field_behavior) = OPTIONAL];

      // Optional. A list of the type and count of accelerator cards attached to
      // the instance.
      repeated Accelerator accelerators = 11
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Type of host that will be used for the workstation's runtime.
    oneof config {
      // Specifies a Compute Engine instance as the host.
      GceInstance gce_instance = 1;
    }
  }

  // A directory to persist across workstation sessions.
  message PersistentDirectory {
    // A PersistentDirectory backed by a Compute Engine regional persistent
    // disk. The
    // [persistent_directories][google.cloud.workstations.v1beta.WorkstationConfig.persistent_directories]
    // field is repeated, but it may contain only one entry. It creates a
    // [persistent
    // disk](https://cloud.google.com/compute/docs/disks/persistent-disks) that
    // mounts to the workstation VM at `/home` when the session starts and
    // detaches when the session ends. If this field is empty, workstations
    // created with this configuration do not have a persistent home
    // directory.
    message GceRegionalPersistentDisk {
      // Value representing what should happen to the disk after the workstation
      // is deleted.
      enum ReclaimPolicy {
        // Do not use.
        RECLAIM_POLICY_UNSPECIFIED = 0;

        // Delete the persistent disk when deleting the workstation.
        DELETE = 1;

        // Keep the persistent disk when deleting the workstation.
        // An administrator must manually delete the disk.
        RETAIN = 2;
      }

      // Optional. The GB capacity of a persistent home directory for each
      // workstation created with this configuration. Must be empty if
      // [source_snapshot][google.cloud.workstations.v1beta.WorkstationConfig.PersistentDirectory.GceRegionalPersistentDisk.source_snapshot]
      // is set.
      //
      // Valid values are `10`, `50`, `100`, `200`, `500`, or `1000`.
      // Defaults to `200`. If less than `200` GB, the
      // [disk_type][google.cloud.workstations.v1beta.WorkstationConfig.PersistentDirectory.GceRegionalPersistentDisk.disk_type]
      // must be
      // `"pd-balanced"` or `"pd-ssd"`.
      int32 size_gb = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Type of file system that the disk should be formatted with.
      // The workstation image must support this file system type. Must be empty
      // if
      // [source_snapshot][google.cloud.workstations.v1beta.WorkstationConfig.PersistentDirectory.GceRegionalPersistentDisk.source_snapshot]
      // is set. Defaults to `"ext4"`.
      string fs_type = 2 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The [type of the persistent
      // disk](https://cloud.google.com/compute/docs/disks#disk-types) for the
      // home directory. Defaults to `"pd-standard"`.
      string disk_type = 3 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Name of the snapshot to use as the source for the disk. If
      // set,
      // [size_gb][google.cloud.workstations.v1beta.WorkstationConfig.PersistentDirectory.GceRegionalPersistentDisk.size_gb]
      // and
      // [fs_type][google.cloud.workstations.v1beta.WorkstationConfig.PersistentDirectory.GceRegionalPersistentDisk.fs_type]
      // must be empty.
      string source_snapshot = 5 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Whether the persistent disk should be deleted when the
      // workstation is deleted. Valid values are `DELETE` and `RETAIN`.
      // Defaults to `DELETE`.
      ReclaimPolicy reclaim_policy = 4 [(google.api.field_behavior) = OPTIONAL];
    }

    // How a persistent directory should be implemented.
    oneof directory_type {
      // A PersistentDirectory backed by a Compute Engine persistent disk.
      GceRegionalPersistentDisk gce_pd = 2;
    }

    // Optional. Location of this directory in the running workstation.
    string mount_path = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // An ephemeral directory which won't persist across workstation sessions. It
  // is freshly created on every workstation start operation.
  message EphemeralDirectory {
    // An EphemeralDirectory is backed by a Compute Engine persistent disk.
    message GcePersistentDisk {
      // Optional. Type of the disk to use. Defaults to `"pd-standard"`.
      string disk_type = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Name of the snapshot to use as the source for the disk. Must
      // be empty if
      // [source_image][google.cloud.workstations.v1beta.WorkstationConfig.EphemeralDirectory.GcePersistentDisk.source_image]
      // is set. Updating
      // [source_snapshot][google.cloud.workstations.v1beta.WorkstationConfig.EphemeralDirectory.GcePersistentDisk.source_snapshot]
      // will update content in the ephemeral directory after the workstation is
      // restarted. This field is mutable.
      string source_snapshot = 2 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Name of the disk image to use as the source for the disk.
      // Must be empty if
      // [source_snapshot][google.cloud.workstations.v1beta.WorkstationConfig.EphemeralDirectory.GcePersistentDisk.source_snapshot]
      // is set. Updating
      // [source_image][google.cloud.workstations.v1beta.WorkstationConfig.EphemeralDirectory.GcePersistentDisk.source_image]
      // will update content in the ephemeral directory after the workstation is
      // restarted. This field is mutable.
      string source_image = 3 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Whether the disk is read only. If true, the disk may be
      // shared by multiple VMs and
      // [source_snapshot][google.cloud.workstations.v1beta.WorkstationConfig.EphemeralDirectory.GcePersistentDisk.source_snapshot]
      // must be set.
      bool read_only = 4 [(google.api.field_behavior) = OPTIONAL];
    }

    // How an ephemeral directory should be implemented.
    oneof directory_type {
      // An EphemeralDirectory backed by a Compute Engine persistent disk.
      GcePersistentDisk gce_pd = 3;
    }

    // Required. Location of this directory in the running workstation.
    string mount_path = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // A Docker container.
  message Container {
    // Optional. A Docker container image that defines a custom environment.
    //
    // Cloud Workstations provides a number of
    // [preconfigured
    // images](https://cloud.google.com/workstations/docs/preconfigured-base-images),
    // but you can create your own
    // [custom container
    // images](https://cloud.google.com/workstations/docs/custom-container-images).
    // If using a private image, the `host.gceInstance.serviceAccount` field
    // must be specified in the workstation configuration and must have
    // permission to pull the specified image. Otherwise, the image must be
    // publicly accessible.
    string image = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If set, overrides the default ENTRYPOINT specified by the
    // image.
    repeated string command = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Arguments passed to the entrypoint.
    repeated string args = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Environment variables passed to the container's entrypoint.
    map<string, string> env = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If set, overrides the default DIR specified by the image.
    string working_dir = 5 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If set, overrides the USER specified in the image with the
    // given uid.
    int32 run_as_user = 6 [(google.api.field_behavior) = OPTIONAL];
  }

  // A customer-managed encryption key (CMEK) for the Compute Engine
  // resources of the associated workstation configuration. Specify the name of
  // your Cloud KMS encryption key and the default service account.
  // We recommend that you use a separate service account and follow
  // [Cloud KMS best
  // practices](https://cloud.google.com/kms/docs/separation-of-duties).
  message CustomerEncryptionKey {
    // Immutable. The name of the Google Cloud KMS encryption key. For example,
    // `"projects/PROJECT_ID/locations/REGION/keyRings/KEY_RING/cryptoKeys/KEY_NAME"`.
    // The key must be in the same region as the workstation configuration.
    string kms_key = 1 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The service account to use with the specified
    // KMS key. We recommend that you use a separate service account
    // and follow KMS best practices. For more information, see
    // [Separation of
    // duties](https://cloud.google.com/kms/docs/separation-of-duties) and
    // `gcloud kms keys add-iam-policy-binding`
    // [`--member`](https://cloud.google.com/sdk/gcloud/reference/kms/keys/add-iam-policy-binding#--member).
    string kms_key_service_account = 2
        [(google.api.field_behavior) = IMMUTABLE];
  }

  // A readiness check to be performed on a workstation.
  message ReadinessCheck {
    // Optional. Path to which the request should be sent.
    string path = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Port to which the request should be sent.
    int32 port = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Full name of this workstation configuration.
  string name = 1;

  // Optional. Human-readable name for this workstation configuration.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A system-assigned unique identifier for this workstation
  // configuration.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether this workstation configuration is currently
  // being updated to match its intended state.
  bool reconciling = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Client-specified annotations.
  map<string, string> annotations = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  // [Labels](https://cloud.google.com/workstations/docs/label-resources) that
  // are applied to the workstation configuration and that are also propagated
  // to the underlying Compute Engine resources.
  map<string, string> labels = 18 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Time when this workstation configuration was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this workstation configuration was most recently
  // updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this workstation configuration was soft-deleted.
  google.protobuf.Timestamp delete_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Checksum computed by the server. May be sent on update and delete
  // requests to make sure that the client has an up-to-date value before
  // proceeding.
  string etag = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of seconds to wait before automatically stopping a
  // workstation after it last received user traffic.
  //
  // A value of `"0s"` indicates that Cloud Workstations VMs created with this
  // configuration should never time out due to idleness.
  // Provide
  // [duration](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#duration)
  // terminated by `s` for seconds—for example, `"7200s"` (2 hours).
  // The default is `"1200s"` (20 minutes).
  google.protobuf.Duration idle_timeout = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Number of seconds that a workstation can run until it is
  // automatically shut down. We recommend that workstations be shut down daily
  // to reduce costs and so that security updates can be applied upon restart.
  // The
  // [idle_timeout][google.cloud.workstations.v1beta.WorkstationConfig.idle_timeout]
  // and
  // [running_timeout][google.cloud.workstations.v1beta.WorkstationConfig.running_timeout]
  // fields are independent of each other. Note that the
  // [running_timeout][google.cloud.workstations.v1beta.WorkstationConfig.running_timeout]
  // field shuts down VMs after the specified time, regardless of whether or not
  // the VMs are idle.
  //
  // Provide duration terminated by `s` for seconds—for example, `"54000s"`
  // (15 hours). Defaults to `"43200s"` (12 hours). A value of `"0s"` indicates
  // that workstations using this configuration should never time out. If
  // [encryption_key][google.cloud.workstations.v1beta.WorkstationConfig.encryption_key]
  // is set, it must be greater than `"0s"` and less than
  // `"86400s"` (24 hours).
  //
  // Warning: A value of `"0s"` indicates that Cloud Workstations VMs created
  // with this configuration have no maximum running time. This is strongly
  // discouraged because you incur costs and will not pick up security updates.
  google.protobuf.Duration running_timeout = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Runtime host for the workstation.
  Host host = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Directories to persist across workstation sessions.
  repeated PersistentDirectory persistent_directories = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Ephemeral directories which won't persist across workstation
  // sessions.
  repeated EphemeralDirectory ephemeral_directories = 22
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Container that runs upon startup for each workstation using this
  // workstation configuration.
  Container container = 14 [(google.api.field_behavior) = OPTIONAL];

  // Immutable. Encrypts resources of this workstation configuration using a
  // customer-managed encryption key (CMEK).
  //
  // If specified, the boot disk of the Compute Engine instance and the
  // persistent disk are encrypted using this encryption key. If
  // this field is not set, the disks are encrypted using a generated
  // key. Customer-managed encryption keys do not protect disk metadata.
  //
  // If the customer-managed encryption key is rotated, when the workstation
  // instance is stopped, the system attempts to recreate the
  // persistent disk with the new version of the key. Be sure to keep
  // older versions of the key until the persistent disk is recreated.
  // Otherwise, data on the persistent disk might be lost.
  //
  // If the encryption key is revoked, the workstation session automatically
  // stops within 7 hours.
  //
  // Immutable after the workstation configuration is created.
  CustomerEncryptionKey encryption_key = 17
      [(google.api.field_behavior) = IMMUTABLE];

  // Optional. Readiness checks to perform when starting a workstation using
  // this workstation configuration. Mark a workstation as running only after
  // all specified readiness checks return 200 status codes.
  repeated ReadinessCheck readiness_checks = 19
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Immutable. Specifies the zones used to replicate the VM and disk
  // resources within the region. If set, exactly two zones within the
  // workstation cluster's region must be specified—for example,
  // `['us-central1-a', 'us-central1-f']`. If this field is empty, two default
  // zones within the region are used.
  //
  // Immutable after the workstation configuration is created.
  repeated string replica_zones = 23 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Whether this resource is degraded, in which case it may
  // require user action to restore full functionality. See also the
  // [conditions][google.cloud.workstations.v1beta.WorkstationConfig.conditions]
  // field.
  bool degraded = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status conditions describing the current resource state.
  repeated google.rpc.Status conditions = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Whether to enable Linux `auditd` logging on the workstation. When
  // enabled, a service account must also be specified that has
  // `logging.buckets.write` permission on the project. Operating system audit
  // logging is distinct from [Cloud Audit
  // Logs](https://cloud.google.com/workstations/docs/audit-logging).
  bool enable_audit_agent = 20 [(google.api.field_behavior) = OPTIONAL];
}

// A single instance of a developer workstation with its own persistent storage.
message Workstation {
  option (google.api.resource) = {
    type: "workstations.googleapis.com/Workstation"
    pattern: "projects/{project}/locations/{location}/workstationClusters/{workstation_cluster}/workstationConfigs/{workstation_config}/workstations/{workstation}"
    plural: "workstations"
    singular: "workstation"
    style: DECLARATIVE_FRIENDLY
  };

  // Whether a workstation is running and ready to receive user requests.
  enum State {
    // Do not use.
    STATE_UNSPECIFIED = 0;

    // The workstation is not yet ready to accept requests from users but will
    // be soon.
    STATE_STARTING = 1;

    // The workstation is ready to accept requests from users.
    STATE_RUNNING = 2;

    // The workstation is being stopped.
    STATE_STOPPING = 3;

    // The workstation is stopped and will not be able to receive requests until
    // it is started.
    STATE_STOPPED = 4;
  }

  // Full name of this workstation.
  string name = 1;

  // Optional. Human-readable name for this workstation.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A system-assigned unique identifier for this workstation.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether this workstation is currently being updated
  // to match its intended state.
  bool reconciling = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Client-specified annotations.
  map<string, string> annotations = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional.
  // [Labels](https://cloud.google.com/workstations/docs/label-resources) that
  // are applied to the workstation and that are also propagated to the
  // underlying Compute Engine resources.
  map<string, string> labels = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Time when this workstation was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this workstation was most recently updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this workstation was most recently successfully
  // started, regardless of the workstation's initial state.
  google.protobuf.Timestamp start_time = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this workstation was soft-deleted.
  google.protobuf.Timestamp delete_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Checksum computed by the server. May be sent on update and delete
  // requests to make sure that the client has an up-to-date value before
  // proceeding.
  string etag = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Current state of the workstation.
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Host to which clients can send HTTPS traffic that will be
  // received by the workstation. Authorized traffic will be received to the
  // workstation as HTTP on port 80. To send traffic to a different port,
  // clients may prefix the host with the destination port in the format
  // `{port}-{host}`.
  string host = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Environment variables passed to the workstation container's
  // entrypoint.
  map<string, string> env = 12 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for GetWorkstationCluster.
message GetWorkstationClusterRequest {
  // Required. Name of the requested resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/WorkstationCluster"
    }
  ];
}

// Request message for ListWorkstationClusters.
message ListWorkstationClustersRequest {
  // Required. Parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "workstations.googleapis.com/WorkstationCluster"
    }
  ];

  // Optional. Maximum number of items to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. next_page_token value returned from a previous List request, if
  // any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListWorkstationClusters.
message ListWorkstationClustersResponse {
  // The requested workstation clusters.
  repeated WorkstationCluster workstation_clusters = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Unreachable resources.
  repeated string unreachable = 3;
}

// Message for creating a CreateWorkstationCluster.
message CreateWorkstationClusterRequest {
  // Required. Parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "workstations.googleapis.com/WorkstationCluster"
    }
  ];

  // Required. ID to use for the workstation cluster.
  string workstation_cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Workstation cluster to create.
  WorkstationCluster workstation_cluster = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // actually apply it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for UpdateWorkstationCluster.
message UpdateWorkstationClusterRequest {
  // Required. Workstation cluster to update.
  WorkstationCluster workstation_cluster = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Mask that specifies which fields in the workstation cluster
  // should be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // actually apply it.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, and the workstation cluster is not found, a new
  // workstation cluster will be created. In this situation, update_mask is
  // ignored.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a workstation cluster.
message DeleteWorkstationClusterRequest {
  // Required. Name of the workstation cluster to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/WorkstationCluster"
    }
  ];

  // Optional. If set, validate the request and preview the review, but do not
  // apply it.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the request will be rejected if the latest version of the
  // workstation cluster on the server does not have this ETag.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, any workstation configurations and workstations in the
  // workstation cluster are also deleted. Otherwise, the request only
  // works if the workstation cluster has no configurations or workstations.
  bool force = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for GetWorkstationConfig.
message GetWorkstationConfigRequest {
  // Required. Name of the requested resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/WorkstationConfig"
    }
  ];
}

// Request message for ListWorkstationConfigs.
message ListWorkstationConfigsRequest {
  // Required. Parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/WorkstationCluster"
    }
  ];

  // Optional. Maximum number of items to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. next_page_token value returned from a previous List request, if
  // any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListWorkstationConfigs.
message ListWorkstationConfigsResponse {
  // The requested configs.
  repeated WorkstationConfig workstation_configs = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Unreachable resources.
  repeated string unreachable = 3;
}

// Request message for ListUsableWorkstationConfigs.
message ListUsableWorkstationConfigsRequest {
  // Required. Parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/WorkstationCluster"
    }
  ];

  // Optional. Maximum number of items to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. next_page_token value returned from a previous List request, if
  // any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListUsableWorkstationConfigs.
message ListUsableWorkstationConfigsResponse {
  // The requested configs.
  repeated WorkstationConfig workstation_configs = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Unreachable resources.
  repeated string unreachable = 3;
}

// Message for creating a CreateWorkstationConfig.
message CreateWorkstationConfigRequest {
  // Required. Parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/WorkstationCluster"
    }
  ];

  // Required. ID to use for the workstation configuration.
  string workstation_config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Config to create.
  WorkstationConfig workstation_config = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // actually apply it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for UpdateWorkstationConfig.
message UpdateWorkstationConfigRequest {
  // Required. Config to update.
  WorkstationConfig workstation_config = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Mask specifying which fields in the workstation configuration
  // should be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // actually apply it.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set and the workstation configuration is not found, a new
  // workstation configuration will be created. In this situation,
  // update_mask is ignored.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a workstation configuration.
message DeleteWorkstationConfigRequest {
  // Required. Name of the workstation configuration to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/WorkstationConfig"
    }
  ];

  // Optional. If set, validate the request and preview the review, but do not
  // actually apply it.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the request is rejected if the latest version of the
  // workstation configuration on the server does not have this ETag.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, any workstations in the workstation configuration are
  // also deleted. Otherwise, the request works only if the workstation
  // configuration has no workstations.
  bool force = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for GetWorkstation.
message GetWorkstationRequest {
  // Required. Name of the requested resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/Workstation"
    }
  ];
}

// Request message for ListWorkstations.
message ListWorkstationsRequest {
  // Required. Parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/WorkstationConfig"
    }
  ];

  // Optional. Maximum number of items to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. next_page_token value returned from a previous List request, if
  // any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListWorkstations.
message ListWorkstationsResponse {
  // The requested workstations.
  repeated Workstation workstations = 1;

  // Optional. Token to retrieve the next page of results, or empty if there are
  // no more results in the list.
  string next_page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Unreachable resources.
  repeated string unreachable = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for ListUsableWorkstations.
message ListUsableWorkstationsRequest {
  // Required. Parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/WorkstationConfig"
    }
  ];

  // Optional. Maximum number of items to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. next_page_token value returned from a previous List request, if
  // any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListUsableWorkstations.
message ListUsableWorkstationsResponse {
  // The requested workstations.
  repeated Workstation workstations = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Unreachable resources.
  repeated string unreachable = 3;
}

// Message for creating a CreateWorkstation.
message CreateWorkstationRequest {
  // Required. Parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/WorkstationConfig"
    }
  ];

  // Required. ID to use for the workstation.
  string workstation_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Workstation to create.
  Workstation workstation = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // actually apply it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for UpdateWorkstation.
message UpdateWorkstationRequest {
  // Required. Workstation to update.
  Workstation workstation = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Mask specifying which fields in the workstation configuration
  // should be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, validate the request and preview the review, but do not
  // actually apply it.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set and the workstation configuration is not found, a new
  // workstation configuration is created. In this situation, update_mask
  // is ignored.
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for DeleteWorkstation.
message DeleteWorkstationRequest {
  // Required. Name of the workstation to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/Workstation"
    }
  ];

  // Optional. If set, validate the request and preview the review, but do not
  // actually apply it.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the request will be rejected if the latest version of the
  // workstation on the server does not have this ETag.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for StartWorkstation.
message StartWorkstationRequest {
  // Required. Name of the workstation to start.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/Workstation"
    }
  ];

  // Optional. If set, validate the request and preview the review, but do not
  // actually apply it.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the request will be rejected if the latest version of the
  // workstation on the server does not have this ETag.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for StopWorkstation.
message StopWorkstationRequest {
  // Required. Name of the workstation to stop.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/Workstation"
    }
  ];

  // Optional. If set, validate the request and preview the review, but do not
  // actually apply it.
  bool validate_only = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the request will be rejected if the latest version of the
  // workstation on the server does not have this ETag.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for GenerateAccessToken.
message GenerateAccessTokenRequest {
  // Desired expiration or lifetime of the access token.
  oneof expiration {
    // Desired expiration time of the access token. This value must
    // be at most 24 hours in the future. If a value is not specified, the
    // token's expiration time will be set to a default value of 1 hour in the
    // future.
    google.protobuf.Timestamp expire_time = 2;

    // Desired lifetime duration of the access token. This value must
    // be at most 24 hours. If a value is not specified, the token's lifetime
    // will be set to a default value of 1 hour.
    google.protobuf.Duration ttl = 3;
  }

  // Required. Name of the workstation for which the access token should be
  // generated.
  string workstation = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workstations.googleapis.com/Workstation"
    }
  ];
}

// Response message for GenerateAccessToken.
message GenerateAccessTokenResponse {
  // The generated bearer access token. To use this token, include it in an
  // Authorization header of an HTTP request sent to the associated
  // workstation's hostname—for example, `Authorization: Bearer
  // <access_token>`.
  string access_token = 1;

  // Time at which the generated token will expire.
  google.protobuf.Timestamp expire_time = 2;
}

// Metadata for long-running operations.
message OperationMetadata {
  // Output only. Time that the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time that the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
