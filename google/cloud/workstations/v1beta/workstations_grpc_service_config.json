{"methodConfig": [{"name": [{"service": "google.cloud.workstations.v1beta.Workstations", "method": "GenerateAccessToken"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "GetWorkstation"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "GetWorkstationCluster"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "GetWorkstationConfig"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "ListUsableWorkstationConfigs"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "ListUsableWorkstations"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "ListWorkstationClusters"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "ListWorkstationConfigs"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "ListWorkstations"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "GenerateAccessToken"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "GetWorkstation"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "GetWorkstationCluster"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "GetWorkstationConfig"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "ListUsableWorkstationConfigs"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "ListUsableWorkstations"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "ListWorkstationClusters"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "ListWorkstationConfigs"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "ListWorkstations"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.workstations.v1beta.Workstations", "method": "CreateWorkstation"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "CreateWorkstationCluster"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "CreateWorkstationConfig"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "DeleteWorkstation"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "DeleteWorkstationCluster"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "DeleteWorkstationConfig"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "StartWorkstation"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "StopWorkstation"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "UpdateWorkstation"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "UpdateWorkstationCluster"}, {"service": "google.cloud.workstations.v1beta.Workstations", "method": "UpdateWorkstationConfig"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "CreateWorkstation"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "CreateWorkstationCluster"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "CreateWorkstationConfig"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "DeleteWorkstation"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "DeleteWorkstationCluster"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "DeleteWorkstationConfig"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "StartWorkstation"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "StopWorkstation"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "UpdateWorkstation"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "UpdateWorkstationCluster"}, {"service": "google.cloud.workstations.v1.Workstations", "method": "UpdateWorkstationConfig"}], "timeout": "60s"}]}