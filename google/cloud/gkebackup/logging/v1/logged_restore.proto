// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.logging.v1;

option go_package = "cloud.google.com/go/gkebackup/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggedRestoreProto";
option java_package = "google.cloud.gkebackup.logging.v1";
option csharp_namespace = "Google.Cloud.GkeBackup.Logging.V1";
option php_namespace = "Google\\Cloud\\GkeBackup\\Logging\\V1";
option ruby_package = "Google::Cloud::GkeBackup::Logging::V1";

// Restore as stored in Platform log. It's used to log the update details of a
// updateRestore request, so only mutable and non-output_only fields are
// included here..
message LoggedRestore {
  // Possible values for state of the Restore.
  enum State {
    // The Restore resource is in the process of being created.
    STATE_UNSPECIFIED = 0;

    // The Restore resource has been created and the associated RestoreJob
    // Kubernetes resource has been injected into target cluster.
    CREATING = 1;

    // The gkebackup agent in the cluster has begun executing the restore
    // operation.
    IN_PROGRESS = 2;

    // The restore operation has completed successfully. Restored workloads may
    // not yet be operational.
    SUCCEEDED = 3;

    // The restore operation has failed.
    FAILED = 4;

    // This Restore resource is in the process of being deleted.
    DELETING = 5;
  }

  // Full name of the Backup resource this Restore resource used to restore
  // from. Format: projects/*/locations/*/backupPlans/*/backups/*.
  string backup = 1;

  // GCP Labels.
  map<string, string> labels = 2;

  // User specified descriptive string for this Restore.
  string description = 3;

  // The current state of the Restore.
  State state = 4;

  // Human-readable description of why the Restore is in its current state.
  string state_reason = 5;
}
