// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.logging.v1;

import "google/cloud/gkebackup/logging/v1/logged_backup.proto";
import "google/cloud/gkebackup/logging/v1/logged_backup_plan.proto";
import "google/cloud/gkebackup/logging/v1/logged_restore.proto";
import "google/cloud/gkebackup/logging/v1/logged_restore_plan.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/gkebackup/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggingProto";
option java_package = "google.cloud.gkebackup.logging.v1";
option csharp_namespace = "Google.Cloud.GkeBackup.Logging.V1";
option php_namespace = "Google\\Cloud\\GkeBackup\\Logging\\V1";
option ruby_package = "Google::Cloud::GkeBackup::Logging::V1";

// use case 1
// A log entry when modification(creation, update, deletion) is made to a
// BackupPlan
message BackupPlanChange {
  // The full name of the old BackupPlan resource that is being modified.
  // Empty for creation.
  // Format: projects/{project}/locations/{location}/backupPlans/{backup_plan}
  string backup_plan = 1;

  // Type of the change is being made.
  ChangeType change_type = 2;

  // Modification details.
  google.protobuf.FieldMask update_mask = 3;

  // The input BackupPlan resource with the updated fields populated to update
  // the source BackupPlan to.
  LoggedBackupPlan input_backup_plan = 4;

  // The error code and message.
  google.rpc.Status error = 5;
}

// use case 2
// A log entry when modification(creation, update, deletion) is made to a
// Backup
message BackupChange {
  // The full name of the Backup resource that is being modified.
  // Format:
  // projects/{project}/locations/{location}/backupPlans/{backup_plan}/backups/{backup}
  string backup = 1;

  // Type of the change is being made.
  ChangeType change_type = 2;

  // Whether the change is made manually or automatically.
  bool scheduled = 3;

  // Modification details.
  google.protobuf.FieldMask update_mask = 4;

  // The input Backup resource with the updated fields populated to update
  // the source Backup to, or the backup created automatically from retention
  // policy.
  LoggedBackup input_backup = 5;

  // The error code and message.
  google.rpc.Status error = 6;
}

// use case 3
// A log entry when modification(creation, update, deletion) is made to a
// restorePlan.
message RestorePlanChange {
  // The full name of the RestorePlan resource that is being modified.
  // Empty for creation.
  // Format: projects/*/locations/*/restorePlans/*
  string restore_plan = 1;

  // Type of the change is being made.
  ChangeType change_type = 2;

  // Modification details.
  google.protobuf.FieldMask update_mask = 3;

  // The input RestorePlan resource with the updated fields populated to update
  // the source RestorePlan to.
  LoggedRestorePlan input_restore_plan = 4;

  // The error code and message.
  google.rpc.Status error = 5;
}

// use case 4
// A log entry when modification(creation, update, deletion) is made to a
// restore.
message RestoreChange {
  // The full name of the Restore resource that is being modified.
  // Empty for creation.
  // Format: projects/*/locations/*/restorePlans/*/restores/*
  string restore = 1;

  // Type of the change is being made.
  ChangeType change_type = 2;

  // Modification details.
  google.protobuf.FieldMask update_mask = 3;

  // The input Restore resource with the updated fields populated to update
  // the source Restore to.
  LoggedRestore input_restore = 4;

  // The error code and message.
  google.rpc.Status error = 5;
}

// The type of changes this log is about.
enum ChangeType {
  // Default value, not specified.
  CHANGE_TYPE_UNSPECIFIED = 0;

  // The resource is created.
  CREATION = 1;

  // The resource is updated.
  UPDATE = 2;

  // The resource is deleted.
  DELETION = 3;
}
