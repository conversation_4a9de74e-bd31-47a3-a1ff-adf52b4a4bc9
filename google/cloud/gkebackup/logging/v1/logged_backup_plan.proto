// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.logging.v1;

import "google/cloud/gkebackup/logging/v1/logged_common.proto";

option go_package = "cloud.google.com/go/gkebackup/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggedBackupPlanProto";
option java_package = "google.cloud.gkebackup.logging.v1";
option csharp_namespace = "Google.Cloud.GkeBackup.Logging.V1";
option php_namespace = "Google\\Cloud\\GkeBackup\\Logging\\V1";
option ruby_package = "Google::Cloud::GkeBackup::Logging::V1";

// BackupPlan as stored in Platform log. It's used to log the details of
// a createBackupPlan/updateBackupPlan request, so only fields that can be taken
// from user input are included here.
message LoggedBackupPlan {
  // RentionPolicy is an inner message type to define:
  // 1. When to automatically delete Backups created under this BackupPlan
  // 2. A plan level minimum Backup retain days which blocks deletion
  // 3. Lock to disallow any policy updates
  message RetentionPolicy {
    // Number of days during which deletion of a Backup created under this
    // BackupPlan will be blocked.
    int32 backup_delete_lock_days = 1;

    // Number of days after which the service will delete a Backup.
    // If specified, a Backup created under this BackupPlan will be
    // automatically deleted after its age reaches create_time +
    // backup_retain_days.
    int32 backup_retain_days = 2;

    // A flag denotes that the retention policy of this BackupPlan is locked.
    // If set to True, no further update is allowed on this policy, including
    // the 'locked' field itself.
    // Default to False.
    bool locked = 3;
  }

  // Schedule, an inner message type defines a cron schedule.
  message Schedule {
    // A cron style string schedule on which an operation will be executed.
    string cron_schedule = 1;

    // A flag to toggle scheduled operation.
    bool paused = 2;
  }

  // BackupConfig, an inner message type defines the configuration of creating
  // a backup from this BackupPlan
  message BackupConfig {
    oneof backup_scope {
      // If set to true, backup whole cluster
      bool all_namespaces = 1;

      // If set, backup the list of namespaces
      Namespaces selected_namespaces = 2;

      // If set, backup the list of applications
      NamespacedNames selected_applications = 3;
    }

    // A boolean flag specifies whether volume data should be backed up
    bool include_volume_data = 4;

    // A boolean flag specifies whether secrets should be backed up
    bool include_secrets = 5;

    // Custom encryption key. For preview, support GCP KMS only.
    // This only contains the key metadata, and no key material.
    EncryptionKey encryption_key = 6;
  }

  // User specified descriptive string for this BackupPlan.
  string description = 1;

  // GCP resource name of the source cluster for this BackupPlan.
  string cluster = 2;

  // RetentionPolicy governs lifecycle of Backups created under this plan.
  RetentionPolicy retention_policy = 3;

  // A set of custom labels supplied by user.
  map<string, string> labels = 4;

  // Defines scheduled Backup creation under this BackupPlan.
  Schedule backup_schedule = 5;

  // A flag indicates whether the plan has been deactivated.
  bool deactivated = 6;

  // Defines backup configuration of this BackupPlan.
  BackupConfig backup_config = 7;

  // A number that represents the current risk level of this BackupPlan from RPO
  // perspective with 1 being no risk and 5 being highest risk.
  int32 rpo_risk_level = 8;
}
