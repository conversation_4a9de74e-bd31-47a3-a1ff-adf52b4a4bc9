// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.logging.v1;

option go_package = "cloud.google.com/go/gkebackup/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggedBackupProto";
option java_package = "google.cloud.gkebackup.logging.v1";
option csharp_namespace = "Google.Cloud.GkeBackup.Logging.V1";
option php_namespace = "Google\\Cloud\\GkeBackup\\Logging\\V1";
option ruby_package = "Google::Cloud::GkeBackup::Logging::V1";

// Backup as stored in Platform log. It's used to log the details of
// a createBackup/updateBackup request, so only fields that can be taken
// from API calls are included here.
message LoggedBackup {
  // State
  enum State {
    // The Backup resource is in the process of being created.
    STATE_UNSPECIFIED = 0;

    // The Backup resource has been created and the associated BackupJob
    // Kubernetes resource has been injected into the source cluster.
    CREATING = 1;

    // The gkebackup agent in the cluster has begun executing the backup
    // operation.
    IN_PROGRESS = 2;

    // The backup operation has completed successfully.
    SUCCEEDED = 3;

    // The backup operation has failed.
    FAILED = 4;

    // This Backup resource (and its associated artifacts) is in the process
    // of being deleted.
    DELETING = 5;
  }

  // A set of custom labels supplied by user.
  map<string, string> labels = 1;

  // delete_lock_days specifies the number of days from the create_time of this
  // Backup before which deletion will be blocked.
  int32 delete_lock_days = 2;

  // retain_days specifies the desired number of days from the create_time of
  // this Backup after which it will be automatically deleted.
  int32 retain_days = 3;

  // User specified descriptive string for this Backup.
  string description = 4;

  // Current state of the Backup
  State state = 5;

  // Human-readable description of why the backup is in the current `state`.
  string state_reason = 6;
}
