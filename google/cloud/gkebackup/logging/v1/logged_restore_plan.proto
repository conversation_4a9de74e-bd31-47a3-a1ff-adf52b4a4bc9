// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.logging.v1;

import "google/cloud/gkebackup/logging/v1/logged_common.proto";

option go_package = "cloud.google.com/go/gkebackup/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggedRestorePlanProto";
option java_package = "google.cloud.gkebackup.logging.v1";
option csharp_namespace = "Google.Cloud.GkeBackup.Logging.V1";
option php_namespace = "Google\\Cloud\\GkeBackup\\Logging\\V1";
option ruby_package = "Google::Cloud::GkeBackup::Logging::V1";

// RestorePlan as stored in Platform log. It's used to log the details of
// a createRestorePlan/updateRestorePlan request, so only fields that can be
// taken from user input are included here.
message LoggedRestorePlan {
  // User specified descriptive string for this RestorePlan.
  string description = 1;

  // The BackupPlan from which Backups may be used as the source
  // for Restores created via this RestorePlan.
  // Format: projects/*/locations/*/backupPlans/*.
  string backup_plan = 2;

  // The target cluster into which Restores created via this RestorePlan
  // will restore data. NOTE: the cluster's region must be the same as the
  // RestorePlan.
  // Possible formats:
  //   1. projects/*/locations/*/clusters/*
  //   2. projects/*/zones/*/clusters/*
  string cluster = 3;

  // Configuration of Restores created via this RestorePlan.
  RestoreConfig restore_config = 4;

  // A set of custom labels supplied by user.
  map<string, string> labels = 5;
}

// Configuration of a restore.
message RestoreConfig {
  // This is a direct map to the Kubernetes GroupKind type
  // [GroupKind](https://godoc.org/k8s.io/apimachinery/pkg/runtime/schema#GroupKind)
  // and is used for identifying specific "types" of resources to restore.
  message GroupKind {
    // API group string of a Kubernetes resource, e.g.
    // "apiextensions.k8s.io", "storage.k8s.io", etc.
    // Note: use empty string for core API group
    string resource_group = 1;

    // Kind of a Kubernetes resource, e.g.
    // "CustomResourceDefinition", "StorageClass", etc.
    string resource_kind = 2;
  }

  // Identifies the cluster-scoped resources to restore from the Backup.
  message ClusterResourceRestoreScope {
    // A list of "types" of cluster-scoped resources to be restored from the
    // Backup.  An empty list means that NO cluster-scoped resources will be
    // restored. Note that Namespaces and PersistentVolume restoration is
    // handled separately and is not governed by this field.
    repeated GroupKind selected_group_kinds = 1;
  }

  // A transformation rule to be applied against Kubernetes resources as they
  // are selected for restoration from a Backup. A rule contains both filtering
  // logic (which resources are subject to substitution) and substitution logic.
  message SubstitutionRule {
    // (Filtering parameter) Any resource subject to substitution must be
    // contained within one of the listed Kubernetes Namespace in the Backup.
    // If this field is not provided, no namespace filtering will be performed
    // (all resources in all Namespaces, including all cluster-scoped resources,
    // will be candidates for substitution).
    // To mix cluster-scoped and namespaced resources in the same rule, use an
    // empty string ("") as one of the target namespaces.
    repeated string target_namespaces = 1;

    // (Filtering parameter) Any resource subject to substitution must belong to
    // one of the listed "types".
    // If this field is not provided, no type filtering will be performed (all
    // resources of all types matching previous filtering parameters will be
    // candidates for substitution).
    repeated GroupKind target_group_kinds = 2;

    // This is a [JSONPath]
    // ([https://kubernetes.io/docs/reference/kubectl/jsonpath/)
    // expression that matches specific fields of candidate
    // resources and it operates as both a filtering parameter (resources that
    // are not matched with this expression will not be candidates for
    // substitution) as well as a field identifier (identifies exactly which
    // fields out of the candidate resources will be modified).
    string target_json_path = 3;

    // (Filtering parameter) This is a [regular expression]
    // (https://en.wikipedia.org/wiki/Regular_expression)
    // that is compared against the fields matched by the target_json_path
    // expression (and must also have passed the previous filters).
    // Substitution will not be performed against fields whose
    // value does not match this expression. If this field is NOT specified,
    // then ALL fields matched by the target_json_path expression will undergo
    // substitution. Note that an empty (e.g., "", rather than unspecified)
    // value for for this field will only match empty fields.
    string original_value_pattern = 4;

    // This is the new value to set for any fields that pass the filtering and
    // selection criteria. To remove a value from a Kubernetes resource, either
    // leave this field unspecified, or set it to the empty string ("").
    string new_value = 5;
  }

  // Defines how volume data should be restored
  enum VolumeDataRestorePolicy {
    // unspecified, default value
    VOLUME_DATA_RESTORE_POLICY_UNSPECIFIED = 0;

    // For each PVC to be restored, will create a new underlying volume (and PV)
    // from the corresponding VolumeBackup contained within the Backup.
    RESTORE_VOLUME_DATA_FROM_BACKUP = 1;

    // For each PVC to be restored, attempt to reuse the original PV contained
    // in the Backup (with its original underlying volume).  Note that option
    // is likely only usable when restoring a workload to its original cluster.
    REUSE_VOLUME_HANDLE_FROM_BACKUP = 2;

    // For each PVC to be restored, PVCs will be created without any particular
    // action to restore data.  In this case, the normal Kubernetes provisioning
    // logic would kick in, and this would likely result in either dynamically
    // provisioning blank PVs or binding to statically provisioned PVs.
    NO_VOLUME_DATA_RESTORATION = 3;
  }

  // Defines the behavior for handling the situation where cluster-scoped
  // resources being restored already exist in the target cluster.
  enum ClusterResourceConflictPolicy {
    // Unspecified. Only allowed if no cluster-scoped resources will be
    // restored.
    CLUSTER_RESOURCE_CONFLICT_POLICY_UNSPECIFIED = 0;

    // Do not attempt to restore the conflicting resource.
    USE_EXISTING_VERSION = 1;

    // Delete the existing version before re-creating it from the Backup.
    // Note that this is a dangerous option which could cause unintentional
    // data loss if used inappropriately - for example, deleting a CRD will
    // cause Kubernetes to delete all CRs of that type.
    USE_BACKUP_VERSION = 2;
  }

  // Defines the behavior for handling the situation where sets of namespaced
  // resources being restored already exist in the target cluster.
  enum NamespacedResourceRestoreMode {
    // Unspecified. Only allowed if no namespaced resources will be restored.
    NAMESPACED_RESOURCE_RESTORE_MODE_UNSPECIFIED = 0;

    // When conflicting top-level resources (either Namespaces or
    // ProtectedApplications, depending upon the scope) are encountered, this
    // will first trigger a delete of the conflicting resource AND ALL OF ITS
    // REFERENCED RESOURCES (e.g., all resources in the Namespace or all
    // resources referenced by the ProtectedApplication) before restoring the
    // resources from the Backup. This mode should only be used when you are
    // intending to revert some portion of a cluster to an earlier state.
    DELETE_AND_RESTORE = 1;

    // If conflicting top-level resources (either Namespaces or
    // ProtectedApplications, depending upon the scope) are encountered at the
    // beginning of a restore process, the Restore will fail.  If a conflict
    // occurs during the restore process itself (e.g., because an out of band
    // process creates conflicting resources), a conflict will be reported.
    FAIL_ON_CONFLICT = 2;
  }

  // Specifies the mechanism to be used to restore volume data.
  // Default: VOLUME_DATA_RESTORE_POLICY_UNSPECIFIED (will be treated as
  // NO_VOLUME_DATA_RESTORATION).
  VolumeDataRestorePolicy volume_data_restore_policy = 1;

  // Defines the behavior for handling the situation where cluster-scoped
  // resources being restored already exist in the target cluster. This MUST be
  // set to a value other than CLUSTER_RESOURCE_CONFLICT_POLICY_UNSPECIFIED if
  // cluster_resource_restore_scope is not empty.
  ClusterResourceConflictPolicy cluster_resource_conflict_policy = 2;

  // Defines the behavior for handling the situation where sets of namespaced
  // resources being restored already exist in the target cluster. This MUST be
  // set to a value other than NAMESPACED_RESOURCE_RESTORE_MODE_UNSPECIFIED if
  // any namespaced restoration is configured via
  // namespaced_resource_restore_scope .
  NamespacedResourceRestoreMode namespaced_resource_restore_mode = 3;

  // Identifies the cluster-scoped resources to restore from the Backup.
  // Not specifying it means NO cluster resource will be restored.
  ClusterResourceRestoreScope cluster_resource_restore_scope = 4;

  // Specifies the namespaced resources to restore from the Backup.
  // Only one of the entries may be specified. If not specified, NO namespaced
  // resources will be restored.
  oneof namespaced_resource_restore_scope {
    // Restore all namespaced resources in the Backup if set to "True".
    // Specifying this field to "False" is an error.
    bool all_namespaces = 5;

    // A list of selected Namespaces to restore from the Backup. The listed
    // Namespaces and all resources contained in them will be restored.
    Namespaces selected_namespaces = 6;

    // A list of selected ProtectedApplications to restore. The listed
    // ProtectedApplications and all the resources to which they refer will be
    // restored.
    NamespacedNames selected_applications = 7;
  }

  // A list of transformation rules to be applied against Kubernetes resources
  // as they are selected for restoration from a Backup. Rules are executed in
  // order defined - this order matters, as changes made by a rule may impact
  // the filtering logic of subsequent rules. An empty list means no
  // substitution will occur.
  repeated SubstitutionRule substitution_rules = 8;
}
