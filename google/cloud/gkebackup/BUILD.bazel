# This build file includes a target for the Ruby wrapper library for
# google-cloud-gke_backup.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for gkebackup.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "gkebackup_ruby_wrapper",
    srcs = ["//google/cloud/gkebackup/v1:gkebackup_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=gkebackup.googleapis.com",
        "ruby-cloud-api-shortname=gkebackup",
        "ruby-cloud-gem-name=google-cloud-gke_backup",
        "ruby-cloud-product-url=https://cloud.google.com/kubernetes-engine/docs/add-on/backup-for-gke/",
        "ruby-cloud-wrapper-of=v1:0.7",
    ],
    ruby_cloud_description = "Backup for GKE lets you protect, manage, and restore your containerized applications and data for stateful workloads running on Google Kubernetes Engine clusters.",
    ruby_cloud_title = "Backup for GKE",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-gkebackup-ruby",
    deps = [
        ":gkebackup_ruby_wrapper",
    ],
)
