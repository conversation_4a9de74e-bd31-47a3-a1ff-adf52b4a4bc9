// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/gkebackup/v1/backup.proto";
import "google/cloud/gkebackup/v1/backup_plan.proto";
import "google/cloud/gkebackup/v1/restore.proto";
import "google/cloud/gkebackup/v1/restore_plan.proto";
import "google/cloud/gkebackup/v1/volume.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeBackup.V1";
option go_package = "cloud.google.com/go/gkebackup/apiv1/gkebackuppb;gkebackuppb";
option java_multiple_files = true;
option java_outer_classname = "GKEBackupProto";
option java_package = "com.google.cloud.gkebackup.v1";
option php_namespace = "Google\\Cloud\\GkeBackup\\V1";
option ruby_package = "Google::Cloud::GkeBackup::V1";
option (google.api.resource_definition) = {
  type: "container.googleapis.com/Cluster"
  pattern: "projects/{project}/locations/{location}/clusters/{cluster}"
};
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};

// BackupForGKE allows Kubernetes administrators to configure, execute, and
// manage backup and restore operations for their GKE clusters.
service BackupForGKE {
  option (google.api.default_host) = "gkebackup.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new BackupPlan in a given location.
  rpc CreateBackupPlan(CreateBackupPlanRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/backupPlans"
      body: "backup_plan"
    };
    option (google.api.method_signature) = "parent,backup_plan,backup_plan_id";
    option (google.longrunning.operation_info) = {
      response_type: "BackupPlan"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists BackupPlans in a given location.
  rpc ListBackupPlans(ListBackupPlansRequest)
      returns (ListBackupPlansResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/backupPlans"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieve the details of a single BackupPlan.
  rpc GetBackupPlan(GetBackupPlanRequest) returns (BackupPlan) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupPlans/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update a BackupPlan.
  rpc UpdateBackupPlan(UpdateBackupPlanRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{backup_plan.name=projects/*/locations/*/backupPlans/*}"
      body: "backup_plan"
    };
    option (google.api.method_signature) = "backup_plan,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "BackupPlan"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes an existing BackupPlan.
  rpc DeleteBackupPlan(DeleteBackupPlanRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backupPlans/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a Backup for the given BackupPlan.
  rpc CreateBackup(CreateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/backupPlans/*}/backups"
      body: "backup"
    };
    option (google.api.method_signature) = "parent,backup,backup_id";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the Backups for a given BackupPlan.
  rpc ListBackups(ListBackupsRequest) returns (ListBackupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/backupPlans/*}/backups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieve the details of a single Backup.
  rpc GetBackup(GetBackupRequest) returns (Backup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupPlans/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update a Backup.
  rpc UpdateBackup(UpdateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{backup.name=projects/*/locations/*/backupPlans/*/backups/*}"
      body: "backup"
    };
    option (google.api.method_signature) = "backup,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes an existing Backup.
  rpc DeleteBackup(DeleteBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backupPlans/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the VolumeBackups for a given Backup.
  rpc ListVolumeBackups(ListVolumeBackupsRequest)
      returns (ListVolumeBackupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/backupPlans/*/backups/*}/volumeBackups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieve the details of a single VolumeBackup.
  rpc GetVolumeBackup(GetVolumeBackupRequest) returns (VolumeBackup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupPlans/*/backups/*/volumeBackups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new RestorePlan in a given location.
  rpc CreateRestorePlan(CreateRestorePlanRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/restorePlans"
      body: "restore_plan"
    };
    option (google.api.method_signature) =
        "parent,restore_plan,restore_plan_id";
    option (google.longrunning.operation_info) = {
      response_type: "RestorePlan"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists RestorePlans in a given location.
  rpc ListRestorePlans(ListRestorePlansRequest)
      returns (ListRestorePlansResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/restorePlans"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieve the details of a single RestorePlan.
  rpc GetRestorePlan(GetRestorePlanRequest) returns (RestorePlan) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/restorePlans/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update a RestorePlan.
  rpc UpdateRestorePlan(UpdateRestorePlanRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{restore_plan.name=projects/*/locations/*/restorePlans/*}"
      body: "restore_plan"
    };
    option (google.api.method_signature) = "restore_plan,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "RestorePlan"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes an existing RestorePlan.
  rpc DeleteRestorePlan(DeleteRestorePlanRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/restorePlans/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new Restore for the given RestorePlan.
  rpc CreateRestore(CreateRestoreRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/restorePlans/*}/restores"
      body: "restore"
    };
    option (google.api.method_signature) = "parent,restore,restore_id";
    option (google.longrunning.operation_info) = {
      response_type: "Restore"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the Restores for a given RestorePlan.
  rpc ListRestores(ListRestoresRequest) returns (ListRestoresResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/restorePlans/*}/restores"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the details of a single Restore.
  rpc GetRestore(GetRestoreRequest) returns (Restore) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/restorePlans/*/restores/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update a Restore.
  rpc UpdateRestore(UpdateRestoreRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{restore.name=projects/*/locations/*/restorePlans/*/restores/*}"
      body: "restore"
    };
    option (google.api.method_signature) = "restore,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Restore"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes an existing Restore.
  rpc DeleteRestore(DeleteRestoreRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/restorePlans/*/restores/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the VolumeRestores for a given Restore.
  rpc ListVolumeRestores(ListVolumeRestoresRequest)
      returns (ListVolumeRestoresResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/restorePlans/*/restores/*}/volumeRestores"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieve the details of a single VolumeRestore.
  rpc GetVolumeRestore(GetVolumeRestoreRequest) returns (VolumeRestore) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/restorePlans/*/restores/*/volumeRestores/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieve the link to the backupIndex.
  rpc GetBackupIndexDownloadUrl(GetBackupIndexDownloadUrlRequest)
      returns (GetBackupIndexDownloadUrlResponse) {
    option (google.api.http) = {
      get: "/v1/{backup=projects/*/locations/*/backupPlans/*/backups/*}:getBackupIndexDownloadUrl"
    };
    option (google.api.method_signature) = "backup";
  }
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for CreateBackupPlan.
message CreateBackupPlanRequest {
  // Required. The location within which to create the BackupPlan.
  // Format: `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The BackupPlan resource object to create.
  BackupPlan backup_plan = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The client-provided short name for the BackupPlan resource.
  // This name must:
  //
  // - be between 1 and 63 characters long (inclusive)
  // - consist of only lower-case ASCII letters, numbers, and dashes
  // - start with a lower-case letter
  // - end with a lower-case letter or number
  // - be unique within the set of BackupPlans in this location
  string backup_plan_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListBackupPlans.
message ListBackupPlansRequest {
  // Required. The location that contains the BackupPlans to list.
  // Format: `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The target number of results to return in a single response.
  // If not specified, a default value will be chosen by the service.
  // Note that the response may include a partial list and a caller should
  // only rely on the response's
  // [next_page_token][google.cloud.gkebackup.v1.ListBackupPlansResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value of
  // [next_page_token][google.cloud.gkebackup.v1.ListBackupPlansResponse.next_page_token]
  // received from a previous `ListBackupPlans` call.
  // Provide this to retrieve the subsequent page in a multi-page list of
  // results. When paginating, all other parameters provided to
  // `ListBackupPlans` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field match expression used to filter the results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field by which to sort the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListBackupPlans.
message ListBackupPlansResponse {
  // The list of BackupPlans matching the given criteria.
  repeated BackupPlan backup_plans = 1;

  // A token which may be sent as
  // [page_token][google.cloud.gkebackup.v1.ListBackupPlansRequest.page_token]
  // in a subsequent `ListBackupPlans` call to retrieve the next page of
  // results. If this field is omitted or empty, then there are no more results
  // to return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for GetBackupPlan.
message GetBackupPlanRequest {
  // Required. Fully qualified BackupPlan name.
  // Format: `projects/*/locations/*/backupPlans/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/BackupPlan"
    }
  ];
}

// Request message for UpdateBackupPlan.
message UpdateBackupPlanRequest {
  // Required. A new version of the BackupPlan resource that contains updated
  // fields. This may be sparsely populated if an `update_mask` is provided.
  BackupPlan backup_plan = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. This is used to specify the fields to be overwritten in the
  // BackupPlan targeted for update. The values for each of these
  // updated fields will be taken from the `backup_plan` provided
  // with this request. Field names are relative to the root of the resource
  // (e.g., `description`, `backup_config.include_volume_data`, etc.)
  // If no `update_mask` is provided, all fields in `backup_plan` will be
  // written to the target BackupPlan resource.
  // Note that OUTPUT_ONLY and IMMUTABLE fields in `backup_plan` are ignored
  // and are not used to update the target BackupPlan.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for DeleteBackupPlan.
message DeleteBackupPlanRequest {
  // Required. Fully qualified BackupPlan name.
  // Format: `projects/*/locations/*/backupPlans/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/BackupPlan"
    }
  ];

  // Optional. If provided, this value must match the current value of the
  // target BackupPlan's [etag][google.cloud.gkebackup.v1.BackupPlan.etag] field
  // or the request is rejected.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for CreateBackup.
message CreateBackupRequest {
  // Required. The BackupPlan within which to create the Backup.
  // Format: `projects/*/locations/*/backupPlans/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/BackupPlan"
    }
  ];

  // Optional. The Backup resource to create.
  Backup backup = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The client-provided short name for the Backup resource.
  // This name must:
  //
  // - be between 1 and 63 characters long (inclusive)
  // - consist of only lower-case ASCII letters, numbers, and dashes
  // - start with a lower-case letter
  // - end with a lower-case letter or number
  // - be unique within the set of Backups in this BackupPlan
  string backup_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for ListBackups.
message ListBackupsRequest {
  // Required. The BackupPlan that contains the Backups to list.
  // Format: `projects/*/locations/*/backupPlans/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/BackupPlan"
    }
  ];

  // Optional. The target number of results to return in a single response.
  // If not specified, a default value will be chosen by the service.
  // Note that the response may include a partial list and a caller should
  // only rely on the response's
  // [next_page_token][google.cloud.gkebackup.v1.ListBackupsResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value of
  // [next_page_token][google.cloud.gkebackup.v1.ListBackupsResponse.next_page_token]
  // received from a previous `ListBackups` call.
  // Provide this to retrieve the subsequent page in a multi-page list of
  // results. When paginating, all other parameters provided to
  // `ListBackups` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field match expression used to filter the results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field by which to sort the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListBackups.
message ListBackupsResponse {
  // The list of Backups matching the given criteria.
  repeated Backup backups = 1;

  // A token which may be sent as
  // [page_token][google.cloud.gkebackup.v1.ListBackupsRequest.page_token] in a
  // subsequent `ListBackups` call to retrieve the next page of results. If this
  // field is omitted or empty, then there are no more results to return.
  string next_page_token = 2;
}

// Request message for GetBackup.
message GetBackupRequest {
  // Required. Full name of the Backup resource.
  // Format: `projects/*/locations/*/backupPlans/*/backups/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/Backup"
    }
  ];
}

// Request message for UpdateBackup.
message UpdateBackupRequest {
  // Required. A new version of the Backup resource that contains updated
  // fields. This may be sparsely populated if an `update_mask` is provided.
  Backup backup = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. This is used to specify the fields to be overwritten in the
  // Backup targeted for update. The values for each of these
  // updated fields will be taken from the `backup_plan` provided
  // with this request. Field names are relative to the root of the resource.
  // If no `update_mask` is provided, all fields in `backup` will be
  // written to the target Backup resource.
  // Note that OUTPUT_ONLY and IMMUTABLE fields in `backup` are ignored
  // and are not used to update the target Backup.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for DeleteBackup.
message DeleteBackupRequest {
  // Required. Name of the Backup resource.
  // Format: `projects/*/locations/*/backupPlans/*/backups/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/Backup"
    }
  ];

  // Optional. If provided, this value must match the current value of the
  // target Backup's [etag][google.cloud.gkebackup.v1.Backup.etag] field or the
  // request is rejected.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, any VolumeBackups below this Backup will also be
  // deleted. Otherwise, the request will only succeed if the Backup has no
  // VolumeBackups.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for ListVolumeBackups.
message ListVolumeBackupsRequest {
  // Required. The Backup that contains the VolumeBackups to list.
  // Format: `projects/*/locations/*/backupPlans/*/backups/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/Backup"
    }
  ];

  // Optional. The target number of results to return in a single response.
  // If not specified, a default value will be chosen by the service.
  // Note that the response may include a partial list and a caller should
  // only rely on the response's
  // [next_page_token][google.cloud.gkebackup.v1.ListVolumeBackupsResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value of
  // [next_page_token][google.cloud.gkebackup.v1.ListVolumeBackupsResponse.next_page_token]
  // received from a previous `ListVolumeBackups` call.
  // Provide this to retrieve the subsequent page in a multi-page list of
  // results. When paginating, all other parameters provided to
  // `ListVolumeBackups` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field match expression used to filter the results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field by which to sort the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListVolumeBackups.
message ListVolumeBackupsResponse {
  // The list of VolumeBackups matching the given criteria.
  repeated VolumeBackup volume_backups = 1;

  // A token which may be sent as
  // [page_token][google.cloud.gkebackup.v1.ListVolumeBackupsRequest.page_token]
  // in a subsequent `ListVolumeBackups` call to retrieve the next page of
  // results. If this field is omitted or empty, then there are no more results
  // to return.
  string next_page_token = 2;
}

// Request message for GetVolumeBackup.
message GetVolumeBackupRequest {
  // Required. Full name of the VolumeBackup resource.
  // Format: `projects/*/locations/*/backupPlans/*/backups/*/volumeBackups/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/VolumeBackup"
    }
  ];
}

// Request message for CreateRestorePlan.
message CreateRestorePlanRequest {
  // Required. The location within which to create the RestorePlan.
  // Format: `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The RestorePlan resource object to create.
  RestorePlan restore_plan = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The client-provided short name for the RestorePlan resource.
  // This name must:
  //
  // - be between 1 and 63 characters long (inclusive)
  // - consist of only lower-case ASCII letters, numbers, and dashes
  // - start with a lower-case letter
  // - end with a lower-case letter or number
  // - be unique within the set of RestorePlans in this location
  string restore_plan_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListRestorePlans.
message ListRestorePlansRequest {
  // Required. The location that contains the RestorePlans to list.
  // Format: `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. The target number of results to return in a single response.
  // If not specified, a default value will be chosen by the service.
  // Note that the response may include a partial list and a caller should
  // only rely on the response's
  // [next_page_token][google.cloud.gkebackup.v1.ListRestorePlansResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value of
  // [next_page_token][google.cloud.gkebackup.v1.ListRestorePlansResponse.next_page_token]
  // received from a previous `ListRestorePlans` call.
  // Provide this to retrieve the subsequent page in a multi-page list of
  // results. When paginating, all other parameters provided to
  // `ListRestorePlans` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field match expression used to filter the results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field by which to sort the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListRestorePlans.
message ListRestorePlansResponse {
  // The list of RestorePlans matching the given criteria.
  repeated RestorePlan restore_plans = 1;

  // A token which may be sent as
  // [page_token][google.cloud.gkebackup.v1.ListRestorePlansRequest.page_token]
  // in a subsequent `ListRestorePlans` call to retrieve the next page of
  // results. If this field is omitted or empty, then there are no more results
  // to return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for GetRestorePlan.
message GetRestorePlanRequest {
  // Required. Fully qualified RestorePlan name.
  // Format: `projects/*/locations/*/restorePlans/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/RestorePlan"
    }
  ];
}

// Request message for UpdateRestorePlan.
message UpdateRestorePlanRequest {
  // Required. A new version of the RestorePlan resource that contains updated
  // fields. This may be sparsely populated if an `update_mask` is provided.
  RestorePlan restore_plan = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. This is used to specify the fields to be overwritten in the
  // RestorePlan targeted for update. The values for each of these
  // updated fields will be taken from the `restore_plan` provided
  // with this request. Field names are relative to the root of the resource.
  // If no `update_mask` is provided, all fields in `restore_plan` will be
  // written to the target RestorePlan resource.
  // Note that OUTPUT_ONLY and IMMUTABLE fields in `restore_plan` are ignored
  // and are not used to update the target RestorePlan.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for DeleteRestorePlan.
message DeleteRestorePlanRequest {
  // Required. Fully qualified RestorePlan name.
  // Format: `projects/*/locations/*/restorePlans/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/RestorePlan"
    }
  ];

  // Optional. If provided, this value must match the current value of the
  // target RestorePlan's [etag][google.cloud.gkebackup.v1.RestorePlan.etag]
  // field or the request is rejected.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, any Restores below this RestorePlan will also be
  // deleted. Otherwise, the request will only succeed if the RestorePlan has no
  // Restores.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for CreateRestore.
message CreateRestoreRequest {
  // Required. The RestorePlan within which to create the Restore.
  // Format: `projects/*/locations/*/restorePlans/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/RestorePlan"
    }
  ];

  // Required. The restore resource to create.
  Restore restore = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The client-provided short name for the Restore resource.
  // This name must:
  //
  // - be between 1 and 63 characters long (inclusive)
  // - consist of only lower-case ASCII letters, numbers, and dashes
  // - start with a lower-case letter
  // - end with a lower-case letter or number
  // - be unique within the set of Restores in this RestorePlan.
  string restore_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ListRestores.
message ListRestoresRequest {
  // Required. The RestorePlan that contains the Restores to list.
  // Format: `projects/*/locations/*/restorePlans/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/RestorePlan"
    }
  ];

  // Optional. The target number of results to return in a single response.
  // If not specified, a default value will be chosen by the service.
  // Note that the response may include a partial list and a caller should
  // only rely on the response's
  // [next_page_token][google.cloud.gkebackup.v1.ListRestoresResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value of
  // [next_page_token][google.cloud.gkebackup.v1.ListRestoresResponse.next_page_token]
  // received from a previous `ListRestores` call.
  // Provide this to retrieve the subsequent page in a multi-page list of
  // results. When paginating, all other parameters provided to `ListRestores`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field match expression used to filter the results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field by which to sort the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListRestores.
message ListRestoresResponse {
  // The list of Restores matching the given criteria.
  repeated Restore restores = 1;

  // A token which may be sent as
  // [page_token][google.cloud.gkebackup.v1.ListRestoresRequest.page_token] in a
  // subsequent `ListRestores` call to retrieve the next page of results. If
  // this field is omitted or empty, then there are no more results to return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for GetRestore.
message GetRestoreRequest {
  // Required. Name of the restore resource.
  // Format: `projects/*/locations/*/restorePlans/*/restores/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/Restore"
    }
  ];
}

// Request message for UpdateRestore.
message UpdateRestoreRequest {
  // Required. A new version of the Restore resource that contains updated
  // fields. This may be sparsely populated if an `update_mask` is provided.
  Restore restore = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. This is used to specify the fields to be overwritten in the
  // Restore targeted for update. The values for each of these
  // updated fields will be taken from the `restore` provided
  // with this request. Field names are relative to the root of the resource.
  // If no `update_mask` is provided, all fields in `restore` will be
  // written to the target Restore resource.
  // Note that OUTPUT_ONLY and IMMUTABLE fields in `restore` are ignored
  // and are not used to update the target Restore.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for DeleteRestore.
message DeleteRestoreRequest {
  // Required. Full name of the Restore
  // Format: `projects/*/locations/*/restorePlans/*/restores/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/Restore"
    }
  ];

  // Optional. If provided, this value must match the current value of the
  // target Restore's [etag][google.cloud.gkebackup.v1.Restore.etag] field or
  // the request is rejected.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, any VolumeRestores below this restore will also
  // be deleted. Otherwise, the request will only succeed if the restore has no
  // VolumeRestores.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for ListVolumeRestores.
message ListVolumeRestoresRequest {
  // Required. The Restore that contains the VolumeRestores to list.
  // Format: `projects/*/locations/*/restorePlans/*/restores/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/Restore"
    }
  ];

  // Optional. The target number of results to return in a single response.
  // If not specified, a default value will be chosen by the service.
  // Note that the response may include a partial list and a caller should
  // only rely on the response's
  // [next_page_token][google.cloud.gkebackup.v1.ListVolumeRestoresResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value of
  // [next_page_token][google.cloud.gkebackup.v1.ListVolumeRestoresResponse.next_page_token]
  // received from a previous `ListVolumeRestores` call.
  // Provide this to retrieve the subsequent page in a multi-page list of
  // results. When paginating, all other parameters provided to
  // `ListVolumeRestores` must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field match expression used to filter the results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field by which to sort the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListVolumeRestores.
message ListVolumeRestoresResponse {
  // The list of VolumeRestores matching the given criteria.
  repeated VolumeRestore volume_restores = 1;

  // A token which may be sent as
  // [page_token][google.cloud.gkebackup.v1.ListVolumeRestoresRequest.page_token]
  // in a subsequent `ListVolumeRestores` call to retrieve the next page of
  // results. If this field is omitted or empty, then there are no more results
  // to return.
  string next_page_token = 2;
}

// Request message for GetVolumeRestore.
message GetVolumeRestoreRequest {
  // Required. Full name of the VolumeRestore resource.
  // Format: `projects/*/locations/*/restorePlans/*/restores/*/volumeRestores/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/VolumeRestore"
    }
  ];
}

// Request message for GetBackupIndexDownloadUrl.
message GetBackupIndexDownloadUrlRequest {
  // Required. Full name of Backup resource.
  // Format:
  // projects/{project}/locations/{location}/backupPlans/{backup_plan}/backups/{backup}
  string backup = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/Backup"
    }
  ];
}

// Response message for GetBackupIndexDownloadUrl.
message GetBackupIndexDownloadUrlResponse {
  string signed_url = 1 [(google.api.field_behavior) = REQUIRED];
}
