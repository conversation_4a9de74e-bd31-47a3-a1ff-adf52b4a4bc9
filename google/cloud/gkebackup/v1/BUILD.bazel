# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "gkebackup_proto",
    srcs = [
        "backup.proto",
        "backup_plan.proto",
        "common.proto",
        "gkebackup.proto",
        "restore.proto",
        "restore_plan.proto",
        "volume.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:date_proto",
        "//google/type:dayofweek_proto",
        "//google/type:timeofday_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "gkebackup_proto_with_info",
    deps = [
        ":gkebackup_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "gkebackup_java_proto",
    deps = [":gkebackup_proto"],
)

java_grpc_library(
    name = "gkebackup_java_grpc",
    srcs = [":gkebackup_proto"],
    deps = [":gkebackup_java_proto"],
)

java_gapic_library(
    name = "gkebackup_java_gapic",
    srcs = [":gkebackup_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "gkebackup_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "gkebackup_v1.yaml",
    test_deps = [
        ":gkebackup_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":gkebackup_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "gkebackup_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.gkebackup.v1.BackupForGKEClientHttpJsonTest",
        "com.google.cloud.gkebackup.v1.BackupForGKEClientTest",
    ],
    runtime_deps = [":gkebackup_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-gkebackup-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":gkebackup_java_gapic",
        ":gkebackup_java_grpc",
        ":gkebackup_java_proto",
        ":gkebackup_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "gkebackup_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/gkebackup/apiv1/gkebackuppb",
    protos = [":gkebackup_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:date_go_proto",
        "//google/type:dayofweek_go_proto",
        "//google/type:timeofday_go_proto",
    ],
)

go_gapic_library(
    name = "gkebackup_go_gapic",
    srcs = [":gkebackup_proto_with_info"],
    grpc_service_config = "gkebackup_grpc_service_config.json",
    importpath = "cloud.google.com/go/gkebackup/apiv1;gkebackup",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "gkebackup_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gkebackup_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-gkebackup-v1-go",
    deps = [
        ":gkebackup_go_gapic",
        ":gkebackup_go_gapic_srcjar-metadata.srcjar",
        ":gkebackup_go_gapic_srcjar-snippets.srcjar",
        ":gkebackup_go_gapic_srcjar-test.srcjar",
        ":gkebackup_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "gkebackup_py_gapic",
    srcs = [":gkebackup_proto"],
    grpc_service_config = "gkebackup_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=gke_backup",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-gke-backup",
    ],
    rest_numeric_enums = True,
    service_yaml = "gkebackup_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "gkebackup_py_gapic_test",
    srcs = [
        "gkebackup_py_gapic_pytest.py",
        "gkebackup_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":gkebackup_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "gkebackup-v1-py",
    deps = [
        ":gkebackup_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "gkebackup_php_proto",
    deps = [":gkebackup_proto"],
)

php_gapic_library(
    name = "gkebackup_php_gapic",
    srcs = [":gkebackup_proto_with_info"],
    grpc_service_config = "gkebackup_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "gkebackup_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gkebackup_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-gkebackup-v1-php",
    deps = [
        ":gkebackup_php_gapic",
        ":gkebackup_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "gkebackup_nodejs_gapic",
    package_name = "@google-cloud/gke-backup",
    src = ":gkebackup_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "gkebackup_grpc_service_config.json",
    package = "google.cloud.gkebackup.v1",
    rest_numeric_enums = True,
    service_yaml = "gkebackup_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "gkebackup-v1-nodejs",
    deps = [
        ":gkebackup_nodejs_gapic",
        ":gkebackup_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "gkebackup_ruby_proto",
    deps = [":gkebackup_proto"],
)

ruby_grpc_library(
    name = "gkebackup_ruby_grpc",
    srcs = [":gkebackup_proto"],
    deps = [":gkebackup_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "gkebackup_ruby_gapic",
    srcs = [":gkebackup_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=gkebackup.googleapis.com",
        "ruby-cloud-api-shortname=gkebackup",
        "ruby-cloud-gem-name=google-cloud-gke_backup-v1",
        "ruby-cloud-product-url=https://cloud.google.com/kubernetes-engine/docs/add-on/backup-for-gke/",
    ],
    grpc_service_config = "gkebackup_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Backup for GKE lets you protect, manage, and restore your containerized applications and data for stateful workloads running on Google Kubernetes Engine clusters.",
    ruby_cloud_title = "Backup for GKE V1",
    service_yaml = "gkebackup_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gkebackup_ruby_grpc",
        ":gkebackup_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-gkebackup-v1-ruby",
    deps = [
        ":gkebackup_ruby_gapic",
        ":gkebackup_ruby_grpc",
        ":gkebackup_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "gkebackup_csharp_proto",
    extra_opts = [],
    deps = [":gkebackup_proto"],
)

csharp_grpc_library(
    name = "gkebackup_csharp_grpc",
    srcs = [":gkebackup_proto"],
    deps = [":gkebackup_csharp_proto"],
)

csharp_gapic_library(
    name = "gkebackup_csharp_gapic",
    srcs = [":gkebackup_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "gkebackup_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "gkebackup_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gkebackup_csharp_grpc",
        ":gkebackup_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-gkebackup-v1-csharp",
    deps = [
        ":gkebackup_csharp_gapic",
        ":gkebackup_csharp_grpc",
        ":gkebackup_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "gkebackup_cc_proto",
    deps = [":gkebackup_proto"],
)

cc_grpc_library(
    name = "gkebackup_cc_grpc",
    srcs = [":gkebackup_proto"],
    grpc_only = True,
    deps = [":gkebackup_cc_proto"],
)
