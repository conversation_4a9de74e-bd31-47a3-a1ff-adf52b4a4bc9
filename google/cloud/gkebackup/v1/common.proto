// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.GkeBackup.V1";
option go_package = "cloud.google.com/go/gkebackup/apiv1/gkebackuppb;gkebackuppb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.cloud.gkebackup.v1";
option php_namespace = "Google\\Cloud\\GkeBackup\\V1";
option ruby_package = "Google::Cloud::GkeBackup::V1";

// A list of Kubernetes Namespaces
message Namespaces {
  // Optional. A list of Kubernetes Namespaces
  repeated string namespaces = 1 [(google.api.field_behavior) = OPTIONAL];
}

// A reference to a namespaced resource in Kubernetes.
message NamespacedName {
  // Optional. The Namespace of the Kubernetes resource.
  string namespace = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the Kubernetes resource.
  string name = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A list of namespaced Kubernetes resources.
message NamespacedNames {
  // Optional. A list of namespaced Kubernetes resources.
  repeated NamespacedName namespaced_names = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// Defined a customer managed encryption key that will be used to encrypt Backup
// artifacts.
message EncryptionKey {
  // Optional. Google Cloud KMS encryption key. Format:
  // `projects/*/locations/*/keyRings/*/cryptoKeys/*`
  string gcp_kms_encryption_key = 1 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];
}

// Message to encapsulate VolumeType enum.
message VolumeTypeEnum {
  // Supported volume types.
  enum VolumeType {
    // Default
    VOLUME_TYPE_UNSPECIFIED = 0;

    // Compute Engine Persistent Disk volume
    GCE_PERSISTENT_DISK = 1;
  }
}
