{"methodConfig": [{"name": [{"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "ListBackupPlans"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "GetBackupPlan"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "ListBackups"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "GetBackup"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "ListVolumeBackups"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "GetVolumeBackup"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "ListRestorePlans"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "GetRestorePlan"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "ListRestores"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "GetRestore"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "ListVolumeRestores"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "GetVolumeRestore"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "GetBackupIndexDownloadUrl"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "UpdateBackupPlan"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "UpdateBackup"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "UpdateRestorePlan"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "UpdateRestore"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "DeleteBackupPlan"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "DeleteRestorePlan"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "DeleteRestore"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "CreateBackup"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "CreateRestorePlan"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "CreateRestore"}], "timeout": "120s"}, {"name": [{"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "CreateBackupPlan"}, {"service": "google.cloud.gkebackup.v1.BackupForGKE", "method": "DeleteBackup"}], "timeout": "300s"}]}