// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/gkebackup/v1/common.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeBackup.V1";
option go_package = "cloud.google.com/go/gkebackup/apiv1/gkebackuppb;gkebackuppb";
option java_multiple_files = true;
option java_outer_classname = "BackupProto";
option java_package = "com.google.cloud.gkebackup.v1";
option php_namespace = "Google\\Cloud\\GkeBackup\\V1";
option ruby_package = "Google::Cloud::GkeBackup::V1";

// Represents a request to perform a single point-in-time capture of
// some portion of the state of a GKE cluster, the record of the backup
// operation itself, and an anchor for the underlying artifacts that
// comprise the Backup (the config backup and VolumeBackups).
message Backup {
  option (google.api.resource) = {
    type: "gkebackup.googleapis.com/Backup"
    pattern: "projects/{project}/locations/{location}/backupPlans/{backup_plan}/backups/{backup}"
  };

  // Information about the GKE cluster from which this Backup was created.
  message ClusterMetadata {
    // Output only. The source cluster from which this Backup was created.
    // Valid formats:
    //
    //   - `projects/*/locations/*/clusters/*`
    //   - `projects/*/zones/*/clusters/*`
    //
    // This is inherited from the parent BackupPlan's
    // [cluster][google.cloud.gkebackup.v1.BackupPlan.cluster] field.
    string cluster = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The Kubernetes server version of the source cluster.
    string k8s_version = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A list of the Backup for GKE CRD versions found in the
    // cluster.
    map<string, string> backup_crd_versions = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Platform-specific version
    oneof platform_version {
      // Output only. GKE version
      string gke_version = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Anthos version
      string anthos_version = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
    }
  }

  // State
  enum State {
    // The Backup resource is in the process of being created.
    STATE_UNSPECIFIED = 0;

    // The Backup resource has been created and the associated BackupJob
    // Kubernetes resource has been injected into the source cluster.
    CREATING = 1;

    // The gkebackup agent in the cluster has begun executing the backup
    // operation.
    IN_PROGRESS = 2;

    // The backup operation has completed successfully.
    SUCCEEDED = 3;

    // The backup operation has failed.
    FAILED = 4;

    // This Backup resource (and its associated artifacts) is in the process
    // of being deleted.
    DELETING = 5;
  }

  // Output only. The fully qualified name of the Backup.
  // `projects/*/locations/*/backupPlans/*/backups/*`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server generated global unique identifier of
  // [UUID4](https://en.wikipedia.org/wiki/Universally_unique_identifier)
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this Backup resource was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this Backup resource was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This flag indicates whether this Backup resource was created
  // manually by a user or via a schedule in the BackupPlan. A value of True
  // means that the Backup was created manually.
  bool manual = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A set of custom labels supplied by user.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum age for this Backup (in days). If this field is set to a
  // non-zero value, the Backup will be "locked" against deletion (either manual
  // or automatic deletion) for the number of days provided (measured from the
  // creation time of the Backup).  MUST be an integer value between 0-90
  // (inclusive).
  //
  // Defaults to parent BackupPlan's
  // [backup_delete_lock_days][google.cloud.gkebackup.v1.BackupPlan.RetentionPolicy.backup_delete_lock_days]
  // setting and may only be increased
  // (either at creation time or in a subsequent update).
  int32 delete_lock_days = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time at which an existing delete lock will expire for this
  // backup (calculated from create_time +
  // [delete_lock_days][google.cloud.gkebackup.v1.Backup.delete_lock_days]).
  google.protobuf.Timestamp delete_lock_expire_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The age (in days) after which this Backup will be automatically
  // deleted. Must be an integer value >= 0:
  //
  // - If 0, no automatic deletion will occur for this Backup.
  // - If not 0, this must be >=
  // [delete_lock_days][google.cloud.gkebackup.v1.Backup.delete_lock_days] and
  // <= 365.
  //
  // Once a Backup is created, this value may only be increased.
  //
  // Defaults to the parent BackupPlan's
  // [backup_retain_days][google.cloud.gkebackup.v1.BackupPlan.RetentionPolicy.backup_retain_days]
  // value.
  int32 retain_days = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time at which this Backup will be automatically deleted
  // (calculated from create_time +
  // [retain_days][google.cloud.gkebackup.v1.Backup.retain_days]).
  google.protobuf.Timestamp retain_expire_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The customer managed encryption key that was used to encrypt
  // the Backup's artifacts.  Inherited from the parent BackupPlan's
  // [encryption_key][google.cloud.gkebackup.v1.BackupPlan.BackupConfig.encryption_key]
  // value.
  EncryptionKey encryption_key = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Defines the "scope" of the Backup - which namespaced resources in the
  // cluster were included in the Backup.  Inherited from the parent
  // BackupPlan's
  // [backup_scope][google.cloud.gkebackup.v1.BackupPlan.BackupConfig.backup_scope]
  // value.
  oneof backup_scope {
    // Output only. If True, all namespaces were included in the Backup.
    bool all_namespaces = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. If set, the list of namespaces that were included in the
    // Backup.
    Namespaces selected_namespaces = 13
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. If set, the list of ProtectedApplications whose resources
    // were included in the Backup.
    NamespacedNames selected_applications = 14
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Whether or not the Backup contains volume data.  Controlled by
  // the parent BackupPlan's
  // [include_volume_data][google.cloud.gkebackup.v1.BackupPlan.BackupConfig.include_volume_data]
  // value.
  bool contains_volume_data = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether or not the Backup contains Kubernetes Secrets.
  // Controlled by the parent BackupPlan's
  // [include_secrets][google.cloud.gkebackup.v1.BackupPlan.BackupConfig.include_secrets]
  // value.
  bool contains_secrets = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Information about the GKE cluster from which this Backup was
  // created.
  ClusterMetadata cluster_metadata = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state of the Backup
  State state = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable description of why the backup is in the current
  // `state`.
  string state_reason = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Completion time of the Backup
  google.protobuf.Timestamp complete_time = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total number of Kubernetes resources included in the
  // Backup.
  int32 resource_count = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total number of volume backups contained in the Backup.
  int32 volume_count = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total size of the Backup in bytes = config backup size +
  // sum(volume backup sizes)
  int64 size_bytes = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. `etag` is used for optimistic concurrency control as a way to
  // help prevent simultaneous updates of a backup from overwriting each other.
  // It is strongly suggested that systems make use of the `etag` in the
  // read-modify-write cycle to perform backup updates in order to avoid
  // race conditions: An `etag` is returned in the response to `GetBackup`,
  // and systems are expected to put that etag in the request to
  // `UpdateBackup` or `DeleteBackup` to ensure that their change will be
  // applied to the same version of the resource.
  string etag = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User specified descriptive string for this Backup.
  string description = 25 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The total number of Kubernetes Pods contained in the Backup.
  int32 pod_count = 26 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The size of the config backup in bytes.
  int64 config_backup_size_bytes = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If false, Backup will fail when Backup for GKE detects
  // Kubernetes configuration that is non-standard or
  // requires additional setup to restore.
  //
  // Inherited from the parent BackupPlan's
  // [permissive_mode][google.cloud.gkebackup.v1.BackupPlan.BackupConfig.permissive_mode]
  // value.
  bool permissive_mode = 28 [(google.api.field_behavior) = OUTPUT_ONLY];
}
