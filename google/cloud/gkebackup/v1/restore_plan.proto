// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/gkebackup/v1/restore.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeBackup.V1";
option go_package = "cloud.google.com/go/gkebackup/apiv1/gkebackuppb;gkebackuppb";
option java_multiple_files = true;
option java_outer_classname = "RestorePlanProto";
option java_package = "com.google.cloud.gkebackup.v1";
option php_namespace = "Google\\Cloud\\GkeBackup\\V1";
option ruby_package = "Google::Cloud::GkeBackup::V1";

// The configuration of a potential series of Restore operations to be performed
// against Backups belong to a particular BackupPlan.
message RestorePlan {
  option (google.api.resource) = {
    type: "gkebackup.googleapis.com/RestorePlan"
    pattern: "projects/{project}/locations/{location}/restorePlans/{restore_plan}"
  };

  // State
  enum State {
    // Default first value for Enums.
    STATE_UNSPECIFIED = 0;

    // Waiting for cluster state to be RUNNING.
    CLUSTER_PENDING = 1;

    // The RestorePlan has successfully been created and is ready for Restores.
    READY = 2;

    // RestorePlan creation has failed.
    FAILED = 3;

    // The RestorePlan is in the process of being deleted.
    DELETING = 4;
  }

  // Output only. The full name of the RestorePlan resource.
  // Format: `projects/*/locations/*/restorePlans/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server generated global unique identifier of
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier) format.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this RestorePlan resource was
  // created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this RestorePlan resource was last
  // updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User specified descriptive string for this RestorePlan.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Required. Immutable. A reference to the
  // [BackupPlan][google.cloud.gkebackup.v1.BackupPlan] from which Backups may
  // be used as the source for Restores created via this RestorePlan. Format:
  // `projects/*/locations/*/backupPlans/*`.
  string backup_plan = 6 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkebackup.googleapis.com/BackupPlan"
    }
  ];

  // Required. Immutable. The target cluster into which Restores created via
  // this RestorePlan will restore data. NOTE: the cluster's region must be the
  // same as the RestorePlan. Valid formats:
  //
  //   - `projects/*/locations/*/clusters/*`
  //   - `projects/*/zones/*/clusters/*`
  string cluster = 7 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "container.googleapis.com/Cluster"
    }
  ];

  // Required. Configuration of Restores created via this RestorePlan.
  RestoreConfig restore_config = 8 [(google.api.field_behavior) = REQUIRED];

  // Optional. A set of custom labels supplied by user.
  map<string, string> labels = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. `etag` is used for optimistic concurrency control as a way to
  // help prevent simultaneous updates of a restore from overwriting each other.
  // It is strongly suggested that systems make use of the `etag` in the
  // read-modify-write cycle to perform restore updates in order to avoid
  // race conditions: An `etag` is returned in the response to `GetRestorePlan`,
  // and systems are expected to put that etag in the request to
  // `UpdateRestorePlan` or `DeleteRestorePlan` to ensure that their change
  // will be applied to the same version of the resource.
  string etag = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the RestorePlan. This State field reflects the
  // various stages a RestorePlan can be in
  // during the Create operation.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable description of why RestorePlan is in the
  // current `state`
  string state_reason = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}
