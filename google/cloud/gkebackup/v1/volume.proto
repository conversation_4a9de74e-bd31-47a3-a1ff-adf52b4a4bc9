// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/gkebackup/v1/common.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeBackup.V1";
option go_package = "cloud.google.com/go/gkebackup/apiv1/gkebackuppb;gkebackuppb";
option java_multiple_files = true;
option java_outer_classname = "VolumeProto";
option java_package = "com.google.cloud.gkebackup.v1";
option php_namespace = "Google\\Cloud\\GkeBackup\\V1";
option ruby_package = "Google::Cloud::GkeBackup::V1";

// Represents the backup of a specific persistent volume as a component of a
// Backup - both the record of the operation and a pointer to the underlying
// storage-specific artifacts.
message VolumeBackup {
  option (google.api.resource) = {
    type: "gkebackup.googleapis.com/VolumeBackup"
    pattern: "projects/{project}/locations/{location}/backupPlans/{backup_plan}/backups/{backup}/volumeBackups/{volume_backup}"
  };

  // Identifies the format used for the volume backup.
  enum VolumeBackupFormat {
    // Default value, not specified.
    VOLUME_BACKUP_FORMAT_UNSPECIFIED = 0;

    // Compute Engine Persistent Disk snapshot based volume backup.
    GCE_PERSISTENT_DISK = 1;
  }

  // The current state of a VolumeBackup
  enum State {
    // This is an illegal state and should not be encountered.
    STATE_UNSPECIFIED = 0;

    // A volume for the backup was identified and backup process is about to
    // start.
    CREATING = 1;

    // The volume backup operation has begun and is in the initial "snapshot"
    // phase of the process. Any defined ProtectedApplication "pre" hooks will
    // be executed before entering this state and "post" hooks will be executed
    // upon leaving this state.
    SNAPSHOTTING = 2;

    // The snapshot phase of the volume backup operation has completed and
    // the snapshot is now being uploaded to backup storage.
    UPLOADING = 3;

    // The volume backup operation has completed successfully.
    SUCCEEDED = 4;

    // The volume backup operation has failed.
    FAILED = 5;

    // This VolumeBackup resource (and its associated artifacts) is in the
    // process of being deleted.
    DELETING = 6;
  }

  // Output only. The full name of the VolumeBackup resource.
  // Format: `projects/*/locations/*/backupPlans/*/backups/*/volumeBackups/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server generated global unique identifier of
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier) format.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this VolumeBackup resource was
  // created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this VolumeBackup resource was last
  // updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A reference to the source Kubernetes PVC from which this
  // VolumeBackup was created.
  NamespacedName source_pvc = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A storage system-specific opaque handle to the underlying
  // volume backup.
  string volume_backup_handle = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The format used for the volume backup.
  VolumeBackupFormat format = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The aggregate size of the underlying artifacts associated with
  // this VolumeBackup in the backup storage. This may change over time when
  // multiple backups of the same volume share the same backup storage
  // location. In particular, this is likely to increase in size when
  // the immediately preceding backup of the same volume is deleted.
  int64 storage_bytes = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The minimum size of the disk to which this VolumeBackup can be
  // restored.
  int64 disk_size_bytes = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the associated underlying volume backup
  // operation completed.
  google.protobuf.Timestamp complete_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of this VolumeBackup.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A human readable message explaining why the VolumeBackup is in
  // its current state.
  string state_message = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. `etag` is used for optimistic concurrency control as a way to
  // help prevent simultaneous updates of a volume backup from overwriting each
  // other. It is strongly suggested that systems make use of the `etag` in the
  // read-modify-write cycle to perform volume backup updates in order to avoid
  // race conditions.
  string etag = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the operation of restoring a volume from a VolumeBackup.
message VolumeRestore {
  option (google.api.resource) = {
    type: "gkebackup.googleapis.com/VolumeRestore"
    pattern: "projects/{project}/locations/{location}/restorePlans/{restore_plan}/restores/{restore}/volumeRestores/{volume_restore}"
  };

  // Supported volume types.
  enum VolumeType {
    // Default
    VOLUME_TYPE_UNSPECIFIED = 0;

    // Compute Engine Persistent Disk volume
    GCE_PERSISTENT_DISK = 1;
  }

  // The current state of a VolumeRestore
  enum State {
    // This is an illegal state and should not be encountered.
    STATE_UNSPECIFIED = 0;

    // A volume for the restore was identified and restore process is about to
    // start.
    CREATING = 1;

    // The volume is currently being restored.
    RESTORING = 2;

    // The volume has been successfully restored.
    SUCCEEDED = 3;

    // The volume restoration process failed.
    FAILED = 4;

    // This VolumeRestore resource is in the process of being deleted.
    DELETING = 5;
  }

  // Output only. Full name of the VolumeRestore resource.
  // Format: `projects/*/locations/*/restorePlans/*/restores/*/volumeRestores/*`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server generated global unique identifier of
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier) format.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this VolumeRestore resource was
  // created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this VolumeRestore resource was last
  // updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The full name of the VolumeBackup from which the volume will
  // be restored. Format:
  // `projects/*/locations/*/backupPlans/*/backups/*/volumeBackups/*`.
  string volume_backup = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The reference to the target Kubernetes PVC to be restored.
  NamespacedName target_pvc = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A storage system-specific opaque handler to the underlying
  // volume created for the target PVC from the volume backup.
  string volume_handle = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of volume provisioned
  VolumeType volume_type = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the associated underlying volume
  // restoration completed.
  google.protobuf.Timestamp complete_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of this VolumeRestore.
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A human readable message explaining why the VolumeRestore is
  // in its current state.
  string state_message = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. `etag` is used for optimistic concurrency control as a way to
  // help prevent simultaneous updates of a volume restore from overwriting each
  // other. It is strongly suggested that systems make use of the `etag` in the
  // read-modify-write cycle to perform volume restore updates in order to avoid
  // race conditions.
  string etag = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}
