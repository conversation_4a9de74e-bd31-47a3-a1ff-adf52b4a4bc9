// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.runtimeconfig.v1beta1;

import "google/api/annotations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.RuntimeConfig.V1Beta1";
option go_package = "cloud.google.com/go/runtimeconfig/apiv1beta1/runtimeconfigpb;runtimeconfigpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.runtimeconfig.v1beta1";
option php_namespace = "Google\\Cloud\\RuntimeConfig\\V1beta1";

// A RuntimeConfig resource is the primary resource in the Cloud RuntimeConfig
// service. A RuntimeConfig resource consists of metadata and a hierarchy of
// variables.
message RuntimeConfig {
  // The resource name of a runtime config. The name must have the format:
  //
  //     projects/[PROJECT_ID]/configs/[CONFIG_NAME]
  //
  // The `[PROJECT_ID]` must be a valid project ID, and `[CONFIG_NAME]` is an
  // arbitrary name that matches RFC 1035 segment specification. The length of
  // `[CONFIG_NAME]` must be less than 64 bytes.
  //
  // You pick the RuntimeConfig resource name, but the server will validate that
  // the name adheres to this format. After you create the resource, you cannot
  // change the resource's name.
  string name = 1;

  // An optional description of the RuntimeConfig object.
  string description = 2;
}

// Describes a single variable within a RuntimeConfig resource.
// The name denotes the hierarchical variable name. For example,
// `ports/serving_port` is a valid variable name. The variable value is an
// opaque string and only leaf variables can have values (that is, variables
// that do not have any child variables).
message Variable {
  // The name of the variable resource, in the format:
  //
  //     projects/[PROJECT_ID]/configs/[CONFIG_NAME]/variables/[VARIABLE_NAME]
  //
  // The `[PROJECT_ID]` must be a valid project ID, `[CONFIG_NAME]` must be a
  // valid RuntimeConfig reource and `[VARIABLE_NAME]` follows Unix file system
  // file path naming.
  //
  // The `[VARIABLE_NAME]` can contain ASCII letters, numbers, slashes and
  // dashes. Slashes are used as path element separators and are not part of the
  // `[VARIABLE_NAME]` itself, so `[VARIABLE_NAME]` must contain at least one
  // non-slash character. Multiple slashes are coalesced into single slash
  // character. Each path segment should follow RFC 1035 segment specification.
  // The length of a `[VARIABLE_NAME]` must be less than 256 bytes.
  //
  // Once you create a variable, you cannot change the variable name.
  string name = 1;

  // The value of the variable. It can be either a binary or a string
  // value. You must specify one of either `value` or `text`. Specifying both
  // will cause the server to return an error.
  oneof contents {
    // The binary value of the variable. The length of the value must be less
    // than 4096 bytes. Empty values are also accepted. The value must be
    // base64 encoded. Only one of `value` or `text` can be set.
    bytes value = 2;

    // The string value of the variable. The length of the value must be less
    // than 4096 bytes. Empty values are also accepted. For example,
    // `text: "my text value"`. The string must be valid UTF-8.
    string text = 5;
  }

  // [Output Only] The time of the last variable update.
  google.protobuf.Timestamp update_time = 3;

  // [Ouput only] The current state of the variable. The variable state
  // indicates the outcome of the `variables().watch` call and is visible
  // through the `get` and `list` calls.
  VariableState state = 4;
}

// The condition that a Waiter resource is waiting for.
message EndCondition {
  // A Cardinality condition for the Waiter resource. A cardinality condition is
  // met when the number of variables under a specified path prefix reaches a
  // predefined number. For example, if you set a Cardinality condition where
  // the `path` is set to `/foo` and the number of paths is set to 2, the
  // following variables would meet the condition in a RuntimeConfig resource:
  //
  // + `/foo/variable1 = "value1"`
  // + `/foo/variable2 = "value2"`
  // + `/bar/variable3 = "value3"`
  //
  // It would not would not satisify the same condition with the `number` set to
  // 3, however, because there is only 2 paths that start with `/foo`.
  // Cardinality conditions are recursive; all subtrees under the specific
  // path prefix are counted.
  message Cardinality {
    // The root of the variable subtree to monitor. For example, `/foo`.
    string path = 1;

    // The number variables under the `path` that must exist to meet this
    // condition. Defaults to 1 if not specified.
    int32 number = 2;
  }

  // The condition oneof holds the available condition types for this
  // EndCondition. Currently, the only available type is Cardinality.
  oneof condition {
    // The cardinality of the `EndCondition`.
    Cardinality cardinality = 1;
  }
}

// A Waiter resource waits for some end condition within a RuntimeConfig
// resource to be met before it returns. For example, assume you have a
// distributed system where each node writes to a Variable resource indidicating
// the node's readiness as part of the startup process.
//
// You then configure a Waiter resource with the success condition set to wait
// until some number of nodes have checked in. Afterwards, your application
// runs some arbitrary code after the condition has been met and the waiter
// returns successfully.
//
// Once created, a Waiter resource is immutable.
//
// To learn more about using waiters, read the
// [Creating a
// Waiter](/deployment-manager/runtime-configurator/creating-a-waiter)
// documentation.
message Waiter {
  // The name of the Waiter resource, in the format:
  //
  //     projects/[PROJECT_ID]/configs/[CONFIG_NAME]/waiters/[WAITER_NAME]
  //
  // The `[PROJECT_ID]` must be a valid Google Cloud project ID,
  // the `[CONFIG_NAME]` must be a valid RuntimeConfig resource, the
  // `[WAITER_NAME]` must match RFC 1035 segment specification, and the length
  // of `[WAITER_NAME]` must be less than 64 bytes.
  //
  // After you create a Waiter resource, you cannot change the resource name.
  string name = 1;

  // [Required] Specifies the timeout of the waiter in seconds, beginning from
  // the instant that `waiters().create` method is called. If this time elapses
  // before the success or failure conditions are met, the waiter fails and sets
  // the `error` code to `DEADLINE_EXCEEDED`.
  google.protobuf.Duration timeout = 2;

  // [Optional] The failure condition of this waiter. If this condition is met,
  // `done` will be set to `true` and the `error` code will be set to `ABORTED`.
  // The failure condition takes precedence over the success condition. If both
  // conditions are met, a failure will be indicated. This value is optional; if
  // no failure condition is set, the only failure scenario will be a timeout.
  EndCondition failure = 3;

  // [Required] The success condition. If this condition is met, `done` will be
  // set to `true` and the `error` value will remain unset. The failure
  // condition takes precedence over the success condition. If both conditions
  // are met, a failure will be indicated.
  EndCondition success = 4;

  // [Output Only] The instant at which this Waiter resource was created. Adding
  // the value of `timeout` to this instant yields the timeout deadline for the
  // waiter.
  google.protobuf.Timestamp create_time = 5;

  // [Output Only] If the value is `false`, it means the waiter is still waiting
  // for one of its conditions to be met.
  //
  // If true, the waiter has finished. If the waiter finished due to a timeout
  // or failure, `error` will be set.
  bool done = 6;

  // [Output Only] If the waiter ended due to a failure or timeout, this value
  // will be set.
  google.rpc.Status error = 7;
}

// The `VariableState` describes the last known state of the variable and is
// used during a `variables().watch` call to distinguish the state of the
// variable.
enum VariableState {
  // Default variable state.
  VARIABLE_STATE_UNSPECIFIED = 0;

  // The variable was updated, while `variables().watch` was executing.
  UPDATED = 1;

  // The variable was deleted, while `variables().watch` was executing.
  DELETED = 2;
}
