{"methodConfig": [{"name": [{"service": "google.cloud.datacatalog.v1beta1.DataCatalog"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "RESOURCE_EXHAUSTED", "INTERNAL"]}}, {"name": [{"service": "google.cloud.datacatalog.v1beta1.PolicyTagManager"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.datacatalog.v1beta1.PolicyTagManagerSerialization"}], "timeout": "60s"}]}