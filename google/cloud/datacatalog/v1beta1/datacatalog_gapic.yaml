type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
# The settings of generated code in a specific language.
language_settings:
  python:
    package_name: google.cloud.datacatalog_v1beta1.gapic
  go:
    package_name: cloud.google.com/go/datacatalog/apiv1beta1
  csharp:
    package_name: Google.Cloud.Datacatalog.V1beta1
  ruby:
    package_name: Google::Cloud::Datacatalog::V1beta1
  php:
    package_name: Google\Cloud\Datacatalog\V1beta1
  nodejs:
    package_name: datacatalog.v1beta1
    domain_layer_location: google-cloud
# A list of API interface configurations.
interfaces:
# The fully qualified name of the API interface.
- name: google.cloud.datacatalog.v1beta1.DataCatalog
  methods:
  - name: SearchCatalog
    samples:
      standalone:
      - value_sets: [datacatalog_search]
        region_tag: datacatalog_search
    sample_value_sets:
    - id: datacatalog_search
      description: Search Catalog
      parameters:
        defaults:
        - scope.include_project_ids[0] = "[Google Cloud Project ID]"
        - scope.include_gcp_public_datasets = false
        - query = "[String in search query syntax]"
        attributes:
        - parameter: scope.include_project_ids[0]
          description: Your Google Cloud project ID.
          sample_argument_name: include_project_id
        - parameter: scope.include_gcp_public_datasets
          description: If true, include Google Cloud Platform (GCP) public datasets in the search results.
          sample_argument_name: include_gcp_public_datasets
        - parameter: query
          description: |
            Your query string.
            See: https://cloud.google.com/data-catalog/docs/how-to/search-reference
            Example: system=bigquery type=dataset
          sample_argument_name: query
      on_success:
      - print: ["Result type: %s", $resp.search_result_type]
      - print: ["Result subtype: %s", $resp.search_result_subtype]
      - print: ["Relative resource name: %s", $resp.relative_resource_name]
      - print: ["Linked resource: %s", $resp.linked_resource]
  - name: GetEntry
    samples:
      standalone:
      - region_tag: datacatalog_get_entry
        value_sets: [datacatalog_get_entry]
    sample_value_sets:
    - id: datacatalog_get_entry
      description: Get Entry
      parameters:
        defaults:
        # The full "name" is of the format: "projects/{project_id}/locations/{location_id}/entryGroups/{entry_group_id}/entries/{entry_id}"
        # Each part of the name can be separated out into its own parameter using `name%[part name]`
        - name%project = "[Google Cloud Project ID]"
        - name%location = "[Google Cloud Location ID]"
        - name%entry_group = "[Entry Group ID]"
        - name%entry = "[Entry ID]"
        attributes:
        - parameter: name%project
          description: "Your Google Cloud project ID"
          sample_argument_name: project_id
        - parameter: name%location
          description: "Google Cloud region, e.g. us-central1"
          sample_argument_name: location_id
        - parameter: name%entry_group
          description: "ID of the Entry Group, e.g. @bigquery, @pubsub, my_entry_group"
          sample_argument_name: entry_group_id
        - parameter: name%entry
          description: "ID of the Entry"
          sample_argument_name: entry_id
      on_success:
      - define: entry = $resp
      - print: ["Entry name: %s", entry.name]
      - print: ["Entry type: %s", entry.type]
      - print: ["Linked resource: %s", entry.linked_resource]
  - name: LookupEntry
    samples:
      standalone:
      - region_tag: datacatalog_lookup_entry
        value_sets: [datacatalog_lookup_entry]
      - region_tag: datacatalog_lookup_entry_sql_resource
        value_sets: [datacatalog_lookup_entry_sql_resource]
    sample_value_sets:
    - id: datacatalog_lookup_entry
      description: Lookup Entry
      parameters:
        defaults:
        - linked_resource = "[Full Resource Name]"
        attributes:
        - parameter: linked_resource
          sample_argument_name: resource_name
          description: |
            The full name of the Google Cloud Platform resource the Data Catalog entry represents.
            See: https://cloud.google.com/apis/design/resource_names#full_resource_name
            Examples:
              //bigquery.googleapis.com/projects/bigquery-public-data/datasets/new_york_taxi_trips/tables/taxi_zone_geom
              //pubsub.googleapis.com/projects/pubsub-public-data/topics/taxirides-realtime
      on_success:
      - define: entry = $resp
      - print: ["Entry name: %s", entry.name]
      - print: ["Entry type: %s", entry.type]
      - print: ["Linked resource: %s", entry.linked_resource]
    - id: datacatalog_lookup_entry_sql_resource
      description: Lookup Entry using SQL resource
      parameters:
        defaults:
        - sql_resource = "[SQL Resource Name]"
        attributes:
        - parameter: sql_resource
          sample_argument_name: sql_name
          description: |
            The SQL name of the Google Cloud Platform resource the Data Catalog entry represents.
            Examples:
              bigquery.table.`bigquery-public-data`.new_york_taxi_trips.taxi_zone_geom
              pubsub.topic.`pubsub-public-data`.`taxirides-realtime`
      on_success:
      - define: entry = $resp
      - print: ["Entry name: %s", entry.name]
      - print: ["Entry type: %s", entry.type]
      - print: ["Linked resource: %s", entry.linked_resource]
