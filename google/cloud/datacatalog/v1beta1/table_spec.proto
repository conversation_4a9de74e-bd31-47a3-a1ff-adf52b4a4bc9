// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1Beta1";
option go_package = "cloud.google.com/go/datacatalog/apiv1beta1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1beta1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1beta1";
option ruby_package = "Google::Cloud::DataCatalog::V1beta1";

// Describes a BigQuery table.
message BigQueryTableSpec {
  // Output only. The table source type.
  TableSourceType table_source_type = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only.
  oneof type_spec {
    // Table view specification. This field should only be populated if
    // `table_source_type` is `BIGQUERY_VIEW`.
    ViewSpec view_spec = 2;

    // Spec of a BigQuery table. This field should only be populated if
    // `table_source_type` is `BIGQUERY_TABLE`.
    TableSpec table_spec = 3;
  }
}

// Table source type.
enum TableSourceType {
  // Default unknown type.
  TABLE_SOURCE_TYPE_UNSPECIFIED = 0;

  // Table view.
  BIGQUERY_VIEW = 2;

  // BigQuery native table.
  BIGQUERY_TABLE = 5;

  // BigQuery materialized view.
  BIGQUERY_MATERIALIZED_VIEW = 7;
}

// Table view specification.
message ViewSpec {
  // Output only. The query that defines the table view.
  string view_query = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Normal BigQuery table spec.
message TableSpec {
  // Output only. If the table is a dated shard, i.e., with name pattern
  // `[prefix]YYYYMMDD`, `grouped_entry` is the Data Catalog resource name of
  // the date sharded grouped entry, for example,
  // `projects/{project_id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entry_id}`.
  // Otherwise, `grouped_entry` is empty.
  string grouped_entry = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];
}

// Spec for a group of BigQuery tables with name pattern `[prefix]YYYYMMDD`.
// Context:
// https://cloud.google.com/bigquery/docs/partitioned-tables#partitioning_versus_sharding
message BigQueryDateShardedSpec {
  // Output only. The Data Catalog resource name of the dataset entry the
  // current table belongs to, for example,
  // `projects/{project_id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entry_id}`.
  string dataset = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];

  // Output only. The table name prefix of the shards. The name of any given
  // shard is
  // `[table_prefix]YYYYMMDD`, for example, for shard `MyTable20180101`, the
  // `table_prefix` is `MyTable`.
  string table_prefix = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total number of shards.
  int64 shard_count = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
