type: google.api.Service
config_version: 3
name: datacatalog.googleapis.com
title: Google Cloud Data Catalog API

apis:
- name: google.cloud.datacatalog.v1beta1.DataCatalog
- name: google.cloud.datacatalog.v1beta1.PolicyTagManager
- name: google.cloud.datacatalog.v1beta1.PolicyTagManagerSerialization
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

documentation:
  summary: |-
    A fully managed and highly scalable data discovery and metadata management
    service.
  overview: |-
    DataCatalog is a centralized and unified data catalog service for all your
    Cloud resources, where users and systems can discover data, explore and
    curate  its semantics, understand how to act on it, and help govern its
    usage.
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

authentication:
  rules:
  - selector: 'google.cloud.datacatalog.v1beta1.DataCatalog.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.datacatalog.v1beta1.PolicyTagManager.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.datacatalog.v1beta1.PolicyTagManagerSerialization.ExportTaxonomies
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.datacatalog.v1beta1.PolicyTagManagerSerialization.ImportTaxonomies
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=655468&template=1284353
  documentation_uri: https://cloud.google.com/data-catalog/docs
  api_short_name: datacatalog
  github_label: 'api: datacatalog'
  doc_tag_prefix: datacatalog
  organization: CLOUD
