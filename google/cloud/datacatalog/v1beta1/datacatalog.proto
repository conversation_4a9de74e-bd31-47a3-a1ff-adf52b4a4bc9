// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/datacatalog/v1beta1/common.proto";
import "google/cloud/datacatalog/v1beta1/gcs_fileset_spec.proto";
import "google/cloud/datacatalog/v1beta1/schema.proto";
import "google/cloud/datacatalog/v1beta1/search.proto";
import "google/cloud/datacatalog/v1beta1/table_spec.proto";
import "google/cloud/datacatalog/v1beta1/tags.proto";
import "google/cloud/datacatalog/v1beta1/timestamps.proto";
import "google/cloud/datacatalog/v1beta1/usage.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1Beta1";
option go_package = "cloud.google.com/go/datacatalog/apiv1beta1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1beta1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1beta1";
option ruby_package = "Google::Cloud::DataCatalog::V1beta1";
option (google.api.resource_definition) = {
  type: "datacatalog.googleapis.com/TagTemplateFieldEnumValue"
  pattern: "projects/{project}/locations/{location}/tagTemplates/{tag_template}/fields/{tag_template_field_id}/enumValues/{enum_value_display_name}"
};

// Data Catalog API service allows clients to discover, understand, and manage
// their data.
service DataCatalog {
  option (google.api.default_host) = "datacatalog.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Searches Data Catalog for multiple resources like entries, tags that
  // match a query.
  //
  // This is a custom method
  // (https://cloud.google.com/apis/design/custom_methods) and does not return
  // the complete resource, only the resource identifier and high level
  // fields. Clients can subsequently call `Get` methods.
  //
  // Note that Data Catalog search queries do not guarantee full recall. Query
  // results that match your query may not be returned, even in subsequent
  // result pages. Also note that results returned (and not returned) can vary
  // across repeated search queries.
  //
  // See [Data Catalog Search
  // Syntax](https://cloud.google.com/data-catalog/docs/how-to/search-reference)
  // for more information.
  rpc SearchCatalog(SearchCatalogRequest) returns (SearchCatalogResponse) {
    option (google.api.http) = {
      post: "/v1beta1/catalog:search"
      body: "*"
    };
    option (google.api.method_signature) = "scope,query";
  }

  // A maximum of 10,000 entry groups may be created per organization across all
  // locations.
  //
  // Users should enable the Data Catalog API in the project identified by
  // the `parent` parameter (see [Data Catalog Resource Project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for
  // more information).
  rpc CreateEntryGroup(CreateEntryGroupRequest) returns (EntryGroup) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/entryGroups"
      body: "entry_group"
    };
    option (google.api.method_signature) = "parent,entry_group_id,entry_group";
  }

  // Updates an EntryGroup. The user should enable the Data Catalog API in the
  // project identified by the `entry_group.name` parameter (see [Data Catalog
  // Resource Project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for
  // more information).
  rpc UpdateEntryGroup(UpdateEntryGroupRequest) returns (EntryGroup) {
    option (google.api.http) = {
      patch: "/v1beta1/{entry_group.name=projects/*/locations/*/entryGroups/*}"
      body: "entry_group"
    };
    option (google.api.method_signature) = "entry_group";
    option (google.api.method_signature) = "entry_group,update_mask";
  }

  // Gets an EntryGroup.
  rpc GetEntryGroup(GetEntryGroupRequest) returns (EntryGroup) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/entryGroups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.api.method_signature) = "name,read_mask";
  }

  // Deletes an EntryGroup. Only entry groups that do not contain entries can be
  // deleted. Users should enable the Data Catalog API in the project
  // identified by the `name` parameter (see [Data Catalog Resource Project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for
  // more information).
  rpc DeleteEntryGroup(DeleteEntryGroupRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/entryGroups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists entry groups.
  rpc ListEntryGroups(ListEntryGroupsRequest)
      returns (ListEntryGroupsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/entryGroups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates an entry. Only entries of 'FILESET' type or user-specified type can
  // be created.
  //
  // Users should enable the Data Catalog API in the project identified by
  // the `parent` parameter (see [Data Catalog Resource Project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for
  // more information).
  //
  // A maximum of 100,000 entries may be created per entry group.
  rpc CreateEntry(CreateEntryRequest) returns (Entry) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/entryGroups/*}/entries"
      body: "entry"
    };
    option (google.api.method_signature) = "parent,entry_id,entry";
  }

  // Updates an existing entry.
  // Users should enable the Data Catalog API in the project identified by
  // the `entry.name` parameter (see [Data Catalog Resource Project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for
  // more information).
  rpc UpdateEntry(UpdateEntryRequest) returns (Entry) {
    option (google.api.http) = {
      patch: "/v1beta1/{entry.name=projects/*/locations/*/entryGroups/*/entries/*}"
      body: "entry"
    };
    option (google.api.method_signature) = "entry";
    option (google.api.method_signature) = "entry,update_mask";
  }

  // Deletes an existing entry. Only entries created through
  // [CreateEntry][google.cloud.datacatalog.v1beta1.DataCatalog.CreateEntry]
  // method can be deleted.
  // Users should enable the Data Catalog API in the project identified by
  // the `name` parameter (see [Data Catalog Resource Project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for
  // more information).
  rpc DeleteEntry(DeleteEntryRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/entryGroups/*/entries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets an entry.
  rpc GetEntry(GetEntryRequest) returns (Entry) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/entryGroups/*/entries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Get an entry by target resource name. This method allows clients to use
  // the resource name from the source Google Cloud Platform service to get the
  // Data Catalog Entry.
  rpc LookupEntry(LookupEntryRequest) returns (Entry) {
    option (google.api.http) = {
      get: "/v1beta1/entries:lookup"
    };
  }

  // Lists entries.
  rpc ListEntries(ListEntriesRequest) returns (ListEntriesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/entryGroups/*}/entries"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a tag template. The user should enable the Data Catalog API in
  // the project identified by the `parent` parameter (see [Data Catalog
  // Resource
  // Project](https://cloud.google.com/data-catalog/docs/concepts/resource-project)
  // for more information).
  rpc CreateTagTemplate(CreateTagTemplateRequest) returns (TagTemplate) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/tagTemplates"
      body: "tag_template"
    };
    option (google.api.method_signature) =
        "parent,tag_template_id,tag_template";
  }

  // Gets a tag template.
  rpc GetTagTemplate(GetTagTemplateRequest) returns (TagTemplate) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/tagTemplates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a tag template. This method cannot be used to update the fields of
  // a template. The tag template fields are represented as separate resources
  // and should be updated using their own create/update/delete methods.
  // Users should enable the Data Catalog API in the project identified by
  // the `tag_template.name` parameter (see [Data Catalog Resource Project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for
  // more information).
  rpc UpdateTagTemplate(UpdateTagTemplateRequest) returns (TagTemplate) {
    option (google.api.http) = {
      patch: "/v1beta1/{tag_template.name=projects/*/locations/*/tagTemplates/*}"
      body: "tag_template"
    };
    option (google.api.method_signature) = "tag_template";
    option (google.api.method_signature) = "tag_template,update_mask";
  }

  // Deletes a tag template and all tags using the template.
  // Users should enable the Data Catalog API in the project identified by
  // the `name` parameter (see [Data Catalog Resource Project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for
  // more information).
  rpc DeleteTagTemplate(DeleteTagTemplateRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/tagTemplates/*}"
    };
    option (google.api.method_signature) = "name,force";
  }

  // Creates a field in a tag template. The user should enable the Data Catalog
  // API in the project identified by the `parent` parameter (see
  // [Data Catalog Resource
  // Project](https://cloud.google.com/data-catalog/docs/concepts/resource-project)
  // for more information).
  rpc CreateTagTemplateField(CreateTagTemplateFieldRequest)
      returns (TagTemplateField) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/tagTemplates/*}/fields"
      body: "tag_template_field"
    };
    option (google.api.method_signature) =
        "parent,tag_template_field_id,tag_template_field";
  }

  // Updates a field in a tag template. This method cannot be used to update the
  // field type. Users should enable the Data Catalog API in the project
  // identified by the `name` parameter (see [Data Catalog Resource Project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for
  // more information).
  rpc UpdateTagTemplateField(UpdateTagTemplateFieldRequest)
      returns (TagTemplateField) {
    option (google.api.http) = {
      patch: "/v1beta1/{name=projects/*/locations/*/tagTemplates/*/fields/*}"
      body: "tag_template_field"
    };
    option (google.api.method_signature) = "name,tag_template_field";
    option (google.api.method_signature) =
        "name,tag_template_field,update_mask";
  }

  // Renames a field in a tag template. The user should enable the Data Catalog
  // API in the project identified by the `name` parameter (see [Data Catalog
  // Resource
  // Project](https://cloud.google.com/data-catalog/docs/concepts/resource-project)
  // for more information).
  rpc RenameTagTemplateField(RenameTagTemplateFieldRequest)
      returns (TagTemplateField) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/tagTemplates/*/fields/*}:rename"
      body: "*"
    };
    option (google.api.method_signature) = "name,new_tag_template_field_id";
  }

  // Renames an enum value in a tag template. The enum values have to be unique
  // within one enum field. Thus, an enum value cannot be renamed with a name
  // used in any other enum value within the same enum field.
  rpc RenameTagTemplateFieldEnumValue(RenameTagTemplateFieldEnumValueRequest)
      returns (TagTemplateField) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/tagTemplates/*/fields/*/enumValues/*}:rename"
      body: "*"
    };
    option (google.api.method_signature) = "name,new_enum_value_display_name";
  }

  // Deletes a field in a tag template and all uses of that field.
  // Users should enable the Data Catalog API in the project identified by
  // the `name` parameter (see [Data Catalog Resource Project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for
  // more information).
  rpc DeleteTagTemplateField(DeleteTagTemplateFieldRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/tagTemplates/*/fields/*}"
    };
    option (google.api.method_signature) = "name,force";
  }

  // Creates a tag on an [Entry][google.cloud.datacatalog.v1beta1.Entry].
  // Note: The project identified by the `parent` parameter for the
  // [tag](https://cloud.google.com/data-catalog/docs/reference/rest/v1beta1/projects.locations.entryGroups.entries.tags/create#path-parameters)
  // and the
  // [tag
  // template](https://cloud.google.com/data-catalog/docs/reference/rest/v1beta1/projects.locations.tagTemplates/create#path-parameters)
  // used to create the tag must be from the same organization.
  rpc CreateTag(CreateTagRequest) returns (Tag) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/entryGroups/*/entries/*}/tags"
      body: "tag"
      additional_bindings {
        post: "/v1beta1/{parent=projects/*/locations/*/entryGroups/*}/tags"
        body: "tag"
      }
    };
    option (google.api.method_signature) = "parent,tag";
  }

  // Updates an existing tag.
  rpc UpdateTag(UpdateTagRequest) returns (Tag) {
    option (google.api.http) = {
      patch: "/v1beta1/{tag.name=projects/*/locations/*/entryGroups/*/entries/*/tags/*}"
      body: "tag"
      additional_bindings {
        patch: "/v1beta1/{tag.name=projects/*/locations/*/entryGroups/*/tags/*}"
        body: "tag"
      }
    };
    option (google.api.method_signature) = "tag";
    option (google.api.method_signature) = "tag,update_mask";
  }

  // Deletes a tag.
  rpc DeleteTag(DeleteTagRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/entryGroups/*/entries/*/tags/*}"
      additional_bindings {
        delete: "/v1beta1/{name=projects/*/locations/*/entryGroups/*/tags/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists tags assigned to an [Entry][google.cloud.datacatalog.v1beta1.Entry].
  // The [columns][google.cloud.datacatalog.v1beta1.Tag.column] in the response
  // are lowercased.
  rpc ListTags(ListTagsRequest) returns (ListTagsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/entryGroups/*/entries/*}/tags"
      additional_bindings {
        get: "/v1beta1/{parent=projects/*/locations/*/entryGroups/*}/tags"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Sets the access control policy for a resource. Replaces any existing
  // policy.
  // Supported resources are:
  //   - Tag templates.
  //   - Entries.
  //   - Entry groups.
  // Note, this method cannot be used to manage policies for BigQuery, Pub/Sub
  // and any external Google Cloud Platform resources synced to Data Catalog.
  //
  // Callers must have following Google IAM permission
  //   - `datacatalog.tagTemplates.setIamPolicy` to set policies on tag
  //     templates.
  //   - `datacatalog.entries.setIamPolicy` to set policies on entries.
  //   - `datacatalog.entryGroups.setIamPolicy` to set policies on entry groups.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/locations/*/tagTemplates/*}:setIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/entryGroups/*}:setIamPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Gets the access control policy for a resource. A `NOT_FOUND` error
  // is returned if the resource does not exist. An empty policy is returned
  // if the resource exists but does not have a policy set on it.
  //
  // Supported resources are:
  //   - Tag templates.
  //   - Entries.
  //   - Entry groups.
  // Note, this method cannot be used to manage policies for BigQuery, Pub/Sub
  // and any external Google Cloud Platform resources synced to Data Catalog.
  //
  // Callers must have following Google IAM permission
  //   - `datacatalog.tagTemplates.getIamPolicy` to get policies on tag
  //     templates.
  //   - `datacatalog.entries.getIamPolicy` to get policies on entries.
  //   - `datacatalog.entryGroups.getIamPolicy` to get policies on entry groups.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/locations/*/tagTemplates/*}:getIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/entryGroups/*}:getIamPolicy"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/entryGroups/*/entries/*}:getIamPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource";
  }

  // Returns the caller's permissions on a resource.
  // If the resource does not exist, an empty set of permissions is returned
  // (We don't return a `NOT_FOUND` error).
  //
  // Supported resources are:
  //   - Tag templates.
  //   - Entries.
  //   - Entry groups.
  // Note, this method cannot be used to manage policies for BigQuery, Pub/Sub
  // and any external Google Cloud Platform resources synced to Data Catalog.
  //
  // A caller is not required to have Google IAM permission to make this
  // request.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{resource=projects/*/locations/*/tagTemplates/*}:testIamPermissions"
      body: "*"
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/entryGroups/*}:testIamPermissions"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta1/{resource=projects/*/locations/*/entryGroups/*/entries/*}:testIamPermissions"
        body: "*"
      }
    };
  }
}

// Request message for
// [SearchCatalog][google.cloud.datacatalog.v1beta1.DataCatalog.SearchCatalog].
message SearchCatalogRequest {
  // The criteria that select the subspace used for query matching.
  message Scope {
    // The list of organization IDs to search within. To find your organization
    // ID, follow instructions in
    // https://cloud.google.com/resource-manager/docs/creating-managing-organization.
    repeated string include_org_ids = 2;

    // The list of project IDs to search within. To learn more about the
    // distinction between project names/IDs/numbers, go to
    // https://cloud.google.com/docs/overview/#projects.
    repeated string include_project_ids = 3;

    // If `true`, include Google Cloud public datasets in the
    // search results. Info on Google Cloud public datasets is available at
    // https://cloud.google.com/public-datasets/. By default, Google Cloud
    // public datasets are excluded.
    bool include_gcp_public_datasets = 7;

    // Optional. The list of locations to search within.
    // 1. If empty, search will be performed in all locations;
    // 2. If any of the locations are NOT in the valid locations list, error
    // will be returned;
    // 3. Otherwise, search only the given locations for matching results.
    // Typical usage is to leave this field empty. When a location is
    // unreachable as returned in the `SearchCatalogResponse.unreachable` field,
    // users can repeat the search request with this parameter set to get
    // additional information on the error.
    //
    // Valid locations:
    //  * asia-east1
    //  * asia-east2
    //  * asia-northeast1
    //  * asia-northeast2
    //  * asia-northeast3
    //  * asia-south1
    //  * asia-southeast1
    //  * australia-southeast1
    //  * eu
    //  * europe-north1
    //  * europe-west1
    //  * europe-west2
    //  * europe-west3
    //  * europe-west4
    //  * europe-west6
    //  * global
    //  * northamerica-northeast1
    //  * southamerica-east1
    //  * us
    //  * us-central1
    //  * us-east1
    //  * us-east4
    //  * us-west1
    //  * us-west2
    repeated string restricted_locations = 16
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The scope of this search request. A `scope` that has empty
  // `include_org_ids`, `include_project_ids` AND false
  // `include_gcp_public_datasets` is considered invalid. Data Catalog will
  // return an error in such a case.
  Scope scope = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. The query string in search query syntax. An empty query string
  // will result in all data assets (in the specified scope) that the user has
  // access to. Query strings can be simple as "x" or more qualified as:
  //
  // * name:x
  // * column:x
  // * description:y
  //
  // Note: Query tokens need to have a minimum of 3 characters for substring
  // matching to work correctly. See [Data Catalog Search
  // Syntax](https://cloud.google.com/data-catalog/docs/how-to/search-reference)
  // for more information.
  string query = 1 [(google.api.field_behavior) = OPTIONAL];

  // Number of results in the search page. If <=0 then defaults to 10. Max limit
  // for page_size is 1000. Throws an invalid argument for page_size > 1000.
  int32 page_size = 2;

  // Optional. Pagination token returned in an earlier
  // [SearchCatalogResponse.next_page_token][google.cloud.datacatalog.v1beta1.SearchCatalogResponse.next_page_token],
  // which indicates that this is a continuation of a prior
  // [SearchCatalogRequest][google.cloud.datacatalog.v1beta1.DataCatalog.SearchCatalog]
  // call, and that the system should return the next page of data. If empty,
  // the first page is returned.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Specifies the ordering of results, currently supported case-sensitive
  // choices are:
  //
  //   * `relevance`, only supports descending
  //   * `last_modified_timestamp [asc|desc]`, defaults to descending if not
  //     specified
  //   * `default` that can only be descending
  //
  // If not specified, defaults to `relevance` descending.
  string order_by = 5;
}

// Response message for
// [SearchCatalog][google.cloud.datacatalog.v1beta1.DataCatalog.SearchCatalog].
message SearchCatalogResponse {
  // Search results.
  repeated SearchCatalogResult results = 1;

  // The approximate total number of entries matched by the query.
  int32 total_size = 2;

  // The token that can be used to retrieve the next page of results.
  string next_page_token = 3;

  // Unreachable locations. Search result does not include data from those
  // locations. Users can get additional information on the error by repeating
  // the search request with a more restrictive parameter -- setting the value
  // for `SearchDataCatalogRequest.scope.restricted_locations`.
  repeated string unreachable = 6;
}

// Request message for
// [CreateEntryGroup][google.cloud.datacatalog.v1beta1.DataCatalog.CreateEntryGroup].
message CreateEntryGroupRequest {
  // Required. The name of the project this entry group is in. Example:
  //
  // * projects/{project_id}/locations/{location}
  //
  // Note that this EntryGroup and its child resources may not actually be
  // stored in the location in this name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // Required. The id of the entry group to create.
  // The id must begin with a letter or underscore, contain only English
  // letters, numbers and underscores, and be at most 64 characters.
  string entry_group_id = 3 [(google.api.field_behavior) = REQUIRED];

  // The entry group to create. Defaults to an empty entry group.
  EntryGroup entry_group = 2;
}

// Request message for
// [UpdateEntryGroup][google.cloud.datacatalog.v1beta1.DataCatalog.UpdateEntryGroup].
message UpdateEntryGroupRequest {
  // Required. The updated entry group. "name" field must be set.
  EntryGroup entry_group = 1 [(google.api.field_behavior) = REQUIRED];

  // Names of fields whose values to overwrite on an entry group.
  //
  // If this parameter is absent or empty, all modifiable fields
  // are overwritten. If such fields are non-required and omitted in the
  // request body, their values are emptied.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [GetEntryGroup][google.cloud.datacatalog.v1beta1.DataCatalog.GetEntryGroup].
message GetEntryGroupRequest {
  // Required. The name of the entry group. For example,
  // `projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // The fields to return. If not set or empty, all fields are returned.
  google.protobuf.FieldMask read_mask = 2;
}

// Request message for
// [DeleteEntryGroup][google.cloud.datacatalog.v1beta1.DataCatalog.DeleteEntryGroup].
message DeleteEntryGroupRequest {
  // Required. The name of the entry group. For example,
  // `projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // Optional. If true, deletes all entries in the entry group.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [ListEntryGroups][google.cloud.datacatalog.v1beta1.DataCatalog.ListEntryGroups].
message ListEntryGroupsRequest {
  // Required. The name of the location that contains the entry groups, which
  // can be provided in URL format. Example:
  //
  // * projects/{project_id}/locations/{location}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // Optional. The maximum number of items to return. Default is 10. Max limit
  // is 1000. Throws an invalid argument for `page_size > 1000`.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Token that specifies which page is requested. If empty, the first
  // page is returned.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [ListEntryGroups][google.cloud.datacatalog.v1beta1.DataCatalog.ListEntryGroups].
message ListEntryGroupsResponse {
  // EntryGroup details.
  repeated EntryGroup entry_groups = 1;

  // Token to retrieve the next page of results. It is set to empty if no items
  // remain in results.
  string next_page_token = 2;
}

// Request message for
// [CreateEntry][google.cloud.datacatalog.v1beta1.DataCatalog.CreateEntry].
message CreateEntryRequest {
  // Required. The name of the entry group this entry is in. Example:
  //
  // * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
  //
  // Note that this Entry and its child resources may not actually be stored in
  // the location in this name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // Required. The id of the entry to create.
  string entry_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The entry to create.
  Entry entry = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [UpdateEntry][google.cloud.datacatalog.v1beta1.DataCatalog.UpdateEntry].
message UpdateEntryRequest {
  // Required. The updated entry. The "name" field must be set.
  Entry entry = 1 [(google.api.field_behavior) = REQUIRED];

  // Names of fields whose values to overwrite on an entry.
  //
  // If this parameter is absent or empty, all modifiable fields
  // are overwritten. If such fields are non-required and omitted in the
  // request body, their values are emptied.
  //
  // The following fields are modifiable:
  //
  // * For entries with type `DATA_STREAM`:
  //    * `schema`
  // * For entries with type `FILESET`:
  //    * `schema`
  //    * `display_name`
  //    * `description`
  //    * `gcs_fileset_spec`
  //    * `gcs_fileset_spec.file_patterns`
  // * For entries with `user_specified_type`:
  //    * `schema`
  //    * `display_name`
  //    * `description`
  //    * `user_specified_type`
  //    * `user_specified_system`
  //    * `linked_resource`
  //    * `source_system_timestamps`
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [DeleteEntry][google.cloud.datacatalog.v1beta1.DataCatalog.DeleteEntry].
message DeleteEntryRequest {
  // Required. The name of the entry. Example:
  //
  // * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];
}

// Request message for
// [GetEntry][google.cloud.datacatalog.v1beta1.DataCatalog.GetEntry].
message GetEntryRequest {
  // Required. The name of the entry. Example:
  //
  // * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];
}

// Request message for
// [LookupEntry][google.cloud.datacatalog.v1beta1.DataCatalog.LookupEntry].
message LookupEntryRequest {
  // Required. Represents either the Google Cloud Platform resource or SQL name
  // for a Google Cloud Platform resource.
  oneof target_name {
    // The full name of the Google Cloud Platform resource the Data Catalog
    // entry represents. See:
    // https://cloud.google.com/apis/design/resource_names#full_resource_name.
    // Full names are case-sensitive.
    //
    // Examples:
    //
    //  * //bigquery.googleapis.com/projects/projectId/datasets/datasetId/tables/tableId
    //  * //pubsub.googleapis.com/projects/projectId/topics/topicId
    string linked_resource = 1;

    // The SQL name of the entry. SQL names are case-sensitive.
    //
    // Examples:
    //
    //   * `pubsub.project_id.topic_id`
    //   * ``pubsub.project_id.`topic.id.with.dots` ``
    //   * `bigquery.table.project_id.dataset_id.table_id`
    //   * `bigquery.dataset.project_id.dataset_id`
    //   * `datacatalog.entry.project_id.location_id.entry_group_id.entry_id`
    //
    // `*_id`s should satisfy the standard SQL rules for identifiers.
    // https://cloud.google.com/bigquery/docs/reference/standard-sql/lexical.
    string sql_resource = 3;
  }
}

// Entry Metadata.
// A Data Catalog Entry resource represents another resource in Google
// Cloud Platform (such as a BigQuery dataset or a Pub/Sub topic), or
// outside of Google Cloud Platform. Clients can use the `linked_resource` field
// in the Entry resource to refer to the original resource ID of the source
// system.
//
// An Entry resource contains resource details, such as its schema. An Entry can
// also be used to attach flexible metadata, such as a
// [Tag][google.cloud.datacatalog.v1beta1.Tag].
message Entry {
  option (google.api.resource) = {
    type: "datacatalog.googleapis.com/Entry"
    pattern: "projects/{project}/locations/{location}/entryGroups/{entry_group}/entries/{entry}"
  };

  // Output only. Identifier. The Data Catalog resource name of the entry in URL
  // format. Example:
  //
  // * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}
  //
  // Note that this Entry and its child resources may not actually be stored in
  // the location in this name.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // The resource this metadata entry refers to.
  //
  // For Google Cloud Platform resources, `linked_resource` is the [full name of
  // the
  // resource](https://cloud.google.com/apis/design/resource_names#full_resource_name).
  // For example, the `linked_resource` for a table resource from BigQuery is:
  //
  // * //bigquery.googleapis.com/projects/projectId/datasets/datasetId/tables/tableId
  //
  // Output only when Entry is of type in the EntryType enum. For entries with
  // user_specified_type, this field is optional and defaults to an empty
  // string.
  string linked_resource = 9;

  // Required. Entry type.
  oneof entry_type {
    // The type of the entry.
    // Only used for Entries with types in the EntryType enum.
    EntryType type = 2;

    // Entry type if it does not fit any of the input-allowed values listed in
    // `EntryType` enum above. When creating an entry, users should check the
    // enum values first, if nothing matches the entry to be created, then
    // provide a custom value, for example "my_special_type".
    // `user_specified_type` strings must begin with a letter or underscore and
    // can only contain letters, numbers, and underscores; are case insensitive;
    // must be at least 1 character and at most 64 characters long.
    //
    // Currently, only FILESET enum value is allowed. All other entries created
    // through Data Catalog must use `user_specified_type`.
    string user_specified_type = 16;
  }

  // The source system of the entry.
  oneof system {
    // Output only. This field indicates the entry's source system that Data
    // Catalog integrates with, such as BigQuery or Pub/Sub.
    IntegratedSystem integrated_system = 17
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // This field indicates the entry's source system that Data Catalog does not
    // integrate with. `user_specified_system` strings must begin with a letter
    // or underscore and can only contain letters, numbers, and underscores; are
    // case insensitive; must be at least 1 character and at most 64 characters
    // long.
    string user_specified_system = 18;
  }

  // Type specification information.
  oneof type_spec {
    // Specification that applies to a Cloud Storage fileset. This is only valid
    // on entries of type FILESET.
    GcsFilesetSpec gcs_fileset_spec = 6;

    // Specification that applies to a BigQuery table. This is only valid on
    // entries of type `TABLE`.
    BigQueryTableSpec bigquery_table_spec = 12;

    // Specification for a group of BigQuery tables with name pattern
    // `[prefix]YYYYMMDD`. Context:
    // https://cloud.google.com/bigquery/docs/partitioned-tables#partitioning_versus_sharding.
    BigQueryDateShardedSpec bigquery_date_sharded_spec = 15;
  }

  // Display information such as title and description. A short name to identify
  // the entry, for example, "Analytics Data - Jan 2011". Default value is an
  // empty string.
  string display_name = 3;

  // Entry description, which can consist of several sentences or paragraphs
  // that describe entry contents. Default value is an empty string.
  string description = 4;

  // Schema of the entry. An entry might not have any schema attached to it.
  Schema schema = 5;

  // Output only. Timestamps about the underlying resource, not about this Data
  // Catalog entry. Output only when Entry is of type in the EntryType enum. For
  // entries with user_specified_type, this field is optional and defaults to an
  // empty timestamp.
  SystemTimestamps source_system_timestamps = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Statistics on the usage level of the resource.
  UsageSignal usage_signal = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// EntryGroup Metadata.
// An EntryGroup resource represents a logical grouping of zero or more
// Data Catalog [Entry][google.cloud.datacatalog.v1beta1.Entry] resources.
message EntryGroup {
  option (google.api.resource) = {
    type: "datacatalog.googleapis.com/EntryGroup"
    pattern: "projects/{project}/locations/{location}/entryGroups/{entry_group}"
  };

  // Identifier. The resource name of the entry group in URL format. Example:
  //
  // * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
  //
  // Note that this EntryGroup and its child resources may not actually be
  // stored in the location in this name.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // A short name to identify the entry group, for example,
  // "analytics data - jan 2011". Default value is an empty string.
  string display_name = 2;

  // Entry group description, which can consist of several sentences or
  // paragraphs that describe entry group contents. Default value is an empty
  // string.
  string description = 3;

  // Output only. Timestamps about this EntryGroup. Default value is empty
  // timestamps.
  SystemTimestamps data_catalog_timestamps = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [CreateTagTemplate][google.cloud.datacatalog.v1beta1.DataCatalog.CreateTagTemplate].
message CreateTagTemplateRequest {
  // Required. The name of the project and the template location
  // [region](https://cloud.google.com/data-catalog/docs/concepts/regions.
  //
  // Example:
  //
  // * projects/{project_id}/locations/us-central1
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/TagTemplate"
    }
  ];

  // Required. The id of the tag template to create.
  string tag_template_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The tag template to create.
  TagTemplate tag_template = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [GetTagTemplate][google.cloud.datacatalog.v1beta1.DataCatalog.GetTagTemplate].
message GetTagTemplateRequest {
  // Required. The name of the tag template. Example:
  //
  // * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplate"
    }
  ];
}

// Request message for
// [UpdateTagTemplate][google.cloud.datacatalog.v1beta1.DataCatalog.UpdateTagTemplate].
message UpdateTagTemplateRequest {
  // Required. The template to update. The "name" field must be set.
  TagTemplate tag_template = 1 [(google.api.field_behavior) = REQUIRED];

  // Names of fields whose values to overwrite on a tag template. Currently,
  // only `display_name` can be overwritten.
  //
  // In general, if this parameter is absent or empty, all modifiable fields
  // are overwritten. If such fields are non-required and omitted in the
  // request body, their values are emptied.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [DeleteTagTemplate][google.cloud.datacatalog.v1beta1.DataCatalog.DeleteTagTemplate].
message DeleteTagTemplateRequest {
  // Required. The name of the tag template to delete. Example:
  //
  // * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplate"
    }
  ];

  // Required. Currently, this field must always be set to `true`.
  // This confirms the deletion of any possible tags using this template.
  // `force = false` will be supported in the future.
  bool force = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [CreateTag][google.cloud.datacatalog.v1beta1.DataCatalog.CreateTag].
message CreateTagRequest {
  // Required. The name of the resource to attach this tag to. Tags can be
  // attached to
  //  Entries. Example:
  //
  //  * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}
  //
  //  Note that this Tag and its child resources may not actually be stored in
  //  the location in this name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Tag"
    }
  ];

  // Required. The tag to create.
  Tag tag = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [UpdateTag][google.cloud.datacatalog.v1beta1.DataCatalog.UpdateTag].
message UpdateTagRequest {
  // Required. The updated tag. The "name" field must be set.
  Tag tag = 1 [(google.api.field_behavior) = REQUIRED];

  // Note: Currently, this parameter can only take `"fields"` as value.
  //
  // Names of fields whose values to overwrite on a tag. Currently, a tag has
  // the only modifiable field with the name `fields`.
  //
  // In general, if this parameter is absent or empty, all modifiable fields
  // are overwritten. If such fields are non-required and omitted in the
  // request body, their values are emptied.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [DeleteTag][google.cloud.datacatalog.v1beta1.DataCatalog.DeleteTag].
message DeleteTagRequest {
  // Required. The name of the tag to delete. Example:
  //
  // * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}/tags/{tag_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Tag"
    }
  ];
}

// Request message for
// [CreateTagTemplateField][google.cloud.datacatalog.v1beta1.DataCatalog.CreateTagTemplateField].
message CreateTagTemplateFieldRequest {
  // Required. The name of the project and the template location
  // [region](https://cloud.google.com/data-catalog/docs/concepts/regions).
  //
  // Example:
  //
  // * projects/{project_id}/locations/us-central1/tagTemplates/{tag_template_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplate"
    }
  ];

  // Required. The ID of the tag template field to create.
  // Field ids can contain letters (both uppercase and lowercase), numbers
  // (0-9), underscores (_) and dashes (-). Field IDs must be at least 1
  // character long and at most 128 characters long. Field IDs must also be
  // unique within their template.
  string tag_template_field_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The tag template field to create.
  TagTemplateField tag_template_field = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [UpdateTagTemplateField][google.cloud.datacatalog.v1beta1.DataCatalog.UpdateTagTemplateField].
message UpdateTagTemplateFieldRequest {
  // Required. The name of the tag template field. Example:
  //
  // * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{tag_template_field_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplateField"
    }
  ];

  // Required. The template to update.
  TagTemplateField tag_template_field = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Names of fields whose values to overwrite on an individual field
  // of a tag template. The following fields are modifiable:
  //
  //   * `display_name`
  //   * `type.enum_type`
  //   * `is_required`
  //
  // If this parameter is absent or empty, all modifiable fields
  // are overwritten. If such fields are non-required and omitted in the request
  // body, their values are emptied with one exception: when updating an enum
  // type, the provided values are merged with the existing values. Therefore,
  // enum values can only be added, existing enum values cannot be deleted or
  // renamed.
  //
  // Additionally, updating a template field from optional to required is
  // *not* allowed.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [RenameTagTemplateField][google.cloud.datacatalog.v1beta1.DataCatalog.RenameTagTemplateField].
message RenameTagTemplateFieldRequest {
  // Required. The name of the tag template. Example:
  //
  // * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{tag_template_field_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplateField"
    }
  ];

  // Required. The new ID of this tag template field. For example,
  // `my_new_field`.
  string new_tag_template_field_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [RenameTagTemplateFieldEnumValue][google.cloud.datacatalog.v1.DataCatalog.RenameTagTemplateFieldEnumValue].
message RenameTagTemplateFieldEnumValueRequest {
  // Required. The name of the enum field value. Example:
  //
  // * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{tag_template_field_id}/enumValues/{enum_value_display_name}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplateFieldEnumValue"
    }
  ];

  // Required. The new display name of the enum value. For example,
  // `my_new_enum_value`.
  string new_enum_value_display_name = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [DeleteTagTemplateField][google.cloud.datacatalog.v1beta1.DataCatalog.DeleteTagTemplateField].
message DeleteTagTemplateFieldRequest {
  // Required. The name of the tag template field to delete. Example:
  //
  // * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{tag_template_field_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplateField"
    }
  ];

  // Required. Currently, this field must always be set to `true`.
  // This confirms the deletion of this field from any tags using this field.
  // `force = false` will be supported in the future.
  bool force = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ListTags][google.cloud.datacatalog.v1beta1.DataCatalog.ListTags].
message ListTagsRequest {
  // Required. The name of the Data Catalog resource to list the tags of. The
  // resource could be an [Entry][google.cloud.datacatalog.v1beta1.Entry] or an
  // [EntryGroup][google.cloud.datacatalog.v1beta1.EntryGroup].
  //
  // Examples:
  //
  // * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
  // * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Tag"
    }
  ];

  // The maximum number of tags to return. Default is 10. Max limit is 1000.
  int32 page_size = 2;

  // Token that specifies which page is requested. If empty, the first page is
  // returned.
  string page_token = 3;
}

// Response message for
// [ListTags][google.cloud.datacatalog.v1beta1.DataCatalog.ListTags].
message ListTagsResponse {
  // [Tag][google.cloud.datacatalog.v1beta1.Tag] details.
  repeated Tag tags = 1;

  // Token to retrieve the next page of results. It is set to empty if no items
  // remain in results.
  string next_page_token = 2;
}

// Request message for
// [ListEntries][google.cloud.datacatalog.v1beta1.DataCatalog.ListEntries].
message ListEntriesRequest {
  // Required. The name of the entry group that contains the entries, which can
  // be provided in URL format. Example:
  //
  // * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // The maximum number of items to return. Default is 10. Max limit is 1000.
  // Throws an invalid argument for `page_size > 1000`.
  int32 page_size = 2;

  // Token that specifies which page is requested. If empty, the first page is
  // returned.
  string page_token = 3;

  // The fields to return for each Entry. If not set or empty, all
  // fields are returned.
  // For example, setting read_mask to contain only one path "name" will cause
  // ListEntries to return a list of Entries with only "name" field.
  google.protobuf.FieldMask read_mask = 4;
}

// Response message for
// [ListEntries][google.cloud.datacatalog.v1beta1.DataCatalog.ListEntries].
message ListEntriesResponse {
  // Entry details.
  repeated Entry entries = 1;

  // Token to retrieve the next page of results. It is set to empty if no items
  // remain in results.
  string next_page_token = 2;
}

// Entry resources in Data Catalog can be of different types e.g. a BigQuery
// Table entry is of type `TABLE`. This enum describes all the possible types
// Data Catalog contains.
enum EntryType {
  // Default unknown type.
  ENTRY_TYPE_UNSPECIFIED = 0;

  // Output only. The type of entry that has a GoogleSQL schema, including
  // logical views.
  TABLE = 2;

  // Output only. The type of models.
  // https://cloud.google.com/bigquery-ml/docs/bigqueryml-intro
  MODEL = 5;

  // Output only. An entry type which is used for streaming entries. Example:
  // Pub/Sub topic.
  DATA_STREAM = 3;

  // An entry type which is a set of files or objects. Example:
  // Cloud Storage fileset.
  FILESET = 4;
}
