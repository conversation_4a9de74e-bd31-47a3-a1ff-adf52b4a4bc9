// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1beta1;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1Beta1";
option go_package = "cloud.google.com/go/datacatalog/apiv1beta1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1beta1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1beta1";
option ruby_package = "Google::Cloud::DataCatalog::V1beta1";

// Detailed counts on the entry's usage.
// Caveats:
// - Only BigQuery tables have usage stats
// - The usage stats only include BigQuery query jobs
// - The usage stats might be underestimated, e.g. wildcard table references
// are not yet counted in usage computation
// https://cloud.google.com/bigquery/docs/querying-wildcard-tables
message UsageStats {
  // The number of times that the underlying entry was successfully used.
  float total_completions = 1;

  // The number of times that the underlying entry was attempted to be used
  // but failed.
  float total_failures = 2;

  // The number of times that the underlying entry was attempted to be used
  // but was cancelled by the user.
  float total_cancellations = 3;

  // Total time spent (in milliseconds) during uses the resulted in completions.
  float total_execution_time_for_completions_millis = 4;
}

// The set of all usage signals that we store in Data Catalog.
message UsageSignal {
  // The timestamp of the end of the usage statistics duration.
  google.protobuf.Timestamp update_time = 1;

  // Usage statistics over each of the pre-defined time ranges, supported
  // strings for time ranges are {"24H", "7D", "30D"}.
  map<string, UsageStats> usage_within_time_range = 2;
}
