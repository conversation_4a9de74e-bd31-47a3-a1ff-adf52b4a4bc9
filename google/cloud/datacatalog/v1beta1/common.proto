// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1beta1;

option csharp_namespace = "Google.Cloud.DataCatalog.V1Beta1";
option go_package = "cloud.google.com/go/datacatalog/apiv1beta1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1beta1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1beta1";
option ruby_package = "Google::Cloud::DataCatalog::V1beta1";

// This enum describes all the possible systems that Data Catalog integrates
// with.
enum IntegratedSystem {
  // Default unknown system.
  INTEGRATED_SYSTEM_UNSPECIFIED = 0;

  // BigQuery.
  BIGQUERY = 1;

  // Cloud Pub/Sub.
  CLOUD_PUBSUB = 2;
}

// This enum describes all the systems that manage
// Taxonomy and PolicyTag resources in DataCatalog.
enum ManagingSystem {
  // Default value
  MANAGING_SYSTEM_UNSPECIFIED = 0;

  // Dataplex.
  MANAGING_SYSTEM_DATAPLEX = 1;

  // Other
  MANAGING_SYSTEM_OTHER = 2;
}
