// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_outer_classname = "DataSourceProto";
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Physical location of an entry.
message DataSource {
  // Name of a service that stores the data.
  enum Service {
    // Default unknown service.
    SERVICE_UNSPECIFIED = 0;

    // Google Cloud Storage service.
    CLOUD_STORAGE = 1;

    // BigQuery service.
    BIGQUERY = 2;
  }

  // Service that physically stores the data.
  Service service = 1;

  // Full name of a resource as defined by the service. For example:
  //
  // `//bigquery.googleapis.com/projects/{PROJECT_ID}/locations/{LOCATION}/datasets/{DATASET_ID}/tables/{TABLE_ID}`
  string resource = 2;

  // Output only. Data Catalog entry name, if applicable.
  string source_entry = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  oneof properties {
    // Detailed properties of the underlying storage.
    StorageProperties storage_properties = 4;
  }
}

// Details the properties of the underlying storage.
message StorageProperties {
  // Patterns to identify a set of files for this fileset.
  //
  // Examples of a valid `file_pattern`:
  //
  //  * `gs://bucket_name/dir/*`: matches all files in the `bucket_name/dir`
  //                              directory
  //  * `gs://bucket_name/dir/**`: matches all files in the `bucket_name/dir`
  //                               and all subdirectories recursively
  //  * `gs://bucket_name/file*`: matches files prefixed by `file` in
  //                              `bucket_name`
  //  * `gs://bucket_name/??.txt`: matches files with two characters followed by
  //                               `.txt` in `bucket_name`
  //  * `gs://bucket_name/[aeiou].txt`: matches files that contain a single
  //                                    vowel character followed by `.txt` in
  //                                    `bucket_name`
  //  * `gs://bucket_name/[a-m].txt`: matches files that contain `a`, `b`, ...
  //                                  or `m` followed by `.txt` in `bucket_name`
  //  * `gs://bucket_name/a/*/b`: matches all files in `bucket_name` that match
  //                              the `a/*/b` pattern, such as `a/c/b`, `a/d/b`
  //  * `gs://another_bucket/a.txt`: matches `gs://another_bucket/a.txt`
  repeated string file_pattern = 1;

  // File type in MIME format, for example, `text/plain`.
  string file_type = 2;
}
