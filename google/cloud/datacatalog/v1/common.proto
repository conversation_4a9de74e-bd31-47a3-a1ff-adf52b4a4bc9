// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Entry metadata relevant only to the user and private to them.
message PersonalDetails {
  // True if the entry is starred by the user; false otherwise.
  bool starred = 1;

  // Set if the entry is starred; unset otherwise.
  google.protobuf.Timestamp star_time = 2;
}

// This enum lists all the systems that Data Catalog integrates with.
enum IntegratedSystem {
  // Default unknown system.
  INTEGRATED_SYSTEM_UNSPECIFIED = 0;

  // BigQuery.
  BIGQUERY = 1;

  // Cloud Pub/Sub.
  CLOUD_PUBSUB = 2;

  // Dataproc Metastore.
  DATAPROC_METASTORE = 3;

  // Dataplex.
  DATAPLEX = 4;

  // Cloud Spanner
  CLOUD_SPANNER = 6;

  // Cloud Bigtable
  CLOUD_BIGTABLE = 7;

  // Cloud Sql
  CLOUD_SQL = 8;

  // Looker
  LOOKER = 9;

  // Vertex AI
  VERTEX_AI = 10;
}

// This enum describes all the systems that manage
// Taxonomy and PolicyTag resources in DataCatalog.
enum ManagingSystem {
  // Default value
  MANAGING_SYSTEM_UNSPECIFIED = 0;

  // Dataplex.
  MANAGING_SYSTEM_DATAPLEX = 1;

  // Other
  MANAGING_SYSTEM_OTHER = 2;
}
