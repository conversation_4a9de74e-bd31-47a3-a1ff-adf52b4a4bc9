// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Describes a BigQuery table.
message BigQueryTableSpec {
  // Output only. The table source type.
  TableSourceType table_source_type = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only.
  oneof type_spec {
    // Table view specification. Populated only if
    // the `table_source_type` is `BIGQUERY_VIEW`.
    ViewSpec view_spec = 2;

    // Specification of a BigQuery table. Populated only if
    // the `table_source_type` is `BIGQUERY_TABLE`.
    TableSpec table_spec = 3;
  }
}

// Table source type.
enum TableSourceType {
  // Default unknown type.
  TABLE_SOURCE_TYPE_UNSPECIFIED = 0;

  // Table view.
  BIGQUERY_VIEW = 2;

  // BigQuery native table.
  BIGQUERY_TABLE = 5;

  // BigQuery materialized view.
  BIGQUERY_MATERIALIZED_VIEW = 7;
}

// Table view specification.
message ViewSpec {
  // Output only. The query that defines the table view.
  string view_query = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Normal BigQuery table specification.
message TableSpec {
  // Output only. If the table is date-sharded, that is, it matches the
  // `[prefix]YYYYMMDD` name pattern, this field is the Data Catalog resource
  // name of the date-sharded grouped entry. For example:
  //
  // `projects/{PROJECT_ID}/locations/{LOCATION}/entrygroups/{ENTRY_GROUP_ID}/entries/{ENTRY_ID}`.
  //
  // Otherwise, `grouped_entry` is empty.
  string grouped_entry = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];
}

// Specification for a group of BigQuery tables with the `[prefix]YYYYMMDD` name
// pattern.
//
// For more information, see [Introduction to partitioned tables]
// (https://cloud.google.com/bigquery/docs/partitioned-tables#partitioning_versus_sharding).
message BigQueryDateShardedSpec {
  // Output only. The Data Catalog resource name of the dataset entry the
  // current table belongs to. For example:
  //
  // `projects/{PROJECT_ID}/locations/{LOCATION}/entrygroups/{ENTRY_GROUP_ID}/entries/{ENTRY_ID}`.
  string dataset = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];

  // Output only. The table name prefix of the shards.
  //
  // The name of any given shard is `[table_prefix]YYYYMMDD`.
  // For example, for the `MyTable20180101` shard, the
  // `table_prefix` is `MyTable`.
  string table_prefix = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total number of shards.
  int64 shard_count = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. BigQuery resource name of the latest shard.
  string latest_shard_resource = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}
