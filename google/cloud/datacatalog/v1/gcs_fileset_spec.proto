// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/field_behavior.proto";
import "google/cloud/datacatalog/v1/timestamps.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Describes a Cloud Storage fileset entry.
message GcsFilesetSpec {
  // Required. Patterns to identify a set of files in Google Cloud Storage.
  //
  // For more information, see [Wildcard Names]
  // (https://cloud.google.com/storage/docs/gsutil/addlhelp/WildcardNames).
  //
  // Note: Currently, bucket wildcards are not supported.
  //
  // Examples of valid `file_patterns`:
  //
  //  * `gs://bucket_name/dir/*`: matches all files in `bucket_name/dir`
  //                              directory
  //  * `gs://bucket_name/dir/**`: matches all files in `bucket_name/dir`
  //                               and all subdirectories
  //  * `gs://bucket_name/file*`: matches files prefixed by `file` in
  //                              `bucket_name`
  //  * `gs://bucket_name/??.txt`: matches files with two characters followed by
  //                               `.txt` in `bucket_name`
  //  * `gs://bucket_name/[aeiou].txt`: matches files that contain a single
  //                                    vowel character followed by `.txt` in
  //                                    `bucket_name`
  //  * `gs://bucket_name/[a-m].txt`: matches files that contain `a`, `b`, ...
  //                                  or `m` followed by `.txt` in `bucket_name`
  //  * `gs://bucket_name/a/*/b`: matches all files in `bucket_name` that match
  //                              the `a/*/b` pattern, such as `a/c/b`, `a/d/b`
  //  * `gs://another_bucket/a.txt`: matches `gs://another_bucket/a.txt`
  //
  // You can combine wildcards to match complex sets of files, for example:
  //
  // `gs://bucket_name/[a-m]??.j*g`
  repeated string file_patterns = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Sample files contained in this fileset, not all files
  // contained in this fileset are represented here.
  repeated GcsFileSpec sample_gcs_file_specs = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Specification of a single file in Cloud Storage.
message GcsFileSpec {
  // Required. Full file path. Example: `gs://bucket_name/a/b.txt`.
  string file_path = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Creation, modification, and expiration timestamps of a Cloud
  // Storage file.
  SystemTimestamps gcs_timestamps = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. File size in bytes.
  int64 size_bytes = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}
