// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Timestamps associated with this resource in a particular system.
message SystemTimestamps {
  // Creation timestamp of the resource within the given system.
  google.protobuf.Timestamp create_time = 1;

  // Timestamp of the last modification of the resource or its metadata within
  // a given system.
  //
  // Note: Depending on the source system, not every modification updates this
  // timestamp.
  // For example, BigQuery timestamps every metadata modification but not data
  // or permission changes.
  google.protobuf.Timestamp update_time = 2;

  // Output only. Expiration timestamp of the resource within the given system.
  //
  // Currently only applicable to BigQuery resources.
  google.protobuf.Timestamp expire_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
