// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/datacatalog/v1/policytagmanager.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_outer_classname = "PolicyTagManagerSerializationProto";
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Policy Tag Manager Serialization API service allows you to manipulate
// your policy tags and taxonomies in a serialized format.
//
// Taxonomy is a hierarchical group of policy tags.
service PolicyTagManagerSerialization {
  option (google.api.default_host) = "datacatalog.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Replaces (updates) a taxonomy and all its policy tags.
  //
  // The taxonomy and its entire hierarchy of policy tags must be
  // represented literally by `SerializedTaxonomy` and the nested
  // `SerializedPolicyTag` messages.
  //
  // This operation automatically does the following:
  //
  // - Deletes the existing policy tags that are missing from the
  //   `SerializedPolicyTag`.
  // - Creates policy tags that don't have resource names. They are considered
  //   new.
  // - Updates policy tags with valid resources names accordingly.
  rpc ReplaceTaxonomy(ReplaceTaxonomyRequest) returns (Taxonomy) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/taxonomies/*}:replace"
      body: "*"
    };
  }

  // Creates new taxonomies (including their policy tags) in a given project
  // by importing from inlined or cross-regional sources.
  //
  // For a cross-regional source, new taxonomies are created by copying
  // from a source in another region.
  //
  // For an inlined source, taxonomies and policy tags are created in bulk using
  // nested protocol buffer structures.
  rpc ImportTaxonomies(ImportTaxonomiesRequest)
      returns (ImportTaxonomiesResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/taxonomies:import"
      body: "*"
    };
  }

  // Exports taxonomies in the requested type and returns them,
  // including their policy tags. The requested taxonomies must belong to the
  // same project.
  //
  // This method generates `SerializedTaxonomy` protocol buffers with nested
  // policy tags that can be used as input for `ImportTaxonomies` calls.
  rpc ExportTaxonomies(ExportTaxonomiesRequest)
      returns (ExportTaxonomiesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/taxonomies:export"
    };
  }
}

// A nested protocol buffer that represents a taxonomy and the hierarchy of its
// policy tags. Used for taxonomy replacement, import, and
// export.
message SerializedTaxonomy {
  // Required. Display name of the taxonomy. At most 200 bytes when encoded in
  // UTF-8.
  string display_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Description of the serialized taxonomy. At most 2000 bytes when
  // encoded in UTF-8. If not set, defaults to an empty description.
  string description = 2;

  // Top level policy tags associated with the taxonomy, if any.
  repeated SerializedPolicyTag policy_tags = 3;

  // A list of policy types that are activated per taxonomy.
  repeated Taxonomy.PolicyType activated_policy_types = 4;
}

// A nested protocol buffer that represents a policy tag and all its
// descendants.
message SerializedPolicyTag {
  // Resource name of the policy tag.
  //
  // This field is ignored when calling `ImportTaxonomies`.
  string policy_tag = 1;

  // Required. Display name of the policy tag. At most 200 bytes when encoded
  // in UTF-8.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Description of the serialized policy tag. At most
  // 2000 bytes when encoded in UTF-8. If not set, defaults to an
  // empty description.
  string description = 3;

  // Children of the policy tag, if any.
  repeated SerializedPolicyTag child_policy_tags = 4;
}

// Request message for
// [ReplaceTaxonomy][google.cloud.datacatalog.v1.PolicyTagManagerSerialization.ReplaceTaxonomy].
message ReplaceTaxonomyRequest {
  // Required. Resource name of the taxonomy to update.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Taxonomy"
    }
  ];

  // Required. Taxonomy to update along with its child policy tags.
  SerializedTaxonomy serialized_taxonomy = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ImportTaxonomies][google.cloud.datacatalog.v1.PolicyTagManagerSerialization.ImportTaxonomies].
message ImportTaxonomiesRequest {
  // Required. Resource name of project that the imported taxonomies will belong
  // to.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Taxonomy"
    }
  ];

  // Source taxonomies to import.
  oneof source {
    // Inline source taxonomy to import.
    InlineSource inline_source = 2;

    // Cross-regional source taxonomy to import.
    CrossRegionalSource cross_regional_source = 3;
  }
}

// Inline source containing taxonomies to import.
message InlineSource {
  // Required. Taxonomies to import.
  repeated SerializedTaxonomy taxonomies = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Cross-regional source used to import an existing taxonomy into a different
// region.
message CrossRegionalSource {
  // Required. The resource name of the source taxonomy to import.
  string taxonomy = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Taxonomy"
    }
  ];
}

// Response message for
// [ImportTaxonomies][google.cloud.datacatalog.v1.PolicyTagManagerSerialization.ImportTaxonomies].
message ImportTaxonomiesResponse {
  // Imported taxonomies.
  repeated Taxonomy taxonomies = 1;
}

// Request message for
// [ExportTaxonomies][google.cloud.datacatalog.v1.PolicyTagManagerSerialization.ExportTaxonomies].
message ExportTaxonomiesRequest {
  // Required. Resource name of the project that the exported taxonomies belong
  // to.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Taxonomy"
    }
  ];

  // Required. Resource names of the taxonomies to export.
  repeated string taxonomies = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Taxonomy"
    }
  ];

  // Required. Export destination for taxonomies.
  oneof destination {
    // Serialized export taxonomies that contain all the policy
    // tags as nested protocol buffers.
    bool serialized_taxonomies = 3;
  }
}

// Response message for
// [ExportTaxonomies][google.cloud.datacatalog.v1.PolicyTagManagerSerialization.ExportTaxonomies].
message ExportTaxonomiesResponse {
  // List of taxonomies and policy tags as nested protocol buffers.
  repeated SerializedTaxonomy taxonomies = 1;
}
