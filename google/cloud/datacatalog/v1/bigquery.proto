// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_outer_classname = "BigQueryProto";
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Specification for the BigQuery connection.
message BigQueryConnectionSpec {
  // The type of the BigQuery connection.
  enum ConnectionType {
    // Unspecified type.
    CONNECTION_TYPE_UNSPECIFIED = 0;

    // Cloud SQL connection.
    CLOUD_SQL = 1;
  }

  // The type of the BigQuery connection.
  ConnectionType connection_type = 1;

  oneof connection_spec {
    // Specification for the BigQuery connection to a Cloud SQL instance.
    CloudSqlBigQueryConnectionSpec cloud_sql = 2;
  }

  // True if there are credentials attached to the BigQuery connection; false
  // otherwise.
  bool has_credential = 3;
}

// Specification for the BigQuery connection to a Cloud SQL instance.
message CloudSqlBigQueryConnectionSpec {
  // Supported Cloud SQL database types.
  enum DatabaseType {
    // Unspecified database type.
    DATABASE_TYPE_UNSPECIFIED = 0;

    // Cloud SQL for PostgreSQL.
    POSTGRES = 1;

    // Cloud SQL for MySQL.
    MYSQL = 2;
  }

  // Cloud SQL instance ID in the format of `project:location:instance`.
  string instance_id = 1;

  // Database name.
  string database = 2;

  // Type of the Cloud SQL database.
  DatabaseType type = 3;
}

// Fields specific for BigQuery routines.
message BigQueryRoutineSpec {
  // Paths of the imported libraries.
  repeated string imported_libraries = 1;
}
