// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/field_behavior.proto";
import "google/cloud/datacatalog/v1/datacatalog.proto";
import "google/cloud/datacatalog/v1/tags.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_outer_classname = "DumpContentProto";
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Wrapper containing Entry and information about Tags
// that should and should not be attached to it.
message TaggedEntry {
  // Required. Entry to be ingested.
  oneof entry {
    // Non-encrypted Data Catalog v1 Entry.
    Entry v1_entry = 1;
  }

  // Optional. Tags that should be ingested into the Data Catalog.
  // Caller should populate template name, column and fields.
  repeated Tag present_tags = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Tags that should be deleted from the Data Catalog.
  // Caller should populate template name and column only.
  repeated Tag absent_tags = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Wrapper for any item that can be contained in the dump.
message DumpItem {
  oneof item {
    // Entry and its tags.
    TaggedEntry tagged_entry = 1;
  }
}
