// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_outer_classname = "PhysicalSchemaProto";
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Native schema used by a resource represented as an entry. Used by query
// engines for deserializing and parsing source data.
message PhysicalSchema {
  // Schema in Avro JSON format.
  message AvroSchema {
    // JSON source of the Avro schema.
    string text = 1;
  }

  // Schema in Thrift format.
  message ThriftSchema {
    // Thrift IDL source of the schema.
    string text = 1;
  }

  // Schema in protocol buffer format.
  message ProtobufSchema {
    // Protocol buffer source of the schema.
    string text = 1;
  }

  // Marks a Parquet-encoded data source.
  message ParquetSchema {}

  // Marks an ORC-encoded data source.
  message OrcSchema {}

  // Marks a CSV-encoded data source.
  message CsvSchema {}

  oneof schema {
    // Schema in Avro JSON format.
    AvroSchema avro = 1;

    // Schema in Thrift format.
    ThriftSchema thrift = 2;

    // Schema in protocol buffer format.
    ProtobufSchema protobuf = 3;

    // Marks a Parquet-encoded data source.
    ParquetSchema parquet = 4;

    // Marks an ORC-encoded data source.
    OrcSchema orc = 5;

    // Marks a CSV-encoded data source.
    CsvSchema csv = 6;
  }
}
