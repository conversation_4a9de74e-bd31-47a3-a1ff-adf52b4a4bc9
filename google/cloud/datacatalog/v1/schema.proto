// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Represents a schema, for example, a BigQuery, GoogleSQL, or Avro schema.
message Schema {
  // The unified GoogleSQL-like schema of columns.
  //
  // The overall maximum number of columns and nested columns is 10,000.
  // The maximum nested depth is 15 levels.
  repeated ColumnSchema columns = 2;
}

// A column within a schema. Columns can be nested inside
// other columns.
message ColumnSchema {
  // Specifies inclusion of the column in an index
  enum IndexingType {
    // Unspecified.
    INDEXING_TYPE_UNSPECIFIED = 0;

    // Column not a part of an index.
    INDEXING_TYPE_NONE = 1;

    // Column Part of non unique index.
    INDEXING_TYPE_NON_UNIQUE = 2;

    // Column part of unique index.
    INDEXING_TYPE_UNIQUE = 3;

    // Column part of the primary key.
    INDEXING_TYPE_PRIMARY_KEY = 4;
  }

  // Column info specific to Looker System.
  message LookerColumnSpec {
    // Column type in Looker.
    enum LookerColumnType {
      // Unspecified.
      LOOKER_COLUMN_TYPE_UNSPECIFIED = 0;

      // Dimension.
      DIMENSION = 1;

      // Dimension group - parent for Dimension.
      DIMENSION_GROUP = 2;

      // Filter.
      FILTER = 3;

      // Measure.
      MEASURE = 4;

      // Parameter.
      PARAMETER = 5;
    }

    // Looker specific column type of this column.
    LookerColumnType type = 1;
  }

  // Represents the type of a field element.
  message FieldElementType {
    // Required. The type of a field element. See
    // [ColumnSchema.type][google.cloud.datacatalog.v1.ColumnSchema.type].
    string type = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. Name of the column.
  //
  // Must be a UTF-8 string without dots (.).
  // The maximum size is 64 bytes.
  string column = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. Type of the column.
  //
  // Must be a UTF-8 string with the maximum size of 128 bytes.
  string type = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description of the column. Default value is an empty string.
  //
  // The description must be a UTF-8 string with the maximum size of 2000
  // bytes.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A column's mode indicates whether values in this column are
  // required, nullable, or repeated.
  //
  // Only `NULLABLE`, `REQUIRED`, and `REPEATED` values are supported.
  // Default mode is `NULLABLE`.
  string mode = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Default value for the column.
  string default_value = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Ordinal position
  int32 ordinal_position = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Most important inclusion of this column.
  IndexingType highest_indexing_type = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Schema of sub-columns. A column can have zero or more
  // sub-columns.
  repeated ColumnSchema subcolumns = 7 [(google.api.field_behavior) = OPTIONAL];

  // Information only applying for columns in Entries from a specific system.
  oneof system_spec {
    // Looker specific column info of this column.
    LookerColumnSpec looker_column_spec = 18;
  }

  // Optional. The subtype of the RANGE, if the type of this field is RANGE. If
  // the type is RANGE, this field is required. Possible values for the field
  // element type of a RANGE include:
  // * DATE
  // * DATETIME
  // * TIMESTAMP
  FieldElementType range_element_type = 19
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Garbage collection policy for the column or column family.
  // Applies to systems like Cloud Bigtable.
  string gc_rule = 11 [(google.api.field_behavior) = OPTIONAL];
}
