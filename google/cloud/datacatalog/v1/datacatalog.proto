// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/datacatalog/v1/bigquery.proto";
import "google/cloud/datacatalog/v1/common.proto";
import "google/cloud/datacatalog/v1/data_source.proto";
import "google/cloud/datacatalog/v1/dataplex_spec.proto";
import "google/cloud/datacatalog/v1/gcs_fileset_spec.proto";
import "google/cloud/datacatalog/v1/schema.proto";
import "google/cloud/datacatalog/v1/search.proto";
import "google/cloud/datacatalog/v1/table_spec.proto";
import "google/cloud/datacatalog/v1/tags.proto";
import "google/cloud/datacatalog/v1/timestamps.proto";
import "google/cloud/datacatalog/v1/usage.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";
option (google.api.resource_definition) = {
  type: "datacatalog.googleapis.com/TagTemplateFieldEnumValue"
  pattern: "projects/{project}/locations/{location}/tagTemplates/{tag_template}/fields/{tag_template_field_id}/enumValues/{enum_value_display_name}"
};

// Data Catalog API service allows you to discover, understand, and manage
// your data.
service DataCatalog {
  option (google.api.default_host) = "datacatalog.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Searches Data Catalog for multiple resources like entries and tags that
  // match a query.
  //
  // This is a [Custom Method]
  // (https://cloud.google.com/apis/design/custom_methods) that doesn't return
  // all information on a resource, only its ID and high level fields. To get
  // more information, you can subsequently call specific get methods.
  //
  // Note: Data Catalog search queries don't guarantee full recall. Results
  // that match your query might not be returned, even in subsequent
  // result pages. Additionally, returned (and not returned) results can vary
  // if you repeat search queries.
  //
  // For more information, see [Data Catalog search syntax]
  // (https://cloud.google.com/data-catalog/docs/how-to/search-reference).
  rpc SearchCatalog(SearchCatalogRequest) returns (SearchCatalogResponse) {
    option (google.api.http) = {
      post: "/v1/catalog:search"
      body: "*"
    };
    option (google.api.method_signature) = "scope,query";
  }

  // Creates an entry group.
  //
  // An entry group contains logically related entries together with [Cloud
  // Identity and Access Management](/data-catalog/docs/concepts/iam) policies.
  // These policies specify users who can create, edit, and view entries
  // within entry groups.
  //
  // Data Catalog automatically creates entry groups with names that start with
  // the `@` symbol for the following resources:
  //
  // * BigQuery entries (`@bigquery`)
  // * Pub/Sub topics (`@pubsub`)
  // * Dataproc Metastore services (`@dataproc_metastore_{SERVICE_NAME_HASH}`)
  //
  // You can create your own entry groups for Cloud Storage fileset entries
  // and custom entries together with the corresponding IAM policies.
  // User-created entry groups can't contain the `@` symbol, it is reserved
  // for automatically created groups.
  //
  // Entry groups, like entries, can be searched.
  //
  // A maximum of 10,000 entry groups may be created per organization across all
  // locations.
  //
  // You must enable the Data Catalog API in the project identified by
  // the `parent` parameter. For more information, see [Data Catalog resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc CreateEntryGroup(CreateEntryGroupRequest) returns (EntryGroup) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/entryGroups"
      body: "entry_group"
    };
    option (google.api.method_signature) = "parent,entry_group_id,entry_group";
  }

  // Gets an entry group.
  rpc GetEntryGroup(GetEntryGroupRequest) returns (EntryGroup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/entryGroups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.api.method_signature) = "name,read_mask";
  }

  // Updates an entry group.
  //
  // You must enable the Data Catalog API in the project identified by
  // the `entry_group.name` parameter. For more information, see [Data Catalog
  // resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc UpdateEntryGroup(UpdateEntryGroupRequest) returns (EntryGroup) {
    option (google.api.http) = {
      patch: "/v1/{entry_group.name=projects/*/locations/*/entryGroups/*}"
      body: "entry_group"
    };
    option (google.api.method_signature) = "entry_group";
    option (google.api.method_signature) = "entry_group,update_mask";
  }

  // Deletes an entry group.
  //
  // You must enable the Data Catalog API in the project
  // identified by the `name` parameter. For more information, see [Data Catalog
  // resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc DeleteEntryGroup(DeleteEntryGroupRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/entryGroups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists entry groups.
  rpc ListEntryGroups(ListEntryGroupsRequest)
      returns (ListEntryGroupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/entryGroups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates an entry.
  //
  // You can create entries only with 'FILESET', 'CLUSTER', 'DATA_STREAM',
  // or custom types. Data Catalog automatically creates entries with other
  // types during metadata ingestion from integrated systems.
  //
  // You must enable the Data Catalog API in the project identified by
  // the `parent` parameter. For more information, see [Data Catalog resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  //
  // An entry group can have a maximum of 100,000 entries.
  rpc CreateEntry(CreateEntryRequest) returns (Entry) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/entryGroups/*}/entries"
      body: "entry"
    };
    option (google.api.method_signature) = "parent,entry_id,entry";
  }

  // Updates an existing entry.
  //
  // You must enable the Data Catalog API in the project identified by
  // the `entry.name` parameter. For more information, see [Data Catalog
  // resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc UpdateEntry(UpdateEntryRequest) returns (Entry) {
    option (google.api.http) = {
      patch: "/v1/{entry.name=projects/*/locations/*/entryGroups/*/entries/*}"
      body: "entry"
    };
    option (google.api.method_signature) = "entry";
    option (google.api.method_signature) = "entry,update_mask";
  }

  // Deletes an existing entry.
  //
  // You can delete only the entries created by the
  // [CreateEntry][google.cloud.datacatalog.v1.DataCatalog.CreateEntry]
  // method.
  //
  // You must enable the Data Catalog API in the project identified by
  // the `name` parameter. For more information, see [Data Catalog
  // resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc DeleteEntry(DeleteEntryRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/entryGroups/*/entries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets an entry.
  rpc GetEntry(GetEntryRequest) returns (Entry) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/entryGroups/*/entries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets an entry by its target resource name.
  //
  // The resource name comes from the source Google Cloud Platform service.
  rpc LookupEntry(LookupEntryRequest) returns (Entry) {
    option (google.api.http) = {
      get: "/v1/entries:lookup"
    };
  }

  // Lists entries.
  //
  // Note: Currently, this method can list only custom entries.
  // To get a list of both custom and automatically created entries, use
  // [SearchCatalog][google.cloud.datacatalog.v1.DataCatalog.SearchCatalog].
  rpc ListEntries(ListEntriesRequest) returns (ListEntriesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/entryGroups/*}/entries"
    };
    option (google.api.method_signature) = "parent";
  }

  // Modifies entry overview, part of the business context of an
  // [Entry][google.cloud.datacatalog.v1.Entry].
  //
  // To call this method, you must have the `datacatalog.entries.updateOverview`
  // IAM permission on the corresponding project.
  rpc ModifyEntryOverview(ModifyEntryOverviewRequest) returns (EntryOverview) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/entryGroups/*/entries/*}:modifyEntryOverview"
      body: "*"
    };
  }

  // Modifies contacts, part of the business context of an
  // [Entry][google.cloud.datacatalog.v1.Entry].
  //
  // To call this method, you must have the `datacatalog.entries.updateContacts`
  // IAM permission on the corresponding project.
  rpc ModifyEntryContacts(ModifyEntryContactsRequest) returns (Contacts) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/entryGroups/*/entries/*}:modifyEntryContacts"
      body: "*"
    };
  }

  // Creates a tag template.
  //
  // You must enable the Data Catalog API in the project identified by the
  // `parent` parameter.
  // For more information, see [Data Catalog resource project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc CreateTagTemplate(CreateTagTemplateRequest) returns (TagTemplate) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/tagTemplates"
      body: "tag_template"
    };
    option (google.api.method_signature) =
        "parent,tag_template_id,tag_template";
  }

  // Gets a tag template.
  rpc GetTagTemplate(GetTagTemplateRequest) returns (TagTemplate) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/tagTemplates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a tag template.
  //
  // You can't update template fields with this method. These fields are
  // separate resources with their own create, update, and delete methods.
  //
  // You must enable the Data Catalog API in the project identified by
  // the `tag_template.name` parameter. For more information, see [Data Catalog
  // resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc UpdateTagTemplate(UpdateTagTemplateRequest) returns (TagTemplate) {
    option (google.api.http) = {
      patch: "/v1/{tag_template.name=projects/*/locations/*/tagTemplates/*}"
      body: "tag_template"
    };
    option (google.api.method_signature) = "tag_template";
    option (google.api.method_signature) = "tag_template,update_mask";
  }

  // Deletes a tag template and all tags that use it.
  //
  // You must enable the Data Catalog API in the project identified by
  // the `name` parameter. For more information, see [Data Catalog resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc DeleteTagTemplate(DeleteTagTemplateRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/tagTemplates/*}"
    };
    option (google.api.method_signature) = "name,force";
  }

  // Creates a field in a tag template.
  //
  // You must enable the Data Catalog API in the project identified by
  // the `parent` parameter. For more information, see [Data Catalog resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc CreateTagTemplateField(CreateTagTemplateFieldRequest)
      returns (TagTemplateField) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/tagTemplates/*}/fields"
      body: "tag_template_field"
    };
    option (google.api.method_signature) =
        "parent,tag_template_field_id,tag_template_field";
  }

  // Updates a field in a tag template.
  //
  // You can't update the field type with this method.
  //
  // You must enable the Data Catalog API in the project
  // identified by the `name` parameter. For more information, see [Data Catalog
  // resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc UpdateTagTemplateField(UpdateTagTemplateFieldRequest)
      returns (TagTemplateField) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/tagTemplates/*/fields/*}"
      body: "tag_template_field"
    };
    option (google.api.method_signature) = "name,tag_template_field";
    option (google.api.method_signature) =
        "name,tag_template_field,update_mask";
  }

  // Renames a field in a tag template.
  //
  // You must enable the Data Catalog API in the project identified by the
  // `name` parameter. For more information, see [Data Catalog resource project]
  // (https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc RenameTagTemplateField(RenameTagTemplateFieldRequest)
      returns (TagTemplateField) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/tagTemplates/*/fields/*}:rename"
      body: "*"
    };
    option (google.api.method_signature) = "name,new_tag_template_field_id";
  }

  // Renames an enum value in a tag template.
  //
  // Within a single enum field, enum values must be unique.
  rpc RenameTagTemplateFieldEnumValue(RenameTagTemplateFieldEnumValueRequest)
      returns (TagTemplateField) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/tagTemplates/*/fields/*/enumValues/*}:rename"
      body: "*"
    };
    option (google.api.method_signature) = "name,new_enum_value_display_name";
  }

  // Deletes a field in a tag template and all uses of this field from the tags
  // based on this template.
  //
  // You must enable the Data Catalog API in the project identified by
  // the `name` parameter. For more information, see [Data Catalog resource
  // project](https://cloud.google.com/data-catalog/docs/concepts/resource-project).
  rpc DeleteTagTemplateField(DeleteTagTemplateFieldRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/tagTemplates/*/fields/*}"
    };
    option (google.api.method_signature) = "name,force";
  }

  // Creates a tag and assigns it to:
  //
  // * An [Entry][google.cloud.datacatalog.v1.Entry] if the method name is
  //   `projects.locations.entryGroups.entries.tags.create`.
  // * Or [EntryGroup][google.cloud.datacatalog.v1.EntryGroup]if the method
  //   name is `projects.locations.entryGroups.tags.create`.
  //
  // Note: The project identified by the `parent` parameter for the [tag]
  // (https://cloud.google.com/data-catalog/docs/reference/rest/v1/projects.locations.entryGroups.entries.tags/create#path-parameters)
  // and the [tag template]
  // (https://cloud.google.com/data-catalog/docs/reference/rest/v1/projects.locations.tagTemplates/create#path-parameters)
  // used to create the tag must be in the same organization.
  rpc CreateTag(CreateTagRequest) returns (Tag) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/entryGroups/*/entries/*}/tags"
      body: "tag"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/entryGroups/*}/tags"
        body: "tag"
      }
    };
    option (google.api.method_signature) = "parent,tag";
  }

  // Updates an existing tag.
  rpc UpdateTag(UpdateTagRequest) returns (Tag) {
    option (google.api.http) = {
      patch: "/v1/{tag.name=projects/*/locations/*/entryGroups/*/entries/*/tags/*}"
      body: "tag"
      additional_bindings {
        patch: "/v1/{tag.name=projects/*/locations/*/entryGroups/*/tags/*}"
        body: "tag"
      }
    };
    option (google.api.method_signature) = "tag";
    option (google.api.method_signature) = "tag,update_mask";
  }

  // Deletes a tag.
  rpc DeleteTag(DeleteTagRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/entryGroups/*/entries/*/tags/*}"
      additional_bindings {
        delete: "/v1/{name=projects/*/locations/*/entryGroups/*/tags/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists tags assigned to an [Entry][google.cloud.datacatalog.v1.Entry].
  // The [columns][google.cloud.datacatalog.v1.Tag.column] in the response are
  // lowercased.
  rpc ListTags(ListTagsRequest) returns (ListTagsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/entryGroups/*/entries/*}/tags"
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/entryGroups/*}/tags"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // `ReconcileTags` creates or updates a list of tags on the entry.
  // If the
  // [ReconcileTagsRequest.force_delete_missing][google.cloud.datacatalog.v1.ReconcileTagsRequest.force_delete_missing]
  // parameter is set, the operation deletes tags not included in the input tag
  // list.
  //
  // `ReconcileTags` returns a [long-running operation]
  // [google.longrunning.Operation] resource that can be queried with
  // [Operations.GetOperation][google.longrunning.Operations.GetOperation]
  // to return [ReconcileTagsMetadata]
  // [google.cloud.datacatalog.v1.ReconcileTagsMetadata] and
  // a [ReconcileTagsResponse]
  // [google.cloud.datacatalog.v1.ReconcileTagsResponse] message.
  rpc ReconcileTags(ReconcileTagsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/entryGroups/*/entries/*}/tags:reconcile"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ReconcileTagsResponse"
      metadata_type: "ReconcileTagsMetadata"
    };
  }

  // Marks an [Entry][google.cloud.datacatalog.v1.Entry] as starred by
  // the current user. Starring information is private to each user.
  rpc StarEntry(StarEntryRequest) returns (StarEntryResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/entryGroups/*/entries/*}:star"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Marks an [Entry][google.cloud.datacatalog.v1.Entry] as NOT starred by
  // the current user. Starring information is private to each user.
  rpc UnstarEntry(UnstarEntryRequest) returns (UnstarEntryResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/entryGroups/*/entries/*}:unstar"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Sets an access control policy for a resource. Replaces any existing
  // policy.
  //
  // Supported resources are:
  //
  // - Tag templates
  // - Entry groups
  //
  // Note: This method sets policies only within Data Catalog and can't be
  // used to manage policies in BigQuery, Pub/Sub, Dataproc Metastore, and any
  // external Google Cloud Platform resources synced with the Data Catalog.
  //
  // To call this method, you must have the following Google IAM permissions:
  //
  // - `datacatalog.tagTemplates.setIamPolicy` to set policies on tag
  //   templates.
  // - `datacatalog.entryGroups.setIamPolicy` to set policies on entry groups.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/tagTemplates/*}:setIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1/{resource=projects/*/locations/*/entryGroups/*}:setIamPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Gets the access control policy for a resource.
  //
  // May return:
  //
  // * A`NOT_FOUND` error if the resource doesn't exist or you don't have the
  //   permission to view it.
  // * An empty policy if the resource exists but doesn't have a set policy.
  //
  // Supported resources are:
  //
  // - Tag templates
  // - Entry groups
  //
  // Note: This method doesn't get policies from Google Cloud Platform
  // resources ingested into Data Catalog.
  //
  // To call this method, you must have the following Google IAM permissions:
  //
  // - `datacatalog.tagTemplates.getIamPolicy` to get policies on tag
  //   templates.
  // - `datacatalog.entryGroups.getIamPolicy` to get policies on entry groups.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/tagTemplates/*}:getIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1/{resource=projects/*/locations/*/entryGroups/*}:getIamPolicy"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{resource=projects/*/locations/*/entryGroups/*/entries/*}:getIamPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource";
  }

  // Gets your permissions on a resource.
  //
  // Returns an empty set of permissions if the resource doesn't exist.
  //
  // Supported resources are:
  //
  // - Tag templates
  // - Entry groups
  //
  // Note: This method gets policies only within Data Catalog and can't be
  // used to get policies from BigQuery, Pub/Sub, Dataproc Metastore, and any
  // external Google Cloud Platform resources ingested into Data Catalog.
  //
  // No Google IAM permissions are required to call this method.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/tagTemplates/*}:testIamPermissions"
      body: "*"
      additional_bindings {
        post: "/v1/{resource=projects/*/locations/*/entryGroups/*}:testIamPermissions"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{resource=projects/*/locations/*/entryGroups/*/entries/*}:testIamPermissions"
        body: "*"
      }
    };
  }

  // Imports entries from a source, such as data previously dumped into a
  // Cloud Storage bucket, into Data Catalog. Import of entries
  // is a sync operation that reconciles the state of the third-party system
  // with the Data Catalog.
  //
  // `ImportEntries` accepts source data snapshots of a third-party system.
  // Snapshot should be delivered as a .wire or base65-encoded .txt file
  // containing a sequence of Protocol Buffer messages of
  // [DumpItem][google.cloud.datacatalog.v1.DumpItem] type.
  //
  // `ImportEntries` returns a [long-running operation]
  // [google.longrunning.Operation] resource that can be queried with
  // [Operations.GetOperation][google.longrunning.Operations.GetOperation]
  // to return
  // [ImportEntriesMetadata][google.cloud.datacatalog.v1.ImportEntriesMetadata]
  // and an
  // [ImportEntriesResponse][google.cloud.datacatalog.v1.ImportEntriesResponse]
  // message.
  rpc ImportEntries(ImportEntriesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/entryGroups/*}/entries:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ImportEntriesResponse"
      metadata_type: "ImportEntriesMetadata"
    };
  }

  // Sets the configuration related to the migration to Dataplex for an
  // organization or project.
  rpc SetConfig(SetConfigRequest) returns (MigrationConfig) {
    option (google.api.http) = {
      post: "/v1/{name=organizations/*/locations/*}:setConfig"
      body: "*"
      additional_bindings {
        post: "/v1/{name=projects/*/locations/*}:setConfig"
        body: "*"
      }
    };
  }

  // Retrieves the configuration related to the migration from Data Catalog to
  // Dataplex for a specific organization, including all the projects under it
  // which have a separate configuration set.
  rpc RetrieveConfig(RetrieveConfigRequest) returns (OrganizationConfig) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*}:retrieveConfig"
    };
  }

  // Retrieves the effective configuration related to the migration from Data
  // Catalog to Dataplex for a specific organization or project. If there is no
  // specific configuration set for the resource, the setting is checked
  // hierarchicahlly through the ancestors of the resource, starting from the
  // resource itself.
  rpc RetrieveEffectiveConfig(RetrieveEffectiveConfigRequest)
      returns (MigrationConfig) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*}:retrieveEffectiveConfig"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*}:retrieveEffectiveConfig"
      }
    };
  }
}

// Request message for
// [SearchCatalog][google.cloud.datacatalog.v1.DataCatalog.SearchCatalog].
message SearchCatalogRequest {
  // The criteria that select the subspace used for query matching.
  message Scope {
    // The list of organization IDs to search within.
    //
    // To find your organization ID, follow the steps from
    // [Creating and managing organizations]
    // (/resource-manager/docs/creating-managing-organization).
    repeated string include_org_ids = 2;

    // The list of project IDs to search within.
    //
    // For more information on the distinction between project names, IDs, and
    // numbers, see [Projects](/docs/overview/#projects).
    repeated string include_project_ids = 3;

    // If `true`, include Google Cloud public datasets in
    // search results. By default, they are excluded.
    //
    // See [Google Cloud Public Datasets](/public-datasets) for more
    // information.
    bool include_gcp_public_datasets = 7;

    // Optional. The list of locations to search within. If empty, all locations
    // are searched.
    //
    // Returns an error if any location in the list isn't one of the [Supported
    // regions](https://cloud.google.com/data-catalog/docs/concepts/regions#supported_regions).
    //
    // If a location is unreachable, its name is returned in the
    // `SearchCatalogResponse.unreachable` field. To get additional information
    // on the error, repeat the search request and set the location name as the
    // value of this parameter.
    repeated string restricted_locations = 16
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. If `true`, search only among starred entries.
    //
    // By default, all results are returned, starred or not.
    bool starred_only = 18 [(google.api.field_behavior) = OPTIONAL];

    // Optional. This field is deprecated. The search mechanism for public and
    // private tag templates is the same.
    bool include_public_tag_templates = 19
        [deprecated = true, (google.api.field_behavior) = OPTIONAL];
  }

  // Required. The scope of this search request.
  //
  // The `scope` is invalid if `include_org_ids`, `include_project_ids` are
  // empty AND `include_gcp_public_datasets` is set to `false`. In this case,
  // the request returns an error.
  Scope scope = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. The query string with a minimum of 3 characters and specific
  // syntax. For more information, see [Data Catalog search
  // syntax](https://cloud.google.com/data-catalog/docs/how-to/search-reference).
  //
  // An empty query string returns all data assets (in the specified scope)
  // that you have access to.
  //
  // A query string can be a simple `xyz` or qualified by predicates:
  //
  // * `name:x`
  // * `column:y`
  // * `description:z`
  string query = 1 [(google.api.field_behavior) = OPTIONAL];

  // Upper bound on the number of results you can get in a single response.
  //
  // Can't be negative or 0, defaults to 10 in this case.
  // The maximum number is 1000. If exceeded, throws an "invalid argument"
  // exception.
  int32 page_size = 2;

  // Optional. Pagination token that, if specified, returns the next page of
  // search results. If empty, returns the first page.
  //
  // This token is returned in the
  // [SearchCatalogResponse.next_page_token][google.cloud.datacatalog.v1.SearchCatalogResponse.next_page_token]
  // field of the response to a previous
  // [SearchCatalogRequest][google.cloud.datacatalog.v1.DataCatalog.SearchCatalog]
  // call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Specifies the order of results.
  //
  // Currently supported case-sensitive values are:
  //
  // * `relevance` that can only be descending
  // * `last_modified_timestamp [asc|desc]` with descending (`desc`) as default
  // * `default` that can only be descending
  //
  // Search queries don't guarantee full recall. Results that match your query
  // might not be returned, even in subsequent result pages. Additionally,
  // returned (and not returned) results can vary if you repeat search queries.
  // If you are experiencing recall issues and you don't have to fetch the
  // results in any specific order, consider setting this parameter to
  // `default`.
  //
  // If this parameter is omitted, it defaults to the descending `relevance`.
  string order_by = 5;

  // Optional. If set, use searchAll permission granted on organizations from
  // `include_org_ids` and projects from `include_project_ids` instead of the
  // fine grained per resource permissions when filtering the search results.
  // The only allowed `order_by` criteria for admin_search mode is `default`.
  // Using this flags guarantees a full recall of the search results.
  bool admin_search = 17 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [SearchCatalog][google.cloud.datacatalog.v1.DataCatalog.SearchCatalog].
message SearchCatalogResponse {
  // Search results.
  repeated SearchCatalogResult results = 1;

  // The approximate total number of entries matched by the query.
  int32 total_size = 2;

  // Pagination token that can be used in subsequent calls to retrieve the next
  // page of results.
  string next_page_token = 3;

  // Unreachable locations. Search results don't include data from those
  // locations.
  //
  // To get additional information on an error, repeat the search request and
  // restrict it to specific locations by setting the
  // `SearchCatalogRequest.scope.restricted_locations` parameter.
  repeated string unreachable = 6;
}

// Request message for
// [CreateEntryGroup][google.cloud.datacatalog.v1.DataCatalog.CreateEntryGroup].
message CreateEntryGroupRequest {
  // Required. The names of the project and location that the new entry group
  // belongs to.
  //
  // Note: The entry group itself and its child resources might not be
  // stored in the location specified in its name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // Required. The ID of the entry group to create.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9),
  // underscores (_), and must start with a letter or underscore.
  // The maximum size is 64 bytes when encoded in UTF-8.
  string entry_group_id = 3 [(google.api.field_behavior) = REQUIRED];

  // The entry group to create. Defaults to empty.
  EntryGroup entry_group = 2;
}

// Request message for
// [UpdateEntryGroup][google.cloud.datacatalog.v1.DataCatalog.UpdateEntryGroup].
message UpdateEntryGroupRequest {
  // Required. Updates for the entry group. The `name` field must be set.
  EntryGroup entry_group = 1 [(google.api.field_behavior) = REQUIRED];

  // Names of fields whose values to overwrite on an entry group.
  //
  // If this parameter is absent or empty, all modifiable fields
  // are overwritten. If such fields are non-required and omitted in the
  // request body, their values are emptied.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [GetEntryGroup][google.cloud.datacatalog.v1.DataCatalog.GetEntryGroup].
message GetEntryGroupRequest {
  // Required. The name of the entry group to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // The fields to return. If empty or omitted, all fields are returned.
  google.protobuf.FieldMask read_mask = 2;
}

// Request message for
// [DeleteEntryGroup][google.cloud.datacatalog.v1.DataCatalog.DeleteEntryGroup].
message DeleteEntryGroupRequest {
  // Required. The name of the entry group to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // Optional. If true, deletes all entries in the entry group.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [ListEntryGroups][google.cloud.datacatalog.v1.DataCatalog.ListEntryGroups].
message ListEntryGroupsRequest {
  // Required. The name of the location that contains the entry groups to list.
  //
  // Can be provided as a URL.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // Optional. The maximum number of items to return.
  //
  // Default is 10. Maximum limit is 1000.
  // Throws an invalid argument if `page_size` is greater than 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pagination token that specifies the next page to return.
  // If empty, returns the first page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [ListEntryGroups][google.cloud.datacatalog.v1.DataCatalog.ListEntryGroups].
message ListEntryGroupsResponse {
  // Entry group details.
  repeated EntryGroup entry_groups = 1;

  // Pagination token to specify in the next call to retrieve the next page of
  // results. Empty if there are no more items.
  string next_page_token = 2;
}

// Request message for
// [CreateEntry][google.cloud.datacatalog.v1.DataCatalog.CreateEntry].
message CreateEntryRequest {
  // Required. The name of the entry group this entry belongs to.
  //
  // Note: The entry itself and its child resources might not be stored in
  // the location specified in its name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // Required. The ID of the entry to create.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9),
  // and underscores (_).
  // The maximum size is 64 bytes when encoded in UTF-8.
  string entry_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The entry to create.
  Entry entry = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [UpdateEntry][google.cloud.datacatalog.v1.DataCatalog.UpdateEntry].
message UpdateEntryRequest {
  // Required. Updates for the entry. The `name` field must be set.
  Entry entry = 1 [(google.api.field_behavior) = REQUIRED];

  // Names of fields whose values to overwrite on an entry.
  //
  // If this parameter is absent or empty, all modifiable fields
  // are overwritten. If such fields are non-required and omitted in the
  // request body, their values are emptied.
  //
  // You can modify only the fields listed below.
  //
  // For entries with type `DATA_STREAM`:
  //
  // * `schema`
  //
  // For entries with type `FILESET`:
  //
  // * `schema`
  // * `display_name`
  // * `description`
  // * `gcs_fileset_spec`
  // * `gcs_fileset_spec.file_patterns`
  //
  // For entries with `user_specified_type`:
  //
  // * `schema`
  // * `display_name`
  // * `description`
  // * `user_specified_type`
  // * `user_specified_system`
  // * `linked_resource`
  // * `source_system_timestamps`
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [DeleteEntry][google.cloud.datacatalog.v1.DataCatalog.DeleteEntry].
message DeleteEntryRequest {
  // Required. The name of the entry to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];
}

// Request message for
// [GetEntry][google.cloud.datacatalog.v1.DataCatalog.GetEntry].
message GetEntryRequest {
  // Required. The name of the entry to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];
}

// Request message for
// [LookupEntry][google.cloud.datacatalog.v1.DataCatalog.LookupEntry].
message LookupEntryRequest {
  // Required. A full name, SQL name, or a fully qualified name of a
  // Google Cloud Platform resource.
  oneof target_name {
    // The full name of the Google Cloud Platform resource the Data Catalog
    // entry represents. For more information, see [Full Resource Name]
    // (https://cloud.google.com/apis/design/resource_names#full_resource_name).
    //
    // Full names are case-sensitive. For example:
    //
    //  * `//bigquery.googleapis.com/projects/{PROJECT_ID}/datasets/{DATASET_ID}/tables/{TABLE_ID}`
    //  * `//pubsub.googleapis.com/projects/{PROJECT_ID}/topics/{TOPIC_ID}`
    string linked_resource = 1;

    // The SQL name of the entry. SQL names are case-sensitive.
    //
    // Examples:
    //
    // * `pubsub.topic.{PROJECT_ID}.{TOPIC_ID}`
    // * `pubsub.topic.{PROJECT_ID}.`\``{TOPIC.ID.SEPARATED.WITH.DOTS}`\`
    // * `bigquery.table.{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}`
    // * `bigquery.dataset.{PROJECT_ID}.{DATASET_ID}`
    // * `datacatalog.entry.{PROJECT_ID}.{LOCATION_ID}.{ENTRY_GROUP_ID}.{ENTRY_ID}`
    //
    // Identifiers (`*_ID`) should comply with the
    // [Lexical structure in Standard SQL]
    // (https://cloud.google.com/bigquery/docs/reference/standard-sql/lexical).
    string sql_resource = 3;

    // [Fully Qualified Name
    // (FQN)](https://cloud.google.com//data-catalog/docs/fully-qualified-names)
    // of the resource.
    //
    // FQNs take two forms:
    //
    // * For non-regionalized resources:
    //
    //   `{SYSTEM}:{PROJECT}.{PATH_TO_RESOURCE_SEPARATED_WITH_DOTS}`
    //
    // * For regionalized resources:
    //
    //   `{SYSTEM}:{PROJECT}.{LOCATION_ID}.{PATH_TO_RESOURCE_SEPARATED_WITH_DOTS}`
    //
    // Example for a DPMS table:
    //
    // `dataproc_metastore:{PROJECT_ID}.{LOCATION_ID}.{INSTANCE_ID}.{DATABASE_ID}.{TABLE_ID}`
    string fully_qualified_name = 5;
  }

  // Project where the lookup should be performed. Required to lookup
  // entry that is not a part of `DPMS` or `DATAPLEX` `integrated_system`
  // using its `fully_qualified_name`. Ignored in other cases.
  string project = 6;

  // Location where the lookup should be performed. Required to lookup
  // entry that is not a part of `DPMS` or `DATAPLEX` `integrated_system`
  // using its `fully_qualified_name`. Ignored in other cases.
  string location = 7;
}

// Entry metadata.
// A Data Catalog entry represents another resource in Google
// Cloud Platform (such as a BigQuery dataset or a Pub/Sub topic) or
// outside of it. You can use the `linked_resource` field
// in the entry resource to refer to the original resource ID of the source
// system.
//
// An entry resource contains resource details, for example, its schema.
// Additionally, you can attach flexible metadata to an entry in the form of a
// [Tag][google.cloud.datacatalog.v1.Tag].
message Entry {
  option (google.api.resource) = {
    type: "datacatalog.googleapis.com/Entry"
    pattern: "projects/{project}/locations/{location}/entryGroups/{entry_group}/entries/{entry}"
  };

  // Output only. Identifier. The resource name of an entry in URL format.
  //
  // Note: The entry itself and its child resources might not be
  // stored in the location specified in its name.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // The resource this metadata entry refers to.
  //
  // For Google Cloud Platform resources, `linked_resource` is the
  // [Full Resource Name]
  // (https://cloud.google.com/apis/design/resource_names#full_resource_name).
  // For example, the `linked_resource` for a table resource from BigQuery is:
  //
  // `//bigquery.googleapis.com/projects/{PROJECT_ID}/datasets/{DATASET_ID}/tables/{TABLE_ID}`
  //
  // Output only when the entry is one of the types in the `EntryType` enum.
  //
  // For entries with a `user_specified_type`, this field is optional and
  // defaults to an empty string.
  //
  // The resource string must contain only letters (a-z, A-Z), numbers (0-9),
  // underscores (_), periods (.), colons (:), slashes (/), dashes (-),
  // and hashes (#).
  // The maximum size is 200 bytes when encoded in UTF-8.
  string linked_resource = 9;

  // [Fully Qualified Name
  // (FQN)](https://cloud.google.com//data-catalog/docs/fully-qualified-names)
  // of the resource. Set automatically for entries representing resources from
  // synced systems. Settable only during creation, and read-only later. Can
  // be used for search and lookup of the entries.
  //
  string fully_qualified_name = 29;

  // Required. Entry type.
  oneof entry_type {
    // The type of the entry.
    //
    // For details, see [`EntryType`](#entrytype).
    EntryType type = 2;

    // Custom entry type that doesn't match any of the values allowed for input
    // and listed in the `EntryType` enum.
    //
    // When creating an entry, first check the type values in the enum.
    // If there are no appropriate types for the new entry,
    // provide a custom value, for example, `my_special_type`.
    //
    // The `user_specified_type` string has the following limitations:
    //
    // * Is case insensitive.
    // * Must begin with a letter or underscore.
    // * Can only contain letters, numbers, and underscores.
    // * Must be at least 1 character and at most 64 characters long.
    string user_specified_type = 16;
  }

  // The source system of the entry.
  oneof system {
    // Output only. Indicates the entry's source system that Data Catalog
    // integrates with, such as BigQuery, Pub/Sub, or Dataproc Metastore.
    IntegratedSystem integrated_system = 17
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Indicates the entry's source system that Data Catalog doesn't
    // automatically integrate with.
    //
    // The `user_specified_system` string has the following limitations:
    //
    // * Is case insensitive.
    // * Must begin with a letter or underscore.
    // * Can only contain letters, numbers, and underscores.
    // * Must be at least 1 character and at most 64 characters long.
    string user_specified_system = 18;
  }

  // System specification.
  // Can be used as a complement for `spec`, when some metadata is relevant for
  // all entries existing within given system
  oneof system_spec {
    // Specification that applies to a relational database system. Only settable
    // when `user_specified_system` is equal to `SQL_DATABASE`
    SqlDatabaseSystemSpec sql_database_system_spec = 39;

    // Specification that applies to Looker sysstem. Only settable when
    // `user_specified_system` is equal to `LOOKER`
    LookerSystemSpec looker_system_spec = 40;

    // Specification that applies to Cloud Bigtable system. Only settable when
    // `integrated_system` is equal to `CLOUD_BIGTABLE`
    CloudBigtableSystemSpec cloud_bigtable_system_spec = 41;
  }

  // Type specification.
  oneof type_spec {
    // Specification that applies to a Cloud Storage fileset. Valid only
    // for entries with the `FILESET` type.
    GcsFilesetSpec gcs_fileset_spec = 6;

    // Output only. Specification that applies to a BigQuery table. Valid only
    // for entries with the `TABLE` type.
    BigQueryTableSpec bigquery_table_spec = 12
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Specification for a group of BigQuery tables with
    // the `[prefix]YYYYMMDD` name pattern.
    //
    // For more information, see [Introduction to partitioned tables]
    // (https://cloud.google.com/bigquery/docs/partitioned-tables#partitioning_versus_sharding).
    BigQueryDateShardedSpec bigquery_date_sharded_spec = 15
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Type- and system-specific information. Specifications for types contain
  // fields common to all entries of a given type, and sub-specifications with
  // fields specific to a given source system.
  //
  // When extending the API with new types and systems, use this field instead
  // of the legacy `type_spec`.
  oneof spec {
    // Specification that applies to a table resource. Valid only
    // for entries with the `TABLE` or `EXPLORE` type.
    DatabaseTableSpec database_table_spec = 24;

    // Specification that applies to a data source connection. Valid only
    // for entries with the `DATA_SOURCE_CONNECTION` type.
    DataSourceConnectionSpec data_source_connection_spec = 27;

    // Specification that applies to a user-defined function or procedure. Valid
    // only for entries with the `ROUTINE` type.
    RoutineSpec routine_spec = 28;

    // Specification that applies to a dataset.
    DatasetSpec dataset_spec = 32;

    // Specification that applies to a fileset resource. Valid only
    // for entries with the `FILESET` type.
    FilesetSpec fileset_spec = 33;

    // Specification that applies to a Service resource.
    ServiceSpec service_spec = 42;

    // Model specification.
    ModelSpec model_spec = 43;

    // FeatureonlineStore spec for Vertex AI Feature Store.
    FeatureOnlineStoreSpec feature_online_store_spec = 45;
  }

  // Display name of an entry.
  //
  // The maximum size is 500 bytes when encoded in UTF-8.
  // Default value is an empty string.
  string display_name = 3;

  // Entry description that can consist of several sentences or paragraphs
  // that describe entry contents.
  //
  // The description must not contain Unicode non-characters as well as C0
  // and C1 control codes except tabs (HT), new lines (LF), carriage returns
  // (CR), and page breaks (FF).
  // The maximum size is 2000 bytes when encoded in UTF-8.
  // Default value is an empty string.
  string description = 4;

  // Business Context of the entry. Not supported for BigQuery datasets
  BusinessContext business_context = 37;

  // Schema of the entry. An entry might not have any schema attached to it.
  Schema schema = 5;

  // Timestamps from the underlying resource, not from the Data Catalog
  // entry.
  //
  // Output only when the entry has a system listed in the `IntegratedSystem`
  // enum. For entries with `user_specified_system`, this field is optional
  // and defaults to an empty timestamp.
  SystemTimestamps source_system_timestamps = 7;

  // Resource usage statistics.
  UsageSignal usage_signal = 13;

  // Cloud labels attached to the entry.
  //
  // In Data Catalog, you can create and modify labels attached only to custom
  // entries. Synced entries have unmodifiable labels that come from the source
  // system.
  map<string, string> labels = 14;

  // Output only. Physical location of the entry.
  DataSource data_source = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information related to the entry. Private to the
  // current user.
  PersonalDetails personal_details = 26
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Specification that applies to a table resource. Valid only
// for entries with the `TABLE` type.
message DatabaseTableSpec {
  // Type of the table.
  enum TableType {
    // Default unknown table type.
    TABLE_TYPE_UNSPECIFIED = 0;

    // Native table.
    NATIVE = 1;

    // External table.
    EXTERNAL = 2;
  }

  // Specification that applies to database view.
  message DatabaseViewSpec {
    // Concrete type of the view.
    enum ViewType {
      // Default unknown view type.
      VIEW_TYPE_UNSPECIFIED = 0;

      // Standard view.
      STANDARD_VIEW = 1;

      // Materialized view.
      MATERIALIZED_VIEW = 2;
    }

    // Type of this view.
    ViewType view_type = 1;

    // Definition of the view.
    oneof source_definition {
      // Name of a singular table this view reflects one to one.
      string base_table = 2;

      // SQL query used to generate this view.
      string sql_query = 3;
    }
  }

  // Type of this table.
  TableType type = 1;

  // Output only. Fields specific to a Dataplex table and present only in the
  // Dataplex table entries.
  DataplexTableSpec dataplex_table = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Spec what aplies to tables that are actually views.
  // Not set for "real" tables.
  DatabaseViewSpec database_view_spec = 3;
}

// Specification that applies to a fileset. Valid only for entries with the
// 'FILESET' type.
message FilesetSpec {
  // Fields specific to a Dataplex fileset and present only in the Dataplex
  // fileset entries.
  DataplexFilesetSpec dataplex_fileset = 1;
}

// Specification that applies to a data source connection. Valid only for
// entries with the `DATA_SOURCE_CONNECTION` type.
// Only one of internal specs can be set at the time, and cannot
// be changed later.
message DataSourceConnectionSpec {
  // Output only. Fields specific to BigQuery connections.
  BigQueryConnectionSpec bigquery_connection_spec = 1;
}

// Specification that applies to a routine. Valid only for
// entries with the `ROUTINE` type.
message RoutineSpec {
  // The fine-grained type of the routine.
  enum RoutineType {
    // Unspecified type.
    ROUTINE_TYPE_UNSPECIFIED = 0;

    // Non-builtin permanent scalar function.
    SCALAR_FUNCTION = 1;

    // Stored procedure.
    PROCEDURE = 2;
  }

  // Input or output argument of a function or stored procedure.
  message Argument {
    // The input or output mode of the argument.
    enum Mode {
      // Unspecified mode.
      MODE_UNSPECIFIED = 0;

      // The argument is input-only.
      IN = 1;

      // The argument is output-only.
      OUT = 2;

      // The argument is both an input and an output.
      INOUT = 3;
    }

    // The name of the argument. A return argument of a function might not have
    // a name.
    string name = 1;

    // Specifies whether the argument is input or output.
    Mode mode = 2;

    // Type of the argument. The exact value depends on the source system and
    // the language.
    string type = 3;
  }

  // The type of the routine.
  RoutineType routine_type = 1;

  // The language the routine is written in. The exact value depends on the
  // source system. For BigQuery routines, possible values are:
  //
  // * `SQL`
  // * `JAVASCRIPT`
  string language = 2;

  // Arguments of the routine.
  repeated Argument routine_arguments = 3;

  // Return type of the argument. The exact value depends on the source system
  // and the language.
  string return_type = 4;

  // The body of the routine.
  string definition_body = 5;

  // Contains fields specific to the source system.
  oneof system_spec {
    // Fields specific for BigQuery routines.
    BigQueryRoutineSpec bigquery_routine_spec = 6;
  }
}

// Specification that applies to a dataset. Valid only for
// entries with the `DATASET` type.
message DatasetSpec {
  // Fields specific to the source system.
  oneof system_spec {
    // Vertex AI Dataset specific fields
    VertexDatasetSpec vertex_dataset_spec = 2;
  }
}

// Specification that applies to
// entries that are part `SQL_DATABASE` system
// (user_specified_type)
message SqlDatabaseSystemSpec {
  // SQL Database Engine.
  // enum SqlEngine {
  //  UNDEFINED = 0;
  //  MY_SQL = 1;
  //  POSTGRE_SQL = 2;
  //  SQL_SERVER = 3;
  // }
  // Engine of the enclosing database instance.
  string sql_engine = 1;

  // Version of the database engine.
  string database_version = 2;

  // Host of the SQL database
  // enum InstanceHost {
  //  UNDEFINED = 0;
  //  SELF_HOSTED = 1;
  //  CLOUD_SQL = 2;
  //  AMAZON_RDS = 3;
  //  AZURE_SQL = 4;
  // }
  // Host of the enclousing database instance.
  string instance_host = 3;
}

// Specification that applies to
// entries that are part `LOOKER` system
// (user_specified_type)
message LookerSystemSpec {
  // ID of the parent Looker Instance. Empty if it does not exist.
  // Example value: `someinstance.looker.com`
  string parent_instance_id = 1;

  // Name of the parent Looker Instance. Empty if it does not exist.
  string parent_instance_display_name = 2;

  // ID of the parent Model. Empty if it does not exist.
  string parent_model_id = 3;

  // Name of the parent Model. Empty if it does not exist.
  string parent_model_display_name = 4;

  // ID of the parent View. Empty if it does not exist.
  string parent_view_id = 5;

  // Name of the parent View. Empty if it does not exist.
  string parent_view_display_name = 6;
}

// Specification that applies to
// all entries that are part of `CLOUD_BIGTABLE` system
// (user_specified_type)
message CloudBigtableSystemSpec {
  // Display name of the Instance. This is user specified and different from
  // the resource name.
  string instance_display_name = 1;
}

// Specification that applies to Instance
// entries that are part of `CLOUD_BIGTABLE` system.
// (user_specified_type)
message CloudBigtableInstanceSpec {
  // Spec that applies to clusters of an Instance of Cloud Bigtable.
  message CloudBigtableClusterSpec {
    // Name of the cluster.
    string display_name = 1;

    // Location of the cluster, typically a Cloud zone.
    string location = 2;

    // Type of the resource. For a cluster this would be "CLUSTER".
    string type = 3;

    // A link back to the parent resource, in this case Instance.
    string linked_resource = 4;
  }

  // The list of clusters for the Instance.
  repeated CloudBigtableClusterSpec cloud_bigtable_cluster_specs = 1;
}

// Specification that applies to a Service resource. Valid only
// for entries with the `SERVICE` type.
message ServiceSpec {
  // System spec
  oneof system_spec {
    // Specification that applies to Instance entries of `CLOUD_BIGTABLE`
    // system.
    CloudBigtableInstanceSpec cloud_bigtable_instance_spec = 1;
  }
}

// Detail description of the source information of a Vertex model.
message VertexModelSourceInfo {
  // Source of the model.
  enum ModelSourceType {
    // Should not be used.
    MODEL_SOURCE_TYPE_UNSPECIFIED = 0;

    // The Model is uploaded by automl training pipeline.
    AUTOML = 1;

    // The Model is uploaded by user or custom training pipeline.
    CUSTOM = 2;

    // The Model is registered and sync'ed from BigQuery ML.
    BQML = 3;

    // The Model is saved or tuned from Model Garden.
    MODEL_GARDEN = 4;

    // The Model is saved or tuned from Genie.
    GENIE = 5;

    // The Model is uploaded by text embedding finetuning pipeline.
    CUSTOM_TEXT_EMBEDDING = 6;

    // The Model is saved or tuned from Marketplace.
    MARKETPLACE = 7;
  }

  // Type of the model source.
  ModelSourceType source_type = 1;

  // If this Model is copy of another Model. If true then
  // [source_type][google.cloud.datacatalog.v1.VertexModelSourceInfo.source_type]
  // pertains to the original.
  bool copy = 2;
}

// Specification for vertex model resources.
message VertexModelSpec {
  // The version ID of the model.
  string version_id = 1;

  // User provided version aliases so that a model version can be referenced via
  // alias
  repeated string version_aliases = 2;

  // The description of this version.
  string version_description = 3;

  // Source of a Vertex model.
  VertexModelSourceInfo vertex_model_source_info = 4;

  // URI of the Docker image to be used as the custom container for serving
  // predictions.
  string container_image_uri = 5;
}

// Specification for vertex dataset resources.
message VertexDatasetSpec {
  // Type of data stored in the dataset.
  enum DataType {
    // Should not be used.
    DATA_TYPE_UNSPECIFIED = 0;

    // Structured data dataset.
    TABLE = 1;

    // Image dataset which supports ImageClassification, ImageObjectDetection
    // and ImageSegmentation problems.
    IMAGE = 2;

    // Document dataset which supports TextClassification, TextExtraction and
    // TextSentiment problems.
    TEXT = 3;

    // Video dataset which supports VideoClassification, VideoObjectTracking and
    // VideoActionRecognition problems.
    VIDEO = 4;

    // Conversation dataset which supports conversation problems.
    CONVERSATION = 5;

    // TimeSeries dataset.
    TIME_SERIES = 6;

    // Document dataset which supports DocumentAnnotation problems.
    DOCUMENT = 7;

    // TextToSpeech dataset which supports TextToSpeech problems.
    TEXT_TO_SPEECH = 8;

    // Translation dataset which supports Translation problems.
    TRANSLATION = 9;

    // Store Vision dataset which is used for HITL integration.
    STORE_VISION = 10;

    // Enterprise Knowledge Graph dataset which is used for HITL labeling
    // integration.
    ENTERPRISE_KNOWLEDGE_GRAPH = 11;

    // Text prompt dataset which supports Large Language Models.
    TEXT_PROMPT = 12;
  }

  // The number of DataItems in this Dataset. Only apply for non-structured
  // Dataset.
  int64 data_item_count = 1;

  // Type of the dataset.
  DataType data_type = 2;
}

// Specification that applies to a model. Valid only for
// entries with the `MODEL` type.
message ModelSpec {
  // System spec
  oneof system_spec {
    // Specification for vertex model resources.
    VertexModelSpec vertex_model_spec = 1;
  }
}

// Detail description of the source information of a Vertex Feature Online
// Store.
message FeatureOnlineStoreSpec {
  // Type of underlaying storage type.
  enum StorageType {
    // Should not be used.
    STORAGE_TYPE_UNSPECIFIED = 0;

    // Underlsying storgae is Bigtable.
    BIGTABLE = 1;

    // Underlaying is optimized online server (Lightning).
    OPTIMIZED = 2;
  }

  // Output only. Type of underelaying storage for the FeatureOnlineStore.
  StorageType storage_type = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Business Context of the entry.
message BusinessContext {
  // Entry overview fields for rich text descriptions of entries.
  EntryOverview entry_overview = 1;

  // Contact people for the entry.
  Contacts contacts = 2;
}

// Entry overview fields for rich text descriptions of entries.
message EntryOverview {
  // Entry overview with support for rich text.
  //
  // The overview must only contain Unicode characters, and should be
  // formatted using HTML.
  // The maximum length is 10 MiB as this value holds HTML descriptions
  // including encoded images. The maximum length of the text without images
  // is 100 KiB.
  string overview = 1;
}

// Contact people for the entry.
message Contacts {
  // A contact person for the entry.
  message Person {
    // Designation of the person, for example, Data Steward.
    string designation = 1;

    // Email of the person in the format of `john.doe@xyz`,
    // `<john.doe@xyz>`, or `John Doe<john.doe@xyz>`.
    string email = 2;
  }

  // The list of contact people for the entry.
  repeated Person people = 1;
}

// Entry group metadata.
//
//  An `EntryGroup` resource represents a logical grouping of zero or more
//  Data Catalog [Entry][google.cloud.datacatalog.v1.Entry] resources.
message EntryGroup {
  option (google.api.resource) = {
    type: "datacatalog.googleapis.com/EntryGroup"
    pattern: "projects/{project}/locations/{location}/entryGroups/{entry_group}"
  };

  // Identifier. The resource name of the entry group in URL format.
  //
  // Note: The entry group itself and its child resources might not be
  // stored in the location specified in its name.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // A short name to identify the entry group, for example,
  // "analytics data - jan 2011". Default value is an empty string.
  string display_name = 2;

  // Entry group description. Can consist of several sentences or
  // paragraphs that describe the entry group contents.
  // Default value is an empty string.
  string description = 3;

  // Output only. Timestamps of the entry group. Default value is empty.
  SystemTimestamps data_catalog_timestamps = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. When set to [true], it means DataCatalog EntryGroup was
  // transferred to Dataplex Catalog Service. It makes EntryGroup and its
  // Entries to be read-only in DataCatalog. However, new Tags on EntryGroup and
  // its Entries can be created. After setting the flag to [true] it cannot be
  // unset.
  bool transferred_to_dataplex = 9 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CreateTagTemplate][google.cloud.datacatalog.v1.DataCatalog.CreateTagTemplate].
message CreateTagTemplateRequest {
  // Required. The name of the project and the template location
  // [region](https://cloud.google.com/data-catalog/docs/concepts/regions).
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/TagTemplate"
    }
  ];

  // Required. The ID of the tag template to create.
  //
  // The ID must contain only lowercase letters (a-z), numbers (0-9),
  // or underscores (_), and must start with a letter or underscore.
  // The maximum size is 64 bytes when encoded in UTF-8.
  string tag_template_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The tag template to create.
  TagTemplate tag_template = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [GetTagTemplate][google.cloud.datacatalog.v1.DataCatalog.GetTagTemplate].
message GetTagTemplateRequest {
  // Required. The name of the tag template to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplate"
    }
  ];
}

// Request message for
// [UpdateTagTemplate][google.cloud.datacatalog.v1.DataCatalog.UpdateTagTemplate].
message UpdateTagTemplateRequest {
  // Required. The template to update. The `name` field must be set.
  TagTemplate tag_template = 1 [(google.api.field_behavior) = REQUIRED];

  // Names of fields whose values to overwrite on a tag template. Currently,
  // only `display_name` and `is_publicly_readable` can be overwritten.
  //
  // If this parameter is absent or empty, all modifiable fields
  // are overwritten. If such fields are non-required and omitted in the
  // request body, their values are emptied.
  //
  // Note: Updating the `is_publicly_readable` field may require up to 12
  // hours to take effect in search results.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [DeleteTagTemplate][google.cloud.datacatalog.v1.DataCatalog.DeleteTagTemplate].
message DeleteTagTemplateRequest {
  // Required. The name of the tag template to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplate"
    }
  ];

  // Required. If true, deletes all tags that use this template.
  //
  // Currently, `true` is the only supported value.
  bool force = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [CreateTag][google.cloud.datacatalog.v1.DataCatalog.CreateTag].
message CreateTagRequest {
  // Required. The name of the resource to attach this tag to.
  //
  // Tags can be attached to entries or entry groups. An entry can have up to
  // 1000 attached tags.
  //
  // Note: The tag and its child resources might not be stored in
  // the location specified in its name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Tag"
    }
  ];

  // Required. The tag to create.
  Tag tag = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [UpdateTag][google.cloud.datacatalog.v1.DataCatalog.UpdateTag].
message UpdateTagRequest {
  // Required. The updated tag. The "name" field must be set.
  Tag tag = 1 [(google.api.field_behavior) = REQUIRED];

  // Names of fields whose values to overwrite on a tag. Currently, a tag has
  // the only modifiable field with the name `fields`.
  //
  // In general, if this parameter is absent or empty, all modifiable fields
  // are overwritten. If such fields are non-required and omitted in the
  // request body, their values are emptied.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [DeleteTag][google.cloud.datacatalog.v1.DataCatalog.DeleteTag].
message DeleteTagRequest {
  // Required. The name of the tag to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Tag"
    }
  ];
}

// Request message for
// [CreateTagTemplateField][google.cloud.datacatalog.v1.DataCatalog.CreateTagTemplateField].
message CreateTagTemplateFieldRequest {
  // Required. The name of the project and the template location
  // [region](https://cloud.google.com/data-catalog/docs/concepts/regions).
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplate"
    }
  ];

  // Required. The ID of the tag template field to create.
  //
  // Note: Adding a required field to an existing template is *not* allowed.
  //
  // Field IDs can contain letters (both uppercase and lowercase), numbers
  // (0-9), underscores (_) and dashes (-). Field IDs must be at least 1
  // character long and at most 128 characters long. Field IDs must also be
  // unique within their template.
  string tag_template_field_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The tag template field to create.
  TagTemplateField tag_template_field = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [UpdateTagTemplateField][google.cloud.datacatalog.v1.DataCatalog.UpdateTagTemplateField].
message UpdateTagTemplateFieldRequest {
  // Required. The name of the tag template field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplateField"
    }
  ];

  // Required. The template to update.
  TagTemplateField tag_template_field = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Names of fields whose values to overwrite on an individual field
  // of a tag template. The following fields are modifiable:
  //
  // * `display_name`
  // * `type.enum_type`
  // * `is_required`
  //
  // If this parameter is absent or empty, all modifiable fields
  // are overwritten. If such fields are non-required and omitted in the request
  // body, their values are emptied with one exception: when updating an enum
  // type, the provided values are merged with the existing values. Therefore,
  // enum values can only be added, existing enum values cannot be deleted or
  // renamed.
  //
  // Additionally, updating a template field from optional to required is
  // *not* allowed.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [RenameTagTemplateField][google.cloud.datacatalog.v1.DataCatalog.RenameTagTemplateField].
message RenameTagTemplateFieldRequest {
  // Required. The name of the tag template field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplateField"
    }
  ];

  // Required. The new ID of this tag template field. For example,
  // `my_new_field`.
  string new_tag_template_field_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [RenameTagTemplateFieldEnumValue][google.cloud.datacatalog.v1.DataCatalog.RenameTagTemplateFieldEnumValue].
message RenameTagTemplateFieldEnumValueRequest {
  // Required. The name of the enum field value.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplateFieldEnumValue"
    }
  ];

  // Required. The new display name of the enum value. For example,
  // `my_new_enum_value`.
  string new_enum_value_display_name = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [DeleteTagTemplateField][google.cloud.datacatalog.v1.DataCatalog.DeleteTagTemplateField].
message DeleteTagTemplateFieldRequest {
  // Required. The name of the tag template field to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplateField"
    }
  ];

  // Required. If true, deletes this field from any tags that use it.
  //
  // Currently, `true` is the only supported value.
  bool force = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ListTags][google.cloud.datacatalog.v1.DataCatalog.ListTags].
message ListTagsRequest {
  // Required. The name of the Data Catalog resource to list the tags of.
  //
  // The resource can be an [Entry][google.cloud.datacatalog.v1.Entry]
  // or an [EntryGroup][google.cloud.datacatalog.v1.EntryGroup]
  // (without `/entries/{entries}` at the end).
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Tag"
    }
  ];

  // The maximum number of tags to return. Default is 10. Maximum limit is 1000.
  int32 page_size = 2;

  // Pagination token that specifies the next page to return. If empty, the
  // first page is returned.
  string page_token = 3;
}

// Response message for
// [ListTags][google.cloud.datacatalog.v1.DataCatalog.ListTags].
message ListTagsResponse {
  // [Tag][google.cloud.datacatalog.v1.Tag] details.
  repeated Tag tags = 1;

  // Pagination token of the next results page. Empty if there are
  // no more items in results.
  string next_page_token = 2;
}

// Request message for
// [ReconcileTags][google.cloud.datacatalog.v1.DataCatalog.ReconcileTags].
message ReconcileTagsRequest {
  // Required. Name of [Entry][google.cloud.datacatalog.v1.Entry] to be tagged.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];

  // Required. The name of the tag template, which is used for reconciliation.
  string tag_template = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/TagTemplate"
    }
  ];

  // If set to `true`, deletes entry tags related to a tag template
  // not listed in the tags source from an entry. If set to `false`,
  // unlisted tags are retained.
  bool force_delete_missing = 3;

  // A list of tags to apply to an entry. A tag can specify a
  // tag template, which must be the template specified in the
  // `ReconcileTagsRequest`.
  // The sole entry and each of its columns must be mentioned at most once.
  repeated Tag tags = 4;
}

// [Long-running operation][google.longrunning.Operation]
// response message returned by
// [ReconcileTags][google.cloud.datacatalog.v1.DataCatalog.ReconcileTags].
message ReconcileTagsResponse {
  // Number of tags created in the request.
  int64 created_tags_count = 1;

  // Number of tags updated in the request.
  int64 updated_tags_count = 2;

  // Number of tags deleted in the request.
  int64 deleted_tags_count = 3;
}

// [Long-running operation][google.longrunning.Operation]
// metadata message returned by the
// [ReconcileTags][google.cloud.datacatalog.v1.DataCatalog.ReconcileTags].
message ReconcileTagsMetadata {
  // Enum holding possible states of the reconciliation operation.
  enum ReconciliationState {
    // Default value. This value is unused.
    RECONCILIATION_STATE_UNSPECIFIED = 0;

    // The reconciliation has been queued and awaits for execution.
    RECONCILIATION_QUEUED = 1;

    // The reconciliation is in progress.
    RECONCILIATION_IN_PROGRESS = 2;

    // The reconciliation has been finished.
    RECONCILIATION_DONE = 3;
  }

  // State of the reconciliation operation.
  ReconciliationState state = 1;

  // Maps the name of each tagged column (or empty string for a
  // sole entry) to tagging operation [status][google.rpc.Status].
  map<string, google.rpc.Status> errors = 2;
}

// Request message for
// [ListEntries][google.cloud.datacatalog.v1.DataCatalog.ListEntries].
message ListEntriesRequest {
  // Required. The name of the entry group that contains the entries to list.
  //
  // Can be provided in URL format.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/EntryGroup"
    }
  ];

  // The maximum number of items to return. Default is 10. Maximum limit is
  // 1000. Throws an invalid argument if `page_size` is more than 1000.
  int32 page_size = 2;

  // Pagination token that specifies the next page to return. If empty, the
  // first page is returned.
  string page_token = 3;

  // The fields to return for each entry. If empty or omitted, all
  // fields are returned.
  //
  // For example, to return a list of entries with only the `name` field,
  // set `read_mask` to only one path with the `name` value.
  google.protobuf.FieldMask read_mask = 4;
}

// Response message for
// [ListEntries][google.cloud.datacatalog.v1.DataCatalog.ListEntries].
message ListEntriesResponse {
  // Entry details.
  repeated Entry entries = 1;

  // Pagination token of the next results page. Empty if there are no more items
  // in results.
  string next_page_token = 2;
}

// Request message for
// [StarEntry][google.cloud.datacatalog.v1.DataCatalog.StarEntry].
message StarEntryRequest {
  // Required. The name of the entry to mark as starred.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];
}

// Response message for
// [StarEntry][google.cloud.datacatalog.v1.DataCatalog.StarEntry].
// Empty for now
message StarEntryResponse {}

// Request message for
// [UnstarEntry][google.cloud.datacatalog.v1.DataCatalog.UnstarEntry].
message UnstarEntryRequest {
  // Required. The name of the entry to mark as **not** starred.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];
}

// Response message for
// [UnstarEntry][google.cloud.datacatalog.v1.DataCatalog.UnstarEntry].
// Empty for now
message UnstarEntryResponse {}

// Request message for
// [ImportEntries][google.cloud.datacatalog.v1.DataCatalog.ImportEntries]
// method.
message ImportEntriesRequest {
  // Required. Target entry group for ingested entries.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datacatalog.googleapis.com/Entry"
    }
  ];

  // Source of imported entries, e.g. dump stored in a Cloud Storage
  oneof source {
    // Path to a Cloud Storage bucket that contains a dump ready for ingestion.
    string gcs_bucket_path = 2;
  }

  // Optional. (Optional) Dataplex task job id, if specified will be used as
  // part of ImportEntries LRO ID
  string job_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for [long-running operation][google.longrunning.Operation]
// returned by the
// [ImportEntries][google.cloud.datacatalog.v1.DataCatalog.ImportEntries].
message ImportEntriesResponse {
  // Cumulative number of entries created and entries updated as a result of
  // import operation.
  optional int64 upserted_entries_count = 5;

  // Number of entries deleted as a result of import operation.
  optional int64 deleted_entries_count = 6;
}

// Metadata message for [long-running operation][google.longrunning.Operation]
// returned by the
// [ImportEntries][google.cloud.datacatalog.v1.DataCatalog.ImportEntries].
message ImportEntriesMetadata {
  // Enum holding possible states of the import operation.
  enum ImportState {
    // Default value. This value is unused.
    IMPORT_STATE_UNSPECIFIED = 0;

    // The dump with entries has been queued for import.
    IMPORT_QUEUED = 1;

    // The import of entries is in progress.
    IMPORT_IN_PROGRESS = 2;

    // The import of entries has been finished.
    IMPORT_DONE = 3;

    // The import of entries has been abandoned in favor of a newer request.
    IMPORT_OBSOLETE = 4;
  }

  // State of the import operation.
  ImportState state = 1;

  // Partial errors that are encountered during the ImportEntries operation.
  // There is no guarantee that all the encountered errors are reported.
  // However, if no errors are reported, it means that no errors were
  // encountered.
  repeated google.rpc.Status errors = 2;
}

// Request message for
// [ModifyEntryOverview][google.cloud.datacatalog.v1.DataCatalog.ModifyEntryOverview].
message ModifyEntryOverviewRequest {
  // Required. The full resource name of the entry.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];

  // Required. The new value for the Entry Overview.
  EntryOverview entry_overview = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [ModifyEntryContacts][google.cloud.datacatalog.v1.DataCatalog.ModifyEntryContacts].
message ModifyEntryContactsRequest {
  // Required. The full resource name of the entry.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datacatalog.googleapis.com/Entry"
    }
  ];

  // Required. The new value for the Contacts.
  Contacts contacts = 2 [(google.api.field_behavior) = REQUIRED];
}

// Metadata automatically ingested from Google Cloud resources like BigQuery
// tables or Pub/Sub topics always uses enum values from `EntryType` as the type
// of entry.
//
// Other sources of metadata like Hive or Oracle databases can identify the type
// by either using one of the enum values from `EntryType` (for example,
// `FILESET` for a Cloud Storage fileset) or specifying a custom value using
// the [`Entry`](#resource:-entry) field `user_specified_type`. For more
// information, see
// [Surface files from Cloud Storage with fileset
// entries](/data-catalog/docs/how-to/filesets) or [Create custom entries for
// your data sources](/data-catalog/docs/how-to/custom-entries).
enum EntryType {
  // Default unknown type.
  ENTRY_TYPE_UNSPECIFIED = 0;

  // The entry type that has a GoogleSQL schema, including
  // logical views.
  TABLE = 2;

  // The type of models.
  //
  // For more information, see [Supported models in BigQuery
  // ML](/bigquery/docs/bqml-introduction#supported_models).
  MODEL = 5;

  // An entry type for streaming entries. For example, a Pub/Sub topic.
  DATA_STREAM = 3;

  // An entry type for a set of files or objects. For example, a
  // Cloud Storage fileset.
  FILESET = 4;

  // A group of servers that work together. For example, a Kafka cluster.
  CLUSTER = 6;

  // A database.
  DATABASE = 7;

  // Connection to a data source. For example, a BigQuery
  // connection.
  DATA_SOURCE_CONNECTION = 8;

  // Routine, for example, a BigQuery routine.
  ROUTINE = 9;

  // A Dataplex lake.
  LAKE = 10;

  // A Dataplex zone.
  ZONE = 11;

  // A service, for example, a Dataproc Metastore service.
  SERVICE = 14;

  // Schema within a relational database.
  DATABASE_SCHEMA = 15;

  // A Dashboard, for example from Looker.
  DASHBOARD = 16;

  // A Looker Explore.
  //
  // For more information, see [Looker Explore API]
  // (https://developers.looker.com/api/explorer/4.0/methods/LookmlModel/lookml_model_explore).
  EXPLORE = 17;

  // A Looker Look.
  //
  // For more information, see [Looker Look API]
  // (https://developers.looker.com/api/explorer/4.0/methods/Look).
  LOOK = 18;

  // Feature Online Store resource in Vertex AI Feature Store.
  FEATURE_ONLINE_STORE = 19;

  // Feature View resource in Vertex AI Feature Store.
  FEATURE_VIEW = 20;

  // Feature Group resource in Vertex AI Feature Store.
  FEATURE_GROUP = 21;
}

// Request message for
// [SetConfig][google.cloud.datacatalog.v1.DataCatalog.SetConfig].
message SetConfigRequest {
  // Required. The organization or project whose config is being specified.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // The configuration field to set.
  oneof configuration {
    // Opt-in status for the migration of Tag Templates to Dataplex.
    TagTemplateMigration tag_template_migration = 2;

    // Opt-in status for the UI switch to Dataplex.
    CatalogUIExperience catalog_ui_experience = 3;
  }
}

// Request message for
// [RetrieveConfig][google.cloud.datacatalog.v1.DataCatalog.RetrieveConfig].
message RetrieveConfigRequest {
  // Required. The organization whose config is being retrieved.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [RetrieveEffectiveConfig][google.cloud.datacatalog.v1.DataCatalog.RetrieveEffectiveConfig].
message RetrieveEffectiveConfigRequest {
  // Required. The resource whose effective config is being retrieved.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Configuration related to the opt-in status for the migration of TagTemplates
// to Dataplex.
enum TagTemplateMigration {
  // Default value. Migration of Tag Templates from Data Catalog to Dataplex is
  // not performed.
  TAG_TEMPLATE_MIGRATION_UNSPECIFIED = 0;

  // Migration of Tag Templates from Data Catalog to Dataplex is enabled.
  TAG_TEMPLATE_MIGRATION_ENABLED = 1;

  // Migration of Tag Templates from Data Catalog to Dataplex is disabled.
  TAG_TEMPLATE_MIGRATION_DISABLED = 2;
}

// Configuration related to the opt-in status for the UI switch to Dataplex.
enum CatalogUIExperience {
  // Default value. The default UI is Dataplex.
  CATALOG_UI_EXPERIENCE_UNSPECIFIED = 0;

  // The UI is Dataplex.
  CATALOG_UI_EXPERIENCE_ENABLED = 1;

  // The UI is Data Catalog.
  CATALOG_UI_EXPERIENCE_DISABLED = 2;
}

// The configuration related to the migration from Data Catalog to Dataplex that
// has been applied to an organization and any projects under it. It is the
// response message for
// [RetrieveConfig][google.cloud.datacatalog.v1.DataCatalog.RetrieveConfig].
message OrganizationConfig {
  // Map of organizations and project resource names and their configuration.
  // The format for the map keys is `organizations/{organizationId}` or
  // `projects/{projectId}`.
  map<string, MigrationConfig> config = 1;
}

// The configuration related to the migration to Dataplex applied to an
// organization or project.
// It is the response message for
// [SetConfig][google.cloud.datacatalog.v1.DataCatalog.SetConfig] and
// [RetrieveEffectiveConfig][google.cloud.datacatalog.v1.DataCatalog.RetrieveEffectiveConfig].
message MigrationConfig {
  // Opt-in status for the migration of Tag Templates to Dataplex.
  TagTemplateMigration tag_template_migration = 1;

  // Opt-in status for the UI switch to Dataplex.
  CatalogUIExperience catalog_ui_experience = 2;
}
