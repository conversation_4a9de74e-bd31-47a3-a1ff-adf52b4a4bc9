// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/field_behavior.proto";
import "google/cloud/datacatalog/v1/common.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Result in the response to a search request.
//
// Each result captures details of one entry that matches the search.
message SearchCatalogResult {
  // Type of the search result.
  //
  // You can use this field to determine which get method to call to fetch the
  // full resource.
  SearchResultType search_result_type = 1;

  // Sub-type of the search result.
  //
  // A dot-delimited full type of the resource. The same type you
  // specify in the `type` search predicate.
  //
  // Examples: `entry.table`, `entry.dataStream`, `tagTemplate`.
  string search_result_subtype = 2;

  // The relative name of the resource in URL format.
  //
  // Examples:
  //
  //  * `projects/{PROJECT_ID}/locations/{LOCATION_ID}/entryGroups/{ENTRY_GROUP_ID}/entries/{ENTRY_ID}`
  //  * `projects/{PROJECT_ID}/tagTemplates/{TAG_TEMPLATE_ID}`
  string relative_resource_name = 3;

  // The full name of the Google Cloud resource the entry belongs to.
  //
  // For more information, see [Full Resource Name]
  // (/apis/design/resource_names#full_resource_name).
  //
  // Example:
  //
  // `//bigquery.googleapis.com/projects/PROJECT_ID/datasets/DATASET_ID/tables/TABLE_ID`
  string linked_resource = 4;

  // The last modification timestamp of the entry in the source system.
  google.protobuf.Timestamp modify_time = 7;

  // The source system of the entry. Applicable only when the
  // `search_result_type` is `ENTRY`.
  oneof system {
    // Output only. The source system that Data Catalog automatically integrates
    // with, such as BigQuery, Cloud Pub/Sub, or Dataproc Metastore.
    IntegratedSystem integrated_system = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Custom source system that you can manually integrate Data Catalog with.
    string user_specified_system = 9;
  }

  // Fully qualified name (FQN) of the resource.
  //
  // FQNs take two forms:
  //
  // * For non-regionalized resources:
  //
  //   `{SYSTEM}:{PROJECT}.{PATH_TO_RESOURCE_SEPARATED_WITH_DOTS}`
  //
  // * For regionalized resources:
  //
  //   `{SYSTEM}:{PROJECT}.{LOCATION_ID}.{PATH_TO_RESOURCE_SEPARATED_WITH_DOTS}`
  //
  // Example for a DPMS table:
  //
  // `dataproc_metastore:PROJECT_ID.LOCATION_ID.INSTANCE_ID.DATABASE_ID.TABLE_ID`
  string fully_qualified_name = 10;

  // The display name of the result.
  string display_name = 12;

  // Entry description that can consist of several sentences or paragraphs that
  // describe entry contents.
  string description = 13;
}

// The resource types that can be returned in search results.
enum SearchResultType {
  // Default unknown type.
  SEARCH_RESULT_TYPE_UNSPECIFIED = 0;

  // An [Entry][google.cloud.datacatalog.v1.Entry].
  ENTRY = 1;

  // A [TagTemplate][google.cloud.datacatalog.v1.TagTemplate].
  TAG_TEMPLATE = 2;

  // An [EntryGroup][google.cloud.datacatalog.v1.EntryGroup].
  ENTRY_GROUP = 3;
}
