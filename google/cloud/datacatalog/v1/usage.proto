// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1";
option go_package = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1";
option ruby_package = "Google::Cloud::DataCatalog::V1";

// Detailed statistics on the entry's usage.
//
// Usage statistics have the following limitations:
//
// - Only BigQuery tables have them.
// - They only include BigQuery query jobs.
// - They might be underestimated because wildcard table references
//   are not yet counted. For more information, see
//   [Querying multiple tables using a wildcard table]
//   (https://cloud.google.com/bigquery/docs/querying-wildcard-tables)
message UsageStats {
  // The number of successful uses of the underlying entry.
  float total_completions = 1;

  // The number of failed attempts to use the underlying entry.
  float total_failures = 2;

  // The number of cancelled attempts to use the underlying entry.
  float total_cancellations = 3;

  // Total time spent only on successful uses, in milliseconds.
  float total_execution_time_for_completions_millis = 4;
}

// Common statistics on the entry's usage.
//
// They can be set on any system.
message CommonUsageStats {
  // View count in source system.
  optional int64 view_count = 1;
}

// The set of all usage signals that Data Catalog stores.
//
// Note: Usually, these signals are updated daily. In rare cases, an update may
// fail but will be performed again on the next day.
message UsageSignal {
  // The end timestamp of the duration of usage statistics.
  google.protobuf.Timestamp update_time = 1;

  // Output only. BigQuery usage statistics over each of the predefined time
  // ranges.
  //
  // Supported time ranges are `{"24H", "7D", "30D"}`.
  map<string, UsageStats> usage_within_time_range = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Common usage statistics over each of the predefined time ranges.
  //
  // Supported time ranges are `{"24H", "7D", "30D", "Lifetime"}`.
  map<string, CommonUsageStats> common_usage_within_time_range = 3;

  // Favorite count in the source system.
  optional int64 favorite_count = 4;
}
