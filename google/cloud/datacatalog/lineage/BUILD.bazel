# This build file includes a target for the Ruby wrapper library for
# google-cloud-data_catalog-lineage.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for datalineage.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "datalineage_ruby_wrapper",
    srcs = ["//google/cloud/datacatalog/lineage/v1:lineage_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-data_catalog-lineage",
        "ruby-cloud-wrapper-of=v1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/data-catalog/docs/reference/data-lineage/rpc",
        "ruby-cloud-api-id=datalineage.googleapis.com",
        "ruby-cloud-api-shortname=datalineage",
    ],
    ruby_cloud_description = "DataCatalog is a centralized and unified data catalog service for all your Cloud resources, where users and systems can discover data, explore and curate its semantics, understand how to act on it, and help govern its usage. Lineage is used to track data flows between assets over time. You can create Lineage Events to record lineage between multiple sources and a single target, for example, when table data is based on data from multiple tables.",
    ruby_cloud_title = "Data Lineage",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-datacatalog-lineage-ruby",
    deps = [
        ":datalineage_ruby_wrapper",
    ],
)
