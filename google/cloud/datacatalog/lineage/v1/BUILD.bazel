# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "lineage_proto",
    srcs = [
        "lineage.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "lineage_proto_with_info",
    deps = [
        ":lineage_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "lineage_java_proto",
    deps = [":lineage_proto"],
)

java_grpc_library(
    name = "lineage_java_grpc",
    srcs = [":lineage_proto"],
    deps = [":lineage_java_proto"],
)

java_gapic_library(
    name = "lineage_java_gapic",
    srcs = [":lineage_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "lineage_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datalineage_v1.yaml",
    test_deps = [
        ":lineage_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":lineage_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "lineage_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.datacatalog.lineage.v1.LineageClientHttpJsonTest",
        "com.google.cloud.datacatalog.lineage.v1.LineageClientTest",
    ],
    runtime_deps = [":lineage_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-datacatalog-lineage-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":lineage_java_gapic",
        ":lineage_java_grpc",
        ":lineage_java_proto",
        ":lineage_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "lineage_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/datacatalog/lineage/apiv1/lineagepb",
    protos = [":lineage_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "lineage_go_gapic",
    srcs = [":lineage_proto_with_info"],
    grpc_service_config = "lineage_grpc_service_config.json",
    importpath = "cloud.google.com/go/datacatalog/lineage/apiv1;lineage",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "datalineage_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":lineage_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-datacatalog-lineage-v1-go",
    deps = [
        ":lineage_go_gapic",
        ":lineage_go_gapic_srcjar-metadata.srcjar",
        ":lineage_go_gapic_srcjar-snippets.srcjar",
        ":lineage_go_gapic_srcjar-test.srcjar",
        ":lineage_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "lineage_py_gapic",
    srcs = [":lineage_proto"],
    grpc_service_config = "lineage_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datalineage_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=datacatalog_lineage",
    ],
)

py_test(
    name = "lineage_py_gapic_test",
    srcs = [
        "lineage_py_gapic_pytest.py",
        "lineage_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":lineage_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "datacatalog-lineage-v1-py",
    deps = [
        ":lineage_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "lineage_php_proto",
    deps = [":lineage_proto"],
)

php_gapic_library(
    name = "lineage_php_gapic",
    srcs = [":lineage_proto_with_info"],
    grpc_service_config = "lineage_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = False,
    service_yaml = "datalineage_v1.yaml",
    transport = "grpc+rest",
    deps = [":lineage_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-datacatalog-lineage-v1-php",
    deps = [
        ":lineage_php_gapic",
        ":lineage_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "lineage_nodejs_gapic",
    package_name = "@google-cloud/lineage",
    src = ":lineage_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "lineage_grpc_service_config.json",
    package = "google.cloud.datacatalog.lineage.v1",
    rest_numeric_enums = True,
    service_yaml = "datalineage_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "datacatalog-lineage-v1-nodejs",
    deps = [
        ":lineage_nodejs_gapic",
        ":lineage_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "lineage_ruby_proto",
    deps = [":lineage_proto"],
)

ruby_grpc_library(
    name = "lineage_ruby_grpc",
    srcs = [":lineage_proto"],
    deps = [":lineage_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "lineage_ruby_gapic",
    srcs = [":lineage_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=datalineage.googleapis.com",
        "ruby-cloud-api-shortname=datalineage",
        "ruby-cloud-gem-name=google-cloud-data_catalog-lineage-v1",
        "ruby-cloud-product-url=https://cloud.google.com/data-catalog/docs/reference/data-lineage/rpc",
    ],
    grpc_service_config = "lineage_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "DataCatalog is a centralized and unified data catalog service for all your Cloud resources, where users and systems can discover data, explore and curate its semantics, understand how to act on it, and help govern its usage. Lineage is used to track data flows between assets over time. You can create Lineage Events to record lineage between multiple sources and a single target, for example, when table data is based on data from multiple tables.",
    ruby_cloud_title = "Data Lineage V1",
    service_yaml = "datalineage_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":lineage_ruby_grpc",
        ":lineage_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-datacatalog-lineage-v1-ruby",
    deps = [
        ":lineage_ruby_gapic",
        ":lineage_ruby_grpc",
        ":lineage_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "lineage_csharp_proto",
    deps = [":lineage_proto"],
)

csharp_grpc_library(
    name = "lineage_csharp_grpc",
    srcs = [":lineage_proto"],
    deps = [":lineage_csharp_proto"],
)

csharp_gapic_library(
    name = "lineage_csharp_gapic",
    srcs = [":lineage_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "lineage_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "datalineage_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":lineage_csharp_grpc",
        ":lineage_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-datacatalog-lineage-v1-csharp",
    deps = [
        ":lineage_csharp_gapic",
        ":lineage_csharp_grpc",
        ":lineage_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "lineage_cc_proto",
    deps = [":lineage_proto"],
)

cc_grpc_library(
    name = "lineage_cc_grpc",
    srcs = [":lineage_proto"],
    grpc_only = True,
    deps = [":lineage_cc_proto"],
)
