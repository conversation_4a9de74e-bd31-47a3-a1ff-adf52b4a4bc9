type: google.api.Service
config_version: 3
name: datalineage.googleapis.com
title: Data Lineage API

apis:
- name: google.cloud.datacatalog.lineage.v1.Lineage
- name: google.longrunning.Operations

types:
- name: google.cloud.datacatalog.lineage.v1.OperationMetadata
- name: google.cloud.datacatalog.lineage.v1.ProcessOpenLineageRunEventResponse

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.datacatalog.lineage.v1.Lineage.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
