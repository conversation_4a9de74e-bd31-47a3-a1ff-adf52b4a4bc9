# This build file includes a target for the Ruby wrapper library for
# google-cloud-data_catalog.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for datacatalog.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "datacatalog_ruby_wrapper",
    srcs = ["//google/cloud/datacatalog/v1:datacatalog_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-data_catalog",
        "ruby-cloud-env-prefix=DATA_CATALOG",
        "ruby-cloud-wrapper-of=v1:0.21",
        "ruby-cloud-product-url=https://cloud.google.com/data-catalog",
        "ruby-cloud-api-id=datacatalog.googleapis.com",
        "ruby-cloud-api-shortname=datacatalog",
    ],
    ruby_cloud_description = "Data Catalog is a centralized and unified data catalog service for all your Cloud resources, where users and systems can discover data, explore and curate its semantics, understand how to act on it, and help govern its usage.",
    ruby_cloud_title = "Data Catalog",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-datacatalog-ruby",
    deps = [
        ":datacatalog_ruby_wrapper",
    ],
)
