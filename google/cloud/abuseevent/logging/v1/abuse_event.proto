// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.abuseevent.logging.v1;

import "google/api/field_info.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.AbuseEvent.Logging.V1";
option go_package = "cloud.google.com/go/abuseevent/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "AbuseEventProto";
option java_package = "com.google.cloud.abuseevent.logging.v1";

// Logged event discussing an abuse finding on the monitored resource.
message AbuseEvent {
  // Indicates the type of abuse detected on the monitored resource.
  enum DetectionType {
    // Default/unspecified abuse detection type.
    DETECTION_TYPE_UNSPECIFIED = 0;

    // The monitored resource is used to mine cryptocurrencies.
    CRYPTO_MINING = 1;

    // The monitored resource's credentials have accidentally been leaked on the
    // internet.
    LEAKED_CREDENTIALS = 2;

    // The monitored resource is used for phishing attacks.
    PHISHING = 3;

    // The monitored resource is associated with content that is infected or
    // infects users with malicious software.
    MALWARE = 4;

    // No abuse is detected on the monitored resource, but its abuse state may
    // have been updated.
    NO_ABUSE = 5;
  }

  // Indicates any action taken on the monitored resource due to abuse
  // detection.
  enum ActionType {
    // The monitored resource state is unspecified.
    ACTION_TYPE_UNSPECIFIED = 0;

    // The owner of the monitored resource needs to be notified about a finding.
    NOTIFY = 1;

    // The associated cloud project has been suspended.
    PROJECT_SUSPENSION = 2;

    // The monitored resource has been reinstated.
    REINSTATE = 3;

    // The owner of the monitored resource needs to be warned about abusive
    // behavior. This notification could lead to future enforcements if remedial
    // actions are not taken. This action should not be confused with NOTIFY.
    WARN = 4;

    // The resource (eg: VM) has been suspended.
    RESOURCE_SUSPENSION = 5;
  }

  // REQUIRED Correlates to the abuse finding causing the notification.
  DetectionType detection_type = 1;

  // REQUIRED Explains the meaning of the detection_type.
  string reason = 2;

  // REQUIRED Correlates to the action taken on the monitored resource.
  ActionType action = 3;

  // REQUIRED Contains addiional metadata about the detected abuse event.
  oneof event_type {
    // Information about a cryptocurrency mining event observed on the monitored
    // resource.
    CryptoMiningEvent crypto_mining_event = 4;

    // Information about the leaked client credentials observed on the monitored
    // resource.
    LeakedCredentialEvent leaked_credential_event = 5;

    // Information about the phishing/malware URI(s) associated with the
    // monitored resource.
    HarmfulContentEvent harmful_content_event = 6;

    // Information about the reinstatement issued on the monitored resource.
    ReinstatementEvent reinstatement_event = 8;

    // Information about the escalation of enforcement action on the monitored
    // resource.
    DecisionEscalationEvent decision_escalation_event = 9;
  }

  // Contains the umbrella link for remediation after an abuse notification.
  string remediation_link = 7;
}

// Information about a cryptocurrency mining event observed on the monitored
// resource.
message CryptoMiningEvent {
  // VM and zone in which cryptocurrency mining occurred.
  // Format: projects/{project}/zones/{zone}/instances/{instance}
  repeated string vm_resource = 1;

  // Detected start time of the cryptocurrency mining.
  google.protobuf.Timestamp detected_mining_start_time = 2;

  // Detected end time of the cryptocurrency mining.
  google.protobuf.Timestamp detected_mining_end_time = 3;

  // The IP address(es) of the VM associated with the cryptocurrency mining.
  // This field may be empty if this information is not available.
  repeated string vm_ip = 4 [(google.api.field_info).format = IPV4_OR_IPV6];
}

// Information about the leaked client credentials observed on the monitored
// resource.
message LeakedCredentialEvent {
  // Indicates the type of credential leaked.
  oneof credential_type {
    // Information about leaked service accounts.
    ServiceAccountCredential service_account_credential = 1;

    // Information about leaked API keys.
    ApiKeyCredential api_key_credential = 2;
  }

  // URI where the client credentials were found.
  string detected_uri = 3;
}

// Information about leaked service accounts.
message ServiceAccountCredential {
  // Service account whose credentials were leaked.
  string service_account = 1;

  // Private Key ID for any leaked keys.
  string key_id = 2;
}

// Information about leaked API keys.
message ApiKeyCredential {
  // API key that was leaked.
  string api_key = 1;
}

// Information about the phishing/malware URIs associated with the monitored
// resource.
message HarmfulContentEvent {
  // URIs associated with the phishing/malware finding.
  repeated string uri = 1;
}

// Information about the reinstatement issued on the monitored resource.
message ReinstatementEvent {}

// Information about the escalation of enforcement action on the monitored
// resource. Eg: Used for repeat offenders.
message DecisionEscalationEvent {}
