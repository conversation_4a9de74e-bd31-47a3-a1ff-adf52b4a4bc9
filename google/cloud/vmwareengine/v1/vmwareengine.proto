// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.vmwareengine.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/vmwareengine/v1/vmwareengine_resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.VmwareEngine.V1";
option go_package = "cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb;vmwareenginepb";
option java_multiple_files = true;
option java_outer_classname = "VmwareengineProto";
option java_package = "com.google.cloud.vmwareengine.v1";
option php_namespace = "Google\\Cloud\\VmwareEngine\\V1";
option ruby_package = "Google::Cloud::VmwareEngine::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Network"
  pattern: "projects/{project}/global/networks/{network}"
};

// VMwareEngine manages VMware's private clusters in the Cloud.
service VmwareEngine {
  option (google.api.default_host) = "vmwareengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists `PrivateCloud` resources in a given project and location.
  rpc ListPrivateClouds(ListPrivateCloudsRequest)
      returns (ListPrivateCloudsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/privateClouds"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a `PrivateCloud` resource by its resource name.
  rpc GetPrivateCloud(GetPrivateCloudRequest) returns (PrivateCloud) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateClouds/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new `PrivateCloud` resource in a given project and location.
  // Private clouds of type `STANDARD` and
  // `TIME_LIMITED` are zonal resources, `STRETCHED` private clouds are
  // regional.
  // Creating a private cloud also creates a [management
  // cluster](https://cloud.google.com/vmware-engine/docs/concepts-vmware-components)
  // for that private cloud.
  rpc CreatePrivateCloud(CreatePrivateCloudRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/privateClouds"
      body: "private_cloud"
    };
    option (google.api.method_signature) =
        "parent,private_cloud,private_cloud_id";
    option (google.longrunning.operation_info) = {
      response_type: "PrivateCloud"
      metadata_type: "OperationMetadata"
    };
  }

  // Modifies a `PrivateCloud` resource. Only the following fields can be
  // updated: `description`.
  // Only fields specified in `updateMask` are applied.
  //
  // During operation processing, the resource is temporarily in the `ACTIVE`
  // state before the operation fully completes. For that period of time, you
  // can't update the resource. Use the operation status to determine when the
  // processing fully completes.
  rpc UpdatePrivateCloud(UpdatePrivateCloudRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{private_cloud.name=projects/*/locations/*/privateClouds/*}"
      body: "private_cloud"
    };
    option (google.api.method_signature) = "private_cloud,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "PrivateCloud"
      metadata_type: "OperationMetadata"
    };
  }

  // Schedules a `PrivateCloud` resource for deletion.
  //
  // A `PrivateCloud` resource scheduled for deletion has `PrivateCloud.state`
  // set to `DELETED` and `expireTime` set to the time when deletion is final
  // and can no longer be reversed. The delete operation is marked as done
  // as soon as the `PrivateCloud` is successfully scheduled for deletion
  // (this also applies when `delayHours` is set to zero), and the operation is
  // not kept in pending state until `PrivateCloud` is purged.
  // `PrivateCloud` can be restored using `UndeletePrivateCloud` method before
  // the `expireTime` elapses. When `expireTime` is reached, deletion is final
  // and all private cloud resources are irreversibly removed and billing stops.
  // During the final removal process, `PrivateCloud.state` is set to `PURGING`.
  // `PrivateCloud` can be polled using standard `GET` method for the whole
  // period of deletion and purging. It will not be returned only
  // when it is completely purged.
  rpc DeletePrivateCloud(DeletePrivateCloudRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/privateClouds/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "PrivateCloud"
      metadata_type: "OperationMetadata"
    };
  }

  // Restores a private cloud that was previously scheduled for deletion by
  // `DeletePrivateCloud`. A `PrivateCloud` resource scheduled for deletion has
  // `PrivateCloud.state` set to `DELETED` and `PrivateCloud.expireTime` set to
  // the time when deletion can no longer be reversed.
  rpc UndeletePrivateCloud(UndeletePrivateCloudRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/privateClouds/*}:undelete"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "PrivateCloud"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists `Cluster` resources in a given private cloud.
  rpc ListClusters(ListClustersRequest) returns (ListClustersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/privateClouds/*}/clusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a `Cluster` resource by its resource name.
  rpc GetCluster(GetClusterRequest) returns (Cluster) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateClouds/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new cluster in a given private cloud.
  // Creating a new cluster provides additional nodes for
  // use in the parent private cloud and requires sufficient [node
  // quota](https://cloud.google.com/vmware-engine/quotas).
  rpc CreateCluster(CreateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/privateClouds/*}/clusters"
      body: "cluster"
    };
    option (google.api.method_signature) = "parent,cluster,cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Modifies a `Cluster` resource. Only fields specified in `updateMask` are
  // applied.
  //
  // During operation processing, the resource is temporarily in the `ACTIVE`
  // state before the operation fully completes. For that period of time, you
  // can't update the resource. Use the operation status to determine when the
  // processing fully completes.
  rpc UpdateCluster(UpdateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{cluster.name=projects/*/locations/*/privateClouds/*/clusters/*}"
      body: "cluster"
    };
    option (google.api.method_signature) = "cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a `Cluster` resource. To avoid unintended data loss, migrate or
  // gracefully shut down any workloads running on the cluster before deletion.
  // You cannot delete the management cluster of a private cloud using this
  // method.
  rpc DeleteCluster(DeleteClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/privateClouds/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists nodes in a given cluster.
  rpc ListNodes(ListNodesRequest) returns (ListNodesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/privateClouds/*/clusters/*}/nodes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single node.
  rpc GetNode(GetNodeRequest) returns (Node) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateClouds/*/clusters/*/nodes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists external IP addresses assigned to VMware workload VMs in a given
  // private cloud.
  rpc ListExternalAddresses(ListExternalAddressesRequest)
      returns (ListExternalAddressesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/privateClouds/*}/externalAddresses"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists external IP addresses assigned to VMware workload VMs within the
  // scope of the given network policy.
  rpc FetchNetworkPolicyExternalAddresses(
      FetchNetworkPolicyExternalAddressesRequest)
      returns (FetchNetworkPolicyExternalAddressesResponse) {
    option (google.api.http) = {
      get: "/v1/{network_policy=projects/*/locations/*/networkPolicies/*}:fetchExternalAddresses"
    };
    option (google.api.method_signature) = "network_policy";
  }

  // Gets details of a single external IP address.
  rpc GetExternalAddress(GetExternalAddressRequest) returns (ExternalAddress) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateClouds/*/externalAddresses/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new `ExternalAddress` resource in a given private cloud. The
  // network policy that corresponds to the private cloud must have the external
  // IP address network service enabled (`NetworkPolicy.external_ip`).
  rpc CreateExternalAddress(CreateExternalAddressRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/privateClouds/*}/externalAddresses"
      body: "external_address"
    };
    option (google.api.method_signature) =
        "parent,external_address,external_address_id";
    option (google.longrunning.operation_info) = {
      response_type: "ExternalAddress"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single external IP address.
  // Only fields specified in `update_mask` are applied.
  //
  // During operation processing, the resource is temporarily in the `ACTIVE`
  // state before the operation fully completes. For that period of time, you
  // can't update the resource. Use the operation status to determine when the
  // processing fully completes.
  rpc UpdateExternalAddress(UpdateExternalAddressRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{external_address.name=projects/*/locations/*/privateClouds/*/externalAddresses/*}"
      body: "external_address"
    };
    option (google.api.method_signature) = "external_address,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ExternalAddress"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single external IP address. When you delete an external IP
  // address, connectivity between the external IP address and the corresponding
  // internal IP address is lost.
  rpc DeleteExternalAddress(DeleteExternalAddressRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/privateClouds/*/externalAddresses/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists subnets in a given private cloud.
  rpc ListSubnets(ListSubnetsRequest) returns (ListSubnetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/privateClouds/*}/subnets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single subnet.
  rpc GetSubnet(GetSubnetRequest) returns (Subnet) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateClouds/*/subnets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the parameters of a single subnet. Only fields specified in
  // `update_mask` are applied.
  //
  // *Note*: This API is synchronous and always returns a successful
  // `google.longrunning.Operation` (LRO). The returned LRO will only have
  // `done` and `response` fields.
  rpc UpdateSubnet(UpdateSubnetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{subnet.name=projects/*/locations/*/privateClouds/*/subnets/*}"
      body: "subnet"
    };
    option (google.api.method_signature) = "subnet,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Subnet"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists `ExternalAccessRule` resources in the specified network policy.
  rpc ListExternalAccessRules(ListExternalAccessRulesRequest)
      returns (ListExternalAccessRulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/networkPolicies/*}/externalAccessRules"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single external access rule.
  rpc GetExternalAccessRule(GetExternalAccessRuleRequest)
      returns (ExternalAccessRule) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/networkPolicies/*/externalAccessRules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new external access rule in a given network policy.
  rpc CreateExternalAccessRule(CreateExternalAccessRuleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/networkPolicies/*}/externalAccessRules"
      body: "external_access_rule"
    };
    option (google.api.method_signature) =
        "parent,external_access_rule,external_access_rule_id";
    option (google.longrunning.operation_info) = {
      response_type: "ExternalAccessRule"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single external access rule.
  // Only fields specified in `update_mask` are applied.
  rpc UpdateExternalAccessRule(UpdateExternalAccessRuleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{external_access_rule.name=projects/*/locations/*/networkPolicies/*/externalAccessRules/*}"
      body: "external_access_rule"
    };
    option (google.api.method_signature) = "external_access_rule,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ExternalAccessRule"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single external access rule.
  rpc DeleteExternalAccessRule(DeleteExternalAccessRuleRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/networkPolicies/*/externalAccessRules/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists logging servers configured for a given private
  // cloud.
  rpc ListLoggingServers(ListLoggingServersRequest)
      returns (ListLoggingServersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/privateClouds/*}/loggingServers"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a logging server.
  rpc GetLoggingServer(GetLoggingServerRequest) returns (LoggingServer) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateClouds/*/loggingServers/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Create a new logging server for a given private cloud.
  rpc CreateLoggingServer(CreateLoggingServerRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/privateClouds/*}/loggingServers"
      body: "logging_server"
    };
    option (google.api.method_signature) =
        "parent,logging_server,logging_server_id";
    option (google.longrunning.operation_info) = {
      response_type: "LoggingServer"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single logging server.
  // Only fields specified in `update_mask` are applied.
  rpc UpdateLoggingServer(UpdateLoggingServerRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{logging_server.name=projects/*/locations/*/privateClouds/*/loggingServers/*}"
      body: "logging_server"
    };
    option (google.api.method_signature) = "logging_server,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "LoggingServer"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single logging server.
  rpc DeleteLoggingServer(DeleteLoggingServerRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/privateClouds/*/loggingServers/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists node types
  rpc ListNodeTypes(ListNodeTypesRequest) returns (ListNodeTypesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/nodeTypes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single `NodeType`.
  rpc GetNodeType(GetNodeTypeRequest) returns (NodeType) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/nodeTypes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets details of credentials for NSX appliance.
  rpc ShowNsxCredentials(ShowNsxCredentialsRequest) returns (Credentials) {
    option (google.api.http) = {
      get: "/v1/{private_cloud=projects/*/locations/*/privateClouds/*}:showNsxCredentials"
    };
    option (google.api.method_signature) = "private_cloud";
  }

  // Gets details of credentials for Vcenter appliance.
  rpc ShowVcenterCredentials(ShowVcenterCredentialsRequest)
      returns (Credentials) {
    option (google.api.http) = {
      get: "/v1/{private_cloud=projects/*/locations/*/privateClouds/*}:showVcenterCredentials"
    };
    option (google.api.method_signature) = "private_cloud";
  }

  // Resets credentials of the NSX appliance.
  rpc ResetNsxCredentials(ResetNsxCredentialsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{private_cloud=projects/*/locations/*/privateClouds/*}:resetNsxCredentials"
      body: "*"
    };
    option (google.api.method_signature) = "private_cloud";
    option (google.longrunning.operation_info) = {
      response_type: "PrivateCloud"
      metadata_type: "OperationMetadata"
    };
  }

  // Resets credentials of the Vcenter appliance.
  rpc ResetVcenterCredentials(ResetVcenterCredentialsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{private_cloud=projects/*/locations/*/privateClouds/*}:resetVcenterCredentials"
      body: "*"
    };
    option (google.api.method_signature) = "private_cloud";
    option (google.longrunning.operation_info) = {
      response_type: "PrivateCloud"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of the `DnsForwarding` config.
  rpc GetDnsForwarding(GetDnsForwardingRequest) returns (DnsForwarding) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateClouds/*/dnsForwarding}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the parameters of the `DnsForwarding` config, like associated
  // domains. Only fields specified in `update_mask` are applied.
  rpc UpdateDnsForwarding(UpdateDnsForwardingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{dns_forwarding.name=projects/*/locations/*/privateClouds/*/dnsForwarding}"
      body: "dns_forwarding"
    };
    option (google.api.method_signature) = "dns_forwarding,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "DnsForwarding"
      metadata_type: "OperationMetadata"
    };
  }

  // Retrieves a `NetworkPeering` resource by its resource name. The resource
  // contains details of the network peering, such as peered
  // networks, import and export custom route configurations, and peering state.
  // NetworkPeering is a global resource and location can only be global.
  rpc GetNetworkPeering(GetNetworkPeeringRequest) returns (NetworkPeering) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/networkPeerings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists `NetworkPeering` resources in a given project. NetworkPeering is a
  // global resource and location can only be global.
  rpc ListNetworkPeerings(ListNetworkPeeringsRequest)
      returns (ListNetworkPeeringsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/networkPeerings"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new network peering between the peer network and VMware Engine
  // network provided in a `NetworkPeering` resource. NetworkPeering is a
  // global resource and location can only be global.
  rpc CreateNetworkPeering(CreateNetworkPeeringRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/networkPeerings"
      body: "network_peering"
    };
    option (google.api.method_signature) =
        "parent,network_peering,network_peering_id";
    option (google.longrunning.operation_info) = {
      response_type: "NetworkPeering"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a `NetworkPeering` resource. When a network peering is deleted for
  // a VMware Engine network, the peer network becomes inaccessible to that
  // VMware Engine network. NetworkPeering is a global resource and location can
  // only be global.
  rpc DeleteNetworkPeering(DeleteNetworkPeeringRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/networkPeerings/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Modifies a `NetworkPeering` resource. Only the `description` field can be
  // updated. Only fields specified in `updateMask` are applied. NetworkPeering
  // is a global resource and location can only be global.
  rpc UpdateNetworkPeering(UpdateNetworkPeeringRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{network_peering.name=projects/*/locations/*/networkPeerings/*}"
      body: "network_peering"
    };
    option (google.api.method_signature) = "network_peering,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "NetworkPeering"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the network peering routes exchanged over a peering connection.
  // NetworkPeering is a global resource and location can only be global.
  rpc ListPeeringRoutes(ListPeeringRoutesRequest)
      returns (ListPeeringRoutesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/networkPeerings/*}/peeringRoutes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new HCX activation key in a given private cloud.
  rpc CreateHcxActivationKey(CreateHcxActivationKeyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/privateClouds/*}/hcxActivationKeys"
      body: "hcx_activation_key"
    };
    option (google.api.method_signature) =
        "parent,hcx_activation_key,hcx_activation_key_id";
    option (google.longrunning.operation_info) = {
      response_type: "HcxActivationKey"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists `HcxActivationKey` resources in a given private cloud.
  rpc ListHcxActivationKeys(ListHcxActivationKeysRequest)
      returns (ListHcxActivationKeysResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/privateClouds/*}/hcxActivationKeys"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a `HcxActivationKey` resource by its resource name.
  rpc GetHcxActivationKey(GetHcxActivationKeyRequest)
      returns (HcxActivationKey) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateClouds/*/hcxActivationKeys/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves a `NetworkPolicy` resource by its resource name.
  rpc GetNetworkPolicy(GetNetworkPolicyRequest) returns (NetworkPolicy) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/networkPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists `NetworkPolicy` resources in a specified project and location.
  rpc ListNetworkPolicies(ListNetworkPoliciesRequest)
      returns (ListNetworkPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/networkPolicies"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new network policy in a given VMware Engine network of a
  // project and location (region). A new network policy cannot be created if
  // another network policy already exists in the same scope.
  rpc CreateNetworkPolicy(CreateNetworkPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/networkPolicies"
      body: "network_policy"
    };
    option (google.api.method_signature) =
        "parent,network_policy,network_policy_id";
    option (google.longrunning.operation_info) = {
      response_type: "NetworkPolicy"
      metadata_type: "OperationMetadata"
    };
  }

  // Modifies a `NetworkPolicy` resource. Only the following fields can be
  // updated: `internet_access`, `external_ip`, `edge_services_cidr`.
  // Only fields specified in `updateMask` are applied. When updating a network
  // policy, the external IP network service can only be disabled if there are
  // no external IP addresses present in the scope of the policy. Also, a
  // `NetworkService` cannot be updated when `NetworkService.state` is set
  // to `RECONCILING`.
  //
  // During operation processing, the resource is temporarily in the `ACTIVE`
  // state before the operation fully completes. For that period of time, you
  // can't update the resource. Use the operation status to determine when the
  // processing fully completes.
  rpc UpdateNetworkPolicy(UpdateNetworkPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{network_policy.name=projects/*/locations/*/networkPolicies/*}"
      body: "network_policy"
    };
    option (google.api.method_signature) = "network_policy,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "NetworkPolicy"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a `NetworkPolicy` resource. A network policy cannot be deleted
  // when `NetworkService.state` is set to `RECONCILING` for either its external
  // IP or internet access service.
  rpc DeleteNetworkPolicy(DeleteNetworkPolicyRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/networkPolicies/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Consumer VPCs bound to Management DNS Zone of a given private cloud.
  rpc ListManagementDnsZoneBindings(ListManagementDnsZoneBindingsRequest)
      returns (ListManagementDnsZoneBindingsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/privateClouds/*}/managementDnsZoneBindings"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a 'ManagementDnsZoneBinding' resource by its resource name.
  rpc GetManagementDnsZoneBinding(GetManagementDnsZoneBindingRequest)
      returns (ManagementDnsZoneBinding) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateClouds/*/managementDnsZoneBindings/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new `ManagementDnsZoneBinding` resource in a private cloud.
  // This RPC creates the DNS binding and the resource that represents the
  // DNS binding of the consumer VPC network to the management DNS zone. A
  // management DNS zone is the Cloud DNS cross-project binding zone that
  // VMware Engine creates for each private cloud. It contains FQDNs and
  // corresponding IP addresses for the private cloud's ESXi hosts and
  // management VM appliances like vCenter and NSX Manager.
  rpc CreateManagementDnsZoneBinding(CreateManagementDnsZoneBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/privateClouds/*}/managementDnsZoneBindings"
      body: "management_dns_zone_binding"
    };
    option (google.api.method_signature) =
        "parent,management_dns_zone_binding,management_dns_zone_binding_id";
    option (google.longrunning.operation_info) = {
      response_type: "ManagementDnsZoneBinding"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates a `ManagementDnsZoneBinding` resource.
  // Only fields specified in `update_mask` are applied.
  rpc UpdateManagementDnsZoneBinding(UpdateManagementDnsZoneBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{management_dns_zone_binding.name=projects/*/locations/*/privateClouds/*/managementDnsZoneBindings/*}"
      body: "management_dns_zone_binding"
    };
    option (google.api.method_signature) =
        "management_dns_zone_binding,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ManagementDnsZoneBinding"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a `ManagementDnsZoneBinding` resource. When a management DNS zone
  // binding is deleted, the corresponding consumer VPC network is no longer
  // bound to the management DNS zone.
  rpc DeleteManagementDnsZoneBinding(DeleteManagementDnsZoneBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/privateClouds/*/managementDnsZoneBindings/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Retries to create a `ManagementDnsZoneBinding` resource that is
  // in failed state.
  rpc RepairManagementDnsZoneBinding(RepairManagementDnsZoneBindingRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/privateClouds/*/managementDnsZoneBindings/*}:repair"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "ManagementDnsZoneBinding"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new VMware Engine network that can be used by a private cloud.
  rpc CreateVmwareEngineNetwork(CreateVmwareEngineNetworkRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/vmwareEngineNetworks"
      body: "vmware_engine_network"
    };
    option (google.api.method_signature) =
        "parent,vmware_engine_network,vmware_engine_network_id";
    option (google.longrunning.operation_info) = {
      response_type: "VmwareEngineNetwork"
      metadata_type: "OperationMetadata"
    };
  }

  // Modifies a VMware Engine network resource. Only the following fields can be
  // updated: `description`. Only fields specified in `updateMask` are
  // applied.
  rpc UpdateVmwareEngineNetwork(UpdateVmwareEngineNetworkRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{vmware_engine_network.name=projects/*/locations/*/vmwareEngineNetworks/*}"
      body: "vmware_engine_network"
    };
    option (google.api.method_signature) = "vmware_engine_network,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "VmwareEngineNetwork"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a `VmwareEngineNetwork` resource. You can only delete a VMware
  // Engine network after all resources that refer to it are deleted. For
  // example, a private cloud, a network peering, and a network policy can all
  // refer to the same VMware Engine network.
  rpc DeleteVmwareEngineNetwork(DeleteVmwareEngineNetworkRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/vmwareEngineNetworks/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Retrieves a `VmwareEngineNetwork` resource by its resource name. The
  // resource contains details of the VMware Engine network, such as its VMware
  // Engine network type, peered networks in a service project, and state
  // (for example, `CREATING`, `ACTIVE`, `DELETING`).
  rpc GetVmwareEngineNetwork(GetVmwareEngineNetworkRequest)
      returns (VmwareEngineNetwork) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/vmwareEngineNetworks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists `VmwareEngineNetwork` resources in a given project and location.
  rpc ListVmwareEngineNetworks(ListVmwareEngineNetworksRequest)
      returns (ListVmwareEngineNetworksResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/vmwareEngineNetworks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new private connection that can be used for accessing private
  // Clouds.
  rpc CreatePrivateConnection(CreatePrivateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/privateConnections"
      body: "private_connection"
    };
    option (google.api.method_signature) =
        "parent,private_connection,private_connection_id";
    option (google.longrunning.operation_info) = {
      response_type: "PrivateConnection"
      metadata_type: "OperationMetadata"
    };
  }

  // Retrieves a `PrivateConnection` resource by its resource name. The resource
  // contains details of the private connection, such as connected
  // network, routing mode and state.
  rpc GetPrivateConnection(GetPrivateConnectionRequest)
      returns (PrivateConnection) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateConnections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists `PrivateConnection` resources in a given project and location.
  rpc ListPrivateConnections(ListPrivateConnectionsRequest)
      returns (ListPrivateConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/privateConnections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Modifies a `PrivateConnection` resource. Only `description` and
  // `routing_mode` fields can be updated. Only fields specified in `updateMask`
  // are applied.
  rpc UpdatePrivateConnection(UpdatePrivateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{private_connection.name=projects/*/locations/*/privateConnections/*}"
      body: "private_connection"
    };
    option (google.api.method_signature) = "private_connection,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "PrivateConnection"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a `PrivateConnection` resource. When a private connection is
  // deleted for a VMware Engine network, the connected network becomes
  // inaccessible to that VMware Engine network.
  rpc DeletePrivateConnection(DeletePrivateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/privateConnections/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the private connection routes exchanged over a peering connection.
  rpc ListPrivateConnectionPeeringRoutes(
      ListPrivateConnectionPeeringRoutesRequest)
      returns (ListPrivateConnectionPeeringRoutesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/privateConnections/*}/peeringRoutes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Grants the bind permission to the customer provided principal(user /
  // service account) to bind their DNS zone with the intranet VPC associated
  // with the project. DnsBindPermission is a global resource and location can
  // only be global.
  rpc GrantDnsBindPermission(GrantDnsBindPermissionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/dnsBindPermission}:grant"
      body: "*"
    };
    option (google.api.method_signature) = "name,principal";
    option (google.longrunning.operation_info) = {
      response_type: "DnsBindPermission"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets all the principals having bind permission on the intranet VPC
  // associated with the consumer project granted by the Grant API.
  // DnsBindPermission is a global resource and location can only be global.
  rpc GetDnsBindPermission(GetDnsBindPermissionRequest)
      returns (DnsBindPermission) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/dnsBindPermission}"
    };
    option (google.api.method_signature) = "name";
  }

  // Revokes the bind permission from the customer provided principal(user /
  // service account) on the intranet VPC associated with the consumer project.
  // DnsBindPermission is a global resource and location can only be global.
  rpc RevokeDnsBindPermission(RevokeDnsBindPermissionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/dnsBindPermission}:revoke"
      body: "*"
    };
    option (google.api.method_signature) = "name,principal";
    option (google.longrunning.operation_info) = {
      response_type: "DnsBindPermission"
      metadata_type: "OperationMetadata"
    };
  }
}

// Request message for
// [VmwareEngine.ListPrivateClouds][google.cloud.vmwareengine.v1.VmwareEngine.ListPrivateClouds]
message ListPrivateCloudsRequest {
  // Required. The resource name of the private cloud to be queried for
  // clusters. Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of private clouds to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListPrivateClouds` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListPrivateClouds` must
  // match the call that provided the page token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // The expression must specify the field name, a comparison operator, and the
  // value that you want to use for filtering. The value must be a string, a
  // number, or a boolean. The comparison operator must be `=`, `!=`, `>`, or
  // `<`.
  //
  // For example, if you are filtering a list of private clouds, you can exclude
  // the ones named `example-pc` by specifying `name != "example-pc"`.
  //
  // You can also filter nested fields. For example, you could specify
  // `networkConfig.managementCidr = "***********/24"` to include private clouds
  // only if they have a matching address in their network configuration.
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "example-pc")
  // (createTime > "2021-04-12T08:15:10.40Z")
  // ```
  //
  // By default, each expression is an `AND` expression. However, you can
  // include `AND` and `OR` expressions explicitly. For example:
  // ```
  // (name = "private-cloud-1") AND
  // (createTime > "2021-04-12T08:15:10.40Z") OR
  // (name = "private-cloud-2")
  // ```
  string filter = 4;

  // Sorts list results by a certain order. By default, returned results are
  // ordered by `name` in ascending order. You can also sort results in
  // descending order based on the `name` value using `orderBy="name desc"`.
  // Currently, only ordering by `name` is supported.
  string order_by = 5;
}

// Response message for
// [VmwareEngine.ListPrivateClouds][google.cloud.vmwareengine.v1.VmwareEngine.ListPrivateClouds]
message ListPrivateCloudsResponse {
  // A list of private clouds.
  repeated PrivateCloud private_clouds = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached when making an aggregated query using
  // wildcards.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.GetPrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.GetPrivateCloud]
message GetPrivateCloudRequest {
  // Required. The resource name of the private cloud to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];
}

// Request message for
// [VmwareEngine.CreatePrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.CreatePrivateCloud]
message CreatePrivateCloudRequest {
  // Required. The resource name of the location to create the new
  // private cloud in. Resource names are schemeless URIs that follow the
  // conventions in https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The user-provided identifier of the private cloud to be created.
  // This identifier must be unique among each `PrivateCloud` within the parent
  // and becomes the final token in the name URI.
  // The identifier must meet the following requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string private_cloud_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The initial description of the new private cloud.
  PrivateCloud private_cloud = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The request ID must be a valid UUID with the exception that zero
  // UUID is not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. True if you want the request to be validated and not executed;
  // false otherwise.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.UpdatePrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.UpdatePrivateCloud]
message UpdatePrivateCloudRequest {
  // Required. Private cloud description.
  PrivateCloud private_cloud = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Field mask is used to specify the fields to be overwritten in the
  // `PrivateCloud` resource by the update. The fields specified in `updateMask`
  // are relative to the resource, not the full request. A field will be
  // overwritten if it is in the mask. If the user does not provide a mask then
  // all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The request ID must be a valid UUID with the exception that zero
  // UUID is not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.DeletePrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.DeletePrivateCloud]
message DeletePrivateCloudRequest {
  // Required. The resource name of the private cloud to delete.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // Optional. The request ID must be a valid UUID with the exception that zero
  // UUID is not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, cascade delete is enabled and all children of
  // this private cloud resource are also deleted. When this flag is set to
  // false, the private cloud will not be deleted if there are any children
  // other than the management cluster. The management cluster is always
  // deleted.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Time delay of the deletion specified in hours. The default value
  // is `3`. Specifying a non-zero value for this field changes the value of
  // `PrivateCloud.state` to `DELETED` and sets `expire_time` to the planned
  // deletion time. Deletion can be cancelled before `expire_time` elapses using
  // [VmwareEngine.UndeletePrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.UndeletePrivateCloud].
  // Specifying a value of `0` for this field instead begins the deletion
  // process and ceases billing immediately. During the final deletion process,
  // the value of `PrivateCloud.state` becomes `PURGING`.
  optional int32 delay_hours = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.UndeletePrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.UndeletePrivateCloud]
message UndeletePrivateCloudRequest {
  // Required. The resource name of the private cloud scheduled for deletion.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // Optional. The request ID must be a valid UUID with the exception that zero
  // UUID is not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.ListClusters][google.cloud.vmwareengine.v1.VmwareEngine.ListClusters]
message ListClustersRequest {
  // Required. The resource name of the private cloud to query for clusters.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // The maximum number of clusters to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListClusters` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListClusters`
  // must match the call that provided the page token.
  string page_token = 3;

  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "example-cluster")
  // (nodeCount = "3")
  // ```
  //
  // By default, each expression is an `AND` expression. However, you can
  // include `AND` and `OR` expressions explicitly. For example:
  // ```
  // (name = "example-cluster-1") AND
  // (createTime > "2021-04-12T08:15:10.40Z") OR
  // (name = "example-cluster-2")
  // ```
  string filter = 4;

  // Sorts list results by a certain order. By default, returned results are
  // ordered by `name` in ascending order. You can also sort results in
  // descending order based on the `name` value using `orderBy="name desc"`.
  // Currently, only ordering by `name` is supported.
  string order_by = 5;
}

// Response message for
// [VmwareEngine.ListClusters][google.cloud.vmwareengine.v1.VmwareEngine.ListClusters]
message ListClustersResponse {
  // A list of private cloud clusters.
  repeated Cluster clusters = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached when making an aggregated query using
  // wildcards.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.GetCluster][google.cloud.vmwareengine.v1.VmwareEngine.GetCluster]
message GetClusterRequest {
  // Required. The cluster resource name to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/Cluster"
    }
  ];
}

// Request message for
// [VmwareEngine.CreateCluster][google.cloud.vmwareengine.v1.VmwareEngine.CreateCluster]
message CreateClusterRequest {
  // Required. The resource name of the private cloud to create a new cluster
  // in. Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // Required. The user-provided identifier of the new `Cluster`.
  // This identifier must be unique among clusters within the parent and becomes
  // the final token in the name URI.
  // The identifier must meet the following requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The initial description of the new cluster.
  Cluster cluster = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The request ID must be a valid UUID with the exception that zero
  // UUID is not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. True if you want the request to be validated and not executed;
  // false otherwise.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.UpdateCluster][google.cloud.vmwareengine.v1.VmwareEngine.UpdateCluster]
message UpdateClusterRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `Cluster` resource by the update. The fields specified in the `updateMask`
  // are relative to the resource, not the full request. A field will be
  // overwritten if it is in the mask. If the user does not provide a mask then
  // all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The description of the cluster.
  Cluster cluster = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The request ID must be a valid UUID with the exception that
  // zero UUID is not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. True if you want the request to be validated and not executed;
  // false otherwise.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.DeleteCluster][google.cloud.vmwareengine.v1.VmwareEngine.DeleteCluster]
message DeleteClusterRequest {
  // Required. The resource name of the cluster to delete.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/Cluster"
    }
  ];

  // Optional. The request ID must be a valid UUID with the exception that zero
  // UUID is not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.ListNodes][google.cloud.vmwareengine.v1.VmwareEngine.ListNodes]
message ListNodesRequest {
  // Required. The resource name of the cluster to be queried for nodes.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/Cluster"
    }
  ];

  // The maximum number of nodes to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListNodes` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListNodes` must match the call that provided the page
  // token.
  string page_token = 3;
}

// Response message for
// [VmwareEngine.ListNodes][google.cloud.vmwareengine.v1.VmwareEngine.ListNodes]
message ListNodesResponse {
  // The nodes.
  repeated Node nodes = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [VmwareEngine.GetNode][google.cloud.vmwareengine.v1.VmwareEngine.GetNode]
message GetNodeRequest {
  // Required. The resource name of the node to retrieve.
  // For example:
  // `projects/{project}/locations/{location}/privateClouds/{private_cloud}/clusters/{cluster}/nodes/{node}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/Node"
    }
  ];
}

// Request message for
// [VmwareEngine.ListExternalAddresses][google.cloud.vmwareengine.v1.VmwareEngine.ListExternalAddresses]
message ListExternalAddressesRequest {
  // Required. The resource name of the private cloud to be queried for
  // external IP addresses.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // The maximum number of external IP addresses to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListExternalAddresses` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListExternalAddresses` must match the call that provided the page token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value
  // must be a string, a number, or a boolean. The comparison operator
  // must be `=`, `!=`, `>`, or `<`.
  //
  // For example, if you are filtering a list of IP addresses, you can
  // exclude the ones named `example-ip` by specifying
  // `name != "example-ip"`.
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "example-ip")
  // (createTime > "2021-04-12T08:15:10.40Z")
  // ```
  //
  // By default, each expression is an `AND` expression. However, you
  // can include `AND` and `OR` expressions explicitly.
  // For example:
  // ```
  // (name = "example-ip-1") AND
  // (createTime > "2021-04-12T08:15:10.40Z") OR
  // (name = "example-ip-2")
  // ```
  string filter = 4;

  // Sorts list results by a certain order. By default, returned results
  // are ordered by `name` in ascending order.
  // You can also sort results in descending order based on the `name` value
  // using `orderBy="name desc"`.
  // Currently, only ordering by `name` is supported.
  string order_by = 5;
}

// Response message for
// [VmwareEngine.ListExternalAddresses][google.cloud.vmwareengine.v1.VmwareEngine.ListExternalAddresses]
message ListExternalAddressesResponse {
  // A list of external IP addresses.
  repeated ExternalAddress external_addresses = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached when making an aggregated query using
  // wildcards.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.FetchNetworkPolicyExternalAddresses][google.cloud.vmwareengine.v1.VmwareEngine.FetchNetworkPolicyExternalAddresses]
message FetchNetworkPolicyExternalAddressesRequest {
  // Required. The resource name of the network policy to query for assigned
  // external IP addresses. Resource names are schemeless URIs that follow the
  // conventions in https://cloud.google.com/apis/design/resource_names. For
  // example:
  // `projects/my-project/locations/us-central1/networkPolicies/my-policy`
  string network_policy = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/NetworkPolicy"
    }
  ];

  // The maximum number of external IP addresses to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous
  // `FetchNetworkPolicyExternalAddresses` call. Provide this to retrieve the
  // subsequent page.
  //
  // When paginating, all parameters provided to
  // `FetchNetworkPolicyExternalAddresses`, except for `page_size` and
  // `page_token`, must match the call that provided the page token.
  string page_token = 3;
}

// Response message for
// [VmwareEngine.FetchNetworkPolicyExternalAddresses][google.cloud.vmwareengine.v1.VmwareEngine.FetchNetworkPolicyExternalAddresses]
message FetchNetworkPolicyExternalAddressesResponse {
  // A list of external IP addresses assigned to VMware workload VMs within the
  // scope of the given network policy.
  repeated ExternalAddress external_addresses = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [VmwareEngine.GetExternalAddress][google.cloud.vmwareengine.v1.VmwareEngine.GetExternalAddress]
message GetExternalAddressRequest {
  // Required. The resource name of the external IP address to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/externalAddresses/my-ip`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/ExternalAddress"
    }
  ];
}

// Request message for
// [VmwareEngine.CreateExternalAddress][google.cloud.vmwareengine.v1.VmwareEngine.CreateExternalAddress]
message CreateExternalAddressRequest {
  // Required. The resource name of the private cloud
  // to create a new external IP address in.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // Required. The initial description of a new external IP address.
  ExternalAddress external_address = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided identifier of the `ExternalAddress` to be
  // created. This identifier must be unique among `ExternalAddress` resources
  // within the parent and becomes the final token in the name URI. The
  // identifier must meet the following requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string external_address_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if the original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.UpdateExternalAddress][google.cloud.vmwareengine.v1.VmwareEngine.UpdateExternalAddress]
message UpdateExternalAddressRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `ExternalAddress` resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. External IP address description.
  ExternalAddress external_address = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if the original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.DeleteExternalAddress][google.cloud.vmwareengine.v1.VmwareEngine.DeleteExternalAddress]
message DeleteExternalAddressRequest {
  // Required. The resource name of the external IP address to delete.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/externalAddresses/my-ip`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/ExternalAddress"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.ListSubnets][google.cloud.vmwareengine.v1.VmwareEngine.ListSubnets]
message ListSubnetsRequest {
  // Required. The resource name of the private cloud to be queried for
  // subnets.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // The maximum number of subnets to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListSubnetsRequest` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListSubnetsRequest` must match the call that provided the page token.
  string page_token = 3;
}

// Response message for
// [VmwareEngine.ListSubnets][google.cloud.vmwareengine.v1.VmwareEngine.ListSubnets]
message ListSubnetsResponse {
  // A list of subnets.
  repeated Subnet subnets = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached when making an aggregated query using
  // wildcards.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.GetSubnet][google.cloud.vmwareengine.v1.VmwareEngine.GetSubnet]
message GetSubnetRequest {
  // Required. The resource name of the subnet to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/subnets/my-subnet`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/Subnet"
    }
  ];
}

// Request message for
// [VmwareEngine.UpdateSubnet][google.cloud.vmwareengine.v1.VmwareEngine.UpdateSubnet]
message UpdateSubnetRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `Subnet` resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Subnet description.
  Subnet subnet = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [VmwareEngine.ListExternalAccessRules][google.cloud.vmwareengine.v1.VmwareEngine.ListExternalAccessRules]
message ListExternalAccessRulesRequest {
  // Required. The resource name of the network policy to query for external
  // access firewall rules. Resource names are schemeless URIs that follow the
  // conventions in https://cloud.google.com/apis/design/resource_names. For
  // example:
  // `projects/my-project/locations/us-central1/networkPolicies/my-policy`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/NetworkPolicy"
    }
  ];

  // The maximum number of external access rules to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListExternalAccessRulesRequest`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListExternalAccessRulesRequest` must match the call that provided the page
  // token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value
  // must be a string, a number, or a boolean. The comparison operator
  // must be `=`, `!=`, `>`, or `<`.
  //
  // For example, if you are filtering a list of external access rules, you can
  // exclude the ones named `example-rule` by specifying
  // `name != "example-rule"`.
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "example-rule")
  // (createTime > "2021-04-12T08:15:10.40Z")
  // ```
  //
  // By default, each expression is an `AND` expression. However, you
  // can include `AND` and `OR` expressions explicitly.
  // For example:
  // ```
  // (name = "example-rule-1") AND
  // (createTime > "2021-04-12T08:15:10.40Z") OR
  // (name = "example-rule-2")
  // ```
  string filter = 4;

  // Sorts list results by a certain order. By default, returned results
  // are ordered by `name` in ascending order.
  // You can also sort results in descending order based on the `name` value
  // using `orderBy="name desc"`.
  // Currently, only ordering by `name` is supported.
  string order_by = 5;
}

// Response message for
// [VmwareEngine.ListExternalAccessRules][google.cloud.vmwareengine.v1.VmwareEngine.ListExternalAccessRules]
message ListExternalAccessRulesResponse {
  // A list of external access firewall rules.
  repeated ExternalAccessRule external_access_rules = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached when making an aggregated query using
  // wildcards.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.GetExternalAccessRule][google.cloud.vmwareengine.v1.VmwareEngine.GetExternalAccessRule]
message GetExternalAccessRuleRequest {
  // Required. The resource name of the external access firewall rule to
  // retrieve. Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/networkPolicies/my-policy/externalAccessRules/my-rule`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/ExternalAccessRule"
    }
  ];
}

// Request message for
// [VmwareEngine.CreateExternalAccessRule][google.cloud.vmwareengine.v1.VmwareEngine.CreateExternalAccessRule]
message CreateExternalAccessRuleRequest {
  // Required. The resource name of the network policy
  // to create a new external access firewall rule in.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/networkPolicies/my-policy`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/NetworkPolicy"
    }
  ];

  // Required. The initial description of a new external access rule.
  ExternalAccessRule external_access_rule = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided identifier of the `ExternalAccessRule` to be
  // created. This identifier must be unique among `ExternalAccessRule`
  // resources within the parent and becomes the final token in the name URI.
  // The identifier must meet the following requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string external_access_rule_id = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if the original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4;
}

// Request message for
// [VmwareEngine.UpdateExternalAccessRule][google.cloud.vmwareengine.v1.VmwareEngine.UpdateExternalAccessRule]
message UpdateExternalAccessRuleRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `ExternalAccessRule` resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Description of the external access rule.
  ExternalAccessRule external_access_rule = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if the original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.DeleteExternalAccessRule][google.cloud.vmwareengine.v1.VmwareEngine.DeleteExternalAccessRule]
message DeleteExternalAccessRuleRequest {
  // Required. The resource name of the external access firewall rule to delete.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/networkPolicies/my-policy/externalAccessRules/my-rule`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/ExternalAccessRule"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.ListLoggingServers][google.cloud.vmwareengine.v1.VmwareEngine.ListLoggingServers]
message ListLoggingServersRequest {
  // Required. The resource name of the private cloud to be queried for
  // logging servers.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // The maximum number of logging servers to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListLoggingServersRequest` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListLoggingServersRequest` must match the call that provided the page
  // token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value
  // must be a string, a number, or a boolean. The comparison operator
  // must be `=`, `!=`, `>`, or `<`.
  //
  // For example, if you are filtering a list of logging servers, you can
  // exclude the ones named `example-server` by specifying
  // `name != "example-server"`.
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "example-server")
  // (createTime > "2021-04-12T08:15:10.40Z")
  // ```
  //
  // By default, each expression is an `AND` expression. However, you
  // can include `AND` and `OR` expressions explicitly.
  // For example:
  // ```
  // (name = "example-server-1") AND
  // (createTime > "2021-04-12T08:15:10.40Z") OR
  // (name = "example-server-2")
  // ```
  string filter = 4;

  // Sorts list results by a certain order. By default, returned results
  // are ordered by `name` in ascending order.
  // You can also sort results in descending order based on the `name` value
  // using `orderBy="name desc"`.
  // Currently, only ordering by `name` is supported.
  string order_by = 5;
}

// Response message for
// [VmwareEngine.ListLoggingServers][google.cloud.vmwareengine.v1.VmwareEngine.ListLoggingServers]
message ListLoggingServersResponse {
  // A list of Logging Servers.
  repeated LoggingServer logging_servers = 1;

  // A token, which can be send as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached when making an aggregated query using
  // wildcards.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.GetLoggingServer][google.cloud.vmwareengine.v1.VmwareEngine.GetLoggingServer]
message GetLoggingServerRequest {
  // Required. The resource name of the Logging Server to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/loggingServers/my-logging-server`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/LoggingServer"
    }
  ];
}

// Request message for
// [VmwareEngine.CreateLoggingServer][google.cloud.vmwareengine.v1.VmwareEngine.CreateLoggingServer]
message CreateLoggingServerRequest {
  // Required. The resource name of the private cloud
  // to create a new Logging Server in.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // Required. The initial description of a new logging server.
  LoggingServer logging_server = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided identifier of the `LoggingServer` to be
  // created. This identifier must be unique among `LoggingServer` resources
  // within the parent and becomes the final token in the name URI.
  // The identifier must meet the following requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string logging_server_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.UpdateLoggingServer][google.cloud.vmwareengine.v1.VmwareEngine.UpdateLoggingServer]
message UpdateLoggingServerRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `LoggingServer` resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Logging server description.
  LoggingServer logging_server = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.DeleteLoggingServer][google.cloud.vmwareengine.v1.VmwareEngine.DeleteLoggingServer]
message DeleteLoggingServerRequest {
  // Required. The resource name of the logging server to delete.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/loggingServers/my-logging-server`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/LoggingServer"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if the user has requested cancellation
  // of the operation; false otherwise.
  // Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for
// [VmwareEngine.ListNodeTypes][google.cloud.vmwareengine.v1.VmwareEngine.ListNodeTypes]
message ListNodeTypesRequest {
  // Required. The resource name of the location to be queried for node types.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of node types to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListNodeTypes` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListNodeTypes` must match the call that provided the page token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value
  // must be a string, a number, or a boolean. The comparison operator
  // must be `=`, `!=`, `>`, or `<`.
  //
  // For example, if you are filtering a list of node types, you can
  // exclude the ones named `standard-72` by specifying
  // `name != "standard-72"`.
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "standard-72")
  // (virtual_cpu_count > 2)
  // ```
  //
  // By default, each expression is an `AND` expression. However, you
  // can include `AND` and `OR` expressions explicitly.
  // For example:
  // ```
  // (name = "standard-96") AND
  // (virtual_cpu_count > 2) OR
  // (name = "standard-72")
  // ```
  string filter = 4;
}

// Response message for
// [VmwareEngine.ListNodeTypes][google.cloud.vmwareengine.v1.VmwareEngine.ListNodeTypes]
message ListNodeTypesResponse {
  // A list of Node Types.
  repeated NodeType node_types = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached when making an aggregated query using
  // wildcards.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.GetNodeType][google.cloud.vmwareengine.v1.VmwareEngine.GetNodeType]
message GetNodeTypeRequest {
  // Required. The resource name of the node type to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-proj/locations/us-central1-a/nodeTypes/standard-72`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/NodeType"
    }
  ];
}

// Request message for
// [VmwareEngine.ShowNsxCredentials][google.cloud.vmwareengine.v1.VmwareEngine.ShowNsxCredentials]
message ShowNsxCredentialsRequest {
  // Required. The resource name of the private cloud
  // to be queried for credentials.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string private_cloud = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];
}

// Request message for
// [VmwareEngine.ShowVcenterCredentials][google.cloud.vmwareengine.v1.VmwareEngine.ShowVcenterCredentials]
message ShowVcenterCredentialsRequest {
  // Required. The resource name of the private cloud
  // to be queried for credentials.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string private_cloud = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // Optional. The username of the user to be queried for credentials.
  // The default value of this <NAME_EMAIL>.
  // The provided value must be one of the following:
  // <EMAIL>,
  // <EMAIL>,
  // <EMAIL>,
  // <EMAIL>,
  // <EMAIL>,
  // <EMAIL>,
  // <EMAIL>.
  string username = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.ResetNsxCredentials][google.cloud.vmwareengine.v1.VmwareEngine.ResetNsxCredentials]
message ResetNsxCredentialsRequest {
  // Required. The resource name of the private cloud
  // to reset credentials for.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string private_cloud = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.ResetVcenterCredentials][google.cloud.vmwareengine.v1.VmwareEngine.ResetVcenterCredentials]
message ResetVcenterCredentialsRequest {
  // Required. The resource name of the private cloud
  // to reset credentials for.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string private_cloud = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The username of the user to be to reset the credentials.
  // The default value of this <NAME_EMAIL>.
  // The provided value should be one of the following:
  // <EMAIL>,
  // <EMAIL>,
  // <EMAIL>,
  // <EMAIL>,
  // <EMAIL>,
  // <EMAIL>.
  string username = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [VmwareEngine.ListHcxActivationKeys][google.cloud.vmwareengine.v1.VmwareEngine.ListHcxActivationKeys]
message ListHcxActivationKeysResponse {
  // List of HCX activation keys.
  repeated HcxActivationKey hcx_activation_keys = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached when making an aggregated query using
  // wildcards.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.ListHcxActivationKeys][google.cloud.vmwareengine.v1.VmwareEngine.ListHcxActivationKeys]
message ListHcxActivationKeysRequest {
  // Required. The resource name of the private cloud
  // to be queried for HCX activation keys.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // The maximum number of HCX activation keys to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListHcxActivationKeys` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListHcxActivationKeys` must match the call that provided the page
  // token.
  string page_token = 3;
}

// Request message for [VmwareEngine.GetHcxActivationKeys][]
message GetHcxActivationKeyRequest {
  // Required. The resource name of the HCX activation key to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/privateClouds/my-cloud/hcxActivationKeys/my-key`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/HcxActivationKey"
    }
  ];
}

// Request message for
// [VmwareEngine.CreateHcxActivationKey][google.cloud.vmwareengine.v1.VmwareEngine.CreateHcxActivationKey]
message CreateHcxActivationKeyRequest {
  // Required. The resource name of the private cloud to create the key for.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // Required. The initial description of a new HCX activation key. When
  // creating a new key, this field must be an empty object.
  HcxActivationKey hcx_activation_key = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided identifier of the `HcxActivationKey` to be
  // created. This identifier must be unique among `HcxActivationKey` resources
  // within the parent and becomes the final token in the name URI.
  // The identifier must meet the following requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string hcx_activation_key_id = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4;
}

// Request message for
// [VmwareEngine.GetDnsForwarding][google.cloud.vmwareengine.v1.VmwareEngine.GetDnsForwarding]
message GetDnsForwardingRequest {
  // Required. The resource name of a `DnsForwarding` to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/dnsForwarding`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/DnsForwarding"
    }
  ];
}

// Request message for
// [VmwareEngine.UpdateDnsForwarding][google.cloud.vmwareengine.v1.VmwareEngine.UpdateDnsForwarding]
message UpdateDnsForwardingRequest {
  // Required. DnsForwarding config details.
  DnsForwarding dns_forwarding = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Field mask is used to specify the fields to be overwritten in the
  // `DnsForwarding` resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.CreateNetworkPeering][google.cloud.vmwareengine.v1.VmwareEngine.CreateNetworkPeering]
message CreateNetworkPeeringRequest {
  // Required. The resource name of the location to create the new network
  // peering in. This value is always `global`, because `NetworkPeering` is a
  // global resource. Resource names are schemeless URIs that follow the
  // conventions in https://cloud.google.com/apis/design/resource_names. For
  // example: `projects/my-project/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The user-provided identifier of the new `NetworkPeering`.
  // This identifier must be unique among `NetworkPeering` resources within the
  // parent and becomes the final token in the name URI.
  // The identifier must meet the following requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string network_peering_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The initial description of the new network peering.
  NetworkPeering network_peering = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.DeleteNetworkPeering][google.cloud.vmwareengine.v1.VmwareEngine.DeleteNetworkPeering]
message DeleteNetworkPeeringRequest {
  // Required. The resource name of the network peering to be deleted.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/global/networkPeerings/my-peering`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/NetworkPeering"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.GetNetworkPeering][google.cloud.vmwareengine.v1.VmwareEngine.GetNetworkPeering]
message GetNetworkPeeringRequest {
  // Required. The resource name of the network peering to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/global/networkPeerings/my-peering`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/NetworkPeering"
    }
  ];
}

// Request message for
// [VmwareEngine.ListNetworkPeerings][google.cloud.vmwareengine.v1.VmwareEngine.ListNetworkPeerings]
message ListNetworkPeeringsRequest {
  // Required. The resource name of the location (global) to query for
  // network peerings. Resource names are schemeless URIs that follow the
  // conventions in https://cloud.google.com/apis/design/resource_names. For
  // example: `projects/my-project/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmwareengine.googleapis.com/NetworkPeering"
    }
  ];

  // The maximum number of network peerings to return in one page.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListNetworkPeerings` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListNetworkPeerings` must match the call that provided the page
  // token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value
  // must be a string, a number, or a boolean. The comparison operator
  // must be `=`, `!=`, `>`, or `<`.
  //
  // For example, if you are filtering a list of network peerings, you can
  // exclude the ones named `example-peering` by specifying
  // `name != "example-peering"`.
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "example-peering")
  // (createTime > "2021-04-12T08:15:10.40Z")
  // ```
  //
  // By default, each expression is an `AND` expression. However, you
  // can include `AND` and `OR` expressions explicitly.
  // For example:
  // ```
  // (name = "example-peering-1") AND
  // (createTime > "2021-04-12T08:15:10.40Z") OR
  // (name = "example-peering-2")
  // ```
  string filter = 4;

  // Sorts list results by a certain order. By default, returned results
  // are ordered by `name` in ascending order.
  // You can also sort results in descending order based on the `name` value
  // using `orderBy="name desc"`.
  // Currently, only ordering by `name` is supported.
  string order_by = 5;
}

// Request message for
// [VmwareEngine.UpdateNetworkPeering][google.cloud.vmwareengine.v1.VmwareEngine.UpdateNetworkPeering]
message UpdateNetworkPeeringRequest {
  // Required. Network peering description.
  NetworkPeering network_peering = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Field mask is used to specify the fields to be overwritten in the
  // `NetworkPeering` resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [VmwareEngine.ListNetworkPeerings][google.cloud.vmwareengine.v1.VmwareEngine.ListNetworkPeerings]
message ListNetworkPeeringsResponse {
  // A list of network peerings.
  repeated NetworkPeering network_peerings = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Unreachable resources.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.ListPeeringRoutes][google.cloud.vmwareengine.v1.VmwareEngine.ListPeeringRoutes]
message ListPeeringRoutesRequest {
  // Required. The resource name of the network peering to retrieve peering
  // routes from. Resource names are schemeless URIs that follow the conventions
  // in https://cloud.google.com/apis/design/resource_names. For example:
  // `projects/my-project/locations/global/networkPeerings/my-peering`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/NetworkPeering"
    }
  ];

  // The maximum number of peering routes to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListPeeringRoutes` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListPeeringRoutes` must
  // match the call that provided the page token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // Currently, only filtering on the `direction` field is supported. To return
  // routes imported from the peer network, provide "direction=INCOMING". To
  // return routes exported from the VMware Engine network, provide
  // "direction=OUTGOING". Other filter expressions return an error.
  string filter = 6;
}

// Response message for
// [VmwareEngine.ListPeeringRoutes][google.cloud.vmwareengine.v1.VmwareEngine.ListPeeringRoutes]
message ListPeeringRoutesResponse {
  // A list of peering routes.
  repeated PeeringRoute peering_routes = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [VmwareEngine.ListNetworkPolicies][google.cloud.vmwareengine.v1.VmwareEngine.ListNetworkPolicies]
message ListNetworkPoliciesRequest {
  // Required. The resource name of the location (region) to query for
  // network policies. Resource names are schemeless URIs that follow the
  // conventions in https://cloud.google.com/apis/design/resource_names. For
  // example: `projects/my-project/locations/us-central1`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmwareengine.googleapis.com/NetworkPolicy"
    }
  ];

  // The maximum number of network policies to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListNetworkPolicies` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListNetworkPolicies` must match the call that provided the page
  // token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value
  // must be a string, a number, or a boolean. The comparison operator
  // must be `=`, `!=`, `>`, or `<`.
  //
  // For example, if you are filtering a list of network policies, you can
  // exclude the ones named `example-policy` by specifying
  // `name != "example-policy"`.
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "example-policy")
  // (createTime > "2021-04-12T08:15:10.40Z")
  // ```
  //
  // By default, each expression is an `AND` expression. However, you
  // can include `AND` and `OR` expressions explicitly.
  // For example:
  // ```
  // (name = "example-policy-1") AND
  // (createTime > "2021-04-12T08:15:10.40Z") OR
  // (name = "example-policy-2")
  // ```
  string filter = 4;

  // Sorts list results by a certain order. By default, returned results
  // are ordered by `name` in ascending order.
  // You can also sort results in descending order based on the `name` value
  // using `orderBy="name desc"`.
  // Currently, only ordering by `name` is supported.
  string order_by = 5;
}

// Response message for
// [VmwareEngine.ListNetworkPolicies][google.cloud.vmwareengine.v1.VmwareEngine.ListNetworkPolicies]
message ListNetworkPoliciesResponse {
  // A list of network policies.
  repeated NetworkPolicy network_policies = 1;

  // A token, which can be send as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached when making an aggregated query using
  // wildcards.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.GetNetworkPolicy][google.cloud.vmwareengine.v1.VmwareEngine.GetNetworkPolicy]
message GetNetworkPolicyRequest {
  // Required. The resource name of the network policy to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/networkPolicies/my-network-policy`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/NetworkPolicy"
    }
  ];
}

// Request message for
// [VmwareEngine.UpdateNetworkPolicy][google.cloud.vmwareengine.v1.VmwareEngine.UpdateNetworkPolicy]
message UpdateNetworkPolicyRequest {
  // Required. Network policy description.
  NetworkPolicy network_policy = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Field mask is used to specify the fields to be overwritten in the
  // `NetworkPolicy` resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.CreateNetworkPolicy][google.cloud.vmwareengine.v1.VmwareEngine.CreateNetworkPolicy]
message CreateNetworkPolicyRequest {
  // Required. The resource name of the location (region)
  // to create the new network policy in.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  //  `projects/my-project/locations/us-central1`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmwareengine.googleapis.com/NetworkPolicy"
    }
  ];

  // Required. The user-provided identifier of the network policy to be created.
  // This identifier must be unique within parent
  // `projects/{my-project}/locations/{us-central1}/networkPolicies` and becomes
  // the final token in the name URI.
  // The identifier must meet the following requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string network_policy_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The network policy configuration to use in the request.
  NetworkPolicy network_policy = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.DeleteNetworkPolicy][google.cloud.vmwareengine.v1.VmwareEngine.DeleteNetworkPolicy]
message DeleteNetworkPolicyRequest {
  // Required. The resource name of the network policy to delete.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/networkPolicies/my-network-policy`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/NetworkPolicy"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.ListManagementDnsZoneBindings][google.cloud.vmwareengine.v1.VmwareEngine.ListManagementDnsZoneBindings]
message ListManagementDnsZoneBindingsRequest {
  // Required. The resource name of the private cloud to be queried for
  // management DNS zone bindings.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // The maximum number of management DNS zone bindings to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListManagementDnsZoneBindings`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListManagementDnsZoneBindings` must match the call that provided the page
  // token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value
  // must be a string, a number, or a boolean. The comparison operator
  // must be `=`, `!=`, `>`, or `<`.
  //
  // For example, if you are filtering a list of Management DNS Zone Bindings,
  // you can exclude the ones named `example-management-dns-zone-binding` by
  // specifying `name != "example-management-dns-zone-binding"`.
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "example-management-dns-zone-binding")
  // (createTime > "2021-04-12T08:15:10.40Z")
  // ```
  //
  // By default, each expression is an `AND` expression. However, you
  // can include `AND` and `OR` expressions explicitly.
  // For example:
  // ```
  // (name = "example-management-dns-zone-binding-1") AND
  // (createTime > "2021-04-12T08:15:10.40Z") OR
  // (name = "example-management-dns-zone-binding-2")
  // ```
  string filter = 4;

  // Sorts list results by a certain order. By default, returned results
  // are ordered by `name` in ascending order.
  // You can also sort results in descending order based on the `name` value
  // using `orderBy="name desc"`.
  // Currently, only ordering by `name` is supported.
  string order_by = 5;
}

// Response message for
// [VmwareEngine.ListManagementDnsZoneBindings][google.cloud.vmwareengine.v1.VmwareEngine.ListManagementDnsZoneBindings]
message ListManagementDnsZoneBindingsResponse {
  // A list of management DNS zone bindings.
  repeated ManagementDnsZoneBinding management_dns_zone_bindings = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached when making an aggregated query using
  // wildcards.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.GetManagementDnsZoneBinding][google.cloud.vmwareengine.v1.VmwareEngine.GetManagementDnsZoneBinding]
message GetManagementDnsZoneBindingRequest {
  // Required. The resource name of the management DNS zone binding to
  // retrieve. Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/managementDnsZoneBindings/my-management-dns-zone-binding`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/ManagementDnsZoneBinding"
    }
  ];
}

// Request message for [VmwareEngine.CreateManagementDnsZoneBindings][]
message CreateManagementDnsZoneBindingRequest {
  // Required. The resource name of the private cloud
  // to create a new management DNS zone binding for.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateCloud"
    }
  ];

  // Required. The initial values for a new management DNS zone binding.
  ManagementDnsZoneBinding management_dns_zone_binding = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The user-provided identifier of the `ManagementDnsZoneBinding`
  // resource to be created. This identifier must be unique among
  // `ManagementDnsZoneBinding` resources within the parent and becomes the
  // final token in the name URI. The identifier must meet the following
  // requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string management_dns_zone_binding_id = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if the original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.UpdateManagementDnsZoneBinding][google.cloud.vmwareengine.v1.VmwareEngine.UpdateManagementDnsZoneBinding]
message UpdateManagementDnsZoneBindingRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // `ManagementDnsZoneBinding` resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. New values to update the management DNS zone binding with.
  ManagementDnsZoneBinding management_dns_zone_binding = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if the original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.DeleteManagementDnsZoneBinding][google.cloud.vmwareengine.v1.VmwareEngine.DeleteManagementDnsZoneBinding]
message DeleteManagementDnsZoneBindingRequest {
  // Required. The resource name of the management DNS zone binding to delete.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/managementDnsZoneBindings/my-management-dns-zone-binding`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/ManagementDnsZoneBinding"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for [VmwareEngine.RepairManagementDnsZoneBindings][]
message RepairManagementDnsZoneBindingRequest {
  // Required. The resource name of the management DNS zone binding to repair.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/managementDnsZoneBindings/my-management-dns-zone-binding`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/ManagementDnsZoneBinding"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if the original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.CreateVmwareEngineNetwork][google.cloud.vmwareengine.v1.VmwareEngine.CreateVmwareEngineNetwork]
message CreateVmwareEngineNetworkRequest {
  // Required. The resource name of the location to create the new VMware Engine
  // network in. A VMware Engine network of type
  // `LEGACY` is a regional resource, and a VMware
  // Engine network of type `STANDARD` is a global resource.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names. For example:
  // `projects/my-project/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }
  ];

  // Required. The user-provided identifier of the new VMware Engine network.
  // This identifier must be unique among VMware Engine network resources
  // within the parent and becomes the final token in the name URI. The
  // identifier must meet the following requirements:
  //
  // * For networks of type LEGACY, adheres to the format:
  // `{region-id}-default`. Replace `{region-id}` with the region where you want
  // to create the VMware Engine network. For example, "us-central1-default".
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string vmware_engine_network_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The initial description of the new VMware Engine network.
  VmwareEngineNetwork vmware_engine_network = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.UpdateVmwareEngineNetwork][google.cloud.vmwareengine.v1.VmwareEngine.UpdateVmwareEngineNetwork]
message UpdateVmwareEngineNetworkRequest {
  // Required. VMware Engine network description.
  VmwareEngineNetwork vmware_engine_network = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Field mask is used to specify the fields to be overwritten in the
  // VMware Engine network resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten. Only the
  // following fields can be updated: `description`.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.DeleteVmwareEngineNetwork][google.cloud.vmwareengine.v1.VmwareEngine.DeleteVmwareEngineNetwork]
message DeleteVmwareEngineNetworkRequest {
  // Required. The resource name of the VMware Engine network to be deleted.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/global/vmwareEngineNetworks/my-network`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Checksum used to ensure that the user-provided value is up to
  // date before the server processes the request. The server compares provided
  // checksum with the current checksum of the resource. If the user-provided
  // value is out of date, this request returns an `ABORTED` error.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.GetVmwareEngineNetwork][google.cloud.vmwareengine.v1.VmwareEngine.GetVmwareEngineNetwork]
message GetVmwareEngineNetworkRequest {
  // Required. The resource name of the VMware Engine network to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/global/vmwareEngineNetworks/my-network`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }
  ];
}

// Request message for
// [VmwareEngine.ListVmwareEngineNetworks][google.cloud.vmwareengine.v1.VmwareEngine.ListVmwareEngineNetworks]
message ListVmwareEngineNetworksRequest {
  // Required. The resource name of the location to query for
  // VMware Engine networks. Resource names are schemeless URIs that follow the
  // conventions in https://cloud.google.com/apis/design/resource_names. For
  // example: `projects/my-project/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of results to return in one page.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListVmwareEngineNetworks` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListVmwareEngineNetworks` must match the call that provided the page
  // token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value
  // must be a string, a number, or a boolean. The comparison operator
  // must be `=`, `!=`, `>`, or `<`.
  //
  // For example, if you are filtering a list of network peerings, you can
  // exclude the ones named `example-network` by specifying
  // `name != "example-network"`.
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "example-network")
  // (createTime > "2021-04-12T08:15:10.40Z")
  // ```
  //
  // By default, each expression is an `AND` expression. However, you
  // can include `AND` and `OR` expressions explicitly.
  // For example:
  // ```
  // (name = "example-network-1") AND
  // (createTime > "2021-04-12T08:15:10.40Z") OR
  // (name = "example-network-2")
  // ```
  string filter = 4;

  // Sorts list results by a certain order. By default, returned results
  // are ordered by `name` in ascending order.
  // You can also sort results in descending order based on the `name` value
  // using `orderBy="name desc"`.
  // Currently, only ordering by `name` is supported.
  string order_by = 5;
}

// Response message for
// [VmwareEngine.ListVmwareEngineNetworks][google.cloud.vmwareengine.v1.VmwareEngine.ListVmwareEngineNetworks]
message ListVmwareEngineNetworksResponse {
  // A list of VMware Engine networks.
  repeated VmwareEngineNetwork vmware_engine_networks = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Unreachable resources.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.CreatePrivateConnection][google.cloud.vmwareengine.v1.VmwareEngine.CreatePrivateConnection]
message CreatePrivateConnectionRequest {
  // Required. The resource name of the location to create the new private
  // connection in. Private connection is a regional resource.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names. For example:
  // `projects/my-project/locations/us-central1`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmwareengine.googleapis.com/PrivateConnection"
    }
  ];

  // Required. The user-provided identifier of the new private connection.
  // This identifier must be unique among private connection resources
  // within the parent and becomes the final token in the name URI. The
  // identifier must meet the following requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
  // (section 3.5)
  string private_connection_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The initial description of the new private connection.
  PrivateConnection private_connection = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.GetPrivateConnection][google.cloud.vmwareengine.v1.VmwareEngine.GetPrivateConnection]
message GetPrivateConnectionRequest {
  // Required. The resource name of the private connection to retrieve.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/privateConnections/my-connection`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateConnection"
    }
  ];
}

// Request message for
// [VmwareEngine.ListPrivateConnections][google.cloud.vmwareengine.v1.VmwareEngine.ListPrivateConnections]
message ListPrivateConnectionsRequest {
  // Required. The resource name of the location to query for
  // private connections. Resource names are schemeless URIs that follow the
  // conventions in https://cloud.google.com/apis/design/resource_names. For
  // example: `projects/my-project/locations/us-central1`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmwareengine.googleapis.com/PrivateConnection"
    }
  ];

  // The maximum number of private connections to return in one page.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListPrivateConnections` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListPrivateConnections` must match the call that provided the page
  // token.
  string page_token = 3;

  // A filter expression that matches resources returned in the response.
  // The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value
  // must be a string, a number, or a boolean. The comparison operator
  // must be `=`, `!=`, `>`, or `<`.
  //
  // For example, if you are filtering a list of private connections, you can
  // exclude the ones named `example-connection` by specifying
  // `name != "example-connection"`.
  //
  // To filter on multiple expressions, provide each separate expression within
  // parentheses. For example:
  // ```
  // (name = "example-connection")
  // (createTime > "2022-09-22T08:15:10.40Z")
  // ```
  //
  // By default, each expression is an `AND` expression. However, you
  // can include `AND` and `OR` expressions explicitly.
  // For example:
  // ```
  // (name = "example-connection-1") AND
  // (createTime > "2021-04-12T08:15:10.40Z") OR
  // (name = "example-connection-2")
  // ```
  string filter = 4;

  // Sorts list results by a certain order. By default, returned results
  // are ordered by `name` in ascending order.
  // You can also sort results in descending order based on the `name` value
  // using `orderBy="name desc"`.
  // Currently, only ordering by `name` is supported.
  string order_by = 5;
}

// Response message for
// [VmwareEngine.ListPrivateConnections][google.cloud.vmwareengine.v1.VmwareEngine.ListPrivateConnections]
message ListPrivateConnectionsResponse {
  // A list of private connections.
  repeated PrivateConnection private_connections = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Unreachable resources.
  repeated string unreachable = 3;
}

// Request message for
// [VmwareEngine.UpdatePrivateConnection][google.cloud.vmwareengine.v1.VmwareEngine.UpdatePrivateConnection]
message UpdatePrivateConnectionRequest {
  // Required. Private connection description.
  PrivateConnection private_connection = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Field mask is used to specify the fields to be overwritten in the
  // `PrivateConnection` resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.DeletePrivateConnection][google.cloud.vmwareengine.v1.VmwareEngine.DeletePrivateConnection]
message DeletePrivateConnectionRequest {
  // Required. The resource name of the private connection to be deleted.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/privateConnections/my-connection`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateConnection"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.ListPrivateConnectionPeeringRoutes][google.cloud.vmwareengine.v1.VmwareEngine.ListPrivateConnectionPeeringRoutes]
message ListPrivateConnectionPeeringRoutesRequest {
  // Required. The resource name of the private connection to retrieve peering
  // routes from. Resource names are schemeless URIs that follow the conventions
  // in https://cloud.google.com/apis/design/resource_names. For example:
  // `projects/my-project/locations/us-west1/privateConnections/my-connection`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/PrivateConnection"
    }
  ];

  // The maximum number of peering routes to return in one page.
  // The service may return fewer than this value.
  // The maximum value is coerced to 1000.
  // The default value of this field is 500.
  int32 page_size = 2;

  // A page token, received from a previous `ListPrivateConnectionPeeringRoutes`
  // call. Provide this to retrieve the subsequent page. When paginating, all
  // other parameters provided to `ListPrivateConnectionPeeringRoutes` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for
// [VmwareEngine.ListPrivateConnectionPeeringRoutes][google.cloud.vmwareengine.v1.VmwareEngine.ListPrivateConnectionPeeringRoutes]
message ListPrivateConnectionPeeringRoutesResponse {
  // A list of peering routes.
  repeated PeeringRoute peering_routes = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [VmwareEngine.GrantDnsBindPermission][google.cloud.vmwareengine.v1.VmwareEngine.GrantDnsBindPermission]
message GrantDnsBindPermissionRequest {
  // Required. The name of the resource which stores the users/service accounts
  // having the permission to bind to the corresponding intranet VPC of the
  // consumer project. DnsBindPermission is a global resource. Resource names
  // are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names. For example:
  // `projects/my-project/locations/global/dnsBindPermission`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/DnsBindPermission"
    }
  ];

  // Required. The consumer provided user/service account which needs to be
  // granted permission to bind with the intranet VPC corresponding to the
  // consumer project.
  Principal principal = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.RevokeDnsBindPermission][google.cloud.vmwareengine.v1.VmwareEngine.RevokeDnsBindPermission]
message RevokeDnsBindPermissionRequest {
  // Required. The name of the resource which stores the users/service accounts
  // having the permission to bind to the corresponding intranet VPC of the
  // consumer project. DnsBindPermission is a global resource. Resource names
  // are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names. For example:
  // `projects/my-project/locations/global/dnsBindPermission`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/DnsBindPermission"
    }
  ];

  // Required. The consumer provided user/service account which needs to be
  // granted permission to bind with the intranet VPC corresponding to the
  // consumer project.
  Principal principal = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server guarantees that a
  // request doesn't result in creation of duplicate commitments for at least 60
  // minutes.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [VmwareEngine.GetDnsBindPermission][google.cloud.vmwareengine.v1.VmwareEngine.GetDnsBindPermission]
message GetDnsBindPermissionRequest {
  // Required. The name of the resource which stores the users/service accounts
  // having the permission to bind to the corresponding intranet VPC of the
  // consumer project. DnsBindPermission is a global resource. Resource names
  // are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names. For example:
  // `projects/my-project/locations/global/dnsBindPermission`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/DnsBindPermission"
    }
  ];
}
