// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.vmwareengine.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.VmwareEngine.V1";
option go_package = "cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb;vmwareenginepb";
option java_multiple_files = true;
option java_outer_classname = "VmwareengineResourcesProto";
option java_package = "com.google.cloud.vmwareengine.v1";
option php_namespace = "Google\\Cloud\\VmwareEngine\\V1";
option ruby_package = "Google::Cloud::VmwareEngine::V1";

// Network configuration in the consumer project
// with which the peering has to be done.
message NetworkConfig {
  // Required. Management CIDR used by VMware management appliances.
  string management_cidr = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. The relative resource name of the VMware Engine network attached
  // to the private cloud. Specify the name in the following form:
  // `projects/{project}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}`
  // where `{project}` can either be a project number or a project ID.
  string vmware_engine_network = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }
  ];

  // Output only. The canonical name of the VMware Engine network in the form:
  // `projects/{project_number}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}`
  string vmware_engine_network_canonical = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }
  ];

  // Output only. The IP address layout version of the management IP address
  // range. Possible versions include:
  // * `managementIpAddressLayoutVersion=1`: Indicates the legacy IP address
  // layout used by some existing private clouds. This is no longer supported
  // for new private clouds as it does not support all features.
  // * `managementIpAddressLayoutVersion=2`: Indicates the latest IP address
  // layout used by all newly created private clouds. This version supports all
  // current features.
  int32 management_ip_address_layout_version = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. DNS Server IP of the Private Cloud.
  // All DNS queries can be forwarded to this address for name resolution of
  // Private Cloud's management entities like vCenter, NSX-T Manager and
  // ESXi hosts.
  string dns_server_ip = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Information about the type and number of nodes associated with the cluster.
message NodeTypeConfig {
  // Required. The number of nodes of this type in the cluster
  int32 node_count = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Customized number of cores available to each node of the type.
  // This number must always be one of `nodeType.availableCustomCoreCounts`.
  // If zero is provided max value from `nodeType.availableCustomCoreCounts`
  // will be used.
  int32 custom_core_count = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Configuration of a stretched cluster.
message StretchedClusterConfig {
  // Required. Zone that will remain operational when connection between the two
  // zones is lost. Specify the resource name of a zone that belongs to the
  // region of the private cloud. For example:
  // `projects/{project}/locations/europe-west3-a` where `{project}` can either
  // be a project number or a project ID.
  string preferred_location = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Additional zone for a higher level of availability and load
  // balancing. Specify the resource name of a zone that belongs to the region
  // of the private cloud. For example:
  // `projects/{project}/locations/europe-west3-b` where `{project}` can either
  // be a project number or a project ID.
  string secondary_location = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// Represents a private cloud resource. Private clouds of type `STANDARD` and
// `TIME_LIMITED` are zonal resources, `STRETCHED` private clouds are
// regional.
message PrivateCloud {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/PrivateCloud"
    pattern: "projects/{project}/locations/{location}/privateClouds/{private_cloud}"
  };

  // Enum State defines possible states of private clouds.
  enum State {
    // The default value. This value should never be used.
    STATE_UNSPECIFIED = 0;

    // The private cloud is ready.
    ACTIVE = 1;

    // The private cloud is being created.
    CREATING = 2;

    // The private cloud is being updated.
    UPDATING = 3;

    // The private cloud is in failed state.
    FAILED = 5;

    // The private cloud is scheduled for deletion. The deletion process can be
    // cancelled by using the corresponding undelete method.
    DELETED = 6;

    // The private cloud is irreversibly deleted and is being removed from the
    // system.
    PURGING = 7;
  }

  // Management cluster configuration.
  message ManagementCluster {
    // Required. The user-provided identifier of the new `Cluster`.
    // The identifier must meet the following requirements:
    //
    // * Only contains 1-63 alphanumeric characters and hyphens
    // * Begins with an alphabetical character
    // * Ends with a non-hyphen character
    // * Not formatted as a UUID
    // * Complies with [RFC
    // 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
    string cluster_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The map of cluster node types in this cluster, where the key is
    // canonical identifier of the node type (corresponds to the `NodeType`).
    map<string, NodeTypeConfig> node_type_configs = 7
        [(google.api.field_behavior) = REQUIRED];

    // Optional. Configuration of a stretched cluster. Required for STRETCHED
    // private clouds.
    StretchedClusterConfig stretched_cluster_config = 8
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Enum Type defines private cloud type.
  enum Type {
    // Standard private is a zonal resource, with 3+ nodes. Default type.
    STANDARD = 0;

    // Time limited private cloud is a zonal resource, can have only 1 node and
    // has limited life span. Will be deleted after defined period of time,
    // can be converted into standard private cloud by expanding it up to 3
    // or more nodes.
    TIME_LIMITED = 1;

    // Stretched private cloud is a regional resource with redundancy,
    // with a minimum of 6 nodes, nodes count has to be even.
    STRETCHED = 2;
  }

  // Output only. The resource name of this private cloud.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the resource was scheduled for deletion.
  google.protobuf.Timestamp delete_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the resource will be irreversibly deleted.
  google.protobuf.Timestamp expire_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the resource. New values may be added to this enum
  // when appropriate.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Network configuration of the private cloud.
  NetworkConfig network_config = 9 [(google.api.field_behavior) = REQUIRED];

  // Required. Input only. The management cluster for this private cloud.
  // This field is required during creation of the private cloud to provide
  // details for the default cluster.
  //
  // The following fields can't be changed after private cloud creation:
  // `ManagementCluster.clusterId`, `ManagementCluster.nodeTypeId`.
  ManagementCluster management_cluster = 10 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = REQUIRED
  ];

  // User-provided description for this private cloud.
  string description = 11;

  // Output only. HCX appliance.
  Hcx hcx = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. NSX appliance.
  Nsx nsx = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Vcenter appliance.
  Vcenter vcenter = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. System-generated unique identifier for the resource.
  string uid = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Type of the private cloud. Defaults to STANDARD.
  Type type = 22 [(google.api.field_behavior) = OPTIONAL];
}

// A cluster in a private cloud.
message Cluster {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/Cluster"
    pattern: "projects/{project}/locations/{location}/privateClouds/{private_cloud}/clusters/{cluster}"
  };

  // Enum State defines possible states of private cloud clusters.
  enum State {
    // The default value. This value should never be used.
    STATE_UNSPECIFIED = 0;

    // The Cluster is operational and can be used by the user.
    ACTIVE = 1;

    // The Cluster is being deployed.
    CREATING = 2;

    // Adding or removing of a node to the cluster, any other cluster specific
    // updates.
    UPDATING = 3;

    // The Cluster is being deleted.
    DELETING = 4;

    // The Cluster is undergoing maintenance, for example: a failed node is
    // getting replaced.
    REPAIRING = 5;
  }

  // Output only. The resource name of this cluster.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the resource.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if the cluster is a management cluster; false otherwise.
  // There can only be one management cluster in a private cloud
  // and it has to be the first one.
  bool management = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Configuration of the autoscaling applied to this cluster.
  AutoscalingSettings autoscaling_settings = 18
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. System-generated unique identifier for the resource.
  string uid = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The map of cluster node types in this cluster, where the key is
  // canonical identifier of the node type (corresponds to the `NodeType`).
  map<string, NodeTypeConfig> node_type_configs = 16
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Configuration of a stretched cluster. Required for clusters that
  // belong to a STRETCHED private cloud.
  StretchedClusterConfig stretched_cluster_config = 17
      [(google.api.field_behavior) = OPTIONAL];
}

// Node in a cluster.
message Node {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/Node"
    pattern: "projects/{project}/locations/{location}/privateClouds/{private_cloud}/clusters/{cluster}/nodes/{node}"
  };

  // Enum State defines possible states of a node in a cluster.
  enum State {
    // The default value. This value should never be used.
    STATE_UNSPECIFIED = 0;

    // Node is operational and can be used by the user.
    ACTIVE = 1;

    // Node is being provisioned.
    CREATING = 2;

    // Node is in a failed state.
    FAILED = 3;

    // Node is undergoing maintenance, e.g.: during private cloud upgrade.
    UPGRADING = 4;
  }

  // Output only. The resource name of this node.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // projects/my-project/locations/us-central1-a/privateClouds/my-cloud/clusters/my-cluster/nodes/my-node
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Fully qualified domain name of the node.
  string fqdn = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Internal IP address of the node.
  string internal_ip = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The canonical identifier of the node type (corresponds to the
  // `NodeType`).
  // For example: standard-72.
  string node_type_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The version number of the VMware ESXi
  // management component in this cluster.
  string version = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Customized number of cores
  int64 custom_core_count = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the appliance.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents an allocated external IP address and its corresponding internal IP
// address in a private cloud.
message ExternalAddress {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/ExternalAddress"
    pattern: "projects/{project}/locations/{location}/privateClouds/{private_cloud}/externalAddresses/{external_address}"
  };

  // Enum State defines possible states of external addresses.
  enum State {
    // The default value. This value should never be used.
    STATE_UNSPECIFIED = 0;

    // The address is ready.
    ACTIVE = 1;

    // The address is being created.
    CREATING = 2;

    // The address is being updated.
    UPDATING = 3;

    // The address is being deleted.
    DELETING = 4;
  }

  // Output only. The resource name of this external IP address.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/externalAddresses/my-address`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The internal IP address of a workload VM.
  string internal_ip = 6;

  // Output only. The external IP address of a workload VM.
  string external_ip = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the resource.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. System-generated unique identifier for the resource.
  string uid = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-provided description for this resource.
  string description = 11;
}

// Subnet in a private cloud. Either `management` subnets (such as vMotion) that
// are read-only, or `userDefined`, which can also be updated.
message Subnet {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/Subnet"
    pattern: "projects/{project}/locations/{location}/privateClouds/{private_cloud}/subnets/{subnet}"
  };

  // Defines possible states of subnets.
  enum State {
    // The default value. This value should never be used.
    STATE_UNSPECIFIED = 0;

    // The subnet is ready.
    ACTIVE = 1;

    // The subnet is being created.
    CREATING = 2;

    // The subnet is being updated.
    UPDATING = 3;

    // The subnet is being deleted.
    DELETING = 4;

    // Changes requested in the last operation are being propagated.
    RECONCILING = 5;

    // Last operation on the subnet did not succeed. Subnet's payload is
    // reverted back to its most recent working state.
    FAILED = 6;
  }

  // Output only. The resource name of this subnet.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/subnets/my-subnet`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The IP address range of the subnet in CIDR format '10.0.0.0/24'.
  string ip_cidr_range = 7;

  // The IP address of the gateway of this subnet.
  // Must fall within the IP prefix defined above.
  string gateway_ip = 8;

  // Output only. The type of the subnet. For example "management" or
  // "userDefined".
  string type = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the resource.
  State state = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. VLAN ID of the VLAN on which the subnet is configured
  int32 vlan_id = 16 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// External access firewall rules for filtering incoming traffic destined to
// `ExternalAddress` resources.
message ExternalAccessRule {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/ExternalAccessRule"
    pattern: "projects/{project}/locations/{location}/networkPolicies/{network_policy}/externalAccessRules/{external_access_rule}"
  };

  // Action determines whether the external access rule permits or blocks
  // traffic, subject to the other components of the rule matching the traffic.
  enum Action {
    // Defaults to allow.
    ACTION_UNSPECIFIED = 0;

    // Allows connections that match the other specified components.
    ALLOW = 1;

    // Blocks connections that match the other specified components.
    DENY = 2;
  }

  // An IP range provided in any one of the supported formats.
  message IpRange {
    oneof ip_range {
      // A single IP address. For example: `********`.
      string ip_address = 1;

      // An IP address range in the CIDR format. For example: `10.0.0.0/24`.
      string ip_address_range = 2;

      // The name of an `ExternalAddress` resource. The external address must
      // have been reserved in the scope of this external access rule's parent
      // network policy.  Provide the external address name in the form of
      // `projects/{project}/locations/{location}/privateClouds/{private_cloud}/externalAddresses/{external_address}`.
      // For example:
      // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/externalAddresses/my-address`.
      string external_address = 3 [(google.api.resource_reference) = {
        type: "vmwareengine.googleapis.com/ExternalAddress"
      }];
    }
  }

  // Defines possible states of external access firewall rules.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // The rule is ready.
    ACTIVE = 1;

    // The rule is being created.
    CREATING = 2;

    // The rule is being updated.
    UPDATING = 3;

    // The rule is being deleted.
    DELETING = 4;
  }

  // Output only. The resource name of this external access rule.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/networkPolicies/my-policy/externalAccessRules/my-rule`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-provided description for this external access rule.
  string description = 5;

  // External access rule priority, which determines the external access rule to
  // use when multiple rules apply. If multiple rules have the same priority,
  // their ordering is non-deterministic. If specific ordering is required,
  // assign unique priorities to enforce such ordering. The external access rule
  // priority is an integer from 100 to 4096, both inclusive. Lower integers
  // indicate higher precedence. For example, a rule with priority `100` has
  // higher precedence than a rule with priority `101`.
  int32 priority = 6;

  // The action that the external access rule performs.
  Action action = 7;

  // The IP protocol to which the external access rule applies. This value can
  // be one of the following three protocol strings (not case-sensitive):
  // `tcp`, `udp`, or `icmp`.
  string ip_protocol = 8;

  // If source ranges are specified, the external access rule applies only to
  // traffic that has a source IP address in these ranges. These ranges can
  // either be expressed in the CIDR format or as an IP address. As only inbound
  // rules are supported, `ExternalAddress` resources cannot be the source IP
  // addresses of an external access rule. To match all source addresses,
  // specify `0.0.0.0/0`.
  repeated IpRange source_ip_ranges = 9;

  // A list of source ports to which the external access rule applies. This
  // field is only applicable for the UDP or TCP protocol.
  // Each entry must be either an integer or a range. For example: `["22"]`,
  // `["80","443"]`, or `["12345-12349"]`. To match all source ports, specify
  // `["0-65535"]`.
  repeated string source_ports = 10;

  // If destination ranges are specified, the external access rule applies only
  // to the traffic that has a destination IP address in these ranges. The
  // specified IP addresses must have reserved external IP addresses in the
  // scope of the parent network policy. To match all external IP addresses in
  // the scope of the parent network policy, specify `0.0.0.0/0`. To match a
  // specific external IP address, specify it using the
  // `IpRange.external_address` property.
  repeated IpRange destination_ip_ranges = 11;

  // A list of destination ports to which the external access rule applies. This
  // field is only applicable for the UDP or TCP protocol.
  // Each entry must be either an integer or a range. For example: `["22"]`,
  // `["80","443"]`, or `["12345-12349"]`. To match all destination ports,
  // specify `["0-65535"]`.
  repeated string destination_ports = 12;

  // Output only. The state of the resource.
  State state = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. System-generated unique identifier for the resource.
  string uid = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Logging server to receive vCenter or ESXi logs.
message LoggingServer {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/LoggingServer"
    pattern: "projects/{project}/locations/{location}/privateClouds/{private_cloud}/loggingServers/{logging_server}"
  };

  // Defines possible protocols used to send logs to
  // a logging server.
  enum Protocol {
    // Unspecified communications protocol. This is the default value.
    PROTOCOL_UNSPECIFIED = 0;

    // UDP
    UDP = 1;

    // TCP
    TCP = 2;

    // TLS
    TLS = 3;

    // SSL
    SSL = 4;

    // RELP
    RELP = 5;
  }

  // Defines possible types of component that produces logs.
  enum SourceType {
    // The default value. This value should never be used.
    SOURCE_TYPE_UNSPECIFIED = 0;

    // Logs produced by ESXI hosts
    ESXI = 1;

    // Logs produced by vCenter server
    VCSA = 2;
  }

  // Output only. The resource name of this logging server.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/loggingServers/my-logging-server`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Fully-qualified domain name (FQDN) or IP Address of the logging
  // server.
  string hostname = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. Port number at which the logging server receives logs.
  int32 port = 7 [(google.api.field_behavior) = REQUIRED];

  // Required. Protocol used by vCenter to send logs to a logging server.
  Protocol protocol = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of component that produces logs that will be forwarded
  // to this logging server.
  SourceType source_type = 10 [(google.api.field_behavior) = REQUIRED];

  // Output only. System-generated unique identifier for the resource.
  string uid = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Describes node type.
message NodeType {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/NodeType"
    pattern: "projects/{project}/locations/{location}/nodeTypes/{node_type}"
  };

  // Enum Kind defines possible types of a NodeType.
  enum Kind {
    // The default value. This value should never be used.
    KIND_UNSPECIFIED = 0;

    // Standard HCI node.
    STANDARD = 1;

    // Storage only Node.
    STORAGE_ONLY = 2;
  }

  // Capability of a node type.
  enum Capability {
    // The default value. This value is used if the capability is omitted or
    // unknown.
    CAPABILITY_UNSPECIFIED = 0;

    // This node type supports stretch clusters.
    STRETCHED_CLUSTERS = 1;
  }

  // Output only. The resource name of this node type.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-proj/locations/us-central1-a/nodeTypes/standard-72`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The canonical identifier of the node type
  // (corresponds to the `NodeType`). For example: standard-72.
  string node_type_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The friendly name for this node type.
  // For example: ve1-standard-72
  string display_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total number of virtual CPUs in a single node.
  int32 virtual_cpu_count = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total number of CPU cores in a single node.
  int32 total_core_count = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The amount of physical memory available, defined in GB.
  int32 memory_gb = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The amount of storage available, defined in GB.
  int32 disk_size_gb = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of possible values of custom core count.
  repeated int32 available_custom_core_counts = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the resource.
  Kind kind = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Families of the node type.
  // For node types to be in the same cluster
  // they must share at least one element in the `families`.
  repeated string families = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Capabilities of this node type.
  repeated Capability capabilities = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Credentials for a private cloud.
message Credentials {
  // Initial username.
  string username = 1;

  // Initial password.
  string password = 2;
}

// HCX activation key. A default key is created during
// private cloud provisioning, but this behavior is subject to change
// and you should always verify active keys.
// Use
// [VmwareEngine.ListHcxActivationKeys][google.cloud.vmwareengine.v1.VmwareEngine.ListHcxActivationKeys]
// to retrieve existing keys and
// [VmwareEngine.CreateHcxActivationKey][google.cloud.vmwareengine.v1.VmwareEngine.CreateHcxActivationKey]
// to create new ones.
message HcxActivationKey {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/HcxActivationKey"
    pattern: "projects/{project}/locations/{location}/privateClouds/{private_cloud}/hcxActivationKeys/{hcx_activation_key}"
  };

  // State of HCX activation key
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // State of a newly generated activation key.
    AVAILABLE = 1;

    // State of key when it has been used to activate HCX appliance.
    CONSUMED = 2;

    // State of key when it is being created.
    CREATING = 3;
  }

  // Output only. The resource name of this HcxActivationKey.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/privateClouds/my-cloud/hcxActivationKeys/my-key`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of HCX activation key.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of HCX activation key.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. HCX activation key.
  string activation_key = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. System-generated unique identifier for the resource.
  string uid = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Details about a HCX Cloud Manager appliance.
message Hcx {
  // State of the appliance
  enum State {
    // Unspecified appliance state. This is the default value.
    STATE_UNSPECIFIED = 0;

    // The appliance is operational and can be used.
    ACTIVE = 1;

    // The appliance is being deployed.
    CREATING = 2;

    // The appliance is being activated.
    ACTIVATING = 3;
  }

  // Internal IP address of the appliance.
  string internal_ip = 2;

  // Version of the appliance.
  string version = 4;

  // Output only. The state of the appliance.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Fully qualified domain name of the appliance.
  string fqdn = 6;
}

// Details about a NSX Manager appliance.
message Nsx {
  // State of the appliance
  enum State {
    // Unspecified appliance state. This is the default value.
    STATE_UNSPECIFIED = 0;

    // The appliance is operational and can be used.
    ACTIVE = 1;

    // The appliance is being deployed.
    CREATING = 2;
  }

  // Internal IP address of the appliance.
  string internal_ip = 2;

  // Version of the appliance.
  string version = 4;

  // Output only. The state of the appliance.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Fully qualified domain name of the appliance.
  string fqdn = 6;
}

// Details about a vCenter Server management appliance.
message Vcenter {
  // State of the appliance
  enum State {
    // Unspecified appliance state. This is the default value.
    STATE_UNSPECIFIED = 0;

    // The appliance is operational and can be used.
    ACTIVE = 1;

    // The appliance is being deployed.
    CREATING = 2;
  }

  // Internal IP address of the appliance.
  string internal_ip = 2;

  // Version of the appliance.
  string version = 4;

  // Output only. The state of the appliance.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Fully qualified domain name of the appliance.
  string fqdn = 6;
}

// Autoscaling settings define the rules used by VMware Engine to
// automatically scale-out and scale-in the clusters in a private cloud.
message AutoscalingSettings {
  // Thresholds define the utilization of resources triggering
  // scale-out and scale-in operations.
  message Thresholds {
    // Required. The utilization triggering the scale-out operation in percent.
    int32 scale_out = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The utilization triggering the scale-in operation in percent.
    int32 scale_in = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Autoscaling policy describes the behavior of the autoscaling
  // with respect to the resource utilization.
  // The scale-out operation is initiated if the utilization
  // exceeds ANY of the respective thresholds.
  // The scale-in operation is initiated if the utilization
  // is below ALL of the respective thresholds.
  message AutoscalingPolicy {
    // Required. The canonical identifier of the node type to add or remove.
    // Corresponds to the `NodeType`.
    string node_type_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Number of nodes to add to a cluster during a scale-out
    // operation. Must be divisible by 2 for stretched clusters. During a
    // scale-in operation only one node (or 2 for stretched clusters) are
    // removed in a single iteration.
    int32 scale_out_size = 2 [(google.api.field_behavior) = REQUIRED];

    // Optional. Utilization thresholds pertaining to CPU utilization.
    Thresholds cpu_thresholds = 11 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Utilization thresholds pertaining to amount of granted memory.
    Thresholds granted_memory_thresholds = 12
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Utilization thresholds pertaining to amount of consumed memory.
    Thresholds consumed_memory_thresholds = 13
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Utilization thresholds pertaining to amount of consumed
    // storage.
    Thresholds storage_thresholds = 14 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The map with autoscaling policies applied to the cluster.
  // The key is the identifier of the policy.
  // It must meet the following requirements:
  //
  // * Only contains 1-63 alphanumeric characters and hyphens
  // * Begins with an alphabetical character
  // * Ends with a non-hyphen character
  // * Not formatted as a UUID
  // * Complies with [RFC
  // 1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
  //
  // Currently there map must contain only one element
  // that describes the autoscaling policy for compute nodes.
  map<string, AutoscalingPolicy> autoscaling_policies = 1
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Minimum number of nodes of any type in a cluster.
  // If not specified the default limits apply.
  int32 min_cluster_node_count = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum number of nodes of any type in a cluster.
  // If not specified the default limits apply.
  int32 max_cluster_node_count = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The minimum duration between consecutive autoscale operations.
  // It starts once addition or removal of nodes is fully completed.
  // Defaults to 30 minutes if not specified. Cool down period must be in whole
  // minutes (for example, 30, 31, 50, 180 minutes).
  google.protobuf.Duration cool_down_period = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// DNS forwarding config.
// This config defines a list of domain to name server mappings,
// and is attached to the private cloud for custom domain resolution.
message DnsForwarding {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/DnsForwarding"
    pattern: "projects/{project}/locations/{location}/privateClouds/{private_cloud}/dnsForwarding"
  };

  // A forwarding rule is a mapping of a `domain` to `name_servers`.
  // This mapping allows VMware Engine to resolve domains for attached private
  // clouds by forwarding DNS requests for a given domain to the specified
  // nameservers.
  message ForwardingRule {
    // Required. Domain used to resolve a `name_servers` list.
    string domain = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. List of DNS servers to use for domain resolution
    repeated string name_servers = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Output only. The resource name of this DNS profile.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/dnsForwarding`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. List of domain mappings to configure
  repeated ForwardingRule forwarding_rules = 4
      [(google.api.field_behavior) = REQUIRED];
}

// Details of a network peering.
message NetworkPeering {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/NetworkPeering"
    pattern: "projects/{project}/locations/{location}/networkPeerings/{network_peering}"
  };

  // Possible states of a network peering.
  enum State {
    // Unspecified network peering state. This is the default value.
    STATE_UNSPECIFIED = 0;

    // The peering is not active.
    INACTIVE = 1;

    // The peering is active.
    ACTIVE = 2;

    // The peering is being created.
    CREATING = 3;

    // The peering is being deleted.
    DELETING = 4;
  }

  // Type or purpose of the network peering connection.
  enum PeerNetworkType {
    // Unspecified
    PEER_NETWORK_TYPE_UNSPECIFIED = 0;

    // Peering connection used for connecting to another VPC network established
    // by the same user. For example, a peering connection to another VPC
    // network in the same project or to an on-premises network.
    STANDARD = 1;

    // Peering connection used for connecting to another VMware Engine network.
    VMWARE_ENGINE_NETWORK = 2;

    // Peering connection used for establishing [private services
    // access](https://cloud.google.com/vpc/docs/private-services-access).
    PRIVATE_SERVICES_ACCESS = 3;

    // Peering connection used for connecting to NetApp Cloud Volumes.
    NETAPP_CLOUD_VOLUMES = 4;

    // Peering connection used for connecting to third-party services. Most
    // third-party services require manual setup of reverse peering on the VPC
    // network associated with the third-party service.
    THIRD_PARTY_SERVICE = 5;

    // Peering connection used for connecting to Dell PowerScale Filers
    DELL_POWERSCALE = 6;

    // Peering connection used for connecting to Google Cloud NetApp Volumes.
    GOOGLE_CLOUD_NETAPP_VOLUMES = 7;
  }

  // Output only. The resource name of the network peering. NetworkPeering is a
  // global resource and location can only be global. Resource names are
  // scheme-less URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/global/networkPeerings/my-peering`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The relative resource name of the network to peer with
  // a standard VMware Engine network. The provided network can be a
  // consumer VPC network or another standard VMware Engine network. If the
  // `peer_network_type` is VMWARE_ENGINE_NETWORK, specify the name in the form:
  // `projects/{project}/locations/global/vmwareEngineNetworks/{vmware_engine_network_id}`.
  // Otherwise specify the name in the form:
  // `projects/{project}/global/networks/{network_id}`, where
  // `{project}` can either be a project number or a project ID.
  string peer_network = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. True if custom routes are exported to the peered network;
  // false otherwise. The default value is true.
  optional bool export_custom_routes = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. True if custom routes are imported from the peered network;
  // false otherwise. The default value is true.
  optional bool import_custom_routes = 9
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. True if full mesh connectivity is created and managed
  // automatically between peered networks; false otherwise. Currently this
  // field is always true because Google Compute Engine automatically creates
  // and manages subnetwork routes between two VPC networks when peering state
  // is 'ACTIVE'.
  optional bool exchange_subnet_routes = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. True if all subnet routes with a public IP address range are
  // exported; false otherwise. The default value is true. IPv4 special-use
  // ranges (https://en.wikipedia.org/wiki/IPv4#Special_addresses) are always
  // exported to peers and are not controlled by this field.
  optional bool export_custom_routes_with_public_ip = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. True if all subnet routes with public IP address range are
  // imported; false otherwise. The default value is true. IPv4 special-use
  // ranges (https://en.wikipedia.org/wiki/IPv4#Special_addresses) are always
  // imported to peers and are not controlled by this field.
  optional bool import_custom_routes_with_public_ip = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. State of the network peering. This field
  // has a value of 'ACTIVE' when there's a matching configuration in the peer
  // network. New values may be added to this enum when appropriate.
  State state = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Output Only. Details about the current state of the network
  // peering.
  string state_details = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Maximum transmission unit (MTU) in bytes.
  // The default value is `1500`. If a value of `0` is provided for this field,
  // VMware Engine uses the default value instead.
  int32 peer_mtu = 14 [(google.api.field_behavior) = OPTIONAL];

  // Required. The type of the network to peer with the VMware Engine network.
  PeerNetworkType peer_network_type = 16
      [(google.api.field_behavior) = REQUIRED];

  // Output only. System-generated unique identifier for the resource.
  string uid = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The relative resource name of the VMware Engine network.
  // Specify the name in the following form:
  // `projects/{project}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}`
  // where `{project}` can either be a project number or a project ID.
  string vmware_engine_network = 20 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }
  ];

  // Optional. User-provided description for this network peering.
  string description = 21 [(google.api.field_behavior) = OPTIONAL];
}

// Exchanged network peering route.
message PeeringRoute {
  // The type of the peering route.
  enum Type {
    // Unspecified peering route type. This is the default value.
    TYPE_UNSPECIFIED = 0;

    // Dynamic routes in the peer network.
    DYNAMIC_PEERING_ROUTE = 1;

    // Static routes in the peer network.
    STATIC_PEERING_ROUTE = 2;

    // Created, updated, and removed automatically by Google Cloud when subnets
    // are created, modified, or deleted in the peer network.
    SUBNET_PEERING_ROUTE = 3;
  }

  // The direction of the exchanged routes.
  enum Direction {
    // Unspecified exchanged routes direction. This is default.
    DIRECTION_UNSPECIFIED = 0;

    // Routes imported from the peer network.
    INCOMING = 1;

    // Routes exported to the peer network.
    OUTGOING = 2;
  }

  // Output only. Destination range of the peering route in CIDR notation.
  string dest_range = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the route in the peer VPC network.
  Type type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Region containing the next hop of the peering route. This
  // field only applies to dynamic routes in the peer VPC network.
  string next_hop_region = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The priority of the peering route.
  int64 priority = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. True if the peering route has been imported from a peered
  // VPC network; false otherwise. The import happens if the field
  // `NetworkPeering.importCustomRoutes` is true for this network,
  // `NetworkPeering.exportCustomRoutes` is true for the peer VPC network, and
  // the import does not result in a route conflict.
  bool imported = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Direction of the routes exchanged with the peer network, from
  // the VMware Engine network perspective:
  //
  // * Routes of direction `INCOMING` are imported from the peer network.
  // * Routes of direction `OUTGOING` are exported from the intranet VPC network
  // of the VMware Engine network.
  Direction direction = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents a network policy resource. Network policies are regional
// resources. You can use a network policy to enable or disable internet access
// and external IP access. Network policies are associated with a VMware Engine
// network, which might span across regions. For a given region, a network
// policy applies to all private clouds in the VMware Engine network associated
// with the policy.
message NetworkPolicy {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/NetworkPolicy"
    pattern: "projects/{project}/locations/{location}/networkPolicies/{network_policy}"
  };

  // Represents a network service that is managed by a `NetworkPolicy` resource.
  // A network service provides a way to control an aspect of external access to
  // VMware workloads. For example, whether the VMware workloads in the
  // private clouds governed by a network policy can access or be accessed from
  // the internet.
  message NetworkService {
    // Enum State defines possible states of a network policy controlled
    // service.
    enum State {
      // Unspecified service state. This is the default value.
      STATE_UNSPECIFIED = 0;

      // Service is not provisioned.
      UNPROVISIONED = 1;

      // Service is in the process of being provisioned/deprovisioned.
      RECONCILING = 2;

      // Service is active.
      ACTIVE = 3;
    }

    // True if the service is enabled; false otherwise.
    bool enabled = 1;

    // Output only. State of the service. New values may be added to this enum
    // when appropriate.
    State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The resource name of this network policy.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/networkPolicies/my-network-policy`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Network service that allows VMware workloads to access the internet.
  NetworkService internet_access = 6;

  // Network service that allows External IP addresses to be assigned to VMware
  // workloads. This service can only be enabled when `internet_access` is also
  // enabled.
  NetworkService external_ip = 7;

  // Required. IP address range in CIDR notation used to create internet access
  // and external IP access. An RFC 1918 CIDR block, with a "/26" prefix, is
  // required. The range cannot overlap with any prefixes either in the consumer
  // VPC network or in use by the private clouds attached to that VPC network.
  string edge_services_cidr = 9 [(google.api.field_behavior) = REQUIRED];

  // Output only. System-generated unique identifier for the resource.
  string uid = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The relative resource name of the VMware Engine network.
  // Specify the name in the following form:
  // `projects/{project}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}`
  // where `{project}` can either be a project number or a project ID.
  string vmware_engine_network = 12 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }
  ];

  // Optional. User-provided description for this network policy.
  string description = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The canonical name of the VMware Engine network in the form:
  // `projects/{project_number}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}`
  string vmware_engine_network_canonical = 14 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }
  ];
}

// Represents a binding between a network and the management DNS zone.
// A management DNS zone is the Cloud DNS cross-project binding zone that
// VMware Engine creates for each private cloud. It contains FQDNs and
// corresponding IP addresses for the private cloud's ESXi hosts and management
// VM appliances like vCenter and NSX Manager.
message ManagementDnsZoneBinding {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/ManagementDnsZoneBinding"
    pattern: "projects/{project}/locations/{location}/privateClouds/{private_cloud}/managementDnsZoneBindings/{management_dns_zone_binding}"
  };

  // Enum State defines possible states of binding between the consumer VPC
  // network and the management DNS zone.
  enum State {
    // The default value. This value should never be used.
    STATE_UNSPECIFIED = 0;

    // The binding is ready.
    ACTIVE = 1;

    // The binding is being created.
    CREATING = 2;

    // The binding is being updated.
    UPDATING = 3;

    // The binding is being deleted.
    DELETING = 4;

    // The binding has failed.
    FAILED = 5;
  }

  // Output only. The resource name of this binding.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1-a/privateClouds/my-cloud/managementDnsZoneBindings/my-management-dns-zone-binding`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the resource.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-provided description for this resource.
  string description = 13;

  // Required. The relative resource name of the network to bind to the
  // management DNS zone. This network can be a consumer VPC network or a
  // VMware engine network.
  oneof bind_network {
    // Network to bind is a standard consumer VPC.
    // Specify the name in the following form for consumer
    // VPC network: `projects/{project}/global/networks/{network_id}`.
    // `{project}` can either be a project number or a project ID.
    string vpc_network = 14 [(google.api.resource_reference) = {
      type: "compute.googleapis.com/Network"
    }];

    // Network to bind is a VMware Engine network.
    // Specify the name in the following form for VMware engine network:
    // `projects/{project}/locations/global/vmwareEngineNetworks/{vmware_engine_network_id}`.
    // `{project}` can either be a project number or a project ID.
    string vmware_engine_network = 15 [(google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }];
  }

  // Output only. System-generated unique identifier for the resource.
  string uid = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// VMware Engine network resource that provides connectivity for VMware Engine
// private clouds.
message VmwareEngineNetwork {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    pattern: "projects/{project}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network}"
  };

  // Represents a VMware Engine VPC network that is managed by a
  // VMware Engine network resource.
  message VpcNetwork {
    // Enum Type defines possible types of a VMware Engine network controlled
    // service.
    enum Type {
      // The default value. This value should never be used.
      TYPE_UNSPECIFIED = 0;

      // VPC network that will be peered with a consumer VPC network or the
      // intranet VPC of another VMware Engine network. Access a private cloud
      // through Compute Engine VMs on a peered VPC network or an on-premises
      // resource connected to a peered consumer VPC network.
      INTRANET = 1;

      // VPC network used for internet access to and from a private cloud.
      INTERNET = 2;

      // VPC network used for access to Google Cloud services like
      // Cloud Storage.
      GOOGLE_CLOUD = 3;
    }

    // Output only. Type of VPC network (INTRANET, INTERNET, or
    // GOOGLE_CLOUD)
    Type type = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The relative resource name of the service VPC network this
    // VMware Engine network is attached to. For example:
    // `projects/123123/global/networks/my-network`
    string network = 2 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "compute.googleapis.com/Network"
      }
    ];
  }

  // Enum State defines possible states of VMware Engine network.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // The VMware Engine network is being created.
    CREATING = 1;

    // The VMware Engine network is ready.
    ACTIVE = 2;

    // The VMware Engine network is being updated.
    UPDATING = 3;

    // The VMware Engine network is being deleted.
    DELETING = 4;
  }

  // Enum Type defines possible types of VMware Engine network.
  enum Type {
    // The default value. This value should never be used.
    TYPE_UNSPECIFIED = 0;

    // Network type used by private clouds created in projects without a network
    // of type `STANDARD`. This network type is no longer used for new VMware
    // Engine private cloud deployments.
    LEGACY = 1;

    // Standard network type used for private cloud connectivity.
    STANDARD = 2;
  }

  // Output only. The resource name of the VMware Engine network.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/global/vmwareEngineNetworks/my-network`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-provided description for this VMware Engine network.
  string description = 5;

  // Output only. VMware Engine service VPC networks that provide connectivity
  // from a private cloud to customer projects, the internet, and other Google
  // Cloud services.
  repeated VpcNetwork vpc_networks = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the VMware Engine network.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. VMware Engine network type.
  Type type = 8 [(google.api.field_behavior) = REQUIRED];

  // Output only. System-generated unique identifier for the resource.
  string uid = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Checksum that may be sent on update and delete requests to ensure that the
  // user-provided value is up to date before the server processes a request.
  // The server computes checksums based on the value of other fields in the
  // request.
  string etag = 10;
}

// Private connection resource that provides connectivity for VMware Engine
// private clouds.
message PrivateConnection {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/PrivateConnection"
    pattern: "projects/{project}/locations/{location}/privateConnections/{private_connection}"
  };

  // Enum State defines possible states of private connection.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // The private connection is being created.
    CREATING = 1;

    // The private connection is ready.
    ACTIVE = 2;

    // The private connection is being updated.
    UPDATING = 3;

    // The private connection is being deleted.
    DELETING = 4;

    // The private connection is not provisioned, since no private cloud is
    // present for which this private connection is needed.
    UNPROVISIONED = 5;

    // The private connection is in failed state.
    FAILED = 6;
  }

  // Enum Type defines possible types of private connection.
  enum Type {
    // The default value. This value should never be used.
    TYPE_UNSPECIFIED = 0;

    // Connection used for establishing [private services
    // access](https://cloud.google.com/vpc/docs/private-services-access).
    PRIVATE_SERVICE_ACCESS = 1;

    // Connection used for connecting to NetApp Cloud Volumes.
    NETAPP_CLOUD_VOLUMES = 2;

    // Connection used for connecting to Dell PowerScale.
    DELL_POWERSCALE = 3;

    // Connection used for connecting to third-party services.
    THIRD_PARTY_SERVICE = 4;
  }

  // Possible types for RoutingMode
  enum RoutingMode {
    // The default value. This value should never be used.
    ROUTING_MODE_UNSPECIFIED = 0;

    // Global Routing Mode
    GLOBAL = 1;

    // Regional Routing Mode
    REGIONAL = 2;
  }

  // Enum PeeringState defines the possible states of peering between service
  // network and the vpc network peered to service network
  enum PeeringState {
    // The default value. This value is used if the peering state is omitted or
    // unknown.
    PEERING_STATE_UNSPECIFIED = 0;

    // The peering is in active state.
    PEERING_ACTIVE = 1;

    // The peering is in inactive state.
    PEERING_INACTIVE = 2;
  }

  // Output only. The resource name of the private connection.
  // Resource names are schemeless URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names.
  // For example:
  // `projects/my-project/locations/us-central1/privateConnections/my-connection`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Creation time of this resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Last update time of this resource.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User-provided description for this private connection.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. State of the private connection.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The relative resource name of Legacy VMware Engine network.
  // Specify the name in the following form:
  // `projects/{project}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}`
  // where `{project}`, `{location}` will be same as specified in private
  // connection resource name and `{vmware_engine_network_id}` will be in the
  // form of `{location}`-default e.g.
  // projects/project/locations/us-central1/vmwareEngineNetworks/us-central1-default.
  string vmware_engine_network = 8 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }
  ];

  // Output only. The canonical name of the VMware Engine network in the form:
  // `projects/{project_number}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}`
  string vmware_engine_network_canonical = 9 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "vmwareengine.googleapis.com/VmwareEngineNetwork"
    }
  ];

  // Required. Private connection type.
  Type type = 10 [(google.api.field_behavior) = REQUIRED];

  // Output only. VPC network peering id between given network VPC and
  // VMwareEngineNetwork.
  string peering_id = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Routing Mode.
  // Default value is set to GLOBAL.
  // For type = PRIVATE_SERVICE_ACCESS, this field can be set to GLOBAL or
  // REGIONAL, for other types only GLOBAL is supported.
  RoutingMode routing_mode = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. System-generated unique identifier for the resource.
  string uid = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Service network to create private connection.
  // Specify the name in the following form:
  // `projects/{project}/global/networks/{network_id}`
  // For type = PRIVATE_SERVICE_ACCESS, this field represents servicenetworking
  // VPC, e.g. projects/project-tp/global/networks/servicenetworking.
  // For type = NETAPP_CLOUD_VOLUME, this field represents NetApp service VPC,
  // e.g. projects/project-tp/global/networks/netapp-tenant-vpc.
  // For type = DELL_POWERSCALE, this field represent Dell service VPC, e.g.
  // projects/project-tp/global/networks/dell-tenant-vpc.
  // For type= THIRD_PARTY_SERVICE, this field could represent a consumer VPC or
  // any other producer VPC to which the VMware Engine Network needs to be
  // connected, e.g. projects/project/global/networks/vpc.
  string service_network = 16 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Output only. Peering state between service network and VMware Engine
  // network.
  PeeringState peering_state = 17 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// VmwareEngine specific metadata for the given
// [google.cloud.location.Location][google.cloud.location.Location]. It is
// returned as a content of the `google.cloud.location.Location.metadata` field.
message LocationMetadata {
  // Capability of a location.
  enum Capability {
    // The default value. This value is used if the capability is omitted or
    // unknown.
    CAPABILITY_UNSPECIFIED = 0;

    // Stretch clusters are supported in this location.
    STRETCHED_CLUSTERS = 1;
  }

  // Output only. Capabilities of this location.
  repeated Capability capabilities = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// DnsBindPermission resource that contains the accounts having the consumer DNS
// bind permission on the corresponding intranet VPC of the consumer project.
message DnsBindPermission {
  option (google.api.resource) = {
    type: "vmwareengine.googleapis.com/DnsBindPermission"
    pattern: "projects/{project}/locations/{location}/dnsBindPermission"
  };

  // Required. Output only. The name of the resource which stores the
  // users/service accounts having the permission to bind to the corresponding
  // intranet VPC of the consumer project. DnsBindPermission is a global
  // resource and location can only be global. Resource names are schemeless
  // URIs that follow the conventions in
  // https://cloud.google.com/apis/design/resource_names. For example:
  // `projects/my-project/locations/global/dnsBindPermission`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Users/Service accounts which have access for binding on the
  // intranet VPC project corresponding to the consumer project.
  repeated Principal principals = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Users/Service accounts which have access for DNS binding on the intranet
// VPC corresponding to the consumer project.
message Principal {
  // The consumer provided user/service account which needs to be
  // granted permission to DNS bind with the intranet VPC corresponding to the
  // consumer project.
  oneof principal {
    // The user who needs to be granted permission.
    string user = 1;

    // The service account which needs to be granted the permission.
    string service_account = 2;
  }
}
