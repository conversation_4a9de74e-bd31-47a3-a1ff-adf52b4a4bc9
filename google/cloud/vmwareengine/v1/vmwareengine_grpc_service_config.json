{"methodConfig": [{"name": [{"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListPrivateClouds"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetPrivateCloud"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListClusters"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetCluster"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListSubnets"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetSubnet"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetPrivateConnection"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListPrivateConnections"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListPrivateConnectionPeeringRoutes"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListNodeTypes"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetNodeType"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListHcxActivationKeys"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetHcxActivationKey"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListNetworkPolicies"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetNetworkPolicy"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ShowNsxCredentials"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ShowVcenterCredentials"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetVmwareEngineNetwork"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListVmwareEngineNetworks"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetManagementDnsZoneBinding"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListManagementDnsZoneBindings"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetDnsBindPermission"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetDnsForwarding"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetExternalAccessRule"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListExternalAccessRules"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetExternalAddress"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListExternalAddresses"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetLoggingServer"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListLoggingServers"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetNetworkPeering"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListNetworkPeerings"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListPeeringRoutes"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "GetNode"}, {"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "ListNodes"}], "timeout": "120s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.vmwareengine.v1.VmwareEngine", "method": "UpdateSubnet"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.vmwareengine.v1.VmwareEngine"}], "timeout": "120s"}]}