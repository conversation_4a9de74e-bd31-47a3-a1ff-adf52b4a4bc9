# This build file includes a target for the Ruby wrapper library for
# google-cloud-vmware_engine.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for VMware Engine.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "vmwareengine_ruby_wrapper",
    srcs = ["//google/cloud/vmwareengine/v1:vmwareengine_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-vmware_engine",
        "ruby-cloud-wrapper-of=v1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/vmware-engine/",
        "ruby-cloud-api-id=vmwareengine.googleapis.com",
        "ruby-cloud-api-shortname=vmwareengine",
    ],
    ruby_cloud_description = "Google Cloud VMware Engine is a fully managed service that lets you run the VMware platform in Google Cloud. VMware Engine provides you with VMware operational continuity so you can benefit from a cloud consumption model and lower your total cost of ownership. VMware Engine also offers on-demand provisioning, pay-as-you-grow, and capacity optimization.",
    ruby_cloud_title = "Google Cloud VMware Engine",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-vmwareengine-ruby",
    deps = [
        ":vmwareengine_ruby_wrapper",
    ],
)
