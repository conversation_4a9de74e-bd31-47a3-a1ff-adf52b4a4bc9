# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "iot_proto",
    srcs = [
        "device_manager.proto",
        "resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "iot_proto_with_info",
    deps = [
        ":iot_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "iot_java_proto",
    deps = [":iot_proto"],
)

java_grpc_library(
    name = "iot_java_grpc",
    srcs = [":iot_proto"],
    deps = [":iot_java_proto"],
)

java_gapic_library(
    name = "iot_java_gapic",
    srcs = [":iot_proto_with_info"],
    grpc_service_config = "cloudiot_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudiot_v1.yaml",
    test_deps = [
        ":iot_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":iot_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "iot_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.iot.v1.DeviceManagerClientHttpJsonTest",
        "com.google.cloud.iot.v1.DeviceManagerClientTest",
    ],
    runtime_deps = [":iot_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-iot-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":iot_java_gapic",
        ":iot_java_grpc",
        ":iot_java_proto",
        ":iot_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "iot_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/iot/apiv1/iotpb",
    protos = [":iot_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "iot_go_gapic",
    srcs = [":iot_proto_with_info"],
    grpc_service_config = "cloudiot_grpc_service_config.json",
    importpath = "cloud.google.com/go/iot/apiv1;iot",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudiot_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":iot_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-iot-v1-go",
    deps = [
        ":iot_go_gapic",
        ":iot_go_gapic_srcjar-snippets.srcjar",
        ":iot_go_gapic_srcjar-test.srcjar",
        ":iot_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "iot_py_gapic",
    srcs = [":iot_proto"],
    grpc_service_config = "cloudiot_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudiot_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "iot_py_gapic_test",
    srcs = [
        "iot_py_gapic_pytest.py",
        "iot_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":iot_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "iot-v1-py",
    deps = [
        ":iot_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "iot_php_proto",
    deps = [":iot_proto"],
)

php_gapic_library(
    name = "iot_php_gapic",
    srcs = [":iot_proto_with_info"],
    grpc_service_config = "cloudiot_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudiot_v1.yaml",
    transport = "grpc+rest",
    deps = [":iot_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-iot-v1-php",
    deps = [
        ":iot_php_gapic",
        ":iot_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "iot_nodejs_gapic",
    package_name = "@google-cloud/iot",
    src = ":iot_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudiot_grpc_service_config.json",
    main_service = "iot",
    package = "google.cloud.iot.v1",
    rest_numeric_enums = True,
    service_yaml = "cloudiot_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "iot-v1-nodejs",
    deps = [
        ":iot_nodejs_gapic",
        ":iot_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "iot_ruby_proto",
    deps = [":iot_proto"],
)

ruby_grpc_library(
    name = "iot_ruby_grpc",
    srcs = [":iot_proto"],
    deps = [":iot_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "iot_ruby_gapic",
    srcs = [":iot_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-iot-v1",
        "ruby-cloud-env-prefix=IOT",
        "ruby-cloud-product-url=https://cloud.google.com/iot",
        "ruby-cloud-api-id=cloudiot.googleapis.com",
        "ruby-cloud-api-shortname=cloudiot",
    ],
    grpc_service_config = "cloudiot_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Registers and manages IoT (Internet of Things) devices that connect to the Google Cloud Platform.",
    ruby_cloud_title = "Cloud IoT V1",
    service_yaml = "cloudiot_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":iot_ruby_grpc",
        ":iot_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-iot-v1-ruby",
    deps = [
        ":iot_ruby_gapic",
        ":iot_ruby_grpc",
        ":iot_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "iot_csharp_proto",
    deps = [":iot_proto"],
)

csharp_grpc_library(
    name = "iot_csharp_grpc",
    srcs = [":iot_proto"],
    deps = [":iot_csharp_proto"],
)

csharp_gapic_library(
    name = "iot_csharp_gapic",
    srcs = [":iot_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudiot_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudiot_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":iot_csharp_grpc",
        ":iot_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-iot-v1-csharp",
    deps = [
        ":iot_csharp_gapic",
        ":iot_csharp_grpc",
        ":iot_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "iot_cc_proto",
    deps = [":iot_proto"],
)

cc_grpc_library(
    name = "iot_cc_grpc",
    srcs = [":iot_proto"],
    grpc_only = True,
    deps = [":iot_cc_proto"],
)
