// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.iot.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/iot/v1/resources.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option cc_enable_arenas = true;
option go_package = "cloud.google.com/go/iot/apiv1/iotpb;iotpb";
option java_multiple_files = true;
option java_outer_classname = "DeviceManagerProto";
option java_package = "com.google.cloud.iot.v1";

// Internet of Things (IoT) service. Securely connect and manage IoT devices.
service DeviceManager {
  option (google.api.default_host) = "cloudiot.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloudiot";

  // Creates a device registry that contains devices.
  rpc CreateDeviceRegistry(CreateDeviceRegistryRequest) returns (DeviceRegistry) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/registries"
      body: "device_registry"
    };
    option (google.api.method_signature) = "parent,device_registry";
  }

  // Gets a device registry configuration.
  rpc GetDeviceRegistry(GetDeviceRegistryRequest) returns (DeviceRegistry) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/registries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a device registry configuration.
  rpc UpdateDeviceRegistry(UpdateDeviceRegistryRequest) returns (DeviceRegistry) {
    option (google.api.http) = {
      patch: "/v1/{device_registry.name=projects/*/locations/*/registries/*}"
      body: "device_registry"
    };
    option (google.api.method_signature) = "device_registry,update_mask";
  }

  // Deletes a device registry configuration.
  rpc DeleteDeviceRegistry(DeleteDeviceRegistryRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/registries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists device registries.
  rpc ListDeviceRegistries(ListDeviceRegistriesRequest) returns (ListDeviceRegistriesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/registries"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a device in a device registry.
  rpc CreateDevice(CreateDeviceRequest) returns (Device) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/registries/*}/devices"
      body: "device"
    };
    option (google.api.method_signature) = "parent,device";
  }

  // Gets details about a device.
  rpc GetDevice(GetDeviceRequest) returns (Device) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/registries/*/devices/*}"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/registries/*/groups/*/devices/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a device.
  rpc UpdateDevice(UpdateDeviceRequest) returns (Device) {
    option (google.api.http) = {
      patch: "/v1/{device.name=projects/*/locations/*/registries/*/devices/*}"
      body: "device"
      additional_bindings {
        patch: "/v1/{device.name=projects/*/locations/*/registries/*/groups/*/devices/*}"
        body: "device"
      }
    };
    option (google.api.method_signature) = "device,update_mask";
  }

  // Deletes a device.
  rpc DeleteDevice(DeleteDeviceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/registries/*/devices/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // List devices in a device registry.
  rpc ListDevices(ListDevicesRequest) returns (ListDevicesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/registries/*}/devices"
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/registries/*/groups/*}/devices"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Modifies the configuration for the device, which is eventually sent from
  // the Cloud IoT Core servers. Returns the modified configuration version and
  // its metadata.
  rpc ModifyCloudToDeviceConfig(ModifyCloudToDeviceConfigRequest) returns (DeviceConfig) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/registries/*/devices/*}:modifyCloudToDeviceConfig"
      body: "*"
      additional_bindings {
        post: "/v1/{name=projects/*/locations/*/registries/*/groups/*/devices/*}:modifyCloudToDeviceConfig"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,binary_data";
  }

  // Lists the last few versions of the device configuration in descending
  // order (i.e.: newest first).
  rpc ListDeviceConfigVersions(ListDeviceConfigVersionsRequest) returns (ListDeviceConfigVersionsResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/registries/*/devices/*}/configVersions"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/registries/*/groups/*/devices/*}/configVersions"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the last few versions of the device state in descending order (i.e.:
  // newest first).
  rpc ListDeviceStates(ListDeviceStatesRequest) returns (ListDeviceStatesResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/registries/*/devices/*}/states"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/registries/*/groups/*/devices/*}/states"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Sets the access control policy on the specified resource. Replaces any
  // existing policy.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/registries/*}:setIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1/{resource=projects/*/locations/*/registries/*/groups/*}:setIamPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Gets the access control policy for a resource.
  // Returns an empty policy if the resource exists and does not have a policy
  // set.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/registries/*}:getIamPolicy"
      body: "*"
      additional_bindings {
        post: "/v1/{resource=projects/*/locations/*/registries/*/groups/*}:getIamPolicy"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource";
  }

  // Returns permissions that a caller has on the specified resource.
  // If the resource does not exist, this will return an empty set of
  // permissions, not a NOT_FOUND error.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest) returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*/locations/*/registries/*}:testIamPermissions"
      body: "*"
      additional_bindings {
        post: "/v1/{resource=projects/*/locations/*/registries/*/groups/*}:testIamPermissions"
        body: "*"
      }
    };
    option (google.api.method_signature) = "resource,permissions";
  }

  // Sends a command to the specified device. In order for a device to be able
  // to receive commands, it must:
  // 1) be connected to Cloud IoT Core using the MQTT protocol, and
  // 2) be subscribed to the group of MQTT topics specified by
  //    /devices/{device-id}/commands/#. This subscription will receive commands
  //    at the top-level topic /devices/{device-id}/commands as well as commands
  //    for subfolders, like /devices/{device-id}/commands/subfolder.
  //    Note that subscribing to specific subfolders is not supported.
  // If the command could not be delivered to the device, this method will
  // return an error; in particular, if the device is not subscribed, this
  // method will return FAILED_PRECONDITION. Otherwise, this method will
  // return OK. If the subscription is QoS 1, at least once delivery will be
  // guaranteed; for QoS 0, no acknowledgment will be expected from the device.
  rpc SendCommandToDevice(SendCommandToDeviceRequest) returns (SendCommandToDeviceResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/registries/*/devices/*}:sendCommandToDevice"
      body: "*"
      additional_bindings {
        post: "/v1/{name=projects/*/locations/*/registries/*/groups/*/devices/*}:sendCommandToDevice"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,binary_data";
    option (google.api.method_signature) = "name,binary_data,subfolder";
  }

  // Associates the device with the gateway.
  rpc BindDeviceToGateway(BindDeviceToGatewayRequest) returns (BindDeviceToGatewayResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/registries/*}:bindDeviceToGateway"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/registries/*/groups/*}:bindDeviceToGateway"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,gateway_id,device_id";
  }

  // Deletes the association between the device and the gateway.
  rpc UnbindDeviceFromGateway(UnbindDeviceFromGatewayRequest) returns (UnbindDeviceFromGatewayResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/registries/*}:unbindDeviceFromGateway"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/registries/*/groups/*}:unbindDeviceFromGateway"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,gateway_id,device_id";
  }
}

// Request for `CreateDeviceRegistry`.
message CreateDeviceRegistryRequest {
  // Required. The project and cloud region where this device registry must be created.
  // For example, `projects/example-project/locations/us-central1`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The device registry. The field `name` must be empty. The server will
  // generate that field from the device registry `id` provided and the
  // `parent` field.
  DeviceRegistry device_registry = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for `GetDeviceRegistry`.
message GetDeviceRegistryRequest {
  // Required. The name of the device registry. For example,
  // `projects/example-project/locations/us-central1/registries/my-registry`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Registry"
    }
  ];
}

// Request for `DeleteDeviceRegistry`.
message DeleteDeviceRegistryRequest {
  // Required. The name of the device registry. For example,
  // `projects/example-project/locations/us-central1/registries/my-registry`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Registry"
    }
  ];
}

// Request for `UpdateDeviceRegistry`.
message UpdateDeviceRegistryRequest {
  // Required. The new values for the device registry. The `id` field must be empty, and
  // the `name` field must indicate the path of the resource. For example,
  // `projects/example-project/locations/us-central1/registries/my-registry`.
  DeviceRegistry device_registry = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Only updates the `device_registry` fields indicated by this mask.
  // The field mask must not be empty, and it must not contain fields that
  // are immutable or only set by the server.
  // Mutable top-level fields: `event_notification_config`, `http_config`,
  // `mqtt_config`, and `state_notification_config`.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for `ListDeviceRegistries`.
message ListDeviceRegistriesRequest {
  // Required. The project and cloud region path. For example,
  // `projects/example-project/locations/us-central1`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of registries to return in the response. If this value
  // is zero, the service will select a default size. A call may return fewer
  // objects than requested. A non-empty `next_page_token` in the response
  // indicates that more data is available.
  int32 page_size = 2;

  // The value returned by the last `ListDeviceRegistriesResponse`; indicates
  // that this is a continuation of a prior `ListDeviceRegistries` call and
  // the system should return the next page of data.
  string page_token = 3;
}

// Response for `ListDeviceRegistries`.
message ListDeviceRegistriesResponse {
  // The registries that matched the query.
  repeated DeviceRegistry device_registries = 1;

  // If not empty, indicates that there may be more registries that match the
  // request; this value should be passed in a new
  // `ListDeviceRegistriesRequest`.
  string next_page_token = 2;
}

// Request for `CreateDevice`.
message CreateDeviceRequest {
  // Required. The name of the device registry where this device should be created.
  // For example,
  // `projects/example-project/locations/us-central1/registries/my-registry`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Registry"
    }
  ];

  // Required. The device registration details. The field `name` must be empty. The server
  // generates `name` from the device registry `id` and the
  // `parent` field.
  Device device = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for `GetDevice`.
message GetDeviceRequest {
  // Required. The name of the device. For example,
  // `projects/p0/locations/us-central1/registries/registry0/devices/device0` or
  // `projects/p0/locations/us-central1/registries/registry0/devices/{num_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Device"
    }
  ];

  // The fields of the `Device` resource to be returned in the response. If the
  // field mask is unset or empty, all fields are returned. Fields have to be
  // provided in snake_case format, for example: `last_heartbeat_time`.
  google.protobuf.FieldMask field_mask = 2;
}

// Request for `UpdateDevice`.
message UpdateDeviceRequest {
  // Required. The new values for the device. The `id` and `num_id` fields must
  // be empty, and the field `name` must specify the name path. For example,
  // `projects/p0/locations/us-central1/registries/registry0/devices/device0`or
  // `projects/p0/locations/us-central1/registries/registry0/devices/{num_id}`.
  Device device = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Only updates the `device` fields indicated by this mask.
  // The field mask must not be empty, and it must not contain fields that
  // are immutable or only set by the server.
  // Mutable top-level fields: `credentials`, `blocked`, and `metadata`
  google.protobuf.FieldMask update_mask = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for `DeleteDevice`.
message DeleteDeviceRequest {
  // Required. The name of the device. For example,
  // `projects/p0/locations/us-central1/registries/registry0/devices/device0` or
  // `projects/p0/locations/us-central1/registries/registry0/devices/{num_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Device"
    }
  ];
}

// Request for `ListDevices`.
message ListDevicesRequest {
  // Required. The device registry path. Required. For example,
  // `projects/my-project/locations/us-central1/registries/my-registry`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Registry"
    }
  ];

  // A list of device numeric IDs. If empty, this field is ignored. Maximum
  // IDs: 10,000.
  repeated uint64 device_num_ids = 2;

  // A list of device string IDs. For example, `['device0', 'device12']`.
  // If empty, this field is ignored. Maximum IDs: 10,000
  repeated string device_ids = 3;

  // The fields of the `Device` resource to be returned in the response. The
  // fields `id` and `num_id` are always returned, along with any
  // other fields specified in snake_case format, for example:
  // `last_heartbeat_time`.
  google.protobuf.FieldMask field_mask = 4;

  // Options related to gateways.
  GatewayListOptions gateway_list_options = 6;

  // The maximum number of devices to return in the response. If this value
  // is zero, the service will select a default size. A call may return fewer
  // objects than requested. A non-empty `next_page_token` in the response
  // indicates that more data is available.
  int32 page_size = 100;

  // The value returned by the last `ListDevicesResponse`; indicates
  // that this is a continuation of a prior `ListDevices` call and
  // the system should return the next page of data.
  string page_token = 101;
}

// Options for limiting the list based on gateway type and associations.
message GatewayListOptions {
  // If not set, all devices and gateways are returned. If set, the list is
  // filtered based on gateway type and associations.
  oneof filter {
    // If `GATEWAY` is specified, only gateways are returned. If `NON_GATEWAY`
    // is specified, only non-gateway devices are returned. If
    // `GATEWAY_TYPE_UNSPECIFIED` is specified, all devices are returned.
    GatewayType gateway_type = 1;

    // If set, only devices associated with the specified gateway are returned.
    // The gateway ID can be numeric (`num_id`) or the user-defined string
    // (`id`). For example, if `123` is specified, only devices bound to the
    // gateway with `num_id` 123 are returned.
    string associations_gateway_id = 2;

    // If set, returns only the gateways with which the specified device is
    // associated. The device ID can be numeric (`num_id`) or the user-defined
    // string (`id`). For example, if `456` is specified, returns only the
    // gateways to which the device with `num_id` 456 is bound.
    string associations_device_id = 3;
  }
}

// Response for `ListDevices`.
message ListDevicesResponse {
  // The devices that match the request.
  repeated Device devices = 1;

  // If not empty, indicates that there may be more devices that match the
  // request; this value should be passed in a new `ListDevicesRequest`.
  string next_page_token = 2;
}

// Request for `ModifyCloudToDeviceConfig`.
message ModifyCloudToDeviceConfigRequest {
  // Required. The name of the device. For example,
  // `projects/p0/locations/us-central1/registries/registry0/devices/device0` or
  // `projects/p0/locations/us-central1/registries/registry0/devices/{num_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Device"
    }
  ];

  // The version number to update. If this value is zero, it will not check the
  // version number of the server and will always update the current version;
  // otherwise, this update will fail if the version number found on the server
  // does not match this version number. This is used to support multiple
  // simultaneous updates without losing data.
  int64 version_to_update = 2;

  // Required. The configuration data for the device.
  bytes binary_data = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for `ListDeviceConfigVersions`.
message ListDeviceConfigVersionsRequest {
  // Required. The name of the device. For example,
  // `projects/p0/locations/us-central1/registries/registry0/devices/device0` or
  // `projects/p0/locations/us-central1/registries/registry0/devices/{num_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Device"
    }
  ];

  // The number of versions to list. Versions are listed in decreasing order of
  // the version number. The maximum number of versions retained is 10. If this
  // value is zero, it will return all the versions available.
  int32 num_versions = 2;
}

// Response for `ListDeviceConfigVersions`.
message ListDeviceConfigVersionsResponse {
  // The device configuration for the last few versions. Versions are listed
  // in decreasing order, starting from the most recent one.
  repeated DeviceConfig device_configs = 1;
}

// Request for `ListDeviceStates`.
message ListDeviceStatesRequest {
  // Required. The name of the device. For example,
  // `projects/p0/locations/us-central1/registries/registry0/devices/device0` or
  // `projects/p0/locations/us-central1/registries/registry0/devices/{num_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Device"
    }
  ];

  // The number of states to list. States are listed in descending order of
  // update time. The maximum number of states retained is 10. If this
  // value is zero, it will return all the states available.
  int32 num_states = 2;
}

// Response for `ListDeviceStates`.
message ListDeviceStatesResponse {
  // The last few device states. States are listed in descending order of server
  // update time, starting from the most recent one.
  repeated DeviceState device_states = 1;
}

// Request for `SendCommandToDevice`.
message SendCommandToDeviceRequest {
  // Required. The name of the device. For example,
  // `projects/p0/locations/us-central1/registries/registry0/devices/device0` or
  // `projects/p0/locations/us-central1/registries/registry0/devices/{num_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Device"
    }
  ];

  // Required. The command data to send to the device.
  bytes binary_data = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional subfolder for the command. If empty, the command will be delivered
  // to the /devices/{device-id}/commands topic, otherwise it will be delivered
  // to the /devices/{device-id}/commands/{subfolder} topic. Multi-level
  // subfolders are allowed. This field must not have more than 256 characters,
  // and must not contain any MQTT wildcards ("+" or "#") or null characters.
  string subfolder = 3;
}

// Response for `SendCommandToDevice`.
message SendCommandToDeviceResponse {

}

// Request for `BindDeviceToGateway`.
message BindDeviceToGatewayRequest {
  // Required. The name of the registry. For example,
  // `projects/example-project/locations/us-central1/registries/my-registry`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Registry"
    }
  ];

  // Required. The value of `gateway_id` can be either the device numeric ID or the
  // user-defined device identifier.
  string gateway_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The device to associate with the specified gateway. The value of
  // `device_id` can be either the device numeric ID or the user-defined device
  // identifier.
  string device_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response for `BindDeviceToGateway`.
message BindDeviceToGatewayResponse {

}

// Request for `UnbindDeviceFromGateway`.
message UnbindDeviceFromGatewayRequest {
  // Required. The name of the registry. For example,
  // `projects/example-project/locations/us-central1/registries/my-registry`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudiot.googleapis.com/Registry"
    }
  ];

  // Required. The value of `gateway_id` can be either the device numeric ID or the
  // user-defined device identifier.
  string gateway_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The device to disassociate from the specified gateway. The value of
  // `device_id` can be either the device numeric ID or the user-defined device
  // identifier.
  string device_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response for `UnbindDeviceFromGateway`.
message UnbindDeviceFromGatewayResponse {

}
