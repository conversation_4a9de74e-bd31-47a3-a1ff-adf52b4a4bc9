type: google.api.Service
config_version: 3
name: cloudiot.googleapis.com
title: Cloud IoT API

apis:
- name: google.cloud.iot.v1.DeviceManager

documentation:
  summary: |-
    Registers and manages IoT (Internet of Things) devices that connect to the
    Google Cloud Platform.

backend:
  rules:
  - selector: google.cloud.iot.v1.DeviceManager.SendCommandToDevice
    deadline: 60.0

authentication:
  rules:
  - selector: 'google.cloud.iot.v1.DeviceManager.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloudiot
