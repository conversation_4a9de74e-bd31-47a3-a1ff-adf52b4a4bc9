# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

_PROTO_SUBPACKAGE_DEPS = [
    "//google/cloud/gkehub/v1/multiclusteringress:multiclusteringress_proto",
    "//google/cloud/gkehub/v1/configmanagement:configmanagement_proto",
]

proto_library(
    name = "gkehub_proto",
    srcs = [
        "feature.proto",
        "membership.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ] + _PROTO_SUBPACKAGE_DEPS,
)

proto_library_with_info(
    name = "gkehub_proto_with_info",
    deps = [
        ":gkehub_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

_JAVA_PROTO_SUBPACKAGE_DEPS = [
    "//google/cloud/gkehub/v1/multiclusteringress:multiclusteringress_java_proto",
    "//google/cloud/gkehub/v1/configmanagement:configmanagement_java_proto",
]

_JAVA_GRPC_SUBPACKAGE_DEPS = [
    "//google/cloud/gkehub/v1/multiclusteringress:multiclusteringress_java_grpc",
    "//google/cloud/gkehub/v1/configmanagement:configmanagement_java_grpc",
]

java_proto_library(
    name = "gkehub_java_proto",
    deps = [":gkehub_proto"],
)

java_grpc_library(
    name = "gkehub_java_grpc",
    srcs = [":gkehub_proto"],
    deps = [":gkehub_java_proto"] + _JAVA_PROTO_SUBPACKAGE_DEPS,
)

java_gapic_library(
    name = "gkehub_java_gapic",
    srcs = [":gkehub_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "gkehub_v1.yaml",
    test_deps = [
        ":gkehub_java_grpc",
    ] + _JAVA_GRPC_SUBPACKAGE_DEPS,
    transport = "grpc+rest",
    deps = [
        ":gkehub_java_proto",
        "//google/api:api_java_proto",
    ] + _JAVA_PROTO_SUBPACKAGE_DEPS,
)

java_gapic_test(
    name = "gkehub_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.gkehub.v1.GkeHubClientHttpJsonTest",
        "com.google.cloud.gkehub.v1.GkeHubClientTest",
    ],
    runtime_deps = [":gkehub_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-gkehub-v1-java",
    transport = "grpc+rest",
    deps = [
        ":gkehub_java_gapic",
        ":gkehub_java_grpc",
        ":gkehub_java_proto",
        ":gkehub_proto",
    ] + _PROTO_SUBPACKAGE_DEPS + _JAVA_PROTO_SUBPACKAGE_DEPS + _JAVA_GRPC_SUBPACKAGE_DEPS,
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "gkehub_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/gkehub/apiv1/gkehubpb",
    protos = [":gkehub_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/cloud/gkehub/v1/configmanagement:configmanagement_go_proto",
        "//google/cloud/gkehub/v1/multiclusteringress:multiclusteringress_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "gkehub_go_gapic",
    srcs = [":gkehub_proto_with_info"],
    grpc_service_config = "v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/gkehub/apiv1;gkehub",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "gkehub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gkehub_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-gkehub-v1-go",
    deps = [
        ":gkehub_go_gapic",
        ":gkehub_go_gapic_srcjar-metadata.srcjar",
        ":gkehub_go_gapic_srcjar-snippets.srcjar",
        ":gkehub_go_gapic_srcjar-test.srcjar",
        ":gkehub_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
)

py_gapic_library(
    name = "gkehub_py_gapic",
    srcs = [":gkehub_proto"],
    grpc_service_config = "v1_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-gke-hub"],
    rest_numeric_enums = True,
    service_yaml = "gkehub_v1.yaml",
    transport = "grpc+rest",
)

# Uncomment once https://github.com/googleapis/gapic-generator-python/issues/1376 is fixed
#py_test(
#    name = "gkehub_py_gapic_test",
#    srcs = [
#        "gkehub_py_gapic_pytest.py",
#        "gkehub_py_gapic_test.py",
#    ],
#    legacy_create_init = False,
#    deps = [":gkehub_py_gapic"],
#)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "gkehub-v1-py",
    deps = [
        ":gkehub_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "gkehub_php_proto",
    deps = [":gkehub_proto"],
)

php_gapic_library(
    name = "gkehub_php_gapic",
    srcs = [":gkehub_proto_with_info"],
    grpc_service_config = "v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "gkehub_v1.yaml",
    transport = "grpc+rest",
    deps = [":gkehub_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-gkehub-v1-php",
    deps = [
        ":gkehub_php_gapic",
        ":gkehub_php_proto",
        "//google/cloud/gkehub/v1/configmanagement:configmanagement_php_proto",
        "//google/cloud/gkehub/v1/multiclusteringress:multiclusteringress_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "gkehub_nodejs_gapic",
    package_name = "@google-cloud/gke-hub",
    src = ":gkehub_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "v1_grpc_service_config.json",
    package = "google.cloud.gkehub.v1",
    rest_numeric_enums = True,
    service_yaml = "gkehub_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "gkehub-v1-nodejs",
    deps = [
        ":gkehub_nodejs_gapic",
        ":gkehub_proto",
        "//google/cloud/gkehub/v1/configmanagement:configmanagement_proto",
        "//google/cloud/gkehub/v1/multiclusteringress:multiclusteringress_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "gkehub_ruby_proto",
    deps = [
        ":gkehub_proto",
        "//google/cloud/gkehub/v1/configmanagement:configmanagement_proto",
        "//google/cloud/gkehub/v1/multiclusteringress:multiclusteringress_proto",
    ],
)

ruby_grpc_library(
    name = "gkehub_ruby_grpc",
    srcs = [":gkehub_proto"],
    deps = [":gkehub_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "gkehub_ruby_gapic",
    srcs = [":gkehub_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=gkehub.googleapis.com",
        "ruby-cloud-api-shortname=gkehub",
        "ruby-cloud-env-prefix=GKE_HUB",
        "ruby-cloud-gem-name=google-cloud-gke_hub-v1",
        "ruby-cloud-product-url=https://cloud.google.com/anthos/clusters/docs",
    ],
    grpc_service_config = "v1_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The GKE Hub API centrally manages features and services on all your Kubernetes clusters running in a variety of environments, including Google cloud, on premises in customer datacenters, or other third party clouds.",
    ruby_cloud_title = "GKE Hub V1",
    service_yaml = "gkehub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gkehub_ruby_grpc",
        ":gkehub_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-gkehub-v1-ruby",
    deps = [
        ":gkehub_ruby_gapic",
        ":gkehub_ruby_grpc",
        ":gkehub_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "gkehub_csharp_proto",
    deps = [":gkehub_proto"],
)

csharp_grpc_library(
    name = "gkehub_csharp_grpc",
    srcs = [":gkehub_proto"],
    deps = [":gkehub_csharp_proto"],
)

csharp_gapic_library(
    name = "gkehub_csharp_gapic",
    srcs = [":gkehub_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "gkehub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":gkehub_csharp_grpc",
        ":gkehub_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-gkehub-v1-csharp",
    deps = [
        ":gkehub_csharp_gapic",
        ":gkehub_csharp_grpc",
        ":gkehub_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "gkehub_cc_proto",
    deps = [":gkehub_proto"],
)

cc_grpc_library(
    name = "gkehub_cc_grpc",
    srcs = [":gkehub_proto"],
    grpc_only = True,
    deps = [":gkehub_cc_proto"],
)
