// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkehub.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeHub.V1";
option go_package = "cloud.google.com/go/gkehub/apiv1/gkehubpb;gkehubpb";
option java_multiple_files = true;
option java_outer_classname = "MembershipProto";
option java_package = "com.google.cloud.gkehub.v1";
option php_namespace = "Google\\Cloud\\GkeHub\\V1";
option ruby_package = "Google::Cloud::GkeHub::V1";

// Membership contains information about a member cluster.
message Membership {
  option (google.api.resource) = {
    type: "gkehub.googleapis.com/Membership"
    pattern: "projects/{project}/locations/{location}/memberships/{membership}"
  };

  // Type of resource represented by this Membership
  oneof type {
    // Optional. Endpoint information to reach this member.
    MembershipEndpoint endpoint = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. The full, unique name of this Membership resource in the
  // format `projects/*/locations/*/memberships/{membership_id}`, set during
  // creation.
  //
  // `membership_id` must be a valid RFC 1123 compliant DNS label:
  //
  //   1. At most 63 characters in length
  //   2. It must consist of lower case alphanumeric characters or `-`
  //   3. It must start and end with an alphanumeric character
  //
  // Which can be expressed as the regex: `[a-z0-9]([-a-z0-9]*[a-z0-9])?`,
  // with a maximum length of 63 characters.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels for this membership.
  map<string, string> labels = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Description of this membership, limited to 63 characters.
  // Must match the regex: `[a-zA-Z0-9][a-zA-Z0-9_\-\.\ ]*`
  //
  // This field is present for legacy purposes.
  string description = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the Membership resource.
  MembershipState state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the Membership was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the Membership was last updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the Membership was deleted.
  google.protobuf.Timestamp delete_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. An externally-generated and managed ID for this Membership. This
  // ID may be modified after creation, but this is not recommended.
  //
  // The ID must match the regex: `[a-zA-Z0-9][a-zA-Z0-9_\-\.]*`
  //
  // If this Membership represents a Kubernetes cluster, this value should be
  // set to the UID of the `kube-system` namespace object.
  string external_id = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. For clusters using Connect, the timestamp of the most recent
  // connection established with Google Cloud. This time is updated every
  // several minutes, not continuously. For clusters that do not use GKE
  // Connect, or that have never connected successfully, this field will be
  // unset.
  google.protobuf.Timestamp last_connection_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Google-generated UUID for this resource. This is unique across
  // all Membership resources. If a Membership resource is deleted and another
  // resource with the same name is created, it gets a different unique_id.
  string unique_id = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. How to identify workloads from this Membership.
  // See the documentation on Workload Identity for more details:
  // https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity
  Authority authority = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The monitoring config information for this membership.
  MonitoringConfig monitoring_config = 14
      [(google.api.field_behavior) = OPTIONAL];
}

// MembershipEndpoint contains information needed to contact a Kubernetes API,
// endpoint and any additional Kubernetes metadata.
message MembershipEndpoint {
  // Optional. GKE-specific information. Only present if this Membership is a GKE cluster.
    GkeCluster gke_cluster = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Useful Kubernetes-specific metadata.
  KubernetesMetadata kubernetes_metadata = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The in-cluster Kubernetes Resources that should be applied for a
  // correctly registered cluster, in the steady state. These resources:
  //
  //   * Ensure that the cluster is exclusively registered to one and only one
  //     Hub Membership.
  //   * Propagate Workload Pool Information available in the Membership
  //     Authority field.
  //   * Ensure proper initial configuration of default Hub Features.
  KubernetesResource kubernetes_resource = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Whether the lifecycle of this membership is managed by a
  // google cluster platform service.
  bool google_managed = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// KubernetesResource contains the YAML manifests and configuration for
// Membership Kubernetes resources in the cluster. After CreateMembership or
// UpdateMembership, these resources should be re-applied in the cluster.
message KubernetesResource {
  // Input only. The YAML representation of the Membership CR. This field is
  // ignored for GKE clusters where Hub can read the CR directly.
  //
  // Callers should provide the CR that is currently present in the cluster
  // during CreateMembership or UpdateMembership, or leave this field empty if
  // none exists. The CR manifest is used to validate the cluster has not been
  // registered with another Membership.
  string membership_cr_manifest = 1 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Additional Kubernetes resources that need to be applied to the
  // cluster after Membership creation, and after every update.
  //
  // This field is only populated in the Membership returned from a successful
  // long-running operation from CreateMembership or UpdateMembership. It is not
  // populated during normal GetMembership or ListMemberships requests. To get
  // the resource manifest after the initial registration, the caller should
  // make a UpdateMembership call with an empty field mask.
  repeated ResourceManifest membership_resources = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Kubernetes resources for installing the GKE Connect agent
  //
  // This field is only populated in the Membership returned from a successful
  // long-running operation from CreateMembership or UpdateMembership. It is not
  // populated during normal GetMembership or ListMemberships requests. To get
  // the resource manifest after the initial registration, the caller should
  // make a UpdateMembership call with an empty field mask.
  repeated ResourceManifest connect_resources = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Options for Kubernetes resource generation.
  ResourceOptions resource_options = 4 [(google.api.field_behavior) = OPTIONAL];
}

// ResourceOptions represent options for Kubernetes resource generation.
message ResourceOptions {
  // Optional. The Connect agent version to use for connect_resources. Defaults
  // to the latest GKE Connect version. The version must be a currently
  // supported version, obsolete versions will be rejected.
  string connect_version = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Use `apiextensions/v1beta1` instead of `apiextensions/v1` for
  // CustomResourceDefinition resources.
  // This option should be set for clusters with Kubernetes apiserver versions
  // <1.16.
  bool v1beta1_crd = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Major version of the Kubernetes cluster. This is only used to
  // determine which version to use for the CustomResourceDefinition resources,
  // `apiextensions/v1beta1` or`apiextensions/v1`.
  string k8s_version = 3 [(google.api.field_behavior) = OPTIONAL];
}

// ResourceManifest represents a single Kubernetes resource to be applied to
// the cluster.
message ResourceManifest {
  // YAML manifest of the resource.
  string manifest = 1;

  // Whether the resource provided in the manifest is `cluster_scoped`.
  // If unset, the manifest is assumed to be namespace scoped.
  //
  // This field is used for REST mapping when applying the resource in a
  // cluster.
  bool cluster_scoped = 2;
}

// GkeCluster contains information specific to GKE clusters.
message GkeCluster {
  // Immutable. Self-link of the Google Cloud resource for the GKE cluster. For
  // example:
  //
  // //container.googleapis.com/projects/my-project/locations/us-west1-a/clusters/my-cluster
  //
  // Zonal clusters are also supported.
  string resource_link = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. If cluster_missing is set then it denotes that the GKE cluster
  // no longer exists in the GKE Control Plane.
  bool cluster_missing = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// KubernetesMetadata provides informational metadata for Memberships
// representing Kubernetes clusters.
message KubernetesMetadata {
  // Output only. Kubernetes API server version string as reported by
  // `/version`.
  string kubernetes_api_server_version = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Node providerID as reported by the first node in the list of
  // nodes on the Kubernetes endpoint. On Kubernetes platforms that support
  // zero-node clusters (like GKE-on-GCP), the node_count will be zero and the
  // node_provider_id will be empty.
  string node_provider_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Node count as reported by Kubernetes nodes resources.
  int32 node_count = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. vCPU count as reported by Kubernetes nodes resources.
  int32 vcpu_count = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total memory capacity as reported by the sum of all
  // Kubernetes nodes resources, defined in MB.
  int32 memory_mb = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which these details were last updated. This
  // update_time is different from the Membership-level update_time since
  // EndpointDetails are updated internally for API consumers.
  google.protobuf.Timestamp update_time = 100
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// This field informs Fleet-based applications/services/UIs with the necessary
// information for where each underlying Cluster reports its metrics.
message MonitoringConfig {
  // Immutable. Project used to report Metrics
  string project_id = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Location used to report Metrics
  string location = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Cluster name used to report metrics.
  // For Anthos on VMWare/Baremetal, it would be in format
  // `memberClusters/cluster_name`; And for Anthos on MultiCloud, it would be in
  // format
  // `{azureClusters, awsClusters}/cluster_name`.
  string cluster = 3 [(google.api.field_behavior) = IMMUTABLE];

  // Kubernetes system metrics, if available, are written to this prefix.
  // This defaults to kubernetes.io for GKE, and kubernetes.io/anthos for Anthos
  // eventually. Noted: Anthos MultiCloud will have kubernetes.io prefix today
  // but will migration to be under kubernetes.io/anthos
  string kubernetes_metrics_prefix = 4;

  // Immutable. Cluster hash, this is a unique string generated by google code,
  // which does not contain any PII, which we can use to reference the cluster.
  // This is expected to be created by the monitoring stack and persisted into
  // the Cluster object as well as to GKE-Hub.
  string cluster_hash = 5 [(google.api.field_behavior) = IMMUTABLE];
}

// MembershipState describes the state of a Membership resource.
message MembershipState {
  // Code describes the state of a Membership resource.
  enum Code {
    // The code is not set.
    CODE_UNSPECIFIED = 0;

    // The cluster is being registered.
    CREATING = 1;

    // The cluster is registered.
    READY = 2;

    // The cluster is being unregistered.
    DELETING = 3;

    // The Membership is being updated.
    UPDATING = 4;

    // The Membership is being updated by the Hub Service.
    SERVICE_UPDATING = 5;
  }

  // Output only. The current state of the Membership resource.
  Code code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Authority encodes how Google will recognize identities from this Membership.
// See the workload identity documentation for more details:
// https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity
message Authority {
  // Optional. A JSON Web Token (JWT) issuer URI. `issuer` must start with
  // `https://` and be a valid URL with length <2000 characters.
  //
  // If set, then Google will allow valid OIDC tokens from this issuer to
  // authenticate within the workload_identity_pool. OIDC discovery will be
  // performed on this URI to validate tokens from the issuer.
  //
  // Clearing `issuer` disables Workload Identity. `issuer` cannot be directly
  // modified; it must be cleared (and Workload Identity disabled) before using
  // a new issuer (and re-enabling Workload Identity).
  string issuer = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The name of the workload identity pool in which `issuer` will
  // be recognized.
  //
  // There is a single Workload Identity Pool per Hub that is shared
  // between all Memberships that belong to that Hub. For a Hub hosted in
  // {PROJECT_ID}, the workload pool format is `{PROJECT_ID}.hub.id.goog`,
  // although this is subject to change in newer versions of this API.
  string workload_identity_pool = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An identity provider that reflects the `issuer` in the
  // workload identity pool.
  string identity_provider = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. OIDC verification keys for this Membership in JWKS format (RFC
  // 7517).
  //
  // When this field is set, OIDC discovery will NOT be performed on `issuer`,
  // and instead OIDC tokens will be validated using this field.
  bytes oidc_jwks = 4 [(google.api.field_behavior) = OPTIONAL];
}
