// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkehub.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/gkehub/v1/feature.proto";
import "google/cloud/gkehub/v1/membership.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeHub.V1";
option go_package = "cloud.google.com/go/gkehub/apiv1/gkehubpb;gkehubpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.gkehub.v1";
option php_namespace = "Google\\Cloud\\GkeHub\\V1";
option ruby_package = "Google::Cloud::GkeHub::V1";

// The GKE Hub service handles the registration of many Kubernetes clusters to
// Google Cloud, and the management of multi-cluster features over those
// clusters.
//
// The GKE Hub service operates on the following resources:
//
// * [Membership][google.cloud.gkehub.v1.Membership]
// * [Feature][google.cloud.gkehub.v1.Feature]
//
// GKE Hub is currently available in the global region and all regions in
// https://cloud.google.com/compute/docs/regions-zones. Feature is only
// available in global region while membership is global region and all the
// regions.
//
// **Membership management may be non-trivial:** it is recommended to use one
// of the Google-provided client libraries or tools where possible when working
// with Membership resources.
service GkeHub {
  option (google.api.default_host) = "gkehub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Memberships in a given project and location.
  rpc ListMemberships(ListMembershipsRequest)
      returns (ListMembershipsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/memberships"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists Features in a given project and location.
  rpc ListFeatures(ListFeaturesRequest) returns (ListFeaturesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/features"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a Membership.
  rpc GetMembership(GetMembershipRequest) returns (Membership) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/memberships/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets details of a single Feature.
  rpc GetFeature(GetFeatureRequest) returns (Feature) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/features/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Membership.
  //
  // **This is currently only supported for GKE clusters on Google Cloud**.
  // To register other clusters, follow the instructions at
  // https://cloud.google.com/anthos/multicluster-management/connect/registering-a-cluster.
  rpc CreateMembership(CreateMembershipRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/memberships"
      body: "resource"
    };
    option (google.api.method_signature) = "parent,resource,membership_id";
    option (google.longrunning.operation_info) = {
      response_type: "Membership"
      metadata_type: "OperationMetadata"
    };
  }

  // Adds a new Feature.
  rpc CreateFeature(CreateFeatureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/features"
      body: "resource"
    };
    option (google.api.method_signature) = "parent,resource,feature_id";
    option (google.longrunning.operation_info) = {
      response_type: "Feature"
      metadata_type: "OperationMetadata"
    };
  }

  // Removes a Membership.
  //
  // **This is currently only supported for GKE clusters on Google Cloud**.
  // To unregister other clusters, follow the instructions at
  // https://cloud.google.com/anthos/multicluster-management/connect/unregistering-a-cluster.
  rpc DeleteMembership(DeleteMembershipRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/memberships/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Removes a Feature.
  rpc DeleteFeature(DeleteFeatureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/features/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an existing Membership.
  rpc UpdateMembership(UpdateMembershipRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/memberships/*}"
      body: "resource"
    };
    option (google.api.method_signature) = "name,resource,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Membership"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an existing Feature.
  rpc UpdateFeature(UpdateFeatureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/features/*}"
      body: "resource"
    };
    option (google.api.method_signature) = "name,resource,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Feature"
      metadata_type: "OperationMetadata"
    };
  }

  // Generates the manifest for deployment of the GKE connect agent.
  //
  // **This method is used internally by Google-provided libraries.**
  // Most clients should not need to call this method directly.
  rpc GenerateConnectManifest(GenerateConnectManifestRequest)
      returns (GenerateConnectManifestResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/memberships/*}:generateConnectManifest"
    };
  }
}

// Request message for `GkeHub.ListMemberships` method.
message ListMembershipsRequest {
  // Required. The parent (project and location) where the Memberships will be
  // listed. Specified in the format `projects/*/locations/*`.
  // `projects/*/locations/-` list memberships in all the regions.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Optional. When requesting a 'page' of resources, `page_size` specifies
  // number of resources to return. If unspecified or set to 0, all resources
  // will be returned.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Token returned by previous call to `ListMemberships` which
  // specifies the position in the list from where to continue listing the
  // resources.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Lists Memberships that match the filter expression, following the
  // syntax outlined in https://google.aip.dev/160.
  //
  // Examples:
  //
  //   - Name is `bar` in project `foo-proj` and location `global`:
  //
  //       name = "projects/foo-proj/locations/global/membership/bar"
  //
  //   - Memberships that have a label called `foo`:
  //
  //       labels.foo:*
  //
  //   - Memberships that have a label called `foo` whose value is `bar`:
  //
  //       labels.foo = bar
  //
  //   - Memberships in the CREATING state:
  //
  //       state = CREATING
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. One or more fields to compare and use to sort the output.
  // See https://google.aip.dev/132#ordering.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `GkeHub.ListMemberships` method.
message ListMembershipsResponse {
  // The list of matching Memberships.
  repeated Membership resources = 1;

  // A token to request the next page of resources from the
  // `ListMemberships` method. The value of an empty string means that
  // there are no more resources to return.
  string next_page_token = 2;

  // List of locations that could not be reached while fetching this list.
  repeated string unreachable = 3;
}

// Request message for `GkeHub.GetMembership` method.
message GetMembershipRequest {
  // Required. The Membership resource name in the format
  // `projects/*/locations/*/memberships/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Membership"
    }
  ];
}

// Request message for the `GkeHub.CreateMembership` method.
message CreateMembershipRequest {
  // Required. The parent (project and location) where the Memberships will be
  // created. Specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Required. Client chosen ID for the membership. `membership_id` must be a
  // valid RFC 1123 compliant DNS label:
  //
  //   1. At most 63 characters in length
  //   2. It must consist of lower case alphanumeric characters or `-`
  //   3. It must start and end with an alphanumeric character
  //
  // Which can be expressed as the regex: `[a-z0-9]([-a-z0-9]*[a-z0-9])?`,
  // with a maximum length of 63 characters.
  string membership_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The membership to create.
  Membership resource = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for `GkeHub.DeleteMembership` method.
message DeleteMembershipRequest {
  // Required. The Membership resource name in the format
  // `projects/*/locations/*/memberships/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, any subresource from this Membership will also be
  // deleted. Otherwise, the request will only work if the Membership has no
  // subresource.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for `GkeHub.UpdateMembership` method.
message UpdateMembershipRequest {
  // Required. The Membership resource name in the format
  // `projects/*/locations/*/memberships/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Required. Mask of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. Only fields specified in update_mask are updated.
  // If you specify a field in the update_mask but don't specify its value here
  // that field will be deleted.
  // If you are updating a map field, set the value of a key to null or empty
  // string to delete the key from the map. It's not possible to update a key's
  // value to the empty string.
  // If you specify the update_mask to be a special path "*", fully replaces all
  // user-modifiable fields to match `resource`.
  Membership resource = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for `GkeHub.GenerateConnectManifest`
// method.
// .
message GenerateConnectManifestRequest {
  // Required. The Membership resource name the Agent will associate with, in
  // the format `projects/*/locations/*/memberships/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Optional. Namespace for GKE Connect agent resources. Defaults to
  // `gke-connect`.
  //
  // The Connect Agent is authorized automatically when run in the default
  // namespace. Otherwise, explicit authorization must be granted with an
  // additional IAM binding.
  string namespace = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. URI of a proxy if connectivity from the agent to
  // gkeconnect.googleapis.com requires the use of a proxy. Format must be in
  // the form `http(s)://{proxy_address}`, depending on the HTTP/HTTPS protocol
  // supported by the proxy. This will direct the connect agent's outbound
  // traffic through a HTTP(S) proxy.
  bytes proxy = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Connect agent version to use. Defaults to the most current
  // version.
  string version = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true, generate the resources for upgrade only. Some resources
  // generated only for installation (e.g. secrets) will be excluded.
  bool is_upgrade = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The registry to fetch the connect agent image from. Defaults to
  // gcr.io/gkeconnect.
  string registry = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The image pull secret content for the registry, if not public.
  bytes image_pull_secret_content = 7 [(google.api.field_behavior) = OPTIONAL];
}

// GenerateConnectManifestResponse contains manifest information for
// installing/upgrading a Connect agent.
message GenerateConnectManifestResponse {
  // The ordered list of Kubernetes resources that need to be applied to the
  // cluster for GKE Connect agent installation/upgrade.
  repeated ConnectAgentResource manifest = 1;
}

// ConnectAgentResource represents a Kubernetes resource manifest for Connect
// Agent deployment.
message ConnectAgentResource {
  // Kubernetes type of the resource.
  TypeMeta type = 1;

  // YAML manifest of the resource.
  string manifest = 2;
}

// TypeMeta is the type information needed for content unmarshalling of
// Kubernetes resources in the manifest.
message TypeMeta {
  // Kind of the resource (e.g. Deployment).
  string kind = 1;

  // APIVersion of the resource (e.g. v1).
  string api_version = 2;
}

// Request message for `GkeHub.ListFeatures` method.
message ListFeaturesRequest {
  // Required. The parent (project and location) where the Features will be listed.
  // Specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.resource_reference) = {
      child_type: "gkehub.googleapis.com/Feature"
    }
  ];

  // When requesting a 'page' of resources, `page_size` specifies number of
  // resources to return. If unspecified or set to 0, all resources will
  // be returned.
  int32 page_size = 2;

  // Token returned by previous call to `ListFeatures` which
  // specifies the position in the list from where to continue listing the
  // resources.
  string page_token = 3;

  // Lists Features that match the filter expression, following the syntax
  // outlined in https://google.aip.dev/160.
  //
  // Examples:
  //
  //   - Feature with the name "servicemesh" in project "foo-proj":
  //
  //       name = "projects/foo-proj/locations/global/features/servicemesh"
  //
  //   - Features that have a label called `foo`:
  //
  //       labels.foo:*
  //
  //   - Features that have a label called `foo` whose value is `bar`:
  //
  //       labels.foo = bar
  string filter = 4;

  // One or more fields to compare and use to sort the output.
  // See https://google.aip.dev/132#ordering.
  string order_by = 5;
}

// Response message for the `GkeHub.ListFeatures` method.
message ListFeaturesResponse {
  // The list of matching Features
  repeated Feature resources = 1;

  // A token to request the next page of resources from the
  // `ListFeatures` method. The value of an empty string means
  // that there are no more resources to return.
  string next_page_token = 2;
}

// Request message for `GkeHub.GetFeature` method.
message GetFeatureRequest {
  // Required. The Feature resource name in the format
  // `projects/*/locations/*/features/*`
  string name = 1 [
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Feature"
    }
  ];
}

// Request message for the `GkeHub.CreateFeature` method.
message CreateFeatureRequest {
  // Required. The parent (project and location) where the Feature will be created.
  // Specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.resource_reference) = {
      child_type: "gkehub.googleapis.com/Feature"
    }
  ];

  // The ID of the feature to create.
  string feature_id = 2;

  // The Feature resource to create.
  Feature resource = 3;

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

// Request message for `GkeHub.DeleteFeature` method.
message DeleteFeatureRequest {
  // Required. The Feature resource name in the format
  // `projects/*/locations/*/features/*`.
  string name = 1 [
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Feature"
    }
  ];

  // If set to true, the delete will ignore any outstanding resources for
  // this Feature (that is, `FeatureState.has_resources` is set to true). These
  // resources will NOT be cleaned up or modified in any way.
  bool force = 2;

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for `GkeHub.UpdateFeature` method.
message UpdateFeatureRequest {
  // Required. The Feature resource name in the format
  // `projects/*/locations/*/features/*`.
  string name = 1 [
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Feature"
    }
  ];

  // Mask of fields to update.
  google.protobuf.FieldMask update_mask = 2;

  // Only fields specified in update_mask are updated.
  // If you specify a field in the update_mask but don't specify its value here
  // that field will be deleted.
  // If you are updating a map field, set the value of a key to null or empty
  // string to delete the key from the map. It's not possible to update a key's
  // value to the empty string.
  // If you specify the update_mask to be a special path "*", fully replaces all
  // user-modifiable fields to match `resource`.
  Feature resource = 3;

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_detail = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool cancel_requested = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
