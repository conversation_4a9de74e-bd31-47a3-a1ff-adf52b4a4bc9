// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkehub.servicemesh.v1beta;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.GkeHub.ServiceMesh.V1Beta";
option go_package = "cloud.google.com/go/gkehub/servicemesh/apiv1beta/servicemeshpb;servicemeshpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceMeshProto";
option java_package = "com.google.cloud.gkehub.servicemesh.v1beta";
option php_namespace = "Google\\Cloud\\GkeHub\\ServiceMesh\\V1beta";
option ruby_package = "Google::Cloud::GkeHub::ServiceMesh::V1beta";

// **Service Mesh**: Spec for a single Membership for the servicemesh feature
message MembershipSpec {
  // Whether to automatically manage Service Mesh control planes.
  enum ControlPlaneManagement {
    // Unspecified
    CONTROL_PLANE_MANAGEMENT_UNSPECIFIED = 0;

    // Google should provision a control plane revision and make it available in
    // the cluster. Google will enroll this revision in a release channel and
    // keep it up to date. The control plane revision may be a managed service,
    // or a managed install.
    AUTOMATIC = 1;

    // User will manually configure the control plane (e.g. via CLI, or via the
    // ControlPlaneRevision KRM API)
    MANUAL = 2;
  }

  // Whether to automatically manage Service Mesh.
  enum Management {
    // Unspecified
    MANAGEMENT_UNSPECIFIED = 0;

    // Google should manage my Service Mesh for the cluster.
    MANAGEMENT_AUTOMATIC = 1;

    // User will manually configure their service mesh components.
    MANAGEMENT_MANUAL = 2;
  }

  // Deprecated: use `management` instead
  // Enables automatic control plane management.
  ControlPlaneManagement control_plane = 1 [deprecated = true];

  // Enables automatic Service Mesh management.
  Management management = 4;
}

// **Service Mesh**: State for a single Membership, as analyzed by the Service
// Mesh Hub Controller.
message MembershipState {
  // Status of control plane management.
  message ControlPlaneManagement {
    // Implementation of managed control plane.
    enum Implementation {
      // Unspecified
      IMPLEMENTATION_UNSPECIFIED = 0;

      // A Google build of istiod is used for the managed control plane.
      ISTIOD = 1;

      // Traffic director is used for the managed control plane.
      TRAFFIC_DIRECTOR = 2;

      // The control plane implementation is being updated.
      UPDATING = 3;
    }

    // Explanation of state.
    repeated StatusDetails details = 2;

    // LifecycleState of control plane management.
    LifecycleState state = 3;

    // Output only. Implementation of managed control plane.
    Implementation implementation = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Status of data plane management. Only reported per-member.
  message DataPlaneManagement {
    // Lifecycle status of data plane management.
    LifecycleState state = 1;

    // Explanation of the status.
    repeated StatusDetails details = 2;
  }

  // Condition being reported.
  message Condition {
    // Unique identifier of the condition which describes the condition
    // recognizable to the user.
    enum Code {
      // Default Unspecified code
      CODE_UNSPECIFIED = 0;

      // Mesh IAM permission denied error code
      MESH_IAM_PERMISSION_DENIED = 100;

      // CNI config unsupported error code
      CNI_CONFIG_UNSUPPORTED = 201;

      // GKE sandbox unsupported error code
      GKE_SANDBOX_UNSUPPORTED = 202;

      // Nodepool workload identity federation required error code
      NODEPOOL_WORKLOAD_IDENTITY_FEDERATION_REQUIRED = 203;

      // CNI installation failed error code
      CNI_INSTALLATION_FAILED = 204;

      // CNI pod unschedulable error code
      CNI_POD_UNSCHEDULABLE = 205;

      // Multiple control planes unsupported error code
      UNSUPPORTED_MULTIPLE_CONTROL_PLANES = 301;

      // VPC-SC GA is supported for this control plane.
      VPCSC_GA_SUPPORTED = 302;

      // Configuration (Istio/k8s resources) failed to apply due to internal
      // error.
      CONFIG_APPLY_INTERNAL_ERROR = 401;

      // Configuration failed to be applied due to being invalid.
      CONFIG_VALIDATION_ERROR = 402;

      // Encountered configuration(s) with possible unintended behavior or
      // invalid configuration. These configs may not have been applied.
      CONFIG_VALIDATION_WARNING = 403;

      // BackendService quota exceeded error code.
      QUOTA_EXCEEDED_BACKEND_SERVICES = 404;

      // HealthCheck quota exceeded error code.
      QUOTA_EXCEEDED_HEALTH_CHECKS = 405;

      // HTTPRoute quota exceeded error code.
      QUOTA_EXCEEDED_HTTP_ROUTES = 406;

      // TCPRoute quota exceeded error code.
      QUOTA_EXCEEDED_TCP_ROUTES = 407;

      // TLS routes quota exceeded error code.
      QUOTA_EXCEEDED_TLS_ROUTES = 408;

      // TrafficPolicy quota exceeded error code.
      QUOTA_EXCEEDED_TRAFFIC_POLICIES = 409;

      // EndpointPolicy quota exceeded error code.
      QUOTA_EXCEEDED_ENDPOINT_POLICIES = 410;

      // Gateway quota exceeded error code.
      QUOTA_EXCEEDED_GATEWAYS = 411;

      // Mesh quota exceeded error code.
      QUOTA_EXCEEDED_MESHES = 412;

      // ServerTLSPolicy quota exceeded error code.
      QUOTA_EXCEEDED_SERVER_TLS_POLICIES = 413;

      // ClientTLSPolicy quota exceeded error code.
      QUOTA_EXCEEDED_CLIENT_TLS_POLICIES = 414;

      // ServiceLBPolicy quota exceeded error code.
      QUOTA_EXCEEDED_SERVICE_LB_POLICIES = 415;

      // HTTPFilter quota exceeded error code.
      QUOTA_EXCEEDED_HTTP_FILTERS = 416;

      // TCPFilter quota exceeded error code.
      QUOTA_EXCEEDED_TCP_FILTERS = 417;

      // NetworkEndpointGroup quota exceeded error code.
      QUOTA_EXCEEDED_NETWORK_ENDPOINT_GROUPS = 418;
    }

    // Severity level of the reported condition
    enum Severity {
      // Unspecified severity
      SEVERITY_UNSPECIFIED = 0;

      // Indicates an issue that prevents the mesh from operating correctly
      ERROR = 1;

      // Indicates a setting is likely wrong, but the mesh is still able to
      // operate
      WARNING = 2;

      // An informational message, not requiring any action
      INFO = 3;
    }

    // Unique identifier of the condition which describes the condition
    // recognizable to the user.
    Code code = 1;

    // Links contains actionable information.
    string documentation_link = 2;

    // A short summary about the issue.
    string details = 3;

    // Severity level of the condition.
    Severity severity = 4;
  }

  // Lifecycle state of Service Mesh components.
  enum LifecycleState {
    // Unspecified
    LIFECYCLE_STATE_UNSPECIFIED = 0;

    // DISABLED means that the component is not enabled.
    DISABLED = 1;

    // FAILED_PRECONDITION means that provisioning cannot proceed because of
    // some characteristic of the member cluster.
    FAILED_PRECONDITION = 2;

    // PROVISIONING means that provisioning is in progress.
    PROVISIONING = 3;

    // ACTIVE means that the component is ready for use.
    ACTIVE = 4;

    // STALLED means that provisioning could not be done.
    STALLED = 5;

    // NEEDS_ATTENTION means that the component is ready, but some user
    // intervention is required. (For example that the user should migrate
    // workloads to a new control plane revision.)
    NEEDS_ATTENTION = 6;

    // DEGRADED means that the component is ready, but operating in a
    // degraded state.
    DEGRADED = 7;
  }

  // Output only. Status of control plane management
  ControlPlaneManagement control_plane_management = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Status of data plane management.
  DataPlaneManagement data_plane_management = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of conditions reported for this membership.
  repeated Condition conditions = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Structured and human-readable details for a status.
message StatusDetails {
  // A machine-readable code that further describes a broad status.
  string code = 1;

  // Human-readable explanation of code.
  string details = 2;
}
