// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkehub.cloudauditlogging.v1alpha;


option csharp_namespace = "Google.Cloud.GkeHub.CloudAuditLogging.V1Alpha";
option go_package = "cloud.google.com/go/gkehub/cloudauditlogging/apiv1alpha/cloudauditloggingpb;cloudauditloggingpb";
option java_multiple_files = true;
option java_outer_classname = "CloudAuditLoggingProto";
option java_package = "com.google.cloud.gkehub.cloudauditlogging.v1alpha";
option php_namespace = "Google\\Cloud\\GkeHub\\CloudAuditLogging\\V1alpha";
option ruby_package = "Google::Cloud::GkeHub::CloudAuditLogging::V1alpha";

// **Cloud Audit Logging**: Spec for Audit Logging Allowlisting.
message FeatureSpec {
  // Service account that should be allowlisted to send the audit logs; eg
  // <EMAIL>. These accounts must
  // already exist, but do not need to have any permissions granted to them.
  // The customer's entitlements will be checked prior to allowlisting (i.e.
  // the customer must be an Anthos customer.)
  repeated string allowlisted_service_accounts = 1;
}
