# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "cloudauditlogging_proto",
    srcs = [
        "cloudauditlogging.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "cloudauditlogging_java_proto",
    deps = [":cloudauditlogging_proto"],
)

java_grpc_library(
    name = "cloudauditlogging_java_grpc",
    srcs = [":cloudauditlogging_proto"],
    deps = [":cloudauditlogging_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "cloudauditlogging_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/gkehub/cloudauditlogging/apiv1alpha/cloudauditloggingpb",
    protos = [":cloudauditlogging_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_library",
)

py_gapic_library(
    name = "cloudauditlogging_py_gapic",
    srcs = [":cloudauditlogging_proto"],
    rest_numeric_enums = False,
    transport = "grpc",
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "cloudauditlogging_php_proto",
    deps = [":cloudauditlogging_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "cloudauditlogging_ruby_proto",
    deps = [":cloudauditlogging_proto"],
)

ruby_grpc_library(
    name = "cloudauditlogging_ruby_grpc",
    srcs = [":cloudauditlogging_proto"],
    deps = [":cloudauditlogging_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "cloudauditlogging_csharp_proto",
    deps = [":cloudauditlogging_proto"],
)

csharp_grpc_library(
    name = "cloudauditlogging_csharp_grpc",
    srcs = [":cloudauditlogging_proto"],
    deps = [":cloudauditlogging_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
