// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkehub.metering.v1alpha;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeHub.Metering.V1Alpha";
option go_package = "cloud.google.com/go/gkehub/metering/apiv1alpha/meteringpb;meteringpb";
option java_multiple_files = true;
option java_outer_classname = "MeteringProto";
option java_package = "com.google.cloud.gkehub.metering.v1alpha";
option php_namespace = "Google\\Cloud\\GkeHub\\Metering\\V1alpha";
option ruby_package = "Google::Cloud::GkeHub::Metering::V1alpha";

// **Metering**: Per-Membership Feature State.
message MembershipState {
  // The time stamp of the most recent measurement of the number of vCPUs
  // in the cluster.
  google.protobuf.Timestamp last_measurement_time = 1;

  // The vCPUs capacity in the cluster according to the most recent
  // measurement (1/1000 precision).
  float precise_last_measured_cluster_vcpu_capacity = 3;
}
