// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkehub.multiclusteringress.v1alpha;


option csharp_namespace = "Google.Cloud.GkeHub.MultiClusterIngress.V1Alpha";
option go_package = "cloud.google.com/go/gkehub/multiclusteringress/apiv1alpha/multiclusteringresspb;multiclusteringresspb";
option java_multiple_files = true;
option java_outer_classname = "MultiClusterIngressProto";
option java_package = "com.google.cloud.gkehub.multiclusteringress.v1alpha";
option php_namespace = "Google\\Cloud\\GkeHub\\MultiClusterIngress\\V1alpha";
option ruby_package = "Google::Cloud::GkeHub::MultiClusterIngress::V1alpha";

// Billing identifies which billing structure the customer is using.
enum Billing {
  // Unknown
  BILLING_UNSPECIFIED = 0;

  // User pays a fee per-endpoint.
  PAY_AS_YOU_GO = 1;

  // User is paying for Anthos as a whole.
  ANTHOS_LICENSE = 2;
}

// **Multi-cluster Ingress**: The configuration for the MultiClusterIngress
// feature.
message FeatureSpec {
  // Fully-qualified Membership name which hosts the MultiClusterIngress CRD.
  // Example: `projects/foo-proj/locations/global/memberships/bar`
  string config_membership = 1;

  // Customer's billing structure
  Billing billing = 2;
}
