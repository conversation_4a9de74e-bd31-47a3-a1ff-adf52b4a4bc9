// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkehub.configmanagement.v1alpha;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeHub.ConfigManagement.V1Alpha";
option go_package = "cloud.google.com/go/gkehub/configmanagement/apiv1alpha/configmanagementpb;configmanagementpb";
option java_multiple_files = true;
option java_outer_classname = "ConfigManagementProto";
option java_package = "com.google.cloud.gkehub.configmanagement.v1alpha";
option php_namespace = "Google\\Cloud\\GkeHub\\ConfigManagement\\V1alpha";
option ruby_package = "Google::Cloud::GkeHub::ConfigManagement::V1alpha";

// Enum representing the state of an ACM's deployment on a cluster
enum DeploymentState {
  // Deployment's state cannot be determined
  DEPLOYMENT_STATE_UNSPECIFIED = 0;

  // Deployment is not installed
  NOT_INSTALLED = 1;

  // Deployment is installed
  INSTALLED = 2;

  // Deployment was attempted to be installed, but has errors
  ERROR = 3;
}

// **Anthos Config Management**: State for a single cluster.
message MembershipState {
  // The user-defined name for the cluster used by ClusterSelectors to group
  // clusters together. This should match Membership's membership_name,
  // unless the user installed ACM on the cluster manually prior to enabling
  // the ACM hub feature.
  // Unique within a Anthos Config Management installation.
  string cluster_name = 1;

  // Membership configuration in the cluster. This represents the actual state
  // in the cluster, while the MembershipSpec in the FeatureSpec represents
  // the intended state
  MembershipSpec membership_spec = 2;

  // Current install status of ACM's Operator
  OperatorState operator_state = 3;

  // Current sync status
  ConfigSyncState config_sync_state = 4;

  // PolicyController status
  PolicyControllerState policy_controller_state = 5;

  // Binauthz status
  BinauthzState binauthz_state = 6;

  // Hierarchy Controller status
  HierarchyControllerState hierarchy_controller_state = 7;
}

// **Anthos Config Management**: Configuration for a single cluster.
// Intended to parallel the ConfigManagement CR.
message MembershipSpec {
  // Config Sync configuration for the cluster.
  ConfigSync config_sync = 1;

  // Policy Controller configuration for the cluster.
  PolicyController policy_controller = 2;

  // Binauthz conifguration for the cluster.
  BinauthzConfig binauthz = 3;

  // Hierarchy Controller configuration for the cluster.
  HierarchyControllerConfig hierarchy_controller = 4;

  // Version of ACM installed.
  string version = 10;
}

// Configuration for Config Sync
message ConfigSync {
  // Git repo configuration for the cluster.
  GitConfig git = 7;

  // Specifies whether the Config Sync Repo is
  // in “hierarchical” or “unstructured” mode.
  string source_format = 8;
}

// Git repo configuration for a single cluster.
message GitConfig {
  // The URL of the Git repository to use as the source of truth.
  string sync_repo = 1;

  // The branch of the repository to sync from. Default: master.
  string sync_branch = 2;

  // The path within the Git repository that represents the top level of the
  // repo to sync. Default: the root directory of the repository.
  string policy_dir = 3;

  // Period in seconds between consecutive syncs. Default: 15.
  int64 sync_wait_secs = 4;

  // Git revision (tag or hash) to check out. Default HEAD.
  string sync_rev = 5;

  // Type of secret configured for access to the Git repo.
  string secret_type = 6;

  // URL for the HTTPS proxy to be used when communicating with the Git repo.
  string https_proxy = 7;

  // The GCP Service Account Email used for auth when secret_type is
  // gcpServiceAccount.
  string gcp_service_account_email = 8;
}

// Configuration for Policy Controller
message PolicyController {
  // Enables the installation of Policy Controller.
  // If false, the rest of PolicyController fields take no
  // effect.
  bool enabled = 1;

  // Installs the default template library along with Policy Controller.
  optional bool template_library_installed = 2;

  // Sets the interval for Policy Controller Audit Scans (in seconds).
  // When set to 0, this disables audit functionality altogether.
  optional int64 audit_interval_seconds = 3;

  // The set of namespaces that are excluded from Policy Controller checks.
  // Namespaces do not need to currently exist on the cluster.
  repeated string exemptable_namespaces = 4;

  // Enables the ability to use Constraint Templates that reference to objects
  // other than the object currently being evaluated.
  bool referential_rules_enabled = 5;

  // Logs all denies and dry run failures.
  bool log_denies_enabled = 6;

  // Enable users to try out mutation for PolicyController.
  bool mutation_enabled = 7;
}

// Configuration for Binauthz
message BinauthzConfig {
  // Whether binauthz is enabled in this cluster.
  bool enabled = 1;
}

// Configuration for Hierarchy Controller
message HierarchyControllerConfig {
  // Whether Hierarchy Controller is enabled in this cluster.
  bool enabled = 1;

  // Whether pod tree labels are enabled in this cluster.
  bool enable_pod_tree_labels = 2;

  // Whether hierarchical resource quota is enabled in this cluster.
  bool enable_hierarchical_resource_quota = 3;
}

// Deployment state for Hierarchy Controller
message HierarchyControllerDeploymentState {
  // The deployment state for open source HNC (e.g. v0.7.0-hc.0)
  DeploymentState hnc = 1;

  // The deployment state for Hierarchy Controller extension (e.g. v0.7.0-hc.1)
  DeploymentState extension = 2;
}

// Version for Hierarchy Controller
message HierarchyControllerVersion {
  // Version for open source HNC
  string hnc = 1;

  // Version for Hierarchy Controller extension
  string extension = 2;
}

// State for Hierarchy Controller
message HierarchyControllerState {
  // The version for Hierarchy Controller
  HierarchyControllerVersion version = 1;

  // The deployment state for Hierarchy Controller
  HierarchyControllerDeploymentState state = 2;
}

// State information for an ACM's Operator
message OperatorState {
  // The semenatic version number of the operator
  string version = 1;

  // The state of the Operator's deployment
  DeploymentState deployment_state = 2;

  // Install errors.
  repeated InstallError errors = 3;
}

// Errors pertaining to the installation of ACM
message InstallError {
  // A string representing the user facing error message
  string error_message = 1;
}

// State information for ConfigSync
message ConfigSyncState {
  // The version of ConfigSync deployed
  ConfigSyncVersion version = 1;

  // Information about the deployment of ConfigSync, including the version
  // of the various Pods deployed
  ConfigSyncDeploymentState deployment_state = 2;

  // The state of ConfigSync's process to sync configs to a cluster
  SyncState sync_state = 3;
}

// Specific versioning information pertaining to ConfigSync's Pods
message ConfigSyncVersion {
  // Version of the deployed importer pod
  string importer = 1;

  // Version of the deployed syncer pod
  string syncer = 2;

  // Version of the deployed git-sync pod
  string git_sync = 3;

  // Version of the deployed monitor pod
  string monitor = 4;

  // Version of the deployed reconciler-manager pod
  string reconciler_manager = 5;

  // Version of the deployed reconciler container in root-reconciler pod
  string root_reconciler = 6;
}

// The state of ConfigSync's deployment on a cluster
message ConfigSyncDeploymentState {
  // Deployment state of the importer pod
  DeploymentState importer = 1;

  // Deployment state of the syncer pod
  DeploymentState syncer = 2;

  // Deployment state of the git-sync pod
  DeploymentState git_sync = 3;

  // Deployment state of the monitor pod
  DeploymentState monitor = 4;

  // Deployment state of reconciler-manager pod
  DeploymentState reconciler_manager = 5;

  // Deployment state of root-reconciler
  DeploymentState root_reconciler = 6;
}

// State indicating an ACM's progress syncing configurations to a cluster
message SyncState {
  // An enum representing an ACM's status syncing configs to a cluster
  enum SyncCode {
    // ACM cannot determine a sync code
    SYNC_CODE_UNSPECIFIED = 0;

    // ACM successfully synced the git Repo with the cluster
    SYNCED = 1;

    // ACM is in the progress of syncing a new change
    PENDING = 2;

    // Indicates an error configuring ACM, and user action is required
    ERROR = 3;

    // ACM has been installed (operator manifest deployed),
    // but not configured.
    NOT_CONFIGURED = 4;

    // ACM has not been installed (no operator pod found)
    NOT_INSTALLED = 5;

    // Error authorizing with the cluster
    UNAUTHORIZED = 6;

    // Cluster could not be reached
    UNREACHABLE = 7;
  }

  // Token indicating the state of the repo.
  string source_token = 1;

  // Token indicating the state of the importer.
  string import_token = 2;

  // Token indicating the state of the syncer.
  string sync_token = 3;

  // Deprecated: use last_sync_time instead.
  // Timestamp of when ACM last successfully synced the repo
  // The time format is specified in https://golang.org/pkg/time/#Time.String
  string last_sync = 4 [deprecated = true];

  // Timestamp type of when ACM last successfully synced the repo
  google.protobuf.Timestamp last_sync_time = 7;

  // Sync status code
  SyncCode code = 5;

  // A list of errors resulting from problematic configs.
  // This list will be truncated after 100 errors, although it is
  // unlikely for that many errors to simultaneously exist.
  repeated SyncError errors = 6;
}

// An ACM created error representing a problem syncing configurations
message SyncError {
  // An ACM defined error code
  string code = 1;

  // A description of the error
  string error_message = 2;

  // A list of config(s) associated with the error, if any
  repeated ErrorResource error_resources = 3;
}

// Model for a config file in the git repo with an associated Sync error
message ErrorResource {
  // Path in the git repo of the erroneous config
  string source_path = 1;

  // Metadata name of the resource that is causing an error
  string resource_name = 2;

  // Namespace of the resource that is causing an error
  string resource_namespace = 3;

  // Group/version/kind of the resource that is causing an error
  GroupVersionKind resource_gvk = 4;
}

// A Kubernetes object's GVK
message GroupVersionKind {
  // Kubernetes Group
  string group = 1;

  // Kubernetes Version
  string version = 2;

  // Kubernetes Kind
  string kind = 3;
}

// State for PolicyControllerState.
message PolicyControllerState {
  // The version of Gatekeeper Policy Controller deployed.
  PolicyControllerVersion version = 1;

  // The state about the policy controller installation.
  GatekeeperDeploymentState deployment_state = 2;
}

// The build version of Gatekeeper Policy Controller is using.
message PolicyControllerVersion {
  // The gatekeeper image tag that is composed of ACM version, git tag, build
  // number.
  string version = 1;
}

// State for Binauthz
message BinauthzState {
  // The state of the binauthz webhook.
  DeploymentState webhook = 1;

  // The version of binauthz that is installed.
  BinauthzVersion version = 2;
}

// The version of binauthz.
message BinauthzVersion {
  // The version of the binauthz webhook.
  string webhook_version = 1;
}

// State of Policy Controller installation.
message GatekeeperDeploymentState {
  // Status of gatekeeper-controller-manager pod.
  DeploymentState gatekeeper_controller_manager_state = 1;

  // Status of gatekeeper-audit deployment.
  DeploymentState gatekeeper_audit = 2;

  // Status of the pod serving the mutation webhook.
  DeploymentState gatekeeper_mutation = 3;
}
