// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkehub.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/gkehub/policycontroller/v1beta/policycontroller.proto";
import "google/cloud/gkehub/servicemesh/v1beta/servicemesh.proto";
import "google/cloud/gkehub/v1beta/configmanagement/configmanagement.proto";
import "google/cloud/gkehub/v1beta/metering/metering.proto";
import "google/cloud/gkehub/v1beta/multiclusteringress/multiclusteringress.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeHub.V1Beta";
option go_package = "cloud.google.com/go/gkehub/apiv1beta/gkehubpb;gkehubpb";
option java_multiple_files = true;
option java_outer_classname = "FeatureProto";
option java_package = "com.google.cloud.gkehub.v1beta";
option php_namespace = "Google\\Cloud\\GkeHub\\V1beta";
option ruby_package = "Google::Cloud::GkeHub::V1beta";

// Feature represents the settings and status of any Hub Feature.
message Feature {
  option (google.api.resource) = {
    type: "gkehub.googleapis.com/Feature"
    pattern: "projects/{project}/locations/{location}/features/{feature}"
  };

  // Output only. The full, unique name of this Feature resource in the format
  // `projects/*/locations/*/features/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels for this Feature.
  map<string, string> labels = 2;

  // Output only. State of the Feature resource itself.
  FeatureResourceState resource_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Hub-wide Feature configuration. If this Feature does not support
  // any Hub-wide configuration, this field may be unused.
  CommonFeatureSpec spec = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Membership-specific configuration for this Feature. If this
  // Feature does not support any per-Membership configuration, this field may
  // be unused.
  //
  // The keys indicate which Membership the configuration is for, in the form:
  //
  // `projects/{p}/locations/{l}/memberships/{m}`
  //
  // Where {p} is the project, {l} is a valid location and {m} is a valid
  // Membership in this project at that location. {p} WILL match the Feature's
  // project.
  //
  // {p} will always be returned as the project number, but the project ID is
  // also accepted during input. If the same Membership is specified in the map
  // twice (using the project ID form, and the project number form), exactly
  // ONE of the entries will be saved, with no guarantees as to which. For this
  // reason, it is recommended the same format be used for all entries when
  // mutating a Feature.
  map<string, MembershipFeatureSpec> membership_specs = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The Hub-wide Feature state.
  CommonFeatureState state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Membership-specific Feature status. If this Feature does
  // report any per-Membership status, this field may be unused.
  //
  // The keys indicate which Membership the state is for, in the form:
  //
  // `projects/{p}/locations/{l}/memberships/{m}`
  //
  // Where {p} is the project number, {l} is a valid location and {m} is a valid
  // Membership in this project at that location. {p} MUST match the Feature's
  // project number.
  map<string, MembershipFeatureState> membership_states = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the Feature resource was created.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the Feature resource was last updated.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the Feature resource was deleted.
  google.protobuf.Timestamp delete_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// FeatureResourceState describes the state of a Feature *resource* in the
// GkeHub API. See `FeatureState` for the "running state" of the Feature in the
// Hub and across Memberships.
message FeatureResourceState {
  // State describes the lifecycle status of a Feature.
  enum State {
    // State is unknown or not set.
    STATE_UNSPECIFIED = 0;

    // The Feature is being enabled, and the Feature resource is being created.
    // Once complete, the corresponding Feature will be enabled in this Hub.
    ENABLING = 1;

    // The Feature is enabled in this Hub, and the Feature resource is fully
    // available.
    ACTIVE = 2;

    // The Feature is being disabled in this Hub, and the Feature resource
    // is being deleted.
    DISABLING = 3;

    // The Feature resource is being updated.
    UPDATING = 4;

    // The Feature resource is being updated by the Hub Service.
    SERVICE_UPDATING = 5;
  }

  // The current state of the Feature resource in the Hub API.
  State state = 1;
}

// FeatureState describes the high-level state of a Feature. It may be used to
// describe a Feature's state at the environ-level, or per-membershop, depending
// on the context.
message FeatureState {
  // Code represents a machine-readable, high-level status of the Feature.
  enum Code {
    // Unknown or not set.
    CODE_UNSPECIFIED = 0;

    // The Feature is operating normally.
    OK = 1;

    // The Feature has encountered an issue, and is operating in a degraded
    // state. The Feature may need intervention to return to normal operation.
    // See the description and any associated Feature-specific details for more
    // information.
    WARNING = 2;

    // The Feature is not operating or is in a severely degraded state.
    // The Feature may need intervention to return to normal operation.
    // See the description and any associated Feature-specific details for more
    // information.
    ERROR = 3;
  }

  // The high-level, machine-readable status of this Feature.
  Code code = 1;

  // A human-readable description of the current status.
  string description = 2;

  // The time this status and any related Feature-specific details were updated.
  google.protobuf.Timestamp update_time = 3;
}

// CommonFeatureSpec contains Hub-wide configuration information
message CommonFeatureSpec {
  oneof feature_spec {
    // Multicluster Ingress-specific spec.
    google.cloud.gkehub.multiclusteringress.v1beta.FeatureSpec
        multiclusteringress = 102;
  }
}

// CommonFeatureState contains Hub-wide Feature status information.
message CommonFeatureState {
  // Output only. The "running state" of the Feature in this Hub.
  FeatureState state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// MembershipFeatureSpec contains configuration information for a single
// Membership.
// NOTE: Please use snake case in your feature name.
message MembershipFeatureSpec {
  oneof feature_spec {
    // Config Management-specific spec.
    google.cloud.gkehub.configmanagement.v1beta.MembershipSpec
        configmanagement = 106;

    // Anthos Service Mesh-specific spec
    google.cloud.gkehub.servicemesh.v1beta.MembershipSpec mesh = 116;

    // Policy Controller spec.
    google.cloud.gkehub.policycontroller.v1beta.MembershipSpec
        policycontroller = 118;
  }
}

// MembershipFeatureState contains Feature status information for a single
// Membership.
message MembershipFeatureState {
  oneof feature_state {
    // Service Mesh-specific state.
    google.cloud.gkehub.servicemesh.v1beta.MembershipState servicemesh = 100;

    // Metering-specific state.
    google.cloud.gkehub.metering.v1beta.MembershipState metering = 104;

    // Config Management-specific state.
    google.cloud.gkehub.configmanagement.v1beta.MembershipState
        configmanagement = 106;

    // Policycontroller-specific state.
    google.cloud.gkehub.policycontroller.v1beta.MembershipState
        policycontroller = 116;
  }

  // The high-level state of this Feature for a single membership.
  FeatureState state = 1;
}
