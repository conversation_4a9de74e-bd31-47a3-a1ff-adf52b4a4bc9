// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkehub.configmanagement.v1beta;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GkeHub.ConfigManagement.V1Beta";
option go_package = "cloud.google.com/go/gkehub/configmanagement/apiv1beta/configmanagementpb;configmanagementpb";
option java_multiple_files = true;
option java_outer_classname = "ConfigManagementProto";
option java_package = "com.google.cloud.gkehub.configmanagement.v1beta";
option php_namespace = "Google\\Cloud\\GkeHub\\ConfigManagement\\V1beta";
option ruby_package = "Google::Cloud::GkeHub::ConfigManagement::V1beta";

// Enum representing the state of an ACM's deployment on a cluster
enum DeploymentState {
  // Deployment's state cannot be determined
  DEPLOYMENT_STATE_UNSPECIFIED = 0;

  // Deployment is not installed
  NOT_INSTALLED = 1;

  // Deployment is installed
  INSTALLED = 2;

  // Deployment was attempted to be installed, but has errors
  ERROR = 3;

  // Deployment is installing or terminating
  PENDING = 4;
}

// **Anthos Config Management**: State for a single cluster.
message MembershipState {
  // Output only. This field is set to the `cluster_name` field of the
  // Membership Spec if it is not empty. Otherwise, it is set to the cluster's
  // fleet membership name.
  string cluster_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Membership configuration in the cluster. This represents the
  // actual state in the cluster, while the MembershipSpec in the FeatureSpec
  // represents the intended state
  MembershipSpec membership_spec = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current install status of ACM's Operator
  OperatorState operator_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current sync status
  ConfigSyncState config_sync_state = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. PolicyController status
  PolicyControllerState policy_controller_state = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Binauthz status
  BinauthzState binauthz_state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Hierarchy Controller status
  HierarchyControllerState hierarchy_controller_state = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// **Anthos Config Management**: Configuration for a single cluster.
// Intended to parallel the ConfigManagement CR.
message MembershipSpec {
  // Whether to automatically manage the Feature.
  enum Management {
    // Unspecified
    MANAGEMENT_UNSPECIFIED = 0;

    // Google will manage the Feature for the cluster.
    MANAGEMENT_AUTOMATIC = 1;

    // User will manually manage the Feature for the cluster.
    MANAGEMENT_MANUAL = 2;
  }

  // Optional. Config Sync configuration for the cluster.
  ConfigSync config_sync = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Policy Controller configuration for the cluster.
  // Deprecated: Configuring Policy Controller through the configmanagement
  // feature is no longer recommended. Use the policycontroller feature instead.
  PolicyController policy_controller = 2
      [deprecated = true, (google.api.field_behavior) = OPTIONAL];

  // Optional. Binauthz conifguration for the cluster. Deprecated: This field
  // will be ignored and should not be set.
  BinauthzConfig binauthz = 3
      [deprecated = true, (google.api.field_behavior) = OPTIONAL];

  // Optional. Hierarchy Controller configuration for the cluster.
  // Deprecated: Configuring Hierarchy Controller through the configmanagement
  // feature is no longer recommended. Use
  // https://github.com/kubernetes-sigs/hierarchical-namespaces instead.
  HierarchyControllerConfig hierarchy_controller = 4
      [deprecated = true, (google.api.field_behavior) = OPTIONAL];

  // Optional. Version of ACM installed.
  string version = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The user-specified cluster name used by Config Sync
  // cluster-name-selector annotation or ClusterSelector, for applying configs
  // to only a subset of clusters. Omit this field if the cluster's fleet
  // membership name is used by Config Sync cluster-name-selector annotation or
  // ClusterSelector. Set this field if a name different from the cluster's
  // fleet membership name is used by Config Sync cluster-name-selector
  // annotation or ClusterSelector.
  string cluster = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Enables automatic Feature management.
  Management management = 12 [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for Config Sync
message ConfigSync {
  // Optional. Git repo configuration for the cluster.
  GitConfig git = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether the Config Sync Repo is
  // in "hierarchical" or "unstructured" mode.
  string source_format = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Enables the installation of ConfigSync.
  // If set to true, ConfigSync resources will be created and the other
  // ConfigSync fields will be applied if exist.
  // If set to false, all other ConfigSync fields will be ignored, ConfigSync
  // resources will be deleted.
  // If omitted, ConfigSync resources will be managed depends on the presence
  // of the git or oci field.
  optional bool enabled = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set to true to enable the Config Sync admission webhook to
  // prevent drifts. If set to `false`, disables the Config Sync admission
  // webhook and does not prevent drifts.
  bool prevent_drift = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. OCI repo configuration for the cluster
  OciConfig oci = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set to true to stop syncing configs for a single cluster.
  // Default to false.
  bool stop_syncing = 16 [(google.api.field_behavior) = OPTIONAL];
}

// Git repo configuration for a single cluster.
message GitConfig {
  // Required. The URL of the Git repository to use as the source of truth.
  string sync_repo = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The branch of the repository to sync from. Default: master.
  string sync_branch = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The path within the Git repository that represents the top level
  // of the repo to sync. Default: the root directory of the repository.
  string policy_dir = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Period in seconds between consecutive syncs. Default: 15.
  int64 sync_wait_secs = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Git revision (tag or hash) to check out. Default HEAD.
  string sync_rev = 5 [(google.api.field_behavior) = OPTIONAL];

  // Required. Type of secret configured for access to the Git repo.
  // Must be one of ssh, cookiefile, gcenode, token, gcpserviceaccount,
  // githubapp or none.
  // The validation of this is case-sensitive.
  string secret_type = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. URL for the HTTPS proxy to be used when communicating with the
  // Git repo.
  string https_proxy = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Google Cloud Service Account Email used for auth when
  // secret_type is gcpServiceAccount.
  string gcp_service_account_email = 8 [(google.api.field_behavior) = OPTIONAL];
}

// OCI repo configuration for a single cluster
message OciConfig {
  // Required. The OCI image repository URL for the package to sync from.
  // e.g. `LOCATION-docker.pkg.dev/PROJECT_ID/REPOSITORY_NAME/PACKAGE_NAME`.
  string sync_repo = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The absolute path of the directory that contains
  // the local resources.  Default: the root directory of the image.
  string policy_dir = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Period in seconds between consecutive syncs. Default: 15.
  int64 sync_wait_secs = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. Type of secret configured for access to the OCI repo.
  // Must be one of gcenode, gcpserviceaccount, k8sserviceaccount or none.
  // The validation of this is case-sensitive.
  string secret_type = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. The Google Cloud Service Account Email used for auth when
  // secret_type is gcpServiceAccount.
  string gcp_service_account_email = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for Policy Controller
message PolicyController {
  // Enables the installation of Policy Controller.
  // If false, the rest of PolicyController fields take no
  // effect.
  bool enabled = 1;

  // Installs the default template library along with Policy Controller.
  optional bool template_library_installed = 2;

  // Sets the interval for Policy Controller Audit Scans (in seconds).
  // When set to 0, this disables audit functionality altogether.
  optional int64 audit_interval_seconds = 3;

  // The set of namespaces that are excluded from Policy Controller checks.
  // Namespaces do not need to currently exist on the cluster.
  repeated string exemptable_namespaces = 4;

  // Enables the ability to use Constraint Templates that reference to objects
  // other than the object currently being evaluated.
  bool referential_rules_enabled = 5;

  // Logs all denies and dry run failures.
  bool log_denies_enabled = 6;

  // Enable or disable mutation in policy controller.
  // If true, mutation CRDs, webhook and controller deployment
  // will be deployed to the cluster.
  bool mutation_enabled = 7;

  // Monitoring specifies the configuration of monitoring.
  PolicyControllerMonitoring monitoring = 8;

  // Output only. Last time this membership spec was updated.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// PolicyControllerMonitoring specifies the backends Policy Controller should
// export metrics to. For example, to specify metrics should be exported to
// Cloud Monitoring and Prometheus, specify
// backends: ["cloudmonitoring", "prometheus"]
message PolicyControllerMonitoring {
  // Supported backend options for monitoring
  enum MonitoringBackend {
    // Backend cannot be determined
    MONITORING_BACKEND_UNSPECIFIED = 0;

    // Prometheus backend for monitoring
    PROMETHEUS = 1;

    // Stackdriver/Cloud Monitoring backend for monitoring
    CLOUD_MONITORING = 2;
  }

  // Specifies the list of backends Policy Controller will export to.
  // An empty list would effectively disable metrics export.
  repeated MonitoringBackend backends = 1;
}

// Configuration for Binauthz
message BinauthzConfig {
  // Whether binauthz is enabled in this cluster.
  bool enabled = 1;
}

// Configuration for Hierarchy Controller
message HierarchyControllerConfig {
  // Whether Hierarchy Controller is enabled in this cluster.
  bool enabled = 1;

  // Whether pod tree labels are enabled in this cluster.
  bool enable_pod_tree_labels = 2;

  // Whether hierarchical resource quota is enabled in this cluster.
  bool enable_hierarchical_resource_quota = 3;
}

// Deployment state for Hierarchy Controller
message HierarchyControllerDeploymentState {
  // The deployment state for open source HNC (e.g. v0.7.0-hc.0)
  DeploymentState hnc = 1;

  // The deployment state for Hierarchy Controller extension (e.g. v0.7.0-hc.1)
  DeploymentState extension = 2;
}

// Version for Hierarchy Controller
message HierarchyControllerVersion {
  // Version for open source HNC
  string hnc = 1;

  // Version for Hierarchy Controller extension
  string extension = 2;
}

// State for Hierarchy Controller
message HierarchyControllerState {
  // The version for Hierarchy Controller
  HierarchyControllerVersion version = 1;

  // The deployment state for Hierarchy Controller
  HierarchyControllerDeploymentState state = 2;
}

// State information for an ACM's Operator
message OperatorState {
  // The semenatic version number of the operator
  string version = 1;

  // The state of the Operator's deployment
  DeploymentState deployment_state = 2;

  // Install errors.
  repeated InstallError errors = 3;
}

// Errors pertaining to the installation of ACM
message InstallError {
  // A string representing the user facing error message
  string error_message = 1;
}

// State information for ConfigSync
message ConfigSyncState {
  // CRDState representing the state of a CRD
  enum CRDState {
    // CRD's state cannot be determined
    CRD_STATE_UNSPECIFIED = 0;

    // CRD is not installed
    NOT_INSTALLED = 1;

    // CRD is installed
    INSTALLED = 2;

    // CRD is terminating (i.e., it has been deleted and is cleaning up)
    TERMINATING = 3;

    // CRD is installing
    INSTALLING = 4;
  }

  // State of Config Sync
  enum State {
    // CS's state cannot be determined.
    STATE_UNSPECIFIED = 0;

    // CS is not installed.
    CONFIG_SYNC_NOT_INSTALLED = 1;

    // The expected CS version is installed successfully.
    CONFIG_SYNC_INSTALLED = 2;

    // CS encounters errors.
    CONFIG_SYNC_ERROR = 3;

    // CS is installing or terminating.
    CONFIG_SYNC_PENDING = 4;
  }

  // StopSyncingState representing Config Sync's status of syncing configs to a
  // cluster
  enum StopSyncingState {
    // State cannot be determined
    STOP_SYNCING_STATE_UNSPECIFIED = 0;

    // Syncing resources to the cluster is not stopped at the cluster level.
    NOT_STOPPED = 1;

    // Some reconcilers stop syncing resources to the cluster, while others are
    // still syncing.
    PENDING = 2;

    // Syncing resources to the cluster is stopped at the cluster level.
    STOPPED = 3;
  }

  // Output only. The version of ConfigSync deployed
  ConfigSyncVersion version = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Information about the deployment of ConfigSync, including the
  // version of the various Pods deployed
  ConfigSyncDeploymentState deployment_state = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of ConfigSync's process to sync configs to a cluster
  SyncState sync_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Errors pertaining to the installation of Config Sync.
  repeated ConfigSyncError errors = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the RootSync CRD
  CRDState rootsync_crd = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of the Reposync CRD
  CRDState reposync_crd = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of CS
  // This field summarizes the other fields in this message.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Whether syncing resources to the cluster is stopped at the
  // cluster level.
  StopSyncingState cluster_level_stop_syncing_state = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of RootSync and RepoSync CRs in the cluster.
  int32 cr_count = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Errors pertaining to the installation of Config Sync
message ConfigSyncError {
  // A string representing the user facing error message
  string error_message = 1;
}

// Specific versioning information pertaining to ConfigSync's Pods
message ConfigSyncVersion {
  // Version of the deployed importer pod
  string importer = 1;

  // Version of the deployed syncer pod
  string syncer = 2;

  // Version of the deployed git-sync pod
  string git_sync = 3;

  // Version of the deployed monitor pod
  string monitor = 4;

  // Version of the deployed reconciler-manager pod
  string reconciler_manager = 5;

  // Version of the deployed reconciler container in root-reconciler pod
  string root_reconciler = 6;

  // Version of the deployed admission-webhook pod
  string admission_webhook = 7;

  // Version of the deployed resource-group-controller-manager pod
  string resource_group_controller_manager = 8;

  // Version of the deployed otel-collector pod
  string otel_collector = 9;
}

// The state of ConfigSync's deployment on a cluster
message ConfigSyncDeploymentState {
  // Deployment state of the importer pod
  DeploymentState importer = 1;

  // Deployment state of the syncer pod
  DeploymentState syncer = 2;

  // Deployment state of the git-sync pod
  DeploymentState git_sync = 3;

  // Deployment state of the monitor pod
  DeploymentState monitor = 4;

  // Deployment state of reconciler-manager pod
  DeploymentState reconciler_manager = 5;

  // Deployment state of root-reconciler
  DeploymentState root_reconciler = 6;

  // Deployment state of admission-webhook
  DeploymentState admission_webhook = 7;

  // Deployment state of resource-group-controller-manager
  DeploymentState resource_group_controller_manager = 8;

  // Deployment state of otel-collector
  DeploymentState otel_collector = 9;
}

// State indicating an ACM's progress syncing configurations to a cluster
message SyncState {
  // An enum representing Config Sync's status of syncing configs to a cluster.
  enum SyncCode {
    // Config Sync cannot determine a sync code
    SYNC_CODE_UNSPECIFIED = 0;

    // Config Sync successfully synced the git Repo with the cluster
    SYNCED = 1;

    // Config Sync is in the progress of syncing a new change
    PENDING = 2;

    // Indicates an error configuring Config Sync, and user action is required
    ERROR = 3;

    // Config Sync has been installed but not configured
    NOT_CONFIGURED = 4;

    // Config Sync has not been installed
    NOT_INSTALLED = 5;

    // Error authorizing with the cluster
    UNAUTHORIZED = 6;

    // Cluster could not be reached
    UNREACHABLE = 7;
  }

  // Token indicating the state of the repo.
  string source_token = 1;

  // Token indicating the state of the importer.
  string import_token = 2;

  // Token indicating the state of the syncer.
  string sync_token = 3;

  // Deprecated: use last_sync_time instead.
  // Timestamp of when ACM last successfully synced the repo
  // The time format is specified in https://golang.org/pkg/time/#Time.String
  string last_sync = 4 [deprecated = true];

  // Timestamp type of when ACM last successfully synced the repo
  google.protobuf.Timestamp last_sync_time = 7;

  // Sync status code
  SyncCode code = 5;

  // A list of errors resulting from problematic configs.
  // This list will be truncated after 100 errors, although it is
  // unlikely for that many errors to simultaneously exist.
  repeated SyncError errors = 6;
}

// An ACM created error representing a problem syncing configurations
message SyncError {
  // An ACM defined error code
  string code = 1;

  // A description of the error
  string error_message = 2;

  // A list of config(s) associated with the error, if any
  repeated ErrorResource error_resources = 3;
}

// Model for a config file in the git repo with an associated Sync error
message ErrorResource {
  // Path in the git repo of the erroneous config
  string source_path = 1;

  // Metadata name of the resource that is causing an error
  string resource_name = 2;

  // Namespace of the resource that is causing an error
  string resource_namespace = 3;

  // Group/version/kind of the resource that is causing an error
  GroupVersionKind resource_gvk = 4;
}

// A Kubernetes object's GVK
message GroupVersionKind {
  // Kubernetes Group
  string group = 1;

  // Kubernetes Version
  string version = 2;

  // Kubernetes Kind
  string kind = 3;
}

// State for PolicyControllerState.
message PolicyControllerState {
  // The version of Gatekeeper Policy Controller deployed.
  PolicyControllerVersion version = 1;

  // The state about the policy controller installation.
  GatekeeperDeploymentState deployment_state = 2;

  // Record state of ACM -> PoCo Hub migration for this feature.
  PolicyControllerMigration migration = 4;
}

// The build version of Gatekeeper Policy Controller is using.
message PolicyControllerVersion {
  // The gatekeeper image tag that is composed of ACM version, git tag, build
  // number.
  string version = 1;
}

// State for the migration of PolicyController from ACM -> PoCo Hub.
message PolicyControllerMigration {
  // Stage marks what stage of the migration ACM hub is in.
  enum Stage {
    // Unknown state of migration.
    STAGE_UNSPECIFIED = 0;

    // ACM Hub/Operator manages policycontroller. No migration yet completed.
    ACM_MANAGED = 1;

    // All migrations steps complete; Poco Hub now manages policycontroller.
    POCO_MANAGED = 2;
  }

  // Stage of the migration.
  Stage stage = 1;

  // Last time this membership spec was copied to PoCo feature.
  google.protobuf.Timestamp copy_time = 2;
}

// State for Binauthz
message BinauthzState {
  // The state of the binauthz webhook.
  DeploymentState webhook = 1;

  // The version of binauthz that is installed.
  BinauthzVersion version = 2;
}

// The version of binauthz.
message BinauthzVersion {
  // The version of the binauthz webhook.
  string webhook_version = 1;
}

// State of Policy Controller installation.
message GatekeeperDeploymentState {
  // Status of gatekeeper-controller-manager pod.
  DeploymentState gatekeeper_controller_manager_state = 1;

  // Status of gatekeeper-audit deployment.
  DeploymentState gatekeeper_audit = 2;

  // Status of the pod serving the mutation webhook.
  DeploymentState gatekeeper_mutation = 3;
}
