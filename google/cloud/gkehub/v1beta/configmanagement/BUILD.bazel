# This file was automatically generated by BuildFileGenerator

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "configmanagement_proto",
    srcs = [
        "configmanagement.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_proto_library",
    "java_gapic_assembly_gradle_pkg",
)

java_proto_library(
    name = "configmanagement_java_proto",
    deps = [":configmanagement_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-gkehub-configmanagement-v1beta-java",
    deps = [
        ":configmanagement_proto",
        ":configmanagement_java_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
    "go_gapic_assembly_pkg",
)

go_proto_library(
    name = "configmanagement_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/gkehub/configmanagement/apiv1beta/configmanagementpb",
    protos = [":configmanagement_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_assembly_pkg(
    name = "google-cloud-gkehub-configmanagement-v1beta-go",
    deps = [
        ":configmanagement_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
    "py_gapic_library",
    "py_gapic_assembly_pkg",
)

moved_proto_library(
    name = "configmanagement_moved_proto",
    srcs = [":configmanagement_proto"],
    deps = [
        "//google/api:field_behavior_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "configmanagement_py_proto",
    deps = [":configmanagement_moved_proto"],
)

py_grpc_library(
    name = "configmanagement_py_grpc",
    srcs = [":configmanagement_moved_proto"],
    deps = [":configmanagement_py_proto"],
)

py_gapic_library(
    name = "configmanagement_py_gapic",
    srcs = [":configmanagement_proto"],
    rest_numeric_enums = False,
    transport = "grpc+rest",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-gkehub-configmanagement-v1beta-py",
    deps = [
        ":configmanagement_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "configmanagement_php_proto",
    deps = [":configmanagement_proto"],
)

php_gapic_assembly_pkg(
    name = "google-cloud-gkehub-configmanagement-v1beta-php",
    deps = [
        ":configmanagement_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "configmanagement_ruby_proto",
    deps = [":configmanagement_proto"],
)

ruby_grpc_library(
    name = "configmanagement_ruby_grpc",
    srcs = [":configmanagement_proto"],
    deps = [":configmanagement_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_proto_library",
    "csharp_gapic_assembly_pkg",
)

csharp_proto_library(
    name = "configmanagement_csharp_proto",
    deps = [":configmanagement_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-gkehub-configmanagement-v1beta-csharp",
    package_name = "Google.Cloud.GkeHub.ConfigManagement.V1Beta",
    generate_nongapic_package = True,
    deps = [
        ":configmanagement_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "configmanagement_cc_proto",
    deps = [":configmanagement_proto"],
)

cc_grpc_library(
    name = "configmanagement_cc_grpc",
    srcs = [":configmanagement_proto"],
    grpc_only = True,
    deps = [":configmanagement_cc_proto"],
)
