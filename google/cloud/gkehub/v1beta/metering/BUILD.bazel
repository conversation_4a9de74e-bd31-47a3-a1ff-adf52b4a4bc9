# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "metering_proto",
    srcs = [
        "metering.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "metering_java_proto",
    deps = [":metering_proto"],
)

java_grpc_library(
    name = "metering_java_grpc",
    srcs = [":metering_proto"],
    deps = [":metering_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "metering_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/gkehub/metering/apiv1beta/meteringpb",
    protos = [":metering_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_library",
)

py_gapic_library(
    name = "metering_py_gapic",
    srcs = [":metering_proto"],
    rest_numeric_enums = False,
    transport = "grpc",
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "metering_php_proto",
    deps = [":metering_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "metering_ruby_proto",
    deps = [":metering_proto"],
)

ruby_grpc_library(
    name = "metering_ruby_grpc",
    srcs = [":metering_proto"],
    deps = [":metering_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "metering_csharp_proto",
    deps = [":metering_proto"],
)

csharp_grpc_library(
    name = "metering_csharp_grpc",
    srcs = [":metering_proto"],
    deps = [":metering_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
