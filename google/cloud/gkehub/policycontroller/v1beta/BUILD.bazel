# This file was automatically generated by BuildFileGenerator

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "policycontroller_proto",
    srcs = [
        "policycontroller.proto",
    ],
    deps = [
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "policycontroller_java_proto",
    deps = [":policycontroller_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-gkehub-policycontroller-v1beta-java",
    deps = [
        ":policycontroller_java_proto",
        ":policycontroller_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_proto_library",
)

go_proto_library(
    name = "policycontroller_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/gkehub/policycontroller/apiv1beta/policycontrollerpb",
    protos = [":policycontroller_proto"],
    deps = [
    ],
)

go_gapic_assembly_pkg(
    name = "google-cloud-gkehub-policycontroller-v1beta-go",
    deps = [
        ":policycontroller_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "policycontroller_moved_proto",
    srcs = [":policycontroller_proto"],
    deps = [
    ],
)

py_proto_library(
    name = "policycontroller_py_proto",
    deps = [":policycontroller_moved_proto"],
)

py_grpc_library(
    name = "policycontroller_py_grpc",
    srcs = [":policycontroller_moved_proto"],
    deps = [":policycontroller_py_proto"],
)

py_gapic_library(
    name = "policycontroller_py_gapic",
    srcs = [":policycontroller_proto"],
    rest_numeric_enums = False,
    transport = "grpc+rest",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-gkehub-policycontroller-v1beta-py",
    deps = [
        ":policycontroller_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "policycontroller_php_proto",
    deps = [":policycontroller_proto"],
)

php_gapic_assembly_pkg(
    name = "google-cloud-gkehub-policycontroller-v1beta-php",
    deps = [
        ":policycontroller_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "policycontroller_ruby_proto",
    deps = [":policycontroller_proto"],
)

ruby_grpc_library(
    name = "policycontroller_ruby_grpc",
    srcs = [":policycontroller_proto"],
    deps = [":policycontroller_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "policycontroller_csharp_proto",
    deps = [":policycontroller_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-gkehub-policycontroller-v1beta-csharp",
    package_name = "Google.Cloud.GkeHub.PolicyController.V1Beta",
    generate_nongapic_package = True,
    deps = [
        ":policycontroller_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "policycontroller_cc_proto",
    deps = [":policycontroller_proto"],
)

cc_grpc_library(
    name = "policycontroller_cc_grpc",
    srcs = [":policycontroller_proto"],
    grpc_only = True,
    deps = [":policycontroller_cc_proto"],
)
