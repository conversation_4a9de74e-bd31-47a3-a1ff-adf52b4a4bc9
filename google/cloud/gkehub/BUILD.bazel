# This build file includes a target for the Ruby wrapper library for
# google-cloud-gke_hub.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for gkehub.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "gkehub_ruby_wrapper",
    srcs = ["//google/cloud/gkehub/v1:gkehub_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-gke_hub",
        "ruby-cloud-env-prefix=GKE_HUB",
        "ruby-cloud-wrapper-of=v1:0.8;v1beta1:0.10",
        "ruby-cloud-product-url=https://cloud.google.com/anthos/clusters/docs",
        "ruby-cloud-api-id=gkehub.googleapis.com",
        "ruby-cloud-api-shortname=gkehub",
    ],
    ruby_cloud_description = "The GKE Hub API centrally manages features and services on all your Kubernetes clusters running in a variety of environments, including Google cloud, on premises in customer datacenters, or other third party clouds.",
    ruby_cloud_title = "GKE Hub",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-gkehub-ruby",
    deps = [
        ":gkehub_ruby_wrapper",
    ],
)
