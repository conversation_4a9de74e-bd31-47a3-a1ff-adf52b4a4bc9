// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkehub.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.GkeHub.V1Beta1";
option go_package = "cloud.google.com/go/gkehub/apiv1beta1/gkehubpb;gkehubpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.gkehub.v1beta1";
option php_namespace = "Google\\Cloud\\GkeHub\\V1beta1";
option ruby_package = "Google::Cloud::GkeHub::V1beta1";

// The GKE Hub MembershipService handles the registration of many Kubernetes
// clusters to Google Cloud, represented with the
// [Membership][google.cloud.gkehub.v1beta1.Membership] resource.
//
// GKE Hub is currently available in the global region and all regions in
// https://cloud.google.com/compute/docs/regions-zones.
//
// **Membership management may be non-trivial:** it is recommended to use one
// of the Google-provided client libraries or tools where possible when working
// with Membership resources.
service GkeHubMembershipService {
  option (google.api.default_host) = "gkehub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Memberships in a given project and location.
  rpc ListMemberships(ListMembershipsRequest)
      returns (ListMembershipsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/memberships"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a Membership.
  rpc GetMembership(GetMembershipRequest) returns (Membership) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/memberships/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Membership.
  //
  // **This is currently only supported for GKE clusters on Google Cloud**.
  // To register other clusters, follow the instructions at
  // https://cloud.google.com/anthos/multicluster-management/connect/registering-a-cluster.
  rpc CreateMembership(CreateMembershipRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/memberships"
      body: "resource"
    };
    option (google.api.method_signature) = "parent,resource,membership_id";
    option (google.longrunning.operation_info) = {
      response_type: "Membership"
      metadata_type: "OperationMetadata"
    };
  }

  // Removes a Membership.
  //
  // **This is currently only supported for GKE clusters on Google Cloud**.
  // To unregister other clusters, follow the instructions at
  // https://cloud.google.com/anthos/multicluster-management/connect/unregistering-a-cluster.
  rpc DeleteMembership(DeleteMembershipRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/memberships/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an existing Membership.
  rpc UpdateMembership(UpdateMembershipRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{name=projects/*/locations/*/memberships/*}"
      body: "resource"
    };
    option (google.api.method_signature) = "name,resource,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Membership"
      metadata_type: "OperationMetadata"
    };
  }

  // Generates the manifest for deployment of the GKE connect agent.
  //
  // **This method is used internally by Google-provided libraries.**
  // Most clients should not need to call this method directly.
  rpc GenerateConnectManifest(GenerateConnectManifestRequest)
      returns (GenerateConnectManifestResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/memberships/*}:generateConnectManifest"
    };
  }

  // ValidateExclusivity validates the state of exclusivity in the cluster.
  // The validation does not depend on an existing Hub membership resource.
  rpc ValidateExclusivity(ValidateExclusivityRequest)
      returns (ValidateExclusivityResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/memberships:validateExclusivity"
    };
  }

  // GenerateExclusivityManifest generates the manifests to update the
  // exclusivity artifacts in the cluster if needed.
  //
  // Exclusivity artifacts include the Membership custom resource definition
  // (CRD) and the singleton Membership custom resource (CR). Combined with
  // ValidateExclusivity, exclusivity artifacts guarantee that a Kubernetes
  // cluster is only registered to a single GKE Hub.
  //
  // The Membership CRD is versioned, and may require conversion when the GKE
  // Hub API server begins serving a newer version of the CRD and
  // corresponding CR. The response will be the converted CRD and CR if there
  // are any differences between the versions.
  rpc GenerateExclusivityManifest(GenerateExclusivityManifestRequest)
      returns (GenerateExclusivityManifestResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/memberships/*}:generateExclusivityManifest"
    };
  }
}

// Membership contains information about a member cluster.
message Membership {
  option (google.api.resource) = {
    type: "gkehub.googleapis.com/Membership"
    pattern: "projects/{project}/locations/{location}/memberships/{membership}"
  };

  // Specifies the infrastructure type of a Membership. Infrastructure type is
  // used by Hub to control infrastructure-specific behavior, including pricing.
  //
  // Each GKE distribution (on-GCP, on-Prem, on-X,...) will set this field
  // automatically, but Attached Clusters customers should specify a type
  // during registration.
  enum InfrastructureType {
    // No type was specified. Some Hub functionality may require a type be
    // specified, and will not support Memberships with this value.
    INFRASTRUCTURE_TYPE_UNSPECIFIED = 0;

    // Private infrastructure that is owned or operated by customer. This
    // includes GKE distributions such as GKE-OnPrem and GKE-OnBareMetal.
    ON_PREM = 1;

    // Public cloud infrastructure.
    MULTI_CLOUD = 2;
  }

  // Output only. The full, unique name of this Membership resource in the
  // format `projects/*/locations/*/memberships/{membership_id}`, set during
  // creation.
  //
  // `membership_id` must be a valid RFC 1123 compliant DNS label:
  //
  //   1. At most 63 characters in length
  //   2. It must consist of lower case alphanumeric characters or `-`
  //   3. It must start and end with an alphanumeric character
  //
  // Which can be expressed as the regex: `[a-z0-9]([-a-z0-9]*[a-z0-9])?`,
  // with a maximum length of 63 characters.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. GCP labels for this membership.
  map<string, string> labels = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of this membership, limited to 63 characters.
  // Must match the regex: `[a-zA-Z0-9][a-zA-Z0-9_\-\.\ ]*`
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Type of resource represented by this Membership
  oneof type {
    // Optional. Endpoint information to reach this member.
    MembershipEndpoint endpoint = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. State of the Membership resource.
  MembershipState state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. How to identify workloads from this Membership.
  // See the documentation on Workload Identity for more details:
  // https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity
  Authority authority = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. When the Membership was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the Membership was last updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When the Membership was deleted.
  google.protobuf.Timestamp delete_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. An externally-generated and managed ID for this Membership. This
  // ID may be modified after creation, but this is not recommended. For GKE
  // clusters, external_id is managed by the Hub API and updates will be
  // ignored.
  //
  // The ID must match the regex: `[a-zA-Z0-9][a-zA-Z0-9_\-\.]*`
  //
  // If this Membership represents a Kubernetes cluster, this value should be
  // set to the UID of the `kube-system` namespace object.
  string external_id = 10 [(google.api.field_behavior) = OPTIONAL];

  // Output only. For clusters using Connect, the timestamp of the most recent
  // connection established with Google Cloud. This time is updated every
  // several minutes, not continuously. For clusters that do not use GKE
  // Connect, or that have never connected successfully, this field will be
  // unset.
  google.protobuf.Timestamp last_connection_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Google-generated UUID for this resource. This is unique across
  // all Membership resources. If a Membership resource is deleted and another
  // resource with the same name is created, it gets a different unique_id.
  string unique_id = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The infrastructure type this Membership is running on.
  InfrastructureType infrastructure_type = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The monitoring config information for this membership.
  MonitoringConfig monitoring_config = 14
      [(google.api.field_behavior) = OPTIONAL];
}

// MembershipEndpoint contains information needed to contact a Kubernetes API,
// endpoint and any additional Kubernetes metadata.
message MembershipEndpoint {
  // Cluster information of the registered cluster.
  oneof type {
    // Optional. Specific information for a GKE-on-GCP cluster.
    GkeCluster gke_cluster = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Specific information for a GKE On-Prem cluster. An onprem
    // user-cluster who has no resourceLink is not allowed to use this field, it
    // should have a nil "type" instead.
    OnPremCluster on_prem_cluster = 7 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Specific information for a GKE Multi-Cloud cluster.
    MultiCloudCluster multi_cloud_cluster = 8
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Specific information for a Google Edge cluster.
    EdgeCluster edge_cluster = 9 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Specific information for a GDC Edge Appliance cluster.
    ApplianceCluster appliance_cluster = 10
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. Useful Kubernetes-specific metadata.
  KubernetesMetadata kubernetes_metadata = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The in-cluster Kubernetes Resources that should be applied for a
  // correctly registered cluster, in the steady state. These resources:
  //
  //   * Ensure that the cluster is exclusively registered to one and only one
  //     Hub Membership.
  //   * Propagate Workload Pool Information available in the Membership
  //     Authority field.
  //   * Ensure proper initial configuration of default Hub Features.
  KubernetesResource kubernetes_resource = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// KubernetesResource contains the YAML manifests and configuration for
// Membership Kubernetes resources in the cluster. After CreateMembership or
// UpdateMembership, these resources should be re-applied in the cluster.
message KubernetesResource {
  // Input only. The YAML representation of the Membership CR. This field is
  // ignored for GKE clusters where Hub can read the CR directly.
  //
  // Callers should provide the CR that is currently present in the cluster
  // during CreateMembership or UpdateMembership, or leave this field empty if
  // none exists. The CR manifest is used to validate the cluster has not been
  // registered with another Membership.
  string membership_cr_manifest = 1 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Additional Kubernetes resources that need to be applied to the
  // cluster after Membership creation, and after every update.
  //
  // This field is only populated in the Membership returned from a successful
  // long-running operation from CreateMembership or UpdateMembership. It is not
  // populated during normal GetMembership or ListMemberships requests. To get
  // the resource manifest after the initial registration, the caller should
  // make a UpdateMembership call with an empty field mask.
  repeated ResourceManifest membership_resources = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Kubernetes resources for installing the GKE Connect agent
  //
  // This field is only populated in the Membership returned from a successful
  // long-running operation from CreateMembership or UpdateMembership. It is not
  // populated during normal GetMembership or ListMemberships requests. To get
  // the resource manifest after the initial registration, the caller should
  // make a UpdateMembership call with an empty field mask.
  repeated ResourceManifest connect_resources = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Options for Kubernetes resource generation.
  ResourceOptions resource_options = 4 [(google.api.field_behavior) = OPTIONAL];
}

// ResourceOptions represent options for Kubernetes resource generation.
message ResourceOptions {
  // Optional. The Connect agent version to use for connect_resources. Defaults
  // to the latest GKE Connect version. The version must be a currently
  // supported version, obsolete versions will be rejected.
  string connect_version = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Use `apiextensions/v1beta1` instead of `apiextensions/v1` for
  // CustomResourceDefinition resources.
  // This option should be set for clusters with Kubernetes apiserver versions
  // <1.16.
  bool v1beta1_crd = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Major version of the Kubernetes cluster. This is only used to
  // determine which version to use for the CustomResourceDefinition resources,
  // `apiextensions/v1beta1` or`apiextensions/v1`.
  string k8s_version = 3 [(google.api.field_behavior) = OPTIONAL];
}

// ResourceManifest represents a single Kubernetes resource to be applied to
// the cluster.
message ResourceManifest {
  // YAML manifest of the resource.
  string manifest = 1;

  // Whether the resource provided in the manifest is `cluster_scoped`.
  // If unset, the manifest is assumed to be namespace scoped.
  //
  // This field is used for REST mapping when applying the resource in a
  // cluster.
  bool cluster_scoped = 2;
}

// GkeCluster contains information specific to GKE clusters.
message GkeCluster {
  // Immutable. Self-link of the GCP resource for the GKE cluster. For example:
  //
  //     //container.googleapis.com/projects/my-project/locations/us-west1-a/clusters/my-cluster
  //
  // Zonal clusters are also supported.
  string resource_link = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. If cluster_missing is set then it denotes that the GKE cluster
  // no longer exists in the GKE Control Plane.
  bool cluster_missing = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// OnPremCluster contains information specific to GKE On-Prem clusters.
message OnPremCluster {
  // ClusterType describes on prem cluster's type.
  enum ClusterType {
    // The ClusterType is not set.
    CLUSTERTYPE_UNSPECIFIED = 0;

    // The ClusterType is bootstrap cluster.
    BOOTSTRAP = 1;

    // The ClusterType is baremetal hybrid cluster.
    HYBRID = 2;

    // The ClusterType is baremetal standalone cluster.
    STANDALONE = 3;

    // The ClusterType is user cluster.
    USER = 4;
  }

  // Immutable. Self-link of the GCP resource for the GKE On-Prem cluster. For
  // example:
  //
  //  //gkeonprem.googleapis.com/projects/my-project/locations/us-west1-a/vmwareClusters/my-cluster
  //  //gkeonprem.googleapis.com/projects/my-project/locations/us-west1-a/bareMetalClusters/my-cluster
  string resource_link = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. If cluster_missing is set then it denotes that
  // API(gkeonprem.googleapis.com) resource for this GKE On-Prem cluster no
  // longer exists.
  bool cluster_missing = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Whether the cluster is an admin cluster.
  bool admin_cluster = 3 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. The on prem cluster's type.
  ClusterType cluster_type = 4 [(google.api.field_behavior) = IMMUTABLE];
}

// MultiCloudCluster contains information specific to GKE Multi-Cloud clusters.
message MultiCloudCluster {
  // Immutable. Self-link of the GCP resource for the GKE Multi-Cloud cluster.
  // For example:
  //
  //  //gkemulticloud.googleapis.com/projects/my-project/locations/us-west1-a/awsClusters/my-cluster
  //  //gkemulticloud.googleapis.com/projects/my-project/locations/us-west1-a/azureClusters/my-cluster
  //  //gkemulticloud.googleapis.com/projects/my-project/locations/us-west1-a/attachedClusters/my-cluster
  string resource_link = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. If cluster_missing is set then it denotes that
  // API(gkemulticloud.googleapis.com) resource for this GKE Multi-Cloud cluster
  // no longer exists.
  bool cluster_missing = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// EdgeCluster contains information specific to Google Edge Clusters.
message EdgeCluster {
  // Immutable. Self-link of the GCP resource for the Edge Cluster. For
  // example:
  //
  // //edgecontainer.googleapis.com/projects/my-project/locations/us-west1-a/clusters/my-cluster
  string resource_link = 1 [(google.api.field_behavior) = IMMUTABLE];
}

// ApplianceCluster contains information specific to GDC Edge Appliance
// Clusters.
message ApplianceCluster {
  // Immutable. Self-link of the GCP resource for the Appliance Cluster. For
  // example:
  //
  // //transferappliance.googleapis.com/projects/my-project/locations/us-west1-a/appliances/my-appliance
  string resource_link = 1 [(google.api.field_behavior) = IMMUTABLE];
}

// KubernetesMetadata provides informational metadata for Memberships
// representing Kubernetes clusters.
message KubernetesMetadata {
  // Output only. Kubernetes API server version string as reported by
  // '/version'.
  string kubernetes_api_server_version = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Node providerID as reported by the first node in the list of
  // nodes on the Kubernetes endpoint. On Kubernetes platforms that support
  // zero-node clusters (like GKE-on-GCP), the node_count will be zero and the
  // node_provider_id will be empty.
  string node_provider_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Node count as reported by Kubernetes nodes resources.
  int32 node_count = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. vCPU count as reported by Kubernetes nodes resources.
  int32 vcpu_count = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The total memory capacity as reported by the sum of all
  // Kubernetes nodes resources, defined in MB.
  int32 memory_mb = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which these details were last updated. This
  // update_time is different from the Membership-level update_time since
  // EndpointDetails are updated internally for API consumers.
  google.protobuf.Timestamp update_time = 100
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Authority encodes how Google will recognize identities from this Membership.
// See the workload identity documentation for more details:
// https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity
message Authority {
  // Optional. A JSON Web Token (JWT) issuer URI. `issuer` must start with
  // `https://` and be a valid URL with length <2000 characters.
  //
  // If set, then Google will allow valid OIDC tokens from this issuer to
  // authenticate within the workload_identity_pool. OIDC discovery will be
  // performed on this URI to validate tokens from the issuer.
  //
  // Clearing `issuer` disables Workload Identity. `issuer` cannot be directly
  // modified; it must be cleared (and Workload Identity disabled) before using
  // a new issuer (and re-enabling Workload Identity).
  string issuer = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The name of the workload identity pool in which `issuer` will
  // be recognized.
  //
  // There is a single Workload Identity Pool per Hub that is shared
  // between all Memberships that belong to that Hub. For a Hub hosted in
  // {PROJECT_ID}, the workload pool format is `{PROJECT_ID}.hub.id.goog`,
  // although this is subject to change in newer versions of this API.
  string workload_identity_pool = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An identity provider that reflects the `issuer` in the
  // workload identity pool.
  string identity_provider = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. OIDC verification keys for this Membership in JWKS format (RFC
  // 7517).
  //
  // When this field is set, OIDC discovery will NOT be performed on `issuer`,
  // and instead OIDC tokens will be validated using this field.
  bytes oidc_jwks = 4 [(google.api.field_behavior) = OPTIONAL];
}

// This field informs Fleet-based applications/services/UIs with the necessary
// information for where each underlying Cluster reports its metrics.
message MonitoringConfig {
  // Immutable. Project used to report Metrics
  string project_id = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Location used to report Metrics
  string location = 2 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Cluster name used to report metrics.
  // For Anthos on VMWare/Baremetal, it would be in format
  // `memberClusters/cluster_name`; And for Anthos on MultiCloud, it would be in
  // format
  // `{azureClusters, awsClusters}/cluster_name`.
  string cluster = 3 [(google.api.field_behavior) = IMMUTABLE];

  // Kubernetes system metrics, if available, are written to this prefix.
  // This defaults to kubernetes.io for GKE, and kubernetes.io/anthos for Anthos
  // eventually. Noted: Anthos MultiCloud will have kubernetes.io prefix today
  // but will migration to be under kubernetes.io/anthos
  string kubernetes_metrics_prefix = 4;

  // Immutable. Cluster hash, this is a unique string generated by google code,
  // which does not contain any PII, which we can use to reference the cluster.
  // This is expected to be created by the monitoring stack and persisted into
  // the Cluster object as well as to GKE-Hub.
  string cluster_hash = 5 [(google.api.field_behavior) = IMMUTABLE];
}

// State of the Membership resource.
message MembershipState {
  // Code describes the state of a Membership resource.
  enum Code {
    // The code is not set.
    CODE_UNSPECIFIED = 0;

    // The cluster is being registered.
    CREATING = 1;

    // The cluster is registered.
    READY = 2;

    // The cluster is being unregistered.
    DELETING = 3;

    // The Membership is being updated.
    UPDATING = 4;

    // The Membership is being updated by the Hub Service.
    SERVICE_UPDATING = 5;
  }

  // Output only. The current state of the Membership resource.
  Code code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // This field is never set by the Hub Service.
  string description = 2 [deprecated = true];

  // This field is never set by the Hub Service.
  google.protobuf.Timestamp update_time = 3 [deprecated = true];
}

// Request message for `GkeHubMembershipService.ListMemberships` method.
message ListMembershipsRequest {
  // Required. The parent (project and location) where the Memberships will be
  // listed. Specified in the format `projects/*/locations/*`.
  // `projects/*/locations/-` list memberships in all the regions.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Optional. When requesting a 'page' of resources, `page_size` specifies
  // number of resources to return. If unspecified or set to 0, all resources
  // will be returned.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Token returned by previous call to `ListMemberships` which
  // specifies the position in the list from where to continue listing the
  // resources.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Lists Memberships that match the filter expression, following the
  // syntax outlined in https://google.aip.dev/160.
  //
  // Examples:
  //
  //   - Name is `bar` in project `foo-proj` and location `global`:
  //
  //       name = "projects/foo-proj/locations/global/membership/bar"
  //
  //   - Memberships that have a label called `foo`:
  //
  //       labels.foo:*
  //
  //   - Memberships that have a label called `foo` whose value is `bar`:
  //
  //       labels.foo = bar
  //
  //   - Memberships in the CREATING state:
  //
  //       state = CREATING
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. One or more fields to compare and use to sort the output.
  // See https://google.aip.dev/132#ordering.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `GkeHubMembershipService.ListMemberships` method.
message ListMembershipsResponse {
  // The list of matching Memberships.
  repeated Membership resources = 1;

  // A token to request the next page of resources from the
  // `ListMemberships` method. The value of an empty string means that
  // there are no more resources to return.
  string next_page_token = 2;

  // List of locations that could not be reached while fetching this list.
  repeated string unreachable = 3;
}

// Request message for `GkeHubMembershipService.GetMembership` method.
message GetMembershipRequest {
  // Required. The Membership resource name in the format
  // `projects/*/locations/*/memberships/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Membership"
    }
  ];
}

// Request message for the `GkeHubMembershipService.CreateMembership` method.
message CreateMembershipRequest {
  // Required. The parent (project and location) where the Memberships will be
  // created. Specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Required. Client chosen ID for the membership. `membership_id` must be a
  // valid RFC 1123 compliant DNS label:
  //
  //   1. At most 63 characters in length
  //   2. It must consist of lower case alphanumeric characters or `-`
  //   3. It must start and end with an alphanumeric character
  //
  // Which can be expressed as the regex: `[a-z0-9]([-a-z0-9]*[a-z0-9])?`,
  // with a maximum length of 63 characters.
  string membership_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The membership to create.
  Membership resource = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for `GkeHubMembershipService.DeleteMembership` method.
message DeleteMembershipRequest {
  // Required. The Membership resource name in the format
  // `projects/*/locations/*/memberships/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, any subresource from this Membership will also be
  // deleted. Otherwise, the request will only work if the Membership has no
  // subresource.
  bool force = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for `GkeHubMembershipService.UpdateMembership` method.
message UpdateMembershipRequest {
  // Required. The membership resource name in the format:
  // `projects/[project_id]/locations/global/memberships/[membership_id]`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Required. Mask of fields to update. At least one field path must be
  // specified in this mask.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. Only fields specified in update_mask are updated.
  // If you specify a field in the update_mask but don't specify its value here
  // that field will be deleted.
  // If you are updating a map field, set the value of a key to null or empty
  // string to delete the key from the map. It's not possible to update a key's
  // value to the empty string.
  // If you specify the update_mask to be a special path "*", fully replaces all
  // user-modifiable fields to match `resource`.
  Membership resource = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for `GkeHubMembershipService.GenerateConnectManifest`
// method.
message GenerateConnectManifestRequest {
  // Required. The Membership resource name the Agent will associate with, in
  // the format `projects/*/locations/*/memberships/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Optional. The connect agent to generate manifest for.
  ConnectAgent connect_agent = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Connect agent version to use. Defaults to the most current
  // version.
  string version = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true, generate the resources for upgrade only. Some resources
  // generated only for installation (e.g. secrets) will be excluded.
  bool is_upgrade = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The registry to fetch the connect agent image from. Defaults to
  // gcr.io/gkeconnect.
  string registry = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The image pull secret content for the registry, if not public.
  bytes image_pull_secret_content = 6 [(google.api.field_behavior) = OPTIONAL];
}

// GenerateConnectManifestResponse contains manifest information for
// installing/upgrading a Connect agent.
message GenerateConnectManifestResponse {
  // The ordered list of Kubernetes resources that need to be applied to the
  // cluster for GKE Connect agent installation/upgrade.
  repeated ConnectAgentResource manifest = 1;
}

// ConnectAgentResource represents a Kubernetes resource manifest for Connect
// Agent deployment.
message ConnectAgentResource {
  // Kubernetes type of the resource.
  TypeMeta type = 1;

  // YAML manifest of the resource.
  string manifest = 2;
}

// TypeMeta is the type information needed for content unmarshalling of
// Kubernetes resources in the manifest.
message TypeMeta {
  // Kind of the resource (e.g. Deployment).
  string kind = 1;

  // APIVersion of the resource (e.g. v1).
  string api_version = 2;
}

// The information required from end users to use GKE Connect.
message ConnectAgent {
  // Do not set.
  string name = 1 [deprecated = true];

  // Optional. URI of a proxy if connectivity from the agent to
  // gkeconnect.googleapis.com requires the use of a proxy. Format must be in
  // the form `http(s)://{proxy_address}`, depending on the HTTP/HTTPS protocol
  // supported by the proxy. This will direct the connect agent's outbound
  // traffic through a HTTP(S) proxy.
  bytes proxy = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Namespace for GKE Connect agent resources. Defaults to
  // `gke-connect`.
  //
  // The Connect Agent is authorized automatically when run in the default
  // namespace. Otherwise, explicit authorization must be granted with an
  // additional IAM binding.
  string namespace = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The request to validate the existing state of the membership CR in the
// cluster.
message ValidateExclusivityRequest {
  // Required. The parent (project and location) where the Memberships will be
  // created. Specified in the format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Optional. The YAML of the membership CR in the cluster. Empty if the
  // membership CR does not exist.
  string cr_manifest = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The intended membership name under the `parent`. This method only
  // does validation in anticipation of a CreateMembership call with the same
  // name.
  string intended_membership = 3 [(google.api.field_behavior) = REQUIRED];
}

// The response of exclusivity artifacts validation result status.
message ValidateExclusivityResponse {
  // The validation result.
  //
  // * `OK` means that exclusivity is validated, assuming the manifest produced
  //    by GenerateExclusivityManifest is successfully applied.
  // * `ALREADY_EXISTS` means that the Membership CRD is already owned by
  //    another Hub. See `status.message` for more information.
  google.rpc.Status status = 1;
}

// The request to generate the manifests for exclusivity artifacts.
message GenerateExclusivityManifestRequest {
  // Required. The Membership resource name in the format
  // `projects/*/locations/*/memberships/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gkehub.googleapis.com/Membership"
    }
  ];

  // Optional. The YAML manifest of the membership CRD retrieved by
  // `kubectl get customresourcedefinitions membership`.
  // Leave empty if the resource does not exist.
  string crd_manifest = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The YAML manifest of the membership CR retrieved by
  // `kubectl get memberships membership`.
  // Leave empty if the resource does not exist.
  string cr_manifest = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response of the exclusivity artifacts manifests for the client to apply.
message GenerateExclusivityManifestResponse {
  // The YAML manifest of the membership CRD to apply if a newer version of the
  // CRD is available. Empty if no update needs to be applied.
  string crd_manifest = 1;

  // The YAML manifest of the membership CR to apply if a new version of the
  // CR is available. Empty if no update needs to be applied.
  string cr_manifest = 2;
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_detail = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool cancel_requested = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
