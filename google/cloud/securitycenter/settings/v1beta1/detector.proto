// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.settings.v1beta1;

import "google/api/field_behavior.proto";
import "google/cloud/securitycenter/settings/v1beta1/billing_settings.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.SecurityCenter.Settings.V1Beta1";
option go_package = "cloud.google.com/go/securitycenter/settings/apiv1beta1/settingspb;settingspb";
option java_multiple_files = true;
option java_outer_classname = "DetectorsProto";
option java_package = "com.google.cloud.securitycenter.settings.v1beta1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\Settings\\V1beta1";
option ruby_package = "Google::Cloud::SecurityCenter::Settings::V1beta1";

// Detector is a set of detectors or scanners act as individual checks done
// within a component e.g. bad IP, bad domains, IAM anomaly, cryptomining, open
// firewall, etc. Detector is independent of Organization, meaning each detector
// must be defined for a given Security Center component under a specified
// billing tier. Organizations can configure the list of detectors based on
// their subscribed billing tier.
//
// Defines a detector, its billing tier and any applicable labels.
message Detector {
  // Output only. Detector Identifier
  string detector = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Component that supports detector type.  Multiple components may support the
  // same detector.
  string component = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The billing tier may be different for a detector of the same name in
  // another component.
  BillingTier billing_tier = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Google curated detector labels. These are alphanumeric tags that are not
  // necessarily human readable. Labels can be used to group detectors together
  // in the future. An example might be tagging all detectors “PCI” that help
  // with PCI compliance.
  repeated string detector_labels = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}
