# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "settings_proto",
    srcs = [
        "billing_settings.proto",
        "component_settings.proto",
        "detector.proto",
        "securitycenter_settings_service.proto",
        "settings.proto",
        "sink_settings.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "settings_proto_with_info",
    deps = [
        ":settings_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "settings_java_proto",
    deps = [":settings_proto"],
)

java_grpc_library(
    name = "settings_java_grpc",
    srcs = [":settings_proto"],
    deps = [":settings_java_proto"],
)

java_gapic_library(
    name = "settings_java_gapic",
    srcs = [":settings_proto_with_info"],
    grpc_service_config = "securitycenter_settings_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "securitycenter_settings.yaml",
    test_deps = [
        ":settings_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":settings_java_proto",
    ],
)

java_gapic_test(
    name = "settings_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsServiceClientTest",
    ],
    runtime_deps = [":settings_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-securitycenter-settings-v1beta1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":settings_java_gapic",
        ":settings_java_grpc",
        ":settings_java_proto",
        ":settings_proto",
    ],
)

go_proto_library(
    name = "settings_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/securitycenter/settings/apiv1beta1/settingspb",
    protos = [":settings_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "settings_go_gapic",
    srcs = [":settings_proto_with_info"],
    grpc_service_config = "securitycenter_settings_grpc_service_config.json",
    importpath = "cloud.google.com/go/securitycenter/settings/apiv1beta1;settings",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = False,
    service_yaml = "securitycenter_settings.yaml",
    transport = "grpc+rest",
    deps = [
        ":settings_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-securitycenter-settings-v1beta1-go",
    deps = [
        ":settings_go_gapic",
        ":settings_go_gapic_srcjar-snippets.srcjar",
        ":settings_go_gapic_srcjar-test.srcjar",
        ":settings_go_proto",
    ],
)

py_gapic_library(
    name = "settings_py_gapic",
    srcs = [":settings_proto"],
    grpc_service_config = "securitycenter_settings_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "securitycenter_settings.yaml",
    transport = "grpc",
)

py_test(
    name = "settings_py_gapic_test",
    srcs = [
        "settings_py_gapic_pytest.py",
        "settings_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":settings_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "securitycenter-settings-v1beta1-py",
    deps = [
        ":settings_py_gapic",
    ],
)

php_proto_library(
    name = "settings_php_proto",
    deps = [":settings_proto"],
)

php_gapic_library(
    name = "settings_php_gapic",
    srcs = [":settings_proto_with_info"],
    grpc_service_config = "securitycenter_settings_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "securitycenter_settings.yaml",
    transport = "grpc+rest",
    deps = [":settings_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-securitycenter-settings-v1beta1-php",
    deps = [
        ":settings_php_gapic",
        ":settings_php_proto",
    ],
)

nodejs_gapic_library(
    name = "settings_nodejs_gapic",
    src = ":settings_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "securitycenter_settings_grpc_service_config.json",
    package = "google.cloud.securitycenter.settings.v1beta1",
    rest_numeric_enums = False,
    service_yaml = "securitycenter_settings.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "securitycenter-settings-v1beta1-nodejs",
    deps = [
        ":settings_nodejs_gapic",
        ":settings_proto",
    ],
)

ruby_proto_library(
    name = "settings_ruby_proto",
    deps = [":settings_proto"],
)

ruby_grpc_library(
    name = "settings_ruby_grpc",
    srcs = [":settings_proto"],
    deps = [":settings_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "settings_ruby_gapic",
    srcs = [":settings_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-settings-v1beta1"],
    rest_numeric_enums = False,
    service_yaml = "securitycenter_settings.yaml",
    deps = [
        ":settings_ruby_grpc",
        ":settings_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-securitycenter-settings-v1beta1-ruby",
    deps = [
        ":settings_ruby_gapic",
        ":settings_ruby_grpc",
        ":settings_ruby_proto",
    ],
)

csharp_proto_library(
    name = "settings_csharp_proto",
    deps = [":settings_proto"],
)

csharp_grpc_library(
    name = "settings_csharp_grpc",
    srcs = [":settings_proto"],
    deps = [":settings_csharp_proto"],
)

csharp_gapic_library(
    name = "settings_csharp_gapic",
    srcs = [":settings_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "securitycenter_settings_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "securitycenter_settings.yaml",
    deps = [
        ":settings_csharp_grpc",
        ":settings_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-securitycenter-settings-v1beta1-csharp",
    deps = [
        ":settings_csharp_gapic",
        ":settings_csharp_grpc",
        ":settings_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
