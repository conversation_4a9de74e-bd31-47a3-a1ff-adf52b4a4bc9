// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.settings.v1beta1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.SecurityCenter.Settings.V1Beta1";
option go_package = "cloud.google.com/go/securitycenter/settings/apiv1beta1/settingspb;settingspb";
option java_multiple_files = true;
option java_outer_classname = "BillingSettingsProto";
option java_package = "com.google.cloud.securitycenter.settings.v1beta1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\Settings\\V1beta1";
option ruby_package = "Google::Cloud::SecurityCenter::Settings::V1beta1";

// If this field is populated and billing_tier is STANDARD, this is
// indication of a point in the _past_ when a PREMIUM access ended.

// Billing settings
message BillingSettings {
  // Output only. Billing tier selected by customer
  BillingTier billing_tier = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of billing method
  BillingType billing_type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The absolute point in time when the subscription became effective.
  // Can be compared to expire_time value to determine full contract duration
  google.protobuf.Timestamp start_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The absolute point in time when the subscription expires.
  //
  // If this field is populated and billing_tier is STANDARD, this is
  // indication of a point in the _past_ when a PREMIUM access ended.
  google.protobuf.Timestamp expire_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Billing tier options
enum BillingTier {
  // Default value. This value is unused.
  BILLING_TIER_UNSPECIFIED = 0;

  // The standard billing tier.
  STANDARD = 1;

  // The premium billing tier.
  PREMIUM = 2;
}

// Billing type
enum BillingType {
  // Default billing type
  BILLING_TYPE_UNSPECIFIED = 0;

  // Subscription for Premium billing tier
  SUBSCRIPTION = 1;

  // Trial subscription for Premium billing tier
  TRIAL_SUBSCRIPTION = 2;

  // Alpha customer for Premium billing tier
  ALPHA = 3;
}
