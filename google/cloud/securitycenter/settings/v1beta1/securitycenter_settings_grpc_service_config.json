{"methodConfig": [{"name": [{"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "GetServiceAccount"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "GetSettings"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "UpdateSettings"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "ResetSettings"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "BatchGetSettings"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "CalculateEffectiveSettings"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "BatchCalculateEffectiveSettings"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "GetComponentSettings"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "UpdateComponentSettings"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "ResetComponentSettings"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "CalculateEffectiveComponentSettings"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "ListDetectors"}, {"service": "google.cloud.securitycenter.settings.v1beta1.SecurityCenterSettingsService", "method": "ListComponents"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}