// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.settings.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securitycenter/settings/v1beta1/billing_settings.proto";
import "google/cloud/securitycenter/settings/v1beta1/component_settings.proto";
import "google/cloud/securitycenter/settings/v1beta1/sink_settings.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.SecurityCenter.Settings.V1Beta1";
option go_package = "cloud.google.com/go/securitycenter/settings/apiv1beta1/settingspb;settingspb";
option java_multiple_files = true;
option java_outer_classname = "SettingsProto";
option java_package = "com.google.cloud.securitycenter.settings.v1beta1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\Settings\\V1beta1";
option ruby_package = "Google::Cloud::SecurityCenter::Settings::V1beta1";

// Common configuration settings for all of Security Center.
message Settings {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/Settings"
    pattern: "organizations/{organization}/settings"
    pattern: "folders/{folder}/settings"
    pattern: "projects/{project}/settings"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/settings"
    pattern: "projects/{project}/regions/{region}/clusters/{cluster}/settings"
    pattern: "projects/{project}/zones/{zone}/clusters/{cluster}/settings"
  };

  // The DetectorGroupSettings define the configuration for a detector group.
  message DetectorGroupSettings {
    // The state determines if the group is enabled or not.
    ComponentEnablementState state = 1;
  }

  // Defines the onboarding states for SCC
  //
  // Potentially is just an indicator that a user has reviewed some subset of
  // our configuration surface, even if it's still currently set to its
  // API-default state.
  enum OnboardingState {
    // No onboarding state has been set. Should not be seen in practice, but
    // should be functionally equivalent to DISABLED.
    ONBOARDING_STATE_UNSPECIFIED = 0;

    // SCC is fully on boarded
    ENABLED = 1;

    // SCC has been disabled after being on boarded
    DISABLED = 2;

    // SCC's onboarding tier has been explicitly set
    BILLING_SELECTED = 3;

    // SCC's CTD FindingsProviders have been chosen
    PROVIDERS_SELECTED = 4;

    // SCC's Service-Resource mappings have been set
    RESOURCES_SELECTED = 5;

    // SCC's core Service Account was created
    ORG_SERVICE_ACCOUNT_CREATED = 6;
  }

  // The relative resource name of the settings resource.
  // Formats:
  //  * `organizations/{organization}/settings`
  //  * `folders/{folder}/settings`
  //  * `projects/{project}/settings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/settings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/settings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/settings`
  string name = 1;

  // Billing settings
  BillingSettings billing_settings = 2;

  // An enum representing the current on boarding state of SCC.
  OnboardingState state = 3;

  // Output only. The organization-level service account to be used for security center
  // components. The component must have permission to "act as" the service
  // account.
  string org_service_account = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Sink settings.
  SinkSettings sink_settings = 6;

  // The settings for detectors and/or scanners.
  map<string, ComponentSettings> component_settings = 7;

  // Detector group settings for all Security Center components.
  // The key is the name of the detector group and the value is the settings for
  // that group.
  map<string, DetectorGroupSettings> detector_group_settings = 8;

  // A fingerprint used for optimistic concurrency. If none is provided
  // on updates then the existing metadata will be blindly overwritten.
  string etag = 9;

  // Output only. The time these settings were last updated.
  google.protobuf.Timestamp update_time = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}
