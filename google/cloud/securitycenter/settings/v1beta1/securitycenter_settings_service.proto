// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.settings.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securitycenter/settings/v1beta1/component_settings.proto";
import "google/cloud/securitycenter/settings/v1beta1/detector.proto";
import "google/cloud/securitycenter/settings/v1beta1/settings.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.SecurityCenter.Settings.V1Beta1";
option go_package = "cloud.google.com/go/securitycenter/settings/apiv1beta1/settingspb;settingspb";
option java_multiple_files = true;
option java_outer_classname = "SettingsServiceProto";
option java_package = "com.google.cloud.securitycenter.settings.v1beta1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\Settings\\V1beta1";
option ruby_package = "Google::Cloud::SecurityCenter::Settings::V1beta1";

// ## API Overview
//
// The SecurityCenterSettingsService is a sub-api of
// `securitycenter.googleapis.com`. The service provides methods to manage
// Security Center Settings, and Component Settings for GCP organizations,
// folders, projects, and clusters.
service SecurityCenterSettingsService {
  option (google.api.default_host) = "securitycenter.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Retrieves the organizations service account, if it exists, otherwise it
  // creates the organization service account. This API is idempotent and
  // will only create a service account once. On subsequent calls it will
  // return the previously created service account.  SHA, SCC and CTD Infra
  // Automation will use this SA.  This SA will not have any permissions when
  // created.  The UI will provision this via IAM or the user will using
  // their own internal process. This API only creates SAs on the organization.
  // Folders are not supported and projects will use per-project SAs associated
  // with APIs enabled on a project. This API will be called by the UX
  // onboarding workflow.
  rpc GetServiceAccount(GetServiceAccountRequest) returns (ServiceAccount) {
    option (google.api.http) = {
      get: "/settings/v1beta1/{name=organizations/*/serviceAccount}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the Settings.
  rpc GetSettings(GetSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      get: "/settings/v1beta1/{name=organizations/*/settings}"
      additional_bindings {
        get: "/settings/v1beta1/{name=folders/*/settings}"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/settings}"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/locations/*/clusters/*/settings}"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/regions/*/clusters/*/settings}"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/zones/*/clusters/*/settings}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the Settings.
  rpc UpdateSettings(UpdateSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      patch: "/settings/v1beta1/{settings.name=organizations/*/settings}"
      body: "settings"
      additional_bindings {
        patch: "/settings/v1beta1/{settings.name=folders/*/settings}"
        body: "settings"
      }
      additional_bindings {
        patch: "/settings/v1beta1/{settings.name=projects/*/settings}"
        body: "settings"
      }
      additional_bindings {
        patch: "/settings/v1beta1/{settings.name=projects/*/locations/*/clusters/*/settings}"
        body: "settings"
      }
      additional_bindings {
        patch: "/settings/v1beta1/{settings.name=projects/*/regions/*/clusters/*/settings}"
        body: "settings"
      }
      additional_bindings {
        patch: "/settings/v1beta1/{settings.name=projects/*/zones/*/clusters/*/settings}"
        body: "settings"
      }
    };
    option (google.api.method_signature) = "settings,update_mask";
  }

  // Reset the organization, folder or project's settings and return
  // the settings of just that resource to the default.
  //
  // Settings are present at the organization, folder, project, and cluster
  // levels. Using Reset on a sub-organization level will remove that resource's
  // override and result in the parent's settings being used (eg: if Reset on a
  // cluster, project settings will be used).
  //
  // Using Reset on organization will remove the override that was set and
  // result in default settings being used.
  rpc ResetSettings(ResetSettingsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/settings/v1beta1/{name=organizations/*/settings}:reset"
      body: "*"
      additional_bindings {
        post: "/settings/v1beta1/{name=folders/*/settings}:reset"
        body: "*"
      }
      additional_bindings {
        post: "/settings/v1beta1/{name=projects/*/settings}:reset"
        body: "*"
      }
      additional_bindings {
        post: "/settings/v1beta1/{name=projects/*/locations/*/clusters/*/settings}:reset"
        body: "*"
      }
      additional_bindings {
        post: "/settings/v1beta1/{name=projects/*/regions/*/clusters/*/settings}:reset"
        body: "*"
      }
      additional_bindings {
        post: "/settings/v1beta1/{name=projects/*/zones/*/clusters/*/settings}:reset"
        body: "*"
      }
    };
  }

  // Gets a list of settings.
  rpc BatchGetSettings(BatchGetSettingsRequest) returns (BatchGetSettingsResponse) {
    option (google.api.http) = {
      get: "/settings/v1beta1/{parent=organizations/*}/settings:batchGet"
    };
  }

  // CalculateEffectiveSettings looks up all of the Security Center
  // Settings resources in the GCP resource hierarchy, and calculates the
  // effective settings on that resource by applying the following rules:
  //  * Settings provided closer to the target resource take precedence over
  //    those further away (e.g. folder will override organization level
  //    settings).
  //  * Product defaults can be overridden at org, folder, project, and cluster
  //  levels.
  //  * Detectors will be filtered out if they belong to a billing tier the
  //  customer
  //    has not configured.
  rpc CalculateEffectiveSettings(CalculateEffectiveSettingsRequest) returns (Settings) {
    option (google.api.http) = {
      get: "/settings/v1beta1/{name=organizations/*/effectiveSettings}:calculate"
      additional_bindings {
        get: "/settings/v1beta1/{name=folders/*/effectiveSettings}:calculate"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/effectiveSettings}:calculate"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/locations/*/clusters/*/effectiveSettings}:calculate"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/regions/*/clusters/*/effectiveSettings}:calculate"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/zones/*/clusters/*/effectiveSettings}:calculate"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a list of effective settings.
  rpc BatchCalculateEffectiveSettings(BatchCalculateEffectiveSettingsRequest) returns (BatchCalculateEffectiveSettingsResponse) {
    option (google.api.http) = {
      post: "/settings/v1beta1/{parent=organizations/*}/effectiveSettings:batchCalculate"
      body: "*"
    };
  }

  // Gets the Component Settings.
  rpc GetComponentSettings(GetComponentSettingsRequest) returns (ComponentSettings) {
    option (google.api.http) = {
      get: "/settings/v1beta1/{name=organizations/*/components/*/settings}"
      additional_bindings {
        get: "/settings/v1beta1/{name=folders/*/components/*/settings}"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/components/*/settings}"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/locations/*/clusters/*/components/*/settings}"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/regions/*/clusters/*/components/*/settings}"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/zones/*/clusters/*/components/*/settings}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the Component Settings.
  rpc UpdateComponentSettings(UpdateComponentSettingsRequest) returns (ComponentSettings) {
    option (google.api.http) = {
      patch: "/settings/v1beta1/{component_settings.name=organizations/*/components/*/settings}"
      body: "component_settings"
      additional_bindings {
        patch: "/settings/v1beta1/{component_settings.name=folders/*/components/*/settings}"
        body: "component_settings"
      }
      additional_bindings {
        patch: "/settings/v1beta1/{component_settings.name=projects/*/components/*/settings}"
        body: "component_settings"
      }
      additional_bindings {
        patch: "/settings/v1beta1/{component_settings.name=projects/*/locations/*/clusters/*/components/*/settings}"
        body: "component_settings"
      }
      additional_bindings {
        patch: "/settings/v1beta1/{component_settings.name=projects/*/regions/*/clusters/*/components/*/settings}"
        body: "component_settings"
      }
      additional_bindings {
        patch: "/settings/v1beta1/{component_settings.name=projects/*/zones/*/clusters/*/components/*/settings}"
        body: "component_settings"
      }
    };
    option (google.api.method_signature) = "component_settings,update_mask";
  }

  // Reset the organization, folder or project's component settings and return
  // the settings to the default. Settings are present at the
  // organization, folder and project levels. Using Reset for a folder or
  // project will remove the override that was set and result in the
  // organization-level settings being used.
  rpc ResetComponentSettings(ResetComponentSettingsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/settings/v1beta1/{name=organizations/*/components/*/settings}:reset"
      body: "*"
      additional_bindings {
        post: "/settings/v1beta1/{name=folders/*/components/*/settings}:reset"
        body: "*"
      }
      additional_bindings {
        post: "/settings/v1beta1/{name=projects/*/components/*/settings}:reset"
        body: "*"
      }
      additional_bindings {
        post: "/settings/v1beta1/{name=projects/*/locations/*/clusters/*/components/*/settings}:reset"
        body: "*"
      }
      additional_bindings {
        post: "/settings/v1beta1/{name=projects/*/regions/*/clusters/*/components/*/settings}:reset"
        body: "*"
      }
      additional_bindings {
        post: "/settings/v1beta1/{name=projects/*/zones/*/clusters/*/components/*/settings}:reset"
        body: "*"
      }
    };
  }

  // Gets the Effective Component Settings.
  rpc CalculateEffectiveComponentSettings(CalculateEffectiveComponentSettingsRequest) returns (ComponentSettings) {
    option (google.api.http) = {
      get: "/settings/v1beta1/{name=organizations/*/components/*/effectiveSettings}:calculate"
      additional_bindings {
        get: "/settings/v1beta1/{name=folders/*/components/*/effectiveSettings}:calculate"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/components/*/effectiveSettings}:calculate"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/locations/*/clusters/*/components/*/effectiveSettings}:calculate"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/regions/*/clusters/*/components/*/effectiveSettings}:calculate"
      }
      additional_bindings {
        get: "/settings/v1beta1/{name=projects/*/zones/*/clusters/*/components/*/effectiveSettings}:calculate"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves an unordered list of available detectors.
  rpc ListDetectors(ListDetectorsRequest) returns (ListDetectorsResponse) {
    option (google.api.http) = {
      get: "/settings/v1beta1/{parent=organizations/*}/detectors"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves an unordered list of available SCC components.
  rpc ListComponents(ListComponentsRequest) returns (ListComponentsResponse) {
    option (google.api.http) = {
      get: "/settings/v1beta1/{parent=organizations/*}/components"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for GetServiceAccount.
message GetServiceAccountRequest {
  // Required. The relative resource name of the service account resource.
  // Format:
  //  * `organizations/{organization}/serviceAccount`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/ServiceAccount"
    }
  ];
}

// An organization-level service account to be used by threat detection
// components.
message ServiceAccount {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/ServiceAccount"
    pattern: "organizations/{organization}/serviceAccount"
  };

  // The relative resource name of the service account resource.
  // Format:
  //  * `organizations/{organization}/serviceAccount`
  string name = 1;

  // Security Center managed service account for the organization
  // example <EMAIL>
  // This service_account will be stored in the ComponentSettings field for the
  // SCC, SHA, and Infra Automation components.
  string service_account = 2;
}

// Request message for GetSettings.
message GetSettingsRequest {
  // Required. The name of the settings to retrieve.
  // Formats:
  //  * `organizations/{organization}/settings`
  //  * `folders/{folder}/settings`
  //  * `projects/{project}/settings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/settings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/settings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/settings`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Settings"
    }
  ];
}

// Request message for UpdateSettings.
message UpdateSettingsRequest {
  // Required. The settings to update.
  //
  // The settings' `name` field is used to identify the settings to be updated.
  // Formats:
  //  * `organizations/{organization}/settings`
  //  * `folders/{folder}/settings`
  //  * `projects/{project}/settings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/settings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/settings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/settings`
  Settings settings = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated on the settings.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for ResetSettings.
message ResetSettingsRequest {
  // Required. The name of the settings to reset.
  // Formats:
  //  * `organizations/{organization}/settings`
  //  * `folders/{folder}/settings`
  //  * `projects/{project}/settings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/settings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/settings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/settings`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Settings"
    }
  ];

  // A fingerprint used for optimistic concurrency. If none is provided,
  // then the existing settings will be blindly overwritten.
  string etag = 2;
}

// Request message for BatchGetSettings.
message BatchGetSettingsRequest {
  // Required. The relative resource name of the organization shared by all of the
  // settings being retrieved.
  // Format:
  //  * `organizations/{organization}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // The names of the settings to retrieve.
  // A maximum of 1000 settings can be retrieved in a batch.
  // Formats:
  //  * `organizations/{organization}/settings`
  //  * `folders/{folder}/settings`
  //  * `projects/{project}/settings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/settings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/settings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/settings`
  repeated string names = 2;
}

// Response message for BatchGetSettings.
message BatchGetSettingsResponse {
  // Settings requested.
  repeated Settings settings = 1;
}

// Request message for CalculateEffectiveSettings.
message CalculateEffectiveSettingsRequest {
  // Required. The name of the effective settings to retrieve.
  // Formats:
  //  * `organizations/{organization}/effectiveSettings`
  //  * `folders/{folder}/effectiveSettings`
  //  * `projects/{project}/effectiveSettings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/effectiveSettings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/effectiveSettings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/effectiveSettings`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Settings"
    }
  ];
}

// Request message for BatchGetEffectiveSettings.
message BatchCalculateEffectiveSettingsRequest {
  // Required. The relative resource name of the organization shared by all of the
  // settings being retrieved.
  // Format:
  //  * `organizations/{organization}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // The requests specifying the effective settings to retrieve.
  // A maximum of 1000 effective settings can be retrieved in a batch.
  repeated CalculateEffectiveSettingsRequest requests = 2;
}

// Response message for BatchGetEffectiveSettings.
message BatchCalculateEffectiveSettingsResponse {
  // Settings requested.
  repeated Settings settings = 1;
}

// Request message for GetComponentSettings.
message GetComponentSettingsRequest {
  // Required. The component settings to retrieve.
  //
  // Formats:
  //  * `organizations/{organization}/components/{component}/settings`
  //  * `folders/{folder}/components/{component}/settings`
  //  * `projects/{project}/components/{component}/settings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/components/{component}/settings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/components/{component}/settings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/components/{component}/settings`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/ComponentSettings"
    }
  ];
}

// Request message for UpdateComponentSettings.
message UpdateComponentSettingsRequest {
  // Required. The component settings to update.
  //
  // The component settings' `name` field is used to identify the component
  // settings to be updated. Formats:
  //  * `organizations/{organization}/components/{component}/settings`
  //  * `folders/{folder}/components/{component}/settings`
  //  * `projects/{project}/components/{component}/settings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/components/{component}/settings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/components/{component}/settings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/components/{component}/settings`
  ComponentSettings component_settings = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated on the component settings resource.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for ResetComponentSettings.
message ResetComponentSettingsRequest {
  // Required. The component settings to reset.
  //
  // Formats:
  //  * `organizations/{organization}/components/{component}/settings`
  //  * `folders/{folder}/components/{component}/settings`
  //  * `projects/{project}/components/{component}/settings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/components/{component}/settings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/components/{component}/settings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/components/{component}/settings`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/ComponentSettings"
    }
  ];

  // An fingerprint used for optimistic concurrency. If none is provided,
  // then the existing settings will be blindly overwritten.
  string etag = 2;
}

// Request message for CalculateEffectiveComponentSettings.
message CalculateEffectiveComponentSettingsRequest {
  // Required. The effective component settings to retrieve.
  //
  // Formats:
  //  * `organizations/{organization}/components/{component}/settings`
  //  * `folders/{folder}/components/{component}/settings`
  //  * `projects/{project}/components/{component}/settings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/components/{component}/settings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/components/{component}/settings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/components/{component}/settings`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/ComponentSettings"
    }
  ];
}

// Request message for ListDetectors.
message ListDetectorsRequest {
  // Required. The parent, which owns this collection of detectors.
  // Format:
  //  * `organizations/{organization}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // Filters to apply on the response. Filters can be applied on:
  //  * components
  //  * labels
  //  * billing tiers
  //
  // Component filters will retrieve only detectors for the components
  // specified. Label filters will retrieve only detectors that match one of the
  // labels specified. Billing tier filters will retrieve only detectors for
  // that billing tier.
  //
  // The filters
  string filter = 2;

  // The maximum number of detectors to return. The service may return fewer
  // than this value. If unspecified, at most 100 detectors will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 3;

  // A page token, received from a previous `ListDetectors` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListDetectors` must
  // match the call that provided the page token.
  string page_token = 4;
}

// Response message for ListDetectors.
message ListDetectorsResponse {
  // The detectors from the specified organization.
  repeated Detector detectors = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for ListComponents.
message ListComponentsRequest {
  // Required. The parent, which owns this collection of components.
  // Format:
  //  * `organizations/{organization}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // The maximum number of components to return. The service may return fewer
  // than this value. If unspecified, at most 100 components will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListComponents` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListComponents` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for ListComponents.
message ListComponentsResponse {
  // The components from the specified organization.
  repeated string components = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
