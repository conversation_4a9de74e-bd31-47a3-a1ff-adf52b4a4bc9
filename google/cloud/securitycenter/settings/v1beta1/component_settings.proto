// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.settings.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.SecurityCenter.Settings.V1Beta1";
option go_package = "cloud.google.com/go/securitycenter/settings/apiv1beta1/settingspb;settingspb";
option java_multiple_files = true;
option java_outer_classname = "ComponentSettingsProto";
option java_package = "com.google.cloud.securitycenter.settings.v1beta1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\Settings\\V1beta1";
option ruby_package = "Google::Cloud::SecurityCenter::Settings::V1beta1";

// Component Settings for Security Command Center
message ComponentSettings {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/ComponentSettings"
    pattern: "organizations/{organization}/components/{component}/settings"
    pattern: "folders/{folder}/components/{component}/settings"
    pattern: "projects/{project}/components/{component}/settings"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/components/{component}/settings"
    pattern: "projects/{project}/regions/{region}/clusters/{cluster}/components/{component}/settings"
    pattern: "projects/{project}/zones/{zone}/clusters/{cluster}/components/{component}/settings"
  };

  // Settings for each detector.
  message DetectorSettings {
    // ENABLE to enable component, DISABLE to disable and INHERIT to inherit
    // setting from ancestors.
    ComponentEnablementState state = 1;
  }

  // The relative resource name of the component settings.
  // Formats:
  //  * `organizations/{organization}/components/{component}/settings`
  //  * `folders/{folder}/components/{component}/settings`
  //  * `projects/{project}/components/{component}/settings`
  //  * `projects/{project}/locations/{location}/clusters/{cluster}/components/{component}/settings`
  //  * `projects/{project}/regions/{region}/clusters/{cluster}/components/{component}/settings`
  //  * `projects/{project}/zones/{zone}/clusters/{cluster}/components/{component}/settings`
  string name = 1;

  // ENABLE to enable component, DISABLE to disable and INHERIT to inherit
  // setting from ancestors.
  ComponentEnablementState state = 2;

  // Output only. The service account to be used for security center component.
  // The component must have permission to "act as" the service account.
  string project_service_account = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Settings for detectors.  Not all detectors must have settings present at
  // each and every level in the hierarchy.  If it is not present the setting
  // will be inherited from its ancestors folders, organizations or the
  // defaults.
  map<string, DetectorSettings> detector_settings = 4;

  // Output only. An fingerprint used for optimistic concurrency. If none is provided
  // on updates then the existing metadata will be blindly overwritten.
  string etag = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time these settings were last updated.
  google.protobuf.Timestamp update_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Component specific settings.  This must match the component value.
  oneof specific_settings {
    // Container Threate Detection specific settings
    // For component, expect CONTAINER_THREAT_DETECTION
    ContainerThreatDetectionSettings container_threat_detection_settings = 41;

    // Event Threat Detection specific settings
    // For component, expect EVENT_THREAT_DETECTION
    EventThreatDetectionSettings event_threat_detection_settings = 42;

    // Security Health Analytics specific settings
    // For component, expect SECURITY_HEALTH_ANALYTICS
    SecurityHealthAnalyticsSettings security_health_analytics_settings = 44;

    // Web Security Scanner specific settings
    // For component, expect WEB_SECURITY_SCANNER
    WebSecurityScanner web_security_scanner_settings = 40;
  }
}

// User specified settings for Web Security Scanner
message WebSecurityScanner {

}

// User specified settings for KTD
message ContainerThreatDetectionSettings {

}

// User specified settings for ETD
message EventThreatDetectionSettings {

}

// User specified settings for Security Health Analytics
message SecurityHealthAnalyticsSettings {
  // Settings for "NON_ORG_IAM_MEMBER" scanner.
  message NonOrgIamMemberSettings {
    // User emails ending in the provided identities are allowed to have IAM
    // permissions on a project or the organization. Otherwise a finding will
    // be created.
    // A valid identity can be:
    //   *  a domain that starts with "@", e.g. "@yourdomain.com".
    //   *  a fully specified email address that does not start with "@", e.g.
    //   "<EMAIL>"
    // Regular expressions are not supported.
    // Service accounts are not examined by the scanner and will be omitted if
    // added to the list.
    // If not specified, only Gmail accounts will be considered as non-approved.
    repeated string approved_identities = 1;
  }

  // Settings for "ADMIN_SERVICE_ACCOUNT" scanner.
  message AdminServiceAccountSettings {
    // User-created service accounts ending in the provided identities are
    // allowed to have Admin, Owner or Editor roles granted to them. Otherwise
    // a finding will be created.
    // A valid identity can be:
    //   *  a partilly specified service account that starts with "@", e.g.
    //   "@myproject.iam.gserviceaccount.com". This approves all the service
    //   accounts suffixed with the specified identity.
    //   *  a fully specified service account that does not start with "@", e.g.
    //   "<EMAIL>".
    // Google-created service accounts are all approved.
    repeated string approved_identities = 1;
  }

  // Settings for "NON_ORG_IAM_MEMBER" scanner.
  NonOrgIamMemberSettings non_org_iam_member_settings = 1;

  // Settings for "ADMIN_SERVICE_ACCOUNT" scanner.
  AdminServiceAccountSettings admin_service_account_settings = 2;
}

// Valid states for a component
enum ComponentEnablementState {
  // No state specified, equivalent of INHERIT
  COMPONENT_ENABLEMENT_STATE_UNSPECIFIED = 0;

  // Disable the component
  DISABLE = 1;

  // Enable the component
  ENABLE = 2;

  // Inherit the state from resources parent folder or organization.
  INHERIT = 3;
}
