// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.settings.v1beta1;


option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.SecurityCenter.Settings.V1Beta1";
option go_package = "cloud.google.com/go/securitycenter/settings/apiv1beta1/settingspb;settingspb";
option java_multiple_files = true;
option java_outer_classname = "SinkProto";
option java_package = "com.google.cloud.securitycenter.settings.v1beta1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\Settings\\V1beta1";
option ruby_package = "Google::Cloud::SecurityCenter::Settings::V1beta1";

// Sink Settings for Security Command Center
message SinkSettings {
  // The resource name of the project to send logs to. This project must be
  // part of the same organization where the Security Center API is
  // enabled. The format is `projects/{project}`. If it is empty, we do
  // not output logs. If a project ID is provided it will be normalized to a
  // project number.
  string logging_sink_project = 1;
}
