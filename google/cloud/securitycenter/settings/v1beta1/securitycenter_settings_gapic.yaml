type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  python:
    package_name: google.cloud.securitycenter.settings_v1beta1.gapic
  go:
    package_name: cloud.google.com/go/securitycenter/settings/apiv1beta1
  csharp:
    package_name: Google.Cloud.SecurityCenter.Settings.V1Beta1
  ruby:
    package_name: Google::Cloud::SecurityCenter::Settings::V1beta1
  php:
    package_name: Google\Cloud\SecurityCenter\Settings\V1beta1
  nodejs:
    package_name: security-center.settings.v1beta1
    domain_layer_location: google-cloud
