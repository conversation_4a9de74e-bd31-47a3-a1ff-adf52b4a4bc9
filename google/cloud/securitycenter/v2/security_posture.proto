// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "SecurityPostureProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// Represents a posture that is deployed on Google Cloud by the
// Security Command Center Posture Management service.
// A posture contains one or more policy sets. A policy set is a
// group of policies that enforce a set of security rules on Google
// Cloud.
message SecurityPosture {
  // The policy field that violates the deployed posture and its expected and
  // detected values.
  message PolicyDriftDetails {
    // The name of the updated field, for example
    // constraint.implementation.policy_rules[0].enforce
    string field = 1;

    // The value of this field that was configured in a posture, for example,
    // `true` or `allowed_values={"projects/29831892"}`.
    string expected_value = 2;

    // The detected value that violates the deployed posture, for example,
    // `false` or `allowed_values={"projects/22831892"}`.
    string detected_value = 3;
  }

  // Name of the posture, for example, `CIS-Posture`.
  string name = 1;

  // The version of the posture, for example, `c7cfa2a8`.
  string revision_id = 2;

  // The project, folder, or organization on which the posture is deployed,
  // for example, `projects/{project_number}`.
  string posture_deployment_resource = 3;

  // The name of the posture deployment, for example,
  // `organizations/{org_id}/posturedeployments/{posture_deployment_id}`.
  string posture_deployment = 4;

  // The name of the updated policy, for example,
  // `projects/{project_id}/policies/{constraint_name}`.
  string changed_policy = 5;

  // The name of the updated policy set, for example, `cis-policyset`.
  string policy_set = 6;

  // The ID of the updated policy, for example, `compute-policy-1`.
  string policy = 7;

  // The details about a change in an updated policy that violates the deployed
  // posture.
  repeated PolicyDriftDetails policy_drift_details = 8;
}
