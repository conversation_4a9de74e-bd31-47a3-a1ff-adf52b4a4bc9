// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "MitreAttackProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// MITRE ATT&CK tactics and techniques related to this finding.
// See: https://attack.mitre.org
message MitreAttack {
  // MITRE ATT&CK tactics that can be referenced by SCC findings.
  // See: https://attack.mitre.org/tactics/enterprise/
  enum Tactic {
    // Unspecified value.
    TACTIC_UNSPECIFIED = 0;

    // TA0043
    RECONNAISSANCE = 1;

    // TA0042
    RESOURCE_DEVELOPMENT = 2;

    // TA0001
    INITIAL_ACCESS = 5;

    // TA0002
    EXECUTION = 3;

    // TA0003
    PERSISTENCE = 6;

    // TA0004
    PRIVILEGE_ESCALATION = 8;

    // TA0005
    DEFENSE_EVASION = 7;

    // TA0006
    CREDENTIAL_ACCESS = 9;

    // TA0007
    DISCOVERY = 10;

    // TA0008
    LATERAL_MOVEMENT = 11;

    // TA0009
    COLLECTION = 12;

    // TA0011
    COMMAND_AND_CONTROL = 4;

    // TA0010
    EXFILTRATION = 13;

    // TA0040
    IMPACT = 14;
  }

  // MITRE ATT&CK techniques that can be referenced by SCC findings.
  // See: https://attack.mitre.org/techniques/enterprise/
  // Next ID: 65
  enum Technique {
    // Unspecified value.
    TECHNIQUE_UNSPECIFIED = 0;

    // T1036
    MASQUERADING = 49;

    // T1036.005
    MATCH_LEGITIMATE_NAME_OR_LOCATION = 50;

    // T1037
    BOOT_OR_LOGON_INITIALIZATION_SCRIPTS = 37;

    // T1037.005
    STARTUP_ITEMS = 38;

    // T1046
    NETWORK_SERVICE_DISCOVERY = 32;

    // T1057
    PROCESS_DISCOVERY = 56;

    // T1059
    COMMAND_AND_SCRIPTING_INTERPRETER = 6;

    // T1059.004
    UNIX_SHELL = 7;

    // T1059.006
    PYTHON = 59;

    // T1068
    EXPLOITATION_FOR_PRIVILEGE_ESCALATION = 63;

    // T1069
    PERMISSION_GROUPS_DISCOVERY = 18;

    // T1069.003
    CLOUD_GROUPS = 19;

    // T1070.004
    INDICATOR_REMOVAL_FILE_DELETION = 64;

    // T1071
    APPLICATION_LAYER_PROTOCOL = 45;

    // T1071.004
    DNS = 46;

    // T1072
    SOFTWARE_DEPLOYMENT_TOOLS = 47;

    // T1078
    VALID_ACCOUNTS = 14;

    // T1078.001
    DEFAULT_ACCOUNTS = 35;

    // T1078.003
    LOCAL_ACCOUNTS = 15;

    // T1078.004
    CLOUD_ACCOUNTS = 16;

    // T1090
    PROXY = 9;

    // T1090.002
    EXTERNAL_PROXY = 10;

    // T1090.003
    MULTI_HOP_PROXY = 11;

    // T1098
    ACCOUNT_MANIPULATION = 22;

    // T1098.001
    ADDITIONAL_CLOUD_CREDENTIALS = 40;

    // T1098.004
    SSH_AUTHORIZED_KEYS = 23;

    // T1098.006
    ADDITIONAL_CONTAINER_CLUSTER_ROLES = 58;

    // T1105
    INGRESS_TOOL_TRANSFER = 3;

    // T1106
    NATIVE_API = 4;

    // T1110
    BRUTE_FORCE = 44;

    // T1129
    SHARED_MODULES = 5;

    // T1134
    ACCESS_TOKEN_MANIPULATION = 33;

    // T1134.001
    TOKEN_IMPERSONATION_OR_THEFT = 39;

    // T1190
    EXPLOIT_PUBLIC_FACING_APPLICATION = 27;

    // T1484
    DOMAIN_POLICY_MODIFICATION = 30;

    // T1485
    DATA_DESTRUCTION = 29;

    // T1489
    SERVICE_STOP = 52;

    // T1490
    INHIBIT_SYSTEM_RECOVERY = 36;

    // T1496
    RESOURCE_HIJACKING = 8;

    // T1498
    NETWORK_DENIAL_OF_SERVICE = 17;

    // T1526
    CLOUD_SERVICE_DISCOVERY = 48;

    // T1528
    STEAL_APPLICATION_ACCESS_TOKEN = 42;

    // T1531
    ACCOUNT_ACCESS_REMOVAL = 51;

    // T1539
    STEAL_WEB_SESSION_COOKIE = 25;

    // T1543
    CREATE_OR_MODIFY_SYSTEM_PROCESS = 24;

    // T1548
    ABUSE_ELEVATION_CONTROL_MECHANISM = 34;

    // T1552
    UNSECURED_CREDENTIALS = 13;

    // T1556
    MODIFY_AUTHENTICATION_PROCESS = 28;

    // T1562
    IMPAIR_DEFENSES = 31;

    // T1562.001
    DISABLE_OR_MODIFY_TOOLS = 55;

    // T1567
    EXFILTRATION_OVER_WEB_SERVICE = 20;

    // T1567.002
    EXFILTRATION_TO_CLOUD_STORAGE = 21;

    // T1568
    DYNAMIC_RESOLUTION = 12;

    // T1570
    LATERAL_TOOL_TRANSFER = 41;

    // T1578
    MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE = 26;

    // T1578.001
    CREATE_SNAPSHOT = 54;

    // T1580
    CLOUD_INFRASTRUCTURE_DISCOVERY = 53;

    // T1588
    OBTAIN_CAPABILITIES = 43;

    // T1595
    ACTIVE_SCANNING = 1;

    // T1595.001
    SCANNING_IP_BLOCKS = 2;

    // T1609
    CONTAINER_ADMINISTRATION_COMMAND = 60;

    // T1611
    ESCAPE_TO_HOST = 61;

    // T1613
    CONTAINER_AND_RESOURCE_DISCOVERY = 57;

    // T1649
    STEAL_OR_FORGE_AUTHENTICATION_CERTIFICATES = 62;
  }

  // The MITRE ATT&CK tactic most closely represented by this finding, if any.
  Tactic primary_tactic = 1;

  // The MITRE ATT&CK technique most closely represented by this finding, if
  // any. primary_techniques is a repeated field because there are multiple
  // levels of MITRE ATT&CK techniques.  If the technique most closely
  // represented by this finding is a sub-technique (e.g. `SCANNING_IP_BLOCKS`),
  // both the sub-technique and its parent technique(s) will be listed (e.g.
  // `SCANNING_IP_BLOCKS`, `ACTIVE_SCANNING`).
  repeated Technique primary_techniques = 2;

  // Additional MITRE ATT&CK tactics related to this finding, if any.
  repeated Tactic additional_tactics = 3;

  // Additional MITRE ATT&CK techniques related to this finding, if any, along
  // with any of their respective parent techniques.
  repeated Technique additional_techniques = 4;

  // The MITRE ATT&CK version referenced by the above fields. E.g. "8".
  string version = 5;
}
