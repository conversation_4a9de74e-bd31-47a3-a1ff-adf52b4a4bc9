// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "FileProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// File information about the related binary/library used by an executable, or
// the script used by a script interpreter
message File {
  // Path of the file in terms of underlying disk/partition identifiers.
  message DiskPath {
    // UUID of the partition (format
    // https://wiki.archlinux.org/title/persistent_block_device_naming#by-uuid)
    string partition_uuid = 1;

    // Relative path of the file in the partition as a JSON encoded string.
    // Example: /home/<USER>/executable_file.sh
    string relative_path = 2;
  }

  // Absolute path of the file as a JSON encoded string.
  string path = 1;

  // Size of the file in bytes.
  int64 size = 2;

  // SHA256 hash of the first hashed_size bytes of the file encoded as a
  // hex string.  If hashed_size == size, sha256 represents the SHA256 hash
  // of the entire file.
  string sha256 = 3;

  // The length in bytes of the file prefix that was hashed.  If
  // hashed_size == size, any hashes reported represent the entire
  // file.
  int64 hashed_size = 4;

  // True when the hash covers only a prefix of the file.
  bool partially_hashed = 5;

  // Prefix of the file contents as a JSON-encoded string.
  string contents = 6;

  // Path of the file in terms of underlying disk/partition identifiers.
  DiskPath disk_path = 7;
}
