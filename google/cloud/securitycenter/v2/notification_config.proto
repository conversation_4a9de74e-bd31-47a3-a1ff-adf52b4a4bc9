// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "NotificationConfigProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";
option (google.api.resource_definition) = {
  type: "pubsub.googleapis.com/Topic"
  pattern: "projects/{project}/topics/{topic}"
};

// Cloud Security Command Center (Cloud SCC) notification configs.
//
// A notification config is a Cloud SCC resource that contains the configuration
// to send notifications for create/update events of findings, assets and etc.
message NotificationConfig {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/NotificationConfig"
    pattern: "organizations/{organization}/locations/{location}/notificationConfigs/{notification_config}"
    pattern: "folders/{folder}/locations/{location}/notificationConfigs/{notification_config}"
    pattern: "projects/{project}/locations/{location}/notificationConfigs/{notification_config}"
    plural: "notificationConfigs"
    singular: "notificationConfig"
  };

  // The config for streaming-based notifications, which send each event as soon
  // as it is detected.
  message StreamingConfig {
    // Expression that defines the filter to apply across create/update events
    // of assets or findings as specified by the event type. The expression is a
    // list of zero or more restrictions combined via logical operators `AND`
    // and `OR`. Parentheses are supported, and `OR` has higher precedence than
    // `AND`.
    //
    // Restrictions have the form `<field> <operator> <value>` and may have a
    // `-` character in front of them to indicate negation. The fields map to
    // those defined in the corresponding resource.
    //
    // The supported operators are:
    //
    // * `=` for all value types.
    // * `>`, `<`, `>=`, `<=` for integer values.
    // * `:`, meaning substring matching, for strings.
    //
    // The supported value types are:
    //
    // * string literals in quotes.
    // * integer literals without quotes.
    // * boolean literals `true` and `false` without quotes.
    string filter = 1;
  }

  // Identifier. The relative resource name of this notification config. See:
  // https://cloud.google.com/apis/design/resource_names#relative_resource_name
  // The following list shows some examples:
  // +
  // `organizations/{organization_id}/locations/{location_id}/notificationConfigs/notify_public_bucket`
  // +
  // `folders/{folder_id}/locations/{location_id}/notificationConfigs/notify_public_bucket`
  // +
  // `projects/{project_id}/locations/{location_id}/notificationConfigs/notify_public_bucket`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The description of the notification config (max of 1024 characters).
  string description = 2;

  // The Pub/Sub topic to send notifications to. Its format is
  // "projects/[project_id]/topics/[topic]".
  string pubsub_topic = 3 [
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
  ];

  // Output only. The service account that needs "pubsub.topics.publish"
  // permission to publish to the Pub/Sub topic.
  string service_account = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The config for triggering notifications.
  oneof notify_config {
    // The config for triggering streaming-based notifications.
    StreamingConfig streaming_config = 5;
  }
}
