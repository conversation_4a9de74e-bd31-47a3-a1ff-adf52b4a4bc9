// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/api/routing.proto";
import "google/cloud/securitycenter/v2/attack_path.proto";
import "google/cloud/securitycenter/v2/bigquery_export.proto";
import "google/cloud/securitycenter/v2/external_system.proto";
import "google/cloud/securitycenter/v2/finding.proto";
import "google/cloud/securitycenter/v2/mute_config.proto";
import "google/cloud/securitycenter/v2/notification_config.proto";
import "google/cloud/securitycenter/v2/resource.proto";
import "google/cloud/securitycenter/v2/resource_value_config.proto";
import "google/cloud/securitycenter/v2/security_marks.proto";
import "google/cloud/securitycenter/v2/simulation.proto";
import "google/cloud/securitycenter/v2/source.proto";
import "google/cloud/securitycenter/v2/valued_resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "SecuritycenterServiceProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";
option (google.api.resource_definition) = {
  type: "securitycenter.googleapis.com/OrganizationLocation"
  pattern: "organizations/{organization}/locations/{location}"
};
option (google.api.resource_definition) = {
  type: "securitycenter.googleapis.com/FolderLocation"
  pattern: "folders/{folder}/locations/{location}"
};
option (google.api.resource_definition) = {
  type: "securitycenter.googleapis.com/OrganizationValuedResource"
  pattern: "organizations/{organization}/locations/{location}/simulations/{simulation}/valuedResources/{valued_resource}"
};

// V2 APIs for Security Center service.
service SecurityCenter {
  option (google.api.default_host) = "securitycenter.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a ResourceValueConfig for an organization. Maps user's tags to
  // difference resource values for use by the attack path simulation.
  rpc BatchCreateResourceValueConfigs(BatchCreateResourceValueConfigsRequest)
      returns (BatchCreateResourceValueConfigsResponse) {
    option (google.api.http) = {
      post: "/v2/{parent=organizations/*}/resourceValueConfigs:batchCreate"
      body: "*"
      additional_bindings {
        post: "/v2/{parent=organizations/*/locations/*}/resourceValueConfigs:batchCreate"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,requests";
  }

  // Kicks off an LRO to bulk mute findings for a parent based on a filter. If
  // no location is specified, findings are muted in global. The parent
  // can be either an organization, folder, or project. The findings matched by
  // the filter will be muted after the LRO is done.
  rpc BulkMuteFindings(BulkMuteFindingsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=organizations/*}/findings:bulkMute"
      body: "*"
      additional_bindings {
        post: "/v2/{parent=organizations/*/locations/*}/findings:bulkMute"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{parent=folders/*}/findings:bulkMute"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{parent=folders/*/locations/*}/findings:bulkMute"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{parent=projects/*}/findings:bulkMute"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*}/findings:bulkMute"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.securitycenter.v2.BulkMuteFindingsResponse"
      metadata_type: "google.protobuf.Empty"
    };
  }

  // Creates a BigQuery export.
  rpc CreateBigQueryExport(CreateBigQueryExportRequest)
      returns (BigQueryExport) {
    option (google.api.http) = {
      post: "/v2/{parent=organizations/*/locations/*}/bigQueryExports"
      body: "big_query_export"
      additional_bindings {
        post: "/v2/{parent=folders/*/locations/*}/bigQueryExports"
        body: "big_query_export"
      }
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*}/bigQueryExports"
        body: "big_query_export"
      }
    };
    option (google.api.method_signature) =
        "parent,big_query_export,big_query_export_id";
  }

  // Creates a finding in a location. The corresponding source must exist for
  // finding creation to succeed.
  rpc CreateFinding(CreateFindingRequest) returns (Finding) {
    option (google.api.http) = {
      post: "/v2/{parent=organizations/*/sources/*/locations/*}/findings"
      body: "finding"
      additional_bindings {
        post: "/v2/{parent=organizations/*/sources/*}/findings"
        body: "finding"
      }
    };
    option (google.api.method_signature) = "parent,finding,finding_id";
  }

  // Creates a mute config.
  rpc CreateMuteConfig(CreateMuteConfigRequest) returns (MuteConfig) {
    option (google.api.http) = {
      post: "/v2/{parent=organizations/*/locations/*}/muteConfigs"
      body: "mute_config"
      additional_bindings {
        post: "/v2/{parent=folders/*/locations/*}/muteConfigs"
        body: "mute_config"
      }
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*}/muteConfigs"
        body: "mute_config"
      }
      additional_bindings {
        post: "/v2/{parent=organizations/*}/muteConfigs"
        body: "mute_config"
      }
      additional_bindings {
        post: "/v2/{parent=folders/*}/muteConfigs"
        body: "mute_config"
      }
      additional_bindings {
        post: "/v2/{parent=projects/*}/muteConfigs"
        body: "mute_config"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}"
      }
      routing_parameters {
        field: "parent"
        path_template: "organizations/*/locations/{location=*}"
      }
      routing_parameters {
        field: "parent"
        path_template: "folders/*/locations/{location=*}"
      }
    };
    option (google.api.method_signature) = "parent,mute_config,mute_config_id";
  }

  // Creates a notification config.
  rpc CreateNotificationConfig(CreateNotificationConfigRequest)
      returns (NotificationConfig) {
    option (google.api.http) = {
      post: "/v2/{parent=organizations/*/locations/*}/notificationConfigs"
      body: "notification_config"
      additional_bindings {
        post: "/v2/{parent=folders/*/locations/*}/notificationConfigs"
        body: "notification_config"
      }
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*}/notificationConfigs"
        body: "notification_config"
      }
    };
    option (google.api.method_signature) =
        "parent,notification_config,config_id";
  }

  // Creates a source.
  rpc CreateSource(CreateSourceRequest) returns (Source) {
    option (google.api.http) = {
      post: "/v2/{parent=organizations/*}/sources"
      body: "source"
    };
    option (google.api.method_signature) = "parent,source";
  }

  // Deletes an existing BigQuery export.
  rpc DeleteBigQueryExport(DeleteBigQueryExportRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=organizations/*/locations/*/bigQueryExports/*}"
      additional_bindings {
        delete: "/v2/{name=folders/*/locations/*/bigQueryExports/*}"
      }
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/bigQueryExports/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes an existing mute config. If no location is specified, default is
  // global.
  rpc DeleteMuteConfig(DeleteMuteConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=organizations/*/muteConfigs/*}"
      additional_bindings {
        delete: "/v2/{name=organizations/*/locations/*/muteConfigs/*}"
      }
      additional_bindings { delete: "/v2/{name=folders/*/muteConfigs/*}" }
      additional_bindings {
        delete: "/v2/{name=folders/*/locations/*/muteConfigs/*}"
      }
      additional_bindings { delete: "/v2/{name=projects/*/muteConfigs/*}" }
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/muteConfigs/*}"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/muteConfigs/*"
      }
      routing_parameters {
        field: "name"
        path_template: "organizations/*/locations/{location=*}/muteConfigs/*"
      }
      routing_parameters {
        field: "name"
        path_template: "folders/*/locations/{location=*}/muteConfigs/*"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a notification config.
  rpc DeleteNotificationConfig(DeleteNotificationConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=organizations/*/locations/*/notificationConfigs/*}"
      additional_bindings {
        delete: "/v2/{name=folders/*/locations/*/notificationConfigs/*}"
      }
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/notificationConfigs/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a ResourceValueConfig.
  rpc DeleteResourceValueConfig(DeleteResourceValueConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=organizations/*/resourceValueConfigs/*}"
      additional_bindings {
        delete: "/v2/{name=organizations/*/locations/*/resourceValueConfigs/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a BigQuery export.
  rpc GetBigQueryExport(GetBigQueryExportRequest) returns (BigQueryExport) {
    option (google.api.http) = {
      get: "/v2/{name=organizations/*/locations/*/bigQueryExports/*}"
      additional_bindings {
        get: "/v2/{name=folders/*/locations/*/bigQueryExports/*}"
      }
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/bigQueryExports/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Get the simulation by name or the latest simulation for the given
  // organization.
  rpc GetSimulation(GetSimulationRequest) returns (Simulation) {
    option (google.api.http) = {
      get: "/v2/{name=organizations/*/simulations/*}"
      additional_bindings {
        get: "/v2/{name=organizations/*/locations/*/simulations/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Get the valued resource by name
  rpc GetValuedResource(GetValuedResourceRequest) returns (ValuedResource) {
    option (google.api.http) = {
      get: "/v2/{name=organizations/*/simulations/*/valuedResources/*}"
      additional_bindings {
        get: "/v2/{name=organizations/*/locations/*/simulations/*/valuedResources/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the access control policy on the specified Source.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v2/{resource=organizations/*/sources/*}:getIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Gets a mute config. If no location is specified, default is
  // global.
  rpc GetMuteConfig(GetMuteConfigRequest) returns (MuteConfig) {
    option (google.api.http) = {
      get: "/v2/{name=organizations/*/muteConfigs/*}"
      additional_bindings {
        get: "/v2/{name=organizations/*/locations/*/muteConfigs/*}"
      }
      additional_bindings { get: "/v2/{name=folders/*/muteConfigs/*}" }
      additional_bindings {
        get: "/v2/{name=folders/*/locations/*/muteConfigs/*}"
      }
      additional_bindings { get: "/v2/{name=projects/*/muteConfigs/*}" }
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/muteConfigs/*}"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "projects/*/locations/{location=*}/muteConfigs/*"
      }
      routing_parameters {
        field: "name"
        path_template: "organizations/*/locations/{location=*}/muteConfigs/*"
      }
      routing_parameters {
        field: "name"
        path_template: "folders/*/locations/{location=*}/muteConfigs/*"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a notification config.
  rpc GetNotificationConfig(GetNotificationConfigRequest)
      returns (NotificationConfig) {
    option (google.api.http) = {
      get: "/v2/{name=organizations/*/locations/*/notificationConfigs/*}"
      additional_bindings {
        get: "/v2/{name=folders/*/locations/*/notificationConfigs/*}"
      }
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/notificationConfigs/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a ResourceValueConfig.
  rpc GetResourceValueConfig(GetResourceValueConfigRequest)
      returns (ResourceValueConfig) {
    option (google.api.http) = {
      get: "/v2/{name=organizations/*/resourceValueConfigs/*}"
      additional_bindings {
        get: "/v2/{name=organizations/*/locations/*/resourceValueConfigs/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a source.
  rpc GetSource(GetSourceRequest) returns (Source) {
    option (google.api.http) = {
      get: "/v2/{name=organizations/*/sources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Filters an organization or source's findings and groups them by their
  // specified properties in a location. If no location is specified, findings
  // are assumed to be in global
  //
  // To group across all sources provide a `-` as the source id.
  // The following list shows some examples:
  //
  // + `/v2/organizations/{organization_id}/sources/-/findings`
  // +
  // `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings`
  // + `/v2/folders/{folder_id}/sources/-/findings`
  // + `/v2/folders/{folder_id}/sources/-/locations/{location_id}/findings`
  // + `/v2/projects/{project_id}/sources/-/findings`
  // + `/v2/projects/{project_id}/sources/-/locations/{location_id}/findings`
  rpc GroupFindings(GroupFindingsRequest) returns (GroupFindingsResponse) {
    option (google.api.http) = {
      post: "/v2/{parent=organizations/*/sources/*}/findings:group"
      body: "*"
      additional_bindings {
        post: "/v2/{parent=organizations/*/sources/*/locations/*}/findings:group"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{parent=folders/*/sources/*}/findings:group"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{parent=folders/*/sources/*/locations/*}/findings:group"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{parent=projects/*/sources/*}/findings:group"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{parent=projects/*/sources/*/locations/*}/findings:group"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,group_by";
  }

  // Lists the attack paths for a set of simulation results or valued resources
  // and filter.
  rpc ListAttackPaths(ListAttackPathsRequest)
      returns (ListAttackPathsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=organizations/*/simulations/*}/attackPaths"
      additional_bindings {
        get: "/v2/{parent=organizations/*/simulations/*/valuedResources/*}/attackPaths"
      }
      additional_bindings {
        get: "/v2/{parent=organizations/*/locations/*/simulations/*/valuedResources/*}/attackPaths"
      }
      additional_bindings {
        get: "/v2/{parent=organizations/*/simulations/*/attackExposureResults/*}/attackPaths"
      }
      additional_bindings {
        get: "/v2/{parent=organizations/*/locations/*/simulations/*/attackExposureResults/*}/attackPaths"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists BigQuery exports. Note that when requesting BigQuery exports at a
  // given level all exports under that level are also returned e.g. if
  // requesting BigQuery exports under a folder, then all BigQuery exports
  // immediately under the folder plus the ones created under the projects
  // within the folder are returned.
  rpc ListBigQueryExports(ListBigQueryExportsRequest)
      returns (ListBigQueryExportsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=organizations/*/locations/*}/bigQueryExports"
      additional_bindings {
        get: "/v2/{parent=folders/*/locations/*}/bigQueryExports"
      }
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*}/bigQueryExports"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists an organization or source's findings.
  //
  // To list across all sources for a given location provide a `-` as the source
  // id. If no location is specified, finding are assumed to be in global.
  // The following list shows some examples:
  //
  // + `/v2/organizations/{organization_id}/sources/-/findings`
  // +
  // `/v2/organizations/{organization_id}/sources/-/locations/{location_id}/findings`
  rpc ListFindings(ListFindingsRequest) returns (ListFindingsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=organizations/*/sources/*}/findings"
      additional_bindings {
        get: "/v2/{parent=organizations/*/sources/*/locations/*}/findings"
      }
      additional_bindings { get: "/v2/{parent=folders/*/sources/*}/findings" }
      additional_bindings {
        get: "/v2/{parent=folders/*/sources/*/locations/*}/findings"
      }
      additional_bindings { get: "/v2/{parent=projects/*/sources/*}/findings" }
      additional_bindings {
        get: "/v2/{parent=projects/*/sources/*/locations/*}/findings"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists mute configs. If no location is specified, default is
  // global.
  rpc ListMuteConfigs(ListMuteConfigsRequest)
      returns (ListMuteConfigsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=organizations/*}/muteConfigs"
      additional_bindings {
        get: "/v2/{parent=organizations/*/locations/*}/muteConfigs"
      }
      additional_bindings { get: "/v2/{parent=folders/*}/muteConfigs" }
      additional_bindings {
        get: "/v2/{parent=folders/*/locations/*}/muteConfigs"
      }
      additional_bindings { get: "/v2/{parent=projects/*}/muteConfigs" }
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*}/muteConfigs"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "projects/*/locations/{location=*}/muteConfigs"
      }
      routing_parameters {
        field: "parent"
        path_template: "organizations/*/locations/{location=*}/muteConfigs"
      }
      routing_parameters {
        field: "parent"
        path_template: "folders/*/locations/{location=*}/muteConfigs"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists notification configs.
  rpc ListNotificationConfigs(ListNotificationConfigsRequest)
      returns (ListNotificationConfigsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=organizations/*/locations/*}/notificationConfigs"
      additional_bindings {
        get: "/v2/{parent=folders/*/locations/*}/notificationConfigs"
      }
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*}/notificationConfigs"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all ResourceValueConfigs.
  rpc ListResourceValueConfigs(ListResourceValueConfigsRequest)
      returns (ListResourceValueConfigsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=organizations/*}/resourceValueConfigs"
      additional_bindings {
        get: "/v2/{parent=organizations/*/locations/*}/resourceValueConfigs"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all sources belonging to an organization.
  rpc ListSources(ListSourcesRequest) returns (ListSourcesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=organizations/*}/sources"
      additional_bindings { get: "/v2/{parent=folders/*}/sources" }
      additional_bindings { get: "/v2/{parent=projects/*}/sources" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists the valued resources for a set of simulation results and filter.
  rpc ListValuedResources(ListValuedResourcesRequest)
      returns (ListValuedResourcesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=organizations/*/simulations/*}/valuedResources"
      additional_bindings {
        get: "/v2/{parent=organizations/*/simulations/*/attackExposureResults/*}/valuedResources"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the state of a finding. If no location is specified, finding is
  // assumed to be in global
  rpc SetFindingState(SetFindingStateRequest) returns (Finding) {
    option (google.api.http) = {
      post: "/v2/{name=organizations/*/sources/*/findings/*}:setState"
      body: "*"
      additional_bindings {
        post: "/v2/{name=organizations/*/sources/*/locations/*/findings/*}:setState"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=folders/*/sources/*/findings/*}:setState"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=folders/*/sources/*/locations/*/findings/*}:setState"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=projects/*/sources/*/findings/*}:setState"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=projects/*/sources/*/locations/*/findings/*}:setState"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,state";
  }

  // Sets the access control policy on the specified Source.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v2/{resource=organizations/*/sources/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Updates the mute state of a finding. If no location is specified, finding
  // is assumed to be in global
  rpc SetMute(SetMuteRequest) returns (Finding) {
    option (google.api.http) = {
      post: "/v2/{name=organizations/*/sources/*/findings/*}:setMute"
      body: "*"
      additional_bindings {
        post: "/v2/{name=organizations/*/sources/*/locations/*/findings/*}:setMute"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=folders/*/sources/*/findings/*}:setMute"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=folders/*/sources/*/locations/*/findings/*}:setMute"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=projects/*/sources/*/findings/*}:setMute"
        body: "*"
      }
      additional_bindings {
        post: "/v2/{name=projects/*/sources/*/locations/*/findings/*}:setMute"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,mute";
  }

  // Returns the permissions that a caller has on the specified source.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v2/{resource=organizations/*/sources/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource,permissions";
  }

  // Updates a BigQuery export.
  rpc UpdateBigQueryExport(UpdateBigQueryExportRequest)
      returns (BigQueryExport) {
    option (google.api.http) = {
      patch: "/v2/{big_query_export.name=organizations/*/locations/*/bigQueryExports/*}"
      body: "big_query_export"
      additional_bindings {
        patch: "/v2/{big_query_export.name=folders/*/locations/*/bigQueryExports/*}"
        body: "big_query_export"
      }
      additional_bindings {
        patch: "/v2/{big_query_export.name=projects/*/locations/*/bigQueryExports/*}"
        body: "big_query_export"
      }
    };
    option (google.api.method_signature) = "big_query_export,update_mask";
  }

  // Updates external system. This is for a given finding. If no location is
  // specified, finding is assumed to be in global
  rpc UpdateExternalSystem(UpdateExternalSystemRequest)
      returns (ExternalSystem) {
    option (google.api.http) = {
      patch: "/v2/{external_system.name=organizations/*/sources/*/findings/*/externalSystems/*}"
      body: "external_system"
      additional_bindings {
        patch: "/v2/{external_system.name=organizations/*/sources/*/locations/*/findings/*/externalSystems/*}"
        body: "external_system"
      }
      additional_bindings {
        patch: "/v2/{external_system.name=folders/*/sources/*/findings/*/externalSystems/*}"
        body: "external_system"
      }
      additional_bindings {
        patch: "/v2/{external_system.name=folders/*/sources/*/locations/*/findings/*/externalSystems/*}"
        body: "external_system"
      }
      additional_bindings {
        patch: "/v2/{external_system.name=projects/*/sources/*/findings/*/externalSystems/*}"
        body: "external_system"
      }
      additional_bindings {
        patch: "/v2/{external_system.name=projects/*/sources/*/locations/*/findings/*/externalSystems/*}"
        body: "external_system"
      }
    };
    option (google.api.method_signature) = "external_system,update_mask";
  }

  // Creates or updates a finding. If no location is specified, finding is
  // assumed to be in global. The corresponding source must exist for a finding
  // creation to succeed.
  rpc UpdateFinding(UpdateFindingRequest) returns (Finding) {
    option (google.api.http) = {
      patch: "/v2/{finding.name=organizations/*/sources/*/findings/*}"
      body: "finding"
      additional_bindings {
        patch: "/v2/{finding.name=organizations/*/sources/*/locations/*/findings/*}"
        body: "finding"
      }
      additional_bindings {
        patch: "/v2/{finding.name=folders/*/sources/*/findings/*}"
        body: "finding"
      }
      additional_bindings {
        patch: "/v2/{finding.name=folders/*/sources/*/locations/*/findings/*}"
        body: "finding"
      }
      additional_bindings {
        patch: "/v2/{finding.name=projects/*/sources/*/findings/*}"
        body: "finding"
      }
      additional_bindings {
        patch: "/v2/{finding.name=projects/*/sources/*/locations/*/findings/*}"
        body: "finding"
      }
    };
    option (google.api.method_signature) = "finding,update_mask";
  }

  // Updates a mute config. If no location is specified, default is
  // global.
  rpc UpdateMuteConfig(UpdateMuteConfigRequest) returns (MuteConfig) {
    option (google.api.http) = {
      patch: "/v2/{mute_config.name=organizations/*/muteConfigs/*}"
      body: "mute_config"
      additional_bindings {
        patch: "/v2/{mute_config.name=organizations/*/locations/*/muteConfigs/*}"
        body: "mute_config"
      }
      additional_bindings {
        patch: "/v2/{mute_config.name=folders/*/muteConfigs/*}"
        body: "mute_config"
      }
      additional_bindings {
        patch: "/v2/{mute_config.name=folders/*/locations/*/muteConfigs/*}"
        body: "mute_config"
      }
      additional_bindings {
        patch: "/v2/{mute_config.name=projects/*/muteConfigs/*}"
        body: "mute_config"
      }
      additional_bindings {
        patch: "/v2/{mute_config.name=projects/*/locations/*/muteConfigs/*}"
        body: "mute_config"
      }
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "mute_config.name"
        path_template: "projects/*/locations/{location=*}/muteConfigs/*"
      }
      routing_parameters {
        field: "mute_config.name"
        path_template: "organizations/*/locations/{location=*}/muteConfigs/*"
      }
      routing_parameters {
        field: "mute_config.name"
        path_template: "folders/*/locations/{location=*}/muteConfigs/*"
      }
    };
    option (google.api.method_signature) = "mute_config,update_mask";
  }

  // Updates a notification config. The following update
  // fields are allowed: description, pubsub_topic, streaming_config.filter
  rpc UpdateNotificationConfig(UpdateNotificationConfigRequest)
      returns (NotificationConfig) {
    option (google.api.http) = {
      patch: "/v2/{notification_config.name=organizations/*/locations/*/notificationConfigs/*}"
      body: "notification_config"
      additional_bindings {
        patch: "/v2/{notification_config.name=folders/*/locations/*/notificationConfigs/*}"
        body: "notification_config"
      }
      additional_bindings {
        patch: "/v2/{notification_config.name=projects/*/locations/*/notificationConfigs/*}"
        body: "notification_config"
      }
    };
    option (google.api.method_signature) = "notification_config,update_mask";
  }

  // Updates an existing ResourceValueConfigs with new rules.
  rpc UpdateResourceValueConfig(UpdateResourceValueConfigRequest)
      returns (ResourceValueConfig) {
    option (google.api.http) = {
      patch: "/v2/{resource_value_config.name=organizations/*/resourceValueConfigs/*}"
      body: "resource_value_config"
      additional_bindings {
        patch: "/v2/{resource_value_config.name=organizations/*/locations/*/resourceValueConfigs/*}"
        body: "resource_value_config"
      }
    };
    option (google.api.method_signature) = "resource_value_config,update_mask";
  }

  // Updates security marks. For Finding Security marks, if no location is
  // specified, finding is assumed to be in global. Assets Security Marks can
  // only be accessed through global endpoint.
  rpc UpdateSecurityMarks(UpdateSecurityMarksRequest) returns (SecurityMarks) {
    option (google.api.http) = {
      patch: "/v2/{security_marks.name=organizations/*/sources/*/findings/*/securityMarks}"
      body: "security_marks"
      additional_bindings {
        patch: "/v2/{security_marks.name=organizations/*/assets/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v2/{security_marks.name=organizations/*/sources/*/locations/*/findings/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v2/{security_marks.name=folders/*/sources/*/findings/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v2/{security_marks.name=folders/*/assets/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v2/{security_marks.name=folders/*/sources/*/locations/*/findings/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v2/{security_marks.name=projects/*/sources/*/findings/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v2/{security_marks.name=projects/*/assets/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v2/{security_marks.name=projects/*/sources/*/locations/*/findings/*/securityMarks}"
        body: "security_marks"
      }
    };
    option (google.api.method_signature) = "security_marks,update_mask";
  }

  // Updates a source.
  rpc UpdateSource(UpdateSourceRequest) returns (Source) {
    option (google.api.http) = {
      patch: "/v2/{source.name=organizations/*/sources/*}"
      body: "source"
    };
    option (google.api.method_signature) = "source,update_mask";
  }
}

// Request message to create multiple resource value configs
message BatchCreateResourceValueConfigsRequest {
  // Required. Resource name of the new ResourceValueConfig's parent.
  // The parent field in the CreateResourceValueConfigRequest
  // messages must either be empty or match this field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/ResourceValueConfig"
    }
  ];

  // Required. The resource value configs to be created.
  repeated CreateResourceValueConfigRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for BatchCreateResourceValueConfigs
message BatchCreateResourceValueConfigsResponse {
  // The resource value configs created
  repeated ResourceValueConfig resource_value_configs = 1;
}

// Request message for bulk findings update.
//
// Note:
// 1. If multiple bulk update requests match the same resource, the order in
// which they get executed is not defined.
// 2. Once a bulk operation is started, there is no way to stop it.
message BulkMuteFindingsRequest {
  // The mute state.
  enum MuteState {
    // Unused.
    MUTE_STATE_UNSPECIFIED = 0;

    // Matching findings will be muted (default).
    MUTED = 1;

    // Matching findings will have their mute state cleared.
    UNDEFINED = 2;
  }

  // Required. The parent, at which bulk action needs to be applied. If no
  // location is specified, findings are updated in global. The following list
  // shows some examples:
  //
  // + `organizations/[organization_id]`
  // + `organizations/[organization_id]/locations/[location_id]`
  // + `folders/[folder_id]`
  // + `folders/[folder_id]/locations/[location_id]`
  // + `projects/[project_id]`
  // + `projects/[project_id]/locations/[location_id]`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "*" }
  ];

  // Expression that identifies findings that should be updated.
  // The expression is a list of zero or more restrictions combined
  // via logical operators `AND` and `OR`. Parentheses are supported, and `OR`
  // has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a
  // `-` character in front of them to indicate negation. The fields map to
  // those defined in the corresponding resource.
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  string filter = 2;

  // Optional. All findings matching the given filter will have their mute state
  // set to this value. The default value is `MUTED`. Setting this to
  // `UNDEFINED` will clear the mute state on all matching findings.
  MuteState mute_state = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response to a BulkMute request. Contains the LRO information.
message BulkMuteFindingsResponse {}

// Request message for creating a BigQuery export.
message CreateBigQueryExportRequest {
  // Required. The name of the parent resource of the new BigQuery export. Its
  // format is `organizations/[organization_id]/locations/[location_id]`,
  // `folders/[folder_id]/locations/[location_id]`, or
  // `projects/[project_id]/locations/[location_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/BigQueryExport"
    }
  ];

  // Required. The BigQuery export being created.
  BigQueryExport big_query_export = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Unique identifier provided by the client within the parent scope.
  // It must consist of only lowercase letters, numbers, and hyphens, must start
  // with a letter, must end with either a letter or a number, and must be 63
  // characters or less.
  string big_query_export_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for creating a finding.
message CreateFindingRequest {
  // Required. Resource name of the new finding's parent. The following list
  // shows some examples of the format:
  // +
  // `organizations/[organization_id]/sources/[source_id]`
  // +
  // `organizations/[organization_id]/sources/[source_id]/locations/[location_id]`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];

  // Required. Unique identifier provided by the client within the parent scope.
  // It must be alphanumeric and less than or equal to 32 characters and
  // greater than 0 characters in length.
  string finding_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Finding being created. The name and security_marks will be
  // ignored as they are both output only fields on this resource.
  Finding finding = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for creating a mute config.
message CreateMuteConfigRequest {
  // Required. Resource name of the new mute configs's parent. Its format is
  // `organizations/[organization_id]/locations/[location_id]`,
  // `folders/[folder_id]/locations/[location_id]`, or
  // `projects/[project_id]/locations/[location_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/MuteConfig"
    }
  ];

  // Required. The mute config being created.
  MuteConfig mute_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Unique identifier provided by the client within the parent scope.
  // It must consist of only lowercase letters, numbers, and hyphens, must start
  // with a letter, must end with either a letter or a number, and must be 63
  // characters or less.
  string mute_config_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for creating a notification config.
message CreateNotificationConfigRequest {
  // Required. Resource name of the new notification config's parent. Its format
  // is `organizations/[organization_id]/locations/[location_id]`,
  // `folders/[folder_id]/locations/[location_id]`, or
  // `projects/[project_id]/locations/[location_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/NotificationConfig"
    }
  ];

  // Required.
  // Unique identifier provided by the client within the parent scope.
  // It must be between 1 and 128 characters and contain alphanumeric
  // characters, underscores, or hyphens only.
  string config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The notification config being created. The name and the service
  // account will be ignored as they are both output only fields on this
  // resource.
  NotificationConfig notification_config = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request message to create single resource value config
message CreateResourceValueConfigRequest {
  // Required. Resource name of the new ResourceValueConfig's parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/ResourceValueConfig"
    }
  ];

  // Required. The resource value config being created.
  ResourceValueConfig resource_value_config = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for creating a source.
message CreateSourceRequest {
  // Required. Resource name of the new source's parent. Its format should be
  // `organizations/[organization_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // Required. The Source being created, only the display_name and description
  // will be used. All other fields will be ignored.
  Source source = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for deleting a BigQuery export.
message DeleteBigQueryExportRequest {
  // Required. The name of the BigQuery export to delete. The following list
  // shows some examples of the format:
  //
  // +
  // `organizations/{organization}/locations/{location}/bigQueryExports/{export_id}`
  // + `folders/{folder}/locations/{location}/bigQueryExports/{export_id}`
  // + `projects/{project}/locations/{location}/bigQueryExports/{export_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/BigQueryExport"
    }
  ];
}

// Request message for deleting a mute config. If no location is specified,
// default is global.
message DeleteMuteConfigRequest {
  // Required. Name of the mute config to delete. The following list shows some
  // examples of the format:
  //
  // + `organizations/{organization}/muteConfigs/{config_id}`
  // +
  // `organizations/{organization}/locations/{location}/muteConfigs/{config_id}`
  // + `folders/{folder}/muteConfigs/{config_id}`
  // + `folders/{folder}/locations/{location}/muteConfigs/{config_id}`
  // + `projects/{project}/muteConfigs/{config_id}`
  // + `projects/{project}/locations/{location}/muteConfigs/{config_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/MuteConfig"
    }
  ];
}

// Request message for deleting a notification config.
message DeleteNotificationConfigRequest {
  // Required. Name of the notification config to delete. The following list
  // shows some examples of the format:
  //
  // +
  // `organizations/[organization_id]/locations/[location_id]/notificationConfigs/[config_id]`
  // +
  // `folders/[folder_id]/locations/[location_id]notificationConfigs/[config_id]`
  // +
  // `projects/[project_id]/locations/[location_id]notificationConfigs/[config_id]`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/NotificationConfig"
    }
  ];
}

// Request message to delete resource value config
message DeleteResourceValueConfigRequest {
  // Required. Name of the ResourceValueConfig to delete
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/ResourceValueConfig"
    }
  ];
}

// Request message for retrieving a BigQuery export.
message GetBigQueryExportRequest {
  // Required. Name of the BigQuery export to retrieve. The following list shows
  // some examples of the format:
  //
  // +
  // `organizations/{organization}/locations/{location}/bigQueryExports/{export_id}`
  // + `folders/{folder}/locations/{location}/bigQueryExports/{export_id}`
  // + `projects/{project}locations/{location}//bigQueryExports/{export_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/BigQueryExport"
    }
  ];
}

// Request message for retrieving a mute config. If no location is specified,
// default is global.
message GetMuteConfigRequest {
  // Required. Name of the mute config to retrieve. The following list shows
  // some examples of the format:
  //
  // + `organizations/{organization}/muteConfigs/{config_id}`
  // +
  // `organizations/{organization}/locations/{location}/muteConfigs/{config_id}`
  // + `folders/{folder}/muteConfigs/{config_id}`
  // + `folders/{folder}/locations/{location}/muteConfigs/{config_id}`
  // + `projects/{project}/muteConfigs/{config_id}`
  // + `projects/{project}/locations/{location}/muteConfigs/{config_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/MuteConfig"
    }
  ];
}

// Request message for getting a notification config.
message GetNotificationConfigRequest {
  // Required. Name of the notification config to get. The following list shows
  // some examples of the format:
  //
  // +
  // `organizations/[organization_id]/locations/[location_id]/notificationConfigs/[config_id]`
  // +
  // `folders/[folder_id]/locations/[location_id]/notificationConfigs/[config_id]`
  // +
  // `projects/[project_id]/locations/[location_id]/notificationConfigs/[config_id]`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/NotificationConfig"
    }
  ];
}

// Request message to get resource value config
message GetResourceValueConfigRequest {
  // Required. Name of the resource value config to retrieve. Its format is
  // organizations/{organization}/resourceValueConfigs/{config_id}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/ResourceValueConfig"
    }
  ];
}

// Request message for getting a source.
message GetSourceRequest {
  // Required. Relative resource name of the source. Its format is
  // `organizations/[organization_id]/source/[source_id]`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];
}

// Request message for grouping by findings.
message GroupFindingsRequest {
  // Required. Name of the source to groupBy. If no location is specified,
  // finding is assumed to be in global.
  //  The following list shows some examples:
  //
  // + `organizations/[organization_id]/sources/[source_id]`
  // +
  // `organizations/[organization_id]/sources/[source_id]/locations/[location_id]`
  // + `folders/[folder_id]/sources/[source_id]`
  // + `folders/[folder_id]/sources/[source_id]/locations/[location_id]`
  // + `projects/[project_id]/sources/[source_id]`
  // + `projects/[project_id]/sources/[source_id]/locations/[location_id]`
  //
  // To groupBy across all sources provide a source_id of `-`. The following
  // list shows some examples:
  //
  // + `organizations/{organization_id}/sources/-`
  // + `organizations/{organization_id}/sources/-/locations/[location_id]`
  // + `folders/{folder_id}/sources/-`
  // + `folders/{folder_id}/sources/-/locations/[location_id]`
  // + `projects/{project_id}/sources/-`
  // + `projects/{project_id}/sources/-/locations/[location_id]`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];

  // Expression that defines the filter to apply across findings.
  // The expression is a list of one or more restrictions combined via logical
  // operators `AND` and `OR`.
  // Parentheses are supported, and `OR` has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a `-`
  // character in front of them to indicate negation. Examples include:
  //
  //  * name
  //  * security_marks.marks.marka
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  //
  // The following field and operator combinations are supported:
  //
  // * name: `=`
  // * parent: `=`, `:`
  // * resource_name: `=`, `:`
  // * state: `=`, `:`
  // * category: `=`, `:`
  // * external_uri: `=`, `:`
  // * event_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `event_time = "2019-06-10T16:07:18-07:00"`
  //     `event_time = 1560208038000`
  //
  // * severity: `=`, `:`
  // * security_marks.marks: `=`, `:`
  // * resource:
  //   * resource.name: `=`, `:`
  //   * resource.parent_name: `=`, `:`
  //   * resource.parent_display_name: `=`, `:`
  //   * resource.project_name: `=`, `:`
  //   * resource.project_display_name: `=`, `:`
  //   * resource.type: `=`, `:`
  string filter = 2;

  // Required. Expression that defines what assets fields to use for grouping.
  // The string value should follow SQL syntax: comma separated list of fields.
  // For example: "parent,resource_name".
  string group_by = 3 [(google.api.field_behavior) = REQUIRED];

  // The value returned by the last `GroupFindingsResponse`; indicates
  // that this is a continuation of a prior `GroupFindings` call, and
  // that the system should return the next page of data.
  string page_token = 7;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 8;
}

// Response message for group by findings.
message GroupFindingsResponse {
  // Group results. There exists an element for each existing unique
  // combination of property/values. The element contains a count for the number
  // of times those specific property/values appear.
  repeated GroupResult group_by_results = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 3;

  // The total number of results matching the query.
  int32 total_size = 4;
}

// Result containing the properties and count of a groupBy request.
message GroupResult {
  // Properties matching the groupBy fields in the request.
  map<string, google.protobuf.Value> properties = 1;

  // Total count of resources for the given properties.
  int64 count = 2;
}

// Request message for listing the attack paths for a given simulation or valued
// resource.
message ListAttackPathsRequest {
  // Required. Name of parent to list attack paths.
  //
  // Valid formats:
  // `organizations/{organization}`,
  // `organizations/{organization}/simulations/{simulation}`
  // `organizations/{organization}/simulations/{simulation}/attackExposureResults/{attack_exposure_result_v2}`
  // `organizations/{organization}/simulations/{simulation}/valuedResources/{valued_resource}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/AttackPath"
    }
  ];

  // The filter expression that filters the attack path in the response.
  // Supported fields:
  //
  //   * `valued_resources` supports =
  string filter = 2;

  // The value returned by the last `ListAttackPathsResponse`; indicates
  // that this is a continuation of a prior `ListAttackPaths` call, and
  // that the system should return the next page of data.
  string page_token = 3;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 4;
}

// Response message for listing the attack paths for a given simulation or
// valued resource.
message ListAttackPathsResponse {
  // The attack paths that the attack path simulation identified.
  repeated AttackPath attack_paths = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 2;
}

// Request message for getting simulation.
// Simulation name can include "latest" to retrieve the latest simulation
// For example, "organizations/123/simulations/latest"
message GetSimulationRequest {
  // Required. The organization name or simulation name of this simulation
  //
  // Valid format:
  // `organizations/{organization}/simulations/latest`
  // `organizations/{organization}/simulations/{simulation}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Simulation"
    }
  ];
}

// Request message for getting a valued resource.
message GetValuedResourceRequest {
  // Required. The name of this valued resource
  //
  // Valid format:
  // `organizations/{organization}/simulations/{simulation}/valuedResources/{valued_resource}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/ValuedResource"
    }
  ];
}

// Request message for listing BigQuery exports at a given scope e.g.
// organization, folder or project.
message ListBigQueryExportsRequest {
  // Required. The parent, which owns the collection of BigQuery exports. Its
  // format is `organizations/[organization_id]/locations/[location_id]`,
  // `folders/[folder_id]/locations/[location_id]`, or
  // `projects/[project_id]/locations/[location_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/BigQueryExport"
    }
  ];

  // The maximum number of configs to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 configs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListBigQueryExports` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListBigQueryExports`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for listing BigQuery exports.
message ListBigQueryExportsResponse {
  // The BigQuery exports from the specified parent.
  repeated BigQueryExport big_query_exports = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for listing findings.
message ListFindingsRequest {
  // Required. Name of the source the findings belong to. If no location is
  // specified, the default is global. The following list shows some examples:
  //
  // + `organizations/[organization_id]/sources/[source_id]`
  // +
  // `organizations/[organization_id]/sources/[source_id]/locations/[location_id]`
  // + `folders/[folder_id]/sources/[source_id]`
  // + `folders/[folder_id]/sources/[source_id]/locations/[location_id]`
  // + `projects/[project_id]/sources/[source_id]`
  // + `projects/[project_id]/sources/[source_id]/locations/[location_id]`
  //
  // To list across all sources provide a source_id of `-`. The following
  // list shows some examples:
  //
  // + `organizations/{organization_id}/sources/-`
  // + `organizations/{organization_id}/sources/-/locations/{location_id}`
  // + `folders/{folder_id}/sources/-`
  // + `folders/{folder_id}/sources/-locations/{location_id}`
  // + `projects/{projects_id}/sources/-`
  // + `projects/{projects_id}/sources/-/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];

  // Expression that defines the filter to apply across findings.
  // The expression is a list of one or more restrictions combined via logical
  // operators `AND` and `OR`.
  // Parentheses are supported, and `OR` has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a `-`
  // character in front of them to indicate negation. Examples include:
  //
  //  * name
  //  * security_marks.marks.marka
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  //
  // The following field and operator combinations are supported:
  //
  // * name: `=`
  // * parent: `=`, `:`
  // * resource_name: `=`, `:`
  // * state: `=`, `:`
  // * category: `=`, `:`
  // * external_uri: `=`, `:`
  // * event_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `event_time = "2019-06-10T16:07:18-07:00"`
  //     `event_time = 1560208038000`
  //
  // * severity: `=`, `:`
  // * security_marks.marks: `=`, `:`
  // * resource:
  //   * resource.name: `=`, `:`
  //   * resource.parent_name: `=`, `:`
  //   * resource.parent_display_name: `=`, `:`
  //   * resource.project_name: `=`, `:`
  //   * resource.project_display_name: `=`, `:`
  //   * resource.type: `=`, `:`
  //   * resource.folders.resource_folder: `=`, `:`
  //   * resource.display_name: `=`, `:`
  string filter = 2;

  // Expression that defines what fields and order to use for sorting. The
  // string value should follow SQL syntax: comma separated list of fields. For
  // example: "name,parent". The default sorting order
  // is ascending. To specify descending order for a field, a suffix " desc"
  // should be appended to the field name. For example: "name
  // desc,parent". Redundant space characters in the
  // syntax are insignificant. "name desc,parent" and "
  // name     desc  ,   parent  " are equivalent.
  //
  // The following fields are supported:
  // name
  // parent
  // state
  // category
  // resource_name
  // event_time
  // security_marks.marks
  string order_by = 3;

  // A field mask to specify the Finding fields to be listed in the response.
  // An empty field mask will list all fields.
  google.protobuf.FieldMask field_mask = 7;

  // The value returned by the last `ListFindingsResponse`; indicates
  // that this is a continuation of a prior `ListFindings` call, and
  // that the system should return the next page of data.
  string page_token = 8;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 9;
}

// Response message for listing findings.
message ListFindingsResponse {
  // Result containing the Finding.
  message ListFindingsResult {
    // Information related to the Google Cloud resource that is
    // associated with this finding.
    message Resource {
      // The full resource name of the resource. See:
      // https://cloud.google.com/apis/design/resource_names#full_resource_name
      string name = 1;

      // The human readable name of the resource.
      string display_name = 2;

      // The full resource type of the resource.
      string type = 3;

      // Indicates which cloud provider the finding is from.
      CloudProvider cloud_provider = 4;

      // The service or resource provider associated with the resource.
      string service = 5;

      // The region or location of the service (if applicable).
      string location = 6;

      oneof cloud_provider_metadata {
        // The GCP metadata associated with the finding.
        GcpMetadata gcp_metadata = 7;

        // The AWS metadata associated with the finding.
        AwsMetadata aws_metadata = 8;

        // The Azure metadata associated with the finding.
        AzureMetadata azure_metadata = 9;
      }

      // Provides the path to the resource within the resource hierarchy.
      ResourcePath resource_path = 10;

      // A string representation of the resource path.
      // For Google Cloud, it has the format of
      // `organizations/{organization_id}/folders/{folder_id}/folders/{folder_id}/projects/{project_id}`
      // where there can be any number of folders.
      // For AWS, it has the format of
      // `org/{organization_id}/ou/{organizational_unit_id}/ou/{organizational_unit_id}/account/{account_id}`
      // where there can be any number of organizational units.
      // For Azure, it has the format of
      // `mg/{management_group_id}/mg/{management_group_id}/subscription/{subscription_id}/rg/{resource_group_name}`
      // where there can be any number of management groups.
      string resource_path_string = 11;
    }

    // Finding matching the search request.
    Finding finding = 1;

    // Output only. Resource that is associated with this finding.
    Resource resource = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Findings matching the list request.
  repeated ListFindingsResult list_findings_results = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 3;

  // The total number of findings matching the query.
  int32 total_size = 4;
}

// Request message for listing  mute configs at a given scope e.g. organization,
// folder or project. If no location is specified, default is
// global.
message ListMuteConfigsRequest {
  // Required. The parent, which owns the collection of mute configs. Its format
  // is `organizations/[organization_id]", "folders/[folder_id]`,
  // `projects/[project_id]`,
  // `organizations/[organization_id]/locations/[location_id]`,
  // `folders/[folder_id]/locations/[location_id]`,
  // `projects/[project_id]/locations/[location_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/MuteConfig"
    }
  ];

  // The maximum number of configs to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 configs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListMuteConfigs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListMuteConfigs` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for listing mute configs.
message ListMuteConfigsResponse {
  // The mute configs from the specified parent.
  repeated MuteConfig mute_configs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for listing notification configs.
message ListNotificationConfigsRequest {
  // Required. The name of the parent in which to list the notification
  // configurations. Its format is
  // "organizations/[organization_id]/locations/[location_id]",
  // "folders/[folder_id]/locations/[location_id]", or
  // "projects/[project_id]/locations/[location_id]".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/NotificationConfig"
    }
  ];

  // The value returned by the last `ListNotificationConfigsResponse`; indicates
  // that this is a continuation of a prior `ListNotificationConfigs` call, and
  // that the system should return the next page of data.
  string page_token = 2;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 3;
}

// Response message for listing notification configs.
message ListNotificationConfigsResponse {
  // Notification configs belonging to the requested parent.
  repeated NotificationConfig notification_configs = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 2;
}

// Request message to list resource value configs of a parent
message ListResourceValueConfigsRequest {
  // Required. The parent, which owns the collection of resource value configs.
  // Its format is
  // `organizations/[organization_id]`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/ResourceValueConfig"
    }
  ];

  // The maximum number of configs to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 configs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListResourceValueConfigs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListResourceValueConfigs` must match the call that provided the
  // page token.
  //
  // page_size can be specified, and the new page_size will be used.
  string page_token = 3;
}

// Response message to list resource value configs
message ListResourceValueConfigsResponse {
  // The resource value configs from the specified parent.
  repeated ResourceValueConfig resource_value_configs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is empty, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for listing sources.
message ListSourcesRequest {
  // Required. Resource name of the parent of sources to list. Its format should
  // be `organizations/[organization_id]`, `folders/[folder_id]`, or
  // `projects/[project_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/Source"
    }
  ];

  // The value returned by the last `ListSourcesResponse`; indicates
  // that this is a continuation of a prior `ListSources` call, and
  // that the system should return the next page of data.
  string page_token = 2;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 7;
}

// Response message for listing sources.
message ListSourcesResponse {
  // Sources belonging to the requested parent.
  repeated Source sources = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 2;
}

// Request message for listing the valued resources for a given simulation.
message ListValuedResourcesRequest {
  // Required. Name of parent to list exposed resources.
  //
  // Valid formats:
  // `organizations/{organization}`,
  // `organizations/{organization}/simulations/{simulation}`
  // `organizations/{organization}/simulations/{simulation}/attackExposureResults/{attack_exposure_result_v2}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/ValuedResource"
    }
  ];

  // The filter expression that filters the valued resources in the response.
  // Supported fields:
  //
  //   * `resource_value` supports =
  //   * `resource_type` supports =
  string filter = 2;

  // The value returned by the last `ListValuedResourcesResponse`; indicates
  // that this is a continuation of a prior `ListValuedResources` call, and
  // that the system should return the next page of data.
  string page_token = 3;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 4;

  // Optional. The fields by which to order the valued resources response.
  //
  // Supported fields:
  //
  //   * `exposed_score`
  //
  //   * `resource_value`
  //
  //   * `resource_type`
  //
  // Values should be a comma separated list of fields. For example:
  // `exposed_score,resource_value`.
  //
  // The default sorting order is descending. To specify ascending or descending
  // order for a field, append a " ASC" or a " DESC" suffix, respectively; for
  // example: `exposed_score DESC`.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for listing the valued resources for a given simulation.
message ListValuedResourcesResponse {
  // The valued resources that the attack path simulation identified.
  repeated ValuedResource valued_resources = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 2;

  // The estimated total number of results matching the query.
  int32 total_size = 3;
}

// Request message for updating a finding's state.
message SetFindingStateRequest {
  // Required. The [relative resource
  // name](https://cloud.google.com/apis/design/resource_names#relative_resource_name)
  // of the finding. If no location is specified, finding is assumed to be in
  // global. The following list shows some examples:
  //
  // +
  // `organizations/{organization_id}/sources/{source_id}/findings/{finding_id}`
  // +
  // `organizations/{organization_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}`
  // + `folders/{folder_id}/sources/{source_id}/findings/{finding_id}`
  // +
  // `folders/{folder_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}`
  // + `projects/{project_id}/sources/{source_id}/findings/{finding_id}`
  // +
  // `projects/{project_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Finding"
    }
  ];

  // Required. The desired State of the finding.
  Finding.State state = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for updating a finding's mute status.
message SetMuteRequest {
  // Required. The [relative resource
  // name](https://cloud.google.com/apis/design/resource_names#relative_resource_name)
  // of the finding. If no location is specified, finding is assumed to be in
  // global. The following list shows some examples:
  //
  // +
  // `organizations/{organization_id}/sources/{source_id}/findings/{finding_id}`
  // +
  // `organizations/{organization_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}`
  // + `folders/{folder_id}/sources/{source_id}/findings/{finding_id}`
  // +
  // `folders/{folder_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}`
  // + `projects/{project_id}/sources/{source_id}/findings/{finding_id}`
  // +
  // `projects/{project_id}/sources/{source_id}/locations/{location_id}/findings/{finding_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Finding"
    }
  ];

  // Required. The desired state of the Mute.
  Finding.Mute mute = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for updating a BigQuery export.
message UpdateBigQueryExportRequest {
  // Required. The BigQuery export being updated.
  BigQueryExport big_query_export = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a ExternalSystem resource.
message UpdateExternalSystemRequest {
  // Required. The external system resource to update.
  ExternalSystem external_system = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the external system resource.
  //
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating or creating a finding.
message UpdateFindingRequest {
  // Required. The finding resource to update or create if it does not already
  // exist. parent, security_marks, and update_time will be ignored.
  //
  // In the case of creation, the finding id portion of the name must be
  // alphanumeric and less than or equal to 32 characters and greater than 0
  // characters in length.
  Finding finding = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the finding resource. This field should
  // not be specified when creating a finding.
  //
  // When updating a finding, an empty mask is treated as updating all mutable
  // fields and replacing source_properties.  Individual source_properties can
  // be added/updated by using "source_properties.<property key>" in the field
  // mask.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a mute config.
message UpdateMuteConfigRequest {
  // Required. The mute config being updated.
  MuteConfig mute_config = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a notification config.
message UpdateNotificationConfigRequest {
  // Required. The notification config to update.
  NotificationConfig notification_config = 1
      [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the notification config.
  //
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message to update resource value config
message UpdateResourceValueConfigRequest {
  // Required. The resource value config being updated.
  ResourceValueConfig resource_value_config = 1
      [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  // If empty all mutable fields will be updated.
  //
  // To update nested fields, include the top level field in the mask
  // For example, to update gcp_metadata.resource_type, include the
  // "gcp_metadata" field mask
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a SecurityMarks resource.
message UpdateSecurityMarksRequest {
  // Required. The security marks resource to update.
  SecurityMarks security_marks = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the security marks resource.
  //
  // The field mask must not contain duplicate fields.
  // If empty or set to "marks", all marks will be replaced.  Individual
  // marks can be updated using "marks.<mark_key>".
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a source.
message UpdateSourceRequest {
  // Required. The source resource to update.
  Source source = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the source resource.
  //
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}
