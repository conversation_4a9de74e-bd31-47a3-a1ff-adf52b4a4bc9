// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

import "google/cloud/securitycenter/v2/label.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "ContainerProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// Container associated with the finding.
message Container {
  // Name of the container.
  string name = 1;

  // Container image URI provided when configuring a pod or container. This
  // string can identify a container image version using mutable tags.
  string uri = 2;

  // Optional container image ID, if provided by the container runtime. Uniquely
  // identifies the container image launched using a container image digest.
  string image_id = 3;

  // Container labels, as provided by the container runtime.
  repeated Label labels = 4;

  // The time that the container was created.
  google.protobuf.Timestamp create_time = 5;
}
