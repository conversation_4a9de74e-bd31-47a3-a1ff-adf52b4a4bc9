// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

import "google/cloud/securitycenter/v2/container.proto";
import "google/cloud/securitycenter/v2/label.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "KubernetesProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// Kubernetes-related attributes.
message Kubernetes {
  // A Kubernetes Pod.
  message Pod {
    // Kubernetes Pod namespace.
    string ns = 1;

    // Kubernetes Pod name.
    string name = 2;

    // Pod labels.  For Kubernetes containers, these are applied to the
    // container.
    repeated Label labels = 3;

    // Pod containers associated with this finding, if any.
    repeated Container containers = 4;
  }

  // Kubernetes nodes associated with the finding.
  message Node {
    // [Full resource name](https://google.aip.dev/122#full-resource-names) of
    // the Compute Engine VM running the cluster node.
    string name = 1;
  }

  // Provides GKE node pool information.
  message NodePool {
    // Kubernetes node pool name.
    string name = 1;

    // Nodes associated with the finding.
    repeated Node nodes = 2;
  }

  // Kubernetes Role or ClusterRole.
  message Role {
    // Types of Kubernetes roles.
    enum Kind {
      // Role type is not specified.
      KIND_UNSPECIFIED = 0;

      // Kubernetes Role.
      ROLE = 1;

      // Kubernetes ClusterRole.
      CLUSTER_ROLE = 2;
    }

    // Role type.
    Kind kind = 1;

    // Role namespace.
    string ns = 2;

    // Role name.
    string name = 3;
  }

  // Represents a Kubernetes RoleBinding or ClusterRoleBinding.
  message Binding {
    // Namespace for the binding.
    string ns = 1;

    // Name for the binding.
    string name = 2;

    // The Role or ClusterRole referenced by the binding.
    Role role = 3;

    // Represents one or more subjects that are bound to the role. Not always
    // available for PATCH requests.
    repeated Subject subjects = 4;
  }

  // Represents a Kubernetes subject.
  message Subject {
    // Auth types that can be used for the subject's kind field.
    enum AuthType {
      // Authentication is not specified.
      AUTH_TYPE_UNSPECIFIED = 0;

      // User with valid certificate.
      USER = 1;

      // Users managed by Kubernetes API with credentials stored as secrets.
      SERVICEACCOUNT = 2;

      // Collection of users.
      GROUP = 3;
    }

    // Authentication type for the subject.
    AuthType kind = 1;

    // Namespace for the subject.
    string ns = 2;

    // Name for the subject.
    string name = 3;
  }

  // Conveys information about a Kubernetes access review (such as one returned
  // by a [`kubectl auth
  // can-i`](https://kubernetes.io/docs/reference/access-authn-authz/authorization/#checking-api-access)
  // command) that was involved in a finding.
  message AccessReview {
    // The API group of the resource. "*" means all.
    string group = 1;

    // Namespace of the action being requested. Currently, there is no
    // distinction between no namespace and all namespaces.  Both
    // are represented by "" (empty).
    string ns = 2;

    // The name of the resource being requested. Empty means all.
    string name = 3;

    // The optional resource type requested. "*" means all.
    string resource = 4;

    // The optional subresource type.
    string subresource = 5;

    // A Kubernetes resource API verb, like get, list, watch, create, update,
    // delete, proxy. "*" means all.
    string verb = 6;

    // The API version of the resource. "*" means all.
    string version = 7;
  }

  // Kubernetes object related to the finding, uniquely identified by GKNN.
  // Used if the object Kind is not one of Pod, Node, NodePool, Binding, or
  // AccessReview.
  message Object {
    // Kubernetes object group, such as "policy.k8s.io/v1".
    string group = 1;

    // Kubernetes object kind, such as "Namespace".
    string kind = 2;

    // Kubernetes object namespace. Must be a valid DNS label. Named
    // "ns" to avoid collision with C++ namespace keyword. For details see
    // https://kubernetes.io/docs/tasks/administer-cluster/namespaces/.
    string ns = 3;

    // Kubernetes object name. For details see
    // https://kubernetes.io/docs/concepts/overview/working-with-objects/names/.
    string name = 4;

    // Pod containers associated with this finding, if any.
    repeated Container containers = 5;
  }

  // Kubernetes
  // [Pods](https://cloud.google.com/kubernetes-engine/docs/concepts/pod)
  // associated with the finding. This field contains Pod records for each
  // container that is owned by a Pod.
  repeated Pod pods = 1;

  // Provides Kubernetes
  // [node](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-architecture#nodes)
  // information.
  repeated Node nodes = 2;

  // GKE [node
  // pools](https://cloud.google.com/kubernetes-engine/docs/concepts/node-pools)
  // associated with the finding. This field contains node pool information for
  // each node, when it is available.
  repeated NodePool node_pools = 3;

  // Provides Kubernetes role information for findings that involve [Roles or
  // ClusterRoles](https://cloud.google.com/kubernetes-engine/docs/how-to/role-based-access-control).
  repeated Role roles = 4;

  // Provides Kubernetes role binding information for findings that involve
  // [RoleBindings or
  // ClusterRoleBindings](https://cloud.google.com/kubernetes-engine/docs/how-to/role-based-access-control).
  repeated Binding bindings = 5;

  // Provides information on any Kubernetes access reviews (privilege checks)
  // relevant to the finding.
  repeated AccessReview access_reviews = 6;

  // Kubernetes objects related to the finding.
  repeated Object objects = 7;
}
