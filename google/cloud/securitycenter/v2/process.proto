// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

import "google/cloud/securitycenter/v2/file.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "ProcessProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// Represents an operating system process.
message Process {
  // The process name, as displayed in utilities like `top` and `ps`. This name
  // can be accessed through `/proc/[pid]/comm` and changed with
  // `prctl(PR_SET_NAME)`.
  string name = 1;

  // File information for the process executable.
  File binary = 2;

  // File information for libraries loaded by the process.
  repeated File libraries = 3;

  // When the process represents the invocation of a script, `binary` provides
  // information about the interpreter, while `script` provides information
  // about the script file provided to the interpreter.
  File script = 4;

  // Process arguments as JSON encoded strings.
  repeated string args = 5;

  // True if `args` is incomplete.
  bool arguments_truncated = 6;

  // Process environment variables.
  repeated EnvironmentVariable env_variables = 7;

  // True if `env_variables` is incomplete.
  bool env_variables_truncated = 8;

  // The process ID.
  int64 pid = 9;

  // The parent process ID.
  int64 parent_pid = 10;
}

// A name-value pair representing an environment variable used in an operating
// system process.
message EnvironmentVariable {
  // Environment variable name as a JSON encoded string.
  string name = 1;

  // Environment variable value as a JSON encoded string.
  string val = 2;
}
