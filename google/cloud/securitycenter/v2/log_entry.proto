// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "LogEntryProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// An individual entry in a log.
message LogEntry {
  oneof log_entry {
    // An individual entry in a log stored in Cloud Logging.
    CloudLoggingEntry cloud_logging_entry = 1;
  }
}

// Metadata taken from a [Cloud Logging
// LogEntry](https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry)
message CloudLoggingEntry {
  // A unique identifier for the log entry.
  string insert_id = 1;

  // The type of the log (part of `log_name`. `log_name` is the resource name of
  // the log to which this log entry belongs). For example:
  // `cloudresourcemanager.googleapis.com/activity` Note that this field is not
  // URL-encoded, unlike in `LogEntry`.
  string log_id = 2;

  // The organization, folder, or project of the monitored resource that
  // produced this log entry.
  string resource_container = 3;

  // The time the event described by the log entry occurred.
  google.protobuf.Timestamp timestamp = 4;
}
