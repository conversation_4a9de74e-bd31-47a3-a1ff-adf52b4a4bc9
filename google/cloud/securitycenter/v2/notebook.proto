// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "NotebookProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// Represents a Jupyter notebook IPYNB file, such as a [Colab Enterprise
// notebook](https://cloud.google.com/colab/docs/introduction) file, that is
// associated with a finding.
message Notebook {
  // The name of the notebook.
  string name = 1;

  // The source notebook service, for example, "Colab Enterprise".
  string service = 2;

  // The user ID of the latest author to modify the notebook.
  string last_author = 3;

  // The most recent time the notebook was updated.
  google.protobuf.Timestamp notebook_update_time = 4;
}
