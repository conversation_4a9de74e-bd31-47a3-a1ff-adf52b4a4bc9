// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "ToxicCombinationProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// Contains details about a group of security issues that, when the issues
// occur together, represent a greater risk than when the issues occur
// independently. A group of such issues is referred to as a toxic
// combination.
message ToxicCombination {
  // The
  // [Attack exposure
  // score](https://cloud.google.com/security-command-center/docs/attack-exposure-learn#attack_exposure_scores)
  // of this toxic combination. The score is a measure of how much this toxic
  // combination exposes one or more high-value resources to potential attack.
  double attack_exposure_score = 1;

  // List of resource names of findings associated with this toxic combination.
  // For example, `organizations/123/sources/456/findings/789`.
  repeated string related_findings = 2;
}
