type: google.api.Service
config_version: 3
name: securitycenter.googleapis.com
title: Security Command Center API

apis:
- name: google.cloud.securitycenter.v2.SecurityCenter
- name: google.longrunning.Operations

types:
- name: google.cloud.securitycenter.v2.BigQueryExport
- name: google.cloud.securitycenter.v2.BulkMuteFindingsResponse
- name: google.cloud.securitycenter.v2.ExternalSystem
- name: google.cloud.securitycenter.v2.MuteConfig
- name: google.cloud.securitycenter.v2.NotificationMessage
- name: google.cloud.securitycenter.v2.Resource
- name: google.cloud.securitycenter.v2.ResourceValueConfig

documentation:
  summary: |-
    Security Command Center API provides access to temporal views of assets and
    findings within an organization.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v2/{name=organizations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v2/{name=organizations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2/{name=organizations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2/{name=organizations/*/operations}'

authentication:
  rules:
  - selector: 'google.cloud.securitycenter.v2.SecurityCenter.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=709980&template=1322867
  documentation_uri: https://cloud.google.com/security-command-center/docs/reference/rest
  api_short_name: securitycenter
  github_label: 'api: securitycenter'
  doc_tag_prefix: securitycenter
  organization: CLOUD
  library_settings:
  - version: google.cloud.securitycenter.v2
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/security-command-center/docs/reference/rest
