// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "ExfiltrationProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// Exfiltration represents a data exfiltration attempt from one or more sources
// to one or more targets. The `sources` attribute lists the sources of the
// exfiltrated data. The `targets` attribute lists the destinations the data was
// copied to.
message Exfiltration {
  // If there are multiple sources, then the data is considered "joined" between
  // them. For instance, BigQuery can join multiple tables, and each
  // table would be considered a source.
  repeated ExfilResource sources = 1;

  // If there are multiple targets, each target would get a complete copy of the
  // "joined" source data.
  repeated ExfilResource targets = 2;

  // Total exfiltrated bytes processed for the entire job.
  int64 total_exfiltrated_bytes = 3;
}

// Resource where data was exfiltrated from or exfiltrated to.
message ExfilResource {
  // The resource's [full resource
  // name](https://cloud.google.com/apis/design/resource_names#full_resource_name).
  string name = 1;

  // Subcomponents of the asset that was exfiltrated, like URIs used during
  // exfiltration, table names, databases, and filenames. For example, multiple
  // tables might have been exfiltrated from the same Cloud SQL instance, or
  // multiple files might have been exfiltrated from the same Cloud Storage
  // bucket.
  repeated string components = 2;
}
