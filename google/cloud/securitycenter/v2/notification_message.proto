// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

import "google/cloud/securitycenter/v2/finding.proto";
import "google/cloud/securitycenter/v2/resource.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "NotificationMessageProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// Cloud SCC's Notification
message NotificationMessage {
  // Name of the notification config that generated current notification.
  string notification_config_name = 1;

  // Notification Event.
  oneof event {
    // If it's a Finding based notification config, this field will be
    // populated.
    Finding finding = 2;
  }

  // The Cloud resource tied to this notification's Finding.
  Resource resource = 3;
}
