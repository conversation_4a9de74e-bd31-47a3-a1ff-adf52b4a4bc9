// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "DatabaseProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// Represents database access information, such as queries. A database may be a
// sub-resource of an instance (as in the case of Cloud SQL instances or Cloud
// Spanner instances), or the database instance itself. Some database resources
// might not have the [full resource
// name](https://google.aip.dev/122#full-resource-names) populated because these
// resource types, such as Cloud SQL databases, are not yet supported by Cloud
// Asset Inventory. In these cases only the display name is provided.
message Database {
  // Some database resources may not have the [full resource
  // name](https://google.aip.dev/122#full-resource-names) populated because
  // these resource types are not yet supported by Cloud Asset Inventory (e.g.
  // Cloud SQL databases). In these cases only the display name will be
  // provided.
  // The [full resource name](https://google.aip.dev/122#full-resource-names) of
  // the database that the user connected to, if it is supported by Cloud Asset
  // Inventory.
  string name = 1;

  // The human-readable name of the database that the user connected to.
  string display_name = 2;

  // The username used to connect to the database. The username might not be an
  // IAM principal and does not have a set format.
  string user_name = 3;

  // The SQL statement that is associated with the database access.
  string query = 4;

  // The target usernames, roles, or groups of an SQL privilege grant, which is
  // not an IAM policy change.
  repeated string grantees = 5;

  // The version of the database, for example, POSTGRES_14.
  // See [the complete
  // list](https://cloud.google.com/sql/docs/mysql/admin-api/rest/v1/SqlDatabaseVersion).
  string version = 6;
}
