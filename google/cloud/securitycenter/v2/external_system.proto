// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v2;

import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V2";
option go_package = "cloud.google.com/go/securitycenter/apiv2/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "ExternalSystemProto";
option java_package = "com.google.cloud.securitycenter.v2";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V2";
option ruby_package = "Google::Cloud::SecurityCenter::V2";

// Representation of third party SIEM/SOAR fields within SCC.
message ExternalSystem {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/ExternalSystem"
    pattern: "organizations/{organization}/sources/{source}/findings/{finding}/externalSystems/{externalsystem}"
    pattern: "organizations/{organization}/sources/{source}/locations/{location}/findings/{finding}/externalSystems/{externalsystem}"
    pattern: "folders/{folder}/sources/{source}/findings/{finding}/externalSystems/{externalsystem}"
    pattern: "folders/{folder}/sources/{source}/locations/{location}/findings/{finding}/externalSystems/{externalsystem}"
    pattern: "projects/{project}/sources/{source}/findings/{finding}/externalSystems/{externalsystem}"
    pattern: "projects/{project}/sources/{source}/locations/{location}/findings/{finding}/externalSystems/{externalsystem}"
  };

  // Information about the ticket, if any, that is being used to track the
  // resolution of the issue that is identified by this finding.
  message TicketInfo {
    // The identifier of the ticket in the ticket system.
    string id = 1;

    // The assignee of the ticket in the ticket system.
    string assignee = 2;

    // The description of the ticket in the ticket system.
    string description = 3;

    // The link to the ticket in the ticket system.
    string uri = 4;

    // The latest status of the ticket, as reported by the ticket system.
    string status = 5;

    // The time when the ticket was last updated, as reported by the ticket
    // system.
    google.protobuf.Timestamp update_time = 6;
  }

  // Full resource name of the external system. The following list
  // shows some examples:
  //
  // + `organizations/1234/sources/5678/findings/123456/externalSystems/jira`
  // +
  // `organizations/1234/sources/5678/locations/us/findings/123456/externalSystems/jira`
  // + `folders/1234/sources/5678/findings/123456/externalSystems/jira`
  // +
  // `folders/1234/sources/5678/locations/us/findings/123456/externalSystems/jira`
  // + `projects/1234/sources/5678/findings/123456/externalSystems/jira`
  // +
  // `projects/1234/sources/5678/locations/us/findings/123456/externalSystems/jira`
  string name = 1;

  // References primary/secondary etc assignees in the external system.
  repeated string assignees = 2;

  // The identifier that's used to track the finding's corresponding case in the
  // external system.
  string external_uid = 3;

  // The most recent status of the finding's corresponding case, as reported by
  // the external system.
  string status = 4;

  // The time when the case was last updated, as reported by the external
  // system.
  google.protobuf.Timestamp external_system_update_time = 5;

  // The link to the finding's corresponding case in the external system.
  string case_uri = 6;

  // The priority of the finding's corresponding case in the external system.
  string case_priority = 7;

  // The SLA of the finding's corresponding case in the external system.
  google.protobuf.Timestamp case_sla = 9;

  // The time when the case was created, as reported by the external system.
  google.protobuf.Timestamp case_create_time = 10;

  // The time when the case was closed, as reported by the external system.
  google.protobuf.Timestamp case_close_time = 11;

  // Information about the ticket, if any, that is being used to track the
  // resolution of the issue that is identified by this finding.
  TicketInfo ticket_info = 8;
}
