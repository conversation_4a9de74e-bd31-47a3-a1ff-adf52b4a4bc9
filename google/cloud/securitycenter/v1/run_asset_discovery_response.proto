// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Response of asset discovery run
message RunAssetDiscoveryResponse {
  // The state of an asset discovery run.
  enum State {
    // Asset discovery run state was unspecified.
    STATE_UNSPECIFIED = 0;

    // Asset discovery run completed successfully.
    COMPLETED = 1;

    // Asset discovery run was cancelled with tasks still pending, as another
    // run for the same organization was started with a higher priority.
    SUPERSEDED = 2;

    // Asset discovery run was killed and terminated.
    TERMINATED = 3;
  }

  // The state of an asset discovery run.
  State state = 1;

  // The duration between asset discovery run start and end
  google.protobuf.Duration duration = 2;
}
