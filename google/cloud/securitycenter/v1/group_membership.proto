// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "GroupMembershipProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Contains details about groups of which this finding is a member. A group is a
// collection of findings that are related in some way.
message GroupMembership {
  // Possible types of groups.
  enum GroupType {
    // Default value.
    GROUP_TYPE_UNSPECIFIED = 0;

    // Group represents a toxic combination.
    GROUP_TYPE_TOXIC_COMBINATION = 1;
  }

  // Type of group.
  GroupType group_type = 1;

  // ID of the group.
  string group_id = 2;
}
