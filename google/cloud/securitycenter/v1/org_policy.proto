// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "OrgPolicyProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Contains information about the org policies associated with the finding.
message OrgPolicy {
  option (google.api.resource) = {
    type: "orgpolicy.googleapis.com/Policy"
    pattern: "organizations/{organization}/policies/{constraint_name}"
    pattern: "folders/{folder}/policies/{constraint_name}"
    pattern: "projects/{project}/policies/{constraint_name}"
  };

  // The resource name of the org policy.
  // Example:
  // "organizations/{organization_id}/policies/{constraint_name}"
  string name = 1;
}
