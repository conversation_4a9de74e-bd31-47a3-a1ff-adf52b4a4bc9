// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "AccessProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Represents an access event.
message Access {
  // Associated email, such as "<EMAIL>".
  //
  // The email address of the authenticated user or a service account acting on
  // behalf of a third party principal making the request. For third party
  // identity callers, the `principal_subject` field is populated instead of
  // this field. For privacy reasons, the principal email address is sometimes
  // redacted. For more information, see [Caller identities in audit
  // logs](https://cloud.google.com/logging/docs/audit#user-id).
  string principal_email = 1;

  // Caller's IP address, such as "*******".
  string caller_ip = 2;

  // The caller IP's geolocation, which identifies where the call came from.
  Geolocation caller_ip_geo = 3;

  // Type of user agent associated with the finding. For example, an operating
  // system shell or an embedded or standalone application.
  string user_agent_family = 4;

  // The caller's user agent string associated with the finding.
  string user_agent = 12;

  // This is the API service that the service account made a call to, e.g.
  // "iam.googleapis.com"
  string service_name = 5;

  // The method that the service account called, e.g. "SetIamPolicy".
  string method_name = 6;

  // A string that represents the principal_subject that is associated with the
  // identity. Unlike `principal_email`, `principal_subject` supports principals
  // that aren't associated with email addresses, such as third party
  // principals. For most identities, the format is
  // `principal://iam.googleapis.com/{identity pool name}/subject/{subject}`.
  // Some GKE identities, such as GKE_WORKLOAD, FREEFORM, and GKE_HUB_WORKLOAD,
  // still use the legacy format `serviceAccount:{identity pool
  // name}[{subject}]`.
  string principal_subject = 7;

  // The name of the service account key that was used to create or exchange
  // credentials when authenticating the service account that made the request.
  // This is a scheme-less URI full resource name. For example:
  //
  // "//iam.googleapis.com/projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}".
  //
  string service_account_key_name = 8;

  // The identity delegation history of an authenticated service account that
  // made the request. The `serviceAccountDelegationInfo[]` object contains
  // information about the real authorities that try to access Google Cloud
  // resources by delegating on a service account. When multiple authorities are
  // present, they are guaranteed to be sorted based on the original ordering of
  // the identity delegation events.
  repeated ServiceAccountDelegationInfo service_account_delegation_info = 9;

  // A string that represents a username. The username provided depends on the
  // type of the finding and is likely not an IAM principal. For example, this
  // can be a system username if the finding is related to a virtual machine, or
  // it can be an application login username.
  string user_name = 11;
}

// Identity delegation history of an authenticated service account.
message ServiceAccountDelegationInfo {
  // The email address of a Google account.
  string principal_email = 1;

  // A string representing the principal_subject associated with the identity.
  // As compared to `principal_email`, supports principals that aren't
  // associated with email addresses, such as third party principals. For most
  // identities, the format will be `principal://iam.googleapis.com/{identity
  // pool name}/subjects/{subject}` except for some GKE identities
  // (GKE_WORKLOAD, FREEFORM, GKE_HUB_WORKLOAD) that are still in the legacy
  // format `serviceAccount:{identity pool name}[{subject}]`
  string principal_subject = 2;
}

// Represents a geographical location for a given access.
message Geolocation {
  // A CLDR.
  string region_code = 1;
}
