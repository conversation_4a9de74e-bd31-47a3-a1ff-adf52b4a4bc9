// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "IndicatorProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Represents what's commonly known as an _indicator of compromise_ (IoC) in
// computer forensics. This is an artifact observed on a network or in an
// operating system that, with high confidence, indicates a computer intrusion.
// For more information, see [Indicator of
// compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).
message Indicator {
  // Indicates what signature matched this process.
  message ProcessSignature {
    // A signature corresponding to memory page hashes.
    message MemoryHashSignature {
      // Memory hash detection contributing to the binary family match.
      message Detection {
        // The name of the binary associated with the memory hash
        // signature detection.
        string binary = 2;

        // The percentage of memory page hashes in the signature
        // that were matched.
        double percent_pages_matched = 3;
      }

      // The binary family.
      string binary_family = 1;

      // The list of memory hash detections contributing to the binary family
      // match.
      repeated Detection detections = 4;
    }

    // A signature corresponding to a YARA rule.
    message YaraRuleSignature {
      // The name of the YARA rule.
      string yara_rule = 5;
    }

    // Possible resource types to be associated with a signature.
    enum SignatureType {
      // The default signature type.
      SIGNATURE_TYPE_UNSPECIFIED = 0;

      // Used for signatures concerning processes.
      SIGNATURE_TYPE_PROCESS = 1;

      // Used for signatures concerning disks.
      SIGNATURE_TYPE_FILE = 2;
    }

    oneof signature {
      // Signature indicating that a binary family was matched.
      MemoryHashSignature memory_hash_signature = 6;

      // Signature indicating that a YARA rule was matched.
      YaraRuleSignature yara_rule_signature = 7;
    }

    // Describes the type of resource associated with the signature.
    SignatureType signature_type = 8;
  }

  // The list of IP addresses that are associated with the finding.
  repeated string ip_addresses = 1;

  // List of domains associated to the Finding.
  repeated string domains = 2;

  // The list of matched signatures indicating that the given
  // process is present in the environment.
  repeated ProcessSignature signatures = 3;

  // The list of URIs associated to the Findings.
  repeated string uris = 4;
}
