// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securitycenter/v1/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "ResourceValueConfigProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// A resource value configuration (RVC) is a mapping configuration of user's
// resources to resource values. Used in Attack path simulations.
message ResourceValueConfig {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/ResourceValueConfig"
    pattern: "organizations/{organization}/resourceValueConfigs/{resource_value_config}"
    plural: "resourceValueConfigs"
    singular: "resourceValueConfig"
  };

  // Resource value mapping for Sensitive Data Protection findings.
  // If any of these mappings have a resource value that is not unspecified,
  // the resource_value field will be ignored when reading this configuration.
  message SensitiveDataProtectionMapping {
    // Resource value mapping for high-sensitivity Sensitive Data Protection
    // findings
    ResourceValue high_sensitivity_mapping = 1;

    // Resource value mapping for medium-sensitivity Sensitive Data Protection
    // findings
    ResourceValue medium_sensitivity_mapping = 2;
  }

  // Name for the resource value configuration
  string name = 1;

  // Required. Resource value level this expression represents
  ResourceValue resource_value = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Tag values combined with `AND` to check against.
  // Values in the form "tagValues/123"
  // Example: `[ "tagValues/123", "tagValues/456", "tagValues/789" ]`
  // https://cloud.google.com/resource-manager/docs/tags/tags-creating-and-managing
  repeated string tag_values = 3 [(google.api.field_behavior) = REQUIRED];

  // Apply resource_value only to resources that match resource_type.
  // resource_type will be checked with `AND` of other resources.
  // For example, "storage.googleapis.com/Bucket" with resource_value "HIGH"
  // will apply "HIGH" value only to "storage.googleapis.com/Bucket" resources.
  string resource_type = 4;

  // Project or folder to scope this configuration to.
  // For example, "project/456" would apply this configuration only to resources
  // in "project/456" scope will be checked with `AND` of other
  // resources.
  string scope = 5;

  // List of resource labels to search for, evaluated with `AND`.
  // For example, `"resource_labels_selector": {"key": "value", "env": "prod"}`
  // will match resources with labels "key": "value" `AND` "env":
  // "prod"
  // https://cloud.google.com/resource-manager/docs/creating-managing-labels
  map<string, string> resource_labels_selector = 6;

  // Description of the resource value configuration.
  string description = 7;

  // Output only. Timestamp this resource value configuration was created.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp this resource value configuration was last updated.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Cloud provider this configuration applies to
  CloudProvider cloud_provider = 10;

  // A mapping of the sensitivity on Sensitive Data Protection finding to
  // resource values. This mapping can only be used in combination with a
  // resource_type that is related to BigQuery, e.g.
  // "bigquery.googleapis.com/Dataset".
  SensitiveDataProtectionMapping sensitive_data_protection_mapping = 11;
}

// Value enum to map to a resource
enum ResourceValue {
  // Unspecific value
  RESOURCE_VALUE_UNSPECIFIED = 0;

  // High resource value
  HIGH = 1;

  // Medium resource value
  MEDIUM = 2;

  // Low resource value
  LOW = 3;

  // No resource value, e.g. ignore these resources
  NONE = 4;
}
