// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/field_behavior.proto";
import "google/cloud/securitycenter/v1/folder.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "ResourceProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Information related to the Google Cloud resource.
message Resource {
  // The full resource name of the resource. See:
  // https://cloud.google.com/apis/design/resource_names#full_resource_name
  string name = 1;

  // The human readable name of the resource.
  string display_name = 8;

  // The full resource type of the resource.
  string type = 6;

  // The full resource name of project that the resource belongs to.
  string project = 2;

  // The project ID that the resource belongs to.
  string project_display_name = 3;

  // The full resource name of resource's parent.
  string parent = 4;

  // The human readable name of resource's parent.
  string parent_display_name = 5;

  // Output only. Contains a Folder message for each folder in the assets
  // ancestry. The first folder is the deepest nested folder, and the last
  // folder is the folder directly under the Organization.
  repeated Folder folders = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Indicates which cloud provider the resource resides in.
  CloudProvider cloud_provider = 9;

  // Indicates which organization or tenant in the cloud provider the finding
  // applies to.
  string organization = 10;

  // The parent service or product from which the resource is provided, for
  // example, GKE or SNS.
  string service = 11;

  // The region or location of the service (if applicable).
  string location = 12;

  oneof cloud_provider_metadata {
    // The AWS metadata associated with the finding.
    AwsMetadata aws_metadata = 16;

    // The Azure metadata associated with the finding.
    AzureMetadata azure_metadata = 17;
  }

  // Provides the path to the resource within the resource hierarchy.
  ResourcePath resource_path = 18;

  // A string representation of the resource path.
  // For Google Cloud, it has the format of
  // `organizations/{organization_id}/folders/{folder_id}/folders/{folder_id}/projects/{project_id}`
  // where there can be any number of folders.
  // For AWS, it has the format of
  // `org/{organization_id}/ou/{organizational_unit_id}/ou/{organizational_unit_id}/account/{account_id}`
  // where there can be any number of organizational units.
  // For Azure, it has the format of
  // `mg/{management_group_id}/mg/{management_group_id}/subscription/{subscription_id}/rg/{resource_group_name}`
  // where there can be any number of management groups.
  string resource_path_string = 19;
}

// Enumeration representing the various cloud providers a finding's resource
// could reside in.
enum CloudProvider {
  // The cloud provider is unspecified.
  CLOUD_PROVIDER_UNSPECIFIED = 0;

  // The cloud provider is Google Cloud Platform.
  GOOGLE_CLOUD_PLATFORM = 1;

  // The cloud provider is Amazon Web Services.
  AMAZON_WEB_SERVICES = 2;

  // The cloud provider is Microsoft Azure.
  MICROSOFT_AZURE = 3;
}

// AWS metadata associated with the resource, only applicable if the finding's
// cloud provider is Amazon Web Services.
message AwsMetadata {
  // An organization is a collection of accounts that are centrally managed
  // together using consolidated billing, organized hierarchically with
  // organizational units (OUs), and controlled with policies.
  message AwsOrganization {
    // The unique identifier (ID) for the organization. The regex pattern for an
    // organization ID string requires "o-" followed by from 10 to 32 lowercase
    // letters or digits.
    string id = 1;
  }

  // An Organizational Unit (OU) is a container of AWS accounts within a root of
  // an organization. Policies that are attached to an OU apply to all accounts
  // contained in that OU and in any child OUs.
  message AwsOrganizationalUnit {
    // The unique identifier (ID) associated with this OU. The regex pattern for
    // an organizational unit ID string requires "ou-" followed by from 4 to 32
    // lowercase letters or digits (the ID of the root that contains the OU).
    // This string is followed by a second "-" dash and from 8 to 32 additional
    // lowercase letters or digits. For example, "ou-ab12-cd34ef56".
    string id = 1;

    // The friendly name of the OU.
    string name = 2;
  }

  // An AWS account that is a member of an organization.
  message AwsAccount {
    // The unique identifier (ID) of the account, containing exactly 12 digits.
    string id = 1;

    // The friendly name of this account.
    string name = 2;
  }

  // The AWS organization associated with the resource.
  AwsOrganization organization = 2;

  // A list of AWS organizational units associated with the resource, ordered
  // from lowest level (closest to the account) to highest level.
  repeated AwsOrganizationalUnit organizational_units = 3;

  // The AWS account associated with the resource.
  AwsAccount account = 4;
}

// Azure metadata associated with the resource, only applicable if the finding's
// cloud provider is Microsoft Azure.
message AzureMetadata {
  // Represents an Azure management group.
  message AzureManagementGroup {
    // The UUID of the Azure management group, for example,
    // `*************-0000-0000-************`.
    string id = 1;

    // The display name of the Azure management group.
    string display_name = 2;
  }

  // Represents an Azure subscription.
  message AzureSubscription {
    // The UUID of the Azure subscription, for example,
    // `291bba3f-e0a5-47bc-a099-3bdcb2a50a05`.
    string id = 1;

    // The display name of the Azure subscription.
    string display_name = 2;
  }

  // Represents an Azure resource group.
  message AzureResourceGroup {
    // The name of the Azure resource group. This is not a UUID.
    string name = 1;
  }

  // A list of Azure management groups associated with the resource, ordered
  // from lowest level (closest to the subscription) to highest level.
  repeated AzureManagementGroup management_groups = 4;

  // The Azure subscription associated with the resource.
  AzureSubscription subscription = 5;

  // The Azure resource group associated with the resource.
  AzureResourceGroup resource_group = 6;
}

// Represents the path of resources leading up to the resource this finding is
// about.
message ResourcePath {
  // The type of resource the node represents.
  enum ResourcePathNodeType {
    // Node type is unspecified.
    RESOURCE_PATH_NODE_TYPE_UNSPECIFIED = 0;

    // The node represents a Google Cloud organization.
    GCP_ORGANIZATION = 1;

    // The node represents a Google Cloud folder.
    GCP_FOLDER = 2;

    // The node represents a Google Cloud project.
    GCP_PROJECT = 3;

    // The node represents an AWS organization.
    AWS_ORGANIZATION = 4;

    // The node represents an AWS organizational unit.
    AWS_ORGANIZATIONAL_UNIT = 5;

    // The node represents an AWS account.
    AWS_ACCOUNT = 6;

    // The node represents an Azure management group.
    AZURE_MANAGEMENT_GROUP = 7;

    // The node represents an Azure subscription.
    AZURE_SUBSCRIPTION = 8;

    // The node represents an Azure resource group.
    AZURE_RESOURCE_GROUP = 9;
  }

  // A node within the resource path. Each node represents a resource within the
  // resource hierarchy.
  message ResourcePathNode {
    // The type of resource this node represents.
    ResourcePathNodeType node_type = 1;

    // The ID of the resource this node represents.
    string id = 2;

    // The display name of the resource this node represents.
    string display_name = 3;
  }

  // The list of nodes that make the up resource path, ordered from lowest
  // level to highest level.
  repeated ResourcePathNode nodes = 1;
}
