// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securitycenter/v1/security_health_analytics_custom_config.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "EffectiveSecurityHealthAnalyticsCustomModuleProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// An EffectiveSecurityHealthAnalyticsCustomModule is the representation of
// a Security Health Analytics custom module at a specified level of the
// resource hierarchy: organization, folder, or project. If a custom module is
// inherited from a parent organization or folder, the value of the
// `enablementState` property in EffectiveSecurityHealthAnalyticsCustomModule is
// set to the value that is effective in the parent, instead of  `INHERITED`.
// For example, if the module is enabled in a parent organization or folder, the
// effective enablement_state for the module in all child folders or projects is
// also `enabled`. EffectiveSecurityHealthAnalyticsCustomModule is read-only.
message EffectiveSecurityHealthAnalyticsCustomModule {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/EffectiveSecurityHealthAnalyticsCustomModule"
    pattern: "organizations/{organization}/securityHealthAnalyticsSettings/effectiveCustomModules/{effective_custom_module}"
    pattern: "folders/{folder}/securityHealthAnalyticsSettings/effectiveCustomModules/{effective_custom_module}"
    pattern: "projects/{project}/securityHealthAnalyticsSettings/effectiveCustomModules/{effective_custom_module}"
  };

  // The enablement state of the module.
  enum EnablementState {
    // Unspecified enablement state.
    ENABLEMENT_STATE_UNSPECIFIED = 0;

    // The module is enabled at the given level.
    ENABLED = 1;

    // The module is disabled at the given level.
    DISABLED = 2;
  }

  // Output only. The resource name of the custom module.
  // Its format is
  // "organizations/{organization}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}",
  // or
  // "folders/{folder}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}",
  // or
  // "projects/{project}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user-specified configuration for the module.
  CustomConfig custom_config = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The effective state of enablement for the module at the given
  // level of the hierarchy.
  EnablementState enablement_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The display name for the custom module. The name must be
  // between 1 and 128 characters, start with a lowercase letter, and contain
  // alphanumeric characters or underscores only.
  string display_name = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}
