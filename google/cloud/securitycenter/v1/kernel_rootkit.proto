// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "KernelRootkitProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Kernel mode rootkit signatures.
message KernelRootkit {
  // Rootkit name, when available.
  string name = 1;

  // True if unexpected modifications of kernel code memory are present.
  bool unexpected_code_modification = 2;

  // True if unexpected modifications of kernel read-only data memory are
  // present.
  bool unexpected_read_only_data_modification = 3;

  // True if `ftrace` points are present with callbacks pointing to regions
  // that are not in the expected kernel or module code range.
  bool unexpected_ftrace_handler = 4;

  // True if `kprobe` points are present with callbacks pointing to regions
  // that are not in the expected kernel or module code range.
  bool unexpected_kprobe_handler = 5;

  // True if kernel code pages that are not in the expected kernel or module
  // code regions are present.
  bool unexpected_kernel_code_pages = 6;

  // True if system call handlers that are are not in the expected kernel or
  // module code regions are present.
  bool unexpected_system_call_handler = 7;

  // True if interrupt handlers that are are not in the expected kernel or
  // module code regions are present.
  bool unexpected_interrupt_handler = 8;

  // True if unexpected processes in the scheduler run queue are present. Such
  // processes are in the run queue, but not in the process task list.
  bool unexpected_processes_in_runqueue = 9;
}
