// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securitycenter/v1/security_health_analytics_custom_config.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "SecurityHealthAnalyticsCustomModuleProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";
option (google.api.resource_definition) = {
  type: "securitycenter.googleapis.com/SecurityHealthAnalyticsSettings"
  pattern: "organizations/{organization}/securityHealthAnalyticsSettings"
  pattern: "folders/{folder}/securityHealthAnalyticsSettings"
  pattern: "projects/{project}/securityHealthAnalyticsSettings"
};

// Represents an instance of a Security Health Analytics custom module,
// including its full module name, display name, enablement state, and last
// updated time. You can create a custom module at the organization, folder, or
// project level. Custom modules that you create at the organization or folder
// level are inherited by the child folders and projects.
message SecurityHealthAnalyticsCustomModule {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/SecurityHealthAnalyticsCustomModule"
    pattern: "organizations/{organization}/securityHealthAnalyticsSettings/customModules/{custom_module}"
    pattern: "folders/{folder}/securityHealthAnalyticsSettings/customModules/{custom_module}"
    pattern: "projects/{project}/securityHealthAnalyticsSettings/customModules/{custom_module}"
  };

  // Possible enablement states of a custom module.
  enum EnablementState {
    // Unspecified enablement state.
    ENABLEMENT_STATE_UNSPECIFIED = 0;

    // The module is enabled at the given CRM resource.
    ENABLED = 1;

    // The module is disabled at the given CRM resource.
    DISABLED = 2;

    // State is inherited from an ancestor module. The module will either
    // be effectively ENABLED or DISABLED based on its closest non-inherited
    // ancestor module in the CRM hierarchy.
    INHERITED = 3;
  }

  // Immutable. The resource name of the custom module.
  // Its format is
  // "organizations/{organization}/securityHealthAnalyticsSettings/customModules/{customModule}",
  // or
  // "folders/{folder}/securityHealthAnalyticsSettings/customModules/{customModule}",
  // or
  // "projects/{project}/securityHealthAnalyticsSettings/customModules/{customModule}"
  //
  // The id {customModule} is server-generated and is not user settable.
  // It will be a numeric id containing 1-20 digits.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // The display name of the Security Health Analytics custom module. This
  // display name becomes the finding category for all findings that are
  // returned by this custom module. The display name must be between 1 and
  // 128 characters, start with a lowercase letter, and contain alphanumeric
  // characters or underscores only.
  string display_name = 2;

  // The enablement state of the custom module.
  EnablementState enablement_state = 4;

  // Output only. The time at which the custom module was last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The editor that last updated the custom module.
  string last_editor = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If empty, indicates that the custom module was created in the
  // organization, folder, or project in which you are viewing the custom
  // module. Otherwise, `ancestor_module` specifies the organization or folder
  // from which the custom module is inherited.
  string ancestor_module = 7 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];

  // The user specified custom configuration for the module.
  CustomConfig custom_config = 8;
}
