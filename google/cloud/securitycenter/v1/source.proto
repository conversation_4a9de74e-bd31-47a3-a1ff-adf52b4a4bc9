// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Security Command Center finding source. A finding source
// is an entity or a mechanism that can produce a finding. A source is like a
// container of findings that come from the same scanner, logger, monitor, and
// other tools.
message Source {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/Source"
    pattern: "organizations/{organization}/sources/{source}"
    pattern: "folders/{folder}/sources/{source}"
    pattern: "projects/{project}/sources/{source}"
  };

  // The relative resource name of this source. See:
  // https://cloud.google.com/apis/design/resource_names#relative_resource_name
  // Example:
  // "organizations/{organization_id}/sources/{source_id}"
  string name = 1;

  // The source's display name.
  // A source's display name must be unique amongst its siblings, for example,
  // two sources with the same parent can't share the same display name.
  // The display name must have a length between 1 and 64 characters
  // (inclusive).
  string display_name = 2;

  // The description of the source (max of 1024 characters).
  // Example:
  // "Web Security Scanner is a web security scanner for common
  // vulnerabilities in App Engine applications. It can automatically
  // scan and detect four common vulnerabilities, including cross-site-scripting
  // (XSS), Flash injection, mixed content (HTTP in HTTPS), and
  // outdated or insecure libraries."
  string description = 3;

  // The canonical name of the finding source. It's either
  // "organizations/{organization_id}/sources/{source_id}",
  // "folders/{folder_id}/sources/{source_id}", or
  // "projects/{project_number}/sources/{source_id}",
  // depending on the closest CRM ancestor of the resource associated with the
  // finding.
  string canonical_name = 14;
}
