// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "BackupDisasterRecoveryProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Information related to Google Cloud Backup and DR Service findings.
message BackupDisasterRecovery {
  // The name of a Backup and DR template which comprises one or more backup
  // policies. See the [Backup and DR
  // documentation](https://cloud.google.com/backup-disaster-recovery/docs/concepts/backup-plan#temp)
  // for more information. For example, `snap-ov`.
  string backup_template = 1;

  // The names of Backup and DR policies that are associated with a template
  // and that define when to run a backup, how frequently to run a backup, and
  // how long to retain the backup image. For example, `onvaults`.
  repeated string policies = 2;

  // The name of a Backup and DR host, which is managed by the backup and
  // recovery appliance and known to the management console. The host can be of
  // type Generic (for example, Compute Engine, SQL Server, Oracle DB, SMB file
  // system, etc.), vCenter, or an ESX server. See the [Backup and DR
  // documentation on
  // hosts](https://cloud.google.com/backup-disaster-recovery/docs/configuration/manage-hosts-and-their-applications)
  // for more information. For example, `centos7-01`.
  string host = 3;

  // The names of Backup and DR applications. An application is a VM, database,
  // or file system on a managed host monitored by a backup and recovery
  // appliance. For example, `centos7-01-vol00`, `centos7-01-vol01`,
  // `centos7-01-vol02`.
  repeated string applications = 4;

  // The name of the Backup and DR storage pool that the backup and recovery
  // appliance is storing data in. The storage pool could be of type Cloud,
  // Primary, Snapshot, or OnVault. See the [Backup and DR documentation on
  // storage
  // pools](https://cloud.google.com/backup-disaster-recovery/docs/concepts/storage-pools).
  // For example, `DiskPoolOne`.
  string storage_pool = 5;

  // The names of Backup and DR advanced policy options of a policy applying to
  // an application. See the [Backup and DR documentation on policy
  // options](https://cloud.google.com/backup-disaster-recovery/docs/create-plan/policy-settings).
  // For example, `skipofflineappsincongrp, nounmap`.
  repeated string policy_options = 6;

  // The name of the Backup and DR resource profile that specifies the storage
  // media for backups of application and VM data. See the [Backup and DR
  // documentation on
  // profiles](https://cloud.google.com/backup-disaster-recovery/docs/concepts/backup-plan#profile).
  // For example, `GCP`.
  string profile = 7;

  // The name of the Backup and DR appliance that captures, moves, and manages
  // the lifecycle of backup data. For example, `backup-server-57137`.
  string appliance = 8;

  // The backup type of the Backup and DR image.
  // For example, `Snapshot`, `Remote Snapshot`, `OnVault`.
  string backup_type = 9;

  // The timestamp at which the Backup and DR backup was created.
  google.protobuf.Timestamp backup_create_time = 10;
}
