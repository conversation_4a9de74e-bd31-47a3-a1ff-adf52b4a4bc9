// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "EventThreatDetectionCustomModuleValidationErrorsProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// A list of zero or more errors encountered while validating the uploaded
// configuration of an Event Threat Detection Custom Module.
message CustomModuleValidationErrors {
  repeated CustomModuleValidationError errors = 1;
}

// An error encountered while validating the uploaded configuration of an
// Event Threat Detection Custom Module.
message CustomModuleValidationError {
  // A description of the error, suitable for human consumption. Required.
  string description = 1;

  // The path, in RFC 8901 JSON Pointer format, to the field that failed
  // validation. This may be left empty if no specific field is affected.
  string field_path = 2;

  // The initial position of the error in the uploaded text version of the
  // module. This field may be omitted if no specific position applies, or if
  // one could not be computed.
  optional Position start = 3;

  // The end position of the error in the uploaded text version of the
  // module. This field may be omitted if no specific position applies, or if
  // one could not be computed..
  optional Position end = 4;
}

// A position in the uploaded text version of a module.
message Position {
  int32 line_number = 1;

  int32 column_number = 2;
}
