// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securitycenter/v1/resource.proto";
import "google/cloud/securitycenter/v1/valued_resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "SimulationProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Attack path simulation
message Simulation {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/Simulation"
    pattern: "organizations/{organization}/simulations/{simulation}"
    plural: "simulations"
    singular: "simulation"
  };

  // Full resource name of the Simulation:
  // `organizations/123/simulations/456`
  string name = 1;

  // Output only. Time simulation was created
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource value configurations' metadata used in this simulation. Maximum of
  // 100.
  repeated ResourceValueConfigMetadata resource_value_configs_metadata = 3;

  // Indicates which cloud provider was used in this simulation.
  CloudProvider cloud_provider = 4;
}
