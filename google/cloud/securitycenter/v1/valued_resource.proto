// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "ValuedResourceProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// A resource that is determined to have value to a user's system
message ValuedResource {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/ValuedResource"
    pattern: "organizations/{organization}/simulations/{simulation}/valuedResources/{valued_resource}"
    plural: "valuedResources"
    singular: "valuedResource"
  };

  // How valuable the resource is.
  enum ResourceValue {
    // The resource value isn't specified.
    RESOURCE_VALUE_UNSPECIFIED = 0;

    // This is a low-value resource.
    RESOURCE_VALUE_LOW = 1;

    // This is a medium-value resource.
    RESOURCE_VALUE_MEDIUM = 2;

    // This is a high-value resource.
    RESOURCE_VALUE_HIGH = 3;
  }

  // Valued resource name, for example,
  //  e.g.:
  //  `organizations/123/simulations/456/valuedResources/789`
  string name = 1;

  // The
  // [full resource
  // name](https://cloud.google.com/apis/design/resource_names#full_resource_name)
  // of the valued resource.
  string resource = 2;

  // The [resource
  // type](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
  // of the valued resource.
  string resource_type = 3;

  // Human-readable name of the valued resource.
  string display_name = 4;

  // How valuable this resource is.
  ResourceValue resource_value = 5;

  // Exposed score for this valued resource. A value of 0 means no exposure was
  // detected exposure.
  double exposed_score = 6;

  // List of resource value configurations' metadata used to determine the value
  // of this resource. Maximum of 100.
  repeated ResourceValueConfigMetadata resource_value_configs_used = 7;
}

// Metadata about a ResourceValueConfig. For example, id and name.
message ResourceValueConfigMetadata {
  // Resource value config name
  string name = 1;
}
