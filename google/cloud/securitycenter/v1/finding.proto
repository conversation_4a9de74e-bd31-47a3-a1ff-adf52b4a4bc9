// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securitycenter/v1/access.proto";
import "google/cloud/securitycenter/v1/application.proto";
import "google/cloud/securitycenter/v1/attack_exposure.proto";
import "google/cloud/securitycenter/v1/backup_disaster_recovery.proto";
import "google/cloud/securitycenter/v1/cloud_armor.proto";
import "google/cloud/securitycenter/v1/cloud_dlp_data_profile.proto";
import "google/cloud/securitycenter/v1/cloud_dlp_inspection.proto";
import "google/cloud/securitycenter/v1/compliance.proto";
import "google/cloud/securitycenter/v1/connection.proto";
import "google/cloud/securitycenter/v1/contact_details.proto";
import "google/cloud/securitycenter/v1/container.proto";
import "google/cloud/securitycenter/v1/database.proto";
import "google/cloud/securitycenter/v1/exfiltration.proto";
import "google/cloud/securitycenter/v1/external_system.proto";
import "google/cloud/securitycenter/v1/file.proto";
import "google/cloud/securitycenter/v1/group_membership.proto";
import "google/cloud/securitycenter/v1/iam_binding.proto";
import "google/cloud/securitycenter/v1/indicator.proto";
import "google/cloud/securitycenter/v1/kernel_rootkit.proto";
import "google/cloud/securitycenter/v1/kubernetes.proto";
import "google/cloud/securitycenter/v1/load_balancer.proto";
import "google/cloud/securitycenter/v1/log_entry.proto";
import "google/cloud/securitycenter/v1/mitre_attack.proto";
import "google/cloud/securitycenter/v1/notebook.proto";
import "google/cloud/securitycenter/v1/org_policy.proto";
import "google/cloud/securitycenter/v1/process.proto";
import "google/cloud/securitycenter/v1/security_marks.proto";
import "google/cloud/securitycenter/v1/security_posture.proto";
import "google/cloud/securitycenter/v1/toxic_combination.proto";
import "google/cloud/securitycenter/v1/vulnerability.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Security Command Center finding.
//
// A finding is a record of assessment data like security, risk, health, or
// privacy, that is ingested into Security Command Center for presentation,
// notification, analysis, policy testing, and enforcement. For example, a
// cross-site scripting (XSS) vulnerability in an App Engine application is a
// finding.
message Finding {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/Finding"
    pattern: "organizations/{organization}/sources/{source}/findings/{finding}"
    pattern: "folders/{folder}/sources/{source}/findings/{finding}"
    pattern: "projects/{project}/sources/{source}/findings/{finding}"
    plural: "findings"
    singular: "finding"
  };

  // The state of the finding.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The finding requires attention and has not been addressed yet.
    ACTIVE = 1;

    // The finding has been fixed, triaged as a non-issue or otherwise addressed
    // and is no longer active.
    INACTIVE = 2;
  }

  // The severity of the finding.
  enum Severity {
    // This value is used for findings when a source doesn't write a severity
    // value.
    SEVERITY_UNSPECIFIED = 0;

    // Vulnerability:
    // A critical vulnerability is easily discoverable by an external actor,
    // exploitable, and results in the direct ability to execute arbitrary code,
    // exfiltrate data, and otherwise gain additional access and privileges to
    // cloud resources and workloads. Examples include publicly accessible
    // unprotected user data and public SSH access with weak or no
    // passwords.
    //
    // Threat:
    // Indicates a threat that is able to access, modify, or delete data or
    // execute unauthorized code within existing resources.
    CRITICAL = 1;

    // Vulnerability:
    // A high risk vulnerability can be easily discovered and exploited in
    // combination with other vulnerabilities in order to gain direct access and
    // the ability to execute arbitrary code, exfiltrate data, and otherwise
    // gain additional access and privileges to cloud resources and workloads.
    // An example is a database with weak or no passwords that is only
    // accessible internally. This database could easily be compromised by an
    // actor that had access to the internal network.
    //
    // Threat:
    // Indicates a threat that is able to create new computational resources in
    // an environment but not able to access data or execute code in existing
    // resources.
    HIGH = 2;

    // Vulnerability:
    // A medium risk vulnerability could be used by an actor to gain access to
    // resources or privileges that enable them to eventually (through multiple
    // steps or a complex exploit) gain access and the ability to execute
    // arbitrary code or exfiltrate data. An example is a service account with
    // access to more projects than it should have. If an actor gains access to
    // the service account, they could potentially use that access to manipulate
    // a project the service account was not intended to.
    //
    // Threat:
    // Indicates a threat that is able to cause operational impact but may not
    // access data or execute unauthorized code.
    MEDIUM = 3;

    // Vulnerability:
    // A low risk vulnerability hampers a security organization's ability to
    // detect vulnerabilities or active threats in their deployment, or prevents
    // the root cause investigation of security issues. An example is monitoring
    // and logs being disabled for resource configurations and access.
    //
    // Threat:
    // Indicates a threat that has obtained minimal access to an environment but
    // is not able to access data, execute code, or create resources.
    LOW = 4;
  }

  // Mute state a finding can be in.
  enum Mute {
    // Unspecified.
    MUTE_UNSPECIFIED = 0;

    // Finding has been muted.
    MUTED = 1;

    // Finding has been unmuted.
    UNMUTED = 2;

    // Finding has never been muted/unmuted.
    UNDEFINED = 4;
  }

  // Represents what kind of Finding it is.
  enum FindingClass {
    // Unspecified finding class.
    FINDING_CLASS_UNSPECIFIED = 0;

    // Describes unwanted or malicious activity.
    THREAT = 1;

    // Describes a potential weakness in software that increases risk to
    // Confidentiality & Integrity & Availability.
    VULNERABILITY = 2;

    // Describes a potential weakness in cloud resource/asset configuration that
    // increases risk.
    MISCONFIGURATION = 3;

    // Describes a security observation that is for informational purposes.
    OBSERVATION = 4;

    // Describes an error that prevents some SCC functionality.
    SCC_ERROR = 5;

    // Describes a potential security risk due to a change in the security
    // posture.
    POSTURE_VIOLATION = 6;

    // Describes a group of security issues that, when the issues
    // occur together, represent a greater risk than when the issues occur
    // independently. A group of such issues is referred to as a toxic
    // combination.
    TOXIC_COMBINATION = 7;
  }

  // Mute information about the finding, including whether the finding has a
  // static mute or any matching dynamic mute rules.
  message MuteInfo {
    // Information about the static mute state. A static mute state overrides
    // any dynamic mute rules that apply to this finding. The static mute state
    // can be set by a static mute rule or by muting the finding directly.
    message StaticMute {
      // The static mute state. If the value is `MUTED` or `UNMUTED`, then the
      // finding's overall mute state will have the same value.
      Mute state = 1;

      // When the static mute was applied.
      google.protobuf.Timestamp apply_time = 2;
    }

    // The record of a dynamic mute rule that matches the finding.
    message DynamicMuteRecord {
      // The relative resource name of the mute rule, represented by a mute
      // config, that created this record, for example
      // `organizations/123/muteConfigs/mymuteconfig` or
      // `organizations/123/locations/global/muteConfigs/mymuteconfig`.
      string mute_config = 1;

      // When the dynamic mute rule first matched the finding.
      google.protobuf.Timestamp match_time = 2;
    }

    // If set, the static mute applied to this finding. Static mutes override
    // dynamic mutes. If unset, there is no static mute.
    StaticMute static_mute = 1;

    // The list of dynamic mute rules that currently match the finding.
    repeated DynamicMuteRecord dynamic_mute_records = 2;
  }

  // The [relative resource
  // name](https://cloud.google.com/apis/design/resource_names#relative_resource_name)
  // of the finding. Example:
  // "organizations/{organization_id}/sources/{source_id}/findings/{finding_id}",
  // "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
  // "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
  string name = 1;

  // The relative resource name of the source the finding belongs to. See:
  // https://cloud.google.com/apis/design/resource_names#relative_resource_name
  // This field is immutable after creation time.
  // For example:
  // "organizations/{organization_id}/sources/{source_id}"
  string parent = 2;

  // For findings on Google Cloud resources, the full resource
  // name of the Google Cloud resource this finding is for. See:
  // https://cloud.google.com/apis/design/resource_names#full_resource_name
  // When the finding is for a non-Google Cloud resource, the resourceName can
  // be a customer or partner defined string. This field is immutable after
  // creation time.
  string resource_name = 3;

  // The state of the finding.
  State state = 4;

  // The additional taxonomy group within findings from a given source.
  // This field is immutable after creation time.
  // Example: "XSS_FLASH_INJECTION"
  string category = 5;

  // The URI that, if available, points to a web page outside of Security
  // Command Center where additional information about the finding can be found.
  // This field is guaranteed to be either empty or a well formed URL.
  string external_uri = 6;

  // Source specific properties. These properties are managed by the source
  // that writes the finding. The key names in the source_properties map must be
  // between 1 and 255 characters, and must start with a letter and contain
  // alphanumeric characters or underscores only.
  map<string, google.protobuf.Value> source_properties = 7;

  // Output only. User specified security marks. These marks are entirely
  // managed by the user and come from the SecurityMarks resource that belongs
  // to the finding.
  SecurityMarks security_marks = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The time the finding was first detected. If an existing finding is updated,
  // then this is the time the update occurred.
  // For example, if the finding represents an open firewall, this property
  // captures the time the detector believes the firewall became open. The
  // accuracy is determined by the detector. If the finding is later resolved,
  // then this time reflects when the finding was resolved. This must not
  // be set to a value greater than the current timestamp.
  google.protobuf.Timestamp event_time = 9;

  // The time at which the finding was created in Security Command Center.
  google.protobuf.Timestamp create_time = 10;

  // The severity of the finding. This field is managed by the source that
  // writes the finding.
  Severity severity = 12;

  // The canonical name of the finding. It's either
  // "organizations/{organization_id}/sources/{source_id}/findings/{finding_id}",
  // "folders/{folder_id}/sources/{source_id}/findings/{finding_id}" or
  // "projects/{project_number}/sources/{source_id}/findings/{finding_id}",
  // depending on the closest CRM ancestor of the resource associated with the
  // finding.
  string canonical_name = 14;

  // Indicates the mute state of a finding (either muted, unmuted
  // or undefined). Unlike other attributes of a finding, a finding provider
  // shouldn't set the value of mute.
  Mute mute = 15;

  // The class of the finding.
  FindingClass finding_class = 17;

  // Represents what's commonly known as an *indicator of compromise* (IoC) in
  // computer forensics. This is an artifact observed on a network or in an
  // operating system that, with high confidence, indicates a computer
  // intrusion. For more information, see [Indicator of
  // compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).
  Indicator indicator = 18;

  // Represents vulnerability-specific fields like CVE and CVSS scores.
  // CVE stands for Common Vulnerabilities and Exposures
  // (https://cve.mitre.org/about/)
  Vulnerability vulnerability = 20;

  // Output only. The most recent time this finding was muted or unmuted.
  google.protobuf.Timestamp mute_update_time = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Third party SIEM/SOAR fields within SCC, contains external
  // system information and external system finding fields.
  map<string, ExternalSystem> external_systems = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // MITRE ATT&CK tactics and techniques related to this finding.
  // See: https://attack.mitre.org
  MitreAttack mitre_attack = 25;

  // Access details associated with the finding, such as more information on the
  // caller, which method was accessed, and from where.
  Access access = 26;

  // Contains information about the IP connection associated with the finding.
  repeated Connection connections = 31;

  // Records additional information about the mute operation, for example, the
  // [mute configuration](/security-command-center/docs/how-to-mute-findings)
  // that muted the finding and the user who muted the finding.
  string mute_initiator = 28;

  // Output only. The mute information regarding this finding.
  MuteInfo mute_info = 61 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Represents operating system processes associated with the Finding.
  repeated Process processes = 30;

  // Output only. Map containing the points of contact for the given finding.
  // The key represents the type of contact, while the value contains a list of
  // all the contacts that pertain. Please refer to:
  // https://cloud.google.com/resource-manager/docs/managing-notification-contacts#notification-categories
  //
  //     {
  //       "security": {
  //         "contacts": [
  //           {
  //             "email": "<EMAIL>"
  //           },
  //           {
  //             "email": "<EMAIL>"
  //           }
  //         ]
  //       }
  //     }
  map<string, ContactDetails> contacts = 33
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Contains compliance information for security standards associated to the
  // finding.
  repeated Compliance compliances = 34;

  // Output only. The human readable display name of the finding source such as
  // "Event Threat Detection" or "Security Health Analytics".
  string parent_display_name = 36 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Contains more details about the finding.
  string description = 37;

  // Represents exfiltrations associated with the finding.
  Exfiltration exfiltration = 38;

  // Represents IAM bindings associated with the finding.
  repeated IamBinding iam_bindings = 39;

  // Steps to address the finding.
  string next_steps = 40;

  // Unique identifier of the module which generated the finding.
  // Example:
  // folders/598186756061/securityHealthAnalyticsSettings/customModules/56799441161885
  string module_name = 41;

  // Containers associated with the finding. This field provides information for
  // both Kubernetes and non-Kubernetes containers.
  repeated Container containers = 42;

  // Kubernetes resources associated with the finding.
  Kubernetes kubernetes = 43;

  // Database associated with the finding.
  Database database = 44;

  // The results of an attack path simulation relevant to this finding.
  AttackExposure attack_exposure = 45;

  // File associated with the finding.
  repeated File files = 46;

  // Cloud Data Loss Prevention (Cloud DLP) inspection results that are
  // associated with the finding.
  CloudDlpInspection cloud_dlp_inspection = 48;

  // Cloud DLP data profile that is associated with the finding.
  CloudDlpDataProfile cloud_dlp_data_profile = 49;

  // Signature of the kernel rootkit.
  KernelRootkit kernel_rootkit = 50;

  // Contains information about the org policies associated with the finding.
  repeated OrgPolicy org_policies = 51;

  // Represents an application associated with the finding.
  Application application = 53;

  // Fields related to Backup and DR findings.
  BackupDisasterRecovery backup_disaster_recovery = 55;

  // The security posture associated with the finding.
  SecurityPosture security_posture = 56;

  // Log entries that are relevant to the finding.
  repeated LogEntry log_entries = 57;

  // The load balancers associated with the finding.
  repeated LoadBalancer load_balancers = 58;

  // Fields related to Cloud Armor findings.
  CloudArmor cloud_armor = 59;

  // Notebook associated with the finding.
  Notebook notebook = 63;

  // Contains details about a group of security issues that, when the issues
  // occur together, represent a greater risk than when the issues occur
  // independently. A group of such issues is referred to as a toxic
  // combination.
  // This field cannot be updated. Its value is ignored in all update requests.
  ToxicCombination toxic_combination = 64;

  // Contains details about groups of which this finding is a member. A group is
  // a collection of findings that are related in some way.
  // This field cannot be updated. Its value is ignored in all update requests.
  repeated GroupMembership group_memberships = 65;
}
