// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "BigQueryExportProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Configures how to deliver Findings to BigQuery Instance.
message BigQueryExport {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/BigQueryExport"
    pattern: "organizations/{organization}/bigQueryExports/{export}"
    pattern: "folders/{folder}/bigQueryExports/{export}"
    pattern: "projects/{project}/bigQueryExports/{export}"
    plural: "bigQueryExports"
    singular: "bigQueryExport"
  };

  // The relative resource name of this export. See:
  // https://cloud.google.com/apis/design/resource_names#relative_resource_name.
  // Example format:
  // "organizations/{organization_id}/bigQueryExports/{export_id}" Example
  // format: "folders/{folder_id}/bigQueryExports/{export_id}" Example format:
  // "projects/{project_id}/bigQueryExports/{export_id}"
  // This field is provided in responses, and is ignored when provided in create
  // requests.
  string name = 1;

  // The description of the export (max of 1024 characters).
  string description = 2;

  // Expression that defines the filter to apply across create/update events
  // of findings. The expression is a list of zero or more restrictions combined
  // via logical operators `AND` and `OR`. Parentheses are supported, and `OR`
  // has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a
  // `-` character in front of them to indicate negation. The fields map to
  // those defined in the corresponding resource.
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  string filter = 3;

  // The dataset to write findings' updates to. Its format is
  // "projects/[project_id]/datasets/[bigquery_dataset_id]".
  // BigQuery Dataset unique ID  must contain only letters (a-z, A-Z), numbers
  // (0-9), or underscores (_).
  string dataset = 4;

  // Output only. The time at which the BigQuery export was created.
  // This field is set by the server and will be ignored if provided on export
  // on creation.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The most recent time at which the BigQuery export was updated.
  // This field is set by the server and will be ignored if provided on export
  // creation or update.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the user who last edited the BigQuery export.
  // This field is set by the server and will be ignored if provided on export
  // creation or update.
  string most_recent_editor = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The service account that needs permission to create table and
  // upload data to the BigQuery dataset.
  string principal = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}
