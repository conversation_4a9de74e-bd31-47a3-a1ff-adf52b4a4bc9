// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securitycenter/v1/asset.proto";
import "google/cloud/securitycenter/v1/attack_path.proto";
import "google/cloud/securitycenter/v1/bigquery_export.proto";
import "google/cloud/securitycenter/v1/effective_event_threat_detection_custom_module.proto";
import "google/cloud/securitycenter/v1/effective_security_health_analytics_custom_module.proto";
import "google/cloud/securitycenter/v1/event_threat_detection_custom_module.proto";
import "google/cloud/securitycenter/v1/event_threat_detection_custom_module_validation_errors.proto";
import "google/cloud/securitycenter/v1/external_system.proto";
import "google/cloud/securitycenter/v1/finding.proto";
import "google/cloud/securitycenter/v1/folder.proto";
import "google/cloud/securitycenter/v1/mute_config.proto";
import "google/cloud/securitycenter/v1/notification_config.proto";
import "google/cloud/securitycenter/v1/organization_settings.proto";
import "google/cloud/securitycenter/v1/resource.proto";
import "google/cloud/securitycenter/v1/resource_value_config.proto";
import "google/cloud/securitycenter/v1/run_asset_discovery_response.proto";
import "google/cloud/securitycenter/v1/security_health_analytics_custom_config.proto";
import "google/cloud/securitycenter/v1/security_health_analytics_custom_module.proto";
import "google/cloud/securitycenter/v1/security_marks.proto";
import "google/cloud/securitycenter/v1/simulation.proto";
import "google/cloud/securitycenter/v1/source.proto";
import "google/cloud/securitycenter/v1/valued_resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";
option (google.api.resource_definition) = {
  type: "securitycenter.googleapis.com/OrganizationLocation"
  pattern: "organizations/{organization}/locations/{location}"
};
option (google.api.resource_definition) = {
  type: "securitycenter.googleapis.com/FolderLocation"
  pattern: "folders/{folder}/locations/{location}"
};
option (google.api.resource_definition) = {
  type: "securitycenter.googleapis.com/OrganizationSimulation"
  pattern: "organizations/{organization}/simulations/{simulation}"
};
option (google.api.resource_definition) = {
  type: "securitycenter.googleapis.com/EventThreatDetectionSettings"
  pattern: "organizations/{organization}/eventThreatDetectionSettings"
  pattern: "folders/{folder}/eventThreatDetectionSettings"
  pattern: "projects/{project}/eventThreatDetectionSettings"
};

// V1 APIs for Security Center service.
service SecurityCenter {
  option (google.api.default_host) = "securitycenter.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Kicks off an LRO to bulk mute findings for a parent based on a filter. The
  // parent can be either an organization, folder or project. The findings
  // matched by the filter will be muted after the LRO is done.
  rpc BulkMuteFindings(BulkMuteFindingsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*}/findings:bulkMute"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=folders/*}/findings:bulkMute"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*}/findings:bulkMute"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.securitycenter.v1.BulkMuteFindingsResponse"
      metadata_type: "google.protobuf.Empty"
    };
  }

  // Creates a resident SecurityHealthAnalyticsCustomModule at the scope of the
  // given CRM parent, and also creates inherited
  // SecurityHealthAnalyticsCustomModules for all CRM descendants of the given
  // parent. These modules are enabled by default.
  rpc CreateSecurityHealthAnalyticsCustomModule(
      CreateSecurityHealthAnalyticsCustomModuleRequest)
      returns (SecurityHealthAnalyticsCustomModule) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*/securityHealthAnalyticsSettings}/customModules"
      body: "security_health_analytics_custom_module"
      additional_bindings {
        post: "/v1/{parent=folders/*/securityHealthAnalyticsSettings}/customModules"
        body: "security_health_analytics_custom_module"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*/securityHealthAnalyticsSettings}/customModules"
        body: "security_health_analytics_custom_module"
      }
    };
    option (google.api.method_signature) =
        "parent,security_health_analytics_custom_module";
  }

  // Creates a source.
  rpc CreateSource(CreateSourceRequest) returns (Source) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*}/sources"
      body: "source"
    };
    option (google.api.method_signature) = "parent,source";
  }

  // Creates a finding. The corresponding source must exist for finding creation
  // to succeed.
  rpc CreateFinding(CreateFindingRequest) returns (Finding) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*/sources/*}/findings"
      body: "finding"
    };
    option (google.api.method_signature) = "parent,finding_id,finding";
  }

  // Creates a mute config.
  rpc CreateMuteConfig(CreateMuteConfigRequest) returns (MuteConfig) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*}/muteConfigs"
      body: "mute_config"
      additional_bindings {
        post: "/v1/{parent=organizations/*/locations/*}/muteConfigs"
        body: "mute_config"
      }
      additional_bindings {
        post: "/v1/{parent=folders/*}/muteConfigs"
        body: "mute_config"
      }
      additional_bindings {
        post: "/v1/{parent=folders/*/locations/*}/muteConfigs"
        body: "mute_config"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*}/muteConfigs"
        body: "mute_config"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*}/muteConfigs"
        body: "mute_config"
      }
    };
    option (google.api.method_signature) = "parent,mute_config";
    option (google.api.method_signature) = "parent,mute_config,mute_config_id";
  }

  // Creates a notification config.
  rpc CreateNotificationConfig(CreateNotificationConfigRequest)
      returns (NotificationConfig) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*}/notificationConfigs"
      body: "notification_config"
      additional_bindings {
        post: "/v1/{parent=folders/*}/notificationConfigs"
        body: "notification_config"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*}/notificationConfigs"
        body: "notification_config"
      }
    };
    option (google.api.method_signature) =
        "parent,config_id,notification_config";
    option (google.api.method_signature) = "parent,notification_config";
  }

  // Deletes an existing mute config.
  rpc DeleteMuteConfig(DeleteMuteConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/muteConfigs/*}"
      additional_bindings { delete: "/v1/{name=folders/*/muteConfigs/*}" }
      additional_bindings { delete: "/v1/{name=projects/*/muteConfigs/*}" }
      additional_bindings {
        delete: "/v1/{name=organizations/*/locations/*/muteConfigs/*}"
      }
      additional_bindings {
        delete: "/v1/{name=folders/*/locations/*/muteConfigs/*}"
      }
      additional_bindings {
        delete: "/v1/{name=projects/*/locations/*/muteConfigs/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a notification config.
  rpc DeleteNotificationConfig(DeleteNotificationConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/notificationConfigs/*}"
      additional_bindings {
        delete: "/v1/{name=folders/*/notificationConfigs/*}"
      }
      additional_bindings {
        delete: "/v1/{name=projects/*/notificationConfigs/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes the specified SecurityHealthAnalyticsCustomModule and all of its
  // descendants in the CRM hierarchy. This method is only supported for
  // resident custom modules.
  rpc DeleteSecurityHealthAnalyticsCustomModule(
      DeleteSecurityHealthAnalyticsCustomModuleRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/securityHealthAnalyticsSettings/customModules/*}"
      additional_bindings {
        delete: "/v1/{name=folders/*/securityHealthAnalyticsSettings/customModules/*}"
      }
      additional_bindings {
        delete: "/v1/{name=projects/*/securityHealthAnalyticsSettings/customModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Get the simulation by name or the latest simulation for the given
  // organization.
  rpc GetSimulation(GetSimulationRequest) returns (Simulation) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/simulations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Get the valued resource by name
  rpc GetValuedResource(GetValuedResourceRequest) returns (ValuedResource) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/simulations/*/valuedResources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a BigQuery export.
  rpc GetBigQueryExport(GetBigQueryExportRequest) returns (BigQueryExport) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/bigQueryExports/*}"
      additional_bindings { get: "/v1/{name=folders/*/bigQueryExports/*}" }
      additional_bindings { get: "/v1/{name=projects/*/bigQueryExports/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the access control policy on the specified Source.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=organizations/*/sources/*}:getIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Gets a mute config.
  rpc GetMuteConfig(GetMuteConfigRequest) returns (MuteConfig) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/muteConfigs/*}"
      additional_bindings { get: "/v1/{name=folders/*/muteConfigs/*}" }
      additional_bindings { get: "/v1/{name=projects/*/muteConfigs/*}" }
      additional_bindings {
        get: "/v1/{name=organizations/*/locations/*/muteConfigs/*}"
      }
      additional_bindings {
        get: "/v1/{name=folders/*/locations/*/muteConfigs/*}"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/muteConfigs/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a notification config.
  rpc GetNotificationConfig(GetNotificationConfigRequest)
      returns (NotificationConfig) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/notificationConfigs/*}"
      additional_bindings { get: "/v1/{name=folders/*/notificationConfigs/*}" }
      additional_bindings { get: "/v1/{name=projects/*/notificationConfigs/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the settings for an organization.
  rpc GetOrganizationSettings(GetOrganizationSettingsRequest)
      returns (OrganizationSettings) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/organizationSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves an EffectiveSecurityHealthAnalyticsCustomModule.
  rpc GetEffectiveSecurityHealthAnalyticsCustomModule(
      GetEffectiveSecurityHealthAnalyticsCustomModuleRequest)
      returns (EffectiveSecurityHealthAnalyticsCustomModule) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/securityHealthAnalyticsSettings/effectiveCustomModules/*}"
      additional_bindings {
        get: "/v1/{name=folders/*/securityHealthAnalyticsSettings/effectiveCustomModules/*}"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/securityHealthAnalyticsSettings/effectiveCustomModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves a SecurityHealthAnalyticsCustomModule.
  rpc GetSecurityHealthAnalyticsCustomModule(
      GetSecurityHealthAnalyticsCustomModuleRequest)
      returns (SecurityHealthAnalyticsCustomModule) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/securityHealthAnalyticsSettings/customModules/*}"
      additional_bindings {
        get: "/v1/{name=folders/*/securityHealthAnalyticsSettings/customModules/*}"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/securityHealthAnalyticsSettings/customModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a source.
  rpc GetSource(GetSourceRequest) returns (Source) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/sources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Filters an organization's assets and  groups them by their specified
  // properties.
  rpc GroupAssets(GroupAssetsRequest) returns (GroupAssetsResponse) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*}/assets:group"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=folders/*}/assets:group"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*}/assets:group"
        body: "*"
      }
    };
  }

  // Filters an organization or source's findings and  groups them by their
  // specified properties.
  //
  // To group across all sources provide a `-` as the source id.
  // Example: /v1/organizations/{organization_id}/sources/-/findings,
  // /v1/folders/{folder_id}/sources/-/findings,
  // /v1/projects/{project_id}/sources/-/findings
  rpc GroupFindings(GroupFindingsRequest) returns (GroupFindingsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*/sources/*}/findings:group"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=folders/*/sources/*}/findings:group"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*/sources/*}/findings:group"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,group_by";
  }

  // Lists an organization's assets.
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*}/assets"
      additional_bindings { get: "/v1/{parent=folders/*}/assets" }
      additional_bindings { get: "/v1/{parent=projects/*}/assets" }
    };
  }

  // Returns a list of all resident SecurityHealthAnalyticsCustomModules under
  // the given CRM parent and all of the parent’s CRM descendants.
  rpc ListDescendantSecurityHealthAnalyticsCustomModules(
      ListDescendantSecurityHealthAnalyticsCustomModulesRequest)
      returns (ListDescendantSecurityHealthAnalyticsCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/securityHealthAnalyticsSettings}/customModules:listDescendant"
      additional_bindings {
        get: "/v1/{parent=folders/*/securityHealthAnalyticsSettings}/customModules:listDescendant"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*/securityHealthAnalyticsSettings}/customModules:listDescendant"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists an organization or source's findings.
  //
  // To list across all sources provide a `-` as the source id.
  // Example: /v1/organizations/{organization_id}/sources/-/findings
  rpc ListFindings(ListFindingsRequest) returns (ListFindingsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/sources/*}/findings"
      additional_bindings { get: "/v1/{parent=folders/*/sources/*}/findings" }
      additional_bindings { get: "/v1/{parent=projects/*/sources/*}/findings" }
    };
  }

  // Lists mute configs.
  rpc ListMuteConfigs(ListMuteConfigsRequest)
      returns (ListMuteConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*}/muteConfigs"
      additional_bindings { get: "/v1/{parent=folders/*}/muteConfigs" }
      additional_bindings { get: "/v1/{parent=projects/*}/muteConfigs" }
      additional_bindings {
        get: "/v1/{parent=organizations/*/locations/*/muteConfigs}"
      }
      additional_bindings {
        get: "/v1/{parent=folders/*/locations/*/muteConfigs}"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/muteConfigs}"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists notification configs.
  rpc ListNotificationConfigs(ListNotificationConfigsRequest)
      returns (ListNotificationConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*}/notificationConfigs"
      additional_bindings { get: "/v1/{parent=folders/*}/notificationConfigs" }
      additional_bindings { get: "/v1/{parent=projects/*}/notificationConfigs" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns a list of all EffectiveSecurityHealthAnalyticsCustomModules for the
  // given parent. This includes resident modules defined at the scope of the
  // parent, and inherited modules, inherited from CRM ancestors.
  rpc ListEffectiveSecurityHealthAnalyticsCustomModules(
      ListEffectiveSecurityHealthAnalyticsCustomModulesRequest)
      returns (ListEffectiveSecurityHealthAnalyticsCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/securityHealthAnalyticsSettings}/effectiveCustomModules"
      additional_bindings {
        get: "/v1/{parent=folders/*/securityHealthAnalyticsSettings}/effectiveCustomModules"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*/securityHealthAnalyticsSettings}/effectiveCustomModules"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns a list of all SecurityHealthAnalyticsCustomModules for the given
  // parent. This includes resident modules defined at the scope of the parent,
  // and inherited modules, inherited from CRM ancestors.
  rpc ListSecurityHealthAnalyticsCustomModules(
      ListSecurityHealthAnalyticsCustomModulesRequest)
      returns (ListSecurityHealthAnalyticsCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/securityHealthAnalyticsSettings}/customModules"
      additional_bindings {
        get: "/v1/{parent=folders/*/securityHealthAnalyticsSettings}/customModules"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*/securityHealthAnalyticsSettings}/customModules"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all sources belonging to an organization.
  rpc ListSources(ListSourcesRequest) returns (ListSourcesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*}/sources"
      additional_bindings { get: "/v1/{parent=folders/*}/sources" }
      additional_bindings { get: "/v1/{parent=projects/*}/sources" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Runs asset discovery. The discovery is tracked with a long-running
  // operation.
  //
  // This API can only be called with limited frequency for an organization. If
  // it is called too frequently the caller will receive a TOO_MANY_REQUESTS
  // error.
  rpc RunAssetDiscovery(RunAssetDiscoveryRequest)
      returns (google.longrunning.Operation) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*}/assets:runDiscovery"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.securitycenter.v1.RunAssetDiscoveryResponse"
      metadata_type: "google.protobuf.Empty"
    };
  }

  // Updates the state of a finding.
  rpc SetFindingState(SetFindingStateRequest) returns (Finding) {
    option (google.api.http) = {
      post: "/v1/{name=organizations/*/sources/*/findings/*}:setState"
      body: "*"
      additional_bindings {
        post: "/v1/{name=folders/*/sources/*/findings/*}:setState"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{name=projects/*/sources/*/findings/*}:setState"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,state,start_time";
  }

  // Updates the mute state of a finding.
  rpc SetMute(SetMuteRequest) returns (Finding) {
    option (google.api.http) = {
      post: "/v1/{name=organizations/*/sources/*/findings/*}:setMute"
      body: "*"
      additional_bindings {
        post: "/v1/{name=folders/*/sources/*/findings/*}:setMute"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{name=projects/*/sources/*/findings/*}:setMute"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,mute";
  }

  // Sets the access control policy on the specified Source.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1/{resource=organizations/*/sources/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Returns the permissions that a caller has on the specified source.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1/{resource=organizations/*/sources/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource,permissions";
  }

  // Simulates a given SecurityHealthAnalyticsCustomModule and Resource.
  rpc SimulateSecurityHealthAnalyticsCustomModule(
      SimulateSecurityHealthAnalyticsCustomModuleRequest)
      returns (SimulateSecurityHealthAnalyticsCustomModuleResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*/securityHealthAnalyticsSettings}/customModules:simulate"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=folders/*/securityHealthAnalyticsSettings}/customModules:simulate"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*/securityHealthAnalyticsSettings}/customModules:simulate"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,custom_config,resource";
  }

  // Updates external system. This is for a given finding.
  rpc UpdateExternalSystem(UpdateExternalSystemRequest)
      returns (ExternalSystem) {
    option (google.api.http) = {
      patch: "/v1/{external_system.name=organizations/*/sources/*/findings/*/externalSystems/*}"
      body: "external_system"
      additional_bindings {
        patch: "/v1/{external_system.name=folders/*/sources/*/findings/*/externalSystems/*}"
        body: "external_system"
      }
      additional_bindings {
        patch: "/v1/{external_system.name=projects/*/sources/*/findings/*/externalSystems/*}"
        body: "external_system"
      }
    };
    option (google.api.method_signature) = "external_system,update_mask";
  }

  // Creates or updates a finding. The corresponding source must exist for a
  // finding creation to succeed.
  rpc UpdateFinding(UpdateFindingRequest) returns (Finding) {
    option (google.api.http) = {
      patch: "/v1/{finding.name=organizations/*/sources/*/findings/*}"
      body: "finding"
      additional_bindings {
        patch: "/v1/{finding.name=folders/*/sources/*/findings/*}"
        body: "finding"
      }
      additional_bindings {
        patch: "/v1/{finding.name=projects/*/sources/*/findings/*}"
        body: "finding"
      }
    };
    option (google.api.method_signature) = "finding";
  }

  // Updates a mute config.
  rpc UpdateMuteConfig(UpdateMuteConfigRequest) returns (MuteConfig) {
    option (google.api.http) = {
      patch: "/v1/{mute_config.name=organizations/*/muteConfigs/*}"
      body: "mute_config"
      additional_bindings {
        patch: "/v1/{mute_config.name=folders/*/muteConfigs/*}"
        body: "mute_config"
      }
      additional_bindings {
        patch: "/v1/{mute_config.name=projects/*/muteConfigs/*}"
        body: "mute_config"
      }
      additional_bindings {
        patch: "/v1/{mute_config.name=organizations/*/locations/*/muteConfigs/*}"
        body: "mute_config"
      }
      additional_bindings {
        patch: "/v1/{mute_config.name=folders/*/locations/*/muteConfigs/*}"
        body: "mute_config"
      }
      additional_bindings {
        patch: "/v1/{mute_config.name=projects/*/locations/*/muteConfigs/*}"
        body: "mute_config"
      }
    };
    option (google.api.method_signature) = "mute_config,update_mask";
  }

  //
  // Updates a notification config. The following update
  // fields are allowed: description, pubsub_topic, streaming_config.filter
  rpc UpdateNotificationConfig(UpdateNotificationConfigRequest)
      returns (NotificationConfig) {
    option (google.api.http) = {
      patch: "/v1/{notification_config.name=organizations/*/notificationConfigs/*}"
      body: "notification_config"
      additional_bindings {
        patch: "/v1/{notification_config.name=folders/*/notificationConfigs/*}"
        body: "notification_config"
      }
      additional_bindings {
        patch: "/v1/{notification_config.name=projects/*/notificationConfigs/*}"
        body: "notification_config"
      }
    };
    option (google.api.method_signature) = "notification_config";
    option (google.api.method_signature) = "notification_config,update_mask";
  }

  // Updates an organization's settings.
  rpc UpdateOrganizationSettings(UpdateOrganizationSettingsRequest)
      returns (OrganizationSettings) {
    option (google.api.http) = {
      patch: "/v1/{organization_settings.name=organizations/*/organizationSettings}"
      body: "organization_settings"
    };
    option (google.api.method_signature) = "organization_settings";
  }

  // Updates the SecurityHealthAnalyticsCustomModule under the given name based
  // on the given update mask. Updating the enablement state is supported on
  // both resident and inherited modules (though resident modules cannot have an
  // enablement state of "inherited"). Updating the display name and custom
  // config of a module is supported on resident modules only.
  rpc UpdateSecurityHealthAnalyticsCustomModule(
      UpdateSecurityHealthAnalyticsCustomModuleRequest)
      returns (SecurityHealthAnalyticsCustomModule) {
    option (google.api.http) = {
      patch: "/v1/{security_health_analytics_custom_module.name=organizations/*/securityHealthAnalyticsSettings/customModules/*}"
      body: "security_health_analytics_custom_module"
      additional_bindings {
        patch: "/v1/{security_health_analytics_custom_module.name=folders/*/securityHealthAnalyticsSettings/customModules/*}"
        body: "security_health_analytics_custom_module"
      }
      additional_bindings {
        patch: "/v1/{security_health_analytics_custom_module.name=projects/*/securityHealthAnalyticsSettings/customModules/*}"
        body: "security_health_analytics_custom_module"
      }
    };
    option (google.api.method_signature) =
        "security_health_analytics_custom_module,update_mask";
  }

  // Updates a source.
  rpc UpdateSource(UpdateSourceRequest) returns (Source) {
    option (google.api.http) = {
      patch: "/v1/{source.name=organizations/*/sources/*}"
      body: "source"
    };
    option (google.api.method_signature) = "source";
  }

  // Updates security marks.
  rpc UpdateSecurityMarks(UpdateSecurityMarksRequest) returns (SecurityMarks) {
    option (google.api.http) = {
      patch: "/v1/{security_marks.name=organizations/*/assets/*/securityMarks}"
      body: "security_marks"
      additional_bindings {
        patch: "/v1/{security_marks.name=folders/*/assets/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v1/{security_marks.name=projects/*/assets/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v1/{security_marks.name=organizations/*/sources/*/findings/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v1/{security_marks.name=folders/*/sources/*/findings/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v1/{security_marks.name=projects/*/sources/*/findings/*/securityMarks}"
        body: "security_marks"
      }
    };
    option (google.api.method_signature) = "security_marks";
  }

  // Creates a BigQuery export.
  rpc CreateBigQueryExport(CreateBigQueryExportRequest)
      returns (BigQueryExport) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*}/bigQueryExports"
      body: "big_query_export"
      additional_bindings {
        post: "/v1/{parent=folders/*}/bigQueryExports"
        body: "big_query_export"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*}/bigQueryExports"
        body: "big_query_export"
      }
    };
    option (google.api.method_signature) =
        "parent,big_query_export,big_query_export_id";
  }

  // Deletes an existing BigQuery export.
  rpc DeleteBigQueryExport(DeleteBigQueryExportRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/bigQueryExports/*}"
      additional_bindings { delete: "/v1/{name=folders/*/bigQueryExports/*}" }
      additional_bindings { delete: "/v1/{name=projects/*/bigQueryExports/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a BigQuery export.
  rpc UpdateBigQueryExport(UpdateBigQueryExportRequest)
      returns (BigQueryExport) {
    option (google.api.http) = {
      patch: "/v1/{big_query_export.name=organizations/*/bigQueryExports/*}"
      body: "big_query_export"
      additional_bindings {
        patch: "/v1/{big_query_export.name=folders/*/bigQueryExports/*}"
        body: "big_query_export"
      }
      additional_bindings {
        patch: "/v1/{big_query_export.name=projects/*/bigQueryExports/*}"
        body: "big_query_export"
      }
    };
    option (google.api.method_signature) = "big_query_export,update_mask";
  }

  // Lists BigQuery exports. Note that when requesting BigQuery exports at a
  // given level all exports under that level are also returned e.g. if
  // requesting BigQuery exports under a folder, then all BigQuery exports
  // immediately under the folder plus the ones created under the projects
  // within the folder are returned.
  rpc ListBigQueryExports(ListBigQueryExportsRequest)
      returns (ListBigQueryExportsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*}/bigQueryExports"
      additional_bindings { get: "/v1/{parent=folders/*}/bigQueryExports" }
      additional_bindings { get: "/v1/{parent=projects/*}/bigQueryExports" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a resident Event Threat Detection custom module at the scope of the
  // given Resource Manager parent, and also creates inherited custom modules
  // for all descendants of the given parent. These modules are enabled by
  // default.
  rpc CreateEventThreatDetectionCustomModule(
      CreateEventThreatDetectionCustomModuleRequest)
      returns (EventThreatDetectionCustomModule) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*/eventThreatDetectionSettings}/customModules"
      body: "event_threat_detection_custom_module"
      additional_bindings {
        post: "/v1/{parent=folders/*/eventThreatDetectionSettings}/customModules"
        body: "event_threat_detection_custom_module"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*/eventThreatDetectionSettings}/customModules"
        body: "event_threat_detection_custom_module"
      }
    };
    option (google.api.method_signature) =
        "parent,event_threat_detection_custom_module";
  }

  // Deletes the specified Event Threat Detection custom module and all of its
  // descendants in the Resource Manager hierarchy. This method is only
  // supported for resident custom modules.
  rpc DeleteEventThreatDetectionCustomModule(
      DeleteEventThreatDetectionCustomModuleRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/eventThreatDetectionSettings/customModules/*}"
      additional_bindings {
        delete: "/v1/{name=folders/*/eventThreatDetectionSettings/customModules/*}"
      }
      additional_bindings {
        delete: "/v1/{name=projects/*/eventThreatDetectionSettings/customModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets an Event Threat Detection custom module.
  rpc GetEventThreatDetectionCustomModule(
      GetEventThreatDetectionCustomModuleRequest)
      returns (EventThreatDetectionCustomModule) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/eventThreatDetectionSettings/customModules/*}"
      additional_bindings {
        get: "/v1/{name=folders/*/eventThreatDetectionSettings/customModules/*}"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/eventThreatDetectionSettings/customModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all resident Event Threat Detection custom modules under the
  // given Resource Manager parent and its descendants.
  rpc ListDescendantEventThreatDetectionCustomModules(
      ListDescendantEventThreatDetectionCustomModulesRequest)
      returns (ListDescendantEventThreatDetectionCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/eventThreatDetectionSettings}/customModules:listDescendant"
      additional_bindings {
        get: "/v1/{parent=folders/*/eventThreatDetectionSettings}/customModules:listDescendant"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*/eventThreatDetectionSettings}/customModules:listDescendant"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all Event Threat Detection custom modules for the given
  // Resource Manager parent. This includes resident modules defined at the
  // scope of the parent along with modules inherited from ancestors.
  rpc ListEventThreatDetectionCustomModules(
      ListEventThreatDetectionCustomModulesRequest)
      returns (ListEventThreatDetectionCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/eventThreatDetectionSettings}/customModules"
      additional_bindings {
        get: "/v1/{parent=folders/*/eventThreatDetectionSettings}/customModules"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*/eventThreatDetectionSettings}/customModules"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the Event Threat Detection custom module with the given name based
  // on the given update mask. Updating the enablement state is supported for
  // both resident and inherited modules (though resident modules cannot have an
  // enablement state of "inherited"). Updating the display name or
  // configuration of a module is supported for resident modules only. The type
  // of a module cannot be changed.
  rpc UpdateEventThreatDetectionCustomModule(
      UpdateEventThreatDetectionCustomModuleRequest)
      returns (EventThreatDetectionCustomModule) {
    option (google.api.http) = {
      patch: "/v1/{event_threat_detection_custom_module.name=organizations/*/eventThreatDetectionSettings/customModules/*}"
      body: "event_threat_detection_custom_module"
      additional_bindings {
        patch: "/v1/{event_threat_detection_custom_module.name=folders/*/eventThreatDetectionSettings/customModules/*}"
        body: "event_threat_detection_custom_module"
      }
      additional_bindings {
        patch: "/v1/{event_threat_detection_custom_module.name=projects/*/eventThreatDetectionSettings/customModules/*}"
        body: "event_threat_detection_custom_module"
      }
    };
    option (google.api.method_signature) =
        "event_threat_detection_custom_module,update_mask";
  }

  // Validates the given Event Threat Detection custom module.
  rpc ValidateEventThreatDetectionCustomModule(
      ValidateEventThreatDetectionCustomModuleRequest)
      returns (ValidateEventThreatDetectionCustomModuleResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*/eventThreatDetectionSettings}:validateCustomModule"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=folders/*/eventThreatDetectionSettings}:validateCustomModule"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{parent=projects/*/eventThreatDetectionSettings}:validateCustomModule"
        body: "*"
      }
    };
  }

  // Gets an effective Event Threat Detection custom module at the given level.
  rpc GetEffectiveEventThreatDetectionCustomModule(
      GetEffectiveEventThreatDetectionCustomModuleRequest)
      returns (EffectiveEventThreatDetectionCustomModule) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/eventThreatDetectionSettings/effectiveCustomModules/*}"
      additional_bindings {
        get: "/v1/{name=folders/*/eventThreatDetectionSettings/effectiveCustomModules/*}"
      }
      additional_bindings {
        get: "/v1/{name=projects/*/eventThreatDetectionSettings/effectiveCustomModules/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all effective Event Threat Detection custom modules for the
  // given parent. This includes resident modules defined at the scope of the
  // parent along with modules inherited from its ancestors.
  rpc ListEffectiveEventThreatDetectionCustomModules(
      ListEffectiveEventThreatDetectionCustomModulesRequest)
      returns (ListEffectiveEventThreatDetectionCustomModulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/eventThreatDetectionSettings}/effectiveCustomModules"
      additional_bindings {
        get: "/v1/{parent=folders/*/eventThreatDetectionSettings}/effectiveCustomModules"
      }
      additional_bindings {
        get: "/v1/{parent=projects/*/eventThreatDetectionSettings}/effectiveCustomModules"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a ResourceValueConfig for an organization. Maps user's tags to
  // difference resource values for use by the attack path simulation.
  rpc BatchCreateResourceValueConfigs(BatchCreateResourceValueConfigsRequest)
      returns (BatchCreateResourceValueConfigsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*}/resourceValueConfigs:batchCreate"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
  }

  // Deletes a ResourceValueConfig.
  rpc DeleteResourceValueConfig(DeleteResourceValueConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/resourceValueConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a ResourceValueConfig.
  rpc GetResourceValueConfig(GetResourceValueConfigRequest)
      returns (ResourceValueConfig) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/resourceValueConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all ResourceValueConfigs.
  rpc ListResourceValueConfigs(ListResourceValueConfigsRequest)
      returns (ListResourceValueConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*}/resourceValueConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates an existing ResourceValueConfigs with new rules.
  rpc UpdateResourceValueConfig(UpdateResourceValueConfigRequest)
      returns (ResourceValueConfig) {
    option (google.api.http) = {
      patch: "/v1/{resource_value_config.name=organizations/*/resourceValueConfigs/*}"
      body: "resource_value_config"
    };
    option (google.api.method_signature) = "resource_value_config,update_mask";
  }

  // Lists the valued resources for a set of simulation results and filter.
  rpc ListValuedResources(ListValuedResourcesRequest)
      returns (ListValuedResourcesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/simulations/*}/valuedResources"
      additional_bindings {
        get: "/v1/{parent=organizations/*/simulations/*/attackExposureResults/*}/valuedResources"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists the attack paths for a set of simulation results or valued resources
  // and filter.
  rpc ListAttackPaths(ListAttackPathsRequest)
      returns (ListAttackPathsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/simulations/*}/attackPaths"
      additional_bindings {
        get: "/v1/{parent=organizations/*/simulations/*/valuedResources/*}/attackPaths"
      }
      additional_bindings {
        get: "/v1/{parent=organizations/*/simulations/*/attackExposureResults/*}/attackPaths"
      }
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for bulk findings update.
//
// Note:
// 1. If multiple bulk update requests match the same resource, the order in
// which they get executed is not defined.
// 2. Once a bulk operation is started, there is no way to stop it.
message BulkMuteFindingsRequest {
  // The mute state.
  enum MuteState {
    // Unused.
    MUTE_STATE_UNSPECIFIED = 0;

    // Matching findings will be muted (default).
    MUTED = 1;

    // Matching findings will have their mute state cleared.
    UNDEFINED = 2;
  }

  // Required. The parent, at which bulk action needs to be applied. Its format
  // is `organizations/[organization_id]`, `folders/[folder_id]`,
  // `projects/[project_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "*" }
  ];

  // Expression that identifies findings that should be updated.
  // The expression is a list of zero or more restrictions combined
  // via logical operators `AND` and `OR`. Parentheses are supported, and `OR`
  // has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a
  // `-` character in front of them to indicate negation. The fields map to
  // those defined in the corresponding resource.
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  string filter = 2;

  // This can be a mute configuration name or any identifier for mute/unmute
  // of findings based on the filter.
  string mute_annotation = 3 [deprecated = true];

  // Optional. All findings matching the given filter will have their mute state
  // set to this value. The default value is `MUTED`. Setting this to
  // `UNDEFINED` will clear the mute state on all matching findings.
  MuteState mute_state = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The response to a BulkMute request. Contains the LRO information.
message BulkMuteFindingsResponse {}

// Request message for creating a finding.
message CreateFindingRequest {
  // Required. Resource name of the new finding's parent. Its format should be
  // `organizations/[organization_id]/sources/[source_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];

  // Required. Unique identifier provided by the client within the parent scope.
  // It must be alphanumeric and less than or equal to 32 characters and
  // greater than 0 characters in length.
  string finding_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Finding being created. The name and security_marks will be
  // ignored as they are both output only fields on this resource.
  Finding finding = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for creating a mute config.
message CreateMuteConfigRequest {
  // Required. Resource name of the new mute configs's parent. Its format is
  // `organizations/[organization_id]`, `folders/[folder_id]`, or
  // `projects/[project_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/MuteConfig"
    }
  ];

  // Required. The mute config being created.
  MuteConfig mute_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Unique identifier provided by the client within the parent scope.
  // It must consist of only lowercase letters, numbers, and hyphens, must start
  // with a letter, must end with either a letter or a number, and must be 63
  // characters or less.
  string mute_config_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message to create single resource value config
message CreateResourceValueConfigRequest {
  // Required. Resource name of the new ResourceValueConfig's parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/ResourceValueConfig"
    }
  ];

  // Required. The resource value config being created.
  ResourceValueConfig resource_value_config = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message to create multiple resource value configs
message BatchCreateResourceValueConfigsRequest {
  // Required. Resource name of the new ResourceValueConfig's parent.
  // The parent field in the CreateResourceValueConfigRequest
  // messages must either be empty or match this field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/ResourceValueConfig"
    }
  ];

  // Required. The resource value configs to be created.
  repeated CreateResourceValueConfigRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for BatchCreateResourceValueConfigs
message BatchCreateResourceValueConfigsResponse {
  // The resource value configs created
  repeated ResourceValueConfig resource_value_configs = 1;
}

// Request message to delete resource value config
message DeleteResourceValueConfigRequest {
  // Required. Name of the ResourceValueConfig to delete
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/ResourceValueConfig"
    }
  ];
}

// Request message to get resource value config
message GetResourceValueConfigRequest {
  // Required. Name of the resource value config to retrieve. Its format is
  // `organizations/{organization}/resourceValueConfigs/{config_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/ResourceValueConfig"
    }
  ];
}

// Request message to list resource value configs of a parent
message ListResourceValueConfigsRequest {
  // Required. The parent, which owns the collection of resource value configs.
  // Its format is
  // `organizations/[organization_id]`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/ResourceValueConfig"
    }
  ];

  // The number of results to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 configs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListResourceValueConfigs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListResourceValueConfigs` must match the call that provided the
  // page token.
  //
  // page_size can be specified, and the new page_size will be used.
  string page_token = 3;
}

// Response message to list resource value configs
message ListResourceValueConfigsResponse {
  // The resource value configs from the specified parent.
  repeated ResourceValueConfig resource_value_configs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is empty, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message to update resource value config
message UpdateResourceValueConfigRequest {
  // Required. The resource value config being updated.
  ResourceValueConfig resource_value_config = 1
      [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for creating a notification config.
message CreateNotificationConfigRequest {
  // Required. Resource name of the new notification config's parent. Its format
  // is `organizations/[organization_id]`, `folders/[folder_id]`, or
  // `projects/[project_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/NotificationConfig"
    }
  ];

  // Required.
  // Unique identifier provided by the client within the parent scope.
  // It must be between 1 and 128 characters and contain alphanumeric
  // characters, underscores, or hyphens only.
  string config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The notification config being created. The name and the service
  // account will be ignored as they are both output only fields on this
  // resource.
  NotificationConfig notification_config = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for creating Security Health Analytics custom modules.
message CreateSecurityHealthAnalyticsCustomModuleRequest {
  // Required. Resource name of the new custom module's parent. Its format is
  // `organizations/{organization}/securityHealthAnalyticsSettings`,
  // `folders/{folder}/securityHealthAnalyticsSettings`, or
  // `projects/{project}/securityHealthAnalyticsSettings`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];

  // Required. SecurityHealthAnalytics custom module to create. The provided
  // name is ignored and reset with provided parent information and
  // server-generated ID.
  SecurityHealthAnalyticsCustomModule security_health_analytics_custom_module =
      2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for creating a source.
message CreateSourceRequest {
  // Required. Resource name of the new source's parent. Its format should be
  // `organizations/[organization_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // Required. The Source being created, only the display_name and description
  // will be used. All other fields will be ignored.
  Source source = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for deleting a mute config.
message DeleteMuteConfigRequest {
  // Required. Name of the mute config to delete. Its format is
  // `organizations/{organization}/muteConfigs/{config_id}`,
  // `folders/{folder}/muteConfigs/{config_id}`,
  // `projects/{project}/muteConfigs/{config_id}`,
  // `organizations/{organization}/locations/global/muteConfigs/{config_id}`,
  // `folders/{folder}/locations/global/muteConfigs/{config_id}`, or
  // `projects/{project}/locations/global/muteConfigs/{config_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/MuteConfig"
    }
  ];
}

// Request message for deleting a notification config.
message DeleteNotificationConfigRequest {
  // Required. Name of the notification config to delete. Its format is
  // `organizations/[organization_id]/notificationConfigs/[config_id]`,
  // `folders/[folder_id]/notificationConfigs/[config_id]`,
  // or `projects/[project_id]/notificationConfigs/[config_id]`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/NotificationConfig"
    }
  ];
}

// Request message for deleting Security Health Analytics custom modules.
message DeleteSecurityHealthAnalyticsCustomModuleRequest {
  // Required. Name of the custom module to delete. Its format is
  // `organizations/{organization}/securityHealthAnalyticsSettings/customModules/{customModule}`,
  // `folders/{folder}/securityHealthAnalyticsSettings/customModules/{customModule}`,
  // or
  // `projects/{project}/securityHealthAnalyticsSettings/customModules/{customModule}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];
}

// Request message for retrieving a BigQuery export.
message GetBigQueryExportRequest {
  // Required. Name of the BigQuery export to retrieve. Its format is
  // `organizations/{organization}/bigQueryExports/{export_id}`,
  // `folders/{folder}/bigQueryExports/{export_id}`, or
  // `projects/{project}/bigQueryExports/{export_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/BigQueryExport"
    }
  ];
}

// Request message for retrieving a mute config.
message GetMuteConfigRequest {
  // Required. Name of the mute config to retrieve. Its format is
  // `organizations/{organization}/muteConfigs/{config_id}`,
  // `folders/{folder}/muteConfigs/{config_id}`,
  // `projects/{project}/muteConfigs/{config_id}`,
  // `organizations/{organization}/locations/global/muteConfigs/{config_id}`,
  // `folders/{folder}/locations/global/muteConfigs/{config_id}`, or
  // `projects/{project}/locations/global/muteConfigs/{config_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/MuteConfig"
    }
  ];
}

// Request message for getting a notification config.
message GetNotificationConfigRequest {
  // Required. Name of the notification config to get. Its format is
  // `organizations/[organization_id]/notificationConfigs/[config_id]`,
  // `folders/[folder_id]/notificationConfigs/[config_id]`,
  // or `projects/[project_id]/notificationConfigs/[config_id]`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/NotificationConfig"
    }
  ];
}

// Request message for getting organization settings.
message GetOrganizationSettingsRequest {
  // Required. Name of the organization to get organization settings for. Its
  // format is `organizations/[organization_id]/organizationSettings`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/OrganizationSettings"
    }
  ];
}

// Request message for getting effective Security Health Analytics custom
// modules.
message GetEffectiveSecurityHealthAnalyticsCustomModuleRequest {
  // Required. Name of the effective custom module to get. Its format is
  // `organizations/{organization}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}`,
  // `folders/{folder}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}`,
  // or
  // `projects/{project}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/EffectiveSecurityHealthAnalyticsCustomModule"
    }
  ];
}

// Request message for getting Security Health Analytics custom modules.
message GetSecurityHealthAnalyticsCustomModuleRequest {
  // Required. Name of the custom module to get. Its format is
  // `organizations/{organization}/securityHealthAnalyticsSettings/customModules/{customModule}`,
  // `folders/{folder}/securityHealthAnalyticsSettings/customModules/{customModule}`,
  // or
  // `projects/{project}/securityHealthAnalyticsSettings/customModules/{customModule}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];
}

// Request message for getting a source.
message GetSourceRequest {
  // Required. Relative resource name of the source. Its format is
  // `organizations/[organization_id]/source/[source_id]`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];
}

// Request message for grouping by assets.
message GroupAssetsRequest {
  // Required. The name of the parent to group the assets by. Its format is
  // `organizations/[organization_id]`, `folders/[folder_id]`, or
  // `projects/[project_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/Asset"
    }
  ];

  // Expression that defines the filter to apply across assets.
  // The expression is a list of zero or more restrictions combined via logical
  // operators `AND` and `OR`.
  // Parentheses are supported, and `OR` has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a `-`
  // character in front of them to indicate negation. The fields map to those
  // defined in the Asset resource. Examples include:
  //
  // * name
  // * security_center_properties.resource_name
  // * resource_properties.a_property
  // * security_marks.marks.marka
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  //
  // The following field and operator combinations are supported:
  //
  // * name: `=`
  // * update_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `update_time = "2019-06-10T16:07:18-07:00"`
  //     `update_time = 1560208038000`
  //
  // * create_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `create_time = "2019-06-10T16:07:18-07:00"`
  //     `create_time = 1560208038000`
  //
  // * iam_policy.policy_blob: `=`, `:`
  // * resource_properties: `=`, `:`, `>`, `<`, `>=`, `<=`
  // * security_marks.marks: `=`, `:`
  // * security_center_properties.resource_name: `=`, `:`
  // * security_center_properties.resource_display_name: `=`, `:`
  // * security_center_properties.resource_type: `=`, `:`
  // * security_center_properties.resource_parent: `=`, `:`
  // * security_center_properties.resource_parent_display_name: `=`, `:`
  // * security_center_properties.resource_project: `=`, `:`
  // * security_center_properties.resource_project_display_name: `=`, `:`
  // * security_center_properties.resource_owners: `=`, `:`
  //
  // For example, `resource_properties.size = 100` is a valid filter string.
  //
  // Use a partial match on the empty string to filter based on a property
  // existing: `resource_properties.my_property : ""`
  //
  // Use a negated partial match on the empty string to filter based on a
  // property not existing: `-resource_properties.my_property : ""`
  string filter = 2;

  // Required. Expression that defines what assets fields to use for grouping.
  // The string value should follow SQL syntax: comma separated list of fields.
  // For example:
  // "security_center_properties.resource_project,security_center_properties.project".
  //
  // The following fields are supported when compare_duration is not set:
  //
  // * security_center_properties.resource_project
  // * security_center_properties.resource_project_display_name
  // * security_center_properties.resource_type
  // * security_center_properties.resource_parent
  // * security_center_properties.resource_parent_display_name
  //
  // The following fields are supported when compare_duration is set:
  //
  // * security_center_properties.resource_type
  // * security_center_properties.resource_project_display_name
  // * security_center_properties.resource_parent_display_name
  string group_by = 3 [(google.api.field_behavior) = REQUIRED];

  // When compare_duration is set, the GroupResult's "state_change" property is
  // updated to indicate whether the asset was added, removed, or remained
  // present during the compare_duration period of time that precedes the
  // read_time. This is the time between (read_time - compare_duration) and
  // read_time.
  //
  // The state change value is derived based on the presence of the asset at the
  // two points in time. Intermediate state changes between the two times don't
  // affect the result. For example, the results aren't affected if the asset is
  // removed and re-created again.
  //
  // Possible "state_change" values when compare_duration is specified:
  //
  // * "ADDED":   indicates that the asset was not present at the start of
  //                compare_duration, but present at reference_time.
  // * "REMOVED": indicates that the asset was present at the start of
  //                compare_duration, but not present at reference_time.
  // * "ACTIVE":  indicates that the asset was present at both the
  //                start and the end of the time period defined by
  //                compare_duration and reference_time.
  //
  // If compare_duration is not specified, then the only possible state_change
  // is "UNUSED", which will be the state_change set for all assets present at
  // read_time.
  //
  // If this field is set then `state_change` must be a specified field in
  // `group_by`.
  google.protobuf.Duration compare_duration = 4;

  // Time used as a reference point when filtering assets. The filter is limited
  // to assets existing at the supplied time and their values are those at that
  // specific time. Absence of this field will default to the API's version of
  // NOW.
  google.protobuf.Timestamp read_time = 5;

  // The value returned by the last `GroupAssetsResponse`; indicates
  // that this is a continuation of a prior `GroupAssets` call, and that the
  // system should return the next page of data.
  string page_token = 7;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 8;
}

// Response message for grouping by assets.
message GroupAssetsResponse {
  // Group results. There exists an element for each existing unique
  // combination of property/values. The element contains a count for the number
  // of times those specific property/values appear.
  repeated GroupResult group_by_results = 1;

  // Time used for executing the groupBy request.
  google.protobuf.Timestamp read_time = 2;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 3;

  // The total number of results matching the query.
  int32 total_size = 4;
}

// Request message for grouping by findings.
message GroupFindingsRequest {
  // Required. Name of the source to groupBy. Its format is
  // `organizations/[organization_id]/sources/[source_id]`,
  // `folders/[folder_id]/sources/[source_id]`, or
  // `projects/[project_id]/sources/[source_id]`. To groupBy across all sources
  // provide a source_id of `-`. For example:
  // `organizations/{organization_id}/sources/-, folders/{folder_id}/sources/-`,
  // or `projects/{project_id}/sources/-`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];

  // Expression that defines the filter to apply across findings.
  // The expression is a list of one or more restrictions combined via logical
  // operators `AND` and `OR`.
  // Parentheses are supported, and `OR` has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a `-`
  // character in front of them to indicate negation. Examples include:
  //
  //  * name
  //  * source_properties.a_property
  //  * security_marks.marks.marka
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  //
  // The following field and operator combinations are supported:
  //
  // * name: `=`
  // * parent: `=`, `:`
  // * resource_name: `=`, `:`
  // * state: `=`, `:`
  // * category: `=`, `:`
  // * external_uri: `=`, `:`
  // * event_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `event_time = "2019-06-10T16:07:18-07:00"`
  //     `event_time = 1560208038000`
  //
  // * severity: `=`, `:`
  // * workflow_state: `=`, `:`
  // * security_marks.marks: `=`, `:`
  // * source_properties: `=`, `:`, `>`, `<`, `>=`, `<=`
  //
  //   For example, `source_properties.size = 100` is a valid filter string.
  //
  //   Use a partial match on the empty string to filter based on a property
  //   existing: `source_properties.my_property : ""`
  //
  //   Use a negated partial match on the empty string to filter based on a
  //   property not existing: `-source_properties.my_property : ""`
  //
  // * resource:
  //   * resource.name: `=`, `:`
  //   * resource.parent_name: `=`, `:`
  //   * resource.parent_display_name: `=`, `:`
  //   * resource.project_name: `=`, `:`
  //   * resource.project_display_name: `=`, `:`
  //   * resource.type: `=`, `:`
  string filter = 2;

  // Required. Expression that defines what assets fields to use for grouping
  // (including `state_change`). The string value should follow SQL syntax:
  // comma separated list of fields. For example: "parent,resource_name".
  //
  // The following fields are supported when compare_duration is set:
  //
  // * state_change
  string group_by = 3 [(google.api.field_behavior) = REQUIRED];

  // Time used as a reference point when filtering findings. The filter is
  // limited to findings existing at the supplied time and their values are
  // those at that specific time. Absence of this field will default to the
  // API's version of NOW.
  google.protobuf.Timestamp read_time = 4;

  // When compare_duration is set, the GroupResult's "state_change" attribute is
  // updated to indicate whether the finding had its state changed, the
  // finding's state remained unchanged, or if the finding was added during the
  // compare_duration period of time that precedes the read_time. This is the
  // time between (read_time - compare_duration) and read_time.
  //
  // The state_change value is derived based on the presence and state of the
  // finding at the two points in time. Intermediate state changes between the
  // two times don't affect the result. For example, the results aren't affected
  // if the finding is made inactive and then active again.
  //
  // Possible "state_change" values when compare_duration is specified:
  //
  // * "CHANGED":   indicates that the finding was present and matched the given
  //                  filter at the start of compare_duration, but changed its
  //                  state at read_time.
  // * "UNCHANGED": indicates that the finding was present and matched the given
  //                  filter at the start of compare_duration and did not change
  //                  state at read_time.
  // * "ADDED":     indicates that the finding did not match the given filter or
  //                  was not present at the start of compare_duration, but was
  //                  present at read_time.
  // * "REMOVED":   indicates that the finding was present and matched the
  //                  filter at the start of compare_duration, but did not match
  //                  the filter at read_time.
  //
  // If compare_duration is not specified, then the only possible state_change
  // is "UNUSED",  which will be the state_change set for all findings present
  // at read_time.
  //
  // If this field is set then `state_change` must be a specified field in
  // `group_by`.
  google.protobuf.Duration compare_duration = 5;

  // The value returned by the last `GroupFindingsResponse`; indicates
  // that this is a continuation of a prior `GroupFindings` call, and
  // that the system should return the next page of data.
  string page_token = 7;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 8;
}

// Response message for group by findings.
message GroupFindingsResponse {
  // Group results. There exists an element for each existing unique
  // combination of property/values. The element contains a count for the number
  // of times those specific property/values appear.
  repeated GroupResult group_by_results = 1;

  // Time used for executing the groupBy request.
  google.protobuf.Timestamp read_time = 2;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 3;

  // The total number of results matching the query.
  int32 total_size = 4;
}

// Result containing the properties and count of a groupBy request.
message GroupResult {
  // Properties matching the groupBy fields in the request.
  map<string, google.protobuf.Value> properties = 1;

  // Total count of resources for the given properties.
  int64 count = 2;
}

// Request message for listing descendant Security Health Analytics custom
// modules.
message ListDescendantSecurityHealthAnalyticsCustomModulesRequest {
  // Required. Name of parent to list descendant custom modules. Its format is
  // `organizations/{organization}/securityHealthAnalyticsSettings`,
  // `folders/{folder}/securityHealthAnalyticsSettings`, or
  // `projects/{project}/securityHealthAnalyticsSettings`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 2;

  // The value returned by the last call indicating a continuation
  string page_token = 3;
}

// Response message for listing descendant Security Health Analytics custom
// modules.
message ListDescendantSecurityHealthAnalyticsCustomModulesResponse {
  // Custom modules belonging to the requested parent and its descendants.
  repeated SecurityHealthAnalyticsCustomModule
      security_health_analytics_custom_modules = 1;

  // If not empty, indicates that there may be more custom modules to be
  // returned.
  string next_page_token = 2;
}

// Request message for listing the valued resources for a given simulation.
message ListValuedResourcesRequest {
  // Required. Name of parent to list valued resources.
  //
  // Valid formats:
  // `organizations/{organization}`,
  // `organizations/{organization}/simulations/{simulation}`
  // `organizations/{organization}/simulations/{simulation}/attackExposureResults/{attack_exposure_result_v2}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/ValuedResource"
    }
  ];

  // The filter expression that filters the valued resources in the response.
  // Supported fields:
  //
  //   * `resource_value` supports =
  //   * `resource_type` supports =
  string filter = 2;

  // The value returned by the last `ListValuedResourcesResponse`; indicates
  // that this is a continuation of a prior `ListValuedResources` call, and
  // that the system should return the next page of data.
  string page_token = 3;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 4;

  // Optional. The fields by which to order the valued resources response.
  //
  // Supported fields:
  //
  //   * `exposed_score`
  //
  //   * `resource_value`
  //
  //   * `resource_type`
  //
  //   * `resource`
  //
  //   * `display_name`
  //
  // Values should be a comma separated list of fields. For example:
  // `exposed_score,resource_value`.
  //
  // The default sorting order is descending. To specify ascending or descending
  // order for a field, append a ` ASC` or a ` DESC` suffix, respectively; for
  // example: `exposed_score DESC`.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for listing the valued resources for a given simulation.
message ListValuedResourcesResponse {
  // The valued resources that the attack path simulation identified.
  repeated ValuedResource valued_resources = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 2;

  // The estimated total number of results matching the query.
  int32 total_size = 3;
}

// Request message for listing the attack paths for a given simulation or valued
// resource.
message ListAttackPathsRequest {
  // Required. Name of parent to list attack paths.
  //
  // Valid formats:
  // `organizations/{organization}`,
  // `organizations/{organization}/simulations/{simulation}`
  // `organizations/{organization}/simulations/{simulation}/attackExposureResults/{attack_exposure_result_v2}`
  // `organizations/{organization}/simulations/{simulation}/valuedResources/{valued_resource}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/AttackPath"
    }
  ];

  // The filter expression that filters the attack path in the response.
  // Supported fields:
  //
  //   * `valued_resources` supports =
  string filter = 2;

  // The value returned by the last `ListAttackPathsResponse`; indicates
  // that this is a continuation of a prior `ListAttackPaths` call, and
  // that the system should return the next page of data.
  string page_token = 3;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 4;
}

// Response message for listing the attack paths for a given simulation or
// valued resource.
message ListAttackPathsResponse {
  // The attack paths that the attack path simulation identified.
  repeated AttackPath attack_paths = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 2;
}

// Request message for getting simulation.
// Simulation name can include "latest" to retrieve the latest simulation
// For example, "organizations/123/simulations/latest"
message GetSimulationRequest {
  // Required. The organization name or simulation name of this simulation
  //
  // Valid format:
  // `organizations/{organization}/simulations/latest`
  // `organizations/{organization}/simulations/{simulation}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Simulation"
    }
  ];
}

// Request message for getting a valued resource.
message GetValuedResourceRequest {
  // Required. The name of this valued resource
  //
  // Valid format:
  // `organizations/{organization}/simulations/{simulation}/valuedResources/{valued_resource}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/ValuedResource"
    }
  ];
}

// Request message for listing  mute configs at a given scope e.g. organization,
// folder or project.
message ListMuteConfigsRequest {
  // Required. The parent, which owns the collection of mute configs. Its format
  // is `organizations/[organization_id]`, `folders/[folder_id]`,
  // `projects/[project_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/MuteConfig"
    }
  ];

  // The maximum number of configs to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 configs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListMuteConfigs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListMuteConfigs` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for listing mute configs.
message ListMuteConfigsResponse {
  // The mute configs from the specified parent.
  repeated MuteConfig mute_configs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for listing notification configs.
message ListNotificationConfigsRequest {
  // Required. The name of the parent in which to list the notification
  // configurations. Its format is "organizations/[organization_id]",
  // "folders/[folder_id]", or "projects/[project_id]".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/NotificationConfig"
    }
  ];

  // The value returned by the last `ListNotificationConfigsResponse`; indicates
  // that this is a continuation of a prior `ListNotificationConfigs` call, and
  // that the system should return the next page of data.
  string page_token = 2;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 3;
}

// Response message for listing notification configs.
message ListNotificationConfigsResponse {
  // Notification configs belonging to the requested parent.
  repeated NotificationConfig notification_configs = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 2;
}

// Request message for listing effective Security Health Analytics custom
// modules.
message ListEffectiveSecurityHealthAnalyticsCustomModulesRequest {
  // Required. Name of parent to list effective custom modules. Its format is
  // `organizations/{organization}/securityHealthAnalyticsSettings`,
  // `folders/{folder}/securityHealthAnalyticsSettings`, or
  // `projects/{project}/securityHealthAnalyticsSettings`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 2;

  // The value returned by the last call indicating a continuation
  string page_token = 3;
}

// Response message for listing effective Security Health Analytics custom
// modules.
message ListEffectiveSecurityHealthAnalyticsCustomModulesResponse {
  // Effective custom modules belonging to the requested parent.
  repeated EffectiveSecurityHealthAnalyticsCustomModule
      effective_security_health_analytics_custom_modules = 1;

  // If not empty, indicates that there may be more effective custom modules to
  // be returned.
  string next_page_token = 2;
}

// Request message for listing Security Health Analytics custom modules.
message ListSecurityHealthAnalyticsCustomModulesRequest {
  // Required. Name of parent to list custom modules. Its format is
  // `organizations/{organization}/securityHealthAnalyticsSettings`,
  // `folders/{folder}/securityHealthAnalyticsSettings`, or
  // `projects/{project}/securityHealthAnalyticsSettings`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/SecurityHealthAnalyticsCustomModule"
    }
  ];

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 2;

  // The value returned by the last call indicating a continuation
  string page_token = 3;
}

// Response message for listing Security Health Analytics custom modules.
message ListSecurityHealthAnalyticsCustomModulesResponse {
  // Custom modules belonging to the requested parent.
  repeated SecurityHealthAnalyticsCustomModule
      security_health_analytics_custom_modules = 1;

  // If not empty, indicates that there may be more custom modules to be
  // returned.
  string next_page_token = 2;
}

// Request message for listing sources.
message ListSourcesRequest {
  // Required. Resource name of the parent of sources to list. Its format should
  // be `organizations/[organization_id]`, `folders/[folder_id]`, or
  // `projects/[project_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/Source"
    }
  ];

  // The value returned by the last `ListSourcesResponse`; indicates
  // that this is a continuation of a prior `ListSources` call, and
  // that the system should return the next page of data.
  string page_token = 2;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 7;
}

// Response message for listing sources.
message ListSourcesResponse {
  // Sources belonging to the requested parent.
  repeated Source sources = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 2;
}

// Request message for listing assets.
message ListAssetsRequest {
  // Required. The name of the parent resource that contains the assets. The
  // value that you can specify on parent depends on the method in which you
  // specify parent. You can specify one of the following values:
  // `organizations/[organization_id]`, `folders/[folder_id]`, or
  // `projects/[project_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/Asset"
    }
  ];

  // Expression that defines the filter to apply across assets.
  // The expression is a list of zero or more restrictions combined via logical
  // operators `AND` and `OR`.
  // Parentheses are supported, and `OR` has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a `-`
  // character in front of them to indicate negation. The fields map to those
  // defined in the Asset resource. Examples include:
  //
  // * name
  // * security_center_properties.resource_name
  // * resource_properties.a_property
  // * security_marks.marks.marka
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  //
  // The following are the allowed field and operator combinations:
  //
  // * name: `=`
  // * update_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `update_time = "2019-06-10T16:07:18-07:00"`
  //     `update_time = 1560208038000`
  //
  // * create_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `create_time = "2019-06-10T16:07:18-07:00"`
  //     `create_time = 1560208038000`
  //
  // * iam_policy.policy_blob: `=`, `:`
  // * resource_properties: `=`, `:`, `>`, `<`, `>=`, `<=`
  // * security_marks.marks: `=`, `:`
  // * security_center_properties.resource_name: `=`, `:`
  // * security_center_properties.resource_display_name: `=`, `:`
  // * security_center_properties.resource_type: `=`, `:`
  // * security_center_properties.resource_parent: `=`, `:`
  // * security_center_properties.resource_parent_display_name: `=`, `:`
  // * security_center_properties.resource_project: `=`, `:`
  // * security_center_properties.resource_project_display_name: `=`, `:`
  // * security_center_properties.resource_owners: `=`, `:`
  //
  // For example, `resource_properties.size = 100` is a valid filter string.
  //
  // Use a partial match on the empty string to filter based on a property
  // existing: `resource_properties.my_property : ""`
  //
  // Use a negated partial match on the empty string to filter based on a
  // property not existing: `-resource_properties.my_property : ""`
  string filter = 2;

  // Expression that defines what fields and order to use for sorting. The
  // string value should follow SQL syntax: comma separated list of fields. For
  // example: "name,resource_properties.a_property". The default sorting order
  // is ascending. To specify descending order for a field, a suffix " desc"
  // should be appended to the field name. For example: "name
  // desc,resource_properties.a_property". Redundant space characters in the
  // syntax are insignificant. "name desc,resource_properties.a_property" and "
  // name     desc  ,   resource_properties.a_property  " are equivalent.
  //
  // The following fields are supported:
  // name
  // update_time
  // resource_properties
  // security_marks.marks
  // security_center_properties.resource_name
  // security_center_properties.resource_display_name
  // security_center_properties.resource_parent
  // security_center_properties.resource_parent_display_name
  // security_center_properties.resource_project
  // security_center_properties.resource_project_display_name
  // security_center_properties.resource_type
  string order_by = 3;

  // Time used as a reference point when filtering assets. The filter is limited
  // to assets existing at the supplied time and their values are those at that
  // specific time. Absence of this field will default to the API's version of
  // NOW.
  google.protobuf.Timestamp read_time = 4;

  // When compare_duration is set, the ListAssetsResult's "state_change"
  // attribute is updated to indicate whether the asset was added, removed, or
  // remained present during the compare_duration period of time that precedes
  // the read_time. This is the time between (read_time - compare_duration) and
  // read_time.
  //
  // The state_change value is derived based on the presence of the asset at the
  // two points in time. Intermediate state changes between the two times don't
  // affect the result. For example, the results aren't affected if the asset is
  // removed and re-created again.
  //
  // Possible "state_change" values when compare_duration is specified:
  //
  // * "ADDED":   indicates that the asset was not present at the start of
  //                compare_duration, but present at read_time.
  // * "REMOVED": indicates that the asset was present at the start of
  //                compare_duration, but not present at read_time.
  // * "ACTIVE":  indicates that the asset was present at both the
  //                start and the end of the time period defined by
  //                compare_duration and read_time.
  //
  // If compare_duration is not specified, then the only possible state_change
  // is "UNUSED",  which will be the state_change set for all assets present at
  // read_time.
  google.protobuf.Duration compare_duration = 5;

  // A field mask to specify the ListAssetsResult fields to be listed in the
  // response.
  // An empty field mask will list all fields.
  google.protobuf.FieldMask field_mask = 7;

  // The value returned by the last `ListAssetsResponse`; indicates
  // that this is a continuation of a prior `ListAssets` call, and
  // that the system should return the next page of data.
  string page_token = 8;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 9;
}

// Response message for listing assets.
message ListAssetsResponse {
  // Result containing the Asset and its State.
  message ListAssetsResult {
    // The change in state of the asset.
    //
    // When querying across two points in time this describes
    // the change between the two points: ADDED, REMOVED, or ACTIVE.
    // If there was no compare_duration supplied in the request the state change
    // will be: UNUSED
    enum StateChange {
      // State change is unused, this is the canonical default for this enum.
      UNUSED = 0;

      // Asset was added between the points in time.
      ADDED = 1;

      // Asset was removed between the points in time.
      REMOVED = 2;

      // Asset was present at both point(s) in time.
      ACTIVE = 3;
    }

    // Asset matching the search request.
    Asset asset = 1;

    // State change of the asset between the points in time.
    StateChange state_change = 2;
  }

  // Assets matching the list request.
  repeated ListAssetsResult list_assets_results = 1;

  // Time used for executing the list request.
  google.protobuf.Timestamp read_time = 2;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 3;

  // The total number of assets matching the query.
  int32 total_size = 4;
}

// Request message for listing findings.
message ListFindingsRequest {
  // Required. Name of the source the findings belong to. Its format is
  // `organizations/[organization_id]/sources/[source_id]`,
  // `folders/[folder_id]/sources/[source_id]`, or
  // `projects/[project_id]/sources/[source_id]`. To list across all sources
  // provide a source_id of `-`. For example:
  // `organizations/{organization_id}/sources/-`,
  // `folders/{folder_id}/sources/-` or `projects/{projects_id}/sources/-`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];

  // Expression that defines the filter to apply across findings.
  // The expression is a list of one or more restrictions combined via logical
  // operators `AND` and `OR`.
  // Parentheses are supported, and `OR` has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a `-`
  // character in front of them to indicate negation. Examples include:
  //
  //  * name
  //  * source_properties.a_property
  //  * security_marks.marks.marka
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  //
  // The following field and operator combinations are supported:
  //
  // * name: `=`
  // * parent: `=`, `:`
  // * resource_name: `=`, `:`
  // * state: `=`, `:`
  // * category: `=`, `:`
  // * external_uri: `=`, `:`
  // * event_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `event_time = "2019-06-10T16:07:18-07:00"`
  //     `event_time = 1560208038000`
  //
  // * severity: `=`, `:`
  // * workflow_state: `=`, `:`
  // * security_marks.marks: `=`, `:`
  // * source_properties: `=`, `:`, `>`, `<`, `>=`, `<=`
  //
  //   For example, `source_properties.size = 100` is a valid filter string.
  //
  //   Use a partial match on the empty string to filter based on a property
  //   existing: `source_properties.my_property : ""`
  //
  //   Use a negated partial match on the empty string to filter based on a
  //   property not existing: `-source_properties.my_property : ""`
  //
  // * resource:
  //   * resource.name: `=`, `:`
  //   * resource.parent_name: `=`, `:`
  //   * resource.parent_display_name: `=`, `:`
  //   * resource.project_name: `=`, `:`
  //   * resource.project_display_name: `=`, `:`
  //   * resource.type: `=`, `:`
  //   * resource.folders.resource_folder: `=`, `:`
  //   * resource.display_name: `=`, `:`
  string filter = 2;

  // Expression that defines what fields and order to use for sorting. The
  // string value should follow SQL syntax: comma separated list of fields. For
  // example: "name,resource_properties.a_property". The default sorting order
  // is ascending. To specify descending order for a field, a suffix " desc"
  // should be appended to the field name. For example: "name
  // desc,source_properties.a_property". Redundant space characters in the
  // syntax are insignificant. "name desc,source_properties.a_property" and "
  // name     desc  ,   source_properties.a_property  " are equivalent.
  //
  // The following fields are supported:
  // name
  // parent
  // state
  // category
  // resource_name
  // event_time
  // source_properties
  // security_marks.marks
  string order_by = 3;

  // Time used as a reference point when filtering findings. The filter is
  // limited to findings existing at the supplied time and their values are
  // those at that specific time. Absence of this field will default to the
  // API's version of NOW.
  google.protobuf.Timestamp read_time = 4;

  // When compare_duration is set, the ListFindingsResult's "state_change"
  // attribute is updated to indicate whether the finding had its state changed,
  // the finding's state remained unchanged, or if the finding was added in any
  // state during the compare_duration period of time that precedes the
  // read_time. This is the time between (read_time - compare_duration) and
  // read_time.
  //
  // The state_change value is derived based on the presence and state of the
  // finding at the two points in time. Intermediate state changes between the
  // two times don't affect the result. For example, the results aren't affected
  // if the finding is made inactive and then active again.
  //
  // Possible "state_change" values when compare_duration is specified:
  //
  // * "CHANGED":   indicates that the finding was present and matched the given
  //                  filter at the start of compare_duration, but changed its
  //                  state at read_time.
  // * "UNCHANGED": indicates that the finding was present and matched the given
  //                  filter at the start of compare_duration and did not change
  //                  state at read_time.
  // * "ADDED":     indicates that the finding did not match the given filter or
  //                  was not present at the start of compare_duration, but was
  //                  present at read_time.
  // * "REMOVED":   indicates that the finding was present and matched the
  //                  filter at the start of compare_duration, but did not match
  //                  the filter at read_time.
  //
  // If compare_duration is not specified, then the only possible state_change
  // is "UNUSED", which will be the state_change set for all findings present at
  // read_time.
  google.protobuf.Duration compare_duration = 5;

  // A field mask to specify the Finding fields to be listed in the response.
  // An empty field mask will list all fields.
  google.protobuf.FieldMask field_mask = 7;

  // The value returned by the last `ListFindingsResponse`; indicates
  // that this is a continuation of a prior `ListFindings` call, and
  // that the system should return the next page of data.
  string page_token = 8;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 9;
}

// Response message for listing findings.
message ListFindingsResponse {
  // Result containing the Finding and its StateChange.
  message ListFindingsResult {
    // The change in state of the finding.
    //
    // When querying across two points in time this describes
    // the change in the finding between the two points: CHANGED, UNCHANGED,
    // ADDED, or REMOVED. Findings can not be deleted, so REMOVED implies that
    // the finding at timestamp does not match the filter specified, but it did
    // at timestamp - compare_duration. If there was no compare_duration
    // supplied in the request the state change will be: UNUSED
    enum StateChange {
      // State change is unused, this is the canonical default for this enum.
      UNUSED = 0;

      // The finding has changed state in some way between the points in time
      // and existed at both points.
      CHANGED = 1;

      // The finding has not changed state between the points in time and
      // existed at both points.
      UNCHANGED = 2;

      // The finding was created between the points in time.
      ADDED = 3;

      // The finding at timestamp does not match the filter specified, but it
      // did at timestamp - compare_duration.
      REMOVED = 4;
    }

    // Information related to the Google Cloud resource that is
    // associated with this finding.
    message Resource {
      // The full resource name of the resource. See:
      // https://cloud.google.com/apis/design/resource_names#full_resource_name
      string name = 1;

      // The human readable name of the resource.
      string display_name = 8;

      // The full resource type of the resource.
      string type = 6;

      // The full resource name of project that the resource belongs to.
      string project_name = 2;

      // The project ID that the resource belongs to.
      string project_display_name = 3;

      // The full resource name of resource's parent.
      string parent_name = 4;

      // The human readable name of resource's parent.
      string parent_display_name = 5;

      // Contains a Folder message for each folder in the assets ancestry.
      // The first folder is the deepest nested folder, and the last folder is
      // the folder directly under the Organization.
      repeated Folder folders = 7;

      // Indicates which cloud provider the finding is from.
      CloudProvider cloud_provider = 9;

      // Indicates which organization / tenant the finding is for.
      string organization = 10;

      // The service or resource provider associated with the resource.
      string service = 11;

      // The region or location of the service (if applicable).
      string location = 12;

      oneof cloud_provider_metadata {
        // The AWS metadata associated with the finding.
        AwsMetadata aws_metadata = 16;

        // The Azure metadata associated with the finding.
        AzureMetadata azure_metadata = 17;
      }

      // Provides the path to the resource within the resource hierarchy.
      ResourcePath resource_path = 18;

      // A string representation of the resource path.
      // For Google Cloud, it has the format of
      // `org/{organization_id}/folder/{folder_id}/folder/{folder_id}/project/{project_id}`
      // where there can be any number of folders.
      // For AWS, it has the format of
      // `org/{organization_id}/ou/{organizational_unit_id}/ou/{organizational_unit_id}/account/{account_id}`
      // where there can be any number of organizational units.
      // For Azure, it has the format of
      // `mg/{management_group_id}/mg/{management_group_id}/subscription/{subscription_id}/rg/{resource_group_name}`
      // where there can be any number of management groups.
      string resource_path_string = 19;
    }

    // Finding matching the search request.
    Finding finding = 1;

    // State change of the finding between the points in time.
    StateChange state_change = 2;

    // Output only. Resource that is associated with this finding.
    Resource resource = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Findings matching the list request.
  repeated ListFindingsResult list_findings_results = 1;

  // Time used for executing the list request.
  google.protobuf.Timestamp read_time = 2;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 3;

  // The total number of findings matching the query.
  int32 total_size = 4;
}

// Request message for updating a finding's state.
message SetFindingStateRequest {
  // Required. The [relative resource
  // name](https://cloud.google.com/apis/design/resource_names#relative_resource_name)
  // of the finding. Example:
  // `organizations/{organization_id}/sources/{source_id}/findings/{finding_id}`,
  // `folders/{folder_id}/sources/{source_id}/findings/{finding_id}`,
  // `projects/{project_id}/sources/{source_id}/findings/{finding_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Finding"
    }
  ];

  // Required. The desired State of the finding.
  Finding.State state = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The time at which the updated state takes effect.
  google.protobuf.Timestamp start_time = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for updating a finding's mute status.
message SetMuteRequest {
  // Required. The [relative resource
  // name](https://cloud.google.com/apis/design/resource_names#relative_resource_name)
  // of the finding. Example:
  // `organizations/{organization_id}/sources/{source_id}/findings/{finding_id}`,
  // `folders/{folder_id}/sources/{source_id}/findings/{finding_id}`,
  // `projects/{project_id}/sources/{source_id}/findings/{finding_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Finding"
    }
  ];

  // Required. The desired state of the Mute.
  Finding.Mute mute = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for running asset discovery for an organization.
message RunAssetDiscoveryRequest {
  // Required. Name of the organization to run asset discovery for. Its format
  // is `organizations/[organization_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];
}

// Request message to simulate a CustomConfig against a given test resource.
// Maximum size of the request is 4 MB by default.
message SimulateSecurityHealthAnalyticsCustomModuleRequest {
  // Manually constructed resource name. If the custom module evaluates against
  // only the resource data, you can omit the `iam_policy_data` field. If it
  // evaluates only the `iam_policy_data` field, you can omit the resource data.
  message SimulatedResource {
    // Required. The type of the resource, for example,
    // `compute.googleapis.com/Disk`.
    string resource_type = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. A representation of the Google Cloud resource. Should match the
    // Google Cloud resource JSON format.
    google.protobuf.Struct resource_data = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. A representation of the IAM policy.
    google.iam.v1.Policy iam_policy_data = 3
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The relative resource name of the organization, project, or
  // folder. For more information about relative resource names, see [Relative
  // Resource
  // Name](https://cloud.google.com/apis/design/resource_names#relative_resource_name)
  // Example: `organizations/{organization_id}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The custom configuration that you need to test.
  CustomConfig custom_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Resource data to simulate custom module against.
  SimulatedResource resource = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response message for simulating a `SecurityHealthAnalyticsCustomModule`
// against a given resource.
message SimulateSecurityHealthAnalyticsCustomModuleResponse {
  // Possible test result.
  message SimulatedResult {
    oneof result {
      // Finding that would be published for the test case,
      // if a violation is detected.
      Finding finding = 1;

      // Indicates that the test case does not trigger any violation.
      google.protobuf.Empty no_violation = 2;

      // Error encountered during the test.
      google.rpc.Status error = 3;
    }
  }

  // Result for test case in the corresponding request.
  SimulatedResult result = 1;
}

// Request message for updating a ExternalSystem resource.
message UpdateExternalSystemRequest {
  // Required. The external system resource to update.
  ExternalSystem external_system = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the external system resource.
  //
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating or creating a finding.
message UpdateFindingRequest {
  // Required. The finding resource to update or create if it does not already
  // exist. parent, security_marks, and update_time will be ignored.
  //
  // In the case of creation, the finding id portion of the name must be
  // alphanumeric and less than or equal to 32 characters and greater than 0
  // characters in length.
  Finding finding = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the finding resource. This field should
  // not be specified when creating a finding.
  //
  // When updating a finding, an empty mask is treated as updating all mutable
  // fields and replacing source_properties.  Individual source_properties can
  // be added/updated by using "source_properties.<property key>" in the field
  // mask.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a mute config.
message UpdateMuteConfigRequest {
  // Required. The mute config being updated.
  MuteConfig mute_config = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a notification config.
message UpdateNotificationConfigRequest {
  // Required. The notification config to update.
  NotificationConfig notification_config = 1
      [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the notification config.
  //
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating an organization's settings.
message UpdateOrganizationSettingsRequest {
  // Required. The organization settings resource to update.
  OrganizationSettings organization_settings = 1
      [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the settings resource.
  //
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating Security Health Analytics custom modules.
message UpdateSecurityHealthAnalyticsCustomModuleRequest {
  // Required. The SecurityHealthAnalytics custom module to update.
  SecurityHealthAnalyticsCustomModule security_health_analytics_custom_module =
      1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated. The only fields that can be updated are
  // `enablement_state` and `custom_config`. If empty or set to the wildcard
  // value `*`, both `enablement_state` and `custom_config` are updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a source.
message UpdateSourceRequest {
  // Required. The source resource to update.
  Source source = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the source resource.
  //
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a SecurityMarks resource.
message UpdateSecurityMarksRequest {
  // Required. The security marks resource to update.
  SecurityMarks security_marks = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the security marks resource.
  //
  // The field mask must not contain duplicate fields.
  // If empty or set to "marks", all marks will be replaced.  Individual
  // marks can be updated using "marks.<mark_key>".
  google.protobuf.FieldMask update_mask = 2;

  // The time at which the updated SecurityMarks take effect.
  // If not set uses current server time.  Updates will be applied to the
  // SecurityMarks that are active immediately preceding this time. Must be
  // earlier or equal to the server time.
  google.protobuf.Timestamp start_time = 3;
}

// Request message for creating a BigQuery export.
message CreateBigQueryExportRequest {
  // Required. The name of the parent resource of the new BigQuery export. Its
  // format is `organizations/[organization_id]`, `folders/[folder_id]`, or
  // `projects/[project_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/BigQueryExport"
    }
  ];

  // Required. The BigQuery export being created.
  BigQueryExport big_query_export = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Unique identifier provided by the client within the parent scope.
  // It must consist of only lowercase letters, numbers, and hyphens, must start
  // with a letter, must end with either a letter or a number, and must be 63
  // characters or less.
  string big_query_export_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for updating a BigQuery export.
message UpdateBigQueryExportRequest {
  // Required. The BigQuery export being updated.
  BigQueryExport big_query_export = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for listing BigQuery exports at a given scope e.g.
// organization, folder or project.
message ListBigQueryExportsRequest {
  // Required. The parent, which owns the collection of BigQuery exports. Its
  // format is `organizations/[organization_id]`, `folders/[folder_id]`,
  // `projects/[project_id]`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/BigQueryExport"
    }
  ];

  // The maximum number of configs to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 configs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListBigQueryExports` call.
  // Provide this to retrieve the subsequent page.
  // When paginating, all other parameters provided to `ListBigQueryExports`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for listing BigQuery exports.
message ListBigQueryExportsResponse {
  // The BigQuery exports from the specified parent.
  repeated BigQueryExport big_query_exports = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for deleting a BigQuery export.
message DeleteBigQueryExportRequest {
  // Required. The name of the BigQuery export to delete. Its format is
  // `organizations/{organization}/bigQueryExports/{export_id}`,
  // `folders/{folder}/bigQueryExports/{export_id}`, or
  // `projects/{project}/bigQueryExports/{export_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/BigQueryExport"
    }
  ];
}

// Request to create an Event Threat Detection custom module.
message CreateEventThreatDetectionCustomModuleRequest {
  // Required. The new custom module's parent.
  //
  // Its format is:
  //
  //   * `organizations/{organization}/eventThreatDetectionSettings`.
  //   * `folders/{folder}/eventThreatDetectionSettings`.
  //   * `projects/{project}/eventThreatDetectionSettings`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // Required. The module to create. The
  // event_threat_detection_custom_module.name will be ignored and server
  // generated.
  EventThreatDetectionCustomModule event_threat_detection_custom_module = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request to validate an Event Threat Detection custom module.
message ValidateEventThreatDetectionCustomModuleRequest {
  // Required. Resource name of the parent to validate the Custom Module under.
  //
  // Its format is:
  //
  //   * `organizations/{organization}/eventThreatDetectionSettings`.
  //   * `folders/{folder}/eventThreatDetectionSettings`.
  //   * `projects/{project}/eventThreatDetectionSettings`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // Required. The raw text of the module's contents. Used to generate error
  // messages.
  string raw_text = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of the module (e.g. CONFIGURABLE_BAD_IP).
  string type = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response to validating an Event Threat Detection custom module.
message ValidateEventThreatDetectionCustomModuleResponse {
  // A list of errors returned by the validator. If the list is empty, there
  // were no errors.
  CustomModuleValidationErrors errors = 2;
}

// Request to delete an Event Threat Detection custom module.
message DeleteEventThreatDetectionCustomModuleRequest {
  // Required. Name of the custom module to delete.
  //
  // Its format is:
  //
  // * `organizations/{organization}/eventThreatDetectionSettings/customModules/{module}`.
  // * `folders/{folder}/eventThreatDetectionSettings/customModules/{module}`.
  // * `projects/{project}/eventThreatDetectionSettings/customModules/{module}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];
}

// Request to get an Event Threat Detection custom module.
message GetEventThreatDetectionCustomModuleRequest {
  // Required. Name of the custom module to get.
  //
  // Its format is:
  //
  // * `organizations/{organization}/eventThreatDetectionSettings/customModules/{module}`.
  // * `folders/{folder}/eventThreatDetectionSettings/customModules/{module}`.
  // * `projects/{project}/eventThreatDetectionSettings/customModules/{module}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];
}

// Request to list current and descendant resident Event Threat Detection custom
// modules.
message ListDescendantEventThreatDetectionCustomModulesRequest {
  // Required. Name of the parent to list custom modules under.
  //
  // Its format is:
  //
  //   * `organizations/{organization}/eventThreatDetectionSettings`.
  //   * `folders/{folder}/eventThreatDetectionSettings`.
  //   * `projects/{project}/eventThreatDetectionSettings`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // A page token, received from a previous
  // `ListDescendantEventThreatDetectionCustomModules` call. Provide this to
  // retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListDescendantEventThreatDetectionCustomModules` must match the call that
  // provided the page token.
  string page_token = 2;

  // The maximum number of modules to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 configs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 3;
}

// Response for listing current and descendant resident
// Event Threat Detection custom modules.
message ListDescendantEventThreatDetectionCustomModulesResponse {
  // Custom modules belonging to the requested parent.
  repeated EventThreatDetectionCustomModule
      event_threat_detection_custom_modules = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request to list Event Threat Detection custom modules.
message ListEventThreatDetectionCustomModulesRequest {
  // Required. Name of the parent to list custom modules under.
  //
  // Its format is:
  //
  //   * `organizations/{organization}/eventThreatDetectionSettings`.
  //   * `folders/{folder}/eventThreatDetectionSettings`.
  //   * `projects/{project}/eventThreatDetectionSettings`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // A page token, received from a previous
  // `ListEventThreatDetectionCustomModules` call. Provide this to retrieve the
  // subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListEventThreatDetectionCustomModules` must match the call that provided
  // the page token.
  string page_token = 2;

  // The maximum number of modules to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 configs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 3;
}

// Response for listing Event Threat Detection custom modules.
message ListEventThreatDetectionCustomModulesResponse {
  // Custom modules belonging to the requested parent.
  repeated EventThreatDetectionCustomModule
      event_threat_detection_custom_modules = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request to update an Event Threat Detection custom module.
message UpdateEventThreatDetectionCustomModuleRequest {
  // Required. The module being updated.
  EventThreatDetectionCustomModule event_threat_detection_custom_module = 1
      [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request to get an EffectiveEventThreatDetectionCustomModule.
message GetEffectiveEventThreatDetectionCustomModuleRequest {
  // Required. The resource name of the effective Event Threat Detection custom
  // module.
  //
  // Its format is:
  //
  //   * `organizations/{organization}/eventThreatDetectionSettings/effectiveCustomModules/{module}`.
  //   * `folders/{folder}/eventThreatDetectionSettings/effectiveCustomModules/{module}`.
  //   * `projects/{project}/eventThreatDetectionSettings/effectiveCustomModules/{module}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/EffectiveEventThreatDetectionCustomModule"
    }
  ];
}

// Request to list effective Event Threat Detection custom modules.
message ListEffectiveEventThreatDetectionCustomModulesRequest {
  // Required. Name of the parent to list custom modules for.
  //
  // Its format is:
  //
  //   * `organizations/{organization}/eventThreatDetectionSettings`.
  //   * `folders/{folder}/eventThreatDetectionSettings`.
  //   * `projects/{project}/eventThreatDetectionSettings`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/EffectiveEventThreatDetectionCustomModule"
    }
  ];

  // A page token, received from a previous
  // `ListEffectiveEventThreatDetectionCustomModules` call. Provide this to
  // retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListEffectiveEventThreatDetectionCustomModules` must match the call that
  // provided the page token.
  string page_token = 2;

  // The maximum number of modules to return. The service may return fewer than
  // this value.
  // If unspecified, at most 10 configs will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 3;
}

// Response for listing EffectiveEventThreatDetectionCustomModules.
message ListEffectiveEventThreatDetectionCustomModulesResponse {
  // Effective custom modules belonging to the requested parent.
  repeated EffectiveEventThreatDetectionCustomModule
      effective_event_threat_detection_custom_modules = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
