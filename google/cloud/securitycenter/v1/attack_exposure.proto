// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "AttackExposureProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// An attack exposure contains the results of an attack path simulation run.
message AttackExposure {
  // This enum defines the various states an AttackExposure can be in.
  enum State {
    // The state is not specified.
    STATE_UNSPECIFIED = 0;

    // The attack exposure has been calculated.
    CALCULATED = 1;

    // The attack exposure has not been calculated.
    NOT_CALCULATED = 2;
  }

  // A number between 0 (inclusive) and infinity that represents how important
  // this finding is to remediate. The higher the score, the more important it
  // is to remediate.
  double score = 1;

  // The most recent time the attack exposure was updated on this finding.
  google.protobuf.Timestamp latest_calculation_time = 2;

  // The resource name of the attack path simulation result that contains the
  // details regarding this attack exposure score.
  // Example: `organizations/123/simulations/456/attackExposureResults/789`
  string attack_exposure_result = 3;

  // What state this AttackExposure is in. This captures whether or not an
  // attack exposure has been calculated or not.
  State state = 4;

  // The number of high value resources that are exposed as a result of this
  // finding.
  int32 exposed_high_value_resources_count = 5;

  // The number of medium value resources that are exposed as a result of this
  // finding.
  int32 exposed_medium_value_resources_count = 6;

  // The number of high value resources that are exposed as a result of this
  // finding.
  int32 exposed_low_value_resources_count = 7;
}
