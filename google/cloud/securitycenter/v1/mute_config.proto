// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "MuteConfigProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// A mute config is a Cloud SCC resource that contains the configuration
// to mute create/update events of findings.
message MuteConfig {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/MuteConfig"
    pattern: "organizations/{organization}/muteConfigs/{mute_config}"
    pattern: "folders/{folder}/muteConfigs/{mute_config}"
    pattern: "projects/{project}/muteConfigs/{mute_config}"
    pattern: "organizations/{organization}/locations/{location}/muteConfigs/{mute_config}"
    pattern: "folders/{folder}/locations/{location}/muteConfigs/{mute_config}"
    pattern: "projects/{project}/locations/{location}/muteConfigs/{mute_config}"
    plural: "muteConfigs"
    singular: "muteConfig"
  };

  // The type of MuteConfig.
  enum MuteConfigType {
    // Unused.
    MUTE_CONFIG_TYPE_UNSPECIFIED = 0;

    // A static mute config, which sets the static mute state of future matching
    // findings to muted. Once the static mute state has been set, finding or
    // config modifications will not affect the state.
    STATIC = 1;

    // A dynamic mute config, which is applied to existing and future matching
    // findings, setting their dynamic mute state to "muted". If the config is
    // updated or deleted, or a matching finding is updated, such that the
    // finding doesn't match the config, the config will be removed from the
    // finding, and the finding's dynamic mute state may become "unmuted"
    // (unless other configs still match).
    DYNAMIC = 2;
  }

  // This field will be ignored if provided on config creation. Format
  // `organizations/{organization}/muteConfigs/{mute_config}`
  // `folders/{folder}/muteConfigs/{mute_config}`
  // `projects/{project}/muteConfigs/{mute_config}`
  // `organizations/{organization}/locations/global/muteConfigs/{mute_config}`
  // `folders/{folder}/locations/global/muteConfigs/{mute_config}`
  // `projects/{project}/locations/global/muteConfigs/{mute_config}`
  string name = 1;

  // The human readable name to be displayed for the mute config.
  string display_name = 2 [deprecated = true];

  // A description of the mute config.
  string description = 3;

  // Required. An expression that defines the filter to apply across
  // create/update events of findings. While creating a filter string, be
  // mindful of the scope in which the mute configuration is being created.
  // E.g., If a filter contains project = X but is created under the project = Y
  // scope, it might not match any findings.
  //
  // The following field and operator combinations are supported:
  //
  // * severity: `=`, `:`
  // * category: `=`, `:`
  // * resource.name: `=`, `:`
  // * resource.project_name: `=`, `:`
  // * resource.project_display_name: `=`, `:`
  // * resource.folders.resource_folder: `=`, `:`
  // * resource.parent_name: `=`, `:`
  // * resource.parent_display_name: `=`, `:`
  // * resource.type: `=`, `:`
  // * finding_class: `=`, `:`
  // * indicator.ip_addresses: `=`, `:`
  // * indicator.domains: `=`, `:`
  string filter = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. The time at which the mute config was created.
  // This field is set by the server and will be ignored if provided on config
  // creation.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The most recent time at which the mute config was updated.
  // This field is set by the server and will be ignored if provided on config
  // creation or update.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Email address of the user who last edited the mute config.
  // This field is set by the server and will be ignored if provided on config
  // creation or update.
  string most_recent_editor = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The type of the mute config, which determines what type of mute
  // state the config affects. The static mute state takes precedence over the
  // dynamic mute state. Immutable after creation. STATIC by default if not set
  // during creation.
  MuteConfigType type = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The expiry of the mute config. Only applicable for dynamic
  // configs. If the expiry is set, when the config expires, it is removed from
  // all findings.
  google.protobuf.Timestamp expiry_time = 9
      [(google.api.field_behavior) = OPTIONAL];
}
