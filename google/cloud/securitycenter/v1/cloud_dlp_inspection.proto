// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "CloudDlpInspectionProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";
option (google.api.resource_definition) = {
  type: "dlp.googleapis.com/DlpJob"
  pattern: "projects/{project}/dlpJobs/{dlp_job}"
  pattern: "projects/{project}/locations/{location}/dlpJobs/{dlp_job}"
};

// Details about the Cloud Data Loss Prevention (Cloud DLP) [inspection
// job](https://cloud.google.com/dlp/docs/concepts-job-triggers) that produced
// the finding.
message CloudDlpInspection {
  // Name of the inspection job, for example,
  // `projects/123/locations/europe/dlpJobs/i-8383929`.
  string inspect_job = 1
      [(google.api.resource_reference) = { type: "dlp.googleapis.com/DlpJob" }];

  // The type of information (or
  // *[infoType](https://cloud.google.com/dlp/docs/infotypes-reference)*) found,
  // for example, `EMAIL_ADDRESS` or `STREET_ADDRESS`.
  string info_type = 2;

  // The number of times Cloud DLP found this infoType within this job
  // and resource.
  int64 info_type_count = 3;

  // Whether Cloud DLP scanned the complete resource or a sampled subset.
  bool full_scan = 4;
}
