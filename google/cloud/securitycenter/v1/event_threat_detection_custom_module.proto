// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "EventThreatDetectionCustomModuleProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Represents an instance of an Event Threat Detection custom module,
// including its full module name, display name, enablement state, and last
// updated time. You can create a custom module at the organization, folder, or
// project level. Custom modules that you create at the organization or folder
// level are inherited by child folders and projects.
message EventThreatDetectionCustomModule {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/EventThreatDetectionCustomModule"
    pattern: "organizations/{organization}/eventThreatDetectionSettings/customModules/{module}"
    pattern: "folders/{folder}/eventThreatDetectionSettings/customModules/{module}"
    pattern: "projects/{project}/eventThreatDetectionSettings/customModules/{module}"
  };

  // The enablement state of the module.
  enum EnablementState {
    // Unspecified enablement state.
    ENABLEMENT_STATE_UNSPECIFIED = 0;

    // The module is enabled at the given level.
    ENABLED = 1;

    // The module is disabled at the given level.
    DISABLED = 2;

    // When the enablement state is inherited.
    INHERITED = 3;
  }

  // Immutable. The resource name of the Event Threat Detection custom module.
  //
  // Its format is:
  //
  //   * `organizations/{organization}/eventThreatDetectionSettings/customModules/{module}`.
  //   * `folders/{folder}/eventThreatDetectionSettings/customModules/{module}`.
  //   * `projects/{project}/eventThreatDetectionSettings/customModules/{module}`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Config for the module. For the resident module, its config value is defined
  // at this level. For the inherited module, its config value is inherited from
  // the ancestor module.
  google.protobuf.Struct config = 2;

  // Output only. The closest ancestor module that this module inherits the
  // enablement state from. The format is the same as the
  // EventThreatDetectionCustomModule resource name.
  string ancestor_module = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/EventThreatDetectionCustomModule"
    }
  ];

  // The state of enablement for the module at the given level of the hierarchy.
  EnablementState enablement_state = 4;

  // Type for the module. e.g. CONFIGURABLE_BAD_IP.
  string type = 5;

  // The human readable name to be displayed for the module.
  string display_name = 6;

  // The description for the module.
  string description = 7;

  // Output only. The time the module was last updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The editor the module was last updated by.
  string last_editor = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}
