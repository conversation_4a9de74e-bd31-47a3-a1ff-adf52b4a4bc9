// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "CloudArmorProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Fields related to Google Cloud Armor findings.
message CloudArmor {
  // Information about the [Google Cloud Armor security
  // policy](https://cloud.google.com/armor/docs/security-policy-overview)
  // relevant to the finding.
  SecurityPolicy security_policy = 1;

  // Information about incoming requests evaluated by [Google Cloud Armor
  // security
  // policies](https://cloud.google.com/armor/docs/security-policy-overview).
  Requests requests = 2;

  // Information about potential Layer 7 DDoS attacks identified by [Google
  // Cloud Armor Adaptive
  // Protection](https://cloud.google.com/armor/docs/adaptive-protection-overview).
  AdaptiveProtection adaptive_protection = 3;

  // Information about DDoS attack volume and classification.
  Attack attack = 4;

  // Distinguish between volumetric & protocol DDoS attack and
  // application layer attacks. For example, "L3_4" for Layer 3 and Layer 4 DDoS
  // attacks, or "L_7" for Layer 7 DDoS attacks.
  string threat_vector = 5;

  // Duration of attack from the start until the current moment (updated every 5
  // minutes).
  google.protobuf.Duration duration = 6;
}

// Information about the [Google Cloud Armor security
// policy](https://cloud.google.com/armor/docs/security-policy-overview)
// relevant to the finding.
message SecurityPolicy {
  // The name of the Google Cloud Armor security policy, for example,
  // "my-security-policy".
  string name = 1;

  // The type of Google Cloud Armor security policy for example, 'backend
  // security policy', 'edge security policy', 'network edge security policy',
  // or 'always-on DDoS protection'.
  string type = 2;

  // Whether or not the associated rule or policy is in preview mode.
  bool preview = 3;
}

// Information about the requests relevant to the finding.
message Requests {
  // For 'Increasing deny ratio', the ratio is the denied traffic divided by the
  // allowed traffic. For 'Allowed traffic spike', the ratio is the allowed
  // traffic in the short term divided by allowed traffic in the long term.
  double ratio = 1;

  // Allowed RPS (requests per second) in the short term.
  int32 short_term_allowed = 2;

  // Allowed RPS (requests per second) over the long term.
  int32 long_term_allowed = 3;

  // Denied RPS (requests per second) over the long term.
  int32 long_term_denied = 4;
}

// Information about [Google Cloud Armor Adaptive
// Protection](https://cloud.google.com/armor/docs/cloud-armor-overview#google-cloud-armor-adaptive-protection).
message AdaptiveProtection {
  // A score of 0 means that there is low confidence that the detected event is
  // an actual attack. A score of 1 means that there is high confidence that the
  // detected event is an attack. See the [Adaptive Protection
  // documentation](https://cloud.google.com/armor/docs/adaptive-protection-overview#configure-alert-tuning)
  // for further explanation.
  double confidence = 1;
}

// Information about DDoS attack volume and classification.
message Attack {
  // Total PPS (packets per second) volume of attack.
  int32 volume_pps = 1;

  // Total BPS (bytes per second) volume of attack.
  int32 volume_bps = 2;

  // Type of attack, for example, 'SYN-flood', 'NTP-udp', or 'CHARGEN-udp'.
  string classification = 3;
}
