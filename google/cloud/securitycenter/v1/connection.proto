// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "ConnectionProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// Contains information about the IP connection associated with the finding.
message Connection {
  // IANA Internet Protocol Number such as TCP(6) and UDP(17).
  enum Protocol {
    // Unspecified protocol (not HOPOPT).
    PROTOCOL_UNSPECIFIED = 0;

    // Internet Control Message Protocol.
    ICMP = 1;

    // Transmission Control Protocol.
    TCP = 6;

    // User Datagram Protocol.
    UDP = 17;

    // Generic Routing Encapsulation.
    GRE = 47;

    // Encap Security Payload.
    ESP = 50;
  }

  // Destination IP address. Not present for sockets that are listening and not
  // connected.
  string destination_ip = 1;

  // Destination port. Not present for sockets that are listening and not
  // connected.
  int32 destination_port = 2;

  // Source IP address.
  string source_ip = 3;

  // Source port.
  int32 source_port = 4;

  // IANA Internet Protocol Number such as TCP(6) and UDP(17).
  Protocol protocol = 5;
}
