// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1";
option go_package = "cloud.google.com/go/securitycenter/apiv1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "AttackPathProto";
option java_package = "com.google.cloud.securitycenter.v1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1";
option ruby_package = "Google::Cloud::SecurityCenter::V1";

// A path that an attacker could take to reach an exposed resource.
message AttackPath {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/AttackPath"
    pattern: "organizations/{organization}/simulations/{simulation}/valuedResources/{valued_resource}/attackPaths/{attack_path}"
    plural: "attackPaths"
    singular: "attackPath"
  };

  // Represents one point that an attacker passes through in this attack path.
  message AttackPathNode {
    // A finding that is associated with this node in the attack path.
    message PathNodeAssociatedFinding {
      // Canonical name of the associated findings. Example:
      // `organizations/123/sources/456/findings/789`
      string canonical_finding = 1;

      // The additional taxonomy group within findings from a given source.
      string finding_category = 2;

      // Full resource name of the finding.
      string name = 3;
    }

    // The type of the incoming attack step node.
    enum NodeType {
      // Type not specified
      NODE_TYPE_UNSPECIFIED = 0;

      // Incoming edge joined with AND
      NODE_TYPE_AND = 1;

      // Incoming edge joined with OR
      NODE_TYPE_OR = 2;

      // Incoming edge is defense
      NODE_TYPE_DEFENSE = 3;

      // Incoming edge is attacker
      NODE_TYPE_ATTACKER = 4;
    }

    // Detailed steps the attack can take between path nodes.
    message AttackStepNode {
      // Unique ID for one Node
      string uuid = 1;

      // Attack step type. Can be either AND, OR or DEFENSE
      NodeType type = 2;

      // User friendly name of the attack step
      string display_name = 3;

      // Attack step labels for metadata
      map<string, string> labels = 4;

      // Attack step description
      string description = 5;
    }

    // The name of the resource at this point in the attack path.
    // The format of the name follows the Cloud Asset Inventory [resource
    // name
    // format](https://cloud.google.com/asset-inventory/docs/resource-name-format)
    string resource = 1;

    // The [supported resource
    // type](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
    string resource_type = 2;

    // Human-readable name of this resource.
    string display_name = 3;

    // The findings associated with this node in the attack path.
    repeated PathNodeAssociatedFinding associated_findings = 4;

    // Unique id of the attack path node.
    string uuid = 5;

    // A list of attack step nodes that exist in this attack path node.
    repeated AttackStepNode attack_steps = 6;
  }

  // Represents a connection between a source node and a destination node in
  // this attack path.
  message AttackPathEdge {
    // The attack node uuid of the source node.
    string source = 1;

    // The attack node uuid of the destination node.
    string destination = 2;
  }

  // The attack path name, for example,
  //  `organizations/12/simulation/34/valuedResources/56/attackPaths/78`
  string name = 1;

  // A list of nodes that exist in this attack path.
  repeated AttackPathNode path_nodes = 2;

  // A list of the edges between nodes in this attack path.
  repeated AttackPathEdge edges = 3;
}
