type: google.api.Service
config_version: 3
name: securitycenter.googleapis.com
title: Security Command Center API

apis:
- name: google.cloud.securitycenter.v1p1beta1.SecurityCenter

types:
- name: google.cloud.securitycenter.v1p1beta1.NotificationMessage
- name: google.cloud.securitycenter.v1p1beta1.Resource
- name: google.cloud.securitycenter.v1p1beta1.RunAssetDiscoveryResponse

documentation:
  summary: |-
    Security Command Center API provides access to temporal views of assets and
    findings within an organization.

backend:
  rules:
  - selector: 'google.cloud.securitycenter.v1p1beta1.SecurityCenter.*'
    deadline: 480.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1p1beta1/{name=organizations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1p1beta1/{name=organizations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1p1beta1/{name=organizations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1p1beta1/{name=organizations/*/operations}'

authentication:
  rules:
  - selector: 'google.cloud.securitycenter.v1p1beta1.SecurityCenter.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
