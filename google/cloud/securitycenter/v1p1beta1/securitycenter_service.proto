// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1p1beta1;

import public "google/cloud/securitycenter/v1p1beta1/notification_message.proto";
import public "google/cloud/securitycenter/v1p1beta1/run_asset_discovery_response.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securitycenter/v1p1beta1/asset.proto";
import "google/cloud/securitycenter/v1p1beta1/finding.proto";
import "google/cloud/securitycenter/v1p1beta1/folder.proto";
import "google/cloud/securitycenter/v1p1beta1/notification_config.proto";
import "google/cloud/securitycenter/v1p1beta1/organization_settings.proto";
import "google/cloud/securitycenter/v1p1beta1/security_marks.proto";
import "google/cloud/securitycenter/v1p1beta1/source.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1P1Beta1";
option go_package = "cloud.google.com/go/securitycenter/apiv1p1beta1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.securitycenter.v1p1beta1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1p1beta1";
option ruby_package = "Google::Cloud::SecurityCenter::V1p1beta1";

// V1p1Beta1 APIs for Security Center service.
service SecurityCenter {
  option (google.api.default_host) = "securitycenter.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Creates a source.
  rpc CreateSource(CreateSourceRequest) returns (Source) {
    option (google.api.http) = {
      post: "/v1p1beta1/{parent=organizations/*}/sources"
      body: "source"
    };
    option (google.api.method_signature) = "parent,source";
  }

  // Creates a finding. The corresponding source must exist for finding
  // creation to succeed.
  rpc CreateFinding(CreateFindingRequest) returns (Finding) {
    option (google.api.http) = {
      post: "/v1p1beta1/{parent=organizations/*/sources/*}/findings"
      body: "finding"
    };
    option (google.api.method_signature) = "parent,finding_id,finding";
    option (google.api.method_signature) = "parent,finding,finding_id";
  }

  // Creates a notification config.
  rpc CreateNotificationConfig(CreateNotificationConfigRequest) returns (NotificationConfig) {
    option (google.api.http) = {
      post: "/v1p1beta1/{parent=organizations/*}/notificationConfigs"
      body: "notification_config"
    };
    option (google.api.method_signature) = "parent,config_id,notification_config";
    option (google.api.method_signature) = "parent,notification_config";
  }

  // Deletes a notification config.
  rpc DeleteNotificationConfig(DeleteNotificationConfigRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1p1beta1/{name=organizations/*/notificationConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the access control policy on the specified Source.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1p1beta1/{resource=organizations/*/sources/*}:getIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Gets a notification config.
  rpc GetNotificationConfig(GetNotificationConfigRequest) returns (NotificationConfig) {
    option (google.api.http) = {
      get: "/v1p1beta1/{name=organizations/*/notificationConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the settings for an organization.
  rpc GetOrganizationSettings(GetOrganizationSettingsRequest) returns (OrganizationSettings) {
    option (google.api.http) = {
      get: "/v1p1beta1/{name=organizations/*/organizationSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a source.
  rpc GetSource(GetSourceRequest) returns (Source) {
    option (google.api.http) = {
      get: "/v1p1beta1/{name=organizations/*/sources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Filters an organization's assets and  groups them by their specified
  // properties.
  rpc GroupAssets(GroupAssetsRequest) returns (GroupAssetsResponse) {
    option (google.api.http) = {
      post: "/v1p1beta1/{parent=organizations/*}/assets:group"
      body: "*"
      additional_bindings {
        post: "/v1p1beta1/{parent=folders/*}/assets:group"
        body: "*"
      }
      additional_bindings {
        post: "/v1p1beta1/{parent=projects/*}/assets:group"
        body: "*"
      }
    };
  }

  // Filters an organization or source's findings and  groups them by their
  // specified properties.
  //
  // To group across all sources provide a `-` as the source id.
  // Example: /v1/organizations/{organization_id}/sources/-/findings,
  // /v1/folders/{folder_id}/sources/-/findings,
  // /v1/projects/{project_id}/sources/-/findings
  rpc GroupFindings(GroupFindingsRequest) returns (GroupFindingsResponse) {
    option (google.api.http) = {
      post: "/v1p1beta1/{parent=organizations/*/sources/*}/findings:group"
      body: "*"
      additional_bindings {
        post: "/v1p1beta1/{parent=folders/*/sources/*}/findings:group"
        body: "*"
      }
      additional_bindings {
        post: "/v1p1beta1/{parent=projects/*/sources/*}/findings:group"
        body: "*"
      }
    };
    option (google.api.method_signature) = "parent,group_by";
  }

  // Lists an organization's assets.
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse) {
    option (google.api.http) = {
      get: "/v1p1beta1/{parent=organizations/*}/assets"
      additional_bindings {
        get: "/v1p1beta1/{parent=folders/*}/assets"
      }
      additional_bindings {
        get: "/v1p1beta1/{parent=projects/*}/assets"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists an organization or source's findings.
  //
  // To list across all sources provide a `-` as the source id.
  // Example: /v1p1beta1/organizations/{organization_id}/sources/-/findings
  rpc ListFindings(ListFindingsRequest) returns (ListFindingsResponse) {
    option (google.api.http) = {
      get: "/v1p1beta1/{parent=organizations/*/sources/*}/findings"
      additional_bindings {
        get: "/v1p1beta1/{parent=folders/*/sources/*}/findings"
      }
      additional_bindings {
        get: "/v1p1beta1/{parent=projects/*/sources/*}/findings"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists notification configs.
  rpc ListNotificationConfigs(ListNotificationConfigsRequest) returns (ListNotificationConfigsResponse) {
    option (google.api.http) = {
      get: "/v1p1beta1/{parent=organizations/*}/notificationConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists all sources belonging to an organization.
  rpc ListSources(ListSourcesRequest) returns (ListSourcesResponse) {
    option (google.api.http) = {
      get: "/v1p1beta1/{parent=organizations/*}/sources"
      additional_bindings {
        get: "/v1p1beta1/{parent=folders/*}/sources"
      }
      additional_bindings {
        get: "/v1p1beta1/{parent=projects/*}/sources"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Runs asset discovery. The discovery is tracked with a long-running
  // operation.
  //
  // This API can only be called with limited frequency for an organization. If
  // it is called too frequently the caller will receive a TOO_MANY_REQUESTS
  // error.
  rpc RunAssetDiscovery(RunAssetDiscoveryRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1p1beta1/{parent=organizations/*}/assets:runDiscovery"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.securitycenter.v1p1beta1.RunAssetDiscoveryResponse"
      metadata_type: "google.protobuf.Empty"
    };
  }

  // Updates the state of a finding.
  rpc SetFindingState(SetFindingStateRequest) returns (Finding) {
    option (google.api.http) = {
      post: "/v1p1beta1/{name=organizations/*/sources/*/findings/*}:setState"
      body: "*"
      additional_bindings {
        post: "/v1p1beta1/{name=folders/*/sources/*/findings/*}:setState"
        body: "*"
      }
      additional_bindings {
        post: "/v1p1beta1/{name=projects/*/sources/*/findings/*}:setState"
        body: "*"
      }
    };
    option (google.api.method_signature) = "name,state,start_time";
  }

  // Sets the access control policy on the specified Source.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest) returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v1p1beta1/{resource=organizations/*/sources/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Returns the permissions that a caller has on the specified source.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest) returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v1p1beta1/{resource=organizations/*/sources/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource,permissions";
  }

  // Creates or updates a finding. The corresponding source must exist for a
  // finding creation to succeed.
  rpc UpdateFinding(UpdateFindingRequest) returns (Finding) {
    option (google.api.http) = {
      patch: "/v1p1beta1/{finding.name=organizations/*/sources/*/findings/*}"
      body: "finding"
      additional_bindings {
        patch: "/v1p1beta1/{finding.name=folders/*/sources/*/findings/*}"
        body: "finding"
      }
      additional_bindings {
        patch: "/v1p1beta1/{finding.name=projects/*/sources/*/findings/*}"
        body: "finding"
      }
    };
    option (google.api.method_signature) = "finding";
    option (google.api.method_signature) = "finding,update_mask";
  }

  // Updates a notification config. The following update
  // fields are allowed: description, pubsub_topic, streaming_config.filter
  rpc UpdateNotificationConfig(UpdateNotificationConfigRequest) returns (NotificationConfig) {
    option (google.api.http) = {
      patch: "/v1p1beta1/{notification_config.name=organizations/*/notificationConfigs/*}"
      body: "notification_config"
    };
    option (google.api.method_signature) = "notification_config";
    option (google.api.method_signature) = "notification_config,update_mask";
  }

  // Updates an organization's settings.
  rpc UpdateOrganizationSettings(UpdateOrganizationSettingsRequest) returns (OrganizationSettings) {
    option (google.api.http) = {
      patch: "/v1p1beta1/{organization_settings.name=organizations/*/organizationSettings}"
      body: "organization_settings"
    };
    option (google.api.method_signature) = "organization_settings";
  }

  // Updates a source.
  rpc UpdateSource(UpdateSourceRequest) returns (Source) {
    option (google.api.http) = {
      patch: "/v1p1beta1/{source.name=organizations/*/sources/*}"
      body: "source"
    };
    option (google.api.method_signature) = "source";
    option (google.api.method_signature) = "source,update_mask";
  }

  // Updates security marks.
  rpc UpdateSecurityMarks(UpdateSecurityMarksRequest) returns (SecurityMarks) {
    option (google.api.http) = {
      patch: "/v1p1beta1/{security_marks.name=organizations/*/assets/*/securityMarks}"
      body: "security_marks"
      additional_bindings {
        patch: "/v1p1beta1/{security_marks.name=folders/*/assets/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v1p1beta1/{security_marks.name=projects/*/assets/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v1p1beta1/{security_marks.name=organizations/*/sources/*/findings/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v1p1beta1/{security_marks.name=folders/*/sources/*/findings/*/securityMarks}"
        body: "security_marks"
      }
      additional_bindings {
        patch: "/v1p1beta1/{security_marks.name=projects/*/sources/*/findings/*/securityMarks}"
        body: "security_marks"
      }
    };
    option (google.api.method_signature) = "security_marks";
    option (google.api.method_signature) = "security_marks,update_mask";
  }
}

// Request message for creating a finding.
message CreateFindingRequest {
  // Required. Resource name of the new finding's parent. Its format should be
  // "organizations/[organization_id]/sources/[source_id]".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];

  // Required. Unique identifier provided by the client within the parent scope.
  string finding_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Finding being created. The name and security_marks will be ignored as
  // they are both output only fields on this resource.
  Finding finding = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for creating a notification config.
message CreateNotificationConfigRequest {
  // Required. Resource name of the new notification config's parent. Its format is
  // "organizations/[organization_id]".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // Required. Unique identifier provided by the client within the parent scope.
  // It must be between 1 and 128 characters, and contains alphanumeric
  // characters, underscores or hyphens only.
  string config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The notification config being created. The name and the service account
  // will be ignored as they are both output only fields on this resource.
  NotificationConfig notification_config = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for creating a source.
message CreateSourceRequest {
  // Required. Resource name of the new source's parent. Its format should be
  // "organizations/[organization_id]".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // Required. The Source being created, only the display_name and description will be
  // used. All other fields will be ignored.
  Source source = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for deleting a notification config.
message DeleteNotificationConfigRequest {
  // Required. Name of the notification config to delete. Its format is
  // "organizations/[organization_id]/notificationConfigs/[config_id]".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/NotificationConfig"
    }
  ];
}

// Request message for getting a notification config.
message GetNotificationConfigRequest {
  // Required. Name of the notification config to get. Its format is
  // "organizations/[organization_id]/notificationConfigs/[config_id]".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/NotificationConfig"
    }
  ];
}

// Request message for getting organization settings.
message GetOrganizationSettingsRequest {
  // Required. Name of the organization to get organization settings for. Its format is
  // "organizations/[organization_id]/organizationSettings".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/OrganizationSettings"
    }
  ];
}

// Request message for getting a source.
message GetSourceRequest {
  // Required. Relative resource name of the source. Its format is
  // "organizations/[organization_id]/source/[source_id]".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];
}

// Request message for grouping by assets.
message GroupAssetsRequest {
  // Required. Name of the organization to groupBy. Its format is
  // "organizations/[organization_id], folders/[folder_id], or
  // projects/[project_id]".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/Asset"
    }
  ];

  // Expression that defines the filter to apply across assets.
  // The expression is a list of zero or more restrictions combined via logical
  // operators `AND` and `OR`.
  // Parentheses are supported, and `OR` has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a `-`
  // character in front of them to indicate negation. The fields map to those
  // defined in the Asset resource. Examples include:
  //
  // * name
  // * security_center_properties.resource_name
  // * resource_properties.a_property
  // * security_marks.marks.marka
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  //
  // The following field and operator combinations are supported:
  //
  // * name: `=`
  // * update_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `update_time = "2019-06-10T16:07:18-07:00"`
  //     `update_time = 1560208038000`
  //
  // * create_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `create_time = "2019-06-10T16:07:18-07:00"`
  //     `create_time = 1560208038000`
  //
  // * iam_policy.policy_blob: `=`, `:`
  // * resource_properties: `=`, `:`, `>`, `<`, `>=`, `<=`
  // * security_marks.marks: `=`, `:`
  // * security_center_properties.resource_name: `=`, `:`
  // * security_center_properties.resource_name_display_name: `=`, `:`
  // * security_center_properties.resource_type: `=`, `:`
  // * security_center_properties.resource_parent: `=`, `:`
  // * security_center_properties.resource_parent_display_name: `=`, `:`
  // * security_center_properties.resource_project: `=`, `:`
  // * security_center_properties.resource_project_display_name: `=`, `:`
  // * security_center_properties.resource_owners: `=`, `:`
  //
  // For example, `resource_properties.size = 100` is a valid filter string.
  //
  // Use a partial match on the empty string to filter based on a property
  // existing: `resource_properties.my_property : ""`
  //
  // Use a negated partial match on the empty string to filter based on a
  // property not existing: `-resource_properties.my_property : ""`
  string filter = 2;

  // Required. Expression that defines what assets fields to use for grouping. The string
  // value should follow SQL syntax: comma separated list of fields. For
  // example:
  // "security_center_properties.resource_project,security_center_properties.project".
  //
  // The following fields are supported when compare_duration is not set:
  //
  // * security_center_properties.resource_project
  // * security_center_properties.resource_project_display_name
  // * security_center_properties.resource_type
  // * security_center_properties.resource_parent
  // * security_center_properties.resource_parent_display_name
  //
  // The following fields are supported when compare_duration is set:
  //
  // * security_center_properties.resource_type
  // * security_center_properties.resource_project_display_name
  // * security_center_properties.resource_parent_display_name
  string group_by = 3 [(google.api.field_behavior) = REQUIRED];

  // When compare_duration is set, the GroupResult's "state_change" property is
  // updated to indicate whether the asset was added, removed, or remained
  // present during the compare_duration period of time that precedes the
  // read_time. This is the time between (read_time - compare_duration) and
  // read_time.
  //
  // The state change value is derived based on the presence of the asset at the
  // two points in time. Intermediate state changes between the two times don't
  // affect the result. For example, the results aren't affected if the asset is
  // removed and re-created again.
  //
  // Possible "state_change" values when compare_duration is specified:
  //
  // * "ADDED":   indicates that the asset was not present at the start of
  //                compare_duration, but present at reference_time.
  // * "REMOVED": indicates that the asset was present at the start of
  //                compare_duration, but not present at reference_time.
  // * "ACTIVE":  indicates that the asset was present at both the
  //                start and the end of the time period defined by
  //                compare_duration and reference_time.
  //
  // If compare_duration is not specified, then the only possible state_change
  // is "UNUSED", which will be the state_change set for all assets present at
  // read_time.
  //
  // If this field is set then `state_change` must be a specified field in
  // `group_by`.
  google.protobuf.Duration compare_duration = 4;

  // Time used as a reference point when filtering assets. The filter is limited
  // to assets existing at the supplied time and their values are those at that
  // specific time. Absence of this field will default to the API's version of
  // NOW.
  google.protobuf.Timestamp read_time = 5;

  // The value returned by the last `GroupAssetsResponse`; indicates
  // that this is a continuation of a prior `GroupAssets` call, and that the
  // system should return the next page of data.
  string page_token = 7;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 8;
}

// Response message for grouping by assets.
message GroupAssetsResponse {
  // Group results. There exists an element for each existing unique
  // combination of property/values. The element contains a count for the number
  // of times those specific property/values appear.
  repeated GroupResult group_by_results = 1;

  // Time used for executing the groupBy request.
  google.protobuf.Timestamp read_time = 2;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 3;

  // The total number of results matching the query.
  int32 total_size = 4;
}

// Request message for grouping by findings.
message GroupFindingsRequest {
  // Required. Name of the source to groupBy. Its format is
  // "organizations/[organization_id]/sources/[source_id]",
  // folders/[folder_id]/sources/[source_id], or
  // projects/[project_id]/sources/[source_id]. To groupBy across all sources
  // provide a source_id of `-`. For example:
  // organizations/{organization_id}/sources/-, folders/{folder_id}/sources/-,
  // or projects/{project_id}/sources/-
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];

  // Expression that defines the filter to apply across findings.
  // The expression is a list of one or more restrictions combined via logical
  // operators `AND` and `OR`.
  // Parentheses are supported, and `OR` has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a `-`
  // character in front of them to indicate negation. Examples include:
  //
  //  * name
  //  * source_properties.a_property
  //  * security_marks.marks.marka
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  //
  // The following field and operator combinations are supported:
  //
  // * name: `=`
  // * parent: `=`, `:`
  // * resource_name: `=`, `:`
  // * state: `=`, `:`
  // * category: `=`, `:`
  // * external_uri: `=`, `:`
  // * event_time: `=`, `>`, `<`, `>=`, `<=`
  // * severity: `=`, `:`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `event_time = "2019-06-10T16:07:18-07:00"`
  //     `event_time = 1560208038000`
  //
  // * security_marks.marks: `=`, `:`
  // * source_properties: `=`, `:`, `>`, `<`, `>=`, `<=`
  //
  // For example, `source_properties.size = 100` is a valid filter string.
  //
  // Use a partial match on the empty string to filter based on a property
  // existing: `source_properties.my_property : ""`
  //
  // Use a negated partial match on the empty string to filter based on a
  // property not existing: `-source_properties.my_property : ""`
  string filter = 2;

  // Required. Expression that defines what assets fields to use for grouping (including
  // `state_change`). The string value should follow SQL syntax: comma separated
  // list of fields. For example: "parent,resource_name".
  //
  // The following fields are supported:
  //
  // * resource_name
  // * category
  // * state
  // * parent
  // * severity
  //
  // The following fields are supported when compare_duration is set:
  //
  // * state_change
  string group_by = 3 [(google.api.field_behavior) = REQUIRED];

  // Time used as a reference point when filtering findings. The filter is
  // limited to findings existing at the supplied time and their values are
  // those at that specific time. Absence of this field will default to the
  // API's version of NOW.
  google.protobuf.Timestamp read_time = 4;

  // When compare_duration is set, the GroupResult's "state_change" attribute is
  // updated to indicate whether the finding had its state changed, the
  // finding's state remained unchanged, or if the finding was added during the
  // compare_duration period of time that precedes the read_time. This is the
  // time between (read_time - compare_duration) and read_time.
  //
  // The state_change value is derived based on the presence and state of the
  // finding at the two points in time. Intermediate state changes between the
  // two times don't affect the result. For example, the results aren't affected
  // if the finding is made inactive and then active again.
  //
  // Possible "state_change" values when compare_duration is specified:
  //
  // * "CHANGED":   indicates that the finding was present and matched the given
  //                  filter at the start of compare_duration, but changed its
  //                  state at read_time.
  // * "UNCHANGED": indicates that the finding was present and matched the given
  //                  filter at the start of compare_duration and did not change
  //                  state at read_time.
  // * "ADDED":     indicates that the finding did not match the given filter or
  //                  was not present at the start of compare_duration, but was
  //                  present at read_time.
  // * "REMOVED":   indicates that the finding was present and matched the
  //                  filter at the start of compare_duration, but did not match
  //                  the filter at read_time.
  //
  // If compare_duration is not specified, then the only possible state_change
  // is "UNUSED",  which will be the state_change set for all findings present
  // at read_time.
  //
  // If this field is set then `state_change` must be a specified field in
  // `group_by`.
  google.protobuf.Duration compare_duration = 5;

  // The value returned by the last `GroupFindingsResponse`; indicates
  // that this is a continuation of a prior `GroupFindings` call, and
  // that the system should return the next page of data.
  string page_token = 7;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 8;
}

// Response message for group by findings.
message GroupFindingsResponse {
  // Group results. There exists an element for each existing unique
  // combination of property/values. The element contains a count for the number
  // of times those specific property/values appear.
  repeated GroupResult group_by_results = 1;

  // Time used for executing the groupBy request.
  google.protobuf.Timestamp read_time = 2;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 3;

  // The total number of results matching the query.
  int32 total_size = 4;
}

// Result containing the properties and count of a groupBy request.
message GroupResult {
  // Properties matching the groupBy fields in the request.
  map<string, google.protobuf.Value> properties = 1;

  // Total count of resources for the given properties.
  int64 count = 2;
}

// Request message for listing notification configs.
message ListNotificationConfigsRequest {
  // Required. Name of the organization to list notification configs.
  // Its format is "organizations/[organization_id]".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];

  // The value returned by the last `ListNotificationConfigsResponse`; indicates
  // that this is a continuation of a prior `ListNotificationConfigs` call, and
  // that the system should return the next page of data.
  string page_token = 2;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 3;
}

// Response message for listing notification configs.
message ListNotificationConfigsResponse {
  // Notification configs belonging to the requested parent.
  repeated NotificationConfig notification_configs = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 2;
}

// Request message for listing sources.
message ListSourcesRequest {
  // Required. Resource name of the parent of sources to list. Its format should be
  // "organizations/[organization_id], folders/[folder_id], or
  // projects/[project_id]".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/Source"
    }
  ];

  // The value returned by the last `ListSourcesResponse`; indicates
  // that this is a continuation of a prior `ListSources` call, and
  // that the system should return the next page of data.
  string page_token = 2;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 7;
}

// Response message for listing sources.
message ListSourcesResponse {
  // Sources belonging to the requested parent.
  repeated Source sources = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 2;
}

// Request message for listing assets.
message ListAssetsRequest {
  // Required. Name of the organization assets should belong to. Its format is
  // "organizations/[organization_id], folders/[folder_id], or
  // projects/[project_id]".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securitycenter.googleapis.com/Asset"
    }
  ];

  // Expression that defines the filter to apply across assets.
  // The expression is a list of zero or more restrictions combined via logical
  // operators `AND` and `OR`.
  // Parentheses are supported, and `OR` has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a `-`
  // character in front of them to indicate negation. The fields map to those
  // defined in the Asset resource. Examples include:
  //
  // * name
  // * security_center_properties.resource_name
  // * resource_properties.a_property
  // * security_marks.marks.marka
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  //
  // The following are the allowed field and operator combinations:
  //
  // * name: `=`
  // * update_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `update_time = "2019-06-10T16:07:18-07:00"`
  //     `update_time = 1560208038000`
  //
  // * create_time: `=`, `>`, `<`, `>=`, `<=`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `create_time = "2019-06-10T16:07:18-07:00"`
  //     `create_time = 1560208038000`
  //
  // * iam_policy.policy_blob: `=`, `:`
  // * resource_properties: `=`, `:`, `>`, `<`, `>=`, `<=`
  // * security_marks.marks: `=`, `:`
  // * security_center_properties.resource_name: `=`, `:`
  // * security_center_properties.resource_display_name: `=`, `:`
  // * security_center_properties.resource_type: `=`, `:`
  // * security_center_properties.resource_parent: `=`, `:`
  // * security_center_properties.resource_parent_display_name: `=`, `:`
  // * security_center_properties.resource_project: `=`, `:`
  // * security_center_properties.resource_project_display_name: `=`, `:`
  // * security_center_properties.resource_owners: `=`, `:`
  //
  // For example, `resource_properties.size = 100` is a valid filter string.
  //
  // Use a partial match on the empty string to filter based on a property
  // existing: `resource_properties.my_property : ""`
  //
  // Use a negated partial match on the empty string to filter based on a
  // property not existing: `-resource_properties.my_property : ""`
  string filter = 2;

  // Expression that defines what fields and order to use for sorting. The
  // string value should follow SQL syntax: comma separated list of fields. For
  // example: "name,resource_properties.a_property". The default sorting order
  // is ascending. To specify descending order for a field, a suffix " desc"
  // should be appended to the field name. For example: "name
  // desc,resource_properties.a_property". Redundant space characters in the
  // syntax are insignificant. "name desc,resource_properties.a_property" and "
  // name     desc  ,   resource_properties.a_property  " are equivalent.
  //
  // The following fields are supported:
  // name
  // update_time
  // resource_properties
  // security_marks.marks
  // security_center_properties.resource_name
  // security_center_properties.resource_display_name
  // security_center_properties.resource_parent
  // security_center_properties.resource_parent_display_name
  // security_center_properties.resource_project
  // security_center_properties.resource_project_display_name
  // security_center_properties.resource_type
  string order_by = 3;

  // Time used as a reference point when filtering assets. The filter is limited
  // to assets existing at the supplied time and their values are those at that
  // specific time. Absence of this field will default to the API's version of
  // NOW.
  google.protobuf.Timestamp read_time = 4;

  // When compare_duration is set, the ListAssetsResult's "state_change"
  // attribute is updated to indicate whether the asset was added, removed, or
  // remained present during the compare_duration period of time that precedes
  // the read_time. This is the time between (read_time - compare_duration) and
  // read_time.
  //
  // The state_change value is derived based on the presence of the asset at the
  // two points in time. Intermediate state changes between the two times don't
  // affect the result. For example, the results aren't affected if the asset is
  // removed and re-created again.
  //
  // Possible "state_change" values when compare_duration is specified:
  //
  // * "ADDED":   indicates that the asset was not present at the start of
  //                compare_duration, but present at read_time.
  // * "REMOVED": indicates that the asset was present at the start of
  //                compare_duration, but not present at read_time.
  // * "ACTIVE":  indicates that the asset was present at both the
  //                start and the end of the time period defined by
  //                compare_duration and read_time.
  //
  // If compare_duration is not specified, then the only possible state_change
  // is "UNUSED",  which will be the state_change set for all assets present at
  // read_time.
  google.protobuf.Duration compare_duration = 5;

  // A field mask to specify the ListAssetsResult fields to be listed in the
  // response.
  // An empty field mask will list all fields.
  google.protobuf.FieldMask field_mask = 7;

  // The value returned by the last `ListAssetsResponse`; indicates
  // that this is a continuation of a prior `ListAssets` call, and
  // that the system should return the next page of data.
  string page_token = 8;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 9;
}

// Response message for listing assets.
message ListAssetsResponse {
  // Result containing the Asset and its State.
  message ListAssetsResult {
    // The change in state of the asset.
    //
    // When querying across two points in time this describes
    // the change between the two points: ADDED, REMOVED, or ACTIVE.
    // If there was no compare_duration supplied in the request the state change
    // will be: UNUSED
    enum StateChange {
      // State change is unused, this is the canonical default for this enum.
      UNUSED = 0;

      // Asset was added between the points in time.
      ADDED = 1;

      // Asset was removed between the points in time.
      REMOVED = 2;

      // Asset was present at both point(s) in time.
      ACTIVE = 3;
    }

    // Asset matching the search request.
    Asset asset = 1;

    // State change of the asset between the points in time.
    StateChange state_change = 2;
  }

  // Assets matching the list request.
  repeated ListAssetsResult list_assets_results = 1;

  // Time used for executing the list request.
  google.protobuf.Timestamp read_time = 2;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 3;

  // The total number of assets matching the query.
  int32 total_size = 4;
}

// Request message for listing findings.
message ListFindingsRequest {
  // Required. Name of the source the findings belong to. Its format is
  // "organizations/[organization_id]/sources/[source_id],
  // folders/[folder_id]/sources/[source_id], or
  // projects/[project_id]/sources/[source_id]". To list across all sources
  // provide a source_id of `-`. For example:
  // organizations/{organization_id}/sources/-, folders/{folder_id}/sources/- or
  // projects/{projects_id}/sources/-
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Source"
    }
  ];

  // Expression that defines the filter to apply across findings.
  // The expression is a list of one or more restrictions combined via logical
  // operators `AND` and `OR`.
  // Parentheses are supported, and `OR` has higher precedence than `AND`.
  //
  // Restrictions have the form `<field> <operator> <value>` and may have a `-`
  // character in front of them to indicate negation. Examples include:
  //
  //  * name
  //  * source_properties.a_property
  //  * security_marks.marks.marka
  //
  // The supported operators are:
  //
  // * `=` for all value types.
  // * `>`, `<`, `>=`, `<=` for integer values.
  // * `:`, meaning substring matching, for strings.
  //
  // The supported value types are:
  //
  // * string literals in quotes.
  // * integer literals without quotes.
  // * boolean literals `true` and `false` without quotes.
  //
  // The following field and operator combinations are supported:
  //
  // * name: `=`
  // * parent: `=`, `:`
  // * resource_name: `=`, `:`
  // * state: `=`, `:`
  // * category: `=`, `:`
  // * external_uri: `=`, `:`
  // * event_time: `=`, `>`, `<`, `>=`, `<=`
  // * severity: `=`, `:`
  //
  //   Usage: This should be milliseconds since epoch or an RFC3339 string.
  //   Examples:
  //     `event_time = "2019-06-10T16:07:18-07:00"`
  //     `event_time = 1560208038000`
  //
  // security_marks.marks: `=`, `:`
  // source_properties: `=`, `:`, `>`, `<`, `>=`, `<=`
  //
  // For example, `source_properties.size = 100` is a valid filter string.
  //
  // Use a partial match on the empty string to filter based on a property
  // existing: `source_properties.my_property : ""`
  //
  // Use a negated partial match on the empty string to filter based on a
  // property not existing: `-source_properties.my_property : ""`
  string filter = 2;

  // Expression that defines what fields and order to use for sorting. The
  // string value should follow SQL syntax: comma separated list of fields. For
  // example: "name,resource_properties.a_property". The default sorting order
  // is ascending. To specify descending order for a field, a suffix " desc"
  // should be appended to the field name. For example: "name
  // desc,source_properties.a_property". Redundant space characters in the
  // syntax are insignificant. "name desc,source_properties.a_property" and "
  // name     desc  ,   source_properties.a_property  " are equivalent.
  //
  // The following fields are supported:
  // name
  // parent
  // state
  // category
  // resource_name
  // event_time
  // source_properties
  // security_marks.marks
  string order_by = 3;

  // Time used as a reference point when filtering findings. The filter is
  // limited to findings existing at the supplied time and their values are
  // those at that specific time. Absence of this field will default to the
  // API's version of NOW.
  google.protobuf.Timestamp read_time = 4;

  // When compare_duration is set, the ListFindingsResult's "state_change"
  // attribute is updated to indicate whether the finding had its state changed,
  // the finding's state remained unchanged, or if the finding was added in any
  // state during the compare_duration period of time that precedes the
  // read_time. This is the time between (read_time - compare_duration) and
  // read_time.
  //
  // The state_change value is derived based on the presence and state of the
  // finding at the two points in time. Intermediate state changes between the
  // two times don't affect the result. For example, the results aren't affected
  // if the finding is made inactive and then active again.
  //
  // Possible "state_change" values when compare_duration is specified:
  //
  // * "CHANGED":   indicates that the finding was present and matched the given
  //                  filter at the start of compare_duration, but changed its
  //                  state at read_time.
  // * "UNCHANGED": indicates that the finding was present and matched the given
  //                  filter at the start of compare_duration and did not change
  //                  state at read_time.
  // * "ADDED":     indicates that the finding did not match the given filter or
  //                  was not present at the start of compare_duration, but was
  //                  present at read_time.
  // * "REMOVED":   indicates that the finding was present and matched the
  //                  filter at the start of compare_duration, but did not match
  //                  the filter at read_time.
  //
  // If compare_duration is not specified, then the only possible state_change
  // is "UNUSED", which will be the state_change set for all findings present at
  // read_time.
  google.protobuf.Duration compare_duration = 5;

  // A field mask to specify the Finding fields to be listed in the response.
  // An empty field mask will list all fields.
  google.protobuf.FieldMask field_mask = 7;

  // The value returned by the last `ListFindingsResponse`; indicates
  // that this is a continuation of a prior `ListFindings` call, and
  // that the system should return the next page of data.
  string page_token = 8;

  // The maximum number of results to return in a single response. Default is
  // 10, minimum is 1, maximum is 1000.
  int32 page_size = 9;
}

// Response message for listing findings.
message ListFindingsResponse {
  // Result containing the Finding and its StateChange.
  message ListFindingsResult {
    // Information related to the Google Cloud resource that is
    // associated with this finding.
    message Resource {
      // The full resource name of the resource. See:
      // https://cloud.google.com/apis/design/resource_names#full_resource_name
      string name = 1;

      // The full resource name of project that the resource belongs to.
      string project_name = 2;

      // The human readable name of project that the resource belongs to.
      string project_display_name = 3;

      // The full resource name of resource's parent.
      string parent_name = 4;

      // The human readable name of resource's parent.
      string parent_display_name = 5;

      // Contains a Folder message for each folder in the assets ancestry.
      // The first folder is the deepest nested folder, and the last folder is
      // the folder directly under the Organization.
      repeated Folder folders = 10;
    }

    // The change in state of the finding.
    //
    // When querying across two points in time this describes
    // the change in the finding between the two points: CHANGED, UNCHANGED,
    // ADDED, or REMOVED. Findings can not be deleted, so REMOVED implies that
    // the finding at timestamp does not match the filter specified, but it did
    // at timestamp - compare_duration. If there was no compare_duration
    // supplied in the request the state change will be: UNUSED
    enum StateChange {
      // State change is unused, this is the canonical default for this enum.
      UNUSED = 0;

      // The finding has changed state in some way between the points in time
      // and existed at both points.
      CHANGED = 1;

      // The finding has not changed state between the points in time and
      // existed at both points.
      UNCHANGED = 2;

      // The finding was created between the points in time.
      ADDED = 3;

      // The finding at timestamp does not match the filter specified, but it
      // did at timestamp - compare_duration.
      REMOVED = 4;
    }

    // Finding matching the search request.
    Finding finding = 1;

    // State change of the finding between the points in time.
    StateChange state_change = 2;

    // Output only. Resource that is associated with this finding.
    Resource resource = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Findings matching the list request.
  repeated ListFindingsResult list_findings_results = 1;

  // Time used for executing the list request.
  google.protobuf.Timestamp read_time = 2;

  // Token to retrieve the next page of results, or empty if there are no more
  // results.
  string next_page_token = 3;

  // The total number of findings matching the query.
  int32 total_size = 4;
}

// Request message for updating a finding's state.
message SetFindingStateRequest {
  // Required. The relative resource name of the finding. See:
  // https://cloud.google.com/apis/design/resource_names#relative_resource_name
  // Example:
  // "organizations/{organization_id}/sources/{source_id}/finding/{finding_id}".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securitycenter.googleapis.com/Finding"
    }
  ];

  // Required. The desired State of the finding.
  Finding.State state = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The time at which the updated state takes effect.
  google.protobuf.Timestamp start_time = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for running asset discovery for an organization.
message RunAssetDiscoveryRequest {
  // Required. Name of the organization to run asset discovery for. Its format is
  // "organizations/[organization_id]".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Organization"
    }
  ];
}

// Request message for updating or creating a finding.
message UpdateFindingRequest {
  // Required. The finding resource to update or create if it does not already exist.
  // parent, security_marks, and update_time will be ignored.
  //
  // In the case of creation, the finding id portion of the name must be
  // alphanumeric and less than or equal to 32 characters and greater than 0
  // characters in length.
  Finding finding = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the finding resource. This field should
  // not be specified when creating a finding.
  //
  // When updating a finding, an empty mask is treated as updating all mutable
  // fields and replacing source_properties.  Individual source_properties can
  // be added/updated by using "source_properties.<property key>" in the field
  // mask.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a notification config.
message UpdateNotificationConfigRequest {
  // Required. The notification config to update.
  NotificationConfig notification_config = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the notification config.
  //
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating an organization's settings.
message UpdateOrganizationSettingsRequest {
  // Required. The organization settings resource to update.
  OrganizationSettings organization_settings = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the settings resource.
  //
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a source.
message UpdateSourceRequest {
  // Required. The source resource to update.
  Source source = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the source resource.
  //
  // If empty all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for updating a SecurityMarks resource.
message UpdateSecurityMarksRequest {
  // Required. The security marks resource to update.
  SecurityMarks security_marks = 1 [(google.api.field_behavior) = REQUIRED];

  // The FieldMask to use when updating the security marks resource.
  //
  // The field mask must not contain duplicate fields.
  // If empty or set to "marks", all marks will be replaced.  Individual
  // marks can be updated using "marks.<mark_key>".
  google.protobuf.FieldMask update_mask = 2;

  // The time at which the updated SecurityMarks take effect.
  // If not set uses current server time.  Updates will be applied to the
  // SecurityMarks that are active immediately preceding this time.
  google.protobuf.Timestamp start_time = 3;
}
