// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1p1beta1;

import "google/api/field_behavior.proto";
import "google/cloud/securitycenter/v1p1beta1/folder.proto";

option csharp_namespace = "Google.Cloud.SecurityCenter.V1P1Beta1";
option go_package = "cloud.google.com/go/securitycenter/apiv1p1beta1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_outer_classname = "ResourceProto";
option java_package = "com.google.cloud.securitycenter.v1p1beta1";
option php_namespace = "Google\\Cloud\\SecurityCenter\\V1p1beta1";
option ruby_package = "Google::Cloud::SecurityCenter::V1p1beta1";

// Information related to the Google Cloud resource.
message Resource {
  // The full resource name of the resource. See:
  // https://cloud.google.com/apis/design/resource_names#full_resource_name
  string name = 1;

  // The full resource name of project that the resource belongs to.
  string project = 2;

  // The human readable name of project that the resource belongs to.
  string project_display_name = 3;

  // The full resource name of resource's parent.
  string parent = 4;

  // The human readable name of resource's parent.
  string parent_display_name = 5;

  // Output only. Contains a Folder message for each folder in the assets ancestry.
  // The first folder is the deepest nested folder, and the last folder is the
  // folder directly under the Organization.
  repeated Folder folders = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
