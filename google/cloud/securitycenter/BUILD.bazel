# This build file includes a target for the Ruby wrapper library for
# google-cloud-security_center.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for securitycenter.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "securitycenter_ruby_wrapper",
    srcs = ["//google/cloud/securitycenter/v1:securitycenter_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-security_center",
        "ruby-cloud-env-prefix=SECURITY_CENTER",
        "ruby-cloud-wrapper-of=v1:0.34;v1p1beta1:0.13;v2:0.0",
        "ruby-cloud-product-url=https://cloud.google.com/security-command-center",
        "ruby-cloud-api-id=securitycenter.googleapis.com",
        "ruby-cloud-api-shortname=securitycenter",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Security Command Center API provides access to temporal views of assets and findings within an organization.",
    ruby_cloud_title = "Security Command Center",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-securitycenter-ruby",
    deps = [
        ":securitycenter_ruby_wrapper",
    ],
)
