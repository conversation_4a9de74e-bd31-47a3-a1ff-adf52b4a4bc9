// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1beta1;

import "google/api/resource.proto";

option go_package = "cloud.google.com/go/securitycenter/apiv1beta1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.securitycenter.v1beta1";

// User specified settings that are attached to the Security Command
// Center organization.
message OrganizationSettings {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/OrganizationSettings"
    pattern: "organizations/{organization}/organizationSettings"
  };

  // The configuration used for Asset Discovery runs.
  message AssetDiscoveryConfig {
    // The mode of inclusion when running Asset Discovery.
    // Asset discovery can be limited by explicitly identifying projects to be
    // included or excluded. If INCLUDE_ONLY is set, then only those projects
    // within the organization and their children are discovered during asset
    // discovery. If EXCLUDE is set, then projects that don't match those
    // projects are discovered during asset discovery. If neither are set, then
    // all projects within the organization are discovered during asset
    // discovery.
    enum InclusionMode {
      // Unspecified. Setting the mode with this value will disable
      // inclusion/exclusion filtering for Asset Discovery.
      INCLUSION_MODE_UNSPECIFIED = 0;

      // Asset Discovery will capture only the resources within the projects
      // specified. All other resources will be ignored.
      INCLUDE_ONLY = 1;

      // Asset Discovery will ignore all resources under the projects specified.
      // All other resources will be retrieved.
      EXCLUDE = 2;
    }

    // The project ids to use for filtering asset discovery.
    repeated string project_ids = 1;

    // The mode to use for filtering asset discovery.
    InclusionMode inclusion_mode = 2;
  }

  // The relative resource name of the settings. See:
  // https://cloud.google.com/apis/design/resource_names#relative_resource_name
  // Example:
  // "organizations/{organization_id}/organizationSettings".
  string name = 1;

  // A flag that indicates if Asset Discovery should be enabled. If the flag is
  // set to `true`, then discovery of assets will occur. If it is set to `false,
  // all historical assets will remain, but discovery of future assets will not
  // occur.
  bool enable_asset_discovery = 2;

  // The configuration used for Asset Discovery runs.
  AssetDiscoveryConfig asset_discovery_config = 3;
}
