# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "securitycenter_proto",
    srcs = [
        "asset.proto",
        "finding.proto",
        "organization_settings.proto",
        "run_asset_discovery_response.proto",
        "security_marks.proto",
        "securitycenter_service.proto",
        "source.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "securitycenter_proto_with_info",
    deps = [
        ":securitycenter_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "securitycenter_java_proto",
    deps = [":securitycenter_proto"],
)

java_grpc_library(
    name = "securitycenter_java_grpc",
    srcs = [":securitycenter_proto"],
    deps = [":securitycenter_java_proto"],
)

java_gapic_library(
    name = "securitycenter_java_gapic",
    srcs = [":securitycenter_proto_with_info"],
    grpc_service_config = "securitycenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "securitycenter_v1beta1.yaml",
    test_deps = [
        ":securitycenter_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":securitycenter_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "securitycenter_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.securitycenter.v1beta1.SecurityCenterClientHttpJsonTest",
        "com.google.cloud.securitycenter.v1beta1.SecurityCenterClientTest",
    ],
    runtime_deps = [":securitycenter_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-securitycenter-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":securitycenter_java_gapic",
        ":securitycenter_java_grpc",
        ":securitycenter_java_proto",
        ":securitycenter_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "securitycenter_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/securitycenter/apiv1beta1/securitycenterpb",
    protos = [":securitycenter_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "securitycenter_go_gapic",
    srcs = [":securitycenter_proto_with_info"],
    grpc_service_config = "securitycenter_grpc_service_config.json",
    importpath = "cloud.google.com/go/securitycenter/apiv1beta1;securitycenter",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "securitycenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":securitycenter_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-securitycenter-v1beta1-go",
    deps = [
        ":securitycenter_go_gapic",
        ":securitycenter_go_gapic_srcjar-snippets.srcjar",
        ":securitycenter_go_gapic_srcjar-test.srcjar",
        ":securitycenter_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "securitycenter_py_gapic",
    srcs = [":securitycenter_proto"],
    grpc_service_config = "securitycenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "securitycenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "securitycenter_py_gapic_test",
    srcs = [
        "securitycenter_py_gapic_pytest.py",
        "securitycenter_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":securitycenter_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "securitycenter-v1beta1-py",
    deps = [
        ":securitycenter_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "securitycenter_php_proto",
    deps = [":securitycenter_proto"],
)

php_gapic_library(
    name = "securitycenter_php_gapic",
    srcs = [":securitycenter_proto_with_info"],
    grpc_service_config = "securitycenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "securitycenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":securitycenter_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-securitycenter-v1beta1-php",
    deps = [
        ":securitycenter_php_gapic",
        ":securitycenter_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "securitycenter_nodejs_gapic",
    package_name = "@google-cloud/security-center",
    src = ":securitycenter_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "securitycenter_grpc_service_config.json",
    package = "google.cloud.securitycenter.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "securitycenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "securitycenter-v1beta1-nodejs",
    deps = [
        ":securitycenter_nodejs_gapic",
        ":securitycenter_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "securitycenter_ruby_proto",
    deps = [":securitycenter_proto"],
)

ruby_grpc_library(
    name = "securitycenter_ruby_grpc",
    srcs = [":securitycenter_proto"],
    deps = [":securitycenter_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "securitycenter_ruby_gapic",
    srcs = [":securitycenter_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-securitycenter-v1beta1"],
    rest_numeric_enums = True,
    service_yaml = "securitycenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":securitycenter_ruby_grpc",
        ":securitycenter_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-securitycenter-v1beta1-ruby",
    deps = [
        ":securitycenter_ruby_gapic",
        ":securitycenter_ruby_grpc",
        ":securitycenter_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "securitycenter_csharp_proto",
    deps = [":securitycenter_proto"],
)

csharp_grpc_library(
    name = "securitycenter_csharp_grpc",
    srcs = [":securitycenter_proto"],
    deps = [":securitycenter_csharp_proto"],
)

csharp_gapic_library(
    name = "securitycenter_csharp_gapic",
    srcs = [":securitycenter_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "securitycenter_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "securitycenter_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":securitycenter_csharp_grpc",
        ":securitycenter_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-securitycenter-v1beta1-csharp",
    deps = [
        ":securitycenter_csharp_gapic",
        ":securitycenter_csharp_grpc",
        ":securitycenter_csharp_proto",
    ],
)
