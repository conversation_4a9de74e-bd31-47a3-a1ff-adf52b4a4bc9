// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securitycenter.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securitycenter/v1beta1/security_marks.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/securitycenter/apiv1beta1/securitycenterpb;securitycenterpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.securitycenter.v1beta1";

// Security Command Center finding.
//
// A finding is a record of assessment data (security, risk, health or privacy)
// ingested into Security Command Center for presentation, notification,
// analysis, policy testing, and enforcement. For example, an XSS vulnerability
// in an App Engine application is a finding.
message Finding {
  option (google.api.resource) = {
    type: "securitycenter.googleapis.com/Finding"
    pattern: "organizations/{organization}/sources/{source}/findings/{finding}"
  };

  // The state of the finding.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The finding requires attention and has not been addressed yet.
    ACTIVE = 1;

    // The finding has been fixed, triaged as a non-issue or otherwise addressed
    // and is no longer active.
    INACTIVE = 2;
  }

  // The relative resource name of this finding. See:
  // https://cloud.google.com/apis/design/resource_names#relative_resource_name
  // Example:
  // "organizations/{organization_id}/sources/{source_id}/findings/{finding_id}"
  string name = 1;

  // Immutable. The relative resource name of the source the finding belongs to.
  // See:
  // https://cloud.google.com/apis/design/resource_names#relative_resource_name
  // This field is immutable after creation time.
  // For example:
  // "organizations/{organization_id}/sources/{source_id}"
  string parent = 2 [(google.api.field_behavior) = IMMUTABLE];

  // For findings on Google Cloud resources, the full resource
  // name of the Google Cloud resource this finding is for. See:
  // https://cloud.google.com/apis/design/resource_names#full_resource_name
  // When the finding is for a non-Google Cloud resource, the resourceName can
  // be a customer or partner defined string. This field is immutable after
  // creation time.
  string resource_name = 3;

  // The state of the finding.
  State state = 4;

  // The additional taxonomy group within findings from a given source.
  // This field is immutable after creation time.
  // Example: "XSS_FLASH_INJECTION"
  string category = 5;

  // The URI that, if available, points to a web page outside of Security
  // Command Center where additional information about the finding can be found.
  // This field is guaranteed to be either empty or a well formed URL.
  string external_uri = 6;

  // Source specific properties. These properties are managed by the source
  // that writes the finding. The key names in the source_properties map must be
  // between 1 and 255 characters, and must start with a letter and contain
  // alphanumeric characters or underscores only.
  map<string, google.protobuf.Value> source_properties = 7;

  // Output only. User specified security marks. These marks are entirely
  // managed by the user and come from the SecurityMarks resource that belongs
  // to the finding.
  SecurityMarks security_marks = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The time at which the event took place, or when an update to the finding
  // occurred. For example, if the finding represents an open firewall it would
  // capture the time the detector believes the firewall became open. The
  // accuracy is determined by the detector. If the finding were to be resolved
  // afterward, this time would reflect when the finding was resolved.
  google.protobuf.Timestamp event_time = 9;

  // The time at which the finding was created in Security Command Center.
  google.protobuf.Timestamp create_time = 10;
}
