{"methodConfig": [{"name": [{"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "CreateSource"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "CreateF<PERSON>ing"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "RunAssetDiscovery"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "SetFindingState"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "SetIamPolicy"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "UpdateFinding"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "UpdateOrganizationSettings"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "UpdateSource"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "GetIamPolicy"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "GetOrganizationSettings"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "GetSource"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "ListSources"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "TestIamPermissions"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "UpdateSecurityMarks"}], "timeout": "480s"}, {"name": [{"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "GroupAssets"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "GroupFindings"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "ListAssets"}, {"service": "google.cloud.securitycenter.v1beta1.SecurityCenter", "method": "ListFindings"}], "timeout": "480s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}