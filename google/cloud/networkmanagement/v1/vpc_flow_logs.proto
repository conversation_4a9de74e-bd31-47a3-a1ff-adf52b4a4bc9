// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkmanagement.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/networkmanagement/v1/reachability.proto";
import "google/cloud/networkmanagement/v1/vpc_flow_logs_config.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.NetworkManagement.V1";
option go_package = "cloud.google.com/go/networkmanagement/apiv1/networkmanagementpb;networkmanagementpb";
option java_multiple_files = true;
option java_outer_classname = "VpcFlowLogsProto";
option java_package = "com.google.cloud.networkmanagement.v1";
option php_namespace = "Google\\Cloud\\NetworkManagement\\V1";
option ruby_package = "Google::Cloud::NetworkManagement::V1";

// The VPC Flow Logs service in the Google Cloud Network Management API provides
// configurations that generate Flow Logs. The service and the configuration
// resources created using this service are global.
service VpcFlowLogsService {
  option (google.api.default_host) = "networkmanagement.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all `VpcFlowLogsConfigs` in a given project.
  rpc ListVpcFlowLogsConfigs(ListVpcFlowLogsConfigsRequest)
      returns (ListVpcFlowLogsConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/vpcFlowLogsConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a specific `VpcFlowLogsConfig`.
  rpc GetVpcFlowLogsConfig(GetVpcFlowLogsConfigRequest)
      returns (VpcFlowLogsConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/vpcFlowLogsConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new `VpcFlowLogsConfig`.
  // If a configuration with the exact same settings already exists (even if the
  // ID is different), the creation fails.
  // Notes:
  //
  //   1. Creating a configuration with state=DISABLED will fail
  //   2. The following fields are not considered as `settings` for the purpose
  //   of the check mentioned above, therefore - creating another configuration
  //   with the same fields but different values for the following fields will
  //   fail as well:
  //       * name
  //       * create_time
  //       * update_time
  //       * labels
  //       * description
  rpc CreateVpcFlowLogsConfig(CreateVpcFlowLogsConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/vpcFlowLogsConfigs"
      body: "vpc_flow_logs_config"
    };
    option (google.api.method_signature) =
        "parent,vpc_flow_logs_config,vpc_flow_logs_config_id";
    option (google.longrunning.operation_info) = {
      response_type: "VpcFlowLogsConfig"
      metadata_type: "google.cloud.networkmanagement.v1.OperationMetadata"
    };
  }

  // Updates an existing `VpcFlowLogsConfig`.
  // If a configuration with the exact same settings already exists (even if the
  // ID is different), the creation fails.
  // Notes:
  //
  //   1. Updating a configuration with state=DISABLED will fail.
  //   2. The following fields are not considered as `settings` for the purpose
  //   of the check mentioned above, therefore - updating another configuration
  //   with the same fields but different values for the following fields will
  //   fail as well:
  //       * name
  //       * create_time
  //       * update_time
  //       * labels
  //       * description
  rpc UpdateVpcFlowLogsConfig(UpdateVpcFlowLogsConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{vpc_flow_logs_config.name=projects/*/locations/*/vpcFlowLogsConfigs/*}"
      body: "vpc_flow_logs_config"
    };
    option (google.api.method_signature) = "vpc_flow_logs_config,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "VpcFlowLogsConfig"
      metadata_type: "google.cloud.networkmanagement.v1.OperationMetadata"
    };
  }

  // Deletes a specific `VpcFlowLogsConfig`.
  rpc DeleteVpcFlowLogsConfig(DeleteVpcFlowLogsConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/vpcFlowLogsConfigs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.networkmanagement.v1.OperationMetadata"
    };
  }
}

// Request for the `ListVpcFlowLogsConfigs` method.
message ListVpcFlowLogsConfigsRequest {
  // Required. The parent resource of the VpcFlowLogsConfig:
  //     `projects/{project_id}/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkmanagement.googleapis.com/VpcFlowLogsConfig"
    }
  ];

  // Optional. Number of `VpcFlowLogsConfigs` to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token from an earlier query, as returned in
  // `next_page_token`.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Lists the `VpcFlowLogsConfigs` that match the filter expression.
  // A filter expression must use the supported [CEL logic operators]
  // (https://cloud.google.com/vpc/docs/about-flow-logs-records#supported_cel_logic_operators).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Field to use to sort the list.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response for the `ListVpcFlowLogsConfigs` method.
message ListVpcFlowLogsConfigsResponse {
  // List of VPC Flow Log configurations.
  repeated VpcFlowLogsConfig vpc_flow_logs_configs = 1;

  // Page token to fetch the next set of configurations.
  string next_page_token = 2;

  // Locations that could not be reached (when querying all locations with `-`).
  repeated string unreachable = 3;
}

// Request for the `GetVpcFlowLogsConfig` method.
message GetVpcFlowLogsConfigRequest {
  // Required. `VpcFlowLogsConfig` resource name using the form:
  //     `projects/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkmanagement.googleapis.com/VpcFlowLogsConfig"
    }
  ];
}

// Request for the `CreateVpcFlowLogsConfig` method.
message CreateVpcFlowLogsConfigRequest {
  // Required. The parent resource of the VPC Flow Logs configuration to create:
  //     `projects/{project_id}/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "networkmanagement.googleapis.com/VpcFlowLogsConfig"
    }
  ];

  // Required. ID of the `VpcFlowLogsConfig`.
  string vpc_flow_logs_config_id = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkmanagement.googleapis.com/VpcFlowLogsConfig"
    }
  ];

  // Required. A `VpcFlowLogsConfig` resource
  VpcFlowLogsConfig vpc_flow_logs_config = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `UpdateVpcFlowLogsConfig` method.
message UpdateVpcFlowLogsConfigRequest {
  // Required. Mask of fields to update. At least one path must be supplied in
  // this field.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Only fields specified in update_mask are updated.
  VpcFlowLogsConfig vpc_flow_logs_config = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `DeleteVpcFlowLogsConfig` method.
message DeleteVpcFlowLogsConfigRequest {
  // Required. `VpcFlowLogsConfig` resource name using the form:
  //     `projects/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkmanagement.googleapis.com/VpcFlowLogsConfig"
    }
  ];
}
