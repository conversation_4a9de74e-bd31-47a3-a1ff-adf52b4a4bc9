// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkmanagement.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkManagement.V1";
option go_package = "cloud.google.com/go/networkmanagement/apiv1/networkmanagementpb;networkmanagementpb";
option java_multiple_files = true;
option java_outer_classname = "VpcFlowLogsConfigProto";
option java_package = "com.google.cloud.networkmanagement.v1";
option php_namespace = "Google\\Cloud\\NetworkManagement\\V1";
option ruby_package = "Google::Cloud::NetworkManagement::V1";

// A configuration to generate VPC Flow Logs.
message VpcFlowLogsConfig {
  option (google.api.resource) = {
    type: "networkmanagement.googleapis.com/VpcFlowLogsConfig"
    pattern: "projects/{project}/locations/{location}/vpcFlowLogsConfigs/{vpc_flow_logs_config}"
    plural: "vpcFlowLogsConfigs"
    singular: "vpcFlowLogsConfig"
  };

  // Determines whether this configuration will be generating logs.
  // Setting state=DISABLED will pause the log generation for this config.
  enum State {
    // If not specified, will default to ENABLED.
    STATE_UNSPECIFIED = 0;

    // When ENABLED, this configuration will generate logs.
    ENABLED = 1;

    // When DISABLED, this configuration will not generate logs.
    DISABLED = 2;
  }

  // Toggles the aggregation interval for collecting flow logs by 5-tuple.
  enum AggregationInterval {
    // If not specified, will default to INTERVAL_5_SEC.
    AGGREGATION_INTERVAL_UNSPECIFIED = 0;

    // Aggregate logs in 5s intervals.
    INTERVAL_5_SEC = 1;

    // Aggregate logs in 30s intervals.
    INTERVAL_30_SEC = 2;

    // Aggregate logs in 1m intervals.
    INTERVAL_1_MIN = 3;

    // Aggregate logs in 5m intervals.
    INTERVAL_5_MIN = 4;

    // Aggregate logs in 10m intervals.
    INTERVAL_10_MIN = 5;

    // Aggregate logs in 15m intervals.
    INTERVAL_15_MIN = 6;
  }

  // Configures which log fields would be included.
  enum Metadata {
    // If not specified, will default to INCLUDE_ALL_METADATA.
    METADATA_UNSPECIFIED = 0;

    // Include all metadata fields.
    INCLUDE_ALL_METADATA = 1;

    // Exclude all metadata fields.
    EXCLUDE_ALL_METADATA = 2;

    // Include only custom fields (specified in metadata_fields).
    CUSTOM_METADATA = 3;
  }

  // Optional states of the target resource that are used as part of the
  // diagnostic bit.
  enum TargetResourceState {
    // Unspecified target resource state.
    TARGET_RESOURCE_STATE_UNSPECIFIED = 0;

    // Indicates that the target resource exists.
    TARGET_RESOURCE_EXISTS = 1;

    // Indicates that the target resource does not exist.
    TARGET_RESOURCE_DOES_NOT_EXIST = 2;
  }

  // Identifier. Unique name of the configuration using the form:
  //     `projects/{project_id}/locations/global/vpcFlowLogsConfigs/{vpc_flow_logs_config_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. The user-supplied description of the VPC Flow Logs configuration.
  // Maximum of 512 characters.
  optional string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The state of the VPC Flow Log configuration. Default value is
  // ENABLED. When creating a new configuration, it must be enabled.
  optional State state = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The aggregation interval for the logs. Default value is
  // INTERVAL_5_SEC.
  optional AggregationInterval aggregation_interval = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The value of the field must be in (0, 1]. The sampling rate of
  // VPC Flow Logs where 1.0 means all collected logs are reported. Setting the
  // sampling rate to 0.0 is not allowed. If you want to disable VPC Flow Logs,
  // use the state field instead. Default value is 1.0.
  optional float flow_sampling = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configures whether all, none or a subset of metadata fields
  // should be added to the reported VPC flow logs. Default value is
  // INCLUDE_ALL_METADATA.
  optional Metadata metadata = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Custom metadata fields to include in the reported VPC flow logs.
  // Can only be specified if "metadata" was set to CUSTOM_METADATA.
  repeated string metadata_fields = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Export filter used to define which VPC Flow Logs should be
  // logged.
  optional string filter_expr = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A diagnostic bit - describes the state of the configured
  // target resource for diagnostic purposes.
  optional TargetResourceState target_resource_state = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Reference to the resource of the config scope. That is, the scope from
  // which traffic is logged. The target resource must belong to the same
  // project as the configuration.
  oneof target_resource {
    // Traffic will be logged from the Interconnect Attachment.
    // Format:
    // projects/{project_id}/regions/{region}/interconnectAttachments/{name}
    string interconnect_attachment = 102;

    // Traffic will be logged from the VPN Tunnel.
    // Format: projects/{project_id}/regions/{region}/vpnTunnels/{name}
    string vpn_tunnel = 103;
  }

  // Optional. Resource labels to represent user-provided metadata.
  map<string, string> labels = 11 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time the config was created.
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the config was updated.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
