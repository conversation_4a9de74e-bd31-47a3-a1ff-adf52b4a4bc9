// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkmanagement.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";

option csharp_namespace = "Google.Cloud.NetworkManagement.V1Beta1";
option go_package = "cloud.google.com/go/networkmanagement/apiv1beta1/networkmanagementpb;networkmanagementpb";
option java_multiple_files = true;
option java_outer_classname = "TraceProto";
option java_package = "com.google.cloud.networkmanagement.v1beta1";
option php_namespace = "Google\\Cloud\\NetworkManagement\\V1beta1";
option ruby_package = "Google::Cloud::NetworkManagement::V1beta1";

// Trace represents one simulated packet forwarding path.
//
//   * Each trace contains multiple ordered steps.
//   * Each step is in a particular state with associated configuration.
//   * State is categorized as final or non-final states.
//   * Each final state has a reason associated.
//   * Each trace must end with a final state (the last step).
// ```
//   |---------------------Trace----------------------|
//   Step1(State) Step2(State) ---  StepN(State(final))
// ```
message Trace {
  // Derived from the source and destination endpoints definition specified by
  // user request, and validated by the data plane model.
  // If there are multiple traces starting from different source locations, then
  // the endpoint_info may be different between traces.
  EndpointInfo endpoint_info = 1;

  // A trace of a test contains multiple steps from the initial state to the
  // final state (delivered, dropped, forwarded, or aborted).
  //
  // The steps are ordered by the processing sequence within the simulated
  // network state machine. It is critical to preserve the order of the steps
  // and avoid reordering or sorting them.
  repeated Step steps = 2;

  // ID of trace. For forward traces, this ID is unique for each trace. For
  // return traces, it matches ID of associated forward trace. A single forward
  // trace can be associated with none, one or more than one return trace.
  int32 forward_trace_id = 4;
}

// A simulated forwarding path is composed of multiple steps.
// Each step has a well-defined state and an associated configuration.
message Step {
  // Type of states that are defined in the network state machine.
  // Each step in the packet trace is in a specific state.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // Initial state: packet originating from a Compute Engine instance.
    // An InstanceInfo is populated with starting instance information.
    START_FROM_INSTANCE = 1;

    // Initial state: packet originating from the internet.
    // The endpoint information is populated.
    START_FROM_INTERNET = 2;

    // Initial state: packet originating from a Google service.
    // The google_service information is populated.
    START_FROM_GOOGLE_SERVICE = 27;

    // Initial state: packet originating from a VPC or on-premises network
    // with internal source IP.
    // If the source is a VPC network visible to the user, a NetworkInfo
    // is populated with details of the network.
    START_FROM_PRIVATE_NETWORK = 3;

    // Initial state: packet originating from a Google Kubernetes Engine cluster
    // master. A GKEMasterInfo is populated with starting instance information.
    START_FROM_GKE_MASTER = 21;

    // Initial state: packet originating from a Cloud SQL instance.
    // A CloudSQLInstanceInfo is populated with starting instance information.
    START_FROM_CLOUD_SQL_INSTANCE = 22;

    // Initial state: packet originating from a Redis instance.
    // A RedisInstanceInfo is populated with starting instance information.
    START_FROM_REDIS_INSTANCE = 32;

    // Initial state: packet originating from a Redis Cluster.
    // A RedisClusterInfo is populated with starting Cluster information.
    START_FROM_REDIS_CLUSTER = 33;

    // Initial state: packet originating from a Cloud Function.
    // A CloudFunctionInfo is populated with starting function information.
    START_FROM_CLOUD_FUNCTION = 23;

    // Initial state: packet originating from an App Engine service version.
    // An AppEngineVersionInfo is populated with starting version information.
    START_FROM_APP_ENGINE_VERSION = 25;

    // Initial state: packet originating from a Cloud Run revision.
    // A CloudRunRevisionInfo is populated with starting revision information.
    START_FROM_CLOUD_RUN_REVISION = 26;

    // Initial state: packet originating from a Storage Bucket. Used only for
    // return traces.
    // The storage_bucket information is populated.
    START_FROM_STORAGE_BUCKET = 29;

    // Initial state: packet originating from a published service that uses
    // Private Service Connect. Used only for return traces.
    START_FROM_PSC_PUBLISHED_SERVICE = 30;

    // Initial state: packet originating from a serverless network endpoint
    // group backend. Used only for return traces.
    // The serverless_neg information is populated.
    START_FROM_SERVERLESS_NEG = 31;

    // Config checking state: verify ingress firewall rule.
    APPLY_INGRESS_FIREWALL_RULE = 4;

    // Config checking state: verify egress firewall rule.
    APPLY_EGRESS_FIREWALL_RULE = 5;

    // Config checking state: verify route.
    APPLY_ROUTE = 6;

    // Config checking state: match forwarding rule.
    APPLY_FORWARDING_RULE = 7;

    // Config checking state: verify load balancer backend configuration.
    ANALYZE_LOAD_BALANCER_BACKEND = 28;

    // Config checking state: packet sent or received under foreign IP
    // address and allowed.
    SPOOFING_APPROVED = 8;

    // Forwarding state: arriving at a Compute Engine instance.
    ARRIVE_AT_INSTANCE = 9;

    // Forwarding state: arriving at a Compute Engine internal load balancer.
    // Deprecated in favor of the `ANALYZE_LOAD_BALANCER_BACKEND` state, not
    // used in new tests.
    ARRIVE_AT_INTERNAL_LOAD_BALANCER = 10 [deprecated = true];

    // Forwarding state: arriving at a Compute Engine external load balancer.
    // Deprecated in favor of the `ANALYZE_LOAD_BALANCER_BACKEND` state, not
    // used in new tests.
    ARRIVE_AT_EXTERNAL_LOAD_BALANCER = 11 [deprecated = true];

    // Forwarding state: arriving at a Cloud VPN gateway.
    ARRIVE_AT_VPN_GATEWAY = 12;

    // Forwarding state: arriving at a Cloud VPN tunnel.
    ARRIVE_AT_VPN_TUNNEL = 13;

    // Forwarding state: arriving at a VPC connector.
    ARRIVE_AT_VPC_CONNECTOR = 24;

    // Transition state: packet header translated.
    NAT = 14;

    // Transition state: original connection is terminated and a new proxied
    // connection is initiated.
    PROXY_CONNECTION = 15;

    // Final state: packet could be delivered.
    DELIVER = 16;

    // Final state: packet could be dropped.
    DROP = 17;

    // Final state: packet could be forwarded to a network with an unknown
    // configuration.
    FORWARD = 18;

    // Final state: analysis is aborted.
    ABORT = 19;

    // Special state: viewer of the test result does not have permission to
    // see the configuration in this step.
    VIEWER_PERMISSION_MISSING = 20;
  }

  // A description of the step. Usually this is a summary of the state.
  string description = 1;

  // Each step is in one of the pre-defined states.
  State state = 2;

  // This is a step that leads to the final state Drop.
  bool causes_drop = 3;

  // Project ID that contains the configuration this step is validating.
  string project_id = 4;

  // Configuration or metadata associated with each step.
  // The configuration is filtered based on viewer's permission. If a viewer
  // has no permission to view the configuration in this step, for non-final
  // states a special state is populated (VIEWER_PERMISSION_MISSING), and for
  // final state the configuration is cleared.
  oneof step_info {
    // Display information of a Compute Engine instance.
    InstanceInfo instance = 5;

    // Display information of a Compute Engine firewall rule.
    FirewallInfo firewall = 6;

    // Display information of a Compute Engine route.
    RouteInfo route = 7;

    // Display information of the source and destination under analysis.
    // The endpoint information in an intermediate state may differ with the
    // initial input, as it might be modified by state like NAT,
    // or Connection Proxy.
    EndpointInfo endpoint = 8;

    // Display information of a Google service
    GoogleServiceInfo google_service = 24;

    // Display information of a Compute Engine forwarding rule.
    ForwardingRuleInfo forwarding_rule = 9;

    // Display information of a Compute Engine VPN gateway.
    VpnGatewayInfo vpn_gateway = 10;

    // Display information of a Compute Engine VPN tunnel.
    VpnTunnelInfo vpn_tunnel = 11;

    // Display information of a VPC connector.
    VpcConnectorInfo vpc_connector = 21;

    // Display information of the final state "deliver" and reason.
    DeliverInfo deliver = 12;

    // Display information of the final state "forward" and reason.
    ForwardInfo forward = 13;

    // Display information of the final state "abort" and reason.
    AbortInfo abort = 14;

    // Display information of the final state "drop" and reason.
    DropInfo drop = 15;

    // Display information of the load balancers. Deprecated in favor of the
    // `load_balancer_backend_info` field, not used in new tests.
    LoadBalancerInfo load_balancer = 16 [deprecated = true];

    // Display information of a Google Cloud network.
    NetworkInfo network = 17;

    // Display information of a Google Kubernetes Engine cluster master.
    GKEMasterInfo gke_master = 18;

    // Display information of a Cloud SQL instance.
    CloudSQLInstanceInfo cloud_sql_instance = 19;

    // Display information of a Redis Instance.
    RedisInstanceInfo redis_instance = 30;

    // Display information of a Redis Cluster.
    RedisClusterInfo redis_cluster = 31;

    // Display information of a Cloud Function.
    CloudFunctionInfo cloud_function = 20;

    // Display information of an App Engine service version.
    AppEngineVersionInfo app_engine_version = 22;

    // Display information of a Cloud Run revision.
    CloudRunRevisionInfo cloud_run_revision = 23;

    // Display information of a NAT.
    NatInfo nat = 25;

    // Display information of a ProxyConnection.
    ProxyConnectionInfo proxy_connection = 26;

    // Display information of a specific load balancer backend.
    LoadBalancerBackendInfo load_balancer_backend_info = 27;

    // Display information of a Storage Bucket. Used only for return traces.
    StorageBucketInfo storage_bucket = 28;

    // Display information of a Serverless network endpoint group backend. Used
    // only for return traces.
    ServerlessNegInfo serverless_neg = 29;
  }
}

// For display only. Metadata associated with a Compute Engine instance.
message InstanceInfo {
  // Name of a Compute Engine instance.
  string display_name = 1;

  // URI of a Compute Engine instance.
  string uri = 2;

  // Name of the network interface of a Compute Engine instance.
  string interface = 3;

  // URI of a Compute Engine network.
  string network_uri = 4;

  // Internal IP address of the network interface.
  string internal_ip = 5;

  // External IP address of the network interface.
  string external_ip = 6;

  // Network tags configured on the instance.
  repeated string network_tags = 7;

  // Service account authorized for the instance.
  string service_account = 8 [deprecated = true];

  // URI of the PSC network attachment the NIC is attached to (if relevant).
  string psc_network_attachment_uri = 9;
}

// For display only. Metadata associated with a Compute Engine network.
// Next ID: 7
message NetworkInfo {
  // Name of a Compute Engine network.
  string display_name = 1;

  // URI of a Compute Engine network.
  string uri = 2;

  // URI of the subnet matching the source IP address of the test.
  string matched_subnet_uri = 5;

  // The IP range of the subnet matching the source IP address of the test.
  string matched_ip_range = 4;

  // The region of the subnet matching the source IP address of the test.
  string region = 6;
}

// For display only. Metadata associated with a VPC firewall rule, an implied
// VPC firewall rule, or a firewall policy rule.
message FirewallInfo {
  // The firewall rule's type.
  enum FirewallRuleType {
    // Unspecified type.
    FIREWALL_RULE_TYPE_UNSPECIFIED = 0;

    // Hierarchical firewall policy rule. For details, see
    // [Hierarchical firewall policies
    // overview](https://cloud.google.com/vpc/docs/firewall-policies).
    HIERARCHICAL_FIREWALL_POLICY_RULE = 1;

    // VPC firewall rule. For details, see
    // [VPC firewall rules
    // overview](https://cloud.google.com/vpc/docs/firewalls).
    VPC_FIREWALL_RULE = 2;

    // Implied VPC firewall rule. For details, see
    // [Implied
    // rules](https://cloud.google.com/vpc/docs/firewalls#default_firewall_rules).
    IMPLIED_VPC_FIREWALL_RULE = 3;

    // Implicit firewall rules that are managed by serverless VPC access to
    // allow ingress access. They are not visible in the Google Cloud console.
    // For details, see [VPC connector's implicit
    // rules](https://cloud.google.com/functions/docs/networking/connecting-vpc#restrict-access).
    SERVERLESS_VPC_ACCESS_MANAGED_FIREWALL_RULE = 4;

    // Global network firewall policy rule.
    // For details, see [Network firewall
    // policies](https://cloud.google.com/vpc/docs/network-firewall-policies).
    NETWORK_FIREWALL_POLICY_RULE = 5;

    // Regional network firewall policy rule.
    // For details, see [Regional network firewall
    // policies](https://cloud.google.com/firewall/docs/regional-firewall-policies).
    NETWORK_REGIONAL_FIREWALL_POLICY_RULE = 6;

    // Firewall policy rule containing attributes not yet supported in
    // Connectivity tests. Firewall analysis is skipped if such a rule can
    // potentially be matched. Please see the [list of unsupported
    // configurations](https://cloud.google.com/network-intelligence-center/docs/connectivity-tests/concepts/overview#unsupported-configs).
    UNSUPPORTED_FIREWALL_POLICY_RULE = 100;

    // Tracking state for response traffic created when request traffic goes
    // through allow firewall rule.
    // For details, see [firewall rules
    // specifications](https://cloud.google.com/firewall/docs/firewalls#specifications)
    TRACKING_STATE = 101;
  }

  // The display name of the firewall rule. This field might be empty for
  // firewall policy rules.
  string display_name = 1;

  // The URI of the firewall rule. This field is not applicable to implied
  // VPC firewall rules.
  string uri = 2;

  // Possible values: INGRESS, EGRESS
  string direction = 3;

  // Possible values: ALLOW, DENY, APPLY_SECURITY_PROFILE_GROUP
  string action = 4;

  // The priority of the firewall rule.
  int32 priority = 5;

  // The URI of the VPC network that the firewall rule is associated with.
  // This field is not applicable to hierarchical firewall policy rules.
  string network_uri = 6;

  // The target tags defined by the VPC firewall rule. This field is not
  // applicable to firewall policy rules.
  repeated string target_tags = 7;

  // The target service accounts specified by the firewall rule.
  repeated string target_service_accounts = 8;

  // The name of the firewall policy that this rule is associated with.
  // This field is not applicable to VPC firewall rules and implied VPC firewall
  // rules.
  string policy = 9;

  // The URI of the firewall policy that this rule is associated with.
  // This field is not applicable to VPC firewall rules and implied VPC firewall
  // rules.
  string policy_uri = 11;

  // The firewall rule's type.
  FirewallRuleType firewall_rule_type = 10;
}

// For display only. Metadata associated with a Compute Engine route.
message RouteInfo {
  // Type of route:
  enum RouteType {
    // Unspecified type. Default value.
    ROUTE_TYPE_UNSPECIFIED = 0;

    // Route is a subnet route automatically created by the system.
    SUBNET = 1;

    // Static route created by the user, including the default route to the
    // internet.
    STATIC = 2;

    // Dynamic route exchanged between BGP peers.
    DYNAMIC = 3;

    // A subnet route received from peering network.
    PEERING_SUBNET = 4;

    // A static route received from peering network.
    PEERING_STATIC = 5;

    // A dynamic route received from peering network.
    PEERING_DYNAMIC = 6;

    // Policy based route.
    POLICY_BASED = 7;

    // Advertised route. Synthetic route which is used to transition from the
    // StartFromPrivateNetwork state in Connectivity tests.
    ADVERTISED = 101;
  }

  // Type of next hop:
  enum NextHopType {
    // Unspecified type. Default value.
    NEXT_HOP_TYPE_UNSPECIFIED = 0;

    // Next hop is an IP address.
    NEXT_HOP_IP = 1;

    // Next hop is a Compute Engine instance.
    NEXT_HOP_INSTANCE = 2;

    // Next hop is a VPC network gateway.
    NEXT_HOP_NETWORK = 3;

    // Next hop is a peering VPC.
    NEXT_HOP_PEERING = 4;

    // Next hop is an interconnect.
    NEXT_HOP_INTERCONNECT = 5;

    // Next hop is a VPN tunnel.
    NEXT_HOP_VPN_TUNNEL = 6;

    // Next hop is a VPN gateway. This scenario only happens when tracing
    // connectivity from an on-premises network to Google Cloud through a VPN.
    // The analysis simulates a packet departing from the on-premises network
    // through a VPN tunnel and arriving at a Cloud VPN gateway.
    NEXT_HOP_VPN_GATEWAY = 7;

    // Next hop is an internet gateway.
    NEXT_HOP_INTERNET_GATEWAY = 8;

    // Next hop is blackhole; that is, the next hop either does not exist or is
    // not running.
    NEXT_HOP_BLACKHOLE = 9;

    // Next hop is the forwarding rule of an Internal Load Balancer.
    NEXT_HOP_ILB = 10;

    // Next hop is a
    // [router appliance
    // instance](https://cloud.google.com/network-connectivity/docs/network-connectivity-center/concepts/ra-overview).
    NEXT_HOP_ROUTER_APPLIANCE = 11;

    // Next hop is an NCC hub.
    NEXT_HOP_NCC_HUB = 12;
  }

  // Indicates where routes are applicable.
  enum RouteScope {
    // Unspecified scope. Default value.
    ROUTE_SCOPE_UNSPECIFIED = 0;

    // Route is applicable to packets in Network.
    NETWORK = 1;

    // Route is applicable to packets using NCC Hub's routing table.
    NCC_HUB = 2;
  }

  // Type of route.
  RouteType route_type = 8;

  // Type of next hop.
  NextHopType next_hop_type = 9;

  // Indicates where route is applicable.
  RouteScope route_scope = 14;

  // Name of a route.
  string display_name = 1;

  // URI of a route (if applicable).
  string uri = 2;

  // Region of the route (if applicable).
  string region = 19;

  // Destination IP range of the route.
  string dest_ip_range = 3;

  // Next hop of the route.
  string next_hop = 4;

  // URI of a Compute Engine network. NETWORK routes only.
  string network_uri = 5;

  // Priority of the route.
  int32 priority = 6;

  // Instance tags of the route.
  repeated string instance_tags = 7;

  // Source IP address range of the route. Policy based routes only.
  string src_ip_range = 10;

  // Destination port ranges of the route. Policy based routes only.
  repeated string dest_port_ranges = 11;

  // Source port ranges of the route. Policy based routes only.
  repeated string src_port_ranges = 12;

  // Protocols of the route. Policy based routes only.
  repeated string protocols = 13;

  // URI of a NCC Hub. NCC_HUB routes only.
  optional string ncc_hub_uri = 15;

  // URI of a NCC Spoke. NCC_HUB routes only.
  optional string ncc_spoke_uri = 16;

  // For advertised dynamic routes, the URI of the Cloud Router that advertised
  // the corresponding IP prefix.
  optional string advertised_route_source_router_uri = 17;

  // For advertised routes, the URI of their next hop, i.e. the URI of the
  // hybrid endpoint (VPN tunnel, Interconnect attachment, NCC router appliance)
  // the advertised prefix is advertised through, or URI of the source peered
  // network.
  optional string advertised_route_next_hop_uri = 18;
}

// For display only. Details of a Google Service sending packets to a
// VPC network. Although the source IP might be a publicly routable address,
// some Google Services use special routes within Google production
// infrastructure to reach Compute Engine Instances.
// https://cloud.google.com/vpc/docs/routes#special_return_paths
message GoogleServiceInfo {
  // Recognized type of a Google Service.
  enum GoogleServiceType {
    // Unspecified Google Service.
    GOOGLE_SERVICE_TYPE_UNSPECIFIED = 0;

    // Identity aware proxy.
    // https://cloud.google.com/iap/docs/using-tcp-forwarding
    IAP = 1;

    // One of two services sharing IP ranges:
    // * Load Balancer proxy
    // * Centralized Health Check prober
    // https://cloud.google.com/load-balancing/docs/firewall-rules
    GFE_PROXY_OR_HEALTH_CHECK_PROBER = 2;

    // Connectivity from Cloud DNS to forwarding targets or alternate name
    // servers that use private routing.
    // https://cloud.google.com/dns/docs/zones/forwarding-zones#firewall-rules
    // https://cloud.google.com/dns/docs/policies#firewall-rules
    CLOUD_DNS = 3;

    // private.googleapis.com and restricted.googleapis.com
    GOOGLE_API = 4;

    // Google API via Private Service Connect.
    // https://cloud.google.com/vpc/docs/configure-private-service-connect-apis
    GOOGLE_API_PSC = 5;

    // Google API via VPC Service Controls.
    // https://cloud.google.com/vpc/docs/configure-private-service-connect-apis
    GOOGLE_API_VPC_SC = 6;
  }

  // Source IP address.
  string source_ip = 1;

  // Recognized type of a Google Service.
  GoogleServiceType google_service_type = 2;
}

// For display only. Metadata associated with a Compute Engine forwarding rule.
message ForwardingRuleInfo {
  // Name of the forwarding rule.
  string display_name = 1;

  // URI of the forwarding rule.
  string uri = 2;

  // Protocol defined in the forwarding rule that matches the packet.
  string matched_protocol = 3;

  // Port range defined in the forwarding rule that matches the packet.
  string matched_port_range = 6;

  // VIP of the forwarding rule.
  string vip = 4;

  // Target type of the forwarding rule.
  string target = 5;

  // Network URI.
  string network_uri = 7;

  // Region of the forwarding rule. Set only for regional forwarding rules.
  string region = 8;

  // Name of the load balancer the forwarding rule belongs to. Empty for
  // forwarding rules not related to load balancers (like PSC forwarding rules).
  string load_balancer_name = 9;

  // URI of the PSC service attachment this forwarding rule targets (if
  // applicable).
  string psc_service_attachment_uri = 10;

  // PSC Google API target this forwarding rule targets (if applicable).
  string psc_google_api_target = 11;
}

// For display only. Metadata associated with a load balancer.
message LoadBalancerInfo {
  // The type definition for a load balancer:
  enum LoadBalancerType {
    // Type is unspecified.
    LOAD_BALANCER_TYPE_UNSPECIFIED = 0;

    // Internal TCP/UDP load balancer.
    INTERNAL_TCP_UDP = 1;

    // Network TCP/UDP load balancer.
    NETWORK_TCP_UDP = 2;

    // HTTP(S) proxy load balancer.
    HTTP_PROXY = 3;

    // TCP proxy load balancer.
    TCP_PROXY = 4;

    // SSL proxy load balancer.
    SSL_PROXY = 5;
  }

  // The type definition for a load balancer backend configuration:
  enum BackendType {
    // Type is unspecified.
    BACKEND_TYPE_UNSPECIFIED = 0;

    // Backend Service as the load balancer's backend.
    BACKEND_SERVICE = 1;

    // Target Pool as the load balancer's backend.
    TARGET_POOL = 2;

    // Target Instance as the load balancer's backend.
    TARGET_INSTANCE = 3;
  }

  // Type of the load balancer.
  LoadBalancerType load_balancer_type = 1;

  // URI of the health check for the load balancer. Deprecated and no longer
  // populated as different load balancer backends might have different health
  // checks.
  string health_check_uri = 2 [deprecated = true];

  // Information for the loadbalancer backends.
  repeated LoadBalancerBackend backends = 3;

  // Type of load balancer's backend configuration.
  BackendType backend_type = 4;

  // Backend configuration URI.
  string backend_uri = 5;
}

// For display only. Metadata associated with a specific load balancer backend.
message LoadBalancerBackend {
  // State of a health check firewall configuration:
  enum HealthCheckFirewallState {
    // State is unspecified. Default state if not populated.
    HEALTH_CHECK_FIREWALL_STATE_UNSPECIFIED = 0;

    // There are configured firewall rules to allow health check probes to the
    // backend.
    CONFIGURED = 1;

    // There are firewall rules configured to allow partial health check ranges
    // or block all health check ranges.
    // If a health check probe is sent from denied IP ranges,
    // the health check to the backend will fail. Then, the backend will be
    // marked unhealthy and will not receive traffic sent to the load balancer.
    MISCONFIGURED = 2;
  }

  // Name of a Compute Engine instance or network endpoint.
  string display_name = 1;

  // URI of a Compute Engine instance or network endpoint.
  string uri = 2;

  // State of the health check firewall configuration.
  HealthCheckFirewallState health_check_firewall_state = 3;

  // A list of firewall rule URIs allowing probes from health check IP ranges.
  repeated string health_check_allowing_firewall_rules = 4;

  // A list of firewall rule URIs blocking probes from health check IP ranges.
  repeated string health_check_blocking_firewall_rules = 5;
}

// For display only. Metadata associated with a Compute Engine VPN gateway.
message VpnGatewayInfo {
  // Name of a VPN gateway.
  string display_name = 1;

  // URI of a VPN gateway.
  string uri = 2;

  // URI of a Compute Engine network where the VPN gateway is configured.
  string network_uri = 3;

  // IP address of the VPN gateway.
  string ip_address = 4;

  // A VPN tunnel that is associated with this VPN gateway.
  // There may be multiple VPN tunnels configured on a VPN gateway, and only
  // the one relevant to the test is displayed.
  string vpn_tunnel_uri = 5;

  // Name of a Google Cloud region where this VPN gateway is configured.
  string region = 6;
}

// For display only. Metadata associated with a Compute Engine VPN tunnel.
message VpnTunnelInfo {
  // Types of VPN routing policy. For details, refer to [Networks and Tunnel
  // routing](https://cloud.google.com/network-connectivity/docs/vpn/concepts/choosing-networks-routing/).
  enum RoutingType {
    // Unspecified type. Default value.
    ROUTING_TYPE_UNSPECIFIED = 0;

    // Route based VPN.
    ROUTE_BASED = 1;

    // Policy based routing.
    POLICY_BASED = 2;

    // Dynamic (BGP) routing.
    DYNAMIC = 3;
  }

  // Name of a VPN tunnel.
  string display_name = 1;

  // URI of a VPN tunnel.
  string uri = 2;

  // URI of the VPN gateway at local end of the tunnel.
  string source_gateway = 3;

  // URI of a VPN gateway at remote end of the tunnel.
  string remote_gateway = 4;

  // Remote VPN gateway's IP address.
  string remote_gateway_ip = 5;

  // Local VPN gateway's IP address.
  string source_gateway_ip = 6;

  // URI of a Compute Engine network where the VPN tunnel is configured.
  string network_uri = 7;

  // Name of a Google Cloud region where this VPN tunnel is configured.
  string region = 8;

  // Type of the routing policy.
  RoutingType routing_type = 9;
}

// For display only. The specification of the endpoints for the test.
// EndpointInfo is derived from source and destination Endpoint and validated
// by the backend data plane model.
message EndpointInfo {
  // Source IP address.
  string source_ip = 1;

  // Destination IP address.
  string destination_ip = 2;

  // IP protocol in string format, for example: "TCP", "UDP", "ICMP".
  string protocol = 3;

  // Source port. Only valid when protocol is TCP or UDP.
  int32 source_port = 4;

  // Destination port. Only valid when protocol is TCP or UDP.
  int32 destination_port = 5;

  // URI of the network where this packet originates from.
  string source_network_uri = 6;

  // URI of the network where this packet is sent to.
  string destination_network_uri = 7;

  // URI of the source telemetry agent this packet originates from.
  string source_agent_uri = 8;
}

// Details of the final state "deliver" and associated resource.
message DeliverInfo {
  // Deliver target types:
  enum Target {
    // Target not specified.
    TARGET_UNSPECIFIED = 0;

    // Target is a Compute Engine instance.
    INSTANCE = 1;

    // Target is the internet.
    INTERNET = 2;

    // Target is a Google API.
    GOOGLE_API = 3;

    // Target is a Google Kubernetes Engine cluster master.
    GKE_MASTER = 4;

    // Target is a Cloud SQL instance.
    CLOUD_SQL_INSTANCE = 5;

    // Target is a published service that uses [Private Service
    // Connect](https://cloud.google.com/vpc/docs/configure-private-service-connect-services).
    PSC_PUBLISHED_SERVICE = 6;

    // Target is Google APIs that use [Private Service
    // Connect](https://cloud.google.com/vpc/docs/configure-private-service-connect-apis).
    PSC_GOOGLE_API = 7;

    // Target is a VPC-SC that uses [Private Service
    // Connect](https://cloud.google.com/vpc/docs/configure-private-service-connect-apis).
    PSC_VPC_SC = 8;

    // Target is a serverless network endpoint group.
    SERVERLESS_NEG = 9;

    // Target is a Cloud Storage bucket.
    STORAGE_BUCKET = 10;

    // Target is a private network. Used only for return traces.
    PRIVATE_NETWORK = 11;

    // Target is a Cloud Function. Used only for return traces.
    CLOUD_FUNCTION = 12;

    // Target is a App Engine service version. Used only for return traces.
    APP_ENGINE_VERSION = 13;

    // Target is a Cloud Run revision. Used only for return traces.
    CLOUD_RUN_REVISION = 14;

    // Target is a Google-managed service. Used only for return traces.
    GOOGLE_MANAGED_SERVICE = 15;

    // Target is a Redis Instance.
    REDIS_INSTANCE = 16;

    // Target is a Redis Cluster.
    REDIS_CLUSTER = 17;
  }

  // Target type where the packet is delivered to.
  Target target = 1;

  // URI of the resource that the packet is delivered to.
  string resource_uri = 2;

  // IP address of the target (if applicable).
  string ip_address = 3 [(google.api.field_info).format = IPV4_OR_IPV6];

  // Name of the Cloud Storage Bucket the packet is delivered to (if
  // applicable).
  string storage_bucket = 4;

  // PSC Google API target the packet is delivered to (if applicable).
  string psc_google_api_target = 5;
}

// Details of the final state "forward" and associated resource.
message ForwardInfo {
  // Forward target types.
  enum Target {
    // Target not specified.
    TARGET_UNSPECIFIED = 0;

    // Forwarded to a VPC peering network.
    PEERING_VPC = 1;

    // Forwarded to a Cloud VPN gateway.
    VPN_GATEWAY = 2;

    // Forwarded to a Cloud Interconnect connection.
    INTERCONNECT = 3;

    // Forwarded to a Google Kubernetes Engine Container cluster master.
    GKE_MASTER = 4 [deprecated = true];

    // Forwarded to the next hop of a custom route imported from a peering VPC.
    IMPORTED_CUSTOM_ROUTE_NEXT_HOP = 5;

    // Forwarded to a Cloud SQL instance.
    CLOUD_SQL_INSTANCE = 6 [deprecated = true];

    // Forwarded to a VPC network in another project.
    ANOTHER_PROJECT = 7;

    // Forwarded to an NCC Hub.
    NCC_HUB = 8;

    // Forwarded to a router appliance.
    ROUTER_APPLIANCE = 9;
  }

  // Target type where this packet is forwarded to.
  Target target = 1;

  // URI of the resource that the packet is forwarded to.
  string resource_uri = 2;

  // IP address of the target (if applicable).
  string ip_address = 3 [(google.api.field_info).format = IPV4_OR_IPV6];
}

// Details of the final state "abort" and associated resource.
message AbortInfo {
  // Abort cause types:
  enum Cause {
    // Cause is unspecified.
    CAUSE_UNSPECIFIED = 0;

    // Aborted due to unknown network. Deprecated, not used in the new tests.
    UNKNOWN_NETWORK = 1 [deprecated = true];

    // Aborted because no project information can be derived from the test
    // input. Deprecated, not used in the new tests.
    UNKNOWN_PROJECT = 3 [deprecated = true];

    // Aborted because traffic is sent from a public IP to an instance without
    // an external IP. Deprecated, not used in the new tests.
    NO_EXTERNAL_IP = 7 [deprecated = true];

    // Aborted because none of the traces matches destination information
    // specified in the input test request. Deprecated, not used in the new
    // tests.
    UNINTENDED_DESTINATION = 8 [deprecated = true];

    // Aborted because the source endpoint could not be found. Deprecated, not
    // used in the new tests.
    SOURCE_ENDPOINT_NOT_FOUND = 11 [deprecated = true];

    // Aborted because the source network does not match the source endpoint.
    // Deprecated, not used in the new tests.
    MISMATCHED_SOURCE_NETWORK = 12 [deprecated = true];

    // Aborted because the destination endpoint could not be found. Deprecated,
    // not used in the new tests.
    DESTINATION_ENDPOINT_NOT_FOUND = 13 [deprecated = true];

    // Aborted because the destination network does not match the destination
    // endpoint. Deprecated, not used in the new tests.
    MISMATCHED_DESTINATION_NETWORK = 14 [deprecated = true];

    // Aborted because no endpoint with the packet's destination IP address is
    // found.
    UNKNOWN_IP = 2;

    // Aborted because no endpoint with the packet's destination IP is found in
    // the Google-managed project.
    GOOGLE_MANAGED_SERVICE_UNKNOWN_IP = 32;

    // Aborted because the source IP address doesn't belong to any of the
    // subnets of the source VPC network.
    SOURCE_IP_ADDRESS_NOT_IN_SOURCE_NETWORK = 23;

    // Aborted because user lacks permission to access all or part of the
    // network configurations required to run the test.
    PERMISSION_DENIED = 4;

    // Aborted because user lacks permission to access Cloud NAT configs
    // required to run the test.
    PERMISSION_DENIED_NO_CLOUD_NAT_CONFIGS = 28;

    // Aborted because user lacks permission to access Network endpoint group
    // endpoint configs required to run the test.
    PERMISSION_DENIED_NO_NEG_ENDPOINT_CONFIGS = 29;

    // Aborted because user lacks permission to access Cloud Router configs
    // required to run the test.
    PERMISSION_DENIED_NO_CLOUD_ROUTER_CONFIGS = 36;

    // Aborted because no valid source or destination endpoint is derived from
    // the input test request.
    NO_SOURCE_LOCATION = 5;

    // Aborted because the source or destination endpoint specified in
    // the request is invalid. Some examples:
    // - The request might contain malformed resource URI, project ID, or IP
    // address.
    // - The request might contain inconsistent information (for example, the
    // request might include both the instance and the network, but the instance
    // might not have a NIC in that network).
    INVALID_ARGUMENT = 6;

    // Aborted because the number of steps in the trace exceeds a certain
    // limit. It might be caused by a routing loop.
    TRACE_TOO_LONG = 9;

    // Aborted due to internal server error.
    INTERNAL_ERROR = 10;

    // Aborted because the test scenario is not supported.
    UNSUPPORTED = 15;

    // Aborted because the source and destination resources have no common IP
    // version.
    MISMATCHED_IP_VERSION = 16;

    // Aborted because the connection between the control plane and the node of
    // the source cluster is initiated by the node and managed by the
    // Konnectivity proxy.
    GKE_KONNECTIVITY_PROXY_UNSUPPORTED = 17;

    // Aborted because expected resource configuration was missing.
    RESOURCE_CONFIG_NOT_FOUND = 18;

    // Aborted because expected VM instance configuration was missing.
    VM_INSTANCE_CONFIG_NOT_FOUND = 24;

    // Aborted because expected network configuration was missing.
    NETWORK_CONFIG_NOT_FOUND = 25;

    // Aborted because expected firewall configuration was missing.
    FIREWALL_CONFIG_NOT_FOUND = 26;

    // Aborted because expected route configuration was missing.
    ROUTE_CONFIG_NOT_FOUND = 27;

    // Aborted because a PSC endpoint selection for the Google-managed service
    // is ambiguous (several PSC endpoints satisfy test input).
    GOOGLE_MANAGED_SERVICE_AMBIGUOUS_PSC_ENDPOINT = 19;

    // Aborted because tests with a PSC-based Cloud SQL instance as a source are
    // not supported.
    SOURCE_PSC_CLOUD_SQL_UNSUPPORTED = 20;

    // Aborted because tests with a Redis Cluster as a source are not supported.
    SOURCE_REDIS_CLUSTER_UNSUPPORTED = 34;

    // Aborted because tests with a Redis Instance as a source are not
    // supported.
    SOURCE_REDIS_INSTANCE_UNSUPPORTED = 35;

    // Aborted because tests with a forwarding rule as a source are not
    // supported.
    SOURCE_FORWARDING_RULE_UNSUPPORTED = 21;

    // Aborted because one of the endpoints is a non-routable IP address
    // (loopback, link-local, etc).
    NON_ROUTABLE_IP_ADDRESS = 22;

    // Aborted due to an unknown issue in the Google-managed project.
    UNKNOWN_ISSUE_IN_GOOGLE_MANAGED_PROJECT = 30;

    // Aborted due to an unsupported configuration of the Google-managed
    // project.
    UNSUPPORTED_GOOGLE_MANAGED_PROJECT_CONFIG = 31;
  }

  // Causes that the analysis is aborted.
  Cause cause = 1;

  // URI of the resource that caused the abort.
  string resource_uri = 2;

  // IP address that caused the abort.
  string ip_address = 4 [(google.api.field_info).format = IPV4_OR_IPV6];

  // List of project IDs the user specified in the request but lacks access to.
  // In this case, analysis is aborted with the PERMISSION_DENIED cause.
  repeated string projects_missing_permission = 3;
}

// Details of the final state "drop" and associated resource.
message DropInfo {
  // Drop cause types:
  enum Cause {
    // Cause is unspecified.
    CAUSE_UNSPECIFIED = 0;

    // Destination external address cannot be resolved to a known target. If
    // the address is used in a Google Cloud project, provide the project ID
    // as test input.
    UNKNOWN_EXTERNAL_ADDRESS = 1;

    // A Compute Engine instance can only send or receive a packet with a
    // foreign IP address if ip_forward is enabled.
    FOREIGN_IP_DISALLOWED = 2;

    // Dropped due to a firewall rule, unless allowed due to connection
    // tracking.
    FIREWALL_RULE = 3;

    // Dropped due to no matching routes.
    NO_ROUTE = 4;

    // Dropped due to invalid route. Route's next hop is a blackhole.
    ROUTE_BLACKHOLE = 5;

    // Packet is sent to a wrong (unintended) network. Example: you trace a
    // packet from VM1:Network1 to VM2:Network2, however, the route configured
    // in Network1 sends the packet destined for VM2's IP address to Network3.
    ROUTE_WRONG_NETWORK = 6;

    // Route's next hop IP address cannot be resolved to a GCP resource.
    ROUTE_NEXT_HOP_IP_ADDRESS_NOT_RESOLVED = 42;

    // Route's next hop resource is not found.
    ROUTE_NEXT_HOP_RESOURCE_NOT_FOUND = 43;

    // Route's next hop instance doesn't have a NIC in the route's network.
    ROUTE_NEXT_HOP_INSTANCE_WRONG_NETWORK = 49;

    // Route's next hop IP address is not a primary IP address of the next hop
    // instance.
    ROUTE_NEXT_HOP_INSTANCE_NON_PRIMARY_IP = 50;

    // Route's next hop forwarding rule doesn't match next hop IP address.
    ROUTE_NEXT_HOP_FORWARDING_RULE_IP_MISMATCH = 51;

    // Route's next hop VPN tunnel is down (does not have valid IKE SAs).
    ROUTE_NEXT_HOP_VPN_TUNNEL_NOT_ESTABLISHED = 52;

    // Route's next hop forwarding rule type is invalid (it's not a forwarding
    // rule of the internal passthrough load balancer).
    ROUTE_NEXT_HOP_FORWARDING_RULE_TYPE_INVALID = 53;

    // Packet is sent from the Internet to the private IPv6 address.
    NO_ROUTE_FROM_INTERNET_TO_PRIVATE_IPV6_ADDRESS = 44;

    // The packet does not match a policy-based VPN tunnel local selector.
    VPN_TUNNEL_LOCAL_SELECTOR_MISMATCH = 45;

    // The packet does not match a policy-based VPN tunnel remote selector.
    VPN_TUNNEL_REMOTE_SELECTOR_MISMATCH = 46;

    // Packet with internal destination address sent to the internet gateway.
    PRIVATE_TRAFFIC_TO_INTERNET = 7;

    // Instance with only an internal IP address tries to access Google API and
    // services, but private Google access is not enabled in the subnet.
    PRIVATE_GOOGLE_ACCESS_DISALLOWED = 8;

    // Source endpoint tries to access Google API and services through the VPN
    // tunnel to another network, but Private Google Access needs to be enabled
    // in the source endpoint network.
    PRIVATE_GOOGLE_ACCESS_VIA_VPN_TUNNEL_UNSUPPORTED = 47;

    // Instance with only an internal IP address tries to access external hosts,
    // but Cloud NAT is not enabled in the subnet, unless special configurations
    // on a VM allow this connection.
    NO_EXTERNAL_ADDRESS = 9;

    // Destination internal address cannot be resolved to a known target. If
    // this is a shared VPC scenario, verify if the service project ID is
    // provided as test input. Otherwise, verify if the IP address is being
    // used in the project.
    UNKNOWN_INTERNAL_ADDRESS = 10;

    // Forwarding rule's protocol and ports do not match the packet header.
    FORWARDING_RULE_MISMATCH = 11;

    // Forwarding rule does not have backends configured.
    FORWARDING_RULE_NO_INSTANCES = 12;

    // Firewalls block the health check probes to the backends and cause
    // the backends to be unavailable for traffic from the load balancer.
    // For more details, see [Health check firewall
    // rules](https://cloud.google.com/load-balancing/docs/health-checks#firewall_rules).
    FIREWALL_BLOCKING_LOAD_BALANCER_BACKEND_HEALTH_CHECK = 13;

    // Packet is sent from or to a Compute Engine instance that is not in a
    // running state.
    INSTANCE_NOT_RUNNING = 14;

    // Packet sent from or to a GKE cluster that is not in running state.
    GKE_CLUSTER_NOT_RUNNING = 27;

    // Packet sent from or to a Cloud SQL instance that is not in running state.
    CLOUD_SQL_INSTANCE_NOT_RUNNING = 28;

    // Packet sent from or to a Redis Instance that is not in running state.
    REDIS_INSTANCE_NOT_RUNNING = 68;

    // Packet sent from or to a Redis Cluster that is not in running state.
    REDIS_CLUSTER_NOT_RUNNING = 69;

    // The type of traffic is blocked and the user cannot configure a firewall
    // rule to enable it. See [Always blocked
    // traffic](https://cloud.google.com/vpc/docs/firewalls#blockedtraffic) for
    // more details.
    TRAFFIC_TYPE_BLOCKED = 15;

    // Access to Google Kubernetes Engine cluster master's endpoint is not
    // authorized. See [Access to the cluster
    // endpoints](https://cloud.google.com/kubernetes-engine/docs/how-to/private-clusters#access_to_the_cluster_endpoints)
    // for more details.
    GKE_MASTER_UNAUTHORIZED_ACCESS = 16;

    // Access to the Cloud SQL instance endpoint is not authorized.
    // See [Authorizing with authorized
    // networks](https://cloud.google.com/sql/docs/mysql/authorize-networks) for
    // more details.
    CLOUD_SQL_INSTANCE_UNAUTHORIZED_ACCESS = 17;

    // Packet was dropped inside Google Kubernetes Engine Service.
    DROPPED_INSIDE_GKE_SERVICE = 18;

    // Packet was dropped inside Cloud SQL Service.
    DROPPED_INSIDE_CLOUD_SQL_SERVICE = 19;

    // Packet was dropped because there is no peering between the originating
    // network and the Google Managed Services Network.
    GOOGLE_MANAGED_SERVICE_NO_PEERING = 20;

    // Packet was dropped because the Google-managed service uses Private
    // Service Connect (PSC), but the PSC endpoint is not found in the project.
    GOOGLE_MANAGED_SERVICE_NO_PSC_ENDPOINT = 38;

    // Packet was dropped because the GKE cluster uses Private Service Connect
    // (PSC), but the PSC endpoint is not found in the project.
    GKE_PSC_ENDPOINT_MISSING = 36;

    // Packet was dropped because the Cloud SQL instance has neither a private
    // nor a public IP address.
    CLOUD_SQL_INSTANCE_NO_IP_ADDRESS = 21;

    // Packet was dropped because a GKE cluster private endpoint is
    // unreachable from a region different from the cluster's region.
    GKE_CONTROL_PLANE_REGION_MISMATCH = 30;

    // Packet sent from a public GKE cluster control plane to a private
    // IP address.
    PUBLIC_GKE_CONTROL_PLANE_TO_PRIVATE_DESTINATION = 31;

    // Packet was dropped because there is no route from a GKE cluster
    // control plane to a destination network.
    GKE_CONTROL_PLANE_NO_ROUTE = 32;

    // Packet sent from a Cloud SQL instance to an external IP address is not
    // allowed. The Cloud SQL instance is not configured to send packets to
    // external IP addresses.
    CLOUD_SQL_INSTANCE_NOT_CONFIGURED_FOR_EXTERNAL_TRAFFIC = 33;

    // Packet sent from a Cloud SQL instance with only a public IP address to a
    // private IP address.
    PUBLIC_CLOUD_SQL_INSTANCE_TO_PRIVATE_DESTINATION = 34;

    // Packet was dropped because there is no route from a Cloud SQL
    // instance to a destination network.
    CLOUD_SQL_INSTANCE_NO_ROUTE = 35;

    // Packet was dropped because the Cloud SQL instance requires all
    // connections to use Cloud SQL connectors and to target the Cloud SQL proxy
    // port (3307).
    CLOUD_SQL_CONNECTOR_REQUIRED = 63;

    // Packet could be dropped because the Cloud Function is not in an active
    // status.
    CLOUD_FUNCTION_NOT_ACTIVE = 22;

    // Packet could be dropped because no VPC connector is set.
    VPC_CONNECTOR_NOT_SET = 23;

    // Packet could be dropped because the VPC connector is not in a running
    // state.
    VPC_CONNECTOR_NOT_RUNNING = 24;

    // Packet could be dropped because the traffic from the serverless service
    // to the VPC connector is not allowed.
    VPC_CONNECTOR_SERVERLESS_TRAFFIC_BLOCKED = 60;

    // Packet could be dropped because the health check traffic to the VPC
    // connector is not allowed.
    VPC_CONNECTOR_HEALTH_CHECK_TRAFFIC_BLOCKED = 61;

    // Packet could be dropped because it was sent from a different region
    // to a regional forwarding without global access.
    FORWARDING_RULE_REGION_MISMATCH = 25;

    // The Private Service Connect endpoint is in a project that is not approved
    // to connect to the service.
    PSC_CONNECTION_NOT_ACCEPTED = 26;

    // The packet is sent to the Private Service Connect endpoint over the
    // peering, but [it's not
    // supported](https://cloud.google.com/vpc/docs/configure-private-service-connect-services#on-premises).
    PSC_ENDPOINT_ACCESSED_FROM_PEERED_NETWORK = 41;

    // The packet is sent to the Private Service Connect backend (network
    // endpoint group), but the producer PSC forwarding rule does not have
    // global access enabled.
    PSC_NEG_PRODUCER_ENDPOINT_NO_GLOBAL_ACCESS = 48;

    // The packet is sent to the Private Service Connect backend (network
    // endpoint group), but the producer PSC forwarding rule has multiple ports
    // specified.
    PSC_NEG_PRODUCER_FORWARDING_RULE_MULTIPLE_PORTS = 54;

    // The packet is sent to the Private Service Connect backend (network
    // endpoint group) targeting a Cloud SQL service attachment, but this
    // configuration is not supported.
    CLOUD_SQL_PSC_NEG_UNSUPPORTED = 58;

    // No NAT subnets are defined for the PSC service attachment.
    NO_NAT_SUBNETS_FOR_PSC_SERVICE_ATTACHMENT = 57;

    // PSC endpoint is accessed via NCC, but PSC transitivity configuration is
    // not yet propagated.
    PSC_TRANSITIVITY_NOT_PROPAGATED = 64;

    // The packet sent from the hybrid NEG proxy matches a non-dynamic route,
    // but such a configuration is not supported.
    HYBRID_NEG_NON_DYNAMIC_ROUTE_MATCHED = 55;

    // The packet sent from the hybrid NEG proxy matches a dynamic route with a
    // next hop in a different region, but such a configuration is not
    // supported.
    HYBRID_NEG_NON_LOCAL_DYNAMIC_ROUTE_MATCHED = 56;

    // Packet sent from a Cloud Run revision that is not ready.
    CLOUD_RUN_REVISION_NOT_READY = 29;

    // Packet was dropped inside Private Service Connect service producer.
    DROPPED_INSIDE_PSC_SERVICE_PRODUCER = 37;

    // Packet sent to a load balancer, which requires a proxy-only subnet and
    // the subnet is not found.
    LOAD_BALANCER_HAS_NO_PROXY_SUBNET = 39;

    // Packet sent to Cloud Nat without active NAT IPs.
    CLOUD_NAT_NO_ADDRESSES = 40;

    // Packet is stuck in a routing loop.
    ROUTING_LOOP = 59;

    // Packet is dropped inside a Google-managed service due to being delivered
    // in return trace to an endpoint that doesn't match the endpoint the packet
    // was sent from in forward trace. Used only for return traces.
    DROPPED_INSIDE_GOOGLE_MANAGED_SERVICE = 62;

    // Packet is dropped due to a load balancer backend instance not having a
    // network interface in the network expected by the load balancer.
    LOAD_BALANCER_BACKEND_INVALID_NETWORK = 65;

    // Packet is dropped due to a backend service named port not being defined
    // on the instance group level.
    BACKEND_SERVICE_NAMED_PORT_NOT_DEFINED = 66;

    // Packet is dropped due to a destination IP range being part of a Private
    // NAT IP range.
    DESTINATION_IS_PRIVATE_NAT_IP_RANGE = 67;

    // Generic drop cause for a packet being dropped inside a Redis Instance
    // service project.
    DROPPED_INSIDE_REDIS_INSTANCE_SERVICE = 70;

    // Packet is dropped due to an unsupported port being used to connect to a
    // Redis Instance. Port 6379 should be used to connect to a Redis Instance.
    REDIS_INSTANCE_UNSUPPORTED_PORT = 71;

    // Packet is dropped due to connecting from PUPI address to a PSA based
    // Redis Instance.
    REDIS_INSTANCE_CONNECTING_FROM_PUPI_ADDRESS = 72;

    // Packet is dropped due to no route to the destination network.
    REDIS_INSTANCE_NO_ROUTE_TO_DESTINATION_NETWORK = 73;

    // Redis Instance does not have an external IP address.
    REDIS_INSTANCE_NO_EXTERNAL_IP = 74;

    // Packet is dropped due to an unsupported protocol being used to connect to
    // a Redis Instance. Only TCP connections are accepted by a Redis Instance.
    REDIS_INSTANCE_UNSUPPORTED_PROTOCOL = 78;

    // Generic drop cause for a packet being dropped inside a Redis Cluster
    // service project.
    DROPPED_INSIDE_REDIS_CLUSTER_SERVICE = 75;

    // Packet is dropped due to an unsupported port being used to connect to a
    // Redis Cluster. Ports 6379 and 11000 to 13047 should be used to connect to
    // a Redis Cluster.
    REDIS_CLUSTER_UNSUPPORTED_PORT = 76;

    // Redis Cluster does not have an external IP address.
    REDIS_CLUSTER_NO_EXTERNAL_IP = 77;

    // Packet is dropped due to an unsupported protocol being used to connect to
    // a Redis Cluster. Only TCP connections are accepted by a Redis Cluster.
    REDIS_CLUSTER_UNSUPPORTED_PROTOCOL = 79;

    // Packet from the non-GCP (on-prem) or unknown GCP network is dropped due
    // to the destination IP address not belonging to any IP prefix advertised
    // via BGP by the Cloud Router.
    NO_ADVERTISED_ROUTE_TO_GCP_DESTINATION = 80;

    // Packet from the non-GCP (on-prem) or unknown GCP network is dropped due
    // to the destination IP address not belonging to any IP prefix included to
    // the local traffic selector of the VPN tunnel.
    NO_TRAFFIC_SELECTOR_TO_GCP_DESTINATION = 81;

    // Packet from the unknown peered network is dropped due to no known route
    // from the source network to the destination IP address.
    NO_KNOWN_ROUTE_FROM_PEERED_NETWORK_TO_DESTINATION = 82;

    // Sending packets processed by the Private NAT Gateways to the Private
    // Service Connect endpoints is not supported.
    PRIVATE_NAT_TO_PSC_ENDPOINT_UNSUPPORTED = 83;
  }

  // Cause that the packet is dropped.
  Cause cause = 1;

  // URI of the resource that caused the drop.
  string resource_uri = 2;

  // Source IP address of the dropped packet (if relevant).
  string source_ip = 3;

  // Destination IP address of the dropped packet (if relevant).
  string destination_ip = 4;

  // Region of the dropped packet (if relevant).
  string region = 5;
}

// For display only. Metadata associated with a Google Kubernetes Engine (GKE)
// cluster master.
message GKEMasterInfo {
  // URI of a GKE cluster.
  string cluster_uri = 2;

  // URI of a GKE cluster network.
  string cluster_network_uri = 4;

  // Internal IP address of a GKE cluster control plane.
  string internal_ip = 5;

  // External IP address of a GKE cluster control plane.
  string external_ip = 6;

  // DNS endpoint of a GKE cluster control plane.
  string dns_endpoint = 7;
}

// For display only. Metadata associated with a Cloud SQL instance.
message CloudSQLInstanceInfo {
  // Name of a Cloud SQL instance.
  string display_name = 1;

  // URI of a Cloud SQL instance.
  string uri = 2;

  // URI of a Cloud SQL instance network or empty string if the instance does
  // not have one.
  string network_uri = 4;

  // Internal IP address of a Cloud SQL instance.
  string internal_ip = 5;

  // External IP address of a Cloud SQL instance.
  string external_ip = 6;

  // Region in which the Cloud SQL instance is running.
  string region = 7;
}

// For display only. Metadata associated with a Cloud Redis Instance.
message RedisInstanceInfo {
  // Name of a Cloud Redis Instance.
  string display_name = 1;

  // URI of a Cloud Redis Instance.
  string uri = 2;

  // URI of a Cloud Redis Instance network.
  string network_uri = 3;

  // Primary endpoint IP address of a Cloud Redis Instance.
  string primary_endpoint_ip = 4;

  // Read endpoint IP address of a Cloud Redis Instance (if applicable).
  string read_endpoint_ip = 5;

  // Region in which the Cloud Redis Instance is defined.
  string region = 6;
}

// For display only. Metadata associated with a Redis Cluster.
message RedisClusterInfo {
  // Name of a Redis Cluster.
  string display_name = 1;

  // URI of a Redis Cluster in format
  // "projects/{project_id}/locations/{location}/clusters/{cluster_id}"
  string uri = 2;

  // URI of a Redis Cluster network in format
  // "projects/{project_id}/global/networks/{network_id}".
  string network_uri = 3;

  // Discovery endpoint IP address of a Redis Cluster.
  string discovery_endpoint_ip_address = 4
      [(google.api.field_info).format = IPV4_OR_IPV6];

  // Secondary endpoint IP address of a Redis Cluster.
  string secondary_endpoint_ip_address = 5
      [(google.api.field_info).format = IPV4_OR_IPV6];

  // Name of the region in which the Redis Cluster is defined. For example,
  // "us-central1".
  string location = 6;
}

// For display only. Metadata associated with a Cloud Function.
message CloudFunctionInfo {
  // Name of a Cloud Function.
  string display_name = 1;

  // URI of a Cloud Function.
  string uri = 2;

  // Location in which the Cloud Function is deployed.
  string location = 3;

  // Latest successfully deployed version id of the Cloud Function.
  int64 version_id = 4;
}

// For display only. Metadata associated with a Cloud Run revision.
message CloudRunRevisionInfo {
  // Name of a Cloud Run revision.
  string display_name = 1;

  // URI of a Cloud Run revision.
  string uri = 2;

  // Location in which this revision is deployed.
  string location = 4;

  // URI of Cloud Run service this revision belongs to.
  string service_uri = 5;
}

// For display only. Metadata associated with an App Engine version.
message AppEngineVersionInfo {
  // Name of an App Engine version.
  string display_name = 1;

  // URI of an App Engine version.
  string uri = 2;

  // Runtime of the App Engine version.
  string runtime = 3;

  // App Engine execution environment for a version.
  string environment = 4;
}

// For display only. Metadata associated with a VPC connector.
message VpcConnectorInfo {
  // Name of a VPC connector.
  string display_name = 1;

  // URI of a VPC connector.
  string uri = 2;

  // Location in which the VPC connector is deployed.
  string location = 3;
}

// For display only. Metadata associated with NAT.
message NatInfo {
  // Types of NAT.
  enum Type {
    // Type is unspecified.
    TYPE_UNSPECIFIED = 0;

    // From Compute Engine instance's internal address to external address.
    INTERNAL_TO_EXTERNAL = 1;

    // From Compute Engine instance's external address to internal address.
    EXTERNAL_TO_INTERNAL = 2;

    // Cloud NAT Gateway.
    CLOUD_NAT = 3;

    // Private service connect NAT.
    PRIVATE_SERVICE_CONNECT = 4;
  }

  // Type of NAT.
  Type type = 1;

  // IP protocol in string format, for example: "TCP", "UDP", "ICMP".
  string protocol = 2;

  // URI of the network where NAT translation takes place.
  string network_uri = 3;

  // Source IP address before NAT translation.
  string old_source_ip = 4;

  // Source IP address after NAT translation.
  string new_source_ip = 5;

  // Destination IP address before NAT translation.
  string old_destination_ip = 6;

  // Destination IP address after NAT translation.
  string new_destination_ip = 7;

  // Source port before NAT translation. Only valid when protocol is TCP or UDP.
  int32 old_source_port = 8;

  // Source port after NAT translation. Only valid when protocol is TCP or UDP.
  int32 new_source_port = 9;

  // Destination port before NAT translation. Only valid when protocol is TCP or
  // UDP.
  int32 old_destination_port = 10;

  // Destination port after NAT translation. Only valid when protocol is TCP or
  // UDP.
  int32 new_destination_port = 11;

  // Uri of the Cloud Router. Only valid when type is CLOUD_NAT.
  string router_uri = 12;

  // The name of Cloud NAT Gateway. Only valid when type is CLOUD_NAT.
  string nat_gateway_name = 13;
}

// For display only. Metadata associated with ProxyConnection.
message ProxyConnectionInfo {
  // IP protocol in string format, for example: "TCP", "UDP", "ICMP".
  string protocol = 1;

  // Source IP address of an original connection.
  string old_source_ip = 2;

  // Source IP address of a new connection.
  string new_source_ip = 3;

  // Destination IP address of an original connection
  string old_destination_ip = 4;

  // Destination IP address of a new connection.
  string new_destination_ip = 5;

  // Source port of an original connection. Only valid when protocol is TCP or
  // UDP.
  int32 old_source_port = 6;

  // Source port of a new connection. Only valid when protocol is TCP or UDP.
  int32 new_source_port = 7;

  // Destination port of an original connection. Only valid when protocol is TCP
  // or UDP.
  int32 old_destination_port = 8;

  // Destination port of a new connection. Only valid when protocol is TCP or
  // UDP.
  int32 new_destination_port = 9;

  // Uri of proxy subnet.
  string subnet_uri = 10;

  // URI of the network where connection is proxied.
  string network_uri = 11;
}

// For display only. Metadata associated with the load balancer backend.
message LoadBalancerBackendInfo {
  // Health check firewalls configuration state enum.
  enum HealthCheckFirewallsConfigState {
    // Configuration state unspecified. It usually means that the backend has
    // no health check attached, or there was an unexpected configuration error
    // preventing Connectivity tests from verifying health check configuration.
    HEALTH_CHECK_FIREWALLS_CONFIG_STATE_UNSPECIFIED = 0;

    // Firewall rules (policies) allowing health check traffic from all required
    // IP ranges to the backend are configured.
    FIREWALLS_CONFIGURED = 1;

    // Firewall rules (policies) allow health check traffic only from a part of
    // required IP ranges.
    FIREWALLS_PARTIALLY_CONFIGURED = 2;

    // Firewall rules (policies) deny health check traffic from all required
    // IP ranges to the backend.
    FIREWALLS_NOT_CONFIGURED = 3;

    // The network contains firewall rules of unsupported types, so Connectivity
    // tests were not able to verify health check configuration status. Please
    // refer to the documentation for the list of unsupported configurations:
    // https://cloud.google.com/network-intelligence-center/docs/connectivity-tests/concepts/overview#unsupported-configs
    FIREWALLS_UNSUPPORTED = 4;
  }

  // Display name of the backend. For example, it might be an instance name for
  // the instance group backends, or an IP address and port for zonal network
  // endpoint group backends.
  string name = 1;

  // URI of the backend instance (if applicable). Populated for instance group
  // backends, and zonal NEG backends.
  string instance_uri = 2;

  // URI of the backend service this backend belongs to (if applicable).
  string backend_service_uri = 3;

  // URI of the instance group this backend belongs to (if applicable).
  string instance_group_uri = 4;

  // URI of the network endpoint group this backend belongs to (if applicable).
  string network_endpoint_group_uri = 5;

  // URI of the backend bucket this backend targets (if applicable).
  string backend_bucket_uri = 8;

  // URI of the PSC service attachment this PSC NEG backend targets (if
  // applicable).
  string psc_service_attachment_uri = 9;

  // PSC Google API target this PSC NEG backend targets (if applicable).
  string psc_google_api_target = 10;

  // URI of the health check attached to this backend (if applicable).
  string health_check_uri = 6;

  // Output only. Health check firewalls configuration state for the backend.
  // This is a result of the static firewall analysis (verifying that health
  // check traffic from required IP ranges to the backend is allowed or not).
  // The backend might still be unhealthy even if these firewalls are
  // configured. Please refer to the documentation for more information:
  // https://cloud.google.com/load-balancing/docs/firewall-rules
  HealthCheckFirewallsConfigState health_check_firewalls_config_state = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Type of a load balancer. For more information, see [Summary of Google Cloud
// load
// balancers](https://cloud.google.com/load-balancing/docs/load-balancing-overview#summary-of-google-cloud-load-balancers).
enum LoadBalancerType {
  // Forwarding rule points to a different target than a load balancer or a
  // load balancer type is unknown.
  LOAD_BALANCER_TYPE_UNSPECIFIED = 0;

  // Global external HTTP(S) load balancer.
  HTTPS_ADVANCED_LOAD_BALANCER = 1;

  // Global external HTTP(S) load balancer (classic)
  HTTPS_LOAD_BALANCER = 2;

  // Regional external HTTP(S) load balancer.
  REGIONAL_HTTPS_LOAD_BALANCER = 3;

  // Internal HTTP(S) load balancer.
  INTERNAL_HTTPS_LOAD_BALANCER = 4;

  // External SSL proxy load balancer.
  SSL_PROXY_LOAD_BALANCER = 5;

  // External TCP proxy load balancer.
  TCP_PROXY_LOAD_BALANCER = 6;

  // Internal regional TCP proxy load balancer.
  INTERNAL_TCP_PROXY_LOAD_BALANCER = 7;

  // External TCP/UDP Network load balancer.
  NETWORK_LOAD_BALANCER = 8;

  // Target-pool based external TCP/UDP Network load balancer.
  LEGACY_NETWORK_LOAD_BALANCER = 9;

  // Internal TCP/UDP load balancer.
  TCP_UDP_INTERNAL_LOAD_BALANCER = 10;
}

// For display only. Metadata associated with Storage Bucket.
message StorageBucketInfo {
  // Cloud Storage Bucket name.
  string bucket = 1;
}

// For display only. Metadata associated with the serverless network endpoint
// group backend.
message ServerlessNegInfo {
  // URI of the serverless network endpoint group.
  string neg_uri = 1;
}
