// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkmanagement.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/networkmanagement/v1beta1/trace.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.NetworkManagement.V1Beta1";
option go_package = "cloud.google.com/go/networkmanagement/apiv1beta1/networkmanagementpb;networkmanagementpb";
option java_multiple_files = true;
option java_outer_classname = "TestOuterClass";
option java_package = "com.google.cloud.networkmanagement.v1beta1";
option php_namespace = "Google\\Cloud\\NetworkManagement\\V1beta1";
option ruby_package = "Google::Cloud::NetworkManagement::V1beta1";

// A Connectivity Test for a network reachability analysis.
message ConnectivityTest {
  option (google.api.resource) = {
    type: "networkmanagement.googleapis.com/ConnectivityTest"
    pattern: "projects/{project}/locations/global/connectivityTests/{test}"
  };

  // Identifier. Unique name of the resource using the form:
  //     `projects/{project_id}/locations/global/connectivityTests/{test}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The user-supplied description of the Connectivity Test.
  // Maximum of 512 characters.
  string description = 2;

  // Required. Source specification of the Connectivity Test.
  //
  // You can use a combination of source IP address, virtual machine
  // (VM) instance, or Compute Engine network to uniquely identify
  // the source location.
  //
  // Examples:
  // If the source IP address is an internal IP address within a Google Cloud
  // Virtual Private Cloud (VPC) network, then you must also specify the VPC
  // network. Otherwise, specify the VM instance, which already contains its
  // internal IP address and VPC network information.
  //
  // If the source of the test is within an on-premises network, then you must
  // provide the destination VPC network.
  //
  // If the source endpoint is a Compute Engine VM instance with multiple
  // network interfaces, the instance itself is not sufficient to identify the
  // endpoint. So, you must also specify the source IP address or VPC network.
  //
  // A reachability analysis proceeds even if the source location is
  // ambiguous. However, the test result may include endpoints that you don't
  // intend to test.
  Endpoint source = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Destination specification of the Connectivity Test.
  //
  // You can use a combination of destination IP address, Compute Engine
  // VM instance, or VPC network to uniquely identify the destination
  // location.
  //
  // Even if the destination IP address is not unique, the source IP
  // location is unique. Usually, the analysis can infer the destination
  // endpoint from route information.
  //
  // If the destination you specify is a VM instance and the instance has
  // multiple network interfaces, then you must also specify either
  // a destination IP address  or VPC network to identify the destination
  // interface.
  //
  // A reachability analysis proceeds even if the destination location is
  // ambiguous. However, the result can include endpoints that you don't
  // intend to test.
  Endpoint destination = 4 [(google.api.field_behavior) = REQUIRED];

  // IP Protocol of the test. When not provided, "TCP" is assumed.
  string protocol = 5;

  // Other projects that may be relevant for reachability analysis.
  // This is applicable to scenarios where a test can cross project boundaries.
  repeated string related_projects = 6;

  // Output only. The display name of a Connectivity Test.
  string display_name = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource labels to represent user-provided metadata.
  map<string, string> labels = 8;

  // Output only. The time the test was created.
  google.protobuf.Timestamp create_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the test's configuration was updated.
  google.protobuf.Timestamp update_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The reachability details of this test from the latest run.
  // The details are updated when creating a new test, updating an
  // existing test, or triggering a one-time rerun of an existing test.
  ReachabilityDetails reachability_details = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The probing details of this test from the latest run, present
  // for applicable tests only. The details are updated when creating a new
  // test, updating an existing test, or triggering a one-time rerun of an
  // existing test.
  ProbingDetails probing_details = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether run analysis for the return path from destination to source.
  // Default value is false.
  bool round_trip = 15;

  // Output only. The reachability details of this test from the latest run for
  // the return path. The details are updated when creating a new test,
  // updating an existing test, or triggering a one-time rerun of an existing
  // test.
  ReachabilityDetails return_reachability_details = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether the test should skip firewall checking.
  // If not provided, we assume false.
  bool bypass_firewall_checks = 17;
}

// Source or destination of the Connectivity Test.
message Endpoint {
  // The type definition of an endpoint's network. Use one of the
  // following choices:
  enum NetworkType {
    // Default type if unspecified.
    NETWORK_TYPE_UNSPECIFIED = 0;

    // A network hosted within Google Cloud.
    // To receive more detailed output, specify the URI for the source or
    // destination network.
    GCP_NETWORK = 1;

    // A network hosted outside of Google Cloud.
    // This can be an on-premises network, or a network hosted by another cloud
    // provider.
    NON_GCP_NETWORK = 2;
  }

  // Type of the target of a forwarding rule.
  enum ForwardingRuleTarget {
    // Forwarding rule target is unknown.
    FORWARDING_RULE_TARGET_UNSPECIFIED = 0;

    // Compute Engine instance for protocol forwarding.
    INSTANCE = 1;

    // Load Balancer. The specific type can be found from [load_balancer_type]
    // [google.cloud.networkmanagement.v1beta1.Endpoint.load_balancer_type].
    LOAD_BALANCER = 2;

    // Classic Cloud VPN Gateway.
    VPN_GATEWAY = 3;

    // Forwarding Rule is a Private Service Connect endpoint.
    PSC = 4;
  }

  // Wrapper for Cloud Function attributes.
  message CloudFunctionEndpoint {
    // A [Cloud Function](https://cloud.google.com/functions) name.
    string uri = 1;
  }

  // Wrapper for the App Engine service version attributes.
  message AppEngineVersionEndpoint {
    // An [App Engine](https://cloud.google.com/appengine) [service
    // version](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1/apps.services.versions)
    // name.
    string uri = 1;
  }

  // Wrapper for Cloud Run revision attributes.
  message CloudRunRevisionEndpoint {
    // A [Cloud Run](https://cloud.google.com/run)
    // [revision](https://cloud.google.com/run/docs/reference/rest/v1/namespaces.revisions/get)
    // URI. The format is:
    // projects/{project}/locations/{location}/revisions/{revision}
    string uri = 1;
  }

  // The IP address of the endpoint, which can be an external or internal IP.
  string ip_address = 1;

  // The IP protocol port of the endpoint.
  // Only applicable when protocol is TCP or UDP.
  int32 port = 2;

  // A Compute Engine instance URI.
  string instance = 3;

  // A forwarding rule and its corresponding IP address represent the frontend
  // configuration of a Google Cloud load balancer. Forwarding rules are also
  // used for protocol forwarding, Private Service Connect and other network
  // services to provide forwarding information in the control plane. Format:
  //  projects/{project}/global/forwardingRules/{id} or
  //  projects/{project}/regions/{region}/forwardingRules/{id}
  string forwarding_rule = 13;

  // Output only. Specifies the type of the target of the forwarding rule.
  optional ForwardingRuleTarget forwarding_rule_target = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. ID of the load balancer the forwarding rule points to. Empty
  // for forwarding rules not related to load balancers.
  optional string load_balancer_id = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the load balancer the forwarding rule points to.
  optional LoadBalancerType load_balancer_type = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // A cluster URI for [Google Kubernetes Engine cluster control
  // plane](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-architecture).
  string gke_master_cluster = 7;

  // DNS endpoint of [Google Kubernetes Engine cluster control
  // plane](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-architecture).
  // Requires gke_master_cluster to be set, can't be used simultaneoulsly with
  // ip_address or network. Applicable only to destination endpoint.
  string fqdn = 19;

  // A [Cloud SQL](https://cloud.google.com/sql) instance URI.
  string cloud_sql_instance = 8;

  // A [Redis Instance](https://cloud.google.com/memorystore/docs/redis)
  // URI.
  string redis_instance = 17;

  // A [Redis Cluster](https://cloud.google.com/memorystore/docs/cluster)
  // URI.
  string redis_cluster = 18;

  // A [Cloud Function](https://cloud.google.com/functions).
  CloudFunctionEndpoint cloud_function = 10;

  // An [App Engine](https://cloud.google.com/appengine) [service
  // version](https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1/apps.services.versions).
  AppEngineVersionEndpoint app_engine_version = 11;

  // A [Cloud Run](https://cloud.google.com/run)
  // [revision](https://cloud.google.com/run/docs/reference/rest/v1/namespaces.revisions/get)
  CloudRunRevisionEndpoint cloud_run_revision = 12;

  // A Compute Engine network URI.
  string network = 4;

  // Type of the network where the endpoint is located.
  // Applicable only to source endpoint, as destination network type can be
  // inferred from the source.
  NetworkType network_type = 5;

  // Project ID where the endpoint is located.
  // The Project ID can be derived from the URI if you provide a VM instance or
  // network URI.
  // The following are two cases where you must provide the project ID:
  // 1. Only the IP address is specified, and the IP address is within a Google
  // Cloud project.
  // 2. When you are using Shared VPC and the IP address that you provide is
  // from the service project. In this case, the network that the IP address
  // resides in is defined in the host project.
  string project_id = 6;
}

// Results of the configuration analysis from the last run of the test.
message ReachabilityDetails {
  // The overall result of the test's configuration analysis.
  enum Result {
    // No result was specified.
    RESULT_UNSPECIFIED = 0;

    // Possible scenarios are:
    //
    // * The configuration analysis determined that a packet originating from
    //   the source is expected to reach the destination.
    // * The analysis didn't complete because the user lacks permission for
    //   some of the resources in the trace. However, at the time the user's
    //   permission became insufficient, the trace had been successful so far.
    REACHABLE = 1;

    // A packet originating from the source is expected to be dropped before
    // reaching the destination.
    UNREACHABLE = 2;

    // The source and destination endpoints do not uniquely identify
    // the test location in the network, and the reachability result contains
    // multiple traces. For some traces, a packet could be delivered, and for
    // others, it would not be. This result is also assigned to
    // configuration analysis of return path if on its own it should be
    // REACHABLE, but configuration analysis of forward path is AMBIGUOUS.
    AMBIGUOUS = 4;

    // The configuration analysis did not complete. Possible reasons are:
    //
    // * A permissions error occurred--for example, the user might not have
    //   read permission for all of the resources named in the test.
    // * An internal error occurred.
    // * The analyzer received an invalid or unsupported argument or was unable
    //   to identify a known endpoint.
    UNDETERMINED = 5;
  }

  // The overall result of the test's configuration analysis.
  Result result = 1;

  // The time of the configuration analysis.
  google.protobuf.Timestamp verify_time = 2;

  // The details of a failure or a cancellation of reachability analysis.
  google.rpc.Status error = 3;

  // Result may contain a list of traces if a test has multiple possible
  // paths in the network, such as when destination endpoint is a load balancer
  // with multiple backends.
  repeated Trace traces = 5;
}

// Latency percentile rank and value.
message LatencyPercentile {
  // Percentage of samples this data point applies to.
  int32 percent = 1;

  // percent-th percentile of latency observed, in microseconds.
  // Fraction of percent/100 of samples have latency lower or
  // equal to the value of this field.
  int64 latency_micros = 2;
}

// Describes measured latency distribution.
message LatencyDistribution {
  // Representative latency percentiles.
  repeated LatencyPercentile latency_percentiles = 1;
}

// Results of active probing from the last run of the test.
message ProbingDetails {
  // Overall probing result of the test.
  enum ProbingResult {
    // No result was specified.
    PROBING_RESULT_UNSPECIFIED = 0;

    // At least 95% of packets reached the destination.
    REACHABLE = 1;

    // No packets reached the destination.
    UNREACHABLE = 2;

    // Less than 95% of packets reached the destination.
    REACHABILITY_INCONSISTENT = 3;

    // Reachability could not be determined. Possible reasons are:
    // * The user lacks permission to access some of the network resources
    //   required to run the test.
    // * No valid source endpoint could be derived from the request.
    // * An internal error occurred.
    UNDETERMINED = 4;
  }

  // Abort cause types.
  enum ProbingAbortCause {
    // No reason was specified.
    PROBING_ABORT_CAUSE_UNSPECIFIED = 0;

    // The user lacks permission to access some of the
    // network resources required to run the test.
    PERMISSION_DENIED = 1;

    // No valid source endpoint could be derived from the request.
    NO_SOURCE_LOCATION = 2;
  }

  // Representation of a network edge location as per
  // https://cloud.google.com/vpc/docs/edge-locations.
  message EdgeLocation {
    // Name of the metropolitan area.
    string metropolitan_area = 1;
  }

  // The overall result of active probing.
  ProbingResult result = 1;

  // The time that reachability was assessed through active probing.
  google.protobuf.Timestamp verify_time = 2;

  // Details about an internal failure or the cancellation of active probing.
  google.rpc.Status error = 3;

  // The reason probing was aborted.
  ProbingAbortCause abort_cause = 4;

  // Number of probes sent.
  int32 sent_probe_count = 5;

  // Number of probes that reached the destination.
  int32 successful_probe_count = 6;

  // The source and destination endpoints derived from the test input and used
  // for active probing.
  EndpointInfo endpoint_info = 7;

  // Latency as measured by active probing in one direction:
  // from the source to the destination endpoint.
  LatencyDistribution probing_latency = 8;

  // The EdgeLocation from which a packet destined for/originating from the
  // internet will egress/ingress the Google network.
  // This will only be populated for a connectivity test which has an internet
  // destination/source address.
  // The absence of this field *must not* be used as an indication that the
  // destination/source is part of the Google network.
  EdgeLocation destination_egress_location = 9;
}
