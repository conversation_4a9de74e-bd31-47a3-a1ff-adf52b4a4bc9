// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkmanagement.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/networkmanagement/v1beta1/connectivity_test.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkManagement.V1Beta1";
option go_package = "cloud.google.com/go/networkmanagement/apiv1beta1/networkmanagementpb;networkmanagementpb";
option java_multiple_files = true;
option java_outer_classname = "ReachabilityServiceProto";
option java_package = "com.google.cloud.networkmanagement.v1beta1";
option php_namespace = "Google\\Cloud\\NetworkManagement\\V1beta1";
option ruby_package = "Google::Cloud::NetworkManagement::V1beta1";

// The Reachability service in Google Cloud Network Management API

// The Reachability service in the Google Cloud Network Management API provides
// services that analyze the reachability within a single Google Virtual Private
// Cloud (VPC) network, between peered VPC networks, between VPC and on-premises
// networks, or between VPC networks and internet hosts. A reachability analysis
// is based on Google Cloud network configurations.
//
// You can use the analysis results to verify these configurations and
// to troubleshoot connectivity issues.
service ReachabilityService {
  option (google.api.default_host) = "networkmanagement.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all Connectivity Tests owned by a project.
  rpc ListConnectivityTests(ListConnectivityTestsRequest)
      returns (ListConnectivityTestsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/global}/connectivityTests"
    };
  }

  // Gets the details of a specific Connectivity Test.
  rpc GetConnectivityTest(GetConnectivityTestRequest)
      returns (ConnectivityTest) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/global/connectivityTests/*}"
    };
  }

  // Creates a new Connectivity Test.
  // After you create a test, the reachability analysis is performed as part
  // of the long running operation, which completes when the analysis completes.
  //
  // If the endpoint specifications in `ConnectivityTest` are invalid
  // (for example, containing non-existent resources in the network, or you
  // don't have read permissions to the network configurations of listed
  // projects), then the reachability result returns a value of `UNKNOWN`.
  //
  // If the endpoint specifications in `ConnectivityTest` are
  // incomplete, the reachability result returns a value of
  // <code>AMBIGUOUS</code>. For more information,
  // see the Connectivity Test documentation.
  rpc CreateConnectivityTest(CreateConnectivityTestRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/global}/connectivityTests"
      body: "resource"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.networkmanagement.v1beta1.ConnectivityTest"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the configuration of an existing `ConnectivityTest`.
  // After you update a test, the reachability analysis is performed as part
  // of the long running operation, which completes when the analysis completes.
  // The Reachability state in the test resource is updated with the new result.
  //
  // If the endpoint specifications in `ConnectivityTest` are invalid
  // (for example, they contain non-existent resources in the network, or the
  // user does not have read permissions to the network configurations of
  // listed projects), then the reachability result returns a value of
  // <code>UNKNOWN</code>.
  //
  // If the endpoint specifications in `ConnectivityTest` are incomplete, the
  // reachability result returns a value of `AMBIGUOUS`. See the documentation
  // in `ConnectivityTest` for more details.
  rpc UpdateConnectivityTest(UpdateConnectivityTestRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{resource.name=projects/*/locations/global/connectivityTests/*}"
      body: "resource"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.networkmanagement.v1beta1.ConnectivityTest"
      metadata_type: "OperationMetadata"
    };
  }

  // Rerun an existing `ConnectivityTest`.
  // After the user triggers the rerun, the reachability analysis is performed
  // as part of the long running operation, which completes when the analysis
  // completes.
  //
  // Even though the test configuration remains the same, the reachability
  // result may change due to underlying network configuration changes.
  //
  // If the endpoint specifications in `ConnectivityTest` become invalid (for
  // example, specified resources are deleted in the network, or you lost
  // read permissions to the network configurations of listed projects), then
  // the reachability result returns a value of `UNKNOWN`.
  rpc RerunConnectivityTest(RerunConnectivityTestRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/global/connectivityTests/*}:rerun"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.networkmanagement.v1beta1.ConnectivityTest"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a specific `ConnectivityTest`.
  rpc DeleteConnectivityTest(DeleteConnectivityTestRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/global/connectivityTests/*}"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Request for the `ListConnectivityTests` method.
message ListConnectivityTestsRequest {
  // Required. The parent resource of the Connectivity Tests:
  //     `projects/{project_id}/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Number of `ConnectivityTests` to return.
  int32 page_size = 2;

  // Page token from an earlier query, as returned in `next_page_token`.
  string page_token = 3;

  // Lists the `ConnectivityTests` that match the filter expression. A filter
  // expression filters the resources listed in the response. The expression
  // must be of the form `<field> <operator> <value>` where operators: `<`, `>`,
  // `<=`,
  // `>=`,
  // `!=`, `=`, `:` are supported (colon `:` represents a HAS operator which is
  // roughly synonymous with equality). <field> can refer to a proto or JSON
  // field, or a synthetic field. Field names can be camelCase or snake_case.
  //
  // Examples:
  // - Filter by name:
  //   name = "projects/proj-1/locations/global/connectivityTests/test-1
  //
  // - Filter by labels:
  //   - Resources that have a key called `foo`
  //     labels.foo:*
  //   - Resources that have a key called `foo` whose value is `bar`
  //     labels.foo = bar
  string filter = 4;

  // Field to use to sort the list.
  string order_by = 5;
}

// Response for the `ListConnectivityTests` method.
message ListConnectivityTestsResponse {
  // List of Connectivity Tests.
  repeated ConnectivityTest resources = 1;

  // Page token to fetch the next set of Connectivity Tests.
  string next_page_token = 2;

  // Locations that could not be reached (when querying all locations with `-`).
  repeated string unreachable = 3;
}

// Request for the `GetConnectivityTest` method.
message GetConnectivityTestRequest {
  // Required. `ConnectivityTest` resource name using the form:
  //     `projects/{project_id}/locations/global/connectivityTests/{test_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkmanagement.googleapis.com/ConnectivityTest"
    }
  ];
}

// Request for the `CreateConnectivityTest` method.
message CreateConnectivityTestRequest {
  // Required. The parent resource of the Connectivity Test to create:
  //     `projects/{project_id}/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The logical name of the Connectivity Test in your project
  // with the following restrictions:
  //
  // * Must contain only lowercase letters, numbers, and hyphens.
  // * Must start with a letter.
  // * Must be between 1-40 characters.
  // * Must end with a number or a letter.
  // * Must be unique within the customer project
  string test_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A `ConnectivityTest` resource
  ConnectivityTest resource = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `UpdateConnectivityTest` method.
message UpdateConnectivityTestRequest {
  // Required. Mask of fields to update. At least one path must be supplied in
  // this field.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Only fields specified in update_mask are updated.
  ConnectivityTest resource = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `DeleteConnectivityTest` method.
message DeleteConnectivityTestRequest {
  // Required. Connectivity Test resource name using the form:
  //     `projects/{project_id}/locations/global/connectivityTests/{test_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkmanagement.googleapis.com/ConnectivityTest"
    }
  ];
}

// Request for the `RerunConnectivityTest` method.
message RerunConnectivityTestRequest {
  // Required. Connectivity Test resource name using the form:
  //     `projects/{project_id}/locations/global/connectivityTests/{test_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "networkmanagement.googleapis.com/ConnectivityTest"
    }
  ];
}

// Metadata describing an [Operation][google.longrunning.Operation]
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Target of the operation - for example
  // projects/project-1/locations/global/connectivityTests/test-1
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation, if any.
  string status_detail = 5;

  // Specifies if cancellation was requested for the operation.
  bool cancel_requested = 6;

  // API version.
  string api_version = 7;
}
