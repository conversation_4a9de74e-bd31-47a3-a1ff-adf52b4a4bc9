# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "networkmanagement_proto",
    srcs = [
        "connectivity_test.proto",
        "reachability.proto",
        "trace.proto",
        "vpc_flow_logs.proto",
        "vpc_flow_logs_config.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "networkmanagement_proto_with_info",
    deps = [
        ":networkmanagement_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "networkmanagement_java_proto",
    deps = [":networkmanagement_proto"],
)

java_grpc_library(
    name = "networkmanagement_java_grpc",
    srcs = [":networkmanagement_proto"],
    deps = [":networkmanagement_java_proto"],
)

java_gapic_library(
    name = "networkmanagement_java_gapic",
    srcs = [":networkmanagement_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "networkmanagement_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networkmanagement_v1beta1.yaml",
    test_deps = [
        ":networkmanagement_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":networkmanagement_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "networkmanagement_java_gapic_test_suite",
    test_classes = [
        # This test is temporarily disabled due to the issue:
        # https://github.com/googleapis/sdk-platform-java/issues/1839
        # "com.google.cloud.networkmanagement.v1beta1.ReachabilityServiceClientHttpJsonTest",
        "com.google.cloud.networkmanagement.v1beta1.ReachabilityServiceClientTest",
        # This test is temporarily disabled due to the issue:
        # https://github.com/googleapis/sdk-platform-java/issues/1839
        # "com.google.cloud.networkmanagement.v1beta1.VpcFlowLogsServiceClientHttpJsonTest",
        "com.google.cloud.networkmanagement.v1beta1.VpcFlowLogsServiceClientTest",
    ],
    runtime_deps = [":networkmanagement_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-networkmanagement-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":networkmanagement_java_gapic",
        ":networkmanagement_java_grpc",
        ":networkmanagement_java_proto",
        ":networkmanagement_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "networkmanagement_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/networkmanagement/apiv1beta1/networkmanagementpb",
    protos = [":networkmanagement_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "networkmanagement_go_gapic",
    srcs = [":networkmanagement_proto_with_info"],
    grpc_service_config = "networkmanagement_grpc_service_config.json",
    importpath = "cloud.google.com/go/networkmanagement/apiv1beta1;networkmanagement",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "networkmanagement_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networkmanagement_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-networkmanagement-v1beta1-go",
    deps = [
        ":networkmanagement_go_gapic",
        ":networkmanagement_go_gapic_srcjar-metadata.srcjar",
        ":networkmanagement_go_gapic_srcjar-snippets.srcjar",
        ":networkmanagement_go_gapic_srcjar-test.srcjar",
        ":networkmanagement_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "networkmanagement_py_gapic",
    srcs = [":networkmanagement_proto"],
    grpc_service_config = "networkmanagement_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networkmanagement_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "networkmanagement_py_gapic_test",
    srcs = [
        "networkmanagement_py_gapic_pytest.py",
        "networkmanagement_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":networkmanagement_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "networkmanagement-v1beta1-py",
    deps = [
        ":networkmanagement_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "networkmanagement_php_proto",
    deps = [":networkmanagement_proto"],
)

php_gapic_library(
    name = "networkmanagement_php_gapic",
    srcs = [":networkmanagement_proto_with_info"],
    grpc_service_config = "networkmanagement_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "networkmanagement_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networkmanagement_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-networkmanagement-v1beta1-php",
    deps = [
        ":networkmanagement_php_gapic",
        ":networkmanagement_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "networkmanagement_nodejs_gapic",
    package_name = "@google-cloud/network-management",
    src = ":networkmanagement_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "networkmanagement_grpc_service_config.json",
    package = "google.cloud.networkmanagement.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "networkmanagement_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "networkmanagement-v1beta1-nodejs",
    deps = [
        ":networkmanagement_nodejs_gapic",
        ":networkmanagement_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "networkmanagement_ruby_proto",
    deps = [":networkmanagement_proto"],
)

ruby_grpc_library(
    name = "networkmanagement_ruby_grpc",
    srcs = [":networkmanagement_proto"],
    deps = [":networkmanagement_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "networkmanagement_ruby_gapic",
    srcs = [":networkmanagement_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=networkmanagement.googleapis.com",
        "ruby-cloud-api-shortname=networkmanagement",
        "ruby-cloud-gem-name=google-cloud-network_management-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/network-intelligence-center/docs/connectivity-tests/reference/networkmanagement/rest",
    ],
    grpc_service_config = "networkmanagement_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Network Management API provides a collection of network performance monitoring and diagnostic capabilities.",
    ruby_cloud_title = "Network Management V1beta1",
    service_yaml = "networkmanagement_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networkmanagement_ruby_grpc",
        ":networkmanagement_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-networkmanagement-v1beta1-ruby",
    deps = [
        ":networkmanagement_ruby_gapic",
        ":networkmanagement_ruby_grpc",
        ":networkmanagement_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "networkmanagement_csharp_proto",
    extra_opts = [],
    deps = [":networkmanagement_proto"],
)

csharp_grpc_library(
    name = "networkmanagement_csharp_grpc",
    srcs = [":networkmanagement_proto"],
    deps = [":networkmanagement_csharp_proto"],
)

csharp_gapic_library(
    name = "networkmanagement_csharp_gapic",
    srcs = [":networkmanagement_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "networkmanagement_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networkmanagement_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networkmanagement_csharp_grpc",
        ":networkmanagement_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-networkmanagement-v1beta1-csharp",
    deps = [
        ":networkmanagement_csharp_gapic",
        ":networkmanagement_csharp_grpc",
        ":networkmanagement_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "networkmanagement_cc_proto",
    deps = [":networkmanagement_proto"],
)

cc_grpc_library(
    name = "networkmanagement_cc_grpc",
    srcs = [":networkmanagement_proto"],
    grpc_only = True,
    deps = [":networkmanagement_cc_proto"],
)
