# This build file includes a target for the Ruby wrapper library for
# google-cloud-network_management.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for networkmanagement.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "networkmanagement_ruby_wrapper",
    srcs = ["//google/cloud/networkmanagement/v1:networkmanagement_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-network_management",
        "ruby-cloud-wrapper-of=v1:0.10",
        "ruby-cloud-product-url=https://cloud.google.com/network-intelligence-center/docs/connectivity-tests/reference/networkmanagement/rest",
        "ruby-cloud-api-id=networkmanagement.googleapis.com",
        "ruby-cloud-api-shortname=networkmanagement",
    ],
    ruby_cloud_description = "The Network Management API provides a collection of network performance monitoring and diagnostic capabilities.",
    ruby_cloud_title = "Network Management",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-networkmanagement-ruby",
    deps = [
        ":networkmanagement_ruby_wrapper",
    ],
)
