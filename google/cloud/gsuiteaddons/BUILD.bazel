# This build file includes a target for the Ruby wrapper library for
# google-cloud-gsuite_add_ons.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for gsuiteaddons.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "gsuiteaddons_ruby_wrapper",
    srcs = ["//google/cloud/gsuiteaddons/v1:gsuiteaddons_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-gsuite_add_ons",
        "ruby-cloud-gem-namespace=Google::Cloud::GSuiteAddOns",
        "ruby-cloud-wrapper-of=v1:0.5",
        "ruby-cloud-product-url=https://developers.google.com/workspace/add-ons/",
        "ruby-cloud-api-id=gsuiteaddons.googleapis.com",
        "ruby-cloud-api-shortname=gsuiteaddons",
        "ruby-cloud-namespace-override=GsuiteAddOns=GSuiteAddOns",
        "ruby-cloud-path-override=g_suite_add_ons=gsuite_add_ons",
    ],
    ruby_cloud_description = "Add-ons are customized applications that integrate with Google Workspace productivity applications.",
    ruby_cloud_title = "Google Workspace Add-ons",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-gsuiteaddons-ruby",
    deps = [
        ":gsuiteaddons_ruby_wrapper",
    ],
)
