{"methodConfig": [{"name": [{"service": "google.cloud.gsuiteaddons.v1.GSuiteAddOns"}], "timeout": "10s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "UNKNOWN"]}}, {"name": [{"service": "google.cloud.gsuiteaddons.v1.GSuiteAddOns", "method": "CreateDeployment"}, {"service": "google.cloud.gsuiteaddons.v1.GSuiteAddOns", "method": "DeleteDeployment"}], "timeout": "10s"}, {"name": [{"service": "google.cloud.gsuiteaddons.v1.GSuiteAddOns", "method": "GetAuthorization"}], "timeout": "120s"}]}