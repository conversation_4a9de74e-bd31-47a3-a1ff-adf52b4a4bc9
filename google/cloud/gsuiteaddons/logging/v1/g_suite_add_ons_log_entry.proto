// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gsuiteaddons.logging.v1;

import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.GSuiteAddOns.Logging.V1";
option go_package = "cloud.google.com/go/gsuiteaddons/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "GSuiteAddOnsLogEntryProto";
option java_package = "com.google.cloud.gsuiteaddons.logging.v1";
option php_namespace = "Google\\Cloud\\GSuiteAddOns\\Logging\\V1";
option ruby_package = "Google::Cloud::GSuiteAddOns::Logging::V1";

// JSON payload of error messages that are logged to Cloud Logging. An error
// message (in English) is written to Cloud Logging (if not disabled) when an
// error is encountered while using an add-on.
message GSuiteAddOnsLogEntry {
  // The deployment that caused the error. For add-ons built in Apps Script,
  // this is the deployment ID defined by Apps Script. For add-ons built in
  // other languages, this is the deployment ID defined in Google Cloud.
  string deployment = 1;

  // The error code and message.
  google.rpc.Status error = 2;

  // The function name that was running when the error occurred. This field
  // might not always be set, for example, if an error happens when fetching the
  // list of installed add-ons for a user.
  string deployment_function = 3;
}
