// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.parallelstore.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Parallelstore.V1";
option go_package = "cloud.google.com/go/parallelstore/apiv1/parallelstorepb;parallelstorepb";
option java_multiple_files = true;
option java_outer_classname = "ParallelstoreProto";
option java_package = "com.google.cloud.parallelstore.v1";
option php_namespace = "Google\\Cloud\\Parallelstore\\V1";
option ruby_package = "Google::Cloud::Parallelstore::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Network"
  pattern: "projects/{project}/global/networks/{network}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Address"
  pattern: "projects/{project}/regions/{region}/addresses/{address}"
};
option (google.api.resource_definition) = {
  type: "iam.googleapis.com/ServiceAccount"
  pattern: "projects/{project}/serviceAccounts/{service_account}"
};

// Service describing handlers for resources
// Configures and manages parallelstore resources.
//
// Parallelstore service.
//
// The `parallelstore.googleapis.com` service implements the parallelstore API
// and defines the following resource model for managing instances:
// * The service works with a collection of cloud projects, named: `/projects/*`
// * Each project has a collection of available locations, named: `/locations/*`
// * Each location has a collection of instances named `/instances/*`.
// * Parallelstore instances are resources of the form:
//   `/projects/{project_id}/locations/{location_id}/instances/{instance_id}`
//
// Note that location_id must be a Google Cloud `zone`; for example:
// * `projects/12345/locations/us-central1-c/instances/my-parallelstore-share`
service Parallelstore {
  option (google.api.default_host) = "parallelstore.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all instances in a given project and location.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a Parallelstore instance in a given project and location.
  rpc CreateInstance(CreateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance,instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single instance.
  rpc UpdateInstance(UpdateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{instance.name=projects/*/locations/*/instances/*}"
      body: "instance"
    };
    option (google.api.method_signature) = "instance,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single instance.
  rpc DeleteInstance(DeleteInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Copies data from Cloud Storage to Parallelstore.
  rpc ImportData(ImportDataRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:importData"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ImportDataResponse"
      metadata_type: "ImportDataMetadata"
    };
  }

  // Copies data from Parallelstore to Cloud Storage.
  rpc ExportData(ExportDataRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:exportData"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportDataResponse"
      metadata_type: "ExportDataMetadata"
    };
  }
}

// Type of transfer that occurred.
enum TransferType {
  // Zero is an illegal value.
  TRANSFER_TYPE_UNSPECIFIED = 0;

  // Imports to Parallelstore.
  IMPORT = 1;

  // Exports from Parallelstore.
  EXPORT = 2;
}

// Represents the striping options for files.
enum FileStripeLevel {
  // If not set, FileStripeLevel will default to FILE_STRIPE_LEVEL_BALANCED
  FILE_STRIPE_LEVEL_UNSPECIFIED = 0;

  // Minimum file striping
  FILE_STRIPE_LEVEL_MIN = 1;

  // Medium file striping
  FILE_STRIPE_LEVEL_BALANCED = 2;

  // Maximum file striping
  FILE_STRIPE_LEVEL_MAX = 3;
}

// Represents the striping options for directories.
enum DirectoryStripeLevel {
  // If not set, DirectoryStripeLevel will default to DIRECTORY_STRIPE_LEVEL_MAX
  DIRECTORY_STRIPE_LEVEL_UNSPECIFIED = 0;

  // Minimum directory striping
  DIRECTORY_STRIPE_LEVEL_MIN = 1;

  // Medium directory striping
  DIRECTORY_STRIPE_LEVEL_BALANCED = 2;

  // Maximum directory striping
  DIRECTORY_STRIPE_LEVEL_MAX = 3;
}

// A Parallelstore instance.
message Instance {
  option (google.api.resource) = {
    type: "parallelstore.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/instances/{instance}"
    plural: "instances"
    singular: "instance"
  };

  // The possible states of a Parallelstore instance.
  enum State {
    // Not set.
    STATE_UNSPECIFIED = 0;

    // The instance is being created.
    CREATING = 1;

    // The instance is available for use.
    ACTIVE = 2;

    // The instance is being deleted.
    DELETING = 3;

    // The instance is not usable.
    FAILED = 4;

    // The instance is being upgraded.
    UPGRADING = 5;
  }

  // Identifier. The resource name of the instance, in the format
  // `projects/{project}/locations/{location}/instances/{instance_id}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. The description of the instance. 2048 characters or less.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The instance state.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the instance was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the instance was updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Cloud Labels are a flexible and lightweight mechanism for
  // organizing cloud resources into groups that reflect a customer's
  // organizational needs and deployment strategies. See
  // https://cloud.google.com/resource-manager/docs/labels-overview for details.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Required. Immutable. The instance's storage capacity in Gibibytes (GiB).
  // Allowed values are between 12000 and 100000, in multiples of 4000; e.g.,
  // 12000, 16000, 20000, ...
  int64 capacity_gib = 8 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED
  ];

  // Output only. The version of DAOS software running in the instance.
  string daos_version = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of IPv4 addresses used for client side configuration.
  repeated string access_points = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Immutable. The name of the Compute Engine
  // [VPC network](https://cloud.google.com/vpc/docs/vpc) to which the
  // instance is connected.
  string network = 11 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Optional. Immutable. The ID of the IP address range being used by the
  // instance's VPC network. See [Configure a VPC
  // network](https://cloud.google.com/parallelstore/docs/vpc#create_and_configure_the_vpc).
  // If no ID is provided, all ranges are considered.
  string reserved_ip_range = 12 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Address" }
  ];

  // Output only. Immutable. The ID of the IP address range being used by the
  // instance's VPC network. This field is populated by the service and contains
  // the value currently used by the service.
  string effective_reserved_ip_range = 14 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Address" }
  ];

  // Optional. Stripe level for files. Allowed values are:
  //
  // * `FILE_STRIPE_LEVEL_MIN`: offers the best performance for small size
  //   files.
  // * `FILE_STRIPE_LEVEL_BALANCED`: balances performance for workloads
  //   involving a mix of small and large files.
  // * `FILE_STRIPE_LEVEL_MAX`: higher throughput performance for larger files.
  FileStripeLevel file_stripe_level = 15
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Stripe level for directories. Allowed values are:
  //
  // * `DIRECTORY_STRIPE_LEVEL_MIN`: recommended when directories contain a
  //   small number of files.
  // * `DIRECTORY_STRIPE_LEVEL_BALANCED`: balances performance for workloads
  //   involving a mix of small and large directories.
  // * `DIRECTORY_STRIPE_LEVEL_MAX`: recommended for directories with a large
  //   number of files.
  DirectoryStripeLevel directory_stripe_level = 16
      [(google.api.field_behavior) = OPTIONAL];
}

// List instances request.
message ListInstancesRequest {
  // Required. The project and location for which to retrieve instance
  // information, in the format `projects/{project_id}/locations/{location}`.
  //
  // To retrieve instance information for all locations, use "-" as the value of
  // `{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "parallelstore.googleapis.com/Instance"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, the server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response from
// [ListInstances][google.cloud.parallelstore.v1.Parallelstore.ListInstances].
message ListInstancesResponse {
  // The list of Parallelstore instances.
  repeated Instance instances = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Get an instance's details.
message GetInstanceRequest {
  // Required. The instance resource name, in the format
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "parallelstore.googleapis.com/Instance"
    }
  ];
}

// Create a new Parallelstore instance.
message CreateInstanceRequest {
  // Required. The instance's project and location, in the format
  // `projects/{project}/locations/{location}`.
  // Locations map to Google Cloud zones; for example, `us-west1-b`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "parallelstore.googleapis.com/Instance"
    }
  ];

  // Required. The name of the Parallelstore instance.
  //
  // * Must contain only lowercase letters, numbers, and hyphens.
  // * Must start with a letter.
  // * Must be between 1-63 characters.
  // * Must end with a number or a letter.
  // * Must be unique within the customer project / location
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The instance to create.
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Update an instance.
message UpdateInstanceRequest {
  // Required. Mask of fields to update. Field mask is used to specify the
  // fields to be overwritten in the Instance resource by the update. At least
  // one path must be supplied in this field. The fields specified in the
  // update_mask are relative to the resource, not the full request.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The instance to update.
  Instance instance = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Delete an instance.
message DeleteInstanceRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "parallelstore.googleapis.com/Instance"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Long-running operation metadata.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][google.longrunning.Operation.error] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Cloud Storage as the source of a data transfer.
message SourceGcsBucket {
  // Required. URI to a Cloud Storage bucket in the format:
  // `gs://<bucket_name>/<path_inside_bucket>`. The path inside the bucket is
  // optional.
  string uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// Cloud Storage as the destination of a data transfer.
message DestinationGcsBucket {
  // Required. URI to a Cloud Storage bucket in the format:
  // `gs://<bucket_name>/<path_inside_bucket>`. The path inside the bucket is
  // optional.
  string uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// Parallelstore as the source of a data transfer.
message SourceParallelstore {
  // Optional. Root directory path to the Paralellstore filesystem, starting
  // with `/`. Defaults to `/` if unset.
  string path = 1 [(google.api.field_behavior) = OPTIONAL];
}

// Parallelstore as the destination of a data transfer.
message DestinationParallelstore {
  // Optional. Root directory path to the Paralellstore filesystem, starting
  // with `/`. Defaults to `/` if unset.
  string path = 1 [(google.api.field_behavior) = OPTIONAL];
}

// Import data from Cloud Storage into a Parallelstore instance.
message ImportDataRequest {
  // The source of the data being imported into the Parallelstore instance.
  oneof source {
    // The Cloud Storage source bucket and, optionally, path inside the bucket.
    SourceGcsBucket source_gcs_bucket = 2;
  }

  // The Parallelstore instance into which to import data.
  oneof destination {
    // Parallelstore destination.
    DestinationParallelstore destination_parallelstore = 3;
  }

  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "parallelstore.googleapis.com/Instance"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. User-specified service account credentials to be used when
  // performing the transfer.
  //
  // Use one of the following formats:
  //
  // * `{EMAIL_ADDRESS_OR_UNIQUE_ID}`
  // * `projects/{PROJECT_ID_OR_NUMBER}/serviceAccounts/{EMAIL_ADDRESS_OR_UNIQUE_ID}`
  // * `projects/-/serviceAccounts/{EMAIL_ADDRESS_OR_UNIQUE_ID}`
  //
  // If unspecified, the Parallelstore service agent is used:
  // `service-<PROJECT_NUMBER>@gcp-sa-parallelstore.iam.gserviceaccount.com`
  string service_account = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];
}

// Export data from Parallelstore to Cloud Storage.
message ExportDataRequest {
  // The Parallelstore instance to export from.
  oneof source {
    // Parallelstore source.
    SourceParallelstore source_parallelstore = 2;
  }

  // The Cloud Storage bucket to export to.
  oneof destination {
    // Cloud Storage destination.
    DestinationGcsBucket destination_gcs_bucket = 3;
  }

  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "parallelstore.googleapis.com/Instance"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (********-0000-0000-0000-********0000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. User-specified Service Account (SA) credentials to be used when
  // performing the transfer.
  // Use one of the following formats:
  //
  // * `{EMAIL_ADDRESS_OR_UNIQUE_ID}`
  // * `projects/{PROJECT_ID_OR_NUMBER}/serviceAccounts/{EMAIL_ADDRESS_OR_UNIQUE_ID}`
  // * `projects/-/serviceAccounts/{EMAIL_ADDRESS_OR_UNIQUE_ID}`
  //
  // If unspecified, the Parallelstore service agent is used:
  // `service-<PROJECT_NUMBER>@gcp-sa-parallelstore.iam.gserviceaccount.com`
  string service_account = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];
}

// The response to a request to import data to Parallelstore.
message ImportDataResponse {}

// Metadata related to the data import operation.
message ImportDataMetadata {
  // Data transfer operation metadata.
  TransferOperationMetadata operation_metadata = 1;

  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][google.longrunning.Operation.error] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The response to a request to export data from Parallelstore.
message ExportDataResponse {}

// Metadata related to the data export operation.
message ExportDataMetadata {
  // Data transfer operation metadata.
  TransferOperationMetadata operation_metadata = 1;

  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][google.longrunning.Operation.error] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Long-running operation metadata related to a data transfer.
message TransferOperationMetadata {
  // The source of transfer operation.
  oneof source {
    // Output only. Parallelstore source.
    SourceParallelstore source_parallelstore = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Cloud Storage source.
    SourceGcsBucket source_gcs_bucket = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The destination of transfer operation.
  oneof destination {
    // Output only. Cloud Storage destination.
    DestinationGcsBucket destination_gcs_bucket = 9
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Parallelstore destination.
    DestinationParallelstore destination_parallelstore = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The progress of the transfer operation.
  TransferCounters counters = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of transfer occurring.
  TransferType transfer_type = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A collection of counters that report the progress of a transfer operation.
message TransferCounters {
  // Objects found in the data source that are scheduled to be transferred,
  // excluding any that are filtered based on object conditions or skipped due
  // to sync.
  int64 objects_found = 1;

  // Bytes found in the data source that are scheduled to be transferred,
  // excluding any that are filtered based on object conditions or skipped due
  // to sync.
  int64 bytes_found = 2;

  // Objects in the data source that are not transferred because they already
  // exist in the data destination.
  int64 objects_skipped = 3;

  // Bytes in the data source that are not transferred because they already
  // exist in the data destination.
  int64 bytes_skipped = 4;

  // Objects that are copied to the data destination.
  int64 objects_copied = 5;

  // Bytes that are copied to the data destination.
  int64 bytes_copied = 6;
}
