{"methodConfig": [{"name": [{"service": "google.cloud.parallelstore.v1main.v1.Parallelstore", "method": "ListInstances"}, {"service": "google.cloud.parallelstore.v1main.v1.Parallelstore", "method": "GetInstance"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.parallelstore.v1main.v1.Parallelstore", "method": "CreateInstance"}], "timeout": "1800s"}, {"name": [{"service": "google.cloud.parallelstore.v1main.v1.Parallelstore", "method": "UpdateInstance"}, {"service": "google.cloud.parallelstore.v1main.v1.Parallelstore", "method": "DeleteInstance"}], "timeout": "3600s"}]}