# This build file includes a target for the Ruby wrapper library for
# google-cloud-security-private_ca.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for privateca.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "privateca_ruby_wrapper",
    srcs = ["//google/cloud/security/privateca/v1:privateca_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-security-private_ca",
        "ruby-cloud-gem-namespace=Google::Cloud::Security::PrivateCA",
        "ruby-cloud-env-prefix=PRIVATE_CA",
        "ruby-cloud-wrapper-of=v1:0.10;v1beta1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/certificate-authority-service/",
        "ruby-cloud-api-id=privateca.googleapis.com",
        "ruby-cloud-api-shortname=privateca",
    ],
    ruby_cloud_description = "Certificate Authority Service is a highly available, scalable Google Cloud service that enables you to simplify, automate, and customize the deployment, management, and security of private certificate authorities (CA).",
    ruby_cloud_title = "Certificate Authority Service",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-security-privateca-ruby",
    deps = [
        ":privateca_ruby_wrapper",
    ],
)
