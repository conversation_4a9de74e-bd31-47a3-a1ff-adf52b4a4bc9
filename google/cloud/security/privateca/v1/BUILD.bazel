# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "privateca_proto",
    srcs = [
        "resources.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:expr_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "privateca_proto_with_info",
    deps = [
        ":privateca_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "privateca_java_proto",
    deps = [":privateca_proto"],
)

java_grpc_library(
    name = "privateca_java_grpc",
    srcs = [":privateca_proto"],
    deps = [":privateca_java_proto"],
)

java_gapic_library(
    name = "privateca_java_gapic",
    srcs = [":privateca_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "privateca_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "privateca_v1.yaml",
    test_deps = [
        ":privateca_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":privateca_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "privateca_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.security.privateca.v1.CertificateAuthorityServiceClientHttpJsonTest",
        "com.google.cloud.security.privateca.v1.CertificateAuthorityServiceClientTest",
    ],
    runtime_deps = [":privateca_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-security-privateca-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":privateca_java_gapic",
        ":privateca_java_grpc",
        ":privateca_java_proto",
        ":privateca_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "privateca_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/security/privateca/apiv1/privatecapb",
    protos = [":privateca_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:expr_go_proto",
    ],
)

go_gapic_library(
    name = "privateca_go_gapic",
    srcs = [":privateca_proto_with_info"],
    grpc_service_config = "privateca_grpc_service_config.json",
    importpath = "cloud.google.com/go/security/privateca/apiv1;privateca",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "privateca_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":privateca_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-security-privateca-v1-go",
    deps = [
        ":privateca_go_gapic",
        ":privateca_go_gapic_srcjar-metadata.srcjar",
        ":privateca_go_gapic_srcjar-snippets.srcjar",
        ":privateca_go_gapic_srcjar-test.srcjar",
        ":privateca_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "privateca_py_gapic",
    srcs = [":privateca_proto"],
    grpc_service_config = "privateca_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-private-ca",
    ],
    rest_numeric_enums = True,
    service_yaml = "privateca_v1.yaml",
    transport = "grpc+rest",
    deps = ["//google/iam/v1:iam_policy_py_proto"],
)

py_test(
    name = "privateca_py_gapic_test",
    srcs = [
        "privateca_py_gapic_pytest.py",
        "privateca_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [
        ":privateca_py_gapic",
    ],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "security-privateca-v1-py",
    deps = [
        ":privateca_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "privateca_php_proto",
    deps = [":privateca_proto"],
)

php_gapic_library(
    name = "privateca_php_gapic",
    srcs = [":privateca_proto_with_info"],
    grpc_service_config = "privateca_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "privateca_v1.yaml",
    transport = "grpc+rest",
    deps = [":privateca_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-security-privateca-v1-php",
    deps = [
        ":privateca_php_gapic",
        ":privateca_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "privateca_nodejs_gapic",
    package_name = "@google-cloud/security-private-ca",
    src = ":privateca_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "privateca_grpc_service_config.json",
    package = "google.cloud.security.privateca.v1",
    rest_numeric_enums = True,
    service_yaml = "privateca_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "security-privateca-v1-nodejs",
    deps = [
        ":privateca_nodejs_gapic",
        ":privateca_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "privateca_ruby_proto",
    deps = [":privateca_proto"],
)

ruby_grpc_library(
    name = "privateca_ruby_grpc",
    srcs = [":privateca_proto"],
    deps = [":privateca_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "privateca_ruby_gapic",
    srcs = [":privateca_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-security-private_ca-v1",
        "ruby-cloud-gem-namespace=Google::Cloud::Security::PrivateCA::V1",
        "ruby-cloud-env-prefix=PRIVATE_CA",
        "ruby-cloud-product-url=https://cloud.google.com/certificate-authority-service/",
        "ruby-cloud-api-id=privateca.googleapis.com",
        "ruby-cloud-api-shortname=privateca",
    ],
    grpc_service_config = "privateca_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Certificate Authority Service is a highly available, scalable Google Cloud service that enables you to simplify, automate, and customize the deployment, management, and security of private certificate authorities (CA).",
    ruby_cloud_title = "Certificate Authority Service V1",
    service_yaml = "privateca_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":privateca_ruby_grpc",
        ":privateca_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-security-privateca-v1-ruby",
    deps = [
        ":privateca_ruby_gapic",
        ":privateca_ruby_grpc",
        ":privateca_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "privateca_csharp_proto",
    deps = [":privateca_proto"],
)

csharp_grpc_library(
    name = "privateca_csharp_grpc",
    srcs = [":privateca_proto"],
    deps = [":privateca_csharp_proto"],
)

csharp_gapic_library(
    name = "privateca_csharp_gapic",
    srcs = [":privateca_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "privateca_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "privateca_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":privateca_csharp_grpc",
        ":privateca_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-security-privateca-v1-csharp",
    deps = [
        ":privateca_csharp_gapic",
        ":privateca_csharp_grpc",
        ":privateca_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "privateca_cc_proto",
    deps = [":privateca_proto"],
)

cc_grpc_library(
    name = "privateca_cc_grpc",
    srcs = [":privateca_proto"],
    grpc_only = True,
    deps = [":privateca_cc_proto"],
)
