// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.security.privateca.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/expr.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Security.PrivateCA.V1";
option go_package = "cloud.google.com/go/security/privateca/apiv1/privatecapb;privatecapb";
option java_multiple_files = true;
option java_outer_classname = "PrivateCaResourcesProto";
option java_package = "com.google.cloud.security.privateca.v1";
option php_namespace = "Google\\Cloud\\Security\\PrivateCA\\V1";
option ruby_package = "Google::Cloud::Security::PrivateCA::V1";

// A
// [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
// represents an individual Certificate Authority. A
// [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
// can be used to create
// [Certificates][google.cloud.security.privateca.v1.Certificate].
message CertificateAuthority {
  option (google.api.resource) = {
    type: "privateca.googleapis.com/CertificateAuthority"
    pattern: "projects/{project}/locations/{location}/caPools/{ca_pool}/certificateAuthorities/{certificate_authority}"
  };

  // The type of a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority],
  // indicating its issuing chain.
  enum Type {
    // Not specified.
    TYPE_UNSPECIFIED = 0;

    // Self-signed CA.
    SELF_SIGNED = 1;

    // Subordinate CA. Could be issued by a Private CA
    // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
    // or an unmanaged CA.
    SUBORDINATE = 2;
  }

  // The state of a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority],
  // indicating if it can be used.
  enum State {
    // Not specified.
    STATE_UNSPECIFIED = 0;

    // Certificates can be issued from this CA. CRLs will be generated for this
    // CA. The CA will be part of the
    // [CaPool][google.cloud.security.privateca.v1.CaPool]'s trust anchor, and
    // will be used to issue certificates from the
    // [CaPool][google.cloud.security.privateca.v1.CaPool].
    ENABLED = 1;

    // Certificates cannot be issued from this CA. CRLs will still be generated.
    // The CA will be part of the
    // [CaPool][google.cloud.security.privateca.v1.CaPool]'s trust anchor, but
    // will not be used to issue certificates from the
    // [CaPool][google.cloud.security.privateca.v1.CaPool].
    DISABLED = 2;

    // Certificates can be issued from this CA. CRLs will be generated for this
    // CA. The CA will be part of the
    // [CaPool][google.cloud.security.privateca.v1.CaPool]'s trust anchor, but
    // will not be used to issue certificates from the
    // [CaPool][google.cloud.security.privateca.v1.CaPool].
    STAGED = 3;

    // Certificates cannot be issued from this CA. CRLs will not be generated.
    // The CA will not be part of the
    // [CaPool][google.cloud.security.privateca.v1.CaPool]'s trust anchor, and
    // will not be used to issue certificates from the
    // [CaPool][google.cloud.security.privateca.v1.CaPool].
    AWAITING_USER_ACTIVATION = 4;

    // Certificates cannot be issued from this CA. CRLs will not be generated.
    // The CA may still be recovered by calling
    // [CertificateAuthorityService.UndeleteCertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthorityService.UndeleteCertificateAuthority]
    // before
    // [expire_time][google.cloud.security.privateca.v1.CertificateAuthority.expire_time].
    // The CA will not be part of the
    // [CaPool][google.cloud.security.privateca.v1.CaPool]'s trust anchor, and
    // will not be used to issue certificates from the
    // [CaPool][google.cloud.security.privateca.v1.CaPool].
    DELETED = 5;
  }

  // URLs where a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // will publish content.
  message AccessUrls {
    // The URL where this
    // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
    // CA certificate is published. This will only be set for CAs that have been
    // activated.
    string ca_certificate_access_url = 1;

    // The URLs where this
    // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
    // CRLs are published. This will only be set for CAs that have been
    // activated.
    repeated string crl_access_urls = 2;
  }

  // A Cloud KMS key configuration that a
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // will use.
  message KeyVersionSpec {
    oneof KeyVersion {
      // The resource name for an existing Cloud KMS CryptoKeyVersion in the
      // format
      // `projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*`.
      // This option enables full flexibility in the key's capabilities and
      // properties.
      string cloud_kms_key_version = 1;

      // The algorithm to use for creating a managed Cloud KMS key for a for a
      // simplified experience. All managed keys will be have their
      // [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] as `HSM`.
      SignHashAlgorithm algorithm = 2;
    }
  }

  // The algorithm of a Cloud KMS CryptoKeyVersion of a
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] with the
  // [CryptoKeyPurpose][google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose] value
  // `ASYMMETRIC_SIGN`. These values correspond to the
  // [CryptoKeyVersionAlgorithm][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionAlgorithm]
  // values. For RSA signing algorithms, the PSS algorithms should be preferred,
  // use PKCS1 algorithms if required for compatibility. For further
  // recommendations, see
  // https://cloud.google.com/kms/docs/algorithms#algorithm_recommendations.
  enum SignHashAlgorithm {
    // Not specified.
    SIGN_HASH_ALGORITHM_UNSPECIFIED = 0;

    // maps to CryptoKeyVersionAlgorithm.RSA_SIGN_PSS_2048_SHA256
    RSA_PSS_2048_SHA256 = 1;

    // maps to CryptoKeyVersionAlgorithm. RSA_SIGN_PSS_3072_SHA256
    RSA_PSS_3072_SHA256 = 2;

    // maps to CryptoKeyVersionAlgorithm.RSA_SIGN_PSS_4096_SHA256
    RSA_PSS_4096_SHA256 = 3;

    // maps to CryptoKeyVersionAlgorithm.RSA_SIGN_PKCS1_2048_SHA256
    RSA_PKCS1_2048_SHA256 = 6;

    // maps to CryptoKeyVersionAlgorithm.RSA_SIGN_PKCS1_3072_SHA256
    RSA_PKCS1_3072_SHA256 = 7;

    // maps to CryptoKeyVersionAlgorithm.RSA_SIGN_PKCS1_4096_SHA256
    RSA_PKCS1_4096_SHA256 = 8;

    // maps to CryptoKeyVersionAlgorithm.EC_SIGN_P256_SHA256
    EC_P256_SHA256 = 4;

    // maps to CryptoKeyVersionAlgorithm.EC_SIGN_P384_SHA384
    EC_P384_SHA384 = 5;
  }

  // Output only. The resource name for this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in the format `projects/*/locations/*/caPools/*/certificateAuthorities/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. The
  // [Type][google.cloud.security.privateca.v1.CertificateAuthority.Type] of
  // this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
  Type type = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Immutable. The config used to create a self-signed X.509
  // certificate or CSR.
  CertificateConfig config = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Immutable. The desired lifetime of the CA certificate. Used to
  // create the "not_before_time" and "not_after_time" fields inside an X.509
  // certificate.
  google.protobuf.Duration lifetime = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Immutable. Used when issuing certificates for this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
  // If this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // is a self-signed CertificateAuthority, this key is also used to sign the
  // self-signed CA certificate. Otherwise, it is used to sign a CSR.
  KeyVersionSpec key_spec = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. If this is a subordinate
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority],
  // this field will be set with the subordinate configuration, which describes
  // its issuers. This may be updated, but this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // must continue to validate.
  SubordinateConfig subordinate_config = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The
  // [CaPool.Tier][google.cloud.security.privateca.v1.CaPool.Tier] of the
  // [CaPool][google.cloud.security.privateca.v1.CaPool] that includes this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
  CaPool.Tier tier = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The
  // [State][google.cloud.security.privateca.v1.CertificateAuthority.State] for
  // this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
  // certificate chain, including the current
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
  // certificate. Ordered such that the root issuer is the final element
  // (consistent with RFC 5246). For a self-signed CA, this will only list the
  // current
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
  // certificate.
  repeated string pem_ca_certificates = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A structured description of this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
  // CA certificate and its issuers. Ordered as self-to-root.
  repeated CertificateDescription ca_certificate_descriptions = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The name of a Cloud Storage bucket where this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // will publish content, such as the CA certificate and CRLs. This must be a
  // bucket name, without any prefixes (such as `gs://`) or suffixes (such as
  // `.googleapis.com`). For example, to use a bucket named `my-bucket`, you
  // would simply specify `my-bucket`. If not specified, a managed bucket will
  // be created.
  string gcs_bucket = 11 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. URLs for accessing content published by this CA, such as the
  // CA certificate and CRLs.
  AccessUrls access_urls = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // was created.
  google.protobuf.Timestamp create_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // was last updated.
  google.protobuf.Timestamp update_time = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // was soft deleted, if it is in the
  // [DELETED][google.cloud.security.privateca.v1.CertificateAuthority.State.DELETED]
  // state.
  google.protobuf.Timestamp delete_time = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // will be permanently purged, if it is in the
  // [DELETED][google.cloud.security.privateca.v1.CertificateAuthority.State.DELETED]
  // state.
  google.protobuf.Timestamp expire_time = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels with user-defined metadata.
  map<string, string> labels = 17 [(google.api.field_behavior) = OPTIONAL];
}

// A [CaPool][google.cloud.security.privateca.v1.CaPool] represents a group of
// [CertificateAuthorities][google.cloud.security.privateca.v1.CertificateAuthority]
// that form a trust anchor. A
// [CaPool][google.cloud.security.privateca.v1.CaPool] can be used to manage
// issuance policies for one or more
// [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
// resources and to rotate CA certificates in and out of the trust anchor.
message CaPool {
  option (google.api.resource) = {
    type: "privateca.googleapis.com/CaPool"
    pattern: "projects/{project}/locations/{location}/caPools/{ca_pool}"
  };

  // The tier of a [CaPool][google.cloud.security.privateca.v1.CaPool],
  // indicating its supported functionality and/or billing SKU.
  enum Tier {
    // Not specified.
    TIER_UNSPECIFIED = 0;

    // Enterprise tier.
    ENTERPRISE = 1;

    // DevOps tier.
    DEVOPS = 2;
  }

  // Options relating to the publication of each
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
  // CA certificate and CRLs and their inclusion as extensions in issued
  // [Certificates][google.cloud.security.privateca.v1.Certificate]. The options
  // set here apply to certificates issued by any
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in the [CaPool][google.cloud.security.privateca.v1.CaPool].
  message PublishingOptions {
    // Supported encoding formats for publishing.
    enum EncodingFormat {
      // Not specified. By default, PEM format will be used.
      ENCODING_FORMAT_UNSPECIFIED = 0;

      // The
      // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
      // CA certificate and CRLs will be published in PEM format.
      PEM = 1;

      // The
      // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
      // CA certificate and CRLs will be published in DER format.
      DER = 2;
    }

    // Optional. When true, publishes each
    // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
    // CA certificate and includes its URL in the "Authority Information Access"
    // X.509 extension in all issued
    // [Certificates][google.cloud.security.privateca.v1.Certificate]. If this
    // is false, the CA certificate will not be published and the corresponding
    // X.509 extension will not be written in issued certificates.
    bool publish_ca_cert = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. When true, publishes each
    // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]'s
    // CRL and includes its URL in the "CRL Distribution Points" X.509 extension
    // in all issued
    // [Certificates][google.cloud.security.privateca.v1.Certificate]. If this
    // is false, CRLs will not be published and the corresponding X.509
    // extension will not be written in issued certificates. CRLs will expire 7
    // days from their creation. However, we will rebuild daily. CRLs are also
    // rebuilt shortly after a certificate is revoked.
    bool publish_crl = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Specifies the encoding format of each
    // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
    // resource's CA certificate and CRLs. If this is omitted, CA certificates
    // and CRLs will be published in PEM.
    EncodingFormat encoding_format = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Defines controls over all certificate issuance within a
  // [CaPool][google.cloud.security.privateca.v1.CaPool].
  message IssuancePolicy {
    // Describes a "type" of key that may be used in a
    // [Certificate][google.cloud.security.privateca.v1.Certificate] issued from
    // a [CaPool][google.cloud.security.privateca.v1.CaPool]. Note that a single
    // [AllowedKeyType][google.cloud.security.privateca.v1.CaPool.IssuancePolicy.AllowedKeyType]
    // may refer to either a fully-qualified key algorithm, such as RSA 4096, or
    // a family of key algorithms, such as any RSA key.
    message AllowedKeyType {
      // Describes an RSA key that may be used in a
      // [Certificate][google.cloud.security.privateca.v1.Certificate] issued
      // from a [CaPool][google.cloud.security.privateca.v1.CaPool].
      message RsaKeyType {
        // Optional. The minimum allowed RSA modulus size (inclusive), in bits.
        // If this is not set, or if set to zero, the service-level min RSA
        // modulus size will continue to apply.
        int64 min_modulus_size = 1 [(google.api.field_behavior) = OPTIONAL];

        // Optional. The maximum allowed RSA modulus size (inclusive), in bits.
        // If this is not set, or if set to zero, the service will not enforce
        // an explicit upper bound on RSA modulus sizes.
        int64 max_modulus_size = 2 [(google.api.field_behavior) = OPTIONAL];
      }

      // Describes an Elliptic Curve key that may be used in a
      // [Certificate][google.cloud.security.privateca.v1.Certificate] issued
      // from a [CaPool][google.cloud.security.privateca.v1.CaPool].
      message EcKeyType {
        // Describes an elliptic curve-based signature algorithm that may be
        // used in a
        // [Certificate][google.cloud.security.privateca.v1.Certificate] issued
        // from a [CaPool][google.cloud.security.privateca.v1.CaPool].
        enum EcSignatureAlgorithm {
          // Not specified. Signifies that any signature algorithm may be used.
          EC_SIGNATURE_ALGORITHM_UNSPECIFIED = 0;

          // Refers to the Elliptic Curve Digital Signature Algorithm over the
          // NIST P-256 curve.
          ECDSA_P256 = 1;

          // Refers to the Elliptic Curve Digital Signature Algorithm over the
          // NIST P-384 curve.
          ECDSA_P384 = 2;

          // Refers to the Edwards-curve Digital Signature Algorithm over curve
          // 25519, as described in RFC 8410.
          EDDSA_25519 = 3;
        }

        // Optional. A signature algorithm that must be used. If this is
        // omitted, any EC-based signature algorithm will be allowed.
        EcSignatureAlgorithm signature_algorithm = 1
            [(google.api.field_behavior) = OPTIONAL];
      }

      oneof key_type {
        // Represents an allowed RSA key type.
        RsaKeyType rsa = 1;

        // Represents an allowed Elliptic Curve key type.
        EcKeyType elliptic_curve = 2;
      }
    }

    // [IssuanceModes][google.cloud.security.privateca.v1.CaPool.IssuancePolicy.IssuanceModes]
    // specifies the allowed ways in which
    // [Certificates][google.cloud.security.privateca.v1.Certificate] may be
    // requested from this [CaPool][google.cloud.security.privateca.v1.CaPool].
    message IssuanceModes {
      // Optional. When true, allows callers to create
      // [Certificates][google.cloud.security.privateca.v1.Certificate] by
      // specifying a CSR.
      bool allow_csr_based_issuance = 1
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. When true, allows callers to create
      // [Certificates][google.cloud.security.privateca.v1.Certificate] by
      // specifying a
      // [CertificateConfig][google.cloud.security.privateca.v1.CertificateConfig].
      bool allow_config_based_issuance = 2
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Optional. If any
    // [AllowedKeyType][google.cloud.security.privateca.v1.CaPool.IssuancePolicy.AllowedKeyType]
    // is specified, then the certificate request's public key must match one of
    // the key types listed here. Otherwise, any key may be used.
    repeated AllowedKeyType allowed_key_types = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The maximum lifetime allowed for issued
    // [Certificates][google.cloud.security.privateca.v1.Certificate]. Note that
    // if the issuing
    // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
    // expires before a
    // [Certificate][google.cloud.security.privateca.v1.Certificate] resource's
    // requested maximum_lifetime, the effective lifetime will be explicitly
    // truncated to match it.
    google.protobuf.Duration maximum_lifetime = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. If specified, then only methods allowed in the
    // [IssuanceModes][google.cloud.security.privateca.v1.CaPool.IssuancePolicy.IssuanceModes]
    // may be used to issue
    // [Certificates][google.cloud.security.privateca.v1.Certificate].
    IssuanceModes allowed_issuance_modes = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. A set of X.509 values that will be applied to all certificates
    // issued through this [CaPool][google.cloud.security.privateca.v1.CaPool].
    // If a certificate request includes conflicting values for the same
    // properties, they will be overwritten by the values defined here. If a
    // certificate request uses a
    // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
    // that defines conflicting
    // [predefined_values][google.cloud.security.privateca.v1.CertificateTemplate.predefined_values]
    // for the same properties, the certificate issuance request will fail.
    X509Parameters baseline_values = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Describes constraints on identities that may appear in
    // [Certificates][google.cloud.security.privateca.v1.Certificate] issued
    // through this [CaPool][google.cloud.security.privateca.v1.CaPool]. If this
    // is omitted, then this [CaPool][google.cloud.security.privateca.v1.CaPool]
    // will not add restrictions on a certificate's identity.
    CertificateIdentityConstraints identity_constraints = 5
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Describes the set of X.509 extensions that may appear in a
    // [Certificate][google.cloud.security.privateca.v1.Certificate] issued
    // through this [CaPool][google.cloud.security.privateca.v1.CaPool]. If a
    // certificate request sets extensions that don't appear in the
    // [passthrough_extensions][google.cloud.security.privateca.v1.CaPool.IssuancePolicy.passthrough_extensions],
    // those extensions will be dropped. If a certificate request uses a
    // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
    // with
    // [predefined_values][google.cloud.security.privateca.v1.CertificateTemplate.predefined_values]
    // that don't appear here, the certificate issuance request will fail. If
    // this is omitted, then this
    // [CaPool][google.cloud.security.privateca.v1.CaPool] will not add
    // restrictions on a certificate's X.509 extensions. These constraints do
    // not apply to X.509 extensions set in this
    // [CaPool][google.cloud.security.privateca.v1.CaPool]'s
    // [baseline_values][google.cloud.security.privateca.v1.CaPool.IssuancePolicy.baseline_values].
    CertificateExtensionConstraints passthrough_extensions = 6
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. The resource name for this
  // [CaPool][google.cloud.security.privateca.v1.CaPool] in the format
  // `projects/*/locations/*/caPools/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. The
  // [Tier][google.cloud.security.privateca.v1.CaPool.Tier] of this
  // [CaPool][google.cloud.security.privateca.v1.CaPool].
  Tier tier = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. The
  // [IssuancePolicy][google.cloud.security.privateca.v1.CaPool.IssuancePolicy]
  // to control how
  // [Certificates][google.cloud.security.privateca.v1.Certificate] will be
  // issued from this [CaPool][google.cloud.security.privateca.v1.CaPool].
  IssuancePolicy issuance_policy = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The
  // [PublishingOptions][google.cloud.security.privateca.v1.CaPool.PublishingOptions]
  // to follow when issuing
  // [Certificates][google.cloud.security.privateca.v1.Certificate] from any
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in this [CaPool][google.cloud.security.privateca.v1.CaPool].
  PublishingOptions publishing_options = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels with user-defined metadata.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A
// [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList]
// corresponds to a signed X.509 certificate Revocation List (CRL). A CRL
// contains the serial numbers of certificates that should no longer be trusted.
message CertificateRevocationList {
  option (google.api.resource) = {
    type: "privateca.googleapis.com/CertificateRevocationList"
    pattern: "projects/{project}/locations/{location}/caPools/{ca_pool}/certificateAuthorities/{certificate_authority}/certificateRevocationLists/{certificate_revocation_list}"
  };

  // Describes a revoked
  // [Certificate][google.cloud.security.privateca.v1.Certificate].
  message RevokedCertificate {
    // The resource name for the
    // [Certificate][google.cloud.security.privateca.v1.Certificate] in the
    // format `projects/*/locations/*/caPools/*/certificates/*`.
    string certificate = 1 [(google.api.resource_reference) = {
      type: "privateca.googleapis.com/Certificate"
    }];

    // The serial number of the
    // [Certificate][google.cloud.security.privateca.v1.Certificate].
    string hex_serial_number = 2;

    // The reason the
    // [Certificate][google.cloud.security.privateca.v1.Certificate] was
    // revoked.
    RevocationReason revocation_reason = 3;
  }

  // The state of a
  // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList],
  // indicating if it is current.
  enum State {
    // Not specified.
    STATE_UNSPECIFIED = 0;

    // The
    // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList]
    // is up to date.
    ACTIVE = 1;

    // The
    // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList]
    // is no longer current.
    SUPERSEDED = 2;
  }

  // Output only. The resource name for this
  // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList]
  // in the format `projects/*/locations/*/caPools/*certificateAuthorities/*/
  //    certificateRevocationLists/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The CRL sequence number that appears in pem_crl.
  int64 sequence_number = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The revoked serial numbers that appear in pem_crl.
  repeated RevokedCertificate revoked_certificates = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The PEM-encoded X.509 CRL.
  string pem_crl = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The location where 'pem_crl' can be accessed.
  string access_url = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The
  // [State][google.cloud.security.privateca.v1.CertificateRevocationList.State]
  // for this
  // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList].
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this
  // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList]
  // was created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this
  // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList]
  // was updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The revision ID of this
  // [CertificateRevocationList][google.cloud.security.privateca.v1.CertificateRevocationList].
  // A new revision is committed whenever a new CRL is published. The format is
  // an 8-character hexadecimal string.
  string revision_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels with user-defined metadata.
  map<string, string> labels = 10 [(google.api.field_behavior) = OPTIONAL];
}

// A [Certificate][google.cloud.security.privateca.v1.Certificate] corresponds
// to a signed X.509 certificate issued by a
// [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
message Certificate {
  option (google.api.resource) = {
    type: "privateca.googleapis.com/Certificate"
    pattern: "projects/{project}/locations/{location}/caPools/{ca_pool}/certificates/{certificate}"
  };

  // Describes fields that are relavent to the revocation of a
  // [Certificate][google.cloud.security.privateca.v1.Certificate].
  message RevocationDetails {
    // Indicates why a
    // [Certificate][google.cloud.security.privateca.v1.Certificate] was
    // revoked.
    RevocationReason revocation_state = 1;

    // The time at which this
    // [Certificate][google.cloud.security.privateca.v1.Certificate] was
    // revoked.
    google.protobuf.Timestamp revocation_time = 2;
  }

  // Output only. The resource name for this
  // [Certificate][google.cloud.security.privateca.v1.Certificate] in the format
  // `projects/*/locations/*/caPools/*/certificates/*`.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/Certificate"
    }
  ];

  // The config used to create a signed X.509 certificate.
  oneof certificate_config {
    // Immutable. A pem-encoded X.509 certificate signing request (CSR).
    string pem_csr = 2 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. A description of the certificate and key that does not require
    // X.509 or ASN.1.
    CertificateConfig config = 3 [(google.api.field_behavior) = IMMUTABLE];
  }

  // Output only. The resource name of the issuing
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // in the format `projects/*/locations/*/caPools/*/certificateAuthorities/*`.
  string issuer_certificate_authority = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Required. Immutable. The desired lifetime of a certificate. Used to create
  // the "not_before_time" and "not_after_time" fields inside an X.509
  // certificate. Note that the lifetime may be truncated if it would extend
  // past the life of any certificate authority in the issuing chain.
  google.protobuf.Duration lifetime = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Immutable. The resource name for a
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
  // used to issue this certificate, in the format
  // `projects/*/locations/*/certificateTemplates/*`.
  // If this is specified, the caller must have the necessary permission to
  // use this template. If this is omitted, no template will be used.
  // This template must be in the same location as the
  // [Certificate][google.cloud.security.privateca.v1.Certificate].
  string certificate_template = 6 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateTemplate"
    }
  ];

  // Immutable. Specifies how the
  // [Certificate][google.cloud.security.privateca.v1.Certificate]'s identity
  // fields are to be decided. If this is omitted, the `DEFAULT` subject mode
  // will be used.
  SubjectRequestMode subject_mode = 7 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Details regarding the revocation of this
  // [Certificate][google.cloud.security.privateca.v1.Certificate]. This
  // [Certificate][google.cloud.security.privateca.v1.Certificate] is considered
  // revoked if and only if this field is present.
  RevocationDetails revocation_details = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The pem-encoded, signed X.509 certificate.
  string pem_certificate = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A structured description of the issued X.509 certificate.
  CertificateDescription certificate_description = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The chain that may be used to verify the X.509 certificate.
  // Expected to be in issuer-to-root order according to RFC 5246.
  repeated string pem_certificate_chain = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this
  // [Certificate][google.cloud.security.privateca.v1.Certificate] was created.
  google.protobuf.Timestamp create_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this
  // [Certificate][google.cloud.security.privateca.v1.Certificate] was updated.
  google.protobuf.Timestamp update_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels with user-defined metadata.
  map<string, string> labels = 14 [(google.api.field_behavior) = OPTIONAL];
}

// A
// [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
// refers to a managed template for certificate issuance.
message CertificateTemplate {
  option (google.api.resource) = {
    type: "privateca.googleapis.com/CertificateTemplate"
    pattern: "projects/{project}/locations/{location}/certificateTemplates/{certificate_template}"
  };

  // Output only. The resource name for this
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
  // in the format `projects/*/locations/*/certificateTemplates/*`.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateTemplate"
    }
  ];

  // Optional. The maximum lifetime allowed for issued
  // [Certificates][google.cloud.security.privateca.v1.Certificate] that use
  // this template. If the issuing
  // [CaPool][google.cloud.security.privateca.v1.CaPool] resource's
  // [IssuancePolicy][google.cloud.security.privateca.v1.CaPool.IssuancePolicy]
  // specifies a
  // [maximum_lifetime][google.cloud.security.privateca.v1.CaPool.IssuancePolicy.maximum_lifetime]
  // the minimum of the two durations will be the maximum lifetime for issued
  // [Certificates][google.cloud.security.privateca.v1.Certificate]. Note that
  // if the issuing
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // expires before a
  // [Certificate][google.cloud.security.privateca.v1.Certificate]'s requested
  // maximum_lifetime, the effective lifetime will be explicitly truncated
  //  to match it.
  google.protobuf.Duration maximum_lifetime = 9
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A set of X.509 values that will be applied to all issued
  // certificates that use this template. If the certificate request includes
  // conflicting values for the same properties, they will be overwritten by the
  // values defined here. If the issuing
  // [CaPool][google.cloud.security.privateca.v1.CaPool]'s
  // [IssuancePolicy][google.cloud.security.privateca.v1.CaPool.IssuancePolicy]
  // defines conflicting
  // [baseline_values][google.cloud.security.privateca.v1.CaPool.IssuancePolicy.baseline_values]
  // for the same properties, the certificate issuance request will fail.
  X509Parameters predefined_values = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes constraints on identities that may be appear in
  // [Certificates][google.cloud.security.privateca.v1.Certificate] issued using
  // this template. If this is omitted, then this template will not add
  // restrictions on a certificate's identity.
  CertificateIdentityConstraints identity_constraints = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes the set of X.509 extensions that may appear in a
  // [Certificate][google.cloud.security.privateca.v1.Certificate] issued using
  // this
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate].
  // If a certificate request sets extensions that don't appear in the
  // [passthrough_extensions][google.cloud.security.privateca.v1.CertificateTemplate.passthrough_extensions],
  // those extensions will be dropped. If the issuing
  // [CaPool][google.cloud.security.privateca.v1.CaPool]'s
  // [IssuancePolicy][google.cloud.security.privateca.v1.CaPool.IssuancePolicy]
  // defines
  // [baseline_values][google.cloud.security.privateca.v1.CaPool.IssuancePolicy.baseline_values]
  // that don't appear here, the certificate issuance request will fail. If this
  // is omitted, then this template will not add restrictions on a certificate's
  // X.509 extensions. These constraints do not apply to X.509 extensions set in
  // this
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]'s
  // [predefined_values][google.cloud.security.privateca.v1.CertificateTemplate.predefined_values].
  CertificateExtensionConstraints passthrough_extensions = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A human-readable description of scenarios this template is
  // intended for.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time at which this
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
  // was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this
  // [CertificateTemplate][google.cloud.security.privateca.v1.CertificateTemplate]
  // was updated.
  google.protobuf.Timestamp update_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels with user-defined metadata.
  map<string, string> labels = 8 [(google.api.field_behavior) = OPTIONAL];
}

// An [X509Parameters][google.cloud.security.privateca.v1.X509Parameters] is
// used to describe certain fields of an X.509 certificate, such as the key
// usage fields, fields specific to CA certificates, certificate policy
// extensions and custom extensions.
message X509Parameters {
  // Describes values that are relevant in a CA certificate.
  message CaOptions {
    // Optional. Refers to the "CA" X.509 extension, which is a boolean value.
    // When this value is missing, the extension will be omitted from the CA
    // certificate.
    optional bool is_ca = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Refers to the path length restriction X.509 extension. For a CA
    // certificate, this value describes the depth of subordinate CA
    // certificates that are allowed.
    // If this value is less than 0, the request will fail.
    // If this value is missing, the max path length will be omitted from the
    // CA certificate.
    optional int32 max_issuer_path_length = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Describes the X.509 name constraints extension, per
  // https://tools.ietf.org/html/rfc5280#section-*******0
  message NameConstraints {
    // Indicates whether or not the name constraints are marked critical.
    bool critical = 1;

    // Contains permitted DNS names. Any DNS name that can be
    // constructed by simply adding zero or more labels to
    // the left-hand side of the name satisfies the name constraint.
    // For example, `example.com`, `www.example.com`, `www.sub.example.com`
    // would satisfy `example.com` while `example1.com` does not.
    repeated string permitted_dns_names = 2;

    // Contains excluded DNS names. Any DNS name that can be
    // constructed by simply adding zero or more labels to
    // the left-hand side of the name satisfies the name constraint.
    // For example, `example.com`, `www.example.com`, `www.sub.example.com`
    // would satisfy `example.com` while `example1.com` does not.
    repeated string excluded_dns_names = 3;

    // Contains the permitted IP ranges. For IPv4 addresses, the ranges
    // are expressed using CIDR notation as specified in RFC 4632.
    // For IPv6 addresses, the ranges are expressed in similar encoding as IPv4
    // addresses.
    repeated string permitted_ip_ranges = 4;

    // Contains the excluded IP ranges. For IPv4 addresses, the ranges
    // are expressed using CIDR notation as specified in RFC 4632.
    // For IPv6 addresses, the ranges are expressed in similar encoding as IPv4
    // addresses.
    repeated string excluded_ip_ranges = 5;

    // Contains the permitted email addresses. The value can be a particular
    // email address, a hostname to indicate all email addresses on that host or
    // a domain with a leading period (e.g. `.example.com`) to indicate
    // all email addresses in that domain.
    repeated string permitted_email_addresses = 6;

    // Contains the excluded email addresses. The value can be a particular
    // email address, a hostname to indicate all email addresses on that host or
    // a domain with a leading period (e.g. `.example.com`) to indicate
    // all email addresses in that domain.
    repeated string excluded_email_addresses = 7;

    // Contains the permitted URIs that apply to the host part of the name.
    // The value can be a hostname or a domain with a
    // leading period (like `.example.com`)
    repeated string permitted_uris = 8;

    // Contains the excluded URIs that apply to the host part of the name.
    // The value can be a hostname or a domain with a
    // leading period (like `.example.com`)
    repeated string excluded_uris = 9;
  }

  // Optional. Indicates the intended use for keys that correspond to a
  // certificate.
  KeyUsage key_usage = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes options in this
  // [X509Parameters][google.cloud.security.privateca.v1.X509Parameters] that
  // are relevant in a CA certificate.
  CaOptions ca_options = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes the X.509 certificate policy object identifiers, per
  // https://tools.ietf.org/html/rfc5280#section-*******.
  repeated ObjectId policy_ids = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes Online Certificate Status Protocol (OCSP) endpoint
  // addresses that appear in the "Authority Information Access" extension in
  // the certificate.
  repeated string aia_ocsp_servers = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes the X.509 name constraints extension.
  NameConstraints name_constraints = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes custom X.509 extensions.
  repeated X509Extension additional_extensions = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Describes a subordinate CA's issuers. This is either a resource name to a
// known issuing
// [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority],
// or a PEM issuer certificate chain.
message SubordinateConfig {
  // This message describes a subordinate CA's issuer certificate chain. This
  // wrapper exists for compatibility reasons.
  message SubordinateConfigChain {
    // Required. Expected to be in leaf-to-root order according to RFC 5246.
    repeated string pem_certificates = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  oneof subordinate_config {
    // Required. This can refer to a
    // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
    // that was used to create a subordinate
    // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority].
    // This field is used for information and usability purposes only. The
    // resource name is in the format
    // `projects/*/locations/*/caPools/*/certificateAuthorities/*`.
    string certificate_authority = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "privateca.googleapis.com/CertificateAuthority"
      }
    ];

    // Required. Contains the PEM certificate chain for the issuers of this
    // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority],
    // but not pem certificate for this CA itself.
    SubordinateConfigChain pem_issuer_chain = 2
        [(google.api.field_behavior) = REQUIRED];
  }
}

// A [PublicKey][google.cloud.security.privateca.v1.PublicKey] describes a
// public key.
message PublicKey {
  // Types of public keys formats that are supported. Currently, only `PEM`
  // format is supported.
  enum KeyFormat {
    // Default unspecified value.
    KEY_FORMAT_UNSPECIFIED = 0;

    // The key is PEM-encoded as defined in [RFC
    // 7468](https://tools.ietf.org/html/rfc7468). It can be any of the
    // following: a PEM-encoded PKCS#1/RFC 3447 RSAPublicKey
    // structure, an RFC 5280
    // [SubjectPublicKeyInfo](https://tools.ietf.org/html/rfc5280#section-4.1)
    // or a PEM-encoded X.509 certificate signing request (CSR). If a
    // [SubjectPublicKeyInfo](https://tools.ietf.org/html/rfc5280#section-4.1)
    // is specified, it can contain a A PEM-encoded PKCS#1/RFC 3447 RSAPublicKey
    // or a NIST P-256/secp256r1/prime256v1 or P-384 key. If a CSR is specified,
    // it will used solely for the purpose of extracting the public key. When
    // generated by the service, it will always be an RFC 5280
    // [SubjectPublicKeyInfo](https://tools.ietf.org/html/rfc5280#section-4.1)
    // structure containing an algorithm identifier and a key.
    PEM = 1;
  }

  // Required. A public key. The padding and encoding
  // must match with the `KeyFormat` value specified for the `format` field.
  bytes key = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The format of the public key.
  KeyFormat format = 2 [(google.api.field_behavior) = REQUIRED];
}

// A [CertificateConfig][google.cloud.security.privateca.v1.CertificateConfig]
// describes an X.509 certificate or CSR that is to be created, as an
// alternative to using ASN.1.
message CertificateConfig {
  // These values are used to create the distinguished name and subject
  // alternative name fields in an X.509 certificate.
  message SubjectConfig {
    // Optional. Contains distinguished name fields such as the common name,
    // location and organization.
    Subject subject = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The subject alternative name fields.
    SubjectAltNames subject_alt_name = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // A KeyId identifies a specific public key, usually by hashing the public
  // key.
  message KeyId {
    // Required. The value of this KeyId encoded in lowercase hexadecimal. This
    // is most likely the 160 bit SHA-1 hash of the public key.
    string key_id = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. Specifies some of the values in a certificate that are related to
  // the subject.
  SubjectConfig subject_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Describes how some of the technical X.509 fields in a certificate
  // should be populated.
  X509Parameters x509_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The public key that corresponds to this config. This is, for
  // example, used when issuing
  // [Certificates][google.cloud.security.privateca.v1.Certificate], but not
  // when creating a self-signed
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // or
  // [CertificateAuthority][google.cloud.security.privateca.v1.CertificateAuthority]
  // CSR.
  PublicKey public_key = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. When specified this provides a custom SKI to be used in the
  // certificate. This should only be used to maintain a SKI of an existing CA
  // originally created outside CA service, which was not generated using method
  // (1) described in RFC 5280 section *******.
  KeyId subject_key_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A
// [CertificateDescription][google.cloud.security.privateca.v1.CertificateDescription]
// describes an X.509 certificate or CSR that has been issued, as an alternative
// to using ASN.1 / X.509.
message CertificateDescription {
  // These values describe fields in an issued X.509 certificate such as the
  // distinguished name, subject alternative names, serial number, and lifetime.
  message SubjectDescription {
    // Contains distinguished name fields such as the common name, location and
    // / organization.
    Subject subject = 1;

    // The subject alternative name fields.
    SubjectAltNames subject_alt_name = 2;

    // The serial number encoded in lowercase hexadecimal.
    string hex_serial_number = 3;

    // For convenience, the actual lifetime of an issued certificate.
    google.protobuf.Duration lifetime = 4;

    // The time at which the certificate becomes valid.
    google.protobuf.Timestamp not_before_time = 5;

    // The time after which the certificate is expired.
    // Per RFC 5280, the validity period for a certificate is the period of time
    // from not_before_time through not_after_time, inclusive.
    // Corresponds to 'not_before_time' + 'lifetime' - 1 second.
    google.protobuf.Timestamp not_after_time = 6;
  }

  // A KeyId identifies a specific public key, usually by hashing the public
  // key.
  message KeyId {
    // Optional. The value of this KeyId encoded in lowercase hexadecimal. This
    // is most likely the 160 bit SHA-1 hash of the public key.
    string key_id = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // A group of fingerprints for the x509 certificate.
  message CertificateFingerprint {
    // The SHA 256 hash, encoded in hexadecimal, of the DER x509 certificate.
    string sha256_hash = 1;
  }

  // Describes some of the values in a certificate that are related to the
  // subject and lifetime.
  SubjectDescription subject_description = 1;

  // Describes some of the technical X.509 fields in a certificate.
  X509Parameters x509_description = 2;

  // The public key that corresponds to an issued certificate.
  PublicKey public_key = 3;

  // Provides a means of identifiying certificates that contain a particular
  // public key, per https://tools.ietf.org/html/rfc5280#section-*******.
  KeyId subject_key_id = 4;

  // Identifies the subject_key_id of the parent certificate, per
  // https://tools.ietf.org/html/rfc5280#section-*******
  KeyId authority_key_id = 5;

  // Describes a list of locations to obtain CRL information, i.e.
  // the DistributionPoint.fullName described by
  // https://tools.ietf.org/html/rfc5280#section-*******3
  repeated string crl_distribution_points = 6;

  // Describes lists of issuer CA certificate URLs that appear in the
  // "Authority Information Access" extension in the certificate.
  repeated string aia_issuing_certificate_urls = 7;

  // The hash of the x.509 certificate.
  CertificateFingerprint cert_fingerprint = 8;
}

// An [ObjectId][google.cloud.security.privateca.v1.ObjectId] specifies an
// object identifier (OID). These provide context and describe types in ASN.1
// messages.
message ObjectId {
  // Required. The parts of an OID path. The most significant parts of the path
  // come first.
  repeated int32 object_id_path = 1 [(google.api.field_behavior) = REQUIRED];
}

// An [X509Extension][google.cloud.security.privateca.v1.X509Extension]
// specifies an X.509 extension, which may be used in different parts of X.509
// objects like certificates, CSRs, and CRLs.
message X509Extension {
  // Required. The OID for this X.509 extension.
  ObjectId object_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Indicates whether or not this extension is critical (i.e., if the
  // client does not know how to handle this extension, the client should
  // consider this to be an error).
  bool critical = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The value of this X.509 extension.
  bytes value = 3 [(google.api.field_behavior) = REQUIRED];
}

// A [KeyUsage][google.cloud.security.privateca.v1.KeyUsage] describes key usage
// values that may appear in an X.509 certificate.
message KeyUsage {
  // [KeyUsage.KeyUsageOptions][google.cloud.security.privateca.v1.KeyUsage.KeyUsageOptions]
  // corresponds to the key usage values described in
  // https://tools.ietf.org/html/rfc5280#section-*******.
  message KeyUsageOptions {
    // The key may be used for digital signatures.
    bool digital_signature = 1;

    // The key may be used for cryptographic commitments. Note that this may
    // also be referred to as "non-repudiation".
    bool content_commitment = 2;

    // The key may be used to encipher other keys.
    bool key_encipherment = 3;

    // The key may be used to encipher data.
    bool data_encipherment = 4;

    // The key may be used in a key agreement protocol.
    bool key_agreement = 5;

    // The key may be used to sign certificates.
    bool cert_sign = 6;

    // The key may be used sign certificate revocation lists.
    bool crl_sign = 7;

    // The key may be used to encipher only.
    bool encipher_only = 8;

    // The key may be used to decipher only.
    bool decipher_only = 9;
  }

  // [KeyUsage.ExtendedKeyUsageOptions][google.cloud.security.privateca.v1.KeyUsage.ExtendedKeyUsageOptions]
  // has fields that correspond to certain common OIDs that could be specified
  // as an extended key usage value.
  message ExtendedKeyUsageOptions {
    // Corresponds to OID *******.*******.1. Officially described as "TLS WWW
    // server authentication", though regularly used for non-WWW TLS.
    bool server_auth = 1;

    // Corresponds to OID *******.*******.2. Officially described as "TLS WWW
    // client authentication", though regularly used for non-WWW TLS.
    bool client_auth = 2;

    // Corresponds to OID *******.*******.3. Officially described as "Signing of
    // downloadable executable code client authentication".
    bool code_signing = 3;

    // Corresponds to OID *******.*******.4. Officially described as "Email
    // protection".
    bool email_protection = 4;

    // Corresponds to OID *******.*******.8. Officially described as "Binding
    // the hash of an object to a time".
    bool time_stamping = 5;

    // Corresponds to OID *******.*******.9. Officially described as "Signing
    // OCSP responses".
    bool ocsp_signing = 6;
  }

  // Describes high-level ways in which a key may be used.
  KeyUsageOptions base_key_usage = 1;

  // Detailed scenarios in which a key may be used.
  ExtendedKeyUsageOptions extended_key_usage = 2;

  // Used to describe extended key usages that are not listed in the
  // [KeyUsage.ExtendedKeyUsageOptions][google.cloud.security.privateca.v1.KeyUsage.ExtendedKeyUsageOptions]
  // message.
  repeated ObjectId unknown_extended_key_usages = 3;
}

// [Subject][google.cloud.security.privateca.v1.Subject] describes parts of a
// distinguished name that, in turn, describes the subject of the certificate.
message Subject {
  // The "common name" of the subject.
  string common_name = 1;

  // The country code of the subject.
  string country_code = 2;

  // The organization of the subject.
  string organization = 3;

  // The organizational_unit of the subject.
  string organizational_unit = 4;

  // The locality or city of the subject.
  string locality = 5;

  // The province, territory, or regional state of the subject.
  string province = 6;

  // The street address of the subject.
  string street_address = 7;

  // The postal code of the subject.
  string postal_code = 8;
}

// [SubjectAltNames][google.cloud.security.privateca.v1.SubjectAltNames]
// corresponds to a more modern way of listing what the asserted identity is in
// a certificate (i.e., compared to the "common name" in the distinguished
// name).
message SubjectAltNames {
  // Contains only valid, fully-qualified host names.
  repeated string dns_names = 1;

  // Contains only valid RFC 3986 URIs.
  repeated string uris = 2;

  // Contains only valid RFC 2822 E-mail addresses.
  repeated string email_addresses = 3;

  // Contains only valid 32-bit IPv4 addresses or RFC 4291 IPv6 addresses.
  repeated string ip_addresses = 4;

  // Contains additional subject alternative name values.
  // For each custom_san, the `value` field must contain an ASN.1 encoded
  // UTF8String.
  repeated X509Extension custom_sans = 5;
}

// Describes constraints on a
// [Certificate][google.cloud.security.privateca.v1.Certificate]'s
// [Subject][google.cloud.security.privateca.v1.Subject] and
// [SubjectAltNames][google.cloud.security.privateca.v1.SubjectAltNames].
message CertificateIdentityConstraints {
  // Optional. A CEL expression that may be used to validate the resolved X.509
  // Subject and/or Subject Alternative Name before a certificate is signed. To
  // see the full allowed syntax and some examples, see
  // https://cloud.google.com/certificate-authority-service/docs/using-cel
  google.type.Expr cel_expression = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. If this is true, the
  // [Subject][google.cloud.security.privateca.v1.Subject] field may be copied
  // from a certificate request into the signed certificate. Otherwise, the
  // requested [Subject][google.cloud.security.privateca.v1.Subject] will be
  // discarded.
  optional bool allow_subject_passthrough = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. If this is true, the
  // [SubjectAltNames][google.cloud.security.privateca.v1.SubjectAltNames]
  // extension may be copied from a certificate request into the signed
  // certificate. Otherwise, the requested
  // [SubjectAltNames][google.cloud.security.privateca.v1.SubjectAltNames] will
  // be discarded.
  optional bool allow_subject_alt_names_passthrough = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Describes a set of X.509 extensions that may be part of some certificate
// issuance controls.
message CertificateExtensionConstraints {
  // Describes well-known X.509 extensions that can appear in a
  // [Certificate][google.cloud.security.privateca.v1.Certificate], not
  // including the
  // [SubjectAltNames][google.cloud.security.privateca.v1.SubjectAltNames]
  // extension.
  enum KnownCertificateExtension {
    // Not specified.
    KNOWN_CERTIFICATE_EXTENSION_UNSPECIFIED = 0;

    // Refers to a certificate's Key Usage extension, as described in [RFC 5280
    // section *******](https://tools.ietf.org/html/rfc5280#section-*******).
    // This corresponds to the
    // [KeyUsage.base_key_usage][google.cloud.security.privateca.v1.KeyUsage.base_key_usage]
    // field.
    BASE_KEY_USAGE = 1;

    // Refers to a certificate's Extended Key Usage extension, as described in
    // [RFC 5280
    // section *******2](https://tools.ietf.org/html/rfc5280#section-*******2).
    // This corresponds to the
    // [KeyUsage.extended_key_usage][google.cloud.security.privateca.v1.KeyUsage.extended_key_usage]
    // message.
    EXTENDED_KEY_USAGE = 2;

    // Refers to a certificate's Basic Constraints extension, as described in
    // [RFC 5280
    // section 4.2.1.9](https://tools.ietf.org/html/rfc5280#section-4.2.1.9).
    // This corresponds to the
    // [X509Parameters.ca_options][google.cloud.security.privateca.v1.X509Parameters.ca_options]
    // field.
    CA_OPTIONS = 3;

    // Refers to a certificate's Policy object identifiers, as described in
    // [RFC 5280
    // section *******](https://tools.ietf.org/html/rfc5280#section-*******).
    // This corresponds to the
    // [X509Parameters.policy_ids][google.cloud.security.privateca.v1.X509Parameters.policy_ids]
    // field.
    POLICY_IDS = 4;

    // Refers to OCSP servers in a certificate's Authority Information Access
    // extension, as described in
    // [RFC 5280
    // section 4.2.2.1](https://tools.ietf.org/html/rfc5280#section-4.2.2.1),
    // This corresponds to the
    // [X509Parameters.aia_ocsp_servers][google.cloud.security.privateca.v1.X509Parameters.aia_ocsp_servers]
    // field.
    AIA_OCSP_SERVERS = 5;

    // Refers to Name Constraints extension as described in
    // [RFC 5280
    // section *******0](https://tools.ietf.org/html/rfc5280#section-*******0)
    NAME_CONSTRAINTS = 6;
  }

  // Optional. A set of named X.509 extensions. Will be combined with
  // [additional_extensions][google.cloud.security.privateca.v1.CertificateExtensionConstraints.additional_extensions]
  // to determine the full set of X.509 extensions.
  repeated KnownCertificateExtension known_extensions = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A set of [ObjectIds][google.cloud.security.privateca.v1.ObjectId]
  // identifying custom X.509 extensions. Will be combined with
  // [known_extensions][google.cloud.security.privateca.v1.CertificateExtensionConstraints.known_extensions]
  // to determine the full set of X.509 extensions.
  repeated ObjectId additional_extensions = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// A [RevocationReason][google.cloud.security.privateca.v1.RevocationReason]
// indicates whether a
// [Certificate][google.cloud.security.privateca.v1.Certificate] has been
// revoked, and the reason for revocation. These correspond to standard
// revocation reasons from RFC 5280. Note that the enum labels and values in
// this definition are not the same ASN.1 values defined in RFC 5280. These
// values will be translated to the correct ASN.1 values when a CRL is created.
enum RevocationReason {
  // Default unspecified value. This value does indicate that a
  // [Certificate][google.cloud.security.privateca.v1.Certificate] has been
  // revoked, but that a reason has not been recorded.
  REVOCATION_REASON_UNSPECIFIED = 0;

  // Key material for this
  // [Certificate][google.cloud.security.privateca.v1.Certificate] may have
  // leaked.
  KEY_COMPROMISE = 1;

  // The key material for a certificate authority in the issuing path may have
  // leaked.
  CERTIFICATE_AUTHORITY_COMPROMISE = 2;

  // The subject or other attributes in this
  // [Certificate][google.cloud.security.privateca.v1.Certificate] have changed.
  AFFILIATION_CHANGED = 3;

  // This [Certificate][google.cloud.security.privateca.v1.Certificate] has been
  // superseded.
  SUPERSEDED = 4;

  // This [Certificate][google.cloud.security.privateca.v1.Certificate] or
  // entities in the issuing path have ceased to operate.
  CESSATION_OF_OPERATION = 5;

  // This [Certificate][google.cloud.security.privateca.v1.Certificate] should
  // not be considered valid, it is expected that it may become valid in the
  // future.
  CERTIFICATE_HOLD = 6;

  // This [Certificate][google.cloud.security.privateca.v1.Certificate] no
  // longer has permission to assert the listed attributes.
  PRIVILEGE_WITHDRAWN = 7;

  // The authority which determines appropriate attributes for a
  // [Certificate][google.cloud.security.privateca.v1.Certificate] may have been
  // compromised.
  ATTRIBUTE_AUTHORITY_COMPROMISE = 8;
}

// Describes the way in which a
// [Certificate][google.cloud.security.privateca.v1.Certificate]'s
// [Subject][google.cloud.security.privateca.v1.Subject] and/or
// [SubjectAltNames][google.cloud.security.privateca.v1.SubjectAltNames] will be
// resolved.
enum SubjectRequestMode {
  // Not specified.
  SUBJECT_REQUEST_MODE_UNSPECIFIED = 0;

  // The default mode used in most cases. Indicates that the certificate's
  // [Subject][google.cloud.security.privateca.v1.Subject] and/or
  // [SubjectAltNames][google.cloud.security.privateca.v1.SubjectAltNames] are
  // specified in the certificate request. This mode requires the caller to have
  // the `privateca.certificates.create` permission.
  DEFAULT = 1;

  // A mode reserved for special cases. Indicates that the certificate should
  // have one SPIFFE
  // [SubjectAltNames][google.cloud.security.privateca.v1.SubjectAltNames] set
  // by the service based on the caller's identity. This mode will ignore any
  // explicitly specified [Subject][google.cloud.security.privateca.v1.Subject]
  // and/or
  // [SubjectAltNames][google.cloud.security.privateca.v1.SubjectAltNames] in
  // the certificate request. This mode requires the caller to have the
  // `privateca.certificates.createForSelf` permission.
  REFLECTED_SPIFFE = 2;
}
