// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.security.privateca.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Security.PrivateCA.V1Beta1";
option go_package = "cloud.google.com/go/security/privateca/apiv1beta1/privatecapb;privatecapb";
option java_multiple_files = true;
option java_outer_classname = "PrivateCaResourcesProto";
option java_package = "com.google.cloud.security.privateca.v1beta1";
option php_namespace = "Google\\Cloud\\Security\\PrivateCA\\V1beta1";
option ruby_package = "Google::Cloud::Security::PrivateCA::V1beta1";

// A [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] represents an individual Certificate Authority.
// A [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] can be used to create [Certificates][google.cloud.security.privateca.v1beta1.Certificate].
message CertificateAuthority {
  option (google.api.resource) = {
    type: "privateca.googleapis.com/CertificateAuthority"
    pattern: "projects/{project}/locations/{location}/certificateAuthorities/{certificate_authority}"
  };

  // Options that affect all certificates issued by a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  message IssuingOptions {
    // Required. When true, includes a URL to the issuing CA certificate in the
    // "authority information access" X.509 extension.
    bool include_ca_cert_url = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. When true, includes a URL to the CRL corresponding to certificates
    // issued from a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
    // CRLs will expire 7 days from their creation. However, we will rebuild
    // daily. CRLs are also rebuilt shortly after a certificate is revoked.
    bool include_crl_access_url = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // The issuing policy for a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  // [Certificates][google.cloud.security.privateca.v1beta1.Certificate] will not be successfully issued from this
  // [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] if they violate the policy.
  message CertificateAuthorityPolicy {
    message AllowedConfigList {
      // Required. All [Certificates][google.cloud.security.privateca.v1beta1.Certificate] issued by the [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]
      // must match at least one listed [ReusableConfigWrapper][google.cloud.security.privateca.v1beta1.ReusableConfigWrapper]. If a
      // [ReusableConfigWrapper][google.cloud.security.privateca.v1beta1.ReusableConfigWrapper] has an empty field, any value will be
      // allowed for that field.
      repeated ReusableConfigWrapper allowed_config_values = 1 [(google.api.field_behavior) = REQUIRED];
    }

    // [AllowedSubjectAltNames][google.cloud.security.privateca.v1beta1.CertificateAuthority.CertificateAuthorityPolicy.AllowedSubjectAltNames] specifies the allowed values for
    // [SubjectAltNames][google.cloud.security.privateca.v1beta1.SubjectAltNames] by the [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] when issuing
    // [Certificates][google.cloud.security.privateca.v1beta1.Certificate].
    message AllowedSubjectAltNames {
      // Optional. Contains valid, fully-qualified host names. Glob patterns are also
      // supported. To allow an explicit wildcard certificate, escape with
      // backlash (i.e. `\*`).
      // E.g. for globbed entries: `*bar.com` will allow `foo.bar.com`, but not
      // `*.bar.com`, unless the [allow_globbing_dns_wildcards][google.cloud.security.privateca.v1beta1.CertificateAuthority.CertificateAuthorityPolicy.AllowedSubjectAltNames.allow_globbing_dns_wildcards] field is set.
      // E.g. for wildcard entries: `\*.bar.com` will allow `*.bar.com`, but not
      // `foo.bar.com`.
      repeated string allowed_dns_names = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Contains valid RFC 3986 URIs. Glob patterns are also supported. To
      // match across path seperators (i.e. '/') use the double star glob
      // pattern (i.e. '**').
      repeated string allowed_uris = 2 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Contains valid RFC 2822 E-mail addresses. Glob patterns are also
      // supported.
      repeated string allowed_email_addresses = 3 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Contains valid 32-bit IPv4 addresses and subnet ranges or RFC 4291 IPv6
      // addresses and subnet ranges. Subnet ranges are specified using the
      // '/' notation (e.g. 10.0.0.0/8, 2001:700:300:1800::/64). Glob patterns
      // are supported only for ip address entries (i.e. not for subnet ranges).
      repeated string allowed_ips = 4 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Specifies if glob patterns used for [allowed_dns_names][google.cloud.security.privateca.v1beta1.CertificateAuthority.CertificateAuthorityPolicy.AllowedSubjectAltNames.allowed_dns_names] allows
      // wildcard certificates.
      bool allow_globbing_dns_wildcards = 5 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Specifies if to allow custom X509Extension values.
      bool allow_custom_sans = 6 [(google.api.field_behavior) = OPTIONAL];
    }

    // [IssuanceModes][google.cloud.security.privateca.v1beta1.CertificateAuthority.CertificateAuthorityPolicy.IssuanceModes] specifies the allowed ways in which
    // [Certificates][google.cloud.security.privateca.v1beta1.Certificate] may be requested from this
    // [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
    message IssuanceModes {
      // Required. When true, allows callers to create [Certificates][google.cloud.security.privateca.v1beta1.Certificate] by
      // specifying a CSR.
      bool allow_csr_based_issuance = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. When true, allows callers to create [Certificates][google.cloud.security.privateca.v1beta1.Certificate] by
      // specifying a [CertificateConfig][google.cloud.security.privateca.v1beta1.CertificateConfig].
      bool allow_config_based_issuance = 2 [(google.api.field_behavior) = REQUIRED];
    }

    // Allowed configurations or a single configuration for all issued
    // certificates.
    oneof config_policy {
      // Optional. All [Certificates][google.cloud.security.privateca.v1beta1.Certificate] issued by the [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]
      // must match at least one listed [ReusableConfigWrapper][google.cloud.security.privateca.v1beta1.ReusableConfigWrapper] in the list.
      AllowedConfigList allowed_config_list = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. All [Certificates][google.cloud.security.privateca.v1beta1.Certificate] issued by the [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]
      // will use the provided configuration values, overwriting any requested
      // configuration values.
      ReusableConfigWrapper overwrite_config_values = 2 [(google.api.field_behavior) = OPTIONAL];
    }

    // Optional. If any [Subject][google.cloud.security.privateca.v1beta1.Subject] is specified here, then all
    // [Certificates][google.cloud.security.privateca.v1beta1.Certificate] issued by the [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] must
    // match at least one listed [Subject][google.cloud.security.privateca.v1beta1.Subject]. If a [Subject][google.cloud.security.privateca.v1beta1.Subject] has an empty
    // field, any value will be allowed for that field.
    repeated Subject allowed_locations_and_organizations = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If any value is specified here, then all
    // [Certificates][google.cloud.security.privateca.v1beta1.Certificate] issued by the [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] must
    // match at least one listed value. If no value is specified, all values
    // will be allowed for this fied. Glob patterns are also supported.
    repeated string allowed_common_names = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If a [AllowedSubjectAltNames][google.cloud.security.privateca.v1beta1.CertificateAuthority.CertificateAuthorityPolicy.AllowedSubjectAltNames] is specified here, then all
    // [Certificates][google.cloud.security.privateca.v1beta1.Certificate] issued by the [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] must
    // match [AllowedSubjectAltNames][google.cloud.security.privateca.v1beta1.CertificateAuthority.CertificateAuthorityPolicy.AllowedSubjectAltNames]. If no value or an empty value
    // is specified, any value will be allowed for the [SubjectAltNames][google.cloud.security.privateca.v1beta1.SubjectAltNames]
    // field.
    AllowedSubjectAltNames allowed_sans = 5 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The maximum lifetime allowed by the [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]. Note that
    // if the any part if the issuing chain expires before a [Certificate][google.cloud.security.privateca.v1beta1.Certificate]'s
    // requested maximum_lifetime, the effective lifetime will be explicitly
    // truncated.
    google.protobuf.Duration maximum_lifetime = 6 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If specified, then only methods allowed in the [IssuanceModes][google.cloud.security.privateca.v1beta1.CertificateAuthority.CertificateAuthorityPolicy.IssuanceModes] may be
    // used to issue [Certificates][google.cloud.security.privateca.v1beta1.Certificate].
    IssuanceModes allowed_issuance_modes = 8 [(google.api.field_behavior) = OPTIONAL];
  }

  // URLs where a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] will publish content.
  message AccessUrls {
    // The URL where this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]'s CA certificate is
    // published. This will only be set for CAs that have been activated.
    string ca_certificate_access_url = 1;

    // The URL where this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]'s CRLs are published. This
    // will only be set for CAs that have been activated.
    string crl_access_url = 2;
  }

  // A Cloud KMS key configuration that a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] will use.
  message KeyVersionSpec {
    oneof KeyVersion {
      // Required. The resource name for an existing Cloud KMS CryptoKeyVersion in the
      // format
      // `projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*`.
      // This option enables full flexibility in the key's capabilities and
      // properties.
      string cloud_kms_key_version = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. The algorithm to use for creating a managed Cloud KMS key for a for a
      // simplified experience. All managed keys will be have their
      // [ProtectionLevel][google.cloud.kms.v1.ProtectionLevel] as `HSM`.
      SignHashAlgorithm algorithm = 2 [(google.api.field_behavior) = REQUIRED];
    }
  }

  // The type of a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority], indicating its issuing chain.
  enum Type {
    // Not specified.
    TYPE_UNSPECIFIED = 0;

    // Self-signed CA.
    SELF_SIGNED = 1;

    // Subordinate CA. Could be issued by a Private CA [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]
    // or an unmanaged CA.
    SUBORDINATE = 2;
  }

  // The tier of a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority], indicating its supported
  // functionality and/or billing SKU.
  enum Tier {
    // Not specified.
    TIER_UNSPECIFIED = 0;

    // Enterprise tier.
    ENTERPRISE = 1;

    // DevOps tier.
    DEVOPS = 2;
  }

  // The state of a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority], indicating if it can be used.
  enum State {
    // Not specified.
    STATE_UNSPECIFIED = 0;

    // Certificates can be issued from this CA. CRLs will be generated for this
    // CA.
    ENABLED = 1;

    // Certificates cannot be issued from this CA. CRLs will still be generated.
    DISABLED = 2;

    // Certificates cannot be issued from this CA. CRLs will not be generated.
    PENDING_ACTIVATION = 3;

    // Certificates cannot be issued from this CA. CRLs will not be generated.
    PENDING_DELETION = 4;
  }

  // The algorithm of a Cloud KMS CryptoKeyVersion of a
  // [CryptoKey][google.cloud.kms.v1.CryptoKey] with the
  // [CryptoKeyPurpose][google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose] value
  // `ASYMMETRIC_SIGN`. These values correspond to the
  // [CryptoKeyVersionAlgorithm][google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionAlgorithm]
  // values. For RSA signing algorithms, the PSS algorithms should be preferred,
  // use PKCS1 algorithms if required for compatibility. For further
  // recommandations, see
  // https://cloud.google.com/kms/docs/algorithms#algorithm_recommendations.
  enum SignHashAlgorithm {
    // Not specified.
    SIGN_HASH_ALGORITHM_UNSPECIFIED = 0;

    // maps to CryptoKeyVersionAlgorithm.RSA_SIGN_PSS_2048_SHA256
    RSA_PSS_2048_SHA256 = 1;

    // maps to CryptoKeyVersionAlgorithm. RSA_SIGN_PSS_3072_SHA256
    RSA_PSS_3072_SHA256 = 2;

    // maps to CryptoKeyVersionAlgorithm.RSA_SIGN_PSS_4096_SHA256
    RSA_PSS_4096_SHA256 = 3;

    // maps to CryptoKeyVersionAlgorithm.RSA_SIGN_PKCS1_2048_SHA256
    RSA_PKCS1_2048_SHA256 = 6;

    // maps to CryptoKeyVersionAlgorithm.RSA_SIGN_PKCS1_3072_SHA256
    RSA_PKCS1_3072_SHA256 = 7;

    // maps to CryptoKeyVersionAlgorithm.RSA_SIGN_PKCS1_4096_SHA256
    RSA_PKCS1_4096_SHA256 = 8;

    // maps to CryptoKeyVersionAlgorithm.EC_SIGN_P256_SHA256
    EC_P256_SHA256 = 4;

    // maps to CryptoKeyVersionAlgorithm.EC_SIGN_P384_SHA384
    EC_P384_SHA384 = 5;
  }

  // Output only. The resource name for this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] in the
  // format `projects/*/locations/*/certificateAuthorities/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. The [Type][google.cloud.security.privateca.v1beta1.CertificateAuthority.Type] of this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  Type type = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Immutable. The [Tier][google.cloud.security.privateca.v1beta1.CertificateAuthority.Tier] of this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  Tier tier = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Immutable. The config used to create a self-signed X.509 certificate or CSR.
  CertificateConfig config = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. The desired lifetime of the CA certificate. Used to create the
  // "not_before_time" and "not_after_time" fields inside an X.509
  // certificate.
  google.protobuf.Duration lifetime = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. Used when issuing certificates for this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]. If this
  // [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] is a self-signed CertificateAuthority, this key
  // is also used to sign the self-signed CA certificate. Otherwise, it
  // is used to sign a CSR.
  KeyVersionSpec key_spec = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. The [CertificateAuthorityPolicy][google.cloud.security.privateca.v1beta1.CertificateAuthority.CertificateAuthorityPolicy] to enforce when issuing
  // [Certificates][google.cloud.security.privateca.v1beta1.Certificate] from this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  CertificateAuthorityPolicy certificate_policy = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The [IssuingOptions][google.cloud.security.privateca.v1beta1.CertificateAuthority.IssuingOptions] to follow when issuing [Certificates][google.cloud.security.privateca.v1beta1.Certificate]
  // from this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  IssuingOptions issuing_options = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If this is a subordinate [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority], this field will be set
  // with the subordinate configuration, which describes its issuers. This may
  // be updated, but this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] must continue to validate.
  SubordinateConfig subordinate_config = 19 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The [State][google.cloud.security.privateca.v1beta1.CertificateAuthority.State] for this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]'s certificate chain, including the current
  // [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]'s certificate. Ordered such that the root issuer
  // is the final element (consistent with RFC 5246). For a self-signed CA, this
  // will only list the current [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]'s certificate.
  repeated string pem_ca_certificates = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A structured description of this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]'s CA certificate
  // and its issuers. Ordered as self-to-root.
  repeated CertificateDescription ca_certificate_descriptions = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The name of a Cloud Storage bucket where this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] will
  // publish content, such as the CA certificate and CRLs. This must be a bucket
  // name, without any prefixes (such as `gs://`) or suffixes (such as
  // `.googleapis.com`). For example, to use a bucket named `my-bucket`, you
  // would simply specify `my-bucket`. If not specified, a managed bucket will
  // be created.
  string gcs_bucket = 13 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. URLs for accessing content published by this CA, such as the CA certificate
  // and CRLs.
  AccessUrls access_urls = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] was created.
  google.protobuf.Timestamp create_time = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] was updated.
  google.protobuf.Timestamp update_time = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] will be deleted, if
  // scheduled for deletion.
  google.protobuf.Timestamp delete_time = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels with user-defined metadata.
  map<string, string> labels = 18 [(google.api.field_behavior) = OPTIONAL];
}

// A [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList] corresponds to a signed X.509 certificate
// Revocation List (CRL). A CRL contains the serial numbers of certificates that
// should no longer be trusted.
message CertificateRevocationList {
  option (google.api.resource) = {
    type: "privateca.googleapis.com/CertificateRevocationList"
    pattern: "projects/{project}/locations/{location}/certificateAuthorities/{certificate_authority}/certificateRevocationLists/{certificate_revocation_list}"
  };

  // Describes a revoked [Certificate][google.cloud.security.privateca.v1beta1.Certificate].
  message RevokedCertificate {
    // The resource path for the [Certificate][google.cloud.security.privateca.v1beta1.Certificate] in the format
    // `projects/*/locations/*/certificateAuthorities/*/certificates/*`.
    string certificate = 1;

    // The serial number of the [Certificate][google.cloud.security.privateca.v1beta1.Certificate].
    string hex_serial_number = 2;

    // The reason the [Certificate][google.cloud.security.privateca.v1beta1.Certificate] was revoked.
    RevocationReason revocation_reason = 3;
  }

  // The state of a [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList], indicating if it is current.
  enum State {
    // Not specified.
    STATE_UNSPECIFIED = 0;

    // The [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList] is up to date.
    ACTIVE = 1;

    // The [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList] is no longer current.
    SUPERSEDED = 2;
  }

  // Output only. The resource path for this [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList] in
  // the format
  // `projects/*/locations/*/certificateAuthorities/*/
  //    certificateRevocationLists/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The CRL sequence number that appears in pem_crl.
  int64 sequence_number = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The revoked serial numbers that appear in pem_crl.
  repeated RevokedCertificate revoked_certificates = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The PEM-encoded X.509 CRL.
  string pem_crl = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The location where 'pem_crl' can be accessed.
  string access_url = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The [State][google.cloud.security.privateca.v1beta1.CertificateRevocationList.State] for this [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList].
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList] was created.
  google.protobuf.Timestamp create_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList] was updated.
  google.protobuf.Timestamp update_time = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels with user-defined metadata.
  map<string, string> labels = 9 [(google.api.field_behavior) = OPTIONAL];
}

// A [Certificate][google.cloud.security.privateca.v1beta1.Certificate] corresponds to a signed X.509 certificate issued by a
// [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
message Certificate {
  option (google.api.resource) = {
    type: "privateca.googleapis.com/Certificate"
    pattern: "projects/{project}/locations/{location}/certificateAuthorities/{certificate_authority}/certificates/{certificate}"
  };

  // Describes fields that are relavent to the revocation of a [Certificate][google.cloud.security.privateca.v1beta1.Certificate].
  message RevocationDetails {
    // Indicates why a [Certificate][google.cloud.security.privateca.v1beta1.Certificate] was revoked.
    RevocationReason revocation_state = 1;

    // The time at which this [Certificate][google.cloud.security.privateca.v1beta1.Certificate] was revoked.
    google.protobuf.Timestamp revocation_time = 2;
  }

  // Output only. The resource path for this [Certificate][google.cloud.security.privateca.v1beta1.Certificate] in the format
  // `projects/*/locations/*/certificateAuthorities/*/certificates/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The config used to create a signed X.509 certificate.
  oneof certificate_config {
    // Immutable. A pem-encoded X.509 certificate signing request (CSR).
    string pem_csr = 2 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. A description of the certificate and key that does not require X.509 or
    // ASN.1.
    CertificateConfig config = 3 [(google.api.field_behavior) = IMMUTABLE];
  }

  // Required. Immutable. The desired lifetime of a certificate. Used to create the
  // "not_before_time" and "not_after_time" fields inside an X.509
  // certificate. Note that the lifetime may be truncated if it would extend
  // past the life of any certificate authority in the issuing chain.
  google.protobuf.Duration lifetime = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Details regarding the revocation of this [Certificate][google.cloud.security.privateca.v1beta1.Certificate]. This
  // [Certificate][google.cloud.security.privateca.v1beta1.Certificate] is considered revoked if and only if this field is present.
  RevocationDetails revocation_details = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The pem-encoded, signed X.509 certificate.
  string pem_certificate = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A structured description of the issued X.509 certificate.
  CertificateDescription certificate_description = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The chain that may be used to verify the X.509 certificate. Expected to be
  // in issuer-to-root order according to RFC 5246.
  repeated string pem_certificate_chain = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this [Certificate][google.cloud.security.privateca.v1beta1.Certificate] was created.
  google.protobuf.Timestamp create_time = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this [Certificate][google.cloud.security.privateca.v1beta1.Certificate] was updated.
  google.protobuf.Timestamp update_time = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels with user-defined metadata.
  map<string, string> labels = 11 [(google.api.field_behavior) = OPTIONAL];
}

// A [ReusableConfig][google.cloud.security.privateca.v1beta1.ReusableConfig] refers to a managed [ReusableConfigValues][google.cloud.security.privateca.v1beta1.ReusableConfigValues]. Those, in
// turn, are used to describe certain fields of an X.509 certificate, such as
// the key usage fields, fields specific to CA certificates, certificate policy
// extensions and custom extensions.
message ReusableConfig {
  option (google.api.resource) = {
    type: "privateca.googleapis.com/ReusableConfig"
    pattern: "projects/{project}/locations/{location}/reusableConfigs/{reusable_config}"
  };

  // Output only. The resource path for this [ReusableConfig][google.cloud.security.privateca.v1beta1.ReusableConfig] in the format
  // `projects/*/locations/*/reusableConfigs/*`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The config values.
  ReusableConfigValues values = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A human-readable description of scenarios these ReusableConfigValues may be
  // compatible with.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The time at which this [ReusableConfig][google.cloud.security.privateca.v1beta1.ReusableConfig] was created.
  google.protobuf.Timestamp create_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this [ReusableConfig][google.cloud.security.privateca.v1beta1.ReusableConfig] was updated.
  google.protobuf.Timestamp update_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels with user-defined metadata.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];
}

// A [ReusableConfigValues][google.cloud.security.privateca.v1beta1.ReusableConfigValues] is used to describe certain fields of an
// X.509 certificate, such as the key usage fields, fields specific to CA
// certificates, certificate policy extensions and custom extensions.
message ReusableConfigValues {
  // Describes values that are relevant in a CA certificate.
  message CaOptions {
    // Optional. Refers to the "CA" X.509 extension, which is a boolean value. When this
    // value is missing, the extension will be omitted from the CA certificate.
    google.protobuf.BoolValue is_ca = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Refers to the path length restriction X.509 extension. For a CA
    // certificate, this value describes the depth of subordinate CA
    // certificates that are allowed.
    // If this value is less than 0, the request will fail.
    // If this value is missing, the max path length will be omitted from the
    // CA certificate.
    google.protobuf.Int32Value max_issuer_path_length = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. Indicates the intended use for keys that correspond to a certificate.
  KeyUsage key_usage = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes options in this [ReusableConfigValues][google.cloud.security.privateca.v1beta1.ReusableConfigValues] that are
  // relevant in a CA certificate.
  CaOptions ca_options = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes the X.509 certificate policy object identifiers, per
  // https://tools.ietf.org/html/rfc5280#section-*******.
  repeated ObjectId policy_ids = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes Online Certificate Status Protocol (OCSP) endpoint addresses
  // that appear in the "Authority Information Access" extension in the
  // certificate.
  repeated string aia_ocsp_servers = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Describes custom X.509 extensions.
  repeated X509Extension additional_extensions = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A [ReusableConfigWrapper][google.cloud.security.privateca.v1beta1.ReusableConfigWrapper] describes values that may assist in creating an
// X.509 certificate, or a reference to a pre-defined set of values.
message ReusableConfigWrapper {
  // Reusable or inline config values.
  oneof config_values {
    // Required. A resource path to a [ReusableConfig][google.cloud.security.privateca.v1beta1.ReusableConfig] in the format
    // `projects/*/locations/*/reusableConfigs/*`.
    string reusable_config = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. A user-specified inline [ReusableConfigValues][google.cloud.security.privateca.v1beta1.ReusableConfigValues].
    ReusableConfigValues reusable_config_values = 2 [(google.api.field_behavior) = REQUIRED];
  }
}

// Describes a subordinate CA's issuers. This is either a resource path to a
// known issuing [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority], or a PEM issuer certificate chain.
message SubordinateConfig {
  // This message describes a subordinate CA's issuer certificate chain. This
  // wrapper exists for compatibility reasons.
  message SubordinateConfigChain {
    // Required. Expected to be in leaf-to-root order according to RFC 5246.
    repeated string pem_certificates = 1 [(google.api.field_behavior) = REQUIRED];
  }

  oneof subordinate_config {
    // Required. This can refer to a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] in the same project that
    // was used to create a subordinate [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]. This field
    // is used for information and usability purposes only. The resource name
    // is in the format `projects/*/locations/*/certificateAuthorities/*`.
    string certificate_authority = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Contains the PEM certificate chain for the issuers of this
    // [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority], but not pem certificate for this CA itself.
    SubordinateConfigChain pem_issuer_chain = 2 [(google.api.field_behavior) = REQUIRED];
  }
}

// A [PublicKey][google.cloud.security.privateca.v1beta1.PublicKey] describes a public key.
message PublicKey {
  // Types of public keys that are supported.
  // At a minimum, we support RSA and ECDSA, for the key sizes or curves listed:
  // https://cloud.google.com/kms/docs/algorithms#asymmetric_signing_algorithms
  enum KeyType {
    // Default unspecified value.
    KEY_TYPE_UNSPECIFIED = 0;

    // A PEM-encoded PKCS#1/RFC 3447 RSAPrivateKey structure.
    PEM_RSA_KEY = 1;

    // A PEM-encoded compressed NIST P-256/secp256r1/prime256v1 or P-384 key.
    PEM_EC_KEY = 2;
  }

  // Required. The type of public key.
  KeyType type = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A public key. Padding and encoding varies by 'KeyType' and is described
  // along with the KeyType values.
  bytes key = 2 [(google.api.field_behavior) = REQUIRED];
}

// A [CertificateConfig][google.cloud.security.privateca.v1beta1.CertificateConfig] describes an X.509 certificate or CSR that is to be
// created, as an alternative to using ASN.1.
message CertificateConfig {
  // These values are used to create the distinguished name and subject
  // alternative name fields in an X.509 certificate.
  message SubjectConfig {
    // Required. Contains distinguished name fields such as the location and organization.
    Subject subject = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. The "common name" of the distinguished name.
    string common_name = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The subject alternative name fields.
    SubjectAltNames subject_alt_name = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. Specifies some of the values in a certificate that are related to the
  // subject.
  SubjectConfig subject_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Describes how some of the technical fields in a certificate should be
  // populated.
  ReusableConfigWrapper reusable_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The public key that corresponds to this config. This is, for example, used
  // when issuing [Certificates][google.cloud.security.privateca.v1beta1.Certificate], but not when creating a
  // self-signed [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] or [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] CSR.
  PublicKey public_key = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A [CertificateDescription][google.cloud.security.privateca.v1beta1.CertificateDescription] describes an X.509 certificate or CSR that has
// been issued, as an alternative to using ASN.1 / X.509.
message CertificateDescription {
  // These values describe fields in an issued X.509 certificate such as the
  // distinguished name, subject alternative names, serial number, and lifetime.
  message SubjectDescription {
    // Contains distinguished name fields such as the location and organization.
    Subject subject = 1;

    // The "common name" of the distinguished name.
    string common_name = 2;

    // The subject alternative name fields.
    SubjectAltNames subject_alt_name = 3;

    // The serial number encoded in lowercase hexadecimal.
    string hex_serial_number = 4;

    // For convenience, the actual lifetime of an issued certificate.
    // Corresponds to 'not_after_time' - 'not_before_time'.
    google.protobuf.Duration lifetime = 5;

    // The time at which the certificate becomes valid.
    google.protobuf.Timestamp not_before_time = 6;

    // The time at which the certificate expires.
    google.protobuf.Timestamp not_after_time = 7;
  }

  // A KeyId identifies a specific public key, usually by hashing the public
  // key.
  message KeyId {
    // Optional. The value of this KeyId encoded in lowercase hexadecimal. This is most
    // likely the 160 bit SHA-1 hash of the public key.
    string key_id = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // A group of fingerprints for the x509 certificate.
  message CertificateFingerprint {
    // The SHA 256 hash, encoded in hexadecimal, of the DER x509 certificate.
    string sha256_hash = 1;
  }

  // Describes some of the values in a certificate that are related to the
  // subject and lifetime.
  SubjectDescription subject_description = 1;

  // Describes some of the technical fields in a certificate.
  ReusableConfigValues config_values = 2;

  // The public key that corresponds to an issued certificate.
  PublicKey public_key = 3;

  // Provides a means of identifiying certificates that contain a particular
  // public key, per https://tools.ietf.org/html/rfc5280#section-*******.
  KeyId subject_key_id = 4;

  // Identifies the subject_key_id of the parent certificate, per
  // https://tools.ietf.org/html/rfc5280#section-*******
  KeyId authority_key_id = 5;

  // Describes a list of locations to obtain CRL information, i.e.
  // the DistributionPoint.fullName described by
  // https://tools.ietf.org/html/rfc5280#section-********
  repeated string crl_distribution_points = 6;

  // Describes lists of issuer CA certificate URLs that appear in the
  // "Authority Information Access" extension in the certificate.
  repeated string aia_issuing_certificate_urls = 7;

  // The hash of the x.509 certificate.
  CertificateFingerprint cert_fingerprint = 8;
}

// An [ObjectId][google.cloud.security.privateca.v1beta1.ObjectId] specifies an object identifier (OID). These provide context
// and describe types in ASN.1 messages.
message ObjectId {
  // Required. The parts of an OID path. The most significant parts of the path come
  // first.
  repeated int32 object_id_path = 1 [(google.api.field_behavior) = REQUIRED];
}

// An [X509Extension][google.cloud.security.privateca.v1beta1.X509Extension] specifies an X.509 extension, which may be used in
// different parts of X.509 objects like certificates, CSRs, and CRLs.
message X509Extension {
  // Required. The OID for this X.509 extension.
  ObjectId object_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Indicates whether or not this extension is critical (i.e., if the client
  // does not know how to handle this extension, the client should consider this
  // to be an error).
  bool critical = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The value of this X.509 extension.
  bytes value = 3 [(google.api.field_behavior) = REQUIRED];
}

// A [KeyUsage][google.cloud.security.privateca.v1beta1.KeyUsage] describes key usage values that may appear in an X.509
// certificate.
message KeyUsage {
  // [KeyUsage.KeyUsageOptions][google.cloud.security.privateca.v1beta1.KeyUsage.KeyUsageOptions] corresponds to the key usage values
  // described in https://tools.ietf.org/html/rfc5280#section-*******.
  message KeyUsageOptions {
    // The key may be used for digital signatures.
    bool digital_signature = 1;

    // The key may be used for cryptographic commitments. Note that this may
    // also be referred to as "non-repudiation".
    bool content_commitment = 2;

    // The key may be used to encipher other keys.
    bool key_encipherment = 3;

    // The key may be used to encipher data.
    bool data_encipherment = 4;

    // The key may be used in a key agreement protocol.
    bool key_agreement = 5;

    // The key may be used to sign certificates.
    bool cert_sign = 6;

    // The key may be used sign certificate revocation lists.
    bool crl_sign = 7;

    // The key may be used to encipher only.
    bool encipher_only = 8;

    // The key may be used to decipher only.
    bool decipher_only = 9;
  }

  // [KeyUsage.ExtendedKeyUsageOptions][google.cloud.security.privateca.v1beta1.KeyUsage.ExtendedKeyUsageOptions] has fields that correspond to
  // certain common OIDs that could be specified as an extended key usage value.
  message ExtendedKeyUsageOptions {
    // Corresponds to OID *******.*******.1. Officially described as "TLS WWW
    // server authentication", though regularly used for non-WWW TLS.
    bool server_auth = 1;

    // Corresponds to OID *******.*******.2. Officially described as "TLS WWW
    // client authentication", though regularly used for non-WWW TLS.
    bool client_auth = 2;

    // Corresponds to OID *******.*******.3. Officially described as "Signing of
    // downloadable executable code client authentication".
    bool code_signing = 3;

    // Corresponds to OID *******.*******.4. Officially described as "Email
    // protection".
    bool email_protection = 4;

    // Corresponds to OID *******.*******.8. Officially described as "Binding
    // the hash of an object to a time".
    bool time_stamping = 5;

    // Corresponds to OID *******.*******.9. Officially described as "Signing
    // OCSP responses".
    bool ocsp_signing = 6;
  }

  // Describes high-level ways in which a key may be used.
  KeyUsageOptions base_key_usage = 1;

  // Detailed scenarios in which a key may be used.
  ExtendedKeyUsageOptions extended_key_usage = 2;

  // Used to describe extended key usages that are not listed in the
  // [KeyUsage.ExtendedKeyUsageOptions][google.cloud.security.privateca.v1beta1.KeyUsage.ExtendedKeyUsageOptions] message.
  repeated ObjectId unknown_extended_key_usages = 3;
}

// [Subject][google.cloud.security.privateca.v1beta1.Subject] describes parts of a distinguished name that, in turn,
// describes the subject of the certificate.
message Subject {
  // The country code of the subject.
  string country_code = 1;

  // The organization of the subject.
  string organization = 2;

  // The organizational_unit of the subject.
  string organizational_unit = 3;

  // The locality or city of the subject.
  string locality = 4;

  // The province, territory, or regional state of the subject.
  string province = 5;

  // The street address of the subject.
  string street_address = 6;

  // The postal code of the subject.
  string postal_code = 7;
}

// [SubjectAltNames][google.cloud.security.privateca.v1beta1.SubjectAltNames] corresponds to a more modern way of listing what
// the asserted identity is in a certificate (i.e., compared to the "common
// name" in the distinguished name).
message SubjectAltNames {
  // Contains only valid, fully-qualified host names.
  repeated string dns_names = 1;

  // Contains only valid RFC 3986 URIs.
  repeated string uris = 2;

  // Contains only valid RFC 2822 E-mail addresses.
  repeated string email_addresses = 3;

  // Contains only valid 32-bit IPv4 addresses or RFC 4291 IPv6 addresses.
  repeated string ip_addresses = 4;

  // Contains additional subject alternative name values.
  repeated X509Extension custom_sans = 5;
}

// A [RevocationReason][google.cloud.security.privateca.v1beta1.RevocationReason] indicates whether a [Certificate][google.cloud.security.privateca.v1beta1.Certificate] has been revoked,
// and the reason for revocation. These correspond to standard revocation
// reasons from RFC 5280. Note that the enum labels and values in this
// definition are not the same ASN.1 values defined in RFC 5280. These values
// will be translated to the correct ASN.1 values when a CRL is created.
enum RevocationReason {
  // Default unspecified value. This value does indicate that a [Certificate][google.cloud.security.privateca.v1beta1.Certificate]
  // has been revoked, but that a reason has not been recorded.
  REVOCATION_REASON_UNSPECIFIED = 0;

  // Key material for this [Certificate][google.cloud.security.privateca.v1beta1.Certificate] may have leaked.
  KEY_COMPROMISE = 1;

  // The key material for a certificate authority in the issuing path may have
  // leaked.
  CERTIFICATE_AUTHORITY_COMPROMISE = 2;

  // The subject or other attributes in this [Certificate][google.cloud.security.privateca.v1beta1.Certificate] have changed.
  AFFILIATION_CHANGED = 3;

  // This [Certificate][google.cloud.security.privateca.v1beta1.Certificate] has been superseded.
  SUPERSEDED = 4;

  // This [Certificate][google.cloud.security.privateca.v1beta1.Certificate] or entities in the issuing path have ceased to
  // operate.
  CESSATION_OF_OPERATION = 5;

  // This [Certificate][google.cloud.security.privateca.v1beta1.Certificate] should not be considered valid, it is expected that it
  // may become valid in the future.
  CERTIFICATE_HOLD = 6;

  // This [Certificate][google.cloud.security.privateca.v1beta1.Certificate] no longer has permission to assert the listed
  // attributes.
  PRIVILEGE_WITHDRAWN = 7;

  // The authority which determines appropriate attributes for a [Certificate][google.cloud.security.privateca.v1beta1.Certificate]
  // may have been compromised.
  ATTRIBUTE_AUTHORITY_COMPROMISE = 8;
}
