type: google.api.Service
config_version: 3
name: privateca.googleapis.com
title: Certificate Authority API

apis:
- name: google.cloud.security.privateca.v1beta1.CertificateAuthorityService

types:
- name: google.cloud.security.privateca.v1beta1.OperationMetadata

documentation:
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: 'google.cloud.security.privateca.v1beta1.CertificateAuthorityService.*'
    deadline: 60.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0
  - selector: google.longrunning.Operations.GetOperation
    deadline: 5.0

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1beta1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1beta1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1beta1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1beta1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.security.privateca.v1beta1.CertificateAuthorityService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
