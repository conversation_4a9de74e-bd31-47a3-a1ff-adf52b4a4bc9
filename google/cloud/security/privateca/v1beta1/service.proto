// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.security.privateca.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/security/privateca/v1beta1/resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Security.PrivateCA.V1Beta1";
option go_package = "cloud.google.com/go/security/privateca/apiv1beta1/privatecapb;privatecapb";
option java_multiple_files = true;
option java_outer_classname = "PrivateCaProto";
option java_package = "com.google.cloud.security.privateca.v1beta1";
option php_namespace = "Google\\Cloud\\Security\\PrivateCA\\V1beta1";
option ruby_package = "Google::Cloud::Security::PrivateCA::V1beta1";

// [Certificate Authority Service][google.cloud.security.privateca.v1beta1.CertificateAuthorityService] manages private
// certificate authorities and issued certificates.
service CertificateAuthorityService {
  option (google.api.default_host) = "privateca.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Create a new [Certificate][google.cloud.security.privateca.v1beta1.Certificate] in a given Project, Location from a particular
  // [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  rpc CreateCertificate(CreateCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/certificateAuthorities/*}/certificates"
      body: "certificate"
    };
    option (google.api.method_signature) = "parent,certificate,certificate_id";
  }

  // Returns a [Certificate][google.cloud.security.privateca.v1beta1.Certificate].
  rpc GetCertificate(GetCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/certificateAuthorities/*/certificates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [Certificates][google.cloud.security.privateca.v1beta1.Certificate].
  rpc ListCertificates(ListCertificatesRequest) returns (ListCertificatesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/certificateAuthorities/*}/certificates"
    };
    option (google.api.method_signature) = "parent";
  }

  // Revoke a [Certificate][google.cloud.security.privateca.v1beta1.Certificate].
  rpc RevokeCertificate(RevokeCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/certificateAuthorities/*/certificates/*}:revoke"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Update a [Certificate][google.cloud.security.privateca.v1beta1.Certificate]. Currently, the only field you can update is the
  // [labels][google.cloud.security.privateca.v1beta1.Certificate.labels] field.
  rpc UpdateCertificate(UpdateCertificateRequest) returns (Certificate) {
    option (google.api.http) = {
      patch: "/v1beta1/{certificate.name=projects/*/locations/*/certificateAuthorities/*/certificates/*}"
      body: "certificate"
    };
    option (google.api.method_signature) = "certificate,update_mask";
  }

  // Activate a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] that is in state
  // [PENDING_ACTIVATION][google.cloud.security.privateca.v1beta1.CertificateAuthority.State.PENDING_ACTIVATION] and is
  // of type [SUBORDINATE][google.cloud.security.privateca.v1beta1.CertificateAuthority.Type.SUBORDINATE]. After the
  // parent Certificate Authority signs a certificate signing request from
  // [FetchCertificateAuthorityCsr][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.FetchCertificateAuthorityCsr], this method can complete the activation
  // process.
  rpc ActivateCertificateAuthority(ActivateCertificateAuthorityRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/certificateAuthorities/*}:activate"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Create a new [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] in a given Project and Location.
  rpc CreateCertificateAuthority(CreateCertificateAuthorityRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/certificateAuthorities"
      body: "certificate_authority"
    };
    option (google.api.method_signature) = "parent,certificate_authority,certificate_authority_id";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Disable a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  rpc DisableCertificateAuthority(DisableCertificateAuthorityRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/certificateAuthorities/*}:disable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Enable a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  rpc EnableCertificateAuthority(EnableCertificateAuthorityRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/certificateAuthorities/*}:enable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Fetch a certificate signing request (CSR) from a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]
  // that is in state
  // [PENDING_ACTIVATION][google.cloud.security.privateca.v1beta1.CertificateAuthority.State.PENDING_ACTIVATION] and is
  // of type [SUBORDINATE][google.cloud.security.privateca.v1beta1.CertificateAuthority.Type.SUBORDINATE]. The CSR must
  // then be signed by the desired parent Certificate Authority, which could be
  // another [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] resource, or could be an on-prem
  // certificate authority. See also [ActivateCertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ActivateCertificateAuthority].
  rpc FetchCertificateAuthorityCsr(FetchCertificateAuthorityCsrRequest) returns (FetchCertificateAuthorityCsrResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/certificateAuthorities/*}:fetch"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  rpc GetCertificateAuthority(GetCertificateAuthorityRequest) returns (CertificateAuthority) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/certificateAuthorities/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [CertificateAuthorities][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  rpc ListCertificateAuthorities(ListCertificateAuthoritiesRequest) returns (ListCertificateAuthoritiesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/certificateAuthorities"
    };
    option (google.api.method_signature) = "parent";
  }

  // Restore a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] that is scheduled for deletion.
  rpc RestoreCertificateAuthority(RestoreCertificateAuthorityRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/certificateAuthorities/*}:restore"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Schedule a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] for deletion.
  rpc ScheduleDeleteCertificateAuthority(ScheduleDeleteCertificateAuthorityRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/certificateAuthorities/*}:scheduleDelete"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Update a [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  rpc UpdateCertificateAuthority(UpdateCertificateAuthorityRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{certificate_authority.name=projects/*/locations/*/certificateAuthorities/*}"
      body: "certificate_authority"
    };
    option (google.api.method_signature) = "certificate_authority,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateAuthority"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns a [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList].
  rpc GetCertificateRevocationList(GetCertificateRevocationListRequest) returns (CertificateRevocationList) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/certificateAuthorities/*/certificateRevocationLists/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [CertificateRevocationLists][google.cloud.security.privateca.v1beta1.CertificateRevocationList].
  rpc ListCertificateRevocationLists(ListCertificateRevocationListsRequest) returns (ListCertificateRevocationListsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/certificateAuthorities/*}/certificateRevocationLists"
    };
    option (google.api.method_signature) = "parent";
  }

  // Update a [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList].
  rpc UpdateCertificateRevocationList(UpdateCertificateRevocationListRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{certificate_revocation_list.name=projects/*/locations/*/certificateAuthorities/*/certificateRevocationLists/*}"
      body: "certificate_revocation_list"
    };
    option (google.api.method_signature) = "certificate_revocation_list,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "CertificateRevocationList"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns a [ReusableConfig][google.cloud.security.privateca.v1beta1.ReusableConfig].
  rpc GetReusableConfig(GetReusableConfigRequest) returns (ReusableConfig) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/reusableConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists [ReusableConfigs][google.cloud.security.privateca.v1beta1.ReusableConfig].
  rpc ListReusableConfigs(ListReusableConfigsRequest) returns (ListReusableConfigsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/reusableConfigs"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for [CertificateAuthorityService.CreateCertificate][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.CreateCertificate].
message CreateCertificateRequest {
  // Required. The resource name of the location and [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority]
  // associated with the [Certificate][google.cloud.security.privateca.v1beta1.Certificate], in the format
  // `projects/*/locations/*/certificateAuthorities/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. It must be unique within a location and match the regular
  // expression `[a-zA-Z0-9_-]{1,63}`. This field is required when using a
  // [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] in the Enterprise [CertificateAuthority.Tier][google.cloud.security.privateca.v1beta1.CertificateAuthority.Tier],
  // but is optional and its value is ignored otherwise.
  string certificate_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A [Certificate][google.cloud.security.privateca.v1beta1.Certificate] with initial field values.
  Certificate certificate = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.GetCertificate][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.GetCertificate].
message GetCertificateRequest {
  // Required. The [name][google.cloud.security.privateca.v1beta1.Certificate.name] of the [Certificate][google.cloud.security.privateca.v1beta1.Certificate] to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/Certificate"
    }
  ];
}

// Request message for [CertificateAuthorityService.ListCertificates][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ListCertificates].
message ListCertificatesRequest {
  // Required. The resource name of the location associated with the
  // [Certificates][google.cloud.security.privateca.v1beta1.Certificate], in the format
  // `projects/*/locations/*/certificateauthorities/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. Limit on the number of
  // [Certificates][google.cloud.security.privateca.v1beta1.Certificate] to include in the
  // response. Further [Certificates][google.cloud.security.privateca.v1beta1.Certificate] can subsequently be obtained
  // by including the
  // [ListCertificatesResponse.next_page_token][google.cloud.security.privateca.v1beta1.ListCertificatesResponse.next_page_token] in a subsequent
  // request. If unspecified, the server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pagination token, returned earlier via
  // [ListCertificatesResponse.next_page_token][google.cloud.security.privateca.v1beta1.ListCertificatesResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response. For details
  // on supported filters and syntax, see [Certificates Filtering
  // documentation](https://cloud.google.com/certificate-authority-service/docs/sorting-filtering-certificates#filtering_support).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted. For details on supported fields
  // and syntax, see [Certificates Sorting
  // documentation](https://cloud.google.com/certificate-authority-service/docs/sorting-filtering-certificates#sorting_support).
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for [CertificateAuthorityService.ListCertificates][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ListCertificates].
message ListCertificatesResponse {
  // The list of [Certificates][google.cloud.security.privateca.v1beta1.Certificate].
  repeated Certificate certificates = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListCertificatesRequest.next_page_token][] to retrieve the
  // next page of results.
  string next_page_token = 2;

  // A list of locations (e.g. "us-west1") that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [CertificateAuthorityService.RevokeCertificate][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.RevokeCertificate].
message RevokeCertificateRequest {
  // Required. The resource name for this [Certificate][google.cloud.security.privateca.v1beta1.Certificate] in the
  // format `projects/*/locations/*/certificateAuthorities/*/certificates/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/Certificate"
    }
  ];

  // Required. The [RevocationReason][google.cloud.security.privateca.v1beta1.RevocationReason] for revoking this certificate.
  RevocationReason reason = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for [CertificateAuthorityService.UpdateCertificate][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.UpdateCertificate].
message UpdateCertificateRequest {
  // Required. [Certificate][google.cloud.security.privateca.v1beta1.Certificate] with updated values.
  Certificate certificate = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A list of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.ActivateCertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ActivateCertificateAuthority].
message ActivateCertificateAuthorityRequest {
  // Required. The resource name for this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] in the
  // format `projects/*/locations/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Required. The signed CA certificate issued from
  // [FetchCertificateAuthorityCsrResponse.pem_csr][google.cloud.security.privateca.v1beta1.FetchCertificateAuthorityCsrResponse.pem_csr].
  string pem_ca_certificate = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Must include information about the issuer of 'pem_ca_certificate', and any
  // further issuers until the self-signed CA.
  SubordinateConfig subordinate_config = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.CreateCertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.CreateCertificateAuthority].
message CreateCertificateAuthorityRequest {
  // Required. The resource name of the location associated with the
  // [CertificateAuthorities][google.cloud.security.privateca.v1beta1.CertificateAuthority], in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. It must be unique within a location and match the regular
  // expression `[a-zA-Z0-9_-]{1,63}`
  string certificate_authority_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] with initial field values.
  CertificateAuthority certificate_authority = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.DisableCertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.DisableCertificateAuthority].
message DisableCertificateAuthorityRequest {
  // Required. The resource name for this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] in the
  // format `projects/*/locations/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.EnableCertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.EnableCertificateAuthority].
message EnableCertificateAuthorityRequest {
  // Required. The resource name for this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] in the
  // format `projects/*/locations/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.FetchCertificateAuthorityCsr][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.FetchCertificateAuthorityCsr].
message FetchCertificateAuthorityCsrRequest {
  // Required. The resource name for this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] in the
  // format `projects/*/locations/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];
}

// Response message for
// [CertificateAuthorityService.FetchCertificateAuthorityCsr][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.FetchCertificateAuthorityCsr].
message FetchCertificateAuthorityCsrResponse {
  // Output only. The PEM-encoded signed certificate signing request (CSR).
  string pem_csr = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for [CertificateAuthorityService.GetCertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.GetCertificateAuthority].
message GetCertificateAuthorityRequest {
  // Required. The [name][google.cloud.security.privateca.v1beta1.CertificateAuthority.name] of the [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] to
  // get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];
}

// Request message for
// [CertificateAuthorityService.ListCertificateAuthorities][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ListCertificateAuthorities].
message ListCertificateAuthoritiesRequest {
  // Required. The resource name of the location associated with the
  // [CertificateAuthorities][google.cloud.security.privateca.v1beta1.CertificateAuthority], in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Limit on the number of [CertificateAuthorities][google.cloud.security.privateca.v1beta1.CertificateAuthority] to
  // include in the response.
  // Further [CertificateAuthorities][google.cloud.security.privateca.v1beta1.CertificateAuthority] can subsequently be
  // obtained by including the
  // [ListCertificateAuthoritiesResponse.next_page_token][google.cloud.security.privateca.v1beta1.ListCertificateAuthoritiesResponse.next_page_token] in a subsequent
  // request. If unspecified, the server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pagination token, returned earlier via
  // [ListCertificateAuthoritiesResponse.next_page_token][google.cloud.security.privateca.v1beta1.ListCertificateAuthoritiesResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CertificateAuthorityService.ListCertificateAuthorities][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ListCertificateAuthorities].
message ListCertificateAuthoritiesResponse {
  // The list of [CertificateAuthorities][google.cloud.security.privateca.v1beta1.CertificateAuthority].
  repeated CertificateAuthority certificate_authorities = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListCertificateAuthoritiesRequest.next_page_token][] to retrieve the next
  // page of results.
  string next_page_token = 2;

  // A list of locations (e.g. "us-west1") that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [CertificateAuthorityService.RestoreCertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.RestoreCertificateAuthority].
message RestoreCertificateAuthorityRequest {
  // Required. The resource name for this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] in the
  // format `projects/*/locations/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.ScheduleDeleteCertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ScheduleDeleteCertificateAuthority].
message ScheduleDeleteCertificateAuthorityRequest {
  // Required. The resource name for this [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] in the
  // format `projects/*/locations/*/certificateAuthorities/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.UpdateCertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.UpdateCertificateAuthority].
message UpdateCertificateAuthorityRequest {
  // Required. [CertificateAuthority][google.cloud.security.privateca.v1beta1.CertificateAuthority] with updated values.
  CertificateAuthority certificate_authority = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A list of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.GetCertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.GetCertificateRevocationList].
message GetCertificateRevocationListRequest {
  // Required. The [name][google.cloud.security.privateca.v1beta1.CertificateRevocationList.name] of the
  // [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList] to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateRevocationList"
    }
  ];
}

// Request message for
// [CertificateAuthorityService.ListCertificateRevocationLists][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ListCertificateRevocationLists].
message ListCertificateRevocationListsRequest {
  // Required. The resource name of the location associated with the
  // [CertificateRevocationLists][google.cloud.security.privateca.v1beta1.CertificateRevocationList], in the format
  // `projects/*/locations/*/certificateauthorities/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/CertificateAuthority"
    }
  ];

  // Optional. Limit on the number of
  // [CertificateRevocationLists][google.cloud.security.privateca.v1beta1.CertificateRevocationList] to include in the
  // response. Further [CertificateRevocationLists][google.cloud.security.privateca.v1beta1.CertificateRevocationList]
  // can subsequently be obtained by including the
  // [ListCertificateRevocationListsResponse.next_page_token][google.cloud.security.privateca.v1beta1.ListCertificateRevocationListsResponse.next_page_token] in a subsequent
  // request. If unspecified, the server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pagination token, returned earlier via
  // [ListCertificateRevocationListsResponse.next_page_token][google.cloud.security.privateca.v1beta1.ListCertificateRevocationListsResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CertificateAuthorityService.ListCertificateRevocationLists][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ListCertificateRevocationLists].
message ListCertificateRevocationListsResponse {
  // The list of [CertificateRevocationLists][google.cloud.security.privateca.v1beta1.CertificateRevocationList].
  repeated CertificateRevocationList certificate_revocation_lists = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListCertificateRevocationListsRequest.next_page_token][] to retrieve the
  // next page of results.
  string next_page_token = 2;

  // A list of locations (e.g. "us-west1") that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [CertificateAuthorityService.UpdateCertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.UpdateCertificateRevocationList].
message UpdateCertificateRevocationListRequest {
  // Required. [CertificateRevocationList][google.cloud.security.privateca.v1beta1.CertificateRevocationList] with updated values.
  CertificateRevocationList certificate_revocation_list = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A list of fields to be updated in this request.
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An ID to identify requests. Specify a unique request ID so that if you must
  // retry your request, the server will know to ignore the request if it has
  // already been completed. The server will guarantee that for at least 60
  // minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [CertificateAuthorityService.GetReusableConfig][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.GetReusableConfig].
message GetReusableConfigRequest {
  // Required. The [name][ReusableConfigs.name] of the [ReusableConfigs][] to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "privateca.googleapis.com/ReusableConfig"
    }
  ];
}

// Request message for
// [CertificateAuthorityService.ListReusableConfigs][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ListReusableConfigs].
message ListReusableConfigsRequest {
  // Required. The resource name of the location associated with the
  // [ReusableConfigs][google.cloud.security.privateca.v1beta1.ReusableConfig], in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Limit on the number of
  // [ReusableConfigs][google.cloud.security.privateca.v1beta1.ReusableConfig] to include in the response.
  // Further [ReusableConfigs][google.cloud.security.privateca.v1beta1.ReusableConfig] can subsequently be
  // obtained by including the
  // [ListReusableConfigsResponse.next_page_token][google.cloud.security.privateca.v1beta1.ListReusableConfigsResponse.next_page_token] in a subsequent request. If
  // unspecified, the server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Pagination token, returned earlier via
  // [ListReusableConfigsResponse.next_page_token][google.cloud.security.privateca.v1beta1.ListReusableConfigsResponse.next_page_token].
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only include resources that match the filter in the response.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify how the results should be sorted.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CertificateAuthorityService.ListReusableConfigs][google.cloud.security.privateca.v1beta1.CertificateAuthorityService.ListReusableConfigs].
message ListReusableConfigsResponse {
  // The list of [ReusableConfigs][google.cloud.security.privateca.v1beta1.ReusableConfig].
  repeated ReusableConfig reusable_configs = 1;

  // A token to retrieve next page of results. Pass this value in
  // [ListReusableConfigsRequest.next_page_token][] to retrieve
  // the next page of results.
  string next_page_token = 2;

  // A list of locations (e.g. "us-west1") that could not be reached.
  repeated string unreachable = 3;
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
