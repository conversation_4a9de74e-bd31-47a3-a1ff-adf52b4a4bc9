# This build file includes a target for the Ruby wrapper library for
# google-cloud-security-public_ca.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for publicca.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "publicca_ruby_wrapper",
    srcs = ["//google/cloud/security/publicca/v1beta1:publicca_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=publicca.googleapis.com",
        "ruby-cloud-api-shortname=publicca",
        "ruby-cloud-gem-name=google-cloud-security-public_ca",
        "ruby-cloud-gem-namespace=Google::Cloud::Security::PublicCA",
        "ruby-cloud-wrapper-of=v1beta1:0.5",
        "ruby-cloud-product-url=https://cloud.google.com/certificate-manager/docs/public-ca/",
    ],
    ruby_cloud_description = "Certificate Manager's Public Certificate Authority (CA) functionality allows you to provision and deploy widely trusted X.509 certificates after validating that the certificate requester controls the domains. Certificate Manager lets you directly and programmatically request publicly trusted TLS certificates that are already in the root of trust stores used by major browsers, operating systems, and applications. You can use these TLS certificates to authenticate and encrypt internet traffic.",
    ruby_cloud_title = "Public Certificate Authority",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-security-publicca-ruby",
    deps = [
        ":publicca_ruby_wrapper",
    ],
)
