type: google.api.Service
config_version: 3
name: publicca.googleapis.com
title: Public Certificate Authority API

apis:
- name: google.cloud.security.publicca.v1beta1.PublicCertificateAuthorityService

documentation:
  summary: |-
    The Public Certificate Authority API may be used to create and manage ACME
    external account binding keys associated with Google Trust Services'
    publicly trusted certificate authority.

backend:
  rules:
  - selector: google.cloud.security.publicca.v1beta1.PublicCertificateAuthorityService.CreateExternalAccountKey
    deadline: 5.0

authentication:
  rules:
  - selector: google.cloud.security.publicca.v1beta1.PublicCertificateAuthorityService.CreateExternalAccountKey
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
