// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.security.publicca.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Security.PublicCA.V1Beta1";
option go_package = "cloud.google.com/go/security/publicca/apiv1beta1/publiccapb;publiccapb";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.cloud.security.publicca.v1beta1";
option php_namespace = "Google\\Cloud\\Security\\PublicCA\\V1beta1";
option ruby_package = "Google::Cloud::Security::PublicCA::V1beta1";

// A representation of an ExternalAccountKey used for [external account
// binding](https://tools.ietf.org/html/rfc8555#section-7.3.4) within ACME.
message ExternalAccountKey {
  option (google.api.resource) = {
    type: "publicca.googleapis.com/ExternalAccountKey"
    pattern: "projects/{project}/locations/{location}/externalAccountKeys/{external_account_key}"
  };

  // Output only. Resource name.
  // projects/{project}/locations/{location}/externalAccountKeys/{key_id}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Key ID.
  // It is generated by the PublicCertificateAuthorityService
  // when the ExternalAccountKey is created
  string key_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Base64-URL-encoded HS256 key.
  // It is generated by the PublicCertificateAuthorityService
  // when the ExternalAccountKey is created
  bytes b64_mac_key = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
