# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "publicca_proto",
    srcs = [
        "resources.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
    ],
)

proto_library_with_info(
    name = "publicca_proto_with_info",
    deps = [
        ":publicca_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "publicca_java_proto",
    deps = [":publicca_proto"],
)

java_grpc_library(
    name = "publicca_java_grpc",
    srcs = [":publicca_proto"],
    deps = [":publicca_java_proto"],
)

java_gapic_library(
    name = "publicca_java_gapic",
    srcs = [":publicca_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "publicca_v1beta1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "publicca_v1beta1.yaml",
    test_deps = [
        ":publicca_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":publicca_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "publicca_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.security.publicca.v1beta1.PublicCertificateAuthorityServiceClientHttpJsonTest",
        "com.google.cloud.security.publicca.v1beta1.PublicCertificateAuthorityServiceClientTest",
    ],
    runtime_deps = [":publicca_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-security-publicca-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":publicca_java_gapic",
        ":publicca_java_grpc",
        ":publicca_java_proto",
        ":publicca_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "publicca_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/security/publicca/apiv1beta1/publiccapb",
    protos = [":publicca_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "publicca_go_gapic",
    srcs = [":publicca_proto_with_info"],
    grpc_service_config = "publicca_v1beta1_grpc_service_config.json",
    importpath = "cloud.google.com/go/security/publicca/apiv1beta1;publicca",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "publicca_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":publicca_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-security-publicca-v1beta1-go",
    deps = [
        ":publicca_go_gapic",
        ":publicca_go_gapic_srcjar-metadata.srcjar",
        ":publicca_go_gapic_srcjar-snippets.srcjar",
        ":publicca_go_gapic_srcjar-test.srcjar",
        ":publicca_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "publicca_py_gapic",
    srcs = [":publicca_proto"],
    grpc_service_config = "publicca_v1beta1_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-public-ca"],
    rest_numeric_enums = True,
    service_yaml = "publicca_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "publicca_py_gapic_test",
    srcs = [
        "publicca_py_gapic_pytest.py",
        "publicca_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":publicca_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "security-publicca-v1beta1-py",
    deps = [
        ":publicca_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "publicca_php_proto",
    deps = [":publicca_proto"],
)

php_gapic_library(
    name = "publicca_php_gapic",
    srcs = [":publicca_proto_with_info"],
    grpc_service_config = "publicca_v1beta1_grpc_service_config.json",
    migration_mode = "MIGRATING",
    rest_numeric_enums = True,
    service_yaml = "publicca_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":publicca_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-security-publicca-v1beta1-php",
    deps = [
        ":publicca_php_gapic",
        ":publicca_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "publicca_nodejs_gapic",
    package_name = "@google-cloud/publicca",
    src = ":publicca_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "publicca_v1beta1_grpc_service_config.json",
    package = "google.cloud.security.publicca.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "publicca_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "security-publicca-v1beta1-nodejs",
    deps = [
        ":publicca_nodejs_gapic",
        ":publicca_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "publicca_ruby_proto",
    deps = [":publicca_proto"],
)

ruby_grpc_library(
    name = "publicca_ruby_grpc",
    srcs = [":publicca_proto"],
    deps = [":publicca_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "publicca_ruby_gapic",
    srcs = [":publicca_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=publicca.googleapis.com",
        "ruby-cloud-api-shortname=publicca",
        "ruby-cloud-gem-name=google-cloud-security-public_ca-v1beta1",
        "ruby-cloud-gem-namespace=Google::Cloud::Security::PublicCA::V1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/certificate-manager/docs/public-ca/",
    ],
    grpc_service_config = "publicca_v1beta1_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Certificate Manager's Public Certificate Authority (CA) functionality allows you to provision and deploy widely trusted X.509 certificates after validating that the certificate requester controls the domains. Certificate Manager lets you directly and programmatically request publicly trusted TLS certificates that are already in the root of trust stores used by major browsers, operating systems, and applications. You can use these TLS certificates to authenticate and encrypt internet traffic.",
    ruby_cloud_title = "Public Certificate Authority V1beta1",
    service_yaml = "publicca_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":publicca_ruby_grpc",
        ":publicca_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-security-publicca-v1beta1-ruby",
    deps = [
        ":publicca_ruby_gapic",
        ":publicca_ruby_grpc",
        ":publicca_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "publicca_csharp_proto",
    extra_opts = [],
    deps = [":publicca_proto"],
)

csharp_grpc_library(
    name = "publicca_csharp_grpc",
    srcs = [":publicca_proto"],
    deps = [":publicca_csharp_proto"],
)

csharp_gapic_library(
    name = "publicca_csharp_gapic",
    srcs = [":publicca_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "publicca_v1beta1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "publicca_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":publicca_csharp_grpc",
        ":publicca_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-security-publicca-v1beta1-csharp",
    deps = [
        ":publicca_csharp_gapic",
        ":publicca_csharp_grpc",
        ":publicca_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "publicca_cc_proto",
    deps = [":publicca_proto"],
)

cc_grpc_library(
    name = "publicca_cc_grpc",
    srcs = [":publicca_proto"],
    grpc_only = True,
    deps = [":publicca_cc_proto"],
)
