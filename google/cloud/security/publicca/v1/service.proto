// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.security.publicca.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/security/publicca/v1/resources.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Security.PublicCA.V1";
option go_package = "cloud.google.com/go/security/publicca/apiv1/publiccapb;publiccapb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.security.publicca.v1";
option php_namespace = "Google\\Cloud\\Security\\PublicCA\\V1";
option ruby_package = "Google::Cloud::Security::PublicCA::V1";

// Manages the resources required for ACME [external account
// binding](https://tools.ietf.org/html/rfc8555#section-7.3.4) for
// the public certificate authority service.
service PublicCertificateAuthorityService {
  option (google.api.default_host) = "publicca.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new
  // [ExternalAccountKey][google.cloud.security.publicca.v1.ExternalAccountKey]
  // bound to the project.
  rpc CreateExternalAccountKey(CreateExternalAccountKeyRequest)
      returns (ExternalAccountKey) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/externalAccountKeys"
      body: "external_account_key"
    };
    option (google.api.method_signature) = "parent,external_account_key";
  }
}

// Creates a new
// [ExternalAccountKey][google.cloud.security.publicca.v1.ExternalAccountKey] in
// a given project.
message CreateExternalAccountKeyRequest {
  // Required. The parent resource where this external_account_key will be
  // created. Format: projects/[project_id]/locations/[location]. At present
  // only the "global" location is supported.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "publicca.googleapis.com/ExternalAccountKey"
    }
  ];

  // Required. The external account key to create. This field only exists to
  // future-proof the API. At present, all fields in ExternalAccountKey are
  // output only and all values are ignored. For the purpose of the
  // CreateExternalAccountKeyRequest, set it to a default/empty value.
  ExternalAccountKey external_account_key = 2
      [(google.api.field_behavior) = REQUIRED];
}
