type: google.api.Service
config_version: 3
name: publicca.googleapis.com
title: Public Certificate Authority API

apis:
- name: google.cloud.security.publicca.v1.PublicCertificateAuthorityService

documentation:
  summary: |-
    The Public Certificate Authority API may be used to create and manage ACME
    external account binding keys associated with Google Trust Services'
    publicly trusted certificate authority.

authentication:
  rules:
  - selector: google.cloud.security.publicca.v1.PublicCertificateAuthorityService.CreateExternalAccountKey
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://cloud.google.com/certificate-manager/docs/getting-support
  documentation_uri: https://cloud.google.com/certificate-manager/docs/public-ca
  api_short_name: publicca
  github_label: 'api: publicca'
  doc_tag_prefix: publicca
  organization: CLOUD
  library_settings:
  - version: google.cloud.security.publicca.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/certificate-manager/docs/reference/public-ca/rpc
