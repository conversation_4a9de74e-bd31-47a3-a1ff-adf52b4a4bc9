# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "mediatranslation_proto",
    srcs = [
        "media_translation.proto",
    ],
    deps = [
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/rpc:status_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "mediatranslation_java_proto",
    deps = [":mediatranslation_proto"],
)

java_grpc_library(
    name = "mediatranslation_java_grpc",
    srcs = [":mediatranslation_proto"],
    deps = [":mediatranslation_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "mediatranslation_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/mediatranslation/apiv1alpha1/mediatranslationpb",
    protos = [":mediatranslation_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "mediatranslation_moved_proto",
    srcs = [":mediatranslation_proto"],
    deps = [
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/rpc:status_proto",
    ],
)

py_proto_library(
    name = "mediatranslation_py_proto",
    deps = [":mediatranslation_moved_proto"],
)

py_grpc_library(
    name = "mediatranslation_py_grpc",
    srcs = [":mediatranslation_moved_proto"],
    deps = [":mediatranslation_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "mediatranslation_php_proto",
    deps = [":mediatranslation_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "mediatranslation_ruby_proto",
    deps = [":mediatranslation_proto"],
)

ruby_grpc_library(
    name = "mediatranslation_ruby_grpc",
    srcs = [":mediatranslation_proto"],
    deps = [":mediatranslation_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "mediatranslation_csharp_proto",
    deps = [":mediatranslation_proto"],
)

csharp_grpc_library(
    name = "mediatranslation_csharp_grpc",
    srcs = [":mediatranslation_proto"],
    deps = [":mediatranslation_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
