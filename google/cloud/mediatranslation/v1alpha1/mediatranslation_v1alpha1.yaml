type: google.api.Service
config_version: 3
name: mediatranslation.googleapis.com
title: Media Translation API

apis:
- name: google.cloud.mediatranslation.v1alpha1.SpeechTranslationService

backend:
  rules:
  - selector: google.cloud.mediatranslation.v1alpha1.SpeechTranslationService.StreamingTranslateSpeech
    deadline: 355.0

authentication:
  rules:
  - selector: google.cloud.mediatranslation.v1alpha1.SpeechTranslationService.StreamingTranslateSpeech
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
