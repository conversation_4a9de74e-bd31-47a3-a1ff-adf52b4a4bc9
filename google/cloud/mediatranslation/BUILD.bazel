# This build file includes a target for the Ruby wrapper library for
# google-cloud-media_translation.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for mediatranslation.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "mediatranslation_ruby_wrapper",
    srcs = ["//google/cloud/mediatranslation/v1beta1:mediatranslation_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-media_translation",
        "ruby-cloud-env-prefix=MEDIA_TRANSLATION",
        "ruby-cloud-wrapper-of=v1beta1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/media-translation/",
        "ruby-cloud-api-id=mediatranslation.googleapis.com",
        "ruby-cloud-api-shortname=mediatranslation",
    ],
    ruby_cloud_description = "Media Translation API delivers real-time speech translation to your content and applications directly from your audio data. Leveraging Google’s machine learning technologies, the API offers enhanced accuracy and simplified integration while equipping you with a comprehensive set of features to further refine your translation results. Improve user experience with low-latency streaming translation and scale quickly with straightforward internationalization.",
    ruby_cloud_title = "Media Translation",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-mediatranslation-ruby",
    deps = [
        ":mediatranslation_ruby_wrapper",
    ],
)
