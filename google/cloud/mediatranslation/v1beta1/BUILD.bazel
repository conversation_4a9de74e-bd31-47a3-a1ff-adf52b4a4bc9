# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "mediatranslation_proto",
    srcs = [
        "media_translation.proto",
    ],
    deps = [
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/rpc:status_proto",
    ],
)

proto_library_with_info(
    name = "mediatranslation_proto_with_info",
    deps = [
        ":mediatranslation_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "mediatranslation_java_proto",
    deps = [":mediatranslation_proto"],
)

java_grpc_library(
    name = "mediatranslation_java_grpc",
    srcs = [":mediatranslation_proto"],
    deps = [":mediatranslation_java_proto"],
)

java_gapic_library(
    name = "mediatranslation_java_gapic",
    srcs = [":mediatranslation_proto_with_info"],
    grpc_service_config = "mediatranslation_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "mediatranslation_v1beta1.yaml",
    test_deps = [
        ":mediatranslation_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":mediatranslation_java_proto",
    ],
)

java_gapic_test(
    name = "mediatranslation_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.mediatranslation.v1beta1.SpeechTranslationServiceClientTest",
    ],
    runtime_deps = [":mediatranslation_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-mediatranslation-v1beta1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":mediatranslation_java_gapic",
        ":mediatranslation_java_grpc",
        ":mediatranslation_java_proto",
        ":mediatranslation_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "mediatranslation_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/mediatranslation/apiv1beta1/mediatranslationpb",
    protos = [":mediatranslation_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "mediatranslation_go_gapic",
    srcs = [":mediatranslation_proto_with_info"],
    grpc_service_config = "mediatranslation_grpc_service_config.json",
    importpath = "cloud.google.com/go/mediatranslation/apiv1beta1;mediatranslation",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "mediatranslation_v1beta1.yaml",
    transport = "grpc",
    deps = [
        ":mediatranslation_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-mediatranslation-v1beta1-go",
    deps = [
        ":mediatranslation_go_gapic",
        ":mediatranslation_go_gapic_srcjar-metadata.srcjar",
        ":mediatranslation_go_gapic_srcjar-snippets.srcjar",
        ":mediatranslation_go_gapic_srcjar-test.srcjar",
        ":mediatranslation_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "mediatranslation_py_gapic",
    srcs = [":mediatranslation_proto"],
    grpc_service_config = "mediatranslation_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-media-translation",
    ],
    rest_numeric_enums = True,
    service_yaml = "mediatranslation_v1beta1.yaml",
    transport = "grpc",
)

py_test(
    name = "mediatranslation_py_gapic_test",
    srcs = [
        "mediatranslation_py_gapic_pytest.py",
        "mediatranslation_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":mediatranslation_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "mediatranslation-v1beta1-py",
    deps = [
        ":mediatranslation_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "mediatranslation_php_proto",
    deps = [":mediatranslation_proto"],
)

php_gapic_library(
    name = "mediatranslation_php_gapic",
    srcs = [":mediatranslation_proto_with_info"],
    grpc_service_config = "mediatranslation_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "mediatranslation_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":mediatranslation_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-mediatranslation-v1beta1-php",
    deps = [
        ":mediatranslation_php_gapic",
        ":mediatranslation_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "mediatranslation_nodejs_gapic",
    package_name = "@google-cloud/media-translation",
    src = ":mediatranslation_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "mediatranslation_grpc_service_config.json",
    package = "google.cloud.mediatranslation.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "mediatranslation_v1beta1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "mediatranslation-v1beta1-nodejs",
    deps = [
        ":mediatranslation_nodejs_gapic",
        ":mediatranslation_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "mediatranslation_ruby_proto",
    deps = [":mediatranslation_proto"],
)

ruby_grpc_library(
    name = "mediatranslation_ruby_grpc",
    srcs = [":mediatranslation_proto"],
    deps = [":mediatranslation_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "mediatranslation_ruby_gapic",
    srcs = [":mediatranslation_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=mediatranslation.googleapis.com",
        "ruby-cloud-api-shortname=mediatranslation",
        "ruby-cloud-env-prefix=MEDIA_TRANSLATION",
        "ruby-cloud-gem-name=google-cloud-media_translation-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/media-translation/",
    ],
    grpc_service_config = "mediatranslation_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Media Translation API delivers real-time speech translation to your content and applications directly from your audio data. Leveraging Google’s machine learning technologies, the API offers enhanced accuracy and simplified integration while equipping you with a comprehensive set of features to further refine your translation results. Improve user experience with low-latency streaming translation and scale quickly with straightforward internationalization.",
    ruby_cloud_title = "Media Translation V1beta1",
    service_yaml = "mediatranslation_v1beta1.yaml",
    transport = "grpc",
    deps = [
        ":mediatranslation_ruby_grpc",
        ":mediatranslation_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-mediatranslation-v1beta1-ruby",
    deps = [
        ":mediatranslation_ruby_gapic",
        ":mediatranslation_ruby_grpc",
        ":mediatranslation_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "mediatranslation_csharp_proto",
    deps = [":mediatranslation_proto"],
)

csharp_grpc_library(
    name = "mediatranslation_csharp_grpc",
    srcs = [":mediatranslation_proto"],
    deps = [":mediatranslation_csharp_proto"],
)

csharp_gapic_library(
    name = "mediatranslation_csharp_gapic",
    srcs = [":mediatranslation_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "mediatranslation_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "mediatranslation_v1beta1.yaml",
    transport = "grpc",
    deps = [
        ":mediatranslation_csharp_grpc",
        ":mediatranslation_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-mediatranslation-v1beta1-csharp",
    deps = [
        ":mediatranslation_csharp_gapic",
        ":mediatranslation_csharp_grpc",
        ":mediatranslation_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
