{"methodConfig": [{"name": [{"service": "google.cloud.mediatranslation.v1beta1.SpeechTranslationService"}], "timeout": "400s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "UNKNOWN", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.mediatranslation.v1beta1.SpeechTranslationService", "method": "StreamingTranslateSpeech"}], "timeout": "400s"}]}