type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  java:
    package_name: com.google.cloud.resourcesettings.v1
  python:
    package_name: google.cloud.resourcesettings_v1.gapic
  go:
    package_name: cloud.google.com/go/resourcesettings/apiv1
  csharp:
    package_name: Google.Cloud.ResourceSettings.V1
  ruby:
    package_name: Google::Cloud::ResourceSettings::V1
  php:
    package_name: Google\Cloud\ResourceSettings\V1
  nodejs:
    package_name: resourcesettings.v1
    domain_layer_location: google-cloud
