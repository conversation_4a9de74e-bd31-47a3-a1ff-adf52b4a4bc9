type: google.api.Service
config_version: 3
name: resourcesettings.googleapis.com
title: Resource Settings API

apis:
- name: google.cloud.resourcesettings.v1.ResourceSettingsService

documentation:
  summary: |-
    The Resource Settings API allows users to control and modify the behavior
    of their GCP resources (e.g., VM, firewall, Project, etc.) across the
    Cloud Resource Hierarchy.

authentication:
  rules:
  - selector: 'google.cloud.resourcesettings.v1.ResourceSettingsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
