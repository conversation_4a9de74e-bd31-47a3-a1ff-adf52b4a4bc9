# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel
# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "resourcesettings_proto",
    srcs = [
        "resource_settings.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
    ],
)

proto_library_with_info(
    name = "resourcesettings_proto_with_info",
    deps = [
        ":resourcesettings_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "resourcesettings_java_proto",
    deps = [":resourcesettings_proto"],
)

java_grpc_library(
    name = "resourcesettings_java_grpc",
    srcs = [":resourcesettings_proto"],
    deps = [":resourcesettings_java_proto"],
)

java_gapic_library(
    name = "resourcesettings_java_gapic",
    srcs = [":resourcesettings_proto_with_info"],
    grpc_service_config = "resourcesettings_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "resourcesettings_v1.yaml",
    test_deps = [
        ":resourcesettings_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":resourcesettings_java_proto",
    ],
)

java_gapic_test(
    name = "resourcesettings_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.resourcesettings.v1.ResourceSettingsServiceClientHttpJsonTest",
        "com.google.cloud.resourcesettings.v1.ResourceSettingsServiceClientTest",
    ],
    runtime_deps = [":resourcesettings_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-resourcesettings-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":resourcesettings_java_gapic",
        ":resourcesettings_java_grpc",
        ":resourcesettings_java_proto",
        ":resourcesettings_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "resourcesettings_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/resourcesettings/apiv1/resourcesettingspb",
    protos = [":resourcesettings_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "resourcesettings_go_gapic",
    srcs = [":resourcesettings_proto_with_info"],
    grpc_service_config = "resourcesettings_grpc_service_config.json",
    importpath = "cloud.google.com/go/resourcesettings/apiv1;resourcesettings",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "resourcesettings_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":resourcesettings_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-resourcesettings-v1-go",
    deps = [
        ":resourcesettings_go_gapic",
        ":resourcesettings_go_gapic_srcjar-metadata.srcjar",
        ":resourcesettings_go_gapic_srcjar-snippets.srcjar",
        ":resourcesettings_go_gapic_srcjar-test.srcjar",
        ":resourcesettings_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "resourcesettings_py_gapic",
    srcs = [":resourcesettings_proto"],
    grpc_service_config = "resourcesettings_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-resource-settings"],
    rest_numeric_enums = True,
    service_yaml = "resourcesettings_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "resourcesettings_py_gapic_test",
    srcs = [
        "resourcesettings_py_gapic_pytest.py",
        "resourcesettings_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":resourcesettings_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "resourcesettings-v1-py",
    deps = [
        ":resourcesettings_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "resourcesettings_php_proto",
    deps = [":resourcesettings_proto"],
)

php_gapic_library(
    name = "resourcesettings_php_gapic",
    srcs = [":resourcesettings_proto_with_info"],
    grpc_service_config = "resourcesettings_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "resourcesettings_v1.yaml",
    transport = "grpc+rest",
    deps = [":resourcesettings_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-resourcesettings-v1-php",
    deps = [
        ":resourcesettings_php_gapic",
        ":resourcesettings_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "resourcesettings_nodejs_gapic",
    package_name = "@google-cloud/resource-settings",
    src = ":resourcesettings_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "resourcesettings_grpc_service_config.json",
    package = "google.cloud.resourcesettings.v1",
    rest_numeric_enums = True,
    service_yaml = "resourcesettings_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "resourcesettings-v1-nodejs",
    deps = [
        ":resourcesettings_nodejs_gapic",
        ":resourcesettings_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "resourcesettings_ruby_proto",
    deps = [":resourcesettings_proto"],
)

ruby_grpc_library(
    name = "resourcesettings_ruby_grpc",
    srcs = [":resourcesettings_proto"],
    deps = [":resourcesettings_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "resourcesettings_ruby_gapic",
    srcs = [":resourcesettings_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-resource_settings-v1",
        "ruby-cloud-env-prefix=RESOURCE_SETTINGS",
        "ruby-cloud-product-url=https://cloud.google.com/resource-manager/docs/resource-settings/overview",
        "ruby-cloud-api-id=resourcesettings.googleapis.com",
        "ruby-cloud-api-shortname=resourcesettings",
    ],
    grpc_service_config = "resourcesettings_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "You can use Resource Settings to centrally configure settings for your Google Cloud projects, folders, and organization. These settings are inherited by their descendants in the resource hierarchy. Each setting is created and managed by Google.",
    ruby_cloud_title = "Resource Settings V1",
    service_yaml = "resourcesettings_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":resourcesettings_ruby_grpc",
        ":resourcesettings_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-resourcesettings-v1-ruby",
    deps = [
        ":resourcesettings_ruby_gapic",
        ":resourcesettings_ruby_grpc",
        ":resourcesettings_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "resourcesettings_csharp_proto",
    deps = [":resourcesettings_proto"],
)

csharp_grpc_library(
    name = "resourcesettings_csharp_grpc",
    srcs = [":resourcesettings_proto"],
    deps = [":resourcesettings_csharp_proto"],
)

csharp_gapic_library(
    name = "resourcesettings_csharp_gapic",
    srcs = [":resourcesettings_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "resourcesettings_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "resourcesettings_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":resourcesettings_csharp_grpc",
        ":resourcesettings_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-resourcesettings-v1-csharp",
    deps = [
        ":resourcesettings_csharp_gapic",
        ":resourcesettings_csharp_grpc",
        ":resourcesettings_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "resourcesettings_cc_proto",
    deps = [":resourcesettings_proto"],
)

cc_grpc_library(
    name = "resourcesettings_cc_grpc",
    srcs = [":resourcesettings_proto"],
    grpc_only = True,
    deps = [":resourcesettings_cc_proto"],
)
