# This build file includes a target for the Ruby wrapper library for
# google-cloud-resource_settings.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for resourcesettings.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "resourcesettings_ruby_wrapper",
    srcs = ["//google/cloud/resourcesettings/v1:resourcesettings_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-resource_settings",
        "ruby-cloud-env-prefix=RESOURCE_SETTINGS",
        "ruby-cloud-wrapper-of=v1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/resource-manager/docs/resource-settings/overview",
        "ruby-cloud-api-id=resourcesettings.googleapis.com",
        "ruby-cloud-api-shortname=resourcesettings",
    ],
    ruby_cloud_description = "You can use Resource Settings to centrally configure settings for your Google Cloud projects, folders, and organization. These settings are inherited by their descendants in the resource hierarchy. Each setting is created and managed by Google.",
    ruby_cloud_title = "Resource Settings",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-resourcesettings-ruby",
    deps = [
        ":resourcesettings_ruby_wrapper",
    ],
)
