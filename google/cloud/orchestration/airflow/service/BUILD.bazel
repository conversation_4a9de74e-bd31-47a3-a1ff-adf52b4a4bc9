# This build file includes a target for the Ruby wrapper library for
# google-cloud-orchestration-airflow-service.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for composer.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "composer_ruby_wrapper",
    srcs = ["//google/cloud/orchestration/airflow/service/v1:service_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-orchestration-airflow-service",
        "ruby-cloud-wrapper-of=v1:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/composer",
        "ruby-cloud-api-id=composer.googleapis.com",
        "ruby-cloud-api-shortname=composer",
    ],
    ruby_cloud_description = "The client library for the Cloud Composer API, built on the popular Apache Airflow open source project. Cloud Composer is a fully managed workflow orchestration service, enabling you to create, schedule, monitor, and manage workflows that span across clouds and on-premises data centers.",
    ruby_cloud_title = "Cloud Composer",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-orchestration-airflow-service-ruby",
    deps = [
        ":composer_ruby_wrapper",
    ],
)
