// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.orchestration.airflow.service.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/type/date.proto";

option go_package = "cloud.google.com/go/orchestration/airflow/service/apiv1beta1/servicepb;servicepb";
option java_multiple_files = true;
option java_package = "com.google.cloud.orchestration.airflow.service.v1beta1";

// Readonly service to query available ImageVersions.
service ImageVersions {
  option (google.api.default_host) = "composer.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // List ImageVersions for provided location.
  rpc ListImageVersions(ListImageVersionsRequest)
      returns (ListImageVersionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/imageVersions"
    };
    option (google.api.method_signature) = "parent";
  }
}

// List ImageVersions in a project and location.
message ListImageVersionsRequest {
  // List ImageVersions in the given project and location, in the form:
  // "projects/{projectId}/locations/{locationId}"
  string parent = 1;

  // The maximum number of image_versions to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;

  // Whether or not image versions from old releases should be included.
  bool include_past_releases = 4;
}

// The ImageVersions in a project and location.
message ListImageVersionsResponse {
  // The list of supported ImageVersions in a location.
  repeated ImageVersion image_versions = 1;

  // The page token used to query for the next page if one exists.
  string next_page_token = 2;
}

// Image Version information
message ImageVersion {
  // The string identifier of the ImageVersion, in the form:
  // "composer-x.y.z-airflow-a.b.c"
  string image_version_id = 1;

  // Whether this is the default ImageVersion used by Composer during
  // environment creation if no input ImageVersion is specified.
  bool is_default = 2;

  // supported python versions
  repeated string supported_python_versions = 3;

  // The date of the version release.
  google.type.Date release_date = 4;

  // Whether it is impossible to create an environment with the image version.
  bool creation_disabled = 5;

  // Whether it is impossible to upgrade an environment running with the image
  // version.
  bool upgrade_disabled = 6;
}
