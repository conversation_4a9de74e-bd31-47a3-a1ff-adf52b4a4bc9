# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "service_proto",
    srcs = [
        "environments.proto",
        "image_versions.proto",
        "operations.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "service_proto_with_info",
    deps = [
        ":service_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "service_java_proto",
    deps = [":service_proto"],
)

java_grpc_library(
    name = "service_java_grpc",
    srcs = [":service_proto"],
    deps = [":service_java_proto"],
)

java_gapic_library(
    name = "service_java_gapic",
    srcs = [":service_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "composer_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "composer_v1beta1.yaml",
    test_deps = [
        ":service_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":service_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "service_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.orchestration.airflow.service.v1beta1.EnvironmentsClientHttpJsonTest",
        "com.google.cloud.orchestration.airflow.service.v1beta1.EnvironmentsClientTest",
        "com.google.cloud.orchestration.airflow.service.v1beta1.ImageVersionsClientHttpJsonTest",
        "com.google.cloud.orchestration.airflow.service.v1beta1.ImageVersionsClientTest",
    ],
    runtime_deps = [":service_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-airflow-service-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":service_java_gapic",
        ":service_java_grpc",
        ":service_java_proto",
        ":service_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "service_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/orchestration/airflow/service/apiv1beta1/servicepb",
    protos = [":service_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "service_go_gapic",
    srcs = [":service_proto_with_info"],
    grpc_service_config = "composer_grpc_service_config.json",
    importpath = "cloud.google.com/go/orchestration/airflow/service/apiv1beta1;service",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "composer_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":service_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-airflow-service-v1beta1-go",
    deps = [
        ":service_go_gapic",
        ":service_go_gapic_srcjar-metadata.srcjar",
        ":service_go_gapic_srcjar-snippets.srcjar",
        ":service_go_gapic_srcjar-test.srcjar",
        ":service_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "service_py_gapic",
    srcs = [":service_proto"],
    grpc_service_config = "composer_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "composer_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "service_py_gapic_test",
    srcs = [
        "service_py_gapic_pytest.py",
        "service_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":service_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "airflow-service-v1beta1-py",
    deps = [
        ":service_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "service_php_proto",
    deps = [":service_proto"],
)

php_gapic_library(
    name = "service_php_gapic",
    srcs = [":service_proto_with_info"],
    grpc_service_config = "composer_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "composer_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":service_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-airflow-service-v1beta1-php",
    deps = [
        ":service_php_gapic",
        ":service_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "service_nodejs_gapic",
    package_name = "@google-cloud/orchestration-airflow",
    src = ":service_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "composer_grpc_service_config.json",
    package = "google.cloud.orchestration.airflow.service.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "composer_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "airflow-service-v1beta1-nodejs",
    deps = [
        ":service_nodejs_gapic",
        ":service_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "service_ruby_proto",
    deps = [":service_proto"],
)

ruby_grpc_library(
    name = "service_ruby_grpc",
    srcs = [":service_proto"],
    deps = [":service_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "service_ruby_gapic",
    srcs = [":service_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=composer.googleapis.com",
        "ruby-cloud-api-shortname=composer",
        "ruby-cloud-gem-name=google-cloud-orchestration-airflow-service-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/composer",
    ],
    grpc_service_config = "composer_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The client library for the Cloud Composer API, built on the popular Apache Airflow open source project. Cloud Composer is a fully managed workflow orchestration service, enabling you to create, schedule, monitor, and manage workflows that span across clouds and on-premises data centers.",
    ruby_cloud_title = "Cloud Composer V1beta1",
    service_yaml = "composer_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":service_ruby_grpc",
        ":service_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-airflow-service-v1beta1-ruby",
    deps = [
        ":service_ruby_gapic",
        ":service_ruby_grpc",
        ":service_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "service_csharp_proto",
    extra_opts = [],
    deps = [":service_proto"],
)

csharp_grpc_library(
    name = "service_csharp_grpc",
    srcs = [":service_proto"],
    deps = [":service_csharp_proto"],
)

csharp_gapic_library(
    name = "service_csharp_gapic",
    srcs = [":service_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "composer_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "composer_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":service_csharp_grpc",
        ":service_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-airflow-service-v1beta1-csharp",
    deps = [
        ":service_csharp_gapic",
        ":service_csharp_grpc",
        ":service_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "service_cc_proto",
    deps = [":service_proto"],
)

cc_grpc_library(
    name = "service_cc_grpc",
    srcs = [":service_proto"],
    grpc_only = True,
    deps = [":service_cc_proto"],
)
