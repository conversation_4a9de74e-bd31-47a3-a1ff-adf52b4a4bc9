// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.orchestration.airflow.service.v1beta1;

import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/orchestration/airflow/service/apiv1beta1/servicepb;servicepb";
option java_multiple_files = true;
option java_outer_classname = "OperationsProto";
option java_package = "com.google.cloud.orchestration.airflow.service.v1beta1";

// Metadata describing an operation.
message OperationMetadata {
  // An enum describing the overall state of an operation.
  enum State {
    // Unused.
    STATE_UNSPECIFIED = 0;

    // The operation has been created but is not yet started.
    PENDING = 1;

    // The operation is underway.
    RUNNING = 2;

    // The operation completed successfully.
    SUCCESSFUL = 3;

    // The operation is no longer running but did not succeed.
    FAILED = 4;
  }

  // Type of longrunning operation.
  enum Type {
    // Unused.
    TYPE_UNSPECIFIED = 0;

    // A resource creation operation.
    CREATE = 1;

    // A resource deletion operation.
    DELETE = 2;

    // A resource update operation.
    UPDATE = 3;

    // A resource check operation.
    CHECK = 4;

    // Saves snapshot of the resource operation.
    SAVE_SNAPSHOT = 5;

    // Loads snapshot of the resource operation.
    LOAD_SNAPSHOT = 6;

    // Triggers failover of environment's Cloud SQL instance (only for highly
    // resilient environments).
    DATABASE_FAILOVER = 7;
  }

  // Output only. The current operation state.
  State state = 1;

  // Output only. The type of operation being performed.
  Type operation_type = 2;

  // Output only. The resource being operated on, as a [relative resource name](
  // /apis/design/resource_names#relative_resource_name).
  string resource = 3;

  // Output only. The UUID of the resource being operated on.
  string resource_uuid = 4;

  // Output only. The time the operation was submitted to the server.
  google.protobuf.Timestamp create_time = 5;

  // Output only. The time when the operation terminated, regardless of its
  // success. This field is unset if the operation is still ongoing.
  google.protobuf.Timestamp end_time = 6;
}
