// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.developerconnect.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DeveloperConnect.V1";
option go_package = "cloud.google.com/go/developerconnect/apiv1/developerconnectpb;developerconnectpb";
option java_multiple_files = true;
option java_outer_classname = "DeveloperConnectProto";
option java_package = "com.google.cloud.developerconnect.v1";
option objc_class_prefix = "DeveloperConnect";
option php_namespace = "Google\\Cloud\\DeveloperConnect\\V1";
option ruby_package = "Google::Cloud::DeveloperConnect::V1";
option (google.api.resource_definition) = {
  type: "secretmanager.googleapis.com/SecretVersion"
  pattern: "projects/{project}/secrets/{secret}/versions/{secret_version}"
};
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};
option (google.api.resource_definition) = {
  type: "servicedirectory.googleapis.com/Service"
  pattern: "projects/{project}/locations/{location}/namespaces/{namespace}/services/{service}"
};

// Service describing handlers for resources
service DeveloperConnect {
  option (google.api.default_host) = "developerconnect.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Connections in a given project and location.
  rpc ListConnections(ListConnectionsRequest)
      returns (ListConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/connections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Connection.
  rpc GetConnection(GetConnectionRequest) returns (Connection) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/connections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Connection in a given project and location.
  rpc CreateConnection(CreateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/connections"
      body: "connection"
    };
    option (google.api.method_signature) = "parent,connection,connection_id";
    option (google.longrunning.operation_info) = {
      response_type: "Connection"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Connection.
  rpc UpdateConnection(UpdateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{connection.name=projects/*/locations/*/connections/*}"
      body: "connection"
    };
    option (google.api.method_signature) = "connection,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Connection"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Connection.
  rpc DeleteConnection(DeleteConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/connections/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a GitRepositoryLink. Upon linking a Git Repository, Developer
  // Connect will configure the Git Repository to send webhook events to
  // Developer Connect. Connections that use Firebase GitHub Application will
  // have events forwarded to the Firebase service. All other Connections will
  // have events forwarded to Cloud Build.
  rpc CreateGitRepositoryLink(CreateGitRepositoryLinkRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/connections/*}/gitRepositoryLinks"
      body: "git_repository_link"
    };
    option (google.api.method_signature) =
        "parent,git_repository_link,git_repository_link_id";
    option (google.longrunning.operation_info) = {
      response_type: "GitRepositoryLink"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single GitRepositoryLink.
  rpc DeleteGitRepositoryLink(DeleteGitRepositoryLinkRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/connections/*/gitRepositoryLinks/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists GitRepositoryLinks in a given project, location, and connection.
  rpc ListGitRepositoryLinks(ListGitRepositoryLinksRequest)
      returns (ListGitRepositoryLinksResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/connections/*}/gitRepositoryLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single GitRepositoryLink.
  rpc GetGitRepositoryLink(GetGitRepositoryLinkRequest)
      returns (GitRepositoryLink) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/connections/*/gitRepositoryLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Fetches read/write token of a given gitRepositoryLink.
  rpc FetchReadWriteToken(FetchReadWriteTokenRequest)
      returns (FetchReadWriteTokenResponse) {
    option (google.api.http) = {
      post: "/v1/{git_repository_link=projects/*/locations/*/connections/*/gitRepositoryLinks/*}:fetchReadWriteToken"
      body: "*"
    };
    option (google.api.method_signature) = "git_repository_link";
  }

  // Fetches read token of a given gitRepositoryLink.
  rpc FetchReadToken(FetchReadTokenRequest) returns (FetchReadTokenResponse) {
    option (google.api.http) = {
      post: "/v1/{git_repository_link=projects/*/locations/*/connections/*/gitRepositoryLinks/*}:fetchReadToken"
      body: "*"
    };
    option (google.api.method_signature) = "git_repository_link";
  }

  // FetchLinkableGitRepositories returns a list of git repositories from an SCM
  // that are available to be added to a Connection.
  rpc FetchLinkableGitRepositories(FetchLinkableGitRepositoriesRequest)
      returns (FetchLinkableGitRepositoriesResponse) {
    option (google.api.http) = {
      get: "/v1/{connection=projects/*/locations/*/connections/*}:fetchLinkableGitRepositories"
    };
    option (google.api.method_signature) = "connection";
  }

  // FetchGitHubInstallations returns the list of GitHub Installations that
  // are available to be added to a Connection.
  // For github.com, only installations accessible to the authorizer token
  // are returned. For GitHub Enterprise, all installations are returned.
  rpc FetchGitHubInstallations(FetchGitHubInstallationsRequest)
      returns (FetchGitHubInstallationsResponse) {
    option (google.api.http) = {
      get: "/v1/{connection=projects/*/locations/*/connections/*}:fetchGitHubInstallations"
    };
    option (google.api.method_signature) = "connection";
  }

  // Fetch the list of branches or tags for a given repository.
  rpc FetchGitRefs(FetchGitRefsRequest) returns (FetchGitRefsResponse) {
    option (google.api.http) = {
      get: "/v1/{git_repository_link=projects/*/locations/*/connections/*/gitRepositoryLinks/*}:fetchGitRefs"
    };
    option (google.api.method_signature) = "git_repository_link,ref_type";
  }
}

// Message describing Connection object
message Connection {
  option (google.api.resource) = {
    type: "developerconnect.googleapis.com/Connection"
    pattern: "projects/{project}/locations/{location}/connections/{connection}"
    plural: "connections"
    singular: "connection"
    style: DECLARATIVE_FRIENDLY
  };

  // Configuration for the connection depending on the type of provider.
  oneof connection_config {
    // Configuration for connections to github.com.
    GitHubConfig github_config = 5;

    // Configuration for connections to an instance of GitHub Enterprise.
    GitHubEnterpriseConfig github_enterprise_config = 13;

    // Configuration for connections to gitlab.com.
    GitLabConfig gitlab_config = 14;

    // Configuration for connections to an instance of GitLab Enterprise.
    GitLabEnterpriseConfig gitlab_enterprise_config = 16;
  }

  // Identifier. The resource name of the connection, in the format
  // `projects/{project}/locations/{location}/connections/{connection_id}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. [Output only] Create timestamp
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update timestamp
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Delete timestamp
  google.protobuf.Timestamp delete_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels as key value pairs
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Installation state of the Connection.
  InstallationState installation_state = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. If disabled is set to true, functionality is disabled for this
  // connection. Repository based API methods and webhooks processing for
  // repositories in this connection will be disabled.
  bool disabled = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Set to true when the connection is being set up or updated in
  // the background.
  bool reconciling = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Allows clients to store small amounts of arbitrary data.
  map<string, string> annotations = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 10 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A system-assigned unique identifier for a the
  // GitRepositoryLink.
  string uid = 12 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. The crypto key configuration. This field is used by the
  // Customer-Managed Encryption Keys (CMEK) feature.
  CryptoKeyConfig crypto_key_config = 15
      [(google.api.field_behavior) = OPTIONAL];
}

// The crypto key configuration. This field is used by the Customer-managed
// encryption keys (CMEK) feature.
message CryptoKeyConfig {
  // Required. The name of the key which is used to encrypt/decrypt customer
  // data. For key in Cloud KMS, the key should be in the format of
  // `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
  string key_reference = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];
}

// Describes stage and necessary actions to be taken by the
// user to complete the installation. Used for GitHub and GitHub Enterprise
// based connections.
message InstallationState {
  // Stage of the installation process.
  enum Stage {
    // No stage specified.
    STAGE_UNSPECIFIED = 0;

    // Only for GitHub Enterprise. An App creation has been requested.
    // The user needs to confirm the creation in their GitHub enterprise host.
    PENDING_CREATE_APP = 1;

    // User needs to authorize the GitHub (or Enterprise) App via OAuth.
    PENDING_USER_OAUTH = 2;

    // User needs to follow the link to install the GitHub (or Enterprise) App.
    PENDING_INSTALL_APP = 3;

    // Installation process has been completed.
    COMPLETE = 10;
  }

  // Output only. Current step of the installation process.
  Stage stage = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Message of what the user should do next to continue the
  // installation. Empty string if the installation is already complete.
  string message = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Link to follow for next action. Empty string if the
  // installation is already complete.
  string action_uri = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for connections to github.com.
message GitHubConfig {
  // Represents the various GitHub Applications that can be installed to a
  // GitHub user or organization and used with Developer Connect.
  enum GitHubApp {
    // GitHub App not specified.
    GIT_HUB_APP_UNSPECIFIED = 0;

    // The Developer Connect GitHub Application.
    DEVELOPER_CONNECT = 1;

    // The Firebase GitHub Application.
    FIREBASE = 2;
  }

  // Required. Immutable. The GitHub Application that was installed to the
  // GitHub user or organization.
  GitHubApp github_app = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. OAuth credential of the account that authorized the GitHub App.
  // It is recommended to use a robot account instead of a human user account.
  // The OAuth token must be tied to the GitHub App of this config.
  OAuthCredential authorizer_credential = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. GitHub App installation id.
  int64 app_installation_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The URI to navigate to in order to manage the installation
  // associated with this GitHubConfig.
  string installation_uri = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for connections to an instance of GitHub Enterprise.
message GitHubEnterpriseConfig {
  // Required. The URI of the GitHub Enterprise host this connection is for.
  string host_uri = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. ID of the GitHub App created from the manifest.
  int64 app_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The URL-friendly name of the GitHub App.
  string app_slug = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. SecretManager resource containing the private key of the GitHub
  // App, formatted as `projects/*/secrets/*/versions/*`.
  string private_key_secret_version = 4 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Optional. SecretManager resource containing the webhook secret of the
  // GitHub App, formatted as `projects/*/secrets/*/versions/*`.
  string webhook_secret_secret_version = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Optional. ID of the installation of the GitHub App.
  int64 app_installation_id = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The URI to navigate to in order to manage the installation
  // associated with this GitHubEnterpriseConfig.
  string installation_uri = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Configuration for using Service Directory to privately connect to
  // a GitHub Enterprise server. This should only be set if the GitHub
  // Enterprise server is hosted on-premises and not reachable by public
  // internet. If this field is left empty, calls to the GitHub Enterprise
  // server will be made over the public internet.
  ServiceDirectoryConfig service_directory_config = 10
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. GitHub Enterprise version installed at the host_uri.
  string server_version = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. SSL certificate to use for requests to GitHub Enterprise.
  string ssl_ca_certificate = 14 [(google.api.field_behavior) = OPTIONAL];
}

// ServiceDirectoryConfig represents Service Directory configuration for a
// connection.
message ServiceDirectoryConfig {
  // Required. The Service Directory service name.
  // Format:
  // projects/{project}/locations/{location}/namespaces/{namespace}/services/{service}.
  string service = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "servicedirectory.googleapis.com/Service"
    }
  ];
}

// Represents an OAuth token of the account that authorized the Connection,
// and associated metadata.
message OAuthCredential {
  // Required. A SecretManager resource containing the OAuth token that
  // authorizes the connection. Format: `projects/*/secrets/*/versions/*`.
  string oauth_token_secret_version = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Output only. The username associated with this token.
  string username = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for connections to gitlab.com.
message GitLabConfig {
  // Required. Immutable. SecretManager resource containing the webhook secret
  // of a GitLab project, formatted as `projects/*/secrets/*/versions/*`. This
  // is used to validate webhooks.
  string webhook_secret_secret_version = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Required. A GitLab personal access token with the minimum `read_api` scope
  // access and a minimum role of `reporter`. The GitLab Projects visible to
  // this Personal Access Token will control which Projects Developer Connect
  // has access to.
  UserCredential read_authorizer_credential = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. A GitLab personal access token with the minimum `api` scope
  // access and a minimum role of `maintainer`. The GitLab Projects visible to
  // this Personal Access Token will control which Projects Developer Connect
  // has access to.
  UserCredential authorizer_credential = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Represents a personal access token that authorized the Connection,
// and associated metadata.
message UserCredential {
  // Required. A SecretManager resource containing the user token that
  // authorizes the Developer Connect connection. Format:
  // `projects/*/secrets/*/versions/*`.
  string user_token_secret_version = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Output only. The username associated with this token.
  string username = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for connections to an instance of GitLab Enterprise.
message GitLabEnterpriseConfig {
  // Required. The URI of the GitLab Enterprise host this connection is for.
  string host_uri = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. SecretManager resource containing the webhook secret
  // of a GitLab project, formatted as `projects/*/secrets/*/versions/*`. This
  // is used to validate webhooks.
  string webhook_secret_secret_version = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Required. A GitLab personal access token with the minimum `read_api` scope
  // access and a minimum role of `reporter`. The GitLab Projects visible to
  // this Personal Access Token will control which Projects Developer Connect
  // has access to.
  UserCredential read_authorizer_credential = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. A GitLab personal access token with the minimum `api` scope
  // access and a minimum role of `maintainer`. The GitLab Projects visible to
  // this Personal Access Token will control which Projects Developer Connect
  // has access to.
  UserCredential authorizer_credential = 4
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Configuration for using Service Directory to privately connect to
  // a GitLab Enterprise instance. This should only be set if the GitLab
  // Enterprise server is hosted on-premises and not reachable by public
  // internet. If this field is left empty, calls to the GitLab Enterprise
  // server will be made over the public internet.
  ServiceDirectoryConfig service_directory_config = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. SSL Certificate Authority certificate to use for requests to
  // GitLab Enterprise instance.
  string ssl_ca_certificate = 6 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Version of the GitLab Enterprise server running on the
  // `host_uri`.
  string server_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting list of Connections
message ListConnectionsRequest {
  // Required. Parent value for ListConnectionsRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "developerconnect.googleapis.com/Connection"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing Connections
message ListConnectionsResponse {
  // The list of Connection
  repeated Connection connections = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Connection
message GetConnectionRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "developerconnect.googleapis.com/Connection"
    }
  ];
}

// Message for creating a Connection
message CreateConnectionRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "developerconnect.googleapis.com/Connection"
    }
  ];

  // Required. Id of the requesting object
  // If auto-generating Id server-side, remove this field and
  // connection_id from the method_signature of Create RPC
  string connection_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Connection connection = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. If set, validate the request, but do not actually post it.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Connection
message UpdateConnectionRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Connection resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  Connection connection = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. If set to true, and the connection is not found a new connection
  // will be created. In this situation `update_mask` is ignored.
  // The creation will succeed only if the input connection has all the
  // necessary information (e.g a github_config with both  user_oauth_token and
  // installation_id properties).
  bool allow_missing = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validate the request, but do not actually post it.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Connection
message DeleteConnectionRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "developerconnect.googleapis.com/Connection"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. If set, validate the request, but do not actually post it.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The current etag of the Connection.
  // If an etag is provided and does not match the current etag of the
  // Connection, deletion will be blocked and an ABORTED error will be returned.
  string etag = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have
  // [google.longrunning.Operation.error][google.longrunning.Operation.error]
  // value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message describing the GitRepositoryLink object
message GitRepositoryLink {
  option (google.api.resource) = {
    type: "developerconnect.googleapis.com/GitRepositoryLink"
    pattern: "projects/{project}/locations/{location}/connections/{connection}/gitRepositoryLinks/{git_repository_link}"
    plural: "gitRepositoryLinks"
    singular: "gitRepositoryLink"
    style: DECLARATIVE_FRIENDLY
  };

  // Identifier. Resource name of the repository, in the format
  // `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Git Clone URI.
  string clone_uri = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. [Output only] Create timestamp
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update timestamp
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Delete timestamp
  google.protobuf.Timestamp delete_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels as key value pairs
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Set to true when the connection is being set up or updated in
  // the background.
  bool reconciling = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Allows clients to store small amounts of arbitrary data.
  map<string, string> annotations = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A system-assigned unique identifier for a the
  // GitRepositoryLink.
  string uid = 10 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. External ID of the webhook created for the repository.
  string webhook_id = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for creating a GitRepositoryLink
message CreateGitRepositoryLinkRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "developerconnect.googleapis.com/GitRepositoryLink"
    }
  ];

  // Required. The resource being created
  GitRepositoryLink git_repository_link = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the repository, which will become the final
  // component of the repository's resource name. This ID should be unique in
  // the connection. Allows alphanumeric characters and any of
  // -._~%!$&'()*+,;=@.
  string git_repository_link_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. If set, validate the request, but do not actually post it.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a GitRepositoryLink
message DeleteGitRepositoryLinkRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "developerconnect.googleapis.com/GitRepositoryLink"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. If set, validate the request, but do not actually post it.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This checksum is computed by the server based on the value of
  // other fields, and may be sent on update and delete requests to ensure the
  // client has an up-to-date value before proceeding.
  string etag = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting a list of GitRepositoryLinks
message ListGitRepositoryLinksRequest {
  // Required. Parent value for ListGitRepositoryLinksRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "developerconnect.googleapis.com/GitRepositoryLink"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing GitRepositoryLinks
message ListGitRepositoryLinksResponse {
  // The list of GitRepositoryLinks
  repeated GitRepositoryLink git_repository_links = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a GitRepositoryLink
message GetGitRepositoryLinkRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "developerconnect.googleapis.com/GitRepositoryLink"
    }
  ];
}

// Message for fetching SCM read/write token.
message FetchReadWriteTokenRequest {
  // Required. The resource name of the gitRepositoryLink in the format
  // `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
  string git_repository_link = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "developerconnect.googleapis.com/GitRepositoryLink"
    }
  ];
}

// Message for fetching SCM read token.
message FetchReadTokenRequest {
  // Required. The resource name of the gitRepositoryLink in the format
  // `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
  string git_repository_link = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "developerconnect.googleapis.com/GitRepositoryLink"
    }
  ];
}

// Message for responding to get read token.
message FetchReadTokenResponse {
  // The token content.
  string token = 1;

  // Expiration timestamp. Can be empty if unknown or non-expiring.
  google.protobuf.Timestamp expiration_time = 2;

  // The git_username to specify when making a git clone with the
  // token. For example, for GitHub GitRepositoryLinks, this would be
  // "x-access-token"
  string git_username = 3;
}

// Message for responding to get read/write token.
message FetchReadWriteTokenResponse {
  // The token content.
  string token = 1;

  // Expiration timestamp. Can be empty if unknown or non-expiring.
  google.protobuf.Timestamp expiration_time = 2;

  // The git_username to specify when making a git clone with the
  // token. For example, for GitHub GitRepositoryLinks, this would be
  // "x-access-token"
  string git_username = 3;
}

// Request message for FetchLinkableGitRepositoriesRequest.
message FetchLinkableGitRepositoriesRequest {
  // Required. The name of the Connection.
  // Format: `projects/*/locations/*/connections/*`.
  string connection = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "developerconnect.googleapis.com/Connection"
    }
  ];

  // Optional. Number of results to return in the list. Defaults to 20.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page start.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for FetchLinkableGitRepositories.
message FetchLinkableGitRepositoriesResponse {
  // The git repositories that can be linked to the connection.
  repeated LinkableGitRepository linkable_git_repositories = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// LinkableGitRepository represents a git repository that can be linked to a
// connection.
message LinkableGitRepository {
  // The clone uri of the repository.
  string clone_uri = 1;
}

// Request for fetching github installations.
message FetchGitHubInstallationsRequest {
  // Required. The resource name of the connection in the format
  // `projects/*/locations/*/connections/*`.
  string connection = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "developerconnect.googleapis.com/Connection"
    }
  ];
}

// Response of fetching github installations.
message FetchGitHubInstallationsResponse {
  // Represents an installation of the GitHub App.
  message Installation {
    // ID of the installation in GitHub.
    int64 id = 1;

    // Name of the GitHub user or organization that owns this installation.
    string name = 2;

    // Either "user" or "organization".
    string type = 3;
  }

  // List of installations available to the OAuth user (for github.com)
  // or all the installations (for GitHub enterprise).
  repeated Installation installations = 1;
}

// Request for fetching git refs.
message FetchGitRefsRequest {
  // Type of refs.
  enum RefType {
    // No type specified.
    REF_TYPE_UNSPECIFIED = 0;

    // To fetch tags.
    TAG = 1;

    // To fetch branches.
    BRANCH = 2;
  }

  // Required. The resource name of GitRepositoryLink in the format
  // `projects/*/locations/*/connections/*/gitRepositoryLinks/*`.
  string git_repository_link = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "developerconnect.googleapis.com/GitRepositoryLink"
    }
  ];

  // Required. Type of refs to fetch.
  RefType ref_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Number of results to return in the list. Default to 20.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page start.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response for fetching git refs.
message FetchGitRefsResponse {
  // Name of the refs fetched.
  repeated string ref_names = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}
