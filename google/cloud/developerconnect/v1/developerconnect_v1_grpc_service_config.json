{"methodConfig": [{"name": [{"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "ListConnections"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "GetConnection"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "CreateConnection"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "UpdateConnection"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "DeleteConnection"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "CreateGitRepositoryLink"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "DeleteGitRepositoryLink"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "ListGitRepositoryLink"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "GetGitRepositoryLink"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "FetchReadWriteToken"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "FetchReadToken"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "FetchLinkableGitRepositories"}, {"service": "google.cloud.developerconnect.v1.DeveloperConnect", "method": "FetchGitHubInstallations"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}