# This build file includes a target for the Ruby wrapper library for
# google-cloud-tasks.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudtasks.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "cloudtasks_ruby_wrapper",
    srcs = ["//google/cloud/tasks/v2:tasks_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-tasks",
        "ruby-cloud-env-prefix=TASKS",
        "ruby-cloud-wrapper-of=v2:0.10;v2beta2:0.11;v2beta3:0.12",
        "ruby-cloud-product-url=https://cloud.google.com/tasks",
        "ruby-cloud-api-id=cloudtasks.googleapis.com",
        "ruby-cloud-api-shortname=cloudtasks",
        "ruby-cloud-migration-version=2.0",
    ],
    ruby_cloud_description = "Cloud Tasks is a fully managed service that allows you to manage the execution, dispatch and delivery of a large number of distributed tasks. You can asynchronously perform work outside of a user request. Your tasks can be executed on App Engine or any arbitrary HTTP endpoint.",
    ruby_cloud_title = "Cloud Tasks",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-tasks-ruby",
    deps = [
        ":cloudtasks_ruby_wrapper",
    ],
)
