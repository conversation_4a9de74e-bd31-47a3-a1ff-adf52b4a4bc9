{"methodConfig": [{"name": [{"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "BufferTask"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "CreateQueue"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "UpdateQueue"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "Purge<PERSON><PERSON>ue"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "ResumeQueue"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "SetIamPolicy"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "CreateTask"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "RunTask"}], "timeout": "20s"}, {"name": [{"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "ListQueues"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "GetQueue"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "DeleteQueue"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "GetIamPolicy"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "TestIamPermissions"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "ListTasks"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "GetTask"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "DeleteTask"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "UpdateCmekConfig"}, {"service": "google.cloud.tasks.v2beta3.CloudTasks", "method": "GetCmekConfig"}], "timeout": "20s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}