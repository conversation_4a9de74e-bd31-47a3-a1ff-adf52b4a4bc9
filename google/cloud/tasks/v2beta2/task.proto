// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.tasks.v2beta2;

import "google/api/resource.proto";
import "google/cloud/tasks/v2beta2/target.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/cloudtasks/apiv2beta2/cloudtaskspb;cloudtaskspb";
option java_multiple_files = true;
option java_outer_classname = "TaskProto";
option java_package = "com.google.cloud.tasks.v2beta2";

// A unit of scheduled work.
message Task {
  option (google.api.resource) = {
    type: "cloudtasks.googleapis.com/Task"
    pattern: "projects/{project}/locations/{location}/queues/{queue}/tasks/{task}"
  };

  // The view specifies a subset of [Task][google.cloud.tasks.v2beta2.Task]
  // data.
  //
  // When a task is returned in a response, not all
  // information is retrieved by default because some data, such as
  // payloads, might be desirable to return only when needed because
  // of its large size or because of the sensitivity of data that it
  // contains.
  enum View {
    // Unspecified. Defaults to BASIC.
    VIEW_UNSPECIFIED = 0;

    // The basic view omits fields which can be large or can contain
    // sensitive data.
    //
    // This view does not include the
    // ([payload in
    // AppEngineHttpRequest][google.cloud.tasks.v2beta2.AppEngineHttpRequest]
    // and [payload in
    // PullMessage][google.cloud.tasks.v2beta2.PullMessage.payload]). These
    // payloads are desirable to return only when needed, because they can be
    // large and because of the sensitivity of the data that you choose to store
    // in it.
    BASIC = 1;

    // All information is returned.
    //
    // Authorization for [FULL][google.cloud.tasks.v2beta2.Task.View.FULL]
    // requires `cloudtasks.tasks.fullView` [Google
    // IAM](https://cloud.google.com/iam/) permission on the
    // [Queue][google.cloud.tasks.v2beta2.Queue] resource.
    FULL = 2;
  }

  // Optionally caller-specified in
  // [CreateTask][google.cloud.tasks.v2beta2.CloudTasks.CreateTask].
  //
  // The task name.
  //
  // The task name must have the following format:
  // `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks/TASK_ID`
  //
  // * `PROJECT_ID` can contain letters ([A-Za-z]), numbers ([0-9]),
  //    hyphens (-), colons (:), or periods (.).
  //    For more information, see
  //    [Identifying
  //    projects](https://cloud.google.com/resource-manager/docs/creating-managing-projects#identifying_projects)
  // * `LOCATION_ID` is the canonical ID for the task's location.
  //    The list of available locations can be obtained by calling
  //    [ListLocations][google.cloud.location.Locations.ListLocations].
  //    For more information, see https://cloud.google.com/about/locations/.
  // * `QUEUE_ID` can contain letters ([A-Za-z]), numbers ([0-9]), or
  //   hyphens (-). The maximum length is 100 characters.
  // * `TASK_ID` can contain only letters ([A-Za-z]), numbers ([0-9]),
  //   hyphens (-), or underscores (_). The maximum length is 500 characters.
  string name = 1;

  // Required.
  //
  // The task's payload is used by the task's target to process the task.
  // A payload is valid only if it is compatible with the queue's target.
  oneof payload_type {
    // App Engine HTTP request that is sent to the task's target. Can
    // be set only if
    // [app_engine_http_target][google.cloud.tasks.v2beta2.Queue.app_engine_http_target]
    // is set on the queue.
    //
    // An App Engine task is a task that has
    // [AppEngineHttpRequest][google.cloud.tasks.v2beta2.AppEngineHttpRequest]
    // set.
    AppEngineHttpRequest app_engine_http_request = 3;

    // [LeaseTasks][google.cloud.tasks.v2beta2.CloudTasks.LeaseTasks] to process
    // the task. Can be set only if
    // [pull_target][google.cloud.tasks.v2beta2.Queue.pull_target] is set on the
    // queue.
    //
    // A pull task is a task that has
    // [PullMessage][google.cloud.tasks.v2beta2.PullMessage] set.
    PullMessage pull_message = 4;

    // HTTP request that is sent to the task's target.
    //
    // An HTTP task is a task that has
    // [HttpRequest][google.cloud.tasks.v2beta2.HttpRequest] set.
    HttpRequest http_request = 13;
  }

  // The time when the task is scheduled to be attempted.
  //
  // For App Engine queues, this is when the task will be attempted or retried.
  //
  // For pull queues, this is the time when the task is available to
  // be leased; if a task is currently leased, this is the time when
  // the current lease expires, that is, the time that the task was
  // leased plus the
  // [lease_duration][google.cloud.tasks.v2beta2.LeaseTasksRequest.lease_duration].
  //
  // `schedule_time` will be truncated to the nearest microsecond.
  google.protobuf.Timestamp schedule_time = 5;

  // Output only. The time that the task was created.
  //
  // `create_time` will be truncated to the nearest second.
  google.protobuf.Timestamp create_time = 6;

  // Output only. The task status.
  TaskStatus status = 7;

  // Output only. The view specifies which subset of the
  // [Task][google.cloud.tasks.v2beta2.Task] has been returned.
  View view = 8;
}

// Status of the task.
message TaskStatus {
  // Output only. The number of attempts dispatched.
  //
  // This count includes attempts which have been dispatched but haven't
  // received a response.
  int32 attempt_dispatch_count = 1;

  // Output only. The number of attempts which have received a response.
  //
  // This field is not calculated for [pull
  // tasks][google.cloud.tasks.v2beta2.PullMessage].
  int32 attempt_response_count = 2;

  // Output only. The status of the task's first attempt.
  //
  // Only
  // [dispatch_time][google.cloud.tasks.v2beta2.AttemptStatus.dispatch_time]
  // will be set. The other
  // [AttemptStatus][google.cloud.tasks.v2beta2.AttemptStatus] information is
  // not retained by Cloud Tasks.
  //
  // This field is not calculated for [pull
  // tasks][google.cloud.tasks.v2beta2.PullMessage].
  AttemptStatus first_attempt_status = 3;

  // Output only. The status of the task's last attempt.
  //
  // This field is not calculated for [pull
  // tasks][google.cloud.tasks.v2beta2.PullMessage].
  AttemptStatus last_attempt_status = 4;
}

// The status of a task attempt.
message AttemptStatus {
  // Output only. The time that this attempt was scheduled.
  //
  // `schedule_time` will be truncated to the nearest microsecond.
  google.protobuf.Timestamp schedule_time = 1;

  // Output only. The time that this attempt was dispatched.
  //
  // `dispatch_time` will be truncated to the nearest microsecond.
  google.protobuf.Timestamp dispatch_time = 2;

  // Output only. The time that this attempt response was received.
  //
  // `response_time` will be truncated to the nearest microsecond.
  google.protobuf.Timestamp response_time = 3;

  // Output only. The response from the target for this attempt.
  //
  // If the task has not been attempted or the task is currently running
  // then the response status is unset.
  google.rpc.Status response_status = 4;
}
