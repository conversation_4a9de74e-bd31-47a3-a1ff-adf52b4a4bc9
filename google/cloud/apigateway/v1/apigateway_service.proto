// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apigateway.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/cloud/apigateway/v1/apigateway.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.ApiGateway.V1";
option go_package = "cloud.google.com/go/apigateway/apiv1/apigatewaypb;apigatewaypb";
option java_multiple_files = true;
option java_package = "com.google.cloud.apigateway.v1";
option php_namespace = "Google\\Cloud\\ApiGateway\\V1";
option ruby_package = "Google::Cloud::ApiGateway::V1";

// The API Gateway Service is the interface for managing API Gateways.
service ApiGatewayService {
  option (google.api.default_host) = "apigateway.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists Gateways in a given project and location.
  rpc ListGateways(ListGatewaysRequest) returns (ListGatewaysResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/gateways"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Gateway.
  rpc GetGateway(GetGatewayRequest) returns (Gateway) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/gateways/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Gateway in a given project and location.
  rpc CreateGateway(CreateGatewayRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/gateways"
      body: "gateway"
    };
    option (google.api.method_signature) = "parent,gateway,gateway_id";
    option (google.longrunning.operation_info) = {
      response_type: "Gateway"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Gateway.
  rpc UpdateGateway(UpdateGatewayRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{gateway.name=projects/*/locations/*/gateways/*}"
      body: "gateway"
    };
    option (google.api.method_signature) = "gateway,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Gateway"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Gateway.
  rpc DeleteGateway(DeleteGatewayRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/gateways/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Apis in a given project and location.
  rpc ListApis(ListApisRequest) returns (ListApisResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/apis"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Api.
  rpc GetApi(GetApiRequest) returns (Api) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Api in a given project and location.
  rpc CreateApi(CreateApiRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/apis"
      body: "api"
    };
    option (google.api.method_signature) = "parent,api,api_id";
    option (google.longrunning.operation_info) = {
      response_type: "Api"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Api.
  rpc UpdateApi(UpdateApiRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{api.name=projects/*/locations/*/apis/*}"
      body: "api"
    };
    option (google.api.method_signature) = "api,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Api"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Api.
  rpc DeleteApi(DeleteApiRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists ApiConfigs in a given project and location.
  rpc ListApiConfigs(ListApiConfigsRequest) returns (ListApiConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/apis/*}/configs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ApiConfig.
  rpc GetApiConfig(GetApiConfigRequest) returns (ApiConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/apis/*/configs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new ApiConfig in a given project and location.
  rpc CreateApiConfig(CreateApiConfigRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/apis/*}/configs"
      body: "api_config"
    };
    option (google.api.method_signature) = "parent,api_config,api_config_id";
    option (google.longrunning.operation_info) = {
      response_type: "ApiConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single ApiConfig.
  rpc UpdateApiConfig(UpdateApiConfigRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{api_config.name=projects/*/locations/*/apis/*/configs/*}"
      body: "api_config"
    };
    option (google.api.method_signature) = "api_config,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ApiConfig"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single ApiConfig.
  rpc DeleteApiConfig(DeleteApiConfigRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/apis/*/configs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}
