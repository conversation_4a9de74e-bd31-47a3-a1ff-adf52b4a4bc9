# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "apigateway_proto",
    srcs = [
        "apigateway.proto",
        "apigateway_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "apigateway_proto_with_info",
    deps = [
        ":apigateway_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "apigateway_java_proto",
    deps = [":apigateway_proto"],
)

java_grpc_library(
    name = "apigateway_java_grpc",
    srcs = [":apigateway_proto"],
    deps = [":apigateway_java_proto"],
)

java_gapic_library(
    name = "apigateway_java_gapic",
    srcs = [":apigateway_proto_with_info"],
    grpc_service_config = "apigateway_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apigateway_v1.yaml",
    test_deps = [
        ":apigateway_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":apigateway_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "apigateway_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.apigateway.v1.ApiGatewayServiceClientHttpJsonTest",
        "com.google.cloud.apigateway.v1.ApiGatewayServiceClientTest",
    ],
    runtime_deps = [":apigateway_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-apigateway-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":apigateway_java_gapic",
        ":apigateway_java_grpc",
        ":apigateway_java_proto",
        ":apigateway_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "apigateway_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/apigateway/apiv1/apigatewaypb",
    protos = [":apigateway_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "apigateway_go_gapic",
    srcs = [":apigateway_proto_with_info"],
    grpc_service_config = "apigateway_grpc_service_config.json",
    importpath = "cloud.google.com/go/apigateway/apiv1;apigateway",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "apigateway_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apigateway_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-apigateway-v1-go",
    deps = [
        ":apigateway_go_gapic",
        ":apigateway_go_gapic_srcjar-metadata.srcjar",
        ":apigateway_go_gapic_srcjar-snippets.srcjar",
        ":apigateway_go_gapic_srcjar-test.srcjar",
        ":apigateway_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "apigateway_py_gapic",
    srcs = [":apigateway_proto"],
    grpc_service_config = "apigateway_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-api-gateway"],
    rest_numeric_enums = True,
    service_yaml = "apigateway_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "apigateway_py_gapic_test",
    srcs = [
        "apigateway_py_gapic_pytest.py",
        "apigateway_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":apigateway_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-apigateway-v1-py",
    deps = [
        ":apigateway_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "apigateway_php_proto",
    deps = [":apigateway_proto"],
)

php_gapic_library(
    name = "apigateway_php_gapic",
    srcs = [":apigateway_proto_with_info"],
    grpc_service_config = "apigateway_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "apigateway_v1.yaml",
    transport = "grpc+rest",
    deps = [":apigateway_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-apigateway-v1-php",
    deps = [
        ":apigateway_php_gapic",
        ":apigateway_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "apigateway_nodejs_gapic",
    package_name = "@google-cloud/api-gateway",
    src = ":apigateway_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "apigateway_grpc_service_config.json",
    package = "google.cloud.apigateway.v1",
    rest_numeric_enums = True,
    service_yaml = "apigateway_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "apigateway-v1-nodejs",
    deps = [
        ":apigateway_nodejs_gapic",
        ":apigateway_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "apigateway_ruby_proto",
    deps = [":apigateway_proto"],
)

ruby_grpc_library(
    name = "apigateway_ruby_grpc",
    srcs = [":apigateway_proto"],
    deps = [":apigateway_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "apigateway_ruby_gapic",
    srcs = [":apigateway_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-api_gateway-v1",
        "ruby-cloud-env-prefix=API_GATEWAY",
        "ruby-cloud-product-url=https://cloud.google.com/api-gateway/",
        "ruby-cloud-api-id=apigateway.googleapis.com",
        "ruby-cloud-api-shortname=apigateway",
    ],
    grpc_service_config = "apigateway_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "API Gateway enables you to provide secure access to your backend services through a well-defined REST API that is consistent across all of your services, regardless of the service implementation. Clients consume your REST APIS to implement standalone apps for a mobile device or tablet, through apps running in a browser, or through any other type of app that can make a request to an HTTP endpoint.",
    ruby_cloud_title = "API Gateway V1",
    service_yaml = "apigateway_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apigateway_ruby_grpc",
        ":apigateway_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-apigateway-v1-ruby",
    deps = [
        ":apigateway_ruby_gapic",
        ":apigateway_ruby_grpc",
        ":apigateway_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "apigateway_csharp_proto",
    deps = [":apigateway_proto"],
)

csharp_grpc_library(
    name = "apigateway_csharp_grpc",
    srcs = [":apigateway_proto"],
    deps = [":apigateway_csharp_proto"],
)

csharp_gapic_library(
    name = "apigateway_csharp_gapic",
    srcs = [":apigateway_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "apigateway_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apigateway_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":apigateway_csharp_grpc",
        ":apigateway_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-apigateway-v1-csharp",
    deps = [
        ":apigateway_csharp_gapic",
        ":apigateway_csharp_grpc",
        ":apigateway_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "apigateway_cc_proto",
    deps = [":apigateway_proto"],
)

cc_grpc_library(
    name = "apigateway_cc_grpc",
    srcs = [":apigateway_proto"],
    grpc_only = True,
    deps = [":apigateway_cc_proto"],
)
