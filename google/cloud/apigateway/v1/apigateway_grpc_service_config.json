{"methodConfig": [{"name": [{"service": "google.cloud.apigateway.v1.ApiGatewayService", "method": "CreateApi"}, {"service": "google.cloud.apigateway.v1.ApiGatewayService", "method": "CreateApiConfig"}, {"service": "google.cloud.apigateway.v1.ApiGatewayService", "method": "CreateGateway"}, {"service": "google.cloud.apigateway.v1.ApiGatewayService", "method": "UpdateA<PERSON>"}, {"service": "google.cloud.apigateway.v1.ApiGatewayService", "method": "UpdateApiConfig"}, {"service": "google.cloud.apigateway.v1.ApiGatewayService", "method": "UpdateGateway"}, {"service": "google.cloud.apigateway.v1.ApiGatewayService", "method": "DeleteApi"}, {"service": "google.cloud.apigateway.v1.ApiGatewayService", "method": "DeleteApiConfig"}, {"service": "google.cloud.apigateway.v1.ApiGatewayService", "method": "DeleteGateway"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 2, "retryableStatusCodes": ["UNKNOWN", "UNAVAILABLE"]}}]}