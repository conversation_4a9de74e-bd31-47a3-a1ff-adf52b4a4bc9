# This build file includes a target for the Ruby wrapper library for
# google-cloud-api_gateway.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for apigateway.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "apigateway_ruby_wrapper",
    srcs = ["//google/cloud/apigateway/v1:apigateway_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-api_gateway",
        "ruby-cloud-env-prefix=API_GATEWAY",
        "ruby-cloud-wrapper-of=v1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/api-gateway/",
        "ruby-cloud-api-id=apigateway.googleapis.com",
        "ruby-cloud-api-shortname=apigateway",
    ],
    ruby_cloud_description = "API Gateway enables you to provide secure access to your backend services through a well-defined REST API that is consistent across all of your services, regardless of the service implementation. Clients consume your REST APIS to implement standalone apps for a mobile device or tablet, through apps running in a browser, or through any other type of app that can make a request to an HTTP endpoint.",
    ruby_cloud_title = "API Gateway",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-apigateway-ruby",
    deps = [
        ":apigateway_ruby_wrapper",
    ],
)
