// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.modelarmor.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/modelarmor/apiv1/modelarmorpb;modelarmorpb";
option java_multiple_files = true;
option java_outer_classname = "V1mainProto";
option java_package = "com.google.cloud.modelarmor.v1";

// Service describing handlers for resources
service ModelArmor {
  option (google.api.default_host) = "modelarmor.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Templates in a given project and location.
  rpc ListTemplates(ListTemplatesRequest) returns (ListTemplatesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/templates"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Template.
  rpc GetTemplate(GetTemplateRequest) returns (Template) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/templates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Template in a given project and location.
  rpc CreateTemplate(CreateTemplateRequest) returns (Template) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/templates"
      body: "template"
    };
    option (google.api.method_signature) = "parent,template,template_id";
  }

  // Updates the parameters of a single Template.
  rpc UpdateTemplate(UpdateTemplateRequest) returns (Template) {
    option (google.api.http) = {
      patch: "/v1/{template.name=projects/*/locations/*/templates/*}"
      body: "template"
    };
    option (google.api.method_signature) = "template,update_mask";
  }

  // Deletes a single Template.
  rpc DeleteTemplate(DeleteTemplateRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/templates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets details of a single floor setting of a project
  rpc GetFloorSetting(GetFloorSettingRequest) returns (FloorSetting) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/floorSetting}"
      additional_bindings {
        get: "/v1/{name=folders/*/locations/*/floorSetting}"
      }
      additional_bindings {
        get: "/v1/{name=organizations/*/locations/*/floorSetting}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the parameters of a single floor setting of a project
  rpc UpdateFloorSetting(UpdateFloorSettingRequest) returns (FloorSetting) {
    option (google.api.http) = {
      patch: "/v1/{floor_setting.name=projects/*/locations/*/floorSetting}"
      body: "floor_setting"
      additional_bindings {
        patch: "/v1/{floor_setting.name=folders/*/locations/*/floorSetting}"
        body: "floor_setting"
      }
      additional_bindings {
        patch: "/v1/{floor_setting.name=organizations/*/locations/*/floorSetting}"
        body: "floor_setting"
      }
    };
    option (google.api.method_signature) = "floor_setting,update_mask";
  }

  // Sanitizes User Prompt.
  rpc SanitizeUserPrompt(SanitizeUserPromptRequest)
      returns (SanitizeUserPromptResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/templates/*}:sanitizeUserPrompt"
      body: "*"
    };
  }

  // Sanitizes Model Response.
  rpc SanitizeModelResponse(SanitizeModelResponseRequest)
      returns (SanitizeModelResponseResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/templates/*}:sanitizeModelResponse"
      body: "*"
    };
  }
}

// Option to specify filter match state.
enum FilterMatchState {
  // Unused
  FILTER_MATCH_STATE_UNSPECIFIED = 0;

  // Matching criteria is not achieved for filters.
  NO_MATCH_FOUND = 1;

  // Matching criteria is achieved for the filter.
  MATCH_FOUND = 2;
}

// Enum which reports whether a specific filter executed successfully or not.
enum FilterExecutionState {
  // Unused
  FILTER_EXECUTION_STATE_UNSPECIFIED = 0;

  // Filter executed successfully
  EXECUTION_SUCCESS = 1;

  // Filter execution was skipped. This can happen due to server-side error
  // or permission issue.
  EXECUTION_SKIPPED = 2;
}

// Options for responsible AI Filter Types.
enum RaiFilterType {
  // Unspecified filter type.
  RAI_FILTER_TYPE_UNSPECIFIED = 0;

  // Sexually Explicit.
  SEXUALLY_EXPLICIT = 2;

  // Hate Speech.
  HATE_SPEECH = 3;

  // Harassment.
  HARASSMENT = 6;

  // Danger
  DANGEROUS = 17;
}

// Confidence levels for detectors.
// Higher value maps to a greater confidence level. To enforce stricter level a
// lower value should be used.
enum DetectionConfidenceLevel {
  // Same as LOW_AND_ABOVE.
  DETECTION_CONFIDENCE_LEVEL_UNSPECIFIED = 0;

  // Highest chance of a false positive.
  LOW_AND_ABOVE = 1;

  // Some chance of false positives.
  MEDIUM_AND_ABOVE = 2;

  // Low chance of false positives.
  HIGH = 3;
}

// For more information about each Sensitive Data Protection likelihood level,
// see https://cloud.google.com/sensitive-data-protection/docs/likelihood.
enum SdpFindingLikelihood {
  // Default value; same as POSSIBLE.
  SDP_FINDING_LIKELIHOOD_UNSPECIFIED = 0;

  // Highest chance of a false positive.
  VERY_UNLIKELY = 1;

  // High chance of a false positive.
  UNLIKELY = 2;

  // Some matching signals. The default value.
  POSSIBLE = 3;

  // Low chance of a false positive.
  LIKELY = 4;

  // Confidence level is high. Lowest chance of a false positive.
  VERY_LIKELY = 5;
}

// A field indicating the outcome of the invocation, irrespective of match
// status.
enum InvocationResult {
  // Unused. Default value.
  INVOCATION_RESULT_UNSPECIFIED = 0;

  // All filters were invoked successfully.
  SUCCESS = 1;

  // Some filters were skipped or failed.
  PARTIAL = 2;

  // All filters were skipped or failed.
  FAILURE = 3;
}

// Message describing Template resource
message Template {
  option (google.api.resource) = {
    type: "modelarmor.googleapis.com/Template"
    pattern: "projects/{project}/locations/{location}/templates/{template}"
    plural: "templates"
    singular: "template"
  };

  // Message describing TemplateMetadata
  message TemplateMetadata {
    // Optional. If true, partial detector failures should be ignored.
    bool ignore_partial_invocation_failures = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Indicates the custom error code set by the user to be returned
    // to the end user by the service extension if the prompt trips Model Armor
    // filters.
    int32 custom_prompt_safety_error_code = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Indicates the custom error message set by the user to be
    // returned to the end user if the prompt trips Model Armor filters.
    string custom_prompt_safety_error_message = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Indicates the custom error code set by the user to be returned
    // to the end user if the LLM response trips Model Armor filters.
    int32 custom_llm_response_safety_error_code = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Indicates the custom error message set by the user to be
    // returned to the end user if the LLM response trips Model Armor filters.
    string custom_llm_response_safety_error_message = 5
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true, log template crud operations.
    bool log_template_operations = 6 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true, log sanitize operations.
    bool log_sanitize_operations = 7 [(google.api.field_behavior) = OPTIONAL];
  }

  // Identifier. name of resource
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. [Output only] Create time stamp
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update time stamp
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels as key value pairs
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. filter configuration for this template
  FilterConfig filter_config = 5 [(google.api.field_behavior) = REQUIRED];

  // Optional. metadata for this template
  TemplateMetadata template_metadata = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Message describing FloorSetting resource
message FloorSetting {
  option (google.api.resource) = {
    type: "modelarmor.googleapis.com/FloorSetting"
    pattern: "projects/{project}/locations/{location}/floorSetting"
    pattern: "folders/{folder}/locations/{location}/floorSetting"
    pattern: "organizations/{organization}/locations/{location}/floorSetting"
    plural: "floorSettings"
    singular: "floorSetting"
  };

  // Identifier. The resource name.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. [Output only] Create timestamp
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update timestamp
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. ModelArmor filter configuration.
  FilterConfig filter_config = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. Floor Settings enforcement status.
  bool enable_floor_setting_enforcement = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Templates
message ListTemplatesRequest {
  // Required. Parent value for ListTemplatesRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "modelarmor.googleapis.com/Template"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing Templates
message ListTemplatesResponse {
  // The list of Template
  repeated Template templates = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Template
message GetTemplateRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "modelarmor.googleapis.com/Template"
    }
  ];
}

// Message for creating a Template
message CreateTemplateRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "modelarmor.googleapis.com/Template"
    }
  ];

  // Required. Id of the requesting object
  // If auto-generating Id server-side, remove this field and
  // template_id from the method_signature of Create RPC
  string template_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Template template = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server stores the
  // request ID for 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for updating a Template
message UpdateTemplateRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Template resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  Template template = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server stores the
  // request ID for 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for deleting a Template
message DeleteTemplateRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "modelarmor.googleapis.com/Template"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server stores the
  // request ID for 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Message for getting a Floor Setting
message GetFloorSettingRequest {
  // Required. The name of the floor setting to get, example
  // projects/123/floorsetting.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "modelarmor.googleapis.com/FloorSetting"
    }
  ];
}

// Message for Updating a Floor Setting
message UpdateFloorSettingRequest {
  // Required. The floor setting being updated.
  FloorSetting floor_setting = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Field mask is used to specify the fields to be overwritten in the
  // FloorSetting resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Filters configuration.
message FilterConfig {
  // Optional. Responsible AI settings.
  RaiFilterSettings rai_settings = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Sensitive Data Protection settings.
  SdpFilterSettings sdp_settings = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Prompt injection and Jailbreak filter settings.
  PiAndJailbreakFilterSettings pi_and_jailbreak_filter_settings = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Malicious URI filter settings.
  MaliciousUriFilterSettings malicious_uri_filter_settings = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Prompt injection and Jailbreak Filter settings.
message PiAndJailbreakFilterSettings {
  // Option to specify the state of Prompt Injection and Jailbreak filter
  // (ENABLED/DISABLED).
  enum PiAndJailbreakFilterEnforcement {
    // Same as Disabled
    PI_AND_JAILBREAK_FILTER_ENFORCEMENT_UNSPECIFIED = 0;

    // Enabled
    ENABLED = 1;

    // Enabled
    DISABLED = 2;
  }

  // Optional. Tells whether Prompt injection and Jailbreak filter is enabled or
  // disabled.
  PiAndJailbreakFilterEnforcement filter_enforcement = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Confidence level for this filter.
  // Confidence level is used to determine the threshold for the filter. If
  // detection confidence is equal to or greater than the specified level, a
  // positive match is reported. Confidence level will only be used if the
  // filter is enabled.
  DetectionConfidenceLevel confidence_level = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Malicious URI filter settings.
message MaliciousUriFilterSettings {
  // Option to specify the state of Malicious URI filter (ENABLED/DISABLED).
  enum MaliciousUriFilterEnforcement {
    // Same as Disabled
    MALICIOUS_URI_FILTER_ENFORCEMENT_UNSPECIFIED = 0;

    // Enabled
    ENABLED = 1;

    // Disabled
    DISABLED = 2;
  }

  // Optional. Tells whether the Malicious URI filter is enabled or disabled.
  MaliciousUriFilterEnforcement filter_enforcement = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// Responsible AI Filter settings.
message RaiFilterSettings {
  // Responsible AI filter.
  message RaiFilter {
    // Required. Type of responsible AI filter.
    RaiFilterType filter_type = 1 [(google.api.field_behavior) = REQUIRED];

    // Optional. Confidence level for this RAI filter.
    // During data sanitization, if data is classified under this filter with a
    // confidence level equal to or greater than the specified level, a positive
    // match is reported. If the confidence level is unspecified (i.e., 0), the
    // system will use a reasonable default level based on the `filter_type`.
    DetectionConfidenceLevel confidence_level = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. List of Responsible AI filters enabled for template.
  repeated RaiFilter rai_filters = 1 [(google.api.field_behavior) = REQUIRED];
}

// Sensitive Data Protection settings.
message SdpFilterSettings {
  // Either of Sensitive Data Protection basic or advanced configuration.
  oneof sdp_configuration {
    // Optional. Basic Sensitive Data Protection configuration inspects the
    // content for sensitive data using a fixed set of six info-types. Sensitive
    // Data Protection templates cannot be used with basic configuration. Only
    // Sensitive Data Protection inspection operation is supported with basic
    // configuration.
    SdpBasicConfig basic_config = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Advanced Sensitive Data Protection configuration which enables
    // use of Sensitive Data Protection templates. Supports both Sensitive Data
    // Protection inspection and de-identification operations.
    SdpAdvancedConfig advanced_config = 2
        [(google.api.field_behavior) = OPTIONAL];
  }
}

// Sensitive Data Protection basic configuration.
message SdpBasicConfig {
  // Option to specify the state of Sensitive Data Protection basic config
  // (ENABLED/DISABLED).
  enum SdpBasicConfigEnforcement {
    // Same as Disabled
    SDP_BASIC_CONFIG_ENFORCEMENT_UNSPECIFIED = 0;

    // Enabled
    ENABLED = 1;

    // Disabled
    DISABLED = 2;
  }

  // Optional. Tells whether the Sensitive Data Protection basic config is
  // enabled or disabled.
  SdpBasicConfigEnforcement filter_enforcement = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Sensitive Data Protection Advanced configuration.
message SdpAdvancedConfig {
  // Optional. Sensitive Data Protection inspect template resource name
  //
  // If only inspect template is provided (de-identify template not provided),
  // then Sensitive Data Protection InspectContent action is performed during
  // Sanitization. All Sensitive Data Protection findings identified during
  // inspection will be returned as SdpFinding in SdpInsepctionResult e.g.
  // `organizations/{organization}/inspectTemplates/{inspect_template}`,
  // `projects/{project}/inspectTemplates/{inspect_template}`
  // `organizations/{organization}/locations/{location}/inspectTemplates/{inspect_template}`
  // `projects/{project}/locations/{location}/inspectTemplates/{inspect_template}`
  string inspect_template = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional Sensitive Data Protection Deidentify template resource
  // name.
  //
  // If provided then DeidentifyContent action is performed during Sanitization
  // using this template and inspect template. The De-identified data will
  // be returned in SdpDeidentifyResult.
  // Note that all info-types present in the deidentify template must be present
  // in inspect template.
  //
  // e.g.
  // `organizations/{organization}/deidentifyTemplates/{deidentify_template}`,
  // `projects/{project}/deidentifyTemplates/{deidentify_template}`
  // `organizations/{organization}/locations/{location}/deidentifyTemplates/{deidentify_template}`
  // `projects/{project}/locations/{location}/deidentifyTemplates/{deidentify_template}`
  string deidentify_template = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Sanitize User Prompt request.
message SanitizeUserPromptRequest {
  // Required. Represents resource name of template
  // e.g. name=projects/sample-project/locations/us-central1/templates/templ01
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "modelarmor.googleapis.com/Template"
    }
  ];

  // Required. User prompt data to sanitize.
  DataItem user_prompt_data = 2 [(google.api.field_behavior) = REQUIRED];
}

// Sanitize Model Response request.
message SanitizeModelResponseRequest {
  // Required. Represents resource name of template
  // e.g. name=projects/sample-project/locations/us-central1/templates/templ01
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "modelarmor.googleapis.com/Template"
    }
  ];

  // Required. Model response data to sanitize.
  DataItem model_response_data = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. User Prompt associated with Model response.
  string user_prompt = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Sanitized User Prompt Response.
message SanitizeUserPromptResponse {
  // Output only. Sanitization Result.
  SanitizationResult sanitization_result = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Sanitized Model Response Response.
message SanitizeModelResponseResponse {
  // Output only. Sanitization Result.
  SanitizationResult sanitization_result = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Sanitization result after applying all the filters on input content.
message SanitizationResult {
  // Message describing Sanitization metadata.
  message SanitizationMetadata {
    // Error code if any.
    int64 error_code = 1;

    // Error message if any.
    string error_message = 2;
  }

  // Output only. Overall filter match state for Sanitization.
  // The state can have below two values.
  //
  // 1) NO_MATCH_FOUND: No filters in configuration satisfy matching criteria.
  // In other words, input passed all filters.
  //
  // 2) MATCH_FOUND: At least one filter in configuration satisfies matching.
  // In other words, input did not pass one or more filters.
  FilterMatchState filter_match_state = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Results for all filters where the key is the filter name -
  // either of "csam", "malicious_uris", "rai", "pi_and_jailbreak" ,"sdp".
  map<string, FilterResult> filter_results = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A field indicating the outcome of the invocation, irrespective
  // of match status. It can have the following three values: SUCCESS: All
  // filters were executed successfully. PARTIAL: Some filters were skipped or
  // failed execution. FAILURE: All filters were skipped or failed execution.
  InvocationResult invocation_result = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Metadata related to Sanitization.
  SanitizationMetadata sanitization_metadata = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Filter Result obtained after Sanitization operations.
message FilterResult {
  // Encapsulates one of responsible AI, Sensitive Data Protection, Prompt
  // Injection and Jailbreak, Malicious URI, CSAM, Virus Scan related filter
  // results.
  oneof filter_result {
    // Responsible AI filter results.
    RaiFilterResult rai_filter_result = 1;

    // Sensitive Data Protection results.
    SdpFilterResult sdp_filter_result = 2;

    // Prompt injection and Jailbreak filter results.
    PiAndJailbreakFilterResult pi_and_jailbreak_filter_result = 3;

    // Malicious URI filter results.
    MaliciousUriFilterResult malicious_uri_filter_result = 4;

    // CSAM filter results.
    CsamFilterResult csam_filter_filter_result = 5;

    // Virus scan results.
    VirusScanFilterResult virus_scan_filter_result = 6;
  }
}

// Responsible AI Result.
message RaiFilterResult {
  // Detailed Filter result for each of the responsible AI Filter Types.
  message RaiFilterTypeResult {
    // Type of responsible AI filter.
    RaiFilterType filter_type = 1;

    // Confidence level identified for this RAI filter.
    DetectionConfidenceLevel confidence_level = 2;

    // Output only. Match state for this RAI filter.
    FilterMatchState match_state = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. Reports whether the RAI filter was successfully executed or
  // not.
  FilterExecutionState execution_state = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional messages corresponding to the result.
  // A message can provide warnings or error details.
  // For example, if execution state is skipped then this field provides
  // related reason/explanation.
  repeated MessageItem message_items = 2;

  // Output only. Overall filter match state for RAI.
  // Value is MATCH_FOUND if at least one RAI filter confidence level is
  // equal to or higher than the confidence level defined in configuration.
  FilterMatchState match_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The map of RAI filter results where key is RAI filter type - either of
  // "sexually_explicit", "hate_speech", "harassment", "dangerous".
  map<string, RaiFilterTypeResult> rai_filter_type_results = 4;
}

// Sensitive Data Protection filter result.
message SdpFilterResult {
  // Either of Sensitive Data Protection Inspect result or Deidentify result.
  oneof result {
    // Sensitive Data Protection Inspection result if inspection is performed.
    SdpInspectResult inspect_result = 1;

    // Sensitive Data Protection Deidentification result if deidentification is
    // performed.
    SdpDeidentifyResult deidentify_result = 2;
  }
}

// Sensitive Data Protection Inspection Result.
message SdpInspectResult {
  // Output only. Reports whether Sensitive Data Protection inspection was
  // successfully executed or not.
  FilterExecutionState execution_state = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional messages corresponding to the result.
  // A message can provide warnings or error details.
  // For example, if execution state is skipped then this field provides
  // related reason/explanation.
  repeated MessageItem message_items = 2;

  // Output only. Match state for SDP Inspection.
  // Value is MATCH_FOUND if at least one Sensitive Data Protection finding is
  // identified.
  FilterMatchState match_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // List of Sensitive Data Protection findings.
  repeated SdpFinding findings = 4;

  // If true, then there is possibility that more findings were identified and
  // the findings returned are a subset of all findings. The findings
  // list might be truncated because the input items were too large, or because
  // the server reached the maximum amount of resources allowed for a single API
  // call.
  bool findings_truncated = 5;
}

// Represents Data item
message DataItem {
  // Either of text or bytes data.
  oneof data_item {
    // Plaintext string data for sanitization.
    string text = 1;

    // Data provided in the form of bytes.
    ByteDataItem byte_item = 2;
  }
}

// Represents Byte Data item.
message ByteDataItem {
  // Option to specify the type of byte data.
  enum ByteItemType {
    // Unused
    BYTE_ITEM_TYPE_UNSPECIFIED = 0;

    // plain text
    PLAINTEXT_UTF8 = 1;

    // PDF
    PDF = 2;
  }

  // Required. The type of byte data
  ByteItemType byte_data_type = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Bytes Data
  bytes byte_data = 2 [(google.api.field_behavior) = REQUIRED];
}

// Sensitive Data Protection Deidentification Result.
message SdpDeidentifyResult {
  // Output only. Reports whether Sensitive Data Protection deidentification was
  // successfully executed or not.
  FilterExecutionState execution_state = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional messages corresponding to the result.
  // A message can provide warnings or error details.
  // For example, if execution state is skipped then this field provides
  // related reason/explanation.
  repeated MessageItem message_items = 2;

  // Output only. Match state for Sensitive Data Protection Deidentification.
  // Value is MATCH_FOUND if content is de-identified.
  FilterMatchState match_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // De-identified data.
  DataItem data = 4;

  // Total size in bytes that were transformed during deidentification.
  int64 transformed_bytes = 5;
}

// Finding corresponding to Sensitive Data Protection filter.
message SdpFinding {
  // Location of this Sensitive Data Protection Finding within input content.
  message SdpFindingLocation {
    // Zero-based byte offsets delimiting the finding.
    // These are relative to the finding's containing element.
    // Note that when the content is not textual, this references
    // the UTF-8 encoded textual representation of the content.
    RangeInfo byte_range = 1;

    // Unicode character offsets delimiting the finding.
    // These are relative to the finding's containing element.
    // Provided when the content is text.
    RangeInfo codepoint_range = 2;
  }

  // Name of Sensitive Data Protection info type for this finding.
  string info_type = 1;

  // Identified confidence likelihood for `info_type`.
  SdpFindingLikelihood likelihood = 2;

  // Location for this finding.
  SdpFindingLocation location = 3;
}

// Prompt injection and Jailbreak Filter Result.
message PiAndJailbreakFilterResult {
  // Output only. Reports whether Prompt injection and Jailbreak filter was
  // successfully executed or not.
  FilterExecutionState execution_state = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional messages corresponding to the result.
  // A message can provide warnings or error details.
  // For example, if execution state is skipped then this field provides
  // related reason/explanation.
  repeated MessageItem message_items = 2;

  // Output only. Match state for Prompt injection and Jailbreak.
  FilterMatchState match_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Confidence level identified for Prompt injection and Jailbreak.
  DetectionConfidenceLevel confidence_level = 5;
}

// Malicious URI Filter Result.
message MaliciousUriFilterResult {
  // Information regarding malicious URI and its location within the input
  // content.
  message MaliciousUriMatchedItem {
    // Malicious URI.
    string uri = 1;

    // List of locations where Malicious URI is identified.
    // The `locations` field is supported only for plaintext content i.e.
    // ByteItemType.PLAINTEXT_UTF8
    repeated RangeInfo locations = 2;
  }

  // Output only. Reports whether Malicious URI filter was successfully executed
  // or not.
  FilterExecutionState execution_state = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional messages corresponding to the result.
  // A message can provide warnings or error details.
  // For example, if execution state is skipped then this field provides
  // related reason/explanation.
  repeated MessageItem message_items = 2;

  // Output only. Match state for this Malicious URI.
  // Value is MATCH_FOUND if at least one Malicious URI is found.
  FilterMatchState match_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // List of Malicious URIs found in data.
  repeated MaliciousUriMatchedItem malicious_uri_matched_items = 4;
}

// Virus scan results.
message VirusScanFilterResult {
  // Type of content scanned.
  enum ScannedContentType {
    // Unused
    SCANNED_CONTENT_TYPE_UNSPECIFIED = 0;

    // Unknown content
    UNKNOWN = 1;

    // Plaintext
    PLAINTEXT = 2;

    // PDF
    // Scanning for only PDF is supported.
    PDF = 3;
  }

  // Output only. Reports whether Virus Scan was successfully executed or not.
  FilterExecutionState execution_state = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional messages corresponding to the result.
  // A message can provide warnings or error details.
  // For example, if execution status is skipped then this field provides
  // related reason/explanation.
  repeated MessageItem message_items = 2;

  // Output only. Match status for Virus.
  // Value is MATCH_FOUND if the data is infected with a virus.
  FilterMatchState match_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Type of content scanned.
  ScannedContentType scanned_content_type = 4;

  // Size of scanned content in bytes.
  optional int64 scanned_size = 5;

  // List of Viruses identified.
  // This field will be empty if no virus was detected.
  repeated VirusDetail virus_details = 6;
}

// Details of an identified virus
message VirusDetail {
  // Defines all the threat types of a virus
  enum ThreatType {
    // Unused
    THREAT_TYPE_UNSPECIFIED = 0;

    // Unable to categorize threat
    UNKNOWN = 1;

    // Virus or Worm threat.
    VIRUS_OR_WORM = 2;

    // Malicious program. E.g. Spyware, Trojan.
    MALICIOUS_PROGRAM = 3;

    // Potentially harmful content. E.g. Injected code, Macro
    POTENTIALLY_HARMFUL_CONTENT = 4;

    // Potentially unwanted content. E.g. Adware.
    POTENTIALLY_UNWANTED_CONTENT = 5;
  }

  // Name of vendor that produced this virus identification.
  string vendor = 1;

  // Names of this Virus.
  repeated string names = 2;

  // Threat type of the identified virus
  ThreatType threat_type = 3;
}

// CSAM (Child Safety Abuse Material) Filter Result
message CsamFilterResult {
  // Output only. Reports whether the CSAM filter was successfully executed or
  // not.
  FilterExecutionState execution_state = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional messages corresponding to the result.
  // A message can provide warnings or error details.
  // For example, if execution state is skipped then this field provides
  // related reason/explanation.
  repeated MessageItem message_items = 2;

  // Output only. Match state for CSAM.
  FilterMatchState match_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message item to report information, warning or error messages.
message MessageItem {
  // Option to specify the type of message.
  enum MessageType {
    // Unused
    MESSAGE_TYPE_UNSPECIFIED = 0;

    // Information related message.
    INFO = 1;

    // Warning related message.
    WARNING = 2;

    // Error message.
    ERROR = 3;
  }

  // Type of message.
  MessageType message_type = 1;

  // The message content.
  string message = 2;
}

// Half-open range interval [start, end)
message RangeInfo {
  // For proto3, value cannot be set to 0 unless the field is optional.
  // Ref: https://protobuf.dev/programming-guides/proto3/#default
  // Index of first character (inclusive).
  optional int64 start = 1;

  // Index of last character (exclusive).
  optional int64 end = 2;
}
