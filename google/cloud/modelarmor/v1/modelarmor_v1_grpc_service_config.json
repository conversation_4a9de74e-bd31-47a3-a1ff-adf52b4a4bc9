{"methodConfig": [{"name": [{"service": "google.cloud.modelarmor.v1.ModelArmor", "method": "ListTemplates"}, {"service": "google.cloud.modelarmor.v1.ModelArmor", "method": "GetTemplate"}, {"service": "google.cloud.modelarmor.v1.ModelArmor", "method": "SanitizeUserPrompt"}, {"service": "google.cloud.modelarmor.v1.ModelArmor", "method": "SanitizeModelResponse"}, {"service": "google.cloud.modelarmor.v1.ModelArmor", "method": "GetFloorSetting"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.modelarmor.v1.ModelArmor", "method": "CreateTemplate"}, {"service": "google.cloud.modelarmor.v1.ModelArmor", "method": "DeleteTemplate"}, {"service": "google.cloud.modelarmor.v1.ModelArmor", "method": "UpdateTemplate"}, {"service": "google.cloud.modelarmor.v1.ModelArmor", "method": "UpdateFloorSetting"}], "timeout": "60s"}]}