type: google.api.Service
config_version: 3
name: modelarmor.googleapis.com
title: Model Armor API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.modelarmor.v1.ModelArmor

documentation:
  summary: |-
    Model Armor helps you protect against risks like prompt injection, harmful
    content, and data leakage in generative AI applications by letting you
    define policies that filter user prompts and model responses.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.modelarmor.v1.ModelArmor.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1514910&template=0
  documentation_uri: https://cloud.google.com/security-command-center/docs/model-armor-overview
  api_short_name: modelarmor
  github_label: 'api: modelarmor'
  doc_tag_prefix: modelarmor
  organization: CLOUD
  library_settings:
  - version: google.cloud.modelarmor.v1
    launch_stage: GA
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
