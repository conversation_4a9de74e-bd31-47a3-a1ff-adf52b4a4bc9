# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "modelarmor_proto",
    srcs = [
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "modelarmor_proto_with_info",
    deps = [
        ":modelarmor_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "modelarmor_java_proto",
    deps = [":modelarmor_proto"],
)

java_grpc_library(
    name = "modelarmor_java_grpc",
    srcs = [":modelarmor_proto"],
    deps = [":modelarmor_java_proto"],
)

java_gapic_library(
    name = "modelarmor_java_gapic",
    srcs = [":modelarmor_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "modelarmor_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "modelarmor_v1.yaml",
    test_deps = [
        ":modelarmor_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":modelarmor_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "modelarmor_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.modelarmor.v1.ModelArmorClientHttpJsonTest",
        "com.google.cloud.modelarmor.v1.ModelArmorClientTest",
    ],
    runtime_deps = [":modelarmor_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-modelarmor-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":modelarmor_java_gapic",
        ":modelarmor_java_grpc",
        ":modelarmor_java_proto",
        ":modelarmor_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "modelarmor_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/modelarmor/apiv1/modelarmorpb",
    protos = [":modelarmor_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "modelarmor_go_gapic",
    srcs = [":modelarmor_proto_with_info"],
    grpc_service_config = "modelarmor_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/modelarmor/apiv1;modelarmor",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "modelarmor_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":modelarmor_go_proto",
        "//google/cloud/location:location_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-modelarmor-v1-go",
    deps = [
        ":modelarmor_go_gapic",
        ":modelarmor_go_gapic_srcjar-metadata.srcjar",
        ":modelarmor_go_gapic_srcjar-snippets.srcjar",
        ":modelarmor_go_gapic_srcjar-test.srcjar",
        ":modelarmor_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "modelarmor_py_gapic",
    srcs = [":modelarmor_proto"],
    grpc_service_config = "modelarmor_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "modelarmor_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "modelarmor_py_gapic_test",
    srcs = [
        "modelarmor_py_gapic_pytest.py",
        "modelarmor_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":modelarmor_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "modelarmor-v1-py",
    deps = [
        ":modelarmor_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "modelarmor_php_proto",
    deps = [":modelarmor_proto"],
)

php_gapic_library(
    name = "modelarmor_php_gapic",
    srcs = [":modelarmor_proto_with_info"],
    grpc_service_config = "modelarmor_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "modelarmor_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":modelarmor_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-modelarmor-v1-php",
    deps = [
        ":modelarmor_php_gapic",
        ":modelarmor_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "modelarmor_nodejs_gapic",
    package_name = "@google-cloud/modelarmor",
    src = ":modelarmor_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "modelarmor_v1_grpc_service_config.json",
    package = "google.cloud.modelarmor.v1",
    rest_numeric_enums = True,
    service_yaml = "modelarmor_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "modelarmor-v1-nodejs",
    deps = [
        ":modelarmor_nodejs_gapic",
        ":modelarmor_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "modelarmor_ruby_proto",
    deps = [":modelarmor_proto"],
)

ruby_grpc_library(
    name = "modelarmor_ruby_grpc",
    srcs = [":modelarmor_proto"],
    deps = [":modelarmor_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "modelarmor_ruby_gapic",
    srcs = [":modelarmor_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-modelarmor-v1",
    ],
    grpc_service_config = "modelarmor_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "modelarmor_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":modelarmor_ruby_grpc",
        ":modelarmor_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-modelarmor-v1-ruby",
    deps = [
        ":modelarmor_ruby_gapic",
        ":modelarmor_ruby_grpc",
        ":modelarmor_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "modelarmor_csharp_proto",
    extra_opts = [],
    deps = [":modelarmor_proto"],
)

csharp_grpc_library(
    name = "modelarmor_csharp_grpc",
    srcs = [":modelarmor_proto"],
    deps = [":modelarmor_csharp_proto"],
)

csharp_gapic_library(
    name = "modelarmor_csharp_gapic",
    srcs = [":modelarmor_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "modelarmor_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "modelarmor_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":modelarmor_csharp_grpc",
        ":modelarmor_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-modelarmor-v1-csharp",
    deps = [
        ":modelarmor_csharp_gapic",
        ":modelarmor_csharp_grpc",
        ":modelarmor_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "modelarmor_cc_proto",
    deps = [":modelarmor_proto"],
)

cc_grpc_library(
    name = "modelarmor_cc_grpc",
    srcs = [":modelarmor_proto"],
    grpc_only = True,
    deps = [":modelarmor_cc_proto"],
)
