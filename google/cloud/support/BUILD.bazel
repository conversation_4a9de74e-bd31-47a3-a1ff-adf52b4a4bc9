# This build file includes a target for the Ruby wrapper library for
# google-cloud-support.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for support.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "support_ruby_wrapper",
    srcs = ["//google/cloud/support/v2:support_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-support",
        "ruby-cloud-wrapper-of=v2:0.3",
    ],
    service_yaml = "//google/cloud/support/v2:cloudsupport_v2.yaml",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-support-ruby",
    deps = [
        ":support_ruby_wrapper",
    ],
)

