// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.support.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/support/v2/comment.proto";

option csharp_namespace = "Google.Cloud.Support.V2";
option go_package = "cloud.google.com/go/support/apiv2/supportpb;supportpb";
option java_multiple_files = true;
option java_outer_classname = "CommentServiceProto";
option java_package = "com.google.cloud.support.v2";
option php_namespace = "Google\\Cloud\\Support\\V2";
option ruby_package = "Google::Cloud::Support::V2";

// A service to manage comments on cases.
service CommentService {
  option (google.api.default_host) = "cloudsupport.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Retrieve all Comments associated with the Case object.
  rpc ListComments(ListCommentsRequest) returns (ListCommentsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/cases/*}/comments"
      additional_bindings {
        get: "/v2/{parent=organizations/*/cases/*}/comments"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Add a new comment to the specified Case.
  // The comment object must have the following fields set: body.
  rpc CreateComment(CreateCommentRequest) returns (Comment) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/cases/*}/comments"
      body: "comment"
      additional_bindings {
        post: "/v2/{parent=organizations/*/cases/*}/comments"
        body: "comment"
      }
    };
    option (google.api.method_signature) = "parent,comment";
  }
}

// The request message for the ListComments endpoint.
message ListCommentsRequest {
  // Required. The resource name of Case object for which comments should be
  // listed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudsupport.googleapis.com/Case"
    }
  ];

  // The maximum number of comments fetched with each request. Defaults to 10.
  int32 page_size = 4;

  // A token identifying the page of results to return. If unspecified, the
  // first page is retrieved.
  string page_token = 5;
}

// The response message for the ListComments endpoint.
message ListCommentsResponse {
  // The list of Comments associated with the given Case.
  repeated Comment comments = 1;

  // A token to retrieve the next page of results. This should be set in the
  // `page_token` field of subsequent `ListCommentsRequest` message that is
  // issued. If unspecified, there are no more results to retrieve.
  string next_page_token = 2;
}

// The request message for CreateComment endpoint.
message CreateCommentRequest {
  // Required. The resource name of Case to which this comment should be added.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudsupport.googleapis.com/Case"
    }
  ];

  // Required. The Comment object to be added to this Case.
  Comment comment = 2 [(google.api.field_behavior) = REQUIRED];
}
