// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.support.v2;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Support.V2";
option go_package = "cloud.google.com/go/support/apiv2/supportpb;supportpb";
option java_multiple_files = true;
option java_outer_classname = "ActorProto";
option java_package = "com.google.cloud.support.v2";
option php_namespace = "Google\\Cloud\\Support\\V2";
option ruby_package = "Google::Cloud::Support::V2";

// An object containing information about the effective user and
// authenticated principal responsible for an action.
message Actor {
  // The name to display for the actor. If not provided, it is inferred from
  // credentials supplied during case creation. When an email is provided, a
  // display name must also be provided. This will be obfuscated if the user
  // is a Google Support agent.
  string display_name = 1;

  // The email address of the actor. If not provided, it is inferred from
  // credentials supplied during case creation. If the authenticated principal
  // does not have an email address, one must be provided. When a name is
  // provided, an email must also be provided. This will be obfuscated if the
  // user is a Google Support agent.
  string email = 2;

  // Output only. Whether the actor is a Google support actor.
  bool google_support = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}
