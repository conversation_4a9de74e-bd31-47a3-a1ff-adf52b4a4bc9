// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.support.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/support/v2/attachment.proto";

option csharp_namespace = "Google.Cloud.Support.V2";
option go_package = "cloud.google.com/go/support/apiv2/supportpb;supportpb";
option java_multiple_files = true;
option java_outer_classname = "AttachmentServiceProto";
option java_package = "com.google.cloud.support.v2";
option php_namespace = "Google\\Cloud\\Support\\V2";
option ruby_package = "Google::Cloud::Support::V2";

// A service to manage file attachment for Google Cloud support cases.
service CaseAttachmentService {
  option (google.api.default_host) = "cloudsupport.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Retrieve all attachments associated with a support case.
  rpc ListAttachments(ListAttachmentsRequest)
      returns (ListAttachmentsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/cases/*}/attachments"
      additional_bindings {
        get: "/v2/{parent=organizations/*/cases/*}/attachments"
      }
    };
    option (google.api.method_signature) = "parent";
  }
}

// The request message for the ListAttachments endpoint.
message ListAttachmentsRequest {
  // Required. The resource name of Case object for which attachments should be
  // listed.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudsupport.googleapis.com/Case"
    }
  ];

  // The maximum number of attachments fetched with each request. If not
  // provided, the default is 10. The maximum page size that will be returned is
  // 100.
  int32 page_size = 2;

  // A token identifying the page of results to return. If unspecified, the
  // first page is retrieved.
  string page_token = 3;
}

// The response message for the ListAttachments endpoint.
message ListAttachmentsResponse {
  // The list of attachments associated with the given case.
  repeated Attachment attachments = 1;

  // A token to retrieve the next page of results. This should be set in the
  // `page_token` field of subsequent `cases.attachments.list` requests. If
  // unspecified, there are no more results to retrieve.
  string next_page_token = 2;
}
