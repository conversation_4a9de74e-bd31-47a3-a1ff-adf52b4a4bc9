// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.support.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/support/v2/actor.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Support.V2";
option go_package = "cloud.google.com/go/support/apiv2/supportpb;supportpb";
option java_multiple_files = true;
option java_outer_classname = "AttachmentProto";
option java_package = "com.google.cloud.support.v2";
option php_namespace = "Google\\Cloud\\Support\\V2";
option ruby_package = "Google::Cloud::Support::V2";

// Represents a file attached to a support case.
message Attachment {
  option (google.api.resource) = {
    type: "cloudsupport.googleapis.com/Attachment"
    pattern: "organizations/{organization}/cases/{case}/attachments/{attachment_id}"
    pattern: "projects/{project}/cases/{case}/attachments/{attachment_id}"
  };

  // Output only. The resource name of the attachment.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the attachment was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user who uploaded the attachment. Note, the name and email
  // will be obfuscated if the attachment was uploaded by Google support.
  Actor creator = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The filename of the attachment (e.g. `"graph.jpg"`).
  string filename = 4;

  // Output only. The MIME type of the attachment (e.g. text/plain).
  string mime_type = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The size of the attachment in bytes.
  int64 size_bytes = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}
