// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.support.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/support/v2/actor.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Support.V2";
option go_package = "cloud.google.com/go/support/apiv2/supportpb;supportpb";
option java_multiple_files = true;
option java_outer_classname = "CaseProto";
option java_package = "com.google.cloud.support.v2";
option php_namespace = "Google\\Cloud\\Support\\V2";
option ruby_package = "Google::Cloud::Support::V2";

// A support case.
message Case {
  option (google.api.resource) = {
    type: "cloudsupport.googleapis.com/Case"
    pattern: "organizations/{organization}/cases/{case}"
    pattern: "projects/{project}/cases/{case}"
  };

  // The status of a support case.
  enum State {
    // Case is in an unknown state.
    STATE_UNSPECIFIED = 0;

    // The case has been created but no one is assigned to work on it yet.
    NEW = 1;

    // The case is currently being handled by Google support.
    IN_PROGRESS_GOOGLE_SUPPORT = 2;

    // Google is waiting for a response.
    ACTION_REQUIRED = 3;

    // A solution has been offered for the case, but it isn't yet closed.
    SOLUTION_PROVIDED = 4;

    // The case has been resolved.
    CLOSED = 5;
  }

  // The case Priority. P0 is most urgent and P4 the least.
  enum Priority {
    // Priority is undefined or has not been set yet.
    PRIORITY_UNSPECIFIED = 0;

    // Extreme impact on a production service. Service is hard down.
    P0 = 1;

    // Critical impact on a production service. Service is currently unusable.
    P1 = 2;

    // Severe impact on a production service. Service is usable but greatly
    // impaired.
    P2 = 3;

    // Medium impact on a production service.  Service is available, but
    // moderately impaired.
    P3 = 4;

    // General questions or minor issues.  Production service is fully
    // available.
    P4 = 5;
  }

  // The resource name for the case.
  string name = 1;

  // The short summary of the issue reported in this case.
  string display_name = 2;

  // A broad description of the issue.
  string description = 3;

  // The issue classification applicable to this case.
  CaseClassification classification = 4;

  // The timezone of the user who created the support case.
  // It should be in a format IANA recognizes: https://www.iana.org/time-zones.
  // There is no additional validation done by the API.
  string time_zone = 8;

  // The email addresses to receive updates on this case.
  repeated string subscriber_email_addresses = 9;

  // Output only. The current status of the support case.
  State state = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this case was created.
  google.protobuf.Timestamp create_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time this case was last updated.
  google.protobuf.Timestamp update_time = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The user who created the case.
  //
  // Note: The name and email will be obfuscated if the case was created by
  // Google Support.
  Actor creator = 15;

  // A user-supplied email address to send case update notifications for. This
  // should only be used in BYOID flows, where we cannot infer the user's email
  // address directly from their EUCs.
  string contact_email = 35;

  // Whether the case is currently escalated.
  bool escalated = 17;

  // Whether this case was created for internal API testing and should not be
  // acted on by the support team.
  bool test_case = 19;

  // The language the user has requested to receive support in. This should be a
  // BCP 47 language code (e.g., `"en"`, `"zh-CN"`, `"zh-TW"`, `"ja"`, `"ko"`).
  // If no language or an unsupported language is specified, this field defaults
  // to English (en).
  //
  // Language selection during case creation may affect your available support
  // options. For a list of supported languages and their support working hours,
  // see: https://cloud.google.com/support/docs/language-working-hours
  string language_code = 23;

  // The priority of this case.
  Priority priority = 32;
}

// A classification object with a product type and value.
message CaseClassification {
  // The unique ID for a classification. Must be specified for case creation.
  //
  // To retrieve valid classification IDs for case creation, use
  // `caseClassifications.search`.
  string id = 3;

  // The display name of the classification.
  string display_name = 4;
}
