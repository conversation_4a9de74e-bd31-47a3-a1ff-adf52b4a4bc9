// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.support.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/support/v2/case.proto";
import "google/cloud/support/v2/escalation.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Support.V2";
option go_package = "cloud.google.com/go/support/apiv2/supportpb;supportpb";
option java_multiple_files = true;
option java_outer_classname = "CaseServiceProto";
option java_package = "com.google.cloud.support.v2";
option php_namespace = "Google\\Cloud\\Support\\V2";
option ruby_package = "Google::Cloud::Support::V2";

// A service to manage Google Cloud support cases.
service CaseService {
  option (google.api.default_host) = "cloudsupport.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Retrieve the specified case.
  rpc GetCase(GetCaseRequest) returns (Case) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/cases/*}"
      additional_bindings { get: "/v2/{name=organizations/*/cases/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieve all cases under the specified parent.
  //
  // Note: Listing cases under an Organization returns only the cases directly
  // parented by that organization. To retrieve all cases under an organization,
  // including cases parented by projects under that organization, use
  // `cases.search`.
  rpc ListCases(ListCasesRequest) returns (ListCasesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*}/cases"
      additional_bindings { get: "/v2/{parent=organizations/*}/cases" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Search cases using the specified query.
  rpc SearchCases(SearchCasesRequest) returns (SearchCasesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*}/cases:search"
      additional_bindings { get: "/v2/{parent=organizations/*}/cases:search" }
    };
  }

  // Create a new case and associate it with the given Google Cloud Resource.
  // The case object must have the following fields set: `display_name`,
  // `description`, `classification`, and `priority`.
  rpc CreateCase(CreateCaseRequest) returns (Case) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*}/cases"
      body: "case"
      additional_bindings {
        post: "/v2/{parent=organizations/*}/cases"
        body: "case"
      }
    };
    option (google.api.method_signature) = "parent,case";
  }

  // Update the specified case. Only a subset of fields can be updated.
  rpc UpdateCase(UpdateCaseRequest) returns (Case) {
    option (google.api.http) = {
      patch: "/v2/{case.name=projects/*/cases/*}"
      body: "case"
      additional_bindings {
        patch: "/v2/{case.name=organizations/*/cases/*}"
        body: "case"
      }
    };
    option (google.api.method_signature) = "case,update_mask";
  }

  // Escalate a case. Escalating a case will initiate the Google Cloud Support
  // escalation management process.
  //
  // This operation is only available to certain Customer Care tiers. Go to
  // https://cloud.google.com/support and look for 'Technical support
  // escalations' in the feature list to find out which tiers are able to
  // perform escalations.
  rpc EscalateCase(EscalateCaseRequest) returns (Case) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/cases/*}:escalate"
      body: "*"
      additional_bindings {
        post: "/v2/{name=organizations/*/cases/*}:escalate"
        body: "*"
      }
    };
  }

  // Close the specified case.
  rpc CloseCase(CloseCaseRequest) returns (Case) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/cases/*}:close"
      body: "*"
      additional_bindings {
        post: "/v2/{name=organizations/*/cases/*}:close"
        body: "*"
      }
    };
  }

  // Retrieve valid classifications to be used when creating a support case.
  // The classications are hierarchical, with each classification containing
  // all levels of the hierarchy, separated by " > ". For example "Technical
  // Issue > Compute > Compute Engine".
  rpc SearchCaseClassifications(SearchCaseClassificationsRequest)
      returns (SearchCaseClassificationsResponse) {
    option (google.api.http) = {
      get: "/v2/caseClassifications:search"
    };
  }
}

// The request message for the GetCase endpoint.
message GetCaseRequest {
  // Required. The fully qualified name of a case to be retrieved.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudsupport.googleapis.com/Case"
    }
  ];
}

// The request message for the CreateCase endpoint.
message CreateCaseRequest {
  // Required. The name of the Google Cloud Resource under which the case should
  // be created.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudsupport.googleapis.com/Case"
    }
  ];

  // Required. The case to be created.
  Case case = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for the ListCases endpoint.
message ListCasesRequest {
  // Required. The fully qualified name of parent resource to list cases under.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudsupport.googleapis.com/Case"
    }
  ];

  // An expression written in filter language. If non-empty, the query returns
  // the cases that match the filter. Else, the query doesn't filter the cases.
  //
  // Filter expressions use the following fields with the operators equals (`=`)
  // and `AND`:
  //
  // - `state`: The accepted values are `OPEN` or `CLOSED`.
  // - `priority`: The accepted values are `P0`, `P1`, `P2`, `P3`, or `P4`. You
  // can specify multiple values for priority using the `OR` operator. For
  // example, `priority=P1 OR priority=P2`.
  // - `creator.email`: The email address of the case creator.
  //
  // Examples:
  //
  // - `state=CLOSED`
  // - `state=OPEN AND creator.email="<EMAIL>"`
  // - `state=OPEN AND (priority=P0 OR priority=P1)`
  string filter = 2;

  // The maximum number of cases fetched with each request. Defaults to 10.
  int32 page_size = 4;

  // A token identifying the page of results to return. If unspecified, the
  // first page is retrieved.
  string page_token = 5;
}

// The response message for the ListCases endpoint.
message ListCasesResponse {
  // The list of cases associated with the Google Cloud Resource, after any
  // filters have been applied.
  repeated Case cases = 1;

  // A token to retrieve the next page of results. This should be set in the
  // `page_token` field of the subsequent `ListCasesRequest` message that is
  // issued. If unspecified, there are no more results to retrieve.
  string next_page_token = 2;
}

// The request message for the SearchCases endpoint.
message SearchCasesRequest {
  // The fully qualified name of parent resource to search cases under.
  string parent = 4;

  // An expression written in filter language.
  //
  // A query uses the following fields with the operators equals (`=`) and
  // `AND`:
  //
  // - `organization`: An organization name in the form
  // `organizations/<organization_id>`.
  // - `project`: A project name in the form `projects/<project_id>`.
  // - `state`: The accepted values are `OPEN` or `CLOSED`.
  // - `priority`: The accepted values are `P0`, `P1`, `P2`, `P3`, or `P4`. You
  // can specify multiple values for priority using the `OR` operator. For
  // example, `priority=P1 OR priority=P2`.
  // - `creator.email`: The email address of the case creator.
  // - `billingAccount`: A billing account in the form
  // `billingAccounts/<billing_account_id>`
  //
  // You must specify either `organization` or `project`.
  //
  // To search across `displayName`, `description`, and comments, use a global
  // restriction with no keyword or operator. For example, `"my search"`.
  //
  // To search only cases updated after a certain date, use `update_time`
  // restricted with that particular date, time, and timezone in ISO datetime
  // format. For example, `update_time>"2020-01-01T00:00:00-05:00"`.
  // `update_time` only supports the greater than operator (`>`).
  //
  // Examples:
  //
  // - `organization="organizations/*********"`
  // - `project="projects/my-project-id"`
  // - `project="projects/*********"`
  // - `billing_account="billingAccounts/123456-A0B0C0-CUZ789"`
  // - `organization="organizations/*********" AND state=CLOSED`
  // - `project="projects/my-project-id" AND creator.email="<EMAIL>"`
  // - `project="projects/my-project-id" AND (priority=P0 OR priority=P1)`
  string query = 1;

  // The maximum number of cases fetched with each request. The default page
  // size is 10.
  int32 page_size = 2;

  // A token identifying the page of results to return. If unspecified, the
  // first page is retrieved.
  string page_token = 3;
}

// The response message for the SearchCases endpoint.
message SearchCasesResponse {
  // The list of cases associated with the Google Cloud Resource, after any
  // filters have been applied.
  repeated Case cases = 1;

  // A token to retrieve the next page of results. This should be set in the
  // `page_token` field of subsequent `SearchCaseRequest` message that is
  // issued. If unspecified, there are no more results to retrieve.
  string next_page_token = 2;
}

// The request message for the EscalateCase endpoint.
message EscalateCaseRequest {
  // Required. The fully qualified name of the Case resource to be escalated.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudsupport.googleapis.com/Case"
    }
  ];

  // The escalation object to be sent with the escalation request.
  Escalation escalation = 2;
}

// The request message for the UpdateCase endpoint
message UpdateCaseRequest {
  // Required. The case object to update.
  Case case = 1 [(google.api.field_behavior) = REQUIRED];

  // A list of attributes of the case object that should be updated
  // as part of this request. Supported values are `priority`, `display_name`,
  // and `subscriber_email_addresses`. If no fields are specified, all supported
  // fields are updated.
  //
  // WARNING: If you do not provide a field mask, then you might accidentally
  // clear some fields. For example, if you leave the field mask empty and do
  // not provide a value for `subscriber_email_addresses`, then
  // `subscriber_email_addresses` is updated to empty.
  google.protobuf.FieldMask update_mask = 2;
}

// The request message for the CloseCase endpoint.
message CloseCaseRequest {
  // Required. The fully qualified name of the case resource to be closed.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudsupport.googleapis.com/Case"
    }
  ];
}

// The request message for SearchCaseClassifications endpoint.
message SearchCaseClassificationsRequest {
  // An expression written in the Google Cloud filter language. If non-empty,
  // then only cases whose fields match the filter are returned. If empty, then
  // no messages are filtered out.
  string query = 1;

  // The maximum number of cases fetched with each request.
  int32 page_size = 2;

  // A token identifying the page of results to return. If unspecified, the
  // first page is retrieved.
  string page_token = 3;
}

// The response message for SearchCaseClassifications endpoint.
message SearchCaseClassificationsResponse {
  // The classifications retrieved.
  repeated CaseClassification case_classifications = 1;

  // A token to retrieve the next page of results. This should be set in the
  // `page_token` field of subsequent `SearchCaseClassificationsRequest` message
  // that is issued. If unspecified, there are no more results to retrieve.
  string next_page_token = 2;
}
