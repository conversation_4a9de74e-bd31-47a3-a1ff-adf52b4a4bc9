# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "support_proto",
    srcs = [
        "actor.proto",
        "attachment.proto",
        "attachment_service.proto",
        "case.proto",
        "case_service.proto",
        "comment.proto",
        "comment_service.proto",
        "escalation.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "support_proto_with_info",
    deps = [
        ":support_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "support_java_proto",
    deps = [":support_proto"],
)

java_grpc_library(
    name = "support_java_grpc",
    srcs = [":support_proto"],
    deps = [":support_java_proto"],
)

java_gapic_library(
    name = "support_java_gapic",
    srcs = [":support_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "support_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudsupport_v2.yaml",
    test_deps = [
        ":support_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":support_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "support_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.support.v2.CaseAttachmentServiceClientHttpJsonTest",
        "com.google.cloud.support.v2.CaseAttachmentServiceClientTest",
        "com.google.cloud.support.v2.CaseServiceClientHttpJsonTest",
        "com.google.cloud.support.v2.CaseServiceClientTest",
        "com.google.cloud.support.v2.CommentServiceClientHttpJsonTest",
        "com.google.cloud.support.v2.CommentServiceClientTest",
    ],
    runtime_deps = [":support_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-support-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":support_java_gapic",
        ":support_java_grpc",
        ":support_java_proto",
        ":support_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "support_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/support/apiv2/supportpb",
    protos = [":support_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "support_go_gapic",
    srcs = [":support_proto_with_info"],
    grpc_service_config = "support_v2_grpc_service_config.json",
    importpath = "cloud.google.com/go/support/apiv2;support",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudsupport_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":support_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-support-v2-go",
    deps = [
        ":support_go_gapic",
        ":support_go_gapic_srcjar-metadata.srcjar",
        ":support_go_gapic_srcjar-snippets.srcjar",
        ":support_go_gapic_srcjar-test.srcjar",
        ":support_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "support_py_gapic",
    srcs = [":support_proto"],
    grpc_service_config = "support_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudsupport_v2.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "support_py_gapic_test",
    srcs = [
        "support_py_gapic_pytest.py",
        "support_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":support_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "support-v2-py",
    deps = [
        ":support_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "support_php_proto",
    deps = [":support_proto"],
)

php_gapic_library(
    name = "support_php_gapic",
    srcs = [":support_proto_with_info"],
    grpc_service_config = "support_v2_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudsupport_v2.yaml",
    transport = "grpc+rest",
    deps = [":support_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-support-v2-php",
    deps = [
        ":support_php_gapic",
        ":support_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "support_nodejs_gapic",
    package_name = "@google-cloud/support",
    src = ":support_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "support_v2_grpc_service_config.json",
    package = "google.cloud.support.v2",
    rest_numeric_enums = True,
    service_yaml = "cloudsupport_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "support-v2-nodejs",
    deps = [
        ":support_nodejs_gapic",
        ":support_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "support_ruby_proto",
    deps = [":support_proto"],
)

ruby_grpc_library(
    name = "support_ruby_grpc",
    srcs = [":support_proto"],
    deps = [":support_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "support_ruby_gapic",
    srcs = [":support_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-support-v2"],
    grpc_service_config = "support_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudsupport_v2.yaml",
    deps = [
        ":support_ruby_grpc",
        ":support_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-support-v2-ruby",
    deps = [
        ":support_ruby_gapic",
        ":support_ruby_grpc",
        ":support_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "support_csharp_proto",
    deps = [":support_proto"],
)

csharp_grpc_library(
    name = "support_csharp_grpc",
    srcs = [":support_proto"],
    deps = [":support_csharp_proto"],
)

csharp_gapic_library(
    name = "support_csharp_gapic",
    srcs = [":support_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "support_v2_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudsupport_v2.yaml",
    deps = [
        ":support_csharp_grpc",
        ":support_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-support-v2-csharp",
    deps = [
        ":support_csharp_gapic",
        ":support_csharp_grpc",
        ":support_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "support_cc_proto",
    deps = [":support_proto"],
)

cc_grpc_library(
    name = "support_cc_grpc",
    srcs = [":support_proto"],
    grpc_only = True,
    deps = [":support_cc_proto"],
)
