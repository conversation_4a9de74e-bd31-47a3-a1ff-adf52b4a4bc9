// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.support.v2;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Support.V2";
option go_package = "cloud.google.com/go/support/apiv2/supportpb;supportpb";
option java_multiple_files = true;
option java_outer_classname = "EscalationProto";
option java_package = "com.google.cloud.support.v2";
option php_namespace = "Google\\Cloud\\Support\\V2";
option ruby_package = "Google::Cloud::Support::V2";

// An escalation of a support case.
message Escalation {
  // An enum detailing the possible reasons a case may be escalated.
  enum Reason {
    // The escalation reason is in an unknown state or has not been specified.
    REASON_UNSPECIFIED = 0;

    // The case is taking too long to resolve.
    RESOLUTION_TIME = 1;

    // The support agent does not have the expertise required to successfully
    // resolve the issue.
    TECHNICAL_EXPERTISE = 2;

    // The issue is having a significant business impact.
    BUSINESS_IMPACT = 3;
  }

  // Required. The reason why the Case is being escalated.
  Reason reason = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. A free text description to accompany the `reason` field above.
  // Provides additional context on why the case is being escalated.
  string justification = 5 [(google.api.field_behavior) = REQUIRED];
}
