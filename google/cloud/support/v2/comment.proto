// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.support.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/support/v2/actor.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Support.V2";
option go_package = "cloud.google.com/go/support/apiv2/supportpb;supportpb";
option java_multiple_files = true;
option java_outer_classname = "CommentProto";
option java_package = "com.google.cloud.support.v2";
option php_namespace = "Google\\Cloud\\Support\\V2";
option ruby_package = "Google::Cloud::Support::V2";

// A comment associated with a support case.
message Comment {
  option (google.api.resource) = {
    type: "cloudsupport.googleapis.com/Comment"
    pattern: "organizations/{organization}/cases/{case}/comments/{comment}"
    pattern: "projects/{project}/cases/{case}/comments/{comment}"
  };

  // Output only. The resource name for the comment.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when this comment was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user or Google Support agent created this comment.
  Actor creator = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The full comment body. Maximum of 12800 characters. This can contain rich
  // text syntax.
  string body = 4;

  // Output only. DEPRECATED. An automatically generated plain text version of
  // body with all rich text syntax stripped.
  string plain_text_body = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}
