{"methodConfig": [{"name": [{"service": "google.cloud.support.v2.CaseAttachmentService", "method": "ListAttachments"}, {"service": "google.cloud.support.v2.CaseService", "method": "GetCase"}, {"service": "google.cloud.support.v2.CaseService", "method": "ListCases"}, {"service": "google.cloud.support.v2.CaseService", "method": "SearchCases"}, {"service": "google.cloud.support.v2.CaseService", "method": "SearchCaseClassifications"}, {"service": "google.cloud.support.v2.CommentService", "method": "ListComments"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.support.v2.CaseService", "method": "CloseCase"}, {"service": "google.cloud.support.v2.CaseService", "method": "CreateCase"}, {"service": "google.cloud.support.v2.CaseService", "method": "UpdateCase"}, {"service": "google.cloud.support.v2.CaseService", "method": "EscalateCase"}, {"service": "google.cloud.support.v2.CommentService", "method": "CreateComment"}], "timeout": "60s"}]}