type: google.api.Service
config_version: 3
name: cloudsupport.googleapis.com
title: Google Cloud Support API

apis:
- name: google.cloud.support.v2.CaseAttachmentService
- name: google.cloud.support.v2.CaseService
- name: google.cloud.support.v2.CommentService

documentation:
  summary: |-
    Manages Google Cloud technical support cases for Customer Care support
    offerings.

authentication:
  rules:
  - selector: google.cloud.support.v2.CaseAttachmentService.ListAttachments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.support.v2.CaseService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.support.v2.CommentService.CreateComment
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.support.v2.CommentService.ListComments
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1051180
  documentation_uri: https://cloud.google.com/support/docs/reference/support-api
  api_short_name: cloudsupport
  github_label: 'api: cloudsupport'
  doc_tag_prefix: cloudsupport
  organization: CLOUD
  library_settings:
  - version: google.cloud.support.v2
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
