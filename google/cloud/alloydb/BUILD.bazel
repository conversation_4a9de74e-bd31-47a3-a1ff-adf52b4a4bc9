# This build file includes a target for the Ruby wrapper library for
# google-cloud-alloy_db.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for alloydb.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "alloydb_ruby_wrapper",
    srcs = ["//google/cloud/alloydb/v1:alloydb_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-alloy_db",
        "ruby-cloud-gem-namespace=Google::Cloud::AlloyDB",
        "ruby-cloud-wrapper-of=v1:0.8;v1beta:0.6",
    ],
    service_yaml = "//google/cloud/alloydb/v1:alloydb_v1.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-alloydb-ruby",
    deps = [
        ":alloydb_ruby_wrapper",
    ],
)
