// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.alloydb.v1beta;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/alloydb/v1beta/csql_resources.proto";
import "google/cloud/alloydb/v1beta/gemini.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/type/dayofweek.proto";
import "google/type/timeofday.proto";

option csharp_namespace = "Google.Cloud.AlloyDb.V1Beta";
option go_package = "cloud.google.com/go/alloydb/apiv1beta/alloydbpb;alloydbpb";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.cloud.alloydb.v1beta";
option php_namespace = "Google\\Cloud\\AlloyDb\\V1beta";
option ruby_package = "Google::Cloud::AlloyDB::V1beta";
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKeyVersion"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}/cryptoKeyVersions/{crypto_key_version}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Network"
  pattern: "projects/{project}/global/networks/{network}"
};

// View on Instance. Pass this enum to rpcs that returns an Instance message to
// control which subsets of fields to get.
enum InstanceView {
  // INSTANCE_VIEW_UNSPECIFIED Not specified, equivalent to BASIC.
  INSTANCE_VIEW_UNSPECIFIED = 0;

  // BASIC server responses for a primary or read instance include all the
  // relevant instance details, excluding the details of each node in the
  // instance. The default value.
  INSTANCE_VIEW_BASIC = 1;

  // FULL response is equivalent to BASIC for primary instance (for now).
  // For read pool instance, this includes details of each node in the pool.
  INSTANCE_VIEW_FULL = 2;
}

// View on Cluster. Pass this enum to rpcs that returns a cluster message to
// control which subsets of fields to get.
enum ClusterView {
  // CLUSTER_VIEW_UNSPECIFIED Not specified, equivalent to BASIC.
  CLUSTER_VIEW_UNSPECIFIED = 0;

  // BASIC server responses include all the relevant cluster details, excluding
  // Cluster.ContinuousBackupInfo.EarliestRestorableTime and other view-specific
  // fields. The default value.
  CLUSTER_VIEW_BASIC = 1;

  // CONTINUOUS_BACKUP response returns all the fields from BASIC plus
  // the earliest restorable time if continuous backups are enabled.
  // May increase latency.
  CLUSTER_VIEW_CONTINUOUS_BACKUP = 2;
}

// The supported database engine versions.
enum DatabaseVersion {
  // This is an unknown database version.
  DATABASE_VERSION_UNSPECIFIED = 0;

  // DEPRECATED - The database version is Postgres 13.
  POSTGRES_13 = 1 [deprecated = true];

  // The database version is Postgres 14.
  POSTGRES_14 = 2;

  // The database version is Postgres 15.
  POSTGRES_15 = 3;

  // The database version is Postgres 16.
  POSTGRES_16 = 4;
}

// Subscription_type added to distinguish between Standard and Trial
// subscriptions. By default, a subscription type is considered STANDARD unless
// explicitly specified.
enum SubscriptionType {
  // This is an unknown subscription type. By default, the subscription type is
  // STANDARD.
  SUBSCRIPTION_TYPE_UNSPECIFIED = 0;

  // Standard subscription.
  STANDARD = 1;

  // Trial subscription.
  TRIAL = 2;
}

// The username/password for a database user. Used for specifying initial
// users at cluster creation time.
message UserPassword {
  // The database username.
  string user = 1;

  // The initial password for the user.
  string password = 2;
}

// Subset of the source instance configuration that is available when reading
// the cluster resource.
message MigrationSource {
  // Denote the type of migration source that created this cluster.
  enum MigrationSourceType {
    // Migration source is unknown.
    MIGRATION_SOURCE_TYPE_UNSPECIFIED = 0;

    // DMS source means the cluster was created via DMS migration job.
    DMS = 1;
  }

  // Output only. The host and port of the on-premises instance in host:port
  // format
  string host_port = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Place holder for the external source identifier(e.g DMS job
  // name) that created the cluster.
  string reference_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of migration source.
  MigrationSourceType source_type = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// EncryptionConfig describes the encryption config of a cluster or a backup
// that is encrypted with a CMEK (customer-managed encryption key).
message EncryptionConfig {
  // The fully-qualified resource name of the KMS key.
  // Each Cloud KMS key is regionalized and has the following format:
  // projects/[PROJECT]/locations/[REGION]/keyRings/[RING]/cryptoKeys/[KEY_NAME]
  string kms_key_name = 1;
}

// EncryptionInfo describes the encryption information of a cluster or a backup.
message EncryptionInfo {
  // Possible encryption types.
  enum Type {
    // Encryption type not specified. Defaults to GOOGLE_DEFAULT_ENCRYPTION.
    TYPE_UNSPECIFIED = 0;

    // The data is encrypted at rest with a key that is fully managed by Google.
    // No key version will be populated. This is the default state.
    GOOGLE_DEFAULT_ENCRYPTION = 1;

    // The data is encrypted at rest with a key that is managed by the customer.
    // KMS key versions will be populated.
    CUSTOMER_MANAGED_ENCRYPTION = 2;
  }

  // Output only. Type of encryption.
  Type encryption_type = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cloud KMS key versions that are being used to protect the
  // database or the backup.
  repeated string kms_key_versions = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];
}

// SSL configuration.
message SslConfig {
  // SSL mode options.
  enum SslMode {
    // SSL mode is not specified. Defaults to ENCRYPTED_ONLY.
    SSL_MODE_UNSPECIFIED = 0;

    // SSL connections are optional. CA verification not enforced.
    SSL_MODE_ALLOW = 1 [deprecated = true];

    // SSL connections are required. CA verification not enforced.
    // Clients may use locally self-signed certificates (default psql client
    // behavior).
    SSL_MODE_REQUIRE = 2 [deprecated = true];

    // SSL connections are required. CA verification enforced.
    // Clients must have certificates signed by a Cluster CA, for example, using
    // GenerateClientCertificate.
    SSL_MODE_VERIFY_CA = 3 [deprecated = true];

    // SSL connections are optional. CA verification not enforced.
    ALLOW_UNENCRYPTED_AND_ENCRYPTED = 4;

    // SSL connections are required. CA verification not enforced.
    ENCRYPTED_ONLY = 5;
  }

  // Certificate Authority (CA) source for SSL/TLS certificates.
  enum CaSource {
    // Certificate Authority (CA) source not specified. Defaults to
    // CA_SOURCE_MANAGED.
    CA_SOURCE_UNSPECIFIED = 0;

    // Certificate Authority (CA) managed by the AlloyDB Cluster.
    CA_SOURCE_MANAGED = 1;
  }

  // Optional. SSL mode. Specifies client-server SSL/TLS connection behavior.
  SslMode ssl_mode = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Certificate Authority (CA) source. Only CA_SOURCE_MANAGED is
  // supported currently, and is the default value.
  CaSource ca_source = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message describing the user-specified automated backup policy.
//
// All fields in the automated backup policy are optional. Defaults for each
// field are provided if they are not set.
message AutomatedBackupPolicy {
  // A weekly schedule starts a backup at prescribed start times within a
  // day, for the specified days of the week.
  //
  // The weekly schedule message is flexible and can be used to create many
  // types of schedules. For example, to have a daily backup that starts at
  // 22:00, configure the `start_times` field to have one element "22:00" and
  // the `days_of_week` field to have all seven days of the week.
  message WeeklySchedule {
    // The times during the day to start a backup. The start times are assumed
    // to be in UTC and to be an exact hour (e.g., 04:00:00).
    //
    // If no start times are provided, a single fixed start time is chosen
    // arbitrarily.
    repeated google.type.TimeOfDay start_times = 1;

    // The days of the week to perform a backup.
    //
    // If this field is left empty, the default of every day of the week is
    // used.
    repeated google.type.DayOfWeek days_of_week = 2;
  }

  // A time based retention policy specifies that all backups within a certain
  // time period should be retained.
  message TimeBasedRetention {
    // The retention period.
    google.protobuf.Duration retention_period = 1;
  }

  // A quantity based policy specifies that a certain number of the most recent
  // successful backups should be retained.
  message QuantityBasedRetention {
    // The number of backups to retain.
    int32 count = 1;
  }

  // The schedule for this automated backup policy.
  //
  // A schedule specifies times at which to start a backup. If a backup
  // window is also provided, the backup is guaranteed to be started and
  // completed within the start time plus the backup window. If the backup is
  // not completed within the backup window it is marked as failed.
  //
  // If not set, the schedule defaults to a weekly schedule with one backup
  // per day and a start time chosen arbitrarily.
  oneof schedule {
    // Weekly schedule for the Backup.
    WeeklySchedule weekly_schedule = 2;
  }

  // The retention policy for automated backups.
  //
  // The retention policy for a backup is fixed at the time the backup is
  // created. Changes to this field only apply to new backups taken with the
  // policy; the retentions of existing backups remain unchanged.
  //
  // If no retention policy is set, a default of 14 days is used.
  oneof retention {
    // Time-based Backup retention policy.
    TimeBasedRetention time_based_retention = 4;

    // Quantity-based Backup retention policy to retain recent backups.
    QuantityBasedRetention quantity_based_retention = 5;
  }

  // Whether automated automated backups are enabled. If not set, defaults to
  // true.
  optional bool enabled = 1;

  // The length of the time window during which a backup can be
  // taken. If a backup does not succeed within this time window, it will be
  // canceled and considered failed.
  //
  // The backup window must be at least 5 minutes long. There is no upper bound
  // on the window. If not set, it defaults to 1 hour.
  google.protobuf.Duration backup_window = 3;

  // Optional. The encryption config can be specified to encrypt the
  // backups with a customer-managed encryption key (CMEK). When this field is
  // not specified, the backup will then use default encryption scheme to
  // protect the user data.
  EncryptionConfig encryption_config = 8
      [(google.api.field_behavior) = OPTIONAL];

  // The location where the backup will be stored. Currently, the only supported
  // option is to store the backup in the same region as the cluster.
  //
  // If empty, defaults to the region of the cluster.
  string location = 6;

  // Labels to apply to backups created using this configuration.
  map<string, string> labels = 7;
}

// ContinuousBackupConfig describes the continuous backups recovery
// configurations of a cluster.
message ContinuousBackupConfig {
  // Whether ContinuousBackup is enabled.
  optional bool enabled = 1;

  // The number of days that are eligible to restore from using PITR. To support
  // the entire recovery window, backups and logs are retained for one day more
  // than the recovery window. If not set, defaults to 14 days.
  int32 recovery_window_days = 4;

  // The encryption config can be specified to encrypt the
  // backups with a customer-managed encryption key (CMEK). When this field is
  // not specified, the backup will then use default encryption scheme to
  // protect the user data.
  EncryptionConfig encryption_config = 3;
}

// ContinuousBackupInfo describes the continuous backup properties of a
// cluster.
message ContinuousBackupInfo {
  // Output only. The encryption information for the WALs and backups required
  // for ContinuousBackup.
  EncryptionInfo encryption_info = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. When ContinuousBackup was most recently enabled. Set to null
  // if ContinuousBackup is not enabled.
  google.protobuf.Timestamp enabled_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Days of the week on which a continuous backup is taken. Output
  // only field. Ignored if passed into the request.
  repeated google.type.DayOfWeek schedule = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The earliest restorable time that can be restored to. Output
  // only field.
  google.protobuf.Timestamp earliest_restorable_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message describing a BackupSource.
message BackupSource {
  // Output only. The system-generated UID of the backup which was used to
  // create this resource. The UID is generated when the backup is created, and
  // it is retained until the backup is deleted.
  string backup_uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The name of the backup resource with the format:
  //  * projects/{project}/locations/{region}/backups/{backup_id}
  string backup_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/Backup" }
  ];
}

// Message describing a ContinuousBackupSource.
message ContinuousBackupSource {
  // Required. The source cluster from which to restore. This cluster must have
  // continuous backup enabled for this operation to succeed. For the required
  // format, see the comment on the Cluster.name field.
  string cluster = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The point in time to restore to.
  google.protobuf.Timestamp point_in_time = 2
      [(google.api.field_behavior) = REQUIRED];
}

// MaintenanceUpdatePolicy defines the policy for system updates.
message MaintenanceUpdatePolicy {
  // MaintenanceWindow specifies a preferred day and time for maintenance.
  message MaintenanceWindow {
    // Preferred day of the week for maintenance, e.g. MONDAY, TUESDAY, etc.
    google.type.DayOfWeek day = 1;

    // Preferred time to start the maintenance operation on the specified day.
    // Maintenance will start within 1 hour of this time.
    google.type.TimeOfDay start_time = 2;
  }

  // Preferred windows to perform maintenance. Currently limited to 1.
  repeated MaintenanceWindow maintenance_windows = 1;
}

// MaintenanceSchedule stores the maintenance schedule generated from
// the MaintenanceUpdatePolicy, once a maintenance rollout is triggered, if
// MaintenanceWindow is set, and if there is no conflicting DenyPeriod.
// The schedule is cleared once the update takes place. This field cannot be
// manually changed; modify the MaintenanceUpdatePolicy instead.
message MaintenanceSchedule {
  // Output only. The scheduled start time for the maintenance.
  google.protobuf.Timestamp start_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A cluster is a collection of regional AlloyDB resources. It can include a
// primary instance and one or more read pool instances.
// All cluster resources share a storage layer, which scales as needed.
message Cluster {
  option (google.api.resource) = {
    type: "alloydb.googleapis.com/Cluster"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}"
    plural: "clusters"
    singular: "cluster"
    style: DECLARATIVE_FRIENDLY
  };

  // Metadata related to network configuration.
  message NetworkConfig {
    // Optional. The resource link for the VPC network in which cluster
    // resources are created and from which they are accessible via Private IP.
    // The network must belong to the same project as the cluster. It is
    // specified in the form:
    // `projects/{project_number}/global/networks/{network_id}`. This is
    // required to create a cluster.
    string network = 1 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "compute.googleapis.com/Network"
      }
    ];

    // Optional. Name of the allocated IP range for the private IP AlloyDB
    // cluster, for example: "google-managed-services-default". If set, the
    // instance IPs for this cluster will be created in the allocated range. The
    // range name must comply with RFC 1035. Specifically, the name must be 1-63
    // characters long and match the regular expression
    // `[a-z]([-a-z0-9]*[a-z0-9])?`.
    // Field name is intended to be consistent with Cloud SQL.
    string allocated_ip_range = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Configuration information for the secondary cluster. This should be set
  // if and only if the cluster is of type SECONDARY.
  message SecondaryConfig {
    // The name of the primary cluster name with the format:
    // * projects/{project}/locations/{region}/clusters/{cluster_id}
    string primary_cluster_name = 1;
  }

  // Configuration for the primary cluster. It has the list of clusters that are
  // replicating from this cluster. This should be set if and only if the
  // cluster is of type PRIMARY.
  message PrimaryConfig {
    // Output only. Names of the clusters that are replicating from this
    // cluster.
    repeated string secondary_cluster_names = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // PscConfig contains PSC related configuration at a cluster level.
  message PscConfig {
    // Optional. Create an instance that allows connections from Private Service
    // Connect endpoints to the instance.
    bool psc_enabled = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Contains information and all metadata related to TRIAL clusters.
  message TrialMetadata {
    // start time of the trial cluster.
    google.protobuf.Timestamp start_time = 1;

    // End time of the trial cluster.
    google.protobuf.Timestamp end_time = 2;

    // Upgrade time of trial cluster to Standard cluster.
    google.protobuf.Timestamp upgrade_time = 3;

    // grace end time of the cluster.
    google.protobuf.Timestamp grace_end_time = 4;
  }

  // Cluster State
  enum State {
    // The state of the cluster is unknown.
    STATE_UNSPECIFIED = 0;

    // The cluster is active and running.
    READY = 1;

    // The cluster is stopped. All instances in the cluster are stopped.
    // Customers can start a stopped cluster at any point and all their
    // instances will come back to life with same names and IP resources. In
    // this state, customer pays for storage.
    // Associated backups could also be present in a stopped cluster.
    STOPPED = 2;

    // The cluster is empty and has no associated resources.
    // All instances, associated storage and backups have been deleted.
    EMPTY = 3;

    // The cluster is being created.
    CREATING = 4;

    // The cluster is being deleted.
    DELETING = 5;

    // The creation of the cluster failed.
    FAILED = 6;

    // The cluster is bootstrapping with data from some other source.
    // Direct mutations to the cluster (e.g. adding read pool) are not allowed.
    BOOTSTRAPPING = 7;

    // The cluster is under maintenance. AlloyDB regularly performs maintenance
    // and upgrades on customer clusters. Updates on the cluster are
    // not allowed while the cluster is in this state.
    MAINTENANCE = 8;

    // The cluster is being promoted.
    PROMOTING = 9;
  }

  // Type of Cluster
  enum ClusterType {
    // The type of the cluster is unknown.
    CLUSTER_TYPE_UNSPECIFIED = 0;

    // Primary cluster that support read and write operations.
    PRIMARY = 1;

    // Secondary cluster that is replicating from another region.
    // This only supports read.
    SECONDARY = 2;
  }

  // In case of an imported cluster, this field contains information about the
  // source this cluster was imported from.
  oneof source {
    // Output only. Cluster created from backup.
    BackupSource backup_source = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Cluster created via DMS migration.
    MigrationSource migration_source = 16
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Cluster created from CloudSQL snapshot.
    CloudSQLBackupRunSource cloudsql_backup_run_source = 42
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The name of the cluster resource with the format:
  //  * projects/{project}/locations/{region}/clusters/{cluster_id}
  // where the cluster ID segment should satisfy the regex expression
  // `[a-z0-9-]+`. For more details see https://google.aip.dev/122.
  // The prefix of the cluster resource name is the name of the parent resource:
  //  * projects/{project}/locations/{region}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-settable and human-readable display name for the Cluster.
  string display_name = 2;

  // Output only. The system-generated UID of the resource. The UID is assigned
  // when the resource is created, and it is retained until it is deleted.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create time stamp
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update time stamp
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Delete time stamp
  google.protobuf.Timestamp delete_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs
  map<string, string> labels = 7;

  // Output only. The current serving state of the cluster.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the cluster. This is an output-only field and it's
  // populated at the Cluster creation time or the Cluster promotion
  // time. The cluster type is determined by which RPC was used to create
  // the cluster (i.e. `CreateCluster` vs. `CreateSecondaryCluster`
  ClusterType cluster_type = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The database engine major version. This is an optional field and
  // it is populated at the Cluster creation time. If a database version is not
  // supplied at cluster creation time, then a default database version will
  // be used.
  DatabaseVersion database_version = 9 [(google.api.field_behavior) = OPTIONAL];

  NetworkConfig network_config = 29 [(google.api.field_behavior) = OPTIONAL];

  // Required. The resource link for the VPC network in which cluster resources
  // are created and from which they are accessible via Private IP. The network
  // must belong to the same project as the cluster. It is specified in the
  // form: `projects/{project}/global/networks/{network_id}`. This is required
  // to create a cluster. Deprecated, use network_config.network instead.
  string network = 10 [
    deprecated = true,
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // For Resource freshness validation (https://google.aip.dev/154)
  string etag = 11;

  // Annotations to allow client tools to store small amount of arbitrary data.
  // This is distinct from labels.
  // https://google.aip.dev/128
  map<string, string> annotations = 12;

  // Output only. Reconciling (https://google.aip.dev/128#reconciliation).
  // Set to true if the current state of Cluster does not match the user's
  // intended state, and the service is actively updating the resource to
  // reconcile them. This can happen due to user-triggered updates or
  // system actions like failover or maintenance.
  bool reconciling = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. Initial user to setup during cluster creation. Required.
  // If used in `RestoreCluster` this is ignored.
  UserPassword initial_user = 14 [(google.api.field_behavior) = INPUT_ONLY];

  // The automated backup policy for this cluster.
  //
  // If no policy is provided then the default policy will be used. If backups
  // are supported for the cluster, the default policy takes one backup a day,
  // has a backup window of 1 hour, and retains backups for 14 days.
  // For more information on the defaults, consult the
  // documentation for the message type.
  AutomatedBackupPolicy automated_backup_policy = 17;

  // SSL configuration for this AlloyDB cluster.
  SslConfig ssl_config = 18 [deprecated = true];

  // Optional. The encryption config can be specified to encrypt the data disks
  // and other persistent data resources of a cluster with a
  // customer-managed encryption key (CMEK). When this field is not
  // specified, the cluster will then use default encryption scheme to
  // protect the user data.
  EncryptionConfig encryption_config = 19
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The encryption information for the cluster.
  EncryptionInfo encryption_info = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Continuous backup configuration for this cluster.
  ContinuousBackupConfig continuous_backup_config = 27
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Continuous backup properties for this cluster.
  ContinuousBackupInfo continuous_backup_info = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Cross Region replication config specific to SECONDARY cluster.
  SecondaryConfig secondary_config = 22;

  // Output only. Cross Region replication config specific to PRIMARY cluster.
  PrimaryConfig primary_config = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 30 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The configuration for Private Service Connect (PSC) for the
  // cluster.
  PscConfig psc_config = 31 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maintenance update policy determines when to allow or deny
  // updates.
  MaintenanceUpdatePolicy maintenance_update_policy = 32
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The maintenance schedule for the cluster, generated for a
  // specific rollout if a maintenance window is set.
  MaintenanceSchedule maintenance_schedule = 37
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Configuration parameters related to the Gemini in Databases
  // add-on.
  GeminiClusterConfig gemini_config = 36
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Subscription type of the cluster.
  SubscriptionType subscription_type = 38
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Metadata for free trial clusters
  TrialMetadata trial_metadata = 39 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Input only. Immutable. Tag keys/values directly bound to this
  // resource. For example:
  // ```
  // "123/environment": "production",
  // "123/costCenter": "marketing"
  // ```
  map<string, string> tags = 41 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// An Instance is a computing unit that an end customer can connect to.
// It's the main unit of computing resources in AlloyDB.
message Instance {
  option (google.api.resource) = {
    type: "alloydb.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/instances/{instance}"
    plural: "instances"
    singular: "instance"
    style: DECLARATIVE_FRIENDLY
  };

  // MachineConfig describes the configuration of a machine.
  message MachineConfig {
    // The number of CPU's in the VM instance.
    int32 cpu_count = 1;
  }

  // Details of a single node in the instance.
  // Nodes in an AlloyDB instance are ephemereal, they can change during
  // update, failover, autohealing and resize operations.
  message Node {
    // The Compute Engine zone of the VM e.g. "us-central1-b".
    string zone_id = 1;

    // The identifier of the VM e.g. "test-read-0601-407e52be-ms3l".
    string id = 2;

    // The private IP address of the VM e.g. "**********".
    string ip = 3;

    // Determined by state of the compute VM and postgres-service health.
    // Compute VM state can have values listed in
    // https://cloud.google.com/compute/docs/instances/instance-life-cycle and
    // postgres-service health can have values: HEALTHY and UNHEALTHY.
    string state = 4;
  }

  // QueryInsights Instance specific configuration.
  message QueryInsightsInstanceConfig {
    // Record application tags for an instance.
    // This flag is turned "on" by default.
    optional bool record_application_tags = 2;

    // Record client address for an instance. Client address is PII information.
    // This flag is turned "on" by default.
    optional bool record_client_address = 3;

    // Query string length. The default value is 1024.
    // Any integer between 256 and 4500 is considered valid.
    uint32 query_string_length = 4;

    // Number of query execution plans captured by Insights per minute
    // for all queries combined. The default value is 5.
    // Any integer between 0 and 20 is considered valid.
    optional uint32 query_plans_per_minute = 5;
  }

  // Observability Instance specific configuration.
  message ObservabilityInstanceConfig {
    // Observability feature status for an instance.
    // This flag is turned "off" by default.
    optional bool enabled = 1;

    // Preserve comments in query string for an instance.
    // This flag is turned "off" by default.
    optional bool preserve_comments = 2;

    // Track wait events during query execution for an instance.
    // This flag is turned "on" by default but tracking is enabled only after
    // observability enabled flag is also turned on.
    optional bool track_wait_events = 3;

    // Output only. Track wait event types during query execution for an
    // instance. This flag is turned "on" by default but tracking is enabled
    // only after observability enabled flag is also turned on. This is
    // read-only flag and only modifiable by producer API.
    optional bool track_wait_event_types = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Query string length. The default value is 10k.
    optional int32 max_query_string_length = 5;

    // Record application tags for an instance.
    // This flag is turned "off" by default.
    optional bool record_application_tags = 6;

    // Number of query execution plans captured by Insights per minute
    // for all queries combined. The default value is 200.
    // Any integer between 0 to 200 is considered valid.
    optional int32 query_plans_per_minute = 7;

    // Track actively running queries on the instance.
    // If not set, this flag is "off" by default.
    optional bool track_active_queries = 8;

    // Track client address for an instance.
    // If not set, default value is "off".
    optional bool track_client_address = 9;
  }

  // Configuration for a read pool instance.
  message ReadPoolConfig {
    // Read capacity, i.e. number of nodes in a read pool instance.
    int32 node_count = 1;
  }

  // Policy to be used while updating the instance.
  message UpdatePolicy {
    // Specifies the available modes of update.
    enum Mode {
      // Mode is unknown.
      MODE_UNSPECIFIED = 0;

      // Least disruptive way to apply the update.
      DEFAULT = 1;

      // Performs a forced update when applicable. This will be fast but may
      // incur a downtime.
      FORCE_APPLY = 2;
    }

    // Mode for updating the instance.
    Mode mode = 1;
  }

  // Client connection configuration
  message ClientConnectionConfig {
    // Optional. Configuration to enforce connectors only (ex: AuthProxy)
    // connections to the database.
    bool require_connectors = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. SSL configuration option for this instance.
    SslConfig ssl_config = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // PscInstanceConfig contains PSC related configuration at an
  // instance level.
  message PscInstanceConfig {
    // Output only. The service attachment created when Private
    // Service Connect (PSC) is enabled for the instance.
    // The name of the resource will be in the format of
    // `projects/<alloydb-tenant-project-number>/regions/<region-name>/serviceAttachments/<service-attachment-name>`
    string service_attachment_link = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Optional. List of consumer projects that are allowed to create
    // PSC endpoints to service-attachments to this instance.
    repeated string allowed_consumer_projects = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Output only. The DNS name of the instance for PSC connectivity.
    // Name convention: <uid>.<uid>.<region>.alloydb-psc.goog
    string psc_dns_name = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Metadata related to instance-level network configuration.
  message InstanceNetworkConfig {
    // AuthorizedNetwork contains metadata for an authorized network.
    message AuthorizedNetwork {
      // CIDR range for one authorzied network of the instance.
      string cidr_range = 1 [(google.api.field_info).format = IPV4];
    }

    // Optional. A list of external network authorized to access this instance.
    repeated AuthorizedNetwork authorized_external_networks = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Enabling public ip for the instance.
    bool enable_public_ip = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Enabling an outbound public IP address to support a database
    // server sending requests out into the internet.
    bool enable_outbound_public_ip = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Instance State
  enum State {
    // The state of the instance is unknown.
    STATE_UNSPECIFIED = 0;

    // The instance is active and running.
    READY = 1;

    // The instance is stopped. Instance name and IP resources are preserved.
    STOPPED = 2;

    // The instance is being created.
    CREATING = 3;

    // The instance is being deleted.
    DELETING = 4;

    // The instance is down for maintenance.
    MAINTENANCE = 5;

    // The creation of the instance failed or a fatal error occurred during
    // an operation on the instance.
    // Note: Instances in this state would tried to be auto-repaired. And
    // Customers should be able to restart, update or delete these instances.
    FAILED = 6;

    // Index 7 is used in the producer apis for ROLLED_BACK state. Keeping that
    // index unused in case that state also needs to exposed via consumer apis
    // in future.
    // The instance has been configured to sync data from some other source.
    BOOTSTRAPPING = 8;

    // The instance is being promoted.
    PROMOTING = 9;
  }

  // Type of an Instance
  enum InstanceType {
    // The type of the instance is unknown.
    INSTANCE_TYPE_UNSPECIFIED = 0;

    // PRIMARY instances support read and write operations.
    PRIMARY = 1;

    // READ POOL instances support read operations only. Each read pool instance
    // consists of one or more homogeneous nodes.
    //  * Read pool of size 1 can only have zonal availability.
    //  * Read pools with node count of 2 or more can have regional
    //    availability (nodes are present in 2 or more zones in a region).
    READ_POOL = 2;

    // SECONDARY instances support read operations only. SECONDARY instance
    // is a cross-region read replica
    SECONDARY = 3;
  }

  // The Availability type of an instance. Potential values:
  //
  // - ZONAL: The instance serves data from only one zone. Outages in that
  //     zone affect instance availability.
  // - REGIONAL: The instance can serve data from more than one zone in a
  //     region (it is highly available).
  enum AvailabilityType {
    // This is an unknown Availability type.
    AVAILABILITY_TYPE_UNSPECIFIED = 0;

    // Zonal available instance.
    ZONAL = 1;

    // Regional (or Highly) available instance.
    REGIONAL = 2;
  }

  // Output only. The name of the instance resource with the format:
  //  * projects/{project}/locations/{region}/clusters/{cluster_id}/instances/{instance_id}
  // where the cluster and instance ID segments should satisfy the regex
  // expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters of
  // lowercase letters, numbers, and dashes, starting with a letter, and ending
  // with a letter or number. For more details see https://google.aip.dev/122.
  // The prefix of the instance resource name is the name of the parent
  // resource:
  //  * projects/{project}/locations/{region}/clusters/{cluster_id}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-settable and human-readable display name for the Instance.
  string display_name = 2;

  // Output only. The system-generated UID of the resource. The UID is assigned
  // when the resource is created, and it is retained until it is deleted.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create time stamp
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update time stamp
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Delete time stamp
  google.protobuf.Timestamp delete_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs
  map<string, string> labels = 7;

  // Output only. The current serving state of the instance.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The type of the instance. Specified at creation time.
  InstanceType instance_type = 9 [(google.api.field_behavior) = REQUIRED];

  // Configurations for the machines that host the underlying
  // database engine.
  MachineConfig machine_config = 10;

  // Availability type of an Instance.
  // If empty, defaults to REGIONAL for primary instances.
  // For read pools, availability_type is always UNSPECIFIED. Instances in the
  // read pools are evenly distributed across available zones within the region
  // (i.e. read pools with more than one node will have a node in at
  // least two zones).
  AvailabilityType availability_type = 11;

  // The Compute Engine zone that the instance should serve from, per
  // https://cloud.google.com/compute/docs/regions-zones
  // This can ONLY be specified for ZONAL instances.
  // If present for a REGIONAL instance, an error will be thrown.
  // If this is absent for a ZONAL instance, instance is created in a random
  // zone with available capacity.
  string gce_zone = 12;

  // Database flags. Set at instance level.
  //  * They are copied from primary instance on read instance creation.
  //  * Read instances can set new or override existing flags that are relevant
  //    for reads, e.g. for enabling columnar cache on a read instance. Flags
  //    set on read instance may or may not be present on primary.
  //
  //
  // This is a list of "key": "value" pairs.
  // "key": The name of the flag. These flags are passed at instance setup time,
  // so include both server options and system variables for Postgres. Flags are
  // specified with underscores, not hyphens.
  // "value": The value of the flag. Booleans are set to **on** for true
  // and **off** for false. This field must be omitted if the flag
  // doesn't take a value.
  map<string, string> database_flags = 13;

  // Output only. This is set for the read-write VM of the PRIMARY instance
  // only.
  Node writable_node = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of available read-only VMs in this instance, including
  // the standby for a PRIMARY instance.
  repeated Node nodes = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Configuration for query insights.
  QueryInsightsInstanceConfig query_insights_config = 21;

  // Configuration for observability.
  ObservabilityInstanceConfig observability_config = 26;

  // Read pool instance configuration.
  // This is required if the value of instanceType is READ_POOL.
  ReadPoolConfig read_pool_config = 14;

  // Output only. The IP address for the Instance.
  // This is the connection endpoint for an end-user application.
  string ip_address = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The public IP addresses for the Instance. This is available
  // ONLY when enable_public_ip is set. This is the connection endpoint for an
  // end-user application.
  string public_ip_address = 27 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. Reconciling (https://google.aip.dev/128#reconciliation).
  // Set to true if the current state of Instance does not match the user's
  // intended state, and the service is actively updating the resource to
  // reconcile them. This can happen due to user-triggered updates or
  // system actions like failover or maintenance.
  bool reconciling = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // For Resource freshness validation (https://google.aip.dev/154)
  string etag = 17;

  // Annotations to allow client tools to store small amount of arbitrary data.
  // This is distinct from labels.
  // https://google.aip.dev/128
  map<string, string> annotations = 18;

  // Update policy that will be applied during instance update.
  // This field is not persisted when you update the instance.
  // To use a non-default update policy, you must
  // specify explicitly specify the value in each update request.
  UpdatePolicy update_policy = 22;

  // Optional. Client connection specific configurations
  ClientConnectionConfig client_connection_config = 23
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 24 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The configuration for Private Service Connect (PSC) for the
  // instance.
  PscInstanceConfig psc_instance_config = 28
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Instance-level network configuration.
  InstanceNetworkConfig network_config = 29
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration parameters related to the Gemini in Databases
  // add-on.
  GeminiInstanceConfig gemini_config = 33
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. All outbound public IP addresses configured for the instance.
  repeated string outbound_public_ip_addresses = 34 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];
}

// ConnectionInfo singleton resource.
// https://google.aip.dev/156
message ConnectionInfo {
  option (google.api.resource) = {
    type: "alloydb.googleapis.com/ConnectionInfo"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/instances/{instance}/connectionInfo"
    plural: "connectionInfos"
    singular: "connectionInfo"
  };

  // The name of the ConnectionInfo singleton resource, e.g.:
  // projects/{project}/locations/{location}/clusters/*/instances/*/connectionInfo
  // This field currently has no semantic meaning.
  string name = 1;

  // Output only. The private network IP address for the Instance. This is the
  // default IP for the instance and is always created (even if enable_public_ip
  // is set). This is the connection endpoint for an end-user application.
  string ip_address = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The public IP addresses for the Instance. This is available
  // ONLY when enable_public_ip is set. This is the connection endpoint for an
  // end-user application.
  string public_ip_address = 5 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. The pem-encoded chain that may be used to verify the X.509
  // certificate. Expected to be in issuer-to-root order according to RFC 5246.
  repeated string pem_certificate_chain = 3
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The unique ID of the Instance.
  string instance_uid = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The DNS name to use with PSC for the Instance.
  string psc_dns_name = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message describing Backup object
message Backup {
  option (google.api.resource) = {
    type: "alloydb.googleapis.com/Backup"
    pattern: "projects/{project}/locations/{location}/backups/{backup}"
    plural: "backups"
    singular: "backup"
    style: DECLARATIVE_FRIENDLY
  };

  // A backup's position in a quantity-based retention queue, of backups with
  // the same source cluster and type, with length, retention, specified by the
  // backup's retention policy.
  // Once the position is greater than the retention, the backup is eligible to
  // be garbage collected.
  //
  // Example: 5 backups from the same source cluster and type with a
  // quantity-based retention of 3 and denoted by backup_id (position,
  // retention).
  //
  // Safe: backup_5 (1, 3), backup_4, (2, 3), backup_3 (3, 3).
  // Awaiting garbage collection: backup_2 (4, 3), backup_1 (5, 3)
  message QuantityBasedExpiry {
    // Output only. The backup's position among its backups with the same source
    // cluster and type, by descending chronological order create time(i.e.
    // newest first).
    int32 retention_count = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The length of the quantity-based queue, specified by the
    // backup's retention policy.
    int32 total_retention_count = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Backup State
  enum State {
    // The state of the backup is unknown.
    STATE_UNSPECIFIED = 0;

    // The backup is ready.
    READY = 1;

    // The backup is creating.
    CREATING = 2;

    // The backup failed.
    FAILED = 3;

    // The backup is being deleted.
    DELETING = 4;
  }

  // Backup Type
  enum Type {
    // Backup Type is unknown.
    TYPE_UNSPECIFIED = 0;

    // ON_DEMAND backups that were triggered by the customer (e.g., not
    // AUTOMATED).
    ON_DEMAND = 1;

    // AUTOMATED backups triggered by the automated backups scheduler pursuant
    // to an automated backup policy.
    AUTOMATED = 2;

    // CONTINUOUS backups triggered by the automated backups scheduler
    // due to a continuous backup policy.
    CONTINUOUS = 3;
  }

  // Output only. The name of the backup resource with the format:
  //  * projects/{project}/locations/{region}/backups/{backup_id}
  // where the cluster and backup ID segments should satisfy the regex
  // expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`, e.g. 1-63 characters of
  // lowercase letters, numbers, and dashes, starting with a letter, and ending
  // with a letter or number. For more details see https://google.aip.dev/122.
  // The prefix of the backup resource name is the name of the parent
  // resource:
  //  * projects/{project}/locations/{region}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-settable and human-readable display name for the Backup.
  string display_name = 2;

  // Output only. The system-generated UID of the resource. The UID is assigned
  // when the resource is created, and it is retained until it is deleted.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Create time stamp
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update time stamp
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Delete time stamp
  google.protobuf.Timestamp delete_time = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels as key value pairs
  map<string, string> labels = 6;

  // Output only. The current state of the backup.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The backup type, which suggests the trigger for the backup.
  Type type = 8;

  // User-provided description of the backup.
  string description = 9;

  // Output only. The system-generated UID of the cluster which was used to
  // create this resource.
  string cluster_uid = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The full resource name of the backup source cluster
  // (e.g., projects/{project}/locations/{region}/clusters/{cluster_id}).
  string cluster_name = 10 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/Cluster" }
  ];

  // Output only. Reconciling (https://google.aip.dev/128#reconciliation), if
  // true, indicates that the service is actively updating the resource. This
  // can happen due to user-triggered updates or system actions like failover or
  // maintenance.
  bool reconciling = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The encryption config can be specified to encrypt the
  // backup with a customer-managed encryption key (CMEK). When this field is
  // not specified, the backup will then use default encryption scheme to
  // protect the user data.
  EncryptionConfig encryption_config = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The encryption information for the backup.
  EncryptionInfo encryption_info = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // For Resource freshness validation (https://google.aip.dev/154)
  string etag = 14;

  // Annotations to allow client tools to store small amount of arbitrary data.
  // This is distinct from labels.
  // https://google.aip.dev/128
  map<string, string> annotations = 16;

  // Output only. The size of the backup in bytes.
  int64 size_bytes = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which after the backup is eligible to be garbage
  // collected. It is the duration specified by the backup's retention policy,
  // added to the backup's create_time.
  google.protobuf.Timestamp expiry_time = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The QuantityBasedExpiry of the backup, specified by the
  // backup's retention policy. Once the expiry quantity is over retention, the
  // backup is eligible to be garbage collected.
  QuantityBasedExpiry expiry_quantity = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Reserved for future use.
  bool satisfies_pzs = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The database engine major version of the cluster this backup
  // was created from. Any restored cluster created from this backup will have
  // the same database version.
  DatabaseVersion database_version = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Input only. Immutable. Tag keys/values directly bound to this
  // resource. For example:
  // ```
  // "123/environment": "production",
  // "123/costCenter": "marketing"
  // ```
  map<string, string> tags = 25 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// SupportedDatabaseFlag gives general information about a database flag,
// like type and allowed values. This is a static value that is defined
// on the server side, and it cannot be modified by callers.
// To set the Database flags on a particular Instance, a caller should modify
// the Instance.database_flags field.
message SupportedDatabaseFlag {
  option (google.api.resource) = {
    type: "alloydb.googleapis.com/SupportedDatabaseFlag"
    pattern: "projects/{project}/locations/{location}/flags/{flag}"
    plural: "supportedDatabaseFlags"
    singular: "supportedDatabaseFlag"
  };

  // Restrictions on STRING type values
  message StringRestrictions {
    // The list of allowed values, if bounded. This field will be empty
    // if there is a unbounded number of allowed values.
    repeated string allowed_values = 1;
  }

  // Restrictions on INTEGER type values.
  message IntegerRestrictions {
    // The minimum value that can be specified, if applicable.
    google.protobuf.Int64Value min_value = 1;

    // The maximum value that can be specified, if applicable.
    google.protobuf.Int64Value max_value = 2;
  }

  // ValueType describes the semantic type of the value that the flag accepts.
  // Regardless of the ValueType, the Instance.database_flags field accepts the
  // stringified version of the value, i.e. "20" or "3.14".
  enum ValueType {
    // This is an unknown flag type.
    VALUE_TYPE_UNSPECIFIED = 0;

    // String type flag.
    STRING = 1;

    // Integer type flag.
    INTEGER = 2;

    // Float type flag.
    FLOAT = 3;

    // Denotes that the flag does not accept any values.
    NONE = 4;
  }

  // The restrictions on the flag value per type.
  oneof restrictions {
    // Restriction on STRING type value.
    StringRestrictions string_restrictions = 7;

    // Restriction on INTEGER type value.
    IntegerRestrictions integer_restrictions = 8;
  }

  // The name of the flag resource, following Google Cloud conventions, e.g.:
  //  * projects/{project}/locations/{location}/flags/{flag}
  // This field currently has no semantic meaning.
  string name = 1;

  // The name of the database flag, e.g. "max_allowed_packets".
  // The is a possibly key for the Instance.database_flags map field.
  string flag_name = 2;

  ValueType value_type = 3;

  // Whether the database flag accepts multiple values. If true,
  // a comma-separated list of stringified values may be specified.
  bool accepts_multiple_values = 4;

  // Major database engine versions for which this flag is supported.
  repeated DatabaseVersion supported_db_versions = 5;

  // Whether setting or updating this flag on an Instance requires a database
  // restart. If a flag that requires database restart is set, the backend
  // will automatically restart the database (making sure to satisfy any
  // availability SLO's).
  bool requires_db_restart = 6;
}

// Message describing User object.
message User {
  option (google.api.resource) = {
    type: "alloydb.googleapis.com/User"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/users/{user}"
    plural: "users"
    singular: "user"
    style: DECLARATIVE_FRIENDLY
  };

  // Enum that details the user type.
  enum UserType {
    // Unspecified user type.
    USER_TYPE_UNSPECIFIED = 0;

    // The default user type that authenticates via password-based
    // authentication.
    ALLOYDB_BUILT_IN = 1;

    // Database user that can authenticate via IAM-Based authentication.
    ALLOYDB_IAM_USER = 2;
  }

  // Output only. Name of the resource in the form of
  // projects/{project}/locations/{location}/cluster/{cluster}/users/{user}.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. Password for the user.
  string password = 2 [(google.api.field_behavior) = INPUT_ONLY];

  // Optional. List of database roles this user has.
  // The database role strings are subject to the PostgreSQL naming conventions.
  repeated string database_roles = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Type of this user.
  UserType user_type = 5 [(google.api.field_behavior) = OPTIONAL];

  // Input only. If the user already exists and it has additional roles, keep
  // them granted.
  bool keep_extra_roles = 6 [(google.api.field_behavior) = INPUT_ONLY];
}

// Message describing Database object.
message Database {
  option (google.api.resource) = {
    type: "alloydb.googleapis.com/Database"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/databases/{database}"
    plural: "databases"
    singular: "database"
    style: DECLARATIVE_FRIENDLY
  };

  // Identifier. Name of the resource in the form of
  // `projects/{project}/locations/{location}/clusters/{cluster}/databases/{database}`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Charset for the database.
  // This field can contain any PostgreSQL supported charset name.
  // Example values include "UTF8", "SQL_ASCII", etc.
  string charset = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Collation for the database.
  // Name of the custom or native collation for postgres.
  // Example values include "C", "POSIX", etc
  string collation = 3 [(google.api.field_behavior) = OPTIONAL];
}
