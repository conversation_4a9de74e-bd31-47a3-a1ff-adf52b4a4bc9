// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.alloydb.v1beta;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AlloyDb.V1Beta";
option go_package = "cloud.google.com/go/alloydb/apiv1beta/alloydbpb;alloydbpb";
option java_multiple_files = true;
option java_outer_classname = "CsqlResourcesProto";
option java_package = "com.google.cloud.alloydb.v1beta";
option php_namespace = "Google\\Cloud\\AlloyDb\\V1beta";
option ruby_package = "Google::Cloud::AlloyDB::V1beta";

// The source CloudSQL backup resource.
message CloudSQLBackupRunSource {
  // The project ID of the source CloudSQL instance. This should be the same as
  // the AlloyDB cluster's project.
  string project = 1;

  // Required. The CloudSQL instance ID.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The CloudSQL backup run ID.
  int64 backup_run_id = 3 [(google.api.field_behavior) = REQUIRED];
}
