{"methodConfig": [{"name": [{"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "ListClusters"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "GetCluster"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "ListInstances"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "GetInstance"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "ListBackups"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "GetBackup"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "ListSupportedDatabaseFlags"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "GenerateClientCertificate"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "GetConnectionInfo"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "ListUsers"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "GetUser"}, {"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin", "method": "ListDatabases"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.alloydb.v1beta.AlloyDBAdmin"}], "timeout": "60s"}]}