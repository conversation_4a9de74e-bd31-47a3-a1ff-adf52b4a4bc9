{"methodConfig": [{"name": [{"service": "google.cloud.alloydb.v1.AlloyDBAdmin", "method": "ListClusters"}, {"service": "google.cloud.alloydb.v1.AlloyDBAdmin", "method": "GetCluster"}, {"service": "google.cloud.alloydb.v1.AlloyDBAdmin", "method": "ListInstances"}, {"service": "google.cloud.alloydb.v1.AlloyDBAdmin", "method": "GetInstance"}, {"service": "google.cloud.alloydb.v1.AlloyDBAdmin", "method": "ListBackups"}, {"service": "google.cloud.alloydb.v1.AlloyDBAdmin", "method": "GetBackup"}, {"service": "google.cloud.alloydb.v1.AlloyDBAdmin", "method": "ListSupportedDatabaseFlags"}, {"service": "google.cloud.alloydb.v1.AlloyDBAdmin", "method": "GenerateClientCertificate"}, {"service": "google.cloud.alloydb.v1.AlloyDBAdmin", "method": "GetConnectionInfo"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.alloydb.v1.AlloyDBAdmin"}], "timeout": "60s"}]}