// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.alloydb.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/alloydb/v1/data_model.proto";
import "google/cloud/alloydb/v1/resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.AlloyDb.V1";
option go_package = "cloud.google.com/go/alloydb/apiv1/alloydbpb;alloydbpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.alloydb.v1";
option php_namespace = "Google\\Cloud\\AlloyDb\\V1";
option ruby_package = "Google::Cloud::AlloyDB::V1";

// Service describing handlers for resources
service AlloyDBAdmin {
  option (google.api.default_host) = "alloydb.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Clusters in a given project and location.
  rpc ListClusters(ListClustersRequest) returns (ListClustersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/clusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Cluster.
  rpc GetCluster(GetClusterRequest) returns (Cluster) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Cluster in a given project and location.
  rpc CreateCluster(CreateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/clusters"
      body: "cluster"
    };
    option (google.api.method_signature) = "parent,cluster,cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Cluster.
  rpc UpdateCluster(UpdateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{cluster.name=projects/*/locations/*/clusters/*}"
      body: "cluster"
    };
    option (google.api.method_signature) = "cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Cluster.
  rpc DeleteCluster(DeleteClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Promotes a SECONDARY cluster. This turns down replication
  // from the PRIMARY cluster and promotes a secondary cluster
  // into its own standalone cluster.
  // Imperative only.
  rpc PromoteCluster(PromoteClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/clusters/*}:promote"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Switches the roles of PRIMARY and SECONDARY clusters without any data loss.
  // This promotes the SECONDARY cluster to PRIMARY and sets up the original
  // PRIMARY cluster to replicate from this newly promoted cluster.
  rpc SwitchoverCluster(SwitchoverClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/clusters/*}:switchover"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new Cluster in a given project and location, with a volume
  // restored from the provided source, either a backup ID or a point-in-time
  // and a source cluster.
  rpc RestoreCluster(RestoreClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/clusters:restore"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a cluster of type SECONDARY in the given location using
  // the primary cluster as the source.
  rpc CreateSecondaryCluster(CreateSecondaryClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/clusters:createsecondary"
      body: "cluster"
    };
    option (google.api.method_signature) = "parent,cluster,cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Instances in a given project and location.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/clusters/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clusters/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Instance in a given project and location.
  rpc CreateInstance(CreateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/clusters/*}/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance,instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new SECONDARY Instance in a given project and location.
  rpc CreateSecondaryInstance(CreateSecondaryInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/clusters/*}/instances:createsecondary"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance,instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates new instances under the given project, location and cluster.
  // There can be only one primary instance in a cluster. If the primary
  // instance exists in the cluster as well as this request, then API will
  // throw an error.
  // The primary instance should exist before any read pool instance is
  // created. If the primary instance is a part of the request payload, then
  // the API will take care of creating instances in the correct order.
  // This method is here to support Google-internal use cases, and is not meant
  // for external customers to consume. Please do not start relying on it; its
  // behavior is subject to change without notice.
  rpc BatchCreateInstances(BatchCreateInstancesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/clusters/*}/instances:batchCreate"
      body: "requests"
    };
    option (google.longrunning.operation_info) = {
      response_type: "BatchCreateInstancesResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Instance.
  rpc UpdateInstance(UpdateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{instance.name=projects/*/locations/*/clusters/*/instances/*}"
      body: "instance"
    };
    option (google.api.method_signature) = "instance,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Instance.
  rpc DeleteInstance(DeleteInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clusters/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Forces a Failover for a highly available instance.
  // Failover promotes the HA standby instance as the new primary.
  // Imperative only.
  rpc FailoverInstance(FailoverInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/clusters/*/instances/*}:failover"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Injects fault in an instance.
  // Imperative only.
  rpc InjectFault(InjectFaultRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/clusters/*/instances/*}:injectFault"
      body: "*"
    };
    option (google.api.method_signature) = "fault_type,name";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Restart an Instance in a cluster.
  // Imperative only.
  rpc RestartInstance(RestartInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/clusters/*/instances/*}:restart"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Executes a SQL statement in a database inside an AlloyDB instance.
  rpc ExecuteSql(ExecuteSqlRequest) returns (ExecuteSqlResponse) {
    option (google.api.http) = {
      post: "/v1/{instance=projects/*/locations/*/clusters/*/instances/*}:executeSql"
      body: "*"
    };
    option (google.api.method_signature) =
        "instance,database,user,sql_statement,password";
  }

  // Lists Backups in a given project and location.
  rpc ListBackups(ListBackupsRequest) returns (ListBackupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/backups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Backup.
  rpc GetBackup(GetBackupRequest) returns (Backup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Backup in a given project and location.
  rpc CreateBackup(CreateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/backups"
      body: "backup"
    };
    option (google.api.method_signature) = "parent,backup,backup_id";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Backup.
  rpc UpdateBackup(UpdateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{backup.name=projects/*/locations/*/backups/*}"
      body: "backup"
    };
    option (google.api.method_signature) = "backup,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Backup.
  rpc DeleteBackup(DeleteBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists SupportedDatabaseFlags for a given project and location.
  rpc ListSupportedDatabaseFlags(ListSupportedDatabaseFlagsRequest)
      returns (ListSupportedDatabaseFlagsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/supportedDatabaseFlags"
    };
    option (google.api.method_signature) = "parent";
  }

  // Generate a client certificate signed by a Cluster CA.
  // The sole purpose of this endpoint is to support AlloyDB connectors and the
  // Auth Proxy client. The endpoint's behavior is subject to change without
  // notice, so do not rely on its behavior remaining constant. Future changes
  // will not break AlloyDB connectors or the Auth Proxy client.
  rpc GenerateClientCertificate(GenerateClientCertificateRequest)
      returns (GenerateClientCertificateResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/clusters/*}:generateClientCertificate"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get instance metadata used for a connection.
  rpc GetConnectionInfo(GetConnectionInfoRequest) returns (ConnectionInfo) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/clusters/*/instances/*}/connectionInfo"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists Users in a given project and location.
  rpc ListUsers(ListUsersRequest) returns (ListUsersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/clusters/*}/users"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single User.
  rpc GetUser(GetUserRequest) returns (User) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clusters/*/users/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new User in a given project, location, and cluster.
  rpc CreateUser(CreateUserRequest) returns (User) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/clusters/*}/users"
      body: "user"
    };
    option (google.api.method_signature) = "parent,user,user_id";
  }

  // Updates the parameters of a single User.
  rpc UpdateUser(UpdateUserRequest) returns (User) {
    option (google.api.http) = {
      patch: "/v1/{user.name=projects/*/locations/*/clusters/*/users/*}"
      body: "user"
    };
    option (google.api.method_signature) = "user,update_mask";
  }

  // Deletes a single User.
  rpc DeleteUser(DeleteUserRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clusters/*/users/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Databases in a given project and location.
  rpc ListDatabases(ListDatabasesRequest) returns (ListDatabasesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/clusters/*}/databases"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Message for requesting list of Clusters
message ListClustersRequest {
  // Required. The name of the parent resource. For the required format, see the
  // comment on the Cluster.name field. Additionally, you can perform an
  // aggregated list operation by specifying a value with the following format:
  //  * projects/{project}/locations/-
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Cluster"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing Clusters
message ListClustersResponse {
  // The list of Cluster
  repeated Cluster clusters = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Cluster
message GetClusterRequest {
  // Required. The name of the resource. For the required format, see the
  // comment on the Cluster.name field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/Cluster" }
  ];

  // Optional. The view of the cluster to return. Returns all default fields if
  // not set.
  ClusterView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

message CreateSecondaryClusterRequest {
  // Required. The location of the new cluster. For the required
  // format, see the comment on the Cluster.name field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Cluster"
    }
  ];

  // Required. ID of the requesting object (the secondary cluster).
  string cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Configuration of the requesting object (the secondary cluster).
  Cluster cluster = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Message for creating a Cluster
message CreateClusterRequest {
  // Required. The location of the new cluster. For the required format, see the
  // comment on the Cluster.name field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Cluster"
    }
  ];

  // Required. ID of the requesting object.
  string cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Cluster cluster = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Cluster
message UpdateClusterRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // Cluster resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The resource being updated
  Cluster cluster = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, update succeeds even if cluster is not found. In
  // that case, a new cluster is created and `update_mask` is ignored.
  bool allow_missing = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Cluster
message DeleteClusterRequest {
  // Required. The name of the resource. For the required format, see the
  // comment on the Cluster.name field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/Cluster" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The current etag of the Cluster.
  // If an etag is provided and does not match the current etag of the Cluster,
  // deletion will be blocked and an ABORTED error will be returned.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether to cascade delete child instances for given cluster.
  bool force = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for switching over to a cluster
message SwitchoverClusterRequest {
  // Required. The name of the resource. For the required format, see the
  // comment on the Cluster.name field
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/Cluster" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for promoting a Cluster
message PromoteClusterRequest {
  // Required. The name of the resource. For the required format, see the
  // comment on the Cluster.name field
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/Cluster" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The current etag of the Cluster.
  // If an etag is provided and does not match the current etag of the Cluster,
  // deletion will be blocked and an ABORTED error will be returned.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for restoring a Cluster from a backup or another cluster at a given
// point in time.
message RestoreClusterRequest {
  // Required.
  // The source to import from.
  oneof source {
    // Backup source.
    BackupSource backup_source = 4;

    // ContinuousBackup source. Continuous backup needs to be enabled in the
    // source cluster for this operation to succeed.
    ContinuousBackupSource continuous_backup_source = 8;
  }

  // Required. The name of the parent resource. For the required format, see the
  // comment on the Cluster.name field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Cluster"
    }
  ];

  // Required. ID of the requesting object.
  string cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Cluster cluster = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Instances
message ListInstancesRequest {
  // Required. The name of the parent resource. For the required format, see the
  // comment on the Instance.name field. Additionally, you can perform an
  // aggregated list operation by specifying a value with one of the following
  // formats:
  //  * projects/{project}/locations/-/clusters/-
  //  * projects/{project}/locations/{region}/clusters/-
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Instance"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing Instances
message ListInstancesResponse {
  // The list of Instance
  repeated Instance instances = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Instance
message GetInstanceRequest {
  // Required. The name of the resource. For the required format, see the
  // comment on the Instance.name field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "alloydb.googleapis.com/Instance"
    }
  ];

  // The view of the instance to return.
  InstanceView view = 2;
}

// Message for creating a Instance
message CreateInstanceRequest {
  // Required. The name of the parent resource. For the required format, see the
  // comment on the Instance.name field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Instance"
    }
  ];

  // Required. ID of the requesting object.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for creating a Secondary Instance
message CreateSecondaryInstanceRequest {
  // Required. The name of the parent resource. For the required format, see the
  // comment on the Instance.name field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Instance"
    }
  ];

  // Required. ID of the requesting object.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// See usage below for notes.
message CreateInstanceRequests {
  // Required. Primary and read replica instances to be created. This list
  // should not be empty.
  repeated CreateInstanceRequest create_instance_requests = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Message for creating a batch of instances under the specified cluster.
message BatchCreateInstancesRequest {
  // Required. The name of the parent resource.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Instance"
    }
  ];

  // Required. Resources being created.
  CreateInstanceRequests requests = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for creating batches of instances in a cluster.
message BatchCreateInstancesResponse {
  // Created instances.
  repeated Instance instances = 1;
}

// Message for metadata that is specific to BatchCreateInstances API.
// NEXT_ID: 3
message BatchCreateInstancesMetadata {
  // The instances being created in the API call. Each string in this list
  // is the server defined resource path for target instances in the request
  // and for the format of each string, see the comment on the Instance.name
  // field.
  repeated string instance_targets = 1;

  // A map representing state of the instances involved in the
  // BatchCreateInstances operation during the operation execution.
  // The instance state will be in STATE_UNSPECIFIED state if the instance has
  // not yet been picked up for processing.
  // The key of the map is the name of the instance resource.
  // For the format, see the comment on the Instance.name field.
  map<string, BatchCreateInstanceStatus> instance_statuses = 2;
}

// Message for current status of an instance in the BatchCreateInstances
// operation.
// For example, lets say a BatchCreateInstances workflow has 4 instances,
// Instance1 through Instance4. Lets also assume that 2 instances succeeded
// but the third failed to create and the 4th was never picked up for creation
// because of failure of the previous one. Then, resulting states would look
// something like:
//   1. Instance1 = ROLLED_BACK
//   2. Instance2 = ROLLED_BACK
//   3. Instance3 = FAILED
//   4. Instance4 = FAILED
//
// However, while the operation is running, the instance might be in other
// states including PENDING_CREATE, ACTIVE, DELETING and CREATING. The states
// / do not get further updated once the operation is done.
message BatchCreateInstanceStatus {
  // State contains all valid instance states for the BatchCreateInstances
  // operation. This is mainly used for status reporting through the LRO
  // metadata.
  enum State {
    // The state of the instance is unknown.
    STATE_UNSPECIFIED = 0;

    // Instance is pending creation and has not yet been picked up for
    // processing in the backend.
    PENDING_CREATE = 1;

    // The instance is active and running.
    READY = 2;

    // The instance is being created.
    CREATING = 3;

    // The instance is being deleted.
    DELETING = 4;

    // The creation of the instance failed or a fatal error occurred during
    // an operation on the instance or a batch of instances.
    FAILED = 5;

    // The instance was created successfully, but was rolled back and deleted
    // due to some other failure during BatchCreateInstances operation.
    ROLLED_BACK = 6;
  }

  // The current state of an instance involved in the batch create operation.
  // Once the operation is complete, the final state of the instances in the
  // LRO can be one of:
  //   1. ACTIVE, indicating that instances were created successfully
  //   2. FAILED, indicating that a particular instance failed creation
  //   3. ROLLED_BACK indicating that although the instance was created
  //      successfully, it had to be rolled back and deleted due to failure in
  //      other steps of the workflow.
  State state = 1;

  // DEPRECATED - Use the error field instead.
  // Error, if any error occurred and is available, during instance creation.
  string error_msg = 2;

  // The RPC status of the instance creation operation. This field will be
  // present if an error happened during the instance creation.
  google.rpc.Status error = 4;

  Instance.InstanceType type = 3;
}

// Message for updating a Instance
message UpdateInstanceRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // Instance resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The resource being updated
  Instance instance = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, update succeeds even if instance is not found. In
  // that case, a new instance is created and `update_mask` is ignored.
  bool allow_missing = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Instance
message DeleteInstanceRequest {
  // Required. The name of the resource. For the required format, see the
  // comment on the Instance.name field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "alloydb.googleapis.com/Instance"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The current etag of the Instance.
  // If an etag is provided and does not match the current etag of the Instance,
  // deletion will be blocked and an ABORTED error will be returned.
  string etag = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for triggering failover on an Instance
message FailoverInstanceRequest {
  // Required. The name of the resource. For the required format, see the
  // comment on the Instance.name field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "alloydb.googleapis.com/Instance"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for triggering fault injection on an instance
message InjectFaultRequest {
  // FaultType contains all valid types of faults that can be injected to an
  // instance.
  enum FaultType {
    // The fault type is unknown.
    FAULT_TYPE_UNSPECIFIED = 0;

    // Stop the VM
    STOP_VM = 1;
  }

  // Required. The type of fault to be injected in an instance.
  FaultType fault_type = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the resource. For the required format, see the
  // comment on the Instance.name field.
  string name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "alloydb.googleapis.com/Instance"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

message RestartInstanceRequest {
  // Required. The name of the resource. For the required format, see the
  // comment on the Instance.name field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "alloydb.googleapis.com/Instance"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, performs request validation, for example, permission
  // checks and any other type of validation, but does not actually execute the
  // create request.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Full name of the nodes as obtained from INSTANCE_VIEW_FULL to
  // restart upon. Applicable only to read instances.
  repeated string node_ids = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request for ExecuteSql rpc.
message ExecuteSqlRequest {
  // Oneof field to support other credential mechanisms in future like
  // SecretManager etc.
  oneof user_credential {
    // Optional. The database native user’s password.
    string password = 5 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The instance where the SQL will be executed. For the required
  // format, see the comment on the Instance.name field.
  string instance = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "alloydb.googleapis.com/Instance"
    }
  ];

  // Required. Name of the database where the query will be executed.
  // Note - Value provided should be the same as expected from `SELECT
  // current_database();` and NOT as a resource reference.
  string database = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Database user to be used for executing the SQL.
  // Note - Value provided should be the same as expected from
  // `SELECT current_user;` and NOT as a resource reference.
  string user = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. SQL statement to execute on database. Any valid statement is
  // permitted, including DDL, DML, DQL statements.
  string sql_statement = 4 [(google.api.field_behavior) = REQUIRED];
}

// Execute a SQL statement response.
message ExecuteSqlResponse {
  // SqlResult represents the results for the execution of sql statements.
  repeated SqlResult sql_results = 1;

  // Any additional metadata information regarding the execution of the sql
  // statement.
  ExecuteSqlMetadata metadata = 3;
}

// Any additional metadata information regarding the execution of the SQL
message ExecuteSqlMetadata {
  // Status contains all valid Status a SQL execution can end up in.
  enum Status {
    // The status is unknown.
    STATUS_UNSPECIFIED = 0;

    // No error during SQL execution i.e. All SQL statements ran to completion.
    // The "message" will be empty.
    OK = 1;

    // Same as OK, except indicates that only partial results were
    // returned. The "message" field will contain details on why results were
    // truncated.
    PARTIAL = 2;

    // Error during SQL execution. Atleast 1 SQL statement execution resulted in
    // a error. Side effects of other statements are rolled back.  The "message"
    // field will contain human readable error given by Postgres of the first
    // bad SQL statement. SQL execution errors don't constitute API errors as
    // defined in https://google.aip.dev/193 but will be returned as part of
    // this message.
    ERROR = 3;
  }

  // Message related to SQL execution. Marked as core content since it
  // can potentially contain details related to the query or result set. This
  // field can be used to convey messages such as "when the SQL result set
  // exceeds the acceptable response size limits."
  string message = 1;

  // Set to true if SQL returned a result set larger than the acceptable
  // response size limits and the result was truncated.
  bool partial_result = 2;

  // The time duration taken to execute the sql statement.
  google.protobuf.Duration sql_statement_execution_duration = 3;

  // Status of SQL execution.
  Status status = 4;
}

// Message for requesting list of Backups
message ListBackupsRequest {
  // Required. Parent value for ListBackupsRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Backup"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing Backups
message ListBackupsResponse {
  // The list of Backup
  repeated Backup backups = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Backup
message GetBackupRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/Backup" }
  ];
}

// Message for creating a Backup
message CreateBackupRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Backup"
    }
  ];

  // Required. ID of the requesting object.
  string backup_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Backup backup = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the backend validates the request, but doesn't actually
  // execute it.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Backup
message UpdateBackupRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // Backup resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The resource being updated
  Backup backup = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the backend validates the request, but doesn't actually
  // execute it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, update succeeds even if instance is not found. In
  // that case, a new backup is created and `update_mask` is ignored.
  bool allow_missing = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Backup
message DeleteBackupRequest {
  // Required. Name of the resource. For the required format, see the comment on
  // the Backup.name field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/Backup" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the backend validates the request, but doesn't actually
  // execute it.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The current etag of the Backup.
  // If an etag is provided and does not match the current etag of the Backup,
  // deletion will be blocked and an ABORTED error will be returned.
  string etag = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for listing the information about the supported Database flags.
message ListSupportedDatabaseFlagsRequest {
  // Required. The name of the parent resource. The required format is:
  //  * projects/{project}/locations/{location}
  //
  // Regardless of the parent specified here, as long it is contains a valid
  // project and location, the service will return a static list of supported
  // flags resources. Note that we do not yet support region-specific
  // flags.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/SupportedDatabaseFlag"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;
}

// Message for response to listing SupportedDatabaseFlags.
message ListSupportedDatabaseFlagsResponse {
  // The list of SupportedDatabaseFlags.
  repeated SupportedDatabaseFlag supported_database_flags = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// Message for requests to generate a client certificate signed by the Cluster
// CA.
message GenerateClientCertificateRequest {
  // Required. The name of the parent resource. The required format is:
  //  * projects/{project}/locations/{location}/clusters/{cluster}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/Cluster" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional hint to the endpoint to generate the client
  // certificate with the requested duration. The duration can be from 1 hour to
  // 24 hours. The endpoint may or may not honor the hint. If the hint is left
  // unspecified or is not honored, then the endpoint will pick an appropriate
  // default duration.
  google.protobuf.Duration cert_duration = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The public key from the client.
  string public_key = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An optional hint to the endpoint to generate a client
  // ceritificate that can be used by AlloyDB connectors to exchange additional
  // metadata with the server after TLS handshake.
  bool use_metadata_exchange = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Message returned by a GenerateClientCertificate operation.
message GenerateClientCertificateResponse {
  // Output only. The pem-encoded chain that may be used to verify the X.509
  // certificate. Expected to be in issuer-to-root order according to RFC 5246.
  repeated string pem_certificate_chain = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The pem-encoded cluster ca X.509 certificate.
  string ca_cert = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for GetConnectionInfo.
message GetConnectionInfoRequest {
  // Required. The name of the parent resource. The required format is:
  // projects/{project}/locations/{location}/clusters/{cluster}/instances/{instance}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "alloydb.googleapis.com/Instance"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Request specific metadata, if any.
  oneof request_specific {
    // Output only. BatchCreateInstances related metadata.
    BatchCreateInstancesMetadata batch_create_instances_metadata = 8
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting list of Users
message ListUsersRequest {
  // Required. Parent value for ListUsersRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/User"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing Users
message ListUsersResponse {
  // The list of User
  repeated User users = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a User
message GetUserRequest {
  // Required. The name of the resource. For the required format, see the
  // comment on the User.name field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/User" }
  ];
}

// Message for creating a User
message CreateUserRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/User"
    }
  ];

  // Required. ID of the requesting object.
  string user_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  User user = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the backend validates the request, but doesn't actually
  // execute it.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a User
message UpdateUserRequest {
  // Optional. Field mask is used to specify the fields to be overwritten in the
  // User resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The resource being updated
  User user = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the backend validates the request, but doesn't actually
  // execute it.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Allow missing fields in the update mask.
  bool allow_missing = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a User
message DeleteUserRequest {
  // Required. The name of the resource. For the required format, see the
  // comment on the User.name field.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "alloydb.googleapis.com/User" }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server ignores the
  // request if it has already been completed. The server guarantees that for at
  // least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if the original operation with the same request ID
  // was received, and if so, ignores the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, the backend validates the request, but doesn't actually
  // execute it.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Databases.
message ListDatabasesRequest {
  // Required. Parent value for ListDatabasesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "alloydb.googleapis.com/Database"
    }
  ];

  // Optional. The maximum number of databases to return. The service may return
  // fewer than this value. If unspecified, 2000 is the default page_size. The
  // max value of page_size will be 4000, values above max will be coerced to
  // max.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListDatabases` call.
  // This should be provided to retrieve the subsequent page.
  // This field is currently not supported, its value will be ignored if passed.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  // This field is currently not supported, its value will be ignored if passed.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing Databases.
message ListDatabasesResponse {
  // The list of databases
  repeated Database databases = 1;

  // A token identifying the next page of results the server should return.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
