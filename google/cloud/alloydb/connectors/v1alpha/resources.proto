// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.alloydb.connectors.v1alpha;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AlloyDb.Connectors.V1Alpha";
option go_package = "cloud.google.com/go/alloydb/connectors/apiv1alpha/connectorspb;connectorspb";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.cloud.alloydb.connectors.v1alpha";
option php_namespace = "Google\\Cloud\\AlloyDb\\Connectors\\V1alpha";
option ruby_package = "Google::Cloud::AlloyDb::Connectors::V1alpha";

// Message used by AlloyDB connectors to exchange client and connection metadata
// with the server after a successful TLS handshake. This metadata includes an
// IAM token, which is used to authenticate users based on their IAM identity.
// The sole purpose of this message is for the use of AlloyDB connectors.
// Clients should not rely on this message directly as there can be breaking
// changes in the future.
message MetadataExchangeRequest {
  // AuthType contains all supported authentication types.
  enum AuthType {
    // Authentication type is unspecified and DB_NATIVE is used by default
    AUTH_TYPE_UNSPECIFIED = 0;

    // Database native authentication (user/password)
    DB_NATIVE = 1;

    // Automatic IAM authentication
    AUTO_IAM = 2;
  }

  // Optional. Connector information.
  string user_agent = 1 [(google.api.field_behavior) = OPTIONAL];

  // Authentication type.
  AuthType auth_type = 2;

  // IAM token used for both IAM user authentiation and
  // `alloydb.instances.connect` permission check.
  string oauth2_token = 3;
}

// Message for response to metadata exchange request. The sole purpose of this
// message is for the use of AlloyDB connectors. Clients should not rely on this
// message directly as there can be breaking changes in the future.
message MetadataExchangeResponse {
  // Response code.
  enum ResponseCode {
    // Unknown response code
    RESPONSE_CODE_UNSPECIFIED = 0;

    // Success
    OK = 1;

    // Failure
    ERROR = 2;
  }

  // Response code.
  ResponseCode response_code = 1;

  // Optional. Error message.
  string error = 2 [(google.api.field_behavior) = OPTIONAL];
}
