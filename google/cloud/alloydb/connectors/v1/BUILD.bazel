# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "connectors_proto",
    srcs = [
        "resources.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_proto_library",
    "java_gapic_assembly_gradle_pkg",
)

java_proto_library(
    name = "connectors_java_proto",
    deps = [":connectors_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-alloydb-connectors-v1-java",
    deps = [
        ":connectors_proto",
        ":connectors_java_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
    "go_gapic_assembly_pkg",
)

go_proto_library(
    name = "connectors_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/alloydb/connectors/apiv1/connectorspb",
    protos = [":connectors_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_assembly_pkg(
    name = "google-cloud-alloydb-connectors-v1-go",
    deps = [
        ":connectors_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
    "py_gapic_library",
    "py_gapic_assembly_pkg",
)

moved_proto_library(
    name = "connectors_moved_proto",
    srcs = [":connectors_proto"],
    deps = [
        "//google/api:field_behavior_proto",
    ],
)

py_proto_library(
    name = "connectors_py_proto",
    deps = [":connectors_moved_proto"],
)

py_grpc_library(
    name = "connectors_py_grpc",
    srcs = [":connectors_moved_proto"],
    deps = [":connectors_py_proto"],
)

py_gapic_library(
    name = "connectors_py_gapic",
    srcs = [":connectors_proto"],
    rest_numeric_enums = False,
    transport = "grpc+rest",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-alloydb-connectors-v1-py",
    deps = [
        ":connectors_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "connectors_php_proto",
    deps = [":connectors_proto"],
)

php_gapic_assembly_pkg(
    name = "google-cloud-alloydb-connectors-v1-php",
    deps = [
        ":connectors_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "connectors_ruby_proto",
    deps = [":connectors_proto"],
)

ruby_grpc_library(
    name = "connectors_ruby_grpc",
    srcs = [":connectors_proto"],
    deps = [":connectors_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_proto_library",
    "csharp_gapic_assembly_pkg",
)

csharp_proto_library(
    name = "connectors_csharp_proto",
    deps = [":connectors_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-alloydb-connectors-v1-csharp",
    package_name = "Google.Cloud.AlloyDb.Connectors.V1",
    generate_nongapic_package = True,
    deps = [
        ":connectors_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "connectors_cc_proto",
    deps = [":connectors_proto"],
)

cc_grpc_library(
    name = "connectors_cc_grpc",
    srcs = [":connectors_proto"],
    grpc_only = True,
    deps = [":connectors_cc_proto"],
)
