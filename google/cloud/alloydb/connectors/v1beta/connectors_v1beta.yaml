type: google.api.Service
config_version: 3
name: connectors.googleapis.com
title: AlloyDB connectors

types:
- name: google.cloud.alloydb.connectors.v1beta.MetadataExchangeRequest
- name: google.cloud.alloydb.connectors.v1beta.MetadataExchangeResponse

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1194526&template=1689942
  documentation_uri: https://cloud.google.com/alloydb/docs
  api_short_name: alloydb
  github_label: 'api: alloydb'
  doc_tag_prefix: alloydb
  organization: CLOUD
  library_settings:
  - version: google.cloud.alloydb.connectors.v1beta
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
