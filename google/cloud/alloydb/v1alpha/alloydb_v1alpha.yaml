type: google.api.Service
config_version: 3
name: alloydb.googleapis.com
title: AlloyDB API

apis:
- name: google.cloud.alloydb.v1alpha.AlloyDBAdmin
- name: google.cloud.location.Locations
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.alloydb.v1alpha.BatchCreateInstancesResponse
- name: google.cloud.alloydb.v1alpha.OperationMetadata
- name: google.cloud.alloydb.v1alpha.UpgradeClusterResponse

documentation:
  summary: |-
    AlloyDB for PostgreSQL is an open source-compatible database service that
    provides a powerful option for migrating, modernizing, or building
    commercial-grade applications. It offers full compatibility with standard
    PostgreSQL, and is more than 4x faster for transactional workloads and up
    to 100x faster for analytical queries than standard PostgreSQL in our
    performance tests. AlloyDB for PostgreSQL offers a 99.99 percent
    availability SLA inclusive of maintenance. AlloyDB is optimized
    for the most demanding use cases, allowing you to build new applications
    that require high transaction throughput, large database sizes, or
    multiple read resources; scale existing PostgreSQL workloads with no
    application changes; and modernize legacy proprietary databases.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1alpha/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1alpha/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1alpha/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1alpha/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1alpha/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1alpha/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.alloydb.v1alpha.AlloyDBAdmin.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1194526&template=1689942
  documentation_uri: https://cloud.google.com/alloydb/docs
  api_short_name: alloydb
  github_label: 'api: alloydb'
  doc_tag_prefix: alloydb
  organization: CLOUD
  library_settings:
  - version: google.cloud.alloydb.v1alpha
    launch_stage: ALPHA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
