// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.alloydb.v1alpha;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AlloyDb.V1Alpha";
option go_package = "cloud.google.com/go/alloydb/apiv1alpha/alloydbpb;alloydbpb";
option java_multiple_files = true;
option java_outer_classname = "GeminiProto";
option java_package = "com.google.cloud.alloydb.v1alpha";
option php_namespace = "Google\\Cloud\\AlloyDb\\V1alpha";
option ruby_package = "Google::Cloud::AlloyDB::V1alpha";

// Cluster level configuration parameters related to the Gemini in Databases
// add-on.
message GeminiClusterConfig {
  // Output only. Whether the Gemini in Databases add-on is enabled for the
  // cluster. It will be true only if the add-on has been enabled for the
  // billing account corresponding to the cluster. Its status is toggled from
  // the Admin Control Center (ACC) and cannot be toggled using AlloyDB's APIs.
  bool entitled = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Instance level configuration parameters related to the Gemini in Databases
// add-on.
message GeminiInstanceConfig {
  // Output only. Whether the Gemini in Databases add-on is enabled for the
  // instance. It will be true only if the add-on has been enabled for the
  // billing account corresponding to the instance. Its status is toggled from
  // the Admin Control Center (ACC) and cannot be toggled using AlloyDB's APIs.
  bool entitled = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}
