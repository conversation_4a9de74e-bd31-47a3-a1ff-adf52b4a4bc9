// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.alloydb.v1alpha;

option csharp_namespace = "Google.Cloud.AlloyDb.V1Alpha";
option go_package = "cloud.google.com/go/alloydb/apiv1alpha/alloydbpb;alloydbpb";
option java_multiple_files = true;
option java_outer_classname = "DataModelProto";
option java_package = "com.google.cloud.alloydb.v1alpha";
option php_namespace = "Google\\Cloud\\AlloyDb\\V1alpha";
option ruby_package = "Google::Cloud::AlloyDB::V1alpha";

// SqlResult represents the result for the execution of a sql statement.
message SqlResult {
  // List of columns included in the result. This also includes the data type
  // of the column.
  repeated SqlResultColumn columns = 1;

  // Rows returned by the SQL statement.
  repeated SqlResultRow rows = 2;
}

// Contains the name and datatype of a column in a SQL Result.
message SqlResultColumn {
  // Name of the column.
  string name = 1;

  // Datatype of the column as reported by the postgres driver.
  // Common type names are "VARCHAR", "TEXT", "NVARCHAR", "DECIMAL", "BOOL",
  // "INT", and "BIGINT".
  string type = 2;
}

// A single row from a sql result.
message SqlResultRow {
  // List of values in a row of sql result.
  repeated SqlResultValue values = 1;
}

// A single value in a row from a sql result.
message SqlResultValue {
  // The cell value represented in string format.
  // Timestamps are converted to string using RFC3339Nano format.
  optional string value = 1;

  // Set to true if cell value is null.
  optional bool null_value = 2;
}
