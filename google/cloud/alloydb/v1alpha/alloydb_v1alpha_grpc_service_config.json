{"methodConfig": [{"name": [{"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "ListClusters"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "GetCluster"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "ListInstances"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "GetInstance"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "ListBackups"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "GetBackup"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "ListSupportedDatabaseFlags"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "GenerateClientCertificate"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "GetConnectionInfo"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "ListUsers"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin", "method": "GetUser"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.alloydb.v1alpha.AlloyDBAdmin"}, {"service": "google.cloud.alloydb.v1alpha.AlloyDBOmniAdmin"}], "timeout": "60s"}]}