# This build file includes a target for the Ruby wrapper library for
# google-cloud-text_to_speech.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for texttospeech.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "texttospeech_ruby_wrapper",
    srcs = ["//google/cloud/texttospeech/v1:texttospeech_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-text_to_speech",
        "ruby-cloud-env-prefix=TEXTTOSPEECH",
        "ruby-cloud-wrapper-of=v1:0.12;v1beta1:0.13",
        "ruby-cloud-product-url=https://cloud.google.com/text-to-speech",
        "ruby-cloud-api-id=texttospeech.googleapis.com",
        "ruby-cloud-api-shortname=texttospeech",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Text-to-Speech converts text or Speech Synthesis Markup Language (SSML) input into audio data of natural human speech.",
    ruby_cloud_title = "Cloud Text-to-Speech",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-texttospeech-ruby",
    deps = [
        ":texttospeech_ruby_wrapper",
    ],
)
