# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "texttospeech_proto",
    srcs = [
        "cloud_tts.proto",
        "cloud_tts_lrs.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "texttospeech_proto_with_info",
    deps = [
        ":texttospeech_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "texttospeech_java_proto",
    deps = [":texttospeech_proto"],
)

java_grpc_library(
    name = "texttospeech_java_grpc",
    srcs = [":texttospeech_proto"],
    deps = [":texttospeech_java_proto"],
)

java_gapic_library(
    name = "texttospeech_java_gapic",
    srcs = [":texttospeech_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "texttospeech_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "texttospeech_v1beta1.yaml",
    test_deps = [
        ":texttospeech_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":texttospeech_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "texttospeech_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.texttospeech.v1beta1.TextToSpeechClientHttpJsonTest",
        "com.google.cloud.texttospeech.v1beta1.TextToSpeechClientTest",
        "com.google.cloud.texttospeech.v1beta1.TextToSpeechLongAudioSynthesizeClientHttpJsonTest",
        "com.google.cloud.texttospeech.v1beta1.TextToSpeechLongAudioSynthesizeClientTest",
    ],
    runtime_deps = [":texttospeech_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-texttospeech-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":texttospeech_java_gapic",
        ":texttospeech_java_grpc",
        ":texttospeech_java_proto",
        ":texttospeech_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "texttospeech_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/texttospeech/apiv1beta1/texttospeechpb",
    protos = [":texttospeech_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "texttospeech_go_gapic",
    srcs = [":texttospeech_proto_with_info"],
    grpc_service_config = "texttospeech_grpc_service_config.json",
    importpath = "cloud.google.com/go/texttospeech/apiv1beta1;texttospeech",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "texttospeech_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":texttospeech_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-texttospeech-v1beta1-go",
    deps = [
        ":texttospeech_go_gapic",
        ":texttospeech_go_gapic_srcjar-metadata.srcjar",
        ":texttospeech_go_gapic_srcjar-snippets.srcjar",
        ":texttospeech_go_gapic_srcjar-test.srcjar",
        ":texttospeech_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "texttospeech_py_gapic",
    srcs = [":texttospeech_proto"],
    grpc_service_config = "texttospeech_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "texttospeech_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "texttospeech_py_gapic_test",
    srcs = [
        "texttospeech_py_gapic_pytest.py",
        "texttospeech_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":texttospeech_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "texttospeech-v1beta1-py",
    deps = [
        ":texttospeech_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "texttospeech_php_proto",
    deps = [":texttospeech_proto"],
)

php_gapic_library(
    name = "texttospeech_php_gapic",
    srcs = [":texttospeech_proto_with_info"],
    grpc_service_config = "texttospeech_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "texttospeech_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":texttospeech_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-texttospeech-v1beta1-php",
    deps = [
        ":texttospeech_php_gapic",
        ":texttospeech_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "texttospeech_nodejs_gapic",
    package_name = "@google-cloud/text-to-speech",
    src = ":texttospeech_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "texttospeech_grpc_service_config.json",
    package = "google.cloud.texttospeech.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "texttospeech_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "texttospeech-v1beta1-nodejs",
    deps = [
        ":texttospeech_nodejs_gapic",
        ":texttospeech_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "texttospeech_ruby_proto",
    deps = [":texttospeech_proto"],
)

ruby_grpc_library(
    name = "texttospeech_ruby_grpc",
    srcs = [":texttospeech_proto"],
    deps = [":texttospeech_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "texttospeech_ruby_gapic",
    srcs = [":texttospeech_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=texttospeech.googleapis.com",
        "ruby-cloud-api-shortname=texttospeech",
        "ruby-cloud-env-prefix=TEXTTOSPEECH",
        "ruby-cloud-gem-name=google-cloud-text_to_speech-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/text-to-speech",
    ],
    grpc_service_config = "texttospeech_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Text-to-Speech converts text or Speech Synthesis Markup Language (SSML) input into audio data of natural human speech.",
    ruby_cloud_title = "Cloud Text-to-Speech V1beta1",
    service_yaml = "texttospeech_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":texttospeech_ruby_grpc",
        ":texttospeech_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-texttospeech-v1beta1-ruby",
    deps = [
        ":texttospeech_ruby_gapic",
        ":texttospeech_ruby_grpc",
        ":texttospeech_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "texttospeech_csharp_proto",
    deps = [":texttospeech_proto"],
)

csharp_grpc_library(
    name = "texttospeech_csharp_grpc",
    srcs = [":texttospeech_proto"],
    deps = [":texttospeech_csharp_proto"],
)

csharp_gapic_library(
    name = "texttospeech_csharp_gapic",
    srcs = [":texttospeech_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "texttospeech_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "texttospeech_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":texttospeech_csharp_grpc",
        ":texttospeech_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-texttospeech-v1beta1-csharp",
    deps = [
        ":texttospeech_csharp_gapic",
        ":texttospeech_csharp_grpc",
        ":texttospeech_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "texttospeech_cc_proto",
    deps = [":texttospeech_proto"],
)

cc_grpc_library(
    name = "texttospeech_cc_grpc",
    srcs = [":texttospeech_proto"],
    grpc_only = True,
    deps = [":texttospeech_cc_proto"],
)
