{"methodConfig": [{"name": [{"service": "google.cloud.texttospeech.v1beta1.TextToSpeech"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.texttospeech.v1beta1.TextToSpeechLongAudioSynthesize", "method": "SynthesizeLongAudio"}], "timeout": "5000s"}]}