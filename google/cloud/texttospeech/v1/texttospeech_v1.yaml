type: google.api.Service
config_version: 3
name: texttospeech.googleapis.com
title: Cloud Text-to-Speech API

apis:
- name: google.cloud.texttospeech.v1.TextToSpeech
- name: google.cloud.texttospeech.v1.TextToSpeechLongAudioSynthesize
- name: google.longrunning.Operations

types:
- name: google.cloud.texttospeech.v1.SynthesizeLongAudioMetadata

documentation:
  summary: |-
    Synthesizes natural-sounding speech by applying powerful neural network
    models.
  overview: |-
    # Introduction

    Google Cloud Text-to-Speech API provides speech synthesis as a service.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.texttospeech.v1.TextToSpeech.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.texttospeech.v1.TextToSpeechLongAudioSynthesize.SynthesizeLongAudio
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
