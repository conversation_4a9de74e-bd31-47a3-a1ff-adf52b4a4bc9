# This build file includes a target for the Ruby wrapper library for
# google-cloud-web_risk.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for webrisk.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "webrisk_ruby_wrapper",
    srcs = ["//google/cloud/webrisk/v1:webrisk_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-web_risk",
        "ruby-cloud-env-prefix=WEBRISK",
        "ruby-cloud-wrapper-of=v1:0.11;v1beta1:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/web-risk",
        "ruby-cloud-api-id=webrisk.googleapis.com",
        "ruby-cloud-api-shortname=webrisk",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Web Risk is an enterprise security product that lets your client applications check URLs against Google's constantly updated lists of unsafe web resources.",
    ruby_cloud_title = "Web Risk",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-webrisk-ruby",
    deps = [
        ":webrisk_ruby_wrapper",
    ],
)
