# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "webrisk_proto",
    srcs = [
        "webrisk.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "webrisk_proto_with_info",
    deps = [
        ":webrisk_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "webrisk_java_proto",
    deps = [":webrisk_proto"],
)

java_grpc_library(
    name = "webrisk_java_grpc",
    srcs = [":webrisk_proto"],
    deps = [":webrisk_java_proto"],
)

java_gapic_library(
    name = "webrisk_java_gapic",
    srcs = [":webrisk_proto_with_info"],
    gapic_yaml = "webrisk_gapic.yaml",
    grpc_service_config = "webrisk_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "webrisk_v1beta1.yaml",
    test_deps = [
        ":webrisk_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":webrisk_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "webrisk_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.webrisk.v1beta1.WebRiskServiceV1Beta1ClientHttpJsonTest",
        "com.google.cloud.webrisk.v1beta1.WebRiskServiceV1Beta1ClientTest",
    ],
    runtime_deps = [":webrisk_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-webrisk-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":webrisk_java_gapic",
        ":webrisk_java_grpc",
        ":webrisk_java_proto",
        ":webrisk_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "webrisk_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/webrisk/apiv1beta1/webriskpb",
    protos = [":webrisk_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "webrisk_go_gapic",
    srcs = [":webrisk_proto_with_info"],
    grpc_service_config = "webrisk_grpc_service_config.json",
    importpath = "cloud.google.com/go/webrisk/apiv1beta1;webrisk",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "webrisk_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":webrisk_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-webrisk-v1beta1-go",
    deps = [
        ":webrisk_go_gapic",
        ":webrisk_go_gapic_srcjar-metadata.srcjar",
        ":webrisk_go_gapic_srcjar-snippets.srcjar",
        ":webrisk_go_gapic_srcjar-test.srcjar",
        ":webrisk_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "webrisk_py_gapic",
    srcs = [":webrisk_proto"],
    grpc_service_config = "webrisk_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "webrisk_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "webrisk_py_gapic_test",
    srcs = [
        "webrisk_py_gapic_pytest.py",
        "webrisk_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":webrisk_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "webrisk-v1beta1-py",
    deps = [
        ":webrisk_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "webrisk_php_proto",
    deps = [":webrisk_proto"],
)

php_gapic_library(
    name = "webrisk_php_gapic",
    srcs = [":webrisk_proto_with_info"],
    grpc_service_config = "webrisk_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "webrisk_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":webrisk_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-webrisk-v1beta1-php",
    deps = [
        ":webrisk_php_gapic",
        ":webrisk_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "webrisk_nodejs_gapic",
    package_name = "@google-cloud/web-risk",
    src = ":webrisk_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "webrisk_grpc_service_config.json",
    package = "google.cloud.webrisk.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "webrisk_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "webrisk-v1beta1-nodejs",
    deps = [
        ":webrisk_nodejs_gapic",
        ":webrisk_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "webrisk_ruby_proto",
    deps = [":webrisk_proto"],
)

ruby_grpc_library(
    name = "webrisk_ruby_grpc",
    srcs = [":webrisk_proto"],
    deps = [":webrisk_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "webrisk_ruby_gapic",
    srcs = [":webrisk_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=webrisk.googleapis.com",
        "ruby-cloud-api-shortname=webrisk",
        "ruby-cloud-env-prefix=WEBRISK",
        "ruby-cloud-gem-name=google-cloud-web_risk-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/web-risk",
        "ruby-cloud-service-override=WebRiskServiceV1Beta1=WebRiskService",
    ],
    grpc_service_config = "webrisk_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Web Risk is an enterprise security product that lets your client applications check URLs against Google's constantly updated lists of unsafe web resources.",
    ruby_cloud_title = "Web Risk V1beta1",
    service_yaml = "webrisk_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":webrisk_ruby_grpc",
        ":webrisk_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-webrisk-v1beta1-ruby",
    deps = [
        ":webrisk_ruby_gapic",
        ":webrisk_ruby_grpc",
        ":webrisk_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "webrisk_csharp_proto",
    deps = [":webrisk_proto"],
)

csharp_grpc_library(
    name = "webrisk_csharp_grpc",
    srcs = [":webrisk_proto"],
    deps = [":webrisk_csharp_proto"],
)

csharp_gapic_library(
    name = "webrisk_csharp_gapic",
    srcs = [":webrisk_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "webrisk_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "webrisk_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":webrisk_csharp_grpc",
        ":webrisk_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-webrisk-v1beta1-csharp",
    deps = [
        ":webrisk_csharp_gapic",
        ":webrisk_csharp_grpc",
        ":webrisk_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "webrisk_cc_proto",
    deps = [":webrisk_proto"],
)

cc_grpc_library(
    name = "webrisk_cc_grpc",
    srcs = [":webrisk_proto"],
    grpc_only = True,
    deps = [":webrisk_cc_proto"],
)
