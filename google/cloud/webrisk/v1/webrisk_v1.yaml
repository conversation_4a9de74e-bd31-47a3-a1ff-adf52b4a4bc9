type: google.api.Service
config_version: 3
name: webrisk.googleapis.com
title: Web Risk API

apis:
- name: google.cloud.webrisk.v1.WebRiskService
- name: google.longrunning.Operations

types:
- name: google.cloud.webrisk.v1.Submission
- name: google.cloud.webrisk.v1.SubmitUriMetadata

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.webrisk.v1.WebRiskService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
