// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.logging;

import "google/protobuf/struct.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Retail.Logging";
option go_package = "cloud.google.com/go/retail/logging/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "ErrorLogProto";
option java_package = "com.google.cloud.retail.logging";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\Logging";
option ruby_package = "Google::Cloud::Retail::Logging";

// Describes a running service that sends errors.
message ServiceContext {
  // An identifier of the service.
  // For example, "retail.googleapis.com".
  string service = 1;
}

// HTTP request data that is related to a reported error.
message HttpRequestContext {
  // The HTTP response status code for the request.
  int32 response_status_code = 1;
}

// Indicates a location in the source code of the service for which
// errors are reported.
message SourceLocation {
  // Human-readable name of a function or method.
  // For example, "google.cloud.retail.v2.UserEventService.ImportUserEvents".
  string function_name = 1;
}

// A description of the context in which an error occurred.
message ErrorContext {
  // The HTTP request which was processed when the error was triggered.
  HttpRequestContext http_request = 1;

  // The location in the source code where the decision was made to
  // report the error, usually the place where it was logged.
  SourceLocation report_location = 2;
}

// The error payload that is populated on LRO import APIs, including
//   "google.cloud.retail.v2.ProductService.ImportProducts" and
//   "google.cloud.retail.v2.EventService.ImportUserEvents".
message ImportErrorContext {
  // The operation resource name of the LRO.
  string operation_name = 1;

  // Cloud Storage file path of the import source.
  // Can be set for batch operation error.
  string gcs_path = 2;

  // Line number of the content in file.
  // Should be empty for permission or batch operation error.
  string line_number = 3;

  // Detailed content which caused the error.
  // Should be empty for permission or batch operation error.
  oneof line_content {
    // The detailed content which caused the error on importing a catalog item.
    string catalog_item = 4;

    // The detailed content which caused the error on importing a product.
    string product = 5;

    // The detailed content which caused the error on importing a user event.
    string user_event = 6;
  }
}

// An error log which is reported to the Error Reporting system.
// This proto a superset of
// google.devtools.clouderrorreporting.v1beta1.ReportedErrorEvent.
message ErrorLog {
  // The service context in which this error has occurred.
  ServiceContext service_context = 1;

  // A description of the context in which the error occurred.
  ErrorContext context = 2;

  // A message describing the error.
  string message = 3;

  // The RPC status associated with the error log.
  google.rpc.Status status = 4;

  // The API request payload, represented as a protocol buffer.
  //
  // Most API request types are supported. For example:
  //
  //   "type.googleapis.com/google.cloud.retail.v2.ProductService.CreateProductRequest"
  //   "type.googleapis.com/google.cloud.retail.v2.UserEventService.WriteUserEventRequest"
  google.protobuf.Struct request_payload = 5;

  // The API response payload, represented as a protocol buffer.
  //
  // This is used to log some "soft errors", where the response is valid but we
  // consider there are some quality issues like unjoined events.
  //
  // The following API responses are supported and no PII is included:
  //   "google.cloud.retail.v2.PredictionService.Predict"
  //   "google.cloud.retail.v2.UserEventService.WriteUserEvent"
  //   "google.cloud.retail.v2.UserEventService.CollectUserEvent"
  google.protobuf.Struct response_payload = 6;

  // The error payload that is populated on LRO import APIs.
  ImportErrorContext import_payload = 7;
}
