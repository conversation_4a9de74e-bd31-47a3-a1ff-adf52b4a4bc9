// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2;

option csharp_namespace = "Google.Cloud.Retail.V2";
option go_package = "cloud.google.com/go/retail/apiv2/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "PromotionProto";
option java_package = "com.google.cloud.retail.v2";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2";
option ruby_package = "Google::Cloud::Retail::V2";

// Promotion information.
message Promotion {
  // ID of the promotion. For example, "free gift".
  //
  // The value must be a UTF-8 encoded string with a length limit of 128
  // characters, and match the pattern: `[a-zA-Z][a-zA-Z0-9_]*`. For example,
  // id0LikeThis or ID_1_LIKE_THIS. Otherwise, an INVALID_ARGUMENT error is
  // returned.
  //
  // Corresponds to Google Merchant Center property
  // [promotion_id](https://support.google.com/merchants/answer/7050148).
  string promotion_id = 1;
}
