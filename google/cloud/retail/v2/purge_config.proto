// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Retail.V2";
option go_package = "cloud.google.com/go/retail/apiv2/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "PurgeConfigProto";
option java_package = "com.google.cloud.retail.v2";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2";
option ruby_package = "Google::Cloud::Retail::V2";

// Metadata related to the progress of the Purge operation.
// This will be returned by the google.longrunning.Operation.metadata field.
message PurgeMetadata {}

// Metadata related to the progress of the PurgeProducts operation.
// This will be returned by the google.longrunning.Operation.metadata field.
message PurgeProductsMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;

  // Count of entries that were deleted successfully.
  int64 success_count = 3;

  // Count of entries that encountered errors while processing.
  int64 failure_count = 4;
}

// Request message for PurgeProducts method.
message PurgeProductsRequest {
  // Required. The resource name of the branch under which the products are
  // created. The format is
  // `projects/${projectId}/locations/global/catalogs/${catalogId}/branches/${branchId}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Branch" }
  ];

  // Required. The filter string to specify the products to be deleted with a
  // length limit of 5,000 characters.
  //
  // Empty string filter is not allowed. "*" implies delete all items in a
  // branch.
  //
  // The eligible fields for filtering are:
  //
  // * `availability`: Double quoted
  // [Product.availability][google.cloud.retail.v2.Product.availability] string.
  // * `create_time` : in ISO 8601 "zulu" format.
  //
  // Supported syntax:
  //
  // * Comparators (">", "<", ">=", "<=", "=").
  //   Examples:
  //   * create_time <= "2015-02-13T17:05:46Z"
  //   * availability = "IN_STOCK"
  //
  // * Conjunctions ("AND")
  //   Examples:
  //   * create_time <= "2015-02-13T17:05:46Z" AND availability = "PREORDER"
  //
  // * Disjunctions ("OR")
  //   Examples:
  //   * create_time <= "2015-02-13T17:05:46Z" OR availability = "IN_STOCK"
  //
  // * Can support nested queries.
  //   Examples:
  //   * (create_time <= "2015-02-13T17:05:46Z" AND availability = "PREORDER")
  //   OR (create_time >= "2015-02-14T13:03:32Z" AND availability = "IN_STOCK")
  //
  // * Filter Limits:
  //   * Filter should not contain more than 6 conditions.
  //   * Max nesting depth should not exceed 2 levels.
  //
  // Examples queries:
  // * Delete back order products created before a timestamp.
  //   create_time <= "2015-02-13T17:05:46Z" OR availability = "BACKORDER"
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // Actually perform the purge.
  // If `force` is set to false, the method will return the expected purge count
  // without deleting any products.
  bool force = 3;
}

// Response of the PurgeProductsRequest. If the long running operation is
// successfully done, then this message is returned by the
// google.longrunning.Operations.response field.
message PurgeProductsResponse {
  // The total count of products purged as a result of the operation.
  int64 purge_count = 1;

  // A sample of the product names that will be deleted.
  // Only populated if `force` is set to false. A max of 100 names will be
  // returned and the names are chosen at random.
  repeated string purge_sample = 2 [
    (google.api.resource_reference) = { type: "retail.googleapis.com/Product" }
  ];
}

// Request message for PurgeUserEvents method.
message PurgeUserEventsRequest {
  // Required. The resource name of the catalog under which the events are
  // created. The format is
  // `projects/${projectId}/locations/global/catalogs/${catalogId}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // Required. The filter string to specify the events to be deleted with a
  // length limit of 5,000 characters. Empty string filter is not allowed. The
  // eligible fields for filtering are:
  //
  // * `eventType`: Double quoted
  // [UserEvent.event_type][google.cloud.retail.v2.UserEvent.event_type] string.
  // * `eventTime`: in ISO 8601 "zulu" format.
  // * `visitorId`: Double quoted string. Specifying this will delete all
  //   events associated with a visitor.
  // * `userId`: Double quoted string. Specifying this will delete all events
  //   associated with a user.
  //
  // Examples:
  //
  // * Deleting all events in a time range:
  //   `eventTime > "2012-04-23T18:25:43.511Z"
  //   eventTime < "2012-04-23T18:30:43.511Z"`
  // * Deleting specific eventType in time range:
  //   `eventTime > "2012-04-23T18:25:43.511Z" eventType = "detail-page-view"`
  // * Deleting all events for a specific visitor:
  //   `visitorId = "visitor1024"`
  //
  // The filtering fields are assumed to have an implicit AND.
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // Actually perform the purge.
  // If `force` is set to false, the method will return the expected purge count
  // without deleting any user events.
  bool force = 3;
}

// Response of the PurgeUserEventsRequest. If the long running operation is
// successfully done, then this message is returned by the
// google.longrunning.Operations.response field.
message PurgeUserEventsResponse {
  // The total count of events purged as a result of the operation.
  int64 purged_events_count = 1;
}
