{"methodConfig": [{"name": [{"service": "google.cloud.retail.v2.CatalogService"}, {"service": "google.cloud.retail.v2.CompletionService"}, {"service": "google.cloud.retail.v2.PredictionService"}, {"service": "google.cloud.retail.v2.SearchService"}], "timeout": "5s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "5s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2.UserEventService"}], "timeout": "10s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "5s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2.ProductService"}, {"service": "google.cloud.retail.v2.UserEventService", "method": "PurgeUserEvents"}], "timeout": "30s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2.AnalyticsService", "method": "ExportAnalyticsMetrics"}, {"service": "google.cloud.retail.v2.ModelService"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.longrunning.Operations", "method": "ListOperations"}, {"service": "google.cloud.retail.v2.ProductService", "method": "ImportProducts"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "300s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2.UserEventService", "method": "ImportUserEvents"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "300s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}