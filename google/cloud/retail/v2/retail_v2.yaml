type: google.api.Service
config_version: 3
name: retail.googleapis.com
title: Vertex AI Search for Retail API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.retail.v2.AnalyticsService
- name: google.cloud.retail.v2.CatalogService
- name: google.cloud.retail.v2.CompletionService
- name: google.cloud.retail.v2.ControlService
- name: google.cloud.retail.v2.GenerativeQuestionService
- name: google.cloud.retail.v2.ModelService
- name: google.cloud.retail.v2.PredictionService
- name: google.cloud.retail.v2.ProductService
- name: google.cloud.retail.v2.SearchService
- name: google.cloud.retail.v2.ServingConfigService
- name: google.cloud.retail.v2.UserEventService
- name: google.longrunning.Operations

types:
- name: google.cloud.retail.logging.ErrorLog
- name: google.cloud.retail.v2.AddFulfillmentPlacesMetadata
- name: google.cloud.retail.v2.AddFulfillmentPlacesResponse
- name: google.cloud.retail.v2.AddLocalInventoriesMetadata
- name: google.cloud.retail.v2.AddLocalInventoriesResponse
- name: google.cloud.retail.v2.CreateModelMetadata
- name: google.cloud.retail.v2.ExportAnalyticsMetricsResponse
- name: google.cloud.retail.v2.ExportMetadata
- name: google.cloud.retail.v2.ImportCompletionDataResponse
- name: google.cloud.retail.v2.ImportErrorsConfig
- name: google.cloud.retail.v2.ImportMetadata
- name: google.cloud.retail.v2.ImportProductsResponse
- name: google.cloud.retail.v2.ImportUserEventsResponse
- name: google.cloud.retail.v2.Model
- name: google.cloud.retail.v2.PurgeMetadata
- name: google.cloud.retail.v2.PurgeProductsMetadata
- name: google.cloud.retail.v2.PurgeProductsResponse
- name: google.cloud.retail.v2.PurgeUserEventsResponse
- name: google.cloud.retail.v2.RejoinUserEventsMetadata
- name: google.cloud.retail.v2.RejoinUserEventsResponse
- name: google.cloud.retail.v2.RemoveFulfillmentPlacesMetadata
- name: google.cloud.retail.v2.RemoveFulfillmentPlacesResponse
- name: google.cloud.retail.v2.RemoveLocalInventoriesMetadata
- name: google.cloud.retail.v2.RemoveLocalInventoriesResponse
- name: google.cloud.retail.v2.SetInventoryMetadata
- name: google.cloud.retail.v2.SetInventoryResponse
- name: google.cloud.retail.v2.TuneModelMetadata
- name: google.cloud.retail.v2.TuneModelResponse

documentation:
  summary: |-
    Vertex AI Search for Retail API is made up of Retail Search, Browse and
    Recommendations. These discovery AI solutions help you implement
    personalized search, browse and recommendations, based on machine learning
    models, across your websites and mobile applications.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2/{name=projects/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/v2/{name=projects/*/locations/*/catalogs/*/branches/*/operations/*}'
    - get: '/v2/{name=projects/*/locations/*/catalogs/*/operations/*}'
    - get: '/v2/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2/{name=projects/*/locations/*}/operations'
    additional_bindings:
    - get: '/v2/{name=projects/*/locations/*/catalogs/*}/operations'
    - get: '/v2/{name=projects/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2.AnalyticsService.ExportAnalyticsMetrics
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2.CatalogService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2.CompletionService.CompleteQuery
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2.CompletionService.ImportCompletionData
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2.ControlService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2.GenerativeQuestionService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2.ModelService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2.PredictionService.Predict
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2.ProductService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2.SearchService.Search
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2.ServingConfigService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2.UserEventService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.ListOperations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
