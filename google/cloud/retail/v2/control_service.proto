// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2/control.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Retail.V2";
option go_package = "cloud.google.com/go/retail/apiv2/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ControlServiceProto";
option java_package = "com.google.cloud.retail.v2";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2";
option ruby_package = "Google::Cloud::Retail::V2";

// Service for modifying Control.
service ControlService {
  option (google.api.default_host) = "retail.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a Control.
  //
  // If the [Control][google.cloud.retail.v2.Control] to create already exists,
  // an ALREADY_EXISTS error is returned.
  rpc CreateControl(CreateControlRequest) returns (Control) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*/catalogs/*}/controls"
      body: "control"
    };
    option (google.api.method_signature) = "parent,control,control_id";
  }

  // Deletes a Control.
  //
  // If the [Control][google.cloud.retail.v2.Control] to delete does not exist,
  // a NOT_FOUND error is returned.
  rpc DeleteControl(DeleteControlRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/catalogs/*/controls/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a Control.
  //
  // [Control][google.cloud.retail.v2.Control] cannot be set to a different
  // oneof field, if so an INVALID_ARGUMENT is returned. If the
  // [Control][google.cloud.retail.v2.Control] to update does not exist, a
  // NOT_FOUND error is returned.
  rpc UpdateControl(UpdateControlRequest) returns (Control) {
    option (google.api.http) = {
      patch: "/v2/{control.name=projects/*/locations/*/catalogs/*/controls/*}"
      body: "control"
    };
    option (google.api.method_signature) = "control,update_mask";
  }

  // Gets a Control.
  rpc GetControl(GetControlRequest) returns (Control) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/catalogs/*/controls/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all Controls by their parent
  // [Catalog][google.cloud.retail.v2.Catalog].
  rpc ListControls(ListControlsRequest) returns (ListControlsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*/catalogs/*}/controls"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request for CreateControl method.
message CreateControlRequest {
  // Required. Full resource name of parent catalog. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // Required. The Control to create.
  Control control = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the Control, which will become the final
  // component of the Control's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][0-9]-_/.
  string control_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for UpdateControl method.
message UpdateControlRequest {
  // Required. The Control to update.
  Control control = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates which fields in the provided
  // [Control][google.cloud.retail.v2.Control] to update. The following are NOT
  // supported:
  //
  // * [Control.name][google.cloud.retail.v2.Control.name]
  //
  // If not set or empty, all supported fields are updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request for DeleteControl method.
message DeleteControlRequest {
  // Required. The resource name of the Control to delete. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/controls/{control_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Control" }
  ];
}

// Request for GetControl method.
message GetControlRequest {
  // Required. The resource name of the Control to get. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/controls/{control_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Control" }
  ];
}

// Request for ListControls method.
message ListControlsRequest {
  // Required. The catalog resource name. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // Optional. Maximum number of results to return. If unspecified, defaults
  // to 50. Max allowed value is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListControls` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter to apply on the list results. Supported features:
  //
  // * List all the products under the parent branch if
  // [filter][google.cloud.retail.v2.ListControlsRequest.filter] is unset.
  // * List controls that are used in a single ServingConfig:
  //     'serving_config = "boosted_home_page_cvr"'
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response for ListControls method.
message ListControlsResponse {
  // All the Controls for a given catalog.
  repeated Control controls = 1;

  // Pagination token, if not returned indicates the last page.
  string next_page_token = 2;
}
