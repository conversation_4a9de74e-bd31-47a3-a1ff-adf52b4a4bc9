// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2/model.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Retail.V2";
option go_package = "cloud.google.com/go/retail/apiv2/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ModelServiceProto";
option java_package = "com.google.cloud.retail.v2";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2";
option ruby_package = "Google::Cloud::Retail::V2";

// Service for performing CRUD operations on models.
// Recommendation models contain all the metadata necessary to generate a set of
// models for the `Predict()` API. A model is queried
// indirectly via a ServingConfig, which associates a model with a
// given Placement (e.g. Frequently Bought Together on Home Page).
//
// This service allows you to do the following:
//
// * Initiate training of a model.
// * Pause training of an existing model.
// * List all the available models along with their metadata.
// * Control their tuning schedule.
service ModelService {
  option (google.api.default_host) = "retail.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new model.
  rpc CreateModel(CreateModelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*/catalogs/*}/models"
      body: "model"
    };
    option (google.api.method_signature) = "parent,model";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.retail.v2.Model"
      metadata_type: "google.cloud.retail.v2.CreateModelMetadata"
    };
  }

  // Gets a model.
  rpc GetModel(GetModelRequest) returns (Model) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/catalogs/*/models/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Pauses the training of an existing model.
  rpc PauseModel(PauseModelRequest) returns (Model) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/catalogs/*/models/*}:pause"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Resumes the training of an existing model.
  rpc ResumeModel(ResumeModelRequest) returns (Model) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/catalogs/*/models/*}:resume"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes an existing model.
  rpc DeleteModel(DeleteModelRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/catalogs/*/models/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all the models linked to this event store.
  rpc ListModels(ListModelsRequest) returns (ListModelsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*/catalogs/*}/models"
    };
    option (google.api.method_signature) = "parent";
  }

  // Update of model metadata. Only fields that
  // currently can be updated are: `filtering_option` and
  // `periodic_tuning_state`.
  // If other values are provided, this API method ignores them.
  rpc UpdateModel(UpdateModelRequest) returns (Model) {
    option (google.api.http) = {
      patch: "/v2/{model.name=projects/*/locations/*/catalogs/*/models/*}"
      body: "model"
    };
    option (google.api.method_signature) = "model,update_mask";
  }

  // Tunes an existing model.
  rpc TuneModel(TuneModelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/catalogs/*/models/*}:tune"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.retail.v2.TuneModelResponse"
      metadata_type: "google.cloud.retail.v2.TuneModelMetadata"
    };
  }
}

// Request for creating a model.
message CreateModelRequest {
  // Required. The parent resource under which to create the model. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // Required. The payload of the [Model][google.cloud.retail.v2.Model]  to
  // create.
  Model model = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Whether to run a dry run to validate the request (without
  // actually creating the model).
  bool dry_run = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request for updating an existing model.
message UpdateModelRequest {
  // Required. The body of the updated [Model][google.cloud.retail.v2.Model].
  Model model = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Indicates which fields in the provided 'model' to
  // update. If not set, by default updates all fields.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request for getting a model.
message GetModelRequest {
  // Required. The resource name of the [Model][google.cloud.retail.v2.Model] to
  // get. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog}/models/{model_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Model" }
  ];
}

// Request for pausing training of a model.
message PauseModelRequest {
  // Required. The name of the model to pause.
  // Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Model" }
  ];
}

// Request for resuming training of a model.
message ResumeModelRequest {
  // Required. The name of the model to resume.
  // Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for listing models associated with a resource.
message ListModelsRequest {
  // Required. The parent for which to list models.
  // Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // Optional. Maximum number of results to return. If unspecified, defaults
  // to 50. Max allowed value is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListModels`
  // call. Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request for deleting a model.
message DeleteModelRequest {
  // Required. The resource name of the [Model][google.cloud.retail.v2.Model] to
  // delete. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Model" }
  ];
}

// Response to a ListModelRequest.
message ListModelsResponse {
  // List of Models.
  repeated Model models = 1;

  // Pagination token, if not returned indicates the last page.
  string next_page_token = 2;
}

// Request to manually start a tuning process now (instead of waiting for
// the periodically scheduled tuning to happen).
message TuneModelRequest {
  // Required. The resource name of the model to tune.
  // Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Model" }
  ];
}

// Metadata associated with a create operation.
message CreateModelMetadata {
  // The resource name of the model that this create applies to.
  // Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`
  string model = 1;
}

// Metadata associated with a tune operation.
message TuneModelMetadata {
  // The resource name of the model that this tune applies to.
  // Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`
  string model = 1;
}

// Response associated with a tune operation.
message TuneModelResponse {}
