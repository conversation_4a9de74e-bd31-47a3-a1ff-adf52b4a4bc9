// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Retail.V2";
option go_package = "cloud.google.com/go/retail/apiv2/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "GenerativeQuestionProto";
option java_package = "com.google.cloud.retail.v2";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2";
option ruby_package = "Google::Cloud::Retail::V2";

// Configuration for overall generative question feature state.
message GenerativeQuestionsFeatureConfig {
  // Required. Resource name of the affected catalog.
  // Format: projects/{project}/locations/{location}/catalogs/{catalog}
  string catalog = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Determines whether questions will be used at serving time.
  // Note: This feature cannot be enabled until initial data requirements are
  // satisfied.
  bool feature_enabled = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Minimum number of products in the response to trigger follow-up
  // questions. Value must be 0 or positive.
  int32 minimum_products = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for a single generated question.
message GenerativeQuestionConfig {
  // Required. Resource name of the catalog.
  // Format: projects/{project}/locations/{location}/catalogs/{catalog}
  string catalog = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The facet to which the question is associated.
  string facet = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. The LLM generated question.
  string generated_question = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The question that will be used at serving time.
  // Question can have a max length of 300 bytes.
  // When not populated, generated_question should be used.
  string final_question = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Values that can be used to answer the question.
  repeated string example_values = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ratio of how often a question was asked.
  float frequency = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Whether the question is asked at serving time.
  bool allowed_in_conversation = 7 [(google.api.field_behavior) = OPTIONAL];
}
