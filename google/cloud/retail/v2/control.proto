// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2/common.proto";

option csharp_namespace = "Google.Cloud.Retail.V2";
option go_package = "cloud.google.com/go/retail/apiv2/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ControlProto";
option java_package = "com.google.cloud.retail.v2";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2";
option ruby_package = "Google::Cloud::Retail::V2";

// Configures dynamic metadata that can be linked to a
// [ServingConfig][google.cloud.retail.v2.ServingConfig] and affect search or
// recommendation results at serving time.
message Control {
  option (google.api.resource) = {
    type: "retail.googleapis.com/Control"
    pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/controls/{control}"
  };

  // The behavior/type of the control
  //
  // A behavior/type must be specified on creation. Type cannot be changed once
  // specified (e.g. A Rule control will always be a Rule control.). An
  // INVALID_ARGUMENT will be returned if either condition is violated.
  oneof control {
    // A rule control - a condition-action pair.
    // Enacts a set action when the condition is triggered.
    // For example: Boost "gShoe" when query full matches "Running Shoes".
    Rule rule = 4;
  }

  // Immutable. Fully qualified name
  // `projects/*/locations/global/catalogs/*/controls/*`
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Required. The human readable control display name. Used in Retail UI.
  //
  // This field must be a UTF-8 encoded string with a length limit of 128
  // characters. Otherwise, an INVALID_ARGUMENT error is thrown.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. List of [serving config][google.cloud.retail.v2.ServingConfig]
  // ids that are associated with this control in the same
  // [Catalog][google.cloud.retail.v2.Catalog].
  //
  // Note the association is managed via the
  // [ServingConfig][google.cloud.retail.v2.ServingConfig], this is an output
  // only denormalized view.
  repeated string associated_serving_config_ids = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. The solution types that the control is used for.
  // Currently we support setting only one type of solution at creation time.
  //
  // Only `SOLUTION_TYPE_SEARCH` value is supported at the moment.
  // If no solution type is provided at creation time, will default to
  // [SOLUTION_TYPE_SEARCH][google.cloud.retail.v2.SolutionType.SOLUTION_TYPE_SEARCH].
  repeated SolutionType solution_types = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Specifies the use case for the control.
  // Affects what condition fields can be set.
  // Only settable by search controls.
  // Will default to
  // [SEARCH_SOLUTION_USE_CASE_SEARCH][google.cloud.retail.v2.SearchSolutionUseCase.SEARCH_SOLUTION_USE_CASE_SEARCH]
  // if not specified. Currently only allow one search_solution_use_case per
  // control.
  repeated SearchSolutionUseCase search_solution_use_case = 7;
}
