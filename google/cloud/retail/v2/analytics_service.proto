// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2/export_config.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.Retail.V2";
option go_package = "cloud.google.com/go/retail/apiv2/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "AnalyticsServiceProto";
option java_package = "com.google.cloud.retail.v2";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2";
option ruby_package = "Google::Cloud::Retail::V2";

// Service for managing & accessing retail search business metric.
// Retail recommendation business metric is currently not available.
service AnalyticsService {
  option (google.api.default_host) = "retail.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Exports analytics metrics.
  //
  // `Operation.response` is of type `ExportAnalyticsMetricsResponse`.
  // `Operation.metadata` is of type `ExportMetadata`.
  rpc ExportAnalyticsMetrics(ExportAnalyticsMetricsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{catalog=projects/*/locations/*/catalogs/*}:exportAnalyticsMetrics"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.retail.v2.ExportAnalyticsMetricsResponse"
      metadata_type: "google.cloud.retail.v2.ExportMetadata"
    };
  }
}
