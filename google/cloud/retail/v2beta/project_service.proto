// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2beta/project.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Beta";
option go_package = "cloud.google.com/go/retail/apiv2beta/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ProjectServiceProto";
option java_package = "com.google.cloud.retail.v2beta";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2beta";
option ruby_package = "Google::Cloud::Retail::V2beta";

// Service for settings at Project level.
service ProjectService {
  option (google.api.default_host) = "retail.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Get the [AlertConfig][google.cloud.retail.v2beta.AlertConfig] of the
  // requested project.
  rpc GetAlertConfig(GetAlertConfigRequest) returns (AlertConfig) {
    option (google.api.http) = {
      get: "/v2beta/{name=projects/*/alertConfig}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update the alert config of the requested project.
  rpc UpdateAlertConfig(UpdateAlertConfigRequest) returns (AlertConfig) {
    option (google.api.http) = {
      patch: "/v2beta/{alert_config.name=projects/*/alertConfig}"
      body: "alert_config"
    };
    option (google.api.method_signature) = "alert_config,update_mask";
  }
}

// Request for
// [ProjectService.GetAlertConfig][google.cloud.retail.v2beta.ProjectService.GetAlertConfig]
// method.
message GetAlertConfigRequest {
  // Required. Full AlertConfig resource name. Format:
  // projects/{project_number}/alertConfig
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "retail.googleapis.com/AlertConfig"
    }
  ];
}

// Request for
// [ProjectService.UpdateAlertConfig][google.cloud.retail.v2beta.ProjectService.UpdateAlertConfig]
// method.
message UpdateAlertConfigRequest {
  // Required. The [AlertConfig][google.cloud.retail.v2beta.AlertConfig] to
  // update.
  //
  // If the caller does not have permission to update the
  // [AlertConfig][google.cloud.retail.v2beta.AlertConfig], then a
  // PERMISSION_DENIED error is returned.
  //
  // If the [AlertConfig][google.cloud.retail.v2beta.AlertConfig] to update does
  // not exist, a NOT_FOUND error is returned.
  AlertConfig alert_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates which fields in the provided
  // [AlertConfig][google.cloud.retail.v2beta.AlertConfig] to update. If not
  // set, all supported fields are updated.
  google.protobuf.FieldMask update_mask = 2;
}
