// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/httpbody.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2beta/export_config.proto";
import "google/cloud/retail/v2beta/import_config.proto";
import "google/cloud/retail/v2beta/purge_config.proto";
import "google/cloud/retail/v2beta/user_event.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Beta";
option go_package = "cloud.google.com/go/retail/apiv2beta/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "UserEventServiceProto";
option java_package = "com.google.cloud.retail.v2beta";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2beta";
option ruby_package = "Google::Cloud::Retail::V2beta";

// Service for ingesting end user actions on the customer website.
service UserEventService {
  option (google.api.default_host) = "retail.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Writes a single user event.
  rpc WriteUserEvent(WriteUserEventRequest) returns (UserEvent) {
    option (google.api.http) = {
      post: "/v2beta/{parent=projects/*/locations/*/catalogs/*}/userEvents:write"
      body: "user_event"
    };
  }

  // Writes a single user event from the browser. This uses a GET request to
  // due to browser restriction of POST-ing to a 3rd party domain.
  //
  // This method is used only by the Retail API JavaScript pixel and Google Tag
  // Manager. Users should not call this method directly.
  rpc CollectUserEvent(CollectUserEventRequest) returns (google.api.HttpBody) {
    option (google.api.http) = {
      get: "/v2beta/{parent=projects/*/locations/*/catalogs/*}/userEvents:collect"
    };
  }

  // Deletes permanently all user events specified by the filter provided.
  // Depending on the number of events specified by the filter, this operation
  // could take hours or days to complete. To test a filter, use the list
  // command first.
  rpc PurgeUserEvents(PurgeUserEventsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta/{parent=projects/*/locations/*/catalogs/*}/userEvents:purge"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.retail.v2beta.PurgeUserEventsResponse"
      metadata_type: "google.cloud.retail.v2beta.PurgeMetadata"
    };
  }

  // Bulk import of User events. Request processing might be
  // synchronous. Events that already exist are skipped.
  // Use this method for backfilling historical user events.
  //
  // `Operation.response` is of type `ImportResponse`. Note that it is
  // possible for a subset of the items to be successfully inserted.
  // `Operation.metadata` is of type `ImportMetadata`.
  rpc ImportUserEvents(ImportUserEventsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta/{parent=projects/*/locations/*/catalogs/*}/userEvents:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.retail.v2beta.ImportUserEventsResponse"
      metadata_type: "google.cloud.retail.v2beta.ImportMetadata"
    };
  }

  // Exports user events.
  //
  // `Operation.response` is of type `ExportResponse`.
  // `Operation.metadata` is of type `ExportMetadata`.
  rpc ExportUserEvents(ExportUserEventsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta/{parent=projects/*/locations/*/catalogs/*}/userEvents:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.retail.v2beta.ExportUserEventsResponse"
      metadata_type: "google.cloud.retail.v2beta.ExportMetadata"
    };
  }

  // Starts a user-event rejoin operation with latest product catalog. Events
  // are not annotated with detailed product information for products that are
  // missing from the catalog when the user event is ingested. These
  // events are stored as unjoined events with limited usage on training and
  // serving. You can use this method to start a join operation on specified
  // events with the latest version of product catalog. You can also use this
  // method to correct events joined with the wrong product catalog. A rejoin
  // operation can take hours or days to complete.
  rpc RejoinUserEvents(RejoinUserEventsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2beta/{parent=projects/*/locations/*/catalogs/*}/userEvents:rejoin"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "RejoinUserEventsResponse"
      metadata_type: "RejoinUserEventsMetadata"
    };
  }
}

// Request message for WriteUserEvent method.
message WriteUserEventRequest {
  // Required. The parent catalog resource name, such as
  // `projects/1234/locations/global/catalogs/default_catalog`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. User event to write.
  UserEvent user_event = 2 [(google.api.field_behavior) = REQUIRED];

  // If set to true, the user event will be written asynchronously after
  // validation, and the API will respond without waiting for the write.
  // Therefore, silent failures can occur even if the API returns success. In
  // case of silent failures, error messages can be found in Stackdriver logs.
  bool write_async = 3;
}

// Request message for CollectUserEvent method.
message CollectUserEventRequest {
  // The rule that can convert the raw_json to a user event. It is needed
  // only when the raw_json is set.
  oneof conversion_rule {
    // The prebuilt rule name that can convert a specific type of raw_json.
    // For example: "ga4_bq" rule for the GA4 user event schema.
    string prebuilt_rule = 6;
  }

  // Required. The parent catalog name, such as
  // `projects/1234/locations/global/catalogs/default_catalog`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. URL encoded UserEvent proto with a length limit of 2,000,000
  // characters.
  string user_event = 2 [(google.api.field_behavior) = REQUIRED];

  // The URL including cgi-parameters but excluding the hash fragment with a
  // length limit of 5,000 characters. This is often more useful than the
  // referer URL, because many browsers only send the domain for 3rd party
  // requests.
  string uri = 3;

  // The event timestamp in milliseconds. This prevents browser caching of
  // otherwise identical get requests. The name is abbreviated to reduce the
  // payload bytes.
  int64 ets = 4;

  // An arbitrary serialized JSON string that contains necessary information
  // that can comprise a user event. When this field is specified, the
  // user_event field will be ignored. Note: line-delimited JSON is not
  // supported, a single JSON only.
  string raw_json = 5;
}

// Request message for RejoinUserEvents method.
message RejoinUserEventsRequest {
  // The scope of user events to be rejoined with the latest product catalog.
  // If the rejoining aims at reducing number of unjoined events, set
  // `UserEventRejoinScope` to `UNJOINED_EVENTS`.
  // If the rejoining aims at correcting product catalog information in joined
  // events, set `UserEventRejoinScope` to `JOINED_EVENTS`.
  // If all events needs to be rejoined, set `UserEventRejoinScope` to
  // `USER_EVENT_REJOIN_SCOPE_UNSPECIFIED`.
  enum UserEventRejoinScope {
    // Rejoin all events with the latest product catalog, including both joined
    // events and unjoined events.
    USER_EVENT_REJOIN_SCOPE_UNSPECIFIED = 0;

    // Only rejoin joined events with the latest product catalog.
    JOINED_EVENTS = 1;

    // Only rejoin unjoined events with the latest product catalog.
    UNJOINED_EVENTS = 2;
  }

  // Required. The parent catalog resource name, such as
  // `projects/1234/locations/global/catalogs/default_catalog`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // The type of the user event rejoin to define the scope and range of the user
  // events to be rejoined with the latest product catalog. Defaults to
  // `USER_EVENT_REJOIN_SCOPE_UNSPECIFIED` if this field is not set, or set to
  // an invalid integer value.
  UserEventRejoinScope user_event_rejoin_scope = 2;
}

// Response message for `RejoinUserEvents` method.
message RejoinUserEventsResponse {
  // Number of user events that were joined with latest product catalog.
  int64 rejoined_user_events_count = 1;
}

// Metadata for `RejoinUserEvents` method.
message RejoinUserEventsMetadata {}
