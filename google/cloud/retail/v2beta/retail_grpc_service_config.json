{"methodConfig": [{"name": [{"service": "google.cloud.retail.v2beta.CatalogService"}, {"service": "google.cloud.retail.v2beta.CompletionService"}, {"service": "google.cloud.retail.v2beta.PredictionService"}, {"service": "google.cloud.retail.v2beta.SearchService"}], "timeout": "5s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "5s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2beta.UserEventService"}], "timeout": "10s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "5s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2beta.ProductService"}, {"service": "google.cloud.retail.v2beta.UserEventService", "method": "PurgeUserEvents"}], "timeout": "30s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2beta.AnalyticsService", "method": "ExportAnalyticsMetrics"}, {"service": "google.cloud.retail.v2beta.ModelService"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.longrunning.Operations", "method": "ListOperations"}, {"service": "google.cloud.retail.v2beta.ProductService", "method": "ImportProducts"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "300s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2beta.UserEventService", "method": "ImportUserEvents"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "300s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}