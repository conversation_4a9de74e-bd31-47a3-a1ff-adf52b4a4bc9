// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2beta/common.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Beta";
option go_package = "cloud.google.com/go/retail/apiv2beta/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ModelProto";
option java_package = "com.google.cloud.retail.v2beta";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2beta";
option ruby_package = "Google::Cloud::Retail::V2beta";

// Metadata that describes the training and serving parameters of a
// [Model][google.cloud.retail.v2beta.Model]. A
// [Model][google.cloud.retail.v2beta.Model] can be associated with a
// [ServingConfig][google.cloud.retail.v2beta.ServingConfig] and then queried
// through the Predict API.
message Model {
  option (google.api.resource) = {
    type: "retail.googleapis.com/Model"
    pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/models/{model}"
  };

  // Represents an ordered combination of valid serving configs, which
  // can be used for `PAGE_OPTIMIZATION` recommendations.
  message ServingConfigList {
    // Optional. A set of valid serving configs that may be used for
    // `PAGE_OPTIMIZATION`.
    repeated string serving_config_ids = 1
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Additional configs for the frequently-bought-together model type.
  message FrequentlyBoughtTogetherFeaturesConfig {
    // Optional. Specifies the context of the model when it is used in predict
    // requests. Can only be set for the `frequently-bought-together` type. If
    // it isn't specified, it defaults to
    // [MULTIPLE_CONTEXT_PRODUCTS][google.cloud.retail.v2beta.Model.ContextProductsType.MULTIPLE_CONTEXT_PRODUCTS].
    ContextProductsType context_products_type = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Additional model features config.
  message ModelFeaturesConfig {
    oneof type_dedicated_config {
      // Additional configs for frequently-bought-together models.
      FrequentlyBoughtTogetherFeaturesConfig frequently_bought_together_config =
          1;
    }
  }

  // The serving state of the model.
  enum ServingState {
    // Unspecified serving state.
    SERVING_STATE_UNSPECIFIED = 0;

    // The model is not serving.
    INACTIVE = 1;

    // The model is serving and can be queried.
    ACTIVE = 2;

    // The model is trained on tuned hyperparameters and can be
    // queried.
    TUNED = 3;
  }

  // The training state of the model.
  enum TrainingState {
    // Unspecified training state.
    TRAINING_STATE_UNSPECIFIED = 0;

    // The model training is paused.
    PAUSED = 1;

    // The model is training.
    TRAINING = 2;
  }

  // Describes whether periodic tuning is enabled for this model
  // or not. Periodic tuning is scheduled at most every three months. You can
  // start a tuning process manually by using the `TuneModel`
  // method, which starts a tuning process immediately and resets the quarterly
  // schedule. Enabling or disabling periodic tuning does not affect any
  // current tuning processes.
  enum PeriodicTuningState {
    // Unspecified default value, should never be explicitly set.
    PERIODIC_TUNING_STATE_UNSPECIFIED = 0;

    // The model has periodic tuning disabled. Tuning
    // can be reenabled by calling the `EnableModelPeriodicTuning`
    // method or by calling the `TuneModel` method.
    PERIODIC_TUNING_DISABLED = 1;

    // The model cannot be tuned with periodic tuning OR the
    // `TuneModel` method. Hide the options in customer UI and
    // reject any requests through the backend self serve API.
    ALL_TUNING_DISABLED = 3;

    // The model has periodic tuning enabled. Tuning
    // can be disabled by calling the `DisableModelPeriodicTuning`
    // method.
    PERIODIC_TUNING_ENABLED = 2;
  }

  // Describes whether this model have sufficient training data
  // to be continuously trained.
  enum DataState {
    // Unspecified default value, should never be explicitly set.
    DATA_STATE_UNSPECIFIED = 0;

    // The model has sufficient training data.
    DATA_OK = 1;

    // The model does not have sufficient training data. Error
    // messages can be queried via Stackdriver.
    DATA_ERROR = 2;
  }

  // Use single or multiple context products for recommendations.
  enum ContextProductsType {
    // Unspecified default value, should never be explicitly set.
    // Defaults to
    // [MULTIPLE_CONTEXT_PRODUCTS][google.cloud.retail.v2beta.Model.ContextProductsType.MULTIPLE_CONTEXT_PRODUCTS].
    CONTEXT_PRODUCTS_TYPE_UNSPECIFIED = 0;

    // Use only a single product as context for the recommendation. Typically
    // used on pages like add-to-cart or product details.
    SINGLE_CONTEXT_PRODUCT = 1;

    // Use one or multiple products as context for the recommendation. Typically
    // used on shopping cart pages.
    MULTIPLE_CONTEXT_PRODUCTS = 2;
  }

  // Required. The fully qualified resource name of the model.
  //
  // Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/models/{model_id}`
  // catalog_id has char limit of 50.
  // recommendation_model_id has char limit of 40.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The display name of the model.
  //
  // Should be human readable, used to display Recommendation Models in the
  // Retail Cloud Console Dashboard. UTF-8 encoded string with limit of 1024
  // characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The training state that the model is in (e.g.
  // `TRAINING` or `PAUSED`).
  //
  // Since part of the cost of running the service
  // is frequency of training - this can be used to determine when to train
  // model in order to control cost. If not specified: the default value for
  // `CreateModel` method is `TRAINING`. The default value for
  // `UpdateModel` method is to keep the state the same as before.
  TrainingState training_state = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The serving state of the model: `ACTIVE`, `NOT_ACTIVE`.
  ServingState serving_state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp the Recommendation Model was created at.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp the Recommendation Model was last updated. E.g.
  // if a Recommendation Model was paused - this would be the time the pause was
  // initiated.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The type of model e.g. `home-page`.
  //
  // Currently supported values: `recommended-for-you`, `others-you-may-like`,
  // `frequently-bought-together`, `page-optimization`, `similar-items`,
  // `buy-it-again`, `on-sale-items`, and `recently-viewed`(readonly value).
  //
  //
  // This field together with
  // [optimization_objective][google.cloud.retail.v2beta.Model.optimization_objective]
  // describe model metadata to use to control model training and serving.
  // See https://cloud.google.com/retail/docs/models
  // for more details on what the model metadata control and which combination
  // of parameters are valid. For invalid combinations of parameters (e.g. type
  // = `frequently-bought-together` and optimization_objective = `ctr`), you
  // receive an error 400 if you try to create/update a recommendation with
  // this set of knobs.
  string type = 7 [(google.api.field_behavior) = REQUIRED];

  // Optional. The optimization objective e.g. `cvr`.
  //
  // Currently supported
  // values: `ctr`, `cvr`, `revenue-per-order`.
  //
  //  If not specified, we choose default based on model type.
  // Default depends on type of recommendation:
  //
  // `recommended-for-you` => `ctr`
  //
  // `others-you-may-like` => `ctr`
  //
  // `frequently-bought-together` => `revenue_per_order`
  //
  // This field together with
  // [optimization_objective][google.cloud.retail.v2beta.Model.type]
  // describe model metadata to use to control model training and serving.
  // See https://cloud.google.com/retail/docs/models
  // for more details on what the model metadata control and which combination
  // of parameters are valid. For invalid combinations of parameters (e.g. type
  // = `frequently-bought-together` and optimization_objective = `ctr`), you
  // receive an error 400 if you try to create/update a recommendation with
  // this set of knobs.
  string optimization_objective = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The state of periodic tuning.
  //
  // The period we use is 3 months - to do a
  // one-off tune earlier use the `TuneModel` method. Default value
  // is `PERIODIC_TUNING_ENABLED`.
  PeriodicTuningState periodic_tuning_state = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The timestamp when the latest successful tune finished.
  google.protobuf.Timestamp last_tune_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The tune operation associated with the model.
  //
  // Can be used to determine if there is an ongoing tune for this
  // recommendation. Empty field implies no tune is goig on.
  string tuning_operation = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of data requirements for this model: `DATA_OK` and
  // `DATA_ERROR`.
  //
  // Recommendation model cannot be trained if the data is in
  // `DATA_ERROR` state. Recommendation model can have `DATA_ERROR` state even
  // if serving state is `ACTIVE`: models were trained successfully before, but
  // cannot be refreshed because model no longer has sufficient
  // data for training.
  DataState data_state = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. If `RECOMMENDATIONS_FILTERING_ENABLED`, recommendation filtering
  // by attributes is enabled for the model.
  RecommendationsFilteringOption filtering_option = 18
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The list of valid serving configs associated with the
  // PageOptimizationConfig.
  repeated ServingConfigList serving_config_lists = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Additional model features config.
  ModelFeaturesConfig model_features_config = 22
      [(google.api.field_behavior) = OPTIONAL];
}
