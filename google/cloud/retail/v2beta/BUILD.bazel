# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "retail_proto",
    srcs = [
        "analytics_service.proto",
        "catalog.proto",
        "catalog_service.proto",
        "common.proto",
        "completion_service.proto",
        "control.proto",
        "control_service.proto",
        "export_config.proto",
        "generative_question.proto",
        "generative_question_service.proto",
        "import_config.proto",
        "model.proto",
        "model_service.proto",
        "prediction_service.proto",
        "product.proto",
        "product_service.proto",
        "project.proto",
        "project_service.proto",
        "promotion.proto",
        "purge_config.proto",
        "search_service.proto",
        "serving_config.proto",
        "serving_config_service.proto",
        "user_event.proto",
        "user_event_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:httpbody_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "retail_proto_with_info",
    deps = [
        ":retail_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "retail_java_proto",
    deps = [":retail_proto"],
)

java_grpc_library(
    name = "retail_java_grpc",
    srcs = [":retail_proto"],
    deps = [":retail_java_proto"],
)

java_gapic_library(
    name = "retail_java_gapic",
    srcs = [":retail_proto_with_info"],
    gapic_yaml = "retail_gapic.yaml",
    grpc_service_config = "retail_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "retail_v2beta.yaml",
    test_deps = [
        ":retail_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":retail_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "retail_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.retail.v2beta.AnalyticsServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.AnalyticsServiceClientTest",
        "com.google.cloud.retail.v2beta.CatalogServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.CatalogServiceClientTest",
        "com.google.cloud.retail.v2beta.CompletionServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.CompletionServiceClientTest",
        "com.google.cloud.retail.v2beta.ControlServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.ControlServiceClientTest",
        "com.google.cloud.retail.v2beta.GenerativeQuestionServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.GenerativeQuestionServiceClientTest",
        "com.google.cloud.retail.v2beta.ModelServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.ModelServiceClientTest",
        "com.google.cloud.retail.v2beta.PredictionServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.PredictionServiceClientTest",
        "com.google.cloud.retail.v2beta.ProductServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.ProductServiceClientTest",
        "com.google.cloud.retail.v2beta.ProjectServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.ProjectServiceClientTest",
        "com.google.cloud.retail.v2beta.SearchServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.SearchServiceClientTest",
        "com.google.cloud.retail.v2beta.ServingConfigServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.ServingConfigServiceClientTest",
        "com.google.cloud.retail.v2beta.UserEventServiceClientHttpJsonTest",
        "com.google.cloud.retail.v2beta.UserEventServiceClientTest",
    ],
    runtime_deps = [":retail_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-retail-v2beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":retail_java_gapic",
        ":retail_java_grpc",
        ":retail_java_proto",
        ":retail_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "retail_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/retail/apiv2beta/retailpb",
    protos = [":retail_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "retail_go_gapic",
    srcs = [":retail_proto_with_info"],
    grpc_service_config = "retail_grpc_service_config.json",
    importpath = "cloud.google.com/go/retail/apiv2beta;retail",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "retail_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":retail_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-retail-v2beta-go",
    deps = [
        ":retail_go_gapic",
        ":retail_go_gapic_srcjar-metadata.srcjar",
        ":retail_go_gapic_srcjar-snippets.srcjar",
        ":retail_go_gapic_srcjar-test.srcjar",
        ":retail_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "retail_py_gapic",
    srcs = [":retail_proto"],
    grpc_service_config = "retail_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "retail_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "retail_py_gapic_test",
    srcs = [
        "retail_py_gapic_pytest.py",
        "retail_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":retail_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "retail-v2beta-py",
    deps = [
        ":retail_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "retail_php_proto",
    deps = [":retail_proto"],
)

php_gapic_library(
    name = "retail_php_gapic",
    srcs = [":retail_proto_with_info"],
    grpc_service_config = "retail_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "retail_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":retail_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-retail-v2beta-php",
    deps = [
        ":retail_php_gapic",
        ":retail_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "retail_nodejs_gapic",
    package_name = "@google-cloud/retail",
    src = ":retail_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "retail_grpc_service_config.json",
    mixins = "google.longrunning.Operations;google.cloud.location.Locations",
    package = "google.cloud.retail.v2beta",
    rest_numeric_enums = True,
    service_yaml = "retail_v2beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "retail-v2beta-nodejs",
    deps = [
        ":retail_nodejs_gapic",
        ":retail_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "retail_ruby_proto",
    deps = [":retail_proto"],
)

ruby_grpc_library(
    name = "retail_ruby_grpc",
    srcs = [":retail_proto"],
    deps = [":retail_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "retail_ruby_gapic",
    srcs = [":retail_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=retail.googleapis.com",
        "ruby-cloud-api-shortname=retail",
        "ruby-cloud-env-prefix=RETAIL",
        "ruby-cloud-gem-name=google-cloud-retail-v2beta",
        "ruby-cloud-product-url=https://cloud.google.com/retail/docs/apis",
    ],
    grpc_service_config = "retail_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Retail enables you to build an end-to-end personalized recommendation system based on state-of-the-art deep learning ML models, without a need for expertise in ML or recommendation systems.",
    ruby_cloud_title = "Retail V2beta",
    service_yaml = "retail_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":retail_ruby_grpc",
        ":retail_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-retail-v2beta-ruby",
    deps = [
        ":retail_ruby_gapic",
        ":retail_ruby_grpc",
        ":retail_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "retail_csharp_proto",
    extra_opts = [],
    deps = [":retail_proto"],
)

csharp_grpc_library(
    name = "retail_csharp_grpc",
    srcs = [":retail_proto"],
    deps = [":retail_csharp_proto"],
)

csharp_gapic_library(
    name = "retail_csharp_gapic",
    srcs = [":retail_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "retail_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "retail_v2beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":retail_csharp_grpc",
        ":retail_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-retail-v2beta-csharp",
    deps = [
        ":retail_csharp_gapic",
        ":retail_csharp_grpc",
        ":retail_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "retail_cc_proto",
    deps = [":retail_proto"],
)

cc_grpc_library(
    name = "retail_cc_grpc",
    srcs = [":retail_proto"],
    grpc_only = True,
    deps = [":retail_cc_proto"],
)
