// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Beta";
option go_package = "cloud.google.com/go/retail/apiv2beta/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ProjectProto";
option java_package = "com.google.cloud.retail.v2beta";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2beta";
option ruby_package = "Google::Cloud::Retail::V2beta";

// Project level alert config.
message AlertConfig {
  option (google.api.resource) = {
    type: "retail.googleapis.com/AlertConfig"
    pattern: "projects/{project}/alertConfig"
  };

  // Alert policy for a customer.
  message AlertPolicy {
    // Recipient contact information.
    message Recipient {
      // Email address of the recipient.
      string email_address = 1;
    }

    // The enrollment status enum for alert policy.
    enum EnrollStatus {
      // Default value. Used for customers who have not responded to the
      // alert policy.
      ENROLL_STATUS_UNSPECIFIED = 0;

      // Customer is enrolled in this policy.
      ENROLLED = 1;

      // Customer declined this policy.
      DECLINED = 2;
    }

    // The feature that provides alerting capability.
    // Supported value:
    // - `search-data-quality` for retail search customers.
    // - `conv-data-quality` for retail conversation customers.
    string alert_group = 1;

    // The enrollment status of a customer.
    EnrollStatus enroll_status = 2;

    // Recipients for the alert policy.
    // One alert policy should not exceed 20 recipients.
    repeated Recipient recipients = 3;
  }

  // Required. Immutable. The name of the AlertConfig singleton resource.
  // Format: projects/*/alertConfig
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Alert policies for a customer.
  // They must be unique by [AlertPolicy.alert_group]
  repeated AlertPolicy alert_policies = 2;
}
