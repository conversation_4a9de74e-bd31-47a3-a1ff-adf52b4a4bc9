type: google.api.Service
config_version: 3
name: retail.googleapis.com
title: Vertex AI Search for Retail API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.retail.v2beta.AnalyticsService
- name: google.cloud.retail.v2beta.CatalogService
- name: google.cloud.retail.v2beta.CompletionService
- name: google.cloud.retail.v2beta.ControlService
- name: google.cloud.retail.v2beta.GenerativeQuestionService
- name: google.cloud.retail.v2beta.ModelService
- name: google.cloud.retail.v2beta.PredictionService
- name: google.cloud.retail.v2beta.ProductService
- name: google.cloud.retail.v2beta.ProjectService
- name: google.cloud.retail.v2beta.SearchService
- name: google.cloud.retail.v2beta.ServingConfigService
- name: google.cloud.retail.v2beta.UserEventService
- name: google.longrunning.Operations

types:
- name: google.cloud.retail.logging.ErrorLog
- name: google.cloud.retail.v2beta.AddFulfillmentPlacesMetadata
- name: google.cloud.retail.v2beta.AddFulfillmentPlacesResponse
- name: google.cloud.retail.v2beta.AddLocalInventoriesMetadata
- name: google.cloud.retail.v2beta.AddLocalInventoriesResponse
- name: google.cloud.retail.v2beta.CreateModelMetadata
- name: google.cloud.retail.v2beta.ExportAnalyticsMetricsResponse
- name: google.cloud.retail.v2beta.ExportErrorsConfig
- name: google.cloud.retail.v2beta.ExportMetadata
- name: google.cloud.retail.v2beta.ExportProductsResponse
- name: google.cloud.retail.v2beta.ExportUserEventsResponse
- name: google.cloud.retail.v2beta.ImportCompletionDataResponse
- name: google.cloud.retail.v2beta.ImportErrorsConfig
- name: google.cloud.retail.v2beta.ImportMetadata
- name: google.cloud.retail.v2beta.ImportProductsResponse
- name: google.cloud.retail.v2beta.ImportUserEventsResponse
- name: google.cloud.retail.v2beta.Model
- name: google.cloud.retail.v2beta.PurgeMetadata
- name: google.cloud.retail.v2beta.PurgeProductsMetadata
- name: google.cloud.retail.v2beta.PurgeProductsResponse
- name: google.cloud.retail.v2beta.PurgeUserEventsResponse
- name: google.cloud.retail.v2beta.RejoinUserEventsMetadata
- name: google.cloud.retail.v2beta.RejoinUserEventsResponse
- name: google.cloud.retail.v2beta.RemoveFulfillmentPlacesMetadata
- name: google.cloud.retail.v2beta.RemoveFulfillmentPlacesResponse
- name: google.cloud.retail.v2beta.RemoveLocalInventoriesMetadata
- name: google.cloud.retail.v2beta.RemoveLocalInventoriesResponse
- name: google.cloud.retail.v2beta.SetInventoryMetadata
- name: google.cloud.retail.v2beta.SetInventoryResponse
- name: google.cloud.retail.v2beta.TuneModelMetadata
- name: google.cloud.retail.v2beta.TuneModelResponse

documentation:
  summary: |-
    Vertex AI Search for Retail API is made up of Retail Search, Browse and
    Recommendations. These discovery AI solutions help you implement
    personalized search, browse and recommendations, based on machine learning
    models, across your websites and mobile applications.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2beta/{name=projects/*/locations/*/catalogs/*/branches/*/operations/*}'
    additional_bindings:
    - get: '/v2beta/{name=projects/*/locations/*/catalogs/*/operations/*}'
    - get: '/v2beta/{name=projects/*/locations/*/operations/*}'
    - get: '/v2beta/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2beta/{name=projects/*/locations/*/catalogs/*}/operations'
    additional_bindings:
    - get: '/v2beta/{name=projects/*/locations/*}/operations'
    - get: '/v2beta/{name=projects/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2beta.AnalyticsService.ExportAnalyticsMetrics
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2beta.CatalogService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2beta.CompletionService.CompleteQuery
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2beta.CompletionService.ImportCompletionData
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2beta.ControlService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2beta.GenerativeQuestionService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2beta.ModelService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2beta.PredictionService.Predict
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2beta.ProductService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2beta.ProjectService.GetAlertConfig
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2beta.ProjectService.UpdateAlertConfig
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2beta.SearchService.Search
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2beta.ServingConfigService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2beta.UserEventService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.ListOperations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
