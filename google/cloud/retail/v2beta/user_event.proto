// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2beta;

import "google/api/field_behavior.proto";
import "google/cloud/retail/v2beta/common.proto";
import "google/cloud/retail/v2beta/product.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Beta";
option go_package = "cloud.google.com/go/retail/apiv2beta/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "UserEventProto";
option java_package = "com.google.cloud.retail.v2beta";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2beta";
option ruby_package = "Google::Cloud::Retail::V2beta";

// UserEvent captures all metadata information Retail API needs to know about
// how end users interact with customers' website.
message UserEvent {
  // Required. User event type. Allowed values are:
  //
  // * `add-to-cart`: Products being added to cart.
  // * `remove-from-cart`: Products being removed from cart.
  // * `category-page-view`: Special pages such as sale or promotion pages
  //   viewed.
  // * `detail-page-view`: Products detail page viewed.
  // * `home-page-view`: Homepage viewed.
  // * `promotion-offered`: Promotion is offered to a user.
  // * `promotion-not-offered`: Promotion is not offered to a user.
  // * `purchase-complete`: User finishing a purchase.
  // * `search`: Product search.
  // * `shopping-cart-page-view`: User viewing a shopping cart.
  string event_type = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A unique identifier for tracking visitors.
  //
  // For example, this could be implemented with an HTTP cookie, which should be
  // able to uniquely identify a visitor on a single device. This unique
  // identifier should not change if the visitor log in/out of the website.
  //
  // Don't set the field to the same fixed ID for different users. This mixes
  // the event history of those users together, which results in degraded model
  // quality.
  //
  // The field must be a UTF-8 encoded string with a length limit of 128
  // characters. Otherwise, an INVALID_ARGUMENT error is returned.
  //
  // The field should not contain PII or user-data. We recommend to use Google
  // Analytics [Client
  // ID](https://developers.google.com/analytics/devguides/collection/analyticsjs/field-reference#clientId)
  // for this field.
  string visitor_id = 2 [(google.api.field_behavior) = REQUIRED];

  // A unique identifier for tracking a visitor session with a length limit of
  // 128 bytes. A session is an aggregation of an end user behavior in a time
  // span.
  //
  // A general guideline to populate the sesion_id:
  // 1. If user has no activity for 30 min, a new session_id should be assigned.
  // 2. The session_id should be unique across users, suggest use uuid or add
  // visitor_id as prefix.
  string session_id = 21;

  // Only required for
  // [UserEventService.ImportUserEvents][google.cloud.retail.v2beta.UserEventService.ImportUserEvents]
  // method. Timestamp of when the user event happened.
  google.protobuf.Timestamp event_time = 3;

  // A list of identifiers for the independent experiment groups this user event
  // belongs to. This is used to distinguish between user events associated with
  // different experiment setups (e.g. using Retail API, using different
  // recommendation models).
  repeated string experiment_ids = 4;

  // Highly recommended for user events that are the result of
  // [PredictionService.Predict][google.cloud.retail.v2beta.PredictionService.Predict].
  // This field enables accurate attribution of recommendation model
  // performance.
  //
  // The value must be a valid
  // [PredictResponse.attribution_token][google.cloud.retail.v2beta.PredictResponse.attribution_token]
  // for user events that are the result of
  // [PredictionService.Predict][google.cloud.retail.v2beta.PredictionService.Predict].
  // The value must be a valid
  // [SearchResponse.attribution_token][google.cloud.retail.v2beta.SearchResponse.attribution_token]
  // for user events that are the result of
  // [SearchService.Search][google.cloud.retail.v2beta.SearchService.Search].
  //
  // This token enables us to accurately attribute page view or purchase back to
  // the event and the particular predict response containing this
  // clicked/purchased product. If user clicks on product K in the
  // recommendation results, pass
  // [PredictResponse.attribution_token][google.cloud.retail.v2beta.PredictResponse.attribution_token]
  // as a URL parameter to product K's page. When recording events on product
  // K's page, log the
  // [PredictResponse.attribution_token][google.cloud.retail.v2beta.PredictResponse.attribution_token]
  // to this field.
  string attribution_token = 5;

  // The main product details related to the event.
  //
  // This field is optional except for the following event types:
  //
  // * `add-to-cart`
  // * `detail-page-view`
  // * `purchase-complete`
  //
  // In a `search` event, this field represents the products returned to the end
  // user on the current page (the end user may have not finished browsing the
  // whole page yet). When a new page is returned to the end user, after
  // pagination/filtering/ordering even for the same query, a new `search` event
  // with different
  // [product_details][google.cloud.retail.v2beta.UserEvent.product_details] is
  // desired. The end user may have not finished browsing the whole page yet.
  repeated ProductDetail product_details = 6;

  // The main auto-completion details related to the event.
  //
  // This field should be set for `search` event when autocomplete function is
  // enabled and the user clicks a suggestion for search.
  CompletionDetail completion_detail = 22;

  // Extra user event features to include in the recommendation model.
  //
  // If you provide custom attributes for ingested user events, also include
  // them in the user events that you associate with prediction requests. Custom
  // attribute formatting must be consistent between imported events and events
  // provided with prediction requests. This lets the Retail API use
  // those custom attributes when training models and serving predictions, which
  // helps improve recommendation quality.
  //
  // This field needs to pass all below criteria, otherwise an INVALID_ARGUMENT
  // error is returned:
  //
  // * The key must be a UTF-8 encoded string with a length limit of 5,000
  //   characters.
  // * For text attributes, at most 400 values are allowed. Empty values are not
  //   allowed. Each value must be a UTF-8 encoded string with a length limit of
  //   256 characters.
  // * For number attributes, at most 400 values are allowed.
  //
  // For product recommendations, an example of extra user information is
  // traffic_channel, which is how a user arrives at the site. Users can arrive
  // at the site by coming to the site directly, coming through Google
  // search, or in other ways.
  map<string, CustomAttribute> attributes = 7;

  // The ID or name of the associated shopping cart. This ID is used
  // to associate multiple items added or present in the cart before purchase.
  //
  // This can only be set for `add-to-cart`, `purchase-complete`, or
  // `shopping-cart-page-view` events.
  string cart_id = 8;

  // A transaction represents the entire purchase transaction.
  //
  // Required for `purchase-complete` events. Other event types should not set
  // this field. Otherwise, an INVALID_ARGUMENT error is returned.
  PurchaseTransaction purchase_transaction = 9;

  // The user's search query.
  //
  // See [SearchRequest.query][google.cloud.retail.v2beta.SearchRequest.query]
  // for definition.
  //
  // The value must be a UTF-8 encoded string with a length limit of 5,000
  // characters. Otherwise, an INVALID_ARGUMENT error is returned.
  //
  // At least one of
  // [search_query][google.cloud.retail.v2beta.UserEvent.search_query] or
  // [page_categories][google.cloud.retail.v2beta.UserEvent.page_categories] is
  // required for `search` events. Other event types should not set this field.
  // Otherwise, an INVALID_ARGUMENT error is returned.
  string search_query = 10;

  // The filter syntax consists of an expression language for constructing a
  // predicate from one or more fields of the products being filtered.
  //
  // See [SearchRequest.filter][google.cloud.retail.v2beta.SearchRequest.filter]
  // for definition and syntax.
  //
  // The value must be a UTF-8 encoded string with a length limit of 1,000
  // characters. Otherwise, an INVALID_ARGUMENT error is returned.
  string filter = 16;

  // The order in which products are returned.
  //
  // See
  // [SearchRequest.order_by][google.cloud.retail.v2beta.SearchRequest.order_by]
  // for definition and syntax.
  //
  // The value must be a UTF-8 encoded string with a length limit of 1,000
  // characters. Otherwise, an INVALID_ARGUMENT error is returned.
  //
  // This can only be set for `search` events. Other event types should not set
  // this field. Otherwise, an INVALID_ARGUMENT error is returned.
  string order_by = 17;

  // An integer that specifies the current offset for pagination (the 0-indexed
  // starting location, amongst the products deemed by the API as relevant).
  //
  // See [SearchRequest.offset][google.cloud.retail.v2beta.SearchRequest.offset]
  // for definition.
  //
  // If this field is negative, an INVALID_ARGUMENT is returned.
  //
  // This can only be set for `search` events. Other event types should not set
  // this field. Otherwise, an INVALID_ARGUMENT error is returned.
  int32 offset = 18;

  // The categories associated with a category page.
  //
  // To represent full path of category, use '>' sign to separate different
  // hierarchies. If '>' is part of the category name, replace it with
  // other character(s).
  //
  // Category pages include special pages such as sales or promotions. For
  // instance, a special sale page may have the category hierarchy:
  // "pageCategories" : ["Sales > 2017 Black Friday Deals"].
  //
  // Required for `category-page-view` events. At least one of
  // [search_query][google.cloud.retail.v2beta.UserEvent.search_query] or
  // [page_categories][google.cloud.retail.v2beta.UserEvent.page_categories] is
  // required for `search` events. Other event types should not set this field.
  // Otherwise, an INVALID_ARGUMENT error is returned.
  repeated string page_categories = 11;

  // User information.
  UserInfo user_info = 12;

  // Complete URL (window.location.href) of the user's current page.
  //
  // When using the client side event reporting with JavaScript pixel and Google
  // Tag Manager, this value is filled in automatically. Maximum length 5,000
  // characters.
  string uri = 13;

  // The referrer URL of the current page.
  //
  // When using the client side event reporting with JavaScript pixel and Google
  // Tag Manager, this value is filled in automatically.
  string referrer_uri = 14;

  // A unique ID of a web page view.
  //
  // This should be kept the same for all user events triggered from the same
  // pageview. For example, an item detail page view could trigger multiple
  // events as the user is browsing the page. The `pageViewId` property should
  // be kept the same for all these events so that they can be grouped together
  // properly.
  //
  // When using the client side event reporting with JavaScript pixel and Google
  // Tag Manager, this value is filled in automatically.
  string page_view_id = 15;

  // The entity for customers that may run multiple different entities, domains,
  // sites or regions, for example, `Google US`, `Google Ads`, `Waymo`,
  // `google.com`, `youtube.com`, etc.
  // We recommend that you set this field to get better per-entity search,
  // completion, and prediction results.
  string entity = 23;
}

// Detailed product information associated with a user event.
message ProductDetail {
  // Required. [Product][google.cloud.retail.v2beta.Product] information.
  //
  // Required field(s):
  //
  // * [Product.id][google.cloud.retail.v2beta.Product.id]
  //
  // Optional override field(s):
  //
  // * [Product.price_info][google.cloud.retail.v2beta.Product.price_info]
  //
  // If any supported optional fields are provided, we will treat them as a full
  // override when looking up product information from the catalog. Thus, it is
  // important to ensure that the overriding fields are accurate and
  // complete.
  //
  // All other product fields are ignored and instead populated via catalog
  // lookup after event ingestion.
  Product product = 1 [(google.api.field_behavior) = REQUIRED];

  // Quantity of the product associated with the user event.
  //
  // For example, this field will be 2 if two products are added to the shopping
  // cart for `purchase-complete` event. Required for `add-to-cart` and
  // `purchase-complete` event types.
  google.protobuf.Int32Value quantity = 2;
}

// Detailed completion information including completion attribution token and
// clicked completion info.
message CompletionDetail {
  // Completion attribution token in
  // [CompleteQueryResponse.attribution_token][google.cloud.retail.v2beta.CompleteQueryResponse.attribution_token].
  string completion_attribution_token = 1;

  // End user selected
  // [CompleteQueryResponse.CompletionResult.suggestion][google.cloud.retail.v2beta.CompleteQueryResponse.CompletionResult.suggestion].
  string selected_suggestion = 2;

  // End user selected
  // [CompleteQueryResponse.CompletionResult.suggestion][google.cloud.retail.v2beta.CompleteQueryResponse.CompletionResult.suggestion]
  // position, starting from 0.
  int32 selected_position = 3;
}

// A transaction represents the entire purchase transaction.
message PurchaseTransaction {
  // The transaction ID with a length limit of 128 characters.
  string id = 1;

  // Required. Total non-zero revenue or grand total associated with the
  // transaction. This value include shipping, tax, or other adjustments to
  // total revenue that you want to include as part of your revenue
  // calculations.
  float revenue = 2 [(google.api.field_behavior) = REQUIRED];

  // All the taxes associated with the transaction.
  float tax = 3;

  // All the costs associated with the products. These can be manufacturing
  // costs, shipping expenses not borne by the end user, or any other costs,
  // such that:
  //
  // * Profit =
  // [revenue][google.cloud.retail.v2beta.PurchaseTransaction.revenue] -
  // [tax][google.cloud.retail.v2beta.PurchaseTransaction.tax] -
  // [cost][google.cloud.retail.v2beta.PurchaseTransaction.cost]
  float cost = 4;

  // Required. Currency code. Use three-character ISO-4217 code.
  string currency_code = 5 [(google.api.field_behavior) = REQUIRED];
}
