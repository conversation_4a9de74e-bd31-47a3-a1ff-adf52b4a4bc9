// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Beta";
option go_package = "cloud.google.com/go/retail/apiv2beta/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ExportConfigProto";
option java_package = "com.google.cloud.retail.v2beta";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2beta";
option ruby_package = "Google::Cloud::Retail::V2beta";

// The output configuration setting.
message OutputConfig {
  // The Google Cloud Storage output destination configuration.
  message GcsDestination {
    // Required. The output uri prefix for saving output data to json files.
    // Some mapping examples are as follows:
    // output_uri_prefix         sample output(assuming the object is foo.json)
    // ========================  =============================================
    // gs://bucket/              gs://bucket/foo.json
    // gs://bucket/folder/       gs://bucket/folder/foo.json
    // gs://bucket/folder/item_  gs://bucket/folder/item_foo.json
    string output_uri_prefix = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // The BigQuery output destination configuration.
  message BigQueryDestination {
    // Required. The ID of a BigQuery Dataset.
    string dataset_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The prefix of exported BigQuery tables.
    string table_id_prefix = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. Describes the table type. The following values are supported:
    //
    // * `table`: A BigQuery native table.
    // * `view`: A virtual table defined by a SQL query.
    string table_type = 3 [(google.api.field_behavior) = REQUIRED];
  }

  // The configuration of destination for holding output data.
  oneof destination {
    // The Google Cloud Storage location where the output is to be written to.
    GcsDestination gcs_destination = 1;

    // The BigQuery location where the output is to be written to.
    BigQueryDestination bigquery_destination = 2;
  }
}

// Configuration of destination for Export related errors.
message ExportErrorsConfig {
  // Required. Errors destination.
  oneof destination {
    // Google Cloud Storage path for import errors. This must be an empty,
    // existing Cloud Storage bucket. Export errors will be written to a file in
    // this bucket, one per line, as a JSON-encoded
    // `google.rpc.Status` message.
    string gcs_prefix = 1;
  }
}

// Request message for ExportProducts method.
message ExportProductsRequest {
  // Required. Resource name of a [Branch][google.cloud.retail.v2beta.Branch],
  // and `default_branch` for branch_id component is supported. For example
  //  `projects/1234/locations/global/catalogs/default_catalog/branches/default_branch`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Branch" }
  ];

  // Required. The output location of the data.
  OutputConfig output_config = 2 [(google.api.field_behavior) = REQUIRED];

  // A filtering expression to specify restrictions on returned events.
  // The expression is a sequence of terms. Each term applies a restriction to
  // the returned products. Use this expression to restrict results to a
  // specific time range, tag, or stock state or to filter products by product
  // type.
  // For example, `lastModifiedTime > "2012-04-23T18:25:43.511Z"
  // lastModifiedTime<"2012-04-23T18:25:43.511Z" productType=primary`
  //
  //   We expect only four types of fields:
  //
  //    * `lastModifiedTime`: This can be specified twice, once with a
  //      less than operator and once with a greater than operator. The
  //      `lastModifiedTime` restriction should result in one, contiguous,
  //      valid, last-modified, time range.
  //
  //    * `productType`: Supported values are `primary` and `variant`. The
  //    Boolean operators `OR` and `NOT` are supported if the expression is
  //    enclosed in parentheses and must be separated from the
  //      `productType` values by a space.
  //
  //    * `availability`: Supported values are `IN_STOCK`, `OUT_OF_STOCK`,
  //    `PREORDER`, and `BACKORDER`. Boolean operators `OR` and `NOT` are
  //    supported if the expression is enclosed in parentheses and must be
  //    separated from the `availability` values by a space.
  //
  //    * `Tag expressions`: Restricts output to products that match all of the
  //      specified tags. Boolean operators `OR` and `NOT` are supported if the
  //      expression is enclosed in parentheses and the operators are separated
  //      from the tag values by a space. Also supported is '`-"tagA"`', which
  //      is equivalent to '`NOT "tagA"`'. Tag values must be double-quoted,
  //      UTF-8 encoded strings and have a size limit of 1,000 characters.
  //
  //   Some examples of valid filters expressions:
  //
  //   * Example 1: `lastModifiedTime > "2012-04-23T18:25:43.511Z"
  //             lastModifiedTime < "2012-04-23T18:30:43.511Z"`
  //   * Example 2: `lastModifiedTime > "2012-04-23T18:25:43.511Z"
  //             productType = "variant"`
  //   * Example 3: `tag=("Red" OR "Blue") tag="New-Arrival"
  //             tag=(NOT "promotional")
  //             productType = "primary" lastModifiedTime <
  //             "2018-04-23T18:30:43.511Z"`
  //   * Example 4: `lastModifiedTime > "2012-04-23T18:25:43.511Z"`
  //   * Example 5: `availability = (IN_STOCK OR BACKORDER)`
  string filter = 3;
}

// Request message for the `ExportUserEvents` method.
message ExportUserEventsRequest {
  // Required. Resource name of a [Catalog][google.cloud.retail.v2beta.Catalog].
  // For example `projects/1234/locations/global/catalogs/default_catalog`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // Required. The output location of the data.
  OutputConfig output_config = 2 [(google.api.field_behavior) = REQUIRED];

  // A filtering expression to specify restrictions on returned events.
  // The expression is a sequence of terms. Each term applies a restriction to
  // the returned user events. Use this expression to restrict results to a
  // specific time range or to filter events by eventType.
  // For example, `eventTime > "2012-04-23T18:25:43.511Z"
  // eventsMissingCatalogItems eventTime<"2012-04-23T18:25:43.511Z"
  // eventType=search`
  //
  //   We expect only three types of fields:
  //
  //    * `eventTime`: This can be specified twice, once with a
  //      less than operator and once with a greater than operator. The
  //      `eventTime` restriction should result in one, contiguous, valid,
  //      `eventTime` range.
  //
  //    * `eventType`: Boolean operators `OR` and `NOT` are supported if the
  //      expression is enclosed in parentheses and the operators are separated
  //      from the tag values by a space.
  //
  //    * `eventsMissingCatalogItems`: This restricts results
  //      to events for which catalog items were not found in the catalog. The
  //      default behavior is to return only those events for which catalog
  //      items were found.
  //
  //   Some examples of valid filters expressions:
  //
  //   * Example 1: `eventTime > "2012-04-23T18:25:43.511Z"
  //             eventTime < "2012-04-23T18:30:43.511Z"`
  //   * Example 2: `eventTime > "2012-04-23T18:25:43.511Z"
  //             eventType = detail-page-view`
  //   * Example 3: `eventsMissingCatalogItems
  //             eventType = (NOT search) eventTime <
  //             "2018-04-23T18:30:43.511Z"`
  //   * Example 4: `eventTime > "2012-04-23T18:25:43.511Z"`
  //   * Example 5: `eventType = (detail-page-view OR search)`
  //   * Example 6: `eventsMissingCatalogItems`
  string filter = 3;
}

// Request message for the `ExportAnalyticsMetrics` method.
message ExportAnalyticsMetricsRequest {
  // Required. Full resource name of the parent catalog.
  // Expected format: `projects/*/locations/*/catalogs/*`
  string catalog = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The output location of the data.
  OutputConfig output_config = 2 [(google.api.field_behavior) = REQUIRED];

  // A filtering expression to specify restrictions on returned metrics.
  // The expression is a sequence of terms. Each term applies a restriction to
  // the returned metrics. Use this expression to restrict results to a
  // specific time range.
  //
  //   Currently we expect only one types of fields:
  //
  //    * `timestamp`: This can be specified twice, once with a
  //      less than operator and once with a greater than operator. The
  //      `timestamp` restriction should result in one, contiguous, valid,
  //      `timestamp` range.
  //
  //   Some examples of valid filters expressions:
  //
  //   * Example 1: `timestamp > "2012-04-23T18:25:43.511Z"
  //             timestamp < "2012-04-23T18:30:43.511Z"`
  //   * Example 2: `timestamp > "2012-04-23T18:25:43.511Z"`
  string filter = 3;
}

// Metadata related to the progress of the Export operation. This is
// returned by the google.longrunning.Operation.metadata field.
message ExportMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Response of the ExportProductsRequest. If the long running
// operation is done, then this message is returned by the
// google.longrunning.Operations.response field if the operation was successful.
message ExportProductsResponse {
  // A sample of errors encountered while processing the request.
  repeated google.rpc.Status error_samples = 1;

  // This field is never set.
  ExportErrorsConfig errors_config = 2;

  // Output result indicating where the data were exported to.
  OutputResult output_result = 3;
}

// Response of the ExportUserEventsRequest. If the long running
// operation was successful, then this message is returned by the
// google.longrunning.Operations.response field if the operation was successful.
message ExportUserEventsResponse {
  // A sample of errors encountered while processing the request.
  repeated google.rpc.Status error_samples = 1;

  // This field is never set.
  ExportErrorsConfig errors_config = 2;

  // Output result indicating where the data were exported to.
  OutputResult output_result = 3;
}

// Response of the ExportAnalyticsMetricsRequest. If the long running
// operation was successful, then this message is returned by the
// google.longrunning.Operations.response field if the operation was successful.
message ExportAnalyticsMetricsResponse {
  // A sample of errors encountered while processing the request.
  repeated google.rpc.Status error_samples = 1;

  // This field is never set.
  ExportErrorsConfig errors_config = 2;

  // Output result indicating where the data were exported to.
  OutputResult output_result = 3;
}

// Output result that stores the information about where the exported data is
// stored.
message OutputResult {
  // The BigQuery location where the result is stored.
  repeated BigQueryOutputResult bigquery_result = 1;

  // The Google Cloud Storage location where the result is stored.
  repeated GcsOutputResult gcs_result = 2;
}

// A BigQuery output result.
message BigQueryOutputResult {
  // The ID of a BigQuery Dataset.
  string dataset_id = 1;

  // The ID of a BigQuery Table.
  string table_id = 2;
}

// A Gcs output result.
message GcsOutputResult {
  // The uri of Gcs output
  string output_uri = 1;
}
