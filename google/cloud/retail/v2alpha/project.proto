// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2alpha/common.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Alpha";
option go_package = "cloud.google.com/go/retail/apiv2alpha/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ProjectProto";
option java_package = "com.google.cloud.retail.v2alpha";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2alpha";
option ruby_package = "Google::Cloud::Retail::V2alpha";

// Project level logging config to control what level of log will be generated
// and written to Cloud Logging.
message LoggingConfig {
  option (google.api.resource) = {
    type: "retail.googleapis.com/LoggingConfig"
    pattern: "projects/{project}/loggingConfig"
  };

  // The logging configurations for services supporting log generation.
  message LogGenerationRule {
    // The logging level.
    //
    // By default it is set to `LOG_WARNINGS_AND_ABOVE`.
    LoggingLevel logging_level = 1;

    // The log sample rate for INFO level log entries. You can use this to
    // reduce the number of entries generated for INFO level logs.
    //
    // DO NOT set this field if the
    // [logging_level][google.cloud.retail.v2alpha.LoggingConfig.LogGenerationRule.logging_level]
    // is not
    // [LoggingLevel.LOG_ALL][google.cloud.retail.v2alpha.LoggingConfig.LoggingLevel.LOG_ALL].
    // Otherwise, an INVALID_ARGUMENT error is returned.
    //
    // Sample rate for INFO logs defaults to 1 when unset (generate and send all
    // INFO logs to Cloud Logging). Its value must be greater than 0 and less
    // than or equal to 1.
    optional float info_log_sample_rate = 2;
  }

  // The granular logging configurations for supported services.
  message ServiceLogGenerationRule {
    // Required. Supported service names:
    // "CatalogService",
    // "CompletionService",
    // "ControlService",
    // "MerchantCenterStreaming",
    // "ModelService",
    // "PredictionService",
    // "ProductService",
    // "ServingConfigService",
    // "UserEventService",
    string service_name = 1 [(google.api.field_behavior) = REQUIRED];

    // The log generation rule that applies to this service.
    LogGenerationRule log_generation_rule = 3;
  }

  // The setting to control log generation.
  enum LoggingLevel {
    // Default value. Defaults to `LOG_FOR_WARNINGS_AND_ABOVE` if unset.
    LOGGING_LEVEL_UNSPECIFIED = 0;

    // No log will be generated and sent to Cloud Logging.
    LOGGING_DISABLED = 1;

    // Log for operations resulted in fatal error.
    LOG_ERRORS_AND_ABOVE = 2;

    // In addition to `LOG_ERRORS_AND_ABOVE`, also log for operations that have
    // soft errors, quality suggestions.
    LOG_WARNINGS_AND_ABOVE = 3;

    // Log all operations, including successful ones.
    LOG_ALL = 4;
  }

  // Required. Immutable. The name of the LoggingConfig singleton resource.
  // Format: projects/*/loggingConfig
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The log generation rule that applies by default to all services
  // supporting log generation. It can be overridden by
  // [ServiceLogGenerationRule][google.cloud.retail.v2alpha.LoggingConfig.ServiceLogGenerationRule]
  // for service level control.
  LogGenerationRule default_log_generation_rule = 2;

  // Controls logging configurations more granularly for each supported
  // service.
  //
  // This overrides the
  // [default_log_generation_rule][google.cloud.retail.v2alpha.LoggingConfig.default_log_generation_rule]
  // for the services specified. For those not mentioned, they will fallback to
  // the default log generation rule.
  repeated ServiceLogGenerationRule service_log_generation_rules = 4;
}

// Metadata that describes a Cloud Retail Project.
message Project {
  option (google.api.resource) = {
    type: "retail.googleapis.com/RetailProject"
    pattern: "projects/{project}/retailProject"
  };

  // Output only. Full resource name of the retail project, such as
  // `projects/{project_id_or_number}/retailProject`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Retail API solutions that the project has enrolled.
  repeated SolutionType enrolled_solutions = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Project level alert config.
message AlertConfig {
  option (google.api.resource) = {
    type: "retail.googleapis.com/AlertConfig"
    pattern: "projects/{project}/alertConfig"
  };

  // Alert policy for a customer.
  message AlertPolicy {
    // Recipient contact information.
    message Recipient {
      // Email address of the recipient.
      string email_address = 1;
    }

    // The enrollment status enum for alert policy.
    enum EnrollStatus {
      // Default value. Used for customers who have not responded to the
      // alert policy.
      ENROLL_STATUS_UNSPECIFIED = 0;

      // Customer is enrolled in this policy.
      ENROLLED = 1;

      // Customer declined this policy.
      DECLINED = 2;
    }

    // The feature that provides alerting capability.
    // Supported value:
    // - `search-data-quality` for retail search customers.
    // - `conv-data-quality` for retail conversation customers.
    string alert_group = 1;

    // The enrollment status of a customer.
    EnrollStatus enroll_status = 2;

    // Recipients for the alert policy.
    // One alert policy should not exceed 20 recipients.
    repeated Recipient recipients = 3;
  }

  // Required. Immutable. The name of the AlertConfig singleton resource.
  // Format: projects/*/alertConfig
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Alert policies for a customer.
  // They must be unique by [AlertPolicy.alert_group]
  repeated AlertPolicy alert_policies = 2;
}
