// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2alpha/branch.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Alpha";
option go_package = "cloud.google.com/go/retail/apiv2alpha/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "BranchServiceProto";
option java_package = "com.google.cloud.retail.v2alpha";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2alpha";
option ruby_package = "Google::Cloud::Retail::V2alpha";

// Service for [Branch][google.cloud.retail.v2alpha.Branch] Management
//
// [Branch][google.cloud.retail.v2alpha.Branch]es are automatically created when
// a [Catalog][google.cloud.retail.v2alpha.Catalog] is created. There are fixed
// three branches in each catalog, and may use
// [ListBranches][google.cloud.retail.v2alpha.BranchService.ListBranches] method
// to get the details of all branches.
service BranchService {
  option (google.api.default_host) = "retail.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all instances of [Branch][google.cloud.retail.v2alpha.Branch] under
  // the specified parent [Catalog][google.cloud.retail.v2alpha.Catalog].
  rpc ListBranches(ListBranchesRequest) returns (ListBranchesResponse) {
    option (google.api.http) = {
      get: "/v2alpha/{parent=projects/*/locations/*/catalogs/*}/branches"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a [Branch][google.cloud.retail.v2alpha.Branch].
  rpc GetBranch(GetBranchRequest) returns (Branch) {
    option (google.api.http) = {
      get: "/v2alpha/{name=projects/*/locations/*/catalogs/*/branches/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request for
// [BranchService.ListBranches][google.cloud.retail.v2alpha.BranchService.ListBranches]
// method.
message ListBranchesRequest {
  // Required. The parent catalog resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // The view to apply to the returned
  // [Branch][google.cloud.retail.v2alpha.Branch]. Defaults to
  // [Branch.BranchView.BASIC] if unspecified.
  // See documentation of fields of [Branch][google.cloud.retail.v2alpha.Branch]
  // to find what fields are excluded from BASIC view.
  BranchView view = 2;
}

// Response for
// [BranchService.ListBranches][google.cloud.retail.v2alpha.BranchService.ListBranches]
// method.
message ListBranchesResponse {
  // The Branches.
  repeated Branch branches = 1;
}

// Request for
// [BranchService.GetBranch][google.cloud.retail.v2alpha.BranchService.GetBranch]
// method.
message GetBranchRequest {
  // Required. The name of the branch to retrieve.
  // Format:
  // `projects/*/locations/global/catalogs/default_catalog/branches/some_branch_id`.
  //
  // "default_branch" can be used as a special branch_id, it returns the
  // default branch that has been set for the catalog.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Branch" }
  ];

  // The view to apply to the returned
  // [Branch][google.cloud.retail.v2alpha.Branch]. Defaults to
  // [Branch.BranchView.BASIC] if unspecified.
  // See documentation of fields of [Branch][google.cloud.retail.v2alpha.Branch]
  // to find what fields are excluded from BASIC view.
  BranchView view = 2;
}
