// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2alpha/common.proto";
import "google/cloud/retail/v2alpha/project.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Alpha";
option go_package = "cloud.google.com/go/retail/apiv2alpha/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ProjectServiceProto";
option java_package = "com.google.cloud.retail.v2alpha";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2alpha";
option ruby_package = "Google::Cloud::Retail::V2alpha";

// Service for settings at Project level.
service ProjectService {
  option (google.api.default_host) = "retail.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets the project.
  //
  // Throws `NOT_FOUND` if the project wasn't initialized for the Retail API
  // service.
  rpc GetProject(GetProjectRequest) returns (Project) {
    option (google.api.http) = {
      get: "/v2alpha/{name=projects/*/retailProject}"
    };
    option (google.api.method_signature) = "name";
  }

  // Accepts service terms for this project.
  // By making requests to this API, you agree to the terms of service linked
  // below.
  // https://cloud.google.com/retail/data-use-terms
  rpc AcceptTerms(AcceptTermsRequest) returns (Project) {
    option (google.api.http) = {
      post: "/v2alpha/{project=projects/*/retailProject}:acceptTerms"
      body: "*"
    };
    option (google.api.method_signature) = "project";
  }

  // The method enrolls a solution of type [Retail
  // Search][google.cloud.retail.v2alpha.SolutionType.SOLUTION_TYPE_SEARCH]
  // into a project.
  //
  // The [Recommendations AI solution
  // type][google.cloud.retail.v2alpha.SolutionType.SOLUTION_TYPE_RECOMMENDATION]
  // is enrolled by default when your project enables Retail API, so you don't
  // need to call the enrollSolution method for recommendations.
  rpc EnrollSolution(EnrollSolutionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha/{project=projects/*}:enrollSolution"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.retail.v2alpha.EnrollSolutionResponse"
      metadata_type: "google.cloud.retail.v2alpha.EnrollSolutionMetadata"
    };
  }

  // Lists all the retail API solutions the project has enrolled.
  rpc ListEnrolledSolutions(ListEnrolledSolutionsRequest)
      returns (ListEnrolledSolutionsResponse) {
    option (google.api.http) = {
      get: "/v2alpha/{parent=projects/*}:enrolledSolutions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the [LoggingConfig][google.cloud.retail.v2alpha.LoggingConfig] of the
  // requested project.
  rpc GetLoggingConfig(GetLoggingConfigRequest) returns (LoggingConfig) {
    option (google.api.http) = {
      get: "/v2alpha/{name=projects/*/loggingConfig}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the [LoggingConfig][google.cloud.retail.v2alpha.LoggingConfig] of
  // the requested project.
  rpc UpdateLoggingConfig(UpdateLoggingConfigRequest) returns (LoggingConfig) {
    option (google.api.http) = {
      patch: "/v2alpha/{logging_config.name=projects/*/loggingConfig}"
      body: "logging_config"
    };
    option (google.api.method_signature) = "logging_config,update_mask";
  }

  // Get the [AlertConfig][google.cloud.retail.v2alpha.AlertConfig] of the
  // requested project.
  rpc GetAlertConfig(GetAlertConfigRequest) returns (AlertConfig) {
    option (google.api.http) = {
      get: "/v2alpha/{name=projects/*/alertConfig}"
    };
    option (google.api.method_signature) = "name";
  }

  // Update the alert config of the requested project.
  rpc UpdateAlertConfig(UpdateAlertConfigRequest) returns (AlertConfig) {
    option (google.api.http) = {
      patch: "/v2alpha/{alert_config.name=projects/*/alertConfig}"
      body: "alert_config"
    };
    option (google.api.method_signature) = "alert_config,update_mask";
  }
}

// Request for GetProject method.
message GetProjectRequest {
  // Required. Full resource name of the project. Format:
  // `projects/{project_number_or_id}/retailProject`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "retail.googleapis.com/RetailProject"
    }
  ];
}

// Request for AcceptTerms method.
message AcceptTermsRequest {
  // Required. Full resource name of the project. Format:
  // `projects/{project_number_or_id}/retailProject`
  string project = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "retail.googleapis.com/RetailProject"
    }
  ];
}

// Request for EnrollSolution method.
message EnrollSolutionRequest {
  // Required. Full resource name of parent. Format:
  // `projects/{project_number_or_id}`
  string project = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. Solution to enroll.
  SolutionType solution = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response for EnrollSolution method.
message EnrollSolutionResponse {
  // Retail API solution that the project has enrolled.
  SolutionType enrolled_solution = 1;
}

// Metadata related to the EnrollSolution method.
// This will be returned by the google.longrunning.Operation.metadata field.
message EnrollSolutionMetadata {}

// Request for ListEnrolledSolutions method.
message ListEnrolledSolutionsRequest {
  // Required. Full resource name of parent. Format:
  // `projects/{project_number_or_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];
}

// Response for ListEnrolledSolutions method.
message ListEnrolledSolutionsResponse {
  // Retail API solutions that the project has enrolled.
  repeated SolutionType enrolled_solutions = 1;
}

// Request for
// [ProjectService.GetLoggingConfig][google.cloud.retail.v2alpha.ProjectService.GetLoggingConfig]
// method.
message GetLoggingConfigRequest {
  // Required. Full LoggingConfig resource name. Format:
  // projects/{project_number}/loggingConfig
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "retail.googleapis.com/LoggingConfig"
    }
  ];
}

// Request for
// [ProjectService.UpdateLoggingConfig][google.cloud.retail.v2alpha.ProjectService.UpdateLoggingConfig]
// method.
message UpdateLoggingConfigRequest {
  // Required. The [LoggingConfig][google.cloud.retail.v2alpha.LoggingConfig] to
  // update.
  //
  // If the caller does not have permission to update the
  // [LoggingConfig][google.cloud.retail.v2alpha.LoggingConfig], then a
  // PERMISSION_DENIED error is returned.
  //
  // If the [LoggingConfig][google.cloud.retail.v2alpha.LoggingConfig] to update
  // does not exist, a NOT_FOUND error is returned.
  LoggingConfig logging_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates which fields in the provided
  // [LoggingConfig][google.cloud.retail.v2alpha.LoggingConfig] to update. The
  // following are the only supported fields:
  //
  // * [LoggingConfig.default_log_generation_rule][google.cloud.retail.v2alpha.LoggingConfig.default_log_generation_rule]
  // * [LoggingConfig.service_log_generation_rules][google.cloud.retail.v2alpha.LoggingConfig.service_log_generation_rules]
  //
  // If not set, all supported fields are updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request for
// [ProjectService.GetAlertConfig][google.cloud.retail.v2alpha.ProjectService.GetAlertConfig]
// method.
message GetAlertConfigRequest {
  // Required. Full AlertConfig resource name. Format:
  // projects/{project_number}/alertConfig
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "retail.googleapis.com/AlertConfig"
    }
  ];
}

// Request for
// [ProjectService.UpdateAlertConfig][google.cloud.retail.v2alpha.ProjectService.UpdateAlertConfig]
// method.
message UpdateAlertConfigRequest {
  // Required. The [AlertConfig][google.cloud.retail.v2alpha.AlertConfig] to
  // update.
  //
  // If the caller does not have permission to update the
  // [AlertConfig][google.cloud.retail.v2alpha.AlertConfig], then a
  // PERMISSION_DENIED error is returned.
  //
  // If the [AlertConfig][google.cloud.retail.v2alpha.AlertConfig] to update
  // does not exist, a NOT_FOUND error is returned.
  AlertConfig alert_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates which fields in the provided
  // [AlertConfig][google.cloud.retail.v2alpha.AlertConfig] to update. If not
  // set, all supported fields are updated.
  google.protobuf.FieldMask update_mask = 2;
}
