type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  python:
    package_name: google.cloud.retail_v2alpha.gapic
  go:
    package_name: cloud.google.com/go/retail/apiv2alpha
  csharp:
    package_name: Google.Retail.V2alpha
  ruby:
    package_name: Google::Cloud::Retail::V2alpha
  php:
    package_name: Google\Cloud\Retail\V2alpha
  nodejs:
    package_name: retail.v2alpha
    domain_layer_location: google-cloud
