// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2alpha/product.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Alpha";
option go_package = "cloud.google.com/go/retail/apiv2alpha/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "BranchProto";
option java_package = "com.google.cloud.retail.v2alpha";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2alpha";
option ruby_package = "Google::Cloud::Retail::V2alpha";

// A view that specifies different level of fields of a
// [Branch][google.cloud.retail.v2alpha.Branch] to show in responses.
enum BranchView {
  // The value when it's unspecified. This defaults to the BASIC view.
  BRANCH_VIEW_UNSPECIFIED = 0;

  // Includes basic metadata about the branch, but not statistical fields.
  // See documentation of fields of [Branch][google.cloud.retail.v2alpha.Branch]
  // to find what fields are excluded from BASIC view.
  BRANCH_VIEW_BASIC = 1;

  // Includes all fields of a [Branch][google.cloud.retail.v2alpha.Branch].
  BRANCH_VIEW_FULL = 2;
}

// A data branch that stores all instances of
// [Product][google.cloud.retail.v2alpha.Product]s.
message Branch {
  option (google.api.resource) = {
    type: "retail.googleapis.com/Branch"
    pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/branches/{branch}"
  };

  // A statistic about the number of products in a branch.
  message ProductCountStatistic {
    // Scope of what products are included for this count.
    enum ProductCountScope {
      // Default value for enum. This value is not used in the API response.
      PRODUCT_COUNT_SCOPE_UNSPECIFIED = 0;

      // Scope for all existing products in the branch. Useful for understanding
      // how many products there are in a branch.
      ALL_PRODUCTS = 1;

      // Scope for products created or updated in the last 24 hours.
      LAST_24_HOUR_UPDATE = 2;
    }

    // [ProductCountScope] of the [counts].
    ProductCountScope scope = 1;

    // The number of products in
    // [scope][google.cloud.retail.v2alpha.Branch.ProductCountStatistic.scope]
    // broken down into different groups.
    //
    // The key is a group representing a set of products, and the value is the
    // number of products in that group.
    // Note: keys in this map may change over time.
    //
    // Possible keys:
    // * "primary-in-stock", products have
    // [Product.Type.PRIMARY][google.cloud.retail.v2alpha.Product.Type.PRIMARY]
    // type and
    // [Product.Availability.IN_STOCK][google.cloud.retail.v2alpha.Product.Availability.IN_STOCK]
    // availability.
    //
    // * "primary-out-of-stock", products have
    // [Product.Type.PRIMARY][google.cloud.retail.v2alpha.Product.Type.PRIMARY]
    // type and
    // [Product.Availability.OUT_OF_STOCK][google.cloud.retail.v2alpha.Product.Availability.OUT_OF_STOCK]
    // availability.
    //
    // * "primary-preorder", products have
    // [Product.Type.PRIMARY][google.cloud.retail.v2alpha.Product.Type.PRIMARY]
    // type and
    // [Product.Availability.PREORDER][google.cloud.retail.v2alpha.Product.Availability.PREORDER]
    // availability.
    //
    // * "primary-backorder", products have
    // [Product.Type.PRIMARY][google.cloud.retail.v2alpha.Product.Type.PRIMARY]
    // type and
    // [Product.Availability.BACKORDER][google.cloud.retail.v2alpha.Product.Availability.BACKORDER]
    // availability.
    //
    // * "variant-in-stock", products have
    // [Product.Type.VARIANT][google.cloud.retail.v2alpha.Product.Type.VARIANT]
    // type and
    // [Product.Availability.IN_STOCK][google.cloud.retail.v2alpha.Product.Availability.IN_STOCK]
    // availability.
    //
    // * "variant-out-of-stock", products have
    // [Product.Type.VARIANT][google.cloud.retail.v2alpha.Product.Type.VARIANT]
    // type and
    // [Product.Availability.OUT_OF_STOCK][google.cloud.retail.v2alpha.Product.Availability.OUT_OF_STOCK]
    // availability.
    //
    // * "variant-preorder", products have
    // [Product.Type.VARIANT][google.cloud.retail.v2alpha.Product.Type.VARIANT]
    // type and
    // [Product.Availability.PREORDER][google.cloud.retail.v2alpha.Product.Availability.PREORDER]
    // availability.
    //
    // * "variant-backorder", products have
    // [Product.Type.VARIANT][google.cloud.retail.v2alpha.Product.Type.VARIANT]
    // type and
    // [Product.Availability.BACKORDER][google.cloud.retail.v2alpha.Product.Availability.BACKORDER]
    // availability.
    //
    // * "price-discounted", products have [Product.price_info.price] <
    // [Product.price_info.original_price].
    map<string, int64> counts = 2;
  }

  // Metric measured on a group of
  // [Product][google.cloud.retail.v2alpha.Product]s against a certain quality
  // requirement. Contains the number of products that pass the check and the
  // number of products that don't.
  message QualityMetric {
    // The key that represents a quality requirement rule.
    //
    // Supported keys:
    // * "has-valid-uri": product has a valid and accessible
    // [uri][google.cloud.retail.v2alpha.Product.uri].
    //
    // * "available-expire-time-conformance":
    // [Product.available_time][google.cloud.retail.v2alpha.Product.available_time]
    // is early than "now", and
    // [Product.expire_time][google.cloud.retail.v2alpha.Product.expire_time] is
    // greater than "now".
    //
    // * "has-searchable-attributes": product has at least one
    // [attribute][google.cloud.retail.v2alpha.Product.attributes] set to
    // searchable.
    //
    // * "has-description": product has non-empty
    // [description][google.cloud.retail.v2alpha.Product.description].
    //
    // * "has-at-least-bigram-title": Product
    // [title][google.cloud.retail.v2alpha.Product.title] has at least two
    // words. A comprehensive title helps to improve search quality.
    //
    // * "variant-has-image": the
    // [variant][google.cloud.retail.v2alpha.Product.Type.VARIANT] products has
    // at least one [image][google.cloud.retail.v2alpha.Product.images]. You may
    // ignore this metric if all your products are at
    // [primary][google.cloud.retail.v2alpha.Product.Type.PRIMARY] level.
    //
    // * "variant-has-price-info": the
    // [variant][google.cloud.retail.v2alpha.Product.Type.VARIANT] products has
    // [price_info][google.cloud.retail.v2alpha.Product.price_info] set. You may
    // ignore this metric if all your products are at
    // [primary][google.cloud.retail.v2alpha.Product.Type.PRIMARY] level.
    //
    // * "has-publish-time": product has non-empty
    // [publish_time][google.cloud.retail.v2alpha.Product.publish_time].
    string requirement_key = 1;

    // Number of products passing the quality requirement check. We only check
    // searchable products.
    int32 qualified_product_count = 2;

    // Number of products failing the quality requirement check. We only check
    // searchable products.
    int32 unqualified_product_count = 3;

    // Value from 0 to 100 representing the suggested percentage of products
    // that meet the quality requirements to get good search and recommendation
    // performance. 100 * (qualified_product_count) /
    // (qualified_product_count + unqualified_product_count) should be greater
    // or equal to this suggestion.
    double suggested_quality_percent_threshold = 4;

    // A list of a maximum of 100 sample products that do not qualify for
    // this requirement.
    //
    // This field is only populated in the response to
    // [BranchService.GetBranch][google.cloud.retail.v2alpha.BranchService.GetBranch]
    // API, and is always empty for
    // [BranchService.ListBranches][google.cloud.retail.v2alpha.BranchService.ListBranches].
    //
    // Only the following fields are set in the
    // [Product][google.cloud.retail.v2alpha.Product].
    //
    // * [Product.name][google.cloud.retail.v2alpha.Product.name]
    // * [Product.id][google.cloud.retail.v2alpha.Product.id]
    // * [Product.title][google.cloud.retail.v2alpha.Product.title]
    repeated Product unqualified_sample_products = 5;
  }

  // Immutable. Full resource name of the branch, such as
  // `projects/*/locations/global/catalogs/default_catalog/branches/branch_id`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Human readable name of the branch to display in the UI.
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether this branch is set as the default branch of
  // its parent catalog.
  bool is_default = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp of last import through
  // [ProductService.ImportProducts][google.cloud.retail.v2alpha.ProductService.ImportProducts].
  // Empty value means no import has been made to this branch.
  google.protobuf.Timestamp last_product_import_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Statistics for number of products in the branch, provided for
  // different
  // [scopes][google.cloud.retail.v2alpha.Branch.ProductCountStatistic.ProductCountScope].
  //
  // This field is not populated in [BranchView.BASIC][] view.
  repeated ProductCountStatistic product_count_stats = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The quality metrics measured among products of this branch.
  //
  // See
  // [QualityMetric.requirement_key][google.cloud.retail.v2alpha.Branch.QualityMetric.requirement_key]
  // for supported metrics. Metrics could be missing if failed to retrieve.
  //
  // This field is not populated in [BranchView.BASIC][] view.
  repeated QualityMetric quality_metrics = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
