// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2alpha/merchant_center_account_link.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Alpha";
option go_package = "cloud.google.com/go/retail/apiv2alpha/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "MerchantCenterAccountLinkServiceProto";
option java_package = "com.google.cloud.retail.v2alpha";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2alpha";
option ruby_package = "Google::Cloud::Retail::V2alpha";

// Merchant Center Link service to link a Branch to a Merchant Center Account.
service MerchantCenterAccountLinkService {
  option (google.api.default_host) = "retail.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all
  // [MerchantCenterAccountLink][google.cloud.retail.v2alpha.MerchantCenterAccountLink]s
  // under the specified parent [Catalog][google.cloud.retail.v2alpha.Catalog].
  rpc ListMerchantCenterAccountLinks(ListMerchantCenterAccountLinksRequest)
      returns (ListMerchantCenterAccountLinksResponse) {
    option (google.api.http) = {
      get: "/v2alpha/{parent=projects/*/locations/*/catalogs/*}/merchantCenterAccountLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a
  // [MerchantCenterAccountLink][google.cloud.retail.v2alpha.MerchantCenterAccountLink].
  rpc CreateMerchantCenterAccountLink(CreateMerchantCenterAccountLinkRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha/{parent=projects/*/locations/*/catalogs/*}/merchantCenterAccountLinks"
      body: "merchant_center_account_link"
    };
    option (google.api.method_signature) =
        "parent,merchant_center_account_link";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.retail.v2alpha.MerchantCenterAccountLink"
      metadata_type: "google.cloud.retail.v2alpha.CreateMerchantCenterAccountLinkMetadata"
    };
  }

  // Deletes a
  // [MerchantCenterAccountLink][google.cloud.retail.v2alpha.MerchantCenterAccountLink].
  // If the
  // [MerchantCenterAccountLink][google.cloud.retail.v2alpha.MerchantCenterAccountLink]
  // to delete does not exist, a NOT_FOUND error is returned.
  rpc DeleteMerchantCenterAccountLink(DeleteMerchantCenterAccountLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2alpha/{name=projects/*/locations/*/catalogs/*/merchantCenterAccountLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request for
// [MerchantCenterAccountLinkService.ListMerchantCenterAccountLinks][google.cloud.retail.v2alpha.MerchantCenterAccountLinkService.ListMerchantCenterAccountLinks]
// method.
message ListMerchantCenterAccountLinksRequest {
  // Required. The parent Catalog of the resource.
  // It must match this format:
  // `projects/{PROJECT_NUMBER}/locations/global/catalogs/{CATALOG_ID}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];
}

// Response for
// [MerchantCenterAccountLinkService.ListMerchantCenterAccountLinks][google.cloud.retail.v2alpha.MerchantCenterAccountLinkService.ListMerchantCenterAccountLinks]
// method.
message ListMerchantCenterAccountLinksResponse {
  // The links.
  repeated MerchantCenterAccountLink merchant_center_account_links = 1;
}

// Request for
// [MerchantCenterAccountLinkService.CreateMerchantCenterAccountLink][google.cloud.retail.v2alpha.MerchantCenterAccountLinkService.CreateMerchantCenterAccountLink]
// method.
message CreateMerchantCenterAccountLinkRequest {
  // Required. The branch resource where this MerchantCenterAccountLink will be
  // created. Format:
  // `projects/{PROJECT_NUMBER}/locations/global/catalogs/{CATALOG_ID}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // Required. The
  // [MerchantCenterAccountLink][google.cloud.retail.v2alpha.MerchantCenterAccountLink]
  // to create.
  //
  // If the caller does not have permission to create the
  // [MerchantCenterAccountLink][google.cloud.retail.v2alpha.MerchantCenterAccountLink],
  // regardless of whether or not it exists, a PERMISSION_DENIED error is
  // returned.
  MerchantCenterAccountLink merchant_center_account_link = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for
// [MerchantCenterAccountLinkService.DeleteMerchantCenterAccountLink][google.cloud.retail.v2alpha.MerchantCenterAccountLinkService.DeleteMerchantCenterAccountLink]
// method.
message DeleteMerchantCenterAccountLinkRequest {
  // Required. Full resource name. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/merchantCenterAccountLinks/{merchant_center_account_link_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "retail.googleapis.com/MerchantCenterAccountLink"
    }
  ];
}
