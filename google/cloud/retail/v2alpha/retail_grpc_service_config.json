{"methodConfig": [{"name": [{"service": "google.cloud.retail.v2alpha.CatalogService"}, {"service": "google.cloud.retail.v2alpha.CompletionService"}, {"service": "google.cloud.retail.v2alpha.PromotionService"}, {"service": "google.cloud.retail.v2alpha.PredictionService"}, {"service": "google.cloud.retail.v2alpha.SearchService"}, {"service": "google.cloud.retail.v2alpha.RequirementService"}, {"service": "google.cloud.retail.v2alpha.SampleService"}], "timeout": "5s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "5s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2alpha.UserEventService"}], "timeout": "10s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "5s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2alpha.PredictionService", "method": "BatchPredict"}], "timeout": "30s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2alpha.BranchService"}, {"service": "google.cloud.retail.v2alpha.ProductService"}, {"service": "google.cloud.retail.v2alpha.ProjectService"}, {"service": "google.cloud.retail.v2alpha.UserEventService", "method": "PurgeUserEvents"}], "timeout": "30s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2alpha.AnalyticsService", "method": "ExportAnalyticsMetrics"}, {"service": "google.cloud.retail.v2alpha.ModelService"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.longrunning.Operations", "method": "ListOperations"}, {"service": "google.cloud.retail.v2alpha.ProductService", "method": "ImportProducts"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "300s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.cloud.retail.v2alpha.UserEventService", "method": "ImportUserEvents"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "300s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}