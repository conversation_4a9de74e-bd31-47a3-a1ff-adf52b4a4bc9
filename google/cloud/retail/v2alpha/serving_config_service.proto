// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2alpha/serving_config.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Alpha";
option go_package = "cloud.google.com/go/retail/apiv2alpha/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ServingConfigServiceProto";
option java_package = "com.google.cloud.retail.v2alpha";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2alpha";
option ruby_package = "Google::Cloud::Retail::V2alpha";

// Service for modifying ServingConfig.
service ServingConfigService {
  option (google.api.default_host) = "retail.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a ServingConfig.
  //
  // A maximum of 100
  // [ServingConfig][google.cloud.retail.v2alpha.ServingConfig]s are allowed in
  // a [Catalog][google.cloud.retail.v2alpha.Catalog], otherwise a
  // FAILED_PRECONDITION error is returned.
  rpc CreateServingConfig(CreateServingConfigRequest) returns (ServingConfig) {
    option (google.api.http) = {
      post: "/v2alpha/{parent=projects/*/locations/*/catalogs/*}/servingConfigs"
      body: "serving_config"
    };
    option (google.api.method_signature) =
        "parent,serving_config,serving_config_id";
  }

  // Deletes a ServingConfig.
  //
  // Returns a NotFound error if the ServingConfig does not exist.
  rpc DeleteServingConfig(DeleteServingConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2alpha/{name=projects/*/locations/*/catalogs/*/servingConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a ServingConfig.
  rpc UpdateServingConfig(UpdateServingConfigRequest) returns (ServingConfig) {
    option (google.api.http) = {
      patch: "/v2alpha/{serving_config.name=projects/*/locations/*/catalogs/*/servingConfigs/*}"
      body: "serving_config"
    };
    option (google.api.method_signature) = "serving_config,update_mask";
  }

  // Gets a ServingConfig.
  //
  // Returns a NotFound error if the ServingConfig does not exist.
  rpc GetServingConfig(GetServingConfigRequest) returns (ServingConfig) {
    option (google.api.http) = {
      get: "/v2alpha/{name=projects/*/locations/*/catalogs/*/servingConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all ServingConfigs linked to this catalog.
  rpc ListServingConfigs(ListServingConfigsRequest)
      returns (ListServingConfigsResponse) {
    option (google.api.http) = {
      get: "/v2alpha/{parent=projects/*/locations/*/catalogs/*}/servingConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Enables a Control on the specified ServingConfig.
  // The control is added in the last position of the list of controls
  // it belongs to (e.g. if it's a facet spec control it will be applied
  // in the last position of servingConfig.facetSpecIds)
  // Returns a ALREADY_EXISTS error if the control has already been applied.
  // Returns a FAILED_PRECONDITION error if the addition could exceed maximum
  // number of control allowed for that type of control.
  rpc AddControl(AddControlRequest) returns (ServingConfig) {
    option (google.api.http) = {
      post: "/v2alpha/{serving_config=projects/*/locations/*/catalogs/*/servingConfigs/*}:addControl"
      body: "*"
    };
    option (google.api.method_signature) = "serving_config";
  }

  // Disables a Control on the specified ServingConfig.
  // The control is removed from the ServingConfig.
  // Returns a NOT_FOUND error if the Control is not enabled for the
  // ServingConfig.
  rpc RemoveControl(RemoveControlRequest) returns (ServingConfig) {
    option (google.api.http) = {
      post: "/v2alpha/{serving_config=projects/*/locations/*/catalogs/*/servingConfigs/*}:removeControl"
      body: "*"
    };
    option (google.api.method_signature) = "serving_config";
  }
}

// Request for CreateServingConfig method.
message CreateServingConfigRequest {
  // Required. Full resource name of parent. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // Required. The ServingConfig to create.
  ServingConfig serving_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the ServingConfig, which will become the final
  // component of the ServingConfig's resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are /[a-z][0-9]-_/.
  string serving_config_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for UpdateServingConfig method.
message UpdateServingConfigRequest {
  // Required. The ServingConfig to update.
  ServingConfig serving_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates which fields in the provided
  // [ServingConfig][google.cloud.retail.v2alpha.ServingConfig] to update. The
  // following are NOT supported:
  //
  // * [ServingConfig.name][google.cloud.retail.v2alpha.ServingConfig.name]
  //
  // If not set, all supported fields are updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request for DeleteServingConfig method.
message DeleteServingConfigRequest {
  // Required. The resource name of the ServingConfig to delete. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/servingConfigs/{serving_config_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "retail.googleapis.com/ServingConfig"
    }
  ];
}

// Request for GetServingConfig method.
message GetServingConfigRequest {
  // Required. The resource name of the ServingConfig to get. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/servingConfigs/{serving_config_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "retail.googleapis.com/ServingConfig"
    }
  ];
}

// Request for ListServingConfigs method.
message ListServingConfigsRequest {
  // Required. The catalog resource name. Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // Optional. Maximum number of results to return. If unspecified, defaults
  // to 100. If a value greater than 100 is provided, at most 100 results are
  // returned.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListServingConfigs` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response for ListServingConfigs method.
message ListServingConfigsResponse {
  // All the ServingConfigs for a given catalog.
  repeated ServingConfig serving_configs = 1;

  // Pagination token, if not returned indicates the last page.
  string next_page_token = 2;
}

// Request for AddControl method.
message AddControlRequest {
  // Required. The source ServingConfig resource name . Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/servingConfigs/{serving_config_id}`
  string serving_config = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "retail.googleapis.com/ServingConfig"
    }
  ];

  // Required. The id of the control to apply. Assumed to be in the same catalog
  // as the serving config - if id is not found a NOT_FOUND error is returned.
  string control_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for RemoveControl method.
message RemoveControlRequest {
  // Required. The source ServingConfig resource name . Format:
  // `projects/{project_number}/locations/{location_id}/catalogs/{catalog_id}/servingConfigs/{serving_config_id}`
  string serving_config = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "retail.googleapis.com/ServingConfig"
    }
  ];

  // Required. The id of the control to apply. Assumed to be in the same catalog
  // as the serving config.
  string control_id = 2 [(google.api.field_behavior) = REQUIRED];
}
