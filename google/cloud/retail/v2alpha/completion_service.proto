// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/retail/v2alpha/common.proto";
import "google/cloud/retail/v2alpha/import_config.proto";
import "google/cloud/retail/v2alpha/search_service.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Alpha";
option go_package = "cloud.google.com/go/retail/apiv2alpha/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "CompletionServiceProto";
option java_package = "com.google.cloud.retail.v2alpha";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2alpha";
option ruby_package = "Google::Cloud::Retail::V2alpha";

// Autocomplete service for retail.
//
// This feature is only available for users who have Retail Search enabled.
// Enable Retail Search on Cloud Console before using this feature.
service CompletionService {
  option (google.api.default_host) = "retail.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Completes the specified prefix with keyword suggestions.
  //
  // This feature is only available for users who have Retail Search enabled.
  // Enable Retail Search on Cloud Console before using this feature.
  rpc CompleteQuery(CompleteQueryRequest) returns (CompleteQueryResponse) {
    option (google.api.http) = {
      get: "/v2alpha/{catalog=projects/*/locations/*/catalogs/*}:completeQuery"
    };
  }

  // Bulk import of processed completion dataset.
  //
  // Request processing is asynchronous. Partial updating is not supported.
  //
  // The operation is successfully finished only after the imported suggestions
  // are indexed successfully and ready for serving. The process takes hours.
  //
  // This feature is only available for users who have Retail Search enabled.
  // Enable Retail Search on Cloud Console before using this feature.
  rpc ImportCompletionData(ImportCompletionDataRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha/{parent=projects/*/locations/*/catalogs/*}/completionData:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.retail.v2alpha.ImportCompletionDataResponse"
      metadata_type: "google.cloud.retail.v2alpha.ImportMetadata"
    };
  }
}

// Autocomplete parameters.
message CompleteQueryRequest {
  // Required. Catalog for which the completion is performed.
  //
  // Full resource name of catalog, such as
  // `projects/*/locations/global/catalogs/default_catalog`.
  string catalog = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "retail.googleapis.com/Catalog" }
  ];

  // Required. The query used to generate suggestions.
  //
  // The maximum number of allowed characters is 255.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Required field. A unique identifier for tracking visitors. For example,
  // this could be implemented with an HTTP cookie, which should be able to
  // uniquely identify a visitor on a single device. This unique identifier
  // should not change if the visitor logs in or out of the website.
  //
  // The field must be a UTF-8 encoded string with a length limit of 128
  // characters. Otherwise, an INVALID_ARGUMENT error is returned.
  string visitor_id = 7;

  // Note that this field applies for `user-data` dataset only. For requests
  // with `cloud-retail` dataset, setting this field has no effect.
  //
  // The language filters applied to the output suggestions. If set, it should
  // contain the language of the query. If not set, suggestions are returned
  // without considering language restrictions. This is the BCP-47 language
  // code, such as "en-US" or "sr-Latn". For more information, see [Tags for
  // Identifying Languages](https://tools.ietf.org/html/bcp47). The maximum
  // number of language codes is 3.
  repeated string language_codes = 3;

  // The device type context for completion suggestions. We recommend that you
  // leave this field empty.
  //
  // It can apply different suggestions on different device types, e.g.
  // `DESKTOP`, `MOBILE`. If it is empty, the suggestions are across all device
  // types.
  //
  // Supported formats:
  //
  // * `UNKNOWN_DEVICE_TYPE`
  //
  // * `DESKTOP`
  //
  // * `MOBILE`
  //
  // * A customized string starts with `OTHER_`, e.g. `OTHER_IPHONE`.
  string device_type = 4;

  // Determines which dataset to use for fetching completion. "user-data" will
  // use the imported dataset through
  // [CompletionService.ImportCompletionData][google.cloud.retail.v2alpha.CompletionService.ImportCompletionData].
  // "cloud-retail" will use the dataset generated by cloud retail based on user
  // events. If leave empty, it will use the "user-data".
  //
  // Current supported values:
  //
  // * user-data
  //
  // * cloud-retail:
  //   This option requires enabling auto-learning function first. See
  //   [guidelines](https://cloud.google.com/retail/docs/completion-overview#generated-completion-dataset).
  string dataset = 6;

  // Completion max suggestions. If left unset or set to 0, then will fallback
  // to the configured value
  // [CompletionConfig.max_suggestions][google.cloud.retail.v2alpha.CompletionConfig.max_suggestions].
  //
  // The maximum allowed max suggestions is 20. If it is set higher, it will be
  // capped by 20.
  int32 max_suggestions = 5;

  // If true, attribute suggestions are enabled and provided in the response.
  //
  // This field is only available for the "cloud-retail" dataset.
  bool enable_attribute_suggestions = 9;

  // The entity for customers who run multiple entities, domains, sites, or
  // regions, for example, `Google US`, `Google Ads`, `Waymo`,
  // `google.com`, `youtube.com`, etc.
  // If this is set, it must be an exact match with
  // [UserEvent.entity][google.cloud.retail.v2alpha.UserEvent.entity] to get
  // per-entity autocomplete results.
  string entity = 10;
}

// Response of the autocomplete query.
message CompleteQueryResponse {
  // Resource that represents completion results.
  message CompletionResult {
    // The suggestion for the query.
    string suggestion = 1;

    // Custom attributes for the suggestion term.
    //
    // * For "user-data", the attributes are additional custom attributes
    // ingested through BigQuery.
    //
    // * For "cloud-retail", the attributes are product attributes generated
    // by Cloud Retail. It requires
    // [UserEvent.product_details][google.cloud.retail.v2alpha.UserEvent.product_details]
    // is imported properly.
    map<string, CustomAttribute> attributes = 2;

    // Facet information for the suggestion term. Gives the number of items
    // resulting from a search with this suggestion term for each facet.
    //
    // This is an experimental feature for limited customers. If you want to
    // receive this facet information, reach out to the Retail support team.
    repeated SearchResponse.Facet facets = 3;

    // Total number of products associated with a search with this suggestion.
    //
    // This is an experimental feature for limited customers. If you want to
    // receive this product count information, reach out to the Retail support
    // team.
    int32 total_product_count = 4;
  }

  // Deprecated: Recent search of this user.
  message RecentSearchResult {
    option deprecated = true;

    // The recent search query.
    string recent_search = 1;
  }

  // Resource that represents attribute results.
  // The list of suggestions for the attribute.
  message AttributeResult {
    repeated string suggestions = 1;
  }

  // Results of the matching suggestions. The result list is ordered and the
  // first result is top suggestion.
  repeated CompletionResult completion_results = 1;

  // A unique complete token. This should be included in the
  // [UserEvent.completion_detail][google.cloud.retail.v2alpha.UserEvent.completion_detail]
  // for search events resulting from this completion, which enables accurate
  // attribution of complete model performance.
  string attribution_token = 2;

  // Deprecated. Matched recent searches of this user. The maximum number of
  // recent searches is 10. This field is a restricted feature. If you want to
  // enable it, contact Retail Search support.
  //
  // This feature is only available when
  // [CompleteQueryRequest.visitor_id][google.cloud.retail.v2alpha.CompleteQueryRequest.visitor_id]
  // field is set and [UserEvent][google.cloud.retail.v2alpha.UserEvent] is
  // imported. The recent searches satisfy the follow rules:
  //
  //  * They are ordered from latest to oldest.
  //
  //  * They are matched with
  //  [CompleteQueryRequest.query][google.cloud.retail.v2alpha.CompleteQueryRequest.query]
  //  case insensitively.
  //
  //  * They are transformed to lower case.
  //
  //  * They are UTF-8 safe.
  //
  // Recent searches are deduplicated. More recent searches will be reserved
  // when duplication happens.
  repeated RecentSearchResult recent_search_results = 3 [deprecated = true];

  // A map of matched attribute suggestions. This field is only available for
  // "cloud-retail" dataset.
  //
  // Current supported keys:
  //
  // * `brands`
  //
  // * `categories`
  map<string, AttributeResult> attribute_results = 4;
}
