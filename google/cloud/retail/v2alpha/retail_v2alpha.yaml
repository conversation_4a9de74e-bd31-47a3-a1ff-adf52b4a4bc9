type: google.api.Service
config_version: 3
name: retail.googleapis.com
title: Vertex AI Search for Retail API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.retail.v2alpha.AnalyticsService
- name: google.cloud.retail.v2alpha.BranchService
- name: google.cloud.retail.v2alpha.CatalogService
- name: google.cloud.retail.v2alpha.CompletionService
- name: google.cloud.retail.v2alpha.ControlService
- name: google.cloud.retail.v2alpha.GenerativeQuestionService
- name: google.cloud.retail.v2alpha.MerchantCenterAccountLinkService
- name: google.cloud.retail.v2alpha.ModelService
- name: google.cloud.retail.v2alpha.PredictionService
- name: google.cloud.retail.v2alpha.ProductService
- name: google.cloud.retail.v2alpha.ProjectService
- name: google.cloud.retail.v2alpha.SearchService
- name: google.cloud.retail.v2alpha.ServingConfigService
- name: google.cloud.retail.v2alpha.UserEventService
- name: google.longrunning.Operations

types:
- name: google.cloud.retail.logging.ErrorLog
- name: google.cloud.retail.v2alpha.AddFulfillmentPlacesMetadata
- name: google.cloud.retail.v2alpha.AddFulfillmentPlacesResponse
- name: google.cloud.retail.v2alpha.AddLocalInventoriesMetadata
- name: google.cloud.retail.v2alpha.AddLocalInventoriesResponse
- name: google.cloud.retail.v2alpha.CreateMerchantCenterAccountLinkMetadata
- name: google.cloud.retail.v2alpha.CreateModelMetadata
- name: google.cloud.retail.v2alpha.EnrollSolutionMetadata
- name: google.cloud.retail.v2alpha.EnrollSolutionResponse
- name: google.cloud.retail.v2alpha.ExportAnalyticsMetricsResponse
- name: google.cloud.retail.v2alpha.ExportErrorsConfig
- name: google.cloud.retail.v2alpha.ExportMetadata
- name: google.cloud.retail.v2alpha.ExportProductsResponse
- name: google.cloud.retail.v2alpha.ExportUserEventsResponse
- name: google.cloud.retail.v2alpha.ImportCompletionDataResponse
- name: google.cloud.retail.v2alpha.ImportErrorsConfig
- name: google.cloud.retail.v2alpha.ImportMetadata
- name: google.cloud.retail.v2alpha.ImportProductsResponse
- name: google.cloud.retail.v2alpha.ImportUserEventsResponse
- name: google.cloud.retail.v2alpha.MerchantCenterAccountLink
- name: google.cloud.retail.v2alpha.Model
- name: google.cloud.retail.v2alpha.PurgeMetadata
- name: google.cloud.retail.v2alpha.PurgeProductsMetadata
- name: google.cloud.retail.v2alpha.PurgeProductsResponse
- name: google.cloud.retail.v2alpha.PurgeUserEventsResponse
- name: google.cloud.retail.v2alpha.RejoinUserEventsMetadata
- name: google.cloud.retail.v2alpha.RejoinUserEventsResponse
- name: google.cloud.retail.v2alpha.RemoveFulfillmentPlacesMetadata
- name: google.cloud.retail.v2alpha.RemoveFulfillmentPlacesResponse
- name: google.cloud.retail.v2alpha.RemoveLocalInventoriesMetadata
- name: google.cloud.retail.v2alpha.RemoveLocalInventoriesResponse
- name: google.cloud.retail.v2alpha.SetInventoryMetadata
- name: google.cloud.retail.v2alpha.SetInventoryResponse
- name: google.cloud.retail.v2alpha.TuneModelMetadata
- name: google.cloud.retail.v2alpha.TuneModelResponse

documentation:
  summary: |-
    Vertex AI Search for Retail API is made up of Retail Search, Browse and
    Recommendations. These discovery AI solutions help you implement
    personalized search, browse and recommendations, based on machine learning
    models, across your websites and mobile applications.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2alpha/{name=projects/*/locations/*/catalogs/*/branches/*/operations/*}'
    additional_bindings:
    - get: '/v2alpha/{name=projects/*/locations/*/catalogs/*/branches/*/places/*/operations/*}'
    - get: '/v2alpha/{name=projects/*/locations/*/catalogs/*/operations/*}'
    - get: '/v2alpha/{name=projects/*/locations/*/operations/*}'
    - get: '/v2alpha/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2alpha/{name=projects/*/locations/*/catalogs/*}/operations'
    additional_bindings:
    - get: '/v2alpha/{name=projects/*/locations/*}/operations'
    - get: '/v2alpha/{name=projects/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2alpha.AnalyticsService.ExportAnalyticsMetrics
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2alpha.BranchService.GetBranch
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2alpha.BranchService.ListBranches
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2alpha.CatalogService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2alpha.CompletionService.CompleteQuery
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2alpha.CompletionService.ImportCompletionData
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2alpha.ControlService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2alpha.GenerativeQuestionService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2alpha.MerchantCenterAccountLinkService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2alpha.ModelService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2alpha.PredictionService.Predict
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2alpha.ProductService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2alpha.ProjectService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.retail.v2alpha.SearchService.Search
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2alpha.ServingConfigService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.retail.v2alpha.UserEventService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.ListOperations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
