// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Retail.V2Alpha";
option go_package = "cloud.google.com/go/retail/apiv2alpha/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "MerchantCenterAccountLinkProto";
option java_package = "com.google.cloud.retail.v2alpha";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2alpha";
option ruby_package = "Google::Cloud::Retail::V2alpha";

// Represents a link between a Merchant Center account and a branch.
// After a link is established, products from the linked Merchant Center account
// are streamed to the linked branch.
message MerchantCenterAccountLink {
  option (google.api.resource) = {
    type: "retail.googleapis.com/MerchantCenterAccountLink"
    pattern: "projects/{project}/locations/{location}/catalogs/{catalog}/merchantCenterAccountLinks/{merchant_center_account_link}"
  };

  // Merchant Center Feed filter criterion.
  message MerchantCenterFeedFilter {
    // Merchant Center primary feed ID.
    int64 primary_feed_id = 1;

    // Merchant Center primary feed name. The name is used for the display
    // purposes only.
    string primary_feed_name = 2;
  }

  // The state of the link.
  enum State {
    // Default value.
    STATE_UNSPECIFIED = 0;

    // Link is created and LRO is not complete.
    PENDING = 1;

    // Link is active.
    ACTIVE = 2;

    // Link creation failed.
    FAILED = 3;
  }

  // Output only. Immutable. Full resource name of the Merchant Center Account
  // Link, such as
  // `projects/*/locations/global/catalogs/default_catalog/merchantCenterAccountLinks/merchant_center_account_link`.
  string name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. Immutable.
  // [MerchantCenterAccountLink][google.cloud.retail.v2alpha.MerchantCenterAccountLink]
  // identifier, which is the final component of
  // [name][google.cloud.retail.v2alpha.MerchantCenterAccountLink.name]. This
  // field is auto generated and follows the convention:
  // `BranchId_MerchantCenterAccountId`.
  // `projects/*/locations/global/catalogs/default_catalog/merchantCenterAccountLinks/id_1`.
  string id = 8 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Required. The linked [Merchant center account
  // id](https://developers.google.com/shopping-content/guides/accountstatuses).
  // The account must be a standalone account or a sub-account of a MCA.
  int64 merchant_center_account_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The branch ID (e.g. 0/1/2) within the catalog that products from
  // merchant_center_account_id are streamed to. When updating this field, an
  // empty value will use the currently configured default branch. However,
  // changing the default branch later on won't change the linked branch here.
  //
  // A single branch ID can only have one linked Merchant Center account ID.
  string branch_id = 3 [(google.api.field_behavior) = REQUIRED];

  // The FeedLabel used to perform filtering.
  // Note: this replaces
  // [region_id](https://developers.google.com/shopping-content/reference/rest/v2.1/products#Product.FIELDS.feed_label).
  //
  // Example value: `US`.
  // Example value: `FeedLabel1`.
  string feed_label = 4;

  // Language of the title/description and other string attributes. Use language
  // tags defined by [BCP 47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt).
  // ISO 639-1.
  //
  // This specifies the language of offers in Merchant Center that will be
  // accepted. If empty, no language filtering will be performed.
  //
  // Example value: `en`.
  string language_code = 5;

  // Criteria for the Merchant Center feeds to be ingested via the link.
  // All offers will be ingested if the list is empty.
  // Otherwise the offers will be ingested from selected feeds.
  repeated MerchantCenterFeedFilter feed_filters = 6;

  // Output only. Represents the state of the link.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Google Cloud project ID.
  string project_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. An optional arbitrary string that could be used as a tag for
  // tracking link source.
  string source = 10 [(google.api.field_behavior) = OPTIONAL];
}

// Common metadata related to the progress of the operations.
message CreateMerchantCenterAccountLinkMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}
