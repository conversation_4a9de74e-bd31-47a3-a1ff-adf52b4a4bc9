# This build file includes a target for the Ruby wrapper library for
# google-cloud-retail.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for retail.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "retail_ruby_wrapper",
    srcs = ["//google/cloud/retail/v2:retail_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-retail",
        "ruby-cloud-env-prefix=RETAIL",
        "ruby-cloud-wrapper-of=v2:1.1",
        "ruby-cloud-product-url=https://cloud.google.com/retail/docs/apis",
        "ruby-cloud-api-id=retail.googleapis.com",
        "ruby-cloud-api-shortname=retail",
    ],
    ruby_cloud_description = "Retail enables you to build an end-to-end personalized recommendation system based on state-of-the-art deep learning ML models, without a need for expertise in ML or recommendation systems.",
    ruby_cloud_title = "Retail",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-retail-ruby",
    deps = [
        ":retail_ruby_wrapper",
    ],
)
