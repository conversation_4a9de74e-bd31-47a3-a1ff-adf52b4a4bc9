{"methodConfig": [{"name": [{"service": "google.cloud.compute.v1.AcceleratorTypes", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.AcceleratorTypes", "method": "Get"}, {"service": "google.cloud.compute.v1.AcceleratorTypes", "method": "List"}, {"service": "google.cloud.compute.v1.Addresses", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.Addresses", "method": "Get"}, {"service": "google.cloud.compute.v1.Addresses", "method": "List"}, {"service": "google.cloud.compute.v1.Autoscalers", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.Autoscalers", "method": "Get"}, {"service": "google.cloud.compute.v1.Autoscalers", "method": "List"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "Get"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "List"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "Get"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "List"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "ListUsable"}, {"service": "google.cloud.compute.v1.DiskTypes", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.DiskTypes", "method": "Get"}, {"service": "google.cloud.compute.v1.DiskTypes", "method": "List"}, {"service": "google.cloud.compute.v1.Disks", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.Disks", "method": "Get"}, {"service": "google.cloud.compute.v1.Disks", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.Disks", "method": "List"}, {"service": "google.cloud.compute.v1.ExternalVpnGateways", "method": "Get"}, {"service": "google.cloud.compute.v1.ExternalVpnGateways", "method": "List"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "Get"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "GetAssociation"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "GetRule"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "List"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "ListAssociations"}, {"service": "google.cloud.compute.v1.Firewalls", "method": "Get"}, {"service": "google.cloud.compute.v1.Firewalls", "method": "List"}, {"service": "google.cloud.compute.v1.ForwardingRules", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.ForwardingRules", "method": "Get"}, {"service": "google.cloud.compute.v1.ForwardingRules", "method": "List"}, {"service": "google.cloud.compute.v1.GlobalAddresses", "method": "Get"}, {"service": "google.cloud.compute.v1.GlobalAddresses", "method": "List"}, {"service": "google.cloud.compute.v1.GlobalForwardingRules", "method": "Get"}, {"service": "google.cloud.compute.v1.GlobalForwardingRules", "method": "List"}, {"service": "google.cloud.compute.v1.GlobalNetworkEndpointGroups", "method": "Get"}, {"service": "google.cloud.compute.v1.GlobalNetworkEndpointGroups", "method": "List"}, {"service": "google.cloud.compute.v1.GlobalOperations", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.GlobalOperations", "method": "Get"}, {"service": "google.cloud.compute.v1.GlobalOperations", "method": "List"}, {"service": "google.cloud.compute.v1.GlobalOrganizationOperations", "method": "Get"}, {"service": "google.cloud.compute.v1.GlobalOrganizationOperations", "method": "List"}, {"service": "google.cloud.compute.v1.GlobalPublicDelegatedPrefixes", "method": "Get"}, {"service": "google.cloud.compute.v1.GlobalPublicDelegatedPrefixes", "method": "List"}, {"service": "google.cloud.compute.v1.HealthChecks", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.HealthChecks", "method": "Get"}, {"service": "google.cloud.compute.v1.HealthChecks", "method": "List"}, {"service": "google.cloud.compute.v1.ImageFamilyViews", "method": "Get"}, {"service": "google.cloud.compute.v1.Images", "method": "Get"}, {"service": "google.cloud.compute.v1.Images", "method": "GetFromFamily"}, {"service": "google.cloud.compute.v1.Images", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.Images", "method": "List"}, {"service": "google.cloud.compute.v1.InstanceGroupManagerResizeRequests", "method": "Get"}, {"service": "google.cloud.compute.v1.InstanceGroupManagerResizeRequests", "method": "List"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "Get"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "List"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "ListErrors"}, {"service": "google.cloud.compute.v1.InstanceGroups", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.InstanceGroups", "method": "Get"}, {"service": "google.cloud.compute.v1.InstanceGroups", "method": "List"}, {"service": "google.cloud.compute.v1.InstanceSettingsService", "method": "Get"}, {"service": "google.cloud.compute.v1.InstanceTemplates", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.InstanceTemplates", "method": "Get"}, {"service": "google.cloud.compute.v1.InstanceTemplates", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.InstanceTemplates", "method": "List"}, {"service": "google.cloud.compute.v1.Instances", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.Instances", "method": "Get"}, {"service": "google.cloud.compute.v1.Instances", "method": "GetEffectiveFirewalls"}, {"service": "google.cloud.compute.v1.Instances", "method": "GetGuestAttributes"}, {"service": "google.cloud.compute.v1.Instances", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.Instances", "method": "GetScreenshot"}, {"service": "google.cloud.compute.v1.Instances", "method": "GetSerialPortOutput"}, {"service": "google.cloud.compute.v1.Instances", "method": "GetShieldedInstanceIdentity"}, {"service": "google.cloud.compute.v1.Instances", "method": "List"}, {"service": "google.cloud.compute.v1.Instances", "method": "ListReferrers"}, {"service": "google.cloud.compute.v1.InstantSnapshots", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.InstantSnapshots", "method": "Get"}, {"service": "google.cloud.compute.v1.InstantSnapshots", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.InstantSnapshots", "method": "List"}, {"service": "google.cloud.compute.v1.InterconnectAttachments", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.InterconnectAttachments", "method": "Get"}, {"service": "google.cloud.compute.v1.InterconnectAttachments", "method": "List"}, {"service": "google.cloud.compute.v1.InterconnectLocations", "method": "Get"}, {"service": "google.cloud.compute.v1.InterconnectLocations", "method": "List"}, {"service": "google.cloud.compute.v1.InterconnectRemoteLocations", "method": "Get"}, {"service": "google.cloud.compute.v1.InterconnectRemoteLocations", "method": "List"}, {"service": "google.cloud.compute.v1.Interconnects", "method": "Get"}, {"service": "google.cloud.compute.v1.Interconnects", "method": "GetDiagnostics"}, {"service": "google.cloud.compute.v1.Interconnects", "method": "GetMacsecConfig"}, {"service": "google.cloud.compute.v1.Interconnects", "method": "List"}, {"service": "google.cloud.compute.v1.LicenseCodes", "method": "Get"}, {"service": "google.cloud.compute.v1.Licenses", "method": "Get"}, {"service": "google.cloud.compute.v1.Licenses", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.Licenses", "method": "List"}, {"service": "google.cloud.compute.v1.MachineImages", "method": "Get"}, {"service": "google.cloud.compute.v1.MachineImages", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.MachineImages", "method": "List"}, {"service": "google.cloud.compute.v1.MachineTypes", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.MachineTypes", "method": "Get"}, {"service": "google.cloud.compute.v1.MachineTypes", "method": "List"}, {"service": "google.cloud.compute.v1.NetworkAttachments", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.NetworkAttachments", "method": "Get"}, {"service": "google.cloud.compute.v1.NetworkAttachments", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.NetworkAttachments", "method": "List"}, {"service": "google.cloud.compute.v1.NetworkEdgeSecurityServices", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.NetworkEdgeSecurityServices", "method": "Get"}, {"service": "google.cloud.compute.v1.NetworkEndpointGroups", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.NetworkEndpointGroups", "method": "Get"}, {"service": "google.cloud.compute.v1.NetworkEndpointGroups", "method": "List"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "Get"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "GetAssociation"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "GetRule"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "List"}, {"service": "google.cloud.compute.v1.NetworkProfiles", "method": "Get"}, {"service": "google.cloud.compute.v1.NetworkProfiles", "method": "List"}, {"service": "google.cloud.compute.v1.Networks", "method": "Get"}, {"service": "google.cloud.compute.v1.Networks", "method": "GetEffectiveFirewalls"}, {"service": "google.cloud.compute.v1.Networks", "method": "List"}, {"service": "google.cloud.compute.v1.Networks", "method": "ListPeeringRoutes"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "Get"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "List"}, {"service": "google.cloud.compute.v1.NodeTemplates", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.NodeTemplates", "method": "Get"}, {"service": "google.cloud.compute.v1.NodeTemplates", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.NodeTemplates", "method": "List"}, {"service": "google.cloud.compute.v1.NodeTypes", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.NodeTypes", "method": "Get"}, {"service": "google.cloud.compute.v1.NodeTypes", "method": "List"}, {"service": "google.cloud.compute.v1.PacketMirrorings", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.PacketMirrorings", "method": "Get"}, {"service": "google.cloud.compute.v1.PacketMirrorings", "method": "List"}, {"service": "google.cloud.compute.v1.Projects", "method": "Get"}, {"service": "google.cloud.compute.v1.Projects", "method": "GetXpnHost"}, {"service": "google.cloud.compute.v1.Projects", "method": "GetXpnResources"}, {"service": "google.cloud.compute.v1.PublicAdvertisedPrefixes", "method": "Get"}, {"service": "google.cloud.compute.v1.PublicAdvertisedPrefixes", "method": "List"}, {"service": "google.cloud.compute.v1.PublicDelegatedPrefixes", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.PublicDelegatedPrefixes", "method": "Get"}, {"service": "google.cloud.compute.v1.PublicDelegatedPrefixes", "method": "List"}, {"service": "google.cloud.compute.v1.RegionAutoscalers", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionAutoscalers", "method": "List"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "List"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "ListUsable"}, {"service": "google.cloud.compute.v1.RegionCommitments", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.RegionCommitments", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionCommitments", "method": "List"}, {"service": "google.cloud.compute.v1.RegionDiskTypes", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionDiskTypes", "method": "List"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "List"}, {"service": "google.cloud.compute.v1.RegionHealthCheckServices", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionHealthCheckServices", "method": "List"}, {"service": "google.cloud.compute.v1.RegionHealthChecks", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionHealthChecks", "method": "List"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "List"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "ListErrors"}, {"service": "google.cloud.compute.v1.RegionInstanceGroups", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionInstanceGroups", "method": "List"}, {"service": "google.cloud.compute.v1.RegionInstanceTemplates", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionInstanceTemplates", "method": "List"}, {"service": "google.cloud.compute.v1.RegionInstantSnapshots", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionInstantSnapshots", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.RegionInstantSnapshots", "method": "List"}, {"service": "google.cloud.compute.v1.RegionNetworkEndpointGroups", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionNetworkEndpointGroups", "method": "List"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "GetAssociation"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "GetEffectiveFirewalls"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "GetRule"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "List"}, {"service": "google.cloud.compute.v1.RegionNotificationEndpoints", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionNotificationEndpoints", "method": "List"}, {"service": "google.cloud.compute.v1.RegionOperations", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionOperations", "method": "List"}, {"service": "google.cloud.compute.v1.RegionSecurityPolicies", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionSecurityPolicies", "method": "GetRule"}, {"service": "google.cloud.compute.v1.RegionSecurityPolicies", "method": "List"}, {"service": "google.cloud.compute.v1.RegionSslCertificates", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionSslCertificates", "method": "List"}, {"service": "google.cloud.compute.v1.RegionSslPolicies", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionSslPolicies", "method": "List"}, {"service": "google.cloud.compute.v1.RegionSslPolicies", "method": "ListAvailableFeatures"}, {"service": "google.cloud.compute.v1.RegionTargetHttpProxies", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionTargetHttpProxies", "method": "List"}, {"service": "google.cloud.compute.v1.RegionTargetHttpsProxies", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionTargetHttpsProxies", "method": "List"}, {"service": "google.cloud.compute.v1.RegionTargetTcpProxies", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionTargetTcpProxies", "method": "List"}, {"service": "google.cloud.compute.v1.RegionUrlMaps", "method": "Get"}, {"service": "google.cloud.compute.v1.RegionUrlMaps", "method": "List"}, {"service": "google.cloud.compute.v1.RegionZones", "method": "List"}, {"service": "google.cloud.compute.v1.Regions", "method": "Get"}, {"service": "google.cloud.compute.v1.Regions", "method": "List"}, {"service": "google.cloud.compute.v1.Reservations", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.Reservations", "method": "Get"}, {"service": "google.cloud.compute.v1.Reservations", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.Reservations", "method": "List"}, {"service": "google.cloud.compute.v1.ResourcePolicies", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.ResourcePolicies", "method": "Get"}, {"service": "google.cloud.compute.v1.ResourcePolicies", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.ResourcePolicies", "method": "List"}, {"service": "google.cloud.compute.v1.Routers", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.Routers", "method": "Get"}, {"service": "google.cloud.compute.v1.Routers", "method": "GetNatIpInfo"}, {"service": "google.cloud.compute.v1.Routers", "method": "GetNatMappingInfo"}, {"service": "google.cloud.compute.v1.Routers", "method": "GetRouterStatus"}, {"service": "google.cloud.compute.v1.Routers", "method": "List"}, {"service": "google.cloud.compute.v1.Routes", "method": "Get"}, {"service": "google.cloud.compute.v1.Routes", "method": "List"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "Get"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "GetRule"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "List"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "ListPreconfiguredExpressionSets"}, {"service": "google.cloud.compute.v1.ServiceAttachments", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.ServiceAttachments", "method": "Get"}, {"service": "google.cloud.compute.v1.ServiceAttachments", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.ServiceAttachments", "method": "List"}, {"service": "google.cloud.compute.v1.SnapshotSettingsService", "method": "Get"}, {"service": "google.cloud.compute.v1.Snapshots", "method": "Get"}, {"service": "google.cloud.compute.v1.Snapshots", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.Snapshots", "method": "List"}, {"service": "google.cloud.compute.v1.SslCertificates", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.SslCertificates", "method": "Get"}, {"service": "google.cloud.compute.v1.SslCertificates", "method": "List"}, {"service": "google.cloud.compute.v1.SslPolicies", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.SslPolicies", "method": "Get"}, {"service": "google.cloud.compute.v1.SslPolicies", "method": "List"}, {"service": "google.cloud.compute.v1.SslPolicies", "method": "ListAvailableFeatures"}, {"service": "google.cloud.compute.v1.StoragePoolTypes", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.StoragePoolTypes", "method": "Get"}, {"service": "google.cloud.compute.v1.StoragePoolTypes", "method": "List"}, {"service": "google.cloud.compute.v1.StoragePools", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.StoragePools", "method": "Get"}, {"service": "google.cloud.compute.v1.StoragePools", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.StoragePools", "method": "List"}, {"service": "google.cloud.compute.v1.StoragePools", "method": "ListDisks"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "Get"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "GetIamPolicy"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "List"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "ListUsable"}, {"service": "google.cloud.compute.v1.TargetGrpcProxies", "method": "Get"}, {"service": "google.cloud.compute.v1.TargetGrpcProxies", "method": "List"}, {"service": "google.cloud.compute.v1.TargetHttpProxies", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.TargetHttpProxies", "method": "Get"}, {"service": "google.cloud.compute.v1.TargetHttpProxies", "method": "List"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "Get"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "List"}, {"service": "google.cloud.compute.v1.TargetInstances", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.TargetInstances", "method": "Get"}, {"service": "google.cloud.compute.v1.TargetInstances", "method": "List"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "Get"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "List"}, {"service": "google.cloud.compute.v1.TargetSslProxies", "method": "Get"}, {"service": "google.cloud.compute.v1.TargetSslProxies", "method": "List"}, {"service": "google.cloud.compute.v1.TargetTcpProxies", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.TargetTcpProxies", "method": "Get"}, {"service": "google.cloud.compute.v1.TargetTcpProxies", "method": "List"}, {"service": "google.cloud.compute.v1.TargetVpnGateways", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.TargetVpnGateways", "method": "Get"}, {"service": "google.cloud.compute.v1.TargetVpnGateways", "method": "List"}, {"service": "google.cloud.compute.v1.UrlMaps", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.UrlMaps", "method": "Get"}, {"service": "google.cloud.compute.v1.UrlMaps", "method": "List"}, {"service": "google.cloud.compute.v1.VpnGateways", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.VpnGateways", "method": "Get"}, {"service": "google.cloud.compute.v1.VpnGateways", "method": "GetStatus"}, {"service": "google.cloud.compute.v1.VpnGateways", "method": "List"}, {"service": "google.cloud.compute.v1.VpnTunnels", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1.VpnTunnels", "method": "Get"}, {"service": "google.cloud.compute.v1.VpnTunnels", "method": "List"}, {"service": "google.cloud.compute.v1.ZoneOperations", "method": "Get"}, {"service": "google.cloud.compute.v1.ZoneOperations", "method": "List"}, {"service": "google.cloud.compute.v1.Zones", "method": "Get"}, {"service": "google.cloud.compute.v1.Zones", "method": "List"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.compute.v1.Addresses", "method": "Delete"}, {"service": "google.cloud.compute.v1.Addresses", "method": "Insert"}, {"service": "google.cloud.compute.v1.Addresses", "method": "Move"}, {"service": "google.cloud.compute.v1.Addresses", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.Autoscalers", "method": "Delete"}, {"service": "google.cloud.compute.v1.Autoscalers", "method": "Insert"}, {"service": "google.cloud.compute.v1.Autoscalers", "method": "Patch"}, {"service": "google.cloud.compute.v1.Autoscalers", "method": "Update"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "AddSignedUrlKey"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "Delete"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "DeleteSignedUrlKey"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "Insert"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "Patch"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "SetEdgeSecurityPolicy"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.BackendBuckets", "method": "Update"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "AddSignedUrlKey"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "Delete"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "DeleteSignedUrlKey"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "GetHealth"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "Insert"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "Patch"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "SetEdgeSecurityPolicy"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "SetSecurityPolicy"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.BackendServices", "method": "Update"}, {"service": "google.cloud.compute.v1.Disks", "method": "AddResourcePolicies"}, {"service": "google.cloud.compute.v1.Disks", "method": "BulkInsert"}, {"service": "google.cloud.compute.v1.Disks", "method": "CreateSnapshot"}, {"service": "google.cloud.compute.v1.Disks", "method": "Delete"}, {"service": "google.cloud.compute.v1.Disks", "method": "Insert"}, {"service": "google.cloud.compute.v1.Disks", "method": "RemoveResourcePolicies"}, {"service": "google.cloud.compute.v1.Disks", "method": "Resize"}, {"service": "google.cloud.compute.v1.Disks", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.Disks", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.Disks", "method": "StartAsyncReplication"}, {"service": "google.cloud.compute.v1.Disks", "method": "StopAsyncReplication"}, {"service": "google.cloud.compute.v1.Disks", "method": "StopGroupAsyncReplication"}, {"service": "google.cloud.compute.v1.Disks", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.Disks", "method": "Update"}, {"service": "google.cloud.compute.v1.ExternalVpnGateways", "method": "Delete"}, {"service": "google.cloud.compute.v1.ExternalVpnGateways", "method": "Insert"}, {"service": "google.cloud.compute.v1.ExternalVpnGateways", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.ExternalVpnGateways", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "AddAssociation"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "AddRule"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "CloneRules"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "Delete"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "Insert"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "Move"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "Patch"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "PatchRule"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "RemoveAssociation"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "RemoveRule"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.FirewallPolicies", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.Firewalls", "method": "Delete"}, {"service": "google.cloud.compute.v1.Firewalls", "method": "Insert"}, {"service": "google.cloud.compute.v1.Firewalls", "method": "Patch"}, {"service": "google.cloud.compute.v1.Firewalls", "method": "Update"}, {"service": "google.cloud.compute.v1.ForwardingRules", "method": "Delete"}, {"service": "google.cloud.compute.v1.ForwardingRules", "method": "Insert"}, {"service": "google.cloud.compute.v1.ForwardingRules", "method": "Patch"}, {"service": "google.cloud.compute.v1.ForwardingRules", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.ForwardingRules", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.GlobalAddresses", "method": "Delete"}, {"service": "google.cloud.compute.v1.GlobalAddresses", "method": "Insert"}, {"service": "google.cloud.compute.v1.GlobalAddresses", "method": "Move"}, {"service": "google.cloud.compute.v1.GlobalAddresses", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.GlobalForwardingRules", "method": "Delete"}, {"service": "google.cloud.compute.v1.GlobalForwardingRules", "method": "Insert"}, {"service": "google.cloud.compute.v1.GlobalForwardingRules", "method": "Patch"}, {"service": "google.cloud.compute.v1.GlobalForwardingRules", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.GlobalForwardingRules", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.GlobalNetworkEndpointGroups", "method": "AttachNetworkEndpoints"}, {"service": "google.cloud.compute.v1.GlobalNetworkEndpointGroups", "method": "Delete"}, {"service": "google.cloud.compute.v1.GlobalNetworkEndpointGroups", "method": "DetachNetworkEndpoints"}, {"service": "google.cloud.compute.v1.GlobalNetworkEndpointGroups", "method": "Insert"}, {"service": "google.cloud.compute.v1.GlobalNetworkEndpointGroups", "method": "ListNetworkEndpoints"}, {"service": "google.cloud.compute.v1.GlobalOperations", "method": "Delete"}, {"service": "google.cloud.compute.v1.GlobalOperations", "method": "Wait"}, {"service": "google.cloud.compute.v1.GlobalOrganizationOperations", "method": "Delete"}, {"service": "google.cloud.compute.v1.GlobalPublicDelegatedPrefixes", "method": "Delete"}, {"service": "google.cloud.compute.v1.GlobalPublicDelegatedPrefixes", "method": "Insert"}, {"service": "google.cloud.compute.v1.GlobalPublicDelegatedPrefixes", "method": "Patch"}, {"service": "google.cloud.compute.v1.HealthChecks", "method": "Delete"}, {"service": "google.cloud.compute.v1.HealthChecks", "method": "Insert"}, {"service": "google.cloud.compute.v1.HealthChecks", "method": "Patch"}, {"service": "google.cloud.compute.v1.HealthChecks", "method": "Update"}, {"service": "google.cloud.compute.v1.Images", "method": "Delete"}, {"service": "google.cloud.compute.v1.Images", "method": "Deprecate"}, {"service": "google.cloud.compute.v1.Images", "method": "Insert"}, {"service": "google.cloud.compute.v1.Images", "method": "Patch"}, {"service": "google.cloud.compute.v1.Images", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.Images", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.Images", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.InstanceGroupManagerResizeRequests", "method": "Cancel"}, {"service": "google.cloud.compute.v1.InstanceGroupManagerResizeRequests", "method": "Delete"}, {"service": "google.cloud.compute.v1.InstanceGroupManagerResizeRequests", "method": "Insert"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "AbandonInstances"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "ApplyUpdatesToInstances"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "CreateInstances"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "Delete"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "DeleteInstances"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "DeletePerInstanceConfigs"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "Insert"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "ListManagedInstances"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "ListPerInstanceConfigs"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "Patch"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "PatchPerInstanceConfigs"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "RecreateInstances"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "Resize"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "ResumeInstances"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "SetInstanceTemplate"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "SetTargetPools"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "StartInstances"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "StopInstances"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "SuspendInstances"}, {"service": "google.cloud.compute.v1.InstanceGroupManagers", "method": "UpdatePerInstanceConfigs"}, {"service": "google.cloud.compute.v1.InstanceGroups", "method": "AddInstances"}, {"service": "google.cloud.compute.v1.InstanceGroups", "method": "Delete"}, {"service": "google.cloud.compute.v1.InstanceGroups", "method": "Insert"}, {"service": "google.cloud.compute.v1.InstanceGroups", "method": "ListInstances"}, {"service": "google.cloud.compute.v1.InstanceGroups", "method": "RemoveInstances"}, {"service": "google.cloud.compute.v1.InstanceGroups", "method": "SetNamedPorts"}, {"service": "google.cloud.compute.v1.InstanceSettingsService", "method": "Patch"}, {"service": "google.cloud.compute.v1.InstanceTemplates", "method": "Delete"}, {"service": "google.cloud.compute.v1.InstanceTemplates", "method": "Insert"}, {"service": "google.cloud.compute.v1.InstanceTemplates", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.InstanceTemplates", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.Instances", "method": "AddAccessConfig"}, {"service": "google.cloud.compute.v1.Instances", "method": "AddResourcePolicies"}, {"service": "google.cloud.compute.v1.Instances", "method": "AttachDisk"}, {"service": "google.cloud.compute.v1.Instances", "method": "BulkInsert"}, {"service": "google.cloud.compute.v1.Instances", "method": "Delete"}, {"service": "google.cloud.compute.v1.Instances", "method": "DeleteAccessConfig"}, {"service": "google.cloud.compute.v1.Instances", "method": "DetachDisk"}, {"service": "google.cloud.compute.v1.Instances", "method": "Insert"}, {"service": "google.cloud.compute.v1.Instances", "method": "PerformMaintenance"}, {"service": "google.cloud.compute.v1.Instances", "method": "RemoveResourcePolicies"}, {"service": "google.cloud.compute.v1.Instances", "method": "Reset"}, {"service": "google.cloud.compute.v1.Instances", "method": "Resume"}, {"service": "google.cloud.compute.v1.Instances", "method": "SendDiagnosticInterrupt"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetDeletionProtection"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetDiskAutoDelete"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.Instances", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetMachineResources"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetMachineType"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetMetadata"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetMinCpuPlatform"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetName"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetScheduling"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetSecurityPolicy"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetServiceAccount"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetShieldedInstanceIntegrityPolicy"}, {"service": "google.cloud.compute.v1.Instances", "method": "SetTags"}, {"service": "google.cloud.compute.v1.Instances", "method": "SimulateMaintenanceEvent"}, {"service": "google.cloud.compute.v1.Instances", "method": "Start"}, {"service": "google.cloud.compute.v1.Instances", "method": "StartWithEncryptionKey"}, {"service": "google.cloud.compute.v1.Instances", "method": "Stop"}, {"service": "google.cloud.compute.v1.Instances", "method": "Suspend"}, {"service": "google.cloud.compute.v1.Instances", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.Instances", "method": "Update"}, {"service": "google.cloud.compute.v1.Instances", "method": "UpdateAccessConfig"}, {"service": "google.cloud.compute.v1.Instances", "method": "UpdateDisplayDevice"}, {"service": "google.cloud.compute.v1.Instances", "method": "UpdateNetworkInterface"}, {"service": "google.cloud.compute.v1.Instances", "method": "UpdateShieldedInstanceConfig"}, {"service": "google.cloud.compute.v1.InstantSnapshots", "method": "Delete"}, {"service": "google.cloud.compute.v1.InstantSnapshots", "method": "Insert"}, {"service": "google.cloud.compute.v1.InstantSnapshots", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.InstantSnapshots", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.InstantSnapshots", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.InterconnectAttachments", "method": "Delete"}, {"service": "google.cloud.compute.v1.InterconnectAttachments", "method": "Insert"}, {"service": "google.cloud.compute.v1.InterconnectAttachments", "method": "Patch"}, {"service": "google.cloud.compute.v1.InterconnectAttachments", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.Interconnects", "method": "Delete"}, {"service": "google.cloud.compute.v1.Interconnects", "method": "Insert"}, {"service": "google.cloud.compute.v1.Interconnects", "method": "Patch"}, {"service": "google.cloud.compute.v1.Interconnects", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.LicenseCodes", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.Licenses", "method": "Delete"}, {"service": "google.cloud.compute.v1.Licenses", "method": "Insert"}, {"service": "google.cloud.compute.v1.Licenses", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.Licenses", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.MachineImages", "method": "Delete"}, {"service": "google.cloud.compute.v1.MachineImages", "method": "Insert"}, {"service": "google.cloud.compute.v1.MachineImages", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.MachineImages", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.NetworkAttachments", "method": "Delete"}, {"service": "google.cloud.compute.v1.NetworkAttachments", "method": "Insert"}, {"service": "google.cloud.compute.v1.NetworkAttachments", "method": "Patch"}, {"service": "google.cloud.compute.v1.NetworkAttachments", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.NetworkAttachments", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.NetworkEdgeSecurityServices", "method": "Delete"}, {"service": "google.cloud.compute.v1.NetworkEdgeSecurityServices", "method": "Insert"}, {"service": "google.cloud.compute.v1.NetworkEdgeSecurityServices", "method": "Patch"}, {"service": "google.cloud.compute.v1.NetworkEndpointGroups", "method": "AttachNetworkEndpoints"}, {"service": "google.cloud.compute.v1.NetworkEndpointGroups", "method": "Delete"}, {"service": "google.cloud.compute.v1.NetworkEndpointGroups", "method": "DetachNetworkEndpoints"}, {"service": "google.cloud.compute.v1.NetworkEndpointGroups", "method": "Insert"}, {"service": "google.cloud.compute.v1.NetworkEndpointGroups", "method": "ListNetworkEndpoints"}, {"service": "google.cloud.compute.v1.NetworkEndpointGroups", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "AddAssociation"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "AddRule"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "CloneRules"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "Delete"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "Insert"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "Patch"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "PatchRule"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "RemoveAssociation"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "RemoveRule"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.NetworkFirewallPolicies", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.Networks", "method": "AddPeering"}, {"service": "google.cloud.compute.v1.Networks", "method": "Delete"}, {"service": "google.cloud.compute.v1.Networks", "method": "Insert"}, {"service": "google.cloud.compute.v1.Networks", "method": "Patch"}, {"service": "google.cloud.compute.v1.Networks", "method": "RemovePeering"}, {"service": "google.cloud.compute.v1.Networks", "method": "SwitchToCustomMode"}, {"service": "google.cloud.compute.v1.Networks", "method": "UpdatePeering"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "AddNodes"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "Delete"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "DeleteNodes"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "Insert"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "ListNodes"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "Patch"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "PerformMaintenance"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "SetNodeTemplate"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "SimulateMaintenanceEvent"}, {"service": "google.cloud.compute.v1.NodeGroups", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.NodeTemplates", "method": "Delete"}, {"service": "google.cloud.compute.v1.NodeTemplates", "method": "Insert"}, {"service": "google.cloud.compute.v1.NodeTemplates", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.NodeTemplates", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.PacketMirrorings", "method": "Delete"}, {"service": "google.cloud.compute.v1.PacketMirrorings", "method": "Insert"}, {"service": "google.cloud.compute.v1.PacketMirrorings", "method": "Patch"}, {"service": "google.cloud.compute.v1.PacketMirrorings", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.Projects", "method": "DisableXpnHost"}, {"service": "google.cloud.compute.v1.Projects", "method": "DisableXpnResource"}, {"service": "google.cloud.compute.v1.Projects", "method": "EnableXpnHost"}, {"service": "google.cloud.compute.v1.Projects", "method": "EnableXpnResource"}, {"service": "google.cloud.compute.v1.Projects", "method": "ListXpnHosts"}, {"service": "google.cloud.compute.v1.Projects", "method": "MoveDisk"}, {"service": "google.cloud.compute.v1.Projects", "method": "MoveInstance"}, {"service": "google.cloud.compute.v1.Projects", "method": "SetCloudArmorTier"}, {"service": "google.cloud.compute.v1.Projects", "method": "SetCommonInstanceMetadata"}, {"service": "google.cloud.compute.v1.Projects", "method": "SetDefaultNetworkTier"}, {"service": "google.cloud.compute.v1.Projects", "method": "SetUsageExportBucket"}, {"service": "google.cloud.compute.v1.PublicAdvertisedPrefixes", "method": "Announce"}, {"service": "google.cloud.compute.v1.PublicAdvertisedPrefixes", "method": "Delete"}, {"service": "google.cloud.compute.v1.PublicAdvertisedPrefixes", "method": "Insert"}, {"service": "google.cloud.compute.v1.PublicAdvertisedPrefixes", "method": "Patch"}, {"service": "google.cloud.compute.v1.PublicAdvertisedPrefixes", "method": "Withdraw"}, {"service": "google.cloud.compute.v1.PublicDelegatedPrefixes", "method": "Announce"}, {"service": "google.cloud.compute.v1.PublicDelegatedPrefixes", "method": "Delete"}, {"service": "google.cloud.compute.v1.PublicDelegatedPrefixes", "method": "Insert"}, {"service": "google.cloud.compute.v1.PublicDelegatedPrefixes", "method": "Patch"}, {"service": "google.cloud.compute.v1.PublicDelegatedPrefixes", "method": "Withdraw"}, {"service": "google.cloud.compute.v1.RegionAutoscalers", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionAutoscalers", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionAutoscalers", "method": "Patch"}, {"service": "google.cloud.compute.v1.RegionAutoscalers", "method": "Update"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "GetHealth"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "Patch"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "SetSecurityPolicy"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.RegionBackendServices", "method": "Update"}, {"service": "google.cloud.compute.v1.RegionCommitments", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionCommitments", "method": "Update"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "AddResourcePolicies"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "BulkInsert"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "CreateSnapshot"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "RemoveResourcePolicies"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "Resize"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "StartAsyncReplication"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "StopAsyncReplication"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "StopGroupAsyncReplication"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.RegionDisks", "method": "Update"}, {"service": "google.cloud.compute.v1.RegionHealthCheckServices", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionHealthCheckServices", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionHealthCheckServices", "method": "Patch"}, {"service": "google.cloud.compute.v1.RegionHealthChecks", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionHealthChecks", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionHealthChecks", "method": "Patch"}, {"service": "google.cloud.compute.v1.RegionHealthChecks", "method": "Update"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "AbandonInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "ApplyUpdatesToInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "CreateInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "DeleteInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "DeletePerInstanceConfigs"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "ListManagedInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "ListPerInstanceConfigs"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "Patch"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "PatchPerInstanceConfigs"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "RecreateInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "Resize"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "ResumeInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "SetInstanceTemplate"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "SetTargetPools"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "StartInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "StopInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "SuspendInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroupManagers", "method": "UpdatePerInstanceConfigs"}, {"service": "google.cloud.compute.v1.RegionInstanceGroups", "method": "ListInstances"}, {"service": "google.cloud.compute.v1.RegionInstanceGroups", "method": "SetNamedPorts"}, {"service": "google.cloud.compute.v1.RegionInstanceTemplates", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionInstanceTemplates", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionInstances", "method": "BulkInsert"}, {"service": "google.cloud.compute.v1.RegionInstantSnapshots", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionInstantSnapshots", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionInstantSnapshots", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.RegionInstantSnapshots", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.RegionInstantSnapshots", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.RegionNetworkEndpointGroups", "method": "AttachNetworkEndpoints"}, {"service": "google.cloud.compute.v1.RegionNetworkEndpointGroups", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionNetworkEndpointGroups", "method": "DetachNetworkEndpoints"}, {"service": "google.cloud.compute.v1.RegionNetworkEndpointGroups", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionNetworkEndpointGroups", "method": "ListNetworkEndpoints"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "AddAssociation"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "AddRule"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "CloneRules"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "Patch"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "PatchRule"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "RemoveAssociation"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "RemoveRule"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.RegionNetworkFirewallPolicies", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.RegionNotificationEndpoints", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionNotificationEndpoints", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionOperations", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionOperations", "method": "Wait"}, {"service": "google.cloud.compute.v1.RegionSecurityPolicies", "method": "AddRule"}, {"service": "google.cloud.compute.v1.RegionSecurityPolicies", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionSecurityPolicies", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionSecurityPolicies", "method": "Patch"}, {"service": "google.cloud.compute.v1.RegionSecurityPolicies", "method": "PatchRule"}, {"service": "google.cloud.compute.v1.RegionSecurityPolicies", "method": "RemoveRule"}, {"service": "google.cloud.compute.v1.RegionSecurityPolicies", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.RegionSslCertificates", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionSslCertificates", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionSslPolicies", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionSslPolicies", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionSslPolicies", "method": "Patch"}, {"service": "google.cloud.compute.v1.RegionTargetHttpProxies", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionTargetHttpProxies", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionTargetHttpProxies", "method": "SetUrlMap"}, {"service": "google.cloud.compute.v1.RegionTargetHttpsProxies", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionTargetHttpsProxies", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionTargetHttpsProxies", "method": "Patch"}, {"service": "google.cloud.compute.v1.RegionTargetHttpsProxies", "method": "SetSslCertificates"}, {"service": "google.cloud.compute.v1.RegionTargetHttpsProxies", "method": "SetUrlMap"}, {"service": "google.cloud.compute.v1.RegionTargetTcpProxies", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionTargetTcpProxies", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionUrlMaps", "method": "Delete"}, {"service": "google.cloud.compute.v1.RegionUrlMaps", "method": "Insert"}, {"service": "google.cloud.compute.v1.RegionUrlMaps", "method": "Patch"}, {"service": "google.cloud.compute.v1.RegionUrlMaps", "method": "Update"}, {"service": "google.cloud.compute.v1.RegionUrlMaps", "method": "Validate"}, {"service": "google.cloud.compute.v1.Reservations", "method": "Delete"}, {"service": "google.cloud.compute.v1.Reservations", "method": "Insert"}, {"service": "google.cloud.compute.v1.Reservations", "method": "Resize"}, {"service": "google.cloud.compute.v1.Reservations", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.Reservations", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.Reservations", "method": "Update"}, {"service": "google.cloud.compute.v1.ResourcePolicies", "method": "Delete"}, {"service": "google.cloud.compute.v1.ResourcePolicies", "method": "Insert"}, {"service": "google.cloud.compute.v1.ResourcePolicies", "method": "Patch"}, {"service": "google.cloud.compute.v1.ResourcePolicies", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.ResourcePolicies", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.Routers", "method": "Delete"}, {"service": "google.cloud.compute.v1.Routers", "method": "Insert"}, {"service": "google.cloud.compute.v1.Routers", "method": "Patch"}, {"service": "google.cloud.compute.v1.Routers", "method": "Preview"}, {"service": "google.cloud.compute.v1.Routers", "method": "Update"}, {"service": "google.cloud.compute.v1.Routes", "method": "Delete"}, {"service": "google.cloud.compute.v1.Routes", "method": "Insert"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "AddRule"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "Delete"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "Insert"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "Patch"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "PatchRule"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "RemoveRule"}, {"service": "google.cloud.compute.v1.SecurityPolicies", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.ServiceAttachments", "method": "Delete"}, {"service": "google.cloud.compute.v1.ServiceAttachments", "method": "Insert"}, {"service": "google.cloud.compute.v1.ServiceAttachments", "method": "Patch"}, {"service": "google.cloud.compute.v1.ServiceAttachments", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.ServiceAttachments", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.SnapshotSettingsService", "method": "Patch"}, {"service": "google.cloud.compute.v1.Snapshots", "method": "Delete"}, {"service": "google.cloud.compute.v1.Snapshots", "method": "Insert"}, {"service": "google.cloud.compute.v1.Snapshots", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.Snapshots", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.Snapshots", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.SslCertificates", "method": "Delete"}, {"service": "google.cloud.compute.v1.SslCertificates", "method": "Insert"}, {"service": "google.cloud.compute.v1.SslPolicies", "method": "Delete"}, {"service": "google.cloud.compute.v1.SslPolicies", "method": "Insert"}, {"service": "google.cloud.compute.v1.SslPolicies", "method": "Patch"}, {"service": "google.cloud.compute.v1.StoragePools", "method": "Delete"}, {"service": "google.cloud.compute.v1.StoragePools", "method": "Insert"}, {"service": "google.cloud.compute.v1.StoragePools", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.StoragePools", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.StoragePools", "method": "Update"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "Delete"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "ExpandIpCidrRange"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "Insert"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "Patch"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "SetIamPolicy"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "SetPrivateIpGoogleAccess"}, {"service": "google.cloud.compute.v1.Subnetworks", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.TargetGrpcProxies", "method": "Delete"}, {"service": "google.cloud.compute.v1.TargetGrpcProxies", "method": "Insert"}, {"service": "google.cloud.compute.v1.TargetGrpcProxies", "method": "Patch"}, {"service": "google.cloud.compute.v1.TargetHttpProxies", "method": "Delete"}, {"service": "google.cloud.compute.v1.TargetHttpProxies", "method": "Insert"}, {"service": "google.cloud.compute.v1.TargetHttpProxies", "method": "Patch"}, {"service": "google.cloud.compute.v1.TargetHttpProxies", "method": "SetUrlMap"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "Delete"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "Insert"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "Patch"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "SetCertificateMap"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "SetQuicOverride"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "SetSslCertificates"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "SetSslPolicy"}, {"service": "google.cloud.compute.v1.TargetHttpsProxies", "method": "SetUrlMap"}, {"service": "google.cloud.compute.v1.TargetInstances", "method": "Delete"}, {"service": "google.cloud.compute.v1.TargetInstances", "method": "Insert"}, {"service": "google.cloud.compute.v1.TargetInstances", "method": "SetSecurityPolicy"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "AddHealthCheck"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "AddInstance"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "Delete"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "GetHealth"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "Insert"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "RemoveHealthCheck"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "RemoveInstance"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "SetBackup"}, {"service": "google.cloud.compute.v1.TargetPools", "method": "SetSecurityPolicy"}, {"service": "google.cloud.compute.v1.TargetSslProxies", "method": "Delete"}, {"service": "google.cloud.compute.v1.TargetSslProxies", "method": "Insert"}, {"service": "google.cloud.compute.v1.TargetSslProxies", "method": "SetBackendService"}, {"service": "google.cloud.compute.v1.TargetSslProxies", "method": "SetCertificateMap"}, {"service": "google.cloud.compute.v1.TargetSslProxies", "method": "SetProxyHeader"}, {"service": "google.cloud.compute.v1.TargetSslProxies", "method": "SetSslCertificates"}, {"service": "google.cloud.compute.v1.TargetSslProxies", "method": "SetSslPolicy"}, {"service": "google.cloud.compute.v1.TargetTcpProxies", "method": "Delete"}, {"service": "google.cloud.compute.v1.TargetTcpProxies", "method": "Insert"}, {"service": "google.cloud.compute.v1.TargetTcpProxies", "method": "SetBackendService"}, {"service": "google.cloud.compute.v1.TargetTcpProxies", "method": "SetProxyHeader"}, {"service": "google.cloud.compute.v1.TargetVpnGateways", "method": "Delete"}, {"service": "google.cloud.compute.v1.TargetVpnGateways", "method": "Insert"}, {"service": "google.cloud.compute.v1.TargetVpnGateways", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.UrlMaps", "method": "Delete"}, {"service": "google.cloud.compute.v1.UrlMaps", "method": "Insert"}, {"service": "google.cloud.compute.v1.UrlMaps", "method": "InvalidateCache"}, {"service": "google.cloud.compute.v1.UrlMaps", "method": "Patch"}, {"service": "google.cloud.compute.v1.UrlMaps", "method": "Update"}, {"service": "google.cloud.compute.v1.UrlMaps", "method": "Validate"}, {"service": "google.cloud.compute.v1.VpnGateways", "method": "Delete"}, {"service": "google.cloud.compute.v1.VpnGateways", "method": "Insert"}, {"service": "google.cloud.compute.v1.VpnGateways", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.VpnGateways", "method": "TestIamPermissions"}, {"service": "google.cloud.compute.v1.VpnTunnels", "method": "Delete"}, {"service": "google.cloud.compute.v1.VpnTunnels", "method": "Insert"}, {"service": "google.cloud.compute.v1.VpnTunnels", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.compute.v1.ZoneOperations", "method": "Delete"}, {"service": "google.cloud.compute.v1.ZoneOperations", "method": "Wait"}], "timeout": "600s"}]}