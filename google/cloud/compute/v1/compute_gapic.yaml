# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http:#www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the disco-to-proto3-converter. DO NOT EDIT!
# Source Discovery file: compute.v1.json
# Source file revision: 20241201
# API name: compute
# API version: v1

type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  java:
    package_name: com.google.cloud.compute.v1
interfaces:
- name: google.cloud.compute.v1.Addresses
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Move
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Autoscalers
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.BackendBuckets
  methods:
  - name: AddSignedUrlKey
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DeleteSignedUrlKey
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetEdgeSecurityPolicy
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.BackendServices
  methods:
  - name: AddSignedUrlKey
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DeleteSignedUrlKey
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetEdgeSecurityPolicy
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetSecurityPolicy
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Disks
  methods:
  - name: AddResourcePolicies
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: BulkInsert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: CreateSnapshot
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveResourcePolicies
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Resize
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StartAsyncReplication
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StopAsyncReplication
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StopGroupAsyncReplication
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.ExternalVpnGateways
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.FirewallPolicies
  methods:
  - name: AddAssociation
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: AddRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: CloneRules
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Move
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: PatchRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveAssociation
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Firewalls
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.ForwardingRules
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetTarget
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.GlobalAddresses
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Move
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.GlobalForwardingRules
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetTarget
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.GlobalNetworkEndpointGroups
  methods:
  - name: AttachNetworkEndpoints
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DetachNetworkEndpoints
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.GlobalPublicDelegatedPrefixes
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.HealthChecks
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Images
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Deprecate
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.InstanceGroupManagerResizeRequests
  methods:
  - name: Cancel
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.InstanceGroupManagers
  methods:
  - name: AbandonInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: ApplyUpdatesToInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: CreateInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DeleteInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DeletePerInstanceConfigs
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: PatchPerInstanceConfigs
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RecreateInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Resize
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: ResumeInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetInstanceTemplate
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetTargetPools
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StartInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StopInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SuspendInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: UpdatePerInstanceConfigs
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.InstanceGroups
  methods:
  - name: AddInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetNamedPorts
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.InstanceSettingsService
  methods:
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.InstanceTemplates
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Instances
  methods:
  - name: AddAccessConfig
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: AddResourcePolicies
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: AttachDisk
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: BulkInsert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DeleteAccessConfig
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DetachDisk
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: PerformMaintenance
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveResourcePolicies
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Reset
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Resume
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetDeletionProtection
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetDiskAutoDelete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetMachineResources
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetMachineType
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetMetadata
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetMinCpuPlatform
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetName
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetScheduling
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetSecurityPolicy
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetServiceAccount
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetShieldedInstanceIntegrityPolicy
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetTags
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SimulateMaintenanceEvent
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Start
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StartWithEncryptionKey
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Stop
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Suspend
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: UpdateAccessConfig
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: UpdateDisplayDevice
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: UpdateNetworkInterface
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: UpdateShieldedInstanceConfig
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.InstantSnapshots
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.InterconnectAttachments
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Interconnects
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Licenses
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.MachineImages
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.NetworkAttachments
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.NetworkEdgeSecurityServices
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.NetworkEndpointGroups
  methods:
  - name: AttachNetworkEndpoints
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DetachNetworkEndpoints
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.NetworkFirewallPolicies
  methods:
  - name: AddAssociation
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: AddRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: CloneRules
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: PatchRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveAssociation
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Networks
  methods:
  - name: AddPeering
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemovePeering
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SwitchToCustomMode
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: UpdatePeering
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.NodeGroups
  methods:
  - name: AddNodes
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DeleteNodes
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: PerformMaintenance
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetNodeTemplate
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SimulateMaintenanceEvent
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.NodeTemplates
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.PacketMirrorings
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Projects
  methods:
  - name: DisableXpnHost
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DisableXpnResource
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: EnableXpnHost
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: EnableXpnResource
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: MoveDisk
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: MoveInstance
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetCloudArmorTier
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetCommonInstanceMetadata
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetDefaultNetworkTier
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetUsageExportBucket
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.PublicAdvertisedPrefixes
  methods:
  - name: Announce
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Withdraw
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.PublicDelegatedPrefixes
  methods:
  - name: Announce
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Withdraw
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionAutoscalers
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionBackendServices
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetSecurityPolicy
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionCommitments
  methods:
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionDisks
  methods:
  - name: AddResourcePolicies
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: BulkInsert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: CreateSnapshot
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveResourcePolicies
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Resize
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StartAsyncReplication
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StopAsyncReplication
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StopGroupAsyncReplication
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionHealthCheckServices
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionHealthChecks
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionInstanceGroupManagers
  methods:
  - name: AbandonInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: ApplyUpdatesToInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: CreateInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DeleteInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DeletePerInstanceConfigs
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: PatchPerInstanceConfigs
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RecreateInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Resize
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: ResumeInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetInstanceTemplate
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetTargetPools
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StartInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: StopInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SuspendInstances
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: UpdatePerInstanceConfigs
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionInstanceGroups
  methods:
  - name: SetNamedPorts
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionInstanceTemplates
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionInstances
  methods:
  - name: BulkInsert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionInstantSnapshots
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionNetworkEndpointGroups
  methods:
  - name: AttachNetworkEndpoints
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: DetachNetworkEndpoints
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionNetworkFirewallPolicies
  methods:
  - name: AddAssociation
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: AddRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: CloneRules
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: PatchRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveAssociation
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionNotificationEndpoints
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionSecurityPolicies
  methods:
  - name: AddRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: PatchRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionSslCertificates
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionSslPolicies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionTargetHttpProxies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetUrlMap
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionTargetHttpsProxies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetSslCertificates
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetUrlMap
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionTargetTcpProxies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.RegionUrlMaps
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Reservations
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Resize
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.ResourcePolicies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Routers
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Routes
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.SecurityPolicies
  methods:
  - name: AddRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: PatchRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveRule
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.ServiceAttachments
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.SnapshotSettingsService
  methods:
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Snapshots
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.SslCertificates
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.SslPolicies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.StoragePools
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.Subnetworks
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: ExpandIpCidrRange
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetPrivateIpGoogleAccess
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.TargetGrpcProxies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.TargetHttpProxies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetUrlMap
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.TargetHttpsProxies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetCertificateMap
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetQuicOverride
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetSslCertificates
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetSslPolicy
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetUrlMap
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.TargetInstances
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetSecurityPolicy
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.TargetPools
  methods:
  - name: AddHealthCheck
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: AddInstance
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveHealthCheck
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: RemoveInstance
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetBackup
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetSecurityPolicy
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.TargetSslProxies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetBackendService
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetCertificateMap
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetProxyHeader
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetSslCertificates
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetSslPolicy
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.TargetTcpProxies
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetBackendService
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetProxyHeader
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.TargetVpnGateways
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.UrlMaps
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: InvalidateCache
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Patch
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Update
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.VpnGateways
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
- name: google.cloud.compute.v1.VpnTunnels
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: SetLabels
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
