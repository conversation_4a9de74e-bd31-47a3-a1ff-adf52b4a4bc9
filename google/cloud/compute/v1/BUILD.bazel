# This file was automatically generated by B<PERSON>FileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

exports_files(glob(include = ["*grpc_service_config.json"]))

##############################################################################
# Discovery
##############################################################################
load(
    "@com_google_disco_to_proto3_converter//rules_gapic:disco_to_proto.bzl",
    "gapic_yaml_from_disco",
    "grpc_service_config_from_disco",
    "proto_from_disco",
)

_MESSAGE_IGNORE_LIST = [
    "HttpHealthCheck",
    "HttpsHealthCheck",
    "HttpHealthCheckList",
    "HttpsHealthCheckList",
    "GetHttpHealthCheckRequest",
    "GetHttpsHealthCheckRequest",
    "PatchHttpHealthCheckRequest",
    "PatchHttpsHealthCheckRequest",
    "UpdateHttpHealthCheckRequest",
    "UpdateHttpsHealthCheckRequest",
    "InsertHttpHealthCheckRequest",
    "InsertHttpsHealthCheckRequest",
    "ListHttpHealthChecksRequest",
    "ListHttpsHealthChecksRequest",
    "DeleteHttpHealthCheckRequest",
    "DeleteHttpsHealthCheckRequest",
]

_SERVICE_IGNORELIST = [
    "HttpHealthChecks",
    "HttpsHealthChecks",
]

proto_from_disco(
    name = "compute_gen",
    src = "compute.v1.json",
    enums_as_strings = True,
    message_ignorelist = _MESSAGE_IGNORE_LIST,
    previous_proto = "compute.proto",
    service_ignorelist = _SERVICE_IGNORELIST,
)

grpc_service_config_from_disco(
    name = "compute_grpc_service_config_gen",
    src = "compute.v1.json",
    message_ignorelist = _MESSAGE_IGNORE_LIST,
    previous_proto = "compute.proto",
    service_ignorelist = _SERVICE_IGNORELIST,
)

gapic_yaml_from_disco(
    name = "compute_gapic_gen",
    src = "compute.v1.json",
    message_ignorelist = _MESSAGE_IGNORE_LIST,
    previous_proto = "compute.proto",
    service_ignorelist = _SERVICE_IGNORELIST,
)

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "compute_proto",
    srcs = [
        "compute.proto",
    ],
    deps = [
        "@com_google_googleapis//google/api:annotations_proto",
        "@com_google_googleapis//google/api:client_proto",
        "@com_google_googleapis//google/api:field_behavior_proto",
        "@com_google_googleapis//google/api:resource_proto",
        "@com_google_googleapis//google/cloud:extended_operations_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:descriptor_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library_with_info(
    name = "compute_proto_with_info",
    deps = [
        ":compute_proto",
        "@com_google_googleapis//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "compute_java_proto",
    deps = [":compute_proto"],
)

java_gapic_library(
    name = "compute_java_gapic",
    srcs = [":compute_proto_with_info"],
    gapic_yaml = "compute_gapic.yaml",
    grpc_service_config = ":compute_grpc_service_config.json",
    test_deps = [],
    transport = "rest",
    deps = [
        ":compute_java_proto",
    ],
)

java_gapic_test(
    name = "compute_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.compute.v1.AcceleratorTypesClientTest",
        "com.google.cloud.compute.v1.AddressesClientTest",
        "com.google.cloud.compute.v1.AutoscalersClientTest",
        "com.google.cloud.compute.v1.BackendBucketsClientTest",
        "com.google.cloud.compute.v1.BackendServicesClientTest",
        "com.google.cloud.compute.v1.DiskTypesClientTest",
        "com.google.cloud.compute.v1.DisksClientTest",
        "com.google.cloud.compute.v1.ExternalVpnGatewaysClientTest",
        "com.google.cloud.compute.v1.FirewallPoliciesClientTest",
        "com.google.cloud.compute.v1.FirewallsClientTest",
        "com.google.cloud.compute.v1.ForwardingRulesClientTest",
        "com.google.cloud.compute.v1.GlobalAddressesClientTest",
        "com.google.cloud.compute.v1.GlobalForwardingRulesClientTest",
        "com.google.cloud.compute.v1.GlobalNetworkEndpointGroupsClientTest",
        "com.google.cloud.compute.v1.GlobalOperationsClientTest",
        "com.google.cloud.compute.v1.GlobalOrganizationOperationsClientTest",
        "com.google.cloud.compute.v1.GlobalPublicDelegatedPrefixesClientTest",
        "com.google.cloud.compute.v1.HealthChecksClientTest",
        "com.google.cloud.compute.v1.ImageFamilyViewsClientTest",
        "com.google.cloud.compute.v1.ImagesClientTest",
        "com.google.cloud.compute.v1.InstanceGroupManagersClientTest",
        "com.google.cloud.compute.v1.InstanceGroupsClientTest",
        "com.google.cloud.compute.v1.InstanceTemplatesClientTest",
        "com.google.cloud.compute.v1.InstancesClientTest",
        "com.google.cloud.compute.v1.InterconnectAttachmentsClientTest",
        "com.google.cloud.compute.v1.InterconnectLocationsClientTest",
        "com.google.cloud.compute.v1.InterconnectsClientTest",
        "com.google.cloud.compute.v1.LicenseCodesClientTest",
        "com.google.cloud.compute.v1.LicensesClientTest",
        "com.google.cloud.compute.v1.MachineTypesClientTest",
        "com.google.cloud.compute.v1.NetworkEndpointGroupsClientTest",
        "com.google.cloud.compute.v1.NetworksClientTest",
        "com.google.cloud.compute.v1.NodeGroupsClientTest",
        "com.google.cloud.compute.v1.NodeTemplatesClientTest",
        "com.google.cloud.compute.v1.NodeTypesClientTest",
        "com.google.cloud.compute.v1.PacketMirroringsClientTest",
        "com.google.cloud.compute.v1.ProjectsClientTest",
        "com.google.cloud.compute.v1.PublicAdvertisedPrefixesClientTest",
        "com.google.cloud.compute.v1.PublicDelegatedPrefixesClientTest",
        "com.google.cloud.compute.v1.RegionAutoscalersClientTest",
        "com.google.cloud.compute.v1.RegionBackendServicesClientTest",
        "com.google.cloud.compute.v1.RegionCommitmentsClientTest",
        "com.google.cloud.compute.v1.RegionDiskTypesClientTest",
        "com.google.cloud.compute.v1.RegionDisksClientTest",
        "com.google.cloud.compute.v1.RegionHealthCheckServicesClientTest",
        "com.google.cloud.compute.v1.RegionHealthChecksClientTest",
        "com.google.cloud.compute.v1.RegionInstanceGroupManagersClientTest",
        "com.google.cloud.compute.v1.RegionInstanceGroupsClientTest",
        "com.google.cloud.compute.v1.RegionInstancesClientTest",
        "com.google.cloud.compute.v1.RegionNetworkEndpointGroupsClientTest",
        "com.google.cloud.compute.v1.RegionNotificationEndpointsClientTest",
        "com.google.cloud.compute.v1.RegionOperationsClientTest",
        "com.google.cloud.compute.v1.RegionSslCertificatesClientTest",
        "com.google.cloud.compute.v1.RegionTargetHttpProxiesClientTest",
        "com.google.cloud.compute.v1.RegionTargetHttpsProxiesClientTest",
        "com.google.cloud.compute.v1.RegionUrlMapsClientTest",
        "com.google.cloud.compute.v1.RegionsClientTest",
        "com.google.cloud.compute.v1.ReservationsClientTest",
        "com.google.cloud.compute.v1.ResourcePoliciesClientTest",
        "com.google.cloud.compute.v1.RoutersClientTest",
        "com.google.cloud.compute.v1.RoutesClientTest",
        "com.google.cloud.compute.v1.SecurityPoliciesClientTest",
        "com.google.cloud.compute.v1.ServiceAttachmentsClientTest",
        "com.google.cloud.compute.v1.SnapshotsClientTest",
        "com.google.cloud.compute.v1.SslCertificatesClientTest",
        "com.google.cloud.compute.v1.SslPoliciesClientTest",
        "com.google.cloud.compute.v1.SubnetworksClientTest",
        "com.google.cloud.compute.v1.TargetGrpcProxiesClientTest",
        "com.google.cloud.compute.v1.TargetHttpProxiesClientTest",
        "com.google.cloud.compute.v1.TargetHttpsProxiesClientTest",
        "com.google.cloud.compute.v1.TargetInstancesClientTest",
        "com.google.cloud.compute.v1.TargetPoolsClientTest",
        "com.google.cloud.compute.v1.TargetSslProxiesClientTest",
        "com.google.cloud.compute.v1.TargetTcpProxiesClientTest",
        "com.google.cloud.compute.v1.TargetVpnGatewaysClientTest",
        "com.google.cloud.compute.v1.UrlMapsClientTest",
        "com.google.cloud.compute.v1.VpnGatewaysClientTest",
        "com.google.cloud.compute.v1.VpnTunnelsClientTest",
        "com.google.cloud.compute.v1.ZoneOperationsClientTest",
        "com.google.cloud.compute.v1.ZonesClientTest",
    ],
    runtime_deps = [":compute_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-compute-v1-java",
    include_samples = True,
    transport = "rest",
    deps = [
        ":compute_java_gapic",
        ":compute_java_proto",
        ":compute_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "compute_py_gapic",
    srcs = [
        ":compute_proto",
    ],
    opt_args = [
        "transport=rest",
    ],
)

py_test(
    name = "compute_py_gapic_test",
    srcs = [
        "compute_py_gapic_pytest.py",
        "compute_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":compute_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "compute-v1-py",
    deps = [
        ":compute_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "compute_php_proto",
    deps = [":compute_proto"],
)

php_gapic_library(
    name = "compute_php_gapic",
    srcs = [":compute_proto_with_info"],
    grpc_service_config = "compute_grpc_service_config.json",
    service_yaml = "compute_v1.yaml",
    transport = "rest",
    migration_mode = "MIGRATING",
    deps = [
        ":compute_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-compute-v1-php",
    deps = [
        ":compute_php_gapic",
        ":compute_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "compute_nodejs_gapic",
    package_name = "@google-cloud/compute",
    src = ":compute_proto_with_info",
    diregapic = True,
    extra_protoc_parameters = ["metadata"],
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "compute-v1-nodejs",
    deps = [
        ":compute_nodejs_gapic",
        ":compute_proto",
        "@com_google_googleapis//google/cloud:extended_operations_proto",
    ],
)

###############################################################################
# Ruby
###############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "compute_ruby_proto",
    deps = [":compute_proto"],
)

ruby_cloud_gapic_library(
    name = "compute_ruby_gapic",
    srcs = [":compute_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=compute.googleapis.com",
        "ruby-cloud-api-shortname=compute",
        "ruby-cloud-gem-name=google-cloud-compute-v1",
        "ruby-cloud-generate-metadata=false",
        "ruby-cloud-generate-transports=rest",
        "ruby-cloud-env-prefix=COMPUTE",
        "ruby-cloud-product-url=https://cloud.google.com/compute/",
        "ruby-cloud-wrapper-gem-override=",
        "ruby-cloud-extra-dependencies=google-cloud-common=~> 1.0",
    ],
    grpc_service_config = ":compute_grpc_service_config.json",
    ruby_cloud_description = "google-cloud-compute-v1 is the official client library for the Google Cloud Compute V1 API.",
    ruby_cloud_title = "Google Cloud Compute V1",
    deps = [
        ":compute_ruby_proto",
    ],
)

ruby_gapic_assembly_pkg(
    name = "google-cloud-compute-v1-ruby",
    deps = [
        ":compute_ruby_gapic",
        ":compute_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "compute_csharp_proto",
    deps = [":compute_proto"],
)

csharp_grpc_library(
    name = "compute_csharp_grpc",
    srcs = [":compute_proto"],
    deps = [":compute_csharp_proto"],
)

csharp_gapic_library(
    name = "compute_csharp_gapic",
    srcs = [":compute_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = ":compute_grpc_service_config.json",
    service_yaml = "compute_v1.yaml",
    transport = "rest",
    deps = [
        ":compute_csharp_grpc",
        ":compute_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-compute-v1-csharp",
    deps = [
        ":compute_csharp_gapic",
        ":compute_csharp_grpc",
        ":compute_csharp_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "compute_go_proto",
    importpath = "cloud.google.com/go/compute/apiv1/computepb",
    protos = [":compute_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/cloud:extended_operations_go_proto",
    ],
)

go_gapic_library(
    name = "compute_go_gapic",
    srcs = [":compute_proto_with_info"],
    diregapic = True,
    grpc_service_config = "compute_grpc_service_config.json",
    importpath = "cloud.google.com/go/compute/apiv1;compute",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = False,
    service_yaml = "compute_v1.yaml",
    transport = "rest",
    deps = [
        ":compute_go_proto",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-compute-v1-go",
    deps = [
        ":compute_go_gapic",
        ":compute_go_gapic_srcjar-metadata.srcjar",
        ":compute_go_gapic_srcjar-snippets.srcjar",
        ":compute_go_gapic_srcjar-test.srcjar",
        ":compute_go_proto",
    ],
)
