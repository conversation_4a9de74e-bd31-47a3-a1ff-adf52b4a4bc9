# This build file includes a target for the Ruby wrapper library for
# google-cloud-compute.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for compute.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "compute_ruby_wrapper",
    srcs = ["//google/cloud/compute/v1:compute_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-compute",
        "ruby-cloud-wrapper-of=v1:2.15",
        "ruby-cloud-product-url=https://cloud.google.com/compute/",
        "ruby-cloud-api-id=compute.googleapis.com",
        "ruby-cloud-api-shortname=compute",
        "ruby-cloud-generate-transports=rest",
        "ruby-cloud-env-prefix=COMPUTE",
    ],
    ruby_cloud_description = "google-cloud-compute is the official client library for the Google Cloud Compute API.",
    ruby_cloud_title = "Google Cloud Compute",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-compute-ruby",
    deps = [
        ":compute_ruby_wrapper",
    ],
)
