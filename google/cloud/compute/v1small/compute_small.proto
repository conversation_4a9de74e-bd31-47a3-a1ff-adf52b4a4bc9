// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Generated by the disco-to-proto3-converter. DO NOT EDIT!
// Source Discovery file: compute.v1small.json
// Source file revision: 20200302
// API name: compute
// API version: v1small

syntax = "proto3";

package google.cloud.compute.v1small;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/extended_operations.proto";

//
// File Options
//
option csharp_namespace = "Google.Cloud.Compute.V1Small";
option go_package = "google.golang.org/genproto/googleapis/cloud/compute/v1small;compute";
option java_multiple_files = true;
option java_package = "com.google.cloud.compute.v1small";
option php_namespace = "Google\\Cloud\\Compute\\V1small";
option ruby_package = "Google::Cloud::Compute::V1small";

//
// Messages
//
// Use global external addresses for GFE-based external HTTP(S) load balancers in Premium Tier.
//
// Use global internal addresses for reserved peering network range.
//
// Use regional external addresses for the following resources:
//
// - External IP addresses for VM instances - Regional external forwarding rules - Cloud NAT external IP addresses - GFE based LBs in Standard Tier - Network LBs in Premium or Standard Tier - Cloud VPN gateways (both Classic and HA)
//
// Use regional internal IP addresses for subnet IP ranges (primary and secondary). This includes:
//
// - Internal IP addresses for VM instances - Alias IP ranges of VM instances (/32 only) - Regional internal forwarding rules - Internal TCP/UDP load balancer addresses - Internal HTTP(S) load balancer addresses - Cloud DNS inbound forwarding IP addresses
//
// For more information, read reserved IP address.
//
// (== resource_for {$api_version}.addresses ==) (== resource_for {$api_version}.globalAddresses ==)
message Address {
  // The type of address to reserve, either INTERNAL or EXTERNAL. If unspecified, defaults to EXTERNAL.
  enum AddressType {
    // A value indicating that the enum field is not set.
    UNDEFINED_ADDRESS_TYPE = 0;

    EXTERNAL = 35607499;

    INTERNAL = 279295677;

    UNSPECIFIED_TYPE = 53933922;

  }

  // The IP version that will be used by this address. Valid options are IPV4 or IPV6. This can only be specified for a global address.
  enum IpVersion {
    // A value indicating that the enum field is not set.
    UNDEFINED_IP_VERSION = 0;

    IPV4 = 2254341;

    IPV6 = 2254343;

    UNSPECIFIED_VERSION = 21850000;

  }

  // This signifies the networking tier used for configuring this address and can only take the following values: PREMIUM or STANDARD. Global forwarding rules can only be Premium Tier. Regional forwarding rules can be either Premium or Standard Tier. Standard Tier addresses applied to regional forwarding rules can be used with any external load balancer. Regional forwarding rules in Premium Tier can only be used with a network load balancer.
  //
  // If this field is not specified, it is assumed to be PREMIUM.
  enum NetworkTier {
    // A value indicating that the enum field is not set.
    UNDEFINED_NETWORK_TIER = 0;

    PREMIUM = 399530551;

    STANDARD = 484642493;

  }

  // The purpose of this resource, which can be one of the following values:
  // - `GCE_ENDPOINT` for addresses that are used by VM instances, alias IP ranges, internal load balancers, and similar resources.
  // - `DNS_RESOLVER` for a DNS resolver address in a subnetwork
  // - `VPC_PEERING` for addresses that are reserved for VPC peer networks.
  // - `NAT_AUTO` for addresses that are external IP addresses automatically reserved for Cloud NAT.
  enum Purpose {
    // A value indicating that the enum field is not set.
    UNDEFINED_PURPOSE = 0;

    DNS_RESOLVER = 476114556;

    GCE_ENDPOINT = 230515243;

    NAT_AUTO = 163666477;

    VPC_PEERING = 400800170;

  }

  // [Output Only] The status of the address, which can be one of RESERVING, RESERVED, or IN_USE. An address that is RESERVING is currently in the process of being reserved. A RESERVED address is currently reserved and available to use. An IN_USE address is currently being used by another resource and is not available.
  enum Status {
    // A value indicating that the enum field is not set.
    UNDEFINED_STATUS = 0;

    IN_USE = 17393485;

    RESERVED = 432241448;

    RESERVING = 514587225;

  }

  // The static IP address represented by this resource.
  optional string address = 462920692;

  // The type of address to reserve, either INTERNAL or EXTERNAL. If unspecified, defaults to EXTERNAL.
  // Check the AddressType enum for the list of possible values.
  optional string address_type = 264307877;

  // [Output Only] Creation timestamp in RFC3339 text format.
  optional string creation_timestamp = 30525366;

  // An optional description of this resource. Provide this field when you create the resource.
  optional string description = 422937596;

  // [Output Only] The unique identifier for the resource. This identifier is defined by the server.
  optional uint64 id = 3355;

  // The IP version that will be used by this address. Valid options are IPV4 or IPV6. This can only be specified for a global address.
  // Check the IpVersion enum for the list of possible values.
  optional string ip_version = 294959552;

  // [Output Only] Type of the resource. Always compute#address for addresses.
  optional string kind = 3292052;

  // Name of the resource. Provided by the client when the resource is created. The name must be 1-63 characters long, and comply with RFC1035. Specifically, the name must be 1-63 characters long and match the regular expression `[a-z]([-a-z0-9]*[a-z0-9])?`. The first character must be a lowercase letter, and all following characters (except for the last character) must be a dash, lowercase letter, or digit. The last character must be a lowercase letter or digit.
  optional string name = 3373707;

  // The URL of the network in which to reserve the address. This field can only be used with INTERNAL type with the VPC_PEERING purpose.
  optional string network = 232872494;

  // This signifies the networking tier used for configuring this address and can only take the following values: PREMIUM or STANDARD. Global forwarding rules can only be Premium Tier. Regional forwarding rules can be either Premium or Standard Tier. Standard Tier addresses applied to regional forwarding rules can be used with any external load balancer. Regional forwarding rules in Premium Tier can only be used with a network load balancer.
  //
  // If this field is not specified, it is assumed to be PREMIUM.
  // Check the NetworkTier enum for the list of possible values.
  optional string network_tier = 517397843;

  // The prefix length if the resource reprensents an IP range.
  optional int32 prefix_length = 453565747;

  // The purpose of this resource, which can be one of the following values:
  // - `GCE_ENDPOINT` for addresses that are used by VM instances, alias IP ranges, internal load balancers, and similar resources.
  // - `DNS_RESOLVER` for a DNS resolver address in a subnetwork
  // - `VPC_PEERING` for addresses that are reserved for VPC peer networks.
  // - `NAT_AUTO` for addresses that are external IP addresses automatically reserved for Cloud NAT.
  // Check the Purpose enum for the list of possible values.
  optional string purpose = 316407070;

  // [Output Only] The URL of the region where the regional address resides. This field is not applicable to global addresses. You must specify this field as part of the HTTP request URL.
  optional string region = 138946292;

  // [Output Only] Server-defined URL for the resource.
  optional string self_link = 456214797;

  // [Output Only] The status of the address, which can be one of RESERVING, RESERVED, or IN_USE. An address that is RESERVING is currently in the process of being reserved. A RESERVED address is currently reserved and available to use. An IN_USE address is currently being used by another resource and is not available.
  // Check the Status enum for the list of possible values.
  optional string status = 181260274;

  // The URL of the subnetwork in which to reserve the address. If an IP address is specified, it must be within the subnetwork's IP range. This field can only be used with INTERNAL type with a GCE_ENDPOINT or DNS_RESOLVER purpose.
  optional string subnetwork = 307827694;

  // [Output Only] The URLs of the resources that are using this address.
  repeated string users = 111578632;

}

//
message AddressAggregatedList {
  // [Output Only] Unique identifier for the resource; defined by the server.
  optional string id = 3355;

  // A list of AddressesScopedList resources.
  map<string, AddressesScopedList> items = 100526016;

  // [Output Only] Type of resource. Always compute#addressAggregatedList for aggregated lists of addresses.
  optional string kind = 3292052;

  // [Output Only] This token allows you to get the next page of results for list requests. If the number of results is larger than maxResults, use the nextPageToken as a value for the query parameter pageToken in the next list request. Subsequent list requests will have their own nextPageToken to continue paging through the results.
  optional string next_page_token = 79797525;

  // [Output Only] Server-defined URL for this resource.
  optional string self_link = 456214797;

  // [Output Only] Informational warning message.
  optional Warning warning = 50704284;

}

// Contains a list of addresses.
message AddressList {
  // [Output Only] Unique identifier for the resource; defined by the server.
  optional string id = 3355;

  // A list of Address resources.
  repeated Address items = 100526016;

  // [Output Only] Type of resource. Always compute#addressList for lists of addresses.
  optional string kind = 3292052;

  // [Output Only] This token allows you to get the next page of results for list requests. If the number of results is larger than maxResults, use the nextPageToken as a value for the query parameter pageToken in the next list request. Subsequent list requests will have their own nextPageToken to continue paging through the results.
  optional string next_page_token = 79797525;

  // [Output Only] Server-defined URL for this resource.
  optional string self_link = 456214797;

  // [Output Only] Informational warning message.
  optional Warning warning = 50704284;

}

//
message AddressesScopedList {
  // [Output Only] A list of addresses contained in this scope.
  repeated Address addresses = 337673122;

  // [Output Only] Informational warning which replaces the list of addresses when the list is empty.
  optional Warning warning = 50704284;

}

// A request message for Addresses.AggregatedList. See the method description for details.
message AggregatedListAddressesRequest {
  // A filter expression that filters resources listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be either `=`, `!=`, `>`, or `<`.
  //
  // For example, if you are filtering Compute Engine instances, you can exclude instances named `example-instance` by specifying `name != example-instance`.
  //
  // You can also filter nested fields. For example, you could specify `scheduling.automaticRestart = false` to include instances only if they are not scheduled for automatic restarts. You can use filtering on nested fields to filter based on resource labels.
  //
  // To filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (scheduling.automaticRestart = true) (cpuPlatform = "Intel Skylake") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart = true) ```
  optional string filter = 336120696;

  // Indicates whether every visible scope for each scope type (zone, region, global) should be included in the response. For new resource types added after this field, the flag has no effect as new resource types will always include every visible scope for each scope type in response. For resource types which predate this field, if this flag is omitted or false, only scopes of the scope types where the resource type is expected to be found will be included.
  optional bool include_all_scopes = 391327988;

  // The maximum number of results per page that should be returned. If the number of available results is larger than `maxResults`, Compute Engine returns a `nextPageToken` that can be used to get the next page of results in subsequent list requests. Acceptable values are `0` to `500`, inclusive. (Default: `500`)
  optional uint32 max_results = 54715419;

  // Sorts list results by a certain order. By default, results are returned in alphanumerical order based on the resource name.
  //
  // You can also sort results in descending order based on the creation timestamp using `orderBy="creationTimestamp desc"`. This sorts results based on the `creationTimestamp` field in reverse chronological order (newest result first). Use this to sort resources like operations so that the newest operation is returned first.
  //
  // Currently, only sorting by `name` or `creationTimestamp desc` is supported.
  optional string order_by = 160562920;

  // Specifies a page token to use. Set `pageToken` to the `nextPageToken` returned by a previous list request to get the next page of results.
  optional string page_token = 19994697;

  // Project ID for this request.
  string project = 227560217 [(google.api.field_behavior) = REQUIRED];

}

//
message Data {
  // [Output Only] A key that provides more detail on the warning being returned. For example, for warnings where there are no results in a list request for a particular zone, this key might be scope and the key value might be the zone name. Other examples might be a key indicating a deprecated resource and a suggested replacement, or a warning about invalid network settings (for example, if an instance attempts to perform IP forwarding but is not enabled for IP forwarding).
  optional string key = 106079;

  // [Output Only] A warning data value corresponding to the key.
  optional string value = 111972721;

}

// A request message for Addresses.Delete. See the method description for details.
message DeleteAddressRequest {
  // Name of the address resource to delete.
  string address = 462920692 [(google.api.field_behavior) = REQUIRED];

  // Project ID for this request.
  string project = 227560217 [
    (google.api.field_behavior) = REQUIRED,
    (google.cloud.operation_request_field) = "project"
  ];

  // Name of the region for this request.
  string region = 138946292 [
    (google.api.field_behavior) = REQUIRED,
    (google.cloud.operation_request_field) = "region"
  ];

  // An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed.
  //
  // For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
  optional string request_id = 37109963;

}

// [Output Only] If errors are generated during processing of the operation, this field will be populated.
message Error {
  // [Output Only] The array of errors encountered while processing this operation.
  repeated Errors errors = 315977579;

}

//
message Errors {
  // [Output Only] The error type identifier for this error.
  optional string code = 3059181;

  // [Output Only] Indicates the field in the request that caused the error. This property is optional.
  optional string location = 290430901;

  // [Output Only] An optional, human-readable error message.
  optional string message = 418054151;

}

// A request message for RegionOperations.Get. See the method description for details.
message GetRegionOperationRequest {
  // Name of the Operations resource to return.
  string operation = 52090215 [
    (google.api.field_behavior) = REQUIRED,
    (google.cloud.operation_response_field) = "name"
  ];

  // Project ID for this request.
  string project = 227560217 [(google.api.field_behavior) = REQUIRED];

  // Name of the region for this request.
  string region = 138946292 [(google.api.field_behavior) = REQUIRED];

}

// A request message for Addresses.Insert. See the method description for details.
message InsertAddressRequest {
  // The body resource for this request
  Address address_resource = 483888121 [(google.api.field_behavior) = REQUIRED];

  // Project ID for this request.
  string project = 227560217 [
    (google.api.field_behavior) = REQUIRED,
    (google.cloud.operation_request_field) = "project"
  ];

  // Name of the region for this request.
  string region = 138946292 [
    (google.api.field_behavior) = REQUIRED,
    (google.cloud.operation_request_field) = "region"
  ];

  // An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed.
  //
  // For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).
  optional string request_id = 37109963;

}

// A request message for Addresses.List. See the method description for details.
message ListAddressesRequest {
  // A filter expression that filters resources listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be either =, !=, >, or <.
  //
  // For example, if you are filtering Compute Engine instances, you can exclude instances named example-instance by specifying name != example-instance.
  //
  // You can also filter nested fields. For example, you could specify scheduling.automaticRestart = false to include instances only if they are not scheduled for automatic restarts. You can use filtering on nested fields to filter based on resource labels.
  //
  // To filter on multiple expressions, provide each separate expression within parentheses. For example, (scheduling.automaticRestart = true) (cpuPlatform = "Intel Skylake"). By default, each expression is an AND expression. However, you can include AND and OR expressions explicitly. For example, (cpuPlatform = "Intel Skylake") OR (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart = true).
  optional string filter = 336120696;

  // The maximum number of results per page that should be returned. If the number of available results is larger than maxResults, Compute Engine returns a nextPageToken that can be used to get the next page of results in subsequent list requests. Acceptable values are 0 to 500, inclusive. (Default: 500)
  optional uint32 max_results = 54715419;

  // Sorts list results by a certain order. By default, results are returned in alphanumerical order based on the resource name.
  //
  // You can also sort results in descending order based on the creation timestamp using orderBy="creationTimestamp desc". This sorts results based on the creationTimestamp field in reverse chronological order (newest result first). Use this to sort resources like operations so that the newest operation is returned first.
  //
  // Currently, only sorting by name or creationTimestamp desc is supported.
  string order_by = 160562920 [(google.api.field_behavior) = REQUIRED];

  // Specifies a page token to use. Set pageToken to the nextPageToken returned by a previous list request to get the next page of results.
  optional string page_token = 19994697;

  // Project ID for this request.
  string project = 227560217 [(google.api.field_behavior) = REQUIRED];

  // Name of the region for this request.
  string region = 138946292 [(google.api.field_behavior) = REQUIRED];

}

// Represents an Operation resource.
//
// Google Compute Engine has three Operation resources:
//
// * [Global](/compute/docs/reference/rest/{$api_version}/globalOperations) * [Regional](/compute/docs/reference/rest/{$api_version}/regionOperations) * [Zonal](/compute/docs/reference/rest/{$api_version}/zoneOperations)
//
// You can use an operation resource to manage asynchronous API requests. For more information, read Handling API responses.
//
// Operations can be global, regional or zonal.
// - For global operations, use the globalOperations resource.
// - For regional operations, use the regionOperations resource.
// - For zonal operations, use the zoneOperations resource.
//
// For more information, read  Global, Regional, and Zonal Resources. (== resource_for {$api_version}.globalOperations ==) (== resource_for {$api_version}.regionOperations ==) (== resource_for {$api_version}.zoneOperations ==)
message Operation {
  // [Output Only] The status of the operation, which can be one of the following: PENDING, RUNNING, or DONE.
  enum Status {
    // A value indicating that the enum field is not set.
    UNDEFINED_STATUS = 0;

    DONE = 2104194;

    PENDING = 35394935;

    RUNNING = 121282975;

  }

  // [Output Only] The value of `requestId` if you provided it in the request. Not present otherwise.
  optional string client_operation_id = 297240295;

  // [Deprecated] This field is deprecated.
  optional string creation_timestamp = 30525366;

  // [Output Only] A textual description of the operation, which is set when the operation is created.
  optional string description = 422937596;

  // [Output Only] The time that this operation was completed. This value is in RFC3339 text format.
  optional string end_time = 114938801;

  // [Output Only] If errors are generated during processing of the operation, this field will be populated.
  optional Error error = 96784904;

  // [Output Only] If the operation fails, this field contains the HTTP error message that was returned, such as NOT FOUND.
  optional string http_error_message = 202521945 [(google.cloud.operation_field) = ERROR_MESSAGE];

  // [Output Only] If the operation fails, this field contains the HTTP error status code that was returned. For example, a 404 means the resource was not found.
  optional int32 http_error_status_code = 312345196 [(google.cloud.operation_field) = ERROR_CODE];

  // [Output Only] The unique identifier for the operation. This identifier is defined by the server.
  optional uint64 id = 3355;

  // [Output Only] The time that this operation was requested. This value is in RFC3339 text format.
  optional string insert_time = 433722515;

  // [Output Only] Type of the resource. Always compute#operation for Operation resources.
  optional string kind = 3292052;

  // [Output Only] Name of the operation.
  optional string name = 3373707 [(google.cloud.operation_field) = NAME];

  // [Output Only] The type of operation, such as insert, update, or delete, and so on.
  optional string operation_type = 177650450;

  // [Output Only] An optional progress indicator that ranges from 0 to 100. There is no requirement that this be linear or support any granularity of operations. This should not be used to guess when the operation will be complete. This number should monotonically increase as the operation progresses.
  optional int32 progress = 72663597;

  // [Output Only] The URL of the region where the operation resides. Only applicable when performing regional operations.
  optional string region = 138946292;

  // [Output Only] Server-defined URL for the resource.
  optional string self_link = 456214797;

  // [Output Only] The time that this operation was started by the server. This value is in RFC3339 text format.
  optional string start_time = 37467274;

  // [Output Only] The status of the operation, which can be one of the following: PENDING, RUNNING, or DONE.
  optional Status status = 181260274 [(google.cloud.operation_field) = STATUS];

  // [Output Only] An optional textual description of the current status of the operation.
  optional string status_message = 297428154;

  // [Output Only] The unique target ID, which identifies a specific incarnation of the target resource.
  optional uint64 target_id = 258165385;

  // [Output Only] The URL of the resource that the operation modifies. For operations related to creating a snapshot, this points to the persistent disk that the snapshot was created from.
  optional string target_link = 62671336;

  // [Output Only] User who requested the operation, for example: <EMAIL>.
  optional string user = 3599307;

  // [Output Only] If warning messages are generated during processing of the operation, this field will be populated.
  repeated Warnings warnings = 498091095;

  // [Output Only] The URL of the zone where the operation resides. Only applicable when performing per-zone operations.
  optional string zone = 3744684;

}

// A request message for RegionOperations.Wait. See the method description for details.
message WaitRegionOperationRequest {
  // Name of the Operations resource to return.
  string operation = 52090215 [(google.api.field_behavior) = REQUIRED];

  // Project ID for this request.
  string project = 227560217 [(google.api.field_behavior) = REQUIRED];

  // Name of the region for this request.
  string region = 138946292 [(google.api.field_behavior) = REQUIRED];

}

// [Output Only] Informational warning message.
message Warning {
  // [Output Only] A warning code, if applicable. For example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no results in the response.
  enum Code {
    // A value indicating that the enum field is not set.
    UNDEFINED_CODE = 0;

    CLEANUP_FAILED = 150308440;

    DEPRECATED_RESOURCE_USED = 391835586;

    DEPRECATED_TYPE_USED = 346526230;

    DISK_SIZE_LARGER_THAN_IMAGE_SIZE = 369442967;

    EXPERIMENTAL_TYPE_USED = 451954443;

    EXTERNAL_API_WARNING = 175546307;

    FIELD_VALUE_OVERRIDEN = 329669423;

    INJECTED_KERNELS_DEPRECATED = 417377419;

    MISSING_TYPE_DEPENDENCY = 344505463;

    NEXT_HOP_ADDRESS_NOT_ASSIGNED = 324964999;

    NEXT_HOP_CANNOT_IP_FORWARD = 383382887;

    NEXT_HOP_INSTANCE_NOT_FOUND = 464250446;

    NEXT_HOP_INSTANCE_NOT_ON_NETWORK = 243758146;

    NEXT_HOP_NOT_RUNNING = 417081265;

    NOT_CRITICAL_ERROR = 105763924;

    NO_RESULTS_ON_PAGE = 30036744;

    REQUIRED_TOS_AGREEMENT = 3745539;

    RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING = 496728641;

    RESOURCE_NOT_DELETED = 168598460;

    SCHEMA_VALIDATION_IGNORED = 275245642;

    SINGLE_INSTANCE_PROPERTY_TEMPLATE = 268305617;

    UNDECLARED_PROPERTIES = 390513439;

    UNREACHABLE = 13328052;

  }

  // [Output Only] A warning code, if applicable. For example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no results in the response.
  // Check the Code enum for the list of possible values.
  optional string code = 3059181;

  // [Output Only] Metadata about this warning in key: value format. For example:
  // "data": [ { "key": "scope", "value": "zones/us-east1-d" }
  repeated Data data = 3076010;

  // [Output Only] A human-readable description of the warning code.
  optional string message = 418054151;

}

//
message Warnings {
  // [Output Only] A warning code, if applicable. For example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no results in the response.
  enum Code {
    // A value indicating that the enum field is not set.
    UNDEFINED_CODE = 0;

    CLEANUP_FAILED = 150308440;

    DEPRECATED_RESOURCE_USED = 391835586;

    DEPRECATED_TYPE_USED = 346526230;

    DISK_SIZE_LARGER_THAN_IMAGE_SIZE = 369442967;

    EXPERIMENTAL_TYPE_USED = 451954443;

    EXTERNAL_API_WARNING = 175546307;

    FIELD_VALUE_OVERRIDEN = 329669423;

    INJECTED_KERNELS_DEPRECATED = 417377419;

    MISSING_TYPE_DEPENDENCY = 344505463;

    NEXT_HOP_ADDRESS_NOT_ASSIGNED = 324964999;

    NEXT_HOP_CANNOT_IP_FORWARD = 383382887;

    NEXT_HOP_INSTANCE_NOT_FOUND = 464250446;

    NEXT_HOP_INSTANCE_NOT_ON_NETWORK = 243758146;

    NEXT_HOP_NOT_RUNNING = 417081265;

    NOT_CRITICAL_ERROR = 105763924;

    NO_RESULTS_ON_PAGE = 30036744;

    REQUIRED_TOS_AGREEMENT = 3745539;

    RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING = 496728641;

    RESOURCE_NOT_DELETED = 168598460;

    SCHEMA_VALIDATION_IGNORED = 275245642;

    SINGLE_INSTANCE_PROPERTY_TEMPLATE = 268305617;

    UNDECLARED_PROPERTIES = 390513439;

    UNREACHABLE = 13328052;

  }

  // [Output Only] A warning code, if applicable. For example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no results in the response.
  // Check the Code enum for the list of possible values.
  optional string code = 3059181;

  // [Output Only] Metadata about this warning in key: value format. For example:
  // "data": [ { "key": "scope", "value": "zones/us-east1-d" }
  repeated Data data = 3076010;

  // [Output Only] A human-readable description of the warning code.
  optional string message = 418054151;

}

//
// Services
//
// The Addresses API.
service Addresses {
  option (google.api.default_host) =
    "compute.googleapis.com";

  option (google.api.oauth_scopes) =
    "https://www.googleapis.com/auth/compute,"
    "https://www.googleapis.com/auth/cloud-platform";

  // Retrieves an aggregated list of addresses.
  rpc AggregatedList(AggregatedListAddressesRequest) returns (AddressAggregatedList) {
    option (google.api.http) = {
      get: "/compute/v1/projects/{project}/aggregated/addresses"
    };
    option (google.api.method_signature) = "project";
  }

  // Deletes the specified address resource.
  rpc Delete(DeleteAddressRequest) returns (Operation) {
    option (google.api.http) = {
      delete: "/compute/v1/projects/{project}/regions/{region}/addresses/{address}"
    };
    option (google.api.method_signature) = "project,region,address";
    option (google.cloud.operation_service) = "RegionOperations";
  }

  // Creates an address resource in the specified project by using the data included in the request.
  rpc Insert(InsertAddressRequest) returns (Operation) {
    option (google.api.http) = {
      body: "address_resource"
      post: "/compute/v1/projects/{project}/regions/{region}/addresses"
    };
    option (google.api.method_signature) = "project,region,address_resource";
    option (google.cloud.operation_service) = "RegionOperations";
  }

  // Retrieves a list of addresses contained within the specified region.
  rpc List(ListAddressesRequest) returns (AddressList) {
    option (google.api.http) = {
      get: "/compute/v1/projects/{project}/regions/{region}/addresses"
    };
    option (google.api.method_signature) = "project,region,order_by";
  }

}

// The RegionOperations API.
service RegionOperations {
  option (google.api.default_host) =
    "compute.googleapis.com";

  option (google.api.oauth_scopes) =
    "https://www.googleapis.com/auth/compute.readonly,"
    "https://www.googleapis.com/auth/compute,"
    "https://www.googleapis.com/auth/cloud-platform";

  // Retrieves the specified region-specific Operations resource.
  rpc Get(GetRegionOperationRequest) returns (Operation) {
    option (google.api.http) = {
      get: "/compute/v1/projects/{project}/regions/{region}/operations/{operation}"
    };
    option (google.api.method_signature) = "project,region,operation";
    option (google.cloud.operation_polling_method) = true;
  }

  // Waits for the specified Operation resource to return as `DONE` or for the request to approach the 2 minute deadline, and retrieves the specified Operation resource. This method differs from the `GET` method in that it waits for no more than the default deadline (2 minutes) and then returns the current state of the operation, which might be `DONE` or still in progress.
  //
  // This method is called on a best-effort basis. Specifically:
  // - In uncommon cases, when the server is overloaded, the request might return before the default deadline is reached, or might return after zero seconds.
  // - If the default deadline is reached, there is no guarantee that the operation is actually done when the method returns. Be prepared to retry if the operation is not `DONE`.
  rpc Wait(WaitRegionOperationRequest) returns (Operation) {
    option (google.api.http) = {
      post: "/compute/v1/projects/projects/{project}/regions/{region}/operations/{operation}/wait"
    };
    option (google.api.method_signature) = "project,region,operation";
  }

}

