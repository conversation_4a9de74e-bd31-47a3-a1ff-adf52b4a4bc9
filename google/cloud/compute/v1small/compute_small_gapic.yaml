# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http:#www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the disco-to-proto3-converter. DO NOT EDIT!
# Source Discovery file: compute.v1small.json
# Source file revision: 20200302
# API name: compute
# API version: v1small

type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  java:
    package_name: com.google.cloud.compute.v1small
interfaces:
- name: google.cloud.compute.v1small.Addresses
  methods:
  - name: Delete
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
  - name: Insert
    long_running:
      initial_poll_delay_millis: 500
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 20000
      total_poll_timeout_millis: 600000
