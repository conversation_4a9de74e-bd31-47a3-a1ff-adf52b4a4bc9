{"kind": "discovery#restDescription", "etag": "\"u9GIe6H63LSGq-9_t39K2Zx_EAc/FxLyjO3NNw-MCcpaZiOfen7ZKXY\"", "discoveryVersion": "v1", "id": "compute:v1", "name": "compute", "version": "v1small", "revision": "20200302", "title": "Compute Engine API", "description": "Creates and runs virtual machines on Google Cloud Platform.", "ownerDomain": "google.com", "ownerName": "Google", "icons": {"x16": "https://www.google.com/images/icons/product/compute_engine-16.png", "x32": "https://www.google.com/images/icons/product/compute_engine-32.png"}, "documentationLink": "https://developers.google.com/compute/docs/reference/latest/", "protocol": "rest", "baseUrl": "https://compute.googleapis.com/compute/v1/projects/", "basePath": "/compute/v1/projects/", "rootUrl": "https://compute.googleapis.com/", "servicePath": "compute/v1/projects/", "batchPath": "batch/compute/v1", "parameters": {"alt": {"type": "string", "description": "Data format for the response.", "default": "json", "enum": ["json"], "enumDescriptions": ["Responses with Content-Type of application/json"], "location": "query"}, "fields": {"type": "string", "description": "Selector specifying which fields to include in a partial response.", "location": "query"}, "key": {"type": "string", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query"}, "oauth_token": {"type": "string", "description": "OAuth 2.0 token for the current user.", "location": "query"}, "prettyPrint": {"type": "boolean", "description": "Returns response with indentations and line breaks.", "default": "true", "location": "query"}, "quotaUser": {"type": "string", "description": "An opaque string that represents a user for quota purposes. Must not exceed 40 characters.", "location": "query"}, "userIp": {"type": "string", "description": "Deprecated. Please use quotaUser instead.", "location": "query"}}, "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "View and manage your data across Google Cloud Platform services"}, "https://www.googleapis.com/auth/compute": {"description": "View and manage your Google Compute Engine resources"}, "https://www.googleapis.com/auth/compute.readonly": {"description": "View your Google Compute Engine resources"}, "https://www.googleapis.com/auth/devstorage.full_control": {"description": "Manage your data and permissions in Google Cloud Storage"}, "https://www.googleapis.com/auth/devstorage.read_only": {"description": "View your data in Google Cloud Storage"}, "https://www.googleapis.com/auth/devstorage.read_write": {"description": "Manage your data in Google Cloud Storage"}}}}, "schemas": {"Operation": {"id": "Operation", "type": "object", "description": "Represents an Operation resource.\n\nGoogle Compute Engine has three Operation resources:\n\n* [Global](/compute/docs/reference/rest/{$api_version}/globalOperations) * [Regional](/compute/docs/reference/rest/{$api_version}/regionOperations) * [Zonal](/compute/docs/reference/rest/{$api_version}/zoneOperations)\n\nYou can use an operation resource to manage asynchronous API requests. For more information, read Handling API responses.\n\nOperations can be global, regional or zonal.  \n- For global operations, use the globalOperations resource. \n- For regional operations, use the regionOperations resource. \n- For zonal operations, use the zoneOperations resource.  \n\nFor more information, read  Global, Regional, and Zonal Resources. (== resource_for {$api_version}.globalOperations ==) (== resource_for {$api_version}.regionOperations ==) (== resource_for {$api_version}.zoneOperations ==)", "properties": {"clientOperationId": {"type": "string", "description": "[Output Only] The value of `requestId` if you provided it in the request. Not present otherwise."}, "creationTimestamp": {"type": "string", "description": "[Deprecated] This field is deprecated."}, "description": {"type": "string", "description": "[Output Only] A textual description of the operation, which is set when the operation is created."}, "endTime": {"type": "string", "description": "[Output Only] The time that this operation was completed. This value is in RFC3339 text format."}, "error": {"type": "object", "description": "[Output Only] If errors are generated during processing of the operation, this field will be populated.", "properties": {"errors": {"type": "array", "description": "[Output Only] The array of errors encountered while processing this operation.", "items": {"type": "object", "properties": {"code": {"type": "string", "description": "[Output Only] The error type identifier for this error."}, "location": {"type": "string", "description": "[Output Only] Indicates the field in the request that caused the error. This property is optional."}, "message": {"type": "string", "description": "[Output Only] An optional, human-readable error message."}}}}}}, "httpErrorMessage": {"type": "string", "description": "[Output Only] If the operation fails, this field contains the HTTP error message that was returned, such as NOT FOUND."}, "httpErrorStatusCode": {"type": "integer", "description": "[Output Only] If the operation fails, this field contains the HTTP error status code that was returned. For example, a 404 means the resource was not found.", "format": "int32"}, "id": {"type": "string", "description": "[Output Only] The unique identifier for the operation. This identifier is defined by the server.", "format": "uint64"}, "insertTime": {"type": "string", "description": "[Output Only] The time that this operation was requested. This value is in RFC3339 text format."}, "kind": {"type": "string", "description": "[Output Only] Type of the resource. Always compute#operation for Operation resources.", "default": "compute#operation"}, "name": {"type": "string", "description": "[Output Only] Name of the operation."}, "operationType": {"type": "string", "description": "[Output Only] The type of operation, such as insert, update, or delete, and so on."}, "progress": {"type": "integer", "description": "[Output Only] An optional progress indicator that ranges from 0 to 100. There is no requirement that this be linear or support any granularity of operations. This should not be used to guess when the operation will be complete. This number should monotonically increase as the operation progresses.", "format": "int32"}, "region": {"type": "string", "description": "[Output Only] The URL of the region where the operation resides. Only applicable when performing regional operations."}, "selfLink": {"type": "string", "description": "[Output Only] Server-defined URL for the resource."}, "startTime": {"type": "string", "description": "[Output Only] The time that this operation was started by the server. This value is in RFC3339 text format."}, "status": {"type": "string", "description": "[Output Only] The status of the operation, which can be one of the following: PENDING, RUNNING, or DONE.", "enum": ["DONE", "PENDING", "RUNNING"], "enumDescriptions": ["", "", ""]}, "statusMessage": {"type": "string", "description": "[Output Only] An optional textual description of the current status of the operation."}, "targetId": {"type": "string", "description": "[Output Only] The unique target ID, which identifies a specific incarnation of the target resource.", "format": "uint64"}, "targetLink": {"type": "string", "description": "[Output Only] The URL of the resource that the operation modifies. For operations related to creating a snapshot, this points to the persistent disk that the snapshot was created from."}, "user": {"type": "string", "description": "[Output Only] User who requested the operation, for example: <EMAIL>."}, "warnings": {"type": "array", "description": "[Output Only] If warning messages are generated during processing of the operation, this field will be populated.", "items": {"type": "object", "properties": {"code": {"type": "string", "description": "[Output Only] A warning code, if applicable. For example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no results in the response.", "enum": ["CLEANUP_FAILED", "DEPRECATED_RESOURCE_USED", "DEPRECATED_TYPE_USED", "DISK_SIZE_LARGER_THAN_IMAGE_SIZE", "EXPERIMENTAL_TYPE_USED", "EXTERNAL_API_WARNING", "FIELD_VALUE_OVERRIDEN", "INJECTED_KERNELS_DEPRECATED", "MISSING_TYPE_DEPENDENCY", "NEXT_HOP_ADDRESS_NOT_ASSIGNED", "NEXT_HOP_CANNOT_IP_FORWARD", "NEXT_HOP_INSTANCE_NOT_FOUND", "NEXT_HOP_INSTANCE_NOT_ON_NETWORK", "NEXT_HOP_NOT_RUNNING", "NOT_CRITICAL_ERROR", "NO_RESULTS_ON_PAGE", "REQUIRED_TOS_AGREEMENT", "RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING", "RESOURCE_NOT_DELETED", "SCHEMA_VALIDATION_IGNORED", "SINGLE_INSTANCE_PROPERTY_TEMPLATE", "UNDECLARED_PROPERTIES", "UNREACHABLE"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "data": {"type": "array", "description": "[Output Only] Metadata about this warning in key: value format. For example:\n\"data\": [ { \"key\": \"scope\", \"value\": \"zones/us-east1-d\" }", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "[Output Only] A key that provides more detail on the warning being returned. For example, for warnings where there are no results in a list request for a particular zone, this key might be scope and the key value might be the zone name. Other examples might be a key indicating a deprecated resource and a suggested replacement, or a warning about invalid network settings (for example, if an instance attempts to perform IP forwarding but is not enabled for IP forwarding)."}, "value": {"type": "string", "description": "[Output Only] A warning data value corresponding to the key."}}}}, "message": {"type": "string", "description": "[Output Only] A human-readable description of the warning code."}}}}, "zone": {"type": "string", "description": "[Output Only] The URL of the zone where the operation resides. Only applicable when performing per-zone operations."}}}, "Address": {"id": "Address", "type": "object", "description": "Use global external addresses for GFE-based external HTTP(S) load balancers in Premium Tier.\n\nUse global internal addresses for reserved peering network range.\n\nUse regional external addresses for the following resources:\n\n- External IP addresses for VM instances - Regional external forwarding rules - Cloud NAT external IP addresses - GFE based LBs in Standard Tier - Network LBs in Premium or Standard Tier - Cloud VPN gateways (both Classic and HA)\n\nUse regional internal IP addresses for subnet IP ranges (primary and secondary). This includes:\n\n- Internal IP addresses for VM instances - Alias IP ranges of VM instances (/32 only) - Regional internal forwarding rules - Internal TCP/UDP load balancer addresses - Internal HTTP(S) load balancer addresses - Cloud DNS inbound forwarding IP addresses\n\nFor more information, read reserved IP address.\n\n(== resource_for {$api_version}.addresses ==) (== resource_for {$api_version}.globalAddresses ==)", "properties": {"address": {"type": "string", "description": "The static IP address represented by this resource."}, "addressType": {"type": "string", "description": "The type of address to reserve, either INTERNAL or EXTERNAL. If unspecified, defaults to EXTERNAL.", "enum": ["EXTERNAL", "INTERNAL", "UNSPECIFIED_TYPE"], "enumDescriptions": ["", "", ""]}, "creationTimestamp": {"type": "string", "description": "[Output Only] Creation timestamp in RFC3339 text format."}, "description": {"type": "string", "description": "An optional description of this resource. Provide this field when you create the resource."}, "id": {"type": "string", "description": "[Output Only] The unique identifier for the resource. This identifier is defined by the server.", "format": "uint64"}, "ipVersion": {"type": "string", "description": "The IP version that will be used by this address. Valid options are IPV4 or IPV6. This can only be specified for a global address.", "enum": ["IPV4", "IPV6", "UNSPECIFIED_VERSION"], "enumDescriptions": ["", "", ""]}, "kind": {"type": "string", "description": "[Output Only] Type of the resource. Always compute#address for addresses.", "default": "compute#address"}, "name": {"type": "string", "description": "Name of the resource. Provided by the client when the resource is created. The name must be 1-63 characters long, and comply with RFC1035. Specifically, the name must be 1-63 characters long and match the regular expression `[a-z]([-a-z0-9]*[a-z0-9])?`. The first character must be a lowercase letter, and all following characters (except for the last character) must be a dash, lowercase letter, or digit. The last character must be a lowercase letter or digit.", "pattern": "[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?", "annotations": {"required": ["compute.addresses.insert"]}}, "network": {"type": "string", "description": "The URL of the network in which to reserve the address. This field can only be used with INTERNAL type with the VPC_PEERING purpose."}, "networkTier": {"type": "string", "description": "This signifies the networking tier used for configuring this address and can only take the following values: PREMIUM or STANDARD. Global forwarding rules can only be Premium Tier. Regional forwarding rules can be either Premium or Standard Tier. Standard Tier addresses applied to regional forwarding rules can be used with any external load balancer. Regional forwarding rules in Premium Tier can only be used with a network load balancer.\n\nIf this field is not specified, it is assumed to be PREMIUM.", "enum": ["PREMIUM", "STANDARD"], "enumDescriptions": ["", ""]}, "prefixLength": {"type": "integer", "description": "The prefix length if the resource reprensents an IP range.", "format": "int32"}, "purpose": {"type": "string", "description": "The purpose of this resource, which can be one of the following values:  \n- `GCE_ENDPOINT` for addresses that are used by VM instances, alias IP ranges, internal load balancers, and similar resources. \n- `DNS_RESOLVER` for a DNS resolver address in a subnetwork \n- `VPC_PEERING` for addresses that are reserved for VPC peer networks. \n- `NAT_AUTO` for addresses that are external IP addresses automatically reserved for Cloud NAT.", "enum": ["DNS_RESOLVER", "GCE_ENDPOINT", "NAT_AUTO", "VPC_PEERING"], "enumDescriptions": ["", "", "", ""]}, "region": {"type": "string", "description": "[Output Only] The URL of the region where the regional address resides. This field is not applicable to global addresses. You must specify this field as part of the HTTP request URL."}, "selfLink": {"type": "string", "description": "[Output Only] Server-defined URL for the resource."}, "status": {"type": "string", "description": "[Output Only] The status of the address, which can be one of RESERVING, RESERVED, or IN_USE. An address that is RESERVING is currently in the process of being reserved. A RESERVED address is currently reserved and available to use. An IN_USE address is currently being used by another resource and is not available.", "enum": ["IN_USE", "RESERVED", "RESERVING"], "enumDescriptions": ["", "", ""]}, "subnetwork": {"type": "string", "description": "The URL of the subnetwork in which to reserve the address. If an IP address is specified, it must be within the subnetwork's IP range. This field can only be used with INTERNAL type with a GCE_ENDPOINT or DNS_RESOLVER purpose."}, "users": {"type": "array", "description": "[Output Only] The URLs of the resources that are using this address.", "items": {"type": "string"}}}}, "AddressAggregatedList": {"id": "AddressAggregatedList", "type": "object", "properties": {"id": {"type": "string", "description": "[Output Only] Unique identifier for the resource; defined by the server."}, "items": {"type": "object", "description": "A list of AddressesScopedList resources.", "additionalProperties": {"$ref": "AddressesScopedList", "description": "[Output Only] Name of the scope containing this set of addresses."}}, "kind": {"type": "string", "description": "[Output Only] Type of resource. Always compute#addressAggregatedList for aggregated lists of addresses.", "default": "compute#addressAggregatedList"}, "nextPageToken": {"type": "string", "description": "[Output Only] This token allows you to get the next page of results for list requests. If the number of results is larger than maxResults, use the nextPageToken as a value for the query parameter pageToken in the next list request. Subsequent list requests will have their own nextPageToken to continue paging through the results."}, "selfLink": {"type": "string", "description": "[Output Only] Server-defined URL for this resource."}, "warning": {"type": "object", "description": "[Output Only] Informational warning message.", "properties": {"code": {"type": "string", "description": "[Output Only] A warning code, if applicable. For example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no results in the response.", "enum": ["CLEANUP_FAILED", "DEPRECATED_RESOURCE_USED", "DEPRECATED_TYPE_USED", "DISK_SIZE_LARGER_THAN_IMAGE_SIZE", "EXPERIMENTAL_TYPE_USED", "EXTERNAL_API_WARNING", "FIELD_VALUE_OVERRIDEN", "INJECTED_KERNELS_DEPRECATED", "MISSING_TYPE_DEPENDENCY", "NEXT_HOP_ADDRESS_NOT_ASSIGNED", "NEXT_HOP_CANNOT_IP_FORWARD", "NEXT_HOP_INSTANCE_NOT_FOUND", "NEXT_HOP_INSTANCE_NOT_ON_NETWORK", "NEXT_HOP_NOT_RUNNING", "NOT_CRITICAL_ERROR", "NO_RESULTS_ON_PAGE", "REQUIRED_TOS_AGREEMENT", "RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING", "RESOURCE_NOT_DELETED", "SCHEMA_VALIDATION_IGNORED", "SINGLE_INSTANCE_PROPERTY_TEMPLATE", "UNDECLARED_PROPERTIES", "UNREACHABLE"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "data": {"type": "array", "description": "[Output Only] Metadata about this warning in key: value format. For example:\n\"data\": [ { \"key\": \"scope\", \"value\": \"zones/us-east1-d\" }", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "[Output Only] A key that provides more detail on the warning being returned. For example, for warnings where there are no results in a list request for a particular zone, this key might be scope and the key value might be the zone name. Other examples might be a key indicating a deprecated resource and a suggested replacement, or a warning about invalid network settings (for example, if an instance attempts to perform IP forwarding but is not enabled for IP forwarding)."}, "value": {"type": "string", "description": "[Output Only] A warning data value corresponding to the key."}}}}, "message": {"type": "string", "description": "[Output Only] A human-readable description of the warning code."}}}}}, "AddressList": {"id": "AddressList", "type": "object", "description": "Contains a list of addresses.", "properties": {"id": {"type": "string", "description": "[Output Only] Unique identifier for the resource; defined by the server."}, "items": {"type": "array", "description": "A list of Address resources.", "items": {"$ref": "Address"}}, "kind": {"type": "string", "description": "[Output Only] Type of resource. Always compute#addressList for lists of addresses.", "default": "compute#addressList"}, "nextPageToken": {"type": "string", "description": "[Output Only] This token allows you to get the next page of results for list requests. If the number of results is larger than maxResults, use the nextPageToken as a value for the query parameter pageToken in the next list request. Subsequent list requests will have their own nextPageToken to continue paging through the results."}, "selfLink": {"type": "string", "description": "[Output Only] Server-defined URL for this resource."}, "warning": {"type": "object", "description": "[Output Only] Informational warning message.", "properties": {"code": {"type": "string", "description": "[Output Only] A warning code, if applicable. For example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no results in the response.", "enum": ["CLEANUP_FAILED", "DEPRECATED_RESOURCE_USED", "DEPRECATED_TYPE_USED", "DISK_SIZE_LARGER_THAN_IMAGE_SIZE", "EXPERIMENTAL_TYPE_USED", "EXTERNAL_API_WARNING", "FIELD_VALUE_OVERRIDEN", "INJECTED_KERNELS_DEPRECATED", "MISSING_TYPE_DEPENDENCY", "NEXT_HOP_ADDRESS_NOT_ASSIGNED", "NEXT_HOP_CANNOT_IP_FORWARD", "NEXT_HOP_INSTANCE_NOT_FOUND", "NEXT_HOP_INSTANCE_NOT_ON_NETWORK", "NEXT_HOP_NOT_RUNNING", "NOT_CRITICAL_ERROR", "NO_RESULTS_ON_PAGE", "REQUIRED_TOS_AGREEMENT", "RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING", "RESOURCE_NOT_DELETED", "SCHEMA_VALIDATION_IGNORED", "SINGLE_INSTANCE_PROPERTY_TEMPLATE", "UNDECLARED_PROPERTIES", "UNREACHABLE"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "data": {"type": "array", "description": "[Output Only] Metadata about this warning in key: value format. For example:\n\"data\": [ { \"key\": \"scope\", \"value\": \"zones/us-east1-d\" }", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "[Output Only] A key that provides more detail on the warning being returned. For example, for warnings where there are no results in a list request for a particular zone, this key might be scope and the key value might be the zone name. Other examples might be a key indicating a deprecated resource and a suggested replacement, or a warning about invalid network settings (for example, if an instance attempts to perform IP forwarding but is not enabled for IP forwarding)."}, "value": {"type": "string", "description": "[Output Only] A warning data value corresponding to the key."}}}}, "message": {"type": "string", "description": "[Output Only] A human-readable description of the warning code."}}}}}, "AddressesScopedList": {"id": "AddressesScopedList", "type": "object", "properties": {"addresses": {"type": "array", "description": "[Output Only] A list of addresses contained in this scope.", "items": {"$ref": "Address"}}, "warning": {"type": "object", "description": "[Output Only] Informational warning which replaces the list of addresses when the list is empty.", "properties": {"code": {"type": "string", "description": "[Output Only] A warning code, if applicable. For example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no results in the response.", "enum": ["CLEANUP_FAILED", "DEPRECATED_RESOURCE_USED", "DEPRECATED_TYPE_USED", "DISK_SIZE_LARGER_THAN_IMAGE_SIZE", "EXPERIMENTAL_TYPE_USED", "EXTERNAL_API_WARNING", "FIELD_VALUE_OVERRIDEN", "INJECTED_KERNELS_DEPRECATED", "MISSING_TYPE_DEPENDENCY", "NEXT_HOP_ADDRESS_NOT_ASSIGNED", "NEXT_HOP_CANNOT_IP_FORWARD", "NEXT_HOP_INSTANCE_NOT_FOUND", "NEXT_HOP_INSTANCE_NOT_ON_NETWORK", "NEXT_HOP_NOT_RUNNING", "NOT_CRITICAL_ERROR", "NO_RESULTS_ON_PAGE", "REQUIRED_TOS_AGREEMENT", "RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING", "RESOURCE_NOT_DELETED", "SCHEMA_VALIDATION_IGNORED", "SINGLE_INSTANCE_PROPERTY_TEMPLATE", "UNDECLARED_PROPERTIES", "UNREACHABLE"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}, "data": {"type": "array", "description": "[Output Only] Metadata about this warning in key: value format. For example:\n\"data\": [ { \"key\": \"scope\", \"value\": \"zones/us-east1-d\" }", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "[Output Only] A key that provides more detail on the warning being returned. For example, for warnings where there are no results in a list request for a particular zone, this key might be scope and the key value might be the zone name. Other examples might be a key indicating a deprecated resource and a suggested replacement, or a warning about invalid network settings (for example, if an instance attempts to perform IP forwarding but is not enabled for IP forwarding)."}, "value": {"type": "string", "description": "[Output Only] A warning data value corresponding to the key."}}}}, "message": {"type": "string", "description": "[Output Only] A human-readable description of the warning code."}}}}}}, "resources": {"addresses": {"methods": {"aggregatedList": {"id": "compute.addresses.aggregatedList", "path": "{project}/aggregated/addresses", "httpMethod": "GET", "description": "Retrieves an aggregated list of addresses.", "parameters": {"filter": {"type": "string", "description": "A filter expression that filters resources listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be either `=`, `!=`, `>`, or `<`.\n\nFor example, if you are filtering Compute Engine instances, you can exclude instances named `example-instance` by specifying `name != example-instance`.\n\nYou can also filter nested fields. For example, you could specify `scheduling.automaticRestart = false` to include instances only if they are not scheduled for automatic restarts. You can use filtering on nested fields to filter based on resource labels.\n\nTo filter on multiple expressions, provide each separate expression within parentheses. For example: ``` (scheduling.automaticRestart = true) (cpuPlatform = \"Intel Skylake\") ``` By default, each expression is an `AND` expression. However, you can include `AND` and `OR` expressions explicitly. For example: ``` (cpuPlatform = \"Intel Skylake\") OR (cpuPlatform = \"Intel Broadwell\") AND (scheduling.automaticRestart = true) ```", "location": "query"}, "includeAllScopes": {"type": "boolean", "description": "Indicates whether every visible scope for each scope type (zone, region, global) should be included in the response. For new resource types added after this field, the flag has no effect as new resource types will always include every visible scope for each scope type in response. For resource types which predate this field, if this flag is omitted or false, only scopes of the scope types where the resource type is expected to be found will be included.", "location": "query"}, "maxResults": {"type": "integer", "description": "The maximum number of results per page that should be returned. If the number of available results is larger than `maxResults`, Compute Engine returns a `nextPageToken` that can be used to get the next page of results in subsequent list requests. Acceptable values are `0` to `500`, inclusive. (Default: `500`)", "default": "500", "format": "uint32", "minimum": "0", "location": "query"}, "orderBy": {"type": "string", "description": "Sorts list results by a certain order. By default, results are returned in alphanumerical order based on the resource name.\n\nYou can also sort results in descending order based on the creation timestamp using `orderBy=\"creationTimestamp desc\"`. This sorts results based on the `creationTimestamp` field in reverse chronological order (newest result first). Use this to sort resources like operations so that the newest operation is returned first.\n\nCurrently, only sorting by `name` or `creationTimestamp desc` is supported.", "location": "query"}, "pageToken": {"type": "string", "description": "Specifies a page token to use. Set `pageToken` to the `nextPageToken` returned by a previous list request to get the next page of results.", "location": "query"}, "project": {"type": "string", "description": "Project ID for this request.", "required": true, "pattern": "(?:(?:[-a-z0-9]{1,63}\\.)*(?:[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?):)?(?:[0-9]{1,19}|(?:[a-z0-9](?:[-a-z0-9]{0,61}[a-z0-9])?))", "location": "path"}}, "parameterOrder": ["project"], "response": {"$ref": "AddressAggregatedList"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/compute", "https://www.googleapis.com/auth/compute.readonly"]}, "delete": {"id": "compute.addresses.delete", "path": "{project}/regions/{region}/addresses/{address}", "httpMethod": "DELETE", "description": "Deletes the specified address resource.", "parameters": {"address": {"type": "string", "description": "Name of the address resource to delete.", "required": true, "pattern": "[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?|[1-9][0-9]{0,19}", "location": "path"}, "project": {"type": "string", "description": "Project ID for this request.", "required": true, "pattern": "(?:(?:[-a-z0-9]{1,63}\\.)*(?:[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?):)?(?:[0-9]{1,19}|(?:[a-z0-9](?:[-a-z0-9]{0,61}[a-z0-9])?))", "location": "path"}, "region": {"type": "string", "description": "Name of the region for this request.", "required": true, "pattern": "[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?", "location": "path"}, "requestId": {"type": "string", "description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed.\n\nFor example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments.\n\nThe request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query"}}, "parameterOrder": ["project", "region", "address"], "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/compute"]}, "insert": {"id": "compute.addresses.insert", "path": "{project}/regions/{region}/addresses", "httpMethod": "POST", "description": "Creates an address resource in the specified project by using the data included in the request.", "parameters": {"project": {"type": "string", "description": "Project ID for this request.", "required": true, "pattern": "(?:(?:[-a-z0-9]{1,63}\\.)*(?:[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?):)?(?:[0-9]{1,19}|(?:[a-z0-9](?:[-a-z0-9]{0,61}[a-z0-9])?))", "location": "path"}, "region": {"type": "string", "description": "Name of the region for this request.", "required": true, "pattern": "[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?", "location": "path"}, "requestId": {"type": "string", "description": "An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed.\n\nFor example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments.\n\nThe request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query"}}, "parameterOrder": ["project", "region"], "request": {"$ref": "Address"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/compute"]}, "list": {"id": "compute.addresses.list", "path": "{project}/regions/{region}/addresses", "httpMethod": "GET", "description": "Retrieves a list of addresses contained within the specified region.", "parameters": {"filter": {"type": "string", "description": "A filter expression that filters resources listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be either =, !=, >, or <.\n\nFor example, if you are filtering Compute Engine instances, you can exclude instances named example-instance by specifying name != example-instance.\n\nYou can also filter nested fields. For example, you could specify scheduling.automaticRestart = false to include instances only if they are not scheduled for automatic restarts. You can use filtering on nested fields to filter based on resource labels.\n\nTo filter on multiple expressions, provide each separate expression within parentheses. For example, (scheduling.automaticRestart = true) (cpuPlatform = \"Intel Skylake\"). By default, each expression is an AND expression. However, you can include AND and OR expressions explicitly. For example, (cpuPlatform = \"Intel Skylake\") OR (cpuPlatform = \"Intel Broadwell\") AND (scheduling.automaticRestart = true).", "location": "query"}, "maxResults": {"type": "integer", "description": "The maximum number of results per page that should be returned. If the number of available results is larger than maxResults, Compute Engine returns a nextPageToken that can be used to get the next page of results in subsequent list requests. Acceptable values are 0 to 500, inclusive. (Default: 500)", "default": "500", "format": "uint32", "minimum": "0", "location": "query"}, "orderBy": {"type": "string", "description": "Sorts list results by a certain order. By default, results are returned in alphanumerical order based on the resource name.\n\nYou can also sort results in descending order based on the creation timestamp using orderBy=\"creationTimestamp desc\". This sorts results based on the creationTimestamp field in reverse chronological order (newest result first). Use this to sort resources like operations so that the newest operation is returned first.\n\nCurrently, only sorting by name or creationTimestamp desc is supported.", "location": "query", "required": true}, "pageToken": {"type": "string", "description": "Specifies a page token to use. Set pageToken to the nextPageToken returned by a previous list request to get the next page of results.", "location": "query"}, "project": {"type": "string", "description": "Project ID for this request.", "required": true, "pattern": "(?:(?:[-a-z0-9]{1,63}\\.)*(?:[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?):)?(?:[0-9]{1,19}|(?:[a-z0-9](?:[-a-z0-9]{0,61}[a-z0-9])?))", "location": "path"}, "region": {"type": "string", "description": "Name of the region for this request.", "required": true, "pattern": "[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?", "location": "path"}}, "parameterOrder": ["project", "region", "orderBy"], "response": {"$ref": "AddressList"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/compute", "https://www.googleapis.com/auth/compute.readonly"]}}}, "regionOperations": {"methods": {"get": {"id": "compute.regionOperations.get", "path": "{project}/regions/{region}/operations/{operation}", "httpMethod": "GET", "description": "Retrieves the specified region-specific Operations resource.", "parameters": {"operation": {"type": "string", "description": "Name of the Operations resource to return.", "required": true, "pattern": "[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?|[1-9][0-9]{0,19}", "location": "path"}, "project": {"type": "string", "description": "Project ID for this request.", "required": true, "pattern": "(?:(?:[-a-z0-9]{1,63}\\.)*(?:[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?):)?(?:[0-9]{1,19}|(?:[a-z0-9](?:[-a-z0-9]{0,61}[a-z0-9])?))", "location": "path"}, "region": {"type": "string", "description": "Name of the region for this request.", "required": true, "pattern": "[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?", "location": "path"}}, "parameterOrder": ["project", "region", "operation"], "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/compute", "https://www.googleapis.com/auth/compute.readonly"]}, "wait": {"id": "compute.regionOperations.wait", "path": "projects/{project}/regions/{region}/operations/{operation}/wait", "httpMethod": "POST", "description": "Waits for the specified Operation resource to return as `DON<PERSON>` or for the request to approach the 2 minute deadline, and retrieves the specified Operation resource. This method differs from the `GET` method in that it waits for no more than the default deadline (2 minutes) and then returns the current state of the operation, which might be `DONE` or still in progress.\n\nThis method is called on a best-effort basis. Specifically:  \n- In uncommon cases, when the server is overloaded, the request might return before the default deadline is reached, or might return after zero seconds. \n- If the default deadline is reached, there is no guarantee that the operation is actually done when the method returns. Be prepared to retry if the operation is not `DONE`.", "parameters": {"operation": {"type": "string", "description": "Name of the Operations resource to return.", "required": true, "pattern": "[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?|[1-9][0-9]{0,19}", "location": "path"}, "project": {"type": "string", "description": "Project ID for this request.", "required": true, "pattern": "(?:(?:[-a-z0-9]{1,63}\\.)*(?:[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?):)?(?:[0-9]{1,19}|(?:[a-z0-9](?:[-a-z0-9]{0,61}[a-z0-9])?))", "location": "path"}, "region": {"type": "string", "description": "Name of the region for this request.", "required": true, "pattern": "[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?", "location": "path"}}, "parameterOrder": ["project", "region", "operation"], "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/compute", "https://www.googleapis.com/auth/compute.readonly"]}}}}}