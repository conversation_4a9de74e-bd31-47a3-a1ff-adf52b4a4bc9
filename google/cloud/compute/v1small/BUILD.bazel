# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

exports_files(glob(include = ["*grpc_service_config.json"]))

##############################################################################
# Discovery
##############################################################################
load(
    "@com_google_disco_to_proto3_converter//rules_gapic:disco_to_proto.bzl",
    "gapic_yaml_from_disco",
    "grpc_service_config_from_disco",
    "proto_from_disco",
)

proto_from_disco(
    name = "compute_small_gen",
    src = "compute.v1small.json",
    enums_as_strings = True,
)

grpc_service_config_from_disco(
    name = "compute_small_grpc_service_config_gen",
    src = "compute.v1small.json",
)

gapic_yaml_from_disco(
    name = "compute_small_gapic_gen",
    src = "compute.v1small.json",
)

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "compute_small_proto",
    srcs = [
        "compute_small.proto",
    ],
    deps = [
        "@com_google_googleapis//google/api:annotations_proto",
        "@com_google_googleapis//google/api:client_proto",
        "@com_google_googleapis//google/api:field_behavior_proto",
        "@com_google_googleapis//google/api:resource_proto",
        "@com_google_googleapis//google/cloud:extended_operations_proto",
        "@com_google_googleapis//google/longrunning:operations_proto",
        "@com_google_protobuf//:descriptor_proto",
    ],
)

proto_library_with_info(
    name = "compute_small_proto_with_info",
    deps = [
        ":compute_small_proto",
        "@com_google_googleapis//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

# Java Compute Small (for testing and prototyping purposes)
java_proto_library(
    name = "compute_small_java_proto",
    deps = [":compute_small_proto"],
)

# Used for integration tests
java_gapic_library(
    name = "compute_small_java_gapic",
    srcs = [":compute_small_proto_with_info"],
    gapic_yaml = "compute_small_gapic.yaml",
    grpc_service_config = ":compute_small_grpc_service_config.json",
    test_deps = [],
    transport = "rest",
    deps = [
        ":compute_small_java_proto",
    ],
)

java_gapic_test(
    name = "compute_small_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.compute.v1small.AddressesClientTest",
    ],
    runtime_deps = [":compute_small_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-compute-small-v1-java",
    include_samples = True,
    transport = "rest",
    deps = [
        ":compute_small_java_gapic",
        ":compute_small_java_proto",
        ":compute_small_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
)

# Python Compute Small (for testing and prototyping purposes)
py_gapic_library(
    name = "compute_small_py_gapic",
    srcs = [
        ":compute_small_proto",
    ],
    opt_args = [
        "transport=rest",
    ],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "compute-small-v1-py",
    deps = [
        ":compute_small_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# Put your PHP rules here

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

# Node.js Compute Small (for testing and prototyping purposes)
nodejs_gapic_library(
    name = "compute_small_nodejs_gapic",
    package_name = "@google-cloud/compute-small",
    src = ":compute_small_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    diregapic = True,
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "compute-small-v1-nodejs",
    deps = [
        ":compute_small_nodejs_gapic",
        ":compute_small_proto",
        "@com_google_googleapis//google/cloud:extended_operations_proto",
    ],
)

###############################################################################
# Ruby
###############################################################################
# Put your Ruby rules here

##############################################################################
# C#
##############################################################################
# Put your C# rules here
