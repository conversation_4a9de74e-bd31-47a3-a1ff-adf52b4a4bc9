{"methodConfig": [{"name": [{"service": "google.cloud.compute.v1small.Addresses", "method": "AggregatedList"}, {"service": "google.cloud.compute.v1small.Addresses", "method": "List"}, {"service": "google.cloud.compute.v1small.RegionOperations", "method": "Get"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.compute.v1small.Addresses", "method": "Delete"}, {"service": "google.cloud.compute.v1small.Addresses", "method": "Insert"}, {"service": "google.cloud.compute.v1small.RegionOperations", "method": "Wait"}], "timeout": "600s"}]}