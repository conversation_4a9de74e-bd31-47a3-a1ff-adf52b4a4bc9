# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "enterpriseknowledgegraph_proto",
    srcs = [
        "job_state.proto",
        "operation_metadata.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "enterpriseknowledgegraph_proto_with_info",
    deps = [
        ":enterpriseknowledgegraph_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "enterpriseknowledgegraph_java_proto",
    deps = [
        ":enterpriseknowledgegraph_proto",
    ],
)

java_grpc_library(
    name = "enterpriseknowledgegraph_java_grpc",
    srcs = [":enterpriseknowledgegraph_proto"],
    deps = [":enterpriseknowledgegraph_java_proto"],
)

java_gapic_library(
    name = "enterpriseknowledgegraph_java_gapic",
    srcs = [":enterpriseknowledgegraph_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "enterpriseknowledgegraph_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "enterpriseknowledgegraph_v1.yaml",
    test_deps = [
        ":enterpriseknowledgegraph_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":enterpriseknowledgegraph_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "enterpriseknowledgegraph_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphServiceClientHttpJsonTest",
        "com.google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphServiceClientTest",
    ],
    runtime_deps = [":enterpriseknowledgegraph_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-enterpriseknowledgegraph-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":enterpriseknowledgegraph_java_gapic",
        ":enterpriseknowledgegraph_java_grpc",
        ":enterpriseknowledgegraph_java_proto",
        ":enterpriseknowledgegraph_proto",
    ],
)

py_gapic_library(
    name = "enterpriseknowledgegraph_py_gapic",
    srcs = [
        ":enterpriseknowledgegraph_proto",
    ],
    grpc_service_config = "enterpriseknowledgegraph_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "enterpriseknowledgegraph_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "enterpriseknowledgegraph_py_gapic_test",
    srcs = [
        "enterpriseknowledgegraph_py_gapic_pytest.py",
        "enterpriseknowledgegraph_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [
        ":enterpriseknowledgegraph_py_gapic",
    ],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "enterpriseknowledgegraph-v1-py",
    deps = [
        ":enterpriseknowledgegraph_py_gapic",
    ],
)

cc_proto_library(
    name = "enterpriseknowledgegraph_cc_proto",
    deps = [":enterpriseknowledgegraph_proto"],
)

cc_grpc_library(
    name = "enterpriseknowledgegraph_cc_grpc",
    srcs = [":enterpriseknowledgegraph_proto"],
    grpc_only = True,
    deps = [":enterpriseknowledgegraph_cc_proto"],
)

##############################################################################
# PHP
##############################################################################

php_proto_library(
    name = "enterpriseknowledgegraph_php_proto",
    deps = [":enterpriseknowledgegraph_proto"],
)

php_gapic_library(
    name = "enterpriseknowledgegraph_php_gapic",
    srcs = [":enterpriseknowledgegraph_proto_with_info"],
    grpc_service_config = "enterpriseknowledgegraph_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "enterpriseknowledgegraph_v1.yaml",
    transport = "grpc+rest",
    deps = [":enterpriseknowledgegraph_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-enterpriseknowledgegraph-v1-php",
    deps = [
        ":enterpriseknowledgegraph_php_gapic",
        ":enterpriseknowledgegraph_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
ruby_proto_library(
    name = "enterpriseknowledgegraph_ruby_proto",
    deps = [":enterpriseknowledgegraph_proto"],
)

ruby_grpc_library(
    name = "enterpriseknowledgegraph_ruby_grpc",
    srcs = [":enterpriseknowledgegraph_proto"],
    deps = [":enterpriseknowledgegraph_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "enterpriseknowledgegraph_ruby_gapic",
    srcs = [":enterpriseknowledgegraph_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-enterpriseknowledgegraph-v1",
    ],
    grpc_service_config = "enterpriseknowledgegraph_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "enterpriseknowledgegraph_v1.yaml",
    deps = [
        ":enterpriseknowledgegraph_ruby_grpc",
        ":enterpriseknowledgegraph_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-enterpriseknowledgegraph-v1-ruby",
    deps = [
        ":enterpriseknowledgegraph_ruby_gapic",
        ":enterpriseknowledgegraph_ruby_grpc",
        ":enterpriseknowledgegraph_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
csharp_proto_library(
    name = "enterpriseknowledgegraph_csharp_proto",
    deps = [":enterpriseknowledgegraph_proto"],
)

csharp_grpc_library(
    name = "enterpriseknowledgegraph_csharp_grpc",
    srcs = [":enterpriseknowledgegraph_proto"],
    deps = [":enterpriseknowledgegraph_csharp_proto"],
)

csharp_gapic_library(
    name = "enterpriseknowledgegraph_csharp_gapic",
    srcs = [":enterpriseknowledgegraph_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "enterpriseknowledgegraph_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "enterpriseknowledgegraph_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":enterpriseknowledgegraph_csharp_grpc",
        ":enterpriseknowledgegraph_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-enterpriseknowledgegraph-v1-csharp",
    deps = [
        ":enterpriseknowledgegraph_csharp_gapic",
        ":enterpriseknowledgegraph_csharp_grpc",
        ":enterpriseknowledgegraph_csharp_proto",
    ],
)
