// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.enterpriseknowledgegraph.v1;

option go_package = "cloud.google.com/go/enterpriseknowledgegraph/apiv1/enterpriseknowledgegraphpb;enterpriseknowledgegraphpb";
option java_multiple_files = true;
option java_outer_classname = "JobStateProto";
option java_package = "com.google.cloud.enterpriseknowledgegraph.v1";
option php_namespace = "Google\\Cloud\\EnterpriseKnowledgeGraph\\V1";
option ruby_package = "Google::Cloud::EnterpriseKnowledgeGraph::V1";
option csharp_namespace = "Google.Cloud.EnterpriseKnowledgeGraph.V1";

// Describes the state of a job.
enum JobState {
  // The job state is unspecified.
  JOB_STATE_UNSPECIFIED = 0;

  // The service is preparing to run the job.
  JOB_STATE_PENDING = 9;

  // The job is in progress.
  JOB_STATE_RUNNING = 1;

  // The job completed successfully.
  JOB_STATE_SUCCEEDED = 2;

  // The job failed.
  JOB_STATE_FAILED = 3;

  // The job has been cancelled.
  JOB_STATE_CANCELLED = 4;

  // Entity Recon API: The knowledge extraction job is running.
  JOB_STATE_KNOWLEDGE_EXTRACTION = 5;

  // Entity Recon API: The preprocessing job is running.
  JOB_STATE_RECON_PREPROCESSING = 6;

  // Entity Recon API: The clustering job is running.
  JOB_STATE_CLUSTERING = 7;

  // Entity Recon API: The exporting clusters job is running.
  JOB_STATE_EXPORTING_CLUSTERS = 8;
}
