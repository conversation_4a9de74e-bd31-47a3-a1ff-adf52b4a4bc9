// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.enterpriseknowledgegraph.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/enterpriseknowledgegraph/v1/job_state.proto";
import "google/cloud/enterpriseknowledgegraph/v1/operation_metadata.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.EnterpriseKnowledgeGraph.V1";
option go_package = "cloud.google.com/go/enterpriseknowledgegraph/apiv1/enterpriseknowledgegraphpb;enterpriseknowledgegraphpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.enterpriseknowledgegraph.v1";
option php_namespace = "Google\\Cloud\\EnterpriseKnowledgeGraph\\V1";
option ruby_package = "Google::Cloud::EnterpriseKnowledgeGraph::V1";
option (google.api.resource_definition) = {
  type: "bigquery.googleapis.com/Dataset"
  pattern: "projects/{project}/datasets/{dataset}"
};
option (google.api.resource_definition) = {
  type: "bigquery.googleapis.com/Table"
  pattern: "projects/{project}/datasets/{dataset}/tables/{table}"
};
option (google.api.resource_definition) = {
  type: "enterpriseknowledgegraph.googleapis.com/CloudKnowledgeGraphEntity"
  pattern: "projects/{project}/locations/{location}/cloudKnowledgeGraphEntities/{cloud_knowledge_graph_entity}"
};
option (google.api.resource_definition) = {
  type: "enterpriseknowledgegraph.googleapis.com/PublicKnowledgeGraphEntity"
  pattern: "projects/{project}/locations/{location}/publicKnowledgeGraphEntities/{public_knowledge_graph_entity}"
};

// APIs for enterprise knowledge graph product.
service EnterpriseKnowledgeGraphService {
  option (google.api.default_host) = "enterpriseknowledgegraph.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a EntityReconciliationJob. A EntityReconciliationJob once created
  // will right away be attempted to start.
  rpc CreateEntityReconciliationJob(CreateEntityReconciliationJobRequest)
      returns (EntityReconciliationJob) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/entityReconciliationJobs"
      body: "entity_reconciliation_job"
    };
    option (google.api.method_signature) = "parent,entity_reconciliation_job";
  }

  // Gets a EntityReconciliationJob.
  rpc GetEntityReconciliationJob(GetEntityReconciliationJobRequest)
      returns (EntityReconciliationJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/entityReconciliationJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Entity Reconciliation Jobs.
  rpc ListEntityReconciliationJobs(ListEntityReconciliationJobsRequest)
      returns (ListEntityReconciliationJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/entityReconciliationJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Cancels a EntityReconciliationJob. Success of cancellation is not
  // guaranteed.
  rpc CancelEntityReconciliationJob(CancelEntityReconciliationJobRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/entityReconciliationJobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a EntityReconciliationJob.
  // It only deletes the job when the job state is in FAILED, SUCCEEDED, and
  // CANCELLED.
  rpc DeleteEntityReconciliationJob(DeleteEntityReconciliationJobRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/entityReconciliationJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Finds the Cloud KG entities with CKG ID(s).
  rpc Lookup(LookupRequest) returns (LookupResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/cloudKnowledgeGraphEntities:Lookup"
    };
    option (google.api.method_signature) = "parent,ids";
  }

  // Searches the Cloud KG entities with entity name.
  rpc Search(SearchRequest) returns (SearchResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/cloudKnowledgeGraphEntities:Search"
    };
    option (google.api.method_signature) = "parent,query";
  }

  // Finds the public KG entities with public KG ID(s).
  rpc LookupPublicKg(LookupPublicKgRequest) returns (LookupPublicKgResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/publicKnowledgeGraphEntities:Lookup"
    };
    option (google.api.method_signature) = "parent,ids";
  }

  // Searches the public KG entities with entity name.
  rpc SearchPublicKg(SearchPublicKgRequest) returns (SearchPublicKgResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/publicKnowledgeGraphEntities:Search"
    };
    option (google.api.method_signature) = "parent,query";
  }
}

// The desired input location and metadata.
message InputConfig {
  // The type of entities we will support. Currently, we only support people,
  // establishment, property, and product types. If the type is
  // unspecified, it will be generic type.
  enum EntityType {
    // The default value.
    ENTITY_TYPE_UNSPECIFIED = 0;

    // People entity.
    PEOPLE = 1 [deprecated = true];

    // Establishment entity.
    ESTABLISHMENT = 2 [deprecated = true];

    // Property entity. e.g. real estate property.
    PROPERTY = 3 [deprecated = true];

    // Product entity.
    PRODUCT = 4;

    // Organization entity.
    ORGANIZATION = 5;

    // Local Business entity.
    LOCAL_BUSINESS = 6;

    // Person entity.
    PERSON = 7;
  }

  // Set of input BigQuery tables.
  repeated BigQueryInputConfig bigquery_input_configs = 1;

  // Entity type
  EntityType entity_type = 2;

  // Optional. Provide the bigquery table containing the previous results if
  // cluster ID stability is desired. Format is
  // `projects/*/datasets/*/tables/*`.
  string previous_result_bigquery_table = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "bigquery.googleapis.com/Table" }
  ];
}

// The input config for BigQuery tables.
message BigQueryInputConfig {
  // Required. Format is `projects/*/datasets/*/tables/*`.
  string bigquery_table = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "bigquery.googleapis.com/Table" }
  ];

  // Required. Schema mapping file
  string gcs_uri = 2 [(google.api.field_behavior) = REQUIRED];
}

// The desired output location and metadata.
message OutputConfig {
  // Format is “projects/*/datasets/*”.
  string bigquery_dataset = 1 [(google.api.resource_reference) = {
    type: "bigquery.googleapis.com/Dataset"
  }];
}

// Recon configs
message ReconConfig {
  // Options for experimental changes on entity clustering behavior.
  message Options {
    // If true, separate clusters by their geographic region (from geocoding).
    // Uses the following entity features:
    //
    // - schema.org/addressLocality
    // - schema.org/addressRegion
    // - schema.org/addressCountry
    // Warning: processing will no longer be regionalized!
    bool enable_geocoding_separation = 100;
  }

  // Model Configs
  message ModelConfig {
    // Model name. Refer to external documentation for valid names.
    // If unspecified, it defaults to the one mentioned in the documentation.
    string model_name = 1;

    // Model version tag. Refer to external documentation for valid tags.
    // If unspecified, it defaults to the one mentioned in the documentation.
    string version_tag = 2;
  }

  // Choice of clustering algorithm. Default is ConnectedComponentsConfig.
  oneof clustering_config {
    // Configs for connected components.
    ConnectedComponentsConfig connected_components_config = 1;

    // Configs for affinity clustering.
    AffinityClusteringConfig affinity_clustering_config = 2;
  }

  // Extra options that affect entity clustering behavior.
  Options options = 3;

  // Model Configs
  ModelConfig model_config = 4;
}

// Options for connected components.
message ConnectedComponentsConfig {
  // Threshold used for connected components. Default value is 0.85.
  float weight_threshold = 1;
}

// Options for affinity clustering.
message AffinityClusteringConfig {
  // Number of iterations to perform. Default value is 1.
  int64 compression_round_count = 1;
}

// Details of operations that perform deletes of any entities.
message DeleteOperationMetadata {
  // The common part of the operation metadata.
  CommonOperationMetadata common_metadata = 1;
}

// Request message for CreateEntityReconciliationJob.
message CreateEntityReconciliationJobRequest {
  // Required. The resource name of the Location to create the
  // EntityReconciliationJob in. Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "enterpriseknowledgegraph.googleapis.com/EntityReconciliationJob"
    }
  ];

  // Required. The EntityReconciliationJob to create.
  EntityReconciliationJob entity_reconciliation_job = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for GetEntityReconciliationJob.
message GetEntityReconciliationJobRequest {
  // Required. The name of the EntityReconciliationJob resource.
  // Format:
  // `projects/{project}/locations/{location}/entityReconciliationJobs/{entity_reconciliation_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "enterpriseknowledgegraph.googleapis.com/EntityReconciliationJob"
    }
  ];
}

// Request message for
// [EnterpriseKnowledgeGraphService.ListEntityReconciliationJobs][google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService.ListEntityReconciliationJobs].
message ListEntityReconciliationJobsRequest {
  // Required. The name of the EntityReconciliationJob's parent resource.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "enterpriseknowledgegraph.googleapis.com/EntityReconciliationJob"
    }
  ];

  // An expression for filtering the results of the request. For field names
  // both snake_case and camelCase are supported.
  string filter = 2;

  // The standard list page size.
  int32 page_size = 3;

  // The standard list page token.
  string page_token = 4;
}

// Response message for
// [EnterpriseKnowledgeGraphService.ListEntityReconciliationJobs][google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService.ListEntityReconciliationJobs].
message ListEntityReconciliationJobsResponse {
  // A list of EntityReconciliationJobs that matches the specified filter in the
  // request.
  repeated EntityReconciliationJob entity_reconciliation_jobs = 1;

  // The standard List next-page token.
  string next_page_token = 2;
}

// Request message for CancelEntityReconciliationJob.
message CancelEntityReconciliationJobRequest {
  // Required. The name of the EntityReconciliationJob resource.
  // Format:
  // `projects/{project}/locations/{location}/entityReconciliationJobs/{entity_reconciliation_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "enterpriseknowledgegraph.googleapis.com/EntityReconciliationJob"
    }
  ];
}

// Request message for DeleteEntityReconciliationJob.
message DeleteEntityReconciliationJobRequest {
  // Required. The name of the EntityReconciliationJob resource.
  // Format:
  // `projects/{project}/locations/{location}/entityReconciliationJobs/{entity_reconciliation_job}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "enterpriseknowledgegraph.googleapis.com/EntityReconciliationJob"
    }
  ];
}

// Entity reconciliation job message.
message EntityReconciliationJob {
  option (google.api.resource) = {
    type: "enterpriseknowledgegraph.googleapis.com/EntityReconciliationJob"
    pattern: "projects/{project}/locations/{location}/entityReconciliationJobs/{entity_reconciliation_job}"
  };

  // Output only. Resource name of the EntityReconciliationJob.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Information about the input BigQuery tables.
  InputConfig input_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The desired output location.
  OutputConfig output_config = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. The detailed state of the job.
  JobState state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Only populated when the job's state is JOB_STATE_FAILED or
  // JOB_STATE_CANCELLED.
  google.rpc.Status error = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the EntityReconciliationJob was created.
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the EntityReconciliationJob entered any of the
  // following states: `JOB_STATE_SUCCEEDED`, `JOB_STATE_FAILED`,
  // `JOB_STATE_CANCELLED`.
  google.protobuf.Timestamp end_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the EntityReconciliationJob was most recently
  // updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Recon configs to adjust the clustering behavior.
  ReconConfig recon_config = 9 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [EnterpriseKnowledgeGraphService.Lookup][google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService.Lookup].
message LookupRequest {
  // Required. The name of the Entity's parent resource.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "enterpriseknowledgegraph.googleapis.com/CloudKnowledgeGraphEntity"
    }
  ];

  // Required. The list of entity ids to be used for lookup.
  repeated string ids = 2 [(google.api.field_behavior) = REQUIRED];

  // The list of language codes (defined in ISO 693) to run the query with,
  // e.g. 'en'.
  repeated string languages = 3;
}

// Response message for
// [EnterpriseKnowledgeGraphService.Lookup][google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService.Lookup].
message LookupResponse {
  // The local context applicable for the response. See more details at
  // http://www.w3.org/TR/json-ld/#context-definitions.
  google.protobuf.Value context = 1;

  // The schema type of top-level JSON-LD object, e.g. ItemList.
  google.protobuf.Value type = 2;

  // The item list of search results.
  google.protobuf.ListValue item_list_element = 3;
}

// Request message for
// [EnterpriseKnowledgeGraphService.Search][google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService.Search].
message SearchRequest {
  // Required. The name of the Entity's parent resource.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "enterpriseknowledgegraph.googleapis.com/CloudKnowledgeGraphEntity"
    }
  ];

  // Required. The literal query string for search.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // The list of language codes (defined in ISO 693) to run the query with,
  // e.g. 'en'.
  repeated string languages = 3;

  // Restricts returned entities with these types, e.g. Person
  // (as defined in http://schema.org/Person). If multiple types are specified,
  // returned entities will contain one or more of these types.
  repeated string types = 4;

  // Limits the number of entities to be returned.
  google.protobuf.Int32Value limit = 6;
}

// Response message for
// [EnterpriseKnowledgeGraphService.Search][google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService.Search].
message SearchResponse {
  // The local context applicable for the response. See more details at
  // http://www.w3.org/TR/json-ld/#context-definitions.
  google.protobuf.Value context = 1;

  // The schema type of top-level JSON-LD object, e.g. ItemList.
  google.protobuf.Value type = 2;

  // The item list of search results.
  google.protobuf.ListValue item_list_element = 3;
}

// Request message for
// [EnterpriseKnowledgeGraphService.LookupPublicKg][google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService.LookupPublicKg].
message LookupPublicKgRequest {
  // Required. The name of the Entity's parent resource.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "enterpriseknowledgegraph.googleapis.com/PublicKnowledgeGraphEntity"
    }
  ];

  // Required. The list of entity ids to be used for lookup.
  repeated string ids = 2 [(google.api.field_behavior) = REQUIRED];

  // The list of language codes (defined in ISO 693) to run the query with,
  // e.g. 'en'.
  repeated string languages = 3;
}

// Response message for
// [EnterpriseKnowledgeGraphService.LookupPublicKg][google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService.LookupPublicKg].
message LookupPublicKgResponse {
  // The local context applicable for the response. See more details at
  // http://www.w3.org/TR/json-ld/#context-definitions.
  google.protobuf.Value context = 1;

  // The schema type of top-level JSON-LD object, e.g. ItemList.
  google.protobuf.Value type = 2;

  // The item list of search results.
  google.protobuf.ListValue item_list_element = 3;
}

// Request message for
// [EnterpriseKnowledgeGraphService.Search][google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService.Search].
message SearchPublicKgRequest {
  // Required. The name of the Entity's parent resource.
  // Format:
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "enterpriseknowledgegraph.googleapis.com/PublicKnowledgeGraphEntity"
    }
  ];

  // Required. The literal query string for search.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // The list of language codes (defined in ISO 693) to run the query with,
  // e.g. 'en'.
  repeated string languages = 3;

  // Restricts returned entities with these types, e.g. Person
  // (as defined in http://schema.org/Person). If multiple types are specified,
  // returned entities will contain one or more of these types.
  repeated string types = 4;

  // Limits the number of entities to be returned.
  google.protobuf.Int32Value limit = 6;
}

// Response message for
// [EnterpriseKnowledgeGraphService.Search][google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService.Search].
message SearchPublicKgResponse {
  // The local context applicable for the response. See more details at
  // http://www.w3.org/TR/json-ld/#context-definitions.
  google.protobuf.Value context = 1;

  // The schema type of top-level JSON-LD object, e.g. ItemList.
  google.protobuf.Value type = 2;

  // The item list of search results.
  google.protobuf.ListValue item_list_element = 3;
}
