{"methodConfig": [{"name": [{"service": "google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService", "method": "ListEntityReconciliationJobs"}, {"service": "google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService", "method": "GetEntityReconciliationJob"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService", "method": "CreateEntityReconciliationJob"}, {"service": "google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService", "method": "DeleteEntityReconciliationJob"}, {"service": "google.cloud.enterpriseknowledgegraph.v1.EnterpriseKnowledgeGraphService", "method": "CancelEntityReconciliationJob"}], "timeout": "60s"}]}