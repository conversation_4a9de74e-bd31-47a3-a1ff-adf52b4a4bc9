// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// This file contains stub messages for common resources in GCP.
// It is not intended to be directly generated, and is instead used by
// other tooling to be able to match common resource patterns.
syntax = "proto3";

package google.cloud;

import "google/api/resource.proto";


option (google.api.resource_definition) = {
  type: "cloudresourcemanager.googleapis.com/Project"
  pattern: "projects/{project}"
};


option (google.api.resource_definition) = {
  type: "cloudresourcemanager.googleapis.com/Organization"
  pattern: "organizations/{organization}"
};


option (google.api.resource_definition) = {
  type: "cloudresourcemanager.googleapis.com/Folder"
  pattern: "folders/{folder}"
};


option (google.api.resource_definition) = {
  type: "cloudbilling.googleapis.com/BillingAccount"
  pattern: "billingAccounts/{billing_account}"
};

option (google.api.resource_definition) = {
  type: "locations.googleapis.com/Location"
  pattern: "projects/{project}/locations/{location}"
};

