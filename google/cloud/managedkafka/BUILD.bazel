# This build file includes a target for the Ruby wrapper library for
# google-cloud-managed_kafka.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for google-cloud-managed_kafka.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "managedkafka_ruby_wrapper",
    srcs = ["//google/cloud/managedkafka/v1:managedkafka_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-managed_kafka",
        "ruby-cloud-wrapper-of=v1:0.0",
    ],
    service_yaml = "//google/cloud/managedkafka/v1:managedkafka_v1.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-managedkafka-ruby",
    deps = [
        ":managedkafka_ruby_wrapper",
    ],
)
