{"methodConfig": [{"name": [{"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "ListClusters"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "GetCluster"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "ListTopics"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "GetTopic"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "ListConsumerGroups"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "GetConsumerGroup"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "ListConnectClusters"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "GetConnectCluster"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "ListConnectors"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "GetConnector"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "ListSchemaRegistries"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "GetSchemaRegistry"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "ListContexts"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "GetContext"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "GetSchema"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "GetRawSchema"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "ListSchemaVersions"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "ListSchemaTypes"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "ListSubjects"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "ListSubjectsBySchemaId"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "LookUpVersion"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "GetVersion"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "GetRawSchemaVersion"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "ListVersions"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "ListReferencedSchemas"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "CheckCompatibility"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "GetSchemaConfig"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "GetSchemaMode"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "CreateCluster"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "DeleteCluster"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "UpdateCluster"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "CreateTopic"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "DeleteTopic"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "UpdateTopic"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "CreateConsumerGroup"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "DeleteConsumerGroup"}, {"service": "google.cloud.managedkafka.v1.ManagedKafka", "method": "UpdateConsumerGroup"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "CreateConnectCluster"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "DeleteConnectCluster"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "UpdateConnectCluster"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "CreateConnector"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "DeleteConnector"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "UpdateConnector"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "PauseConnector"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "ResumeConnector"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "RestartConnector"}, {"service": "google.cloud.managedkafka.v1.ManagedKafkaConnect", "method": "StopConnector"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "CreateSchemaRegistry"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "DeleteSchemaRegistry"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "CreateContext"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "DeleteSubject"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "CreateVersion"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "DeleteVersion"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "UpdateSchemaConfig"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "DeleteSchemaConfig"}, {"service": "google.cloud.managedkafka.v1.ManagedSchemaRegistry", "method": "UpdateSchemaMode"}], "timeout": "60s"}]}