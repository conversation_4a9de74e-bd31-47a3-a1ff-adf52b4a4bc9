{"methodConfig": [{"name": [{"service": "google.cloud.metastore.v1beta.DataprocMetastore"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.metastore.v1beta.DataprocMetastore", "method": "CreateService"}, {"service": "google.cloud.metastore.v1beta.DataprocMetastore", "method": "UpdateService"}, {"service": "google.cloud.metastore.v1beta.DataprocMetastore", "method": "DeleteService"}, {"service": "google.cloud.metastore.v1beta.DataprocMetastore", "method": "CreateMetadataImport"}, {"service": "google.cloud.metastore.v1beta.DataprocMetastore", "method": "UpdateMetadataImport"}, {"service": "google.cloud.metastore.v1beta.DataprocMetastore", "method": "ExportMetadata"}, {"service": "google.cloud.metastore.v1beta.DataprocMetastore", "method": "RestoreService"}, {"service": "google.cloud.metastore.v1beta.DataprocMetastore", "method": "CreateBackup"}, {"service": "google.cloud.metastore.v1beta.DataprocMetastore", "method": "DeleteBackup"}], "timeout": "60s"}]}