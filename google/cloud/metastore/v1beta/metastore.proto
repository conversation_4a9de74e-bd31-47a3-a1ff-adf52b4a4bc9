// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.metastore.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/type/dayofweek.proto";

option go_package = "cloud.google.com/go/metastore/apiv1beta/metastorepb;metastorepb";
option java_multiple_files = true;
option java_outer_classname = "MetastoreProto";
option java_package = "com.google.cloud.metastore.v1beta";
option php_namespace = "Google\\Cloud\\Metastore\\V1beta";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Network"
  pattern: "projects/{project}/global/networks/{network}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Subnetwork"
  pattern: "projects/{project}/regions/{region}/subnetworks/{subnetwork}"
};
option (google.api.resource_definition) = {
  type: "dataplex.googleapis.com/Lake"
  pattern: "projects/{project}/locations/{location}/lakes/{lake}"
};

// Configures and manages metastore services.
// Metastore services are fully managed, highly available, autoscaled,
// autohealing, OSS-native deployments of technical metadata management
// software. Each metastore service exposes a network endpoint through which
// metadata queries are served. Metadata queries can originate from a variety
// of sources, including Apache Hive, Apache Presto, and Apache Spark.
//
// The Dataproc Metastore API defines the following resource model:
//
// * The service works with a collection of Google Cloud projects, named:
// `/projects/*`
// * Each project has a collection of available locations, named: `/locations/*`
//   (a location must refer to a Google Cloud `region`)
// * Each location has a collection of services, named: `/services/*`
// * Dataproc Metastore services are resources with names of the form:
//
//   `/projects/{project_number}/locations/{location_id}/services/{service_id}`.
service DataprocMetastore {
  option (google.api.default_host) = "metastore.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists services in a project and location.
  rpc ListServices(ListServicesRequest) returns (ListServicesResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*}/services"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a single service.
  rpc GetService(GetServiceRequest) returns (Service) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/services/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a metastore service in a project and location.
  rpc CreateService(CreateServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*}/services"
      body: "service"
    };
    option (google.api.method_signature) = "parent,service,service_id";
    option (google.longrunning.operation_info) = {
      response_type: "Service"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Updates the parameters of a single service.
  rpc UpdateService(UpdateServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{service.name=projects/*/locations/*/services/*}"
      body: "service"
    };
    option (google.api.method_signature) = "service,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Service"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Deletes a single service.
  rpc DeleteService(DeleteServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/services/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Lists imports in a service.
  rpc ListMetadataImports(ListMetadataImportsRequest)
      returns (ListMetadataImportsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/services/*}/metadataImports"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single import.
  rpc GetMetadataImport(GetMetadataImportRequest) returns (MetadataImport) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/services/*/metadataImports/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new MetadataImport in a given project and location.
  rpc CreateMetadataImport(CreateMetadataImportRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/services/*}/metadataImports"
      body: "metadata_import"
    };
    option (google.api.method_signature) =
        "parent,metadata_import,metadata_import_id";
    option (google.longrunning.operation_info) = {
      response_type: "MetadataImport"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Updates a single import.
  // Only the description field of MetadataImport is supported to be updated.
  rpc UpdateMetadataImport(UpdateMetadataImportRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{metadata_import.name=projects/*/locations/*/services/*/metadataImports/*}"
      body: "metadata_import"
    };
    option (google.api.method_signature) = "metadata_import,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "MetadataImport"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Exports metadata from a service.
  rpc ExportMetadata(ExportMetadataRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{service=projects/*/locations/*/services/*}:exportMetadata"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "MetadataExport"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Restores a service from a backup.
  rpc RestoreService(RestoreServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{service=projects/*/locations/*/services/*}:restore"
      body: "*"
    };
    option (google.api.method_signature) = "service,backup";
    option (google.longrunning.operation_info) = {
      response_type: "Restore"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Lists backups in a service.
  rpc ListBackups(ListBackupsRequest) returns (ListBackupsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/services/*}/backups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single backup.
  rpc GetBackup(GetBackupRequest) returns (Backup) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/services/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new backup in a given project and location.
  rpc CreateBackup(CreateBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/services/*}/backups"
      body: "backup"
    };
    option (google.api.method_signature) = "parent,backup,backup_id";
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Deletes a single backup.
  rpc DeleteBackup(DeleteBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/services/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Removes the attached IAM policies for a resource
  rpc RemoveIamPolicy(RemoveIamPolicyRequest)
      returns (RemoveIamPolicyResponse) {
    option (google.api.http) = {
      post: "/v1beta/{resource=projects/*/locations/*/services/*/**}:removeIamPolicy"
      body: "*"
    };
  }

  // Query DPMS metadata.
  rpc QueryMetadata(QueryMetadataRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{service=projects/*/locations/*/services/*}:queryMetadata"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "QueryMetadataResponse"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Move a table to another database.
  rpc MoveTableToDatabase(MoveTableToDatabaseRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{service=projects/*/locations/*/services/*}:moveTableToDatabase"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "MoveTableToDatabaseResponse"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }

  // Alter metadata resource location. The metadata resource can be a database,
  // table, or partition. This functionality only updates the parent directory
  // for the respective metadata resource and does not transfer any existing
  // data to the new location.
  rpc AlterMetadataResourceLocation(AlterMetadataResourceLocationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{service=projects/*/locations/*/services/*}:alterLocation"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "AlterMetadataResourceLocationResponse"
      metadata_type: "google.cloud.metastore.v1beta.OperationMetadata"
    };
  }
}

// A managed metastore service that serves metadata queries.
message Service {
  option (google.api.resource) = {
    type: "metastore.googleapis.com/Service"
    pattern: "projects/{project}/locations/{location}/services/{service}"
  };

  // The current state of the metastore service.
  enum State {
    // The state of the metastore service is unknown.
    STATE_UNSPECIFIED = 0;

    // The metastore service is in the process of being created.
    CREATING = 1;

    // The metastore service is running and ready to serve queries.
    ACTIVE = 2;

    // The metastore service is entering suspension. Its query-serving
    // availability may cease unexpectedly.
    SUSPENDING = 3;

    // The metastore service is suspended and unable to serve queries.
    SUSPENDED = 4;

    // The metastore service is being updated. It remains usable but cannot
    // accept additional update requests or be deleted at this time.
    UPDATING = 5;

    // The metastore service is undergoing deletion. It cannot be used.
    DELETING = 6;

    // The metastore service has encountered an error and cannot be used. The
    // metastore service should be deleted.
    ERROR = 7;
  }

  // Available service tiers.
  enum Tier {
    // The tier is not set.
    TIER_UNSPECIFIED = 0;

    // The developer tier provides limited scalability and no fault tolerance.
    // Good for low-cost proof-of-concept.
    DEVELOPER = 1;

    // The enterprise tier provides multi-zone high availability, and sufficient
    // scalability for enterprise-level Dataproc Metastore workloads.
    ENTERPRISE = 3;
  }

  // Release channels bundle features of varying levels of stability. Newer
  // features may be introduced initially into less stable release channels and
  // can be automatically promoted into more stable release channels.
  enum ReleaseChannel {
    // Release channel is not specified.
    RELEASE_CHANNEL_UNSPECIFIED = 0;

    // The `CANARY` release channel contains the newest features, which may be
    // unstable and subject to unresolved issues with no known workarounds.
    // Services using the `CANARY` release channel are not subject to any SLAs.
    CANARY = 1;

    // The `STABLE` release channel contains features that are considered stable
    // and have been validated for production use.
    STABLE = 2;
  }

  // The backend database type for the metastore service.
  enum DatabaseType {
    // The DATABASE_TYPE is not set.
    DATABASE_TYPE_UNSPECIFIED = 0;

    // MySQL is used to persist the metastore data.
    MYSQL = 1;

    // Spanner is used to persist the metastore data.
    SPANNER = 2;
  }

  // Configuration properties specific to the underlying metastore service
  // technology (the software that serves metastore queries).
  oneof metastore_config {
    // Configuration information specific to running Hive metastore
    // software as the metastore service.
    HiveMetastoreConfig hive_metastore_config = 5;
  }

  // Immutable. The relative resource name of the metastore service, in the
  // following format:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The time when the metastore service was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the metastore service was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-defined labels for the metastore service.
  map<string, string> labels = 4;

  // Immutable. The relative resource name of the VPC network on which the
  // instance can be accessed. It is specified in the following form:
  //
  // `projects/{project_number}/global/networks/{network_id}`.
  string network = 7 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Output only. The URI of the endpoint used to access the metastore service.
  string endpoint_uri = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The TCP port at which the metastore service is reached. Default: 9083.
  int32 port = 9;

  // Output only. The current state of the metastore service.
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the current state of the
  // metastore service, if available.
  string state_message = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A Cloud Storage URI (starting with `gs://`) that specifies
  // where artifacts related to the metastore service are stored.
  string artifact_gcs_uri = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The tier of the service.
  Tier tier = 13;

  // The setting that defines how metastore metadata should be integrated with
  // external services and systems.
  MetadataIntegration metadata_integration = 14;

  // The one hour maintenance window of the metastore service. This specifies
  // when the service can be restarted for maintenance purposes in UTC time.
  // Maintenance window is not needed for services with the SPANNER
  // database type.
  MaintenanceWindow maintenance_window = 15;

  // Output only. The globally unique resource identifier of the metastore
  // service.
  string uid = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The metadata management activities of the metastore service.
  MetadataManagementActivity metadata_management_activity = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The release channel of the service.
  // If unspecified, defaults to `STABLE`.
  ReleaseChannel release_channel = 19 [(google.api.field_behavior) = IMMUTABLE];

  // Immutable. Information used to configure the Dataproc Metastore service to
  // encrypt customer data at rest. Cannot be updated.
  EncryptionConfig encryption_config = 20
      [(google.api.field_behavior) = IMMUTABLE];

  // The configuration specifying the network settings for the
  // Dataproc Metastore service.
  NetworkConfig network_config = 21;

  // Immutable. The database type that the Metastore service stores its data.
  DatabaseType database_type = 22 [(google.api.field_behavior) = IMMUTABLE];

  // The configuration specifying telemetry settings for the Dataproc Metastore
  // service. If unspecified defaults to `JSON`.
  TelemetryConfig telemetry_config = 23;

  // Scaling configuration of the metastore service.
  ScalingConfig scaling_config = 24;
}

// Specifies how metastore metadata should be integrated with external services.
message MetadataIntegration {
  // The integration config for the Data Catalog service.
  DataCatalogConfig data_catalog_config = 1;

  // The integration config for the Dataplex service.
  DataplexConfig dataplex_config = 2;
}

// Specifies how metastore metadata should be integrated with the Data Catalog
// service.
message DataCatalogConfig {
  // Defines whether the metastore metadata should be synced to Data Catalog.
  // The default value is to disable syncing metastore metadata to Data Catalog.
  bool enabled = 2;
}

// Specifies how metastore metadata should be integrated with the Dataplex
// service.
message DataplexConfig {
  // A reference to the Lake resources that this metastore service is attached
  // to. The key is the lake resource name. Example:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}`.
  map<string, Lake> lake_resources = 1;
}

// Represents a Lake resource
message Lake {
  // The Lake resource name.
  // Example:
  // `projects/{project_number}/locations/{location_id}/lakes/{lake_id}`
  string name = 1 [
    (google.api.resource_reference) = { type: "dataplex.googleapis.com/Lake" }
  ];
}

// Maintenance window. This specifies when Dataproc Metastore
// may perform system maintenance operation to the service.
message MaintenanceWindow {
  // The hour of day (0-23) when the window starts.
  google.protobuf.Int32Value hour_of_day = 1;

  // The day of week, when the window starts.
  google.type.DayOfWeek day_of_week = 2;
}

// Specifies configuration information specific to running Hive metastore
// software as the metastore service.
message HiveMetastoreConfig {
  // Protocols available for serving the metastore service endpoint.
  enum EndpointProtocol {
    // The protocol is not set.
    ENDPOINT_PROTOCOL_UNSPECIFIED = 0;

    // Use the legacy Apache Thrift protocol for the metastore service endpoint.
    THRIFT = 1;

    // Use the modernized gRPC protocol for the metastore service endpoint.
    GRPC = 2;
  }

  // Immutable. The Hive metastore schema version.
  string version = 1 [(google.api.field_behavior) = IMMUTABLE];

  // A mapping of Hive metastore configuration key-value pairs to apply to the
  // Hive metastore (configured in `hive-site.xml`). The mappings
  // override system defaults (some keys cannot be overridden). These
  // overrides are also applied to auxiliary versions and can be further
  // customized in the auxiliary version's `AuxiliaryVersionConfig`.
  map<string, string> config_overrides = 2;

  // Information used to configure the Hive metastore service as a service
  // principal in a Kerberos realm. To disable Kerberos, use the `UpdateService`
  // method and specify this field's path
  // (`hive_metastore_config.kerberos_config`) in the request's `update_mask`
  // while omitting this field from the request's `service`.
  KerberosConfig kerberos_config = 3;

  // The protocol to use for the metastore service endpoint. If unspecified,
  // defaults to `THRIFT`.
  EndpointProtocol endpoint_protocol = 4;

  // A mapping of Hive metastore version to the auxiliary version
  // configuration. When specified, a secondary Hive metastore service is
  // created along with the primary service. All auxiliary versions must be less
  // than the service's primary version. The key is the auxiliary service name
  // and it must match the regular expression [a-z]([-a-z0-9]*[a-z0-9])?. This
  // means that the first character must be a lowercase letter, and all the
  // following characters must be hyphens, lowercase letters, or digits, except
  // the last character, which cannot be a hyphen.
  map<string, AuxiliaryVersionConfig> auxiliary_versions = 5;
}

// Configuration information for a Kerberos principal.
message KerberosConfig {
  // A Kerberos keytab file that can be used to authenticate a service principal
  // with a Kerberos Key Distribution Center (KDC).
  Secret keytab = 1;

  // A Kerberos principal that exists in the both the keytab the KDC
  // to authenticate as. A typical principal is of the form
  // `primary/instance@REALM`, but there is no exact format.
  string principal = 2;

  // A Cloud Storage URI that specifies the path to a
  // krb5.conf file. It is of the form `gs://{bucket_name}/path/to/krb5.conf`,
  // although the file does not need to be named krb5.conf explicitly.
  string krb5_config_gcs_uri = 3;
}

// A securely stored value.
message Secret {
  oneof value {
    // The relative resource name of a Secret Manager secret version, in the
    // following form:
    //
    // `projects/{project_number}/secrets/{secret_id}/versions/{version_id}`.
    string cloud_secret = 2;
  }
}

// Encryption settings for the service.
message EncryptionConfig {
  // The fully qualified customer provided Cloud KMS key name to use for
  // customer data encryption, in the following form:
  //
  // `projects/{project_number}/locations/{location_id}/keyRings/{key_ring_id}/cryptoKeys/{crypto_key_id}`.
  string kms_key = 1;
}

// Configuration information for the auxiliary service versions.
message AuxiliaryVersionConfig {
  // The Hive metastore version of the auxiliary service. It must be less
  // than the primary Hive metastore service's version.
  string version = 1;

  // A mapping of Hive metastore configuration key-value pairs to apply to the
  // auxiliary Hive metastore (configured in `hive-site.xml`) in addition to
  // the primary version's overrides. If keys are present in both the auxiliary
  // version's overrides and the primary version's overrides, the value from
  // the auxiliary version's overrides takes precedence.
  map<string, string> config_overrides = 2;

  // Output only. The network configuration contains the endpoint URI(s) of the
  // auxiliary Hive metastore service.
  NetworkConfig network_config = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Network configuration for the Dataproc Metastore service.
//
// Next available ID: 4
message NetworkConfig {
  // Contains information of the customer's network configurations.
  //
  // Next available ID: 5
  message Consumer {
    oneof vpc_resource {
      // Immutable. The subnetwork of the customer project from which an IP
      // address is reserved and used as the Dataproc Metastore service's
      // endpoint. It is accessible to hosts in the subnet and to all
      // hosts in a subnet in the same region and same network. There must
      // be at least one IP address available in the subnet's primary range. The
      // subnet is specified in the following form:
      //
      // `projects/{project_number}/regions/{region_id}/subnetworks/{subnetwork_id}`
      string subnetwork = 1 [
        (google.api.field_behavior) = IMMUTABLE,
        (google.api.resource_reference) = {
          type: "compute.googleapis.com/Subnetwork"
        }
      ];
    }

    // Output only. The URI of the endpoint used to access the metastore
    // service.
    string endpoint_uri = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The location of the endpoint URI. Format:
    // `projects/{project}/locations/{location}`.
    string endpoint_location = 4 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "locations.googleapis.com/Location"
      }
    ];
  }

  // Immutable. The consumer-side network configuration for the Dataproc
  // Metastore instance.
  repeated Consumer consumers = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Enables custom routes to be imported and exported for the Dataproc
  // Metastore service's peered VPC network.
  bool custom_routes_enabled = 2;
}

// Telemetry Configuration for the Dataproc Metastore service.
message TelemetryConfig {
  enum LogFormat {
    // The LOG_FORMAT is not set.
    LOG_FORMAT_UNSPECIFIED = 0;

    // Logging output uses the legacy `textPayload` format.
    LEGACY = 1;

    // Logging output uses the `jsonPayload` format.
    JSON = 2;
  }

  // The output format of the Dataproc Metastore service's logs.
  LogFormat log_format = 1;
}

// The metadata management activities of the metastore service.
message MetadataManagementActivity {
  // Output only. The latest metadata exports of the metastore service.
  repeated MetadataExport metadata_exports = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The latest restores of the metastore service.
  repeated Restore restores = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A metastore resource that imports metadata.
message MetadataImport {
  option (google.api.resource) = {
    type: "metastore.googleapis.com/MetadataImport"
    pattern: "projects/{project}/locations/{location}/services/{service}/metadataImports/{metadata_import}"
  };

  // A specification of the location of and metadata about a database dump from
  // a relational database management system.
  message DatabaseDump {
    // The type of the database.
    enum DatabaseType {
      // The type of the source database is unknown.
      DATABASE_TYPE_UNSPECIFIED = 0;

      // The type of the source database is MySQL.
      MYSQL = 1;
    }

    // The type of the database.
    DatabaseType database_type = 1 [deprecated = true];

    // A Cloud Storage object or folder URI that specifies the source from which
    // to import metadata. It must begin with `gs://`.
    string gcs_uri = 2;

    // The name of the source database.
    string source_database = 3 [deprecated = true];

    // Optional. The type of the database dump. If unspecified, defaults to
    // `MYSQL`.
    DatabaseDumpSpec.Type type = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // The current state of the metadata import.
  enum State {
    // The state of the metadata import is unknown.
    STATE_UNSPECIFIED = 0;

    // The metadata import is running.
    RUNNING = 1;

    // The metadata import completed successfully.
    SUCCEEDED = 2;

    // The metadata import is being updated.
    UPDATING = 3;

    // The metadata import failed, and attempted metadata changes were rolled
    // back.
    FAILED = 4;
  }

  // The metadata to be imported.
  oneof metadata {
    // Immutable. A database dump from a pre-existing metastore's database.
    DatabaseDump database_dump = 6 [(google.api.field_behavior) = IMMUTABLE];
  }

  // Immutable. The relative resource name of the metadata import, of the form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}/metadataImports/{metadata_import_id}`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // The description of the metadata import.
  string description = 2;

  // Output only. The time when the metadata import was started.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the metadata import was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the metadata import finished.
  google.protobuf.Timestamp end_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the metadata import.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The details of a metadata export operation.
message MetadataExport {
  // The current state of the metadata export.
  enum State {
    // The state of the metadata export is unknown.
    STATE_UNSPECIFIED = 0;

    // The metadata export is running.
    RUNNING = 1;

    // The metadata export completed successfully.
    SUCCEEDED = 2;

    // The metadata export failed.
    FAILED = 3;

    // The metadata export is cancelled.
    CANCELLED = 4;
  }

  oneof destination {
    // Output only. A Cloud Storage URI of a folder that metadata are exported
    // to, in the form of
    // `gs://<bucket_name>/<path_inside_bucket>/<export_folder>`, where
    // `<export_folder>` is automatically generated.
    string destination_gcs_uri = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The time when the export started.
  google.protobuf.Timestamp start_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the export ended.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the export.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the database dump.
  DatabaseDumpSpec.Type database_dump_type = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The details of a backup resource.
message Backup {
  option (google.api.resource) = {
    type: "metastore.googleapis.com/Backup"
    pattern: "projects/{project}/locations/{location}/services/{service}/backups/{backup}"
  };

  // The current state of the backup.
  enum State {
    // The state of the backup is unknown.
    STATE_UNSPECIFIED = 0;

    // The backup is being created.
    CREATING = 1;

    // The backup is being deleted.
    DELETING = 2;

    // The backup is active and ready to use.
    ACTIVE = 3;

    // The backup failed.
    FAILED = 4;

    // The backup is being restored.
    RESTORING = 5;
  }

  // Immutable. The relative resource name of the backup, in the following form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}/backups/{backup_id}`
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The time when the backup was started.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the backup finished creating.
  google.protobuf.Timestamp end_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the backup.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The revision of the service at the time of backup.
  Service service_revision = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The description of the backup.
  string description = 6;

  // Output only. Services that are restoring from the backup.
  repeated string restoring_services = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The details of a metadata restore operation.
message Restore {
  // The current state of the restore.
  enum State {
    // The state of the metadata restore is unknown.
    STATE_UNSPECIFIED = 0;

    // The metadata restore is running.
    RUNNING = 1;

    // The metadata restore completed successfully.
    SUCCEEDED = 2;

    // The metadata restore failed.
    FAILED = 3;

    // The metadata restore is cancelled.
    CANCELLED = 4;
  }

  // The type of restore. If unspecified, defaults to `METADATA_ONLY`.
  enum RestoreType {
    // The restore type is unknown.
    RESTORE_TYPE_UNSPECIFIED = 0;

    // The service's metadata and configuration are restored.
    FULL = 1;

    // Only the service's metadata is restored.
    METADATA_ONLY = 2;
  }

  // Output only. The time when the restore started.
  google.protobuf.Timestamp start_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the restore ended.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the restore.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The relative resource name of the metastore service backup to
  // restore from, in the following form:
  //
  // `projects/{project_id}/locations/{location_id}/services/{service_id}/backups/{backup_id}`.
  string backup = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Backup"
    }
  ];

  // Output only. The type of restore.
  RestoreType type = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The restore details containing the revision of the service to
  // be restored to, in format of JSON.
  string details = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents the scaling configuration of a metastore service.
message ScalingConfig {
  // Metastore instance sizes.
  enum InstanceSize {
    // Unspecified instance size
    INSTANCE_SIZE_UNSPECIFIED = 0;

    // Extra small instance size, maps to a scaling factor of 0.1.
    EXTRA_SMALL = 1;

    // Small instance size, maps to a scaling factor of 0.5.
    SMALL = 2;

    // Medium instance size, maps to a scaling factor of 1.0.
    MEDIUM = 3;

    // Large instance size, maps to a scaling factor of 3.0.
    LARGE = 4;

    // Extra large instance size, maps to a scaling factor of 6.0.
    EXTRA_LARGE = 5;
  }

  // Represents either a predetermined instance size or a numeric
  // scaling factor.
  oneof scaling_model {
    // An enum of readable instance sizes, with each instance size mapping to a
    // float value (e.g. InstanceSize.EXTRA_SMALL = scaling_factor(0.1))
    InstanceSize instance_size = 1;

    // Scaling factor, increments of 0.1 for values less than 1.0, and
    // increments of 1.0 for values greater than 1.0.
    float scaling_factor = 2;
  }
}

// Request message for
// [DataprocMetastore.ListServices][google.cloud.metastore.v1beta.DataprocMetastore.ListServices].
message ListServicesRequest {
  // Required. The relative resource name of the location of metastore services
  // to list, in the following form:
  //
  // `projects/{project_number}/locations/{location_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "metastore.googleapis.com/Service"
    }
  ];

  // Optional. The maximum number of services to return. The response may
  // contain less than the maximum number. If unspecified, no more than 500
  // services are returned. The maximum value is 1000; values above 1000 are
  // changed to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // [DataprocMetastore.ListServices][google.cloud.metastore.v1beta.DataprocMetastore.ListServices]
  // call. Provide this token to retrieve the subsequent page.
  //
  // To retrieve the first page, supply an empty page token.
  //
  // When paginating, other parameters provided to
  // [DataprocMetastore.ListServices][google.cloud.metastore.v1beta.DataprocMetastore.ListServices]
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter to apply to list results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify the ordering of results as described in [Sorting
  // Order](https://cloud.google.com/apis/design/design_patterns#sorting_order).
  // If not specified, the results will be sorted in the default order.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [DataprocMetastore.ListServices][google.cloud.metastore.v1beta.DataprocMetastore.ListServices].
message ListServicesResponse {
  // The services in the specified location.
  repeated Service services = 1;

  // A token that can be sent as `page_token` to retrieve the next page. If this
  // field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [DataprocMetastore.GetService][google.cloud.metastore.v1beta.DataprocMetastore.GetService].
message GetServiceRequest {
  // Required. The relative resource name of the metastore service to retrieve,
  // in the following form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Service"
    }
  ];
}

// Request message for
// [DataprocMetastore.CreateService][google.cloud.metastore.v1beta.DataprocMetastore.CreateService].
message CreateServiceRequest {
  // Required. The relative resource name of the location in which to create a
  // metastore service, in the following form:
  //
  // `projects/{project_number}/locations/{location_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "metastore.googleapis.com/Service"
    }
  ];

  // Required. The ID of the metastore service, which is used as the final
  // component of the metastore service's name.
  //
  // This value must be between 2 and 63 characters long inclusive, begin with a
  // letter, end with a letter or number, and consist of alpha-numeric
  // ASCII characters or hyphens.
  string service_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Metastore service to create. The `name` field is
  // ignored. The ID of the created metastore service must be provided in
  // the request's `service_id` field.
  Service service = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID. Specify a unique request ID to allow the server to
  // ignore the request if it has completed. The server will ignore subsequent
  // requests that provide a duplicate request ID for at least 60 minutes after
  // the first request.
  //
  // For example, if an initial request times out, followed by another request
  // with the same request ID, the server ignores the second request to prevent
  // the creation of duplicate commitments.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format)
  // A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [DataprocMetastore.UpdateService][google.cloud.metastore.v1beta.DataprocMetastore.UpdateService].
message UpdateServiceRequest {
  // Required. A field mask used to specify the fields to be overwritten in the
  // metastore service resource by the update.
  // Fields specified in the `update_mask` are relative to the resource (not
  // to the full request). A field is overwritten if it is in the mask.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The metastore service to update. The server only merges fields
  // in the service if they are specified in `update_mask`.
  //
  // The metastore service's `name` field is used to identify the metastore
  // service to be updated.
  Service service = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID. Specify a unique request ID to allow the server to
  // ignore the request if it has completed. The server will ignore subsequent
  // requests that provide a duplicate request ID for at least 60 minutes after
  // the first request.
  //
  // For example, if an initial request times out, followed by another request
  // with the same request ID, the server ignores the second request to prevent
  // the creation of duplicate commitments.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format)
  // A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [DataprocMetastore.DeleteService][google.cloud.metastore.v1beta.DataprocMetastore.DeleteService].
message DeleteServiceRequest {
  // Required. The relative resource name of the metastore service to delete, in
  // the following form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Service"
    }
  ];

  // Optional. A request ID. Specify a unique request ID to allow the server to
  // ignore the request if it has completed. The server will ignore subsequent
  // requests that provide a duplicate request ID for at least 60 minutes after
  // the first request.
  //
  // For example, if an initial request times out, followed by another request
  // with the same request ID, the server ignores the second request to prevent
  // the creation of duplicate commitments.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format)
  // A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [DataprocMetastore.ListMetadataImports][google.cloud.metastore.v1beta.DataprocMetastore.ListMetadataImports].
message ListMetadataImportsRequest {
  // Required. The relative resource name of the service whose metadata imports
  // to list, in the following form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}/metadataImports`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "metastore.googleapis.com/MetadataImport"
    }
  ];

  // Optional. The maximum number of imports to return. The response may contain
  // less than the maximum number. If unspecified, no more than 500 imports are
  // returned. The maximum value is 1000; values above 1000 are changed to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // [DataprocMetastore.ListServices][google.cloud.metastore.v1beta.DataprocMetastore.ListServices]
  // call. Provide this token to retrieve the subsequent page.
  //
  // To retrieve the first page, supply an empty page token.
  //
  // When paginating, other parameters provided to
  // [DataprocMetastore.ListServices][google.cloud.metastore.v1beta.DataprocMetastore.ListServices]
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter to apply to list results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify the ordering of results as described in [Sorting
  // Order](https://cloud.google.com/apis/design/design_patterns#sorting_order).
  // If not specified, the results will be sorted in the default order.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [DataprocMetastore.ListMetadataImports][google.cloud.metastore.v1beta.DataprocMetastore.ListMetadataImports].
message ListMetadataImportsResponse {
  // The imports in the specified service.
  repeated MetadataImport metadata_imports = 1;

  // A token that can be sent as `page_token` to retrieve the next page. If this
  // field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [DataprocMetastore.GetMetadataImport][google.cloud.metastore.v1beta.DataprocMetastore.GetMetadataImport].
message GetMetadataImportRequest {
  // Required. The relative resource name of the metadata import to retrieve, in
  // the following form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}/metadataImports/{import_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/MetadataImport"
    }
  ];
}

// Request message for
// [DataprocMetastore.CreateMetadataImport][google.cloud.metastore.v1beta.DataprocMetastore.CreateMetadataImport].
message CreateMetadataImportRequest {
  // Required. The relative resource name of the service in which to create a
  // metastore import, in the following form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "metastore.googleapis.com/MetadataImport"
    }
  ];

  // Required. The ID of the metadata import, which is used as the final
  // component of the metadata import's name.
  //
  // This value must be between 1 and 64 characters long, begin with a letter,
  // end with a letter or number, and consist of alpha-numeric ASCII characters
  // or hyphens.
  string metadata_import_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The metadata import to create. The `name` field is ignored. The
  // ID of the created metadata import must be provided in the request's
  // `metadata_import_id` field.
  MetadataImport metadata_import = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID. Specify a unique request ID to allow the server to
  // ignore the request if it has completed. The server will ignore subsequent
  // requests that provide a duplicate request ID for at least 60 minutes after
  // the first request.
  //
  // For example, if an initial request times out, followed by another request
  // with the same request ID, the server ignores the second request to prevent
  // the creation of duplicate commitments.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format)
  // A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [DataprocMetastore.UpdateMetadataImport][google.cloud.metastore.v1beta.DataprocMetastore.UpdateMetadataImport].
message UpdateMetadataImportRequest {
  // Required. A field mask used to specify the fields to be overwritten in the
  // metadata import resource by the update.
  // Fields specified in the `update_mask` are relative to the resource (not
  // to the full request). A field is overwritten if it is in the mask.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The metadata import to update. The server only merges fields
  // in the import if they are specified in `update_mask`.
  //
  // The metadata import's `name` field is used to identify the metastore
  // import to be updated.
  MetadataImport metadata_import = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID. Specify a unique request ID to allow the server to
  // ignore the request if it has completed. The server will ignore subsequent
  // requests that provide a duplicate request ID for at least 60 minutes after
  // the first request.
  //
  // For example, if an initial request times out, followed by another request
  // with the same request ID, the server ignores the second request to prevent
  // the creation of duplicate commitments.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format)
  // A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [DataprocMetastore.ListBackups][google.cloud.metastore.v1beta.DataprocMetastore.ListBackups].
message ListBackupsRequest {
  // Required. The relative resource name of the service whose backups to
  // list, in the following form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}/backups`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "metastore.googleapis.com/Backup"
    }
  ];

  // Optional. The maximum number of backups to return. The response may contain
  // less than the maximum number. If unspecified, no more than 500 backups are
  // returned. The maximum value is 1000; values above 1000 are changed to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // [DataprocMetastore.ListBackups][google.cloud.metastore.v1beta.DataprocMetastore.ListBackups]
  // call. Provide this token to retrieve the subsequent page.
  //
  // To retrieve the first page, supply an empty page token.
  //
  // When paginating, other parameters provided to
  // [DataprocMetastore.ListBackups][google.cloud.metastore.v1beta.DataprocMetastore.ListBackups]
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter to apply to list results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify the ordering of results as described in [Sorting
  // Order](https://cloud.google.com/apis/design/design_patterns#sorting_order).
  // If not specified, the results will be sorted in the default order.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [DataprocMetastore.ListBackups][google.cloud.metastore.v1beta.DataprocMetastore.ListBackups].
message ListBackupsResponse {
  // The backups of the specified service.
  repeated Backup backups = 1;

  // A token that can be sent as `page_token` to retrieve the next page. If this
  // field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for
// [DataprocMetastore.GetBackup][google.cloud.metastore.v1beta.DataprocMetastore.GetBackup].
message GetBackupRequest {
  // Required. The relative resource name of the backup to retrieve, in the
  // following form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}/backups/{backup_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Backup"
    }
  ];
}

// Request message for
// [DataprocMetastore.CreateBackup][google.cloud.metastore.v1beta.DataprocMetastore.CreateBackup].
message CreateBackupRequest {
  // Required. The relative resource name of the service in which to create a
  // backup of the following form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "metastore.googleapis.com/Backup"
    }
  ];

  // Required. The ID of the backup, which is used as the final component of the
  // backup's name.
  //
  // This value must be between 1 and 64 characters long, begin with a letter,
  // end with a letter or number, and consist of alpha-numeric ASCII characters
  // or hyphens.
  string backup_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The backup to create. The `name` field is ignored. The ID of the
  // created backup must be provided in the request's `backup_id` field.
  Backup backup = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID. Specify a unique request ID to allow the server to
  // ignore the request if it has completed. The server will ignore subsequent
  // requests that provide a duplicate request ID for at least 60 minutes after
  // the first request.
  //
  // For example, if an initial request times out, followed by another request
  // with the same request ID, the server ignores the second request to prevent
  // the creation of duplicate commitments.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format)
  // A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [DataprocMetastore.DeleteBackup][google.cloud.metastore.v1beta.DataprocMetastore.DeleteBackup].
message DeleteBackupRequest {
  // Required. The relative resource name of the backup to delete, in the
  // following form:
  //
  // `projects/{project_number}/locations/{location_id}/services/{service_id}/backups/{backup_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Backup"
    }
  ];

  // Optional. A request ID. Specify a unique request ID to allow the server to
  // ignore the request if it has completed. The server will ignore subsequent
  // requests that provide a duplicate request ID for at least 60 minutes after
  // the first request.
  //
  // For example, if an initial request times out, followed by another request
  // with the same request ID, the server ignores the second request to prevent
  // the creation of duplicate commitments.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format)
  // A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [DataprocMetastore.ExportMetadata][google.cloud.metastore.v1beta.DataprocMetastore.ExportMetadata].
message ExportMetadataRequest {
  // Required. Destination that metadata is exported to.
  oneof destination {
    // A Cloud Storage URI of a folder, in the format
    // `gs://<bucket_name>/<path_inside_bucket>`. A sub-folder
    // `<export_folder>` containing exported files will be created below it.
    string destination_gcs_folder = 2;
  }

  // Required. The relative resource name of the metastore service to run
  // export, in the following form:
  //
  // `projects/{project_id}/locations/{location_id}/services/{service_id}`.
  string service = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Service"
    }
  ];

  // Optional. A request ID. Specify a unique request ID to allow the server to
  // ignore the request if it has completed. The server will ignore subsequent
  // requests that provide a duplicate request ID for at least 60 minutes after
  // the first request.
  //
  // For example, if an initial request times out, followed by another request
  // with the same request ID, the server ignores the second request to prevent
  // the creation of duplicate commitments.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format).
  // A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The type of the database dump. If unspecified, defaults to
  // `MYSQL`.
  DatabaseDumpSpec.Type database_dump_type = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for [DataprocMetastore.Restore][].
message RestoreServiceRequest {
  // Required. The relative resource name of the metastore service to run
  // restore, in the following form:
  //
  // `projects/{project_id}/locations/{location_id}/services/{service_id}`.
  string service = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Service"
    }
  ];

  // Required. The relative resource name of the metastore service backup to
  // restore from, in the following form:
  //
  // `projects/{project_id}/locations/{location_id}/services/{service_id}/backups/{backup_id}`.
  string backup = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Backup"
    }
  ];

  // Optional. The type of restore. If unspecified, defaults to `METADATA_ONLY`.
  Restore.RestoreType restore_type = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A request ID. Specify a unique request ID to allow the server to
  // ignore the request if it has completed. The server will ignore subsequent
  // requests that provide a duplicate request ID for at least 60 minutes after
  // the first request.
  //
  // For example, if an initial request times out, followed by another request
  // with the same request ID, the server ignores the second request to prevent
  // the creation of duplicate commitments.
  //
  // The request ID must be a valid
  // [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier#Format).
  // A zero UUID (00000000-0000-0000-0000-000000000000) is not supported.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of a long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the caller has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata about the service in a location.
message LocationMetadata {
  // A specification of a supported version of the Hive Metastore software.
  message HiveMetastoreVersion {
    // The semantic version of the Hive Metastore software.
    string version = 1;

    // Whether `version` will be chosen by the server if a metastore service is
    // created with a `HiveMetastoreConfig` that omits the `version`.
    bool is_default = 2;
  }

  // The versions of Hive Metastore that can be used when creating a new
  // metastore service in this location. The server guarantees that exactly one
  // `HiveMetastoreVersion` in the list will set `is_default`.
  repeated HiveMetastoreVersion supported_hive_metastore_versions = 1;
}

// The specification of database dump to import from or export to.
message DatabaseDumpSpec {
  // The type of the database dump.
  enum Type {
    // The type of the database dump is unknown.
    TYPE_UNSPECIFIED = 0;

    // Database dump is a MySQL dump file.
    MYSQL = 1;

    // Database dump contains Avro files.
    AVRO = 2;
  }
}

// Request message for
// [DataprocMetastore.RemoveIamPolicy][google.cloud.metastore.v1beta.DataprocMetastore.RemoveIamPolicy].
message RemoveIamPolicyRequest {
  // Required. The relative resource name of the dataplane resource to remove
  // IAM policy, in the following form:
  //
  // `projects/{project_id}/locations/{location_id}/services/{service_id}/databases/{database_id}`
  // or
  // `projects/{project_id}/locations/{location_id}/services/{service_id}/databases/{database_id}/tables/{table_id}`.
  string resource = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "*" }
  ];

  // Optional. Removes IAM policy attached to database or table asynchronously
  // when it is set. The default is false.
  bool asynchronous = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [DataprocMetastore.RemoveIamPolicy][google.cloud.metastore.v1beta.DataprocMetastore.RemoveIamPolicy].
message RemoveIamPolicyResponse {
  // True if the policy is successfully removed.
  bool success = 1;
}

// Request message for
// [DataprocMetastore.QueryMetadata][google.cloud.metastore.v1beta.DataprocMetastore.QueryMetadata].
message QueryMetadataRequest {
  // Required. The relative resource name of the metastore service to query
  // metadata, in the following format:
  //
  // `projects/{project_id}/locations/{location_id}/services/{service_id}`.
  string service = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Service"
    }
  ];

  // Required. A read-only SQL query to execute against the metadata database.
  // The query cannot change or mutate the data.
  string query = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [DataprocMetastore.QueryMetadata][google.cloud.metastore.v1beta.DataprocMetastore.QueryMetadata].
message QueryMetadataResponse {
  // The manifest URI  is link to a JSON instance in Cloud Storage.
  // This instance manifests immediately along with QueryMetadataResponse. The
  // content of the URI is not retriable until the long-running operation query
  // against the metadata finishes.
  string result_manifest_uri = 1;
}

// Error details in public error message for
// [DataprocMetastore.QueryMetadata][google.cloud.metastore.v1beta.DataprocMetastore.QueryMetadata].
message ErrorDetails {
  // Additional structured details about this error.
  //
  // Keys define the failure items.
  // Value describes the exception or details of the item.
  map<string, string> details = 1;
}

// Request message for
// [DataprocMetastore.MoveTableToDatabase][google.cloud.metastore.v1beta.DataprocMetastore.MoveTableToDatabase].
message MoveTableToDatabaseRequest {
  // Required. The relative resource name of the metastore service to mutate
  // metadata, in the following format:
  //
  // `projects/{project_id}/locations/{location_id}/services/{service_id}`.
  string service = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Service"
    }
  ];

  // Required. The name of the table to be moved.
  string table_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the database where the table resides.
  string db_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the database where the table should be moved.
  string destination_db_name = 4 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [DataprocMetastore.MoveTableToDatabase][google.cloud.metastore.v1beta.DataprocMetastore.MoveTableToDatabase].
message MoveTableToDatabaseResponse {}

// Request message for
// [DataprocMetastore.AlterMetadataResourceLocation][google.cloud.metastore.v1beta.DataprocMetastore.AlterMetadataResourceLocation].
message AlterMetadataResourceLocationRequest {
  // Required. The relative resource name of the metastore service to mutate
  // metadata, in the following format:
  //
  // `projects/{project_id}/locations/{location_id}/services/{service_id}`.
  string service = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "metastore.googleapis.com/Service"
    }
  ];

  // Required. The relative metadata resource name in the following format.
  //
  // `databases/{database_id}`
  // or
  // `databases/{database_id}/tables/{table_id}`
  // or
  // `databases/{database_id}/tables/{table_id}/partitions/{partition_id}`
  string resource_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The new location URI for the metadata resource.
  string location_uri = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [DataprocMetastore.AlterMetadataResourceLocation][google.cloud.metastore.v1beta.DataprocMetastore.AlterMetadataResourceLocation].
message AlterMetadataResourceLocationResponse {}
