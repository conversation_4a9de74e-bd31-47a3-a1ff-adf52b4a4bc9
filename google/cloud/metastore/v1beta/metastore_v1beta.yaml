type: google.api.Service
config_version: 3
name: metastore.googleapis.com
title: Dataproc Metastore API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.metastore.v1beta.DataprocMetastore
- name: google.cloud.metastore.v1beta.DataprocMetastoreFederation
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.metastore.v1beta.AlterMetadataResourceLocationResponse
- name: google.cloud.metastore.v1beta.ErrorDetails
- name: google.cloud.metastore.v1beta.LocationMetadata
- name: google.cloud.metastore.v1beta.MoveTableToDatabaseResponse
- name: google.cloud.metastore.v1beta.OperationMetadata
- name: google.cloud.metastore.v1beta.QueryMetadataResponse

documentation:
  summary: |-
    The Dataproc Metastore API is used to manage the lifecycle and
    configuration of metastore services.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: 'google.cloud.metastore.v1beta.DataprocMetastore.*'
    deadline: 60.0
  - selector: 'google.cloud.metastore.v1beta.DataprocMetastoreFederation.*'
    deadline: 60.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1beta/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1beta/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1beta/{resource=projects/*/locations/*/services/*}:getIamPolicy'
    additional_bindings:
    - get: '/v1beta/{resource=projects/*/locations/*/services/*/backups/*}:getIamPolicy'
    - get: '/v1beta/{resource=projects/*/locations/*/services/*/databases/*}:getIamPolicy'
    - get: '/v1beta/{resource=projects/*/locations/*/services/*/databases/*/tables/*}:getIamPolicy'
    - get: '/v1beta/{resource=projects/*/locations/*/federations/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1beta/{resource=projects/*/locations/*/services/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1beta/{resource=projects/*/locations/*/services/*/backups/*}:setIamPolicy'
      body: '*'
    - post: '/v1beta/{resource=projects/*/locations/*/services/*/databases/*}:setIamPolicy'
      body: '*'
    - post: '/v1beta/{resource=projects/*/locations/*/services/*/databases/*/tables/*}:setIamPolicy'
      body: '*'
    - post: '/v1beta/{resource=projects/*/locations/*/federations/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1beta/{resource=projects/*/locations/*/services/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1beta/{resource=projects/*/locations/*/services/*/backups/*}:testIamPermissions'
      body: '*'
    - post: '/v1beta/{resource=projects/*/locations/*/services/*/databases/*}:testIamPermissions'
      body: '*'
    - post: '/v1beta/{resource=projects/*/locations/*/services/*/databases/*/tables/*}:testIamPermissions'
      body: '*'
    - post: '/v1beta/{resource=projects/*/locations/*/federations/*}:testIamPermissions'
      body: '*'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1beta/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1beta/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1beta/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1beta/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.metastore.v1beta.DataprocMetastore.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.metastore.v1beta.DataprocMetastoreFederation.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
