// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.metastore.logging.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/metastore/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "ScheduledBackupsLogProto";
option java_package = "google.cloud.metastore.logging.v1";

// Cloud Logging log schema for scheduled backup events.
message ScheduledBackupLogEntry {
  // The current state of the backup.
  enum State {
    // The state of the backup is unknown.
    STATE_UNSPECIFIED = 0;

    // The backup is in progress.
    IN_PROGRESS = 1;

    // The backup completed.
    SUCCEEDED = 2;

    // The backup failed.
    FAILED = 3;
  }

  // The ID of the backup.
  string backup_id = 1;

  // The relative resource name of a Metastore service in the form of
  // `projects/{project_id}/locations/{location_id}/services/{service_id}`
  string service = 2;

  // Timestamp when the backup was started.
  google.protobuf.Timestamp start_time = 3;

  // Timestamp when the backup was completed.
  google.protobuf.Timestamp end_time = 4;

  // Output only. The current state of the backup.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Size of the backup data in bytes.
  int64 backup_size_bytes = 6;

  // A Cloud Storage URI of a folder, in the format
  // `gs://<bucket_name>/<path_inside_bucket>`.
  string backup_location = 7;

  // Message that provides (optional) details about the backup.
  string message = 8;
}
