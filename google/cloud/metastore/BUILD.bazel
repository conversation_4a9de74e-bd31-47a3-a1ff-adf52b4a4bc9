# This build file includes a target for the Ruby wrapper library for
# google-cloud-metastore.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for metastore.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "metastore_ruby_wrapper",
    srcs = ["//google/cloud/metastore/v1:metastore_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-metastore",
        "ruby-cloud-env-prefix=METASTORE",
        "ruby-cloud-wrapper-of=v1:0.12;v1beta:0.12",
        "ruby-cloud-product-url=https://cloud.google.com/dataproc-metastore/",
        "ruby-cloud-api-id=metastore.googleapis.com",
        "ruby-cloud-api-shortname=metastore",
    ],
    ruby_cloud_description = "Dataproc Metastore is a fully managed, highly available within a region, autohealing serverless Apache Hive metastore (HMS) on Google Cloud for data analytics products. It supports HMS and serves as a critical component for managing the metadata of relational entities and provides interoperability between data processing applications in the open source data ecosystem.",
    ruby_cloud_title = "Dataproc Metastore",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-metastore-ruby",
    deps = [
        ":metastore_ruby_wrapper",
    ],
)
