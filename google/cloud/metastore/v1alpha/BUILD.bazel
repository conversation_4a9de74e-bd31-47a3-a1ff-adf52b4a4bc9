# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "metastore_proto",
    srcs = [
        "metastore.proto",
        "metastore_federation.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:dayofweek_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "metastore_proto_with_info",
    deps = [
        ":metastore_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "metastore_java_proto",
    deps = [":metastore_proto"],
)

java_grpc_library(
    name = "metastore_java_grpc",
    srcs = [":metastore_proto"],
    deps = [":metastore_java_proto"],
)

java_gapic_library(
    name = "metastore_java_gapic",
    srcs = [":metastore_proto_with_info"],
    gapic_yaml = "metastore_gapic.yaml",
    grpc_service_config = "metastore_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "metastore_v1alpha.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
        ":metastore_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":metastore_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "metastore_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.metastore.v1alpha.DataprocMetastoreClientHttpJsonTest",
        "com.google.cloud.metastore.v1alpha.DataprocMetastoreClientTest",
        "com.google.cloud.metastore.v1alpha.DataprocMetastoreFederationClientHttpJsonTest",
        "com.google.cloud.metastore.v1alpha.DataprocMetastoreFederationClientTest",
    ],
    runtime_deps = [":metastore_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-metastore-v1alpha-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":metastore_java_gapic",
        ":metastore_java_grpc",
        ":metastore_java_proto",
        ":metastore_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "metastore_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/metastore/apiv1alpha/metastorepb",
    protos = [":metastore_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:dayofweek_go_proto",
    ],
)

go_gapic_library(
    name = "metastore_go_gapic",
    srcs = [":metastore_proto_with_info"],
    grpc_service_config = "metastore_grpc_service_config.json",
    importpath = "cloud.google.com/go/metastore/apiv1alpha;metastore",
    metadata = True,
    release_level = "alpha",
    rest_numeric_enums = True,
    service_yaml = "metastore_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":metastore_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-metastore-v1alpha-go",
    deps = [
        ":metastore_go_gapic",
        ":metastore_go_gapic_srcjar-metadata.srcjar",
        ":metastore_go_gapic_srcjar-snippets.srcjar",
        ":metastore_go_gapic_srcjar-test.srcjar",
        ":metastore_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "metastore_py_gapic",
    srcs = [":metastore_proto"],
    grpc_service_config = "metastore_grpc_service_config.json",
    opt_args = ["warehouse-package-name=google-cloud-dataproc-metastore"],
    rest_numeric_enums = True,
    service_yaml = "metastore_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "metastore_py_gapic_test",
    srcs = [
        "metastore_py_gapic_pytest.py",
        "metastore_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":metastore_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "metastore-v1alpha-py",
    deps = [
        ":metastore_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "metastore_php_proto",
    deps = [":metastore_proto"],
)

php_gapic_library(
    name = "metastore_php_gapic",
    srcs = [":metastore_proto_with_info"],
    grpc_service_config = "metastore_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "metastore_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":metastore_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-metastore-v1alpha-php",
    deps = [
        ":metastore_php_gapic",
        ":metastore_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "metastore_nodejs_gapic",
    package_name = "@google-cloud/dataproc-metastore",
    src = ":metastore_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "metastore_grpc_service_config.json",
    package = "google.cloud.metastore.v1alpha",
    rest_numeric_enums = True,
    service_yaml = "metastore_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "metastore-v1alpha-nodejs",
    deps = [
        ":metastore_nodejs_gapic",
        ":metastore_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "metastore_ruby_proto",
    deps = [":metastore_proto"],
)

ruby_grpc_library(
    name = "metastore_ruby_grpc",
    srcs = [":metastore_proto"],
    deps = [":metastore_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "metastore_ruby_gapic",
    srcs = [":metastore_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-metastore-v1alpha"],
    grpc_service_config = "metastore_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "metastore_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":metastore_ruby_grpc",
        ":metastore_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-metastore-v1alpha-ruby",
    deps = [
        ":metastore_ruby_gapic",
        ":metastore_ruby_grpc",
        ":metastore_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "metastore_csharp_proto",
    deps = [":metastore_proto"],
)

csharp_grpc_library(
    name = "metastore_csharp_grpc",
    srcs = [":metastore_proto"],
    deps = [":metastore_csharp_proto"],
)

csharp_gapic_library(
    name = "metastore_csharp_gapic",
    srcs = [":metastore_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "metastore_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "metastore_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":metastore_csharp_grpc",
        ":metastore_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-metastore-v1alpha-csharp",
    deps = [
        ":metastore_csharp_gapic",
        ":metastore_csharp_grpc",
        ":metastore_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "metastore_cc_proto",
    deps = [":metastore_proto"],
)

cc_grpc_library(
    name = "metastore_cc_grpc",
    srcs = [":metastore_proto"],
    grpc_only = True,
    deps = [":metastore_cc_proto"],
)
