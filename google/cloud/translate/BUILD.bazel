# This build file includes a target for the Ruby wrapper library for
# google-cloud-translate.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for translate.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v3 in this case.
ruby_cloud_gapic_library(
    name = "translate_ruby_wrapper",
    srcs = ["//google/cloud/translate/v3:translation_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-translate",
        "ruby-cloud-env-prefix=TRANSLATE",
        "ruby-cloud-wrapper-of=v3:0.11;v2:0.0",
        "ruby-cloud-product-url=https://cloud.google.com/translate",
        "ruby-cloud-api-id=translate.googleapis.com",
        "ruby-cloud-api-shortname=translate",
        "ruby-cloud-migration-version=3.0",
    ],
    ruby_cloud_description = "Cloud Translation can dynamically translate text between thousands of language pairs. Translation lets websites and programs programmatically integrate with the translation service.",
    ruby_cloud_title = "Cloud Translation",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-translate-ruby",
    deps = [
        ":translate_ruby_wrapper",
    ],
)
