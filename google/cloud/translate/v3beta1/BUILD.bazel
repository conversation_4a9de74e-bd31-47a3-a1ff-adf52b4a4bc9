# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "translation_proto",
    srcs = [
        "translation_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "translation_proto_with_info",
    deps = [
        ":translation_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "translation_java_proto",
    deps = [":translation_proto"],
)

java_grpc_library(
    name = "translation_java_grpc",
    srcs = [":translation_proto"],
    deps = [":translation_java_proto"],
)

java_gapic_library(
    name = "translation_java_gapic",
    srcs = [":translation_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "translate_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "translate_v3beta1.yaml",
    test_deps = [
        ":translation_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":translation_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "translation_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.translate.v3beta1.TranslationServiceClientHttpJsonTest",
        "com.google.cloud.translate.v3beta1.TranslationServiceClientTest",
    ],
    runtime_deps = [":translation_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-translation-v3beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":translation_java_gapic",
        ":translation_java_grpc",
        ":translation_java_proto",
        ":translation_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "translation_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/translation/apiv3beta1/translationpb",
    protos = [":translation_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "translation_go_gapic",
    srcs = [":translation_proto_with_info"],
    grpc_service_config = "translate_grpc_service_config.json",
    importpath = "cloud.google.com/go/translate/apiv3beta1;translate",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "translate_v3beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":translation_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-translation-v3beta1-go",
    deps = [
        ":translation_go_gapic",
        ":translation_go_gapic_srcjar-metadata.srcjar",
        ":translation_go_gapic_srcjar-snippets.srcjar",
        ":translation_go_gapic_srcjar-test.srcjar",
        ":translation_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "translation_py_gapic",
    srcs = [":translation_proto"],
    grpc_service_config = "translate_grpc_service_config.json",
    opt_args = ["python-gapic-name=translate"],
    rest_numeric_enums = True,
    service_yaml = "translate_v3beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "translation_py_gapic_test",
    srcs = [
        "translation_py_gapic_pytest.py",
        "translation_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":translation_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "translation-v3beta1-py",
    deps = [
        ":translation_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "translation_php_proto",
    deps = [":translation_proto"],
)

php_gapic_library(
    name = "translation_php_gapic",
    srcs = [":translation_proto_with_info"],
    grpc_service_config = "translate_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "translate_v3beta1.yaml",
    transport = "grpc+rest",
    deps = [":translation_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-translation-v3beta1-php",
    deps = [
        ":translation_php_gapic",
        ":translation_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "translation_nodejs_gapic",
    package_name = "@google-cloud/translate",
    src = ":translation_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "translate_grpc_service_config.json",
    package = "google.cloud.translation.v3beta1",
    rest_numeric_enums = True,
    service_yaml = "translate_v3beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "translation-v3beta1-nodejs",
    deps = [
        ":translation_nodejs_gapic",
        ":translation_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "translation_ruby_proto",
    deps = [":translation_proto"],
)

ruby_grpc_library(
    name = "translation_ruby_grpc",
    srcs = [":translation_proto"],
    deps = [":translation_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "translation_ruby_gapic",
    srcs = [":translation_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-translation-v3beta1"],
    grpc_service_config = "translate_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "translate_v3beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":translation_ruby_grpc",
        ":translation_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-translation-v3beta1-ruby",
    deps = [
        ":translation_ruby_gapic",
        ":translation_ruby_grpc",
        ":translation_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "translation_csharp_proto",
    deps = [":translation_proto"],
)

csharp_grpc_library(
    name = "translation_csharp_grpc",
    srcs = [":translation_proto"],
    deps = [":translation_csharp_proto"],
)

csharp_gapic_library(
    name = "translation_csharp_gapic",
    srcs = [":translation_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "translate_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "translate_v3beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":translation_csharp_grpc",
        ":translation_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-translation-v3beta1-csharp",
    deps = [
        ":translation_csharp_gapic",
        ":translation_csharp_grpc",
        ":translation_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
