// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.translation.v3;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/translate/v3/common.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Translate.V3";
option go_package = "cloud.google.com/go/translate/apiv3/translatepb;translatepb";
option java_multiple_files = true;
option java_outer_classname = "AdaptiveMtProto";
option java_package = "com.google.cloud.translate.v3";
option php_namespace = "Google\\Cloud\\Translate\\V3";
option ruby_package = "Google::Cloud::Translate::V3";

// An Adaptive MT Dataset.
message AdaptiveMtDataset {
  option (google.api.resource) = {
    type: "translate.googleapis.com/AdaptiveMtDataset"
    pattern: "projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}"
  };

  // Required. The resource name of the dataset, in form of
  // `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtDataset"
    }
  ];

  // The name of the dataset to show in the interface. The name can be
  // up to 32 characters long and can consist only of ASCII Latin letters A-Z
  // and a-z, underscores (_), and ASCII digits 0-9.
  string display_name = 2;

  // The BCP-47 language code of the source language.
  string source_language_code = 3;

  // The BCP-47 language code of the target language.
  string target_language_code = 4;

  // The number of examples in the dataset.
  int32 example_count = 5;

  // Output only. Timestamp when this dataset was created.
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this dataset was last updated.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

//  Request message for creating an AdaptiveMtDataset.
message CreateAdaptiveMtDatasetRequest {
  // Required. Name of the parent project. In form of
  // `projects/{project-number-or-id}/locations/{location-id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The AdaptiveMtDataset to be created.
  AdaptiveMtDataset adaptive_mt_dataset = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for deleting an AdaptiveMtDataset.
message DeleteAdaptiveMtDatasetRequest {
  // Required. Name of the dataset. In the form of
  // `projects/{project-number-or-id}/locations/{location-id}/adaptiveMtDatasets/{adaptive-mt-dataset-id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtDataset"
    }
  ];
}

// Request message for getting an Adaptive MT dataset.
message GetAdaptiveMtDatasetRequest {
  // Required. Name of the dataset. In the form of
  // `projects/{project-number-or-id}/locations/{location-id}/adaptiveMtDatasets/{adaptive-mt-dataset-id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtDataset"
    }
  ];
}

// Request message for listing all Adaptive MT datasets that the requestor has
// access to.
message ListAdaptiveMtDatasetsRequest {
  // Required. The resource name of the project from which to list the Adaptive
  // MT datasets. `projects/{project-number-or-id}/locations/{location-id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Requested page size. The server may return fewer results than
  // requested. If unspecified, the server picks an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  // Typically, this is the value of
  // ListAdaptiveMtDatasetsResponse.next_page_token returned from the
  // previous call to `ListAdaptiveMtDatasets` method. The first page is
  // returned if `page_token`is empty or missing.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An expression for filtering the results of the request.
  // Filter is not supported yet.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A list of AdaptiveMtDatasets.
message ListAdaptiveMtDatasetsResponse {
  // Output only. A list of Adaptive MT datasets.
  repeated AdaptiveMtDataset adaptive_mt_datasets = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A token to retrieve a page of results. Pass this value in the
  // [ListAdaptiveMtDatasetsRequest.page_token] field in the subsequent call to
  // `ListAdaptiveMtDatasets` method to retrieve the next page of results.
  string next_page_token = 2 [(google.api.field_behavior) = OPTIONAL];
}

// The request for sending an AdaptiveMt translation query.
message AdaptiveMtTranslateRequest {
  // A pair of sentences used as reference in source and target languages.
  message ReferenceSentencePair {
    // Source sentence in the sentence pair.
    string source_sentence = 1;

    // Target sentence in the sentence pair.
    string target_sentence = 2;
  }

  // A list of reference sentence pairs.
  message ReferenceSentencePairList {
    // Reference sentence pairs.
    repeated ReferenceSentencePair reference_sentence_pairs = 1;
  }

  // Message of caller-provided reference configuration.
  message ReferenceSentenceConfig {
    // Reference sentences pair lists. Each list will be used as the references
    // to translate the sentence under "content" field at the corresponding
    // index. Length of the list is required to be equal to the length of
    // "content" field.
    repeated ReferenceSentencePairList reference_sentence_pair_lists = 1;

    // Source language code.
    string source_language_code = 2;

    // Target language code.
    string target_language_code = 3;
  }

  // Configures which glossary is used for a specific target language and
  // defines
  // options for applying that glossary.
  message GlossaryConfig {
    // Required. The `glossary` to be applied for this translation.
    //
    // The format depends on the glossary:
    //
    // - User-provided custom glossary:
    //   `projects/{project-number-or-id}/locations/{location-id}/glossaries/{glossary-id}`
    string glossary = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "translate.googleapis.com/Glossary"
      }
    ];

    // Optional. Indicates match is case insensitive. The default value is
    // `false` if missing.
    bool ignore_case = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If set to true, the glossary will be used for contextual
    // translation.
    bool contextual_translation_enabled = 4
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. Location to make a regional call.
  //
  // Format: `projects/{project-number-or-id}/locations/{location-id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The resource name for the dataset to use for adaptive MT.
  // `projects/{project}/locations/{location-id}/adaptiveMtDatasets/{dataset}`
  string dataset = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtDataset"
    }
  ];

  // Required. The content of the input in string format.
  repeated string content = 3 [(google.api.field_behavior) = REQUIRED];

  // Configuration for caller provided reference sentences.
  optional ReferenceSentenceConfig reference_sentence_config = 6;

  // Optional. Glossary to be applied. The glossary must be
  // within the same region (have the same location-id) as the model, otherwise
  // an INVALID_ARGUMENT (400) error is returned.
  optional GlossaryConfig glossary_config = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// An AdaptiveMt translation.
message AdaptiveMtTranslation {
  // Output only. The translated text.
  string translated_text = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An AdaptiveMtTranslate response.
message AdaptiveMtTranslateResponse {
  // Output only. The translation.
  repeated AdaptiveMtTranslation translations = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The translation's language code.
  string language_code = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Text translation response if a glossary is provided in the request. This
  // could be the same as 'translation' above if no terms apply.
  repeated AdaptiveMtTranslation glossary_translations = 4;
}

// An AdaptiveMtFile.
message AdaptiveMtFile {
  option (google.api.resource) = {
    type: "translate.googleapis.com/AdaptiveMtFile"
    pattern: "projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}"
    plural: "adaptiveMtFiles"
    singular: "adaptiveMtFile"
  };

  // Required. The resource name of the file, in form of
  // `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtFile"
    }
  ];

  // The file's display name.
  string display_name = 2;

  // The number of entries that the file contains.
  int32 entry_count = 3;

  // Output only. Timestamp when this file was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this file was last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request for getting an AdaptiveMtFile.
message GetAdaptiveMtFileRequest {
  // Required. The resource name of the file, in form of
  // `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtFile"
    }
  ];
}

// The request for deleting an AdaptiveMt file.
message DeleteAdaptiveMtFileRequest {
  // Required. The resource name of the file to delete, in form of
  // `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtFile"
    }
  ];
}

// The request for importing an AdaptiveMt file along with its sentences.
message ImportAdaptiveMtFileRequest {
  // Required. The resource name of the file, in form of
  // `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtDataset"
    }
  ];

  // The source for the document.
  oneof source {
    // Inline file source.
    FileInputSource file_input_source = 2;

    // Google Cloud Storage file source.
    GcsInputSource gcs_input_source = 3;
  }
}

// The response for importing an AdaptiveMtFile
message ImportAdaptiveMtFileResponse {
  // Output only. The Adaptive MT file that was imported.
  AdaptiveMtFile adaptive_mt_file = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request to list all AdaptiveMt files under a given dataset.
message ListAdaptiveMtFilesRequest {
  // Required. The resource name of the project from which to list the Adaptive
  // MT files.
  // `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtDataset"
    }
  ];

  // Optional.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  // Typically, this is the value of
  // ListAdaptiveMtFilesResponse.next_page_token returned from the
  // previous call to `ListAdaptiveMtFiles` method. The first page is
  // returned if `page_token`is empty or missing.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response for listing all AdaptiveMt files under a given dataset.
message ListAdaptiveMtFilesResponse {
  // Output only. The Adaptive MT files.
  repeated AdaptiveMtFile adaptive_mt_files = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A token to retrieve a page of results. Pass this value in the
  // ListAdaptiveMtFilesRequest.page_token field in the subsequent call to
  // `ListAdaptiveMtFiles` method to retrieve the next page of results.
  string next_page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// An AdaptiveMt sentence entry.
message AdaptiveMtSentence {
  option (google.api.resource) = {
    type: "translate.googleapis.com/AdaptiveMtSentence"
    pattern: "projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}/adaptiveMtSentences/{sentence}"
    plural: "adaptiveMtSentences"
    singular: "adaptiveMtSentence"
  };

  // Required. The resource name of the file, in form of
  // `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}/adaptiveMtSentences/{sentence}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtSentence"
    }
  ];

  // Required. The source sentence.
  string source_sentence = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The target sentence.
  string target_sentence = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp when this sentence was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this sentence was last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The request for listing Adaptive MT sentences from a Dataset/File.
message ListAdaptiveMtSentencesRequest {
  // Required. The resource name of the project from which to list the Adaptive
  // MT files. The following format lists all sentences under a file.
  // `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
  // The following format lists all sentences within a dataset.
  // `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/AdaptiveMtFile"
    }
  ];

  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  // Typically, this is the value of
  // ListAdaptiveMtSentencesRequest.next_page_token returned from the
  // previous call to `ListTranslationMemories` method. The first page is
  // returned if `page_token` is empty or missing.
  string page_token = 3;
}

// List AdaptiveMt sentences response.
message ListAdaptiveMtSentencesResponse {
  // Output only. The list of AdaptiveMtSentences.
  repeated AdaptiveMtSentence adaptive_mt_sentences = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional.
  string next_page_token = 2 [(google.api.field_behavior) = OPTIONAL];
}
