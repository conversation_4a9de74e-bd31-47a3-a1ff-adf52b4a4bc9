type: google.api.Service
config_version: 3
name: translate.googleapis.com
title: Cloud Translation API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.translation.v3.TranslationService
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

documentation:
  summary: Integrates text translation into your website or application.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v3/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v3/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v3/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v3/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v3/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v3/{name=projects/*/locations/*}/operations'
  - selector: google.longrunning.Operations.WaitOperation
    post: '/v3/{name=projects/*/locations/*/operations/*}:wait'
    body: '*'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-translation
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-translation
  - selector: 'google.cloud.translation.v3.TranslationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-translation
  - selector: google.cloud.translation.v3.TranslationService.BatchTranslateDocument
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.translation.v3.TranslationService.BatchTranslateText
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.translation.v3.TranslationService.CreateGlossary
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-translation
