// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.translation.v3;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Translate.V3";
option go_package = "cloud.google.com/go/translate/apiv3/translatepb;translatepb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.cloud.translate.v3";
option php_namespace = "Google\\Cloud\\Translate\\V3";
option ruby_package = "Google::Cloud::Translate::V3";

// The Google Cloud Storage location for the input content.
message GcsInputSource {
  // Required. Source data URI. For example, `gs://my_bucket/my_object`.
  string input_uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// An inlined file.
message FileInputSource {
  // Required. The file's mime type.
  string mime_type = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The file's byte contents.
  bytes content = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The file's display name.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];
}

// The Google Cloud Storage location for the output content.
message GcsOutputDestination {
  // Required. Google Cloud Storage URI to output directory. For example,
  // `gs://bucket/directory`. The requesting user must have write permission to
  // the bucket. The directory will be created if it doesn't exist.
  string output_uri_prefix = 1 [(google.api.field_behavior) = REQUIRED];
}

// Possible states of long running operations.
enum OperationState {
  // Invalid.
  OPERATION_STATE_UNSPECIFIED = 0;

  // Request is being processed.
  OPERATION_STATE_RUNNING = 1;

  // The operation was successful.
  OPERATION_STATE_SUCCEEDED = 2;

  // Failed to process operation.
  OPERATION_STATE_FAILED = 3;

  // Request is in the process of being canceled after caller invoked
  // longrunning.Operations.CancelOperation on the request id.
  OPERATION_STATE_CANCELLING = 4;

  // The operation request was successfully canceled.
  OPERATION_STATE_CANCELLED = 5;
}

// Represents a single entry in a glossary.
message GlossaryEntry {
  option (google.api.resource) = {
    type: "translate.googleapis.com/GlossaryEntry"
    pattern: "projects/{project}/locations/{location}/glossaries/{glossary}/glossaryEntries/{glossary_entry}"
    plural: "glossaryEntries"
    singular: "glossaryEntry"
  };

  // Represents a single entry for an unidirectional glossary.
  message GlossaryTermsPair {
    // The source term is the term that will get match in the text,
    GlossaryTerm source_term = 1;

    // The term that will replace the match source term.
    GlossaryTerm target_term = 2;
  }

  // Represents a single entry for an equivalent term set glossary. This is used
  // for equivalent term sets where each term can be replaced by the other terms
  // in the set.
  message GlossaryTermsSet {
    // Each term in the set represents a term that can be replaced by the other
    // terms.
    repeated GlossaryTerm terms = 1;
  }

  // Identifier. The resource name of the entry.
  // Format:
  //   `projects/*/locations/*/glossaries/*/glossaryEntries/*`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The different data for the glossary types (Unidirectional, Equivalent term
  // sets).
  oneof data {
    // Used for an unidirectional glossary.
    GlossaryTermsPair terms_pair = 2;

    // Used for an equivalent term sets glossary.
    GlossaryTermsSet terms_set = 3;
  }

  // Describes the glossary entry.
  string description = 4;
}

// Represents a single glossary term
message GlossaryTerm {
  // The language for this glossary term.
  string language_code = 1;

  // The text for the glossary term.
  string text = 2;
}
