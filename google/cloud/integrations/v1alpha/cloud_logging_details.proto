// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.integrations.v1alpha;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Integrations.V1Alpha";
option go_package = "cloud.google.com/go/integrations/apiv1alpha/integrationspb;integrationspb";
option java_multiple_files = true;
option java_outer_classname = "CloudLoggingDetailsProto";
option java_package = "com.google.cloud.integrations.v1alpha";

// Cloud Logging details for execution info
message CloudLoggingDetails {
  // The severity will be mapped to the Integration Execution State.
  enum CloudLoggingSeverity {
    // Unspecified
    CLOUD_LOGGING_SEVERITY_UNSPECIFIED = 0;

    // If Severity selected is `INFO`, then all the Integration Execution States
    // (`IN_PROCESS`, `ON_HOLD`, `SUCCEEDED`, `SUSPENDED`, `ERROR`, `CANCELLED`)
    // will be sent to Cloud Logging.
    INFO = 2;

    // If Severity selected is `ERROR`, then only the following Integration
    // Execution States (`ERROR`, `CANCELLED`) will be sent to Cloud Logging.
    ERROR = 3;

    // If Severity selected is `WARNING`, then only the following Integration
    // Execution States (`ERROR`, `CANCELLED`) will be sent to Cloud Logging.
    WARNING = 4;
  }

  // Optional. Severity selected by the customer for the logs to be sent to
  // Cloud Logging, for the integration version getting executed.
  optional CloudLoggingSeverity cloud_logging_severity = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Status of whether Cloud Logging is enabled or not for the
  // integration version getting executed.
  optional bool enable_cloud_logging = 2
      [(google.api.field_behavior) = OPTIONAL];
}
