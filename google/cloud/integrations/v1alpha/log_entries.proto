// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.integrations.v1alpha;

import "google/api/field_behavior.proto";
import "google/cloud/integrations/v1alpha/cloud_logging_details.proto";
import "google/cloud/integrations/v1alpha/event_parameter.proto";
import "google/cloud/integrations/v1alpha/integration_state.proto";
import "google/cloud/integrations/v1alpha/task_config.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Integrations.V1Alpha";
option go_package = "cloud.google.com/go/integrations/apiv1alpha/integrationspb;integrationspb";
option java_multiple_files = true;
option java_outer_classname = "LogEntriesProto";
option java_package = "com.google.cloud.integrations.v1alpha";

// Log entry to log execution info for the monitored resource
// `integrations.googleapis.com/IntegrationVersion`.
message ExecutionInfo {
  // ExecutionMethod Enum
  enum ExecutionMethod {
    // Default value.
    EXECUTION_METHOD_UNSPECIFIED = 0;

    // Sync post.
    POST = 1;

    // Async post with schedule time.
    SCHEDULE = 2;

    // Async post.
    POST_TO_QUEUE = 3;
  }

  // Contains the details of the execution info: this includes the replay reason
  // and replay tree connecting executions in a parent-child relationship
  message ReplayInfo {
    // If this execution is a replay of another execution, then this field
    // contains the original execution id.
    string original_execution_info_id = 1;

    // If this execution has been replayed, then this field contains the
    // execution ids of the replayed executions.
    repeated string replayed_execution_info_ids = 2;

    // reason for replay
    string replay_reason = 3;
  }

  // Name of the integration.
  string integration = 2;

  // The customer's project number.
  string project_id = 4;

  // The trigger id of the integration trigger config. If both trigger_id
  // and client_id is present, the integration is executed from the start tasks
  // provided by the matching trigger config otherwise it is executed from the
  // default start tasks.
  string trigger_id = 5;

  // Execution parameters come in as part of the request.
  map<string, EventParameter> request_params = 6;

  // Execution parameters come out as part of the response.
  map<string, EventParameter> response_params = 7;

  // Errors, warnings, and informationals associated with the workflow/task.
  // The order in which the errors were added by the workflow/task is
  // maintained.
  repeated ErrorDetail errors = 10;

  // The configuration details for a task.
  repeated TaskConfig task_configs = 13;

  // Pointer to the active version it is executing.
  string integration_version_number = 14;

  // Auto-generated primary key.
  string execution_id = 15;

  // Output only. State of the integration version
  IntegrationState integration_version_state = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Database persistence policy for execution info
  bool enable_database_persistence = 17;

  // Cloud Logging details for execution info
  CloudLoggingDetails cloud_logging_details = 18;

  // The details about this integration execution.
  IntegrationExecutionDetails integration_execution_details = 19;

  // Specifies whether this execution info corresponds to actual integration or
  // test case.
  ExecutionType execution_type = 20;

  // The ways user posts this event.
  ExecutionMethod execution_method = 21;

  // An increasing sequence that is set when a new snapshot (Integration
  // Version) is created.
  int64 integration_snapshot_number = 22;

  // Replay info for the execution
  ReplayInfo replay_info = 23;
}

// Contains the details of the execution info: this includes the tasks execution
// details plus the integration execution statistics.
message IntegrationExecutionDetails {
  // Enum ExecutionState.
  enum IntegrationExecutionState {
    // Default value.
    INTEGRATION_EXECUTION_STATE_UNSPECIFIED = 0;

    // Integration is received and waiting for the execution. This happens when
    // firing the Integration via "postToQueue" or "schedule".
    ON_HOLD = 1;

    // Integration is under processing.
    IN_PROCESS = 2;

    // Integration execution successfully finished. There's no more change after
    // this state.
    SUCCEEDED = 3;

    // Integration execution failed. There's no more change after this state.
    FAILED = 4;

    // Integration execution canceled by user. There's no more change after this
    // state.
    CANCELLED = 5;

    // Integration execution failed and waiting for retry.
    RETRY_ON_HOLD = 6;

    // Integration execution suspended and waiting for manual intervention.
    SUSPENDED = 7;
  }

  // Output only. The execution state of this Integration.
  IntegrationExecutionState integration_execution_state = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Execution snapshot.
  repeated IntegrationExecutionSnapshot integration_execution_snapshot = 2;

  // Status for the current execution attempt.
  repeated AttemptStats execution_attempt_stats = 3;

  // Next scheduled execution time in case the execution status was
  // RETRY_ON_HOLD.
  google.protobuf.Timestamp next_execution_time = 4;

  // Indicates the number of times the execution has restarted from the
  // beginning.
  int32 execution_retries_count = 5;

  // If the execution is manually canceled, this field will contain the reason
  // for cancellation.
  string cancel_reason = 6;
}

// Contains the snapshot of the integration execution for a given checkpoint.
message IntegrationExecutionSnapshot {
  // Metadata for the integration/task retry.
  message IntegrationExecutionSnapshotMetadata {
    // The task number associated with this snapshot. Could be empty.
    string task_number = 1;

    // the task name associated with this snapshot. Could be empty.
    string task = 2;

    // the integration execution attempt number this snapshot belongs to.
    int32 integration_execution_attempt_num = 3;

    // the task attempt number this snapshot belongs to. Could be empty.
    int32 task_attempt_num = 4;

    // the task label associated with this snapshot. Could be empty.
    string task_label = 5;

    // Ancestor task number for the task(it will only be non-empty if the task
    // is under 'private workflow')
    repeated string ancestor_task_numbers = 6;

    // Ancestor iteration number for the task(it will only be non-empty if the
    // task is under 'private workflow')
    repeated string ancestor_iteration_numbers = 7;

    // The direct integration which the event execution snapshots belongs to
    string integration = 8;
  }

  // Indicates "right after which checkpoint task's execution" this snapshot
  // is taken.
  string checkpoint_task_number = 1;

  // Indicates when this snapshot is taken.
  google.protobuf.Timestamp snapshot_time = 2;

  // Snapshot metadata.
  IntegrationExecutionSnapshotMetadata integration_execution_snapshot_metadata =
      3;

  // All of the task execution details at the given point of time.
  repeated TaskExecutionDetails task_execution_details = 4;

  // All of the computed conditions that been calculated.
  repeated ConditionResult condition_results = 5;

  // The parameters in Event object.
  map<string, EventParameter> execution_params = 6;
}

// Contains the details of the execution of this task.
message TaskExecutionDetails {
  // Enum TaskExecutionState.
  enum TaskExecutionState {
    // Default value.
    TASK_EXECUTION_STATE_UNSPECIFIED = 0;

    // Task is waiting for its precondition tasks to finish to start the
    // execution.
    PENDING_EXECUTION = 1;

    // Task is under processing.
    IN_PROCESS = 2;

    // Task execution successfully finished. There's no more change after
    // this state.
    SUCCEED = 3;

    // Task execution failed. There's no more change after this state.
    FAILED = 4;

    // Task execution failed and cause the whole integration execution to fail
    // immediately. There's no more change after this state.
    FATAL = 5;

    // Task execution failed and waiting for retry.
    RETRY_ON_HOLD = 6;

    // Task execution skipped. This happens when its precondition wasn't met,
    // or the integration execution been canceled before reach to the task.
    // There's no more changes after this state.
    SKIPPED = 7;

    // Task execution canceled when in progress. This happens when integration
    // execution been canceled or any other task fall in fatal state.
    CANCELLED = 8;

    // Task is waiting for its dependency tasks' rollback to finish to start
    // its rollback.
    PENDING_ROLLBACK = 9;

    // Task is rolling back.
    ROLLBACK_IN_PROCESS = 10;

    // Task is rolled back. This is the state we will set regardless of
    // rollback succeeding or failing.
    ROLLEDBACK = 11;

    // Task is a SuspensionTask which has executed once, creating a pending
    // suspension.
    SUSPENDED = 12;
  }

  // Pointer to the task config it used for execution.
  string task_number = 1;

  // The execution state of this task.
  TaskExecutionState task_execution_state = 2;

  // Status for the current task execution attempt.
  repeated AttemptStats task_attempt_stats = 3;
}

// Status for the execution attempt.
message AttemptStats {
  // The start time of the integration execution for current attempt. This could
  // be in the future if it's been scheduled.
  google.protobuf.Timestamp start_time = 1;

  // The end time of the integration execution for current attempt.
  google.protobuf.Timestamp end_time = 2;
}

// An error, warning, or information message associated with an integration.
message ErrorDetail {
  // The full text of the error message, including any parameters that were
  // thrown along with the exception.
  string error_message = 1;

  // The task try-number, in which, the error occurred.  If zero, the error
  // happened at the integration level.
  int32 task_number = 2;
}

// Contains the combined condition calculation results.
message ConditionResult {
  // the current task number.
  string current_task_number = 1;

  // the next task number.
  string next_task_number = 2;

  // the result comes out after evaluate the combined condition. True if there's
  // no combined condition specified.
  bool result = 3;
}

// Specifies whether this execution info corresponds to actual integration or
// test case.
enum ExecutionType {
  // Unspecified value.
  EXECUTION_TYPE_UNSPECIFIED = 0;

  // Execution corresponds to run of an integration version.
  INTEGRATION_VERSION = 1;

  // Execution corresponds to run of a functional test case.
  TEST_CASE = 2;
}
