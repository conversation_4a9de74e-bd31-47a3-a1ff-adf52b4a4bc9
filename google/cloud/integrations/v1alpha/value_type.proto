// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.integrations.v1alpha;

option csharp_namespace = "Google.Cloud.Integrations.V1Alpha";
option go_package = "cloud.google.com/go/integrations/apiv1alpha/integrationspb;integrationspb";
option java_multiple_files = true;
option java_outer_classname = "ValueTypeProto";
option java_package = "com.google.cloud.integrations.v1alpha";

// The type of the parameter.
message ValueType {
  oneof value {
    // String.
    string string_value = 1;

    // Integer.
    int64 int_value = 2;

    // Double Number.
    double double_value = 3;

    // Boolean.
    bool boolean_value = 4;

    // String Array.
    StringParameterArray string_array = 5;

    // Integer Array.
    IntParameterArray int_array = 6;

    // Double Number Array.
    DoubleParameterArray double_array = 7;

    // Boolean Array.
    BooleanParameterArray boolean_array = 8;

    // Json.
    string json_value = 9;
  }
}

// This message only contains a field of string array.
message StringParameterArray {
  // String array.
  repeated string string_values = 1;
}

// This message only contains a field of integer array.
message IntParameterArray {
  // Integer array.
  repeated int64 int_values = 1;
}

// This message only contains a field of double number array.
message DoubleParameterArray {
  // Double number array.
  repeated double double_values = 1;
}

// This message only contains a field of boolean array.
message BooleanParameterArray {
  // Boolean array.
  repeated bool boolean_values = 1;
}
