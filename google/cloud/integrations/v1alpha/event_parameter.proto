// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.integrations.v1alpha;

import "google/cloud/integrations/v1alpha/value_type.proto";

option csharp_namespace = "Google.Cloud.Integrations.V1Alpha";
option go_package = "cloud.google.com/go/integrations/apiv1alpha/integrationspb;integrationspb";
option java_multiple_files = true;
option java_outer_classname = "EventParameterProto";
option java_package = "com.google.cloud.integrations.v1alpha";

// This message is used for processing and persisting (when applicable) key
// value pair parameters for each event in the event bus.
// Next available id: 4
message EventParameter {
  // Key is used to retrieve the corresponding parameter value. This should be
  // unique for a given fired event. These parameters must be predefined in the
  // integration definition.
  string key = 1;

  // Values for the defined keys. Each value can either be string, int, double
  // or any proto message.
  ValueType value = 2;

  // True if this parameter should be masked in the logs
  bool masked = 3;
}
