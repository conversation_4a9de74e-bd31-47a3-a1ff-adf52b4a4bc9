# This file was automatically generated by BuildFileGenerator

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "integrations_proto",
    srcs = [
        "cloud_logging_details.proto",
        "coordinate.proto",
        "event_parameter.proto",
        "integration_state.proto",
        "json_validation.proto",
        "log_entries.proto",
        "product.proto",
        "task_config.proto",
        "value_type.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_proto_library",
    "java_gapic_assembly_gradle_pkg",
)

java_proto_library(
    name = "integrations_java_proto",
    deps = [":integrations_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-integrations-v1alpha-java",
    deps = [
        ":integrations_proto",
        ":integrations_java_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
    "go_gapic_assembly_pkg",
)

go_proto_library(
    name = "integrations_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/integrations/apiv1alpha/integrationspb",
    protos = [":integrations_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_assembly_pkg(
    name = "google-cloud-integrations-v1alpha-go",
    deps = [
        ":integrations_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
    "py_gapic_library",
    "py_gapic_assembly_pkg",
)

moved_proto_library(
    name = "integrations_moved_proto",
    srcs = [":integrations_proto"],
    deps = [
        "//google/api:field_behavior_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "integrations_py_proto",
    deps = [":integrations_moved_proto"],
)

py_grpc_library(
    name = "integrations_py_grpc",
    srcs = [":integrations_moved_proto"],
    deps = [":integrations_py_proto"],
)

py_gapic_library(
    name = "integrations_py_gapic",
    srcs = [":integrations_proto"],
    rest_numeric_enums = False,
    transport = "grpc+rest",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-integrations-v1alpha-py",
    deps = [
        ":integrations_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "integrations_php_proto",
    deps = [":integrations_proto"],
)

php_gapic_assembly_pkg(
    name = "google-cloud-integrations-v1alpha-php",
    deps = [
        ":integrations_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "integrations_ruby_proto",
    deps = [":integrations_proto"],
)

ruby_grpc_library(
    name = "integrations_ruby_grpc",
    srcs = [":integrations_proto"],
    deps = [":integrations_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_proto_library",
    "csharp_gapic_assembly_pkg",
)

csharp_proto_library(
    name = "integrations_csharp_proto",
    deps = [":integrations_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-integrations-v1alpha-csharp",
    package_name = "Google.Cloud.Integrations.V1Alpha",
    generate_nongapic_package = True,
    deps = [
        ":integrations_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "integrations_cc_proto",
    deps = [":integrations_proto"],
)

cc_grpc_library(
    name = "integrations_cc_grpc",
    srcs = [":integrations_proto"],
    grpc_only = True,
    deps = [":integrations_cc_proto"],
)
