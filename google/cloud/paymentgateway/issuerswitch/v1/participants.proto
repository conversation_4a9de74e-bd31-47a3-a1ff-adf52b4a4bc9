// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.paymentgateway.issuerswitch.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/paymentgateway/issuerswitch/v1/common_fields.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.PaymentGateway.IssuerSwitch.V1";
option go_package = "cloud.google.com/go/paymentgateway/issuerswitch/apiv1/issuerswitchpb;issuerswitchpb";
option java_multiple_files = true;
option java_outer_classname = "ParticipantsProto";
option java_package = "com.google.cloud.paymentgateway.issuerswitch.v1";
option php_namespace = "Google\\Cloud\\PaymentGateway\\IssuerSwitch\\V1";
option ruby_package = "Google::Cloud::PaymentGateway::IssuerSwitch::V1";

// A service that allows for the management of participants in the issuer
// switch.
service IssuerSwitchParticipants {
  option (google.api.default_host) = "issuerswitch.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Fetch the issuer switch participant. This method can be used to retrieve
  // all details of a participant in the issuer switch.
  //
  // In UPI, the participant is identified by their account's IFSC and their
  // account number.
  rpc FetchParticipant(FetchParticipantRequest) returns (IssuerParticipant) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/issuerParticipants:fetch"
      body: "*"
    };
  }

  // Update the issuer switch participant. Currently, this API only allows for
  // the
  // [metadata][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.metadata]
  // field to be updated.
  //
  // The `number` of key-value pairs in the `metadata` field, the length of each
  // `key` and the length of each `value` should be within the thresholds
  // defined for them in the issuer switch configuration. Any violation of these
  // thresholds will cause this API to return an error. The default values for
  // these thresholds are:
  //
  // * `Maximum number` of key-value pairs - `5`
  // * `Maximum length` of a key - `100`
  // * `Maximum length` of a value - `500`
  //
  // **Note** that this method replaces any existing `metadata` field value in
  // the participant with the new value. Specifically, it does not do a merge.
  // If key-value pairs are to be added/removed from the metadata, then
  // callers must follow the following steps:
  //
  // 1. Invoke the
  //   [FetchParticipant][google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants.FetchParticipant]
  //    API to get the current value of the `metadata` field.
  // 1. Update the `metadata` map to add/remove key-value pairs from it.
  // 1. Update the `metadata` in the issuer switch using this method.
  rpc UpdateIssuerParticipant(UpdateIssuerParticipantRequest)
      returns (IssuerParticipant) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/issuerParticipants:update"
      body: "issuer_participant"
    };
    option (google.api.method_signature) =
        "parent,issuer_participant,update_mask";
  }

  // Activate the issuer switch participant for UPI transactions. This API
  // sets the state of the participant to
  // [ACTIVE][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.ACTIVE].
  // A participant in the `ACTIVE` state can perform all UPI operations
  // normally.
  //
  // The behavior of this API varies based on the current state of the
  // participant.
  //
  // *   Current state is
  //     [ACTIVE][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.ACTIVE]
  //     : This API will make no change to the participant's state and returns a
  //     successful response.
  // *    Current state is
  //     [INACTIVE][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.INACTIVE]
  //     : If an _MPIN_ has already been provisioned for the participant, then
  //     this API will change the state of the participant to `ACTIVE`. Else,
  //     this API will return an error.
  // *   Current state is
  //     [MOBILE_NUMBER_CHANGED][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.MOBILE_NUMBER_CHANGED]
  //     : The state cannot be changed to `ACTIVE`. This API will return an
  //     error.
  // *   Current state is
  //     [NEW_REGISTRATION_INITIATED][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.NEW_REGISTRATION_INITIATED]
  //     : The state cannot be changed to `ACTIVE`. This API will return an
  //     error.
  // *   Current state is
  //     [RE_REGISTRATION_INITIATED][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.RE_REGISTRATION_INITIATED]
  //     : The state cannot be changed to `ACTIVE`. This API will return an
  //     error.
  rpc ActivateParticipant(ParticipantStateChangeRequest)
      returns (IssuerParticipants) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/issuerParticipants:activate"
      body: "*"
    };
  }

  // Deactivate the issuer switch participant for UPI transactions. This API
  // sets the state of the participant to
  // [INACTIVE][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.INACTIVE].
  // An `INACTIVE` participant cannot perform any UPI operation which involves
  // MPIN verification.
  //
  // The behavior of this API varies based on the current state of the
  // participant.
  //
  // *   Current state is
  //     [ACTIVE][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.ACTIVE]
  //     : The state will change to `INACTIVE`. The user will be forced to
  //     re-register with UPI and reset their MPIN  to perform any UPI
  //     operations.
  // *   Current state is
  //     [INACTIVE][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.INACTIVE]
  //     : This API will make no change to the participant's state and returns a
  //     successful response.
  // *   Current state is
  //     [MOBILE_NUMBER_CHANGED][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.MOBILE_NUMBER_CHANGED]
  //     : The state cannot be changed to `INACTIVE`. This API will return an
  //     error.
  // *   Current state is
  //     [NEW_REGISTRATION_INITIATED][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.NEW_REGISTRATION_INITIATED]
  //     : The state cannot be changed to `INACTIVE`. This API will return an
  //     error.
  // *   Current state is
  //     [RE_REGISTRATION_INITIATED][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.RE_REGISTRATION_INITIATED]
  //     : The state cannot be changed to `INACTIVE`. This API will return an
  //     error.
  rpc DeactivateParticipant(ParticipantStateChangeRequest)
      returns (IssuerParticipants) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/issuerParticipants:deactivate"
      body: "*"
    };
  }

  // Mark the state of the issuer switch participant as _mobile number changed_
  // to prevent UPI transactions by the user. This API sets the state of the
  // participant to
  // [MOBILE_NUMBER_CHANGED][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.MOBILE_NUMBER_CHANGED].
  //
  // Any UPI operation for a participant in the `MOBILE_NUMBER_CHANGED` state
  // will cause the issuer switch to return a `B1` error to the UPI payments
  // orchestrator which would force the user to re-register with UPI.
  //
  // The behavior of this API varies based on the current state of the
  // participant.
  //
  // *   Current state is
  //     [ACTIVE][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.ACTIVE]
  //     : The state will change to `MOBILE_NUMBER_CHANGED`. Any operation
  //     involving MPIN verification of the participant will return a `B1` error
  //     to the UPI payments orchestrator. The user will be forced to
  //     re-register with their changed mobile number.
  // *   Current state is
  //     [INACTIVE][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.INACTIVE]
  //     : The state will change to `MOBILE_NUMBER_CHANGED`. Any operation
  //     involving MPIN verification of the participant will return a `B1` error
  //     to the UPI payments orchestrator. The user will be forced to
  //     re-register with their changed mobile number.
  // *   Current state is
  //     [MOBILE_NUMBER_CHANGED][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.MOBILE_NUMBER_CHANGED]
  //     : This API will make no change to the participant's state and returns a
  //     successful response.
  // *   Current state is
  //     [NEW_REGISTRATION_INITIATED][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.NEW_REGISTRATION_INITIATED]
  //     : The state cannot be changed to `MOBILE_NUMBER_CHANGED`. This API will
  //     return an error.
  // *   Current state is
  //     [RE_REGISTRATION_INITIATED][google.cloud.paymentgateway.issuerswitch.v1.IssuerParticipant.State.RE_REGISTRATION_INITIATED]
  //     : The state will change to `MOBILE_NUMBER_CHANGED`. Any operation
  //     involving MPIN verification of the participant will return a `B1` error
  //     to the UPI payments orchestrator. The user will be forced to
  //     re-register with their changed mobile number.
  rpc MobileNumberChanged(ParticipantStateChangeRequest)
      returns (IssuerParticipants) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/issuerParticipants:mobileNumberChanged"
      body: "*"
    };
  }
}

// Request for the
// [FetchParticipant][google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants.FetchParticipant]
// method.
message FetchParticipantRequest {
  // Required. The parent resource for the participants. The format is
  // `projects/{project}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The account details of the issuer participant.
  AccountReference account_reference = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A customer of the bank who participates in transactions processed by the
// issuer switch.
message IssuerParticipant {
  // The state of the participant.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The participant is inactive for all UPI transactions. The issuer switch
    // will return the `AM` error to the UPI payments orchestrator for any
    // operation involving MPIN verification for the participant. They need to
    // register with UPI again and provide a new MPIN.
    INACTIVE = 1;

    // The participant is active for all UPI transactions.
    ACTIVE = 2;

    // The participants MPIN has been locked because they have exceeded the
    // threshold for maximum number of incorrect MPIN verification attempts. No
    // UPI transactions will be permitted until the participant's MPIN has been
    // reset.
    MPIN_LOCKED = 3;

    // The participants mobile number has been changed in the issuer bank. Any
    // transaction involving MPIN verification of the participant will return a
    // `B1` error to the UPI payments orchestrator. The user will be forced to
    // re-register with their changed mobile number.
    MOBILE_NUMBER_CHANGED = 4;

    // The participant is registering for UPI transactions for the first time.
    NEW_REGISTRATION_INITIATED = 5;

    // The participant had already registered for UPI transactions but is now
    // registering again or resetting their MPIN.
    RE_REGISTRATION_INITIATED = 6;
  }

  // The metadata of the participant.
  message Metadata {
    // Optional. Additional metadata about a particular participant as key-value
    // pairs. These values are returned by the bank adapter/card adapter in
    // response to the SearchAccounts/InitiateRegistration APIs.
    map<string, string> values = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. The account details of the issuer participant. Only the
  // account_number and ifsc fields will be used.
  AccountReference account_reference = 1
      [(google.api.field_behavior) = REQUIRED];

  // Output only. The mobile number of the participant.
  string mobile_number = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the participant.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Additional metadata about the participant.
  Metadata metadata = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The current count of consecutive incorrect MPIN attempts.
  int32 mpin_failure_count = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when participant's MPIN got locked due to too many
  // incorrect attempts.
  google.protobuf.Timestamp mpin_locked_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the participant's account was onboarded to PGIS.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the participant was last updated.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request for the
// [UpdateIssuerParticipant][google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants.UpdateIssuerParticipant]
// method.
message UpdateIssuerParticipantRequest {
  // Required. The parent resource for the participants. The format is
  // `projects/{project}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The participant to update.
  IssuerParticipant issuer_participant = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the
// [ActivateParticipant][google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants.ActivateParticipant],
// [DeactivateParticipant][google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants.DeactivateParticipant]
// and
// [MobileNumberUpdated][google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants.MobileNumberChanged]
// methods.
message ParticipantStateChangeRequest {
  // Required. The parent resource for the participant. The format is
  // `projects/{project}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // The identifier for the issuer participant. One of the two values must be
  // specified.
  oneof id {
    // Optional. The account details of the issuer participant.
    AccountReference account_reference = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The mobile number of the issuer participant.
    string mobile_number = 3 [(google.api.field_behavior) = OPTIONAL];
  }
}

// Response for the
// [ActivateParticipant][google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants.ActivateParticipant],
// [DeactivateParticipant][google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants.DeactivateParticipant]
// and
// [MobileNumberChanged][google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants.MobileNumberChanged]
// methods.
message IssuerParticipants {
  // Output only. The list of updated participants.
  repeated IssuerParticipant participants = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
