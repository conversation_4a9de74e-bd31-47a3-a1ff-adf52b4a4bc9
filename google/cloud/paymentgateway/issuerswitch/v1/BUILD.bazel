# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "issuerswitch_proto",
    srcs = [
        "common_fields.proto",
        "logs.proto",
        "participants.proto",
        "resolutions.proto",
        "rules.proto",
        "transactions.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/logging/type:type_proto",
        "//google/longrunning:operations_proto",
        "//google/type:date_proto",
        "//google/type:latlng_proto",
        "//google/type:money_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "issuerswitch_proto_with_info",
    deps = [
        ":issuerswitch_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "issuerswitch_java_proto",
    deps = [":issuerswitch_proto"],
)

java_grpc_library(
    name = "issuerswitch_java_grpc",
    srcs = [":issuerswitch_proto"],
    deps = [":issuerswitch_java_proto"],
)

java_gapic_library(
    name = "issuerswitch_java_gapic",
    srcs = [":issuerswitch_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "issuerswitch_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "issuerswitch_v1.yaml",
    test_deps = [
        ":issuerswitch_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":issuerswitch_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "issuerswitch_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipantsClientHttpJsonTest",
        "com.google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipantsClientTest",
        "com.google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchResolutionsClientHttpJsonTest",
        "com.google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchResolutionsClientTest",
        "com.google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchRulesClientHttpJsonTest",
        "com.google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchRulesClientTest",
        "com.google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchTransactionsClientHttpJsonTest",
        "com.google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchTransactionsClientTest",
    ],
    runtime_deps = [":issuerswitch_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-paymentgateway-issuerswitch-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":issuerswitch_java_gapic",
        ":issuerswitch_java_grpc",
        ":issuerswitch_java_proto",
        ":issuerswitch_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "issuerswitch_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/paymentgateway/issuerswitch/apiv1/issuerswitchpb",
    protos = [":issuerswitch_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/logging/type:type_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:date_go_proto",
        "//google/type:latlng_go_proto",
        "//google/type:money_go_proto",
    ],
)

go_gapic_library(
    name = "issuerswitch_go_gapic",
    srcs = [":issuerswitch_proto_with_info"],
    grpc_service_config = "issuerswitch_grpc_service_config.json",
    importpath = "cloud.google.com/go/paymentgateway/issuerswitch/apiv1;issuerswitch",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = False,
    service_yaml = "issuerswitch_v1.yaml",
    transport = "grpc",
    deps = [
        ":issuerswitch_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-paymentgateway-issuerswitch-v1-go",
    deps = [
        ":issuerswitch_go_gapic",
        ":issuerswitch_go_gapic_srcjar-metadata.srcjar",
        ":issuerswitch_go_gapic_srcjar-snippets.srcjar",
        ":issuerswitch_go_gapic_srcjar-test.srcjar",
        ":issuerswitch_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "issuerswitch_py_gapic",
    srcs = [":issuerswitch_proto"],
    grpc_service_config = "issuerswitch_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=payment_gateway_issuer_switch",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-payment-gateway-issuer-switch",
    ],
    rest_numeric_enums = False,
    service_yaml = "issuerswitch_v1.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "issuerswitch_py_gapic_test",
    srcs = [
        "issuerswitch_py_gapic_pytest.py",
        "issuerswitch_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":issuerswitch_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "paymentgateway-issuerswitch-v1-py",
    deps = [
        ":issuerswitch_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "issuerswitch_php_proto",
    deps = [":issuerswitch_proto"],
)

php_gapic_library(
    name = "issuerswitch_php_gapic",
    srcs = [":issuerswitch_proto_with_info"],
    grpc_service_config = "issuerswitch_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = False,
    service_yaml = "issuerswitch_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":issuerswitch_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-paymentgateway-issuerswitch-v1-php",
    deps = [
        ":issuerswitch_php_gapic",
        ":issuerswitch_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "issuerswitch_nodejs_gapic",
    package_name = "@google-cloud/issuerswitch",
    src = ":issuerswitch_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "issuerswitch_grpc_service_config.json",
    package = "google.cloud.paymentgateway.issuerswitch.v1",
    rest_numeric_enums = False,
    service_yaml = "issuerswitch_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "paymentgateway-issuerswitch-v1-nodejs",
    deps = [
        ":issuerswitch_nodejs_gapic",
        ":issuerswitch_proto",
        "//google/logging/type:type_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "issuerswitch_ruby_proto",
    deps = [":issuerswitch_proto"],
)

ruby_grpc_library(
    name = "issuerswitch_ruby_grpc",
    srcs = [":issuerswitch_proto"],
    deps = [":issuerswitch_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "issuerswitch_ruby_gapic",
    srcs = [":issuerswitch_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-paymentgateway-issuerswitch-v1"],
    grpc_service_config = "issuerswitch_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "issuerswitch_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":issuerswitch_ruby_grpc",
        ":issuerswitch_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-paymentgateway-issuerswitch-v1-ruby",
    deps = [
        ":issuerswitch_ruby_gapic",
        ":issuerswitch_ruby_grpc",
        ":issuerswitch_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "issuerswitch_csharp_proto",
    extra_opts = [],
    deps = [":issuerswitch_proto"],
)

csharp_grpc_library(
    name = "issuerswitch_csharp_grpc",
    srcs = [":issuerswitch_proto"],
    deps = [":issuerswitch_csharp_proto"],
)

csharp_gapic_library(
    name = "issuerswitch_csharp_gapic",
    srcs = [":issuerswitch_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "issuerswitch_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "issuerswitch_v1.yaml",
    deps = [
        ":issuerswitch_csharp_grpc",
        ":issuerswitch_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-paymentgateway-issuerswitch-v1-csharp",
    deps = [
        ":issuerswitch_csharp_gapic",
        ":issuerswitch_csharp_grpc",
        ":issuerswitch_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "issuerswitch_cc_proto",
    deps = [":issuerswitch_proto"],
)

cc_grpc_library(
    name = "issuerswitch_cc_grpc",
    srcs = [":issuerswitch_proto"],
    grpc_only = True,
    deps = [":issuerswitch_cc_proto"],
)
