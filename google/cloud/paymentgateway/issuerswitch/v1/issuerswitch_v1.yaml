type: google.api.Service
config_version: 3
name: issuerswitch.googleapis.com
title: Issuer switch API

apis:
- name: google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants
- name: google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchResolutions
- name: google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchRules
- name: google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchTransactions
- name: google.longrunning.Operations

types:
- name: google.cloud.paymentgateway.issuerswitch.v1.Complaint
- name: google.cloud.paymentgateway.issuerswitch.v1.CreateComplaintMetadata
- name: google.cloud.paymentgateway.issuerswitch.v1.CreateDisputeMetadata
- name: google.cloud.paymentgateway.issuerswitch.v1.Dispute
- name: google.cloud.paymentgateway.issuerswitch.v1.ExportComplaintTransactionsMetadata
- name: google.cloud.paymentgateway.issuerswitch.v1.ExportComplaintTransactionsResponse
- name: google.cloud.paymentgateway.issuerswitch.v1.ExportFinancialTransactionsMetadata
- name: google.cloud.paymentgateway.issuerswitch.v1.ExportFinancialTransactionsResponse
- name: google.cloud.paymentgateway.issuerswitch.v1.ExportMandateTransactionsMetadata
- name: google.cloud.paymentgateway.issuerswitch.v1.ExportMandateTransactionsResponse
- name: google.cloud.paymentgateway.issuerswitch.v1.ExportMetadataTransactionsMetadata
- name: google.cloud.paymentgateway.issuerswitch.v1.ExportMetadataTransactionsResponse
- name: google.cloud.paymentgateway.issuerswitch.v1.ResolveComplaintMetadata
- name: google.cloud.paymentgateway.issuerswitch.v1.ResolveDisputeMetadata
- name: google.cloud.paymentgateway.issuerswitch.v1.UpiTransaction

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchResolutions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchRules.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchTransactions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
