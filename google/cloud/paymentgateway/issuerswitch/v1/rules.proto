// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.paymentgateway.issuerswitch.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/paymentgateway/issuerswitch/v1/common_fields.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Cloud.PaymentGateway.IssuerSwitch.V1";
option go_package = "cloud.google.com/go/paymentgateway/issuerswitch/apiv1/issuerswitchpb;issuerswitchpb";
option java_multiple_files = true;
option java_outer_classname = "RulesProto";
option java_package = "com.google.cloud.paymentgateway.issuerswitch.v1";
option php_namespace = "Google\\Cloud\\PaymentGateway\\IssuerSwitch\\V1";
option ruby_package = "Google::Cloud::PaymentGateway::IssuerSwitch::V1";

// Manages rules used by the issuer switch's rules engine.
service IssuerSwitchRules {
  option (google.api.default_host) = "issuerswitch.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // List all rules that are applied on transactions by the issuer switch. Rules
  // can be filtered on API type and transaction type.
  rpc ListRules(ListRulesRequest) returns (ListRulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/rules"
    };
    option (google.api.method_signature) = "parent";
  }

  // List all rule metadata for a given rule identifier.
  rpc ListRuleMetadata(ListRuleMetadataRequest)
      returns (ListRuleMetadataResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/rules/*}/metadata"
    };
    option (google.api.method_signature) = "parent";
  }

  // List all metadata values for a rule metadata identifier.
  rpc ListRuleMetadataValues(ListRuleMetadataValuesRequest)
      returns (ListRuleMetadataValuesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/rules/*/metadata/*}/values"
    };
    option (google.api.method_signature) = "parent";
  }

  // Create (add) multiple values to the list of values under the specified rule
  // metadata resource.
  rpc BatchCreateRuleMetadataValues(BatchCreateRuleMetadataValuesRequest)
      returns (BatchCreateRuleMetadataValuesResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/rules/*/metadata/*}/values:batchCreate"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }

  // Delete (remove) multiple values from the list of values under the specified
  // rules metadata resource.
  rpc BatchDeleteRuleMetadataValues(BatchDeleteRuleMetadataValuesRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/rules/*/metadata/*}/values:batchDelete"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }
}

// A rule that is executed by the issuer switch while processing an
// API transaction.
message Rule {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/Rule"
    pattern: "projects/{project}/rules/{rule}"
  };

  // The unique identifier for this resource.
  // Format: projects/{project}/rules/{rule}
  string name = 1;

  // The description of the rule.
  string rule_description = 2;

  // The API Type for which this rule gets executed. A value of
  // `API_TYPE_UNSPECIFIED` indicates that the rule is executed for all API
  // transactions.
  ApiType api_type = 3;

  // The transaction type for which this rule gets executed. A value of
  // `TRANSACTION_TYPE_UNSPECIFIED` indicates that the rule is executed for
  // all transaction types.
  TransactionType transaction_type = 4;
}

// The metadata associated with a rule. This defines data that are used by the
// rule during execution.
message RuleMetadata {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/RuleMetadata"
    pattern: "projects/{project}/rules/{rule}/metadata/{metadata}"
  };

  // The type of metadata.
  enum Type {
    // Unspecified type.
    TYPE_UNSPECIFIED = 0;

    // List type. Indicates that the metadata contains a list of values which
    // the rule requires for execution.
    LIST = 1;
  }

  // The unique identifier for this resource.
  // Format: projects/{project}/rules/{rule}/metadata/{metadata}
  string name = 1;

  // The description of the rule metadata.
  string description = 2;

  // Type of rule metadata.
  Type type = 3;
}

// Represent a single value in a rule's metadata.
message RuleMetadataValue {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/RuleMetadataValue"
    pattern: "projects/{project}/rules/{rule}/metadata/{metadata}/values/{value}"
  };

  // Output only. The unique identifier for this resource.
  // Format: projects/{project}/rules/{rule}/metadata/{metadata}/values/{value}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The value of the resource which could be of type string or
  // AccountReference. The metadata values for rules
  // BlockedPayeeAccountReqPayDebitRule, BlockedPayerAccountReqPayDebitRule,
  // BlockedPayeeAccountReqPayCreditRule and BlockedPayerAccountReqPayCreditRule
  // should be of type AccountReference. For all other rules, metadata values
  // should be of type string.
  //
  // The length of the `value` field depends on the type of
  // the value being used for the rule metadata. The following are the minimum
  // and maximum lengths for the different types of values.
  //
  // Value Type | Minimum Length | Maximum Length |
  // -------- | -------- | -------- |
  // Bank account IFSC   | 11   | 11   |
  // Bank account number   | 1   | 255  |
  // Device identifier   | 1   | 255   |
  // Mobile number   | 12   | 12  |
  // Virtual private address (VPA)   | 3   | 255   |
  oneof value {
    // The value for string metadata.
    string id = 2;

    // The value for account reference metadata.
    AccountReference account_reference = 3;
  }
}

// Request body for the `ListRules` method.
message ListRulesRequest {
  // Required. The parent resource must have the format of `projects/{project}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/Rule"
    }
  ];

  // The maximum number of rules to return. The service may return fewer
  // than this value. If unspecified or if the specified value is less than 50,
  // at most 50 rules will be returned. The maximum value is 1000; values above
  // 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListRulesRequest` call.
  // Specify this parameter to retrieve the next page of rules.
  string page_token = 3;
}

// Response body for the `ListRules` method.
message ListRulesResponse {
  // List of rules satisfying the specified filter criteria.
  repeated Rule rules = 1;

  // Pass this token in a subsequent `ListRulesRequest` call to continue to list
  // results. If all results have been returned, this field is an empty string
  // or not present in the response.
  string next_page_token = 2;

  // Total number of rules matching request criteria across all pages.
  int64 total_size = 3;
}

// Request body for the `ListRuleMetadata` method.
message ListRuleMetadataRequest {
  // Required. The parent resource. The format is
  // `projects/{project}/rules/{rule}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/RuleMetadata"
    }
  ];

  // The maximum number of rule metadata to return. The service may return fewer
  // than this value. If unspecified or if the specified value is less than 50,
  // at most 50 rule metadata will be returned. The maximum value is 1000;
  // values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListRuleMetadataRequest` call.
  // Specify this parameter to retrieve the next page of rule metadata.
  string page_token = 3;
}

// Response body for the `ListRuleMetadata` method.
message ListRuleMetadataResponse {
  // List of rule metadata associated with the rule.
  repeated RuleMetadata rule_metadata = 1;

  // Pass this token in a subsequent `ListRuleMetadataRequest` call to continue
  // to list results. If all results have been returned, this field is an empty
  // string or not present in the response.
  string next_page_token = 2;

  // Total number of rule metadata matching request criteria across all pages.
  int64 total_size = 3;
}

// Request body for the `ListRuleMetadataValues` method.
message ListRuleMetadataValuesRequest {
  // Required. The parent resource. The format is
  // `projects/{project}/rules/{rule}/metadata/{metadata}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/RuleMetadataValue"
    }
  ];

  // The maximum number of metadata values to return. The service may return
  // fewer than this value. If unspecified or if the specified value is less
  // than 1, at most 50 rule metadata values will be returned. The maximum
  // value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token received from a previous `ListRuleMetadataValuesRequest`
  // call. Specify this parameter to retrieve the next page of rule metadata
  // values.
  string page_token = 3;
}

// Response body for ListRuleMetadataValues. Contains a List of values for a
// given rule metadata resource.
message ListRuleMetadataValuesResponse {
  // List of values for a given rule metadata resource identifier.
  repeated RuleMetadataValue rule_metadata_values = 1;

  // Pass this token in a subsequent `ListRuleMetadataValuesRequest` call to
  // continue to list results. If all results have been returned, this field is
  // an empty string or not present in the response.
  string next_page_token = 2;
}

// Request body for the `BatchCreateRuleMetadataValues` method.
message BatchCreateRuleMetadataValuesRequest {
  // The parent resource shared by all ruleMetadataValue being created. The
  // format is `projects/{project}/rules/{rule}/metadata/{metadata}`. The
  // [CreateRuleMetadataValueRequest.parent][google.cloud.paymentgateway.issuerswitch.v1.CreateRuleMetadataValueRequest.parent]
  // field in the
  // [CreateRuleMetadataValueRequest][google.cloud.paymentgateway.issuerswitch.v1.CreateRuleMetadataValueRequest]
  // messages contained in this request must match this field.
  string parent = 1 [(google.api.resource_reference) = {
    child_type: "issuerswitch.googleapis.com/RuleMetadataValue"
  }];

  // Required. The request message specifying the resources to create.
  // A maximum of 1000 RuleMetadataValues can be created in a batch.
  repeated CreateRuleMetadataValueRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response body for the `BatchCreateRuleMetadataValues` method.
message BatchCreateRuleMetadataValuesResponse {
  // List of RuleMetadataValue created.
  repeated RuleMetadataValue rule_metadata_value = 1;
}

// Request for creating a single `RuleMetadataValue`.
message CreateRuleMetadataValueRequest {
  // Required. The parent resource where this RuleMetadataValue will be created.
  // The format is `projects/{project}/rules/{rule}/metadata/{metadata}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/RuleMetadataValue"
    }
  ];

  // Required. The rule metadata value to create or add to a list.
  RuleMetadataValue rule_metadata_value = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request body for the `BatchDeleteRuleMetadataValues` method.
message BatchDeleteRuleMetadataValuesRequest {
  // The parent resource shared by all RuleMetadataValues being deleted. The
  // format is `projects/{project}/rules/{rule}/metadata/{metadata}`. If this is
  // set, the parent of all of the RuleMetadataValues specified in the
  // list of names must match this field.
  string parent = 1 [(google.api.resource_reference) = {
    child_type: "issuerswitch.googleapis.com/RuleMetadataValue"
  }];

  // Required. The names of the rule metadata values to delete.
  // A maximum of 1000 RuleMetadataValue can be deleted in a batch.
  // Format: projects/{project}/rules/{rule}/metadata/{metadata}/values/{value}
  repeated string names = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "issuerswitch.googleapis.com/RuleMetadataValue"
    }
  ];
}
