// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.paymentgateway.issuerswitch.v1;

import "google/cloud/paymentgateway/issuerswitch/v1/common_fields.proto";
import "google/cloud/paymentgateway/issuerswitch/v1/transactions.proto";
import "google/logging/type/log_severity.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.PaymentGateway.IssuerSwitch.V1";
option go_package = "cloud.google.com/go/paymentgateway/issuerswitch/apiv1/issuerswitchpb;issuerswitchpb";
option java_multiple_files = true;
option java_outer_classname = "LogsProto";
option java_package = "com.google.cloud.paymentgateway.issuerswitch.v1";
option php_namespace = "Google\\Cloud\\PaymentGateway\\IssuerSwitch\\V1";
option ruby_package = "Google::Cloud::PaymentGateway::IssuerSwitch::V1";

// The payload for the log entry.
message UpiTransaction {
  // A human readable message about the log entry.
  string message = 1;

  // The severity of the log entry.
  google.logging.type.LogSeverity severity = 2;

  // The API type of the transaction.
  ApiType api_type = 3;

  // The XML API type of the transaction.
  XmlApiType xml_api_type = 4;

  // The type of the transaction.
  TransactionType transaction_type = 5;

  // UPI's transaction ID.
  string transaction_id = 6;

  // UPI's message ID.
  string message_id = 7;

  // The payment's RRN. This will be present only for payment related
  // transactions.
  string rrn = 8;

  // The timestamp at which the payload was received by the issuer switch.
  google.protobuf.Timestamp payload_receipt_time = 9;

  // The timestamp at which the payload was sent by the issuer switch.
  google.protobuf.Timestamp payload_sent_time = 10;

  // Status of the transaction which could be SUCCESS or FAILURE. This will be
  // populated only after transaction is complete.
  TransactionInfo.State status = 11;

  // Issuer switch error code. This will be present only for failed
  // transactions.
  string error_code = 12;

  // UPI error code that was sent back to NPCI. This will be present only for
  // failed transactions.
  string upi_error_code = 13;

  // Issuer switch error message. This will be present only for failed
  // transactions.
  string error_message = 14;

  // The ack, request or response payload.
  oneof payload {
    // The payload in XML format sent to the issuer switch.
    string sent = 15;

    // The payload in XML format received by the issuer switch.
    string received = 16;
  }
}
