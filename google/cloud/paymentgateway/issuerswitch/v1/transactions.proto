// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.paymentgateway.issuerswitch.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/paymentgateway/issuerswitch/v1/common_fields.proto";
import "google/cloud/paymentgateway/issuerswitch/v1/resolutions.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option csharp_namespace = "Google.Cloud.PaymentGateway.IssuerSwitch.V1";
option go_package = "cloud.google.com/go/paymentgateway/issuerswitch/apiv1/issuerswitchpb;issuerswitchpb";
option java_multiple_files = true;
option java_outer_classname = "TransactionsProto";
option java_package = "com.google.cloud.paymentgateway.issuerswitch.v1";
option php_namespace = "Google\\Cloud\\PaymentGateway\\IssuerSwitch\\V1";
option ruby_package = "Google::Cloud::PaymentGateway::IssuerSwitch::V1";

// Fetch the issuer switch participant.
// Lists and exports transactions processed by the issuer switch.
service IssuerSwitchTransactions {
  option (google.api.default_host) = "issuerswitch.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // List metadata transactions that satisfy the specified filter criteria.
  rpc ListMetadataTransactions(ListMetadataTransactionsRequest)
      returns (ListMetadataTransactionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/metadataTransactions"
    };
    option (google.api.method_signature) = "parent";
  }

  // List financial transactions that satisfy specified filter criteria.
  rpc ListFinancialTransactions(ListFinancialTransactionsRequest)
      returns (ListFinancialTransactionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/financialTransactions"
    };
    option (google.api.method_signature) = "parent";
  }

  // List mandate transactions that satisfy specified filter criteria.
  rpc ListMandateTransactions(ListMandateTransactionsRequest)
      returns (ListMandateTransactionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/mandateTransactions"
    };
    option (google.api.method_signature) = "parent";
  }

  // List complaint transactions that satisfy specified filter criteria.
  rpc ListComplaintTransactions(ListComplaintTransactionsRequest)
      returns (ListComplaintTransactionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/complaintTransactions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Export financial transactions received within the specified time range as a
  // file into a configured target location. The returned `Operation` type has
  // the following method-specific fields:
  //
  // - `metadata`:
  // [ExportFinancialTransactionsMetadata][google.cloud.paymentgateway.issuerswitch.v1.ExportFinancialTransactionsMetadata]
  // - `response`:
  // [ExportFinancialTransactionsResponse][google.cloud.paymentgateway.issuerswitch.v1.ExportFinancialTransactionsResponse]
  //
  // The exported file will be in the standard CSV format where each row in the
  // file represents a transaction. The file has the following fields in order:
  //
  // 1. `TransactionID`
  //     * **Min Length** - 35 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - UPI transaction ID.
  // 1. `TransactionType`
  //     * **Min Length** - 22 characters
  //     * **Max Length** - 25 characters
  //     * **Description** - Type of the transaction. This will be one of
  //     `TRANSACTION_TYPE_CREDIT`, `TRANSACTION_TYPE_DEBIT` or
  //     `TRANSACTION_TYPE_REVERSAL`.
  // 1. `TransactionSubType`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 7 characters
  //     * **Description** - Subtype of the transaction. This will be one of
  //     `COLLECT`, or `PAY`.
  // 1. `CreationTime`
  //     * **Min Length** - 20 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Timestamp (in UTC) indicating when the issuer
  //     switch created the transaction resource for processing the transaction.
  //     The format will be as per RFC-3339. Example : 2022-11-22T23:00:05Z
  // 1. `State`
  //     * **Min Length** - 6 characters
  //     * **Max Length** - 9 characters
  //     * **Description** - State of the transaction. This will be one of
  //     `FAILED`, `SUCCEEDED`, or `TIMED_OUT`.
  // 1. `RRN`
  //     * **Min Length** - 12 characters
  //     * **Max Length** - 12 characters
  //     * **Description** - Retrieval reference number associated with the
  //     transaction.
  // 1. `PayerVPA`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Virtual Payment Address (VPA) of the payer.
  // 1. `PayerMobileNumber`
  //     * **Min Length** - 12 characters
  //     * **Max Length** - 12 characters
  //     * **Description** - Mobile number of the payer.
  // 1. `PayerIFSC`
  //     * **Min Length** - 11 characters
  //     * **Max Length** - 11 characters
  //     * **Description** - IFSC of the payer's bank account.
  // 1. `PayerAccountNumber`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 30 characters
  //     * **Description** - Payer's bank account number.
  // 1. `PayerAccountType`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 7 characters
  //     * **Description** - Payer's bank account type. This will be one of
  //     `SAVINGS`, `DEFAULT`, `CURRENT`, `NRE`, `NRO`, `PPIWALLET`,
  //     `BANKWALLET`, `CREDIT`, `SOD`, or `UOD`.
  // 1. `PayeeVPA`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Virtual Payment Address (VPA) of the payee.
  // 1. `PayeeMobileNumber`
  //     * **Min Length** - 12 characters
  //     * **Max Length** - 12 characters
  //     * **Description** - Payee's mobile number.
  // 1. `PayeeIFSC`
  //     * **Min Length** - 11 characters
  //     * **Max Length** - 11 characters
  //     * **Description** - IFSC of the payee's bank account.
  // 1. `PayeeAccountNumber`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 30 characters
  //     * **Description** - Payee's bank account number.
  // 1. `PayeeAccountType`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 10 characters
  //     * **Description** - Payee's bank account type. This will be one of
  //     `SAVINGS`, `DEFAULT`, `CURRENT`, `NRE`, `NRO`, `PPIWALLET`,
  //     `BANKWALLET`, `CREDIT`, `SOD`, or `UOD`.
  // 1. `PayeeMerchantID`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Payee's merchant ID, only if the payee is a
  //     merchant.
  // 1. `PayeeMerchantName`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 99 characters
  //     * **Description** - Payee's merchant name, only if the payee is a
  //     merchant.
  // 1. `PayeeMCC`
  //     * **Min Length** - 4 characters
  //     * **Max Length** - 4 characters
  //     * **Description** - Payee's Merchant Category Code (MCC), only if the
  //     payee is a merchant.
  // 1. `Currency`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 3 characters
  //     * **Description** - Currency of the amount involved in the transaction.
  //     The currency codes are defined in ISO 4217.
  // 1. `Amount`
  //     * **Description** - Amount involved in the transaction.
  // 1. `AdapterRequestIDs`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 2,000 characters
  //     * **Description** - List of adapter request IDs (colon separated) used
  //     when invoking the Adapter APIs for fulfilling a transaction request.
  // 1. `ErrorCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Error code of a failed transaction.
  // 1. `ErrorMessage`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 10,000 characters
  //     * **Description** - Error description for a failed transaction.
  // 1. `UPIErrorCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 3 characters
  //     * **Description** - Error code as per the UPI specification. The issuer
  //     switch maps the ErrorCode to an appropriate error code that complies
  //     with the UPI specification.
  // 1. `PayerDeviceInfoTypeAppName`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Payment application name on the payer's device.
  // 1. `PayerDeviceInfoTypeCapability`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 99 characters
  //     * **Description** - Capability of the payer's device.
  // 1. `PayerDeviceInfoTypeGeoCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 15 characters
  //     * **Description** - Geo code of the payer's device. This will include
  //     floating point values for latitude and longitude (separated by colon).
  // 1. `PayerDeviceInfoTypeID`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - Device ID of the payer's device.
  // 1. `PayerDeviceInfoTypeIP`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 39 characters
  //     * **Description** - IP address of the payer's device.
  // 1. `PayerDeviceInfoTypeLocation`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 40 characters
  //     * **Description** - Coarse location of the payer's device.
  // 1. `PayerDeviceInfoTypeOS`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Operating system on the payer's device.
  // 1. `PayerDeviceInfoTypeTelecomProvider`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 99 characters
  //     * **Description** - Telecom provider for the payer's device.
  // 1. `PayerDeviceInfoTypeDeviceType`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 9 characters
  //     * **Description** - Type of the payer's device. This will be one of
  //     'MOB', 'INET', 'USDC/USDB', 'POS'.
  // 1. `PayeeDeviceInfoTypeAppName`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Payment application name on the payee's device.
  // 1. `PayeeDeviceInfoTypeCapability`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 99 characters
  //     * **Description** - Capability of the payee's device.
  // 1. `PayeeDeviceInfoTypeGeoCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 15 characters
  //     * **Description** - Geo code of the payee's device. This will include
  //     floating point values for latitude and longitude (separated by colon).
  // 1. `PayeeDeviceInfoTypeID`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - Device ID of the payee's device.
  // 1. `PayeeDeviceInfoTypeIP`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 39 characters
  //     * **Description** - IP address of the payee's device.
  // 1. `PayeeDeviceInfoTypeLocation`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 40 characters
  //     * **Description** - Coarse location of the payee's device.
  // 1. `PayeeDeviceInfoTypeOS`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Operating system on the payee's device.
  // 1. `PayeeDeviceInfoTypeTelecomProvider`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 99 characters
  //     * **Description** - Telecom provider for the payee's device.
  // 1. `PayeeDeviceInfoTypeDeviceType`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 9 characters
  //     * **Description** - Type of the payee's device. This will be one of
  //     'MOB', 'INET', 'USDC/USDB', 'POS'.
  // 1. `ReferenceID`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - Consumer reference number to identify loan number,
  //     order id etc.
  // 1. `ReferenceURI`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - URL for the  transaction.
  // 1. `ReferenceCategory`
  //     * **Min Length** - 2 characters
  //     * **Max Length** - 2 characters
  //     * **Description** - Reference category.
  rpc ExportFinancialTransactions(ExportFinancialTransactionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/financialTransactions:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportFinancialTransactionsResponse"
      metadata_type: "ExportFinancialTransactionsMetadata"
    };
  }

  // Export metadata transactions received within the specified time range as a
  // file into a configured target location. The returned `Operation` type has
  // the following method-specific fields:
  //
  // - `metadata`:
  // [ExportMetadataTransactionsMetadata][google.cloud.paymentgateway.issuerswitch.v1.ExportMetadataTransactionsMetadata]
  // - `response`:
  // [ExportMetadataTransactionsResponse][google.cloud.paymentgateway.issuerswitch.v1.ExportMetadataTransactionsResponse]
  //
  // The exported file will be in the standard CSV format where each row in the
  // file represents a transaction. The file has the following fields in order:
  //
  // 1. `TransactionID`
  //     * **Min Length** - 35 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - UPI transaction ID.
  // 1. `APIType`
  //     * **Description** - The transaction's API type. The value will be of
  //     the [ApiType][google.cloud.paymentgateway.issuerswitch.v1.ApiType]
  //     enum.
  // 1. `TransactionType`
  //     * **Description** - Type of the transaction. The value will be of the
  //     [TransactionType][google.cloud.paymentgateway.issuerswitch.v1.TransactionType]
  //     enum.
  // 1. `CreationTime`
  //     * **Min Length** - 20 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Timestamp (in UTC) indicating when the issuer
  //     switch created the transaction resource for processing the transaction.
  //     The format will be as per RFC-3339. Example : 2022-11-22T23:00:05Z
  // 1. `State`
  //     * **Min Length** - 6 characters
  //     * **Max Length** - 9 characters
  //     * **Description** - State of the transaction. This will be one of
  //     `FAILED`, `SUCCEEDED`, or `TIMED_OUT`.
  // 1. `OriginVPA`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Virtual Payment Address (VPA) of the originator of
  //     the transaction.
  // 1. `AdapterRequestIDs`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 2,000 characters
  //     * **Description** - List of adapter request IDs (colon separated) used
  //     when invoking the Adapter APIs for fulfilling a transaction request.
  // 1. `ErrorCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Error code of the failed transaction.
  // 1. `ErrorMessage`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 10,000 characters
  //     * **Description** - Error description for the failed transaction.
  // 1. `UPIErrorCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 3 characters
  //     * **Description** - Error code as per the UPI specification. The issuer
  //     switch maps the ErrorCode to an appropriate error code that complies
  //     with the UPI specification.
  rpc ExportMetadataTransactions(ExportMetadataTransactionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/metadataTransactions:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportMetadataTransactionsResponse"
      metadata_type: "ExportMetadataTransactionsMetadata"
    };
  }

  // Export mandate transactions received within the specified time range as a
  // file into a configured target location. The returned `Operation` type has
  // the following method-specific fields:
  //
  // - `metadata`:
  // [ExportMandateTransactionsMetadata][google.cloud.paymentgateway.issuerswitch.v1.ExportMandateTransactionsMetadata]
  // - `response`:
  // [ExportMandateTransactionsResponse][google.cloud.paymentgateway.issuerswitch.v1.ExportMandateTransactionsResponse]
  //
  // The exported file will be in the standard CSV format where each row in the
  // file represents a transaction. The file has the following fields in order:
  //
  // 1. `TransactionID`
  //     * **Min Length** - 35 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - UPI transaction ID.
  // 1. `UniqueMandateNumber`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 70 characters
  //     * **Description** - UPI Unique Mandate Number.
  // 1. `TransactionType`
  //     * **Min Length** - 23 characters
  //     * **Max Length** - 23 characters
  //     * **Description** - Type of the transaction. This will be one of
  //     `TRANSACTION_TYPE_CREATE`, `TRANSACTION_TYPE_REVOKE`,
  //     `TRANSACTION_TYPE_UPDATE`, `TRANSACTION_TYPE_PAUSE` or
  //     `TRANSACTION_TYPE_UNPAUSE`.
  // 1. `CreationTime`
  //     * **Min Length** - 20 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Timestamp (in UTC) indicating when the issuer
  //     switch created the transaction resource for processing the transaction.
  //     The format will be as per RFC-3339. Example : 2022-11-22T23:00:05Z
  // 1. `State`
  //     * **Min Length** - 6 characters
  //     * **Max Length** - 9 characters
  //     * **Description** - State of the transaction. This will be one of
  //     `FAILED`, `SUCCEEDED`, or `TIMED_OUT`.
  // 1. `PayerVPA`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Virtual Payment Address (VPA) of the payer.
  // 1. `PayerMobileNumber`
  //     * **Min Length** - 12 characters
  //     * **Max Length** - 12 characters
  //     * **Description** - Mobile number of the payer.
  // 1. `PayerIFSC`
  //     * **Min Length** - 11 characters
  //     * **Max Length** - 11 characters
  //     * **Description** - IFSC of the payer's bank account.
  // 1. `PayerAccountNumber`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 30 characters
  //     * **Description** - Payer's bank account number.
  // 1. `PayerAccountType`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 7 characters
  //     * **Description** - Payer's bank account type. This will be one of
  //     `SAVINGS`, `DEFAULT`, `CURRENT`, `NRE`, `NRO`, `PPIWALLET`,
  //     `BANKWALLET`, `CREDIT`, `SOD`, or `UOD`.
  // 1. `PayeeVPA`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Virtual Payment Address (VPA) of the payee.
  // 1. `PayeeMobileNumber`
  //     * **Min Length** - 12 characters
  //     * **Max Length** - 12 characters
  //     * **Description** - Mobile number of the payee.
  // 1. `PayeeIFSC`
  //     * **Min Length** - 11 characters
  //     * **Max Length** - 11 characters
  //     * **Description** - IFSC of the payee's bank account.
  // 1. `PayeeAccountNumber`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 30 characters
  //     * **Description** - Payee's bank account number.
  // 1. `PayeeAccountType`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 10 characters
  //     * **Description** - Payee's bank account type. This will be one of
  //     `SAVINGS`, `DEFAULT`, `CURRENT`, `NRE`, `NRO`, `PPIWALLET`,
  //     `BANKWALLET`, `CREDIT`, `SOD`, or `UOD`.
  // 1. `PayeeMerchantID`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 30 characters
  //     * **Description** - Payee's merchant ID, only if the payee is a
  //     merchant
  // 1. `PayeeMerchantName`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 99 characters
  //     * **Description** - Payee's merchant name, only if the payee is a
  //     merchant.
  // 1. `PayeeMCC`
  //     * **Min Length** - 4 characters
  //     * **Max Length** - 4 characters
  //     * **Description** - Payee's Merchant Category Code (MCC), only if the
  //     payee is a merchant.
  // 1. `Amount`
  //     * **Description** - Amount specified in the mandate.
  // 1. `RecurrencePattern`
  //     * **Description** - Reccurence pattern of the mandate. The value will
  //     be of the
  //     [MandateTransaction.RecurrencePatternType][google.cloud.paymentgateway.issuerswitch.v1.MandateTransaction.RecurrencePatternType]
  //     enum.
  // 1. `RecurrenceRuleType`
  //     * **Description** - Reccurrence rule type of the mandate. The value
  //     will be of the
  //     [MandateTransaction.RecurrenceRuleType][google.cloud.paymentgateway.issuerswitch.v1.MandateTransaction.RecurrenceRuleType]
  //     enum.
  // 1. `RecurrenceRuleValue`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 2 characters
  //     * **Description** - Recurrence rule value of the mandate. This will be
  //     an integer between 1 and 31.
  // 1. `Revokeable`
  //     * **Min Length** - 4 characters
  //     * **Max Length** - 5 characters
  //     * **Description** - Boolean value specifying if the mandate is
  //     revokable.
  // 1. `StartDate`
  //     * **Min Length** - 10 characters
  //     * **Max Length** - 10 characters
  //     * **Description** - The start date of the mandate in `DD-MM-YYYY`
  //     format.
  // 1. `EndDate`
  //     * **Min Length** - 10 characters
  //     * **Max Length** - 10 characters
  //     * **Description** - The end date of the mandate in `DD-MM-YYYY` format.
  // 1. `AmountRuleType`
  //     * **Description** - The amount rule of the mandate. The value will be
  //     of the
  //     [MandateTransaction.AmountRuleType][google.cloud.paymentgateway.issuerswitch.v1.MandateTransaction.AmountRuleType]
  //     enum.
  // 1. `ApprovalReference`
  //     * **Min Length** - 6 characters
  //     * **Max Length** - 9 characters
  //     * **Description** - The block funds reference generated by the bank, if
  //     funds have been blocked for the mandate. This column will have a value
  //     only when the RecurrencePattern is ONETIME.
  // 1. `BlockFunds`
  //     * **Min Length** - 4 characters
  //     * **Max Length** - 5 characters
  //     * **Description** - Boolean value specifying if the mandate transaction
  //     requested to block funds.
  // 1. `LastUpdateTime`
  //     * **Min Length** - 20 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Timestamp (in UTC) indicating when was the last
  //     modification made to the mandate. The format will be as per RFC-3339.
  //     Example : 2022-11-22T23:00:05Z
  // 1. `AdapterRequestIDs`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 2,000 characters
  //     * **Description** - List of adapter request IDs (colon separated) used
  //     when invoking the Adapter APIs for fulfilling a transaction request.
  // 1. `ErrorCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Error code of the failed transaction.
  // 1. `ErrorMessage`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 10,000 characters
  //     * **Description** - Error description for the failed transaction.
  // 1. `UPIErrorCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 3 characters
  //     * **Description** - Error code as per the UPI specification. The issuer
  //     switch maps the ErrorCode to an appropriate error code that complies
  //     with the UPI specification.
  // 1. `PayerDeviceInfoTypeAppName`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Payment application name on the payer's device.
  // 1. `PayerDeviceInfoTypeCapability`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 99 characters
  //     * **Description** - Capability of the payer's device.
  // 1. `PayerDeviceInfoTypeGeoCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 15 characters
  //     * **Description** - Geo code of the payer's device. This will include
  //     floating point values for latitude and longitude (separated by colon).
  // 1. `PayerDeviceInfoTypeID`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - Device ID of the payer's device.
  // 1. `PayerDeviceInfoTypeIP`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 39 characters
  //     * **Description** - IP address of the payer's device.
  // 1. `PayerDeviceInfoTypeLocation`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 40 characters
  //     * **Description** - Coarse location of the payer's device.
  // 1. `PayerDeviceInfoTypeOS`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Operating system on the payer's device.
  // 1. `PayerDeviceInfoTypeTelecomProvider`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 99 characters
  //     * **Description** - Telecom provider for the payer's device.
  // 1. `PayerDeviceInfoTypeDeviceType`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 9 characters
  //     * **Description** - Type of the payer's device. This will be one of
  //     'MOB', 'INET', 'USDC/USDB', 'POS'.
  // 1. `PayeeDeviceInfoTypeAppName`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Payment application name on the payee's device.
  // 1. `PayeeDeviceInfoTypeCapability`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 99 characters
  //     * **Description** - Capability of the payee's device.
  // 1. `PayeeDeviceInfoTypeGeoCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 15 characters
  //     * **Description** - Geo code of the payee's device. This will include
  //     floating point values for latitude and longitude (separated by colon).
  // 1. `PayeeDeviceInfoTypeID`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - Device ID of the payee's device.
  // 1. `PayeeDeviceInfoTypeIP`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 39 characters
  //     * **Description** - IP address of the payee's device.
  // 1. `PayeeDeviceInfoTypeLocation`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 40 characters
  //     * **Description** - Coarse location of the payee's device.
  // 1. `PayeeDeviceInfoTypeOS`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Operating system on the payee's device.
  // 1. `PayeeDeviceInfoTypeTelecomProvider`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 99 characters
  //     * **Description** - Telecom provider for the payee's device.
  // 1. `PayeeDeviceInfoTypeDeviceType`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 9 characters
  //     * **Description** - Type of the payee's device. This will be one of
  //     `MOB`, `INET`, `USDC/USDB`, `POS`.
  // 1. `ReferenceID`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - Consumer reference number to identify loan number,
  //     order id etc.
  // 1. `ReferenceURI`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - URL for the  transaction.
  // 1. `ReferenceCategory`
  //     * **Min Length** - 2 characters
  //     * **Max Length** - 2 characters
  //     * **Description** - Reference category.
  // 1. `MandateName`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - The mandate's name.
  rpc ExportMandateTransactions(ExportMandateTransactionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/mandateTransactions:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportMandateTransactionsResponse"
      metadata_type: "ExportMandateTransactionsMetadata"
    };
  }

  // Export complaint transactions received within the specified time range as a
  // file into a configured target location. The returned `Operation` type has
  // the following method-specific fields:
  //
  // - `metadata`:
  // [ExportComplaintTransactionsMetadata][google.cloud.paymentgateway.issuerswitch.v1.ExportComplaintTransactionsMetadata]
  // - `response`:
  // [ExportComplaintTransactionsResponse][google.cloud.paymentgateway.issuerswitch.v1.ExportComplaintTransactionsResponse]
  //
  // The exported file will be in the standard CSV format where each row in the
  // file represents a transaction. The file has the following fields in order:
  //
  // 1. `TransactionID`
  //     * **Min Length** - 35 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - UPI transaction ID.
  // 1. `TransactionType`
  //     * **Min Length** - 23 characters
  //     * **Max Length** - 30 characters
  //     * **Description** - Type of the transaction. This will be one of
  //     `TRANSACTION_TYPE_CHECK_STATUS`, `TRANSACTION_TYPE_COMPLAINT`,
  //     `TRANSACTION_TYPE_REVERSAL`, `TRANSACTION_TYPE_DISPUTE`,
  //     `TRANSACTION_TYPE_REFUND`, or `TRANSACTION_TYPE_STATUS_UPDATE`.
  // 1. `CreationTime`
  //     * **Min Length** - 20 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Timestamp (in UTC) indicating when the issuer
  //     switch created the transaction resource for processing the transaction.
  //     The format will be as per RFC-3339. Example : 2022-11-22T23:00:05Z
  // 1: `State`
  //     * **Min Length** - 6 characters
  //     * **Max Length** - 9 characters
  //     * **Description** - State of the transaction. This will be one of
  //     `FAILED`, `SUCCEEDED`, or `TIMED_OUT`.
  // 1. `OriginalRRN`
  //     * **Min Length** - 12 characters
  //     * **Max Length** - 12 characters
  //     * **Description** - Retrieval reference number of the original payment
  //     transaction.
  // 1. `BankType`
  //     * **Min Length** - 8 characters
  //     * **Max Length** - 11 characters
  //     * **Description** - The subtype of the transaction based on the bank
  //     involved. This will be one of `BENEFICIARY`, or `REMITTER`.
  // 1. `OriginalTransactionID`
  //     * **Min Length** - 35 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - Transaction ID of the original unresolved
  //     transaction.
  // 1. `RaiseComplaintAdjFlag`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the type of action to raise the
  //     complaint.
  // 1. `RaiseComplaintAdjCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the reason of action to raise the
  //     complaint.
  // 1. `ResolveComplaintAdjFlag`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the type of action to resolve the
  //     complaint.
  // 1. `ResolveComplaintAdjCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the reason of action to resolve the
  //     complaint.
  // 1. `RaiseDisputeAdjFlag`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the type of action to raise the dispute.
  // 1. `RaiseDisputeAdjCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the reason of action to raise the
  //     dispute.
  // 1. `ResolveDisputeAdjFlag`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the type of action to resolve the
  //     dispute.
  // 1. `ResolveDisputeAdjCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the reason of action to resolve the
  //     dispute.
  // 1. `Amount`
  //     * **Description** - Amount to be resolved.
  // 1. `CurrentCycle`
  //     * **Min Length** - 4 characters
  //     * **Max Length** - 5 characters
  //     * **Description** - Boolean value specifying if the complaint / dispute
  //     belongs to current settlement cycle or not.
  // 1. `CRN`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Defines the Complaint Reference number.
  // 1. `AdjTime`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the time when the resolution was done.
  // 1. `RespAdjFlag`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the response category type.
  // 1. `RespAdjCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the response reason used.
  // 1. `AdjRemarks`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Indicates the additional remarks for the complaint
  //     / dispute.
  // 1. `AdapterRequestIDs`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 2,000 characters
  //     * **Description** - List of adapter request IDs (colon separated) used
  //     when invoking the Adapter APIs for fulfilling a transaction request.
  // 1. `ErrorCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Error code of the failed transaction.
  // 1. `ErrorMessage`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 10,000 characters
  //     * **Description** - Error description for the failed transaction.
  // 1. `UPIErrorCode`
  //     * **Min Length** - 0 characters
  //     * **Max Length** - 3 characters
  //     * **Description** - Error code as per the UPI specification. The issuer
  //     switch service maps the ErrorCode to an appropriate error code that
  //     complies with the UPI specification.
  rpc ExportComplaintTransactions(ExportComplaintTransactionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/complaintTransactions:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportComplaintTransactionsResponse"
      metadata_type: "ExportComplaintTransactionsMetadata"
    };
  }
}

// Information about a transaction processed by the issuer switch.
// The fields in this type are common across both financial and metadata
// transactions.
message TransactionInfo {
  // Specifies the current state of the transaction.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The transaction has successfully completed.
    SUCCEEDED = 1;

    // The transaction has failed.
    FAILED = 2;

    // The transaction has timed out.
    TIMED_OUT = 3;
  }

  // The subtype of a transaction. This value is used only for certain API type
  // and transaction type combinations.
  enum TransactionSubType {
    // Unspecified transaction subtype.
    TRANSACTION_SUB_TYPE_UNSPECIFIED = 0;

    // Collect subtype. This is used in a `SETTLE_PAYMENT` API type
    // transaction, with the transaction type as either
    // `TRANSACTION_TYPE_CREDIT` or `TRANSACTION_TYPE_DEBIT` when the payment
    // was initiated by a collect request.
    COLLECT = 1;

    // Debit subtype. This is used in a `SETTLE_PAYMENT` API type transaction,
    // with the transaction type as `TRANSACTION_TYPE_REVERSAL` when the
    // original payment was a debit request.
    DEBIT = 2;

    // Pay subtype. This is used in a `SETTLE_PAYMENT` API type transaction,
    // with the transaction type as either `TRANSACTION_TYPE_CREDIT` or
    // `TRANSACTION_TYPE_DEBIT` when the payment was initiated by a pay request.
    PAY = 3;

    // Beneficiary subtype. This is used in a `COMPLAINT` API type transaction,
    // when the complaint / dispute request is initiated / received by the
    // beneficiary bank.
    BENEFICIARY = 4;

    // Remitter subtype. This is used in a `COMPLAINT` API type transaction,
    // when the complaint / dispute request is initiated / received by the
    // remitter bank.
    REMITTER = 5;

    // Refund subtype. This is used in a `SETTLE_PAYMENT` API type transaction,
    // with the transaction type as `TRANSACTION_TYPE_CREDIT` when the payment
    // was initiated in response to a refund.
    REFUND = 6;

    // Credit subtype. This is used in a `SETTLE_PAYMENT` API type transaction,
    // with the transaction type as `TRANSACTION_TYPE_REVERSAL` when the
    // original payment was a credit request.
    CREDIT = 7;
  }

  // Common metadata about an API transaction.
  message TransactionMetadata {
    // Output only. The time at which the transaction resource was created by
    // the issuer switch.
    google.protobuf.Timestamp create_time = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time at which the transaction resource was last updated
    // by the issuer switch.
    google.protobuf.Timestamp update_time = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A reference id for the API transaction.
    string reference_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A reference URI to this API transaction.
    string reference_uri = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A descriptive note about this API transaction.
    string description = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The initiation mode of this API transaction. In UPI, the
    // values are as defined by the UPI API specification.
    string initiation_mode = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The purpose code of this API transaction. In UPI, the values
    // are as defined by the UPI API specification.
    string purpose_code = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The reference category of this API transaction.
    string reference_category = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // All details about any error in the processing of an API transaction.
  message TransactionErrorDetails {
    // Output only. Error code of the failed transaction.
    string error_code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Error description for the failed transaction.
    string error_message = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Error code as per the UPI specification. The issuer switch
    // maps the ErrorCode to an appropriate error code that complies with the
    // UPI specification.
    string upi_error_code = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Information about an adapter invocation triggered as part of the
  // processing of an API transaction.
  message AdapterInfo {
    // Metadata about a response that the adapter includes in its response
    // to the issuer switch.
    message ResponseMetadata {
      // A map of name-value pairs.
      map<string, string> values = 1;
    }

    // Output only. List of adapter request IDs (colon separated) used when
    // invoking the Adapter APIs for fulfilling a transaction request.
    string request_ids = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Response metadata included by the adapter in its response to
    // an API invocation from the issuer switch.
    ResponseMetadata response_metadata = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Information about the transaction's risk evaluation as provided by the
  // payments orchestrator.
  message TransactionRiskInfo {
    // Entity providing the risk score. This could either be the payment service
    // provider or the payment orchestrator (UPI, etc).
    string provider = 1;

    // Type of risk. Examples include `TXNRISK`.
    string type = 2;

    // Numeric value of risk evaluation ranging from 0 (No Risk) to 100 (Maximum
    // Risk).
    string value = 3;
  }

  // Output only. An identifier that is mandatorily present in every transaction
  // processed via UPI. This maps to UPI's transaction ID.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The API type of the transaction.
  ApiType api_type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The transaction type.
  TransactionType transaction_type = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The transaction subtype.
  TransactionSubType transaction_sub_type = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The transaction's state.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Metadata about the API transaction.
  TransactionMetadata metadata = 6;

  // Output only. Any error details for the current API transaction, if the
  // state is `FAILED`.
  TransactionErrorDetails error_details = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Information about the adapter invocation from the issuer
  // switch for processing this API transaction.
  AdapterInfo adapter_info = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Risk information as provided by the payments orchestrator.
  repeated TransactionRiskInfo risk_info = 9;
}

// A metadata API transaction processed by the issuer switch. This
// includes UPI APIs such as List Accounts, Balance Enquiry, etc.
message MetadataTransaction {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/MetadataTransaction"
    pattern: "projects/{project}/metadataTransactions/{transaction}"
  };

  // The name of the metadata transaction. This uniquely identifies the
  // transaction. Format of name is
  // projects/{project_id}/metadataTransaction/{metadata_transaction_id}.
  string name = 1;

  // Information about the transaction.
  TransactionInfo info = 2;

  // Output only. The initiator of the metadata transaction.
  Participant initiator = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A financial API transaction processed by the issuer switch. In UPI, this maps
// to the Pay API.
message FinancialTransaction {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/FinancialTransaction"
    pattern: "projects/{project}/financialTransactions/{transaction}"
  };

  // A payment rule as provided by the payments orchestrator.
  message PaymentRule {
    // An enum of the possible rule names.
    enum PaymentRuleName {
      // Rule name unspecified.
      PAYMENT_RULE_NAME_UNSPECIFIED = 0;

      // The `expire after` rule.
      EXPIRE_AFTER = 1;

      // The `min amount` rule.
      MIN_AMOUNT = 2;
    }

    // The rule's name.
    PaymentRuleName payment_rule = 1;

    // The rule's value.
    string value = 2;
  }

  // The name of the financial transaction. This uniquely identifies the
  // transaction. Format of name is
  // projects/{project_id}/financialTransactions/{financial_transaction_id}.
  string name = 1;

  // Information about the transaction.
  TransactionInfo info = 2;

  // Output only. A 12 digit numeric code associated with the request. It could
  // contain leading 0s. In UPI, this is also known as as the customer reference
  // or the UPI transaction ID.
  string retrieval_reference_number = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The payer in the transaction.
  SettlementParticipant payer = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The payee in the transaction.
  SettlementParticipant payee = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The amount for payment settlement in the transaction.
  google.type.Money amount = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A list of rules specified by the payments orchestrator for this API
  // transaction.
  repeated PaymentRule payment_rules = 7;
}

// A mandate processed by the issuer switch. In UPI, this maps to the Mandate
// API.
message MandateTransaction {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/MandateTransaction"
    pattern: "projects/{project}/mandateTransactions/{transaction}"
  };

  // RecurrencePatternType specifies the recurrence pattern type of the mandate.
  enum RecurrencePatternType {
    // Unspecified recurrence pattern.
    RECURRENCE_PATTERN_TYPE_UNSPECIFIED = 0;

    // As presented recurrence pattern.
    AS_PRESENTED = 1;

    // Bi monthly recurrence pattern.
    BIMONTHLY = 2;

    // Daily recurrence pattern.
    DAILY = 3;

    // Bi weekly recurrence pattern.
    FORTNIGHTLY = 4;

    // Half yearly recurrence pattern.
    HALF_YEARLY = 5;

    // Monthly recurrence pattern.
    MONTHLY = 6;

    // One time recurrence pattern.
    ONE_TIME = 7;

    // Quarterly recurrence pattern.
    QUARTERLY = 8;

    // Weekly recurrence pattern.
    WEEKLY = 9;

    // Yearly recurrence pattern.
    YEARLY = 10;
  }

  // RecurrenceRuleType specifies the recurrence rule type of mandate.
  enum RecurrenceRuleType {
    // Unspecified recurrence rule type.
    RECURRENCE_RULE_TYPE_UNSPECIFIED = 0;

    // After recurrence rule type.
    AFTER = 1;

    // Before recurrence rule type.
    BEFORE = 2;

    // On recurrence rule type.
    ON = 3;
  }

  // AmountRuleType specifies the type of rule associated with the mandate
  // amount.
  enum AmountRuleType {
    // Unspecified amount rule.
    AMOUNT_RULE_TYPE_UNSPECIFIED = 0;

    // Exact amount rule. Amount specified is the exact amount for which
    // mandate could be granted.
    EXACT = 1;

    // Max amount rule. Amount specified is the maximum amount for which
    // mandate could be granted.
    MAX = 2;
  }

  // The name of the mandate transaction. This uniquely identifies the
  // transaction. Format of name is
  // projects/{project_id}/mandateTransactions/{mandate_transaction_id}.
  string name = 1;

  // Information about the transaction.
  TransactionInfo transaction_info = 2;

  // Output only. This maps to Unique Mandate Number (UMN) in UPI specification.
  string unique_mandate_number = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The payer in the transaction.
  SettlementParticipant payer = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The payee in the transaction.
  SettlementParticipant payee = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of recurrence pattern of the mandate.
  RecurrencePatternType recurrence_pattern = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of recurrence rule of the mandate.
  RecurrenceRuleType recurrence_rule_type = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The recurrence rule value of the mandate. This is a value from
  // 1 to 31.
  int32 recurrence_rule_value = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The start date of the mandate.
  google.type.Date start_date = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The end date of the mandate.
  google.type.Date end_date = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If true, this specifies mandate can be revoked.
  bool revokable = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The amount of the mandate.
  double amount = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The amount rule type of the mandate.
  AmountRuleType amount_rule = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Block funds reference generated by the bank, this will be
  // available only when Recurrence is ONETIME.
  string approval_reference = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If true, this specifies the mandate transaction requested
  // funds to be blocked.
  bool block_funds = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The mandate's name.
  string mandate_name = 16 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A complaint API transaction processed by the issuer switch. In
// UPI, this maps to the Complaint API.
message ComplaintTransaction {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/ComplaintTransaction"
    pattern: "projects/{project}/complaintTransactions/{transaction}"
  };

  // The name of the complaint transaction. This uniquely identifies the
  // transaction. Format of name is
  // projects/{project_id}/complaintTransactions/{complaint_transaction_id}.
  string name = 1;

  // Information about the transaction.
  TransactionInfo info = 2;

  // Information about the complaint transaction. It can be one of Complaint or
  // Dispute.
  oneof case {
    // Output only. Information about the complaint transaction when it is of
    // type complaint.
    Complaint complaint = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Information about the complaint transaction when it is of
    // type dispute.
    Dispute dispute = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }
}

// Request for the `ListMetadataTransactions` method. Callers can request for
// transactions to be filtered by the given filter criteria and specified
// pagination parameters.
message ListMetadataTransactionsRequest {
  // Required. The parent resource. The format is `projects/{project}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/MetadataTransaction"
    }
  ];

  // The maximum number of transactions to return. The service may return fewer
  // than this value. If unspecified or if the specified value is less than 1,
  // at most 50 transactions will be returned. The maximum value is 1000; values
  // above 1000 will be coerced to 1000. While paginating, you can specify a new
  // page size parameter for each page of transactions to be listed.
  int32 page_size = 2;

  // A page token, received from a previous `ListMetadataTransactions` call.
  // Specify this parameter to retrieve the next page of transactions.
  //
  // When paginating, you must specify only the `page_token` parameter. The
  // filter that was specified in the initial call to the
  // `ListMetadataTransactions` method that returned the page token will be
  // reused for all further calls where the page token parameter is specified.
  string page_token = 3;

  // An expression that filters the list of metadata transactions.
  //
  // A filter expression consists of a field name, a comparison
  // operator, and a value for filtering. The value must be a string, a
  // number, or a boolean. The comparison operator must be one of: `<`, `>` or
  // `=`. Filters are not case sensitive.
  //
  // The following fields in the `MetadataTransaction` are eligible for
  // filtering:
  //
  //   * `apiType` - The API type of the metadata transaction. Must be one of
  //   [ApiType][google.cloud.paymentgateway.issuerswitch.v1.ApiType] values.
  //   Allowed comparison operators: `=`.
  //   * `transactionType` - The transaction type of the metadata transaction.
  //   Must be one of
  //   [TransactionType][google.cloud.paymentgateway.issuerswitch.v1.TransactionType]
  //   values. Allowed comparison operators: `=`.
  //   * `transactionID` - The UPI transaction ID of the metadata transaction.
  //   Allowed comparison operators: `=`.
  //   * `createTime` - The time at which the transaction was created
  //   (received) by the issuer switch. The value should be in
  //   the format `YYYY-MM-DDTHH:MM:SSZ`. Allowed comparison operators: `>`,
  //   `<`.
  //
  // You can combine multiple expressions by enclosing each expression in
  // parentheses. Expressions are combined with AND logic. No other logical
  // operators are supported.
  //
  // Here are a few examples:
  //
  //   * `apiType = LIST_ACCOUNTS` -  - The API type is _LIST_ACCOUNTS_.
  //   * `state = SUCCEEDED` - The transaction's state is _SUCCEEDED_.
  //   * `(apiType = LIST_ACCOUNTS) AND (create_time <
  //   \"2021-08-15T14:50:00Z\")` - The API type is _LIST_ACCOUNTS_ and
  //   the transaction was received before _2021-08-15 14:50:00 UTC_.
  string filter = 4;
}

// Request for the `ListFinancialTransactions` method. Callers can request for
// transactions to be filtered by the given filter criteria and specified
// pagination parameters.
message ListFinancialTransactionsRequest {
  // Required. The parent resource. The format is `projects/{project}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/FinancialTransaction"
    }
  ];

  // The maximum number of transactions to return. The service may return fewer
  // than this value. If unspecified or if the specified value is less than 1,
  // at most 50 transactions will be returned. The maximum value is 1000; values
  // above 1000 will be coerced to 1000. While paginating, you can specify a new
  // page size parameter for each page of transactions to be listed.
  int32 page_size = 2;

  // A page token, received from a previous `ListFinancialTransactions` call.
  // Specify this parameter to retrieve the next page of transactions.
  //
  // When paginating, you must specify only the `page_token` parameter. The
  // filter that was specified in the initial call to the
  // `ListFinancialTransactions` method that returned the page token will be
  // reused for all further calls where the page token parameter is specified.
  string page_token = 3;

  // An expression that filters the list of financial transactions.
  //
  // A filter expression consists of a field name, a comparison operator, and
  // a value for filtering. The value must be a string, a number, or a
  // boolean. The comparison operator must be one of: `<`, `>`, or `=`.
  // Filters are not case sensitive.
  //
  // The following fields in the `FinancialTransaction` are eligible for
  // filtering:
  //
  //   * `transactionID` - The UPI transaction ID of the financial
  //   transaction. Allowed comparison operators: `=`.
  //   * `RRN` - The retrieval reference number of the transaction. Allowed
  //   comparison operators: `=`.
  //   * `payerVPA` - The VPA of the payer in a financial transaction. Allowed
  //   comparison operators: `=`.
  //   * `payeeVPA` - The VPA of the payee in a financial transaction. Allowed
  //   comparison operators: `=`.
  //   * `payerMobileNumber` - The mobile number of the payer in a financial
  //      transaction. Allowed comparison operators: `=`.
  //   * `payeeMobileNumber` - The mobile number of the payee in a financial
  //      transaction. Allowed comparison operators: `=`.
  //   * `createTime` - The time at which the transaction was created
  //   (received) by the issuer switch. The value should be in
  //   the format `YYYY-MM-DDTHH:MM:SSZ`. Allowed comparison operators: `>`,
  //   `<`.
  //
  // You can combine multiple expressions by enclosing each expression in
  // parentheses. Expressions are combined with AND logic. No other logical
  // operators are supported.
  //
  // Here are a few examples:
  //
  //   * `rrn = 123456789123` - The RRN is _123456789123_.
  //   * `payerVpa = example@goog` - The VPA of the payer is the string
  //   _example@goog_.
  //   * `(payeeVpa = example@goog) AND (createTime < "2021-08-15T14:50:00Z")`
  //   - The VPA of the payee is _example@goog_ and the transaction was received
  //   before _2021-08-15 14:50:00 UTC_.
  //   * `createTime > "2021-08-15T14:50:00Z" AND createTime <
  //   "2021-08-16T14:50:00Z"` - The transaction was received between
  //   _2021-08-15 14:50:00 UTC_ and _2021-08-16 14:50:00 UTC_.
  string filter = 4;
}

// Request for the `ListMandateTransactions` method. Callers can request for
// transactions to be filtered by the given filter criteria and specified
// pagination parameters.
message ListMandateTransactionsRequest {
  // Required. The parent resource. The format is `projects/{project}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/MandateTransaction"
    }
  ];

  // The maximum number of transactions to return. The service may return fewer
  // than this value. If unspecified or if the specified value is less than 1,
  // at most 50 transactions will be returned. The maximum value is 1000; values
  // above 1000 will be coerced to 1000. While paginating, you can specify a new
  // page size parameter for each page of transactions to be listed.
  int32 page_size = 2;

  // A page token, received from a previous `ListMandateTransactions` call.
  // Specify this parameter to retrieve the next page of transactions.
  //
  // When paginating, you must specify only the `page_token` parameter. The
  // filter that was specified in the initial call to the
  // `ListMandateTransactions` method that returned the page token will be
  // reused for all further calls where the page token parameter is specified.
  string page_token = 3;

  // An expression that filters the list of mandate transactions.
  //
  // A filter expression consists of a field name, a comparison operator, and
  // a value for filtering. The value must be a string, a number, or a
  // boolean. The comparison operator must be one of: `<`, `>`, or `=`.
  // Filters are not case sensitive.
  //
  // The following fields in the `Mandate` are eligible for
  // filtering:
  //
  //   * `uniqueMandateNumber` - UPI Unique Mandate Number (UMN). Allowed
  //   comparison operators: `=`.
  //   * `transactionID` - The transaction ID of the mandate transaction.
  //   Allowed comparison operators: `=`.
  //   * `transactionType` - The transaction type of the mandate
  //   transaction. Must be one of
  //   [TransactionType][google.cloud.paymentgateway.issuerswitch.v1.TransactionType]
  //   values. For mandate transactions, only valid transaction types are
  //   `TRANSACTION_TYPE_CREATE`, `TRANSACTION_TYPE_REVOKE` and
  //   `TRANSACTION_TYPE_UPDATE`. Allowed comparison operators: `=`.
  //   * `createTime` - The time at which the transaction was created
  //   (received) by the issuer switch. The value should be in
  //   the format `YYYY-MM-DDTHH:MM:SSZ`. Allowed comparison
  //   operators: `>`, `<`.
  // You can combine multiple expressions by enclosing each expression in
  // parentheses. Expressions are combined with AND logic. No other logical
  // operators are supported.
  //
  // Here are a few examples:
  //   * `recurrencePattern = MONTHLY` - The recurrence pattern type is
  //   monthly.
  //   * `state = SUCCEEDED` - The transaction's state is _SUCCEEDED_.
  //   * `payerVPA = example@okbank` - The VPA of the payer is the string
  //   _example@okbank_.
  //   * `(payerVPA = example@okbank) AND (createTime <
  //   "2021-08-15T14:50:00Z")`
  //   - The payer VPA example@okbank and the transaction was received
  //   before _2021-08-15 14:50:00 UTC_.
  //   * `createTime > "2021-08-15T14:50:00Z" AND createTime <
  //   "2021-08-16T14:50:00Z"` - The transaction was received between
  //   _2021-08-15 14:50:00 UTC_ and _2021-08-16 14:50:00 UTC_.
  //   * `startDate > "2021-08-15" AND startDate < "2021-08-17"` - The start
  //   date for mandate is between _2021-08-15_ and _2021-08-17_.
  string filter = 4;
}

// Request for the `ListComplaintTransactions` method. Callers can request for
// transactions to be filtered by the given filter criteria and specified
// pagination parameters.
message ListComplaintTransactionsRequest {
  // Required. The parent resource. The format is `projects/{project}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/ComplaintTransaction"
    }
  ];

  // The maximum number of transactions to return. The service may return fewer
  // than this value. If unspecified or if the specified value is less than 1,
  // at most 50 transactions will be returned. The maximum value is 1000; values
  // above 1000 will be coerced to 1000. While paginating, you can specify a new
  // page size parameter for each page of transactions to be listed.
  int32 page_size = 2;

  // A page token, received from a previous `ListComplaintTransactions` call.
  // Specify this parameter to retrieve the next page of transactions.
  //
  // When paginating, you must specify only the `page_token` parameter. The
  // filter that was specified in the initial call to the
  // `ListComplaintTransactions` method that returned the page token will be
  // reused for all further calls where the page token parameter is specified.
  string page_token = 3;

  // An expression that filters the list of complaint transactions.
  //
  // A filter expression consists of a field name, a comparison operator, and
  // a value for filtering. The value must be a string, a number, or a
  // boolean. The comparison operator must be one of: `<`, `>`, or `=`.
  // Filters are not case sensitive.
  //
  // The following fields in the `Complaint` are eligible for
  // filtering:
  //
  //   * `transactionID` - The transaction ID of the complaint transaction.
  //   Allowed comparison operators: `=`.
  //   * `transactionType` - The transaction type of the complaint
  //   transaction. Must be one of
  //   [TransactionType][google.cloud.paymentgateway.issuerswitch.v1.TransactionType]
  //   values. For complaint transactions, only valid transaction types are
  //  `TRANSACTION_TYPE_CHECK_STATUS`, `TRANSACTION_TYPE_COMPLAINT`,
  //  `TRANSACTION_TYPE_REVERSAL`, `TRANSACTION_TYPE_DISPUTE`,
  //  `TRANSACTION_TYPE_REFUND` or `TRANSACTION_TYPE_STATUS_UPDATE`. Allowed
  //   comparison operators: `=`.
  //   * `originalRRN` - The retrieval reference number of the original
  //   transaction for which complaint / dispute was raised / resolved. Allowed
  //   comparison operators: `=`.
  //   * `createTime` - The time at which the transaction was created
  //   (received) by the issuer switch. The value should be in
  //   the format `YYYY-MM-DDTHH:MM:SSZ`. Allowed comparison
  //   operators: `>`, `<`.
  //   * `state` - The state of the transaction. Must be one of
  //   [TransactionInfo.State][google.cloud.paymentgateway.issuerswitch.v1.TransactionInfo.State]
  //   values. Allowed comparison operators: `=`.
  //   * `errorCode` - Use this filter to list complaint transactions which
  //   have failed a particular error code. Allowed comparison
  //   operators: `=`.
  // You can combine multiple expressions by enclosing each expression in
  // parentheses. Expressions are combined with AND logic. No other logical
  // operators are supported.
  //
  // Here are a few examples:
  //
  //   * `state = SUCCEEDED` - The transaction's state is _SUCCEEDED_.
  //   * (createTime < "2021-08-15T14:50:00Z")`
  //   - The transaction was received before _2021-08-15 14:50:00 UTC_.
  //   * `createTime > "2021-08-15T14:50:00Z" AND createTime <
  //   "2021-08-16T14:50:00Z"` - The transaction was received between
  //   _2021-08-15 14:50:00 UTC_ and _2021-08-16 14:50:00 UTC_.
  string filter = 4;
}

// Response for the `ListMetadataTransactions` method.
message ListMetadataTransactionsResponse {
  // List of non financial metadata transactions satisfying the filtered
  // request.
  repeated MetadataTransaction metadata_transactions = 1;

  // Pass this token in the ListMetadataTransactionsRequest to continue to list
  // results. If all results have been returned, this field is an empty string
  // or not present in the response.
  string next_page_token = 2;
}

// Response for the `ListFinancialTransactions` method.
message ListFinancialTransactionsResponse {
  // List of financial transactions satisfying the filtered request.
  repeated FinancialTransaction financial_transactions = 1;

  // Pass this token in the ListFinancialTransactionsRequest to continue to list
  // results. If all results have been returned, this field is an empty string
  // or not present in the response.
  string next_page_token = 2;
}

// Response for the `ListMandateTransactionsResponse` method.
message ListMandateTransactionsResponse {
  // List of mandate transactions satisfying the filtered request.
  repeated MandateTransaction mandate_transactions = 1;

  // Pass this token in the ListMandateTransactionsRequest to continue to list
  // results. If all results have been returned, this field is an empty string
  // or not present in the response.
  string next_page_token = 2;
}

// Response for the `ListComplaintTransactionsResponse` method.
message ListComplaintTransactionsResponse {
  // List of complaint transactions satisfying the filtered request.
  repeated ComplaintTransaction complaint_transactions = 1;

  // Pass this token in the ListComplaintTransactionsRequest to continue to list
  // results. If all results have been returned, this field is an empty string
  // or not present in the response.
  string next_page_token = 2;
}

// Request for the `ExportFinancialTransactions` method.
message ExportFinancialTransactionsRequest {
  // Required. The parent resource for the transactions. The format is
  // `projects/{project}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Transaction type for the financial transaction API. The possible values for
  // transaction type are
  //
  // * TRANSACTION_TYPE_CREDIT
  // * TRANSACTION_TYPE_DEBIT
  // * TRANSACTION_TYPE_REVERSAL
  //
  // If no transaction type is specified, records of all the above transaction
  // types will be exported.
  TransactionType transaction_type = 2;

  // The start time for the query.
  google.protobuf.Timestamp start_time = 3;

  // The end time for the query.
  google.protobuf.Timestamp end_time = 4;
}

// Request for the `ExportMetadataTransactions` method.
message ExportMetadataTransactionsRequest {
  // Required. The parent resource for the transactions. The format is
  // `projects/{project}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // API type of the metadata transaction API. The possible values for API type
  // are
  //
  // * BALANCE
  // * CHECK_STATUS
  // * HEART_BEAT
  // * INITIATE_REGISTRATION
  // * LIST_ACCOUNTS
  // * UPDATE_CREDENTIALS
  // * VALIDATE_REGISTRATION
  //
  // If no API type is specified, records of all the above API types will be
  // exported.
  ApiType api_type = 2;

  // The start time for the query.
  google.protobuf.Timestamp start_time = 3;

  // The end time for the query.
  google.protobuf.Timestamp end_time = 4;
}

// Request for the `ExportMandateTransactions` method.
message ExportMandateTransactionsRequest {
  // Required. The parent resource for the transactions. The format is
  // `projects/{project}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Transaction type for the mandate transaction API.  The possible values for
  // transaction type are
  //
  // * TRANSACTION_TYPE_CREATE
  // * TRANSACTION_TYPE_REVOKE
  // * TRANSACTION_TYPE_UPDATE
  //
  // If no transaction type is specified, records of all the above transaction
  // types will be exported.
  TransactionType transaction_type = 2;

  // The start time for the query.
  google.protobuf.Timestamp start_time = 3;

  // The end time for the query.
  google.protobuf.Timestamp end_time = 4;
}

// Request for the `ExportComplaintTransactions` method.
message ExportComplaintTransactionsRequest {
  // Required. The parent resource for the transactions. The format is
  // `projects/{project}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Transaction type for the complaint transaction API. The possible values for
  // transaction type are
  //
  // * TRANSACTION_TYPE_CHECK_STATUS
  // * TRANSACTION_TYPE_COMPLAINT
  // * TRANSACTION_TYPE_DISPUTE
  // * TRANSACTION_TYPE_REFUND
  // * TRANSACTION_TYPE_REVERSAL
  // * TRANSACTION_TYPE_STATUS_UPDATE
  //
  // If no transaction type is specified, records of all the above transaction
  // types will be exported.
  TransactionType transaction_type = 2;

  // The start time for the query.
  google.protobuf.Timestamp start_time = 3;

  // The end time for the query.
  google.protobuf.Timestamp end_time = 4;
}

// Response for the `ExportFinancialTransactions` method.
message ExportFinancialTransactionsResponse {
  // URI of the exported file.
  string target_uri = 1;
}

// Response for the `ExportMetadataTransactions` method.
message ExportMetadataTransactionsResponse {
  // URI of the exported file.
  string target_uri = 1;
}

// Response for the `ExportMandateTransactions` method.
message ExportMandateTransactionsResponse {
  // URI of the exported file.
  string target_uri = 1;
}

// Response for the `ExportComplaintTransactions` method.
message ExportComplaintTransactionsResponse {
  // URI of the exported file.
  string target_uri = 1;
}

// Metadata for ExportFinancialTransactions.
message ExportFinancialTransactionsMetadata {
  // Output only. The time at which the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata for ExportMandateTransactions.
message ExportMandateTransactionsMetadata {
  // Output only. The time at which the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata for ExportMetadataTransactions.
message ExportMetadataTransactionsMetadata {
  // Output only. The time at which the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Metadata for ExportComplaintTransactions.
message ExportComplaintTransactionsMetadata {
  // Output only. The time at which the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
