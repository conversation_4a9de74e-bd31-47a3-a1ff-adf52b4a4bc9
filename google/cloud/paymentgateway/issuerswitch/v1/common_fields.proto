// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.paymentgateway.issuerswitch.v1;

import "google/api/field_behavior.proto";
import "google/type/latlng.proto";
import "google/type/money.proto";

option csharp_namespace = "Google.Cloud.PaymentGateway.IssuerSwitch.V1";
option go_package = "cloud.google.com/go/paymentgateway/issuerswitch/apiv1/issuerswitchpb;issuerswitchpb";
option java_multiple_files = true;
option java_outer_classname = "CommonFieldsProto";
option java_package = "com.google.cloud.paymentgateway.issuerswitch.v1";
option php_namespace = "Google\\Cloud\\PaymentGateway\\IssuerSwitch\\V1";
option ruby_package = "Google::Cloud::PaymentGateway::IssuerSwitch::V1";

// A reference to uniquely identify an account according to India's UPI
// standards.
message AccountReference {
  // IFSC of the account's bank branch.
  string ifsc = 1;

  // Output only. Type of account. Examples include SAVINGS, CURRENT, etc.
  string account_type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Unique number for an account in a bank and branch.
  string account_number = 3;
}

// A participant in a payment settlement transaction processed by the issuer
// switch. The participant could either be the payer or the payee in the
// transaction.
message SettlementParticipant {
  // Details about a payment settlement.
  message SettlementDetails {
    // Output only. The id for the settlement in the bank's backend system. In
    // UPI, this maps to the approval reference number. This value will be
    // present only if this API transaction's state is SUCCEEDED.
    string backend_settlement_id = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A code indicating additional details about the settlement.
    // In UPI, this maps to the response code.
    string code = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A code indicating additional details about the reversal of a
    // settlement. In UPI, this maps to the reversal response code.
    string reversal_code = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The amount settled as part of this API transaction. If the
    // settlement had failed, then this value will be 0.00.
    google.type.Money settled_amount = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The participant information.
  Participant participant = 1;

  // Information about a merchant who is a participant in the payment. This
  // field will be specified only if the participant is a merchant.
  MerchantInfo merchant_info = 2;

  // Output only. The mobile number of the participant.
  string mobile = 3
      [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional details about the payment settlement. Values will
  // be populated depending on whether the settlement transaction succeeded or
  // failed.
  SettlementDetails details = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A participant's device tags
message DeviceDetails {
  // The payment application on the device.
  string payment_app = 1;

  // The capability of the device.
  string capability = 2;

  // The geo-code of the device.
  google.type.LatLng geo_code = 3;

  // The device's ID.
  string id = 4;

  // The device's IP address.
  string ip_address = 5;

  // The coarse location of the device.
  string location = 6;

  // The operating system on the device.
  string operating_system = 7;

  // The device's telecom provider.
  string telecom_provider = 8;

  // The type of device.
  string type = 9;
}

// A participant in a transaction processed by the issuer switch.
message Participant {
  // The type of the participant.
  enum Persona {
    // Unspecified persona.
    PERSONA_UNSPECIFIED = 0;

    // Participant is an entity.
    ENTITY = 1;

    // Participant is a person.
    PERSON = 2;
  }

  // The payment address of the participant. In the UPI system, this will be the
  // virtual payment address (VPA) of the participant.
  string payment_address = 1;

  // The persona of the participant.
  Persona persona = 2;

  // The name of the participant.
  string user = 3;

  // Output only. Unique identification of an account according to India's UPI
  // standards.
  AccountReference account = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The device info of the participant.
  DeviceDetails device_details = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The mobile number of the participant.
  string mobile_number = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A merchant entity participating in a payment settlement transaction.
message MerchantInfo {
  // A unique identifier for the merchant.
  string id = 1;

  // The name of the merchant who is a party in the payment. Includes multiple
  // possible names for the merchant.
  MerchantName merchant = 2;

  // Additional information about the merchant.
  MerchantAdditionalInfo additional_info = 3;
}

// The name of a merchant who is a participant in a payment settlement
// transaction. Includes multiple possible names for the merchant.
message MerchantName {
  // The brand name of the merchant.
  string brand = 1;

  // The merchant's legal name.
  string legal = 2;

  // The franchise name under which the merchant operates.
  string franchise = 3;
}

// Additional merchant information specific to India's UPI requirements.
message MerchantAdditionalInfo {
  // Indicates the merchant's type as a small or large merchant.
  enum Type {
    // Unspecified merchant type.
    TYPE_UNSPECIFIED = 0;

    // Large merchant.
    LARGE = 1;

    // Small merchant.
    SMALL = 2;
  }

  // Indicates whether the merchant is an online or offline merchant.
  enum Genre {
    // Unspecified merchant genre.
    GENRE_UNSPECIFIED = 0;

    // Offline merchant
    OFFLINE = 1;

    // Online merchant.
    ONLINE = 2;
  }

  // Indicates whether the merchant has been onboarded by a bank or an
  // aggregator.
  enum OnboardingType {
    // Unspecified merchant onboarding type.
    ONBOARDING_TYPE_UNSPECIFIED = 0;

    // Onboarded by aggreagator.
    AGGREGATOR = 1;

    // Onboarded by bank.
    BANK = 2;

    // Onboarded by the UPI network.
    NETWORK = 3;

    // Onboarded by the TPAP.
    TPAP = 4;
  }

  // Indicates the ownership type of the merchant.
  enum OwnershipType {
    // Unspecified merchant ownership type.
    OWNERSHIP_TYPE_UNSPECIFIED = 0;

    // Properietary ownership.
    PROPRIETARY = 1;

    // Partnership ownership.
    PARTNERSHIP = 2;

    // Public ownership.
    PUBLIC = 3;

    // Private ownership.
    PRIVATE = 4;

    // Other ownership model.
    OTHERS = 5;
  }

  // Merchant Category Code (MCC) as specified by UPI. This is a four-digit
  // number listed in ISO 18245 for retail financial services.
  string category_code = 1;

  // A unique identifier for the merchant store where the payment settlement
  // transaction occurred.
  string store_id = 2;

  // A unique identifier for the POS terminal in the store where the payment
  // settlement transaction occurred.
  string terminal_id = 3;

  // Indicates the type of merchant.
  Type type = 4;

  // Indicates the genre of the merchant.
  Genre genre = 5;

  // Indicates the merchant's onboarding type.
  OnboardingType onboarding_type = 6;

  // Indicates the merchant's owner type.
  OwnershipType ownership_type = 7;
}

// The API type for a transaction. Every transaction processed by the issuer
// switch will be one of these API types.
enum ApiType {
  // Unspecified API type.
  API_TYPE_UNSPECIFIED = 0;

  // Balance API. Maps to UPI's `BalEnq` API. This is a metadata
  // transaction API.
  BALANCE = 1;

  // Check transaction status API. Maps to UPI's `ChkTxn` API. This is a
  // metadata transaction API.
  CHECK_STATUS = 2;

  // Complaint API. Maps to UPI's `Complaint` API. This is a dispute and issue
  // resolution API.
  COMPLAINT = 3;

  // Heart beat API. Maps to UPI's `Hbt` API. This is a metadata transaction
  // API.
  HEART_BEAT = 4;

  // Initiate registration API. Maps to UPI's `Otp` API. This is a metadata
  // transaction API.
  INITIATE_REGISTRATION = 5;

  // List accounts API. Maps to UPI's `ListAccount` API. This is a metadata
  // transaction API.
  LIST_ACCOUNTS = 6;

  // Mandate API. Maps to UPI's `Mandate` API. This is a metadata transaction
  // API.
  MANDATE = 7;

  // Mandate confirmation API. Maps to UPI's `MandateConfirmation` API. This is
  // a metadata transaction API.
  MANDATE_CONFIRMATION = 8;

  // Payment settlement API. Maps to UPI's `Pay` API. This is a financial
  // transaction API.
  SETTLE_PAYMENT = 9;

  // Update credentials API. Maps to UPI's `SetCre` API. This is a metadata
  // transaction API.
  UPDATE_CREDENTIALS = 10;

  // Validate registration API. Maps to UPI's `RegMob` API. This is a metadata
  // transaction API.
  VALIDATE_REGISTRATION = 11;

  // Validate customer API. Maps to UPI's `ValCust` API. This is a validation
  // API.
  VALIDATE_CUSTOMER = 12;

  // Voucher API. Maps to UPI's `Voucher` API.
  VOUCHER = 13;

  // Voucher confirmation API. Maps to UPI's `VoucherConfirmation` API.
  VOUCHER_CONFIRMATION = 14;

  // Activation API. Maps to UPI's `Activation` API.
  ACTIVATION = 15;
}

// The type of a transaction. Every transaction processed by the issuer switch
// will be one of these transaction types. Transaction types are associated with
// a particular API type. This associated is documented with each value.
enum TransactionType {
  // Unspecified transaction type.
  TRANSACTION_TYPE_UNSPECIFIED = 0;

  // Autoupdate transaction type. This is associated with the `CHECK_STATUS`
  // API type. Maps to UPI's `AUTOUPDATE` type.
  TRANSACTION_TYPE_AUTOUPDATE = 1;

  // Balance check transaction type. This is associated with the
  // `BALANCE_ENQUIRY` API type. Maps to UPI's `BalChk` type.
  TRANSACTION_TYPE_BALANCE_CHECK = 2;

  // Balance enquiry transaction type. This is associated with the
  // `BALANCE_ENQUIRY` API type. Maps to UPI's `BalEnq` type.
  TRANSACTION_TYPE_BALANCE_ENQUIRY = 3;

  // Check status transaction type. This is associated with the `COMPLAINT` API
  // type. Maps to UPI's `CHECKSTATUS` type.
  TRANSACTION_TYPE_CHECK_STATUS = 4;

  // Check transaction type. This is associated with the `CHECK_STATUS` API
  // type. Maps to UPI's `ChkTxn` type.
  TRANSACTION_TYPE_CHECK_TRANSACTION = 5;

  // Complaint transaction type. This is associated with the `COMPLAINT` API
  // type. Maps to UPI's `COMPLAINT` type.
  TRANSACTION_TYPE_COMPLAINT = 6;

  // Create transaction type. This is associated with the `MANDATE` API type.
  // Maps to UPI's `CREATE` type.
  TRANSACTION_TYPE_CREATE = 7;

  // Credit transaction type. This is associated with the `SETTLE_PAYMENT` API
  // type. Maps to UPI's `CREDIT` type.
  TRANSACTION_TYPE_CREDIT = 8;

  // Debit transaction type. This is associated with the `SETTLE_PAYMENT` API
  // type. Maps to UPI's `DEBIT` type.
  TRANSACTION_TYPE_DEBIT = 9;

  // Dispute transaction type. This is associated with the `COMPLAINT` API
  // type. Maps to UPI's `DISPUTE` type.
  TRANSACTION_TYPE_DISPUTE = 10;

  // Heart beat transaction type. This is associated with `HEART_BEAT` API type.
  // Maps to UPI's `Hbt` type.
  TRANSACTION_TYPE_HEART_BEAT = 11;

  // List accounts transaction type. This is associated with `LIST_ACCOUNTS` API
  // type. Maps to UPI's `ListAccount` type.
  TRANSACTION_TYPE_LIST_ACCOUNTS = 12;

  // Mandate notification transaction type. This is associated with the
  // `VALIDATE_CUSTOMER` API type. Maps to UPI's `MandateNotification` type.
  TRANSACTION_TYPE_MANDATE_NOTIFICATION = 13;

  // OTP transaction type. This is associated with the `INITIATE_REGISTRATION`
  // API type. Maps to UPI's `Otp` type.
  TRANSACTION_TYPE_OTP = 14;

  // Pause transaction type. This is associated with the `MANDATE` API type.
  // Maps to UPI's `PAUSE` type.
  TRANSACTION_TYPE_PAUSE = 15;

  // Redeem transaction type. This is associated with the `VOUCHER_CONFIRMATION`
  // API type. Maps to UPI's `REDEEM` type.
  TRANSACTION_TYPE_REDEEM = 16;

  // Refund transaction type. This is associated with the `COMPLAINT` API
  // type. Maps to UPI's `REFUND` type.
  TRANSACTION_TYPE_REFUND = 17;

  // Register mobile transaction type. This is associated with the
  // `VALIDATE_REGISTRATION` API type. Maps to UPI's `RegMob` type.
  TRANSACTION_TYPE_REGISTER_MOBILE = 18;

  // Reversal transaction type. This is associated with the `SETTLE_PAYMENT` and
  // `COMPLAINT` API types. Maps to UPI's `REVERSAL` type.
  TRANSACTION_TYPE_REVERSAL = 19;

  // Revoke transaction type. This is associated with the `MANDATE` API type.
  // Maps to UPI's `REVOKE` type.
  TRANSACTION_TYPE_REVOKE = 20;

  // Status update transaction type. This is associated with the `COMPLAINT` API
  // type. Maps to UPI's `STATUSUPDATE` type.
  TRANSACTION_TYPE_STATUS_UPDATE = 21;

  // Update transaction type. This is associated with the `MANDATE` API type.
  // Maps to UPI's `UNPAUSE` type.
  TRANSACTION_TYPE_UNPAUSE = 22;

  // Update transaction type. This is associated with the `MANDATE` API type.
  // Maps to UPI's `UPDATE` type.
  TRANSACTION_TYPE_UPDATE = 23;

  // Update credentials transaction type. This is associated with
  // `UPDATE_CREDENTIALS` API type. Maps to UPI's `SetCre` type.
  TRANSACTION_TYPE_UPDATE_CREDENTIALS = 24;

  // Validate customer transaction type. This is associated with
  // `VALIDATE_CUSTOMER` API type. Maps to UPI's `ValCust` type.
  TRANSACTION_TYPE_VALIDATE_CUSTOMER = 25;

  // Activation international transaction type. This is associated with
  // 'ACTIVATION' API type. Maps to UPI's `International` type.
  TRANSACTION_TYPE_ACTIVATION_INTERNATIONAL = 26;

  // Activation UPI services transaction type. This is associated with
  // 'ACTIVATION' API type. Maps to UPI's `UPI Services` type.
  TRANSACTION_TYPE_ACTIVATION_UPI_SERVICES = 27;
}

// XmlApiType specifies the API type of the request or response as specified in
// the XML payload.
enum XmlApiType {
  // Unspecified API type.
  XML_API_TYPE_UNSPECIFIED = 0;

  // Balance enquiry request API type. Maps to UPI's `ReqBalEnq` API.
  REQ_BAL_ENQ = 1;

  // Check transaction request API type. Maps to UPI's `ReqChkTxn` API.
  REQ_CHK_TXN = 2;

  // Complaint request API type. Maps to UPI's `ReqComplaint` API.
  REQ_COMPLAINT = 3;

  // Heart beat request API type. Maps to UPI's `ReqHbt` API.
  REQ_HBT = 4;

  // List accounts request API type. Maps to UPI's `ReqListAccount` API.
  REQ_LIST_ACCOUNT = 5;

  // Mandate request  API. Maps to UPI's `ReqMandate` API.
  REQ_MANDATE = 6;

  // Mandate confirmation request API type. Maps to UPI's
  // `ReqMandateConfirmation` API.
  REQ_MANDATE_CONFIRMATION = 7;

  // OTP request API. Maps to UPI's `ReqOtp` API.
  REQ_OTP = 8;

  // Payment settlement request API type. Maps to UPI's `ReqPay` API.
  REQ_PAY = 9;

  // Register mobile request API type. Maps to UPI's `ReqRegMob` API.
  REQ_REG_MOB = 10;

  // Update credentials request API type. Maps to UPI's `ReqSetCre` API.
  REQ_SET_CRE = 11;

  // Validate customer request API type. Maps to UPI's `ReqValCust`.
  REQ_VAL_CUST = 12;

  // Create voucher request API type. Maps to UPI's `ReqVoucher`.
  REQ_VOUCHER = 13;

  // Voucher confirmation request API type. Maps to UPI's
  // `ReqVoucherConfirmation` API.
  REQ_VOUCHER_CONFIRMATION = 14;

  // Transaction confirmation request API type. Maps to UPI's
  // `ReqTxnConfirmation` API.
  REQ_TXN_CONFIRMATION = 15;

  // Balance enquiry response API type. Maps to UPI's `RespBalEnq` API.
  RESP_BAL_ENQ = 16;

  // Check transaction response API type. Maps to UPI's `RespChkTxn` API.
  RESP_CHK_TXN = 17;

  // Complaint response API type. Maps to UPI's `RespComplaint` API.
  RESP_COMPLAINT = 18;

  // Heart beat response API type. Maps to UPI's `RespHbt` API.
  RESP_HBT = 19;

  // List accounts response API type. Maps to UPI's `RespListAccount` API.
  RESP_LIST_ACCOUNT = 20;

  // Mandate response API type. Maps to UPI's `RespMandate` API.
  RESP_MANDATE = 21;

  // Mandate confirmation response API type. Maps to UPI's
  // `RespMandateConfirmation` API.
  RESP_MANDATE_CONFIRMATION = 22;

  // OTP response API. Maps to UPI's `RespOtp` API.
  RESP_OTP = 23;

  // Payment settlement response API type. Maps to UPI's `RespPay` API.
  RESP_PAY = 24;

  // Register mobile response API type. Maps to UPI's `RespRegMob` API.
  RESP_REG_MOB = 25;

  // Update credentials response API type. Maps to UPI's `RespSetCre` API.
  RESP_SET_CRE = 26;

  // Validate customer response API type. Maps to UPI's `RespValCust`.
  RESP_VAL_CUST = 27;

  // Create voucher response API type. Maps to UPI's `RespVoucher`.
  RESP_VOUCHER = 28;

  // Voucher confirmation responseAPI type. Maps to UPI's
  // `RespVoucherConfirmation` API.
  RESP_VOUCHER_CONFIRMATION = 29;

  // Transaction confirmation response API type. Maps to UPI's
  // `RespTxnConfirmation` API.
  RESP_TXN_CONFIRMATION = 30;

  // Activation request API type. Maps to UPI's `ReqActivation` API.
  REQ_ACTIVATION = 31;

  // Activation response API type. Maps to UPI's `RespActivation` API.
  RESP_ACTIVATION = 32;
}
