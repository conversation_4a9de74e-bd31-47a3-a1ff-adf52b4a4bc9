// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.paymentgateway.issuerswitch.accountmanager.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/paymentgateway/issuerswitch/v1/common_fields.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option csharp_namespace = "Google.Cloud.PaymentGateway.IssuerSwitch.AccountManager.V1";
option go_package = "cloud.google.com/go/paymentgateway/issuerswitch/accountmanager/apiv1/accountmanagerpb;accountmanagerpb";
option java_multiple_files = true;
option java_outer_classname = "ManagedAccountsProto";
option java_package = "com.google.cloud.paymentgateway.issuerswitch.accountmanager.v1";
option php_namespace = "Google\\Cloud\\PaymentGateway\\IssuerSwitch\\AccountManager\\V1";
option ruby_package = "Google::Cloud::PaymentGateway::IssuerSwitch::AccountManager::V1";

// Reconciles and provide balance information for an account within the account
// manager.
service ManagedAccounts {
  option (google.api.default_host) = "issuerswitch.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Batch reconcile account balance and return status for each account.
  rpc BatchReconcileManagedAccountBalance(
      BatchReconcileManagedAccountBalanceRequest)
      returns (BatchReconcileManagedAccountBalanceResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/accountManagers/*}/accounts:batchReconcileBalance"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
  }

  // Get information on the account managed by account manager.
  rpc GetManagedAccount(GetManagedAccountRequest) returns (ManagedAccount) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/accountManagers/*/accounts/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Entity representing an account managed by the account manager.
message ManagedAccount {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/ManagedAccount"
    pattern: "projects/{project}/accountManagers/{account_manager}/accounts/{account}"
    plural: "managedAccounts"
    singular: "managedAccount"
  };

  // State of an account.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // Account is active.
    ACTIVE = 1;

    // Account is inactive.
    DEACTIVATED = 2;
  }

  // Reconciliation state of an account.
  enum AccountReconciliationState {
    // Unspecified state.
    ACCOUNT_RECONCILIATION_STATE_UNSPECIFIED = 0;

    // Successful reconciliation.
    SUCCEEDED = 1;

    // Reconciliation failed.
    FAILED = 2;
  }

  // The name of the account which uniquely identifies the account.
  // Format:
  // projects/{project}/accountManagers/{account_manager}/accounts/{account}
  // When account manager is used for managing UPI Lite transactions,
  // `{account}` is the Lite Reference Number (LRN).
  string name = 1;

  // Required. The associated bank account information.
  google.cloud.paymentgateway.issuerswitch.v1.AccountReference
      account_reference = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. State of the account.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Current balance of the account.
  google.type.Money balance = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. State of the last reconciliation done on the account.
  AccountReconciliationState last_reconciliation_state = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time at which last reconciliation was done on the account.
  google.protobuf.Timestamp last_reconciliation_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the account was created by the account
  // manager.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which the account was last updated by the account
  // manager.
  google.protobuf.Timestamp update_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Reconciliation request for an account balance.
message ReconcileManagedAccountBalanceRequest {
  // Required. Account that needs to be reconciled.
  ManagedAccount account = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Expected balance amount for the account.
  google.type.Money expected_balance = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. Timestamp to be taken as reference for reconciling the balance
  // amount.
  google.protobuf.Timestamp reference_time = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `BatchReconcileManagedAccountBalance` method.
message BatchReconcileManagedAccountBalanceRequest {
  // Required. The parent resource. The format is
  // `projects/{project}/accountManagers/{account_manager}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The request message specifying the accounts to reconcile.
  // A maximum of 200 account balances can be reconciled in a batch.
  repeated ReconcileManagedAccountBalanceRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response for the `BatchReconcileManagedAccountBalance` method.
message BatchReconcileManagedAccountBalanceResponse {
  // Accounts whose balances were reconciled.
  repeated ManagedAccount accounts = 1;
}

// Request for the `GetManagedAccount` method.
message GetManagedAccountRequest {
  // Required. The name of the managed account to retrieve.
  // Format:
  // `projects/{project}/accountManagers/{account_manager}/accounts/{account}`
  // When account manager is used for managing UPI Lite transactions, {account}
  // should be the Lite Reference Number (LRN).
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "issuerswitch.googleapis.com/ManagedAccount"
    }
  ];
}
