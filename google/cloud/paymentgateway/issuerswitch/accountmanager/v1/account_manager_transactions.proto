// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.paymentgateway.issuerswitch.accountmanager.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/paymentgateway/issuerswitch/v1/common_fields.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option csharp_namespace = "Google.Cloud.PaymentGateway.IssuerSwitch.AccountManager.V1";
option go_package = "cloud.google.com/go/paymentgateway/issuerswitch/accountmanager/apiv1/accountmanagerpb;accountmanagerpb";
option java_multiple_files = true;
option java_outer_classname = "AccountManagerTransactionsProto";
option java_package = "com.google.cloud.paymentgateway.issuerswitch.accountmanager.v1";
option php_namespace = "Google\\Cloud\\PaymentGateway\\IssuerSwitch\\AccountManager\\V1";
option ruby_package = "Google::Cloud::PaymentGateway::IssuerSwitch::AccountManager::V1";

// Lists and exports transactions processed by the account manager.
service AccountManagerTransactions {
  option (google.api.default_host) = "issuerswitch.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Export transactions received within the specified time range as a
  // file into a configured target location. The returned `Operation` type has
  // the following method-specific fields:
  //
  // - `metadata`:
  // [ExportAccountManagerTransactionsMetadata][google.cloud.paymentgateway.issuerswitch.accountmanager.v1.ExportAccountManagerTransactionsMetadata]
  // - `response`:
  // [ExportAccountManagerTransactionsResponse][google.cloud.paymentgateway.issuerswitch.accountmanager.v1.ExportAccountManagerTransactionsResponse]
  //
  // The exported file will be in the standard CSV format where each row in the
  // file represents a transaction. The file has the following fields in order:
  //
  // 1. `TransactionID`
  //     * **Min Length** - 35 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - Account manager transaction ID.
  // 1. `TransactionType`
  //     * **Min Length** - 22 characters
  //     * **Max Length** - 25 characters
  //     * **Description** - Type of the transaction. This will be one of
  //     `TRANSACTION_TYPE_CREDIT`, `TRANSACTION_TYPE_CREDIT_REVERSAL`,
  //     `TRANSACTION_TYPE_DEBIT` or `TRANSACTION_TYPE_DEBIT_REVERSAL`. When
  //     account manager is used for managing UPI Lite transactions, the CREDIT
  //     transactions would be for Lite account top-ups and DEBIT transactions
  //     could be either for a Lite account disablement where balance is
  //     transferred back the underlying bank account or for a Lite account
  //     financial transaction which happened offline.
  // 1. `AccountID`
  //     * **Min Length** - 35 characters
  //     * **Max Length** - 35 characters
  //     * **Description** - Account ID. When account manager is used for
  //     managing UPI Lite transactions, this column will contain the Lite
  //     Reference Number (LRN) of the UPI Lite account.
  // 1. `State`
  //     * **Min Length** - 6 characters
  //     * **Max Length** - 12 characters
  //     * **Description** - State of the transaction. This will be one of
  //     `SUCCEEDED` or `FAILED`.
  // 1. `RRN`
  //     * **Min Length** - 12 characters
  //     * **Max Length** - 12 characters
  //     * **Description** - Retrieval reference number associated with the
  //     transaction.
  // 1. `PayerVPA`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Virtual Payment Address (VPA) of the payer.
  // 1. `PayerIFSC`
  //     * **Min Length** - 11 characters
  //     * **Max Length** - 11 characters
  //     * **Description** - IFSC of the payer's bank account.
  // 1. `PayerAccountNumber`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 30 characters
  //     * **Description** - Payer's bank account number.
  // 1. `PayeeVPA`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 255 characters
  //     * **Description** - Virtual Payment Address (VPA) of the payee.
  // 1. `PayeeIFSC`
  //     * **Min Length** - 11 characters
  //     * **Max Length** - 11 characters
  //     * **Description** - IFSC of the payee's bank account.
  // 1. `PayeeAccountNumber`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 30 characters
  //     * **Description** - Payee's bank account number.
  // 1. `PayeeMCC`
  //     * **Min Length** - 4 characters
  //     * **Max Length** - 4 characters
  //     * **Description** - Payee's Merchant Category Code (MCC), only if the
  //     payee is a merchant.
  // 1. `PayeeMerchantID`
  //     * **Min Length** - 4 characters
  //     * **Max Length** - 4 characters
  //     * **Description** - Payee's merchant ID, only if the payee is a
  //     merchant.
  // 1. `Currency`
  //     * **Min Length** - 3 characters
  //     * **Max Length** - 3 characters
  //     * **Description** - Currency of the amount involved in the transaction.
  //     The currency codes are defined in ISO 4217.
  // 1. `Amount`
  //     * **Description** - Amount involved in the transaction.
  // 1. `Purpose`
  //     * **Min Length** - 1 characters
  //     * **Max Length** - 4 characters
  //     * **Description** - Purpose code associated with the transaction. When
  //     account manager is used for managing UPI Lite transactions, this column
  //     will contain one of the values from `41` (Lite account creation with
  //     initial topup), `42` (Lite account topup), `43` (Lite account
  //     disablement with balance transfer) or `44` (Lite account online
  //     transaction).
  // 1. `TransactionTime`
  //     * **Min Length** - 20 characters
  //     * **Max Length** - 20 characters
  //     * **Description** - Timestamp (in UTC) indicating the timestamp at
  //     which the transaction took place. The format will be as per RFC-3339.
  //     Example : 2022-11-22T23:00:05Z
  rpc ExportAccountManagerTransactions(ExportAccountManagerTransactionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/accountManagers/*}/transactions:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ExportAccountManagerTransactionsResponse"
      metadata_type: "ExportAccountManagerTransactionsMetadata"
    };
  }

  // List account manager transactions that satisfy specified filter criteria.
  rpc ListAccountManagerTransactions(ListAccountManagerTransactionsRequest)
      returns (ListAccountManagerTransactionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/accountManagers/*}/transactions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Batch reconcile account manager transactions and return status for each
  // transaction.
  rpc BatchReconcileAccountManagerTransactions(
      BatchReconcileAccountManagerTransactionsRequest)
      returns (BatchReconcileAccountManagerTransactionsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/accountManagers/*}/transactions:batchReconcile"
      body: "*"
    };
    option (google.api.method_signature) = "parent,requests";
  }
}

// A transaction processed by the account manager.
message AccountManagerTransaction {
  option (google.api.resource) = {
    type: "issuerswitch.googleapis.com/AccountManagerTransaction"
    pattern: "projects/{project}/accountManagers/{account_manager}/transactions/{transaction}"
    plural: "accountManagerTransactions"
    singular: "accountManagerTransaction"
  };

  // The name of the transaction. This uniquely identifies the
  // transaction. Format of name is
  // projects/{project}/accountManagers/{account_manager}/transactions/{transaction}.
  string name = 1;

  // The account ID for which the transaction was processed.
  string account_id = 2;

  // Information about the transaction.
  AccountManagerTransactionInfo info = 3;

  // The payer in the transaction.
  AccountManagerSettlementParticipant payer = 4;

  // The payee in the transaction.
  AccountManagerSettlementParticipant payee = 5;

  // Reconciliation information for the transaction.
  AccountManagerTransactionReconciliationInfo reconciliation_info = 6;

  // The amount for payment settlement in the transaction.
  google.type.Money amount = 7;
}

// Information about a transaction processed by the account manager.
message AccountManagerTransactionInfo {
  // Specifies the current state of the transaction.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The transaction has successfully completed.
    SUCCEEDED = 1;

    // The transaction has failed.
    FAILED = 2;
  }

  // Common metadata about a transaction.
  message AccountManagerTransactionMetadata {
    // The time at which the transaction took place.
    google.protobuf.Timestamp transaction_time = 1;

    // Output only. The time at which the transaction resource was created by
    // the account manager.
    google.protobuf.Timestamp create_time = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The time at which the transaction resource was last updated
    // by the account manager.
    google.protobuf.Timestamp update_time = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Retrieval reference number (RRN) for the transaction.
    string retrieval_reference_number = 4;

    // The initiation mode of this transaction.
    string initiation_mode = 5;

    // The purpose code of this transaction.
    string purpose_code = 6;
  }

  // All details about any error in the processing of a transaction.
  message AccountManagerTransactionErrorDetails {
    // Output only. Error code of the failed transaction.
    string error_code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Error description for the failed transaction.
    string error_message = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // An identifier that is mandatorily present in every transaction processed
  // via account manager.
  string id = 1;

  // The transaction type.
  AccountManagerTransactionType transaction_type = 3;

  // Output only. The transaction's state.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Metadata about the transaction.
  AccountManagerTransactionMetadata metadata = 6;

  // Output only. Any error details for the current transaction, if the state is
  // `FAILED`.
  AccountManagerTransactionErrorDetails error_details = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A participant in a payment settlement transaction processed by the
// account manager. The participant could either be the payer or the payee
// in the transaction.
message AccountManagerSettlementParticipant {
  // The participant information.
  AccountManagerParticipant participant = 1;

  // Information about a merchant who is a participant in the payment. This
  // field will be specified only if the participant is a merchant.
  AccountManagerMerchantInfo merchant_info = 2;
}

// A participant in a transaction processed by the account manager.
message AccountManagerParticipant {
  // The type of the participant.
  enum Persona {
    // Unspecified persona.
    PERSONA_UNSPECIFIED = 0;

    // Participant is an entity.
    ENTITY = 1;

    // Participant is a person.
    PERSON = 2;
  }

  // The payment address of the participant. In the UPI system, this will be the
  // virtual payment address (VPA) of the participant.
  string payment_address = 1;

  // The persona of the participant.
  Persona persona = 2;

  // Unique identification of an account.
  google.cloud.paymentgateway.issuerswitch.v1.AccountReference account = 3;
}

// A merchant in a transaction processed by the account manager.
message AccountManagerMerchantInfo {
  // Merchant Category Code (MCC) as specified by UPI. This is a four-digit
  // number listed in ISO 18245 for retail financial services.
  string category_code = 1;

  // ID of the merchant.
  string id = 2;
}

// Reconciliation information for a transaction processed by account manager.
message AccountManagerTransactionReconciliationInfo {
  // State of transaction reconciliation.
  enum ReconciliationState {
    // Unspecified state.
    RECONCILIATION_STATE_UNSPECIFIED = 0;

    // Reconciliation was successful.
    SUCCEEDED = 1;

    // Reconciliation failed.
    FAILED = 2;
  }

  // Output only. State of reconciliation.
  ReconciliationState state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Time at which reconciliation was performed for the transaction.
  google.protobuf.Timestamp reconciliation_time = 2;
}

// Request for the `ExportAccountManagerTransactions` method.
message ExportAccountManagerTransactionsRequest {
  // Required. The parent resource for the transactions. The format is
  // `projects/{project}/accountManagers/{account_manager}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Transaction type of an account manager transaction. The possible
  // values for transaction type are
  //
  // * CREDIT
  // * CREDIT_REVERSAL
  // * DEBIT
  // * DEBIT_REVERSAL
  //
  // If no transaction type is specified, records of all the above transaction
  // types will be exported.
  AccountManagerTransactionType transaction_type = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The start time for the query.
  google.protobuf.Timestamp start_time = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The end time for the query.
  google.protobuf.Timestamp end_time = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Request for the `ListAccountManagerTransactions` method. Callers can request
// for transactions to be filtered by the given filter criteria and specified
// pagination parameters.
message ListAccountManagerTransactionsRequest {
  // Required. The parent resource. The format is
  // `projects/{project}/accountManagers/{account_manager}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "issuerswitch.googleapis.com/AccountManagerTransaction"
    }
  ];

  // Optional. The maximum number of transactions to return. The service may
  // return fewer than this value. If unspecified or if the specified value is
  // less than 1, at most 50 transactions will be returned. The maximum value is
  // 1000; values above 1000 will be coerced to 1000. While paginating, you can
  // specify a new page size parameter for each page of transactions to be
  // listed.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous
  // `ListAccountManagerTransactions` call. Specify this parameter to retrieve
  // the next page of transactions.
  //
  // When paginating, you must specify only the `page_token` parameter. The
  // filter that was specified in the initial call to the
  // `ListAccountManagerTransactions` method that returned the page token will
  // be reused for all further calls where the page token parameter is
  // specified.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An expression that filters the list of transactions.
  //
  // A filter expression consists of a field name, a comparison operator, and
  // a value for filtering. The value must be a string, a number, or a
  // boolean. The comparison operator must be one of: `<`, `>`, or `=`.
  // Filters are not case sensitive.
  //
  // The following fields in the `Transaction` are eligible for filtering:
  //
  //   * `accountID` - The account ID. Allowed comparison operators: `=`. When
  //   account manager is used for managing UPI Lite transactions, accountID
  //   should be the Lite Reference Number (LRN).
  //   * `accountNumber` - Bank account number associated with the
  //   account. Allowed comparison operators: `=`.
  //   * `IFSC` - Bank IFSC code associated with the account.
  //   Allowed comparison operators: `=`.
  //   * `RRN` - The retrieval reference number of the transaction. Allowed
  //   comparison operators: `=`.
  //   * `transactionTime` - The timestamp (in UTC) at which the transaction
  //   took place. The value should be in the format `YYYY-MM-DDTHH:MM:SSZ`.
  //   Allowed comparison operators: `>`, `<`.
  //
  // You can combine multiple expressions by enclosing each expression in
  // parentheses. Expressions are combined with AND logic. No other logical
  // operators are supported.
  //
  // Here are a few examples:
  //
  //   * `rrn = ********9123` - The RRN is _********9123_.
  //   * `(accountID = ********) AND (transactionTime < "2021-08-15T14:50:00Z")`
  //   - The accountID is ******** and the transaction was received
  //   before _2021-08-15 14:50:00 UTC_.
  //   * `transactionTime > "2021-08-15T14:50:00Z" AND transactionTime <
  //   "2021-08-16T14:50:00Z"` - The transaction was received between
  //   _2021-08-15 14:50:00 UTC_ and _2021-08-16 14:50:00 UTC_.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response for the `ListAccountManagerTransactions` method.
message ListAccountManagerTransactionsResponse {
  // List of account manager transactions satisfying the filtered request.
  repeated AccountManagerTransaction account_manager_transactions = 1;

  // Pass this token in the ListAccountManagerTransactionsRequest to continue to
  // list results. If all results have been returned, this field is an empty
  // string or not present in the response.
  string next_page_token = 2;
}

// Reconciliation request for an account manager transaction.
message ReconcileAccountManagerTransactionsRequest {
  // Required. The transaction that will be reconciled.
  AccountManagerTransaction transaction = 1
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `BatchReconcileAccountManagerTransactions` method.
message BatchReconcileAccountManagerTransactionsRequest {
  // Required. The parent resource. The format is
  // `projects/{project}/accountManagers/{account_manager}`.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The request message specifying the transaction to reconcile.
  // A maximum of 200 transactions can be reconciled in a batch.
  repeated ReconcileAccountManagerTransactionsRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response for the `BatchReconcileAccountManagerTransactions` method.
message BatchReconcileAccountManagerTransactionsResponse {
  // List of transactions reconciled.
  repeated AccountManagerTransaction account_manager_transactions = 1;
}

// The type of a account manager transaction. Every transaction processed by the
// account manager will be one of these transaction types.
enum AccountManagerTransactionType {
  // Unspecified transaction type.
  ACCOUNT_MANAGER_TRANSACTION_TYPE_UNSPECIFIED = 0;

  // Credit transaction type.
  CREDIT = 1;

  // Credit reversal transaction type.
  CREDIT_REVERSAL = 2;

  // Debit transaction type.
  DEBIT = 3;

  // Debit reversal transaction type.
  DEBIT_REVERSAL = 4;
}
