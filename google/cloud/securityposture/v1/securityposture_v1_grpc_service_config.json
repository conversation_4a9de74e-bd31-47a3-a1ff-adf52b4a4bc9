{"methodConfig": [{"name": [{"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "ListPostures"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "ListPostureRevisions"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "GetPosture"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "CreatePosture"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "UpdatePosture"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "DeletePosture"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "ExtractPosture"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "ListPostureDeployments"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "GetPostureDeployment"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "CreatePostureDeployment"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "UpdatePostureDeployment"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "DeletePostureDeployment"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "ListPostureTemplates"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "GetPostureTemplate"}, {"service": "google.cloud.securityposture.v1.SecurityPosture", "method": "CreateIaCValidationReport"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}