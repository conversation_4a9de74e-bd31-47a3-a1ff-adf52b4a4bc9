// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securityposture.v1;

import "google/api/field_behavior.proto";
import "google/cloud/securityposture/v1/org_policy_config.proto";

option go_package = "cloud.google.com/go/securityposture/apiv1/securityposturepb;securityposturepb";
option java_multiple_files = true;
option java_outer_classname = "OrgPolicyConstraintsProto";
option java_package = "com.google.cloud.securityposture.v1";

// Message for Org Policy Canned Constraint.
message OrgPolicyConstraint {
  // Required. Org Policy Canned Constraint id.
  string canned_constraint_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Org PolicySpec rules.
  repeated PolicyRule policy_rules = 2 [(google.api.field_behavior) = REQUIRED];
}

// Message for Org Policy Custom Constraint.
message OrgPolicyConstraintCustom {
  // Required. Org Policy Custom Constraint.
  CustomConstraint custom_constraint = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Org Policyspec rules.
  repeated PolicyRule policy_rules = 2 [(google.api.field_behavior) = REQUIRED];
}
