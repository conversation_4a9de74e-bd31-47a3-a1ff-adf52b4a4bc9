// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securityposture.v1;

import "google/api/field_behavior.proto";
import "google/cloud/securityposture/v1/sha_custom_config.proto";

option go_package = "cloud.google.com/go/securityposture/apiv1/securityposturepb;securityposturepb";
option java_multiple_files = true;
option java_outer_classname = "ShaConstraintsProto";
option java_package = "com.google.cloud.securityposture.v1";

// Possible enablement states of a service or module.
enum EnablementState {
  // Default value. This value is unused.
  ENABLEMENT_STATE_UNSPECIFIED = 0;

  // State is enabled.
  ENABLED = 1;

  // State is disabled.
  DISABLED = 2;
}

// Message for Security Health Analytics built-in detector.
message SecurityHealthAnalyticsModule {
  // Required. The name of the module eg: BIGQUERY_TABLE_CMEK_DISABLED.
  string module_name = 1 [(google.api.field_behavior) = REQUIRED];

  // The state of enablement for the module at its level of the resource
  // hierarchy.
  EnablementState module_enablement_state = 2;
}

// Message for SHA Custom Module
message SecurityHealthAnalyticsCustomModule {
  // Output only. Immutable. The id of the custom module.
  // The id is server-generated and is not user settable.
  // It will be a numeric id containing 1-20 digits.
  string id = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. The display name of the Security Health Analytics custom module.
  // This display name becomes the finding category for all findings that are
  // returned by this custom module. The display name must be between 1 and
  // 128 characters, start with a lowercase letter, and contain alphanumeric
  // characters or underscores only.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. custom module details
  CustomConfig config = 3 [(google.api.field_behavior) = REQUIRED];

  // The state of enablement for the module at its level of the resource
  // hierarchy.
  EnablementState module_enablement_state = 4;
}
