// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securityposture.v1;

import "google/api/field_behavior.proto";
import "google/type/expr.proto";

option go_package = "cloud.google.com/go/securityposture/apiv1/securityposturepb;securityposturepb";
option java_multiple_files = true;
option java_outer_classname = "ShaCustomConfigProto";
option java_package = "com.google.cloud.securityposture.v1";

// Defines the properties in a custom module configuration for Security
// Health Analytics. Use the custom module configuration to create custom
// detectors that generate custom findings for resources that you specify.
message CustomConfig {
  // A set of optional name-value pairs that define custom source properties to
  // return with each finding that is generated by the custom module. The custom
  // source properties that are defined here are included in the finding JSON
  // under `sourceProperties`.
  message CustomOutputSpec {
    // An individual name-value pair that defines a custom source property.
    message Property {
      // Required. Name of the property for the custom output.
      string name = 1 [(google.api.field_behavior) = REQUIRED];

      // Optional. The CEL expression for the custom output. A resource property
      // can be specified to return the value of the property or a text string
      // enclosed in quotation marks.
      google.type.Expr value_expression = 2
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Optional. A list of custom output properties to add to the finding.
    repeated Property properties = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Resource for selecting resource type.
  message ResourceSelector {
    // Required. The resource types to run the detector on.
    repeated string resource_types = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Defines the valid value options for the severity of a finding.
  enum Severity {
    // Unspecified severity.
    SEVERITY_UNSPECIFIED = 0;

    // Critical severity.
    CRITICAL = 1;

    // High severity.
    HIGH = 2;

    // Medium severity.
    MEDIUM = 3;

    // Low severity.
    LOW = 4;
  }

  // Required. The CEL expression to evaluate to produce findings. When the
  // expression evaluates to true against a resource, a finding is generated.
  google.type.Expr predicate = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Custom output properties.
  CustomOutputSpec custom_output = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The resource types that the custom module operates on. Each
  // custom module can specify up to 5 resource types.
  ResourceSelector resource_selector = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. The severity to assign to findings generated by the module.
  Severity severity = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. Text that describes the vulnerability or misconfiguration that
  // the custom module detects. This explanation is returned with each finding
  // instance to help investigators understand the detected issue. The text must
  // be enclosed in quotation marks.
  string description = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An explanation of the recommended steps that security teams can
  // take to resolve the detected issue. This explanation is returned with each
  // finding generated by this module in the `nextSteps` property of the finding
  // JSON.
  string recommendation = 6 [(google.api.field_behavior) = OPTIONAL];
}
