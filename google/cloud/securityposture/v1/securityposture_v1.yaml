type: google.api.Service
config_version: 3
name: securityposture.googleapis.com
title: Security Posture API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.securityposture.v1.SecurityPosture
- name: google.longrunning.Operations

types:
- name: google.cloud.securityposture.v1.OperationMetadata
- name: google.cloud.securityposture.v1.Posture
- name: google.cloud.securityposture.v1.PostureDeployment

documentation:
  summary: Security Posture is a comprehensive framework of policy sets that empowers organizations to define, assess early, deploy, and monitor their security measures in a unified way and helps simplify governance and reduces administrative toil.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=organizations/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=organizations/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=organizations/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=organizations/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=organizations/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=organizations/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.securityposture.v1.SecurityPosture.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=936838&template=1502786
  api_short_name: securityposture
  github_label: 'api: securityposture'
  doc_tag_prefix: securityposture
  organization: CLOUD
  library_settings:
  - version: google.cloud.securityposture.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/security-command-center/docs/security-posture-overview
