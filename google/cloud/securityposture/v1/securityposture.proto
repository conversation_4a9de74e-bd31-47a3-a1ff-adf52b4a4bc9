// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.securityposture.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/securityposture/v1/org_policy_constraints.proto";
import "google/cloud/securityposture/v1/sha_constraints.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Securityposture.V1";
option go_package = "cloud.google.com/go/securityposture/apiv1/securityposturepb;securityposturepb";
option java_multiple_files = true;
option java_outer_classname = "V1mainProto";
option java_package = "com.google.cloud.securityposture.v1";
option php_namespace = "Google\\Cloud\\Securityposture\\V1";
option ruby_package = "Google::Cloud::Securityposture::V1";
option (google.api.resource_definition) = {
  type: "securitposture.googleapis.com/Organization"
  pattern: "organizations/{organization}/locations/{location}"
};

// Service describing handlers for resources.
service SecurityPosture {
  option (google.api.default_host) = "securityposture.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // (-- This option restricts the visibility of the API to only projects that
  // will
  // (-- be labeled as `PREVIEW` or `GOOGLE_INTERNAL` by the service.
  // (-- option (google.api.api_visibility).restriction =
  // "PREVIEW,GOOGLE_INTERNAL"; Postures Lists Postures in a given organization
  // and location. In case a posture has multiple revisions, the latest revision
  // as per UpdateTime will be returned.
  rpc ListPostures(ListPosturesRequest) returns (ListPosturesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/locations/*}/postures"
    };
    option (google.api.method_signature) = "parent";
  }

  // Lists revisions of a Posture in a given organization and location.
  rpc ListPostureRevisions(ListPostureRevisionsRequest)
      returns (ListPostureRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/postures/*}:listRevisions"
    };
  }

  // Gets a posture in a given organization and location.
  // User must provide revision_id to retrieve a specific revision of the
  // resource.
  // NOT_FOUND error is returned if the revision_id or the Posture name does not
  // exist. In case revision_id is not provided then the latest Posture revision
  // by UpdateTime is returned.
  rpc GetPosture(GetPostureRequest) returns (Posture) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/postures/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Posture resource.
  // If a Posture with the specified name already exists in the specified
  // organization and location, the long running operation returns a
  // [ALREADY_EXISTS][google.rpc.Code.ALREADY_EXISTS] error.
  rpc CreatePosture(CreatePostureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*/locations/*}/postures"
      body: "posture"
    };
    option (google.api.method_signature) = "parent,posture,posture_id";
    option (google.longrunning.operation_info) = {
      response_type: "Posture"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an existing Posture.
  // A new revision of the posture will be created if the revision to be
  // updated is currently deployed on a workload.
  // Returns a `google.rpc.Status` with `google.rpc.Code.NOT_FOUND` if the
  // Posture does not exist.
  // Returns a `google.rpc.Status` with `google.rpc.Code.ABORTED` if the etag
  // supplied in the request does not match the persisted etag of the Posture.
  // Updatable fields are state, description and policy_sets.
  // State update operation cannot be clubbed with update of description and
  // policy_sets.
  // An ACTIVE posture can be updated to both DRAFT or DEPRECATED states.
  // Postures in DRAFT or DEPRECATED states can only be updated to ACTIVE state.
  rpc UpdatePosture(UpdatePostureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{posture.name=organizations/*/locations/*/postures/*}"
      body: "posture"
    };
    option (google.api.method_signature) = "posture,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Posture"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes all the revisions of a resource.
  // A posture can only be deleted when none of the revisions are deployed to
  // any workload.
  rpc DeletePosture(DeletePostureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/locations/*/postures/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Extracts existing policies on a workload as a posture.
  // If a Posture on the given workload already exists, the long running
  // operation returns a [ALREADY_EXISTS][google.rpc.Code.ALREADY_EXISTS] error.
  rpc ExtractPosture(ExtractPostureRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*/locations/*}/postures:extract"
      body: "*"
    };
    option (google.api.method_signature) = "parent,posture_id,workload";
    option (google.longrunning.operation_info) = {
      response_type: "Posture"
      metadata_type: "OperationMetadata"
    };
  }

  // PostureDeployments
  // Lists PostureDeployments in a given project and location.
  rpc ListPostureDeployments(ListPostureDeploymentsRequest)
      returns (ListPostureDeploymentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/locations/*}/postureDeployments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single PostureDeployment.
  rpc GetPostureDeployment(GetPostureDeploymentRequest)
      returns (PostureDeployment) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/postureDeployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new PostureDeployment in a given project and location.
  rpc CreatePostureDeployment(CreatePostureDeploymentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=organizations/*/locations/*}/postureDeployments"
      body: "posture_deployment"
    };
    option (google.api.method_signature) =
        "parent,posture_deployment,posture_deployment_id";
    option (google.longrunning.operation_info) = {
      response_type: "PostureDeployment"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single PostureDeployment.
  rpc UpdatePostureDeployment(UpdatePostureDeploymentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{posture_deployment.name=organizations/*/locations/*/postureDeployments/*}"
      body: "posture_deployment"
    };
    option (google.api.method_signature) = "posture_deployment,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "PostureDeployment"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single PostureDeployment.
  rpc DeletePostureDeployment(DeletePostureDeploymentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=organizations/*/locations/*/postureDeployments/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // PostureTemplates
  // Lists all the PostureTemplates available to the user.
  rpc ListPostureTemplates(ListPostureTemplatesRequest)
      returns (ListPostureTemplatesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=organizations/*/locations/*}/postureTemplates"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a PostureTemplate.
  // User must provide revision_id to retrieve a specific revision of the
  // resource.
  // NOT_FOUND error is returned if the revision_id or the PostureTemplate name
  // does not exist. In case revision_id is not provided then the
  // PostureTemplate with latest revision_id is returned.
  rpc GetPostureTemplate(GetPostureTemplateRequest) returns (PostureTemplate) {
    option (google.api.http) = {
      get: "/v1/{name=organizations/*/locations/*/postureTemplates/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This is a output only optional field which will be filled only
  // in cases where PostureDeployments enter failure states like UPDATE_FAILED
  // or CREATE_FAILED or DELETE_FAILED.
  string error_message = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Postures
// Definition of a Posture.
message Posture {
  option (google.api.resource) = {
    type: "securityposture.googleapis.com/Posture"
    pattern: "organizations/{organization}/locations/{location}/postures/{posture}"
    plural: "postures"
    singular: "posture"
    style: DECLARATIVE_FRIENDLY
  };

  // State of a Posture.
  enum State {
    // Unspecified operation state.
    STATE_UNSPECIFIED = 0;

    // The Posture is marked deprecated when it is not in use by the user.
    DEPRECATED = 1;

    // The Posture is created successfully but is not yet ready for usage.
    DRAFT = 2;

    // The Posture state is active. Ready for use/deployments.
    ACTIVE = 3;
  }

  // Required. Identifier. The name of this Posture resource, in the format of
  // organizations/{org_id}/locations/{location_id}/postures/{posture}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Required. State of Posture resource.
  State state = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Immutable. The revision ID of the posture.
  // The format is an 8-character hexadecimal string.
  // https://google.aip.dev/162
  string revision_id = 3 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. The timestamp that the posture was created.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp that the posture was updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User provided description of the posture.
  string description = 6 [(google.api.field_behavior) = OPTIONAL];

  // Required. List of Policy sets.
  repeated PolicySet policy_sets = 7 [(google.api.field_behavior) = REQUIRED];

  // Optional. An opaque tag indicating the current version of the Posture, used
  // for concurrency control. When the `Posture` is returned from either a
  // `GetPosture` or a `ListPostures` request, this `etag` indicates the version
  // of the current `Posture` to use when executing a read-modify-write loop.
  //
  // When the `Posture` is used in a `UpdatePosture` method, use the `etag`
  // value that was returned from a `GetPosture` request as part of a
  // read-modify-write loop for concurrency control. Not setting the `etag` in a
  // `UpdatePosture` request will result in an unconditional write of the
  // `Posture`.
  string etag = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User annotations. These attributes can only be set and used by
  // the user, and not by Google Security Postures.
  // .
  map<string, string> annotations = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Whether or not this Posture is in the process of being
  // updated.
  bool reconciling = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// PolicySet representation.
message PolicySet {
  // Required. ID of the Policy set.
  string policy_set_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description of the Policy set.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. List of policies.
  repeated Policy policies = 3 [(google.api.field_behavior) = REQUIRED];
}

// Policy representation.
message Policy {
  // Mapping for a Policy to standard and control.
  message ComplianceStandard {
    // Optional. The compliance standard that the Policy maps to, e.g.: CIS-2.0.
    string standard = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Control mapping provided by user for this Policy. e.g.: 1.5.
    string control = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. ID of the Policy that is user generated, immutable and unique
  // within the scope of a policy set.
  string policy_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Contains list of mapping for a Policy to a standard and control.
  repeated ComplianceStandard compliance_standards = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Constraint details.
  Constraint constraint = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Description of the Policy.
  string description = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Representation of a Constraint.
message Constraint {
  oneof implementation {
    // Optional. SHA built-in detector.
    SecurityHealthAnalyticsModule security_health_analytics_module = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. SHA custom detector.
    SecurityHealthAnalyticsCustomModule
        security_health_analytics_custom_module = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Org Policy builtin constraint.
    OrgPolicyConstraint org_policy_constraint = 5
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Org Policy custom constraint.
    OrgPolicyConstraintCustom org_policy_constraint_custom = 6
        [(google.api.field_behavior) = OPTIONAL];
  }
}

// Message for requesting list of Postures.
message ListPosturesRequest {
  // Required. Parent value for ListPosturesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securityposture.googleapis.com/Posture"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;
}

// Message for response to listing Postures.
message ListPosturesResponse {
  // The list of Posture.
  repeated Posture postures = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Unreachable resources.
  repeated string unreachable = 3;
}

// Message for requesting list of Posture revisions.
message ListPostureRevisionsRequest {
  // Required. Name value for ListPostureRevisionsRequest.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securityposture.googleapis.com/Posture"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick 100 as default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing PostureRevisions.
message ListPostureRevisionsResponse {
  // The list of Posture revisions.
  repeated Posture revisions = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// Message for getting a Posture.
message GetPostureRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securityposture.googleapis.com/Posture"
    }
  ];

  // Optional. Posture revision which needs to be retrieved.
  string revision_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for creating a Posture.
message CreatePostureRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securityposture.googleapis.com/Posture"
    }
  ];

  // Required. User provided identifier. It should be unique in scope of an
  // Organization and location.
  string posture_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  Posture posture = 3 [(google.api.field_behavior) = REQUIRED];
}

// Message for updating a Posture.
message UpdatePostureRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Posture resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  Posture posture = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Posture revision which needs to be updated.
  string revision_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Message for deleting a Posture.
message DeletePostureRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securityposture.googleapis.com/Posture"
    }
  ];

  // Optional. Etag value of the Posture to be deleted.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for extracting existing policies on a workload as a Posture.
message ExtractPostureRequest {
  // Required. The parent resource name. The format of this value is as follows:
  // `organizations/{organization}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securityposture.googleapis.com/Posture"
    }
  ];

  // Required. User provided identifier. It should be unique in scope of an
  // Organization and location.
  string posture_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Workload from which the policies are to be extracted, it should
  // belong to the same organization defined in parent. The format of this value
  // varies depending on the scope of the request:
  // - `folder/folderNumber`
  // - `project/projectNumber`
  // - `organization/organizationNumber`
  string workload = 3 [(google.api.field_behavior) = REQUIRED];
}

// ========================== PostureDeployments ==========================
// Message describing PostureDeployment resource.
message PostureDeployment {
  option (google.api.resource) = {
    type: "securityposture.googleapis.com/PostureDeployment"
    pattern: "organizations/{organization}/locations/{location}/postureDeployments/{posture_deployment}"
    plural: "postureDeployments"
    singular: "postureDeployment"
    style: DECLARATIVE_FRIENDLY
  };

  // State of a PostureDeployment.
  enum State {
    // Unspecified operation state.
    STATE_UNSPECIFIED = 0;

    // The PostureDeployment is being created.
    CREATING = 1;

    // The PostureDeployment is being deleted.
    DELETING = 2;

    // The PostureDeployment state is being updated.
    UPDATING = 3;

    // The PostureDeployment state is active and in use.
    ACTIVE = 4;

    // The PostureDeployment creation failed.
    CREATE_FAILED = 5;

    // The PostureDeployment update failed.
    UPDATE_FAILED = 6;

    // The PostureDeployment deletion failed.
    DELETE_FAILED = 7;
  }

  // Required. The name of this PostureDeployment resource, in the format of
  // organizations/{organization}/locations/{location_id}/postureDeployments/{postureDeployment}.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Target resource where the Posture will be deployed. Currently
  // supported resources are of types: projects/projectNumber,
  // folders/folderNumber, organizations/organizationNumber.
  string target_resource = 13 [(google.api.field_behavior) = REQUIRED];

  // Output only. State of PostureDeployment resource.
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Posture that needs to be deployed.
  // Format:
  // organizations/{org_id}/locations/{location_id}/postures/<posture>
  // Example:
  // organizations/99/locations/global/postures/les-miserables.
  string posture_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Revision_id of the Posture that is to be deployed.
  string posture_revision_id = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. The timestamp that the PostureDeployment was created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp that the PostureDeployment was updated.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User provided description of the PostureDeployment.
  string description = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An opaque tag indicating the current version of the
  // PostureDeployment, used for concurrency control. When the
  // `PostureDeployment` is returned from either a `GetPostureDeployment` or a
  // `ListPostureDeployments` request, this `etag` indicates the version of the
  // current `PostureDeployment` to use when executing a read-modify-write loop.
  //
  // When the `PostureDeployment` is used in a `UpdatePostureDeployment` method,
  // use the `etag` value that was returned from a `GetPostureDeployment`
  // request as part of a read-modify-write loop for concurrency control. Not
  // setting the `etag` in a `UpdatePostureDeployment` request will result in an
  // unconditional write of the `PostureDeployment`.
  string etag = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User annotations. These attributes can only be set and used by
  // the user, and not by Google Security Postures.
  // .
  map<string, string> annotations = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Whether or not this Posture is in the process of being
  // updated.
  bool reconciling = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This is a output only optional field which will be filled in
  // case where PostureDeployment state is UPDATE_FAILED or CREATE_FAILED or
  // DELETE_FAILED. It denotes the desired Posture.
  string desired_posture_id = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Output only optional field which provides revision_id of the
  // desired_posture_id.
  string desired_posture_revision_id = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This is a output only optional field which will be filled in
  // case where PostureDeployment enters a failure state like UPDATE_FAILED or
  // CREATE_FAILED or DELETE_FAILED.
  string failure_message = 14 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting list of PostureDeployments.
message ListPostureDeploymentsRequest {
  // Required. Parent value for ListPostureDeploymentsRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securityposture.googleapis.com/PostureDeployment"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter to be applied on the resource, defined by EBNF grammar
  // https://google.aip.dev/assets/misc/ebnf-filtering.txt.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing PostureDeployments.
message ListPostureDeploymentsResponse {
  // The list of PostureDeployment.
  repeated PostureDeployment posture_deployments = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a PostureDeployment.
message GetPostureDeploymentRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securityposture.googleapis.com/PostureDeployment"
    }
  ];
}

// Message for creating a PostureDeployment.
message CreatePostureDeploymentRequest {
  // Required. Value for parent.
  // Format: organizations/{org_id}/locations/{location}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securityposture.googleapis.com/PostureDeployment"
    }
  ];

  // Required. User provided identifier. It should be unique in scope of an
  // Organization and location.
  string posture_deployment_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  PostureDeployment posture_deployment = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Message for updating a PostureDeployment.
message UpdatePostureDeploymentRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // PostureDeployment resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  PostureDeployment posture_deployment = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Message for deleting a PostureDeployment.
message DeletePostureDeploymentRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securityposture.googleapis.com/PostureDeployment"
    }
  ];

  // Optional. Etag value of the PostureDeployment to be deleted.
  string etag = 2 [(google.api.field_behavior) = OPTIONAL];
}

// PostureTemplates
// Message describing PostureTemplate object.
message PostureTemplate {
  option (google.api.resource) = {
    type: "securityposture.googleapis.com/PostureTemplate"
    pattern: "organizations/{organization}/locations/{location}/postureTemplates/{posture_template}"
    plural: "postureTemplates"
    singular: "postureTemplate"
  };

  // State of a PostureTemplate
  enum State {
    // Unspecified state
    STATE_UNSPECIFIED = 0;

    // If the Posture template is adhering to the latest controls and standards.
    ACTIVE = 1;

    // If the Posture template controls and standards are outdated and not
    // recommended for use.
    DEPRECATED = 2;
  }

  // Output only. Identifier. The name of the Posture template will be of the
  // format
  // organizations/{organization}/locations/{location}/postureTemplates/{postureTemplate}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. The revision_id of a PostureTemplate.
  string revision_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Description of the Posture template.
  string description = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of PostureTemplate resource.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Policy_sets to be used by the user.
  repeated PolicySet policy_sets = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting list of Posture Templates.
message ListPostureTemplatesRequest {
  // Required. Parent value for ListPostureTemplatesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "securityposture.googleapis.com/PostureTemplate"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter to be applied on the resource, defined by EBNF grammar
  // https://google.aip.dev/assets/misc/ebnf-filtering.txt.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing PostureTemplates.
message ListPostureTemplatesResponse {
  // The list of PostureTemplate.
  repeated PostureTemplate posture_templates = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;
}

// Message for getting a Posture Template.
message GetPostureTemplateRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "securityposture.googleapis.com/PostureTemplate"
    }
  ];

  // Optional. Specific revision_id of a Posture Template.
  // PostureTemplate revision_id which needs to be retrieved.
  string revision_id = 2 [(google.api.field_behavior) = OPTIONAL];
}
