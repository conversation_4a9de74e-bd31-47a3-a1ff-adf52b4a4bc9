// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.blockchainnodeengine.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BlockchainNodeEngine.V1";
option go_package = "cloud.google.com/go/blockchainnodeengine/apiv1/blockchainnodeenginepb;blockchainnodeenginepb";
option java_multiple_files = true;
option java_outer_classname = "BlockchainnodeengineProto";
option java_package = "com.google.cloud.blockchainnodeengine.v1";
option php_namespace = "Google\\Cloud\\BlockchainNodeEngine\\V1";
option ruby_package = "Google::Cloud::BlockchainNodeEngine::V1";

// This service is the control plane API for Blockchain Node Engine,
// and can be used to create, read, and delete blockchain nodes.
service BlockchainNodeEngine {
  option (google.api.default_host) = "blockchainnodeengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists blockchain nodes in a given project and location.
  rpc ListBlockchainNodes(ListBlockchainNodesRequest)
      returns (ListBlockchainNodesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/blockchainNodes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single blockchain node.
  rpc GetBlockchainNode(GetBlockchainNodeRequest) returns (BlockchainNode) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/blockchainNodes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new blockchain node in a given project and location.
  rpc CreateBlockchainNode(CreateBlockchainNodeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/blockchainNodes"
      body: "blockchain_node"
    };
    option (google.api.method_signature) =
        "parent,blockchain_node,blockchain_node_id";
    option (google.longrunning.operation_info) = {
      response_type: "BlockchainNode"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single blockchain node.
  rpc UpdateBlockchainNode(UpdateBlockchainNodeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{blockchain_node.name=projects/*/locations/*/blockchainNodes/*}"
      body: "blockchain_node"
    };
    option (google.api.method_signature) = "blockchain_node,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "BlockchainNode"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single blockchain node.
  rpc DeleteBlockchainNode(DeleteBlockchainNodeRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/blockchainNodes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// A representation of a blockchain node.
message BlockchainNode {
  option (google.api.resource) = {
    type: "blockchainnodeengine.googleapis.com/BlockchainNode"
    pattern: "projects/{project}/locations/{location}/blockchainNodes/{blockchain_node}"
  };

  // The connection information through which to interact with a blockchain
  // node.
  message ConnectionInfo {
    // Contains endpoint information through which to interact with a blockchain
    // node.
    message EndpointInfo {
      // Output only. The assigned URL for the node JSON-RPC API endpoint.
      string json_rpc_api_endpoint = 1
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The assigned URL for the node WebSockets API endpoint.
      string websockets_api_endpoint = 2
          [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Output only. The endpoint information through which to interact with a
    // blockchain node.
    EndpointInfo endpoint_info = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A service attachment that exposes a node, and has the
    // following format:
    // projects/{project}/regions/{region}/serviceAttachments/{service_attachment_name}
    string service_attachment = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Ethereum-specific blockchain node details.
  message EthereumDetails {
    // Options for the Geth execution client.
    //
    // See [Command-line
    // Options](https://geth.ethereum.org/docs/fundamentals/command-line-options)
    // for more details.
    message GethDetails {
      // Blockchain garbage collection modes. Only applicable when `NodeType` is
      // `FULL` or `ARCHIVE`.
      enum GarbageCollectionMode {
        // The garbage collection has not been specified.
        GARBAGE_COLLECTION_MODE_UNSPECIFIED = 0;

        // Configures Geth's garbage collection so that older data not needed
        // for a full node is deleted. This is the default mode when creating a
        // full node.
        FULL = 1;

        // Configures Geth's garbage collection so that old data is never
        // deleted. This is the default mode when creating an archive node. This
        // value can also be chosen when creating a full node in order to create
        // a partial/recent archive node. See
        // [Sync modes](https://geth.ethereum.org/docs/fundamentals/sync-modes)
        // for more details.
        ARCHIVE = 2;
      }

      // Immutable. Blockchain garbage collection mode.
      optional GarbageCollectionMode garbage_collection_mode = 1
          [(google.api.field_behavior) = IMMUTABLE];
    }

    // Contains endpoint information specific to Ethereum nodes.
    message EthereumEndpoints {
      // Output only. The assigned URL for the node's Beacon API endpoint.
      string beacon_api_endpoint = 1
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The assigned URL for the node's Beacon Prometheus metrics
      // endpoint. See [Prometheus
      // Metrics](https://lighthouse-book.sigmaprime.io/advanced_metrics.html)
      // for more details.
      string beacon_prometheus_metrics_api_endpoint = 2
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The assigned URL for the node's execution client's
      // Prometheus metrics endpoint.
      string execution_client_prometheus_metrics_api_endpoint = 3
          [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Configuration for validator-related parameters on the beacon client,
    // and for any GCP-managed validator client.
    message ValidatorConfig {
      // URLs for MEV-relay services to use for block building. When set, a
      // GCP-managed MEV-boost service is configured on the beacon client.
      repeated string mev_relay_urls = 1;

      // Immutable. When true, deploys a GCP-managed validator client alongside
      // the beacon client.
      bool managed_validator_client = 2
          [(google.api.field_behavior) = IMMUTABLE];

      // An Ethereum address which the beacon client will send fee rewards to if
      // no recipient is configured in the validator client.
      //
      // See https://lighthouse-book.sigmaprime.io/suggested-fee-recipient.html
      // or https://docs.prylabs.network/docs/execution-node/fee-recipient for
      // examples of how this is used.
      //
      // Note that while this is often described as "suggested", as we run the
      // execution node we can trust the execution node, and therefore this is
      // considered enforced.
      optional string beacon_fee_recipient = 3;
    }

    // The Ethereum environment being accessed.
    //
    // See [Networks](https://ethereum.org/en/developers/docs/networks)
    // for more details.
    enum Network {
      // The network has not been specified, but should be.
      NETWORK_UNSPECIFIED = 0;

      // The Ethereum Mainnet.
      MAINNET = 1;

      // Deprecated: The Ethereum Testnet based on Goerli protocol. Please
      // use another test network.
      TESTNET_GOERLI_PRATER = 2 [deprecated = true];

      // The Ethereum Testnet based on Sepolia/Bepolia protocol.
      // See https://github.com/eth-clients/sepolia.
      TESTNET_SEPOLIA = 3;

      // The Ethereum Testnet based on Holesky specification.
      // See https://github.com/eth-clients/holesky.
      TESTNET_HOLESKY = 4;
    }

    // The type of Ethereum node.
    //
    // See
    // [Node
    // Types](https://ethereum.org/en/developers/docs/nodes-and-clients/#node-types)
    // for more details.
    enum NodeType {
      // Node type has not been specified, but should be.
      NODE_TYPE_UNSPECIFIED = 0;

      // An Ethereum node that only downloads Ethereum block headers.
      LIGHT = 1;

      // Keeps a complete copy of the blockchain data, and contributes to the
      // network by receiving, validating, and forwarding transactions.
      FULL = 2;

      // Holds the same data as full node as well as all of the blockchain's
      // history state data dating back to the Genesis Block.
      ARCHIVE = 3;
    }

    // The execution client (i.e., Execution Engine or EL client) listens to new
    // transactions broadcast in the network, executes them in EVM, and holds
    // the latest state and database of all current Ethereum data.
    //
    // See
    // [What are nodes and
    // clients?](https://ethereum.org/en/developers/docs/nodes-and-clients/#what-are-nodes-and-clients)
    // for more details.
    enum ExecutionClient {
      // Execution client has not been specified, but should be.
      EXECUTION_CLIENT_UNSPECIFIED = 0;

      // Official Go implementation of the Ethereum protocol.
      // See [go-ethereum](https://geth.ethereum.org/) for details.
      GETH = 1;

      // An implementation of Ethereum (execution client), on the efficiency
      // frontier, written in Go. See [Erigon on
      // GitHub](https://github.com/ledgerwatch/erigon) for details.
      ERIGON = 2;
    }

    // The consensus client (also referred to as beacon node or CL client)
    // implements the proof-of-stake consensus algorithm, which enables the
    // network to achieve agreement based on validated data from the
    // execution client.
    //
    // See
    // [What are nodes and
    // clients?](https://ethereum.org/en/developers/docs/nodes-and-clients/#what-are-nodes-and-clients)
    // for more details.
    enum ConsensusClient {
      // Consensus client has not been specified, but should be.
      CONSENSUS_CLIENT_UNSPECIFIED = 0;

      // Consensus client implementation written in Rust, maintained by Sigma
      // Prime. See [Lighthouse - Sigma
      // Prime](https://lighthouse.sigmaprime.io/) for details.
      LIGHTHOUSE = 1;
    }

    // Options for the execution client.
    oneof execution_client_details {
      // Details for the Geth execution client.
      GethDetails geth_details = 8;
    }

    // Immutable. The Ethereum environment being accessed.
    optional Network network = 1 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The type of Ethereum node.
    optional NodeType node_type = 2 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The execution client
    optional ExecutionClient execution_client = 3
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The consensus client.
    optional ConsensusClient consensus_client = 4
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Enables JSON-RPC access to functions in the `admin` namespace.
    // Defaults to `false`.
    optional bool api_enable_admin = 5
        [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. Enables JSON-RPC access to functions in the `debug` namespace.
    // Defaults to `false`.
    optional bool api_enable_debug = 6
        [(google.api.field_behavior) = IMMUTABLE];

    // Output only. Ethereum-specific endpoint information.
    optional EthereumEndpoints additional_endpoints = 7
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Configuration for validator-related parameters on the beacon client,
    // and for any GCP-managed validator client.
    optional ValidatorConfig validator_config = 10;
  }

  // The blockchain type of the node.
  enum BlockchainType {
    // Blockchain type has not been specified, but should be.
    BLOCKCHAIN_TYPE_UNSPECIFIED = 0;

    // The blockchain type is Ethereum.
    ETHEREUM = 1;
  }

  // All possible states for a given blockchain node.
  enum State {
    // The state has not been specified.
    STATE_UNSPECIFIED = 0;

    // The node has been requested and is in the process of being created.
    CREATING = 1;

    // The existing node is undergoing deletion, but is not yet finished.
    DELETING = 2;

    // The node is running and ready for use.
    RUNNING = 4;

    // The node is in an unexpected or errored state.
    ERROR = 5;

    // The node is currently being updated.
    UPDATING = 6;

    // The node is currently being repaired.
    REPAIRING = 7;

    // The node is currently being reconciled.
    RECONCILING = 8;

    // The node is syncing, which is the process by which it obtains the latest
    // block and current global state.
    SYNCING = 9;
  }

  // Information that is specific to a particular blockchain type.
  oneof blockchain_type_details {
    // Ethereum-specific blockchain node details.
    EthereumDetails ethereum_details = 7;
  }

  // Output only. The fully qualified name of the blockchain node.
  // e.g. `projects/my-project/locations/us-central1/blockchainNodes/my-node`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp at which the blockchain node was first created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp at which the blockchain node was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-provided key-value pairs.
  map<string, string> labels = 4;

  // Immutable. The blockchain type of the node.
  optional BlockchainType blockchain_type = 5
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The connection information used to interact with a blockchain
  // node.
  ConnectionInfo connection_info = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A status representing the state of the node.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. When true, the node is only accessible via Private Service
  // Connect; no public endpoints are exposed. Otherwise, the node is only
  // accessible via public endpoints. Warning: Private Service Connect enabled
  // nodes may require a manual migration effort to remain compatible with
  // future versions of the product. If this feature is enabled, you will be
  // notified of these changes along with any required action to avoid
  // disruption. See https://cloud.google.com/vpc/docs/private-service-connect.
  bool private_service_connect_enabled = 12
      [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of blockchain nodes.
message ListBlockchainNodesRequest {
  // Required. Parent value for `ListNodesRequest`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "blockchainnodeengine.googleapis.com/BlockchainNode"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results.
  string filter = 4;

  // Hint for how to order the results.
  string order_by = 5;
}

// Message for response to listing blockchain nodes.
message ListBlockchainNodesResponse {
  // The list of nodes
  repeated BlockchainNode blockchain_nodes = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a blockchain node.
message GetBlockchainNodeRequest {
  // Required. The fully qualified name of the blockchain node to fetch.
  // e.g. `projects/my-project/locations/us-central1/blockchainNodes/my-node`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "blockchainnodeengine.googleapis.com/BlockchainNode"
    }
  ];
}

// Message for creating a blockchain node.
message CreateBlockchainNodeRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "blockchainnodeengine.googleapis.com/BlockchainNode"
    }
  ];

  // Required. ID of the requesting object.
  string blockchain_node_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created.
  BlockchainNode blockchain_node = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a blockchain node.
message UpdateBlockchainNodeRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Blockchain node resource by the update.
  // The fields specified in the `update_mask` are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  BlockchainNode blockchain_node = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a blockchain node.
message DeleteBlockchainNodeRequest {
  // Required. The fully qualified name of the blockchain node to delete.
  // e.g. `projects/my-project/locations/us-central1/blockchainNodes/my-node`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "blockchainnodeengine.googleapis.com/BlockchainNode"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have `[Operation.error][]` value with a
  // `[google.rpc.Status.code][google.rpc.Status.code]` of `1`, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
