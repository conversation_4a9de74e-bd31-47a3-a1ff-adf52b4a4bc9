type: google.api.Service
config_version: 3
name: gdchardwaremanagement.googleapis.com
title: GDC Hardware Management API

apis:
- name: google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.gdchardwaremanagement.v1alpha.OperationMetadata

documentation:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1alpha/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1alpha/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1alpha/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1alpha/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1alpha/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1alpha/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1563150
  documentation_uri: https://cloud.google.com/distributed-cloud/edge/latest/docs
  api_short_name: gdchardwaremanagement
  github_label: 'api: gdchardwaremanagement'
  doc_tag_prefix: gdchardwaremanagement
  organization: CLOUD
  library_settings:
  - version: google.cloud.gdchardwaremanagement.v1alpha
    launch_stage: ALPHA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/distributed-cloud/edge/latest/docs/reference/hardware/rpc
