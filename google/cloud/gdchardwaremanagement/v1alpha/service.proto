// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gdchardwaremanagement.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/gdchardwaremanagement/v1alpha/resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.GdcHardwareManagement.V1Alpha";
option go_package = "cloud.google.com/go/gdchardwaremanagement/apiv1alpha/gdchardwaremanagementpb;gdchardwaremanagementpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.gdchardwaremanagement.v1alpha";
option php_namespace = "Google\\Cloud\\GdcHardwareManagement\\V1alpha";
option ruby_package = "Google::Cloud::GDCHardwareManagement::V1alpha";

// The GDC Hardware Management service.
service GDCHardwareManagement {
  option (google.api.default_host) = "gdchardwaremanagement.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists orders in a given project and location.
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*}/orders"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of an order.
  rpc GetOrder(GetOrderRequest) returns (Order) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/orders/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new order in a given project and location.
  rpc CreateOrder(CreateOrderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*}/orders"
      body: "order"
    };
    option (google.api.method_signature) = "parent,order,order_id";
    option (google.longrunning.operation_info) = {
      response_type: "Order"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of an order.
  rpc UpdateOrder(UpdateOrderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha/{order.name=projects/*/locations/*/orders/*}"
      body: "order"
    };
    option (google.api.method_signature) = "order,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Order"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes an order.
  rpc DeleteOrder(DeleteOrderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=projects/*/locations/*/orders/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Submits an order.
  rpc SubmitOrder(SubmitOrderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{name=projects/*/locations/*/orders/*}:submit"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Order"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists sites in a given project and location.
  rpc ListSites(ListSitesRequest) returns (ListSitesResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*}/sites"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a site.
  rpc GetSite(GetSiteRequest) returns (Site) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/sites/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new site in a given project and location.
  rpc CreateSite(CreateSiteRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*}/sites"
      body: "site"
    };
    option (google.api.method_signature) = "parent,site,site_id";
    option (google.longrunning.operation_info) = {
      response_type: "Site"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a site.
  rpc UpdateSite(UpdateSiteRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha/{site.name=projects/*/locations/*/sites/*}"
      body: "site"
    };
    option (google.api.method_signature) = "site,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Site"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a site.
  rpc DeleteSite(DeleteSiteRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=projects/*/locations/*/sites/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists hardware groups in a given order.
  rpc ListHardwareGroups(ListHardwareGroupsRequest)
      returns (ListHardwareGroupsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*/orders/*}/hardwareGroups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a hardware group.
  rpc GetHardwareGroup(GetHardwareGroupRequest) returns (HardwareGroup) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/orders/*/hardwareGroups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new hardware group in a given order.
  rpc CreateHardwareGroup(CreateHardwareGroupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*/orders/*}/hardwareGroups"
      body: "hardware_group"
    };
    option (google.api.method_signature) =
        "parent,hardware_group,hardware_group_id";
    option (google.longrunning.operation_info) = {
      response_type: "HardwareGroup"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a hardware group.
  rpc UpdateHardwareGroup(UpdateHardwareGroupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha/{hardware_group.name=projects/*/locations/*/orders/*/hardwareGroups/*}"
      body: "hardware_group"
    };
    option (google.api.method_signature) = "hardware_group,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "HardwareGroup"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a hardware group.
  rpc DeleteHardwareGroup(DeleteHardwareGroupRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=projects/*/locations/*/orders/*/hardwareGroups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists hardware in a given project and location.
  rpc ListHardware(ListHardwareRequest) returns (ListHardwareResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*}/hardware"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets hardware details.
  rpc GetHardware(GetHardwareRequest) returns (Hardware) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/hardware/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates new hardware in a given project and location.
  rpc CreateHardware(CreateHardwareRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*}/hardware"
      body: "hardware"
    };
    option (google.api.method_signature) = "parent,hardware,hardware_id";
    option (google.longrunning.operation_info) = {
      response_type: "Hardware"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates hardware parameters.
  rpc UpdateHardware(UpdateHardwareRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha/{hardware.name=projects/*/locations/*/hardware/*}"
      body: "hardware"
    };
    option (google.api.method_signature) = "hardware,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Hardware"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes hardware.
  rpc DeleteHardware(DeleteHardwareRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=projects/*/locations/*/hardware/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists the comments on an order.
  rpc ListComments(ListCommentsRequest) returns (ListCommentsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*/orders/*}/comments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the content of a comment.
  rpc GetComment(GetCommentRequest) returns (Comment) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/orders/*/comments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new comment on an order.
  rpc CreateComment(CreateCommentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*/orders/*}/comments"
      body: "comment"
    };
    option (google.api.method_signature) = "parent,comment,comment_id";
    option (google.longrunning.operation_info) = {
      response_type: "Comment"
      metadata_type: "OperationMetadata"
    };
  }

  // Record Action on a Comment. If the Action specified in the request is READ,
  // the viewed time in the comment is set to the time the request was received.
  // If the comment is already marked as read, subsequent calls will be ignored.
  // If the Action is UNREAD, the viewed time is cleared from the comment.
  rpc RecordActionOnComment(RecordActionOnCommentRequest) returns (Comment) {
    option (google.api.http) = {
      post: "/v1alpha/{name=projects/*/locations/*/orders/*/comments/*}:recordAction"
      body: "*"
    };
    option (google.api.method_signature) = "name,action_type";
  }

  // Lists the changes made to an order.
  rpc ListChangeLogEntries(ListChangeLogEntriesRequest)
      returns (ListChangeLogEntriesResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*/orders/*}/changeLogEntries"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a change to an order.
  rpc GetChangeLogEntry(GetChangeLogEntryRequest) returns (ChangeLogEntry) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/orders/*/changeLogEntries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists SKUs for a given project and location.
  rpc ListSkus(ListSkusRequest) returns (ListSkusResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*}/skus"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of an SKU.
  rpc GetSku(GetSkuRequest) returns (Sku) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/skus/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists zones in a given project and location.
  rpc ListZones(ListZonesRequest) returns (ListZonesResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*}/zones"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a zone.
  rpc GetZone(GetZoneRequest) returns (Zone) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/zones/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new zone in a given project and location.
  rpc CreateZone(CreateZoneRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*}/zones"
      body: "zone"
    };
    option (google.api.method_signature) = "parent,zone,zone_id";
    option (google.longrunning.operation_info) = {
      response_type: "Zone"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a zone.
  rpc UpdateZone(UpdateZoneRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha/{zone.name=projects/*/locations/*/zones/*}"
      body: "zone"
    };
    option (google.api.method_signature) = "zone,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Zone"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a zone.
  rpc DeleteZone(DeleteZoneRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=projects/*/locations/*/zones/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Signals the state of a zone.
  rpc SignalZoneState(SignalZoneStateRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{name=projects/*/locations/*/zones/*}:signal"
      body: "*"
    };
    option (google.api.method_signature) = "name,state_signal";
    option (google.longrunning.operation_info) = {
      response_type: "Zone"
      metadata_type: "OperationMetadata"
    };
  }
}

// A request to list orders.
message ListOrdersRequest {
  // Required. The project and location to list orders in.
  // Format: `projects/{project}/locations/{location}`
  //
  // To list orders across all locations, substitute `-` (the hyphen or
  // dash character) for the location and check the unreachable field in
  // the response message.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Order"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering condition. See [AIP-160](https://google.aip.dev/160).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A list of orders.
message ListOrdersResponse {
  // The list of orders.
  repeated Order orders = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached. Only used for queries to the wildcard
  // location `-`. If non-empty, it indicates that the results are incomplete.
  repeated string unreachable = 3;
}

// A request to get an order.
message GetOrderRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Order"
    }
  ];
}

// A request to create an order.
message CreateOrderRequest {
  // Required. The project and location to create the order in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Order"
    }
  ];

  // Optional. ID used to uniquely identify the Order within its parent scope.
  // This field should contain at most 63 characters and must start with
  // lowercase characters.
  // Only lowercase characters, numbers and `-` are accepted.
  // The `-` character cannot be the first or the last one.
  // A system generated ID will be used if the field is not set.
  //
  // The order.name field in the request will be ignored.
  string order_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The order to create.
  Order order = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to update an order.
message UpdateOrderRequest {
  // Required. A mask to specify the fields in the Order to overwrite with this
  // update. The fields specified in the update_mask are relative to the order,
  // not the full request. A field will be overwritten if it is in the mask. If
  // you don't provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The order to update.
  Order order = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete an order.
message DeleteOrderRequest {
  // Required. The name of the order.
  // Format: `projects/{project}/locations/{location}/orders/{order}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Order"
    }
  ];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An option to delete any nested resources in the Order, such as a
  // HardwareGroup. If true, any nested resources for this Order will also be
  // deleted. Otherwise, the request will only succeed if the Order has no
  // nested resources.
  bool force = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to submit an order.
message SubmitOrderRequest {
  // Valid types of submit order request.
  enum Type {
    // Request type is unspecified. This should not be used.
    TYPE_UNSPECIFIED = 0;

    // Use this request type to submit your order and initiate conversation with
    // Google. After this submission, you will not be able to modify the number
    // or SKU of your ordered hardware. Please note that this order will not be
    // ready for fulfillment yet until you provide more information, such as
    // zone network configuration, hardware physical and installation
    // information, etc.
    // If you are submitting an order for a SKU type of RACK, please use this
    // request type, as additional information will be required outside of the
    // API.
    INFO_PENDING = 1;

    // Use this request type if and when you are ready to submit your order for
    // fulfillment. In addition to the information required for `INFO_PENDING`,
    // the order must contain all required information, such as zone network
    // configuration, hardware physical and installation information, etc.
    // Further changes to any order information will no longer be allowed.
    INFO_COMPLETE = 2;
  }

  // Required. The name of the order.
  // Format: `projects/{project}/locations/{location}/orders/{order}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Order"
    }
  ];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Type of this request. If unset, the request type is assumed to be
  // `INFO_PENDING`.
  Type type = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to list sites.
message ListSitesRequest {
  // Required. The project and location to list sites in.
  // Format: `projects/{project}/locations/{location}`
  //
  // To list sites across all locations, substitute `-` (the hyphen or
  // dash character) for the location and check the unreachable field in
  // the response message.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Site"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering condition. See [AIP-160](https://google.aip.dev/160).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A list of sites.
message ListSitesResponse {
  // The list of sites.
  repeated Site sites = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached. Only used for queries to the wildcard
  // location `-`. If non-empty, it indicates that the results are incomplete.
  repeated string unreachable = 3;
}

// A request to get a site.
message GetSiteRequest {
  // Required. The name of the site.
  // Format: `projects/{project}/locations/{location}/sites/{site}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Site"
    }
  ];
}

// A request to create a site.
message CreateSiteRequest {
  // Required. The project and location to create the site in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Site"
    }
  ];

  // Optional. ID used to uniquely identify the Site within its parent scope.
  // This field should contain at most 63 characters and must start with
  // lowercase characters.
  // Only lowercase characters, numbers and `-` are accepted.
  // The `-` character cannot be the first or the last one.
  // A system generated ID will be used if the field is not set.
  //
  // The site.name field in the request will be ignored.
  string site_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The site to create.
  Site site = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to update a site.
message UpdateSiteRequest {
  // Required. A mask to specify the fields in the Site to overwrite with this
  // update. The fields specified in the update_mask are relative to the site,
  // not the full request. A field will be overwritten if it is in the mask. If
  // you don't provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The site to update.
  Site site = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete a site.
message DeleteSiteRequest {
  // Required. The name of the site.
  // Format: `projects/{project}/locations/{location}/sites/{site}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Site"
    }
  ];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// A request to list hardware groups.
message ListHardwareGroupsRequest {
  // Required. The order to list hardware groups in.
  // Format: `projects/{project}/locations/{location}/orders/{order}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/HardwareGroup"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering condition. See [AIP-160](https://google.aip.dev/160).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A list of hardware groups.
message ListHardwareGroupsResponse {
  // The list of hardware groups.
  repeated HardwareGroup hardware_groups = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to get a hardware group.
message GetHardwareGroupRequest {
  // Required. The name of the hardware group.
  // Format:
  // `projects/{project}/locations/{location}/orders/{order}/hardwareGroups/{hardware_group}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/HardwareGroup"
    }
  ];
}

// A request to create a hardware group.
message CreateHardwareGroupRequest {
  // Required. The order to create the hardware group in.
  // Format: `projects/{project}/locations/{location}/orders/{order}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/HardwareGroup"
    }
  ];

  // Optional. ID used to uniquely identify the HardwareGroup within its parent
  // scope. This field should contain at most 63 characters and must start with
  // lowercase characters.
  // Only lowercase characters, numbers and `-` are accepted.
  // The `-` character cannot be the first or the last one.
  // A system generated ID will be used if the field is not set.
  //
  // The hardware_group.name field in the request will be ignored.
  string hardware_group_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The hardware group to create.
  HardwareGroup hardware_group = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to update a hardware group.
message UpdateHardwareGroupRequest {
  // Required. A mask to specify the fields in the HardwareGroup to overwrite
  // with this update. The fields specified in the update_mask are relative to
  // the hardware group, not the full request. A field will be overwritten if it
  // is in the mask. If you don't provide a mask then all fields will be
  // overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The hardware group to update.
  HardwareGroup hardware_group = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete a hardware group.
message DeleteHardwareGroupRequest {
  // Required. The name of the hardware group.
  // Format:
  // `projects/{project}/locations/{location}/orders/{order}/hardwareGroups/{hardware_group}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/HardwareGroup"
    }
  ];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A request to list hardware.
message ListHardwareRequest {
  // Required. The project and location to list hardware in.
  // Format: `projects/{project}/locations/{location}`
  //
  // To list hardware across all locations, substitute `-` (the hyphen or
  // dash character) for the location and check the unreachable field in
  // the response message.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Hardware"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering condition. See [AIP-160](https://google.aip.dev/160).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A list of hardware.
message ListHardwareResponse {
  // The list of hardware.
  repeated Hardware hardware = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached. Only used for queries to the wildcard
  // location `-`. If non-empty, it indicates that the results are incomplete.
  repeated string unreachable = 3;
}

// A request to get hardware.
message GetHardwareRequest {
  // Required. The name of the hardware.
  // Format: `projects/{project}/locations/{location}/hardware/{hardware}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Hardware"
    }
  ];
}

// A request to create hardware.
message CreateHardwareRequest {
  // Required. The project and location to create hardware in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Hardware"
    }
  ];

  // Optional. ID used to uniquely identify the Hardware within its parent
  // scope. This field should contain at most 63 characters and must start with
  // lowercase characters.
  // Only lowercase characters, numbers and `-` are accepted.
  // The `-` character cannot be the first or the last one.
  // A system generated ID will be used if the field is not set.
  //
  // The hardware.name field in the request will be ignored.
  string hardware_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The resource to create.
  Hardware hardware = 3 [(google.api.field_behavior) = REQUIRED];
}

// A request to update hardware.
message UpdateHardwareRequest {
  // Required. A mask to specify the fields in the Hardware to overwrite with
  // this update. The fields specified in the update_mask are relative to the
  // hardware, not the full request. A field will be overwritten if it is in the
  // mask. If you don't provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The hardware to update.
  Hardware hardware = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A request to delete hardware.
message DeleteHardwareRequest {
  // Required. The name of the hardware.
  // Format: `projects/{project}/locations/{location}/hardware/{hardware}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Hardware"
    }
  ];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// A request to list comments.
message ListCommentsRequest {
  // Required. The order to list comments on.
  // Format: `projects/{project}/locations/{location}/orders/{order}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Comment"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering condition. See [AIP-160](https://google.aip.dev/160).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A request to list comments.
message ListCommentsResponse {
  // The list of comments.
  repeated Comment comments = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to get a comment.
message GetCommentRequest {
  // Required. The name of the comment.
  // Format:
  // `projects/{project}/locations/{location}/orders/{order}/comments/{comment}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Comment"
    }
  ];
}

// A request to create a comment.
message CreateCommentRequest {
  // Required. The order to create the comment on.
  // Format: `projects/{project}/locations/{location}/orders/{order}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Comment"
    }
  ];

  // Optional. ID used to uniquely identify the Comment within its parent scope.
  // This field should contain at most 63 characters and must start with
  // lowercase characters.
  // Only lowercase characters, numbers and `-` are accepted.
  // The `-` character cannot be the first or the last one.
  // A system generated ID will be used if the field is not set.
  //
  // The comment.name field in the request will be ignored.
  string comment_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The comment to create.
  Comment comment = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A request to record an action on a comment.
message RecordActionOnCommentRequest {
  // Valid action types of Comment.
  enum ActionType {
    // Action is unspecified.
    ACTION_TYPE_UNSPECIFIED = 0;

    // Mark comment as read.
    READ = 1;

    // Mark comment as unread.
    UNREAD = 2;
  }

  // Required. The name of the comment.
  // Format:
  // `projects/{project}/locations/{location}/orders/{order}/comments/{comment}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Comment"
    }
  ];

  // Required. The action type of the recorded action.
  ActionType action_type = 2 [(google.api.field_behavior) = REQUIRED];
}

// A request to list change log entries.
message ListChangeLogEntriesRequest {
  // Required. The order to list change log entries for.
  // Format: `projects/{project}/locations/{location}/orders/{order}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/ChangeLogEntry"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering condition. See [AIP-160](https://google.aip.dev/160).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A list of change log entries.
message ListChangeLogEntriesResponse {
  // The list of change log entries.
  repeated ChangeLogEntry change_log_entries = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A request to get a change log entry.
message GetChangeLogEntryRequest {
  // Required. The name of the change log entry.
  // Format:
  // `projects/{project}/locations/{location}/orders/{order}/changeLogEntries/{change_log_entry}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/ChangeLogEntry"
    }
  ];
}

// A request to list SKUs.
message ListSkusRequest {
  // Required. The project and location to list SKUs in.
  // Format: `projects/{project}/locations/{location}`
  //
  // To list SKUs across all locations, substitute `-` (the hyphen or
  // dash character) for the location and check the unreachable field in
  // the response message.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Sku"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering condition. See [AIP-160](https://google.aip.dev/160).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A list of SKUs.
message ListSkusResponse {
  // The list of SKUs.
  repeated Sku skus = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached. Only used for queries to the wildcard
  // location `-`. If non-empty, it indicates that the results are incomplete.
  repeated string unreachable = 3;
}

// A request to get an SKU.
message GetSkuRequest {
  // Required. The name of the SKU.
  // Format: `projects/{project}/locations/{location}/skus/{sku}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Sku"
    }
  ];
}

// A request to list zones.
message ListZonesRequest {
  // Required. The project and location to list zones in.
  // Format: `projects/{project}/locations/{location}`
  //
  // To list zones across all locations, substitute `-` (the hyphen or
  // dash character) for the location and check the unreachable field in
  // the response message.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Zone"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering condition. See [AIP-160](https://google.aip.dev/160).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// A list of zones.
message ListZonesResponse {
  // The list of zones.
  repeated Zone zones = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached. Only used for queries to the wildcard
  // location `-`. If non-empty, it indicates that the results are incomplete.
  repeated string unreachable = 3;
}

// A request to get a zone.
message GetZoneRequest {
  // Required. The name of the zone.
  // Format: `projects/{project}/locations/{location}/zones/{zone}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Zone"
    }
  ];
}

// A request to create a zone.
message CreateZoneRequest {
  // Required. The project and location to create the zone in.
  // Format: `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "gdchardwaremanagement.googleapis.com/Zone"
    }
  ];

  // Optional. ID used to uniquely identify the Zone within its parent scope.
  // This field should contain at most 63 characters and must start with
  // lowercase characters.
  // Only lowercase characters, numbers and `-` are accepted.
  // The `-` character cannot be the first or the last one.
  // A system generated ID will be used if the field is not set.
  //
  // The zone.name field in the request will be ignored.
  string zone_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The zone to create.
  Zone zone = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// A request to update a zone.
message UpdateZoneRequest {
  // Required. A mask to specify the fields in the Zone to overwrite with this
  // update. The fields specified in the update_mask are relative to the zone,
  // not the full request. A field will be overwritten if it is in the mask. If
  // you don't provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The zone to update.
  Zone zone = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// A request to delete a zone.
message DeleteZoneRequest {
  // Required. The name of the zone.
  // Format: `projects/{project}/locations/{location}/zones/{zone}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Zone"
    }
  ];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// A request to signal the state of a zone.
message SignalZoneStateRequest {
  // Valid state signals for a zone.
  enum StateSignal {
    option allow_alias = true;

    // State signal of the zone is unspecified.
    STATE_SIGNAL_UNSPECIFIED = 0;

    // The Zone is ready for site turnup.
    FACTORY_TURNUP_CHECKS_PASSED = 1;

    // The Zone is ready for site turnup. Deprecated, but not deleted.
    READY_FOR_SITE_TURNUP = 1 [deprecated = true];

    // The Zone failed in factory turnup checks.
    FACTORY_TURNUP_CHECKS_FAILED = 2;
  }

  // Valid provisioning state signals for a zone.
  enum ProvisioningStateSignal {
    // Provisioning state signal is unspecified.
    PROVISIONING_STATE_SIGNAL_UNSPECIFIED = 0;

    // Provisioning is in progress.
    PROVISIONING_IN_PROGRESS = 1;

    // Provisioning is complete.
    PROVISIONING_COMPLETE = 2;
  }

  // Required. The name of the zone.
  // Format: `projects/{project}/locations/{location}/zones/{zone}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Zone"
    }
  ];

  // Optional. An optional unique identifier for this request. See
  // [AIP-155](https://google.aip.dev/155).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. The state signal to send for this zone. Either state_signal or
  // provisioning_state_signal must be set, but not both.
  StateSignal state_signal = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The provisioning state signal to send for this zone. Either
  // state_signal or provisioning_state_signal must be set, but not both.
  ProvisioningStateSignal provisioning_state_signal = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of a long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
