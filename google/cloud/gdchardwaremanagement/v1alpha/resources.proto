// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gdchardwaremanagement.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/datetime.proto";
import "google/type/dayofweek.proto";
import "google/type/postal_address.proto";
import "google/type/timeofday.proto";

option csharp_namespace = "Google.Cloud.GdcHardwareManagement.V1Alpha";
option go_package = "cloud.google.com/go/gdchardwaremanagement/apiv1alpha/gdchardwaremanagementpb;gdchardwaremanagementpb";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.cloud.gdchardwaremanagement.v1alpha";
option php_namespace = "Google\\Cloud\\GdcHardwareManagement\\V1alpha";
option ruby_package = "Google::Cloud::GDCHardwareManagement::V1alpha";

// The power supply options.
enum PowerSupply {
  // Power supply is unspecified.
  POWER_SUPPLY_UNSPECIFIED = 0;

  // AC power supply.
  POWER_SUPPLY_AC = 1;

  // DC power supply.
  POWER_SUPPLY_DC = 2;
}

// Entity is used to denote an organization or party.
enum Entity {
  // Entity is unspecified.
  ENTITY_UNSPECIFIED = 0;

  // Google.
  GOOGLE = 1;

  // Customer.
  CUSTOMER = 2;

  // Vendor.
  VENDOR = 3;
}

// An order for GDC hardware.
message Order {
  option (google.api.resource) = {
    type: "gdchardwaremanagement.googleapis.com/Order"
    pattern: "projects/{project}/locations/{location}/orders/{order}"
    plural: "orders"
    singular: "order"
  };

  // Valid states of an order.
  enum State {
    // State of the order is unspecified.
    STATE_UNSPECIFIED = 0;

    // Order is being drafted by the customer and has not been submitted yet.
    DRAFT = 1;

    // Order has been submitted to Google.
    SUBMITTED = 2;

    // All information required from the customer for fulfillment of the order
    // is complete.
    INFO_COMPLETE = 12;

    // Order has been accepted by Google.
    ACCEPTED = 3;

    // Order needs more information from the customer.
    ADDITIONAL_INFO_NEEDED = 4;

    // Google has initiated building hardware for the order.
    BUILDING = 5;

    // The hardware has been built and is being shipped.
    SHIPPING = 6;

    // The hardware is being installed.
    INSTALLING = 7;

    // An error occurred in processing the order and customer intervention is
    // required.
    FAILED = 8;

    // Order has been partially completed i.e., some hardware have been
    // delivered and installed.
    PARTIALLY_COMPLETED = 9;

    // Order has been completed.
    COMPLETED = 10;

    // Order has been cancelled.
    CANCELLED = 11;
  }

  // Valid types of an Order.
  enum Type {
    // Type of the order is unspecified.
    TYPE_UNSPECIFIED = 0;

    // Paid by the customer.
    PAID = 1;

    // Proof of concept for the customer.
    POC = 2;
  }

  // Identifier. Name of this order.
  // Format: `projects/{project}/locations/{location}/orders/{order}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Display name of this order.
  string display_name = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Time when this order was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this order was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels associated with this order as key value pairs.
  // For more information about labels, see [Create and manage
  // labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels).
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. State of this order. On order creation, state will be set to
  // DRAFT.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Customer contact information.
  OrganizationContact organization_contact = 6
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Customer specified workloads of interest targeted by this order.
  // This must contain <= 20 elements and the length of each element must be <=
  // 50 characters.
  repeated string target_workloads = 7 [(google.api.field_behavior) = OPTIONAL];

  // Required. Information about the customer's motivation for this order. The
  // length of this field must be <= 1000 characters.
  string customer_motivation = 8 [(google.api.field_behavior) = REQUIRED];

  // Required. Customer specified deadline by when this order should be
  // fulfilled.
  google.protobuf.Timestamp fulfillment_time = 9
      [(google.api.field_behavior) = REQUIRED];

  // Required. [Unicode CLDR](http://cldr.unicode.org/) region code where this
  // order will be deployed. For a list of valid CLDR region codes, see the
  // [Language Subtag
  // Registry](https://www.iana.org/assignments/language-subtag-registry/language-subtag-registry).
  string region_code = 10 [(google.api.field_behavior) = REQUIRED];

  // Output only. Link to the order form.
  string order_form_uri = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of this Order.
  Type type = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the order was submitted. Is auto-populated to the
  // current time when an order is submitted.
  google.protobuf.Timestamp submit_time = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The Google Cloud Billing ID to be charged for this order.
  string billing_id = 15 [(google.api.field_behavior) = REQUIRED];

  // Optional. Existing hardware to be removed as part of this order.
  // Note: any hardware removed will be recycled unless otherwise agreed.
  repeated HardwareLocation existing_hardware = 16
      [(google.api.field_behavior) = OPTIONAL];
}

// A physical site where hardware will be installed.
message Site {
  option (google.api.resource) = {
    type: "gdchardwaremanagement.googleapis.com/Site"
    pattern: "projects/{project}/locations/{location}/sites/{site}"
    plural: "sites"
    singular: "site"
  };

  // Identifier. Name of the site.
  // Format: `projects/{project}/locations/{location}/sites/{site}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Display name of this Site.
  string display_name = 24 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of this Site.
  string description = 25 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Time when this site was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this site was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels associated with this site as key value pairs.
  // For more information about labels, see [Create and manage
  // labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels).
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. Contact information for this site.
  OrganizationContact organization_contact = 5
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A URL to the Google Maps address location of the site.
  // An example value is `https://goo.gl/maps/xxxxxxxxx`.
  string google_maps_pin_uri = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The time periods when the site is accessible.
  // If this field is empty, the site is accessible at all times.
  //
  // This field is used by Google to schedule the initial installation as well
  // as any later hardware maintenance. You may update this at any time. For
  // example, if the initial installation is requested during off-hours but
  // maintenance should be performed during regular business hours, you should
  // update the access times after initial installation is complete.
  repeated TimePeriod access_times = 26
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Any additional notes for this Site. Please include information
  // about:
  //  - security or access restrictions
  //  - any regulations affecting the technicians visiting the site
  //  - any special process or approval required to move the equipment
  //  - whether a representative will be available during site visits
  string notes = 27 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Customer defined identifier for this Site. This can be used to
  // identify the site in the customer's own systems.
  string customer_site_id = 28 [(google.api.field_behavior) = OPTIONAL];
}

// A group of hardware that is part of the same order, has the same SKU, and is
// delivered to the same site.
message HardwareGroup {
  option (google.api.resource) = {
    type: "gdchardwaremanagement.googleapis.com/HardwareGroup"
    pattern: "projects/{project}/locations/{location}/orders/{order}/hardwareGroups/{hardware_group}"
    plural: "hardwareGroups"
    singular: "hardwareGroup"
  };

  // Valid states of a HardwareGroup.
  enum State {
    // State of the HardwareGroup is unspecified.
    STATE_UNSPECIFIED = 0;

    // More information is required from the customer to make progress.
    ADDITIONAL_INFO_NEEDED = 1;

    // Google has initiated building hardware for this HardwareGroup.
    BUILDING = 2;

    // The hardware has been built and is being shipped.
    SHIPPING = 3;

    // The hardware is being installed.
    INSTALLING = 4;

    // Some hardware in the HardwareGroup have been installed.
    PARTIALLY_INSTALLED = 5;

    // All hardware in the HardwareGroup have been installed.
    INSTALLED = 6;

    // An error occurred and customer intervention is required.
    FAILED = 7;
  }

  // Identifier. Name of this hardware group.
  // Format:
  // `projects/{project}/locations/{location}/orders/{order}/hardwareGroups/{hardware_group}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Time when this hardware group was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this hardware group was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels associated with this hardware group as key value pairs.
  // For more information about labels, see [Create and manage
  // labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels).
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. Number of hardware in this HardwareGroup.
  int32 hardware_count = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. Configuration for hardware in this HardwareGroup.
  HardwareConfig config = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the site where the hardware in this HardwareGroup will be
  // delivered.
  // Format: `projects/{project}/locations/{location}/sites/{site}`
  string site = 7 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Site"
    }
  ];

  // Output only. Current state of this HardwareGroup.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Name of the zone that the hardware in this HardwareGroup belongs
  // to. Format: `projects/{project}/locations/{location}/zones/{zone}`
  string zone = 9 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Zone"
    }
  ];

  // Optional. Requested installation date for the hardware in this
  // HardwareGroup. Filled in by the customer.
  google.type.Date requested_installation_date = 10
      [(google.api.field_behavior) = OPTIONAL];
}

// An instance of hardware installed at a site.
message Hardware {
  option (google.api.resource) = {
    type: "gdchardwaremanagement.googleapis.com/Hardware"
    pattern: "projects/{project}/locations/{location}/hardware/{hardware}"
    plural: "hardware"
    singular: "hardware"
  };

  // Message to describe the MAC address of a machine.
  message MacAddress {
    // Enum for the different types of MAC address.
    enum AddressType {
      // Unspecified address type.
      ADDRESS_TYPE_UNSPECIFIED = 0;

      // Address of a network interface card.
      NIC = 1;

      // Address of a baseboard management controller.
      BMC = 2;

      // Address of a virtual interface.
      VIRTUAL = 3;
    }

    // Output only. Address string.
    string address = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Address type for this MAC address.
    AddressType type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Static IP address (if used) that is associated with the MAC
    // address. Only applicable for VIRTUAL MAC address type.
    string ipv4_address = 3 [
      (google.api.field_info).format = IPV4,
      (google.api.field_behavior) = OUTPUT_ONLY
    ];
  }

  // Information about individual disks on a machine.
  message DiskInfo {
    // Output only. Disk manufacturer.
    string manufacturer = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Disk slot number.
    int32 slot = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Disk serial number.
    string serial_number = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Disk PSID.
    string psid = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Disk part number.
    string part_number = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Disk model number.
    string model_number = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Information about individual machines vendors will provide during turnup.
  message MachineInfo {
    // Output only. Machine service tag.
    string service_tag = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Each associated MAC address.
    repeated MacAddress mac_addresses = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Machine name.
    string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Information for each disk installed.
    repeated DiskInfo disk_infos = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Valid states for hardware.
  enum State {
    // State of the Hardware is unspecified.
    STATE_UNSPECIFIED = 0;

    // More information is required from the customer to make progress.
    ADDITIONAL_INFO_NEEDED = 1;

    // Google has initiated building hardware for this Hardware.
    BUILDING = 2;

    // The hardware has been built and is being shipped.
    SHIPPING = 3;

    // The hardware is being installed.
    INSTALLING = 4;

    // The hardware has been installed.
    INSTALLED = 5;

    // An error occurred and customer intervention is required.
    FAILED = 6;
  }

  // Identifier. Name of this hardware.
  // Format: `projects/{project}/locations/{location}/hardware/{hardware}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Display name for this hardware.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Time when this hardware was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this hardware was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels associated with this hardware as key value pairs.
  // For more information about labels, see [Create and manage
  // labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels).
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];

  // Required. Name of the order that this hardware belongs to.
  // Format: `projects/{project}/locations/{location}/orders/{order}`
  string order = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Order"
    }
  ];

  // Output only. Name for the hardware group that this hardware belongs to.
  // Format:
  // `projects/{project}/locations/{location}/orders/{order}/hardwareGroups/{hardware_group}`
  string hardware_group = 7 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/HardwareGroup"
    }
  ];

  // Required. Name for the site that this hardware belongs to.
  // Format: `projects/{project}/locations/{location}/sites/{site}`
  string site = 8 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Site"
    }
  ];

  // Output only. Current state for this hardware.
  State state = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Link to the Customer Intake Questionnaire (CIQ) sheet for this
  // Hardware.
  string ciq_uri = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Configuration for this hardware.
  HardwareConfig config = 11 [(google.api.field_behavior) = REQUIRED];

  // Output only. Estimated installation date for this hardware.
  google.type.Date estimated_installation_date = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Physical properties of this hardware.
  HardwarePhysicalInfo physical_info = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Information for installation of this hardware.
  HardwareInstallationInfo installation_info = 14
      [(google.api.field_behavior) = OPTIONAL];

  // Required. Name for the zone that this hardware belongs to.
  // Format: `projects/{project}/locations/{location}/zones/{zone}`
  string zone = 15 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Zone"
    }
  ];

  // Optional. Requested installation date for this hardware. If not specified,
  // this is auto-populated from the order's fulfillment_time upon submission or
  // from the HardwareGroup's requested_installation_date upon order acceptance.
  google.type.Date requested_installation_date = 16
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Actual installation date for this hardware. Filled in by
  // Google.
  google.type.Date actual_installation_date = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Per machine asset information needed for turnup.
  repeated MachineInfo machine_infos = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A comment on an order.
message Comment {
  option (google.api.resource) = {
    type: "gdchardwaremanagement.googleapis.com/Comment"
    pattern: "projects/{project}/locations/{location}/orders/{order}/comments/{comment}"
    plural: "comments"
    singular: "comment"
  };

  // Identifier. Name of this comment.
  // Format:
  // `projects/{project}/locations/{location}/orders/{order}/comments/{comment}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Time when this comment was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels associated with this comment as key value pairs.
  // For more information about labels, see [Create and manage
  // labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels).
  map<string, string> labels = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Username of the author of this comment. This is auto-populated
  // from the credentials used during creation of the comment.
  string author = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Text of this comment. The length of text must be <= 1000
  // characters.
  string text = 5 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp of the first time this comment was viewed by the
  // customer. If the comment wasn't viewed then this timestamp will be unset.
  google.protobuf.Timestamp customer_viewed_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The entity the author belongs to.
  Entity author_entity = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A log entry of a change made to an order.
message ChangeLogEntry {
  option (google.api.resource) = {
    type: "gdchardwaremanagement.googleapis.com/ChangeLogEntry"
    pattern: "projects/{project}/locations/{location}/orders/{order}/changeLogEntries/{change_log_entry}"
    plural: "changeLogEntries"
    singular: "changeLogEntry"
  };

  // Identifier. Name of this change log entry.
  // Format:
  // `projects/{project}/locations/{location}/orders/{order}/changeLogEntries/{change_log_entry}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Time when this change log entry was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels associated with this change log entry as key value pairs.
  // For more information about labels, see [Create and manage
  // labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels).
  map<string, string> labels = 3 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Content of this log entry.
  string log = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A stock keeping unit (SKU) of GDC hardware.
message Sku {
  option (google.api.resource) = {
    type: "gdchardwaremanagement.googleapis.com/Sku"
    pattern: "projects/{project}/locations/{location}/skus/{sku}"
    plural: "skus"
    singular: "sku"
  };

  // Valid types of a SKU.
  enum Type {
    // Type of the SKU is unspecified. This is not an allowed value.
    TYPE_UNSPECIFIED = 0;

    // Rack SKU.
    RACK = 1;

    // Server SKU.
    SERVER = 2;
  }

  // Identifier. Name of this SKU.
  // Format: `projects/{project}/locations/{location}/skus/{sku}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Display name of this SKU.
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this SKU was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this SKU was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Configuration for this SKU.
  SkuConfig config = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Available instances of this SKU. This field should be used for
  // checking availability of a SKU.
  repeated SkuInstance instances = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Description of this SKU.
  string description = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The SKU revision ID.
  // A new revision is created whenever `config` is updated. The format is an
  // 8-character hexadecimal string.
  string revision_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Flag to indicate whether or not this revision is active. Only
  // an active revision can be used in a new Order.
  bool is_active = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of this SKU.
  Type type = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The vCPU count associated with this SKU.
  int32 vcpu_count = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A zone holding a set of hardware.
message Zone {
  option (google.api.resource) = {
    type: "gdchardwaremanagement.googleapis.com/Zone"
    pattern: "projects/{project}/locations/{location}/zones/{zone}"
    plural: "zones"
    singular: "zone"
  };

  // Valid states for a zone.
  enum State {
    // State of the Zone is unspecified.
    STATE_UNSPECIFIED = 0;

    // More information is required from the customer to make progress.
    ADDITIONAL_INFO_NEEDED = 1;

    // Google is preparing the Zone.
    PREPARING = 2;

    // Factory turnup has succeeded.
    READY_FOR_CUSTOMER_FACTORY_TURNUP_CHECKS = 5;

    // The Zone is ready for site turnup.
    READY_FOR_SITE_TURNUP = 6;

    // The Zone failed in factory turnup checks.
    CUSTOMER_FACTORY_TURNUP_CHECKS_FAILED = 7;

    // The Zone is available to use.
    ACTIVE = 3;

    // The Zone has been cancelled.
    CANCELLED = 4;
  }

  // Valid provisioning states for configurations like MAC addresses.
  enum ProvisioningState {
    // Provisioning state is unspecified.
    PROVISIONING_STATE_UNSPECIFIED = 0;

    // Provisioning is required. Set by Google.
    PROVISIONING_REQUIRED = 1;

    // Provisioning is in progress. Set by customer.
    PROVISIONING_IN_PROGRESS = 2;

    // Provisioning is complete. Set by customer.
    PROVISIONING_COMPLETE = 3;
  }

  // Identifier. Name of this zone.
  // Format: `projects/{project}/locations/{location}/zones/{zone}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Time when this zone was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this zone was last updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Labels associated with this zone as key value pairs.
  // For more information about labels, see [Create and manage
  // labels](https://cloud.google.com/resource-manager/docs/creating-managing-labels).
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Human friendly display name of this zone.
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Current state for this zone.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The points of contact.
  repeated Contact contacts = 9 [(google.api.field_behavior) = REQUIRED];

  // Output only. Link to the Customer Intake Questionnaire (CIQ) sheet for this
  // zone.
  string ciq_uri = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Networking configuration for this zone.
  ZoneNetworkConfig network_config = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Globally unique identifier generated for this Edge Zone.
  string globally_unique_id = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Subscription configurations for this zone.
  repeated SubscriptionConfig subscription_configs = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provisioning state for configurations like MAC addresses.
  ProvisioningState provisioning_state = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contact information of the customer organization.
message OrganizationContact {
  // Required. The organization's address.
  google.type.PostalAddress address = 1
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The organization's email.
  string email = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The organization's phone number.
  string phone = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. The individual points of contact in the organization at this
  // location.
  repeated Contact contacts = 4 [(google.api.field_behavior) = REQUIRED];
}

// Contact details of a point of contact.
message Contact {
  // Required. Given name of the contact.
  string given_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Family name of the contact.
  string family_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. Email of the contact.
  string email = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Phone number of the contact.
  string phone = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. Time zone of the contact.
  google.type.TimeZone time_zone = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The time periods when the contact is reachable.
  // If this field is empty, the contact is reachable at all times.
  repeated TimePeriod reachable_times = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for GDC hardware.
message HardwareConfig {
  // Required. Reference to the SKU for this hardware. This can point to a
  // specific SKU revision in the form of `resource_name@revision_id` as defined
  // in [AIP-162](https://google.aip.dev/162). If no revision_id is specified,
  // it refers to the latest revision.
  string sku = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Sku"
    }
  ];

  // Required. Power supply type for this hardware.
  PowerSupply power_supply = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Subscription duration for the hardware in months.
  int32 subscription_duration_months = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for a SKU.
message SkuConfig {
  // Information about CPU configuration.
  string cpu = 1;

  // Information about GPU configuration.
  string gpu = 2;

  // Information about RAM configuration.
  string ram = 3;

  // Information about storage configuration.
  string storage = 4;
}

// A specific instance of the SKU.
message SkuInstance {
  // The [Unicode CLDR](https://cldr.unicode.org) region code where this
  // instance is available.
  string region_code = 1;

  // Power supply type for this instance.
  PowerSupply power_supply = 2;

  // Reference to the corresponding SKU in the Cloud Billing API.
  // The estimated price information can be retrieved using that API.
  // Format: `services/{service}/skus/{sku}`
  string billing_sku = 3;

  // Reference to the corresponding SKU per vCPU in the Cloud Billing API.
  // The estimated price information can be retrieved using that API.
  // Format: `services/{service}/skus/{sku}`
  string billing_sku_per_vcpu = 4;

  // Subscription duration for the hardware in months.
  int32 subscription_duration_months = 5;
}

// Physical properties of a hardware.
message HardwarePhysicalInfo {
  // Valid power receptacle types.
  enum PowerReceptacleType {
    // Facility plug type is unspecified.
    POWER_RECEPTACLE_TYPE_UNSPECIFIED = 0;

    // NEMA 5-15.
    NEMA_5_15 = 1;

    // C13.
    C_13 = 2;

    // Standard european receptacle.
    STANDARD_EU = 3;
  }

  // Valid network uplink types.
  enum NetworkUplinkType {
    // Network uplink type is unspecified.
    NETWORK_UPLINK_TYPE_UNSPECIFIED = 0;

    // RJ-45.
    RJ_45 = 1;
  }

  // Valid voltage values.
  enum Voltage {
    // Voltage is unspecified.
    VOLTAGE_UNSPECIFIED = 0;

    // 120V.
    VOLTAGE_110 = 1;

    // 220V.
    VOLTAGE_220 = 3;
  }

  // Valid amperes values.
  enum Amperes {
    // Amperes is unspecified.
    AMPERES_UNSPECIFIED = 0;

    // 15A.
    AMPERES_15 = 1;
  }

  // Required. The power receptacle type.
  PowerReceptacleType power_receptacle = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Type of the uplink network connection.
  NetworkUplinkType network_uplink = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Voltage of the power supply.
  Voltage voltage = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Amperes of the power supply.
  Amperes amperes = 4 [(google.api.field_behavior) = REQUIRED];
}

// Information for installation of a Hardware.
message HardwareInstallationInfo {
  // Valid rack types.
  enum RackType {
    // Rack type is unspecified.
    RACK_TYPE_UNSPECIFIED = 0;

    // Two post rack.
    TWO_POST = 1;

    // Four post rack.
    FOUR_POST = 2;
  }

  // Required. Location of the rack in the site e.g. Floor 2, Room 201, Row 7,
  // Rack 3.
  string rack_location = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Distance from the power outlet in meters.
  int32 power_distance_meters = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Distance from the network switch in meters.
  int32 switch_distance_meters = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Dimensions of the rack unit.
  Dimensions rack_unit_dimensions = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. Rack space allocated for the hardware.
  RackSpace rack_space = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. Type of the rack.
  RackType rack_type = 6 [(google.api.field_behavior) = REQUIRED];
}

// Networking configuration for a zone.
message ZoneNetworkConfig {
  // Required. An IPv4 address block for machine management.
  // Should be a private RFC1918 or public CIDR block large enough to allocate
  // at least one address per machine in the Zone.
  // Should be in `management_ipv4_subnet`, and disjoint with other address
  // ranges.
  string machine_mgmt_ipv4_range = 1 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = REQUIRED
  ];

  // Required. An IPv4 address block for kubernetes nodes.
  // Should be a private RFC1918 or public CIDR block large enough to allocate
  // at least one address per machine in the Zone.
  // Should be in `kubernetes_ipv4_subnet`, and disjoint with other address
  // ranges.
  string kubernetes_node_ipv4_range = 2 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = REQUIRED
  ];

  // Required. An IPv4 address block for kubernetes control plane.
  // Should be a private RFC1918 or public CIDR block large enough to allocate
  // at least one address per cluster in the Zone.
  // Should be in `kubernetes_ipv4_subnet`, and disjoint with other address
  // ranges.
  string kubernetes_control_plane_ipv4_range = 3 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = REQUIRED
  ];

  // Required. An IPv4 subnet for the management network.
  Subnet management_ipv4_subnet = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. An IPv4 subnet for the kubernetes network.
  // If unspecified, the kubernetes subnet will be the same as the management
  // subnet.
  Subnet kubernetes_ipv4_subnet = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. DNS nameservers.
  // The GDC Infrastructure will resolve DNS queries via these IPs.
  // If unspecified, Google DNS is used.
  repeated string dns_ipv4_addresses = 6 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Kubernetes VLAN ID.
  // By default, the kubernetes node, including the primary kubernetes network,
  // are in the same VLAN as the machine management network.
  // For network segmentation purposes, these can optionally be separated.
  int32 kubernetes_primary_vlan_id = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Represents a subnet.
message Subnet {
  // Required. Address range for this subnet in CIDR notation.
  string address_range = 1 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = REQUIRED
  ];

  // Required. Default gateway for this subnet.
  string default_gateway_ip_address = 2 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = REQUIRED
  ];
}

// Represents a time period in a week.
message TimePeriod {
  // Required. The start of the time period.
  google.type.TimeOfDay start_time = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The end of the time period.
  google.type.TimeOfDay end_time = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The days of the week that the time period is active.
  repeated google.type.DayOfWeek days = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Represents the dimensions of an object.
message Dimensions {
  // Required. Width in inches.
  float width_inches = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Height in inches.
  float height_inches = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Depth in inches.
  float depth_inches = 3 [(google.api.field_behavior) = REQUIRED];
}

// Represents contiguous space in a rack.
message RackSpace {
  // Required. First rack unit of the rack space (inclusive).
  int32 start_rack_unit = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Last rack unit of the rack space (inclusive).
  int32 end_rack_unit = 2 [(google.api.field_behavior) = REQUIRED];
}

// Represents the location of one or many hardware.
message HardwareLocation {
  // Required. Name of the site where the hardware are present.
  // Format: `projects/{project}/locations/{location}/sites/{site}`
  string site = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gdchardwaremanagement.googleapis.com/Site"
    }
  ];

  // Required. Location of the rack in the site e.g. Floor 2, Room 201, Row 7,
  // Rack 3.
  string rack_location = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Spaces occupied by the hardware in the rack.
  // If unset, this location is assumed to be the entire rack.
  repeated RackSpace rack_space = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A message to store a subscription configuration.
message SubscriptionConfig {
  // Enum to represent the state of the subscription.
  enum SubscriptionState {
    // State is unspecified.
    SUBSCRIPTION_STATE_UNSPECIFIED = 0;

    // Active state means that the subscription has been created successfully
    // and billing is happening.
    ACTIVE = 1;

    // Inactive means that the subscription has been created successfully, but
    // billing has not started yet.
    INACTIVE = 2;

    // The subscription is in an erroneous state.
    ERROR = 3;

    // The subscription state failed to be retrieved. This may be a transient
    // issue. The user should retry the request.
    FAILED_TO_RETRIEVE = 4;

    // The subscription has been completed, because it has reached the end date.
    COMPLETED = 5;
  }

  // Output only. The unique identifier of the subscription.
  string subscription_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Google Cloud Billing ID that the subscription is created
  // under.
  string billing_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the subscription.
  SubscriptionState state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
