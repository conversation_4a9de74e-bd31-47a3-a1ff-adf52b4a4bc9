{"methodConfig": [{"name": [{"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "DeleteHardware"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "DeleteHardwareGroup"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "DeleteOrder"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "DeleteSite"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "DeleteZone"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "GetChangeLogEntry"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "GetComment"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "GetHardware"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "GetHardwareGroup"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "GetOrder"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "GetSite"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "GetSku"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "GetZone"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "ListChangeLogEntries"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "ListComments"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "ListHardware"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "ListHardwareGroups"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "ListOrders"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "ListSites"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "ListSkus"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "ListZones"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "SubmitOrder"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "UpdateHardware"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "UpdateHardwareGroup"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "UpdateOrder"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "UpdateSite"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "UpdateZone"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "CreateComment"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "CreateHardware"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "CreateHardwareGroup"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "CreateOrder"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "CreateSite"}, {"service": "google.cloud.gdchardwaremanagement.v1alpha.GDCHardwareManagement", "method": "CreateZone"}], "timeout": "60s"}]}