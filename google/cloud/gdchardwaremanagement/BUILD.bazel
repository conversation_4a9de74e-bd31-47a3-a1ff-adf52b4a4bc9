# This build file includes a target for the Ruby wrapper library for
# google-cloud-gdc_hardware_management.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for gdchardwaremanagement.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1alpha in this case.
ruby_cloud_gapic_library(
    name = "gdchardwaremanagement_ruby_wrapper",
    srcs = ["//google/cloud/gdchardwaremanagement/v1alpha:gdchardwaremanagement_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-gdc_hardware_management",
        "ruby-cloud-wrapper-of=v1alpha:0.0",
        "ruby-cloud-gem-namespace=Google::Cloud::GDCHardwareManagement",
        "ruby-cloud-service-override=GdcHardwareManagement=GDCHardwareManagement",
    ],
    service_yaml = "//google/cloud/gdchardwaremanagement/v1alpha:gdchardwaremanagement_v1alpha.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-gdchardwaremanagement-ruby",
    deps = [
        ":gdchardwaremanagement_ruby_wrapper",
    ],
)