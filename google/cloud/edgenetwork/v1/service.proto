// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.edgenetwork.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/edgenetwork/v1/resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.EdgeNetwork.V1";
option go_package = "cloud.google.com/go/edgenetwork/apiv1/edgenetworkpb;edgenetworkpb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.edgenetwork.v1";
option php_namespace = "Google\\Cloud\\EdgeNetwork\\V1";
option ruby_package = "Google::Cloud::EdgeNetwork::V1";

// EdgeNetwork API provides managed, highly available cloud dynamic network
// configuration service to the GEC customer to enable edge application and
// network function solutions. This allows the customers to easily define and
// configure the network setup and property to meet the workload requirement.
service EdgeNetwork {
  option (google.api.default_host) = "edgenetwork.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // InitializeZone will initialize resources for a zone in a project.
  rpc InitializeZone(InitializeZoneRequest) returns (InitializeZoneResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/zones/*}:initialize"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Deprecated: not implemented.
  // Lists Zones in a given project and location.
  rpc ListZones(ListZonesRequest) returns (ListZonesResponse) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/zones"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deprecated: not implemented.
  // Gets details of a single Zone.
  rpc GetZone(GetZoneRequest) returns (Zone) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/zones/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Networks in a given project and location.
  rpc ListNetworks(ListNetworksRequest) returns (ListNetworksResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/zones/*}/networks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Network.
  rpc GetNetwork(GetNetworkRequest) returns (Network) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/zones/*/networks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Get the diagnostics of a single network resource.
  rpc DiagnoseNetwork(DiagnoseNetworkRequest)
      returns (DiagnoseNetworkResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/zones/*/networks/*}:diagnose"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Network in a given project and location.
  rpc CreateNetwork(CreateNetworkRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/zones/*}/networks"
      body: "network"
    };
    option (google.api.method_signature) = "parent,network,network_id";
    option (google.longrunning.operation_info) = {
      response_type: "Network"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Network.
  rpc DeleteNetwork(DeleteNetworkRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/zones/*/networks/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Subnets in a given project and location.
  rpc ListSubnets(ListSubnetsRequest) returns (ListSubnetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/zones/*}/subnets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Subnet.
  rpc GetSubnet(GetSubnetRequest) returns (Subnet) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/zones/*/subnets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Subnet in a given project and location.
  rpc CreateSubnet(CreateSubnetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/zones/*}/subnets"
      body: "subnet"
    };
    option (google.api.method_signature) = "parent,subnet,subnet_id";
    option (google.longrunning.operation_info) = {
      response_type: "Subnet"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Subnet.
  rpc UpdateSubnet(UpdateSubnetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{subnet.name=projects/*/locations/*/zones/*/subnets/*}"
      body: "subnet"
    };
    option (google.api.method_signature) = "subnet,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Subnet"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Subnet.
  rpc DeleteSubnet(DeleteSubnetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/zones/*/subnets/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Interconnects in a given project and location.
  rpc ListInterconnects(ListInterconnectsRequest)
      returns (ListInterconnectsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/zones/*}/interconnects"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Interconnect.
  rpc GetInterconnect(GetInterconnectRequest) returns (Interconnect) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/zones/*/interconnects/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Get the diagnostics of a single interconnect resource.
  rpc DiagnoseInterconnect(DiagnoseInterconnectRequest)
      returns (DiagnoseInterconnectResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/zones/*/interconnects/*}:diagnose"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists InterconnectAttachments in a given project and location.
  rpc ListInterconnectAttachments(ListInterconnectAttachmentsRequest)
      returns (ListInterconnectAttachmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/zones/*}/interconnectAttachments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single InterconnectAttachment.
  rpc GetInterconnectAttachment(GetInterconnectAttachmentRequest)
      returns (InterconnectAttachment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/zones/*/interconnectAttachments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new InterconnectAttachment in a given project and location.
  rpc CreateInterconnectAttachment(CreateInterconnectAttachmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/zones/*}/interconnectAttachments"
      body: "interconnect_attachment"
    };
    option (google.api.method_signature) =
        "parent,interconnect_attachment,interconnect_attachment_id";
    option (google.longrunning.operation_info) = {
      response_type: "InterconnectAttachment"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single InterconnectAttachment.
  rpc DeleteInterconnectAttachment(DeleteInterconnectAttachmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/zones/*/interconnectAttachments/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Routers in a given project and location.
  rpc ListRouters(ListRoutersRequest) returns (ListRoutersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/zones/*}/routers"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Router.
  rpc GetRouter(GetRouterRequest) returns (Router) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/zones/*/routers/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Get the diagnostics of a single router resource.
  rpc DiagnoseRouter(DiagnoseRouterRequest) returns (DiagnoseRouterResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/zones/*/routers/*}:diagnose"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Router in a given project and location.
  rpc CreateRouter(CreateRouterRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/zones/*}/routers"
      body: "router"
    };
    option (google.api.method_signature) = "parent,router,router_id";
    option (google.longrunning.operation_info) = {
      response_type: "Router"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Router.
  rpc UpdateRouter(UpdateRouterRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{router.name=projects/*/locations/*/zones/*/routers/*}"
      body: "router"
    };
    option (google.api.method_signature) = "router,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Router"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Router.
  rpc DeleteRouter(DeleteRouterRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/zones/*/routers/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Deprecated: not implemented.
// Message for requesting list of Zones
message ListZonesRequest {
  option deprecated = true;

  // Required. Parent value for ListZonesRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgenetwork.googleapis.com/Zone"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Deprecated: not implemented.
// Message for response to listing Zones
message ListZonesResponse {
  option deprecated = true;

  // The list of Zone
  repeated Zone zones = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Deprecated: not implemented.
// Message for getting a Zone
message GetZoneRequest {
  option deprecated = true;

  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Zone"
    }
  ];
}

// Message for requesting list of Networks
message ListNetworksRequest {
  // Required. Parent value for ListNetworksRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgenetwork.googleapis.com/Network"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing Networks
message ListNetworksResponse {
  // The list of Network
  repeated Network networks = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Network
message GetNetworkRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Network"
    }
  ];
}

// Message for creating a Network
message CreateNetworkRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgenetwork.googleapis.com/Network"
    }
  ];

  // Required. Id of the requesting object
  // If auto-generating Id server-side, remove this field and
  // network_id from the method_signature of Create RPC
  string network_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Network network = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Network
message DeleteNetworkRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Network"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Subnets
message ListSubnetsRequest {
  // Required. Parent value for ListSubnetsRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgenetwork.googleapis.com/Subnet"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing Subnets
message ListSubnetsResponse {
  // The list of Subnet
  repeated Subnet subnets = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Subnet
message GetSubnetRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Subnet"
    }
  ];
}

// Message for creating a Subnet
message CreateSubnetRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgenetwork.googleapis.com/Subnet"
    }
  ];

  // Required. Id of the requesting object
  // If auto-generating Id server-side, remove this field and
  // subnet_id from the method_signature of Create RPC
  string subnet_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Subnet subnet = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Subnet
message UpdateSubnetRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Subnet resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  Subnet subnet = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Subnet
message DeleteSubnetRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Subnet"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Interconnects
message ListInterconnectsRequest {
  // Required. Parent value for ListInterconnectsRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgenetwork.googleapis.com/Interconnect"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing Interconnects
message ListInterconnectsResponse {
  // The list of Interconnect
  repeated Interconnect interconnects = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Interconnect
message GetInterconnectRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Interconnect"
    }
  ];
}

// Message for requesting list of InterconnectAttachments
message ListInterconnectAttachmentsRequest {
  // Required. Parent value for ListInterconnectAttachmentsRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgenetwork.googleapis.com/InterconnectAttachment"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing InterconnectAttachments
message ListInterconnectAttachmentsResponse {
  // The list of InterconnectAttachment
  repeated InterconnectAttachment interconnect_attachments = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a InterconnectAttachment
message GetInterconnectAttachmentRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/InterconnectAttachment"
    }
  ];
}

// Message for creating a InterconnectAttachment
message CreateInterconnectAttachmentRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgenetwork.googleapis.com/InterconnectAttachment"
    }
  ];

  // Required. Id of the requesting object
  // If auto-generating Id server-side, remove this field and
  // interconnect_attachment_id from the method_signature of Create RPC
  string interconnect_attachment_id = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  InterconnectAttachment interconnect_attachment = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a InterconnectAttachment
message DeleteInterconnectAttachmentRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/InterconnectAttachment"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Message for requesting list of Routers
message ListRoutersRequest {
  // Required. Parent value for ListRoutersRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgenetwork.googleapis.com/Router"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Message for response to listing Routers
message ListRoutersResponse {
  // The list of Router
  repeated Router routers = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a Router
message GetRouterRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Router"
    }
  ];
}

// Message for creating a Router
message CreateRouterRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "edgenetwork.googleapis.com/Router"
    }
  ];

  // Required. Id of the requesting object
  // If auto-generating Id server-side, remove this field and
  // router_id from the method_signature of Create RPC
  string router_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Router router = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a Router
message UpdateRouterRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Router resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated
  Router router = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a Router
message DeleteRouterRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Router"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting the diagnostics of a network within a specific zone.
message DiagnoseNetworkRequest {
  // Required. The name of the network resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Network"
    }
  ];
}

// DiagnoseNetworkResponse contains the current status for a specific network.
message DiagnoseNetworkResponse {
  // NetworkStatus has a list of status for the subnets under the current
  // network.
  message NetworkStatus {
    // Denotes the status of MACsec sessions for the links of a zone.
    enum MacsecStatus {
      // MACsec status not specified, likely due to missing metrics.
      MACSEC_STATUS_UNSPECIFIED = 0;

      // All relevant links have at least one MACsec session up.
      SECURE = 1;

      // At least one relevant link does not have any MACsec sessions up.
      UNSECURE = 2;
    }

    // A list of status for the subnets under the current network.
    repeated SubnetStatus subnet_status = 1;

    // The MACsec status of internal links.
    MacsecStatus macsec_status_internal_links = 2;
  }

  // The time when the network status was last updated.
  google.protobuf.Timestamp update_time = 1;

  // The network status of a specific network.
  NetworkStatus result = 2;
}

// Message for requesting the diagnostics of an interconnect within a specific
// zone.
message DiagnoseInterconnectRequest {
  // Required. The name of the interconnect resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Interconnect"
    }
  ];
}

// DiagnoseInterconnectResponse contains the current diagnostics for a
// specific interconnect.
message DiagnoseInterconnectResponse {
  // The time when the interconnect diagnostics was last updated.
  google.protobuf.Timestamp update_time = 1;

  // The network status of a specific interconnect.
  InterconnectDiagnostics result = 2;
}

// Message for requesting diagnositcs of a router within a specific zone.
message DiagnoseRouterRequest {
  // Required. The name of the router resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Router"
    }
  ];
}

// DiagnoseRouterResponse contains the current status for a specific router.
message DiagnoseRouterResponse {
  // The time when the router status was last updated.
  google.protobuf.Timestamp update_time = 1;

  // The network status of a specific router.
  RouterStatus result = 2;
}

// Message for initializing a specified zone
message InitializeZoneRequest {
  // Required. The name of the zone resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "edgenetwork.googleapis.com/Zone"
    }
  ];
}

// The response of initializing a zone
message InitializeZoneResponse {}
