{"methodConfig": [{"name": [{"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "ListNetworks"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "GetNetwork"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "DiagnoseNetwork"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "ListSubnets"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "GetSubnet"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "ListInterconnectAttachments"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "GetInterconnectAttachment"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "ListInterconnects"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "GetInterconnect"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "DiagnoseInterconnect"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "ListRouters"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "GetRouter"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "Diagnose<PERSON><PERSON><PERSON>"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "ListRoutes"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "GetRoute"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "InitializeZone"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "CreateNetwork"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "DeleteNetwork"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "CreateSubnet"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "DeleteSubnet"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "CreateInterconnectAttachment"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "DeleteInterconnectAttachment"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "UpdateRouter"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "DeleteRouter"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "CreateRoute"}, {"service": "google.cloud.edgenetwork.v1.EdgeNetwork", "method": "DeleteRoute"}], "timeout": "60s"}]}