# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "tpu_proto",
    srcs = [
        "cloud_tpu.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "tpu_proto_with_info",
    deps = [
        ":tpu_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "tpu_java_proto",
    deps = [":tpu_proto"],
)

java_grpc_library(
    name = "tpu_java_grpc",
    srcs = [":tpu_proto"],
    deps = [":tpu_java_proto"],
)

java_gapic_library(
    name = "tpu_java_gapic",
    srcs = [":tpu_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "tpu_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "tpu_v1.yaml",
    test_deps = [
        ":tpu_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":tpu_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "tpu_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.tpu.v1.TpuClientTest",
    ],
    runtime_deps = [":tpu_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-tpu-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":tpu_java_gapic",
        ":tpu_java_grpc",
        ":tpu_java_proto",
        ":tpu_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "tpu_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/tpu/apiv1/tpupb",
    protos = [":tpu_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "tpu_go_gapic",
    srcs = [":tpu_proto_with_info"],
    grpc_service_config = "tpu_grpc_service_config.json",
    importpath = "cloud.google.com/go/tpu/apiv1;tpu",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "tpu_v1.yaml",
    transport = "grpc",
    deps = [
        ":tpu_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-tpu-v1-go",
    deps = [
        ":tpu_go_gapic",
        ":tpu_go_gapic_srcjar-metadata.srcjar",
        ":tpu_go_gapic_srcjar-snippets.srcjar",
        ":tpu_go_gapic_srcjar-test.srcjar",
        ":tpu_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "tpu_py_gapic",
    srcs = [":tpu_proto"],
    grpc_service_config = "tpu_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "tpu_v1.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "tpu_py_gapic_test",
    srcs = [
        "tpu_py_gapic_pytest.py",
        "tpu_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":tpu_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "tpu-v1-py",
    deps = [
        ":tpu_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "tpu_php_proto",
    deps = [":tpu_proto"],
)

php_gapic_library(
    name = "tpu_php_gapic",
    srcs = [":tpu_proto_with_info"],
    grpc_service_config = "tpu_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "tpu_v1.yaml",
    transport = "grpc+rest",
    deps = [":tpu_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-tpu-v1-php",
    deps = [
        ":tpu_php_gapic",
        ":tpu_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "tpu_nodejs_gapic",
    package_name = "@google-cloud/tpu",
    src = ":tpu_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "tpu_grpc_service_config.json",
    package = "google.cloud.tpu.v1",
    rest_numeric_enums = True,
    service_yaml = "tpu_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "tpu-v1-nodejs",
    deps = [
        ":tpu_nodejs_gapic",
        ":tpu_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "tpu_ruby_proto",
    deps = [":tpu_proto"],
)

ruby_grpc_library(
    name = "tpu_ruby_grpc",
    srcs = [":tpu_proto"],
    deps = [":tpu_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "tpu_ruby_gapic",
    srcs = [":tpu_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=tpu.googleapis.com",
        "ruby-cloud-api-shortname=tpu",
        "ruby-cloud-env-prefix=CLOUD_TPU",
        "ruby-cloud-gem-name=google-cloud-tpu-v1",
        "ruby-cloud-product-url=https://cloud.google.com/tpu/",
    ],
    grpc_service_config = "tpu_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Tensor Processing Units (TPUs) are Google's custom-developed application-specific integrated circuits (ASICs) used to accelerate machine learning workloads. Cloud TPUs allow you to access TPUs from Compute Engine, Google Kubernetes Engine and AI Platform.",
    ruby_cloud_title = "Cloud TPU V1",
    service_yaml = "tpu_v1.yaml",
    transport = "grpc",
    deps = [
        ":tpu_ruby_grpc",
        ":tpu_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-tpu-v1-ruby",
    deps = [
        ":tpu_ruby_gapic",
        ":tpu_ruby_grpc",
        ":tpu_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "tpu_csharp_proto",
    deps = [":tpu_proto"],
)

csharp_grpc_library(
    name = "tpu_csharp_grpc",
    srcs = [":tpu_proto"],
    deps = [":tpu_csharp_proto"],
)

csharp_gapic_library(
    name = "tpu_csharp_gapic",
    srcs = [":tpu_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "tpu_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "tpu_v1.yaml",
    transport = "grpc",
    deps = [
        ":tpu_csharp_grpc",
        ":tpu_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-tpu-v1-csharp",
    deps = [
        ":tpu_csharp_gapic",
        ":tpu_csharp_grpc",
        ":tpu_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "tpu_cc_proto",
    deps = [":tpu_proto"],
)

cc_grpc_library(
    name = "tpu_cc_grpc",
    srcs = [":tpu_proto"],
    grpc_only = True,
    deps = [":tpu_cc_proto"],
)
