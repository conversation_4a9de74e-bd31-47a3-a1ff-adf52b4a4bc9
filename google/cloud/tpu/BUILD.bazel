# This build file includes a target for the Ruby wrapper library for
# google-cloud-tpu.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for tpu.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "tpu_ruby_wrapper",
    srcs = ["//google/cloud/tpu/v1:tpu_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-tpu",
        "ruby-cloud-env-prefix=CLOUD_TPU",
        "ruby-cloud-wrapper-of=v1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/tpu/",
        "ruby-cloud-api-id=tpu.googleapis.com",
        "ruby-cloud-api-shortname=tpu",
    ],
    ruby_cloud_description = "Tensor Processing Units (TPUs) are Google's custom-developed application-specific integrated circuits (ASICs) used to accelerate machine learning workloads. Cloud TPUs allow you to access TPUs from Compute Engine, Google Kubernetes Engine and AI Platform.",
    ruby_cloud_title = "Cloud TPU",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-tpu-ruby",
    deps = [
        ":tpu_ruby_wrapper",
    ],
)
