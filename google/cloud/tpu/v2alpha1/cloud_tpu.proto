// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.tpu.v2alpha1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/interval.proto";

option go_package = "cloud.google.com/go/tpu/apiv2alpha1/tpupb;tpupb";
option java_multiple_files = true;
option java_outer_classname = "CloudTpuProto";
option java_package = "com.google.cloud.tpu.v2alpha1";

// Manages TPU nodes and other resources
//
// TPU API v2alpha1
service Tpu {
  option (google.api.default_host) = "tpu.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists nodes.
  rpc ListNodes(ListNodesRequest) returns (ListNodesResponse) {
    option (google.api.http) = {
      get: "/v2alpha1/{parent=projects/*/locations/*}/nodes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a node.
  rpc GetNode(GetNodeRequest) returns (Node) {
    option (google.api.http) = {
      get: "/v2alpha1/{name=projects/*/locations/*/nodes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a node.
  rpc CreateNode(CreateNodeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha1/{parent=projects/*/locations/*}/nodes"
      body: "node"
    };
    option (google.api.method_signature) = "parent,node,node_id";
    option (google.longrunning.operation_info) = {
      response_type: "Node"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a node.
  rpc DeleteNode(DeleteNodeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2alpha1/{name=projects/*/locations/*/nodes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Stops a node. This operation is only available with single TPU nodes.
  rpc StopNode(StopNodeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha1/{name=projects/*/locations/*/nodes/*}:stop"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Node"
      metadata_type: "OperationMetadata"
    };
  }

  // Starts a node.
  rpc StartNode(StartNodeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha1/{name=projects/*/locations/*/nodes/*}:start"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Node"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the configurations of a node.
  rpc UpdateNode(UpdateNodeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2alpha1/{node.name=projects/*/locations/*/nodes/*}"
      body: "node"
    };
    option (google.api.method_signature) = "node,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Node"
      metadata_type: "OperationMetadata"
    };
  }

  // Perform manual maintenance on a node.
  rpc PerformMaintenance(PerformMaintenanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha1/{name=projects/*/locations/*/nodes/*}:performMaintenance"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Node"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists queued resources.
  rpc ListQueuedResources(ListQueuedResourcesRequest)
      returns (ListQueuedResourcesResponse) {
    option (google.api.http) = {
      get: "/v2alpha1/{parent=projects/*/locations/*}/queuedResources"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a queued resource.
  rpc GetQueuedResource(GetQueuedResourceRequest) returns (QueuedResource) {
    option (google.api.http) = {
      get: "/v2alpha1/{name=projects/*/locations/*/queuedResources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a QueuedResource TPU instance.
  rpc CreateQueuedResource(CreateQueuedResourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha1/{parent=projects/*/locations/*}/queuedResources"
      body: "queued_resource"
    };
    option (google.api.method_signature) =
        "parent,queued_resource,queued_resource_id";
    option (google.longrunning.operation_info) = {
      response_type: "QueuedResource"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a QueuedResource TPU instance.
  rpc DeleteQueuedResource(DeleteQueuedResourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2alpha1/{name=projects/*/locations/*/queuedResources/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "QueuedResource"
      metadata_type: "OperationMetadata"
    };
  }

  // Resets a QueuedResource TPU instance
  rpc ResetQueuedResource(ResetQueuedResourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha1/{name=projects/*/locations/*/queuedResources/*}:reset"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "QueuedResource"
      metadata_type: "OperationMetadata"
    };
  }

  // Perform manual maintenance on specific nodes of a QueuedResource.
  rpc PerformMaintenanceQueuedResource(PerformMaintenanceQueuedResourceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha1/{name=projects/*/locations/*/queuedResources/*}:performMaintenanceQueuedResource"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "QueuedResource"
      metadata_type: "OperationMetadata"
    };
  }

  // Generates the Cloud TPU service identity for the project.
  rpc GenerateServiceIdentity(GenerateServiceIdentityRequest)
      returns (GenerateServiceIdentityResponse) {
    option (google.api.http) = {
      post: "/v2alpha1/{parent=projects/*/locations/*}:generateServiceIdentity"
      body: "*"
    };
  }

  // Lists accelerator types supported by this API.
  rpc ListAcceleratorTypes(ListAcceleratorTypesRequest)
      returns (ListAcceleratorTypesResponse) {
    option (google.api.http) = {
      get: "/v2alpha1/{parent=projects/*/locations/*}/acceleratorTypes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets AcceleratorType.
  rpc GetAcceleratorType(GetAcceleratorTypeRequest) returns (AcceleratorType) {
    option (google.api.http) = {
      get: "/v2alpha1/{name=projects/*/locations/*/acceleratorTypes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists runtime versions supported by this API.
  rpc ListRuntimeVersions(ListRuntimeVersionsRequest)
      returns (ListRuntimeVersionsResponse) {
    option (google.api.http) = {
      get: "/v2alpha1/{parent=projects/*/locations/*}/runtimeVersions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a runtime version.
  rpc GetRuntimeVersion(GetRuntimeVersionRequest) returns (RuntimeVersion) {
    option (google.api.http) = {
      get: "/v2alpha1/{name=projects/*/locations/*/runtimeVersions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves the guest attributes for the node.
  rpc GetGuestAttributes(GetGuestAttributesRequest)
      returns (GetGuestAttributesResponse) {
    option (google.api.http) = {
      post: "/v2alpha1/{name=projects/*/locations/*/nodes/*}:getGuestAttributes"
      body: "*"
    };
  }

  // Retrieves the reservations for the given project in the given location.
  rpc ListReservations(ListReservationsRequest)
      returns (ListReservationsResponse) {
    option (google.api.http) = {
      get: "/v2alpha1/{parent=projects/*/locations/*}/reservations"
    };
  }

  // Simulates a maintenance event.
  rpc SimulateMaintenanceEvent(SimulateMaintenanceEventRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2alpha1/{name=projects/*/locations/*/nodes/*}:simulateMaintenanceEvent"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Node"
      metadata_type: "OperationMetadata"
    };
  }
}

// A guest attributes.
message GuestAttributes {
  // The path to be queried. This can be the default namespace ('/') or a
  // nested namespace ('/\<namespace\>/') or a specified key
  // ('/\<namespace\>/\<key\>')
  string query_path = 1;

  // The value of the requested queried path.
  GuestAttributesValue query_value = 2;
}

// Array of guest attribute namespace/key/value tuples.
message GuestAttributesValue {
  // The list of guest attributes entries.
  repeated GuestAttributesEntry items = 1;
}

// A guest attributes namespace/key/value entry.
message GuestAttributesEntry {
  // Namespace for the guest attribute entry.
  string namespace = 1;

  // Key for the guest attribute entry.
  string key = 2;

  // Value for the guest attribute entry.
  string value = 3;
}

// A node-attached disk resource.
// Next ID: 8;
message AttachedDisk {
  // The different mode of the attached disk.
  enum DiskMode {
    // The disk mode is not known/set.
    DISK_MODE_UNSPECIFIED = 0;

    // Attaches the disk in read-write mode. Only one TPU node can attach a disk
    // in read-write mode at a time.
    READ_WRITE = 1;

    // Attaches the disk in read-only mode. Multiple TPU nodes can attach
    // a disk in read-only mode at a time.
    READ_ONLY = 2;
  }

  // Specifies the full path to an existing disk.
  // For example: "projects/my-project/zones/us-central1-c/disks/my-disk".
  string source_disk = 3;

  // The mode in which to attach this disk.
  // If not specified, the default is READ_WRITE mode.
  // Only applicable to data_disks.
  DiskMode mode = 4;
}

// Sets the scheduling options for this node.
message SchedulingConfig {
  // Defines whether the node is preemptible.
  bool preemptible = 1;

  // Whether the node is created under a reservation.
  bool reserved = 2;

  // Optional. Defines whether the node is Spot VM.
  bool spot = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A network endpoint over which a TPU worker can be reached.
message NetworkEndpoint {
  // The internal IP address of this network endpoint.
  string ip_address = 1;

  // The port of this network endpoint.
  int32 port = 2;

  // The access config for the TPU worker.
  AccessConfig access_config = 5;
}

// An access config attached to the TPU worker.
message AccessConfig {
  // Output only. An external IP address associated with the TPU worker.
  string external_ip = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Network related configurations.
message NetworkConfig {
  // The name of the network for the TPU node. It must be a preexisting Google
  // Compute Engine network. If none is provided, "default" will be used.
  string network = 1;

  // The name of the subnetwork for the TPU node. It must be a preexisting
  // Google Compute Engine subnetwork. If none is provided, "default" will be
  // used.
  string subnetwork = 2;

  // Indicates that external IP addresses would be associated with the TPU
  // workers. If set to false, the specified subnetwork or network should have
  // Private Google Access enabled.
  bool enable_external_ips = 3;

  // Allows the TPU node to send and receive packets with non-matching
  // destination or source IPs. This is required if you plan to use the TPU
  // workers to forward routes.
  bool can_ip_forward = 4;

  // Optional. Specifies networking queue count for TPU VM instance's network
  // interface.
  int32 queue_count = 6 [(google.api.field_behavior) = OPTIONAL];
}

// A service account.
message ServiceAccount {
  // Email address of the service account. If empty, default Compute service
  // account will be used.
  string email = 1;

  // The list of scopes to be made available for this service account. If empty,
  // access to all Cloud APIs will be allowed.
  repeated string scope = 2;
}

// A TPU instance.
message Node {
  option (google.api.resource) = {
    type: "tpu.googleapis.com/Node"
    pattern: "projects/{project}/locations/{location}/nodes/{node}"
  };

  // Represents the different states of a TPU node during its lifecycle.
  enum State {
    // TPU node state is not known/set.
    STATE_UNSPECIFIED = 0;

    // TPU node is being created.
    CREATING = 1;

    // TPU node has been created.
    READY = 2;

    // TPU node is restarting.
    RESTARTING = 3;

    // TPU node is undergoing reimaging.
    REIMAGING = 4;

    // TPU node is being deleted.
    DELETING = 5;

    // TPU node is being repaired and may be unusable. Details can be
    // found in the 'help_description' field.
    REPAIRING = 6;

    // TPU node is stopped.
    STOPPED = 8;

    // TPU node is currently stopping.
    STOPPING = 9;

    // TPU node is currently starting.
    STARTING = 10;

    // TPU node has been preempted. Only applies to Preemptible TPU Nodes.
    PREEMPTED = 11;

    // TPU node has been terminated due to maintenance or has reached the end of
    // its life cycle (for preemptible nodes).
    TERMINATED = 12;

    // TPU node is currently hiding.
    HIDING = 13;

    // TPU node has been hidden.
    HIDDEN = 14;

    // TPU node is currently unhiding.
    UNHIDING = 15;

    // TPU node has unknown state after a failed repair.
    UNKNOWN = 16;
  }

  // Health defines the status of a TPU node as reported by
  // Health Monitor.
  enum Health {
    // Health status is unknown: not initialized or failed to retrieve.
    HEALTH_UNSPECIFIED = 0;

    // The resource is healthy.
    HEALTHY = 1;

    // The resource is unresponsive.
    TIMEOUT = 3;

    // The in-guest ML stack is unhealthy.
    UNHEALTHY_TENSORFLOW = 4;

    // The node is under maintenance/priority boost caused rescheduling and
    // will resume running once rescheduled.
    UNHEALTHY_MAINTENANCE = 5;
  }

  // TPU API Version.
  enum ApiVersion {
    // API version is unknown.
    API_VERSION_UNSPECIFIED = 0;

    // TPU API V1Alpha1 version.
    V1_ALPHA1 = 1;

    // TPU API V1 version.
    V1 = 2;

    // TPU API V2Alpha1 version.
    V2_ALPHA1 = 3;
  }

  // Output only. Immutable. The name of the TPU.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The user-supplied description of the TPU. Maximum of 512 characters.
  string description = 3;

  // The type of hardware accelerators associated with this node.
  string accelerator_type = 5;

  // Output only. The current state for the TPU Node.
  State state = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If this field is populated, it contains a description of why
  // the TPU Node is unhealthy.
  string health_description = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The runtime version running in the Node.
  string runtime_version = 11 [(google.api.field_behavior) = REQUIRED];

  // Network configurations for the TPU node. network_config and network_configs
  // are mutually exclusive, you can only specify one of them. If both are
  // specified, an error will be returned.
  NetworkConfig network_config = 36;

  // Optional. Repeated network configurations for the TPU node. This field is
  // used to specify multiple networks configs for the TPU node. network_config
  // and network_configs are mutually exclusive, you can only specify one of
  // them. If both are specified, an error will be returned.
  repeated NetworkConfig network_configs = 50
      [(google.api.field_behavior) = OPTIONAL];

  // The CIDR block that the TPU node will use when selecting an IP address.
  // This CIDR block must be a /29 block; the Compute Engine networks API
  // forbids a smaller block, and using a larger block would be wasteful (a
  // node can only consume one IP address). Errors will occur if the CIDR block
  // has already been used for a currently existing TPU node, the CIDR block
  // conflicts with any subnetworks in the user's provided network, or the
  // provided network is peered with another network that is using that CIDR
  // block.
  string cidr_block = 13;

  // The Google Cloud Platform Service Account to be used by the TPU node VMs.
  // If None is specified, the default compute service account will be used.
  ServiceAccount service_account = 37;

  // Output only. The time when the node was created.
  google.protobuf.Timestamp create_time = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The scheduling options for this node.
  SchedulingConfig scheduling_config = 17;

  // Output only. The network endpoints where TPU workers can be accessed and
  // sent work. It is recommended that runtime clients of the node reach out
  // to the 0th entry in this map first.
  repeated NetworkEndpoint network_endpoints = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The health status of the TPU node.
  Health health = 22;

  // Resource labels to represent user-provided metadata.
  map<string, string> labels = 24;

  // Custom metadata to apply to the TPU Node.
  // Can set startup-script and shutdown-script
  map<string, string> metadata = 34;

  // Tags to apply to the TPU Node. Tags are used to identify valid sources or
  // targets for network firewalls.
  repeated string tags = 40;

  // Output only. The unique identifier for the TPU Node.
  int64 id = 33 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The additional data disks for the Node.
  repeated AttachedDisk data_disks = 41;

  // Output only. The API version that created this Node.
  ApiVersion api_version = 38 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The Symptoms that have occurred to the TPU Node.
  repeated Symptom symptoms = 39 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The qualified name of the QueuedResource that requested this
  // Node.
  string queued_resource = 43 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The AccleratorConfig for the TPU Node.
  AcceleratorConfig accelerator_config = 44;

  // Shielded Instance options.
  ShieldedInstanceConfig shielded_instance_config = 45;

  // Output only. Whether the Node belongs to a Multislice group.
  bool multislice_node = 47 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Whether Autocheckpoint is enabled.
  bool autocheckpoint_enabled = 48 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Boot disk configuration.
  BootDiskConfig boot_disk_config = 49 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Upcoming maintenance on this TPU node.
  UpcomingMaintenance upcoming_maintenance = 51
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A QueuedResource represents a request for resources that will be placed
// in a queue and fulfilled when the necessary resources are available.
message QueuedResource {
  option (google.api.resource) = {
    type: "tpu.googleapis.com/QueuedResource"
    pattern: "projects/{project}/locations/{location}/queuedResources/{queued_resource}"
  };

  // Details of the TPU resource(s) being requested.
  message Tpu {
    // Details of the TPU node(s) being requested. Users can request either a
    // single node or multiple nodes.
    // NodeSpec provides the specification for node(s) to be created.
    message NodeSpec {
      // Parameters to specify for multi-node QueuedResource requests. This
      // field must be populated in case of multi-node requests instead of
      // node_id. It's an error to specify both node_id and multi_node_params.
      message MultiNodeParams {
        // The workload type for the multi-node request.
        enum WorkloadType {
          // Not specified.
          WORKLOAD_TYPE_UNSPECIFIED = 0;

          // All of the nodes are available most of the time.
          // Recommended for training workloads.
          THROUGHPUT_OPTIMIZED = 1;

          // Most of the nodes are available all of the time.
          // Recommended for serving workloads.
          AVAILABILITY_OPTIMIZED = 2;
        }

        // Required. Number of nodes with this spec. The system will attempt
        // to provison "node_count" nodes as part of the request.
        // This needs to be > 1.
        int32 node_count = 1 [(google.api.field_behavior) = REQUIRED];

        // Prefix of node_ids in case of multi-node request
        // Should follow the `^[A-Za-z0-9_.~+%-]+$` regex format.
        // If node_count = 3 and node_id_prefix = "np", node ids of nodes
        // created will be "np-0", "np-1", "np-2". If this field is not
        // provided we use queued_resource_id as the node_id_prefix.
        string node_id_prefix = 2;

        // Optional. The workload type for the multi-node request.
        WorkloadType workload_type = 4 [(google.api.field_behavior) = OPTIONAL];
      }

      // Required. The parent resource name.
      string parent = 1 [
        (google.api.field_behavior) = REQUIRED,
        (google.api.resource_reference) = {
          type: "locations.googleapis.com/Location"
        }
      ];

      // The unqualified resource name. Should follow the `^[A-Za-z0-9_.~+%-]+$`
      // regex format. This is only specified when requesting a single node.
      // In case of multi-node requests, multi_node_params must be populated
      // instead. It's an error to specify both node_id and multi_node_params.
      string node_id = 2;

      // Optional. Fields to specify in case of multi-node request.
      MultiNodeParams multi_node_params = 6
          [(google.api.field_behavior) = OPTIONAL];

      // Required. The node.
      Node node = 3 [(google.api.field_behavior) = REQUIRED];
    }

    // The TPU node(s) being requested.
    repeated NodeSpec node_spec = 1;
  }

  // BestEffort tier definition.
  message BestEffort {}

  // Spot tier definition.
  message Spot {}

  // Guaranteed tier definition.
  message Guaranteed {
    // Optional. Defines the minimum duration of the guarantee. If specified,
    // the requested resources will only be provisioned if they can be
    // allocated for at least the given duration.
    google.protobuf.Duration min_duration = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Specifies the request should be scheduled on reserved capacity.
    bool reserved = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Defines the policy of the QueuedRequest.
  message QueueingPolicy {
    // Time flexibility specification.
    oneof start_timing_constraints {
      // A relative time after which resources should not be created.
      // If the request cannot be fulfilled by this time the request will be
      // failed.
      google.protobuf.Duration valid_until_duration = 1;

      // An absolute time after which resources should not be created.
      // If the request cannot be fulfilled by this time the request will be
      // failed.
      google.protobuf.Timestamp valid_until_time = 2;

      // A relative time after which resources may be created.
      google.protobuf.Duration valid_after_duration = 3;

      // An absolute time at which resources may be created.
      google.protobuf.Timestamp valid_after_time = 4;

      // An absolute time interval within which resources may be created.
      google.type.Interval valid_interval = 5;
    }
  }

  // Output only. Immutable. The name of the QueuedResource.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. The time when the QueuedResource was created.
  google.protobuf.Timestamp create_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Resource specification.
  oneof resource {
    // Defines a TPU resource.
    Tpu tpu = 2;
  }

  // Tier specifies the required tier.
  oneof tier {
    // The BestEffort tier.
    BestEffort best_effort = 3;

    // The Guaranteed tier.
    Guaranteed guaranteed = 4;

    // Optional. The Spot tier.
    Spot spot = 9 [(google.api.field_behavior) = OPTIONAL];
  }

  // The queueing policy of the QueuedRequest.
  QueueingPolicy queueing_policy = 5;

  // Output only. State of the QueuedResource request.
  QueuedResourceState state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Name of the reservation in which the resource should be provisioned.
  // Format: projects/{project}/locations/{zone}/reservations/{reservation}
  string reservation_name = 8;
}

// QueuedResourceState defines the details of the QueuedResource request.
message QueuedResourceState {
  // Output only state of the request
  enum State {
    // State of the QueuedResource request is not known/set.
    STATE_UNSPECIFIED = 0;

    // The QueuedResource request has been received. We're still working on
    // determining if we will be able to honor this request.
    CREATING = 1;

    // The QueuedResource request has passed initial validation/admission
    // control and has been persisted in the queue.
    ACCEPTED = 2;

    // The QueuedResource request has been selected. The
    // associated resources are currently being provisioned (or very soon
    // will begin provisioning).
    PROVISIONING = 3;

    // The request could not be completed. This may be due to some
    // late-discovered problem with the request itself, or due to
    // unavailability of resources within the constraints of the request
    // (e.g., the 'valid until' start timing constraint expired).
    FAILED = 4;

    // The QueuedResource is being deleted.
    DELETING = 5;

    // The resources specified in the QueuedResource request have been
    // provisioned and are ready for use by the end-user/consumer.
    ACTIVE = 6;

    // The resources specified in the QueuedResource request are being
    // deleted. This may have been initiated by the user, or
    // the Cloud TPU service. Inspect the state data for more details.
    SUSPENDING = 7;

    // The resources specified in the QueuedResource request have been
    // deleted.
    SUSPENDED = 8;

    // The QueuedResource request has passed initial validation and has been
    // persisted in the queue. It will remain in this state until there are
    // sufficient free resources to begin provisioning your request. Wait times
    // will vary significantly depending on demand levels. When demand is high,
    // not all requests can be immediately provisioned. If you
    // need more reliable obtainability of TPUs consider purchasing a
    // reservation. To put a limit on how long you are willing to wait, use
    // [timing
    // constraints](https://cloud.google.com/tpu/docs/queued-resources#request_a_queued_resource_before_a_specified_time).
    WAITING_FOR_RESOURCES = 9;
  }

  // Further data for the creating state.
  message CreatingData {}

  // Further data for the accepted state.
  message AcceptedData {}

  // Further data for the provisioning state.
  message ProvisioningData {}

  // Further data for the failed state.
  message FailedData {
    // The error that caused the queued resource to enter the FAILED state.
    google.rpc.Status error = 1;
  }

  // Further data for the deleting state.
  message DeletingData {}

  // Further data for the active state.
  message ActiveData {}

  // Further data for the suspending state.
  message SuspendingData {}

  // Further data for the suspended state.
  message SuspendedData {}

  // The initiator of the QueuedResource's SUSPENDING/SUSPENDED state.
  enum StateInitiator {
    // The state initiator is unspecified.
    STATE_INITIATOR_UNSPECIFIED = 0;

    // The current QueuedResource state was initiated by the user.
    USER = 1;

    // The current QueuedResource state was initiated by the service.
    SERVICE = 2;
  }

  // State of the QueuedResource request.
  State state = 1;

  // Further data for the state.
  oneof state_data {
    // Further data for the creating state.
    CreatingData creating_data = 2;

    // Further data for the accepted state.
    AcceptedData accepted_data = 3;

    // Further data for the provisioning state.
    ProvisioningData provisioning_data = 4;

    // Further data for the failed state.
    FailedData failed_data = 5;

    // Further data for the deleting state.
    DeletingData deleting_data = 6;

    // Further data for the active state.
    ActiveData active_data = 7;

    // Further data for the suspending state.
    SuspendingData suspending_data = 8;

    // Further data for the suspended state.
    SuspendedData suspended_data = 9;
  }

  // Output only. The initiator of the QueuedResources's current state. Used to
  // indicate whether the SUSPENDING/SUSPENDED state was initiated by the user
  // or the service.
  StateInitiator state_initiator = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request for [ListNodes][google.cloud.tpu.v2alpha1.Tpu.ListNodes].
message ListNodesRequest {
  // Required. The parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "tpu.googleapis.com/Node" }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;
}

// Response for [ListNodes][google.cloud.tpu.v2alpha1.Tpu.ListNodes].
message ListNodesResponse {
  // The listed nodes.
  repeated Node nodes = 1;

  // The next page token or empty if none.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for [GetNode][google.cloud.tpu.v2alpha1.Tpu.GetNode].
message GetNodeRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "tpu.googleapis.com/Node" }
  ];
}

// Request for [CreateNode][google.cloud.tpu.v2alpha1.Tpu.CreateNode].
message CreateNodeRequest {
  // Required. The parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The unqualified resource name.
  string node_id = 2;

  // Required. The node.
  Node node = 3 [(google.api.field_behavior) = REQUIRED];

  // Idempotent request UUID.
  string request_id = 6;
}

// Request for [DeleteNode][google.cloud.tpu.v2alpha1.Tpu.DeleteNode].
message DeleteNodeRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "tpu.googleapis.com/Node" }
  ];

  // Idempotent request UUID.
  string request_id = 3;
}

// Request for [StopNode][google.cloud.tpu.v2alpha1.Tpu.StopNode].
message StopNodeRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "tpu.googleapis.com/Node" }
  ];
}

// Request for [StartNode][google.cloud.tpu.v2alpha1.Tpu.StartNode].
message StartNodeRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "tpu.googleapis.com/Node" }
  ];
}

// Request for [UpdateNode][google.cloud.tpu.v2alpha1.Tpu.UpdateNode].
message UpdateNodeRequest {
  // Required. Mask of fields from [Node][Tpu.Node] to update.
  // Supported fields: [description, tags, labels, metadata,
  // network_config.enable_external_ips].
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The node. Only fields specified in update_mask are updated.
  Node node = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for
// [ListQueuedResources][google.cloud.tpu.v2alpha1.Tpu.ListQueuedResources].
message ListQueuedResourcesRequest {
  // Required. The parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "tpu.googleapis.com/QueuedResource"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;
}

// Response for
// [ListQueuedResources][google.cloud.tpu.v2alpha1.Tpu.ListQueuedResources].
message ListQueuedResourcesResponse {
  // The listed queued resources.
  repeated QueuedResource queued_resources = 1;

  // The next page token or empty if none.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for
// [GetQueuedResource][google.cloud.tpu.v2alpha1.Tpu.GetQueuedResource]
message GetQueuedResourceRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "tpu.googleapis.com/QueuedResource"
    }
  ];
}

// Request for
// [CreateQueuedResource][google.cloud.tpu.v2alpha1.Tpu.CreateQueuedResource].
message CreateQueuedResourceRequest {
  // Required. The parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The unqualified resource name. Should follow the `^[A-Za-z0-9_.~+%-]+$`
  // regex format.
  string queued_resource_id = 2;

  // Required. The queued resource.
  QueuedResource queued_resource = 3 [(google.api.field_behavior) = REQUIRED];

  // Idempotent request UUID.
  string request_id = 4;
}

// Request for
// [DeleteQueuedResource][google.cloud.tpu.v2alpha1.Tpu.DeleteQueuedResource].
message DeleteQueuedResourceRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "tpu.googleapis.com/QueuedResource"
    }
  ];

  // Idempotent request UUID.
  string request_id = 2;

  // If set to true, all running nodes belonging to this queued resource will
  // be deleted first and then the queued resource will be deleted.
  // Otherwise (i.e. force=false), the queued resource will only be deleted if
  // its nodes have already been deleted or the queued resource is in the
  // ACCEPTED, FAILED, or SUSPENDED state.
  bool force = 3;
}

// Request for
// [ResetQueuedResource][google.cloud.tpu.v2alpha1.Tpu.ResetQueuedResource].
message ResetQueuedResourceRequest {
  // Required. The name of the queued resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "tpu.googleapis.com/QueuedResource"
    }
  ];
}

// The per-product per-project service identity for Cloud TPU service.
message ServiceIdentity {
  // The email address of the service identity.
  string email = 1;
}

// Request for
// [GenerateServiceIdentity][google.cloud.tpu.v2alpha1.Tpu.GenerateServiceIdentity].
message GenerateServiceIdentityRequest {
  // Required. The parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];
}

// Response for
// [GenerateServiceIdentity][google.cloud.tpu.v2alpha1.Tpu.GenerateServiceIdentity].
message GenerateServiceIdentityResponse {
  // ServiceIdentity that was created or retrieved.
  ServiceIdentity identity = 1;
}

// A accelerator type that a Node can be configured with.
message AcceleratorType {
  option (google.api.resource) = {
    type: "tpu.googleapis.com/AcceleratorType"
    pattern: "projects/{project}/locations/{location}/acceleratorTypes/{accelerator_type}"
  };

  // The resource name.
  string name = 1;

  // The accelerator type.
  string type = 2;

  // The accelerator config.
  repeated AcceleratorConfig accelerator_configs = 3;
}

// Request for
// [GetAcceleratorType][google.cloud.tpu.v2alpha1.Tpu.GetAcceleratorType].
message GetAcceleratorTypeRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "tpu.googleapis.com/AcceleratorType"
    }
  ];
}

// Request for
// [ListAcceleratorTypes][google.cloud.tpu.v2alpha1.Tpu.ListAcceleratorTypes].
message ListAcceleratorTypesRequest {
  // Required. The parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "tpu.googleapis.com/AcceleratorType"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;

  // List filter.
  string filter = 5;

  // Sort results.
  string order_by = 6;
}

// Response for
// [ListAcceleratorTypes][google.cloud.tpu.v2alpha1.Tpu.ListAcceleratorTypes].
message ListAcceleratorTypesResponse {
  // The listed nodes.
  repeated AcceleratorType accelerator_types = 1;

  // The next page token or empty if none.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// A runtime version that a Node can be configured with.
message RuntimeVersion {
  option (google.api.resource) = {
    type: "tpu.googleapis.com/RuntimeVersion"
    pattern: "projects/{project}/locations/{location}/runtimeVersions/{runtime_version}"
  };

  // The resource name.
  string name = 1;

  // The runtime version.
  string version = 2;
}

// Request for
// [GetRuntimeVersion][google.cloud.tpu.v2alpha1.Tpu.GetRuntimeVersion].
message GetRuntimeVersionRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "tpu.googleapis.com/RuntimeVersion"
    }
  ];
}

// Request for
// [ListRuntimeVersions][google.cloud.tpu.v2alpha1.Tpu.ListRuntimeVersions].
message ListRuntimeVersionsRequest {
  // Required. The parent resource name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "tpu.googleapis.com/RuntimeVersion"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;

  // List filter.
  string filter = 5;

  // Sort results.
  string order_by = 6;
}

// Response for
// [ListRuntimeVersions][google.cloud.tpu.v2alpha1.Tpu.ListRuntimeVersions].
message ListRuntimeVersionsResponse {
  // The listed nodes.
  repeated RuntimeVersion runtime_versions = 1;

  // The next page token or empty if none.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Metadata describing an [Operation][google.longrunning.Operation]
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Target of the operation - for example
  // projects/project-1/connectivityTests/test-1
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation, if any.
  string status_detail = 5;

  // Specifies if cancellation was requested for the operation.
  bool cancel_requested = 6;

  // API version.
  string api_version = 7;
}

// A Symptom instance.
message Symptom {
  // SymptomType represents the different types of Symptoms that a TPU can be
  // at.
  enum SymptomType {
    // Unspecified symptom.
    SYMPTOM_TYPE_UNSPECIFIED = 0;

    // TPU VM memory is low.
    LOW_MEMORY = 1;

    // TPU runtime is out of memory.
    OUT_OF_MEMORY = 2;

    // TPU runtime execution has timed out.
    EXECUTE_TIMED_OUT = 3;

    // TPU runtime fails to construct a mesh that recognizes each TPU device's
    // neighbors.
    MESH_BUILD_FAIL = 4;

    // TPU HBM is out of memory.
    HBM_OUT_OF_MEMORY = 5;

    // Abusive behaviors have been identified on the current project.
    PROJECT_ABUSE = 6;
  }

  // Timestamp when the Symptom is created.
  google.protobuf.Timestamp create_time = 1;

  // Type of the Symptom.
  SymptomType symptom_type = 2;

  // Detailed information of the current Symptom.
  string details = 3;

  // A string used to uniquely distinguish a worker within a TPU node.
  string worker_id = 4;
}

// Request for
// [GetGuestAttributes][google.cloud.tpu.v2alpha1.Tpu.GetGuestAttributes].
message GetGuestAttributesRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "tpu.googleapis.com/Node" }
  ];

  // The guest attributes path to be queried.
  string query_path = 2;

  // The 0-based worker ID. If it is empty, all workers' GuestAttributes will be
  // returned.
  repeated string worker_ids = 3;
}

// Response for
// [GetGuestAttributes][google.cloud.tpu.v2alpha1.Tpu.GetGuestAttributes].
message GetGuestAttributesResponse {
  // The guest attributes for the TPU workers.
  repeated GuestAttributes guest_attributes = 1;
}

// Request for
// [SimulateMaintenanceEvent][google.cloud.tpu.v2alpha1.Tpu.SimulateMaintenanceEvent].
message SimulateMaintenanceEventRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "tpu.googleapis.com/Node" }
  ];

  // The 0-based worker ID. If it is empty, worker ID 0 will be selected for
  // maintenance event simulation. A maintenance event will only be fired on the
  // first specified worker ID. Future implementations may support firing on
  // multiple workers.
  repeated string worker_ids = 2;
}

// Request for
// [PerformMaintenance][google.cloud.tpu.v2alpha1.Tpu.PerformMaintenance].
message PerformMaintenanceRequest {
  // Required. The resource name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "tpu.googleapis.com/Node" }
  ];
}

// Request for
// [PerformMaintenanceQueuedResource][google.cloud.tpu.v2alpha1.Tpu.PerformMaintenanceQueuedResource].
message PerformMaintenanceQueuedResourceRequest {
  // Required. The name of the QueuedResource which holds the nodes to perform
  // maintenance on.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "tpu.googleapis.com/QueuedResource"
    }
  ];

  // The names of the nodes to perform maintenance on.
  repeated string node_names = 2;
}

// A reservation describes the amount of a resource 'allotted' for a defined
// period of time.
message Reservation {
  option (google.api.resource) = {
    type: "tpu.googleapis.com/Reservation"
    pattern: "projects/{project}/locations/{location}/reservations/{reservation}"
  };

  // Details of a standard reservation.
  message Standard {
    // Units in which capacity for a reservation is measured.
    enum CapacityUnits {
      // The capacity units is not known/set.
      CAPACITY_UNITS_UNSPECIFIED = 0;

      // The capacity unit is set to CORES.
      CORES = 1;

      // The capacity unit is set to CHIPS.
      CHIPS = 2;
    }

    // Usage details of a reservation.
    message Usage {
      // The real-time value of usage within the reservation, with the unit
      // specified in field capacity_units.
      int64 total = 1;
    }

    // The size of the reservation, in the units specified in the
    // 'capacity_units' field.
    int32 size = 1;

    // Capacity units this reservation is measured in.
    CapacityUnits capacity_units = 2;

    // The resource type of the reservation.
    string resource_type = 3;

    // The start and end time of the reservation.
    google.type.Interval interval = 4;

    // The current usage of the reservation.
    Usage usage = 5;
  }

  // State of the Reservation.
  enum State {
    // The Reservation state is unspecified.
    STATE_UNSPECIFIED = 0;

    // The Reservation has been approved.
    APPROVED = 3;

    // The Reservation is being provisioned.
    PROVISIONING = 4;

    // The Reservation is active.
    ACTIVE = 5;

    // The Reservation is being deprovisioned.
    DEPROVISIONING = 6;

    // The Reservation is past its end date.
    EXPIRED = 7;

    // The Reservation encountered a failure during mutation.
    FAILED = 8;
  }

  // The reservation name with the format:
  // projects/{projectID}/locations/{location}/reservations/{reservationID}
  string name = 1;

  // The kind of reservation.
  oneof kind {
    // A standard reservation.
    Standard standard = 2;
  }

  // Output only. The state of the Reservation.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request for
// [ListReservations][google.cloud.tpu.v2alpha1.Tpu.ListReservations].
message ListReservationsRequest {
  // Required. The parent for reservations.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "tpu.googleapis.com/Reservation"
    }
  ];

  // The maximum number of items to return. Defaults to 0 if not specified,
  // which means no limit.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;
}

// Response for
// [ListReservations][google.cloud.tpu.v2alpha1.Tpu.ListReservations].
message ListReservationsResponse {
  // The listed reservations.
  repeated Reservation reservations = 1;

  // The next page token or empty if none.
  string next_page_token = 2;
}

// A TPU accelerator configuration.
message AcceleratorConfig {
  // TPU type.
  enum Type {
    // Unspecified version.
    TYPE_UNSPECIFIED = 0;

    // TPU v2.
    V2 = 2;

    // TPU v3.
    V3 = 4;

    // TPU v4.
    V4 = 7;

    // TPU v5lite pod.
    V5LITE_POD = 9;

    // TPU v5.
    V5P = 10;

    // TPU v6e.
    V6E = 11;
  }

  // Required. Type of TPU.
  Type type = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Topology of TPU in chips.
  string topology = 2 [(google.api.field_behavior) = REQUIRED];
}

// A set of Shielded Instance options.
message ShieldedInstanceConfig {
  // Defines whether the instance has Secure Boot enabled.
  bool enable_secure_boot = 1;
}

// Boot disk configurations.
message BootDiskConfig {
  // Optional. Customer encryption key for boot disk.
  CustomerEncryptionKey customer_encryption_key = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Whether the boot disk will be created with confidential compute
  // mode.
  bool enable_confidential_compute = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Customer's encryption key.
message CustomerEncryptionKey {
  oneof key {
    // The name of the encryption key that is stored in Google Cloud KMS.
    // For example:
    // <pre class="lang-html">"kmsKeyName": "projects/
    // <var class="apiparam">kms_project_id</var>/locations/
    // <var class="apiparam">region</var>/keyRings/<var class="apiparam">
    // key_region</var>/cryptoKeys/<var class="apiparam">key</var>
    // </pre>
    // The fully-qualifed key name may be returned for resource GET requests.
    // For example:
    // <pre class="lang-html">"kmsKeyName": "projects/
    // <var class="apiparam">kms_project_id</var>/locations/
    // <var class="apiparam">region</var>/keyRings/<var class="apiparam">
    // key_region</var>/cryptoKeys/<var class="apiparam">key</var>
    // /cryptoKeyVersions/1</pre>
    string kms_key_name = 7;
  }
}

// Upcoming Maintenance notification information.
message UpcomingMaintenance {
  // The type of maintenance for this notification.
  enum MaintenanceType {
    // No type specified. Do not use this value.
    UNKNOWN_TYPE = 0;

    // Scheduled maintenance (e.g. maintenance after uptime guarantee is
    // complete).
    SCHEDULED = 1;

    // Unscheduled maintenance (e.g. emergency maintenance during
    // uptime guarantee).
    UNSCHEDULED = 2;
  }

  // The status of the maintenance for this notification.
  enum MaintenanceStatus {
    // Unknown maintenance status. Do not use this value.
    UNKNOWN = 0;

    // There is pending maintenance.
    PENDING = 1;

    // There is ongoing maintenance on this VM.
    ONGOING = 2;
  }

  // Defines the type of maintenance.
  optional MaintenanceType type = 1;

  // Indicates if the maintenance can be customer triggered.
  optional bool can_reschedule = 5;

  // The current start time of the maintenance window.
  // This timestamp value is in RFC3339 text format.
  optional string window_start_time = 6;

  // The time by which the maintenance disruption will be completed.
  // This timestamp value is in RFC3339 text format.
  optional string window_end_time = 7;

  // The latest time for the planned maintenance window to start.
  // This timestamp value is in RFC3339 text format.
  optional string latest_window_start_time = 8;

  // The status of the maintenance.
  optional MaintenanceStatus maintenance_status = 9;
}
