// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/struct.proto";

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "AdTagDetailsProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Information related to the details for one ad tag. This resource is only
// available for live sessions that do not implement Google Ad Manager ad
// insertion.
message LiveAdTagDetail {
  option (google.api.resource) = {
    type: "videostitcher.googleapis.com/LiveAdTagDetail"
    pattern: "projects/{project}/locations/{location}/liveSessions/{live_session}/liveAdTagDetails/{live_ad_tag_detail}"
  };

  // The resource name in the form of
  // `projects/{project}/locations/{location}/liveSessions/{live_session}/liveAdTagDetails/{id}`.
  string name = 1;

  // A list of ad requests.
  repeated AdRequest ad_requests = 2;
}

// Information related to the details for one ad tag. This resource is only
// available for VOD sessions that do not implement Google Ad Manager ad
// insertion.
message VodAdTagDetail {
  option (google.api.resource) = {
    type: "videostitcher.googleapis.com/VodAdTagDetail"
    pattern: "projects/{project}/locations/{location}/vodSessions/{vod_session}/vodAdTagDetails/{vod_ad_tag_detail}"
  };

  // The name of the ad tag detail for the specified VOD session, in the form of
  // `projects/{project}/locations/{location}/vodSessions/{vod_session_id}/vodAdTagDetails/{id}`.
  string name = 1;

  // A list of ad requests for one ad tag.
  repeated AdRequest ad_requests = 2;
}

// Details of an ad request to an ad server.
message AdRequest {
  // The ad tag URI processed with integrated macros.
  string uri = 1;

  // The request metadata used to make the ad request.
  RequestMetadata request_metadata = 2;

  // The response metadata received from the ad request.
  ResponseMetadata response_metadata = 3;
}

// Metadata for an ad request.
message RequestMetadata {
  // The HTTP headers of the ad request.
  google.protobuf.Struct headers = 1;
}

// Metadata for the response of an ad request.
message ResponseMetadata {
  // Error message received when making the ad request.
  string error = 1;

  // Headers from the response.
  google.protobuf.Struct headers = 2;

  // Status code for the response.
  string status_code = 3;

  // Size in bytes of the response.
  int32 size_bytes = 4;

  // Total time elapsed for the response.
  google.protobuf.Duration duration = 5;

  // The body of the response.
  string body = 6;
}
