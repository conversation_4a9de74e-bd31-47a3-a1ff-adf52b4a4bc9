// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

import "google/cloud/video/stitcher/v1/events.proto";

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "CompanionsProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Metadata for companion ads.
message CompanionAds {
  // Indicates how many of the companions should be displayed with the ad.
  enum DisplayRequirement {
    // Required companions are not specified. The default is ALL.
    DISPLAY_REQUIREMENT_UNSPECIFIED = 0;

    // All companions are required to be displayed.
    ALL = 1;

    // At least one of companions needs to be displayed.
    ANY = 2;

    // All companions are optional for display.
    NONE = 3;
  }

  // Indicates how many of the companions should be displayed with the ad.
  DisplayRequirement display_requirement = 1;

  // List of companion ads.
  repeated Companion companions = 2;
}

// Metadata for a companion.
message Companion {
  // Ad resource associated with the companion ad.
  oneof ad_resource {
    // The IFrame ad resource associated with the companion ad.
    IframeAdResource iframe_ad_resource = 10;

    // The static ad resource associated with the companion ad.
    StaticAdResource static_ad_resource = 11;

    // The HTML ad resource associated with the companion ad.
    HtmlAdResource html_ad_resource = 12;
  }

  // The API necessary to communicate with the creative if available.
  string api_framework = 1;

  // The pixel height of the placement slot for the intended creative.
  int32 height_px = 2;

  // The pixel width of the placement slot for the intended creative.
  int32 width_px = 3;

  // The pixel height of the creative.
  int32 asset_height_px = 4;

  // The maximum pixel height of the creative in its expanded state.
  int32 expanded_height_px = 5;

  // The pixel width of the creative.
  int32 asset_width_px = 6;

  // The maximum pixel width of the creative in its expanded state.
  int32 expanded_width_px = 7;

  // The ID used to identify the desired placement on a publisher's page.
  // Values to be used should be discussed between publishers and
  // advertisers.
  string ad_slot_id = 8;

  // The list of tracking events for the companion.
  repeated Event events = 9;
}

// Metadata for an HTML ad resource.
message HtmlAdResource {
  // The HTML to display for the ad resource.
  string html_source = 1;
}

// Metadata for an IFrame ad resource.
message IframeAdResource {
  // URI source for an IFrame to display for the ad resource.
  string uri = 1;
}

// Metadata for a static ad resource.
message StaticAdResource {
  // URI to the static file for the ad resource.
  string uri = 1;

  // Describes the MIME type of the ad resource.
  string creative_type = 2;
}
