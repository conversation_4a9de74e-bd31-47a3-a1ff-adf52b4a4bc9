# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "stitcher_proto",
    srcs = [
        "ad_tag_details.proto",
        "cdn_keys.proto",
        "companions.proto",
        "events.proto",
        "fetch_options.proto",
        "live_configs.proto",
        "sessions.proto",
        "slates.proto",
        "stitch_details.proto",
        "video_stitcher_service.proto",
        "vod_configs.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "stitcher_proto_with_info",
    deps = [
        ":stitcher_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "stitcher_java_proto",
    deps = [":stitcher_proto"],
)

java_grpc_library(
    name = "stitcher_java_grpc",
    srcs = [":stitcher_proto"],
    deps = [":stitcher_java_proto"],
)

java_gapic_library(
    name = "stitcher_java_gapic",
    srcs = [":stitcher_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "videostitcher_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "videostitcher_v1.yaml",
    test_deps = [
        ":stitcher_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":stitcher_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "stitcher_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.video.stitcher.v1.VideoStitcherServiceClientTest",
    ],
    runtime_deps = [":stitcher_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-video-stitcher-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":stitcher_java_gapic",
        ":stitcher_java_grpc",
        ":stitcher_java_proto",
        ":stitcher_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "stitcher_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb",
    protos = [":stitcher_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "stitcher_go_gapic",
    srcs = [":stitcher_proto_with_info"],
    grpc_service_config = "videostitcher_grpc_service_config.json",
    importpath = "cloud.google.com/go/video/stitcher/apiv1;stitcher",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "videostitcher_v1.yaml",
    transport = "grpc",
    deps = [
        ":stitcher_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-video-stitcher-v1-go",
    deps = [
        ":stitcher_go_gapic",
        ":stitcher_go_gapic_srcjar-metadata.srcjar",
        ":stitcher_go_gapic_srcjar-snippets.srcjar",
        ":stitcher_go_gapic_srcjar-test.srcjar",
        ":stitcher_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "stitcher_py_gapic",
    srcs = [":stitcher_proto"],
    grpc_service_config = "videostitcher_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "videostitcher_v1.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "stitcher_py_gapic_test",
    srcs = [
        "stitcher_py_gapic_pytest.py",
        "stitcher_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":stitcher_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "video-stitcher-v1-py",
    deps = [
        ":stitcher_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "stitcher_php_proto",
    deps = [":stitcher_proto"],
)

php_gapic_library(
    name = "stitcher_php_gapic",
    srcs = [":stitcher_proto_with_info"],
    grpc_service_config = "videostitcher_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "videostitcher_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":stitcher_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-video-stitcher-v1-php",
    deps = [
        ":stitcher_php_gapic",
        ":stitcher_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "stitcher_nodejs_gapic",
    package_name = "@google-cloud/video-stitcher",
    src = ":stitcher_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "videostitcher_grpc_service_config.json",
    package = "google.cloud.video.stitcher.v1",
    rest_numeric_enums = True,
    service_yaml = "videostitcher_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "video-stitcher-v1-nodejs",
    deps = [
        ":stitcher_nodejs_gapic",
        ":stitcher_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "stitcher_ruby_proto",
    deps = [":stitcher_proto"],
)

ruby_grpc_library(
    name = "stitcher_ruby_grpc",
    srcs = [":stitcher_proto"],
    deps = [":stitcher_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "stitcher_ruby_gapic",
    srcs = [":stitcher_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=videostitcher.googleapis.com",
        "ruby-cloud-api-shortname=videostitcher",
        "ruby-cloud-gem-name=google-cloud-video-stitcher-v1",
        "ruby-cloud-product-url=https://cloud.google.com/video-stitcher/",
    ],
    grpc_service_config = "videostitcher_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Video Stitcher API allows you to manipulate video content to dynamically insert ads prior to delivery to client devices. Using the Video Stitcher API, you can monetize your video-on-demand (VOD) and livestreaming videos by inserting ads as described by metadata stored on ad servers.",
    ruby_cloud_title = "Video Stitcher V1",
    service_yaml = "videostitcher_v1.yaml",
    transport = "grpc",
    deps = [
        ":stitcher_ruby_grpc",
        ":stitcher_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-video-stitcher-v1-ruby",
    deps = [
        ":stitcher_ruby_gapic",
        ":stitcher_ruby_grpc",
        ":stitcher_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "stitcher_csharp_proto",
    extra_opts = [],
    deps = [":stitcher_proto"],
)

csharp_grpc_library(
    name = "stitcher_csharp_grpc",
    srcs = [":stitcher_proto"],
    deps = [":stitcher_csharp_proto"],
)

csharp_gapic_library(
    name = "stitcher_csharp_gapic",
    srcs = [":stitcher_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "videostitcher_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "videostitcher_v1.yaml",
    transport = "grpc",
    deps = [
        ":stitcher_csharp_grpc",
        ":stitcher_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-video-stitcher-v1-csharp",
    deps = [
        ":stitcher_csharp_gapic",
        ":stitcher_csharp_grpc",
        ":stitcher_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "stitcher_cc_proto",
    deps = [":stitcher_proto"],
)

cc_grpc_library(
    name = "stitcher_cc_grpc",
    srcs = [":stitcher_proto"],
    grpc_only = True,
    deps = [":stitcher_cc_proto"],
)
