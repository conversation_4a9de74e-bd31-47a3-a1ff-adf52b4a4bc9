type: google.api.Service
config_version: 3
name: videostitcher.googleapis.com
title: Video Stitcher API

apis:
- name: google.cloud.video.stitcher.v1.VideoStitcherService
- name: google.longrunning.Operations

types:
- name: google.cloud.video.stitcher.v1.OperationMetadata

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.video.stitcher.v1.VideoStitcherService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
