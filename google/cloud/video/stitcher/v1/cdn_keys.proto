// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "CdnKeysProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Configuration for a CDN key. Used by the Video Stitcher
// to sign URIs for fetching video manifests and signing
// media segments for playback.
message CdnKey {
  option (google.api.resource) = {
    type: "videostitcher.googleapis.com/CdnKey"
    pattern: "projects/{project}/locations/{location}/cdnKeys/{cdn_key}"
  };

  // Configuration associated with the CDN key.
  oneof cdn_key_config {
    // The configuration for a Google Cloud CDN key.
    GoogleCdnKey google_cdn_key = 5;

    // The configuration for an Akamai CDN key.
    AkamaiCdnKey akamai_cdn_key = 6;

    // The configuration for a Media CDN key.
    MediaCdnKey media_cdn_key = 8;
  }

  // The resource name of the CDN key, in the form of
  // `projects/{project}/locations/{location}/cdnKeys/{id}`.
  // The name is ignored when creating a CDN key.
  string name = 1;

  // The hostname this key applies to.
  string hostname = 4;
}

// Configuration for a Google Cloud CDN key.
message GoogleCdnKey {
  // Input only. Secret for this Google Cloud CDN key.
  bytes private_key = 1 [(google.api.field_behavior) = INPUT_ONLY];

  // The public name of the Google Cloud CDN key.
  string key_name = 2;
}

// Configuration for an Akamai CDN key.
message AkamaiCdnKey {
  // Input only. Token key for the Akamai CDN edge configuration.
  bytes token_key = 1 [(google.api.field_behavior) = INPUT_ONLY];
}

// Configuration for a Media CDN key.
message MediaCdnKey {
  // Configuration for a Media CDN token.
  message TokenConfig {
    // Optional. The query parameter in which to find the token.
    //
    // The name must be 1-64 characters long and match
    // the regular expression `[a-zA-Z]([a-zA-Z0-9_-])*` which means the
    // first character must be a letter, and all following characters
    // must be a dash, underscore, letter or digit.
    //
    // Defaults to `edge-cache-token`.
    string query_parameter = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Input only. 64-byte ed25519 private key for this Media CDN key.
  bytes private_key = 1 [(google.api.field_behavior) = INPUT_ONLY];

  // The keyset name of the Media CDN key.
  string key_name = 2;

  // Optional. If set, the URL will be signed using the Media CDN token.
  // Otherwise, the URL would be signed using the standard Media CDN signature.
  TokenConfig token_config = 3 [(google.api.field_behavior) = OPTIONAL];
}
