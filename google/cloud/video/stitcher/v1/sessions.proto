// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/video/stitcher/v1/companions.proto";
import "google/cloud/video/stitcher/v1/events.proto";
import "google/cloud/video/stitcher/v1/live_configs.proto";
import "google/protobuf/duration.proto";

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "SessionsProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Metadata for a VOD session. The session expires 4 hours after its creation.
message VodSession {
  option (google.api.resource) = {
    type: "videostitcher.googleapis.com/VodSession"
    pattern: "projects/{project}/locations/{location}/vodSessions/{vod_session}"
  };

  // Defines fields related to Google Ad Manager (GAM). This should be set if
  // GAM is being used for ads.
  message GamSettings {
    // Required. Ad Manager network code.
    string network_code = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The stream ID generated by Ad Manager.
    string stream_id = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Output only. The name of the VOD session, in the form of
  // `projects/{project_number}/locations/{location}/vodSessions/{id}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Metadata of what was stitched into the content.
  Interstitials interstitials = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The playback URI of the stitched content.
  string play_uri = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // URI of the media to stitch. For most use cases, you should create a
  // [VodConfig][google.cloud.video.stitcher.v1.VodConfig] with this information
  // rather than setting this field directly.
  string source_uri = 5;

  // Ad tag URI. For most use cases, you should create a
  // [VodConfig][google.cloud.video.stitcher.v1.VodConfig] with this information
  // rather than setting this field directly.
  string ad_tag_uri = 6;

  // Key value pairs for ad tag macro replacement, only available for VOD
  // sessions that do not implement Google Ad manager ad insertion. If the
  // specified ad tag URI has macros, this field provides the mapping to the
  // value that will replace the macro in the ad tag URI.
  //
  // Macros are designated by square brackets, for example:
  //
  //   Ad tag URI: `"https://doubleclick.google.com/ad/1?geo_id=[geoId]"`
  //
  //   Ad tag macro map: `{"geoId": "123"}`
  //
  //   Fully qualified ad tag:
  //   `"https://doubleclick.google.com/ad/1?geo_id=123"`
  map<string, string> ad_tag_macro_map = 7;

  // Additional options that affect the output of the manifest.
  ManifestOptions manifest_options = 9;

  // Output only. The generated ID of the VodSession's source media.
  string asset_id = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Determines how the ad should be tracked.
  AdTracking ad_tracking = 11 [(google.api.field_behavior) = REQUIRED];

  // This field should be set with appropriate values if GAM is being used for
  // ads.
  GamSettings gam_settings = 13;

  // The resource name of the VOD config for this session, in the form of
  // `projects/{project}/locations/{location}/vodConfigs/{id}`.
  string vod_config = 14 [(google.api.resource_reference) = {
    type: "videostitcher.googleapis.com/VodConfig"
  }];
}

// Describes what was stitched into a VOD session's manifest.
message Interstitials {
  // List of ad breaks ordered by time.
  repeated VodSessionAdBreak ad_breaks = 1;

  // Information related to the content of the VOD session.
  VodSessionContent session_content = 2;
}

// Metadata for an inserted ad in a VOD session.
message VodSessionAd {
  // Duration in seconds of the ad.
  google.protobuf.Duration duration = 1;

  // Metadata of companion ads associated with the ad.
  CompanionAds companion_ads = 2;

  // The list of progress tracking events for the ad break. These can be of
  // the following IAB types: `MUTE`, `UNMUTE`, `PAUSE`, `CLICK`,
  // `CLICK_THROUGH`, `REWIND`, `RESUME`, `ERROR`, `FULLSCREEN`,
  // `EXIT_FULLSCREEN`, `EXPAND`, `COLLAPSE`, `ACCEPT_INVITATION_LINEAR`,
  // `CLOSE_LINEAR`, `SKIP`.
  repeated Event activity_events = 3;
}

// Metadata for the entire stitched content in a VOD session.
message VodSessionContent {
  // The total duration in seconds of the content including the ads stitched
  // in.
  google.protobuf.Duration duration = 1;
}

// Metadata for an inserted ad break.
message VodSessionAdBreak {
  // List of events that are expected to be triggered, ordered by time.
  repeated ProgressEvent progress_events = 1;

  // Ordered list of ads stitched into the ad break.
  repeated VodSessionAd ads = 2;

  // Ad break end time in seconds relative to the start of the VOD asset.
  google.protobuf.Duration end_time_offset = 3;

  // Ad break start time in seconds relative to the start of the VOD asset.
  google.protobuf.Duration start_time_offset = 4;
}

// Metadata for a live session. The session expires 5 minutes after the client
// stops fetching the session's playlists.
message LiveSession {
  option (google.api.resource) = {
    type: "videostitcher.googleapis.com/LiveSession"
    pattern: "projects/{project}/locations/{location}/liveSessions/{live_session}"
  };

  // Defines fields related to Google Ad Manager (GAM).
  message GamSettings {
    // Required. The stream ID generated by Ad Manager. This must be set if GAM
    // is being used for ads and the session uses client-side ad tracking.
    string stream_id = 1 [(google.api.field_behavior) = REQUIRED];

    // [Targeting
    // parameters](https://support.google.com/admanager/answer/7320899) to send
    // to Ad Manager to generate a stream ID. This should only be set if the
    // session uses server-side ad tracking.
    map<string, string> targeting_parameters = 4;
  }

  // Output only. The name of the live session, in the form of
  // `projects/{project}/locations/{location}/liveSessions/{id}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The URI to play the live session's ad-stitched stream.
  string play_uri = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Key value pairs for ad tag macro replacement, only available for live
  // sessions that do not implement Google Ad manager ad insertion. If the
  // specified ad tag URI has macros, this field provides the mapping to the
  // value that will replace the macro in the ad tag URI.
  //
  // Macros are designated by square brackets, for example:
  //
  //   Ad tag URI: "https://doubleclick.google.com/ad/1?geo_id=[geoId]"
  //
  //   Ad tag macros: `{"geoId": "123"}`
  //
  //   Fully qualified ad tag:
  //   `"https://doubleclick.google.com/ad/1?geo_id=123"`
  map<string, string> ad_tag_macros = 6;

  // Additional options that affect the output of the manifest.
  ManifestOptions manifest_options = 10;

  // This field should be set with appropriate values if GAM is being used for
  // ads.
  GamSettings gam_settings = 15;

  // Required. The resource name of the live config for this session, in the
  // form of `projects/{project}/locations/{location}/liveConfigs/{id}`.
  string live_config = 16 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/LiveConfig"
    }
  ];

  // Determines how the ad should be tracked. This overrides the value set in
  // the live config for this session.
  AdTracking ad_tracking = 17;
}

// Options for manifest generation.
message ManifestOptions {
  // Defines the ordering policy during manifest generation.
  enum OrderPolicy {
    // Ordering policy is not specified.
    ORDER_POLICY_UNSPECIFIED = 0;

    // Order by ascending.
    ASCENDING = 1;

    // Order by descending.
    DESCENDING = 2;
  }

  // If specified, the output manifest will only return renditions matching the
  // specified filters.
  repeated RenditionFilter include_renditions = 1;

  // If specified, the output manifest will orders the video and muxed
  // renditions by bitrate according to the ordering policy.
  OrderPolicy bitrate_order = 2;
}

// Filters for a video or muxed redition.
message RenditionFilter {
  // Bitrate in bits per second for the rendition. If set, only renditions with
  // the exact bitrate will match.
  int32 bitrate_bps = 1;

  // Codecs for the rendition. If set, only renditions with the exact value
  // will match.
  string codecs = 2;
}
