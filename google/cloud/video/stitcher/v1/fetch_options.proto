// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "FetchOptionsProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Options on how fetches should be made.
message FetchOptions {
  // Custom headers to pass into fetch request.
  // Headers must have a maximum of 3 key value pairs.
  // Each key value pair must have a maximum of 256 characters per key and 256
  // characters per value.
  map<string, string> headers = 1;
}
