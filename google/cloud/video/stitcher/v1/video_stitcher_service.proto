// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/video/stitcher/v1/ad_tag_details.proto";
import "google/cloud/video/stitcher/v1/cdn_keys.proto";
import "google/cloud/video/stitcher/v1/live_configs.proto";
import "google/cloud/video/stitcher/v1/sessions.proto";
import "google/cloud/video/stitcher/v1/slates.proto";
import "google/cloud/video/stitcher/v1/stitch_details.proto";
import "google/cloud/video/stitcher/v1/vod_configs.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "VideoStitcherServiceProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Video-On-Demand content stitching API allows you to insert ads
// into (VoD) video on demand files. You will be able to render custom
// scrubber bars with highlighted ads, enforce ad policies, allow
// seamless playback and tracking on native players and monetize
// content with any standard VMAP compliant ad server.
service VideoStitcherService {
  option (google.api.default_host) = "videostitcher.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new CDN key.
  rpc CreateCdnKey(CreateCdnKeyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/cdnKeys"
      body: "cdn_key"
    };
    option (google.api.method_signature) = "parent,cdn_key,cdn_key_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.video.stitcher.v1.CdnKey"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Lists all CDN keys in the specified project and location.
  rpc ListCdnKeys(ListCdnKeysRequest) returns (ListCdnKeysResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/cdnKeys"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified CDN key.
  rpc GetCdnKey(GetCdnKeyRequest) returns (CdnKey) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/cdnKeys/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes the specified CDN key.
  rpc DeleteCdnKey(DeleteCdnKeyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/cdnKeys/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Updates the specified CDN key. Only update fields specified
  // in the call method body.
  rpc UpdateCdnKey(UpdateCdnKeyRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{cdn_key.name=projects/*/locations/*/cdnKeys/*}"
      body: "cdn_key"
    };
    option (google.api.method_signature) = "cdn_key,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.video.stitcher.v1.CdnKey"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Creates a client side playback VOD session and returns the full
  // tracking and playback metadata of the session.
  rpc CreateVodSession(CreateVodSessionRequest) returns (VodSession) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/vodSessions"
      body: "vod_session"
    };
    option (google.api.method_signature) = "parent,vod_session";
  }

  // Returns the full tracking, playback metadata, and relevant ad-ops
  // logs for the specified VOD session.
  rpc GetVodSession(GetVodSessionRequest) returns (VodSession) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/vodSessions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of detailed stitching information of the specified VOD
  // session.
  rpc ListVodStitchDetails(ListVodStitchDetailsRequest)
      returns (ListVodStitchDetailsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/vodSessions/*}/vodStitchDetails"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified stitching information for the specified VOD session.
  rpc GetVodStitchDetail(GetVodStitchDetailRequest) returns (VodStitchDetail) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/vodSessions/*/vodStitchDetails/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Return the list of ad tag details for the specified VOD session.
  rpc ListVodAdTagDetails(ListVodAdTagDetailsRequest)
      returns (ListVodAdTagDetailsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/vodSessions/*}/vodAdTagDetails"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified ad tag detail for the specified VOD session.
  rpc GetVodAdTagDetail(GetVodAdTagDetailRequest) returns (VodAdTagDetail) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/vodSessions/*/vodAdTagDetails/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Return the list of ad tag details for the specified live session.
  rpc ListLiveAdTagDetails(ListLiveAdTagDetailsRequest)
      returns (ListLiveAdTagDetailsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/liveSessions/*}/liveAdTagDetails"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified ad tag detail for the specified live session.
  rpc GetLiveAdTagDetail(GetLiveAdTagDetailRequest) returns (LiveAdTagDetail) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/liveSessions/*/liveAdTagDetails/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a slate.
  rpc CreateSlate(CreateSlateRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/slates"
      body: "slate"
    };
    option (google.api.method_signature) = "parent,slate,slate_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.video.stitcher.v1.Slate"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Lists all slates in the specified project and location.
  rpc ListSlates(ListSlatesRequest) returns (ListSlatesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/slates"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified slate.
  rpc GetSlate(GetSlateRequest) returns (Slate) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/slates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the specified slate.
  rpc UpdateSlate(UpdateSlateRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{slate.name=projects/*/locations/*/slates/*}"
      body: "slate"
    };
    option (google.api.method_signature) = "slate,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.video.stitcher.v1.Slate"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Deletes the specified slate.
  rpc DeleteSlate(DeleteSlateRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/slates/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Creates a new live session.
  rpc CreateLiveSession(CreateLiveSessionRequest) returns (LiveSession) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/liveSessions"
      body: "live_session"
    };
    option (google.api.method_signature) = "parent,live_session";
  }

  // Returns the details for the specified live session.
  rpc GetLiveSession(GetLiveSessionRequest) returns (LiveSession) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/liveSessions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Registers the live config with the provided unique ID in
  // the specified region.
  rpc CreateLiveConfig(CreateLiveConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/liveConfigs"
      body: "live_config"
    };
    option (google.api.method_signature) = "parent,live_config,live_config_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.video.stitcher.v1.LiveConfig"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Lists all live configs managed by the Video Stitcher that
  // belong to the specified project and region.
  rpc ListLiveConfigs(ListLiveConfigsRequest)
      returns (ListLiveConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/liveConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified live config managed by the Video
  // Stitcher service.
  rpc GetLiveConfig(GetLiveConfigRequest) returns (LiveConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/liveConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes the specified live config.
  rpc DeleteLiveConfig(DeleteLiveConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/liveConfigs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Updates the specified LiveConfig. Only update fields specified
  // in the call method body.
  rpc UpdateLiveConfig(UpdateLiveConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{live_config.name=projects/*/locations/*/liveConfigs/*}"
      body: "live_config"
    };
    option (google.api.method_signature) = "live_config,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.video.stitcher.v1.LiveConfig"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Registers the VOD config with the provided unique ID in
  // the specified region.
  rpc CreateVodConfig(CreateVodConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/vodConfigs"
      body: "vod_config"
    };
    option (google.api.method_signature) = "parent,vod_config,vod_config_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.video.stitcher.v1.VodConfig"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Lists all VOD configs managed by the Video Stitcher API that
  // belong to the specified project and region.
  rpc ListVodConfigs(ListVodConfigsRequest) returns (ListVodConfigsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/vodConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified VOD config managed by the Video
  // Stitcher API service.
  rpc GetVodConfig(GetVodConfigRequest) returns (VodConfig) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/vodConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes the specified VOD config.
  rpc DeleteVodConfig(DeleteVodConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/vodConfigs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }

  // Updates the specified VOD config. Only update fields specified
  // in the call method body.
  rpc UpdateVodConfig(UpdateVodConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{vod_config.name=projects/*/locations/*/vodConfigs/*}"
      body: "vod_config"
    };
    option (google.api.method_signature) = "vod_config,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.video.stitcher.v1.VodConfig"
      metadata_type: "google.cloud.video.stitcher.v1.OperationMetadata"
    };
  }
}

// Request message for VideoStitcherService.createCdnKey.
message CreateCdnKeyRequest {
  // Required. The project in which the CDN key should be created, in the form
  // of `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/CdnKey"
    }
  ];

  // Required. The CDN key resource to create.
  CdnKey cdn_key = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the CDN key, which will become the final
  // component of the CDN key's resource name.
  //
  // This value should conform to RFC-1034, which restricts to
  // lower-case letters, numbers, and hyphen, with the first character a
  // letter, the last a letter or a number, and a 63 character maximum.
  string cdn_key_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for VideoStitcherService.listCdnKeys.
message ListCdnKeysRequest {
  // Required. The project that contains the list of CDN keys, in the form of
  // `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/CdnKey"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Response message for VideoStitcher.ListCdnKeys.
message ListCdnKeysResponse {
  // List of CDN keys.
  repeated CdnKey cdn_keys = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for VideoStitcherService.getCdnKey.
message GetCdnKeyRequest {
  // Required. The name of the CDN key to be retrieved, in the form of
  // `projects/{project}/locations/{location}/cdnKeys/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/CdnKey"
    }
  ];
}

// Request message for VideoStitcherService.deleteCdnKey.
message DeleteCdnKeyRequest {
  // Required. The name of the CDN key to be deleted, in the form of
  // `projects/{project_number}/locations/{location}/cdnKeys/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/CdnKey"
    }
  ];
}

// Request message for VideoStitcherService.updateCdnKey.
message UpdateCdnKeyRequest {
  // Required. The CDN key resource which replaces the resource on the server.
  CdnKey cdn_key = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource.
  // For the `FieldMask` definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for VideoStitcherService.createVodSession
message CreateVodSessionRequest {
  // Required. The project and location in which the VOD session should be
  // created, in the form of `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/VodSession"
    }
  ];

  // Required. Parameters for creating a session.
  VodSession vod_session = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for VideoStitcherService.getVodSession
message GetVodSessionRequest {
  // Required. The name of the VOD session to be retrieved, in the form of
  // `projects/{project_number}/locations/{location}/vodSessions/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/VodSession"
    }
  ];
}

// Request message for VideoStitcherService.listVodStitchDetails.
message ListVodStitchDetailsRequest {
  // Required. The VOD session where the stitch details belong to, in the form
  // of `projects/{project}/locations/{location}/vodSessions/{id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/VodStitchDetail"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;
}

// Response message for VideoStitcherService.listVodStitchDetails.
message ListVodStitchDetailsResponse {
  // A List of stitch Details.
  repeated VodStitchDetail vod_stitch_details = 1;

  // The pagination token.
  string next_page_token = 2;
}

// Request message for VideoStitcherService.getVodStitchDetail.
message GetVodStitchDetailRequest {
  // Required. The name of the stitch detail in the specified VOD session, in
  // the form of
  // `projects/{project}/locations/{location}/vodSessions/{vod_session_id}/vodStitchDetails/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/VodStitchDetail"
    }
  ];
}

// Request message for VideoStitcherService.listVodAdTagDetails.
message ListVodAdTagDetailsRequest {
  // Required. The VOD session which the ad tag details belong to, in the form
  // of `projects/{project}/locations/{location}/vodSessions/{vod_session_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/VodAdTagDetail"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;
}

// Response message for VideoStitcherService.listVodAdTagDetails.
message ListVodAdTagDetailsResponse {
  // A List of ad tag details.
  repeated VodAdTagDetail vod_ad_tag_details = 1;

  // The pagination token.
  string next_page_token = 2;
}

// Request message for VideoStitcherService.getVodAdTagDetail
message GetVodAdTagDetailRequest {
  // Required. The name of the ad tag detail for the specified VOD session, in
  // the form of
  // `projects/{project}/locations/{location}/vodSessions/{vod_session_id}/vodAdTagDetails/{vod_ad_tag_detail}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/VodAdTagDetail"
    }
  ];
}

// Request message for VideoStitcherService.listLiveAdTagDetails.
message ListLiveAdTagDetailsRequest {
  // Required. The resource parent in the form of
  // `projects/{project}/locations/{location}/liveSessions/{live_session}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/LiveAdTagDetail"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The pagination token returned from a previous List request.
  string page_token = 3;
}

// Response message for VideoStitcherService.listLiveAdTagDetails.
message ListLiveAdTagDetailsResponse {
  // A list of live session ad tag details.
  repeated LiveAdTagDetail live_ad_tag_details = 1;

  // The pagination token.
  string next_page_token = 2;
}

// Request message for VideoStitcherService.getLiveAdTagDetail
message GetLiveAdTagDetailRequest {
  // Required. The resource name in the form of
  // `projects/{project}/locations/{location}/liveSessions/{live_session}/liveAdTagDetails/{live_ad_tag_detail}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/LiveAdTagDetail"
    }
  ];
}

// Request message for VideoStitcherService.createSlate.
message CreateSlateRequest {
  // Required. The project in which the slate should be created, in the form of
  // `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/Slate"
    }
  ];

  // Required. The unique identifier for the slate.
  // This value should conform to RFC-1034, which restricts to
  // lower-case letters, numbers, and hyphen, with the first character a
  // letter, the last a letter or a number, and a 63 character maximum.
  string slate_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The slate to create.
  Slate slate = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 4;
}

// Request message for VideoStitcherService.getSlate.
message GetSlateRequest {
  // Required. The name of the slate to be retrieved, of the slate, in the form
  // of `projects/{project_number}/locations/{location}/slates/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/Slate"
    }
  ];
}

// Request message for VideoStitcherService.listSlates.
message ListSlatesRequest {
  // Required. The project to list slates, in the form of
  // `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/Slate"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Response message for VideoStitcherService.listSlates.
message ListSlatesResponse {
  // The list of slates
  repeated Slate slates = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for VideoStitcherService.updateSlate.
message UpdateSlateRequest {
  // Required. The resource with updated fields.
  Slate slate = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask which specifies fields which should be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for VideoStitcherService.deleteSlate.
message DeleteSlateRequest {
  // Required. The name of the slate to be deleted, in the form of
  // `projects/{project_number}/locations/{location}/slates/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/Slate"
    }
  ];
}

// Request message for VideoStitcherService.createLiveSession.
message CreateLiveSessionRequest {
  // Required. The project and location in which the live session should be
  // created, in the form of `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/LiveSession"
    }
  ];

  // Required. Parameters for creating a live session.
  LiveSession live_session = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for VideoStitcherService.getSession.
message GetLiveSessionRequest {
  // Required. The name of the live session, in the form of
  // `projects/{project_number}/locations/{location}/liveSessions/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/LiveSession"
    }
  ];
}

// Request message for VideoStitcherService.createLiveConfig
message CreateLiveConfigRequest {
  // Required. The project in which the live config should be created, in
  // the form of `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/LiveConfig"
    }
  ];

  // Required. The unique identifier ID to use for the live config.
  string live_config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The live config resource to create.
  LiveConfig live_config = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 4;
}

// Request message for VideoStitcherService.listLiveConfig.
message ListLiveConfigsRequest {
  // Required. The project that contains the list of live configs, in the
  // form of `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/LiveConfig"
    }
  ];

  // The maximum number of items to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;

  // Optional. The filter to apply to list results (see
  // [Filtering](https://google.aip.dev/160)).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the ordering of results following
  // [Cloud API
  // syntax](https://cloud.google.com/apis/design/design_patterns#sorting_order).
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for VideoStitcher.ListLiveConfig.
message ListLiveConfigsResponse {
  // List of live configs.
  repeated LiveConfig live_configs = 1;

  // The pagination token.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for VideoStitcherService.getLiveConfig.
message GetLiveConfigRequest {
  // Required. The name of the live config to be retrieved, in the form
  // of
  // `projects/{project_number}/locations/{location}/liveConfigs/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/LiveConfig"
    }
  ];
}

// Request message for VideoStitcherService.deleteLiveConfig.
message DeleteLiveConfigRequest {
  // Required. The name of the live config to be deleted, in the form of
  // `projects/{project_number}/locations/{location}/liveConfigs/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/LiveConfig"
    }
  ];
}

// Request message for VideoStitcherService.updateLiveConfig.
message UpdateLiveConfigRequest {
  // Required. The LiveConfig resource which replaces the resource on the
  // server.
  LiveConfig live_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource.
  // For the `FieldMask` definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for VideoStitcherService.createVodConfig
message CreateVodConfigRequest {
  // Required. The project in which the VOD config should be created, in
  // the form of `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/VodConfig"
    }
  ];

  // Required. The unique identifier ID to use for the VOD config.
  string vod_config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The VOD config resource to create.
  VodConfig vod_config = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for VideoStitcherService.listVodConfig.
message ListVodConfigsRequest {
  // Required. The project that contains the list of VOD configs, in the
  // form of `projects/{project_number}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "videostitcher.googleapis.com/VodConfig"
    }
  ];

  // Optional. The maximum number of items to return.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous List request,
  // if any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The filter to apply to list results (see
  // [Filtering](https://google.aip.dev/160)).
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the ordering of results following
  // [Cloud API
  // syntax](https://cloud.google.com/apis/design/design_patterns#sorting_order).
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for VideoStitcher.ListVodConfig.
message ListVodConfigsResponse {
  // List of VOD configs.
  repeated VodConfig vod_configs = 1;

  // The pagination token.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for VideoStitcherService.getVodConfig.
message GetVodConfigRequest {
  // Required. The name of the VOD config to be retrieved, in the form
  // of `projects/{project_number}/locations/{location}/vodConfigs/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/VodConfig"
    }
  ];
}

// Request message for VideoStitcherService.deleteVodConfig.
message DeleteVodConfigRequest {
  // Required. The name of the VOD config to be deleted, in the form of
  // `projects/{project_number}/locations/{location}/vodConfigs/{id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "videostitcher.googleapis.com/VodConfig"
    }
  ];
}

// Request message for VideoStitcherService.updateVodConfig.
message UpdateVodConfigRequest {
  // Required. The VOD config resource which replaces the resource on the
  // server.
  VodConfig vod_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The update mask applies to the resource.
  // For the `FieldMask` definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;
}
