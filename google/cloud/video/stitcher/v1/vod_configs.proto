// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/video/stitcher/v1/fetch_options.proto";

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "VodConfigsProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Metadata used to register VOD configs.
message VodConfig {
  option (google.api.resource) = {
    type: "videostitcher.googleapis.com/VodConfig"
    pattern: "projects/{project}/locations/{location}/vodConfigs/{vod_config}"
  };

  // State of the VOD config.
  enum State {
    // State is not specified.
    STATE_UNSPECIFIED = 0;

    // VOD config is being created.
    CREATING = 1;

    // VOD config is ready for use.
    READY = 2;

    // VOD config is queued up for deletion.
    DELETING = 3;
  }

  // Output only. The resource name of the VOD config, in the form of
  // `projects/{project}/locations/{location}/vodConfigs/{id}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Source URI for the VOD stream manifest.
  string source_uri = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The default ad tag associated with this VOD config.
  string ad_tag_uri = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Google Ad Manager (GAM) metadata.
  GamVodConfig gam_vod_config = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. State of the VOD config.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Options for fetching source manifests and segments.
  FetchOptions source_fetch_options = 8;
}

// Metadata used for GAM ad decisioning.
message GamVodConfig {
  // Required. Ad Manager network code to associate with the VOD config.
  string network_code = 1 [(google.api.field_behavior) = REQUIRED];
}
