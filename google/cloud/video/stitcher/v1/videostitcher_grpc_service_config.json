{"methodConfig": [{"name": [{"service": "google.cloud.video.stitcher.v1.VideoStitcherService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "Create<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "ListCdnKeys"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "GetCdnKey"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "DeleteCdnKey"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "UpdateCdnKey"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "CreateLiveConfig"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "ListLiveConfigs"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "GetLiveConfig"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "DeleteLiveConfig"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "CreateSlate"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "ListSlates"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "GetSlate"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "DeleteSlate"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "UpdateSlate"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "CreateLiveSession"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "GetLiveSession"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "ListLiveAdTagDetails"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "GetLiveAdTagDetail"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "CreateVodSession"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "GetVodSession"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "ListVodAdTagDetails"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "GetVodAdTagDetail"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "ListVodStitchDetails"}, {"service": "google.cloud.video.stitcher.v1.VideoStitcherService", "method": "GetVodStitchDetail"}], "timeout": "60s"}]}