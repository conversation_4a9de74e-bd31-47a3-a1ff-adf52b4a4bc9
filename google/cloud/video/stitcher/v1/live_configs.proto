// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/video/stitcher/v1/fetch_options.proto";
import "google/protobuf/duration.proto";

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "LiveConfigsProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Determines the ad tracking policy.
enum AdTracking {
  // The ad tracking policy is not specified.
  AD_TRACKING_UNSPECIFIED = 0;

  // Client-side ad tracking is specified. The client player is expected to
  // trigger playback and activity events itself.
  CLIENT = 1;

  // The Video Stitcher API will trigger playback events on behalf of
  // the client player.
  SERVER = 2;
}

// Metadata for used to register live configs.
message LiveConfig {
  option (google.api.resource) = {
    type: "videostitcher.googleapis.com/LiveConfig"
    pattern: "projects/{project}/locations/{location}/liveConfigs/{live_config}"
  };

  // State of the live config.
  enum State {
    // State is not specified.
    STATE_UNSPECIFIED = 0;

    // Live config is being created.
    CREATING = 1;

    // Live config is ready for use.
    READY = 2;

    // Live config is queued up for deletion.
    DELETING = 3;
  }

  // Defines the ad stitching behavior in case the ad duration does not align
  // exactly with the ad break boundaries. If not specified, the default is
  // `CUT_CURRENT`.
  enum StitchingPolicy {
    // Stitching policy is not specified.
    STITCHING_POLICY_UNSPECIFIED = 0;

    // Cuts an ad short and returns to content in the middle of the ad.
    CUT_CURRENT = 1;

    // Finishes stitching the current ad before returning to content.
    COMPLETE_AD = 2;
  }

  // Output only. The resource name of the live config, in the form of
  // `projects/{project}/locations/{location}/liveConfigs/{id}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Source URI for the live stream manifest.
  string source_uri = 2 [(google.api.field_behavior) = REQUIRED];

  // The default ad tag associated with this live stream config.
  string ad_tag_uri = 3;

  // Additional metadata used to register a live stream with Google Ad Manager
  // (GAM)
  GamLiveConfig gam_live_config = 4;

  // Output only. State of the live config.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Determines how the ads are tracked.
  AdTracking ad_tracking = 6 [(google.api.field_behavior) = REQUIRED];

  // This must refer to a slate in the same
  // project. If Google Ad Manager (GAM) is used for ads, this string sets the
  // value of `slateCreativeId` in
  // https://developers.google.com/ad-manager/api/reference/v202211/LiveStreamEventService.LiveStreamEvent#slateCreativeId
  string default_slate = 7 [(google.api.resource_reference) = {
    type: "videostitcher.googleapis.com/Slate"
  }];

  // Defines the stitcher behavior in case an ad does not align exactly with
  // the ad break boundaries. If not specified, the default is `CUT_CURRENT`.
  StitchingPolicy stitching_policy = 8;

  // The configuration for prefetching ads.
  PrefetchConfig prefetch_config = 10;

  // Options for fetching source manifests and segments.
  FetchOptions source_fetch_options = 16;
}

// The configuration for prefetch ads.
message PrefetchConfig {
  // Required. Indicates whether the option to prefetch ad requests is enabled.
  bool enabled = 1 [(google.api.field_behavior) = REQUIRED];

  // The duration in seconds of the part of the break to be prefetched.
  // This field is only relevant if prefetch is enabled.
  // You should set this duration to as long as possible to increase the
  // benefits of prefetching, but not longer than the shortest ad break
  // expected. For example, for a live event with 30s and 60s ad breaks, the
  // initial duration should be set to 30s.
  google.protobuf.Duration initial_ad_request_duration = 2;
}

// Metadata used to register a live stream with Google Ad Manager (GAM)
message GamLiveConfig {
  // Required. Ad Manager network code to associate with the live config.
  string network_code = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The asset key identifier generated for the live config.
  string asset_key = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The custom asset key identifier generated for the live config.
  string custom_asset_key = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
