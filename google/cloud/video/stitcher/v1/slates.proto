// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "SlatesProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Slate object
message Slate {
  option (google.api.resource) = {
    type: "videostitcher.googleapis.com/Slate"
    pattern: "projects/{project}/locations/{location}/slates/{slate}"
  };

  // GamSlate object has Google Ad Manager (GAM) related properties for the
  // slate.
  message GamSlate {
    // Required. Ad Manager network code to associate with the live config.
    string network_code = 1 [(google.api.field_behavior) = REQUIRED];

    // Output only. The identifier generated for the slate by GAM.
    int64 gam_slate_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The name of the slate, in the form of
  // `projects/{project_number}/locations/{location}/slates/{id}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The URI to fetch the source content for the slate. This URI must return an
  // MP4 video with at least one audio track.
  string uri = 2;

  // gam_slate has all the GAM-related attributes of slates.
  GamSlate gam_slate = 3;
}
