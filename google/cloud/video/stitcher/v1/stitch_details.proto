// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/struct.proto";

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "StitchDetailsProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Information related to the interstitial of a VOD session. This resource is
// only available for VOD sessions that do not implement Google Ad Manager ad
// insertion.
message VodStitchDetail {
  option (google.api.resource) = {
    type: "videostitcher.googleapis.com/VodStitchDetail"
    pattern: "projects/{project}/locations/{location}/vodSessions/{vod_session}/vodStitchDetails/{vod_stitch_detail}"
  };

  // The name of the stitch detail in the specified VOD session, in the form of
  // `projects/{project}/locations/{location}/vodSessions/{vod_session_id}/vodStitchDetails/{id}`.
  string name = 1;

  // A list of ad processing details for the fetched ad playlist.
  repeated AdStitchDetail ad_stitch_details = 3;
}

// Metadata for a stitched ad.
message AdStitchDetail {
  // Required. The ad break ID of the processed ad.
  string ad_break_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The ad ID of the processed ad.
  string ad_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The time offset of the processed ad.
  google.protobuf.Duration ad_time_offset = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Indicates the reason why the ad has been skipped.
  string skip_reason = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The metadata of the chosen media file for the ad.
  map<string, google.protobuf.Value> media = 5
      [(google.api.field_behavior) = OPTIONAL];
}
