// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.livestream.logging.v1;

import "google/cloud/video/livestream/v1/resources.proto";
import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/video/livestream/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LogsProto";
option java_package = "com.google.cloud.video.livestream.logging.v1";

// Logs of activities related to the Channels.
message ChannelActivity {
  // Message is for more details of the log and instructions to users.
  string message = 1;

  // Different types of the logs.
  oneof activity_type {
    // The channel streaming state changes.
    StreamingStateChange streaming_state_change = 2;

    // An error happens with the video pipeline.
    StreamingError streaming_error = 3;

    // The channel has accepted an input stream.
    InputAccept input_accept = 4;

    // An error happens with the input stream.
    InputError input_error = 5;

    // An input stream disconnects.
    InputDisconnect input_disconnect = 6;

    // An event state changes.
    EventStateChange event_state_change = 7;

    // A SCTE35 command is received.
    Scte35Command scte35_command_received = 8;
  }
}

// StreamingStateChange records when the channel streaming state changes.
message StreamingStateChange {
  // New streaming state of the channel.
  google.cloud.video.livestream.v1.Channel.StreamingState new_state = 1;

  // Previous streaming state of the channel.
  google.cloud.video.livestream.v1.Channel.StreamingState previous_state = 2;
}

// StreamingError records when an error happens with the video pipeline.
message StreamingError {
  // A description of the reason for the streaming error.
  google.rpc.Status error = 1;
}

// InputAccept records when the channel has accepted an input stream.
message InputAccept {
  // ID of the input stream.
  string stream_id = 1;

  // The user-defined key for the input attachment.
  string input_attachment = 2;

  // Properties of the input stream.
  InputStreamProperty input_stream_property = 3;
}

// InputError records when an error happens with the input stream.
message InputError {
  // ID of the input stream.
  string stream_id = 1;

  // The user-defined key for the input attachment. If the stream doesn’t belong
  // to any input attachment, this field is empty.
  string input_attachment = 2;

  // Properties of the input stream.
  InputStreamProperty input_stream_property = 3;

  // A description of the reason for the error with the input stream.
  google.rpc.Status error = 4;
}

// Properties of the input stream.
message InputStreamProperty {
  // Properties of the video streams.
  repeated VideoStream video_streams = 1;

  // Properties of the audio streams.
  repeated AudioStream audio_streams = 2;
}

// Properties of the video stream.
message VideoStream {
  // Index of this video stream.
  int32 index = 1;

  // Properties of the video format.
  VideoFormat video_format = 2;
}

// Properties of the video format.
message VideoFormat {
  // Video codec used in this video stream.
  string codec = 1;

  // The width of the video stream in pixels.
  int32 width_pixels = 2;

  // The height of the video stream in pixels.
  int32 height_pixels = 3;

  // The frame rate of the input video stream.
  double frame_rate = 4;
}

// Properties of the audio stream.
message AudioStream {
  // Index of this audio stream.
  int32 index = 1;

  // Properties of the audio format.
  AudioFormat audio_format = 2;
}

// Properties of the audio format.
message AudioFormat {
  // Audio codec used in this audio stream.
  string codec = 1;

  // The number of audio channels.
  int32 channel_count = 2;

  // A list of channel names specifying the layout of the audio channels.
  repeated string channel_layout = 3;
}

// InputDisconnect records when an input stream disconnects.
message InputDisconnect {
  // ID of the input stream.
  string stream_id = 1;

  // The user-defined key for the input attachment.
  string input_attachment = 2;
}

// EventStateChange records when an Event state changes.
message EventStateChange {
  // Resource name of the event.
  string event_id = 1;

  // New state of the event.
  google.cloud.video.livestream.v1.Event.State new_state = 2;

  // Previous state of the event.
  google.cloud.video.livestream.v1.Event.State previous_state = 3;
}

// Scte35Command includes details of a received SCTE35 command.
message Scte35Command {
  // SpliceTime contains information about the execution time of the splice
  // insert.
  message SpliceTime {
    // If true, the execution time of the splice insert is specified.
    bool time_specified_flag = 1;

    // The time of the splice insert.
    int64 pts_time = 2;
  }

  // BreakDuration contains information about the duration of the splice
  // insert.
  message BreakDuration {
    // If true, the splice insert will automatically return upon finishing.
    bool auto_return = 1;

    // Duration of the splice insert.
    int64 duration = 2;
  }

  // Fine grained control on the scte command insertion for a specific
  // elementary stream. This is ignored if program_splice_flag is true.
  message Component {
    // Elementary stream PID that the scte command should be inserted into.
    int32 component_tag = 1;

    // The time of the insert.
    SpliceTime splice_time = 2;
  }

  // SpliceInsert contains information about the splice insert.
  message SpliceInsert {
    // Event ID of the scte command.
    int32 splice_event_id = 1;

    // Whether this scte command is to cancel another scheduled scte command.
    bool splice_event_cancel_indicator = 2;

    // Whether this scte command is cueing out the current program.
    bool out_of_network_indicator = 3;

    // If true, send splice insert to all streams.
    bool program_splice_flag = 4;

    // If true, the duration of the event is specified.
    bool duration_flag = 5;

    // If true, the event should be executed immediately.
    bool splice_immediate_flag = 6;

    // Information about the execution time of the splice insert.
    SpliceTime splice_time = 7;

    // Information about the duration of the splice insert.
    BreakDuration break_duration = 8;

    // Unique ID for a viewing event.
    int32 unique_program_id = 9;

    // ID for an avail within one unique_program_id.
    int32 avail_num = 10;

    // The number of avails within the current viewing event.
    int32 avails_expected = 11;

    // Number of components.
    int32 component_count = 12;

    // Components of the program.
    repeated Component components = 13;
  }

  // SpliceInfoSection contains information about the splice insert.
  message SpliceInfoSection {
    // Overflow of pts_time, when pts_time doesn't have enough bits
    // to represent the time.
    int64 pts_adjustment = 1;

    // Information about the splice insert.
    SpliceInsert splice_insert = 2;
  }

  // Information about the splice insert.
  SpliceInfoSection splice_info_section = 1;
}
