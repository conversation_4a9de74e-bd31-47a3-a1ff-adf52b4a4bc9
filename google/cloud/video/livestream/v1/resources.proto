// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.livestream.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/video/livestream/v1/outputs.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Video.LiveStream.V1";
option go_package = "cloud.google.com/go/video/livestream/apiv1/livestreampb;livestreampb";
option java_multiple_files = true;
option java_outer_classname = "ResourcesProto";
option java_package = "com.google.cloud.video.livestream.v1";
option php_namespace = "Google\\Cloud\\Video\\LiveStream\\V1";
option ruby_package = "Google::Cloud::Video::LiveStream::V1";

// Input resource represents the endpoint from which the channel ingests
// the input stream.
message Input {
  option (google.api.resource) = {
    type: "livestream.googleapis.com/Input"
    pattern: "projects/{project}/locations/{location}/inputs/{input}"
  };

  // The type of the input.
  enum Type {
    // Input type is not specified.
    TYPE_UNSPECIFIED = 0;

    // Input will take an rtmp input stream.
    RTMP_PUSH = 1;

    // Input will take an srt (Secure Reliable Transport) input stream.
    SRT_PUSH = 2;
  }

  // Tier of the input specification.
  enum Tier {
    // Tier is not specified.
    TIER_UNSPECIFIED = 0;

    // Resolution < 1280x720. Bitrate <= 6 Mbps. FPS <= 60.
    SD = 1;

    // Resolution <= 1920x1080. Bitrate <= 25 Mbps. FPS <= 60.
    HD = 2;

    // Resolution <= 4096x2160. Not supported yet.
    UHD = 3;
  }

  // Security rules for access control. Each field represents one security rule.
  // Only when the source of the input stream satisfies all the fields, this
  // input stream can be accepted.
  message SecurityRule {
    // At least one ip range must match unless none specified. The IP range is
    // defined by CIDR block: for example, `*********/24` for a range and
    // `*********/32` for a single IP address.
    repeated string ip_ranges = 1;
  }

  // The resource name of the input, in the form of:
  // `projects/{project}/locations/{location}/inputs/{inputId}`.
  string name = 1;

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update time.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-defined key/value metadata.
  map<string, string> labels = 4;

  // Source type.
  Type type = 5;

  // Tier defines the maximum input specification that is accepted by the
  // video pipeline. The billing is charged based on the tier specified here.
  // See [Pricing](https://cloud.google.com/livestream/pricing) for more detail.
  // The default is `HD`.
  Tier tier = 14;

  // Output only. URI to push the input stream to.
  // Its format depends on the input
  // [type][google.cloud.video.livestream.v1.Input.type], for example:
  //
  // *  `RTMP_PUSH`: `rtmp://*******/live/{STREAM-ID}`
  // *  `SRT_PUSH`: `srt://*******:4201?streamid={STREAM-ID}`
  string uri = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Preprocessing configurations.
  PreprocessingConfig preprocessing_config = 9;

  // Security rule for access control.
  SecurityRule security_rules = 12;

  // Output only. The information for the input stream. This field will be
  // present only when this input receives the input stream.
  InputStreamProperty input_stream_property = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Channel resource represents the processor that does a user-defined
// "streaming" operation, which includes getting an input stream through an
// input, transcoding it to multiple renditions, and publishing output live
// streams in certain formats (for example, HLS or DASH) to the specified
// location.
message Channel {
  option (google.api.resource) = {
    type: "livestream.googleapis.com/Channel"
    pattern: "projects/{project}/locations/{location}/channels/{channel}"
  };

  // Location of output file(s) in a Google Cloud Storage bucket.
  message Output {
    // URI for the output file(s). For example, `gs://my-bucket/outputs/`.
    string uri = 1;
  }

  // State of streaming operation that the channel is running.
  enum StreamingState {
    // Streaming state is not specified.
    STREAMING_STATE_UNSPECIFIED = 0;

    // Channel is getting the input stream, generating the live streams to the
    // specified output location.
    STREAMING = 1;

    // Channel is waiting for the input stream through the input.
    AWAITING_INPUT = 2;

    // Channel is running, but has trouble publishing the live streams onto the
    // specified output location (for example, the specified Cloud Storage
    // bucket is not writable).
    STREAMING_ERROR = 4;

    // Channel is generating live streams with no input stream. Live streams are
    // filled out with black screen, while input stream is missing.
    // Not supported yet.
    STREAMING_NO_INPUT = 5;

    // Channel is stopped, finishing live streams.
    STOPPED = 6;

    // Channel is starting.
    STARTING = 7;

    // Channel is stopping.
    STOPPING = 8;
  }

  // The resource name of the channel, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}`.
  string name = 1;

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update time.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-defined key/value metadata.
  map<string, string> labels = 4;

  // A list of input attachments that this channel uses.
  // One channel can have multiple inputs as the input sources. Only one
  // input can be selected as the input source at one time.
  repeated InputAttachment input_attachments = 16;

  // Output only. The
  // [InputAttachment.key][google.cloud.video.livestream.v1.InputAttachment.key]
  // that serves as the current input source. The first input in the
  // [input_attachments][google.cloud.video.livestream.v1.Channel.input_attachments]
  // is the initial input source.
  string active_input = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Information about the output (that is, the Cloud Storage bucket
  // to store the generated live stream).
  Output output = 9 [(google.api.field_behavior) = REQUIRED];

  // List of elementary streams.
  repeated ElementaryStream elementary_streams = 10;

  // List of multiplexing settings for output streams.
  repeated MuxStream mux_streams = 11;

  // List of output manifests.
  repeated Manifest manifests = 12;

  // List of output sprite sheets.
  repeated SpriteSheet sprite_sheets = 13;

  // Output only. State of the streaming operation.
  StreamingState streaming_state = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A description of the reason for the streaming error. This
  // property is always present when
  // [streaming_state][google.cloud.video.livestream.v1.Channel.streaming_state]
  // is
  // [STREAMING_ERROR][google.cloud.video.livestream.v1.Channel.StreamingState.STREAMING_ERROR].
  google.rpc.Status streaming_error = 18
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Configuration of platform logs for this channel.
  LogConfig log_config = 19;

  // Configuration of timecode for this channel.
  TimecodeConfig timecode_config = 21;

  // Encryption configurations for this channel. Each configuration has an ID
  // which is referred to by each MuxStream to indicate which configuration is
  // used for that output.
  repeated Encryption encryptions = 24;

  // The configuration for input sources defined in
  // [input_attachments][google.cloud.video.livestream.v1.Channel.input_attachments].
  InputConfig input_config = 25;

  // Optional. Configuration for retention of output files for this channel.
  RetentionConfig retention_config = 26
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. List of static overlay images. Those images display over the
  // output content for the whole duration of the live stream.
  repeated StaticOverlay static_overlays = 27
      [(google.api.field_behavior) = OPTIONAL];
}

// 2D normalized coordinates.
message NormalizedCoordinate {
  // Optional. Normalized x coordinate. Valid range is [0.0, 1.0]. Default is 0.
  double x = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Normalized y coordinate. Valid range is [0.0, 1.0]. Default is 0.
  double y = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Normalized resolution.
message NormalizedResolution {
  // Optional. Normalized width. Valid range is [0.0, 1.0]. Default is 0.
  double w = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Normalized height. Valid range is [0.0, 1.0]. Default is 0.
  double h = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for the static overlay.
message StaticOverlay {
  // Required. Asset to use for the overlaid image.
  // The asset must be represented in the form of:
  // `projects/{project}/locations/{location}/assets/{assetId}`.
  // The asset's resource type must be image.
  string asset = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Asset"
    }
  ];

  // Optional. Normalized image resolution, based on output video resolution.
  // Valid values are [0.0, 1.0]. To respect the original image aspect ratio,
  // set either `w` or `h` to 0. To use the original image resolution, set both
  // `w` and `h` to 0. The default is {0, 0}.
  NormalizedResolution resolution = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Position of the image in terms of normalized coordinates of the
  // upper-left corner of the image, based on output video resolution. For
  // example, use the x and y coordinates {0, 0} to position the top-left corner
  // of the overlay animation in the top-left corner of the output video.
  NormalizedCoordinate position = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Target image opacity. Valid values are from `1.0` (solid,
  // default) to `0.0` (transparent), exclusive. Set this to a value greater
  // than `0.0`.
  double opacity = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Configuration for the input sources of a channel.
message InputConfig {
  // Input switch mode.
  enum InputSwitchMode {
    // The input switch mode is not specified.
    INPUT_SWITCH_MODE_UNSPECIFIED = 0;

    // Automatic failover is enabled. The primary input stream is always
    // preferred over its backup input streams configured using the
    // [AutomaticFailover][google.cloud.video.livestream.v1.InputAttachment.AutomaticFailover]
    // field.
    FAILOVER_PREFER_PRIMARY = 1;

    // Automatic failover is disabled. You must use the
    // [inputSwitch][google.cloud.video.livestream.v1.Event.input_switch] event
    // to switch the active input source for the channel to stream from. When
    // this mode is chosen, the
    // [AutomaticFailover][google.cloud.video.livestream.v1.InputAttachment.AutomaticFailover]
    // field is ignored.
    MANUAL = 3;
  }

  // Input switch mode. Default mode is `FAILOVER_PREFER_PRIMARY`.
  InputSwitchMode input_switch_mode = 1;
}

// Configuration of platform logs.
// See [Using and managing platform
// logs](https://cloud.google.com/logging/docs/api/platform-logs#managing-logs)
// for more information about how to view platform logs through Cloud Logging.
message LogConfig {
  // The severity level of platform logging for this channel. Logs with a
  // severity level higher than or equal to the chosen severity level will be
  // logged and can be viewed through Cloud Logging.
  // The severity level of a log is ranked as followed from low to high: DEBUG <
  // INFO < NOTICE < WARNING < ERROR < CRITICAL < ALERT < EMERGENCY.
  // See
  // [LogSeverity](https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry#logseverity)
  // for more information.
  enum LogSeverity {
    // Log severity is not specified. This is the same as log severity is OFF.
    LOG_SEVERITY_UNSPECIFIED = 0;

    // Log is turned off.
    OFF = 1;

    // Log with severity higher than or equal to DEBUG are logged.
    DEBUG = 100;

    // Logs with severity higher than or equal to INFO are logged.
    INFO = 200;

    // Logs with severity higher than or equal to WARNING are logged.
    WARNING = 400;

    // Logs with severity higher than or equal to ERROR are logged.
    ERROR = 500;
  }

  // The severity level of platform logging for this resource.
  LogSeverity log_severity = 1;
}

// Configuration for retention of output files.
message RetentionConfig {
  // The minimum duration for which the output files from the channel will
  // remain in the output bucket. After this duration, output files are
  // deleted asynchronously.
  //
  // When the channel is deleted, all output files are deleted from the output
  // bucket asynchronously.
  //
  // If omitted or set to zero, output files will remain in the output bucket
  // based on
  // [Manifest.segment_keep_duration][google.cloud.video.livestream.v1.Manifest.segment_keep_duration],
  // which defaults to 60s.
  //
  // If both retention_window_duration and
  // [Manifest.segment_keep_duration][google.cloud.video.livestream.v1.Manifest.segment_keep_duration]
  // are set, retention_window_duration is used and
  // [Manifest.segment_keep_duration][google.cloud.video.livestream.v1.Manifest.segment_keep_duration]
  // is ignored.
  google.protobuf.Duration retention_window_duration = 1;
}

// Properties of the input stream.
message InputStreamProperty {
  // The time that the current input stream is accepted and the connection is
  // established.
  google.protobuf.Timestamp last_establish_time = 1;

  // Properties of the video streams.
  repeated VideoStreamProperty video_streams = 2;

  // Properties of the audio streams.
  repeated AudioStreamProperty audio_streams = 3;
}

// Properties of the video stream.
message VideoStreamProperty {
  // Index of this video stream.
  int32 index = 1;

  // Properties of the video format.
  VideoFormat video_format = 2;
}

// Properties of the video format.
message VideoFormat {
  // Video codec used in this video stream.
  string codec = 1;

  // The width of the video stream in pixels.
  int32 width_pixels = 2;

  // The height of the video stream in pixels.
  int32 height_pixels = 3;

  // The frame rate of the input video stream.
  double frame_rate = 4;
}

// Properties of the audio stream.
message AudioStreamProperty {
  // Index of this audio stream.
  int32 index = 1;

  // Properties of the audio format.
  AudioFormat audio_format = 2;
}

// Properties of the audio format.
message AudioFormat {
  // Audio codec used in this audio stream.
  string codec = 1;

  // The number of audio channels.
  int32 channel_count = 2;

  // A list of channel names specifying the layout of the audio channels.
  repeated string channel_layout = 3;
}

// A group of information for attaching an input resource to this channel.
message InputAttachment {
  // Configurations to follow when automatic failover happens.
  message AutomaticFailover {
    // The
    // [InputAttachment.key][google.cloud.video.livestream.v1.InputAttachment.key]s
    // of inputs to failover to when this input is disconnected. Currently, only
    // up to one backup input is supported.
    repeated string input_keys = 1;
  }

  // A unique key for this input attachment. The key must be 1-63
  // characters in length. The key must begin and end with a letter (regardless
  // of case) or a number, but can contain dashes or underscores in between.
  string key = 1;

  // The resource name of an existing input, in the form of:
  // `projects/{project}/locations/{location}/inputs/{inputId}`.
  string input = 2 [(google.api.resource_reference) = {
    type: "livestream.googleapis.com/Input"
  }];

  // Automatic failover configurations.
  AutomaticFailover automatic_failover = 3;
}

// Event is a sub-resource of a channel, which can be scheduled by the user to
// execute operations on a channel resource without having to stop the channel.
message Event {
  option (google.api.resource) = {
    type: "livestream.googleapis.com/Event"
    pattern: "projects/{project}/locations/{location}/channels/{channel}/events/{event}"
  };

  // Switches to another input stream. Automatic failover is then disabled.
  message InputSwitchTask {
    // The
    // [InputAttachment.key][google.cloud.video.livestream.v1.InputAttachment.key]
    // of the input to switch to.
    string input_key = 1;
  }

  // Inserts a new ad opportunity.
  message AdBreakTask {
    // Duration of an ad opportunity. Must be greater than 0.
    google.protobuf.Duration duration = 1;
  }

  // Inserts a slate.
  message SlateTask {
    // Optional. Duration of the slate. Must be greater than 0 if specified.
    // Omit this field for a long running slate.
    google.protobuf.Duration duration = 1;

    // Slate asset to use for the duration. If its duration is less than the
    // duration of the SlateTask, then the slate loops. The slate must be
    // represented in the form of:
    // `projects/{project}/locations/{location}/assets/{assetId}`.
    string asset = 2 [(google.api.resource_reference) = {
      type: "livestream.googleapis.com/Asset"
    }];
  }

  // Stops any events which are currently running. This only applies to events
  // with a duration.
  message ReturnToProgramTask {}

  // Mutes the stream.
  message MuteTask {
    // Duration for which the stream should be muted. If omitted, the stream
    // will be muted until an UnmuteTask event is sent.
    google.protobuf.Duration duration = 1;
  }

  // Unmutes the stream. The task fails if the stream is not currently muted.
  message UnmuteTask {}

  // State of the event
  enum State {
    // Event state is not specified.
    STATE_UNSPECIFIED = 0;

    // Event is scheduled but not executed yet.
    SCHEDULED = 1;

    // Event is being executed.
    RUNNING = 2;

    // Event has been successfully executed.
    SUCCEEDED = 3;

    // Event fails to be executed.
    FAILED = 4;

    // Event has been created but not scheduled yet.
    PENDING = 5;

    // Event was stopped before running for its full duration.
    STOPPED = 6;
  }

  // The resource name of the event, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}/events/{eventId}`.
  string name = 1;

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update time.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-defined key/value metadata.
  map<string, string> labels = 4;

  // Required. Operation to be executed by this event.
  oneof task {
    // Switches to another input stream.
    InputSwitchTask input_switch = 5;

    // Inserts a new ad opportunity.
    AdBreakTask ad_break = 6;

    // Stops any running ad break.
    ReturnToProgramTask return_to_program = 13;

    // Inserts a slate.
    SlateTask slate = 14;

    // Mutes the stream.
    MuteTask mute = 15;

    // Unmutes the stream.
    UnmuteTask unmute = 16;
  }

  // When this field is set to true, the event will be executed at the earliest
  // time that the server can schedule the event and
  // [execution_time][google.cloud.video.livestream.v1.Event.execution_time]
  // will be populated with the time that the server actually schedules the
  // event.
  bool execute_now = 9;

  // The time to execute the event. If you set
  // [execute_now][google.cloud.video.livestream.v1.Event.execute_now] to
  // `true`, then do not set this field in the `CreateEvent` request. In
  // this case, the server schedules the event and populates this field. If you
  // set [execute_now][google.cloud.video.livestream.v1.Event.execute_now] to
  // `false`, then you must set this field to at least 10 seconds in the future
  // or else the event can't be created.
  google.protobuf.Timestamp execution_time = 10;

  // Output only. The state of the event.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An error object that describes the reason for the failure.
  // This property is always present when `state` is `FAILED`.
  google.rpc.Status error = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Clip is a sub-resource under channel. Each clip represents a clipping
// operation that generates a VOD playlist from its channel given a set of
// timestamp ranges.
message Clip {
  option (google.api.resource) = {
    type: "livestream.googleapis.com/Clip"
    pattern: "projects/{project}/locations/{location}/channels/{channel}/clips/{clip}"
  };

  // State of clipping operation.
  enum State {
    // State is not specified.
    STATE_UNSPECIFIED = 0;

    // The operation is pending to be picked up by the server.
    PENDING = 1;

    // The server admitted this create clip request, and
    // outputs are under processing.
    CREATING = 2;

    // Outputs are available in the specified Cloud Storage bucket. For
    // additional information, see the `outputs` field.
    SUCCEEDED = 3;

    // The operation has failed. For additional information, see the `error`
    // field.
    FAILED = 4;
  }

  // TimeSlice represents a tuple of Unix epoch timestamps that specifies a time
  // range.
  message TimeSlice {
    // The mark-in Unix epoch time in the original live stream manifest.
    google.protobuf.Timestamp markin_time = 1;

    // The mark-out Unix epoch time in the original live stream manifest.
    google.protobuf.Timestamp markout_time = 2;
  }

  // Slice represents a slice of the requested clip.
  message Slice {
    // The allowlist forms of a slice.
    oneof kind {
      // A slice in form of a tuple of Unix epoch time.
      TimeSlice time_slice = 1;
    }
  }

  // ClipManifest identifies a source manifest for the generated clip manifest.
  message ClipManifest {
    // Required. A unique key that identifies a manifest config in the parent
    // channel. This key is the same as `channel.manifests.key` for the selected
    // manifest.
    string manifest_key = 1 [(google.api.field_behavior) = REQUIRED];

    // Output only. The output URI of the generated clip manifest. This field
    // will be populated when the CreateClip request is accepted. Current output
    // format is provided below but may change in the future. Please read this
    // field to get the uri to the generated clip manifest. Format:
    // {clip.output_uri}/{channel.manifest.fileName} Example:
    // gs://my-bucket/clip-outputs/main.m3u8
    string output_uri = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The resource name of the clip, in the following format:
  // `projects/{project}/locations/{location}/channels/{c}/clips/{clipId}`.
  // `{clipId}` is a user-specified resource id that conforms to the following
  // criteria:
  //
  // 1. 1 character minimum, 63 characters maximum
  // 2. Only contains letters, digits, underscores, and hyphens
  string name = 1;

  // Output only. The creation timestamp of the clip resource.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when the clip request starts to be processed.
  google.protobuf.Timestamp start_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update timestamp of the clip resource.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels associated with this resource. Each label is a key-value pair.
  map<string, string> labels = 5;

  // Output only. The state of the clip.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Specify the `output_uri` to determine where to place the clip segments and
  // clip manifest files in Cloud Storage. The manifests specified in
  // `clip_manifests` fields will be placed under this URI. The exact URI of the
  // generated manifests will be provided in `clip_manifests.output_uri` for
  // each manifest.
  // Example:
  // "output_uri": "gs://my-bucket/clip-outputs"
  // "clip_manifests.output_uri": "gs://my-bucket/clip-outputs/main.m3u8"
  string output_uri = 7;

  // Output only. An error object that describes the reason for the failure.
  // This property only presents when `state` is `FAILED`.
  google.rpc.Status error = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The specified ranges of segments to generate a clip.
  repeated Slice slices = 10;

  // Required. A list of clip manifests. Currently only one clip manifest is
  // allowed.
  repeated ClipManifest clip_manifests = 12
      [(google.api.field_behavior) = REQUIRED];
}

// An asset represents a video or an image.
message Asset {
  option (google.api.resource) = {
    type: "livestream.googleapis.com/Asset"
    pattern: "projects/{project}/locations/{location}/assets/{asset}"
  };

  // VideoAsset represents a video. The supported formats are MP4, MPEG-TS, and
  // FLV. The supported video codec is H264. The supported audio codecs are
  // AAC, AC3, MP2, and MP3.
  message VideoAsset {
    // Cloud Storage URI of the video. The format is `gs://my-bucket/my-object`.
    string uri = 1;
  }

  // Image represents an image. The supported formats are JPEG, PNG.
  message ImageAsset {
    // Cloud Storage URI of the image. The format is `gs://my-bucket/my-object`.
    string uri = 1;
  }

  // State of the asset resource.
  enum State {
    // State is not specified.
    STATE_UNSPECIFIED = 0;

    // The asset is being created.
    CREATING = 1;

    // The asset is ready for use.
    ACTIVE = 2;

    // The asset is being deleted.
    DELETING = 3;

    // The asset has an error.
    ERROR = 4;
  }

  // The resource name of the asset, in the form of:
  // `projects/{project}/locations/{location}/assets/{assetId}`.
  string name = 1;

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update time.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-defined key/value metadata.
  map<string, string> labels = 4;

  // The reference to the asset.
  // The maximum size of the resource is 250 MB.
  oneof resource {
    // VideoAsset represents a video.
    VideoAsset video = 5;

    // ImageAsset represents an image.
    ImageAsset image = 6;
  }

  // Based64-encoded CRC32c checksum of the asset file. For more information,
  // see the crc32c checksum of the [Cloud Storage Objects
  // resource](https://cloud.google.com/storage/docs/json_api/v1/objects).
  // If crc32c is omitted or left empty when the asset is created, this field is
  // filled by the crc32c checksum of the Cloud Storage object indicated by
  // [VideoAsset.uri][google.cloud.video.livestream.v1.Asset.VideoAsset.uri] or
  // [ImageAsset.uri][google.cloud.video.livestream.v1.Asset.ImageAsset.uri]. If
  // crc32c is set, the asset can't be created if the crc32c value does not
  // match with the crc32c checksum of the Cloud Storage object indicated by
  // [VideoAsset.uri][google.cloud.video.livestream.v1.Asset.VideoAsset.uri] or
  // [ImageAsset.uri][google.cloud.video.livestream.v1.Asset.ImageAsset.uri].
  string crc32c = 7;

  // Output only. The state of the asset resource.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Only present when `state` is `ERROR`. The reason for the error
  // state of the asset.
  google.rpc.Status error = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Encryption settings.
message Encryption {
  // Configuration for secrets stored in Google Secret Manager.
  message SecretManagerSource {
    // Required. The name of the Secret Version containing the encryption key.
    // `projects/{project}/secrets/{secret_id}/versions/{version_number}`
    string secret_version = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "secretmanager.googleapis.com/SecretVersion"
      }
    ];
  }

  // Widevine configuration.
  message Widevine {}

  // Fairplay configuration.
  message Fairplay {}

  // Playready configuration.
  message Playready {}

  // Clearkey configuration.
  message Clearkey {}

  // Defines configuration for DRM systems in use. If a field is omitted,
  // that DRM system will be considered to be disabled.
  message DrmSystems {
    // Widevine configuration.
    Widevine widevine = 1;

    // Fairplay configuration.
    Fairplay fairplay = 2;

    // Playready configuration.
    Playready playready = 3;

    // Clearkey configuration.
    Clearkey clearkey = 4;
  }

  // Configuration for HLS AES-128 encryption.
  message Aes128Encryption {}

  // Configuration for HLS SAMPLE-AES encryption.
  message SampleAesEncryption {}

  // Configuration for MPEG-Dash Common Encryption (MPEG-CENC).
  message MpegCommonEncryption {
    // Required. Specify the encryption scheme, supported schemes:
    // - `cenc` - AES-CTR subsample
    // - `cbcs`- AES-CBC subsample pattern
    string scheme = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. Identifier for this set of encryption options. The ID must be
  // 1-63 characters in length. The ID must begin and end with a letter
  // (regardless of case) or a number, but can contain dashes or underscores in
  // between.
  string id = 1 [(google.api.field_behavior) = REQUIRED];

  // Defines where content keys are stored.
  oneof secret_source {
    // For keys stored in Google Secret Manager.
    SecretManagerSource secret_manager_key_source = 7;
  }

  // Required. Configuration for DRM systems.
  DrmSystems drm_systems = 3 [(google.api.field_behavior) = REQUIRED];

  // Encryption modes for HLS and MPEG-Dash.
  oneof encryption_mode {
    // Configuration for HLS AES-128 encryption.
    Aes128Encryption aes128 = 4;

    // Configuration for HLS SAMPLE-AES encryption.
    SampleAesEncryption sample_aes = 5;

    // Configuration for MPEG-Dash Common Encryption (MPEG-CENC).
    MpegCommonEncryption mpeg_cenc = 6;
  }
}

// Pool resource defines the configuration of Live Stream pools for a specific
// location. Currently we support only one pool resource per project per
// location. After the creation of the first input, a default pool is created
// automatically at "projects/{project}/locations/{location}/pools/default".
message Pool {
  option (google.api.resource) = {
    type: "livestream.googleapis.com/Pool"
    pattern: "projects/{project}/locations/{location}/pools/{pool}"
  };

  // Defines the network configuration for the pool.
  message NetworkConfig {
    // peered_network is the network resource URL of the network that is peered
    // to the service provider network. Must be of the format
    // projects/NETWORK_PROJECT_NUMBER/global/networks/NETWORK_NAME, where
    // NETWORK_PROJECT_NUMBER is the project number of the Cloud project that
    // holds your VPC network and NETWORK_NAME is the name of your VPC network.
    // If peered_network is omitted or empty, the pool will use endpoints that
    // are publicly available.
    string peered_network = 1 [(google.api.resource_reference) = {
      type: "compute.googleapis.com/Network"
    }];
  }

  // The resource name of the pool, in the form of:
  // `projects/{project}/locations/{location}/pools/{poolId}`.
  string name = 1;

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update time.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-defined key/value metadata.
  map<string, string> labels = 4;

  // Network configuration for the pool.
  NetworkConfig network_config = 5;
}
