// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.livestream.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/video/livestream/v1/resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Video.LiveStream.V1";
option go_package = "cloud.google.com/go/video/livestream/apiv1/livestreampb;livestreampb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.video.livestream.v1";
option php_namespace = "Google\\Cloud\\Video\\LiveStream\\V1";
option ruby_package = "Google::Cloud::Video::LiveStream::V1";
option (google.api.resource_definition) = {
  type: "secretmanager.googleapis.com/SecretVersion"
  pattern: "projects/{project}/secrets/{secret}/versions/{version}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Network"
  pattern: "projects/{project}/global/networks/{network}"
};

// Using Live Stream API, you can generate live streams in the various
// renditions and streaming formats. The streaming format include HTTP Live
// Streaming (HLS) and Dynamic Adaptive Streaming over HTTP (DASH). You can send
// a source stream in the various ways, including Real-Time Messaging
// Protocol (RTMP) and Secure Reliable Transport (SRT).
service LivestreamService {
  option (google.api.default_host) = "livestream.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a channel with the provided unique ID in the specified
  // region.
  rpc CreateChannel(CreateChannelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/channels"
      body: "channel"
    };
    option (google.api.method_signature) = "parent,channel,channel_id";
    option (google.longrunning.operation_info) = {
      response_type: "Channel"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns a list of all channels in the specified region.
  rpc ListChannels(ListChannelsRequest) returns (ListChannelsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/channels"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified channel.
  rpc GetChannel(GetChannelRequest) returns (Channel) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/channels/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes the specified channel.
  rpc DeleteChannel(DeleteChannelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/channels/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the specified channel.
  rpc UpdateChannel(UpdateChannelRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{channel.name=projects/*/locations/*/channels/*}"
      body: "channel"
    };
    option (google.api.method_signature) = "channel,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Channel"
      metadata_type: "OperationMetadata"
    };
  }

  // Starts the specified channel. Part of the video pipeline will be created
  // only when the StartChannel request is received by the server.
  rpc StartChannel(StartChannelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/channels/*}:start"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "ChannelOperationResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Stops the specified channel. Part of the video pipeline will be released
  // when the StopChannel request is received by the server.
  rpc StopChannel(StopChannelRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/channels/*}:stop"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "ChannelOperationResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates an input with the provided unique ID in the specified region.
  rpc CreateInput(CreateInputRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/inputs"
      body: "input"
    };
    option (google.api.method_signature) = "parent,input,input_id";
    option (google.longrunning.operation_info) = {
      response_type: "Input"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns a list of all inputs in the specified region.
  rpc ListInputs(ListInputsRequest) returns (ListInputsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/inputs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified input.
  rpc GetInput(GetInputRequest) returns (Input) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/inputs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes the specified input.
  rpc DeleteInput(DeleteInputRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/inputs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the specified input.
  rpc UpdateInput(UpdateInputRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{input.name=projects/*/locations/*/inputs/*}"
      body: "input"
    };
    option (google.api.method_signature) = "input,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Input"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates an event with the provided unique ID in the specified channel.
  rpc CreateEvent(CreateEventRequest) returns (Event) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/channels/*}/events"
      body: "event"
    };
    option (google.api.method_signature) = "parent,event,event_id";
  }

  // Returns a list of all events in the specified channel.
  rpc ListEvents(ListEventsRequest) returns (ListEventsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/channels/*}/events"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified event.
  rpc GetEvent(GetEventRequest) returns (Event) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/channels/*/events/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes the specified event.
  rpc DeleteEvent(DeleteEventRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/channels/*/events/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of all clips in the specified channel.
  rpc ListClips(ListClipsRequest) returns (ListClipsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/channels/*}/clips"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified clip.
  rpc GetClip(GetClipRequest) returns (Clip) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/channels/*/clips/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a clip with the provided clip ID in the specified channel.
  rpc CreateClip(CreateClipRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/channels/*}/clips"
      body: "clip"
    };
    option (google.api.method_signature) = "parent,clip,clip_id";
    option (google.longrunning.operation_info) = {
      response_type: "Clip"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes the specified clip job resource. This method only deletes the clip
  // job and does not delete the VOD clip stored in the GCS.
  rpc DeleteClip(DeleteClipRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/channels/*/clips/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a Asset with the provided unique ID in the specified
  // region.
  rpc CreateAsset(CreateAssetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/assets"
      body: "asset"
    };
    option (google.api.method_signature) = "parent,asset,asset_id";
    option (google.longrunning.operation_info) = {
      response_type: "Asset"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes the specified asset if it is not used.
  rpc DeleteAsset(DeleteAssetRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/assets/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Returns the specified asset.
  rpc GetAsset(GetAssetRequest) returns (Asset) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/assets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Returns a list of all assets in the specified region.
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/assets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified pool.
  rpc GetPool(GetPoolRequest) returns (Pool) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/pools/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the specified pool.
  rpc UpdatePool(UpdatePoolRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{pool.name=projects/*/locations/*/pools/*}"
      body: "pool"
    };
    option (google.api.method_signature) = "pool,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Pool"
      metadata_type: "OperationMetadata"
    };
  }
}

// Request message for "LivestreamService.CreateAsset".
message CreateAssetRequest {
  // Required. The parent location for the resource, in the form of:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "livestream.googleapis.com/Asset"
    }
  ];

  // Required. The asset resource to be created.
  Asset asset = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the asset resource to be created.
  // This value must be 1-63 characters, begin and end with `[a-z0-9]`,
  // could contain dashes (-) in between.
  string asset_id = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 4;
}

// Request message for "LivestreamService.DeleteAsset".
message DeleteAssetRequest {
  // Required. The name of the asset resource, in the form of:
  // `projects/{project}/locations/{location}/assets/{assetId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Asset"
    }
  ];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 2;
}

// Request message for "LivestreamService.ListAssets".
message ListAssetsRequest {
  // Required. The parent location for the resource, in the form of:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "livestream.googleapis.com/Asset"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Response message for "LivestreamService.ListAssets".
message ListAssetsResponse {
  // The list of Assets
  repeated Asset assets = 1;

  // The next_page_token value returned from a previous List request, if any.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for "LivestreamService.GetAsset".
message GetAssetRequest {
  // Required. Name of the resource, in the following form:
  // `projects/{project}/locations/{location}/assets/{asset}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Asset"
    }
  ];
}

// Request message for "LivestreamService.CreateChannel".
message CreateChannelRequest {
  // Required. The parent location for the resource, in the form of:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "livestream.googleapis.com/Channel"
    }
  ];

  // Required. The channel resource to be created.
  Channel channel = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the channel resource to be created.
  // This value must be 1-63 characters, begin and end with `[a-z0-9]`,
  // could contain dashes (-) in between.
  string channel_id = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 4;
}

// Request message for "LivestreamService.ListChannels".
message ListChannelsRequest {
  // Required. The parent location for the resource, in the form of:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "livestream.googleapis.com/Channel"
    }
  ];

  // The maximum number of items to return. If unspecified, server
  // will pick an appropriate default. Server may return fewer items than
  // requested. A caller should only rely on response's
  // [next_page_token][google.cloud.video.livestream.v1.ListChannelsResponse.next_page_token]
  // to determine if there are more items left to be queried.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;

  // The filter to apply to list results.
  string filter = 4;

  // Specifies the ordering of results following syntax at
  // https://cloud.google.com/apis/design/design_patterns#sorting_order.
  string order_by = 5;
}

// Response message for "LivestreamService.ListChannels".
message ListChannelsResponse {
  // A list of channels.
  repeated Channel channels = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for "LivestreamService.GetChannel".
message GetChannelRequest {
  // Required. The name of the channel resource, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Channel"
    }
  ];
}

// Request message for "LivestreamService.DeleteChannel".
message DeleteChannelRequest {
  // Required. The name of the channel resource, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Channel"
    }
  ];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 2;

  // If the `force` field is set to the default value of `false`, you must
  // delete all of a channel's events before you can delete the channel itself.
  // If the field is set to `true`, requests to delete a channel also delete
  // associated channel events.
  bool force = 3;
}

// Request message for "LivestreamService.UpdateChannel".
message UpdateChannelRequest {
  // Field mask is used to specify the fields to be overwritten in the Channel
  // resource by the update. You can only update the following fields:
  //
  // * [`inputAttachments`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.channels#inputattachment)
  // * [`inputConfig`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.channels#inputconfig)
  // * [`output`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.channels#output)
  // * [`elementaryStreams`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.channels#elementarystream)
  // * [`muxStreams`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.channels#muxstream)
  // * [`manifests`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.channels#manifest)
  // * [`spriteSheets`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.channels#spritesheet)
  // * [`logConfig`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.channels#logconfig)
  // * [`timecodeConfig`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.channels#timecodeconfig)
  // * [`encryptions`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.channels#encryption)
  //
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask.
  //
  // If the mask is not present, then each field from the list above is updated
  // if the field appears in the request payload. To unset a field, add the
  // field to the update mask and remove it from the request payload.
  google.protobuf.FieldMask update_mask = 1;

  // Required. The channel resource to be updated.
  Channel channel = 2 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 3;
}

// Request message for "LivestreamService.StartChannel".
message StartChannelRequest {
  // Required. The name of the channel resource, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Channel"
    }
  ];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 2;
}

// Request message for "LivestreamService.StopChannel".
message StopChannelRequest {
  // Required. The name of the channel resource, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Channel"
    }
  ];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 2;
}

// Request message for "LivestreamService.CreateInput".
message CreateInputRequest {
  // Required. The parent location for the resource, in the form of:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "livestream.googleapis.com/Input"
    }
  ];

  // Required. The input resource to be created.
  Input input = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the input resource to be created.
  // This value must be 1-63 characters, begin and end with `[a-z0-9]`,
  // could contain dashes (-) in between.
  string input_id = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 4;
}

// Request message for "LivestreamService.ListInputs".
message ListInputsRequest {
  // Required. The parent location for the resource, in the form of:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "livestream.googleapis.com/Input"
    }
  ];

  // The maximum number of items to return. If unspecified, server
  // will pick an appropriate default. Server may return fewer items than
  // requested. A caller should only rely on response's
  // [next_page_token][google.cloud.video.livestream.v1.ListInputsResponse.next_page_token]
  // to determine if there are more items left to be queried.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;

  // The filter to apply to list results.
  string filter = 4;

  // Specifies the ordering of results following syntax at [Sorting
  // Order](https://cloud.google.com/apis/design/design_patterns#sorting_order).
  string order_by = 5;
}

// Response message for "LivestreamService.ListInputs".
message ListInputsResponse {
  // A list of inputs.
  repeated Input inputs = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for "LivestreamService.GetInput".
message GetInputRequest {
  // Required. The name of the input resource, in the form of:
  // `projects/{project}/locations/{location}/inputs/{inputId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Input"
    }
  ];
}

// Request message for "LivestreamService.DeleteInput".
message DeleteInputRequest {
  // Required. The name of the input resource, in the form of:
  // `projects/{project}/locations/{location}/inputs/{inputId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Input"
    }
  ];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 2;
}

// Request message for "LivestreamService.UpdateInput".
message UpdateInputRequest {
  // Field mask is used to specify the fields to be overwritten in the Input
  // resource by the update. You can only update the following fields:
  //
  // * [`preprocessingConfig`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.inputs#PreprocessingConfig)
  // * [`securityRules`](https://cloud.google.com/livestream/docs/reference/rest/v1/projects.locations.inputs#SecurityRule)
  //
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask.
  //
  // If the mask is not present, then each field from the list above is updated
  // if the field appears in the request payload. To unset a field, add the
  // field to the update mask and remove it from the request payload.
  google.protobuf.FieldMask update_mask = 1;

  // Required. The input resource to be updated.
  Input input = 2 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 3;
}

// Request message for "LivestreamService.CreateEvent".
message CreateEventRequest {
  // Required. The parent channel for the resource, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "livestream.googleapis.com/Event"
    }
  ];

  // Required. The event resource to be created.
  Event event = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the event resource to be created.
  // This value must be 1-63 characters, begin and end with `[a-z0-9]`,
  // could contain dashes (-) in between.
  string event_id = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 4;
}

// Request message for "LivestreamService.ListEvents".
message ListEventsRequest {
  // Required. The parent channel for the resource, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "livestream.googleapis.com/Event"
    }
  ];

  // The maximum number of items to return. If unspecified, server
  // will pick an appropriate default. Server may return fewer items than
  // requested. A caller should only rely on response's
  // [next_page_token][google.cloud.video.livestream.v1.ListEventsResponse.next_page_token]
  // to determine if there are more items left to be queried.
  int32 page_size = 2;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 3;

  // The filter to apply to list results.
  string filter = 4;

  // Specifies the ordering of results following syntax at
  // https://cloud.google.com/apis/design/design_patterns#sorting_order.
  string order_by = 5;
}

// Response message for "LivestreamService.ListEvents".
message ListEventsResponse {
  // A list of events.
  repeated Event events = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for "LivestreamService.GetEvent".
message GetEventRequest {
  // Required. The name of the event resource, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}/events/{eventId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Event"
    }
  ];
}

// Request message for "LivestreamService.DeleteEvent".
message DeleteEventRequest {
  // Required. The name of the event resource, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}/events/{eventId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "livestream.googleapis.com/Event"
    }
  ];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 2;
}

// Response message for Start/Stop Channel long-running operations.
message ChannelOperationResponse {}

// Request message for "LivestreamService.ListClips".
message ListClipsRequest {
  // Required. Parent value for ListClipsRequest
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "livestream.googleapis.com/Clip"
    }
  ];

  // Requested page size. Server may return fewer items than requested.
  // If unspecified, server will pick an appropriate default.
  int32 page_size = 2;

  // A token identifying a page of results the server should return.
  string page_token = 3;

  // Filtering results
  string filter = 4;

  // Hint for how to order the results
  string order_by = 5;
}

// Response message for "LivestreamService.ListClips".
message ListClipsResponse {
  // The list of Clip
  repeated Clip clips = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for "LivestreamService.GetClip".
message GetClipRequest {
  // Required. Name of the resource, in the following form:
  // `projects/{project}/locations/{location}/channels/{channel}/clips/{clip}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "livestream.googleapis.com/Clip" }
  ];
}

// Request message for "LivestreamService.CreateClip".
message CreateClipRequest {
  // Required. The parent resource name, in the following form:
  // `projects/{project}/locations/{location}/channels/{channel}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "livestream.googleapis.com/Clip"
    }
  ];

  // Required. Id of the requesting object in the following form:
  //
  // 1. 1 character minimum, 63 characters maximum
  // 2. Only contains letters, digits, underscores, and hyphens
  string clip_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  Clip clip = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for "LivestreamService.DeleteClip".
message DeleteClipRequest {
  // Required. The name of the clip resource, in the form of:
  // `projects/{project}/locations/{location}/channels/{channelId}/clips/{clipId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "livestream.googleapis.com/Clip" }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for "LivestreamService.GetPool".
message GetPoolRequest {
  // Required. The name of the pool resource, in the form of:
  // `projects/{project}/locations/{location}/pools/{poolId}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "livestream.googleapis.com/Pool" }
  ];
}

// Request message for "LivestreamService.UpdatePool".
message UpdatePoolRequest {
  // Field mask is used to specify the fields to be overwritten in the Pool
  // resource by the update. You can only update the following fields:
  //
  // * `networkConfig`
  //
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask.
  google.protobuf.FieldMask update_mask = 1;

  // Required. The pool resource to be updated.
  Pool pool = 2 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported `(00000000-0000-0000-0000-000000000000)`.
  string request_id = 3;
}
