{"methodConfig": [{"name": [{"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "ListChannels"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "GetChannel"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "ListInputs"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "GetInput"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "ListEvents"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "GetEvent"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "CreateChannel"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "DeleteChannel"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "UpdateChannel"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "StartChannel"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "StopChannel"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "CreateInput"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "DeleteInput"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "UpdateInput"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "CreateEvent"}, {"service": "google.cloud.video.livestream.v1.LivestreamService", "method": "DeleteEvent"}], "timeout": "60s"}]}