# This build file includes a target for the Ruby wrapper library for
# google-cloud-video-live_stream.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for livestream.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "livestream_ruby_wrapper",
    srcs = ["//google/cloud/video/livestream/v1:livestream_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-video-live_stream",
        "ruby-cloud-wrapper-of=v1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/livestream/",
        "ruby-cloud-api-id=livestream.googleapis.com",
        "ruby-cloud-api-shortname=livestream",
    ],
    ruby_cloud_description = "The Live Stream API transcodes mezzanine live signals into direct-to-consumer streaming formats, including Dynamic Adaptive Streaming over HTTP (DASH/MPEG-DASH), and HTTP Live Streaming (HLS), for multiple device platforms.",
    ruby_cloud_title = "Live Stream",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-video-livestream-ruby",
    deps = [
        ":livestream_ruby_wrapper",
    ],
)
