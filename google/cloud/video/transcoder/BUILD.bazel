# This build file includes a target for the Ruby wrapper library for
# google-cloud-video-transcoder.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for transcoder.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "transcoder_ruby_wrapper",
    srcs = ["//google/cloud/video/transcoder/v1:transcoder_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-video-transcoder",
        "ruby-cloud-env-prefix=TRANSCODER",
        "ruby-cloud-wrapper-of=v1:0.12",
        "ruby-cloud-product-url=https://cloud.google.com/transcoder/",
        "ruby-cloud-api-id=transcoder.googleapis.com",
        "ruby-cloud-api-shortname=transcoder",
    ],
    ruby_cloud_description = "The Transcoder API allows you to convert video files and package them for optimized delivery to web, mobile and connected TVs.",
    ruby_cloud_title = "Transcoder",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-video-transcoder-ruby",
    deps = [
        ":transcoder_ruby_wrapper",
    ],
)
