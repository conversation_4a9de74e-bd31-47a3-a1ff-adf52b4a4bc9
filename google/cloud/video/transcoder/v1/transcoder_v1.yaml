type: google.api.Service
config_version: 3
name: transcoder.googleapis.com
title: Transcoder API

apis:
- name: google.cloud.video.transcoder.v1.TranscoderService

documentation:
  summary: |-
    This API converts video files into formats suitable for consumer
    distribution. For more information, see the <a
    href="https://cloud.google.com/transcoder/docs/concepts/overview">Transcoder
    API overview</a>.

backend:
  rules:
  - selector: 'google.cloud.video.transcoder.v1.TranscoderService.*'
    deadline: 30.0

authentication:
  rules:
  - selector: 'google.cloud.video.transcoder.v1.TranscoderService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
