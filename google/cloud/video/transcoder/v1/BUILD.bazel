# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "transcoder_proto",
    srcs = [
        "resources.proto",
        "services.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "transcoder_proto_with_info",
    deps = [
        ":transcoder_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "transcoder_java_proto",
    deps = [":transcoder_proto"],
)

java_grpc_library(
    name = "transcoder_java_grpc",
    srcs = [":transcoder_proto"],
    deps = [":transcoder_java_proto"],
)

java_gapic_library(
    name = "transcoder_java_gapic",
    srcs = [":transcoder_proto_with_info"],
    grpc_service_config = "transcoder_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "transcoder_v1.yaml",
    test_deps = [
        ":transcoder_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":transcoder_java_proto",
    ],
)

java_gapic_test(
    name = "transcoder_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.video.transcoder.v1.TranscoderServiceClientHttpJsonTest",
        "com.google.cloud.video.transcoder.v1.TranscoderServiceClientTest",
    ],
    runtime_deps = [":transcoder_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-video-transcoder-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":transcoder_java_gapic",
        ":transcoder_java_grpc",
        ":transcoder_java_proto",
        ":transcoder_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "transcoder_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/video/transcoder/apiv1/transcoderpb",
    protos = [":transcoder_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "transcoder_go_gapic",
    srcs = [":transcoder_proto_with_info"],
    grpc_service_config = "transcoder_grpc_service_config.json",
    importpath = "cloud.google.com/go/video/transcoder/apiv1;transcoder",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "transcoder_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":transcoder_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-video-transcoder-v1-go",
    deps = [
        ":transcoder_go_gapic",
        ":transcoder_go_gapic_srcjar-metadata.srcjar",
        ":transcoder_go_gapic_srcjar-snippets.srcjar",
        ":transcoder_go_gapic_srcjar-test.srcjar",
        ":transcoder_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "transcoder_py_gapic",
    srcs = [":transcoder_proto"],
    grpc_service_config = "transcoder_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "transcoder_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "transcoder_py_gapic_test",
    srcs = [
        "transcoder_py_gapic_pytest.py",
        "transcoder_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":transcoder_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "video-transcoder-v1-py",
    deps = [
        ":transcoder_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "transcoder_php_proto",
    deps = [":transcoder_proto"],
)

php_gapic_library(
    name = "transcoder_php_gapic",
    srcs = [":transcoder_proto_with_info"],
    grpc_service_config = "transcoder_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "transcoder_v1.yaml",
    transport = "grpc+rest",
    deps = [":transcoder_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-video-transcoder-v1-php",
    deps = [
        ":transcoder_php_gapic",
        ":transcoder_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "transcoder_nodejs_gapic",
    package_name = "@google-cloud/video-transcoder",
    src = ":transcoder_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "transcoder_grpc_service_config.json",
    package = "google.cloud.video.transcoder.v1",
    rest_numeric_enums = True,
    service_yaml = "transcoder_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "video-transcoder-v1-nodejs",
    deps = [
        ":transcoder_nodejs_gapic",
        ":transcoder_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "transcoder_ruby_proto",
    deps = [":transcoder_proto"],
)

ruby_grpc_library(
    name = "transcoder_ruby_grpc",
    srcs = [":transcoder_proto"],
    deps = [":transcoder_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "transcoder_ruby_gapic",
    srcs = [":transcoder_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=transcoder.googleapis.com",
        "ruby-cloud-api-shortname=transcoder",
        "ruby-cloud-env-prefix=TRANSCODER",
        "ruby-cloud-gem-name=google-cloud-video-transcoder-v1",
        "ruby-cloud-product-url=https://cloud.google.com/transcoder/",
    ],
    grpc_service_config = "transcoder_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Transcoder API allows you to convert video files and package them for optimized delivery to web, mobile and connected TVs.",
    ruby_cloud_title = "Transcoder V1",
    service_yaml = "transcoder_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":transcoder_ruby_grpc",
        ":transcoder_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-video-transcoder-v1-ruby",
    deps = [
        ":transcoder_ruby_gapic",
        ":transcoder_ruby_grpc",
        ":transcoder_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "transcoder_csharp_proto",
    deps = [":transcoder_proto"],
)

csharp_grpc_library(
    name = "transcoder_csharp_grpc",
    srcs = [":transcoder_proto"],
    deps = [":transcoder_csharp_proto"],
)

csharp_gapic_library(
    name = "transcoder_csharp_gapic",
    srcs = [":transcoder_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "transcoder_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "transcoder_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":transcoder_csharp_grpc",
        ":transcoder_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-video-transcoder-v1-csharp",
    deps = [
        ":transcoder_csharp_gapic",
        ":transcoder_csharp_grpc",
        ":transcoder_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "transcoder_cc_proto",
    deps = [":transcoder_proto"],
)

cc_grpc_library(
    name = "transcoder_cc_grpc",
    srcs = [":transcoder_proto"],
    grpc_only = True,
    deps = [":transcoder_cc_proto"],
)
