{"methodConfig": [{"name": [{"service": "google.cloud.video.transcoder.v1.TranscoderService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.video.transcoder.v1.TranscoderService", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.video.transcoder.v1.TranscoderService", "method": "ListJobs"}, {"service": "google.cloud.video.transcoder.v1.TranscoderService", "method": "Get<PERSON>ob"}, {"service": "google.cloud.video.transcoder.v1.TranscoderService", "method": "DeleteJob"}, {"service": "google.cloud.video.transcoder.v1.TranscoderService", "method": "CreateJobTemplate"}, {"service": "google.cloud.video.transcoder.v1.TranscoderService", "method": "ListJobTemplates"}, {"service": "google.cloud.video.transcoder.v1.TranscoderService", "method": "GetJobTemplate"}, {"service": "google.cloud.video.transcoder.v1.TranscoderService", "method": "DeleteJobTemplate"}], "timeout": "60s"}]}