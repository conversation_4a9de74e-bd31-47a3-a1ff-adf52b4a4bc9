{"methodConfig": [{"name": [{"service": "google.cloud.discoveryengine.v1.CompletionService"}, {"service": "google.cloud.discoveryengine.v1.GroundedGenerationService"}, {"service": "google.cloud.discoveryengine.v1.RecommendationService"}], "timeout": "5s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "5s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.discoveryengine.v1.ControlService"}, {"service": "google.cloud.discoveryengine.v1.ConversationalSearchService"}, {"service": "google.cloud.discoveryengine.v1.DocumentService"}, {"service": "google.cloud.discoveryengine.v1.ProjectService"}, {"service": "google.cloud.discoveryengine.v1.RankService"}, {"service": "google.cloud.discoveryengine.v1.SchemaService"}, {"service": "google.cloud.discoveryengine.v1.SearchService"}, {"service": "google.cloud.discoveryengine.v1.UserEventService"}, {"service": "google.longrunning.Operations"}], "timeout": "30s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.discoveryengine.v1.DocumentService", "method": "ImportDocuments"}, {"service": "google.cloud.discoveryengine.v1.UserEventService", "method": "ImportUserEvents"}, {"service": "google.longrunning.Operations", "method": "ListOperations"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}