// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1";
option go_package = "cloud.google.com/go/discoveryengine/apiv1/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "CustomTuningModelProto";
option java_package = "com.google.cloud.discoveryengine.v1";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1";

// Metadata that describes a custom tuned model.
message CustomTuningModel {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/CustomTuningModel"
    pattern: "projects/{project}/locations/{location}/dataStores/{data_store}/customTuningModels/{custom_tuning_model}"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/customTuningModels/{custom_tuning_model}"
  };

  // The state of the model.
  enum ModelState {
    // Default value.
    MODEL_STATE_UNSPECIFIED = 0;

    // The model is in a paused training state.
    TRAINING_PAUSED = 1;

    // The model is currently training.
    TRAINING = 2;

    // The model has successfully completed training.
    TRAINING_COMPLETE = 3;

    // The model is ready for serving.
    READY_FOR_SERVING = 4;

    // The model training failed.
    TRAINING_FAILED = 5;

    // The model training finished successfully but metrics did not improve.
    NO_IMPROVEMENT = 6;

    // Input data validation failed. Model training didn't start.
    INPUT_VALIDATION_FAILED = 7;
  }

  // Required. The fully qualified resource name of the model.
  //
  // Format:
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/customTuningModels/{custom_tuning_model}`.
  //
  // Model must be an alpha-numerical string with limit of 40 characters.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/CustomTuningModel"
    }
  ];

  // The display name of the model.
  string display_name = 2;

  // The version of the model.
  int64 model_version = 3;

  // The state that the model is in (e.g.`TRAINING` or `TRAINING_FAILED`).
  ModelState model_state = 4;

  // Deprecated: Timestamp the Model was created at.
  google.protobuf.Timestamp create_time = 5 [deprecated = true];

  // Timestamp the model training was initiated.
  google.protobuf.Timestamp training_start_time = 6;

  // The metrics of the trained model.
  map<string, double> metrics = 7;

  // Currently this is only populated if the model state is
  // `INPUT_VALIDATION_FAILED`.
  string error_message = 8;
}
