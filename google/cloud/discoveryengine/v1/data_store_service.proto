// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1/data_store.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1";
option go_package = "cloud.google.com/go/discoveryengine/apiv1/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "DataStoreServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1";

// Service for managing [DataStore][google.cloud.discoveryengine.v1.DataStore]
// configuration.
service DataStoreService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a [DataStore][google.cloud.discoveryengine.v1.DataStore].
  //
  // DataStore is for storing
  // [Documents][google.cloud.discoveryengine.v1.Document]. To serve these
  // documents for Search, or Recommendation use case, an
  // [Engine][google.cloud.discoveryengine.v1.Engine] needs to be created
  // separately.
  rpc CreateDataStore(CreateDataStoreRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/dataStores"
      body: "data_store"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/collections/*}/dataStores"
        body: "data_store"
      }
    };
    option (google.api.method_signature) = "parent,data_store,data_store_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1.DataStore"
      metadata_type: "google.cloud.discoveryengine.v1.CreateDataStoreMetadata"
    };
  }

  // Gets a [DataStore][google.cloud.discoveryengine.v1.DataStore].
  rpc GetDataStore(GetDataStoreRequest) returns (DataStore) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/dataStores/*}"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/collections/*/dataStores/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all the [DataStore][google.cloud.discoveryengine.v1.DataStore]s
  // associated with the project.
  rpc ListDataStores(ListDataStoresRequest) returns (ListDataStoresResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/dataStores"
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/collections/*}/dataStores"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a [DataStore][google.cloud.discoveryengine.v1.DataStore].
  rpc DeleteDataStore(DeleteDataStoreRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/dataStores/*}"
      additional_bindings {
        delete: "/v1/{name=projects/*/locations/*/collections/*/dataStores/*}"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.discoveryengine.v1.DeleteDataStoreMetadata"
    };
  }

  // Updates a [DataStore][google.cloud.discoveryengine.v1.DataStore]
  rpc UpdateDataStore(UpdateDataStoreRequest) returns (DataStore) {
    option (google.api.http) = {
      patch: "/v1/{data_store.name=projects/*/locations/*/dataStores/*}"
      body: "data_store"
      additional_bindings {
        patch: "/v1/{data_store.name=projects/*/locations/*/collections/*/dataStores/*}"
        body: "data_store"
      }
    };
    option (google.api.method_signature) = "data_store,update_mask";
  }
}

// Request for
// [DataStoreService.CreateDataStore][google.cloud.discoveryengine.v1.DataStoreService.CreateDataStore]
// method.
message CreateDataStoreRequest {
  // Required. The parent resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Collection"
    }
  ];

  // Required. The [DataStore][google.cloud.discoveryengine.v1.DataStore] to
  // create.
  DataStore data_store = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the
  // [DataStore][google.cloud.discoveryengine.v1.DataStore], which will become
  // the final component of the
  // [DataStore][google.cloud.discoveryengine.v1.DataStore]'s resource name.
  //
  // This field must conform to [RFC-1034](https://tools.ietf.org/html/rfc1034)
  // standard with a length limit of 63 characters. Otherwise, an
  // INVALID_ARGUMENT error is returned.
  string data_store_id = 3 [(google.api.field_behavior) = REQUIRED];

  // A boolean flag indicating whether user want to directly create an advanced
  // data store for site search.
  // If the data store is not configured as site
  // search (GENERIC vertical and PUBLIC_WEBSITE content_config), this flag will
  // be ignored.
  bool create_advanced_site_search = 4;

  // A boolean flag indicating whether to skip the default schema creation for
  // the data store. Only enable this flag if you are certain that the default
  // schema is incompatible with your use case.
  //
  // If set to true, you must manually create a schema for the data store before
  // any documents can be ingested.
  //
  // This flag cannot be specified if `data_store.starting_schema` is specified.
  bool skip_default_schema_creation = 7;
}

// Request message for
// [DataStoreService.GetDataStore][google.cloud.discoveryengine.v1.DataStoreService.GetDataStore]
// method.
message GetDataStoreRequest {
  // Required. Full resource name of
  // [DataStore][google.cloud.discoveryengine.v1.DataStore], such as
  // `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`.
  //
  // If the caller does not have permission to access the
  // [DataStore][google.cloud.discoveryengine.v1.DataStore], regardless of
  // whether or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the requested [DataStore][google.cloud.discoveryengine.v1.DataStore]
  // does not exist, a NOT_FOUND error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];
}

// Metadata related to the progress of the
// [DataStoreService.CreateDataStore][google.cloud.discoveryengine.v1.DataStoreService.CreateDataStore]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message CreateDataStoreMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Request message for
// [DataStoreService.ListDataStores][google.cloud.discoveryengine.v1.DataStoreService.ListDataStores]
// method.
message ListDataStoresRequest {
  // Required. The parent branch resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection_id}`.
  //
  // If the caller does not have permission to list
  // [DataStore][google.cloud.discoveryengine.v1.DataStore]s under this
  // location, regardless of whether or not this data store exists, a
  // PERMISSION_DENIED error is returned.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Collection"
    }
  ];

  // Maximum number of [DataStore][google.cloud.discoveryengine.v1.DataStore]s
  // to return. If unspecified, defaults to 10. The maximum allowed value is 50.
  // Values above 50 will be coerced to 50.
  //
  // If this field is negative, an INVALID_ARGUMENT is returned.
  int32 page_size = 2;

  // A page token
  // [ListDataStoresResponse.next_page_token][google.cloud.discoveryengine.v1.ListDataStoresResponse.next_page_token],
  // received from a previous
  // [DataStoreService.ListDataStores][google.cloud.discoveryengine.v1.DataStoreService.ListDataStores]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [DataStoreService.ListDataStores][google.cloud.discoveryengine.v1.DataStoreService.ListDataStores]
  // must match the call that provided the page token. Otherwise, an
  // INVALID_ARGUMENT error is returned.
  string page_token = 3;

  // Filter by solution type .
  // For example: `filter = 'solution_type:SOLUTION_TYPE_SEARCH'`
  string filter = 4;
}

// Response message for
// [DataStoreService.ListDataStores][google.cloud.discoveryengine.v1.DataStoreService.ListDataStores]
// method.
message ListDataStoresResponse {
  // All the customer's [DataStore][google.cloud.discoveryengine.v1.DataStore]s.
  repeated DataStore data_stores = 1;

  // A token that can be sent as
  // [ListDataStoresRequest.page_token][google.cloud.discoveryengine.v1.ListDataStoresRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [DataStoreService.DeleteDataStore][google.cloud.discoveryengine.v1.DataStoreService.DeleteDataStore]
// method.
message DeleteDataStoreRequest {
  // Required. Full resource name of
  // [DataStore][google.cloud.discoveryengine.v1.DataStore], such as
  // `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`.
  //
  // If the caller does not have permission to delete the
  // [DataStore][google.cloud.discoveryengine.v1.DataStore], regardless of
  // whether or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the [DataStore][google.cloud.discoveryengine.v1.DataStore] to delete
  // does not exist, a NOT_FOUND error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];
}

// Request message for
// [DataStoreService.UpdateDataStore][google.cloud.discoveryengine.v1.DataStoreService.UpdateDataStore]
// method.
message UpdateDataStoreRequest {
  // Required. The [DataStore][google.cloud.discoveryengine.v1.DataStore] to
  // update.
  //
  // If the caller does not have permission to update the
  // [DataStore][google.cloud.discoveryengine.v1.DataStore], regardless of
  // whether or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the [DataStore][google.cloud.discoveryengine.v1.DataStore] to update
  // does not exist, a NOT_FOUND error is returned.
  DataStore data_store = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates which fields in the provided
  // [DataStore][google.cloud.discoveryengine.v1.DataStore] to update.
  //
  // If an unsupported or unknown field is provided, an INVALID_ARGUMENT error
  // is returned.
  google.protobuf.FieldMask update_mask = 2;
}

// Metadata related to the progress of the
// [DataStoreService.DeleteDataStore][google.cloud.discoveryengine.v1.DataStoreService.DeleteDataStore]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message DeleteDataStoreMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}
