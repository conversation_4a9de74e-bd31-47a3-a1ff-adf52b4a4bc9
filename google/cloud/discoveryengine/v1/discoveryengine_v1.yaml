type: google.api.Service
config_version: 3
name: discoveryengine.googleapis.com
title: Discovery Engine API

apis:
- name: google.cloud.discoveryengine.v1.CompletionService
- name: google.cloud.discoveryengine.v1.ControlService
- name: google.cloud.discoveryengine.v1.ConversationalSearchService
- name: google.cloud.discoveryengine.v1.DataStoreService
- name: google.cloud.discoveryengine.v1.DocumentService
- name: google.cloud.discoveryengine.v1.EngineService
- name: google.cloud.discoveryengine.v1.GroundedGenerationService
- name: google.cloud.discoveryengine.v1.ProjectService
- name: google.cloud.discoveryengine.v1.RankService
- name: google.cloud.discoveryengine.v1.RecommendationService
- name: google.cloud.discoveryengine.v1.SchemaService
- name: google.cloud.discoveryengine.v1.SearchService
- name: google.cloud.discoveryengine.v1.SearchTuningService
- name: google.cloud.discoveryengine.v1.SiteSearchEngineService
- name: google.cloud.discoveryengine.v1.UserEventService
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.discoveryengine.logging.ErrorLog
- name: google.cloud.discoveryengine.v1.BatchCreateTargetSiteMetadata
- name: google.cloud.discoveryengine.v1.BatchCreateTargetSitesResponse
- name: google.cloud.discoveryengine.v1.Control
- name: google.cloud.discoveryengine.v1.CreateDataStoreMetadata
- name: google.cloud.discoveryengine.v1.CreateEngineMetadata
- name: google.cloud.discoveryengine.v1.CreateSchemaMetadata
- name: google.cloud.discoveryengine.v1.CreateTargetSiteMetadata
- name: google.cloud.discoveryengine.v1.DataStore
- name: google.cloud.discoveryengine.v1.DeleteDataStoreMetadata
- name: google.cloud.discoveryengine.v1.DeleteEngineMetadata
- name: google.cloud.discoveryengine.v1.DeleteSchemaMetadata
- name: google.cloud.discoveryengine.v1.DeleteTargetSiteMetadata
- name: google.cloud.discoveryengine.v1.DisableAdvancedSiteSearchMetadata
- name: google.cloud.discoveryengine.v1.DisableAdvancedSiteSearchResponse
- name: google.cloud.discoveryengine.v1.EnableAdvancedSiteSearchMetadata
- name: google.cloud.discoveryengine.v1.EnableAdvancedSiteSearchResponse
- name: google.cloud.discoveryengine.v1.Engine
- name: google.cloud.discoveryengine.v1.ImportCompletionSuggestionsMetadata
- name: google.cloud.discoveryengine.v1.ImportCompletionSuggestionsResponse
- name: google.cloud.discoveryengine.v1.ImportDocumentsMetadata
- name: google.cloud.discoveryengine.v1.ImportDocumentsResponse
- name: google.cloud.discoveryengine.v1.ImportSuggestionDenyListEntriesMetadata
- name: google.cloud.discoveryengine.v1.ImportSuggestionDenyListEntriesResponse
- name: google.cloud.discoveryengine.v1.ImportUserEventsMetadata
- name: google.cloud.discoveryengine.v1.ImportUserEventsResponse
- name: google.cloud.discoveryengine.v1.Project
- name: google.cloud.discoveryengine.v1.ProvisionProjectMetadata
- name: google.cloud.discoveryengine.v1.PurgeCompletionSuggestionsMetadata
- name: google.cloud.discoveryengine.v1.PurgeCompletionSuggestionsResponse
- name: google.cloud.discoveryengine.v1.PurgeDocumentsMetadata
- name: google.cloud.discoveryengine.v1.PurgeDocumentsResponse
- name: google.cloud.discoveryengine.v1.PurgeSuggestionDenyListEntriesMetadata
- name: google.cloud.discoveryengine.v1.PurgeSuggestionDenyListEntriesResponse
- name: google.cloud.discoveryengine.v1.Schema
- name: google.cloud.discoveryengine.v1.TargetSite
- name: google.cloud.discoveryengine.v1.TrainCustomModelMetadata
- name: google.cloud.discoveryengine.v1.TrainCustomModelResponse
- name: google.cloud.discoveryengine.v1.UpdateSchemaMetadata
- name: google.cloud.discoveryengine.v1.UpdateTargetSiteMetadata

documentation:
  summary: Discovery Engine API.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/operations/*}:cancel'
    body: '*'
    additional_bindings:
    - post: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/branches/*/operations/*}:cancel'
      body: '*'
    - post: '/v1/{name=projects/*/locations/*/dataStores/*/branches/*/operations/*}:cancel'
      body: '*'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/operations/*}'
    additional_bindings:
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataConnector/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/branches/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/models/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/schemas/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/targetSites/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/collections/*/engines/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/collections/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/dataStores/*/branches/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/dataStores/*/models/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/dataStores/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/identityMappingStores/*/operations/*}'
    - get: '/v1/{name=projects/*/locations/*/operations/*}'
    - get: '/v1/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*}/operations'
    additional_bindings:
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataConnector}/operations'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/branches/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/models/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/schemas/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/targetSites}/operations'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}/operations'
    - get: '/v1/{name=projects/*/locations/*/collections/*/dataStores/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/collections/*/engines/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/collections/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/dataStores/*/branches/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/dataStores/*/models/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/dataStores/*}/operations'
    - get: '/v1/{name=projects/*/locations/*/identityMappingStores/*}/operations'
    - get: '/v1/{name=projects/*/locations/*}/operations'
    - get: '/v1/{name=projects/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.discoveryengine.v1.CompletionService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1.ControlService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1.ConversationalSearchService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1.DataStoreService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1.DocumentService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1.EngineService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1.GroundedGenerationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1.ProjectService.ProvisionProject
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1.RankService.Rank
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1.RecommendationService.Recommend
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1.SchemaService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1.SearchService.Search
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1.SearchService.SearchLite
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1.SearchTuningService.ListCustomModels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1.SearchTuningService.TrainCustomModel
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1.SiteSearchEngineService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1.UserEventService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=911831&template=1480251
  documentation_uri: https://cloud.google.com/generative-ai-app-builder/docs
  api_short_name: discoveryengine
  github_label: 'api: discoveryengine'
  doc_tag_prefix: discoveryengine
  organization: CLOUD
  library_settings:
  - version: google.cloud.discoveryengine.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/generative-ai-app-builder/docs/reference/rpc
