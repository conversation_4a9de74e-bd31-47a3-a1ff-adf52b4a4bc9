// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1/import_config.proto";
import "google/cloud/discoveryengine/v1/purge_config.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1";
option go_package = "cloud.google.com/go/discoveryengine/apiv1/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "CompletionServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1";

// Service for Auto-Completion.
service CompletionService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Completes the specified user input with keyword suggestions.
  rpc CompleteQuery(CompleteQueryRequest) returns (CompleteQueryResponse) {
    option (google.api.http) = {
      get: "/v1/{data_store=projects/*/locations/*/dataStores/*}:completeQuery"
      additional_bindings {
        get: "/v1/{data_store=projects/*/locations/*/collections/*/dataStores/*}:completeQuery"
      }
    };
  }

  // Imports all
  // [SuggestionDenyListEntry][google.cloud.discoveryengine.v1.SuggestionDenyListEntry]
  // for a DataStore.
  rpc ImportSuggestionDenyListEntries(ImportSuggestionDenyListEntriesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/collections/*/dataStores/*}/suggestionDenyListEntries:import"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/dataStores/*}/suggestionDenyListEntries:import"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1.ImportSuggestionDenyListEntriesResponse"
      metadata_type: "google.cloud.discoveryengine.v1.ImportSuggestionDenyListEntriesMetadata"
    };
  }

  // Permanently deletes all
  // [SuggestionDenyListEntry][google.cloud.discoveryengine.v1.SuggestionDenyListEntry]
  // for a DataStore.
  rpc PurgeSuggestionDenyListEntries(PurgeSuggestionDenyListEntriesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/collections/*/dataStores/*}/suggestionDenyListEntries:purge"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/dataStores/**}/suggestionDenyListEntries:purge"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1.PurgeSuggestionDenyListEntriesResponse"
      metadata_type: "google.cloud.discoveryengine.v1.PurgeSuggestionDenyListEntriesMetadata"
    };
  }

  // Imports
  // [CompletionSuggestion][google.cloud.discoveryengine.v1.CompletionSuggestion]s
  // for a DataStore.
  rpc ImportCompletionSuggestions(ImportCompletionSuggestionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/collections/*/dataStores/*}/completionSuggestions:import"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/dataStores/*}/completionSuggestions:import"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1.ImportCompletionSuggestionsResponse"
      metadata_type: "google.cloud.discoveryengine.v1.ImportCompletionSuggestionsMetadata"
    };
  }

  // Permanently deletes all
  // [CompletionSuggestion][google.cloud.discoveryengine.v1.CompletionSuggestion]s
  // for a DataStore.
  rpc PurgeCompletionSuggestions(PurgeCompletionSuggestionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/collections/*/dataStores/*}/completionSuggestions:purge"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/dataStores/*}/completionSuggestions:purge"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1.PurgeCompletionSuggestionsResponse"
      metadata_type: "google.cloud.discoveryengine.v1.PurgeCompletionSuggestionsMetadata"
    };
  }
}

// Request message for
// [CompletionService.CompleteQuery][google.cloud.discoveryengine.v1.CompletionService.CompleteQuery]
// method.
message CompleteQueryRequest {
  // Required. The parent data store resource name for which the completion is
  // performed, such as
  // `projects/*/locations/global/collections/default_collection/dataStores/default_data_store`.
  string data_store = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];

  // Required. The typeahead input used to fetch suggestions. Maximum length is
  // 128 characters.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Specifies the autocomplete data model. This overrides any model specified
  // in the Configuration > Autocomplete section of the Cloud console. Currently
  // supported values:
  //
  // * `document` - Using suggestions generated from user-imported documents.
  // * `search-history` - Using suggestions generated from the past history of
  // [SearchService.Search][google.cloud.discoveryengine.v1.SearchService.Search]
  // API calls. Do not use it when there is no traffic for Search API.
  // * `user-event` - Using suggestions generated from user-imported search
  // events.
  // * `document-completable` - Using suggestions taken directly from
  // user-imported document fields marked as completable.
  //
  // Default values:
  //
  // * `document` is the default model for regular dataStores.
  // * `search-history` is the default model for site search dataStores.
  string query_model = 3;

  // A unique identifier for tracking visitors. For example, this could be
  // implemented with an HTTP cookie, which should be able to uniquely identify
  // a visitor on a single device. This unique identifier should not change if
  // the visitor logs in or out of the website.
  //
  // This field should NOT have a fixed value such as `unknown_visitor`.
  //
  // This should be the same identifier as
  // [UserEvent.user_pseudo_id][google.cloud.discoveryengine.v1.UserEvent.user_pseudo_id]
  // and
  // [SearchRequest.user_pseudo_id][google.cloud.discoveryengine.v1.SearchRequest.user_pseudo_id].
  //
  // The field must be a UTF-8 encoded string with a length limit of 128
  // characters. Otherwise, an `INVALID_ARGUMENT` error is returned.
  string user_pseudo_id = 4;

  // Indicates if tail suggestions should be returned if there are no
  // suggestions that match the full query. Even if set to true, if there are
  // suggestions that match the full query, those are returned and no
  // tail suggestions are returned.
  bool include_tail_suggestions = 5;
}

// Response message for
// [CompletionService.CompleteQuery][google.cloud.discoveryengine.v1.CompletionService.CompleteQuery]
// method.
message CompleteQueryResponse {
  // Suggestions as search queries.
  message QuerySuggestion {
    // The suggestion for the query.
    string suggestion = 1;

    // The unique document field paths that serve as the source of this
    // suggestion if it was generated from completable fields.
    //
    // This field is only populated for the document-completable model.
    repeated string completable_field_paths = 2;
  }

  // Results of the matched query suggestions. The result list is ordered and
  // the first result is a top suggestion.
  repeated QuerySuggestion query_suggestions = 1;

  // True if the returned suggestions are all tail suggestions.
  //
  // For tail matching to be triggered, include_tail_suggestions in the request
  // must be true and there must be no suggestions that match the full query.
  bool tail_match_triggered = 2;
}
