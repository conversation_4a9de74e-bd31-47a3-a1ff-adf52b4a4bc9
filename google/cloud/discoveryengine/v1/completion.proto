// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1";
option go_package = "cloud.google.com/go/discoveryengine/apiv1/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "CompletionProto";
option java_package = "com.google.cloud.discoveryengine.v1";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1";

// Suggestion deny list entry identifying the phrase to block from suggestions
// and the applied operation for the phrase.
message SuggestionDenyListEntry {
  // Operator for matching with the generated suggestions.
  enum MatchOperator {
    // Default value. Should not be used
    MATCH_OPERATOR_UNSPECIFIED = 0;

    // If the suggestion is an exact match to the block_phrase, then block it.
    EXACT_MATCH = 1;

    // If the suggestion contains the block_phrase, then block it.
    CONTAINS = 2;
  }

  // Required. Phrase to block from suggestions served. Can be maximum 125
  // characters.
  string block_phrase = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The match operator to apply for this phrase. Whether to block the
  // exact phrase, or block any suggestions containing this phrase.
  MatchOperator match_operator = 2 [(google.api.field_behavior) = REQUIRED];
}

// Autocomplete suggestions that are imported from Customer.
message CompletionSuggestion {
  // Ranking metrics of this suggestion.
  oneof ranking_info {
    // Global score of this suggestion. Control how this suggestion would be
    // scored / ranked.
    double global_score = 2;

    // Frequency of this suggestion. Will be used to rank suggestions when score
    // is not available.
    int64 frequency = 3;
  }

  // Required. The suggestion text.
  string suggestion = 1 [(google.api.field_behavior) = REQUIRED];

  // BCP-47 language code of this suggestion.
  string language_code = 4;

  // If two suggestions have the same groupId, they will not be
  // returned together. Instead the one ranked higher will be returned. This can
  // be used to deduplicate semantically identical suggestions.
  string group_id = 5;

  // The score of this suggestion within its group.
  double group_score = 6;

  // Alternative matching phrases for this suggestion.
  repeated string alternative_phrases = 7;
}
