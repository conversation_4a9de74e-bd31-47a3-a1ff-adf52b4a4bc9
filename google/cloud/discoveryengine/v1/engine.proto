// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1/common.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1";
option go_package = "cloud.google.com/go/discoveryengine/apiv1/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "EngineProto";
option java_package = "com.google.cloud.discoveryengine.v1";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1";

// Metadata that describes the training and serving parameters of an
// [Engine][google.cloud.discoveryengine.v1.Engine].
message Engine {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/Engine"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/engines/{engine}"
  };

  // Configurations for a Search Engine.
  message SearchEngineConfig {
    // The search feature tier of this engine.
    //
    // Different tiers might have different
    // pricing. To learn more, check the pricing documentation.
    //
    // Defaults to
    // [SearchTier.SEARCH_TIER_STANDARD][google.cloud.discoveryengine.v1.SearchTier.SEARCH_TIER_STANDARD]
    // if not specified.
    SearchTier search_tier = 1;

    // The add-on that this search engine enables.
    repeated SearchAddOn search_add_ons = 2;
  }

  // Configurations for a Chat Engine.
  message ChatEngineConfig {
    // Configurations for generating a Dialogflow agent.
    //
    // Note that these configurations are one-time consumed by
    // and passed to Dialogflow service. It means they cannot be retrieved using
    // [EngineService.GetEngine][google.cloud.discoveryengine.v1.EngineService.GetEngine]
    // or
    // [EngineService.ListEngines][google.cloud.discoveryengine.v1.EngineService.ListEngines]
    // API after engine creation.
    message AgentCreationConfig {
      // Name of the company, organization or other entity that the agent
      // represents. Used for knowledge connector LLM prompt and for knowledge
      // search.
      string business = 1;

      // Required. The default language of the agent as a language tag.
      // See [Language
      // Support](https://cloud.google.com/dialogflow/docs/reference/language)
      // for a list of the currently supported language codes.
      string default_language_code = 2;

      // Required. The time zone of the agent from the [time zone
      // database](https://www.iana.org/time-zones), e.g., America/New_York,
      // Europe/Paris.
      string time_zone = 3 [(google.api.field_behavior) = REQUIRED];

      // Agent location for Agent creation, supported values: global/us/eu.
      // If not provided, us Engine will create Agent using us-central-1 by
      // default; eu Engine will create Agent using eu-west-1 by default.
      string location = 4;
    }

    // The configurationt generate the Dialogflow agent that is associated to
    // this Engine.
    //
    // Note that these configurations are one-time consumed by
    // and passed to Dialogflow service. It means they cannot be retrieved using
    // [EngineService.GetEngine][google.cloud.discoveryengine.v1.EngineService.GetEngine]
    // or
    // [EngineService.ListEngines][google.cloud.discoveryengine.v1.EngineService.ListEngines]
    // API after engine creation.
    AgentCreationConfig agent_creation_config = 1;

    // The resource name of an exist Dialogflow agent to link to this Chat
    // Engine. Customers can either provide `agent_creation_config` to create
    // agent or provide an agent name that links the agent with the Chat engine.
    //
    // Format: `projects/<Project ID>/locations/<Location ID>/agents/<Agent
    // ID>`.
    //
    // Note that the `dialogflow_agent_to_link` are one-time consumed by and
    // passed to Dialogflow service. It means they cannot be retrieved using
    // [EngineService.GetEngine][google.cloud.discoveryengine.v1.EngineService.GetEngine]
    // or
    // [EngineService.ListEngines][google.cloud.discoveryengine.v1.EngineService.ListEngines]
    // API after engine creation. Use
    // [ChatEngineMetadata.dialogflow_agent][google.cloud.discoveryengine.v1.Engine.ChatEngineMetadata.dialogflow_agent]
    // for actual agent association after Engine is created.
    string dialogflow_agent_to_link = 2;
  }

  // Common configurations for an Engine.
  message CommonConfig {
    // The name of the company, business or entity that is associated with the
    // engine. Setting this may help improve LLM related features.
    string company_name = 1;
  }

  // Additional information of a Chat Engine.
  // Fields in this message are output only.
  message ChatEngineMetadata {
    // The resource name of a Dialogflow agent, that this Chat Engine refers
    // to.
    //
    // Format: `projects/<Project ID>/locations/<Location ID>/agents/<Agent
    // ID>`.
    string dialogflow_agent = 1;
  }

  // Additional config specs that defines the behavior of the engine.
  oneof engine_config {
    // Configurations for the Chat Engine. Only applicable if
    // [solution_type][google.cloud.discoveryengine.v1.Engine.solution_type] is
    // [SOLUTION_TYPE_CHAT][google.cloud.discoveryengine.v1.SolutionType.SOLUTION_TYPE_CHAT].
    ChatEngineConfig chat_engine_config = 11;

    // Configurations for the Search Engine. Only applicable if
    // [solution_type][google.cloud.discoveryengine.v1.Engine.solution_type] is
    // [SOLUTION_TYPE_SEARCH][google.cloud.discoveryengine.v1.SolutionType.SOLUTION_TYPE_SEARCH].
    SearchEngineConfig search_engine_config = 13;
  }

  // Engine metadata to monitor the status of the engine.
  oneof engine_metadata {
    // Output only. Additional information of the Chat Engine. Only applicable
    // if [solution_type][google.cloud.discoveryengine.v1.Engine.solution_type]
    // is
    // [SOLUTION_TYPE_CHAT][google.cloud.discoveryengine.v1.SolutionType.SOLUTION_TYPE_CHAT].
    ChatEngineMetadata chat_engine_metadata = 12
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Immutable. The fully qualified resource name of the engine.
  //
  // This field must be a UTF-8 encoded string with a length limit of 1024
  // characters.
  //
  // Format:
  // `projects/{project}/locations/{location}/collections/{collection}/engines/{engine}`
  // engine should be 1-63 characters, and valid characters are
  // /[a-z0-9][a-z0-9-_]*/. Otherwise, an INVALID_ARGUMENT error is returned.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Required. The display name of the engine. Should be human readable. UTF-8
  // encoded string with limit of 1024 characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp the Recommendation Engine was created at.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp the Recommendation Engine was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The data stores associated with this engine.
  //
  // For
  // [SOLUTION_TYPE_SEARCH][google.cloud.discoveryengine.v1.SolutionType.SOLUTION_TYPE_SEARCH]
  // and
  // [SOLUTION_TYPE_RECOMMENDATION][google.cloud.discoveryengine.v1.SolutionType.SOLUTION_TYPE_RECOMMENDATION]
  // type of engines, they can only associate with at most one data store.
  //
  // If [solution_type][google.cloud.discoveryengine.v1.Engine.solution_type] is
  // [SOLUTION_TYPE_CHAT][google.cloud.discoveryengine.v1.SolutionType.SOLUTION_TYPE_CHAT],
  // multiple [DataStore][google.cloud.discoveryengine.v1.DataStore]s in the
  // same [Collection][google.cloud.discoveryengine.v1.Collection] can be
  // associated here.
  //
  // Note that when used in
  // [CreateEngineRequest][google.cloud.discoveryengine.v1.CreateEngineRequest],
  // one DataStore id must be provided as the system will use it for necessary
  // initializations.
  repeated string data_store_ids = 5;

  // Required. The solutions of the engine.
  SolutionType solution_type = 6 [(google.api.field_behavior) = REQUIRED];

  // The industry vertical that the engine registers.
  // The restriction of the Engine industry vertical is based on
  // [DataStore][google.cloud.discoveryengine.v1.DataStore]: If unspecified,
  // default to `GENERIC`. Vertical on Engine has to match vertical of the
  // DataStore linked to the engine.
  IndustryVertical industry_vertical = 16;

  // Common config spec that specifies the metadata of the engine.
  CommonConfig common_config = 15;

  // Optional. Whether to disable analytics for searches performed on this
  // engine.
  bool disable_analytics = 26 [(google.api.field_behavior) = OPTIONAL];
}
