// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1";
option go_package = "cloud.google.com/go/discoveryengine/apiv1/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "SiteSearchEngineProto";
option java_package = "com.google.cloud.discoveryengine.v1";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1";

// SiteSearchEngine captures DataStore level site search persisting
// configurations. It is a singleton value per data store.
message SiteSearchEngine {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/SiteSearchEngine"
    pattern: "projects/{project}/locations/{location}/dataStores/{data_store}/siteSearchEngine"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine"
  };

  // The fully qualified resource name of the site search engine.
  // Format: `projects/*/locations/*/dataStores/*/siteSearchEngine`
  string name = 1;
}

// A target site for the SiteSearchEngine.
message TargetSite {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/TargetSite"
    pattern: "projects/{project}/locations/{location}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}"
  };

  // Site search indexing failure reasons.
  message FailureReason {
    // Failed due to insufficient quota.
    message QuotaFailure {
      // This number is an estimation on how much total quota this project needs
      // to successfully complete indexing.
      int64 total_required_quota = 1;
    }

    // Failure reason.
    oneof failure {
      // Failed due to insufficient quota.
      QuotaFailure quota_failure = 1;
    }
  }

  // Possible target site types.
  enum Type {
    // This value is unused. In this case, server behavior defaults to
    // [Type.INCLUDE][google.cloud.discoveryengine.v1.TargetSite.Type.INCLUDE].
    TYPE_UNSPECIFIED = 0;

    // Include the target site.
    INCLUDE = 1;

    // Exclude the target site.
    EXCLUDE = 2;
  }

  // Target site indexing status enumeration.
  enum IndexingStatus {
    // Defaults to SUCCEEDED.
    INDEXING_STATUS_UNSPECIFIED = 0;

    // The target site is in the update queue and will be picked up by indexing
    // pipeline.
    PENDING = 1;

    // The target site fails to be indexed.
    FAILED = 2;

    // The target site has been indexed.
    SUCCEEDED = 3;

    // The previously indexed target site has been marked to be deleted. This is
    // a transitioning state which will resulted in either:
    // 1. target site deleted if unindexing is successful;
    // 2. state reverts to SUCCEEDED if the unindexing fails.
    DELETING = 4;
  }

  // Output only. The fully qualified resource name of the target site.
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}`
  // The `target_site_id` is system-generated.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Input only. The user provided URI pattern from which the
  // `generated_uri_pattern` is generated.
  string provided_uri_pattern = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // The type of the target site, e.g., whether the site is to be included or
  // excluded.
  Type type = 3;

  // Input only. If set to false, a uri_pattern is generated to include all
  // pages whose address contains the provided_uri_pattern. If set to true, an
  // uri_pattern is generated to try to be an exact match of the
  // provided_uri_pattern or just the specific page if the provided_uri_pattern
  // is a specific one. provided_uri_pattern is always normalized to
  // generate the URI pattern to be used by the search engine.
  bool exact_match = 6 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. This is system-generated based on the provided_uri_pattern.
  string generated_uri_pattern = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Root domain of the provided_uri_pattern.
  string root_domain_uri = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Site ownership and validity verification status.
  SiteVerificationInfo site_verification_info = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indexing status.
  IndexingStatus indexing_status = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The target site's last updated time.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Failure reason.
  FailureReason failure_reason = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Verification information for target sites in advanced site search.
message SiteVerificationInfo {
  // Site verification state.
  enum SiteVerificationState {
    // Defaults to VERIFIED.
    SITE_VERIFICATION_STATE_UNSPECIFIED = 0;

    // Site ownership verified.
    VERIFIED = 1;

    // Site ownership pending verification or verification failed.
    UNVERIFIED = 2;

    // Site exempt from verification, e.g., a public website that opens to all.
    EXEMPTED = 3;
  }

  // Site verification state indicating the ownership and validity.
  SiteVerificationState site_verification_state = 1;

  // Latest site verification time.
  google.protobuf.Timestamp verify_time = 2;
}
