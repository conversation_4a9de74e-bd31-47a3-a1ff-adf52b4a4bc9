// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1/document.proto";
import "google/cloud/discoveryengine/v1/import_config.proto";
import "google/cloud/discoveryengine/v1/purge_config.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1";
option go_package = "cloud.google.com/go/discoveryengine/apiv1/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "DocumentServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1";

// Service for ingesting [Document][google.cloud.discoveryengine.v1.Document]
// information of the customer's website.
service DocumentService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets a [Document][google.cloud.discoveryengine.v1.Document].
  rpc GetDocument(GetDocumentRequest) returns (Document) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/dataStores/*/branches/*/documents/*}"
      additional_bindings {
        get: "/v1/{name=projects/*/locations/*/collections/*/dataStores/*/branches/*/documents/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a list of [Document][google.cloud.discoveryengine.v1.Document]s.
  rpc ListDocuments(ListDocumentsRequest) returns (ListDocumentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/dataStores/*/branches/*}/documents"
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/collections/*/dataStores/*/branches/*}/documents"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a [Document][google.cloud.discoveryengine.v1.Document].
  rpc CreateDocument(CreateDocumentRequest) returns (Document) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/dataStores/*/branches/*}/documents"
      body: "document"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/collections/*/dataStores/*/branches/*}/documents"
        body: "document"
      }
    };
    option (google.api.method_signature) = "parent,document,document_id";
  }

  // Updates a [Document][google.cloud.discoveryengine.v1.Document].
  rpc UpdateDocument(UpdateDocumentRequest) returns (Document) {
    option (google.api.http) = {
      patch: "/v1/{document.name=projects/*/locations/*/dataStores/*/branches/*/documents/*}"
      body: "document"
      additional_bindings {
        patch: "/v1/{document.name=projects/*/locations/*/collections/*/dataStores/*/branches/*/documents/*}"
        body: "document"
      }
    };
    option (google.api.method_signature) = "document,update_mask";
  }

  // Deletes a [Document][google.cloud.discoveryengine.v1.Document].
  rpc DeleteDocument(DeleteDocumentRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/dataStores/*/branches/*/documents/*}"
      additional_bindings {
        delete: "/v1/{name=projects/*/locations/*/collections/*/dataStores/*/branches/*/documents/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Bulk import of multiple
  // [Document][google.cloud.discoveryengine.v1.Document]s. Request processing
  // may be synchronous. Non-existing items are created.
  //
  // Note: It is possible for a subset of the
  // [Document][google.cloud.discoveryengine.v1.Document]s to be successfully
  // updated.
  rpc ImportDocuments(ImportDocumentsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/dataStores/*/branches/*}/documents:import"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/collections/*/dataStores/*/branches/*}/documents:import"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1.ImportDocumentsResponse"
      metadata_type: "google.cloud.discoveryengine.v1.ImportDocumentsMetadata"
    };
  }

  // Permanently deletes all selected
  // [Document][google.cloud.discoveryengine.v1.Document]s in a branch.
  //
  // This process is asynchronous. Depending on the number of
  // [Document][google.cloud.discoveryengine.v1.Document]s to be deleted, this
  // operation can take hours to complete. Before the delete operation
  // completes, some [Document][google.cloud.discoveryengine.v1.Document]s might
  // still be returned by
  // [DocumentService.GetDocument][google.cloud.discoveryengine.v1.DocumentService.GetDocument]
  // or
  // [DocumentService.ListDocuments][google.cloud.discoveryengine.v1.DocumentService.ListDocuments].
  //
  // To get a list of the [Document][google.cloud.discoveryengine.v1.Document]s
  // to be deleted, set
  // [PurgeDocumentsRequest.force][google.cloud.discoveryengine.v1.PurgeDocumentsRequest.force]
  // to false.
  rpc PurgeDocuments(PurgeDocumentsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/dataStores/*/branches/*}/documents:purge"
      body: "*"
      additional_bindings {
        post: "/v1/{parent=projects/*/locations/*/collections/*/dataStores/*/branches/*}/documents:purge"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1.PurgeDocumentsResponse"
      metadata_type: "google.cloud.discoveryengine.v1.PurgeDocumentsMetadata"
    };
  }

  // Gets index freshness metadata for
  // [Document][google.cloud.discoveryengine.v1.Document]s. Supported for
  // website search only.
  rpc BatchGetDocumentsMetadata(BatchGetDocumentsMetadataRequest)
      returns (BatchGetDocumentsMetadataResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/dataStores/*/branches/*}/batchGetDocumentsMetadata"
      additional_bindings {
        get: "/v1/{parent=projects/*/locations/*/collections/*/dataStores/*/branches/*}/batchGetDocumentsMetadata"
      }
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for
// [DocumentService.GetDocument][google.cloud.discoveryengine.v1.DocumentService.GetDocument]
// method.
message GetDocumentRequest {
  // Required. Full resource name of
  // [Document][google.cloud.discoveryengine.v1.Document], such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document}`.
  //
  // If the caller does not have permission to access the
  // [Document][google.cloud.discoveryengine.v1.Document], regardless of whether
  // or not it exists, a `PERMISSION_DENIED` error is returned.
  //
  // If the requested [Document][google.cloud.discoveryengine.v1.Document] does
  // not exist, a `NOT_FOUND` error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Document"
    }
  ];
}

// Request message for
// [DocumentService.ListDocuments][google.cloud.discoveryengine.v1.DocumentService.ListDocuments]
// method.
message ListDocumentsRequest {
  // Required. The parent branch resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`.
  // Use `default_branch` as the branch ID, to list documents under the default
  // branch.
  //
  // If the caller does not have permission to list
  // [Document][google.cloud.discoveryengine.v1.Document]s under this branch,
  // regardless of whether or not this branch exists, a `PERMISSION_DENIED`
  // error is returned.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Branch"
    }
  ];

  // Maximum number of [Document][google.cloud.discoveryengine.v1.Document]s to
  // return. If unspecified, defaults to 100. The maximum allowed value is 1000.
  // Values above 1000 are set to 1000.
  //
  // If this field is negative, an `INVALID_ARGUMENT` error is returned.
  int32 page_size = 2;

  // A page token
  // [ListDocumentsResponse.next_page_token][google.cloud.discoveryengine.v1.ListDocumentsResponse.next_page_token],
  // received from a previous
  // [DocumentService.ListDocuments][google.cloud.discoveryengine.v1.DocumentService.ListDocuments]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [DocumentService.ListDocuments][google.cloud.discoveryengine.v1.DocumentService.ListDocuments]
  // must match the call that provided the page token. Otherwise, an
  // `INVALID_ARGUMENT` error is returned.
  string page_token = 3;
}

// Response message for
// [DocumentService.ListDocuments][google.cloud.discoveryengine.v1.DocumentService.ListDocuments]
// method.
message ListDocumentsResponse {
  // The [Document][google.cloud.discoveryengine.v1.Document]s.
  repeated Document documents = 1;

  // A token that can be sent as
  // [ListDocumentsRequest.page_token][google.cloud.discoveryengine.v1.ListDocumentsRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [DocumentService.CreateDocument][google.cloud.discoveryengine.v1.DocumentService.CreateDocument]
// method.
message CreateDocumentRequest {
  // Required. The parent resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Branch"
    }
  ];

  // Required. The [Document][google.cloud.discoveryengine.v1.Document] to
  // create.
  Document document = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the
  // [Document][google.cloud.discoveryengine.v1.Document], which becomes the
  // final component of the
  // [Document.name][google.cloud.discoveryengine.v1.Document.name].
  //
  // If the caller does not have permission to create the
  // [Document][google.cloud.discoveryengine.v1.Document], regardless of whether
  // or not it exists, a `PERMISSION_DENIED` error is returned.
  //
  // This field must be unique among all
  // [Document][google.cloud.discoveryengine.v1.Document]s with the same
  // [parent][google.cloud.discoveryengine.v1.CreateDocumentRequest.parent].
  // Otherwise, an `ALREADY_EXISTS` error is returned.
  //
  // This field must conform to [RFC-1034](https://tools.ietf.org/html/rfc1034)
  // standard with a length limit of 63 characters. Otherwise, an
  // `INVALID_ARGUMENT` error is returned.
  string document_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [DocumentService.UpdateDocument][google.cloud.discoveryengine.v1.DocumentService.UpdateDocument]
// method.
message UpdateDocumentRequest {
  // Required. The document to update/create.
  //
  // If the caller does not have permission to update the
  // [Document][google.cloud.discoveryengine.v1.Document], regardless of whether
  // or not it exists, a `PERMISSION_DENIED` error is returned.
  //
  // If the [Document][google.cloud.discoveryengine.v1.Document] to update does
  // not exist and
  // [allow_missing][google.cloud.discoveryengine.v1.UpdateDocumentRequest.allow_missing]
  // is not set, a `NOT_FOUND` error is returned.
  Document document = 1 [(google.api.field_behavior) = REQUIRED];

  // If set to `true` and the
  // [Document][google.cloud.discoveryengine.v1.Document] is not found, a new
  // [Document][google.cloud.discoveryengine.v1.Document] is be created.
  bool allow_missing = 2;

  // Indicates which fields in the provided imported 'document' to update. If
  // not set, by default updates all fields.
  google.protobuf.FieldMask update_mask = 3;
}

// Request message for
// [DocumentService.DeleteDocument][google.cloud.discoveryengine.v1.DocumentService.DeleteDocument]
// method.
message DeleteDocumentRequest {
  // Required. Full resource name of
  // [Document][google.cloud.discoveryengine.v1.Document], such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}/documents/{document}`.
  //
  // If the caller does not have permission to delete the
  // [Document][google.cloud.discoveryengine.v1.Document], regardless of whether
  // or not it exists, a `PERMISSION_DENIED` error is returned.
  //
  // If the [Document][google.cloud.discoveryengine.v1.Document] to delete does
  // not exist, a `NOT_FOUND` error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Document"
    }
  ];
}

// Request message for
// [DocumentService.BatchGetDocumentsMetadata][google.cloud.discoveryengine.v1.DocumentService.BatchGetDocumentsMetadata]
// method.
message BatchGetDocumentsMetadataRequest {
  // Matcher for the [Document][google.cloud.discoveryengine.v1.Document]s by
  // exact uris.
  message UrisMatcher {
    // The exact URIs to match by.
    repeated string uris = 1;
  }

  // Matcher for the [Document][google.cloud.discoveryengine.v1.Document]s by
  // FHIR resource names.
  message FhirMatcher {
    // Required. The FHIR resources to match by.
    // Format:
    // projects/{project}/locations/{location}/datasets/{dataset}/fhirStores/{fhir_store}/fhir/{resource_type}/{fhir_resource_id}
    repeated string fhir_resources = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "healthcare.googleapis.com/FhirResource"
      }
    ];
  }

  // Matcher for the [Document][google.cloud.discoveryengine.v1.Document]s.
  // Currently supports matching by exact URIs.
  message Matcher {
    // Matcher for the [Document][google.cloud.discoveryengine.v1.Document]s.
    oneof matcher {
      // Matcher by exact URIs.
      UrisMatcher uris_matcher = 1;

      // Matcher by FHIR resource names.
      FhirMatcher fhir_matcher = 2;
    }
  }

  // Required. The parent branch resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Branch"
    }
  ];

  // Required. Matcher for the
  // [Document][google.cloud.discoveryengine.v1.Document]s.
  Matcher matcher = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for
// [DocumentService.BatchGetDocumentsMetadata][google.cloud.discoveryengine.v1.DocumentService.BatchGetDocumentsMetadata]
// method.
message BatchGetDocumentsMetadataResponse {
  // The metadata of a [Document][google.cloud.discoveryengine.v1.Document].
  message DocumentMetadata {
    // The value of the matcher that was used to match the
    // [Document][google.cloud.discoveryengine.v1.Document].
    message MatcherValue {
      // The value of the matcher that was used to match the
      // [Document][google.cloud.discoveryengine.v1.Document].
      oneof matcher_value {
        // If match by URI, the URI of the
        // [Document][google.cloud.discoveryengine.v1.Document].
        string uri = 1;

        // Format:
        // projects/{project}/locations/{location}/datasets/{dataset}/fhirStores/{fhir_store}/fhir/{resource_type}/{fhir_resource_id}
        string fhir_resource = 2 [(google.api.resource_reference) = {
          type: "healthcare.googleapis.com/FhirResource"
        }];
      }
    }

    // The value of the matcher that was used to match the
    // [Document][google.cloud.discoveryengine.v1.Document].
    MatcherValue matcher_value = 2;

    // The state of the document.
    State state = 3;

    // The timestamp of the last time the
    // [Document][google.cloud.discoveryengine.v1.Document] was last indexed.
    google.protobuf.Timestamp last_refreshed_time = 4;

    // The data ingestion source of the
    // [Document][google.cloud.discoveryengine.v1.Document].
    //
    // Allowed values are:
    //
    // * `batch`: Data ingested via Batch API, e.g., ImportDocuments.
    // * `streaming` Data ingested via Streaming API, e.g., FHIR streaming.
    string data_ingestion_source = 5;
  }

  // The state of the [Document][google.cloud.discoveryengine.v1.Document].
  enum State {
    // Should never be set.
    STATE_UNSPECIFIED = 0;

    // The [Document][google.cloud.discoveryengine.v1.Document] is indexed.
    INDEXED = 1;

    // The [Document][google.cloud.discoveryengine.v1.Document] is not indexed
    // because its URI is not in the
    // [TargetSite][google.cloud.discoveryengine.v1.TargetSite].
    NOT_IN_TARGET_SITE = 2;

    // The [Document][google.cloud.discoveryengine.v1.Document] is not indexed.
    NOT_IN_INDEX = 3;
  }

  // The metadata of the [Document][google.cloud.discoveryengine.v1.Document]s.
  repeated DocumentMetadata documents_metadata = 1;
}
