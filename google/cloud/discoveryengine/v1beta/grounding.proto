// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "GroundingProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// Grounding configuration.
message GroundingConfig {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/GroundingConfig"
    pattern: "projects/{project}/locations/{location}/groundingConfigs/{grounding_config}"
  };

  // Required. Name of the GroundingConfig, of the form
  // `projects/{project}/locations/{location}/groundingConfigs/{grounding_config}`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Grounding Fact.
message GroundingFact {
  // Text content of the fact. Can be at most 10K characters long.
  string fact_text = 1;

  // Attributes associated with the fact.
  // Common attributes include `source` (indicating where the fact was sourced
  // from), `author` (indicating the author of the fact), and so on.
  map<string, string> attributes = 2;
}

// Fact Chunk.
message FactChunk {
  // Text content of the fact chunk. Can be at most 10K characters long.
  string chunk_text = 1;

  // Source from which this fact chunk was retrieved. If it was retrieved
  // from the GroundingFacts provided in the request then this field will
  // contain the index of the specific fact from which this chunk was
  // retrieved.
  string source = 2;

  // The index of this chunk. Currently, only used for the streaming mode.
  int32 index = 4;

  // More fine-grained information for the source reference.
  map<string, string> source_metadata = 3;
}
