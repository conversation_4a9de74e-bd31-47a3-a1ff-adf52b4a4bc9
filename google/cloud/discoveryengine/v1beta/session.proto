// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "SessionProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// External session proto definition.
message Session {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/Session"
    pattern: "projects/{project}/locations/{location}/dataStores/{data_store}/sessions/{session}"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/sessions/{session}"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/engines/{engine}/sessions/{session}"
  };

  // Represents a turn, including a query from the user and a
  // answer from service.
  message Turn {
    // The user query.
    Query query = 1;

    // The resource name of the answer to the user query.
    //
    // Only set if the answer generation (/answer API call) happened in this
    // turn.
    string answer = 2 [(google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Answer"
    }];
  }

  // Enumeration of the state of the session.
  enum State {
    // State is unspecified.
    STATE_UNSPECIFIED = 0;

    // The session is currently open.
    IN_PROGRESS = 1;
  }

  // Immutable. Fully qualified name
  // `projects/{project}/locations/global/collections/{collection}/engines/{engine}/sessions/*`
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // The state of the session.
  State state = 2;

  // A unique identifier for tracking users.
  string user_pseudo_id = 3;

  // Turns.
  repeated Turn turns = 4;

  // Output only. The time the session started.
  google.protobuf.Timestamp start_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the session finished.
  google.protobuf.Timestamp end_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines a user inputed query.
message Query {
  // Query content.
  oneof content {
    // Plain text.
    string text = 2;
  }

  // Unique Id for the query.
  string query_id = 1;
}
