// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1beta/common.proto";
import "google/cloud/discoveryengine/v1beta/document_processing_config.proto";
import "google/cloud/discoveryengine/v1beta/schema.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "DataStoreProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// DataStore captures global settings and configs at the DataStore level.
message DataStore {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/DataStore"
    pattern: "projects/{project}/locations/{location}/dataStores/{data_store}"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}"
  };

  // Estimation of data size per data store.
  message BillingEstimation {
    // Data size for structured data in terms of bytes.
    int64 structured_data_size = 1;

    // Data size for unstructured data in terms of bytes.
    int64 unstructured_data_size = 2;

    // Data size for websites in terms of bytes.
    int64 website_data_size = 3;

    // Last updated timestamp for structured data.
    google.protobuf.Timestamp structured_data_update_time = 4;

    // Last updated timestamp for unstructured data.
    google.protobuf.Timestamp unstructured_data_update_time = 5;

    // Last updated timestamp for websites.
    google.protobuf.Timestamp website_data_update_time = 6;
  }

  // Stores information regarding the serving configurations at DataStore level.
  message ServingConfigDataStore {
    // If set true, the DataStore will not be available for serving search
    // requests.
    bool disabled_for_serving = 1;
  }

  // Content config of the data store.
  enum ContentConfig {
    // Default value.
    CONTENT_CONFIG_UNSPECIFIED = 0;

    // Only contains documents without any
    // [Document.content][google.cloud.discoveryengine.v1beta.Document.content].
    NO_CONTENT = 1;

    // Only contains documents with
    // [Document.content][google.cloud.discoveryengine.v1beta.Document.content].
    CONTENT_REQUIRED = 2;

    // The data store is used for public website search.
    PUBLIC_WEBSITE = 3;

    // The data store is used for workspace search. Details of workspace
    // data store are specified in the
    // [WorkspaceConfig][google.cloud.discoveryengine.v1beta.WorkspaceConfig].
    GOOGLE_WORKSPACE = 4;
  }

  // Immutable. The full resource name of the data store.
  // Format:
  // `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`.
  //
  // This field must be a UTF-8 encoded string with a length limit of 1024
  // characters.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Required. The data store display name.
  //
  // This field must be a UTF-8 encoded string with a length limit of 128
  // characters. Otherwise, an INVALID_ARGUMENT error is returned.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Immutable. The industry vertical that the data store registers.
  IndustryVertical industry_vertical = 3
      [(google.api.field_behavior) = IMMUTABLE];

  // The solutions that the data store enrolls. Available solutions for each
  // [industry_vertical][google.cloud.discoveryengine.v1beta.DataStore.industry_vertical]:
  //
  // * `MEDIA`: `SOLUTION_TYPE_RECOMMENDATION` and `SOLUTION_TYPE_SEARCH`.
  // * `SITE_SEARCH`: `SOLUTION_TYPE_SEARCH` is automatically enrolled. Other
  //   solutions cannot be enrolled.
  repeated SolutionType solution_types = 5;

  // Output only. The id of the default
  // [Schema][google.cloud.discoveryengine.v1beta.Schema] asscociated to this
  // data store.
  string default_schema_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The content config of the data store. If this field is unset,
  // the server behavior defaults to
  // [ContentConfig.NO_CONTENT][google.cloud.discoveryengine.v1beta.DataStore.ContentConfig.NO_CONTENT].
  ContentConfig content_config = 6 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Timestamp the
  // [DataStore][google.cloud.discoveryengine.v1beta.DataStore] was created at.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Language info for DataStore.
  LanguageInfo language_info = 14;

  // Optional. Configuration for Natural Language Query Understanding.
  NaturalLanguageQueryUnderstandingConfig
      natural_language_query_understanding_config = 34
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Data size estimation for billing.
  BillingEstimation billing_estimation = 23
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Config to store data store type configuration for workspace data. This
  // must be set when
  // [DataStore.content_config][google.cloud.discoveryengine.v1beta.DataStore.content_config]
  // is set as
  // [DataStore.ContentConfig.GOOGLE_WORKSPACE][google.cloud.discoveryengine.v1beta.DataStore.ContentConfig.GOOGLE_WORKSPACE].
  WorkspaceConfig workspace_config = 25;

  // Configuration for Document understanding and enrichment.
  DocumentProcessingConfig document_processing_config = 27;

  // The start schema to use for this
  // [DataStore][google.cloud.discoveryengine.v1beta.DataStore] when
  // provisioning it. If unset, a default vertical specialized schema will be
  // used.
  //
  // This field is only used by [CreateDataStore][] API, and will be ignored if
  // used in other APIs. This field will be omitted from all API responses
  // including [CreateDataStore][] API. To retrieve a schema of a
  // [DataStore][google.cloud.discoveryengine.v1beta.DataStore], use
  // [SchemaService.GetSchema][google.cloud.discoveryengine.v1beta.SchemaService.GetSchema]
  // API instead.
  //
  // The provided schema will be validated against certain rules on schema.
  // Learn more from [this
  // doc](https://cloud.google.com/generative-ai-app-builder/docs/provide-schema).
  Schema starting_schema = 28;

  // Optional. Stores serving config at DataStore level.
  ServingConfigDataStore serving_config_data_store = 30
      [(google.api.field_behavior) = OPTIONAL];
}

// Language info for DataStore.
message LanguageInfo {
  // The language code for the DataStore.
  string language_code = 1;

  // Output only. This is the normalized form of language_code.
  // E.g.: language_code of `en-GB`, `en_GB`, `en-UK` or `en-gb`
  // will have normalized_language_code of `en-GB`.
  string normalized_language_code = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Language part of normalized_language_code.
  // E.g.: `en-US` -> `en`, `zh-Hans-HK` -> `zh`, `en` -> `en`.
  string language = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Region part of normalized_language_code, if present.
  // E.g.: `en-US` -> `US`, `zh-Hans-HK` -> `HK`, `en` -> ``.
  string region = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration for Natural Language Query Understanding.
message NaturalLanguageQueryUnderstandingConfig {
  // Mode of Natural Language Query Understanding. When the
  // NaturalLanguageQueryUnderstandingConfig.Mode is ENABLED, the natural
  // language understanding capabilities will be enabled for a search request if
  // the NaturalLanguageQueryUnderstandingSpec.FilterExtractionCondition in the
  // SearchRequest is ENABLED.
  enum Mode {
    // Default value.
    MODE_UNSPECIFIED = 0;

    // Natural Language Query Understanding is disabled.
    DISABLED = 1;

    // Natural Language Query Understanding is enabled.
    ENABLED = 2;
  }

  // Mode of Natural Language Query Understanding. If this field is unset, the
  // behavior defaults to
  // [NaturalLanguageQueryUnderstandingConfig.Mode.DISABLED][google.cloud.discoveryengine.v1beta.NaturalLanguageQueryUnderstandingConfig.Mode.DISABLED].
  Mode mode = 1;
}

// Config to store data store type configuration for workspace data
message WorkspaceConfig {
  // Specifies the type of Workspace App supported by this DataStore
  enum Type {
    // Defaults to an unspecified Workspace type.
    TYPE_UNSPECIFIED = 0;

    // Workspace Data Store contains Drive data
    GOOGLE_DRIVE = 1;

    // Workspace Data Store contains Mail data
    GOOGLE_MAIL = 2;

    // Workspace Data Store contains Sites data
    GOOGLE_SITES = 3;

    // Workspace Data Store contains Calendar data
    GOOGLE_CALENDAR = 4;

    // Workspace Data Store contains Chat data
    GOOGLE_CHAT = 5;

    // Workspace Data Store contains Groups data
    GOOGLE_GROUPS = 6;

    // Workspace Data Store contains Keep data
    GOOGLE_KEEP = 7;
  }

  // The Google Workspace data source.
  Type type = 1;

  // Obfuscated Dasher customer ID.
  string dasher_customer_id = 2;

  // Optional. The super admin service account for the workspace that will be
  // used for access token generation. For now we only use it for Native Google
  // Drive connector data ingestion.
  string super_admin_service_account = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The super admin email address for the workspace that will be used
  // for access token generation. For now we only use it for Native Google Drive
  // connector data ingestion.
  string super_admin_email_address = 5 [(google.api.field_behavior) = OPTIONAL];
}
