// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1beta/evaluation.proto";
import "google/cloud/discoveryengine/v1beta/sample_query.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "EvaluationServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// Service for managing
// [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation]s,
service EvaluationService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets a [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation].
  rpc GetEvaluation(GetEvaluationRequest) returns (Evaluation) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/evaluations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a list of
  // [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation]s.
  rpc ListEvaluations(ListEvaluationsRequest)
      returns (ListEvaluationsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*}/evaluations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation].
  //
  // Upon creation, the evaluation will be automatically triggered and begin
  // execution.
  rpc CreateEvaluation(CreateEvaluationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*}/evaluations"
      body: "evaluation"
    };
    option (google.api.method_signature) = "parent,evaluation";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.Evaluation"
      metadata_type: "google.cloud.discoveryengine.v1beta.CreateEvaluationMetadata"
    };
  }

  // Gets a list of results for a given a
  // [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation].
  rpc ListEvaluationResults(ListEvaluationResultsRequest)
      returns (ListEvaluationResultsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{evaluation=projects/*/locations/*/evaluations/*}:listResults"
    };
    option (google.api.method_signature) = "evaluation";
  }
}

// Request message for
// [EvaluationService.GetEvaluation][google.cloud.discoveryengine.v1beta.EvaluationService.GetEvaluation]
// method.
message GetEvaluationRequest {
  // Required. Full resource name of
  // [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation], such as
  // `projects/{project}/locations/{location}/evaluations/{evaluation}`.
  //
  // If the caller does not have permission to access the
  // [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation], regardless of
  // whether or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the requested
  // [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation] does not
  // exist, a NOT_FOUND error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Evaluation"
    }
  ];
}

// Request message for
// [EvaluationService.ListEvaluations][google.cloud.discoveryengine.v1beta.EvaluationService.ListEvaluations]
// method.
message ListEvaluationsRequest {
  // Required. The parent location resource name, such as
  // `projects/{project}/locations/{location}`.
  //
  // If the caller does not have permission to list
  // [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation]s under this
  // location, regardless of whether or not this location exists, a
  // `PERMISSION_DENIED` error is returned.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Location"
    }
  ];

  // Maximum number of
  // [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation]s to return. If
  // unspecified, defaults to 100. The maximum allowed value is 1000. Values
  // above 1000 will be coerced to 1000.
  //
  // If this field is negative, an `INVALID_ARGUMENT` error is returned.
  int32 page_size = 2;

  // A page token
  // [ListEvaluationsResponse.next_page_token][google.cloud.discoveryengine.v1beta.ListEvaluationsResponse.next_page_token],
  // received from a previous
  // [EvaluationService.ListEvaluations][google.cloud.discoveryengine.v1beta.EvaluationService.ListEvaluations]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [EvaluationService.ListEvaluations][google.cloud.discoveryengine.v1beta.EvaluationService.ListEvaluations]
  // must match the call that provided the page token. Otherwise, an
  // `INVALID_ARGUMENT` error is returned.
  string page_token = 3;
}

// Response message for
// [EvaluationService.ListEvaluations][google.cloud.discoveryengine.v1beta.EvaluationService.ListEvaluations]
// method.
message ListEvaluationsResponse {
  // The [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation]s.
  repeated Evaluation evaluations = 1;

  // A token that can be sent as
  // [ListEvaluationsRequest.page_token][google.cloud.discoveryengine.v1beta.ListEvaluationsRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [EvaluationService.CreateEvaluation][google.cloud.discoveryengine.v1beta.EvaluationService.CreateEvaluation]
// method.
message CreateEvaluationRequest {
  // Required. The parent resource name, such as
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Location"
    }
  ];

  // Required. The [Evaluation][google.cloud.discoveryengine.v1beta.Evaluation]
  // to create.
  Evaluation evaluation = 2 [(google.api.field_behavior) = REQUIRED];
}

// Metadata for
// [EvaluationService.CreateEvaluation][google.cloud.discoveryengine.v1beta.EvaluationService.CreateEvaluation]
// method.
message CreateEvaluationMetadata {}

// Request message for
// [EvaluationService.ListEvaluationResults][google.cloud.discoveryengine.v1beta.EvaluationService.ListEvaluationResults]
// method.
message ListEvaluationResultsRequest {
  // Required. The evaluation resource name, such as
  // `projects/{project}/locations/{location}/evaluations/{evaluation}`.
  //
  // If the caller does not have permission to list [EvaluationResult][]
  // under this evaluation, regardless of whether or not this evaluation
  // set exists, a `PERMISSION_DENIED` error is returned.
  string evaluation = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Evaluation"
    }
  ];

  // Maximum number of [EvaluationResult][] to return. If unspecified,
  // defaults to 100. The maximum allowed value is 1000. Values above 1000 will
  // be coerced to 1000.
  //
  // If this field is negative, an `INVALID_ARGUMENT` error is returned.
  int32 page_size = 2;

  // A page token
  // [ListEvaluationResultsResponse.next_page_token][google.cloud.discoveryengine.v1beta.ListEvaluationResultsResponse.next_page_token],
  // received from a previous
  // [EvaluationService.ListEvaluationResults][google.cloud.discoveryengine.v1beta.EvaluationService.ListEvaluationResults]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [EvaluationService.ListEvaluationResults][google.cloud.discoveryengine.v1beta.EvaluationService.ListEvaluationResults]
  // must match the call that provided the page token. Otherwise, an
  // `INVALID_ARGUMENT` error is returned.
  string page_token = 3;
}

// Response message for
// [EvaluationService.ListEvaluationResults][google.cloud.discoveryengine.v1beta.EvaluationService.ListEvaluationResults]
// method.
message ListEvaluationResultsResponse {
  // Represents the results of an evaluation for a single
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery].
  message EvaluationResult {
    // Output only. The
    // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery] that was
    // evaluated.
    SampleQuery sample_query = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The metrics produced by the evaluation, for a given
    // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery].
    QualityMetrics quality_metrics = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The
  // [EvaluationResult][google.cloud.discoveryengine.v1beta.ListEvaluationResultsResponse.EvaluationResult]s.
  repeated EvaluationResult evaluation_results = 1;

  // A token that can be sent as
  // [ListEvaluationResultsRequest.page_token][google.cloud.discoveryengine.v1beta.ListEvaluationResultsRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}
