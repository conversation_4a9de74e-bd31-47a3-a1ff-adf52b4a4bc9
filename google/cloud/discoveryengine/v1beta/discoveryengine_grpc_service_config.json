{"methodConfig": [{"name": [{"service": "google.cloud.discoveryengine.v1beta.CompletionService"}, {"service": "google.cloud.discoveryengine.v1beta.GroundedGenerationService"}, {"service": "google.cloud.discoveryengine.v1beta.RecommendationService"}], "timeout": "5s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "5s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.discoveryengine.v1beta.ControlService"}, {"service": "google.cloud.discoveryengine.v1beta.ConversationalSearchService"}, {"service": "google.cloud.discoveryengine.v1beta.DocumentService"}, {"service": "google.cloud.discoveryengine.v1beta.EvaluationService"}, {"service": "google.cloud.discoveryengine.v1beta.ProjectService"}, {"service": "google.cloud.discoveryengine.v1beta.RankService"}, {"service": "google.cloud.discoveryengine.v1beta.SampleQueryService"}, {"service": "google.cloud.discoveryengine.v1beta.SampleQuerySetService"}, {"service": "google.cloud.discoveryengine.v1beta.SchemaService"}, {"service": "google.cloud.discoveryengine.v1beta.SearchService"}, {"service": "google.cloud.discoveryengine.v1beta.UserEventService"}, {"service": "google.longrunning.Operations"}], "timeout": "30s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.discoveryengine.v1beta.DocumentService", "method": "ImportDocuments"}, {"service": "google.cloud.discoveryengine.v1beta.UserEventService", "method": "ImportUserEvents"}, {"service": "google.longrunning.Operations", "method": "ListOperations"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.discoveryengine.v1beta.EvaluationService", "method": "CreateEvaluation"}], "timeout": "300s"}]}