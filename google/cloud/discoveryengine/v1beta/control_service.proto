// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1beta/control.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "ControlServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// Service for performing CRUD operations on Controls.
// Controls allow for custom logic to be implemented in the serving path.
// Controls need to be attached to a Serving Config to be considered during a
// request.
service ControlService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a Control.
  //
  // By default 1000 controls are allowed for a data store.
  // A request can be submitted to adjust this limit.
  // If the [Control][google.cloud.discoveryengine.v1beta.Control] to create
  // already exists, an ALREADY_EXISTS error is returned.
  rpc CreateControl(CreateControlRequest) returns (Control) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/dataStores/*}/controls"
      body: "control"
      additional_bindings {
        post: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*}/controls"
        body: "control"
      }
      additional_bindings {
        post: "/v1beta/{parent=projects/*/locations/*/collections/*/engines/*}/controls"
        body: "control"
      }
    };
    option (google.api.method_signature) = "parent,control,control_id";
  }

  // Deletes a Control.
  //
  // If the [Control][google.cloud.discoveryengine.v1beta.Control] to delete
  // does not exist, a NOT_FOUND error is returned.
  rpc DeleteControl(DeleteControlRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/dataStores/*/controls/*}"
      additional_bindings {
        delete: "/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/controls/*}"
      }
      additional_bindings {
        delete: "/v1beta/{name=projects/*/locations/*/collections/*/engines/*/controls/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a Control.
  //
  // [Control][google.cloud.discoveryengine.v1beta.Control] action type cannot
  // be changed. If the [Control][google.cloud.discoveryengine.v1beta.Control]
  // to update does not exist, a NOT_FOUND error is returned.
  rpc UpdateControl(UpdateControlRequest) returns (Control) {
    option (google.api.http) = {
      patch: "/v1beta/{control.name=projects/*/locations/*/dataStores/*/controls/*}"
      body: "control"
      additional_bindings {
        patch: "/v1beta/{control.name=projects/*/locations/*/collections/*/dataStores/*/controls/*}"
        body: "control"
      }
      additional_bindings {
        patch: "/v1beta/{control.name=projects/*/locations/*/collections/*/engines/*/controls/*}"
        body: "control"
      }
    };
    option (google.api.method_signature) = "control,update_mask";
  }

  // Gets a Control.
  rpc GetControl(GetControlRequest) returns (Control) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/dataStores/*/controls/*}"
      additional_bindings {
        get: "/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/controls/*}"
      }
      additional_bindings {
        get: "/v1beta/{name=projects/*/locations/*/collections/*/engines/*/controls/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all Controls by their parent
  // [DataStore][google.cloud.discoveryengine.v1beta.DataStore].
  rpc ListControls(ListControlsRequest) returns (ListControlsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/dataStores/*}/controls"
      additional_bindings {
        get: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*}/controls"
      }
      additional_bindings {
        get: "/v1beta/{parent=projects/*/locations/*/collections/*/engines/*}/controls"
      }
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request for CreateControl method.
message CreateControlRequest {
  // Required. Full resource name of parent data store. Format:
  // `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`
  // or
  // `projects/{project}/locations/{location}/collections/{collection_id}/engines/{engine_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "discoveryengine.googleapis.com/Control"
    }
  ];

  // Required. The Control to create.
  Control control = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the Control, which will become the final
  // component of the Control's resource name.
  //
  // This value must be within 1-63 characters.
  // Valid characters are /[a-z][0-9]-_/.
  string control_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for UpdateControl method.
message UpdateControlRequest {
  // Required. The Control to update.
  Control control = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Indicates which fields in the provided
  // [Control][google.cloud.discoveryengine.v1beta.Control] to update. The
  // following are NOT supported:
  //
  // * [Control.name][google.cloud.discoveryengine.v1beta.Control.name]
  // * [Control.solution_type][google.cloud.discoveryengine.v1beta.Control.solution_type]
  //
  // If not set or empty, all supported fields are updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request for DeleteControl method.
message DeleteControlRequest {
  // Required. The resource name of the Control to delete. Format:
  // `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}/controls/{control_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Control"
    }
  ];
}

// Request for GetControl method.
message GetControlRequest {
  // Required. The resource name of the Control to get. Format:
  // `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}/controls/{control_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Control"
    }
  ];
}

// Request for ListControls method.
message ListControlsRequest {
  // Required. The data store resource name. Format:
  // `projects/{project}/locations/{location}/collections/{collection_id}/dataStores/{data_store_id}`
  // or
  // `projects/{project}/locations/{location}/collections/{collection_id}/engines/{engine_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "discoveryengine.googleapis.com/Control"
    }
  ];

  // Optional. Maximum number of results to return. If unspecified, defaults
  // to 50. Max allowed value is 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListControls` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter to apply on the list results. Supported features:
  //
  // * List all the products under the parent branch if
  // [filter][google.cloud.discoveryengine.v1beta.ListControlsRequest.filter] is
  // unset. Currently this field is unsupported.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response for ListControls method.
message ListControlsResponse {
  // All the Controls for a given data store.
  repeated Control controls = 1;

  // Pagination token, if not returned indicates the last page.
  string next_page_token = 2;
}
