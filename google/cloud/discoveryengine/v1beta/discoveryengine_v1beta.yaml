type: google.api.Service
config_version: 3
name: discoveryengine.googleapis.com
title: Discovery Engine API

apis:
- name: google.cloud.discoveryengine.v1beta.CompletionService
- name: google.cloud.discoveryengine.v1beta.ControlService
- name: google.cloud.discoveryengine.v1beta.ConversationalSearchService
- name: google.cloud.discoveryengine.v1beta.DataStoreService
- name: google.cloud.discoveryengine.v1beta.DocumentService
- name: google.cloud.discoveryengine.v1beta.EngineService
- name: google.cloud.discoveryengine.v1beta.EvaluationService
- name: google.cloud.discoveryengine.v1beta.GroundedGenerationService
- name: google.cloud.discoveryengine.v1beta.ProjectService
- name: google.cloud.discoveryengine.v1beta.RankService
- name: google.cloud.discoveryengine.v1beta.RecommendationService
- name: google.cloud.discoveryengine.v1beta.SampleQueryService
- name: google.cloud.discoveryengine.v1beta.SampleQuerySetService
- name: google.cloud.discoveryengine.v1beta.SchemaService
- name: google.cloud.discoveryengine.v1beta.SearchService
- name: google.cloud.discoveryengine.v1beta.SearchTuningService
- name: google.cloud.discoveryengine.v1beta.ServingConfigService
- name: google.cloud.discoveryengine.v1beta.SiteSearchEngineService
- name: google.cloud.discoveryengine.v1beta.UserEventService
- name: google.cloud.location.Locations
- name: google.longrunning.Operations

types:
- name: google.cloud.discoveryengine.logging.ErrorLog
- name: google.cloud.discoveryengine.v1beta.BatchCreateTargetSiteMetadata
- name: google.cloud.discoveryengine.v1beta.BatchCreateTargetSitesResponse
- name: google.cloud.discoveryengine.v1beta.Control
- name: google.cloud.discoveryengine.v1beta.CreateDataStoreMetadata
- name: google.cloud.discoveryengine.v1beta.CreateEngineMetadata
- name: google.cloud.discoveryengine.v1beta.CreateEvaluationMetadata
- name: google.cloud.discoveryengine.v1beta.CreateSchemaMetadata
- name: google.cloud.discoveryengine.v1beta.CreateSitemapMetadata
- name: google.cloud.discoveryengine.v1beta.CreateTargetSiteMetadata
- name: google.cloud.discoveryengine.v1beta.DataStore
- name: google.cloud.discoveryengine.v1beta.DeleteDataStoreMetadata
- name: google.cloud.discoveryengine.v1beta.DeleteEngineMetadata
- name: google.cloud.discoveryengine.v1beta.DeleteSchemaMetadata
- name: google.cloud.discoveryengine.v1beta.DeleteSitemapMetadata
- name: google.cloud.discoveryengine.v1beta.DeleteTargetSiteMetadata
- name: google.cloud.discoveryengine.v1beta.DisableAdvancedSiteSearchMetadata
- name: google.cloud.discoveryengine.v1beta.DisableAdvancedSiteSearchResponse
- name: google.cloud.discoveryengine.v1beta.EnableAdvancedSiteSearchMetadata
- name: google.cloud.discoveryengine.v1beta.EnableAdvancedSiteSearchResponse
- name: google.cloud.discoveryengine.v1beta.Engine
- name: google.cloud.discoveryengine.v1beta.Evaluation
- name: google.cloud.discoveryengine.v1beta.FetchSitemapsResponse
- name: google.cloud.discoveryengine.v1beta.GroundingConfig
- name: google.cloud.discoveryengine.v1beta.ImportCompletionSuggestionsMetadata
- name: google.cloud.discoveryengine.v1beta.ImportCompletionSuggestionsResponse
- name: google.cloud.discoveryengine.v1beta.ImportDocumentsMetadata
- name: google.cloud.discoveryengine.v1beta.ImportDocumentsResponse
- name: google.cloud.discoveryengine.v1beta.ImportSampleQueriesMetadata
- name: google.cloud.discoveryengine.v1beta.ImportSampleQueriesResponse
- name: google.cloud.discoveryengine.v1beta.ImportSuggestionDenyListEntriesMetadata
- name: google.cloud.discoveryengine.v1beta.ImportSuggestionDenyListEntriesResponse
- name: google.cloud.discoveryengine.v1beta.ImportUserEventsMetadata
- name: google.cloud.discoveryengine.v1beta.ImportUserEventsResponse
- name: google.cloud.discoveryengine.v1beta.ListCustomModelsResponse
- name: google.cloud.discoveryengine.v1beta.Project
- name: google.cloud.discoveryengine.v1beta.ProvisionProjectMetadata
- name: google.cloud.discoveryengine.v1beta.PurgeDocumentsMetadata
- name: google.cloud.discoveryengine.v1beta.PurgeDocumentsResponse
- name: google.cloud.discoveryengine.v1beta.PurgeSuggestionDenyListEntriesMetadata
- name: google.cloud.discoveryengine.v1beta.PurgeSuggestionDenyListEntriesResponse
- name: google.cloud.discoveryengine.v1beta.Schema
- name: google.cloud.discoveryengine.v1beta.Sitemap
- name: google.cloud.discoveryengine.v1beta.TargetSite
- name: google.cloud.discoveryengine.v1beta.TrainCustomModelMetadata
- name: google.cloud.discoveryengine.v1beta.TrainCustomModelResponse
- name: google.cloud.discoveryengine.v1beta.TuneEngineMetadata
- name: google.cloud.discoveryengine.v1beta.TuneEngineResponse
- name: google.cloud.discoveryengine.v1beta.UpdateSchemaMetadata
- name: google.cloud.discoveryengine.v1beta.UpdateTargetSiteMetadata

documentation:
  summary: Discovery Engine API.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/branches/*/operations/*}:cancel'
    body: '*'
    additional_bindings:
    - post: '/v1beta/{name=projects/*/locations/*/dataStores/*/branches/*/operations/*}:cancel'
      body: '*'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1beta/{name=projects/*/locations/*/collections/*/dataConnector/operations/*}'
    additional_bindings:
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/branches/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/models/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/schemas/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/targetSites/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/engines/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/dataStores/*/branches/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/dataStores/*/models/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/dataStores/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/evaluations/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/operations/*}'
    - get: '/v1beta/{name=projects/*/locations/*/sampleQuerySets/*/operations/*}'
    - get: '/v1beta/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1beta/{name=projects/*/locations/*/collections/*/dataConnector}/operations'
    additional_bindings:
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/branches/*}/operations'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/models/*}/operations'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/schemas/*}/operations'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/targetSites}/operations'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}/operations'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*}/operations'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*/engines/*}/operations'
    - get: '/v1beta/{name=projects/*/locations/*/collections/*}/operations'
    - get: '/v1beta/{name=projects/*/locations/*/dataStores/*/branches/*}/operations'
    - get: '/v1beta/{name=projects/*/locations/*/dataStores/*/models/*}/operations'
    - get: '/v1beta/{name=projects/*/locations/*/dataStores/*}/operations'
    - get: '/v1beta/{name=projects/*/locations/*}/operations'
    - get: '/v1beta/{name=projects/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.discoveryengine.v1beta.CompletionService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.ControlService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.ConversationalSearchService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.DataStoreService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.DocumentService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.EngineService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.EvaluationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.GroundedGenerationService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1beta.ProjectService.ProvisionProject
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1beta.RankService.Rank
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1beta.RecommendationService.Recommend
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.SampleQueryService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.SampleQuerySetService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.SchemaService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1beta.SearchService.Search
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1beta.SearchService.SearchLite
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1beta.SearchTuningService.ListCustomModels
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.discoveryengine.v1beta.SearchTuningService.TrainCustomModel
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.ServingConfigService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.SiteSearchEngineService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.discoveryengine.v1beta.UserEventService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=911831&template=1480251
  documentation_uri: https://cloud.google.com/generative-ai-app-builder/docs
  api_short_name: discoveryengine
  github_label: 'api: discoveryengine'
  doc_tag_prefix: discoveryengine
  organization: CLOUD
  library_settings:
  - version: google.cloud.discoveryengine.v1beta
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/generative-ai-app-builder/docs/reference/rpc
