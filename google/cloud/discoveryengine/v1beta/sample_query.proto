// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "SampleQueryProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// Sample Query captures metadata to be used for evaluation.
message SampleQuery {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/SampleQuery"
    pattern: "projects/{project}/locations/{location}/sampleQuerySets/{sample_query_set}/sampleQueries/{sample_query}"
  };

  // Query Entry captures metadata to be used for search evaluation.
  message QueryEntry {
    // Defines the parameters of the query's expected outcome.
    message Target {
      // Expected uri of the target.
      //
      // This field must be a UTF-8 encoded string with a length limit of 2048
      // characters.
      //
      // Example of valid uris: `https://example.com/abc`,
      // `gcs://example/example.pdf`.
      string uri = 1;

      // Expected page numbers of the target.
      //
      // Each page number must be non negative.
      repeated int32 page_numbers = 2;

      // Relevance score of the target.
      optional double score = 3;
    }

    // Required. The query.
    string query = 1 [(google.api.field_behavior) = REQUIRED];

    // List of targets for the query.
    repeated Target targets = 3;
  }

  // The content of the sample query.
  oneof content {
    // The query entry.
    QueryEntry query_entry = 2;
  }

  // Identifier. The full resource name of the sample query, in the format of
  // `projects/{project}/locations/{location}/sampleQuerySets/{sample_query_set}/sampleQueries/{sample_query}`.
  //
  // This field must be a UTF-8 encoded string with a length limit of 1024
  // characters.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Timestamp the
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery] was created
  // at.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
