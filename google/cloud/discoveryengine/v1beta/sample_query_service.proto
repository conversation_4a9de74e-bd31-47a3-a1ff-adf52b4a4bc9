// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1beta/import_config.proto";
import "google/cloud/discoveryengine/v1beta/sample_query.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "SampleQueryServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// Service for managing
// [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery]s,
service SampleQueryService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets a [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery].
  rpc GetSampleQuery(GetSampleQueryRequest) returns (SampleQuery) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/sampleQuerySets/*/sampleQueries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a list of
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery]s.
  rpc ListSampleQueries(ListSampleQueriesRequest)
      returns (ListSampleQueriesResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/sampleQuerySets/*}/sampleQueries"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery]
  rpc CreateSampleQuery(CreateSampleQueryRequest) returns (SampleQuery) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/sampleQuerySets/*}/sampleQueries"
      body: "sample_query"
    };
    option (google.api.method_signature) =
        "parent,sample_query,sample_query_id";
  }

  // Updates a [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery].
  rpc UpdateSampleQuery(UpdateSampleQueryRequest) returns (SampleQuery) {
    option (google.api.http) = {
      patch: "/v1beta/{sample_query.name=projects/*/locations/*/sampleQuerySets/*/sampleQueries/*}"
      body: "sample_query"
    };
    option (google.api.method_signature) = "sample_query,update_mask";
  }

  // Deletes a [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery].
  rpc DeleteSampleQuery(DeleteSampleQueryRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/sampleQuerySets/*/sampleQueries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Bulk import of multiple
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery]s. Sample
  // queries that already exist may be deleted.
  //
  // Note: It is possible for a subset of the
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery]s to be
  // successfully imported.
  rpc ImportSampleQueries(ImportSampleQueriesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/sampleQuerySets/*}/sampleQueries:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.ImportSampleQueriesResponse"
      metadata_type: "google.cloud.discoveryengine.v1beta.ImportSampleQueriesMetadata"
    };
  }
}

// Request message for
// [SampleQueryService.GetSampleQuery][google.cloud.discoveryengine.v1beta.SampleQueryService.GetSampleQuery]
// method.
message GetSampleQueryRequest {
  // Required. Full resource name of
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery], such as
  // `projects/{project}/locations/{location}/sampleQuerySets/{sample_query_set}/sampleQueries/{sample_query}`.
  //
  // If the caller does not have permission to access the
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery], regardless
  // of whether or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the requested
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery] does not
  // exist, a NOT_FOUND error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SampleQuery"
    }
  ];
}

// Request message for
// [SampleQueryService.ListSampleQueries][google.cloud.discoveryengine.v1beta.SampleQueryService.ListSampleQueries]
// method.
message ListSampleQueriesRequest {
  // Required. The parent sample query set resource name, such as
  // `projects/{project}/locations/{location}/sampleQuerySets/{sampleQuerySet}`.
  //
  // If the caller does not have permission to list
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery]s under this
  // sample query set, regardless of whether or not this sample query set
  // exists, a `PERMISSION_DENIED` error is returned.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SampleQuerySet"
    }
  ];

  // Maximum number of
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery]s to return.
  // If unspecified, defaults to 100. The maximum allowed value is 1000. Values
  // above 1000 will be coerced to 1000.
  //
  // If this field is negative, an `INVALID_ARGUMENT` error is returned.
  int32 page_size = 2;

  // A page token
  // [ListSampleQueriesResponse.next_page_token][google.cloud.discoveryengine.v1beta.ListSampleQueriesResponse.next_page_token],
  // received from a previous
  // [SampleQueryService.ListSampleQueries][google.cloud.discoveryengine.v1beta.SampleQueryService.ListSampleQueries]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [SampleQueryService.ListSampleQueries][google.cloud.discoveryengine.v1beta.SampleQueryService.ListSampleQueries]
  // must match the call that provided the page token. Otherwise, an
  // `INVALID_ARGUMENT` error is returned.
  string page_token = 3;
}

// Response message for
// [SampleQueryService.ListSampleQueries][google.cloud.discoveryengine.v1beta.SampleQueryService.ListSampleQueries]
// method.
message ListSampleQueriesResponse {
  // The [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery]s.
  repeated SampleQuery sample_queries = 1;

  // A token that can be sent as
  // [ListSampleQueriesRequest.page_token][google.cloud.discoveryengine.v1beta.ListSampleQueriesRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [SampleQueryService.CreateSampleQuery][google.cloud.discoveryengine.v1beta.SampleQueryService.CreateSampleQuery]
// method.
message CreateSampleQueryRequest {
  // Required. The parent resource name, such as
  // `projects/{project}/locations/{location}/sampleQuerySets/{sampleQuerySet}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SampleQuerySet"
    }
  ];

  // Required. The
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery] to create.
  SampleQuery sample_query = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery], which will
  // become the final component of the
  // [SampleQuery.name][google.cloud.discoveryengine.v1beta.SampleQuery.name].
  //
  // If the caller does not have permission to create the
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery], regardless
  // of whether or not it exists, a `PERMISSION_DENIED` error is returned.
  //
  // This field must be unique among all
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery]s with the
  // same
  // [parent][google.cloud.discoveryengine.v1beta.CreateSampleQueryRequest.parent].
  // Otherwise, an `ALREADY_EXISTS` error is returned.
  //
  // This field must conform to [RFC-1034](https://tools.ietf.org/html/rfc1034)
  // standard with a length limit of 63 characters. Otherwise, an
  // `INVALID_ARGUMENT` error is returned.
  string sample_query_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [SampleQueryService.UpdateSampleQuery][google.cloud.discoveryengine.v1beta.SampleQueryService.UpdateSampleQuery]
// method.
message UpdateSampleQueryRequest {
  // Required. The simple query to update.
  //
  // If the caller does not have permission to update the
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery], regardless
  // of whether or not it exists, a `PERMISSION_DENIED` error is returned.
  //
  // If the [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery] to
  // update does not exist a `NOT_FOUND` error is returned.
  SampleQuery sample_query = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates which fields in the provided imported 'simple query' to update.
  // If not set, will by default update all fields.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for
// [SampleQueryService.DeleteSampleQuery][google.cloud.discoveryengine.v1beta.SampleQueryService.DeleteSampleQuery]
// method.
message DeleteSampleQueryRequest {
  // Required. Full resource name of
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery], such as
  // `projects/{project}/locations/{location}/sampleQuerySets/{sample_query_set}/sampleQueries/{sample_query}`.
  //
  // If the caller does not have permission to delete the
  // [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery], regardless
  // of whether or not it exists, a `PERMISSION_DENIED` error is returned.
  //
  // If the [SampleQuery][google.cloud.discoveryengine.v1beta.SampleQuery] to
  // delete does not exist, a `NOT_FOUND` error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SampleQuery"
    }
  ];
}
