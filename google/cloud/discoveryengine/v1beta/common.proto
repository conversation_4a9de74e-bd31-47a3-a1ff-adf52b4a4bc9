// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";
option (google.api.resource_definition) = {
  type: "discoveryengine.googleapis.com/Branch"
  pattern: "projects/{project}/locations/{location}/dataStores/{data_store}/branches/{branch}"
  pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}"
};
option (google.api.resource_definition) = {
  type: "discoveryengine.googleapis.com/Collection"
  pattern: "projects/{project}/locations/{location}/collections/{collection}"
};
option (google.api.resource_definition) = {
  type: "discoveryengine.googleapis.com/Location"
  pattern: "projects/{project}/locations/{location}"
};
option (google.api.resource_definition) = {
  type: "discoveryengine.googleapis.com/RankingConfig"
  pattern: "projects/{project}/locations/{location}/rankingConfigs/{ranking_config}"
};
option (google.api.resource_definition) = {
  type: "discoveryengine.googleapis.com/CompletionConfig"
  pattern: "projects/{project}/locations/{location}/dataStores/{data_store}/completionConfig"
  pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/completionConfig"
  pattern: "projects/{project}/locations/{location}/collections/{collection}/engines/{engine}/completionConfig"
};
option (google.api.resource_definition) = {
  type: "healthcare.googleapis.com/FhirStore"
  pattern: "projects/{project}/locations/{location}/datasets/{dataset}/fhirStores/{fhir_store}"
};
option (google.api.resource_definition) = {
  type: "healthcare.googleapis.com/FhirResource"
  pattern: "projects/{project}/locations/{location}/datasets/{dataset}/fhirStores/{fhir_store}/fhir/{resource_type}/{fhir_resource_id}"
};

// The industry vertical associated with the
// [DataStore][google.cloud.discoveryengine.v1beta.DataStore].
enum IndustryVertical {
  // Value used when unset.
  INDUSTRY_VERTICAL_UNSPECIFIED = 0;

  // The generic vertical for documents that are not specific to any industry
  // vertical.
  GENERIC = 1;

  // The media industry vertical.
  MEDIA = 2;

  // The healthcare FHIR vertical.
  HEALTHCARE_FHIR = 7;
}

// The type of solution.
enum SolutionType {
  // Default value.
  SOLUTION_TYPE_UNSPECIFIED = 0;

  // Used for Recommendations AI.
  SOLUTION_TYPE_RECOMMENDATION = 1;

  // Used for Discovery Search.
  SOLUTION_TYPE_SEARCH = 2;

  // Used for use cases related to the Generative AI agent.
  SOLUTION_TYPE_CHAT = 3;

  // Used for use cases related to the Generative Chat agent.
  // It's used for Generative chat engine only, the associated data stores
  // must enrolled with `SOLUTION_TYPE_CHAT` solution.
  SOLUTION_TYPE_GENERATIVE_CHAT = 4;
}

// Defines a further subdivision of `SolutionType`.
// Specifically applies to
// [SOLUTION_TYPE_SEARCH][google.cloud.discoveryengine.v1beta.SolutionType.SOLUTION_TYPE_SEARCH].
enum SearchUseCase {
  // Value used when unset. Will not occur in CSS.
  SEARCH_USE_CASE_UNSPECIFIED = 0;

  // Search use case. Expects the traffic has a non-empty
  // [query][google.cloud.discoveryengine.v1beta.SearchRequest.query].
  SEARCH_USE_CASE_SEARCH = 1;

  // Browse use case. Expects the traffic has an empty
  // [query][google.cloud.discoveryengine.v1beta.SearchRequest.query].
  SEARCH_USE_CASE_BROWSE = 2;
}

// Tiers of search features. Different tiers might have different
// pricing. To learn more, check the pricing documentation.
enum SearchTier {
  // Default value when the enum is unspecified. This is invalid to use.
  SEARCH_TIER_UNSPECIFIED = 0;

  // Standard tier.
  SEARCH_TIER_STANDARD = 1;

  // Enterprise tier.
  SEARCH_TIER_ENTERPRISE = 2;
}

// Add-on that provides additional functionality for search.
enum SearchAddOn {
  // Default value when the enum is unspecified. This is invalid to use.
  SEARCH_ADD_ON_UNSPECIFIED = 0;

  // Large language model add-on.
  SEARCH_ADD_ON_LLM = 1;
}

// A floating point interval.
message Interval {
  // The lower bound of the interval. If neither of the min fields are
  // set, then the lower bound is negative infinity.
  //
  // This field must be not larger than max.
  // Otherwise, an `INVALID_ARGUMENT` error is returned.
  oneof min {
    // Inclusive lower bound.
    double minimum = 1;

    // Exclusive lower bound.
    double exclusive_minimum = 2;
  }

  // The upper bound of the interval. If neither of the max fields are
  // set, then the upper bound is positive infinity.
  //
  // This field must be not smaller than min.
  // Otherwise, an `INVALID_ARGUMENT` error is returned.
  oneof max {
    // Inclusive upper bound.
    double maximum = 3;

    // Exclusive upper bound.
    double exclusive_maximum = 4;
  }
}

// A custom attribute that is not explicitly modeled in a resource, e.g.
// [UserEvent][google.cloud.discoveryengine.v1beta.UserEvent].
message CustomAttribute {
  // The textual values of this custom attribute. For example, `["yellow",
  // "green"]` when the key is "color".
  //
  // Empty string is not allowed. Otherwise, an `INVALID_ARGUMENT` error is
  // returned.
  //
  // Exactly one of
  // [CustomAttribute.text][google.cloud.discoveryengine.v1beta.CustomAttribute.text]
  // or
  // [CustomAttribute.numbers][google.cloud.discoveryengine.v1beta.CustomAttribute.numbers]
  // should be set. Otherwise, an `INVALID_ARGUMENT` error is returned.
  repeated string text = 1;

  // The numerical values of this custom attribute. For example, `[2.3, 15.4]`
  // when the key is "lengths_cm".
  //
  // Exactly one of
  // [CustomAttribute.text][google.cloud.discoveryengine.v1beta.CustomAttribute.text]
  // or
  // [CustomAttribute.numbers][google.cloud.discoveryengine.v1beta.CustomAttribute.numbers]
  // should be set. Otherwise, an `INVALID_ARGUMENT` error is returned.
  repeated double numbers = 2;
}

// Information of an end user.
message UserInfo {
  // Highly recommended for logged-in users. Unique identifier for logged-in
  // user, such as a user name. Don't set for anonymous users.
  //
  // Always use a hashed value for this ID.
  //
  // Don't set the field to the same fixed ID for different users. This mixes
  // the event history of those users together, which results in degraded
  // model quality.
  //
  // The field must be a UTF-8 encoded string with a length limit of 128
  // characters. Otherwise, an `INVALID_ARGUMENT` error is returned.
  string user_id = 1;

  // User agent as included in the HTTP header.
  //
  // The field must be a UTF-8 encoded string with a length limit of 1,000
  // characters. Otherwise, an `INVALID_ARGUMENT` error is returned.
  //
  // This should not be set when using the client side event reporting with
  // GTM or JavaScript tag in
  // [UserEventService.CollectUserEvent][google.cloud.discoveryengine.v1beta.UserEventService.CollectUserEvent]
  // or if
  // [UserEvent.direct_user_request][google.cloud.discoveryengine.v1beta.UserEvent.direct_user_request]
  // is set.
  string user_agent = 2;
}

// Defines embedding config, used for bring your own embeddings feature.
message EmbeddingConfig {
  // Full field path in the schema mapped as embedding field.
  string field_path = 1;
}

// Double list.
message DoubleList {
  // Double values.
  repeated double values = 1;
}
