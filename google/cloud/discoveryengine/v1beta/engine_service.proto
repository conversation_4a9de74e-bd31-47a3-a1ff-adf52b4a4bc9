// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1beta/engine.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "EngineServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// Service for managing [Engine][google.cloud.discoveryengine.v1beta.Engine]
// configuration.
service EngineService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a [Engine][google.cloud.discoveryengine.v1beta.Engine].
  rpc CreateEngine(CreateEngineRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/collections/*}/engines"
      body: "engine"
    };
    option (google.api.method_signature) = "parent,engine,engine_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.Engine"
      metadata_type: "google.cloud.discoveryengine.v1beta.CreateEngineMetadata"
    };
  }

  // Deletes a [Engine][google.cloud.discoveryengine.v1beta.Engine].
  rpc DeleteEngine(DeleteEngineRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/collections/*/engines/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.discoveryengine.v1beta.DeleteEngineMetadata"
    };
  }

  // Updates an [Engine][google.cloud.discoveryengine.v1beta.Engine]
  rpc UpdateEngine(UpdateEngineRequest) returns (Engine) {
    option (google.api.http) = {
      patch: "/v1beta/{engine.name=projects/*/locations/*/collections/*/engines/*}"
      body: "engine"
    };
    option (google.api.method_signature) = "engine,update_mask";
  }

  // Gets a [Engine][google.cloud.discoveryengine.v1beta.Engine].
  rpc GetEngine(GetEngineRequest) returns (Engine) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/collections/*/engines/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all the [Engine][google.cloud.discoveryengine.v1beta.Engine]s
  // associated with the project.
  rpc ListEngines(ListEnginesRequest) returns (ListEnginesResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/collections/*}/engines"
    };
    option (google.api.method_signature) = "parent";
  }

  // Pauses the training of an existing engine. Only applicable if
  // [SolutionType][google.cloud.discoveryengine.v1beta.SolutionType] is
  // [SOLUTION_TYPE_RECOMMENDATION][google.cloud.discoveryengine.v1beta.SolutionType.SOLUTION_TYPE_RECOMMENDATION].
  rpc PauseEngine(PauseEngineRequest) returns (Engine) {
    option (google.api.http) = {
      post: "/v1beta/{name=projects/*/locations/*/collections/*/engines/*}:pause"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Resumes the training of an existing engine. Only applicable if
  // [SolutionType][google.cloud.discoveryengine.v1beta.SolutionType] is
  // [SOLUTION_TYPE_RECOMMENDATION][google.cloud.discoveryengine.v1beta.SolutionType.SOLUTION_TYPE_RECOMMENDATION].
  rpc ResumeEngine(ResumeEngineRequest) returns (Engine) {
    option (google.api.http) = {
      post: "/v1beta/{name=projects/*/locations/*/collections/*/engines/*}:resume"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Tunes an existing engine. Only applicable if
  // [SolutionType][google.cloud.discoveryengine.v1beta.SolutionType] is
  // [SOLUTION_TYPE_RECOMMENDATION][google.cloud.discoveryengine.v1beta.SolutionType.SOLUTION_TYPE_RECOMMENDATION].
  rpc TuneEngine(TuneEngineRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{name=projects/*/locations/*/collections/*/engines/*}:tune"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "TuneEngineResponse"
      metadata_type: "TuneEngineMetadata"
    };
  }
}

// Request for
// [EngineService.CreateEngine][google.cloud.discoveryengine.v1beta.EngineService.CreateEngine]
// method.
message CreateEngineRequest {
  // Required. The parent resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Collection"
    }
  ];

  // Required. The [Engine][google.cloud.discoveryengine.v1beta.Engine] to
  // create.
  Engine engine = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the
  // [Engine][google.cloud.discoveryengine.v1beta.Engine], which will become the
  // final component of the
  // [Engine][google.cloud.discoveryengine.v1beta.Engine]'s resource name.
  //
  // This field must conform to [RFC-1034](https://tools.ietf.org/html/rfc1034)
  // standard with a length limit of 63 characters. Otherwise, an
  // INVALID_ARGUMENT error is returned.
  string engine_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Metadata related to the progress of the
// [EngineService.CreateEngine][google.cloud.discoveryengine.v1beta.EngineService.CreateEngine]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message CreateEngineMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Request message for
// [EngineService.DeleteEngine][google.cloud.discoveryengine.v1beta.EngineService.DeleteEngine]
// method.
message DeleteEngineRequest {
  // Required. Full resource name of
  // [Engine][google.cloud.discoveryengine.v1beta.Engine], such as
  // `projects/{project}/locations/{location}/collections/{collection_id}/engines/{engine_id}`.
  //
  // If the caller does not have permission to delete the
  // [Engine][google.cloud.discoveryengine.v1beta.Engine], regardless of whether
  // or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the [Engine][google.cloud.discoveryengine.v1beta.Engine] to delete does
  // not exist, a NOT_FOUND error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Engine"
    }
  ];
}

// Metadata related to the progress of the
// [EngineService.DeleteEngine][google.cloud.discoveryengine.v1beta.EngineService.DeleteEngine]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message DeleteEngineMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Request message for
// [EngineService.GetEngine][google.cloud.discoveryengine.v1beta.EngineService.GetEngine]
// method.
message GetEngineRequest {
  // Required. Full resource name of
  // [Engine][google.cloud.discoveryengine.v1beta.Engine], such as
  // `projects/{project}/locations/{location}/collections/{collection_id}/engines/{engine_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Engine"
    }
  ];
}

// Request message for
// [EngineService.ListEngines][google.cloud.discoveryengine.v1beta.EngineService.ListEngines]
// method.
message ListEnginesRequest {
  // Required. The parent resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection_id}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Collection"
    }
  ];

  // Optional. Not supported.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Not supported.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter by solution type. For example:
  // solution_type=SOLUTION_TYPE_SEARCH
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [EngineService.ListEngines][google.cloud.discoveryengine.v1beta.EngineService.ListEngines]
// method.
message ListEnginesResponse {
  // All the customer's [Engine][google.cloud.discoveryengine.v1beta.Engine]s.
  repeated Engine engines = 1;

  // Not supported.
  string next_page_token = 2;
}

// Request message for
// [EngineService.UpdateEngine][google.cloud.discoveryengine.v1beta.EngineService.UpdateEngine]
// method.
message UpdateEngineRequest {
  // Required. The [Engine][google.cloud.discoveryengine.v1beta.Engine] to
  // update.
  //
  // If the caller does not have permission to update the
  // [Engine][google.cloud.discoveryengine.v1beta.Engine], regardless of whether
  // or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the [Engine][google.cloud.discoveryengine.v1beta.Engine] to update does
  // not exist, a NOT_FOUND error is returned.
  Engine engine = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates which fields in the provided
  // [Engine][google.cloud.discoveryengine.v1beta.Engine] to update.
  //
  // If an unsupported or unknown field is provided, an INVALID_ARGUMENT error
  // is returned.
  google.protobuf.FieldMask update_mask = 2;
}

// Request for pausing training of an engine.
message PauseEngineRequest {
  // Required. The name of the engine to pause.
  // Format:
  // `projects/{project}/locations/{location}/collections/{collection_id}/engines/{engine_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Engine"
    }
  ];
}

// Request for resuming training of an engine.
message ResumeEngineRequest {
  // Required. The name of the engine to resume.
  // Format:
  // `projects/{project}/locations/{location}/collections/{collection_id}/engines/{engine_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Engine"
    }
  ];
}

// Request to manually start a tuning process now (instead of waiting for
// the periodically scheduled tuning to happen).
message TuneEngineRequest {
  // Required. The resource name of the engine to tune.
  // Format:
  // `projects/{project}/locations/{location}/collections/{collection_id}/engines/{engine_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Engine"
    }
  ];
}

// Metadata associated with a tune operation.
message TuneEngineMetadata {
  // Required. The resource name of the engine that this tune applies to.
  // Format:
  // `projects/{project}/locations/{location}/collections/{collection_id}/engines/{engine_id}`
  string engine = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Engine"
    }
  ];
}

// Response associated with a tune operation.
message TuneEngineResponse {}
