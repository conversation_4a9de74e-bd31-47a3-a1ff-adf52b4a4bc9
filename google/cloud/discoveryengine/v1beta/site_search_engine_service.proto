// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1beta/site_search_engine.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "SiteSearchEngineServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// Service for managing site search related resources.
service SiteSearchEngineService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets the
  // [SiteSearchEngine][google.cloud.discoveryengine.v1beta.SiteSearchEngine].
  rpc GetSiteSearchEngine(GetSiteSearchEngineRequest)
      returns (SiteSearchEngine) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/dataStores/*/siteSearchEngine}"
      additional_bindings {
        get: "/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite].
  rpc CreateTargetSite(CreateTargetSiteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/dataStores/*/siteSearchEngine}/targetSites"
      body: "target_site"
      additional_bindings {
        post: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}/targetSites"
        body: "target_site"
      }
    };
    option (google.api.method_signature) = "parent,target_site";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.TargetSite"
      metadata_type: "google.cloud.discoveryengine.v1beta.CreateTargetSiteMetadata"
    };
  }

  // Creates [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite] in a
  // batch.
  rpc BatchCreateTargetSites(BatchCreateTargetSitesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/dataStores/*/siteSearchEngine}/targetSites:batchCreate"
      body: "*"
      additional_bindings {
        post: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}/targetSites:batchCreate"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.BatchCreateTargetSitesResponse"
      metadata_type: "google.cloud.discoveryengine.v1beta.BatchCreateTargetSiteMetadata"
    };
  }

  // Gets a [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite].
  rpc GetTargetSite(GetTargetSiteRequest) returns (TargetSite) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/dataStores/*/siteSearchEngine/targetSites/*}"
      additional_bindings {
        get: "/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/targetSites/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite].
  rpc UpdateTargetSite(UpdateTargetSiteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{target_site.name=projects/*/locations/*/dataStores/*/siteSearchEngine/targetSites/*}"
      body: "target_site"
      additional_bindings {
        patch: "/v1beta/{target_site.name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/targetSites/*}"
        body: "target_site"
      }
    };
    option (google.api.method_signature) = "target_site";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.TargetSite"
      metadata_type: "google.cloud.discoveryengine.v1beta.UpdateTargetSiteMetadata"
    };
  }

  // Deletes a [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite].
  rpc DeleteTargetSite(DeleteTargetSiteRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/dataStores/*/siteSearchEngine/targetSites/*}"
      additional_bindings {
        delete: "/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/targetSites/*}"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.discoveryengine.v1beta.DeleteTargetSiteMetadata"
    };
  }

  // Gets a list of
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite]s.
  rpc ListTargetSites(ListTargetSitesRequest)
      returns (ListTargetSitesResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/dataStores/*/siteSearchEngine}/targetSites"
      additional_bindings {
        get: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}/targetSites"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap].
  rpc CreateSitemap(CreateSitemapRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/dataStores/*/siteSearchEngine}/sitemaps"
      body: "sitemap"
      additional_bindings {
        post: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}/sitemaps"
        body: "sitemap"
      }
    };
    option (google.api.method_signature) = "parent,sitemap";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.Sitemap"
      metadata_type: "google.cloud.discoveryengine.v1beta.CreateSitemapMetadata"
    };
  }

  // Deletes a [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap].
  rpc DeleteSitemap(DeleteSitemapRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/dataStores/*/siteSearchEngine/sitemaps/*}"
      additional_bindings {
        delete: "/v1beta/{name=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/sitemaps/*}"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.discoveryengine.v1beta.DeleteSitemapMetadata"
    };
  }

  // Fetch [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap]s in a
  // [DataStore][google.cloud.discoveryengine.v1beta.DataStore].
  rpc FetchSitemaps(FetchSitemapsRequest) returns (FetchSitemapsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/dataStores/*/siteSearchEngine}/sitemaps:fetch"
    };
    option (google.api.method_signature) = "parent";
  }

  // Upgrade from basic site search to advanced site search.
  rpc EnableAdvancedSiteSearch(EnableAdvancedSiteSearchRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{site_search_engine=projects/*/locations/*/dataStores/*/siteSearchEngine}:enableAdvancedSiteSearch"
      body: "*"
      additional_bindings {
        post: "/v1beta/{site_search_engine=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}:enableAdvancedSiteSearch"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.EnableAdvancedSiteSearchResponse"
      metadata_type: "google.cloud.discoveryengine.v1beta.EnableAdvancedSiteSearchMetadata"
    };
  }

  // Downgrade from advanced site search to basic site search.
  rpc DisableAdvancedSiteSearch(DisableAdvancedSiteSearchRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{site_search_engine=projects/*/locations/*/dataStores/*/siteSearchEngine}:disableAdvancedSiteSearch"
      body: "*"
      additional_bindings {
        post: "/v1beta/{site_search_engine=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}:disableAdvancedSiteSearch"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.DisableAdvancedSiteSearchResponse"
      metadata_type: "google.cloud.discoveryengine.v1beta.DisableAdvancedSiteSearchMetadata"
    };
  }

  // Request on-demand recrawl for a list of URIs.
  rpc RecrawlUris(RecrawlUrisRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{site_search_engine=projects/*/locations/*/dataStores/*/siteSearchEngine}:recrawlUris"
      body: "*"
      additional_bindings {
        post: "/v1beta/{site_search_engine=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}:recrawlUris"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.RecrawlUrisResponse"
      metadata_type: "google.cloud.discoveryengine.v1beta.RecrawlUrisMetadata"
    };
  }

  // Verify target sites' ownership and validity.
  // This API sends all the target sites under site search engine for
  // verification.
  rpc BatchVerifyTargetSites(BatchVerifyTargetSitesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}:batchVerifyTargetSites"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.BatchVerifyTargetSitesResponse"
      metadata_type: "google.cloud.discoveryengine.v1beta.BatchVerifyTargetSitesMetadata"
    };
  }

  // Returns list of target sites with its domain verification status.
  // This method can only be called under data store with BASIC_SITE_SEARCH
  // state at the moment.
  rpc FetchDomainVerificationStatus(FetchDomainVerificationStatusRequest)
      returns (FetchDomainVerificationStatusResponse) {
    option (google.api.http) = {
      get: "/v1beta/{site_search_engine=projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine}:fetchDomainVerificationStatus"
    };
  }
}

// Request message for
// [SiteSearchEngineService.GetSiteSearchEngine][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.GetSiteSearchEngine]
// method.
message GetSiteSearchEngineRequest {
  // Required. Resource name of
  // [SiteSearchEngine][google.cloud.discoveryengine.v1beta.SiteSearchEngine],
  // such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.
  //
  // If the caller does not have permission to access the [SiteSearchEngine],
  // regardless of whether or not it exists, a PERMISSION_DENIED error is
  // returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];
}

// Request message for
// [SiteSearchEngineService.CreateTargetSite][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.CreateTargetSite]
// method.
message CreateTargetSiteRequest {
  // Required. Parent resource name of
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite], such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];

  // Required. The [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite]
  // to create.
  TargetSite target_site = 2 [(google.api.field_behavior) = REQUIRED];
}

// Metadata related to the progress of the
// [SiteSearchEngineService.CreateTargetSite][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.CreateTargetSite]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message CreateTargetSiteMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Request message for
// [SiteSearchEngineService.BatchCreateTargetSites][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.BatchCreateTargetSites]
// method.
message BatchCreateTargetSitesRequest {
  // Required. The parent resource shared by all TargetSites being created.
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.
  // The parent field in the CreateBookRequest messages must either be empty or
  // match this field.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];

  // Required. The request message specifying the resources to create.
  // A maximum of 20 TargetSites can be created in a batch.
  repeated CreateTargetSiteRequest requests = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [SiteSearchEngineService.GetTargetSite][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.GetTargetSite]
// method.
message GetTargetSiteRequest {
  // Required. Full resource name of
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite], such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}`.
  //
  // If the caller does not have permission to access the
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite], regardless of
  // whether or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the requested
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite] does not
  // exist, a NOT_FOUND error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/TargetSite"
    }
  ];
}

// Request message for
// [SiteSearchEngineService.UpdateTargetSite][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.UpdateTargetSite]
// method.
message UpdateTargetSiteRequest {
  // Required. The target site to update.
  // If the caller does not have permission to update the
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite], regardless of
  // whether or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite] to
  // update does not exist, a NOT_FOUND error is returned.
  TargetSite target_site = 1 [(google.api.field_behavior) = REQUIRED];
}

// Metadata related to the progress of the
// [SiteSearchEngineService.UpdateTargetSite][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.UpdateTargetSite]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message UpdateTargetSiteMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Request message for
// [SiteSearchEngineService.DeleteTargetSite][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.DeleteTargetSite]
// method.
message DeleteTargetSiteRequest {
  // Required. Full resource name of
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite], such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/targetSites/{target_site}`.
  //
  // If the caller does not have permission to access the
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite], regardless of
  // whether or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the requested
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite] does not
  // exist, a NOT_FOUND error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/TargetSite"
    }
  ];
}

// Metadata related to the progress of the
// [SiteSearchEngineService.DeleteTargetSite][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.DeleteTargetSite]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message DeleteTargetSiteMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Request message for
// [SiteSearchEngineService.ListTargetSites][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.ListTargetSites]
// method.
message ListTargetSitesRequest {
  // Required. The parent site search engine resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.
  //
  // If the caller does not have permission to list
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite]s under this
  // site search engine, regardless of whether or not this branch exists, a
  // PERMISSION_DENIED error is returned.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];

  // Requested page size. Server may return fewer items than requested. If
  // unspecified, server will pick an appropriate default. The maximum value is
  // 1000; values above 1000 will be coerced to 1000.
  //
  // If this field is negative, an INVALID_ARGUMENT error is returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListTargetSites` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListTargetSites`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for
// [SiteSearchEngineService.ListTargetSites][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.ListTargetSites]
// method.
message ListTargetSitesResponse {
  // List of TargetSites.
  repeated TargetSite target_sites = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // The total number of items matching the request.
  // This will always be populated in the response.
  int32 total_size = 3;
}

// Metadata related to the progress of the
// [SiteSearchEngineService.BatchCreateTargetSites][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.BatchCreateTargetSites]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message BatchCreateTargetSiteMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Response message for
// [SiteSearchEngineService.BatchCreateTargetSites][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.BatchCreateTargetSites]
// method.
message BatchCreateTargetSitesResponse {
  // TargetSites created.
  repeated TargetSite target_sites = 1;
}

// Request message for
// [SiteSearchEngineService.CreateSitemap][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.CreateSitemap]
// method.
message CreateSitemapRequest {
  // Required. Parent resource name of the
  // [SiteSearchEngine][google.cloud.discoveryengine.v1beta.SiteSearchEngine],
  // such as
  // `projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];

  // Required. The [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap] to
  // create.
  Sitemap sitemap = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [SiteSearchEngineService.DeleteSitemap][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.DeleteSitemap]
// method.
message DeleteSitemapRequest {
  // Required. Full resource name of
  // [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap], such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine/sitemaps/{sitemap}`.
  //
  // If the caller does not have permission to access the
  // [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap], regardless of
  // whether or not it exists, a PERMISSION_DENIED error is returned.
  //
  // If the requested [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap]
  // does not exist, a NOT_FOUND error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Sitemap"
    }
  ];
}

// Request message for
// [SiteSearchEngineService.FetchSitemaps][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.FetchSitemaps]
// method.
message FetchSitemapsRequest {
  // Matcher for the [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap]s by
  // their uris.
  message UrisMatcher {
    // The [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap] uris.
    repeated string uris = 1;
  }

  // Matcher for the [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap]s.
  // Currently only supports uris matcher.
  message Matcher {
    // Matcher for the [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap]s.
    oneof matcher {
      // Matcher by sitemap URIs.
      UrisMatcher uris_matcher = 1;
    }
  }

  // Required. Parent resource name of the
  // [SiteSearchEngine][google.cloud.discoveryengine.v1beta.SiteSearchEngine],
  // such as
  // `projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];

  // Optional. If specified, fetches the matching
  // [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap]s. If not specified,
  // fetches all [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap]s in the
  // [DataStore][google.cloud.discoveryengine.v1beta.DataStore].
  Matcher matcher = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Metadata related to the progress of the
// [SiteSearchEngineService.CreateSitemap][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.CreateSitemap]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message CreateSitemapMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Metadata related to the progress of the
// [SiteSearchEngineService.DeleteSitemap][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.DeleteSitemap]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message DeleteSitemapMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Response message for
// [SiteSearchEngineService.FetchSitemaps][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.FetchSitemaps]
// method.
message FetchSitemapsResponse {
  // Contains a [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap] and its
  // metadata.
  message SitemapMetadata {
    // The [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap].
    Sitemap sitemap = 1;
  }

  // List of [Sitemap][google.cloud.discoveryengine.v1beta.Sitemap]s fetched.
  repeated SitemapMetadata sitemaps_metadata = 1;
}

// Request message for
// [SiteSearchEngineService.EnableAdvancedSiteSearch][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.EnableAdvancedSiteSearch]
// method.
message EnableAdvancedSiteSearchRequest {
  // Required. Full resource name of the
  // [SiteSearchEngine][google.cloud.discoveryengine.v1beta.SiteSearchEngine],
  // such as
  // `projects/{project}/locations/{location}/dataStores/{data_store_id}/siteSearchEngine`.
  string site_search_engine = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];
}

// Response message for
// [SiteSearchEngineService.EnableAdvancedSiteSearch][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.EnableAdvancedSiteSearch]
// method.
message EnableAdvancedSiteSearchResponse {}

// Metadata related to the progress of the
// [SiteSearchEngineService.EnableAdvancedSiteSearch][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.EnableAdvancedSiteSearch]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message EnableAdvancedSiteSearchMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Request message for
// [SiteSearchEngineService.DisableAdvancedSiteSearch][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.DisableAdvancedSiteSearch]
// method.
message DisableAdvancedSiteSearchRequest {
  // Required. Full resource name of the
  // [SiteSearchEngine][google.cloud.discoveryengine.v1beta.SiteSearchEngine],
  // such as
  // `projects/{project}/locations/{location}/dataStores/{data_store_id}/siteSearchEngine`.
  string site_search_engine = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];
}

// Response message for
// [SiteSearchEngineService.DisableAdvancedSiteSearch][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.DisableAdvancedSiteSearch]
// method.
message DisableAdvancedSiteSearchResponse {}

// Metadata related to the progress of the
// [SiteSearchEngineService.DisableAdvancedSiteSearch][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.DisableAdvancedSiteSearch]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message DisableAdvancedSiteSearchMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Request message for
// [SiteSearchEngineService.RecrawlUris][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.RecrawlUris]
// method.
message RecrawlUrisRequest {
  // Required. Full resource name of the
  // [SiteSearchEngine][google.cloud.discoveryengine.v1beta.SiteSearchEngine],
  // such as
  // `projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine`.
  string site_search_engine = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];

  // Required. List of URIs to crawl. At most 10K URIs are supported, otherwise
  // an INVALID_ARGUMENT error is thrown. Each URI should match at least one
  // [TargetSite][google.cloud.discoveryengine.v1beta.TargetSite] in
  // `site_search_engine`.
  repeated string uris = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Full resource name of the [SiteCredential][], such as
  // `projects/*/locations/*/collections/*/dataStores/*/siteSearchEngine/siteCredentials/*`.
  // Only set to crawl private URIs.
  string site_credential = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [SiteSearchEngineService.RecrawlUris][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.RecrawlUris]
// method.
message RecrawlUrisResponse {
  // Details about why a particular URI failed to be crawled. Each FailureInfo
  // contains one FailureReason per CorpusType.
  message FailureInfo {
    // Details about why crawling failed for a particular CorpusType, e.g.,
    // DESKTOP and MOBILE crawling may fail for different reasons.
    message FailureReason {
      // CorpusType for the failed crawling operation.
      enum CorpusType {
        // Default value.
        CORPUS_TYPE_UNSPECIFIED = 0;

        // Denotes a crawling attempt for the desktop version of a page.
        DESKTOP = 1;

        // Denotes a crawling attempt for the mobile version of a page.
        MOBILE = 2;
      }

      // DESKTOP, MOBILE, or CORPUS_TYPE_UNSPECIFIED.
      CorpusType corpus_type = 1;

      // Reason why the URI was not crawled.
      string error_message = 2;
    }

    // URI that failed to be crawled.
    string uri = 1;

    // List of failure reasons by corpus type (e.g. desktop, mobile).
    repeated FailureReason failure_reasons = 2;
  }

  // Details for a sample of up to 10 `failed_uris`.
  repeated FailureInfo failure_samples = 1;

  // URIs that were not crawled before the LRO terminated.
  repeated string failed_uris = 2;
}

// Metadata related to the progress of the
// [SiteSearchEngineService.RecrawlUris][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.RecrawlUris]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message RecrawlUrisMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;

  // Unique URIs in the request that have invalid format. Sample limited to
  // 1000.
  repeated string invalid_uris = 3;

  // Total number of unique URIs in the request that have invalid format.
  int32 invalid_uris_count = 8;

  // Unique URIs in the request that don't match any TargetSite in the
  // DataStore, only match TargetSites that haven't been fully indexed, or match
  // a TargetSite with type EXCLUDE. Sample limited to 1000.
  repeated string uris_not_matching_target_sites = 9;

  // Total number of URIs that don't match any TargetSites.
  int32 uris_not_matching_target_sites_count = 10;

  // Total number of unique URIs in the request that are not in invalid_uris.
  int32 valid_uris_count = 4;

  // Total number of URIs that have been crawled so far.
  int32 success_count = 5;

  // Total number of URIs that have yet to be crawled.
  int32 pending_count = 6;

  // Total number of URIs that were rejected due to insufficient indexing
  // resources.
  int32 quota_exceeded_count = 7;
}

// Request message for
// [SiteSearchEngineService.BatchVerifyTargetSites][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.BatchVerifyTargetSites]
// method.
message BatchVerifyTargetSitesRequest {
  // Required. The parent resource shared by all TargetSites being verified.
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];
}

// Response message for
// [SiteSearchEngineService.BatchVerifyTargetSites][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.BatchVerifyTargetSites]
// method.
message BatchVerifyTargetSitesResponse {}

// Metadata related to the progress of the
// [SiteSearchEngineService.BatchVerifyTargetSites][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.BatchVerifyTargetSites]
// operation. This will be returned by the google.longrunning.Operation.metadata
// field.
message BatchVerifyTargetSitesMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Request message for
// [SiteSearchEngineService.FetchDomainVerificationStatus][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.FetchDomainVerificationStatus]
// method.
message FetchDomainVerificationStatusRequest {
  // Required. The site search engine resource under which we fetch all the
  // domain verification status.
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/siteSearchEngine`.
  string site_search_engine = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/SiteSearchEngine"
    }
  ];

  // Requested page size. Server may return fewer items than requested. If
  // unspecified, server will pick an appropriate default. The maximum value is
  // 1000; values above 1000 will be coerced to 1000.
  //
  // If this field is negative, an INVALID_ARGUMENT error is returned.
  int32 page_size = 2;

  // A page token, received from a previous `FetchDomainVerificationStatus`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `FetchDomainVerificationStatus` must match the call that provided the page
  // token.
  string page_token = 3;
}

// Response message for
// [SiteSearchEngineService.FetchDomainVerificationStatus][google.cloud.discoveryengine.v1beta.SiteSearchEngineService.FetchDomainVerificationStatus]
// method.
message FetchDomainVerificationStatusResponse {
  // List of TargetSites containing the site verification status.
  repeated TargetSite target_sites = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // The total number of items matching the request.
  // This will always be populated in the response.
  int32 total_size = 3;
}
