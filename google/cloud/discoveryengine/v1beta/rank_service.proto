// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "RankServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// Service for ranking text records.
service RankService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Ranks a list of text records based on the given input query.
  rpc Rank(RankRequest) returns (RankResponse) {
    option (google.api.http) = {
      post: "/v1beta/{ranking_config=projects/*/locations/*/rankingConfigs/*}:rank"
      body: "*"
    };
  }
}

// Record message for
// [RankService.Rank][google.cloud.discoveryengine.v1beta.RankService.Rank]
// method.
message RankingRecord {
  // The unique ID to represent the record.
  string id = 1;

  // The title of the record. Empty by default.
  // At least one of
  // [title][google.cloud.discoveryengine.v1beta.RankingRecord.title] or
  // [content][google.cloud.discoveryengine.v1beta.RankingRecord.content] should
  // be set otherwise an INVALID_ARGUMENT error is thrown.
  string title = 2;

  // The content of the record. Empty by default.
  // At least one of
  // [title][google.cloud.discoveryengine.v1beta.RankingRecord.title] or
  // [content][google.cloud.discoveryengine.v1beta.RankingRecord.content] should
  // be set otherwise an INVALID_ARGUMENT error is thrown.
  string content = 3;

  // The score of this record based on the given query and selected model.
  float score = 4;
}

// Request message for
// [RankService.Rank][google.cloud.discoveryengine.v1beta.RankService.Rank]
// method.
message RankRequest {
  // Required. The resource name of the rank service config, such as
  // `projects/{project_num}/locations/{location}/rankingConfigs/default_ranking_config`.
  string ranking_config = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/RankingConfig"
    }
  ];

  // The identifier of the model to use. It is one of:
  //
  // * `semantic-ranker-512@latest`: Semantic ranking model with maxiumn input
  // token size 512.
  //
  // It is set to `semantic-ranker-512@latest` by default if unspecified.
  string model = 2;

  // The number of results to return. If this is unset or no bigger than zero,
  // returns all results.
  int32 top_n = 3;

  // The query to use.
  string query = 4;

  // Required. A list of records to rank. At most 200 records to rank.
  repeated RankingRecord records = 5 [(google.api.field_behavior) = REQUIRED];

  // If true, the response will contain only record ID and score. By default, it
  // is false, the response will contain record details.
  bool ignore_record_details_in_response = 6;

  // The user labels applied to a resource must meet the following requirements:
  //
  // * Each resource can have multiple labels, up to a maximum of 64.
  // * Each label must be a key-value pair.
  // * Keys have a minimum length of 1 character and a maximum length of 63
  //   characters and cannot be empty. Values can be empty and have a maximum
  //   length of 63 characters.
  // * Keys and values can contain only lowercase letters, numeric characters,
  //   underscores, and dashes. All characters must use UTF-8 encoding, and
  //   international characters are allowed.
  // * The key portion of a label must be unique. However, you can use the same
  //   key with multiple resources.
  // * Keys must start with a lowercase letter or international character.
  //
  // See [Google Cloud
  // Document](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements)
  // for more details.
  map<string, string> user_labels = 7;
}

// Response message for
// [RankService.Rank][google.cloud.discoveryengine.v1beta.RankService.Rank]
// method.
message RankResponse {
  // A list of records sorted by descending score.
  repeated RankingRecord records = 5;
}
