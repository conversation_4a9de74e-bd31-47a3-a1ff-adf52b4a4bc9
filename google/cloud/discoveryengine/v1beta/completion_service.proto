// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1beta/common.proto";
import "google/cloud/discoveryengine/v1beta/document.proto";
import "google/cloud/discoveryengine/v1beta/import_config.proto";
import "google/cloud/discoveryengine/v1beta/purge_config.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Beta";
option go_package = "cloud.google.com/go/discoveryengine/apiv1beta/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "CompletionServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1beta";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1beta";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1beta";

// Service for Auto-Completion.
service CompletionService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Completes the specified user input with keyword suggestions.
  rpc CompleteQuery(CompleteQueryRequest) returns (CompleteQueryResponse) {
    option (google.api.http) = {
      get: "/v1beta/{data_store=projects/*/locations/*/dataStores/*}:completeQuery"
      additional_bindings {
        get: "/v1beta/{data_store=projects/*/locations/*/collections/*/dataStores/*}:completeQuery"
      }
    };
  }

  // Completes the user input with advanced keyword suggestions.
  rpc AdvancedCompleteQuery(AdvancedCompleteQueryRequest)
      returns (AdvancedCompleteQueryResponse) {
    option (google.api.http) = {
      post: "/v1beta/{completion_config=projects/*/locations/*/dataStores/*/completionConfig}:completeQuery"
      body: "*"
      additional_bindings {
        post: "/v1beta/{completion_config=projects/*/locations/*/collections/*/dataStores/*/completionConfig}:completeQuery"
        body: "*"
      }
      additional_bindings {
        post: "/v1beta/{completion_config=projects/*/locations/*/collections/*/engines/*/completionConfig}:completeQuery"
        body: "*"
      }
    };
  }

  // Imports all
  // [SuggestionDenyListEntry][google.cloud.discoveryengine.v1beta.SuggestionDenyListEntry]
  // for a DataStore.
  rpc ImportSuggestionDenyListEntries(ImportSuggestionDenyListEntriesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*}/suggestionDenyListEntries:import"
      body: "*"
      additional_bindings {
        post: "/v1beta/{parent=projects/*/locations/*/dataStores/*}/suggestionDenyListEntries:import"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.ImportSuggestionDenyListEntriesResponse"
      metadata_type: "google.cloud.discoveryengine.v1beta.ImportSuggestionDenyListEntriesMetadata"
    };
  }

  // Permanently deletes all
  // [SuggestionDenyListEntry][google.cloud.discoveryengine.v1beta.SuggestionDenyListEntry]
  // for a DataStore.
  rpc PurgeSuggestionDenyListEntries(PurgeSuggestionDenyListEntriesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*}/suggestionDenyListEntries:purge"
      body: "*"
      additional_bindings {
        post: "/v1beta/{parent=projects/*/locations/*/dataStores/**}/suggestionDenyListEntries:purge"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.PurgeSuggestionDenyListEntriesResponse"
      metadata_type: "google.cloud.discoveryengine.v1beta.PurgeSuggestionDenyListEntriesMetadata"
    };
  }

  // Imports
  // [CompletionSuggestion][google.cloud.discoveryengine.v1beta.CompletionSuggestion]s
  // for a DataStore.
  rpc ImportCompletionSuggestions(ImportCompletionSuggestionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*}/completionSuggestions:import"
      body: "*"
      additional_bindings {
        post: "/v1beta/{parent=projects/*/locations/*/dataStores/*}/completionSuggestions:import"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.ImportCompletionSuggestionsResponse"
      metadata_type: "google.cloud.discoveryengine.v1beta.ImportCompletionSuggestionsMetadata"
    };
  }

  // Permanently deletes all
  // [CompletionSuggestion][google.cloud.discoveryengine.v1beta.CompletionSuggestion]s
  // for a DataStore.
  rpc PurgeCompletionSuggestions(PurgeCompletionSuggestionsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/collections/*/dataStores/*}/completionSuggestions:purge"
      body: "*"
      additional_bindings {
        post: "/v1beta/{parent=projects/*/locations/*/dataStores/*}/completionSuggestions:purge"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1beta.PurgeCompletionSuggestionsResponse"
      metadata_type: "google.cloud.discoveryengine.v1beta.PurgeCompletionSuggestionsMetadata"
    };
  }
}

// Request message for
// [CompletionService.CompleteQuery][google.cloud.discoveryengine.v1beta.CompletionService.CompleteQuery]
// method.
message CompleteQueryRequest {
  // Required. The parent data store resource name for which the completion is
  // performed, such as
  // `projects/*/locations/global/collections/default_collection/dataStores/default_data_store`.
  string data_store = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];

  // Required. The typeahead input used to fetch suggestions. Maximum length is
  // 128 characters.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Specifies the autocomplete data model. This overrides any model specified
  // in the Configuration > Autocomplete section of the Cloud console. Currently
  // supported values:
  //
  // * `document` - Using suggestions generated from user-imported documents.
  // * `search-history` - Using suggestions generated from the past history of
  // [SearchService.Search][google.cloud.discoveryengine.v1beta.SearchService.Search]
  // API calls. Do not use it when there is no traffic for Search API.
  // * `user-event` - Using suggestions generated from user-imported search
  // events.
  // * `document-completable` - Using suggestions taken directly from
  // user-imported document fields marked as completable.
  //
  // Default values:
  //
  // * `document` is the default model for regular dataStores.
  // * `search-history` is the default model for site search dataStores.
  string query_model = 3;

  // A unique identifier for tracking visitors. For example, this could be
  // implemented with an HTTP cookie, which should be able to uniquely identify
  // a visitor on a single device. This unique identifier should not change if
  // the visitor logs in or out of the website.
  //
  // This field should NOT have a fixed value such as `unknown_visitor`.
  //
  // This should be the same identifier as
  // [UserEvent.user_pseudo_id][google.cloud.discoveryengine.v1beta.UserEvent.user_pseudo_id]
  // and
  // [SearchRequest.user_pseudo_id][google.cloud.discoveryengine.v1beta.SearchRequest.user_pseudo_id].
  //
  // The field must be a UTF-8 encoded string with a length limit of 128
  // characters. Otherwise, an `INVALID_ARGUMENT` error is returned.
  string user_pseudo_id = 4;

  // Indicates if tail suggestions should be returned if there are no
  // suggestions that match the full query. Even if set to true, if there are
  // suggestions that match the full query, those are returned and no
  // tail suggestions are returned.
  bool include_tail_suggestions = 5;
}

// Response message for
// [CompletionService.CompleteQuery][google.cloud.discoveryengine.v1beta.CompletionService.CompleteQuery]
// method.
message CompleteQueryResponse {
  // Suggestions as search queries.
  message QuerySuggestion {
    // The suggestion for the query.
    string suggestion = 1;

    // The unique document field paths that serve as the source of this
    // suggestion if it was generated from completable fields.
    //
    // This field is only populated for the document-completable model.
    repeated string completable_field_paths = 2;
  }

  // Results of the matched query suggestions. The result list is ordered and
  // the first result is a top suggestion.
  repeated QuerySuggestion query_suggestions = 1;

  // True if the returned suggestions are all tail suggestions.
  //
  // For tail matching to be triggered, include_tail_suggestions in the request
  // must be true and there must be no suggestions that match the full query.
  bool tail_match_triggered = 2;
}

// Request message for
// [CompletionService.AdvancedCompleteQuery][google.cloud.discoveryengine.v1beta.CompletionService.AdvancedCompleteQuery]
// method.
// .
message AdvancedCompleteQueryRequest {
  // Specification to boost suggestions based on the condtion of the suggestion.
  message BoostSpec {
    // Boost applies to suggestions which match a condition.
    message ConditionBoostSpec {
      // An expression which specifies a boost condition. The syntax is the same
      // as [filter expression
      // syntax](https://cloud.google.com/generative-ai-app-builder/docs/filter-search-metadata#filter-expression-syntax).
      // Currently, the only supported condition is a list of BCP-47 lang codes.
      //
      // Example:
      //
      // * To boost suggestions in languages `en` or `fr`:
      // `(lang_code: ANY("en", "fr"))`
      string condition = 1;

      // Strength of the boost, which should be in [-1, 1].
      // Negative boost means demotion. Default is 0.0.
      //
      // Setting to 1.0 gives the suggestions a big promotion. However, it does
      // not necessarily mean that the top result will be a boosted suggestion.
      //
      // Setting to -1.0 gives the suggestions a big demotion. However, other
      // suggestions that are relevant might still be shown.
      //
      // Setting to 0.0 means no boost applied. The boosting condition is
      // ignored.
      float boost = 2;
    }

    // Condition boost specifications. If a suggestion matches multiple
    // conditions in the specifictions, boost values from these specifications
    // are all applied and combined in a non-linear way. Maximum number of
    // specifications is 20.
    //
    // Note: Currently only support language condition boost.
    repeated ConditionBoostSpec condition_boost_specs = 1;
  }

  // Suggestion type to return.
  enum SuggestionType {
    // Default value.
    SUGGESTION_TYPE_UNSPECIFIED = 0;

    // Returns query suggestions.
    QUERY = 1;

    // Returns people suggestions.
    PEOPLE = 2;

    // Returns content suggestions.
    CONTENT = 3;

    // Returns recent search suggestions.
    RECENT_SEARCH = 4;

    // Returns Google Workspace suggestions.
    GOOGLE_WORKSPACE = 5;
  }

  // Required. The completion_config of the parent dataStore or engine resource
  // name for which the completion is performed, such as
  // `projects/*/locations/global/collections/default_collection/dataStores/*/completionConfig`
  // `projects/*/locations/global/collections/default_collection/engines/*/completionConfig`.
  string completion_config = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/CompletionConfig"
    }
  ];

  // Required. The typeahead input used to fetch suggestions. Maximum length is
  // 128 characters.
  //
  // The query can not be empty for most of the suggestion types. If it is
  // empty, an `INVALID_ARGUMENT` error is returned. The exception is when the
  // suggestion_types contains only the type `RECENT_SEARCH`, the query can
  // be an empty string. The is called "zero prefix" feature, which returns
  // user's recently searched queries given the empty query.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Specifies the autocomplete data model. This overrides any model specified
  // in the Configuration > Autocomplete section of the Cloud console. Currently
  // supported values:
  //
  // * `document` - Using suggestions generated from user-imported documents.
  // * `search-history` - Using suggestions generated from the past history of
  // [SearchService.Search][google.cloud.discoveryengine.v1beta.SearchService.Search]
  // API calls. Do not use it when there is no traffic for Search API.
  // * `user-event` - Using suggestions generated from user-imported search
  // events.
  // * `document-completable` - Using suggestions taken directly from
  // user-imported document fields marked as completable.
  //
  // Default values:
  //
  // * `document` is the default model for regular dataStores.
  // * `search-history` is the default model for site search dataStores.
  string query_model = 3;

  // A unique identifier for tracking visitors. For example, this could be
  // implemented with an HTTP cookie, which should be able to uniquely identify
  // a visitor on a single device. This unique identifier should not change if
  // the visitor logs in or out of the website.
  //
  // This field should NOT have a fixed value such as `unknown_visitor`.
  //
  // This should be the same identifier as
  // [UserEvent.user_pseudo_id][google.cloud.discoveryengine.v1beta.UserEvent.user_pseudo_id]
  // and
  // [SearchRequest.user_pseudo_id][google.cloud.discoveryengine.v1beta.SearchRequest.user_pseudo_id].
  //
  // The field must be a UTF-8 encoded string with a length limit of 128
  string user_pseudo_id = 4;

  // Optional. Information about the end user.
  //
  // This should be the same identifier information as
  // [UserEvent.user_info][google.cloud.discoveryengine.v1beta.UserEvent.user_info]
  // and
  // [SearchRequest.user_info][google.cloud.discoveryengine.v1beta.SearchRequest.user_info].
  UserInfo user_info = 9 [(google.api.field_behavior) = OPTIONAL];

  // Indicates if tail suggestions should be returned if there are no
  // suggestions that match the full query. Even if set to true, if there are
  // suggestions that match the full query, those are returned and no
  // tail suggestions are returned.
  bool include_tail_suggestions = 5;

  // Optional. Specification to boost suggestions matching the condition.
  BoostSpec boost_spec = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Suggestion types to return. If empty or unspecified, query
  // suggestions are returned. Only one suggestion type is supported at the
  // moment.
  repeated SuggestionType suggestion_types = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// Response message for
// [CompletionService.AdvancedCompleteQuery][google.cloud.discoveryengine.v1beta.CompletionService.AdvancedCompleteQuery]
// method.
message AdvancedCompleteQueryResponse {
  // Suggestions as search queries.
  message QuerySuggestion {
    // The suggestion for the query.
    string suggestion = 1;

    // The unique document field paths that serve as the source of this
    // suggestion if it was generated from completable fields.
    //
    // This field is only populated for the document-completable model.
    repeated string completable_field_paths = 2;

    // The name of the dataStore that this suggestion belongs to.
    repeated string data_store = 3;
  }

  // Suggestions as people.
  message PersonSuggestion {
    // The type of the person based on the source.
    enum PersonType {
      // Default value.
      PERSON_TYPE_UNSPECIFIED = 0;

      // The suggestion is from a GOOGLE_IDENTITY source.
      CLOUD_IDENTITY = 1;

      // The suggestion is from a THIRD_PARTY_IDENTITY source.
      THIRD_PARTY_IDENTITY = 2;
    }

    // The suggestion for the query.
    string suggestion = 1;

    // The type of the person.
    PersonType person_type = 2;

    // The document data snippet in the suggestion. Only a subset of fields is
    // populated.
    Document document = 4;

    // The name of the dataStore that this suggestion belongs to.
    string data_store = 5 [(google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }];
  }

  // Suggestions as content.
  message ContentSuggestion {
    // The type of the content returned for content suggestions.
    enum ContentType {
      // Default value.
      CONTENT_TYPE_UNSPECIFIED = 0;

      // The suggestion is from a Google Workspace source.
      GOOGLE_WORKSPACE = 1;

      // The suggestion is from a third party source.
      THIRD_PARTY = 2;
    }

    // The suggestion for the query.
    string suggestion = 1;

    // The type of the content suggestion.
    ContentType content_type = 2;

    // The document data snippet in the suggestion. Only a subset of fields will
    // be populated.
    Document document = 4;

    // The name of the dataStore that this suggestion belongs to.
    string data_store = 5 [(google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }];
  }

  // Suggestions from recent search history.
  message RecentSearchSuggestion {
    // The suggestion for the query.
    string suggestion = 1;

    // The time when this recent rearch happened.
    google.protobuf.Timestamp recent_search_time = 2;
  }

  // Results of the matched query suggestions. The result list is ordered and
  // the first result is a top suggestion.
  repeated QuerySuggestion query_suggestions = 1;

  // True if the returned suggestions are all tail suggestions.
  //
  // For tail matching to be triggered, include_tail_suggestions in the request
  // must be true and there must be no suggestions that match the full query.
  bool tail_match_triggered = 2;

  // Results of the matched people suggestions. The result list is ordered and
  // the first result is the top suggestion.
  repeated PersonSuggestion people_suggestions = 3;

  // Results of the matched content suggestions. The result list is ordered and
  // the first result is the top suggestion.
  repeated ContentSuggestion content_suggestions = 4;

  // Results of the matched "recent search" suggestions. The result list is
  // ordered and the first result is the top suggestion.
  repeated RecentSearchSuggestion recent_search_suggestions = 5;
}
