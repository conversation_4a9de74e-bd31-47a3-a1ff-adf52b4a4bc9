# This build file includes a target for the Ruby wrapper library for
# google-cloud-discovery_engine.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for discoveryengine.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "discoveryengine_ruby_wrapper",
    srcs = ["//google/cloud/discoveryengine/v1:discoveryengine_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-discovery_engine",
        "ruby-cloud-wrapper-of=v1:1.1;v1beta:0.15",
    ],
    service_yaml = "//google/cloud/discoveryengine/v1:discoveryengine_v1.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-discoveryengine-ruby",
    deps = [
        ":discoveryengine_ruby_wrapper",
    ],
)