// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/grounding.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "GroundedGenerationServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Service for grounded generation.
service GroundedGenerationService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Performs a grounding check.
  rpc CheckGrounding(CheckGroundingRequest) returns (CheckGroundingResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{grounding_config=projects/*/locations/*/groundingConfigs/*}:check"
      body: "*"
    };
  }
}

// Specification for the grounding check.
message CheckGroundingSpec {
  // The threshold (in [0,1]) used for determining whether a fact must be
  // cited for a claim in the answer candidate. Choosing a higher threshold
  // will lead to fewer but very strong citations, while choosing a lower
  // threshold may lead to more but somewhat weaker citations. If unset, the
  // threshold will default to 0.6.
  optional double citation_threshold = 1;
}

// Request message for
// [GroundedGenerationService.CheckGrounding][google.cloud.discoveryengine.v1alpha.GroundedGenerationService.CheckGrounding]
// method.
message CheckGroundingRequest {
  // Required. The resource name of the grounding config, such as
  // `projects/*/locations/global/groundingConfigs/default_grounding_config`.
  string grounding_config = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/GroundingConfig"
    }
  ];

  // Answer candidate to check. Can have a maximum length of 1024 characters.
  string answer_candidate = 2;

  // List of facts for the grounding check.
  // We support up to 200 facts.
  repeated GroundingFact facts = 3;

  // Configuration of the grounding check.
  CheckGroundingSpec grounding_spec = 4;

  // The user labels applied to a resource must meet the following requirements:
  //
  // * Each resource can have multiple labels, up to a maximum of 64.
  // * Each label must be a key-value pair.
  // * Keys have a minimum length of 1 character and a maximum length of 63
  //   characters and cannot be empty. Values can be empty and have a maximum
  //   length of 63 characters.
  // * Keys and values can contain only lowercase letters, numeric characters,
  //   underscores, and dashes. All characters must use UTF-8 encoding, and
  //   international characters are allowed.
  // * The key portion of a label must be unique. However, you can use the same
  //   key with multiple resources.
  // * Keys must start with a lowercase letter or international character.
  //
  // See [Google Cloud
  // Document](https://cloud.google.com/resource-manager/docs/creating-managing-labels#requirements)
  // for more details.
  map<string, string> user_labels = 5;
}

// Response message for the
// [GroundedGenerationService.CheckGrounding][google.cloud.discoveryengine.v1alpha.GroundedGenerationService.CheckGrounding]
// method.
message CheckGroundingResponse {
  // Text and citation info for a claim in the answer candidate.
  message Claim {
    // Position indicating the start of the claim in the answer candidate,
    // measured in bytes.
    optional int32 start_pos = 1;

    // Position indicating the end of the claim in the answer candidate,
    // exclusive.
    optional int32 end_pos = 2;

    // Text for the claim in the answer candidate. Always provided regardless of
    // whether citations or anti-citations are found.
    string claim_text = 3;

    // A list of indices (into 'cited_chunks') specifying the citations
    // associated with the claim. For instance [1,3,4] means that
    // cited_chunks[1], cited_chunks[3], cited_chunks[4] are the facts cited
    // supporting for the claim. A citation to a fact indicates that the claim
    // is supported by the fact.
    repeated int32 citation_indices = 4;

    // Indicates that this claim required grounding check. When the system
    // decided this claim doesn't require attribution/grounding check, this
    // field will be set to false. In that case, no grounding check was done for
    // the claim and therefore
    // [citation_indices][google.cloud.discoveryengine.v1alpha.CheckGroundingResponse.Claim.citation_indices],
    // [anti_citation_indices][google.cloud.discoveryengine.v1alpha.CheckGroundingResponse.Claim.anti_citation_indices],
    // and
    // [score][google.cloud.discoveryengine.v1alpha.CheckGroundingResponse.Claim.score]
    // should not be returned.
    optional bool grounding_check_required = 6;
  }

  // The support score for the input answer candidate.
  // Higher the score, higher is the fraction of claims that are supported by
  // the provided facts. This is always set when a response is returned.
  optional float support_score = 1;

  // List of facts cited across all claims in the answer candidate.
  // These are derived from the facts supplied in the request.
  repeated FactChunk cited_chunks = 3;

  // Claim texts and citation info across all claims in the answer candidate.
  repeated Claim claims = 4;
}
