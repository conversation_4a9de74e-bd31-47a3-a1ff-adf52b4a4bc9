// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "ProjectProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Metadata and configurations for a Google Cloud project in the service.
message Project {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/Project"
    pattern: "projects/{project}"
  };

  // Metadata about the terms of service.
  message ServiceTerms {
    // The agreement states this terms of service.
    enum State {
      // The default value of the enum. This value is not actually used.
      STATE_UNSPECIFIED = 0;

      // The project has given consent to the terms of service.
      TERMS_ACCEPTED = 1;

      // The project is pending to review and accept the terms of service.
      TERMS_PENDING = 2;

      // The project has declined or revoked the agreement to terms of service.
      TERMS_DECLINED = 3;
    }

    // The unique identifier of this terms of service.
    // Available terms:
    //
    // * `GA_DATA_USE_TERMS`: [Terms for data
    // use](https://cloud.google.com/retail/data-use-terms). When using this as
    // `id`, the acceptable
    // [version][google.cloud.discoveryengine.v1alpha.Project.ServiceTerms.version]
    // to provide is `2022-11-23`.
    string id = 1;

    // The version string of the terms of service.
    // For acceptable values, see the comments for
    // [id][google.cloud.discoveryengine.v1alpha.Project.ServiceTerms.id] above.
    string version = 2;

    // Whether the project has accepted/rejected the service terms or it is
    // still pending.
    State state = 4;

    // The last time when the project agreed to the terms of service.
    google.protobuf.Timestamp accept_time = 5;

    // The last time when the project declined or revoked the agreement to terms
    // of service.
    google.protobuf.Timestamp decline_time = 6;
  }

  // Output only. Full resource name of the project, for example
  // `projects/{project_number}`.
  // Note that when making requests, project number and project id are both
  // acceptable, but the server will always respond in project number.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this project is created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp when this project is successfully provisioned.
  // Empty value means this project is still provisioning and is not ready for
  // use.
  google.protobuf.Timestamp provision_completion_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A map of terms of services. The key is the `id` of
  // [ServiceTerms][google.cloud.discoveryengine.v1alpha.Project.ServiceTerms].
  map<string, ServiceTerms> service_terms_map = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
