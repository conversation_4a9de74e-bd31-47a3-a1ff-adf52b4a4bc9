// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "SampleQuerySetProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// A SampleQuerySet is the parent resource of SampleQuery, and contains the
// configurations shared by all SampleQuery under it.
message SampleQuerySet {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/SampleQuerySet"
    pattern: "projects/{project}/locations/{location}/sampleQuerySets/{sample_query_set}"
  };

  // Identifier. The full resource name of the
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet], in
  // the format of
  // `projects/{project}/locations/{location}/sampleQuerySets/{sample_query_set}`.
  //
  // This field must be a UTF-8 encoded string with a length limit of 1024
  // characters.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The sample query set display name.
  //
  // This field must be a UTF-8 encoded string with a length limit of 128
  // characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp the
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet] was
  // created at.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The description of the
  // [SampleQuerySet][google.cloud.discoveryengine.v1alpha.SampleQuerySet].
  string description = 4;
}
