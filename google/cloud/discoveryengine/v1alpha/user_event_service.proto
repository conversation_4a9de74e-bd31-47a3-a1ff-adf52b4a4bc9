// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/httpbody.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/import_config.proto";
import "google/cloud/discoveryengine/v1alpha/purge_config.proto";
import "google/cloud/discoveryengine/v1alpha/user_event.proto";
import "google/longrunning/operations.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "UserEventServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Service for ingesting end user actions on a website to Discovery Engine API.
service UserEventService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Writes a single user event.
  rpc WriteUserEvent(WriteUserEventRequest) returns (UserEvent) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*/dataStores/*}/userEvents:write"
      body: "user_event"
      additional_bindings {
        post: "/v1alpha/{parent=projects/*/locations/*/collections/*/dataStores/*}/userEvents:write"
        body: "user_event"
      }
      additional_bindings {
        post: "/v1alpha/{parent=projects/*/locations/*}/userEvents:write"
        body: "user_event"
      }
    };
  }

  // Writes a single user event from the browser. This uses a GET request to
  // due to browser restriction of POST-ing to a third-party domain.
  //
  // This method is used only by the Discovery Engine API JavaScript pixel and
  // Google Tag Manager. Users should not call this method directly.
  rpc CollectUserEvent(CollectUserEventRequest) returns (google.api.HttpBody) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*/dataStores/*}/userEvents:collect"
      additional_bindings {
        get: "/v1alpha/{parent=projects/*/locations/*/collections/*/dataStores/*}/userEvents:collect"
      }
      additional_bindings {
        get: "/v1alpha/{parent=projects/*/locations/*}/userEvents:collect"
      }
    };
  }

  // Deletes permanently all user events specified by the filter provided.
  // Depending on the number of events specified by the filter, this operation
  // could take hours or days to complete. To test a filter, use the list
  // command first.
  rpc PurgeUserEvents(PurgeUserEventsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*/dataStores/*}/userEvents:purge"
      body: "*"
      additional_bindings {
        post: "/v1alpha/{parent=projects/*/locations/*/collections/*/dataStores/*}/userEvents:purge"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1alpha.PurgeUserEventsResponse"
      metadata_type: "google.cloud.discoveryengine.v1alpha.PurgeUserEventsMetadata"
    };
  }

  // Bulk import of user events. Request processing might be
  // synchronous. Events that already exist are skipped.
  // Use this method for backfilling historical user events.
  //
  // Operation.response is of type ImportResponse. Note that it is
  // possible for a subset of the items to be successfully inserted.
  // Operation.metadata is of type ImportMetadata.
  rpc ImportUserEvents(ImportUserEventsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*/dataStores/*}/userEvents:import"
      body: "*"
      additional_bindings {
        post: "/v1alpha/{parent=projects/*/locations/*/collections/*/dataStores/*}/userEvents:import"
        body: "*"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1alpha.ImportUserEventsResponse"
      metadata_type: "google.cloud.discoveryengine.v1alpha.ImportUserEventsMetadata"
    };
  }
}

// Request message for WriteUserEvent method.
message WriteUserEventRequest {
  // Required. The parent resource name.
  // If the write user event action is applied in
  // [DataStore][google.cloud.discoveryengine.v1alpha.DataStore] level, the
  // format is:
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.
  // If the write user event action is applied in [Location][] level, for
  // example, the event with
  // [Document][google.cloud.discoveryengine.v1alpha.Document] across multiple
  // [DataStore][google.cloud.discoveryengine.v1alpha.DataStore], the format is:
  // `projects/{project}/locations/{location}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];

  // Required. User event to write.
  optional UserEvent user_event = 2 [(google.api.field_behavior) = REQUIRED];

  // If set to true, the user event is written asynchronously after
  // validation, and the API responds without waiting for the write.
  bool write_async = 3;
}

// Request message for CollectUserEvent method.
message CollectUserEventRequest {
  // Required. The parent DataStore resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];

  // Required. URL encoded UserEvent proto with a length limit of 2,000,000
  // characters.
  string user_event = 2 [(google.api.field_behavior) = REQUIRED];

  // The URL including cgi-parameters but excluding the hash fragment with a
  // length limit of 5,000 characters. This is often more useful than the
  // referer URL, because many browsers only send the domain for third-party
  // requests.
  optional string uri = 3;

  // The event timestamp in milliseconds. This prevents browser caching of
  // otherwise identical get requests. The name is abbreviated to reduce the
  // payload bytes.
  optional int64 ets = 4;
}
