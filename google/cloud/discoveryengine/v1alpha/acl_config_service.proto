// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/acl_config.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "AclConfigServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Service for managing Acl Configuration.
service AclConfigService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Default ACL configuration for use in a location of a customer's project.
  // Updates will only reflect to new data stores. Existing data stores will
  // still use the old value.
  rpc UpdateAclConfig(UpdateAclConfigRequest) returns (AclConfig) {
    option (google.api.http) = {
      patch: "/v1alpha/{acl_config.name=projects/*/locations/*/aclConfig}"
      body: "acl_config"
    };
  }

  // Gets the [AclConfig][google.cloud.discoveryengine.v1alpha.AclConfig].
  rpc GetAclConfig(GetAclConfigRequest) returns (AclConfig) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/aclConfig}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for GetAclConfigRequest method.
message GetAclConfigRequest {
  // Required. Resource name of
  // [AclConfig][google.cloud.discoveryengine.v1alpha.AclConfig], such as
  // `projects/*/locations/*/aclConfig`.
  //
  // If the caller does not have permission to access the
  // [AclConfig][google.cloud.discoveryengine.v1alpha.AclConfig], regardless of
  // whether or not it exists, a PERMISSION_DENIED error is returned.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/AclConfig"
    }
  ];
}

// Request message for UpdateAclConfig method.
message UpdateAclConfigRequest {
  AclConfig acl_config = 1 [(google.api.field_behavior) = REQUIRED];
}
