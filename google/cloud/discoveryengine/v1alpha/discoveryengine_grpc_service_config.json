{"methodConfig": [{"name": [{"service": "google.cloud.discoveryengine.v1alpha.CompletionService"}, {"service": "google.cloud.discoveryengine.v1alpha.GroundedGenerationService"}, {"service": "google.cloud.discoveryengine.v1alpha.RecommendationService"}], "timeout": "5s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "5s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.discoveryengine.v1alpha.ControlService"}, {"service": "google.cloud.discoveryengine.v1alpha.ConversationalSearchService"}, {"service": "google.cloud.discoveryengine.v1alpha.DocumentService"}, {"service": "google.cloud.discoveryengine.v1alpha.EvaluationService"}, {"service": "google.cloud.discoveryengine.v1alpha.ProjectService"}, {"service": "google.cloud.discoveryengine.v1alpha.RankService"}, {"service": "google.cloud.discoveryengine.v1alpha.SampleQueryService"}, {"service": "google.cloud.discoveryengine.v1alpha.SampleQuerySetService"}, {"service": "google.cloud.discoveryengine.v1alpha.SchemaService"}, {"service": "google.cloud.discoveryengine.v1alpha.SearchService"}, {"service": "google.cloud.discoveryengine.v1alpha.UserEventService"}, {"service": "google.longrunning.Operations"}], "timeout": "30s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.discoveryengine.v1alpha.DocumentService", "method": "ImportDocuments"}, {"service": "google.cloud.discoveryengine.v1alpha.UserEventService", "method": "ImportUserEvents"}, {"service": "google.longrunning.Operations", "method": "ListOperations"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "30s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.discoveryengine.v1alpha.EvaluationService", "method": "CreateEvaluation"}], "timeout": "300s"}]}