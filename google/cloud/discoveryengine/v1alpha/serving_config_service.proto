// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/serving_config.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "ServingConfigServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Service for operations related to
// [ServingConfig][google.cloud.discoveryengine.v1alpha.ServingConfig].
service ServingConfigService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Updates a ServingConfig.
  //
  // Returns a NOT_FOUND error if the ServingConfig does not exist.
  rpc UpdateServingConfig(UpdateServingConfigRequest) returns (ServingConfig) {
    option (google.api.http) = {
      patch: "/v1alpha/{serving_config.name=projects/*/locations/*/dataStores/*/servingConfigs/*}"
      body: "serving_config"
      additional_bindings {
        patch: "/v1alpha/{serving_config.name=projects/*/locations/*/collections/*/dataStores/*/servingConfigs/*}"
        body: "serving_config"
      }
      additional_bindings {
        patch: "/v1alpha/{serving_config.name=projects/*/locations/*/collections/*/engines/*/servingConfigs/*}"
        body: "serving_config"
      }
    };
    option (google.api.method_signature) = "serving_config,update_mask";
  }

  // Gets a ServingConfig.
  //
  // Returns a NotFound error if the ServingConfig does not exist.
  rpc GetServingConfig(GetServingConfigRequest) returns (ServingConfig) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/dataStores/*/servingConfigs/*}"
      additional_bindings {
        get: "/v1alpha/{name=projects/*/locations/*/collections/*/dataStores/*/servingConfigs/*}"
      }
      additional_bindings {
        get: "/v1alpha/{name=projects/*/locations/*/collections/*/engines/*/servingConfigs/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all ServingConfigs linked to this dataStore.
  rpc ListServingConfigs(ListServingConfigsRequest)
      returns (ListServingConfigsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*/dataStores/*}/servingConfigs"
      additional_bindings {
        get: "/v1alpha/{parent=projects/*/locations/*/collections/*/dataStores/*}/servingConfigs"
      }
      additional_bindings {
        get: "/v1alpha/{parent=projects/*/locations/*/collections/*/engines/*}/servingConfigs"
      }
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request for UpdateServingConfig method.
message UpdateServingConfigRequest {
  // Required. The ServingConfig to update.
  ServingConfig serving_config = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates which fields in the provided
  // [ServingConfig][google.cloud.discoveryengine.v1alpha.ServingConfig] to
  // update. The following are NOT supported:
  //
  // * [ServingConfig.name][google.cloud.discoveryengine.v1alpha.ServingConfig.name]
  //
  // If not set, all supported fields are updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request for GetServingConfig method.
message GetServingConfigRequest {
  // Required. The resource name of the ServingConfig to get. Format:
  // `projects/{project_number}/locations/{location}/collections/{collection}/engines/{engine}/servingConfigs/{serving_config_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/ServingConfig"
    }
  ];
}

// Request for ListServingConfigs method.
message ListServingConfigsRequest {
  // Required. Full resource name of the parent resource. Format:
  // `projects/{project_number}/locations/{location}/collections/{collection}/engines/{engine}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "discoveryengine.googleapis.com/ServingConfig"
    }
  ];

  // Optional. Maximum number of results to return. If unspecified, defaults
  // to 100. If a value greater than 100 is provided, at most 100 results are
  // returned.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListServingConfigs` call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response for ListServingConfigs method.
message ListServingConfigsResponse {
  // All the ServingConfigs for a given dataStore.
  repeated ServingConfig serving_configs = 1;

  // Pagination token, if not returned indicates the last page.
  string next_page_token = 2;
}
