# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "discoveryengine_proto",
    srcs = [
        "acl_config.proto",
        "acl_config_service.proto",
        "answer.proto",
        "chunk.proto",
        "chunk_service.proto",
        "common.proto",
        "completion.proto",
        "completion_service.proto",
        "control.proto",
        "control_service.proto",
        "conversation.proto",
        "conversational_search_service.proto",
        "custom_tuning_model.proto",
        "data_store.proto",
        "data_store_service.proto",
        "document.proto",
        "document_processing_config.proto",
        "document_service.proto",
        "engine.proto",
        "engine_service.proto",
        "estimate_billing_service.proto",
        "evaluation.proto",
        "evaluation_service.proto",
        "grounded_generation_service.proto",
        "grounding.proto",
        "import_config.proto",
        "project.proto",
        "project_service.proto",
        "purge_config.proto",
        "rank_service.proto",
        "recommendation_service.proto",
        "sample_query.proto",
        "sample_query_service.proto",
        "sample_query_set.proto",
        "sample_query_set_service.proto",
        "schema.proto",
        "schema_service.proto",
        "search_service.proto",
        "search_tuning_service.proto",
        "serving_config.proto",
        "serving_config_service.proto",
        "session.proto",
        "site_search_engine.proto",
        "site_search_engine_service.proto",
        "user_event.proto",
        "user_event_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:httpbody_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:date_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "discoveryengine_proto_with_info",
    deps = [
        ":discoveryengine_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "discoveryengine_java_proto",
    deps = [":discoveryengine_proto"],
)

java_grpc_library(
    name = "discoveryengine_java_grpc",
    srcs = [":discoveryengine_proto"],
    deps = [":discoveryengine_java_proto"],
)

java_gapic_library(
    name = "discoveryengine_java_gapic",
    srcs = [":discoveryengine_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "discoveryengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "discoveryengine_v1alpha.yaml",
    test_deps = [
        ":discoveryengine_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":discoveryengine_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "discoveryengine_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.discoveryengine.v1alpha.AclConfigServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.AclConfigServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.ChunkServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.ChunkServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.CompletionServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.CompletionServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.ControlServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.ControlServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.ConversationalSearchServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.ConversationalSearchServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.DataStoreServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.DataStoreServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.DocumentServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.DocumentServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.EngineServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.EngineServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.EstimateBillingServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.EstimateBillingServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.EvaluationServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.EvaluationServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.GroundedGenerationServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.GroundedGenerationServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.ProjectServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.ProjectServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.RankServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.RankServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.RecommendationServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.RecommendationServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.SampleQueryServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.SampleQueryServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.SampleQuerySetServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.SampleQuerySetServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.SchemaServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.SchemaServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.SearchServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.SearchServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.SearchTuningServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.SearchTuningServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.ServingConfigServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.ServingConfigServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.SiteSearchEngineServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.SiteSearchEngineServiceClientTest",
        "com.google.cloud.discoveryengine.v1alpha.UserEventServiceClientHttpJsonTest",
        "com.google.cloud.discoveryengine.v1alpha.UserEventServiceClientTest",
    ],
    runtime_deps = [":discoveryengine_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-discoveryengine-v1alpha-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":discoveryengine_java_gapic",
        ":discoveryengine_java_grpc",
        ":discoveryengine_java_proto",
        ":discoveryengine_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "discoveryengine_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb",
    protos = [":discoveryengine_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:date_go_proto",
    ],
)

go_gapic_library(
    name = "discoveryengine_go_gapic",
    srcs = [":discoveryengine_proto_with_info"],
    grpc_service_config = "discoveryengine_grpc_service_config.json",
    importpath = "cloud.google.com/go/discoveryengine/apiv1alpha;discoveryengine",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "discoveryengine_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":discoveryengine_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-discoveryengine-v1alpha-go",
    deps = [
        ":discoveryengine_go_gapic",
        ":discoveryengine_go_gapic_srcjar-metadata.srcjar",
        ":discoveryengine_go_gapic_srcjar-snippets.srcjar",
        ":discoveryengine_go_gapic_srcjar-test.srcjar",
        ":discoveryengine_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "discoveryengine_py_gapic",
    srcs = [":discoveryengine_proto"],
    grpc_service_config = "discoveryengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "discoveryengine_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "discoveryengine_py_gapic_test",
    srcs = [
        "discoveryengine_py_gapic_pytest.py",
        "discoveryengine_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":discoveryengine_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "discoveryengine-v1alpha-py",
    deps = [
        ":discoveryengine_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "discoveryengine_php_proto",
    deps = [":discoveryengine_proto"],
)

php_gapic_library(
    name = "discoveryengine_php_gapic",
    srcs = [":discoveryengine_proto_with_info"],
    grpc_service_config = "discoveryengine_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "discoveryengine_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":discoveryengine_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-discoveryengine-v1alpha-php",
    deps = [
        ":discoveryengine_php_gapic",
        ":discoveryengine_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "discoveryengine_nodejs_gapic",
    package_name = "@google-cloud/discoveryengine",
    src = ":discoveryengine_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "discoveryengine_grpc_service_config.json",
    package = "google.cloud.discoveryengine.v1alpha",
    rest_numeric_enums = True,
    service_yaml = "discoveryengine_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "discoveryengine-v1alpha-nodejs",
    deps = [
        ":discoveryengine_nodejs_gapic",
        ":discoveryengine_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "discoveryengine_ruby_proto",
    deps = [":discoveryengine_proto"],
)

ruby_grpc_library(
    name = "discoveryengine_ruby_grpc",
    srcs = [":discoveryengine_proto"],
    deps = [":discoveryengine_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "discoveryengine_ruby_gapic",
    srcs = [":discoveryengine_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-discoveryengine-v1alpha"],
    grpc_service_config = "discoveryengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "discoveryengine_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":discoveryengine_ruby_grpc",
        ":discoveryengine_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-discoveryengine-v1alpha-ruby",
    deps = [
        ":discoveryengine_ruby_gapic",
        ":discoveryengine_ruby_grpc",
        ":discoveryengine_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "discoveryengine_csharp_proto",
    extra_opts = [],
    deps = [":discoveryengine_proto"],
)

csharp_grpc_library(
    name = "discoveryengine_csharp_grpc",
    srcs = [":discoveryengine_proto"],
    deps = [":discoveryengine_csharp_proto"],
)

csharp_gapic_library(
    name = "discoveryengine_csharp_gapic",
    srcs = [":discoveryengine_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "discoveryengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "discoveryengine_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":discoveryengine_csharp_grpc",
        ":discoveryengine_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-discoveryengine-v1alpha-csharp",
    deps = [
        ":discoveryengine_csharp_gapic",
        ":discoveryengine_csharp_grpc",
        ":discoveryengine_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "discoveryengine_cc_proto",
    deps = [":discoveryengine_proto"],
)

cc_grpc_library(
    name = "discoveryengine_cc_grpc",
    srcs = [":discoveryengine_proto"],
    grpc_only = True,
    deps = [":discoveryengine_cc_proto"],
)
