// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/import_config.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "PurgeConfigProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Request message for PurgeUserEvents method.
message PurgeUserEventsRequest {
  // Required. The resource name of the catalog under which the events are
  // created. The format is
  // `projects/${projectId}/locations/global/collections/{$collectionId}/dataStores/${dataStoreId}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];

  // Required. The filter string to specify the events to be deleted with a
  // length limit of 5,000 characters. The eligible fields for filtering are:
  //
  // * `eventType`: Double quoted
  // [UserEvent.event_type][google.cloud.discoveryengine.v1alpha.UserEvent.event_type]
  // string.
  // * `eventTime`: in ISO 8601 "zulu" format.
  // * `userPseudoId`: Double quoted string. Specifying this will delete all
  //   events associated with a visitor.
  // * `userId`: Double quoted string. Specifying this will delete all events
  //   associated with a user.
  //
  // Examples:
  //
  // * Deleting all events in a time range:
  //   `eventTime > "2012-04-23T18:25:43.511Z"
  //   eventTime < "2012-04-23T18:30:43.511Z"`
  // * Deleting specific eventType:
  //   `eventType = "search"`
  // * Deleting all events for a specific visitor:
  //   `userPseudoId = "visitor1024"`
  // * Deleting all events inside a DataStore:
  //   `*`
  //
  // The filtering fields are assumed to have an implicit AND.
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // The `force` field is currently not supported. Purge user event requests
  // will permanently delete all purgeable events. Once the development is
  // complete:
  // If `force` is set to false, the method will return the expected
  // purge count without deleting any user events. This field will default to
  // false if not included in the request.
  bool force = 3;
}

// Response of the PurgeUserEventsRequest. If the long running operation is
// successfully done, then this message is returned by the
// google.longrunning.Operations.response field.
message PurgeUserEventsResponse {
  // The total count of events purged as a result of the operation.
  int64 purge_count = 1;
}

// Metadata related to the progress of the PurgeUserEvents operation.
// This will be returned by the google.longrunning.Operation.metadata field.
message PurgeUserEventsMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;

  // Count of entries that were deleted successfully.
  int64 success_count = 3;

  // Count of entries that encountered errors while processing.
  int64 failure_count = 4;
}

// Configuration of destination for Purge related errors.
message PurgeErrorConfig {
  // Required. Errors destination.
  oneof destination {
    // Cloud Storage prefix for purge errors. This must be an empty,
    // existing Cloud Storage directory. Purge errors are written to
    // sharded files in this directory, one per line, as a JSON-encoded
    // `google.rpc.Status` message.
    string gcs_prefix = 1;
  }
}

// Request message for
// [DocumentService.PurgeDocuments][google.cloud.discoveryengine.v1alpha.DocumentService.PurgeDocuments]
// method.
message PurgeDocumentsRequest {
  // The inline source for the input config for
  // [DocumentService.PurgeDocuments][google.cloud.discoveryengine.v1alpha.DocumentService.PurgeDocuments]
  // method.
  message InlineSource {
    // Required. A list of full resource name of documents to purge. In the
    // format
    // `projects/*/locations/*/collections/*/dataStores/*/branches/*/documents/*`.
    // Recommended max of 100 items.
    repeated string documents = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "discoveryengine.googleapis.com/Document"
      }
    ];
  }

  // The desired input source for the purging documents based on document IDs.
  oneof source {
    // Cloud Storage location for the input content.
    // Supported `data_schema`:
    // * `document_id`: One valid
    // [Document.id][google.cloud.discoveryengine.v1alpha.Document.id] per line.
    GcsSource gcs_source = 5;

    // Inline source for the input content for purge.
    InlineSource inline_source = 6;
  }

  // Required. The parent resource name, such as
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/branches/{branch}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Branch"
    }
  ];

  // Required. Filter matching documents to purge. Only currently supported
  // value is
  // `*` (all items).
  string filter = 2 [(google.api.field_behavior) = REQUIRED];

  // The desired location of errors incurred during the purge.
  PurgeErrorConfig error_config = 7;

  // Actually performs the purge. If `force` is set to false, return the
  // expected purge count without deleting any documents.
  bool force = 3;
}

// Response message for
// [DocumentService.PurgeDocuments][google.cloud.discoveryengine.v1alpha.DocumentService.PurgeDocuments]
// method. If the long running operation is successfully done, then this message
// is returned by the google.longrunning.Operations.response field.
message PurgeDocumentsResponse {
  // The total count of documents purged as a result of the operation.
  int64 purge_count = 1;

  // A sample of document names that will be deleted. Only populated if `force`
  // is set to false. A max of 100 names will be returned and the names are
  // chosen at random.
  repeated string purge_sample = 2 [(google.api.resource_reference) = {
    type: "discoveryengine.googleapis.com/Document"
  }];
}

// Metadata related to the progress of the PurgeDocuments operation.
// This will be returned by the google.longrunning.Operation.metadata field.
message PurgeDocumentsMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;

  // Count of entries that were deleted successfully.
  int64 success_count = 3;

  // Count of entries that encountered errors while processing.
  int64 failure_count = 4;

  // Count of entries that were ignored as entries were not found.
  int64 ignored_count = 5;
}

// Request message for
// [CompletionService.PurgeSuggestionDenyListEntries][google.cloud.discoveryengine.v1alpha.CompletionService.PurgeSuggestionDenyListEntries]
// method.
message PurgeSuggestionDenyListEntriesRequest {
  // Required. The parent data store resource name for which to import denylist
  // entries. Follows pattern projects/*/locations/*/collections/*/dataStores/*.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];
}

// Response message for
// [CompletionService.PurgeSuggestionDenyListEntries][google.cloud.discoveryengine.v1alpha.CompletionService.PurgeSuggestionDenyListEntries]
// method.
message PurgeSuggestionDenyListEntriesResponse {
  // Number of suggestion deny list entries purged.
  int64 purge_count = 1;

  // A sample of errors encountered while processing the request.
  repeated google.rpc.Status error_samples = 2;
}

// Metadata related to the progress of the PurgeSuggestionDenyListEntries
// operation. This is returned by the google.longrunning.Operation.metadata
// field.
message PurgeSuggestionDenyListEntriesMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Request message for
// [CompletionService.PurgeCompletionSuggestions][google.cloud.discoveryengine.v1alpha.CompletionService.PurgeCompletionSuggestions]
// method.
message PurgeCompletionSuggestionsRequest {
  // Required. The parent data store resource name for which to purge completion
  // suggestions. Follows pattern
  // projects/*/locations/*/collections/*/dataStores/*.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];
}

// Response message for
// [CompletionService.PurgeCompletionSuggestions][google.cloud.discoveryengine.v1alpha.CompletionService.PurgeCompletionSuggestions]
// method.
message PurgeCompletionSuggestionsResponse {
  // Whether the completion suggestions were successfully purged.
  bool purge_succeeded = 1;

  // A sample of errors encountered while processing the request.
  repeated google.rpc.Status error_samples = 2;
}

// Metadata related to the progress of the PurgeCompletionSuggestions
// operation. This is returned by the google.longrunning.Operation.metadata
// field.
message PurgeCompletionSuggestionsMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}
