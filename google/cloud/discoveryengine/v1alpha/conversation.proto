// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/search_service.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "ConversationProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// External conversation proto definition.
message Conversation {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/Conversation"
    pattern: "projects/{project}/locations/{location}/dataStores/{data_store}/conversations/{conversation}"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/conversations/{conversation}"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/engines/{engine}/conversations/{conversation}"
  };

  // Enumeration of the state of the conversation.
  enum State {
    // Unknown.
    STATE_UNSPECIFIED = 0;

    // Conversation is currently open.
    IN_PROGRESS = 1;

    // Conversation has been completed.
    COMPLETED = 2;
  }

  // Immutable. Fully qualified name
  // `projects/{project}/locations/global/collections/{collection}/dataStore/*/conversations/*`
  // or
  // `projects/{project}/locations/global/collections/{collection}/engines/*/conversations/*`.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // The state of the Conversation.
  State state = 2;

  // A unique identifier for tracking users.
  string user_pseudo_id = 3;

  // Conversation messages.
  repeated ConversationMessage messages = 4;

  // Output only. The time the conversation started.
  google.protobuf.Timestamp start_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the conversation finished.
  google.protobuf.Timestamp end_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines a reply message to user.
message Reply {
  // Defines reference in reply.
  message Reference {
    option deprecated = true;

    // URI link reference.
    string uri = 1;

    // Anchor text.
    string anchor_text = 2;

    // Anchor text start index.
    int32 start = 3;

    // Anchor text end index.
    int32 end = 4;
  }

  // DEPRECATED: use `summary` instead.
  // Text reply.
  string reply = 1 [deprecated = true];

  // References in the reply.
  repeated Reference references = 2 [deprecated = true];

  // Summary based on search results.
  SearchResponse.Summary summary = 3;
}

// Defines context of the conversation
message ConversationContext {
  // The current list of documents the user is seeing.
  // It contains the document resource references.
  repeated string context_documents = 1;

  // The current active document the user opened.
  // It contains the document resource reference.
  string active_document = 2;
}

// Defines text input.
message TextInput {
  // Text input.
  string input = 1;

  // Conversation context of the input.
  ConversationContext context = 2;
}

// Defines a conversation message.
message ConversationMessage {
  oneof message {
    // User text input.
    TextInput user_input = 1;

    // Search reply.
    Reply reply = 2;
  }

  // Output only. Message creation timestamp.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
