// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/schema.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "SchemaServiceProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Service for managing [Schema][google.cloud.discoveryengine.v1alpha.Schema]s.
service SchemaService {
  option (google.api.default_host) = "discoveryengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets a [Schema][google.cloud.discoveryengine.v1alpha.Schema].
  rpc GetSchema(GetSchemaRequest) returns (Schema) {
    option (google.api.http) = {
      get: "/v1alpha/{name=projects/*/locations/*/dataStores/*/schemas/*}"
      additional_bindings {
        get: "/v1alpha/{name=projects/*/locations/*/collections/*/dataStores/*/schemas/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets a list of [Schema][google.cloud.discoveryengine.v1alpha.Schema]s.
  rpc ListSchemas(ListSchemasRequest) returns (ListSchemasResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=projects/*/locations/*/dataStores/*}/schemas"
      additional_bindings {
        get: "/v1alpha/{parent=projects/*/locations/*/collections/*/dataStores/*}/schemas"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a [Schema][google.cloud.discoveryengine.v1alpha.Schema].
  rpc CreateSchema(CreateSchemaRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=projects/*/locations/*/dataStores/*}/schemas"
      body: "schema"
      additional_bindings {
        post: "/v1alpha/{parent=projects/*/locations/*/collections/*/dataStores/*}/schemas"
        body: "schema"
      }
    };
    option (google.api.method_signature) = "parent,schema,schema_id";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1alpha.Schema"
      metadata_type: "google.cloud.discoveryengine.v1alpha.CreateSchemaMetadata"
    };
  }

  // Updates a [Schema][google.cloud.discoveryengine.v1alpha.Schema].
  rpc UpdateSchema(UpdateSchemaRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha/{schema.name=projects/*/locations/*/dataStores/*/schemas/*}"
      body: "schema"
      additional_bindings {
        patch: "/v1alpha/{schema.name=projects/*/locations/*/collections/*/dataStores/*/schemas/*}"
        body: "schema"
      }
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.discoveryengine.v1alpha.Schema"
      metadata_type: "google.cloud.discoveryengine.v1alpha.UpdateSchemaMetadata"
    };
  }

  // Deletes a [Schema][google.cloud.discoveryengine.v1alpha.Schema].
  rpc DeleteSchema(DeleteSchemaRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=projects/*/locations/*/dataStores/*/schemas/*}"
      additional_bindings {
        delete: "/v1alpha/{name=projects/*/locations/*/collections/*/dataStores/*/schemas/*}"
      }
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.discoveryengine.v1alpha.DeleteSchemaMetadata"
    };
  }
}

// Request message for
// [SchemaService.GetSchema][google.cloud.discoveryengine.v1alpha.SchemaService.GetSchema]
// method.
message GetSchemaRequest {
  // Required. The full resource name of the schema, in the format of
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Schema"
    }
  ];
}

// Request message for
// [SchemaService.ListSchemas][google.cloud.discoveryengine.v1alpha.SchemaService.ListSchemas]
// method.
message ListSchemasRequest {
  // Required. The parent data store resource name, in the format of
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];

  // The maximum number of
  // [Schema][google.cloud.discoveryengine.v1alpha.Schema]s to return. The
  // service may return fewer than this value.
  //
  // If unspecified, at most 100
  // [Schema][google.cloud.discoveryengine.v1alpha.Schema]s are returned.
  //
  // The maximum value is 1000; values above 1000 are set to 1000.
  int32 page_size = 2;

  // A page token, received from a previous
  // [SchemaService.ListSchemas][google.cloud.discoveryengine.v1alpha.SchemaService.ListSchemas]
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // [SchemaService.ListSchemas][google.cloud.discoveryengine.v1alpha.SchemaService.ListSchemas]
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for
// [SchemaService.ListSchemas][google.cloud.discoveryengine.v1alpha.SchemaService.ListSchemas]
// method.
message ListSchemasResponse {
  // The [Schema][google.cloud.discoveryengine.v1alpha.Schema]s.
  repeated Schema schemas = 1;

  // A token that can be sent as
  // [ListSchemasRequest.page_token][google.cloud.discoveryengine.v1alpha.ListSchemasRequest.page_token]
  // to retrieve the next page. If this field is omitted, there are no
  // subsequent pages.
  string next_page_token = 2;
}

// Request message for
// [SchemaService.CreateSchema][google.cloud.discoveryengine.v1alpha.SchemaService.CreateSchema]
// method.
message CreateSchemaRequest {
  // Required. The parent data store resource name, in the format of
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/DataStore"
    }
  ];

  // Required. The [Schema][google.cloud.discoveryengine.v1alpha.Schema] to
  // create.
  Schema schema = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the
  // [Schema][google.cloud.discoveryengine.v1alpha.Schema], which becomes the
  // final component of the
  // [Schema.name][google.cloud.discoveryengine.v1alpha.Schema.name].
  //
  // This field should conform to
  // [RFC-1034](https://tools.ietf.org/html/rfc1034) standard with a length
  // limit of 63 characters.
  string schema_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for
// [SchemaService.UpdateSchema][google.cloud.discoveryengine.v1alpha.SchemaService.UpdateSchema]
// method.
message UpdateSchemaRequest {
  // Required. The [Schema][google.cloud.discoveryengine.v1alpha.Schema] to
  // update.
  Schema schema = 1 [(google.api.field_behavior) = REQUIRED];

  // If set to true, and the
  // [Schema][google.cloud.discoveryengine.v1alpha.Schema] is not found, a new
  // [Schema][google.cloud.discoveryengine.v1alpha.Schema] is created. In this
  // situation, `update_mask` is ignored.
  bool allow_missing = 3;
}

// Request message for
// [SchemaService.DeleteSchema][google.cloud.discoveryengine.v1alpha.SchemaService.DeleteSchema]
// method.
message DeleteSchemaRequest {
  // Required. The full resource name of the schema, in the format of
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "discoveryengine.googleapis.com/Schema"
    }
  ];
}

// Metadata for Create Schema LRO.
message CreateSchemaMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Metadata for UpdateSchema LRO.
message UpdateSchemaMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Metadata for DeleteSchema LRO.
message DeleteSchemaMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}
