// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/discoveryengine/v1alpha/common.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1Alpha";
option go_package = "cloud.google.com/go/discoveryengine/apiv1alpha/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "EngineProto";
option java_package = "com.google.cloud.discoveryengine.v1alpha";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1alpha";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1alpha";

// Metadata that describes the training and serving parameters of an
// [Engine][google.cloud.discoveryengine.v1alpha.Engine].
message Engine {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/Engine"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/engines/{engine}"
  };

  // Configurations for a Search Engine.
  message SearchEngineConfig {
    // The search feature tier of this engine.
    //
    // Different tiers might have different
    // pricing. To learn more, check the pricing documentation.
    //
    // Defaults to
    // [SearchTier.SEARCH_TIER_STANDARD][google.cloud.discoveryengine.v1alpha.SearchTier.SEARCH_TIER_STANDARD]
    // if not specified.
    SearchTier search_tier = 1;

    // The add-on that this search engine enables.
    repeated SearchAddOn search_add_ons = 2;
  }

  // Additional config specs for a `similar-items` engine.
  message SimilarDocumentsEngineConfig {}

  // Additional config specs for a Media Recommendation engine.
  message MediaRecommendationEngineConfig {
    // Custom threshold for `cvr` optimization_objective.
    message OptimizationObjectiveConfig {
      // Required. The name of the field to target. Currently supported
      // values: `watch-percentage`, `watch-time`.
      string target_field = 1 [(google.api.field_behavior) = REQUIRED];

      // Required. The threshold to be applied to the target (e.g., 0.5).
      float target_field_value_float = 2
          [(google.api.field_behavior) = REQUIRED];
    }

    // The training state of the engine.
    enum TrainingState {
      // Unspecified training state.
      TRAINING_STATE_UNSPECIFIED = 0;

      // The engine training is paused.
      PAUSED = 1;

      // The engine is training.
      TRAINING = 2;
    }

    // Required. The type of engine. e.g., `recommended-for-you`.
    //
    // This field together with
    // [optimization_objective][Engine.optimization_objective] describe engine
    // metadata to use to control engine training and serving.
    //
    // Currently supported values: `recommended-for-you`, `others-you-may-like`,
    // `more-like-this`, `most-popular-items`.
    string type = 1 [(google.api.field_behavior) = REQUIRED];

    // The optimization objective. e.g., `cvr`.
    //
    // This field together with
    // [optimization_objective][google.cloud.discoveryengine.v1alpha.Engine.MediaRecommendationEngineConfig.type]
    // describe engine metadata to use to control engine training and serving.
    //
    // Currently supported
    // values: `ctr`, `cvr`.
    //
    //  If not specified, we choose default based on engine type.
    // Default depends on type of recommendation:
    //
    // `recommended-for-you` => `ctr`
    //
    // `others-you-may-like` => `ctr`
    string optimization_objective = 2;

    // Name and value of the custom threshold for cvr optimization_objective.
    // For target_field `watch-time`, target_field_value must be an integer
    // value indicating the media progress time in seconds between (0, 86400]
    // (excludes 0, includes 86400) (e.g., 90).
    // For target_field `watch-percentage`, the target_field_value must be a
    // valid float value between (0, 1.0] (excludes 0, includes 1.0) (e.g.,
    // 0.5).
    OptimizationObjectiveConfig optimization_objective_config = 3;

    // The training state that the engine is in (e.g.
    // `TRAINING` or `PAUSED`).
    //
    // Since part of the cost of running the service
    // is frequency of training - this can be used to determine when to train
    // engine in order to control cost. If not specified: the default value for
    // `CreateEngine` method is `TRAINING`. The default value for
    // `UpdateEngine` method is to keep the state the same as before.
    TrainingState training_state = 4;
  }

  // Configurations for a Chat Engine.
  message ChatEngineConfig {
    // Configurations for generating a Dialogflow agent.
    //
    // Note that these configurations are one-time consumed by
    // and passed to Dialogflow service. It means they cannot be retrieved using
    // [EngineService.GetEngine][google.cloud.discoveryengine.v1alpha.EngineService.GetEngine]
    // or
    // [EngineService.ListEngines][google.cloud.discoveryengine.v1alpha.EngineService.ListEngines]
    // API after engine creation.
    message AgentCreationConfig {
      // Name of the company, organization or other entity that the agent
      // represents. Used for knowledge connector LLM prompt and for knowledge
      // search.
      string business = 1;

      // Required. The default language of the agent as a language tag.
      // See [Language
      // Support](https://cloud.google.com/dialogflow/docs/reference/language)
      // for a list of the currently supported language codes.
      string default_language_code = 2;

      // Required. The time zone of the agent from the [time zone
      // database](https://www.iana.org/time-zones), e.g., America/New_York,
      // Europe/Paris.
      string time_zone = 3 [(google.api.field_behavior) = REQUIRED];

      // Agent location for Agent creation, supported values: global/us/eu.
      // If not provided, us Engine will create Agent using us-central-1 by
      // default; eu Engine will create Agent using eu-west-1 by default.
      string location = 4;
    }

    // The configurationt generate the Dialogflow agent that is associated to
    // this Engine.
    //
    // Note that these configurations are one-time consumed by
    // and passed to Dialogflow service. It means they cannot be retrieved using
    // [EngineService.GetEngine][google.cloud.discoveryengine.v1alpha.EngineService.GetEngine]
    // or
    // [EngineService.ListEngines][google.cloud.discoveryengine.v1alpha.EngineService.ListEngines]
    // API after engine creation.
    AgentCreationConfig agent_creation_config = 1;

    // The resource name of an exist Dialogflow agent to link to this Chat
    // Engine. Customers can either provide `agent_creation_config` to create
    // agent or provide an agent name that links the agent with the Chat engine.
    //
    // Format: `projects/<Project ID>/locations/<Location ID>/agents/<Agent
    // ID>`.
    //
    // Note that the `dialogflow_agent_to_link` are one-time consumed by and
    // passed to Dialogflow service. It means they cannot be retrieved using
    // [EngineService.GetEngine][google.cloud.discoveryengine.v1alpha.EngineService.GetEngine]
    // or
    // [EngineService.ListEngines][google.cloud.discoveryengine.v1alpha.EngineService.ListEngines]
    // API after engine creation. Use
    // [ChatEngineMetadata.dialogflow_agent][google.cloud.discoveryengine.v1alpha.Engine.ChatEngineMetadata.dialogflow_agent]
    // for actual agent association after Engine is created.
    string dialogflow_agent_to_link = 2;
  }

  // Common configurations for an Engine.
  message CommonConfig {
    // The name of the company, business or entity that is associated with the
    // engine. Setting this may help improve LLM related features.
    string company_name = 1;
  }

  // Additional information of a recommendation engine.
  message RecommendationMetadata {
    // The serving state of the recommendation engine.
    enum ServingState {
      // Unspecified serving state.
      SERVING_STATE_UNSPECIFIED = 0;

      // The engine is not serving.
      INACTIVE = 1;

      // The engine is serving and can be queried.
      ACTIVE = 2;

      // The engine is trained on tuned hyperparameters and can be
      // queried.
      TUNED = 3;
    }

    // Describes whether this engine have sufficient training data
    // to be continuously trained.
    enum DataState {
      // Unspecified default value, should never be explicitly set.
      DATA_STATE_UNSPECIFIED = 0;

      // The engine has sufficient training data.
      DATA_OK = 1;

      // The engine does not have sufficient training data. Error
      // messages can be queried via Stackdriver.
      DATA_ERROR = 2;
    }

    // Output only. The serving state of the engine: `ACTIVE`, `NOT_ACTIVE`.
    ServingState serving_state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The state of data requirements for this engine: `DATA_OK`
    // and `DATA_ERROR`.
    //
    // Engine cannot be trained if the data is in
    // `DATA_ERROR` state. Engine can have `DATA_ERROR` state even
    // if serving state is `ACTIVE`: engines were trained successfully before,
    // but cannot be refreshed because the underlying engine no longer has
    // sufficient data for training.
    DataState data_state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The timestamp when the latest successful tune finished. Only
    // applicable on Media Recommendation engines.
    google.protobuf.Timestamp last_tune_time = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The latest tune operation id associated with the engine.
    // Only applicable on Media Recommendation engines.
    //
    // If present, this operation id can be used to determine if there is an
    // ongoing tune for this engine. To check the operation status, send the
    // GetOperation request with this operation id in the engine resource
    // format. If no tuning has happened for this engine, the string is empty.
    string tuning_operation = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Additional information of a Chat Engine.
  // Fields in this message are output only.
  message ChatEngineMetadata {
    // The resource name of a Dialogflow agent, that this Chat Engine refers
    // to.
    //
    // Format: `projects/<Project ID>/locations/<Location ID>/agents/<Agent
    // ID>`.
    string dialogflow_agent = 1;
  }

  // Additional config specs that defines the behavior of the engine.
  oneof engine_config {
    // Additional config specs for a `similar-items` engine.
    SimilarDocumentsEngineConfig similar_documents_config = 9;

    // Configurations for the Chat Engine. Only applicable if
    // [solution_type][google.cloud.discoveryengine.v1alpha.Engine.solution_type]
    // is
    // [SOLUTION_TYPE_CHAT][google.cloud.discoveryengine.v1alpha.SolutionType.SOLUTION_TYPE_CHAT].
    ChatEngineConfig chat_engine_config = 11;

    // Configurations for the Search Engine. Only applicable if
    // [solution_type][google.cloud.discoveryengine.v1alpha.Engine.solution_type]
    // is
    // [SOLUTION_TYPE_SEARCH][google.cloud.discoveryengine.v1alpha.SolutionType.SOLUTION_TYPE_SEARCH].
    SearchEngineConfig search_engine_config = 13;

    // Configurations for the Media Engine. Only applicable on the data
    // stores with
    // [solution_type][google.cloud.discoveryengine.v1alpha.Engine.solution_type]
    // [SOLUTION_TYPE_RECOMMENDATION][google.cloud.discoveryengine.v1alpha.SolutionType.SOLUTION_TYPE_RECOMMENDATION]
    // and
    // [IndustryVertical.MEDIA][google.cloud.discoveryengine.v1alpha.IndustryVertical.MEDIA]
    // vertical.
    MediaRecommendationEngineConfig media_recommendation_engine_config = 14;
  }

  // Engine metadata to monitor the status of the engine.
  oneof engine_metadata {
    // Output only. Additional information of a recommendation engine. Only
    // applicable if
    // [solution_type][google.cloud.discoveryengine.v1alpha.Engine.solution_type]
    // is
    // [SOLUTION_TYPE_RECOMMENDATION][google.cloud.discoveryengine.v1alpha.SolutionType.SOLUTION_TYPE_RECOMMENDATION].
    RecommendationMetadata recommendation_metadata = 10
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Additional information of the Chat Engine. Only applicable
    // if
    // [solution_type][google.cloud.discoveryengine.v1alpha.Engine.solution_type]
    // is
    // [SOLUTION_TYPE_CHAT][google.cloud.discoveryengine.v1alpha.SolutionType.SOLUTION_TYPE_CHAT].
    ChatEngineMetadata chat_engine_metadata = 12
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Immutable. The fully qualified resource name of the engine.
  //
  // This field must be a UTF-8 encoded string with a length limit of 1024
  // characters.
  //
  // Format:
  // `projects/{project_number}/locations/{location}/collections/{collection}/engines/{engine}`
  // engine should be 1-63 characters, and valid characters are
  // /[a-z0-9][a-z0-9-_]*/. Otherwise, an INVALID_ARGUMENT error is returned.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];

  // Required. The display name of the engine. Should be human readable. UTF-8
  // encoded string with limit of 1024 characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp the Recommendation Engine was created at.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp the Recommendation Engine was last updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The data stores associated with this engine.
  //
  // For
  // [SOLUTION_TYPE_SEARCH][google.cloud.discoveryengine.v1alpha.SolutionType.SOLUTION_TYPE_SEARCH]
  // and
  // [SOLUTION_TYPE_RECOMMENDATION][google.cloud.discoveryengine.v1alpha.SolutionType.SOLUTION_TYPE_RECOMMENDATION]
  // type of engines, they can only associate with at most one data store.
  //
  // If
  // [solution_type][google.cloud.discoveryengine.v1alpha.Engine.solution_type]
  // is
  // [SOLUTION_TYPE_CHAT][google.cloud.discoveryengine.v1alpha.SolutionType.SOLUTION_TYPE_CHAT],
  // multiple [DataStore][google.cloud.discoveryengine.v1alpha.DataStore]s in
  // the same [Collection][google.cloud.discoveryengine.v1alpha.Collection] can
  // be associated here.
  //
  // Note that when used in
  // [CreateEngineRequest][google.cloud.discoveryengine.v1alpha.CreateEngineRequest],
  // one DataStore id must be provided as the system will use it for necessary
  // initializations.
  repeated string data_store_ids = 5;

  // Required. The solutions of the engine.
  SolutionType solution_type = 6 [(google.api.field_behavior) = REQUIRED];

  // The industry vertical that the engine registers.
  // The restriction of the Engine industry vertical is based on
  // [DataStore][google.cloud.discoveryengine.v1alpha.DataStore]: If
  // unspecified, default to `GENERIC`. Vertical on Engine has to match vertical
  // of the DataStore linked to the engine.
  IndustryVertical industry_vertical = 16;

  // Common config spec that specifies the metadata of the engine.
  CommonConfig common_config = 15;
}
