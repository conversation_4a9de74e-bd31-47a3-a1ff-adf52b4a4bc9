type: google.api.Service
config_version: 3
name: identitytoolkit.googleapis.com
title: Identity Toolkit API

apis:
- name: google.cloud.identitytoolkit.v2.AccountManagementService
- name: google.cloud.identitytoolkit.v2.AuthenticationService

documentation:
  summary: |-
    The Google Identity Toolkit API lets you use open standards to verify a
    user's identity.

backend:
  rules:
  - selector: 'google.cloud.identitytoolkit.v2.AccountManagementService.*'
    deadline: 11.0
  - selector: google.cloud.identitytoolkit.v2.AuthenticationService.FinalizeMfaSignIn
    deadline: 11.0
  - selector: google.cloud.identitytoolkit.v2.AuthenticationService.StartMfaSignIn
    deadline: 11.0

authentication:
  rules:
  - selector: 'google.cloud.identitytoolkit.v2.AccountManagementService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.identitytoolkit.v2.AuthenticationService.FinalizeMfaSignIn
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.identitytoolkit.v2.AuthenticationService.StartMfaSignIn
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
