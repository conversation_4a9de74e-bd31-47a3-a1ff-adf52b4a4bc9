// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.identitytoolkit.v2;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.IdentityToolkit.V2";
option go_package = "cloud.google.com/go/identitytoolkit/apiv2/identitytoolkitpb;identitytoolkitpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.identitytoolkit.v2";
option php_namespace = "Google\\Cloud\\IdentityToolkit\\V2";
option ruby_package = "Google::Cloud::IdentityToolkit::V2";

// The information required to auto-retrieve an SMS.
message AutoRetrievalInfo {
  // The Android app's signature hash for Google Play Service's
  // SMS Retriever API.
  string app_signature_hash = 1;
}

// App Verification info for a StartMfa request.
message StartMfaPhoneRequestInfo {
  // Required for enrollment. Phone number to be enrolled as MFA.
  string phone_number = 1;

  // iOS only. Receipt of successful app token validation with APNS.
  string ios_receipt = 2;

  // iOS only. Secret delivered to iOS app via APNS.
  string ios_secret = 3;

  // Web only. Recaptcha solution.
  string recaptcha_token = 4;

  // Android only. Used by Google Play Services to identify the app for
  // auto-retrieval.
  AutoRetrievalInfo auto_retrieval_info = 5;

  // Android only. Used to assert application identity in place of a
  // recaptcha token. A SafetyNet Token can be generated via the
  // [SafetyNet Android Attestation
  // API](https://developer.android.com/training/safetynet/attestation.html),
  // with the Base64 encoding of the `phone_number` field as the nonce.
  string safety_net_token = 6;
}

// Phone Verification info for a StartMfa response.
message StartMfaPhoneResponseInfo {
  // An opaque string that represents the enrollment session.
  string session_info = 1;
}

// Phone Verification info for a FinalizeMfa request.
message FinalizeMfaPhoneRequestInfo {
  // An opaque string that represents the enrollment session.
  string session_info = 1;

  // User-entered verification code.
  string code = 2;

  // Android only. Uses for "instant" phone number verification though GmsCore.
  string android_verification_proof = 3;

  // Required if Android verification proof is presented.
  string phone_number = 4;
}

// Phone Verification info for a FinalizeMfa response.
message FinalizeMfaPhoneResponseInfo {
  // Android only. Long-lived replacement for valid code tied to android device.
  string android_verification_proof = 1;

  // Android only. Expiration time of verification proof in seconds.
  google.protobuf.Timestamp android_verification_proof_expire_time = 2;

  // For Android verification proof.
  string phone_number = 3;
}
