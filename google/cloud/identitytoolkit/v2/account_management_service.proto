// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.identitytoolkit.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/identitytoolkit/v2/mfa_info.proto";

option csharp_namespace = "Google.Cloud.IdentityToolkit.V2";
option go_package = "cloud.google.com/go/identitytoolkit/apiv2/identitytoolkitpb;identitytoolkitpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.identitytoolkit.v2";
option php_namespace = "Google\\Cloud\\IdentityToolkit\\V2";
option ruby_package = "Google::Cloud::IdentityToolkit::V2";

// Account management for Identity Toolkit
service AccountManagementService {
  option (google.api.default_host) = "identitytoolkit.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Finishes enrolling a second factor for the user.
  rpc FinalizeMfaEnrollment(FinalizeMfaEnrollmentRequest) returns (FinalizeMfaEnrollmentResponse) {
    option (google.api.http) = {
      post: "/v2/accounts/mfaEnrollment:finalize"
      body: "*"
    };
  }

  // Step one of the MFA enrollment process. In SMS case, this sends an
  // SMS verification code to the user.
  rpc StartMfaEnrollment(StartMfaEnrollmentRequest) returns (StartMfaEnrollmentResponse) {
    option (google.api.http) = {
      post: "/v2/accounts/mfaEnrollment:start"
      body: "*"
    };
  }

  // Revokes one second factor from the enrolled second factors for an account.
  rpc WithdrawMfa(WithdrawMfaRequest) returns (WithdrawMfaResponse) {
    option (google.api.http) = {
      post: "/v2/accounts/mfaEnrollment:withdraw"
      body: "*"
    };
  }
}

// Finishes enrolling a second factor for the user.
message FinalizeMfaEnrollmentRequest {
  // Required. ID token.
  string id_token = 1 [(google.api.field_behavior) = REQUIRED];

  // Display name which is entered  by users to distinguish between different
  // second factors with same type or different type.
  string display_name = 3;

  // MFA enrollment information to be verified.
  oneof verification_info {
    // Verification info to authorize sending an SMS for phone verification.
    FinalizeMfaPhoneRequestInfo phone_verification_info = 4;
  }

  // The ID of the Identity Platform tenant that the user enrolling MFA belongs
  // to. If not set, the user belongs to the default Identity Platform project.
  string tenant_id = 5;
}

// FinalizeMfaEnrollment response.
message FinalizeMfaEnrollmentResponse {
  // ID token updated to reflect MFA enrollment.
  string id_token = 1;

  // Refresh token updated to reflect MFA enrollment.
  string refresh_token = 2;

  // MFA verified enrollment information.
  oneof auxiliary_auth_info {
    // Auxiliary auth info specific to phone auth.
    FinalizeMfaPhoneResponseInfo phone_auth_info = 3;
  }
}

// Sends MFA enrollment verification SMS for a user.
message StartMfaEnrollmentRequest {
  // Required. User's ID token.
  string id_token = 1 [(google.api.field_behavior) = REQUIRED];

  // MFA information by type of 2nd factor.
  oneof enrollment_info {
    // Verification info to authorize sending an SMS for phone verification.
    StartMfaPhoneRequestInfo phone_enrollment_info = 3;
  }

  // The ID of the Identity Platform tenant that the user enrolling MFA belongs
  // to. If not set, the user belongs to the default Identity Platform project.
  string tenant_id = 4;
}

// StartMfaEnrollment response.
message StartMfaEnrollmentResponse {
  // MFA start enrollment response by 2nd factor type.
  oneof enrollment_response {
    // Verification info to authorize sending an SMS for phone verification.
    StartMfaPhoneResponseInfo phone_session_info = 1;
  }
}

// Withdraws MFA.
message WithdrawMfaRequest {
  // Required. User's ID token.
  string id_token = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. MFA enrollment id from a current MFA enrollment.
  string mfa_enrollment_id = 2 [(google.api.field_behavior) = REQUIRED];

  // The ID of the Identity Platform tenant that the user unenrolling MFA
  // belongs to. If not set, the user belongs to the default Identity Platform
  // project.
  string tenant_id = 3;
}

// Withdraws MultiFactorAuth response.
message WithdrawMfaResponse {
  // ID token updated to reflect removal of the second factor.
  string id_token = 1;

  // Refresh token updated to reflect removal of the second factor.
  string refresh_token = 2;
}
