# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "identitytoolkit_proto",
    srcs = [
        "account_management_service.proto",
        "authentication_service.proto",
        "mfa_info.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "identitytoolkit_proto_with_info",
    deps = [
        ":identitytoolkit_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "identitytoolkit_java_proto",
    deps = [":identitytoolkit_proto"],
)

java_grpc_library(
    name = "identitytoolkit_java_grpc",
    srcs = [":identitytoolkit_proto"],
    deps = [":identitytoolkit_java_proto"],
)

java_gapic_library(
    name = "identitytoolkit_java_gapic",
    srcs = [":identitytoolkit_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "identitytoolkit_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "identitytoolkit_v2.yaml",
    test_deps = [
        ":identitytoolkit_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":identitytoolkit_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "identitytoolkit_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.identitytoolkit.v2.AccountManagementServiceClientHttpJsonTest",
        "com.google.cloud.identitytoolkit.v2.AccountManagementServiceClientTest",
        "com.google.cloud.identitytoolkit.v2.AuthenticationServiceClientHttpJsonTest",
        "com.google.cloud.identitytoolkit.v2.AuthenticationServiceClientTest",
    ],
    runtime_deps = [":identitytoolkit_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-identitytoolkit-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":identitytoolkit_java_gapic",
        ":identitytoolkit_java_grpc",
        ":identitytoolkit_java_proto",
        ":identitytoolkit_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "identitytoolkit_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/identitytoolkit/apiv2/identitytoolkitpb",
    protos = [":identitytoolkit_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "identitytoolkit_go_gapic",
    srcs = [":identitytoolkit_proto_with_info"],
    grpc_service_config = "identitytoolkit_grpc_service_config.json",
    importpath = "cloud.google.com/go/identitytoolkit/apiv2;identitytoolkit",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "identitytoolkit_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":identitytoolkit_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-identitytoolkit-v2-go",
    deps = [
        ":identitytoolkit_go_gapic",
        ":identitytoolkit_go_gapic_srcjar-metadata.srcjar",
        ":identitytoolkit_go_gapic_srcjar-snippets.srcjar",
        ":identitytoolkit_go_gapic_srcjar-test.srcjar",
        ":identitytoolkit_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "identitytoolkit_py_gapic",
    srcs = [":identitytoolkit_proto"],
    grpc_service_config = "identitytoolkit_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-identity-toolkit",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=identity_toolkit",
    ],
    rest_numeric_enums = True,
    service_yaml = "identitytoolkit_v2.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "identitytoolkit_py_gapic_test",
    srcs = [
        "identitytoolkit_py_gapic_pytest.py",
        "identitytoolkit_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":identitytoolkit_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "identitytoolkit-v2-py",
    deps = [
        ":identitytoolkit_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "identitytoolkit_php_proto",
    deps = [":identitytoolkit_proto"],
)

php_gapic_library(
    name = "identitytoolkit_php_gapic",
    srcs = [":identitytoolkit_proto_with_info"],
    grpc_service_config = "identitytoolkit_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "identitytoolkit_v2.yaml",
    transport = "grpc+rest",
    deps = [":identitytoolkit_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-identitytoolkit-v2-php",
    deps = [
        ":identitytoolkit_php_gapic",
        ":identitytoolkit_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "identitytoolkit_nodejs_gapic",
    package_name = "@google-cloud/identitytoolkit",
    src = ":identitytoolkit_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "identitytoolkit_grpc_service_config.json",
    package = "google.cloud.identitytoolkit.v2",
    rest_numeric_enums = True,
    service_yaml = "identitytoolkit_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "identitytoolkit-v2-nodejs",
    deps = [
        ":identitytoolkit_nodejs_gapic",
        ":identitytoolkit_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "identitytoolkit_ruby_proto",
    deps = [":identitytoolkit_proto"],
)

ruby_grpc_library(
    name = "identitytoolkit_ruby_grpc",
    srcs = [":identitytoolkit_proto"],
    deps = [":identitytoolkit_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "identitytoolkit_ruby_gapic",
    srcs = [":identitytoolkit_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=identitytoolkit.googleapis.com",
        "ruby-cloud-api-shortname=identitytoolkit",
        "ruby-cloud-gem-name=google-cloud-identity_toolkit-v2",
        "ruby-cloud-product-url=https://cloud.google.com/identity-platform/docs/reference/rest",
    ],
    grpc_service_config = "identitytoolkit_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Google Identity Toolkit API lets you use open standards to verify a user's identity.",
    ruby_cloud_title = "Identity Platform V2",
    service_yaml = "identitytoolkit_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":identitytoolkit_ruby_grpc",
        ":identitytoolkit_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-identitytoolkit-v2-ruby",
    deps = [
        ":identitytoolkit_ruby_gapic",
        ":identitytoolkit_ruby_grpc",
        ":identitytoolkit_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "identitytoolkit_csharp_proto",
    deps = [":identitytoolkit_proto"],
)

csharp_grpc_library(
    name = "identitytoolkit_csharp_grpc",
    srcs = [":identitytoolkit_proto"],
    deps = [":identitytoolkit_csharp_proto"],
)

csharp_gapic_library(
    name = "identitytoolkit_csharp_gapic",
    srcs = [":identitytoolkit_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "identitytoolkit_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "identitytoolkit_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":identitytoolkit_csharp_grpc",
        ":identitytoolkit_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-identitytoolkit-v2-csharp",
    deps = [
        ":identitytoolkit_csharp_gapic",
        ":identitytoolkit_csharp_grpc",
        ":identitytoolkit_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "identitytoolkit_cc_proto",
    deps = [":identitytoolkit_proto"],
)

cc_grpc_library(
    name = "identitytoolkit_cc_grpc",
    srcs = [":identitytoolkit_proto"],
    grpc_only = True,
    deps = [":identitytoolkit_cc_proto"],
)
