// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.identitytoolkit.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/identitytoolkit/v2/mfa_info.proto";

option csharp_namespace = "Google.Cloud.IdentityToolkit.V2";
option go_package = "cloud.google.com/go/identitytoolkit/apiv2/identitytoolkitpb;identitytoolkitpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.identitytoolkit.v2";
option php_namespace = "Google\\Cloud\\IdentityToolkit\\V2";
option ruby_package = "Google::Cloud::IdentityToolkit::V2";

// Authentication for Identity Toolkit
service AuthenticationService {
  option (google.api.default_host) = "identitytoolkit.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Verifies the MFA challenge and performs sign-in
  rpc FinalizeMfaSignIn(FinalizeMfaSignInRequest) returns (FinalizeMfaSignInResponse) {
    option (google.api.http) = {
      post: "/v2/accounts/mfaSignIn:finalize"
      body: "*"
    };
  }

  // Sends the MFA challenge
  rpc StartMfaSignIn(StartMfaSignInRequest) returns (StartMfaSignInResponse) {
    option (google.api.http) = {
      post: "/v2/accounts/mfaSignIn:start"
      body: "*"
    };
  }
}

// Finalizes sign-in by verifying MFA challenge.
message FinalizeMfaSignInRequest {
  // Required. Pending credential from first factor sign-in.
  string mfa_pending_credential = 2 [(google.api.field_behavior) = REQUIRED];

  // Proof of completion of the MFA challenge.
  oneof verification_info {
    // Proof of completion of the SMS based MFA challenge.
    FinalizeMfaPhoneRequestInfo phone_verification_info = 3;
  }

  // The ID of the Identity Platform tenant the user is signing in to. If not
  // set, the user will sign in to the default Identity Platform project.
  string tenant_id = 4;
}

// FinalizeMfaSignIn response.
message FinalizeMfaSignInResponse {
  // ID token for the authenticated user.
  string id_token = 1;

  // Refresh token for the authenticated user.
  string refresh_token = 2;

  // MFA verified sign-in information.
  oneof auxiliary_auth_info {
    // Extra phone auth info, including android verification proof.
    FinalizeMfaPhoneResponseInfo phone_auth_info = 3;
  }
}

// Starts multi-factor sign-in by sending the multi-factor auth challenge.
message StartMfaSignInRequest {
  // Required. Pending credential from first factor sign-in.
  string mfa_pending_credential = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. MFA enrollment id from the user's list of current MFA enrollments.
  string mfa_enrollment_id = 3 [(google.api.field_behavior) = REQUIRED];

  // MFA information by type of 2nd factor.
  oneof sign_in_info {
    // Verification info to authorize sending an SMS for phone verification.
    StartMfaPhoneRequestInfo phone_sign_in_info = 4;
  }

  // The ID of the Identity Platform tenant the user is signing in to. If not
  // set, the user will sign in to the default Identity Platform project.
  string tenant_id = 5;
}

// StartMfaSignIn response.
message StartMfaSignInResponse {
  // MultiFactor start sign-in response by 2nd factor type.
  oneof response_info {
    // MultiFactor sign-in session information specific to SMS-type second
    // factors. Along with the one-time code retrieved from the sent SMS, the
    // contents of this session information should be passed to
    // FinalizeMfaSignIn to complete the sign in.
    StartMfaPhoneResponseInfo phone_response_info = 1;
  }
}
