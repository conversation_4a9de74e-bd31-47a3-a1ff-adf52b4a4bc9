// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.identitytoolkit.logging;

import "google/protobuf/struct.proto";
import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/identitytoolkit/logging/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "RequestLogProto";
option java_package = "com.google.cloud.identitytoolkit.logging";

// Log of a request to Identitytoolkit. This proto is modeled after
// google.cloud.audit.AuditLog so that consumers can easily query
// for requests regardless of whether those requests were logged via
// Cloud Audit Logging or Identitytoolkit request logging.
message RequestLog {
  // The name of the service method or operation.
  // For API calls, this should be the name of the API method.
  // For example,
  //
  //     "google.datastore.v1.Datastore.RunQuery"
  //     "google.logging.v1.LoggingService.DeleteLog"
  string method_name = 1;

  // The status of the overall operation.
  google.rpc.Status status = 2;

  // Metadata about the operation.
  RequestMetadata request_metadata = 3;

  // The operation request. This may not include all request parameters,
  // such as those that are too large, privacy-sensitive, or duplicated
  // elsewhere in the log record.
  // It should never include user-generated data, such as file contents.
  // When the JSON object represented here has a proto equivalent, the proto
  // name will be indicated in the `@type` property.
  google.protobuf.Struct request = 4;

  // The operation response. This may not include all response elements,
  // such as those that are too large, privacy-sensitive, or duplicated
  // elsewhere in the log record.
  // It should never include user-generated data, such as file contents.
  // When the JSON object represented here has a proto equivalent, the proto
  // name will be indicated in the `@type` property.
  google.protobuf.Struct response = 5;

  // The number of items returned from a List or Query API method,
  // if applicable.
  int64 num_response_items = 6;

  // Other service-specific data about the request, response, and other
  // information associated with the current event.
  google.protobuf.Struct metadata = 7;
}

// Metadata about the request.
message RequestMetadata {
  // The IP address of the caller.
  string caller_ip = 1;

  // The user agent of the caller.
  // This information is not authenticated and should be treated
  // accordingly.
  //
  // For example:
  //
  // +   `google-api-python-client/1.4.0`:
  //     The request was made by the Google API client for Python.
  // +   `Cloud SDK Command Line Tool apitools-client/1.0 gcloud/0.9.62`:
  //     The request was made by the Google Cloud SDK CLI (gcloud).
  // +   `AppEngine-Google; (+http://code.google.com/appengine; appid:
  //      s~my-project`:
  //     The request was made from the `my-project` App Engine app.
  // NOLINT
  string caller_supplied_user_agent = 2;
}
