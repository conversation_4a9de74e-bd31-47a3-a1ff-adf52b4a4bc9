# This build file includes a target for the Ruby wrapper library for
# google-cloud-essential_contacts.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for essentialcontacts.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "essentialcontacts_ruby_wrapper",
    srcs = ["//google/cloud/essentialcontacts/v1:essentialcontacts_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-essential_contacts",
        "ruby-cloud-env-prefix=ESSENTIAL_CONTACTS",
        "ruby-cloud-wrapper-of=v1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/resource-manager/docs/managing-notification-contacts/",
        "ruby-cloud-api-id=essentialcontacts.googleapis.com",
        "ruby-cloud-api-shortname=essentialcontacts",
    ],
    ruby_cloud_description = "Many Google Cloud services, such as Cloud Billing, send out notifications to share important information with Google Cloud users. By default, these notifications are sent to members with certain Identity and Access Management (IAM) roles. With Essential Contacts, you can customize who receives notifications by providing your own list of contacts.",
    ruby_cloud_title = "Essential Contacts",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-essentialcontacts-ruby",
    deps = [
        ":essentialcontacts_ruby_wrapper",
    ],
)
