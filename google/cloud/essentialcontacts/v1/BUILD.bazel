# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "essentialcontacts_proto",
    srcs = [
        "enums.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "essentialcontacts_proto_with_info",
    deps = [
        ":essentialcontacts_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "essentialcontacts_java_proto",
    deps = [":essentialcontacts_proto"],
)

java_grpc_library(
    name = "essentialcontacts_java_grpc",
    srcs = [":essentialcontacts_proto"],
    deps = [":essentialcontacts_java_proto"],
)

java_gapic_library(
    name = "essentialcontacts_java_gapic",
    srcs = [":essentialcontacts_proto_with_info"],
    grpc_service_config = "essentialcontacts_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "essentialcontacts_v1.yaml",
    test_deps = [
        ":essentialcontacts_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":essentialcontacts_java_proto",
    ],
)

java_gapic_test(
    name = "essentialcontacts_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.essentialcontacts.v1.EssentialContactsServiceClientHttpJsonTest",
        "com.google.cloud.essentialcontacts.v1.EssentialContactsServiceClientTest",
    ],
    runtime_deps = [":essentialcontacts_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-essentialcontacts-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":essentialcontacts_java_gapic",
        ":essentialcontacts_java_grpc",
        ":essentialcontacts_java_proto",
        ":essentialcontacts_proto",
    ],
)

go_proto_library(
    name = "essentialcontacts_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/essentialcontacts/apiv1/essentialcontactspb",
    protos = [":essentialcontacts_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "essentialcontacts_go_gapic",
    srcs = [":essentialcontacts_proto_with_info"],
    grpc_service_config = "essentialcontacts_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/essentialcontacts/apiv1;essentialcontacts",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "essentialcontacts_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":essentialcontacts_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-essentialcontacts-v1-go",
    deps = [
        ":essentialcontacts_go_gapic",
        ":essentialcontacts_go_gapic_srcjar-metadata.srcjar",
        ":essentialcontacts_go_gapic_srcjar-snippets.srcjar",
        ":essentialcontacts_go_gapic_srcjar-test.srcjar",
        ":essentialcontacts_go_proto",
    ],
)

py_gapic_library(
    name = "essentialcontacts_py_gapic",
    srcs = [":essentialcontacts_proto"],
    grpc_service_config = "essentialcontacts_v1_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-essential-contacts",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=essential_contacts",
    ],
    rest_numeric_enums = True,
    service_yaml = "essentialcontacts_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "essentialcontacts_py_gapic_test",
    srcs = [
        "essentialcontacts_py_gapic_pytest.py",
        "essentialcontacts_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":essentialcontacts_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "essentialcontacts-v1-py",
    deps = [
        ":essentialcontacts_py_gapic",
    ],
)

php_proto_library(
    name = "essentialcontacts_php_proto",
    deps = [":essentialcontacts_proto"],
)

php_gapic_library(
    name = "essentialcontacts_php_gapic",
    srcs = [":essentialcontacts_proto_with_info"],
    grpc_service_config = "essentialcontacts_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "essentialcontacts_v1.yaml",
    transport = "grpc+rest",
    deps = [":essentialcontacts_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-essentialcontacts-v1-php",
    deps = [
        ":essentialcontacts_php_gapic",
        ":essentialcontacts_php_proto",
    ],
)

nodejs_gapic_library(
    name = "essentialcontacts_nodejs_gapic",
    package_name = "@google-cloud/essential-contacts",
    src = ":essentialcontacts_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "essentialcontacts_v1_grpc_service_config.json",
    package = "google.cloud.essentialcontacts.v1",
    rest_numeric_enums = True,
    service_yaml = "essentialcontacts_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "essentialcontacts-v1-nodejs",
    deps = [
        ":essentialcontacts_nodejs_gapic",
        ":essentialcontacts_proto",
    ],
)

ruby_proto_library(
    name = "essentialcontacts_ruby_proto",
    deps = [":essentialcontacts_proto"],
)

ruby_grpc_library(
    name = "essentialcontacts_ruby_grpc",
    srcs = [":essentialcontacts_proto"],
    deps = [":essentialcontacts_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "essentialcontacts_ruby_gapic",
    srcs = [":essentialcontacts_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-essential_contacts-v1",
        "ruby-cloud-env-prefix=ESSENTIAL_CONTACTS",
        "ruby-cloud-product-url=https://cloud.google.com/resource-manager/docs/managing-notification-contacts/",
        "ruby-cloud-api-id=essentialcontacts.googleapis.com",
        "ruby-cloud-api-shortname=essentialcontacts",
    ],
    grpc_service_config = "essentialcontacts_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Many Google Cloud services, such as Cloud Billing, send out notifications to share important information with Google Cloud users. By default, these notifications are sent to members with certain Identity and Access Management (IAM) roles. With Essential Contacts, you can customize who receives notifications by providing your own list of contacts.",
    ruby_cloud_title = "Essential Contacts V1",
    service_yaml = "essentialcontacts_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":essentialcontacts_ruby_grpc",
        ":essentialcontacts_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-essentialcontacts-v1-ruby",
    deps = [
        ":essentialcontacts_ruby_gapic",
        ":essentialcontacts_ruby_grpc",
        ":essentialcontacts_ruby_proto",
    ],
)

csharp_proto_library(
    name = "essentialcontacts_csharp_proto",
    deps = [":essentialcontacts_proto"],
)

csharp_grpc_library(
    name = "essentialcontacts_csharp_grpc",
    srcs = [":essentialcontacts_proto"],
    deps = [":essentialcontacts_csharp_proto"],
)

csharp_gapic_library(
    name = "essentialcontacts_csharp_gapic",
    srcs = [":essentialcontacts_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "essentialcontacts_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "essentialcontacts_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":essentialcontacts_csharp_grpc",
        ":essentialcontacts_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-essentialcontacts-v1-csharp",
    deps = [
        ":essentialcontacts_csharp_gapic",
        ":essentialcontacts_csharp_grpc",
        ":essentialcontacts_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "essentialcontacts_cc_proto",
    deps = [":essentialcontacts_proto"],
)

cc_grpc_library(
    name = "essentialcontacts_cc_grpc",
    srcs = [":essentialcontacts_proto"],
    grpc_only = True,
    deps = [":essentialcontacts_cc_proto"],
)
