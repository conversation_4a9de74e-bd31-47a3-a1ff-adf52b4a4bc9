// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.essentialcontacts.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/essentialcontacts/v1/enums.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.EssentialContacts.V1";
option go_package = "cloud.google.com/go/essentialcontacts/apiv1/essentialcontactspb;essentialcontactspb";
option java_multiple_files = true;
option java_package = "com.google.cloud.essentialcontacts.v1";
option php_namespace = "Google\\Cloud\\EssentialContacts\\V1";
option ruby_package = "Google::Cloud::EssentialContacts::V1";

// Manages contacts for important Google Cloud notifications.
service EssentialContactsService {
  option (google.api.default_host) = "essentialcontacts.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Adds a new contact for a resource.
  rpc CreateContact(CreateContactRequest) returns (Contact) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/contacts"
      body: "contact"
      additional_bindings {
        post: "/v1/{parent=folders/*}/contacts"
        body: "contact"
      }
      additional_bindings {
        post: "/v1/{parent=organizations/*}/contacts"
        body: "contact"
      }
    };
    option (google.api.method_signature) = "parent,contact";
  }

  // Updates a contact.
  // Note: A contact's email address cannot be changed.
  rpc UpdateContact(UpdateContactRequest) returns (Contact) {
    option (google.api.http) = {
      patch: "/v1/{contact.name=projects/*/contacts/*}"
      body: "contact"
      additional_bindings {
        patch: "/v1/{contact.name=folders/*/contacts/*}"
        body: "contact"
      }
      additional_bindings {
        patch: "/v1/{contact.name=organizations/*/contacts/*}"
        body: "contact"
      }
    };
    option (google.api.method_signature) = "contact,update_mask";
  }

  // Lists the contacts that have been set on a resource.
  rpc ListContacts(ListContactsRequest) returns (ListContactsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/contacts"
      additional_bindings { get: "/v1/{parent=folders/*}/contacts" }
      additional_bindings { get: "/v1/{parent=organizations/*}/contacts" }
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a single contact.
  rpc GetContact(GetContactRequest) returns (Contact) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/contacts/*}"
      additional_bindings { get: "/v1/{name=folders/*/contacts/*}" }
      additional_bindings { get: "/v1/{name=organizations/*/contacts/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a contact.
  rpc DeleteContact(DeleteContactRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/contacts/*}"
      additional_bindings { delete: "/v1/{name=folders/*/contacts/*}" }
      additional_bindings { delete: "/v1/{name=organizations/*/contacts/*}" }
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all contacts for the resource that are subscribed to the
  // specified notification categories, including contacts inherited from
  // any parent resources.
  rpc ComputeContacts(ComputeContactsRequest)
      returns (ComputeContactsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/contacts:compute"
      additional_bindings { get: "/v1/{parent=folders/*}/contacts:compute" }
      additional_bindings {
        get: "/v1/{parent=organizations/*}/contacts:compute"
      }
    };
  }

  // Allows a contact admin to send a test message to contact to verify that it
  // has been configured correctly.
  rpc SendTestMessage(SendTestMessageRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{resource=projects/*}/contacts:sendTestMessage"
      body: "*"
      additional_bindings {
        post: "/v1/{resource=folders/*}/contacts:sendTestMessage"
        body: "*"
      }
      additional_bindings {
        post: "/v1/{resource=organizations/*}/contacts:sendTestMessage"
        body: "*"
      }
    };
  }
}

// A contact that will receive notifications from Google Cloud.
message Contact {
  option (google.api.resource) = {
    type: "essentialcontacts.googleapis.com/Contact"
    pattern: "projects/{project}/contacts/{contact}"
    pattern: "folders/{folder}/contacts/{contact}"
    pattern: "organizations/{organization}/contacts/{contact}"
  };

  // Output only. The identifier for the contact.
  // Format: {resource_type}/{resource_id}/contacts/{contact_id}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The email address to send notifications to. The email address
  // does not need to be a Google Account.
  string email = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The categories of notifications that the contact will receive
  // communications for.
  repeated NotificationCategory notification_category_subscriptions = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. The preferred language for notifications, as a ISO 639-1 language
  // code. See [Supported
  // languages](https://cloud.google.com/resource-manager/docs/managing-notification-contacts#supported-languages)
  // for a list of supported languages.
  string language_tag = 4 [(google.api.field_behavior) = REQUIRED];

  // Output only. The validity of the contact. A contact is considered valid if
  // it is the correct recipient for notifications for a particular resource.
  ValidationState validation_state = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The last time the validation_state was updated, either manually or
  // automatically. A contact is considered stale if its validation state was
  // updated more than 1 year ago.
  google.protobuf.Timestamp validate_time = 9;
}

// Request message for the ListContacts method.
message ListContactsRequest {
  // Required. The parent resource name.
  // Format: organizations/{organization_id}, folders/{folder_id} or
  // projects/{project_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "essentialcontacts.googleapis.com/Contact"
    }
  ];

  // Optional. The maximum number of results to return from this request.
  // Non-positive values are ignored. The presence of `next_page_token` in the
  // response indicates that more results might be available.
  // If not specified, the default page_size is 100.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If present, retrieves the next batch of results from the
  // preceding call to this method. `page_token` must be the value of
  // `next_page_token` from the previous response. The values of other method
  // parameters should be identical to those in the previous call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the ListContacts method.
message ListContactsResponse {
  // The contacts for the specified resource.
  repeated Contact contacts = 1;

  // If there are more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token` and the
  // rest of the parameters the same as the original request.
  string next_page_token = 2;
}

// Request message for the GetContact method.
message GetContactRequest {
  // Required. The name of the contact to retrieve.
  // Format: organizations/{organization_id}/contacts/{contact_id},
  // folders/{folder_id}/contacts/{contact_id} or
  // projects/{project_id}/contacts/{contact_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "essentialcontacts.googleapis.com/Contact"
    }
  ];
}

// Request message for the DeleteContact method.
message DeleteContactRequest {
  // Required. The name of the contact to delete.
  // Format: organizations/{organization_id}/contacts/{contact_id},
  // folders/{folder_id}/contacts/{contact_id} or
  // projects/{project_id}/contacts/{contact_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "essentialcontacts.googleapis.com/Contact"
    }
  ];
}

// Request message for the CreateContact method.
message CreateContactRequest {
  // Required. The resource to save this contact for.
  // Format: organizations/{organization_id}, folders/{folder_id} or
  // projects/{project_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "essentialcontacts.googleapis.com/Contact"
    }
  ];

  // Required. The contact to create. Must specify an email address and language
  // tag.
  Contact contact = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the UpdateContact method.
message UpdateContactRequest {
  // Required. The contact resource to replace the existing saved contact. Note:
  // the email address of the contact cannot be modified.
  Contact contact = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The update mask applied to the resource. For the `FieldMask`
  // definition, see
  // https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
  google.protobuf.FieldMask update_mask = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the ComputeContacts method.
message ComputeContactsRequest {
  // Required. The name of the resource to compute contacts for.
  // Format: organizations/{organization_id},
  // folders/{folder_id} or projects/{project_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "essentialcontacts.googleapis.com/Contact"
    }
  ];

  // The categories of notifications to compute contacts for. If ALL is included
  // in this list, contacts subscribed to any notification category will be
  // returned.
  repeated NotificationCategory notification_categories = 6;

  // Optional. The maximum number of results to return from this request.
  // Non-positive values are ignored. The presence of `next_page_token` in the
  // response indicates that more results might be available.
  // If not specified, the default page_size is 100.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If present, retrieves the next batch of results from the
  // preceding call to this method. `page_token` must be the value of
  // `next_page_token` from the previous response. The values of other method
  // parameters should be identical to those in the previous call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the ComputeContacts method.
message ComputeContactsResponse {
  // All contacts for the resource that are subscribed to the specified
  // notification categories, including contacts inherited from any parent
  // resources.
  repeated Contact contacts = 1;

  // If there are more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token` and the
  // rest of the parameters the same as the original request.
  string next_page_token = 2;
}

// Request message for the SendTestMessage method.
message SendTestMessageRequest {
  // Required. The list of names of the contacts to send a test message to.
  // Format: organizations/{organization_id}/contacts/{contact_id},
  // folders/{folder_id}/contacts/{contact_id} or
  // projects/{project_id}/contacts/{contact_id}
  repeated string contacts = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "essentialcontacts.googleapis.com/Contact"
    }
  ];

  // Required. The name of the resource to send the test message for. All
  // contacts must either be set directly on this resource or inherited from
  // another resource that is an ancestor of this one. Format:
  // organizations/{organization_id}, folders/{folder_id} or
  // projects/{project_id}
  string resource = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "essentialcontacts.googleapis.com/Contact"
    }
  ];

  // Required. The notification category to send the test message for. All
  // contacts must be subscribed to this category.
  NotificationCategory notification_category = 3
      [(google.api.field_behavior) = REQUIRED];
}
