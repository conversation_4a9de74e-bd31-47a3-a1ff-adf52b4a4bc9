// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.privatecatalog.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.PrivateCatalog.V1Beta1";
option go_package = "cloud.google.com/go/privatecatalog/apiv1beta1/privatecatalogpb;privatecatalogpb";
option java_multiple_files = true;
option java_outer_classname = "PrivateCatalogProto";
option java_package = "com.google.cloud.privatecatalog.v1beta1";
option php_namespace = "Google\\Cloud\\PrivateCatalog\\V1beta1";
option ruby_package = "Google::Cloud::PrivateCatalog::V1beta1";

// `PrivateCatalog` allows catalog consumers to retrieve `Catalog`, `Product`
// and `Version` resources under a target resource context.
//
// `Catalog` is computed based on the [Association][]s linked to the target
// resource and its ancestors. Each association's
// [google.cloud.privatecatalogproducer.v1beta.Catalog][] is transformed into a
// `Catalog`. If multiple associations have the same parent
// [google.cloud.privatecatalogproducer.v1beta.Catalog][], they are
// de-duplicated into one `Catalog`. Users must have
// `cloudprivatecatalog.catalogTargets.get` IAM permission on the resource
// context in order to access catalogs. `Catalog` contains the resource name and
// a subset of data of the original
// [google.cloud.privatecatalogproducer.v1beta.Catalog][].
//
// `Product` is child resource of the catalog. A `Product` contains the resource
// name and a subset of the data of the original
// [google.cloud.privatecatalogproducer.v1beta.Product][].
//
// `Version` is child resource of the product. A `Version` contains the resource
// name and a subset of the data of the original
// [google.cloud.privatecatalogproducer.v1beta.Version][].
service PrivateCatalog {
  option (google.api.default_host) = "cloudprivatecatalog.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Search [Catalog][google.cloud.privatecatalog.v1beta1.Catalog] resources that consumers have access to, within the
  // scope of the consumer cloud resource hierarchy context.
  rpc SearchCatalogs(SearchCatalogsRequest) returns (SearchCatalogsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{resource=projects/*}/catalogs:search"
      additional_bindings {
        get: "/v1beta1/{resource=organizations/*}/catalogs:search"
      }
      additional_bindings {
        get: "/v1beta1/{resource=folders/*}/catalogs:search"
      }
    };
  }

  // Search [Product][google.cloud.privatecatalog.v1beta1.Product] resources that consumers have access to, within the
  // scope of the consumer cloud resource hierarchy context.
  rpc SearchProducts(SearchProductsRequest) returns (SearchProductsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{resource=projects/*}/products:search"
      additional_bindings {
        get: "/v1beta1/{resource=organizations/*}/products:search"
      }
      additional_bindings {
        get: "/v1beta1/{resource=folders/*}/products:search"
      }
    };
  }

  // Search [Version][google.cloud.privatecatalog.v1beta1.Version] resources that consumers have access to, within the
  // scope of the consumer cloud resource hierarchy context.
  rpc SearchVersions(SearchVersionsRequest) returns (SearchVersionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{resource=projects/*}/versions:search"
      additional_bindings {
        get: "/v1beta1/{resource=organizations/*}/versions:search"
      }
      additional_bindings {
        get: "/v1beta1/{resource=folders/*}/versions:search"
      }
    };
  }
}

// Request message for [PrivateCatalog.SearchCatalogs][google.cloud.privatecatalog.v1beta1.PrivateCatalog.SearchCatalogs].
message SearchCatalogsRequest {
  // Required. The name of the resource context. It can be in following formats:
  //
  // * `projects/{project}`
  // * `folders/{folder}`
  // * `organizations/{organization}`
  string resource = 1 [(google.api.field_behavior) = REQUIRED];

  // The query to filter the catalogs. The supported queries are:
  //
  // * Get a single catalog: `name=catalogs/{catalog}`
  string query = 2;

  // The maximum number of entries that are requested.
  int32 page_size = 3;

  // A pagination token returned from a previous call to SearchCatalogs that
  // indicates where this listing should continue from.
  string page_token = 4;
}

// Response message for [PrivateCatalog.SearchCatalogs][google.cloud.privatecatalog.v1beta1.PrivateCatalog.SearchCatalogs].
message SearchCatalogsResponse {
  // The `Catalog`s computed from the resource context.
  repeated Catalog catalogs = 1;

  // A pagination token returned from a previous call to SearchCatalogs that
  // indicates from where listing should continue.
  string next_page_token = 2;
}

// Request message for [PrivateCatalog.SearchProducts][google.cloud.privatecatalog.v1beta1.PrivateCatalog.SearchProducts].
message SearchProductsRequest {
  // Required. The name of the resource context. See [SearchCatalogsRequest.resource][google.cloud.privatecatalog.v1beta1.SearchCatalogsRequest.resource]
  // for details.
  string resource = 1 [(google.api.field_behavior) = REQUIRED];

  // The query to filter the products.
  //
  // The supported queries are:
  // * List products of all catalogs: empty
  // * List products under a catalog: `parent=catalogs/{catalog}`
  // * Get a product by name:
  // `name=catalogs/{catalog}/products/{product}`
  string query = 2;

  // The maximum number of entries that are requested.
  int32 page_size = 3;

  // A pagination token returned from a previous call to SearchProducts that
  // indicates where this listing should continue from.
  string page_token = 4;
}

// Response message for [PrivateCatalog.SearchProducts][google.cloud.privatecatalog.v1beta1.PrivateCatalog.SearchProducts].
message SearchProductsResponse {
  // The `Product` resources computed from the resource context.
  repeated Product products = 1;

  // A pagination token returned from a previous call to SearchProducts that
  // indicates from where listing should continue.
  string next_page_token = 2;
}

// Request message for [PrivateCatalog.SearchVersions][google.cloud.privatecatalog.v1beta1.PrivateCatalog.SearchVersions].
message SearchVersionsRequest {
  // Required. The name of the resource context. See [SearchCatalogsRequest.resource][google.cloud.privatecatalog.v1beta1.SearchCatalogsRequest.resource]
  // for details.
  string resource = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The query to filter the versions.
  //
  // The supported queries are:
  // * List versions under a product:
  // `parent=catalogs/{catalog}/products/{product}`
  // * Get a version by name:
  // `name=catalogs/{catalog}/products/{product}/versions/{version}`
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of entries that are requested.
  int32 page_size = 3;

  // A pagination token returned from a previous call to SearchVersions
  // that indicates where this listing should continue from.
  string page_token = 4;
}

// Response message for [PrivateCatalog.SearchVersions][google.cloud.privatecatalog.v1beta1.PrivateCatalog.SearchVersions].
message SearchVersionsResponse {
  // The `Version` resources computed from the resource context.
  repeated Version versions = 1;

  // A pagination token returned from a previous call to SearchVersions that
  // indicates from where the listing should continue.
  string next_page_token = 2;
}

// The readonly representation of a catalog computed with a given resource
// context.
message Catalog {
  option (google.api.resource) = {
    type: "cloudprivatecatalog.googleapis.com/Catalog"
    pattern: "catalogs/{catalog}"
  };

  // Output only. The resource name of the target catalog, in the format of
  // `catalogs/{catalog}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The descriptive name of the catalog as it appears in UIs.
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The description of the catalog.
  string description = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the catalog was created.
  google.protobuf.Timestamp create_time = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the catalog was last updated.
  google.protobuf.Timestamp update_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The readonly representation of a product computed with a given resource
// context.
message Product {
  option (google.api.resource) = {
    type: "cloudprivatecatalog.googleapis.com/Product"
    pattern: "products/{product}"
  };

  // Output only. The resource name of the target product, in the format of
  // `products/[a-z][-a-z0-9]*[a-z0-9]'.
  //
  // A unique identifier for the product under a catalog.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of the product asset. It can be one of the following values:
  //
  // * `google.deploymentmanager.Template`
  // * `google.cloudprivatecatalog.ListingOnly`
  // * `google.cloudprivatecatalog.Terraform`
  string asset_type = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Output only. The display metadata to describe the product. The JSON schema of the
  // metadata differs by [Product.asset_type][google.cloud.privatecatalog.v1beta1.Product.asset_type].
  // When the type is `google.deploymentmanager.Template`, the schema is as
  // follows:
  //
  // ```
  // "$schema": http://json-schema.org/draft-04/schema#
  // type: object
  // properties:
  //   name:
  //     type: string
  //     minLength: 1
  //     maxLength: 64
  //   description:
  //     type: string
  //     minLength: 1
  //     maxLength: 2048
  //   tagline:
  //     type: string
  //     minLength: 1
  //     maxLength: 100
  //   support_info:
  //     type: string
  //     minLength: 1
  //     maxLength: 2048
  //   creator:
  //     type: string
  //     minLength: 1
  //     maxLength: 100
  //   documentations:
  //     type: array
  //     items:
  //       type: object
  //       properties:
  //         url:
  //           type: string
  //           pattern:
  //           "^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]"
  //         title:
  //           type: string
  //           minLength: 1
  //           maxLength: 64
  //         description:
  //           type: string
  //           minLength: 1
  //           maxLength: 2048
  // required:
  // - name
  // - description
  // additionalProperties: false
  //
  // ```
  //
  // When the asset type is `google.cloudprivatecatalog.ListingOnly`, the schema
  // is as follows:
  //
  // ```
  // "$schema": http://json-schema.org/draft-04/schema#
  // type: object
  // properties:
  //   name:
  //     type: string
  //     minLength: 1
  //     maxLength: 64
  //   description:
  //     type: string
  //     minLength: 1
  //     maxLength: 2048
  //   tagline:
  //     type: string
  //     minLength: 1
  //     maxLength: 100
  //   support_info:
  //     type: string
  //     minLength: 1
  //     maxLength: 2048
  //   creator:
  //     type: string
  //     minLength: 1
  //     maxLength: 100
  //   documentations:
  //     type: array
  //     items:
  //       type: object
  //       properties:
  //         url:
  //           type: string
  //           pattern:
  //           "^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]"
  //         title:
  //           type: string
  //           minLength: 1
  //           maxLength: 64
  //         description:
  //           type: string
  //           minLength: 1
  //           maxLength: 2048
  //   signup_url:
  //     type: string
  //     pattern:
  //     "^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]"
  // required:
  // - name
  // - description
  // - signup_url
  // additionalProperties: false
  //
  // ```
  //
  // When the asset type is `google.cloudprivatecatalog.Terraform`, the schema
  // is as follows:
  //
  // ```
  // "$schema": http://json-schema.org/draft-04/schema#
  // type: object
  // properties:
  //   name:
  //     type: string
  //     minLength: 1
  //     maxLength: 64
  //   description:
  //     type: string
  //     minLength: 1
  //     maxLength: 2048
  //   tagline:
  //     type: string
  //     minLength: 1
  //     maxLength: 100
  //   support_info:
  //     type: string
  //     minLength: 1
  //     maxLength: 2048
  //   creator:
  //     type: string
  //     minLength: 1
  //     maxLength: 100
  //   documentations:
  //     type: array
  //     items:
  //       type: object
  //       properties:
  //         url:
  //           type: string
  //           pattern:
  //           "^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]"
  //         title:
  //           type: string
  //           minLength: 1
  //           maxLength: 64
  //         description:
  //           type: string
  //           minLength: 1
  //           maxLength: 2048
  // required:
  // - name
  // - description
  // additionalProperties: true
  google.protobuf.Struct display_metadata = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. The icon URI of the product.
  string icon_uri = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A collection of assets referred by a product.
  // This field is set for Terraform Products only.
  repeated AssetReference asset_references = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the product was created.
  google.protobuf.Timestamp create_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the product was last updated.
  google.protobuf.Timestamp update_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines the reference of an asset belonging to a product.
message AssetReference {
  // Possible validation steates of an asset reference.
  enum AssetValidationState {
    // Unknown state.
    ASSET_VALIDATION_STATE_UNSPECIFIED = 0;

    // The validation is still in process.
    PENDING = 1;

    // The validation is done and the asset reference is valid.
    VALID = 2;

    // The validation is done and the asset reference is invalid.
    INVALID = 3;
  }

  // Output only. A unique identifier among asset references in a product.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The human-readable description of the referenced asset. Maximum 256
  // characters in length.
  string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The definition of input parameters to hydrate the asset template.
  Inputs inputs = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the asset reference.
  AssetValidationState validation_status = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The validation process metadata.
  google.longrunning.Operation validation_operation = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The destination of the asset.
  oneof source {
    // Output only. The asset resource name if an asset is hosted by Private Catalog.
    string asset = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The cloud storage object path.
    string gcs_path = 11 [
      deprecated = true,
      (google.api.field_behavior) = OUTPUT_ONLY
    ];

    // Output only. The git source.
    GitSource git_source = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The cloud storage source.
  GcsSource gcs_source = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The creation timestamp of the asset reference.
  google.protobuf.Timestamp create_time = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of the asset reference.
  google.protobuf.Timestamp update_time = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The version of the source used for this asset reference.
  string version = 14 [deprecated = true];
}

// Defines definition of input parameters of asset templates.
message Inputs {
  // Output only. The JSON schema defining the inputs and their formats.
  google.protobuf.Struct parameters = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines how to access Cloud Storage source.
message GcsSource {
  // Output only. the cloud storage object path.
  string gcs_path = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Generation of the object, which is set when the content of an object starts
  // being written.
  int64 generation = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the object metadata was last changed.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines how to access a Git Source.
message GitSource {
  // Location of the Git repo to build.
  string repo = 1;

  // Directory, relative to the source root, in which to run the build.
  //
  // This must be a relative path. If a step's `dir` is specified and is an
  // absolute path, this value is ignored for that step's execution.
  string dir = 2;

  // The revision to fetch from the Git repository such as a branch, a tag, a
  // commit SHA, or any Git ref.
  oneof ref {
    // The revision commit to use.
    string commit = 3;

    // The revision branch to use.
    string branch = 4;

    // The revision tag to use.
    string tag = 5;
  }
}

// The consumer representation of a version which is a child resource under a
// `Product` with asset data.
message Version {
  option (google.api.resource) = {
    type: "cloudprivatecatalog.googleapis.com/Version"
    pattern: "catalogs/{catalog}/products/{product}/versions/{version}"
  };

  // Output only. The resource name of the version, in the format
  // `catalogs/{catalog}/products/{product}/versions/[a-z][-a-z0-9]*[a-z0-9]`.
  //
  // A unique identifier for the version under a product.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The user-supplied description of the version. Maximum of 256
  // characters.
  string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The asset which has been validated and is ready to be
  // provisioned. See
  // [google.cloud.privatecatalogproducer.v1beta.Version.asset][] for details.
  google.protobuf.Struct asset = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the version was created.
  google.protobuf.Timestamp create_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the version was last updated.
  google.protobuf.Timestamp update_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}
