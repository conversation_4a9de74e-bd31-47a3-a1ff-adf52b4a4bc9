# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "privatecatalog_proto",
    srcs = [
        "private_catalog.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "privatecatalog_proto_with_info",
    deps = [
        ":privatecatalog_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "privatecatalog_java_proto",
    deps = [":privatecatalog_proto"],
)

java_grpc_library(
    name = "privatecatalog_java_grpc",
    srcs = [":privatecatalog_proto"],
    deps = [":privatecatalog_java_proto"],
)

java_gapic_library(
    name = "privatecatalog_java_gapic",
    srcs = [":privatecatalog_proto_with_info"],
    grpc_service_config = "cloudprivatecatalog_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudprivatecatalog_v1beta1.yaml",
    test_deps = [
        ":privatecatalog_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":privatecatalog_java_proto",
    ],
)

java_gapic_test(
    name = "privatecatalog_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.privatecatalog.v1beta1.PrivateCatalogClientHttpJsonTest",
        "com.google.cloud.privatecatalog.v1beta1.PrivateCatalogClientTest",
    ],
    runtime_deps = [":privatecatalog_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-privatecatalog-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":privatecatalog_java_gapic",
        ":privatecatalog_java_grpc",
        ":privatecatalog_java_proto",
        ":privatecatalog_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "privatecatalog_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/privatecatalog/apiv1beta1/privatecatalogpb",
    protos = [":privatecatalog_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "privatecatalog_go_gapic",
    srcs = [":privatecatalog_proto_with_info"],
    grpc_service_config = "cloudprivatecatalog_grpc_service_config.json",
    importpath = "cloud.google.com/go/privatecatalog/apiv1beta1;privatecatalog",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "cloudprivatecatalog_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":privatecatalog_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-privatecatalog-v1beta1-go",
    deps = [
        ":privatecatalog_go_gapic",
        ":privatecatalog_go_gapic_srcjar-metadata.srcjar",
        ":privatecatalog_go_gapic_srcjar-snippets.srcjar",
        ":privatecatalog_go_gapic_srcjar-test.srcjar",
        ":privatecatalog_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "privatecatalog_py_gapic",
    srcs = [":privatecatalog_proto"],
    grpc_service_config = "cloudprivatecatalog_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-private-catalog",
        "autogen-snippets",
    ],
    rest_numeric_enums = True,
    service_yaml = "cloudprivatecatalog_v1beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "privatecatalog_py_gapic_test",
    srcs = [
        "privatecatalog_py_gapic_pytest.py",
        "privatecatalog_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":privatecatalog_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "privatecatalog-v1beta1-py",
    deps = [
        ":privatecatalog_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "privatecatalog_php_proto",
    deps = [":privatecatalog_proto"],
)

php_gapic_library(
    name = "privatecatalog_php_gapic",
    srcs = [":privatecatalog_proto_with_info"],
    grpc_service_config = "cloudprivatecatalog_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudprivatecatalog_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":privatecatalog_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-privatecatalog-v1beta1-php",
    deps = [
        ":privatecatalog_php_gapic",
        ":privatecatalog_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "privatecatalog_nodejs_gapic",
    package_name = "@google-cloud/private-catalog",
    src = ":privatecatalog_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudprivatecatalog_grpc_service_config.json",
    package = "google.cloud.privatecatalog.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "cloudprivatecatalog_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "privatecatalog-v1beta1-nodejs",
    deps = [
        ":privatecatalog_nodejs_gapic",
        ":privatecatalog_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "privatecatalog_ruby_proto",
    deps = [":privatecatalog_proto"],
)

ruby_grpc_library(
    name = "privatecatalog_ruby_grpc",
    srcs = [":privatecatalog_proto"],
    deps = [":privatecatalog_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "privatecatalog_ruby_gapic",
    srcs = [":privatecatalog_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-private_catalog-v1beta1",
        "ruby-cloud-env-prefix=PRIVATE_CATALOG",
        "ruby-cloud-product-url=https://cloud.google.com/private-catalog/",
        "ruby-cloud-api-id=cloudprivatecatalog.googleapis.com",
        "ruby-cloud-api-shortname=cloudprivatecatalog",
        "ruby-cloud-yard-strict=false",
    ],
    grpc_service_config = "cloudprivatecatalog_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "With Private Catalog, developers and cloud admins can make their solutions discoverable to their internal enterprise users. Cloud admins can manage their solutions and ensure their users are always launching the latest versions.",
    ruby_cloud_title = "Private Catalog V1beta1",
    service_yaml = "cloudprivatecatalog_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":privatecatalog_ruby_grpc",
        ":privatecatalog_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-privatecatalog-v1beta1-ruby",
    deps = [
        ":privatecatalog_ruby_gapic",
        ":privatecatalog_ruby_grpc",
        ":privatecatalog_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "privatecatalog_csharp_proto",
    deps = [":privatecatalog_proto"],
)

csharp_grpc_library(
    name = "privatecatalog_csharp_grpc",
    srcs = [":privatecatalog_proto"],
    deps = [":privatecatalog_csharp_proto"],
)

csharp_gapic_library(
    name = "privatecatalog_csharp_gapic",
    srcs = [":privatecatalog_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudprivatecatalog_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudprivatecatalog_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":privatecatalog_csharp_grpc",
        ":privatecatalog_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-privatecatalog-v1beta1-csharp",
    deps = [
        ":privatecatalog_csharp_gapic",
        ":privatecatalog_csharp_grpc",
        ":privatecatalog_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
