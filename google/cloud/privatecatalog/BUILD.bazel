# This build file includes a target for the Ruby wrapper library for
# google-cloud-private_catalog.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudprivatecatalog.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "cloudprivatecatalog_ruby_wrapper",
    srcs = ["//google/cloud/privatecatalog/v1beta1:privatecatalog_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-private_catalog",
        "ruby-cloud-env-prefix=PRIVATE_CATALOG",
        "ruby-cloud-wrapper-of=v1beta1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/private-catalog/",
        "ruby-cloud-api-id=cloudprivatecatalog.googleapis.com",
        "ruby-cloud-api-shortname=cloudprivatecatalog",
    ],
    ruby_cloud_description = "With Private Catalog, developers and cloud admins can make their solutions discoverable to their internal enterprise users. Cloud admins can manage their solutions and ensure their users are always launching the latest versions.",
    ruby_cloud_title = "Private Catalog",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-privatecatalog-ruby",
    deps = [
        ":cloudprivatecatalog_ruby_wrapper",
    ],
)
