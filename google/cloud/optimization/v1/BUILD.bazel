# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "optimization_proto",
    srcs = [
        "async_model.proto",
        "fleet_routing.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/longrunning:operations_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "optimization_proto_with_info",
    deps = [
        ":optimization_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "optimization_java_proto",
    deps = [":optimization_proto"],
)

java_grpc_library(
    name = "optimization_java_grpc",
    srcs = [":optimization_proto"],
    deps = [":optimization_java_proto"],
)

java_gapic_library(
    name = "optimization_java_gapic",
    srcs = [":optimization_proto_with_info"],
    gapic_yaml = "cloudoptimization_gapic.yaml",
    grpc_service_config = "cloudoptimization_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudoptimization_v1.yaml",
    test_deps = [
        ":optimization_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":optimization_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "optimization_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.optimization.v1.FleetRoutingClientHttpJsonTest",
        "com.google.cloud.optimization.v1.FleetRoutingClientTest",
    ],
    runtime_deps = [":optimization_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-optimization-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":optimization_java_gapic",
        ":optimization_java_grpc",
        ":optimization_java_proto",
        ":optimization_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "optimization_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/optimization/apiv1/optimizationpb",
    protos = [":optimization_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "optimization_go_gapic",
    srcs = [":optimization_proto_with_info"],
    grpc_service_config = "cloudoptimization_grpc_service_config.json",
    importpath = "cloud.google.com/go/optimization/apiv1;optimization",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":optimization_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-optimization-v1-go",
    deps = [
        ":optimization_go_gapic",
        ":optimization_go_gapic_srcjar-metadata.srcjar",
        ":optimization_go_gapic_srcjar-snippets.srcjar",
        ":optimization_go_gapic_srcjar-test.srcjar",
        ":optimization_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "optimization_py_gapic",
    srcs = [":optimization_proto"],
    grpc_service_config = "cloudoptimization_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "optimization_py_gapic_test",
    srcs = [
        "optimization_py_gapic_pytest.py",
        "optimization_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":optimization_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "optimization-v1-py",
    deps = [
        ":optimization_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "optimization_php_proto",
    deps = [":optimization_proto"],
)

php_gapic_library(
    name = "optimization_php_gapic",
    srcs = [":optimization_proto_with_info"],
    grpc_service_config = "cloudoptimization_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":optimization_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-optimization-v1-php",
    deps = [
        ":optimization_php_gapic",
        ":optimization_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "optimization_nodejs_gapic",
    package_name = "@google-cloud/optimization",
    src = ":optimization_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudoptimization_grpc_service_config.json",
    package = "google.cloud.optimization.v1",
    rest_numeric_enums = True,
    service_yaml = "cloudoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "optimization-v1-nodejs",
    deps = [
        ":optimization_nodejs_gapic",
        ":optimization_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "optimization_ruby_proto",
    deps = [":optimization_proto"],
)

ruby_grpc_library(
    name = "optimization_ruby_grpc",
    srcs = [":optimization_proto"],
    deps = [":optimization_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "optimization_ruby_gapic",
    srcs = [":optimization_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=cloudoptimization.googleapis.com",
        "ruby-cloud-api-shortname=cloudoptimization",
        "ruby-cloud-gem-name=google-cloud-optimization-v1",
        "ruby-cloud-product-url=https://cloud.google.com/optimization",
    ],
    grpc_service_config = "cloudoptimization_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Optimization API provides a portfolio of solvers to address common optimization use cases starting with optimal route planning for vehicle fleets.",
    ruby_cloud_title = "Cloud Optimization V1",
    service_yaml = "cloudoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":optimization_ruby_grpc",
        ":optimization_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-optimization-v1-ruby",
    deps = [
        ":optimization_ruby_gapic",
        ":optimization_ruby_grpc",
        ":optimization_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "optimization_csharp_proto",
    extra_opts = [],
    deps = [":optimization_proto"],
)

csharp_grpc_library(
    name = "optimization_csharp_grpc",
    srcs = [":optimization_proto"],
    deps = [":optimization_csharp_proto"],
)

csharp_gapic_library(
    name = "optimization_csharp_gapic",
    srcs = [":optimization_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudoptimization_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":optimization_csharp_grpc",
        ":optimization_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-optimization-v1-csharp",
    deps = [
        ":optimization_csharp_gapic",
        ":optimization_csharp_grpc",
        ":optimization_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "optimization_cc_proto",
    deps = [":optimization_proto"],
)

cc_grpc_library(
    name = "optimization_cc_grpc",
    srcs = [":optimization_proto"],
    grpc_only = True,
    deps = [":optimization_cc_proto"],
)
