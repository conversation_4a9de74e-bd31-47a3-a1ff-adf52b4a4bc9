type: google.api.Service
config_version: 3
name: cloudoptimization.googleapis.com
title: Cloud Optimization API

apis:
- name: google.cloud.optimization.v1.FleetRouting
- name: google.longrunning.Operations

types:
- name: google.cloud.optimization.v1.AsyncModelMetadata
- name: google.cloud.optimization.v1.BatchOptimizeToursResponse

documentation:
  summary: |-
    Cloud Optimization API provides a portfolio of solvers to address common
    optimization use cases starting with optimal route planning for vehicle
    fleets.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/operations/*}'
    additional_bindings:
    - get: '/v1/{name=projects/*/locations/*/operations/*}'

authentication:
  rules:
  - selector: google.cloud.optimization.v1.FleetRouting.BatchOptimizeTours
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.optimization.v1.FleetRouting.OptimizeTours
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
