{"methodConfig": [{"name": [{"service": "google.cloud.optimization.v1.FleetRouting", "method": "OptimizeTours"}], "timeout": "3600s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.optimization.v1.FleetRouting", "method": "BatchOptimizeTours"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}