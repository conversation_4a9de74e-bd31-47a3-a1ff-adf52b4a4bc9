// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.optimization.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/optimization/apiv1/optimizationpb;optimizationpb";
option java_multiple_files = true;
option java_outer_classname = "AsyncModelProto";
option java_package = "com.google.cloud.optimization.v1";

// The desired input location information.
message InputConfig {
  // The location of the input model in cloud storage.
  // Required.
  oneof source {
    // The Google Cloud Storage location to read the input from. This must be a
    // single file.
    GcsSource gcs_source = 1;
  }

  // The input data format that used to store the model in Cloud Storage.
  DataFormat data_format = 2;
}

// The desired output location.
message OutputConfig {
  // The location of the output result in cloud storage.
  // Required.
  oneof destination {
    // The Google Cloud Storage location to write the output to.
    GcsDestination gcs_destination = 1;
  }

  // The output data format that used to store the results in Cloud Storage.
  DataFormat data_format = 2;
}

// The Google Cloud Storage location where the input file will be read from.
message GcsSource {
  // Required. URI of the Google Cloud Storage location.
  string uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// The Google Cloud Storage location where the output file will be written to.
message GcsDestination {
  // Required. URI of the Google Cloud Storage location.
  string uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// The long running operation metadata for async model related methods.
message AsyncModelMetadata {
  // Possible states of the operation.
  enum State {
    // The default value. This value is used if the state is omitted.
    STATE_UNSPECIFIED = 0;

    // Request is being processed.
    RUNNING = 1;

    // The operation completed successfully.
    SUCCEEDED = 2;

    // The operation was cancelled.
    CANCELLED = 3;

    // The operation has failed.
    FAILED = 4;
  }

  // The state of the current operation.
  State state = 1;

  // A message providing more details about the current state of the operation.
  // For example, the error message if the operation is failed.
  string state_message = 2;

  // The creation time of the operation.
  google.protobuf.Timestamp create_time = 3;

  // The last update time of the operation.
  google.protobuf.Timestamp update_time = 4;
}

// Data formats for input and output files.
enum DataFormat {
  // Default value.
  DATA_FORMAT_UNSPECIFIED = 0;

  // Input data in json format.
  JSON = 1;

  // Input data in string format.
  STRING = 2;
}
