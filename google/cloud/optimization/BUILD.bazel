# This build file includes a target for the Ruby wrapper library for
# google-cloud-optimization.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudoptimization.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "optimization_ruby_wrapper",
    srcs = ["//google/cloud/optimization/v1:optimization_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-optimization",
        "ruby-cloud-wrapper-of=v1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/optimization",
        "ruby-cloud-api-id=cloudoptimization.googleapis.com",
        "ruby-cloud-api-shortname=cloudoptimization",
    ],
    ruby_cloud_description = "Cloud Optimization API provides a portfolio of solvers to address common optimization use cases starting with optimal route planning for vehicle fleets.",
    ruby_cloud_title = "Cloud Optimization",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-optimization-ruby",
    deps = [
        ":optimization_ruby_wrapper",
    ],
)
