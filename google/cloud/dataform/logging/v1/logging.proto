// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataform.logging.v1;

import "google/api/field_behavior.proto";

option go_package = "cloud.google.com/go/dataform/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggingProto";
option java_package = "com.google.cloud.dataform.logging.v1";

// Structured payload for logs generated from Dataform workflow invocation
// completions.
message WorkflowInvocationCompletionLogEntry {
  // Represents the final termination state of a workflow invocation.
  enum TerminalState {
    // Default value. This value is unused.
    TERMINAL_STATE_UNSPECIFIED = 0;

    // The workflow invocation succeeded.
    SUCCEEDED = 1;

    // The workflow invocation was cancelled.
    CANCELLED = 2;

    // The workflow invocation failed.
    FAILED = 3;
  }

  // Required. Identifier of the workflow invocation.
  string workflow_invocation_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Identifier of the workflow config.
  string workflow_config_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Identifier of the release config.
  string release_config_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. The workflow invocation's final termination state.
  TerminalState terminal_state = 4 [(google.api.field_behavior) = REQUIRED];
}
