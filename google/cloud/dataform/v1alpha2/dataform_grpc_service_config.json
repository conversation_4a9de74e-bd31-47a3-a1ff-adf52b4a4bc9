{"methodConfig": [{"name": [{"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "ListRepositories"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "GetRepository"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "ListWorkspaces"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "GetWorkspace"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "ListCompilationResults"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "GetCompilationResult"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "CreateCompilationResult"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "QueryCompilationResultActions"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "CreateWorkflowInvocation"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "CancelWorkflowInvocation"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "ListWorkflowInvocations"}, {"service": "google.cloud.dataform.v1alpha2.DataformService", "method": "GetWorkflowInvocation"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}