type: google.api.Service
config_version: 3
name: dataform.googleapis.com
title: Dataform API

apis:
- name: google.cloud.dataform.v1alpha2.Dataform
- name: google.cloud.location.Locations
- name: google.iam.v1.IAMPolicy

documentation:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: 'google.cloud.dataform.v1alpha2.Dataform.*'
    deadline: 60.0
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 60.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1alpha2/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1alpha2/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1alpha2/{resource=projects/*/locations/*/repositories/*}:getIamPolicy'
    additional_bindings:
    - get: '/v1alpha2/{resource=projects/*/locations/*/repositories/*/workspaces/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1alpha2/{resource=projects/*/locations/*/repositories/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1alpha2/{resource=projects/*/locations/*/repositories/*/workspaces/*}:setIamPolicy'
      body: '*'
    - post: '/v1alpha2/{resource=projects/*/locations/*/repositories/*/compilationResults/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1alpha2/{resource=projects/*/locations/*/repositories/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1alpha2/{resource=projects/*/locations/*/repositories/*/workspaces/*}:testIamPermissions'
      body: '*'

authentication:
  rules:
  - selector: 'google.cloud.dataform.v1alpha2.Dataform.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
