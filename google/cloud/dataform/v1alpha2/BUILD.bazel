# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "dataform_proto",
    srcs = [
        "dataform.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/type:interval_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
    ],
)

proto_library_with_info(
    name = "dataform_proto_with_info",
    deps = [
        ":dataform_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "dataform_java_proto",
    deps = [":dataform_proto"],
)

java_grpc_library(
    name = "dataform_java_grpc",
    srcs = [":dataform_proto"],
    deps = [":dataform_java_proto"],
)

java_gapic_library(
    name = "dataform_java_gapic",
    srcs = [":dataform_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "dataform_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataform_v1alpha2.yaml",
    test_deps = [
        ":dataform_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":dataform_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "dataform_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.dataform.v1alpha2.DataformClientHttpJsonTest",
        "com.google.cloud.dataform.v1alpha2.DataformClientTest",
    ],
    runtime_deps = [":dataform_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-dataform-v1alpha2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":dataform_java_gapic",
        ":dataform_java_grpc",
        ":dataform_java_proto",
        ":dataform_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "dataform_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/dataform/apiv1alpha2/dataformpb",
    protos = [":dataform_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:interval_go_proto",
    ],
)

go_gapic_library(
    name = "dataform_go_gapic",
    srcs = [":dataform_proto_with_info"],
    grpc_service_config = "dataform_grpc_service_config.json",
    importpath = "cloud.google.com/go/dataform/apiv1alpha2;dataform",
    metadata = True,
    release_level = "alpha",
    rest_numeric_enums = True,
    service_yaml = "dataform_v1alpha2.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataform_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-dataform-v1alpha2-go",
    deps = [
        ":dataform_go_gapic",
        ":dataform_go_gapic_srcjar-metadata.srcjar",
        ":dataform_go_gapic_srcjar-snippets.srcjar",
        ":dataform_go_gapic_srcjar-test.srcjar",
        ":dataform_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
)

py_gapic_library(
    name = "dataform_py_gapic",
    srcs = [":dataform_proto"],
    grpc_service_config = "dataform_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataform_v1alpha2.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "dataform_py_gapic_test",
    srcs = [
        "dataform_py_gapic_pytest.py",
        "dataform_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":dataform_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "dataform-v1alpha2-py",
    deps = [
        ":dataform_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "dataform_php_proto",
    deps = [":dataform_proto"],
)

php_gapic_library(
    name = "dataform_php_gapic",
    srcs = [":dataform_proto_with_info"],
    grpc_service_config = "dataform_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataform_v1alpha2.yaml",
    transport = "grpc+rest",
    deps = [":dataform_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-dataform-v1alpha2-php",
    deps = [
        ":dataform_php_gapic",
        ":dataform_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "dataform_nodejs_gapic",
    package_name = "@google-cloud/dataform",
    src = ":dataform_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "dataform_grpc_service_config.json",
    package = "google.cloud.dataform.v1alpha2",
    rest_numeric_enums = True,
    service_yaml = "dataform_v1alpha2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "dataform-v1alpha2-nodejs",
    deps = [
        ":dataform_nodejs_gapic",
        ":dataform_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "dataform_ruby_proto",
    deps = [":dataform_proto"],
)

ruby_grpc_library(
    name = "dataform_ruby_grpc",
    srcs = [":dataform_proto"],
    deps = [":dataform_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "dataform_ruby_gapic",
    srcs = [":dataform_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-dataform-v1alpha2"],
    grpc_service_config = "dataform_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataform_v1alpha2.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataform_ruby_grpc",
        ":dataform_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataform-v1alpha2-ruby",
    deps = [
        ":dataform_ruby_gapic",
        ":dataform_ruby_grpc",
        ":dataform_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "dataform_csharp_proto",
    deps = [":dataform_proto"],
)

csharp_grpc_library(
    name = "dataform_csharp_grpc",
    srcs = [":dataform_proto"],
    deps = [":dataform_csharp_proto"],
)

csharp_gapic_library(
    name = "dataform_csharp_gapic",
    srcs = [":dataform_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "dataform_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataform_v1alpha2.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataform_csharp_grpc",
        ":dataform_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-dataform-v1alpha2-csharp",
    deps = [
        ":dataform_csharp_gapic",
        ":dataform_csharp_grpc",
        ":dataform_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "dataform_cc_proto",
    deps = [":dataform_proto"],
)

cc_grpc_library(
    name = "dataform_cc_grpc",
    srcs = [":dataform_proto"],
    grpc_only = True,
    deps = [":dataform_cc_proto"],
)
