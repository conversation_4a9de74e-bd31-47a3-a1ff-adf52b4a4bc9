# This build file includes a target for the Ruby wrapper library for
# google-cloud-dataform.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for dataform.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "dataform_ruby_wrapper",
    srcs = ["//google/cloud/dataform/v1beta1:dataform_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-dataform",
        "ruby-cloud-wrapper-of=v1beta1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/dataform",
        "ruby-cloud-api-id=dataform.googleapis.com",
        "ruby-cloud-api-shortname=dataform",
    ],
    ruby_cloud_description = "Dataform is a service for data analysts to develop, test, version control, and schedule complex SQL workflows for data transformation in BigQuery.",
    ruby_cloud_title = "Dataform",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataform-ruby",
    deps = [
        ":dataform_ruby_wrapper",
    ],
)
