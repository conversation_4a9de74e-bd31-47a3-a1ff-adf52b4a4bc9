# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "logging_proto",
    srcs = [
        "runtime_log.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_proto_library",
    "java_gapic_assembly_gradle_pkg",
)

java_proto_library(
    name = "logging_java_proto",
    deps = [":logging_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-notebooks-logging-v1-java",
    deps = [
        ":logging_proto",
        ":logging_java_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
    "go_gapic_assembly_pkg",
)

go_proto_library(
    name = "logging_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/notebooks/logging/apiv1/loggingpb",
    protos = [":logging_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_assembly_pkg(
    name = "google-cloud-notebooks-logging-v1-go",
    deps = [
        ":logging_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "logging_moved_proto",
    srcs = [":logging_proto"],
    deps = [
        "//google/api:field_behavior_proto",
    ],
)

py_proto_library(
    name = "logging_py_proto",
    deps = [":logging_moved_proto"],
)

py_grpc_library(
    name = "logging_py_grpc",
    srcs = [":logging_moved_proto"],
    deps = [":logging_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "logging_php_proto",
    deps = [":logging_proto"],
)

php_gapic_assembly_pkg(
    name = "google-cloud-notebooks-logging-v1-php",
    deps = [
        ":logging_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "logging_ruby_proto",
    deps = [":logging_proto"],
)

ruby_grpc_library(
    name = "logging_ruby_grpc",
    srcs = [":logging_proto"],
    deps = [":logging_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "logging_csharp_proto",
    deps = [":logging_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-notebooks-logging-v1-csharp",
    package_name = "Google.Cloud.Notebooks.Logging.V1",
    generate_nongapic_package = True,
    deps = [
        ":logging_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "logging_cc_proto",
    deps = [":logging_proto"],
)

cc_grpc_library(
    name = "logging_cc_grpc",
    srcs = [":logging_proto"],
    grpc_only = True,
    deps = [":logging_cc_proto"],
)
