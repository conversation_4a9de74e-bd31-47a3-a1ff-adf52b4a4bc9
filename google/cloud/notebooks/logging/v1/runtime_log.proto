// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.logging.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Notebooks.Logging.V1";
option go_package = "cloud.google.com/go/notebooks/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "RuntimeLogProto";
option java_package = "com.google.cloud.notebooks.logging.v1";

// Defines protos for runtime related platform logs

// Log content of an event related to a runtime.
message RuntimeEvent {
  // Defines event type.
  enum EventType {
    // Event is not specified.
    EVENT_TYPE_UNSPECIFIED = 0;

    // Runtime state has been updated.
    RUNTIME_STATE_CHANGE_EVENT = 1;
  }

  // Required. Type of event.
  EventType type = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Additional metadata for the event.
  map<string, string> details = 2 [(google.api.field_behavior) = OPTIONAL];
}
