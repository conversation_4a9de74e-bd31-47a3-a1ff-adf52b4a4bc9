{"methodConfig": [{"name": [{"service": "google.cloud.notebooks.v1beta1.NotebookService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "ListInstances"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "GetInstance"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "CreateInstance"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "RegisterInstance"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "SetInstanceAccelerator"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "SetInstanceMachineType"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "SetInstanceLabels"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "DeleteInstance"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "StartInstance"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "StopInstance"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "ResetInstance"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "ReportInstanceInfo"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "IsInstanceUpgradeable"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "UpgradeInstance"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "UpgradeInstanceInternal"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "ListEnvironments"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "GetEnvironment"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "CreateEnvironment"}, {"service": "google.cloud.notebooks.v1beta1.NotebookService", "method": "DeleteEnvironment"}], "timeout": "60s"}]}