{"methodConfig": [{"name": [{"service": "google.cloud.notebooks.v2.NotebookService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.notebooks.v2.NotebookService", "method": "ListInstances"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "GetInstance"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "CreateInstance"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "RegisterInstance"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "SetInstanceAccelerator"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "SetInstanceMachineType"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "SetInstanceLabels"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "UpdateInstanceConfig"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "DeleteInstance"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "StartInstance"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "StopInstance"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "ResetInstance"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "RollbackInstance"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "ReportInstanceInfo"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "IsInstanceUpgradeable"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "GetInstanceHealth"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "UpgradeInstance"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "UpgradeInstanceInternal"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "UpdateShieldedInstanceConfig"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "ListEnvironments"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "GetEnvironment"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "CreateEnvironment"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "DeleteEnvironment"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "PrepareReportInstanceEvent"}, {"service": "google.cloud.notebooks.v2.NotebookService", "method": "ReportInstanceEvent"}], "timeout": "60s"}, {"name": [{"service": "google.longrunning.Operations", "method": "ListOperations"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "300s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}