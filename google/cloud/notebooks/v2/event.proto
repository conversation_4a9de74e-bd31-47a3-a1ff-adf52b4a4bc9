// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v2;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V2";
option go_package = "cloud.google.com/go/notebooks/apiv2/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "EventProto";
option java_package = "com.google.cloud.notebooks.v2";
option php_namespace = "Google\\Cloud\\Notebooks\\V2";
option ruby_package = "Google::Cloud::Notebooks::V2";

// The definition of an Event for a managed / semi-managed notebook instance.
message Event {
  // The definition of the event types.
  enum EventType {
    // Event is not specified.
    EVENT_TYPE_UNSPECIFIED = 0;

    // The instance / runtime is idle
    IDLE = 1;

    // The instance / runtime is available.
    // This event indicates that instance / runtime underlying compute is
    // operational.
    HEARTBEAT = 2;

    // The instance / runtime health is available.
    // This event indicates that instance / runtime health information.
    HEALTH = 3;

    // The instance / runtime is available.
    // This event allows instance / runtime to send Host maintenance
    // information to Control Plane.
    // https://cloud.google.com/compute/docs/gpus/gpu-host-maintenance
    MAINTENANCE = 4;

    // The instance / runtime is available.
    // This event indicates that the instance had metadata that needs to be
    // modified.
    METADATA_CHANGE = 5;
  }

  // Optional. Event report time.
  google.protobuf.Timestamp report_time = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Event type.
  EventType type = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Event details. This field is used to pass event information.
  map<string, string> details = 3 [(google.api.field_behavior) = OPTIONAL];
}
