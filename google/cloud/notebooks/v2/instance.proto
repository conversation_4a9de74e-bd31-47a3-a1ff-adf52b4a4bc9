// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/notebooks/v2/gce_setup.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V2";
option go_package = "cloud.google.com/go/notebooks/apiv2/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "InstanceProto";
option java_package = "com.google.cloud.notebooks.v2";
option php_namespace = "Google\\Cloud\\Notebooks\\V2";
option ruby_package = "Google::Cloud::Notebooks::V2";

// The entry of VM image upgrade history.
message UpgradeHistoryEntry {
  // The definition of the states of this upgrade history entry.
  enum State {
    // State is not specified.
    STATE_UNSPECIFIED = 0;

    // The instance upgrade is started.
    STARTED = 1;

    // The instance upgrade is succeeded.
    SUCCEEDED = 2;

    // The instance upgrade is failed.
    FAILED = 3;
  }

  // The definition of operations of this upgrade history entry.
  enum Action {
    // Operation is not specified.
    ACTION_UNSPECIFIED = 0;

    // Upgrade.
    UPGRADE = 1;

    // Rollback.
    ROLLBACK = 2;
  }

  // Optional. The snapshot of the boot disk of this notebook instance before
  // upgrade.
  string snapshot = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The VM image before this instance upgrade.
  string vm_image = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The container image before this instance upgrade.
  string container_image = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The framework of this notebook instance.
  string framework = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The version of the notebook instance before this upgrade.
  string version = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The state of this instance upgrade history entry.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The time that this instance upgrade history entry is created.
  google.protobuf.Timestamp create_time = 7
      [(google.api.field_behavior) = IMMUTABLE];

  // Optional. Action. Rolloback or Upgrade.
  Action action = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Target VM Version, like m63.
  string target_version = 9 [(google.api.field_behavior) = OPTIONAL];
}

// The definition of a notebook instance.
message Instance {
  option (google.api.resource) = {
    type: "notebooks.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/instances/{instance}"
    plural: "instances"
    singular: "instance"
  };

  // Output only. The name of this notebook instance. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Setup for the Notebook instance.
  oneof infrastructure {
    // Optional. Compute Engine setup for the notebook. Uses notebook-defined
    // fields.
    GceSetup gce_setup = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. The proxy endpoint that is used to access the Jupyter
  // notebook.
  string proxy_uri = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Input only. The owner of this instance after creation. Format:
  // `<EMAIL>`
  //
  // Currently supports one owner only. If not specified, all of the service
  // account users of your VM instance's service account can use
  // the instance.
  repeated string instance_owners = 4 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Output only. Email address of entity that sent original CreateInstance
  // request.
  string creator = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The state of this instance.
  State state = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The upgrade history of this instance.
  repeated UpgradeHistoryEntry upgrade_history = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Unique ID of the resource.
  string id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Instance health_state.
  HealthState health_state = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about instance health.
  // Example:
  //
  //     healthInfo": {
  //       "docker_proxy_agent_status": "1",
  //       "docker_status": "1",
  //       "jupyterlab_api_status": "-1",
  //       "jupyterlab_status": "-1",
  //       "updated": "2020-10-18 09:40:03.573409"
  //     }
  map<string, string> health_info = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Instance creation time.
  google.protobuf.Timestamp create_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Instance update time.
  google.protobuf.Timestamp update_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. If true, the notebook instance will not register with the proxy.
  bool disable_proxy_access = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Labels to apply to this instance.
  // These can be later modified by the UpdateInstance method.
  map<string, string> labels = 14 [(google.api.field_behavior) = OPTIONAL];
}

// The definition of the states of this instance.
enum State {
  // State is not specified.
  STATE_UNSPECIFIED = 0;

  // The control logic is starting the instance.
  STARTING = 1;

  // The control logic is installing required frameworks and registering the
  // instance with notebook proxy
  PROVISIONING = 2;

  // The instance is running.
  ACTIVE = 3;

  // The control logic is stopping the instance.
  STOPPING = 4;

  // The instance is stopped.
  STOPPED = 5;

  // The instance is deleted.
  DELETED = 6;

  // The instance is upgrading.
  UPGRADING = 7;

  // The instance is being created.
  INITIALIZING = 8;

  // The instance is suspending.
  SUSPENDING = 9;

  // The instance is suspended.
  SUSPENDED = 10;
}

// The instance health state.
enum HealthState {
  // The instance substate is unknown.
  HEALTH_STATE_UNSPECIFIED = 0;

  // The instance is known to be in an healthy state
  // (for example, critical daemons are running)
  // Applies to ACTIVE state.
  HEALTHY = 1;

  // The instance is known to be in an unhealthy state
  // (for example, critical daemons are not running)
  // Applies to ACTIVE state.
  UNHEALTHY = 2;

  // The instance has not installed health monitoring agent.
  // Applies to ACTIVE state.
  AGENT_NOT_INSTALLED = 3;

  // The instance health monitoring agent is not running.
  // Applies to ACTIVE state.
  AGENT_NOT_RUNNING = 4;
}
