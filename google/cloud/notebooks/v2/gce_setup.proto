// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v2;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V2";
option go_package = "cloud.google.com/go/notebooks/apiv2/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "GceSetupProto";
option java_package = "com.google.cloud.notebooks.v2";
option php_namespace = "Google\\Cloud\\Notebooks\\V2";
option ruby_package = "Google::Cloud::Notebooks::V2";

// The definition of a network interface resource attached to a VM.
message NetworkInterface {
  // The type of vNIC driver.
  // Default should be NIC_TYPE_UNSPECIFIED.
  enum NicType {
    // No type specified.
    NIC_TYPE_UNSPECIFIED = 0;

    // VIRTIO
    VIRTIO_NET = 1;

    // GVNIC
    GVNIC = 2;
  }

  // Optional. The name of the VPC that this VM instance is in.
  // Format:
  // `projects/{project_id}/global/networks/{network_id}`
  string network = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the subnet that this VM instance is in.
  // Format:
  // `projects/{project_id}/regions/{region}/subnetworks/{subnetwork_id}`
  string subnet = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The type of vNIC to be used on this interface. This may be gVNIC
  // or VirtioNet.
  NicType nic_type = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Definition of a custom Compute Engine virtual machine image for starting a
// notebook instance with the environment installed directly on the VM.
message VmImage {
  // Required. The name of the Google Cloud project that this VM image belongs
  // to. Format: `{project_id}`
  string project = 1 [(google.api.field_behavior) = REQUIRED];

  // The reference to an external Compute Engine VM image.
  oneof image {
    // Optional. Use VM image name to find the image.
    string name = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Use this VM image family to find the image; the newest image in
    // this family will be used.
    string family = 3 [(google.api.field_behavior) = OPTIONAL];
  }
}

// Definition of a container image for starting a notebook instance with the
// environment installed in a container.
message ContainerImage {
  // Required. The path to the container image repository. For example:
  // `gcr.io/{project_id}/{image_name}`
  string repository = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The tag of the container image. If not specified, this defaults
  // to the latest tag.
  string tag = 2 [(google.api.field_behavior) = OPTIONAL];
}

// An accelerator configuration for a VM instance
// Definition of a hardware accelerator. Note that there is no check on `type`
// and `core_count` combinations. TPUs are not supported.
// See [GPUs on Compute
// Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a
// valid combination.
message AcceleratorConfig {
  // Definition of the types of hardware accelerators that can be used on
  // this instance.
  enum AcceleratorType {
    // Accelerator type is not specified.
    ACCELERATOR_TYPE_UNSPECIFIED = 0;

    // Accelerator type is Nvidia Tesla P100.
    NVIDIA_TESLA_P100 = 2;

    // Accelerator type is Nvidia Tesla V100.
    NVIDIA_TESLA_V100 = 3;

    // Accelerator type is Nvidia Tesla P4.
    NVIDIA_TESLA_P4 = 4;

    // Accelerator type is Nvidia Tesla T4.
    NVIDIA_TESLA_T4 = 5;

    // Accelerator type is Nvidia Tesla A100 - 40GB.
    NVIDIA_TESLA_A100 = 11;

    // Accelerator type is Nvidia Tesla A100 - 80GB.
    NVIDIA_A100_80GB = 12;

    // Accelerator type is Nvidia Tesla L4.
    NVIDIA_L4 = 13;

    // Accelerator type is NVIDIA Tesla T4 Virtual Workstations.
    NVIDIA_TESLA_T4_VWS = 8;

    // Accelerator type is NVIDIA Tesla P100 Virtual Workstations.
    NVIDIA_TESLA_P100_VWS = 9;

    // Accelerator type is NVIDIA Tesla P4 Virtual Workstations.
    NVIDIA_TESLA_P4_VWS = 10;
  }

  // Optional. Type of this accelerator.
  AcceleratorType type = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Count of cores of this accelerator.
  int64 core_count = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A set of Shielded Instance options.
// See [Images using supported Shielded VM
// features](https://cloud.google.com/compute/docs/instances/modifying-shielded-vm).
// Not all combinations are valid.
message ShieldedInstanceConfig {
  // Optional. Defines whether the VM instance has Secure Boot enabled.
  //
  // Secure Boot helps ensure that the system only runs authentic software by
  // verifying the digital signature of all boot components, and halting the
  // boot process if signature verification fails. Disabled by default.
  bool enable_secure_boot = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines whether the VM instance has the vTPM enabled. Enabled by
  // default.
  bool enable_vtpm = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Defines whether the VM instance has integrity monitoring enabled.
  //
  // Enables monitoring and attestation of the boot integrity of the VM
  // instance. The attestation is performed against the integrity policy
  // baseline. This baseline is initially derived from the implicitly trusted
  // boot image when the VM instance is created. Enabled by default.
  bool enable_integrity_monitoring = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A GPU driver configuration
message GPUDriverConfig {
  // Optional. Whether the end user authorizes Google Cloud to install GPU
  // driver on this VM instance. If this field is empty or set to false, the GPU
  // driver won't be installed. Only applicable to instances with GPUs.
  bool enable_gpu_driver = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specify a custom Cloud Storage path where the GPU driver is
  // stored. If not specified, we'll automatically choose from official GPU
  // drivers.
  string custom_gpu_driver_path = 2 [(google.api.field_behavior) = OPTIONAL];
}

// An instance-attached disk resource.
message DataDisk {
  // Optional. The size of the disk in GB attached to this VM instance, up to a
  // maximum of 64000 GB (64 TB). If not specified, this defaults to 100.
  int64 disk_size_gb = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Input only. Indicates the type of the disk.
  DiskType disk_type = 2 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Input only. Disk encryption method used on the boot and data
  // disks, defaults to GMEK.
  DiskEncryption disk_encryption = 5 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Input only. The KMS key used to encrypt the disks, only
  // applicable if disk_encryption is CMEK. Format:
  // `projects/{project_id}/locations/{location}/keyRings/{key_ring_id}/cryptoKeys/{key_id}`
  //
  // Learn more about using your own encryption keys.
  string kms_key = 6 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// The definition of a boot disk.
message BootDisk {
  // Optional. The size of the boot disk in GB attached to this instance, up to
  // a maximum of 64000 GB (64 TB). If not specified, this defaults to the
  // recommended value of 150GB.
  int64 disk_size_gb = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates the type of the disk.
  DiskType disk_type = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Input only. Disk encryption method used on the boot and data
  // disks, defaults to GMEK.
  DiskEncryption disk_encryption = 3 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Input only. The KMS key used to encrypt the disks, only
  // applicable if disk_encryption is CMEK. Format:
  // `projects/{project_id}/locations/{location}/keyRings/{key_ring_id}/cryptoKeys/{key_id}`
  //
  // Learn more about using your own encryption keys.
  string kms_key = 4 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// A service account that acts as an identity.
message ServiceAccount {
  // Optional. Email address of the service account.
  string email = 1 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The list of scopes to be made available for this service
  // account. Set by the CLH to https://www.googleapis.com/auth/cloud-platform
  repeated string scopes = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The definition of how to configure a VM instance outside of Resources and
// Identity.
message GceSetup {
  // Optional. The machine type of the VM instance.
  // https://cloud.google.com/compute/docs/machine-resource
  string machine_type = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The hardware accelerators used on this instance. If you use
  // accelerators, make sure that your configuration has
  // [enough vCPUs and memory to support the `machine_type` you have
  // selected](https://cloud.google.com/compute/docs/gpus/#gpus-list).
  // Currently supports only one accelerator configuration.
  repeated AcceleratorConfig accelerator_configs = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The service account that serves as an identity for the VM
  // instance. Currently supports only one service account.
  repeated ServiceAccount service_accounts = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Type of the image; can be one of VM image, or container image.
  oneof image {
    // Optional. Use a Compute Engine VM image to start the notebook instance.
    VmImage vm_image = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Use a container image to start the notebook instance.
    ContainerImage container_image = 5 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The boot disk for the VM.
  BootDisk boot_disk = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Data disks attached to the VM instance.
  // Currently supports only one data disk.
  repeated DataDisk data_disks = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Shielded VM configuration.
  // [Images using supported Shielded VM
  // features](https://cloud.google.com/compute/docs/instances/modifying-shielded-vm).
  ShieldedInstanceConfig shielded_instance_config = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The network interfaces for the VM.
  // Supports only one interface.
  repeated NetworkInterface network_interfaces = 9
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true, no external IP will be assigned to this VM instance.
  bool disable_public_ip = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Compute Engine tags to add to runtime (see [Tagging
  // instances](https://cloud.google.com/compute/docs/label-or-tag-resources#tags)).
  repeated string tags = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Custom metadata to apply to this instance.
  map<string, string> metadata = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Flag to enable ip forwarding or not, default false/off.
  // https://cloud.google.com/vpc/docs/using-routes#canipforward
  bool enable_ip_forwarding = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Configuration for GPU drivers.
  GPUDriverConfig gpu_driver_config = 14
      [(google.api.field_behavior) = OPTIONAL];
}

// Definition of the disk encryption options.
enum DiskEncryption {
  // Disk encryption is not specified.
  DISK_ENCRYPTION_UNSPECIFIED = 0;

  // Use Google managed encryption keys to encrypt the boot disk.
  GMEK = 1;

  // Use customer managed encryption keys to encrypt the boot disk.
  CMEK = 2;
}

// Possible disk types.
enum DiskType {
  // Disk type not set.
  DISK_TYPE_UNSPECIFIED = 0;

  // Standard persistent disk type.
  PD_STANDARD = 1;

  // SSD persistent disk type.
  PD_SSD = 2;

  // Balanced persistent disk type.
  PD_BALANCED = 3;

  // Extreme persistent disk type.
  PD_EXTREME = 4;
}
