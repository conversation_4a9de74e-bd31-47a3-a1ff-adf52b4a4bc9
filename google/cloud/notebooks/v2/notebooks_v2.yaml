type: google.api.Service
config_version: 3
name: notebooks.googleapis.com
title: Notebooks API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.notebooks.v2.NotebookService
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.notebooks.v2.OperationMetadata

documentation:
  summary: Notebooks API is used to manage notebook resources in Google Cloud.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: 'google.cloud.notebooks.v2.NotebookService.*'
    deadline: 60.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0
  - selector: google.longrunning.Operations.GetOperation
    deadline: 5.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v2/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v2/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v2/{resource=projects/*/locations/*/instances/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v2/{resource=projects/*/locations/*/instances/*}:setIamPolicy'
    body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v2/{resource=projects/*/locations/*/instances/*}:testIamPermissions'
    body: '*'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v2/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v2/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.notebooks.v2.NotebookService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1392625
  documentation_uri: https://cloud.google.com/vertex-ai/docs/workbench/instances/introduction
  api_short_name: notebooks
  github_label: 'api: notebooks'
  doc_tag_prefix: notebooks
  organization: CLOUD
  library_settings:
  - version: google.cloud.notebooks.v2
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/vertex-ai/docs/workbench/reference/rest
