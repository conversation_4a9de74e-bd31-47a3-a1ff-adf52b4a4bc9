// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/notebooks/v2/diagnostic_config.proto";
import "google/cloud/notebooks/v2/instance.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V2";
option go_package = "cloud.google.com/go/notebooks/apiv2/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "ServiceProto";
option java_package = "com.google.cloud.notebooks.v2";
option php_namespace = "Google\\Cloud\\Notebooks\\V2";
option ruby_package = "Google::Cloud::Notebooks::V2";

// API v2 service for Workbench Notebooks Instances.
service NotebookService {
  option (google.api.default_host) = "notebooks.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists instances in a given project and location.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*/locations/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Instance in a given project and location.
  rpc CreateInstance(CreateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*/locations/*}/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance,instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // UpdateInstance updates an Instance.
  rpc UpdateInstance(UpdateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v2/{instance.name=projects/*/locations/*/instances/*}"
      body: "instance"
    };
    option (google.api.method_signature) = "instance,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Instance.
  rpc DeleteInstance(DeleteInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Starts a notebook instance.
  rpc StartInstance(StartInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:start"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Stops a notebook instance.
  rpc StopInstance(StopInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:stop"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Resets a notebook instance.
  rpc ResetInstance(ResetInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:reset"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Checks whether a notebook instance is upgradable.
  rpc CheckInstanceUpgradability(CheckInstanceUpgradabilityRequest)
      returns (CheckInstanceUpgradabilityResponse) {
    option (google.api.http) = {
      get: "/v2/{notebook_instance=projects/*/locations/*/instances/*}:checkUpgradability"
    };
  }

  // Upgrades a notebook instance to the latest version.
  rpc UpgradeInstance(UpgradeInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:upgrade"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Rollbacks a notebook instance to the previous version.
  rpc RollbackInstance(RollbackInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:rollback"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a Diagnostic File and runs Diagnostic Tool given an Instance.
  rpc DiagnoseInstance(DiagnoseInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{name=projects/*/locations/*/instances/*}:diagnose"
      body: "*"
    };
    option (google.api.method_signature) = "name,diagnostic_config";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation, if any.
  string status_message = 5;

  // Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6;

  // API version used to start the operation.
  string api_version = 7;

  // API endpoint name of this operation.
  string endpoint = 8;
}

// Request for listing notebook instances.
message ListInstancesRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "notebooks.googleapis.com/Instance"
    }
  ];

  // Optional. Maximum return size of the list call.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A previous returned page token that can be used to continue
  // listing from the last result.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Sort results. Supported values are "name", "name desc" or ""
  // (unsorted).
  string order_by = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. List filter.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response for listing notebook instances.
message ListInstancesResponse {
  // A list of returned instances.
  repeated Instance instances = 1;

  // Page token that can be used to continue listing from the last result in the
  // next list call.
  string next_page_token = 2;

  // Locations that could not be reached. For example,
  // ['us-west1-a', 'us-central1-b'].
  // A ListInstancesResponse will only contain either instances or unreachables,
  repeated string unreachable = 3;
}

// Request for getting a notebook instance.
message GetInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Instance"
    }
  ];
}

// Request for creating a notebook instance.
message CreateInstanceRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "notebooks.googleapis.com/Instance"
    }
  ];

  // Required. User-defined unique ID of this instance.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The instance to be created.
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. Idempotent request UUID.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request for updating a notebook instance.
message UpdateInstanceRequest {
  // Required. A representation of an instance.
  Instance instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Mask used to update an instance
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Idempotent request UUID.
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request for deleting a notebook instance.
message DeleteInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Instance"
    }
  ];

  // Optional. Idempotent request UUID.
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for starting a notebook instance
message StartInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for stopping a notebook instance
message StopInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for resetting a notebook instance
message ResetInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for checking if a notebook instance is upgradeable.
message CheckInstanceUpgradabilityRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string notebook_instance = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response for checking if a notebook instance is upgradeable.
message CheckInstanceUpgradabilityResponse {
  // If an instance is upgradeable.
  bool upgradeable = 1;

  // The version this instance will be upgraded to if calling the upgrade
  // endpoint. This field will only be populated if field upgradeable is true.
  string upgrade_version = 2;

  // Additional information about upgrade.
  string upgrade_info = 3;

  // The new image self link this instance will be upgraded to if calling the
  // upgrade endpoint. This field will only be populated if field upgradeable
  // is true.
  string upgrade_image = 4;
}

// Request for upgrading a notebook instance
message UpgradeInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for rollbacking a notebook instance
message RollbackInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Instance"
    }
  ];

  // Required. The snapshot for rollback.
  // Example: "projects/test-project/global/snapshots/krwlzipynril".
  string target_snapshot = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Output only. Revision Id
  string revision_id = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = REQUIRED
  ];
}

// Request for creating a notebook instance diagnostic file.
message DiagnoseInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Instance"
    }
  ];

  // Required. Defines flags that are used to run the diagnostic tool
  DiagnosticConfig diagnostic_config = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Maxmium amount of time in minutes before the operation times out.
  int32 timeout_minutes = 3 [(google.api.field_behavior) = OPTIONAL];
}
