# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "notebooks_proto",
    srcs = [
        "diagnostic_config.proto",
        "environment.proto",
        "event.proto",
        "execution.proto",
        "instance.proto",
        "instance_config.proto",
        "managed_service.proto",
        "runtime.proto",
        "schedule.proto",
        "service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "notebooks_proto_with_info",
    deps = [
        ":notebooks_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "notebooks_java_proto",
    deps = [":notebooks_proto"],
)

java_grpc_library(
    name = "notebooks_java_grpc",
    srcs = [":notebooks_proto"],
    deps = [":notebooks_java_proto"],
)

java_gapic_library(
    name = "notebooks_java_gapic",
    srcs = [":notebooks_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "notebooks_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "notebooks_v1.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
        ":notebooks_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":notebooks_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "notebooks_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.notebooks.v1.ManagedNotebookServiceClientTest",
        "com.google.cloud.notebooks.v1.NotebookServiceClientTest",
    ],
    runtime_deps = [":notebooks_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-notebooks-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":notebooks_java_gapic",
        ":notebooks_java_grpc",
        ":notebooks_java_proto",
        ":notebooks_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "notebooks_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/notebooks/apiv1/notebookspb",
    protos = [":notebooks_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "notebooks_go_gapic",
    srcs = [":notebooks_proto_with_info"],
    grpc_service_config = "notebooks_grpc_service_config.json",
    importpath = "cloud.google.com/go/notebooks/apiv1;notebooks",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "notebooks_v1.yaml",
    transport = "grpc",
    deps = [
        ":notebooks_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-notebooks-v1-go",
    deps = [
        ":notebooks_go_gapic",
        ":notebooks_go_gapic_srcjar-metadata.srcjar",
        ":notebooks_go_gapic_srcjar-snippets.srcjar",
        ":notebooks_go_gapic_srcjar-test.srcjar",
        ":notebooks_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "notebooks_py_gapic",
    srcs = [":notebooks_proto"],
    grpc_service_config = "notebooks_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "notebooks_v1.yaml",
    transport = "grpc",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "notebooks_py_gapic_test",
    srcs = [
        "notebooks_py_gapic_pytest.py",
        "notebooks_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":notebooks_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "notebooks-v1-py",
    deps = [
        ":notebooks_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "notebooks_php_proto",
    deps = [":notebooks_proto"],
)

php_gapic_library(
    name = "notebooks_php_gapic",
    srcs = [":notebooks_proto_with_info"],
    grpc_service_config = "notebooks_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "notebooks_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":notebooks_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-notebooks-v1-php",
    deps = [
        ":notebooks_php_gapic",
        ":notebooks_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "notebooks_nodejs_gapic",
    package_name = "@google-cloud/notebooks",
    src = ":notebooks_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "notebooks_grpc_service_config.json",
    package = "google.cloud.notebooks.v1",
    rest_numeric_enums = True,
    service_yaml = "notebooks_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "notebooks-v1-nodejs",
    deps = [
        ":notebooks_nodejs_gapic",
        ":notebooks_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "notebooks_ruby_proto",
    deps = [":notebooks_proto"],
)

ruby_grpc_library(
    name = "notebooks_ruby_grpc",
    srcs = [":notebooks_proto"],
    deps = [":notebooks_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "notebooks_ruby_gapic",
    srcs = [":notebooks_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=notebooks.googleapis.com",
        "ruby-cloud-api-shortname=notebooks",
        "ruby-cloud-env-prefix=NOTEBOOKS",
        "ruby-cloud-gem-name=google-cloud-notebooks-v1",
        "ruby-cloud-product-url=https://cloud.google.com/ai-platform-notebooks",
    ],
    grpc_service_config = "notebooks_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "AI Platform Notebooks makes it easy to manage JupyterLab instances through a protected, publicly available notebook instance URL. A JupyterLab instance is a Deep Learning virtual machine instance with the latest machine learning and data science libraries pre-installed.",
    ruby_cloud_title = "AI Platform Notebooks V1",
    service_yaml = "notebooks_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":notebooks_ruby_grpc",
        ":notebooks_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-notebooks-v1-ruby",
    deps = [
        ":notebooks_ruby_gapic",
        ":notebooks_ruby_grpc",
        ":notebooks_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "notebooks_csharp_proto",
    deps = [":notebooks_proto"],
)

csharp_grpc_library(
    name = "notebooks_csharp_grpc",
    srcs = [":notebooks_proto"],
    deps = [":notebooks_csharp_proto"],
)

csharp_gapic_library(
    name = "notebooks_csharp_gapic",
    srcs = [":notebooks_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "notebooks_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "notebooks_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":notebooks_csharp_grpc",
        ":notebooks_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-notebooks-v1-csharp",
    deps = [
        ":notebooks_csharp_gapic",
        ":notebooks_csharp_grpc",
        ":notebooks_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "notebooks_cc_proto",
    deps = [":notebooks_proto"],
)

cc_grpc_library(
    name = "notebooks_cc_grpc",
    srcs = [":notebooks_proto"],
    grpc_only = True,
    deps = [":notebooks_cc_proto"],
)
