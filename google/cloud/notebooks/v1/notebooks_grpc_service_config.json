{"methodConfig": [{"name": [{"service": "google.cloud.notebooks.v1.NotebookService"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.notebooks.v1.NotebookService", "method": "ListInstances"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "GetInstance"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "CreateInstance"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "RegisterInstance"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "SetInstanceAccelerator"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "SetInstanceMachineType"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "SetInstanceLabels"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "UpdateInstanceConfig"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "DeleteInstance"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "StartInstance"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "StopInstance"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "ResetInstance"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "RollbackInstance"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "ReportInstanceInfo"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "IsInstanceUpgradeable"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "GetInstanceHealth"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "UpgradeInstance"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "UpgradeInstanceInternal"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "UpdateShieldedInstanceConfig"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "ListEnvironments"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "GetEnvironment"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "CreateEnvironment"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "DeleteEnvironment"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "ListSchedules"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "GetSchedule"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "CreateSchedule"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "DeleteSchedule"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "ListExecutions"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "GetExecution"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "CreateExecution"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "DeleteExecution"}, {"service": "google.cloud.notebooks.v1.NotebookService", "method": "DiagnoseInstance"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "ListRuntimes"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "GetRuntime"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "CreateRuntime"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "DeleteRuntime"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "StartRuntime"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "StopRuntime"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "SwitchRuntime"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "RollbackRuntime"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "ReportRuntimeEvent"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "UpdateRuntime"}, {"service": "google.cloud.notebooks.v1.ManagedNotebookService", "method": "DiagnoseRuntime"}], "timeout": "60s"}, {"name": [{"service": "google.longrunning.Operations", "method": "ListOperations"}], "timeout": "300s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "300s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "DEADLINE_EXCEEDED"]}}]}