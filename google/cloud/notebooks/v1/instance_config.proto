// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v1;

option csharp_namespace = "Google.Cloud.Notebooks.V1";
option go_package = "cloud.google.com/go/notebooks/apiv1/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "InstanceConfigProto";
option java_package = "com.google.cloud.notebooks.v1";
option php_namespace = "Google\\Cloud\\Notebooks\\V1";
option ruby_package = "Google::Cloud::Notebooks::V1";

// Notebook instance configurations that can be updated.
message InstanceConfig {
  // Cron expression in UTC timezone, used to schedule instance auto upgrade.
  // Please follow the [cron format](https://en.wikipedia.org/wiki/Cron).
  string notebook_upgrade_schedule = 1;

  // Verifies core internal services are running.
  bool enable_health_monitoring = 2;
}
