// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/notebooks/v1/execution.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/notebooks/apiv1/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "ScheduleProto";
option java_package = "com.google.cloud.notebooks.v1";

// The definition of a schedule.
message Schedule {
  option (google.api.resource) = {
    type: "notebooks.googleapis.com/Schedule"
    pattern: "projects/{project}/location/{location}/schedules/{schedule}"
  };

  // State of the job.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The job is executing normally.
    ENABLED = 1;

    // The job is paused by the user. It will not execute. A user can
    // intentionally pause the job using
    // [PauseJobRequest][].
    PAUSED = 2;

    // The job is disabled by the system due to error. The user
    // cannot directly set a job to be disabled.
    DISABLED = 3;

    // The job state resulting from a failed [CloudScheduler.UpdateJob][]
    // operation. To recover a job from this state, retry
    // [CloudScheduler.UpdateJob][] until a successful response is received.
    UPDATE_FAILED = 4;

    // The schedule resource is being created.
    INITIALIZING = 5;

    // The schedule resource is being deleted.
    DELETING = 6;
  }

  // Output only. The name of this schedule. Format:
  // `projects/{project_id}/locations/{location}/schedules/{schedule_id}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Display name used for UI purposes.
  // Name can only contain alphanumeric characters, hyphens `-`,
  // and underscores `_`.
  string display_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A brief description of this environment.
  string description = 3;

  State state = 4;

  // Cron-tab formatted schedule by which the job will execute.
  // Format: minute, hour, day of month, month, day of week,
  // e.g. `0 0 * * WED` = every Wednesday
  // More examples: https://crontab.guru/examples.html
  string cron_schedule = 5;

  // Timezone on which the cron_schedule.
  // The value of this field must be a time zone name from the tz database.
  // TZ Database: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
  //
  // Note that some time zones include a provision for daylight savings time.
  // The rules for daylight saving time are determined by the chosen tz.
  // For UTC use the string "utc". If a time zone is not specified,
  // the default will be in UTC (also known as GMT).
  string time_zone = 6;

  // Output only. Time the schedule was created.
  google.protobuf.Timestamp create_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time the schedule was last updated.
  google.protobuf.Timestamp update_time = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Notebook Execution Template corresponding to this schedule.
  ExecutionTemplate execution_template = 9;

  // Output only. The most recent execution names triggered from this schedule and their
  // corresponding states.
  repeated Execution recent_executions = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}
