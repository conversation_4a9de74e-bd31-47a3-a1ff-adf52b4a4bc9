// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/notebooks/v1/diagnostic_config.proto";
import "google/cloud/notebooks/v1/environment.proto";
import "google/cloud/notebooks/v1/execution.proto";
import "google/cloud/notebooks/v1/instance.proto";
import "google/cloud/notebooks/v1/instance_config.proto";
import "google/cloud/notebooks/v1/schedule.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V1";
option go_package = "cloud.google.com/go/notebooks/apiv1/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "NotebooksProto";
option java_package = "com.google.cloud.notebooks.v1";
option php_namespace = "Google\\Cloud\\Notebooks\\V1";
option ruby_package = "Google::Cloud::Notebooks::V1";

// API v1 service for Cloud AI Platform Notebooks.
service NotebookService {
  option (google.api.default_host) = "notebooks.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists instances in a given project and location.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Instance in a given project and location.
  rpc CreateInstance(CreateInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance,instance_id";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Registers an existing legacy notebook instance to the Notebooks API server.
  // Legacy instances are instances created with the legacy Compute Engine
  // calls. They are not manageable by the Notebooks API out of the box. This
  // call makes these instances manageable by the Notebooks API.
  rpc RegisterInstance(RegisterInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/instances:register"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the guest accelerators of a single Instance.
  rpc SetInstanceAccelerator(SetInstanceAcceleratorRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/instances/*}:setAccelerator"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the machine type of a single Instance.
  rpc SetInstanceMachineType(SetInstanceMachineTypeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/instances/*}:setMachineType"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Update Notebook Instance configurations.
  rpc UpdateInstanceConfig(UpdateInstanceConfigRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/instances/*}:updateConfig"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the Shielded instance configuration of a single Instance.
  rpc UpdateShieldedInstanceConfig(UpdateShieldedInstanceConfigRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/instances/*}:updateShieldedInstanceConfig"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Replaces all the labels of an Instance.
  rpc SetInstanceLabels(SetInstanceLabelsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/instances/*}:setLabels"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Add/update metadata items for an instance.
  rpc UpdateInstanceMetadataItems(UpdateInstanceMetadataItemsRequest) returns (UpdateInstanceMetadataItemsResponse) {
    option (google.api.http) = {
      patch: "/v1/{name=projects/*/locations/*/instances/*}:updateMetadataItems"
      body: "*"
    };
  }

  // Deletes a single Instance.
  rpc DeleteInstance(DeleteInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Starts a notebook instance.
  rpc StartInstance(StartInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:start"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Stops a notebook instance.
  rpc StopInstance(StopInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:stop"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Resets a notebook instance.
  rpc ResetInstance(ResetInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:reset"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Allows notebook instances to
  // report their latest instance information to the Notebooks
  // API server. The server will merge the reported information to
  // the instance metadata store. Do not use this method directly.
  rpc ReportInstanceInfo(ReportInstanceInfoRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:report"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Check if a notebook instance is upgradable.
  rpc IsInstanceUpgradeable(IsInstanceUpgradeableRequest) returns (IsInstanceUpgradeableResponse) {
    option (google.api.http) = {
      get: "/v1/{notebook_instance=projects/*/locations/*/instances/*}:isUpgradeable"
    };
  }

  // Check if a notebook instance is healthy.
  rpc GetInstanceHealth(GetInstanceHealthRequest) returns (GetInstanceHealthResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*}:getInstanceHealth"
    };
    option (google.api.method_signature) = "name";
  }

  // Upgrades a notebook instance to the latest version.
  rpc UpgradeInstance(UpgradeInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:upgrade"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Rollbacks a notebook instance to the previous version.
  rpc RollbackInstance(RollbackInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:rollback"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a Diagnostic File and runs Diagnostic Tool given an Instance.
  rpc DiagnoseInstance(DiagnoseInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:diagnose"
      body: "*"
    };
    option (google.api.method_signature) = "name,diagnostic_config";
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Allows notebook instances to
  // call this endpoint to upgrade themselves. Do not use this method directly.
  rpc UpgradeInstanceInternal(UpgradeInstanceInternalRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:upgradeInternal"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists environments in a project.
  rpc ListEnvironments(ListEnvironmentsRequest) returns (ListEnvironmentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/environments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Environment.
  rpc GetEnvironment(GetEnvironmentRequest) returns (Environment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/environments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Environment.
  rpc CreateEnvironment(CreateEnvironmentRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/environments"
      body: "environment"
    };
    option (google.api.method_signature) = "parent,environment,environment_id";
    option (google.longrunning.operation_info) = {
      response_type: "Environment"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Environment.
  rpc DeleteEnvironment(DeleteEnvironmentRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/environments/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists schedules in a given project and location.
  rpc ListSchedules(ListSchedulesRequest) returns (ListSchedulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/schedules"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of schedule
  rpc GetSchedule(GetScheduleRequest) returns (Schedule) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/schedules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes schedule and all underlying jobs
  rpc DeleteSchedule(DeleteScheduleRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/schedules/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new Scheduled Notebook in a given project and location.
  rpc CreateSchedule(CreateScheduleRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/schedules"
      body: "schedule"
    };
    option (google.api.method_signature) = "parent,schedule,schedule_id";
    option (google.longrunning.operation_info) = {
      response_type: "Schedule"
      metadata_type: "OperationMetadata"
    };
  }

  // Triggers execution of an existing schedule.
  rpc TriggerSchedule(TriggerScheduleRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/schedules/*}:trigger"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Schedule"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists executions in a given project and location
  rpc ListExecutions(ListExecutionsRequest) returns (ListExecutionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/executions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of executions
  rpc GetExecution(GetExecutionRequest) returns (Execution) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/executions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes execution
  rpc DeleteExecution(DeleteExecutionRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/executions/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new Execution in a given project and location.
  rpc CreateExecution(CreateExecutionRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/executions"
      body: "execution"
    };
    option (google.api.method_signature) = "parent,execution,execution_id";
    option (google.longrunning.operation_info) = {
      response_type: "Execution"
      metadata_type: "OperationMetadata"
    };
  }
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation, if any.
  string status_message = 5;

  // Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6;

  // API version used to start the operation.
  string api_version = 7;

  // API endpoint name of this operation.
  string endpoint = 8;
}

// Request for listing notebook instances.
message ListInstancesRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Maximum return size of the list call.
  int32 page_size = 2;

  // A previous returned page token that can be used to continue listing
  // from the last result.
  string page_token = 3;
}

// Response for listing notebook instances.
message ListInstancesResponse {
  // A list of returned instances.
  repeated Instance instances = 1;

  // Page token that can be used to continue listing from the last result in the
  // next list call.
  string next_page_token = 2;

  // Locations that could not be reached. For example,
  // `['us-west1-a', 'us-central1-b']`.
  // A ListInstancesResponse will only contain either instances or unreachables,
  repeated string unreachable = 3;
}

// Request for getting a notebook instance.
message GetInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for creating a notebook instance.
message CreateInstanceRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. User-defined unique ID of this instance.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The instance to be created.
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for registering a notebook instance.
message RegisterInstanceRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. User defined unique ID of this instance. The `instance_id` must
  // be 1 to 63 characters long and contain only lowercase letters,
  // numeric characters, and dashes. The first character must be a lowercase
  // letter and the last character cannot be a dash.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for setting instance accelerator.
message SetInstanceAcceleratorRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Type of this accelerator.
  Instance.AcceleratorType type = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Count of cores of this accelerator. Note that not all combinations
  // of `type` and `core_count` are valid. Check [GPUs on
  // Compute Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to
  // find a valid combination. TPUs are not supported.
  int64 core_count = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for setting instance machine type.
message SetInstanceMachineTypeRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The [Compute Engine machine
  // type](https://cloud.google.com/compute/docs/machine-types).
  string machine_type = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for updating instance configurations.
message UpdateInstanceConfigRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // The instance configurations to be updated.
  InstanceConfig config = 2;
}

// Request for setting instance labels.
message SetInstanceLabelsRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Labels to apply to this instance.
  // These can be later modified by the setLabels method
  map<string, string> labels = 2;
}

// Request for adding/changing metadata items  for an instance.
message UpdateInstanceMetadataItemsRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Metadata items to add/update for the instance.
  map<string, string> items = 2;
}

// Response for adding/changing metadata items for an instance.
message UpdateInstanceMetadataItemsResponse {
  // Map of items that were added/updated to/in the metadata.
  map<string, string> items = 1;
}

// Request for updating the Shielded Instance config for a notebook instance.
// You can only use this method on a stopped instance
message UpdateShieldedInstanceConfigRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // ShieldedInstance configuration to be updated.
  Instance.ShieldedInstanceConfig shielded_instance_config = 2;
}

// Request for deleting a notebook instance.
message DeleteInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for starting a notebook instance
message StartInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for stopping a notebook instance
message StopInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for resetting a notebook instance
message ResetInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for notebook instances to report information to Notebooks API.
message ReportInstanceInfoRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The VM hardware token for authenticating the VM.
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  string vm_id = 2 [(google.api.field_behavior) = REQUIRED];

  // The metadata reported to Notebooks API. This will be merged to the instance
  // metadata store
  map<string, string> metadata = 3;
}

// Request for checking if a notebook instance is upgradeable.
message IsInstanceUpgradeableRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string notebook_instance = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The optional UpgradeType. Setting this field will search for additional
  // compute images to upgrade this instance.
  UpgradeType type = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Response for checking if a notebook instance is upgradeable.
message IsInstanceUpgradeableResponse {
  // If an instance is upgradeable.
  bool upgradeable = 1;

  // The version this instance will be upgraded to if calling the upgrade
  // endpoint. This field will only be populated if field upgradeable is true.
  string upgrade_version = 2;

  // Additional information about upgrade.
  string upgrade_info = 3;

  // The new image self link this instance will be upgraded to if calling the
  // upgrade endpoint. This field will only be populated if field upgradeable
  // is true.
  string upgrade_image = 4;
}

// Request for checking if a notebook instance is healthy.
message GetInstanceHealthRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Instance"
    }
  ];
}

// Response for checking if a notebook instance is healthy.
message GetInstanceHealthResponse {
  // If an instance is healthy or not.
  enum HealthState {
    // The instance substate is unknown.
    HEALTH_STATE_UNSPECIFIED = 0;

    // The instance is known to be in an healthy state
    // (for example, critical daemons are running)
    // Applies to ACTIVE state.
    HEALTHY = 1;

    // The instance is known to be in an unhealthy state
    // (for example, critical daemons are not running)
    // Applies to ACTIVE state.
    UNHEALTHY = 2;

    // The instance has not installed health monitoring agent.
    // Applies to ACTIVE state.
    AGENT_NOT_INSTALLED = 3;

    // The instance health monitoring agent is not running.
    // Applies to ACTIVE state.
    AGENT_NOT_RUNNING = 4;
  }

  // Output only. Runtime health_state.
  HealthState health_state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about instance health.
  // Example:
  //     healthInfo": {
  //       "docker_proxy_agent_status": "1",
  //       "docker_status": "1",
  //       "jupyterlab_api_status": "-1",
  //       "jupyterlab_status": "-1",
  //       "updated": "2020-10-18 09:40:03.573409"
  //     }
  map<string, string> health_info = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request for upgrading a notebook instance
message UpgradeInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The optional UpgradeType. Setting this field will search for additional
  // compute images to upgrade this instance.
  UpgradeType type = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for rollbacking a notebook instance
message RollbackInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The snapshot for rollback.
  // Example: `projects/test-project/global/snapshots/krwlzipynril`.
  string target_snapshot = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for upgrading a notebook instance from within the VM
message UpgradeInstanceInternalRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The VM hardware token for authenticating the VM.
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  string vm_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The optional UpgradeType. Setting this field will search for additional
  // compute images to upgrade this instance.
  UpgradeType type = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request for listing environments.
message ListEnvironmentsRequest {
  // Required. Format: `projects/{project_id}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Maximum return size of the list call.
  int32 page_size = 2;

  // A previous returned page token that can be used to continue listing from
  // the last result.
  string page_token = 3;
}

// Request for creating a notebook instance diagnostic file.
message DiagnoseInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Instance"
    }
  ];

  // Required. Defines flags that are used to run the diagnostic tool
  DiagnosticConfig diagnostic_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response for listing environments.
message ListEnvironmentsResponse {
  // A list of returned environments.
  repeated Environment environments = 1;

  // A page token that can be used to continue listing from the last result
  // in the next list call.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for getting a notebook environment.
message GetEnvironmentRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/environments/{environment_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for creating a notebook environment.
message CreateEnvironmentRequest {
  // Required. Format: `projects/{project_id}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. User-defined unique ID of this environment. The `environment_id` must
  // be 1 to 63 characters long and contain only lowercase letters,
  // numeric characters, and dashes. The first character must be a lowercase
  // letter and the last character cannot be a dash.
  string environment_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The environment to be created.
  Environment environment = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for deleting a notebook environment.
message DeleteEnvironmentRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/environments/{environment_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for listing scheduled notebook job.
message ListSchedulesRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Schedule"
    }
  ];

  // Maximum return size of the list call.
  int32 page_size = 2;

  // A previous returned page token that can be used to continue listing
  // from the last result.
  string page_token = 3;

  // Filter applied to resulting schedules.
  string filter = 4;

  // Field to order results by.
  string order_by = 5;
}

// Response for listing scheduled notebook job.
message ListSchedulesResponse {
  // A list of returned instances.
  repeated Schedule schedules = 1;

  // Page token that can be used to continue listing from the last result in the
  // next list call.
  string next_page_token = 2;

  // Schedules that could not be reached. For example:
  //
  //     ['projects/{project_id}/location/{location}/schedules/monthly_digest',
  //      'projects/{project_id}/location/{location}/schedules/weekly_sentiment']
  repeated string unreachable = 3;
}

// Request for getting scheduled notebook.
message GetScheduleRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/schedules/{schedule_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Schedule"
    }
  ];
}

// Request for deleting an Schedule
message DeleteScheduleRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/schedules/{schedule_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Schedule"
    }
  ];
}

// Request for created scheduled notebooks
message CreateScheduleRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Schedule"
    }
  ];

  // Required. User-defined unique ID of this schedule.
  string schedule_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The schedule to be created.
  Schedule schedule = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for created scheduled notebooks
message TriggerScheduleRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}/schedules/{schedule_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Schedule"
    }
  ];
}

// Request for listing scheduled notebook executions.
message ListExecutionsRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Execution"
    }
  ];

  // Maximum return size of the list call.
  int32 page_size = 2;

  // A previous returned page token that can be used to continue listing
  // from the last result.
  string page_token = 3;

  // Filter applied to resulting executions. Currently only supports filtering
  // executions by a specified `schedule_id`.
  // Format: `schedule_id=<Schedule_ID>`
  string filter = 4;

  // Sort by field.
  string order_by = 5;
}

// Response for listing scheduled notebook executions
message ListExecutionsResponse {
  // A list of returned instances.
  repeated Execution executions = 1;

  // Page token that can be used to continue listing from the last result in the
  // next list call.
  string next_page_token = 2;

  // Executions IDs that could not be reached. For example:
  //
  //     ['projects/{project_id}/location/{location}/executions/imagenet_test1',
  //      'projects/{project_id}/location/{location}/executions/classifier_train1']
  repeated string unreachable = 3;
}

// Request for getting scheduled notebook execution
message GetExecutionRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/executions/{execution_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Execution"
    }
  ];
}

// Request for deleting a scheduled notebook execution
message DeleteExecutionRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/executions/{execution_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Execution"
    }
  ];
}

// Request to create notebook execution
message CreateExecutionRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Execution"
    }
  ];

  // Required. User-defined unique ID of this execution.
  string execution_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The execution to be created.
  Execution execution = 3 [(google.api.field_behavior) = REQUIRED];
}

// Definition of the types of upgrade that can be used on this
// instance.
enum UpgradeType {
  // Upgrade type is not specified.
  UPGRADE_TYPE_UNSPECIFIED = 0;

  // Upgrade ML framework.
  UPGRADE_FRAMEWORK = 1;

  // Upgrade Operating System.
  UPGRADE_OS = 2;

  // Upgrade CUDA.
  UPGRADE_CUDA = 3;

  // Upgrade All (OS, Framework and CUDA).
  UPGRADE_ALL = 4;
}
