// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/notebooks/v1/environment.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V1";
option go_package = "cloud.google.com/go/notebooks/apiv1/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "InstanceProto";
option java_package = "com.google.cloud.notebooks.v1";
option php_namespace = "Google\\Cloud\\Notebooks\\V1";
option ruby_package = "Google::Cloud::Notebooks::V1";

// Reservation Affinity for consuming Zonal reservation.
message ReservationAffinity {
  // Indicates whether to consume capacity from an reservation or not.
  enum Type {
    // Default type.
    TYPE_UNSPECIFIED = 0;

    // Do not consume from any allocated capacity.
    NO_RESERVATION = 1;

    // Consume any reservation available.
    ANY_RESERVATION = 2;

    // Must consume from a specific reservation. Must specify key value fields
    // for specifying the reservations.
    SPECIFIC_RESERVATION = 3;
  }

  // Optional. Type of reservation to consume
  Type consume_reservation_type = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Corresponds to the label key of reservation resource.
  string key = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Corresponds to the label values of reservation resource.
  repeated string values = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The definition of a notebook instance.
message Instance {
  option (google.api.resource) = {
    type: "notebooks.googleapis.com/Instance"
    pattern: "projects/{project}/instances/{instance}"
  };

  // Definition of the types of hardware accelerators that can be used on this
  // instance.
  enum AcceleratorType {
    // Accelerator type is not specified.
    ACCELERATOR_TYPE_UNSPECIFIED = 0;

    // Accelerator type is Nvidia Tesla K80.
    NVIDIA_TESLA_K80 = 1;

    // Accelerator type is Nvidia Tesla P100.
    NVIDIA_TESLA_P100 = 2;

    // Accelerator type is Nvidia Tesla V100.
    NVIDIA_TESLA_V100 = 3;

    // Accelerator type is Nvidia Tesla P4.
    NVIDIA_TESLA_P4 = 4;

    // Accelerator type is Nvidia Tesla T4.
    NVIDIA_TESLA_T4 = 5;

    // Accelerator type is Nvidia Tesla A100.
    NVIDIA_TESLA_A100 = 11;

    // Accelerator type is NVIDIA Tesla T4 Virtual Workstations.
    NVIDIA_TESLA_T4_VWS = 8;

    // Accelerator type is NVIDIA Tesla P100 Virtual Workstations.
    NVIDIA_TESLA_P100_VWS = 9;

    // Accelerator type is NVIDIA Tesla P4 Virtual Workstations.
    NVIDIA_TESLA_P4_VWS = 10;

    // (Coming soon) Accelerator type is TPU V2.
    TPU_V2 = 6;

    // (Coming soon) Accelerator type is TPU V3.
    TPU_V3 = 7;
  }

  // Definition of a hardware accelerator. Note that not all combinations
  // of `type` and `core_count` are valid. Check [GPUs on Compute
  // Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a
  // valid combination. TPUs are not supported.
  message AcceleratorConfig {
    // Type of this accelerator.
    AcceleratorType type = 1;

    // Count of cores of this accelerator.
    int64 core_count = 2;
  }

  // The definition of the states of this instance.
  enum State {
    // State is not specified.
    STATE_UNSPECIFIED = 0;

    // The control logic is starting the instance.
    STARTING = 1;

    // The control logic is installing required frameworks and registering the
    // instance with notebook proxy
    PROVISIONING = 2;

    // The instance is running.
    ACTIVE = 3;

    // The control logic is stopping the instance.
    STOPPING = 4;

    // The instance is stopped.
    STOPPED = 5;

    // The instance is deleted.
    DELETED = 6;

    // The instance is upgrading.
    UPGRADING = 7;

    // The instance is being created.
    INITIALIZING = 8;

    // The instance is getting registered.
    REGISTERING = 9;

    // The instance is suspending.
    SUSPENDING = 10;

    // The instance is suspended.
    SUSPENDED = 11;
  }

  // Possible disk types for notebook instances.
  enum DiskType {
    // Disk type not set.
    DISK_TYPE_UNSPECIFIED = 0;

    // Standard persistent disk type.
    PD_STANDARD = 1;

    // SSD persistent disk type.
    PD_SSD = 2;

    // Balanced persistent disk type.
    PD_BALANCED = 3;

    // Extreme persistent disk type.
    PD_EXTREME = 4;
  }

  // Definition of the disk encryption options.
  enum DiskEncryption {
    // Disk encryption is not specified.
    DISK_ENCRYPTION_UNSPECIFIED = 0;

    // Use Google managed encryption keys to encrypt the boot disk.
    GMEK = 1;

    // Use customer managed encryption keys to encrypt the boot disk.
    CMEK = 2;
  }

  // An instance-attached disk resource.
  message Disk {
    // Guest OS features for boot disk.
    message GuestOsFeature {
      // The ID of a supported feature. Read  Enabling guest operating system
      // features to see a list of available options.
      // Valid values:
      //
      // * `FEATURE_TYPE_UNSPECIFIED`
      // * `MULTI_IP_SUBNET`
      // * `SECURE_BOOT`
      // * `UEFI_COMPATIBLE`
      // * `VIRTIO_SCSI_MULTIQUEUE`
      // * `WINDOWS`
      string type = 1;
    }

    // Indicates whether the disk will be auto-deleted when the instance is
    // deleted (but not when the disk is detached from the instance).
    bool auto_delete = 1;

    // Indicates that this is a boot disk. The virtual machine will use the
    // first partition of the disk for its root filesystem.
    bool boot = 2;

    // Indicates a unique device name of your choice that is reflected into the
    // `/dev/disk/by-id/google-*` tree of a Linux operating system running
    // within the instance. This name can be used to reference the device for
    // mounting, resizing, and so on, from within the instance.
    //
    // If not specified, the server chooses a default device name to apply to
    // this disk, in the form persistent-disk-x, where x is a number assigned by
    // Google Compute Engine.This field is only applicable for persistent disks.
    string device_name = 3;

    // Indicates the size of the disk in base-2 GB.
    int64 disk_size_gb = 4;

    // Indicates a list of features to enable on the guest operating system.
    // Applicable only for bootable images. Read  Enabling guest operating
    // system features to see a list of available options.
    repeated GuestOsFeature guest_os_features = 5;

    // A zero-based index to this disk, where 0 is reserved for the
    // boot disk. If you have many disks attached to an instance, each disk
    // would have a unique index number.
    int64 index = 6;

    // Indicates the disk interface to use for attaching this disk, which is
    // either SCSI or NVME. The default is SCSI. Persistent disks must always
    // use SCSI and the request will fail if you attempt to attach a persistent
    // disk in any other format than SCSI. Local SSDs can use either NVME or
    // SCSI. For performance characteristics of SCSI over NVMe, see Local SSD
    // performance.
    // Valid values:
    //
    // * `NVME`
    // * `SCSI`
    string interface = 7;

    // Type of the resource. Always compute#attachedDisk for attached
    // disks.
    string kind = 8;

    // A list of publicly visible licenses. Reserved for Google's use.
    // A License represents billing and aggregate usage data for public
    // and marketplace images.
    repeated string licenses = 9;

    // The mode in which to attach this disk, either `READ_WRITE` or
    // `READ_ONLY`. If not specified, the default is to attach the disk in
    // `READ_WRITE` mode. Valid values:
    //
    // * `READ_ONLY`
    // * `READ_WRITE`
    string mode = 10;

    // Indicates a valid partial or full URL to an existing Persistent Disk
    // resource.
    string source = 11;

    // Indicates the type of the disk, either `SCRATCH` or `PERSISTENT`.
    // Valid values:
    //
    // * `PERSISTENT`
    // * `SCRATCH`
    string type = 12;
  }

  // A set of Shielded Instance options.
  // Check [Images using supported Shielded VM
  // features](https://cloud.google.com/compute/docs/instances/modifying-shielded-vm).
  // Not all combinations are valid.
  message ShieldedInstanceConfig {
    // Defines whether the instance has Secure Boot enabled.
    //
    // Secure Boot helps ensure that the system only runs authentic software by
    // verifying the digital signature of all boot components, and halting the
    // boot process if signature verification fails. Disabled by default.
    bool enable_secure_boot = 1;

    // Defines whether the instance has the vTPM enabled. Enabled by default.
    bool enable_vtpm = 2;

    // Defines whether the instance has integrity monitoring enabled.
    //
    // Enables monitoring and attestation of the boot integrity of the instance.
    // The attestation is performed against the integrity policy baseline. This
    // baseline is initially derived from the implicitly trusted boot image when
    // the instance is created. Enabled by default.
    bool enable_integrity_monitoring = 3;
  }

  // The entry of VM image upgrade history.
  message UpgradeHistoryEntry {
    // The definition of the states of this upgrade history entry.
    enum State {
      // State is not specified.
      STATE_UNSPECIFIED = 0;

      // The instance upgrade is started.
      STARTED = 1;

      // The instance upgrade is succeeded.
      SUCCEEDED = 2;

      // The instance upgrade is failed.
      FAILED = 3;
    }

    // The definition of operations of this upgrade history entry.
    enum Action {
      // Operation is not specified.
      ACTION_UNSPECIFIED = 0;

      // Upgrade.
      UPGRADE = 1;

      // Rollback.
      ROLLBACK = 2;
    }

    // The snapshot of the boot disk of this notebook instance before upgrade.
    string snapshot = 1;

    // The VM image before this instance upgrade.
    string vm_image = 2;

    // The container image before this instance upgrade.
    string container_image = 3;

    // The framework of this notebook instance.
    string framework = 4;

    // The version of the notebook instance before this upgrade.
    string version = 5;

    // The state of this instance upgrade history entry.
    State state = 6;

    // The time that this instance upgrade history entry is created.
    google.protobuf.Timestamp create_time = 7;

    // Target VM Image. Format: `ainotebooks-vm/project/image-name/name`.
    string target_image = 8 [deprecated = true];

    // Action. Rolloback or Upgrade.
    Action action = 9;

    // Target VM Version, like m63.
    string target_version = 10;
  }

  // The type of vNIC driver.
  // Default should be UNSPECIFIED_NIC_TYPE.
  enum NicType {
    // No type specified.
    UNSPECIFIED_NIC_TYPE = 0;

    // VIRTIO
    VIRTIO_NET = 1;

    // GVNIC
    GVNIC = 2;
  }

  // Output only. The name of this notebook instance. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Type of the environment; can be one of VM image, or container image.
  oneof environment {
    // Use a Compute Engine VM image to start the notebook instance.
    VmImage vm_image = 2;

    // Use a container image to start the notebook instance.
    ContainerImage container_image = 3;
  }

  // Path to a Bash script that automatically runs after a notebook instance
  // fully boots up. The path must be a URL or
  // Cloud Storage path (`gs://path-to-file/file-name`).
  string post_startup_script = 4;

  // Output only. The proxy endpoint that is used to access the Jupyter notebook.
  string proxy_uri = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. The owner of this instance after creation. Format: `<EMAIL>`
  //
  // Currently supports one owner only. If not specified, all of the service
  // account users of your VM instance's service account can use
  // the instance.
  repeated string instance_owners = 6 [(google.api.field_behavior) = INPUT_ONLY];

  // The service account on this instance, giving access to other Google
  // Cloud services.
  // You can use any service account within the same project, but you
  // must have the service account user permission to use the instance.
  //
  // If not specified, the [Compute Engine default service
  // account](https://cloud.google.com/compute/docs/access/service-accounts#default_service_account)
  // is used.
  string service_account = 7;

  // Optional. The URIs of service account scopes to be included in
  // Compute Engine instances.
  //
  // If not specified, the following
  // [scopes](https://cloud.google.com/compute/docs/access/service-accounts#accesscopesiam)
  // are defined:
  //  - https://www.googleapis.com/auth/cloud-platform
  //  - https://www.googleapis.com/auth/userinfo.email
  // If not using default scopes, you need at least:
  //    https://www.googleapis.com/auth/compute
  repeated string service_account_scopes = 31 [(google.api.field_behavior) = OPTIONAL];

  // Required. The [Compute Engine machine
  // type](https://cloud.google.com/compute/docs/machine-types) of this
  // instance.
  string machine_type = 8 [(google.api.field_behavior) = REQUIRED];

  // The hardware accelerator used on this instance. If you use
  // accelerators, make sure that your configuration has
  // [enough vCPUs and memory to support the `machine_type` you have
  // selected](https://cloud.google.com/compute/docs/gpus/#gpus-list).
  AcceleratorConfig accelerator_config = 9;

  // Output only. The state of this instance.
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether the end user authorizes Google Cloud to install GPU driver
  // on this instance.
  // If this field is empty or set to false, the GPU driver won't be installed.
  // Only applicable to instances with GPUs.
  bool install_gpu_driver = 11;

  // Specify a custom Cloud Storage path where the GPU driver is stored.
  // If not specified, we'll automatically choose from official GPU drivers.
  string custom_gpu_driver_path = 12;

  // Input only. The type of the boot disk attached to this instance, defaults to
  // standard persistent disk (`PD_STANDARD`).
  DiskType boot_disk_type = 13 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The size of the boot disk in GB attached to this instance, up to a maximum
  // of 64000 GB (64 TB). The minimum recommended value is 100 GB. If not
  // specified, this defaults to 100.
  int64 boot_disk_size_gb = 14 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The type of the data disk attached to this instance, defaults to
  // standard persistent disk (`PD_STANDARD`).
  DiskType data_disk_type = 25 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The size of the data disk in GB attached to this instance, up to a maximum
  // of 64000 GB (64 TB). You can choose the size of the data disk based on how
  // big your notebooks and data are. If not specified, this defaults to 100.
  int64 data_disk_size_gb = 26 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. If true, the data disk will not be auto deleted when deleting the instance.
  bool no_remove_data_disk = 27 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. Disk encryption method used on the boot and data disks, defaults to GMEK.
  DiskEncryption disk_encryption = 15 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The KMS key used to encrypt the disks, only applicable if disk_encryption
  // is CMEK.
  // Format:
  // `projects/{project_id}/locations/{location}/keyRings/{key_ring_id}/cryptoKeys/{key_id}`
  //
  // Learn more about [using your own encryption keys](/kms/docs/quickstart).
  string kms_key = 16 [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Attached disks to notebook instance.
  repeated Disk disks = 28 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Shielded VM configuration.
  // [Images using supported Shielded VM
  // features](https://cloud.google.com/compute/docs/instances/modifying-shielded-vm).
  ShieldedInstanceConfig shielded_instance_config = 30 [(google.api.field_behavior) = OPTIONAL];

  // If true, no public IP will be assigned to this instance.
  bool no_public_ip = 17;

  // If true, the notebook instance will not register with the proxy.
  bool no_proxy_access = 18;

  // The name of the VPC that this instance is in.
  // Format:
  // `projects/{project_id}/global/networks/{network_id}`
  string network = 19;

  // The name of the subnet that this instance is in.
  // Format:
  // `projects/{project_id}/regions/{region}/subnetworks/{subnetwork_id}`
  string subnet = 20;

  // Labels to apply to this instance.
  // These can be later modified by the setLabels method.
  map<string, string> labels = 21;

  // Custom metadata to apply to this instance.
  map<string, string> metadata = 22;

  // Optional. The Compute Engine tags to add to runtime (see [Tagging
  // instances](https://cloud.google.com/compute/docs/label-or-tag-resources#tags)).
  repeated string tags = 32 [(google.api.field_behavior) = OPTIONAL];

  // The upgrade history of this instance.
  repeated UpgradeHistoryEntry upgrade_history = 29;

  // Optional. The type of vNIC to be used on this interface. This may be gVNIC or
  // VirtioNet.
  NicType nic_type = 33 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The optional reservation affinity. Setting this field will apply
  // the specified [Zonal Compute
  // Reservation](https://cloud.google.com/compute/docs/instances/reserving-zonal-resources)
  // to this notebook instance.
  ReservationAffinity reservation_affinity = 34 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Email address of entity that sent original CreateInstance request.
  string creator = 36 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Flag to enable ip forwarding or not, default false/off.
  // https://cloud.google.com/vpc/docs/using-routes#canipforward
  bool can_ip_forward = 39 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Instance creation time.
  google.protobuf.Timestamp create_time = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Instance update time.
  google.protobuf.Timestamp update_time = 24 [(google.api.field_behavior) = OUTPUT_ONLY];
}
