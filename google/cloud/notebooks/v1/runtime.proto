// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/notebooks/v1/environment.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V1";
option go_package = "cloud.google.com/go/notebooks/apiv1/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "RuntimeProto";
option java_package = "com.google.cloud.notebooks.v1";
option php_namespace = "Google\\Cloud\\Notebooks\\V1";
option ruby_package = "Google::Cloud::Notebooks::V1";

// The definition of a Runtime for a managed notebook instance.
message Runtime {
  option (google.api.resource) = {
    type: "notebooks.googleapis.com/Runtime"
    pattern: "projects/{project}/locations/{location}/runtimes/{runtime}"
  };

  // The definition of the states of this runtime.
  enum State {
    // State is not specified.
    STATE_UNSPECIFIED = 0;

    // The compute layer is starting the runtime. It is not ready for use.
    STARTING = 1;

    // The compute layer is installing required frameworks and registering the
    // runtime with notebook proxy. It cannot be used.
    PROVISIONING = 2;

    // The runtime is currently running. It is ready for use.
    ACTIVE = 3;

    // The control logic is stopping the runtime. It cannot be used.
    STOPPING = 4;

    // The runtime is stopped. It cannot be used.
    STOPPED = 5;

    // The runtime is being deleted. It cannot be used.
    DELETING = 6;

    // The runtime is upgrading. It cannot be used.
    UPGRADING = 7;

    // The runtime is being created and set up. It is not ready for use.
    INITIALIZING = 8;
  }

  // The runtime substate.
  enum HealthState {
    // The runtime substate is unknown.
    HEALTH_STATE_UNSPECIFIED = 0;

    // The runtime is known to be in an healthy state
    // (for example, critical daemons are running)
    // Applies to ACTIVE state.
    HEALTHY = 1;

    // The runtime is known to be in an unhealthy state
    // (for example, critical daemons are not running)
    // Applies to ACTIVE state.
    UNHEALTHY = 2;

    // The runtime has not installed health monitoring agent.
    // Applies to ACTIVE state.
    AGENT_NOT_INSTALLED = 3;

    // The runtime health monitoring agent is not running.
    // Applies to ACTIVE state.
    AGENT_NOT_RUNNING = 4;
  }

  // Output only. The resource name of the runtime.
  // Format:
  // `projects/{project}/locations/{location}/runtimes/{runtimeId}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Type of the runtime; currently only supports Compute Engine VM.
  oneof runtime_type {
    // Use a Compute Engine VM image to start the managed notebook instance.
    VirtualMachine virtual_machine = 2;
  }

  // Output only. Runtime state.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Runtime health_state.
  HealthState health_state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The config settings for accessing runtime.
  RuntimeAccessConfig access_config = 5;

  // The config settings for software inside the runtime.
  RuntimeSoftwareConfig software_config = 6;

  // Output only. Contains Runtime daemon metrics such as Service status and JupyterLab
  // stats.
  RuntimeMetrics metrics = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Runtime creation time.
  google.protobuf.Timestamp create_time = 20 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Runtime update time.
  google.protobuf.Timestamp update_time = 21 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Definition of the types of hardware accelerators that can be used.
// Definition of the types of hardware accelerators that can be used.
// See [Compute Engine
// AcceleratorTypes](https://cloud.google.com/compute/docs/reference/beta/acceleratorTypes).
// Examples:
//
// * `nvidia-tesla-k80`
// * `nvidia-tesla-p100`
// * `nvidia-tesla-v100`
// * `nvidia-tesla-p4`
// * `nvidia-tesla-t4`
// * `nvidia-tesla-a100`
message RuntimeAcceleratorConfig {
  // Type of this accelerator.
  enum AcceleratorType {
    // Accelerator type is not specified.
    ACCELERATOR_TYPE_UNSPECIFIED = 0;

    // Accelerator type is Nvidia Tesla K80.
    NVIDIA_TESLA_K80 = 1 [deprecated = true];

    // Accelerator type is Nvidia Tesla P100.
    NVIDIA_TESLA_P100 = 2;

    // Accelerator type is Nvidia Tesla V100.
    NVIDIA_TESLA_V100 = 3;

    // Accelerator type is Nvidia Tesla P4.
    NVIDIA_TESLA_P4 = 4;

    // Accelerator type is Nvidia Tesla T4.
    NVIDIA_TESLA_T4 = 5;

    // Accelerator type is Nvidia Tesla A100.
    NVIDIA_TESLA_A100 = 6;

    // (Coming soon) Accelerator type is TPU V2.
    TPU_V2 = 7;

    // (Coming soon) Accelerator type is TPU V3.
    TPU_V3 = 8;

    // Accelerator type is NVIDIA Tesla T4 Virtual Workstations.
    NVIDIA_TESLA_T4_VWS = 9;

    // Accelerator type is NVIDIA Tesla P100 Virtual Workstations.
    NVIDIA_TESLA_P100_VWS = 10;

    // Accelerator type is NVIDIA Tesla P4 Virtual Workstations.
    NVIDIA_TESLA_P4_VWS = 11;
  }

  // Accelerator model.
  AcceleratorType type = 1;

  // Count of cores of this accelerator.
  int64 core_count = 2;
}

// Represents a custom encryption key configuration that can be applied to
// a resource. This will encrypt all disks in Virtual Machine.
message EncryptionConfig {
  // The Cloud KMS resource identifier of the customer-managed encryption key
  // used to protect a resource, such as a disks. It has the following
  // format:
  // `projects/{PROJECT_ID}/locations/{REGION}/keyRings/{KEY_RING_NAME}/cryptoKeys/{KEY_NAME}`
  string kms_key = 1;
}

// A Local attached disk resource.
message LocalDisk {
  // Optional. A list of features to enable on the guest operating system.
  // Applicable only for bootable images.
  // Read [Enabling guest operating system
  // features](https://cloud.google.com/compute/docs/images/create-delete-deprecate-private-images#guest-os-features)
  // to see a list of available options.
  // Guest OS features for boot disk.
  message RuntimeGuestOsFeature {
    // The ID of a supported feature. Read [Enabling guest operating system
    // features](https://cloud.google.com/compute/docs/images/create-delete-deprecate-private-images#guest-os-features)
    // to see a list of available options.
    //
    // Valid values:
    //
    // * `FEATURE_TYPE_UNSPECIFIED`
    // * `MULTI_IP_SUBNET`
    // * `SECURE_BOOT`
    // * `UEFI_COMPATIBLE`
    // * `VIRTIO_SCSI_MULTIQUEUE`
    // * `WINDOWS`
    string type = 1;
  }

  // Optional. Output only. Specifies whether the disk will be auto-deleted when the
  // instance is deleted (but not when the disk is detached from the instance).
  bool auto_delete = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Output only. Indicates that this is a boot disk. The virtual machine
  // will use the first partition of the disk for its root filesystem.
  bool boot = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Output only. Specifies a unique device name
  // of your choice that is reflected into the
  // `/dev/disk/by-id/google-*` tree of a Linux operating system running within
  // the instance. This name can be used to reference the device for mounting,
  // resizing, and so on, from within the instance.
  //
  // If not specified, the server chooses a default device name to apply to this
  // disk, in the form persistent-disk-x, where x is a number assigned by Google
  // Compute Engine. This field is only applicable for persistent disks.
  string device_name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates a list of features to enable on the guest operating system.
  // Applicable only for bootable images. Read  Enabling guest operating
  // system features to see a list of available options.
  repeated RuntimeGuestOsFeature guest_os_features = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A zero-based index to this disk, where 0 is reserved for the
  // boot disk. If you have many disks attached to an instance, each disk would
  // have a unique index number.
  int32 index = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. Specifies the parameters for a new disk that will be created
  // alongside the new instance. Use initialization parameters to create boot
  // disks or local SSDs attached to the new instance.
  //
  // This property is mutually exclusive with the source property; you can only
  // define one or the other, but not both.
  LocalDiskInitializeParams initialize_params = 6 [(google.api.field_behavior) = INPUT_ONLY];

  // Specifies the disk interface to use for attaching this disk, which is
  // either SCSI or NVME. The default is SCSI. Persistent disks must always use
  // SCSI and the request will fail if you attempt to attach a persistent disk
  // in any other format than SCSI. Local SSDs can use either NVME or SCSI. For
  // performance characteristics of SCSI over NVMe, see Local SSD performance.
  // Valid values:
  //
  // * `NVME`
  // * `SCSI`
  string interface = 7;

  // Output only. Type of the resource. Always compute#attachedDisk for attached disks.
  string kind = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Any valid publicly visible licenses.
  repeated string licenses = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The mode in which to attach this disk, either `READ_WRITE` or `READ_ONLY`.
  // If not specified, the default is to attach the disk in `READ_WRITE` mode.
  // Valid values:
  //
  // * `READ_ONLY`
  // * `READ_WRITE`
  string mode = 10;

  // Specifies a valid partial or full URL to an existing Persistent Disk
  // resource.
  string source = 11;

  // Specifies the type of the disk, either `SCRATCH` or `PERSISTENT`. If not
  // specified, the default is `PERSISTENT`.
  // Valid values:
  //
  // * `PERSISTENT`
  // * `SCRATCH`
  string type = 12;
}

// Input only. Specifies the parameters for a new disk that will be created
// alongside the new instance. Use initialization parameters to create boot
// disks or local SSDs attached to the new runtime.
// This property is mutually exclusive with the source property; you can only
// define one or the other, but not both.
message LocalDiskInitializeParams {
  // Possible disk types.
  enum DiskType {
    // Disk type not set.
    DISK_TYPE_UNSPECIFIED = 0;

    // Standard persistent disk type.
    PD_STANDARD = 1;

    // SSD persistent disk type.
    PD_SSD = 2;

    // Balanced persistent disk type.
    PD_BALANCED = 3;

    // Extreme persistent disk type.
    PD_EXTREME = 4;
  }

  // Optional. Provide this property when creating the disk.
  string description = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the disk name. If not specified, the default is to use the name
  // of the instance. If the disk with the instance name exists already in the
  // given zone/region, a new name will be automatically generated.
  string disk_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the size of the disk in base-2 GB. If not specified, the disk
  // will be the same size as the image (usually 10GB). If specified, the size
  // must be equal to or larger than 10GB. Default 100 GB.
  int64 disk_size_gb = 3 [(google.api.field_behavior) = OPTIONAL];

  // Input only. The type of the boot disk attached to this instance, defaults to
  // standard persistent disk (`PD_STANDARD`).
  DiskType disk_type = 4 [(google.api.field_behavior) = INPUT_ONLY];

  // Optional. Labels to apply to this disk. These can be later modified by the
  // disks.setLabels method. This field is only applicable for persistent disks.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Specifies the login configuration for Runtime
message RuntimeAccessConfig {
  // Possible ways to access runtime. Authentication mode.
  // Currently supports: Single User only.
  enum RuntimeAccessType {
    // Unspecified access.
    RUNTIME_ACCESS_TYPE_UNSPECIFIED = 0;

    // Single user login.
    SINGLE_USER = 1;

    // Service Account mode.
    // In Service Account mode, Runtime creator will specify a SA that exists
    // in the consumer project. Using Runtime Service Account field.
    // Users accessing the Runtime need ActAs (Service Account User) permission.
    SERVICE_ACCOUNT = 2;
  }

  // The type of access mode this instance.
  RuntimeAccessType access_type = 1;

  // The owner of this runtime after creation. Format: `<EMAIL>`
  // Currently supports one owner only.
  string runtime_owner = 2;

  // Output only. The proxy endpoint that is used to access the runtime.
  string proxy_uri = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Specifies the selection and configuration of software inside the runtime.
// The properties to set on runtime.
// Properties keys are specified in `key:value` format, for example:
//
// * `idle_shutdown: true`
// * `idle_shutdown_timeout: 180`
// * `enable_health_monitoring: true`
message RuntimeSoftwareConfig {
  // Behavior for the post startup script.
  enum PostStartupScriptBehavior {
    // Unspecified post startup script behavior. Will run only once at creation.
    POST_STARTUP_SCRIPT_BEHAVIOR_UNSPECIFIED = 0;

    // Runs the post startup script provided during creation at every start.
    RUN_EVERY_START = 1;

    // Downloads and runs the provided post startup script at every start.
    DOWNLOAD_AND_RUN_EVERY_START = 2;
  }

  // Cron expression in UTC timezone, used to schedule instance auto upgrade.
  // Please follow the [cron format](https://en.wikipedia.org/wiki/Cron).
  string notebook_upgrade_schedule = 1;

  // Verifies core internal services are running.
  // Default: True
  optional bool enable_health_monitoring = 2;

  // Runtime will automatically shutdown after idle_shutdown_time.
  // Default: True
  optional bool idle_shutdown = 3;

  // Time in minutes to wait before shutting down runtime. Default: 180 minutes
  int32 idle_shutdown_timeout = 4;

  // Install Nvidia Driver automatically.
  // Default: True
  bool install_gpu_driver = 5;

  // Specify a custom Cloud Storage path where the GPU driver is stored.
  // If not specified, we'll automatically choose from official GPU drivers.
  string custom_gpu_driver_path = 6;

  // Path to a Bash script that automatically runs after a notebook instance
  // fully boots up. The path must be a URL or
  // Cloud Storage path (`gs://path-to-file/file-name`).
  string post_startup_script = 7;

  // Optional. Use a list of container images to use as Kernels in the notebook instance.
  repeated ContainerImage kernels = 8 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Bool indicating whether an newer image is available in an image family.
  optional bool upgradeable = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Behavior for the post startup script.
  PostStartupScriptBehavior post_startup_script_behavior = 10;

  // Bool indicating whether JupyterLab terminal will be available or not.
  // Default: False
  optional bool disable_terminal = 11;

  // Output only. version of boot image such as M100, from release label of the image.
  optional string version = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Contains runtime daemon metrics, such as OS and kernels and sessions stats.
message RuntimeMetrics {
  // Output only. The system metrics.
  map<string, string> system_metrics = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A set of Shielded Instance options.
// Check [Images using supported Shielded VM
// features](https://cloud.google.com/compute/docs/instances/modifying-shielded-vm).
// Not all combinations are valid.
message RuntimeShieldedInstanceConfig {
  // Defines whether the instance has Secure Boot enabled.
  //
  // Secure Boot helps ensure that the system only runs authentic software by
  // verifying the digital signature of all boot components, and halting the
  // boot process if signature verification fails. Disabled by default.
  bool enable_secure_boot = 1;

  // Defines whether the instance has the vTPM enabled. Enabled by default.
  bool enable_vtpm = 2;

  // Defines whether the instance has integrity monitoring enabled.
  //
  // Enables monitoring and attestation of the boot integrity of the instance.
  // The attestation is performed against the integrity policy baseline. This
  // baseline is initially derived from the implicitly trusted boot image when
  // the instance is created. Enabled by default.
  bool enable_integrity_monitoring = 3;
}

// Runtime using Virtual Machine for computing.
message VirtualMachine {
  // Output only. The user-friendly name of the Managed Compute Engine instance.
  string instance_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The unique identifier of the Managed Compute Engine instance.
  string instance_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Virtual Machine configuration settings.
  VirtualMachineConfig virtual_machine_config = 3;
}

// The config settings for virtual machine.
message VirtualMachineConfig {
  // The type of vNIC driver.
  // Default should be UNSPECIFIED_NIC_TYPE.
  enum NicType {
    // No type specified.
    UNSPECIFIED_NIC_TYPE = 0;

    // VIRTIO
    VIRTIO_NET = 1;

    // GVNIC
    GVNIC = 2;
  }

  // Definition of the boot image used by the Runtime.
  // Used to facilitate runtime upgradeability.
  message BootImage {

  }

  // Output only. The zone where the virtual machine is located.
  // If using regional request, the notebooks service will pick a location
  // in the corresponding runtime region.
  // On a get request, zone will always be present. Example:
  // * `us-central1-b`
  string zone = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The Compute Engine machine type used for runtimes.
  // Short name is valid. Examples:
  // * `n1-standard-2`
  // * `e2-standard-8`
  string machine_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Use a list of container images to use as Kernels in the notebook instance.
  repeated ContainerImage container_images = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. Data disk option configuration settings.
  LocalDisk data_disk = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. Encryption settings for virtual machine data disk.
  EncryptionConfig encryption_config = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Shielded VM Instance configuration settings.
  RuntimeShieldedInstanceConfig shielded_instance_config = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Compute Engine accelerator configuration for this runtime.
  RuntimeAcceleratorConfig accelerator_config = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Compute Engine network to be used for machine
  // communications. Cannot be specified with subnetwork. If neither
  // `network` nor `subnet` is specified, the "default" network of
  // the project is used, if it exists.
  //
  // A full URL or partial URI. Examples:
  //
  // * `https://www.googleapis.com/compute/v1/projects/[project_id]/global/networks/default`
  // * `projects/[project_id]/global/networks/default`
  //
  // Runtimes are managed resources inside Google Infrastructure.
  // Runtimes support the following network configurations:
  //
  // * Google Managed Network (Network & subnet are empty)
  // * Consumer Project VPC (network & subnet are required). Requires
  // configuring Private Service Access.
  // * Shared VPC (network & subnet are required). Requires configuring Private
  // Service Access.
  string network = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Compute Engine subnetwork to be used for machine
  // communications. Cannot be specified with network.
  //
  // A full URL or partial URI are valid. Examples:
  //
  // * `https://www.googleapis.com/compute/v1/projects/[project_id]/regions/us-east1/subnetworks/sub0`
  // * `projects/[project_id]/regions/us-east1/subnetworks/sub0`
  string subnet = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If true, runtime will only have internal IP
  // addresses. By default, runtimes are not restricted to internal IP
  // addresses, and will have ephemeral external IP addresses assigned to each
  // vm. This `internal_ip_only` restriction can only be enabled for
  // subnetwork enabled networks, and all dependencies must be
  // configured to be accessible without external IP addresses.
  bool internal_ip_only = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Compute Engine tags to add to runtime (see [Tagging
  // instances](https://cloud.google.com/compute/docs/label-or-tag-resources#tags)).
  repeated string tags = 13 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The Compute Engine guest attributes. (see
  // [Project and instance
  // guest
  // attributes](https://cloud.google.com/compute/docs/storing-retrieving-metadata#guest_attributes)).
  map<string, string> guest_attributes = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The Compute Engine metadata entries to add to virtual machine. (see
  // [Project and instance
  // metadata](https://cloud.google.com/compute/docs/storing-retrieving-metadata#project_and_instance_metadata)).
  map<string, string> metadata = 15 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The labels to associate with this runtime.
  // Label **keys** must contain 1 to 63 characters, and must conform to
  // [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt).
  // Label **values** may be empty, but, if present, must contain 1 to 63
  // characters, and must conform to [RFC
  // 1035](https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be
  // associated with a cluster.
  map<string, string> labels = 16 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The type of vNIC to be used on this interface. This may be gVNIC or
  // VirtioNet.
  NicType nic_type = 17 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Reserved IP Range name is used for VPC Peering.
  // The subnetwork allocation will use the range *name* if it's assigned.
  //
  // Example: managed-notebooks-range-c
  //
  //     PEERING_RANGE_NAME_3=managed-notebooks-range-c
  //     gcloud compute addresses create $PEERING_RANGE_NAME_3 \
  //       --global \
  //       --prefix-length=24 \
  //       --description="Google Cloud Managed Notebooks Range 24 c" \
  //       --network=$NETWORK \
  //       --addresses=*********** \
  //       --purpose=VPC_PEERING
  //
  // Field value will be: `managed-notebooks-range-c`
  string reserved_ip_range = 18 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Boot image metadata used for runtime upgradeability.
  BootImage boot_image = 19 [(google.api.field_behavior) = OPTIONAL];
}
