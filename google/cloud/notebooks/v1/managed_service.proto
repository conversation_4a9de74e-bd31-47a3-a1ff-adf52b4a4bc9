// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/notebooks/v1/diagnostic_config.proto";
import "google/cloud/notebooks/v1/event.proto";
import "google/cloud/notebooks/v1/runtime.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V1";
option go_package = "cloud.google.com/go/notebooks/apiv1/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "ManagedNotebooksProto";
option java_package = "com.google.cloud.notebooks.v1";
option php_namespace = "Google\\Cloud\\Notebooks\\V1";
option ruby_package = "Google::Cloud::Notebooks::V1";

// API v1 service for Managed Notebooks.
service ManagedNotebookService {
  option (google.api.default_host) = "notebooks.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists Runtimes in a given project and location.
  rpc ListRuntimes(ListRuntimesRequest) returns (ListRuntimesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/runtimes"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Runtime. The location must be a regional endpoint
  // rather than zonal.
  rpc GetRuntime(GetRuntimeRequest) returns (Runtime) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/runtimes/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Runtime in a given project and location.
  rpc CreateRuntime(CreateRuntimeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/runtimes"
      body: "runtime"
    };
    option (google.api.method_signature) = "parent,runtime_id,runtime";
    option (google.longrunning.operation_info) = {
      response_type: "Runtime"
      metadata_type: "OperationMetadata"
    };
  }

  // Update Notebook Runtime configuration.
  rpc UpdateRuntime(UpdateRuntimeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{runtime.name=projects/*/locations/*/runtimes/*}"
      body: "runtime"
    };
    option (google.api.method_signature) = "runtime,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Runtime"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Runtime.
  rpc DeleteRuntime(DeleteRuntimeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/runtimes/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Starts a Managed Notebook Runtime.
  // Perform "Start" on GPU instances; "Resume" on CPU instances
  // See:
  // https://cloud.google.com/compute/docs/instances/stop-start-instance
  // https://cloud.google.com/compute/docs/instances/suspend-resume-instance
  rpc StartRuntime(StartRuntimeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/runtimes/*}:start"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Runtime"
      metadata_type: "OperationMetadata"
    };
  }

  // Stops a Managed Notebook Runtime.
  // Perform "Stop" on GPU instances; "Suspend" on CPU instances
  // See:
  // https://cloud.google.com/compute/docs/instances/stop-start-instance
  // https://cloud.google.com/compute/docs/instances/suspend-resume-instance
  rpc StopRuntime(StopRuntimeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/runtimes/*}:stop"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Runtime"
      metadata_type: "OperationMetadata"
    };
  }

  // Switch a Managed Notebook Runtime.
  rpc SwitchRuntime(SwitchRuntimeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/runtimes/*}:switch"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Runtime"
      metadata_type: "OperationMetadata"
    };
  }

  // Resets a Managed Notebook Runtime.
  rpc ResetRuntime(ResetRuntimeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/runtimes/*}:reset"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Runtime"
      metadata_type: "OperationMetadata"
    };
  }

  // Upgrades a Managed Notebook Runtime to the latest version.
  rpc UpgradeRuntime(UpgradeRuntimeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/runtimes/*}:upgrade"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Runtime"
      metadata_type: "OperationMetadata"
    };
  }

  // Report and process a runtime event.
  rpc ReportRuntimeEvent(ReportRuntimeEventRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/runtimes/*}:reportEvent"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Runtime"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets an access token for the consumer service account that the customer
  // attached to the runtime. Only accessible from the tenant instance.
  rpc RefreshRuntimeTokenInternal(RefreshRuntimeTokenInternalRequest) returns (RefreshRuntimeTokenInternalResponse) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/runtimes/*}:refreshRuntimeTokenInternal"
      body: "*"
    };
    option (google.api.method_signature) = "name,vm_id";
  }

  // Creates a Diagnostic File and runs Diagnostic Tool given a Runtime.
  rpc DiagnoseRuntime(DiagnoseRuntimeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/runtimes/*}:diagnose"
      body: "*"
    };
    option (google.api.method_signature) = "name,diagnostic_config";
    option (google.longrunning.operation_info) = {
      response_type: "Runtime"
      metadata_type: "OperationMetadata"
    };
  }
}

// Request for listing Managed Notebook Runtimes.
message ListRuntimesRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "notebooks.googleapis.com/Runtime"
    }
  ];

  // Maximum return size of the list call.
  int32 page_size = 2;

  // A previous returned page token that can be used to continue listing
  // from the last result.
  string page_token = 3;
}

// Response for listing Managed Notebook Runtimes.
message ListRuntimesResponse {
  // A list of returned Runtimes.
  repeated Runtime runtimes = 1;

  // Page token that can be used to continue listing from the last result in the
  // next list call.
  string next_page_token = 2;

  // Locations that could not be reached. For example,
  // `['us-west1', 'us-central1']`.
  // A ListRuntimesResponse will only contain either runtimes or unreachables,
  repeated string unreachable = 3;
}

// Request for getting a Managed Notebook Runtime.
message GetRuntimeRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Runtime"
    }
  ];
}

// Request for creating a Managed Notebook Runtime.
message CreateRuntimeRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Runtime"
    }
  ];

  // Required. User-defined unique ID of this Runtime.
  string runtime_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Runtime to be created.
  Runtime runtime = 3 [(google.api.field_behavior) = REQUIRED];

  // Idempotent request UUID.
  string request_id = 4;
}

// Request for deleting a Managed Notebook Runtime.
message DeleteRuntimeRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Runtime"
    }
  ];

  // Idempotent request UUID.
  string request_id = 2;
}

// Request for starting a Managed Notebook Runtime.
message StartRuntimeRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Idempotent request UUID.
  string request_id = 2;
}

// Request for stopping a Managed Notebook Runtime.
message StopRuntimeRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Idempotent request UUID.
  string request_id = 2;
}

// Request for switching a Managed Notebook Runtime.
message SwitchRuntimeRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // machine type.
  string machine_type = 2;

  // accelerator config.
  RuntimeAcceleratorConfig accelerator_config = 3;

  // Idempotent request UUID.
  string request_id = 4;
}

// Request for resetting a Managed Notebook Runtime.
message ResetRuntimeRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Idempotent request UUID.
  string request_id = 2;
}

// Request for upgrading a Managed Notebook Runtime to the latest version.
// option (google.api.message_visibility).restriction =
//     "TRUSTED_TESTER,SPECIAL_TESTER";
message UpgradeRuntimeRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Idempotent request UUID.
  string request_id = 2;
}

// Request for reporting a Managed Notebook Event.
message ReportRuntimeEventRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Runtime"
    }
  ];

  // Required. The VM hardware token for authenticating the VM.
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  string vm_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Event to be reported.
  Event event = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for updating a Managed Notebook configuration.
message UpdateRuntimeRequest {
  // Required. The Runtime to be updated.
  Runtime runtime = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Specifies the path, relative to `Runtime`, of
  // the field to update. For example, to change the software configuration
  // kernels, the `update_mask` parameter would be
  // specified as `software_config.kernels`,
  // and the `PATCH` request body would specify the new value, as follows:
  //
  //     {
  //       "software_config":{
  //         "kernels": [{
  //            'repository':
  //            'gcr.io/deeplearning-platform-release/pytorch-gpu', 'tag':
  //            'latest' }],
  //         }
  //     }
  //
  //
  // Currently, only the following fields can be updated:
  // - `software_config.kernels`
  // - `software_config.post_startup_script`
  // - `software_config.custom_gpu_driver_path`
  // - `software_config.idle_shutdown`
  // - `software_config.idle_shutdown_timeout`
  // - `software_config.disable_terminal`
  google.protobuf.FieldMask update_mask = 2 [(google.api.field_behavior) = REQUIRED];

  // Idempotent request UUID.
  string request_id = 3;
}

// Request for getting a new access token.
message RefreshRuntimeTokenInternalRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Runtime"
    }
  ];

  // Required. The VM hardware token for authenticating the VM.
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  string vm_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response with a new access token.
message RefreshRuntimeTokenInternalResponse {
  // The OAuth 2.0 access token.
  string access_token = 1;

  // Output only. Token expiration time.
  google.protobuf.Timestamp expire_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request for creating a notebook instance diagnostic file.
message DiagnoseRuntimeRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/runtimes/{runtimes_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "notebooks.googleapis.com/Runtime"
    }
  ];

  // Required. Defines flags that are used to run the diagnostic tool
  DiagnosticConfig diagnostic_config = 2 [(google.api.field_behavior) = REQUIRED];
}
