// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V1";
option go_package = "cloud.google.com/go/notebooks/apiv1/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "EnvironmentProto";
option java_package = "com.google.cloud.notebooks.v1";
option php_namespace = "Google\\Cloud\\Notebooks\\V1";
option ruby_package = "Google::Cloud::Notebooks::V1";

// Definition of a software environment that is used to start a notebook
// instance.
message Environment {
  option (google.api.resource) = {
    type: "notebooks.googleapis.com/Environment"
    pattern: "projects/{project}/environments/{environment}"
  };

  // Output only. Name of this environment.
  // Format:
  // `projects/{project_id}/locations/{location}/environments/{environment_id}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Display name of this environment for the UI.
  string display_name = 2;

  // A brief description of this environment.
  string description = 3;

  // Type of the environment; can be one of VM image, or container image.
  oneof image_type {
    // Use a Compute Engine VM image to start the notebook instance.
    VmImage vm_image = 6;

    // Use a container image to start the notebook instance.
    ContainerImage container_image = 7;
  }

  // Path to a Bash script that automatically runs after a notebook instance
  // fully boots up. The path must be a URL or
  // Cloud Storage path. Example: `"gs://path-to-file/file-name"`
  string post_startup_script = 8;

  // Output only. The time at which this environment was created.
  google.protobuf.Timestamp create_time = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Definition of a custom Compute Engine virtual machine image for starting a
// notebook instance with the environment installed directly on the VM.
message VmImage {
  // Required. The name of the Google Cloud project that this VM image belongs to.
  // Format: `{project_id}`
  string project = 1 [(google.api.field_behavior) = REQUIRED];

  // The reference to an external Compute Engine VM image.
  oneof image {
    // Use VM image name to find the image.
    string image_name = 2;

    // Use this VM image family to find the image; the newest image in this
    // family will be used.
    string image_family = 3;
  }
}

// Definition of a container image for starting a notebook instance with the
// environment installed in a container.
message ContainerImage {
  // Required. The path to the container image repository. For example:
  // `gcr.io/{project_id}/{image_name}`
  string repository = 1 [(google.api.field_behavior) = REQUIRED];

  // The tag of the container image. If not specified, this defaults
  // to the latest tag.
  string tag = 2;
}
