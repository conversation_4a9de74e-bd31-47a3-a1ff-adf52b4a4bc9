{"methodConfig": [{"name": [{"service": "google.cloud.scheduler.v1.CloudScheduler", "method": "ListJobs"}, {"service": "google.cloud.scheduler.v1.CloudScheduler", "method": "Get<PERSON>ob"}, {"service": "google.cloud.scheduler.v1.CloudScheduler", "method": "DeleteJob"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.scheduler.v1.CloudScheduler", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.scheduler.v1.CloudScheduler", "method": "Update<PERSON><PERSON>"}, {"service": "google.cloud.scheduler.v1.CloudScheduler", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.scheduler.v1.CloudScheduler", "method": "ResumeJob"}, {"service": "google.cloud.scheduler.v1.CloudScheduler", "method": "<PERSON><PERSON><PERSON>"}], "timeout": "600s"}]}