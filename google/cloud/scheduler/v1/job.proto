// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.scheduler.v1;

import "google/api/resource.proto";
import "google/cloud/scheduler/v1/target.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/scheduler/apiv1/schedulerpb;schedulerpb";
option java_multiple_files = true;
option java_outer_classname = "JobProto";
option java_package = "com.google.cloud.scheduler.v1";

// Configuration for a job.
// The maximum allowed size for a job is 1MB.
message Job {
  option (google.api.resource) = {
    type: "cloudscheduler.googleapis.com/Job"
    pattern: "projects/{project}/locations/{location}/jobs/{job}"
  };

  // State of the job.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The job is executing normally.
    ENABLED = 1;

    // The job is paused by the user. It will not execute. A user can
    // intentionally pause the job using
    // [PauseJobRequest][google.cloud.scheduler.v1.PauseJobRequest].
    PAUSED = 2;

    // The job is disabled by the system due to error. The user
    // cannot directly set a job to be disabled.
    DISABLED = 3;

    // The job state resulting from a failed
    // [CloudScheduler.UpdateJob][google.cloud.scheduler.v1.CloudScheduler.UpdateJob]
    // operation. To recover a job from this state, retry
    // [CloudScheduler.UpdateJob][google.cloud.scheduler.v1.CloudScheduler.UpdateJob]
    // until a successful response is received.
    UPDATE_FAILED = 4;
  }

  // Optionally caller-specified in
  // [CreateJob][google.cloud.scheduler.v1.CloudScheduler.CreateJob], after
  // which it becomes output only.
  //
  // The job name. For example:
  // `projects/PROJECT_ID/locations/LOCATION_ID/jobs/JOB_ID`.
  //
  // * `PROJECT_ID` can contain letters ([A-Za-z]), numbers ([0-9]),
  //    hyphens (-), colons (:), or periods (.).
  //    For more information, see
  //    [Identifying
  //    projects](https://cloud.google.com/resource-manager/docs/creating-managing-projects#identifying_projects)
  // * `LOCATION_ID` is the canonical ID for the job's location.
  //    The list of available locations can be obtained by calling
  //    [ListLocations][google.cloud.location.Locations.ListLocations].
  //    For more information, see https://cloud.google.com/about/locations/.
  // * `JOB_ID` can contain only letters ([A-Za-z]), numbers ([0-9]),
  //    hyphens (-), or underscores (_). The maximum length is 500 characters.
  string name = 1;

  // Optionally caller-specified in
  // [CreateJob][google.cloud.scheduler.v1.CloudScheduler.CreateJob] or
  // [UpdateJob][google.cloud.scheduler.v1.CloudScheduler.UpdateJob].
  //
  // A human-readable description for the job. This string must not contain
  // more than 500 characters.
  string description = 2;

  // Required.
  //
  // Delivery settings containing destination and parameters.
  oneof target {
    // Pub/Sub target.
    PubsubTarget pubsub_target = 4;

    // App Engine HTTP target.
    AppEngineHttpTarget app_engine_http_target = 5;

    // HTTP target.
    HttpTarget http_target = 6;
  }

  // Required, except when used with
  // [UpdateJob][google.cloud.scheduler.v1.CloudScheduler.UpdateJob].
  //
  // Describes the schedule on which the job will be executed.
  //
  // The schedule can be either of the following types:
  //
  // * [Crontab](https://en.wikipedia.org/wiki/Cron#Overview)
  // * English-like
  // [schedule](https://cloud.google.com/scheduler/docs/configuring/cron-job-schedules)
  //
  // As a general rule, execution `n + 1` of a job will not begin
  // until execution `n` has finished. Cloud Scheduler will never
  // allow two simultaneously outstanding executions. For example,
  // this implies that if the `n+1`th execution is scheduled to run at
  // 16:00 but the `n`th execution takes until 16:15, the `n+1`th
  // execution will not start until `16:15`.
  // A scheduled start time will be delayed if the previous
  // execution has not ended when its scheduled time occurs.
  //
  // If [retry_count][google.cloud.scheduler.v1.RetryConfig.retry_count] > 0 and
  // a job attempt fails, the job will be tried a total of
  // [retry_count][google.cloud.scheduler.v1.RetryConfig.retry_count] times,
  // with exponential backoff, until the next scheduled start time.
  string schedule = 20;

  // Specifies the time zone to be used in interpreting
  // [schedule][google.cloud.scheduler.v1.Job.schedule]. The value of this field
  // must be a time zone name from the [tz
  // database](http://en.wikipedia.org/wiki/Tz_database).
  //
  // Note that some time zones include a provision for
  // daylight savings time. The rules for daylight saving time are
  // determined by the chosen tz. For UTC use the string "utc". If a
  // time zone is not specified, the default will be in UTC (also known
  // as GMT).
  string time_zone = 21;

  // Output only. The creation time of the job.
  google.protobuf.Timestamp user_update_time = 9;

  // Output only. State of the job.
  State state = 10;

  // Output only. The response from the target for the last attempted execution.
  google.rpc.Status status = 11;

  // Output only. The next time the job is scheduled. Note that this may be a
  // retry of a previously failed attempt or the next execution time
  // according to the schedule.
  google.protobuf.Timestamp schedule_time = 17;

  // Output only. The time the last job attempt started.
  google.protobuf.Timestamp last_attempt_time = 18;

  // Settings that determine the retry behavior.
  RetryConfig retry_config = 19;

  // The deadline for job attempts. If the request handler does not respond by
  // this deadline then the request is cancelled and the attempt is marked as a
  // `DEADLINE_EXCEEDED` failure. The failed attempt can be viewed in
  // execution logs. Cloud Scheduler will retry the job according
  // to the [RetryConfig][google.cloud.scheduler.v1.RetryConfig].
  //
  // The default and the allowed values depend on the type of target:
  //
  // * For [HTTP targets][google.cloud.scheduler.v1.Job.http_target], the
  // default is 3 minutes. The deadline must be in the interval [15 seconds, 30
  // minutes].
  //
  // * For [App Engine HTTP
  // targets][google.cloud.scheduler.v1.Job.app_engine_http_target], 0 indicates
  // that the request has the default deadline. The default deadline depends on
  // the scaling type of the service: 10 minutes for standard apps with
  // automatic scaling, 24 hours for standard apps with manual and basic
  // scaling, and 60 minutes for flex apps. If the request deadline is set, it
  // must be in the interval [15 seconds, 24 hours 15 seconds].
  //
  // * For [Pub/Sub targets][google.cloud.scheduler.v1.Job.pubsub_target], this
  // field is ignored.
  google.protobuf.Duration attempt_deadline = 22;
}

// Settings that determine the retry behavior.
//
// By default, if a job does not complete successfully (meaning that
// an acknowledgement is not received from the handler, then it will be retried
// with exponential backoff according to the settings in
// [RetryConfig][google.cloud.scheduler.v1.RetryConfig].
message RetryConfig {
  // The number of attempts that the system will make to run a job using the
  // exponential backoff procedure described by
  // [max_doublings][google.cloud.scheduler.v1.RetryConfig.max_doublings].
  //
  // The default value of retry_count is zero.
  //
  // If retry_count is zero, a job attempt will *not* be retried if
  // it fails. Instead the Cloud Scheduler system will wait for the
  // next scheduled execution time.
  //
  // If retry_count is set to a non-zero number then Cloud Scheduler
  // will retry failed attempts, using exponential backoff,
  // retry_count times, or until the next scheduled execution time,
  // whichever comes first.
  //
  // Values greater than 5 and negative values are not allowed.
  int32 retry_count = 1;

  // The time limit for retrying a failed job, measured from time when an
  // execution was first attempted. If specified with
  // [retry_count][google.cloud.scheduler.v1.RetryConfig.retry_count], the job
  // will be retried until both limits are reached.
  //
  // The default value for max_retry_duration is zero, which means retry
  // duration is unlimited.
  google.protobuf.Duration max_retry_duration = 2;

  // The minimum amount of time to wait before retrying a job after
  // it fails.
  //
  // The default value of this field is 5 seconds.
  google.protobuf.Duration min_backoff_duration = 3;

  // The maximum amount of time to wait before retrying a job after
  // it fails.
  //
  // The default value of this field is 1 hour.
  google.protobuf.Duration max_backoff_duration = 4;

  // The time between retries will double `max_doublings` times.
  //
  // A job's retry interval starts at
  // [min_backoff_duration][google.cloud.scheduler.v1.RetryConfig.min_backoff_duration],
  // then doubles `max_doublings` times, then increases linearly, and finally
  // retries at intervals of
  // [max_backoff_duration][google.cloud.scheduler.v1.RetryConfig.max_backoff_duration]
  // up to [retry_count][google.cloud.scheduler.v1.RetryConfig.retry_count]
  // times.
  //
  // For example, if
  // [min_backoff_duration][google.cloud.scheduler.v1.RetryConfig.min_backoff_duration]
  // is 10s,
  // [max_backoff_duration][google.cloud.scheduler.v1.RetryConfig.max_backoff_duration]
  // is 300s, and `max_doublings` is 3, then the a job will first be retried in
  // 10s. The retry interval will double three times, and then increase linearly
  // by 2^3 * 10s.  Finally, the job will retry at intervals of
  // [max_backoff_duration][google.cloud.scheduler.v1.RetryConfig.max_backoff_duration]
  // until the job has been attempted
  // [retry_count][google.cloud.scheduler.v1.RetryConfig.retry_count] times.
  // Thus, the requests will retry at 10s, 20s, 40s, 80s, 160s, 240s, 300s,
  // 300s, ....
  //
  // The default value of this field is 5.
  int32 max_doublings = 5;
}
