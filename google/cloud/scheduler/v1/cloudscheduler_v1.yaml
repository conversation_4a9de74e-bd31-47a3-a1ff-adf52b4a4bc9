type: google.api.Service
config_version: 3
name: cloudscheduler.googleapis.com
title: Cloud Scheduler API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.scheduler.v1.CloudScheduler

documentation:
  summary: Creates and manages jobs run on a regular recurring schedule.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 30.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 30.0
  - selector: 'google.cloud.scheduler.v1.CloudScheduler.*'
    deadline: 30.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.scheduler.v1.CloudScheduler.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
