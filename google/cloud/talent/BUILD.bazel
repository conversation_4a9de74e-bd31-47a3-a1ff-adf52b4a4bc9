# This build file includes a target for the Ruby wrapper library for
# google-cloud-talent.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for jobs.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v4 in this case.
ruby_cloud_gapic_library(
    name = "jobs_ruby_wrapper",
    srcs = ["//google/cloud/talent/v4:talent_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-talent",
        "ruby-cloud-env-prefix=TALENT",
        "ruby-cloud-wrapper-of=v4:0.12;v4beta1:0.10",
        "ruby-cloud-product-url=https://cloud.google.com/solutions/talent-solution",
        "ruby-cloud-api-id=jobs.googleapis.com",
        "ruby-cloud-api-shortname=jobs",
        "ruby-cloud-migration-version=0.20",
    ],
    ruby_cloud_description = "Transform your job search and candidate matching capabilities with Cloud Talent Solution, designed to support enterprise talent acquisition technology and evolve with your growing needs. This AI solution includes features such as Job Search and Profile Search (Beta) to provide candidates and employers with an enhanced talent acquisition experience.",
    ruby_cloud_title = "Cloud Talent Solution",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-talent-ruby",
    deps = [
        ":jobs_ruby_wrapper",
    ],
)
