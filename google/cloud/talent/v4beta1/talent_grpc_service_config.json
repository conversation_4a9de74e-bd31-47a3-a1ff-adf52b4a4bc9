{"methodConfig": [{"name": [{"service": "google.cloud.talent.v4beta1.TenantService", "method": "Get<PERSON>enant"}, {"service": "google.cloud.talent.v4beta1.TenantService", "method": "DeleteTenant"}, {"service": "google.cloud.talent.v4beta1.TenantService", "method": "ListTenants"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.talent.v4beta1.ApplicationService", "method": "GetApplication"}, {"service": "google.cloud.talent.v4beta1.ApplicationService", "method": "DeleteApplication"}, {"service": "google.cloud.talent.v4beta1.ApplicationService", "method": "ListApplications"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.talent.v4beta1.EventService", "method": "CreateClientEvent"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.talent.v4beta1.JobService", "method": "Get<PERSON>ob"}, {"service": "google.cloud.talent.v4beta1.JobService", "method": "DeleteJob"}, {"service": "google.cloud.talent.v4beta1.JobService", "method": "ListJobs"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.talent.v4beta1.ProfileService", "method": "CreateProfile"}, {"service": "google.cloud.talent.v4beta1.ProfileService", "method": "UpdateProfile"}, {"service": "google.cloud.talent.v4beta1.ProfileService", "method": "SearchProfiles"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.talent.v4beta1.JobService", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.talent.v4beta1.JobService", "method": "Update<PERSON><PERSON>"}, {"service": "google.cloud.talent.v4beta1.JobService", "method": "BatchDeleteJobs"}, {"service": "google.cloud.talent.v4beta1.JobService", "method": "SearchJobs"}, {"service": "google.cloud.talent.v4beta1.JobService", "method": "SearchJobsForAlert"}, {"service": "google.cloud.talent.v4beta1.JobService", "method": "BatchCreateJobs"}, {"service": "google.cloud.talent.v4beta1.JobService", "method": "BatchUpdateJobs"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.talent.v4beta1.ProfileService", "method": "ListProfiles"}, {"service": "google.cloud.talent.v4beta1.ProfileService", "method": "GetProfile"}, {"service": "google.cloud.talent.v4beta1.ProfileService", "method": "DeleteProfile"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.talent.v4beta1.TenantService", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.talent.v4beta1.TenantService", "method": "UpdateTenant"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.talent.v4beta1.ApplicationService", "method": "CreateApplication"}, {"service": "google.cloud.talent.v4beta1.ApplicationService", "method": "UpdateApplication"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.talent.v4beta1.CompanyService", "method": "CreateCompany"}, {"service": "google.cloud.talent.v4beta1.CompanyService", "method": "UpdateCompany"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.talent.v4beta1.CompanyService", "method": "GetCompany"}, {"service": "google.cloud.talent.v4beta1.CompanyService", "method": "DeleteCompany"}, {"service": "google.cloud.talent.v4beta1.CompanyService", "method": "ListCompanies"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.talent.v4beta1.Completion", "method": "CompleteQuery"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}