type: google.api.Service
config_version: 3
name: jobs.googleapis.com
title: Cloud Talent Solution API

apis:
- name: google.cloud.talent.v4beta1.CompanyService
- name: google.cloud.talent.v4beta1.Completion
- name: google.cloud.talent.v4beta1.EventService
- name: google.cloud.talent.v4beta1.JobService
- name: google.cloud.talent.v4beta1.TenantService
- name: google.longrunning.Operations

types:
- name: google.cloud.talent.v4beta1.BatchOperationMetadata
- name: google.cloud.talent.v4beta1.JobOperationResult

documentation:
  summary: |-
    Cloud Talent Solution provides the capability to create, read, update, and
    delete job postings, as well as search jobs based on keywords and filters.
  overview: |-
    # Introduction

    Cloud Talent Solution provides hiring companies and job distributors with
    the capability to create and manage job postings and organizational
    information, and perform job search using field based queries. This site
    contains all of the information you need to integrate Cloud Talent
    Solution, create company entities that represent employers with intended
    access control, post and manage jobs on Garage Jobs on behalf of
    employers, and search jobs where access control permits. For a full list
    of supported API methods, refer to the [Reference](rest/) section.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v4beta1/{name=projects/*/operations/*}'

authentication:
  rules:
  - selector: 'google.cloud.talent.v4beta1.CompanyService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/jobs
  - selector: google.cloud.talent.v4beta1.Completion.CompleteQuery
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/jobs
  - selector: google.cloud.talent.v4beta1.EventService.CreateClientEvent
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/jobs
  - selector: 'google.cloud.talent.v4beta1.JobService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/jobs
  - selector: 'google.cloud.talent.v4beta1.TenantService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/jobs
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/jobs
