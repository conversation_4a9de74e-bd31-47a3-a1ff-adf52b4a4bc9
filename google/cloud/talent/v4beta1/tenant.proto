// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.talent.v4beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/talent/apiv4beta1/talentpb;talentpb";
option java_multiple_files = true;
option java_outer_classname = "TenantResourceProto";
option java_package = "com.google.cloud.talent.v4beta1";
option objc_class_prefix = "CTS";

// A Tenant resource represents a tenant in the service. A tenant is a group or
// entity that shares common access with specific privileges for resources like
// profiles. Customer may create multiple tenants to provide data isolation for
// different groups.
message Tenant {
  option (google.api.resource) = {
    type: "jobs.googleapis.com/Tenant"
    pattern: "projects/{project}/tenants/{tenant}"
  };

  // Enum that represents how user data owned by the tenant is used.
  enum DataUsageType {
    // Default value.
    DATA_USAGE_TYPE_UNSPECIFIED = 0;

    // Data owned by this tenant is used to improve search/recommendation
    // quality across tenants.
    AGGREGATED = 1;

    // Data owned by this tenant is used to improve search/recommendation
    // quality for this tenant only.
    ISOLATED = 2;
  }

  // Required during tenant update.
  //
  // The resource name for a tenant. This is generated by the service when a
  // tenant is created.
  //
  // The format is "projects/{project_id}/tenants/{tenant_id}", for example,
  // "projects/foo/tenants/bar".
  string name = 1;

  // Required. Client side tenant identifier, used to uniquely identify the
  // tenant.
  //
  // The maximum number of allowed characters is 255.
  string external_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Indicates whether data owned by this tenant may be used to provide product
  // improvements across other tenants.
  //
  // Defaults behavior is
  // [DataUsageType.ISOLATED][google.cloud.talent.v4beta1.Tenant.DataUsageType.ISOLATED]
  // if it's unset.
  DataUsageType usage_type = 3;

  // A list of keys of filterable
  // [Profile.custom_attributes][google.cloud.talent.v4beta1.Profile.custom_attributes],
  // whose corresponding `string_values` are used in keyword searches. Profiles
  // with `string_values` under these specified field keys are returned if any
  // of the values match the search keyword. Custom field values with
  // parenthesis, brackets and special symbols are not searchable as-is,
  // and must be surrounded by quotes.
  repeated string keyword_searchable_profile_custom_attributes = 4;
}
