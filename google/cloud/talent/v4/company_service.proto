// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.talent.v4;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/talent/v4/common.proto";
import "google/cloud/talent/v4/company.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/talent/apiv4/talentpb;talentpb";
option java_multiple_files = true;
option java_outer_classname = "CompanyServiceProto";
option java_package = "com.google.cloud.talent.v4";
option objc_class_prefix = "CTS";

// A service that handles company management, including CRUD and enumeration.
service CompanyService {
  option (google.api.default_host) = "jobs.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/jobs";

  // Creates a new company entity.
  rpc CreateCompany(CreateCompanyRequest) returns (Company) {
    option (google.api.http) = {
      post: "/v4/{parent=projects/*/tenants/*}/companies"
      body: "company"
    };
    option (google.api.method_signature) = "parent,company";
  }

  // Retrieves specified company.
  rpc GetCompany(GetCompanyRequest) returns (Company) {
    option (google.api.http) = {
      get: "/v4/{name=projects/*/tenants/*/companies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates specified company.
  rpc UpdateCompany(UpdateCompanyRequest) returns (Company) {
    option (google.api.http) = {
      patch: "/v4/{company.name=projects/*/tenants/*/companies/*}"
      body: "company"
    };
    option (google.api.method_signature) = "company,update_mask";
  }

  // Deletes specified company.
  // Prerequisite: The company has no jobs associated with it.
  rpc DeleteCompany(DeleteCompanyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v4/{name=projects/*/tenants/*/companies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all companies associated with the project.
  rpc ListCompanies(ListCompaniesRequest) returns (ListCompaniesResponse) {
    option (google.api.http) = {
      get: "/v4/{parent=projects/*/tenants/*}/companies"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The Request of the CreateCompany method.
message CreateCompanyRequest {
  // Required. Resource name of the tenant under which the company is created.
  //
  // The format is "projects/{project_id}/tenants/{tenant_id}", for example,
  // "projects/foo/tenants/bar".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "jobs.googleapis.com/Tenant" }
  ];

  // Required. The company to be created.
  Company company = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for getting a company by name.
message GetCompanyRequest {
  // Required. The resource name of the company to be retrieved.
  //
  // The format is
  // "projects/{project_id}/tenants/{tenant_id}/companies/{company_id}", for
  // example, "projects/api-test-project/tenants/foo/companies/bar".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "jobs.googleapis.com/Company" }
  ];
}

// Request for updating a specified company.
message UpdateCompanyRequest {
  // Required. The company resource to replace the current resource in the
  // system.
  Company company = 1 [(google.api.field_behavior) = REQUIRED];

  // Strongly recommended for the best service experience.
  //
  // If [update_mask][google.cloud.talent.v4.UpdateCompanyRequest.update_mask]
  // is provided, only the specified fields in
  // [company][google.cloud.talent.v4.UpdateCompanyRequest.company] are updated.
  // Otherwise all the fields are updated.
  //
  // A field mask to specify the company fields to be updated. Only
  // top level fields of [Company][google.cloud.talent.v4.Company] are
  // supported.
  google.protobuf.FieldMask update_mask = 2;
}

// Request to delete a company.
message DeleteCompanyRequest {
  // Required. The resource name of the company to be deleted.
  //
  // The format is
  // "projects/{project_id}/tenants/{tenant_id}/companies/{company_id}", for
  // example, "projects/foo/tenants/bar/companies/baz".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "jobs.googleapis.com/Company" }
  ];
}

// List companies for which the client has ACL visibility.
message ListCompaniesRequest {
  // Required. Resource name of the tenant under which the company is created.
  //
  // The format is "projects/{project_id}/tenants/{tenant_id}", for example,
  // "projects/foo/tenants/bar".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "jobs.googleapis.com/Tenant" }
  ];

  // The starting indicator from which to return results.
  string page_token = 2;

  // The maximum number of companies to be returned, at most 100.
  // Default is 100 if a non-positive number is provided.
  int32 page_size = 3;

  // Set to true if the companies requested must have open jobs.
  //
  // Defaults to false.
  //
  // If true, at most
  // [page_size][google.cloud.talent.v4.ListCompaniesRequest.page_size] of
  // companies are fetched, among which only those with open jobs are returned.
  bool require_open_jobs = 4;
}

// The List companies response object.
message ListCompaniesResponse {
  // Companies for the current client.
  repeated Company companies = 1;

  // A token to retrieve the next page of results.
  string next_page_token = 2;

  // Additional information for the API invocation, such as the request
  // tracking id.
  ResponseMetadata metadata = 3;
}
