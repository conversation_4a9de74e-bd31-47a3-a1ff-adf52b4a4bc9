// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.talent.v4;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/talent/v4/common.proto";

option go_package = "cloud.google.com/go/talent/apiv4/talentpb;talentpb";
option java_multiple_files = true;
option java_outer_classname = "CompanyProto";
option java_package = "com.google.cloud.talent.v4";
option objc_class_prefix = "CTS";

// A Company resource represents a company in the service. A company is the
// entity that owns job postings, that is, the hiring entity responsible for
// employing applicants for the job position.
message Company {
  option (google.api.resource) = {
    type: "jobs.googleapis.com/Company"
    pattern: "projects/{project}/tenants/{tenant}/companies/{company}"
  };

  // Derived details about the company.
  message DerivedInfo {
    // A structured headquarters location of the company, resolved from
    // [Company.headquarters_address][google.cloud.talent.v4.Company.headquarters_address]
    // if provided.
    Location headquarters_location = 1;
  }

  // Required during company update.
  //
  // The resource name for a company. This is generated by the service when a
  // company is created.
  //
  // The format is
  // "projects/{project_id}/tenants/{tenant_id}/companies/{company_id}", for
  // example, "projects/foo/tenants/bar/companies/baz".
  string name = 1;

  // Required. The display name of the company, for example, "Google LLC".
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Client side company identifier, used to uniquely identify the
  // company.
  //
  // The maximum number of allowed characters is 255.
  string external_id = 3 [(google.api.field_behavior) = REQUIRED];

  // The employer's company size.
  CompanySize size = 4;

  // The street address of the company's main headquarters, which may be
  // different from the job location. The service attempts
  // to geolocate the provided address, and populates a more specific
  // location wherever possible in
  // [DerivedInfo.headquarters_location][google.cloud.talent.v4.Company.DerivedInfo.headquarters_location].
  string headquarters_address = 5;

  // Set to true if it is the hiring agency that post jobs for other
  // employers.
  //
  // Defaults to false if not provided.
  bool hiring_agency = 6;

  // Equal Employment Opportunity legal disclaimer text to be
  // associated with all jobs, and typically to be displayed in all
  // roles.
  //
  // The maximum number of allowed characters is 500.
  string eeo_text = 7;

  // The URI representing the company's primary web site or home page,
  // for example, "https://www.google.com".
  //
  // The maximum number of allowed characters is 255.
  string website_uri = 8;

  // The URI to employer's career site or careers page on the employer's web
  // site, for example, "https://careers.google.com".
  string career_site_uri = 9;

  // A URI that hosts the employer's company logo.
  string image_uri = 10;

  // This field is deprecated. Please set the searchability of the custom
  // attribute in the
  // [Job.custom_attributes][google.cloud.talent.v4.Job.custom_attributes] going
  // forward.
  //
  // A list of keys of filterable
  // [Job.custom_attributes][google.cloud.talent.v4.Job.custom_attributes],
  // whose corresponding `string_values` are used in keyword searches. Jobs with
  // `string_values` under these specified field keys are returned if any
  // of the values match the search keyword. Custom field values with
  // parenthesis, brackets and special symbols are not searchable as-is,
  // and those keyword queries must be surrounded by quotes.
  repeated string keyword_searchable_job_custom_attributes = 11
      [deprecated = true];

  // Output only. Derived details about the company.
  DerivedInfo derived_info = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether a company is flagged to be suspended from
  // public availability by the service when job content appears suspicious,
  // abusive, or spammy.
  bool suspended = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}
