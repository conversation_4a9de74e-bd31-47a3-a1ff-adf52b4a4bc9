{"methodConfig": [{"name": [{"service": "google.cloud.talent.v4.TenantService", "method": "Get<PERSON>enant"}, {"service": "google.cloud.talent.v4.TenantService", "method": "DeleteTenant"}, {"service": "google.cloud.talent.v4.TenantService", "method": "ListTenants"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.talent.v4.EventService", "method": "CreateClientEvent"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.talent.v4.JobService", "method": "Get<PERSON>ob"}, {"service": "google.cloud.talent.v4.JobService", "method": "DeleteJob"}, {"service": "google.cloud.talent.v4.JobService", "method": "ListJobs"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.talent.v4.JobService", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.talent.v4.JobService", "method": "Update<PERSON><PERSON>"}, {"service": "google.cloud.talent.v4.JobService", "method": "BatchDeleteJobs"}, {"service": "google.cloud.talent.v4.JobService", "method": "SearchJobs"}, {"service": "google.cloud.talent.v4.JobService", "method": "BatchCreateJobs"}, {"service": "google.cloud.talent.v4.JobService", "method": "BatchUpdateJobs"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.talent.v4.TenantService", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.talent.v4.TenantService", "method": "UpdateTenant"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.talent.v4.CompanyService", "method": "CreateCompany"}, {"service": "google.cloud.talent.v4.CompanyService", "method": "UpdateCompany"}], "timeout": "30s"}, {"name": [{"service": "google.cloud.talent.v4.CompanyService", "method": "GetCompany"}, {"service": "google.cloud.talent.v4.CompanyService", "method": "DeleteCompany"}, {"service": "google.cloud.talent.v4.CompanyService", "method": "ListCompanies"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.talent.v4.Completion", "method": "CompleteQuery"}], "timeout": "30s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}