// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.talent.v4;

import "google/api/field_behavior.proto";
import "google/cloud/talent/v4/common.proto";
import "google/protobuf/duration.proto";
import "google/type/latlng.proto";
import "google/type/timeofday.proto";

option go_package = "cloud.google.com/go/talent/apiv4/talentpb;talentpb";
option java_multiple_files = true;
option java_outer_classname = "FiltersProto";
option java_package = "com.google.cloud.talent.v4";
option objc_class_prefix = "CTS";

// The query required to perform a search query.
message JobQuery {
  // The query string that matches against the job title, description, and
  // location fields.
  //
  // The maximum number of allowed characters is 255.
  string query = 1;

  // The language code of [query][google.cloud.talent.v4.JobQuery.query]. For
  // example, "en-US". This field helps to better interpret the query.
  //
  // If a value isn't specified, the query language code is automatically
  // detected, which may not be accurate.
  //
  // Language code should be in BCP-47 format, such as "en-US" or "sr-Latn".
  // For more information, see
  // [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47).
  string query_language_code = 14;

  // This filter specifies the company entities to search against.
  //
  // If a value isn't specified, jobs are searched for against all
  // companies.
  //
  // If multiple values are specified, jobs are searched against the
  // companies specified.
  //
  // The format is
  // "projects/{project_id}/tenants/{tenant_id}/companies/{company_id}". For
  // example, "projects/foo/tenants/bar/companies/baz".
  //
  // At most 20 company filters are allowed.
  repeated string companies = 2;

  // The location filter specifies geo-regions containing the jobs to
  // search against. See [LocationFilter][google.cloud.talent.v4.LocationFilter]
  // for more information.
  //
  // If a location value isn't specified, jobs fitting the other search
  // criteria are retrieved regardless of where they're located.
  //
  // If multiple values are specified, jobs are retrieved from any of the
  // specified locations. If different values are specified for the
  // [LocationFilter.distance_in_miles][google.cloud.talent.v4.LocationFilter.distance_in_miles]
  // parameter, the maximum provided distance is used for all locations.
  //
  // At most 5 location filters are allowed.
  repeated LocationFilter location_filters = 3;

  // The category filter specifies the categories of jobs to search against.
  // See [JobCategory][google.cloud.talent.v4.JobCategory] for more information.
  //
  // If a value isn't specified, jobs from any category are searched against.
  //
  // If multiple values are specified, jobs from any of the specified
  // categories are searched against.
  repeated JobCategory job_categories = 4;

  // Allows filtering jobs by commute time with different travel methods (for
  //  example, driving or public transit).
  //
  // Note: This only works when you specify a
  // [CommuteMethod][google.cloud.talent.v4.CommuteMethod]. In this case,
  // [location_filters][google.cloud.talent.v4.JobQuery.location_filters] is
  // ignored.
  //
  //  Currently we don't support sorting by commute time.
  CommuteFilter commute_filter = 5;

  // This filter specifies the company
  // [Company.display_name][google.cloud.talent.v4.Company.display_name] of the
  // jobs to search against. The company name must match the value exactly.
  //
  // Alternatively, the value being searched for can be wrapped in different
  // match operators.
  // `SUBSTRING_MATCH([value])`
  // The company name must contain a case insensitive substring match of the
  // value. Using this function may increase latency.
  //
  // Sample Value: `SUBSTRING_MATCH(google)`
  //
  // `MULTI_WORD_TOKEN_MATCH([value])`
  // The value will be treated as a multi word token and the company name must
  // contain a case insensitive match of the value. Using this function may
  // increase latency.
  //
  // Sample Value: `MULTI_WORD_TOKEN_MATCH(google)`
  //
  // If a value isn't specified, jobs within the search results are
  // associated with any company.
  //
  // If multiple values are specified, jobs within the search results may be
  // associated with any of the specified companies.
  //
  // At most 20 company display name filters are allowed.
  repeated string company_display_names = 6;

  // This search filter is applied only to
  // [Job.compensation_info][google.cloud.talent.v4.Job.compensation_info]. For
  // example, if the filter is specified as "Hourly job with per-hour
  // compensation > $15", only jobs meeting these criteria are searched. If a
  // filter isn't defined, all open jobs are searched.
  CompensationFilter compensation_filter = 7;

  // This filter specifies a structured syntax to match against the
  // [Job.custom_attributes][google.cloud.talent.v4.Job.custom_attributes]
  // marked as `filterable`.
  //
  // The syntax for this expression is a subset of SQL syntax.
  //
  // Supported operators are: `=`, `!=`, `<`, `<=`, `>`, and `>=` where the
  // left of the operator is a custom field key and the right of the operator
  // is a number or a quoted string. You must escape backslash (\\) and
  // quote (\") characters.
  //
  // Supported functions are `LOWER([field_name])` to
  // perform a case insensitive match and `EMPTY([field_name])` to filter on the
  // existence of a key.
  //
  // Boolean expressions (AND/OR/NOT) are supported up to 3 levels of
  // nesting (for example, "((A AND B AND C) OR NOT D) AND E"), a maximum of 100
  // comparisons or functions are allowed in the expression. The expression
  // must be < 10000 bytes in length.
  //
  // Sample Query:
  // `(LOWER(driving_license)="class \"a\"" OR EMPTY(driving_license)) AND
  // driving_years > 10`
  string custom_attribute_filter = 8;

  // This flag controls the spell-check feature. If false, the
  // service attempts to correct a misspelled query,
  // for example, "enginee" is corrected to "engineer".
  //
  // Defaults to false: a spell check is performed.
  bool disable_spell_check = 9;

  // The employment type filter specifies the employment type of jobs to
  // search against, such as
  // [EmploymentType.FULL_TIME][google.cloud.talent.v4.EmploymentType.FULL_TIME].
  //
  // If a value isn't specified, jobs in the search results includes any
  // employment type.
  //
  // If multiple values are specified, jobs in the search results include
  // any of the specified employment types.
  repeated EmploymentType employment_types = 10;

  // This filter specifies the locale of jobs to search against,
  // for example, "en-US".
  //
  // If a value isn't specified, the search results can contain jobs in any
  // locale.
  //
  //
  // Language codes should be in BCP-47 format, such as "en-US" or "sr-Latn".
  // For more information, see
  // [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47).
  //
  // At most 10 language code filters are allowed.
  repeated string language_codes = 11;

  // Jobs published within a range specified by this filter are searched
  // against.
  TimestampRange publish_time_range = 12;

  // This filter specifies a list of job names to be excluded during search.
  //
  // At most 400 excluded job names are allowed.
  repeated string excluded_jobs = 13;
}

// Geographic region of the search.
message LocationFilter {
  // Specify whether to include telecommute jobs.
  enum TelecommutePreference {
    // Default value if the telecommute preference isn't specified.
    TELECOMMUTE_PREFERENCE_UNSPECIFIED = 0;

    // Deprecated: Ignore telecommute status of jobs. Use
    // TELECOMMUTE_JOBS_EXCLUDED if want to exclude telecommute jobs.
    TELECOMMUTE_EXCLUDED = 1 [deprecated = true];

    // Allow telecommute jobs.
    TELECOMMUTE_ALLOWED = 2;

    // Exclude telecommute jobs.
    TELECOMMUTE_JOBS_EXCLUDED = 3;
  }

  // The address name, such as "Mountain View" or "Bay Area".
  string address = 1;

  // CLDR region code of the country/region. This field may be used in two ways:
  //
  // 1) If telecommute preference is not set, this field is used address
  // ambiguity of the user-input address. For example, "Liverpool" may refer to
  // "Liverpool, NY, US" or "Liverpool, UK". This region code biases the
  // address resolution toward a specific country or territory. If this field is
  // not set, address resolution is biased toward the United States by default.
  //
  // 2) If telecommute preference is set to TELECOMMUTE_ALLOWED, the
  // telecommute location filter will be limited to the region specified in this
  // field. If this field is not set, the telecommute job locations will not be
  //
  // See
  // https://unicode-org.github.io/cldr-staging/charts/latest/supplemental/territory_information.html
  // for details. Example: "CH" for Switzerland.
  string region_code = 2;

  // The latitude and longitude of the geographic center to search from. This
  // field is ignored if `address` is provided.
  google.type.LatLng lat_lng = 3;

  // The distance_in_miles is applied when the location being searched for is
  // identified as a city or smaller. This field is ignored if the location
  // being searched for is a state or larger.
  double distance_in_miles = 4;

  // Allows the client to return jobs without a
  // set location, specifically, telecommuting jobs (telecommuting is considered
  // by the service as a special location).
  // [Job.posting_region][google.cloud.talent.v4.Job.posting_region] indicates
  // if a job permits telecommuting. If this field is set to
  // [TelecommutePreference.TELECOMMUTE_ALLOWED][google.cloud.talent.v4.LocationFilter.TelecommutePreference.TELECOMMUTE_ALLOWED],
  // telecommuting jobs are searched, and
  // [address][google.cloud.talent.v4.LocationFilter.address] and
  // [lat_lng][google.cloud.talent.v4.LocationFilter.lat_lng] are ignored. If
  // not set or set to
  // [TelecommutePreference.TELECOMMUTE_EXCLUDED][google.cloud.talent.v4.LocationFilter.TelecommutePreference.TELECOMMUTE_EXCLUDED],
  // the telecommute status of the jobs is ignored. Jobs that have
  // [PostingRegion.TELECOMMUTE][google.cloud.talent.v4.PostingRegion.TELECOMMUTE]
  // and have additional [Job.addresses][google.cloud.talent.v4.Job.addresses]
  // may still be matched based on other location filters using
  // [address][google.cloud.talent.v4.LocationFilter.address] or
  // [lat_lng][google.cloud.talent.v4.LocationFilter.lat_lng].
  //
  // This filter can be used by itself to search exclusively for telecommuting
  // jobs, or it can be combined with another location
  // filter to search for a combination of job locations,
  // such as "Mountain View" or "telecommuting" jobs. However, when used in
  // combination with other location filters, telecommuting jobs can be
  // treated as less relevant than other jobs in the search response.
  //
  // This field is only used for job search requests.
  TelecommutePreference telecommute_preference = 5;
}

// Filter on job compensation type and amount.
message CompensationFilter {
  // Specify the type of filtering.
  enum FilterType {
    // Filter type unspecified. Position holder, INVALID, should never be used.
    FILTER_TYPE_UNSPECIFIED = 0;

    // Filter by `base compensation entry's` unit. A job is a match if and
    // only if the job contains a base CompensationEntry and the base
    // CompensationEntry's unit matches provided
    // [units][google.cloud.talent.v4.CompensationFilter.units]. Populate one or
    // more [units][google.cloud.talent.v4.CompensationFilter.units].
    //
    // See
    // [CompensationInfo.CompensationEntry][google.cloud.talent.v4.CompensationInfo.CompensationEntry]
    // for definition of base compensation entry.
    UNIT_ONLY = 1;

    // Filter by `base compensation entry's` unit and amount / range. A job
    // is a match if and only if the job contains a base CompensationEntry, and
    // the base entry's unit matches provided
    // [CompensationUnit][google.cloud.talent.v4.CompensationInfo.CompensationUnit]
    // and amount or range overlaps with provided
    // [CompensationRange][google.cloud.talent.v4.CompensationInfo.CompensationRange].
    //
    // See
    // [CompensationInfo.CompensationEntry][google.cloud.talent.v4.CompensationInfo.CompensationEntry]
    // for definition of base compensation entry.
    //
    // Set exactly one [units][google.cloud.talent.v4.CompensationFilter.units]
    // and populate [range][google.cloud.talent.v4.CompensationFilter.range].
    UNIT_AND_AMOUNT = 2;

    // Filter by annualized base compensation amount and `base compensation
    // entry's` unit. Populate
    // [range][google.cloud.talent.v4.CompensationFilter.range] and zero or more
    // [units][google.cloud.talent.v4.CompensationFilter.units].
    ANNUALIZED_BASE_AMOUNT = 3;

    // Filter by annualized total compensation amount and `base compensation
    // entry's` unit . Populate
    // [range][google.cloud.talent.v4.CompensationFilter.range] and zero or more
    // [units][google.cloud.talent.v4.CompensationFilter.units].
    ANNUALIZED_TOTAL_AMOUNT = 4;
  }

  // Required. Type of filter.
  FilterType type = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Specify desired `base compensation entry's`
  // [CompensationInfo.CompensationUnit][google.cloud.talent.v4.CompensationInfo.CompensationUnit].
  repeated CompensationInfo.CompensationUnit units = 2
      [(google.api.field_behavior) = REQUIRED];

  // Compensation range.
  CompensationInfo.CompensationRange range = 3;

  // If set to true, jobs with unspecified compensation range fields are
  // included.
  bool include_jobs_with_unspecified_compensation_range = 4;
}

// Parameters needed for commute search.
message CommuteFilter {
  // The traffic density to use when calculating commute time.
  enum RoadTraffic {
    // Road traffic situation isn't specified.
    ROAD_TRAFFIC_UNSPECIFIED = 0;

    // Optimal commute time without considering any traffic impact.
    TRAFFIC_FREE = 1;

    // Commute time calculation takes in account the peak traffic impact.
    BUSY_HOUR = 2;
  }

  // Required. The method of transportation to calculate the commute time for.
  CommuteMethod commute_method = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The latitude and longitude of the location to calculate the
  // commute time from.
  google.type.LatLng start_coordinates = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The maximum travel time in seconds. The maximum allowed value is
  // `3600s` (one hour). Format is `123s`.
  google.protobuf.Duration travel_duration = 3
      [(google.api.field_behavior) = REQUIRED];

  // If `true`, jobs without street level addresses may also be returned.
  // For city level addresses, the city center is used. For state and coarser
  // level addresses, text matching is used.
  // If this field is set to `false` or isn't specified, only jobs that include
  // street level addresses will be returned by commute search.
  bool allow_imprecise_addresses = 4;

  // Traffic factor to take into account while searching by commute.
  oneof traffic_option {
    // Specifies the traffic density to use when calculating commute time.
    RoadTraffic road_traffic = 5;

    // The departure time used to calculate traffic impact, represented as
    // [google.type.TimeOfDay][google.type.TimeOfDay] in local time zone.
    //
    // Currently traffic model is restricted to hour level resolution.
    google.type.TimeOfDay departure_time = 6;
  }
}
