// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.talent.v4;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/talent/v4/common.proto";
import "google/cloud/talent/v4/tenant.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/talent/apiv4/talentpb;talentpb";
option java_multiple_files = true;
option java_outer_classname = "TenantServiceProto";
option java_package = "com.google.cloud.talent.v4";
option objc_class_prefix = "CTS";

// A service that handles tenant management, including CRUD and enumeration.
service TenantService {
  option (google.api.default_host) = "jobs.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/jobs";

  // Creates a new tenant entity.
  rpc CreateTenant(CreateTenantRequest) returns (Tenant) {
    option (google.api.http) = {
      post: "/v4/{parent=projects/*}/tenants"
      body: "tenant"
    };
    option (google.api.method_signature) = "parent,tenant";
  }

  // Retrieves specified tenant.
  rpc GetTenant(GetTenantRequest) returns (Tenant) {
    option (google.api.http) = {
      get: "/v4/{name=projects/*/tenants/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates specified tenant.
  rpc UpdateTenant(UpdateTenantRequest) returns (Tenant) {
    option (google.api.http) = {
      patch: "/v4/{tenant.name=projects/*/tenants/*}"
      body: "tenant"
    };
    option (google.api.method_signature) = "tenant,update_mask";
  }

  // Deletes specified tenant.
  rpc DeleteTenant(DeleteTenantRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v4/{name=projects/*/tenants/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all tenants associated with the project.
  rpc ListTenants(ListTenantsRequest) returns (ListTenantsResponse) {
    option (google.api.http) = {
      get: "/v4/{parent=projects/*}/tenants"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The Request of the CreateTenant method.
message CreateTenantRequest {
  // Required. Resource name of the project under which the tenant is created.
  //
  // The format is "projects/{project_id}", for example,
  // "projects/foo".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The tenant to be created.
  Tenant tenant = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for getting a tenant by name.
message GetTenantRequest {
  // Required. The resource name of the tenant to be retrieved.
  //
  // The format is "projects/{project_id}/tenants/{tenant_id}", for example,
  // "projects/foo/tenants/bar".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "jobs.googleapis.com/Tenant" }
  ];
}

// Request for updating a specified tenant.
message UpdateTenantRequest {
  // Required. The tenant resource to replace the current resource in the
  // system.
  Tenant tenant = 1 [(google.api.field_behavior) = REQUIRED];

  // Strongly recommended for the best service experience.
  //
  // If [update_mask][google.cloud.talent.v4.UpdateTenantRequest.update_mask] is
  // provided, only the specified fields in
  // [tenant][google.cloud.talent.v4.UpdateTenantRequest.tenant] are updated.
  // Otherwise all the fields are updated.
  //
  // A field mask to specify the tenant fields to be updated. Only
  // top level fields of [Tenant][google.cloud.talent.v4.Tenant] are supported.
  google.protobuf.FieldMask update_mask = 2;
}

// Request to delete a tenant.
message DeleteTenantRequest {
  // Required. The resource name of the tenant to be deleted.
  //
  // The format is "projects/{project_id}/tenants/{tenant_id}", for example,
  // "projects/foo/tenants/bar".
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "jobs.googleapis.com/Tenant" }
  ];
}

// List tenants for which the client has ACL visibility.
message ListTenantsRequest {
  // Required. Resource name of the project under which the tenant is created.
  //
  // The format is "projects/{project_id}", for example,
  // "projects/foo".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // The starting indicator from which to return results.
  string page_token = 2;

  // The maximum number of tenants to be returned, at most 100.
  // Default is 100 if a non-positive number is provided.
  int32 page_size = 3;
}

// The List tenants response object.
message ListTenantsResponse {
  // Tenants for the current client.
  repeated Tenant tenants = 1;

  // A token to retrieve the next page of results.
  string next_page_token = 2;

  // Additional information for the API invocation, such as the request
  // tracking id.
  ResponseMetadata metadata = 3;
}
