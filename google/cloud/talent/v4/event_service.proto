// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.talent.v4;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/talent/v4/event.proto";

option go_package = "cloud.google.com/go/talent/apiv4/talentpb;talentpb";
option java_multiple_files = true;
option java_outer_classname = "EventServiceProto";
option java_package = "com.google.cloud.talent.v4";
option objc_class_prefix = "CTS";

// A service handles client event report.
service EventService {
  option (google.api.default_host) = "jobs.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/jobs";

  // Report events issued when end user interacts with customer's application
  // that uses Cloud Talent Solution. You may inspect the created events in
  // [self service
  // tools](https://console.cloud.google.com/talent-solution/overview).
  // [Learn
  // more](https://cloud.google.com/talent-solution/docs/management-tools)
  // about self service tools.
  rpc CreateClientEvent(CreateClientEventRequest) returns (ClientEvent) {
    option (google.api.http) = {
      post: "/v4/{parent=projects/*/tenants/*}/clientEvents"
      body: "client_event"
    };
    option (google.api.method_signature) = "parent,client_event";
  }
}

// The report event request.
message CreateClientEventRequest {
  // Required. Resource name of the tenant under which the event is created.
  //
  // The format is "projects/{project_id}/tenants/{tenant_id}", for example,
  // "projects/foo/tenants/bar".
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "jobs.googleapis.com/Tenant" }
  ];

  // Required. Events issued when end user interacts with customer's application
  // that uses Cloud Talent Solution.
  ClientEvent client_event = 2 [(google.api.field_behavior) = REQUIRED];
}
