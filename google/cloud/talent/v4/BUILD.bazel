# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "talent_proto",
    srcs = [
        "common.proto",
        "company.proto",
        "company_service.proto",
        "completion_service.proto",
        "event.proto",
        "event_service.proto",
        "filters.proto",
        "histogram.proto",
        "job.proto",
        "job_service.proto",
        "tenant.proto",
        "tenant_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "//google/type:latlng_proto",
        "//google/type:money_proto",
        "//google/type:postal_address_proto",
        "//google/type:timeofday_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "talent_proto_with_info",
    deps = [
        ":talent_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "talent_java_proto",
    deps = [":talent_proto"],
)

java_grpc_library(
    name = "talent_java_grpc",
    srcs = [":talent_proto"],
    deps = [":talent_java_proto"],
)

java_gapic_library(
    name = "talent_java_gapic",
    srcs = [":talent_proto_with_info"],
    grpc_service_config = "talent_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "jobs_v4.yaml",
    test_deps = [
        ":talent_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":talent_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "talent_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.talent.v4.CompanyServiceClientHttpJsonTest",
        "com.google.cloud.talent.v4.CompanyServiceClientTest",
        "com.google.cloud.talent.v4.CompletionClientHttpJsonTest",
        "com.google.cloud.talent.v4.CompletionClientTest",
        "com.google.cloud.talent.v4.EventServiceClientHttpJsonTest",
        "com.google.cloud.talent.v4.EventServiceClientTest",
        "com.google.cloud.talent.v4.JobServiceClientHttpJsonTest",
        "com.google.cloud.talent.v4.JobServiceClientTest",
        "com.google.cloud.talent.v4.TenantServiceClientHttpJsonTest",
        "com.google.cloud.talent.v4.TenantServiceClientTest",
    ],
    runtime_deps = [":talent_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-talent-v4-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":talent_java_gapic",
        ":talent_java_grpc",
        ":talent_java_proto",
        ":talent_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "talent_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/talent/apiv4/talentpb",
    protos = [":talent_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:latlng_go_proto",
        "//google/type:money_go_proto",
        "//google/type:postaladdress_go_proto",
        "//google/type:timeofday_go_proto",
    ],
)

go_gapic_library(
    name = "talent_go_gapic",
    srcs = [":talent_proto_with_info"],
    grpc_service_config = "talent_grpc_service_config.json",
    importpath = "cloud.google.com/go/talent/apiv4;talent",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "jobs_v4.yaml",
    transport = "grpc+rest",
    deps = [
        ":talent_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-talent-v4-go",
    deps = [
        ":talent_go_gapic",
        ":talent_go_gapic_srcjar-snippets.srcjar",
        ":talent_go_gapic_srcjar-test.srcjar",
        ":talent_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "talent_py_gapic",
    srcs = [":talent_proto"],
    grpc_service_config = "talent_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "jobs_v4.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "talent_py_gapic_test",
    srcs = [
        "talent_py_gapic_pytest.py",
        "talent_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":talent_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "talent-v4-py",
    deps = [
        ":talent_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "talent_php_proto",
    deps = [":talent_proto"],
)

php_gapic_library(
    name = "talent_php_gapic",
    srcs = [":talent_proto_with_info"],
    grpc_service_config = "talent_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "jobs_v4.yaml",
    transport = "grpc+rest",
    deps = [":talent_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-talent-v4-php",
    deps = [
        ":talent_php_gapic",
        ":talent_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "talent_nodejs_gapic",
    package_name = "@google-cloud/talent",
    src = ":talent_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "talent_grpc_service_config.json",
    main_service = "talent",
    package = "google.cloud.talent.v4",
    rest_numeric_enums = True,
    service_yaml = "jobs_v4.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "talent-v4-nodejs",
    deps = [
        ":talent_nodejs_gapic",
        ":talent_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "talent_ruby_proto",
    deps = [":talent_proto"],
)

ruby_grpc_library(
    name = "talent_ruby_grpc",
    srcs = [":talent_proto"],
    deps = [":talent_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "talent_ruby_gapic",
    srcs = [":talent_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-talent-v4",
        "ruby-cloud-env-prefix=TALENT",
        "ruby-cloud-product-url=https://cloud.google.com/solutions/talent-solution",
        "ruby-cloud-api-id=jobs.googleapis.com",
        "ruby-cloud-api-shortname=jobs",
    ],
    grpc_service_config = "talent_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Transform your job search and candidate matching capabilities with Cloud Talent Solution, designed to support enterprise talent acquisition technology and evolve with your growing needs. This AI solution includes features such as Job Search and Profile Search to provide candidates and employers with an enhanced talent acquisition experience.",
    ruby_cloud_title = "Cloud Talent Solution V4",
    service_yaml = "jobs_v4.yaml",
    transport = "grpc+rest",
    deps = [
        ":talent_ruby_grpc",
        ":talent_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-talent-v4-ruby",
    deps = [
        ":talent_ruby_gapic",
        ":talent_ruby_grpc",
        ":talent_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "talent_csharp_proto",
    deps = [":talent_proto"],
)

csharp_grpc_library(
    name = "talent_csharp_grpc",
    srcs = [":talent_proto"],
    deps = [":talent_csharp_proto"],
)

csharp_gapic_library(
    name = "talent_csharp_gapic",
    srcs = [":talent_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "talent_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "jobs_v4.yaml",
    transport = "grpc+rest",
    deps = [
        ":talent_csharp_grpc",
        ":talent_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-talent-v4-csharp",
    deps = [
        ":talent_csharp_gapic",
        ":talent_csharp_grpc",
        ":talent_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "talent_cc_proto",
    deps = [":talent_proto"],
)

cc_grpc_library(
    name = "talent_cc_grpc",
    srcs = [":talent_proto"],
    grpc_only = True,
    deps = [":talent_cc_proto"],
)
