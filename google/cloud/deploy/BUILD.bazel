# This build file includes a target for the Ruby wrapper library for
# google-cloud-deploy.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for clouddeploy.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "clouddeploy_ruby_wrapper",
    srcs = ["//google/cloud/deploy/v1:deploy_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-deploy",
        "ruby-cloud-wrapper-of=v1:0.17",
        "ruby-cloud-product-url=https://cloud.google.com/deploy/",
        "ruby-cloud-api-id=clouddeploy.googleapis.com",
        "ruby-cloud-api-shortname=clouddeploy",
    ],
    ruby_cloud_description = "Google Cloud Deploy is a managed service that automates delivery of your applications to a series of target environments in a defined promotion sequence.",
    ruby_cloud_title = "Google Cloud Deploy",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-deploy-ruby",
    deps = [
        ":clouddeploy_ruby_wrapper",
    ],
)
