// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.deploy.v1;

import "google/cloud/deploy/v1/cloud_deploy.proto";

option go_package = "cloud.google.com/go/deploy/apiv1/deploypb;deploypb";
option java_multiple_files = true;
option java_outer_classname = "DeployPolicyEvaluationPayloadProto";
option java_package = "com.google.cloud.deploy.v1";

// Payload proto for "clouddeploy.googleapis.com/deploypolicy_evaluation"
// Platform Log event that describes the deploy policy evaluation event.
message DeployPolicyEvaluationEvent {
  // The policy verdict of the request.
  enum PolicyVerdict {
    // This should never happen.
    POLICY_VERDICT_UNSPECIFIED = 0;

    // Allowed by policy. This enum value is not currently used but may be used
    // in the future. Currently logs are only generated when a request is denied
    // by policy.
    ALLOWED_BY_POLICY = 1;

    // Denied by policy.
    DENIED_BY_POLICY = 2;
  }

  // Things that could have overridden the policy verdict. When overrides are
  // used, the request will be allowed even if it is DENIED_BY_POLICY.
  enum PolicyVerdictOverride {
    // This should never happen.
    POLICY_VERDICT_OVERRIDE_UNSPECIFIED = 0;

    // The policy was overridden.
    POLICY_OVERRIDDEN = 1;

    // The policy was suspended.
    POLICY_SUSPENDED = 2;
  }

  // Debug message for when a deploy policy event occurs.
  string message = 1;

  // Rule type (e.g. Restrict Rollouts).
  string rule_type = 2;

  // Rule id.
  string rule = 3;

  // Unique identifier of the `Delivery Pipeline`.
  string pipeline_uid = 4;

  // The name of the `Delivery Pipeline`.
  string delivery_pipeline = 5;

  // Unique identifier of the `Target`. This is an optional field, as a `Target`
  // may not always be applicable to a policy.
  string target_uid = 6;

  // The name of the `Target`. This is an optional field, as a `Target` may not
  // always be applicable to a policy.
  string target = 7;

  // What invoked the action (e.g. a user or automation).
  DeployPolicy.Invoker invoker = 8;

  // The name of the `DeployPolicy`.
  string deploy_policy = 9;

  // Unique identifier of the `DeployPolicy`.
  string deploy_policy_uid = 10;

  // Whether the request is allowed. Allowed is set as true if:
  // (1) the request complies with the policy; or
  // (2) the request doesn't comply with the policy but the policy was
  // overridden; or
  // (3) the request doesn't comply with the policy but the policy was suspended
  bool allowed = 11;

  // The policy verdict of the request.
  PolicyVerdict verdict = 12;

  // Things that could have overridden the policy verdict. Overrides together
  // with verdict decide whether the request is allowed.
  repeated PolicyVerdictOverride overrides = 13;
}
