// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.deploy.v1;

import "google/cloud/deploy/v1/log_enums.proto";

option go_package = "cloud.google.com/go/deploy/apiv1/deploypb;deploypb";
option java_multiple_files = true;
option java_outer_classname = "RolloutUpdatePayloadProto";
option java_package = "com.google.cloud.deploy.v1";

// Payload proto for "clouddeploy.googleapis.com/rollout_update"
// Platform Log event that describes the rollout update event.
message RolloutUpdateEvent {
  // RolloutUpdateType indicates the type of the rollout update.
  enum RolloutUpdateType {
    // Rollout update type unspecified.
    ROLLOUT_UPDATE_TYPE_UNSPECIFIED = 0;

    // rollout state updated to pending.
    PENDING = 1;

    // Rollout state updated to pending release.
    PENDING_RELEASE = 2;

    // Rollout state updated to in progress.
    IN_PROGRESS = 3;

    // Rollout state updated to cancelling.
    CANCELLING = 4;

    // Rollout state updated to cancelled.
    CANCELLED = 5;

    // Rollout state updated to halted.
    HALTED = 6;

    // Rollout state updated to succeeded.
    SUCCEEDED = 7;

    // Rollout state updated to failed.
    FAILED = 8;

    // Rollout requires approval.
    APPROVAL_REQUIRED = 9;

    // Rollout has been approved.
    APPROVED = 10;

    // Rollout has been rejected.
    REJECTED = 11;

    // Rollout requires advance to the next phase.
    ADVANCE_REQUIRED = 12;

    // Rollout has been advanced.
    ADVANCED = 13;
  }

  // Debug message for when a rollout update event occurs.
  string message = 6;

  // Unique identifier of the pipeline.
  string pipeline_uid = 1;

  // Unique identifier of the release.
  string release_uid = 2;

  // The name of the `Release`.
  string release = 8;

  // The name of the rollout.
  // rollout_uid is not in this log message because we write some of these log
  // messages at rollout creation time, before we've generated the uid.
  string rollout = 3;

  // ID of the target.
  string target_id = 4;

  // Type of this notification, e.g. for a rollout update event.
  Type type = 7;

  // The type of the rollout update.
  RolloutUpdateType rollout_update_type = 5;
}
