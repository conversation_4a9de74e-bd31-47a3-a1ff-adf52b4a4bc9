{"methodConfig": [{"name": [{"service": "google.cloud.deploy.v1.CloudDeploy", "method": "ListDeliveryPipelines"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "GetDeliveryPipeline"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "ListTargets"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "<PERSON><PERSON><PERSON>get"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "ListReleases"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "GetRelease"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "ListRollouts"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "GetRollout"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "ListJobRuns"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "GetJobRun"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "GetConfig"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "ListAutomationRuns"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "GetAutomationRun"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "ListAutomations"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "GetAutomation"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "ListCustomTargetTypes"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "GetCustomTargetType"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "ListDeployPolicies"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "GetDeployPolicy"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.deploy.v1.CloudDeploy", "method": "CreateDeliveryPipeline"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "UpdateDeliveryPipeline"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "DeleteDeliveryPipeline"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "Update<PERSON>ar<PERSON>"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "DeleteTarget"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "CreateRelease"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "AbandonRelease"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "ApproveRollout"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "CreateRollout"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "AdvanceRollout"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "CancelRollout"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "IgnoreJob"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "TerminateJobRun"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "CancelAutomationRun"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "DeleteAutomation"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "UpdateAutomation"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "CreateAutomation"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "DeleteCustomTargetType"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "UpdateCustomTargetType"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "CreateCustomTargetType"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "DeleteDeployPolicy"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "UpdateDeployPolicy"}, {"service": "google.cloud.deploy.v1.CloudDeploy", "method": "CreateDeployPolicy"}], "timeout": "60s"}]}