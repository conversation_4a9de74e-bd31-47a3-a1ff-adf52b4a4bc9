// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.deploy.v1;

import "google/cloud/deploy/v1/cloud_deploy.proto";
import "google/cloud/deploy/v1/log_enums.proto";

option go_package = "cloud.google.com/go/deploy/apiv1/deploypb;deploypb";
option java_multiple_files = true;
option java_outer_classname = "ReleaseRenderPayloadProto";
option java_package = "com.google.cloud.deploy.v1";

// Payload proto for "clouddeploy.googleapis.com/release_render"
// Platform Log event that describes the render status change.
message ReleaseRenderEvent {
  // Debug message for when a render transition occurs. Provides further
  // details as rendering progresses through render states.
  string message = 1;

  // Unique identifier of the `DeliveryPipeline`.
  string pipeline_uid = 4;

  // The name of the release.
  // release_uid is not in this log message because we write some of these log
  // messages at release creation time, before we've generated the uid.
  string release = 2;

  // Type of this notification, e.g. for a release render state change event.
  Type type = 5;

  // The state of the release render.
  Release.RenderState release_render_state = 3;
}
