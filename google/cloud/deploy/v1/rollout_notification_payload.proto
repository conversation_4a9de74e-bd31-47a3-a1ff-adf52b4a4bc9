// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.deploy.v1;

import "google/cloud/deploy/v1/log_enums.proto";

option go_package = "cloud.google.com/go/deploy/apiv1/deploypb;deploypb";
option java_multiple_files = true;
option java_outer_classname = "RolloutNotificationPayloadProto";
option java_package = "com.google.cloud.deploy.v1";

// Payload proto for "clouddeploy.googleapis.com/rollout_notification"
// Platform Log event that describes the failure to send rollout status change
// Pub/Sub notification.
message RolloutNotificationEvent {
  // Debug message for when a notification fails to send.
  string message = 1;

  // Unique identifier of the `DeliveryPipeline`.
  string pipeline_uid = 2;

  // Unique identifier of the `Release`.
  string release_uid = 3;

  // The name of the `Release`.
  string release = 7;

  // Unique identifier of the `Rollout`.
  string rollout_uid = 8;

  // The name of the `Rollout`.
  string rollout = 4;

  // ID of the `Target` that the rollout is deployed to.
  string target_id = 6;

  // Type of this notification, e.g. for a Pub/Sub failure.
  Type type = 5;
}
