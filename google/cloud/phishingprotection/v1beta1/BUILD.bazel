# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "phishingprotection_proto",
    srcs = [
        "phishingprotection.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
    ],
)

proto_library_with_info(
    name = "phishingprotection_proto_with_info",
    deps = [
        ":phishingprotection_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "phishingprotection_java_proto",
    deps = [":phishingprotection_proto"],
)

java_grpc_library(
    name = "phishingprotection_java_grpc",
    srcs = [":phishingprotection_proto"],
    deps = [":phishingprotection_java_proto"],
)

java_gapic_library(
    name = "phishingprotection_java_gapic",
    srcs = [":phishingprotection_proto_with_info"],
    gapic_yaml = "phishingprotection_gapic.yaml",
    grpc_service_config = "phishingprotection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "phishingprotection_v1beta1.yaml",
    test_deps = [
        ":phishingprotection_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":phishingprotection_java_proto",
    ],
)

java_gapic_test(
    name = "phishingprotection_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.phishingprotection.v1beta1.PhishingProtectionServiceV1Beta1ClientHttpJsonTest",
        "com.google.cloud.phishingprotection.v1beta1.PhishingProtectionServiceV1Beta1ClientTest",
    ],
    runtime_deps = [":phishingprotection_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-phishingprotection-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":phishingprotection_java_gapic",
        ":phishingprotection_java_grpc",
        ":phishingprotection_java_proto",
        ":phishingprotection_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "phishingprotection_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/phishingprotection/apiv1beta1/phishingprotectionpb",
    protos = [":phishingprotection_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "phishingprotection_go_gapic",
    srcs = [":phishingprotection_proto_with_info"],
    grpc_service_config = "phishingprotection_grpc_service_config.json",
    importpath = "cloud.google.com/go/phishingprotection/apiv1beta1;phishingprotection",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "phishingprotection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":phishingprotection_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-phishingprotection-v1beta1-go",
    deps = [
        ":phishingprotection_go_gapic",
        ":phishingprotection_go_gapic_srcjar-snippets.srcjar",
        ":phishingprotection_go_gapic_srcjar-test.srcjar",
        ":phishingprotection_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "phishingprotection_py_gapic",
    srcs = [":phishingprotection_proto"],
    grpc_service_config = "phishingprotection_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-phishing-protection",
    ],
    rest_numeric_enums = True,
    service_yaml = "phishingprotection_v1beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "phishingprotection_py_gapic_test",
    srcs = [
        "phishingprotection_py_gapic_pytest.py",
        "phishingprotection_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":phishingprotection_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "phishingprotection-v1beta1-py",
    deps = [
        ":phishingprotection_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "phishingprotection_php_proto",
    deps = [":phishingprotection_proto"],
)

php_gapic_library(
    name = "phishingprotection_php_gapic",
    srcs = [":phishingprotection_proto_with_info"],
    grpc_service_config = "phishingprotection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "phishingprotection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":phishingprotection_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-phishingprotection-v1beta1-php",
    deps = [
        ":phishingprotection_php_gapic",
        ":phishingprotection_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "phishingprotection_nodejs_gapic",
    package_name = "@google-cloud/phishing-protection",
    src = ":phishingprotection_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "phishingprotection_grpc_service_config.json",
    package = "google.cloud.phishingprotection.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "phishingprotection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "phishingprotection-v1beta1-nodejs",
    deps = [
        ":phishingprotection_nodejs_gapic",
        ":phishingprotection_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "phishingprotection_ruby_proto",
    deps = [":phishingprotection_proto"],
)

ruby_grpc_library(
    name = "phishingprotection_ruby_grpc",
    srcs = [":phishingprotection_proto"],
    deps = [":phishingprotection_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "phishingprotection_ruby_gapic",
    srcs = [":phishingprotection_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-phishing_protection-v1beta1",
        "ruby-cloud-env-prefix=PHISHING_PROTECTION",
        "ruby-cloud-product-url=https://cloud.google.com/ruby/docs/reference/google-cloud-phishing_protection",
        "ruby-cloud-api-id=phishingprotection.googleapis.com",
        "ruby-cloud-api-shortname=phishingprotection",
        "ruby-cloud-service-override=PhishingProtectionServiceV1Beta1=PhishingProtectionService",
    ],
    grpc_service_config = "phishingprotection_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Phishing Protection helps prevent users from accessing phishing sites by identifying various signals associated with malicious content, including the use of your brand assets, classifying malicious content that uses your brand and reporting the unsafe URLs to Google Safe Browsing.",
    ruby_cloud_title = "Phishing Protection V1beta1",
    service_yaml = "phishingprotection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":phishingprotection_ruby_grpc",
        ":phishingprotection_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-phishingprotection-v1beta1-ruby",
    deps = [
        ":phishingprotection_ruby_gapic",
        ":phishingprotection_ruby_grpc",
        ":phishingprotection_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "phishingprotection_csharp_proto",
    deps = [":phishingprotection_proto"],
)

csharp_grpc_library(
    name = "phishingprotection_csharp_grpc",
    srcs = [":phishingprotection_proto"],
    deps = [":phishingprotection_csharp_proto"],
)

csharp_gapic_library(
    name = "phishingprotection_csharp_gapic",
    srcs = [":phishingprotection_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "phishingprotection_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "phishingprotection_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":phishingprotection_csharp_grpc",
        ":phishingprotection_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-phishingprotection-v1beta1-csharp",
    deps = [
        ":phishingprotection_csharp_gapic",
        ":phishingprotection_csharp_grpc",
        ":phishingprotection_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
