# This build file includes a target for the Ruby wrapper library for
# google-cloud-phishing_protection.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for phishingprotection.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta1 in this case.
ruby_cloud_gapic_library(
    name = "phishingprotection_ruby_wrapper",
    srcs = ["//google/cloud/phishingprotection/v1beta1:phishingprotection_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-phishing_protection",
        "ruby-cloud-env-prefix=PHISHING_PROTECTION",
        "ruby-cloud-wrapper-of=v1beta1:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/ruby/docs/reference/google-cloud-phishing_protection",
        "ruby-cloud-api-id=phishingprotection.googleapis.com",
        "ruby-cloud-api-shortname=phishingprotection",
        "ruby-cloud-migration-version=0.10",
        "ruby-cloud-service-override=PhishingProtectionServiceV1Beta1=PhishingProtectionService",
    ],
    ruby_cloud_description = "Phishing Protection helps prevent users from accessing phishing sites by identifying various signals associated with malicious content, including the use of your brand assets, classifying malicious content that uses your brand and reporting the unsafe URLs to Google Safe Browsing.",
    ruby_cloud_title = "Phishing Protection",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-phishingprotection-ruby",
    deps = [
        ":phishingprotection_ruby_wrapper",
    ],
)
