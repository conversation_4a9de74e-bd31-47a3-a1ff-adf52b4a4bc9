// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.workflows.type;

import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/workflows/type/typepb;typepb";

// Logged during the lifetime of Workflow Execution.
message ExecutionsSystemLog {
  // Possible states of the execution. There could be more states in the future.
  enum State {
    // Invalid state.
    STATE_UNSPECIFIED = 0;

    // The Workflow Execution is in progress.
    ACTIVE = 1;

    // The Workflow Execution has finished successfully.
    SUCCEEDED = 2;

    // The Workflow Execution failed with an error.
    FAILED = 3;

    // The Workflow Execution has been stopped intentionally.
    CANCELLED = 4;
  }

  // Detailed information about the start of the execution.
  message Start {
    // The execution input argument.
    string argument = 2;
  }

  // Detailed information about the successful finish of the execution.
  message Success {
    // The final result of the execution.
    string result = 2;
  }

  // Detailed information about the execution failure.
  message Failure {
    // The exception message, e.g. "division by zero". The size limit is 1 kB.
    string exception = 1;

    // The code location of the statement that has created the log. For example,
    // a log created in subworkflow 'Foo' in step 'bar' will have its source
    // equal to 'Foo.bar'. The size limit is 1 kB.
    string source = 2;
  }

  // Human readable contents of the log in English. The size limit is 5 kB.
  string message = 1;

  // The absolute point in time when the activity happened.
  google.protobuf.Timestamp activity_time = 2;

  // State of the execution when the log was created.
  State state = 3;

  // Detailed log information.
  oneof details {
    // Appears only in the log created when the execution has started.
    Start start = 4;

    // Appears only in the log created when the execution has finished
    // successfully.
    Success success = 5;

    // Appears only in the log created when the execution has failed.
    Failure failure = 6;
  }
}
