// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.workflows.type;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/workflows/type/typepb;typepb";
option java_multiple_files = true;
option java_outer_classname = "EngineCallProto";
option java_package = "com.google.cloud.workflows.type";

// Logged during a workflow execution if the customer has requested call
// logging.
message EngineCallLog {
  // The state of a function call.
  enum State {
    // Function call state is unspecified or unknown.
    STATE_UNSPECIFIED = 0;

    // Function call is starting.
    BEGUN = 1;

    // Function call has completed successfully.
    SUCCEEDED = 2;

    // Function call did not succeed because an exception was raised.
    EXCEPTION_RAISED = 3;

    // Function call handled an exception and is continuing.
    EXCEPTION_HANDLED = 4;
  }

  // Information about an argument to a called function.
  message CallArg {
    // A function argument, serialized to a string. This may be truncated for
    // size reasons.
    string argument = 1;
  }

  // Information about the start of a call.
  message Begun {
    // The arguments passed to the function. Only one of 'args' and 'named_args'
    // will be populated.
    repeated CallArg args = 1;

    // The arguments passed to the function, as a map with the argument names as
    // the keys. The values may be JSON values or they may be the serialized
    // string forms of the arguments truncated for size reasons. Only one of
    // 'args' and 'named_args' will be populated.
    map<string, google.protobuf.Value> named_args = 2;
  }

  // Information about the end of a successful call.
  message Succeeded {
    // The time when the call started.
    google.protobuf.Timestamp call_start_time = 1;

    // The result of the call, if the call succeeded, serialized to a string.
    // This may be truncated for size reasons.
    string response = 2;
  }

  // Information about the end of a failed call.
  message ExceptionRaised {
    // The time when the call started.
    google.protobuf.Timestamp call_start_time = 1;

    // The exception message which terminated the call, truncated if necessary.
    string exception = 2;

    // The name of the step where the failure originates, if known. Truncated
    // if necessary.
    string origin = 3;
  }

  // Information about an exception which was handled.
  message ExceptionHandled {
    // The time when the call started.
    google.protobuf.Timestamp call_start_time = 1;

    // The exception message which was handled, truncated if necessary.
    string exception = 2;

    // The name of the step where the failure originates, if known. Truncated
    // if necessary.
    string origin = 3;
  }

  // The execution ID of the execution where the call occurred.
  string execution_id = 1;

  // The point in time when the activity occurred.
  google.protobuf.Timestamp activity_time = 2;

  // The state of the function execution.
  State state = 3;

  // The name of the step in which the call took place, truncated if necessary.
  string step = 4;

  // The name of the target of the call, truncated if necessary.
  string callee = 5;

  oneof details {
    // Appears at the start of a call.
    Begun begun = 6;

    // Appears when a call returns successfully.
    Succeeded succeeded = 7;

    // Appears when a call returns because an exception was raised.
    ExceptionRaised exception_raised = 8;

    // Appears when an exception is handled and normal execution resumes.
    ExceptionHandled exception_handled = 9;
  }
}
