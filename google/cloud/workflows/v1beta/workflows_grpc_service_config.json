{"methodConfig": [{"name": [{"service": "google.cloud.workflows.v1beta", "method": "ListWorkflows"}, {"service": "google.cloud.workflows.v1beta", "method": "GetWorkflow"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.workflows.v1beta", "method": "CreateWorkflow"}, {"service": "google.cloud.workflows.v1beta", "method": "DeleteWorkflow"}, {"service": "google.cloud.workflows.v1beta", "method": "UpdateWorkflow"}], "timeout": "60s"}]}