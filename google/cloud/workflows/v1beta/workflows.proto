// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.workflows.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/workflows/apiv1beta/workflowspb;workflowspb";
option java_multiple_files = true;
option java_outer_classname = "WorkflowsProto";
option java_package = "com.google.cloud.workflows.v1beta";

// Workflows is used to deploy and execute workflow programs.
// Workflows makes sure the program executes reliably, despite hardware and
// networking interruptions.
service Workflows {
  option (google.api.default_host) = "workflows.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists Workflows in a given project and location.
  // The default order is not specified.
  rpc ListWorkflows(ListWorkflowsRequest) returns (ListWorkflowsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*}/workflows"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Workflow.
  rpc GetWorkflow(GetWorkflowRequest) returns (Workflow) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/workflows/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new workflow. If a workflow with the specified name already
  // exists in the specified project and location, the long running operation
  // will return [ALREADY_EXISTS][google.rpc.Code.ALREADY_EXISTS] error.
  rpc CreateWorkflow(CreateWorkflowRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*}/workflows"
      body: "workflow"
    };
    option (google.api.method_signature) = "parent,workflow,workflow_id";
    option (google.longrunning.operation_info) = {
      response_type: "Workflow"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a workflow with the specified name.
  // This method also cancels and deletes all running executions of the
  // workflow.
  rpc DeleteWorkflow(DeleteWorkflowRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta/{name=projects/*/locations/*/workflows/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates an existing workflow.
  // Running this method has no impact on already running executions of the
  // workflow. A new revision of the workflow may be created as a result of a
  // successful update operation. In that case, such revision will be used
  // in new workflow executions.
  rpc UpdateWorkflow(UpdateWorkflowRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta/{workflow.name=projects/*/locations/*/workflows/*}"
      body: "workflow"
    };
    option (google.api.method_signature) = "workflow,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Workflow"
      metadata_type: "OperationMetadata"
    };
  }
}

// Workflow program to be executed by Workflows.
message Workflow {
  option (google.api.resource) = {
    type: "workflows.googleapis.com/Workflow"
    pattern: "projects/{project}/locations/{location}/workflows/{workflow}"
  };

  // Describes the current state of workflow deployment. More states may be
  // added in the future.
  enum State {
    // Invalid state.
    STATE_UNSPECIFIED = 0;

    // The workflow has been deployed successfully and is serving.
    ACTIVE = 1;
  }

  // The resource name of the workflow.
  // Format: projects/{project}/locations/{location}/workflows/{workflow}
  string name = 1;

  // Description of the workflow provided by the user.
  // Must be at most 1000 unicode characters long.
  string description = 2;

  // Output only. State of the workflow deployment.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The revision of the workflow.
  // A new revision of a workflow is created as a result of updating the
  // following fields of a workflow:
  // - `source_code`
  // - `service_account`
  // The format is "000001-a4d", where the first 6 characters define
  // the zero-padded revision ordinal number. They are followed by a hyphen and
  // 3 hexadecimal random characters.
  string revision_id = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp of when the workflow was created.
  google.protobuf.Timestamp create_time = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of the workflow.
  google.protobuf.Timestamp update_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp that the latest revision of the workflow
  // was created.
  google.protobuf.Timestamp revision_create_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels associated with this workflow.
  // Labels can contain at most 64 entries. Keys and values can be no longer
  // than 63 characters and can only contain lowercase letters, numeric
  // characters, underscores and dashes. Label keys must start with a letter.
  // International characters are allowed.
  map<string, string> labels = 8;

  // Name of the service account associated with the latest workflow version.
  // This service account represents the identity of the workflow and determines
  // what permissions the workflow has.
  // Format: projects/{project}/serviceAccounts/{account}
  //
  // Using `-` as a wildcard for the `{project}` will infer the project from
  // the account. The `{account}` value can be the `email` address or the
  // `unique_id` of the service account.
  //
  // If not provided, workflow will use the project's default service account.
  // Modifying this field for an existing workflow results in a new workflow
  // revision.
  string service_account = 9;

  // Required. Location of the workflow source code.
  // Modifying this field for an existing workflow results in a new workflow
  // revision.
  oneof source_code {
    // Workflow code to be executed. The size limit is 32KB.
    string source_contents = 10;
  }
}

// Request for the
// [ListWorkflows][google.cloud.workflows.v1beta.Workflows.ListWorkflows]
// method.
message ListWorkflowsRequest {
  // Required. Project and location from which the workflows should be listed.
  // Format: projects/{project}/locations/{location}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of workflows to return per call. The service may return
  // fewer than this value. If the value is not specified, a default value of
  // 500 will be used. The maximum permitted value is 1000 and values greater
  // than 1000 will be coerced down to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListWorkflows` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListWorkflows` must
  // match the call that provided the page token.
  string page_token = 3;

  // Filter to restrict results to specific workflows.
  string filter = 4;

  // Comma-separated list of fields that that specify the order of the results.
  // Default sorting order for a field is ascending. To specify descending order
  // for a field, append a " desc" suffix.
  // If not specified, the results will be returned in an unspecified order.
  string order_by = 5;
}

// Response for the
// [ListWorkflows][google.cloud.workflows.v1beta.Workflows.ListWorkflows]
// method.
message ListWorkflowsResponse {
  // The workflows which match the request.
  repeated Workflow workflows = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Unreachable resources.
  repeated string unreachable = 3;
}

// Request for the
// [GetWorkflow][google.cloud.workflows.v1beta.Workflows.GetWorkflow] method.
message GetWorkflowRequest {
  // Required. Name of the workflow which information should be retrieved.
  // Format: projects/{project}/locations/{location}/workflows/{workflow}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workflows.googleapis.com/Workflow"
    }
  ];
}

// Request for the
// [CreateWorkflow][google.cloud.workflows.v1beta.Workflows.CreateWorkflow]
// method.
message CreateWorkflowRequest {
  // Required. Project and location in which the workflow should be created.
  // Format:  projects/{project}/locations/{location}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Workflow to be created.
  Workflow workflow = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID of the workflow to be created. It has to fulfill the
  // following requirements:
  //
  // * Must contain only letters, numbers, underscores and hyphens.
  // * Must start with a letter.
  // * Must be between 1-64 characters.
  // * Must end with a number or a letter.
  // * Must be unique within the customer project and location.
  string workflow_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the
// [DeleteWorkflow][google.cloud.workflows.v1beta.Workflows.DeleteWorkflow]
// method.
message DeleteWorkflowRequest {
  // Required. Name of the workflow to be deleted.
  // Format: projects/{project}/locations/{location}/workflows/{workflow}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workflows.googleapis.com/Workflow"
    }
  ];
}

// Request for the
// [UpdateWorkflow][google.cloud.workflows.v1beta.Workflows.UpdateWorkflow]
// method.
message UpdateWorkflowRequest {
  // Required. Workflow to be updated.
  Workflow workflow = 1 [(google.api.field_behavior) = REQUIRED];

  // List of fields to be updated. If not present, the entire workflow
  // will be updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // API version used to start the operation.
  string api_version = 5;
}
