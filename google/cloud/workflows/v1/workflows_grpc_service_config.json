{"methodConfig": [{"name": [{"service": "google.cloud.workflows.v1", "method": "ListWorkflows"}, {"service": "google.cloud.workflows.v1", "method": "GetWorkflow"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.workflows.v1", "method": "CreateWorkflow"}, {"service": "google.cloud.workflows.v1", "method": "DeleteWorkflow"}, {"service": "google.cloud.workflows.v1", "method": "UpdateWorkflow"}], "timeout": "60s"}]}