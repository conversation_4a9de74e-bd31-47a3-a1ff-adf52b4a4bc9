type: google.api.Service
config_version: 3
name: workflowexecutions.googleapis.com
title: Workflow Executions API

apis:
- name: google.cloud.workflows.executions.v1beta.Executions

documentation:
  summary: 'Manages user-provided workflows.'
  overview: 'Manages user-provided workflows.'

backend:
  rules:
  - selector: 'google.cloud.workflows.executions.v1beta.Executions.*'
    deadline: 60.0

authentication:
  rules:
  - selector: 'google.cloud.workflows.executions.v1beta.Executions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
