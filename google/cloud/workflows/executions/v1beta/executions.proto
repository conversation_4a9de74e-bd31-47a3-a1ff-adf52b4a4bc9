// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.workflows.executions.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/workflows/executions/apiv1beta/executionspb;executionspb";
option java_multiple_files = true;
option java_outer_classname = "ExecutionsProto";
option java_package = "com.google.cloud.workflows.executions.v1beta";
option (google.api.resource_definition) = {
  type: "workflows.googleapis.com/Workflow"
  pattern: "projects/{project}/locations/{location}/workflows/{workflow}"
};

// Executions is used to start and manage running instances of
// [Workflows][google.cloud.workflows.v1beta.Workflow] called executions.
service Executions {
  option (google.api.default_host) = "workflowexecutions.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Returns a list of executions which belong to the workflow with
  // the given name. The method returns executions of all workflow
  // revisions. Returned executions are ordered by their start time (newest
  // first).
  rpc ListExecutions(ListExecutionsRequest) returns (ListExecutionsResponse) {
    option (google.api.http) = {
      get: "/v1beta/{parent=projects/*/locations/*/workflows/*}/executions"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new execution using the latest revision of the given workflow.
  rpc CreateExecution(CreateExecutionRequest) returns (Execution) {
    option (google.api.http) = {
      post: "/v1beta/{parent=projects/*/locations/*/workflows/*}/executions"
      body: "execution"
    };
    option (google.api.method_signature) = "parent,execution";
  }

  // Returns an execution of the given name.
  rpc GetExecution(GetExecutionRequest) returns (Execution) {
    option (google.api.http) = {
      get: "/v1beta/{name=projects/*/locations/*/workflows/*/executions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Cancels an execution of the given name.
  rpc CancelExecution(CancelExecutionRequest) returns (Execution) {
    option (google.api.http) = {
      post: "/v1beta/{name=projects/*/locations/*/workflows/*/executions/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }
}

// A running instance of a [Workflow][google.cloud.workflows.v1beta.Workflow].
message Execution {
  option (google.api.resource) = {
    type: "workflowexecutions.googleapis.com/Execution"
    pattern: "projects/{project}/locations/{location}/workflows/{workflow}/executions/{execution}"
  };

  // Error describes why the execution was abnormally terminated.
  message Error {
    // Error payload returned by the execution, represented as a JSON string.
    string payload = 1;

    // Human readable error context, helpful for debugging purposes.
    string context = 2;
  }

  // Describes the current state of the execution. More states may be added
  // in the future.
  enum State {
    // Invalid state.
    STATE_UNSPECIFIED = 0;

    // The execution is in progress.
    ACTIVE = 1;

    // The execution finished successfully.
    SUCCEEDED = 2;

    // The execution failed with an error.
    FAILED = 3;

    // The execution was stopped intentionally.
    CANCELLED = 4;
  }

  // Output only. The resource name of the execution.
  // Format:
  // projects/{project}/locations/{location}/workflows/{workflow}/executions/{execution}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Marks the beginning of execution.
  google.protobuf.Timestamp start_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Marks the end of execution, successful or not.
  google.protobuf.Timestamp end_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Current state of the execution.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input parameters of the execution represented as a JSON string.
  // The size limit is 32KB.
  string argument = 5;

  // Output only. Output of the execution represented as a JSON string. The
  // value can only be present if the execution's state is `SUCCEEDED`.
  string result = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The error which caused the execution to finish prematurely.
  // The value is only present if the execution's state is `FAILED`
  // or `CANCELLED`.
  Error error = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Revision of the workflow this execution is using.
  string workflow_revision_id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request for the
// [ListExecutions][google.cloud.workflows.executions.v1beta.Executions.ListExecutions]
// method.
message ListExecutionsRequest {
  // Required. Name of the workflow for which the executions should be listed.
  // Format: projects/{project}/locations/{location}/workflows/{workflow}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workflows.googleapis.com/Workflow"
    }
  ];

  // Maximum number of executions to return per call.
  // Max supported value depends on the selected Execution view: it's 10000 for
  // BASIC and 100 for FULL. The default value used if the field is not
  // specified is 100, regardless of the selected view. Values greater than
  // the max value will be coerced down to it.
  int32 page_size = 2;

  // A page token, received from a previous `ListExecutions` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListExecutions` must
  // match the call that provided the page token.
  string page_token = 3;

  // Optional. A view defining which fields should be filled in the returned executions.
  // The API will default to the BASIC view.
  ExecutionView view = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response for the
// [ListExecutions][google.cloud.workflows.executions.v1beta.Executions.ListExecutions]
// method.
message ListExecutionsResponse {
  // The executions which match the request.
  repeated Execution executions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request for the
// [CreateExecution][google.cloud.workflows.executions.v1beta.Executions.CreateExecution]
// method.
message CreateExecutionRequest {
  // Required. Name of the workflow for which an execution should be created.
  // Format: projects/{project}/locations/{location}/workflows/{workflow}
  // The latest revision of the workflow will be used.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workflows.googleapis.com/Workflow"
    }
  ];

  // Required. Execution to be created.
  Execution execution = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for the
// [GetExecution][google.cloud.workflows.executions.v1beta.Executions.GetExecution]
// method.
message GetExecutionRequest {
  // Required. Name of the execution to be retrieved.
  // Format:
  // projects/{project}/locations/{location}/workflows/{workflow}/executions/{execution}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workflowexecutions.googleapis.com/Execution"
    }
  ];

  // Optional. A view defining which fields should be filled in the returned execution.
  // The API will default to the FULL view.
  ExecutionView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for the
// [CancelExecution][google.cloud.workflows.executions.v1beta.Executions.CancelExecution]
// method.
message CancelExecutionRequest {
  // Required. Name of the execution to be cancelled.
  // Format:
  // projects/{project}/locations/{location}/workflows/{workflow}/executions/{execution}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "workflowexecutions.googleapis.com/Execution"
    }
  ];
}

// Defines possible views for execution resource.
enum ExecutionView {
  // The default / unset value.
  EXECUTION_VIEW_UNSPECIFIED = 0;

  // Includes only basic metadata about the execution.
  // Following fields are returned: name, start_time, end_time, state
  // and workflow_revision_id.
  BASIC = 1;

  // Includes all data.
  FULL = 2;
}
