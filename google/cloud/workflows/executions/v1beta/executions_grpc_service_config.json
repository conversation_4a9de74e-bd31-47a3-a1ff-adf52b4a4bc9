{"methodConfig": [{"name": [{"service": "google.cloud.workflows.executions.v1beta", "method": "ListExecutions"}, {"service": "google.cloud.workflows.executions.v1beta", "method": "GetExecution"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.workflows.executions.v1beta", "method": "CreateExecution"}, {"service": "google.cloud.workflows.executions.v1beta", "method": "CancelExecution"}], "timeout": "60s"}]}