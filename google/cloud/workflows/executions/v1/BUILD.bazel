# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "executions_proto",
    srcs = [
        "executions.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "executions_proto_with_info",
    deps = [
        ":executions_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "executions_java_proto",
    deps = [":executions_proto"],
)

java_grpc_library(
    name = "executions_java_grpc",
    srcs = [":executions_proto"],
    deps = [":executions_java_proto"],
)

java_gapic_library(
    name = "executions_java_gapic",
    srcs = [":executions_proto_with_info"],
    gapic_yaml = "executions_gapic.yaml",
    grpc_service_config = "executions_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "workflowexecutions_v1.yaml",
    test_deps = [
        ":executions_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":executions_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "executions_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.workflows.executions.v1.ExecutionsClientHttpJsonTest",
        "com.google.cloud.workflows.executions.v1.ExecutionsClientTest",
    ],
    runtime_deps = [":executions_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-workflows-executions-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":executions_java_gapic",
        ":executions_java_grpc",
        ":executions_java_proto",
        ":executions_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "executions_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/workflows/executions/apiv1/executionspb",
    protos = [":executions_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "executions_go_gapic",
    srcs = [":executions_proto_with_info"],
    grpc_service_config = "executions_grpc_service_config.json",
    importpath = "cloud.google.com/go/workflows/executions/apiv1;executions",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = False,
    service_yaml = "workflowexecutions_v1.yaml",
    transport = "grpc",
    deps = [
        ":executions_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-workflows-executions-v1-go",
    deps = [
        ":executions_go_gapic",
        ":executions_go_gapic_srcjar-metadata.srcjar",
        ":executions_go_gapic_srcjar-snippets.srcjar",
        ":executions_go_gapic_srcjar-test.srcjar",
        ":executions_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "executions_py_gapic",
    srcs = [":executions_proto"],
    grpc_service_config = "executions_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "workflowexecutions_v1.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "executions_py_gapic_test",
    srcs = [
        "executions_py_gapic_pytest.py",
        "executions_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":executions_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "workflows-executions-v1-py",
    deps = [
        ":executions_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "executions_php_proto",
    deps = [":executions_proto"],
)

php_gapic_library(
    name = "executions_php_gapic",
    srcs = [":executions_proto_with_info"],
    grpc_service_config = "executions_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = False,
    service_yaml = "workflowexecutions_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":executions_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-workflows-executions-v1-php",
    deps = [
        ":executions_php_gapic",
        ":executions_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "executions_nodejs_gapic",
    package_name = "@google-cloud/workflow-executions",
    src = ":executions_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "executions_grpc_service_config.json",
    package = "google.cloud.workflows.executions.v1",
    rest_numeric_enums = False,
    service_yaml = "workflowexecutions_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "workflows-executions-v1-nodejs",
    deps = [
        ":executions_nodejs_gapic",
        ":executions_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "executions_ruby_proto",
    deps = [":executions_proto"],
)

ruby_grpc_library(
    name = "executions_ruby_grpc",
    srcs = [":executions_proto"],
    deps = [":executions_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "executions_ruby_gapic",
    srcs = [":executions_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=workflowexecutions.googleapis.com",
        "ruby-cloud-api-shortname=workflowexecutions",
        "ruby-cloud-env-prefix=WORKFLOWS",
        "ruby-cloud-gem-name=google-cloud-workflows-executions-v1",
        "ruby-cloud-product-url=https://cloud.google.com/workflows/",
        "ruby-cloud-wrapper-gem-override=google-cloud-workflows",
    ],
    grpc_service_config = "executions_grpc_service_config.json",
    rest_numeric_enums = False,
    ruby_cloud_description = "Workflows link series of serverless tasks together in an order you define. Combine the power of Google Cloud's APIs, serverless products like Cloud Functions and Cloud Run, and calls to external APIs to create flexible serverless applications. Workflows requires no infrastructure management and scales seamlessly with demand, including scaling down to zero..",
    ruby_cloud_title = "Workflows Executions V1",
    service_yaml = "workflowexecutions_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":executions_ruby_grpc",
        ":executions_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-workflows-executions-v1-ruby",
    deps = [
        ":executions_ruby_gapic",
        ":executions_ruby_grpc",
        ":executions_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "executions_csharp_proto",
    extra_opts = [],
    deps = [":executions_proto"],
)

csharp_grpc_library(
    name = "executions_csharp_grpc",
    srcs = [":executions_proto"],
    deps = [":executions_csharp_proto"],
)

csharp_gapic_library(
    name = "executions_csharp_gapic",
    srcs = [":executions_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "executions_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "workflowexecutions_v1.yaml",
    deps = [
        ":executions_csharp_grpc",
        ":executions_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-workflows-executions-v1-csharp",
    deps = [
        ":executions_csharp_gapic",
        ":executions_csharp_grpc",
        ":executions_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "executions_cc_proto",
    deps = [":executions_proto"],
)

cc_grpc_library(
    name = "executions_cc_grpc",
    srcs = [":executions_proto"],
    grpc_only = True,
    deps = [":executions_cc_proto"],
)
