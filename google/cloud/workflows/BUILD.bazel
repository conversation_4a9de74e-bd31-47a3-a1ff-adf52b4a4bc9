# This build file includes a target for the Ruby wrapper library for
# google-cloud-workflows.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for workflowexecutions.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "workflows_ruby_wrapper",
    srcs = ["//google/cloud/workflows/v1:workflows_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-workflows",
        "ruby-cloud-env-prefix=WORKFLOWS",
        "ruby-cloud-wrapper-of=v1:0.7;v1beta:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/workflows/",
        "ruby-cloud-api-id=workflows.googleapis.com",
        "ruby-cloud-api-shortname=workflows",
    ],
    ruby_cloud_description = "Workflows link series of serverless tasks together in an order you define. Combine the power of Google Cloud's APIs, serverless products like Cloud Functions and Cloud Run, and calls to external APIs to create flexible serverless applications. Workflows requires no infrastructure management and scales seamlessly with demand, including scaling down to zero.",
    ruby_cloud_title = "Workflows",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-workflows-ruby",
    deps = [
        ":workflows_ruby_wrapper",
    ],
)
