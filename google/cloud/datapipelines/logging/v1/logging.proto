// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datapipelines.logging.v1;

import "google/rpc/status.proto";

option go_package = "cloud.google.com/go/datapipelines/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggingProto";
option java_package = "com.google.cloud.datapipelines.logging.v1";

// Cloud Logging structured payload for events generated from Data Pipelines API
// requests.
message RequestLogEntry {
  // Type of a Data Pipelines API request.
  enum RequestType {
    // Default value. This value is not used.
    REQUEST_TYPE_UNSPECIFIED = 0;

    // A Data Pipelines Create Pipeline request.
    CREATE_PIPELINE = 1;

    // A Data Pipelines Update Pipeline request.
    UPDATE_PIPELINE = 2;

    // A Data Pipelines Delete Pipeline request.
    DELETE_PIPELINE = 3;

    // A Data Pipelines List Pipelines request.
    LIST_PIPELINES = 4;

    // A Data Pipelines Get Pipeline request.
    GET_PIPELINE = 5;

    // A Data Pipelines Stop Pipeline request.
    STOP_PIPELINE = 6;

    // A Data Pipelines Run Pipeline request.
    RUN_PIPELINE = 7;

    // A Data Pipelines List Jobs request.
    LIST_JOBS = 8;
  }

  // Cause code for a Data Pipelines API request error.
  enum ErrorCause {
    // Default value. This value is not used.
    ERROR_CAUSE_UNSPECIFIED = 0;

    // The request is invalid.
    INVALID_REQUEST = 1;

    // Failed to fetch project number for the provided project id.
    PROJECT_NUMBER_NOT_FOUND = 2;

    // The given pipeline already exists.
    PIPELINE_ID_ALREADY_EXISTS = 3;

    // Failed to allocate a token for the per project pipeline count quota.
    PIPELINE_QUOTA_ALLOCATION_FAILED = 4;

    // The given pipeline is not found.
    PIPELINE_NOT_FOUND = 5;

    // The pipeline's workload is invalid.
    INVALID_PIPELINE_WORKLOAD = 6;

    // The user cannot act as the Dataflow worker service account.
    DATAFLOW_WORKER_SERVICE_ACCOUNT_PERMISSION_DENIED = 7;

    // The user cannot act as the Cloud Scheduler service account.
    CLOUD_SCHEDULER_SERVICE_ACCOUNT_PERMISSION_DENIED = 8;

    // Issues related to the per service per project service account.
    INTERNAL_DATA_PIPELINES_SERVICE_ACCOUNT_ISSUE = 9;

    // Invalid argument in Cloud Scheduler service call.
    CLOUD_SCHEDULER_INVALID_ARGUMENT = 10;

    // Exceeds Cloud Scheduler service quota limit.
    CLOUD_SCHEDULER_RESOURCE_EXHAUSTED = 11;

    // Cloud Scheduler job not found.
    CLOUD_SCHEDULER_JOB_NOT_FOUND = 12;

    // Other Cloud Scheduler related issues.
    OTHER_CLOUD_SCHEDULER_ISSUE = 13;

    // Dataflow job with the same name already exists.
    DATAFLOW_JOB_ALREADY_EXISTS = 14;

    // Invalid argument in Dataflow service call.
    DATAFLOW_INVALID_ARGUMENT = 15;

    // Exceeds Dataflow service quota limit.
    DATAFLOW_RESOURCE_EXHAUSTED = 16;

    // Dataflow job not found.
    DATAFLOW_JOB_NOT_FOUND = 17;

    // Other Dataflow related issues.
    OTHER_DATAFLOW_ISSUE = 18;

    // Database related issues.
    DATABASE_ERROR = 19;

    // Request with the wrong pipeline type. For example, RunPipeline cannot be
    // used with a streaming pipeline.
    WRONG_PIPELINE_TYPE = 20;

    // Issues related to other Google internal services/systems.
    INTERNAL_ERROR = 21;

    // Cannot find the given pipeline or project.
    PIPELINE_OR_PROJECT_NOT_FOUND = 22;
  }

  // Type of the Data Pipelines API request.
  RequestType request_type = 1;

  // The resulting status of the Data Pipelines API request.
  google.rpc.Status status = 2;

  // Cause of the error status.
  ErrorCause error_cause = 3;
}
