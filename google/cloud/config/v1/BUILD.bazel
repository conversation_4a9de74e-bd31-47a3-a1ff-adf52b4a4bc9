# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "config_proto",
    srcs = [
        "config.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "config_proto_with_info",
    deps = [
        ":config_proto",
        "//google/cloud/location:location_proto",
        "//google/cloud:common_resources_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "config_java_proto",
    deps = [":config_proto"],
)

java_grpc_library(
    name = "config_java_grpc",
    srcs = [":config_proto"],
    deps = [":config_java_proto"],
)

java_gapic_library(
    name = "config_java_gapic",
    srcs = [":config_proto_with_info"],
    gapic_yaml = "config_gapic.yaml",
    grpc_service_config = "config_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "config_v1.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
        ":config_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":config_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "config_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.config.v1.ConfigClientHttpJsonTest",
        "com.google.cloud.config.v1.ConfigClientTest",
    ],
    runtime_deps = [":config_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-config-v1-java",
    transport = "grpc+rest",
    deps = [
        ":config_java_gapic",
        ":config_java_grpc",
        ":config_java_proto",
        ":config_proto",
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "config_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/config/apiv1/configpb",
    protos = [":config_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "config_go_gapic",
    srcs = [":config_proto_with_info"],
    grpc_service_config = "config_grpc_service_config.json",
    importpath = "cloud.google.com/go/config/apiv1;config",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "config_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":config_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-config-v1-go",
    deps = [
        ":config_go_gapic",
        ":config_go_gapic_srcjar-test.srcjar",
        ":config_go_gapic_srcjar-metadata.srcjar",
        ":config_go_gapic_srcjar-snippets.srcjar",
        ":config_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "config_py_gapic",
    srcs = [":config_proto"],
    grpc_service_config = "config_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "config_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "config_py_gapic_test",
    srcs = [
        "config_py_gapic_pytest.py",
        "config_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":config_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "config-v1-py",
    deps = [
        ":config_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "config_php_proto",
    deps = [":config_proto"],
)

php_gapic_library(
    name = "config_php_gapic",
    srcs = [":config_proto_with_info"],
    gapic_yaml = "config_gapic.yaml",
    grpc_service_config = "config_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "config_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":config_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-config-v1-php",
    deps = [
        ":config_php_gapic",
        ":config_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "config_nodejs_gapic",
    package_name = "@google-cloud/config",
    src = ":config_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "config_grpc_service_config.json",
    package = "google.cloud.config.v1",
    rest_numeric_enums = True,
    service_yaml = "config_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "config-v1-nodejs",
    deps = [
        ":config_nodejs_gapic",
        ":config_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "config_ruby_proto",
    deps = [":config_proto"],
)

ruby_grpc_library(
    name = "config_ruby_grpc",
    srcs = [":config_proto"],
    deps = [":config_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "config_ruby_gapic",
    srcs = [":config_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-config_service-v1",
    ],
    grpc_service_config = "config_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "config_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":config_ruby_grpc",
        ":config_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-config-v1-ruby",
    deps = [
        ":config_ruby_gapic",
        ":config_ruby_grpc",
        ":config_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "config_csharp_proto",
    extra_opts = [],
    deps = [":config_proto"],
)

csharp_grpc_library(
    name = "config_csharp_grpc",
    srcs = [":config_proto"],
    deps = [":config_csharp_proto"],
)

csharp_gapic_library(
    name = "config_csharp_gapic",
    srcs = [":config_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "config_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "config_v1.yaml",
    deps = [
        ":config_csharp_grpc",
        ":config_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-config-v1-csharp",
    deps = [
        ":config_csharp_gapic",
        ":config_csharp_grpc",
        ":config_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "config_cc_proto",
    deps = [":config_proto"],
)

cc_grpc_library(
    name = "config_cc_grpc",
    srcs = [":config_proto"],
    grpc_only = True,
    deps = [":config_cc_proto"],
)
