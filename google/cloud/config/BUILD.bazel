# This build file includes a target for the Ruby wrapper library for
# google-cloud-config_service.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for config.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "config_ruby_wrapper",
    srcs = ["//google/cloud/config/v1:config_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-config_service",
        "ruby-cloud-wrapper-of=v1:0.2",
    ],
    service_yaml = "//google/cloud/config/v1:config_v1.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-config-ruby",
    deps = [
        ":config_ruby_wrapper",
    ],
)
