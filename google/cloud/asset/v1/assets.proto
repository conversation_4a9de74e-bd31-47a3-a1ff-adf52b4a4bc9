// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.asset.v1;

import "google/api/resource.proto";
import "google/cloud/orgpolicy/v1/orgpolicy.proto";
import "google/cloud/osconfig/v1/inventory.proto";
import "google/iam/v1/policy.proto";
import "google/identity/accesscontextmanager/v1/access_level.proto";
import "google/identity/accesscontextmanager/v1/access_policy.proto";
import "google/identity/accesscontextmanager/v1/service_perimeter.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/code.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Asset.V1";
option go_package = "cloud.google.com/go/asset/apiv1/assetpb;assetpb";
option java_multiple_files = true;
option java_outer_classname = "AssetProto";
option java_package = "com.google.cloud.asset.v1";
option php_namespace = "Google\\Cloud\\Asset\\V1";

// An asset in Google Cloud and its temporal metadata, including the time window
// when it was observed and its status during that window.
message TemporalAsset {
  // State of prior asset.
  enum PriorAssetState {
    // prior_asset is not applicable for the current asset.
    PRIOR_ASSET_STATE_UNSPECIFIED = 0;

    // prior_asset is populated correctly.
    PRESENT = 1;

    // Failed to set prior_asset.
    INVALID = 2;

    // Current asset is the first known state.
    DOES_NOT_EXIST = 3;

    // prior_asset is a deletion.
    DELETED = 4;
  }

  // The time window when the asset data and state was observed.
  TimeWindow window = 1;

  // Whether the asset has been deleted or not.
  bool deleted = 2;

  // An asset in Google Cloud.
  Asset asset = 3;

  // State of prior_asset.
  PriorAssetState prior_asset_state = 4;

  // Prior copy of the asset. Populated if prior_asset_state is PRESENT.
  // Currently this is only set for responses in Real-Time Feed.
  Asset prior_asset = 5;
}

// A time window specified by its `start_time` and `end_time`.
message TimeWindow {
  // Start time of the time window (exclusive).
  google.protobuf.Timestamp start_time = 1;

  // End time of the time window (inclusive). If not specified, the current
  // timestamp is used instead.
  google.protobuf.Timestamp end_time = 2;
}

// An asset in Google Cloud. An asset can be any resource in the Google Cloud
// [resource
// hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy),
// a resource outside the Google Cloud resource hierarchy (such as Google
// Kubernetes Engine clusters and objects), or a policy (e.g. IAM policy),
// or a relationship (e.g. an INSTANCE_TO_INSTANCEGROUP relationship).
// See [Supported asset
// types](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
// for more information.
message Asset {
  option (google.api.resource) = {
    type: "cloudasset.googleapis.com/Asset"
    pattern: "*"
  };

  // The last update timestamp of an asset. update_time is updated when
  // create/update/delete operation is performed.
  google.protobuf.Timestamp update_time = 11;

  // The full name of the asset. Example:
  // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`
  //
  // See [Resource
  // names](https://cloud.google.com/apis/design/resource_names#full_resource_name)
  // for more information.
  string name = 1;

  // The type of the asset. Example: `compute.googleapis.com/Disk`
  //
  // See [Supported asset
  // types](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
  // for more information.
  string asset_type = 2;

  // A representation of the resource.
  Resource resource = 3;

  // A representation of the IAM policy set on a Google Cloud resource.
  // There can be a maximum of one IAM policy set on any given resource.
  // In addition, IAM policies inherit their granted access scope from any
  // policies set on parent resources in the resource hierarchy. Therefore, the
  // effectively policy is the union of both the policy set on this resource
  // and each policy set on all of the resource's ancestry resource levels in
  // the hierarchy. See
  // [this topic](https://cloud.google.com/iam/help/allow-policies/inheritance)
  // for more information.
  google.iam.v1.Policy iam_policy = 4;

  // A representation of an [organization
  // policy](https://cloud.google.com/resource-manager/docs/organization-policy/overview#organization_policy).
  // There can be more than one organization policy with different constraints
  // set on a given resource.
  repeated google.cloud.orgpolicy.v1.Policy org_policy = 6;

  // A representation of an [access
  // policy](https://cloud.google.com/access-context-manager/docs/overview#access-policies).
  oneof access_context_policy {
    // Also refer to the [access policy user
    // guide](https://cloud.google.com/access-context-manager/docs/overview#access-policies).
    google.identity.accesscontextmanager.v1.AccessPolicy access_policy = 7;

    // Also refer to the [access level user
    // guide](https://cloud.google.com/access-context-manager/docs/overview#access-levels).
    google.identity.accesscontextmanager.v1.AccessLevel access_level = 8;

    // Also refer to the [service perimeter user
    // guide](https://cloud.google.com/vpc-service-controls/docs/overview).
    google.identity.accesscontextmanager.v1.ServicePerimeter service_perimeter =
        9;
  }

  // A representation of runtime OS Inventory information. See [this
  // topic](https://cloud.google.com/compute/docs/instances/os-inventory-management)
  // for more information.
  google.cloud.osconfig.v1.Inventory os_inventory = 12;

  // DEPRECATED. This field only presents for the purpose of
  // backward-compatibility. The server will never generate responses with this
  // field.
  // The related assets of the asset of one relationship type. One asset
  // only represents one type of relationship.
  RelatedAssets related_assets = 13 [deprecated = true];

  // One related asset of the current asset.
  RelatedAsset related_asset = 15;

  // The ancestry path of an asset in Google Cloud [resource
  // hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy),
  // represented as a list of relative resource names. An ancestry path starts
  // with the closest ancestor in the hierarchy and ends at root. If the asset
  // is a project, folder, or organization, the ancestry path starts from the
  // asset itself.
  //
  // Example: `["projects/123456789", "folders/5432", "organizations/1234"]`
  repeated string ancestors = 10;
}

// A representation of a Google Cloud resource.
message Resource {
  // The API version. Example: `v1`
  string version = 1;

  // The URL of the discovery document containing the resource's JSON schema.
  // Example:
  // `https://www.googleapis.com/discovery/v1/apis/compute/v1/rest`
  //
  // This value is unspecified for resources that do not have an API based on a
  // discovery document, such as Cloud Bigtable.
  string discovery_document_uri = 2;

  // The JSON schema name listed in the discovery document. Example:
  // `Project`
  //
  // This value is unspecified for resources that do not have an API based on a
  // discovery document, such as Cloud Bigtable.
  string discovery_name = 3;

  // The REST URL for accessing the resource. An HTTP `GET` request using this
  // URL returns the resource itself. Example:
  // `https://cloudresourcemanager.googleapis.com/v1/projects/my-project-123`
  //
  // This value is unspecified for resources without a REST API.
  string resource_url = 4;

  // The full name of the immediate parent of this resource. See
  // [Resource
  // Names](https://cloud.google.com/apis/design/resource_names#full_resource_name)
  // for more information.
  //
  // For Google Cloud assets, this value is the parent resource defined in the
  // [IAM policy
  // hierarchy](https://cloud.google.com/iam/docs/overview#policy_hierarchy).
  // Example:
  // `//cloudresourcemanager.googleapis.com/projects/my_project_123`
  string parent = 5;

  // The content of the resource, in which some sensitive fields are removed
  // and may not be present.
  google.protobuf.Struct data = 6;

  // The location of the resource in Google Cloud, such as its zone and region.
  // For more information, see https://cloud.google.com/about/locations/.
  string location = 8;
}

// DEPRECATED. This message only presents for the purpose of
// backward-compatibility. The server will never populate this message in
// responses.
// The detailed related assets with the `relationship_type`.
message RelatedAssets {
  option deprecated = true;

  // The detailed relationship attributes.
  RelationshipAttributes relationship_attributes = 1;

  // The peer resources of the relationship.
  repeated RelatedAsset assets = 2;
}

// DEPRECATED. This message only presents for the purpose of
// backward-compatibility. The server will never populate this message in
// responses.
// The relationship attributes which include  `type`, `source_resource_type`,
// `target_resource_type` and `action`.
message RelationshipAttributes {
  option deprecated = true;

  // The unique identifier of the relationship type. Example:
  // `INSTANCE_TO_INSTANCEGROUP`
  string type = 4;

  // The source asset type. Example: `compute.googleapis.com/Instance`
  string source_resource_type = 1;

  // The target asset type. Example: `compute.googleapis.com/Disk`
  string target_resource_type = 2;

  // The detail of the relationship, e.g. `contains`, `attaches`
  string action = 3;
}

// An asset identifier in Google Cloud which contains its name, type and
// ancestors. An asset can be any resource in the Google Cloud [resource
// hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy),
// a resource outside the Google Cloud resource hierarchy (such as Google
// Kubernetes Engine clusters and objects), or a policy (e.g. IAM policy).
// See [Supported asset
// types](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
// for more information.
message RelatedAsset {
  // The full name of the asset. Example:
  // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`
  //
  // See [Resource
  // names](https://cloud.google.com/apis/design/resource_names#full_resource_name)
  // for more information.
  string asset = 1 [(google.api.resource_reference) = {
    type: "cloudasset.googleapis.com/Asset"
  }];

  // The type of the asset. Example: `compute.googleapis.com/Disk`
  //
  // See [Supported asset
  // types](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
  // for more information.
  string asset_type = 2;

  // The ancestors of an asset in Google Cloud [resource
  // hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy),
  // represented as a list of relative resource names. An ancestry path starts
  // with the closest ancestor in the hierarchy and ends at root.
  //
  // Example: `["projects/123456789", "folders/5432", "organizations/1234"]`
  repeated string ancestors = 3;

  // The unique identifier of the relationship type. Example:
  // `INSTANCE_TO_INSTANCEGROUP`
  string relationship_type = 4;
}

// The key and value for a
// [tag](https://cloud.google.com/resource-manager/docs/tags/tags-overview).
message Tag {
  // TagKey namespaced name, in the format of {ORG_ID}/{TAG_KEY_SHORT_NAME}.
  optional string tag_key = 1;

  // TagKey ID, in the format of tagKeys/{TAG_KEY_ID}.
  optional string tag_key_id = 2;

  // TagValue namespaced name, in the format of
  // {ORG_ID}/{TAG_KEY_SHORT_NAME}/{TAG_VALUE_SHORT_NAME}.
  optional string tag_value = 3;

  // TagValue ID, in the format of tagValues/{TAG_VALUE_ID}.
  optional string tag_value_id = 4;
}

// The effective tags and the ancestor resources from which they were inherited.
message EffectiveTagDetails {
  // The [full resource
  // name](https://cloud.google.com/asset-inventory/docs/resource-name-format)
  // of the ancestor from which an [effective_tag][] is inherited, according to
  // [tag
  // inheritance](https://cloud.google.com/resource-manager/docs/tags/tags-overview#inheritance).
  optional string attached_resource = 1;

  // The effective tags inherited from the
  // [attached_resource][google.cloud.asset.v1.EffectiveTagDetails.attached_resource].
  // Note that tags with the same key but different values may attach to
  // resources at a different hierarchy levels. The lower hierarchy tag value
  // will overwrite the higher hierarchy tag value of the same tag key. In this
  // case, the tag value at the higher hierarchy level will be removed. For more
  // information, see [tag
  // inheritance](https://cloud.google.com/resource-manager/docs/tags/tags-overview#inheritance).
  repeated Tag effective_tags = 2;
}

// A result of Resource Search, containing information of a cloud resource.
message ResourceSearchResult {
  // The full resource name of this resource. Example:
  // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`.
  // See [Cloud Asset Inventory Resource Name
  // Format](https://cloud.google.com/asset-inventory/docs/resource-name-format)
  // for more information.
  //
  // To search against the `name`:
  //
  // * Use a field query. Example: `name:instance1`
  // * Use a free text query. Example: `instance1`
  string name = 1;

  // The type of this resource. Example: `compute.googleapis.com/Disk`.
  //
  // To search against the `asset_type`:
  //
  // * Specify the `asset_type` field in your search request.
  string asset_type = 2;

  // The project that this resource belongs to, in the form of
  // projects/{PROJECT_NUMBER}. This field is available when the resource
  // belongs to a project.
  //
  // To search against `project`:
  //
  // * Use a field query. Example: `project:12345`
  // * Use a free text query. Example: `12345`
  // * Specify the `scope` field as this project in your search request.
  string project = 3;

  // The folder(s) that this resource belongs to, in the form of
  // folders/{FOLDER_NUMBER}. This field is available when the resource
  // belongs to one or more folders.
  //
  // To search against `folders`:
  //
  // * Use a field query. Example: `folders:(123 OR 456)`
  // * Use a free text query. Example: `123`
  // * Specify the `scope` field as this folder in your search request.
  repeated string folders = 17;

  // The organization that this resource belongs to, in the form of
  // organizations/{ORGANIZATION_NUMBER}. This field is available when the
  // resource belongs to an organization.
  //
  // To search against `organization`:
  //
  // * Use a field query. Example: `organization:123`
  // * Use a free text query. Example: `123`
  // * Specify the `scope` field as this organization in your search request.
  string organization = 18;

  // The display name of this resource. This field is available only when the
  // resource's Protobuf contains it.
  //
  // To search against the `display_name`:
  //
  // * Use a field query. Example: `displayName:"My Instance"`
  // * Use a free text query. Example: `"My Instance"`
  string display_name = 4;

  // One or more paragraphs of text description of this resource. Maximum length
  // could be up to 1M bytes. This field is available only when the resource's
  // Protobuf contains it.
  //
  // To search against the `description`:
  //
  // * Use a field query. Example: `description:"important instance"`
  // * Use a free text query. Example: `"important instance"`
  string description = 5;

  // Location can be `global`, regional like `us-east1`, or zonal like
  // `us-west1-b`. This field is available only when the resource's Protobuf
  // contains it.
  //
  // To search against the `location`:
  //
  // * Use a field query. Example: `location:us-west*`
  // * Use a free text query. Example: `us-west*`
  string location = 6;

  // User labels associated with this resource. See [Labelling and grouping
  // Google Cloud
  // resources](https://cloud.google.com/blog/products/gcp/labelling-and-grouping-your-google-cloud-platform-resources)
  // for more information. This field is available only when the resource's
  // Protobuf contains it.
  //
  // To search against the `labels`:
  //
  // * Use a field query:
  //     - query on any label's key or value. Example: `labels:prod`
  //     - query by a given label. Example: `labels.env:prod`
  //     - query by a given label's existence. Example: `labels.env:*`
  // * Use a free text query. Example: `prod`
  map<string, string> labels = 7;

  // Network tags associated with this resource. Like labels, network tags are a
  // type of annotations used to group Google Cloud resources. See [Labelling
  // Google Cloud
  // resources](https://cloud.google.com/blog/products/gcp/labelling-and-grouping-your-google-cloud-platform-resources)
  // for more information. This field is available only when the resource's
  // Protobuf contains it.
  //
  // To search against the `network_tags`:
  //
  // * Use a field query. Example: `networkTags:internal`
  // * Use a free text query. Example: `internal`
  repeated string network_tags = 8;

  // The Cloud KMS
  // [CryptoKey](https://cloud.google.com/kms/docs/reference/rest/v1/projects.locations.keyRings.cryptoKeys)
  // name or
  // [CryptoKeyVersion](https://cloud.google.com/kms/docs/reference/rest/v1/projects.locations.keyRings.cryptoKeys.cryptoKeyVersions)
  // name.
  //
  // This field only presents for the purpose of backward compatibility.
  // Use the `kms_keys` field to retrieve Cloud KMS key information. This field
  // is available only when the resource's Protobuf contains it and will only be
  // populated for [these resource
  // types](https://cloud.google.com/asset-inventory/docs/legacy-field-names#resource_types_with_the_to_be_deprecated_kmskey_field)
  // for backward compatible purposes.
  //
  // To search against the `kms_key`:
  //
  // * Use a field query. Example: `kmsKey:key`
  // * Use a free text query. Example: `key`
  string kms_key = 10 [deprecated = true];

  // The Cloud KMS
  // [CryptoKey](https://cloud.google.com/kms/docs/reference/rest/v1/projects.locations.keyRings.cryptoKeys)
  // names or
  // [CryptoKeyVersion](https://cloud.google.com/kms/docs/reference/rest/v1/projects.locations.keyRings.cryptoKeys.cryptoKeyVersions)
  // names. This field is available only when the resource's Protobuf contains
  // it.
  //
  // To search against the `kms_keys`:
  //
  // * Use a field query. Example: `kmsKeys:key`
  // * Use a free text query. Example: `key`
  repeated string kms_keys = 28;

  // The create timestamp of this resource, at which the resource was created.
  // The granularity is in seconds. Timestamp.nanos will always be 0. This field
  // is available only when the resource's Protobuf contains it.
  //
  // To search against `create_time`:
  //
  // * Use a field query.
  //     - value in seconds since unix epoch. Example: `createTime > 1609459200`
  //     - value in date string. Example: `createTime > 2021-01-01`
  //     - value in date-time string (must be quoted). Example: `createTime >
  //     "2021-01-01T00:00:00"`
  google.protobuf.Timestamp create_time = 11;

  // The last update timestamp of this resource, at which the resource was last
  // modified or deleted. The granularity is in seconds. Timestamp.nanos will
  // always be 0. This field is available only when the resource's Protobuf
  // contains it.
  //
  // To search against `update_time`:
  //
  // * Use a field query.
  //     - value in seconds since unix epoch. Example: `updateTime < 1609459200`
  //     - value in date string. Example: `updateTime < 2021-01-01`
  //     - value in date-time string (must be quoted). Example: `updateTime <
  //     "2021-01-01T00:00:00"`
  google.protobuf.Timestamp update_time = 12;

  // The state of this resource. Different resources types have different state
  // definitions that are mapped from various fields of different resource
  // types. This field is available only when the resource's Protobuf contains
  // it.
  //
  // Example:
  // If the resource is an instance provided by Compute Engine,
  // its state will include PROVISIONING, STAGING, RUNNING, STOPPING,
  // SUSPENDING, SUSPENDED, REPAIRING, and TERMINATED. See `status` definition
  // in [API
  // Reference](https://cloud.google.com/compute/docs/reference/rest/v1/instances).
  // If the resource is a project provided by Resource Manager, its state
  // will include LIFECYCLE_STATE_UNSPECIFIED, ACTIVE, DELETE_REQUESTED and
  // DELETE_IN_PROGRESS. See `lifecycleState` definition in [API
  // Reference](https://cloud.google.com/resource-manager/reference/rest/v1/projects).
  //
  // To search against the `state`:
  //
  // * Use a field query. Example: `state:RUNNING`
  // * Use a free text query. Example: `RUNNING`
  string state = 13;

  // The additional searchable attributes of this resource. The attributes may
  // vary from one resource type to another. Examples: `projectId` for Project,
  // `dnsName` for DNS ManagedZone. This field contains a subset of the resource
  // metadata fields that are returned by the List or Get APIs provided by the
  // corresponding Google Cloud service (e.g., Compute Engine). see [API
  // references and supported searchable
  // attributes](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
  // to see which fields are included.
  //
  // You can search values of these fields through free text search. However,
  // you should not consume the field programically as the field names and
  // values may change as the Google Cloud service updates to a new incompatible
  // API version.
  //
  // To search against the `additional_attributes`:
  //
  // * Use a free text query to match the attributes values. Example: to search
  //   `additional_attributes = { dnsName: "foobar" }`, you can issue a query
  //   `foobar`.
  google.protobuf.Struct additional_attributes = 9;

  // The full resource name of this resource's parent, if it has one.
  // To search against the `parent_full_resource_name`:
  //
  // * Use a field query. Example:
  // `parentFullResourceName:"project-name"`
  // * Use a free text query. Example:
  // `project-name`
  string parent_full_resource_name = 19;

  // Versioned resource representations of this resource. This is repeated
  // because there could be multiple versions of resource representations during
  // version migration.
  //
  // This `versioned_resources` field is not searchable. Some attributes of the
  // resource representations are exposed in `additional_attributes` field, so
  // as to allow users to search on them.
  repeated VersionedResource versioned_resources = 16;

  // Attached resources of this resource. For example, an OSConfig
  // Inventory is an attached resource of a Compute Instance. This field is
  // repeated because a resource could have multiple attached resources.
  //
  // This `attached_resources` field is not searchable. Some attributes
  // of the attached resources are exposed in `additional_attributes` field, so
  // as to allow users to search on them.
  repeated AttachedResource attached_resources = 20;

  // A map of related resources of this resource, keyed by the
  // relationship type. A relationship type is in the format of
  // {SourceType}_{ACTION}_{DestType}. Example: `DISK_TO_INSTANCE`,
  // `DISK_TO_NETWORK`, `INSTANCE_TO_INSTANCEGROUP`.
  // See [supported relationship
  // types](https://cloud.google.com/asset-inventory/docs/supported-asset-types#supported_relationship_types).
  map<string, RelatedResources> relationships = 21;

  // This field is only present for the purpose of backward compatibility.
  // Use the `tags` field instead.
  //
  // TagKey namespaced names, in the format of {ORG_ID}/{TAG_KEY_SHORT_NAME}.
  // To search against the `tagKeys`:
  //
  // * Use a field query. Example:
  //     - `tagKeys:"123456789/env*"`
  //     - `tagKeys="123456789/env"`
  //     - `tagKeys:"env"`
  //
  // * Use a free text query. Example:
  //     - `env`
  repeated string tag_keys = 23 [deprecated = true];

  // This field is only present for the purpose of backward compatibility.
  // Use the `tags` field instead.
  //
  // TagValue namespaced names, in the format of
  // {ORG_ID}/{TAG_KEY_SHORT_NAME}/{TAG_VALUE_SHORT_NAME}.
  // To search against the `tagValues`:
  //
  // * Use a field query. Example:
  //     - `tagValues:"env"`
  //     - `tagValues:"env/prod"`
  //     - `tagValues:"123456789/env/prod*"`
  //     - `tagValues="123456789/env/prod"`
  //
  // * Use a free text query. Example:
  //     - `prod`
  repeated string tag_values = 25 [deprecated = true];

  // This field is only present for the purpose of backward compatibility.
  // Use the `tags` field instead.
  //
  // TagValue IDs, in the format of tagValues/{TAG_VALUE_ID}.
  // To search against the `tagValueIds`:
  //
  // * Use a field query. Example:
  //     - `tagValueIds="tagValues/456"`
  //
  // * Use a free text query. Example:
  //     - `456`
  repeated string tag_value_ids = 26 [deprecated = true];

  // The tags directly attached to this resource.
  //
  // To search against the `tags`:
  //
  // * Use a field query. Example:
  //     - `tagKeys:"123456789/env*"`
  //     - `tagKeys="123456789/env"`
  //     - `tagKeys:"env"`
  //     - `tagKeyIds="tagKeys/123"`
  //     - `tagValues:"env"`
  //     - `tagValues:"env/prod"`
  //     - `tagValues:"123456789/env/prod*"`
  //     - `tagValues="123456789/env/prod"`
  //     - `tagValueIds="tagValues/456"`
  //
  // * Use a free text query. Example:
  //     - `env/prod`
  repeated Tag tags = 29;

  // The effective tags on this resource. All of the tags that are both attached
  // to and inherited by a resource are collectively called the effective
  // tags. For more information, see [tag
  // inheritance](https://cloud.google.com/resource-manager/docs/tags/tags-overview#inheritance).
  //
  // To search against the `effective_tags`:
  //
  // * Use a field query. Example:
  //     - `effectiveTagKeys:"123456789/env*"`
  //     - `effectiveTagKeys="123456789/env"`
  //     - `effectiveTagKeys:"env"`
  //     - `effectiveTagKeyIds="tagKeys/123"`
  //     - `effectiveTagValues:"env"`
  //     - `effectiveTagValues:"env/prod"`
  //     - `effectiveTagValues:"123456789/env/prod*"`
  //     - `effectiveTagValues="123456789/env/prod"`
  //     - `effectiveTagValueIds="tagValues/456"`
  repeated EffectiveTagDetails effective_tags = 30;

  // The type of this resource's immediate parent, if there is one.
  //
  // To search against the `parent_asset_type`:
  //
  // * Use a field query. Example:
  // `parentAssetType:"cloudresourcemanager.googleapis.com/Project"`
  // * Use a free text query. Example:
  // `cloudresourcemanager.googleapis.com/Project`
  string parent_asset_type = 103;

  // The actual content of Security Command Center security marks associated
  // with the asset.
  //
  //
  // To search against SCC SecurityMarks field:
  //
  //   * Use a field query:
  //     - query by a given key value pair. Example: `sccSecurityMarks.foo=bar`
  //     - query by a given key's existence. Example: `sccSecurityMarks.foo:*`
  map<string, string> scc_security_marks = 32;
}

// Resource representation as defined by the corresponding service providing the
// resource for a given API version.
message VersionedResource {
  // API version of the resource.
  //
  // Example:
  // If the resource is an instance provided by Compute Engine v1 API as defined
  // in `https://cloud.google.com/compute/docs/reference/rest/v1/instances`,
  // version will be "v1".
  string version = 1;

  // JSON representation of the resource as defined by the corresponding
  // service providing this resource.
  //
  // Example:
  // If the resource is an instance provided by Compute Engine, this field will
  // contain the JSON representation of the instance as defined by Compute
  // Engine:
  // `https://cloud.google.com/compute/docs/reference/rest/v1/instances`.
  //
  // You can find the resource definition for each supported resource type in
  // this table:
  // `https://cloud.google.com/asset-inventory/docs/supported-asset-types`
  google.protobuf.Struct resource = 2;
}

// Attached resource representation, which is defined by the corresponding
// service provider. It represents an attached resource's payload.
message AttachedResource {
  // The type of this attached resource.
  //
  // Example: `osconfig.googleapis.com/Inventory`
  //
  // You can find the supported attached asset types of each resource in this
  // table:
  // `https://cloud.google.com/asset-inventory/docs/supported-asset-types`
  string asset_type = 1;

  // Versioned resource representations of this attached resource. This is
  // repeated because there could be multiple versions of the attached resource
  // representations during version migration.
  repeated VersionedResource versioned_resources = 3;
}

// The related resources of the primary resource.
message RelatedResources {
  // The detailed related resources of the primary resource.
  repeated RelatedResource related_resources = 1;
}

// The detailed related resource.
message RelatedResource {
  // The type of the asset. Example: `compute.googleapis.com/Instance`
  string asset_type = 1;

  // The full resource name of the related resource. Example:
  // `//compute.googleapis.com/projects/my_proj_123/zones/instance/instance123`
  string full_resource_name = 2;
}

// A result of IAM Policy search, containing information of an IAM policy.
message IamPolicySearchResult {
  // Explanation about the IAM policy search result.
  message Explanation {
    // IAM permissions
    message Permissions {
      // A list of permissions. A sample permission string: `compute.disk.get`.
      repeated string permissions = 1;
    }

    // The map from roles to their included permissions that match the
    // permission query (i.e., a query containing `policy.role.permissions:`).
    // Example: if query `policy.role.permissions:compute.disk.get`
    // matches a policy binding that contains owner role, the
    // matched_permissions will be `{"roles/owner": ["compute.disk.get"]}`. The
    // roles can also be found in the returned `policy` bindings. Note that the
    // map is populated only for requests with permission queries.
    map<string, Permissions> matched_permissions = 1;
  }

  // The full resource name of the resource associated with this IAM policy.
  // Example:
  // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`.
  // See [Cloud Asset Inventory Resource Name
  // Format](https://cloud.google.com/asset-inventory/docs/resource-name-format)
  // for more information.
  //
  // To search against the `resource`:
  //
  // * use a field query. Example: `resource:organizations/123`
  string resource = 1;

  // The type of the resource associated with this IAM policy. Example:
  // `compute.googleapis.com/Disk`.
  //
  // To search against the `asset_type`:
  //
  // * specify the `asset_types` field in your search request.
  string asset_type = 5;

  // The project that the associated Google Cloud resource belongs to, in the
  // form of projects/{PROJECT_NUMBER}. If an IAM policy is set on a resource
  // (like VM instance, Cloud Storage bucket), the project field will indicate
  // the project that contains the resource. If an IAM policy is set on a folder
  // or orgnization, this field will be empty.
  //
  // To search against the `project`:
  //
  // * specify the `scope` field as this project in your search request.
  string project = 2;

  // The folder(s) that the IAM policy belongs to, in the form of
  // folders/{FOLDER_NUMBER}. This field is available when the IAM policy
  // belongs to one or more folders.
  //
  // To search against `folders`:
  //
  // * use a field query. Example: `folders:(123 OR 456)`
  // * use a free text query. Example: `123`
  // * specify the `scope` field as this folder in your search request.
  repeated string folders = 6;

  // The organization that the IAM policy belongs to, in the form
  // of organizations/{ORGANIZATION_NUMBER}. This field is available when the
  // IAM policy belongs to an organization.
  //
  // To search against `organization`:
  //
  // * use a field query. Example: `organization:123`
  // * use a free text query. Example: `123`
  // * specify the `scope` field as this organization in your search request.
  string organization = 7;

  // The IAM policy directly set on the given resource. Note that the original
  // IAM policy can contain multiple bindings. This only contains the bindings
  // that match the given query. For queries that don't contain a constrain on
  // policies (e.g., an empty query), this contains all the bindings.
  //
  // To search against the `policy` bindings:
  //
  // * use a field query:
  //     - query by the policy contained members. Example:
  //       `policy:<EMAIL>`
  //     - query by the policy contained roles. Example:
  //       `policy:roles/compute.admin`
  //     - query by the policy contained roles' included permissions. Example:
  //       `policy.role.permissions:compute.instances.create`
  google.iam.v1.Policy policy = 3;

  // Explanation about the IAM policy search result. It contains additional
  // information to explain why the search result matches the query.
  Explanation explanation = 4;
}

// Represents the detailed state of an entity under analysis, such as a
// resource, an identity or an access.
message IamPolicyAnalysisState {
  // The Google standard error code that best describes the state.
  // For example:
  // - OK means the analysis on this entity has been successfully finished;
  // - PERMISSION_DENIED means an access denied error is encountered;
  // - DEADLINE_EXCEEDED means the analysis on this entity hasn't been started
  // in time;
  google.rpc.Code code = 1;

  // The human-readable description of the cause of failure.
  string cause = 2;
}

// The condition evaluation.
message ConditionEvaluation {
  // Value of this expression.
  enum EvaluationValue {
    // Reserved for future use.
    EVALUATION_VALUE_UNSPECIFIED = 0;

    // The evaluation result is `true`.
    TRUE = 1;

    // The evaluation result is `false`.
    FALSE = 2;

    // The evaluation result is `conditional` when the condition expression
    // contains variables that are either missing input values or have not been
    // supported by Policy Analyzer yet.
    CONDITIONAL = 3;
  }

  // The evaluation result.
  EvaluationValue evaluation_value = 1;
}

// IAM Policy analysis result, consisting of one IAM policy binding and derived
// access control lists.
message IamPolicyAnalysisResult {
  // A Google Cloud resource under analysis.
  message Resource {
    // The [full resource
    // name](https://cloud.google.com/asset-inventory/docs/resource-name-format)
    string full_resource_name = 1;

    // The analysis state of this resource.
    IamPolicyAnalysisState analysis_state = 2;
  }

  // An IAM role or permission under analysis.
  message Access {
    oneof oneof_access {
      // The role.
      string role = 1;

      // The permission.
      string permission = 2;
    }

    // The analysis state of this access.
    IamPolicyAnalysisState analysis_state = 3;
  }

  // An identity under analysis.
  message Identity {
    // The identity of members, formatted as appear in an
    // [IAM policy
    // binding](https://cloud.google.com/iam/reference/rest/v1/Binding). For
    // example, they might be formatted like the following:
    //
    // - user:<EMAIL>
    // - group:<EMAIL>
    // - serviceAccount:<EMAIL>
    // - projectOwner:some_project_id
    // - domain:google.com
    // - allUsers
    string name = 1;

    // The analysis state of this identity.
    IamPolicyAnalysisState analysis_state = 2;
  }

  // A directional edge.
  message Edge {
    // The source node of the edge. For example, it could be a full resource
    // name for a resource node or an email of an identity.
    string source_node = 1;

    // The target node of the edge. For example, it could be a full resource
    // name for a resource node or an email of an identity.
    string target_node = 2;
  }

  // An access control list, derived from the above IAM policy binding, which
  // contains a set of resources and accesses. May include one
  // item from each set to compose an access control entry.
  //
  // NOTICE that there could be multiple access control lists for one IAM policy
  // binding. The access control lists are created based on resource and access
  // combinations.
  //
  // For example, assume we have the following cases in one IAM policy binding:
  // - Permission P1 and P2 apply to resource R1 and R2;
  // - Permission P3 applies to resource R2 and R3;
  //
  // This will result in the following access control lists:
  // - AccessControlList 1: [R1, R2], [P1, P2]
  // - AccessControlList 2: [R2, R3], [P3]
  message AccessControlList {
    // The resources that match one of the following conditions:
    // - The resource_selector, if it is specified in request;
    // - Otherwise, resources reachable from the policy attached resource.
    repeated Resource resources = 1;

    // The accesses that match one of the following conditions:
    // - The access_selector, if it is specified in request;
    // - Otherwise, access specifiers reachable from the policy binding's role.
    repeated Access accesses = 2;

    // Resource edges of the graph starting from the policy attached
    // resource to any descendant resources. The
    // [Edge.source_node][google.cloud.asset.v1.IamPolicyAnalysisResult.Edge.source_node]
    // contains the full resource name of a parent resource and
    // [Edge.target_node][google.cloud.asset.v1.IamPolicyAnalysisResult.Edge.target_node]
    // contains the full resource name of a child resource. This field is
    // present only if the output_resource_edges option is enabled in request.
    repeated Edge resource_edges = 3;

    // Condition evaluation for this AccessControlList, if there is a condition
    // defined in the above IAM policy binding.
    ConditionEvaluation condition_evaluation = 4;
  }

  // The identities and group edges.
  message IdentityList {
    // Only the identities that match one of the following conditions will be
    // presented:
    // - The identity_selector, if it is specified in request;
    // - Otherwise, identities reachable from the policy binding's members.
    repeated Identity identities = 1;

    // Group identity edges of the graph starting from the binding's
    // group members to any node of the
    // [identities][google.cloud.asset.v1.IamPolicyAnalysisResult.IdentityList.identities].
    // The
    // [Edge.source_node][google.cloud.asset.v1.IamPolicyAnalysisResult.Edge.source_node]
    // contains a group, such as `group:<EMAIL>`. The
    // [Edge.target_node][google.cloud.asset.v1.IamPolicyAnalysisResult.Edge.target_node]
    // contains a member of the group, such as `group:<EMAIL>` or
    // `user:<EMAIL>`. This field is present only if the
    // output_group_edges option is enabled in request.
    repeated Edge group_edges = 2;
  }

  // The [full resource
  // name](https://cloud.google.com/asset-inventory/docs/resource-name-format)
  // of the resource to which the
  // [iam_binding][google.cloud.asset.v1.IamPolicyAnalysisResult.iam_binding]
  // policy attaches.
  string attached_resource_full_name = 1;

  // The IAM policy binding under analysis.
  google.iam.v1.Binding iam_binding = 2;

  // The access control lists derived from the
  // [iam_binding][google.cloud.asset.v1.IamPolicyAnalysisResult.iam_binding]
  // that match or potentially match resource and access selectors specified in
  // the request.
  repeated AccessControlList access_control_lists = 3;

  // The identity list derived from members of the
  // [iam_binding][google.cloud.asset.v1.IamPolicyAnalysisResult.iam_binding]
  // that match or potentially match identity selector specified in the request.
  IdentityList identity_list = 4;

  // Represents whether all analyses on the
  // [iam_binding][google.cloud.asset.v1.IamPolicyAnalysisResult.iam_binding]
  // have successfully finished.
  bool fully_explored = 5;
}
