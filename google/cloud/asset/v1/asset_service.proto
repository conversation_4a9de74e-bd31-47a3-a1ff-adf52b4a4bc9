// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.asset.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/asset/v1/assets.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/expr.proto";

option csharp_namespace = "Google.Cloud.Asset.V1";
option go_package = "cloud.google.com/go/asset/apiv1/assetpb;assetpb";
option java_multiple_files = true;
option java_outer_classname = "AssetServiceProto";
option java_package = "com.google.cloud.asset.v1";
option php_namespace = "Google\\Cloud\\Asset\\V1";

// Asset service definition.
service AssetService {
  option (google.api.default_host) = "cloudasset.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Exports assets with time and resource types to a given Cloud Storage
  // location/BigQuery table. For Cloud Storage location destinations, the
  // output format is newline-delimited JSON. Each line represents a
  // [google.cloud.asset.v1.Asset][google.cloud.asset.v1.Asset] in the JSON
  // format; for BigQuery table destinations, the output table stores the fields
  // in asset Protobuf as columns. This API implements the
  // [google.longrunning.Operation][google.longrunning.Operation] API, which
  // allows you to keep track of the export. We recommend intervals of at least
  // 2 seconds with exponential retry to poll the export operation result. For
  // regular-size resource parent, the export operation usually finishes within
  // 5 minutes.
  rpc ExportAssets(ExportAssetsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=*/*}:exportAssets"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.asset.v1.ExportAssetsResponse"
      metadata_type: "google.cloud.asset.v1.ExportAssetsRequest"
    };
  }

  // Lists assets with time and resource types and returns paged results in
  // response.
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=*/*}/assets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Batch gets the update history of assets that overlap a time window.
  // For IAM_POLICY content, this API outputs history when the asset and its
  // attached IAM POLICY both exist. This can create gaps in the output history.
  // Otherwise, this API outputs history with asset in both non-delete or
  // deleted status.
  // If a specified asset does not exist, this API returns an INVALID_ARGUMENT
  // error.
  rpc BatchGetAssetsHistory(BatchGetAssetsHistoryRequest)
      returns (BatchGetAssetsHistoryResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=*/*}:batchGetAssetsHistory"
    };
  }

  // Creates a feed in a parent project/folder/organization to listen to its
  // asset updates.
  rpc CreateFeed(CreateFeedRequest) returns (Feed) {
    option (google.api.http) = {
      post: "/v1/{parent=*/*}/feeds"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details about an asset feed.
  rpc GetFeed(GetFeedRequest) returns (Feed) {
    option (google.api.http) = {
      get: "/v1/{name=*/*/feeds/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all asset feeds in a parent project/folder/organization.
  rpc ListFeeds(ListFeedsRequest) returns (ListFeedsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=*/*}/feeds"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates an asset feed configuration.
  rpc UpdateFeed(UpdateFeedRequest) returns (Feed) {
    option (google.api.http) = {
      patch: "/v1/{feed.name=*/*/feeds/*}"
      body: "*"
    };
    option (google.api.method_signature) = "feed";
  }

  // Deletes an asset feed.
  rpc DeleteFeed(DeleteFeedRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=*/*/feeds/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Searches all Google Cloud resources within the specified scope, such as a
  // project, folder, or organization. The caller must be granted the
  // `cloudasset.assets.searchAllResources` permission on the desired scope,
  // otherwise the request will be rejected.
  rpc SearchAllResources(SearchAllResourcesRequest)
      returns (SearchAllResourcesResponse) {
    option (google.api.http) = {
      get: "/v1/{scope=*/*}:searchAllResources"
    };
    option (google.api.method_signature) = "scope,query,asset_types";
  }

  // Searches all IAM policies within the specified scope, such as a project,
  // folder, or organization. The caller must be granted the
  // `cloudasset.assets.searchAllIamPolicies` permission on the desired scope,
  // otherwise the request will be rejected.
  rpc SearchAllIamPolicies(SearchAllIamPoliciesRequest)
      returns (SearchAllIamPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/{scope=*/*}:searchAllIamPolicies"
    };
    option (google.api.method_signature) = "scope,query";
  }

  // Analyzes IAM policies to answer which identities have what accesses on
  // which resources.
  rpc AnalyzeIamPolicy(AnalyzeIamPolicyRequest)
      returns (AnalyzeIamPolicyResponse) {
    option (google.api.http) = {
      get: "/v1/{analysis_query.scope=*/*}:analyzeIamPolicy"
    };
  }

  // Analyzes IAM policies asynchronously to answer which identities have what
  // accesses on which resources, and writes the analysis results to a Google
  // Cloud Storage or a BigQuery destination. For Cloud Storage destination, the
  // output format is the JSON format that represents a
  // [AnalyzeIamPolicyResponse][google.cloud.asset.v1.AnalyzeIamPolicyResponse].
  // This method implements the
  // [google.longrunning.Operation][google.longrunning.Operation], which allows
  // you to track the operation status. We recommend intervals of at least 2
  // seconds with exponential backoff retry to poll the operation result. The
  // metadata contains the metadata for the long-running operation.
  rpc AnalyzeIamPolicyLongrunning(AnalyzeIamPolicyLongrunningRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{analysis_query.scope=*/*}:analyzeIamPolicyLongrunning"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.asset.v1.AnalyzeIamPolicyLongrunningResponse"
      metadata_type: "google.cloud.asset.v1.AnalyzeIamPolicyLongrunningMetadata"
    };
  }

  // Analyze moving a resource to a specified destination without kicking off
  // the actual move. The analysis is best effort depending on the user's
  // permissions of viewing different hierarchical policies and configurations.
  // The policies and configuration are subject to change before the actual
  // resource migration takes place.
  rpc AnalyzeMove(AnalyzeMoveRequest) returns (AnalyzeMoveResponse) {
    option (google.api.http) = {
      get: "/v1/{resource=*/*}:analyzeMove"
    };
  }

  // Issue a job that queries assets using a SQL statement compatible with
  // [BigQuery SQL](https://cloud.google.com/bigquery/docs/introduction-sql).
  //
  // If the query execution finishes within timeout and there's no pagination,
  // the full query results will be returned in the `QueryAssetsResponse`.
  //
  // Otherwise, full query results can be obtained by issuing extra requests
  // with the `job_reference` from the a previous `QueryAssets` call.
  //
  // Note, the query result has approximately 10 GB limitation enforced by
  // [BigQuery](https://cloud.google.com/bigquery/docs/best-practices-performance-output).
  // Queries return larger results will result in errors.
  rpc QueryAssets(QueryAssetsRequest) returns (QueryAssetsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=*/*}:queryAssets"
      body: "*"
    };
  }

  // Creates a saved query in a parent project/folder/organization.
  rpc CreateSavedQuery(CreateSavedQueryRequest) returns (SavedQuery) {
    option (google.api.http) = {
      post: "/v1/{parent=*/*}/savedQueries"
      body: "saved_query"
    };
    option (google.api.method_signature) = "parent,saved_query,saved_query_id";
  }

  // Gets details about a saved query.
  rpc GetSavedQuery(GetSavedQueryRequest) returns (SavedQuery) {
    option (google.api.http) = {
      get: "/v1/{name=*/*/savedQueries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all saved queries in a parent project/folder/organization.
  rpc ListSavedQueries(ListSavedQueriesRequest)
      returns (ListSavedQueriesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=*/*}/savedQueries"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates a saved query.
  rpc UpdateSavedQuery(UpdateSavedQueryRequest) returns (SavedQuery) {
    option (google.api.http) = {
      patch: "/v1/{saved_query.name=*/*/savedQueries/*}"
      body: "saved_query"
    };
    option (google.api.method_signature) = "saved_query,update_mask";
  }

  // Deletes a saved query.
  rpc DeleteSavedQuery(DeleteSavedQueryRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=*/*/savedQueries/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets effective IAM policies for a batch of resources.
  rpc BatchGetEffectiveIamPolicies(BatchGetEffectiveIamPoliciesRequest)
      returns (BatchGetEffectiveIamPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/{scope=*/*}/effectiveIamPolicies:batchGet"
    };
  }

  // Analyzes organization policies under a scope.
  rpc AnalyzeOrgPolicies(AnalyzeOrgPoliciesRequest)
      returns (AnalyzeOrgPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1/{scope=*/*}:analyzeOrgPolicies"
    };
    option (google.api.method_signature) = "scope,constraint,filter";
  }

  // Analyzes organization policies governed containers (projects, folders or
  // organization) under a scope.
  rpc AnalyzeOrgPolicyGovernedContainers(
      AnalyzeOrgPolicyGovernedContainersRequest)
      returns (AnalyzeOrgPolicyGovernedContainersResponse) {
    option (google.api.http) = {
      get: "/v1/{scope=*/*}:analyzeOrgPolicyGovernedContainers"
    };
    option (google.api.method_signature) = "scope,constraint,filter";
  }

  // Analyzes organization policies governed assets (Google Cloud resources or
  // policies) under a scope. This RPC supports custom constraints and the
  // following canned constraints:
  //
  // * constraints/ainotebooks.accessMode
  // * constraints/ainotebooks.disableFileDownloads
  // * constraints/ainotebooks.disableRootAccess
  // * constraints/ainotebooks.disableTerminal
  // * constraints/ainotebooks.environmentOptions
  // * constraints/ainotebooks.requireAutoUpgradeSchedule
  // * constraints/ainotebooks.restrictVpcNetworks
  // * constraints/compute.disableGuestAttributesAccess
  // * constraints/compute.disableInstanceDataAccessApis
  // * constraints/compute.disableNestedVirtualization
  // * constraints/compute.disableSerialPortAccess
  // * constraints/compute.disableSerialPortLogging
  // * constraints/compute.disableVpcExternalIpv6
  // * constraints/compute.requireOsLogin
  // * constraints/compute.requireShieldedVm
  // * constraints/compute.restrictLoadBalancerCreationForTypes
  // * constraints/compute.restrictProtocolForwardingCreationForTypes
  // * constraints/compute.restrictXpnProjectLienRemoval
  // * constraints/compute.setNewProjectDefaultToZonalDNSOnly
  // * constraints/compute.skipDefaultNetworkCreation
  // * constraints/compute.trustedImageProjects
  // * constraints/compute.vmCanIpForward
  // * constraints/compute.vmExternalIpAccess
  // * constraints/gcp.detailedAuditLoggingMode
  // * constraints/gcp.resourceLocations
  // * constraints/iam.allowedPolicyMemberDomains
  // * constraints/iam.automaticIamGrantsForDefaultServiceAccounts
  // * constraints/iam.disableServiceAccountCreation
  // * constraints/iam.disableServiceAccountKeyCreation
  // * constraints/iam.disableServiceAccountKeyUpload
  // * constraints/iam.restrictCrossProjectServiceAccountLienRemoval
  // * constraints/iam.serviceAccountKeyExpiryHours
  // * constraints/resourcemanager.accessBoundaries
  // * constraints/resourcemanager.allowedExportDestinations
  // * constraints/sql.restrictAuthorizedNetworks
  // * constraints/sql.restrictNoncompliantDiagnosticDataAccess
  // * constraints/sql.restrictNoncompliantResourceCreation
  // * constraints/sql.restrictPublicIp
  // * constraints/storage.publicAccessPrevention
  // * constraints/storage.restrictAuthTypes
  // * constraints/storage.uniformBucketLevelAccess
  //
  // This RPC only returns either resources of types [supported by search
  // APIs](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
  // or IAM policies.
  rpc AnalyzeOrgPolicyGovernedAssets(AnalyzeOrgPolicyGovernedAssetsRequest)
      returns (AnalyzeOrgPolicyGovernedAssetsResponse) {
    option (google.api.http) = {
      get: "/v1/{scope=*/*}:analyzeOrgPolicyGovernedAssets"
    };
    option (google.api.method_signature) = "scope,constraint,filter";
  }
}

// Represents the metadata of the longrunning operation for the
// AnalyzeIamPolicyLongrunning RPC.
message AnalyzeIamPolicyLongrunningMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Export asset request.
message ExportAssetsRequest {
  // Required. The relative name of the root asset. This can only be an
  // organization number (such as "organizations/123"), a project ID (such as
  // "projects/my-project-id"), or a project number (such as "projects/12345"),
  // or a folder number (such as "folders/123").
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudasset.googleapis.com/Asset"
    }
  ];

  // Timestamp to take an asset snapshot. This can only be set to a timestamp
  // between the current time and the current time minus 35 days (inclusive).
  // If not specified, the current time will be used. Due to delays in resource
  // data collection and indexing, there is a volatile window during which
  // running the same query may get different results.
  google.protobuf.Timestamp read_time = 2;

  // A list of asset types to take a snapshot for. For example:
  // "compute.googleapis.com/Disk".
  //
  // Regular expressions are also supported. For example:
  //
  // * "compute.googleapis.com.*" snapshots resources whose asset type starts
  // with "compute.googleapis.com".
  // * ".*Instance" snapshots resources whose asset type ends with "Instance".
  // * ".*Instance.*" snapshots resources whose asset type contains "Instance".
  //
  // See [RE2](https://github.com/google/re2/wiki/Syntax) for all supported
  // regular expression syntax. If the regular expression does not match any
  // supported asset type, an INVALID_ARGUMENT error will be returned.
  //
  // If specified, only matching assets will be returned, otherwise, it will
  // snapshot all asset types. See [Introduction to Cloud Asset
  // Inventory](https://cloud.google.com/asset-inventory/docs/overview)
  // for all supported asset types.
  repeated string asset_types = 3;

  // Asset content type. If not specified, no content but the asset name will be
  // returned.
  ContentType content_type = 4;

  // Required. Output configuration indicating where the results will be output
  // to.
  OutputConfig output_config = 5 [(google.api.field_behavior) = REQUIRED];

  // A list of relationship types to export, for example:
  // `INSTANCE_TO_INSTANCEGROUP`. This field should only be specified if
  // content_type=RELATIONSHIP.
  // * If specified:
  // it snapshots specified relationships. It returns an error if
  // any of the [relationship_types] doesn't belong to the supported
  // relationship types of the [asset_types] or if any of the [asset_types]
  // doesn't belong to the source types of the [relationship_types].
  // * Otherwise:
  // it snapshots the supported relationships for all [asset_types] or returns
  // an error if any of the [asset_types] has no relationship support.
  // An unspecified asset types field means all supported asset_types.
  // See [Introduction to Cloud Asset
  // Inventory](https://cloud.google.com/asset-inventory/docs/overview) for all
  // supported asset types and relationship types.
  repeated string relationship_types = 6;
}

// The export asset response. This message is returned by the
// [google.longrunning.Operations.GetOperation][google.longrunning.Operations.GetOperation]
// method in the returned
// [google.longrunning.Operation.response][google.longrunning.Operation.response]
// field.
message ExportAssetsResponse {
  // Time the snapshot was taken.
  google.protobuf.Timestamp read_time = 1;

  // Output configuration indicating where the results were output to.
  OutputConfig output_config = 2;

  // Output result indicating where the assets were exported to. For example, a
  // set of actual Cloud Storage object URIs where the assets are exported to.
  // The URIs can be different from what [output_config] has specified, as the
  // service will split the output object into multiple ones once it exceeds a
  // single Cloud Storage object limit.
  OutputResult output_result = 3;
}

// ListAssets request.
message ListAssetsRequest {
  // Required. Name of the organization, folder, or project the assets belong
  // to. Format: "organizations/[organization-number]" (such as
  // "organizations/123"), "projects/[project-id]" (such as
  // "projects/my-project-id"), "projects/[project-number]" (such as
  // "projects/12345"), or "folders/[folder-number]" (such as "folders/12345").
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudasset.googleapis.com/Asset"
    }
  ];

  // Timestamp to take an asset snapshot. This can only be set to a timestamp
  // between the current time and the current time minus 35 days (inclusive).
  // If not specified, the current time will be used. Due to delays in resource
  // data collection and indexing, there is a volatile window during which
  // running the same query may get different results.
  google.protobuf.Timestamp read_time = 2;

  // A list of asset types to take a snapshot for. For example:
  // "compute.googleapis.com/Disk".
  //
  // Regular expression is also supported. For example:
  //
  // * "compute.googleapis.com.*" snapshots resources whose asset type starts
  // with "compute.googleapis.com".
  // * ".*Instance" snapshots resources whose asset type ends with "Instance".
  // * ".*Instance.*" snapshots resources whose asset type contains "Instance".
  //
  // See [RE2](https://github.com/google/re2/wiki/Syntax) for all supported
  // regular expression syntax. If the regular expression does not match any
  // supported asset type, an INVALID_ARGUMENT error will be returned.
  //
  // If specified, only matching assets will be returned, otherwise, it will
  // snapshot all asset types. See [Introduction to Cloud Asset
  // Inventory](https://cloud.google.com/asset-inventory/docs/overview)
  // for all supported asset types.
  repeated string asset_types = 3;

  // Asset content type. If not specified, no content but the asset name will
  // be returned.
  ContentType content_type = 4;

  // The maximum number of assets to be returned in a single response. Default
  // is 100, minimum is 1, and maximum is 1000.
  int32 page_size = 5;

  // The `next_page_token` returned from the previous `ListAssetsResponse`, or
  // unspecified for the first `ListAssetsRequest`. It is a continuation of a
  // prior `ListAssets` call, and the API should return the next page of assets.
  string page_token = 6;

  // A list of relationship types to output, for example:
  // `INSTANCE_TO_INSTANCEGROUP`. This field should only be specified if
  // content_type=RELATIONSHIP.
  // * If specified:
  // it snapshots specified relationships. It returns an error if
  // any of the [relationship_types] doesn't belong to the supported
  // relationship types of the [asset_types] or if any of the [asset_types]
  // doesn't belong to the source types of the [relationship_types].
  // * Otherwise:
  // it snapshots the supported relationships for all [asset_types] or returns
  // an error if any of the [asset_types] has no relationship support.
  // An unspecified asset types field means all supported asset_types.
  // See [Introduction to Cloud Asset
  // Inventory](https://cloud.google.com/asset-inventory/docs/overview)
  // for all supported asset types and relationship types.
  repeated string relationship_types = 7;
}

// ListAssets response.
message ListAssetsResponse {
  // Time the snapshot was taken.
  google.protobuf.Timestamp read_time = 1;

  // Assets.
  repeated Asset assets = 2;

  // Token to retrieve the next page of results. It expires 72 hours after the
  // page token for the first page is generated. Set to empty if there are no
  // remaining results.
  string next_page_token = 3;
}

// Batch get assets history request.
message BatchGetAssetsHistoryRequest {
  // Required. The relative name of the root asset. It can only be an
  // organization number (such as "organizations/123"), a project ID (such as
  // "projects/my-project-id")", or a project number (such as "projects/12345").
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudasset.googleapis.com/Asset"
    }
  ];

  // A list of the full names of the assets.
  // See: https://cloud.google.com/asset-inventory/docs/resource-name-format
  // Example:
  //
  // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`.
  //
  // The request becomes a no-op if the asset name list is empty, and the max
  // size of the asset name list is 100 in one request.
  repeated string asset_names = 2;

  // Optional. The content type.
  ContentType content_type = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The time window for the asset history. Both start_time and
  // end_time are optional and if set, it must be after the current time minus
  // 35 days. If end_time is not set, it is default to current timestamp.
  // If start_time is not set, the snapshot of the assets at end_time will be
  // returned. The returned results contain all temporal assets whose time
  // window overlap with read_time_window.
  TimeWindow read_time_window = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of relationship types to output, for example:
  // `INSTANCE_TO_INSTANCEGROUP`. This field should only be specified if
  // content_type=RELATIONSHIP.
  // * If specified:
  // it outputs specified relationships' history on the [asset_names]. It
  // returns an error if any of the [relationship_types] doesn't belong to the
  // supported relationship types of the [asset_names] or if any of the
  // [asset_names]'s types doesn't belong to the source types of the
  // [relationship_types].
  // * Otherwise:
  // it outputs the supported relationships' history on the [asset_names] or
  // returns an error if any of the [asset_names]'s types has no relationship
  // support.
  // See [Introduction to Cloud Asset
  // Inventory](https://cloud.google.com/asset-inventory/docs/overview) for all
  // supported asset types and relationship types.
  repeated string relationship_types = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Batch get assets history response.
message BatchGetAssetsHistoryResponse {
  // A list of assets with valid time windows.
  repeated TemporalAsset assets = 1;
}

// Create asset feed request.
message CreateFeedRequest {
  // Required. The name of the project/folder/organization where this feed
  // should be created in. It can only be an organization number (such as
  // "organizations/123"), a folder number (such as "folders/123"), a project ID
  // (such as "projects/my-project-id"), or a project number (such as
  // "projects/12345").
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. This is the client-assigned asset feed identifier and it needs to
  // be unique under a specific parent project/folder/organization.
  string feed_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The feed details. The field `name` must be empty and it will be
  // generated in the format of: projects/project_number/feeds/feed_id
  // folders/folder_number/feeds/feed_id
  // organizations/organization_number/feeds/feed_id
  Feed feed = 3 [(google.api.field_behavior) = REQUIRED];
}

// Get asset feed request.
message GetFeedRequest {
  // Required. The name of the Feed and it must be in the format of:
  // projects/project_number/feeds/feed_id
  // folders/folder_number/feeds/feed_id
  // organizations/organization_number/feeds/feed_id
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "cloudasset.googleapis.com/Feed" }
  ];
}

// List asset feeds request.
message ListFeedsRequest {
  // Required. The parent project/folder/organization whose feeds are to be
  // listed. It can only be using project/folder/organization number (such as
  // "folders/12345")", or a project ID (such as "projects/my-project-id").
  string parent = 1 [(google.api.field_behavior) = REQUIRED];
}

message ListFeedsResponse {
  // A list of feeds.
  repeated Feed feeds = 1;
}

// Update asset feed request.
message UpdateFeedRequest {
  // Required. The new values of feed details. It must match an existing feed
  // and the field `name` must be in the format of:
  // projects/project_number/feeds/feed_id or
  // folders/folder_number/feeds/feed_id or
  // organizations/organization_number/feeds/feed_id.
  Feed feed = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Only updates the `feed` fields indicated by this mask.
  // The field mask must not be empty, and it must not contain fields that
  // are immutable or only set by the server.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

message DeleteFeedRequest {
  // Required. The name of the feed and it must be in the format of:
  // projects/project_number/feeds/feed_id
  // folders/folder_number/feeds/feed_id
  // organizations/organization_number/feeds/feed_id
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "cloudasset.googleapis.com/Feed" }
  ];
}

// Output configuration for export assets destination.
message OutputConfig {
  // Asset export destination.
  oneof destination {
    // Destination on Cloud Storage.
    GcsDestination gcs_destination = 1;

    // Destination on BigQuery. The output table stores the fields in asset
    // Protobuf as columns in BigQuery.
    BigQueryDestination bigquery_destination = 2;
  }
}

// Output result of export assets.
message OutputResult {
  // Asset export result.
  oneof result {
    // Export result on Cloud Storage.
    GcsOutputResult gcs_result = 1;
  }
}

// A Cloud Storage output result.
message GcsOutputResult {
  // List of URIs of the Cloud Storage objects. Example:
  // "gs://bucket_name/object_name".
  repeated string uris = 1;
}

// A Cloud Storage location.
message GcsDestination {
  // Required.
  oneof object_uri {
    // The URI of the Cloud Storage object. It's the same URI that is used by
    // gsutil. Example: "gs://bucket_name/object_name". See [Viewing and
    // Editing Object
    // Metadata](https://cloud.google.com/storage/docs/viewing-editing-metadata)
    // for more information.
    //
    // If the specified Cloud Storage object already exists and there is no
    // [hold](https://cloud.google.com/storage/docs/object-holds), it will be
    // overwritten with the exported result.
    string uri = 1;

    // The URI prefix of all generated Cloud Storage objects. Example:
    // "gs://bucket_name/object_name_prefix". Each object URI is in format:
    // "gs://bucket_name/object_name_prefix/<asset type>/<shard number> and only
    // contains assets for that type. <shard number> starts from 0. Example:
    // "gs://bucket_name/object_name_prefix/compute.googleapis.com/Disk/0" is
    // the first shard of output objects containing all
    // compute.googleapis.com/Disk assets. An INVALID_ARGUMENT error will be
    // returned if file with the same name "gs://bucket_name/object_name_prefix"
    // already exists.
    string uri_prefix = 2;
  }
}

// A BigQuery destination for exporting assets to.
message BigQueryDestination {
  // Required. The BigQuery dataset in format
  // "projects/projectId/datasets/datasetId", to which the snapshot result
  // should be exported. If this dataset does not exist, the export call returns
  // an INVALID_ARGUMENT error. Setting the `contentType` for `exportAssets`
  // determines the
  // [schema](/asset-inventory/docs/exporting-to-bigquery#bigquery-schema)
  // of the BigQuery table. Setting `separateTablesPerAssetType` to `TRUE` also
  // influences the schema.
  string dataset = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The BigQuery table to which the snapshot result should be
  // written. If this table does not exist, a new table with the given name
  // will be created.
  string table = 2 [(google.api.field_behavior) = REQUIRED];

  // If the destination table already exists and this flag is `TRUE`, the
  // table will be overwritten by the contents of assets snapshot. If the flag
  // is `FALSE` or unset and the destination table already exists, the export
  // call returns an INVALID_ARGUMEMT error.
  bool force = 3;

  // [partition_spec] determines whether to export to partitioned table(s) and
  // how to partition the data.
  //
  // If [partition_spec] is unset or [partition_spec.partition_key] is unset or
  // `PARTITION_KEY_UNSPECIFIED`, the snapshot results will be exported to
  // non-partitioned table(s). [force] will decide whether to overwrite existing
  // table(s).
  //
  // If [partition_spec] is specified. First, the snapshot results will be
  // written to partitioned table(s) with two additional timestamp columns,
  // readTime and requestTime, one of which will be the partition key. Secondly,
  // in the case when any destination table already exists, it will first try to
  // update existing table's schema as necessary by appending additional
  // columns. Then, if [force] is `TRUE`, the corresponding partition will be
  // overwritten by the snapshot results (data in different partitions will
  // remain intact); if [force] is unset or `FALSE`, it will append the data. An
  // error will be returned if the schema update or data appension fails.
  PartitionSpec partition_spec = 4;

  // If this flag is `TRUE`, the snapshot results will be written to one or
  // multiple tables, each of which contains results of one asset type. The
  // [force] and [partition_spec] fields will apply to each of them.
  //
  // Field [table] will be concatenated with "_" and the asset type names (see
  // https://cloud.google.com/asset-inventory/docs/supported-asset-types for
  // supported asset types) to construct per-asset-type table names, in which
  // all non-alphanumeric characters like "." and "/" will be substituted by
  // "_". Example: if field [table] is "mytable" and snapshot results
  // contain "storage.googleapis.com/Bucket" assets, the corresponding table
  // name will be "mytable_storage_googleapis_com_Bucket". If any of these
  // tables does not exist, a new table with the concatenated name will be
  // created.
  //
  // When [content_type] in the ExportAssetsRequest is `RESOURCE`, the schema of
  // each table will include RECORD-type columns mapped to the nested fields in
  // the Asset.resource.data field of that asset type (up to the 15 nested level
  // BigQuery supports
  // (https://cloud.google.com/bigquery/docs/nested-repeated#limitations)). The
  // fields in >15 nested levels will be stored in JSON format string as a child
  // column of its parent RECORD column.
  //
  // If error occurs when exporting to any table, the whole export call will
  // return an error but the export results that already succeed will persist.
  // Example: if exporting to table_type_A succeeds when exporting to
  // table_type_B fails during one export call, the results in table_type_A will
  // persist and there will not be partial results persisting in a table.
  bool separate_tables_per_asset_type = 5;
}

// Specifications of BigQuery partitioned table as export destination.
message PartitionSpec {
  // This enum is used to determine the partition key column when exporting
  // assets to BigQuery partitioned table(s). Note that, if the partition key is
  // a timestamp column, the actual partition is based on its date value
  // (expressed in UTC. see details in
  // https://cloud.google.com/bigquery/docs/partitioned-tables#date_timestamp_partitioned_tables).
  enum PartitionKey {
    // Unspecified partition key. If used, it means using non-partitioned table.
    PARTITION_KEY_UNSPECIFIED = 0;

    // The time when the snapshot is taken. If specified as partition key, the
    // result table(s) is partitoned by the additional timestamp column,
    // readTime. If [read_time] in ExportAssetsRequest is specified, the
    // readTime column's value will be the same as it. Otherwise, its value will
    // be the current time that is used to take the snapshot.
    READ_TIME = 1;

    // The time when the request is received and started to be processed. If
    // specified as partition key, the result table(s) is partitoned by the
    // requestTime column, an additional timestamp column representing when the
    // request was received.
    REQUEST_TIME = 2;
  }

  // The partition key for BigQuery partitioned table.
  PartitionKey partition_key = 1;
}

// A Pub/Sub destination.
message PubsubDestination {
  // The name of the Pub/Sub topic to publish to.
  // Example: `projects/PROJECT_ID/topics/TOPIC_ID`.
  string topic = 1;
}

// Output configuration for asset feed destination.
message FeedOutputConfig {
  // Asset feed destination.
  oneof destination {
    // Destination on Pub/Sub.
    PubsubDestination pubsub_destination = 1;
  }
}

// An asset feed used to export asset updates to a destinations.
// An asset feed filter controls what updates are exported.
// The asset feed must be created within a project, organization, or
// folder. Supported destinations are:
// Pub/Sub topics.
message Feed {
  option (google.api.resource) = {
    type: "cloudasset.googleapis.com/Feed"
    pattern: "projects/{project}/feeds/{feed}"
    pattern: "folders/{folder}/feeds/{feed}"
    pattern: "organizations/{organization}/feeds/{feed}"
    history: ORIGINALLY_SINGLE_PATTERN
  };

  // Required. The format will be
  // projects/{project_number}/feeds/{client-assigned_feed_identifier} or
  // folders/{folder_number}/feeds/{client-assigned_feed_identifier} or
  // organizations/{organization_number}/feeds/{client-assigned_feed_identifier}
  //
  // The client-assigned feed identifier must be unique within the parent
  // project/folder/organization.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // A list of the full names of the assets to receive updates. You must specify
  // either or both of asset_names and asset_types. Only asset updates matching
  // specified asset_names or asset_types are exported to the feed.
  // Example:
  // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`.
  // For a list of the full names for supported asset types, see [Resource
  // name format](/asset-inventory/docs/resource-name-format).
  repeated string asset_names = 2;

  // A list of types of the assets to receive updates. You must specify either
  // or both of asset_names and asset_types. Only asset updates matching
  // specified asset_names or asset_types are exported to the feed.
  // Example: `"compute.googleapis.com/Disk"`
  //
  // For a list of all supported asset types, see
  // [Supported asset types](/asset-inventory/docs/supported-asset-types).
  repeated string asset_types = 3;

  // Asset content type. If not specified, no content but the asset name and
  // type will be returned.
  ContentType content_type = 4;

  // Required. Feed output configuration defining where the asset updates are
  // published to.
  FeedOutputConfig feed_output_config = 5
      [(google.api.field_behavior) = REQUIRED];

  // A condition which determines whether an asset update should be published.
  // If specified, an asset will be returned only when the expression evaluates
  // to true.
  // When set, `expression` field in the `Expr` must be a valid [CEL expression]
  // (https://github.com/google/cel-spec) on a TemporalAsset with name
  // `temporal_asset`. Example: a Feed with expression ("temporal_asset.deleted
  // == true") will only publish Asset deletions. Other fields of `Expr` are
  // optional.
  //
  // See our [user
  // guide](https://cloud.google.com/asset-inventory/docs/monitoring-asset-changes-with-condition)
  // for detailed instructions.
  google.type.Expr condition = 6;

  // A list of relationship types to output, for example:
  // `INSTANCE_TO_INSTANCEGROUP`. This field should only be specified if
  // content_type=RELATIONSHIP.
  // * If specified:
  // it outputs specified relationship updates on the [asset_names] or the
  // [asset_types]. It returns an error if any of the [relationship_types]
  // doesn't belong to the supported relationship types of the [asset_names] or
  // [asset_types], or any of the [asset_names] or the [asset_types] doesn't
  // belong to the source types of the [relationship_types].
  // * Otherwise:
  // it outputs the supported relationships of the types of [asset_names] and
  // [asset_types] or returns an error if any of the [asset_names] or the
  // [asset_types] has no replationship support.
  // See [Introduction to Cloud Asset
  // Inventory](https://cloud.google.com/asset-inventory/docs/overview)
  // for all supported asset types and relationship types.
  repeated string relationship_types = 7;
}

// Search all resources request.
message SearchAllResourcesRequest {
  // Required. A scope can be a project, a folder, or an organization. The
  // search is limited to the resources within the `scope`. The caller must be
  // granted the
  // [`cloudasset.assets.searchAllResources`](https://cloud.google.com/asset-inventory/docs/access-control#required_permissions)
  // permission on the desired scope.
  //
  // The allowed values are:
  //
  // * projects/{PROJECT_ID} (e.g., "projects/foo-bar")
  // * projects/{PROJECT_NUMBER} (e.g., "projects/12345678")
  // * folders/{FOLDER_NUMBER} (e.g., "folders/1234567")
  // * organizations/{ORGANIZATION_NUMBER} (e.g., "organizations/123456")
  string scope = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The query statement. See [how to construct a
  // query](https://cloud.google.com/asset-inventory/docs/searching-resources#how_to_construct_a_query)
  // for more information. If not specified or empty, it will search all the
  // resources within the specified `scope`.
  //
  // Examples:
  //
  // * `name:Important` to find Google Cloud resources whose name contains
  //   `Important` as a word.
  // * `name=Important` to find the Google Cloud resource whose name is exactly
  //   `Important`.
  // * `displayName:Impor*` to find Google Cloud resources whose display name
  //   contains `Impor` as a prefix of any word in the field.
  // * `location:us-west*` to find Google Cloud resources whose location
  //   contains both `us` and `west` as prefixes.
  // * `labels:prod` to find Google Cloud resources whose labels contain `prod`
  //   as a key or value.
  // * `labels.env:prod` to find Google Cloud resources that have a label `env`
  //   and its value is `prod`.
  // * `labels.env:*` to find Google Cloud resources that have a label `env`.
  // * `tagKeys:env` to find Google Cloud resources that have directly
  //   attached tags where the
  //   [`TagKey.namespacedName`](https://cloud.google.com/resource-manager/reference/rest/v3/tagKeys#resource:-tagkey)
  //   contains `env`.
  // * `tagValues:prod*` to find Google Cloud resources that have directly
  //   attached tags where the
  //   [`TagValue.namespacedName`](https://cloud.google.com/resource-manager/reference/rest/v3/tagValues#resource:-tagvalue)
  //   contains a word prefixed by `prod`.
  // * `tagValueIds=tagValues/123` to find Google Cloud resources that have
  //   directly attached tags where the
  //   [`TagValue.name`](https://cloud.google.com/resource-manager/reference/rest/v3/tagValues#resource:-tagvalue)
  //   is exactly `tagValues/123`.
  // * `effectiveTagKeys:env` to find Google Cloud resources that have
  //   directly attached or inherited tags where the
  //   [`TagKey.namespacedName`](https://cloud.google.com/resource-manager/reference/rest/v3/tagKeys#resource:-tagkey)
  //   contains `env`.
  // * `effectiveTagValues:prod*` to find Google Cloud resources that have
  //   directly attached or inherited tags where the
  //   [`TagValue.namespacedName`](https://cloud.google.com/resource-manager/reference/rest/v3/tagValues#resource:-tagvalue)
  //   contains a word prefixed by `prod`.
  // * `effectiveTagValueIds=tagValues/123` to find Google Cloud resources that
  //    have directly attached or inherited tags where the
  //   [`TagValue.name`](https://cloud.google.com/resource-manager/reference/rest/v3/tagValues#resource:-tagvalue)
  //   is exactly `tagValues/123`.
  // * `kmsKey:key` to find Google Cloud resources encrypted with a
  //   customer-managed encryption key whose name contains `key` as a word. This
  //   field is deprecated. Use the `kmsKeys` field to retrieve Cloud KMS
  //   key information.
  // * `kmsKeys:key` to find Google Cloud resources encrypted with
  //   customer-managed encryption keys whose name contains the word `key`.
  // * `relationships:instance-group-1` to find Google Cloud resources that have
  //   relationships with `instance-group-1` in the related resource name.
  // * `relationships:INSTANCE_TO_INSTANCEGROUP` to find Compute Engine
  //   instances that have relationships of type `INSTANCE_TO_INSTANCEGROUP`.
  // * `relationships.INSTANCE_TO_INSTANCEGROUP:instance-group-1` to find
  //   Compute Engine instances that have relationships with `instance-group-1`
  //   in the Compute Engine instance group resource name, for relationship type
  //   `INSTANCE_TO_INSTANCEGROUP`.
  // * `sccSecurityMarks.key=value` to find Cloud resources that are attached
  //   with security marks whose key is `key` and value is `value`.
  // * `sccSecurityMarks.key:*` to find Cloud resources that are attached with
  //   security marks whose key is `key`.
  // * `state:ACTIVE` to find Google Cloud resources whose state contains
  //   `ACTIVE` as a word.
  // * `NOT state:ACTIVE` to find Google Cloud resources whose state doesn't
  //   contain `ACTIVE` as a word.
  // * `createTime<1609459200` to find Google Cloud resources that were created
  //   before `2021-01-01 00:00:00 UTC`. `1609459200` is the epoch timestamp of
  //   `2021-01-01 00:00:00 UTC` in seconds.
  // * `updateTime>1609459200` to find Google Cloud resources that were updated
  //   after `2021-01-01 00:00:00 UTC`. `1609459200` is the epoch timestamp of
  //   `2021-01-01 00:00:00 UTC` in seconds.
  // * `Important` to find Google Cloud resources that contain `Important` as a
  //   word in any of the searchable fields.
  // * `Impor*` to find Google Cloud resources that contain `Impor` as a prefix
  //   of any word in any of the searchable fields.
  // * `Important location:(us-west1 OR global)` to find Google Cloud
  //   resources that contain `Important` as a word in any of the searchable
  //   fields and are also located in the `us-west1` region or the `global`
  //   location.
  string query = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of asset types that this request searches for. If empty,
  // it will search all the asset types [supported by search
  // APIs](https://cloud.google.com/asset-inventory/docs/supported-asset-types).
  //
  // Regular expressions are also supported. For example:
  //
  // * "compute.googleapis.com.*" snapshots resources whose asset type starts
  // with "compute.googleapis.com".
  // * ".*Instance" snapshots resources whose asset type ends with "Instance".
  // * ".*Instance.*" snapshots resources whose asset type contains "Instance".
  //
  // See [RE2](https://github.com/google/re2/wiki/Syntax) for all supported
  // regular expression syntax. If the regular expression does not match any
  // supported asset type, an INVALID_ARGUMENT error will be returned.
  repeated string asset_types = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page size for search result pagination. Page size is capped
  // at 500 even if a larger value is given. If set to zero or a negative value,
  // server will pick an appropriate default. Returned results may be fewer than
  // requested. When this happens, there could be more results as long as
  // `next_page_token` is returned.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If present, then retrieve the next batch of results from the
  // preceding call to this method. `page_token` must be the value of
  // `next_page_token` from the previous response. The values of all other
  // method parameters, must be identical to those in the previous call.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A comma-separated list of fields specifying the sorting order of
  // the results. The default order is ascending. Add " DESC" after the field
  // name to indicate descending order. Redundant space characters are ignored.
  // Example: "location DESC, name".
  // Only the following fields in the response are sortable:
  //
  //   * name
  //   * assetType
  //   * project
  //   * displayName
  //   * description
  //   * location
  //   * createTime
  //   * updateTime
  //   * state
  //   * parentFullResourceName
  //   * parentAssetType
  string order_by = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A comma-separated list of fields that you want returned in the
  // results. The following fields are returned by default if not specified:
  //
  //   * `name`
  //   * `assetType`
  //   * `project`
  //   * `folders`
  //   * `organization`
  //   * `displayName`
  //   * `description`
  //   * `location`
  //   * `labels`
  //   * `tags`
  //   * `effectiveTags`
  //   * `networkTags`
  //   * `kmsKeys`
  //   * `createTime`
  //   * `updateTime`
  //   * `state`
  //   * `additionalAttributes`
  //   * `parentFullResourceName`
  //   * `parentAssetType`
  //
  // Some fields of large size, such as `versionedResources`,
  // `attachedResources`, `effectiveTags` etc., are not returned by default, but
  // you can specify them in the `read_mask` parameter if you want to include
  // them. If `"*"` is specified, all [available
  // fields](https://cloud.google.com/asset-inventory/docs/reference/rest/v1/TopLevel/searchAllResources#resourcesearchresult)
  // are returned.
  // Examples: `"name,location"`, `"name,versionedResources"`, `"*"`.
  // Any invalid field path will trigger INVALID_ARGUMENT error.
  google.protobuf.FieldMask read_mask = 8
      [(google.api.field_behavior) = OPTIONAL];
}

// Search all resources response.
message SearchAllResourcesResponse {
  // A list of Resources that match the search query. It contains the resource
  // standard metadata information.
  repeated ResourceSearchResult results = 1;

  // If there are more results than those appearing in this response, then
  // `next_page_token` is included. To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Search all IAM policies request.
message SearchAllIamPoliciesRequest {
  // Required. A scope can be a project, a folder, or an organization. The
  // search is limited to the IAM policies within the `scope`. The caller must
  // be granted the
  // [`cloudasset.assets.searchAllIamPolicies`](https://cloud.google.com/asset-inventory/docs/access-control#required_permissions)
  // permission on the desired scope.
  //
  // The allowed values are:
  //
  // * projects/{PROJECT_ID} (e.g., "projects/foo-bar")
  // * projects/{PROJECT_NUMBER} (e.g., "projects/12345678")
  // * folders/{FOLDER_NUMBER} (e.g., "folders/1234567")
  // * organizations/{ORGANIZATION_NUMBER} (e.g., "organizations/123456")
  string scope = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The query statement. See [how to construct a
  // query](https://cloud.google.com/asset-inventory/docs/searching-iam-policies#how_to_construct_a_query)
  // for more information. If not specified or empty, it will search all the
  // IAM policies within the specified `scope`. Note that the query string is
  // compared against each IAM policy binding, including its principals,
  // roles, and IAM conditions. The returned IAM policies will only
  // contain the bindings that match your query. To learn more about the IAM
  // policy structure, see the [IAM policy
  // documentation](https://cloud.google.com/iam/help/allow-policies/structure).
  //
  // Examples:
  //
  // * `policy:<EMAIL>` to find IAM policy bindings that specify user
  //   "<EMAIL>".
  // * `policy:roles/compute.admin` to find IAM policy bindings that specify
  //   the Compute Admin role.
  // * `policy:comp*` to find IAM policy bindings that contain "comp" as a
  //   prefix of any word in the binding.
  // * `policy.role.permissions:storage.buckets.update` to find IAM policy
  //   bindings that specify a role containing "storage.buckets.update"
  //   permission. Note that if callers don't have `iam.roles.get` access to a
  //   role's included permissions, policy bindings that specify this role will
  //   be dropped from the search results.
  // * `policy.role.permissions:upd*` to find IAM policy bindings that specify a
  //   role containing "upd" as a prefix of any word in the role permission.
  //   Note that if callers don't have `iam.roles.get` access to a role's
  //   included permissions, policy bindings that specify this role will be
  //   dropped from the search results.
  // * `resource:organizations/123456` to find IAM policy bindings
  //   that are set on "organizations/123456".
  // * `resource=//cloudresourcemanager.googleapis.com/projects/myproject` to
  //   find IAM policy bindings that are set on the project named "myproject".
  // * `Important` to find IAM policy bindings that contain "Important" as a
  //   word in any of the searchable fields (except for the included
  //   permissions).
  // * `resource:(instance1 OR instance2) policy:amy` to find
  //   IAM policy bindings that are set on resources "instance1" or
  //   "instance2" and also specify user "amy".
  // * `roles:roles/compute.admin` to find IAM policy bindings that specify the
  //   Compute Admin role.
  // * `memberTypes:user` to find IAM policy bindings that contain the
  //   principal type "user".
  string query = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page size for search result pagination. Page size is capped
  // at 500 even if a larger value is given. If set to zero or a negative value,
  // server will pick an appropriate default. Returned results may be fewer than
  // requested. When this happens, there could be more results as long as
  // `next_page_token` is returned.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If present, retrieve the next batch of results from the preceding
  // call to this method. `page_token` must be the value of `next_page_token`
  // from the previous response. The values of all other method parameters must
  // be identical to those in the previous call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of asset types that the IAM policies are attached to. If
  // empty, it will search the IAM policies that are attached to all the asset
  // types [supported by search
  // APIs](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
  //
  // Regular expressions are also supported. For example:
  //
  // * "compute.googleapis.com.*" snapshots IAM policies attached to asset type
  // starts with "compute.googleapis.com".
  // * ".*Instance" snapshots IAM policies attached to asset type ends with
  // "Instance".
  // * ".*Instance.*" snapshots IAM policies attached to asset type contains
  // "Instance".
  //
  // See [RE2](https://github.com/google/re2/wiki/Syntax) for all supported
  // regular expression syntax. If the regular expression does not match any
  // supported asset type, an INVALID_ARGUMENT error will be returned.
  repeated string asset_types = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A comma-separated list of fields specifying the sorting order of
  // the results. The default order is ascending. Add " DESC" after the field
  // name to indicate descending order. Redundant space characters are ignored.
  // Example: "assetType DESC, resource".
  // Only singular primitive fields in the response are sortable:
  //   * resource
  //   * assetType
  //   * project
  // All the other fields such as repeated fields (e.g., `folders`) and
  // non-primitive fields (e.g., `policy`) are not supported.
  string order_by = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Search all IAM policies response.
message SearchAllIamPoliciesResponse {
  // A list of IAM policies that match the search query. Related information
  // such as the associated resource is returned along with the policy.
  repeated IamPolicySearchResult results = 1;

  // Set if there are more results than those appearing in this response; to get
  // the next set of results, call this method again, using this value as the
  // `page_token`.
  string next_page_token = 2;
}

// IAM policy analysis query message.
message IamPolicyAnalysisQuery {
  // Specifies the resource to analyze for access policies, which may be set
  // directly on the resource, or on ancestors such as organizations, folders or
  // projects.
  message ResourceSelector {
    // Required. The [full resource name]
    // (https://cloud.google.com/asset-inventory/docs/resource-name-format)
    // of a resource of [supported resource
    // types](https://cloud.google.com/asset-inventory/docs/supported-asset-types#analyzable_asset_types).
    string full_resource_name = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Specifies an identity for which to determine resource access, based on
  // roles assigned either directly to them or to the groups they belong to,
  // directly or indirectly.
  message IdentitySelector {
    // Required. The identity appear in the form of principals in
    // [IAM policy
    // binding](https://cloud.google.com/iam/reference/rest/v1/Binding).
    //
    // The examples of supported forms are:
    // "user:<EMAIL>",
    // "group:<EMAIL>",
    // "domain:google.com",
    // "serviceAccount:<EMAIL>".
    //
    // Notice that wildcard characters (such as * and ?) are not supported.
    // You must give a specific identity.
    string identity = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Specifies roles and/or permissions to analyze, to determine both the
  // identities possessing them and the resources they control. If multiple
  // values are specified, results will include roles or permissions matching
  // any of them. The total number of roles and permissions should be equal or
  // less than 10.
  message AccessSelector {
    // Optional. The roles to appear in result.
    repeated string roles = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The permissions to appear in result.
    repeated string permissions = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Contains query options.
  message Options {
    // Optional. If true, the identities section of the result will expand any
    // Google groups appearing in an IAM policy binding.
    //
    // If
    // [IamPolicyAnalysisQuery.identity_selector][google.cloud.asset.v1.IamPolicyAnalysisQuery.identity_selector]
    // is specified, the identity in the result will be determined by the
    // selector, and this flag is not allowed to set.
    //
    // If true, the default max expansion per group is 1000 for
    // AssetService.AnalyzeIamPolicy][].
    //
    // Default is false.
    bool expand_groups = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true, the access section of result will expand any roles
    // appearing in IAM policy bindings to include their permissions.
    //
    // If
    // [IamPolicyAnalysisQuery.access_selector][google.cloud.asset.v1.IamPolicyAnalysisQuery.access_selector]
    // is specified, the access section of the result will be determined by the
    // selector, and this flag is not allowed to set.
    //
    // Default is false.
    bool expand_roles = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true and
    // [IamPolicyAnalysisQuery.resource_selector][google.cloud.asset.v1.IamPolicyAnalysisQuery.resource_selector]
    // is not specified, the resource section of the result will expand any
    // resource attached to an IAM policy to include resources lower in the
    // resource hierarchy.
    //
    // For example, if the request analyzes for which resources user A has
    // permission P, and the results include an IAM policy with P on a Google
    // Cloud folder, the results will also include resources in that folder with
    // permission P.
    //
    // If true and
    // [IamPolicyAnalysisQuery.resource_selector][google.cloud.asset.v1.IamPolicyAnalysisQuery.resource_selector]
    // is specified, the resource section of the result will expand the
    // specified resource to include resources lower in the resource hierarchy.
    // Only project or lower resources are supported. Folder and organization
    // resources cannot be used together with this option.
    //
    // For example, if the request analyzes for which users have permission P on
    // a Google Cloud project with this option enabled, the results will include
    // all users who have permission P on that project or any lower resource.
    //
    // If true, the default max expansion per resource is 1000 for
    // AssetService.AnalyzeIamPolicy][] and 100000 for
    // AssetService.AnalyzeIamPolicyLongrunning][].
    //
    // Default is false.
    bool expand_resources = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true, the result will output the relevant parent/child
    // relationships between resources. Default is false.
    bool output_resource_edges = 4 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true, the result will output the relevant membership
    // relationships between groups and other groups, and between groups and
    // principals. Default is false.
    bool output_group_edges = 5 [(google.api.field_behavior) = OPTIONAL];

    // Optional. If true, the response will include access analysis from
    // identities to resources via service account impersonation. This is a very
    // expensive operation, because many derived queries will be executed. We
    // highly recommend you use
    // [AssetService.AnalyzeIamPolicyLongrunning][google.cloud.asset.v1.AssetService.AnalyzeIamPolicyLongrunning]
    // RPC instead.
    //
    // For example, if the request analyzes for which resources user A has
    // permission P, and there's an IAM policy states user A has
    // iam.serviceAccounts.getAccessToken permission to a service account SA,
    // and there's another IAM policy states service account SA has permission P
    // to a Google Cloud folder F, then user A potentially has access to the
    // Google Cloud folder F. And those advanced analysis results will be
    // included in
    // [AnalyzeIamPolicyResponse.service_account_impersonation_analysis][google.cloud.asset.v1.AnalyzeIamPolicyResponse.service_account_impersonation_analysis].
    //
    // Another example, if the request analyzes for who has
    // permission P to a Google Cloud folder F, and there's an IAM policy states
    // user A has iam.serviceAccounts.actAs permission to a service account SA,
    // and there's another IAM policy states service account SA has permission P
    // to the Google Cloud folder F, then user A potentially has access to the
    // Google Cloud folder F. And those advanced analysis results will be
    // included in
    // [AnalyzeIamPolicyResponse.service_account_impersonation_analysis][google.cloud.asset.v1.AnalyzeIamPolicyResponse.service_account_impersonation_analysis].
    //
    // Only the following permissions are considered in this analysis:
    //
    // * `iam.serviceAccounts.actAs`
    // * `iam.serviceAccounts.signBlob`
    // * `iam.serviceAccounts.signJwt`
    // * `iam.serviceAccounts.getAccessToken`
    // * `iam.serviceAccounts.getOpenIdToken`
    // * `iam.serviceAccounts.implicitDelegation`
    //
    // Default is false.
    bool analyze_service_account_impersonation = 6
        [(google.api.field_behavior) = OPTIONAL];
  }

  // The IAM conditions context.
  message ConditionContext {
    // The IAM conditions time context.
    oneof TimeContext {
      // The hypothetical access timestamp to evaluate IAM conditions. Note that
      // this value must not be earlier than the current time; otherwise, an
      // INVALID_ARGUMENT error will be returned.
      google.protobuf.Timestamp access_time = 1;
    }
  }

  // Required. The relative name of the root asset. Only resources and IAM
  // policies within the scope will be analyzed.
  //
  // This can only be an organization number (such as "organizations/123"), a
  // folder number (such as "folders/123"), a project ID (such as
  // "projects/my-project-id"), or a project number (such as "projects/12345").
  //
  // To know how to get organization ID, visit [here
  // ](https://cloud.google.com/resource-manager/docs/creating-managing-organization#retrieving_your_organization_id).
  //
  // To know how to get folder or project ID, visit [here
  // ](https://cloud.google.com/resource-manager/docs/creating-managing-folders#viewing_or_listing_folders_and_projects).
  string scope = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Specifies a resource for analysis.
  ResourceSelector resource_selector = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies an identity for analysis.
  IdentitySelector identity_selector = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies roles or permissions for analysis. This is optional.
  AccessSelector access_selector = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The query options.
  Options options = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The hypothetical context for IAM conditions evaluation.
  ConditionContext condition_context = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// A request message for
// [AssetService.AnalyzeIamPolicy][google.cloud.asset.v1.AssetService.AnalyzeIamPolicy].
message AnalyzeIamPolicyRequest {
  // Required. The request query.
  IamPolicyAnalysisQuery analysis_query = 1
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The name of a saved query, which must be in the format of:
  //
  // * projects/project_number/savedQueries/saved_query_id
  // * folders/folder_number/savedQueries/saved_query_id
  // * organizations/organization_number/savedQueries/saved_query_id
  //
  // If both `analysis_query` and `saved_analysis_query` are provided, they
  // will be merged together with the `saved_analysis_query` as base and
  // the `analysis_query` as overrides. For more details of the merge behavior,
  // refer to the
  // [MergeFrom](https://developers.google.com/protocol-buffers/docs/reference/cpp/google.protobuf.message#Message.MergeFrom.details)
  // page.
  //
  // Note that you cannot override primitive fields with default value, such as
  // 0 or empty string, etc., because we use proto3, which doesn't support field
  // presence yet.
  string saved_analysis_query = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Amount of time executable has to complete.  See JSON
  // representation of
  // [Duration](https://developers.google.com/protocol-buffers/docs/proto3#json).
  //
  // If this field is set with a value less than the RPC deadline, and the
  // execution of your query hasn't finished in the specified
  // execution timeout,  you will get a response with partial result.
  // Otherwise, your query's execution will continue until the RPC deadline.
  // If it's not finished until then, you will get a  DEADLINE_EXCEEDED error.
  //
  // Default is empty.
  google.protobuf.Duration execution_timeout = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// A response message for
// [AssetService.AnalyzeIamPolicy][google.cloud.asset.v1.AssetService.AnalyzeIamPolicy].
message AnalyzeIamPolicyResponse {
  // An analysis message to group the query and results.
  message IamPolicyAnalysis {
    // The analysis query.
    IamPolicyAnalysisQuery analysis_query = 1;

    // A list of
    // [IamPolicyAnalysisResult][google.cloud.asset.v1.IamPolicyAnalysisResult]
    // that matches the analysis query, or empty if no result is found.
    repeated IamPolicyAnalysisResult analysis_results = 2;

    // Represents whether all entries in the
    // [analysis_results][google.cloud.asset.v1.AnalyzeIamPolicyResponse.IamPolicyAnalysis.analysis_results]
    // have been fully explored to answer the query.
    bool fully_explored = 3;

    // A list of non-critical errors happened during the query handling.
    repeated IamPolicyAnalysisState non_critical_errors = 5;
  }

  // The main analysis that matches the original request.
  IamPolicyAnalysis main_analysis = 1;

  // The service account impersonation analysis if
  // [AnalyzeIamPolicyRequest.analyze_service_account_impersonation][] is
  // enabled.
  repeated IamPolicyAnalysis service_account_impersonation_analysis = 2;

  // Represents whether all entries in the
  // [main_analysis][google.cloud.asset.v1.AnalyzeIamPolicyResponse.main_analysis]
  // and
  // [service_account_impersonation_analysis][google.cloud.asset.v1.AnalyzeIamPolicyResponse.service_account_impersonation_analysis]
  // have been fully explored to answer the query in the request.
  bool fully_explored = 3;
}

// Output configuration for export IAM policy analysis destination.
message IamPolicyAnalysisOutputConfig {
  // A Cloud Storage location.
  message GcsDestination {
    // Required. The URI of the Cloud Storage object. It's the same URI that is
    // used by gsutil. Example: "gs://bucket_name/object_name". See [Viewing and
    // Editing Object
    // Metadata](https://cloud.google.com/storage/docs/viewing-editing-metadata)
    // for more information.
    //
    // If the specified Cloud Storage object already exists and there is no
    // [hold](https://cloud.google.com/storage/docs/object-holds), it will be
    // overwritten with the analysis result.
    string uri = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // A BigQuery destination.
  message BigQueryDestination {
    // This enum determines the partition key column for the bigquery tables.
    // Partitioning can improve query performance and reduce query cost by
    // filtering partitions. Refer to
    // https://cloud.google.com/bigquery/docs/partitioned-tables for details.
    enum PartitionKey {
      // Unspecified partition key. Tables won't be partitioned using this
      // option.
      PARTITION_KEY_UNSPECIFIED = 0;

      // The time when the request is received. If specified as partition key,
      // the result table(s) is partitoned by the RequestTime column, an
      // additional timestamp column representing when the request was received.
      REQUEST_TIME = 1;
    }

    // Required. The BigQuery dataset in format
    // "projects/projectId/datasets/datasetId", to which the analysis results
    // should be exported. If this dataset does not exist, the export call will
    // return an INVALID_ARGUMENT error.
    string dataset = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The prefix of the BigQuery tables to which the analysis results
    // will be written. Tables will be created based on this table_prefix if not
    // exist:
    // * <table_prefix>_analysis table will contain export operation's metadata.
    // * <table_prefix>_analysis_result will contain all the
    //   [IamPolicyAnalysisResult][google.cloud.asset.v1.IamPolicyAnalysisResult].
    // When [partition_key] is specified, both tables will be partitioned based
    // on the [partition_key].
    string table_prefix = 2 [(google.api.field_behavior) = REQUIRED];

    // The partition key for BigQuery partitioned table.
    PartitionKey partition_key = 3;

    // Optional. Specifies the action that occurs if the destination table or
    // partition already exists. The following values are supported:
    //
    // * WRITE_TRUNCATE: If the table or partition already exists, BigQuery
    // overwrites the entire table or all the partitions data.
    // * WRITE_APPEND: If the table or partition already exists, BigQuery
    // appends the data to the table or the latest partition.
    // * WRITE_EMPTY: If the table already exists and contains data, an error is
    // returned.
    //
    // The default value is WRITE_APPEND. Each action is atomic and only occurs
    // if BigQuery is able to complete the job successfully. Details are at
    // https://cloud.google.com/bigquery/docs/loading-data-local#appending_to_or_overwriting_a_table_using_a_local_file.
    string write_disposition = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // IAM policy analysis export destination.
  oneof destination {
    // Destination on Cloud Storage.
    GcsDestination gcs_destination = 1;

    // Destination on BigQuery.
    BigQueryDestination bigquery_destination = 2;
  }
}

// A request message for
// [AssetService.AnalyzeIamPolicyLongrunning][google.cloud.asset.v1.AssetService.AnalyzeIamPolicyLongrunning].
message AnalyzeIamPolicyLongrunningRequest {
  // Required. The request query.
  IamPolicyAnalysisQuery analysis_query = 1
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The name of a saved query, which must be in the format of:
  //
  // * projects/project_number/savedQueries/saved_query_id
  // * folders/folder_number/savedQueries/saved_query_id
  // * organizations/organization_number/savedQueries/saved_query_id
  //
  // If both `analysis_query` and `saved_analysis_query` are provided, they
  // will be merged together with the `saved_analysis_query` as base and
  // the `analysis_query` as overrides. For more details of the merge behavior,
  // refer to the
  // [MergeFrom](https://developers.google.com/protocol-buffers/docs/reference/cpp/google.protobuf.message#Message.MergeFrom.details)
  // doc.
  //
  // Note that you cannot override primitive fields with default value, such as
  // 0 or empty string, etc., because we use proto3, which doesn't support field
  // presence yet.
  string saved_analysis_query = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. Output configuration indicating where the results will be output
  // to.
  IamPolicyAnalysisOutputConfig output_config = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A response message for
// [AssetService.AnalyzeIamPolicyLongrunning][google.cloud.asset.v1.AssetService.AnalyzeIamPolicyLongrunning].
message AnalyzeIamPolicyLongrunningResponse {}

// A saved query which can be shared with others or used later.
message SavedQuery {
  option (google.api.resource) = {
    type: "cloudasset.googleapis.com/SavedQuery"
    pattern: "projects/{project}/savedQueries/{saved_query}"
    pattern: "folders/{folder}/savedQueries/{saved_query}"
    pattern: "organizations/{organization}/savedQueries/{saved_query}"
  };

  // The query content.
  message QueryContent {
    oneof query_content {
      // An IAM Policy Analysis query, which could be used in
      // the
      // [AssetService.AnalyzeIamPolicy][google.cloud.asset.v1.AssetService.AnalyzeIamPolicy]
      // RPC or the
      // [AssetService.AnalyzeIamPolicyLongrunning][google.cloud.asset.v1.AssetService.AnalyzeIamPolicyLongrunning]
      // RPC.
      IamPolicyAnalysisQuery iam_policy_analysis_query = 1;
    }
  }

  // The resource name of the saved query. The format must be:
  //
  // * projects/project_number/savedQueries/saved_query_id
  // * folders/folder_number/savedQueries/saved_query_id
  // * organizations/organization_number/savedQueries/saved_query_id
  string name = 1;

  // The description of this saved query. This value should be fewer than 255
  // characters.
  string description = 2;

  // Output only. The create time of this saved query.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The account's email address who has created this saved query.
  string creator = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update time of this saved query.
  google.protobuf.Timestamp last_update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The account's email address who has updated this saved query
  // most recently.
  string last_updater = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels applied on the resource.
  // This value should not contain more than 10 entries. The key and value of
  // each entry must be non-empty and fewer than 64 characters.
  map<string, string> labels = 7;

  // The query content.
  QueryContent content = 8;
}

// Request to create a saved query.
message CreateSavedQueryRequest {
  // Required. The name of the project/folder/organization where this
  // saved_query should be created in. It can only be an organization number
  // (such as "organizations/123"), a folder number (such as "folders/123"), a
  // project ID (such as "projects/my-project-id"), or a project number (such as
  // "projects/12345").
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudasset.googleapis.com/SavedQuery"
    }
  ];

  // Required. The saved_query details. The `name` field must be empty as it
  // will be generated based on the parent and saved_query_id.
  SavedQuery saved_query = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the saved query, which must be unique in the
  // specified parent. It will become the final component of the saved query's
  // resource name.
  //
  // This value should be 4-63 characters, and valid characters
  // are `[a-z][0-9]-`.
  //
  // Notice that this field is required in the saved query creation, and the
  // `name` field of the `saved_query` will be ignored.
  string saved_query_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request to get a saved query.
message GetSavedQueryRequest {
  // Required. The name of the saved query and it must be in the format of:
  //
  // * projects/project_number/savedQueries/saved_query_id
  // * folders/folder_number/savedQueries/saved_query_id
  // * organizations/organization_number/savedQueries/saved_query_id
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudasset.googleapis.com/SavedQuery"
    }
  ];
}

// Request to list saved queries.
message ListSavedQueriesRequest {
  // Required. The parent project/folder/organization whose savedQueries are to
  // be listed. It can only be using project/folder/organization number (such as
  // "folders/12345")", or a project ID (such as "projects/my-project-id").
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudasset.googleapis.com/SavedQuery"
    }
  ];

  // Optional. The expression to filter resources.
  // The expression is a list of zero or more restrictions combined via logical
  // operators `AND` and `OR`. When `AND` and `OR` are both used in the
  // expression, parentheses must be appropriately used to group the
  // combinations. The expression may also contain regular expressions.
  //
  // See https://google.aip.dev/160 for more information on the grammar.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of saved queries to return per page. The
  // service may return fewer than this value. If unspecified, at most 50 will
  // be returned. The maximum value is 1000; values above 1000 will be coerced
  // to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListSavedQueries` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListSavedQueries` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response of listing saved queries.
message ListSavedQueriesResponse {
  // A list of savedQueries.
  repeated SavedQuery saved_queries = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request to update a saved query.
message UpdateSavedQueryRequest {
  // Required. The saved query to update.
  //
  // The saved query's `name` field is used to identify the one to update,
  // which has format as below:
  //
  // * projects/project_number/savedQueries/saved_query_id
  // * folders/folder_number/savedQueries/saved_query_id
  // * organizations/organization_number/savedQueries/saved_query_id
  SavedQuery saved_query = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request to delete a saved query.
message DeleteSavedQueryRequest {
  // Required. The name of the saved query to delete. It must be in the format
  // of:
  //
  // * projects/project_number/savedQueries/saved_query_id
  // * folders/folder_number/savedQueries/saved_query_id
  // * organizations/organization_number/savedQueries/saved_query_id
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudasset.googleapis.com/SavedQuery"
    }
  ];
}

// The request message for performing resource move analysis.
message AnalyzeMoveRequest {
  // View enum for supporting partial analysis responses.
  enum AnalysisView {
    // The default/unset value.
    // The API will default to the FULL view.
    ANALYSIS_VIEW_UNSPECIFIED = 0;

    // Full analysis including all level of impacts of the specified resource
    // move.
    FULL = 1;

    // Basic analysis only including blockers which will prevent the specified
    // resource move at runtime.
    BASIC = 2;
  }

  // Required. Name of the resource to perform the analysis against.
  // Only Google Cloud projects are supported as of today. Hence, this can only
  // be a project ID (such as "projects/my-project-id") or a project number
  // (such as "projects/12345").
  string resource = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. Name of the Google Cloud folder or organization to reparent the
  // target resource. The analysis will be performed against hypothetically
  // moving the resource to this specified desitination parent. This can only be
  // a folder number (such as "folders/123") or an organization number (such as
  // "organizations/123").
  string destination_parent = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "*" }
  ];

  // Analysis view indicating what information should be included in the
  // analysis response. If unspecified, the default view is FULL.
  AnalysisView view = 3;
}

// The response message for resource move analysis.
message AnalyzeMoveResponse {
  // The list of analyses returned from performing the intended resource move
  // analysis. The analysis is grouped by different Google Cloud services.
  repeated MoveAnalysis move_analysis = 1;
}

// A message to group the analysis information.
message MoveAnalysis {
  // The user friendly display name of the analysis. E.g. IAM, organization
  // policy etc.
  string display_name = 1;

  oneof result {
    // Analysis result of moving the target resource.
    MoveAnalysisResult analysis = 2;

    // Description of error encountered when performing the analysis.
    google.rpc.Status error = 3;
  }
}

// An analysis result including blockers and warnings.
message MoveAnalysisResult {
  // Blocking information that would prevent the target resource from moving
  // to the specified destination at runtime.
  repeated MoveImpact blockers = 1;

  // Warning information indicating that moving the target resource to the
  // specified destination might be unsafe. This can include important policy
  // information and configuration changes, but will not block moves at runtime.
  repeated MoveImpact warnings = 2;
}

// A message to group impacts of moving the target resource.
message MoveImpact {
  // User friendly impact detail in a free form message.
  string detail = 1;
}

// Output configuration query assets.
message QueryAssetsOutputConfig {
  // BigQuery destination.
  message BigQueryDestination {
    // Required. The BigQuery dataset where the query results will be saved. It
    // has the format of "projects/{projectId}/datasets/{datasetId}".
    string dataset = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The BigQuery table where the query results will be saved. If
    // this table does not exist, a new table with the given name will be
    // created.
    string table = 2 [(google.api.field_behavior) = REQUIRED];

    // Specifies the action that occurs if the destination table or partition
    // already exists. The following values are supported:
    //
    // * WRITE_TRUNCATE: If the table or partition already exists, BigQuery
    // overwrites the entire table or all the partitions data.
    // * WRITE_APPEND: If the table or partition already exists, BigQuery
    // appends the data to the table or the latest partition.
    // * WRITE_EMPTY: If the table already exists and contains data, a
    // 'duplicate' error is returned in the job result.
    //
    // The default value is WRITE_EMPTY.
    string write_disposition = 3;
  }

  // BigQuery destination where the query results will be saved.
  BigQueryDestination bigquery_destination = 1;
}

// QueryAssets request.
message QueryAssetsRequest {
  // Required. The relative name of the root asset. This can only be an
  // organization number (such as "organizations/123"), a project ID (such as
  // "projects/my-project-id"), or a project number (such as "projects/12345"),
  // or a folder number (such as "folders/123").
  //
  // Only assets belonging to the `parent` will be returned.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudasset.googleapis.com/Asset"
    }
  ];

  oneof query {
    // Optional. A SQL statement that's compatible with [BigQuery
    // SQL](https://cloud.google.com/bigquery/docs/introduction-sql).
    string statement = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Reference to the query job, which is from the
    // `QueryAssetsResponse` of previous `QueryAssets` call.
    string job_reference = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The maximum number of rows to return in the results. Responses
  // are limited to 10 MB and 1000 rows.
  //
  // By default, the maximum row count is 1000. When the byte or row count limit
  // is reached, the rest of the query results will be paginated.
  //
  // The field will be ignored when [output_config] is specified.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token received from previous `QueryAssets`.
  //
  // The field will be ignored when [output_config] is specified.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the maximum amount of time that the client is willing
  // to wait for the query to complete. By default, this limit is 5 min for the
  // first query, and 1 minute for the following queries. If the query is
  // complete, the `done` field in the `QueryAssetsResponse` is true, otherwise
  // false.
  //
  // Like BigQuery [jobs.query
  // API](https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/query#queryrequest)
  // The call is not guaranteed to wait for the specified timeout; it typically
  // returns after around 200 seconds (200,000 milliseconds), even if the query
  // is not complete.
  //
  // The field will be ignored when [output_config] is specified.
  google.protobuf.Duration timeout = 6 [(google.api.field_behavior) = OPTIONAL];

  // Specifies what time period or point in time to query asset metadata at.
  // * unset - query asset metadata as it is right now
  // * [read_time_window] - query asset metadata as it was at any point in time
  // between [start_time] and [end_time].
  // * [read_time] - query asset metadata as it was at that point in time.
  // If data for the timestamp/date range selected does not exist,
  // it will simply return a valid response with no rows.
  oneof time {
    // Optional. [start_time] is required. [start_time] must be less than
    // [end_time] Defaults [end_time] to now if [start_time] is set and
    // [end_time] isn't. Maximum permitted time range is 7 days.
    TimeWindow read_time_window = 7 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Queries cloud assets as they appeared at the specified point in
    // time.
    google.protobuf.Timestamp read_time = 8
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. Destination where the query results will be saved.
  //
  // When this field is specified, the query results won't be saved in the
  // [QueryAssetsResponse.query_result]. Instead
  // [QueryAssetsResponse.output_config] will be set.
  //
  // Meanwhile, [QueryAssetsResponse.job_reference] will be set and can be used
  // to check the status of the query job when passed to a following
  // [QueryAssets] API call.
  QueryAssetsOutputConfig output_config = 9
      [(google.api.field_behavior) = OPTIONAL];
}

// QueryAssets response.
message QueryAssetsResponse {
  // Reference to a query job.
  string job_reference = 1;

  // The query response, which can be either an `error` or a valid `response`.
  //
  // If `done` == `false` and the query result is being saved in an output, the
  // output_config field will be set.
  // If `done` == `true`, exactly one of
  // `error`, `query_result` or `output_config` will be set.
  // [done] is unset unless the [QueryAssetsResponse] contains a
  // [QueryAssetsResponse.job_reference].
  bool done = 2;

  oneof response {
    // Error status.
    google.rpc.Status error = 3;

    // Result of the query.
    QueryResult query_result = 4;

    // Output configuration, which indicates that instead of being returned in
    // an API response on the fly, the query result will be saved in a specific
    // output.
    QueryAssetsOutputConfig output_config = 5;
  }
}

// Execution results of the query.
//
// The result is formatted as rows represented by BigQuery compatible [schema].
// When pagination is necessary, it will contains the page token to retrieve
// the results of following pages.
message QueryResult {
  // Each row hold a query result in the format of `Struct`.
  repeated google.protobuf.Struct rows = 1;

  // Describes the format of the [rows].
  TableSchema schema = 2;

  // Token to retrieve the next page of the results.
  string next_page_token = 3;

  // Total rows of the whole query results.
  int64 total_rows = 4;
}

// BigQuery Compatible table schema.
message TableSchema {
  // Describes the fields in a table.
  repeated TableFieldSchema fields = 1;
}

// A field in TableSchema.
message TableFieldSchema {
  // The field name. The name must contain only letters (a-z, A-Z),
  // numbers (0-9), or underscores (_), and must start with a letter or
  // underscore. The maximum length is 128 characters.
  string field = 1;

  // The field data type. Possible values include
  // * STRING
  // * BYTES
  // * INTEGER
  // * FLOAT
  // * BOOLEAN
  // * TIMESTAMP
  // * DATE
  // * TIME
  // * DATETIME
  // * GEOGRAPHY,
  // * NUMERIC,
  // * BIGNUMERIC,
  // * RECORD
  // (where RECORD indicates that the field contains a nested schema).
  string type = 2;

  // The field mode. Possible values include NULLABLE, REQUIRED and
  // REPEATED. The default value is NULLABLE.
  string mode = 3;

  // Describes the nested schema fields if the type property is set
  // to RECORD.
  repeated TableFieldSchema fields = 4;
}

// A request message for
// [AssetService.BatchGetEffectiveIamPolicies][google.cloud.asset.v1.AssetService.BatchGetEffectiveIamPolicies].
message BatchGetEffectiveIamPoliciesRequest {
  // Required. Only IAM policies on or below the scope will be returned.
  //
  // This can only be an organization number (such as "organizations/123"), a
  // folder number (such as "folders/123"), a project ID (such as
  // "projects/my-project-id"), or a project number (such as "projects/12345").
  //
  // To know how to get organization ID, visit [here
  // ](https://cloud.google.com/resource-manager/docs/creating-managing-organization#retrieving_your_organization_id).
  //
  // To know how to get folder or project ID, visit [here
  // ](https://cloud.google.com/resource-manager/docs/creating-managing-folders#viewing_or_listing_folders_and_projects).
  string scope = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "*" }
  ];

  // Required. The names refer to the [full_resource_names]
  // (https://cloud.google.com/asset-inventory/docs/resource-name-format)
  // of the asset types [supported by search
  // APIs](https://cloud.google.com/asset-inventory/docs/supported-asset-types).
  // A maximum of 20 resources' effective policies can be retrieved in a batch.
  repeated string names = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "*" }
  ];
}

// A response message for
// [AssetService.BatchGetEffectiveIamPolicies][google.cloud.asset.v1.AssetService.BatchGetEffectiveIamPolicies].
message BatchGetEffectiveIamPoliciesResponse {
  // The effective IAM policies on one resource.
  message EffectiveIamPolicy {
    // The IAM policy and its attached resource.
    message PolicyInfo {
      // The full resource name the
      // [policy][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.PolicyInfo.policy]
      // is directly attached to.
      string attached_resource = 1;

      // The IAM policy that's directly attached to the
      // [attached_resource][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.PolicyInfo.attached_resource].
      google.iam.v1.Policy policy = 2;
    }

    // The [full_resource_name]
    // (https://cloud.google.com/asset-inventory/docs/resource-name-format)
    // for which the
    // [policies][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.policies]
    // are computed. This is one of the
    // [BatchGetEffectiveIamPoliciesRequest.names][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesRequest.names]
    // the caller provides in the request.
    string full_resource_name = 1;

    // The effective policies for the
    // [full_resource_name][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.full_resource_name].
    //
    // These policies include the policy set on the
    // [full_resource_name][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.full_resource_name]
    // and those set on its parents and ancestors up to the
    // [BatchGetEffectiveIamPoliciesRequest.scope][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesRequest.scope].
    // Note that these policies are not filtered according to the resource type
    // of the
    // [full_resource_name][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.full_resource_name].
    //
    // These policies are hierarchically ordered by
    // [PolicyInfo.attached_resource][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.PolicyInfo.attached_resource]
    // starting from
    // [full_resource_name][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.full_resource_name]
    // itself to its parents and ancestors, such that policies[i]'s
    // [PolicyInfo.attached_resource][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.PolicyInfo.attached_resource]
    // is the child of policies[i+1]'s
    // [PolicyInfo.attached_resource][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.PolicyInfo.attached_resource],
    // if policies[i+1] exists.
    repeated PolicyInfo policies = 2;
  }

  // The effective policies for a batch of resources. Note that the results
  // order is the same as the order of
  // [BatchGetEffectiveIamPoliciesRequest.names][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesRequest.names].
  // When a resource does not have any effective IAM policies, its corresponding
  // policy_result will contain empty
  // [EffectiveIamPolicy.policies][google.cloud.asset.v1.BatchGetEffectiveIamPoliciesResponse.EffectiveIamPolicy.policies].
  repeated EffectiveIamPolicy policy_results = 2;
}

// This organization policy message is a modified version of the one defined in
// the Organization Policy system. This message contains several fields defined
// in the original organization policy with some new fields for analysis
// purpose.
message AnalyzerOrgPolicy {
  // This rule message is a customized version of the one defined in the
  // Organization Policy system. In addition to the fields defined in the
  // original organization policy, it contains additional field(s) under
  // specific circumstances to support analysis results.
  message Rule {
    // The string values for the list constraints.
    message StringValues {
      // List of values allowed at this resource.
      repeated string allowed_values = 1;

      // List of values denied at this resource.
      repeated string denied_values = 2;
    }

    oneof kind {
      // List of values to be used for this policy rule. This field can be set
      // only in policies for list constraints.
      StringValues values = 3;

      // Setting this to true means that all values are allowed. This field can
      // be set only in Policies for list constraints.
      bool allow_all = 4;

      // Setting this to true means that all values are denied. This field can
      // be set only in Policies for list constraints.
      bool deny_all = 5;

      // If `true`, then the `Policy` is enforced. If `false`, then any
      // configuration is acceptable.
      // This field can be set only in Policies for boolean constraints.
      bool enforce = 6;
    }

    // The evaluating condition for this rule.
    google.type.Expr condition = 7;

    // The condition evaluation result for this rule.
    // Only populated if it meets all the following criteria:
    //
    // * There is a
    // [condition][google.cloud.asset.v1.AnalyzerOrgPolicy.Rule.condition]
    // defined for this rule.
    // * This rule is within
    //   [AnalyzeOrgPolicyGovernedContainersResponse.GovernedContainer.consolidated_policy][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedContainersResponse.GovernedContainer.consolidated_policy],
    //   or
    //   [AnalyzeOrgPolicyGovernedAssetsResponse.GovernedAsset.consolidated_policy][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsResponse.GovernedAsset.consolidated_policy]
    //   when the
    //   [AnalyzeOrgPolicyGovernedAssetsResponse.GovernedAsset][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsResponse.GovernedAsset]
    //   has
    //   [AnalyzeOrgPolicyGovernedAssetsResponse.GovernedAsset.governed_resource][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsResponse.GovernedAsset.governed_resource].
    ConditionEvaluation condition_evaluation = 8;
  }

  // The [full resource name]
  // (https://cloud.google.com/asset-inventory/docs/resource-name-format) of
  // an organization/folder/project resource where this organization policy is
  // set.
  //
  // Notice that some type of constraints are defined with default policy. This
  // field will be empty for them.
  string attached_resource = 1;

  // The [full resource name]
  // (https://cloud.google.com/asset-inventory/docs/resource-name-format) of
  // an organization/folder/project resource where this organization policy
  // applies to.
  //
  // For any user defined org policies, this field has the same value as
  // the [attached_resource] field. Only for default policy, this field has
  // the different value.
  string applied_resource = 5;

  // List of rules for this organization policy.
  repeated Rule rules = 2;

  // If `inherit_from_parent` is true, Rules set higher up in the
  // hierarchy (up to the closest root) are inherited and present in the
  // effective policy. If it is false, then no rules are inherited, and this
  // policy becomes the effective root for evaluation.
  bool inherit_from_parent = 3;

  // Ignores policies set above this resource and restores the default behavior
  // of the constraint at this resource.
  // This field can be set in policies for either list or boolean
  // constraints. If set, `rules` must be empty and `inherit_from_parent`
  // must be set to false.
  bool reset = 4;
}

// The organization policy constraint definition.
message AnalyzerOrgPolicyConstraint {
  // The definition of a constraint.
  message Constraint {
    // Specifies the default behavior in the absence of any `Policy` for the
    // `Constraint`. This must not be `CONSTRAINT_DEFAULT_UNSPECIFIED`.
    enum ConstraintDefault {
      // This is only used for distinguishing unset values and should never be
      // used.
      CONSTRAINT_DEFAULT_UNSPECIFIED = 0;

      // Indicate that all values are allowed for list constraints.
      // Indicate that enforcement is off for boolean constraints.
      ALLOW = 1;

      // Indicate that all values are denied for list constraints.
      // Indicate that enforcement is on for boolean constraints.
      DENY = 2;
    }

    // A `Constraint` that allows or disallows a list of string values, which
    // are configured by an organization's policy administrator with a `Policy`.
    message ListConstraint {
      // Indicates whether values grouped into categories can be used in
      // `Policy.allowed_values` and `Policy.denied_values`. For example,
      // `"in:Python"` would match any value in the 'Python' group.
      bool supports_in = 1;

      // Indicates whether subtrees of Cloud Resource Manager resource hierarchy
      // can be used in `Policy.allowed_values` and `Policy.denied_values`. For
      // example, `"under:folders/123"` would match any resource under the
      // 'folders/123' folder.
      bool supports_under = 2;
    }

    // A `Constraint` that is either enforced or not.
    //
    // For example a constraint `constraints/compute.disableSerialPortAccess`.
    // If it is enforced on a VM instance, serial port connections will not be
    // opened to that instance.
    message BooleanConstraint {}

    // The unique name of the constraint. Format of the name should be
    // * `constraints/{constraint_name}`
    //
    // For example, `constraints/compute.disableSerialPortAccess`.
    string name = 1;

    // The human readable name of the constraint.
    string display_name = 2;

    // Detailed description of what this `Constraint` controls as well as how
    // and where it is enforced.
    string description = 3;

    // The evaluation behavior of this constraint in the absence of 'Policy'.
    ConstraintDefault constraint_default = 4;

    // The type of restrictions for this `Constraint`.
    //
    // Immutable after creation.
    oneof constraint_type {
      // Defines this constraint as being a ListConstraint.
      ListConstraint list_constraint = 5;

      // Defines this constraint as being a BooleanConstraint.
      BooleanConstraint boolean_constraint = 6;
    }
  }

  // The definition of a custom constraint.
  message CustomConstraint {
    // The operation in which this constraint will be applied. For example:
    // If the constraint applies only when create VMs, the method_types will be
    // "CREATE" only. If the constraint applied when create or delete VMs, the
    // method_types will be "CREATE" and "DELETE".
    enum MethodType {
      // Unspecified. Will results in user error.
      METHOD_TYPE_UNSPECIFIED = 0;

      // Constraint applied when creating the resource.
      CREATE = 1;

      // Constraint applied when updating the resource.
      UPDATE = 2;

      // Constraint applied when deleting the resource.
      DELETE = 3;
    }

    // Allow or deny type.
    enum ActionType {
      // Unspecified. Will results in user error.
      ACTION_TYPE_UNSPECIFIED = 0;

      // Allowed action type.
      ALLOW = 1;

      // Deny action type.
      DENY = 2;
    }

    // Name of the constraint. This is unique within the organization. Format of
    // the name should be
    // * `organizations/{organization_id}/customConstraints/{custom_constraint_id}`
    //
    // Example :
    // "organizations/123/customConstraints/custom.createOnlyE2TypeVms"
    string name = 1;

    // The Resource Instance type on which this policy applies to. Format will
    // be of the form : "<canonical service name>/<type>" Example:
    //  * `compute.googleapis.com/Instance`.
    repeated string resource_types = 2;

    // All the operations being applied for this constraint.
    repeated MethodType method_types = 3;

    // Organization Policy condition/expression. For example:
    // `resource.instanceName.matches("[production|test]_.*_(\d)+")'` or,
    // `resource.management.auto_upgrade == true`
    string condition = 4;

    // Allow or deny type.
    ActionType action_type = 5;

    // One line display name for the UI.
    string display_name = 6;

    // Detailed information about this custom policy constraint.
    string description = 7;
  }

  oneof constraint_definition {
    // The definition of the canned constraint defined by Google.
    Constraint google_defined_constraint = 1;

    // The definition of the custom constraint.
    CustomConstraint custom_constraint = 2;
  }
}

// A request message for
// [AssetService.AnalyzeOrgPolicies][google.cloud.asset.v1.AssetService.AnalyzeOrgPolicies].
message AnalyzeOrgPoliciesRequest {
  // Required. The organization to scope the request. Only organization
  // policies within the scope will be analyzed.
  //
  // * organizations/{ORGANIZATION_NUMBER} (e.g., "organizations/123456")
  string scope = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the constraint to analyze organization policies for.
  // The response only contains analyzed organization policies for the provided
  // constraint.
  string constraint = 2 [(google.api.field_behavior) = REQUIRED];

  // The expression to filter
  // [AnalyzeOrgPoliciesResponse.org_policy_results][google.cloud.asset.v1.AnalyzeOrgPoliciesResponse.org_policy_results].
  // Filtering is currently available for bare literal values and the following
  // fields:
  // * consolidated_policy.attached_resource
  // * consolidated_policy.rules.enforce
  //
  // When filtering by a specific field, the only supported operator is `=`.
  // For example, filtering by
  // consolidated_policy.attached_resource="//cloudresourcemanager.googleapis.com/folders/001"
  // will return all the Organization Policy results attached to "folders/001".
  string filter = 3;

  // The maximum number of items to return per page. If unspecified,
  // [AnalyzeOrgPoliciesResponse.org_policy_results][google.cloud.asset.v1.AnalyzeOrgPoliciesResponse.org_policy_results]
  // will contain 20 items with a maximum of 200.
  optional int32 page_size = 4;

  // The pagination token to retrieve the next page.
  string page_token = 5;
}

// The response message for
// [AssetService.AnalyzeOrgPolicies][google.cloud.asset.v1.AssetService.AnalyzeOrgPolicies].
message AnalyzeOrgPoliciesResponse {
  // The organization policy result to the query.
  message OrgPolicyResult {
    // The consolidated organization policy for the analyzed resource. The
    // consolidated organization policy is computed by merging and evaluating
    // [AnalyzeOrgPoliciesResponse.policy_bundle][].
    // The evaluation will respect the organization policy [hierarchy
    // rules](https://cloud.google.com/resource-manager/docs/organization-policy/understanding-hierarchy).
    AnalyzerOrgPolicy consolidated_policy = 1;

    // The ordered list of all organization policies from the
    // [AnalyzeOrgPoliciesResponse.OrgPolicyResult.consolidated_policy.attached_resource][].
    // to the scope specified in the request.
    //
    // If the constraint is defined with default policy, it will also appear in
    // the list.
    repeated AnalyzerOrgPolicy policy_bundle = 2;

    // The project that this consolidated policy belongs to, in the format of
    // projects/{PROJECT_NUMBER}. This field is available when the consolidated
    // policy belongs to a project.
    string project = 3;

    // The folder(s) that this consolidated policy belongs to, in the format of
    // folders/{FOLDER_NUMBER}. This field is available when the consolidated
    // policy belongs (directly or cascadingly) to one or more folders.
    repeated string folders = 4;

    // The organization that this consolidated policy belongs to, in the format
    // of organizations/{ORGANIZATION_NUMBER}. This field is available when the
    // consolidated policy belongs (directly or cascadingly) to an organization.
    string organization = 5;
  }

  // The organization policies under the
  // [AnalyzeOrgPoliciesRequest.scope][google.cloud.asset.v1.AnalyzeOrgPoliciesRequest.scope]
  // with the
  // [AnalyzeOrgPoliciesRequest.constraint][google.cloud.asset.v1.AnalyzeOrgPoliciesRequest.constraint].
  repeated OrgPolicyResult org_policy_results = 1;

  // The definition of the constraint in the request.
  AnalyzerOrgPolicyConstraint constraint = 2;

  // The page token to fetch the next page for
  // [AnalyzeOrgPoliciesResponse.org_policy_results][google.cloud.asset.v1.AnalyzeOrgPoliciesResponse.org_policy_results].
  string next_page_token = 3;
}

// A request message for
// [AssetService.AnalyzeOrgPolicyGovernedContainers][google.cloud.asset.v1.AssetService.AnalyzeOrgPolicyGovernedContainers].
message AnalyzeOrgPolicyGovernedContainersRequest {
  // Required. The organization to scope the request. Only organization
  // policies within the scope will be analyzed. The output containers will
  // also be limited to the ones governed by those in-scope organization
  // policies.
  //
  // * organizations/{ORGANIZATION_NUMBER} (e.g., "organizations/123456")
  string scope = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the constraint to analyze governed containers for.
  // The analysis only contains organization policies for the provided
  // constraint.
  string constraint = 2 [(google.api.field_behavior) = REQUIRED];

  // The expression to filter
  // [AnalyzeOrgPolicyGovernedContainersResponse.governed_containers][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedContainersResponse.governed_containers].
  // Filtering is currently available for bare literal values and the following
  // fields:
  // * parent
  // * consolidated_policy.rules.enforce
  //
  // When filtering by a specific field, the only supported operator is `=`.
  // For example, filtering by
  // parent="//cloudresourcemanager.googleapis.com/folders/001"
  // will return all the containers under "folders/001".
  string filter = 3;

  // The maximum number of items to return per page. If unspecified,
  // [AnalyzeOrgPolicyGovernedContainersResponse.governed_containers][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedContainersResponse.governed_containers]
  // will contain 100 items with a maximum of 200.
  optional int32 page_size = 4;

  // The pagination token to retrieve the next page.
  string page_token = 5;
}

// The response message for
// [AssetService.AnalyzeOrgPolicyGovernedContainers][google.cloud.asset.v1.AssetService.AnalyzeOrgPolicyGovernedContainers].
message AnalyzeOrgPolicyGovernedContainersResponse {
  // The organization/folder/project resource governed by organization policies
  // of
  // [AnalyzeOrgPolicyGovernedContainersRequest.constraint][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedContainersRequest.constraint].
  message GovernedContainer {
    // The [full resource name]
    // (https://cloud.google.com/asset-inventory/docs/resource-name-format) of
    // an organization/folder/project resource.
    string full_resource_name = 1;

    // The [full resource name]
    // (https://cloud.google.com/asset-inventory/docs/resource-name-format) of
    // the parent of
    // [AnalyzeOrgPolicyGovernedContainersResponse.GovernedContainer.full_resource_name][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedContainersResponse.GovernedContainer.full_resource_name].
    string parent = 2;

    // The consolidated organization policy for the analyzed resource. The
    // consolidated organization policy is computed by merging and evaluating
    // [AnalyzeOrgPolicyGovernedContainersResponse.GovernedContainer.policy_bundle][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedContainersResponse.GovernedContainer.policy_bundle].
    // The evaluation will respect the organization policy [hierarchy
    // rules](https://cloud.google.com/resource-manager/docs/organization-policy/understanding-hierarchy).
    AnalyzerOrgPolicy consolidated_policy = 3;

    // The ordered list of all organization policies from the
    // [AnalyzeOrgPoliciesResponse.OrgPolicyResult.consolidated_policy.attached_resource][].
    // to the scope specified in the request.
    //
    // If the constraint is defined with default policy, it will also appear in
    // the list.
    repeated AnalyzerOrgPolicy policy_bundle = 4;

    // The project that this resource belongs to, in the format of
    // projects/{PROJECT_NUMBER}. This field is available when the resource
    // belongs to a project.
    string project = 5;

    // The folder(s) that this resource belongs to, in the format of
    // folders/{FOLDER_NUMBER}. This field is available when the resource
    // belongs (directly or cascadingly) to one or more folders.
    repeated string folders = 6;

    // The organization that this resource belongs to, in the format of
    // organizations/{ORGANIZATION_NUMBER}. This field is available when the
    // resource belongs (directly or cascadingly) to an organization.
    string organization = 7;

    // The effective tags on this resource.
    repeated EffectiveTagDetails effective_tags = 8;
  }

  // The list of the analyzed governed containers.
  repeated GovernedContainer governed_containers = 1;

  // The definition of the constraint in the request.
  AnalyzerOrgPolicyConstraint constraint = 2;

  // The page token to fetch the next page for
  // [AnalyzeOrgPolicyGovernedContainersResponse.governed_containers][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedContainersResponse.governed_containers].
  string next_page_token = 3;
}

// A request message for
// [AssetService.AnalyzeOrgPolicyGovernedAssets][google.cloud.asset.v1.AssetService.AnalyzeOrgPolicyGovernedAssets].
message AnalyzeOrgPolicyGovernedAssetsRequest {
  // Required. The organization to scope the request. Only organization
  // policies within the scope will be analyzed. The output assets will
  // also be limited to the ones governed by those in-scope organization
  // policies.
  //
  // * organizations/{ORGANIZATION_NUMBER} (e.g., "organizations/123456")
  string scope = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the constraint to analyze governed assets for. The
  // analysis only contains analyzed organization policies for the provided
  // constraint.
  string constraint = 2 [(google.api.field_behavior) = REQUIRED];

  // The expression to filter
  // [AnalyzeOrgPolicyGovernedAssetsResponse.governed_assets][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsResponse.governed_assets].
  //
  // For governed resources, filtering is currently available for bare literal
  // values and the following fields:
  // * governed_resource.project
  // * governed_resource.folders
  // * consolidated_policy.rules.enforce
  // When filtering by `governed_resource.project` or
  // `consolidated_policy.rules.enforce`, the only supported operator is `=`.
  // When filtering by `governed_resource.folders`, the supported operators
  // are `=` and `:`.
  // For example, filtering by `governed_resource.project="projects/12345678"`
  // will return all the governed resources under "projects/12345678",
  // including the project itself if applicable.
  //
  // For governed IAM policies, filtering is currently available for bare
  // literal values and the following fields:
  // * governed_iam_policy.project
  // * governed_iam_policy.folders
  // * consolidated_policy.rules.enforce
  // When filtering by `governed_iam_policy.project` or
  // `consolidated_policy.rules.enforce`, the only supported operator is `=`.
  // When filtering by `governed_iam_policy.folders`, the supported operators
  // are `=` and `:`.
  // For example, filtering by `governed_iam_policy.folders:"folders/12345678"`
  // will return all the governed IAM policies under "folders/001".
  string filter = 3;

  // The maximum number of items to return per page. If unspecified,
  // [AnalyzeOrgPolicyGovernedAssetsResponse.governed_assets][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsResponse.governed_assets]
  // will contain 100 items with a maximum of 200.
  optional int32 page_size = 4;

  // The pagination token to retrieve the next page.
  string page_token = 5;
}

// The response message for
// [AssetService.AnalyzeOrgPolicyGovernedAssets][google.cloud.asset.v1.AssetService.AnalyzeOrgPolicyGovernedAssets].
message AnalyzeOrgPolicyGovernedAssetsResponse {
  // The Google Cloud resources governed by the organization policies of the
  // [AnalyzeOrgPolicyGovernedAssetsRequest.constraint][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsRequest.constraint].
  message GovernedResource {
    // The [full resource name]
    // (https://cloud.google.com/asset-inventory/docs/resource-name-format) of
    // the Google Cloud resource.
    string full_resource_name = 1;

    // The [full resource name]
    // (https://cloud.google.com/asset-inventory/docs/resource-name-format) of
    // the parent of
    // [AnalyzeOrgPolicyGovernedAssetsResponse.GovernedResource.full_resource_name][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsResponse.GovernedResource.full_resource_name].
    string parent = 2;

    // The project that this resource belongs to, in the format of
    // projects/{PROJECT_NUMBER}. This field is available when the resource
    // belongs to a project.
    string project = 5;

    // The folder(s) that this resource belongs to, in the format of
    // folders/{FOLDER_NUMBER}. This field is available when the resource
    // belongs (directly or cascadingly) to one or more folders.
    repeated string folders = 6;

    // The organization that this resource belongs to, in the format of
    // organizations/{ORGANIZATION_NUMBER}. This field is available when the
    // resource belongs (directly or cascadingly) to an organization.
    string organization = 7;

    // The asset type of the
    // [AnalyzeOrgPolicyGovernedAssetsResponse.GovernedResource.full_resource_name][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsResponse.GovernedResource.full_resource_name]
    // Example:
    // `cloudresourcemanager.googleapis.com/Project`
    // See [Cloud Asset Inventory Supported Asset
    // Types](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
    // for all supported asset types.
    string asset_type = 8;

    // The effective tags on this resource.
    repeated EffectiveTagDetails effective_tags = 9;
  }

  // The IAM policies governed by the organization policies of the
  // [AnalyzeOrgPolicyGovernedAssetsRequest.constraint][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsRequest.constraint].
  message GovernedIamPolicy {
    // The full resource name of the resource on which this IAM policy is set.
    // Example:
    // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`.
    // See [Cloud Asset Inventory Resource Name
    // Format](https://cloud.google.com/asset-inventory/docs/resource-name-format)
    // for more information.
    string attached_resource = 1;

    // The IAM policy directly set on the given resource.
    google.iam.v1.Policy policy = 2;

    // The project that this IAM policy belongs to, in the format of
    // projects/{PROJECT_NUMBER}. This field is available when the IAM policy
    // belongs to a project.
    string project = 5;

    // The folder(s) that this IAM policy belongs to, in the format of
    // folders/{FOLDER_NUMBER}. This field is available when the IAM policy
    // belongs (directly or cascadingly) to one or more folders.
    repeated string folders = 6;

    // The organization that this IAM policy belongs to, in the format of
    // organizations/{ORGANIZATION_NUMBER}. This field is available when the
    // IAM policy belongs (directly or cascadingly) to an organization.
    string organization = 7;

    // The asset type of the
    // [AnalyzeOrgPolicyGovernedAssetsResponse.GovernedIamPolicy.attached_resource][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsResponse.GovernedIamPolicy.attached_resource].
    // Example:
    // `cloudresourcemanager.googleapis.com/Project`
    // See [Cloud Asset Inventory Supported Asset
    // Types](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
    // for all supported asset types.
    string asset_type = 8;
  }

  // Represents a Google Cloud asset(resource or IAM policy) governed by the
  // organization policies of the
  // [AnalyzeOrgPolicyGovernedAssetsRequest.constraint][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsRequest.constraint].
  message GovernedAsset {
    oneof governed_asset {
      // A Google Cloud resource governed by the organization
      // policies of the
      // [AnalyzeOrgPolicyGovernedAssetsRequest.constraint][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsRequest.constraint].
      GovernedResource governed_resource = 1;

      // An IAM policy governed by the organization
      // policies of the
      // [AnalyzeOrgPolicyGovernedAssetsRequest.constraint][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsRequest.constraint].
      GovernedIamPolicy governed_iam_policy = 2;
    }

    // The consolidated policy for the analyzed asset. The consolidated
    // policy is computed by merging and evaluating
    // [AnalyzeOrgPolicyGovernedAssetsResponse.GovernedAsset.policy_bundle][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsResponse.GovernedAsset.policy_bundle].
    // The evaluation will respect the organization policy [hierarchy
    // rules](https://cloud.google.com/resource-manager/docs/organization-policy/understanding-hierarchy).
    AnalyzerOrgPolicy consolidated_policy = 3;

    // The ordered list of all organization policies from the
    // [AnalyzeOrgPoliciesResponse.OrgPolicyResult.consolidated_policy.attached_resource][]
    // to the scope specified in the request.
    //
    // If the constraint is defined with default policy, it will also appear in
    // the list.
    repeated AnalyzerOrgPolicy policy_bundle = 4;
  }

  // The list of the analyzed governed assets.
  repeated GovernedAsset governed_assets = 1;

  // The definition of the constraint in the request.
  AnalyzerOrgPolicyConstraint constraint = 2;

  // The page token to fetch the next page for
  // [AnalyzeOrgPolicyGovernedAssetsResponse.governed_assets][google.cloud.asset.v1.AnalyzeOrgPolicyGovernedAssetsResponse.governed_assets].
  string next_page_token = 3;
}

// Asset content type.
enum ContentType {
  // Unspecified content type.
  CONTENT_TYPE_UNSPECIFIED = 0;

  // Resource metadata.
  RESOURCE = 1;

  // The actual IAM policy set on a resource.
  IAM_POLICY = 2;

  // The organization policy set on an asset.
  ORG_POLICY = 4;

  // The Access Context Manager policy set on an asset.
  ACCESS_POLICY = 5;

  // The runtime OS Inventory information.
  OS_INVENTORY = 6;

  // The related resources.
  RELATIONSHIP = 7;
}
