// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.asset.v1p5beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/asset/v1p5beta1/assets.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Asset.V1P5Beta1";
option go_package = "cloud.google.com/go/asset/apiv1p5beta1/assetpb;assetpb";
option java_multiple_files = true;
option java_outer_classname = "AssetServiceProto";
option java_package = "com.google.cloud.asset.v1p5beta1";
option php_namespace = "Google\\Cloud\\Asset\\V1p5beta1";

// Asset service definition.
service AssetService {
  option (google.api.default_host) = "cloudasset.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists assets with time and resource types and returns paged results in
  // response.
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse) {
    option (google.api.http) = {
      get: "/v1p5beta1/{parent=*/*}/assets"
    };
    option (google.api.method_signature) = "parent";
  }
}

// ListAssets request.
message ListAssetsRequest {
  // Required. Name of the organization or project the assets belong to. Format:
  // "organizations/[organization-number]" (such as "organizations/123"),
  // "projects/[project-id]" (such as "projects/my-project-id"), or
  // "projects/[project-number]" (such as "projects/12345").
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "cloudasset.googleapis.com/Asset"
    }
  ];

  // Timestamp to take an asset snapshot. This can only be set to a timestamp
  // between the current time and the current time minus 35 days (inclusive).
  // If not specified, the current time will be used. Due to delays in resource
  // data collection and indexing, there is a volatile window during which
  // running the same query may get different results.
  google.protobuf.Timestamp read_time = 2;

  // A list of asset types to take a snapshot for. For example:
  // "compute.googleapis.com/Disk".
  //
  // Regular expression is also supported. For example:
  //
  // * "compute.googleapis.com.*" snapshots resources whose asset type starts
  // with "compute.googleapis.com".
  // * ".*Instance" snapshots resources whose asset type ends with "Instance".
  // * ".*Instance.*" snapshots resources whose asset type contains "Instance".
  //
  // See [RE2](https://github.com/google/re2/wiki/Syntax) for all supported
  // regular expression syntax. If the regular expression does not match any
  // supported asset type, an INVALID_ARGUMENT error will be returned.
  //
  // If specified, only matching assets will be returned, otherwise, it will
  // snapshot all asset types. See [Introduction to Cloud Asset
  // Inventory](https://cloud.google.com/asset-inventory/docs/overview)
  // for all supported asset types.
  repeated string asset_types = 3;

  // Asset content type. If not specified, no content but the asset name will
  // be returned.
  ContentType content_type = 4;

  // The maximum number of assets to be returned in a single response. Default
  // is 100, minimum is 1, and maximum is 1000.
  int32 page_size = 5;

  // The `next_page_token` returned from the previous `ListAssetsResponse`, or
  // unspecified for the first `ListAssetsRequest`. It is a continuation of a
  // prior `ListAssets` call, and the API should return the next page of assets.
  string page_token = 6;
}

// ListAssets response.
message ListAssetsResponse {
  // Time the snapshot was taken.
  google.protobuf.Timestamp read_time = 1;

  // Assets.
  repeated Asset assets = 2;

  // Token to retrieve the next page of results. It expires 72 hours after the
  // page token for the first page is generated. Set to empty if there are no
  // remaining results.
  string next_page_token = 3;
}

// Asset content type.
enum ContentType {
  // Unspecified content type.
  CONTENT_TYPE_UNSPECIFIED = 0;

  // Resource metadata.
  RESOURCE = 1;

  // The actual IAM policy set on a resource.
  IAM_POLICY = 2;

  // The organization policy set on an asset.
  ORG_POLICY = 4;

  // The Access Context Manager policy set on an asset.
  ACCESS_POLICY = 5;
}
