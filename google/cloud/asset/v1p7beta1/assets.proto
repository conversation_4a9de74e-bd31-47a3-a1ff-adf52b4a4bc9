// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.asset.v1p7beta1;

import "google/api/resource.proto";
import "google/cloud/orgpolicy/v1/orgpolicy.proto";
import "google/iam/v1/policy.proto";
import "google/identity/accesscontextmanager/v1/access_level.proto";
import "google/identity/accesscontextmanager/v1/access_policy.proto";
import "google/identity/accesscontextmanager/v1/service_perimeter.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Asset.V1P7Beta1";
option go_package = "cloud.google.com/go/asset/apiv1p7beta1/assetpb;assetpb";
option java_multiple_files = true;
option java_outer_classname = "AssetProto";
option java_package = "com.google.cloud.asset.v1p7beta1";
option php_namespace = "Google\\Cloud\\Asset\\V1p7beta1";

// The Cloud Asset API.

// An asset in Google Cloud. An asset can be any resource in the Google Cloud
// [resource
// hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy),
// a resource outside the Google Cloud resource hierarchy (such as Google
// Kubernetes Engine clusters and objects), or a policy (e.g. IAM policy).
// See [Supported asset
// types](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
// for more information.
message Asset {
  option (google.api.resource) = {
    type: "cloudasset.googleapis.com/Asset"
    pattern: "*"
  };

  // The last update timestamp of an asset. update_time is updated when
  // create/update/delete operation is performed.
  google.protobuf.Timestamp update_time = 11;

  // The full name of the asset. Example:
  // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`
  //
  // See [Resource
  // names](https://cloud.google.com/apis/design/resource_names#full_resource_name)
  // for more information.
  string name = 1;

  // The type of the asset. Example: `compute.googleapis.com/Disk`
  //
  // See [Supported asset
  // types](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
  // for more information.
  string asset_type = 2;

  // A representation of the resource.
  Resource resource = 3;

  // A representation of the IAM policy set on a Google Cloud resource.
  // There can be a maximum of one IAM policy set on any given resource.
  // In addition, IAM policies inherit their granted access scope from any
  // policies set on parent resources in the resource hierarchy. Therefore, the
  // effectively policy is the union of both the policy set on this resource
  // and each policy set on all of the resource's ancestry resource levels in
  // the hierarchy. See
  // [this topic](https://cloud.google.com/iam/help/allow-policies/inheritance)
  // for more information.
  google.iam.v1.Policy iam_policy = 4;

  // A representation of an [organization
  // policy](https://cloud.google.com/resource-manager/docs/organization-policy/overview#organization_policy).
  // There can be more than one organization policy with different constraints
  // set on a given resource.
  repeated google.cloud.orgpolicy.v1.Policy org_policy = 6;

  // A representation of an [access
  // policy](https://cloud.google.com/access-context-manager/docs/overview#access-policies).
  oneof access_context_policy {
    // Please also refer to the [access policy user
    // guide](https://cloud.google.com/access-context-manager/docs/overview#access-policies).
    google.identity.accesscontextmanager.v1.AccessPolicy access_policy = 7;

    // Please also refer to the [access level user
    // guide](https://cloud.google.com/access-context-manager/docs/overview#access-levels).
    google.identity.accesscontextmanager.v1.AccessLevel access_level = 8;

    // Please also refer to the [service perimeter user
    // guide](https://cloud.google.com/vpc-service-controls/docs/overview).
    google.identity.accesscontextmanager.v1.ServicePerimeter service_perimeter =
        9;
  }

  // The related assets of the asset of one relationship type.
  // One asset only represents one type of relationship.
  RelatedAssets related_assets = 13;

  // The ancestry path of an asset in Google Cloud [resource
  // hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy),
  // represented as a list of relative resource names. An ancestry path starts
  // with the closest ancestor in the hierarchy and ends at root. If the asset
  // is a project, folder, or organization, the ancestry path starts from the
  // asset itself.
  //
  // Example: `["projects/123456789", "folders/5432", "organizations/1234"]`
  repeated string ancestors = 10;
}

// A representation of a Google Cloud resource.
message Resource {
  // The API version. Example: `v1`
  string version = 1;

  // The URL of the discovery document containing the resource's JSON schema.
  // Example:
  // `https://www.googleapis.com/discovery/v1/apis/compute/v1/rest`
  //
  // This value is unspecified for resources that do not have an API based on a
  // discovery document, such as Cloud Bigtable.
  string discovery_document_uri = 2;

  // The JSON schema name listed in the discovery document. Example:
  // `Project`
  //
  // This value is unspecified for resources that do not have an API based on a
  // discovery document, such as Cloud Bigtable.
  string discovery_name = 3;

  // The REST URL for accessing the resource. An HTTP `GET` request using this
  // URL returns the resource itself. Example:
  // `https://cloudresourcemanager.googleapis.com/v1/projects/my-project-123`
  //
  // This value is unspecified for resources without a REST API.
  string resource_url = 4;

  // The full name of the immediate parent of this resource. See
  // [Resource
  // Names](https://cloud.google.com/apis/design/resource_names#full_resource_name)
  // for more information.
  //
  // For Google Cloud assets, this value is the parent resource defined in the
  // [IAM policy
  // hierarchy](https://cloud.google.com/iam/docs/overview#policy_hierarchy).
  // Example:
  // `//cloudresourcemanager.googleapis.com/projects/my_project_123`
  //
  // For third-party assets, this field may be set differently.
  string parent = 5;

  // The content of the resource, in which some sensitive fields are removed
  // and may not be present.
  google.protobuf.Struct data = 6;

  // The location of the resource in Google Cloud, such as its zone and region.
  // For more information, see https://cloud.google.com/about/locations/.
  string location = 8;
}

// The detailed related assets with the `relationship_type`.
message RelatedAssets {
  // The detailed relation attributes.
  RelationshipAttributes relationship_attributes = 1;

  // The peer resources of the relationship.
  repeated RelatedAsset assets = 2;
}

// The relationship attributes which include  `type`, `source_resource_type`,
// `target_resource_type` and `action`.
message RelationshipAttributes {
  // The unique identifier of the relationship type. Example:
  // `INSTANCE_TO_INSTANCEGROUP`
  string type = 4;

  // The source asset type. Example: `compute.googleapis.com/Instance`
  string source_resource_type = 1;

  // The target asset type. Example: `compute.googleapis.com/Disk`
  string target_resource_type = 2;

  // The detail of the relationship, e.g. `contains`, `attaches`
  string action = 3;
}

// An asset identify in Google Cloud which contains its name, type and
// ancestors. An asset can be any resource in the Google Cloud [resource
// hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy),
// a resource outside the Google Cloud resource hierarchy (such as Google
// Kubernetes Engine clusters and objects), or a policy (e.g. IAM policy).
// See [Supported asset
// types](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
// for more information.
message RelatedAsset {
  // The full name of the asset. Example:
  // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`
  //
  // See [Resource
  // names](https://cloud.google.com/apis/design/resource_names#full_resource_name)
  // for more information.
  string asset = 1 [(google.api.resource_reference) = {
    type: "cloudasset.googleapis.com/Asset"
  }];

  // The type of the asset. Example: `compute.googleapis.com/Disk`
  //
  // See [Supported asset
  // types](https://cloud.google.com/asset-inventory/docs/supported-asset-types)
  // for more information.
  string asset_type = 2;

  // The ancestors of an asset in Google Cloud [resource
  // hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy),
  // represented as a list of relative resource names. An ancestry path starts
  // with the closest ancestor in the hierarchy and ends at root.
  //
  // Example: `["projects/123456789", "folders/5432", "organizations/1234"]`
  repeated string ancestors = 3;
}
