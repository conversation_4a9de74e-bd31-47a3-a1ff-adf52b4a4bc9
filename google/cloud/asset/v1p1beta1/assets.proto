// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.asset.v1p1beta1;

import "google/iam/v1/policy.proto";

option csharp_namespace = "Google.Cloud.Asset.V1P1Beta1";
option go_package = "cloud.google.com/go/asset/apiv1p1beta1/assetpb;assetpb";
option java_multiple_files = true;
option java_outer_classname = "AssetProto";
option java_package = "com.google.cloud.asset.v1p1beta1";
option php_namespace = "Google\\Cloud\\Asset\\V1p1beta1";

// The standard metadata of a cloud resource.
message StandardResourceMetadata {
  // The full resource name. For example:
  // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`.
  // See [Resource
  // Names](https://cloud.google.com/apis/design/resource_names#full_resource_name)
  // for more information.
  string name = 1;

  // The type of this resource.
  // For example: "compute.googleapis.com/Disk".
  string asset_type = 2;

  // The project that this resource belongs to, in the form of
  // `projects/{project_number}`.
  string project = 3;

  // The display name of this resource.
  string display_name = 4;

  // One or more paragraphs of text description of this resource. Maximum length
  // could be up to 1M bytes.
  string description = 5;

  // Additional searchable attributes of this resource.
  // Informational only. The exact set of attributes is subject to change.
  // For example: project id, DNS name etc.
  repeated string additional_attributes = 10;

  // Location can be "global", regional like "us-east1", or zonal like
  // "us-west1-b".
  string location = 11;

  // Labels associated with this resource. See [Labelling and grouping Google
  // Cloud
  // resources](https://cloud.google.com/blog/products/gcp/labelling-and-grouping-your-google-cloud-platform-resources)
  // for more information.
  map<string, string> labels = 12;

  // Network tags associated with this resource. Like labels, network tags are a
  // type of annotations used to group Google Cloud resources. See [Labelling
  // Google Cloud
  // resources](lhttps://cloud.google.com/blog/products/gcp/labelling-and-grouping-your-google-cloud-platform-resources)
  // for more information.
  repeated string network_tags = 13;
}

// The result for an IAM policy search.
message IamPolicySearchResult {
  // Explanation about the IAM policy search result.
  message Explanation {
    // The map from roles to their included permission matching the permission
    // query (e.g. containing `policy.role.permissions:`). Example role string:
    // "roles/compute.instanceAdmin". The roles can also be found in the
    // returned `policy` bindings. Note that the map is populated only if
    // requesting with a permission query.
    map<string, Permissions> matched_permissions = 1;
  }

  // The
  // [full resource
  // name](https://cloud.google.com/apis/design/resource_names#full_resource_name)
  // of the resource associated with this IAM policy.
  string resource = 1;

  // The project that the associated Google Cloud resource belongs to, in the
  // form of `projects/{project_number}`. If an IAM policy is set on a resource
  // -- such as a Compute Engine instance or a Cloud Storage bucket -- the
  // project field will indicate the project that contains the resource. If an
  // IAM policy is set on a folder or orgnization, the project field will be
  // empty.
  string project = 3;

  // The IAM policy attached to the specified resource. Note that the original
  // IAM policy can contain multiple bindings. This only contains the bindings
  // that match the given query. For queries that don't contain a constraint on
  // policies (e.g. an empty query), this contains all the bindings.
  google.iam.v1.Policy policy = 4;

  // Explanation about the IAM policy search result. It contains additional
  // information that explains why the search result matches the query.
  Explanation explanation = 5;
}

// IAM permissions.
message Permissions {
  // A list of permissions. Example permission string: "compute.disk.get".
  repeated string permissions = 1;
}
