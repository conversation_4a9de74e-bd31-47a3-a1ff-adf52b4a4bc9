// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.asset.v1p1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/asset/v1p1beta1/assets.proto";

option csharp_namespace = "Google.Cloud.Asset.V1P1Beta1";
option go_package = "cloud.google.com/go/asset/apiv1p1beta1/assetpb;assetpb";
option java_multiple_files = true;
option java_outer_classname = "AssetServiceProto";
option java_package = "com.google.cloud.asset.v1p1beta1";
option php_namespace = "Google\\Cloud\\Asset\\V1p1beta1";

// Asset service definition.
service AssetService {
  option (google.api.default_host) = "cloudasset.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Searches all the resources within a given accessible Resource Manager scope
  // (project/folder/organization). This RPC gives callers especially
  // administrators the ability to search all the resources within a scope, even
  // if they don't have `.get` permission of all the resources. Callers should
  // have `cloudasset.assets.searchAllResources` permission on the requested
  // scope, otherwise the request will be rejected.
  rpc SearchAllResources(SearchAllResourcesRequest)
      returns (SearchAllResourcesResponse) {
    option (google.api.http) = {
      get: "/v1p1beta1/{scope=*/*}/resources:searchAll"
    };
    option (google.api.method_signature) = "scope,query,asset_types";
  }

  // Searches all the IAM policies within a given accessible Resource Manager
  // scope (project/folder/organization). This RPC gives callers especially
  // administrators the ability to search all the IAM policies within a scope,
  // even if they don't have `.getIamPolicy` permission of all the IAM policies.
  // Callers should have `cloudasset.assets.searchAllIamPolicies` permission on
  // the requested scope, otherwise the request will be rejected.
  rpc SearchAllIamPolicies(SearchAllIamPoliciesRequest)
      returns (SearchAllIamPoliciesResponse) {
    option (google.api.http) = {
      get: "/v1p1beta1/{scope=*/*}/iamPolicies:searchAll"
    };
    option (google.api.method_signature) = "scope,query";
  }
}

// Search all resources request.
message SearchAllResourcesRequest {
  // Required. The relative name of an asset. The search is limited to the
  // resources within the `scope`. The allowed value must be:
  //
  // * Organization number (such as "organizations/123")
  // * Folder number (such as "folders/1234")
  // * Project number (such as "projects/12345")
  // * Project ID (such as "projects/abc")
  string scope = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The query statement.
  string query = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of asset types that this request searches for. If empty,
  // it will search all the supported asset types.
  repeated string asset_types = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page size for search result pagination. Page size is capped
  // at 500 even if a larger value is given. If set to zero, server will pick an
  // appropriate default. Returned results may be fewer than requested. When
  // this happens, there could be more results as long as `next_page_token` is
  // returned.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If present, then retrieve the next batch of results from the
  // preceding call to this method.  `page_token` must be the value of
  // `next_page_token` from the previous response. The values of all other
  // method parameters, must be identical to those in the previous call.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A comma separated list of fields specifying the sorting order of
  // the results. The default order is ascending. Add ` DESC` after the field
  // name to indicate descending order. Redundant space characters are ignored.
  // For example, `  location DESC ,  name  `.
  string order_by = 10 [(google.api.field_behavior) = OPTIONAL];
}

// Search all resources response.
message SearchAllResourcesResponse {
  // A list of resource that match the search query.
  repeated StandardResourceMetadata results = 1;

  // If there are more results than those appearing in this response, then
  // `next_page_token` is included.  To get the next set of results, call this
  // method again using the value of `next_page_token` as `page_token`.
  string next_page_token = 2;
}

// Search all IAM policies request.
message SearchAllIamPoliciesRequest {
  // Required. The relative name of an asset. The search is limited to the
  // resources within the `scope`. The allowed value must be:
  //
  // * Organization number (such as "organizations/123")
  // * Folder number (such as "folders/1234")
  // * Project number (such as "projects/12345")
  // * Project ID (such as "projects/abc")
  string scope = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The query statement. Examples:
  //
  // * "policy:<EMAIL>"
  // * "policy:(<EMAIL> viewer)"
  string query = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The page size for search result pagination. Page size is capped
  // at 500 even if a larger value is given. If set to zero, server will pick an
  // appropriate default. Returned results may be fewer than requested. When
  // this happens, there could be more results as long as `next_page_token` is
  // returned.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If present, retrieve the next batch of results from the preceding
  // call to this method. `page_token` must be the value of `next_page_token`
  // from the previous response. The values of all other method parameters must
  // be identical to those in the previous call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Search all IAM policies response.
message SearchAllIamPoliciesResponse {
  // A list of IAM policies that match the search query. Related information
  // such as the associated resource is returned along with the policy.
  repeated IamPolicySearchResult results = 1;

  // Set if there are more results than those appearing in this response; to get
  // the next set of results, call this method again, using this value as the
  // `page_token`.
  string next_page_token = 2;
}
