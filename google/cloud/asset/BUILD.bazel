# This build file includes a target for the Ruby wrapper library for
# google-cloud-asset.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudasset.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "cloudasset_ruby_wrapper",
    srcs = ["//google/cloud/asset/v1:asset_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-asset",
        "ruby-cloud-env-prefix=ASSET",
        "ruby-cloud-wrapper-of=v1:0.29",
        "ruby-cloud-product-url=https://cloud.google.com/asset-inventory/",
        "ruby-cloud-api-id=cloudasset.googleapis.com",
        "ruby-cloud-api-shortname=cloudasset",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "A metadata inventory service that allows you to view, monitor, and analyze all your GCP and Anthos assets across projects and services.",
    ruby_cloud_title = "Cloud Asset",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-asset-ruby",
    deps = [
        ":cloudasset_ruby_wrapper",
    ],
)
