// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.asset.v1p2beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/asset/v1p2beta1/assets.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Asset.V1P2Beta1";
option go_package = "cloud.google.com/go/asset/apiv1p2beta1/assetpb;assetpb";
option java_multiple_files = true;
option java_outer_classname = "AssetServiceProto";
option java_package = "com.google.cloud.asset.v1p2beta1";
option php_namespace = "Google\\Cloud\\Asset\\V1p2beta1";

// Asset service definition.
service AssetService {
  option (google.api.default_host) = "cloudasset.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a feed in a parent project/folder/organization to listen to its
  // asset updates.
  rpc CreateFeed(CreateFeedRequest) returns (Feed) {
    option (google.api.http) = {
      post: "/v1p2beta1/{parent=*/*}/feeds"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details about an asset feed.
  rpc GetFeed(GetFeedRequest) returns (Feed) {
    option (google.api.http) = {
      get: "/v1p2beta1/{name=*/*/feeds/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all asset feeds in a parent project/folder/organization.
  rpc ListFeeds(ListFeedsRequest) returns (ListFeedsResponse) {
    option (google.api.http) = {
      get: "/v1p2beta1/{parent=*/*}/feeds"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates an asset feed configuration.
  rpc UpdateFeed(UpdateFeedRequest) returns (Feed) {
    option (google.api.http) = {
      patch: "/v1p2beta1/{feed.name=*/*/feeds/*}"
      body: "*"
    };
    option (google.api.method_signature) = "feed";
  }

  // Deletes an asset feed.
  rpc DeleteFeed(DeleteFeedRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1p2beta1/{name=*/*/feeds/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// The export asset response. This message is returned by the
// [google.longrunning.Operations.GetOperation][google.longrunning.Operations.GetOperation]
// method in the returned
// [google.longrunning.Operation.response][google.longrunning.Operation.response]
// field.
message ExportAssetsResponse {
  // Time the snapshot was taken.
  google.protobuf.Timestamp read_time = 1;

  // Output configuration indicating where the results were output to.
  OutputConfig output_config = 2;
}

// Batch get assets history response.
message BatchGetAssetsHistoryResponse {
  // A list of assets with valid time windows.
  repeated TemporalAsset assets = 1;
}

// Create asset feed request.
message CreateFeedRequest {
  // Required. The name of the project/folder/organization where this feed
  // should be created in. It can only be an organization number (such as
  // "organizations/123"), a folder number (such as "folders/123"), a project ID
  // (such as "projects/my-project-id"), or a project number (such as
  // "projects/12345").
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. This is the client-assigned asset feed identifier and it needs to
  // be unique under a specific parent project/folder/organization.
  string feed_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The feed details. The field `name` must be empty and it will be
  // generated in the format of: projects/project_number/feeds/feed_id
  // folders/folder_number/feeds/feed_id
  // organizations/organization_number/feeds/feed_id
  Feed feed = 3 [(google.api.field_behavior) = REQUIRED];
}

// Get asset feed request.
message GetFeedRequest {
  // Required. The name of the Feed and it must be in the format of:
  // projects/project_number/feeds/feed_id
  // folders/folder_number/feeds/feed_id
  // organizations/organization_number/feeds/feed_id
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "cloudasset.googleapis.com/Feed" }
  ];
}

// List asset feeds request.
message ListFeedsRequest {
  // Required. The parent project/folder/organization whose feeds are to be
  // listed. It can only be using project/folder/organization number (such as
  // "folders/12345")", or a project ID (such as "projects/my-project-id").
  string parent = 1 [(google.api.field_behavior) = REQUIRED];
}

message ListFeedsResponse {
  // A list of feeds.
  repeated Feed feeds = 1;
}

// Update asset feed request.
message UpdateFeedRequest {
  // Required. The new values of feed details. It must match an existing feed
  // and the field `name` must be in the format of:
  // projects/project_number/feeds/feed_id or
  // folders/folder_number/feeds/feed_id or
  // organizations/organization_number/feeds/feed_id.
  Feed feed = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Only updates the `feed` fields indicated by this mask.
  // The field mask must not be empty, and it must not contain fields that
  // are immutable or only set by the server.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

message DeleteFeedRequest {
  // Required. The name of the feed and it must be in the format of:
  // projects/project_number/feeds/feed_id
  // folders/folder_number/feeds/feed_id
  // organizations/organization_number/feeds/feed_id
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "cloudasset.googleapis.com/Feed" }
  ];
}

// Output configuration for export assets destination.
message OutputConfig {
  // Asset export destination.
  oneof destination {
    // Destination on Cloud Storage.
    GcsDestination gcs_destination = 1;
  }
}

// A Cloud Storage location.
message GcsDestination {
  // Required.
  oneof object_uri {
    // The URI of the Cloud Storage object. It's the same URI that is used by
    // gsutil. For example: "gs://bucket_name/object_name". See [Viewing and
    // Editing Object
    // Metadata](https://cloud.google.com/storage/docs/viewing-editing-metadata)
    // for more information.
    string uri = 1;
  }
}

// Asset content type.
enum ContentType {
  // Unspecified content type.
  CONTENT_TYPE_UNSPECIFIED = 0;

  // Resource metadata.
  RESOURCE = 1;

  // The actual IAM policy set on a resource.
  IAM_POLICY = 2;
}

// A Pub/Sub destination.
message PubsubDestination {
  // The name of the Pub/Sub topic to publish to.
  // For example: `projects/PROJECT_ID/topics/TOPIC_ID`.
  string topic = 1;
}

// Output configuration for asset feed destination.
message FeedOutputConfig {
  // Asset feed destination.
  oneof destination {
    // Destination on Pub/Sub.
    PubsubDestination pubsub_destination = 1;
  }
}

// An asset feed used to export asset updates to a destinations.
// An asset feed filter controls what updates are exported.
// The asset feed must be created within a project, organization, or
// folder. Supported destinations are:
// Cloud Pub/Sub topics.
message Feed {
  option (google.api.resource) = {
    type: "cloudasset.googleapis.com/Feed"
    pattern: "projects/{project}/feeds/{feed}"
    pattern: "folders/{folder}/feeds/{feed}"
    pattern: "organizations/{organization}/feeds/{feed}"
    history: ORIGINALLY_SINGLE_PATTERN
  };

  // Required. The format will be
  // projects/{project_number}/feeds/{client-assigned_feed_identifier} or
  // folders/{folder_number}/feeds/{client-assigned_feed_identifier} or
  // organizations/{organization_number}/feeds/{client-assigned_feed_identifier}
  //
  // The client-assigned feed identifier must be unique within the parent
  // project/folder/organization.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // A list of the full names of the assets to receive updates. You must specify
  // either or both of asset_names and asset_types. Only asset updates matching
  // specified asset_names or asset_types are exported to the feed. For
  // example:
  // `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`.
  // See [Resource
  // Names](https://cloud.google.com/apis/design/resource_names#full_resource_name)
  // for more info.
  repeated string asset_names = 2;

  // A list of types of the assets to receive updates. You must specify either
  // or both of asset_names and asset_types. Only asset updates matching
  // specified asset_names or asset_types are exported to the feed.
  // For example:
  // "compute.googleapis.com/Disk" See [Introduction to Cloud Asset
  // Inventory](https://cloud.google.com/resource-manager/docs/cloud-asset-inventory/overview)
  // for all supported asset types.
  repeated string asset_types = 3;

  // Asset content type. If not specified, no content but the asset name and
  // type will be returned.
  ContentType content_type = 4;

  // Required. Feed output configuration defining where the asset updates are
  // published to.
  FeedOutputConfig feed_output_config = 5
      [(google.api.field_behavior) = REQUIRED];
}
