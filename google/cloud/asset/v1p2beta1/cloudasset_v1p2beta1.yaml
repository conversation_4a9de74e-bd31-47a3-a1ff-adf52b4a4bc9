type: google.api.Service
config_version: 3
name: cloudasset.googleapis.com
title: Cloud Asset API

apis:
- name: google.cloud.asset.v1p2beta1.AssetService
- name: google.longrunning.Operations

documentation:
  summary: |-
    The Cloud Asset API manages the history and inventory of Google Cloud
    resources.
  overview: |-
    # Cloud Asset API

    The Cloud Asset API keeps a history of Google Cloud asset metadata, and
    allows Google Cloud users to download a dump of all asset metadata for the
    resource types listed below within an organization or a project at a given
    timestamp.

    Read more documents here:
    https://cloud.google.com/asset-inventory/docs

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1p2beta1/{name=*/*/operations/*/**}'

authentication:
  rules:
  - selector: 'google.cloud.asset.v1p2beta1.AssetService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
