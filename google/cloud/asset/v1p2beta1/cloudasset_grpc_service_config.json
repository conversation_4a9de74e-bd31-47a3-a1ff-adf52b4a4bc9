{"methodConfig": [{"name": [{"service": "google.cloud.asset.v1p2beta1.AssetService", "method": "ExportAssets"}, {"service": "google.cloud.asset.v1p2beta1.AssetService", "method": "CreateFeed"}, {"service": "google.cloud.asset.v1p2beta1.AssetService", "method": "UpdateFeed"}], "timeout": "60s"}, {"name": [{"service": "google.cloud.asset.v1p2beta1.AssetService", "method": "BatchGetAssetsHistory"}, {"service": "google.cloud.asset.v1p2beta1.AssetService", "method": "GetFeed"}, {"service": "google.cloud.asset.v1p2beta1.AssetService", "method": "ListFeeds"}, {"service": "google.cloud.asset.v1p2beta1.AssetService", "method": "DeleteFeed"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}]}