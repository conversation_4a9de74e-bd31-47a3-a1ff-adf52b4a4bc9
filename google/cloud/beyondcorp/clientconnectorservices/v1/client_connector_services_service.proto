// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.beyondcorp.clientconnectorservices.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BeyondCorp.ClientConnectorServices.V1";
option go_package = "cloud.google.com/go/beyondcorp/clientconnectorservices/apiv1/clientconnectorservicespb;clientconnectorservicespb";
option java_multiple_files = true;
option java_outer_classname = "ClientConnectorServicesServiceProto";
option java_package = "com.google.cloud.beyondcorp.clientconnectorservices.v1";
option php_namespace = "Google\\Cloud\\BeyondCorp\\ClientConnectorServices\\V1";
option ruby_package = "Google::Cloud::BeyondCorp::ClientConnectorServices::V1";

// API Overview:
//
// The `beyondcorp.googleapis.com` service implements the Google Cloud
// BeyondCorp API.
//
// Data Model:
//
// The ClientConnectorServicesService exposes the following resources:
//
// * Client Connector Services, named as follows:
//   `projects/{project_id}/locations/{location_id}/client_connector_services/{client_connector_service_id}`.
service ClientConnectorServicesService {
  option (google.api.default_host) = "beyondcorp.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists ClientConnectorServices in a given project and location.
  rpc ListClientConnectorServices(ListClientConnectorServicesRequest)
      returns (ListClientConnectorServicesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/clientConnectorServices"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ClientConnectorService.
  rpc GetClientConnectorService(GetClientConnectorServiceRequest)
      returns (ClientConnectorService) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clientConnectorServices/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new ClientConnectorService in a given project and location.
  rpc CreateClientConnectorService(CreateClientConnectorServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/clientConnectorServices"
      body: "client_connector_service"
    };
    option (google.api.method_signature) =
        "parent,client_connector_service,client_connector_service_id";
    option (google.longrunning.operation_info) = {
      response_type: "ClientConnectorService"
      metadata_type: "ClientConnectorServiceOperationMetadata"
    };
  }

  // Updates the parameters of a single ClientConnectorService.
  rpc UpdateClientConnectorService(UpdateClientConnectorServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{client_connector_service.name=projects/*/locations/*/clientConnectorServices/*}"
      body: "client_connector_service"
    };
    option (google.api.method_signature) =
        "client_connector_service,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ClientConnectorService"
      metadata_type: "ClientConnectorServiceOperationMetadata"
    };
  }

  // Deletes a single ClientConnectorService.
  rpc DeleteClientConnectorService(DeleteClientConnectorServiceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clientConnectorServices/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "ClientConnectorServiceOperationMetadata"
    };
  }
}

// Message describing ClientConnectorService object.
message ClientConnectorService {
  option (google.api.resource) = {
    type: "beyondcorp.googleapis.com/ClientConnectorService"
    pattern: "projects/{project}/locations/{location}/clientConnectorServices/{client_connector_service}"
  };

  // Settings of how to connect to the ClientGateway.
  // One of the following options should be set.
  message Ingress {
    // The basic ingress config for ClientGateways.
    message Config {
      // The setting used to configure ClientGateways.
      // It is adding routes to the client's routing table
      // after the connection is established.
      message DestinationRoute {
        // Required. The network address of the subnet
        // for which the packet is routed to the ClientGateway.
        string address = 1 [(google.api.field_behavior) = REQUIRED];

        // Required. The network mask of the subnet
        // for which the packet is routed to the ClientGateway.
        string netmask = 2 [(google.api.field_behavior) = REQUIRED];
      }

      // The protocol used to connect to the server.
      enum TransportProtocol {
        // Default value. This value is unused.
        TRANSPORT_PROTOCOL_UNSPECIFIED = 0;

        // TCP protocol.
        TCP = 1;
      }

      // Required. Immutable. The transport protocol used between the client and
      // the server.
      TransportProtocol transport_protocol = 1 [
        (google.api.field_behavior) = REQUIRED,
        (google.api.field_behavior) = IMMUTABLE
      ];

      // Required. The settings used to configure basic ClientGateways.
      repeated DestinationRoute destination_routes = 2
          [(google.api.field_behavior) = REQUIRED];
    }

    oneof ingress_config {
      // The basic ingress config for ClientGateways.
      Config config = 1;
    }
  }

  // The details of the egress info. One of the following options should be set.
  message Egress {
    // The peered VPC owned by the consumer project.
    message PeeredVpc {
      // Required. The name of the peered VPC owned by the consumer project.
      string network_vpc = 1 [(google.api.field_behavior) = REQUIRED];
    }

    oneof destination_type {
      // A VPC from the consumer project.
      PeeredVpc peered_vpc = 1;
    }
  }

  // Represents the different states of a ClientConnectorService.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // ClientConnectorService is being created.
    CREATING = 1;

    // ClientConnectorService is being updated.
    UPDATING = 2;

    // ClientConnectorService is being deleted.
    DELETING = 3;

    // ClientConnectorService is running.
    RUNNING = 4;

    // ClientConnectorService is down and may be restored in the future.
    // This happens when CCFE sends ProjectState = OFF.
    DOWN = 5;

    // ClientConnectorService encountered an error and is in an indeterministic
    // state.
    ERROR = 6;
  }

  // Required. Name of resource. The name is ignored during creation.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. [Output only] Create time stamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update time stamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User-provided name.
  // The display name should follow certain format.
  // * Must be 6 to 30 characters in length.
  // * Can only contain lowercase letters, numbers, and hyphens.
  // * Must start with a letter.
  string display_name = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. The details of the ingress settings.
  Ingress ingress = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. The details of the egress settings.
  Egress egress = 7 [(google.api.field_behavior) = REQUIRED];

  // Output only. The operational state of the ClientConnectorService.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting list of ClientConnectorServices.
message ListClientConnectorServicesRequest {
  // Required. Parent value for ListClientConnectorServicesRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/ClientConnectorService"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing ClientConnectorServices.
message ListClientConnectorServicesResponse {
  // The list of ClientConnectorService.
  repeated ClientConnectorService client_connector_services = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a ClientConnectorService.
message GetClientConnectorServiceRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/ClientConnectorService"
    }
  ];
}

// Message for creating a ClientConnectorService.
message CreateClientConnectorServiceRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/ClientConnectorService"
    }
  ];

  // Optional. User-settable client connector service resource ID.
  //  * Must start with a letter.
  //  * Must contain between 4-63 characters from `/[a-z][0-9]-/`.
  //  * Must end with a number or a letter.
  //
  // A random system generated name will be assigned
  // if not specified by the user.
  string client_connector_service_id = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The resource being created.
  ClientConnectorService client_connector_service = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for updating a ClientConnectorService
message UpdateClientConnectorServiceRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // ClientConnectorService resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  //
  // Mutable fields: display_name.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being updated.
  ClientConnectorService client_connector_service = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set as true, will create the resource if it is not found.
  bool allow_missing = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a ClientConnectorService.
message DeleteClientConnectorServiceRequest {
  // Required. Name of the resource.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/ClientConnectorService"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message ClientConnectorServiceOperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
