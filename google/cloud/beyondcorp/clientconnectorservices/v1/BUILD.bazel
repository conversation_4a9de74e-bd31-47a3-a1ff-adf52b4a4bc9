# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "clientconnectorservices_proto",
    srcs = [
        "client_connector_services_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "clientconnectorservices_proto_with_info",
    deps = [
        ":clientconnectorservices_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

java_proto_library(
    name = "clientconnectorservices_java_proto",
    deps = [":clientconnectorservices_proto"],
)

java_grpc_library(
    name = "clientconnectorservices_java_grpc",
    srcs = [":clientconnectorservices_proto"],
    deps = [":clientconnectorservices_java_proto"],
)

java_gapic_library(
    name = "clientconnectorservices_java_gapic",
    srcs = [":clientconnectorservices_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "beyondcorp-clientconnectorservices_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    test_deps = [
        ":clientconnectorservices_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":clientconnectorservices_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "clientconnectorservices_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.beyondcorp.clientconnectorservices.v1.ClientConnectorServicesServiceClientTest",
    ],
    runtime_deps = [":clientconnectorservices_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-beyondcorp-clientconnectorservices-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":clientconnectorservices_java_gapic",
        ":clientconnectorservices_java_grpc",
        ":clientconnectorservices_java_proto",
        ":clientconnectorservices_proto",
    ],
)

go_proto_library(
    name = "clientconnectorservices_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/beyondcorp/clientconnectorservices/apiv1/clientconnectorservicespb",
    protos = [":clientconnectorservices_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "clientconnectorservices_go_gapic",
    srcs = [":clientconnectorservices_proto_with_info"],
    grpc_service_config = "beyondcorp-clientconnectorservices_grpc_service_config.json",
    importpath = "cloud.google.com/go/beyondcorp/clientconnectorservices/apiv1;clientconnectorservices",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    deps = [
        ":clientconnectorservices_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-beyondcorp-clientconnectorservices-v1-go",
    deps = [
        ":clientconnectorservices_go_gapic",
        ":clientconnectorservices_go_gapic_srcjar-metadata.srcjar",
        ":clientconnectorservices_go_gapic_srcjar-snippets.srcjar",
        ":clientconnectorservices_go_gapic_srcjar-test.srcjar",
        ":clientconnectorservices_go_proto",
    ],
)

py_gapic_library(
    name = "clientconnectorservices_py_gapic",
    srcs = [":clientconnectorservices_proto"],
    grpc_service_config = "beyondcorp-clientconnectorservices_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-beyondcorp-clientconnectorservices",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=beyondcorp_clientconnectorservices",
    ],
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "clientconnectorservices_py_gapic_test",
    srcs = [
        "clientconnectorservices_py_gapic_pytest.py",
        "clientconnectorservices_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":clientconnectorservices_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "beyondcorp-clientconnectorservices-v1-py",
    deps = [
        ":clientconnectorservices_py_gapic",
    ],
)

php_proto_library(
    name = "clientconnectorservices_php_proto",
    deps = [":clientconnectorservices_proto"],
)

php_gapic_library(
    name = "clientconnectorservices_php_gapic",
    srcs = [":clientconnectorservices_proto_with_info"],
    grpc_service_config = "beyondcorp-clientconnectorservices_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [":clientconnectorservices_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-clientconnectorservices-v1-php",
    deps = [
        ":clientconnectorservices_php_gapic",
        ":clientconnectorservices_php_proto",
    ],
)

nodejs_gapic_library(
    name = "clientconnectorservices_nodejs_gapic",
    package_name = "@google-cloud/clientconnectorservices",
    src = ":clientconnectorservices_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "beyondcorp-clientconnectorservices_grpc_service_config.json",
    package = "google.cloud.beyondcorp.clientconnectorservices.v1",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "beyondcorp-clientconnectorservices-v1-nodejs",
    deps = [
        ":clientconnectorservices_nodejs_gapic",
        ":clientconnectorservices_proto",
    ],
)

ruby_proto_library(
    name = "clientconnectorservices_ruby_proto",
    deps = [":clientconnectorservices_proto"],
)

ruby_grpc_library(
    name = "clientconnectorservices_ruby_grpc",
    srcs = [":clientconnectorservices_proto"],
    deps = [":clientconnectorservices_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "clientconnectorservices_ruby_gapic",
    srcs = [":clientconnectorservices_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-beyond_corp-client_connector_services-v1",
        "ruby-cloud-product-url=https://cloud.google.com/beyondcorp/",
        "ruby-cloud-api-id=beyondcorp.googleapis.com",
        "ruby-cloud-api-shortname=beyondcorp",
        "ruby-cloud-wrapper-gem-override=google-cloud-beyond_corp",
    ],
    grpc_service_config = "beyondcorp-clientconnectorservices_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Beyondcorp Enterprise provides identity and context aware access controls for enterprise resources and enables zero-trust access. Using the Beyondcorp Enterprise APIs, enterprises can set up multi-cloud and on-prem connectivity using the App Connector hybrid connectivity solution.",
    ruby_cloud_title = "BeyondCorp ClientConnectorServices V1",
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc",
    deps = [
        ":clientconnectorservices_ruby_grpc",
        ":clientconnectorservices_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-clientconnectorservices-v1-ruby",
    deps = [
        ":clientconnectorservices_ruby_gapic",
        ":clientconnectorservices_ruby_grpc",
        ":clientconnectorservices_ruby_proto",
    ],
)

csharp_proto_library(
    name = "clientconnectorservices_csharp_proto",
    deps = [":clientconnectorservices_proto"],
)

csharp_grpc_library(
    name = "clientconnectorservices_csharp_grpc",
    srcs = [":clientconnectorservices_proto"],
    deps = [":clientconnectorservices_csharp_proto"],
)

csharp_gapic_library(
    name = "clientconnectorservices_csharp_gapic",
    srcs = [":clientconnectorservices_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "beyondcorp-clientconnectorservices_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc",
    deps = [
        ":clientconnectorservices_csharp_grpc",
        ":clientconnectorservices_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-clientconnectorservices-v1-csharp",
    deps = [
        ":clientconnectorservices_csharp_gapic",
        ":clientconnectorservices_csharp_grpc",
        ":clientconnectorservices_csharp_proto",
    ],
)

cc_proto_library(
    name = "clientconnectorservices_cc_proto",
    deps = [":clientconnectorservices_proto"],
)

cc_grpc_library(
    name = "clientconnectorservices_cc_grpc",
    srcs = [":clientconnectorservices_proto"],
    grpc_only = True,
    deps = [":clientconnectorservices_cc_proto"],
)
