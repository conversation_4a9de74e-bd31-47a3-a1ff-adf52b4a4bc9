{"methodConfig": [{"name": [{"service": "google.cloud.beyondcorp.clientconnectorservices.v1", "method": "ListClientConnectorServices"}, {"service": "google.cloud.beyondcorp.clientconnectorservices.v1", "method": "GetClientConnectorService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.beyondcorp.clientconnectorservices.v1", "method": "CreateClientConnectorService"}, {"service": "google.cloud.beyondcorp.clientconnectorservices.v1", "method": "UpdateClientConnectorService"}, {"service": "google.cloud.beyondcorp.clientconnectorservices.v1", "method": "DeleteClientConnectorService"}], "timeout": "60s"}]}