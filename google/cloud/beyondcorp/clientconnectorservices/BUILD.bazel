# This build file includes a target for the Ruby wrapper library for
# google-cloud-beyond_corp-client_connector_services.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for clientconnectorservices.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "clientconnectorservices_ruby_wrapper",
    srcs = ["//google/cloud/beyondcorp/clientconnectorservices/v1:clientconnectorservices_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=beyondcorp.googleapis.com",
        "ruby-cloud-api-shortname=beyondcorp",
        "ruby-cloud-gem-name=google-cloud-beyond_corp-client_connector_services",
        "ruby-cloud-product-url=https://cloud.google.com/beyondcorp/",
        "ruby-cloud-wrapper-of=v1:0.4",
    ],
    ruby_cloud_description = "Beyondcorp Enterprise provides identity and context aware access controls for enterprise resources and enables zero-trust access. Using the Beyondcorp Enterprise APIs, enterprises can set up multi-cloud and on-prem connectivity using the App Connector hybrid connectivity solution.",
    ruby_cloud_title = "BeyondCorp ClientConnectorServices",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-clientconnectorservices-ruby",
    deps = [
        ":clientconnectorservices_ruby_wrapper",
    ],
)
