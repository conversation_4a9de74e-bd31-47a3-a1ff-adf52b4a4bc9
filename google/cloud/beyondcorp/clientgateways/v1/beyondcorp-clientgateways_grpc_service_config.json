{"methodConfig": [{"name": [{"service": "google.cloud.beyondcorp.clientgateways.v1", "method": "ListClientGateways"}, {"service": "google.cloud.beyondcorp.clientgateways.v1", "method": "GetClientGateway"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.beyondcorp.clientgateways.v1", "method": "CreateClientGateway"}, {"service": "google.cloud.beyondcorp.clientgateways.v1", "method": "DeleteClientGateway"}], "timeout": "60s"}]}