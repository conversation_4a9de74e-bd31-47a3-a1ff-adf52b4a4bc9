// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.beyondcorp.clientgateways.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BeyondCorp.ClientGateways.V1";
option go_package = "cloud.google.com/go/beyondcorp/clientgateways/apiv1/clientgatewayspb;clientgatewayspb";
option java_multiple_files = true;
option java_outer_classname = "ClientGatewaysServiceProto";
option java_package = "com.google.cloud.beyondcorp.clientgateways.v1";
option php_namespace = "Google\\Cloud\\BeyondCorp\\ClientGateways\\V1";
option ruby_package = "Google::Cloud::BeyondCorp::ClientGateways::V1";

// API Overview:
//
// The `beyondcorp.googleapis.com` service implements the Google Cloud
// BeyondCorp API.
//
// Data Model:
//
// The ClientGatewaysService exposes the following resources:
//
// * Client Gateways, named as follows:
//   `projects/{project_id}/locations/{location_id}/clientGateways/{client_gateway_id}`.
service ClientGatewaysService {
  option (google.api.default_host) = "beyondcorp.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists ClientGateways in a given project and location.
  rpc ListClientGateways(ListClientGatewaysRequest)
      returns (ListClientGatewaysResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/clientGateways"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ClientGateway.
  rpc GetClientGateway(GetClientGatewayRequest) returns (ClientGateway) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clientGateways/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new ClientGateway in a given project and location.
  rpc CreateClientGateway(CreateClientGatewayRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/clientGateways"
      body: "client_gateway"
    };
    option (google.api.method_signature) =
        "parent,client_gateway,client_gateway_id";
    option (google.longrunning.operation_info) = {
      response_type: "ClientGateway"
      metadata_type: "ClientGatewayOperationMetadata"
    };
  }

  // Deletes a single ClientGateway.
  rpc DeleteClientGateway(DeleteClientGatewayRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clientGateways/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "ClientGatewayOperationMetadata"
    };
  }
}

// Message describing ClientGateway object.
message ClientGateway {
  option (google.api.resource) = {
    type: "beyondcorp.googleapis.com/ClientGateway"
    pattern: "projects/{project}/locations/{location}/clientGateways/{client_gateway}"
  };

  // Represents the different states of a gateway.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // Gateway is being created.
    CREATING = 1;

    // Gateway is being updated.
    UPDATING = 2;

    // Gateway is being deleted.
    DELETING = 3;

    // Gateway is running.
    RUNNING = 4;

    // Gateway is down and may be restored in the future.
    // This happens when CCFE sends ProjectState = OFF.
    DOWN = 5;

    // ClientGateway encountered an error and is in indeterministic state.
    ERROR = 6;
  }

  // Required. name of resource. The name is ignored during creation.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. [Output only] Create time stamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. [Output only] Update time stamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The operational state of the gateway.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A unique identifier for the instance generated by the system.
  string id = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The client connector service name that the client gateway is
  // associated to. Client Connector Services, named as follows:
  //   `projects/{project_id}/locations/{location_id}/client_connector_services/{client_connector_service_id}`.
  string client_connector_service = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for requesting list of ClientGateways.
message ListClientGatewaysRequest {
  // Required. Parent value for ListClientGatewaysRequest.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/ClientGateway"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Hint for how to order the results.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for response to listing ClientGateways.
message ListClientGatewaysResponse {
  // The list of ClientGateway.
  repeated ClientGateway client_gateways = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Message for getting a ClientGateway.
message GetClientGatewayRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/ClientGateway"
    }
  ];
}

// Message for creating a ClientGateway.
message CreateClientGatewayRequest {
  // Required. Value for parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/ClientGateway"
    }
  ];

  // Optional. User-settable client gateway resource ID.
  //  * Must start with a letter.
  //  * Must contain between 4-63 characters from `/[a-z][0-9]-/`.
  //  * Must end with a number or a letter.
  string client_gateway_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The resource being created.
  ClientGateway client_gateway = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Message for deleting a ClientGateway
message DeleteClientGatewayRequest {
  // Required. Name of the resource
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/ClientGateway"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message ClientGatewayOperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have been cancelled successfully
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
