type: google.api.Service
config_version: 3
name: beyondcorp.googleapis.com
title: BeyondCorp API

apis:
- name: google.cloud.beyondcorp.appconnections.v1.AppConnectionsService
- name: google.cloud.location.Locations
- name: google.iam.v1.IAMPolicy
- name: google.longrunning.Operations

types:
- name: google.cloud.beyondcorp.appconnections.v1.AppConnectionOperationMetadata

documentation:
  summary: |-
    Beyondcorp Enterprise provides identity and context aware access controls
    for enterprise resources and enables zero-trust access. Using the
    Beyondcorp Enterprise APIs, enterprises can set up multi-cloud and on-prem
    connectivity using the App Connector hybrid connectivity solution.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

backend:
  rules:
  - selector: 'google.cloud.beyondcorp.appconnections.v1.AppConnectionsService.*'
    deadline: 60.0
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 60.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 60.0
  - selector: 'google.iam.v1.IAMPolicy.*'
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=projects/*}/locations'
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1/{resource=projects/*/locations/*/appConnections/*}:getIamPolicy'
    additional_bindings:
    - get: '/v1/{resource=projects/*/locations/*/appConnectors/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/appGateways/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/clientConnectorServices/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/locations/*/clientGateways/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1/{resource=projects/*/locations/*/appConnections/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/appConnectors/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/appGateways/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/clientConnectorServices/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/clientGateways/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1/{resource=projects/*/locations/*/appConnections/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/locations/*/appConnectors/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/appGateways/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/clientConnectorServices/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/locations/*/clientGateways/*}:testIamPermissions'
      body: '*'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/locations/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.cloud.beyondcorp.appconnections.v1.AppConnectionsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
