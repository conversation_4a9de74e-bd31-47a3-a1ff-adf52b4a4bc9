# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "appconnections_proto",
    srcs = [
        "app_connections_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "appconnections_proto_with_info",
    deps = [
        ":appconnections_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

java_proto_library(
    name = "appconnections_java_proto",
    deps = [":appconnections_proto"],
)

java_grpc_library(
    name = "appconnections_java_grpc",
    srcs = [":appconnections_proto"],
    deps = [":appconnections_java_proto"],
)

java_gapic_library(
    name = "appconnections_java_gapic",
    srcs = [":appconnections_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "beyondcorp-appconnections_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    test_deps = [
        ":appconnections_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":appconnections_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "appconnections_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.beyondcorp.appconnections.v1.AppConnectionsServiceClientTest",
    ],
    runtime_deps = [":appconnections_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-beyondcorp-appconnections-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":appconnections_java_gapic",
        ":appconnections_java_grpc",
        ":appconnections_java_proto",
        ":appconnections_proto",
    ],
)

go_proto_library(
    name = "appconnections_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/beyondcorp/appconnections/apiv1/appconnectionspb",
    protos = [":appconnections_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "appconnections_go_gapic",
    srcs = [":appconnections_proto_with_info"],
    grpc_service_config = "beyondcorp-appconnections_grpc_service_config.json",
    importpath = "cloud.google.com/go/beyondcorp/appconnections/apiv1;appconnections",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    deps = [
        ":appconnections_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-beyondcorp-appconnections-v1-go",
    deps = [
        ":appconnections_go_gapic",
        ":appconnections_go_gapic_srcjar-metadata.srcjar",
        ":appconnections_go_gapic_srcjar-snippets.srcjar",
        ":appconnections_go_gapic_srcjar-test.srcjar",
        ":appconnections_go_proto",
    ],
)

py_gapic_library(
    name = "appconnections_py_gapic",
    srcs = [":appconnections_proto"],
    grpc_service_config = "beyondcorp-appconnections_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-beyondcorp-appconnections",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=beyondcorp_appconnections",
    ],
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "appconnections_py_gapic_test",
    srcs = [
        "appconnections_py_gapic_pytest.py",
        "appconnections_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":appconnections_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "beyondcorp-appconnections-v1-py",
    deps = [
        ":appconnections_py_gapic",
    ],
)

php_proto_library(
    name = "appconnections_php_proto",
    deps = [":appconnections_proto"],
)

php_gapic_library(
    name = "appconnections_php_gapic",
    srcs = [":appconnections_proto_with_info"],
    grpc_service_config = "beyondcorp-appconnections_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [":appconnections_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-appconnections-v1-php",
    deps = [
        ":appconnections_php_gapic",
        ":appconnections_php_proto",
    ],
)

nodejs_gapic_library(
    name = "appconnections_nodejs_gapic",
    package_name = "@google-cloud/appconnections",
    src = ":appconnections_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "beyondcorp-appconnections_grpc_service_config.json",
    package = "google.cloud.beyondcorp.appconnections.v1",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "beyondcorp-appconnections-v1-nodejs",
    deps = [
        ":appconnections_nodejs_gapic",
        ":appconnections_proto",
    ],
)

ruby_proto_library(
    name = "appconnections_ruby_proto",
    deps = [":appconnections_proto"],
)

ruby_grpc_library(
    name = "appconnections_ruby_grpc",
    srcs = [":appconnections_proto"],
    deps = [":appconnections_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "appconnections_ruby_gapic",
    srcs = [":appconnections_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-beyond_corp-app_connections-v1",
        "ruby-cloud-product-url=https://cloud.google.com/beyondcorp/",
        "ruby-cloud-api-id=beyondcorp.googleapis.com",
        "ruby-cloud-api-shortname=beyondcorp",
        "ruby-cloud-wrapper-gem-override=google-cloud-beyond_corp",
    ],
    grpc_service_config = "beyondcorp-appconnections_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Beyondcorp Enterprise provides identity and context aware access controls for enterprise resources and enables zero-trust access. Using the Beyondcorp Enterprise APIs, enterprises can set up multi-cloud and on-prem connectivity using the App Connector hybrid connectivity solution.",
    ruby_cloud_title = "BeyondCorp AppConnections V1",
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc",
    deps = [
        ":appconnections_ruby_grpc",
        ":appconnections_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-appconnections-v1-ruby",
    deps = [
        ":appconnections_ruby_gapic",
        ":appconnections_ruby_grpc",
        ":appconnections_ruby_proto",
    ],
)

csharp_proto_library(
    name = "appconnections_csharp_proto",
    deps = [":appconnections_proto"],
)

csharp_grpc_library(
    name = "appconnections_csharp_grpc",
    srcs = [":appconnections_proto"],
    deps = [":appconnections_csharp_proto"],
)

csharp_gapic_library(
    name = "appconnections_csharp_gapic",
    srcs = [":appconnections_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "beyondcorp-appconnections_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc",
    deps = [
        ":appconnections_csharp_grpc",
        ":appconnections_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-appconnections-v1-csharp",
    deps = [
        ":appconnections_csharp_gapic",
        ":appconnections_csharp_grpc",
        ":appconnections_csharp_proto",
    ],
)

cc_proto_library(
    name = "appconnections_cc_proto",
    deps = [":appconnections_proto"],
)

cc_grpc_library(
    name = "appconnections_cc_grpc",
    srcs = [":appconnections_proto"],
    grpc_only = True,
    deps = [":appconnections_cc_proto"],
)
