// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.beyondcorp.appconnections.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BeyondCorp.AppConnections.V1";
option go_package = "cloud.google.com/go/beyondcorp/appconnections/apiv1/appconnectionspb;appconnectionspb";
option java_multiple_files = true;
option java_outer_classname = "AppConnectionsServiceProto";
option java_package = "com.google.cloud.beyondcorp.appconnections.v1";
option php_namespace = "Google\\Cloud\\BeyondCorp\\AppConnections\\V1";
option ruby_package = "Google::Cloud::BeyondCorp::AppConnections::V1";
option (google.api.resource_definition) = {
  type: "beyondcorp.googleapis.com/AppConnector"
  pattern: "projects/{project}/locations/{location}/appConnectors/{app_connector}"
};
option (google.api.resource_definition) = {
  type: "beyondcorp.googleapis.com/AppGateway"
  pattern: "projects/{project}/locations/{location}/appGateways/{app_gateway}"
};

// API Overview:
//
// The `beyondcorp.googleapis.com` service implements the Google Cloud
// BeyondCorp API.
//
// Data Model:
//
// The AppConnectionsService exposes the following resources:
//
// * AppConnections, named as follows:
//   `projects/{project_id}/locations/{location_id}/appConnections/{app_connection_id}`.
//
// The AppConnectionsService service provides methods to manage
// (create/read/update/delete) BeyondCorp AppConnections.
service AppConnectionsService {
  option (google.api.default_host) = "beyondcorp.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists AppConnections in a given project and location.
  rpc ListAppConnections(ListAppConnectionsRequest)
      returns (ListAppConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/appConnections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single AppConnection.
  rpc GetAppConnection(GetAppConnectionRequest) returns (AppConnection) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/appConnections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new AppConnection in a given project and location.
  rpc CreateAppConnection(CreateAppConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/appConnections"
      body: "app_connection"
    };
    option (google.api.method_signature) =
        "parent,app_connection,app_connection_id";
    option (google.longrunning.operation_info) = {
      response_type: "AppConnection"
      metadata_type: "AppConnectionOperationMetadata"
    };
  }

  // Updates the parameters of a single AppConnection.
  rpc UpdateAppConnection(UpdateAppConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{app_connection.name=projects/*/locations/*/appConnections/*}"
      body: "app_connection"
    };
    option (google.api.method_signature) = "app_connection,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AppConnection"
      metadata_type: "AppConnectionOperationMetadata"
    };
  }

  // Deletes a single AppConnection.
  rpc DeleteAppConnection(DeleteAppConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/appConnections/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "AppConnectionOperationMetadata"
    };
  }

  // Resolves AppConnections details for a given AppConnector.
  // An internal method called by a connector to find AppConnections to connect
  // to.
  rpc ResolveAppConnections(ResolveAppConnectionsRequest)
      returns (ResolveAppConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/appConnections:resolve"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for BeyondCorp.ListAppConnections.
message ListAppConnectionsRequest {
  // Required. The resource name of the AppConnection location using the form:
  // `projects/{project_id}/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/AppConnection"
    }
  ];

  // Optional. The maximum number of items to return.
  // If not specified, a default value of 50 will be used by the service.
  // Regardless of the page_size value, the response may include a partial list
  // and a caller should only rely on response's
  // [next_page_token][BeyondCorp.ListAppConnectionsResponse.next_page_token] to
  // determine if there are more instances left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous
  // ListAppConnectionsRequest, if any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter specifying constraints of a list operation.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the ordering of results. See
  // [Sorting
  // order](https://cloud.google.com/apis/design/design_patterns#sorting_order)
  // for more information.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for BeyondCorp.ListAppConnections.
message ListAppConnectionsResponse {
  // A list of BeyondCorp AppConnections in the project.
  repeated AppConnection app_connections = 1;

  // A token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // A list of locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for BeyondCorp.GetAppConnection.
message GetAppConnectionRequest {
  // Required. BeyondCorp AppConnection name using the form:
  // `projects/{project_id}/locations/{location_id}/appConnections/{app_connection_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/AppConnection"
    }
  ];
}

// Request message for BeyondCorp.CreateAppConnection.
message CreateAppConnectionRequest {
  // Required. The resource project name of the AppConnection location using the
  // form: `projects/{project_id}/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/AppConnection"
    }
  ];

  // Optional. User-settable AppConnection resource ID.
  //  * Must start with a letter.
  //  * Must contain between 4-63 characters from `/[a-z][0-9]-/`.
  //  * Must end with a number or a letter.
  string app_connection_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A BeyondCorp AppConnection resource.
  AppConnection app_connection = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for BeyondCorp.UpdateAppConnection.
message UpdateAppConnectionRequest {
  // Required. Mask of fields to update. At least one path must be supplied in
  // this field. The elements of the repeated paths field may only include these
  // fields from [BeyondCorp.AppConnection]:
  // * `labels`
  // * `display_name`
  // * `application_endpoint`
  // * `connectors`
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. AppConnection message with updated fields. Only supported fields
  // specified in update_mask are updated.
  AppConnection app_connection = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set as true, will create the resource if it is not found.
  bool allow_missing = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for BeyondCorp.DeleteAppConnection.
message DeleteAppConnectionRequest {
  // Required. BeyondCorp Connector name using the form:
  // `projects/{project_id}/locations/{location_id}/appConnections/{app_connection_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/AppConnection"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for BeyondCorp.ResolveAppConnections.
message ResolveAppConnectionsRequest {
  // Required. The resource name of the AppConnection location using the form:
  // `projects/{project_id}/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/AppConnection"
    }
  ];

  // Required. BeyondCorp Connector name of the connector associated with those
  // AppConnections using the form:
  // `projects/{project_id}/locations/{location_id}/appConnectors/{app_connector_id}`
  string app_connector_id = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/AppConnector"
    }
  ];

  // Optional. The maximum number of items to return.
  // If not specified, a default value of 50 will be used by the service.
  // Regardless of the page_size value, the response may include a partial list
  // and a caller should only rely on response's
  // [next_page_token][BeyondCorp.ResolveAppConnectionsResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous
  // ResolveAppConnectionsResponse, if any.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for BeyondCorp.ResolveAppConnections.
message ResolveAppConnectionsResponse {
  // Details of the AppConnection.
  message AppConnectionDetails {
    // A BeyondCorp AppConnection in the project.
    AppConnection app_connection = 1;

    // If type=GCP_REGIONAL_MIG, contains most recent VM instances, like
    // `https://www.googleapis.com/compute/v1/projects/{project_id}/zones/{zone_id}/instances/{instance_id}`.
    repeated string recent_mig_vms = 2;
  }

  // A list of BeyondCorp AppConnections with details in the project.
  repeated AppConnectionDetails app_connection_details = 1;

  // A token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // A list of locations that could not be reached.
  repeated string unreachable = 3;
}

// A BeyondCorp AppConnection resource represents a BeyondCorp protected
// AppConnection to a remote application. It creates all the necessary GCP
// components needed for creating a BeyondCorp protected AppConnection. Multiple
// connectors can be authorised for a single AppConnection.
message AppConnection {
  option (google.api.resource) = {
    type: "beyondcorp.googleapis.com/AppConnection"
    pattern: "projects/{project}/locations/{location}/appConnections/{app_connection}"
  };

  // ApplicationEndpoint represents a remote application endpoint.
  message ApplicationEndpoint {
    // Required. Hostname or IP address of the remote application endpoint.
    string host = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Port of the remote application endpoint.
    int32 port = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Gateway represents a user facing component that serves as an entrance to
  // enable connectivity.
  message Gateway {
    // Enum listing possible gateway hosting options.
    enum Type {
      // Default value. This value is unused.
      TYPE_UNSPECIFIED = 0;

      // Gateway hosted in a GCP regional managed instance group.
      GCP_REGIONAL_MIG = 1;
    }

    // Required. The type of hosting used by the gateway.
    Type type = 2 [(google.api.field_behavior) = REQUIRED];

    // Output only. Server-defined URI for this resource.
    string uri = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Ingress port reserved on the gateways for this
    // AppConnection, if not specified or zero, the default port is 19443.
    int32 ingress_port = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Required. AppGateway name in following format:
    // `projects/{project_id}/locations/{location_id}/appgateways/{gateway_id}`
    string app_gateway = 5 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "beyondcorp.googleapis.com/AppGateway"
      }
    ];
  }

  // Enum containing list of all possible network connectivity options
  // supported by BeyondCorp AppConnection.
  enum Type {
    // Default value. This value is unused.
    TYPE_UNSPECIFIED = 0;

    // TCP Proxy based BeyondCorp AppConnection. API will default to this if
    // unset.
    TCP_PROXY = 1;
  }

  // Represents the different states of a AppConnection.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // AppConnection is being created.
    CREATING = 1;

    // AppConnection has been created.
    CREATED = 2;

    // AppConnection's configuration is being updated.
    UPDATING = 3;

    // AppConnection is being deleted.
    DELETING = 4;

    // AppConnection is down and may be restored in the future.
    // This happens when CCFE sends ProjectState = OFF.
    DOWN = 5;
  }

  // Required. Unique resource name of the AppConnection.
  // The name is ignored when creating a AppConnection.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the resource was last modified.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels to represent user provided metadata.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An arbitrary user-provided name for the AppConnection. Cannot
  // exceed 64 characters.
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A unique identifier for the instance generated by the
  // system.
  string uid = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The type of network connectivity used by the AppConnection.
  Type type = 7 [(google.api.field_behavior) = REQUIRED];

  // Required. Address of the remote application endpoint for the BeyondCorp
  // AppConnection.
  ApplicationEndpoint application_endpoint = 8
      [(google.api.field_behavior) = REQUIRED];

  // Optional. List of [google.cloud.beyondcorp.v1main.Connector.name] that are
  // authorised to be associated with this AppConnection.
  repeated string connectors = 9 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The current state of the AppConnection.
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Gateway used by the AppConnection.
  Gateway gateway = 11 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message AppConnectionOperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
