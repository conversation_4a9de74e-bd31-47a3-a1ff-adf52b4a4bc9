{"methodConfig": [{"name": [{"service": "google.cloud.beyondcorp.appconnections.v1", "method": "ListAppConnections"}, {"service": "google.cloud.beyondcorp.appconnections.v1", "method": "GetAppConnection"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.beyondcorp.appconnections.v1", "method": "CreateAppConnection"}, {"service": "google.cloud.beyondcorp.appconnections.v1", "method": "UpdateAppConnection"}, {"service": "google.cloud.beyondcorp.appconnections.v1", "method": "DeleteAppConnection"}], "timeout": "60s"}]}