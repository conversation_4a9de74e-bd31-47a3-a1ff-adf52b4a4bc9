# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "appconnectors_proto",
    srcs = [
        "app_connector_instance_config.proto",
        "app_connectors_service.proto",
        "resource_info.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "appconnectors_proto_with_info",
    deps = [
        ":appconnectors_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

java_proto_library(
    name = "appconnectors_java_proto",
    deps = [":appconnectors_proto"],
)

java_grpc_library(
    name = "appconnectors_java_grpc",
    srcs = [":appconnectors_proto"],
    deps = [":appconnectors_java_proto"],
)

java_gapic_library(
    name = "appconnectors_java_gapic",
    srcs = [":appconnectors_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "beyondcorp-appconnectors_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    test_deps = [
        ":appconnectors_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":appconnectors_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "appconnectors_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.beyondcorp.appconnectors.v1.AppConnectorsServiceClientTest",
    ],
    runtime_deps = [":appconnectors_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-beyondcorp-appconnectors-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":appconnectors_java_gapic",
        ":appconnectors_java_grpc",
        ":appconnectors_java_proto",
        ":appconnectors_proto",
    ],
)

go_proto_library(
    name = "appconnectors_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/beyondcorp/appconnectors/apiv1/appconnectorspb",
    protos = [":appconnectors_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "appconnectors_go_gapic",
    srcs = [":appconnectors_proto_with_info"],
    grpc_service_config = "beyondcorp-appconnectors_grpc_service_config.json",
    importpath = "cloud.google.com/go/beyondcorp/appconnectors/apiv1;appconnectors",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    deps = [
        ":appconnectors_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-beyondcorp-appconnectors-v1-go",
    deps = [
        ":appconnectors_go_gapic",
        ":appconnectors_go_gapic_srcjar-metadata.srcjar",
        ":appconnectors_go_gapic_srcjar-snippets.srcjar",
        ":appconnectors_go_gapic_srcjar-test.srcjar",
        ":appconnectors_go_proto",
    ],
)

py_gapic_library(
    name = "appconnectors_py_gapic",
    srcs = [":appconnectors_proto"],
    grpc_service_config = "beyondcorp-appconnectors_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-beyondcorp-appconnectors",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=beyondcorp_appconnectors",
    ],
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "appconnectors_py_gapic_test",
    srcs = [
        "appconnectors_py_gapic_pytest.py",
        "appconnectors_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":appconnectors_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "beyondcorp-appconnectors-v1-py",
    deps = [
        ":appconnectors_py_gapic",
    ],
)

php_proto_library(
    name = "appconnectors_php_proto",
    deps = [":appconnectors_proto"],
)

php_gapic_library(
    name = "appconnectors_php_gapic",
    srcs = [":appconnectors_proto_with_info"],
    grpc_service_config = "beyondcorp-appconnectors_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [":appconnectors_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-appconnectors-v1-php",
    deps = [
        ":appconnectors_php_gapic",
        ":appconnectors_php_proto",
    ],
)

nodejs_gapic_library(
    name = "appconnectors_nodejs_gapic",
    package_name = "@google-cloud/appconnectors",
    src = ":appconnectors_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "beyondcorp-appconnectors_grpc_service_config.json",
    package = "google.cloud.beyondcorp.appconnectors.v1",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "beyondcorp-appconnectors-v1-nodejs",
    deps = [
        ":appconnectors_nodejs_gapic",
        ":appconnectors_proto",
    ],
)

ruby_proto_library(
    name = "appconnectors_ruby_proto",
    deps = [":appconnectors_proto"],
)

ruby_grpc_library(
    name = "appconnectors_ruby_grpc",
    srcs = [":appconnectors_proto"],
    deps = [":appconnectors_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "appconnectors_ruby_gapic",
    srcs = [":appconnectors_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-beyond_corp-app_connectors-v1",
        "ruby-cloud-product-url=https://cloud.google.com/beyondcorp/",
        "ruby-cloud-api-id=beyondcorp.googleapis.com",
        "ruby-cloud-api-shortname=beyondcorp",
        "ruby-cloud-wrapper-gem-override=google-cloud-beyond_corp",
    ],
    grpc_service_config = "beyondcorp-appconnectors_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Beyondcorp Enterprise provides identity and context aware access controls for enterprise resources and enables zero-trust access. Using the Beyondcorp Enterprise APIs, enterprises can set up multi-cloud and on-prem connectivity using the App Connector hybrid connectivity solution.",
    ruby_cloud_title = "BeyondCorp AppConnectors V1",
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc",
    deps = [
        ":appconnectors_ruby_grpc",
        ":appconnectors_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-appconnectors-v1-ruby",
    deps = [
        ":appconnectors_ruby_gapic",
        ":appconnectors_ruby_grpc",
        ":appconnectors_ruby_proto",
    ],
)

csharp_proto_library(
    name = "appconnectors_csharp_proto",
    deps = [":appconnectors_proto"],
)

csharp_grpc_library(
    name = "appconnectors_csharp_grpc",
    srcs = [":appconnectors_proto"],
    deps = [":appconnectors_csharp_proto"],
)

csharp_gapic_library(
    name = "appconnectors_csharp_gapic",
    srcs = [":appconnectors_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "beyondcorp-appconnectors_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc",
    deps = [
        ":appconnectors_csharp_grpc",
        ":appconnectors_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-appconnectors-v1-csharp",
    deps = [
        ":appconnectors_csharp_gapic",
        ":appconnectors_csharp_grpc",
        ":appconnectors_csharp_proto",
    ],
)

cc_proto_library(
    name = "appconnectors_cc_proto",
    deps = [":appconnectors_proto"],
)

cc_grpc_library(
    name = "appconnectors_cc_grpc",
    srcs = [":appconnectors_proto"],
    grpc_only = True,
    deps = [":appconnectors_cc_proto"],
)
