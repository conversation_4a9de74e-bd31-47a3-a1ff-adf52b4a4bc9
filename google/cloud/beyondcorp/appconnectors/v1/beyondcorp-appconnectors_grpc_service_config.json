{"methodConfig": [{"name": [{"service": "google.cloud.beyondcorp.appconnectors.v1", "method": "ListAppConnectors"}, {"service": "google.cloud.beyondcorp.appconnectors.v1", "method": "GetAppConnector"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.beyondcorp.appconnectors.v1", "method": "CreateAppConnector"}, {"service": "google.cloud.beyondcorp.appconnectors.v1", "method": "UpdateAppConnector"}, {"service": "google.cloud.beyondcorp.appconnectors.v1", "method": "DeleteAppConnector"}, {"service": "google.cloud.beyondcorp.appconnectors.v1", "method": "ReportStatus"}], "timeout": "60s"}]}