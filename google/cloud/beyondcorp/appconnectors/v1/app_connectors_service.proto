// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.beyondcorp.appconnectors.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/beyondcorp/appconnectors/v1/app_connector_instance_config.proto";
import "google/cloud/beyondcorp/appconnectors/v1/resource_info.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BeyondCorp.AppConnectors.V1";
option go_package = "cloud.google.com/go/beyondcorp/appconnectors/apiv1/appconnectorspb;appconnectorspb";
option java_multiple_files = true;
option java_outer_classname = "AppConnectorsServiceProto";
option java_package = "com.google.cloud.beyondcorp.appconnectors.v1";
option php_namespace = "Google\\Cloud\\BeyondCorp\\AppConnectors\\V1";
option ruby_package = "Google::Cloud::BeyondCorp::AppConnectors::V1";

// API Overview:
//
// The `beyondcorp.googleapis.com` service implements the Google Cloud
// BeyondCorp API.
//
// Data Model:
//
// The AppConnectorsService exposes the following resource:
//
// * AppConnectors, named as follows:
//   `projects/{project_id}/locations/{location_id}/appConnectors/{app_connector_id}`.
//
// The AppConnectorsService provides methods to manage
// (create/read/update/delete) BeyondCorp AppConnectors.
service AppConnectorsService {
  option (google.api.default_host) = "beyondcorp.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists AppConnectors in a given project and location.
  rpc ListAppConnectors(ListAppConnectorsRequest)
      returns (ListAppConnectorsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/appConnectors"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single AppConnector.
  rpc GetAppConnector(GetAppConnectorRequest) returns (AppConnector) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/appConnectors/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new AppConnector in a given project and location.
  rpc CreateAppConnector(CreateAppConnectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/appConnectors"
      body: "app_connector"
    };
    option (google.api.method_signature) =
        "parent,app_connector,app_connector_id";
    option (google.longrunning.operation_info) = {
      response_type: "AppConnector"
      metadata_type: "AppConnectorOperationMetadata"
    };
  }

  // Updates the parameters of a single AppConnector.
  rpc UpdateAppConnector(UpdateAppConnectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{app_connector.name=projects/*/locations/*/appConnectors/*}"
      body: "app_connector"
    };
    option (google.api.method_signature) = "app_connector,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "AppConnector"
      metadata_type: "AppConnectorOperationMetadata"
    };
  }

  // Deletes a single AppConnector.
  rpc DeleteAppConnector(DeleteAppConnectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/appConnectors/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "AppConnectorOperationMetadata"
    };
  }

  // Report status for a given connector.
  rpc ReportStatus(ReportStatusRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{app_connector=projects/*/locations/*/appConnectors/*}:reportStatus"
      body: "*"
    };
    option (google.api.method_signature) = "app_connector,resource_info";
    option (google.longrunning.operation_info) = {
      response_type: "AppConnector"
      metadata_type: "AppConnectorOperationMetadata"
    };
  }
}

// Request message for BeyondCorp.ListAppConnectors.
message ListAppConnectorsRequest {
  // Required. The resource name of the AppConnector location using the form:
  // `projects/{project_id}/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/AppConnector"
    }
  ];

  // Optional. The maximum number of items to return.
  // If not specified, a default value of 50 will be used by the service.
  // Regardless of the page_size value, the response may include a partial list
  // and a caller should only rely on response's
  // [next_page_token][BeyondCorp.ListAppConnectorsResponse.next_page_token] to
  // determine if there are more instances left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous
  // ListAppConnectorsRequest, if any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter specifying constraints of a list operation.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the ordering of results. See
  // [Sorting
  // order](https://cloud.google.com/apis/design/design_patterns#sorting_order)
  // for more information.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for BeyondCorp.ListAppConnectors.
message ListAppConnectorsResponse {
  // A list of BeyondCorp AppConnectors in the project.
  repeated AppConnector app_connectors = 1;

  // A token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // A list of locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for BeyondCorp.GetAppConnector.
message GetAppConnectorRequest {
  // Required. BeyondCorp AppConnector name using the form:
  // `projects/{project_id}/locations/{location_id}/appConnectors/{app_connector_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/AppConnector"
    }
  ];
}

// Request message for BeyondCorp.CreateAppConnector.
message CreateAppConnectorRequest {
  // Required. The resource project name of the AppConnector location using the
  // form: `projects/{project_id}/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/AppConnector"
    }
  ];

  // Optional. User-settable AppConnector resource ID.
  //
  //  * Must start with a letter.
  //  * Must contain between 4-63 characters from `/[a-z][0-9]-/`.
  //  * Must end with a number or a letter.
  string app_connector_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A BeyondCorp AppConnector resource.
  AppConnector app_connector = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for BeyondCorp.UpdateAppConnector.
message UpdateAppConnectorRequest {
  // Required. Mask of fields to update. At least one path must be supplied in
  // this field. The elements of the repeated paths field may only include these
  // fields from [BeyondCorp.AppConnector]:
  // * `labels`
  // * `display_name`
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. AppConnector message with updated fields. Only supported fields
  // specified in update_mask are updated.
  AppConnector app_connector = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for BeyondCorp.DeleteAppConnector.
message DeleteAppConnectorRequest {
  // Required. BeyondCorp AppConnector name using the form:
  // `projects/{project_id}/locations/{location_id}/appConnectors/{app_connector_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/AppConnector"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request report the connector status.
message ReportStatusRequest {
  // Required. BeyondCorp Connector name using the form:
  // `projects/{project_id}/locations/{location_id}/connectors/{connector}`
  string app_connector = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/AppConnector"
    }
  ];

  // Required. Resource info of the connector.
  ResourceInfo resource_info = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];
}

// A BeyondCorp connector resource that represents an application facing
// component deployed proximal to and with direct access to the application
// instances. It is used to establish connectivity between the remote enterprise
// environment and GCP. It initiates connections to the applications and can
// proxy the data from users over the connection.
message AppConnector {
  option (google.api.resource) = {
    type: "beyondcorp.googleapis.com/AppConnector"
    pattern: "projects/{project}/locations/{location}/appConnectors/{app_connector}"
  };

  // PrincipalInfo represents an Identity oneof.
  message PrincipalInfo {
    // ServiceAccount represents a GCP service account.
    message ServiceAccount {
      // Email address of the service account.
      string email = 1;
    }

    oneof type {
      // A GCP service account.
      ServiceAccount service_account = 1;
    }
  }

  // Represents the different states of a AppConnector.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // AppConnector is being created.
    CREATING = 1;

    // AppConnector has been created.
    CREATED = 2;

    // AppConnector's configuration is being updated.
    UPDATING = 3;

    // AppConnector is being deleted.
    DELETING = 4;

    // AppConnector is down and may be restored in the future.
    // This happens when CCFE sends ProjectState = OFF.
    DOWN = 5;
  }

  // Required. Unique resource name of the AppConnector.
  // The name is ignored when creating a AppConnector.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the resource was last modified.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels to represent user provided metadata.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An arbitrary user-provided name for the AppConnector. Cannot
  // exceed 64 characters.
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A unique identifier for the instance generated by the
  // system.
  string uid = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of the AppConnector.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Principal information about the Identity of the AppConnector.
  PrincipalInfo principal_info = 8 [(google.api.field_behavior) = REQUIRED];

  // Optional. Resource info of the connector.
  ResourceInfo resource_info = 11 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message AppConnectorOperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
