// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.beyondcorp.appconnectors.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BeyondCorp.AppConnectors.V1";
option go_package = "cloud.google.com/go/beyondcorp/appconnectors/apiv1/appconnectorspb;appconnectorspb";
option java_multiple_files = true;
option java_outer_classname = "ResourceInfoProto";
option java_package = "com.google.cloud.beyondcorp.appconnectors.v1";
option php_namespace = "Google\\Cloud\\BeyondCorp\\AppConnectors\\V1";
option ruby_package = "Google::Cloud::BeyondCorp::AppConnectors::V1";

// HealthStatus represents the health status.
enum HealthStatus {
  // Health status is unknown: not initialized or failed to retrieve.
  HEALTH_STATUS_UNSPECIFIED = 0;

  // The resource is healthy.
  HEALTHY = 1;

  // The resource is unhealthy.
  UNHEALTHY = 2;

  // The resource is unresponsive.
  UNRESPONSIVE = 3;

  // Some sub-resources are UNHEALTHY.
  DEGRADED = 4;
}

// ResourceInfo represents the information/status of an app connector resource.
// Such as:
// - remote_agent
//   - container
//     - runtime
//     - appgateway
//       - appconnector
//         - appconnection
//           - tunnel
//       - logagent
message ResourceInfo {
  // Required. Unique Id for the resource.
  string id = 1 [(google.api.field_behavior) = REQUIRED];

  // Overall health status. Overall status is derived based on the status of
  // each sub level resources.
  HealthStatus status = 2;

  // Specific details for the resource. This is for internal use only.
  google.protobuf.Any resource = 3;

  // The timestamp to collect the info. It is suggested to be set by
  // the topmost level resource only.
  google.protobuf.Timestamp time = 4;

  // List of Info for the sub level resources.
  repeated ResourceInfo sub = 5;
}
