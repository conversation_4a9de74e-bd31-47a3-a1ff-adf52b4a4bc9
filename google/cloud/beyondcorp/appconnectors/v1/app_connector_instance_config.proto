// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.beyondcorp.appconnectors.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/any.proto";

option csharp_namespace = "Google.Cloud.BeyondCorp.AppConnectors.V1";
option go_package = "cloud.google.com/go/beyondcorp/appconnectors/apiv1/appconnectorspb;appconnectorspb";
option java_multiple_files = true;
option java_outer_classname = "AppConnectorInstanceConfigProto";
option java_package = "com.google.cloud.beyondcorp.appconnectors.v1";
option php_namespace = "Google\\Cloud\\BeyondCorp\\AppConnectors\\V1";
option ruby_package = "Google::Cloud::BeyondCorp::AppConnectors::V1";

// AppConnectorInstanceConfig defines the instance config of a AppConnector.
message AppConnectorInstanceConfig {
  // Required. A monotonically increasing number generated and maintained
  // by the API provider. Every time a config changes in the backend, the
  // sequenceNumber should be bumped up to reflect the change.
  int64 sequence_number = 1 [(google.api.field_behavior) = REQUIRED];

  // The SLM instance agent configuration.
  google.protobuf.Any instance_config = 2;

  // NotificationConfig defines the notification mechanism that the remote
  // instance should subscribe to in order to receive notification.
  NotificationConfig notification_config = 3;

  // ImageConfig defines the GCR images to run for the remote agent's control
  // plane.
  ImageConfig image_config = 4;
}

// NotificationConfig defines the mechanisms to notify instance agent.
message NotificationConfig {
  // The configuration for Pub/Sub messaging for the AppConnector.
  message CloudPubSubNotificationConfig {
    // The Pub/Sub subscription the AppConnector uses to receive notifications.
    string pubsub_subscription = 1;
  }

  oneof config {
    // Cloud Pub/Sub Configuration to receive notifications.
    CloudPubSubNotificationConfig pubsub_notification = 1;
  }
}

// ImageConfig defines the control plane images to run.
message ImageConfig {
  // The initial image the remote agent will attempt to run for the control
  // plane.
  string target_image = 1;

  // The stable image that the remote agent will fallback to if the target image
  // fails.
  string stable_image = 2;
}
