# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "appgateways_proto",
    srcs = [
        "app_gateways_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "appgateways_proto_with_info",
    deps = [
        ":appgateways_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

java_proto_library(
    name = "appgateways_java_proto",
    deps = [":appgateways_proto"],
)

java_grpc_library(
    name = "appgateways_java_grpc",
    srcs = [":appgateways_proto"],
    deps = [":appgateways_java_proto"],
)

java_gapic_library(
    name = "appgateways_java_gapic",
    srcs = [":appgateways_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "beyondcorp-appgateways_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    test_deps = [
        ":appgateways_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":appgateways_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "appgateways_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.beyondcorp.appgateways.v1.AppGatewaysServiceClientTest",
    ],
    runtime_deps = [":appgateways_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-beyondcorp-appgateways-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":appgateways_java_gapic",
        ":appgateways_java_grpc",
        ":appgateways_java_proto",
        ":appgateways_proto",
    ],
)

go_proto_library(
    name = "appgateways_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/beyondcorp/appgateways/apiv1/appgatewayspb",
    protos = [":appgateways_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "appgateways_go_gapic",
    srcs = [":appgateways_proto_with_info"],
    grpc_service_config = "beyondcorp-appgateways_grpc_service_config.json",
    importpath = "cloud.google.com/go/beyondcorp/appgateways/apiv1;appgateways",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    deps = [
        ":appgateways_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-beyondcorp-appgateways-v1-go",
    deps = [
        ":appgateways_go_gapic",
        ":appgateways_go_gapic_srcjar-metadata.srcjar",
        ":appgateways_go_gapic_srcjar-snippets.srcjar",
        ":appgateways_go_gapic_srcjar-test.srcjar",
        ":appgateways_go_proto",
    ],
)

py_gapic_library(
    name = "appgateways_py_gapic",
    srcs = [":appgateways_proto"],
    grpc_service_config = "beyondcorp-appgateways_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-beyondcorp-appgateways",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=beyondcorp_appgateways",
    ],
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "appgateways_py_gapic_test",
    srcs = [
        "appgateways_py_gapic_pytest.py",
        "appgateways_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":appgateways_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "beyondcorp-appgateways-v1-py",
    deps = [
        ":appgateways_py_gapic",
    ],
)

php_proto_library(
    name = "appgateways_php_proto",
    deps = [":appgateways_proto"],
)

php_gapic_library(
    name = "appgateways_php_gapic",
    srcs = [":appgateways_proto_with_info"],
    grpc_service_config = "beyondcorp-appgateways_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [":appgateways_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-appgateways-v1-php",
    deps = [
        ":appgateways_php_gapic",
        ":appgateways_php_proto",
    ],
)

nodejs_gapic_library(
    name = "appgateways_nodejs_gapic",
    package_name = "@google-cloud/appgateways",
    src = ":appgateways_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "beyondcorp-appgateways_grpc_service_config.json",
    package = "google.cloud.beyondcorp.appgateways.v1",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "beyondcorp-appgateways-v1-nodejs",
    deps = [
        ":appgateways_nodejs_gapic",
        ":appgateways_proto",
    ],
)

ruby_proto_library(
    name = "appgateways_ruby_proto",
    deps = [":appgateways_proto"],
)

ruby_grpc_library(
    name = "appgateways_ruby_grpc",
    srcs = [":appgateways_proto"],
    deps = [":appgateways_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "appgateways_ruby_gapic",
    srcs = [":appgateways_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-beyond_corp-app_gateways-v1",
        "ruby-cloud-product-url=https://cloud.google.com/beyondcorp/",
        "ruby-cloud-api-id=beyondcorp.googleapis.com",
        "ruby-cloud-api-shortname=beyondcorp",
        "ruby-cloud-wrapper-gem-override=google-cloud-beyond_corp",
    ],
    grpc_service_config = "beyondcorp-appgateways_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Beyondcorp Enterprise provides identity and context aware access controls for enterprise resources and enables zero-trust access. Using the Beyondcorp Enterprise APIs, enterprises can set up multi-cloud and on-prem connectivity using the App Connector hybrid connectivity solution.",
    ruby_cloud_title = "BeyondCorp AppGateways V1",
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc",
    deps = [
        ":appgateways_ruby_grpc",
        ":appgateways_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-appgateways-v1-ruby",
    deps = [
        ":appgateways_ruby_gapic",
        ":appgateways_ruby_grpc",
        ":appgateways_ruby_proto",
    ],
)

csharp_proto_library(
    name = "appgateways_csharp_proto",
    deps = [":appgateways_proto"],
)

csharp_grpc_library(
    name = "appgateways_csharp_grpc",
    srcs = [":appgateways_proto"],
    deps = [":appgateways_csharp_proto"],
)

csharp_gapic_library(
    name = "appgateways_csharp_gapic",
    srcs = [":appgateways_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "beyondcorp-appgateways_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "beyondcorp_v1.yaml",
    transport = "grpc",
    deps = [
        ":appgateways_csharp_grpc",
        ":appgateways_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-beyondcorp-appgateways-v1-csharp",
    deps = [
        ":appgateways_csharp_gapic",
        ":appgateways_csharp_grpc",
        ":appgateways_csharp_proto",
    ],
)

cc_proto_library(
    name = "appgateways_cc_proto",
    deps = [":appgateways_proto"],
)

cc_grpc_library(
    name = "appgateways_cc_grpc",
    srcs = [":appgateways_proto"],
    grpc_only = True,
    deps = [":appgateways_cc_proto"],
)
