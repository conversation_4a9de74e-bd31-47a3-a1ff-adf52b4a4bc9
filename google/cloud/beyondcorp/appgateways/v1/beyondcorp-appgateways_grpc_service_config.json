{"methodConfig": [{"name": [{"service": "google.cloud.beyondcorp.appgateways.v1", "method": "ListAppGateways"}, {"service": "google.cloud.beyondcorp.appgateways.v1", "method": "GetAppGateway"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.beyondcorp.appgateways.v1", "method": "CreateAppGateway"}, {"service": "google.cloud.beyondcorp.appgateways.v1", "method": "DeleteAppGateway"}], "timeout": "60s"}]}