// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.beyondcorp.appgateways.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.BeyondCorp.AppGateways.V1";
option go_package = "cloud.google.com/go/beyondcorp/appgateways/apiv1/appgatewayspb;appgatewayspb";
option java_multiple_files = true;
option java_outer_classname = "AppGatewaysServiceProto";
option java_package = "com.google.cloud.beyondcorp.appgateways.v1";
option php_namespace = "Google\\Cloud\\BeyondCorp\\AppGateways\\V1";
option ruby_package = "Google::Cloud::BeyondCorp::AppGateways::V1";

// API Overview:
//
// The `beyondcorp.googleapis.com` service implements the Google Cloud
// BeyondCorp API.
//
// Data Model:
//
// The AppGatewaysService exposes the following resources:
//
// * AppGateways, named as follows:
//   `projects/{project_id}/locations/{location_id}/appGateways/{app_gateway_id}`.
//
// The AppGatewaysService service provides methods to manage
// (create/read/update/delete) BeyondCorp AppGateways.
service AppGatewaysService {
  option (google.api.default_host) = "beyondcorp.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists AppGateways in a given project and location.
  rpc ListAppGateways(ListAppGatewaysRequest)
      returns (ListAppGatewaysResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/appGateways"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single AppGateway.
  rpc GetAppGateway(GetAppGatewayRequest) returns (AppGateway) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/appGateways/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new AppGateway in a given project and location.
  rpc CreateAppGateway(CreateAppGatewayRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/appGateways"
      body: "app_gateway"
    };
    option (google.api.method_signature) = "parent,app_gateway,app_gateway_id";
    option (google.longrunning.operation_info) = {
      response_type: "AppGateway"
      metadata_type: "AppGatewayOperationMetadata"
    };
  }

  // Deletes a single AppGateway.
  rpc DeleteAppGateway(DeleteAppGatewayRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/appGateways/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "AppGatewayOperationMetadata"
    };
  }
}

// Request message for BeyondCorp.ListAppGateways.
message ListAppGatewaysRequest {
  // Required. The resource name of the AppGateway location using the form:
  // `projects/{project_id}/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/AppGateway"
    }
  ];

  // Optional. The maximum number of items to return.
  // If not specified, a default value of 50 will be used by the service.
  // Regardless of the page_size value, the response may include a partial list
  // and a caller should only rely on response's
  // [next_page_token][BeyondCorp.ListAppGatewaysResponse.next_page_token] to
  // determine if there are more instances left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The next_page_token value returned from a previous
  // ListAppGatewaysRequest, if any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter specifying constraints of a list operation.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the ordering of results. See
  // [Sorting
  // order](https://cloud.google.com/apis/design/design_patterns#sorting_order)
  // for more information.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for BeyondCorp.ListAppGateways.
message ListAppGatewaysResponse {
  // A list of BeyondCorp AppGateways in the project.
  repeated AppGateway app_gateways = 1;

  // A token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // A list of locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for BeyondCorp.GetAppGateway.
message GetAppGatewayRequest {
  // Required. BeyondCorp AppGateway name using the form:
  // `projects/{project_id}/locations/{location_id}/appGateways/{app_gateway_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/AppGateway"
    }
  ];
}

// Request message for BeyondCorp.CreateAppGateway.
message CreateAppGatewayRequest {
  // Required. The resource project name of the AppGateway location using the
  // form: `projects/{project_id}/locations/{location_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "beyondcorp.googleapis.com/AppGateway"
    }
  ];

  // Optional. User-settable AppGateway resource ID.
  //  * Must start with a letter.
  //  * Must contain between 4-63 characters from `/[a-z][0-9]-/`.
  //  * Must end with a number or a letter.
  string app_gateway_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A BeyondCorp AppGateway resource.
  AppGateway app_gateway = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for BeyondCorp.DeleteAppGateway.
message DeleteAppGatewayRequest {
  // Required. BeyondCorp AppGateway name using the form:
  // `projects/{project_id}/locations/{location_id}/appGateways/{app_gateway_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "beyondcorp.googleapis.com/AppGateway"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, validates request by executing a dry-run which would not
  // alter the resource in any way.
  bool validate_only = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A BeyondCorp AppGateway resource represents a BeyondCorp protected AppGateway
// to a remote application. It creates all the necessary GCP components needed
// for creating a BeyondCorp protected AppGateway. Multiple connectors can be
// authorised for a single AppGateway.
message AppGateway {
  option (google.api.resource) = {
    type: "beyondcorp.googleapis.com/AppGateway"
    pattern: "projects/{project}/locations/{location}/appGateways/{app_gateway}"
  };

  // Allocated connection of the AppGateway.
  message AllocatedConnection {
    // Required. The PSC uri of an allocated connection
    string psc_uri = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The ingress port of an allocated connection
    int32 ingress_port = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Enum containing list of all possible network connectivity options
  // supported by BeyondCorp AppGateway.
  enum Type {
    // Default value. This value is unused.
    TYPE_UNSPECIFIED = 0;

    // TCP Proxy based BeyondCorp Connection. API will default to this if unset.
    TCP_PROXY = 1;
  }

  // Represents the different states of an AppGateway.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // AppGateway is being created.
    CREATING = 1;

    // AppGateway has been created.
    CREATED = 2;

    // AppGateway's configuration is being updated.
    UPDATING = 3;

    // AppGateway is being deleted.
    DELETING = 4;

    // AppGateway is down and may be restored in the future.
    // This happens when CCFE sends ProjectState = OFF.
    DOWN = 5;
  }

  // Enum containing list of all possible host types supported by BeyondCorp
  // Connection.
  enum HostType {
    // Default value. This value is unused.
    HOST_TYPE_UNSPECIFIED = 0;

    // AppGateway hosted in a GCP regional managed instance group.
    GCP_REGIONAL_MIG = 1;
  }

  // Required. Unique resource name of the AppGateway.
  // The name is ignored when creating an AppGateway.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. Timestamp when the resource was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the resource was last modified.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels to represent user provided metadata.
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. An arbitrary user-provided name for the AppGateway. Cannot exceed
  // 64 characters.
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Output only. A unique identifier for the instance generated by the
  // system.
  string uid = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The type of network connectivity used by the AppGateway.
  Type type = 7 [(google.api.field_behavior) = REQUIRED];

  // Output only. The current state of the AppGateway.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined URI for this resource.
  string uri = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of connections allocated for the Gateway
  repeated AllocatedConnection allocated_connections = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The type of hosting used by the AppGateway.
  HostType host_type = 11 [(google.api.field_behavior) = REQUIRED];
}

// Represents the metadata of the long-running operation.
message AppGatewayOperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
