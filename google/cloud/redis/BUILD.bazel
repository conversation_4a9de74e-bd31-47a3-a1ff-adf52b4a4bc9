# This build file includes a target for the Ruby wrapper library for
# google-cloud-redis.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for redis.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "redis_ruby_wrapper",
    srcs = ["//google/cloud/redis/v1:redis_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-redis",
        "ruby-cloud-env-prefix=REDIS",
        "ruby-cloud-wrapper-of=v1:0.13;v1beta1:0.12",
        "ruby-cloud-product-url=https://cloud.google.com/memorystore/docs/redis",
        "ruby-cloud-api-id=redis.googleapis.com",
        "ruby-cloud-api-shortname=redis",
        "ruby-cloud-migration-version=1.0",
    ],
    ruby_cloud_description = "Creates and manages Redis instances on the Google Cloud Platform.",
    ruby_cloud_title = "Google Cloud Memorystore for Redis",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-redis-ruby",
    deps = [
        ":redis_ruby_wrapper",
    ],
)
