type: google.api.Service
config_version: 3
name: redis.googleapis.com
title: Google Cloud Memorystore for Redis API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.redis.cluster.v1beta1.CloudRedisCluster
- name: google.longrunning.Operations

types:
- name: google.cloud.redis.cluster.v1beta1.OperationMetadata

documentation:
  summary: Creates and manages Redis instances on the Google Cloud Platform.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1beta1/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1beta1/{name=projects/*}/locations'
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1beta1/{name=projects/*/locations/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1beta1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1beta1/{name=projects/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1beta1/{name=projects/*/locations/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.redis.cluster.v1beta1.CloudRedisCluster.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1288776&template=1161103
  documentation_uri: https://cloud.google.com/memorystore/docs/redis
  api_short_name: redis
  github_label: 'api: redis'
  doc_tag_prefix: redis
  organization: CLOUD
  library_settings:
  - version: google.cloud.redis.cluster.v1beta1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
        - GITHUB
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
        - GITHUB
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
        - GITHUB
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
        - GITHUB
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
        - GITHUB
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
        - GITHUB
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
        - GITHUB
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
        - GITHUB
