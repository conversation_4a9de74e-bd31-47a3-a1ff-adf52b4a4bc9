# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "cluster_proto",
    srcs = [
        "cloud_redis_cluster.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:dayofweek_proto",
        "//google/type:timeofday_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "cluster_proto_with_info",
    deps = [
        ":cluster_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "cluster_java_proto",
    deps = [":cluster_proto"],
)

java_grpc_library(
    name = "cluster_java_grpc",
    srcs = [":cluster_proto"],
    deps = [":cluster_java_proto"],
)

java_gapic_library(
    name = "cluster_java_gapic",
    srcs = [":cluster_proto_with_info"],
    gapic_yaml = "redis_gapic.yaml",
    grpc_service_config = "redis_cluster_v1beta1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    test_deps = [
        ":cluster_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":cluster_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "cluster_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.redis.cluster.v1beta1.CloudRedisClusterClientHttpJsonTest",
        "com.google.cloud.redis.cluster.v1beta1.CloudRedisClusterClientTest",
    ],
    runtime_deps = [":cluster_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-redis-cluster-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":cluster_java_gapic",
        ":cluster_java_grpc",
        ":cluster_java_proto",
        ":cluster_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "cluster_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/redis/cluster/apiv1beta1/clusterpb",
    protos = [":cluster_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:dayofweek_go_proto",
        "//google/type:timeofday_go_proto",
    ],
)

go_gapic_library(
    name = "cluster_go_gapic",
    srcs = [":cluster_proto_with_info"],
    grpc_service_config = "redis_cluster_v1beta1_grpc_service_config.json",
    importpath = "cloud.google.com/go/redis/cluster/apiv1beta1;cluster",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":cluster_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-redis-cluster-v1beta1-go",
    deps = [
        ":cluster_go_gapic",
        ":cluster_go_gapic_srcjar-metadata.srcjar",
        ":cluster_go_gapic_srcjar-snippets.srcjar",
        ":cluster_go_gapic_srcjar-test.srcjar",
        ":cluster_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "cluster_py_gapic",
    srcs = [":cluster_proto"],
    grpc_service_config = "redis_cluster_v1beta1_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=redis_cluster",
        "python-gapic-namespace=google.cloud",
    ],
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "cluster_py_gapic_test",
    srcs = [
        "cluster_py_gapic_pytest.py",
        "cluster_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":cluster_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "redis-cluster-v1beta1-py",
    deps = [
        ":cluster_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "cluster_php_proto",
    deps = [":cluster_proto"],
)

php_gapic_library(
    name = "cluster_php_gapic",
    srcs = [":cluster_proto_with_info"],
    grpc_service_config = "redis_cluster_v1beta1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":cluster_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-redis-cluster-v1beta1-php",
    deps = [
        ":cluster_php_gapic",
        ":cluster_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "cluster_nodejs_gapic",
    package_name = "@google-cloud/redis-cluster",
    src = ":cluster_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "redis_cluster_v1beta1_grpc_service_config.json",
    package = "google.cloud.redis.cluster.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "redis-cluster-v1beta1-nodejs",
    deps = [
        ":cluster_nodejs_gapic",
        ":cluster_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "cluster_ruby_proto",
    deps = [":cluster_proto"],
)

ruby_grpc_library(
    name = "cluster_ruby_grpc",
    srcs = [":cluster_proto"],
    deps = [":cluster_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "cluster_ruby_gapic",
    srcs = [":cluster_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-redis-cluster-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/memorystore/docs/cluster",
    ],
    grpc_service_config = "redis_cluster_v1beta1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":cluster_ruby_grpc",
        ":cluster_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-redis-cluster-v1beta1-ruby",
    deps = [
        ":cluster_ruby_gapic",
        ":cluster_ruby_grpc",
        ":cluster_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "cluster_csharp_proto",
    deps = [":cluster_proto"],
)

csharp_grpc_library(
    name = "cluster_csharp_grpc",
    srcs = [":cluster_proto"],
    deps = [":cluster_csharp_proto"],
)

csharp_gapic_library(
    name = "cluster_csharp_gapic",
    srcs = [":cluster_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "redis_cluster_v1beta1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":cluster_csharp_grpc",
        ":cluster_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-redis-cluster-v1beta1-csharp",
    deps = [
        ":cluster_csharp_gapic",
        ":cluster_csharp_grpc",
        ":cluster_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "cluster_cc_proto",
    deps = [":cluster_proto"],
)

cc_grpc_library(
    name = "cluster_cc_grpc",
    srcs = [":cluster_proto"],
    grpc_only = True,
    deps = [":cluster_cc_proto"],
)
