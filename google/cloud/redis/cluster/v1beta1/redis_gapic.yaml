type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
# A list of API interface configurations.
interfaces:
# The fully qualified name of the API interface.
- name: google.cloud.redis.cluster.v1beta1.CloudRedisCluster
  methods:
  - name: CreateCluster
    long_running:
      initial_poll_delay_millis: 60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 360000  # 6 minutes
      total_poll_timeout_millis: 7200000  # 120 minutes
  - name: UpdateCluster
    long_running:
      initial_poll_delay_millis: 60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 360000  # 6 minutes
      total_poll_timeout_millis: 7200000  # 120 minutes
  - name: DeleteCluster
    long_running:
      initial_poll_delay_millis: 60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 360000  # 6 minutes
      total_poll_timeout_millis: 1200000  # 20 minutes
