{"methodConfig": [{"name": [{"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "ListClusters"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "GetCluster"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "CreateCluster"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "UpdateCluster"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "DeleteCluster"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "GetClusterCertificateAuthority"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "RescheduleClusterMaintenance"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "BackupCluster"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "ListBackups"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "GetBackup"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "DeleteBackup"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "ExportBackup"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "ListBackupCollections"}, {"service": "google.cloud.redis.cluster.v1.CloudRedisCluster", "method": "GetBackupCollection"}], "timeout": "600s"}]}