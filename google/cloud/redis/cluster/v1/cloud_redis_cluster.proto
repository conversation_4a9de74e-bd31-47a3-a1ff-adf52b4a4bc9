// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.redis.cluster.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/any.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "google/type/timeofday.proto";

option go_package = "cloud.google.com/go/redis/cluster/apiv1/clusterpb;clusterpb";
option java_multiple_files = true;
option java_outer_classname = "CloudRedisClusterProto";
option java_package = "com.google.cloud.redis.cluster.v1";
option ruby_package = "Google::Cloud::Redis::Cluster::V1";
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/ForwardingRule"
  pattern: "projects/{project}/regions/{region}/forwardingRules/{forwarding_rule}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/Network"
  pattern: "projects/{project}/global/networks/{network}"
};
option (google.api.resource_definition) = {
  type: "compute.googleapis.com/ServiceAttachment"
  pattern: "projects/{project}/regions/{region}/serviceAttachments/{service_attachment}"
};
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKey"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}"
};
option (google.api.resource_definition) = {
  type: "cloudkms.googleapis.com/CryptoKeyVersion"
  pattern: "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}/cryptoKeyVersions/{crypto_key_version}"
};

// Configures and manages Cloud Memorystore for Redis clusters
//
// Google Cloud Memorystore for Redis Cluster
//
// The `redis.googleapis.com` service implements the Google Cloud Memorystore
// for Redis API and defines the following resource model for managing Redis
// clusters:
// * The service works with a collection of cloud projects, named: `/projects/*`
// * Each project has a collection of available locations, named: `/locations/*`
// * Each location has a collection of Redis clusters, named: `/clusters/*`
// * As such, Redis clusters are resources of the form:
//   `/projects/{project_id}/locations/{location_id}/clusters/{instance_id}`
//
// Note that location_id must be a GCP `region`; for example:
// * `projects/redpepper-1290/locations/us-central1/clusters/my-redis`
service CloudRedisCluster {
  option (google.api.default_host) = "redis.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all Redis clusters owned by a project in either the specified
  // location (region) or all locations.
  //
  // The location should have the following format:
  //
  // * `projects/{project_id}/locations/{location_id}`
  //
  // If `location_id` is specified as `-` (wildcard), then all regions
  // available to the project are queried, and the results are aggregated.
  rpc ListClusters(ListClustersRequest) returns (ListClustersResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/clusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a specific Redis cluster.
  rpc GetCluster(GetClusterRequest) returns (Cluster) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the metadata and configuration of a specific Redis cluster.
  //
  // Completed longrunning.Operation will contain the new cluster object
  // in the response field. The returned operation is automatically deleted
  // after a few hours, so there is no need to call DeleteOperation.
  rpc UpdateCluster(UpdateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{cluster.name=projects/*/locations/*/clusters/*}"
      body: "cluster"
    };
    option (google.api.method_signature) = "cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "google.protobuf.Any"
    };
  }

  // Deletes a specific Redis cluster. Cluster stops serving and data is
  // deleted.
  rpc DeleteCluster(DeleteClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Any"
    };
  }

  // Creates a Redis cluster based on the specified properties.
  // The creation is executed asynchronously and callers may check the returned
  // operation to track its progress. Once the operation is completed the Redis
  // cluster will be fully functional. The completed longrunning.Operation will
  // contain the new cluster object in the response field.
  //
  // The returned operation is automatically deleted after a few hours, so there
  // is no need to call DeleteOperation.
  rpc CreateCluster(CreateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/clusters"
      body: "cluster"
    };
    option (google.api.method_signature) = "parent,cluster,cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "google.protobuf.Any"
    };
  }

  // Gets the details of certificate authority information for Redis cluster.
  rpc GetClusterCertificateAuthority(GetClusterCertificateAuthorityRequest)
      returns (CertificateAuthority) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/clusters/*/certificateAuthority}"
    };
    option (google.api.method_signature) = "name";
  }

  // Reschedules upcoming maintenance event.
  rpc RescheduleClusterMaintenance(RescheduleClusterMaintenanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/clusters/*}:rescheduleClusterMaintenance"
      body: "*"
    };
    option (google.api.method_signature) = "name,reschedule_type,schedule_time";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "google.protobuf.Any"
    };
  }

  // Lists all backup collections owned by a consumer project in either the
  // specified location (region) or all locations.
  //
  // If `location_id` is specified as `-` (wildcard), then all regions
  // available to the project are queried, and the results are aggregated.
  rpc ListBackupCollections(ListBackupCollectionsRequest)
      returns (ListBackupCollectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/backupCollections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Get a backup collection.
  rpc GetBackupCollection(GetBackupCollectionRequest)
      returns (BackupCollection) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupCollections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all backups owned by a backup collection.
  rpc ListBackups(ListBackupsRequest) returns (ListBackupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/backupCollections/*}/backups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a specific backup.
  rpc GetBackup(GetBackupRequest) returns (Backup) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/backupCollections/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes a specific backup.
  rpc DeleteBackup(DeleteBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/backupCollections/*/backups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.protobuf.Any"
    };
  }

  // Exports a specific backup to a customer target Cloud Storage URI.
  rpc ExportBackup(ExportBackupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/backupCollections/*/backups/*}:export"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Backup"
      metadata_type: "google.protobuf.Any"
    };
  }

  // Backup Redis Cluster.
  // If this is the first time a backup is being created, a backup collection
  // will be created at the backend, and this backup belongs to this collection.
  // Both collection and backup will have a resource name. Backup will be
  // executed for each shard. A replica (primary if nonHA) will be selected to
  // perform the execution. Backup call will be rejected if there is an ongoing
  // backup or update operation. Be aware that during preview, if the cluster's
  // internal software version is too old, critical update will be performed
  // before actual backup. Once the internal software version is updated to the
  // minimum version required by the backup feature, subsequent backups will not
  // require critical update. After preview, there will be no critical update
  // needed for backup.
  rpc BackupCluster(BackupClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/clusters/*}:backup"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "google.protobuf.Any"
    };
  }
}

// Status of the PSC connection.
enum PscConnectionStatus {
  // PSC connection status is not specified.
  PSC_CONNECTION_STATUS_UNSPECIFIED = 0;

  // The connection is active
  PSC_CONNECTION_STATUS_ACTIVE = 1;

  // Connection not found
  PSC_CONNECTION_STATUS_NOT_FOUND = 2;
}

// Available authorization mode of a Redis cluster.
enum AuthorizationMode {
  // Not set.
  AUTH_MODE_UNSPECIFIED = 0;

  // IAM basic authorization mode
  AUTH_MODE_IAM_AUTH = 1;

  // Authorization disabled mode
  AUTH_MODE_DISABLED = 2;
}

// NodeType of a redis cluster node,
enum NodeType {
  // Node type unspecified
  NODE_TYPE_UNSPECIFIED = 0;

  // Redis shared core nano node_type.
  REDIS_SHARED_CORE_NANO = 1;

  // Redis highmem medium node_type.
  REDIS_HIGHMEM_MEDIUM = 2;

  // Redis highmem xlarge node_type.
  REDIS_HIGHMEM_XLARGE = 3;

  // Redis standard small node_type.
  REDIS_STANDARD_SMALL = 4;
}

// Available mode of in-transit encryption.
enum TransitEncryptionMode {
  // In-transit encryption not set.
  TRANSIT_ENCRYPTION_MODE_UNSPECIFIED = 0;

  // In-transit encryption disabled.
  TRANSIT_ENCRYPTION_MODE_DISABLED = 1;

  // Use server managed encryption for in-transit encryption.
  TRANSIT_ENCRYPTION_MODE_SERVER_AUTHENTICATION = 2;
}

// Type of a PSC connection, for cluster access purpose.
enum ConnectionType {
  // Cluster endpoint Type is not set
  CONNECTION_TYPE_UNSPECIFIED = 0;

  // Cluster endpoint that will be used as for cluster topology discovery.
  CONNECTION_TYPE_DISCOVERY = 1;

  // Cluster endpoint that will be used as primary endpoint to access primary.
  CONNECTION_TYPE_PRIMARY = 2;

  // Cluster endpoint that will be used as reader endpoint to access replicas.
  CONNECTION_TYPE_READER = 3;
}

// Request for [CreateCluster][CloudRedis.CreateCluster].
message CreateClusterRequest {
  // Required. The resource name of the cluster location using the form:
  //     `projects/{project_id}/locations/{location_id}`
  // where `location_id` refers to a GCP region.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The logical name of the Redis cluster in the customer project
  // with the following restrictions:
  //
  // * Must contain only lowercase letters, numbers, and hyphens.
  // * Must start with a letter.
  // * Must be between 1-63 characters.
  // * Must end with a number or a letter.
  // * Must be unique within the customer project / location
  string cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The cluster that is to be created.
  Cluster cluster = 3 [(google.api.field_behavior) = REQUIRED];

  // Idempotent request UUID.
  string request_id = 4;
}

// Request for [ListClusters][CloudRedis.ListClusters].
message ListClustersRequest {
  // Required. The resource name of the cluster location using the form:
  //     `projects/{project_id}/locations/{location_id}`
  // where `location_id` refers to a GCP region.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return.
  //
  // If not specified, a default value of 1000 will be used by the service.
  // Regardless of the page_size value, the response may include a partial list
  // and a caller should only rely on response's
  // [`next_page_token`][google.cloud.redis.cluster.v1.ListClustersResponse.next_page_token]
  // to determine if there are more clusters left to be queried.
  int32 page_size = 2;

  // The `next_page_token` value returned from a previous
  // [ListClusters][CloudRedis.ListClusters] request, if any.
  string page_token = 3;
}

// Response for [ListClusters][CloudRedis.ListClusters].
message ListClustersResponse {
  // A list of Redis clusters in the project in the specified location,
  // or across all locations.
  //
  // If the `location_id` in the parent field of the request is "-", all regions
  // available to the project are queried, and the results aggregated.
  // If in such an aggregated query a location is unavailable, a placeholder
  // Redis entry is included in the response with the `name` field set to a
  // value of the form
  // `projects/{project_id}/locations/{location_id}/clusters/`- and the
  // `status` field set to ERROR and `status_message` field set to "location not
  // available for ListClusters".
  repeated Cluster clusters = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for [UpdateCluster][CloudRedis.UpdateCluster].
message UpdateClusterRequest {
  // Required. Mask of fields to update. At least one path must be supplied in
  // this field. The elements of the repeated paths field may only include these
  // fields from [Cluster][google.cloud.redis.cluster.v1.Cluster]:
  //
  //  *   `size_gb`
  //  *   `replica_count`
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Update description.
  // Only fields specified in update_mask are updated.
  Cluster cluster = 2 [(google.api.field_behavior) = REQUIRED];

  // Idempotent request UUID.
  string request_id = 3;
}

// Request for [GetCluster][CloudRedis.GetCluster].
message GetClusterRequest {
  // Required. Redis cluster resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Cluster" }
  ];
}

// Request for [DeleteCluster][CloudRedis.DeleteCluster].
message DeleteClusterRequest {
  // Required. Redis cluster resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Cluster" }
  ];

  // Idempotent request UUID.
  string request_id = 2;
}

// Request for
// [GetClusterCertificateAuthorityRequest][CloudRedis.GetClusterCertificateAuthorityRequest].
message GetClusterCertificateAuthorityRequest {
  // Required. Redis cluster certificate authority resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}/certificateAuthority`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "redis.googleapis.com/CertificateAuthority"
    }
  ];
}

// Request for [ListBackupCollections]
message ListBackupCollectionsRequest {
  // Required. The resource name of the backupCollection location using the
  // form:
  //     `projects/{project_id}/locations/{location_id}`
  // where `location_id` refers to a GCP region.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "redis.googleapis.com/BackupCollection"
    }
  ];

  // Optional. The maximum number of items to return.
  //
  // If not specified, a default value of 1000 will be used by the service.
  // Regardless of the page_size value, the response may include a partial list
  // and a caller should only rely on response's
  // [`next_page_token`][google.cloud.redis.cluster.v1.ListBackupCollectionsResponse.next_page_token]
  // to determine if there are more clusters left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The `next_page_token` value returned from a previous
  // [ListBackupCollections] request, if any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response for [ListBackupCollections].
message ListBackupCollectionsResponse {
  // A list of backupCollections in the project.
  //
  // If the `location_id` in the parent field of the request is "-", all regions
  // available to the project are queried, and the results aggregated.
  // If in such an aggregated query a location is unavailable, a placeholder
  // backupCollection entry is included in the response with the `name` field
  // set to a value of the form
  // `projects/{project_id}/locations/{location_id}/backupCollections/`- and the
  // `status` field set to ERROR and `status_message` field set to "location not
  // available for ListBackupCollections".
  repeated BackupCollection backup_collections = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for [GetBackupCollection].
message GetBackupCollectionRequest {
  // Required. Redis backupCollection resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/backupCollections/{backup_collection_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "redis.googleapis.com/BackupCollection"
    }
  ];
}

// Request for [ListBackups].
message ListBackupsRequest {
  // Required. The resource name of the backupCollection using the form:
  // `projects/{project_id}/locations/{location_id}/backupCollections/{backup_collection_id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "redis.googleapis.com/Backup"
    }
  ];

  // Optional. The maximum number of items to return.
  //
  // If not specified, a default value of 1000 will be used by the service.
  // Regardless of the page_size value, the response may include a partial list
  // and a caller should only rely on response's
  // [`next_page_token`][google.cloud.redis.cluster.v1.ListBackupsResponse.next_page_token]
  // to determine if there are more clusters left to be queried.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The `next_page_token` value returned from a previous
  // [ListBackupCollections] request, if any.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response for [ListBackups].
message ListBackupsResponse {
  // A list of backups in the project.
  repeated Backup backups = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Backups that could not be reached.
  repeated string unreachable = 3;
}

// Request for [GetBackup].
message GetBackupRequest {
  // Required. Redis backup resource name using the form:
  // `projects/{project_id}/locations/{location_id}/backupCollections/{backup_collection_id}/backups/{backup_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Backup" }
  ];
}

// Request for [DeleteBackup].
message DeleteBackupRequest {
  // Required. Redis backup resource name using the form:
  // `projects/{project_id}/locations/{location_id}/backupCollections/{backup_collection_id}/backups/{backup_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Backup" }
  ];

  // Optional. Idempotent request UUID.
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Request for [ExportBackup].
message ExportBackupRequest {
  // Required. Specify destination to export a backup.
  oneof destination {
    // Google Cloud Storage bucket, like "my-bucket".
    string gcs_bucket = 3;
  }

  // Required. Redis backup resource name using the form:
  // `projects/{project_id}/locations/{location_id}/backupCollections/{backup_collection_id}/backups/{backup_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Backup" }
  ];
}

// Request for [BackupCluster].
message BackupClusterRequest {
  // Required. Redis cluster resource name using the form:
  //  `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Cluster" }
  ];

  // Optional. TTL for the backup to expire. Value range is 1 day to 100 years.
  // If not specified, the default value is 100 years.
  google.protobuf.Duration ttl = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The id of the backup to be created. If not specified, the
  // default value ([YYYYMMDDHHMMSS]_[Shortened Cluster UID] is used.
  optional string backup_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// A cluster instance.
message Cluster {
  option (google.api.resource) = {
    type: "redis.googleapis.com/Cluster"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}"
    plural: "clusters"
    singular: "cluster"
  };

  // Represents additional information about the state of the cluster.
  message StateInfo {
    // Represents information about an updating cluster.
    message UpdateInfo {
      // Target number of shards for redis cluster
      optional int32 target_shard_count = 1;

      // Target number of replica nodes per shard.
      optional int32 target_replica_count = 2;
    }

    oneof info {
      // Describes ongoing update on the cluster when cluster state is UPDATING.
      UpdateInfo update_info = 1;
    }
  }

  // Backups stored in Cloud Storage buckets.
  // The Cloud Storage buckets need to be the same region as the clusters.
  message GcsBackupSource {
    // Optional. URIs of the GCS objects to import.
    // Example: gs://bucket1/object1, gs://bucket2/folder2/object2
    repeated string uris = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Backups that generated and managed by memorystore.
  message ManagedBackupSource {
    // Optional. Example:
    // //redis.googleapis.com/projects/{project}/locations/{location}/backupCollections/{collection}/backups/{backup}
    // A shorter version (without the prefix) of the backup name is also
    // supported, like
    // projects/{project}/locations/{location}/backupCollections/{collection}/backups/{backup_id}
    // In this case, it assumes the backup is under redis.googleapis.com.
    string backup = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Represents the different states of a Redis cluster.
  enum State {
    // Not set.
    STATE_UNSPECIFIED = 0;

    // Redis cluster is being created.
    CREATING = 1;

    // Redis cluster has been created and is fully usable.
    ACTIVE = 2;

    // Redis cluster configuration is being updated.
    UPDATING = 3;

    // Redis cluster is being deleted.
    DELETING = 4;
  }

  // The source to import from.
  oneof import_sources {
    // Optional. Backups stored in Cloud Storage buckets.
    // The Cloud Storage buckets need to be the same region as the clusters.
    // Read permission is required to import from the provided Cloud Storage
    // objects.
    GcsBackupSource gcs_source = 34 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Backups generated and managed by memorystore service.
    ManagedBackupSource managed_backup_source = 35
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Required. Identifier. Unique name of the resource in this scope including
  // project and location using the form:
  //     `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. The timestamp associated with the cluster creation request.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of this cluster.
  // Can be CREATING, READY, UPDATING, DELETING and SUSPENDED
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. System assigned, unique identifier for the cluster.
  string uid = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The number of replica nodes per shard.
  optional int32 replica_count = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The authorization mode of the Redis cluster.
  // If not provided, auth feature is disabled for the cluster.
  AuthorizationMode authorization_mode = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The in-transit encryption for the Redis cluster.
  // If not provided, encryption  is disabled for the cluster.
  TransitEncryptionMode transit_encryption_mode = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Redis memory size in GB for the entire cluster rounded up to
  // the next integer.
  optional int32 size_gb = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Number of shards for the Redis cluster.
  optional int32 shard_count = 14 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Each PscConfig configures the consumer network where IPs will
  // be designated to the cluster for client access through Private Service
  // Connect Automation. Currently, only one PscConfig is supported.
  repeated PscConfig psc_configs = 15 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Endpoints created on each given network, for Redis clients to
  // connect to the cluster. Currently only one discovery endpoint is supported.
  repeated DiscoveryEndpoint discovery_endpoints = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of PSC connections that are auto-created through
  // service connectivity automation.
  repeated PscConnection psc_connections = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the current state of the cluster.
  StateInfo state_info = 18 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The type of a redis node in the cluster. NodeType determines the
  // underlying machine-type of a redis node.
  NodeType node_type = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Persistence config (RDB, AOF) for the cluster.
  ClusterPersistenceConfig persistence_config = 20
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Key/Value pairs of customer overrides for mutable Redis Configs
  map<string, string> redis_configs = 21
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Precise value of redis memory size in GB for the entire
  // cluster.
  optional double precise_size_gb = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. This config will be used to determine how the customer wants us
  // to distribute cluster resources within the region.
  ZoneDistributionConfig zone_distribution_config = 23
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Cross cluster replication config.
  CrossClusterReplicationConfig cross_cluster_replication_config = 24
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The delete operation will fail when the value is set to true.
  optional bool deletion_protection_enabled = 25
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. ClusterMaintenancePolicy determines when to allow or deny
  // updates.
  optional ClusterMaintenancePolicy maintenance_policy = 26
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. ClusterMaintenanceSchedule Output only Published maintenance
  // schedule.
  optional ClusterMaintenanceSchedule maintenance_schedule = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Service attachment details to configure Psc connections
  repeated PscServiceAttachment psc_service_attachments = 30
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A list of cluster enpoints.
  repeated ClusterEndpoint cluster_endpoints = 36
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Output only. The backup collection full resource name. Example:
  // projects/{project}/locations/{location}/backupCollections/{collection}
  optional string backup_collection = 39 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "redis.googleapis.com/BackupCollection"
    }
  ];

  // Optional. The KMS key used to encrypt the at-rest data of the cluster.
  optional string kms_key = 40 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Optional. The automated backup config for the cluster.
  AutomatedBackupConfig automated_backup_config = 42
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Encryption information of the data at rest of the cluster.
  EncryptionInfo encryption_info = 43
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The automated backup config for a cluster.
message AutomatedBackupConfig {
  // This schedule allows the backup to be triggered at a fixed frequency
  // (currently only daily is supported).
  message FixedFrequencySchedule {
    // Required. The start time of every automated backup in UTC. It must be set
    // to the start of an hour. This field is required.
    optional google.type.TimeOfDay start_time = 2
        [(google.api.field_behavior) = REQUIRED];
  }

  // The automated backup mode.
  enum AutomatedBackupMode {
    // Default value. Automated backup config is not specified.
    AUTOMATED_BACKUP_MODE_UNSPECIFIED = 0;

    // Automated backup config disabled.
    DISABLED = 1;

    // Automated backup config enabled.
    ENABLED = 2;
  }

  // The schedule of automated backups.
  oneof schedule {
    // Optional. Trigger automated backups at a fixed frequency.
    FixedFrequencySchedule fixed_frequency_schedule = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. The automated backup mode. If the mode is disabled, the other
  // fields will be ignored.
  AutomatedBackupMode automated_backup_mode = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. How long to keep automated backups before the backups are
  // deleted. The value should be between 1 day and 365 days. If not specified,
  // the default value is 35 days.
  optional google.protobuf.Duration retention = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// BackupCollection of a cluster.
message BackupCollection {
  option (google.api.resource) = {
    type: "redis.googleapis.com/BackupCollection"
    pattern: "projects/{project}/locations/{location}/backupCollections/{backup_collection}"
    plural: "backupCollections"
    singular: "backupCollection"
  };

  // Identifier. Full resource path of the backup collection.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The cluster uid of the backup collection.
  string cluster_uid = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. The full resource path of the cluster the backup collection
  // belongs to. Example:
  // projects/{project}/locations/{location}/clusters/{cluster}
  string cluster = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Cluster" }
  ];

  // Output only. The KMS key used to encrypt the backups under this backup
  // collection.
  string kms_key = 5 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Output only. System assigned unique identifier of the backup collection.
  string uid = 6 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];
}

// Backup of a cluster.
message Backup {
  option (google.api.resource) = {
    type: "redis.googleapis.com/Backup"
    pattern: "projects/{project}/locations/{location}/backupCollections/{backup_collection}/backups/{backup}"
    plural: "backups"
    singular: "backup"
  };

  // Type of the backup.
  enum BackupType {
    // The default value, not set.
    BACKUP_TYPE_UNSPECIFIED = 0;

    // On-demand backup.
    ON_DEMAND = 1;

    // Automated backup.
    AUTOMATED = 2;
  }

  // State of the backup.
  enum State {
    // The default value, not set.
    STATE_UNSPECIFIED = 0;

    // The backup is being created.
    CREATING = 1;

    // The backup is active to be used.
    ACTIVE = 2;

    // The backup is being deleted.
    DELETING = 3;

    // The backup is currently suspended due to reasons like project deletion,
    // billing account closure, etc.
    SUSPENDED = 4;
  }

  // Identifier. Full resource path of the backup. the last part of the name is
  // the backup id with the following format: [YYYYMMDDHHMMSS]_[Shorted Cluster
  // UID] OR customer specified while backup cluster. Example:
  // 20240515123000_1234
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The time when the backup was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cluster resource path of this backup.
  string cluster = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Cluster" }
  ];

  // Output only. Cluster uid of this backup.
  string cluster_uid = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. Total size of the backup in bytes.
  int64 total_size_bytes = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the backup will expire.
  google.protobuf.Timestamp expire_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. redis-7.2, valkey-7.5
  string engine_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of backup files of the backup.
  repeated BackupFile backup_files = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Node type of the cluster.
  NodeType node_type = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of replicas for the cluster.
  int32 replica_count = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of shards for the cluster.
  int32 shard_count = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the backup.
  BackupType backup_type = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the backup.
  State state = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Encryption information of the backup.
  EncryptionInfo encryption_info = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. System assigned unique identifier of the backup.
  string uid = 15 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];
}

// Backup is consisted of multiple backup files.
message BackupFile {
  // Output only. e.g: <shard-id>.rdb
  string file_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Size of the backup file in bytes.
  int64 size_bytes = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the backup file was created.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configuration of a service attachment of the cluster, for creating PSC
// connections.
message PscServiceAttachment {
  // Output only. Service attachment URI which your self-created PscConnection
  // should use as target
  string service_attachment = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of a PSC connection targeting this service attachment.
  ConnectionType connection_type = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Cross cluster replication config.
message CrossClusterReplicationConfig {
  // Details of the remote cluster associated with this cluster in a cross
  // cluster replication setup.
  message RemoteCluster {
    // The full resource path of the remote cluster in
    // the format: projects/<project>/locations/<region>/clusters/<cluster-id>
    string cluster = 1 [
      (google.api.resource_reference) = { type: "redis.googleapis.com/Cluster" }
    ];

    // Output only. The unique identifier of the remote cluster.
    string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // An output only view of all the member clusters participating in the cross
  // cluster replication.
  message Membership {
    // Output only. The primary cluster that acts as the source of replication
    // for the secondary clusters.
    RemoteCluster primary_cluster = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The list of secondary clusters replicating from the primary
    // cluster.
    repeated RemoteCluster secondary_clusters = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The role of the cluster in cross cluster replication.
  enum ClusterRole {
    // Cluster role is not set.
    // The behavior is equivalent to NONE.
    CLUSTER_ROLE_UNSPECIFIED = 0;

    // This cluster does not participate in cross cluster replication. It is an
    // independent cluster and does not replicate to or from any other clusters.
    NONE = 1;

    // A cluster that allows both reads and writes. Any data written to this
    // cluster is also replicated to the attached secondary clusters.
    PRIMARY = 2;

    // A cluster that allows only reads and replicates data from a primary
    // cluster.
    SECONDARY = 3;
  }

  // The role of the cluster in cross cluster replication.
  ClusterRole cluster_role = 1;

  // Details of the primary cluster that is used as the replication source for
  // this secondary cluster.
  //
  // This field is only set for a secondary cluster.
  RemoteCluster primary_cluster = 2;

  // List of secondary clusters that are replicating from this primary cluster.
  //
  // This field is only set for a primary cluster.
  repeated RemoteCluster secondary_clusters = 3;

  // Output only. The last time cross cluster replication config was updated.
  google.protobuf.Timestamp update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An output only view of all the member clusters participating
  // in the cross cluster replication. This view will be provided by every
  // member cluster irrespective of its cluster role(primary or secondary).
  //
  // A primary cluster can provide information about all the secondary clusters
  // replicating from it. However, a secondary cluster only knows about the
  // primary cluster from which it is replicating. However, for scenarios, where
  // the primary cluster is unavailable(e.g. regional outage), a GetCluster
  // request can be sent to any other member cluster and this field will list
  // all the member clusters participating in cross cluster replication.
  Membership membership = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Maintenance policy per cluster.
message ClusterMaintenancePolicy {
  // Output only. The time when the policy was created i.e. Maintenance Window
  // or Deny Period was assigned.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the policy was updated i.e. Maintenance Window
  // or Deny Period was updated.
  google.protobuf.Timestamp update_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Maintenance window that is applied to resources covered by this
  // policy. Minimum 1. For the current version, the maximum number of
  // weekly_maintenance_window is expected to be one.
  repeated ClusterWeeklyMaintenanceWindow weekly_maintenance_window = 3;
}

// Time window specified for weekly operations.
message ClusterWeeklyMaintenanceWindow {
  // Allows to define schedule that runs specified day of the week.
  google.type.DayOfWeek day = 1;

  // Start time of the window in UTC.
  google.type.TimeOfDay start_time = 2;
}

// Upcoming maitenance schedule.
message ClusterMaintenanceSchedule {
  // Output only. The start time of any upcoming scheduled maintenance for this
  // instance.
  google.protobuf.Timestamp start_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The end time of any upcoming scheduled maintenance for this
  // instance.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

message PscConfig {
  // Required. The network where the IP address of the discovery endpoint will
  // be reserved, in the form of
  // projects/{network_project}/global/networks/{network_id}.
  string network = 2 [(google.api.field_behavior) = REQUIRED];
}

// Endpoints on each network, for Redis clients to connect to the cluster.
message DiscoveryEndpoint {
  // Output only. Address of the exposed Redis endpoint used by clients to
  // connect to the service. The address could be either IP or hostname.
  string address = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The port number of the exposed Redis endpoint.
  int32 port = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Customer configuration for where the endpoint is created and
  // accessed from.
  PscConfig psc_config = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Details of consumer resources in a PSC connection.
message PscConnection {
  // Required. The PSC connection id of the forwarding rule connected to the
  // service attachment.
  string psc_connection_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The IP allocated on the consumer network for the PSC forwarding
  // rule.
  string address = 2 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = REQUIRED
  ];

  // Required. The URI of the consumer side forwarding rule.
  // Example:
  // projects/{projectNumOrId}/regions/us-east1/forwardingRules/{resourceId}.
  string forwarding_rule = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/ForwardingRule"
    }
  ];

  // Optional. Project ID of the consumer project where the forwarding rule is
  // created in.
  string project_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. The consumer network where the IP address resides, in the form of
  // projects/{project_id}/global/networks/{network_id}.
  string network = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Required. The service attachment which is the target of the PSC connection,
  // in the form of
  // projects/{project-id}/regions/{region}/serviceAttachments/{service-attachment-id}.
  string service_attachment = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/ServiceAttachment"
    }
  ];

  // Output only. The status of the PSC connection.
  // Please note that this value is updated periodically.
  // To get the latest status of a PSC connection, follow
  // https://cloud.google.com/vpc/docs/configure-private-service-connect-services#endpoint-details.
  PscConnectionStatus psc_connection_status = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the PSC connection.
  ConnectionType connection_type = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// ClusterEndpoint consists of PSC connections that are created
// as a group in each VPC network for accessing the cluster. In each group,
// there shall be one connection for each service attachment in the cluster.
message ClusterEndpoint {
  // A group of PSC connections. They are created in the same VPC network, one
  // for each service attachment in the cluster.
  repeated ConnectionDetail connections = 1;
}

// Detailed information of each PSC connection.
message ConnectionDetail {
  // A PSC connection to a cluster could either be created through Service
  // Connectivity Automation (auto-registered connection) during the cluster
  // creation, or it could be created by customer themselves (user-registered
  // connection).
  oneof connection {
    // Detailed information of a PSC connection that is created through
    // service connectivity automation.
    PscAutoConnection psc_auto_connection = 1;

    // Detailed information of a PSC connection that is created by the customer
    // who owns the cluster.
    PscConnection psc_connection = 2;
  }
}

// Details of consumer resources in a PSC connection that is created through
// Service Connectivity Automation.
message PscAutoConnection {
  // Output only. The PSC connection id of the forwarding rule connected to the
  // service attachment.
  string psc_connection_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The IP allocated on the consumer network for the PSC
  // forwarding rule.
  string address = 2 [
    (google.api.field_info).format = IPV4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. The URI of the consumer side forwarding rule.
  // Example:
  // projects/{projectNumOrId}/regions/us-east1/forwardingRules/{resourceId}.
  string forwarding_rule = 3 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/ForwardingRule"
    }
  ];

  // Required. The consumer project_id where the forwarding rule is created
  // from.
  string project_id = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The consumer network where the IP address resides, in the form of
  // projects/{project_id}/global/networks/{network_id}.
  string network = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "compute.googleapis.com/Network" }
  ];

  // Output only. The service attachment which is the target of the PSC
  // connection, in the form of
  // projects/{project-id}/regions/{region}/serviceAttachments/{service-attachment-id}.
  string service_attachment = 6 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/ServiceAttachment"
    }
  ];

  // Output only. The status of the PSC connection.
  // Please note that this value is updated periodically.
  // Please use Private Service Connect APIs for the latest status.
  PscConnectionStatus psc_connection_status = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Type of the PSC connection.
  ConnectionType connection_type = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Pre-defined metadata fields.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Redis cluster certificate authority
message CertificateAuthority {
  option (google.api.resource) = {
    type: "redis.googleapis.com/CertificateAuthority"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/certificateAuthority"
    plural: "certificateAuthorities"
    singular: "certificateAuthority"
  };

  message ManagedCertificateAuthority {
    message CertChain {
      // The certificates that form the CA chain, from leaf to root order.
      repeated string certificates = 1;
    }

    // The PEM encoded CA certificate chains for redis managed
    // server authentication
    repeated CertChain ca_certs = 1;
  }

  // server ca information
  oneof server_ca {
    ManagedCertificateAuthority managed_server_ca = 1;
  }

  // Identifier. Unique name of the resource in this scope including project,
  // location and cluster using the form:
  //     `projects/{project}/locations/{location}/clusters/{cluster}/certificateAuthority`
  string name = 2 [(google.api.field_behavior) = IDENTIFIER];
}

// Configuration of the persistence functionality.
message ClusterPersistenceConfig {
  // Configuration of the RDB based persistence.
  message RDBConfig {
    // Available snapshot periods.
    enum SnapshotPeriod {
      // Not set.
      SNAPSHOT_PERIOD_UNSPECIFIED = 0;

      // One hour.
      ONE_HOUR = 1;

      // Six hours.
      SIX_HOURS = 2;

      // Twelve hours.
      TWELVE_HOURS = 3;

      // Twenty four hours.
      TWENTY_FOUR_HOURS = 4;
    }

    // Optional. Period between RDB snapshots.
    SnapshotPeriod rdb_snapshot_period = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The time that the first snapshot was/will be attempted, and to
    // which future snapshots will be aligned. If not provided, the current time
    // will be used.
    google.protobuf.Timestamp rdb_snapshot_start_time = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Configuration of the AOF based persistence.
  message AOFConfig {
    // Available fsync modes.
    enum AppendFsync {
      // Not set. Default: EVERYSEC
      APPEND_FSYNC_UNSPECIFIED = 0;

      // Never fsync. Normally Linux will flush data every 30 seconds with this
      // configuration, but it's up to the kernel's exact tuning.
      NO = 1;

      // fsync every second. Fast enough, and you may lose 1 second of data if
      // there is a disaster
      EVERYSEC = 2;

      // fsync every time new write commands are appended to the AOF. It has the
      // best data loss protection at the cost of performance
      ALWAYS = 3;
    }

    // Optional. fsync configuration.
    AppendFsync append_fsync = 1 [(google.api.field_behavior) = OPTIONAL];
  }

  // Available persistence modes.
  enum PersistenceMode {
    // Not set.
    PERSISTENCE_MODE_UNSPECIFIED = 0;

    // Persistence is disabled, and any snapshot data is deleted.
    DISABLED = 1;

    // RDB based persistence is enabled.
    RDB = 2;

    // AOF based persistence is enabled.
    AOF = 3;
  }

  // Optional. The mode of persistence.
  PersistenceMode mode = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. RDB configuration. This field will be ignored if mode is not RDB.
  RDBConfig rdb_config = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. AOF configuration. This field will be ignored if mode is not AOF.
  AOFConfig aof_config = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Zone distribution config for allocation of cluster resources.
message ZoneDistributionConfig {
  // Defines various modes of zone distribution.
  enum ZoneDistributionMode {
    // Not Set. Default: MULTI_ZONE
    ZONE_DISTRIBUTION_MODE_UNSPECIFIED = 0;

    // Distribute all resources across 3 zones picked at random, within the
    // region.
    MULTI_ZONE = 1;

    // Distribute all resources in a single zone. The zone field must be
    // specified, when this mode is selected.
    SINGLE_ZONE = 2;
  }

  // Optional. The mode of zone distribution. Defaults to MULTI_ZONE, when not
  // specified.
  ZoneDistributionMode mode = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. When SINGLE ZONE distribution is selected, zone field would be
  // used to allocate all resources in that zone. This is not applicable to
  // MULTI_ZONE, and would be ignored for MULTI_ZONE clusters.
  string zone = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request for rescheduling a cluster maintenance.
message RescheduleClusterMaintenanceRequest {
  // Reschedule options.
  enum RescheduleType {
    // Not set.
    RESCHEDULE_TYPE_UNSPECIFIED = 0;

    // If the user wants to schedule the maintenance to happen now.
    IMMEDIATE = 1;

    // If the user wants to reschedule the maintenance to a specific time.
    SPECIFIC_TIME = 3;
  }

  // Required. Redis Cluster instance resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Cluster" }
  ];

  // Required. If reschedule type is SPECIFIC_TIME, must set up schedule_time as
  // well.
  RescheduleType reschedule_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Timestamp when the maintenance shall be rescheduled to if
  // reschedule_type=SPECIFIC_TIME, in RFC 3339 format, for
  // example `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp schedule_time = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// EncryptionInfo describes the encryption information of a cluster or a backup.
message EncryptionInfo {
  // Possible encryption types.
  enum Type {
    // Encryption type not specified. Defaults to GOOGLE_DEFAULT_ENCRYPTION.
    TYPE_UNSPECIFIED = 0;

    // The data is encrypted at rest with a key that is fully managed by Google.
    // No key version will be populated. This is the default state.
    GOOGLE_DEFAULT_ENCRYPTION = 1;

    // The data is encrypted at rest with a key that is managed by the customer.
    // KMS key versions will be populated.
    CUSTOMER_MANAGED_ENCRYPTION = 2;
  }

  // The state of the KMS key perceived by the system. Refer to the public
  // documentation for the impact of each state.
  enum KmsKeyState {
    // The default value. This value is unused.
    KMS_KEY_STATE_UNSPECIFIED = 0;

    // The KMS key is enabled and correctly configured.
    ENABLED = 1;

    // Permission denied on the KMS key.
    PERMISSION_DENIED = 2;

    // The KMS key is disabled.
    DISABLED = 3;

    // The KMS key is destroyed.
    DESTROYED = 4;

    // The KMS key is scheduled to be destroyed.
    DESTROY_SCHEDULED = 5;

    // The EKM key is unreachable.
    EKM_KEY_UNREACHABLE_DETECTED = 6;

    // Billing is disabled for the project.
    BILLING_DISABLED = 7;

    // All other unknown failures.
    UNKNOWN_FAILURE = 8;
  }

  // Output only. Type of encryption.
  Type encryption_type = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. KMS key versions that are being used to protect the data
  // at-rest.
  repeated string kms_key_versions = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKeyVersion"
    }
  ];

  // Output only. The state of the primary version of the KMS key perceived by
  // the system. This field is not populated in backups.
  KmsKeyState kms_key_primary_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The most recent time when the encryption info was updated.
  google.protobuf.Timestamp last_update_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
