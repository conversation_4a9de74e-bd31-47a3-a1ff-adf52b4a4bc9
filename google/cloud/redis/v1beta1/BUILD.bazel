# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "redis_proto",
    srcs = [
        "cloud_redis.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "//google/type:dayofweek_proto",
        "//google/type:timeofday_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "redis_proto_with_info",
    deps = [
        ":redis_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "redis_java_proto",
    deps = [":redis_proto"],
)

java_grpc_library(
    name = "redis_java_grpc",
    srcs = [":redis_proto"],
    deps = [":redis_java_proto"],
)

java_gapic_library(
    name = "redis_java_gapic",
    srcs = [":redis_proto_with_info"],
    gapic_yaml = "redis_gapic.yaml",
    grpc_service_config = "redis_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    test_deps = [
        ":redis_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":redis_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "redis_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.redis.v1beta1.CloudRedisClientHttpJsonTest",
        "com.google.cloud.redis.v1beta1.CloudRedisClientTest",
    ],
    runtime_deps = [":redis_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-redis-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":redis_java_gapic",
        ":redis_java_grpc",
        ":redis_java_proto",
        ":redis_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "redis_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/redis/apiv1beta1/redispb",
    protos = [":redis_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:dayofweek_go_proto",
        "//google/type:timeofday_go_proto",
    ],
)

go_gapic_library(
    name = "redis_go_gapic",
    srcs = [":redis_proto_with_info"],
    grpc_service_config = "redis_grpc_service_config.json",
    importpath = "cloud.google.com/go/redis/apiv1beta1;redis",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":redis_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-redis-v1beta1-go",
    deps = [
        ":redis_go_gapic",
        ":redis_go_gapic_srcjar-metadata.srcjar",
        ":redis_go_gapic_srcjar-snippets.srcjar",
        ":redis_go_gapic_srcjar-test.srcjar",
        ":redis_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "redis_py_gapic",
    srcs = [":redis_proto"],
    grpc_service_config = "redis_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "redis_py_gapic_test",
    srcs = [
        "redis_py_gapic_pytest.py",
        "redis_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":redis_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "redis-v1beta1-py",
    deps = [
        ":redis_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "redis_php_proto",
    deps = [":redis_proto"],
)

php_gapic_library(
    name = "redis_php_gapic",
    srcs = [":redis_proto_with_info"],
    grpc_service_config = "redis_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":redis_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-redis-v1beta1-php",
    deps = [
        ":redis_php_gapic",
        ":redis_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "redis_nodejs_gapic",
    package_name = "@google-cloud/redis",
    src = ":redis_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "redis_grpc_service_config.json",
    package = "google.cloud.redis.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "redis-v1beta1-nodejs",
    deps = [
        ":redis_nodejs_gapic",
        ":redis_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "redis_ruby_proto",
    deps = [":redis_proto"],
)

ruby_grpc_library(
    name = "redis_ruby_grpc",
    srcs = [":redis_proto"],
    deps = [":redis_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "redis_ruby_gapic",
    srcs = [":redis_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=redis.googleapis.com",
        "ruby-cloud-api-shortname=redis",
        "ruby-cloud-env-prefix=REDIS",
        "ruby-cloud-gem-name=google-cloud-redis-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/memorystore/docs/redis",
    ],
    grpc_service_config = "redis_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Creates and manages Redis instances on the Google Cloud Platform.",
    ruby_cloud_title = "Google Cloud Memorystore for Redis V1beta1",
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":redis_ruby_grpc",
        ":redis_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-redis-v1beta1-ruby",
    deps = [
        ":redis_ruby_gapic",
        ":redis_ruby_grpc",
        ":redis_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "redis_csharp_proto",
    deps = [":redis_proto"],
)

csharp_grpc_library(
    name = "redis_csharp_grpc",
    srcs = [":redis_proto"],
    deps = [":redis_csharp_proto"],
)

csharp_gapic_library(
    name = "redis_csharp_gapic",
    srcs = [":redis_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "redis_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "redis_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":redis_csharp_grpc",
        ":redis_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-redis-v1beta1-csharp",
    deps = [
        ":redis_csharp_gapic",
        ":redis_csharp_grpc",
        ":redis_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "redis_cc_proto",
    deps = [":redis_proto"],
)

cc_grpc_library(
    name = "redis_cc_grpc",
    srcs = [":redis_proto"],
    grpc_only = True,
    deps = [":redis_cc_proto"],
)
