type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
# A list of API interface configurations.
interfaces:
# The fully qualified name of the API interface.
- name: google.cloud.redis.v1.CloudRedis
  methods:
  - name: CreateInstance
    long_running:
      initial_poll_delay_millis: 60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 360000  # 6 minutes
      total_poll_timeout_millis: 7200000  # 120 minutes
  - name: UpdateInstance
    long_running:
      initial_poll_delay_millis: 60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 360000  # 6 minutes
      total_poll_timeout_millis: 7200000  # 120 minutes
  - name: ImportInstance
    long_running:
      initial_poll_delay_millis: 60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 360000  # 6 minutes
      total_poll_timeout_millis: 18000000  # 300 minutes
  - name: ExportInstance
    long_running:
      initial_poll_delay_millis: 60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 360000  # 6 minutes
      total_poll_timeout_millis: 18000000  # 300 minutes
  - name: FailoverInstance
    long_running:
      initial_poll_delay_millis: 60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 360000  # 6 minutes
      total_poll_timeout_millis: 1200000  # 20 minutes
  - name: DeleteInstance
    long_running:
      initial_poll_delay_millis: 60000  # 1 minutes
      poll_delay_multiplier: 1.5
      max_poll_delay_millis: 360000  # 6 minutes
      total_poll_timeout_millis: 1200000  # 20 minutes
