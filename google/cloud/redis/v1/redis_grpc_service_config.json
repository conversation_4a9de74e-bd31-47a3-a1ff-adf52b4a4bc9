{"methodConfig": [{"name": [{"service": "google.cloud.redis.v1.CloudRedis", "method": "ListInstances"}, {"service": "google.cloud.redis.v1.CloudRedis", "method": "GetInstance"}, {"service": "google.cloud.redis.v1.CloudRedis", "method": "GetInstanceAuthString"}, {"service": "google.cloud.redis.v1.CloudRedis", "method": "RescheduleMaintenance"}, {"service": "google.cloud.redis.v1.CloudRedis", "method": "CreateInstance"}, {"service": "google.cloud.redis.v1.CloudRedis", "method": "UpdateInstance"}, {"service": "google.cloud.redis.v1.CloudRedis", "method": "UpgradeInstance"}, {"service": "google.cloud.redis.v1.CloudRedis", "method": "ImportInstance"}, {"service": "google.cloud.redis.v1.CloudRedis", "method": "ExportInstance"}, {"service": "google.cloud.redis.v1.CloudRedis", "method": "FailoverInstance"}, {"service": "google.cloud.redis.v1.CloudRedis", "method": "DeleteInstance"}], "timeout": "600s"}]}