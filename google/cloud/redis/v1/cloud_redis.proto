// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.redis.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "google/type/timeofday.proto";

option go_package = "cloud.google.com/go/redis/apiv1/redispb;redispb";
option java_multiple_files = true;
option java_outer_classname = "CloudRedisServiceV1Proto";
option java_package = "com.google.cloud.redis.v1";

// Configures and manages Cloud Memorystore for Redis instances
//
// Google Cloud Memorystore for Redis v1
//
// The `redis.googleapis.com` service implements the Google Cloud Memorystore
// for Redis API and defines the following resource model for managing Redis
// instances:
// * The service works with a collection of cloud projects, named: `/projects/*`
// * Each project has a collection of available locations, named: `/locations/*`
// * Each location has a collection of Redis instances, named: `/instances/*`
// * As such, Redis instances are resources of the form:
//   `/projects/{project_id}/locations/{location_id}/instances/{instance_id}`
//
// Note that location_id must be referring to a GCP `region`; for example:
// * `projects/redpepper-1290/locations/us-central1/instances/my-redis`
service CloudRedis {
  option (google.api.default_host) = "redis.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists all Redis instances owned by a project in either the specified
  // location (region) or all locations.
  //
  // The location should have the following format:
  //
  // * `projects/{project_id}/locations/{location_id}`
  //
  // If `location_id` is specified as `-` (wildcard), then all regions
  // available to the project are queried, and the results are aggregated.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/instances"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a specific Redis instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets the AUTH string for a Redis instance. If AUTH is not enabled for the
  // instance the response will be empty. This information is not included in
  // the details returned to GetInstance.
  rpc GetInstanceAuthString(GetInstanceAuthStringRequest)
      returns (InstanceAuthString) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/instances/*}/authString"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a Redis instance based on the specified tier and memory size.
  //
  // By default, the instance is accessible from the project's
  // [default network](https://cloud.google.com/vpc/docs/vpc).
  //
  // The creation is executed asynchronously and callers may check the returned
  // operation to track its progress. Once the operation is completed the Redis
  // instance will be fully functional. Completed longrunning.Operation will
  // contain the new instance object in the response field.
  //
  // The returned operation is automatically deleted after a few hours, so there
  // is no need to call DeleteOperation.
  rpc CreateInstance(CreateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/instances"
      body: "instance"
    };
    option (google.api.method_signature) = "parent,instance_id,instance";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.redis.v1.Instance"
      metadata_type: "google.cloud.redis.v1.OperationMetadata"
    };
  }

  // Updates the metadata and configuration of a specific Redis instance.
  //
  // Completed longrunning.Operation will contain the new instance object
  // in the response field. The returned operation is automatically deleted
  // after a few hours, so there is no need to call DeleteOperation.
  rpc UpdateInstance(UpdateInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{instance.name=projects/*/locations/*/instances/*}"
      body: "instance"
    };
    option (google.api.method_signature) = "update_mask,instance";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.redis.v1.Instance"
      metadata_type: "google.cloud.redis.v1.OperationMetadata"
    };
  }

  // Upgrades Redis instance to the newer Redis version specified in the
  // request.
  rpc UpgradeInstance(UpgradeInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:upgrade"
      body: "*"
    };
    option (google.api.method_signature) = "name,redis_version";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.redis.v1.Instance"
      metadata_type: "google.cloud.redis.v1.OperationMetadata"
    };
  }

  // Import a Redis RDB snapshot file from Cloud Storage into a Redis instance.
  //
  // Redis may stop serving during this operation. Instance state will be
  // IMPORTING for entire operation. When complete, the instance will contain
  // only data from the imported file.
  //
  // The returned operation is automatically deleted after a few hours, so
  // there is no need to call DeleteOperation.
  rpc ImportInstance(ImportInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:import"
      body: "*"
    };
    option (google.api.method_signature) = "name,input_config";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.redis.v1.Instance"
      metadata_type: "google.cloud.redis.v1.OperationMetadata"
    };
  }

  // Export Redis instance data into a Redis RDB format file in Cloud Storage.
  //
  // Redis will continue serving during this operation.
  //
  // The returned operation is automatically deleted after a few hours, so
  // there is no need to call DeleteOperation.
  rpc ExportInstance(ExportInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:export"
      body: "*"
    };
    option (google.api.method_signature) = "name,output_config";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.redis.v1.Instance"
      metadata_type: "google.cloud.redis.v1.OperationMetadata"
    };
  }

  // Initiates a failover of the primary node to current replica node for a
  // specific STANDARD tier Cloud Memorystore for Redis instance.
  rpc FailoverInstance(FailoverInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:failover"
      body: "*"
    };
    option (google.api.method_signature) = "name,data_protection_mode";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.redis.v1.Instance"
      metadata_type: "google.cloud.redis.v1.OperationMetadata"
    };
  }

  // Deletes a specific Redis instance.  Instance stops serving and data is
  // deleted.
  rpc DeleteInstance(DeleteInstanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "google.cloud.redis.v1.OperationMetadata"
    };
  }

  // Reschedule maintenance for a given instance in a given project and
  // location.
  rpc RescheduleMaintenance(RescheduleMaintenanceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/instances/*}:rescheduleMaintenance"
      body: "*"
    };
    option (google.api.method_signature) =
        "name, reschedule_type, schedule_time";
    option (google.longrunning.operation_info) = {
      response_type: "google.cloud.redis.v1.Instance"
      metadata_type: "google.cloud.redis.v1.OperationMetadata"
    };
  }
}

// Node specific properties.
message NodeInfo {
  // Output only. Node identifying string. e.g. 'node-0', 'node-1'
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Location of the node.
  string zone = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A Memorystore for Redis instance.
message Instance {
  option (google.api.resource) = {
    type: "redis.googleapis.com/Instance"
    pattern: "projects/{project}/locations/{location}/instances/{instance}"
  };

  // Represents the different states of a Redis instance.
  enum State {
    // Not set.
    STATE_UNSPECIFIED = 0;

    // Redis instance is being created.
    CREATING = 1;

    // Redis instance has been created and is fully usable.
    READY = 2;

    // Redis instance configuration is being updated. Certain kinds of updates
    // may cause the instance to become unusable while the update is in
    // progress.
    UPDATING = 3;

    // Redis instance is being deleted.
    DELETING = 4;

    // Redis instance is being repaired and may be unusable.
    REPAIRING = 5;

    // Maintenance is being performed on this Redis instance.
    MAINTENANCE = 6;

    // Redis instance is importing data (availability may be affected).
    IMPORTING = 8;

    // Redis instance is failing over (availability may be affected).
    FAILING_OVER = 9;
  }

  // Available service tiers to choose from
  enum Tier {
    // Not set.
    TIER_UNSPECIFIED = 0;

    // BASIC tier: standalone instance
    BASIC = 1;

    // STANDARD_HA tier: highly available primary/replica instances
    STANDARD_HA = 3;
  }

  // Available connection modes.
  enum ConnectMode {
    // Not set.
    CONNECT_MODE_UNSPECIFIED = 0;

    // Connect via direct peering to the Memorystore for Redis hosted service.
    DIRECT_PEERING = 1;

    // Connect your Memorystore for Redis instance using Private Service
    // Access. Private services access provides an IP address range for multiple
    // Google Cloud services, including Memorystore.
    PRIVATE_SERVICE_ACCESS = 2;
  }

  // Available TLS modes.
  enum TransitEncryptionMode {
    // Not set.
    TRANSIT_ENCRYPTION_MODE_UNSPECIFIED = 0;

    // Client to Server traffic encryption enabled with server authentication.
    SERVER_AUTHENTICATION = 1;

    // TLS is disabled for the instance.
    DISABLED = 2;
  }

  // Read replicas mode.
  enum ReadReplicasMode {
    // If not set, Memorystore Redis backend will default to
    // READ_REPLICAS_DISABLED.
    READ_REPLICAS_MODE_UNSPECIFIED = 0;

    // If disabled, read endpoint will not be provided and the instance cannot
    // scale up or down the number of replicas.
    READ_REPLICAS_DISABLED = 1;

    // If enabled, read endpoint will be provided and the instance can scale
    // up and down the number of replicas. Not valid for basic tier.
    READ_REPLICAS_ENABLED = 2;
  }

  // Possible reasons for the instance to be in a "SUSPENDED" state.
  enum SuspensionReason {
    // Not set.
    SUSPENSION_REASON_UNSPECIFIED = 0;

    // Something wrong with the CMEK key provided by customer.
    CUSTOMER_MANAGED_KEY_ISSUE = 1;
  }

  // Required. Unique name of the resource in this scope including project and
  // location using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  //
  // Note: Redis instances are managed and addressed at regional level so
  // location_id here refers to a GCP region; however, users may choose which
  // specific zone (or collection of zones for cross-zone instances) an instance
  // should be provisioned in. Refer to
  // [location_id][google.cloud.redis.v1.Instance.location_id] and
  // [alternative_location_id][google.cloud.redis.v1.Instance.alternative_location_id]
  // fields for more details.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // An arbitrary and optional user-provided name for the instance.
  string display_name = 2;

  // Resource labels to represent user provided metadata
  map<string, string> labels = 3;

  // Optional. The zone where the instance will be provisioned. If not provided,
  // the service will choose a zone from the specified region for the instance.
  // For standard tier, additional nodes will be added across multiple zones for
  // protection against zonal failures. If specified, at least one node will be
  // provisioned in this zone.
  string location_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If specified, at least one node will be provisioned in this zone
  // in addition to the zone specified in location_id. Only applicable to
  // standard tier. If provided, it must be a different zone from the one
  // provided in [location_id]. Additional nodes beyond the first 2 will be
  // placed in zones selected by the service.
  string alternative_location_id = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The version of Redis software.
  // If not provided, latest supported version will be used. Currently, the
  // supported values are:
  //
  //  *   `REDIS_3_2` for Redis 3.2 compatibility
  //  *   `REDIS_4_0` for Redis 4.0 compatibility (default)
  //  *   `REDIS_5_0` for Redis 5.0 compatibility
  //  *   `REDIS_6_X` for Redis 6.x compatibility
  string redis_version = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. For DIRECT_PEERING mode, the CIDR range of internal addresses
  // that are reserved for this instance. Range must
  // be unique and non-overlapping with existing subnets in an authorized
  // network. For PRIVATE_SERVICE_ACCESS mode, the name of one allocated IP
  // address ranges associated with this private service access connection.
  // If not provided, the service will choose an unused /29 block, for
  // example, 10.0.0.0/29 or ***********/29.  For READ_REPLICAS_ENABLED
  // the default block size is /28.
  string reserved_ip_range = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Additional IP range for node placement. Required when enabling
  // read replicas on an existing instance. For DIRECT_PEERING mode value must
  // be a CIDR range of size /28, or "auto". For PRIVATE_SERVICE_ACCESS mode
  // value must be the name of an allocated address range associated with the
  // private service access connection, or "auto".
  string secondary_ip_range = 30 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Hostname or IP address of the exposed Redis endpoint used by
  // clients to connect to the service.
  string host = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The port number of the exposed Redis endpoint.
  int32 port = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current zone where the Redis primary node is located. In
  // basic tier, this will always be the same as [location_id]. In
  // standard tier, this can be the zone of any node in the instance.
  string current_location_id = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the instance was created.
  google.protobuf.Timestamp create_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current state of this instance.
  State state = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Additional information about the current status of this
  // instance, if available.
  string status_message = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Redis configuration parameters, according to
  // http://redis.io/topics/config. Currently, the only supported parameters
  // are:
  //
  //  Redis version 3.2 and newer:
  //
  //  *   maxmemory-policy
  //  *   notify-keyspace-events
  //
  //  Redis version 4.0 and newer:
  //
  //  *   activedefrag
  //  *   lfu-decay-time
  //  *   lfu-log-factor
  //  *   maxmemory-gb
  //
  //  Redis version 5.0 and newer:
  //
  //  *   stream-node-max-bytes
  //  *   stream-node-max-entries
  map<string, string> redis_configs = 16
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The service tier of the instance.
  Tier tier = 17 [(google.api.field_behavior) = REQUIRED];

  // Required. Redis memory size in GiB.
  int32 memory_size_gb = 18 [(google.api.field_behavior) = REQUIRED];

  // Optional. The full name of the Google Compute Engine
  // [network](https://cloud.google.com/vpc/docs/vpc) to which the
  // instance is connected. If left unspecified, the `default` network
  // will be used.
  string authorized_network = 20 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Cloud IAM identity used by import / export operations to
  // transfer data to/from Cloud Storage. Format is
  // "serviceAccount:<service_account_email>". The value may change over time
  // for a given instance so should be checked before each import/export
  // operation.
  string persistence_iam_identity = 21
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The network connect mode of the Redis instance.
  // If not provided, the connect mode defaults to DIRECT_PEERING.
  ConnectMode connect_mode = 22 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates whether OSS Redis AUTH is enabled for the instance. If
  // set to "true" AUTH is enabled on the instance. Default value is "false"
  // meaning AUTH is disabled.
  bool auth_enabled = 23 [(google.api.field_behavior) = OPTIONAL];

  // Output only. List of server CA certificates for the instance.
  repeated TlsCertificate server_ca_certs = 25
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The TLS mode of the Redis instance.
  // If not provided, TLS is disabled for the instance.
  TransitEncryptionMode transit_encryption_mode = 26
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maintenance policy for the instance. If not provided,
  // maintenance events can be performed at any time.
  MaintenancePolicy maintenance_policy = 27
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. Date and time of upcoming maintenance events which have been
  // scheduled.
  MaintenanceSchedule maintenance_schedule = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The number of replica nodes. The valid range for the Standard
  // Tier with read replicas enabled is [1-5] and defaults to 2. If read
  // replicas are not enabled for a Standard Tier instance, the only valid value
  // is 1 and the default is 1. The valid value for basic tier is 0 and the
  // default is also 0.
  int32 replica_count = 31 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Info per node.
  repeated NodeInfo nodes = 32 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Hostname or IP address of the exposed readonly Redis
  // endpoint. Standard tier only. Targets all healthy replica nodes in
  // instance. Replication is asynchronous and replica nodes will exhibit some
  // lag behind the primary. Write requests must target 'host'.
  string read_endpoint = 33 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The port number of the exposed readonly redis
  // endpoint. Standard tier only. Write requests should target 'port'.
  int32 read_endpoint_port = 34 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Read replicas mode for the instance. Defaults to
  // READ_REPLICAS_DISABLED.
  ReadReplicasMode read_replicas_mode = 35
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The KMS key reference that the customer provides when trying to
  // create the instance.
  string customer_managed_key = 36 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Persistence configuration parameters
  PersistenceConfig persistence_config = 37
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. reasons that causes instance in "SUSPENDED" state.
  repeated SuspensionReason suspension_reasons = 38
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The self service update maintenance version.
  // The version is date based such as "20210712_00_00".
  string maintenance_version = 39 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The available maintenance versions that an instance could update
  // to.
  repeated string available_maintenance_versions = 40
      [(google.api.field_behavior) = OPTIONAL];
}

// Configuration of the persistence functionality.
message PersistenceConfig {
  // Available Persistence modes.
  enum PersistenceMode {
    // Not set.
    PERSISTENCE_MODE_UNSPECIFIED = 0;

    // Persistence is disabled for the instance,
    // and any existing snapshots are deleted.
    DISABLED = 1;

    // RDB based Persistence is enabled.
    RDB = 2;
  }

  // Available snapshot periods for scheduling.
  enum SnapshotPeriod {
    // Not set.
    SNAPSHOT_PERIOD_UNSPECIFIED = 0;

    // Snapshot every 1 hour.
    ONE_HOUR = 3;

    // Snapshot every 6 hours.
    SIX_HOURS = 4;

    // Snapshot every 12 hours.
    TWELVE_HOURS = 5;

    // Snapshot every 24 hours.
    TWENTY_FOUR_HOURS = 6;
  }

  // Optional. Controls whether Persistence features are enabled.
  // If not provided, the existing value will be used.
  PersistenceMode persistence_mode = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Period between RDB snapshots. Snapshots will be attempted every
  // period starting from the provided snapshot start time. For example, a start
  // time of 01/01/2033 06:45 and SIX_HOURS snapshot period will do nothing
  // until 01/01/2033, and then trigger snapshots every day at 06:45, 12:45,
  // 18:45, and 00:45 the next day, and so on. If not provided,
  // TWENTY_FOUR_HOURS will be used as default.
  SnapshotPeriod rdb_snapshot_period = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The next time that a snapshot attempt is scheduled to occur.
  google.protobuf.Timestamp rdb_next_snapshot_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Date and time that the first snapshot was/will be attempted, and
  // to which future snapshots will be aligned. If not provided, the current
  // time will be used.
  google.protobuf.Timestamp rdb_snapshot_start_time = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// Request for
// [RescheduleMaintenance][google.cloud.redis.v1.CloudRedis.RescheduleMaintenance].
message RescheduleMaintenanceRequest {
  // Reschedule options.
  enum RescheduleType {
    // Not set.
    RESCHEDULE_TYPE_UNSPECIFIED = 0;

    // If the user wants to schedule the maintenance to happen now.
    IMMEDIATE = 1;

    // If the user wants to use the existing maintenance policy to find the
    // next available window.
    NEXT_AVAILABLE_WINDOW = 2;

    // If the user wants to reschedule the maintenance to a specific time.
    SPECIFIC_TIME = 3;
  }

  // Required. Redis instance resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Instance" }
  ];

  // Required. If reschedule type is SPECIFIC_TIME, must set up schedule_time as
  // well.
  RescheduleType reschedule_type = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Timestamp when the maintenance shall be rescheduled to if
  // reschedule_type=SPECIFIC_TIME, in RFC 3339 format, for
  // example `2012-11-15T16:19:00.094Z`.
  google.protobuf.Timestamp schedule_time = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Maintenance policy for an instance.
message MaintenancePolicy {
  // Output only. The time when the policy was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the policy was last updated.
  google.protobuf.Timestamp update_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Description of what this policy is for. Create/Update methods
  // return INVALID_ARGUMENT if the length is greater than 512.
  string description = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maintenance window that is applied to resources covered by this
  // policy. Minimum 1. For the current version, the maximum number of
  // weekly_window is expected to be one.
  repeated WeeklyMaintenanceWindow weekly_maintenance_window = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Time window in which disruptive maintenance updates occur. Non-disruptive
// updates can occur inside or outside this window.
message WeeklyMaintenanceWindow {
  // Required. The day of week that maintenance updates occur.
  google.type.DayOfWeek day = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Start time of the window in UTC time.
  google.type.TimeOfDay start_time = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Duration of the maintenance window. The current window is
  // fixed at 1 hour.
  google.protobuf.Duration duration = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Upcoming maintenance schedule. If no maintenance is scheduled, fields are not
// populated.
message MaintenanceSchedule {
  // Output only. The start time of any upcoming scheduled maintenance for this
  // instance.
  google.protobuf.Timestamp start_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The end time of any upcoming scheduled maintenance for this
  // instance.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // If the scheduled maintenance can be rescheduled, default is true.
  bool can_reschedule = 3 [deprecated = true];

  // Output only. The deadline that the maintenance schedule start time can not
  // go beyond, including reschedule.
  google.protobuf.Timestamp schedule_deadline_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request for [ListInstances][google.cloud.redis.v1.CloudRedis.ListInstances].
message ListInstancesRequest {
  // Required. The resource name of the instance location using the form:
  //     `projects/{project_id}/locations/{location_id}`
  // where `location_id` refers to a GCP region.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return.
  //
  // If not specified, a default value of 1000 will be used by the service.
  // Regardless of the page_size value, the response may include a partial list
  // and a caller should only rely on response's
  // [`next_page_token`][google.cloud.redis.v1.ListInstancesResponse.next_page_token]
  // to determine if there are more instances left to be queried.
  int32 page_size = 2;

  // The `next_page_token` value returned from a previous
  // [ListInstances][google.cloud.redis.v1.CloudRedis.ListInstances] request, if
  // any.
  string page_token = 3;
}

// Response for [ListInstances][google.cloud.redis.v1.CloudRedis.ListInstances].
message ListInstancesResponse {
  // A list of Redis instances in the project in the specified location,
  // or across all locations.
  //
  // If the `location_id` in the parent field of the request is "-", all regions
  // available to the project are queried, and the results aggregated.
  // If in such an aggregated query a location is unavailable, a placeholder
  // Redis entry is included in the response with the `name` field set to a
  // value of the form
  // `projects/{project_id}/locations/{location_id}/instances/`- and the
  // `status` field set to ERROR and `status_message` field set to "location not
  // available for ListInstances".
  repeated Instance instances = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for [GetInstance][google.cloud.redis.v1.CloudRedis.GetInstance].
message GetInstanceRequest {
  // Required. Redis instance resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Instance" }
  ];
}

// Request for
// [GetInstanceAuthString][google.cloud.redis.v1.CloudRedis.GetInstanceAuthString].
message GetInstanceAuthStringRequest {
  // Required. Redis instance resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Instance" }
  ];
}

// Instance AUTH string details.
message InstanceAuthString {
  // AUTH string set on the instance.
  string auth_string = 1;
}

// Request for
// [CreateInstance][google.cloud.redis.v1.CloudRedis.CreateInstance].
message CreateInstanceRequest {
  // Required. The resource name of the instance location using the form:
  //     `projects/{project_id}/locations/{location_id}`
  // where `location_id` refers to a GCP region.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The logical name of the Redis instance in the customer project
  // with the following restrictions:
  //
  // * Must contain only lowercase letters, numbers, and hyphens.
  // * Must start with a letter.
  // * Must be between 1-40 characters.
  // * Must end with a number or a letter.
  // * Must be unique within the customer project / location
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. A Redis [Instance] resource
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for
// [UpdateInstance][google.cloud.redis.v1.CloudRedis.UpdateInstance].
message UpdateInstanceRequest {
  // Required. Mask of fields to update. At least one path must be supplied in
  // this field. The elements of the repeated paths field may only include these
  // fields from [Instance][google.cloud.redis.v1.Instance]:
  //
  //  *   `displayName`
  //  *   `labels`
  //  *   `memorySizeGb`
  //  *   `redisConfig`
  //  *   `replica_count`
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Update description.
  // Only fields specified in update_mask are updated.
  Instance instance = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for
// [UpgradeInstance][google.cloud.redis.v1.CloudRedis.UpgradeInstance].
message UpgradeInstanceRequest {
  // Required. Redis instance resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Instance" }
  ];

  // Required. Specifies the target version of Redis software to upgrade to.
  string redis_version = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for
// [DeleteInstance][google.cloud.redis.v1.CloudRedis.DeleteInstance].
message DeleteInstanceRequest {
  // Required. Redis instance resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Instance" }
  ];
}

// The Cloud Storage location for the input content
message GcsSource {
  // Required. Source data URI. (e.g. 'gs://my_bucket/my_object').
  string uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// The input content
message InputConfig {
  // Required. Specify source location of input data
  oneof source {
    // Google Cloud Storage location where input content is located.
    GcsSource gcs_source = 1;
  }
}

// Request for [Import][google.cloud.redis.v1.CloudRedis.ImportInstance].
message ImportInstanceRequest {
  // Required. Redis instance resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Specify data to be imported.
  InputConfig input_config = 3 [(google.api.field_behavior) = REQUIRED];
}

// The Cloud Storage location for the output content
message GcsDestination {
  // Required. Data destination URI (e.g.
  // 'gs://my_bucket/my_object'). Existing files will be overwritten.
  string uri = 1 [(google.api.field_behavior) = REQUIRED];
}

// The output content
message OutputConfig {
  // Required. Specify destination location of output data
  oneof destination {
    // Google Cloud Storage destination for output content.
    GcsDestination gcs_destination = 1;
  }
}

// Request for [Export][google.cloud.redis.v1.CloudRedis.ExportInstance].
message ExportInstanceRequest {
  // Required. Redis instance resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Specify data to be exported.
  OutputConfig output_config = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for [Failover][google.cloud.redis.v1.CloudRedis.FailoverInstance].
message FailoverInstanceRequest {
  // Specifies different modes of operation in relation to the data retention.
  enum DataProtectionMode {
    // Defaults to LIMITED_DATA_LOSS if a data protection mode is not
    // specified.
    DATA_PROTECTION_MODE_UNSPECIFIED = 0;

    // Instance failover will be protected with data loss control. More
    // specifically, the failover will only be performed if the current
    // replication offset diff between primary and replica is under a certain
    // threshold.
    LIMITED_DATA_LOSS = 1;

    // Instance failover will be performed without data loss control.
    FORCE_DATA_LOSS = 2;
  }

  // Required. Redis instance resource name using the form:
  //     `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
  // where `location_id` refers to a GCP region.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "redis.googleapis.com/Instance" }
  ];

  // Optional. Available data protection modes that the user can choose. If it's
  // unspecified, data protection mode will be LIMITED_DATA_LOSS by default.
  DataProtectionMode data_protection_mode = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Represents the v1 metadata of the long-running operation.
message OperationMetadata {
  // Creation timestamp.
  google.protobuf.Timestamp create_time = 1;

  // End timestamp.
  google.protobuf.Timestamp end_time = 2;

  // Operation target.
  string target = 3;

  // Operation verb.
  string verb = 4;

  // Operation status details.
  string status_detail = 5;

  // Specifies if cancellation was requested for the operation.
  bool cancel_requested = 6;

  // API version.
  string api_version = 7;
}

// This location metadata represents additional configuration options for a
// given location where a Redis instance may be created. All fields are output
// only. It is returned as content of the
// `google.cloud.location.Location.metadata` field.
message LocationMetadata {
  // Output only. The set of available zones in the location. The map is keyed
  // by the lowercase ID of each zone, as defined by GCE. These keys can be
  // specified in `location_id` or `alternative_location_id` fields when
  // creating a Redis instance.
  map<string, ZoneMetadata> available_zones = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines specific information for a particular zone. Currently empty and
// reserved for future use only.
message ZoneMetadata {}

// TlsCertificate Resource
message TlsCertificate {
  // Serial number, as extracted from the certificate.
  string serial_number = 1;

  // PEM representation.
  string cert = 2;

  // Output only. The time when the certificate was created in [RFC
  // 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2020-05-18T00:00:00.094Z`.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the certificate expires in [RFC
  // 3339](https://tools.ietf.org/html/rfc3339) format, for example
  // `2020-05-18T00:00:00.094Z`.
  google.protobuf.Timestamp expire_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Sha1 Fingerprint of the certificate.
  string sha1_fingerprint = 5;
}
