type: google.api.Service
config_version: 3
name: datastore.googleapis.com
title: Cloud Datastore API

apis:
- name: google.datastore.admin.v1.DatastoreAdmin
- name: google.longrunning.Operations

types:
- name: google.datastore.admin.v1.DatastoreFirestoreMigrationMetadata
- name: google.datastore.admin.v1.ExportEntitiesMetadata
- name: google.datastore.admin.v1.ExportEntitiesResponse
- name: google.datastore.admin.v1.ImportEntitiesMetadata
- name: google.datastore.admin.v1.IndexOperationMetadata
- name: google.datastore.admin.v1.MigrationProgressEvent
- name: google.datastore.admin.v1.MigrationStateEvent

documentation:
  summary: |-
    Accesses the schemaless NoSQL database to provide fully managed, robust,
    scalable storage for your application.

backend:
  rules:
  - selector: 'google.datastore.admin.v1.DatastoreAdmin.*'
    deadline: 295.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 295.0

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/operations/*}:cancel'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*}/operations'

authentication:
  rules:
  - selector: 'google.datastore.admin.v1.DatastoreAdmin.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
