// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.datastore.admin.v1;

option csharp_namespace = "Google.Cloud.Datastore.Admin.V1";
option go_package = "cloud.google.com/go/datastore/admin/apiv1/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "MigrationProto";
option java_package = "com.google.datastore.admin.v1";
option php_namespace = "Google\\Cloud\\Datastore\\Admin\\V1";
option ruby_package = "Google::Cloud::Datastore::Admin::V1";

// An event signifying a change in state of a [migration from Cloud Datastore to
// Cloud Firestore in Datastore
// mode](https://cloud.google.com/datastore/docs/upgrade-to-firestore).
message MigrationStateEvent {
  // The new state of the migration.
  MigrationState state = 1;
}

// An event signifying the start of a new step in a [migration from Cloud
// Datastore to Cloud Firestore in Datastore
// mode](https://cloud.google.com/datastore/docs/upgrade-to-firestore).
message MigrationProgressEvent {
  // Concurrency modes for transactions in Cloud Firestore.
  enum ConcurrencyMode {
    // Unspecified.
    CONCURRENCY_MODE_UNSPECIFIED = 0;

    // Pessimistic concurrency.
    PESSIMISTIC = 1;

    // Optimistic concurrency.
    OPTIMISTIC = 2;

    // Optimistic concurrency with entity groups.
    OPTIMISTIC_WITH_ENTITY_GROUPS = 3;
  }

  // Details for the `PREPARE` step.
  message PrepareStepDetails {
    // The concurrency mode this database will use when it reaches the
    // `REDIRECT_WRITES` step.
    ConcurrencyMode concurrency_mode = 1;
  }

  // Details for the `REDIRECT_WRITES` step.
  message RedirectWritesStepDetails {
    // Ths concurrency mode for this database.
    ConcurrencyMode concurrency_mode = 1;
  }

  // The step that is starting.
  //
  // An event with step set to `START` indicates that the migration
  // has been reverted back to the initial pre-migration state.
  MigrationStep step = 1;

  // Details about this step.
  oneof step_details {
    // Details for the `PREPARE` step.
    PrepareStepDetails prepare_step_details = 2;

    // Details for the `REDIRECT_WRITES` step.
    RedirectWritesStepDetails redirect_writes_step_details = 3;
  }
}

// States for a migration.
enum MigrationState {
  // Unspecified.
  MIGRATION_STATE_UNSPECIFIED = 0;

  // The migration is running.
  RUNNING = 1;

  // The migration is paused.
  PAUSED = 2;

  // The migration is complete.
  COMPLETE = 3;
}

// Steps in a migration.
enum MigrationStep {
  // Unspecified.
  MIGRATION_STEP_UNSPECIFIED = 0;

  // Pre-migration: the database is prepared for migration.
  PREPARE = 6;

  // Start of migration.
  START = 1;

  // Writes are applied synchronously to at least one replica.
  APPLY_WRITES_SYNCHRONOUSLY = 7;

  // Data is copied to Cloud Firestore and then verified to match the data in
  // Cloud Datastore.
  COPY_AND_VERIFY = 2;

  // Eventually-consistent reads are redirected to Cloud Firestore.
  REDIRECT_EVENTUALLY_CONSISTENT_READS = 3;

  // Strongly-consistent reads are redirected to Cloud Firestore.
  REDIRECT_STRONGLY_CONSISTENT_READS = 4;

  // Writes are redirected to Cloud Firestore.
  REDIRECT_WRITES = 5;
}
