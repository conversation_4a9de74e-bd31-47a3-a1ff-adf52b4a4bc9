// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.datastore.v1;

import "google/api/field_behavior.proto";
import "google/datastore/v1/entity.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Cloud.Datastore.V1";
option go_package = "cloud.google.com/go/datastore/apiv1/datastorepb;datastorepb";
option java_multiple_files = true;
option java_outer_classname = "QueryProto";
option java_package = "com.google.datastore.v1";
option php_namespace = "Google\\Cloud\\Datastore\\V1";
option ruby_package = "Google::Cloud::Datastore::V1";

// The result of fetching an entity from Datastore.
message EntityResult {
  // Specifies what data the 'entity' field contains.
  // A `ResultType` is either implied (for example, in `LookupResponse.missing`
  // from `datastore.proto`, it is always `KEY_ONLY`) or specified by context
  // (for example, in message `QueryResultBatch`, field `entity_result_type`
  // specifies a `ResultType` for all the values in field `entity_results`).
  enum ResultType {
    // Unspecified. This value is never used.
    RESULT_TYPE_UNSPECIFIED = 0;

    // The key and properties.
    FULL = 1;

    // A projected subset of properties. The entity may have no key.
    PROJECTION = 2;

    // Only the key.
    KEY_ONLY = 3;
  }

  // The resulting entity.
  Entity entity = 1;

  // The version of the entity, a strictly positive number that monotonically
  // increases with changes to the entity.
  //
  // This field is set for
  // [`FULL`][google.datastore.v1.EntityResult.ResultType.FULL] entity results.
  //
  // For [missing][google.datastore.v1.LookupResponse.missing] entities in
  // `LookupResponse`, this is the version of the snapshot that was used to look
  // up the entity, and it is always set except for eventually consistent reads.
  int64 version = 4;

  // The time at which the entity was created.
  // This field is set for
  // [`FULL`][google.datastore.v1.EntityResult.ResultType.FULL] entity results.
  // If this entity is missing, this field will not be set.
  google.protobuf.Timestamp create_time = 6;

  // The time at which the entity was last changed.
  // This field is set for
  // [`FULL`][google.datastore.v1.EntityResult.ResultType.FULL] entity results.
  // If this entity is missing, this field will not be set.
  google.protobuf.Timestamp update_time = 5;

  // A cursor that points to the position after the result entity.
  // Set only when the `EntityResult` is part of a `QueryResultBatch` message.
  bytes cursor = 3;
}

// A query for entities.
//
// The query stages are executed in the following order:
// 1. kind
// 2. filter
// 3. projection
// 4. order + start_cursor + end_cursor
// 5. offset
// 6. limit
// 7. find_nearest
message Query {
  // The projection to return. Defaults to returning all properties.
  repeated Projection projection = 2;

  // The kinds to query (if empty, returns entities of all kinds).
  // Currently at most 1 kind may be specified.
  repeated KindExpression kind = 3;

  // The filter to apply.
  Filter filter = 4;

  // The order to apply to the query results (if empty, order is unspecified).
  repeated PropertyOrder order = 5;

  // The properties to make distinct. The query results will contain the first
  // result for each distinct combination of values for the given properties
  // (if empty, all results are returned).
  //
  // Requires:
  //
  // * If `order` is specified, the set of distinct on properties must appear
  // before the non-distinct on properties in `order`.
  repeated PropertyReference distinct_on = 6;

  // A starting point for the query results. Query cursors are
  // returned in query result batches and
  // [can only be used to continue the same
  // query](https://cloud.google.com/datastore/docs/concepts/queries#cursors_limits_and_offsets).
  bytes start_cursor = 7;

  // An ending point for the query results. Query cursors are
  // returned in query result batches and
  // [can only be used to limit the same
  // query](https://cloud.google.com/datastore/docs/concepts/queries#cursors_limits_and_offsets).
  bytes end_cursor = 8;

  // The number of results to skip. Applies before limit, but after all other
  // constraints. Optional. Must be >= 0 if specified.
  int32 offset = 10;

  // The maximum number of results to return. Applies after all other
  // constraints. Optional.
  // Unspecified is interpreted as no limit.
  // Must be >= 0 if specified.
  google.protobuf.Int32Value limit = 12;

  // Optional. A potential Nearest Neighbors Search.
  //
  // Applies after all other filters and ordering.
  //
  // Finds the closest vector embeddings to the given query vector.
  FindNearest find_nearest = 13 [(google.api.field_behavior) = OPTIONAL];
}

// Datastore query for running an aggregation over a
// [Query][google.datastore.v1.Query].
message AggregationQuery {
  // Defines an aggregation that produces a single result.
  message Aggregation {
    // Count of entities that match the query.
    //
    // The `COUNT(*)` aggregation function operates on the entire entity
    // so it does not require a field reference.
    message Count {
      // Optional. Optional constraint on the maximum number of entities to
      // count.
      //
      // This provides a way to set an upper bound on the number of entities
      // to scan, limiting latency, and cost.
      //
      // Unspecified is interpreted as no bound.
      //
      // If a zero value is provided, a count result of zero should always be
      // expected.
      //
      // High-Level Example:
      //
      // ```
      // AGGREGATE COUNT_UP_TO(1000) OVER ( SELECT * FROM k );
      // ```
      //
      // Requires:
      //
      // * Must be non-negative when present.
      google.protobuf.Int64Value up_to = 1
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Sum of the values of the requested property.
    //
    // * Only numeric values will be aggregated. All non-numeric values
    // including `NULL` are skipped.
    //
    // * If the aggregated values contain `NaN`, returns `NaN`. Infinity math
    // follows IEEE-754 standards.
    //
    // * If the aggregated value set is empty, returns 0.
    //
    // * Returns a 64-bit integer if all aggregated numbers are integers and the
    // sum result does not overflow. Otherwise, the result is returned as a
    // double. Note that even if all the aggregated values are integers, the
    // result is returned as a double if it cannot fit within a 64-bit signed
    // integer. When this occurs, the returned value will lose precision.
    //
    // * When underflow occurs, floating-point aggregation is non-deterministic.
    // This means that running the same query repeatedly without any changes to
    // the underlying values could produce slightly different results each
    // time. In those cases, values should be stored as integers over
    // floating-point numbers.
    message Sum {
      // The property to aggregate on.
      PropertyReference property = 1;
    }

    // Average of the values of the requested property.
    //
    // * Only numeric values will be aggregated. All non-numeric values
    // including `NULL` are skipped.
    //
    // * If the aggregated values contain `NaN`, returns `NaN`. Infinity math
    // follows IEEE-754 standards.
    //
    // * If the aggregated value set is empty, returns `NULL`.
    //
    // * Always returns the result as a double.
    message Avg {
      // The property to aggregate on.
      PropertyReference property = 1;
    }

    // The type of aggregation to perform, required.
    oneof operator {
      // Count aggregator.
      Count count = 1;

      // Sum aggregator.
      Sum sum = 2;

      // Average aggregator.
      Avg avg = 3;
    }

    // Optional. Optional name of the property to store the result of the
    // aggregation.
    //
    // If not provided, Datastore will pick a default name following the format
    // `property_<incremental_id++>`. For example:
    //
    // ```
    // AGGREGATE
    //   COUNT_UP_TO(1) AS count_up_to_1,
    //   COUNT_UP_TO(2),
    //   COUNT_UP_TO(3) AS count_up_to_3,
    //   COUNT(*)
    // OVER (
    //   ...
    // );
    // ```
    //
    // becomes:
    //
    // ```
    // AGGREGATE
    //   COUNT_UP_TO(1) AS count_up_to_1,
    //   COUNT_UP_TO(2) AS property_1,
    //   COUNT_UP_TO(3) AS count_up_to_3,
    //   COUNT(*) AS property_2
    // OVER (
    //   ...
    // );
    // ```
    //
    // Requires:
    //
    // * Must be unique across all aggregation aliases.
    // * Conform to [entity property
    // name][google.datastore.v1.Entity.properties] limitations.
    string alias = 7 [(google.api.field_behavior) = OPTIONAL];
  }

  // The base query to aggregate over.
  oneof query_type {
    // Nested query for aggregation
    Query nested_query = 1;
  }

  // Optional. Series of aggregations to apply over the results of the
  // `nested_query`.
  //
  // Requires:
  //
  // * A minimum of one and maximum of five aggregations per query.
  repeated Aggregation aggregations = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// A representation of a kind.
message KindExpression {
  // The name of the kind.
  string name = 1;
}

// A reference to a property relative to the kind expressions.
message PropertyReference {
  // A reference to a property.
  //
  // Requires:
  //
  // * MUST be a dot-delimited (`.`) string of segments, where each segment
  // conforms to [entity property name][google.datastore.v1.Entity.properties]
  // limitations.
  string name = 2;
}

// A representation of a property in a projection.
message Projection {
  // The property to project.
  PropertyReference property = 1;
}

// The desired order for a specific property.
message PropertyOrder {
  // The sort direction.
  enum Direction {
    // Unspecified. This value must not be used.
    DIRECTION_UNSPECIFIED = 0;

    // Ascending.
    ASCENDING = 1;

    // Descending.
    DESCENDING = 2;
  }

  // The property to order by.
  PropertyReference property = 1;

  // The direction to order by. Defaults to `ASCENDING`.
  Direction direction = 2;
}

// A holder for any type of filter.
message Filter {
  // The type of filter.
  oneof filter_type {
    // A composite filter.
    CompositeFilter composite_filter = 1;

    // A filter on a property.
    PropertyFilter property_filter = 2;
  }
}

// A filter that merges multiple other filters using the given operator.
message CompositeFilter {
  // A composite filter operator.
  enum Operator {
    // Unspecified. This value must not be used.
    OPERATOR_UNSPECIFIED = 0;

    // The results are required to satisfy each of the combined filters.
    AND = 1;

    // Documents are required to satisfy at least one of the combined filters.
    OR = 2;
  }

  // The operator for combining multiple filters.
  Operator op = 1;

  // The list of filters to combine.
  //
  // Requires:
  //
  // * At least one filter is present.
  repeated Filter filters = 2;
}

// A filter on a specific property.
message PropertyFilter {
  // A property filter operator.
  enum Operator {
    // Unspecified. This value must not be used.
    OPERATOR_UNSPECIFIED = 0;

    // The given `property` is less than the given `value`.
    //
    // Requires:
    //
    // * That `property` comes first in `order_by`.
    LESS_THAN = 1;

    // The given `property` is less than or equal to the given `value`.
    //
    // Requires:
    //
    // * That `property` comes first in `order_by`.
    LESS_THAN_OR_EQUAL = 2;

    // The given `property` is greater than the given `value`.
    //
    // Requires:
    //
    // * That `property` comes first in `order_by`.
    GREATER_THAN = 3;

    // The given `property` is greater than or equal to the given `value`.
    //
    // Requires:
    //
    // * That `property` comes first in `order_by`.
    GREATER_THAN_OR_EQUAL = 4;

    // The given `property` is equal to the given `value`.
    EQUAL = 5;

    // The given `property` is equal to at least one value in the given array.
    //
    // Requires:
    //
    // * That `value` is a non-empty `ArrayValue`, subject to disjunction
    //   limits.
    // * No `NOT_IN` is in the same query.
    IN = 6;

    // The given `property` is not equal to the given `value`.
    //
    // Requires:
    //
    // * No other `NOT_EQUAL` or `NOT_IN` is in the same query.
    // * That `property` comes first in the `order_by`.
    NOT_EQUAL = 9;

    // Limit the result set to the given entity and its descendants.
    //
    // Requires:
    //
    // * That `value` is an entity key.
    // * All evaluated disjunctions must have the same `HAS_ANCESTOR` filter.
    HAS_ANCESTOR = 11;

    // The value of the `property` is not in the given array.
    //
    // Requires:
    //
    // * That `value` is a non-empty `ArrayValue` with at most 10 values.
    // * No other `OR`, `IN`, `NOT_IN`, `NOT_EQUAL` is in the same query.
    // * That `field` comes first in the `order_by`.
    NOT_IN = 13;
  }

  // The property to filter by.
  PropertyReference property = 1;

  // The operator to filter by.
  Operator op = 2;

  // The value to compare the property to.
  Value value = 3;
}

// Nearest Neighbors search config. The ordering provided by FindNearest
// supersedes the order_by stage. If multiple documents have the same vector
// distance, the returned document order is not guaranteed to be stable between
// queries.
message FindNearest {
  // The distance measure to use when comparing vectors.
  enum DistanceMeasure {
    // Should not be set.
    DISTANCE_MEASURE_UNSPECIFIED = 0;

    // Measures the EUCLIDEAN distance between the vectors. See
    // [Euclidean](https://en.wikipedia.org/wiki/Euclidean_distance) to learn
    // more. The resulting distance decreases the more similar two vectors are.
    EUCLIDEAN = 1;

    // COSINE distance compares vectors based on the angle between them, which
    // allows you to measure similarity that isn't based on the vectors
    // magnitude. We recommend using DOT_PRODUCT with unit normalized vectors
    // instead of COSINE distance, which is mathematically equivalent with
    // better performance. See [Cosine
    // Similarity](https://en.wikipedia.org/wiki/Cosine_similarity) to learn
    // more about COSINE similarity and COSINE distance. The resulting COSINE
    // distance decreases the more similar two vectors are.
    COSINE = 2;

    // Similar to cosine but is affected by the magnitude of the vectors. See
    // [Dot Product](https://en.wikipedia.org/wiki/Dot_product) to learn more.
    // The resulting distance increases the more similar two vectors are.
    DOT_PRODUCT = 3;
  }

  // Required. An indexed vector property to search upon. Only documents which
  // contain vectors whose dimensionality match the query_vector can be
  // returned.
  PropertyReference vector_property = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The query vector that we are searching on. Must be a vector of no
  // more than 2048 dimensions.
  Value query_vector = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The Distance Measure to use, required.
  DistanceMeasure distance_measure = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The number of nearest neighbors to return. Must be a positive
  // integer of no more than 100.
  google.protobuf.Int32Value limit = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. Optional name of the field to output the result of the vector
  // distance calculation. Must conform to [entity
  // property][google.datastore.v1.Entity.properties] limitations.
  string distance_result_property = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Option to specify a threshold for which no less similar documents
  // will be returned. The behavior of the specified `distance_measure` will
  // affect the meaning of the distance threshold. Since DOT_PRODUCT distances
  // increase when the vectors are more similar, the comparison is inverted.
  //
  // For EUCLIDEAN, COSINE: WHERE distance <= distance_threshold
  // For DOT_PRODUCT:       WHERE distance >= distance_threshold
  google.protobuf.DoubleValue distance_threshold = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// A [GQL
// query](https://cloud.google.com/datastore/docs/apis/gql/gql_reference).
message GqlQuery {
  // A string of the format described
  // [here](https://cloud.google.com/datastore/docs/apis/gql/gql_reference).
  string query_string = 1;

  // When false, the query string must not contain any literals and instead must
  // bind all values. For example,
  // `SELECT * FROM Kind WHERE a = 'string literal'` is not allowed, while
  // `SELECT * FROM Kind WHERE a = @value` is.
  bool allow_literals = 2;

  // For each non-reserved named binding site in the query string, there must be
  // a named parameter with that name, but not necessarily the inverse.
  //
  // Key must match regex `[A-Za-z_$][A-Za-z_$0-9]*`, must not match regex
  // `__.*__`, and must not be `""`.
  map<string, GqlQueryParameter> named_bindings = 5;

  // Numbered binding site @1 references the first numbered parameter,
  // effectively using 1-based indexing, rather than the usual 0.
  //
  // For each binding site numbered i in `query_string`, there must be an i-th
  // numbered parameter. The inverse must also be true.
  repeated GqlQueryParameter positional_bindings = 4;
}

// A binding parameter for a GQL query.
message GqlQueryParameter {
  // The type of parameter.
  oneof parameter_type {
    // A value parameter.
    Value value = 2;

    // A query cursor. Query cursors are returned in query
    // result batches.
    bytes cursor = 3;
  }
}

// A batch of results produced by a query.
message QueryResultBatch {
  // The possible values for the `more_results` field.
  enum MoreResultsType {
    // Unspecified. This value is never used.
    MORE_RESULTS_TYPE_UNSPECIFIED = 0;

    // There may be additional batches to fetch from this query.
    NOT_FINISHED = 1;

    // The query is finished, but there may be more results after the limit.
    MORE_RESULTS_AFTER_LIMIT = 2;

    // The query is finished, but there may be more results after the end
    // cursor.
    MORE_RESULTS_AFTER_CURSOR = 4;

    // The query is finished, and there are no more results.
    NO_MORE_RESULTS = 3;
  }

  // The number of results skipped, typically because of an offset.
  int32 skipped_results = 6;

  // A cursor that points to the position after the last skipped result.
  // Will be set when `skipped_results` != 0.
  bytes skipped_cursor = 3;

  // The result type for every entity in `entity_results`.
  EntityResult.ResultType entity_result_type = 1;

  // The results for this batch.
  repeated EntityResult entity_results = 2;

  // A cursor that points to the position after the last result in the batch.
  bytes end_cursor = 4;

  // The state of the query after the current batch.
  MoreResultsType more_results = 5;

  // The version number of the snapshot this batch was returned from.
  // This applies to the range of results from the query's `start_cursor` (or
  // the beginning of the query if no cursor was given) to this batch's
  // `end_cursor` (not the query's `end_cursor`).
  //
  // In a single transaction, subsequent query result batches for the same query
  // can have a greater snapshot version number. Each batch's snapshot version
  // is valid for all preceding batches.
  // The value will be zero for eventually consistent queries.
  int64 snapshot_version = 7;

  // Read timestamp this batch was returned from.
  // This applies to the range of results from the query's `start_cursor` (or
  // the beginning of the query if no cursor was given) to this batch's
  // `end_cursor` (not the query's `end_cursor`).
  //
  // In a single transaction, subsequent query result batches for the same query
  // can have a greater timestamp. Each batch's read timestamp
  // is valid for all preceding batches.
  // This value will not be set for eventually consistent queries in Cloud
  // Datastore.
  google.protobuf.Timestamp read_time = 8;
}
