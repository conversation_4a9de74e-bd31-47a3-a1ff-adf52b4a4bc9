// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.datastore.v1;

import "google/datastore/v1/entity.proto";
import "google/datastore/v1/query.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Datastore.V1";
option go_package = "cloud.google.com/go/datastore/apiv1/datastorepb;datastorepb";
option java_multiple_files = true;
option java_outer_classname = "AggregationResultProto";
option java_package = "com.google.datastore.v1";
option php_namespace = "Google\\Cloud\\Datastore\\V1";
option ruby_package = "Google::Cloud::Datastore::V1";

// The result of a single bucket from a Datastore aggregation query.
//
// The keys of `aggregate_properties` are the same for all results in an
// aggregation query, unlike entity queries which can have different fields
// present for each result.
message AggregationResult {
  // The result of the aggregation functions, ex: `COUNT(*) AS total_entities`.
  //
  // The key is the
  // [alias][google.datastore.v1.AggregationQuery.Aggregation.alias] assigned to
  // the aggregation function on input and the size of this map equals the
  // number of aggregation functions in the query.
  map<string, Value> aggregate_properties = 2;
}

// A batch of aggregation results produced by an aggregation query.
message AggregationResultBatch {
  // The aggregation results for this batch.
  repeated AggregationResult aggregation_results = 1;

  // The state of the query after the current batch.
  // Only COUNT(*) aggregations are supported in the initial launch. Therefore,
  // expected result type is limited to `NO_MORE_RESULTS`.
  QueryResultBatch.MoreResultsType more_results = 2;

  // Read timestamp this batch was returned from.
  //
  // In a single transaction, subsequent query result batches for the same query
  // can have a greater timestamp. Each batch's read timestamp
  // is valid for all preceding batches.
  google.protobuf.Timestamp read_time = 3;
}
