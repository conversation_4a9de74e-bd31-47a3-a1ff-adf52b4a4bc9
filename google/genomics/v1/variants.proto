// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.genomics.v1;

import "google/api/annotations.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

option cc_enable_arenas = true;
option go_package = "google.golang.org/genproto/googleapis/genomics/v1;genomics";
option java_multiple_files = true;
option java_outer_classname = "VariantsProto";
option java_package = "com.google.genomics.v1";

service StreamingVariantService {
  // Returns a stream of all the variants matching the search request, ordered
  // by reference name, position, and ID.
  rpc StreamVariants(StreamVariantsRequest)
      returns (stream StreamVariantsResponse) {
    option (google.api.http) = {
      post: "/v1/variants:stream"
      body: "*"
    };
  }
}

service VariantServiceV1 {
  // Creates variant data by asynchronously importing the provided information.
  //
  // For the definitions of variant sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  //
  // The variants for import will be merged with any existing variant that
  // matches its reference sequence, start, end, reference bases, and
  // alternative bases. If no such variant exists, a new one will be created.
  //
  // When variants are merged, the call information from the new variant
  // is added to the existing variant, and Variant info fields are merged
  // as specified in
  // [infoMergeConfig][google.genomics.v1.ImportVariantsRequest.info_merge_config].
  // As a special case, for single-sample VCF files, QUAL and FILTER fields will
  // be moved to the call level; these are sometimes interpreted in a
  // call-specific context.
  // Imported VCF headers are appended to the metadata already in a variant set.
  rpc ImportVariants(ImportVariantsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/variants:import"
      body: "*"
    };
  }

  // Creates a new variant set.
  //
  // For the definitions of variant sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  //
  // The provided variant set must have a valid `datasetId` set - all other
  // fields are optional. Note that the `id` field will be ignored, as this is
  // assigned by the server.
  rpc CreateVariantSet(CreateVariantSetRequest) returns (VariantSet) {
    option (google.api.http) = {
      post: "/v1/variantsets"
      body: "variant_set"
    };
  }

  // Exports variant set data to an external destination.
  //
  // For the definitions of variant sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  rpc ExportVariantSet(ExportVariantSetRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/variantsets/{variant_set_id}:export"
      body: "*"
    };
  }

  // Gets a variant set by ID.
  //
  // For the definitions of variant sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  rpc GetVariantSet(GetVariantSetRequest) returns (VariantSet) {
    option (google.api.http) = {
      get: "/v1/variantsets/{variant_set_id}"
    };
  }

  // Returns a list of all variant sets matching search criteria.
  //
  // For the definitions of variant sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  //
  // Implements
  // [GlobalAllianceApi.searchVariantSets](https://github.com/ga4gh/schemas/blob/v0.5.1/src/main/resources/avro/variantmethods.avdl#L49).
  rpc SearchVariantSets(SearchVariantSetsRequest)
      returns (SearchVariantSetsResponse) {
    option (google.api.http) = {
      post: "/v1/variantsets/search"
      body: "*"
    };
  }

  // Deletes a variant set including all variants, call sets, and calls within.
  // This is not reversible.
  //
  // For the definitions of variant sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  rpc DeleteVariantSet(DeleteVariantSetRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/variantsets/{variant_set_id}"
    };
  }

  // Updates a variant set using patch semantics.
  //
  // For the definitions of variant sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  rpc UpdateVariantSet(UpdateVariantSetRequest) returns (VariantSet) {
    option (google.api.http) = {
      patch: "/v1/variantsets/{variant_set_id}"
      body: "variant_set"
    };
  }

  // Gets a list of variants matching the criteria.
  //
  // For the definitions of variants and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  //
  // Implements
  // [GlobalAllianceApi.searchVariants](https://github.com/ga4gh/schemas/blob/v0.5.1/src/main/resources/avro/variantmethods.avdl#L126).
  rpc SearchVariants(SearchVariantsRequest) returns (SearchVariantsResponse) {
    option (google.api.http) = {
      post: "/v1/variants/search"
      body: "*"
    };
  }

  // Creates a new variant.
  //
  // For the definitions of variants and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  rpc CreateVariant(CreateVariantRequest) returns (Variant) {
    option (google.api.http) = {
      post: "/v1/variants"
      body: "variant"
    };
  }

  // Updates a variant.
  //
  // For the definitions of variants and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  //
  // This method supports patch semantics. Returns the modified variant without
  // its calls.
  rpc UpdateVariant(UpdateVariantRequest) returns (Variant) {
    option (google.api.http) = {
      patch: "/v1/variants/{variant_id}"
      body: "variant"
    };
  }

  // Deletes a variant.
  //
  // For the definitions of variants and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  rpc DeleteVariant(DeleteVariantRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/variants/{variant_id}"
    };
  }

  // Gets a variant by ID.
  //
  // For the definitions of variants and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  rpc GetVariant(GetVariantRequest) returns (Variant) {
    option (google.api.http) = {
      get: "/v1/variants/{variant_id}"
    };
  }

  // Merges the given variants with existing variants.
  //
  // For the definitions of variants and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  //
  // Each variant will be
  // merged with an existing variant that matches its reference sequence,
  // start, end, reference bases, and alternative bases. If no such variant
  // exists, a new one will be created.
  //
  // When variants are merged, the call information from the new variant
  // is added to the existing variant. Variant info fields are merged as
  // specified in the
  // [infoMergeConfig][google.genomics.v1.MergeVariantsRequest.info_merge_config]
  // field of the MergeVariantsRequest.
  //
  // Please exercise caution when using this method!  It is easy to introduce
  // mistakes in existing variants and difficult to back out of them.  For
  // example,
  // suppose you were trying to merge a new variant with an existing one and
  // both
  // variants contain calls that belong to callsets with the same callset ID.
  //
  //     // Existing variant - irrelevant fields trimmed for clarity
  //     {
  //         "variantSetId": "10473108253681171589",
  //         "referenceName": "1",
  //         "start": "10582",
  //         "referenceBases": "G",
  //         "alternateBases": [
  //             "A"
  //         ],
  //         "calls": [
  //             {
  //                 "callSetId": "10473108253681171589-0",
  //                 "callSetName": "CALLSET0",
  //                 "genotype": [
  //                     0,
  //                     1
  //                 ],
  //             }
  //         ]
  //     }
  //
  //     // New variant with conflicting call information
  //     {
  //         "variantSetId": "10473108253681171589",
  //         "referenceName": "1",
  //         "start": "10582",
  //         "referenceBases": "G",
  //         "alternateBases": [
  //             "A"
  //         ],
  //         "calls": [
  //             {
  //                 "callSetId": "10473108253681171589-0",
  //                 "callSetName": "CALLSET0",
  //                 "genotype": [
  //                     1,
  //                     1
  //                 ],
  //             }
  //         ]
  //     }
  //
  // The resulting merged variant would overwrite the existing calls with those
  // from the new variant:
  //
  //     {
  //         "variantSetId": "10473108253681171589",
  //         "referenceName": "1",
  //         "start": "10582",
  //         "referenceBases": "G",
  //         "alternateBases": [
  //             "A"
  //         ],
  //         "calls": [
  //             {
  //                 "callSetId": "10473108253681171589-0",
  //                 "callSetName": "CALLSET0",
  //                 "genotype": [
  //                     1,
  //                     1
  //                 ],
  //             }
  //         ]
  //     }
  //
  // This may be the desired outcome, but it is up to the user to determine if
  // if that is indeed the case.
  rpc MergeVariants(MergeVariantsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/variants:merge"
      body: "*"
    };
  }

  // Gets a list of call sets matching the criteria.
  //
  // For the definitions of call sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  //
  // Implements
  // [GlobalAllianceApi.searchCallSets](https://github.com/ga4gh/schemas/blob/v0.5.1/src/main/resources/avro/variantmethods.avdl#L178).
  rpc SearchCallSets(SearchCallSetsRequest) returns (SearchCallSetsResponse) {
    option (google.api.http) = {
      post: "/v1/callsets/search"
      body: "*"
    };
  }

  // Creates a new call set.
  //
  // For the definitions of call sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  rpc CreateCallSet(CreateCallSetRequest) returns (CallSet) {
    option (google.api.http) = {
      post: "/v1/callsets"
      body: "call_set"
    };
  }

  // Updates a call set.
  //
  // For the definitions of call sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  //
  // This method supports patch semantics.
  rpc UpdateCallSet(UpdateCallSetRequest) returns (CallSet) {
    option (google.api.http) = {
      patch: "/v1/callsets/{call_set_id}"
      body: "call_set"
    };
  }

  // Deletes a call set.
  //
  // For the definitions of call sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  rpc DeleteCallSet(DeleteCallSetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/callsets/{call_set_id}"
    };
  }

  // Gets a call set by ID.
  //
  // For the definitions of call sets and other genomics resources, see
  // [Fundamentals of Google
  // Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
  rpc GetCallSet(GetCallSetRequest) returns (CallSet) {
    option (google.api.http) = {
      get: "/v1/callsets/{call_set_id}"
    };
  }
}

// Metadata describes a single piece of variant call metadata.
// These data include a top level key and either a single value string (value)
// or a list of key-value pairs (info.)
// Value and info are mutually exclusive.
message VariantSetMetadata {
  enum Type {
    TYPE_UNSPECIFIED = 0;

    INTEGER = 1;

    FLOAT = 2;

    FLAG = 3;

    CHARACTER = 4;

    STRING = 5;
  }

  // The top-level key.
  string key = 1;

  // The value field for simple metadata
  string value = 2;

  // User-provided ID field, not enforced by this API.
  // Two or more pieces of structured metadata with identical
  // id and key fields are considered equivalent.
  string id = 4;

  // The type of data. Possible types include: Integer, Float,
  // Flag, Character, and String.
  Type type = 5;

  // The number of values that can be included in a field described by this
  // metadata.
  string number = 8;

  // A textual description of this metadata.
  string description = 7;

  // Remaining structured metadata key-value pairs. This must be of the form
  // map<string, string[]> (string key mapping to a list of string values).
  map<string, google.protobuf.ListValue> info = 3;
}

// A variant set is a collection of call sets and variants. It contains summary
// statistics of those contents. A variant set belongs to a dataset.
//
// For more genomics resource definitions, see [Fundamentals of Google
// Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
message VariantSet {
  // The dataset to which this variant set belongs.
  string dataset_id = 1;

  // The server-generated variant set ID, unique across all variant sets.
  string id = 2;

  // The reference set to which the variant set is mapped. The reference set
  // describes the alignment provenance of the variant set, while the
  // `referenceBounds` describe the shape of the actual variant data. The
  // reference set's reference names are a superset of those found in the
  // `referenceBounds`.
  //
  // For example, given a variant set that is mapped to the GRCh38 reference set
  // and contains a single variant on reference 'X', `referenceBounds` would
  // contain only an entry for 'X', while the associated reference set
  // enumerates all possible references: '1', '2', 'X', 'Y', 'MT', etc.
  string reference_set_id = 6;

  // A list of all references used by the variants in a variant set
  // with associated coordinate upper bounds for each one.
  repeated ReferenceBound reference_bounds = 5;

  // The metadata associated with this variant set.
  repeated VariantSetMetadata metadata = 4;

  // User-specified, mutable name.
  string name = 7;

  // A textual description of this variant set.
  string description = 8;
}

// A variant represents a change in DNA sequence relative to a reference
// sequence. For example, a variant could represent a SNP or an insertion.
// Variants belong to a variant set.
//
// For more genomics resource definitions, see [Fundamentals of Google
// Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
//
// Each of the calls on a variant represent a determination of genotype with
// respect to that variant. For example, a call might assign probability of 0.32
// to the occurrence of a SNP named rs1234 in a sample named NA12345. A call
// belongs to a call set, which contains related calls typically from one
// sample.
message Variant {
  // The ID of the variant set this variant belongs to.
  string variant_set_id = 15;

  // The server-generated variant ID, unique across all variants.
  string id = 2;

  // Names for the variant, for example a RefSNP ID.
  repeated string names = 3;

  // The date this variant was created, in milliseconds from the epoch.
  int64 created = 12;

  // The reference on which this variant occurs.
  // (such as `chr20` or `X`)
  string reference_name = 14;

  // The position at which this variant occurs (0-based).
  // This corresponds to the first base of the string of reference bases.
  int64 start = 16;

  // The end position (0-based) of this variant. This corresponds to the first
  // base after the last base in the reference allele. So, the length of
  // the reference allele is (end - start). This is useful for variants
  // that don't explicitly give alternate bases, for example large deletions.
  int64 end = 13;

  // The reference bases for this variant. They start at the given
  // position.
  string reference_bases = 6;

  // The bases that appear instead of the reference bases.
  repeated string alternate_bases = 7;

  // A measure of how likely this variant is to be real.
  // A higher value is better.
  double quality = 8;

  // A list of filters (normally quality filters) this variant has failed.
  // `PASS` indicates this variant has passed all filters.
  repeated string filter = 9;

  // A map of additional variant information. This must be of the form
  // map<string, string[]> (string key mapping to a list of string values).
  map<string, google.protobuf.ListValue> info = 10;

  // The variant calls for this particular variant. Each one represents the
  // determination of genotype with respect to this variant.
  repeated VariantCall calls = 11;
}

// A call represents the determination of genotype with respect to a particular
// variant. It may include associated information such as quality and phasing.
// For example, a call might assign a probability of 0.32 to the occurrence of
// a SNP named rs1234 in a call set with the name NA12345.
message VariantCall {
  // The ID of the call set this variant call belongs to.
  string call_set_id = 8;

  // The name of the call set this variant call belongs to.
  string call_set_name = 9;

  // The genotype of this variant call. Each value represents either the value
  // of the `referenceBases` field or a 1-based index into
  // `alternateBases`. If a variant had a `referenceBases`
  // value of `T` and an `alternateBases`
  // value of `["A", "C"]`, and the `genotype` was
  // `[2, 1]`, that would mean the call
  // represented the heterozygous value `CA` for this variant.
  // If the `genotype` was instead `[0, 1]`, the
  // represented value would be `TA`. Ordering of the
  // genotype values is important if the `phaseset` is present.
  // If a genotype is not called (that is, a `.` is present in the
  // GT string) -1 is returned.
  repeated int32 genotype = 7;

  // If this field is present, this variant call's genotype ordering implies
  // the phase of the bases and is consistent with any other variant calls in
  // the same reference sequence which have the same phaseset value.
  // When importing data from VCF, if the genotype data was phased but no
  // phase set was specified this field will be set to `*`.
  string phaseset = 5;

  // The genotype likelihoods for this variant call. Each array entry
  // represents how likely a specific genotype is for this call. The value
  // ordering is defined by the GL tag in the VCF spec.
  // If Phred-scaled genotype likelihood scores (PL) are available and
  // log10(P) genotype likelihood scores (GL) are not, PL scores are converted
  // to GL scores.  If both are available, PL scores are stored in `info`.
  repeated double genotype_likelihood = 6;

  // A map of additional variant call information. This must be of the form
  // map<string, string[]> (string key mapping to a list of string values).
  map<string, google.protobuf.ListValue> info = 2;
}

// A call set is a collection of variant calls, typically for one sample. It
// belongs to a variant set.
//
// For more genomics resource definitions, see [Fundamentals of Google
// Genomics](https://cloud.google.com/genomics/fundamentals-of-google-genomics)
message CallSet {
  // The server-generated call set ID, unique across all call sets.
  string id = 1;

  // The call set name.
  string name = 2;

  // The sample ID this call set corresponds to.
  string sample_id = 7;

  // The IDs of the variant sets this call set belongs to. This field must
  // have exactly length one, as a call set belongs to a single variant set.
  // This field is repeated for compatibility with the
  // [GA4GH 0.5.1
  // API](https://github.com/ga4gh/schemas/blob/v0.5.1/src/main/resources/avro/variants.avdl#L76).
  repeated string variant_set_ids = 6;

  // The date this call set was created in milliseconds from the epoch.
  int64 created = 5;

  // A map of additional call set information. This must be of the form
  // map<string, string[]> (string key mapping to a list of string values).
  map<string, google.protobuf.ListValue> info = 4;
}

// ReferenceBound records an upper bound for the starting coordinate of
// variants in a particular reference.
message ReferenceBound {
  // The name of the reference associated with this reference bound.
  string reference_name = 1;

  // An upper bound (inclusive) on the starting coordinate of any
  // variant in the reference sequence.
  int64 upper_bound = 2;
}

// The variant data import request.
message ImportVariantsRequest {
  enum Format {
    FORMAT_UNSPECIFIED = 0;

    // VCF (Variant Call Format). The VCF files may be gzip compressed. gVCF is
    // also supported.
    FORMAT_VCF = 1;

    // Complete Genomics masterVarBeta format. The masterVarBeta files may
    // be bzip2 compressed.
    FORMAT_COMPLETE_GENOMICS = 2;
  }

  // Required. The variant set to which variant data should be imported.
  string variant_set_id = 1;

  // A list of URIs referencing variant files in Google Cloud Storage. URIs can
  // include wildcards [as described
  // here](https://cloud.google.com/storage/docs/gsutil/addlhelp/WildcardNames).
  // Note that recursive wildcards ('**') are not supported.
  repeated string source_uris = 2;

  // The format of the variant data being imported. If unspecified, defaults to
  // to `VCF`.
  Format format = 3;

  // Convert reference names to the canonical representation.
  // hg19 haploytypes (those reference names containing "_hap")
  // are not modified in any way.
  // All other reference names are modified according to the following rules:
  // The reference name is capitalized.
  // The "chr" prefix is dropped for all autosomes and sex chromsomes.
  // For example "chr17" becomes "17" and "chrX" becomes "X".
  // All mitochondrial chromosomes ("chrM", "chrMT", etc) become "MT".
  bool normalize_reference_names = 5;

  // A mapping between info field keys and the InfoMergeOperations to
  // be performed on them. This is plumbed down to the MergeVariantRequests
  // generated by the resulting import job.
  map<string, InfoMergeOperation> info_merge_config = 6;
}

// The variant data import response.
message ImportVariantsResponse {
  // IDs of the call sets created during the import.
  repeated string call_set_ids = 1;
}

// The CreateVariantSet request
message CreateVariantSetRequest {
  // Required. The variant set to be created. Must have a valid `datasetId`.
  VariantSet variant_set = 1;
}

// The variant data export request.
message ExportVariantSetRequest {
  enum Format {
    FORMAT_UNSPECIFIED = 0;

    // Export the data to Google BigQuery.
    FORMAT_BIGQUERY = 1;
  }

  // Required. The ID of the variant set that contains variant data which
  // should be exported. The caller must have READ access to this variant set.
  string variant_set_id = 1;

  // If provided, only variant call information from the specified call sets
  // will be exported. By default all variant calls are exported.
  repeated string call_set_ids = 2;

  // Required. The Google Cloud project ID that owns the destination
  // BigQuery dataset. The caller must have WRITE access to this project.  This
  // project will also own the resulting export job.
  string project_id = 3;

  // The format for the exported data.
  Format format = 4;

  // Required. The BigQuery dataset to export data to. This dataset must already
  // exist. Note that this is distinct from the Genomics concept of "dataset".
  string bigquery_dataset = 5;

  // Required. The BigQuery table to export data to.
  // If the table doesn't exist, it will be created. If it already exists, it
  // will be overwritten.
  string bigquery_table = 6;
}

// The variant set request.
message GetVariantSetRequest {
  // Required. The ID of the variant set.
  string variant_set_id = 1;
}

// The search variant sets request.
message SearchVariantSetsRequest {
  // Exactly one dataset ID must be provided here. Only variant sets which
  // belong to this dataset will be returned.
  repeated string dataset_ids = 1;

  // The continuation token, which is used to page through large result sets.
  // To get the next page of results, set this parameter to the value of
  // `nextPageToken` from the previous response.
  string page_token = 2;

  // The maximum number of results to return in a single page. If unspecified,
  // defaults to 1024.
  int32 page_size = 3;
}

// The search variant sets response.
message SearchVariantSetsResponse {
  // The variant sets belonging to the requested dataset.
  repeated VariantSet variant_sets = 1;

  // The continuation token, which is used to page through large result sets.
  // Provide this value in a subsequent request to return the next page of
  // results. This field will be empty if there aren't any additional results.
  string next_page_token = 2;
}

// The delete variant set request.
message DeleteVariantSetRequest {
  // The ID of the variant set to be deleted.
  string variant_set_id = 1;
}

message UpdateVariantSetRequest {
  // The ID of the variant to be updated (must already exist).
  string variant_set_id = 1;

  // The new variant data. Only the variant_set.metadata will be considered
  // for update.
  VariantSet variant_set = 2;

  // An optional mask specifying which fields to update. Supported fields:
  //
  // * [metadata][google.genomics.v1.VariantSet.metadata].
  // * [name][google.genomics.v1.VariantSet.name].
  // * [description][google.genomics.v1.VariantSet.description].
  //
  // Leaving `updateMask` unset is equivalent to specifying all mutable
  // fields.
  google.protobuf.FieldMask update_mask = 5;
}

// The variant search request.
message SearchVariantsRequest {
  // At most one variant set ID must be provided. Only variants from this
  // variant set will be returned. If omitted, a call set id must be included in
  // the request.
  repeated string variant_set_ids = 1;

  // Only return variants which have exactly this name.
  string variant_name = 2;

  // Only return variant calls which belong to call sets with these ids.
  // Leaving this blank returns all variant calls. If a variant has no
  // calls belonging to any of these call sets, it won't be returned at all.
  repeated string call_set_ids = 3;

  // Required. Only return variants in this reference sequence.
  string reference_name = 4;

  // The beginning of the window (0-based, inclusive) for which
  // overlapping variants should be returned. If unspecified, defaults to 0.
  int64 start = 5;

  // The end of the window, 0-based exclusive. If unspecified or 0, defaults to
  // the length of the reference.
  int64 end = 6;

  // The continuation token, which is used to page through large result sets.
  // To get the next page of results, set this parameter to the value of
  // `nextPageToken` from the previous response.
  string page_token = 7;

  // The maximum number of variants to return in a single page. If unspecified,
  // defaults to 5000. The maximum value is 10000.
  int32 page_size = 8;

  // The maximum number of calls to return in a single page. Note that this
  // limit may be exceeded in the event that a matching variant contains more
  // calls than the requested maximum. If unspecified, defaults to 5000. The
  // maximum value is 10000.
  int32 max_calls = 9;
}

// The variant search response.
message SearchVariantsResponse {
  // The list of matching Variants.
  repeated Variant variants = 1;

  // The continuation token, which is used to page through large result sets.
  // Provide this value in a subsequent request to return the next page of
  // results. This field will be empty if there aren't any additional results.
  string next_page_token = 2;
}

message CreateVariantRequest {
  // The variant to be created.
  Variant variant = 1;
}

message UpdateVariantRequest {
  // The ID of the variant to be updated.
  string variant_id = 1;

  // The new variant data.
  Variant variant = 2;

  // An optional mask specifying which fields to update. At this time, mutable
  // fields are [names][google.genomics.v1.Variant.names] and
  // [info][google.genomics.v1.Variant.info]. Acceptable values are "names" and
  // "info". If unspecified, all mutable fields will be updated.
  google.protobuf.FieldMask update_mask = 3;
}

message DeleteVariantRequest {
  // The ID of the variant to be deleted.
  string variant_id = 1;
}

message GetVariantRequest {
  // The ID of the variant.
  string variant_id = 1;
}

message MergeVariantsRequest {
  // The destination variant set.
  string variant_set_id = 1;

  // The variants to be merged with existing variants.
  repeated Variant variants = 2;

  // A mapping between info field keys and the InfoMergeOperations to
  // be performed on them.
  map<string, InfoMergeOperation> info_merge_config = 3;
}

// The call set search request.
message SearchCallSetsRequest {
  // Restrict the query to call sets within the given variant sets. At least one
  // ID must be provided.
  repeated string variant_set_ids = 1;

  // Only return call sets for which a substring of the name matches this
  // string.
  string name = 2;

  // The continuation token, which is used to page through large result sets.
  // To get the next page of results, set this parameter to the value of
  // `nextPageToken` from the previous response.
  string page_token = 3;

  // The maximum number of results to return in a single page. If unspecified,
  // defaults to 1024.
  int32 page_size = 4;
}

// The call set search response.
message SearchCallSetsResponse {
  // The list of matching call sets.
  repeated CallSet call_sets = 1;

  // The continuation token, which is used to page through large result sets.
  // Provide this value in a subsequent request to return the next page of
  // results. This field will be empty if there aren't any additional results.
  string next_page_token = 2;
}

message CreateCallSetRequest {
  // The call set to be created.
  CallSet call_set = 1;
}

message UpdateCallSetRequest {
  // The ID of the call set to be updated.
  string call_set_id = 1;

  // The new call set data.
  CallSet call_set = 2;

  // An optional mask specifying which fields to update. At this time, the only
  // mutable field is [name][google.genomics.v1.CallSet.name]. The only
  // acceptable value is "name". If unspecified, all mutable fields will be
  // updated.
  google.protobuf.FieldMask update_mask = 3;
}

message DeleteCallSetRequest {
  // The ID of the call set to be deleted.
  string call_set_id = 1;
}

message GetCallSetRequest {
  // The ID of the call set.
  string call_set_id = 1;
}

// The stream variants request.
message StreamVariantsRequest {
  // The Google Cloud project ID which will be billed
  // for this access. The caller must have WRITE access to this project.
  // Required.
  string project_id = 1;

  // The variant set ID from which to stream variants.
  string variant_set_id = 2;

  // Only return variant calls which belong to call sets with these IDs.
  // Leaving this blank returns all variant calls.
  repeated string call_set_ids = 3;

  // Required. Only return variants in this reference sequence.
  string reference_name = 4;

  // The beginning of the window (0-based, inclusive) for which
  // overlapping variants should be returned.
  int64 start = 5;

  // The end of the window (0-based, exclusive) for which overlapping
  // variants should be returned.
  int64 end = 6;
}

message StreamVariantsResponse {
  repeated Variant variants = 1;
}

// Operations to be performed during import on Variant info fields.
// These operations are set for each info field in the info_merge_config
// map of ImportVariantsRequest, which is plumbed down to the
// MergeVariantRequests generated by the import job.
enum InfoMergeOperation {
  INFO_MERGE_OPERATION_UNSPECIFIED = 0;

  // By default, Variant info fields are persisted if the Variant doesn't
  // already exist in the variantset.  If the Variant is equivalent to a
  // Variant already in the variantset, the incoming Variant's info field
  // is ignored in favor of that of the already persisted Variant.
  IGNORE_NEW = 1;

  // This operation removes an info field from the incoming Variant
  // and persists this info field in each of the incoming Variant's Calls.
  MOVE_TO_CALLS = 2;
}
