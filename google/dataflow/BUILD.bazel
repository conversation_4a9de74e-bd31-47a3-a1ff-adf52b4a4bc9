# This build file includes a target for the Ruby wrapper library for
# google-cloud-dataflow.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for dataflow.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta3 in this case.
ruby_cloud_gapic_library(
    name = "dataflow_ruby_wrapper",
    srcs = ["//google/dataflow/v1beta3:dataflow_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-dataflow",
        "ruby-cloud-env-prefix=DATAFLOW",
        "ruby-cloud-wrapper-of=v1beta3:0.8",
        "ruby-cloud-product-url=https://cloud.google.com/dataflow",
        "ruby-cloud-api-id=dataflow.googleapis.com",
        "ruby-cloud-api-shortname=dataflow",
        "ruby-cloud-service-override=JobsV1Beta3=Jobs;MessagesV1Beta3=Messages;MetricsV1Beta3=Metrics;SnapshotsV1Beta3=Snapshots",
    ],
    ruby_cloud_description = "Dataflow is a managed service for executing a wide variety of data processing patterns.",
    ruby_cloud_title = "Dataflow",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataflow-ruby",
    deps = [
        ":dataflow_ruby_wrapper",
    ],
)
