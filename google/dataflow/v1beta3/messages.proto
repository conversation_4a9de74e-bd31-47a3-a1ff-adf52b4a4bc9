// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.dataflow.v1beta3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dataflow.V1Beta3";
option go_package = "cloud.google.com/go/dataflow/apiv1beta3/dataflowpb;dataflowpb";
option java_multiple_files = true;
option java_outer_classname = "MessagesProto";
option java_package = "com.google.dataflow.v1beta3";
option php_namespace = "Google\\Cloud\\Dataflow\\V1beta3";
option ruby_package = "Google::Cloud::Dataflow::V1beta3";

// The Dataflow Messages API is used for monitoring the progress of
// Dataflow jobs.
service MessagesV1Beta3 {
  option (google.api.default_host) = "dataflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/compute,"
      "https://www.googleapis.com/auth/compute.readonly,"
      "https://www.googleapis.com/auth/userinfo.email";

  // Request the job status.
  //
  // To request the status of a job, we recommend using
  // `projects.locations.jobs.messages.list` with a [regional endpoint]
  // (https://cloud.google.com/dataflow/docs/concepts/regional-endpoints). Using
  // `projects.jobs.messages.list` is not recommended, as you can only request
  // the status of jobs that are running in `us-central1`.
  rpc ListJobMessages(ListJobMessagesRequest) returns (ListJobMessagesResponse) {
    option (google.api.http) = {
      get: "/v1b3/projects/{project_id}/locations/{location}/jobs/{job_id}/messages"
      additional_bindings {
        get: "/v1b3/projects/{project_id}/jobs/{job_id}/messages"
      }
    };
  }
}

// A particular message pertaining to a Dataflow job.
message JobMessage {
  // Deprecated.
  string id = 1;

  // The timestamp of the message.
  google.protobuf.Timestamp time = 2;

  // The text of the message.
  string message_text = 3;

  // Importance level of the message.
  JobMessageImportance message_importance = 4;
}

// Indicates the importance of the message.
enum JobMessageImportance {
  // The message importance isn't specified, or is unknown.
  JOB_MESSAGE_IMPORTANCE_UNKNOWN = 0;

  // The message is at the 'debug' level: typically only useful for
  // software engineers working on the code the job is running.
  // Typically, Dataflow pipeline runners do not display log messages
  // at this level by default.
  JOB_MESSAGE_DEBUG = 1;

  // The message is at the 'detailed' level: somewhat verbose, but
  // potentially useful to users.  Typically, Dataflow pipeline
  // runners do not display log messages at this level by default.
  // These messages are displayed by default in the Dataflow
  // monitoring UI.
  JOB_MESSAGE_DETAILED = 2;

  // The message is at the 'basic' level: useful for keeping
  // track of the execution of a Dataflow pipeline.  Typically,
  // Dataflow pipeline runners display log messages at this level by
  // default, and these messages are displayed by default in the
  // Dataflow monitoring UI.
  JOB_MESSAGE_BASIC = 5;

  // The message is at the 'warning' level: indicating a condition
  // pertaining to a job which may require human intervention.
  // Typically, Dataflow pipeline runners display log messages at this
  // level by default, and these messages are displayed by default in
  // the Dataflow monitoring UI.
  JOB_MESSAGE_WARNING = 3;

  // The message is at the 'error' level: indicating a condition
  // preventing a job from succeeding.  Typically, Dataflow pipeline
  // runners display log messages at this level by default, and these
  // messages are displayed by default in the Dataflow monitoring UI.
  JOB_MESSAGE_ERROR = 4;
}

// A rich message format, including a human readable string, a key for
// identifying the message, and structured data associated with the message for
// programmatic consumption.
message StructuredMessage {
  // Structured data associated with this message.
  message Parameter {
    // Key or name for this parameter.
    string key = 1;

    // Value for this parameter.
    google.protobuf.Value value = 2;
  }

  // Human-readable version of message.
  string message_text = 1;

  // Identifier for this message type.  Used by external systems to
  // internationalize or personalize message.
  string message_key = 2;

  // The structured data associated with this message.
  repeated Parameter parameters = 3;
}

// A structured message reporting an autoscaling decision made by the Dataflow
// service.
message AutoscalingEvent {
  // Indicates the type of autoscaling event.
  enum AutoscalingEventType {
    // Default type for the enum.  Value should never be returned.
    TYPE_UNKNOWN = 0;

    // The TARGET_NUM_WORKERS_CHANGED type should be used when the target
    // worker pool size has changed at the start of an actuation. An event
    // should always be specified as TARGET_NUM_WORKERS_CHANGED if it reflects
    // a change in the target_num_workers.
    TARGET_NUM_WORKERS_CHANGED = 1;

    // The CURRENT_NUM_WORKERS_CHANGED type should be used when actual worker
    // pool size has been changed, but the target_num_workers has not changed.
    CURRENT_NUM_WORKERS_CHANGED = 2;

    // The ACTUATION_FAILURE type should be used when we want to report
    // an error to the user indicating why the current number of workers
    // in the pool could not be changed.
    // Displayed in the current status and history widgets.
    ACTUATION_FAILURE = 3;

    // Used when we want to report to the user a reason why we are
    // not currently adjusting the number of workers.
    // Should specify both target_num_workers, current_num_workers and a
    // decision_message.
    NO_CHANGE = 4;
  }

  // The current number of workers the job has.
  int64 current_num_workers = 1;

  // The target number of workers the worker pool wants to resize to use.
  int64 target_num_workers = 2;

  // The type of autoscaling event to report.
  AutoscalingEventType event_type = 3;

  // A message describing why the system decided to adjust the current
  // number of workers, why it failed, or why the system decided to
  // not make any changes to the number of workers.
  StructuredMessage description = 4;

  // The time this event was emitted to indicate a new target or current
  // num_workers value.
  google.protobuf.Timestamp time = 5;

  // A short and friendly name for the worker pool this event refers to.
  string worker_pool = 7;
}

// Request to list job messages.
// Up to max_results messages will be returned in the time range specified
// starting with the oldest messages first. If no time range is specified
// the results with start with the oldest message.
message ListJobMessagesRequest {
  // A project id.
  string project_id = 1;

  // The job to get messages about.
  string job_id = 2;

  // Filter to only get messages with importance >= level
  JobMessageImportance minimum_importance = 3;

  // If specified, determines the maximum number of messages to
  // return.  If unspecified, the service may choose an appropriate
  // default, or may return an arbitrarily large number of results.
  int32 page_size = 4;

  // If supplied, this should be the value of next_page_token returned
  // by an earlier call. This will cause the next page of results to
  // be returned.
  string page_token = 5;

  // If specified, return only messages with timestamps >= start_time.
  // The default is the job creation time (i.e. beginning of messages).
  google.protobuf.Timestamp start_time = 6;

  // Return only messages with timestamps < end_time. The default is now
  // (i.e. return up to the latest messages available).
  google.protobuf.Timestamp end_time = 7;

  // The [regional endpoint]
  // (https://cloud.google.com/dataflow/docs/concepts/regional-endpoints) that
  // contains the job specified by job_id.
  string location = 8;
}

// Response to a request to list job messages.
message ListJobMessagesResponse {
  // Messages in ascending timestamp order.
  repeated JobMessage job_messages = 1;

  // The token to obtain the next page of results if there are more.
  string next_page_token = 2;

  // Autoscaling events in ascending timestamp order.
  repeated AutoscalingEvent autoscaling_events = 3;
}
