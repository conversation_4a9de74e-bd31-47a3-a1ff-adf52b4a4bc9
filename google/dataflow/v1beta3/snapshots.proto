// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.dataflow.v1beta3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dataflow.V1Beta3";
option go_package = "cloud.google.com/go/dataflow/apiv1beta3/dataflowpb;dataflowpb";
option java_multiple_files = true;
option java_outer_classname = "SnapshotsProto";
option java_package = "com.google.dataflow.v1beta3";
option php_namespace = "Google\\Cloud\\Dataflow\\V1beta3";
option ruby_package = "Google::Cloud::Dataflow::V1beta3";

// Provides methods to manage snapshots of Google Cloud Dataflow jobs.
service SnapshotsV1Beta3 {
  option (google.api.default_host) = "dataflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/compute,"
      "https://www.googleapis.com/auth/compute.readonly,"
      "https://www.googleapis.com/auth/userinfo.email";

  // Gets information about a snapshot.
  rpc GetSnapshot(GetSnapshotRequest) returns (Snapshot) {
    option (google.api.http) = {
      get: "/v1b3/projects/{project_id}/locations/{location}/snapshots/{snapshot_id}"
      additional_bindings {
        get: "/v1b3/projects/{project_id}/snapshots/{snapshot_id}"
      }
    };
  }

  // Deletes a snapshot.
  rpc DeleteSnapshot(DeleteSnapshotRequest) returns (DeleteSnapshotResponse) {
    option (google.api.http) = {
      delete: "/v1b3/projects/{project_id}/locations/{location}/snapshots/{snapshot_id}"
      additional_bindings {
        delete: "/v1b3/projects/{project_id}/snapshots"
      }
    };
  }

  // Lists snapshots.
  rpc ListSnapshots(ListSnapshotsRequest) returns (ListSnapshotsResponse) {
    option (google.api.http) = {
      get: "/v1b3/projects/{project_id}/locations/{location}/jobs/{job_id}/snapshots"
      additional_bindings {
        get: "/v1b3/projects/{project_id}/locations/{location}/snapshots"
      }
      additional_bindings {
        get: "/v1b3/projects/{project_id}/snapshots"
      }
    };
  }
}

// Snapshot state.
enum SnapshotState {
  // Unknown state.
  UNKNOWN_SNAPSHOT_STATE = 0;

  // Snapshot intent to create has been persisted, snapshotting of state has not
  // yet started.
  PENDING = 1;

  // Snapshotting is being performed.
  RUNNING = 2;

  // Snapshot has been created and is ready to be used.
  READY = 3;

  // Snapshot failed to be created.
  FAILED = 4;

  // Snapshot has been deleted.
  DELETED = 5;
}

// Represents a Pubsub snapshot.
message PubsubSnapshotMetadata {
  // The name of the Pubsub topic.
  string topic_name = 1;

  // The name of the Pubsub snapshot.
  string snapshot_name = 2;

  // The expire time of the Pubsub snapshot.
  google.protobuf.Timestamp expire_time = 3;
}

// Represents a snapshot of a job.
message Snapshot {
  // The unique ID of this snapshot.
  string id = 1;

  // The project this snapshot belongs to.
  string project_id = 2;

  // The job this snapshot was created from.
  string source_job_id = 3;

  // The time this snapshot was created.
  google.protobuf.Timestamp creation_time = 4;

  // The time after which this snapshot will be automatically deleted.
  google.protobuf.Duration ttl = 5;

  // State of the snapshot.
  SnapshotState state = 6;

  // Pub/Sub snapshot metadata.
  repeated PubsubSnapshotMetadata pubsub_metadata = 7;

  // User specified description of the snapshot. Maybe empty.
  string description = 8;

  // The disk byte size of the snapshot. Only available for snapshots in READY
  // state.
  int64 disk_size_bytes = 9;

  // Cloud region where this snapshot lives in, e.g., "us-central1".
  string region = 10;
}

// Request to get information about a snapshot
message GetSnapshotRequest {
  // The ID of the Cloud Platform project that the snapshot belongs to.
  string project_id = 1;

  // The ID of the snapshot.
  string snapshot_id = 2;

  // The location that contains this snapshot.
  string location = 3;
}

// Request to delete a snapshot.
message DeleteSnapshotRequest {
  // The ID of the Cloud Platform project that the snapshot belongs to.
  string project_id = 1;

  // The ID of the snapshot.
  string snapshot_id = 2;

  // The location that contains this snapshot.
  string location = 3;
}

// Response from deleting a snapshot.
message DeleteSnapshotResponse {

}

// Request to list snapshots.
message ListSnapshotsRequest {
  // The project ID to list snapshots for.
  string project_id = 1;

  // If specified, list snapshots created from this job.
  string job_id = 3;

  // The location to list snapshots in.
  string location = 2;
}

// List of snapshots.
message ListSnapshotsResponse {
  // Returned snapshots.
  repeated Snapshot snapshots = 1;
}
