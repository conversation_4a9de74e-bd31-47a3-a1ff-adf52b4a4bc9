# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "dataflow_proto",
    srcs = [
        "environment.proto",
        "jobs.proto",
        "messages.proto",
        "metrics.proto",
        "snapshots.proto",
        "streaming.proto",
        "templates.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "dataflow_proto_with_info",
    deps = [
        ":dataflow_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "dataflow_java_proto",
    deps = [":dataflow_proto"],
)

java_grpc_library(
    name = "dataflow_java_grpc",
    srcs = [":dataflow_proto"],
    deps = [":dataflow_java_proto"],
)

java_gapic_library(
    name = "dataflow_java_gapic",
    srcs = [":dataflow_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "dataflow_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataflow_v1beta3.yaml",
    test_deps = [
        ":dataflow_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":dataflow_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "dataflow_java_gapic_test_suite",
    test_classes = [
        "com.google.dataflow.v1beta3.FlexTemplatesServiceClientHttpJsonTest",
        "com.google.dataflow.v1beta3.FlexTemplatesServiceClientTest",
        "com.google.dataflow.v1beta3.JobsV1Beta3ClientHttpJsonTest",
        "com.google.dataflow.v1beta3.JobsV1Beta3ClientTest",
        "com.google.dataflow.v1beta3.MessagesV1Beta3ClientHttpJsonTest",
        "com.google.dataflow.v1beta3.MessagesV1Beta3ClientTest",
        "com.google.dataflow.v1beta3.MetricsV1Beta3ClientHttpJsonTest",
        "com.google.dataflow.v1beta3.MetricsV1Beta3ClientTest",
        "com.google.dataflow.v1beta3.SnapshotsV1Beta3ClientHttpJsonTest",
        "com.google.dataflow.v1beta3.SnapshotsV1Beta3ClientTest",
        "com.google.dataflow.v1beta3.TemplatesServiceClientHttpJsonTest",
        "com.google.dataflow.v1beta3.TemplatesServiceClientTest",
    ],
    runtime_deps = [":dataflow_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-dataflow-v1beta3-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":dataflow_java_gapic",
        ":dataflow_java_grpc",
        ":dataflow_java_proto",
        ":dataflow_proto",
    ],
)

go_proto_library(
    name = "dataflow_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/dataflow/apiv1beta3/dataflowpb",
    protos = [":dataflow_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "dataflow_go_gapic",
    srcs = [":dataflow_proto_with_info"],
    grpc_service_config = "dataflow_grpc_service_config.json",
    importpath = "cloud.google.com/go/dataflow/apiv1beta3;dataflow",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "dataflow_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataflow_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-dataflow-v1beta3-go",
    deps = [
        ":dataflow_go_gapic",
        ":dataflow_go_gapic_srcjar-metadata.srcjar",
        ":dataflow_go_gapic_srcjar-snippets.srcjar",
        ":dataflow_go_gapic_srcjar-test.srcjar",
        ":dataflow_go_proto",
    ],
)

py_gapic_library(
    name = "dataflow_py_gapic",
    srcs = [":dataflow_proto"],
    grpc_service_config = "dataflow_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=dataflow",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-dataflow-client",
    ],
    rest_numeric_enums = True,
    service_yaml = "dataflow_v1beta3.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "dataflow_py_gapic_test",
    srcs = [
        "dataflow_py_gapic_pytest.py",
        "dataflow_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":dataflow_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "dataflow-v1beta3-py",
    deps = [
        ":dataflow_py_gapic",
    ],
)

php_proto_library(
    name = "dataflow_php_proto",
    deps = [":dataflow_proto"],
)

php_gapic_library(
    name = "dataflow_php_gapic",
    srcs = [":dataflow_proto_with_info"],
    grpc_service_config = "dataflow_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "dataflow_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [":dataflow_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-dataflow-v1beta3-php",
    deps = [
        ":dataflow_php_gapic",
        ":dataflow_php_proto",
    ],
)

nodejs_gapic_library(
    name = "dataflow_nodejs_gapic",
    package_name = "@google-cloud/dataflow",
    src = ":dataflow_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "dataflow_grpc_service_config.json",
    package = "google.dataflow.v1beta3",
    rest_numeric_enums = True,
    service_yaml = "dataflow_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "dataflow-v1beta3-nodejs",
    deps = [
        ":dataflow_nodejs_gapic",
        ":dataflow_proto",
    ],
)

ruby_proto_library(
    name = "dataflow_ruby_proto",
    deps = [":dataflow_proto"],
)

ruby_grpc_library(
    name = "dataflow_ruby_grpc",
    srcs = [":dataflow_proto"],
    deps = [":dataflow_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "dataflow_ruby_gapic",
    srcs = [":dataflow_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=dataflow.googleapis.com",
        "ruby-cloud-api-shortname=dataflow",
        "ruby-cloud-env-prefix=DATAFLOW",
        "ruby-cloud-gem-name=google-cloud-dataflow-v1beta3",
        "ruby-cloud-product-url=https://cloud.google.com/dataflow",
        "ruby-cloud-service-override=JobsV1Beta3=Jobs;MessagesV1Beta3=Messages;MetricsV1Beta3=Metrics;SnapshotsV1Beta3=Snapshots",
    ],
    grpc_service_config = "dataflow_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Dataflow is a managed service for executing a wide variety of data processing patterns.",
    ruby_cloud_title = "Dataflow V1beta3",
    service_yaml = "dataflow_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataflow_ruby_grpc",
        ":dataflow_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-dataflow-v1beta3-ruby",
    deps = [
        ":dataflow_ruby_gapic",
        ":dataflow_ruby_grpc",
        ":dataflow_ruby_proto",
    ],
)

csharp_proto_library(
    name = "dataflow_csharp_proto",
    deps = [":dataflow_proto"],
)

csharp_grpc_library(
    name = "dataflow_csharp_grpc",
    srcs = [":dataflow_proto"],
    deps = [":dataflow_csharp_proto"],
)

csharp_gapic_library(
    name = "dataflow_csharp_gapic",
    srcs = [":dataflow_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "dataflow_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "dataflow_v1beta3.yaml",
    transport = "grpc+rest",
    deps = [
        ":dataflow_csharp_grpc",
        ":dataflow_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-dataflow-v1beta3-csharp",
    deps = [
        ":dataflow_csharp_gapic",
        ":dataflow_csharp_grpc",
        ":dataflow_csharp_proto",
    ],
)

cc_proto_library(
    name = "dataflow_cc_proto",
    deps = [":dataflow_proto"],
)

cc_grpc_library(
    name = "dataflow_cc_grpc",
    srcs = [":dataflow_proto"],
    grpc_only = True,
    deps = [":dataflow_cc_proto"],
)
