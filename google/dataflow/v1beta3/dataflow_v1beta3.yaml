type: google.api.Service
config_version: 3
name: dataflow.googleapis.com
title: Dataflow API

apis:
- name: google.dataflow.v1beta3.FlexTemplatesService
- name: google.dataflow.v1beta3.JobsV1Beta3
- name: google.dataflow.v1beta3.MessagesV1Beta3
- name: google.dataflow.v1beta3.MetricsV1Beta3
- name: google.dataflow.v1beta3.SnapshotsV1Beta3
- name: google.dataflow.v1beta3.TemplatesService

documentation:
  summary: Manages Google Cloud Dataflow projects on Google Cloud Platform.

backend:
  rules:
  - selector: google.dataflow.v1beta3.FlexTemplatesService.LaunchFlexTemplate
    deadline: 30.0
  - selector: 'google.dataflow.v1beta3.JobsV1Beta3.*'
    deadline: 30.0
  - selector: google.dataflow.v1beta3.MessagesV1Beta3.ListJobMessages
    deadline: 30.0
  - selector: 'google.dataflow.v1beta3.MetricsV1Beta3.*'
    deadline: 30.0
  - selector: 'google.dataflow.v1beta3.SnapshotsV1Beta3.*'
    deadline: 30.0
  - selector: 'google.dataflow.v1beta3.TemplatesService.*'
    deadline: 30.0

authentication:
  rules:
  - selector: google.dataflow.v1beta3.FlexTemplatesService.LaunchFlexTemplate
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/compute,
        https://www.googleapis.com/auth/compute.readonly,
        https://www.googleapis.com/auth/userinfo.email
  - selector: 'google.dataflow.v1beta3.JobsV1Beta3.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/compute,
        https://www.googleapis.com/auth/compute.readonly,
        https://www.googleapis.com/auth/userinfo.email
  - selector: google.dataflow.v1beta3.MessagesV1Beta3.ListJobMessages
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/compute,
        https://www.googleapis.com/auth/compute.readonly,
        https://www.googleapis.com/auth/userinfo.email
  - selector: 'google.dataflow.v1beta3.MetricsV1Beta3.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/compute,
        https://www.googleapis.com/auth/compute.readonly,
        https://www.googleapis.com/auth/userinfo.email
  - selector: 'google.dataflow.v1beta3.SnapshotsV1Beta3.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/compute,
        https://www.googleapis.com/auth/compute.readonly,
        https://www.googleapis.com/auth/userinfo.email
  - selector: 'google.dataflow.v1beta3.TemplatesService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/compute,
        https://www.googleapis.com/auth/compute.readonly,
        https://www.googleapis.com/auth/userinfo.email
