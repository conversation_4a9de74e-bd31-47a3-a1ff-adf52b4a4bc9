// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.search.partnerdataingestion.logging.v1;

option cc_enable_arenas = true;
option go_package = "google.golang.org/genproto/googleapis/search/partnerdataingestion/logging/v1;logging";
option java_multiple_files = true;
option java_outer_classname = "PlatformLogProto";
option java_package = "com.google.search.partnerdataingestion.logging.v1";

// Log message used to send to Platform Logging.
message IngestDetailsLog {
  // Identification of the successfully accepted request.
  string ingestion_tracking_id = 1;

  // The message content will be sent to Platform Logging.
  string content = 2;
}
