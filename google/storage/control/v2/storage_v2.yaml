type: google.api.Service
config_version: 3
name: storage.googleapis.com
title: Storage Control API

apis:
- name: google.longrunning.Operations
- name: google.storage.control.v2.StorageControl

types:
- name: google.storage.control.v2.CommonLongRunningOperationMetadata
- name: google.storage.control.v2.RenameFolderMetadata

documentation:
  summary: |-
    The Storage Control API lets you perform metadata-specific, control plane,
    and long-running operations.

    The Storage Control API creates one space to perform metadata-specific,
    control plane, and long-running operations apart from the Storage API.
    Separating these operations from the Storage API improves API
    standardization and lets you run faster releases.
  overview: |-
    The Google Cloud Storage API allows applications to read and write data
    through the abstractions of buckets and objects, which are similar to
    directories and files except that buckets cannot contain other buckets,
    and directory-level operations (like directory rename) are not supported.
    Buckets share a single global namespace, and each bucket belongs to a
    specific project that has an associated owner that pays for the data
    stored in the bucket. This API is accessed using standard gRPC requests.

authentication:
  rules:
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/devstorage.full_control,
        https://www.googleapis.com/auth/devstorage.read_only,
        https://www.googleapis.com/auth/devstorage.read_write
  - selector: 'google.storage.control.v2.StorageControl.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/devstorage.full_control,
        https://www.googleapis.com/auth/devstorage.read_only,
        https://www.googleapis.com/auth/devstorage.read_write

publishing:
  method_settings:
  - selector: google.storage.control.v2.StorageControl.CreateFolder
    auto_populated_fields:
    - request_id
  - selector: google.storage.control.v2.StorageControl.DeleteFolder
    auto_populated_fields:
    - request_id
  - selector: google.storage.control.v2.StorageControl.GetFolder
    auto_populated_fields:
    - request_id
  - selector: google.storage.control.v2.StorageControl.RenameFolder
    auto_populated_fields:
    - request_id
  - selector: google.storage.control.v2.StorageControl.GetStorageLayout
    auto_populated_fields:
    - request_id
  - selector: google.storage.control.v2.StorageControl.CreateManagedFolder
    auto_populated_fields:
    - request_id
  - selector: google.storage.control.v2.StorageControl.DeleteManagedFolder
    auto_populated_fields:
    - request_id
  - selector: google.storage.control.v2.StorageControl.GetManagedFolder
    auto_populated_fields:
    - request_id
  - selector: google.storage.control.v2.StorageControl.ListManagedFolders
    auto_populated_fields:
    - request_id
  new_issue_uri: https://issuetracker.google.com/issues/new?component=187243&template=1162869
  documentation_uri: https://cloud.google.com/storage/docs/overview
  api_short_name: storage
  github_label: 'api: storage'
  doc_tag_prefix: storage
  organization: CLOUD
  library_settings:
  - version: google.storage.control.v2
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
