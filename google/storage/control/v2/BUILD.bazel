# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "control_proto",
    srcs = [
        "storage_control.proto",
    ],
    deps = [
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/api:routing_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "control_proto_with_info",
    deps = [
        ":control_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "control_java_proto",
    deps = [":control_proto"],
)

java_grpc_library(
    name = "control_java_grpc",
    srcs = [":control_proto"],
    deps = [":control_java_proto"],
)

java_gapic_library(
    name = "control_java_gapic",
    srcs = [":control_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "storage_control_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "storage_v2.yaml",
    test_deps = [
        ":control_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":control_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "control_java_gapic_test_suite",
    test_classes = [
        "com.google.storage.control.v2.StorageControlClientTest",
    ],
    runtime_deps = [":control_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-storage-control-v2-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":control_java_gapic",
        ":control_java_grpc",
        ":control_java_proto",
        ":control_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "control_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/storage/control/apiv2/controlpb",
    protos = [":control_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "control_go_gapic",
    srcs = [":control_proto_with_info"],
    grpc_service_config = "storage_control_grpc_service_config.json",
    importpath = "cloud.google.com/go/storage/control/apiv2;control",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "storage_v2.yaml",
    transport = "grpc",
    deps = [
        ":control_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-storage-control-v2-go",
    deps = [
        ":control_go_gapic",
        ":control_go_gapic_srcjar-test.srcjar",
        ":control_go_gapic_srcjar-metadata.srcjar",
        ":control_go_gapic_srcjar-snippets.srcjar",
        ":control_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "control_py_gapic",
    srcs = [":control_proto"],
    grpc_service_config = "storage_control_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "storage_v2.yaml",
    transport = "grpc",
    deps = [

    ],
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=storage_control",
        "warehouse-package-name=google-cloud-storage-control"
    ],
)


py_test(
    name = "control_py_gapic_test",
    srcs = [
        "control_py_gapic_pytest.py",
        "control_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":control_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "storage-control-v2-py",
    deps = [
        ":control_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "control_php_proto",
    deps = [":control_proto"],
)

php_gapic_library(
    name = "control_php_gapic",
    srcs = [":control_proto_with_info"],
    grpc_service_config = "storage_control_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "storage_v2.yaml",
    transport = "grpc",
    deps = [
        ":control_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-storage-control-v2-php",
    deps = [
        ":control_php_gapic",
        ":control_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "control_nodejs_gapic",
    package_name = "@google-cloud/storage-control",
    src = ":control_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "storage_control_grpc_service_config.json",
    package = "google.storage.control.v2",
    rest_numeric_enums = True,
    service_yaml = "storage_v2.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "storage-control-v2-nodejs",
    deps = [
        ":control_nodejs_gapic",
        ":control_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "control_ruby_proto",
    deps = [":control_proto"],
)

ruby_grpc_library(
    name = "control_ruby_grpc",
    srcs = [":control_proto"],
    deps = [":control_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "control_ruby_gapic",
    srcs = [":control_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-storage-control-v2",
    ],
    grpc_service_config = "storage_control_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "storage_v2.yaml",
    transport = "grpc",
    deps = [
        ":control_ruby_grpc",
        ":control_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-storage-control-v2-ruby",
    deps = [
        ":control_ruby_gapic",
        ":control_ruby_grpc",
        ":control_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "control_csharp_proto",
    extra_opts = [],
    deps = [":control_proto"],
)

csharp_grpc_library(
    name = "control_csharp_grpc",
    srcs = [":control_proto"],
    deps = [":control_csharp_proto"],
)

csharp_gapic_library(
    name = "control_csharp_gapic",
    srcs = [":control_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "storage_control_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "storage_v2.yaml",
    transport = "grpc",
    deps = [
        ":control_csharp_grpc",
        ":control_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-storage-control-v2-csharp",
    deps = [
        ":control_csharp_gapic",
        ":control_csharp_grpc",
        ":control_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "control_cc_proto",
    deps = [":control_proto"],
)

cc_grpc_library(
    name = "control_cc_grpc",
    srcs = [":control_proto"],
    grpc_only = True,
    deps = [":control_cc_proto"],
)

