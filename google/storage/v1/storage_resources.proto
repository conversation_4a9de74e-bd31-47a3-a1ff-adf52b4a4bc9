// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.storage.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option go_package = "google.golang.org/genproto/googleapis/storage/v1;storage";
option java_multiple_files = true;
option java_outer_classname = "CloudStorageResourcesProto";
option java_package = "com.google.storage.v1";

// A bucket.
message Bucket {
  // Billing properties of a bucket.
  message Billing {
    // When set to true, Requester Pays is enabled for this bucket.
    bool requester_pays = 1;
  }

  // Cross-Origin Response sharing (CORS) properties for a bucket.
  // For more on GCS and CORS, see
  // https://cloud.google.com/storage/docs/cross-origin.
  // For more on CORS in general, see https://tools.ietf.org/html/rfc6454.
  message Cors {
    // The list of Origins eligible to receive CORS response headers. See
    // [https://tools.ietf.org/html/rfc6454][RFC 6454] for more on origins.
    // Note: "*" is permitted in the list of origins, and means "any Origin".
    repeated string origin = 1;

    // The list of HTTP methods on which to include CORS response headers,
    // (`GET`, `OPTIONS`, `POST`, etc) Note: "*" is permitted in the list of
    // methods, and means "any method".
    repeated string method = 2;

    // The list of HTTP headers other than the
    // [https://www.w3.org/TR/cors/#simple-response-header][simple response
    // headers] to give permission for the user-agent to share across domains.
    repeated string response_header = 3;

    // The value, in seconds, to return in the
    // [https://www.w3.org/TR/cors/#access-control-max-age-response-header][Access-Control-Max-Age
    // header] used in preflight responses.
    int32 max_age_seconds = 4;
  }

  // Encryption properties of a bucket.
  message Encryption {
    // A Cloud KMS key that will be used to encrypt objects inserted into this
    // bucket, if no encryption method is specified.
    string default_kms_key_name = 1;
  }

  // Bucket restriction options currently enforced on the bucket.
  message IamConfiguration {
    message UniformBucketLevelAccess {
      // If set, access checks only use bucket-level IAM policies or above.
      bool enabled = 1;

      // The deadline time for changing
      // <code>iamConfiguration.uniformBucketLevelAccess.enabled</code> from
      // true to false in [https://tools.ietf.org/html/rfc3339][RFC 3339]. After
      // the deadline is passed the field is immutable.
      google.protobuf.Timestamp locked_time = 2;
    }

    // Public Access Prevention configuration values.
    enum PublicAccessPrevention {
      // No specified PublicAccessPrevention.
      PUBLIC_ACCESS_PREVENTION_UNSPECIFIED = 0;

      // Prevents access from being granted to public members 'allUsers' and
      // 'allAuthenticatedUsers'. Prevents attempts to grant new access to
      // public members.
      ENFORCED = 1;

      // This setting is inherited from Org Policy. Does not prevent access from
      // being granted to public members 'allUsers' or 'allAuthenticatedUsers'.
      INHERITED = 2;
    }

    UniformBucketLevelAccess uniform_bucket_level_access = 1;

    // Whether IAM will enforce public access prevention.
    PublicAccessPrevention public_access_prevention = 2;
  }

  // Lifecycle properties of a bucket.
  // For more information, see https://cloud.google.com/storage/docs/lifecycle.
  message Lifecycle {
    // A lifecycle Rule, combining an action to take on an object and a
    // condition which will trigger that action.
    message Rule {
      // An action to take on an object.
      message Action {
        // Type of the action. Currently, only `Delete`, `SetStorageClass`, and
        // `AbortIncompleteMultipartUpload` are supported.
        string type = 1;

        // Target storage class. Required iff the type of the action is
        // SetStorageClass.
        string storage_class = 2;
      }

      // A condition of an object which triggers some action.
      message Condition {
        // Age of an object (in days). This condition is satisfied when an
        // object reaches the specified age.
        int32 age = 1;

        // A date in [RFC 3339][1] format with only the date part (for
        // instance, "2013-01-15"). This condition is satisfied when an
        // object is created before midnight of the specified date in UTC.
        // [1]: https://tools.ietf.org/html/rfc3339
        google.protobuf.Timestamp created_before = 2;

        // Relevant only for versioned objects. If the value is
        // `true`, this condition matches live objects; if the value
        // is `false`, it matches archived objects.
        google.protobuf.BoolValue is_live = 3;

        // Relevant only for versioned objects. If the value is N, this
        // condition is satisfied when there are at least N versions (including
        // the live version) newer than this version of the object.
        int32 num_newer_versions = 4;

        // Objects having any of the storage classes specified by this condition
        // will be matched. Values include `MULTI_REGIONAL`, `REGIONAL`,
        // `NEARLINE`, `COLDLINE`, `STANDARD`, and
        // `DURABLE_REDUCED_AVAILABILITY`.
        repeated string matches_storage_class = 5;

        // A regular expression that satisfies the RE2 syntax. This condition is
        // satisfied when the name of the object matches the RE2 pattern.  Note:
        // This feature is currently in the "Early Access" launch stage and is
        // only available to an allowlisted set of users; that means that this
        // feature may be changed in backward-incompatible ways and that it is
        // not guaranteed to be released.
        string matches_pattern = 6;

        // Number of days that has elapsed since the custom timestamp set on an
        // object.
        int32 days_since_custom_time = 7;

        // An object matches this condition if the custom timestamp set on the
        // object is before this timestamp.
        google.protobuf.Timestamp custom_time_before = 8;

        // This condition is relevant only for versioned objects. An object
        // version satisfies this condition only if these many days have been
        // passed since it became noncurrent. The value of the field must be a
        // nonnegative integer. If it's zero, the object version will become
        // eligible for Lifecycle action as soon as it becomes noncurrent.
        int32 days_since_noncurrent_time = 9;

        // This condition is relevant only for versioned objects. An object
        // version satisfies this condition only if it became noncurrent before
        // the specified timestamp.
        google.protobuf.Timestamp noncurrent_time_before = 10;

        // List of object name prefixes. If any prefix exactly matches the
        // beginning of the object name, the condition evaluates to true.
        repeated string matches_prefix = 11;

        // List of object name suffixes. If any suffix exactly matches the
        // end of the object name, the condition evaluates to true.
        repeated string matches_suffix = 12;
      }

      // The action to take.
      Action action = 1;

      // The condition(s) under which the action will be taken.
      Condition condition = 2;
    }

    // A lifecycle management rule, which is made of an action to take and the
    // condition(s) under which the action will be taken.
    repeated Rule rule = 1;
  }

  // Logging-related properties of a bucket.
  message Logging {
    // The destination bucket where the current bucket's logs should be placed.
    string log_bucket = 1;

    // A prefix for log object names.
    string log_object_prefix = 2;
  }

  // Retention policy properties of a bucket.
  message RetentionPolicy {
    // Server-determined value that indicates the time from which policy was
    // enforced and effective. This value is in
    // [https://tools.ietf.org/html/rfc3339][RFC 3339] format.
    google.protobuf.Timestamp effective_time = 1;

    // Once locked, an object retention policy cannot be modified.
    bool is_locked = 2;

    // The duration in seconds that objects need to be retained. Retention
    // duration must be greater than zero and less than 100 years. Note that
    // enforcement of retention periods less than a day is not guaranteed. Such
    // periods should only be used for testing purposes.
    int64 retention_period = 3;
  }

  // Properties of a bucket related to versioning.
  // For more on GCS versioning, see
  // https://cloud.google.com/storage/docs/object-versioning.
  message Versioning {
    // While set to true, versioning is fully enabled for this bucket.
    bool enabled = 1;
  }

  // Properties of a bucket related to accessing the contents as a static
  // website. For more on hosting a static website via GCS, see
  // https://cloud.google.com/storage/docs/hosting-static-website.
  message Website {
    // If the requested object path is missing, the service will ensure the path
    // has a trailing '/', append this suffix, and attempt to retrieve the
    // resulting object. This allows the creation of `index.html`
    // objects to represent directory pages.
    string main_page_suffix = 1;

    // If the requested object path is missing, and any
    // `mainPageSuffix` object is missing, if applicable, the service
    // will return the named object from this bucket as the content for a
    // [https://tools.ietf.org/html/rfc7231#section-6.5.4][404 Not Found]
    // result.
    string not_found_page = 2;
  }

  // Configuration for a bucket's Autoclass feature.
  message Autoclass {
    // Enables Autoclass.
    bool enabled = 1;

    // Latest instant at which the `enabled` bit was flipped.
    google.protobuf.Timestamp toggle_time = 2;
  }

  // Access controls on the bucket.
  repeated BucketAccessControl acl = 1;

  // Default access controls to apply to new objects when no ACL is provided.
  repeated ObjectAccessControl default_object_acl = 2;

  // The bucket's lifecycle configuration. See
  // [https://developers.google.com/storage/docs/lifecycle]Lifecycle Management]
  // for more information.
  Lifecycle lifecycle = 3;

  // The creation time of the bucket in
  // [https://tools.ietf.org/html/rfc3339][RFC 3339] format.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  google.protobuf.Timestamp time_created = 4;

  // The ID of the bucket. For buckets, the `id` and `name` properties are the
  // same.
  // Attempting to update this field after the bucket is created will result in
  // a [FieldViolation][google.rpc.BadRequest.FieldViolation].
  string id = 5;

  // The name of the bucket.
  // Attempting to update this field after the bucket is created will result in
  // an error.
  string name = 6;

  // The project number of the project the bucket belongs to.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  int64 project_number = 7;

  // The metadata generation of this bucket.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  int64 metageneration = 8;

  // The bucket's [https://www.w3.org/TR/cors/][Cross-Origin Resource Sharing]
  // (CORS) configuration.
  repeated Cors cors = 9;

  // The location of the bucket. Object data for objects in the bucket resides
  // in physical storage within this region.  Defaults to `US`. See the
  // [https://developers.google.com/storage/docs/concepts-techniques#specifyinglocations"][developer's
  // guide] for the authoritative list. Attempting to update this field after
  // the bucket is created will result in an error.
  string location = 10;

  // The bucket's default storage class, used whenever no storageClass is
  // specified for a newly-created object. This defines how objects in the
  // bucket are stored and determines the SLA and the cost of storage.
  // If this value is not specified when the bucket is created, it will default
  // to `STANDARD`. For more information, see
  // https://developers.google.com/storage/docs/storage-classes.
  string storage_class = 11;

  // HTTP 1.1 [https://tools.ietf.org/html/rfc7232#section-2.3"]Entity tag]
  // for the bucket.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  string etag = 12;

  // The modification time of the bucket.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  google.protobuf.Timestamp updated = 13;

  // The default value for event-based hold on newly created objects in this
  // bucket.  Event-based hold is a way to retain objects indefinitely until an
  // event occurs, signified by the
  // hold's release. After being released, such objects will be subject to
  // bucket-level retention (if any).  One sample use case of this flag is for
  // banks to hold loan documents for at least 3 years after loan is paid in
  // full. Here, bucket-level retention is 3 years and the event is loan being
  // paid in full. In this example, these objects will be held intact for any
  // number of years until the event has occurred (event-based hold on the
  // object is released) and then 3 more years after that. That means retention
  // duration of the objects begins from the moment event-based hold
  // transitioned from true to false.  Objects under event-based hold cannot be
  // deleted, overwritten or archived until the hold is removed.
  bool default_event_based_hold = 14;

  // User-provided labels, in key/value pairs.
  map<string, string> labels = 15;

  // The bucket's website configuration, controlling how the service behaves
  // when accessing bucket contents as a web site. See the
  // [https://cloud.google.com/storage/docs/static-website][Static Website
  // Examples] for more information.
  Website website = 16;

  // The bucket's versioning configuration.
  Versioning versioning = 17;

  // The bucket's logging configuration, which defines the destination bucket
  // and optional name prefix for the current bucket's logs.
  Logging logging = 18;

  // The owner of the bucket. This is always the project team's owner group.
  Owner owner = 19;

  // Encryption configuration for a bucket.
  Encryption encryption = 20;

  // The bucket's billing configuration.
  Billing billing = 21;

  // The bucket's retention policy. The retention policy enforces a minimum
  // retention time for all objects contained in the bucket, based on their
  // creation time. Any attempt to overwrite or delete objects younger than the
  // retention period will result in a PERMISSION_DENIED error.  An unlocked
  // retention policy can be modified or removed from the bucket via a
  // storage.buckets.update operation. A locked retention policy cannot be
  // removed or shortened in duration for the lifetime of the bucket.
  // Attempting to remove or decrease period of a locked retention policy will
  // result in a PERMISSION_DENIED error.
  RetentionPolicy retention_policy = 22;

  // The location type of the bucket (region, dual-region, multi-region, etc).
  string location_type = 23;

  // The bucket's IAM configuration.
  IamConfiguration iam_configuration = 24;

  // The zone or zones from which the bucket is intended to use zonal quota.
  // Requests for data from outside the specified affinities are still allowed
  // but won't be able to use zonal quota. The values are case-insensitive.
  // Attempting to update this field after bucket is created will result in an
  // error.
  repeated string zone_affinity = 25 [deprecated = true];

  // Reserved for future use.
  bool satisfies_pzs = 26;

  // The bucket's autoclass configuration. If there is no configuration, the
  // Autoclass feature will be disabled and have no effect on the bucket.
  Autoclass autoclass = 28;
}

// An access-control entry.
message BucketAccessControl {
  // The access permission for the entity.
  string role = 1;

  // HTTP 1.1 ["https://tools.ietf.org/html/rfc7232#section-2.3][Entity tag]
  // for the access-control entry.
  string etag = 2;

  // The ID of the access-control entry.
  string id = 3;

  // The name of the bucket.
  string bucket = 4;

  // The entity holding the permission, in one of the following forms:
  // * `user-{userid}`
  // * `user-{email}`
  // * `group-{groupid}`
  // * `group-{email}`
  // * `domain-{domain}`
  // * `project-{team-projectid}`
  // * `allUsers`
  // * `allAuthenticatedUsers`
  // Examples:
  // * The user `<EMAIL>` would be `<EMAIL>`.
  // * The group `<EMAIL>` would be
  // `<EMAIL>`
  // * All members of the Google Apps for Business domain `example.com` would be
  // `domain-example.com`
  string entity = 6;

  // The ID for the entity, if any.
  string entity_id = 7;

  // The email address associated with the entity, if any.
  string email = 8;

  // The domain associated with the entity, if any.
  string domain = 9;

  // The project team associated with the entity, if any.
  ProjectTeam project_team = 10;
}

// The response to a call to BucketAccessControls.ListBucketAccessControls.
message ListBucketAccessControlsResponse {
  // The list of items.
  repeated BucketAccessControl items = 1;
}

// The result of a call to Buckets.ListBuckets
message ListBucketsResponse {
  // The list of items.
  repeated Bucket items = 1;

  // The continuation token, used to page through large result sets. Provide
  // this value in a subsequent request to return the next page of results.
  string next_page_token = 2;
}

// An notification channel used to watch for resource changes.
message Channel {
  // A UUID or similar unique string that identifies this channel.
  string id = 1;

  // An opaque ID that identifies the resource being watched on this channel.
  // Stable across different API versions.
  string resource_id = 2;

  // A version-specific identifier for the watched resource.
  string resource_uri = 3;

  // An arbitrary string delivered to the target address with each notification
  // delivered over this channel. Optional.
  string token = 4;

  // Date and time of notification channel expiration. Optional.
  google.protobuf.Timestamp expiration = 5;

  // The type of delivery mechanism used for this channel.
  string type = 6;

  // The address where notifications are delivered for this channel.
  string address = 7;

  // Additional parameters controlling delivery channel behavior. Optional.
  map<string, string> params = 8;

  // A Boolean value to indicate whether payload is wanted. Optional.
  bool payload = 9;
}

// The result of a call to Channels.ListChannels
message ListChannelsResponse {
  message Items {
    // User-specified name for a channel. Needed to unsubscribe.
    string channel_id = 1;

    // Opaque value generated by GCS representing a bucket. Needed to
    // unsubscribe.
    string resource_id = 2;

    // Url used to identify where notifications are sent to.
    string push_url = 3;

    // Email address of the subscriber.
    string subscriber_email = 4;

    // Time when the channel was created.
    google.protobuf.Timestamp creation_time = 5;
  }

  // The list of notification channels for a bucket.
  repeated Items items = 1;
}

// Message used to convey content being read or written, along with its
// checksum.
message ChecksummedData {
  // The data.
  bytes content = 1;

  // CRC32C digest of the contents.
  google.protobuf.UInt32Value crc32c = 2;
}

// Message used for storing full (not subrange) object checksums.
message ObjectChecksums {
  // CRC32C digest of the object data. Computed by the GCS service for
  // all written objects, and validated by the GCS service against
  // client-supplied values if present in an InsertObjectRequest.
  google.protobuf.UInt32Value crc32c = 1;

  // Hex-encoded MD5 hash of the object data (hexdigest). Whether/how this
  // checksum is provided and validated is service-dependent.
  string md5_hash = 2;
}

// A collection of enums used in multiple places throughout the API.
message CommonEnums {
  // A set of properties to return in a response.
  enum Projection {
    // No specified projection.
    PROJECTION_UNSPECIFIED = 0;

    // Omit `owner`, `acl`, and `defaultObjectAcl` properties.
    NO_ACL = 1;

    // Include all properties.
    FULL = 2;
  }

  // Predefined or "canned" aliases for sets of specific bucket ACL entries.
  enum PredefinedBucketAcl {
    // No predefined ACL.
    PREDEFINED_BUCKET_ACL_UNSPECIFIED = 0;

    // Project team owners get `OWNER` access, and
    // `allAuthenticatedUsers` get `READER` access.
    BUCKET_ACL_AUTHENTICATED_READ = 1;

    // Project team owners get `OWNER` access.
    BUCKET_ACL_PRIVATE = 2;

    // Project team members get access according to their roles.
    BUCKET_ACL_PROJECT_PRIVATE = 3;

    // Project team owners get `OWNER` access, and
    // `allUsers` get `READER` access.
    BUCKET_ACL_PUBLIC_READ = 4;

    // Project team owners get `OWNER` access, and
    // `allUsers` get `WRITER` access.
    BUCKET_ACL_PUBLIC_READ_WRITE = 5;
  }

  // Predefined or "canned" aliases for sets of specific object ACL entries.
  enum PredefinedObjectAcl {
    // No predefined ACL.
    PREDEFINED_OBJECT_ACL_UNSPECIFIED = 0;

    // Object owner gets `OWNER` access, and
    // `allAuthenticatedUsers` get `READER` access.
    OBJECT_ACL_AUTHENTICATED_READ = 1;

    // Object owner gets `OWNER` access, and project team owners get
    // `OWNER` access.
    OBJECT_ACL_BUCKET_OWNER_FULL_CONTROL = 2;

    // Object owner gets `OWNER` access, and project team owners get
    // `READER` access.
    OBJECT_ACL_BUCKET_OWNER_READ = 3;

    // Object owner gets `OWNER` access.
    OBJECT_ACL_PRIVATE = 4;

    // Object owner gets `OWNER` access, and project team members get
    // access according to their roles.
    OBJECT_ACL_PROJECT_PRIVATE = 5;

    // Object owner gets `OWNER` access, and `allUsers`
    // get `READER` access.
    OBJECT_ACL_PUBLIC_READ = 6;
  }


}

// Specifies a requested range of bytes to download.
message ContentRange {
  // The starting offset of the object data.
  int64 start = 1;

  // The ending offset of the object data.
  int64 end = 2;

  // The complete length of the object data.
  int64 complete_length = 3;
}

// Hmac Key Metadata, which includes all information other than the secret.
message HmacKeyMetadata {
  // Resource name ID of the key in the format <projectId>/<accessId>.
  string id = 1;

  // Globally unique id for keys.
  string access_id = 2;

  // The project ID that the hmac key is contained in.
  string project_id = 3;

  // Email of the service account the key authenticates as.
  string service_account_email = 4;

  // State of the key. One of ACTIVE, INACTIVE, or DELETED.
  string state = 5;

  // The creation time of the HMAC key in RFC 3339 format.
  google.protobuf.Timestamp time_created = 6;

  // The last modification time of the HMAC key metadata in RFC 3339 format.
  google.protobuf.Timestamp updated = 7;

  // Tag updated with each key update.
  string etag = 8;
}

// A subscription to receive Google PubSub notifications.
message Notification {
  // The Cloud PubSub topic to which this subscription publishes. Formatted as:
  // '//pubsub.googleapis.com/projects/{project-identifier}/topics/{my-topic}'
  string topic = 1;

  // If present, only send notifications about listed event types. If empty,
  // sent notifications for all event types.
  repeated string event_types = 2;

  // An optional list of additional attributes to attach to each Cloud PubSub
  // message published for this notification subscription.
  map<string, string> custom_attributes = 3;

  // HTTP 1.1 [https://tools.ietf.org/html/rfc7232#section-2.3][Entity tag]
  // for this subscription notification.
  string etag = 4;

  // If present, only apply this notification configuration to object names that
  // begin with this prefix.
  string object_name_prefix = 5;

  // The desired content of the Payload.
  string payload_format = 6;

  // The ID of the notification.
  string id = 7;
}

// The result of a call to Notifications.ListNotifications
message ListNotificationsResponse {
  // The list of items.
  repeated Notification items = 1;
}

// An object.
message Object {
  // Describes the customer-specified mechanism used to store the data at rest.
  message CustomerEncryption {
    // The encryption algorithm.
    string encryption_algorithm = 1;

    // SHA256 hash value of the encryption key.
    string key_sha256 = 2;
  }

  // Content-Encoding of the object data, matching
  // [https://tools.ietf.org/html/rfc7231#section-*******][RFC 7231 §*******]
  string content_encoding = 1;

  // Content-Disposition of the object data, matching
  // [https://tools.ietf.org/html/rfc6266][RFC 6266].
  string content_disposition = 2;

  // Cache-Control directive for the object data, matching
  // [https://tools.ietf.org/html/rfc7234#section-5.2"][RFC 7234 §5.2].
  // If omitted, and the object is accessible to all anonymous users, the
  // default will be `public, max-age=3600`.
  string cache_control = 3;

  // Access controls on the object.
  repeated ObjectAccessControl acl = 4;

  // Content-Language of the object data, matching
  // [https://tools.ietf.org/html/rfc7231#section-3.1.3.2][RFC 7231 §3.1.3.2].
  string content_language = 5;

  // The version of the metadata for this object at this generation. Used for
  // preconditions and for detecting changes in metadata. A metageneration
  // number is only meaningful in the context of a particular generation of a
  // particular object.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  int64 metageneration = 6;

  // The deletion time of the object. Will be returned if and only if this
  // version of the object has been deleted.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  google.protobuf.Timestamp time_deleted = 7;

  // Content-Type of the object data, matching
  // [https://tools.ietf.org/html/rfc7231#section-3.1.1.5][RFC 7231 §3.1.1.5].
  // If an object is stored without a Content-Type, it is served as
  // `application/octet-stream`.
  string content_type = 8;

  // Content-Length of the object data in bytes, matching
  // [https://tools.ietf.org/html/rfc7230#section-3.3.2][RFC 7230 §3.3.2].
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  int64 size = 9;

  // The creation time of the object.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  google.protobuf.Timestamp time_created = 10;

  // CRC32c checksum. For more information about using the CRC32c
  // checksum, see
  // [https://cloud.google.com/storage/docs/hashes-etags#json-api][Hashes and
  // ETags: Best Practices]. This is a server determined value and should not be
  // supplied by the user when sending an Object. The server will ignore any
  // value provided. Users should instead use the object_checksums field on the
  // InsertObjectRequest when uploading an object.
  google.protobuf.UInt32Value crc32c = 11;

  // Number of underlying components that make up this object. Components are
  // accumulated by compose operations.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  int32 component_count = 12;

  // MD5 hash of the data; encoded using base64 as per
  // [https://tools.ietf.org/html/rfc4648#section-4][RFC 4648 §4]. For more
  // information about using the MD5 hash, see
  // [https://cloud.google.com/storage/docs/hashes-etags#json-api][Hashes and
  // ETags: Best Practices]. This is a server determined value and should not be
  // supplied by the user when sending an Object. The server will ignore any
  // value provided. Users should instead use the object_checksums field on the
  // InsertObjectRequest when uploading an object.
  string md5_hash = 13;

  // HTTP 1.1 Entity tag for the object. See
  // [https://tools.ietf.org/html/rfc7232#section-2.3][RFC 7232 §2.3].
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  string etag = 14;

  // The modification time of the object metadata.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  google.protobuf.Timestamp updated = 15;

  // Storage class of the object.
  string storage_class = 16;

  // Cloud KMS Key used to encrypt this object, if the object is encrypted by
  // such a key.
  string kms_key_name = 17;

  // The time at which the object's storage class was last changed. When the
  // object is initially created, it will be set to time_created.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  google.protobuf.Timestamp time_storage_class_updated = 18;

  // Whether an object is under temporary hold. While this flag is set to true,
  // the object is protected against deletion and overwrites.  A common use case
  // of this flag is regulatory investigations where objects need to be retained
  // while the investigation is ongoing. Note that unlike event-based hold,
  // temporary hold does not impact retention expiration time of an object.
  bool temporary_hold = 19;

  // A server-determined value that specifies the earliest time that the
  // object's retention period expires. This value is in
  // [https://tools.ietf.org/html/rfc3339][RFC 3339] format.
  // Note 1: This field is not provided for objects with an active event-based
  // hold, since retention expiration is unknown until the hold is removed.
  // Note 2: This value can be provided even when temporary hold is set (so that
  // the user can reason about policy without having to first unset the
  // temporary hold).
  google.protobuf.Timestamp retention_expiration_time = 20;

  // User-provided metadata, in key/value pairs.
  map<string, string> metadata = 21;

  // Whether an object is under event-based hold. Event-based hold is a way to
  // retain objects until an event occurs, which is signified by the
  // hold's release (i.e. this value is set to false). After being released (set
  // to false), such objects will be subject to bucket-level retention (if any).
  // One sample use case of this flag is for banks to hold loan documents for at
  // least 3 years after loan is paid in full. Here, bucket-level retention is 3
  // years and the event is the loan being paid in full. In this example, these
  // objects will be held intact for any number of years until the event has
  // occurred (event-based hold on the object is released) and then 3 more years
  // after that. That means retention duration of the objects begins from the
  // moment event-based hold transitioned from true to false.
  google.protobuf.BoolValue event_based_hold = 29;

  // The name of the object.
  // Attempting to update this field after the object is created will result in
  // an error.
  string name = 23;

  // The ID of the object, including the bucket name, object name, and
  // generation number.
  // Attempting to update this field after the object is created will result in
  // an error.
  string id = 24;

  // The name of the bucket containing this object.
  // Attempting to update this field after the object is created will result in
  // an error.
  string bucket = 25;

  // The content generation of this object. Used for object versioning.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  int64 generation = 26;

  // The owner of the object. This will always be the uploader of the object.
  // Attempting to set or update this field will result in a
  // [FieldViolation][google.rpc.BadRequest.FieldViolation].
  Owner owner = 27;

  // Metadata of customer-supplied encryption key, if the object is encrypted by
  // such a key.
  CustomerEncryption customer_encryption = 28;

  // A user-specified timestamp set on an object.
  google.protobuf.Timestamp custom_time = 30;
}

// An access-control entry.
message ObjectAccessControl {
  // The access permission for the entity.
  string role = 1;

  // HTTP 1.1 Entity tag for the access-control entry.
  // See [https://tools.ietf.org/html/rfc7232#section-2.3][RFC 7232 §2.3].
  string etag = 2;

  // The ID of the access-control entry.
  string id = 3;

  // The name of the bucket.
  string bucket = 4;

  // The name of the object, if applied to an object.
  string object = 5;

  // The content generation of the object, if applied to an object.
  int64 generation = 6;

  // The entity holding the permission, in one of the following forms:
  // * `user-{userid}`
  // * `user-{email}`
  // * `group-{groupid}`
  // * `group-{email}`
  // * `domain-{domain}`
  // * `project-{team-projectid}`
  // * `allUsers`
  // * `allAuthenticatedUsers`
  // Examples:
  // * The user `<EMAIL>` would be `<EMAIL>`.
  // * The group `<EMAIL>` would be
  // `<EMAIL>`.
  // * All members of the Google Apps for Business domain `example.com` would be
  // `domain-example.com`.
  string entity = 7;

  // The ID for the entity, if any.
  string entity_id = 8;

  // The email address associated with the entity, if any.
  string email = 9;

  // The domain associated with the entity, if any.
  string domain = 10;

  // The project team associated with the entity, if any.
  ProjectTeam project_team = 11;
}

// The result of a call to ObjectAccessControls.ListObjectAccessControls.
message ListObjectAccessControlsResponse {
  // The list of items.
  repeated ObjectAccessControl items = 1;
}

// The result of a call to Objects.ListObjects
message ListObjectsResponse {
  // The list of prefixes of objects matching-but-not-listed up to and including
  // the requested delimiter.
  repeated string prefixes = 1;

  // The list of items.
  repeated Object items = 2;

  // The continuation token, used to page through large result sets. Provide
  // this value in a subsequent request to return the next page of results.
  string next_page_token = 3;
}

// Represents the Viewers, Editors, or Owners of a given project.
message ProjectTeam {
  // The project number.
  string project_number = 1;

  // The team.
  string team = 2;
}

// A subscription to receive Google PubSub notifications.
message ServiceAccount {
  // The ID of the notification.
  string email_address = 1;
}

// The owner of a specific resource.
message Owner {
  // The entity, in the form `user-`*userId*.
  string entity = 1;

  // The ID for the entity.
  string entity_id = 2;
}
