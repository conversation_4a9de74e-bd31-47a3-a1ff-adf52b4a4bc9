# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "storage_proto",
    srcs = [
        "storage.proto",
        "storage_resources.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
        "//google/iam/v1:iam_policy_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "storage_java_proto",
    deps = [":storage_proto"],
)

java_grpc_library(
    name = "storage_java_grpc",
    srcs = [":storage_proto"],
    deps = [":storage_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "storage_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/storage/v1",
    protos = [":storage_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "storage_moved_proto",
    srcs = [":storage_proto"],
    deps = [
        "//google/api:field_behavior_proto",
        "//google/iam/v1:iam_policy_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

py_proto_library(
    name = "storage_py_proto",
    deps = [":storage_moved_proto"],
)

py_grpc_library(
    name = "storage_py_grpc",
    srcs = [":storage_moved_proto"],
    deps = [":storage_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "storage_php_proto",
    deps = [":storage_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "storage_ruby_proto",
    deps = [":storage_proto"],
)

ruby_grpc_library(
    name = "storage_ruby_grpc",
    srcs = [":storage_proto"],
    deps = [":storage_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "storage_csharp_proto",
    deps = [":storage_proto"],
)

csharp_grpc_library(
    name = "storage_csharp_grpc",
    srcs = [":storage_proto"],
    deps = [":storage_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "storage_cc_proto",
    deps = [":storage_proto"],
)

cc_grpc_library(
    name = "storage_cc_grpc",
    srcs = [":storage_proto"],
    grpc_only = True,
    deps = [":storage_cc_proto"],
)
