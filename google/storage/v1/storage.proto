// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.storage.v1;

import "google/api/field_behavior.proto";
import "google/iam/v1/iam_policy.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/wrappers.proto";
import "google/storage/v1/storage_resources.proto";

option go_package = "google.golang.org/genproto/googleapis/storage/v1;storage";
option java_multiple_files = true;
option java_package = "com.google.storage.v1";

// Request message for DeleteBucketAccessControl.
message DeleteBucketAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Request message for GetBucketAccessControl.
message GetBucketAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Request message for InsertBucketAccessControl.
message InsertBucketAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Properties of the new bucket access control being inserted.
  BucketAccessControl bucket_access_control = 3;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Request message for ListBucketAccessControl.
message ListBucketAccessControlsRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 3;
}

// Request for PatchBucketAccessControl.
message PatchBucketAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // The BucketAccessControl for updating.
  BucketAccessControl bucket_access_control = 4;

  // List of fields to be updated.
  //
  // To specify ALL fields, equivalent to the JSON API's "update" function,
  // specify a single field with the value `*`.
  //
  //
  // Not specifying any fields is an error.
  // Not specifying a field while setting that field to a non-default value is
  // an error.
  google.protobuf.FieldMask update_mask = 5;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 6;
}

// Request for UpdateBucketAccessControl.
message UpdateBucketAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // The BucketAccessControl for updating.
  BucketAccessControl bucket_access_control = 4;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 5;
}

// Request message for DeleteBucket.
message DeleteBucketRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // If set, only deletes the bucket if its metageneration matches this value.
  google.protobuf.Int64Value if_metageneration_match = 2;

  // If set, only deletes the bucket if its metageneration does not match this
  // value.
  google.protobuf.Int64Value if_metageneration_not_match = 3;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 5;
}

// Request message for GetBucket.
message GetBucketRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Makes the return of the bucket metadata conditional on whether the bucket's
  // current metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 2;

  // Makes the return of the bucket metadata conditional on whether the bucket's
  // current metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 3;

  // Set of properties to return. Defaults to `NO_ACL`.
  CommonEnums.Projection projection = 4;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 6;
}

// Request message for InsertBucket.
message InsertBucketRequest {
  // Apply a predefined set of access controls to this bucket.
  CommonEnums.PredefinedBucketAcl predefined_acl = 1;

  // Apply a predefined set of default object access controls to this bucket.
  CommonEnums.PredefinedObjectAcl predefined_default_object_acl = 2;

  // Required. A valid API project identifier.
  string project = 3 [(google.api.field_behavior) = REQUIRED];

  // Set of properties to return. Defaults to `NO_ACL`, unless the
  // bucket resource specifies `acl` or `defaultObjectAcl`
  // properties, when it defaults to `FULL`.
  CommonEnums.Projection projection = 4;

  // Properties of the new bucket being inserted, including its name.
  Bucket bucket = 6;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 7;
}

// Request message for ListChannels.
message ListChannelsRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 3;
}

// Request message for ListBuckets.
message ListBucketsRequest {
  // Maximum number of buckets to return in a single response. The service will
  // use this parameter or 1,000 items, whichever is smaller.
  int32 max_results = 1;

  // A previously-returned page token representing part of the larger set of
  // results to view.
  string page_token = 2;

  // Filter results to buckets whose names begin with this prefix.
  string prefix = 3;

  // Required. A valid API project identifier.
  string project = 4 [(google.api.field_behavior) = REQUIRED];

  // Set of properties to return. Defaults to `NO_ACL`.
  CommonEnums.Projection projection = 5;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 7;
}

// Request message for LockRetentionPolicy.
message LockRetentionPolicyRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Makes the operation conditional on whether bucket's current metageneration
  // matches the given value. Must be positive.
  int64 if_metageneration_match = 2;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Request for PatchBucket method.
message PatchBucketRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Makes the return of the bucket metadata conditional on whether the bucket's
  // current metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 2;

  // Makes the return of the bucket metadata conditional on whether the bucket's
  // current metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 3;

  // Apply a predefined set of access controls to this bucket.
  CommonEnums.PredefinedBucketAcl predefined_acl = 4;

  // Apply a predefined set of default object access controls to this bucket.
  CommonEnums.PredefinedObjectAcl predefined_default_object_acl = 5;

  // Set of properties to return. Defaults to `FULL`.
  CommonEnums.Projection projection = 6;

  // The Bucket metadata for updating.
  Bucket metadata = 8;

  // List of fields to be updated.
  //
  // To specify ALL fields, equivalent to the JSON API's "update" function,
  // specify a single field with the value `*`. Note: not recommended. If a new
  // field is introduced at a later time, an older client updating with the `*`
  // may accidentally reset the new field's value.
  //
  // Not specifying any fields is an error.
  // Not specifying a field while setting that field to a non-default value is
  // an error.
  google.protobuf.FieldMask update_mask = 9;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 10;
}

// Request for UpdateBucket method.
message UpdateBucketRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Makes the return of the bucket metadata conditional on whether the bucket's
  // current metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 2;

  // Makes the return of the bucket metadata conditional on whether the bucket's
  // current metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 3;

  // Apply a predefined set of access controls to this bucket.
  CommonEnums.PredefinedBucketAcl predefined_acl = 4;

  // Apply a predefined set of default object access controls to this bucket.
  CommonEnums.PredefinedObjectAcl predefined_default_object_acl = 5;

  // Set of properties to return. Defaults to `FULL`.
  CommonEnums.Projection projection = 6;

  // The Bucket metadata for updating.
  Bucket metadata = 8;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 9;
}

// Request message for StopChannel.
message StopChannelRequest {
  // The channel to be stopped.
  Channel channel = 1;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 2;
}

// Request message for DeleteDefaultObjectAccessControl.
message DeleteDefaultObjectAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Request message for GetDefaultObjectAccessControl.
message GetDefaultObjectAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Request message for InsertDefaultObjectAccessControl.
message InsertDefaultObjectAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Properties of the object access control being inserted.
  ObjectAccessControl object_access_control = 3;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Request message for ListDefaultObjectAccessControls.
message ListDefaultObjectAccessControlsRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // If present, only return default ACL listing if the bucket's current
  // metageneration matches this value.
  google.protobuf.Int64Value if_metageneration_match = 2;

  // If present, only return default ACL listing if the bucket's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 3;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 5;
}

// Request message for PatchDefaultObjectAccessControl.
message PatchDefaultObjectAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // The ObjectAccessControl for updating.
  ObjectAccessControl object_access_control = 4;

  // List of fields to be updated.
  //
  // To specify ALL fields, equivalent to the JSON API's "update" function,
  // specify a single field with the value `*`. Note: not recommended. If a new
  // field is introduced at a later time, an older client updating with the `*`
  // may accidentally reset the new field's value.
  //
  // Not specifying any fields is an error.
  // Not specifying a field while setting that field to a non-default value is
  // an error.
  google.protobuf.FieldMask update_mask = 5;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 6;
}

// Request message for UpdateDefaultObjectAccessControl.
message UpdateDefaultObjectAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // The ObjectAccessControl for updating.
  ObjectAccessControl object_access_control = 4;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 5;
}

// Request message for DeleteNotification.
message DeleteNotificationRequest {
  // Required. The parent bucket of the notification.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. ID of the notification to delete.
  string notification = 2 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Request message for GetNotification.
message GetNotificationRequest {
  // Required. The parent bucket of the notification.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Notification ID.
  // Required.
  string notification = 2 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Request message for InsertNotification.
message InsertNotificationRequest {
  // Required. The parent bucket of the notification.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Properties of the notification to be inserted.
  Notification notification = 3;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Request message for ListNotifications.
message ListNotificationsRequest {
  // Required. Name of a Google Cloud Storage bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 3;
}

// Request message for DeleteObjectAccessControl.
message DeleteObjectAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the object.
  string object = 3 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of this object (as opposed to the
  // latest version, the default).
  int64 generation = 4;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 6;
}

// Request message for GetObjectAccessControl.
message GetObjectAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the object.
  string object = 3 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of this object (as opposed to the
  // latest version, the default).
  int64 generation = 4;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 6;
}

// Request message for InsertObjectAccessControl.
message InsertObjectAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the object.
  string object = 2 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of this object (as opposed to the
  // latest version, the default).
  int64 generation = 3;

  // Properties of the object access control to be inserted.
  ObjectAccessControl object_access_control = 5;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 6;
}

// Request message for ListObjectAccessControls.
message ListObjectAccessControlsRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the object.
  string object = 2 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of this object (as opposed to the
  // latest version, the default).
  int64 generation = 3;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 5;
}

// Request message for PatchObjectAccessControl.
message PatchObjectAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the object.
  // Required.
  string object = 3 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of this object (as opposed to the
  // latest version, the default).
  int64 generation = 4;

  // The ObjectAccessControl for updating.
  ObjectAccessControl object_access_control = 5;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 6;

  // List of fields to be updated.
  //
  // To specify ALL fields, equivalent to the JSON API's "update" function,
  // specify a single field with the value `*`. Note: not recommended. If a new
  // field is introduced at a later time, an older client updating with the `*`
  // may accidentally reset the new field's value.
  //
  // Not specifying any fields is an error.
  // Not specifying a field while setting that field to a non-default value is
  // an error.
  google.protobuf.FieldMask update_mask = 7;
}

// Request message for UpdateObjectAccessControl.
message UpdateObjectAccessControlRequest {
  // Required. Name of a bucket.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The entity holding the permission. Can be one of:
  // * `user-`*userId*
  // * `user-`*emailAddress*
  // * `group-`*groupId*
  // * `group-`*emailAddress*
  // * `allUsers`
  // * `allAuthenticatedUsers`
  string entity = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the object.
  // Required.
  string object = 3 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of this object (as opposed to the
  // latest version, the default).
  int64 generation = 4;

  // The ObjectAccessControl for updating.
  ObjectAccessControl object_access_control = 6;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 7;

  // List of fields to be updated.
  //
  // To specify ALL fields, equivalent to the JSON API's "update" function,
  // specify a single field with the value `*`. Note: not recommended. If a new
  // field is introduced at a later time, an older client updating with the `*`
  // may accidentally reset the new field's value.
  //
  // Not specifying any fields is an error.
  // Not specifying a field while setting that field to a non-default value is
  // an error.
  google.protobuf.FieldMask update_mask = 8;
}

// Request message for ComposeObject.
message ComposeObjectRequest {
  // Description of a source object for a composition request.
  message SourceObjects {
    // Preconditions for a source object of a composition request.
    message ObjectPreconditions {
      // Only perform the composition if the generation of the source object
      // that would be used matches this value.  If this value and a generation
      // are both specified, they must be the same value or the call will fail.
      google.protobuf.Int64Value if_generation_match = 1;
    }

    // The source object's name. All source objects must reside in the same
    // bucket.
    string name = 1;

    // The generation of this object to use as the source.
    int64 generation = 2;

    // Conditions that must be met for this operation to execute.
    ObjectPreconditions object_preconditions = 3;
  }

  // Required. Name of the bucket containing the source objects. The destination object is
  // stored in this bucket.
  string destination_bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the new object.
  string destination_object = 2 [(google.api.field_behavior) = REQUIRED];

  // Apply a predefined set of access controls to the destination object.
  CommonEnums.PredefinedObjectAcl destination_predefined_acl = 3;

  // Properties of the resulting object.
  Object destination = 11;

  // The list of source objects that will be concatenated into a single object.
  repeated SourceObjects source_objects = 12;

  // Makes the operation conditional on whether the object's current generation
  // matches the given value. Setting to 0 makes the operation succeed only if
  // there are no live versions of the object.
  google.protobuf.Int64Value if_generation_match = 4;

  // Makes the operation conditional on whether the object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 5;

  // Resource name of the Cloud KMS key, of the form
  // `projects/my-project/locations/my-location/keyRings/my-kr/cryptoKeys/my-key`,
  // that will be used to encrypt the object. Overrides the object
  // metadata's `kms_key_name` value, if any.
  string kms_key_name = 6;

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 9;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 10;
}

// Request message for CopyObject.
message CopyObjectRequest {
  // Required. Name of the bucket in which to store the new object. Overrides the provided
  // object
  // metadata's `bucket` value, if any.
  string destination_bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the new object.
  // Required when the object metadata is not otherwise provided. Overrides the
  // object metadata's `name` value, if any.
  string destination_object = 2 [(google.api.field_behavior) = REQUIRED];

  // Apply a predefined set of access controls to the destination object.
  CommonEnums.PredefinedObjectAcl destination_predefined_acl = 3;

  // Makes the operation conditional on whether the destination object's current
  // generation matches the given value. Setting to 0 makes the operation
  // succeed only if there are no live versions of the object.
  google.protobuf.Int64Value if_generation_match = 4;

  // Makes the operation conditional on whether the destination object's current
  // generation does not match the given value. If no live object exists, the
  // precondition fails. Setting to 0 makes the operation succeed only if there
  // is a live version of the object.
  google.protobuf.Int64Value if_generation_not_match = 5;

  // Makes the operation conditional on whether the destination object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 6;

  // Makes the operation conditional on whether the destination object's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 7;

  // Makes the operation conditional on whether the source object's current
  // generation matches the given value.
  google.protobuf.Int64Value if_source_generation_match = 8;

  // Makes the operation conditional on whether the source object's current
  // generation does not match the given value.
  google.protobuf.Int64Value if_source_generation_not_match = 9;

  // Makes the operation conditional on whether the source object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_source_metageneration_match = 10;

  // Makes the operation conditional on whether the source object's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_source_metageneration_not_match = 11;

  // Set of properties to return. Defaults to `NO_ACL`, unless the
  // object resource specifies the `acl` property, when it defaults
  // to `full`.
  CommonEnums.Projection projection = 12;

  // Required. Name of the bucket in which to find the source object.
  string source_bucket = 13 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the source object.
  string source_object = 14 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of the source object (as opposed to
  // the latest version, the default).
  int64 source_generation = 15;

  // Properties of the resulting object. If not set, duplicate properties of
  // source object.
  Object destination = 17;

  // Resource name of the Cloud KMS key, of the form
  // `projects/my-project/locations/my-location/keyRings/my-kr/cryptoKeys/my-key`,
  // that will be used to encrypt the object. Overrides the object
  // metadata's `kms_key_name` value, if any.
  string destination_kms_key_name = 20;

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 18;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 19;
}

// Message for deleting an object.
// Either `bucket` and `object` *or* `upload_id` **must** be set (but not both).
message DeleteObjectRequest {
  // Required. Name of the bucket in which the object resides.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the object to delete (when not using a resumable write).
  string object = 2 [(google.api.field_behavior) = REQUIRED];

  // The resumable upload_id of the object to delete (when using a
  // resumable write). This should be copied from the `upload_id` field of
  // `StartResumableWriteResponse`.
  string upload_id = 3;

  // If present, permanently deletes a specific revision of this object (as
  // opposed to the latest version, the default).
  int64 generation = 4;

  // Makes the operation conditional on whether the object's current generation
  // matches the given value. Setting to 0 makes the operation succeed only if
  // there are no live versions of the object.
  google.protobuf.Int64Value if_generation_match = 5;

  // Makes the operation conditional on whether the object's current generation
  // does not match the given value. If no live object exists, the precondition
  // fails. Setting to 0 makes the operation succeed only if there is a live
  // version of the object.
  google.protobuf.Int64Value if_generation_not_match = 6;

  // Makes the operation conditional on whether the object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 7;

  // Makes the operation conditional on whether the object's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 8;

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 10;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 11;
}

// Request message for GetObjectMedia.
message GetObjectMediaRequest {
  // The name of the bucket containing the object to read.
  string bucket = 1;

  // The name of the object to read.
  string object = 2;

  // If present, selects a specific revision of this object (as opposed
  // to the latest version, the default).
  int64 generation = 3;

  // The offset for the first byte to return in the read, relative to the start
  // of the object.
  //
  // A negative `read_offset` value will be interpreted as the number of bytes
  // back from the end of the object to be returned. For example, if an object's
  // length is 15 bytes, a GetObjectMediaRequest with `read_offset` = -5 and
  // `read_limit` = 3 would return bytes 10 through 12 of the object. Requesting
  // a negative offset whose magnitude is larger than the size of the object
  // will result in an error.
  int64 read_offset = 4;

  // The maximum number of `data` bytes the server is allowed to return in the
  // sum of all `Object` messages. A `read_limit` of zero indicates that there
  // is no limit, and a negative `read_limit` will cause an error.
  //
  // If the stream returns fewer bytes than allowed by the `read_limit` and no
  // error occurred, the stream includes all data from the `read_offset` to the
  // end of the resource.
  int64 read_limit = 5;

  // Makes the operation conditional on whether the object's current generation
  // matches the given value. Setting to 0 makes the operation succeed only if
  // there are no live versions of the object.
  google.protobuf.Int64Value if_generation_match = 6;

  // Makes the operation conditional on whether the object's current generation
  // does not match the given value. If no live object exists, the precondition
  // fails. Setting to 0 makes the operation succeed only if there is a live
  // version of the object.
  google.protobuf.Int64Value if_generation_not_match = 7;

  // Makes the operation conditional on whether the object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 8;

  // Makes the operation conditional on whether the object's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 9;

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 11;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 12;
}

// Request message for GetObject.
message GetObjectRequest {
  // Required. Name of the bucket in which the object resides.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the object.
  string object = 2 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of this object (as opposed to the
  // latest version, the default).
  int64 generation = 3;

  // Makes the operation conditional on whether the object's current generation
  // matches the given value. Setting to 0 makes the operation succeed only if
  // there are no live versions of the object.
  google.protobuf.Int64Value if_generation_match = 4;

  // Makes the operation conditional on whether the object's current generation
  // does not match the given value. If no live object exists, the precondition
  // fails. Setting to 0 makes the operation succeed only if there is a live
  // version of the object.
  google.protobuf.Int64Value if_generation_not_match = 5;

  // Makes the operation conditional on whether the object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 6;

  // Makes the operation conditional on whether the object's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 7;

  // Set of properties to return. Defaults to `NO_ACL`.
  CommonEnums.Projection projection = 8;

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 10;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 11;
}

// Response message for GetObject.
message GetObjectMediaResponse {
  // A portion of the data for the object. The service **may** leave `data`
  // empty for any given `ReadResponse`. This enables the service to inform the
  // client that the request is still live while it is running an operation to
  // generate more data.
  ChecksummedData checksummed_data = 1;

  // The checksums of the complete object. The client should compute one of
  // these checksums over the downloaded object and compare it against the value
  // provided here.
  ObjectChecksums object_checksums = 2;

  // If read_offset and or read_limit was specified on the
  // GetObjectMediaRequest, ContentRange will be populated on the first
  // GetObjectMediaResponse message of the read stream.
  ContentRange content_range = 3;

  // Metadata of the object whose media is being returned.
  // Only populated in the first response in the stream.
  Object metadata = 4;
}

// Describes an attempt to insert an object, possibly over multiple requests.
message InsertObjectSpec {
  // Destination object, including its name and its metadata.
  Object resource = 1;

  // Apply a predefined set of access controls to this object.
  CommonEnums.PredefinedObjectAcl predefined_acl = 2;

  // Makes the operation conditional on whether the object's current
  // generation matches the given value. Setting to 0 makes the operation
  // succeed only if there are no live versions of the object.
  google.protobuf.Int64Value if_generation_match = 3;

  // Makes the operation conditional on whether the object's current
  // generation does not match the given value. If no live object exists, the
  // precondition fails. Setting to 0 makes the operation succeed only if
  // there is a live version of the object.
  google.protobuf.Int64Value if_generation_not_match = 4;

  // Makes the operation conditional on whether the object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 5;

  // Makes the operation conditional on whether the object's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 6;

  // Set of properties to return. Defaults to `NO_ACL`, unless the
  // object resource specifies the `acl` property, when it defaults
  // to `full`.
  CommonEnums.Projection projection = 7;
}

// Message for writing an object.
message InsertObjectRequest {
  // The first message of each stream should set one of the following.
  oneof first_message {
    // For resumable uploads. This should be the `upload_id` returned from a
    // call to `StartResumableWriteResponse`.
    string upload_id = 1;

    // For non-resumable uploads. Describes the overall upload, including the
    // destination bucket and object name, preconditions, etc.
    InsertObjectSpec insert_object_spec = 2;
  }

  // Required. The offset from the beginning of the object at which the data should be
  // written.
  //
  // In the first `InsertObjectRequest` of a `InsertObject()` action, it
  // indicates the initial offset for the `Insert()` call. The value **must** be
  // equal to the `committed_size` that a call to `QueryWriteStatus()` would
  // return (0 if this is the first write to the object).
  //
  // On subsequent calls, this value **must** be no larger than the sum of the
  // first `write_offset` and the sizes of all `data` chunks sent previously on
  // this stream.
  //
  // An incorrect value will cause an error.
  int64 write_offset = 3 [(google.api.field_behavior) = REQUIRED];

  // A portion of the data for the object.
  oneof data {
    // The data to insert. If a crc32c checksum is provided that doesn't match
    // the checksum computed by the service, the request will fail.
    ChecksummedData checksummed_data = 4;

    // A reference to an existing object. This can be used to support
    // several use cases:
    //   - Writing a sequence of data buffers supports the basic use case of
    //     uploading a complete object, chunk by chunk.
    //   - Writing a sequence of references to existing objects allows an
    //     object to be composed from a collection of objects, which can be
    //     used to support parallel object writes.
    //   - Writing a single reference with a given offset and size can be used
    //     to create an object from a slice of an existing object.
    //   - Writing an object referencing a object slice (created as noted
    //     above) followed by a data buffer followed by another object
    //     slice can be used to support delta upload functionality.
    GetObjectMediaRequest reference = 5;
  }

  // Checksums for the complete object. If the checksums computed by the service
  // don't match the specifified checksums the call will fail. May only be
  // provided in the first or last request (either with first_message, or
  // finish_write set).
  ObjectChecksums object_checksums = 6;

  // If `true`, this indicates that the write is complete. Sending any
  // `InsertObjectRequest`s subsequent to one in which `finish_write` is `true`
  // will cause an error.
  // For a non-resumable write (where the upload_id was not set in the first
  // message), it is an error not to set this field in the final message of the
  // stream.
  bool finish_write = 7;

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 8;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 9;
}

// Request message for ListObjects.
message ListObjectsRequest {
  // Required. Name of the bucket in which to look for objects.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Returns results in a directory-like mode. `items` will contain
  // only objects whose names, aside from the `prefix`, do not
  // contain `delimiter`. Objects whose names, aside from the
  // `prefix`, contain `delimiter` will have their name,
  // truncated after the `delimiter`, returned in
  // `prefixes`. Duplicate `prefixes` are omitted.
  string delimiter = 2;

  // If true, objects that end in exactly one instance of `delimiter`
  // will have their metadata included in `items` in addition to
  // `prefixes`.
  bool include_trailing_delimiter = 3;

  // Maximum number of `items` plus `prefixes` to return
  // in a single page of responses. As duplicate `prefixes` are
  // omitted, fewer total results may be returned than requested. The service
  // will use this parameter or 1,000 items, whichever is smaller.
  int32 max_results = 4;

  // A previously-returned page token representing part of the larger set of
  // results to view.
  string page_token = 5;

  // Filter results to objects whose names begin with this prefix.
  string prefix = 6;

  // Set of properties to return. Defaults to `NO_ACL`.
  CommonEnums.Projection projection = 7;

  // If `true`, lists all versions of an object as distinct results.
  // The default is `false`. For more information, see
  // [Object
  // Versioning](https://cloud.google.com/storage/docs/object-versioning).
  bool versions = 9;

  // Filter results to objects whose names are lexicographically equal to or
  // after lexicographic_start. If lexicographic_end is also set, the objects
  // listed have names between lexicographic_start (inclusive) and
  // lexicographic_end (exclusive).
  string lexicographic_start = 11;

  // Filter results to objects whose names are lexicographically before
  // lexicographic_end. If lexicographic_start is also set, the objects listed
  // have names between lexicographic_start (inclusive) and lexicographic_end
  // (exclusive).
  string lexicographic_end = 12;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 10;
}

// Request object for `QueryWriteStatus`.
message QueryWriteStatusRequest {
  // Required. The name of the resume token for the object whose write status is being
  // requested.
  string upload_id = 1 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 2;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 3;
}

// Response object for `QueryWriteStatus`.
message QueryWriteStatusResponse {
  // The number of bytes that have been processed for the given object.
  int64 committed_size = 1;

  // `complete` is `true` only if the client has sent a `InsertObjectRequest`
  // with `finish_write` set to true, and the server has processed that request.
  bool complete = 2;

  // The metadata for the uploaded object. Only set if `complete` is `true`.
  Object resource = 3;
}

// Request message for RewriteObject.
message RewriteObjectRequest {
  // Required. Name of the bucket in which to store the new object. Overrides the provided
  // object metadata's `bucket` value, if any.
  string destination_bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the new object.
  // Required when the object metadata is not otherwise provided. Overrides the
  // object metadata's `name` value, if any.
  string destination_object = 2 [(google.api.field_behavior) = REQUIRED];

  // Resource name of the Cloud KMS key, of the form
  // `projects/my-project/locations/my-location/keyRings/my-kr/cryptoKeys/my-key`,
  // that will be used to encrypt the object. Overrides the object
  // metadata's `kms_key_name` value, if any.
  string destination_kms_key_name = 3;

  // Apply a predefined set of access controls to the destination object.
  CommonEnums.PredefinedObjectAcl destination_predefined_acl = 4;

  // Makes the operation conditional on whether the object's current generation
  // matches the given value. Setting to 0 makes the operation succeed only if
  // there are no live versions of the object.
  google.protobuf.Int64Value if_generation_match = 5;

  // Makes the operation conditional on whether the object's current generation
  // does not match the given value. If no live object exists, the precondition
  // fails. Setting to 0 makes the operation succeed only if there is a live
  // version of the object.
  google.protobuf.Int64Value if_generation_not_match = 6;

  // Makes the operation conditional on whether the destination object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 7;

  // Makes the operation conditional on whether the destination object's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 8;

  // Makes the operation conditional on whether the source object's current
  // generation matches the given value.
  google.protobuf.Int64Value if_source_generation_match = 9;

  // Makes the operation conditional on whether the source object's current
  // generation does not match the given value.
  google.protobuf.Int64Value if_source_generation_not_match = 10;

  // Makes the operation conditional on whether the source object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_source_metageneration_match = 11;

  // Makes the operation conditional on whether the source object's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_source_metageneration_not_match = 12;

  // The maximum number of bytes that will be rewritten per rewrite request.
  // Most callers
  // shouldn't need to specify this parameter - it is primarily in place to
  // support testing. If specified the value must be an integral multiple of
  // 1 MiB (1048576). Also, this only applies to requests where the source and
  // destination span locations and/or storage classes. Finally, this value must
  // not change across rewrite calls else you'll get an error that the
  // `rewriteToken` is invalid.
  int64 max_bytes_rewritten_per_call = 13;

  // Set of properties to return. Defaults to `NO_ACL`, unless the
  // object resource specifies the `acl` property, when it defaults
  // to `full`.
  CommonEnums.Projection projection = 14;

  // Include this field (from the previous rewrite response) on each rewrite
  // request after the first one, until the rewrite response 'done' flag is
  // true. Calls that provide a rewriteToken can omit all other request fields,
  // but if included those fields must match the values provided in the first
  // rewrite request.
  string rewrite_token = 15;

  // Required. Name of the bucket in which to find the source object.
  string source_bucket = 16 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the source object.
  string source_object = 17 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of the source object (as opposed to
  // the latest version, the default).
  int64 source_generation = 18;

  // Properties of the destination, post-rewrite object.
  Object object = 20;

  // The algorithm used to encrypt the source object, if any.
  string copy_source_encryption_algorithm = 21;

  // The encryption key used to encrypt the source object, if any.
  string copy_source_encryption_key = 22;

  // The SHA-256 hash of the key used to encrypt the source object, if any.
  string copy_source_encryption_key_sha256 = 23;

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 24;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 25;
}

// A rewrite response.
message RewriteResponse {
  // The total bytes written so far, which can be used to provide a waiting user
  // with a progress indicator. This property is always present in the response.
  int64 total_bytes_rewritten = 1;

  // The total size of the object being copied in bytes. This property is always
  // present in the response.
  int64 object_size = 2;

  // `true` if the copy is finished; otherwise, `false` if
  // the copy is in progress. This property is always present in the response.
  bool done = 3;

  // A token to use in subsequent requests to continue copying data. This token
  // is present in the response only when there is more data to copy.
  string rewrite_token = 4;

  // A resource containing the metadata for the copied-to object. This property
  // is present in the response only when copying completes.
  Object resource = 5;
}

// Request message StartResumableWrite.
message StartResumableWriteRequest {
  // The destination bucket, object, and metadata, as well as any preconditions.
  InsertObjectSpec insert_object_spec = 1;

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 3;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 4;
}

// Response object for `StartResumableWrite`.
message StartResumableWriteResponse {
  // The upload_id of the newly started resumable write operation. This
  // value should be copied into the `InsertObjectRequest.upload_id` field.
  string upload_id = 1;
}

// Request message for PatchObject.
message PatchObjectRequest {
  // Required. Name of the bucket in which the object resides.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the object.
  string object = 2 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of this object (as opposed to the
  // latest version, the default).
  int64 generation = 3;

  // Makes the operation conditional on whether the object's current generation
  // matches the given value. Setting to 0 makes the operation succeed only if
  // there are no live versions of the object.
  google.protobuf.Int64Value if_generation_match = 4;

  // Makes the operation conditional on whether the object's current generation
  // does not match the given value. If no live object exists, the precondition
  // fails. Setting to 0 makes the operation succeed only if there is a live
  // version of the object.
  google.protobuf.Int64Value if_generation_not_match = 5;

  // Makes the operation conditional on whether the object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 6;

  // Makes the operation conditional on whether the object's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 7;

  // Apply a predefined set of access controls to this object.
  CommonEnums.PredefinedObjectAcl predefined_acl = 8;

  // Set of properties to return. Defaults to `FULL`.
  CommonEnums.Projection projection = 9;

  // The Object metadata for updating.
  Object metadata = 11;

  // List of fields to be updated.
  //
  // To specify ALL fields, equivalent to the JSON API's "update" function,
  // specify a single field with the value `*`. Note: not recommended. If a new
  // field is introduced at a later time, an older client updating with the `*`
  // may accidentally reset the new field's value.
  //
  // Not specifying any fields is an error.
  // Not specifying a field while setting that field to a non-default value is
  // an error.
  google.protobuf.FieldMask update_mask = 12;

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 13;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 14;
}

// Request message for UpdateObject.
message UpdateObjectRequest {
  // Required. Name of the bucket in which the object resides.
  string bucket = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Name of the object.
  string object = 2 [(google.api.field_behavior) = REQUIRED];

  // If present, selects a specific revision of this object (as opposed to the
  // latest version, the default).
  int64 generation = 3;

  // Makes the operation conditional on whether the object's current generation
  // matches the given value. Setting to 0 makes the operation succeed only if
  // there are no live versions of the object.
  google.protobuf.Int64Value if_generation_match = 4;

  // Makes the operation conditional on whether the object's current generation
  // does not match the given value. If no live object exists, the precondition
  // fails. Setting to 0 makes the operation succeed only if there is a live
  // version of the object.
  google.protobuf.Int64Value if_generation_not_match = 5;

  // Makes the operation conditional on whether the object's current
  // metageneration matches the given value.
  google.protobuf.Int64Value if_metageneration_match = 6;

  // Makes the operation conditional on whether the object's current
  // metageneration does not match the given value.
  google.protobuf.Int64Value if_metageneration_not_match = 7;

  // Apply a predefined set of access controls to this object.
  CommonEnums.PredefinedObjectAcl predefined_acl = 8;

  // Set of properties to return. Defaults to `FULL`.
  CommonEnums.Projection projection = 9;

  // The Object metadata for updating.
  Object metadata = 11;

  // A set of parameters common to Storage API requests concerning an object.
  CommonObjectRequestParams common_object_request_params = 12;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 13;
}

// Request message for WatchAllObjects.
message WatchAllObjectsRequest {
  // Name of the bucket in which to look for objects.
  string bucket = 1;

  // If `true`, lists all versions of an object as distinct results.
  // The default is `false`. For more information, see
  // [Object
  // Versioning](https://cloud.google.com/storage/docs/object-versioning).
  bool versions = 2;

  // Returns results in a directory-like mode. `items` will contain
  // only objects whose names, aside from the `prefix`, do not
  // contain `delimiter`. Objects whose names, aside from the
  // `prefix`, contain `delimiter` will have their name,
  // truncated after the `delimiter`, returned in
  // `prefixes`. Duplicate `prefixes` are omitted.
  string delimiter = 3;

  // Maximum number of `items` plus `prefixes` to return
  // in a single page of responses. As duplicate `prefixes` are
  // omitted, fewer total results may be returned than requested. The service
  // will use this parameter or 1,000 items, whichever is smaller.
  int32 max_results = 4;

  // Filter results to objects whose names begin with this prefix.
  string prefix = 5;

  // If true, objects that end in exactly one instance of `delimiter`
  // will have their metadata included in `items` in addition to
  // `prefixes`.
  bool include_trailing_delimiter = 6;

  // A previously-returned page token representing part of the larger set of
  // results to view.
  string page_token = 7;

  // Set of properties to return. Defaults to `NO_ACL`.
  CommonEnums.Projection projection = 8;

  // Properties of the channel to be inserted.
  Channel channel = 10;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 11;
}

// Request message for GetProjectServiceAccount.
message GetProjectServiceAccountRequest {
  // Required. Project ID.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 3;
}

message CreateHmacKeyRequest {
  // Required. The project that the HMAC-owning service account lives in.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The service account to create the HMAC for.
  string service_account_email = 2 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 3;
}

// Create hmac response.  The only time the secret for an HMAC will be returned.
message CreateHmacKeyResponse {
  // Key metadata.
  HmacKeyMetadata metadata = 1;

  // HMAC key secret material.
  string secret = 2;
}

// Request object to delete a given HMAC key.
message DeleteHmacKeyRequest {
  // Required. The identifying key for the HMAC to delete.
  string access_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The project id the HMAC key lies in.
  string project_id = 2 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 3;
}

// Request object to get metadata on a given HMAC key.
message GetHmacKeyRequest {
  // Required. The identifying key for the HMAC to delete.
  string access_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The project id the HMAC key lies in.
  string project_id = 2 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 3;
}

// Request to fetch a list of HMAC keys under a given project.
message ListHmacKeysRequest {
  // Required. The project id to list HMAC keys for.
  string project_id = 1 [(google.api.field_behavior) = REQUIRED];

  // An optional filter to only return HMAC keys for one service account.
  string service_account_email = 2;

  // An optional bool to return deleted keys that have not been wiped out yet.
  bool show_deleted_keys = 3;

  // The maximum number of keys to return.
  int32 max_results = 4;

  // A previously returned token from ListHmacKeysResponse to get the next page.
  string page_token = 5;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 6;
}

// Hmac key list response with next page information.
message ListHmacKeysResponse {
  // The continuation token, used to page through large result sets. Provide
  // this value in a subsequent request to return the next page of results.
  string next_page_token = 1;

  // The list of items.
  repeated HmacKeyMetadata items = 2;
}

// Request object to update an HMAC key state.
message UpdateHmacKeyRequest {
  // Required. The id of the HMAC key.
  string access_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The project id the HMAC's service account lies in.
  string project_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The service account owner of the HMAC key.
  HmacKeyMetadata metadata = 3 [(google.api.field_behavior) = REQUIRED];

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 5;
}

// A wrapper around the IAM get policy request to support our
// common_request_params.
message GetIamPolicyRequest {
  // The request sent to IAM.
  google.iam.v1.GetIamPolicyRequest iam_request = 1;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 2;
}

// A wrapper around the IAM set policy request to support our
// common_request_params.
message SetIamPolicyRequest {
  // The request sent to IAM.
  google.iam.v1.SetIamPolicyRequest iam_request = 1;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 2;
}

// A wrapper around the IAM test iam permissions request to support our
// common_request_params.
message TestIamPermissionsRequest {
  // The request sent to IAM.
  google.iam.v1.TestIamPermissionsRequest iam_request = 1;

  // A set of parameters common to all Storage API requests.
  CommonRequestParams common_request_params = 2;
}

// Parameters that can be passed to any object request.
message CommonObjectRequestParams {
  // Encryption algorithm used with Customer-Supplied Encryption Keys feature.
  string encryption_algorithm = 1;

  // Encryption key used with Customer-Supplied Encryption Keys feature.
  string encryption_key = 2;

  // SHA256 hash of encryption key used with Customer-Supplied Encryption Keys
  // feature.
  string encryption_key_sha256 = 3;
}

// Parameters that can be passed to any request.
message CommonRequestParams {
  // Required. Required when using buckets with Requestor Pays feature enabled.
  string user_project = 1 [(google.api.field_behavior) = REQUIRED];

  // Lets you enforce per-user quotas from a server-side application even in
  // cases when the user's IP address is unknown. This can occur, for example,
  // with applications that run cron jobs on App Engine on a user's behalf.
  // You can choose any arbitrary string that uniquely identifies a user, but it
  // is limited to 40 characters.
  string quota_user = 2;

  // Subset of fields to include in the response.
  google.protobuf.FieldMask fields = 4;
}
