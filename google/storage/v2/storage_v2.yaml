type: google.api.Service
config_version: 3
name: storage.googleapis.com
title: Cloud Storage API

apis:
- name: google.longrunning.Operations
- name: google.storage.v2.Storage

types:
- name: google.storage.v2.Bucket

enums:
- name: google.storage.v2.ServiceConstants.Values

documentation:
  summary: |-
    This folder contains protocol buffer definitions for an API only accessible
    to select customers. Customers not participating should
    not depend on this file. Please contact Google Cloud sales if you are
    interested. Unless told otherwise by a Google Cloud
    representative, do not use or otherwise rely on any of the contents of
    this folder. If you would like to use Cloud Storage, please consult our
    [official
    documentation](https://cloud.google.com/storage/docs/apis) for details on
    our XML and JSON APIs, or else consider one of our
    [client
    libraries](https://cloud.google.com/storage/docs/reference/libraries).
  overview: |-
    The Google Cloud Storage API allows applications to read and write data
    through the abstractions of buckets and objects, which are similar to
    directories and files except that buckets cannot contain other buckets,
    and directory-level operations (like directory rename) are not supported.
    Buckets share a single global namespace, and each bucket belongs to a
    specific project that has an associated owner that pays for the data
    stored in the bucket. This API is accessed using standard gRPC requests.

authentication:
  rules:
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/devstorage.full_control,
        https://www.googleapis.com/auth/devstorage.read_only,
        https://www.googleapis.com/auth/devstorage.read_write
  - selector: 'google.storage.v2.Storage.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/devstorage.full_control,
        https://www.googleapis.com/auth/devstorage.read_only,
        https://www.googleapis.com/auth/devstorage.read_write

publishing:
  documentation_uri: https://cloud.google.com/storage/docs/apis
