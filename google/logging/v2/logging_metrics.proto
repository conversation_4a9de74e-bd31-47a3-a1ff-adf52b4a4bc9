// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.logging.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/distribution.proto";
import "google/api/field_behavior.proto";
import "google/api/metric.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.Logging.V2";
option go_package = "cloud.google.com/go/logging/apiv2/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggingMetricsProto";
option java_package = "com.google.logging.v2";
option php_namespace = "Google\\Cloud\\Logging\\V2";
option ruby_package = "Google::Cloud::Logging::V2";

// Service for configuring logs-based metrics.
service MetricsServiceV2 {
  option (google.api.default_host) = "logging.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only,"
      "https://www.googleapis.com/auth/logging.admin,"
      "https://www.googleapis.com/auth/logging.read,"
      "https://www.googleapis.com/auth/logging.write";

  // Lists logs-based metrics.
  rpc ListLogMetrics(ListLogMetricsRequest) returns (ListLogMetricsResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*}/metrics"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a logs-based metric.
  rpc GetLogMetric(GetLogMetricRequest) returns (LogMetric) {
    option (google.api.http) = {
      get: "/v2/{metric_name=projects/*/metrics/*}"
    };
    option (google.api.method_signature) = "metric_name";
  }

  // Creates a logs-based metric.
  rpc CreateLogMetric(CreateLogMetricRequest) returns (LogMetric) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*}/metrics"
      body: "metric"
    };
    option (google.api.method_signature) = "parent,metric";
  }

  // Creates or updates a logs-based metric.
  rpc UpdateLogMetric(UpdateLogMetricRequest) returns (LogMetric) {
    option (google.api.http) = {
      put: "/v2/{metric_name=projects/*/metrics/*}"
      body: "metric"
    };
    option (google.api.method_signature) = "metric_name,metric";
  }

  // Deletes a logs-based metric.
  rpc DeleteLogMetric(DeleteLogMetricRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{metric_name=projects/*/metrics/*}"
    };
    option (google.api.method_signature) = "metric_name";
  }
}

// Describes a logs-based metric. The value of the metric is the number of log
// entries that match a logs filter in a given time interval.
//
// Logs-based metrics can also be used to extract values from logs and create a
// distribution of the values. The distribution records the statistics of the
// extracted values along with an optional histogram of the values as specified
// by the bucket options.
message LogMetric {
  option (google.api.resource) = {
    type: "logging.googleapis.com/LogMetric"
    pattern: "projects/{project}/metrics/{metric}"
  };

  // Logging API version.
  enum ApiVersion {
    // Logging API v2.
    V2 = 0;

    // Logging API v1.
    V1 = 1;
  }

  // Required. The client-assigned metric identifier.
  // Examples: `"error_count"`, `"nginx/requests"`.
  //
  // Metric identifiers are limited to 100 characters and can include only the
  // following characters: `A-Z`, `a-z`, `0-9`, and the special characters
  // `_-.,+!*',()%/`. The forward-slash character (`/`) denotes a hierarchy of
  // name pieces, and it cannot be the first character of the name.
  //
  // This field is the `[METRIC_ID]` part of a metric resource name in the
  // format "projects/[PROJECT_ID]/metrics/[METRIC_ID]". Example: If the
  // resource name of a metric is
  // `"projects/my-project/metrics/nginx%2Frequests"`, this field's value is
  // `"nginx/requests"`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. A description of this metric, which is used in documentation.
  // The maximum length of the description is 8000 characters.
  string description = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. An [advanced logs
  // filter](https://cloud.google.com/logging/docs/view/advanced_filters) which
  // is used to match log entries. Example:
  //
  //     "resource.type=gae_app AND severity>=ERROR"
  //
  // The maximum length of the filter is 20000 characters.
  string filter = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The resource name of the Log Bucket that owns the Log Metric.
  // Only Log Buckets in projects are supported. The bucket has to be in the
  // same project as the metric.
  //
  // For example:
  //
  //   `projects/my-project/locations/global/buckets/my-bucket`
  //
  // If empty, then the Log Metric is considered a non-Bucket Log Metric.
  string bucket_name = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to True, then this metric is disabled and it does not
  // generate any points.
  bool disabled = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The metric descriptor associated with the logs-based metric.
  // If unspecified, it uses a default metric descriptor with a DELTA metric
  // kind, INT64 value type, with no labels and a unit of "1". Such a metric
  // counts the number of log entries matching the `filter` expression.
  //
  // The `name`, `type`, and `description` fields in the `metric_descriptor`
  // are output only, and is constructed using the `name` and `description`
  // field in the LogMetric.
  //
  // To create a logs-based metric that records a distribution of log values, a
  // DELTA metric kind with a DISTRIBUTION value type must be used along with
  // a `value_extractor` expression in the LogMetric.
  //
  // Each label in the metric descriptor must have a matching label
  // name as the key and an extractor expression as the value in the
  // `label_extractors` map.
  //
  // The `metric_kind` and `value_type` fields in the `metric_descriptor` cannot
  // be updated once initially configured. New labels can be added in the
  // `metric_descriptor`, but existing labels cannot be modified except for
  // their description.
  google.api.MetricDescriptor metric_descriptor = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A `value_extractor` is required when using a distribution
  // logs-based metric to extract the values to record from a log entry.
  // Two functions are supported for value extraction: `EXTRACT(field)` or
  // `REGEXP_EXTRACT(field, regex)`. The arguments are:
  //
  //   1. field: The name of the log entry field from which the value is to be
  //      extracted.
  //   2. regex: A regular expression using the Google RE2 syntax
  //      (https://github.com/google/re2/wiki/Syntax) with a single capture
  //      group to extract data from the specified log entry field. The value
  //      of the field is converted to a string before applying the regex.
  //      It is an error to specify a regex that does not include exactly one
  //      capture group.
  //
  // The result of the extraction must be convertible to a double type, as the
  // distribution always records double values. If either the extraction or
  // the conversion to double fails, then those values are not recorded in the
  // distribution.
  //
  // Example: `REGEXP_EXTRACT(jsonPayload.request, ".*quantity=(\d+).*")`
  string value_extractor = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A map from a label key string to an extractor expression which is
  // used to extract data from a log entry field and assign as the label value.
  // Each label key specified in the LabelDescriptor must have an associated
  // extractor expression in this map. The syntax of the extractor expression
  // is the same as for the `value_extractor` field.
  //
  // The extracted value is converted to the type defined in the label
  // descriptor. If either the extraction or the type conversion fails,
  // the label will have a default value. The default value for a string
  // label is an empty string, for an integer label its 0, and for a boolean
  // label its `false`.
  //
  // Note that there are upper bounds on the maximum number of labels and the
  // number of active time series that are allowed in a project.
  map<string, string> label_extractors = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The `bucket_options` are required when the logs-based metric is
  // using a DISTRIBUTION value type and it describes the bucket boundaries
  // used to create a histogram of the extracted values.
  google.api.Distribution.BucketOptions bucket_options = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The creation timestamp of the metric.
  //
  // This field may not be present for older metrics.
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last update timestamp of the metric.
  //
  // This field may not be present for older metrics.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Deprecated. The API version that created or updated this metric.
  // The v2 format is used by default and cannot be changed.
  ApiVersion version = 4 [deprecated = true];
}

// The parameters to ListLogMetrics.
message ListLogMetricsRequest {
  // Required. The name of the project containing the metrics:
  //
  //     "projects/[PROJECT_ID]"
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Optional. If present, then retrieve the next batch of results from the
  // preceding call to this method. `pageToken` must be the value of
  // `nextPageToken` from the previous response. The values of other method
  // parameters should be identical to those in the previous call.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of results to return from this request.
  // Non-positive values are ignored. The presence of `nextPageToken` in the
  // response indicates that more results might be available.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Result returned from ListLogMetrics.
message ListLogMetricsResponse {
  // A list of logs-based metrics.
  repeated LogMetric metrics = 1;

  // If there might be more results than appear in this response, then
  // `nextPageToken` is included. To get the next set of results, call this
  // method again using the value of `nextPageToken` as `pageToken`.
  string next_page_token = 2;
}

// The parameters to GetLogMetric.
message GetLogMetricRequest {
  // Required. The resource name of the desired metric:
  //
  //     "projects/[PROJECT_ID]/metrics/[METRIC_ID]"
  string metric_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/LogMetric"
    }
  ];
}

// The parameters to CreateLogMetric.
message CreateLogMetricRequest {
  // Required. The resource name of the project in which to create the metric:
  //
  //     "projects/[PROJECT_ID]"
  //
  // The new metric must be provided in the request.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "logging.googleapis.com/LogMetric"
    }
  ];

  // Required. The new logs-based metric, which must not have an identifier that
  // already exists.
  LogMetric metric = 2 [(google.api.field_behavior) = REQUIRED];
}

// The parameters to UpdateLogMetric.
message UpdateLogMetricRequest {
  // Required. The resource name of the metric to update:
  //
  //     "projects/[PROJECT_ID]/metrics/[METRIC_ID]"
  //
  // The updated metric must be provided in the request and it's
  // `name` field must be the same as `[METRIC_ID]` If the metric
  // does not exist in `[PROJECT_ID]`, then a new metric is created.
  string metric_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/LogMetric"
    }
  ];

  // Required. The updated metric.
  LogMetric metric = 2 [(google.api.field_behavior) = REQUIRED];
}

// The parameters to DeleteLogMetric.
message DeleteLogMetricRequest {
  // Required. The resource name of the metric to delete:
  //
  //     "projects/[PROJECT_ID]/metrics/[METRIC_ID]"
  string metric_name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "logging.googleapis.com/LogMetric"
    }
  ];
}
