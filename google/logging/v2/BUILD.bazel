# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "logging_proto",
    srcs = [
        "log_entry.proto",
        "logging.proto",
        "logging_config.proto",
        "logging_metrics.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:distribution_proto",
        "//google/api:field_behavior_proto",
        "//google/api:metric_proto",
        "//google/api:monitored_resource_proto",
        "//google/api:resource_proto",
        "//google/logging/type:type_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "logging_proto_with_info",
    deps = [
        ":logging_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "logging_java_proto",
    deps = [":logging_proto"],
)

java_grpc_library(
    name = "logging_java_grpc",
    srcs = [":logging_proto"],
    deps = [":logging_java_proto"],
)

java_gapic_library(
    name = "logging_java_gapic",
    srcs = [":logging_proto_with_info"],
    gapic_yaml = "logging_gapic.yaml",
    grpc_service_config = "logging_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "logging_v2.yaml",
    test_deps = [
        ":logging_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":logging_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "logging_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.logging.v2.ConfigClientTest",
        "com.google.cloud.logging.v2.LoggingClientTest",
        "com.google.cloud.logging.v2.MetricsClientTest",
    ],
    runtime_deps = [":logging_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-logging-v2-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":logging_java_gapic",
        ":logging_java_grpc",
        ":logging_java_proto",
        ":logging_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "logging_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/logging/apiv2/loggingpb",
    protos = [":logging_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:distribution_go_proto",
        "//google/api:metric_go_proto",
        "//google/api:monitoredres_go_proto",
        "//google/logging/type:type_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "logging_go_gapic",
    srcs = [":logging_proto_with_info"],
    grpc_service_config = "logging_grpc_service_config.json",
    importpath = "cloud.google.com/go/logging/apiv2;logging",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "logging_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":logging_go_proto",
        "//google/api:metric_go_proto",
        "//google/api:monitoredres_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-logging-v2-go",
    deps = [
        ":logging_go_gapic",
        ":logging_go_gapic_srcjar-metadata.srcjar",
        ":logging_go_gapic_srcjar-snippets.srcjar",
        ":logging_go_gapic_srcjar-test.srcjar",
        ":logging_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "logging_py_gapic",
    srcs = [":logging_proto"],
    grpc_service_config = "logging_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=logging",
        "python-gapic-namespace=google.cloud",
    ],
    rest_numeric_enums = True,
    service_yaml = "logging_v2.yaml",
    transport = "grpc",
    deps = [
    ],
)

py_test(
    name = "logging_py_gapic_test",
    srcs = [
        "logging_py_gapic_pytest.py",
        "logging_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":logging_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "logging-v2-py",
    deps = [
        ":logging_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "logging_php_proto",
    deps = [":logging_proto"],
)

php_gapic_library(
    name = "logging_php_gapic",
    srcs = [":logging_proto_with_info"],
    gapic_yaml = "logging_gapic.yaml",
    grpc_service_config = "logging_grpc_service_config.json",
    migration_mode = "MIGRATING",
    rest_numeric_enums = True,
    service_yaml = "logging_v2.yaml",
    transport = "grpc+rest",
    deps = [":logging_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-logging-v2-php",
    deps = [
        ":logging_php_gapic",
        ":logging_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "logging_nodejs_gapic",
    package_name = "@google-cloud/logging",
    src = ":logging_proto_with_info",
    bundle_config = "logging_gapic.yaml",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "logging_grpc_service_config.json",
    main_service = "logging",
    package = "google.logging.v2",
    rest_numeric_enums = True,
    service_yaml = "logging_v2.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "logging-v2-nodejs",
    deps = [
        ":logging_nodejs_gapic",
        ":logging_proto",
        "//google/logging/type:type_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "logging_ruby_proto",
    deps = [":logging_proto"],
)

ruby_grpc_library(
    name = "logging_ruby_grpc",
    srcs = [":logging_proto"],
    deps = [":logging_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "logging_ruby_gapic",
    srcs = [":logging_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=logging.googleapis.com",
        "ruby-cloud-api-shortname=logging",
        "ruby-cloud-env-prefix=LOGGING",
        "ruby-cloud-gem-name=google-cloud-logging-v2",
        "ruby-cloud-product-url=https://cloud.google.com/logging",
        "ruby-cloud-service-override=ConfigServiceV2=ConfigService;LoggingServiceV2=LoggingService;MetricsServiceV2=MetricsService",
        "ruby-cloud-yard-strict=false",
    ],
    grpc_service_config = "logging_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Cloud Logging API lets you programmatically read and write log entries, set up exclusions, create logs-based metrics, and manage export sinks.",
    ruby_cloud_title = "Cloud Logging V2",
    service_yaml = "logging_v2.yaml",
    transport = "grpc",
    deps = [
        ":logging_ruby_grpc",
        ":logging_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-logging-v2-ruby",
    deps = [
        ":logging_ruby_gapic",
        ":logging_ruby_grpc",
        ":logging_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "logging_csharp_proto",
    deps = [":logging_proto"],
)

csharp_grpc_library(
    name = "logging_csharp_grpc",
    srcs = [":logging_proto"],
    deps = [":logging_csharp_proto"],
)

csharp_gapic_library(
    name = "logging_csharp_gapic",
    srcs = [":logging_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "logging_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "logging_v2.yaml",
    transport = "grpc",
    deps = [
        ":logging_csharp_grpc",
        ":logging_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-logging-v2-csharp",
    deps = [
        ":logging_csharp_gapic",
        ":logging_csharp_grpc",
        ":logging_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "logging_cc_proto",
    deps = [":logging_proto"],
)

cc_grpc_library(
    name = "logging_cc_grpc",
    srcs = [":logging_proto"],
    grpc_only = True,
    deps = [":logging_cc_proto"],
)
