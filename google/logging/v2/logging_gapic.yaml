type: com.google.api.codegen.ConfigProto
config_schema_version: 2.0.0
language_settings:
  java:
    package_name: com.google.cloud.logging.v2
    interface_names:
      google.logging.v2.ConfigServiceV2: Config
      google.logging.v2.LoggingServiceV2: Logging
      google.logging.v2.MetricsServiceV2: Metrics
interfaces:
- name: google.logging.v2.LoggingServiceV2
  methods:
  - name: WriteLogEntries
    retry_codes_name: idempotent
    batching:
      thresholds:
        element_count_threshold: 1000
        request_byte_threshold: 1048576
        delay_threshold_millis: 50
        flow_control_element_limit: 100000
        flow_control_byte_limit: 10485760
        flow_control_limit_exceeded_behavior: THROW_EXCEPTION
      batch_descriptor:
        batched_field: entries
        discriminator_fields:
        - log_name
        - resource
        - labels
