# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "type_proto",
    srcs = [
        "http_request.proto",
        "log_severity.proto",
    ],
    deps = [
        "@com_google_protobuf//:duration_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "type_java_proto",
    deps = [":type_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-logging-type-java",
    transport = "grpc+rest",
    deps = [
        ":type_java_proto",
        ":type_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_proto_library",
)

go_proto_library(
    name = "type_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/logging/type",
    protos = [":type_proto"],
    deps = [
    ],
)

go_gapic_assembly_pkg(
    name = "logging-type-go",
    deps = [
        ":type_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_proto_library",
    "py_gapic_assembly_pkg",
)

py_proto_library(
    name = "type_py_proto",
    deps = [":type_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "logging-type-py",
    deps = [
        ":type_py_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "type_php_proto",
    deps = [":type_proto"],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate php files for these protos.
# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-logging-type-php",
    deps = [
        ":type_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "type_ruby_proto",
    deps = [":type_proto"],
)

ruby_grpc_library(
    name = "type_ruby_grpc",
    srcs = [":type_proto"],
    deps = [":type_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "type_csharp_proto",
    deps = [":type_proto"],
)

csharp_grpc_library(
    name = "type_csharp_grpc",
    srcs = [":type_proto"],
    deps = [":type_csharp_proto"],
)

csharp_gapic_assembly_pkg(
    name = "google-logging-type-csharp",
    package_name = "Google.Cloud.Logging.Type",
    generate_nongapic_package = True,
    deps = [
        ":type_csharp_grpc",
        ":type_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "type_cc_proto",
    deps = [":type_proto"],
)

cc_grpc_library(
    name = "type_cc_grpc",
    srcs = [":type_proto"],
    grpc_only = True,
    deps = [":type_cc_proto"],
)
