{"methodConfig": [{"name": [{"service": "google.api.cloudquotas.v1.CloudQuotas", "method": "ListQuotaInfos"}, {"service": "google.api.cloudquotas.v1.CloudQuotas", "method": "GetQuotaInfo"}, {"service": "google.api.cloudquotas.v1.CloudQuotas", "method": "ListQuotaPreferences"}, {"service": "google.api.cloudquotas.v1.CloudQuotas", "method": "GetQuotaPreference"}, {"service": "google.api.cloudquotas.v1.CloudQuotas", "method": "CreateQuotaPreference"}, {"service": "google.api.cloudquotas.v1.CloudQuotas", "method": "UpdateQuotaPreference"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}