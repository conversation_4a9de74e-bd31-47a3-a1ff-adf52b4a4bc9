# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "cloudquotas_proto",
    srcs = [
        "cloudquotas.proto",
        "quota_adjuster_settings.proto",
        "resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "cloudquotas_proto_with_info",
    deps = [
        ":cloudquotas_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "cloudquotas_java_proto",
    deps = [":cloudquotas_proto"],
)

java_grpc_library(
    name = "cloudquotas_java_grpc",
    srcs = [":cloudquotas_proto"],
    deps = [":cloudquotas_java_proto"],
)

java_gapic_library(
    name = "cloudquotas_java_gapic",
    srcs = [":cloudquotas_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "cloudquotas_v1beta_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1beta.yaml",
    test_deps = [
        ":cloudquotas_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "cloudquotas_java_gapic_test_suite",
    test_classes = [
        "com.google.api.cloudquotas.v1beta.CloudQuotasClientHttpJsonTest",
        "com.google.api.cloudquotas.v1beta.CloudQuotasClientTest",
        "com.google.api.cloudquotas.v1beta.QuotaAdjusterSettingsManagerClientHttpJsonTest",
        "com.google.api.cloudquotas.v1beta.QuotaAdjusterSettingsManagerClientTest",
    ],
    runtime_deps = [":cloudquotas_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-api-cloudquotas-v1beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_java_gapic",
        ":cloudquotas_java_grpc",
        ":cloudquotas_java_proto",
        ":cloudquotas_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "cloudquotas_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/cloudquotas/apiv1beta/cloudquotaspb",
    protos = [":cloudquotas_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "cloudquotas_go_gapic",
    srcs = [":cloudquotas_proto_with_info"],
    grpc_service_config = "cloudquotas_v1beta_grpc_service_config.json",
    importpath = "cloud.google.com/go/cloudquotas/apiv1beta;cloudquotas",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-api-cloudquotas-v1beta-go",
    deps = [
        ":cloudquotas_go_gapic",
        ":cloudquotas_go_gapic_srcjar-metadata.srcjar",
        ":cloudquotas_go_gapic_srcjar-snippets.srcjar",
        ":cloudquotas_go_gapic_srcjar-test.srcjar",
        ":cloudquotas_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "cloudquotas_py_gapic",
    srcs = [":cloudquotas_proto"],
    grpc_service_config = "cloudquotas_v1beta_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
    ],
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-quotas",
    ],
)

py_test(
    name = "cloudquotas_py_gapic_test",
    srcs = [
        "cloudquotas_py_gapic_pytest.py",
        "cloudquotas_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":cloudquotas_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "api-cloudquotas-v1beta-py",
    deps = [
        ":cloudquotas_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "cloudquotas_php_proto",
    deps = [":cloudquotas_proto"],
)

php_gapic_library(
    name = "cloudquotas_php_gapic",
    srcs = [":cloudquotas_proto_with_info"],
    grpc_service_config = "cloudquotas_v1beta_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-api-cloudquotas-v1beta-php",
    deps = [
        ":cloudquotas_php_gapic",
        ":cloudquotas_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "cloudquotas_nodejs_gapic",
    package_name = "@google-cloud/cloudquotas",
    src = ":cloudquotas_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudquotas_v1beta_grpc_service_config.json",
    package = "google.api.cloudquotas.v1beta",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "api-cloudquotas-v1beta-nodejs",
    deps = [
        ":cloudquotas_nodejs_gapic",
        ":cloudquotas_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "cloudquotas_ruby_proto",
    deps = [":cloudquotas_proto"],
)

ruby_grpc_library(
    name = "cloudquotas_ruby_grpc",
    srcs = [":cloudquotas_proto"],
    deps = [":cloudquotas_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "cloudquotas_ruby_gapic",
    srcs = [":cloudquotas_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-cloud_quotas-v1beta"],
    grpc_service_config = "cloudquotas_v1beta_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_ruby_grpc",
        ":cloudquotas_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-api-cloudquotas-v1beta-ruby",
    deps = [
        ":cloudquotas_ruby_gapic",
        ":cloudquotas_ruby_grpc",
        ":cloudquotas_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "cloudquotas_csharp_proto",
    deps = [":cloudquotas_proto"],
)

csharp_grpc_library(
    name = "cloudquotas_csharp_grpc",
    srcs = [":cloudquotas_proto"],
    deps = [":cloudquotas_csharp_proto"],
)

csharp_gapic_library(
    name = "cloudquotas_csharp_gapic",
    srcs = [":cloudquotas_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudquotas_v1beta_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_csharp_grpc",
        ":cloudquotas_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-api-cloudquotas-v1beta-csharp",
    deps = [
        ":cloudquotas_csharp_gapic",
        ":cloudquotas_csharp_grpc",
        ":cloudquotas_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "cloudquotas_cc_proto",
    deps = [":cloudquotas_proto"],
)

cc_grpc_library(
    name = "cloudquotas_cc_grpc",
    srcs = [":cloudquotas_proto"],
    grpc_only = True,
    deps = [":cloudquotas_cc_proto"],
)
