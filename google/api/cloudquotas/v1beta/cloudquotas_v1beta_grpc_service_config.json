{"methodConfig": [{"name": [{"service": "google.api.cloudquotas.v1beta.CloudQuotas", "method": "ListQuotaInfos"}, {"service": "google.api.cloudquotas.v1beta.CloudQuotas", "method": "GetQuotaInfo"}, {"service": "google.api.cloudquotas.v1beta.CloudQuotas", "method": "ListQuotaPreferences"}, {"service": "google.api.cloudquotas.v1beta.CloudQuotas", "method": "GetQuotaPreference"}, {"service": "google.api.cloudquotas.v1beta.CloudQuotas", "method": "CreateQuotaPreference"}, {"service": "google.api.cloudquotas.v1beta.CloudQuotas", "method": "UpdateQuotaPreference"}, {"service": "google.api.cloudquotas.v1beta.QuotaAdjusterSettingsManager", "method": "GetQuotaAdjusterSettings"}, {"service": "google.api.cloudquotas.v1beta.QuotaAdjusterSettingsManager", "method": "UpdateQuotaAdjusterSettings"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}