# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "expr_proto",
    srcs = [
        "checked.proto",
        "eval.proto",
        "explain.proto",
        "syntax.proto",
        "value.proto",
    ],
    deps = [
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "checked_proto",
    srcs = ["checked.proto"],
    deps = [
        ":syntax_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library(
    name = "eval_proto",
    srcs = ["eval.proto"],
    deps = [
        ":value_proto",
        "//google/rpc:status_proto",
    ],
)

proto_library(
    name = "explain_proto",
    srcs = ["explain.proto"],
    deps = [
        ":value_proto",
    ],
)

proto_library(
    name = "syntax_proto",
    srcs = ["syntax.proto"],
    deps = [
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "value_proto",
    srcs = ["value.proto"],
    deps = [
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "expr_java_proto",
    deps = [":expr_proto"],
)

java_grpc_library(
    name = "expr_java_grpc",
    srcs = [":expr_proto"],
    deps = [":expr_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "expr_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/api/expr/v1alpha1",
    protos = [":expr_proto"],
    deps = [
        "//google/rpc:status_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "expr_moved_proto",
    srcs = [":expr_proto"],
    deps = [
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "expr_py_proto",
    deps = [":expr_moved_proto"],
)

py_grpc_library(
    name = "expr_py_grpc",
    srcs = [":expr_moved_proto"],
    deps = [":expr_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "expr_php_proto",
    deps = [":expr_proto"],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "expr_ruby_proto",
    deps = [":expr_proto"],
)

ruby_grpc_library(
    name = "expr_ruby_grpc",
    srcs = [":expr_proto"],
    deps = [":expr_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "expr_csharp_proto",
    deps = [":expr_proto"],
)

csharp_grpc_library(
    name = "expr_csharp_grpc",
    srcs = [":expr_proto"],
    deps = [":expr_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_proto_library",
)

cc_proto_library(
    name = "checked_cc_proto",
    deps = [":checked_proto"],
)

cc_proto_library(
    name = "eval_cc_proto",
    deps = [":eval_proto"],
)

cc_proto_library(
    name = "explain_cc_proto",
    deps = [":explain_proto"],
)

cc_proto_library(
    name = "syntax_cc_proto",
    deps = [":syntax_proto"],
)

cc_proto_library(
    name = "value_cc_proto",
    deps = [":value_proto"],
)
