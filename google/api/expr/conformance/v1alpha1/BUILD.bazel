# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "conformance_proto",
    srcs = [
        "conformance_service.proto",
    ],
    deps = [
        "//google/api:client_proto",
        "//google/api/expr/v1alpha1:checked_proto",
        "//google/api/expr/v1alpha1:eval_proto",
        "//google/api/expr/v1alpha1:syntax_proto",
        "//google/rpc:status_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "conformance_java_proto",
    deps = [":conformance_proto"],
)

java_grpc_library(
    name = "conformance_java_grpc",
    srcs = [":conformance_proto"],
    deps = [":conformance_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "conformance_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/api/expr/conformance/v1alpha1",
    protos = [":conformance_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api/expr/v1alpha1:expr_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "conformance_moved_proto",
    srcs = [":conformance_proto"],
    deps = [
        "//google/api:client_proto",
        "//google/api/expr/v1alpha1:checked_proto",
        "//google/api/expr/v1alpha1:eval_proto",
        "//google/api/expr/v1alpha1:syntax_proto",
        "//google/rpc:status_proto",
    ],
)

py_proto_library(
    name = "conformance_py_proto",
    deps = [":conformance_moved_proto"],
)

py_grpc_library(
    name = "conformance_py_grpc",
    srcs = [":conformance_moved_proto"],
    deps = [":conformance_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "conformance_php_proto",
    deps = [":conformance_proto"],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "conformance_ruby_proto",
    deps = [":conformance_proto"],
)

ruby_grpc_library(
    name = "conformance_ruby_grpc",
    srcs = [":conformance_proto"],
    deps = [":conformance_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "conformance_csharp_proto",
    deps = [":conformance_proto"],
)

csharp_grpc_library(
    name = "conformance_csharp_grpc",
    srcs = [":conformance_proto"],
    deps = [":conformance_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "conformance_cc_proto",
    deps = [":conformance_proto"],
)

cc_grpc_library(
    name = "conformance_cc_grpc",
    srcs = [":conformance_proto"],
    generate_mocks = True,
    grpc_only = True,
    deps = [":conformance_cc_proto"],
)
