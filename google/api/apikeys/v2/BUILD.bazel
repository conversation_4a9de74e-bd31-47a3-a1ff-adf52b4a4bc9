# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "apikeys_proto",
    srcs = [
        "apikeys.proto",
        "resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "apikeys_proto_with_info",
    deps = [
        ":apikeys_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "apikeys_java_proto",
    deps = [":apikeys_proto"],
)

java_grpc_library(
    name = "apikeys_java_grpc",
    srcs = [":apikeys_proto"],
    deps = [":apikeys_java_proto"],
)

java_gapic_library(
    name = "apikeys_java_gapic",
    srcs = [":apikeys_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "apikeys_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apikeys_v2.yaml",
    test_deps = [
        ":apikeys_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":apikeys_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "apikeys_java_gapic_test_suite",
    test_classes = [
        "com.google.api.apikeys.v2.ApiKeysClientHttpJsonTest",
        "com.google.api.apikeys.v2.ApiKeysClientTest",
    ],
    runtime_deps = [":apikeys_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-api-apikeys-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":apikeys_java_gapic",
        ":apikeys_java_grpc",
        ":apikeys_java_proto",
        ":apikeys_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "apikeys_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/apikeys/apiv2/apikeyspb",
    protos = [":apikeys_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "apikeys_go_gapic",
    srcs = [":apikeys_proto_with_info"],
    grpc_service_config = "apikeys_grpc_service_config.json",
    importpath = "cloud.google.com/go/apikeys/apiv2;apikeys",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "apikeys_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":apikeys_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-api-apikeys-v2-go",
    deps = [
        ":apikeys_go_gapic",
        ":apikeys_go_gapic_srcjar-metadata.srcjar",
        ":apikeys_go_gapic_srcjar-snippets.srcjar",
        ":apikeys_go_gapic_srcjar-test.srcjar",
        ":apikeys_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "apikeys_py_gapic",
    srcs = [":apikeys_proto"],
    grpc_service_config = "apikeys_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=api_keys",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-api-keys",
    ],
    rest_numeric_enums = True,
    service_yaml = "apikeys_v2.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "apikeys_py_gapic_test",
    srcs = [
        "apikeys_py_gapic_pytest.py",
        "apikeys_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":apikeys_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "api-apikeys-v2-py",
    deps = [
        ":apikeys_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "apikeys_php_proto",
    deps = [":apikeys_proto"],
)

php_gapic_library(
    name = "apikeys_php_gapic",
    srcs = [":apikeys_proto_with_info"],
    grpc_service_config = "apikeys_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "apikeys_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":apikeys_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-api-apikeys-v2-php",
    deps = [
        ":apikeys_php_gapic",
        ":apikeys_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "apikeys_nodejs_gapic",
    package_name = "@google-cloud/apikeys",
    src = ":apikeys_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "apikeys_grpc_service_config.json",
    package = "google.api.apikeys.v2",
    rest_numeric_enums = True,
    service_yaml = "apikeys_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "api-apikeys-v2-nodejs",
    deps = [
        ":apikeys_nodejs_gapic",
        ":apikeys_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "apikeys_ruby_proto",
    deps = [":apikeys_proto"],
)

ruby_grpc_library(
    name = "apikeys_ruby_grpc",
    srcs = [":apikeys_proto"],
    deps = [":apikeys_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "apikeys_ruby_gapic",
    srcs = [":apikeys_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=apikeys.googleapis.com",
        "ruby-cloud-api-shortname=apikeys",
        "ruby-cloud-gem-name=google-cloud-api_keys-v2",
        "ruby-cloud-product-url=https://cloud.google.com/api-keys/",
    ],
    grpc_service_config = "apikeys_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "An API key is a simple encrypted string that you can use when calling Google Cloud APIs. The API Keys service manages the API keys associated with developer projects.",
    ruby_cloud_title = "API Keys V2",
    service_yaml = "apikeys_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":apikeys_ruby_grpc",
        ":apikeys_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-api-apikeys-v2-ruby",
    deps = [
        ":apikeys_ruby_gapic",
        ":apikeys_ruby_grpc",
        ":apikeys_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "apikeys_csharp_proto",
    deps = [":apikeys_proto"],
)

csharp_grpc_library(
    name = "apikeys_csharp_grpc",
    srcs = [":apikeys_proto"],
    deps = [":apikeys_csharp_proto"],
)

csharp_gapic_library(
    name = "apikeys_csharp_gapic",
    srcs = [":apikeys_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "apikeys_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "apikeys_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":apikeys_csharp_grpc",
        ":apikeys_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-api-apikeys-v2-csharp",
    deps = [
        ":apikeys_csharp_gapic",
        ":apikeys_csharp_grpc",
        ":apikeys_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "apikeys_cc_proto",
    deps = [":apikeys_proto"],
)

cc_grpc_library(
    name = "apikeys_cc_grpc",
    srcs = [":apikeys_proto"],
    grpc_only = True,
    deps = [":apikeys_cc_proto"],
)
