# This build file includes a target for the Ruby wrapper library for
# google-cloud-service_management.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for servicemanagement.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "servicemanagement_ruby_wrapper",
    srcs = ["//google/api/servicemanagement/v1:servicemanagement_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-service_management",
        "ruby-cloud-env-prefix=SERVICE_MANAGEMENT",
        "ruby-cloud-wrapper-of=v1:0.10",
        "ruby-cloud-product-url=https://cloud.google.com/service-infrastructure/docs/overview/",
        "ruby-cloud-api-id=servicemanagement.googleapis.com",
        "ruby-cloud-api-shortname=servicemanagement",
    ],
    ruby_cloud_description = "Google Service Management allows service producers to publish their services on Google Cloud Platform so that they can be discovered and used by service consumers.",
    ruby_cloud_title = "Service Management",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-servicemanagement-ruby",
    deps = [
        ":servicemanagement_ruby_wrapper",
    ],
)
