# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "servicemanagement_proto",
    srcs = [
        "resources.proto",
        "servicemanager.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:config_change_proto",
        "//google/api:field_behavior_proto",
        "//google/api:service_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "servicemanagement_proto_with_info",
    deps = [
        ":servicemanagement_proto",
        "//google/cloud:common_resources_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "servicemanagement_java_proto",
    deps = [":servicemanagement_proto"],
)

java_grpc_library(
    name = "servicemanagement_java_grpc",
    srcs = [":servicemanagement_proto"],
    deps = [":servicemanagement_java_proto"],
)

java_gapic_library(
    name = "servicemanagement_java_gapic",
    srcs = [":servicemanagement_proto_with_info"],
    gapic_yaml = "servicemanagement_gapic.yaml",
    grpc_service_config = "servicemanagement_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "servicemanagement_v1.yaml",
    test_deps = [
        "//google/iam/v1:iam_java_grpc",
        ":servicemanagement_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":servicemanagement_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "servicemanagement_java_gapic_test_suite",
    test_classes = [
        # This test is temporarily disabled due to the issue:
        # https://github.com/googleapis/sdk-platform-java/issues/1780
        # "com.google.cloud.api.servicemanagement.v1.ServiceManagerClientHttpJsonTest",
        "com.google.cloud.api.servicemanagement.v1.ServiceManagerClientTest",
    ],
    runtime_deps = [":servicemanagement_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-api-servicemanagement-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":servicemanagement_java_gapic",
        ":servicemanagement_java_grpc",
        ":servicemanagement_java_proto",
        ":servicemanagement_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "servicemanagement_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/servicemanagement/apiv1/servicemanagementpb",
    protos = [":servicemanagement_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:configchange_go_proto",
        "//google/api:serviceconfig_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "servicemanagement_go_gapic",
    srcs = [":servicemanagement_proto_with_info"],
    grpc_service_config = "servicemanagement_grpc_service_config.json",
    importpath = "cloud.google.com/go/servicemanagement/apiv1;servicemanagement",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "servicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicemanagement_go_proto",
        "//google/api:serviceconfig_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-api-servicemanagement-v1-go",
    deps = [
        ":servicemanagement_go_gapic",
        ":servicemanagement_go_gapic_srcjar-metadata.srcjar",
        ":servicemanagement_go_gapic_srcjar-snippets.srcjar",
        ":servicemanagement_go_gapic_srcjar-test.srcjar",
        ":servicemanagement_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "servicemanagement_py_gapic",
    srcs = [":servicemanagement_proto"],
    grpc_service_config = "servicemanagement_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=servicemanagement",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-service-management",
    ],
    rest_numeric_enums = True,
    service_yaml = "servicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "servicemanagement_py_gapic_test",
    srcs = [
        "servicemanagement_py_gapic_pytest.py",
        "servicemanagement_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":servicemanagement_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-servicemanagement-v1-py",
    deps = [
        ":servicemanagement_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "servicemanagement_php_proto",
    deps = [":servicemanagement_proto"],
)

php_gapic_library(
    name = "servicemanagement_php_gapic",
    srcs = [":servicemanagement_proto_with_info"],
    grpc_service_config = "servicemanagement_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "servicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicemanagement_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-api-servicemanagement-v1-php",
    deps = [
        ":servicemanagement_php_gapic",
        ":servicemanagement_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "servicemanagement_nodejs_gapic",
    package_name = "@google-cloud/service-management",
    src = ":servicemanagement_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "servicemanagement_grpc_service_config.json",
    package = "google.api.servicemanagement.v1",
    rest_numeric_enums = True,
    service_yaml = "servicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "api-servicemanagement-v1-nodejs",
    deps = [
        ":servicemanagement_nodejs_gapic",
        ":servicemanagement_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "servicemanagement_ruby_proto",
    deps = [":servicemanagement_proto"],
)

ruby_grpc_library(
    name = "servicemanagement_ruby_grpc",
    srcs = [":servicemanagement_proto"],
    deps = [":servicemanagement_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "servicemanagement_ruby_gapic",
    srcs = [":servicemanagement_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=servicemanagement.googleapis.com",
        "ruby-cloud-api-shortname=servicemanagement",
        "ruby-cloud-env-prefix=SERVICE_MANAGEMENT",
        "ruby-cloud-gem-name=google-cloud-service_management-v1",
        "ruby-cloud-product-url=https://cloud.google.com/service-infrastructure/docs/overview/",
    ],
    grpc_service_config = "servicemanagement_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Google Service Management allows service producers to publish their services on Google Cloud Platform so that they can be discovered and used by service consumers.",
    ruby_cloud_title = "Service Management V1",
    service_yaml = "servicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicemanagement_ruby_grpc",
        ":servicemanagement_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-api-servicemanagement-v1-ruby",
    deps = [
        ":servicemanagement_ruby_gapic",
        ":servicemanagement_ruby_grpc",
        ":servicemanagement_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "servicemanagement_csharp_proto",
    extra_opts = [],
    deps = [":servicemanagement_proto"],
)

csharp_grpc_library(
    name = "servicemanagement_csharp_grpc",
    srcs = [":servicemanagement_proto"],
    deps = [":servicemanagement_csharp_proto"],
)

csharp_gapic_library(
    name = "servicemanagement_csharp_gapic",
    srcs = [":servicemanagement_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "servicemanagement_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "servicemanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicemanagement_csharp_grpc",
        ":servicemanagement_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-api-servicemanagement-v1-csharp",
    deps = [
        ":servicemanagement_csharp_gapic",
        ":servicemanagement_csharp_grpc",
        ":servicemanagement_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "servicemanagement_cc_proto",
    deps = [":servicemanagement_proto"],
)

cc_grpc_library(
    name = "servicemanagement_cc_grpc",
    srcs = [":servicemanagement_proto"],
    grpc_only = True,
    deps = [":servicemanagement_cc_proto"],
)
