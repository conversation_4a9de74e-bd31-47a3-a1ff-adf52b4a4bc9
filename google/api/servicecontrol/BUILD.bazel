# This build file includes a target for the Ruby wrapper library for
# google-cloud-service_control.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for servicecontrol.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "servicecontrol_ruby_wrapper",
    srcs = ["//google/api/servicecontrol/v1:servicecontrol_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-service_control",
        "ruby-cloud-env-prefix=SERVICE_CONTROL",
        "ruby-cloud-wrapper-of=v1:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/service-infrastructure/docs/overview/",
        "ruby-cloud-api-id=servicecontrol.googleapis.com",
        "ruby-cloud-api-shortname=servicecontrol",
    ],
    ruby_cloud_description = "The Service Control API provides control plane functionality to managed services, such as logging, monitoring, and status checks.",
    ruby_cloud_title = "Service Control API",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-servicecontrol-ruby",
    deps = [
        ":servicecontrol_ruby_wrapper",
    ],
)
