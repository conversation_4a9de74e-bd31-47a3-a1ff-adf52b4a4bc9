# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "servicecontrol_proto",
    srcs = [
        "check_error.proto",
        "distribution.proto",
        "http_request.proto",
        "log_entry.proto",
        "metric_value.proto",
        "operation.proto",
        "quota_controller.proto",
        "service_controller.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:distribution_proto",
        "//google/logging/type:type_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "servicecontrol_proto_with_info",
    deps = [
        ":servicecontrol_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "servicecontrol_java_proto",
    deps = [":servicecontrol_proto"],
)

java_grpc_library(
    name = "servicecontrol_java_grpc",
    srcs = [":servicecontrol_proto"],
    deps = [":servicecontrol_java_proto"],
)

java_gapic_library(
    name = "servicecontrol_java_gapic",
    srcs = [":servicecontrol_proto_with_info"],
    grpc_service_config = "servicecontrol_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "servicecontrol.yaml",
    test_deps = [
        ":servicecontrol_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":servicecontrol_java_proto",
    ],
)

java_gapic_test(
    name = "servicecontrol_java_gapic_test_suite",
    test_classes = [
        "com.google.api.servicecontrol.v1.QuotaControllerClientHttpJsonTest",
        "com.google.api.servicecontrol.v1.QuotaControllerClientTest",
        "com.google.api.servicecontrol.v1.ServiceControllerClientHttpJsonTest",
        "com.google.api.servicecontrol.v1.ServiceControllerClientTest",
    ],
    runtime_deps = [":servicecontrol_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-api-servicecontrol-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":servicecontrol_java_gapic",
        ":servicecontrol_java_grpc",
        ":servicecontrol_java_proto",
        ":servicecontrol_proto",
    ],
)

go_proto_library(
    name = "servicecontrol_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/servicecontrol/apiv1/servicecontrolpb",
    protos = [":servicecontrol_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:distribution_go_proto",
        "//google/logging/type:type_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "servicecontrol_go_gapic",
    srcs = [":servicecontrol_proto_with_info"],
    grpc_service_config = "servicecontrol_grpc_service_config.json",
    importpath = "cloud.google.com/go/servicecontrol/apiv1;servicecontrol",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "servicecontrol.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicecontrol_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-api-servicecontrol-v1-go",
    deps = [
        ":servicecontrol_go_gapic",
        ":servicecontrol_go_gapic_srcjar-metadata.srcjar",
        ":servicecontrol_go_gapic_srcjar-snippets.srcjar",
        ":servicecontrol_go_gapic_srcjar-test.srcjar",
        ":servicecontrol_go_proto",
    ],
)

py_gapic_library(
    name = "servicecontrol_py_gapic",
    srcs = [":servicecontrol_proto"],
    grpc_service_config = "servicecontrol_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=servicecontrol",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-service-control",
    ],
    rest_numeric_enums = True,
    service_yaml = "servicecontrol.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "servicecontrol_py_gapic_test",
    srcs = [
        "servicecontrol_py_gapic_pytest.py",
        "servicecontrol_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":servicecontrol_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-servicecontrol-v1-py",
    deps = [
        ":servicecontrol_py_gapic",
    ],
)

php_proto_library(
    name = "servicecontrol_php_proto",
    deps = [":servicecontrol_proto"],
)

php_gapic_library(
    name = "servicecontrol_php_gapic",
    srcs = [":servicecontrol_proto_with_info"],
    grpc_service_config = "servicecontrol_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "servicecontrol.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicecontrol_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-api-servicecontrol-v1-php",
    deps = [
        ":servicecontrol_php_gapic",
        ":servicecontrol_php_proto",
    ],
)

nodejs_gapic_library(
    name = "servicecontrol_nodejs_gapic",
    package_name = "@google-cloud/service-control",
    src = ":servicecontrol_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "servicecontrol_grpc_service_config.json",
    package = "google.api.servicecontrol.v1",
    rest_numeric_enums = True,
    service_yaml = "servicecontrol.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "api-servicecontrol-v1-nodejs",
    deps = [
        ":servicecontrol_nodejs_gapic",
        ":servicecontrol_proto",
        ## DO NOT CHANGE: Owlbot add dependency logging type protos.
        "//google/logging/type:type_proto",
    ],
)

ruby_proto_library(
    name = "servicecontrol_ruby_proto",
    deps = [":servicecontrol_proto"],
)

ruby_grpc_library(
    name = "servicecontrol_ruby_grpc",
    srcs = [":servicecontrol_proto"],
    deps = [":servicecontrol_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "servicecontrol_ruby_gapic",
    srcs = [":servicecontrol_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=servicecontrol.googleapis.com",
        "ruby-cloud-api-shortname=servicecontrol",
        "ruby-cloud-env-prefix=SERVICE_CONTROL",
        "ruby-cloud-gem-name=google-cloud-service_control-v1",
        "ruby-cloud-product-url=https://cloud.google.com/service-infrastructure/docs/overview/",
    ],
    grpc_service_config = "servicecontrol_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "The Service Control API provides control plane functionality to managed services, such as logging, monitoring, and status checks.",
    ruby_cloud_title = "Service Control API V1",
    service_yaml = "servicecontrol.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicecontrol_ruby_grpc",
        ":servicecontrol_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-api-servicecontrol-v1-ruby",
    deps = [
        ":servicecontrol_ruby_gapic",
        ":servicecontrol_ruby_grpc",
        ":servicecontrol_ruby_proto",
    ],
)

csharp_proto_library(
    name = "servicecontrol_csharp_proto",
    deps = [":servicecontrol_proto"],
)

csharp_grpc_library(
    name = "servicecontrol_csharp_grpc",
    srcs = [":servicecontrol_proto"],
    deps = [":servicecontrol_csharp_proto"],
)

csharp_gapic_library(
    name = "servicecontrol_csharp_gapic",
    srcs = [":servicecontrol_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "servicecontrol_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "servicecontrol.yaml",
    transport = "grpc+rest",
    deps = [
        ":servicecontrol_csharp_grpc",
        ":servicecontrol_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-api-servicecontrol-v1-csharp",
    deps = [
        ":servicecontrol_csharp_gapic",
        ":servicecontrol_csharp_grpc",
        ":servicecontrol_csharp_proto",
    ],
)

cc_proto_library(
    name = "servicecontrol_cc_proto",
    deps = [":servicecontrol_proto"],
)

cc_grpc_library(
    name = "servicecontrol_cc_grpc",
    srcs = [":servicecontrol_proto"],
    grpc_only = True,
    deps = [":servicecontrol_cc_proto"],
)
