type: google.api.Service
config_version: 3
name: serviceconfig.googleapis.com
title: Service Config API

types:
- name: google.api.ConfigChange
- name: google.api.Distribution
- name: google.api.DocumentationRule
- name: google.api.FieldInfo
- name: google.api.HttpBody
- name: google.api.LabelDescriptor
- name: google.api.Metric
- name: google.api.MonitoredResource
- name: google.api.MonitoredResourceDescriptor
- name: google.api.MonitoredResourceMetadata
- name: google.api.ResourceDescriptor
- name: google.api.ResourceReference
- name: google.api.RoutingRule
- name: google.api.Service
- name: google.api.Visibility

enums:
- name: google.api.ErrorReason
- name: google.api.FieldBehavior

documentation:
  summary: Lets you define and config your API service.
