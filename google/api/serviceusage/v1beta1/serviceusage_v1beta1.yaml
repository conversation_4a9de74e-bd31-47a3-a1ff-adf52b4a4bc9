type: google.api.Service
config_version: 3
name: serviceusage.googleapis.com
title: Service Usage API

apis:
- name: google.api.serviceusage.v1beta1.ServiceUsage
- name: google.longrunning.Operations

types:
- name: google.api.serviceusage.v1beta1.BatchCreateAdminOverridesResponse
- name: google.api.serviceusage.v1beta1.BatchCreateConsumerOverridesResponse
- name: google.api.serviceusage.v1beta1.CreateAdminQuotaPolicyMetadata
- name: google.api.serviceusage.v1beta1.DeleteAdminQuotaPolicyMetadata
- name: google.api.serviceusage.v1beta1.GetServiceIdentityMetadata
- name: google.api.serviceusage.v1beta1.GetServiceIdentityResponse
- name: google.api.serviceusage.v1beta1.ImportAdminOverridesMetadata
- name: google.api.serviceusage.v1beta1.ImportAdminOverridesResponse
- name: google.api.serviceusage.v1beta1.ImportAdminQuotaPoliciesMetadata
- name: google.api.serviceusage.v1beta1.ImportAdminQuotaPoliciesResponse
- name: google.api.serviceusage.v1beta1.ImportConsumerOverridesMetadata
- name: google.api.serviceusage.v1beta1.ImportConsumerOverridesResponse
- name: google.api.serviceusage.v1beta1.OperationMetadata
- name: google.api.serviceusage.v1beta1.ServiceIdentity
- name: google.api.serviceusage.v1beta1.UpdateAdminQuotaPolicyMetadata

documentation:
  summary: |-
    Enables services that service consumers want to use on Google Cloud
    Platform, lists the available or enabled services, or disables services
    that service consumers no longer use.
  overview: |-
    The Service Usage API allows *service consumers* to manage the set
    of *services* they interact with. Consumers can use the Service Usage API
    or the [Google Cloud Console](https://console.cloud.google.com) to enable
    services in their [Google
    developer
    project](https://developers.google.com/console/help/new/). After a service
    is enabled, its APIs become available.

    ## Service consumers

    A service consumer is a Google developer project that has enabled and can
    invoke APIs on a service. A service can have many service consumers.

backend:
  rules:
  - selector: 'google.api.serviceusage.v1beta1.ServiceUsage.*'
    deadline: 20.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 20.0

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1beta1/{name=operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: /v1beta1/operations

authentication:
  rules:
  - selector: 'google.api.serviceusage.v1beta1.ServiceUsage.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/service.management
  - selector: google.api.serviceusage.v1beta1.ServiceUsage.GetConsumerQuotaLimit
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.api.serviceusage.v1beta1.ServiceUsage.GetConsumerQuotaMetric
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.api.serviceusage.v1beta1.ServiceUsage.GetService
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.api.serviceusage.v1beta1.ServiceUsage.ListAdminOverrides
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.api.serviceusage.v1beta1.ServiceUsage.ListConsumerOverrides
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.api.serviceusage.v1beta1.ServiceUsage.ListConsumerQuotaMetrics
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.api.serviceusage.v1beta1.ServiceUsage.ListServices
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/service.management
