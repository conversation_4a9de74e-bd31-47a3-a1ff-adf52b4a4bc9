# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "serviceusage_proto",
    srcs = [
        "resources.proto",
        "serviceusage.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:auth_proto",
        "//google/api:client_proto",
        "//google/api:documentation_proto",
        "//google/api:endpoint_proto",
        "//google/api:field_behavior_proto",
        "//google/api:monitored_resource_proto",
        "//google/api:monitoring_proto",
        "//google/api:quota_proto",
        "//google/api:usage_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:api_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
    ],
)

proto_library_with_info(
    name = "serviceusage_proto_with_info",
    deps = [
        ":serviceusage_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "serviceusage_java_proto",
    deps = [":serviceusage_proto"],
)

java_grpc_library(
    name = "serviceusage_java_grpc",
    srcs = [":serviceusage_proto"],
    deps = [":serviceusage_java_proto"],
)

java_gapic_library(
    name = "serviceusage_java_gapic",
    srcs = [":serviceusage_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "serviceusage_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "serviceusage_v1beta1.yaml",
    test_deps = [
        ":serviceusage_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":serviceusage_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "serviceusage_java_gapic_test_suite",
    test_classes = [
        "com.google.api.serviceusage.v1beta1.ServiceUsageClientHttpJsonTest",
        "com.google.api.serviceusage.v1beta1.ServiceUsageClientTest",
    ],
    runtime_deps = [":serviceusage_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-api-serviceusage-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":serviceusage_java_gapic",
        ":serviceusage_java_grpc",
        ":serviceusage_java_proto",
        ":serviceusage_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "serviceusage_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/api/serviceusage/v1beta1",
    protos = [":serviceusage_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:monitoredres_go_proto",
        "//google/api:serviceconfig_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "serviceusage_go_gapic",
    srcs = [":serviceusage_proto_with_info"],
    grpc_service_config = "serviceusage_grpc_service_config.json",
    importpath = "cloud.google.com/go/api/serviceusage/apiv1beta1;serviceusage",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "serviceusage_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":serviceusage_go_proto",
        "//google/api:monitoredres_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-api-serviceusage-v1beta1-go",
    deps = [
        ":serviceusage_go_gapic",
        ":serviceusage_go_gapic_srcjar-metadata.srcjar",
        ":serviceusage_go_gapic_srcjar-snippets.srcjar",
        ":serviceusage_go_gapic_srcjar-test.srcjar",
        ":serviceusage_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "serviceusage_py_gapic",
    srcs = [":serviceusage_proto"],
    grpc_service_config = "serviceusage_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=service_usage",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-service-usage",
    ],
    rest_numeric_enums = True,
    service_yaml = "serviceusage_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "serviceusage_py_gapic_test",
    srcs = [
        "serviceusage_py_gapic_pytest.py",
        "serviceusage_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":serviceusage_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "api-serviceusage-v1beta1-py",
    deps = [
        ":serviceusage_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "serviceusage_php_proto",
    deps = [":serviceusage_proto"],
)

php_gapic_library(
    name = "serviceusage_php_gapic",
    srcs = [":serviceusage_proto_with_info"],
    grpc_service_config = "serviceusage_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "serviceusage_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":serviceusage_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-api-serviceusage-v1beta1-php",
    deps = [
        ":serviceusage_php_gapic",
        ":serviceusage_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "serviceusage_nodejs_gapic",
    package_name = "@google-cloud/service-usage",
    src = ":serviceusage_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "serviceusage_grpc_service_config.json",
    package = "google.api.serviceusage.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "serviceusage_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "api-serviceusage-v1beta1-nodejs",
    deps = [
        ":serviceusage_nodejs_gapic",
        ":serviceusage_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "serviceusage_ruby_proto",
    deps = [":serviceusage_proto"],
)

ruby_grpc_library(
    name = "serviceusage_ruby_grpc",
    srcs = [":serviceusage_proto"],
    deps = [":serviceusage_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "serviceusage_ruby_gapic",
    srcs = [":serviceusage_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=serviceusage.googleapis.com",
        "ruby-cloud-api-shortname=serviceusage",
        "ruby-cloud-env-prefix=SERVICE_USAGE",
        "ruby-cloud-gem-name=google-cloud-service_usage-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/service-usage/",
    ],
    grpc_service_config = "serviceusage_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Service Usage is an infrastructure service of Google Cloud that lets you list and manage other APIs and services in your Cloud projects. You can list and manage Google Cloud services and their APIs, as well as services created using Cloud Endpoints.",
    ruby_cloud_title = "Service Usage V1beta1",
    service_yaml = "serviceusage_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":serviceusage_ruby_grpc",
        ":serviceusage_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-api-serviceusage-v1beta1-ruby",
    deps = [
        ":serviceusage_ruby_gapic",
        ":serviceusage_ruby_grpc",
        ":serviceusage_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "serviceusage_csharp_proto",
    extra_opts = [],
    deps = [":serviceusage_proto"],
)

csharp_grpc_library(
    name = "serviceusage_csharp_grpc",
    srcs = [":serviceusage_proto"],
    deps = [":serviceusage_csharp_proto"],
)

csharp_gapic_library(
    name = "serviceusage_csharp_gapic",
    srcs = [":serviceusage_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "serviceusage_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "serviceusage_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":serviceusage_csharp_grpc",
        ":serviceusage_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-api-serviceusage-v1beta1-csharp",
    deps = [
        ":serviceusage_csharp_gapic",
        ":serviceusage_csharp_grpc",
        ":serviceusage_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "serviceusage_cc_proto",
    deps = [":serviceusage_proto"],
)

cc_grpc_library(
    name = "serviceusage_cc_grpc",
    srcs = [":serviceusage_proto"],
    grpc_only = True,
    deps = [":serviceusage_cc_proto"],
)
