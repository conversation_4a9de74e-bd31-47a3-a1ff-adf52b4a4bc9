# This build file includes a target for the Ruby wrapper library for
# google-cloud-service_usage.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for serviceusage.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "serviceusage_ruby_wrapper",
    srcs = ["//google/api/serviceusage/v1:serviceusage_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-service_usage",
        "ruby-cloud-env-prefix=SERVICE_USAGE",
        "ruby-cloud-wrapper-of=v1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/service-usage/",
        "ruby-cloud-api-id=serviceusage.googleapis.com",
        "ruby-cloud-api-shortname=serviceusage",
    ],
    ruby_cloud_description = "Service Usage is an infrastructure service of Google Cloud that lets you list and manage other APIs and services in your Cloud projects. You can list and manage Google Cloud services and their APIs, as well as services created using Cloud Endpoints.",
    ruby_cloud_title = "Service Usage",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-serviceusage-ruby",
    deps = [
        ":serviceusage_ruby_wrapper",
    ],
)
