// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.type;

option csharp_namespace = "Google.Shopping.Type";
option go_package = "cloud.google.com/go/shopping/type/typepb;typepb";
option java_multiple_files = true;
option java_outer_classname = "TypesProto";
option java_package = "com.google.shopping.type";

// The weight represented as the value in string and the unit.
message Weight {
  // The weight unit.
  enum WeightUnit {
    // unit unspecified
    WEIGHT_UNIT_UNSPECIFIED = 0;

    // lb unit.
    POUND = 1;

    // kg unit.
    KILOGRAM = 2;
  }

  // Required. The weight represented as a number in micros (1 million micros is
  // an equivalent to one's currency standard unit, for example, 1 kg = 1000000
  // micros).
  // This field can also be set as infinity by setting to -1.
  // This field only support -1 and positive value.
  optional int64 amount_micros = 1;

  // Required. The weight unit.
  // Acceptable values are: kg and lb
  WeightUnit unit = 2;
}

// The price represented as a number and currency.
message Price {
  // The price represented as a number in micros (1 million micros is an
  // equivalent to one's currency standard unit, for example, 1 USD = 1000000
  // micros).
  optional int64 amount_micros = 1;

  // The currency of the price using three-letter acronyms according to [ISO
  // 4217](http://en.wikipedia.org/wiki/ISO_4217).
  optional string currency_code = 2;
}

// A message that represents custom attributes. Exactly one of `value` or
// `group_values` must not be empty.
message CustomAttribute {
  // The name of the attribute.
  optional string name = 1;

  // The value of the attribute. If `value` is not empty, `group_values` must be
  // empty.
  optional string value = 2;

  // Subattributes within this attribute group.  If
  // `group_values` is not empty, `value` must be empty.
  repeated CustomAttribute group_values = 3;
}

// Destinations available for a product.
//
// Destinations are used in Merchant Center to allow you to control where the
// products from your data feed should be displayed.
//
message Destination {
  // Destination values.
  enum DestinationEnum {
    // Not specified.
    DESTINATION_ENUM_UNSPECIFIED = 0;

    // [Shopping ads](https://support.google.com/google-ads/answer/2454022).
    SHOPPING_ADS = 1;

    // [Display ads](https://support.google.com/merchants/answer/6069387).
    DISPLAY_ADS = 2;

    // [Local inventory
    // ads](https://support.google.com/merchants/answer/3057972).
    LOCAL_INVENTORY_ADS = 3;

    // [Free listings](https://support.google.com/merchants/answer/9199328).
    FREE_LISTINGS = 4;

    // [Free local product
    // listings](https://support.google.com/merchants/answer/9825611).
    FREE_LOCAL_LISTINGS = 5;

    // [YouTube Shopping](https://support.google.com/merchants/answer/********).
    YOUTUBE_SHOPPING = 6;
  }
}

// Reporting contexts that your account and product issues apply to.
//
// Reporting contexts are groups of surfaces and formats for product results on
// Google. They can represent the entire destination (for example, [Shopping
// ads](https://support.google.com/merchants/answer/6149970)) or a subset of
// formats within a destination (for example, [Demand Gen
// ads](https://support.google.com/merchants/answer/********)).
//
message ReportingContext {
  // Reporting context values.
  enum ReportingContextEnum {
    // Not specified.
    REPORTING_CONTEXT_ENUM_UNSPECIFIED = 0;

    // [Shopping ads](https://support.google.com/merchants/answer/6149970).
    SHOPPING_ADS = 1;

    // Deprecated:  Use `DEMAND_GEN_ADS` instead.
    // [Discovery and Demand Gen
    // ads](https://support.google.com/merchants/answer/********).
    DISCOVERY_ADS = 2 [deprecated = true];

    // [Demand Gen ads](https://support.google.com/merchants/answer/********).
    DEMAND_GEN_ADS = 13;

    // [Demand Gen ads on Discover
    // surface](https://support.google.com/merchants/answer/********).
    DEMAND_GEN_ADS_DISCOVER_SURFACE = 14;

    // [Video ads](https://support.google.com/google-ads/answer/6340491).
    VIDEO_ADS = 3;

    // [Display ads](https://support.google.com/merchants/answer/6069387).
    DISPLAY_ADS = 4;

    // [Local inventory
    // ads](https://support.google.com/merchants/answer/3271956).
    LOCAL_INVENTORY_ADS = 5;

    // [Vehicle inventory
    // ads](https://support.google.com/merchants/answer/11544533).
    VEHICLE_INVENTORY_ADS = 6;

    // [Free product
    // listings](https://support.google.com/merchants/answer/9199328).
    FREE_LISTINGS = 7;

    // [Free local product
    // listings](https://support.google.com/merchants/answer/9825611).
    FREE_LOCAL_LISTINGS = 8;

    // [Free local vehicle
    // listings](https://support.google.com/merchants/answer/11544533).
    FREE_LOCAL_VEHICLE_LISTINGS = 9;

    // [YouTube
    // Shopping](https://support.google.com/merchants/answer/13478370).
    YOUTUBE_SHOPPING = 10;

    // [Cloud retail](https://cloud.google.com/solutions/retail).
    CLOUD_RETAIL = 11;

    // [Local cloud retail](https://cloud.google.com/solutions/retail).
    LOCAL_CLOUD_RETAIL = 12;
  }
}

// [Channel](https://support.google.com/merchants/answer/7361332) of a product.
//
// Channel is used to distinguish between online and local products.
message Channel {
  // Channel values.
  enum ChannelEnum {
    // Not specified.
    CHANNEL_ENUM_UNSPECIFIED = 0;

    // Online product.
    ONLINE = 1;

    // Local product.
    LOCAL = 2;
  }
}
