# This file was automatically generated by BuildFileGenerator

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "type_proto",
    srcs = [
        "types.proto",
    ],
    deps = [
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_proto_library",
    "java_gapic_assembly_gradle_pkg",
)

java_proto_library(
    name = "type_java_proto",
    deps = [":type_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-shopping-type-java",
    deps = [
        ":type_proto",
        ":type_java_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
    "go_gapic_assembly_pkg",
)

go_proto_library(
    name = "type_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/shopping/type/typepb",
    protos = [":type_proto"],
    deps = [
    ],
)

go_gapic_assembly_pkg(
    name = "google-shopping-type-go",
    deps = [
        ":type_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
    "py_gapic_library",
    "py_gapic_assembly_pkg",
)

moved_proto_library(
    name = "type_moved_proto",
    srcs = [":type_proto"],
    deps = [
    ],
)

py_proto_library(
    name = "type_py_proto",
    deps = [":type_moved_proto"],
)

py_grpc_library(
    name = "type_py_grpc",
    srcs = [":type_moved_proto"],
    deps = [":type_py_proto"],
)

py_gapic_library(
    name = "type_py_gapic",
    srcs = [":type_proto"],
    rest_numeric_enums = False,
    transport = "grpc+rest",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-shopping-type-py",
    deps = [
        ":type_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "type_php_proto",
    deps = [":type_proto"],
)

php_gapic_assembly_pkg(
    name = "google-shopping-type-php",
    deps = [
        ":type_php_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "type_ruby_proto",
    deps = [":type_proto"],
)

ruby_grpc_library(
    name = "type_ruby_grpc",
    srcs = [":type_proto"],
    deps = [":type_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_proto_library",
    "csharp_gapic_assembly_pkg",
)

csharp_proto_library(
    name = "type_csharp_proto",
    deps = [":type_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-shopping-type-csharp",
    package_name = "Google.Shopping.Type",
    generate_nongapic_package = True,
    deps = [
        ":type_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "type_cc_proto",
    deps = [":type_proto"],
)

cc_grpc_library(
    name = "type_cc_grpc",
    srcs = [":type_proto"],
    grpc_only = True,
    deps = [":type_cc_proto"],
)
