// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.reviews.v1beta;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "google/shopping/type/types.proto";

option csharp_namespace = "Google.Shopping.Merchant.Reviews.V1Beta";
option go_package = "cloud.google.com/go/shopping/merchant/reviews/apiv1beta/reviewspb;reviewspb";
option java_multiple_files = true;
option java_outer_classname = "MerchantReviewsCommonProto";
option java_package = "com.google.shopping.merchant.reviews.v1beta";
option php_namespace = "Google\\Shopping\\Merchant\\Reviews\\V1beta";
option ruby_package = "Google::Shopping::Merchant::Reviews::V1beta";

// Attributes.
message MerchantReviewAttributes {
  // The method used to collect the review.
  enum CollectionMethod {
    // Collection method unspecified.
    COLLECTION_METHOD_UNSPECIFIED = 0;

    // The user was not responding to a specific solicitation when they
    // submitted the review.
    MERCHANT_UNSOLICITED = 1;

    // The user submitted the review in response to a solicitation when the
    // user placed an order.
    POINT_OF_SALE = 2;

    // The user submitted the review in response to a solicitation after
    // fulfillment of the user's order.
    AFTER_FULFILLMENT = 3;
  }

  // Required. Must be unique and stable across all requests. In other words, if
  // a request today and another 90 days ago refer to the same merchant, they
  // must have the same id.
  optional string merchant_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Human-readable display name for the merchant.
  optional string merchant_display_name = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. URL to the merchant's main website. Do not use a redirect URL for
  // this value. In other words, the value should point directly to the
  // merchant's site.
  optional string merchant_link = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. URL to the landing page that hosts the reviews for this merchant.
  // Do not use a redirect URL.
  optional string merchant_rating_link = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The minimum possible number for the rating. This should be the
  // worst possible rating and should not be a value for no rating.
  optional int64 min_rating = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum possible number for the rating. The value of the max
  // rating must be greater than the value of the min rating.
  optional int64 max_rating = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The reviewer's overall rating of the merchant.
  optional double rating = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The title of the review.
  optional string title = 8 [(google.api.field_behavior) = OPTIONAL];

  // Required. This should be any freeform text provided by the user and should
  // not be truncated. If multiple responses to different questions are
  // provided, all responses should be included, with the minimal context for
  // the responses to make sense. Context should not be provided if questions
  // were left unanswered.
  optional string content = 9 [(google.api.field_behavior) = REQUIRED];

  // Optional. A permanent, unique identifier for the author of the review in
  // the publisher's system.
  optional string reviewer_id = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Display name of the review author.
  optional string reviewer_username = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set to true if the reviewer should remain anonymous.
  optional bool is_anonymous = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The method used to collect the review.
  optional CollectionMethod collection_method = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The timestamp indicating when the review was written.
  optional google.protobuf.Timestamp review_time = 14
      [(google.api.field_behavior) = REQUIRED];

  // Required. The language of the review defined by BCP-47 language code.
  optional string review_language = 15 [(google.api.field_behavior) = REQUIRED];

  // Optional. The country where the reviewer made the order defined by ISO
  // 3166-1 Alpha-2 Country Code.
  optional string review_country = 16 [(google.api.field_behavior) = OPTIONAL];
}

// The status of a merchant review, data validation issues, that is, information
// about a merchant review computed asynchronously.
message MerchantReviewStatus {
  // The destination status of the merchant review status.
  message MerchantReviewDestinationStatus {
    // Output only. The name of the reporting context.
    google.shopping.type.ReportingContext.ReportingContextEnum
        reporting_context = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The ItemLevelIssue of the merchant review status.
  message MerchantReviewItemLevelIssue {
    // How the issue affects the serving of the merchant review.
    enum Severity {
      // Not specified.
      SEVERITY_UNSPECIFIED = 0;

      // This issue represents a warning and does not have a direct affect
      // on the merchant review.
      NOT_IMPACTED = 1;

      // Issue disapproves the merchant review.
      DISAPPROVED = 2;
    }

    // Output only. The error code of the issue.
    string code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. How this issue affects serving of the merchant review.
    Severity severity = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Whether the issue can be resolved by the merchant.
    string resolution = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The attribute's name, if the issue is caused by a single
    // attribute.
    string attribute = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The reporting context the issue applies to.
    google.shopping.type.ReportingContext.ReportingContextEnum
        reporting_context = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A short issue description in English.
    string description = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A detailed issue description in English.
    string detail = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The URL of a web page to help with resolving this issue.
    string documentation = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The intended destinations for the merchant review.
  repeated MerchantReviewDestinationStatus destination_statuses = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of all issues associated with the merchant review.
  repeated MerchantReviewItemLevelIssue item_level_issues = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Date on which the item has been created, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Date on which the item has been last updated, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp last_update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
