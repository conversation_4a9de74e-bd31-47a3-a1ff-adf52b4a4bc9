// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.reviews.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/shopping/merchant/reviews/v1beta/productreviews_common.proto";
import "google/shopping/type/types.proto";

option csharp_namespace = "Google.Shopping.Merchant.Reviews.V1Beta";
option go_package = "cloud.google.com/go/shopping/merchant/reviews/apiv1beta/reviewspb;reviewspb";
option java_multiple_files = true;
option java_outer_classname = "ProductReviewsProto";
option java_package = "com.google.shopping.merchant.reviews.v1beta";
option php_namespace = "Google\\Shopping\\Merchant\\Reviews\\V1beta";
option ruby_package = "Google::Shopping::Merchant::Reviews::V1beta";

// Service to manage product reviews.
service ProductReviewsService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Gets a product review.
  rpc GetProductReview(GetProductReviewRequest) returns (ProductReview) {
    option (google.api.http) = {
      get: "/reviews/v1beta/{name=accounts/*/productReviews/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists product reviews.
  rpc ListProductReviews(ListProductReviewsRequest)
      returns (ListProductReviewsResponse) {
    option (google.api.http) = {
      get: "/reviews/v1beta/{parent=accounts/*}/productReviews"
    };
    option (google.api.method_signature) = "parent";
  }

  // Inserts a product review.
  rpc InsertProductReview(InsertProductReviewRequest) returns (ProductReview) {
    option (google.api.http) = {
      post: "/reviews/v1beta/{parent=accounts/*}/productReviews:insert"
      body: "product_review"
    };
  }

  // Deletes a product review.
  rpc DeleteProductReview(DeleteProductReviewRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/reviews/v1beta/{name=accounts/*/productReviews/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for the GetProductReview method.
message GetProductReviewRequest {
  // Required. The ID of the merchant review.
  // Format: accounts/{account}/productReviews/{productReview}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/ProductReview"
    }
  ];
}

// Request message for the `DeleteProductReview` method.
message DeleteProductReviewRequest {
  // Required. The ID of the Product review.
  // Format: accounts/{account}/productReviews/{productReview}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/ProductReview"
    }
  ];
}

// Request message for the ListProductReviews method.
message ListProductReviewsRequest {
  // Required. The account to list product reviews for.
  // Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/ProductReview"
    }
  ];

  // Optional. The maximum number of products to return. The service may return
  // fewer than this value.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListProductReviews` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListProductReviews`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `InsertProductReview` method.
message InsertProductReviewRequest {
  // Required. The account where the product review will be inserted.
  // Format: accounts/{account}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The product review to insert.
  ProductReview product_review = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Format:
  // `accounts/{account}/dataSources/{datasource}`.
  string data_source = 3 [(google.api.field_behavior) = REQUIRED];
}

// response message for the ListProductReviews method.
message ListProductReviewsResponse {
  // The product review.
  repeated ProductReview product_reviews = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// A review for a product. For more information, see
// [Introduction to Product Review
// Feeds](https://developers.google.com/product-review-feeds)
message ProductReview {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/ProductReview"
    pattern: "accounts/{account}/productReviews/{productreview}"
    plural: "productReviews"
    singular: "productReview"
  };

  // Identifier. The name of the product review.
  // Format:
  // `"{productreview.name=accounts/{account}/productReviews/{productReview}}"`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The permanent, unique identifier for the product review in the
  // publisher’s system.
  string product_review_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A list of product review attributes.
  ProductReviewAttributes attributes = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of custom (merchant-provided) attributes.
  repeated google.shopping.type.CustomAttribute custom_attributes = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The primary data source of the product review.
  string data_source = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of a product review, data validation issues, that
  // is, information about a product review computed asynchronously.
  ProductReviewStatus product_review_status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
