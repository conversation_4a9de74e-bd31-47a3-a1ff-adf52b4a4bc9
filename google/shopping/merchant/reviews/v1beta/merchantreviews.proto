// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.reviews.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/shopping/merchant/reviews/v1beta/merchantreviews_common.proto";
import "google/shopping/type/types.proto";

option csharp_namespace = "Google.Shopping.Merchant.Reviews.V1Beta";
option go_package = "cloud.google.com/go/shopping/merchant/reviews/apiv1beta/reviewspb;reviewspb";
option java_multiple_files = true;
option java_outer_classname = "MerchantReviewsProto";
option java_package = "com.google.shopping.merchant.reviews.v1beta";
option php_namespace = "Google\\Shopping\\Merchant\\Reviews\\V1beta";
option ruby_package = "Google::Shopping::Merchant::Reviews::V1beta";
option (google.api.resource_definition) = {
  type: "merchantapi.googleapis.com/Account"
  pattern: "accounts/{account}"
};

// Service to manage merchant reviews.
service MerchantReviewsService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Gets a merchant review.
  rpc GetMerchantReview(GetMerchantReviewRequest) returns (MerchantReview) {
    option (google.api.http) = {
      get: "/reviews/v1beta/{name=accounts/*/merchantReviews/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists merchant reviews.
  rpc ListMerchantReviews(ListMerchantReviewsRequest)
      returns (ListMerchantReviewsResponse) {
    option (google.api.http) = {
      get: "/reviews/v1beta/{parent=accounts/*}/merchantReviews"
    };
    option (google.api.method_signature) = "parent";
  }

  // Inserts a review for your Merchant Center account. If the review
  // already exists, then the review is replaced with the new instance.
  rpc InsertMerchantReview(InsertMerchantReviewRequest)
      returns (MerchantReview) {
    option (google.api.http) = {
      post: "/reviews/v1beta/{parent=accounts/*}/merchantReviews:insert"
      body: "merchant_review"
    };
  }

  // Deletes merchant review.
  rpc DeleteMerchantReview(DeleteMerchantReviewRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/reviews/v1beta/{name=accounts/*/merchantReviews/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message for the `GetMerchantReview` method.
message GetMerchantReviewRequest {
  // Required. The ID of the merchant review.
  // Format: accounts/{account}/merchantReviews/{merchantReview}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/MerchantReview"
    }
  ];
}

// Request message for the `DeleteMerchantReview` method.
message DeleteMerchantReviewRequest {
  // Required. The ID of the merchant review.
  // Format: accounts/{account}/merchantReviews/{merchantReview}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/MerchantReview"
    }
  ];
}

// Request message for the `ListMerchantsReview` method.
message ListMerchantReviewsRequest {
  // Required. The account to list merchant reviews for.
  // Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/MerchantReview"
    }
  ];

  // Optional. The maximum number of merchant reviews to return. The service can
  // return fewer than this value. The maximum value is 1000; values above 1000
  // are coerced to 1000. If unspecified, the maximum number of reviews is
  // returned.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListMerchantReviews`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListMerchantReviews`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `InsertMerchantReview` method.
message InsertMerchantReviewRequest {
  // Required. The account where the merchant review will be inserted.
  // Format: accounts/{account}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The merchant review to insert.
  MerchantReview merchant_review = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The data source of the
  // [merchantreview](https://support.google.com/merchants/answer/7045996?sjid=5253581244217581976-EU)
  // Format:
  // `accounts/{account}/dataSources/{datasource}`.
  string data_source = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response message for the `ListMerchantsReview` method.
message ListMerchantReviewsResponse {
  // The merchant review.
  repeated MerchantReview merchant_reviews = 1;

  // The token to retrieve the next page of results.
  string next_page_token = 2;
}

// A review for a merchant. For more information, see
// [Introduction to Merchant Review
// Feeds](https://developers.google.com/merchant-review-feeds)
message MerchantReview {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/MerchantReview"
    pattern: "accounts/{account}/merchantReviews/{name}"
    plural: "merchantReviews"
    singular: "merchantReview"
  };

  // Identifier. The name of the merchant review.
  // Format:
  // `"{merchantreview.name=accounts/{account}/merchantReviews/{merchantReview}}"`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The user provided merchant review ID to uniquely identify the
  // merchant review.
  string merchant_review_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A list of merchant review attributes.
  MerchantReviewAttributes attributes = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Required. A list of custom (merchant-provided) attributes. It can also be
  // used for submitting any attribute of the data specification in its generic
  // form (for example,
  // `{ "name": "size type", "value": "regular" }`).
  // This is useful for submitting attributes not explicitly exposed by the
  // API, such as experimental attributes.
  // Maximum allowed number of characters for each
  // custom attribute is 10240 (represents sum of characters for name and
  // value). Maximum 2500 custom attributes can be set per product, with total
  // size of 102.4kB. Underscores in custom attribute names are replaced by
  // spaces upon insertion.
  repeated google.shopping.type.CustomAttribute custom_attributes = 4
      [(google.api.field_behavior) = REQUIRED];

  // Output only. The primary data source of the merchant review.
  string data_source = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of a merchant review, data validation issues, that
  // is, information about a merchant review computed asynchronously.
  MerchantReviewStatus merchant_review_status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
