// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.reviews.v1beta;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "google/shopping/type/types.proto";

option csharp_namespace = "Google.Shopping.Merchant.Reviews.V1Beta";
option go_package = "cloud.google.com/go/shopping/merchant/reviews/apiv1beta/reviewspb;reviewspb";
option java_multiple_files = true;
option java_outer_classname = "ProductReviewsCommonProto";
option java_package = "com.google.shopping.merchant.reviews.v1beta";
option php_namespace = "Google\\Shopping\\Merchant\\Reviews\\V1beta";
option ruby_package = "Google::Shopping::Merchant::Reviews::V1beta";

// Attributes.
message ProductReviewAttributes {
  // The URI of the review landing page.
  message ReviewLink {
    // Type of the review URI.
    enum Type {
      // Type unspecified.
      TYPE_UNSPECIFIED = 0;

      // The review page contains only this single review.
      SINGLETON = 1;

      // The review page contains a group of reviews including this review.
      GROUP = 2;
    }

    // Optional. Type of the review URI.
    Type type = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The URI of the review landing page.
    // For example: `http://www.example.com/review_5.html`.
    string link = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // The method used to collect the review.
  enum CollectionMethod {
    // Collection method unspecified.
    COLLECTION_METHOD_UNSPECIFIED = 0;

    // The user was not responding to a specific solicitation when they
    // submitted the review.
    UNSOLICITED = 1;

    // The user submitted the review in response to a solicitation after
    // fulfillment of the user's order.
    POST_FULFILLMENT = 2;
  }

  // Optional. The name of the aggregator of the product reviews.
  //
  // A publisher may use a reviews aggregator to manage reviews and provide
  // the feeds. This element indicates the use of an aggregator and contains
  // information about the aggregator.
  optional string aggregator_name = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the subclient of the product reviews.
  //
  // The subclient is an identifier of the product review source.
  // It should be equivalent to the directory provided in the file data source
  // path.
  optional string subclient_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the publisher of the product reviews.
  //
  // The information about the publisher, which may be a retailer,
  // manufacturer, reviews service company, or any entity that publishes
  // product reviews.
  optional string publisher_name = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A link to the company favicon of the publisher. The image
  // dimensions should be favicon size: 16x16 pixels. The image format should be
  // GIF, JPG or PNG.
  optional string publisher_favicon = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The author of the product review.
  //
  // A permanent, unique identifier for the author of the review in the
  // publisher's system.
  optional string reviewer_id = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Set to true if the reviewer should remain anonymous.
  optional bool reviewer_is_anonymous = 6
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the reviewer of the product review.
  optional string reviewer_username = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The language of the review defined by BCP-47 language code.
  optional string review_language = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The country of the review defined by ISO 3166-1 Alpha-2 Country
  // Code.
  optional string review_country = 9 [(google.api.field_behavior) = OPTIONAL];

  // Required. The timestamp indicating when the review was written.
  google.protobuf.Timestamp review_time = 10
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The title of the review.
  optional string title = 11 [(google.api.field_behavior) = OPTIONAL];

  // Required. The content of the review.
  optional string content = 12 [(google.api.field_behavior) = REQUIRED];

  // Optional. Contains the advantages based on the opinion of the reviewer.
  // Omit boilerplate text like "pro:" unless it was written by the reviewer.
  repeated string pros = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Contains the disadvantages based on the opinion of the reviewer.
  // Omit boilerplate text like "con:" unless it was written by the reviewer.
  repeated string cons = 14 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The URI of the review landing page.
  ReviewLink review_link = 15 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A URI to an image of the reviewed product created by the review
  // author. The URI does not have to end with an image file extension.
  repeated string reviewer_image_links = 16
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Contains the ratings associated with the review.
  // The minimum possible number for the rating. This should be the worst
  // possible rating and should not be a value for no rating.
  optional int64 min_rating = 17 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum possible number for the rating. The value of the max
  // rating must be greater than the value of the min attribute.
  optional int64 max_rating = 18 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The reviewer's overall rating of the product.
  optional double rating = 19 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Descriptive name of a product.
  repeated string product_names = 20 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The URI of the product. This URI can have the same value as the
  // `review_link` element, if the review URI and the product URI are the
  // same.
  repeated string product_links = 21 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Contains ASINs (Amazon Standard Identification Numbers)
  // associated with a product.
  repeated string asins = 22 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Contains GTINs (global trade item numbers) associated with a
  // product. Sub-types of GTINs (e.g. UPC, EAN, ISBN, JAN) are supported.
  repeated string gtins = 23 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Contains MPNs (manufacturer part numbers) associated with a
  // product.
  repeated string mpns = 24 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Contains SKUs (stock keeping units) associated with a product.
  // Often this matches the product Offer Id in the product feed.
  repeated string skus = 25 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Contains brand names associated with a product.
  repeated string brands = 26 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates whether the review is marked as spam in the publisher's
  // system.
  optional bool is_spam = 27 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The method used to collect the review.
  CollectionMethod collection_method = 28
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A permanent, unique identifier for the transaction associated
  // with the review in the publisher's system. This ID can be used to indicate
  // that multiple reviews are associated with the same transaction.
  string transaction_id = 29 [(google.api.field_behavior) = OPTIONAL];
}

// Product review status.
message ProductReviewStatus {
  // The destination status of the product review status.
  message ProductReviewDestinationStatus {
    // Output only. The name of the reporting context.
    google.shopping.type.ReportingContext.ReportingContextEnum
        reporting_context = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The ItemLevelIssue of the product review status.
  message ProductReviewItemLevelIssue {
    // How the issue affects the serving of the product review.
    enum Severity {
      // Not specified.
      SEVERITY_UNSPECIFIED = 0;

      // This issue represents a warning and does not have a direct affect
      // on the product review.
      NOT_IMPACTED = 1;

      // Issue disapproves the product review.
      DISAPPROVED = 2;
    }

    // Output only. The error code of the issue.
    string code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. How this issue affects serving of the product review.
    Severity severity = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Whether the issue can be resolved by the merchant.
    string resolution = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The attribute's name, if the issue is caused by a single
    // attribute.
    string attribute = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The reporting context the issue applies to.
    google.shopping.type.ReportingContext.ReportingContextEnum
        reporting_context = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A short issue description in English.
    string description = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. A detailed issue description in English.
    string detail = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The URL of a web page to help with resolving this issue.
    string documentation = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The intended destinations for the product review.
  repeated ProductReviewDestinationStatus destination_statuses = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of all issues associated with the product review.
  repeated ProductReviewItemLevelIssue item_level_issues = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Date on which the item has been created, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Date on which the item has been last updated, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp last_update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
