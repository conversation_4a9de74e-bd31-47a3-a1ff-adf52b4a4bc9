// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.notifications.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/shopping/type/types.proto";

option go_package = "cloud.google.com/go/shopping/merchant/notifications/apiv1beta/notificationspb;notificationspb";
option java_multiple_files = true;
option java_outer_classname = "NotificationsApiProto";
option java_package = "com.google.shopping.merchant.notifications.v1beta";
option (google.api.resource_definition) = {
  type: "merchantapi.googleapis.com/Account"
  pattern: "accounts/{account}"
};

// Service to manage notification subscriptions for merchants
service NotificationsApiService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Gets notification subscriptions for an account.
  rpc GetNotificationSubscription(GetNotificationSubscriptionRequest)
      returns (NotificationSubscription) {
    option (google.api.http) = {
      get: "/notifications/v1beta/{name=accounts/*/notificationsubscriptions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a notification subscription for a merchant. We will allow the
  // following types of notification subscriptions to exist together (per
  // merchant as a subscriber per event type):
  // 1. Subscription for all managed accounts + subscription for self
  // 2. Multiple "partial" subscriptions for managed accounts + subscription
  // for self
  //
  // we will not allow (per merchant as a subscriber per event type):
  // 1. multiple self subscriptions.
  // 2. multiple "all managed accounts" subscriptions.
  // 3. all and partial subscriptions at the same time.
  // 4. multiple partial subscriptions for the same target account
  rpc CreateNotificationSubscription(CreateNotificationSubscriptionRequest)
      returns (NotificationSubscription) {
    option (google.api.http) = {
      post: "/notifications/v1beta/{parent=accounts/*}/notificationsubscriptions"
      body: "notification_subscription"
    };
    option (google.api.method_signature) = "parent,notification_subscription";
  }

  // Updates an existing notification subscription for a merchant.
  rpc UpdateNotificationSubscription(UpdateNotificationSubscriptionRequest)
      returns (NotificationSubscription) {
    option (google.api.http) = {
      patch: "/notifications/v1beta/{notification_subscription.name=accounts/*/notificationsubscriptions/*}"
      body: "notification_subscription"
    };
    option (google.api.method_signature) =
        "notification_subscription,update_mask";
  }

  // Deletes a notification subscription for a merchant.
  rpc DeleteNotificationSubscription(DeleteNotificationSubscriptionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/notifications/v1beta/{name=accounts/*/notificationsubscriptions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets all the notification subscriptions for a merchant.
  rpc ListNotificationSubscriptions(ListNotificationSubscriptionsRequest)
      returns (ListNotificationSubscriptionsResponse) {
    option (google.api.http) = {
      get: "/notifications/v1beta/{parent=accounts/*}/notificationsubscriptions"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Enum to specify the resource that is being changed to notify the merchant
// about.
enum Resource {
  // Unspecified resource
  RESOURCE_UNSPECIFIED = 0;

  // Resource type : product
  PRODUCT = 1;
}

// Enum to specify the attribute in the resource that is being changed to
// notify the merchant about.
enum Attribute {
  // Unspecified attribute
  ATTRIBUTE_UNSPECIFIED = 0;

  // Status of the changed entity
  STATUS = 1;
}

// Request message for the GetNotificationSubscription method.
message GetNotificationSubscriptionRequest {
  // Required. The `name` of the notification subscription.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/NotificationSubscription"
    }
  ];
}

// Request message for the CreateNotificationSubscription method.
message CreateNotificationSubscriptionRequest {
  // Required. The merchant account that owns the new notification subscription.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/NotificationSubscription"
    }
  ];

  // Required. The notification subscription to create.
  NotificationSubscription notification_subscription = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for the UpdateNotificationSubscription method.
message UpdateNotificationSubscriptionRequest {
  // Required. The new version of the notification subscription that should be
  // updated.
  NotificationSubscription notification_subscription = 1
      [(google.api.field_behavior) = REQUIRED];

  // List of fields being updated.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for the DeleteNotificationSubscription method.
message DeleteNotificationSubscriptionRequest {
  // Required. The name of the notification subscription to be deleted.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/NotificationSubscription"
    }
  ];
}

// Request message for the ListNotificationSubscription method.
message ListNotificationSubscriptionsRequest {
  // Required. The merchant account who owns the notification subscriptions.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/NotificationSubscription"
    }
  ];

  // The maximum number of notification subscriptions to return in a page.
  // The default value for `page_size` is 100. The
  // maximum value is `200`. Values above `200` will be coerced to `200`.
  int32 page_size = 2;

  // Token (if provided) to retrieve the subsequent page. All other parameters
  // must match the original call that provided the page token.
  string page_token = 3;
}

// Response message for the ListNotificationSubscription method.
message ListNotificationSubscriptionsResponse {
  // The list of notification subscriptions requested by the merchant.
  repeated NotificationSubscription notification_subscriptions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Represents a notification subscription owned by a Merchant account.
message NotificationSubscription {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/NotificationSubscription"
    pattern: "accounts/{account}/notificationsubscriptions/{notification_subscription}"
  };

  // Represents the event type that the merchant is interested in receiving
  // notifications for.
  enum NotificationEventType {
    // Notifications event type is unspecified.
    NOTIFICATION_EVENT_TYPE_UNSPECIFIED = 0;

    // Notification of product status changes, for example when product becomes
    // disapproved.
    PRODUCT_STATUS_CHANGE = 1;
  }

  oneof interested_in {
    // If this value is true, the requesting account is notified of the
    // specified event for all managed accounts (can be subaccounts or other
    // linked accounts) including newly added accounts on a daily basis.
    bool all_managed_accounts = 3;

    // The `name` of the account you want to receive notifications for.
    // Format: `accounts/{account}`
    string target_account = 4;
  }

  // Output only. The `name` of the notification configuration. Generated by the
  // Content API upon creation of a new `NotificationSubscription`. The
  // `account` represents the merchant ID of the merchant that owns the
  // configuration. Format:
  // `accounts/{account}/notificationsubscriptions/{notification_subscription}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The event that the merchant wants to be notified about.
  NotificationEventType registered_event = 2;

  // URL to be used to push the notification to the merchant.
  string call_back_uri = 5;
}

// The change that happened to the product including old value, new value,
// country code as the region code and reporting context.
message ProductChange {
  // The old value of the changed resource or attribute.
  optional string old_value = 1;

  // The new value of the changed resource or attribute.
  optional string new_value = 2;

  // Countries that have the change (if applicable)
  optional string region_code = 3;

  // Reporting contexts that have the change (if applicable)
  optional google.shopping.type.ReportingContext.ReportingContextEnum
      reporting_context = 4;
}

// The message that the merchant will receive to notify about product status
// change event
message ProductStatusChangeMessage {
  // The target account that owns the entity that changed.
  // Format : `accounts/{merchant_id}`
  optional string account = 1;

  // The account that manages the merchant's account. can be the same as
  // merchant id if it is standalone account. Format :
  // `accounts/{service_provider_id}`
  optional string managing_account = 2;

  // The resource that changed, in this case it will always be `Product`.
  optional Resource resource_type = 3;

  // The attribute in the resource that changed, in this case it will be always
  // `Status`.
  optional Attribute attribute = 4;

  // A message to describe the change that happened to the product
  repeated ProductChange changes = 5;

  // The product id.
  optional string resource_id = 6;

  // The product name.
  // Format: `{product.name=accounts/{account}/products/{product}}`
  optional string resource = 7;
}
