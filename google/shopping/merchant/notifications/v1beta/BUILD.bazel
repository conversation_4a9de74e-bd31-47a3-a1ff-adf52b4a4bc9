# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "notifications_proto",
    srcs = [
        "notificationsapi.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/shopping/type:type_proto", # Manual fix. Original :types_proto
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
    ],
)

proto_library_with_info(
    name = "notifications_proto_with_info",
    deps = [
        ":notifications_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "notifications_java_proto",
    deps = [":notifications_proto"],
)

java_grpc_library(
    name = "notifications_java_grpc",
    srcs = [":notifications_proto"],
    deps = [":notifications_java_proto"],
)

java_gapic_library(
    name = "notifications_java_gapic",
    srcs = [":notifications_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "notifications_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    test_deps = [
        ":notifications_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":notifications_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "notifications_java_gapic_test_suite",
    test_classes = [
        "com.google.shopping.merchant.notifications.v1beta.NotificationsApiServiceClientHttpJsonTest",
        "com.google.shopping.merchant.notifications.v1beta.NotificationsApiServiceClientTest",
    ],
    runtime_deps = [":notifications_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-merchant-notifications-v1beta-java",
    transport = "grpc+rest",
    deps = [
        ":notifications_java_gapic",
        ":notifications_java_grpc",
        ":notifications_java_proto",
        ":notifications_proto",
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "notifications_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/shopping/merchant/notifications/apiv1beta/notificationspb",
    protos = [":notifications_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/shopping/type:type_go_proto", # Manual fix. Original :types_go_proto
    ],
)

go_gapic_library(
    name = "notifications_go_gapic",
    srcs = [":notifications_proto_with_info"],
    grpc_service_config = "notifications_grpc_service_config.json",
    importpath = "cloud.google.com/go/shopping/merchant/notifications/apiv1beta;notifications",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":notifications_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-merchant-notifications-v1beta-go",
    deps = [
        ":notifications_go_gapic",
        ":notifications_go_gapic_srcjar-test.srcjar",
        ":notifications_go_gapic_srcjar-metadata.srcjar",
        ":notifications_go_gapic_srcjar-snippets.srcjar",
        ":notifications_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
)

py_import(
    name = "shopping_type",
    srcs = [
        "//google/shopping/type:type_py_gapic",
],
)

py_gapic_library(
    name = "notifications_py_gapic",
    srcs = [":notifications_proto"],
    grpc_service_config = "notifications_grpc_service_config.json",
    opt_args = [
        "proto-plus-deps=google.shopping.type", # Added manually
        "python-gapic-namespace=google.shopping",
        "python-gapic-name=merchant_notifications",
    ],
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
      ":shopping_type", # Added manually
    ],
)

py_test(
    name = "notifications_py_gapic_test",
    srcs = [
        "notifications_py_gapic_pytest.py",
        "notifications_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":notifications_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "merchant-notifications-v1beta-py",
    deps = [
        ":notifications_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "notifications_php_proto",
    deps = [":notifications_proto"],
)

php_gapic_library(
    name = "notifications_php_gapic",
    srcs = [":notifications_proto_with_info"],
    grpc_service_config = "notifications_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":notifications_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-merchant-notifications-v1beta-php",
    deps = [
        ":notifications_php_gapic",
        ":notifications_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "notifications_nodejs_gapic",
    package_name = "@google-shopping/notifications",
    src = ":notifications_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "notifications_grpc_service_config.json",
    package = "google.shopping.merchant.notifications.v1beta",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "merchant-notifications-v1beta-nodejs",
    deps = [
        ":notifications_nodejs_gapic",
        ":notifications_proto",
        "//google/shopping/type:type_proto" # Added manual
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "notifications_ruby_proto",
    deps = [":notifications_proto"],
)

ruby_grpc_library(
    name = "notifications_ruby_grpc",
    srcs = [":notifications_proto"],
    deps = [":notifications_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "notifications_ruby_gapic",
    srcs = [":notifications_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-shopping-merchant-notifications-v1beta",
        "ruby-cloud-extra-dependencies=google-shopping-type=>0.0+<2.a",
    ],
    grpc_service_config = "notifications_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":notifications_ruby_grpc",
        ":notifications_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-shopping-merchant-notifications-v1beta-ruby",
    deps = [
        ":notifications_ruby_gapic",
        ":notifications_ruby_grpc",
        ":notifications_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "notifications_csharp_proto",
    extra_opts = [],
    deps = [":notifications_proto"],
)

csharp_grpc_library(
    name = "notifications_csharp_grpc",
    srcs = [":notifications_proto"],
    deps = [":notifications_csharp_proto"],
)

csharp_gapic_library(
    name = "notifications_csharp_gapic",
    srcs = [":notifications_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "notifications_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":notifications_csharp_grpc",
        ":notifications_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-merchant-notifications-v1beta-csharp",
    deps = [
        ":notifications_csharp_gapic",
        ":notifications_csharp_grpc",
        ":notifications_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "notifications_cc_proto",
    deps = [":notifications_proto"],
)

cc_grpc_library(
    name = "notifications_cc_grpc",
    srcs = [":notifications_proto"],
    grpc_only = True,
    deps = [":notifications_cc_proto"],
)
