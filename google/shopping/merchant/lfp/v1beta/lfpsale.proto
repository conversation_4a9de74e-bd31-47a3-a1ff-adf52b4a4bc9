// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.lfp.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/shopping/type/types.proto";

option go_package = "cloud.google.com/go/shopping/merchant/lfp/apiv1beta/lfppb;lfppb";
option java_multiple_files = true;
option java_outer_classname = "LfpSaleProto";
option java_package = "com.google.shopping.merchant.lfp.v1beta";

// Service for a [LFP
// partner](https://support.google.com/merchants/answer/7676652) to submit sales
// data for a merchant.
service LfpSaleService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Inserts a `LfpSale` for the given merchant.
  rpc InsertLfpSale(InsertLfpSaleRequest) returns (LfpSale) {
    option (google.api.http) = {
      post: "/lfp/v1beta/{parent=accounts/*}/lfpSales:insert"
      body: "lfp_sale"
    };
  }
}

// A sale for the merchant.
message LfpSale {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/LfpSale"
    pattern: "accounts/{account}/lfpSales/{sale}"
    plural: "lfpSales"
    singular: "lfpSale"
  };

  // Output only. Identifier. The name of the `LfpSale` resource.
  // Format:
  // `accounts/{account}/lfpSales/{sale}`
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Required. The Merchant Center ID of the merchant to submit the sale for.
  int64 target_account = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The identifier of the merchant's store. Either a `storeCode`
  // inserted through the API or the code of the store in the Business Profile.
  string store_code = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. A unique identifier for the product. If both inventories and
  // sales are submitted for a merchant, this id should match for the same
  // product.
  //
  // **Note**: if the merchant sells the same product new and used, they should
  // have different IDs.
  string offer_id = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The [CLDR territory
  // code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml)
  // for the country where the product is sold.
  string region_code = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. The two-letter ISO 639-1 language code for the item.
  string content_language = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. The Global Trade Item Number of the sold product.
  string gtin = 7 [(google.api.field_behavior) = REQUIRED];

  // Required. The unit price of the product.
  google.shopping.type.Price price = 8 [(google.api.field_behavior) = REQUIRED];

  // Required. The relative change of the available quantity. Negative for items
  // returned.
  int64 quantity = 9 [(google.api.field_behavior) = REQUIRED];

  // Required. The timestamp for the sale.
  google.protobuf.Timestamp sale_time = 10
      [(google.api.field_behavior) = REQUIRED];

  // Output only. System generated globally unique ID for the `LfpSale`.
  optional string uid = 11 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. The [feed
  // label](https://developers.google.com/shopping-content/guides/products/feed-labels)
  // for the product. If this is not set, it will default to `regionCode`.
  optional string feed_label = 12 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the InsertLfpSale method.
message InsertLfpSaleRequest {
  // Required. The LFP provider account.
  // Format: `accounts/{lfp_partner}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The sale to insert.
  LfpSale lfp_sale = 2 [(google.api.field_behavior) = REQUIRED];
}
