// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.lfp.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/shopping/type/types.proto";

option go_package = "cloud.google.com/go/shopping/merchant/lfp/apiv1beta/lfppb;lfppb";
option java_multiple_files = true;
option java_outer_classname = "LfpInventoryProto";
option java_package = "com.google.shopping.merchant.lfp.v1beta";
option (google.api.resource_definition) = {
  type: "merchantapi.googleapis.com/Account"
  pattern: "accounts/{account}"
};

// Service for a [LFP
// partner](https://support.google.com/merchants/answer/7676652) to submit local
// inventories for a merchant.
service LfpInventoryService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Inserts a `LfpInventory` resource for the given target merchant account. If
  // the resource already exists, it will be replaced. The inventory
  // automatically expires after 30 days.
  rpc InsertLfpInventory(InsertLfpInventoryRequest) returns (LfpInventory) {
    option (google.api.http) = {
      post: "/lfp/v1beta/{parent=accounts/*}/lfpInventories:insert"
      body: "lfp_inventory"
    };
  }
}

// Local Inventory for the merchant.
message LfpInventory {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/LfpInventory"
    pattern: "accounts/{account}/lfpInventories/{target_merchant}~{store_code}~{offer}"
    plural: "lfpInventories"
    singular: "lfpInventory"
  };

  // Output only. Identifier. The name for the `LfpInventory` resource.
  // Format:
  // `accounts/{account}/lfpInventories/{target_merchant}~{store_code}~{offer}`
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Required. The Merchant Center ID of the merchant to submit the inventory
  // for.
  int64 target_account = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The identifier of the merchant's store. Either the store code
  // inserted through `InsertLfpStore` or the store code in the Business
  // Profile.
  string store_code = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. A unique identifier for the product. If both
  // inventories and sales are submitted for a merchant, this id should match
  // for the same product.
  //
  // **Note**: if the merchant sells the same product new and used, they should
  // have different IDs.
  string offer_id = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. The [CLDR territory
  // code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml)
  // for the country where the product is sold.
  string region_code = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. The two-letter ISO 639-1 language code for the item.
  string content_language = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. The Global Trade Item Number of the product.
  optional string gtin = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The current price of the product.
  google.shopping.type.Price price = 8 [(google.api.field_behavior) = OPTIONAL];

  // Required. Availability of the product at this store.
  // For accepted attribute values, see the [local product inventory data
  // specification](https://support.google.com/merchants/answer/3061342)
  string availability = 9 [(google.api.field_behavior) = REQUIRED];

  // Optional. Quantity of the product available at this store. Must be greater
  // than or equal to zero.
  optional int64 quantity = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The time when the inventory is collected. If not set, it will be
  // set to the time when the inventory is submitted.
  google.protobuf.Timestamp collection_time = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Supported pickup method for this offer. Unless the value is "not
  // supported", this field must be submitted together with `pickupSla`. For
  // accepted attribute values, see the [local product inventory data
  // specification](https://support.google.com/merchants/answer/3061342).
  optional string pickup_method = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Expected date that an order will be ready for pickup relative to
  // the order date. Must be submitted together with `pickupMethod`. For
  // accepted attribute values, see the [local product inventory data
  // specification](https://support.google.com/merchants/answer/3061342).
  optional string pickup_sla = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The [feed
  // label](https://developers.google.com/shopping-content/guides/products/feed-labels)
  // for the product. If this is not set, it will default to `regionCode`.
  optional string feed_label = 14 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `InsertLfpInventory` method.
message InsertLfpInventoryRequest {
  // Required. The LFP provider account.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/LfpInventory"
    }
  ];

  // Required. The inventory to insert.
  LfpInventory lfp_inventory = 2 [(google.api.field_behavior) = REQUIRED];
}
