// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.lfp.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";

option go_package = "cloud.google.com/go/shopping/merchant/lfp/apiv1beta/lfppb;lfppb";
option java_multiple_files = true;
option java_outer_classname = "LfpStoreProto";
option java_package = "com.google.shopping.merchant.lfp.v1beta";

// Service for a [LFP
// partner](https://support.google.com/merchants/answer/7676652) to submit local
// stores for a merchant.
service LfpStoreService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves information about a store.
  rpc GetLfpStore(GetLfpStoreRequest) returns (LfpStore) {
    option (google.api.http) = {
      get: "/lfp/v1beta/{name=accounts/*/lfpStores/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Inserts a store for the target merchant. If the store with the same store
  // code already exists, it will be replaced.
  rpc InsertLfpStore(InsertLfpStoreRequest) returns (LfpStore) {
    option (google.api.http) = {
      post: "/lfp/v1beta/{parent=accounts/*}/lfpStores:insert"
      body: "lfp_store"
    };
    option (google.api.method_signature) = "parent,lfp_store";
  }

  // Deletes a store for a target merchant.
  rpc DeleteLfpStore(DeleteLfpStoreRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/lfp/v1beta/{name=accounts/*/lfpStores/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the stores of the target merchant, specified by the filter in
  // `ListLfpStoresRequest`.
  rpc ListLfpStores(ListLfpStoresRequest) returns (ListLfpStoresResponse) {
    option (google.api.http) = {
      get: "/lfp/v1beta/{parent=accounts/*}/lfpStores"
    };
    option (google.api.method_signature) = "parent";
  }
}

// A store for the merchant. This will be used to match to a store under the
// Google Business Profile of the target merchant. If a matching store can't be
// found, the inventories or sales submitted with the store code will not be
// used.
message LfpStore {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/LfpStore"
    pattern: "accounts/{account}/lfpStores/{target_merchant}~{store_code}"
    plural: "lfpStores"
    singular: "lfpStore"
  };

  // The state of matching `LfpStore` to a Google Business Profile.
  enum StoreMatchingState {
    // Store matching state unspecified.
    STORE_MATCHING_STATE_UNSPECIFIED = 0;

    // The `LfpStore` is successfully matched with a Google Business Profile
    // store.
    STORE_MATCHING_STATE_MATCHED = 1;

    // The `LfpStore` is not matched with a Google Business Profile store.
    STORE_MATCHING_STATE_FAILED = 2;
  }

  // Output only. Identifier. The name of the `LfpStore` resource.
  // Format: `accounts/{account}/lfpStores/{target_merchant}~{store_code}`
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Required. The Merchant Center id of the merchant to submit the store for.
  int64 target_account = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Immutable. A store identifier that is unique for the target
  // merchant.
  string store_code = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. The street address of the store.
  // Example: 1600 Amphitheatre Pkwy, Mountain View, CA 94043, USA.
  string store_address = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. The merchant or store name.
  optional string store_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The store phone number in
  // [E.164](https://en.wikipedia.org/wiki/E.164) format. Example:
  // `+***********`
  optional string phone_number = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The website URL for the store or merchant.
  optional string website_uri = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. [Google My Business category
  // id](https://gcid-explorer.corp.google.com/static/gcid.html).
  repeated string gcid_category = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The [Google Place
  // Id](https://developers.google.com/maps/documentation/places/web-service/place-id#id-overview)
  // of the store location.
  optional string place_id = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Output only. The state of matching to a Google Business Profile.
  // See
  // [matchingStateHint][google.shopping.merchant.lfp.v1beta.LfpStore.matching_state_hint]
  // for further details if no match is found.
  StoreMatchingState matching_state = 10 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Optional. Output only. The hint of why the matching has failed. This is
  // only set when
  // [matchingState][google.shopping.merchant.lfp.v1beta.LfpStore.matching_state]=`STORE_MATCHING_STATE_FAILED`.
  //
  // Possible values are:
  //
  // - "`linked-store-not-found`": There aren't any Google Business
  // Profile stores available for matching.
  // - "`store-match-not-found`": The provided `LfpStore` couldn't be matched to
  // any of the connected Google Business Profile stores. Merchant Center
  // account is connected correctly and stores are available on Google Business
  // Profile, but the `LfpStore` location address does not match with Google
  // Business Profile stores' addresses. Update the `LfpStore` address or Google
  // Business Profile store address to match correctly.
  // - "`store-match-unverified`": The provided `LfpStore` couldn't be matched
  // to any of the connected Google Business Profile stores, as the matched
  // Google Business Profile store is unverified. Go through the Google Business
  // Profile verification process to match correctly.
  optional string matching_state_hint = 11 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];
}

// Request message for the `GetLfpStore` method.
message GetLfpStoreRequest {
  // Required. The name of the store to retrieve.
  // Format: `accounts/{account}/lfpStores/{target_merchant}~{store_code}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/LfpStore"
    }
  ];
}

// Request message for the InsertLfpStore method.
message InsertLfpStoreRequest {
  // Required. The LFP provider account
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/LfpStore"
    }
  ];

  // Required. The store to insert.
  LfpStore lfp_store = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the DeleteLfpStore method.
message DeleteLfpStoreRequest {
  // Required. The name of the store to delete for the target merchant account.
  // Format: `accounts/{account}/lfpStores/{target_merchant}~{store_code}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/LfpStore"
    }
  ];
}

// Request message for the ListLfpStores method.
message ListLfpStoresRequest {
  // Required. The LFP partner.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/LfpStore"
    }
  ];

  // Required. The Merchant Center id of the merchant to list stores for.
  int64 target_account = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The maximum number of `LfpStore` resources for the given account
  // to return. The service returns fewer than this value if the number of
  // stores for the given account is less than the `pageSize`. The default value
  // is 250. The maximum value is 1000; If a value higher than the maximum is
  // specified, then the `pageSize` will default to the maximum.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListLfpStoresRequest`
  // call. Provide the page token to retrieve the subsequent page. When
  // paginating, all other parameters provided to `ListLfpStoresRequest` must
  // match the call that provided the page token. The token returned as
  // [nextPageToken][google.shopping.merchant.lfp.v1beta.ListLfpStoresResponse.next_page_token]
  // in the response to the previous request.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the ListLfpStores method.
message ListLfpStoresResponse {
  // The stores from the specified merchant.
  repeated LfpStore lfp_stores = 1;

  // A token, which can be sent as `pageToken` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
