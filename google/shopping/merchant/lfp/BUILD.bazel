# This build file includes a target for the Ruby wrapper library for
# google-shopping-merchant-lfp.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for merchant lfp.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1beta in this case.
ruby_cloud_gapic_library(
    name = "lfp_ruby_wrapper",
    srcs = ["//google/shopping/merchant/lfp/v1beta:lfp_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-shopping-merchant-lfp",
        "ruby-cloud-wrapper-of=v1beta:0.0",
    ],
    service_yaml = "//google/shopping/merchant/lfp/v1beta:merchantapi_v1beta.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-shopping-merchant-lfp-ruby",
    deps = [
        ":lfp_ruby_wrapper",
    ],
)
