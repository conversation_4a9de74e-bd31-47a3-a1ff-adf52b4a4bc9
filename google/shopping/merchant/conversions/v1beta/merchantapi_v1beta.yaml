type: google.api.Service
config_version: 3
name: merchantapi.googleapis.com
title: Merchant API

apis:
- name: google.shopping.merchant.conversions.v1beta.ConversionSourcesService

documentation:
  summary: Programmatically manage your Merchant Center accounts.

authentication:
  rules:
  - selector: 'google.shopping.merchant.conversions.v1beta.ConversionSourcesService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/content

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=171084&template=555201
  documentation_uri: https://developers.google.com/merchant/api
  api_short_name: merchantapi
  github_label: 'api: merchantapi'
  doc_tag_prefix: merchantapi
  organization: SHOPPING
  library_settings:
  - version: google.shopping.merchant.conversions.v1beta
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
