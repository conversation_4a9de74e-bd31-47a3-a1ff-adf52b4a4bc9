// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.conversions.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/shopping/merchant/conversions/apiv1beta/conversionspb;conversionspb";
option java_multiple_files = true;
option java_outer_classname = "ConversionSourcesProto";
option java_package = "com.google.shopping.merchant.conversions.v1beta";
option (google.api.resource_definition) = {
  type: "merchantapi.googleapis.com/Account"
  pattern: "accounts/{account}"
};

// Service for managing conversion sources for a merchant account.
service ConversionSourcesService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Creates a new conversion source.
  rpc CreateConversionSource(CreateConversionSourceRequest)
      returns (ConversionSource) {
    option (google.api.http) = {
      post: "/conversions/v1beta/{parent=accounts/*}/conversionSources"
      body: "conversion_source"
    };
    option (google.api.method_signature) = "parent,conversion_source";
  }

  // Updates information of an existing conversion source. Available only for
  // Merchant Center Destination conversion sources.
  rpc UpdateConversionSource(UpdateConversionSourceRequest)
      returns (ConversionSource) {
    option (google.api.http) = {
      patch: "/conversions/v1beta/{conversion_source.name=accounts/*/conversionSources/*}"
      body: "conversion_source"
    };
    option (google.api.method_signature) = "conversion_source,update_mask";
  }

  // Archives an existing conversion source. If the conversion source is a
  // Merchant Center Destination, it will be recoverable for 30 days. If the
  // conversion source is a Google Analytics Link, it will be deleted
  // immediately and can be restored by creating a new one.
  rpc DeleteConversionSource(DeleteConversionSourceRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/conversions/v1beta/{name=accounts/*/conversionSources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Re-enables an archived conversion source. Only Available for Merchant
  // Center Destination conversion sources.
  rpc UndeleteConversionSource(UndeleteConversionSourceRequest)
      returns (ConversionSource) {
    option (google.api.http) = {
      post: "/conversions/v1beta/{name=accounts/*/conversionSources/*}:undelete"
      body: "*"
    };
  }

  // Fetches a conversion source.
  rpc GetConversionSource(GetConversionSourceRequest)
      returns (ConversionSource) {
    option (google.api.http) = {
      get: "/conversions/v1beta/{name=accounts/*/conversionSources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves the list of conversion sources the caller has access to.
  rpc ListConversionSources(ListConversionSourcesRequest)
      returns (ListConversionSourcesResponse) {
    option (google.api.http) = {
      get: "/conversions/v1beta/{parent=accounts/*}/conversionSources"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Represents a conversion source owned by a Merchant account. A merchant
// account can have up to 200 conversion sources.
message ConversionSource {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/ConversionSource"
    pattern: "accounts/{account}/conversionSources/{conversion_source}"
    plural: "conversionSources"
    singular: "conversionSource"
  };

  // Represents state of the conversion source.
  enum State {
    // Conversion source has unspecified state.
    STATE_UNSPECIFIED = 0;

    // Conversion source is fully functional.
    ACTIVE = 1;

    // Conversion source has been archived in the last 30 days and not
    // currently functional. Can be restored using the undelete method.
    ARCHIVED = 2;

    // Conversion source creation has started but not fully finished yet.
    PENDING = 3;
  }

  // Entity controlling the conversion source.
  enum Controller {
    // Default value. This value is unused.
    CONTROLLER_UNSPECIFIED = 0;

    // Controlled by the Merchant who owns the Conversion Source.
    MERCHANT = 1;

    // Controlled by the YT Affiliates program.
    YOUTUBE_AFFILIATES = 2;
  }

  // Required.
  // Conversion source data specific to each different type of source.
  oneof source_data {
    // Immutable. Conversion Source of type "Link to Google Analytics Property".
    GoogleAnalyticsLink google_analytics_link = 3
        [(google.api.field_behavior) = IMMUTABLE];

    // Conversion Source of type "Merchant Center Tag Destination".
    MerchantCenterDestination merchant_center_destination = 4;
  }

  // Output only. Identifier. Generated by the Content API upon creation of a
  // new `ConversionSource`. Format: [a-z]{4}:.+ The four characters before the
  // colon represent the type of conversio source. Content after the colon
  // represents the ID of the conversion source within that type. The ID of two
  // different conversion sources might be the same across different types. The
  // following type prefixes are supported:
  // - galk: For GoogleAnalyticsLink sources.
  // - mcdn: For MerchantCenterDestination sources.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Output only. Current state of this conversion source. Can't be edited
  // through the API.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when an archived conversion source becomes
  // permanently deleted and is no longer available to undelete.
  google.protobuf.Timestamp expire_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Controller of the conversion source.
  Controller controller = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents attribution settings for conversion sources receiving
// pre-attribution data.
message AttributionSettings {
  // Message representing a types of conversion events
  message ConversionType {
    // Output only. Conversion event name, as it'll be reported by the client.
    string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Option indicating if the type should be included in Merchant
    // Center reporting.
    bool report = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The attribution model used for this source. We support the same set of
  // models offered by Google Analytics 4, as described in:
  // https://support.google.com/analytics/answer/10596866.
  enum AttributionModel {
    // Unspecified model.
    ATTRIBUTION_MODEL_UNSPECIFIED = 0;

    // Cross-channel Last Click model.
    CROSS_CHANNEL_LAST_CLICK = 1;

    // Ads-preferred Last Click model.
    ADS_PREFERRED_LAST_CLICK = 2;

    // Cross-channel Data Driven model.
    CROSS_CHANNEL_DATA_DRIVEN = 5;

    // Cross-channel First Click model.
    CROSS_CHANNEL_FIRST_CLICK = 6;

    // Cross-channel Linear model.
    CROSS_CHANNEL_LINEAR = 7;

    // Cross-channel Position Based model.
    CROSS_CHANNEL_POSITION_BASED = 8;

    // Cross-channel Time Decay model.
    CROSS_CHANNEL_TIME_DECAY = 9;
  }

  // Required. Lookback windows (in days) used for attribution in this source.
  // Supported values are 7, 30, 40.
  int32 attribution_lookback_window_days = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Attribution model.
  AttributionModel attribution_model = 2
      [(google.api.field_behavior) = REQUIRED];

  // Immutable. Unordered list. List of different conversion types a conversion
  // event can be classified as. A standard "purchase" type will be
  // automatically created if this list is empty at creation time.
  repeated ConversionType conversion_type = 3 [
    (google.api.field_behavior) = UNORDERED_LIST,
    (google.api.field_behavior) = IMMUTABLE
  ];
}

// "Google Analytics Link" sources can be used to get conversion data from an
// existing Google Analytics property into the linked Merchant Center account.
message GoogleAnalyticsLink {
  // Required. Immutable. ID of the Google Analytics property the merchant is
  // linked to.
  int64 property_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. Attribution settings for the linked Google Analytics property.
  AttributionSettings attribution_settings = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the Google Analytics property the merchant is linked
  // to.
  string property = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// "Merchant Center Destination" sources can be used to send conversion events
// from an online store using a Google tag directly to a Merchant Center account
// where the source is created.
message MerchantCenterDestination {
  // Output only. Merchant Center Destination ID.
  string destination = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Attribution settings being used for the Merchant Center
  // Destination.
  AttributionSettings attribution_settings = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. Merchant-specified display name for the destination. This is the
  // name that identifies the conversion source within the Merchant Center UI.
  // Limited to 64 characters.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Three-letter currency code (ISO 4217). The currency code defines
  // in which currency the conversions sent to this destination will be reported
  // in Merchant Center.
  string currency_code = 4 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the CreateConversionSource method.
message CreateConversionSourceRequest {
  // Required. The merchant account that will own the new conversion source.
  // Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/ConversionSource"
    }
  ];

  // Required. The conversion source description. A new ID will be automatically
  // assigned to it upon creation.
  ConversionSource conversion_source = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for the UpdateConversionSource method.
message UpdateConversionSourceRequest {
  // Required. The new version of the conversion source data.
  // Format: accounts/{account}/conversionSources/{conversion_source}
  ConversionSource conversion_source = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields being updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for the DeleteConversionSource method.
message DeleteConversionSourceRequest {
  // Required. The name of the conversion source to be deleted.
  // Format: accounts/{account}/conversionSources/{conversion_source}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/ConversionSource"
    }
  ];
}

// Request message for the UndeleteConversionSource method.
message UndeleteConversionSourceRequest {
  // Required. The name of the conversion source to be undeleted.
  // Format: accounts/{account}/conversionSources/{conversion_source}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/ConversionSource"
    }
  ];
}

// Request message for the GetConversionSource method.
message GetConversionSourceRequest {
  // Required. The name of the conversion source to be fetched.
  // Format: accounts/{account}/conversionsources/{conversion_source}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/ConversionSource"
    }
  ];
}

// Request message for the ListConversionSources method.
message ListConversionSourcesRequest {
  // Required. The merchant account who owns the collection of conversion
  // sources. Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/ConversionSource"
    }
  ];

  // Optional. The maximum number of conversion sources to return in a page.
  // If no `page_size` is specified, `100` is used as the default value. The
  // maximum value is `200`. Values above `200` will be coerced to `200`.
  // Regardless of pagination, at most `200` conversion sources are returned
  // in total.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Show deleted (archived) option.
  bool show_deleted = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the ListConversionSources method.
message ListConversionSourcesResponse {
  // List of conversion sources.
  repeated ConversionSource conversion_sources = 1;

  // Token to be used to fetch the next results page.
  string next_page_token = 2;
}
