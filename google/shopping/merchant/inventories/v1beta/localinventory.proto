// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.inventories.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/shopping/type/types.proto";
import "google/type/interval.proto";

option go_package = "cloud.google.com/go/shopping/merchant/inventories/apiv1beta/inventoriespb;inventoriespb";
option java_multiple_files = true;
option java_outer_classname = "LocalInventoryProto";
option java_package = "com.google.shopping.merchant.inventories.v1beta";
option (google.api.resource_definition) = {
  type: "merchantapi.googleapis.com/Product"
  pattern: "accounts/{account}/products/{product}"
};

// Service to manage local inventory for products
service LocalInventoryService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Lists the `LocalInventory` resources for the given product in your merchant
  // account. The response might contain fewer items than specified by
  // `pageSize`. If `pageToken` was returned in previous request, it can be used
  // to obtain additional results.
  //
  // `LocalInventory` resources are listed per product for a given account.
  rpc ListLocalInventories(ListLocalInventoriesRequest)
      returns (ListLocalInventoriesResponse) {
    option (google.api.http) = {
      get: "/inventories/v1beta/{parent=accounts/*/products/*}/localInventories"
    };
    option (google.api.method_signature) = "parent";
  }

  // Inserts a `LocalInventory` resource to a product in your merchant
  // account.
  //
  // Replaces the full `LocalInventory` resource if an entry with the same
  // [`storeCode`][google.shopping.merchant.inventories.v1beta.LocalInventory.store_code]
  // already exists for the product.
  //
  // It might take up to 30 minutes for the new or updated `LocalInventory`
  // resource to appear in products.
  rpc InsertLocalInventory(InsertLocalInventoryRequest)
      returns (LocalInventory) {
    option (google.api.http) = {
      post: "/inventories/v1beta/{parent=accounts/*/products/*}/localInventories:insert"
      body: "local_inventory"
    };
  }

  // Deletes the specified `LocalInventory` from the given product in your
  // merchant account. It might take a up to an hour for the
  // `LocalInventory` to be deleted from the specific product.
  // Once you have received a successful delete response, wait for that
  // period before attempting a delete again.
  rpc DeleteLocalInventory(DeleteLocalInventoryRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/inventories/v1beta/{name=accounts/*/products/*/localInventories/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Local inventory information for the product. Represents in-store information
// for a specific product at the store specified by
// [`storeCode`][google.shopping.merchant.inventories.v1beta.LocalInventory.store_code].
// For a list of all accepted attribute values, see the [local product inventory
// data specification](https://support.google.com/merchants/answer/3061342).
message LocalInventory {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/LocalInventory"
    pattern: "accounts/{account}/products/{product}/localInventories/{store_code}"
  };

  // Output only. The name of the `LocalInventory` resource.
  // Format:
  // `accounts/{account}/products/{product}/localInventories/{store_code}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The account that owns the product. This field will be ignored
  // if set by the client.
  int64 account = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. Store code (the store ID from your Business Profile)
  // of the physical store the product is sold in. See the [Local product
  // inventory data
  // specification](https://support.google.com/merchants/answer/3061342) for
  // more information.
  string store_code = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Price of the product at this store.
  google.shopping.type.Price price = 4;

  // Sale price of the product at this store. Mandatory if
  // [`salePriceEffectiveDate`][google.shopping.merchant.inventories.v1beta.LocalInventory.sale_price_effective_date]
  // is defined.
  google.shopping.type.Price sale_price = 5;

  // The `TimePeriod` of the
  // sale at this store.
  google.type.Interval sale_price_effective_date = 6;

  // Availability of the product at this store.
  // For accepted attribute values, see the [local product inventory data
  // specification](https://support.google.com/merchants/answer/3061342)
  optional string availability = 7;

  // Quantity of the product available at this store. Must be greater than or
  // equal to zero.
  optional int64 quantity = 8;

  // Supported pickup method for this product. Unless the value is `"not
  // supported"`, this field must be submitted together with
  // `pickupSla`.
  // For accepted attribute values, see the [local product inventory data
  // specification](https://support.google.com/merchants/answer/3061342)
  optional string pickup_method = 9;

  // Relative time period from the order date for an order for this product,
  // from this store, to be ready for pickup. Must be submitted with
  // `pickupMethod`.
  // For accepted attribute values, see the [local product inventory data
  // specification](https://support.google.com/merchants/answer/3061342)
  optional string pickup_sla = 10;

  // Location of the product inside the store. Maximum length is 20 bytes.
  optional string instore_product_location = 11;

  // A list of custom (merchant-provided) attributes. You can also use
  // `CustomAttribute` to submit any attribute of the data specification in its
  // generic form.
  repeated google.shopping.type.CustomAttribute custom_attributes = 12;
}

// Request message for the `ListLocalInventories` method.
message ListLocalInventoriesRequest {
  // Required. The `name` of the parent product to list local inventories for.
  // Format:
  // `accounts/{account}/products/{product}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/LocalInventory"
    }
  ];

  // The maximum number of `LocalInventory` resources for the given
  // product to return. The service returns fewer than this value if the number
  // of inventories for the given product is less that than the `pageSize`. The
  // default value is 25000. The maximum value is 25000; If a value higher than
  // the maximum is specified, then the `pageSize` will default to the maximum
  int32 page_size = 2;

  // A page token, received from a previous `ListLocalInventories` call.
  // Provide the page token to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListLocalInventories`
  // must match the call that provided the page token. The token returned as
  // [nextPageToken][google.shopping.merchant.inventories.v1beta.ListLocalInventoriesResponse.next_page_token]
  // in the response to the previous request.
  string page_token = 3;
}

// Response message for the `ListLocalInventories` method.
message ListLocalInventoriesResponse {
  // The `LocalInventory` resources for the given product from the specified
  // account.
  repeated LocalInventory local_inventories = 1;

  // A token, which can be sent as `pageToken` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for the `InsertLocalInventory` method.
message InsertLocalInventoryRequest {
  // Required. The account and product where this inventory will be inserted.
  // Format: `accounts/{account}/products/{product}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/LocalInventory"
    }
  ];

  // Required. Local inventory information of the product. If the product
  // already has a `LocalInventory` resource for the same `storeCode`, full
  // replacement of the `LocalInventory` resource is performed.
  LocalInventory local_inventory = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `DeleteLocalInventory` method.
message DeleteLocalInventoryRequest {
  // Required. The name of the local inventory for the given product to delete.
  // Format:
  // `accounts/{account}/products/{product}/localInventories/{store_code}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/LocalInventory"
    }
  ];
}
