// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.inventories.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/shopping/type/types.proto";
import "google/type/interval.proto";

option go_package = "cloud.google.com/go/shopping/merchant/inventories/apiv1beta/inventoriespb;inventoriespb";
option java_multiple_files = true;
option java_outer_classname = "RegionalInventoryProto";
option java_package = "com.google.shopping.merchant.inventories.v1beta";

// Service to manage regional inventory for products. There is also separate
// `regions` resource and API to manage regions definitions.
service RegionalInventoryService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Lists the `RegionalInventory` resources for the given product in your
  // merchant account. The response might contain fewer items than specified by
  // `pageSize`.  If `pageToken` was returned in previous request, it can be
  // used to obtain additional results.
  //
  // `RegionalInventory` resources are listed per product for a given account.
  rpc ListRegionalInventories(ListRegionalInventoriesRequest)
      returns (ListRegionalInventoriesResponse) {
    option (google.api.http) = {
      get: "/inventories/v1beta/{parent=accounts/*/products/*}/regionalInventories"
    };
    option (google.api.method_signature) = "parent";
  }

  // Inserts a `RegionalInventory` to a given product in your
  // merchant account.
  //
  // Replaces the full `RegionalInventory` resource if an entry with the same
  // [`region`][google.shopping.merchant.inventories.v1beta.RegionalInventory.region]
  // already exists for the product.
  //
  // It might take up to 30 minutes for the new or updated `RegionalInventory`
  // resource to appear in products.
  rpc InsertRegionalInventory(InsertRegionalInventoryRequest)
      returns (RegionalInventory) {
    option (google.api.http) = {
      post: "/inventories/v1beta/{parent=accounts/*/products/*}/regionalInventories:insert"
      body: "regional_inventory"
    };
  }

  // Deletes the specified `RegionalInventory` resource from the given product
  // in your merchant account.  It might take up to an hour for the
  // `RegionalInventory` to be deleted from the specific product.
  // Once you have received a successful delete response, wait for that
  // period before attempting a delete again.
  rpc DeleteRegionalInventory(DeleteRegionalInventoryRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/inventories/v1beta/{name=accounts/*/products/*/regionalInventories/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Regional inventory information for the product. Represents specific
// information like price and availability for a given product in a specific
// [`region`][google.shopping.merchant.inventories.v1beta.RegionalInventory.region].
// For a list of all accepted attribute values, see the [regional product
// inventory data
// specification](https://support.google.com/merchants/answer/9698880).
message RegionalInventory {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/RegionalInventory"
    pattern: "accounts/{account}/products/{product}/regionalInventories/{region}"
  };

  // Output only. The name of the `RegionalInventory` resource.
  // Format:
  // `{regional_inventory.name=accounts/{account}/products/{product}/regionalInventories/{region}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The account that owns the product. This field will be ignored
  // if set by the client.
  int64 account = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Immutable. ID of the region for this
  // `RegionalInventory` resource. See the [Regional availability and
  // pricing](https://support.google.com/merchants/answer/9698880) for more
  // details.
  string region = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Price of the product in this region.
  google.shopping.type.Price price = 4;

  // Sale price of the product in this region. Mandatory if
  // [`salePriceEffectiveDate`][google.shopping.merchant.inventories.v1beta.RegionalInventory.sale_price_effective_date]
  // is defined.
  google.shopping.type.Price sale_price = 5;

  // The `TimePeriod` of the
  // sale price in this region.
  google.type.Interval sale_price_effective_date = 6;

  // Availability of the product in this region.
  // For accepted attribute values, see the [regional product inventory data
  // specification](https://support.google.com/merchants/answer/3061342)
  optional string availability = 7;

  // A list of custom (merchant-provided) attributes. You can also use
  // `CustomAttribute` to submit any attribute of the data specification in its
  // generic form.
  repeated google.shopping.type.CustomAttribute custom_attributes = 8;
}

// Request message for the `ListRegionalInventories` method.
message ListRegionalInventoriesRequest {
  // Required. The `name` of the parent product to list `RegionalInventory`
  // resources for. Format: `accounts/{account}/products/{product}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/RegionalInventory"
    }
  ];

  // The maximum number of `RegionalInventory` resources for the given product
  // to return. The service returns fewer than this value if the number of
  // inventories for the given product is less that than the `pageSize`. The
  // default value is 25000. The maximum value is 100000; If a value higher than
  // the maximum is specified, then the `pageSize` will default to the maximum.
  int32 page_size = 2;

  // A page token, received from a previous `ListRegionalInventories` call.
  // Provide the page token to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListRegionalInventories`
  // must match the call that provided the page token. The token returned as
  // [nextPageToken][google.shopping.merchant.inventories.v1beta.ListRegionalInventoriesResponse.next_page_token]
  // in the response to the previous request.
  string page_token = 3;
}

// Response message for the `ListRegionalInventories` method.
message ListRegionalInventoriesResponse {
  // The `RegionalInventory` resources for the given product from the specified
  // account.
  repeated RegionalInventory regional_inventories = 1;

  // A token, which can be sent as `pageToken` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for the `InsertRegionalInventory` method.
message InsertRegionalInventoryRequest {
  // Required. The account and product where this inventory will be inserted.
  // Format: `accounts/{account}/products/{product}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/RegionalInventory"
    }
  ];

  // Required. Regional inventory information to add to the product. If the
  // product already has a `RegionalInventory` resource for the same `region`,
  // full replacement of the `RegionalInventory` resource is performed.
  RegionalInventory regional_inventory = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `DeleteRegionalInventory` method.
message DeleteRegionalInventoryRequest {
  // Required. The name of the `RegionalInventory` resource to delete.
  // Format:
  // `accounts/{account}/products/{product}/regionalInventories/{region}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/RegionalInventory"
    }
  ];
}
