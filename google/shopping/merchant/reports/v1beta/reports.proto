// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.reports.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "google/shopping/type/types.proto";
import "google/type/date.proto";

option go_package = "cloud.google.com/go/shopping/merchant/reports/apiv1beta/reportspb;reportspb";
option java_multiple_files = true;
option java_outer_classname = "ReportsProto";
option java_package = "com.google.shopping.merchant.reports.v1beta";

// Service for retrieving reports and insights about your products, their
// performance, and their competitive environment on Google.
service ReportService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves a report defined by a search query. The response might contain
  // fewer rows than specified by `page_size`. Rely on `next_page_token` to
  // determine if there are more rows to be requested.
  rpc Search(SearchRequest) returns (SearchResponse) {
    option (google.api.http) = {
      post: "/reports/v1beta/{parent=accounts/*}/reports:search"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for the `ReportService.Search` method.
message SearchRequest {
  // Required. Id of the account making the call. Must be a standalone account
  // or an MCA subaccount. Format: accounts/{account}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Query that defines a report to be retrieved.
  //
  // For details on how to construct your query, see the Query Language
  // guide. For the full list of available tables and fields, see the Available
  // fields.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Number of `ReportRows` to retrieve in a single page. Defaults to
  // 1000. Values above 5000 are coerced to 5000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Token of the page to retrieve. If not specified, the first page
  // of results is returned. In order to request the next page of results, the
  // value obtained from `next_page_token` in the previous response should be
  // used.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `ReportService.Search` method.
message SearchResponse {
  // Rows that matched the search query.
  repeated ReportRow results = 1;

  // Token which can be sent as `page_token` to retrieve the next page. If
  // omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Result row returned from the search query.
//
// Only the message corresponding to the queried table is populated in the
// response. Within the populated message, only the fields requested explicitly
// in the query are populated.
message ReportRow {
  // Fields available for query in `product_performance_view` table.
  ProductPerformanceView product_performance_view = 1;

  // Fields available for query in `non_product_performance_view` table.
  NonProductPerformanceView non_product_performance_view = 7;

  // Fields available for query in `product_view` table.
  ProductView product_view = 2;

  // Fields available for query in `price_competitiveness_product_view` table.
  PriceCompetitivenessProductView price_competitiveness_product_view = 3;

  // Fields available for query in `price_insights_product_view` table.
  PriceInsightsProductView price_insights_product_view = 4;

  // Fields available for query in `best_sellers_product_cluster_view` table.
  BestSellersProductClusterView best_sellers_product_cluster_view = 5;

  // Fields available for query in `best_sellers_brand_view` table.
  BestSellersBrandView best_sellers_brand_view = 6;

  // Fields available for query in `competitive_visibility_competitor_view`
  // table.
  CompetitiveVisibilityCompetitorView competitive_visibility_competitor_view =
      8;

  // Fields available for query in `competitive_visibility_top_merchant_view`
  // table.
  CompetitiveVisibilityTopMerchantView
      competitive_visibility_top_merchant_view = 9;

  // Fields available for query in `competitive_visibility_benchmark_view`
  // table.
  CompetitiveVisibilityBenchmarkView competitive_visibility_benchmark_view = 10;
}

// Fields available for query in `product_performance_view` table.
//
// Product performance data for your account, including performance metrics (for
// example, `clicks`) and dimensions according to which performance metrics are
// segmented (for example, `offer_id`). Values of product dimensions, such as
// `offer_id`, reflect the state of a product at the time of the impression.
//
// Segment fields cannot be selected in queries without also selecting at least
// one metric field.
//
// Values are only set for fields requested explicitly in the request's search
// query.
message ProductPerformanceView {
  // Marketing method to which metrics apply. Segment.
  optional MarketingMethod.MarketingMethodEnum marketing_method = 1;

  // Date in the merchant timezone to which metrics apply. Segment.
  //
  // Condition on `date` is required in the `WHERE` clause.
  google.type.Date date = 2;

  // First day of the week (Monday) of the metrics date in the merchant
  // timezone. Segment.
  google.type.Date week = 3;

  // Code of the country where the customer is located at the time of the event.
  // Represented in the ISO 3166 format. Segment.
  //
  // If the customer country cannot be determined, a special 'ZZ' code is
  // returned.
  optional string customer_country_code = 4;

  // Merchant-provided id of the product. Segment.
  optional string offer_id = 5;

  // Title of the product. Segment.
  optional string title = 6;

  // Brand of the product. Segment.
  optional string brand = 7;

  // [Product category (1st
  // level)](https://developers.google.com/shopping-content/guides/reports/segmentation#category_and_product_type)
  // in Google's product taxonomy. Segment.
  optional string category_l1 = 8;

  // [Product category (2nd
  // level)](https://developers.google.com/shopping-content/guides/reports/segmentation#category_and_product_type)
  // in Google's product taxonomy. Segment.
  optional string category_l2 = 9;

  // [Product category (3rd
  // level)](https://developers.google.com/shopping-content/guides/reports/segmentation#category_and_product_type)
  // in Google's product taxonomy. Segment.
  optional string category_l3 = 10;

  // [Product category (4th
  // level)](https://developers.google.com/shopping-content/guides/reports/segmentation#category_and_product_type)
  // in Google's product taxonomy. Segment.
  optional string category_l4 = 11;

  // [Product category (5th
  // level)](https://developers.google.com/shopping-content/guides/reports/segmentation#category_and_product_type)
  // in Google's product taxonomy. Segment.
  optional string category_l5 = 12;

  // [Product type (1st
  // level)](https://developers.google.com/shopping-content/guides/reports/segmentation#category_and_product_type)
  // in merchant's own product taxonomy. Segment.
  optional string product_type_l1 = 13;

  // [Product type (2nd
  // level)](https://developers.google.com/shopping-content/guides/reports/segmentation#category_and_product_type)
  // in merchant's own product taxonomy. Segment.
  optional string product_type_l2 = 14;

  // [Product type (3rd
  // level)](https://developers.google.com/shopping-content/guides/reports/segmentation#category_and_product_type)
  // in merchant's own product taxonomy. Segment.
  optional string product_type_l3 = 15;

  // [Product type (4th
  // level)](https://developers.google.com/shopping-content/guides/reports/segmentation#category_and_product_type)
  // in merchant's own product taxonomy. Segment.
  optional string product_type_l4 = 16;

  // [Product type (5th
  // level)](https://developers.google.com/shopping-content/guides/reports/segmentation#category_and_product_type)
  // in merchant's own product taxonomy. Segment.
  optional string product_type_l5 = 17;

  // Custom label 0 for custom grouping of products. Segment.
  optional string custom_label0 = 18;

  // Custom label 1 for custom grouping of products. Segment.
  optional string custom_label1 = 19;

  // Custom label 2 for custom grouping of products. Segment.
  optional string custom_label2 = 20;

  // Custom label 3 for custom grouping of products. Segment.
  optional string custom_label3 = 21;

  // Custom label 4 for custom grouping of products. Segment.
  optional string custom_label4 = 22;

  // Number of clicks. Metric.
  optional int64 clicks = 23;

  // Number of times merchant's products are shown. Metric.
  optional int64 impressions = 24;

  // Click-through rate - the number of clicks merchant's products receive
  // (clicks) divided by the number of times the products are shown
  // (impressions). Metric.
  optional double click_through_rate = 25;

  // Number of conversions attributed to the product, reported on the conversion
  // date. Depending on the attribution model, a conversion might be distributed
  // across multiple clicks, where each click gets its own credit assigned. This
  // metric is a sum of all such credits. Metric.
  //
  // Available only for the `FREE` traffic source.
  optional double conversions = 26;

  // Value of conversions attributed to the product, reported on the conversion
  // date. Metric.
  //
  // Available only for the `FREE` traffic source.
  google.shopping.type.Price conversion_value = 27;

  // Number of conversions divided by the number of clicks, reported on the
  // impression date. Metric.
  //
  // Available only for the `FREE` traffic source.
  optional double conversion_rate = 28;
}

// Fields available for query in `product_view` table.
//
// Products in the current inventory. Products in this table are the same as in
// Products sub-API but not all product attributes from Products sub-API are
// available for query in this table. In contrast to Products sub-API, this
// table allows to filter the returned list of products by product attributes.
// To retrieve a single product by `id` or list all products, Products sub-API
// should be used.
//
// Values are only set for fields requested explicitly in the request's search
// query.
//
message ProductView {
  // Item issue associated with the product.
  message ItemIssue {
    // Issue type.
    message ItemIssueType {
      // Error code of the issue, equivalent to the `code` of [Product
      // issues](https://developers.google.com/shopping-content/guides/product-issues).
      optional string code = 1;

      // Canonical attribute name for attribute-specific issues.
      optional string canonical_attribute = 2;
    }

    // How the issue affects the serving of the product.
    message ItemIssueSeverity {
      // Issue severity per reporting context.
      message IssueSeverityPerReportingContext {
        // Reporting context the issue applies to.
        optional google.shopping.type.ReportingContext.ReportingContextEnum
            reporting_context = 1;

        // List of disapproved countries in the reporting context, represented
        // in ISO 3166 format.
        repeated string disapproved_countries = 2;

        // List of demoted countries in the reporting context, represented in
        // ISO 3166 format.
        repeated string demoted_countries = 3;
      }

      // Issue severity aggregated for all reporting contexts.
      enum AggregatedIssueSeverity {
        // Not specified.
        AGGREGATED_ISSUE_SEVERITY_UNSPECIFIED = 0;

        // Issue disapproves the product in at least one reporting context.
        DISAPPROVED = 1;

        // Issue demotes the product in all reporting contexts it affects.
        DEMOTED = 2;

        // Issue resolution is `PENDING_PROCESSING`.
        PENDING = 3;
      }

      // Issue severity per reporting context.
      repeated IssueSeverityPerReportingContext severity_per_reporting_context =
          1;

      // Aggregated severity of the issue for all reporting contexts it affects.
      //
      // **This field can be used for filtering the results.**
      optional AggregatedIssueSeverity aggregated_severity = 2;
    }

    // How to resolve the issue.
    enum ItemIssueResolution {
      // Not specified.
      ITEM_ISSUE_RESOLUTION_UNSPECIFIED = 0;

      // The merchant has to fix the issue.
      MERCHANT_ACTION = 1;

      // The issue will be resolved automatically (for example, image crawl) or
      // through a Google review. No merchant action is required now. Resolution
      // might lead to another issue (for example, if crawl fails).
      PENDING_PROCESSING = 2;
    }

    // Item issue type.
    ItemIssueType type = 1;

    // Item issue severity.
    ItemIssueSeverity severity = 2;

    // Item issue resolution.
    optional ItemIssueResolution resolution = 3;
  }

  // Status of the product aggregated for all reporting contexts.
  //
  // Here's an example of how the aggregated status is computed:
  //
  // Free listings | Shopping ads | Status
  // --------------|--------------|------------------------------
  // Approved      | Approved     | ELIGIBLE
  // Approved      | Pending      | ELIGIBLE
  // Approved      | Disapproved  | ELIGIBLE_LIMITED
  // Pending       | Pending      | PENDING
  // Disapproved   | Disapproved  | NOT_ELIGIBLE_OR_DISAPPROVED
  //
  //
  enum AggregatedReportingContextStatus {
    // Not specified.
    AGGREGATED_REPORTING_CONTEXT_STATUS_UNSPECIFIED = 0;

    // Product is not eligible or is disapproved for all reporting contexts.
    NOT_ELIGIBLE_OR_DISAPPROVED = 1;

    // Product's status is pending in all reporting contexts.
    PENDING = 2;

    // Product is eligible for some (but not all) reporting contexts.
    ELIGIBLE_LIMITED = 3;

    // Product is eligible for all reporting contexts.
    ELIGIBLE = 4;
  }

  // A product's [click
  // potential](https://support.google.com/merchants/answer/188488) estimates
  // its performance potential compared to highest performing products of the
  // merchant. Click potential of a product helps merchants to prioritize which
  // products to fix and helps them understand how products are performing
  // against their potential.
  enum ClickPotential {
    // Unknown predicted clicks impact.
    CLICK_POTENTIAL_UNSPECIFIED = 0;

    // Potential to receive a low number of clicks compared to the highest
    // performing products of the merchant.
    LOW = 1;

    // Potential to receive a moderate number of clicks compared to the highest
    // performing products of the merchant.
    MEDIUM = 2;

    // Potential to receive a similar number of clicks as the highest performing
    // products of the merchant.
    HIGH = 3;
  }

  // REST ID of the product, in the form of
  // `channel~languageCode~feedLabel~offerId`. Merchant API methods that operate
  // on products take this as their `name` parameter.
  //
  // Required in the `SELECT` clause.
  optional string id = 1;

  // Channel of the product. Can be `ONLINE` or `LOCAL`.
  optional google.shopping.type.Channel.ChannelEnum channel = 28;

  // Language code of the product in BCP 47 format.
  optional string language_code = 2;

  // Feed label of the product.
  optional string feed_label = 3;

  // Merchant-provided id of the product.
  optional string offer_id = 4;

  // Title of the product.
  optional string title = 5;

  // Brand of the product.
  optional string brand = 6;

  // Product category (1st level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l1 = 7;

  // Product category (2nd level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l2 = 8;

  // Product category (3rd level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l3 = 9;

  // Product category (4th level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l4 = 10;

  // Product category (5th level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l5 = 11;

  // Product type (1st level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l1 = 12;

  // Product type (2nd level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l2 = 13;

  // Product type (3rd level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l3 = 14;

  // Product type (4th level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l4 = 15;

  // Product type (5th level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l5 = 16;

  // Product price. Absent if the information about the price of the product is
  // not available.
  google.shopping.type.Price price = 17;

  // [Condition](https://support.google.com/merchants/answer/6324469) of the
  // product.
  optional string condition = 18;

  // [Availability](https://support.google.com/merchants/answer/6324448) of the
  // product.
  optional string availability = 19;

  // Normalized [shipping
  // label](https://support.google.com/merchants/answer/6324504) specified in
  // the data source.
  optional string shipping_label = 20;

  // List of Global Trade Item Numbers (GTINs) of the product.
  repeated string gtin = 21;

  // Item group id provided by the merchant for grouping variants together.
  optional string item_group_id = 22;

  // Link to the processed image of the product, hosted on the Google
  // infrastructure.
  optional string thumbnail_link = 23;

  // The time the merchant created the product in timestamp seconds.
  google.protobuf.Timestamp creation_time = 24;

  // Expiration date for the product, specified on insertion.
  google.type.Date expiration_date = 25;

  // Aggregated status.
  optional AggregatedReportingContextStatus
      aggregated_reporting_context_status = 26;

  // List of item issues for the product.
  //
  // **This field cannot be used for sorting the results.**
  //
  // **Only selected attributes of this field (for example,
  // `item_issues.severity.aggregated_severity`) can be used for filtering the
  // results.**
  repeated ItemIssue item_issues = 27;

  // Estimated performance potential compared to highest performing products of
  // the merchant.
  ClickPotential click_potential = 29;

  // Rank of the product based on its click potential. A product with
  // `click_potential_rank` 1 has the highest click potential among the
  // merchant's products that fulfill the search query conditions.
  optional int64 click_potential_rank = 30;
}

// Fields available for query in `price_competitiveness_product_view` table.
//
// [Price competitiveness](https://support.google.com/merchants/answer/9626903)
// report.
//
// Values are only set for fields requested explicitly in the request's search
// query.
message PriceCompetitivenessProductView {
  // Country of the price benchmark. Represented in the ISO 3166 format.
  //
  // Required in the `SELECT` clause.
  optional string report_country_code = 1;

  // REST ID of the product, in the form of
  // `channel~languageCode~feedLabel~offerId`. Can be used to join data with the
  // `product_view` table.
  //
  // Required in the `SELECT` clause.
  optional string id = 2;

  // Merchant-provided id of the product.
  optional string offer_id = 3;

  // Title of the product.
  optional string title = 4;

  // Brand of the product.
  optional string brand = 5;

  // Product category (1st level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l1 = 6;

  // Product category (2nd level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l2 = 7;

  // Product category (3rd level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l3 = 8;

  // Product category (4th level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l4 = 9;

  // Product category (5th level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l5 = 10;

  // Product type (1st level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l1 = 11;

  // Product type (2nd level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l2 = 12;

  // Product type (3rd level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l3 = 13;

  // Product type (4th level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l4 = 14;

  // Product type (5th level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l5 = 15;

  // Current price of the product.
  google.shopping.type.Price price = 16;

  // Latest available price benchmark for the product's catalog in the benchmark
  // country.
  google.shopping.type.Price benchmark_price = 17;
}

// Fields available for query in `price_insights_product_view` table.
//
// [Price insights](https://support.google.com/merchants/answer/11916926)
// report.
//
// Values are only set for fields requested explicitly in the request's search
// query.
message PriceInsightsProductView {
  // Predicted effectiveness bucket.
  //
  // Effectiveness indicates which products would benefit most from price
  // changes. This rating takes into consideration the performance boost
  // predicted by adjusting the sale price and the difference between your
  // current price and the suggested price. Price suggestions with `HIGH`
  // effectiveness are predicted to drive the largest increase in performance.
  enum Effectiveness {
    // Effectiveness is unknown.
    EFFECTIVENESS_UNSPECIFIED = 0;

    // Effectiveness is low.
    LOW = 1;

    // Effectiveness is medium.
    MEDIUM = 2;

    // Effectiveness is high.
    HIGH = 3;
  }

  // REST ID of the product, in the form of
  // `channel~languageCode~feedLabel~offerId`. Can be used to join data with the
  // `product_view` table.
  //
  // Required in the `SELECT` clause.
  optional string id = 1;

  // Merchant-provided id of the product.
  optional string offer_id = 2;

  // Title of the product.
  optional string title = 3;

  // Brand of the product.
  optional string brand = 4;

  // Product category (1st level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l1 = 5;

  // Product category (2nd level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l2 = 6;

  // Product category (3rd level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l3 = 7;

  // Product category (4th level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l4 = 8;

  // Product category (5th level) in [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l5 = 9;

  // Product type (1st level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l1 = 10;

  // Product type (2nd level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l2 = 11;

  // Product type (3rd level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l3 = 12;

  // Product type (4th level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l4 = 13;

  // Product type (5th level) in merchant's own [product
  // taxonomy](https://support.google.com/merchants/answer/6324406).
  optional string product_type_l5 = 14;

  // Current price of the product.
  google.shopping.type.Price price = 15;

  // Latest suggested price for the product.
  google.shopping.type.Price suggested_price = 16;

  // Predicted change in impressions as a fraction after introducing the
  // suggested price compared to current active price. For example, 0.05 is a 5%
  // predicted increase in impressions.
  optional double predicted_impressions_change_fraction = 17;

  // Predicted change in clicks as a fraction after introducing the
  // suggested price compared to current active price. For example, 0.05 is a 5%
  // predicted increase in clicks.
  optional double predicted_clicks_change_fraction = 18;

  // Predicted change in conversions as a fraction after introducing the
  // suggested price compared to current active price. For example, 0.05 is a 5%
  // predicted increase in conversions).
  optional double predicted_conversions_change_fraction = 19;

  // The predicted effectiveness of applying the price suggestion, bucketed.
  Effectiveness effectiveness = 22;
}

// Fields available for query in `best_sellers_product_cluster_view` table.
//
// [Best sellers](https://support.google.com/merchants/answer/9488679) report
// with top product clusters. A product cluster is a grouping for different
// offers and variants that represent the same product, for example, Google
// Pixel 7.
//
// Values are only set for fields requested explicitly in the request's search
// query.
message BestSellersProductClusterView {
  // Status of the product cluster or brand in your inventory.
  enum InventoryStatus {
    // Not specified.
    INVENTORY_STATUS_UNSPECIFIED = 0;

    // You have a product for this product cluster or brand in stock.
    IN_STOCK = 1;

    // You have a product for this product cluster or brand in inventory but it
    // is currently out of stock.
    OUT_OF_STOCK = 2;

    // You do not have a product for this product cluster or brand in inventory.
    NOT_IN_INVENTORY = 3;
  }

  // Report date. The value of this field can only be one of the following:
  //
  // *   The first day of the week (Monday) for weekly reports,
  // *   The first day of the month for monthly reports.
  //
  // Required in the `SELECT` clause. If a `WHERE` condition on `report_date` is
  // not specified in the query, the latest available weekly or monthly report
  // is returned.
  google.type.Date report_date = 1;

  // Granularity of the report. The ranking can be done over a week or a month
  // timeframe.
  //
  // Required in the `SELECT` clause. Condition on `report_granularity` is
  // required in the `WHERE` clause.
  optional ReportGranularity.ReportGranularityEnum report_granularity = 2;

  // Country where the ranking is calculated. Represented in the ISO 3166
  // format.
  //
  // Required in the `SELECT` clause. Condition on `report_country_code` is
  // required in the `WHERE` clause.
  optional string report_country_code = 3;

  // Google product category ID to calculate the ranking for, represented in
  // [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  //
  // Required in the `SELECT` clause. If a `WHERE` condition on
  // `report_category_id` is not specified in the query, rankings for all
  // top-level categories are returned.
  optional int64 report_category_id = 4;

  // Title of the product cluster.
  optional string title = 6;

  // Brand of the product cluster.
  optional string brand = 7;

  // Product category (1st level) of the product cluster, represented in
  // [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l1 = 8;

  // Product category (2nd level) of the product cluster, represented in
  // [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l2 = 9;

  // Product category (3rd level) of the product cluster, represented in
  // [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l3 = 10;

  // Product category (4th level) of the product cluster, represented in
  // [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l4 = 11;

  // Product category (5th level) of the product cluster, represented in
  // [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  optional string category_l5 = 12;

  // GTINs of example variants of the product cluster.
  repeated string variant_gtins = 13;

  // Whether the product cluster is `IN_STOCK` in your product data source in at
  // least one of the countries, `OUT_OF_STOCK` in your product data source in
  // all countries, or `NOT_IN_INVENTORY` at all.
  //
  // The field doesn't take the Best sellers report country filter into account.
  optional InventoryStatus inventory_status = 14;

  // Whether there is at least one product of the brand currently `IN_STOCK` in
  // your product data source in at least one of the countries, all products are
  // `OUT_OF_STOCK` in your product data source in all countries, or
  // `NOT_IN_INVENTORY`.
  //
  // The field doesn't take the Best sellers report country filter into account.
  optional InventoryStatus brand_inventory_status = 15;

  // Popularity of the product cluster on Ads and organic surfaces, in the
  // selected category and country, based on the estimated number of units sold.
  optional int64 rank = 16;

  // Popularity rank in the previous week or month.
  optional int64 previous_rank = 17;

  // Estimated demand in relation to the product cluster with the highest
  // popularity rank in the same category and country.
  optional RelativeDemand.RelativeDemandEnum relative_demand = 18;

  // Estimated demand in relation to the product cluster with the highest
  // popularity rank in the same category and country in the previous week or
  // month.
  optional RelativeDemand.RelativeDemandEnum previous_relative_demand = 19;

  // Change in the estimated demand. Whether it rose, sank or remained flat.
  optional RelativeDemandChangeType.RelativeDemandChangeTypeEnum
      relative_demand_change = 20;
}

// Fields available for query in `best_sellers_brand_view` table.
//
// [Best sellers](https://support.google.com/merchants/answer/9488679) report
// with top brands.
//
// Values are only set for fields requested explicitly in the request's search
// query.
message BestSellersBrandView {
  // Report date. The value of this field can only be one of the following:
  //
  // *   The first day of the week (Monday) for weekly reports,
  // *   The first day of the month for monthly reports.
  //
  // Required in the `SELECT` clause. If a `WHERE` condition on `report_date` is
  // not specified in the query, the latest available weekly or monthly report
  // is returned.
  google.type.Date report_date = 1;

  // Granularity of the report. The ranking can be done over a week or a month
  // timeframe.
  //
  // Required in the `SELECT` clause. Condition on `report_granularity` is
  // required in the `WHERE` clause.
  optional ReportGranularity.ReportGranularityEnum report_granularity = 2;

  // Country where the ranking is calculated. Represented in the ISO 3166
  // format.
  //
  // Required in the `SELECT` clause. Condition on `report_country_code` is
  // required in the `WHERE` clause.
  optional string report_country_code = 3;

  // Google product category ID to calculate the ranking for, represented in
  // [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  //
  // Required in the `SELECT` clause. If a `WHERE` condition on
  // `report_category_id` is not specified in the query, rankings for all
  // top-level categories are returned.
  optional int64 report_category_id = 4;

  // Name of the brand.
  optional string brand = 6;

  // Popularity of the brand on Ads and organic surfaces, in the selected
  // category and country, based on the estimated number of units sold.
  optional int64 rank = 7;

  // Popularity rank in the previous week or month.
  optional int64 previous_rank = 8;

  // Estimated demand in relation to the brand with the highest popularity rank
  // in the same category and country.
  optional RelativeDemand.RelativeDemandEnum relative_demand = 9;

  // Estimated demand in relation to the brand with the highest popularity rank
  // in the same category and country in the previous week or month.
  optional RelativeDemand.RelativeDemandEnum previous_relative_demand = 10;

  // Change in the estimated demand. Whether it rose, sank or remained flat.
  optional RelativeDemandChangeType.RelativeDemandChangeTypeEnum
      relative_demand_change = 11;
}

// Fields available for query in `non_product_performance_view` table.
//
// Performance data on images and online store links leading to your non-product
// pages. This includes performance metrics (for example, `clicks`)
// and dimensions according to which performance metrics are segmented (for
// example, `date`).
//
// Segment fields cannot be selected in queries without also selecting at least
// one metric field.
//
// Values are only set for fields requested explicitly in the request's search
// query.
message NonProductPerformanceView {
  // Date in the merchant timezone to which metrics apply. Segment.
  //
  // Condition on `date` is required in the `WHERE` clause.
  google.type.Date date = 1;

  // First day of the week (Monday) of the metrics date in the merchant
  // timezone. Segment.
  google.type.Date week = 2;

  // Number of clicks on images and online store links leading to your
  // non-product pages. Metric.
  optional int64 clicks = 3;

  // Number of times images and online store links leading to your non-product
  // pages were shown. Metric.
  optional int64 impressions = 4;

  // Click-through rate - the number of clicks (`clicks`) divided by the number
  // of impressions (`impressions`) of images and online store links leading to
  // your non-product pages. Metric.
  optional double click_through_rate = 5;
}

// Fields available for query in `competitive_visibility_competitor_view` table.
//
// [Competitive
// visibility](https://support.google.com/merchants/answer/11366442) report with
// businesses with similar visibility.
//
// Values are only set for fields requested explicitly in the request's search
// query.
message CompetitiveVisibilityCompetitorView {
  // Date of this row.
  //
  // A condition on `date` is required in the `WHERE` clause.
  google.type.Date date = 1;

  // Domain of your competitor or your domain, if 'is_your_domain' is true.
  //
  // Required in the `SELECT` clause. Cannot be filtered on in the 'WHERE'
  // clause.
  optional string domain = 2;

  // True if this row contains data for your domain.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional bool is_your_domain = 3;

  // Country where impressions appeared.
  //
  // Required in the `SELECT` clause. A condition on `report_country_code` is
  // required in the `WHERE` clause.
  optional string report_country_code = 4;

  // Google product category ID to calculate the report for, represented in
  // [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  //
  // Required in the `SELECT` clause. A condition on `report_category_id` is
  // required in the `WHERE` clause.
  optional int64 report_category_id = 5;

  // Traffic source of impressions.
  //
  // Required in the `SELECT` clause.
  optional TrafficSource.TrafficSourceEnum traffic_source = 6;

  // Position of the domain in the similar businesses ranking for the selected
  // keys (`date`, `report_category_id`, `report_country_code`,
  // `traffic_source`) based on impressions. 1 is the highest.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional int64 rank = 7;

  // [Ads / organic ratio]
  // (https://support.google.com/merchants/answer/11366442#zippy=%2Cads-free-ratio)
  // shows how often the domain receives impressions from Shopping ads compared
  // to organic traffic. The number is rounded and bucketed.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional double ads_organic_ratio = 8;

  // [Page overlap rate]
  // (https://support.google.com/merchants/answer/11366442#zippy=%2Cpage-overlap-rate)
  // shows how frequently competing retailers’ offers are shown together with
  // your offers on the same page.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional double page_overlap_rate = 9;

  // [Higher position rate]
  // (https://support.google.com/merchants/answer/11366442#zippy=%2Chigher-position-rate)
  // shows how often a competitor’s offer got placed in a higher position on the
  // page than your offer.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional double higher_position_rate = 10;

  // [Relative visibility]
  // (https://support.google.com/merchants/answer/11366442#zippy=%2Crelative-visibility)
  // shows how often your competitors’ offers are shown compared to your offers.
  // In other words, this is the number of displayed impressions of a competitor
  // retailer divided by the number of your displayed impressions during a
  // selected time range for a selected product category and country.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional double relative_visibility = 11;
}

// Fields available for query in `competitive_visibility_top_merchant_view`
// table.
//
// [Competitive
// visibility](https://support.google.com/merchants/answer/11366442) report with
// business with highest visibility.
//
// Values are only set for fields requested explicitly in the request's search
// query.
message CompetitiveVisibilityTopMerchantView {
  // Date of this row.
  //
  // Cannot be selected in the `SELECT` clause. A condition on `date` is
  // required in the `WHERE` clause.
  google.type.Date date = 1;

  // Domain of your competitor or your domain, if 'is_your_domain' is true.
  //
  // Required in the `SELECT` clause. Cannot be filtered on in the 'WHERE'
  // clause.
  optional string domain = 2;

  // True if this row contains data for your domain.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional bool is_your_domain = 3;

  // Country where impressions appeared.
  //
  // Required in the `SELECT` clause. A condition on `report_country_code` is
  // required in the `WHERE` clause.
  optional string report_country_code = 4;

  // Google product category ID to calculate the report for, represented in
  // [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  //
  // Required in the `SELECT` clause. A condition on `report_category_id` is
  // required in the `WHERE` clause.
  optional int64 report_category_id = 5;

  // Traffic source of impressions.
  //
  // Required in the `SELECT` clause.
  optional TrafficSource.TrafficSourceEnum traffic_source = 6;

  // Position of the domain in the top merchants ranking for the selected keys
  // (`date`, `report_category_id`, `report_country_code`, `traffic_source`)
  // based on impressions. 1 is the highest.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional int64 rank = 7;

  // [Ads / organic ratio]
  // (https://support.google.com/merchants/answer/11366442#zippy=%2Cads-free-ratio)
  // shows how often the domain receives impressions from Shopping ads compared
  // to organic traffic. The number is rounded and bucketed.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional double ads_organic_ratio = 8;

  // [Page overlap rate]
  // (https://support.google.com/merchants/answer/11366442#zippy=%2Cpage-overlap-rate)
  // shows how frequently competing retailers’ offers are shown together with
  // your offers on the same page.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional double page_overlap_rate = 9;

  // [Higher position rate]
  // (https://support.google.com/merchants/answer/11366442#zippy=%2Chigher-position-rate)
  // shows how often a competitor’s offer got placed in a higher position on the
  // page than your offer.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional double higher_position_rate = 10;
}

// Fields available for query in `competitive_visibility_benchmark_view` table.
//
// [Competitive
// visibility](https://support.google.com/merchants/answer/11366442) report with
// the category benchmark.
//
// Values are only set for fields requested explicitly in the request's search
// query.
message CompetitiveVisibilityBenchmarkView {
  // Date of this row.
  //
  // Required in the `SELECT` clause. A condition on `date` is required in the
  // `WHERE` clause.
  google.type.Date date = 1;

  // Country where impressions appeared.
  //
  // Required in the `SELECT` clause. A condition on `report_country_code` is
  // required in the `WHERE` clause.
  optional string report_country_code = 2;

  // Google product category ID to calculate the report for, represented in
  // [Google's product
  // taxonomy](https://support.google.com/merchants/answer/6324436).
  //
  // Required in the `SELECT` clause. A condition on `report_category_id` is
  // required in the `WHERE` clause.
  optional int64 report_category_id = 3;

  // Traffic source of impressions.
  //
  // Required in the `SELECT` clause.
  optional TrafficSource.TrafficSourceEnum traffic_source = 4;

  // Change in visibility based on impressions for your domain with respect to
  // the start of the selected time range (or first day with non-zero
  // impressions).
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional double your_domain_visibility_trend = 5;

  // Change in visibility based on impressions with respect to the start of the
  // selected time range (or first day with non-zero impressions) for a
  // combined set of merchants with highest visibility approximating the
  // market.
  //
  // Cannot be filtered on in the 'WHERE' clause.
  optional double category_benchmark_visibility_trend = 6;
}

// Marketing method used to promote your products on Google (organic versus
// ads).
message MarketingMethod {
  // Marketing method values.
  enum MarketingMethodEnum {
    // Not specified.
    MARKETING_METHOD_ENUM_UNSPECIFIED = 0;

    // Organic marketing.
    ORGANIC = 1;

    // Ads-based marketing.
    ADS = 2;
  }
}

// Granularity of the Best sellers report. Best sellers reports are computed
// over a week and a month timeframe.
message ReportGranularity {
  // Report granularity values.
  enum ReportGranularityEnum {
    // Not specified.
    REPORT_GRANULARITY_ENUM_UNSPECIFIED = 0;

    // Report is computed over a week timeframe.
    WEEKLY = 1;

    // Report is computed over a month timeframe.
    MONTHLY = 2;
  }
}

// Relative demand of a product cluster or brand in the Best sellers report.
message RelativeDemand {
  // Relative demand values.
  enum RelativeDemandEnum {
    // Not specified.
    RELATIVE_DEMAND_ENUM_UNSPECIFIED = 0;

    // Demand is 0-5% of the demand of the highest ranked product cluster or
    // brand.
    VERY_LOW = 10;

    // Demand is 6-10% of the demand of the highest ranked product cluster or
    // brand.
    LOW = 20;

    // Demand is 11-20% of the demand of the highest ranked product cluster or
    // brand.
    MEDIUM = 30;

    // Demand is 21-50% of the demand of the highest ranked product cluster or
    // brand.
    HIGH = 40;

    // Demand is 51-100% of the demand of the highest ranked product cluster or
    // brand.
    VERY_HIGH = 50;
  }
}

// Relative demand of a product cluster or brand in the Best sellers report
// compared to the previous time period.
message RelativeDemandChangeType {
  // Relative demand change type values.
  enum RelativeDemandChangeTypeEnum {
    // Not specified.
    RELATIVE_DEMAND_CHANGE_TYPE_ENUM_UNSPECIFIED = 0;

    // Relative demand is lower than the previous time period.
    SINKER = 1;

    // Relative demand is equal to the previous time period.
    FLAT = 2;

    // Relative demand is higher than the previous time period.
    RISER = 3;
  }
}

// Traffic source of impressions in the Competitive visibility report.
message TrafficSource {
  // Traffic source values.
  enum TrafficSourceEnum {
    // Not specified.
    TRAFFIC_SOURCE_ENUM_UNSPECIFIED = 0;

    // Organic traffic.
    ORGANIC = 1;

    // Traffic from ads.
    ADS = 2;

    // Organic and ads traffic.
    ALL = 3;
  }
}
