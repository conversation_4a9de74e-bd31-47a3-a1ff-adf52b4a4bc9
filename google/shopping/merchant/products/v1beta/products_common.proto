// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.products.v1beta;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "google/shopping/type/types.proto";
import "google/type/interval.proto";

option go_package = "cloud.google.com/go/shopping/merchant/products/apiv1beta/productspb;productspb";
option java_multiple_files = true;
option java_outer_classname = "ProductsCommonProto";
option java_package = "com.google.shopping.merchant.products.v1beta";

// The subscription period of the product.
enum SubscriptionPeriod {
  // Indicates that the subscription period is unspecified.
  SUBSCRIPTION_PERIOD_UNSPECIFIED = 0;

  // Indicates that the subscription period is month.
  MONTH = 1;

  // Indicates that the subscription period is year.
  YEAR = 2;
}

// Attributes.
message Attributes {
  // Set this value to false when the item does not have unique product
  // identifiers appropriate to its category, such as GTIN, MPN, and brand.
  // Defaults to true, if not provided.
  optional bool identifier_exists = 4;

  // Whether the item is a merchant-defined bundle. A bundle is a custom
  // grouping of different products sold by a merchant for a single price.
  optional bool is_bundle = 5;

  // Title of the item.
  optional string title = 6;

  // Description of the item.
  optional string description = 7;

  // URL directly linking to your item's page on your online store.
  optional string link = 8;

  // URL for the mobile-optimized version of your item's landing page.
  optional string mobile_link = 9;

  // URL for the canonical version of your item's landing page.
  optional string canonical_link = 10;

  // URL of an image of the item.
  optional string image_link = 11;

  // Additional URLs of images of the item.
  repeated string additional_image_links = 12;

  // Date on which the item should expire, as specified upon insertion, in
  // [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format. The actual
  // expiration date is exposed in `productstatuses` as
  // [googleExpirationDate](https://support.google.com/merchants/answer/6324499)
  // and might be earlier if `expirationDate` is too far in the future.
  google.protobuf.Timestamp expiration_date = 16;

  // The date time when an offer becomes visible in search results across
  // Google’s YouTube surfaces, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format. See [Disclosure date](
  // https://support.google.com/merchants/answer/13034208) for more information.
  google.protobuf.Timestamp disclosure_date = 79;

  // Set to true if the item is targeted towards adults.
  optional bool adult = 17;

  // Target [age group](https://support.google.com/merchants/answer/6324463) of
  // the item.
  optional string age_group = 18;

  // Availability status of the item.
  optional string availability = 19;

  // The day a pre-ordered product becomes available for delivery, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp availability_date = 20;

  // Brand of the item.
  optional string brand = 21;

  // Color of the item.
  optional string color = 22;

  // Condition or state of the item.
  optional string condition = 23;

  // Target gender of the item.
  optional string gender = 24;

  // Google's category of the item (see [Google product
  // taxonomy](https://support.google.com/merchants/answer/1705911)). When
  // querying products, this field will contain the user provided value. There
  // is currently no way to get back the auto assigned google product
  // categories through the API.
  optional string google_product_category = 25;

  // Global Trade Item Numbers
  // ([GTIN](https://support.google.com/merchants/answer/188494#gtin)) of the
  // item.
  // You can provide up to 10 GTINs.
  repeated string gtin = 26;

  // Shared identifier for all variants of the same product.
  optional string item_group_id = 27;

  // The material of which the item is made.
  optional string material = 28;

  // Manufacturer Part Number
  // ([MPN](https://support.google.com/merchants/answer/188494#mpn)) of the
  // item.
  optional string mpn = 29;

  // The item's pattern (for example, polka dots).
  optional string pattern = 30;

  // Price of the item.
  google.shopping.type.Price price = 31;

  // Number and amount of installments to pay for an item.
  Installment installment = 32;

  // Number of periods (months or years) and amount of payment per period
  // for an item with an associated subscription contract.
  SubscriptionCost subscription_cost = 33;

  // Loyalty points that users receive after purchasing the item. Japan only.
  LoyaltyPoints loyalty_points = 34;

  // A list of loyalty program information that is used to surface loyalty
  // benefits (for example, better pricing, points, etc) to the user of this
  // item.
  repeated LoyaltyProgram loyalty_programs = 136;

  // Categories of the item (formatted as in [product data
  // specification](https://support.google.com/merchants/answer/188494#product_type)).
  repeated string product_types = 35;

  // Advertised sale price of the item.
  google.shopping.type.Price sale_price = 36;

  // Date range during which the item is on sale (see [product data
  // specification](https://support.google.com/merchants/answer/188494#sale_price_effective_date)).
  google.type.Interval sale_price_effective_date = 37;

  // The quantity of the product that is available for selling on Google.
  // Supported only for online products.
  optional int64 sell_on_google_quantity = 38;

  // The height of the product in the units provided. The value must be
  // between
  // 0 (exclusive) and 3000 (inclusive).
  ProductDimension product_height = 119;

  // The length of the product in the units provided. The value must be
  // between 0 (exclusive) and 3000 (inclusive).
  ProductDimension product_length = 120;

  // The width of the product in the units provided. The value must be between
  // 0 (exclusive) and 3000 (inclusive).
  ProductDimension product_width = 121;

  // The weight of the product in the units provided. The value must be
  // between 0 (exclusive) and 2000 (inclusive).
  ProductWeight product_weight = 122;

  // Shipping rules.
  repeated Shipping shipping = 39;

  // Conditions to be met for a product to have free shipping.
  repeated FreeShippingThreshold free_shipping_threshold = 135;

  // Weight of the item for shipping.
  ShippingWeight shipping_weight = 40;

  // Length of the item for shipping.
  ShippingDimension shipping_length = 41;

  // Width of the item for shipping.
  ShippingDimension shipping_width = 42;

  // Height of the item for shipping.
  ShippingDimension shipping_height = 43;

  // Maximal product handling time (in business days).
  optional int64 max_handling_time = 44;

  // Minimal product handling time (in business days).
  optional int64 min_handling_time = 45;

  // The shipping label of the product, used to group product in account-level
  // shipping rules.
  optional string shipping_label = 46;

  // The transit time label of the product, used to group product in
  // account-level transit time tables.
  optional string transit_time_label = 47;

  // Size of the item. Only one value is allowed. For variants with different
  // sizes, insert a separate product for each size with the same
  // `itemGroupId` value (see
  // [https://support.google.com/merchants/answer/6324492](size definition)).
  optional string size = 48;

  // System in which the size is specified. Recommended for apparel items.
  optional string size_system = 49;

  // The cut of the item. It can be used to represent combined size types for
  // apparel items. Maximum two of size types can be provided (see
  // [https://support.google.com/merchants/answer/6324497](size type)).
  repeated string size_types = 50;

  // Tax information.
  repeated Tax taxes = 51;

  // The tax category of the product.
  optional string tax_category = 52;

  // The energy efficiency class as defined in EU directive 2010/30/EU.
  optional string energy_efficiency_class = 53;

  // The energy efficiency class as defined in EU directive 2010/30/EU.
  optional string min_energy_efficiency_class = 54;

  // The energy efficiency class as defined in EU directive 2010/30/EU.
  optional string max_energy_efficiency_class = 55;

  // The measure and dimension of an item.
  UnitPricingMeasure unit_pricing_measure = 56;

  // The preference of the denominator of the unit price.
  UnitPricingBaseMeasure unit_pricing_base_measure = 57;

  // The number of identical products in a merchant-defined multipack.
  optional int64 multipack = 58;

  // Used to group items in an arbitrary way. Only for CPA%, discouraged
  // otherwise.
  optional string ads_grouping = 59;

  // Similar to ads_grouping, but only works on CPC.
  repeated string ads_labels = 60;

  // Allows advertisers to override the item URL when the product is shown
  // within the context of Product ads.
  optional string ads_redirect = 61;

  // Cost of goods sold. Used for gross profit reporting.
  google.shopping.type.Price cost_of_goods_sold = 62;

  // Technical specification or additional product details.
  repeated ProductDetail product_details = 63;

  // Bullet points describing the most relevant highlights of a product.
  repeated string product_highlights = 64;

  // An identifier for an item for dynamic remarketing campaigns.
  optional string display_ads_id = 65;

  // Advertiser-specified recommendations.
  repeated string display_ads_similar_ids = 66;

  // Title of an item for dynamic remarketing campaigns.
  optional string display_ads_title = 67;

  // URL directly to your item's landing page for dynamic remarketing
  // campaigns.
  optional string display_ads_link = 68;

  // Offer margin for dynamic remarketing campaigns.
  optional double display_ads_value = 69;

  // The unique ID of a promotion.
  repeated string promotion_ids = 70;

  // The pick up option for the item.
  optional string pickup_method = 80;

  // Item store pickup timeline.
  optional string pickup_sla = 81;

  // Link template for merchant hosted local storefront.
  optional string link_template = 82;

  // Link template for merchant hosted local storefront optimized for mobile
  // devices.
  optional string mobile_link_template = 83;

  // Custom label 0 for custom grouping of items in a Shopping campaign.
  optional string custom_label_0 = 71;

  // Custom label 1 for custom grouping of items in a Shopping campaign.
  optional string custom_label_1 = 72;

  // Custom label 2 for custom grouping of items in a Shopping campaign.
  optional string custom_label_2 = 73;

  // Custom label 3 for custom grouping of items in a Shopping campaign.
  optional string custom_label_3 = 74;

  // Custom label 4 for custom grouping of items in a Shopping campaign.
  optional string custom_label_4 = 75;

  // The list of destinations to include for this target (corresponds to
  // checked check boxes in Merchant Center). Default destinations are always
  // included unless provided in `excludedDestinations`.
  repeated string included_destinations = 76;

  // The list of destinations to exclude for this target (corresponds to
  // unchecked check boxes in Merchant Center).
  repeated string excluded_destinations = 77;

  // List of country codes (ISO 3166-1 alpha-2) to exclude the offer from
  // Shopping Ads destination.
  // Countries from this list are removed from countries configured
  // in data source settings.
  repeated string shopping_ads_excluded_countries = 78;

  // Required for multi-seller accounts. Use this attribute if you're a
  // marketplace uploading products for various sellers to your multi-seller
  // account.
  optional string external_seller_id = 1;

  // Publication of this item will be temporarily
  // [paused](https://support.google.com/merchants/answer/********).
  optional string pause = 13;

  // Additional URLs of lifestyle images of the item, used to explicitly
  // identify images that showcase your item in a real-world context. See the
  // [Help Center article](https://support.google.com/merchants/answer/9103186)
  // for more information.
  repeated string lifestyle_image_links = 14;

  // Extra fields to export to the Cloud Retail program.
  repeated CloudExportAdditionalProperties cloud_export_additional_properties =
      84;

  // URL of the 3D image of the item. See the
  // [Help Center article](https://support.google.com/merchants/answer/********)
  // for more information.
  optional string virtual_model_link = 130;

  // Product Certifications, for example for energy efficiency labeling of
  // products recorded in the [EU EPREL](https://eprel.ec.europa.eu/screen/home)
  // database. See the [Help
  // Center](https://support.google.com/merchants/answer/13528839)
  // article for more information.
  repeated Certification certifications = 123;

  // Structured title, for algorithmically (AI)-generated titles.
  optional ProductStructuredTitle structured_title = 132;

  // Structured description, for algorithmically (AI)-generated descriptions.
  optional ProductStructuredDescription structured_description = 133;

  // A safeguard in the "Automated Discounts"
  // (https://support.google.com/merchants/answer/10295759) and
  // "Dynamic Promotions"
  // (https://support.google.com/merchants/answer/13949249) projects,
  // ensuring that discounts on merchants' offers do not fall below this value,
  // thereby preserving the offer's value and profitability.
  google.shopping.type.Price auto_pricing_min_price = 124;
}

// The Tax of the product.
message Tax {
  // The percentage of tax rate that applies to the item price.
  double rate = 1;

  // The country within which the item is taxed, specified as a [CLDR
  // territory
  // code](http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml).
  string country = 2;

  // The geographic region to which the tax rate applies.
  string region = 3;

  // Set to true if tax is charged on shipping.
  bool tax_ship = 4;

  // The numeric ID of a location that the tax rate applies to as defined in
  // the [AdWords
  // API](https://developers.google.com/adwords/api/docs/appendix/geotargeting).
  int64 location_id = 5;

  // The postal code range that the tax rate applies to, represented by
  // a ZIP code, a ZIP code prefix using * wildcard, a range between two ZIP
  // codes or two ZIP code prefixes of equal length.
  // Examples: 94114, 94*, 94002-95460, 94*-95*.
  string postal_code = 6;
}

// The ShippingWeight of the product.
message ShippingWeight {
  // The weight of the product used to calculate the shipping cost of the
  // item.
  double value = 1;

  // The unit of value.
  string unit = 2;
}

// The ShippingDimension of the product.
message ShippingDimension {
  // The dimension of the product used to calculate the shipping cost of the
  // item.
  double value = 1;

  // The unit of value.
  string unit = 2;
}

// The UnitPricingBaseMeasure of the product.
message UnitPricingBaseMeasure {
  // The denominator of the unit price.
  int64 value = 1;

  // The unit of the denominator.
  string unit = 2;
}

// The UnitPricingMeasure of the product.
message UnitPricingMeasure {
  // The measure of an item.
  double value = 1;

  // The unit of the measure.
  string unit = 2;
}

// The SubscriptionCost of the product.
message SubscriptionCost {
  // The type of subscription period.
  // Supported values are:
  //   * "`month`"
  //   * "`year`"
  SubscriptionPeriod period = 1;

  // The number of subscription periods the buyer has to pay.
  int64 period_length = 2;

  // The amount the buyer has to pay per subscription period.
  google.shopping.type.Price amount = 3;
}

// A message that represents installment.
message Installment {
  // The number of installments the buyer has to pay.
  int64 months = 1;

  // The amount the buyer has to pay per month.
  google.shopping.type.Price amount = 2;

  // The up-front down payment amount the buyer has to pay.
  optional google.shopping.type.Price downpayment = 3;

  // Type of installment payments.
  // Supported values are:
  //   * "`finance`"
  //   * "`lease`"
  optional string credit_type = 4;
}

// A message that represents loyalty points.
message LoyaltyPoints {
  // Name of loyalty points program. It is recommended to limit the name to
  // 12 full-width characters or 24 Roman characters.
  string name = 1;

  // The retailer's loyalty points in absolute value.
  int64 points_value = 2;

  // The ratio of a point when converted to currency. Google assumes currency
  // based on Merchant Center settings. If ratio is left out, it defaults to
  // 1.0.
  double ratio = 3;
}

// A message that represents loyalty program.
message LoyaltyProgram {
  // The label of the loyalty program. This is an internal label that uniquely
  // identifies the relationship between a merchant entity and a loyalty
  // program entity. The label must be provided so that the system can associate
  // the assets below (for example, price and points) with a merchant. The
  // corresponding program must be linked to the merchant account.
  optional string program_label = 1;

  // The label of the tier within the loyalty program.
  // Must match one of the labels within the program.
  optional string tier_label = 2;

  // The price for members of the given tier, that is, the instant discount
  // price. Must be smaller or equal to the regular price.
  optional google.shopping.type.Price price = 3;

  // The cashback that can be used for future purchases.
  optional google.shopping.type.Price cashback_for_future_use = 4;

  // The amount of loyalty points earned on a purchase.
  optional int64 loyalty_points = 5;

  // A date range during which the item is eligible for member price. If not
  // specified, the member price is always applicable. The date range is
  // represented by a pair of ISO 8601 dates separated by a space,
  // comma, or slash.
  optional google.type.Interval member_price_effective_date = 6;

  // The label of the shipping benefit. If the field has value, this offer has
  // loyalty shipping benefit. If the field value isn't provided, the item is
  // not eligible for loyalty shipping for the given loyalty tier.
  optional string shipping_label = 7;
}

// The Shipping of the product.
message Shipping {
  // Fixed shipping price, represented as a number.
  google.shopping.type.Price price = 1;

  // The [CLDR territory
  // code](http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml)
  // of the country to which an item will ship.
  string country = 2;

  // The geographic region to which a shipping rate applies.
  // See [region](https://support.google.com/merchants/answer/6324484) for more
  // information.
  string region = 3;

  // A free-form description of the service class or delivery speed.
  string service = 4;

  // The numeric ID of a location that the shipping rate applies to as
  // defined in the [AdWords
  // API](https://developers.google.com/adwords/api/docs/appendix/geotargeting).
  int64 location_id = 5;

  // The location where the shipping is applicable, represented by a
  // location group name.
  string location_group_name = 6;

  // The postal code range that the shipping rate applies to, represented by
  // a postal code, a postal code prefix followed by a * wildcard, a range
  // between two postal codes or two postal code prefixes of equal length.
  string postal_code = 7;

  // Minimum handling time (inclusive) between when the order is received and
  // shipped in business days. 0 means that the order is shipped on the same
  // day as it is received if it happens before the cut-off time.
  // [minHandlingTime][google.shopping.merchant.products.v1beta.Shipping.min_handling_time]
  // can only be present together with
  // [maxHandlingTime][google.shopping.merchant.products.v1beta.Shipping.max_handling_time];
  // but it is not required if
  // [maxHandlingTime][google.shopping.merchant.products.v1beta.Shipping.max_handling_time]
  // is present.
  optional int64 min_handling_time = 8;

  // Maximum handling time (inclusive) between when the order is received and
  // shipped in business days. 0 means that the order is shipped on the same
  // day as it is received if it happens before the cut-off time. Both
  // [maxHandlingTime][google.shopping.merchant.products.v1beta.Shipping.max_handling_time]
  // and
  // [maxTransitTime][google.shopping.merchant.products.v1beta.Shipping.max_transit_time]
  // are required if providing shipping speeds.
  // [minHandlingTime][google.shopping.merchant.products.v1beta.Shipping.min_handling_time]
  // is optional if
  // [maxHandlingTime][google.shopping.merchant.products.v1beta.Shipping.max_handling_time]
  // is present.
  optional int64 max_handling_time = 9;

  // Minimum transit time (inclusive) between when the order has shipped and
  // when it is delivered in business days. 0 means that the order is
  // delivered on the same day as it ships.
  // [minTransitTime][google.shopping.merchant.products.v1beta.Shipping.min_transit_time]
  // can only be present together with
  // [maxTransitTime][google.shopping.merchant.products.v1beta.Shipping.max_transit_time];
  // but it is not required if
  // [maxTransitTime][google.shopping.merchant.products.v1beta.Shipping.max_transit_time]
  // is present.
  optional int64 min_transit_time = 10;

  // Maximum transit time (inclusive) between when the order has shipped and
  // when it is delivered in business days. 0 means that the order is
  // delivered on the same day as it ships. Both
  // [maxHandlingTime][google.shopping.merchant.products.v1beta.Shipping.max_handling_time]
  // and
  // [maxTransitTime][google.shopping.merchant.products.v1beta.Shipping.max_transit_time]
  // are required if providing shipping speeds.
  // [minTransitTime][google.shopping.merchant.products.v1beta.Shipping.min_transit_time]
  // is optional if
  // [maxTransitTime][google.shopping.merchant.products.v1beta.Shipping.max_transit_time]
  // is present.
  optional int64 max_transit_time = 11;
}

// Conditions to be met for a product to have free shipping.
message FreeShippingThreshold {
  // The [CLDR territory
  // code](http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml)
  // of the country to which an item will ship.
  optional string country = 1;

  // The minimum product price for the shipping cost to become free. Represented
  // as a number.
  optional google.shopping.type.Price price_threshold = 2;
}

// The product details.
message ProductDetail {
  // The section header used to group a set of product details.
  string section_name = 1;

  // The name of the product detail.
  string attribute_name = 2;

  // The value of the product detail.
  string attribute_value = 3;
}

// Product
// [certification](https://support.google.com/merchants/answer/13528839),
// initially introduced for EU energy efficiency labeling compliance using the
// EU EPREL database.
message Certification {
  // The certification authority, for example "European_Commission".
  // Maximum length is 2000 characters.
  optional string certification_authority = 1;

  // The name of the certification, for example "EPREL".
  // Maximum length is 2000 characters.
  optional string certification_name = 2;

  // The certification code.
  // Maximum length is 2000 characters.
  optional string certification_code = 3;

  // The certification value (also known as class, level or grade), for example
  // "A+", "C", "gold".
  // Maximum length is 2000 characters.
  optional string certification_value = 4;
}

// Structured title, for algorithmically (AI)-generated titles.
message ProductStructuredTitle {
  // The digital source type, for example "trained_algorithmic_media".
  // Following [IPTC](https://cv.iptc.org/newscodes/digitalsourcetype).
  // Maximum length is 40 characters.
  optional string digital_source_type = 1;

  // The title text
  // Maximum length is 150 characters
  optional string content = 2;
}

// Structured description, for algorithmically (AI)-generated descriptions.
message ProductStructuredDescription {
  // The digital source type, for example "trained_algorithmic_media".
  // Following [IPTC](https://cv.iptc.org/newscodes/digitalsourcetype).
  // Maximum length is 40 characters.
  optional string digital_source_type = 1;

  // The description text
  // Maximum length is 5000 characters
  optional string content = 2;
}

// The dimension of the product.
message ProductDimension {
  // Required. The dimension value represented as a number. The value can have a
  // maximum precision of four decimal places.
  double value = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The dimension units.
  // Acceptable values are:
  //   * "`in`"
  //   * "`cm`"
  string unit = 2 [(google.api.field_behavior) = REQUIRED];
}

// The weight of the product.
message ProductWeight {
  // Required. The weight represented as a number. The weight can have a maximum
  // precision of four decimal places.
  double value = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The weight unit.
  // Acceptable values are:
  //   * "`g`"
  //   * "`kg`"
  //   * "`oz`"
  //   * "`lb`"
  string unit = 2 [(google.api.field_behavior) = REQUIRED];
}

// The status of a product, data validation issues, that is, information about
// a product computed asynchronously.
message ProductStatus {
  // The destination status of the product status.
  message DestinationStatus {
    // The name of the reporting context.
    google.shopping.type.ReportingContext.ReportingContextEnum
        reporting_context = 1;

    // List of country codes (ISO 3166-1 alpha-2) where the offer is approved.
    repeated string approved_countries = 2;

    // List of country codes (ISO 3166-1 alpha-2) where the offer is pending
    // approval.
    repeated string pending_countries = 3;

    // List of country codes (ISO 3166-1 alpha-2) where the offer is
    // disapproved.
    repeated string disapproved_countries = 4;
  }

  // The ItemLevelIssue of the product status.
  message ItemLevelIssue {
    // How the issue affects the serving of the product.
    enum Severity {
      // Not specified.
      SEVERITY_UNSPECIFIED = 0;

      // This issue represents a warning and does not have a direct affect
      // on the product.
      NOT_IMPACTED = 1;

      // The product is demoted and most likely have limited performance
      // in search results
      DEMOTED = 2;

      // Issue disapproves the product.
      DISAPPROVED = 3;
    }

    // The error code of the issue.
    string code = 1;

    // How this issue affects serving of the offer.
    Severity severity = 2;

    // Whether the issue can be resolved by the merchant.
    string resolution = 3;

    // The attribute's name, if the issue is caused by a single attribute.
    string attribute = 4;

    // The reporting context the issue applies to.
    google.shopping.type.ReportingContext.ReportingContextEnum
        reporting_context = 5;

    // A short issue description in English.
    string description = 6;

    // A detailed issue description in English.
    string detail = 7;

    // The URL of a web page to help with resolving this issue.
    string documentation = 8;

    // List of country codes (ISO 3166-1 alpha-2) where issue applies to the
    // offer.
    repeated string applicable_countries = 9;
  }

  // The intended destinations for the product.
  repeated DestinationStatus destination_statuses = 3;

  // A list of all issues associated with the product.
  repeated ItemLevelIssue item_level_issues = 4;

  // Date on which the item has been created, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp creation_date = 5;

  // Date on which the item has been last updated, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp last_update_date = 6;

  // Date on which the item expires, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp google_expiration_date = 7;
}

// Product property for the Cloud Retail API.
// For example, properties for a TV product could be "Screen-Resolution" or
// "Screen-Size".
message CloudExportAdditionalProperties {
  // Name of the given property. For example,
  // "Screen-Resolution" for a TV product. Maximum string size is 256
  // characters.
  optional string property_name = 1;

  // Text value of the given property. For example,
  // "8K(UHD)" could be a text value for a TV product. Maximum
  // repeatedness of this value is 400. Values are stored in an arbitrary but
  // consistent order. Maximum string size is 256 characters.
  repeated string text_value = 2;

  // Boolean value of the given property. For example for a TV product,
  // "True" or "False" if the screen is UHD.
  optional bool bool_value = 3;

  // Integer values of the given property. For example, 1080 for a TV
  // product's Screen Resolution. Maximum repeatedness of this value
  // is 400. Values are stored in an arbitrary but consistent order.
  repeated int64 int_value = 4;

  // Float values of the given property. For example for a TV product
  // 1.2345. Maximum repeatedness of this value is 400. Values
  // are stored in an arbitrary but consistent order.
  repeated float float_value = 5;

  // Minimum float value of the given property. For example for a TV
  // product 1.00.
  optional float min_value = 6;

  // Maximum float value of the given property. For example for a TV
  // product 100.00.
  optional float max_value = 7;

  // Unit of the given property. For example, "Pixels" for a TV product. Maximum
  // string size is 256B.
  optional string unit_code = 8;
}
