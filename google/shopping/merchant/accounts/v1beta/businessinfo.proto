// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/shopping/merchant/accounts/v1beta/customerservice.proto";
import "google/shopping/merchant/accounts/v1beta/phoneverificationstate.proto";
import "google/type/phone_number.proto";
import "google/type/postal_address.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "BusinessInfoProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to support business info API.
service BusinessInfoService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves the business info of an account.
  rpc GetBusinessInfo(GetBusinessInfoRequest) returns (BusinessInfo) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/businessInfo}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the business info of an account. Executing this method requires
  // admin access.
  rpc UpdateBusinessInfo(UpdateBusinessInfoRequest) returns (BusinessInfo) {
    option (google.api.http) = {
      patch: "/accounts/v1beta/{business_info.name=accounts/*/businessInfo}"
      body: "business_info"
    };
    option (google.api.method_signature) = "business_info,update_mask";
  }
}

// Collection of information related to a business.
message BusinessInfo {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/BusinessInfo"
    pattern: "accounts/{account}/businessInfo"
    plural: "businessInfos"
    singular: "businessInfo"
  };

  // Identifier. The resource name of the business info.
  // Format: `accounts/{account}/businessInfo`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. The address of the business.
  optional google.type.PostalAddress address = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The phone number of the business.
  optional google.type.PhoneNumber phone = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The phone verification state of the business.
  optional PhoneVerificationState phone_verification_state = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The customer service of the business.
  optional CustomerService customer_service = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The 10-digit [Korean business registration
  // number](https://support.google.com/merchants/answer/9037766) separated with
  // dashes in the format: XXX-XX-XXXXX.
  optional string korean_business_registration_number = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `GetBusinessInfo` method.
message GetBusinessInfoRequest {
  // Required. The resource name of the business info.
  // Format: `accounts/{account}/businessInfo`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/BusinessInfo"
    }
  ];
}

// Request message for the `UpdateBusinessInfo` method.
message UpdateBusinessInfoRequest {
  // Required. The new version of the business info.
  BusinessInfo business_info = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields being updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}
