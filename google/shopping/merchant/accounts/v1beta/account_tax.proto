// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/shopping/merchant/accounts/v1beta/tax_rule.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "AccountTaxProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Manages account level tax setting data.
//
// This API defines the following resource model:
//
// - [AccountTax][google.shopping.merchant.accounts.v1main.AccountTax]
service AccountTaxService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Returns the tax rules that match the conditions of GetAccountTaxRequest
  rpc GetAccountTax(GetAccountTaxRequest) returns (AccountTax) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/accounttax/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the tax settings of the sub-accounts only in your
  // Merchant Center account.
  // This method can only be called on a multi-client account, otherwise it'll
  // return an error.
  rpc ListAccountTax(ListAccountTaxRequest) returns (ListAccountTaxResponse) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{parent=accounts/*}/accounttax"
    };
    option (google.api.method_signature) = "parent";
  }

  // Updates the tax settings of the account.
  rpc UpdateAccountTax(UpdateAccountTaxRequest) returns (AccountTax) {
    option (google.api.http) = {
      patch: "/accounts/v1beta/{account_tax.name=accounts/*/accounttax/*}"
      body: "account_tax"
    };
    option (google.api.method_signature) = "account_tax,update_mask";
    option (google.api.method_signature) = "account_tax";
  }
}

// The tax settings of a merchant account. All methods require the admin role.
message AccountTax {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/AccountTax"
    pattern: "accounts/{account}/accounttax/{tax}"
    plural: "accountTaxes"
    singular: "accountTax"
  };

  // Identifier. The name of the tax setting.
  // Format:
  // "{account_tax.name=accounts/{account}}"
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The ID of the account to which these account tax settings
  // belong.
  int64 account = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Tax rules. "Define the tax rules in each region.
  // No tax will be presented if a region has no rule."
  repeated TaxRule tax_rules = 3;
}

// Request to get tax settings
message GetAccountTaxRequest {
  // Required. The name from which tax settings will be retrieved
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/AccountTax"
    }
  ];
}

// Request to update the tax settings
message UpdateAccountTaxRequest {
  // Required. The tax setting that will be updated
  AccountTax account_tax = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated
  google.protobuf.FieldMask update_mask = 2;
}

// Request to list all sub-account tax settings only for the requesting merchant
// This method can only be called on a multi-client account, otherwise it'll
// return an error.
message ListAccountTaxRequest {
  // Required. The parent, which owns this collection of account tax.
  // Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/AccountTax"
    }
  ];

  // The maximum number of tax settings to return in the response, used for
  // paging.
  int32 page_size = 2;

  // The token returned by the previous request.
  string page_token = 3;
}

// Response to account tax list request
// This method can only be called on a multi-client account, otherwise it'll
// return an error.
message ListAccountTaxResponse {
  // Page of accounttax settings
  repeated AccountTax account_taxes = 1;

  // The token for the retrieval of the next page of account tax settings.
  string next_page_token = 2;
}
