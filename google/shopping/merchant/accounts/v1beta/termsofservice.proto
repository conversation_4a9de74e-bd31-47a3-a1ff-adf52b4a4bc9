// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/shopping/merchant/accounts/v1beta/termsofservicekind.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "TermsOfServiceProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to support `TermsOfService` API.
service TermsOfServiceService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves the `TermsOfService` associated with the provided version.
  rpc GetTermsOfService(GetTermsOfServiceRequest) returns (TermsOfService) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=termsOfService/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves the latest version of the `TermsOfService` for a given `kind` and
  // `region_code`.
  rpc RetrieveLatestTermsOfService(RetrieveLatestTermsOfServiceRequest)
      returns (TermsOfService) {
    option (google.api.http) = {
      get: "/accounts/v1beta/termsOfService:retrieveLatest"
    };
  }

  // Accepts a `TermsOfService`. Executing this method requires admin access.
  rpc AcceptTermsOfService(AcceptTermsOfServiceRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=termsOfService/*}:accept"
    };
    option (google.api.method_signature) = "name";
  }
}

// A `TermsOfService`.
message TermsOfService {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/TermsOfService"
    pattern: "termsOfService/{version}"
  };

  // Identifier. The resource name of the terms of service version.
  // Format: `termsOfService/{version}`
  string name = 1 [
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/TermsOfService"
    }
  ];

  // Region code as defined by [CLDR](https://cldr.unicode.org/). This is either
  // a country where the ToS applies specifically to that country or `001` when
  // the same `TermsOfService` can be signed in any country. However note that
  // when signing a ToS that applies globally we still expect that a specific
  // country is provided  (this should be merchant business country or program
  // country of participation).
  string region_code = 2;

  // The Kind this terms of service version applies to.
  TermsOfServiceKind kind = 3;

  // URI for terms of service file that needs to be displayed to signing users.
  optional string file_uri = 4;

  // Whether this terms of service version is external. External terms of
  // service versions can only be agreed through external processes and not
  // directly by the merchant through UI or API.
  bool external = 5;
}

// Request message for the `GetTermsOfService` method.
message GetTermsOfServiceRequest {
  // Required. The resource name of the terms of service version.
  // Format: `termsOfService/{version}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/TermsOfService"
    }
  ];
}

// Request message for the `RetrieveLatestTermsOfService` method.
message RetrieveLatestTermsOfServiceRequest {
  // Required. Region code as defined by [CLDR](https://cldr.unicode.org/). This
  // is either a country when the ToS applies specifically to that country or
  // 001 when it applies globally.
  string region_code = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The Kind this terms of service version applies to.
  TermsOfServiceKind kind = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `AcceptTermsOfService` method.
message AcceptTermsOfServiceRequest {
  // Required. The resource name of the terms of service version.
  // Format: `termsOfService/{version}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/TermsOfService"
    }
  ];

  // Required. The account for which to accept the ToS.
  string account = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Account"
    }
  ];

  // Required. Region code as defined by [CLDR](https://cldr.unicode.org/). This
  // is either a country when the ToS applies specifically to that country or
  // 001 when it applies globally.
  string region_code = 3 [(google.api.field_behavior) = REQUIRED];
}
