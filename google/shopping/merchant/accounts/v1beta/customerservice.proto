// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/field_behavior.proto";
import "google/type/phone_number.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "CustomerServiceProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Customer service information.
message CustomerService {
  // Optional. The URI where customer service may be found.
  optional string uri = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The email address where customer service may be reached.
  optional string email = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The phone number where customer service may be called.
  optional google.type.PhoneNumber phone = 3
      [(google.api.field_behavior) = OPTIONAL];
}
