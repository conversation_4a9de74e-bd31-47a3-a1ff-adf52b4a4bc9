// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "AutofeedSettingsProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to support
// [autofeed](https://support.google.com/merchants/answer/7538732) setting.
service AutofeedSettingsService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves the autofeed settings of an account.
  rpc GetAutofeedSettings(GetAutofeedSettingsRequest)
      returns (AutofeedSettings) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/autofeedSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the autofeed settings of an account.
  rpc UpdateAutofeedSettings(UpdateAutofeedSettingsRequest)
      returns (AutofeedSettings) {
    option (google.api.http) = {
      patch: "/accounts/v1beta/{autofeed_settings.name=accounts/*/autofeedSettings}"
      body: "autofeed_settings"
    };
    option (google.api.method_signature) = "autofeed_settings,update_mask";
  }
}

// Collection of information related to the
// [autofeed](https://support.google.com/merchants/answer/7538732) settings.
message AutofeedSettings {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/AutofeedSettings"
    pattern: "accounts/{account}/autofeedSettings"
    plural: "autofeedSettings"
    singular: "autofeedSettings"
  };

  // Identifier. The resource name of the autofeed settings.
  // Format: `accounts/{account}/autofeedSettings`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. Enables or disables product crawling through the autofeed for the
  // given account. Autofeed accounts must meet [certain
  // conditions](https://support.google.com/merchants/answer/7538732#Configure_automated_feeds_Standard_Experience),
  // which can be checked through the `eligible` field.
  // The account must **not** be a marketplace.
  // When the autofeed is enabled for the first time, the products usually
  // appear instantly. When re-enabling, it might take up to 24 hours for
  // products to appear.
  bool enable_products = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Determines whether merchant is eligible for being enrolled
  // into an autofeed.
  bool eligible = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for the `GetAutofeedSettings` method.
message GetAutofeedSettingsRequest {
  // Required. The resource name of the autofeed settings.
  // Format: `accounts/{account}/autofeedSettings`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/AutofeedSettings"
    }
  ];
}

// Request message for the `UpdateAutofeedSettings` method.
message UpdateAutofeedSettingsRequest {
  // Required. The new version of the autofeed setting.
  AutofeedSettings autofeed_settings = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields being updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}
