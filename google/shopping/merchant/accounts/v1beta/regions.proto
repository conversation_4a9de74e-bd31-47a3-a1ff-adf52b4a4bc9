// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "RegionsProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Manages regions configuration.
//
// This API defines the following resource model:
//
// - [Region][google.shopping.merchant.accounts.v1main.Region]
service RegionsService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves a region defined in your Merchant Center account.
  rpc GetRegion(GetRegionRequest) returns (Region) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/regions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a region definition in your Merchant Center account. Executing this
  // method requires admin access.
  rpc CreateRegion(CreateRegionRequest) returns (Region) {
    option (google.api.http) = {
      post: "/accounts/v1beta/{parent=accounts/*}/regions"
      body: "region"
    };
    option (google.api.method_signature) = "parent,region,region_id";
  }

  // Updates a region definition in your Merchant Center account. Executing this
  // method requires admin access.
  rpc UpdateRegion(UpdateRegionRequest) returns (Region) {
    option (google.api.http) = {
      patch: "/accounts/v1beta/{region.name=accounts/*/regions/*}"
      body: "region"
    };
    option (google.api.method_signature) = "region,update_mask";
  }

  // Deletes a region definition from your Merchant Center account. Executing
  // this method requires admin access.
  rpc DeleteRegion(DeleteRegionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/accounts/v1beta/{name=accounts/*/regions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the regions in your Merchant Center account.
  rpc ListRegions(ListRegionsRequest) returns (ListRegionsResponse) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{parent=accounts/*}/regions"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for the `GetRegion` method.
message GetRegionRequest {
  // Required. The name of the region to retrieve.
  // Format: `accounts/{account}/regions/{region}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Region"
    }
  ];
}

// Request message for the `CreateRegion` method.
message CreateRegionRequest {
  // Required. The account to create a region for.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Account"
    }
  ];

  // Required. The identifier for the region, unique over all regions of the
  // same account.
  string region_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The region to create.
  Region region = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `UpdateRegion` method.
message UpdateRegionRequest {
  // Required. The updated region.
  Region region = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The comma-separated field mask indicating the fields to update.
  // Example:
  // `"displayName,postalCodeArea.regionCode"`.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `DeleteRegion` method.
message DeleteRegionRequest {
  // Required. The name of the region to delete.
  // Format: `accounts/{account}/regions/{region}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Region"
    }
  ];
}

// Request message for the `ListRegions` method.
message ListRegionsRequest {
  // Required. The account to list regions for.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Account"
    }
  ];

  // Optional. The maximum number of regions to return. The service may return
  // fewer than this value.
  // If unspecified, at most 50 regions will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListRegions` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListRegions` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `ListRegions` method.
message ListRegionsResponse {
  // The regions from the specified merchant.
  repeated Region regions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Represents a geographic region that you can use as a target with both the
// `RegionalInventory` and `ShippingSettings` services. You can define regions
// as collections of either postal codes or, in some countries, using predefined
// geotargets. For more information, see [Set up regions
// ](https://support.google.com/merchants/answer/7410946#zippy=%2Ccreate-a-new-region)
// for more information.
message Region {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/Region"
    pattern: "accounts/{account}/regions/{region}"
    plural: "regions"
    singular: "region"
  };

  // A list of postal codes that defines the region area.
  // Note: All regions defined using postal codes are accessible through the
  // account's `ShippingSettings.postalCodeGroups` resource.
  message PostalCodeArea {
    // A range of postal codes that defines the region area.
    message PostalCodeRange {
      // Required. A postal code or a pattern of the form prefix* denoting the
      // inclusive lower bound of the range defining the area. Examples values:
      // `94108`, `9410*`, `9*`.
      string begin = 1 [(google.api.field_behavior) = REQUIRED];

      // Optional. A postal code or a pattern of the form `prefix*` denoting the
      // inclusive upper bound of the range defining the area. It must have the
      // same length as postalCodeRangeBegin: if postalCodeRangeBegin is a
      // postal code then postalCodeRangeEnd must be a postal code too; if
      // postalCodeRangeBegin is a pattern then postalCodeRangeEnd must be a
      // pattern with the same prefix length. Optional: if not set, then the
      // area is defined as being all the postal codes matching
      // postalCodeRangeBegin.
      string end = 2 [(google.api.field_behavior) = OPTIONAL];
    }

    // Required. [CLDR territory
    // code](http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml)
    // or the country the postal code group applies to.
    string region_code = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. A range of postal codes.
    repeated PostalCodeRange postal_codes = 2
        [(google.api.field_behavior) = REQUIRED];
  }

  // A list of geotargets that defines the region area.
  message GeoTargetArea {
    // Required. A non-empty list of [location
    // IDs](https://developers.google.com/adwords/api/docs/appendix/geotargeting).
    // They must all be of the same location type (for example, state).
    repeated int64 geotarget_criteria_ids = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // Identifier. The resource name of the region.
  // Format: `accounts/{account}/regions/{region}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. The display name of the region.
  optional string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of postal codes that defines the region area.
  PostalCodeArea postal_code_area = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of geotargets that defines the region area.
  GeoTargetArea geotarget_area = 4 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Indicates if the region is eligible for use in the Regional
  // Inventory configuration.
  google.protobuf.BoolValue regional_inventory_eligible = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates if the region is eligible for use in the Shipping
  // Services configuration.
  google.protobuf.BoolValue shipping_eligible = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
