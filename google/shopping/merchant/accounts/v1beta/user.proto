// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/shopping/merchant/accounts/v1beta/accessright.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "UserProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to support user API.
service UserService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves a Merchant Center account user.
  rpc GetUser(GetUserRequest) returns (User) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/users/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a Merchant Center account user. Executing this method requires
  // admin access.
  rpc CreateUser(CreateUserRequest) returns (User) {
    option (google.api.http) = {
      post: "/accounts/v1beta/{parent=accounts/*}/users"
      body: "user"
    };
    option (google.api.method_signature) = "parent,user";
  }

  // Deletes a Merchant Center account user. Executing this method requires
  // admin access.
  rpc DeleteUser(DeleteUserRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/accounts/v1beta/{name=accounts/*/users/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a Merchant Center account user. Executing this method requires
  // admin access.
  rpc UpdateUser(UpdateUserRequest) returns (User) {
    option (google.api.http) = {
      patch: "/accounts/v1beta/{user.name=accounts/*/users/*}"
      body: "user"
    };
    option (google.api.method_signature) = "user,update_mask";
  }

  // Lists all users of a Merchant Center account.
  rpc ListUsers(ListUsersRequest) returns (ListUsersResponse) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{parent=accounts/*}/users"
    };
    option (google.api.method_signature) = "parent";
  }
}

// A [user](https://support.google.com/merchants/answer/********).
message User {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/User"
    pattern: "accounts/{account}/users/{email}"
    plural: "users"
    singular: "user"
  };

  // The possible states of a user.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // The user is pending confirmation. In this state, the user first needs to
    // accept the invitation before performing other actions.
    PENDING = 1;

    // The user is verified.
    VERIFIED = 2;
  }

  // Identifier. The resource name of the user.
  // Format: `accounts/{account}/user/{email}`
  //
  // Use `me` to refer to your own email address, for example
  // `accounts/{account}/users/me`.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The state of the user.
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The [access
  // rights](https://support.google.com/merchants/answer/********?sjid=6789834943175119429-EU#accesstypes)
  // the user has.
  repeated AccessRight access_rights = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `GetUser` method.
message GetUserRequest {
  // Required. The name of the user to retrieve.
  // Format: `accounts/{account}/users/{email}`
  //
  // It is also possible to retrieve the user corresponding to the caller by
  // using `me` rather than an email address as in
  // `accounts/{account}/users/me`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/User"
    }
  ];
}

// Request message for the `CreateUser` method.
message CreateUserRequest {
  // Required. The resource name of the account for which a user will be
  // created. Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Account"
    }
  ];

  // Required. The email address of the user (for example,
  // `<EMAIL>`).
  string user_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The user to create.
  User user = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `DeleteUser` method.
message DeleteUserRequest {
  // Required. The name of the user to delete.
  // Format: `accounts/{account}/users/{email}`
  //
  // It is also possible to delete the user corresponding to the caller by using
  // `me` rather than an email address as in `accounts/{account}/users/me`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/User"
    }
  ];
}

// Request message for the `UpdateUser` method.
message UpdateUserRequest {
  // Required. The new version of the user.
  //
  // Use `me` to refer to your own email address, for example
  // `accounts/{account}/users/me`.
  User user = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields being updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `ListUsers` method.
message ListUsersRequest {
  // Required. The parent, which owns this collection of users.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Account"
    }
  ];

  // Optional. The maximum number of users to return. The service may return
  // fewer than this value. If unspecified, at most 50 users will be returned.
  // The maximum value is 100; values above 100 will be coerced to 100
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListUsers` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListUsers` must match
  // the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `ListUsers` method.
message ListUsersResponse {
  // The users from the specified account.
  repeated User users = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
