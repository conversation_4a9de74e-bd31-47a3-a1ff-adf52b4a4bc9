// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "EmailPreferencesProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to support the `EmailPreferences` API.
//
// This service only permits retrieving and updating email preferences for the
// authenticated user.
service EmailPreferencesService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Returns the email preferences for a Merchant Center account user.
  //
  // Use the name=accounts/*/users/me/emailPreferences alias to get preferences
  // for the authenticated user.
  rpc GetEmailPreferences(GetEmailPreferencesRequest)
      returns (EmailPreferences) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/users/*/emailPreferences}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the email preferences for a Merchant Center account user. MCA users
  // should specify the MCA account rather than a sub-account of the MCA.
  //
  // Preferences which are not explicitly selected in the update mask will not
  // be updated.
  //
  // It is invalid for updates to specify an UNCONFIRMED opt-in status value.
  //
  // Use the name=accounts/*/users/me/emailPreferences alias to update
  // preferences
  // for the authenticated user.
  rpc UpdateEmailPreferences(UpdateEmailPreferencesRequest)
      returns (EmailPreferences) {
    option (google.api.http) = {
      patch: "/accounts/v1beta/{email_preferences.name=accounts/*/users/*/emailPreferences}"
      body: "email_preferences"
    };
    option (google.api.method_signature) = "email_preferences,update_mask";
  }
}

// The categories of notifications the user opted into / opted out of. The email
// preferences do not include mandatory announcements as users can't opt out of
// them.
message EmailPreferences {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/EmailPreferences"
    pattern: "accounts/{account}/users/{email}/emailPreferences"
    plural: "emailPreferences"
    singular: "emailPreferences"
  };

  // Opt in state of the email preference.
  enum OptInState {
    // Opt-in status is not specified.
    OPT_IN_STATE_UNSPECIFIED = 0;

    // User has opted out of receiving this type of email.
    OPTED_OUT = 1;

    // User has opted in to receiving this type of email.
    OPTED_IN = 2;

    // User has opted in to receiving this type of email and the confirmation
    // email has been sent, but user has not yet confirmed the opt in (applies
    // only to certain countries).
    UNCONFIRMED = 3;
  }

  // Identifier. The name of the EmailPreferences. The endpoint is only
  // supported for the authenticated user.
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Updates on new features, tips and best practices.
  OptInState news_and_tips = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for GetEmailPreferences method.
message GetEmailPreferencesRequest {
  // Required. The name of the `EmailPreferences` resource.
  // Format: `accounts/{account}/users/{email}/emailPreferences`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/EmailPreferences"
    }
  ];
}

// Request message for UpdateEmailPreferences method.
message UpdateEmailPreferencesRequest {
  // Required. Email Preferences to be updated.
  EmailPreferences email_preferences = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields being updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}
