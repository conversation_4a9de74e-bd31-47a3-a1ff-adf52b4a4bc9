// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "BusinessIdentityProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to support [business
// identity](https://support.google.com/merchants/answer/********) API.
service BusinessIdentityService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves the business identity of an account.
  rpc GetBusinessIdentity(GetBusinessIdentityRequest)
      returns (BusinessIdentity) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/businessIdentity}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the business identity of an account. Executing this method requires
  // admin access.
  rpc UpdateBusinessIdentity(UpdateBusinessIdentityRequest)
      returns (BusinessIdentity) {
    option (google.api.http) = {
      patch: "/accounts/v1beta/{business_identity.name=accounts/*/businessIdentity}"
      body: "business_identity"
    };
    option (google.api.method_signature) = "business_identity,update_mask";
  }
}

// Collection of information related to the [identity of a
// business](https://support.google.com/merchants/answer/********).
message BusinessIdentity {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/BusinessIdentity"
    pattern: "accounts/{account}/businessIdentity"
    plural: "businessIdentities"
    singular: "businessIdentity"
  };

  // All information related to an identity attribute.
  message IdentityAttribute {
    // All possible settings regarding the declaration of an identity.
    enum IdentityDeclaration {
      // Default value indicating that no selection was made.
      IDENTITY_DECLARATION_UNSPECIFIED = 0;

      // Indicates that the account identifies with the attribute.
      SELF_IDENTIFIES_AS = 1;

      // Indicates that the account does not identify with the attribute.
      DOES_NOT_SELF_IDENTIFY_AS = 2;
    }

    // Required. The declaration of identity for this attribute.
    IdentityDeclaration identity_declaration = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // All possible settings regarding promotions related to the business
  // identity.
  enum PromotionsConsent {
    // Default value indicating that no selection was made.
    PROMOTIONS_CONSENT_UNSPECIFIED = 0;

    // Indicates that the account consented to having their business identity
    // used for promotions.
    PROMOTIONS_CONSENT_GIVEN = 1;

    // Indicates that the account did not consent to having their business
    // identity used for promotions.
    PROMOTIONS_CONSENT_DENIED = 2;
  }

  // Identifier. The resource name of the business identity.
  // Format: `accounts/{account}/businessIdentity`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. Whether the identity attributes may be used for promotions.
  PromotionsConsent promotions_consent = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether the business identifies itself as being
  // black-owned. This optional field will only be available for merchants with
  // a business country set to `US`. It is also not applicable for marketplaces
  // or marketplace sellers.
  IdentityAttribute black_owned = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether the business identifies itself as being
  // women-owned. This optional field will only be available for merchants with
  // a business country set to `US`. It is also not applicable for marketplaces
  // or marketplace sellers.
  IdentityAttribute women_owned = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether the business identifies itself as being
  // veteran-owned. This optional field will only be available for merchants
  // with a business country set to `US`. It is also not applicable for
  // marketplaces or marketplace sellers.
  IdentityAttribute veteran_owned = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether the business identifies itself as being
  // latino-owned. This optional field will only be available for merchants with
  // a business country set to `US`. It is also not applicable for marketplaces
  // or marketplace sellers.
  IdentityAttribute latino_owned = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether the business identifies itself as a small
  // business. This optional field will only be available for merchants with a
  // business country set to `US`. It is also not applicable for marketplaces.
  IdentityAttribute small_business = 7 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `GetBusinessIdentity` method.
message GetBusinessIdentityRequest {
  // Required. The resource name of the business identity.
  // Format: `accounts/{account}/businessIdentity`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/BusinessIdentity"
    }
  ];
}

// Request message for the `UpdateBusinessIdentity` method.
message UpdateBusinessIdentityRequest {
  // Required. The new version of the business identity.
  BusinessIdentity business_identity = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields being updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}
