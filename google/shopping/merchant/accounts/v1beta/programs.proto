// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "ProgramsProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service for program management.
//
// Programs provide a mechanism for adding functionality to merchant accounts. A
// typical example of this is the [Free product
// listings](https://support.google.com/merchants/topic/9240261?ref_topic=7257954,7259405,&sjid=796648681813264022-EU)
// program, which enables products from a merchant's store to be shown across
// Google for free.
//
// This service exposes methods to retrieve a merchant's
// participation in all available programs, in addition to methods for
// explicitly enabling or disabling participation in each program.
service ProgramsService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves the specified program for the account.
  rpc GetProgram(GetProgramRequest) returns (Program) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/programs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves all programs for the account.
  rpc ListPrograms(ListProgramsRequest) returns (ListProgramsResponse) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{parent=accounts/*}/programs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Enable participation in the specified program for the account. Executing
  // this method requires admin access.
  rpc EnableProgram(EnableProgramRequest) returns (Program) {
    option (google.api.http) = {
      post: "/accounts/v1beta/{name=accounts/*/programs/*}:enable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Disable participation in the specified program for the account. Executing
  // this method requires admin access.
  rpc DisableProgram(DisableProgramRequest) returns (Program) {
    option (google.api.http) = {
      post: "/accounts/v1beta/{name=accounts/*/programs/*}:disable"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }
}

// Defines participation in a given program for the specified account.
//
// Programs provide a mechanism for adding functionality to merchant accounts. A
// typical example of this is the [Free product
// listings](https://support.google.com/merchants/topic/9240261?ref_topic=7257954,7259405,&sjid=796648681813264022-EU)
// program, which enables products from a merchant's store to be shown across
// Google for free.
message Program {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/Program"
    pattern: "accounts/{account}/programs/{program}"
    plural: "programs"
    singular: "program"
  };

  // Defines a requirement specified for participation in the program.
  message Requirement {
    // Output only. Name of the requirement.
    string title = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The URL of a help page describing the requirement.
    string documentation_uri = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The regions that are currently affected by this requirement
    // not being met.
    //
    // Region codes are defined by [CLDR](https://cldr.unicode.org/). This is
    // either a country where the program applies specifically to that country
    // or `001` when the program applies globally.
    repeated string affected_region_codes = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Possible program participation states for the account.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // The account is not eligible to participate in the program.
    NOT_ELIGIBLE = 1;

    // The account is eligible to participate in the program.
    ELIGIBLE = 2;

    // The program is enabled for the account.
    ENABLED = 3;
  }

  // Identifier. The resource name of the program.
  // Format: `accounts/{account}/programs/{program}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The URL of a Merchant Center help page describing the program.
  string documentation_uri = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The participation state of the account in the program.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The regions in which the account is actively participating in
  // the program. Active regions are defined as those where all program
  // requirements affecting the regions have been met.
  //
  // Region codes are defined by [CLDR](https://cldr.unicode.org/). This is
  // either a country where the program applies specifically to that country or
  // `001` when the program applies globally.
  repeated string active_region_codes = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The requirements that the account has not yet satisfied that
  // are affecting participation in the program.
  repeated Requirement unmet_requirements = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for the GetProgram method.
message GetProgramRequest {
  // Required. The name of the program to retrieve.
  // Format: `accounts/{account}/programs/{program}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Program"
    }
  ];
}

// Request message for the ListPrograms method.
message ListProgramsRequest {
  // Required. The name of the account for which to retrieve all programs.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/Program"
    }
  ];

  // Optional. The maximum number of programs to return in a single response. If
  // unspecified (or 0), a default size of 1000 is used. The maximum value is
  // 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A continuation token, received from a previous `ListPrograms`
  // call. Provide this to retrieve the next page.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the ListPrograms method.
message ListProgramsResponse {
  // The programs for the given account.
  repeated Program programs = 1;

  // A token that can be sent as `page_token` to retrieve the next page. If this
  // field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for the EnableProgram method.
message EnableProgramRequest {
  // Required. The name of the program for which to enable participation for the
  // given account. Format: `accounts/{account}/programs/{program}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Program"
    }
  ];
}

// Request message for the DisableProgram method.
message DisableProgramRequest {
  // Required. The name of the program for which to disable participation for
  // the given account. Format: `accounts/{account}/programs/{program}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Program"
    }
  ];
}
