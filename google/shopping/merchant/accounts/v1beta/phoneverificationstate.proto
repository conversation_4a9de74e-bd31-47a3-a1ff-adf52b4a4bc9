// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "PhoneVerificationStateProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// The phone verification state.
enum PhoneVerificationState {
  // Default value. This value is unused.
  PHONE_VERIFICATION_STATE_UNSPECIFIED = 0;

  // The phone is verified.
  PHONE_VERIFICATION_STATE_VERIFIED = 1;

  // The phone is unverified
  PHONE_VERIFICATION_STATE_UNVERIFIED = 2;
}
