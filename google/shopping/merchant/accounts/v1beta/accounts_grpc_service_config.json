{"methodConfig": [{"name": [{"service": "google.shopping.merchant.accounts.v1beta.AccountsService"}, {"service": "google.shopping.merchant.accounts.v1beta.AccountIssueService"}, {"service": "google.shopping.merchant.accounts.v1beta.AccountTaxService"}, {"service": "google.shopping.merchant.accounts.v1beta.AutofeedSettingsService"}, {"service": "google.shopping.merchant.accounts.v1beta.BusinessIdentityService"}, {"service": "google.shopping.merchant.accounts.v1beta.BusinessInfoService"}, {"service": "google.shopping.merchant.accounts.v1beta.EmailPreferencesService"}, {"service": "google.shopping.merchant.accounts.v1beta.HomepageService"}, {"service": "google.shopping.merchant.accounts.v1beta.OnlineReturnPolicyService"}, {"service": "google.shopping.merchant.accounts.v1beta.ProgramsService"}, {"service": "google.shopping.merchant.accounts.v1beta.RegionsService"}, {"service": "google.shopping.merchant.accounts.v1beta.ShippingSettingService"}, {"service": "google.shopping.merchant.accounts.v1beta.TermsOfServiceAgreementStateService"}, {"service": "google.shopping.merchant.accounts.v1beta.TermsOfServiceService"}, {"service": "google.shopping.merchant.accounts.v1beta.UserService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}