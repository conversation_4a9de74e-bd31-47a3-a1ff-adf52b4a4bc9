// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/shopping/type/types.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "AccountIssueProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to support `AccountIssueService` API.
service AccountIssueService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Lists all account issues of a Merchant Center account.
  rpc ListAccountIssues(ListAccountIssuesRequest)
      returns (ListAccountIssuesResponse) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{parent=accounts/*}/issues"
    };
    option (google.api.method_signature) = "parent";
  }
}

// An
// [`AccountIssue`](https://support.google.com/merchants/answer/********?sjid=17798438912526418908-EU#account).
message AccountIssue {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/AccountIssue"
    pattern: "accounts/{account}/issues/{issue}"
    plural: "accountIssues"
    singular: "accountIssue"
  };

  // The impact of the issue on a destination.
  message ImpactedDestination {
    // The impact of the issue on a region.
    message Impact {
      // The [CLDR region code](https://cldr.unicode.org/) where this issue
      // applies.
      string region_code = 1;

      // The severity of the issue on the destination and region.
      Severity severity = 2;
    }

    // The impacted reporting context.
    optional google.shopping.type.ReportingContext.ReportingContextEnum
        reporting_context = 1;

    // The (negative) impact for various regions on the given destination.
    repeated Impact impacts = 2;
  }

  // All possible issue severities.
  enum Severity {
    // The severity is unknown.
    SEVERITY_UNSPECIFIED = 0;

    // The issue causes offers to not serve.
    CRITICAL = 1;

    // The issue might affect offers (in the future) or might be an
    // indicator of issues with offers.
    ERROR = 2;

    // The issue is a suggestion for improvement.
    SUGGESTION = 3;
  }

  // Identifier. The resource name of the account issue.
  // Format: `accounts/{account}/issues/{id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The localized title of the issue.
  string title = 2;

  // The overall severity of the issue.
  Severity severity = 3;

  // The impact this issue has on various destinations.
  repeated ImpactedDestination impacted_destinations = 4;

  // Further localized details about the issue.
  string detail = 5;

  // Link to Merchant Center Help Center providing further information about the
  // issue and how to fix it.
  string documentation_uri = 6;
}

// Request message for the `ListAccountIssues` method.
message ListAccountIssuesRequest {
  // Required. The parent, which owns this collection of issues.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Account"
    }
  ];

  // Optional. The maximum number of issues to return. The service may return
  // fewer than this value. If unspecified, at most 50 users will be returned.
  // The maximum value is 100; values above 100 will be coerced to 100
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListAccountIssues` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAccountIssues` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The issues in the response will have human-readable fields in the
  // given language. The format is [BCP-47](https://tools.ietf.org/html/bcp47),
  // such as `en-US` or `sr-Latn`. If not value is provided, `en-US` will be
  // used.
  string language_code = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The [IANA](https://www.iana.org/time-zones) timezone used to
  // localize times in human-readable fields. For example 'America/Los_Angeles'.
  // If not set, 'America/Los_Angeles' will be used.
  string time_zone = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `ListAccountIssues` method.
message ListAccountIssuesResponse {
  // The issues from the specified account.
  repeated AccountIssue account_issues = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
