// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/shopping/merchant/accounts/v1beta/accountservices.proto";
import "google/shopping/merchant/accounts/v1beta/user.proto";
import "google/type/datetime.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "AccountsProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to support Accounts API.
service AccountsService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves an account from your Merchant Center account.
  // After inserting, updating, or deleting an account, it may take several
  // minutes before changes take effect.
  rpc GetAccount(GetAccountRequest) returns (Account) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a standalone Merchant Center account with additional configuration.
  // Adds the user that makes the request as an admin for the new account.
  rpc CreateAndConfigureAccount(CreateAndConfigureAccountRequest)
      returns (Account) {
    option (google.api.http) = {
      post: "/accounts/v1beta/accounts:createAndConfigure"
      body: "*"
    };
  }

  // Deletes the specified account regardless of its type: standalone, MCA or
  // sub-account. Deleting an MCA leads to the deletion of all of its
  // sub-accounts. Executing this method requires admin access.
  // The deletion succeeds only if the account does not provide services
  // to any other account and has no processed offers. You can use the `force`
  // parameter to override this.
  rpc DeleteAccount(DeleteAccountRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/accounts/v1beta/{name=accounts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an account regardless of its type: standalone, MCA or sub-account.
  // Executing this method requires admin access.
  rpc UpdateAccount(UpdateAccountRequest) returns (Account) {
    option (google.api.http) = {
      patch: "/accounts/v1beta/{account.name=accounts/*}"
      body: "account"
    };
    option (google.api.method_signature) = "account,update_mask";
  }

  // Lists accounts accessible to the calling user and matching the
  // constraints of the request such as page size or filters.
  // This is not just listing the sub-accounts of an MCA, but all accounts the
  // calling user has access to including other MCAs, linked accounts,
  // standalone accounts and so on. If no filter is provided, then it returns
  // accounts the user is directly added to.
  rpc ListAccounts(ListAccountsRequest) returns (ListAccountsResponse) {
    option (google.api.http) = {
      get: "/accounts/v1beta/accounts"
    };
  }

  // List all sub-accounts for a given multi client account. This is a
  // convenience wrapper for the more powerful `ListAccounts` method. This
  // method will produce the same results as calling `ListsAccounts` with the
  // following filter:
  // `relationship(providerId={parent} AND service(type="ACCOUNT_AGGREGATION"))`
  rpc ListSubAccounts(ListSubAccountsRequest)
      returns (ListSubAccountsResponse) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{provider=accounts/*}:listSubaccounts"
    };
    option (google.api.method_signature) = "provider";
  }
}

// An account.
message Account {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/Account"
    pattern: "accounts/{account}"
  };

  // Identifier. The resource name of the account.
  // Format: `accounts/{account}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The ID of the account.
  int64 account_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. A human-readable name of the account. See
  // [store name](https://support.google.com/merchants/answer/160556) and
  // [business name](https://support.google.com/merchants/answer/********) for
  // more information.
  string account_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Whether this account contains adult content.
  bool adult_content = 4;

  // Output only. Whether this is a test account.
  bool test_account = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The time zone of the account.
  //
  // On writes, `time_zone` sets both the `reporting_time_zone` and the
  // `display_time_zone`.
  //
  // For reads, `time_zone` always returns the `display_time_zone`. If
  // `display_time_zone` doesn't exist for your account, `time_zone` is empty.
  google.type.TimeZone time_zone = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. The account's [BCP-47 language
  // code](https://tools.ietf.org/html/bcp47), such as `en-US` or `sr-Latn`.
  string language_code = 7 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `GetAccount` method.
message GetAccountRequest {
  // Required. The name of the account to retrieve.
  // Format: `accounts/{account}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Account"
    }
  ];
}

// Request message for the `CreateAndConfigureAccount` method.
message CreateAndConfigureAccountRequest {
  // Reference to a Terms of Service resource.
  message AcceptTermsOfService {
    // Required. The resource name of the terms of service version in the format
    // `termsOfService/{version}`. To retrieve the latest version, use the
    // [termsOfService.retrieveLatest](/merchant/api/reference/rest/accounts_v1beta/termsOfService/retrieveLatest)
    // method.
    string name = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "merchantapi.googleapis.com/TermsOfService"
      }
    ];

    // Required. Region code as defined by [CLDR](https://cldr.unicode.org/).
    // This is either a country when the ToS applies specifically to that
    // country or `001` when it applies globally.
    string region_code = 3 [(google.api.field_behavior) = REQUIRED];
  }

  // Additional instructions to add account services during creation of the
  // account.
  message AddAccountService {
    // The service type to be added.
    oneof service_type {
      // The provider is an
      // [aggregator](https://support.google.com/merchants/answer/188487) for
      // the account. Payload for service type Account Aggregation.
      AccountAggregation account_aggregation = 103;
    }

    // Optional. The provider of the service.
    // Format: `accounts/{account}`
    optional string provider = 1 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "merchantapi.googleapis.com/Account"
      }
    ];
  }

  // Required. The account to be created.
  Account account = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Users to be added to the account.
  repeated CreateUserRequest users = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The Terms of Service (ToS) to be accepted immediately upon
  // account creation.
  optional AcceptTermsOfService accept_terms_of_service = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Required. An account service between the account to be created and the
  // provider account is initialized as part of the creation. At least one such
  // service needs to be provided. Currently exactly one of these needs to be
  // `account_aggregation`, which means you can only create sub accounts, not
  // standalone account through this method. Additional `account_management` or
  // `product_management` services may be provided.
  repeated AddAccountService service = 4
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `DeleteAccount` method.
message DeleteAccountRequest {
  // Required. The name of the account to delete.
  // Format: `accounts/{account}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Account"
    }
  ];

  // Optional. If set to `true`, the account is deleted even if it provides
  // services to other accounts or has processed offers.
  bool force = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `UpdateAccount` method.
message UpdateAccountRequest {
  // Required. The new version of the account.
  Account account = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields being updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `ListAccounts` method.
message ListAccountsRequest {
  // Optional. The maximum number of accounts to return. The service may return
  // fewer than this value.  If unspecified, at most 250 accounts are returned.
  // The maximum value is 500; values above 500 are coerced to 500.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListAccounts` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAccounts` must match
  // the call that provided the page token.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Returns only accounts that match the
  // [filter](/merchant/api/guides/accounts/filter).
  // For more details, see the
  // [filter syntax reference](/merchant/api/guides/accounts/filter-syntax).
  string filter = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `ListAccounts` method.
message ListAccountsResponse {
  // The accounts matching the `ListAccountsRequest`.
  repeated Account accounts = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for the `ListSubAccounts` method.
message ListSubAccountsRequest {
  // Required. The parent account.
  // Format: `accounts/{account}`
  string provider = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Account"
    }
  ];

  // Optional. The maximum number of accounts to return. The service may return
  // fewer than this value.  If unspecified, at most 250 accounts are returned.
  // The maximum value is 500; values above 500 are coerced to 500.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListAccounts` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAccounts` must match
  // the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `ListSubAccounts` method.
message ListSubAccountsResponse {
  // The accounts for which the given parent account is an aggregator.
  repeated Account accounts = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
