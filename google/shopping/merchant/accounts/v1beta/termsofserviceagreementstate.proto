// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/shopping/merchant/accounts/v1beta/termsofservicekind.proto";
import "google/type/date.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "TermsOfServiceAgreementStateProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to support `TermsOfServiceAgreementState` API.
service TermsOfServiceAgreementStateService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Returns the state of a terms of service agreement.
  rpc GetTermsOfServiceAgreementState(GetTermsOfServiceAgreementStateRequest)
      returns (TermsOfServiceAgreementState) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/termsOfServiceAgreementStates/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves the state of the agreement for the application terms of service.
  rpc RetrieveForApplicationTermsOfServiceAgreementState(
      RetrieveForApplicationTermsOfServiceAgreementStateRequest)
      returns (TermsOfServiceAgreementState) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{parent=accounts/*}/termsOfServiceAgreementStates:retrieveForApplication"
    };
    option (google.api.method_signature) = "parent";
  }
}

// This resource represents the agreement state for a given account and terms of
// service kind.
// The state is as follows:
//
// * If the merchant has accepted a terms of service:
// [accepted](TermsOfServiceAggrementState.accepted) will be populated,
// otherwise it will be empty
// * If the merchant must sign a terms of service:
// [required](TermsOfServiceAggrementState.required) will be populated,
// otherwise it will be empty.
//
// Note that both [required](TermsOfServiceAggrementState.required) and
// [accepted](TermsOfServiceAggrementState.accepted) can be present. In this
// case the `accepted` terms of services will have an expiration date set in the
// [valid_until](Accepted.valid_until) field. The `required` terms of services
// need to be accepted before `valid_until` in order for the account to continue
// having a valid agreement. When accepting new terms of services we expect 3Ps
// to display the text associated with the given terms of service agreement (the
// url to the file containing the text is added in the Required message below as
// [tos_file_uri](Accepted.tos_file_uri). The actual acceptance of the terms of
// service is done by calling accept on the [TermsOfService](TermsOfService)
// resource.
message TermsOfServiceAgreementState {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/TermsOfServiceAgreementState"
    pattern: "accounts/{account}/termsOfServiceAgreementStates/{identifier}"
    plural: "termsOfServiceAgreementStates"
    singular: "termsOfServiceAgreementState"
  };

  // Identifier. The resource name of the terms of service version.
  // Format: `accounts/{account}/termsOfServiceAgreementState/{identifier}`
  // The identifier format is: `{TermsOfServiceKind}-{country}`
  // For example, an identifier could be: `MERCHANT_CENTER-US`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Region code as defined by https://cldr.unicode.org/. This is the
  // country the current state applies to.
  string region_code = 2;

  // Terms of Service kind associated with the particular version.
  TermsOfServiceKind terms_of_service_kind = 3;

  // The accepted terms of service of this kind and for the associated
  // region_code
  optional Accepted accepted = 4;

  // The required terms of service
  optional Required required = 5;
}

// Describes the accepted terms of service.
message Accepted {
  // The accepted
  // [termsOfService](google.shopping.merchant.accounts.v1main.TermsOfService).
  string terms_of_service = 1 [(google.api.resource_reference) = {
    type: "merchantapi.googleapis.com/TermsOfService"
  }];

  // The account where the acceptance was recorded. This can be the account
  // itself or, in the case of subaccounts, the MCA account.
  string accepted_by = 2 [(google.api.resource_reference) = {
    type: "merchantapi.googleapis.com/Account"
  }];

  // When set, it states that the accepted
  // [TermsOfService](google.shopping.merchant.accounts.v1main.TermsOfService)
  // is only valid until the end of this date (in UTC). A new one must be
  // accepted before then. The information of the required
  // [TermsOfService](google.shopping.merchant.accounts.v1main.TermsOfService)
  // is found in the [Required](Required) message.
  optional google.type.Date valid_until = 3;
}

// Describes the terms of service which are required to be accepted.
message Required {
  // The
  // [termsOfService](google.shopping.merchant.accounts.v1main.TermsOfService)
  // that need to be accepted.
  string terms_of_service = 1 [(google.api.resource_reference) = {
    type: "merchantapi.googleapis.com/TermsOfService"
  }];

  // Full URL to the terms of service file. This field is the same as
  // [TermsOfService.file_uri](TermsOfService.file_uri), it is added
  // here for convenience only.
  string tos_file_uri = 2;
}

// Request message for the `GetTermsOfServiceAgreementState` method.
message GetTermsOfServiceAgreementStateRequest {
  // Required. The resource name of the terms of service version.
  // Format: `accounts/{account}/termsOfServiceAgreementState/{identifier}`
  // The identifier format is: `{TermsOfServiceKind}-{country}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/TermsOfServiceAgreementState"
    }
  ];
}

// Request message for the `RetrieveForApplicationTermsOfServiceAgreementState`
// method.
message RetrieveForApplicationTermsOfServiceAgreementStateRequest {
  // Required. The account for which to get a TermsOfServiceAgreementState
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/TermsOfServiceAgreementState"
    }
  ];
}
