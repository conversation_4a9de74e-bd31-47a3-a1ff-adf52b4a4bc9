// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/shopping/type/types.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "OnlineReturnPolicyProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// The service facilitates the management of a merchant's remorse return policy
// configuration, encompassing return policies for both ads and free listings
// ## programs. This API defines the following resource model:
//
// [OnlineReturnPolicy][google.shopping.merchant.accounts.v1.OnlineReturnPolicy]
service OnlineReturnPolicyService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Gets an existing return policy.
  rpc GetOnlineReturnPolicy(GetOnlineReturnPolicyRequest)
      returns (OnlineReturnPolicy) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/onlineReturnPolicies/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all existing return policies.
  rpc ListOnlineReturnPolicies(ListOnlineReturnPoliciesRequest)
      returns (ListOnlineReturnPoliciesResponse) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{parent=accounts/*}/onlineReturnPolicies"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Request message for the `GetOnlineReturnPolicy` method.
message GetOnlineReturnPolicyRequest {
  // Required. The name of the return policy to retrieve.
  // Format: `accounts/{account}/onlineReturnPolicies/{return_policy}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/OnlineReturnPolicy"
    }
  ];
}

// Request message for the `ListOnlineReturnPolicies` method.
message ListOnlineReturnPoliciesRequest {
  // Required. The merchant account for which to list return policies.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/OnlineReturnPolicy"
    }
  ];

  // Optional. The maximum number of `OnlineReturnPolicy` resources to return.
  // The service returns fewer than this value if the number of return policies
  // for the given merchant is less that than the `pageSize`. The default value
  // is 10. The maximum value is 100; If a value higher than the maximum is
  // specified, then the `pageSize` will default to the maximum
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListOnlineReturnPolicies`
  // call. Provide the page token to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListOnlineReturnPolicies` must match the call that provided the page
  // token. The token returned as
  // [nextPageToken][google.shopping.merchant.accounts.v1beta.ListOnlineReturnPoliciesResponse.next_page_token]
  // in the response to the previous request.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `ListOnlineReturnPolicies` method.
message ListOnlineReturnPoliciesResponse {
  // The retrieved return policies.
  repeated OnlineReturnPolicy online_return_policies = 1;

  // A token, which can be sent as `pageToken` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// [Online return policy](https://support.google.com/merchants/answer/********)
// object. This is currently used to represent return policies for ads and free
// listings programs.
message OnlineReturnPolicy {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/OnlineReturnPolicy"
    pattern: "accounts/{account}/onlineReturnPolicies/{return_policy}"
    plural: "onlineReturnPolicies"
    singular: "onlineReturnPolicy"
  };

  // The return shipping fee. This can either be a fixed fee or a boolean to
  // indicate that the customer pays the actual shipping cost.
  message ReturnShippingFee {
    // Return shipping fee types.
    enum Type {
      // Default value. This value is unused.
      TYPE_UNSPECIFIED = 0;

      // The return shipping fee is a fixed value.
      FIXED = 1;

      // Customers will pay the actual return shipping fee.
      CUSTOMER_PAYING_ACTUAL_FEE = 2;
    }

    // Type of return shipping fee.
    Type type = 1;

    // Fixed return shipping fee amount. This value is only applicable when type
    // is `FIXED`. We will treat the return shipping fee as free if type is
    // `FIXED` and this value is not set.
    google.shopping.type.Price fixed_fee = 2;
  }

  // The restocking fee. This can be a flat fee or a micro percent.
  message RestockingFee {
    oneof type {
      // Fixed restocking fee.
      google.shopping.type.Price fixed_fee = 1;

      // Percent of total price in micros. 15,000,000 means 15% of the total
      // price would be charged.
      int32 micro_percent = 2;
    }
  }

  // The available policies.
  message Policy {
    // Return policy types.
    enum Type {
      // Default value. This value is unused.
      TYPE_UNSPECIFIED = 0;

      // The number of days within which a return is valid after delivery.
      NUMBER_OF_DAYS_AFTER_DELIVERY = 1;

      // No returns.
      NO_RETURNS = 2;

      // Life time returns.
      LIFETIME_RETURNS = 3;
    }

    // Policy type.
    Type type = 1;

    // The number of days items can be returned after delivery, where one day
    // is defined as 24 hours after the delivery timestamp. Required for
    // `NUMBER_OF_DAYS_AFTER_DELIVERY` returns.
    int64 days = 2;
  }

  // The available return methods.
  enum ReturnMethod {
    // Default value. This value is unused.
    RETURN_METHOD_UNSPECIFIED = 0;

    // Return by mail.
    BY_MAIL = 1;

    // Return in store.
    IN_STORE = 2;

    // Return at a kiosk.
    AT_A_KIOSK = 3;
  }

  // The available item conditions.
  enum ItemCondition {
    // Default value. This value is unused.
    ITEM_CONDITION_UNSPECIFIED = 0;

    // New.
    NEW = 1;

    // Used.
    USED = 2;
  }

  // Identifier. The name of the `OnlineReturnPolicy` resource.
  // Format: `accounts/{account}/onlineReturnPolicies/{return_policy}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Return policy ID generated by Google.
  string return_policy_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // This field represents the unique user-defined label of the return policy.
  // It is important to note that the same label cannot be used in different
  // return policies for the same country. Unless a product specifies a specific
  // label attribute, policies will be automatically labeled as 'default'.
  // To assign a custom return policy to certain product groups, follow the
  // instructions provided in the [Return policy label]
  // (https://support.google.com/merchants/answer/9445425).
  // The label can contain up to 50 characters.
  string label = 3;

  // The countries of sale where the return policy applies. The values
  // must be a valid 2 letter ISO 3166 code.
  repeated string countries = 4;

  // The return policy.
  Policy policy = 5;

  // The restocking fee that applies to all return reason categories. This would
  // be treated as a free restocking fee if the value is not set.
  RestockingFee restocking_fee = 6;

  // The return methods of how customers can return an item. This value is
  // required to not be empty unless the type of return policy is noReturns.
  repeated ReturnMethod return_methods = 7;

  // The item conditions accepted for returns must not be empty unless the type
  // of return policy is 'noReturns'.
  repeated ItemCondition item_conditions = 8;

  // The return shipping fee. Should be set only when customer need to download
  // and print the return label.
  ReturnShippingFee return_shipping_fee = 9;

  // The return policy uri. This can used by Google to do a sanity check for the
  // policy. It must be a valid URL.
  string return_policy_uri = 10;

  // This field specifies if merchant only accepts defective products for
  // returns, and this field is required.
  optional bool accept_defective_only = 11;

  // The field specifies the number of days it takes for merchants to process
  // refunds, field is optional.
  optional int32 process_refund_days = 12;

  // This field specifies if merchant allows customers to exchange products,
  // this field is required.
  optional bool accept_exchange = 13;
}
