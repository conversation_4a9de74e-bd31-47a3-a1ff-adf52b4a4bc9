// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "HomepageProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to support an API for a store's homepage.
service HomepageService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves a store's homepage.
  rpc GetHomepage(GetHomepageRequest) returns (Homepage) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/homepage}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates a store's homepage. Executing this method requires admin access.
  rpc UpdateHomepage(UpdateHomepageRequest) returns (Homepage) {
    option (google.api.http) = {
      patch: "/accounts/v1beta/{homepage.name=accounts/*/homepage}"
      body: "homepage"
    };
    option (google.api.method_signature) = "homepage,update_mask";
  }

  // Claims a store's homepage. Executing this method requires admin access.
  //
  // If the homepage is already claimed, this will recheck the
  // verification (unless the merchant is exempted from claiming, which also
  // exempts from verification) and return a successful response. If ownership
  // can no longer be verified, it will return an error, but it won't clear the
  // claim. In case of failure, a canonical error message will be returned:
  //    * PERMISSION_DENIED: user doesn't have the necessary permissions on this
  //    MC account;
  //    * FAILED_PRECONDITION:
  //      - The account is not a Merchant Center account;
  //      - MC account doesn't have a homepage;
  //      - claiming failed (in this case the error message will contain more
  //      details).
  rpc ClaimHomepage(ClaimHomepageRequest) returns (Homepage) {
    option (google.api.http) = {
      post: "/accounts/v1beta/{name=accounts/*/homepage}:claim"
      body: "*"
    };
  }

  // Unclaims a store's homepage. Executing this method requires admin access.
  rpc UnclaimHomepage(UnclaimHomepageRequest) returns (Homepage) {
    option (google.api.http) = {
      post: "/accounts/v1beta/{name=accounts/*/homepage}:unclaim"
      body: "*"
    };
  }
}

// A store's homepage.
message Homepage {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/Homepage"
    pattern: "accounts/{account}/homepage"
    plural: "homepages"
    singular: "homepage"
  };

  // Identifier. The resource name of the store's homepage.
  // Format: `accounts/{account}/homepage`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The URI (typically a URL) of the store's homepage.
  optional string uri = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Whether the homepage is claimed. See
  // https://support.google.com/merchants/answer/176793.
  bool claimed = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for the `GetHomepage` method.
message GetHomepageRequest {
  // Required. The name of the homepage to retrieve.
  // Format: `accounts/{account}/homepage`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Homepage"
    }
  ];
}

// Request message for the `UpdateHomepage` method.
message UpdateHomepageRequest {
  // Required. The new version of the homepage.
  Homepage homepage = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. List of fields being updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `ClaimHomepage` method.
message ClaimHomepageRequest {
  // Required. The name of the homepage to claim.
  // Format: `accounts/{account}/homepage`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Homepage"
    }
  ];
}

// Request message for the `UnclaimHomepage` method.
message UnclaimHomepageRequest {
  // Required. The name of the homepage to unclaim.
  // Format: `accounts/{account}/homepage`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Homepage"
    }
  ];
}
