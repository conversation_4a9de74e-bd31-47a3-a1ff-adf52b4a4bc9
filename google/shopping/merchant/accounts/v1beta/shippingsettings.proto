// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/shopping/type/types.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "ShippingSettingsProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Service to get method call shipping setting information per Merchant API
// method.
service ShippingSettingsService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieve shipping setting information.
  rpc GetShippingSettings(GetShippingSettingsRequest)
      returns (ShippingSettings) {
    option (google.api.http) = {
      get: "/accounts/v1beta/{name=accounts/*/shippingSettings}"
    };
    option (google.api.method_signature) = "name";
  }

  // Replace the shipping setting of a merchant with the request shipping
  // setting. Executing this method requires admin access.
  rpc InsertShippingSettings(InsertShippingSettingsRequest)
      returns (ShippingSettings) {
    option (google.api.http) = {
      post: "/accounts/v1beta/{parent=accounts/*}/shippingSettings:insert"
      body: "shipping_setting"
    };
  }
}

// The merchant account's [shipping
// setting](https://support.google.com/merchants/answer/6069284).
message ShippingSettings {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/ShippingSettings"
    pattern: "accounts/{account}/shippingSettings"
    plural: "shippingSettings"
    singular: "shippingSettings"
  };

  // Identifier. The resource name of the shipping setting.
  // Format: `accounts/{account}/shippingSetting`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Optional. The target account's list of services.
  repeated Service services = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of warehouses which can be referred to in `services`.
  repeated Warehouse warehouses = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. This field is used for avoid async issue. Make sure shipping
  // setting data
  //  didn't change between get call and insert call. The user should do
  //  following steps：
  //
  // 1. Set etag field as empty string for initial shipping setting creation.
  //
  // 2. After initial creation, call get method to obtain an etag and current
  // shipping setting data before call insert.
  //
  // 3. Modify to wanted shipping setting information.
  //
  // 4. Call insert method with the wanted shipping setting information with
  // the etag obtained from step 2.
  //
  // 5. If shipping setting data changed between step 2 and step 4. Insert
  // request will fail because the etag changes every time the shipping setting
  // data changes. User should repeate step 2-4 with the new etag.
  string etag = 4 [(google.api.field_behavior) = REQUIRED];
}

// Shipping service.
message Service {
  // A list of stores your products are delivered from.
  // This is only valid for the local delivery shipment type.
  message StoreConfig {
    // Configs related to local delivery ends for the day.
    message CutoffConfig {
      // Time that local delivery ends for the day.
      message LocalCutoffTime {
        // Hour local delivery orders must be placed by to process the same
        // day.
        optional int64 hour = 1;

        // Minute local delivery orders must be placed by to process the same
        // day.
        optional int64 minute = 2;
      }

      // Time that local delivery ends for the day.
      optional LocalCutoffTime local_cutoff_time = 1;

      // Only valid with local delivery fulfillment. Represents cutoff time
      // as the number of hours before store closing. Mutually exclusive
      // with `local_cutoff_time`.
      optional int64 store_close_offset_hours = 2;

      // Merchants can opt-out of showing n+1 day local delivery when they have
      // a shipping service configured to n day local delivery. For example, if
      // the shipping service defines same-day delivery, and it's past the
      // cut-off, setting this field to `true` results in the calculated
      // shipping service rate returning `NO_DELIVERY_POST_CUTOFF`. In the
      // same example, setting this field to `false` results in the calculated
      // shipping time being one day. This is only for local delivery.
      optional bool no_delivery_post_cutoff = 3;
    }

    // Indicates whether all stores, or selected stores, listed by the
    // merchant provide local delivery.
    enum StoreServiceType {
      // Did not specify store service type.
      STORE_SERVICE_TYPE_UNSPECIFIED = 0;

      // Indicates whether all stores, current and future, listed by this
      // merchant provide local delivery.
      ALL_STORES = 1;

      // Indicates that only the stores listed in `store_codes` are eligible
      // for local delivery.
      SELECTED_STORES = 2;
    }

    // Indicates whether all stores, or selected stores, listed by this
    // merchant provide local delivery.
    optional StoreServiceType store_service_type = 1;

    // Optional. A list of store codes that provide local delivery.
    // If empty, then `all_stores` must be true.
    repeated string store_codes = 2 [(google.api.field_behavior) = OPTIONAL];

    // Configs related to local delivery ends for the day.
    optional CutoffConfig cutoff_config = 3;

    // Maximum delivery radius.
    // This is only required for the local delivery shipment type.
    optional Distance service_radius = 4;
  }

  // [Loyalty program](https://support.google.com/merchants/answer/12922446)
  // provided by a merchant.
  message LoyaltyProgram {
    // Subset of a merchants loyalty program.
    message LoyaltyProgramTiers {
      // The tier label [tier_label] sub-attribute differentiates offer level
      // benefits between each tier. This value is also set in your program
      // settings in Merchant Center, and is required for data source changes
      // even if your loyalty program only has 1 tier.
      optional string tier_label = 1;
    }

    // This is the loyalty program label set in your loyalty program settings in
    // Merchant Center. This sub-attribute allows Google to map your loyalty
    // program to eligible offers.
    optional string program_label = 1;

    // Optional. Loyalty program tier of this shipping service.
    repeated LoyaltyProgramTiers loyalty_program_tiers = 2
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Shipment type of shipping service.
  enum ShipmentType {
    // This service did not specify shipment type.
    SHIPMENT_TYPE_UNSPECIFIED = 0;

    // This service ships orders to an address chosen by the customer.
    DELIVERY = 1;

    // This service ships orders to an address chosen by the customer.
    // The order is shipped from a local store near by.
    LOCAL_DELIVERY = 2;

    // This service ships orders to an address chosen by the customer.
    // The order is shipped from a collection point.
    COLLECTION_POINT = 3;
  }

  // Required. Free-form name of the service. Must be unique within target
  // account.
  optional string service_name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. A boolean exposing the active status of the shipping service.
  optional bool active = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The CLDR territory code of the countries to which the service
  // applies.
  repeated string delivery_countries = 3
      [(google.api.field_behavior) = REQUIRED];

  // The CLDR code of the currency to which this service applies. Must match
  // that of the prices in rate groups.
  optional string currency_code = 4;

  // Required. Time spent in various aspects from order to the delivery of the
  // product.
  optional DeliveryTime delivery_time = 5
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Shipping rate group definitions. Only the last one is allowed to
  // have an empty `applicable_shipping_labels`, which means "everything else".
  // The other `applicable_shipping_labels` must not overlap.
  repeated RateGroup rate_groups = 6 [(google.api.field_behavior) = OPTIONAL];

  // Type of locations this service ships orders to.
  optional ShipmentType shipment_type = 7;

  // Minimum order value for this service. If set, indicates that customers
  // will have to spend at least this amount.
  // All prices within a service must have the same currency.
  // Cannot be set together with minimum_order_value_table.
  optional google.shopping.type.Price minimum_order_value = 8;

  // Table of per store minimum order values for the pickup fulfillment type.
  // Cannot be set together with minimum_order_value.
  optional MinimumOrderValueTable minimum_order_value_table = 9;

  // A list of stores your products are delivered from.
  // This is only valid for the local delivery shipment type.
  optional StoreConfig store_config = 10;

  // Optional. Loyalty programs that this shipping service is limited to.
  repeated LoyaltyProgram loyalty_programs = 11
      [(google.api.field_behavior) = OPTIONAL];
}

// Maximum delivery radius.
// This is only required for the local delivery shipment type.
message Distance {
  // Unit can differ based on country, it is parameterized to include miles
  // and kilometers.
  enum Unit {
    // Unit unspecified
    UNIT_UNSPECIFIED = 0;

    // Unit in miles
    MILES = 1;

    // Unit in kilometers
    KILOMETERS = 2;
  }

  // Integer value of distance.
  optional int64 value = 1;

  // Unit can differ based on country, it is parameterized to include miles
  // and kilometers.
  optional Unit unit = 2;
}

// A fulfillment warehouse, which stores and handles inventory.
// Next tag: 7
message Warehouse {
  // Required. The name of the warehouse. Must be unique within account.
  optional string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Shipping address of the warehouse.
  optional Address shipping_address = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The latest time of day that an order can be accepted and begin
  // processing. Later orders will be processed in the next day. The time is
  // based on the warehouse postal code.
  optional WarehouseCutoffTime cutoff_time = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. The number of days it takes for this warehouse to pack up and
  // ship an item. This is on the warehouse level, but can be overridden on the
  // offer level based on the attributes of an item.
  optional int64 handling_days = 4 [(google.api.field_behavior) = REQUIRED];

  // Business days of the warehouse.
  // If not set, will be Monday to Friday by default.
  optional BusinessDayConfig business_day_config = 5;
}

// The latest time of day that an order can be accepted and begin processing.
// Later orders will be processed in the next day. The time is based on the
// warehouse postal code.
message WarehouseCutoffTime {
  // Required. Hour of the cutoff time until which an order has to be placed to
  // be processed in the same day by the warehouse. Hour is based on the
  // timezone of warehouse.
  optional int32 hour = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Minute of the cutoff time until which an order has to be placed
  // to be processed in the same day by the warehouse. Minute is based on the
  // timezone of warehouse.
  optional int32 minute = 2 [(google.api.field_behavior) = REQUIRED];
}

// Shipping address of the warehouse.
message Address {
  // Street-level part of the address. For example: `111w 31st Street`.
  optional string street_address = 1;

  // Required. City, town or commune. May also include dependent localities or
  // sublocalities (For example neighborhoods or suburbs).
  optional string city = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Top-level administrative subdivision of the country. For example,
  // a state like California ("CA") or a province like Quebec ("QC").
  optional string administrative_area = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. Postal code or ZIP (For example "94043").
  optional string postal_code = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. [CLDR country
  // code](http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml)
  // (For example "US").
  optional string region_code = 5 [(google.api.field_behavior) = REQUIRED];
}

// Time spent in various aspects from order to the delivery of the product.
message DeliveryTime {
  // Minimum number of business days that is spent in transit. 0 means same
  // day delivery, 1 means next day delivery.
  // Either `min_transit_days`, `max_transit_days` or
  // `transit_time_table` must be set, but not both.
  optional int32 min_transit_days = 1;

  // Maximum number of business days that is spent in transit. 0 means same
  // day delivery, 1 means next day delivery. Must be greater than or equal
  // to `min_transit_days`.
  optional int32 max_transit_days = 2;

  // Business days cutoff time definition.
  // If not configured the cutoff time will be defaulted to 8AM PST.
  optional CutoffTime cutoff_time = 3;

  // Minimum number of business days spent before an order is shipped.
  // 0 means same day shipped, 1 means next day shipped.
  // 'min_handling_days' and 'max_handling_days' should be either set or not set
  // at the same time.
  optional int32 min_handling_days = 4;

  // Maximum number of business days spent before an order is shipped.
  // 0 means same day shipped, 1 means next day shipped.
  // Must be greater than or equal to `min_handling_days`.
  // 'min_handling_days' and 'max_handling_days' should be either set or not set
  // at the same time.
  optional int32 max_handling_days = 5;

  // Transit time table, number of business days spent in transit based on row
  // and column dimensions. Either `min_transit_days`, `max_transit_days` or
  // `transit_time_table` can be set, but not both.
  optional TransitTable transit_time_table = 6;

  // The business days during which orders can be handled.
  // If not provided, Monday to Friday business days will be assumed.
  optional BusinessDayConfig handling_business_day_config = 7;

  // The business days during which orders can be in-transit.
  // If not provided, Monday to Friday business days will be assumed.
  optional BusinessDayConfig transit_business_day_config = 8;

  // Optional. Indicates that the delivery time should be calculated per
  // warehouse (shipping origin location) based on the settings of the selected
  // carrier. When set, no other transit time related field in [delivery
  // time][[google.shopping.content.bundles.ShippingSetting.DeliveryTime] should
  // be set.
  repeated WarehouseBasedDeliveryTime warehouse_based_delivery_times = 9
      [(google.api.field_behavior) = OPTIONAL];
}

// Business days cutoff time definition.
message CutoffTime {
  // Required. Hour of the cutoff time until which an order has to be placed to
  // be processed in the same day.
  optional int32 hour = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Minute of the cutoff time until which an order has to be placed
  // to be processed in the same day.
  optional int32 minute = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. [Timezone
  // identifier](https://developers.google.com/adwords/api/docs/appendix/codes-formats#timezone-ids)
  // For example "Europe/Zurich".
  optional string time_zone = 3 [(google.api.field_behavior) = REQUIRED];
}

// Business days of the warehouse.
message BusinessDayConfig {
  enum Weekday {
    WEEKDAY_UNSPECIFIED = 0;

    MONDAY = 1;

    TUESDAY = 2;

    WEDNESDAY = 3;

    THURSDAY = 4;

    FRIDAY = 5;

    SATURDAY = 6;

    SUNDAY = 7;
  }

  // Required. Regular business days.
  // May not be empty.
  repeated Weekday business_days = 1 [(google.api.field_behavior) = REQUIRED];
}

// Indicates that the delivery time should be calculated per warehouse
// (shipping origin location) based on the settings of the selected carrier.
// When set, no other transit time related field in `delivery_time` should be
// set.
message WarehouseBasedDeliveryTime {
  // Required. Carrier, such as `"UPS"` or `"Fedex"`.
  optional string carrier = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Carrier service, such as `"ground"` or `"2 days"`. The name of
  // the service must be in the eddSupportedServices list.
  optional string carrier_service = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Warehouse name. This should match
  // [warehouse][ShippingSetting.warehouses.name]
  optional string warehouse = 3 [(google.api.field_behavior) = REQUIRED];
}

// Shipping rate group definitions. Only the last one is allowed to have an
// empty `applicable_shipping_labels`, which means
// "everything else". The other `applicable_shipping_labels` must
// not overlap.
message RateGroup {
  // Required. A list of [shipping
  // labels](https://support.google.com/merchants/answer/6324504) defining the
  // products to which this rate group applies to. This is a disjunction: only
  // one of the labels has to match for the rate group to apply. May only be
  // empty for the last rate group of a service.
  repeated string applicable_shipping_labels = 1
      [(google.api.field_behavior) = REQUIRED];

  // The value of the rate group (For example flat rate $10). Can only be set
  // if `main_table` and `subtables` are not set.
  optional Value single_value = 2;

  // A table defining the rate group, when `single_value` is not
  // expressive enough. Can only be set if `single_value` is not
  // set.
  optional Table main_table = 3;

  // Optional. A list of subtables referred to by `main_table`. Can only
  // be set if `main_table` is set.
  repeated Table subtables = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of carrier rates that can be referred to by
  // `main_table` or `single_value`.
  repeated CarrierRate carrier_rates = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Name of the rate group.
  // If set has to be unique within shipping service.
  optional string name = 6 [(google.api.field_behavior) = OPTIONAL];
}

// A table defining the rate group, when `single_value` is not
// expressive enough.
message Table {
  // Name of the table. Required for subtables, ignored for the main table.
  optional string name = 1;

  // Required. Headers of the table's rows.
  optional Headers row_headers = 2 [(google.api.field_behavior) = REQUIRED];

  // Headers of the table's columns. Optional: if not set then the table has
  // only one dimension.
  optional Headers column_headers = 3;

  // Required. The list of rows that constitute the table. Must have the same
  // length as `row_headers`.
  repeated Row rows = 4 [(google.api.field_behavior) = REQUIRED];
}

// Transit time table, number of business days spent in transit based on row
// and column dimensions. Either `min_transit_days`, `max_transit_days` or
// `transit_time_table` can be set, but not both.
message TransitTable {
  // If there's only one dimension set of `postal_code_group_names` or
  // `transit_time_labels`, there are multiple rows each with one value
  // for that dimension. If there are two dimensions, each row corresponds to a
  // `postal_code_group_names`, and columns (values) to a
  // `transit_time_labels`.
  message TransitTimeRow {
    // Transit time range (min-max) in business days.
    message TransitTimeValue {
      // Minimum transit time range in business days. 0 means same
      // day delivery, 1 means next day delivery.
      optional int32 min_transit_days = 1;

      // Must be greater than or equal to `min_transit_days`.
      optional int32 max_transit_days = 2;
    }

    // Required. Transit time range (min-max) in business days.
    repeated TransitTimeValue values = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // Required. A list of region names
  // [Region.name][google.shopping.merchant.accounts.v1beta.Region.name] . The
  // last value can be
  // `"all other locations"`. Example:
  // `["zone 1", "zone 2", "all other locations"]`. The referred
  // postal code groups must match the delivery country of the service.
  repeated string postal_code_group_names = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. A list of transit time labels. The last value can be
  // `"all other labels"`. Example:
  // `["food", "electronics", "all other labels"]`.
  repeated string transit_time_labels = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. If there's only one dimension set of `postal_code_group_names` or
  // `transit_time_labels`, there are multiple rows each with one value
  // for that dimension. If there are two dimensions, each row corresponds to a
  // `postal_code_group_names`, and columns (values) to a
  // `transit_time_labels`.
  repeated TransitTimeRow rows = 3 [(google.api.field_behavior) = REQUIRED];
}

// Table of per store minimum order values for the pickup fulfillment type.
message MinimumOrderValueTable {
  // A list of store code sets sharing the same minimum order value. At least
  // two sets are required and the last one must be empty, which signifies
  // 'MOV for all other stores'.
  // Each store code can only appear once across all the sets.
  // All prices within a service must have the same currency.
  message StoreCodeSetWithMov {
    // Optional. A list of unique store codes or empty for the catch all.
    repeated string store_codes = 1 [(google.api.field_behavior) = OPTIONAL];

    // The minimum order value for the given stores.
    optional google.shopping.type.Price value = 2;
  }

  // Required. A list of store code sets sharing the same minimum order value
  // (MOV). At least two sets are required and the last one must be empty, which
  // signifies 'MOV for all other stores'. Each store code can only appear once
  // across all the sets. All prices within a service must have the same
  // currency.
  repeated StoreCodeSetWithMov store_code_set_with_movs = 1
      [(google.api.field_behavior) = REQUIRED];
}

// A non-empty list of row or column headers for a table.
// Exactly one of `prices`, `weights`,
// `num_items`, `postal_code_group_names`, or
// `location` must be set.
message Headers {
  // Required. A list of inclusive order price upper bounds. The last price's
  // value can be infinity by setting price amount_micros = -1. For example
  // `[{"amount_micros": 10000000, "currency_code": "USD"},
  // {"amount_micros": 500000000, "currency_code": "USD"},
  // {"amount_micros": -1, "currency_code": "USD"}]` represents the headers
  // "<= $10", "<= $500", and "> $500". All prices within a service must have
  // the same currency. Must be non-empty. Must be positive except -1. Can only
  // be set if all other fields are not set.
  repeated google.shopping.type.Price prices = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. A list of inclusive order weight upper bounds. The last weight's
  // value can be infinity by setting price amount_micros = -1. For example
  // `[{"amount_micros": 10000000, "unit": "kg"}, {"amount_micros": 50000000,
  // "unit": "kg"},
  // {"amount_micros": -1, "unit": "kg"}]` represents the headers
  // "<= 10kg", "<= 50kg", and "> 50kg". All weights within a service must have
  // the same unit. Must be non-empty. Must be positive except -1. Can only be
  // set if all other fields are not set.
  repeated google.shopping.type.Weight weights = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. A list of inclusive number of items upper bounds. The last value
  // can be
  // `"infinity"`. For example
  // `["10", "50", "infinity"]` represents the headers
  // "<= 10 items", "<= 50 items", and "> 50 items". Must be non-empty. Can
  // only be set if all other fields are not set.
  repeated string number_of_items = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. A list of postal group names. The last value can be
  // `"all other locations"`. Example:
  // `["zone 1", "zone 2", "all other locations"]`. The referred
  // postal code groups must match the delivery country of the service. Must
  // be non-empty. Can only be set if all other fields are not set.
  repeated string postal_code_group_names = 4
      [(google.api.field_behavior) = REQUIRED];

  // Required. A list of location ID sets. Must be non-empty. Can only be set if
  // all other fields are not set.
  repeated LocationIdSet locations = 5 [(google.api.field_behavior) = REQUIRED];
}

// A list of location ID sets. Must be non-empty. Can only be set if all
// other fields are not set.
message LocationIdSet {
  // Required. A non-empty list of
  // [location
  // IDs](https://developers.google.com/adwords/api/docs/appendix/geotargeting).
  // They must all be of the same location type (For
  // example, state).
  repeated string location_ids = 1 [(google.api.field_behavior) = REQUIRED];
}

// Include a list of cells.
message Row {
  // Required. The list of cells that constitute the row. Must have the same
  // length as `columnHeaders` for two-dimensional tables, a length of 1 for
  // one-dimensional tables.
  repeated Value cells = 1 [(google.api.field_behavior) = REQUIRED];
}

// The single value of a rate group or the value of a rate group table's cell.
// Exactly one of `no_shipping`, `flat_rate`,
// `price_percentage`, `carrier_rateName`,
// `subtable_name` must be set.
message Value {
  // If true, then the product can't be shipped. Must be true when set, can only
  // be set if all other fields are not set.
  optional bool no_shipping = 1;

  // A flat rate. Can only be set if all other fields are not set.
  optional google.shopping.type.Price flat_rate = 2;

  // A percentage of the price represented as a number in decimal notation
  // (For example, `"5.4"`). Can only be set if all other fields are not
  // set.
  optional string price_percentage = 3;

  // The name of a carrier rate referring to a carrier rate defined in the
  // same rate group. Can only be set if all other fields are not set.
  optional string carrier_rate = 4;

  // The name of a subtable. Can only be set in table cells (For example, not
  // for single values), and only if all other fields are not set.
  optional string subtable = 5;
}

// A list of carrier rates that can be referred to by
// `main_table` or `single_value`.
message CarrierRate {
  // Required. Name of the carrier rate. Must be unique per rate group.
  optional string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Carrier service, such as `"UPS"` or `"Fedex"`.
  optional string carrier = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Carrier service, such as `"ground"` or `"2 days"`.
  optional string carrier_service = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Shipping origin for this carrier rate.
  optional string origin_postal_code = 4
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Multiplicative shipping rate modifier as a number in decimal
  // notation. Can be negative. For example `"5.4"` increases the rate by 5.4%,
  // `"-3"` decreases the rate by 3%.
  optional string percentage_adjustment = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Additive shipping rate modifier. Can be negative. For example
  // `{ "amount_micros": 1, "currency_code" : "USD" }` adds $1 to the rate,
  // `{ "amount_micros": -3, "currency_code" : "USD" }` removes $3 from the
  // rate.
  optional google.shopping.type.Price flat_adjustment = 6
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `GetShippingSetting` method.
message GetShippingSettingsRequest {
  // Required. The name of the shipping setting to retrieve.
  // Format: `accounts/{account}/shippingsetting`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/ShippingSettings"
    }
  ];
}

// Request message for the `InsertShippingSetting` method.
message InsertShippingSettingsRequest {
  // Required. The account where this product will be inserted.
  // Format: accounts/{account}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The new version of the account.
  ShippingSettings shipping_setting = 2
      [(google.api.field_behavior) = REQUIRED];
}
