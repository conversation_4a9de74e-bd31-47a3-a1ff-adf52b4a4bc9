// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.accounts.v1beta;

import "google/api/field_behavior.proto";
import "google/type/interval.proto";

option go_package = "cloud.google.com/go/shopping/merchant/accounts/apiv1beta/accountspb;accountspb";
option java_multiple_files = true;
option java_outer_classname = "TaxRuleProto";
option java_package = "com.google.shopping.merchant.accounts.v1beta";

// Primary type convension
//
// percent micro : 100% = 1 000 000 and 1% = 10 000
//                 cannot be negative.
//
// Information about tax nexus and related parameters applicable to orders
// delivered to the area covered by a single tax admin. Nexus is created when a
// merchant is doing business in an area administered by tax admin (only US
// states are supported for nexus configuration). If merchant has nexus in a US
// state, merchant needs to pay tax to all tax authorities associated with
// the shipping destination.
// Next Id : 8
message TaxRule {
  // A range of postal codes that defines the area.
  message TaxPostalCodeRange {
    // Required. The start of the postal code range, which is also the smallest
    // in the range.
    string start = 1 [(google.api.field_behavior) = REQUIRED];

    // The end of the postal code range. Will be the same as start if not
    // specified.
    string end = 2;
  }

  // Describe the location through either postal code range or a criteria id.
  oneof location {
    // The admin_id or criteria_id of the region in which this rule is
    // applicable.
    int64 location_id = 2;

    // The range of postal codes in which this rule is applicable.
    TaxPostalCodeRange post_code_range = 3;
  }

  // What is the way to calculate tax rate for deliveries to this admin's area.
  // Can only be set on US states.
  oneof rate_calculation {
    // Rate that depends on delivery location: if merchant has a nexus in
    // corresponding US state, rates from authorities with jurisdiction over
    // delivery area are added up.
    bool use_google_rate = 4;

    // A fixed rate specified in micros, where 100% = 1_000_000.
    // Suitable for origin-based states.
    int64 self_specified_rate_micros = 5;
  }

  // Region code in which this rule is applicable
  string region_code = 1;

  // If set, shipping charge is taxed (at the same rate as product) when
  // delivering to this admin's area.
  // Can only be set on US states without category.
  bool shipping_taxed = 6;

  // Required. Time period when this rule is effective. If the duration is
  // missing from effective_time listed, then it is open ended to the future.
  // The start of this time period is inclusive, and the end is exclusive.
  google.type.Interval effective_time_period = 7
      [(google.api.field_behavior) = REQUIRED];
}
