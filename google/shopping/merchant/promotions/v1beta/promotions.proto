// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.promotions.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/shopping/merchant/promotions/v1beta/promotions_common.proto";
import "google/shopping/type/types.proto";

option go_package = "cloud.google.com/go/shopping/merchant/promotions/apiv1beta/promotionspb;promotionspb";
option java_multiple_files = true;
option java_outer_classname = "PromotionsProto";
option java_package = "com.google.shopping.merchant.promotions.v1beta";
option (google.api.resource_definition) = {
  type: "merchantapi.googleapis.com/Account"
  pattern: "accounts/{account}"
};

// Service to manage promotions for products.
service PromotionsService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Inserts a promotion for your Merchant Center account. If the promotion
  // already exists, then it updates the promotion instead.
  rpc InsertPromotion(InsertPromotionRequest) returns (Promotion) {
    option (google.api.http) = {
      post: "/promotions/v1beta/{parent=accounts/*}/promotions:insert"
      body: "*"
    };
  }

  // Retrieves the promotion from your Merchant Center account.
  //
  // After inserting or updating a promotion input, it may take several
  // minutes before the updated promotion can be retrieved.
  rpc GetPromotion(GetPromotionRequest) returns (Promotion) {
    option (google.api.http) = {
      get: "/promotions/v1beta/{name=accounts/*/promotions/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the promotions in your Merchant Center account. The
  // response might contain fewer items than specified by `pageSize`. Rely on
  // `pageToken` to determine if there are more items to be requested.
  //
  // After inserting or updating a promotion, it may take several minutes before
  // the updated processed promotion can be retrieved.
  rpc ListPromotions(ListPromotionsRequest) returns (ListPromotionsResponse) {
    option (google.api.http) = {
      get: "/promotions/v1beta/{parent=accounts/*}/promotions"
    };
    option (google.api.method_signature) = "parent";
  }
}

// Represents a promotion. See the following articles for more details.
//
// Required promotion input attributes to pass data validation checks are
// primarily defined below:
//
// * [Promotions data
// specification](https://support.google.com/merchants/answer/2906014)
// * [Local promotions data
// specification](https://support.google.com/merchants/answer/********)
//
// After inserting, updating a promotion input, it may take several minutes
// before the final promotion can be retrieved.
message Promotion {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/Promotion"
    pattern: "accounts/{account}/promotions/{promotion}"
    plural: "promotions"
    singular: "promotion"
  };

  // Identifier. The name of the promotion.
  // Format: `accounts/{account}/promotions/{promotion}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Required. The user provided promotion ID to uniquely identify the
  // promotion. Follow [minimum
  // requirements](https://support.google.com/merchants/answer/7050148?ref_topic=7322920&sjid=871860036916537104-NC#minimum_requirements)
  // to prevent promotion disapprovals.
  string promotion_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The two-letter [ISO
  // 639-1](http://en.wikipedia.org/wiki/ISO_639-1) language code for the
  // promotion.
  //
  // Promotions is only for [selected
  // languages](https://support.google.com/merchants/answer/4588281?ref_topic=6396150&sjid=18314938579342094533-NC#option3&zippy=).
  string content_language = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The target country used as part of the unique identifier.
  // Represented as a [CLDR territory
  // code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml).
  //
  // Promotions are only available in selected
  // countries, [Free Listings and Shopping
  // ads](https://support.google.com/merchants/answer/4588460) [Local Inventory
  // ads](https://support.google.com/merchants/answer/10146326)
  string target_country = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. [Redemption
  // channel](https://support.google.com/merchants/answer/13837674?ref_topic=13773355&sjid=17642868584668136159-NC)
  // for the promotion. At least one channel is required.
  repeated RedemptionChannel redemption_channel = 5
      [(google.api.field_behavior) = REQUIRED];

  // Output only. The primary data source of the promotion.
  string data_source = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. A list of promotion attributes.
  Attributes attributes = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of custom (merchant-provided) attributes. It can also be
  // used for submitting any attribute of the data specification in its generic
  // form (for example,
  // `{ "name": "size type", "value": "regular" }`).
  // This is useful for submitting attributes not explicitly exposed by the
  // API.
  repeated google.shopping.type.CustomAttribute custom_attributes = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Output only. The [status of a
  // promotion](https://support.google.com/merchants/answer/3398326?ref_topic=7322924&sjid=5155774230887277618-NC),
  // data validation issues, that is, information about a promotion computed
  // asynchronously.
  PromotionStatus promotion_status = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Represents the existing version (freshness) of the promotion,
  // which can be used to preserve the right order when multiple updates are
  // done at the same time.
  //
  // If set, the insertion is prevented when version number is lower than
  // the current version number of the existing promotion. Re-insertion (for
  // example, promotion refresh after 30 days) can be performed with the current
  // `version_number`.
  //
  // If the operation is prevented, the aborted exception will be
  // thrown.
  optional int64 version_number = 10 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `InsertPromotion` method.
message InsertPromotionRequest {
  // Required. The account where the promotion will be inserted.
  // Format: accounts/{account}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The promotion to insert.
  Promotion promotion = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The data source of the
  // [promotion](https://support.google.com/merchants/answer/6396268?sjid=5155774230887277618-NC)
  // Format:
  // `accounts/{account}/dataSources/{datasource}`.
  string data_source = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `GetPromotion` method.
message GetPromotionRequest {
  // Required. The name of the promotion to retrieve.
  // Format: `accounts/{account}/promotions/{promotions}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/Promotion"
    }
  ];
}

// Request message for the `ListPromotions` method.
message ListPromotionsRequest {
  // Required. The account to list processed promotions for.
  // Format: `accounts/{account}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Output only. The maximum number of promotions to return. The service may
  // return fewer than this value. The maximum value is 1000; values above 1000
  // will be coerced to 1000. If unspecified, the maximum number of promotions
  // will be returned.
  int32 page_size = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A page token, received from a previous `ListPromotions` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListPromotions` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Response message for the `ListPromotions` method.
message ListPromotionsResponse {
  // The processed promotions from the specified account.
  repeated Promotion promotions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
