// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.quota.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/shopping/merchant/quota/apiv1beta/quotapb;quotapb";
option java_multiple_files = true;
option java_outer_classname = "QuotaProto";
option java_package = "com.google.shopping.merchant.quota.v1beta";
option (google.api.resource_definition) = {
  type: "merchantapi.googleapis.com/Account"
  pattern: "accounts/{account}"
};

// Service to get method call quota information per Merchant API method.
service QuotaService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Lists the daily call quota and usage per group for your Merchant
  // Center account.
  rpc ListQuotaGroups(ListQuotaGroupsRequest)
      returns (ListQuotaGroupsResponse) {
    option (google.api.http) = {
      get: "/quota/v1beta/{parent=accounts/*}/quotas"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The group information for methods in the Merchant API. The quota is shared
// between all methods in the group. Even if none of the methods within the
// group have usage the information for the group is returned.
message QuotaGroup {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/QuotaGroup"
    pattern: "accounts/{account}/groups/{group}"
    plural: "quotaGroups"
    singular: "quotaGroup"
  };

  // Identifier. The resource name of the quota group.
  // Format: accounts/{account}/quotas/{group}
  // Note: There is no guarantee on the format of {group}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The current quota usage, meaning the number of calls already
  // made on a given day to the methods in the group. The daily quota limits
  // reset at at 12:00 PM midday UTC.
  int64 quota_usage = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The maximum number of calls allowed per day for the group.
  int64 quota_limit = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The maximum number of calls allowed per minute for the group.
  int64 quota_minute_limit = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of all methods group quota applies to.
  repeated MethodDetails method_details = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The method details per method in the Merchant API.
message MethodDetails {
  // Output only. The name of the method for example `products.list`.
  string method = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The API version that the method belongs to.
  string version = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The sub-API that the method belongs to.
  string subapi = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The path for the method such as
  // `products/v1/productInputs.insert`
  string path = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for the ListQuotaGroups method.
message ListQuotaGroupsRequest {
  // Required. The merchant account who owns the collection of method quotas
  // Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/QuotaGroup"
    }
  ];

  // Optional. The maximum number of quotas to return in the response, used
  // for paging. Defaults to 500; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Token (if provided) to retrieve the subsequent page. All other
  // parameters must match the original call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the ListMethodGroups method.
message ListQuotaGroupsResponse {
  // The methods, current quota usage and limits per each group. The quota is
  // shared between all methods in the group. The groups are sorted in
  // descending order based on
  // [quotaUsage][google.shopping.merchant.quota.v1main.QuotaGroup.quota_usage].
  repeated QuotaGroup quota_groups = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
