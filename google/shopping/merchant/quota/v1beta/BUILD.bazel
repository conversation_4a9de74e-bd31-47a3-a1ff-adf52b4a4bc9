# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "quota_proto",
    srcs = [
        "quota.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
    ],
)

proto_library_with_info(
    name = "quota_proto_with_info",
    deps = [
        ":quota_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "quota_java_proto",
    deps = [":quota_proto"],
)

java_grpc_library(
    name = "quota_java_grpc",
    srcs = [":quota_proto"],
    deps = [":quota_java_proto"],
)

java_gapic_library(
    name = "quota_java_gapic",
    srcs = [":quota_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "quota_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    test_deps = [
        ":quota_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":quota_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "quota_java_gapic_test_suite",
    test_classes = [
        "com.google.shopping.merchant.quota.v1beta.QuotaServiceClientHttpJsonTest",
        "com.google.shopping.merchant.quota.v1beta.QuotaServiceClientTest",
    ],
    runtime_deps = [":quota_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-merchant-quota-v1beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":quota_java_gapic",
        ":quota_java_grpc",
        ":quota_java_proto",
        ":quota_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "quota_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/shopping/merchant/quota/apiv1beta/quotapb",
    protos = [":quota_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "quota_go_gapic",
    srcs = [":quota_proto_with_info"],
    grpc_service_config = "quota_grpc_service_config.json",
    importpath = "cloud.google.com/go/shopping/merchant/quota/apiv1beta;quota",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":quota_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-merchant-quota-v1beta-go",
    deps = [
        ":quota_go_gapic",
        ":quota_go_gapic_srcjar-metadata.srcjar",
        ":quota_go_gapic_srcjar-snippets.srcjar",
        ":quota_go_gapic_srcjar-test.srcjar",
        ":quota_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "quota_py_gapic",
    srcs = [":quota_proto"],
    grpc_service_config = "quota_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
    ],
    opt_args = [
        "python-gapic-namespace=google.shopping",
        "python-gapic-name=merchant_quota",
    ],
)

py_test(
    name = "quota_py_gapic_test",
    srcs = [
        "quota_py_gapic_pytest.py",
        "quota_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":quota_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "merchant-quota-v1beta-py",
    deps = [
        ":quota_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "quota_php_proto",
    deps = [":quota_proto"],
)

php_gapic_library(
    name = "quota_php_gapic",
    srcs = [":quota_proto_with_info"],
    grpc_service_config = "quota_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":quota_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-merchant-quota-v1beta-php",
    deps = [
        ":quota_php_gapic",
        ":quota_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "quota_nodejs_gapic",
    package_name = "@google-shopping/quota",
    src = ":quota_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "quota_grpc_service_config.json",
    package = "google.shopping.merchant.quota.v1beta",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "merchant-quota-v1beta-nodejs",
    deps = [
        ":quota_nodejs_gapic",
        ":quota_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "quota_ruby_proto",
    deps = [":quota_proto"],
)

ruby_grpc_library(
    name = "quota_ruby_grpc",
    srcs = [":quota_proto"],
    deps = [":quota_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "quota_ruby_gapic",
    srcs = [":quota_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-shopping-merchant-quota-v1beta",
    ],
    grpc_service_config = "quota_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":quota_ruby_grpc",
        ":quota_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-shopping-merchant-quota-v1beta-ruby",
    deps = [
        ":quota_ruby_gapic",
        ":quota_ruby_grpc",
        ":quota_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "quota_csharp_proto",
    extra_opts = [],
    deps = [":quota_proto"],
)

csharp_grpc_library(
    name = "quota_csharp_grpc",
    srcs = [":quota_proto"],
    deps = [":quota_csharp_proto"],
)

csharp_gapic_library(
    name = "quota_csharp_gapic",
    srcs = [":quota_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "quota_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":quota_csharp_grpc",
        ":quota_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-merchant-quota-v1beta-csharp",
    deps = [
        ":quota_csharp_gapic",
        ":quota_csharp_grpc",
        ":quota_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "quota_cc_proto",
    deps = [":quota_proto"],
)

cc_grpc_library(
    name = "quota_cc_grpc",
    srcs = [":quota_proto"],
    grpc_only = True,
    deps = [":quota_cc_proto"],
)
