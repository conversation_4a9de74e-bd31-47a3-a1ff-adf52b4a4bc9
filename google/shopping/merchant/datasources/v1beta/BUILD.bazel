# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "datasources_proto",
    srcs = [
        "datasources.proto",
        "datasourcetypes.proto",
        "fileinputs.proto",
        "fileuploads.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/shopping/type:type_proto",  # Manual fix. Original :types_proto
        "//google/type:dayofweek_proto",
        "//google/type:timeofday_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "datasources_proto_with_info",
    deps = [
        ":datasources_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "datasources_java_proto",
    deps = [":datasources_proto"],
)

java_grpc_library(
    name = "datasources_java_grpc",
    srcs = [":datasources_proto"],
    deps = [":datasources_java_proto"],
)

java_gapic_library(
    name = "datasources_java_gapic",
    srcs = [":datasources_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "datasources_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    test_deps = [
        ":datasources_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":datasources_java_proto",
        "//google/api:api_java_proto",
        "//google/shopping/type:type_java_proto",  # Added manually
    ],
)

java_gapic_test(
    name = "datasources_java_gapic_test_suite",
    test_classes = [
        "com.google.shopping.merchant.datasources.v1beta.DataSourcesServiceClientHttpJsonTest",
        "com.google.shopping.merchant.datasources.v1beta.DataSourcesServiceClientTest",
        "com.google.shopping.merchant.datasources.v1beta.FileUploadsServiceClientHttpJsonTest",
        "com.google.shopping.merchant.datasources.v1beta.FileUploadsServiceClientTest",
    ],
    runtime_deps = [":datasources_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-merchant-datasources-v1beta-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":datasources_java_gapic",
        ":datasources_java_grpc",
        ":datasources_java_proto",
        ":datasources_proto",
        "//google/api:api_java_proto",  # Added manually
        "//google/shopping/type:type_java_proto",  # Added manually
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "datasources_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/shopping/merchant/datasources/apiv1beta/datasourcespb",
    protos = [":datasources_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/shopping/type:type_go_proto",  # Manual fix
        "//google/type:dayofweek_go_proto",
        "//google/type:timeofday_go_proto",
    ],
)

go_gapic_library(
    name = "datasources_go_gapic",
    srcs = [":datasources_proto_with_info"],
    grpc_service_config = "datasources_grpc_service_config.json",
    importpath = "cloud.google.com/go/shopping/merchant/datasources/apiv1beta;datasources",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":datasources_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-merchant-datasources-v1beta-go",
    deps = [
        ":datasources_go_gapic",
        ":datasources_go_gapic_srcjar-metadata.srcjar",
        ":datasources_go_gapic_srcjar-snippets.srcjar",
        ":datasources_go_gapic_srcjar-test.srcjar",
        ":datasources_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "datasources_py_gapic",
    srcs = [":datasources_proto"],
    grpc_service_config = "datasources_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=merchant_datasources",
        "python-gapic-namespace=google.shopping",
    ],
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "datasources_py_gapic_test",
    srcs = [
        "datasources_py_gapic_pytest.py",
        "datasources_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":datasources_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "merchant-datasources-v1beta-py",
    deps = [
        ":datasources_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "datasources_php_proto",
    deps = [":datasources_proto"],
)

php_gapic_library(
    name = "datasources_php_gapic",
    srcs = [":datasources_proto_with_info"],
    grpc_service_config = "datasources_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":datasources_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-merchant-datasources-v1beta-php",
    deps = [
        ":datasources_php_gapic",
        ":datasources_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "datasources_nodejs_gapic",
    package_name = "@google-shopping/datasources",
    src = ":datasources_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "datasources_grpc_service_config.json",
    package = "google.shopping.merchant.datasources.v1beta",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "merchant-datasources-v1beta-nodejs",
    deps = [
        ":datasources_nodejs_gapic",
        ":datasources_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "datasources_ruby_proto",
    deps = [":datasources_proto"],
)

ruby_grpc_library(
    name = "datasources_ruby_grpc",
    srcs = [":datasources_proto"],
    deps = [":datasources_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "datasources_ruby_gapic",
    srcs = [":datasources_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-shopping-merchant-data_sources-v1beta"],
    grpc_service_config = "datasources_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":datasources_ruby_grpc",
        ":datasources_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-merchant-datasources-v1beta-ruby",
    deps = [
        ":datasources_ruby_gapic",
        ":datasources_ruby_grpc",
        ":datasources_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "datasources_csharp_proto",
    extra_opts = [],
    deps = [":datasources_proto"],
)

csharp_grpc_library(
    name = "datasources_csharp_grpc",
    srcs = [":datasources_proto"],
    deps = [":datasources_csharp_proto"],
)

csharp_gapic_library(
    name = "datasources_csharp_gapic",
    srcs = [":datasources_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "datasources_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "merchantapi_v1beta.yaml",
    transport = "grpc+rest",
    deps = [
        ":datasources_csharp_grpc",
        ":datasources_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-merchant-datasources-v1beta-csharp",
    deps = [
        ":datasources_csharp_gapic",
        ":datasources_csharp_grpc",
        ":datasources_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "datasources_cc_proto",
    deps = [":datasources_proto"],
)

cc_grpc_library(
    name = "datasources_cc_grpc",
    srcs = [":datasources_proto"],
    grpc_only = True,
    deps = [":datasources_cc_proto"],
)
