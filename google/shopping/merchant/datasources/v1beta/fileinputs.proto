// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.datasources.v1beta;

import "google/api/field_behavior.proto";
import "google/type/dayofweek.proto";
import "google/type/timeofday.proto";

option csharp_namespace = "Google.Shopping.Merchant.DataSources.V1Beta";
option go_package = "cloud.google.com/go/shopping/merchant/datasources/apiv1beta/datasourcespb;datasourcespb";
option java_multiple_files = true;
option java_outer_classname = "FileInputsProto";
option java_package = "com.google.shopping.merchant.datasources.v1beta";
option php_namespace = "Google\\Shopping\\Merchant\\DataSources\\V1beta";
option ruby_package = "Google::Shopping::Merchant::DataSources::V1beta";

// The data specific for file data sources. This field is empty for other
// data source inputs.
message FileInput {
  // Fetch details to deliver the data source.
  message FetchSettings {
    // The required fields vary based on the frequency of fetching. For a
    // monthly
    // fetch schedule,
    // [day of
    // month][google.shopping.content.bundles.DataSources.FileInput.FetchSchedule.day_of_month]
    // and
    // [hour of
    // day][google.shopping.content.bundles.DataSources.FileInput.FetchSchedule.time_of_day]
    // are required. For a weekly fetch schedule,
    // [day of
    // week][google.shopping.content.bundles.DataSources.FileInput.FetchSchedule.day_of_week]
    // and [hour of
    // day][google.shopping.content.bundles.DataSources.FileInput.FetchSchedule.time_of_day]
    // are required. For a daily fetch schedule, only an [hour of
    // day][google.shopping.content.bundles.DataSources.FileInput.FetchSchedule.time_of_day]
    // is required.
    enum Frequency {
      // Frequency unspecified.
      FREQUENCY_UNSPECIFIED = 0;

      // The fetch happens every day.
      FREQUENCY_DAILY = 1;

      // The fetch happens every week.
      FREQUENCY_WEEKLY = 2;

      // The fetch happens every month.
      FREQUENCY_MONTHLY = 3;
    }

    // Optional. Enables or pauses the fetch schedule.
    bool enabled = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The day of the month when the data source file should be
    // fetched (1-31). This field can only be set for monthly frequency.
    int32 day_of_month = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The hour of the day when the data source file should be
    // fetched. Minutes and seconds are not supported and will be ignored.
    google.type.TimeOfDay time_of_day = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The day of the week when the data source file should be
    // fetched. This field can only be set for weekly frequency.
    google.type.DayOfWeek day_of_week = 4
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. [Time zone](https://cldr.unicode.org) used for schedule. UTC by
    // default. For example, "America/Los_Angeles".
    string time_zone = 5 [(google.api.field_behavior) = OPTIONAL];

    // Required. The frequency describing fetch schedule.
    Frequency frequency = 6 [(google.api.field_behavior) = REQUIRED];

    // Optional. The URL where the data source file can be fetched. Google
    // Merchant Center supports automatic scheduled uploads using the HTTP,
    // HTTPS or SFTP protocols, so the value will need to be a valid link using
    // one of those three protocols. Immutable for Google Sheets files.
    string fetch_uri = 7 [(google.api.field_behavior) = OPTIONAL];

    // Optional. An optional user name for
    // [fetch_uri][google.shopping.merchant.datasources.v1beta.FileInput.FetchSettings.fetch_uri].
    // Used for [submitting data sources through
    // SFTP](https://support.google.com/merchants/answer/13813117).
    string username = 8 [(google.api.field_behavior) = OPTIONAL];

    // Optional. An optional password for
    // [fetch_uri][google.shopping.merchant.datasources.v1beta.FileInput.FetchSettings.fetch_uri].
    // Used for [submitting data sources through
    // SFTP](https://support.google.com/merchants/answer/13813117).
    string password = 9 [(google.api.field_behavior) = OPTIONAL];
  }

  // The method of file delivery.
  enum FileInputType {
    // File input type unspecified.
    FILE_INPUT_TYPE_UNSPECIFIED = 0;

    // The file is uploaded through SFTP, Google Cloud Storage or manually in
    // the Merchant Center.
    UPLOAD = 1;

    // The file is fetched from the configured
    // [fetch_uri][google.shopping.merchant.datasources.v1beta.FileInput.FetchSettings.fetch_uri].
    FETCH = 2;

    // The file is fetched from Google Sheets specified in the
    // [fetch_uri][google.shopping.merchant.datasources.v1beta.FileInput.FetchSettings.fetch_uri].
    GOOGLE_SHEETS = 3;
  }

  // Optional. Fetch details to deliver the data source. It contains settings
  // for `FETCH` and `GOOGLE_SHEETS` file input types. The required fields vary
  // based on the frequency of fetching.
  FetchSettings fetch_settings = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The file name of the data source. Required for `UPLOAD` file
  // input type.
  string file_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. The type of file input.
  FileInputType file_input_type = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}
