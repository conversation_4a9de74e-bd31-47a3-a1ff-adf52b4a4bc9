type: google.api.Service
config_version: 3
name: merchantapi.googleapis.com
title: Merchant API

apis:
- name: google.shopping.merchant.datasources.v1beta.DataSourcesService
- name: google.shopping.merchant.datasources.v1beta.FileUploadsService

documentation:
  summary: Programmatically manage your Merchant Center Accounts.
  overview: |-
    Merchant API consists of multiple Sub-APIs. Accounts Sub-API: Enables you
    to programmatically manage your accounts. Conversions Sub-API: Enables you
    to programmatically manage your conversion sources for a merchant account.
    Datasources Sub-API: Enables you to programmatically manage your
    datasources. Inventories Sub-API: This bundle enables you to
    programmatically manage your local and regional inventories. Local Feeds
    Partnerships Sub-API: This bundle enables LFP partners to submit local
    inventories for a merchant. Notifications Sub-API: This bundle enables you
    to programmatically manage your notification subscriptions. Products
    Sub-API: This bundle enables you to programmatically manage your products.
    Promotions Sub-API: This bundle enables you to programmatically manage
    your promotions for products. Quota Sub-API: This bundle enables you to
    list your quotas for all APIs you are using. Reports Sub-API: This bundle
    enables you to programmatically retrieve reports and insights about
    products, their performance and their competitive environment.

authentication:
  rules:
  - selector: 'google.shopping.merchant.datasources.v1beta.DataSourcesService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/content
  - selector: google.shopping.merchant.datasources.v1beta.FileUploadsService.GetFileUpload
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/content

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=171084&template=555201
  documentation_uri: https://developers.google.com/merchant/api
  api_short_name: merchantapi
  github_label: 'api: merchantapi'
  doc_tag_prefix: merchantapi
  organization: SHOPPING
  library_settings:
  - version: google.shopping.merchant.datasources.v1beta
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common: {}
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
