// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.datasources.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/shopping/merchant/datasources/v1beta/datasourcetypes.proto";
import "google/shopping/merchant/datasources/v1beta/fileinputs.proto";

option csharp_namespace = "Google.Shopping.Merchant.DataSources.V1Beta";
option go_package = "cloud.google.com/go/shopping/merchant/datasources/apiv1beta/datasourcespb;datasourcespb";
option java_multiple_files = true;
option java_outer_classname = "DataSourcesProto";
option java_package = "com.google.shopping.merchant.datasources.v1beta";
option php_namespace = "Google\\Shopping\\Merchant\\DataSources\\V1beta";
option ruby_package = "Google::Shopping::Merchant::DataSources::V1beta";
option (google.api.resource_definition) = {
  type: "merchantapi.googleapis.com/Account"
  pattern: "accounts/{account}"
};
option (google.api.resource_definition) = {
  type: "merchantapi.googleapis.com/Datasource"
  pattern: "accounts/{account}/dataSources/{datasource}"
};

// Service to manage primary, supplemental, inventory and other data sources.
// See more in the [Merchant
// Center](https://support.google.com/merchants/answer/7439058) help article.
service DataSourcesService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves the data source configuration for the given account.
  rpc GetDataSource(GetDataSourceRequest) returns (DataSource) {
    option (google.api.http) = {
      get: "/datasources/v1beta/{name=accounts/*/dataSources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the configurations for data sources for the given account.
  rpc ListDataSources(ListDataSourcesRequest)
      returns (ListDataSourcesResponse) {
    option (google.api.http) = {
      get: "/datasources/v1beta/{parent=accounts/*}/dataSources"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates the new data source configuration for the given account.
  rpc CreateDataSource(CreateDataSourceRequest) returns (DataSource) {
    option (google.api.http) = {
      post: "/datasources/v1beta/{parent=accounts/*}/dataSources"
      body: "data_source"
    };
    option (google.api.method_signature) = "parent,data_source";
  }

  // Updates the existing data source configuration. The fields that are
  // set in the update mask but not provided in the resource will be deleted.
  rpc UpdateDataSource(UpdateDataSourceRequest) returns (DataSource) {
    option (google.api.http) = {
      patch: "/datasources/v1beta/{data_source.name=accounts/*/dataSources/*}"
      body: "data_source"
    };
    option (google.api.method_signature) = "data_source,update_mask";
  }

  // Deletes a data source from your Merchant Center account.
  rpc DeleteDataSource(DeleteDataSourceRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/datasources/v1beta/{name=accounts/*/dataSources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Performs the data fetch immediately (even outside fetch schedule) on a
  // data source from your Merchant Center Account. If you need to call
  // this method more than once per day, you should use the Products service to
  // update your product data instead.
  // This method only works on data sources with a file input set.
  rpc FetchDataSource(FetchDataSourceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/datasources/v1beta/{name=accounts/*/dataSources/*}:fetch"
      body: "*"
    };
  }
}

// The [data source](https://support.google.com/merchants/answer/7439058) for
// the Merchant Center account.
message DataSource {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/DataSource"
    pattern: "accounts/{account}/dataSources/{datasource}"
    plural: "dataSources"
    singular: "dataSource"
  };

  // Determines the type of input to the data source. Based on the input some
  // settings might not be supported.
  enum Input {
    // Input unspecified.
    INPUT_UNSPECIFIED = 0;

    // Represents data sources for which the data is primarily provided through
    // the API.
    API = 1;

    // Represents data sources for which the data is primarily provided through
    // file input. Data can still be provided through the API.
    FILE = 2;

    // The data source for products added directly in Merchant Center.
    //
    // This type of data source can not be created or updated through this API,
    // only by Merchant Center UI.
    //
    // This type of data source is read only.
    UI = 3;

    // This is also known as
    // [Automated feeds](https://support.google.com/merchants/answer/********)
    // used to automatically build your product data. This type of data source
    // can be enabled or disabled through the Accounts bundle.
    AUTOFEED = 4;
  }

  // Required. The data source type.
  oneof Type {
    // The [primary data
    // source](https://support.google.com/merchants/answer/7439058) for local
    // and online products.
    PrimaryProductDataSource primary_product_data_source = 4;

    // The [supplemental data
    // source](https://support.google.com/merchants/answer/7439058) for local
    // and online products.
    SupplementalProductDataSource supplemental_product_data_source = 5;

    // The [local
    // inventory](https://support.google.com/merchants/answer/7023001) data
    // source.
    LocalInventoryDataSource local_inventory_data_source = 6;

    // The [regional
    // inventory](https://support.google.com/merchants/answer/7439058) data
    // source.
    RegionalInventoryDataSource regional_inventory_data_source = 7;

    // The
    // [promotion](https://support.google.com/merchants/answer/2906014) data
    // source.
    PromotionDataSource promotion_data_source = 8;

    // The [product
    // review](https://support.google.com/merchants/answer/7045996)
    // data source.
    ProductReviewDataSource product_review_data_source = 9;

    // The [merchant
    // review](https://support.google.com/merchants/answer/7045996)
    // data source.
    MerchantReviewDataSource merchant_review_data_source = 12;
  }

  // Identifier. The name of the data source.
  // Format:
  // `{datasource.name=accounts/{account}/dataSources/{datasource}}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The data source id.
  int64 data_source_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The displayed data source name in the Merchant Center UI.
  string display_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. Determines the type of input to the data source. Based on the
  // input some settings might not work. Only generic data sources can be
  // created through the API.
  Input input = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The field is used only when data is managed through a file.
  FileInput file_input = 11 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the GetDataSource method.
message GetDataSourceRequest {
  // Required. The name of the data source to retrieve.
  // Format: `accounts/{account}/dataSources/{datasource}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/DataSource"
    }
  ];
}

// Request message for the ListDataSources method.
message ListDataSourcesRequest {
  // Required. The account to list data sources for.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/DataSource"
    }
  ];

  // Optional. The maximum number of data sources to return. The service may
  // return fewer than this value. The maximum value is 1000; values above 1000
  // will be coerced to 1000. If unspecified, the maximum number of data sources
  // will be returned.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListDataSources` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListDataSources`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the ListDataSources method.
message ListDataSourcesResponse {
  // The data sources from the specified account.
  repeated DataSource data_sources = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for the CreateDataSource method.
message CreateDataSourceRequest {
  // Required. The account where this data source will be created.
  // Format: `accounts/{account}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "merchantapi.googleapis.com/DataSource"
    }
  ];

  // Required. The data source to create.
  DataSource data_source = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the UpdateDataSource method.
message UpdateDataSourceRequest {
  // Required. The data source resource to update.
  DataSource data_source = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of data source fields to be updated.
  //
  // Fields specified in the update mask without a value specified in the
  // body will be deleted from the data source.
  //
  // Providing special "*" value for full data source replacement is not
  // supported.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for the FetchDataSource method.
message FetchDataSourceRequest {
  // Required. The name of the data source resource to fetch.
  // Format: `accounts/{account}/dataSources/{datasource}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/DataSource"
    }
  ];
}

// Request message for the DeleteDataSource method.
message DeleteDataSourceRequest {
  // Required. The name of the data source to delete.
  // Format: `accounts/{account}/dataSources/{datasource}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/DataSource"
    }
  ];
}
