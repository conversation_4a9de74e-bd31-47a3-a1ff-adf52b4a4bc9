// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.datasources.v1beta;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Shopping.Merchant.DataSources.V1Beta";
option go_package = "cloud.google.com/go/shopping/merchant/datasources/apiv1beta/datasourcespb;datasourcespb";
option java_multiple_files = true;
option java_outer_classname = "DatasourcetypesProto";
option java_package = "com.google.shopping.merchant.datasources.v1beta";
option php_namespace = "Google\\Shopping\\Merchant\\DataSources\\V1beta";
option ruby_package = "Google::Shopping::Merchant::DataSources::V1beta";

// The primary data source for local and online products.
message PrimaryProductDataSource {
  // Default rule management of the data source.
  message DefaultRule {
    // Required. The list of data sources linked in the [default
    // rule](https://support.google.com/merchants/answer/7450276).
    // This list is ordered by the default rule priority of joining the data.
    // It might include none or multiple references to `self` and supplemental
    // data sources.
    //
    // The list must not be empty.
    //
    // To link the data source to the default rule, you need to add a
    // new reference to this list (in sequential order).
    //
    // To unlink the data source from the default rule, you need to
    // remove the given reference from this list.
    //
    // Changing the order of this list will result in changing the priority of
    // data sources in the default rule.
    //
    // For example, providing the following list: [`1001`, `self`] will
    // take attribute values from supplemental data source `1001`, and fallback
    // to `self` if the attribute is not set in `1001`.
    repeated DataSourceReference take_from_data_sources = 1
        [(google.api.field_behavior) = REQUIRED];
  }

  // Data Source Channel.
  //
  // Channel is used to distinguish between data sources for different product
  // verticals.
  enum Channel {
    // Not specified.
    CHANNEL_UNSPECIFIED = 0;

    // Online product.
    ONLINE_PRODUCTS = 1;

    // Local product.
    LOCAL_PRODUCTS = 2;

    // Unified data source for both local and online products.
    // Note: Products management through the API is not possible for this
    // channel.
    PRODUCTS = 3;
  }

  // Required. Immutable. Specifies the type of data source channel.
  Channel channel = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Immutable. The feed label that is specified on the data source
  // level.
  //
  // Must be less than or equal to 20 uppercase letters (A-Z), numbers (0-9),
  // and dashes (-).
  //
  // See also [migration to feed
  // labels](https://developers.google.com/shopping-content/guides/products/feed-labels).
  //
  // `feedLabel` and `contentLanguage` must be either both set or unset for data
  // sources with product content type.
  // They must be set for data sources with a file input.
  //
  // If set, the data source will only accept products matching this
  // combination. If unset, the data source will accept products without that
  // restriction.
  optional string feed_label = 4 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Immutable. The two-letter ISO 639-1 language of the items in the
  // data source.
  //
  // `feedLabel` and `contentLanguage` must be either both set or unset.
  // The fields can only be unset for data sources without file input.
  //
  // If set, the data source will only accept products matching this
  // combination. If unset, the data source will accept products without that
  // restriction.
  optional string content_language = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. The countries where the items may be displayed. Represented as a
  // [CLDR territory
  // code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml).
  repeated string countries = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Default rule management of the data source. If set, the linked
  // data sources will be replaced.
  DefaultRule default_rule = 7 [(google.api.field_behavior) = OPTIONAL];
}

// The supplemental data source for local and online products. After creation,
// you should make sure to link the supplemental product data source into one or
// more primary product data sources.
message SupplementalProductDataSource {
  // Optional. Immutable. The feed label that is specified on the data source
  // level.
  //
  // Must be less than or equal to 20 uppercase letters (A-Z), numbers (0-9),
  // and dashes (-).
  //
  // See also [migration to feed
  // labels](https://developers.google.com/shopping-content/guides/products/feed-labels).
  //
  // `feedLabel` and `contentLanguage` must be either both set or unset for data
  // sources with product content type.
  //
  // They must be set for data sources with a [file
  // input][google.shopping.merchant.datasources.v1main.FileInput].
  // The fields must be unset for data sources without [file
  // input][google.shopping.merchant.datasources.v1main.FileInput].
  //
  // If set, the data source will only accept products matching this
  // combination. If unset, the data source will accept produts without that
  // restriction.
  optional string feed_label = 4 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Immutable. The two-letter ISO 639-1 language of the items in the
  // data source.
  //
  // `feedLabel` and `contentLanguage` must be either both set or unset.
  // The fields can only be unset for data sources without file input.
  //
  // If set, the data source will only accept products matching this
  // combination. If unset, the data source will accept produts without that
  // restriction.
  optional string content_language = 5 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Output only. The (unordered and deduplicated) list of all primary data
  // sources linked to this data source in either default or custom rules.
  // Supplemental data source cannot be deleted before all links are removed.
  repeated DataSourceReference referencing_primary_data_sources = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The local inventory data source.
message LocalInventoryDataSource {
  // Required. Immutable. The feed label of the offers to which the local
  // inventory is provided.
  //
  // Must be less than or equal to 20 uppercase letters (A-Z), numbers (0-9),
  // and dashes (-).
  //
  // See also [migration to feed
  // labels](https://developers.google.com/shopping-content/guides/products/feed-labels).
  string feed_label = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Immutable. The two-letter ISO 639-1 language of the items to
  // which the local inventory is provided.
  string content_language = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];
}

// The regional inventory data source.
message RegionalInventoryDataSource {
  // Required. Immutable. The feed label of the offers to which the regional
  // inventory is provided.
  //
  // Must be less than or equal to 20 uppercase letters (A-Z), numbers (0-9),
  // and dashes (-).
  //
  // See also [migration to feed
  // labels](https://developers.google.com/shopping-content/guides/products/feed-labels).
  string feed_label = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Immutable. The two-letter ISO 639-1 language of the items to
  // which the regional inventory is provided.
  string content_language = 5 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];
}

// The promotion data source.
message PromotionDataSource {
  // Required. Immutable. The target country used as part of the unique
  // identifier. Represented as a [CLDR territory
  // code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml).
  //
  // Promotions are only available in selected
  // [countries](https://support.google.com/merchants/answer/4588460).
  string target_country = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. Immutable. The two-letter ISO 639-1 language of the items in the
  // data source.
  string content_language = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.field_behavior) = IMMUTABLE
  ];
}

// The product review data source.
message ProductReviewDataSource {}

// The merchant review data source.
message MerchantReviewDataSource {}

// Data source reference can be used to manage related data sources within the
// data source service.
message DataSourceReference {
  oneof data_source_id {
    // Self should be used to reference the primary data source itself.
    bool self = 1;

    // Optional. The name of the primary data source.
    // Format:
    // `accounts/{account}/dataSources/{datasource}`
    string primary_data_source_name = 3
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. The name of the supplemental data source.
    // Format:
    // `accounts/{account}/dataSources/{datasource}`
    string supplemental_data_source_name = 2
        [(google.api.field_behavior) = OPTIONAL];
  }
}
