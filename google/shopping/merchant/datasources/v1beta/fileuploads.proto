// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.merchant.datasources.v1beta;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Shopping.Merchant.DataSources.V1Beta";
option go_package = "cloud.google.com/go/shopping/merchant/datasources/apiv1beta/datasourcespb;datasourcespb";
option java_multiple_files = true;
option java_outer_classname = "FileUploadsProto";
option java_package = "com.google.shopping.merchant.datasources.v1beta";
option php_namespace = "Google\\Shopping\\Merchant\\DataSources\\V1beta";
option ruby_package = "Google::Shopping::Merchant::DataSources::V1beta";

// Service to manage data source file uploads.
service FileUploadsService {
  option (google.api.default_host) = "merchantapi.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Gets the latest data source file upload. Only the `latest` alias is
  // accepted for a file upload.
  rpc GetFileUpload(GetFileUploadRequest) returns (FileUpload) {
    option (google.api.http) = {
      get: "/datasources/v1beta/{name=accounts/*/dataSources/*/fileUploads/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// The file upload of a specific data source, that is, the result of the
// retrieval of the data source at a certain timestamp computed asynchronously
// when the data source processing is finished. Only applicable to file data
// sources.
message FileUpload {
  option (google.api.resource) = {
    type: "merchantapi.googleapis.com/FileUpload"
    pattern: "accounts/{account}/dataSources/{datasource}/fileUploads/{fileupload}"
    plural: "fileUploads"
    singular: "fileUpload"
  };

  // An error occurring in the data source, like "invalid price".
  message Issue {
    // The severity of the issue.
    enum Severity {
      // Severity unspecified.
      SEVERITY_UNSPECIFIED = 0;

      // The issue is the warning.
      WARNING = 1;

      // The issue is an error.
      ERROR = 2;
    }

    // Output only. The title of the issue, for example, "Item too big".
    string title = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The error description, for example, "Your data source
    // contains items which have too many attributes, or are too big. These
    // items will be dropped".
    string description = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The code of the error, for example,
    // "validation/invalid_value". Returns
    // "?" if the code is unknown.
    string code = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The number of occurrences of the error in the file upload.
    int64 count = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The severity of the issue.
    Severity severity = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Link to the documentation explaining the issue in more
    // details, if available.
    string documentation_uri = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // The processing state of the data source.
  enum ProcessingState {
    // Processing state unspecified.
    PROCESSING_STATE_UNSPECIFIED = 0;

    // The data source could not be processed or all the items had errors.
    FAILED = 1;

    // The data source is being processed.
    IN_PROGRESS = 2;

    // The data source was processed successfully, though some items might have
    // had errors.
    SUCCEEDED = 3;
  }

  // Identifier. The name of the data source file upload.
  // Format:
  // `{datasource.name=accounts/{account}/dataSources/{datasource}/fileUploads/{fileupload}}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. The data source id.
  int64 data_source_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The processing state of the data source.
  ProcessingState processing_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The list of issues occurring in the data source.
  repeated Issue issues = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of items in the data source that were processed.
  int64 items_total = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of items in the data source that were created.
  int64 items_created = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of items in the data source that were updated.
  int64 items_updated = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The date at which the file of the data source was uploaded.
  google.protobuf.Timestamp upload_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for the GetFileUploadRequest method.
message GetFileUploadRequest {
  // Required. The name of the data source file upload to retrieve.
  // Format:
  // `accounts/{account}/dataSources/{datasource}/fileUploads/latest`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "merchantapi.googleapis.com/FileUpload"
    }
  ];
}
