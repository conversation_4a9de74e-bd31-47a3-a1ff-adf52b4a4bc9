// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.css.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "google/shopping/type/types.proto";

option csharp_namespace = "Google.Shopping.Css.V1";
option go_package = "cloud.google.com/go/shopping/css/apiv1/csspb;csspb";
option java_multiple_files = true;
option java_outer_classname = "CssProductCommonProto";
option java_package = "com.google.shopping.css.v1";
option php_namespace = "Google\\Shopping\\Css\\V1";
option ruby_package = "Google::Shopping::Css::V1";

// Attributes for CSS Product.
message Attributes {
  // URL directly linking to your the Product Detail Page of the CSS.
  optional string cpp_link = 1;

  // URL for the mobile-optimized version of the Product Detail Page of the CSS.
  optional string cpp_mobile_link = 2;

  // Allows advertisers to override the item URL when the product is shown
  // within the context of Product Ads.
  optional string cpp_ads_redirect = 42;

  // Low Price of the CSS Product.
  google.shopping.type.Price low_price = 3;

  // High Price of the CSS Product.
  google.shopping.type.Price high_price = 4;

  // The number of CSS Products.
  optional int64 number_of_offers = 5;

  // Condition of the headline offer.
  optional string headline_offer_condition = 6;

  // Headline Price of the CSS Product.
  google.shopping.type.Price headline_offer_price = 7;

  // Link to the headline offer.
  optional string headline_offer_link = 8;

  // Mobile Link to the headline offer.
  optional string headline_offer_mobile_link = 9;

  // Headline Price of the CSS Product.
  google.shopping.type.Price headline_offer_shipping_price = 41;

  // Title of the item.
  optional string title = 10;

  // URL of an image of the item.
  optional string image_link = 11;

  // Additional URL of images of the item.
  repeated string additional_image_links = 12;

  // Description of the item.
  optional string description = 13;

  // Product Related Attributes.[14-36]
  // Brand of the item.
  optional string brand = 14;

  // Manufacturer Part Number
  // ([MPN](https://support.google.com/merchants/answer/188494#mpn)) of the
  // item.
  optional string mpn = 15;

  // Global Trade Item Number
  // ([GTIN](https://support.google.com/merchants/answer/188494#gtin)) of the
  // item.
  optional string gtin = 16;

  // Categories of the item (formatted as in [products data
  // specification](https://support.google.com/merchants/answer/6324406)).
  repeated string product_types = 36;

  // Google's category of the item (see [Google product
  // taxonomy](https://support.google.com/merchants/answer/1705911)). When
  // querying products, this field will contain the user provided value. There
  // is currently no way to get back the auto assigned google product
  // categories through the API.
  optional string google_product_category = 17;

  // Set to true if the item is targeted towards adults.
  optional bool adult = 18;

  // The number of identical products in a merchant-defined multipack.
  optional int64 multipack = 19;

  // Whether the item is a merchant-defined bundle. A bundle is a custom
  // grouping of different products sold by a merchant for a single price.
  optional bool is_bundle = 20;

  // Target age group of the item.
  optional string age_group = 21;

  // Color of the item.
  optional string color = 22;

  // Target gender of the item.
  optional string gender = 23;

  // The material of which the item is made.
  optional string material = 24;

  // The item's pattern (e.g. polka dots).
  optional string pattern = 25;

  // Size of the item. Only one value is allowed. For variants with different
  // sizes, insert a separate product for each size with the same
  // `itemGroupId` value (see
  // [https://support.google.com/merchants/answer/6324492](size definition)).
  optional string size = 26;

  // System in which the size is specified. Recommended for apparel items.
  optional string size_system = 27;

  // The cut of the item. It can be used to represent combined size types for
  // apparel items. Maximum two of size types can be provided (see
  // [https://support.google.com/merchants/answer/6324497](size type)).
  repeated string size_types = 28;

  // Shared identifier for all variants of the same product.
  optional string item_group_id = 29;

  // Technical specification or additional product details.
  repeated ProductDetail product_details = 30;

  // The weight of the product in the units provided. The value must be
  // between 0 (exclusive) and 2000 (inclusive).
  ProductWeight product_weight = 31;

  // The length of the product in the units provided. The value must be
  // between 0 (exclusive) and 3000 (inclusive).
  ProductDimension product_length = 32;

  // The width of the product in the units provided. The value must be between
  // 0 (exclusive) and 3000 (inclusive).
  ProductDimension product_width = 33;

  // The height of the product in the units provided. The value must be
  // between
  // 0 (exclusive) and 3000 (inclusive).
  ProductDimension product_height = 34;

  // Bullet points describing the most relevant highlights of a product.
  repeated string product_highlights = 35;

  // A list of certificates claimed by the CSS for the given product.
  repeated Certification certifications = 39;

  // Date on which the item should expire, as specified upon insertion, in
  // [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format. The actual
  // expiration date is exposed in `productstatuses` as
  // [googleExpirationDate](https://support.google.com/merchants/answer/6324499)
  // and might be earlier if `expirationDate` is too far in the future.
  // Note: It may take 2+ days from the expiration date for the item to
  // actually get deleted.
  google.protobuf.Timestamp expiration_date = 40;

  // The list of destinations to include for this target (corresponds to
  // checked check boxes in Merchant Center). Default destinations are always
  // included unless provided in `excludedDestinations`.
  repeated string included_destinations = 43;

  // The list of destinations to exclude for this target (corresponds to
  // unchecked check boxes in Merchant Center).
  repeated string excluded_destinations = 44;

  // Publication of this item will be temporarily paused.
  optional string pause = 45;

  // Custom label 0 for custom grouping of items in a Shopping campaign.
  optional string custom_label_0 = 46;

  // Custom label 1 for custom grouping of items in a Shopping campaign.
  optional string custom_label_1 = 47;

  // Custom label 2 for custom grouping of items in a Shopping campaign.
  optional string custom_label_2 = 48;

  // Custom label 3 for custom grouping of items in a Shopping campaign.
  optional string custom_label_3 = 49;

  // Custom label 4 for custom grouping of items in a Shopping campaign.
  optional string custom_label_4 = 50;

  // Number and amount of installments to pay for an item.
  HeadlineOfferInstallment headline_offer_installment = 51;

  // Number of periods (months or years) and amount of payment per period
  // for an item with an associated subscription contract.
  HeadlineOfferSubscriptionCost headline_offer_subscription_cost = 52;
}

// The certification for the product. Use the this attribute to describe
// certifications, such as energy efficiency ratings, associated with a product.
message Certification {
  // The name of the certification. At this time, the most common value is
  // "EPREL", which represents energy efficiency certifications in the EU
  // European Registry for Energy Labeling (EPREL) database.
  string name = 1;

  // The authority or certification body responsible for issuing the
  // certification. At this time, the most common value is "EC" or
  // “European_Commission” for energy labels in the EU.
  string authority = 2;

  // The code of the certification. For example, for the EPREL certificate with
  // the link https://eprel.ec.europa.eu/screen/product/dishwashers2019/123456
  // the code is 123456. The code is required for European Energy Labels.
  string code = 3;
}

// The product details.
message ProductDetail {
  // The section header used to group a set of product details.
  string section_name = 1;

  // The name of the product detail.
  string attribute_name = 2;

  // The value of the product detail.
  string attribute_value = 3;
}

// The dimension of the product.
message ProductDimension {
  // Required. The dimension value represented as a number. The value can have a
  // maximum precision of four decimal places.
  double value = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The dimension units.
  // Acceptable values are:
  //   * "`in`"
  //   * "`cm`"
  string unit = 2 [(google.api.field_behavior) = REQUIRED];
}

// The weight of the product.
message ProductWeight {
  // Required. The weight represented as a number. The weight can have a maximum
  // precision of four decimal places.
  double value = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The weight unit.
  // Acceptable values are:
  //   * "`g`"
  //   * "`kg`"
  //   * "`oz`"
  //   * "`lb`"
  string unit = 2 [(google.api.field_behavior) = REQUIRED];
}

// The status of the Css Product, data validation issues, that is,
// information about the Css Product computed asynchronously.
message CssProductStatus {
  // The destination status of the product status.
  message DestinationStatus {
    // The name of the destination
    string destination = 1;

    // List of country codes (ISO 3166-1 alpha-2) where the CSS Product is
    // approved.
    repeated string approved_countries = 2;

    // List of country codes (ISO 3166-1 alpha-2) where the CSS Product is
    // pending approval.
    repeated string pending_countries = 3;

    // List of country codes (ISO 3166-1 alpha-2) where the CSS Product is
    // disapproved.
    repeated string disapproved_countries = 4;
  }

  // The ItemLevelIssue of the product status.
  message ItemLevelIssue {
    // The error code of the issue.
    string code = 1;

    // How this issue affects serving of the CSS Product.
    string servability = 2;

    // Whether the issue can be resolved by the merchant.
    string resolution = 3;

    // The attribute's name, if the issue is caused by a single attribute.
    string attribute = 4;

    // The destination the issue applies to.
    string destination = 5;

    // A short issue description in English.
    string description = 6;

    // A detailed issue description in English.
    string detail = 7;

    // The URL of a web page to help with resolving this issue.
    string documentation = 8;

    // List of country codes (ISO 3166-1 alpha-2) where issue applies to the
    // CSS Product.
    repeated string applicable_countries = 9;
  }

  // The intended destinations for the product.
  repeated DestinationStatus destination_statuses = 3;

  // A list of all issues associated with the product.
  repeated ItemLevelIssue item_level_issues = 4;

  // Date on which the item has been created, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp creation_date = 5;

  // Date on which the item has been last updated, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp last_update_date = 6;

  // Date on which the item expires, in [ISO
  // 8601](http://en.wikipedia.org/wiki/ISO_8601) format.
  google.protobuf.Timestamp google_expiration_date = 7;
}

// The SubscriptionCost of the product.
message HeadlineOfferSubscriptionCost {
  // The type of subscription period.
  // Supported values are:
  //   * "`month`"
  //   * "`year`"
  SubscriptionPeriod period = 1;

  // The number of subscription periods the buyer has to pay.
  int64 period_length = 2;

  // The amount the buyer has to pay per subscription period.
  google.shopping.type.Price amount = 3;
}

// A message that represents installment.
message HeadlineOfferInstallment {
  // The number of installments the buyer has to pay.
  int64 months = 1;

  // The amount the buyer has to pay per month.
  google.shopping.type.Price amount = 2;

  // The up-front down payment amount the buyer has to pay.
  google.shopping.type.Price downpayment = 3;
}

// The subscription period of the product.
enum SubscriptionPeriod {
  // Indicates that the subscription period is unspecified.
  SUBSCRIPTION_PERIOD_UNSPECIFIED = 0;

  // Indicates that the subscription period is month.
  MONTH = 1;

  // Indicates that the subscription period is year.
  YEAR = 2;
}
