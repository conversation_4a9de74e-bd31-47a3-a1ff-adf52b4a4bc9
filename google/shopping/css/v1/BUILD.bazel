# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "css_proto",
    srcs = [
        "accounts.proto",
        "accounts_labels.proto",
        "css_product_common.proto",
        "css_product_inputs.proto",
        "css_products.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/shopping/type:type_proto", # Manual fix. Original :types_proto
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "css_proto_with_info",
    deps = [
        ":css_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "css_java_proto",
    deps = [":css_proto"],
)

java_grpc_library(
    name = "css_java_grpc",
    srcs = [":css_proto"],
    deps = [":css_java_proto"],
)

java_gapic_library(
    name = "css_java_gapic",
    srcs = [":css_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "css_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "css_v1.yaml",
    test_deps = [
        ":css_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":css_java_proto",
        "//google/api:api_java_proto",
        "//google/shopping/type:type_java_proto", # Added manually
    ],
)

java_gapic_test(
    name = "css_java_gapic_test_suite",
    test_classes = [
        "com.google.shopping.css.v1.AccountLabelsServiceClientHttpJsonTest",
        "com.google.shopping.css.v1.AccountLabelsServiceClientTest",
        "com.google.shopping.css.v1.AccountsServiceClientHttpJsonTest",
        "com.google.shopping.css.v1.AccountsServiceClientTest",
        "com.google.shopping.css.v1.CssProductInputsServiceClientHttpJsonTest",
        "com.google.shopping.css.v1.CssProductInputsServiceClientTest",
        "com.google.shopping.css.v1.CssProductsServiceClientHttpJsonTest",
        "com.google.shopping.css.v1.CssProductsServiceClientTest",
    ],
    runtime_deps = [":css_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-shopping-css-v1-java",
    transport = "grpc+rest",
    deps = [
        ":css_java_gapic",
        ":css_java_grpc",
        ":css_java_proto",
        ":css_proto",
        "//google/shopping/type:type_java_proto", # Added manually
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "css_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/shopping/css/apiv1/csspb",
    protos = [":css_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/shopping/type:type_go_proto", # Added manually
    ],
)

go_gapic_library(
    name = "css_go_gapic",
    srcs = [":css_proto_with_info"],
    grpc_service_config = "css_grpc_service_config.json",
    importpath = "cloud.google.com/go/shopping/css/apiv1;css", # Manual fix
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "css_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":css_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-shopping-css-v1-go",
    deps = [
        ":css_go_gapic",
        ":css_go_gapic_srcjar-test.srcjar",
        ":css_go_gapic_srcjar-metadata.srcjar",
        ":css_go_gapic_srcjar-snippets.srcjar",
        ":css_go_proto",
        "//google/api:api_go_proto", # Added manually
        "//google/shopping/type:type_go_proto", # Added manually
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "py_import",
)

py_import( # Added manually
    name = "shopping_type",
    srcs = [
        "//google/shopping/type:type_py_gapic",
    ],
)

py_gapic_library(
    name = "css_py_gapic",
    srcs = [":css_proto"],
    grpc_service_config = "css_grpc_service_config.json",
    opt_args = ["proto-plus-deps=google.shopping.type"], # Added manually
    rest_numeric_enums = True,
    service_yaml = "css_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":shopping_type", # Added manually
    ],
)

py_test(
    name = "css_py_gapic_test",
    srcs = [
        "css_py_gapic_pytest.py",
        "css_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":css_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "shopping-css-v1-py",
    deps = [
        ":css_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "css_php_proto",
    deps = [":css_proto"],
)

php_gapic_library(
    name = "css_php_gapic",
    srcs = [":css_proto_with_info"],
    grpc_service_config = "css_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "css_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":css_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-shopping-css-v1-php",
    deps = [
        ":css_php_gapic",
        ":css_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "css_nodejs_gapic",
    package_name = "@google-shopping/css",
    src = ":css_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "css_grpc_service_config.json",
    package = "google.shopping.css.v1",
    rest_numeric_enums = True,
    service_yaml = "css_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "shopping-css-v1-nodejs",
    deps = [
        ":css_nodejs_gapic",
        ":css_proto",
        "//google/shopping/type:type_proto", # Added manually
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "css_ruby_proto",
    deps = [":css_proto"],
)

ruby_grpc_library(
    name = "css_ruby_grpc",
    srcs = [":css_proto"],
    deps = [":css_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "css_ruby_gapic",
    srcs = [":css_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-shopping-css-v1",
        "ruby-cloud-extra-dependencies=google-shopping-type=>0.0+<2.a",
    ],
    grpc_service_config = "css_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "css_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":css_ruby_grpc",
        ":css_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-shopping-css-v1-ruby",
    deps = [
        ":css_ruby_gapic",
        ":css_ruby_grpc",
        ":css_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "css_csharp_proto",
    extra_opts = [],
    deps = [":css_proto"],
)

csharp_grpc_library(
    name = "css_csharp_grpc",
    srcs = [":css_proto"],
    deps = [":css_csharp_proto"],
)

csharp_gapic_library(
    name = "css_csharp_gapic",
    srcs = [":css_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "css_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "css_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":css_csharp_grpc",
        ":css_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-shopping-css-v1-csharp",
    deps = [
        ":css_csharp_gapic",
        ":css_csharp_grpc",
        ":css_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "css_cc_proto",
    deps = [":css_proto"],
)

cc_grpc_library(
    name = "css_cc_grpc",
    srcs = [":css_proto"],
    grpc_only = True,
    deps = [":css_cc_proto"],
)
