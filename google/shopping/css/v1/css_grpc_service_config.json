{"methodConfig": [{"name": [{"service": "google.shopping.css.v1.AccountsService", "method": "GetAccount"}, {"service": "google.shopping.css.v1.AccountsService", "method": "ListChildAccounts"}, {"service": "google.shopping.css.v1.AccountLabelsService", "method": "ListAccountLabels"}, {"service": "google.shopping.css.v1.CssProductsService", "method": "GetCssProduct"}, {"service": "google.shopping.css.v1.CssProductsService", "method": "ListCssProducts"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.shopping.css.v1.AccountsService", "method": "Update<PERSON><PERSON><PERSON>"}, {"service": "google.shopping.css.v1.AccountLabelsService", "method": "CreateAccountLabel"}, {"service": "google.shopping.css.v1.AccountLabelsService", "method": "UpdateAccountLabel"}, {"service": "google.shopping.css.v1.AccountLabelsService", "method": "DeleteAccountLabel"}, {"service": "google.shopping.css.v1.CssProductInputsService", "method": "InsertCssProductInput"}, {"service": "google.shopping.css.v1.CssProductInputsService", "method": "UpdateCssProductInput"}, {"service": "google.shopping.css.v1.CssProductInputsService", "method": "DeleteCssProductInput"}], "timeout": "60s"}]}