// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.css.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/shopping/css/v1/css_product_common.proto";
import "google/shopping/type/types.proto";

option csharp_namespace = "Google.Shopping.Css.V1";
option go_package = "cloud.google.com/go/shopping/css/apiv1/csspb;csspb";
option java_multiple_files = true;
option java_outer_classname = "CssProductInputsProto";
option java_package = "com.google.shopping.css.v1";
option php_namespace = "Google\\Shopping\\Css\\V1";
option ruby_package = "Google::Shopping::Css::V1";

// Service to use CssProductInput resource.
// This service helps to insert/update/delete CSS Products.
service CssProductInputsService {
  option (google.api.default_host) = "css.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Uploads a CssProductInput to your CSS Center account. If an
  // input with the same contentLanguage, identity, feedLabel and feedId already
  // exists, this method replaces that entry.
  //
  // After inserting, updating, or deleting a CSS Product input, it may
  // take several minutes before the processed CSS Product can be retrieved.
  rpc InsertCssProductInput(InsertCssProductInputRequest)
      returns (CssProductInput) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*}/cssProductInputs:insert"
      body: "css_product_input"
    };
  }

  // Updates the existing Css Product input in your CSS Center account.
  //
  // After inserting, updating, or deleting a CSS Product input, it may take
  // several minutes before the processed Css Product can be retrieved.
  rpc UpdateCssProductInput(UpdateCssProductInputRequest)
      returns (CssProductInput) {
    option (google.api.http) = {
      patch: "/v1/{css_product_input.name=accounts/*/cssProductInputs/*}"
      body: "css_product_input"
    };
    option (google.api.method_signature) = "css_product_input,update_mask";
  }

  // Deletes a CSS Product input from your CSS Center account.
  //
  // After a delete it may take several minutes until the input is no longer
  // available.
  rpc DeleteCssProductInput(DeleteCssProductInputRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=accounts/*/cssProductInputs/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// This resource represents input data you submit for a CSS Product, not
// the processed CSS Product that you see in CSS Center, in Shopping Ads, or
// across Google surfaces.
message CssProductInput {
  option (google.api.resource) = {
    type: "css.googleapis.com/CssProductInput"
    pattern: "accounts/{account}/cssProductInputs/{css_product_input}"
  };

  // The name of the CSS Product input.
  // Format:
  // `accounts/{account}/cssProductInputs/{css_product_input}`
  string name = 1;

  // Output only. The name of the processed CSS Product.
  // Format:
  // `accounts/{account}/cssProducts/{css_product}`
  // "
  string final_name = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Your unique identifier for the CSS Product. This is the same for
  // the CSS Product input and processed CSS Product. We only allow ids with
  // alphanumerics, underscores and dashes. See the [products feed
  // specification](https://support.google.com/merchants/answer/188494#id) for
  // details.
  string raw_provided_id = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The two-letter [ISO
  // 639-1](http://en.wikipedia.org/wiki/ISO_639-1) language code for the CSS
  // Product.
  string content_language = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The [feed
  // label](https://developers.google.com/shopping-content/guides/products/feed-labels)
  // for the CSS Product.
  // Feed Label is synonymous to "target country" and hence should always be a
  // valid region code. For example: 'DE' for Germany, 'FR' for France.
  string feed_label = 5 [(google.api.field_behavior) = REQUIRED];

  // DEPRECATED. Use expiration_date instead.
  // Represents the existing version (freshness) of the CSS Product, which
  // can be used to preserve the right order when multiple updates are done at
  // the same time.
  //
  // This field must not be set to the future time.
  //
  // If set, the update is prevented if a newer version of the item already
  // exists in our system (that is the last update time of the existing
  // CSS products is later than the freshness time set in the update). If
  // the update happens, the last update time is then set to this freshness
  // time.
  //
  // If not set, the update will not be prevented and the last update time will
  // default to when this request was received by the CSS API.
  //
  // If the operation is prevented, the aborted exception will be
  // thrown.
  google.protobuf.Timestamp freshness_time = 6 [deprecated = true];

  // A list of CSS Product attributes.
  Attributes attributes = 7;

  // A list of custom (CSS-provided) attributes. It can also be used for
  // submitting any attribute of the feed specification in its generic
  // form (for example:
  // `{ "name": "size type", "value": "regular" }`).
  // This is useful for submitting attributes not explicitly exposed by the
  // API, such as additional attributes used for Buy on Google.
  repeated google.shopping.type.CustomAttribute custom_attributes = 8;
}

// Request message for the InsertCssProductInput method.
message InsertCssProductInputRequest {
  // Required. The account where this CSS Product will be inserted.
  // Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "css.googleapis.com/CssProductInput"
    }
  ];

  // Required. The CSS Product Input to insert.
  CssProductInput css_product_input = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. DEPRECATED. Feed id is not required for CSS Products.
  // The primary or supplemental feed id. If CSS Product already exists and
  // feed id provided is different, then the CSS Product will be moved to a
  // new feed.
  // Note: For now, CSSs do not need to provide feed ids as we create
  // feeds on the fly.
  // We do not have supplemental feed support for CSS Products yet.
  int64 feed_id = 3 [deprecated = true, (google.api.field_behavior) = OPTIONAL];
}

// Request message for the UpdateCssProductInput method.
message UpdateCssProductInputRequest {
  // Required. The CSS product input resource to update. Information you submit
  // will be applied to the processed CSS product as well.
  CssProductInput css_product_input = 1
      [(google.api.field_behavior) = REQUIRED];

  // The list of CSS product attributes to be updated.
  //
  // If the update mask is omitted, then it is treated as implied field mask
  // equivalent to all fields that are populated (have a non-empty value).
  //
  // Attributes specified in the update mask without a value specified in the
  // body will be deleted from the CSS product.
  //
  // Update mask can only be specified for top level fields in
  // attributes and custom attributes.
  //
  // To specify the update mask for custom attributes you need to add the
  // `custom_attribute.` prefix.
  //
  // Providing special "*" value for full CSS product replacement is not
  // supported.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for the DeleteCssProductInput method.
message DeleteCssProductInputRequest {
  // Required. The name of the CSS product input resource to delete.
  // Format: accounts/{account}/cssProductInputs/{css_product_input}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "css.googleapis.com/CssProductInput"
    }
  ];

  // The Content API Supplemental Feed ID.
  // The field must not be set if the action applies to a primary feed.
  // If the field is set, then product action applies to a supplemental feed
  // instead of primary Content API feed.
  optional int64 supplemental_feed_id = 2;
}
