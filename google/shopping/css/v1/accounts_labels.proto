// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.css.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Shopping.Css.V1";
option go_package = "cloud.google.com/go/shopping/css/apiv1/csspb;csspb";
option java_multiple_files = true;
option java_outer_classname = "AccountsLabelsProto";
option java_package = "com.google.shopping.css.v1";
option php_namespace = "Google\\Shopping\\Css\\V1";
option ruby_package = "Google::Shopping::Css::V1";

// Manages Merchant Center and CSS accounts labels.
service AccountLabelsService {
  option (google.api.default_host) = "css.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Lists the labels owned by an account.
  rpc ListAccountLabels(ListAccountLabelsRequest)
      returns (ListAccountLabelsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*}/labels"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new label, not assigned to any account.
  rpc CreateAccountLabel(CreateAccountLabelRequest) returns (AccountLabel) {
    option (google.api.http) = {
      post: "/v1/{parent=accounts/*}/labels"
      body: "account_label"
    };
    option (google.api.method_signature) = "parent,account_label";
  }

  // Updates a label.
  rpc UpdateAccountLabel(UpdateAccountLabelRequest) returns (AccountLabel) {
    option (google.api.http) = {
      patch: "/v1/{account_label.name=accounts/*/labels/*}"
      body: "account_label"
    };
    option (google.api.method_signature) = "account_label";
  }

  // Deletes a label and removes it from all accounts to which it was assigned.
  rpc DeleteAccountLabel(DeleteAccountLabelRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=accounts/*/labels/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Label assigned by CSS domain or CSS group to one of its sub-accounts.
message AccountLabel {
  option (google.api.resource) = {
    type: "css.googleapis.com/AccountLabel"
    pattern: "accounts/{account}/labels/{label}"
    plural: "accountLabels"
    singular: "accountLabel"
  };

  // The label type.
  enum LabelType {
    // Unknown label type.
    LABEL_TYPE_UNSPECIFIED = 0;

    // Indicates that the label was created manually.
    MANUAL = 1;

    // Indicates that the label was created automatically by CSS Center.
    AUTOMATIC = 2;
  }

  // The resource name of the label.
  // Format: accounts/{account}/labels/{label}
  string name = 1;

  // Output only. The ID of the label.
  int64 label_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ID of account this label belongs to.
  int64 account_id = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The display name of this label.
  optional string display_name = 4;

  // The description of this label.
  optional string description = 5;

  // Output only. The type of this label.
  LabelType label_type = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for the `ListAccountLabels` method.
message ListAccountLabelsRequest {
  // Required. The parent account.
  // Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "css.googleapis.com/AccountLabel"
    }
  ];

  // The maximum number of labels to return. The service may return fewer than
  // this value.
  // If unspecified, at most 50 labels will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListAccountLabels` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListAccountLabels` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message for the `ListAccountLabels` method.
message ListAccountLabelsResponse {
  // The labels from the specified account.
  repeated AccountLabel account_labels = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for the 'CreateAccountLanel' method.
message CreateAccountLabelRequest {
  // Required. The parent account.
  // Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "css.googleapis.com/AccountLabel"
    }
  ];

  // Required. The label to create.
  AccountLabel account_label = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the `UpdateAccountLabel` method.
message UpdateAccountLabelRequest {
  // Required. The updated label. All fields must be provided.
  AccountLabel account_label = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request message for the 'DeleteAccountLabel' method.
message DeleteAccountLabelRequest {
  // Required. The name of the label to delete.
  // Format:  accounts/{account}/labels/{label}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "css.googleapis.com/AccountLabel"
    }
  ];
}
