// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.css.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/shopping/css/v1/css_product_common.proto";
import "google/shopping/type/types.proto";

option csharp_namespace = "Google.Shopping.Css.V1";
option go_package = "cloud.google.com/go/shopping/css/apiv1/csspb;csspb";
option java_multiple_files = true;
option java_outer_classname = "CssProductsProto";
option java_package = "com.google.shopping.css.v1";
option php_namespace = "Google\\Shopping\\Css\\V1";
option ruby_package = "Google::Shopping::Css::V1";

// Service for doing get and list on Css Products(a.k.a Aggregate Offers
// internally).
service CssProductsService {
  option (google.api.default_host) = "css.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Retrieves the processed CSS Product from your CSS Center account. After
  // inserting, updating, or deleting a product input, it may take several
  // minutes before the updated final product can be retrieved.
  rpc GetCssProduct(GetCssProductRequest) returns (CssProduct) {
    option (google.api.http) = {
      get: "/v1/{name=accounts/*/cssProducts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the processed CSS Products in your CSS Center account. The response
  // might contain fewer items than specified by pageSize. Rely on pageToken to
  // determine if there are more items to be requested.
  //
  // After inserting, updating, or deleting a CSS product input, it may
  // take several minutes before the updated processed CSS product can be
  // retrieved.
  rpc ListCssProducts(ListCssProductsRequest)
      returns (ListCssProductsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*}/cssProducts"
    };
    option (google.api.method_signature) = "parent";
  }
}

// The request message for the `GetCssProduct` method.
message GetCssProductRequest {
  // Required. The name of the CSS product to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "css.googleapis.com/CssProduct" }
  ];
}

// The processed CSS Product.
message CssProduct {
  option (google.api.resource) = {
    type: "css.googleapis.com/CssProduct"
    pattern: "accounts/{account}/cssProducts/{css_product}"
    plural: "cssProducts"
    singular: "cssProduct"
  };

  // The name of the CSS Product.
  // Format:
  // `"accounts/{account}/cssProducts/{css_product}"`
  string name = 1;

  // Output only. Your unique raw identifier for the product.
  string raw_provided_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The two-letter [ISO
  // 639-1](http://en.wikipedia.org/wiki/ISO_639-1) language code for the
  // product.
  string content_language = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The feed label for the product.
  string feed_label = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of product attributes.
  Attributes attributes = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A list of custom (CSS-provided) attributes. It can also be
  // used to submit any attribute of the feed specification in its generic form
  // (for example,
  // `{ "name": "size type", "value": "regular" }`).
  // This is useful for submitting attributes not explicitly exposed by the
  // API, such as additional attributes used for Buy on Google.
  repeated google.shopping.type.CustomAttribute custom_attributes = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of a product, data validation issues, that is,
  // information about a product computed asynchronously.
  CssProductStatus css_product_status = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for the ListCssProducts method.
message ListCssProductsRequest {
  // Required. The account/domain to list processed CSS Products for.
  // Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "css.googleapis.com/CssProduct"
    }
  ];

  // The maximum number of CSS Products to return. The service may return
  // fewer than this value.
  // The maximum value is 1000; values above 1000 will be coerced to 1000. If
  // unspecified, the maximum number of CSS products will be returned.
  int32 page_size = 2;

  // A page token, received from a previous `ListCssProducts` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCssProducts`
  // must match the call that provided the page token.
  string page_token = 3;
}

// Response message for the ListCssProducts method.
message ListCssProductsResponse {
  // The processed CSS products from the specified account. These are your
  // processed CSS products after applying rules and supplemental feeds.
  repeated CssProduct css_products = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
