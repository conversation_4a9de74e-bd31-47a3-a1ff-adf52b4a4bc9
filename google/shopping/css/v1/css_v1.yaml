type: google.api.Service
config_version: 3
name: css.googleapis.com
title: CSS API

apis:
- name: google.shopping.css.v1.AccountLabelsService
- name: google.shopping.css.v1.AccountsService
- name: google.shopping.css.v1.CssProductInputsService
- name: google.shopping.css.v1.CssProductsService

documentation:
  summary: |-
    Programmatically manage your Comparison Shopping Service (CSS) account data
    at scale.

authentication:
  rules:
  - selector: 'google.shopping.css.v1.AccountLabelsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/content
  - selector: 'google.shopping.css.v1.AccountsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/content
  - selector: 'google.shopping.css.v1.CssProductInputsService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/content
  - selector: google.shopping.css.v1.CssProductsService.GetCssProduct
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/content
  - selector: google.shopping.css.v1.CssProductsService.ListCssProducts
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/content

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=826068&template=1564577
  documentation_uri: https://developers.google.com/comparison-shopping-services/api
  api_short_name: css
  github_label: 'api: css'
  doc_tag_prefix: css
  organization: SHOPPING
  library_settings:
  - version: google.shopping.css.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
