{"methodConfig": [{"name": [{"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "ListIndexes"}, {"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "GetIndex"}, {"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "DeleteIndex"}, {"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "GetField"}, {"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "ListFields"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE", "INTERNAL", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "CreateIndex"}, {"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "ImportDocuments"}, {"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "ExportDocuments"}, {"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "UpdateField"}, {"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "BulkDeleteDocuments"}], "timeout": "60s"}, {"name": [{"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "CreateDatabase"}, {"service": "google.firestore.admin.v1.FirestoreAdmin", "method": "RestoreDatabase"}], "timeout": "120s"}]}