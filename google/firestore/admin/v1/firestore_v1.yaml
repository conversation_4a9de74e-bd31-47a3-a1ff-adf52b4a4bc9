type: google.api.Service
config_version: 3
name: firestore.googleapis.com
title: Cloud Firestore API

apis:
- name: google.cloud.location.Locations
- name: google.firestore.admin.v1.FirestoreAdmin
- name: google.longrunning.Operations

types:
- name: google.firestore.admin.v1.BulkDeleteDocumentsMetadata
- name: google.firestore.admin.v1.CreateDatabaseMetadata
- name: google.firestore.admin.v1.DeleteDatabaseMetadata
- name: google.firestore.admin.v1.ExportDocumentsMetadata
- name: google.firestore.admin.v1.ExportDocumentsResponse
- name: google.firestore.admin.v1.FieldOperationMetadata
- name: google.firestore.admin.v1.ImportDocumentsMetadata
- name: google.firestore.admin.v1.IndexOperationMetadata
- name: google.firestore.admin.v1.LocationMetadata
- name: google.firestore.admin.v1.RestoreDatabaseMetadata
- name: google.firestore.admin.v1.UpdateDatabaseMetadata

documentation:
  summary: |-
    Accesses the NoSQL document database built for automatic scaling, high
    performance, and ease of application development.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/databases/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/databases/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/databases/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/databases/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: 'google.firestore.admin.v1.FirestoreAdmin.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
