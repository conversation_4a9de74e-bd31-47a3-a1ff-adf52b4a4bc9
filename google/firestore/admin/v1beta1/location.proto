// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.firestore.admin.v1beta1;

import "google/type/latlng.proto";
import "google/api/annotations.proto";

option csharp_namespace = "Google.Cloud.Firestore.Admin.V1Beta1";
option go_package = "cloud.google.com/go/firestore/admin/apiv1beta1/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "LocationProto";
option java_package = "com.google.firestore.admin.v1beta1";
option objc_class_prefix = "GCFS";

// The metadata message for [google.cloud.location.Location.metadata][google.cloud.location.Location.metadata].
message LocationMetadata {

}
