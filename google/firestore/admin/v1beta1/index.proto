// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.firestore.admin.v1beta1;

import "google/api/annotations.proto";

option csharp_namespace = "Google.Cloud.Firestore.Admin.V1Beta1";
option go_package = "cloud.google.com/go/firestore/admin/apiv1beta1/adminpb;adminpb";
option java_multiple_files = true;
option java_outer_classname = "IndexProto";
option java_package = "com.google.firestore.admin.v1beta1";
option objc_class_prefix = "GCFS";

// A field of an index.
message IndexField {
  // The mode determines how a field is indexed.
  enum Mode {
    // The mode is unspecified.
    MODE_UNSPECIFIED = 0;

    // The field's values are indexed so as to support sequencing in
    // ascending order and also query by <, >, <=, >=, and =.
    ASCENDING = 2;

    // The field's values are indexed so as to support sequencing in
    // descending order and also query by <, >, <=, >=, and =.
    DESCENDING = 3;

    // The field's array values are indexed so as to support membership using
    // ARRAY_CONTAINS queries.
    ARRAY_CONTAINS = 4;
  }

  // The path of the field. Must match the field path specification described
  // by [google.firestore.v1beta1.Document.fields][fields].
  // Special field path `__name__` may be used by itself or at the end of a
  // path. `__type__` may be used only at the end of path.
  string field_path = 1;

  // The field's mode.
  Mode mode = 2;
}

// An index definition.
message Index {
  // The state of an index. During index creation, an index will be in the
  // `CREATING` state. If the index is created successfully, it will transition
  // to the `READY` state. If the index is not able to be created, it will
  // transition to the `ERROR` state.
  enum State {
    // The state is unspecified.
    STATE_UNSPECIFIED = 0;

    // The index is being created.
    // There is an active long-running operation for the index.
    // The index is updated when writing a document.
    // Some index data may exist.
    CREATING = 3;

    // The index is ready to be used.
    // The index is updated when writing a document.
    // The index is fully populated from all stored documents it applies to.
    READY = 2;

    // The index was being created, but something went wrong.
    // There is no active long-running operation for the index,
    // and the most recently finished long-running operation failed.
    // The index is not updated when writing a document.
    // Some index data may exist.
    ERROR = 5;
  }

  // The resource name of the index.
  // Output only.
  string name = 1;

  // The collection ID to which this index applies. Required.
  string collection_id = 2;

  // The fields to index.
  repeated IndexField fields = 3;

  // The state of the index.
  // Output only.
  State state = 6;
}
