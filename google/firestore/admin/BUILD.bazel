# This build file includes a target for the Ruby wrapper library for
# google-cloud-firestore-admin.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for firestore admin.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "admin_ruby_wrapper",
    srcs = ["//google/firestore/admin/v1:admin_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-firestore-admin",
        "ruby-cloud-env-prefix=FIRESTORE",
        "ruby-cloud-wrapper-of=v1:0.14",
        "ruby-cloud-product-url=https://cloud.google.com/firestore",
        "ruby-cloud-api-id=firestore.googleapis.com",
        "ruby-cloud-api-shortname=firestore",
    ],
    ruby_cloud_description = "Cloud Firestore is a NoSQL document database built for automatic scaling, high performance, and ease of application development.",
    ruby_cloud_title = "Cloud Firestore Admin",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-firestore-admin-ruby",
    deps = [
        ":admin_ruby_wrapper",
    ],
)
