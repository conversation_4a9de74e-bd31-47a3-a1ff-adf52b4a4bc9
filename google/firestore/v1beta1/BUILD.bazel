# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "firestore_proto",
    srcs = [
        "common.proto",
        "document.proto",
        "firestore.proto",
        "query.proto",
        "undeliverable_first_gen_event.proto",
        "write.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/rpc:status_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "firestore_proto_with_info",
    deps = [
        ":firestore_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "firestore_java_proto",
    deps = [":firestore_proto"],
)

java_grpc_library(
    name = "firestore_java_grpc",
    srcs = [":firestore_proto"],
    deps = [":firestore_java_proto"],
)

java_gapic_library(
    name = "firestore_java_gapic",
    srcs = [":firestore_proto_with_info"],
    gapic_yaml = "firestore_gapic.yaml",
    grpc_service_config = "firestore_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "firestore_v1beta1.yaml",
    test_deps = [
        ":firestore_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":firestore_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "firestore_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.firestore.v1beta1.FirestoreClientHttpJsonTest",
        "com.google.cloud.firestore.v1beta1.FirestoreClientTest",
    ],
    runtime_deps = [":firestore_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-firestore-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":firestore_java_gapic",
        ":firestore_java_grpc",
        ":firestore_java_proto",
        ":firestore_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "firestore_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/firestore/apiv1beta1/firestorepb",
    protos = [":firestore_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "firestore_go_gapic",
    srcs = [":firestore_proto_with_info"],
    grpc_service_config = "firestore_grpc_service_config.json",
    importpath = "cloud.google.com/go/firestore/apiv1beta1;firestore",
    rest_numeric_enums = True,
    service_yaml = "firestore_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":firestore_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-firestore-v1beta1-go",
    deps = [
        ":firestore_go_gapic",
        ":firestore_go_gapic_srcjar-snippets.srcjar",
        ":firestore_go_gapic_srcjar-test.srcjar",
        ":firestore_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "firestore_py_gapic",
    srcs = [":firestore_proto"],
    grpc_service_config = "firestore_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "firestore_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "firestore_py_gapic_test",
    srcs = [
        "firestore_py_gapic_pytest.py",
        "firestore_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":firestore_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "firestore-v1beta1-py",
    deps = [
        ":firestore_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "firestore_php_proto",
    deps = [":firestore_proto"],
)

php_gapic_library(
    name = "firestore_php_gapic",
    srcs = [":firestore_proto_with_info"],
    grpc_service_config = "firestore_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "firestore_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":firestore_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-firestore-v1beta1-php",
    deps = [
        ":firestore_php_gapic",
        ":firestore_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "firestore_nodejs_gapic",
    package_name = "@google-cloud/firestore",
    src = ":firestore_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "firestore_grpc_service_config.json",
    package = "google.firestore.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "firestore_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "firestore-v1beta1-nodejs",
    deps = [
        ":firestore_nodejs_gapic",
        ":firestore_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "firestore_ruby_proto",
    deps = [":firestore_proto"],
)

ruby_grpc_library(
    name = "firestore_ruby_grpc",
    srcs = [":firestore_proto"],
    deps = [":firestore_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "firestore_ruby_gapic",
    srcs = [":firestore_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-firestore-v1beta1"],
    grpc_service_config = "firestore_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "firestore_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":firestore_ruby_grpc",
        ":firestore_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-firestore-v1beta1-ruby",
    deps = [
        ":firestore_ruby_gapic",
        ":firestore_ruby_grpc",
        ":firestore_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "firestore_csharp_proto",
    extra_opts = [],
    deps = [":firestore_proto"],
)

csharp_grpc_library(
    name = "firestore_csharp_grpc",
    srcs = [":firestore_proto"],
    deps = [":firestore_csharp_proto"],
)

csharp_gapic_library(
    name = "firestore_csharp_gapic",
    srcs = [":firestore_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "firestore_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "firestore_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":firestore_csharp_grpc",
        ":firestore_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-firestore-v1beta1-csharp",
    deps = [
        ":firestore_csharp_gapic",
        ":firestore_csharp_grpc",
        ":firestore_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "firestore_cc_proto",
    deps = [":firestore_proto"],
)

cc_grpc_library(
    name = "firestore_cc_grpc",
    srcs = [":firestore_proto"],
    grpc_only = True,
    deps = [":firestore_cc_proto"],
)
