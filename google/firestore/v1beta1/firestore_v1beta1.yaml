type: google.api.Service
config_version: 3
name: firestore.googleapis.com
title: Cloud Firestore API

apis:
- name: google.firestore.v1beta1.Firestore

documentation:
  summary: |-
    Accesses the NoSQL document database built for automatic scaling, high
    performance, and ease of application development.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 295.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 295.0
  - selector: 'google.firestore.v1beta1.Firestore.*'
    deadline: 295.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 295.0

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: 'google.firestore.v1beta1.Firestore.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
