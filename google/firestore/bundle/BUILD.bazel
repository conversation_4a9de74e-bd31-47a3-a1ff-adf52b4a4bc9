# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "firestore_bundle_proto",
    srcs = [
        "bundle.proto",
    ],
    deps = [
        "//google/firestore/v1:firestore_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "firestore_bundle_proto_with_info",
    deps = [
        ":firestore_bundle_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

java_proto_library(
    name = "firestore_bundle_java_proto",
    deps = [":firestore_bundle_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-firestore-bundle-v1-java",
    transport = "grpc+rest",
    deps = [
        ":firestore_bundle_java_proto",
        ":firestore_bundle_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# Put your Go rules here

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
)

py_gapic_library(
    name = "firestore_bundle_py_gapic",
    srcs = [":firestore_bundle_proto"],
    opt_args = ["python-gapic-namespace=google.cloud"],
    rest_numeric_enums = False,
    transport = "grpc",
)

py_gapic_assembly_pkg(
    name = "firestore-bundle-py",
    deps = [
        ":firestore_bundle_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# Put your PHP rules here

##############################################################################
# Node.js
##############################################################################
# Put your Node.js rules here

##############################################################################
# Ruby
##############################################################################
# Put your Ruby rules here

##############################################################################
# C#
##############################################################################
# Put your C# rules here

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "firestore_bundle_cc_proto",
    deps = [":firestore_bundle_proto"],
)

cc_grpc_library(
    name = "firestore_bundle_cc_grpc",
    srcs = [":firestore_bundle_proto"],
    grpc_only = True,
    deps = [":firestore_bundle_cc_proto"],
)
