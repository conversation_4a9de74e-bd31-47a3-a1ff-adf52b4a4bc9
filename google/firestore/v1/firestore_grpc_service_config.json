{"methodConfig": [{"name": [{"service": "google.firestore.v1.Firestore", "method": "CreateDocument"}, {"service": "google.firestore.v1.Firestore", "method": "UpdateDocument"}, {"service": "google.firestore.v1.Firestore", "method": "Commit"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["RESOURCE_EXHAUSTED", "UNAVAILABLE"]}}, {"name": [{"service": "google.firestore.v1.Firestore", "method": "BatchGetDocuments"}, {"service": "google.firestore.v1.Firestore", "method": "<PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.firestore.v1.Firestore", "method": "PartitionQuery"}, {"service": "google.firestore.v1.Firestore", "method": "RunAggregationQuery"}], "timeout": "300s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["RESOURCE_EXHAUSTED", "UNAVAILABLE", "INTERNAL", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.firestore.v1.Firestore", "method": "Listen"}], "timeout": "86400s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["RESOURCE_EXHAUSTED", "UNAVAILABLE", "INTERNAL", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.firestore.v1.Firestore", "method": "Write"}], "timeout": "86400s"}, {"name": [{"service": "google.firestore.v1.Firestore", "method": "GetDocument"}, {"service": "google.firestore.v1.Firestore", "method": "ListDocuments"}, {"service": "google.firestore.v1.Firestore", "method": "DeleteDocument"}, {"service": "google.firestore.v1.Firestore", "method": "BeginTransaction"}, {"service": "google.firestore.v1.Firestore", "method": "Rollback"}, {"service": "google.firestore.v1.Firestore", "method": "ListCollectionIds"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["RESOURCE_EXHAUSTED", "UNAVAILABLE", "INTERNAL", "DEADLINE_EXCEEDED"]}}, {"name": [{"service": "google.firestore.v1.Firestore", "method": "BatchWrite"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["RESOURCE_EXHAUSTED", "UNAVAILABLE", "ABORTED"]}}]}