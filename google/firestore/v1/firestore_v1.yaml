type: google.api.Service
config_version: 3
name: firestore.googleapis.com
title: Cloud Firestore API

apis:
- name: google.cloud.location.Locations
- name: google.firestore.v1.Firestore
- name: google.longrunning.Operations

documentation:
  summary: |-
    Accesses the NoSQL document database built for automatic scaling, high
    performance, and ease of application development.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=projects/*/databases/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.DeleteOperation
    delete: '/v1/{name=projects/*/databases/*/operations/*}'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/databases/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=projects/*/databases/*}/operations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: 'google.firestore.v1.Firestore.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/datastore
