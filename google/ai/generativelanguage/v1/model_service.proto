// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ai.generativelanguage.v1;

import "google/ai/generativelanguage/v1/model.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/ai/generativelanguage/apiv1/generativelanguagepb;generativelanguagepb";
option java_multiple_files = true;
option java_outer_classname = "ModelServiceProto";
option java_package = "com.google.ai.generativelanguage.v1";

// Provides methods for getting metadata information about Generative Models.
service ModelService {
  option (google.api.default_host) = "generativelanguage.googleapis.com";

  // Gets information about a specific `Model` such as its version number, token
  // limits,
  // [parameters](https://ai.google.dev/gemini-api/docs/models/generative-models#model-parameters)
  // and other metadata. Refer to the [Gemini models
  // guide](https://ai.google.dev/gemini-api/docs/models/gemini) for detailed
  // model information.
  rpc GetModel(GetModelRequest) returns (Model) {
    option (google.api.http) = {
      get: "/v1/{name=models/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the [`Model`s](https://ai.google.dev/gemini-api/docs/models/gemini)
  // available through the Gemini API.
  rpc ListModels(ListModelsRequest) returns (ListModelsResponse) {
    option (google.api.http) = {
      get: "/v1/models"
    };
    option (google.api.method_signature) = "page_size,page_token";
  }
}

// Request for getting information about a specific Model.
message GetModelRequest {
  // Required. The resource name of the model.
  //
  // This name should match a model name returned by the `ListModels` method.
  //
  // Format: `models/{model}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/Model"
    }
  ];
}

// Request for listing all Models.
message ListModelsRequest {
  // The maximum number of `Models` to return (per page).
  //
  // If unspecified, 50 models will be returned per page.
  // This method returns at most 1000 models per page, even if you pass a larger
  // page_size.
  int32 page_size = 2;

  // A page token, received from a previous `ListModels` call.
  //
  // Provide the `page_token` returned by one request as an argument to the next
  // request to retrieve the next page.
  //
  // When paginating, all other parameters provided to `ListModels` must match
  // the call that provided the page token.
  string page_token = 3;
}

// Response from `ListModel` containing a paginated list of Models.
message ListModelsResponse {
  // The returned Models.
  repeated Model models = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  //
  // If this field is omitted, there are no more pages.
  string next_page_token = 2;
}
