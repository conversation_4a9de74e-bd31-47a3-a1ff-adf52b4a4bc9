type: google.api.Service
config_version: 3
name: generativelanguage.googleapis.com
title: Generative Language API

apis:
- name: google.ai.generativelanguage.v1.GenerativeService
- name: google.ai.generativelanguage.v1.ModelService
- name: google.longrunning.Operations

documentation:
  summary: |-
    The Gemini API allows developers to build generative AI applications using
    Gemini models. Gemini is our most capable model, built from the ground up
    to be multimodal. It can generalize and seamlessly understand, operate
    across, and combine different types of information including language,
    images, audio, video, and code. You can use the Gemini API for use cases
    like reasoning across text and images, content generation, dialogue
    agents, summarization and classification systems, and more.

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v1/{name=tunedModels/*/operations/*}:cancel'
    body: '*'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=tunedModels/*/operations/*}'
    additional_bindings:
    - get: '/v1/{name=generatedFiles/*/operations/*}'
    - get: '/v1/{name=models/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=operations}'
    additional_bindings:
    - get: '/v1/{name=tunedModels/*}/operations'
    - get: '/v1/{name=models/*}/operations'

publishing:
  new_issue_uri: https://github.com/google/generative-ai-python/issues/new
  documentation_uri: https://ai.google.dev/docs
  api_short_name: generativelanguage
  github_label: 'api: ai'
  doc_tag_prefix: generativelanguage
  organization: GENERATIVE_AI
  library_settings:
  - version: google.ai.generativelanguage.v1
    launch_stage: EARLY_ACCESS
    java_settings:
      common: {}
    cpp_settings:
      common: {}
    php_settings:
      common: {}
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common: {}
    ruby_settings:
      common: {}
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
