// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ai.generativelanguage.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/ai/generativelanguage/apiv1alpha/generativelanguagepb;generativelanguagepb";
option java_multiple_files = true;
option java_outer_classname = "PermissionProto";
option java_package = "com.google.ai.generativelanguage.v1alpha";

// Permission resource grants user, group or the rest of the world access to the
// PaLM API resource (e.g. a tuned model, corpus).
//
// A role is a collection of permitted operations that allows users to perform
// specific actions on PaLM API resources. To make them available to users,
// groups, or service accounts, you assign roles. When you assign a role, you
// grant permissions that the role contains.
//
// There are three concentric roles. Each role is a superset of the previous
// role's permitted operations:
//
// - reader can use the resource (e.g. tuned model, corpus) for inference
// - writer has reader's permissions and additionally can edit and share
// - owner has writer's permissions and additionally can delete
message Permission {
  option (google.api.resource) = {
    type: "generativelanguage.googleapis.com/Permission"
    pattern: "tunedModels/{tuned_model}/permissions/{permission}"
    pattern: "corpora/{corpus}/permissions/{permission}"
    plural: "permissions"
    singular: "permission"
  };

  // Defines types of the grantee of this permission.
  enum GranteeType {
    // The default value. This value is unused.
    GRANTEE_TYPE_UNSPECIFIED = 0;

    // Represents a user. When set, you must provide email_address for the user.
    USER = 1;

    // Represents a group. When set, you must provide email_address for the
    // group.
    GROUP = 2;

    // Represents access to everyone. No extra information is required.
    EVERYONE = 3;
  }

  // Defines the role granted by this permission.
  enum Role {
    // The default value. This value is unused.
    ROLE_UNSPECIFIED = 0;

    // Owner can use, update, share and delete the resource.
    OWNER = 1;

    // Writer can use, update and share the resource.
    WRITER = 2;

    // Reader can use the resource.
    READER = 3;
  }

  // Output only. Identifier. The permission name. A unique name will be
  // generated on create. Examples:
  //     tunedModels/{tuned_model}/permissions/{permission}
  //     corpora/{corpus}/permissions/{permission}
  // Output only.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IDENTIFIER
  ];

  // Optional. Immutable. The type of the grantee.
  optional GranteeType grantee_type = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Optional. Immutable. The email address of the user of group which this
  // permission refers. Field is not set when permission's grantee type is
  // EVERYONE.
  optional string email_address = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // Required. The role granted by this permission.
  optional Role role = 4 [(google.api.field_behavior) = REQUIRED];
}
