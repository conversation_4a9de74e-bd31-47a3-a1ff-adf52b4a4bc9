{"methodConfig": [{"name": [{"service": "google.ai.generativelanguage.v1beta1.DiscussService", "method": "GenerateMessage"}, {"service": "google.ai.generativelanguage.v1beta1.DiscussService", "method": "Count<PERSON><PERSON><PERSON>T<PERSON>s"}, {"service": "google.ai.generativelanguage.v1beta1.ModelService", "method": "GetModel"}, {"service": "google.ai.generativelanguage.v1beta1.ModelService", "method": "ListModels"}, {"service": "google.ai.generativelanguage.v1beta2.DiscussService", "method": "GenerateMessage"}, {"service": "google.ai.generativelanguage.v1beta2.DiscussService", "method": "Count<PERSON><PERSON><PERSON>T<PERSON>s"}, {"service": "google.ai.generativelanguage.v1beta2.ModelService", "method": "GetModel"}, {"service": "google.ai.generativelanguage.v1beta2.ModelService", "method": "ListModels"}, {"service": "google.ai.generativelanguage.v1beta2.TextService", "method": "GenerateText"}, {"service": "google.ai.generativelanguage.v1beta2.TextService", "method": "EmbedText"}, {"service": "google.ai.generativelanguage.v1beta.DiscussService", "method": "GenerateMessage"}, {"service": "google.ai.generativelanguage.v1beta.DiscussService", "method": "Count<PERSON><PERSON><PERSON>T<PERSON>s"}, {"service": "google.ai.generativelanguage.v1beta.GenerativeService", "method": "GenerateAnswer"}, {"service": "google.ai.generativelanguage.v1beta.GenerativeService", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.ai.generativelanguage.v1beta.GenerativeService", "method": "BatchEmbedContents"}, {"service": "google.ai.generativelanguage.v1beta.GenerativeService", "method": "CountTokens"}, {"service": "google.ai.generativelanguage.v1beta.ModelService", "method": "GetModel"}, {"service": "google.ai.generativelanguage.v1beta.ModelService", "method": "ListModels"}, {"service": "google.ai.generativelanguage.v1beta.ModelService", "method": "CreateTunedModel"}, {"service": "google.ai.generativelanguage.v1beta.ModelService", "method": "DeleteTunedModel"}, {"service": "google.ai.generativelanguage.v1beta.ModelService", "method": "GetTunedModel"}, {"service": "google.ai.generativelanguage.v1beta.ModelService", "method": "ListTunedModels"}, {"service": "google.ai.generativelanguage.v1beta.ModelService", "method": "UpdateTunedModel"}, {"service": "google.ai.generativelanguage.v1beta.PermissionService", "method": "CreatePermission"}, {"service": "google.ai.generativelanguage.v1beta.PermissionService", "method": "ListPermission"}, {"service": "google.ai.generativelanguage.v1beta.PermissionService", "method": "GetPermission"}, {"service": "google.ai.generativelanguage.v1beta.PermissionService", "method": "UpdatePermission"}, {"service": "google.ai.generativelanguage.v1beta.PermissionService", "method": "DeletePermission"}, {"service": "google.ai.generativelanguage.v1beta.PermissionService", "method": "TransferOwnership"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "CreateCorpus"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "ListCorpora"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "GetCorpus"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "UpdateCorpus"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "DeleteCorpus"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "QueryCorpus"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "CreateDocument"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "ListDocuments"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "GetDocument"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "UpdateDocument"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "DeleteDocument"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "QueryDocument"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "CreateChunk"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "BatchCreateChunk"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "ListChunk"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "GetChunk"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "UpdateChunk"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "BatchUpdateChunk"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "DeleteChunk"}, {"service": "google.ai.generativelanguage.v1beta.RetrieverService", "method": "BatchDeleteChunk"}, {"service": "google.ai.generativelanguage.v1beta.TextService", "method": "GenerateText"}, {"service": "google.ai.generativelanguage.v1beta.TextService", "method": "EmbedText"}, {"service": "google.ai.generativelanguage.v1beta.TextService", "method": "BatchEmbedText"}, {"service": "google.ai.generativelanguage.v1beta.TextService", "method": "CountTextTokens"}, {"service": "google.ai.generativelanguage.v1.GenerativeService", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"service": "google.ai.generativelanguage.v1.GenerativeService", "method": "BatchEmbedContents"}, {"service": "google.ai.generativelanguage.v1.GenerativeService", "method": "CountTokens"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.ai.generativelanguage.v1beta.GenerativeService", "method": "GenerateContent"}, {"service": "google.ai.generativelanguage.v1beta.GenerativeService", "method": "StreamGenerateContent"}, {"service": "google.ai.generativelanguage.v1.GenerativeService", "method": "GenerateContent"}, {"service": "google.ai.generativelanguage.v1.GenerativeService", "method": "StreamGenerateContent"}], "timeout": "600s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}