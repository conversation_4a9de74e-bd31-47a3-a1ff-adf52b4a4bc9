// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ai.generativelanguage.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";

option go_package = "cloud.google.com/go/ai/generativelanguage/apiv1alpha/generativelanguagepb;generativelanguagepb";
option java_multiple_files = true;
option java_outer_classname = "PredictionServiceProto";
option java_package = "com.google.ai.generativelanguage.v1alpha";

// A service for online predictions and explanations.
service PredictionService {
  option (google.api.default_host) = "generativelanguage.googleapis.com";

  // Performs a prediction request.
  rpc Predict(PredictRequest) returns (PredictResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{model=models/*}:predict"
      body: "*"
    };
    option (google.api.method_signature) = "model,instances";
  }
}

// Request message for
// [PredictionService.Predict][google.ai.generativelanguage.v1alpha.PredictionService.Predict].
message PredictRequest {
  // Required. The name of the model for prediction.
  // Format: `name=models/{model}`.
  string model = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/Model"
    }
  ];

  // Required. The instances that are the input to the prediction call.
  repeated google.protobuf.Value instances = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. The parameters that govern the prediction call.
  google.protobuf.Value parameters = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for [PredictionService.Predict].
message PredictResponse {
  // The outputs of the prediction call.
  repeated google.protobuf.Value predictions = 1;
}
