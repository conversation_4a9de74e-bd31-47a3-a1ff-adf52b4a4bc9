// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ai.generativelanguage.v1alpha;

import "google/ai/generativelanguage/v1alpha/cached_content.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option go_package = "cloud.google.com/go/ai/generativelanguage/apiv1alpha/generativelanguagepb;generativelanguagepb";
option java_multiple_files = true;
option java_outer_classname = "CacheServiceProto";
option java_package = "com.google.ai.generativelanguage.v1alpha";

// API for managing cache of content (CachedContent resources) that can be used
// in GenerativeService requests. This way generate content requests can benefit
// from preprocessing work being done earlier, possibly lowering their
// computational cost. It is intended to be used with large contexts.
service CacheService {
  option (google.api.default_host) = "generativelanguage.googleapis.com";

  // Lists CachedContents.
  rpc ListCachedContents(ListCachedContentsRequest)
      returns (ListCachedContentsResponse) {
    option (google.api.http) = {
      get: "/v1alpha/cachedContents"
    };
    option (google.api.method_signature) = "";
  }

  // Creates CachedContent resource.
  rpc CreateCachedContent(CreateCachedContentRequest) returns (CachedContent) {
    option (google.api.http) = {
      post: "/v1alpha/cachedContents"
      body: "cached_content"
    };
    option (google.api.method_signature) = "cached_content";
  }

  // Reads CachedContent resource.
  rpc GetCachedContent(GetCachedContentRequest) returns (CachedContent) {
    option (google.api.http) = {
      get: "/v1alpha/{name=cachedContents/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates CachedContent resource (only expiration is updatable).
  rpc UpdateCachedContent(UpdateCachedContentRequest) returns (CachedContent) {
    option (google.api.http) = {
      patch: "/v1alpha/{cached_content.name=cachedContents/*}"
      body: "cached_content"
    };
    option (google.api.method_signature) = "cached_content,update_mask";
  }

  // Deletes CachedContent resource.
  rpc DeleteCachedContent(DeleteCachedContentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=cachedContents/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request to list CachedContents.
message ListCachedContentsRequest {
  // Optional. The maximum number of cached contents to return. The service may
  // return fewer than this value. If unspecified, some default (under maximum)
  // number of items will be returned. The maximum value is 1000; values above
  // 1000 will be coerced to 1000.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListCachedContents` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCachedContents` must
  // match the call that provided the page token.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Response with CachedContents list.
message ListCachedContentsResponse {
  // List of cached contents.
  repeated CachedContent cached_contents = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request to create CachedContent.
message CreateCachedContentRequest {
  // Required. The cached content to create.
  CachedContent cached_content = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request to read CachedContent.
message GetCachedContentRequest {
  // Required. The resource name referring to the content cache entry.
  // Format: `cachedContents/{id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/CachedContent"
    }
  ];
}

// Request to update CachedContent.
message UpdateCachedContentRequest {
  // Required. The content cache entry to update
  CachedContent cached_content = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 2;
}

// Request to delete CachedContent.
message DeleteCachedContentRequest {
  // Required. The resource name referring to the content cache entry
  // Format: `cachedContents/{id}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "generativelanguage.googleapis.com/CachedContent"
    }
  ];
}
