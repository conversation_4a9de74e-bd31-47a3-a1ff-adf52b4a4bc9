# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "generativelanguage_proto",
    srcs = [
        "citation.proto",
        "discuss_service.proto",
        "model.proto",
        "model_service.proto",
        "safety.proto",
        "text_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
    ],
)

proto_library_with_info(
    name = "generativelanguage_proto_with_info",
    deps = [
        ":generativelanguage_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "generativelanguage_java_proto",
    deps = [":generativelanguage_proto"],
)

java_grpc_library(
    name = "generativelanguage_java_grpc",
    srcs = [":generativelanguage_proto"],
    deps = [":generativelanguage_java_proto"],
)

java_gapic_library(
    name = "generativelanguage_java_gapic",
    srcs = [":generativelanguage_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "generativeai_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "generativelanguage_v1beta2.yaml",
    test_deps = [
        ":generativelanguage_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":generativelanguage_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "generativelanguage_java_gapic_test_suite",
    test_classes = [
        "com.google.ai.generativelanguage.v1beta2.DiscussServiceClientHttpJsonTest",
        "com.google.ai.generativelanguage.v1beta2.DiscussServiceClientTest",
        "com.google.ai.generativelanguage.v1beta2.ModelServiceClientHttpJsonTest",
        "com.google.ai.generativelanguage.v1beta2.ModelServiceClientTest",
        "com.google.ai.generativelanguage.v1beta2.TextServiceClientHttpJsonTest",
        "com.google.ai.generativelanguage.v1beta2.TextServiceClientTest",
    ],
    runtime_deps = [":generativelanguage_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-ai-generativelanguage-v1beta2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":generativelanguage_java_gapic",
        ":generativelanguage_java_grpc",
        ":generativelanguage_java_proto",
        ":generativelanguage_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "generativelanguage_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/ai/generativelanguage/apiv1beta2/generativelanguagepb",
    protos = [":generativelanguage_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "generativelanguage_go_gapic",
    srcs = [":generativelanguage_proto_with_info"],
    grpc_service_config = "generativeai_grpc_service_config.json",
    importpath = "cloud.google.com/go/ai/generativelanguage/apiv1beta2;generativelanguage",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "generativelanguage_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":generativelanguage_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-ai-generativelanguage-v1beta2-go",
    deps = [
        ":generativelanguage_go_gapic",
        ":generativelanguage_go_gapic_srcjar-metadata.srcjar",
        ":generativelanguage_go_gapic_srcjar-snippets.srcjar",
        ":generativelanguage_go_gapic_srcjar-test.srcjar",
        ":generativelanguage_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "generativelanguage_py_gapic",
    srcs = [":generativelanguage_proto"],
    grpc_service_config = "generativeai_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "generativelanguage_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "generativelanguage_py_gapic_test",
    srcs = [
        "generativelanguage_py_gapic_pytest.py",
        "generativelanguage_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":generativelanguage_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "ai-generativelanguage-v1beta2-py",
    deps = [
        ":generativelanguage_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "generativelanguage_php_proto",
    deps = [":generativelanguage_proto"],
)

php_gapic_library(
    name = "generativelanguage_php_gapic",
    srcs = [":generativelanguage_proto_with_info"],
    grpc_service_config = "generativeai_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "generativelanguage_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":generativelanguage_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-ai-generativelanguage-v1beta2-php",
    deps = [
        ":generativelanguage_php_gapic",
        ":generativelanguage_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "generativelanguage_nodejs_gapic",
    package_name = "@google-ai/generativelanguage",
    src = ":generativelanguage_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "generativeai_grpc_service_config.json",
    package = "google.ai.generativelanguage.v1beta2",
    rest_numeric_enums = True,
    service_yaml = "generativelanguage_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "ai-generativelanguage-v1beta2-nodejs",
    deps = [
        ":generativelanguage_nodejs_gapic",
        ":generativelanguage_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "generativelanguage_ruby_proto",
    deps = [":generativelanguage_proto"],
)

ruby_grpc_library(
    name = "generativelanguage_ruby_grpc",
    srcs = [":generativelanguage_proto"],
    deps = [":generativelanguage_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "generativelanguage_ruby_gapic",
    srcs = [":generativelanguage_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-ai-generativelanguage-v1beta2"],
    grpc_service_config = "generativeai_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "generativelanguage_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":generativelanguage_ruby_grpc",
        ":generativelanguage_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-ai-generativelanguage-v1beta2-ruby",
    deps = [
        ":generativelanguage_ruby_gapic",
        ":generativelanguage_ruby_grpc",
        ":generativelanguage_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "generativelanguage_csharp_proto",
    deps = [":generativelanguage_proto"],
)

csharp_grpc_library(
    name = "generativelanguage_csharp_grpc",
    srcs = [":generativelanguage_proto"],
    deps = [":generativelanguage_csharp_proto"],
)

csharp_gapic_library(
    name = "generativelanguage_csharp_gapic",
    srcs = [":generativelanguage_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "generativeai_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "generativelanguage_v1beta2.yaml",
    transport = "grpc+rest",
    deps = [
        ":generativelanguage_csharp_grpc",
        ":generativelanguage_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-ai-generativelanguage-v1beta2-csharp",
    deps = [
        ":generativelanguage_csharp_gapic",
        ":generativelanguage_csharp_grpc",
        ":generativelanguage_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "generativelanguage_cc_proto",
    deps = [":generativelanguage_proto"],
)

cc_grpc_library(
    name = "generativelanguage_cc_grpc",
    srcs = [":generativelanguage_proto"],
    grpc_only = True,
    deps = [":generativelanguage_cc_proto"],
)
