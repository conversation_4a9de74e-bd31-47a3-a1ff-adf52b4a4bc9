type: google.api.Service
config_version: 3
name: generativelanguage.googleapis.com
title: Generative Language API

apis:
- name: google.ai.generativelanguage.v1beta2.DiscussService
- name: google.ai.generativelanguage.v1beta2.ModelService
- name: google.ai.generativelanguage.v1beta2.TextService

documentation:
  summary: |-
    The PaLM API allows developers to build generative AI applications using
    the PaLM model. Large Language Models (LLMs) are a powerful, versatile
    type of machine learning model that enables computers to comprehend and
    generate natural language through a series of prompts. The PaLM API is
    based on Google's next generation LLM, PaLM. It excels at a variety of
    different tasks like code generation, reasoning, and writing. You can use
    the PaLM API to build generative AI applications for use cases like
    content generation, dialogue agents, summarization and classification
    systems, and more.

backend:
  rules:
  - selector: google.ai.generativelanguage.v1beta2.DiscussService.CountMessageTokens
    deadline: 50.0
  - selector: google.ai.generativelanguage.v1beta2.DiscussService.GenerateMessage
    deadline: 50.0
  - selector: google.ai.generativelanguage.v1beta2.TextService.EmbedText
    deadline: 50.0
  - selector: google.ai.generativelanguage.v1beta2.TextService.GenerateText
    deadline: 50.0

publishing:
  new_issue_uri: https://github.com/google/generative-ai-python/issues/new
  documentation_uri: https://developers.generativeai.google/
  api_short_name: generativelanguage
  github_label: 'api: ai'
  doc_tag_prefix: generativelanguage
  organization: CLOUD
  library_settings:
  - version: google.ai.generativelanguage.v1beta2
    launch_stage: EARLY_ACCESS
    java_settings:
      common: {}
    cpp_settings:
      common: {}
    php_settings:
      common: {}
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common: {}
    ruby_settings:
      common: {}
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
