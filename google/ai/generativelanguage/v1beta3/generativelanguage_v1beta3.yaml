type: google.api.Service
config_version: 3
name: generativelanguage.googleapis.com
title: Generative Language API

apis:
- name: google.ai.generativelanguage.v1beta3.DiscussService
- name: google.ai.generativelanguage.v1beta3.ModelService
- name: google.ai.generativelanguage.v1beta3.PermissionService
- name: google.ai.generativelanguage.v1beta3.TextService
- name: google.longrunning.Operations

types:
- name: google.ai.generativelanguage.v1beta3.CreateTunedModelMetadata

documentation:
  summary: |-
    The PaLM API allows developers to build generative AI applications using
    the PaLM model. Large Language Models (LLMs) are a powerful, versatile
    type of machine learning model that enables computers to comprehend and
    generate natural language through a series of prompts. The PaLM API is
    based on Google's next generation LLM, PaLM. It excels at a variety of
    different tasks like code generation, reasoning, and writing. You can use
    the PaLM API to build generative AI applications for use cases like
    content generation, dialogue agents, summarization and classification
    systems, and more.

backend:
  rules:
  - selector: google.ai.generativelanguage.v1beta3.DiscussService.CountMessageTokens
    deadline: 50.0
  - selector: google.ai.generativelanguage.v1beta3.DiscussService.GenerateMessage
    deadline: 50.0
  - selector: 'google.ai.generativelanguage.v1beta3.PermissionService.*'
    deadline: 50.0
  - selector: 'google.ai.generativelanguage.v1beta3.TextService.*'
    deadline: 50.0

publishing:
  new_issue_uri: https://github.com/google/generative-ai-python/issues/new
  documentation_uri: https://developers.generativeai.google/
  api_short_name: generativelanguage
  github_label: 'api: ai'
  doc_tag_prefix: generativelanguage
  organization: CLOUD
