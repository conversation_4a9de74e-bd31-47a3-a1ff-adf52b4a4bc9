{"methodConfig": [{"name": [{"service": "google.ai.generativelanguage.v1beta1.DiscussService", "method": "GenerateMessage"}, {"service": "google.ai.generativelanguage.v1beta1.DiscussService", "method": "Count<PERSON><PERSON><PERSON>T<PERSON>s"}, {"service": "google.ai.generativelanguage.v1beta1.ModelService", "method": "GetModel"}, {"service": "google.ai.generativelanguage.v1beta1.ModelService", "method": "ListModels"}, {"service": "google.ai.generativelanguage.v1beta2.DiscussService", "method": "GenerateMessage"}, {"service": "google.ai.generativelanguage.v1beta2.DiscussService", "method": "Count<PERSON><PERSON><PERSON>T<PERSON>s"}, {"service": "google.ai.generativelanguage.v1beta2.ModelService", "method": "GetModel"}, {"service": "google.ai.generativelanguage.v1beta2.ModelService", "method": "ListModels"}, {"service": "google.ai.generativelanguage.v1beta2.TextService", "method": "GenerateText"}, {"service": "google.ai.generativelanguage.v1beta2.TextService", "method": "EmbedText"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}