type: google.api.Service
config_version: 3
name: pubsub.googleapis.com
title: Cloud Pub/Sub API

apis:
- name: google.iam.v1.IAMPolicy
- name: google.pubsub.v1.Publisher
- name: google.pubsub.v1.SchemaService
- name: google.pubsub.v1.Subscriber

types:
- name: google.pubsub.v1.IngestionFailureEvent

documentation:
  summary: |-
    Provides reliable, many-to-many, asynchronous messaging between
    applications.
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    description: |-
      Gets the access control policy for a resource. Returns an empty policy
      if the resource exists and does not have a policy set.

  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    description: |-
      Sets the access control policy on the specified resource. Replaces
      any existing policy.

      Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED`
      errors.

  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    description: |-
      Returns permissions that a caller has on the specified resource. If the
      resource does not exist, this will return an empty set of
      permissions, not a `NOT_FOUND` error.

      Note: This operation is designed to be used for building
      permission-aware UIs and command-line tools, not for authorization
      checking. This operation may "fail open" without warning.

http:
  rules:
  - selector: google.iam.v1.IAMPolicy.GetIamPolicy
    get: '/v1/{resource=projects/*/topics/*}:getIamPolicy'
    additional_bindings:
    - get: '/v1/{resource=projects/*/subscriptions/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/snapshots/*}:getIamPolicy'
    - get: '/v1/{resource=projects/*/schemas/*}:getIamPolicy'
  - selector: google.iam.v1.IAMPolicy.SetIamPolicy
    post: '/v1/{resource=projects/*/topics/*}:setIamPolicy'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/subscriptions/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/snapshots/*}:setIamPolicy'
      body: '*'
    - post: '/v1/{resource=projects/*/schemas/*}:setIamPolicy'
      body: '*'
  - selector: google.iam.v1.IAMPolicy.TestIamPermissions
    post: '/v1/{resource=projects/*/subscriptions/*}:testIamPermissions'
    body: '*'
    additional_bindings:
    - post: '/v1/{resource=projects/*/topics/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/snapshots/*}:testIamPermissions'
      body: '*'
    - post: '/v1/{resource=projects/*/schemas/*}:testIamPermissions'
      body: '*'

authentication:
  rules:
  - selector: 'google.iam.v1.IAMPolicy.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/pubsub
  - selector: 'google.pubsub.v1.Publisher.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/pubsub
  - selector: 'google.pubsub.v1.SchemaService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/pubsub
  - selector: 'google.pubsub.v1.Subscriber.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/pubsub

publishing:
  documentation_uri: https://cloud.google.com/pubsub/docs
  github_label: 'api: pubsub'
  organization: CLOUD
  library_settings:
  - version: google.pubsub.v1
    dotnet_settings:
      renamed_services:
        Subscriber: SubscriberServiceApi
        Publisher: PublisherServiceApi
  proto_reference_documentation_uri: https://cloud.google.com/pubsub/docs/reference/rpc
