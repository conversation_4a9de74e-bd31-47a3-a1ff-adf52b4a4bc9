load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "pubsub_proto",
    srcs = [
        "pubsub.proto",
        "schema.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "pubsub_proto_with_info",
    deps = [
        ":pubsub_proto",
        "//google/cloud:common_resources_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "pubsub_java_proto",
    deps = [":pubsub_proto"],
)

java_grpc_library(
    name = "pubsub_java_grpc",
    srcs = [":pubsub_proto"],
    deps = [":pubsub_java_proto"],
)

java_gapic_library(
    name = "pubsub_java_gapic",
    srcs = [":pubsub_proto_with_info"],
    gapic_yaml = "pubsub_gapic.yaml",
    grpc_service_config = "pubsub_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "pubsub_v1.yaml",
    test_deps = [
        ":pubsub_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":pubsub_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "pubsub_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.pubsub.v1.SubscriptionAdminClientHttpJsonTest",
        "com.google.cloud.pubsub.v1.SubscriptionAdminClientTest",
        "com.google.cloud.pubsub.v1.TopicAdminClientHttpJsonTest",
        "com.google.cloud.pubsub.v1.TopicAdminClientTest",
    ],
    runtime_deps = [":pubsub_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-pubsub-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":pubsub_java_gapic",
        ":pubsub_java_grpc",
        ":pubsub_java_proto",
        ":pubsub_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "pubsub_py_gapic",
    srcs = [":pubsub_proto"],
    grpc_service_config = "pubsub_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-pubsub",
        "add-iam-methods",
    ],
    rest_numeric_enums = True,
    service_yaml = ":pubsub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "pubsub_py_gapic_test",
    srcs = [
        "pubsub_py_gapic_pytest.py",
        "pubsub_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":pubsub_py_gapic"],
)

py_gapic_assembly_pkg(
    name = "pubsub-v1-py",
    deps = [
        ":pubsub_py_gapic",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "pubsub_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/pubsub/apiv1/pubsubpb",
    protos = [":pubsub_proto"],
    deps = ["//google/api:annotations_go_proto"],
)

go_gapic_library(
    name = "pubsub_go_gapic",
    srcs = [":pubsub_proto_with_info"],
    grpc_service_config = "pubsub_grpc_service_config.json",
    importpath = "cloud.google.com/go/pubsub/apiv1;pubsub",
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = ":pubsub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":pubsub_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-pubsub-v1-go",
    deps = [
        ":pubsub_go_gapic",
        ":pubsub_go_gapic_srcjar-snippets.srcjar",
        ":pubsub_go_gapic_srcjar-test.srcjar",
        ":pubsub_go_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "pubsub_php_proto",
    deps = [":pubsub_proto"],
)

php_gapic_library(
    name = "pubsub_php_gapic",
    srcs = [":pubsub_proto_with_info"],
    gapic_yaml = "pubsub_gapic.yaml",
    grpc_service_config = "pubsub_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = ":pubsub_v1.yaml",
    transport = "grpc+rest",
    deps = [":pubsub_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-pubsub-v1-php",
    deps = [
        ":pubsub_php_gapic",
        ":pubsub_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "pubsub_nodejs_gapic",
    package_name = "@google-cloud/pubsub",
    src = ":pubsub_proto_with_info",
    bundle_config = "pubsub_gapic.yaml",
    extra_protoc_parameters = ["template=typescript_gapic"],
    grpc_service_config = "pubsub_grpc_service_config.json",
    handwritten_layer = True,
    main_service = "pubsub",
    rest_numeric_enums = True,
    service_yaml = ":pubsub_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "pubsub-v1-nodejs",
    deps = [
        ":pubsub_nodejs_gapic",
        ":pubsub_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "pubsub_ruby_proto",
    deps = [":pubsub_proto"],
)

ruby_grpc_library(
    name = "pubsub_ruby_grpc",
    srcs = [":pubsub_proto"],
    deps = [":pubsub_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "pubsub_ruby_gapic",
    srcs = [":pubsub_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-pubsub-v1",
        "ruby-cloud-env-prefix=PUBSUB",
        "ruby-cloud-path-override=pub_sub=pubsub",
        "ruby-cloud-namespace-override=Pubsub=PubSub",
        "ruby-cloud-product-url=https://cloud.google.com/pubsub",
        "ruby-cloud-api-id=pubsub.googleapis.com",
        "ruby-cloud-api-shortname=pubsub",
    ],
    grpc_service_config = "pubsub_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Pub/Sub is a fully-managed real-time messaging service that allows you to send and receive messages between independent applications.",
    ruby_cloud_title = "Cloud Pub/Sub V1",
    service_yaml = ":pubsub_v1.yaml",
    deps = [
        ":pubsub_ruby_grpc",
        ":pubsub_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-pubsub-v1-ruby",
    deps = [
        ":pubsub_ruby_gapic",
        ":pubsub_ruby_grpc",
        ":pubsub_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "pubsub_csharp_proto",
    deps = [":pubsub_proto"],
)

csharp_grpc_library(
    name = "pubsub_csharp_grpc",
    srcs = [":pubsub_proto"],
    deps = [":pubsub_csharp_proto"],
)

csharp_gapic_library(
    name = "pubsub_csharp_gapic",
    srcs = [":pubsub_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "pubsub_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = ":pubsub_v1.yaml",
    deps = [
        ":pubsub_csharp_grpc",
        ":pubsub_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-pubsub-v1-csharp",
    deps = [
        ":pubsub_csharp_gapic",
        ":pubsub_csharp_grpc",
        ":pubsub_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "pubsub_cc_proto",
    deps = [":pubsub_proto"],
)

cc_grpc_library(
    name = "pubsub_cc_grpc",
    srcs = [":pubsub_proto"],
    grpc_only = True,
    deps = [":pubsub_cc_proto"],
)
