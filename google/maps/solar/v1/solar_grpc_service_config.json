{"methodConfig": [{"name": [{"service": "google.maps.solar.v1.Solar", "method": "FindClosestBuildingInsights"}, {"service": "google.maps.solar.v1.Solar", "method": "GetDataLayers"}, {"service": "google.maps.solar.v1.Solar", "method": "GetGeoTiff"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}