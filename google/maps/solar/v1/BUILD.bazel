# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "solar_proto",
    srcs = [
        "solar_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:httpbody_proto",
        "//google/type:date_proto",
        "//google/type:latlng_proto",
        "//google/type:money_proto",
    ],
)

proto_library_with_info(
    name = "solar_proto_with_info",
    deps = [
        ":solar_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "solar_java_proto",
    deps = [":solar_proto"],
)

java_grpc_library(
    name = "solar_java_grpc",
    srcs = [":solar_proto"],
    deps = [":solar_java_proto"],
)

java_gapic_library(
    name = "solar_java_gapic",
    srcs = [":solar_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "solar_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "solar_v1.yaml",
    test_deps = [
        ":solar_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":solar_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "solar_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.solar.v1.SolarClientHttpJsonTest",
        "com.google.maps.solar.v1.SolarClientTest",
    ],
    runtime_deps = [":solar_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-maps-solar-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":solar_java_gapic",
        ":solar_java_grpc",
        ":solar_java_proto",
        ":solar_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "solar_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/solar/apiv1/solarpb",
    protos = [":solar_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/api:httpbody_go_proto",
        "//google/type:date_go_proto",
        "//google/type:latlng_go_proto",
        "//google/type:money_go_proto",
    ],
)

go_gapic_library(
    name = "solar_go_gapic",
    srcs = [":solar_proto_with_info"],
    grpc_service_config = "solar_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/solar/apiv1;solar",
    metadata = True,
    rest_numeric_enums = True,
    service_yaml = "solar_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":solar_go_proto",
        "//google/api:httpbody_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-maps-solar-v1-go",
    deps = [
        ":solar_go_gapic",
        ":solar_go_gapic_srcjar-metadata.srcjar",
        ":solar_go_gapic_srcjar-snippets.srcjar",
        ":solar_go_gapic_srcjar-test.srcjar",
        ":solar_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "solar_py_gapic",
    srcs = [":solar_proto"],
    grpc_service_config = "solar_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "solar_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "solar_py_gapic_test",
    srcs = [
        "solar_py_gapic_pytest.py",
        "solar_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":solar_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "maps-solar-v1-py",
    deps = [
        ":solar_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "solar_php_proto",
    deps = [":solar_proto"],
)

php_gapic_library(
    name = "solar_php_gapic",
    srcs = [":solar_proto_with_info"],
    grpc_service_config = "solar_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "solar_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":solar_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-maps-solar-v1-php",
    deps = [
        ":solar_php_gapic",
        ":solar_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "solar_nodejs_gapic",
    package_name = "@googlemaps/solar",
    src = ":solar_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "solar_grpc_service_config.json",
    package = "google.maps.solar.v1",
    rest_numeric_enums = True,
    service_yaml = "solar_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "maps-solar-v1-nodejs",
    deps = [
        ":solar_nodejs_gapic",
        ":solar_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "solar_ruby_proto",
    deps = [":solar_proto"],
)

ruby_grpc_library(
    name = "solar_ruby_grpc",
    srcs = [":solar_proto"],
    deps = [":solar_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "solar_ruby_gapic",
    srcs = [":solar_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-maps-solar-v1"],
    grpc_service_config = "solar_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "solar_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":solar_ruby_grpc",
        ":solar_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-maps-solar-v1-ruby",
    deps = [
        ":solar_ruby_gapic",
        ":solar_ruby_grpc",
        ":solar_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "solar_csharp_proto",
    deps = [":solar_proto"],
)

csharp_grpc_library(
    name = "solar_csharp_grpc",
    srcs = [":solar_proto"],
    deps = [":solar_csharp_proto"],
)

csharp_gapic_library(
    name = "solar_csharp_gapic",
    srcs = [":solar_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "solar_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "solar_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":solar_csharp_grpc",
        ":solar_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-maps-solar-v1-csharp",
    deps = [
        ":solar_csharp_gapic",
        ":solar_csharp_grpc",
        ":solar_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "solar_cc_proto",
    deps = [":solar_proto"],
)

cc_grpc_library(
    name = "solar_cc_grpc",
    srcs = [":solar_proto"],
    grpc_only = True,
    deps = [":solar_cc_proto"],
)
