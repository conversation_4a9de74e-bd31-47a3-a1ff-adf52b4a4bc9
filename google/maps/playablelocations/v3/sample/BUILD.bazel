# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "resources_proto",
    srcs = [
        "resources.proto",
    ],
    deps = [
        "//google/api:field_behavior_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:field_mask_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "resources_java_proto",
    deps = [":resources_proto"],
)

java_grpc_library(
    name = "resources_java_grpc",
    srcs = [":resources_proto"],
    deps = [":resources_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "resources_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/playablelocations/apiv3/sample/samplepb",
    protos = [":resources_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "resources_php_proto",
    deps = [":resources_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "resources_ruby_proto",
    deps = [":resources_proto"],
)

ruby_grpc_library(
    name = "resources_ruby_grpc",
    srcs = [":resources_proto"],
    deps = [":resources_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "resources_csharp_proto",
    deps = [":resources_proto"],
)

csharp_grpc_library(
    name = "resources_csharp_grpc",
    srcs = [":resources_proto"],
    deps = [":resources_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
