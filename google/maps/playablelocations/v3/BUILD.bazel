# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "playablelocations_proto",
    srcs = [
        "playablelocations.proto",
        "resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/maps/playablelocations/v3/sample:resources_proto",
        "//google/maps/unity:clientinfo_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:field_mask_proto",
    ],
)

proto_library_with_info(
    name = "playablelocations_proto_with_info",
    deps = [
        ":playablelocations_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "playablelocations_java_proto",
    deps = [
        ":playablelocations_proto",
        "//google/maps/playablelocations/v3/sample:resources_proto",
        "//google/maps/unity:clientinfo_proto",
    ],
)

java_grpc_library(
    name = "playablelocations_java_grpc",
    srcs = [":playablelocations_proto"],
    deps = [":playablelocations_java_proto"],
)

java_gapic_library(
    name = "playablelocations_java_gapic",
    srcs = [":playablelocations_proto_with_info"],
    grpc_service_config = "playablelocations_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "playablelocations_v3.yaml",
    test_deps = [
        ":playablelocations_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":playablelocations_java_proto",
    ],
)

java_gapic_test(
    name = "playablelocations_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.playablelocations.v3.PlayableLocationsClientHttpJsonTest",
        "com.google.maps.playablelocations.v3.PlayableLocationsClientTest",
    ],
    runtime_deps = [":playablelocations_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-maps-playablelocations-v3-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":playablelocations_java_gapic",
        ":playablelocations_java_grpc",
        ":playablelocations_java_proto",
        ":playablelocations_proto",
    ],
)

go_proto_library(
    name = "playablelocations_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/playablelocations/apiv3/playablelocationspb",
    protos = [":playablelocations_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/maps/playablelocations/v3/sample:resources_go_proto",
        "//google/maps/unity:clientinfo_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "playablelocations_go_gapic",
    srcs = [":playablelocations_proto_with_info"],
    grpc_service_config = "playablelocations_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/playablelocations/apiv3;playablelocations",
    rest_numeric_enums = True,
    service_yaml = "playablelocations_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":playablelocations_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-maps-playablelocations-v3-go",
    deps = [
        ":playablelocations_go_gapic",
        ":playablelocations_go_gapic_srcjar-snippets.srcjar",
        ":playablelocations_go_gapic_srcjar-test.srcjar",
        ":playablelocations_go_proto",
    ],
)

php_proto_library(
    name = "playablelocations_php_proto",
    deps = [":playablelocations_proto"],
)

php_gapic_library(
    name = "playablelocations_php_gapic",
    srcs = [":playablelocations_proto_with_info"],
    rest_numeric_enums = True,
    service_yaml = "playablelocations_v3.yaml",
    transport = "grpc+rest",
    deps = [":playablelocations_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-maps-playablelocations-v3-php",
    deps = [
        ":playablelocations_php_gapic",
        ":playablelocations_php_proto",
    ],
)

nodejs_gapic_library(
    name = "playablelocations_nodejs_gapic",
    src = ":playablelocations_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "playablelocations_grpc_service_config.json",
    package = "google.maps.playablelocations.v3",
    rest_numeric_enums = True,
    service_yaml = "playablelocations_v3.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "maps-playablelocations-v3-nodejs",
    deps = [
        ":playablelocations_nodejs_gapic",
        "//google/maps/playablelocations/v3/sample:resources_proto",
        ":playablelocations_proto",
        "//google/maps/unity:clientinfo_proto",
  ],
)

ruby_proto_library(
    name = "playablelocations_ruby_proto",
    deps = [":playablelocations_proto"],
)

ruby_grpc_library(
    name = "playablelocations_ruby_grpc",
    srcs = [":playablelocations_proto"],
    deps = [":playablelocations_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "playablelocations_ruby_gapic",
    srcs = [":playablelocations_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-playablelocations-v3"],
    rest_numeric_enums = True,
    service_yaml = "playablelocations_v3.yaml",
    deps = [
        ":playablelocations_ruby_grpc",
        ":playablelocations_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-maps-playablelocations-v3-ruby",
    deps = [
        ":playablelocations_ruby_gapic",
        ":playablelocations_ruby_grpc",
        ":playablelocations_ruby_proto",
    ],
)

csharp_proto_library(
    name = "playablelocations_csharp_proto",
    deps = [":playablelocations_proto"],
)

csharp_grpc_library(
    name = "playablelocations_csharp_grpc",
    srcs = [":playablelocations_proto"],
    deps = [":playablelocations_csharp_proto"],
)

csharp_gapic_library(
    name = "playablelocations_csharp_gapic",
    srcs = [":playablelocations_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "playablelocations_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "playablelocations_v3.yaml",
    transport = "grpc+rest",
    deps = [
        ":playablelocations_csharp_grpc",
        ":playablelocations_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-maps-playablelocations-v3-csharp",
    deps = [
        ":playablelocations_csharp_gapic",
        ":playablelocations_csharp_grpc",
        ":playablelocations_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
