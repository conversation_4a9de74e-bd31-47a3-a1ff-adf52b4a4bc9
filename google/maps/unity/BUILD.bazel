# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "clientinfo_proto",
    srcs = [
        "clientinfo.proto",
    ],
    deps = [
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "clientinfo_java_proto",
    deps = [":clientinfo_proto"],
)

java_grpc_library(
    name = "clientinfo_java_grpc",
    srcs = [":clientinfo_proto"],
    deps = [":clientinfo_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "clientinfo_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/unity/unitypb",
    protos = [":clientinfo_proto"],
    deps = [
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "clientinfo_php_proto",
    deps = [":clientinfo_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "clientinfo_ruby_proto",
    deps = [":clientinfo_proto"],
)

ruby_grpc_library(
    name = "clientinfo_ruby_grpc",
    srcs = [":clientinfo_proto"],
    deps = [":clientinfo_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "clientinfo_csharp_proto",
    deps = [":clientinfo_proto"],
)

csharp_grpc_library(
    name = "clientinfo_csharp_grpc",
    srcs = [":clientinfo_proto"],
    deps = [":clientinfo_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
