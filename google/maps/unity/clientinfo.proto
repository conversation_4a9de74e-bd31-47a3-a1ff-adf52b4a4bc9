// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.unity;

option csharp_namespace = "Google.Maps.Unity";
option go_package = "cloud.google.com/go/maps/unity/unitypb;unitypb";
option java_multiple_files = true;
option java_outer_classname = "ClientInfoProto";
option java_package = "com.google.maps.unity";
option objc_class_prefix = "GMU";

// Client information.
message ClientInfo {
  // Platform enum.
  enum Platform {
    // Unspecified or unknown OS.
    PLATFORM_UNSPECIFIED = 0;

    // Development environment.
    EDITOR = 1;

    // macOS.
    MAC_OS = 2;

    // Windows.
    WINDOWS = 3;

    // Linux
    LINUX = 4;

    // Android
    ANDROID = 5;

    // iOS
    IOS = 6;

    // WebGL.
    WEB_GL = 7;
  }

  // Application ID, such as the package name on Android and the bundle
  // identifier on iOS platforms.
  string application_id = 1;

  // Application version number, such as "1.2.3". The exact format is
  // application-dependent.
  string application_version = 2;

  // Platform where the application is running.
  Platform platform = 3;

  // Operating system name and version as reported by the OS. For example,
  // "Mac OS X 10.10.4". The exact format is platform-dependent.
  string operating_system = 4;

  // API client name and version. For example, the SDK calling the API. The
  // exact format is up to the client.
  string api_client = 5;

  // Device model as reported by the device. The exact format is
  // platform-dependent.
  string device_model = 6;

  // Language code (in BCP-47 format) indicating the UI language of the client.
  // Examples are "en", "en-US" or "ja-Latn". For more information, see
  // http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  string language_code = 7;

  // Build number/version of the operating system. e.g., the contents of
  // android.os.Build.ID in Android, or the contents of sysctl "kern.osversion"
  // in iOS.
  string operating_system_build = 8;
}
