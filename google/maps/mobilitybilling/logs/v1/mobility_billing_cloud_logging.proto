// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.mobilitybilling.logs.v1;

option csharp_namespace = "Google.Maps.MobilityBilling.Logs.V1";
option go_package = "cloud.google.com/go/maps/mobilitybilling/logs/apiv1/logspb;logspb";
option java_multiple_files = true;
option java_outer_classname = "MobilityBillingCloudLoggingProto";
option java_package = "com.google.maps.mobilitybilling.logs.v1";

// Details on ReportBillableEvent request.
message ReportBillableEventLog {
  // The id of the billable event.
  // Subject to the following restrictions:
  //
  // 1. IDs must be valid Unicode strings.
  // 2. IDs are limited to a maximum length of 64 characters.
  // 3. IDs must be normalized according to Unicode Normalization Form C
  // (http://www.unicode.org/reports/tr15/ ).
  // 4. IDs must not contain any of the following ASCII characters: '/', ':',
  // '?', ',', or '#'.
  string billable_event_id = 1;

  // Two-letter region code of the country or region where the event takes
  // place. A list of valid region codes can be found here:
  // https://developers.google.com/maps/coverage
  string region_code = 2;

  // The identifiers that are directly related to the event being reported.
  // The customer defines the meaning of these IDs.
  repeated string related_ids = 3;
}
