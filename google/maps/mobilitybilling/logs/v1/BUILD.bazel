# This file was automatically generated by BuildFileGenerator

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
    "moved_proto_library",
    "php_gapic_assembly_pkg",
    "php_proto_library",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_grpc_library",
    "py_proto_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "logs_proto",
    srcs = [
        "mobility_billing_cloud_logging.proto",
    ],
    deps = [
    ],
)

java_proto_library(
    name = "logs_java_proto",
    deps = [":logs_proto"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-maps-mobilitybilling-logs-v1-java",
    deps = [
        ":logs_java_proto",
        ":logs_proto",
    ],
)

go_proto_library(
    name = "logs_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/mobilitybilling/logs/apiv1/logspb",
    protos = [":logs_proto"],
    deps = [
    ],
)

go_gapic_assembly_pkg(
    name = "google-maps-mobilitybilling-logs-v1-go",
    deps = [
        ":logs_go_proto",
    ],
)

moved_proto_library(
    name = "logs_moved_proto",
    srcs = [":logs_proto"],
    deps = [
    ],
)

py_proto_library(
    name = "logs_py_proto",
    deps = [":logs_moved_proto"],
)

py_grpc_library(
    name = "logs_py_grpc",
    srcs = [":logs_moved_proto"],
    deps = [":logs_py_proto"],
)

py_gapic_library(
    name = "logs_py_gapic",
    srcs = [":logs_proto"],
    rest_numeric_enums = False,
    transport = "grpc+rest",
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-maps-mobilitybilling-logs-v1-py",
    deps = [
        ":logs_py_gapic",
    ],
)

php_proto_library(
    name = "logs_php_proto",
    deps = [":logs_proto"],
)

php_gapic_assembly_pkg(
    name = "google-maps-mobilitybilling-logs-v1-php",
    deps = [
        ":logs_php_proto",
    ],
)

ruby_proto_library(
    name = "logs_ruby_proto",
    deps = [":logs_proto"],
)

ruby_grpc_library(
    name = "logs_ruby_grpc",
    srcs = [":logs_proto"],
    deps = [":logs_ruby_proto"],
)

csharp_proto_library(
    name = "logs_csharp_proto",
    deps = [":logs_proto"],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-maps-mobilitybilling-logs-v1-csharp",
    package_name = "Google.Maps.MobilityBilling.Logs.V1",
    generate_nongapic_package = True,
    deps = [
        ":logs_csharp_proto",
    ],
)

cc_proto_library(
    name = "logs_cc_proto",
    deps = [":logs_proto"],
)

cc_grpc_library(
    name = "logs_cc_grpc",
    srcs = [":logs_proto"],
    grpc_only = True,
    deps = [":logs_cc_proto"],
)
