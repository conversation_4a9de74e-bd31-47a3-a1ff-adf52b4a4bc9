# This build file includes a target for the Ruby wrapper library for
# google-maps-fleet_engine.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for fleet engine.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "fleetengine_ruby_wrapper",
    srcs = ["//google/maps/fleetengine/v1:fleetengine_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-maps-fleet_engine",
        "ruby-cloud-wrapper-of=v1:0.0",
    ],
    service_yaml = "//google/maps/fleetengine/v1:fleetengine_v1.yaml",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-maps-fleet_engine-ruby",
    deps = [
        ":fleetengine_ruby_wrapper",
    ],
)
