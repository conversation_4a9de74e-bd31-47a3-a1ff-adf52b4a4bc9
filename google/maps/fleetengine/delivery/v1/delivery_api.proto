// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package maps.fleetengine.delivery.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/api/routing.proto";
import "google/geo/type/viewport.proto";
import "google/maps/fleetengine/delivery/v1/delivery_vehicles.proto";
import "google/maps/fleetengine/delivery/v1/header.proto";
import "google/maps/fleetengine/delivery/v1/task_tracking_info.proto";
import "google/maps/fleetengine/delivery/v1/tasks.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Maps.FleetEngine.Delivery.V1";
option go_package = "cloud.google.com/go/maps/fleetengine/delivery/apiv1/deliverypb;deliverypb";
option java_multiple_files = true;
option java_outer_classname = "DeliveryApi";
option java_package = "com.google.maps.fleetengine.delivery.v1";
option objc_class_prefix = "CFED";
option php_namespace = "Google\\Maps\\FleetEngine\\Delivery\\V1";
option ruby_package = "Google::Maps::FleetEngine::Delivery::V1";
option (google.api.resource_definition) = {
  type: "fleetengine.googleapis.com/Provider"
  pattern: "providers/{provider}"
};

// The Last Mile Delivery service.
service DeliveryService {
  option (google.api.default_host) = "fleetengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates and returns a new `DeliveryVehicle`.
  rpc CreateDeliveryVehicle(CreateDeliveryVehicleRequest)
      returns (DeliveryVehicle) {
    option (google.api.http) = {
      post: "/v1/{parent=providers/*}/deliveryVehicles"
      body: "delivery_vehicle"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "{provider_id=providers/*}"
      }
    };
    option (google.api.method_signature) =
        "parent,delivery_vehicle,delivery_vehicle_id";
  }

  // Returns the specified `DeliveryVehicle` instance.
  rpc GetDeliveryVehicle(GetDeliveryVehicleRequest) returns (DeliveryVehicle) {
    option (google.api.http) = {
      get: "/v1/{name=providers/*/deliveryVehicles/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "{provider_id=providers/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Writes updated `DeliveryVehicle` data to Fleet Engine, and assigns
  // `Tasks` to the `DeliveryVehicle`. You cannot update the name of the
  // `DeliveryVehicle`. You *can* update `remaining_vehicle_journey_segments`,
  // but it must contain all of the `VehicleJourneySegment`s to be persisted on
  // the `DeliveryVehicle`. The `task_id`s are retrieved from
  // `remaining_vehicle_journey_segments`, and their corresponding `Tasks` are
  // assigned to the `DeliveryVehicle` if they have not yet been assigned.
  rpc UpdateDeliveryVehicle(UpdateDeliveryVehicleRequest)
      returns (DeliveryVehicle) {
    option (google.api.http) = {
      patch: "/v1/{delivery_vehicle.name=providers/*/deliveryVehicles/*}"
      body: "delivery_vehicle"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "delivery_vehicle.name"
        path_template: "{provider_id=providers/*}"
      }
    };
    option (google.api.method_signature) = "delivery_vehicle,update_mask";
  }

  // Creates and returns a batch of new `Task` objects.
  rpc BatchCreateTasks(BatchCreateTasksRequest)
      returns (BatchCreateTasksResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=providers/*}/tasks:batchCreate"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "{provider_id=providers/*}"
      }
    };
  }

  // Creates and returns a new `Task` object.
  rpc CreateTask(CreateTaskRequest) returns (Task) {
    option (google.api.http) = {
      post: "/v1/{parent=providers/*}/tasks"
      body: "task"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "{provider_id=providers/*}"
      }
    };
    option (google.api.method_signature) = "parent,task,task_id";
  }

  // Gets information about a `Task`.
  rpc GetTask(GetTaskRequest) returns (Task) {
    option (google.api.http) = {
      get: "/v1/{name=providers/*/tasks/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "{provider_id=providers/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Updates `Task` data.
  rpc UpdateTask(UpdateTaskRequest) returns (Task) {
    option (google.api.http) = {
      patch: "/v1/{task.name=providers/*/tasks/*}"
      body: "task"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "task.name"
        path_template: "{provider_id=providers/*}"
      }
    };
    option (google.api.method_signature) = "task,update_mask";
  }

  // Gets all `Task`s that meet the specified filtering criteria.
  rpc ListTasks(ListTasksRequest) returns (ListTasksResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=providers/*}/tasks"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "{provider_id=providers/*}"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Returns the specified `TaskTrackingInfo` instance.
  rpc GetTaskTrackingInfo(GetTaskTrackingInfoRequest)
      returns (TaskTrackingInfo) {
    option (google.api.http) = {
      get: "/v1/{name=providers/*/taskTrackingInfo/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "{provider_id=providers/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Gets all `DeliveryVehicle`s that meet the specified filtering criteria.
  rpc ListDeliveryVehicles(ListDeliveryVehiclesRequest)
      returns (ListDeliveryVehiclesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=providers/*}/deliveryVehicles"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "{provider_id=providers/*}"
      }
    };
    option (google.api.method_signature) = "parent";
  }
}

// The `CreateDeliveryVehicle` request message.
message CreateDeliveryVehicleRequest {
  // Optional. The standard Delivery API request header.
  DeliveryRequestHeader header = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Must be in the format `providers/{provider}`. The provider must
  // be the Google Cloud Project ID. For example, `sample-cloud-project`.
  string parent = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The Delivery Vehicle ID must be unique and subject to the
  // following restrictions:
  //
  // * Must be a valid Unicode string.
  // * Limited to a maximum length of 64 characters.
  // * Normalized according to [Unicode Normalization Form C]
  // (http://www.unicode.org/reports/tr15/).
  // * May not contain any of the following ASCII characters: '/', ':', '?',
  // ',', or '#'.
  string delivery_vehicle_id = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The `DeliveryVehicle` entity to create. When creating a new
  // delivery vehicle, you may set the following optional fields:
  //
  // * type
  // * last_location
  // * attributes
  //
  // Note: The DeliveryVehicle's `name` field is ignored. All other
  // DeliveryVehicle fields must not be set; otherwise, an error is returned.
  DeliveryVehicle delivery_vehicle = 5 [(google.api.field_behavior) = REQUIRED];
}

// The `GetDeliveryVehicle` request message.
message GetDeliveryVehicleRequest {
  // Optional. The standard Delivery API request header.
  DeliveryRequestHeader header = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Must be in the format
  // `providers/{provider}/deliveryVehicles/{delivery_vehicle}`.
  // The `provider` must be the Google Cloud Project ID. For example,
  // `sample-cloud-project`.
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "fleetengine.googleapis.com/DeliveryVehicle"
    }
  ];
}

// The `ListDeliveryVehicles` request message.
message ListDeliveryVehiclesRequest {
  // Optional. The standard Delivery API request header.
  DeliveryRequestHeader header = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Must be in the format `providers/{provider}`.
  // The `provider` must be the Google Cloud Project ID.
  // For example, `sample-cloud-project`.
  string parent = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "fleetengine.googleapis.com/DeliveryVehicle"
    }
  ];

  // Optional. The maximum number of vehicles to return. The service may return
  // fewer than this number. If you don't specify this number, then the server
  // determines the number of results to return.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListDeliveryVehicles`
  // call. You must provide this in order to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListDeliveryVehicles`
  // must match the call that provided the page token.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter query to apply when listing delivery vehicles. See
  // http://aip.dev/160 for examples of the filter syntax. If you don't specify
  // a value, or if you specify an empty string for the filter, then all
  // delivery vehicles are returned.
  //
  // Note that the only queries supported for `ListDeliveryVehicles` are
  // on vehicle attributes (for example, `attributes.<key> = <value>` or
  // `attributes.<key1> = <value1> AND attributes.<key2> = <value2>`). Also, all
  // attributes are stored as strings, so the only supported comparisons against
  // attributes are string comparisons. In order to compare against number or
  // boolean values, the values must be explicitly quoted to be treated as
  // strings (for example, `attributes.<key> = "10"` or
  // `attributes.<key> = "true"`).
  //
  // The maximum number of restrictions allowed in a filter query is 50. A
  // restriction is a part of the query of the form
  // `attribute.<KEY> <COMPARATOR> <VALUE>`, for example `attributes.foo = bar`
  // is 1 restriction.
  string filter = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter that limits the vehicles returned to those whose last
  // known location was in the rectangular area defined by the viewport.
  google.geo.type.Viewport viewport = 7
      [(google.api.field_behavior) = OPTIONAL];
}

// The `ListDeliveryVehicles` response message.
message ListDeliveryVehiclesResponse {
  // The set of delivery vehicles that meet the requested filtering criteria.
  // When no filter is specified, the request returns all delivery vehicles. A
  // successful response can also be empty. An empty response indicates that no
  // delivery vehicles were found meeting the requested filter criteria.
  repeated DeliveryVehicle delivery_vehicles = 1;

  // You can pass this token in the `ListDeliveryVehiclesRequest` to continue to
  // list results. When all of the results are returned, this field won't be in
  // the response, or it will be an empty string.
  string next_page_token = 2;

  // The total number of delivery vehicles that match the request criteria,
  // across all pages.
  int64 total_size = 3;
}

// The `UpdateDeliveryVehicle` request message.
message UpdateDeliveryVehicleRequest {
  // Optional. The standard Delivery API request header.
  DeliveryRequestHeader header = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The `DeliveryVehicle` entity update to apply.
  // Note: You cannot update the name of the `DeliveryVehicle`.
  DeliveryVehicle delivery_vehicle = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. A field mask that indicates which `DeliveryVehicle` fields to
  // update. Note that the update_mask must contain at least one field.
  //
  // This is a comma-separated list of fully qualified names of fields. Example:
  // `"remaining_vehicle_journey_segments"`.
  google.protobuf.FieldMask update_mask = 4
      [(google.api.field_behavior) = REQUIRED];
}

// The `BatchCreateTask` request message.
message BatchCreateTasksRequest {
  // Optional. The standard Delivery API request header.
  // Note: If you set this field, then the header field in the
  // `CreateTaskRequest` messages must either be empty, or it must match this
  // field.
  DeliveryRequestHeader header = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The parent resource shared by all tasks. This value must be in
  // the format `providers/{provider}`. The `provider` must be the Google Cloud
  // Project ID. For example, `sample-cloud-project`. The parent field in the
  // `CreateTaskRequest` messages must either  be empty, or it must match this
  // field.
  string parent = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "fleetengine.googleapis.com/Task"
    }
  ];

  // Required. The request message that specifies the resources to create.
  // Note: You can create a maximum of 500 tasks in a batch.
  repeated CreateTaskRequest requests = 4
      [(google.api.field_behavior) = REQUIRED];
}

// The `BatchCreateTask` response message.
message BatchCreateTasksResponse {
  // The created Tasks.
  repeated Task tasks = 1;
}

// The `CreateTask` request message.
message CreateTaskRequest {
  // Optional. The standard Delivery API request header.
  DeliveryRequestHeader header = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Must be in the format `providers/{provider}`. The `provider` must
  // be the Google Cloud Project ID. For example, `sample-cloud-project`.
  string parent = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The Task ID must be unique, but it should be not a shipment
  // tracking ID. To store a shipment tracking ID, use the `tracking_id` field.
  // Note that multiple tasks can have the same `tracking_id`. Task IDs are
  // subject to the following restrictions:
  //
  // * Must be a valid Unicode string.
  // * Limited to a maximum length of 64 characters.
  // * Normalized according to [Unicode Normalization Form C]
  // (http://www.unicode.org/reports/tr15/).
  // * May not contain any of the following ASCII characters: '/', ':', '?',
  // ',', or '#'.
  string task_id = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. The Task entity to create.
  // When creating a Task, the following fields are required:
  //
  // * `type`
  // * `state` (must be set to `OPEN`)
  // * `tracking_id` (must not be set for `UNAVAILABLE` or `SCHEDULED_STOP`
  // tasks, but required for all other task types)
  // * `planned_location` (optional for `UNAVAILABLE` tasks)
  // * `task_duration`
  //
  // Note: The Task's `name` field is ignored. All other Task fields must not be
  // set; otherwise, an error is returned.
  Task task = 4 [(google.api.field_behavior) = REQUIRED];
}

// The `GetTask` request message.
message GetTaskRequest {
  // Optional. The standard Delivery API request header.
  DeliveryRequestHeader header = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Must be in the format `providers/{provider}/tasks/{task}`. The
  // `provider` must be the Google Cloud Project ID. For example,
  // `sample-cloud-project`.
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "fleetengine.googleapis.com/Task"
    }
  ];
}

// The `UpdateTask` request message.
message UpdateTaskRequest {
  // Optional. The standard Delivery API request header.
  DeliveryRequestHeader header = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. The Task associated with the update.
  // The following fields are maintained by Fleet Engine. Do not update
  // them using `Task.update`.
  //
  //   * `last_location`.
  //   * `last_location_snappable`.
  //   * `name`.
  //   * `remaining_vehicle_journey_segments`.
  //   * `task_outcome_location_source`.
  //
  // Note: You cannot change the value of `task_outcome` once you set it.
  //
  // If the Task has been assigned to a delivery vehicle, then don't set the
  // Task state to CLOSED using `Task.update`. Instead, remove the `VehicleStop`
  // that contains the Task from the delivery vehicle, which automatically sets
  // the Task state to CLOSED.
  Task task = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The field mask that indicates which Task fields to update.
  // Note: The `update_mask` must contain at least one field.
  //
  // This is a comma-separated list of fully qualified names of fields. Example:
  // `"task_outcome,task_outcome_time,task_outcome_location"`.
  google.protobuf.FieldMask update_mask = 4
      [(google.api.field_behavior) = REQUIRED];
}

// The `ListTasks` request message.
message ListTasksRequest {
  // Optional. The standard Delivery API request header.
  DeliveryRequestHeader header = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Must be in the format `providers/{provider}`.
  // The `provider` must be the Google Cloud Project ID. For example,
  // `sample-cloud-project`.
  string parent = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "fleetengine.googleapis.com/Task"
    }
  ];

  // Optional. The maximum number of Tasks to return. The service may return
  // fewer than this value. If you don't specify this value, then the server
  // determines the number of results to return.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token received from a previous `ListTasks` call.
  // You can provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListTasks` must match
  // the call that provided the page token.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter query to apply when listing Tasks. See
  // http://aip.dev/160 for examples of filter syntax. If you don't specify a
  // value, or if you filter on an empty string, then all Tasks are returned.
  // For information about the Task properties that you can filter on, see [List
  // tasks](https://developers.google.com/maps/documentation/mobility/fleet-engine/journeys/tasks/find-tasks#filter_listed_tasks).
  string filter = 6 [(google.api.field_behavior) = OPTIONAL];
}

// The `ListTasks` response that contains the set of Tasks that meet the filter
// criteria in the `ListTasksRequest`.
message ListTasksResponse {
  // The set of Tasks that meet the requested filtering criteria. When no filter
  // is specified, the request returns all tasks. A successful response can also
  // be empty. An empty response indicates that no Tasks were found meeting the
  // requested filter criteria.
  repeated Task tasks = 1;

  // Pass this token in the `ListTasksRequest` to continue to list results.
  // If all results have been returned, then this field is either an empty
  // string, or it doesn't appear in the response.
  string next_page_token = 2;

  // The total number of Tasks that match the request criteria, across all
  // pages.
  int64 total_size = 3;
}

// The `GetTaskTrackingInfoRequest` request message.
message GetTaskTrackingInfoRequest {
  // Optional. The standard Delivery API request header.
  DeliveryRequestHeader header = 1 [(google.api.field_behavior) = OPTIONAL];

  // Required. Must be in the format
  // `providers/{provider}/taskTrackingInfo/{tracking_id}`. The `provider`
  // must be the Google Cloud Project ID, and the `tracking_id` must be the
  // tracking ID associated with the task. An example name can be
  // `providers/sample-cloud-project/taskTrackingInfo/sample-tracking-id`.
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "fleetengine.googleapis.com/TaskTrackingInfo"
    }
  ];
}
