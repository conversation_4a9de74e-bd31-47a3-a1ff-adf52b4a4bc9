// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package maps.fleetengine.delivery.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/maps/fleetengine/delivery/v1/common.proto";
import "google/maps/fleetengine/delivery/v1/delivery_vehicles.proto";
import "google/maps/fleetengine/delivery/v1/tasks.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/type/latlng.proto";

option csharp_namespace = "Google.Maps.FleetEngine.Delivery.V1";
option go_package = "cloud.google.com/go/maps/fleetengine/delivery/apiv1/deliverypb;deliverypb";
option java_multiple_files = true;
option java_outer_classname = "TaskTrackingInfoProto";
option java_package = "com.google.maps.fleetengine.delivery.v1";
option objc_class_prefix = "CFED";
option php_namespace = "Google\\Maps\\FleetEngine\\Delivery\\V1";
option ruby_package = "Google::Maps::FleetEngine::Delivery::V1";

// The `TaskTrackingInfo` message. The message contains task tracking
// information which will be used for display. If a tracking ID is associated
// with multiple Tasks, Fleet Engine uses a heuristic to decide which Task's
// TaskTrackingInfo to select.
message TaskTrackingInfo {
  option (google.api.resource) = {
    type: "fleetengine.googleapis.com/TaskTrackingInfo"
    pattern: "providers/{provider}/taskTrackingInfo/{tracking}"
  };

  // Must be in the format `providers/{provider}/taskTrackingInfo/{tracking}`,
  // where `tracking` represents the tracking ID.
  string name = 1;

  // Immutable. The tracking ID of a Task.
  // * Must be a valid Unicode string.
  // * Limited to a maximum length of 64 characters.
  // * Normalized according to [Unicode Normalization Form C]
  // (http://www.unicode.org/reports/tr15/).
  // * May not contain any of the following ASCII characters: '/', ':', '?',
  // ',', or '#'.
  string tracking_id = 2 [(google.api.field_behavior) = IMMUTABLE];

  // The vehicle's last location.
  DeliveryVehicleLocation vehicle_location = 3;

  // A list of points which when connected forms a polyline of the vehicle's
  // expected route to the location of this task.
  repeated google.type.LatLng route_polyline_points = 4;

  // Indicates the number of stops the vehicle remaining until the task stop is
  // reached, including the task stop. For example, if the vehicle's next stop
  // is the task stop, the value will be 1.
  google.protobuf.Int32Value remaining_stop_count = 5;

  // The total remaining distance in meters to the `VehicleStop` of interest.
  google.protobuf.Int32Value remaining_driving_distance_meters = 6;

  // The timestamp that indicates the estimated arrival time to the stop
  // location.
  google.protobuf.Timestamp estimated_arrival_time = 7;

  // The timestamp that indicates the estimated completion time of a Task.
  google.protobuf.Timestamp estimated_task_completion_time = 8;

  // The current execution state of the Task.
  Task.State state = 11;

  // The outcome of attempting to execute a Task.
  Task.TaskOutcome task_outcome = 9;

  // The timestamp that indicates when the Task's outcome was set by the
  // provider.
  google.protobuf.Timestamp task_outcome_time = 12;

  // Immutable. The location where the Task will be completed.
  LocationInfo planned_location = 10 [(google.api.field_behavior) = IMMUTABLE];

  // The time window during which the task should be completed.
  TimeWindow target_time_window = 13;

  // The custom attributes set on the task.
  repeated TaskAttribute attributes = 14;
}
