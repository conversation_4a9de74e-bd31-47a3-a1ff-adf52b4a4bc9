type: google.api.Service
config_version: 3
name: fleetengine.googleapis.com
title: Last Mile Fleet Solution Delivery API

apis:
- name: maps.fleetengine.delivery.v1.DeliveryService

documentation:
  summary: |-
    Enables Fleet Engine for access to the On Demand Rides and Deliveries and
    Last Mile Fleet Solution APIs.  Customer's use of Google Maps Content in
    the Cloud Logging Services is subject to the Google Maps Platform Terms of
    Service located at https://cloud.google.com/maps-platform/terms.

authentication:
  rules:
  - selector: 'maps.fleetengine.delivery.v1.DeliveryService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1180397&template=1812135
  documentation_uri: https://developers.google.com/maps/documentation/transportation-logistics/mobility
  api_short_name: fleetengine-delivery
  github_label: 'api: fleetengine-delivery'
  doc_tag_prefix: fleetengine-delivery
  organization: GEO
  library_settings:
  - version: maps.fleetengine.delivery.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common: {}
    php_settings:
      common: {}
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
