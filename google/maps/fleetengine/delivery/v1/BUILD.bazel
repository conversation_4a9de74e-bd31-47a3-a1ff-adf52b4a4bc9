# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "delivery_proto",
    srcs = [
        "common.proto",
        "delivery_api.proto",
        "delivery_vehicles.proto",
        "header.proto",
        "task_tracking_info.proto",
        "tasks.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/api:routing_proto",
        "//google/geo/type:viewport_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "delivery_proto_with_info",
    deps = [
        ":delivery_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "delivery_java_proto",
    deps = [":delivery_proto"],
)

java_grpc_library(
    name = "delivery_java_grpc",
    srcs = [":delivery_proto"],
    deps = [":delivery_java_proto"],
)

java_gapic_library(
    name = "delivery_java_gapic",
    srcs = [":delivery_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "fleetengine_delivery_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    test_deps = [
        ":delivery_java_grpc",
        "//google/maps/fleetengine/v1:fleetengine_java_proto",
    ],
    transport = "grpc+rest",
    deps = [
        ":delivery_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "delivery_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.fleetengine.delivery.v1.DeliveryServiceClientHttpJsonTest",
        "com.google.maps.fleetengine.delivery.v1.DeliveryServiceClientTest",
    ],
    runtime_deps = [":delivery_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-maps-fleetengine-delivery-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":delivery_java_gapic",
        ":delivery_java_grpc",
        ":delivery_java_proto",
        ":delivery_proto",
    ],
)

go_proto_library(
    name = "delivery_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/fleetengine/delivery/apiv1/deliverypb",
    protos = [":delivery_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/geo/type:viewport_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "delivery_go_gapic",
    srcs = [":delivery_proto_with_info"],
    grpc_service_config = "fleetengine_delivery_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/fleetengine/delivery/apiv1;delivery",
    metadata = True,
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":delivery_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-maps-fleetengine-delivery-v1-go",
    deps = [
        ":delivery_go_gapic",
        ":delivery_go_gapic_srcjar-metadata.srcjar",
        ":delivery_go_gapic_srcjar-snippets.srcjar",
        ":delivery_go_gapic_srcjar-test.srcjar",
        ":delivery_go_proto",
    ],
)

py_import(
    name = "viewport",
    srcs = [
        "//google/geo/type:viewport_py_gapic",
    ],
)

py_gapic_library(
    name = "delivery_py_gapic",
    srcs = [":delivery_proto"],
    grpc_service_config = "fleetengine_delivery_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.maps",
        "python-gapic-name=fleetengine_delivery",
        "proto-plus-deps=google.geo.type",
    ],
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":viewport",
    ],
)

py_test(
    name = "delivery_py_gapic_test",
    srcs = [
        "delivery_py_gapic_pytest.py",
        "delivery_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":delivery_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "maps-fleetengine-delivery-v1-py",
    deps = [
        ":delivery_py_gapic",
    ],
)

php_proto_library(
    name = "delivery_php_proto",
    deps = [":delivery_proto"],
)

php_gapic_library(
    name = "delivery_php_gapic",
    srcs = [":delivery_proto_with_info"],
    grpc_service_config = "fleetengine_delivery_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc+rest",
    deps = [":delivery_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-maps-fleetengine-delivery-v1-php",
    deps = [
        ":delivery_php_gapic",
        ":delivery_php_proto",
    ],
)

nodejs_gapic_library(
    name = "delivery_nodejs_gapic",
    package_name = "@googlemaps/fleetengine-delivery",
    src = ":delivery_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "fleetengine_delivery_grpc_service_config.json",
    package = "maps.fleetengine.delivery.v1",
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "maps-fleetengine-delivery-v1-nodejs",
    deps = [
        ":delivery_nodejs_gapic",
        ":delivery_proto",
        "//google/geo/type:viewport_proto",
    ],
)

ruby_proto_library(
    name = "delivery_ruby_proto",
    deps = [":delivery_proto"],
)

ruby_grpc_library(
    name = "delivery_ruby_grpc",
    srcs = [":delivery_proto"],
    deps = [":delivery_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "delivery_ruby_gapic",
    srcs = [":delivery_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-maps-fleet_engine-delivery-v1",
        "ruby-cloud-extra-dependencies=google-geo-type=>0.0+<2.a",
    ],
    grpc_service_config = "fleetengine_delivery_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":delivery_ruby_grpc",
        ":delivery_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-maps-fleetengine-delivery-v1-ruby",
    deps = [
        ":delivery_ruby_gapic",
        ":delivery_ruby_grpc",
        ":delivery_ruby_proto",
    ],
)

csharp_proto_library(
    name = "delivery_csharp_proto",
    deps = [":delivery_proto"],
)

csharp_grpc_library(
    name = "delivery_csharp_grpc",
    srcs = [":delivery_proto"],
    deps = [":delivery_csharp_proto"],
)

csharp_gapic_library(
    name = "delivery_csharp_gapic",
    srcs = [":delivery_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "fleetengine_delivery_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":delivery_csharp_grpc",
        ":delivery_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-maps-fleetengine-delivery-v1-csharp",
    deps = [
        ":delivery_csharp_gapic",
        ":delivery_csharp_grpc",
        ":delivery_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
