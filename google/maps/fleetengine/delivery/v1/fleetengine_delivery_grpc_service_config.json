{"methodConfig": [{"name": [{"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "CreateDeliveryVehicle"}, {"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "GetDeliveryVehicle"}, {"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "BatchCreateTasks"}, {"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "CreateTask"}, {"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "GetTask"}, {"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "UpdateTask"}, {"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "UpdateDeliveryVehicle"}, {"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "ListDeliveryVehicles"}, {"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "SearchTasks"}, {"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "ListTasks"}, {"service": "maps.fleetengine.delivery.v1.DeliveryService", "method": "GetTaskTrackingInfo"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}