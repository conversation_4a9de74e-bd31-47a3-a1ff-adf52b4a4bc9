// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package maps.fleetengine.delivery.v1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/type/latlng.proto";

option csharp_namespace = "Google.Maps.FleetEngine.Delivery.V1";
option go_package = "cloud.google.com/go/maps/fleetengine/delivery/apiv1/deliverypb;deliverypb";
option java_multiple_files = true;
option java_outer_classname = "Common";
option java_package = "com.google.maps.fleetengine.delivery.v1";
option objc_class_prefix = "CFED";
option php_namespace = "Google\\Maps\\FleetEngine\\Delivery\\V1";
option ruby_package = "Google::Maps::FleetEngine::Delivery::V1";

// Describes a vehicle attribute as a key-value pair. The "key:value" string
// length cannot exceed 256 characters.
message DeliveryVehicleAttribute {
  // The attribute's key.
  string key = 1;

  // The attribute's value.
  string value = 2;

  // The attribute's value, can be in string, bool, or double type.
  oneof delivery_vehicle_attribute_value {
    // String typed attribute value.
    //
    // Note: This is identical to the `value` field which will eventually be
    // deprecated. For create or update methods, either field can be used, but
    // it's strongly recommended to use `string_value`. If both `string_value`
    // and `value` are set, they must be identical or an error will be thrown.
    // Both fields are populated in responses.
    string string_value = 3;

    // Boolean typed attribute value.
    bool bool_value = 4;

    // Double typed attribute value.
    double number_value = 5;
  }
}

// The location, speed, and heading of a vehicle at a point in time.
message DeliveryVehicleLocation {
  // The location of the vehicle.
  // When it is sent to Fleet Engine, the vehicle's location is a GPS location.
  // When you receive it in a response, the vehicle's location can be either a
  // GPS location, a supplemental location, or some other estimated location.
  // The source is specified in `location_sensor`.
  google.type.LatLng location = 1;

  // Deprecated: Use `latlng_accuracy` instead.
  google.protobuf.DoubleValue horizontal_accuracy = 8 [deprecated = true];

  // Accuracy of `location` in meters as a radius.
  google.protobuf.DoubleValue latlng_accuracy = 22;

  // Direction the vehicle is moving in degrees.  0 represents North.
  // The valid range is [0,360).
  google.protobuf.Int32Value heading = 2;

  // Deprecated: Use `heading_accuracy` instead.
  google.protobuf.DoubleValue bearing_accuracy = 10 [deprecated = true];

  // Accuracy of `heading` in degrees.
  google.protobuf.DoubleValue heading_accuracy = 23;

  // Altitude in meters above WGS84.
  google.protobuf.DoubleValue altitude = 5;

  // Deprecated: Use `altitude_accuracy` instead.
  google.protobuf.DoubleValue vertical_accuracy = 9 [deprecated = true];

  // Accuracy of `altitude` in meters.
  google.protobuf.DoubleValue altitude_accuracy = 24;

  // Speed of the vehicle in kilometers per hour.
  // Deprecated: Use `speed` instead.
  google.protobuf.Int32Value speed_kmph = 3 [deprecated = true];

  // Speed of the vehicle in meters/second
  google.protobuf.DoubleValue speed = 6;

  // Accuracy of `speed` in meters/second.
  google.protobuf.DoubleValue speed_accuracy = 7;

  // The time when `location` was reported by the sensor according to the
  // sensor's clock.
  google.protobuf.Timestamp update_time = 4;

  // Output only. The time when the server received the location information.
  google.protobuf.Timestamp server_time = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Provider of location data (for example, `GPS`).
  DeliveryVehicleLocationSensor location_sensor = 11;

  // Whether `location` is snapped to a road.
  google.protobuf.BoolValue is_road_snapped = 27;

  // Input only. Indicates whether the GPS sensor is enabled on the mobile
  // device.
  google.protobuf.BoolValue is_gps_sensor_enabled = 12
      [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. Time (in seconds) since this location was first sent to the
  // server. This will be zero for the first update. If the time is unknown (for
  // example, when the app restarts), this value resets to zero.
  google.protobuf.Int32Value time_since_update = 14
      [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. Deprecated: Other signals are now used to determine if a
  // location is stale.
  google.protobuf.Int32Value num_stale_updates = 15
      [deprecated = true, (google.api.field_behavior) = INPUT_ONLY];

  // Raw vehicle location (unprocessed by road-snapper).
  google.type.LatLng raw_location = 16;

  // Timestamp associated with the raw location.
  google.protobuf.Timestamp raw_location_time = 17;

  // Source of the raw location. Defaults to `GPS`.
  DeliveryVehicleLocationSensor raw_location_sensor = 28;

  // Accuracy of `raw_location` as a radius, in meters.
  google.protobuf.DoubleValue raw_location_accuracy = 25;

  // Supplemental location provided by the integrating app.
  google.type.LatLng supplemental_location = 18;

  // Timestamp associated with the supplemental location.
  google.protobuf.Timestamp supplemental_location_time = 19;

  // Source of the supplemental location. Defaults to
  // `CUSTOMER_SUPPLIED_LOCATION`.
  DeliveryVehicleLocationSensor supplemental_location_sensor = 20;

  // Accuracy of `supplemental_location` as a radius, in meters.
  google.protobuf.DoubleValue supplemental_location_accuracy = 21;

  // Deprecated: Use `is_road_snapped` instead.
  bool road_snapped = 26 [deprecated = true];
}

// The sensor or methodology used to determine the location.
enum DeliveryVehicleLocationSensor {
  // The sensor is unspecified or unknown.
  UNKNOWN_SENSOR = 0;

  // GPS or Assisted GPS.
  GPS = 1;

  // Assisted GPS, cell tower ID, or WiFi access point.
  NETWORK = 2;

  // Cell tower ID or WiFi access point.
  PASSIVE = 3;

  // A location determined by the mobile device to be the most likely
  // road position.
  ROAD_SNAPPED_LOCATION_PROVIDER = 4;

  // A customer-supplied location from an independent source.  Typically, this
  // value is used for a location provided from sources other than the mobile
  // device running Driver SDK.  If the original source is described by one of
  // the other enum values, use that value. Locations marked
  // CUSTOMER_SUPPLIED_LOCATION are typically provided via a DeliveryVehicle's
  // `last_location.supplemental_location_sensor`.
  CUSTOMER_SUPPLIED_LOCATION = 5;

  // A location calculated by Fleet Engine based on the signals available to it.
  // Output only. This value will be rejected if it is received in a request.
  FLEET_ENGINE_LOCATION = 6;

  // Android's Fused Location Provider.
  FUSED_LOCATION_PROVIDER = 100;

  // The location provider on Apple operating systems.
  CORE_LOCATION = 200;
}

// The vehicle's navigation status.
enum DeliveryVehicleNavigationStatus {
  // Unspecified navigation status.
  UNKNOWN_NAVIGATION_STATUS = 0;

  // The Driver app's navigation is in `FREE_NAV` mode.
  NO_GUIDANCE = 1;

  // Turn-by-turn navigation is available and the Driver app navigation has
  // entered `GUIDED_NAV` mode.
  ENROUTE_TO_DESTINATION = 2;

  // The vehicle has gone off the suggested route.
  OFF_ROUTE = 3;

  // The vehicle is within approximately 50m of the destination.
  ARRIVED_AT_DESTINATION = 4;
}

// A time range.
message TimeWindow {
  // Required. The start time of the time window (inclusive).
  google.protobuf.Timestamp start_time = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The end time of the time window (inclusive).
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Describes a task attribute as a key-value pair. The "key:value" string length
// cannot exceed 256 characters.
message TaskAttribute {
  // The attribute's key. Keys may not contain the colon character (:).
  string key = 1;

  // The attribute's value, can be in string, bool, or double type. If none are
  // set the TaskAttribute string_value will be stored as the empty string "".
  oneof task_attribute_value {
    // String typed attribute value.
    string string_value = 2;

    // Boolean typed attribute value.
    bool bool_value = 3;

    // Double typed attribute value.
    double number_value = 4;
  }
}
