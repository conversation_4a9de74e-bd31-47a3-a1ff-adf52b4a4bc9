# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "fleetengine_proto",
    srcs = [
        "fleetengine.proto",
        "header.proto",
        "traffic.proto",
        "trip_api.proto",
        "trips.proto",
        "vehicle_api.proto",
        "vehicles.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/api:routing_proto",
        "//google/geo/type:viewport_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "fleetengine_proto_with_info",
    deps = [
        ":fleetengine_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "fleetengine_java_proto",
    deps = [":fleetengine_proto"],
)

java_grpc_library(
    name = "fleetengine_java_grpc",
    srcs = [":fleetengine_proto"],
    deps = [":fleetengine_java_proto"],
)

java_gapic_library(
    name = "fleetengine_java_gapic",
    srcs = [":fleetengine_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "fleetengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    test_deps = [
        ":fleetengine_java_grpc",
        "//google/geo/type:viewport_java_proto",
    ],
    transport = "grpc",
    deps = [
        ":fleetengine_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "fleetengine_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.fleetengine.v1.TripServiceClientTest",
        "com.google.maps.fleetengine.v1.VehicleServiceClientTest",
    ],
    runtime_deps = [":fleetengine_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-maps-fleetengine-v1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":fleetengine_java_gapic",
        ":fleetengine_java_grpc",
        ":fleetengine_java_proto",
        ":fleetengine_proto",
    ],
)

go_proto_library(
    name = "fleetengine_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/fleetengine/apiv1/fleetenginepb",
    protos = [":fleetengine_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/geo/type:viewport_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "fleetengine_go_gapic",
    srcs = [":fleetengine_proto_with_info"],
    grpc_service_config = "fleetengine_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/fleetengine/apiv1;fleetengine",
    metadata = True,
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc",
    deps = [
        ":fleetengine_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-maps-fleetengine-v1-go",
    deps = [
        ":fleetengine_go_gapic",
        ":fleetengine_go_gapic_srcjar-metadata.srcjar",
        ":fleetengine_go_gapic_srcjar-snippets.srcjar",
        ":fleetengine_go_gapic_srcjar-test.srcjar",
        ":fleetengine_go_proto",
    ],
)

py_import(
    name = "viewport",
    srcs = [
        "//google/geo/type:viewport_py_gapic",
    ],
)

py_gapic_library(
    name = "fleetengine_py_gapic",
    srcs = [":fleetengine_proto"],
    grpc_service_config = "fleetengine_grpc_service_config.json",
    opt_args = [
        "python-gapic-namespace=google.maps",
        "proto-plus-deps=google.geo.type",
    ],
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc",
    deps = [
        ":viewport",
    ],
)

py_test(
    name = "fleetengine_py_gapic_test",
    srcs = [
        "fleetengine_py_gapic_pytest.py",
        "fleetengine_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":fleetengine_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "maps-fleetengine-v1-py",
    deps = [
        ":fleetengine_py_gapic",
    ],
)

php_proto_library(
    name = "fleetengine_php_proto",
    deps = [":fleetengine_proto"],
)

php_gapic_library(
    name = "fleetengine_php_gapic",
    srcs = [":fleetengine_proto_with_info"],
    grpc_service_config = "fleetengine_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc+rest",
    deps = [":fleetengine_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-maps-fleetengine-v1-php",
    deps = [
        ":fleetengine_php_gapic",
        ":fleetengine_php_proto",
    ],
)

nodejs_gapic_library(
    name = "fleetengine_nodejs_gapic",
    package_name = "@googlemaps/fleetengine",
    src = ":fleetengine_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "fleetengine_grpc_service_config.json",
    package = "maps.fleetengine.v1",
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "maps-fleetengine-v1-nodejs",
    deps = [
        ":fleetengine_nodejs_gapic",
        ":fleetengine_proto",
        "//google/geo/type:viewport_proto",
    ],
)

ruby_proto_library(
    name = "fleetengine_ruby_proto",
    deps = [":fleetengine_proto"],
)

ruby_grpc_library(
    name = "fleetengine_ruby_grpc",
    srcs = [":fleetengine_proto"],
    deps = [":fleetengine_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "fleetengine_ruby_gapic",
    srcs = [":fleetengine_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-maps-fleet_engine-v1",
        "ruby-cloud-extra-dependencies=google-geo-type=>0.0+<2.a",
    ],
    grpc_service_config = "fleetengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":fleetengine_ruby_grpc",
        ":fleetengine_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-maps-fleetengine-v1-ruby",
    deps = [
        ":fleetengine_ruby_gapic",
        ":fleetengine_ruby_grpc",
        ":fleetengine_ruby_proto",
    ],
)

csharp_proto_library(
    name = "fleetengine_csharp_proto",
    deps = [":fleetengine_proto"],
)

csharp_grpc_library(
    name = "fleetengine_csharp_grpc",
    srcs = [":fleetengine_proto"],
    deps = [":fleetengine_csharp_proto"],
)

csharp_gapic_library(
    name = "fleetengine_csharp_gapic",
    srcs = [":fleetengine_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "fleetengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "fleetengine_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":fleetengine_csharp_grpc",
        ":fleetengine_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-maps-fleetengine-v1-csharp",
    deps = [
        ":fleetengine_csharp_gapic",
        ":fleetengine_csharp_grpc",
        ":fleetengine_csharp_proto",
    ],
)
##############################################################################
# C++
##############################################################################
# Put your C++ rules here
