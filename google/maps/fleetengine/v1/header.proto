// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package maps.fleetengine.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Maps.FleetEngine.V1";
option go_package = "cloud.google.com/go/maps/fleetengine/apiv1/fleetenginepb;fleetenginepb";
option java_multiple_files = true;
option java_outer_classname = "Headers";
option java_package = "com.google.maps.fleetengine.v1";
option objc_class_prefix = "CFE";
option php_namespace = "Google\\Maps\\FleetEngine\\V1";
option ruby_package = "Google::Maps::FleetEngine::V1";

// A RequestHeader contains fields common to all Fleet Engine RPC requests.
message RequestHeader {
  // Possible types of SDK.
  enum SdkType {
    // The default value. This value is used if the `sdk_type` is omitted.
    SDK_TYPE_UNSPECIFIED = 0;

    // The calling SDK is Consumer.
    CONSUMER = 1;

    // The calling SDK is Driver.
    DRIVER = 2;

    // The calling SDK is JavaScript.
    JAVASCRIPT = 3;
  }

  // The platform of the calling SDK.
  enum Platform {
    // The default value. This value is used if the platform is omitted.
    PLATFORM_UNSPECIFIED = 0;

    // The request is coming from Android.
    ANDROID = 1;

    // The request is coming from iOS.
    IOS = 2;

    // The request is coming from the web.
    WEB = 3;
  }

  // The BCP-47 language code, such as en-US or sr-Latn. For more information,
  // see http://www.unicode.org/reports/tr35/#Unicode_locale_identifier. If none
  // is specified, the response may be in any language, with a preference for
  // English if such a name exists. Field value example: `en-US`.
  string language_code = 1;

  // Required. CLDR region code of the region where the request originates.
  // Field value example: `US`.
  string region_code = 2 [(google.api.field_behavior) = REQUIRED];

  // Version of the calling SDK, if applicable.
  // The version format is "major.minor.patch", example: `1.1.2`.
  string sdk_version = 3;

  // Version of the operating system on which the calling SDK is running.
  // Field value examples: `4.4.1`, `12.1`.
  string os_version = 4;

  // Model of the device on which the calling SDK is running.
  // Field value examples: `iPhone12,1`, `SM-G920F`.
  string device_model = 5;

  // The type of SDK sending the request.
  SdkType sdk_type = 6;

  // Version of the MapSDK which the calling SDK depends on, if applicable.
  // The version format is "major.minor.patch", example: `5.2.1`.
  string maps_sdk_version = 7;

  // Version of the NavSDK which the calling SDK depends on, if applicable.
  // The version format is "major.minor.patch", example: `2.1.0`.
  string nav_sdk_version = 8;

  // Platform of the calling SDK.
  Platform platform = 9;

  // Manufacturer of the Android device from the calling SDK, only applicable
  // for the Android SDKs.
  // Field value example: `Samsung`.
  string manufacturer = 10;

  // Android API level of the calling SDK, only applicable for the Android SDKs.
  // Field value example: `23`.
  int32 android_api_level = 11;

  // Optional ID that can be provided for logging purposes in order to identify
  // the request.
  string trace_id = 12;
}
