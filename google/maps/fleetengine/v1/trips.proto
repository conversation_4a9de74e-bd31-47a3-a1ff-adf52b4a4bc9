// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package maps.fleetengine.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/maps/fleetengine/v1/fleetengine.proto";
import "google/maps/fleetengine/v1/traffic.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/type/latlng.proto";

option csharp_namespace = "Google.Maps.FleetEngine.V1";
option go_package = "cloud.google.com/go/maps/fleetengine/apiv1/fleetenginepb;fleetenginepb";
option java_multiple_files = true;
option java_outer_classname = "Trips";
option java_package = "com.google.maps.fleetengine.v1";
option objc_class_prefix = "CFE";
option php_namespace = "Google\\Maps\\FleetEngine\\V1";
option ruby_package = "Google::Maps::FleetEngine::V1";

// Trip metadata.
message Trip {
  option (google.api.resource) = {
    type: "fleetengine.googleapis.com/Trip"
    pattern: "providers/{provider}/trips/{trip}"
  };

  // Output only. In the format "providers/{provider}/trips/{trip}"
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // ID of the vehicle making this trip.
  string vehicle_id = 2;

  // Current status of the trip.
  TripStatus trip_status = 3;

  // The type of the trip.
  TripType trip_type = 4;

  // Location where customer indicates they will be picked up.
  TerminalLocation pickup_point = 5;

  // Input only. The actual location when and where customer was picked up.
  // This field is for provider to provide feedback on actual pickup
  // information.
  StopLocation actual_pickup_point = 22
      [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The actual time and location of the driver arrival at
  // the pickup point.
  // This field is for provider to provide feedback on actual arrival
  // information at the pickup point.
  StopLocation actual_pickup_arrival_point = 32
      [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Either the estimated future time when the rider(s) will be
  // picked up, or the actual time when they were picked up.
  google.protobuf.Timestamp pickup_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Intermediate stops in order that the trip requests (in addition
  // to pickup and dropoff). Initially this will not be supported for shared
  // trips.
  repeated TerminalLocation intermediate_destinations = 14;

  // Indicates the last time the `intermediate_destinations` was modified.
  // Your server should cache this value and pass it in `UpdateTripRequest`
  // when update `intermediate_destination_index` to ensure the
  // `intermediate_destinations` is not changed.
  google.protobuf.Timestamp intermediate_destinations_version = 25;

  // When `TripStatus` is `ENROUTE_TO_INTERMEDIATE_DESTINATION`, a number
  // between [0..N-1] indicating which intermediate destination the vehicle will
  // cross next. When `TripStatus` is `ARRIVED_AT_INTERMEDIATE_DESTINATION`, a
  // number between [0..N-1] indicating which intermediate destination the
  // vehicle is at. The provider sets this value. If there are no
  // `intermediate_destinations`, this field is ignored.
  int32 intermediate_destination_index = 15;

  // Input only. The actual time and location of the driver's arrival at
  // an intermediate destination.
  // This field is for provider to provide feedback on actual arrival
  // information at intermediate destinations.
  repeated StopLocation actual_intermediate_destination_arrival_points = 33
      [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The actual time and location when and where the customer was
  // picked up from an intermediate destination. This field is for provider to
  // provide feedback on actual pickup information at intermediate destinations.
  repeated StopLocation actual_intermediate_destinations = 34
      [(google.api.field_behavior) = INPUT_ONLY];

  // Location where customer indicates they will be dropped off.
  TerminalLocation dropoff_point = 7;

  // Input only. The actual time and location when and where customer was
  // dropped off. This field is for provider to provide feedback on actual
  // dropoff information.
  StopLocation actual_dropoff_point = 23
      [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Either the estimated future time when the rider(s) will be
  // dropped off at the final destination, or the actual time when they were
  // dropped off.
  google.protobuf.Timestamp dropoff_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The full path from the current location to the dropoff point,
  // inclusive. This path could include waypoints from other trips.
  repeated TripWaypoint remaining_waypoints = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // This field supports manual ordering of the waypoints for the trip. It
  // contains all of the remaining waypoints for the assigned vehicle, as well
  // as the pickup and drop-off waypoints for this trip. If the trip hasn't been
  // assigned to a vehicle, then Fleet Engine ignores this field. For privacy
  // reasons, this field is only populated by the server on `UpdateTrip` and
  // `CreateTrip` calls, NOT on `GetTrip` calls.
  repeated TripWaypoint vehicle_waypoints = 20;

  // Output only. Anticipated route for this trip to the first entry in
  // remaining_waypoints. Note that the first waypoint may belong to a different
  // trip.
  repeated google.type.LatLng route = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An encoded path to the next waypoint.
  //
  // Note: This field is intended only for use by the Driver SDK and Consumer
  // SDK. Decoding is not yet supported.
  string current_route_segment = 21 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates the last time the route was modified.
  //
  // Note: This field is intended only for use by the Driver SDK and Consumer
  // SDK.
  google.protobuf.Timestamp current_route_segment_version = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates the traffic conditions along the
  // `current_route_segment` when they're available.
  //
  // Note: This field is intended only for use by the Driver SDK and Consumer
  // SDK.
  ConsumableTrafficPolyline current_route_segment_traffic = 28
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates the last time the `current_route_segment_traffic`
  // was modified.
  //
  // Note: This field is intended only for use by the Driver SDK and Consumer
  // SDK.
  google.protobuf.Timestamp current_route_segment_traffic_version = 30
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The waypoint where `current_route_segment` ends.
  TripWaypoint current_route_segment_end_point = 24
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The remaining driving distance in the `current_route_segment`
  // field. The value is unspecified if the trip is not assigned to a vehicle,
  // or the trip is completed or cancelled.
  google.protobuf.Int32Value remaining_distance_meters = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The ETA to the next waypoint (the first entry in the
  // `remaining_waypoints` field). The value is unspecified if the trip is not
  // assigned to a vehicle, or the trip is inactive (completed or cancelled).
  google.protobuf.Timestamp eta_to_first_waypoint = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The duration from when the Trip data is returned to the time
  // in `Trip.eta_to_first_waypoint`. The value is unspecified if the trip is
  // not assigned to a vehicle, or the trip is inactive (completed or
  // cancelled).
  google.protobuf.Duration remaining_time_to_first_waypoint = 27
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates the last time that `remaining_waypoints` was changed
  // (a waypoint was added, removed, or changed).
  google.protobuf.Timestamp remaining_waypoints_version = 19
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates the last time the
  // `remaining_waypoints.path_to_waypoint` and
  // `remaining_waypoints.traffic_to_waypoint` were modified. Your client app
  // should cache this value and pass it in `GetTripRequest` to ensure the
  // paths and traffic for `remaining_waypoints` are only returned if updated.
  google.protobuf.Timestamp remaining_waypoints_route_version = 29
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. Indicates the number of passengers on this trip and does not
  // include the driver. A vehicle must have available capacity to be returned
  // in a `SearchVehicles` response.
  int32 number_of_passengers = 10 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. Indicates the last reported location of the vehicle along the
  // route.
  VehicleLocation last_location = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Indicates whether the vehicle's `last_location` can be snapped
  // to the current_route_segment. False if `last_location` or
  // `current_route_segment` doesn't exist.
  // It is computed by Fleet Engine. Any update from clients will be ignored.
  bool last_location_snappable = 26 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The subset of Trip fields that are populated and how they should be
  // interpreted.
  TripView view = 31;
}

// The actual location where a stop (pickup/dropoff) happened.
message StopLocation {
  // Required. Denotes the actual location.
  google.type.LatLng point = 1 [(google.api.field_behavior) = REQUIRED];

  // Indicates when the stop happened.
  google.protobuf.Timestamp timestamp = 2;

  // Input only. Deprecated.  Use the timestamp field.
  google.protobuf.Timestamp stop_time = 3
      [deprecated = true, (google.api.field_behavior) = INPUT_ONLY];
}

// The status of a trip indicating its progression.
enum TripStatus {
  // Default, used for unspecified or unrecognized trip status.
  UNKNOWN_TRIP_STATUS = 0;

  // Newly created trip.
  NEW = 1;

  // The driver is on their way to the pickup point.
  ENROUTE_TO_PICKUP = 2;

  // The driver has arrived at the pickup point.
  ARRIVED_AT_PICKUP = 3;

  // The driver has arrived at an intermediate destination and is waiting for
  // the rider.
  ARRIVED_AT_INTERMEDIATE_DESTINATION = 7;

  // The driver is on their way to an intermediate destination
  // (not the dropoff point).
  ENROUTE_TO_INTERMEDIATE_DESTINATION = 8;

  // The driver has picked up the rider and is on their way to the
  // next destination.
  ENROUTE_TO_DROPOFF = 4;

  // The rider has been dropped off and the trip is complete.
  COMPLETE = 5;

  // The trip was canceled prior to pickup by the driver, rider, or
  // rideshare provider.
  CANCELED = 6;
}

// A set of values that indicate upon which platform the request was issued.
enum BillingPlatformIdentifier {
  // Default. Used for unspecified platforms.
  BILLING_PLATFORM_IDENTIFIER_UNSPECIFIED = 0;

  // The platform is a client server.
  SERVER = 1;

  // The platform is a web browser.
  WEB = 2;

  // The platform is an Android mobile device.
  ANDROID = 3;

  // The platform is an IOS mobile device.
  IOS = 4;

  // Other platforms that are not listed in this enumeration.
  OTHERS = 5;
}

// Selector for different sets of Trip fields in a `GetTrip` response.  See
// [AIP-157](https://google.aip.dev/157) for context. Additional views are
// likely to be added.
enum TripView {
  // The default value. For backwards-compatibility, the API will default to an
  // SDK view. To ensure stability and support, customers are
  // advised to select a `TripView` other than `SDK`.
  TRIP_VIEW_UNSPECIFIED = 0;

  // Includes fields that may not be interpretable or supportable using
  // publicly available libraries.
  SDK = 1;

  // Trip fields are populated for the Journey Sharing use case. This view is
  // intended for server-to-server communications.
  JOURNEY_SHARING_V1S = 2;
}
