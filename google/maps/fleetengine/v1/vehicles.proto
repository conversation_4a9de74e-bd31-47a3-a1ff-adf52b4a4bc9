// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package maps.fleetengine.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/maps/fleetengine/v1/fleetengine.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Maps.FleetEngine.V1";
option go_package = "cloud.google.com/go/maps/fleetengine/apiv1/fleetenginepb;fleetenginepb";
option java_multiple_files = true;
option java_outer_classname = "Vehicles";
option java_package = "com.google.maps.fleetengine.v1";
option objc_class_prefix = "CFE";
option php_namespace = "Google\\Maps\\FleetEngine\\V1";
option ruby_package = "Google::Maps::FleetEngine::V1";

// Vehicle metadata.
message Vehicle {
  option (google.api.resource) = {
    type: "fleetengine.googleapis.com/Vehicle"
    pattern: "providers/{provider}/vehicles/{vehicle}"
  };

  // The type of vehicle.
  message VehicleType {
    // Vehicle type categories
    enum Category {
      // Default, used for unspecified or unrecognized vehicle categories.
      UNKNOWN = 0;

      // An automobile.
      AUTO = 1;

      // Any vehicle that acts as a taxi (typically licensed or regulated).
      TAXI = 2;

      // Generally, a vehicle with a large storage capacity.
      TRUCK = 3;

      // A motorcycle, moped, or other two-wheeled vehicle
      TWO_WHEELER = 4;

      // Human-powered transport.
      BICYCLE = 5;

      // A human transporter, typically walking or running, traveling along
      // pedestrian pathways.
      PEDESTRIAN = 6;
    }

    // Vehicle type category
    Category category = 1;
  }

  // Output only. The unique name for this vehicle.
  // The format is `providers/{provider}/vehicles/{vehicle}`.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The vehicle state.
  VehicleState vehicle_state = 2;

  // Trip types supported by this vehicle.
  repeated TripType supported_trip_types = 3;

  // Output only. List of `trip_id`'s for trips currently assigned to this
  // vehicle.
  repeated string current_trips = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Last reported location of the vehicle.
  VehicleLocation last_location = 5;

  // The total numbers of riders this vehicle can carry.  The driver is not
  // considered in this value. This value must be greater than or equal to one.
  int32 maximum_capacity = 6;

  // List of vehicle attributes. A vehicle can have at most 100
  // attributes, and each attribute must have a unique key.
  repeated VehicleAttribute attributes = 8;

  // Required. The type of this vehicle.  Can be used to filter vehicles in
  // `SearchVehicles` results.  Also influences ETA and route calculations.
  VehicleType vehicle_type = 9 [(google.api.field_behavior) = REQUIRED];

  // License plate information for the vehicle.
  LicensePlate license_plate = 10;

  // Deprecated: Use `Vehicle.waypoints` instead.
  repeated TerminalLocation route = 12 [deprecated = true];

  // The polyline specifying the route the driver app intends to take to
  // the next waypoint. This list is also returned in
  // `Trip.current_route_segment` for all active trips assigned to the vehicle.
  //
  // Note: This field is intended only for use by the Driver SDK. Decoding is
  // not yet supported.
  string current_route_segment = 20;

  // Input only. Fleet Engine uses this information to improve journey sharing.
  // Note: This field is intended only for use by the Driver SDK.
  TrafficPolylineData current_route_segment_traffic = 28
      [(google.api.field_behavior) = INPUT_ONLY];

  // Output only. Time when `current_route_segment` was set. It can be stored by
  // the client and passed in future `GetVehicle` requests to prevent returning
  // routes that haven't changed.
  google.protobuf.Timestamp current_route_segment_version = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The waypoint where `current_route_segment` ends. This can be supplied by
  // drivers on `UpdateVehicle` calls either as a full trip waypoint, a waypoint
  // `LatLng`, or as the last `LatLng` of the `current_route_segment`. Fleet
  // Engine will then do its best to interpolate to an actual waypoint if it is
  // not fully specified. This field is ignored in `UpdateVehicle` calls unless
  // `current_route_segment` is also specified.
  TripWaypoint current_route_segment_end_point = 24;

  // The remaining driving distance for the `current_route_segment`.
  // This value is also returned in `Trip.remaining_distance_meters` for all
  // active trips assigned to the vehicle. The value is unspecified if the
  // `current_route_segment` field is empty.
  google.protobuf.Int32Value remaining_distance_meters = 18;

  // The ETA to the first entry in the `waypoints` field.  The value is
  // unspecified if the `waypoints` field is empty or the
  // `Vehicle.current_route_segment` field is empty.
  //
  // When updating a vehicle, `remaining_time_seconds` takes precedence over
  // `eta_to_first_waypoint` in the same request.
  google.protobuf.Timestamp eta_to_first_waypoint = 19;

  // Input only. The remaining driving time for the `current_route_segment`. The
  // value is unspecified if the `waypoints` field is empty or the
  // `Vehicle.current_route_segment` field is empty. This value should match
  // `eta_to_first_waypoint` - `current_time` if all parties are using the same
  // clock.
  //
  // When updating a vehicle, `remaining_time_seconds` takes precedence over
  // `eta_to_first_waypoint` in the same request.
  google.protobuf.Int32Value remaining_time_seconds = 25
      [(google.api.field_behavior) = INPUT_ONLY];

  // The remaining waypoints assigned to this Vehicle.
  repeated TripWaypoint waypoints = 22;

  // Output only. Last time the `waypoints` field was updated. Clients should
  // cache this value and pass it in `GetVehicleRequest` to ensure the
  // `waypoints` field is only returned if it is updated.
  google.protobuf.Timestamp waypoints_version = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Indicates if the driver accepts back-to-back trips. If `true`,
  // `SearchVehicles` may include the vehicle even if it is currently assigned
  // to a trip. The default value is `false`.
  bool back_to_back_enabled = 23;

  // The vehicle's navigation status.
  NavigationStatus navigation_status = 26;

  // Input only. Information about settings in the mobile device being used by
  // the driver.
  DeviceSettings device_settings = 27
      [(google.api.field_behavior) = INPUT_ONLY];
}

// Information about the device's battery.
message BatteryInfo {
  // Status of the battery, whether full or charging etc.
  BatteryStatus battery_status = 1;

  // Status of battery power source.
  PowerSource power_source = 2;

  // Current battery percentage [0-100].
  float battery_percentage = 3;
}

// Information about various settings on the mobile device.
message DeviceSettings {
  // How location features are set to behave on the device when battery saver is
  // on.
  LocationPowerSaveMode location_power_save_mode = 1;

  // Whether the device is currently in power save mode.
  bool is_power_save_mode = 2;

  // Whether the device is in an interactive state.
  bool is_interactive = 3;

  // Information about the battery state.
  BatteryInfo battery_info = 4;
}

// The license plate information of the Vehicle.  To avoid storing
// personally-identifiable information, only the minimum information
// about the license plate is stored as part of the entity.
message LicensePlate {
  // Required. CLDR Country/Region Code.  For example, `US` for United States,
  // or `IN` for India.
  string country_code = 1 [(google.api.field_behavior) = REQUIRED];

  // The last digit of the license plate or "-1" to denote no numeric value
  // is present in the license plate.
  //
  // * "ABC 1234" -> "4"
  // * "AB 123 CD" -> "3"
  // * "ABCDEF" -> "-1"
  string last_character = 2;
}

// Describes how clients should color one portion of the polyline along the
// route.
message VisualTrafficReportPolylineRendering {
  // One road stretch that should be rendered.
  message RoadStretch {
    // The traffic style, indicating traffic speed.
    enum Style {
      // No style selected.
      STYLE_UNSPECIFIED = 0;

      // Traffic is slowing down.
      SLOWER_TRAFFIC = 1;

      // There is a traffic jam.
      TRAFFIC_JAM = 2;
    }

    // Required. The style to apply.
    Style style = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The style should be applied between `[offset_meters,
    // offset_meters + length_meters)`.
    int32 offset_meters = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. The length of the path where to apply the style.
    int32 length_meters = 3 [(google.api.field_behavior) = REQUIRED];
  }

  // Optional. Road stretches that should be rendered along the polyline.
  // Stretches are guaranteed to not overlap, and do not necessarily span the
  // full route.
  //
  // In the absence of a road stretch to style, the client should apply the
  // default for the route.
  repeated RoadStretch road_stretch = 1
      [(google.api.field_behavior) = OPTIONAL];
}

// Traffic conditions along the expected vehicle route.
message TrafficPolylineData {
  // A polyline rendering of how fast traffic is for all regions along
  // one stretch of a customer ride.
  VisualTrafficReportPolylineRendering traffic_rendering = 1;
}

// The state of a `Vehicle`.
enum VehicleState {
  // Default, used for unspecified or unrecognized vehicle states.
  UNKNOWN_VEHICLE_STATE = 0;

  // The vehicle is not accepting new trips. Note: the vehicle may continue to
  // operate in this state while completing a trip assigned to it.
  OFFLINE = 1;

  // The vehicle is accepting new trips.
  ONLINE = 2;
}

// How location features are configured to behave on the mobile device when the
// devices "battery saver" feature is on.
// (https://developer.android.com/reference/android/os/PowerManager#getLocationPowerSaveMode())
enum LocationPowerSaveMode {
  // Undefined LocationPowerSaveMode
  UNKNOWN_LOCATION_POWER_SAVE_MODE = 0;

  // Either the location providers shouldn't be affected by battery saver, or
  // battery saver is off.
  LOCATION_MODE_NO_CHANGE = 1;

  // The GPS based location provider should be disabled when battery saver is on
  // and the device is non-interactive.
  LOCATION_MODE_GPS_DISABLED_WHEN_SCREEN_OFF = 2;

  // All location providers should be disabled when battery saver is on and the
  // device is non-interactive.
  LOCATION_MODE_ALL_DISABLED_WHEN_SCREEN_OFF = 3;

  // All the location providers will be kept available, but location fixes
  // should only be provided to foreground apps.
  LOCATION_MODE_FOREGROUND_ONLY = 4;

  // Location will not be turned off, but LocationManager will throttle all
  // requests to providers when the device is non-interactive.
  LOCATION_MODE_THROTTLE_REQUESTS_WHEN_SCREEN_OFF = 5;
}

// Status of the battery, whether full or charging etc.
enum BatteryStatus {
  // Battery status unknown.
  UNKNOWN_BATTERY_STATUS = 0;

  // Battery is being charged.
  BATTERY_STATUS_CHARGING = 1;

  // Battery is discharging.
  BATTERY_STATUS_DISCHARGING = 2;

  // Battery is full.
  BATTERY_STATUS_FULL = 3;

  // Battery is not charging.
  BATTERY_STATUS_NOT_CHARGING = 4;

  // Battery is low on power.
  BATTERY_STATUS_POWER_LOW = 5;
}

// Type of the charger being used to charge the battery.
enum PowerSource {
  // Power source unknown.
  UNKNOWN_POWER_SOURCE = 0;

  // Power source is an AC charger.
  POWER_SOURCE_AC = 1;

  // Power source is a USB port.
  POWER_SOURCE_USB = 2;

  // Power source is wireless.
  POWER_SOURCE_WIRELESS = 3;

  // Battery is unplugged.
  POWER_SOURCE_UNPLUGGED = 4;
}
