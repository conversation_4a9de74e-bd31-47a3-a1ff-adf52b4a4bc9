// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package maps.fleetengine.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/api/routing.proto";
import "google/maps/fleetengine/v1/fleetengine.proto";
import "google/maps/fleetengine/v1/header.proto";
import "google/maps/fleetengine/v1/trips.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Maps.FleetEngine.V1";
option go_package = "cloud.google.com/go/maps/fleetengine/apiv1/fleetenginepb;fleetenginepb";
option java_multiple_files = true;
option java_outer_classname = "TripApi";
option java_package = "com.google.maps.fleetengine.v1";
option objc_class_prefix = "CFE";
option php_namespace = "Google\\Maps\\FleetEngine\\V1";
option ruby_package = "Google::Maps::FleetEngine::V1";

// Trip management service.
service TripService {
  option (google.api.default_host) = "fleetengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a trip in the Fleet Engine and returns the new trip.
  rpc CreateTrip(CreateTripRequest) returns (Trip) {
    option (google.api.http) = {
      post: "/v1/{parent=providers/*}/trips"
      body: "trip"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "{provider_id=providers/*}"
      }
    };
  }

  // Get information about a single trip.
  rpc GetTrip(GetTripRequest) returns (Trip) {
    option (google.api.http) = {
      get: "/v1/{name=providers/*/trips/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "{provider_id=providers/*}"
      }
    };
  }

  // Report billable trip usage.
  rpc ReportBillableTrip(ReportBillableTripRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=providers/*/billableTrips/*}:report"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "{provider_id=providers/*}"
      }
    };
  }

  // Get all the trips for a specific vehicle.
  rpc SearchTrips(SearchTripsRequest) returns (SearchTripsResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=providers/*}/trips:search"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "{provider_id=providers/*}"
      }
    };
  }

  // Updates trip data.
  rpc UpdateTrip(UpdateTripRequest) returns (Trip) {
    option (google.api.http) = {
      put: "/v1/{name=providers/*/trips/*}"
      body: "trip"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "{provider_id=providers/*}"
      }
    };
  }
}

// CreateTrip request message.
message CreateTripRequest {
  // The standard Fleet Engine request header.
  RequestHeader header = 1;

  // Required. Must be in the format `providers/{provider}`.
  // The provider must be the Project ID (for example, `sample-cloud-project`)
  // of the Google Cloud Project of which the service account making
  // this call is a member.
  string parent = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "fleetengine.googleapis.com/Trip"
    }
  ];

  // Required. Unique Trip ID.
  // Subject to the following restrictions:
  //
  // * Must be a valid Unicode string.
  // * Limited to a maximum length of 64 characters.
  // * Normalized according to [Unicode Normalization Form C]
  // (http://www.unicode.org/reports/tr15/).
  // * May not contain any of the following ASCII characters: '/', ':', '?',
  // ',', or '#'.
  string trip_id = 5 [(google.api.field_behavior) = REQUIRED];

  // Required. Trip entity to create.
  //
  // When creating a Trip, the following fields are required:
  //
  // * `trip_type`
  // * `pickup_point`
  //
  // The following fields are used if you provide them:
  //
  // * `number_of_passengers`
  // * `vehicle_id`
  // * `dropoff_point`
  // * `intermediate_destinations`
  // * `vehicle_waypoints`
  //
  // All other Trip fields are ignored. For example, all trips start with a
  // `trip_status` of `NEW` even if you pass in a `trip_status` of `CANCELED` in
  // the creation request.
  //
  // Only `EXCLUSIVE` trips support `intermediate_destinations`.
  //
  // When `vehicle_id` is set for a shared trip, you must supply
  // the list of `Trip.vehicle_waypoints` to specify the order of the remaining
  // waypoints for the vehicle, otherwise the waypoint order will be
  // undetermined.
  //
  // When you specify `Trip.vehicle_waypoints`, the list must contain all
  // the remaining waypoints of the vehicle's trips, with no extra waypoints.
  // You must order these waypoints such that for a given trip, the pickup
  // point is before intermediate destinations, and all intermediate
  // destinations come before the drop-off point. An `EXCLUSIVE` trip's
  // waypoints must not interleave with any other trips.
  //
  // The `trip_id`, `waypoint_type` and `location` fields are used, and all
  // other TripWaypoint fields in `vehicle_waypoints` are ignored.
  Trip trip = 4 [(google.api.field_behavior) = REQUIRED];
}

// GetTrip request message.
message GetTripRequest {
  // The standard Fleet Engine request header.
  RequestHeader header = 1;

  // Required. Must be in the format `providers/{provider}/trips/{trip}`.
  // The provider must be the Project ID (for example, `sample-cloud-project`)
  // of the Google Cloud Project of which the service account making
  // this call is a member.
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "fleetengine.googleapis.com/Trip"
    }
  ];

  // The subset of Trip fields that should be returned and their interpretation.
  TripView view = 11;

  // Indicates the minimum timestamp (exclusive) for which `Trip.route` or
  // `Trip.current_route_segment` data are retrieved. If route data are
  // unchanged since this timestamp, the route field is not set in the response.
  // If a minimum is unspecified, the route data are always retrieved.
  google.protobuf.Timestamp current_route_segment_version = 6;

  // Indicates the minimum timestamp (exclusive) for which
  // `Trip.remaining_waypoints` are retrieved. If they are unchanged since this
  // timestamp, the `remaining_waypoints` are not set in the response. If this
  // field is unspecified, `remaining_waypoints` is always retrieved.
  google.protobuf.Timestamp remaining_waypoints_version = 7;

  // The returned current route format, `LAT_LNG_LIST_TYPE` (in `Trip.route`),
  // or `ENCODED_POLYLINE_TYPE` (in `Trip.current_route_segment`). The default
  // is `LAT_LNG_LIST_TYPE`.
  PolylineFormatType route_format_type = 8;

  // Indicates the minimum timestamp (exclusive) for which
  // `Trip.current_route_segment_traffic` is retrieved. If traffic data are
  // unchanged since this timestamp, the `current_route_segment_traffic` field
  // is not set in the response. If a minimum is unspecified, the traffic data
  // are always retrieved. Note that traffic is only available for On-Demand
  // Rides and Deliveries Solution customers.
  google.protobuf.Timestamp current_route_segment_traffic_version = 9;

  // Indicates the minimum timestamp (exclusive) for which
  // `Trip.remaining_waypoints.traffic_to_waypoint` and
  // `Trip.remaining_waypoints.path_to_waypoint` data are retrieved. If data are
  // unchanged since this timestamp, the fields above are
  // not set in the response. If `remaining_waypoints_route_version` is
  // unspecified, traffic and path are always retrieved.
  google.protobuf.Timestamp remaining_waypoints_route_version = 10;
}

// ReportBillableTrip request message.
message ReportBillableTripRequest {
  // Selector for different solution types of a reported trip.
  enum SolutionType {
    // The default value. For backwards-compatibility, the API will use
    // `ON_DEMAND_RIDESHARING_AND_DELIVERIES` by default which is the first
    // supported solution type.
    SOLUTION_TYPE_UNSPECIFIED = 0;

    // The solution is an on-demand ridesharing and deliveries trip.
    ON_DEMAND_RIDESHARING_AND_DELIVERIES = 1;
  }

  // Required. Must be in the format
  // `providers/{provider}/billableTrips/{billable_trip}`. The
  // provider must be the Project ID (for example, `sample-cloud-project`) of
  // the Google Cloud Project of which the service account making this call is a
  // member.
  string name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Two letter country code of the country where the trip takes
  // place. Price is defined according to country code.
  string country_code = 3 [(google.api.field_behavior) = REQUIRED];

  // The platform upon which the request was issued.
  BillingPlatformIdentifier platform = 5;

  // The identifiers that are directly related to the trip being reported. These
  // are usually IDs (for example, session IDs) of pre-booking operations done
  // before the trip ID is available. The number of `related_ids` is
  // limited to 50.
  repeated string related_ids = 6;

  // The type of GMP product solution (for example,
  // `ON_DEMAND_RIDESHARING_AND_DELIVERIES`) used for the reported trip.
  SolutionType solution_type = 7;
}

// UpdateTrip request message.
message UpdateTripRequest {
  // The standard Fleet Engine request header.
  RequestHeader header = 1;

  // Required. Must be in the format
  // `providers/{provider}/trips/{trip}`. The provider must
  // be the Project ID (for example, `sample-consumer-project`) of the Google
  // Cloud Project of which the service account making this call is a member.
  string name = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The Trip associated with the update.
  //
  // The following fields are maintained by the Fleet Engine. Do not update
  // them using Trip.update.
  //
  // * `current_route_segment`
  // * `current_route_segment_end_point`
  // * `current_route_segment_traffic`
  // * `current_route_segment_traffic_version`
  // * `current_route_segment_version`
  // * `dropoff_time`
  // * `eta_to_next_waypoint`
  // * `intermediate_destinations_version`
  // * `last_location`
  // * `name`
  // * `number_of_passengers`
  // * `pickup_time`
  // * `remaining_distance_meters`
  // * `remaining_time_to_first_waypoint`
  // * `remaining_waypoints`
  // * `remaining_waypoints_version`
  // * `route`
  //
  // When you update the `Trip.vehicle_id` for a shared trip, you must supply
  // the list of `Trip.vehicle_waypoints` to specify the order of the remaining
  // waypoints, otherwise the order will be undetermined.
  //
  // When you specify `Trip.vehicle_waypoints`, the list must contain all
  // the remaining waypoints of the vehicle's trips, with no extra waypoints.
  // You must order these waypoints such that for a given trip, the pickup
  // point is before intermediate destinations, and all intermediate
  // destinations come before the drop-off point. An `EXCLUSIVE` trip's
  // waypoints must not interleave with any other trips.
  // The `trip_id`, `waypoint_type` and `location` fields are used, and all
  // other TripWaypoint fields in `vehicle_waypoints` are ignored.
  //
  // To avoid a race condition for trips with multiple destinations, you
  // should provide `Trip.intermediate_destinations_version` when updating
  // the trip status to `ENROUTE_TO_INTERMEDIATE_DESTINATION`. The
  // `Trip.intermediate_destinations_version` passed must be consistent with
  // Fleet Engine's version. If it isn't, the request fails.
  Trip trip = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The field mask indicating which fields in Trip to update.
  // The `update_mask` must contain at least one field.
  google.protobuf.FieldMask update_mask = 5
      [(google.api.field_behavior) = REQUIRED];
}

// SearchTrips request message.
message SearchTripsRequest {
  // The standard Fleet Engine request header.
  RequestHeader header = 1;

  // Required. Must be in the format `providers/{provider}`.
  // The provider must be the Project ID (for example, `sample-cloud-project`)
  // of the Google Cloud Project of which the service account making
  // this call is a member.
  string parent = 3 [(google.api.field_behavior) = REQUIRED];

  // The vehicle associated with the trips in the request. If unspecified, the
  // returned trips do not contain:
  //
  // * `current_route_segment`
  // * `remaining_waypoints`
  // * `remaining_distance_meters`
  // * `eta_to_first_waypoint`
  string vehicle_id = 4;

  // If set to true, the response includes Trips that influence a driver's
  // route.
  bool active_trips_only = 5;

  // If not set, the server decides the number of results to return.
  int32 page_size = 6;

  // Set this to a value previously returned in the `SearchTripsResponse` to
  // continue from previous results.
  string page_token = 7;

  // If specified, returns the trips that have not been updated after the time
  // `(current - minimum_staleness)`.
  google.protobuf.Duration minimum_staleness = 8;
}

// SearchTrips response message.
message SearchTripsResponse {
  // The list of trips for the requested vehicle.
  repeated Trip trips = 1;

  // Pass this token in the SearchTripsRequest to page through list results. The
  // API returns a trip list on each call, and when no more results remain the
  // trip list is empty.
  string next_page_token = 2;
}
