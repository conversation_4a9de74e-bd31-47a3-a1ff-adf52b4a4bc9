// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package maps.fleetengine.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/api/routing.proto";
import "google/geo/type/viewport.proto";
import "google/maps/fleetengine/v1/fleetengine.proto";
import "google/maps/fleetengine/v1/header.proto";
import "google/maps/fleetengine/v1/vehicles.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/type/latlng.proto";

option csharp_namespace = "Google.Maps.FleetEngine.V1";
option go_package = "cloud.google.com/go/maps/fleetengine/apiv1/fleetenginepb;fleetenginepb";
option java_multiple_files = true;
option java_outer_classname = "VehicleApi";
option java_package = "com.google.maps.fleetengine.v1";
option objc_class_prefix = "CFE";
option php_namespace = "Google\\Maps\\FleetEngine\\V1";
option ruby_package = "Google::Maps::FleetEngine::V1";

// Vehicle management service.
service VehicleService {
  option (google.api.default_host) = "fleetengine.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Instantiates a new vehicle associated with an on-demand rideshare or
  // deliveries provider. Each `Vehicle` must have a unique vehicle ID.
  //
  // The following `Vehicle` fields are required when creating a `Vehicle`:
  //
  // * `vehicleState`
  // * `supportedTripTypes`
  // * `maximumCapacity`
  // * `vehicleType`
  //
  // The following `Vehicle` fields are ignored when creating a `Vehicle`:
  //
  // * `name`
  // * `currentTrips`
  // * `availableCapacity`
  // * `current_route_segment`
  // * `current_route_segment_end_point`
  // * `current_route_segment_version`
  // * `current_route_segment_traffic`
  // * `route`
  // * `waypoints`
  // * `waypoints_version`
  // * `remaining_distance_meters`
  // * `remaining_time_seconds`
  // * `eta_to_next_waypoint`
  // * `navigation_status`
  //
  // All other fields are optional and used if provided.
  rpc CreateVehicle(CreateVehicleRequest) returns (Vehicle) {
    option (google.api.http) = {
      post: "/v1/{parent=providers/*}/vehicles"
      body: "vehicle"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "{provider_id=providers/*}"
      }
    };
  }

  // Returns a vehicle from the Fleet Engine.
  rpc GetVehicle(GetVehicleRequest) returns (Vehicle) {
    option (google.api.http) = {
      get: "/v1/{name=providers/*/vehicles/*}"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "{provider_id=providers/*}"
      }
    };
  }

  // Writes updated vehicle data to the Fleet Engine.
  //
  // When updating a `Vehicle`, the following fields cannot be updated since
  // they are managed by the server:
  //
  // * `currentTrips`
  // * `availableCapacity`
  // * `current_route_segment_version`
  // * `waypoints_version`
  //
  // The vehicle `name` also cannot be updated.
  //
  // If the `attributes` field is updated, **all** the vehicle's attributes are
  // replaced with the attributes provided in the request. If you want to update
  // only some attributes, see the `UpdateVehicleAttributes` method. Likewise,
  // the `waypoints` field can be updated, but must contain all the waypoints
  // currently on the vehicle, and no other waypoints.
  rpc UpdateVehicle(UpdateVehicleRequest) returns (Vehicle) {
    option (google.api.http) = {
      put: "/v1/{name=providers/*/vehicles/*}"
      body: "vehicle"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "{provider_id=providers/*}"
      }
    };
  }

  // Partially updates a vehicle's attributes.
  // Only the attributes mentioned in the request will be updated, other
  // attributes will NOT be altered. Note: this is different in `UpdateVehicle`,
  // where the whole `attributes` field will be replaced by the one in
  // `UpdateVehicleRequest`, attributes not in the request would be removed.
  rpc UpdateVehicleAttributes(UpdateVehicleAttributesRequest)
      returns (UpdateVehicleAttributesResponse) {
    option (google.api.http) = {
      post: "/v1/{name=providers/*/vehicles/*}:updateAttributes"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "name"
        path_template: "{provider_id=providers/*}"
      }
    };
  }

  // Returns a paginated list of vehicles associated with
  // a provider that match the request options.
  rpc ListVehicles(ListVehiclesRequest) returns (ListVehiclesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=providers/*}/vehicles"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "{provider_id=providers/*}"
      }
    };
  }

  // Returns a list of vehicles that match the request options.
  rpc SearchVehicles(SearchVehiclesRequest) returns (SearchVehiclesResponse) {
    option (google.api.http) = {
      post: "/v1/{parent=providers/*}/vehicles:search"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters {
        field: "parent"
        path_template: "{provider_id=providers/*}"
      }
    };
  }
}

// `CreateVehicle` request message.
message CreateVehicleRequest {
  // The standard Fleet Engine request header.
  RequestHeader header = 1;

  // Required. Must be in the format `providers/{provider}`.
  // The provider must be the Project ID (for example, `sample-cloud-project`)
  // of the Google Cloud Project of which the service account making
  // this call is a member.
  string parent = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Unique Vehicle ID.
  // Subject to the following restrictions:
  //
  // * Must be a valid Unicode string.
  // * Limited to a maximum length of 64 characters.
  // * Normalized according to [Unicode Normalization Form C]
  // (http://www.unicode.org/reports/tr15/).
  // * May not contain any of the following ASCII characters: '/', ':', '?',
  // ',', or '#'.
  string vehicle_id = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The Vehicle entity to create. When creating a Vehicle, the
  // following fields are required:
  //
  // * `vehicleState`
  // * `supportedTripTypes`
  // * `maximumCapacity`
  // * `vehicleType`
  //
  // When creating a Vehicle, the following fields are ignored:
  //
  // * `name`
  // * `currentTrips`
  // * `availableCapacity`
  // * `current_route_segment`
  // * `current_route_segment_end_point`
  // * `current_route_segment_version`
  // * `current_route_segment_traffic`
  // * `route`
  // * `waypoints`
  // * `waypoints_version`
  // * `remaining_distance_meters`
  // * `remaining_time_seconds`
  // * `eta_to_next_waypoint`
  // * `navigation_status`
  //
  // All other fields are optional and used if provided.
  Vehicle vehicle = 5 [(google.api.field_behavior) = REQUIRED];
}

// `GetVehicle` request message.
message GetVehicleRequest {
  // The standard Fleet Engine request header.
  RequestHeader header = 1;

  // Required. Must be in the format
  // `providers/{provider}/vehicles/{vehicle}`.
  // The provider must be the Project ID (for example, `sample-cloud-project`)
  // of the Google Cloud Project of which the service account making
  // this call is a member.
  string name = 3 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "fleetengine.googleapis.com/Vehicle"
    }
  ];

  // Indicates the minimum timestamp (exclusive) for which
  // `Vehicle.current_route_segment` is retrieved.
  // If the route is unchanged since this timestamp, the `current_route_segment`
  // field is not set in the response. If a minimum is unspecified, the
  // `current_route_segment` is always retrieved.
  google.protobuf.Timestamp current_route_segment_version = 4;

  // Indicates the minimum timestamp (exclusive) for which `Vehicle.waypoints`
  // data is retrieved. If the waypoints are unchanged since this timestamp, the
  // `vehicle.waypoints` data is not set in the response. If this field is
  // unspecified, `vehicle.waypoints` is always retrieved.
  google.protobuf.Timestamp waypoints_version = 5;
}

// `UpdateVehicle request message.
message UpdateVehicleRequest {
  // The standard Fleet Engine request header.
  RequestHeader header = 1;

  // Required. Must be in the format
  // `providers/{provider}/vehicles/{vehicle}`.
  // The {provider} must be the Project ID (for example, `sample-cloud-project`)
  // of the Google Cloud Project of which the service account making
  // this call is a member.
  string name = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The `Vehicle` entity values to apply.  When updating a `Vehicle`,
  // the following fields may not be updated as they are managed by the
  // server.
  //
  // * `available_capacity`
  // * `current_route_segment_version`
  // * `current_trips`
  // * `name`
  // * `waypoints_version`
  //
  // If the `attributes` field is updated, **all** the vehicle's attributes are
  // replaced with the attributes provided in the request. If you want to update
  // only some attributes, see the `UpdateVehicleAttributes` method.
  //
  // Likewise, the `waypoints` field can be updated, but must contain all the
  // waypoints currently on the vehicle, and no other waypoints.
  Vehicle vehicle = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. A field mask indicating which fields of the `Vehicle` to update.
  // At least one field name must be provided.
  google.protobuf.FieldMask update_mask = 5
      [(google.api.field_behavior) = REQUIRED];
}

// `UpdateVehicleAttributes` request message.
message UpdateVehicleAttributesRequest {
  // The standard Fleet Engine request header.
  RequestHeader header = 1;

  // Required. Must be in the format `providers/{provider}/vehicles/{vehicle}`.
  // The provider must be the Project ID (for example, `sample-cloud-project`)
  // of the Google Cloud Project of which the service account making
  // this call is a member.
  string name = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The vehicle attributes to update. Unmentioned attributes are not
  // altered or removed.
  repeated VehicleAttribute attributes = 4
      [(google.api.field_behavior) = REQUIRED];
}

// `UpdateVehicleAttributes` response message.
message UpdateVehicleAttributesResponse {
  // Required. The updated full list of vehicle attributes, including new,
  // altered, and untouched attributes.
  repeated VehicleAttribute attributes = 1
      [(google.api.field_behavior) = REQUIRED];
}

// `SearchVehicles` request message.
message SearchVehiclesRequest {
  // Specifies the order of the vehicle matches in the response.
  enum VehicleMatchOrder {
    // Default, used for unspecified or unrecognized vehicle matches order.
    UNKNOWN_VEHICLE_MATCH_ORDER = 0;

    // Ascending order by vehicle driving time to the pickup point.
    PICKUP_POINT_ETA = 1;

    // Ascending order by vehicle driving distance to the pickup point.
    PICKUP_POINT_DISTANCE = 2;

    // Ascending order by vehicle driving time to the dropoff point. This order
    // can only be used if the dropoff point is specified in the request.
    DROPOFF_POINT_ETA = 3;

    // Ascending order by straight-line distance from the vehicle's last
    // reported location to the pickup point.
    PICKUP_POINT_STRAIGHT_DISTANCE = 4;

    // Ascending order by the configured match cost. Match cost is defined as a
    // weighted calculation between straight-line distance and ETA. Weights are
    // set with default values and can be modified per customer. Please contact
    // Google support if these weights need to be modified for your project.
    COST = 5;
  }

  // Specifies the types of restrictions on a vehicle's current trips.
  enum CurrentTripsPresent {
    // The availability of vehicles with trips present is governed by the
    // `include_back_to_back` field.
    CURRENT_TRIPS_PRESENT_UNSPECIFIED = 0;

    // Vehicles without trips can appear in search results. When this value is
    // used, `include_back_to_back` cannot be `true`.
    NONE = 1;

    // Vehicles with at most 5 current trips and 10 waypoints are included
    // in the search results. When this value is used, `include_back_to_back`
    // cannot be `true`.
    ANY = 2;
  }

  // The standard Fleet Engine request header.
  RequestHeader header = 1;

  // Required. Must be in the format `providers/{provider}`.
  // The provider must be the Project ID (for example, `sample-cloud-project`)
  // of the Google Cloud Project of which the service account making
  // this call is a member.
  string parent = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The pickup point to search near.
  TerminalLocation pickup_point = 4 [(google.api.field_behavior) = REQUIRED];

  // The customer's intended dropoff location. The field is required if
  // `trip_types` contains `TripType.SHARED`.
  TerminalLocation dropoff_point = 5;

  // Required. Defines the vehicle search radius around the pickup point. Only
  // vehicles within the search radius will be returned. Value must be between
  // 400 and 10000 meters (inclusive).
  int32 pickup_radius_meters = 6 [(google.api.field_behavior) = REQUIRED];

  // Required. Specifies the maximum number of vehicles to return. The value
  // must be between 1 and 50 (inclusive).
  int32 count = 7 [(google.api.field_behavior) = REQUIRED];

  // Required. Specifies the number of passengers being considered for a trip.
  // The value must be greater than or equal to one. The driver is not
  // considered in the capacity value.
  int32 minimum_capacity = 8 [(google.api.field_behavior) = REQUIRED];

  // Required. Represents the type of proposed trip. Must include exactly one
  // type. `UNKNOWN_TRIP_TYPE` is not allowed. Restricts the search to only
  // those vehicles that can support that trip type.
  repeated TripType trip_types = 9 [(google.api.field_behavior) = REQUIRED];

  // Restricts the search to only those vehicles that have sent location updates
  // to Fleet Engine within the specified duration. Stationary vehicles still
  // transmitting their locations are not considered stale. If this field is not
  // set, the server uses five minutes as the default value.
  google.protobuf.Duration maximum_staleness = 10;

  // Required. Restricts the search to vehicles with one of the specified types.
  // At least one vehicle type must be specified. VehicleTypes with a category
  // of `UNKNOWN` are not allowed.
  repeated Vehicle.VehicleType vehicle_types = 14
      [(google.api.field_behavior) = REQUIRED];

  // Callers can form complex logical operations using any combination of the
  // `required_attributes`, `required_one_of_attributes`, and
  // `required_one_of_attribute_sets` fields.
  //
  // `required_attributes` is a list; `required_one_of_attributes` uses a
  // message which allows a list of lists. In combination, the two fields allow
  // the composition of this expression:
  //
  // ```
  // (required_attributes[0] AND required_attributes[1] AND ...)
  // AND
  // (required_one_of_attributes[0][0] OR required_one_of_attributes[0][1] OR
  // ...)
  // AND
  // (required_one_of_attributes[1][0] OR required_one_of_attributes[1][1] OR
  // ...)
  // ```
  //
  // Restricts the search to only those vehicles with the specified attributes.
  // This field is a conjunction/AND operation. A max of 50 required_attributes
  // is allowed. This matches the maximum number of attributes allowed on a
  // vehicle.
  repeated VehicleAttribute required_attributes = 12;

  // Restricts the search to only those vehicles with at least one of
  // the specified attributes in each `VehicleAttributeList`. Within each
  // list, a vehicle must match at least one of the attributes. This field is an
  // inclusive disjunction/OR operation in each `VehicleAttributeList` and a
  // conjunction/AND operation across the collection of `VehicleAttributeList`.
  repeated VehicleAttributeList required_one_of_attributes = 15;

  // `required_one_of_attribute_sets` provides additional functionality.
  //
  // Similar to `required_one_of_attributes`, `required_one_of_attribute_sets`
  // uses a message which allows a list of lists, allowing expressions such as
  // this one:
  //
  // ```
  // (required_attributes[0] AND required_attributes[1] AND ...)
  // AND
  // (
  //   (required_one_of_attribute_sets[0][0] AND
  //   required_one_of_attribute_sets[0][1] AND
  //   ...)
  //   OR
  //   (required_one_of_attribute_sets[1][0] AND
  //   required_one_of_attribute_sets[1][1] AND
  //   ...)
  // )
  // ```
  //
  // Restricts the search to only those vehicles with all the attributes in a
  // `VehicleAttributeList`. Within each list, a
  // vehicle must match all of the attributes. This field is a conjunction/AND
  // operation in each `VehicleAttributeList` and inclusive disjunction/OR
  // operation across the collection of `VehicleAttributeList`.
  repeated VehicleAttributeList required_one_of_attribute_sets = 20;

  // Required. Specifies the desired ordering criterion for results.
  VehicleMatchOrder order_by = 13 [(google.api.field_behavior) = REQUIRED];

  // This indicates if vehicles with a single active trip are eligible for this
  // search. This field is only used when `current_trips_present` is
  // unspecified. When `current_trips_present` is unspecified  and  this field
  // is `false`, vehicles with assigned trips are excluded from the search
  // results. When `current_trips_present` is unspecified and this field is
  // `true`, search results can include vehicles with one active trip that has a
  // status of `ENROUTE_TO_DROPOFF`. When `current_trips_present` is specified,
  // this field cannot be set to true.
  //
  // The default value is `false`.
  bool include_back_to_back = 18;

  // Indicates the trip associated with this `SearchVehicleRequest`.
  string trip_id = 19;

  // This indicates if vehicles with active trips are eligible for this search.
  // This must be set to something other than
  // `CURRENT_TRIPS_PRESENT_UNSPECIFIED` if `trip_type` includes `SHARED`.
  CurrentTripsPresent current_trips_present = 21;

  // Optional. A filter query to apply when searching vehicles. See
  // http://aip.dev/160 for examples of the filter syntax.
  //
  // This field is designed to replace the `required_attributes`,
  // `required_one_of_attributes`, and `required_one_of_attributes_sets` fields.
  // If a non-empty value is specified here, the following fields must be empty:
  // `required_attributes`, `required_one_of_attributes`, and
  // `required_one_of_attributes_sets`.
  //
  // This filter functions as an AND clause with other constraints,
  // such as `minimum_capacity` or `vehicle_types`.
  //
  // Note that the only queries supported are on vehicle attributes (for
  // example, `attributes.<key> = <value>` or `attributes.<key1> = <value1> AND
  // attributes.<key2> = <value2>`). The maximum number of restrictions allowed
  // in a filter query is 50.
  //
  // Also, all attributes are stored as strings, so the only supported
  // comparisons against attributes are string comparisons. In order to compare
  // against number or boolean values, the values must be explicitly quoted to
  // be treated as strings (for example, `attributes.<key> = "10"` or
  // `attributes.<key> = "true"`).
  string filter = 22 [(google.api.field_behavior) = OPTIONAL];
}

// `SearchVehicles` response message.
message SearchVehiclesResponse {
  // List of vehicles that match the `SearchVehiclesRequest` criteria, ordered
  // according to `SearchVehiclesRequest.order_by` field.
  repeated VehicleMatch matches = 1;
}

// `ListVehicles` request message.
message ListVehiclesRequest {
  // The standard Fleet Engine request header.
  RequestHeader header = 12;

  // Required. Must be in the format `providers/{provider}`.
  // The provider must be the Project ID (for example, `sample-cloud-project`)
  // of the Google Cloud Project of which the service account making
  // this call is a member.
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of vehicles to return.
  // Default value: 100.
  int32 page_size = 3;

  // The value of the `next_page_token` provided by a previous call to
  // `ListVehicles` so that you can paginate through groups of vehicles. The
  // value is undefined if the filter criteria of the request is not the same as
  // the filter criteria for the previous call to `ListVehicles`.
  string page_token = 4;

  // Specifies the required minimum capacity of the vehicle. All vehicles
  // returned will have a `maximum_capacity` greater than or equal to this
  // value. If set, must be greater or equal to 0.
  google.protobuf.Int32Value minimum_capacity = 6;

  // Restricts the response to vehicles that support at least one of the
  // specified trip types.
  repeated TripType trip_types = 7;

  // Restricts the response to vehicles that have sent location updates to Fleet
  // Engine within the specified duration. Stationary vehicles still
  // transmitting their locations are not considered stale. If present, must be
  // a valid positive duration.
  google.protobuf.Duration maximum_staleness = 8;

  // Required. Restricts the response to vehicles with one of the specified type
  // categories. `UNKNOWN` is not allowed.
  repeated Vehicle.VehicleType.Category vehicle_type_categories = 9
      [(google.api.field_behavior) = REQUIRED];

  // Callers can form complex logical operations using any combination of the
  // `required_attributes`, `required_one_of_attributes`, and
  // `required_one_of_attribute_sets` fields.
  //
  // `required_attributes` is a list; `required_one_of_attributes` uses a
  // message which allows a list of lists. In combination, the two fields allow
  // the composition of this expression:
  //
  // ```
  // (required_attributes[0] AND required_attributes[1] AND ...)
  // AND
  // (required_one_of_attributes[0][0] OR required_one_of_attributes[0][1] OR
  // ...)
  // AND
  // (required_one_of_attributes[1][0] OR required_one_of_attributes[1][1] OR
  // ...)
  // ```
  //
  // Restricts the response to vehicles with the specified attributes. This
  // field is a conjunction/AND operation. A max of 50 required_attributes is
  // allowed. This matches the maximum number of attributes allowed on a
  // vehicle. Each repeated string should be of the format "key:value".
  repeated string required_attributes = 10;

  // Restricts the response to vehicles with at least one of the specified
  // attributes in each `VehicleAttributeList`. Within each list, a vehicle must
  // match at least one of the attributes. This field is an inclusive
  // disjunction/OR operation in each `VehicleAttributeList` and a
  // conjunction/AND operation across the collection of `VehicleAttributeList`.
  // Each repeated string should be of the format
  // "key1:value1|key2:value2|key3:value3".
  repeated string required_one_of_attributes = 13;

  // `required_one_of_attribute_sets` provides additional functionality.
  //
  // Similar to `required_one_of_attributes`, `required_one_of_attribute_sets`
  // uses a message which allows a list of lists, allowing expressions such as
  // this one:
  //
  // ```
  // (required_attributes[0] AND required_attributes[1] AND ...)
  // AND
  // (
  //   (required_one_of_attribute_sets[0][0] AND
  //   required_one_of_attribute_sets[0][1] AND
  //   ...)
  //   OR
  //   (required_one_of_attribute_sets[1][0] AND
  //   required_one_of_attribute_sets[1][1] AND
  //   ...)
  // )
  // ```
  //
  // Restricts the response to vehicles that match all the attributes in a
  // `VehicleAttributeList`. Within each list, a vehicle must match all of the
  // attributes. This field is a conjunction/AND operation in each
  // `VehicleAttributeList` and inclusive disjunction/OR operation across the
  // collection of `VehicleAttributeList`. Each repeated string should be of the
  // format "key1:value1|key2:value2|key3:value3".
  repeated string required_one_of_attribute_sets = 15;

  // Restricts the response to vehicles that have this vehicle state.
  VehicleState vehicle_state = 11;

  // Only return the vehicles with current trip(s).
  bool on_trip_only = 14;

  // Optional. A filter query to apply when listing vehicles. See
  // http://aip.dev/160 for examples of the filter syntax.
  //
  // This field is designed to replace the `required_attributes`,
  // `required_one_of_attributes`, and `required_one_of_attributes_sets` fields.
  // If a non-empty value is specified here, the following fields must be empty:
  // `required_attributes`, `required_one_of_attributes`, and
  // `required_one_of_attributes_sets`.
  //
  // This filter functions as an AND clause with other constraints,
  // such as `vehicle_state` or `on_trip_only`.
  //
  // Note that the only queries supported are on vehicle attributes (for
  // example, `attributes.<key> = <value>` or `attributes.<key1> = <value1> AND
  // attributes.<key2> = <value2>`). The maximum number of restrictions allowed
  // in a filter query is 50.
  //
  // Also, all attributes are stored as strings, so the only supported
  // comparisons against attributes are string comparisons. In order to compare
  // against number or boolean values, the values must be explicitly quoted to
  // be treated as strings (for example, `attributes.<key> = "10"` or
  // `attributes.<key> = "true"`).
  string filter = 16 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A filter that limits the vehicles returned to those whose last
  // known location was in the rectangular area defined by the viewport.
  google.geo.type.Viewport viewport = 17
      [(google.api.field_behavior) = OPTIONAL];
}

// `ListVehicles` response message.
message ListVehiclesResponse {
  // Vehicles matching the criteria in the request.
  // The maximum number of vehicles returned is determined by the `page_size`
  // field in the request.
  repeated Vehicle vehicles = 1;

  // Token to retrieve the next page of vehicles, or empty if there are no
  // more vehicles that meet the request criteria.
  string next_page_token = 2;

  // Required. Total number of vehicles matching the request criteria across all
  // pages.
  int64 total_size = 3 [(google.api.field_behavior) = REQUIRED];
}

// Describes intermediate points along a route for a `VehicleMatch` in a
// `SearchVehiclesResponse`. This concept is represented as a `TripWaypoint` in
// all other endpoints.
message Waypoint {
  // The location of this waypoint.
  google.type.LatLng lat_lng = 1;

  // The estimated time that the vehicle will arrive at this waypoint.
  google.protobuf.Timestamp eta = 2;
}

// Contains the vehicle and related estimates for a vehicle that match the
// points of active trips for the vehicle `SearchVehiclesRequest`.
message VehicleMatch {
  // Type of vehicle match.
  enum VehicleMatchType {
    // Unknown vehicle match type
    UNKNOWN = 0;

    // The vehicle currently has no trip assigned to it and can proceed to the
    // pickup point.
    EXCLUSIVE = 1;

    // The vehicle is currently assigned to a trip, but can proceed to the
    // pickup point after completing the in-progress trip.  ETA and distance
    // calculations take the existing trip into account.
    BACK_TO_BACK = 2;

    // The vehicle has sufficient capacity for a shared ride.
    CARPOOL = 3;

    // The vehicle will finish its current, active trip before proceeding to the
    // pickup point.  ETA and distance calculations take the existing trip into
    // account.
    CARPOOL_BACK_TO_BACK = 4;
  }

  // Required. A vehicle that matches the request.
  Vehicle vehicle = 1 [(google.api.field_behavior) = REQUIRED];

  // The vehicle's driving ETA to the pickup point specified in the
  // request. An empty value indicates a failure in calculating ETA for the
  // vehicle.  If `SearchVehiclesRequest.include_back_to_back` was `true` and
  // this vehicle has an active trip, `vehicle_pickup_eta` includes the time
  // required to complete the current active trip.
  google.protobuf.Timestamp vehicle_pickup_eta = 2;

  // The distance from the Vehicle's current location to the pickup point
  // specified in the request, including any intermediate pickup or dropoff
  // points for existing trips. This distance comprises the calculated driving
  // (route) distance, plus the straight line distance between the navigation
  // end point and the requested pickup point. (The distance between the
  // navigation end point and the requested pickup point is typically small.) An
  // empty value indicates an error in calculating the distance.
  google.protobuf.Int32Value vehicle_pickup_distance_meters = 3;

  // Required. The straight-line distance between the vehicle and the pickup
  // point specified in the request.
  google.protobuf.Int32Value vehicle_pickup_straight_line_distance_meters = 11
      [(google.api.field_behavior) = REQUIRED];

  // The complete vehicle's driving ETA to the drop off point specified in the
  // request. The ETA includes stopping at any waypoints before the
  // `dropoff_point` specified in the request. The value will only be populated
  // when a drop off point is specified in the request. An empty value indicates
  // an error calculating the ETA.
  google.protobuf.Timestamp vehicle_dropoff_eta = 4;

  // The vehicle's driving distance (in meters) from the pickup point
  // to the drop off point specified in the request. The distance is only
  // between the two points and does not include the vehicle location or any
  // other points that must be visited before the vehicle visits either the
  // pickup point or dropoff point. The value will only be populated when a
  // `dropoff_point` is specified in the request. An empty value indicates
  // a failure in calculating the distance from the pickup to
  // drop off point specified in the request.
  google.protobuf.Int32Value vehicle_pickup_to_dropoff_distance_meters = 5;

  // Required. The trip type of the request that was used to calculate the ETA
  // to the pickup point.
  TripType trip_type = 6 [(google.api.field_behavior) = REQUIRED];

  // The ordered list of waypoints used to calculate the ETA. The list
  // includes vehicle location, the pickup points of active
  // trips for the vehicle, and the pickup points provided in the
  // request. An empty list indicates a failure in calculating ETA for the
  // vehicle.
  repeated Waypoint vehicle_trips_waypoints = 7;

  // Type of the vehicle match.
  VehicleMatchType vehicle_match_type = 8;

  // The order requested for sorting vehicle matches. Equivalent to
  // `ordered_by`.
  SearchVehiclesRequest.VehicleMatchOrder requested_ordered_by = 9;

  // The order requested for sorting vehicle matches. Equivalent to
  // `requested_ordered_by`.
  SearchVehiclesRequest.VehicleMatchOrder ordered_by = 10;
}

// A list-of-lists datatype for vehicle attributes.
message VehicleAttributeList {
  // A list of attributes in this collection.
  repeated VehicleAttribute attributes = 1;
}
