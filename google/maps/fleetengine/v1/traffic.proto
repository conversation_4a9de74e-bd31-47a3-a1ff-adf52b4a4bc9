// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package maps.fleetengine.v1;

option csharp_namespace = "Google.Maps.FleetEngine.V1";
option go_package = "cloud.google.com/go/maps/fleetengine/apiv1/fleetenginepb;fleetenginepb";
option java_multiple_files = true;
option java_outer_classname = "TrafficProto";
option java_package = "com.google.maps.fleetengine.v1";
option objc_class_prefix = "CFE";
option php_namespace = "Google\\Maps\\FleetEngine\\V1";
option ruby_package = "Google::Maps::FleetEngine::V1";

// Traffic density indicator on a contiguous segment of a path. Given a path
// with points P_0, P_1, ... , P_N (zero-based index), the SpeedReadingInterval
// defines an interval and describes its traffic using the following categories.
message SpeedReadingInterval {
  // The classification of polyline speed based on traffic data.
  enum Speed {
    // Default value. This value is unused.
    SPEED_UNSPECIFIED = 0;

    // Normal speed, no slowdown is detected.
    NORMAL = 1;

    // Slowdown detected, but no traffic jam formed.
    SLOW = 2;

    // Traffic jam detected.
    TRAFFIC_JAM = 3;
  }

  // The starting index of this interval in the path.
  // In JSON, when the index is 0, the field will appear to be unpopulated.
  int32 start_polyline_point_index = 1;

  // The ending index of this interval in the path.
  // In JSON, when the index is 0, the field will appear to be unpopulated.
  int32 end_polyline_point_index = 2;

  // Traffic speed in this interval.
  Speed speed = 3;
}

// Traffic density along a Vehicle's path.
message ConsumableTrafficPolyline {
  // Traffic speed along the path from the previous waypoint to the current
  // waypoint.
  repeated SpeedReadingInterval speed_reading_interval = 1;

  // The path the driver is taking from the previous waypoint to the current
  // waypoint. This path has landmarks in it so clients can show traffic markers
  // along the path (see `speed_reading_interval`).  Decoding is not yet
  // supported.
  string encoded_path_to_waypoint = 2;
}
