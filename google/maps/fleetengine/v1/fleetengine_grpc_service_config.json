{"methodConfig": [{"name": [{"service": "maps.fleetengine.v1.TripService", "method": "CreateTrip"}, {"service": "maps.fleetengine.v1.TripService", "method": "GetTrip"}, {"service": "maps.fleetengine.v1.TripService", "method": "SearchTrips"}, {"service": "maps.fleetengine.v1.TripService", "method": "UpdateTrip"}, {"service": "maps.fleetengine.v1.VehicleService", "method": "CreateVehicle"}, {"service": "maps.fleetengine.v1.VehicleService", "method": "GetVehicle"}, {"service": "maps.fleetengine.v1.VehicleService", "method": "SearchVehicles"}, {"service": "maps.fleetengine.v1.VehicleService", "method": "UpdateVehicle"}, {"service": "maps.fleetengine.v1.VehicleService", "method": "UpdateVehicleAttributes"}], "timeout": "15s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}