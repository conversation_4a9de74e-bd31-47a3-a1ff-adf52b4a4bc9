// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1;

import "google/api/field_behavior.proto";
import "google/maps/routes/v1/compute_routes_request.proto";
import "google/maps/routes/v1/waypoint.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Maps.Routes.V1";
option go_package = "cloud.google.com/go/maps/routes/apiv1/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "ComputeRouteMatrixRequestProto";
option java_package = "com.google.maps.routes.v1";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1";

// ComputeRouteMatrix request message
message ComputeRouteMatrixRequest {
  // Required. Array of origins, which determines the rows of the response
  // matrix. Several size restrictions apply to the cardinality of origins and
  // destinations:
  //
  // * The number of elements (origins × destinations) must be no greater than
  // 625 in any case.
  // * The number of elements (origins × destinations) must be no greater than
  // 100 if routing_preference is set to `TRAFFIC_AWARE_OPTIMAL`.
  // * The number of waypoints (origins + destinations) specified as `place_id`
  // must be no greater than 50.
  repeated RouteMatrixOrigin origins = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Array of destinations, which determines the columns of the
  // response matrix.
  repeated RouteMatrixDestination destinations = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Specifies the mode of transportation.
  RouteTravelMode travel_mode = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies how to compute the route. The server attempts to use
  // the selected routing preference to compute the route. If the routing
  // preference results in an error or an extra long latency, an error is
  // returned. You can specify this option only when the `travel_mode` is
  // `DRIVE` or `TWO_WHEELER`, otherwise the request fails.
  RoutingPreference routing_preference = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The departure time. If you don't set this value, this defaults to
  // the time that you made the request. If you set this value to a time that
  // has already occurred, the request fails.
  google.protobuf.Timestamp departure_time = 5
      [(google.api.field_behavior) = OPTIONAL];
}

// A single origin for ComputeRouteMatrixRequest
message RouteMatrixOrigin {
  // Required. Origin waypoint
  Waypoint waypoint = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Modifiers for every route that takes this as the origin
  RouteModifiers route_modifiers = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A single destination for ComputeRouteMatrixRequest
message RouteMatrixDestination {
  // Required. Destination waypoint
  Waypoint waypoint = 1 [(google.api.field_behavior) = REQUIRED];
}
