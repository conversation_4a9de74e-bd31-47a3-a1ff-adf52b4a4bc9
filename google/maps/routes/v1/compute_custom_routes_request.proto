// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1;

import "google/api/field_behavior.proto";
import "google/maps/routes/v1/compute_routes_request.proto";
import "google/maps/routes/v1/polyline.proto";
import "google/maps/routes/v1/waypoint.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Maps.Routes.V1";
option go_package = "cloud.google.com/go/maps/routes/apiv1/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "ComputeCustomRoutesRequestProto";
option java_package = "com.google.maps.routes.v1";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1";

// ComputeCustomRoutes request message.
message ComputeCustomRoutesRequest {
  // Required. Origin waypoint.
  Waypoint origin = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Destination waypoint.
  Waypoint destination = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A set of waypoints along the route (excluding terminal points),
  // for either stopping at or passing by. Up to 25 intermediate waypoints are
  // supported.
  repeated Waypoint intermediates = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the mode of transportation. Only `DRIVE` and
  // 'TWO_WHEELER' are supported.
  RouteTravelMode travel_mode = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies how to compute the route. The server attempts to use
  // the selected routing preference to compute the route. If the routing
  // preference results in an error or an extra long latency, then an error is
  // returned. You can specify this option only when the `travel_mode` is
  // `DRIVE` or `TWO_WHEELER`, otherwise the request fails.
  RoutingPreference routing_preference = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies your preference for the quality of the polyline.
  PolylineQuality polyline_quality = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the preferred encoding for the polyline.
  PolylineEncoding polyline_encoding = 13
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The departure time. If you don't set this value, then this value
  // defaults to the time that you made the request. If you set this value to a
  // time that has already occurred, then the request fails.
  google.protobuf.Timestamp departure_time = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A set of conditions to satisfy that affect the way routes are
  // calculated.
  RouteModifiers route_modifiers = 11 [(google.api.field_behavior) = OPTIONAL];

  // Required. A route objective to optimize for.
  RouteObjective route_objective = 12 [(google.api.field_behavior) = REQUIRED];

  // Optional. The BCP-47 language code, such as "en-US" or "sr-Latn". For more
  // information, see [Unicode Locale
  // Identifier](http://www.unicode.org/reports/tr35/#Unicode_locale_identifier).
  // See [Language
  // Support](https://developers.google.com/maps/faq#languagesupport) for the
  // list of supported languages. When you don't provide this value, the display
  // language is inferred from the location of the route request.
  string language_code = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the units of measure for the display fields. This
  // includes the `instruction` field in `NavigationInstruction`. The units of
  // measure used for the route, leg, step distance, and duration are not
  // affected by this value. If you don't provide this value, then the display
  // units are inferred from the location of the request.
  Units units = 10 [(google.api.field_behavior) = OPTIONAL];
}

// Encapsulates an objective to optimize for by `ComputeCustomRoutes`.
message RouteObjective {
  // Encapsulates a `RateCard` route objective.
  message RateCard {
    // Encapsulates the cost used in the rate card.
    message MonetaryCost {
      // Required. The cost value in local currency inferred from the request.
      double value = 1 [(google.api.field_behavior) = REQUIRED];
    }

    // Optional. Cost per minute.
    MonetaryCost cost_per_minute = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Cost per kilometer.
    MonetaryCost cost_per_km = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Whether to include toll cost in the overall cost.
    bool include_tolls = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // Deprecated: Custom layers will stop affecting route generation soon.
  // Customized data layer that customers use to influence the generated route.
  message CustomLayer {
    option deprecated = true;

    // Information about a dataset that customers have uploaded in advance. The
    // dataset information is used to influence routing.
    message DatasetInfo {
      // Optional. Deprecated: use display_name instead.
      // ID of a customer uploaded dataset which is used to influence the route.
      // If the dataset does not exist or is not yet ready, the request fails.
      string dataset_id = 1
          [deprecated = true, (google.api.field_behavior) = OPTIONAL];

      // Optional. Display name of the customer uploaded dataset which is used
      // to influence the route. If the dataset does not exist or is not yet
      // ready, the request fails.
      string display_name = 2 [(google.api.field_behavior) = OPTIONAL];
    }

    // Required. A dataset that the customer has uploaded in advance.
    DatasetInfo dataset_info = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // The route objective.
  oneof objective {
    // The RateCard objective.
    RateCard rate_card = 1;
  }

  // Optional. Deprecated: Custom layers will stop affecting route generation
  // soon. Specifies the custom data layer being used to affect generated
  // routes. Customers can turn off the custom layer by not setting this field.
  CustomLayer custom_layer = 2 [(google.api.field_behavior) = OPTIONAL];
}
