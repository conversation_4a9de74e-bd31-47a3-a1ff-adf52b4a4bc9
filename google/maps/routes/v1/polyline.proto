// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1;

import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Maps.Routes.V1";
option go_package = "cloud.google.com/go/maps/routes/apiv1/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "PolylineProto";
option java_package = "com.google.maps.routes.v1";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1";

// Encapsulates an encoded polyline.
message Polyline {
  // Encapsulates the type of polyline. Defaults to encoded_polyline.
  oneof polyline_type {
    // The string encoding of the polyline using the [polyline encoding
    // algorithm](https://developers.google.com/maps/documentation/utilities/polylinealgorithm).
    string encoded_polyline = 1;

    // Specifies a polyline using the [GeoJSON LineString
    // format](https://tools.ietf.org/html/rfc7946#section-3.1.4).
    google.protobuf.Struct geo_json_linestring = 2;
  }
}

// A set of values that specify the quality of the polyline.
enum PolylineQuality {
  // No polyline quality preference specified. Defaults to `OVERVIEW`.
  POLYLINE_QUALITY_UNSPECIFIED = 0;

  // Specifies a high-quality polyline - which is composed using more points
  // than `OVERVIEW`, at the cost of increased response size. Use this value
  // when you need more precision.
  HIGH_QUALITY = 1;

  // Specifies an overview polyline - which is composed using a small number of
  // points. Use this value when displaying an overview of the route. Using this
  // option has a lower request latency compared to using the
  // `HIGH_QUALITY` option.
  OVERVIEW = 2;
}

// Specifies the preferred type of polyline to be returned.
enum PolylineEncoding {
  // No polyline type preference specified. Defaults to `ENCODED_POLYLINE`.
  POLYLINE_ENCODING_UNSPECIFIED = 0;

  // Specifies a polyline encoded using the [polyline encoding
  // algorithm](https://developers.google.com/maps/documentation/utilities/polylinealgorithm).
  ENCODED_POLYLINE = 1;

  // Specifies a polyline using the [GeoJSON LineString
  // format](https://tools.ietf.org/html/rfc7946#section-3.1.4).
  GEO_JSON_LINESTRING = 2;
}
