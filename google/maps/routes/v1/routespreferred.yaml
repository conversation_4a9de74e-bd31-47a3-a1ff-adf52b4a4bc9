type: google.api.Service
config_version: 3
name: routespreferred.googleapis.com
title: Routes Preferred API

apis:
- name: google.maps.routes.v1.RoutesPreferred
- name: google.maps.routes.v1alpha.RoutesAlpha

authentication:
  rules:
  - selector: 'google.maps.routes.v1.RoutesPreferred.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/maps-platform.routespreferred
  - selector: 'google.maps.routes.v1alpha.RoutesAlpha.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/maps-platform.routespreferred
