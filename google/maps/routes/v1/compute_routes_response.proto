// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1;

import "google/maps/routes/v1/fallback_info.proto";
import "google/maps/routes/v1/route.proto";

option csharp_namespace = "Google.Maps.Routes.V1";
option go_package = "cloud.google.com/go/maps/routes/apiv1/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "ComputeRoutesResponseProto";
option java_package = "com.google.maps.routes.v1";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1";

// ComputeRoutes the response message.
message ComputeRoutesResponse {
  // Contains an array of computed routes (up to three) when you specify
  // `compute_alternatives_routes`, and contains just one route when you don't.
  // When this array contains multiple entries, the first one is the most
  // recommended route. If the array is empty, then it means no route could be
  // found.
  repeated Route routes = 1;

  // In some cases when the server is not able to compute the route results with
  // all of the input preferences, it may fallback to using a different way of
  // computation. When fallback mode is used, this field contains detailed info
  // about the fallback response. Otherwise this field is unset.
  FallbackInfo fallback_info = 2;
}
