// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1;

option csharp_namespace = "Google.Maps.Routes.V1";
option go_package = "cloud.google.com/go/maps/routes/apiv1/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "FallbackInfoProto";
option java_package = "com.google.maps.routes.v1";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1";

// Information related to how and why a fallback result was used. If this field
// is set, then it means the server used a different routing mode from your
// preferred mode as fallback.
message FallbackInfo {
  // Routing mode used for the response. If fallback was triggered, the mode
  // may be different from routing preference set in the original client
  // request.
  FallbackRoutingMode routing_mode = 1;

  // The reason why fallback response was used instead of the original response.
  // This field is only populated when the fallback mode is triggered and the
  // fallback response is returned.
  FallbackReason reason = 2;
}

// Reasons for using fallback response.
enum FallbackReason {
  // No fallback reason specified.
  FALLBACK_REASON_UNSPECIFIED = 0;

  // A server error happened while calculating routes with your preferred
  // routing mode, but we were able to return a result calculated by an
  // alternative mode.
  SERVER_ERROR = 1;

  // We were not able to finish the calculation with your preferred routing mode
  // on time, but we were able to return a result calculated by an alternative
  // mode.
  LATENCY_EXCEEDED = 2;
}

// Actual routing mode used for returned fallback response.
enum FallbackRoutingMode {
  // Not used.
  FALLBACK_ROUTING_MODE_UNSPECIFIED = 0;

  // Indicates the `TRAFFIC_UNAWARE` routing mode was used to compute the
  // response.
  FALLBACK_TRAFFIC_UNAWARE = 1;

  // Indicates the `TRAFFIC_AWARE` routing mode was used to compute the
  // response.
  FALLBACK_TRAFFIC_AWARE = 2;
}
