// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1;

import "google/maps/routes/v1/route.proto";

option csharp_namespace = "Google.Maps.Routes.V1";
option go_package = "cloud.google.com/go/maps/routes/apiv1/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "CustomRouteProto";
option java_package = "com.google.maps.routes.v1";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1";

// Encapsulates a custom route computed based on the route objective specified
// by the customer. `CustomRoute` contains a route and a route token, which can
// be passed to NavSDK to reconstruct the custom route for turn by turn
// navigation.
message CustomRoute {
  // The route considered 'best' for the input route objective.
  Route route = 11;

  // Web-safe base64 encoded route token that can be passed to NavSDK, which
  // allows NavSDK to reconstruct the route during navigation, and in the event
  // of rerouting honor the original intention when `RoutesPreferred`
  // `ComputeCustomRoutes` is called. Customers should treat this token as an
  // opaque blob.
  string token = 12;
}
