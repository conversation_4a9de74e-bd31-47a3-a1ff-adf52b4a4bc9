// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1;

import "google/maps/routes/v1/custom_route.proto";
import "google/maps/routes/v1/fallback_info.proto";

option csharp_namespace = "Google.Maps.Routes.V1";
option go_package = "cloud.google.com/go/maps/routes/apiv1/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "ComputeCustomRoutesResponseProto";
option java_package = "com.google.maps.routes.v1";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1";

// ComputeCustomRoutes response message.
message ComputeCustomRoutesResponse {
  // Encapsulates fallback info for `ComputeCustomRoutes`. `ComputeCustomRoutes`
  // performs two types of fallbacks:
  //
  // 1. If it cannot compute the route using the `routing_preference` requested
  // by the customer, it will fallback to another routing mode. In this case
  // `fallback_routing_mode` and `routing_mode_fallback_reason` are used to
  // communicate the fallback routing mode used, as well as the reason for
  // fallback.
  //
  // 2. If it cannot compute a 'best' route for the route objective specified by
  // the customer, it might fallback to another objective.
  // `fallback_route_objective` is used to communicate the fallback route
  // objective.
  message FallbackInfo {
    // RouteObjective used for the response.
    enum FallbackRouteObjective {
      // Fallback route objective unspecified.
      FALLBACK_ROUTE_OBJECTIVE_UNSPECIFIED = 0;

      // If customer requests `RateCard` and sets include_tolls to true, and
      // Google does not have toll price data for the route, the API falls back
      // to `RateCard` without considering toll price.
      FALLBACK_RATECARD_WITHOUT_TOLL_PRICE_DATA = 1;
    }

    // Routing mode used for the response. If fallback was triggered, the mode
    // may be different from routing preference set in the original client
    // request.
    FallbackRoutingMode routing_mode = 1;

    // The reason why fallback response was used instead of the original
    // response.
    // This field is only populated when the fallback mode is triggered and
    // the fallback response is returned.
    FallbackReason routing_mode_reason = 2;

    // The route objective used for the response. If fallback was triggered, the
    // objective may be different from the route objective provided in the
    // original client request.
    FallbackRouteObjective route_objective = 3;
  }

  // The ‘best’ routes for the input route objective.
  repeated CustomRoute routes = 7;

  // The fastest reference route.
  CustomRoute fastest_route = 5;

  // The shortest reference route.
  CustomRoute shortest_route = 6;

  // Fallback info for custom routes.
  FallbackInfo fallback_info = 8;
}
