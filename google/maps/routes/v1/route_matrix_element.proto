// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1;

import "google/maps/routes/v1/fallback_info.proto";
import "google/maps/routes/v1/route.proto";
import "google/protobuf/duration.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Maps.Routes.V1";
option go_package = "cloud.google.com/go/maps/routes/apiv1/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "ComputeRouteMatrixElementProto";
option java_package = "com.google.maps.routes.v1";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1";

// Encapsulates route information computed for an origin/destination pair in the
// ComputeRouteMatrix API. This proto can be streamed to the client.
message RouteMatrixElement {
  // Zero-based index of the origin in the request.
  int32 origin_index = 1;

  // Zero-based index of the destination in the request.
  int32 destination_index = 2;

  // Error status code for this element.
  google.rpc.Status status = 3;

  // Indicates whether the route was found or not. Independent of status.
  RouteMatrixElementCondition condition = 9;

  // The travel distance of the route, in meters.
  int32 distance_meters = 4;

  // The length of time needed to navigate the route. If you set the
  // `routing_preference` to `TRAFFIC_UNAWARE`, then this value is the same as
  // `static_duration`. If you set the `routing_preference` to either
  // `TRAFFIC_AWARE` or `TRAFFIC_AWARE_OPTIMAL`, then this value is calculated
  // taking traffic conditions into account.
  google.protobuf.Duration duration = 5;

  // The duration of traveling through the route without taking traffic
  // conditions into consideration.
  google.protobuf.Duration static_duration = 6;

  // Additional information about the route. For example: restriction
  // information and toll information
  RouteTravelAdvisory travel_advisory = 7;

  // In some cases when the server is not able to compute the route with the
  // given preferences for this particular origin/destination pair, it may
  // fall back to using a different mode of computation. When fallback mode is
  // used, this field contains detailed information about the fallback response.
  // Otherwise this field is unset.
  FallbackInfo fallback_info = 8;
}

// The condition of the route being returned.
enum RouteMatrixElementCondition {
  // Only used when the `status` of the element is not OK.
  ROUTE_MATRIX_ELEMENT_CONDITION_UNSPECIFIED = 0;

  // A route was found, and the corresponding information was filled out for the
  // element.
  ROUTE_EXISTS = 1;

  // No route could be found. Fields containing route information, such as
  // `distance_meters` or `duration`, will not be filled out in the element.
  ROUTE_NOT_FOUND = 2;
}
