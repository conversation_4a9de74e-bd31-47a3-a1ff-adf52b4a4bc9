# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_grpc_library",
    "java_proto_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_library",
    "py_import",
    "py_test",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "routes_proto",
    srcs = [
        "compute_custom_routes_request.proto",
        "compute_custom_routes_response.proto",
        "compute_route_matrix_request.proto",
        "compute_routes_request.proto",
        "compute_routes_response.proto",
        "custom_route.proto",
        "fallback_info.proto",
        "polyline.proto",
        "route.proto",
        "route_matrix_element.proto",
        "route_service.proto",
        "toll_passes.proto",
        "vehicle_emission_type.proto",
        "waypoint.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/geo/type:viewport_proto",
        "//google/rpc:status_proto",
        "//google/type:latlng_proto",
        "//google/type:money_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "routes_proto_with_info",
    deps = [
        ":routes_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "routes_java_proto",
    deps = [":routes_proto"],
)

java_grpc_library(
    name = "routes_java_grpc",
    srcs = [":routes_proto"],
    deps = [":routes_java_proto"],
)

java_gapic_assembly_gradle_pkg(
    name = "google-maps-routes-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        "//google/maps/routes/v1:routes_java_grpc",
        "//google/maps/routes/v1:routes_java_proto",
        "//google/maps/routes/v1:routes_proto",
    ],
)

go_proto_library(
    name = "routes_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/routes/apiv1/routespb",
    protos = [":routes_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/geo/type:viewport_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:latlng_go_proto",
        "//google/type:money_go_proto",
    ],
)

py_import(
    name = "viewport",
    srcs = [
        "//google/geo/type:viewport_py_gapic",
    ],
)

py_gapic_library(
    name = "routes_py_gapic",
    srcs = [":routes_proto"],
    grpc_service_config = "routes_grpc_service_config.json",
    opt_args = [
        "proto-plus-deps=google.geo.type",
    ],
    rest_numeric_enums = True,
    transport = "grpc+rest",
    deps = [
        ":viewport",
    ],
)

py_test(
    name = "routes_py_gapic_test",
    srcs = [
        "routes_py_gapic_pytest.py",
        "routes_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":routes_py_gapic"],
)

php_proto_library(
    name = "routes_php_proto",
    deps = [":routes_proto"],
)

ruby_proto_library(
    name = "routes_ruby_proto",
    deps = [":routes_proto"],
)

ruby_grpc_library(
    name = "routes_ruby_grpc",
    srcs = [":routes_proto"],
    deps = [":routes_ruby_proto"],
)

csharp_proto_library(
    name = "routes_csharp_proto",
    deps = [":routes_proto"],
)

csharp_grpc_library(
    name = "routes_csharp_grpc",
    srcs = [":routes_proto"],
    deps = [":routes_csharp_proto"],
)

cc_proto_library(
    name = "routes_cc_proto",
    deps = [":routes_proto"],
)

cc_grpc_library(
    name = "routes_cc_grpc",
    srcs = [":routes_proto"],
    generate_mocks = True,
    grpc_only = True,
    deps = [":routes_cc_proto"],
)
