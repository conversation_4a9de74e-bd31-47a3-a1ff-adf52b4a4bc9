// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1;

import "google/api/field_behavior.proto";
import "google/maps/routes/v1/polyline.proto";
import "google/maps/routes/v1/toll_passes.proto";
import "google/maps/routes/v1/vehicle_emission_type.proto";
import "google/maps/routes/v1/waypoint.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Maps.Routes.V1";
option go_package = "cloud.google.com/go/maps/routes/apiv1/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "ComputeRoutesRequestProto";
option java_package = "com.google.maps.routes.v1";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1";

// `ComputeRoutes` request message.
message ComputeRoutesRequest {
  // Required. Origin waypoint.
  Waypoint origin = 1;

  // Required. Destination waypoint.
  Waypoint destination = 2;

  // Optional. A set of waypoints along the route (excluding terminal points),
  // for either stopping at or passing by. Up to 25 intermediate waypoints are
  // supported.
  repeated Waypoint intermediates = 3;

  // Optional. Specifies the mode of transportation.
  RouteTravelMode travel_mode = 4;

  // Optional. Specifies how to compute the route. The server
  // attempts to use the selected routing preference to compute the route. If
  //  the routing preference results in an error or an extra long latency, then
  // an error is returned. You can specify this option only when the
  // `travel_mode` is `DRIVE` or `TWO_WHEELER`, otherwise the request fails.
  RoutingPreference routing_preference = 5;

  // Optional. Specifies your preference for the quality of the polyline.
  PolylineQuality polyline_quality = 6;

  // Optional. Specifies the preferred encoding for the polyline.
  PolylineEncoding polyline_encoding = 12;

  // Optional. The departure time. If you don't set this value, then this value
  // defaults to the time that you made the request. If you set this value to a
  // time that has already occurred, then the request fails.
  google.protobuf.Timestamp departure_time = 7;

  // Optional. Specifies whether to calculate alternate routes in addition to
  // the route. No alternative routes are returned for requests that have
  // intermediate waypoints.
  bool compute_alternative_routes = 8;

  // Optional. A set of conditions to satisfy that affect the way routes are
  // calculated.
  RouteModifiers route_modifiers = 9;

  // Optional. The BCP-47 language code, such as "en-US" or "sr-Latn". For more
  // information, see [Unicode Locale
  // Identifier](http://www.unicode.org/reports/tr35/#Unicode_locale_identifier).
  // See [Language
  // Support](https://developers.google.com/maps/faq#languagesupport) for the
  // list of supported languages. When you don't provide this value, the display
  // language is inferred from the location of the route request.
  string language_code = 10;

  // Optional. Specifies the units of measure for the display fields. This
  // includes the `instruction` field in `NavigationInstruction`. The units of
  // measure used for the route, leg, step distance, and duration are not
  // affected by this value. If you don't provide this value, then the display
  // units are inferred from the location of the request.
  Units units = 11;

  // If optimizeWaypointOrder is set to true, an attempt is made to re-order the
  // specified intermediate waypoints to minimize the overall cost of the route.
  // If any of the intermediate waypoints is via waypoint the request fails. Use
  // `ComputeRoutesResponse.Routes.optimized_intermediate_waypoint_index` to
  // find the new ordering. If `routes.optimized_intermediate_waypoint_index` is
  // not requested in the `X-Goog-FieldMask` header, the request fails. If
  // `optimize_waypoint_order` is set to false,
  // `ComputeRoutesResponse.optimized_intermediate_waypoint_index` is empty.
  bool optimize_waypoint_order = 13;
}

// Encapsulates a set of optional conditions to satisfy when calculating the
// routes.
message RouteModifiers {
  // When set to true, avoids toll roads where reasonable, giving preference to
  // routes not containing toll roads. Applies only to the `DRIVE` and
  // `TWO_WHEELER` travel modes.
  bool avoid_tolls = 1;

  // When set to true, avoids highways where reasonable, giving preference to
  // routes not containing highways. Applies only to the `DRIVE` and
  // `TWO_WHEELER` travel modes.
  bool avoid_highways = 2;

  // When set to true, avoids ferries where reasonable, giving preference to
  // routes not containing ferries.
  // Applies only to the `DRIVE` and`TWO_WHEELER` travel modes.
  bool avoid_ferries = 3;

  // When set to true, avoids navigating indoors where reasonable, giving
  // preference to routes not containing indoor navigation.
  // Applies only to the `WALK` travel mode.
  bool avoid_indoor = 4;

  // Specifies the vehicle information.
  VehicleInfo vehicle_info = 5;

  // Encapsulates information about toll passes.
  // If toll passes are provided, the API tries to return the pass price. If
  // toll passes are not provided, the API treats the toll pass as unknown and
  // tries to return the cash price.
  // Applies only to the `DRIVE` and `TWO_WHEELER` travel modes.
  repeated TollPass toll_passes = 6;
}

// Encapsulates the vehicle information, such as the vehicle emission type.
message VehicleInfo {
  // Specifies the license plate last character. Could be a digit or a letter.
  string license_plate_last_character = 1;

  // Describes the vehicle's emission type.
  // Applies only to the `DRIVE`
  // [`RouteTravelMode`][google.maps.routes.v1.RouteTravelMode].
  VehicleEmissionType emission_type = 2;
}

// A set of values used to specify the mode of travel.
enum RouteTravelMode {
  // No travel mode specified. Defaults to `DRIVE`.
  TRAVEL_MODE_UNSPECIFIED = 0;

  // Travel by passenger car.
  DRIVE = 1;

  // Travel by bicycle.
  BICYCLE = 2;

  // Travel by walking.
  WALK = 3;

  // Two-wheeled, motorized vehicle. For example, motorcycle. Note that this
  // differs from the `BICYCLE` travel mode which covers human-powered mode.
  TWO_WHEELER = 4;

  // Travel by licensed taxi, which may allow the vehicle to travel on
  // designated taxi lanes in some areas.
  //
  // TAXI mode is an experimental feature. If customer requests TAXI route in a
  // city where taxi lane data is not available, a DRIVE route is returned.
  TAXI = 5;
}

// A set of values that specify factors to take into consideration when
// calculating the route.
enum RoutingPreference {
  // No routing preference specified. Default to `TRAFFIC_AWARE`.
  ROUTING_PREFERENCE_UNSPECIFIED = 0;

  // Computes routes without taking live traffic conditions into consideration.
  // Suitable when traffic conditions don't matter or are not applicable.
  // Using this value produces the lowest latency.
  // Note: For `RouteTravelMode` DRIVE and TWO_WHEELER, the route and duration
  // chosen are based on road network and average time-independent traffic
  // conditions, not current road conditions. Consequently, routes may include
  // roads that are temporarily closed. Results for
  // a given request may vary over time due to changes
  // in the road network, updated average traffic conditions, and the
  // distributed nature of the service. Results may also vary between
  // nearly-equivalent routes at any time or frequency.
  TRAFFIC_UNAWARE = 1;

  // Calculates routes taking traffic conditions into consideration. In contrast
  // to `TRAFFIC_AWARE_OPTIMAL`, some optimizations are applied to significantly
  // reduce latency.
  TRAFFIC_AWARE = 2;

  // Calculates the routes taking traffic conditions into consideration,
  // without applying most performance optimizations. Using this value produces
  // the highest latency.
  TRAFFIC_AWARE_OPTIMAL = 3;
}

// A set of values that specify the unit of measure used in the display.
enum Units {
  // Units of measure not specified. Defaults to the unit of measure inferred
  // from the request.
  UNITS_UNSPECIFIED = 0;

  // Metric units of measure.
  METRIC = 1;

  // Imperial (English) units of measure.
  IMPERIAL = 2;
}
