# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "routes_proto",
    srcs = [
        "route_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/maps/routes/v1:routes_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "routes_java_proto",
    deps = [":routes_proto"],
)

java_grpc_library(
    name = "routes_java_grpc",
    srcs = [":routes_proto"],
    deps = [":routes_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "routes_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/routes/apiv1alpha/routespb",
    protos = [":routes_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/maps/routes/v1:routes_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "routes_moved_proto",
    srcs = [":routes_proto"],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/maps/routes/v1:routes_proto",
    ],
)

py_proto_library(
    name = "routes_py_proto",
    deps = [":routes_moved_proto"],
)

py_grpc_library(
    name = "routes_py_grpc",
    srcs = [":routes_moved_proto"],
    deps = [":routes_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "routes_php_proto",
    deps = [":routes_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "routes_ruby_proto",
    deps = [":routes_proto"],
)

ruby_grpc_library(
    name = "routes_ruby_grpc",
    srcs = [":routes_proto"],
    deps = [":routes_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "routes_csharp_proto",
    deps = [":routes_proto"],
)

csharp_grpc_library(
    name = "routes_csharp_grpc",
    srcs = [":routes_proto"],
    deps = [":routes_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
