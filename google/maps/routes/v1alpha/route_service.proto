// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routes.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/maps/routes/v1/compute_custom_routes_request.proto";
import "google/maps/routes/v1/compute_custom_routes_response.proto";
import "google/maps/routes/v1/compute_route_matrix_request.proto";
import "google/maps/routes/v1/compute_routes_request.proto";
import "google/maps/routes/v1/compute_routes_response.proto";
import "google/maps/routes/v1/route_matrix_element.proto";

option csharp_namespace = "Google.Maps.Routes.V1Alpha";
option go_package = "cloud.google.com/go/maps/routes/apiv1alpha/routespb;routespb";
option java_multiple_files = true;
option java_outer_classname = "RoutesServiceAlphaProto";
option java_package = "com.google.maps.routes.v1alpha";
option objc_class_prefix = "GMRS";
option php_namespace = "Google\\Maps\\Routes\\V1alpha";

// The Routes Preferred API.
service RoutesAlpha {
  option (google.api.default_host) = "routespreferred.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/maps-platform.routespreferred";

  // Returns the primary route along with optional alternate routes, given a set
  // of terminal and intermediate waypoints.
  //
  // **NOTE:** This method requires that you specify a response field mask in
  // the input. You can provide the response field mask by using the URL
  // parameter `$fields` or `fields`, or by using the HTTP/gRPC header
  // `X-Goog-FieldMask` (see the [available URL parameters and
  // headers](https://cloud.google.com/apis/docs/system-parameters). The value
  // is a comma separated list of field paths. See this detailed documentation
  // about [how to construct the field
  // paths](https://github.com/protocolbuffers/protobuf/blob/master/src/google/protobuf/field_mask.proto).
  //
  // For example, in this method:
  //
  // * Field mask of all available fields (for manual inspection):
  //   `X-Goog-FieldMask: *`
  // * Field mask of Route-level duration, distance, and polyline (an example
  // production setup):
  //   `X-Goog-FieldMask:
  //   routes.duration,routes.distanceMeters,routes.polyline.encodedPolyline`
  //
  // Google discourages the use of the wildcard (`*`) response field mask, or
  // specifying the field mask at the top level (`routes`), because:
  //
  // * Selecting only the fields that you need helps our server save computation
  // cycles, allowing us to return the result to you with a lower latency.
  // * Selecting only the fields that you need in your production job ensures
  // stable latency performance. We might add more response fields in the
  // future, and those new fields might require extra computation time. If you
  // select all fields, or if you select all fields at the top level, then you
  // might experience performance degradation because any new field we add will
  // be automatically included in the response.
  // * Selecting only the fields that you need results in a smaller response
  // size, and thus higher network throughput.
  rpc ComputeRoutes(google.maps.routes.v1.ComputeRoutesRequest)
      returns (google.maps.routes.v1.ComputeRoutesResponse) {
    option (google.api.http) = {
      post: "/v1alpha:computeRoutes"
      body: "*"
    };
  }

  // Takes in a list of origins and destinations and returns a stream containing
  // route information for each combination of origin and destination.
  //
  // **NOTE:** This method requires that you specify a response field mask in
  // the input. You can provide the response field mask by using the URL
  // parameter `$fields` or `fields`, or by using the HTTP/gRPC header
  // `X-Goog-FieldMask` (see the [available URL parameters and
  // headers](https://cloud.google.com/apis/docs/system-parameters). The value
  // is a comma separated list of field paths. See this detailed documentation
  // about [how to construct the field
  // paths](https://github.com/protocolbuffers/protobuf/blob/master/src/google/protobuf/field_mask.proto).
  //
  // For example, in this method:
  //
  // * Field mask of all available fields (for manual inspection):
  //   `X-Goog-FieldMask: *`
  // * Field mask of route durations, distances, element status, condition, and
  //   element indices (an example production setup):
  //   `X-Goog-FieldMask:
  //   originIndex,destinationIndex,status,condition,distanceMeters,duration`
  //
  // It is critical that you include `status` in your field mask as otherwise
  // all messages will appear to be OK. Google discourages the use of the
  // wildcard (`*`) response field mask, because:
  //
  // * Selecting only the fields that you need helps our server save computation
  // cycles, allowing us to return the result to you with a lower latency.
  // * Selecting only the fields that you need in your production job ensures
  // stable latency performance. We might add more response fields in the
  // future, and those new fields might require extra computation time. If you
  // select all fields, or if you select all fields at the top level, then you
  // might experience performance degradation because any new field we add will
  // be automatically included in the response.
  // * Selecting only the fields that you need results in a smaller response
  // size, and thus higher network throughput.
  rpc ComputeRouteMatrix(google.maps.routes.v1.ComputeRouteMatrixRequest)
      returns (stream google.maps.routes.v1.RouteMatrixElement) {
    option (google.api.http) = {
      post: "/v1alpha:computeRouteMatrix"
      body: "*"
    };
  }

  // Given a set of terminal and intermediate waypoints, and a route objective,
  // computes the best route for the route objective. Also returns fastest route
  // and shortest route as reference routes.
  //
  // **NOTE:** This method requires that you specify a response field mask in
  // the input. You can provide the response field mask by using the URL
  // parameter `$fields` or `fields`, or by using the HTTP/gRPC header
  // `X-Goog-FieldMask` (see the [available URL parameters and
  // headers](https://cloud.google.com/apis/docs/system-parameters). The value
  // is a comma separated list of field paths. See this detailed documentation
  // about [how to construct the field
  // paths](https://github.com/protocolbuffers/protobuf/blob/master/src/google/protobuf/field_mask.proto).
  //
  // For example, in this method:
  //
  // * Field mask of all available fields (for manual inspection):
  //   `X-Goog-FieldMask: *`
  // * Field mask of route distances, durations, token and toll info:
  //   `X-Goog-FieldMask:
  //   routes.route.distanceMeters,routes.route.duration,routes.token,routes.route.travelAdvisory.tollInfo`
  //
  // Google discourages the use of the wildcard (`*`) response field mask, or
  // specifying the field mask at the top level (`routes`), because:
  //
  // * Selecting only the fields that you need helps our server save computation
  // cycles, allowing us to return the result to you with a lower latency.
  // * Selecting only the fields that you need in your production job ensures
  // stable latency performance. We might add more response fields in the
  // future, and those new fields might require extra computation time. If you
  // select all fields, or if you select all fields at the top level, then you
  // might experience performance degradation because any new field we add will
  // be automatically included in the response.
  // * Selecting only the fields that you need results in a smaller response
  // size, and thus higher network throughput.
  rpc ComputeCustomRoutes(google.maps.routes.v1.ComputeCustomRoutesRequest)
      returns (google.maps.routes.v1.ComputeCustomRoutesResponse) {
    option (google.api.http) = {
      post: "/v1alpha:computeCustomRoutes"
      body: "*"
    };
  }
}
