type: google.api.Service
config_version: 3
name: routeoptimization.googleapis.com
title: Route Optimization API

apis:
- name: google.longrunning.Operations
- name: google.maps.routeoptimization.v1.RouteOptimization

types:
- name: google.maps.routeoptimization.v1.BatchOptimizeToursMetadata
- name: google.maps.routeoptimization.v1.BatchOptimizeToursResponse

documentation:
  summary: |-
    The Route Optimization API assigns tasks and routes to a vehicle fleet,
    optimizing against the objectives and constraints that you supply for your
    transportation goals.

http:
  rules:
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=projects/*/locations/*/operations/*}'

authentication:
  rules:
  - selector: 'google.longrunning.Operations.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.maps.routeoptimization.v1.RouteOptimization.BatchOptimizeTours
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.maps.routeoptimization.v1.RouteOptimization.OptimizeTours
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1546507
  documentation_uri: https://developers.google.com/maps/documentation/route-optimization
  api_short_name: routeoptimization
  github_label: 'api: routeoptimization'
  doc_tag_prefix: routeoptimization
  organization: GEO
  library_settings:
  - version: google.maps.routeoptimization.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://developers.google.com/maps/documentation/route-optimization/reference/rpc
  rest_reference_documentation_uri: https://developers.google.com/maps/documentation/route-optimization/reference/rest
