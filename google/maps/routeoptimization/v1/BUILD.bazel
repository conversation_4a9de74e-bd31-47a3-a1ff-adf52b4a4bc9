# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel
# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.
# buildifier: disable=load-on-top
# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])
##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")
proto_library(
    name = "routeoptimization_proto",
    srcs = [
        "route_optimization_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/longrunning:operations_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)
proto_library_with_info(
    name = "routeoptimization_proto_with_info",
    deps = [
        ":routeoptimization_proto",
        "//google/cloud:common_resources_proto",
    ],
)
##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)
java_proto_library(
    name = "routeoptimization_java_proto",
    deps = [":routeoptimization_proto"],
)
java_grpc_library(
    name = "routeoptimization_java_grpc",
    srcs = [":routeoptimization_proto"],
    deps = [":routeoptimization_java_proto"],
)
java_gapic_library(
    name = "routeoptimization_java_gapic",
    srcs = [":routeoptimization_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "routeoptimization_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "routeoptimization_v1.yaml",
    test_deps = [
        ":routeoptimization_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":routeoptimization_java_proto",
        "//google/api:api_java_proto",
    ],
)
java_gapic_test(
    name = "routeoptimization_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.routeoptimization.v1.RouteOptimizationClientHttpJsonTest",
        "com.google.maps.routeoptimization.v1.RouteOptimizationClientTest",
    ],
    runtime_deps = [":routeoptimization_java_gapic_test"],
)
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-maps-routeoptimization-v1-java",
    transport = "grpc+rest",
    deps = [
        ":routeoptimization_java_gapic",
        ":routeoptimization_java_grpc",
        ":routeoptimization_java_proto",
        ":routeoptimization_proto",
    ],
    include_samples = True,
)
##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)
go_proto_library(
    name = "routeoptimization_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/routeoptimization/apiv1/routeoptimizationpb",
    protos = [":routeoptimization_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:latlng_go_proto",
    ],
)
go_gapic_library(
    name = "routeoptimization_go_gapic",
    srcs = [":routeoptimization_proto_with_info"],
    grpc_service_config = "routeoptimization_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/routeoptimization/apiv1;routeoptimization",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "routeoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":routeoptimization_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)
# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-maps-routeoptimization-v1-go",
    deps = [
        ":routeoptimization_go_gapic",
        ":routeoptimization_go_gapic_srcjar-test.srcjar",
        ":routeoptimization_go_gapic_srcjar-metadata.srcjar",
        ":routeoptimization_go_gapic_srcjar-snippets.srcjar",
        ":routeoptimization_go_proto",
    ],
)
##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)
py_gapic_library(
    name = "routeoptimization_py_gapic",
    srcs = [":routeoptimization_proto"],
    grpc_service_config = "routeoptimization_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "routeoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)
py_test(
    name = "routeoptimization_py_gapic_test",
    srcs = [
        "routeoptimization_py_gapic_pytest.py",
        "routeoptimization_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":routeoptimization_py_gapic"],
)
# Open Source Packages
py_gapic_assembly_pkg(
    name = "maps-routeoptimization-v1-py",
    deps = [
        ":routeoptimization_py_gapic",
    ],
)
##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)
php_proto_library(
    name = "routeoptimization_php_proto",
    deps = [":routeoptimization_proto"],
)
php_gapic_library(
    name = "routeoptimization_php_gapic",
    srcs = [":routeoptimization_proto_with_info"],
    grpc_service_config = "routeoptimization_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "routeoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":routeoptimization_php_proto",
    ],
)
# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-maps-routeoptimization-v1-php",
    deps = [
        ":routeoptimization_php_gapic",
        ":routeoptimization_php_proto",
    ],
)
##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)
nodejs_gapic_library(
    name = "routeoptimization_nodejs_gapic",
    package_name = "@googlemaps/routeoptimization",
    src = ":routeoptimization_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "routeoptimization_v1_grpc_service_config.json",
    package = "google.maps.routeoptimization.v1",
    rest_numeric_enums = True,
    service_yaml = "routeoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)
nodejs_gapic_assembly_pkg(
    name = "maps-routeoptimization-v1-nodejs",
    deps = [
        ":routeoptimization_nodejs_gapic",
        ":routeoptimization_proto",
    ],
)
##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)
ruby_proto_library(
    name = "routeoptimization_ruby_proto",
    deps = [":routeoptimization_proto"],
)
ruby_grpc_library(
    name = "routeoptimization_ruby_grpc",
    srcs = [":routeoptimization_proto"],
    deps = [":routeoptimization_ruby_proto"],
)
ruby_cloud_gapic_library(
    name = "routeoptimization_ruby_gapic",
    srcs = [":routeoptimization_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-maps-routeoptimization-v1",
    ],
    grpc_service_config = "routeoptimization_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "routeoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":routeoptimization_ruby_grpc",
        ":routeoptimization_ruby_proto",
    ],
)
# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-maps-routeoptimization-v1-ruby",
    deps = [
        ":routeoptimization_ruby_gapic",
        ":routeoptimization_ruby_grpc",
        ":routeoptimization_ruby_proto",
    ],
)
##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)
csharp_proto_library(
    name = "routeoptimization_csharp_proto",
    extra_opts = [],
    deps = [":routeoptimization_proto"],
)
csharp_grpc_library(
    name = "routeoptimization_csharp_grpc",
    srcs = [":routeoptimization_proto"],
    deps = [":routeoptimization_csharp_proto"],
)
csharp_gapic_library(
    name = "routeoptimization_csharp_gapic",
    srcs = [":routeoptimization_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "routeoptimization_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "routeoptimization_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":routeoptimization_csharp_grpc",
        ":routeoptimization_csharp_proto",
    ],
)
# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-maps-routeoptimization-v1-csharp",
    deps = [
        ":routeoptimization_csharp_gapic",
        ":routeoptimization_csharp_grpc",
        ":routeoptimization_csharp_proto",
    ],
)
##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)
cc_proto_library(
    name = "routeoptimization_cc_proto",
    deps = [":routeoptimization_proto"],
)
cc_grpc_library(
    name = "routeoptimization_cc_grpc",
    srcs = [":routeoptimization_proto"],
    grpc_only = True,
    deps = [":routeoptimization_cc_proto"],
)