// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.aerialview.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/duration.proto";
import "google/type/date.proto";

option csharp_namespace = "Google.Maps.AerialView.V1";
option go_package = "cloud.google.com/go/maps/aerialview/apiv1/aerialviewpb;aerialviewpb";
option java_multiple_files = true;
option java_outer_classname = "AerialViewProto";
option java_package = "com.google.maps.aerialview.v1";
option objc_class_prefix = "GGMPV1B";
option php_namespace = "Google\\Maps\\AerialView\\V1";
option ruby_package = "Google::Maps::AerialView::V1";

// Service definition for the Aerial View API.
service AerialView {
  option (google.api.default_host) = "aerialview.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Adds an address to the renderer's queue if a video hasn't already been
  // rendered. Otherwise, returns metadata about the video.
  rpc RenderVideo(RenderVideoRequest) returns (RenderVideoResponse) {
    option (google.api.http) = {
      post: "/v1/videos:renderVideo"
      body: "*"
    };
    option (google.api.method_signature) = "address";
  }

  // Fetches a video given its address or videoId. The response will either be
  // a video with a set of playback URIs for ACTIVE videos, a PROCESSING state
  // for pending videos, or a 404 error if the video does not exist. Receiving a
  // video is a billable event, so callers of this method should be ready to use
  // the returned URIs at the time of request.
  rpc LookupVideo(LookupVideoRequest) returns (Video) {
    option (google.api.http) = {
      get: "/v1/videos:lookupVideo"
    };
  }
}

// An object that encapsulates all of the data about a video.
message Video {
  // The different states a video can be in.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // The video is currently processing.
    PROCESSING = 1;

    // The video has finished rendering, and can be viewed through
    // `LookupVideo`.
    ACTIVE = 2;

    // The video has failed to render.
    FAILED = 3;
  }

  // A mapping of media types to their URIs.
  // This field is only included for `ACTIVE` videos.
  // The key is an enum value from `MediaFormat`.
  map<string, Uris> uris = 1;

  // Current state of the render request.
  State state = 2;

  // Contains the video's metadata, only set if the state is `ACTIVE`.
  VideoMetadata metadata = 3;
}

// Contains all the uris for a given video format.
message Uris {
  // A signed short-lived URI for the media in a landscape orientation.
  string landscape_uri = 1;

  // A signed short-lived URI for the media in a portrait orientation.
  string portrait_uri = 2;
}

// Contains metadata about a video, such as its videoId and duration.
message VideoMetadata {
  // An ID for the video, and the recommended way to retrieve a video.
  string video_id = 1;

  // The date at which the imagery used in the video was captured.
  // This will be at a month-level granularity.
  google.type.Date capture_date = 2;

  // The length of the video.
  google.protobuf.Duration duration = 3;
}

// Request message for `AerialView.RenderVideo`.
message RenderVideoRequest {
  // Required. A US postal address for the location to be rendered in the video.
  string address = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response message for `AerialView.RenderVideo`.
message RenderVideoResponse {
  // Current state of the render request.
  Video.State state = 1;

  // Contains the video's metadata, only set if the state is `ACTIVE`.
  VideoMetadata metadata = 2;
}

// Request message for `AerialView.LookupVideo`.
message LookupVideoRequest {
  // Required.
  // A key used to look-up a video.
  oneof key {
    // An ID returned from `RenderVideo`.
    string video_id = 1;

    // A US postal address.
    string address = 2;
  }
}
