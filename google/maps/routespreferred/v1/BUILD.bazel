# This file was automatically generated by BuildFileGenerator

load("//google/maps:postprocessing.bzl", "maps_assembly_pkg")

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

alias(
    name = "routes_proto",
    actual = "//google/maps/routes/v1:routes_proto",
)

alias(
    name = "routes_proto_with_info",
    actual = "//google/maps/routes/v1:routes_proto_with_info",
)

java_gapic_library(
    name = "routespreferred_java_gapic",
    srcs = [":routes_proto_with_info"],
    gapic_yaml = "routespreferred_gapic.yaml",
    grpc_service_config = "//google/maps/routes/v1:routes_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "routespreferred_v1.yaml",
    test_deps = [
        "//google/maps/routes/v1:routes_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        "//google/maps/routes/v1:routes_java_proto",
    ],
)

java_gapic_test(
    name = "routespreferred_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.routespreferred.v1.RoutesPreferredClientHttpJsonTest",
        "com.google.maps.routespreferred.v1.RoutesPreferredClientTest",
    ],
    runtime_deps = [":routespreferred_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-maps-routespreferred-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":routespreferred_java_gapic",
        "//google/maps/routes/v1:routes_java_grpc",  # needed for test mocks
    ],
)

maps_assembly_pkg(
    name = "google-maps-routespreferred-v1-java-postprocess",
    srcs = [":google-maps-routespreferred-v1-java"],
    language = "java",
)

go_gapic_library(
    name = "routespreferred_go_gapic",
    srcs = [":routes_proto_with_info"],
    grpc_service_config = "",
    importpath = "developers.google.com/maps/go/routespreferred/v1;routes",
    rest_numeric_enums = True,
    service_yaml = "routespreferred_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/maps/routes/v1:routes_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapic-maps-routespreferred-v1-go",
    deps = [
        ":routespreferred_go_gapic",
        ":routespreferred_go_gapic_srcjar-snippets.srcjar",
        ":routespreferred_go_gapic_srcjar-test.srcjar",
        "//google/maps/routes/v1:routes_go_proto",
    ],
)
