// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "RouteLabelProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// Labels for the [`Route`][google.maps.routing.v2.Route] that are useful to
// identify specific properties of the route to compare against others.
enum RouteLabel {
  // Default - not used.
  ROUTE_LABEL_UNSPECIFIED = 0;

  // The default "best" route returned for the route computation.
  DEFAULT_ROUTE = 1;

  // An alternative to the default "best" route. Routes like this will be
  // returned when
  // [`compute_alternative_routes`][google.maps.routing.v2.ComputeRoutesRequest.compute_alternative_routes]
  // is specified.
  DEFAULT_ROUTE_ALTERNATE = 2;

  // Fuel efficient route. Routes labeled with this value are determined to be
  // optimized for Eco parameters such as fuel consumption.
  FUEL_EFFICIENT = 3;

  // Shorter travel distance route. This is an experimental feature.
  SHORTER_DISTANCE = 4;
}
