# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "routing_proto",
    srcs = [
        "fallback_info.proto",
        "geocoding_results.proto",
        "localized_time.proto",
        "location.proto",
        "maneuver.proto",
        "navigation_instruction.proto",
        "polyline.proto",
        "polyline_details.proto",
        "route.proto",
        "route_label.proto",
        "route_modifiers.proto",
        "route_travel_mode.proto",
        "routes_service.proto",
        "routing_preference.proto",
        "speed_reading_interval.proto",
        "toll_info.proto",
        "toll_passes.proto",
        "traffic_model.proto",
        "transit.proto",
        "transit_preferences.proto",
        "units.proto",
        "vehicle_emission_type.proto",
        "vehicle_info.proto",
        "waypoint.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/geo/type:viewport_proto",
        "//google/rpc:status_proto",
        "//google/type:latlng_proto",
        "//google/type:localized_text_proto",
        "//google/type:money_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "routing_proto_with_info",
    deps = [
        ":routing_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "routing_java_proto",
    deps = [":routing_proto"],
)

java_grpc_library(
    name = "routing_java_grpc",
    srcs = [":routing_proto"],
    deps = [":routing_java_proto"],
)

java_gapic_library(
    name = "routing_java_gapic",
    srcs = [":routing_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "routes_v2.yaml",
    test_deps = [
        ":routing_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":routing_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "routing_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.routing.v2.RoutesClientHttpJsonTest",
        "com.google.maps.routing.v2.RoutesClientTest",
    ],
    runtime_deps = [":routing_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-maps-routing-v2-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":routing_java_gapic",
        ":routing_java_grpc",
        ":routing_java_proto",
        ":routing_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "routing_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/routing/apiv2/routingpb",
    protos = [":routing_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/geo/type:viewport_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:latlng_go_proto",
        "//google/type:localized_text_go_proto",
        "//google/type:money_go_proto",
    ],
)

go_gapic_library(
    name = "routing_go_gapic",
    srcs = [":routing_proto_with_info"],
    grpc_service_config = "library_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/routing/apiv2;routing",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "routes_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":routing_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-maps-routing-v2-go",
    deps = [
        ":routing_go_gapic",
        ":routing_go_gapic_srcjar-metadata.srcjar",
        ":routing_go_gapic_srcjar-snippets.srcjar",
        ":routing_go_gapic_srcjar-test.srcjar",
        ":routing_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
    "py_test",
)

py_import(
    name = "viewport",
    srcs = [
        "//google/geo/type:viewport_py_gapic",
    ],
)

py_gapic_library(
    name = "routing_py_gapic",
    srcs = [":routing_proto"],
    grpc_service_config = "library_grpc_service_config.json",
    opt_args = [
        "proto-plus-deps=google.geo.type",
    ],
    rest_numeric_enums = True,
    service_yaml = "routes_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":viewport",
    ],
)

py_test(
    name = "routing_py_gapic_test",
    srcs = [
        "routing_py_gapic_pytest.py",
        "routing_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":routing_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "maps-routing-v2-py",
    deps = [
        ":routing_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "routing_php_proto",
    deps = [":routing_proto"],
)

php_gapic_library(
    name = "routing_php_gapic",
    srcs = [":routing_proto_with_info"],
    grpc_service_config = "library_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "routes_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":routing_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-maps-routing-v2-php",
    deps = [
        ":routing_php_gapic",
        ":routing_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "routing_nodejs_gapic",
    package_name = "@googlemaps/routing",
    src = ":routing_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "library_grpc_service_config.json",
    package = "google.maps.routing.v2",
    rest_numeric_enums = True,
    service_yaml = "routes_v2.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "maps-routing-v2-nodejs",
    deps = [
        ":routing_nodejs_gapic",
        ":routing_proto",
        "//google/geo/type:viewport_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "routing_ruby_proto",
    deps = [":routing_proto"],
)

ruby_grpc_library(
    name = "routing_ruby_grpc",
    srcs = [":routing_proto"],
    deps = [":routing_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "routing_ruby_gapic",
    srcs = [":routing_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=routes.googleapis.com",
        "ruby-cloud-api-shortname=routes",
        "ruby-cloud-gem-name=google-maps-routing-v2",
        "ruby-cloud-product-url=https://developers.google.com/maps/documentation/",
    ],
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Routing includes the process of calculating a path along the road network, complete with an Estimated Time of Arrival (ETA).",
    ruby_cloud_title = "Routes V2",
    service_yaml = "routes_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":routing_ruby_grpc",
        ":routing_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-maps-routing-v2-ruby",
    deps = [
        ":routing_ruby_gapic",
        ":routing_ruby_grpc",
        ":routing_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "routing_csharp_proto",
    extra_opts = [],
    deps = [":routing_proto"],
)

csharp_grpc_library(
    name = "routing_csharp_grpc",
    srcs = [":routing_proto"],
    deps = [":routing_csharp_proto"],
)

csharp_gapic_library(
    name = "routing_csharp_gapic",
    srcs = [":routing_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "library_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "routes_v2.yaml",
    transport = "grpc+rest",
    deps = [
        ":routing_csharp_grpc",
        ":routing_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-maps-routing-v2-csharp",
    deps = [
        ":routing_csharp_gapic",
        ":routing_csharp_grpc",
        ":routing_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "routing_cc_proto",
    deps = [":routing_proto"],
)

cc_grpc_library(
    name = "routing_cc_grpc",
    srcs = [":routing_proto"],
    grpc_only = True,
    deps = [":routing_cc_proto"],
)
