// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

import "google/maps/routing/v2/location.proto";
import "google/type/localized_text.proto";

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "TransitProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// A transit agency that operates a transit line.
message TransitAgency {
  // The name of this transit agency.
  string name = 1;

  // The transit agency's locale-specific formatted phone number.
  string phone_number = 2;

  // The transit agency's URI.
  string uri = 3;
}

// Contains information about the transit line used in this step.
message TransitLine {
  // The transit agency (or agencies) that operates this transit line.
  repeated TransitAgency agencies = 1;

  // The full name of this transit line, For example, "8 Avenue Local".
  string name = 2;

  // the URI for this transit line as provided by the transit agency.
  string uri = 3;

  // The color commonly used in signage for this line. Represented in
  // hexadecimal.
  string color = 4;

  // The URI for the icon associated with this line.
  string icon_uri = 5;

  // The short name of this transit line. This name will normally be a line
  // number, such as "M7" or "355".
  string name_short = 6;

  // The color commonly used in text on signage for this line. Represented in
  // hexadecimal.
  string text_color = 7;

  // The type of vehicle that operates on this transit line.
  TransitVehicle vehicle = 8;
}

// Information about a transit stop.
message TransitStop {
  // The name of the transit stop.
  string name = 1;

  // The location of the stop expressed in latitude/longitude coordinates.
  Location location = 2;
}

// Information about a vehicle used in transit routes.
message TransitVehicle {
  // The type of vehicles for transit routes.
  enum TransitVehicleType {
    // Unused.
    TRANSIT_VEHICLE_TYPE_UNSPECIFIED = 0;

    // Bus.
    BUS = 1;

    // A vehicle that operates on a cable, usually on the ground. Aerial cable
    // cars may be of the type `GONDOLA_LIFT`.
    CABLE_CAR = 2;

    // Commuter rail.
    COMMUTER_TRAIN = 3;

    // Ferry.
    FERRY = 4;

    // A vehicle that is pulled up a steep incline by a cable. A Funicular
    // typically consists of two cars, with each car acting as a counterweight
    // for the other.
    FUNICULAR = 5;

    // An aerial cable car.
    GONDOLA_LIFT = 6;

    // Heavy rail.
    HEAVY_RAIL = 7;

    // High speed train.
    HIGH_SPEED_TRAIN = 8;

    // Intercity bus.
    INTERCITY_BUS = 9;

    // Long distance train.
    LONG_DISTANCE_TRAIN = 10;

    // Light rail transit.
    METRO_RAIL = 11;

    // Monorail.
    MONORAIL = 12;

    // All other vehicles.
    OTHER = 13;

    // Rail.
    RAIL = 14;

    // Share taxi is a kind of bus with the ability to drop off and pick up
    // passengers anywhere on its route.
    SHARE_TAXI = 15;

    // Underground light rail.
    SUBWAY = 16;

    // Above ground light rail.
    TRAM = 17;

    // Trolleybus.
    TROLLEYBUS = 18;
  }

  // The name of this vehicle, capitalized.
  google.type.LocalizedText name = 1;

  // The type of vehicle used.
  TransitVehicleType type = 2;

  // The URI for an icon associated with this vehicle type.
  string icon_uri = 3;

  // The URI for the icon associated with this vehicle type, based on the local
  // transport signage.
  string local_icon_uri = 4;
}
