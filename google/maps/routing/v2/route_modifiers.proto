// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

import "google/maps/routing/v2/toll_passes.proto";
import "google/maps/routing/v2/vehicle_info.proto";

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "RouteModifiersProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// Encapsulates a set of optional conditions to satisfy when calculating the
// routes.
message RouteModifiers {
  // When set to true, avoids toll roads where reasonable, giving preference to
  // routes not containing toll roads. Applies only to the `DRIVE` and
  // `TWO_WHEELER` [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode].
  bool avoid_tolls = 1;

  // When set to true, avoids highways where reasonable, giving preference to
  // routes not containing highways. Applies only to the `DRIVE` and
  // `TWO_WHEELER` [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode].
  bool avoid_highways = 2;

  // When set to true, avoids ferries where reasonable, giving preference to
  // routes not containing ferries. Applies only to the `DRIVE` and`TWO_WHEELER`
  // [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode].
  bool avoid_ferries = 3;

  // When set to true, avoids navigating indoors where reasonable, giving
  // preference to routes not containing indoor navigation. Applies only to the
  // `WALK` [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode].
  bool avoid_indoor = 4;

  // Specifies the vehicle information.
  VehicleInfo vehicle_info = 5;

  // Encapsulates information about toll passes.
  // If toll passes are provided, the API tries to return the pass price. If
  // toll passes are not provided, the API treats the toll pass as unknown and
  // tries to return the cash price.
  // Applies only to the `DRIVE` and `TWO_WHEELER`
  // [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode].
  repeated TollPass toll_passes = 6;
}
