// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

import "google/type/money.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "TollInfoProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// Encapsulates toll information on a [`Route`][google.maps.routing.v2.Route] or
// on a [`RouteLeg`][google.maps.routing.v2.RouteLeg].
message TollInfo {
  // The monetary amount of tolls for the corresponding
  // [`Route`][google.maps.routing.v2.Route] or
  // [`RouteLeg`][google.maps.routing.v2.RouteLeg]. This list contains a money
  // amount for each currency that is expected to be charged by the toll
  // stations. Typically this list will contain only one item for routes with
  // tolls in one currency. For international trips, this list may contain
  // multiple items to reflect tolls in different currencies.
  repeated google.type.Money estimated_price = 1;
}
