// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "RouteTravelModeProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// A set of values used to specify the mode of travel.
// NOTE: `WALK`, `BICYCLE`, and `TWO_WHEELER` routes are in beta and might
// sometimes be missing clear sidewalks, pedestrian paths, or bicycling paths.
// You must display this warning to the user for all walking, bicycling, and
// two-wheel routes that you display in your app.
enum RouteTravelMode {
  // No travel mode specified. Defaults to `DRIVE`.
  TRAVEL_MODE_UNSPECIFIED = 0;

  // Travel by passenger car.
  DRIVE = 1;

  // Travel by bicycle.
  BICYCLE = 2;

  // Travel by walking.
  WALK = 3;

  // Two-wheeled, motorized vehicle. For example, motorcycle. Note that this
  // differs from the `BICYCLE` travel mode which covers human-powered mode.
  TWO_WHEELER = 4;

  // Travel by public transit routes, where available.
  TRANSIT = 7;
}
