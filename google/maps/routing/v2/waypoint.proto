// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

import "google/maps/routing/v2/location.proto";

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "WaypointProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// Encapsulates a waypoint. Waypoints mark both the beginning and end of a
// route, and include intermediate stops along the route.
message Waypoint {
  // Different ways to represent a location.
  oneof location_type {
    // A point specified using geographic coordinates, including an optional
    // heading.
    Location location = 1;

    // The POI Place ID associated with the waypoint.
    string place_id = 2;

    // Human readable address or a plus code.
    // See https://plus.codes for details.
    string address = 7;
  }

  // Marks this waypoint as a milestone rather a stopping point. For
  // each non-via waypoint in the request, the response appends an entry to the
  // [`legs`][google.maps.routing.v2.Route.legs]
  // array to provide the details for stopovers on that leg of the trip. Set
  // this value to true when you want the route to pass through this waypoint
  // without stopping over. Via waypoints don't cause an entry to be added to
  // the `legs` array, but they do route the journey through the waypoint. You
  // can only set this value on waypoints that are intermediates. The request
  // fails if you set this field on terminal waypoints. If
  // `ComputeRoutesRequest.optimize_waypoint_order` is set to true then this
  // field cannot be set to true; otherwise, the request fails.
  bool via = 3;

  // Indicates that the waypoint is meant for vehicles to stop at, where the
  // intention is to either pickup or drop-off. When you set this value, the
  // calculated route won't include non-`via` waypoints on roads that are
  // unsuitable for pickup and drop-off. This option works only for `DRIVE` and
  // `TWO_WHEELER` travel modes, and when the `location_type` is
  // [`Location`][google.maps.routing.v2.Location].
  bool vehicle_stopover = 4;

  // Indicates that the location of this waypoint is meant to have a preference
  // for the vehicle to stop at a particular side of road. When you set this
  // value, the route will pass through the location so that the vehicle can
  // stop at the side of road that the location is biased towards from the
  // center of the road. This option works only for `DRIVE` and `TWO_WHEELER`
  // [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode].
  bool side_of_road = 5;
}
