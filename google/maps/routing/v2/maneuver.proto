// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "ManeuverProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// A set of values that specify the navigation action to take for the current
// step (for example, turn left, merge, or straight).
enum Maneuver {
  // Not used.
  MANEUVER_UNSPECIFIED = 0;

  // Turn slightly to the left.
  TURN_SLIGHT_LEFT = 1;

  // Turn sharply to the left.
  TURN_SHARP_LEFT = 2;

  // Make a left u-turn.
  UTURN_LEFT = 3;

  // Turn left.
  TURN_LEFT = 4;

  // Turn slightly to the right.
  TURN_SLIGHT_RIGHT = 5;

  // Turn sharply to the right.
  TURN_SHARP_RIGHT = 6;

  // Make a right u-turn.
  UTURN_RIGHT = 7;

  // Turn right.
  TURN_RIGHT = 8;

  // Go straight.
  STRAIGHT = 9;

  // Take the left ramp.
  RAMP_LEFT = 10;

  // Take the right ramp.
  RAMP_RIGHT = 11;

  // Merge into traffic.
  MERGE = 12;

  // Take the left fork.
  FORK_LEFT = 13;

  // Take the right fork.
  FORK_RIGHT = 14;

  // Take the ferry.
  FERRY = 15;

  // Take the train leading onto the ferry.
  FERRY_TRAIN = 16;

  // Turn left at the roundabout.
  ROUNDABOUT_LEFT = 17;

  // Turn right at the roundabout.
  ROUNDABOUT_RIGHT = 18;

  // Initial maneuver.
  DEPART = 19;

  // Used to indicate a street name change.
  NAME_CHANGE = 20;
}
