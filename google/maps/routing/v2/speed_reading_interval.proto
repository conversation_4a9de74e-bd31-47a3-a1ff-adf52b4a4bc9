// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "SpeedReadingIntervalProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// Traffic density indicator on a contiguous segment of a polyline or path.
// Given a path with points P_0, P_1, ... , P_N (zero-based index), the
// `SpeedReadingInterval` defines an interval and describes its traffic using
// the following categories.
message SpeedReadingInterval {
  // The classification of polyline speed based on traffic data.
  enum Speed {
    // Default value. This value is unused.
    SPEED_UNSPECIFIED = 0;

    // Normal speed, no slowdown is detected.
    NORMAL = 1;

    // Slowdown detected, but no traffic jam formed.
    SLOW = 2;

    // Traffic jam detected.
    TRAFFIC_JAM = 3;
  }

  // The starting index of this interval in the polyline.
  optional int32 start_polyline_point_index = 1;

  // The ending index of this interval in the polyline.
  optional int32 end_polyline_point_index = 2;

  oneof speed_type {
    // Traffic speed in this interval.
    Speed speed = 3;
  }
}
