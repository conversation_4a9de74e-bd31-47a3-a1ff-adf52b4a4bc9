// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

import "google/geo/type/viewport.proto";
import "google/maps/routing/v2/localized_time.proto";
import "google/maps/routing/v2/location.proto";
import "google/maps/routing/v2/navigation_instruction.proto";
import "google/maps/routing/v2/polyline.proto";
import "google/maps/routing/v2/polyline_details.proto";
import "google/maps/routing/v2/route_label.proto";
import "google/maps/routing/v2/route_travel_mode.proto";
import "google/maps/routing/v2/speed_reading_interval.proto";
import "google/maps/routing/v2/toll_info.proto";
import "google/maps/routing/v2/transit.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/localized_text.proto";
import "google/type/money.proto";

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "RouteProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// Contains a route, which consists of a series of connected road segments
// that join beginning, ending, and intermediate waypoints.
message Route {
  // Text representations of certain properties.
  message RouteLocalizedValues {
    // Travel distance represented in text form.
    google.type.LocalizedText distance = 1;

    // Duration, represented in text form and localized to the region of the
    // query. Takes traffic conditions into consideration. Note: If you did not
    // request traffic information, this value is the same value as
    // `static_duration`.
    google.type.LocalizedText duration = 2;

    // Duration without taking traffic conditions into consideration,
    // represented in text form.
    google.type.LocalizedText static_duration = 3;

    // Transit fare represented in text form.
    google.type.LocalizedText transit_fare = 4;
  }

  // Labels for the `Route` that are useful to identify specific properties
  // of the route to compare against others.
  repeated RouteLabel route_labels = 13;

  // A collection of legs (path segments between waypoints) that make up the
  // route. Each leg corresponds to the trip between two non-`via`
  // [`Waypoints`][google.maps.routing.v2.Waypoint]. For example, a route with
  // no intermediate waypoints has only one leg. A route that includes one
  // non-`via` intermediate waypoint has two legs. A route that includes one
  // `via` intermediate waypoint has one leg. The order of the legs matches the
  // order of waypoints from `origin` to `intermediates` to `destination`.
  repeated RouteLeg legs = 1;

  // The travel distance of the route, in meters.
  int32 distance_meters = 2;

  // The length of time needed to navigate the route. If you set the
  // `routing_preference` to `TRAFFIC_UNAWARE`, then this value is the same as
  // `static_duration`. If you set the `routing_preference` to either
  // `TRAFFIC_AWARE` or `TRAFFIC_AWARE_OPTIMAL`, then this value is calculated
  // taking traffic conditions into account.
  google.protobuf.Duration duration = 3;

  // The duration of travel through the route without taking traffic
  // conditions into consideration.
  google.protobuf.Duration static_duration = 4;

  // The overall route polyline. This polyline is the combined polyline of
  // all `legs`.
  Polyline polyline = 5;

  // A description of the route.
  string description = 6;

  // An array of warnings to show when displaying the route.
  repeated string warnings = 7;

  // The viewport bounding box of the polyline.
  google.geo.type.Viewport viewport = 8;

  // Additional information about the route.
  RouteTravelAdvisory travel_advisory = 9;

  // If you set
  // [`optimize_waypoint_order`][google.maps.routing.v2.ComputeRoutesRequest.optimize_waypoint_order]
  // to true, this field contains the optimized ordering of intermediate
  // waypoints. Otherwise, this field is empty.
  // For example, if you give an input of Origin: LA; Intermediate waypoints:
  // Dallas, Bangor, Phoenix; Destination: New York; and the optimized
  // intermediate waypoint order is Phoenix, Dallas, Bangor, then this field
  // contains the values [2, 0, 1]. The index starts with 0 for the first
  // intermediate waypoint provided in the input.
  repeated int32 optimized_intermediate_waypoint_index = 10;

  // Text representations of properties of the `Route`.
  RouteLocalizedValues localized_values = 11;

  // An opaque token that can be passed to [Navigation
  // SDK](https://developers.google.com/maps/documentation/navigation) to
  // reconstruct the route during navigation, and, in the event of rerouting,
  // honor the original intention when the route was created. Treat this token
  // as an opaque blob.  Don't compare its value across requests as its value
  // may change even if the service returns the exact same route.
  //
  // NOTE: `Route.route_token` is only available for requests that have set
  // `ComputeRoutesRequest.routing_preference` to `TRAFFIC_AWARE` or
  // `TRAFFIC_AWARE_OPTIMAL`. `Route.route_token` is not supported for requests
  // that have Via waypoints.
  string route_token = 12;

  // Contains information about details along the polyline.
  PolylineDetails polyline_details = 14;
}

// Contains the additional information that the user should be informed
// about, such as possible traffic zone restrictions.
message RouteTravelAdvisory {
  // Contains information about tolls on the route. This field is only populated
  // if tolls are expected on the route. If this field is set, but the
  // `estimatedPrice` subfield is not populated, then the route contains tolls,
  // but the estimated price is unknown. If this field is not set, then there
  // are no tolls expected on the route.
  TollInfo toll_info = 2;

  // Speed reading intervals detailing traffic density. Applicable in case of
  // `TRAFFIC_AWARE` and `TRAFFIC_AWARE_OPTIMAL` routing preferences.
  // The intervals cover the entire polyline of the route without overlap.
  // The start point of a specified interval is the same as the end point of the
  // preceding interval.
  //
  // Example:
  //
  //     polyline: A ---- B ---- C ---- D ---- E ---- F ---- G
  //     speed_reading_intervals: [A,C), [C,D), [D,G).
  repeated SpeedReadingInterval speed_reading_intervals = 3;

  // The predicted fuel consumption in microliters.
  int64 fuel_consumption_microliters = 5;

  // Returned route may have restrictions that are not suitable for requested
  // travel mode or route modifiers.
  bool route_restrictions_partially_ignored = 6;

  // If present, contains the total fare or ticket costs on this route
  // This property is only returned for `TRANSIT` requests and only
  // for routes where fare information is available for all transit steps.
  google.type.Money transit_fare = 7;
}

// Contains the additional information that the user should be informed
// about on a leg step, such as possible traffic zone restrictions.
message RouteLegTravelAdvisory {
  // Contains information about tolls on the specific `RouteLeg`.
  // This field is only populated if we expect there are tolls on the
  // `RouteLeg`. If this field is set but the estimated_price subfield is not
  // populated, we expect that road contains tolls but we do not know an
  // estimated price. If this field does not exist, then there is no toll on the
  // `RouteLeg`.
  TollInfo toll_info = 1;

  // Speed reading intervals detailing traffic density. Applicable in case of
  // `TRAFFIC_AWARE` and `TRAFFIC_AWARE_OPTIMAL` routing preferences.
  // The intervals cover the entire polyline of the `RouteLeg` without overlap.
  // The start point of a specified interval is the same as the end point of the
  // preceding interval.
  //
  // Example:
  //
  //     polyline: A ---- B ---- C ---- D ---- E ---- F ---- G
  //     speed_reading_intervals: [A,C), [C,D), [D,G).
  repeated SpeedReadingInterval speed_reading_intervals = 2;
}

// Contains the additional information that the user should be informed
// about, such as possible traffic zone restrictions on a leg step.
message RouteLegStepTravelAdvisory {
  // NOTE: This field is not currently populated.
  repeated SpeedReadingInterval speed_reading_intervals = 1;
}

// Contains a segment between non-`via` waypoints.
message RouteLeg {
  // Text representations of certain properties.
  message RouteLegLocalizedValues {
    // Travel distance represented in text form.
    google.type.LocalizedText distance = 1;

    // Duration, represented in text form and localized to the region of the
    // query. Takes traffic conditions into consideration. Note: If you did not
    // request traffic information, this value is the same value as
    // static_duration.
    google.type.LocalizedText duration = 2;

    // Duration without taking traffic conditions into
    // consideration, represented in text form.
    google.type.LocalizedText static_duration = 3;
  }

  // Provides overview information about a list of `RouteLegStep`s.
  message StepsOverview {
    // Provides summarized information about different multi-modal segments of
    // the `RouteLeg.steps`. A multi-modal segment is defined as one or more
    // contiguous `RouteLegStep` that have the same `RouteTravelMode`.
    // This field is not populated if the `RouteLeg` does not contain any
    // multi-modal segments in the steps.
    message MultiModalSegment {
      // The corresponding `RouteLegStep` index that is the start of a
      // multi-modal segment.
      optional int32 step_start_index = 1;

      // The corresponding `RouteLegStep` index that is the end of a
      // multi-modal segment.
      optional int32 step_end_index = 2;

      // NavigationInstruction for the multi-modal segment.
      NavigationInstruction navigation_instruction = 3;

      // The travel mode of the multi-modal segment.
      RouteTravelMode travel_mode = 4;
    }

    // Summarized information about different multi-modal segments of
    // the `RouteLeg.steps`. This field is not populated if the `RouteLeg` does
    // not contain any multi-modal segments in the steps.
    repeated MultiModalSegment multi_modal_segments = 1;
  }

  // The travel distance of the route leg, in meters.
  int32 distance_meters = 1;

  // The length of time needed to navigate the leg. If the `route_preference`
  // is set to `TRAFFIC_UNAWARE`, then this value is the same as
  // `static_duration`. If the `route_preference` is either `TRAFFIC_AWARE` or
  // `TRAFFIC_AWARE_OPTIMAL`, then this value is calculated taking traffic
  // conditions into account.
  google.protobuf.Duration duration = 2;

  // The duration of travel through the leg, calculated without taking
  // traffic conditions into consideration.
  google.protobuf.Duration static_duration = 3;

  // The overall polyline for this leg that includes each `step`'s
  // polyline.
  Polyline polyline = 4;

  // The start location of this leg. This location might be different from the
  // provided `origin`. For example, when the provided `origin` is not near a
  // road, this is a point on the road.
  Location start_location = 5;

  // The end location of this leg. This location might be different from the
  // provided `destination`. For example, when the provided `destination` is not
  // near a road, this is a point on the road.
  Location end_location = 6;

  // An array of steps denoting segments within this leg. Each step represents
  // one navigation instruction.
  repeated RouteLegStep steps = 7;

  // Contains the additional information that the user should be informed
  // about, such as possible traffic zone restrictions, on a route leg.
  RouteLegTravelAdvisory travel_advisory = 8;

  // Text representations of properties of the `RouteLeg`.
  RouteLegLocalizedValues localized_values = 9;

  // Overview information about the steps in this `RouteLeg`. This field is only
  // populated for TRANSIT routes.
  StepsOverview steps_overview = 10;
}

// Contains a segment of a [`RouteLeg`][google.maps.routing.v2.RouteLeg]. A
// step corresponds to a single navigation instruction. Route legs are made up
// of steps.
message RouteLegStep {
  // Text representations of certain properties.
  message RouteLegStepLocalizedValues {
    // Travel distance represented in text form.
    google.type.LocalizedText distance = 1;

    // Duration without taking traffic conditions into
    // consideration, represented in text form.
    google.type.LocalizedText static_duration = 3;
  }

  // The travel distance of this step, in meters. In some circumstances, this
  // field might not have a value.
  int32 distance_meters = 1;

  // The duration of travel through this step without taking traffic conditions
  // into consideration. In some circumstances, this field might not have a
  // value.
  google.protobuf.Duration static_duration = 2;

  // The polyline associated with this step.
  Polyline polyline = 3;

  // The start location of this step.
  Location start_location = 4;

  // The end location of this step.
  Location end_location = 5;

  // Navigation instructions.
  NavigationInstruction navigation_instruction = 6;

  // Contains the additional information that the user should be informed
  // about, such as possible traffic zone restrictions, on a leg step.
  RouteLegStepTravelAdvisory travel_advisory = 7;

  // Text representations of properties of the `RouteLegStep`.
  RouteLegStepLocalizedValues localized_values = 8;

  // Details pertaining to this step if the travel mode is `TRANSIT`.
  RouteLegStepTransitDetails transit_details = 9;

  // The travel mode used for this step.
  RouteTravelMode travel_mode = 10;
}

// Additional information for the `RouteLegStep` related to `TRANSIT` routes.
message RouteLegStepTransitDetails {
  // Details about the transit stops for the `RouteLegStep`.
  message TransitStopDetails {
    // Information about the arrival stop for the step.
    TransitStop arrival_stop = 1;

    // The estimated time of arrival for the step.
    google.protobuf.Timestamp arrival_time = 2;

    // Information about the departure stop for the step.
    TransitStop departure_stop = 3;

    // The estimated time of departure for the step.
    google.protobuf.Timestamp departure_time = 4;
  }

  // Localized descriptions of values for `RouteTransitDetails`.
  message TransitDetailsLocalizedValues {
    // Time in its formatted text representation with a corresponding time zone.
    LocalizedTime arrival_time = 1;

    // Time in its formatted text representation with a corresponding time zone.
    LocalizedTime departure_time = 2;
  }

  // Information about the arrival and departure stops for the step.
  TransitStopDetails stop_details = 1;

  // Text representations of properties of the `RouteLegStepTransitDetails`.
  TransitDetailsLocalizedValues localized_values = 2;

  // Specifies the direction in which to travel on this line as marked on
  // the vehicle or at the departure stop. The direction is often the terminus
  // station.
  string headsign = 3;

  // Specifies the expected time as a duration between departures from the same
  // stop at this time. For example, with a headway seconds value of 600, you
  // would expect a ten minute wait if you should miss your bus.
  google.protobuf.Duration headway = 4;

  // Information about the transit line used in this step.
  TransitLine transit_line = 5;

  // The number of stops from the departure to the arrival stop. This count
  // includes the arrival stop, but excludes the departure stop. For example, if
  // your route leaves from Stop A, passes through stops B and C, and arrives at
  // stop D, <code>stop_count</code> returns 3.
  int32 stop_count = 6;

  // The text that appears in schedules and sign boards to identify a transit
  // trip to passengers. The text should uniquely identify a trip within a
  // service day. For example, "538" is the `trip_short_text` of the Amtrak
  // train that leaves San Jose, CA at 15:10 on weekdays to Sacramento, CA.
  string trip_short_text = 7;
}
