// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

import "google/rpc/status.proto";

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "GeocodingResultsProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// Contains [`GeocodedWaypoints`][google.maps.routing.v2.GeocodedWaypoint] for
// origin, destination and intermediate waypoints. Only populated for address
// waypoints.
message GeocodingResults {
  // Origin geocoded waypoint.
  GeocodedWaypoint origin = 1;

  // Destination geocoded waypoint.
  GeocodedWaypoint destination = 2;

  // A list of intermediate geocoded waypoints each containing an index field
  // that corresponds to the zero-based position of the waypoint in the order
  // they were specified in the request.
  repeated GeocodedWaypoint intermediates = 3;
}

// Details about the locations used as waypoints. Only populated for address
// waypoints. Includes details about the geocoding results for the purposes of
// determining what the address was geocoded to.
message GeocodedWaypoint {
  // Indicates the status code resulting from the geocoding operation.
  google.rpc.Status geocoder_status = 1;

  // The index of the corresponding intermediate waypoint in the request.
  // Only populated if the corresponding waypoint is an intermediate
  // waypoint.
  optional int32 intermediate_waypoint_request_index = 2;

  // The type(s) of the result, in the form of zero or more type tags.
  // Supported types: [Address types and address component
  // types](https://developers.google.com/maps/documentation/geocoding/requests-geocoding#Types).
  repeated string type = 3;

  // Indicates that the geocoder did not return an exact match for the original
  // request, though it was able to match part of the requested address. You may
  // wish to examine the original request for misspellings and/or an incomplete
  // address.
  bool partial_match = 4;

  // The place ID for this result.
  string place_id = 5;
}
