// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "PolylineDetailsProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// Details corresponding to a given index or contiguous segment of a polyline.
// Given a polyline with points P_0, P_1, ... , P_N (zero-based index), the
// `PolylineDetails` defines an interval and associated metadata.
message PolylineDetails {
  // Encapsulates the start and end indexes for a polyline detail.
  // For instances where the data corresponds to a single point, `start_index`
  // and `end_index` will be equal.
  message PolylinePointIndex {
    // The start index of this detail in the polyline.
    optional int32 start_index = 1;

    // The end index of this detail in the polyline.
    optional int32 end_index = 2;
  }

  // Encapsulates the states of road features along a stretch of polyline.
  enum RoadFeatureState {
    // The road feature's state was not computed (default value).
    ROAD_FEATURE_STATE_UNSPECIFIED = 0;

    // The road feature exists.
    EXISTS = 1;

    // The road feature does not exist.
    DOES_NOT_EXIST = 2;
  }

  // Encapsulates information about flyovers along the polyline.
  message FlyoverInfo {
    // Output only. Denotes whether a flyover exists for a given stretch of the
    // polyline.
    RoadFeatureState flyover_presence = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // The location of flyover related information along the polyline.
    PolylinePointIndex polyline_point_index = 2;
  }

  // Encapsulates information about narrow roads along the polyline.
  message NarrowRoadInfo {
    // Output only. Denotes whether a narrow road exists for a given stretch of
    // the polyline.
    RoadFeatureState narrow_road_presence = 1
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // The location of narrow road related information along the polyline.
    PolylinePointIndex polyline_point_index = 2;
  }

  // Flyover details along the polyline.
  repeated FlyoverInfo flyover_info = 12;

  // Narrow road details along the polyline.
  repeated NarrowRoadInfo narrow_road_info = 13;
}
