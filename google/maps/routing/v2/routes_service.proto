// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/maps/routing/v2/fallback_info.proto";
import "google/maps/routing/v2/geocoding_results.proto";
import "google/maps/routing/v2/polyline.proto";
import "google/maps/routing/v2/route.proto";
import "google/maps/routing/v2/route_modifiers.proto";
import "google/maps/routing/v2/route_travel_mode.proto";
import "google/maps/routing/v2/routing_preference.proto";
import "google/maps/routing/v2/traffic_model.proto";
import "google/maps/routing/v2/transit_preferences.proto";
import "google/maps/routing/v2/units.proto";
import "google/maps/routing/v2/waypoint.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/localized_text.proto";

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "RoutesServiceProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// The Routes API.
service Routes {
  option (google.api.default_host) = "routes.googleapis.com";

  // Returns the primary route along with optional alternate routes, given a set
  // of terminal and intermediate waypoints.
  //
  // **NOTE:** This method requires that you specify a response field mask in
  // the input. You can provide the response field mask by using URL parameter
  // `$fields` or `fields`, or by using an HTTP/gRPC header `X-Goog-FieldMask`
  // (see the [available URL parameters and
  // headers](https://cloud.google.com/apis/docs/system-parameters)). The value
  // is a comma separated list of field paths. See detailed documentation about
  // [how to construct the field
  // paths](https://github.com/protocolbuffers/protobuf/blob/master/src/google/protobuf/field_mask.proto).
  //
  // For example, in this method:
  //
  // * Field mask of all available fields (for manual inspection):
  //   `X-Goog-FieldMask: *`
  // * Field mask of Route-level duration, distance, and polyline (an example
  // production setup):
  //   `X-Goog-FieldMask:
  //   routes.duration,routes.distanceMeters,routes.polyline.encodedPolyline`
  //
  // Google discourage the use of the wildcard (`*`) response field mask, or
  // specifying the field mask at the top level (`routes`), because:
  //
  // * Selecting only the fields that you need helps our server save computation
  // cycles, allowing us to return the result to you with a lower latency.
  // * Selecting only the fields that you need
  // in your production job ensures stable latency performance. We might add
  // more response fields in the future, and those new fields might require
  // extra computation time. If you select all fields, or if you select all
  // fields at the top level, then you might experience performance degradation
  // because any new field we add will be automatically included in the
  // response.
  // * Selecting only the fields that you need results in a smaller response
  // size, and thus higher network throughput.
  rpc ComputeRoutes(ComputeRoutesRequest) returns (ComputeRoutesResponse) {
    option (google.api.http) = {
      post: "/directions/v2:computeRoutes"
      body: "*"
    };
  }

  // Takes in a list of origins and destinations and returns a stream containing
  // route information for each combination of origin and destination.
  //
  // **NOTE:** This method requires that you specify a response field mask in
  // the input. You can provide the response field mask by using the URL
  // parameter `$fields` or `fields`, or by using the HTTP/gRPC header
  // `X-Goog-FieldMask` (see the [available URL parameters and
  // headers](https://cloud.google.com/apis/docs/system-parameters)).
  // The value is a comma separated list of field paths. See this detailed
  // documentation about [how to construct the field
  // paths](https://github.com/protocolbuffers/protobuf/blob/master/src/google/protobuf/field_mask.proto).
  //
  // For example, in this method:
  //
  // * Field mask of all available fields (for manual inspection):
  //   `X-Goog-FieldMask: *`
  // * Field mask of route durations, distances, element status, condition, and
  //   element indices (an example production setup):
  //   `X-Goog-FieldMask:
  //   originIndex,destinationIndex,status,condition,distanceMeters,duration`
  //
  // It is critical that you include `status` in your field mask as otherwise
  // all messages will appear to be OK. Google discourages the use of the
  // wildcard (`*`) response field mask, because:
  //
  // * Selecting only the fields that you need helps our server save computation
  // cycles, allowing us to return the result to you with a lower latency.
  // * Selecting only the fields that you need in your production job ensures
  // stable latency performance. We might add more response fields in the
  // future, and those new fields might require extra computation time. If you
  // select all fields, or if you select all fields at the top level, then you
  // might experience performance degradation because any new field we add will
  // be automatically included in the response.
  // * Selecting only the fields that you need results in a smaller response
  // size, and thus higher network throughput.
  rpc ComputeRouteMatrix(ComputeRouteMatrixRequest)
      returns (stream RouteMatrixElement) {
    option (google.api.http) = {
      post: "/distanceMatrix/v2:computeRouteMatrix"
      body: "*"
    };
  }
}

// ComputeRoutes request message.
message ComputeRoutesRequest {
  // A supported reference route on the ComputeRoutesRequest.
  enum ReferenceRoute {
    // Not used. Requests containing this value fail.
    REFERENCE_ROUTE_UNSPECIFIED = 0;

    // Fuel efficient route.
    FUEL_EFFICIENT = 1;

    // Route with shorter travel distance. This is an experimental feature.
    //
    // For `DRIVE` requests, this feature prioritizes shorter distance over
    // driving comfort. For example, it may prefer local roads instead of
    // highways, take dirt roads, cut through parking lots, etc. This feature
    // does not return any maneuvers that Google Maps knows to be illegal.
    //
    // For `BICYCLE` and `TWO_WHEELER` requests, this feature returns routes
    // similar to those returned when you don't specify
    // `requested_reference_routes`.
    //
    // This feature is not compatible with any other travel modes, via
    // intermediate waypoints, or `optimize_waypoint_order`; such requests will
    // fail. However, you can use it with any `routing_preference`.
    SHORTER_DISTANCE = 2;
  }

  // Extra computations to perform while completing the request.
  enum ExtraComputation {
    // Not used. Requests containing this value will fail.
    EXTRA_COMPUTATION_UNSPECIFIED = 0;

    // Toll information for the route(s).
    TOLLS = 1;

    // Estimated fuel consumption for the route(s).
    FUEL_CONSUMPTION = 2;

    // Traffic aware polylines for the route(s).
    TRAFFIC_ON_POLYLINE = 3;

    // [`NavigationInstructions`](google.maps.routing.v2.NavigationInstructions.instructions)
    // presented as a formatted HTML text string. This content
    // is meant to be read as-is. This content is for display only.
    // Do not programmatically parse it.
    HTML_FORMATTED_NAVIGATION_INSTRUCTIONS = 4;

    // Flyover information for the route(s). The
    // `routes.polyline_details.flyover_info` fieldmask must be specified to
    // return this information. This data will only currently be populated for
    // certain metros in India. This feature is experimental, and the
    // SKU/charge is subject to change.
    FLYOVER_INFO_ON_POLYLINE = 7;

    // Narrow road information for the route(s). The
    // `routes.polyline_details.narrow_road_info` fieldmask must be specified
    // to return this information. This data will only currently be populated
    // for certain metros in India. This feature is experimental, and the
    // SKU/charge is subject to change.
    NARROW_ROAD_INFO_ON_POLYLINE = 8;
  }

  // Required. Origin waypoint.
  Waypoint origin = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Destination waypoint.
  Waypoint destination = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. A set of waypoints along the route (excluding terminal points),
  // for either stopping at or passing by. Up to 25 intermediate waypoints are
  // supported.
  repeated Waypoint intermediates = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the mode of transportation.
  RouteTravelMode travel_mode = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies how to compute the route. The server
  // attempts to use the selected routing preference to compute the route. If
  //  the routing preference results in an error or an extra long latency, then
  // an error is returned. You can specify this option only when the
  // `travel_mode` is `DRIVE` or `TWO_WHEELER`, otherwise the request fails.
  RoutingPreference routing_preference = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies your preference for the quality of the polyline.
  PolylineQuality polyline_quality = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the preferred encoding for the polyline.
  PolylineEncoding polyline_encoding = 12
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The departure time. If you don't set this value, then this value
  // defaults to the time that you made the request.
  // NOTE: You can only specify a `departure_time` in the past when
  // [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode] is set to
  // `TRANSIT`. Transit trips are available for up to 7 days in the past or 100
  // days in the future.
  google.protobuf.Timestamp departure_time = 7
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The arrival time.
  // NOTE: Can only be set when
  // [RouteTravelMode][google.maps.routing.v2.RouteTravelMode] is set to
  // `TRANSIT`. You can specify either `departure_time` or `arrival_time`, but
  // not both. Transit trips are available for up to 7 days in the past or 100
  // days in the future.
  google.protobuf.Timestamp arrival_time = 19
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether to calculate alternate routes in addition to
  // the route. No alternative routes are returned for requests that have
  // intermediate waypoints.
  bool compute_alternative_routes = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A set of conditions to satisfy that affect the way routes are
  // calculated.
  RouteModifiers route_modifiers = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The BCP-47 language code, such as "en-US" or "sr-Latn". For more
  // information, see [Unicode Locale
  // Identifier](http://www.unicode.org/reports/tr35/#Unicode_locale_identifier).
  // See [Language
  // Support](https://developers.google.com/maps/faq#languagesupport)
  // for the list of supported languages. When you don't provide this value, the
  // display language is inferred from the location of the route request.
  string language_code = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The region code, specified as a ccTLD ("top-level domain")
  // two-character value. For more information see [Country code top-level
  // domains](https://en.wikipedia.org/wiki/List_of_Internet_top-level_domains#Country_code_top-level_domains).
  string region_code = 16 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the units of measure for the display fields. These
  // fields include the `instruction` field in
  // [`NavigationInstruction`][google.maps.routing.v2.NavigationInstruction].
  // The units of measure used for the route, leg, step distance, and duration
  // are not affected by this value. If you don't provide this value, then the
  // display units are inferred from the location of the first origin.
  Units units = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, the service attempts to minimize the overall cost
  // of the route by re-ordering the specified intermediate waypoints. The
  // request fails if any of the intermediate waypoints is a `via` waypoint. Use
  // `ComputeRoutesResponse.Routes.optimized_intermediate_waypoint_index` to
  // find the new ordering.
  // If `ComputeRoutesResponseroutes.optimized_intermediate_waypoint_index` is
  // not requested in the `X-Goog-FieldMask` header, the request fails.
  // If `optimize_waypoint_order` is set to false,
  // `ComputeRoutesResponse.optimized_intermediate_waypoint_index` will be
  // empty.
  bool optimize_waypoint_order = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies what reference routes to calculate as part of the
  // request in addition to the default route. A reference route is a route with
  // a different route calculation objective than the default route. For example
  // a `FUEL_EFFICIENT` reference route calculation takes into account various
  // parameters that would generate an optimal fuel efficient route. When using
  // this feature, look for
  // [`route_labels`][google.maps.routing.v2.Route.route_labels] on the
  // resulting routes.
  repeated ReferenceRoute requested_reference_routes = 14
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of extra computations which may be used to complete the
  // request. Note: These extra computations may return extra fields on the
  // response. These extra fields must also be specified in the field mask to be
  // returned in the response.
  repeated ExtraComputation extra_computations = 15
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the assumptions to use when calculating time in
  // traffic. This setting affects the value returned in the duration field in
  // the
  // [`Route`][google.maps.routing.v2.Route] and
  // [`RouteLeg`][google.maps.routing.v2.RouteLeg] which contains the predicted
  // time in traffic based on historical averages.
  // `TrafficModel` is only available for requests that have set
  // [`RoutingPreference`][google.maps.routing.v2.RoutingPreference] to
  // `TRAFFIC_AWARE_OPTIMAL` and
  // [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode] to `DRIVE`.
  // Defaults to `BEST_GUESS` if traffic is requested and `TrafficModel` is not
  // specified.
  TrafficModel traffic_model = 18 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies preferences that influence the route returned for
  // `TRANSIT` routes. NOTE: You can only specify a `transit_preferences` when
  // [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode] is set to
  // `TRANSIT`.
  TransitPreferences transit_preferences = 20
      [(google.api.field_behavior) = OPTIONAL];
}

// ComputeRoutes the response message.
message ComputeRoutesResponse {
  // Contains an array of computed routes (up to three) when you specify
  // `compute_alternatives_routes`, and contains just one route when you don't.
  // When this array contains multiple entries, the first one is the most
  // recommended route. If the array is empty, then it means no route could be
  // found.
  repeated Route routes = 1;

  // In some cases when the server is not able to compute the route results with
  // all of the input preferences, it may fallback to using a different way of
  // computation. When fallback mode is used, this field contains detailed info
  // about the fallback response. Otherwise this field is unset.
  FallbackInfo fallback_info = 2;

  // Contains geocoding response info for waypoints specified as addresses.
  GeocodingResults geocoding_results = 3;
}

// ComputeRouteMatrix request message
message ComputeRouteMatrixRequest {
  // Extra computations to perform while completing the request.
  enum ExtraComputation {
    // Not used. Requests containing this value will fail.
    EXTRA_COMPUTATION_UNSPECIFIED = 0;

    // Toll information for the matrix element(s).
    TOLLS = 1;
  }

  // Required. Array of origins, which determines the rows of the response
  // matrix. Several size restrictions apply to the cardinality of origins and
  // destinations:
  //
  // * The sum of the number of origins + the number of destinations specified
  // as either `place_id` or `address` must be no greater than 50.
  // * The product of number of origins × number of destinations must be no
  // greater than 625 in any case.
  // * The product of the number of origins × number of destinations must be no
  // greater than 100 if routing_preference is set to `TRAFFIC_AWARE_OPTIMAL`.
  // * The product of the number of origins × number of destinations must be no
  // greater than 100 if travel_mode is set to `TRANSIT`.
  repeated RouteMatrixOrigin origins = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. Array of destinations, which determines the columns of the
  // response matrix.
  repeated RouteMatrixDestination destinations = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Specifies the mode of transportation.
  RouteTravelMode travel_mode = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies how to compute the route. The server attempts to use
  // the selected routing preference to compute the route. If the routing
  // preference results in an error or an extra long latency, an error is
  // returned. You can specify this option only when the `travel_mode` is
  // `DRIVE` or `TWO_WHEELER`, otherwise the request fails.
  RoutingPreference routing_preference = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The departure time. If you don't set this value, then this value
  // defaults to the time that you made the request.
  // NOTE: You can only specify a `departure_time` in the past when
  // [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode] is set to
  // `TRANSIT`.
  google.protobuf.Timestamp departure_time = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The arrival time.
  // NOTE: Can only be set when
  // [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode] is set to
  // `TRANSIT`. You can specify either `departure_time` or `arrival_time`, but
  // not both.
  google.protobuf.Timestamp arrival_time = 11
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The BCP-47 language code, such as "en-US" or "sr-Latn". For more
  // information, see [Unicode Locale
  // Identifier](http://www.unicode.org/reports/tr35/#Unicode_locale_identifier).
  // See [Language
  // Support](https://developers.google.com/maps/faq#languagesupport)
  // for the list of supported languages. When you don't provide this value, the
  // display language is inferred from the location of the first origin.
  string language_code = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The region code, specified as a ccTLD ("top-level domain")
  // two-character value. For more information see [Country code top-level
  // domains](https://en.wikipedia.org/wiki/List_of_Internet_top-level_domains#Country_code_top-level_domains).
  string region_code = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the units of measure for the display fields.
  Units units = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A list of extra computations which may be used to complete the
  // request. Note: These extra computations may return extra fields on the
  // response. These extra fields must also be specified in the field mask to be
  // returned in the response.
  repeated ExtraComputation extra_computations = 8
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the assumptions to use when calculating time in
  // traffic. This setting affects the value returned in the duration field in
  // the [RouteMatrixElement][google.maps.routing.v2.RouteMatrixElement] which
  // contains the predicted time in traffic based on historical averages.
  // [RoutingPreference][google.maps.routing.v2.RoutingPreference] to
  // `TRAFFIC_AWARE_OPTIMAL` and
  // [RouteTravelMode][google.maps.routing.v2.RouteTravelMode] to `DRIVE`.
  // Defaults to `BEST_GUESS` if traffic is requested and `TrafficModel` is not
  // specified.
  TrafficModel traffic_model = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies preferences that influence the route returned for
  // `TRANSIT` routes. NOTE: You can only specify a `transit_preferences` when
  // [RouteTravelMode][google.maps.routing.v2.RouteTravelMode] is set to
  // `TRANSIT`.
  TransitPreferences transit_preferences = 12
      [(google.api.field_behavior) = OPTIONAL];
}

// A single origin for ComputeRouteMatrixRequest
message RouteMatrixOrigin {
  // Required. Origin waypoint
  Waypoint waypoint = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Modifiers for every route that takes this as the origin
  RouteModifiers route_modifiers = 2 [(google.api.field_behavior) = OPTIONAL];
}

// A single destination for ComputeRouteMatrixRequest
message RouteMatrixDestination {
  // Required. Destination waypoint
  Waypoint waypoint = 1 [(google.api.field_behavior) = REQUIRED];
}

// Contains route information computed for an origin/destination pair in the
// ComputeRouteMatrix API. This proto can be streamed to the client.
message RouteMatrixElement {
  // Text representations of certain properties.
  message LocalizedValues {
    // Travel distance represented in text form.
    google.type.LocalizedText distance = 1;

    // Duration represented in text form taking traffic conditions into
    // consideration. Note: If traffic information was not requested, this value
    // is the same value as static_duration.
    google.type.LocalizedText duration = 2;

    // Duration represented in text form without taking traffic conditions into
    // consideration.
    google.type.LocalizedText static_duration = 3;

    // Transit fare represented in text form.
    google.type.LocalizedText transit_fare = 4;
  }

  // Zero-based index of the origin in the request.
  optional int32 origin_index = 1;

  // Zero-based index of the destination in the request.
  optional int32 destination_index = 2;

  // Error status code for this element.
  google.rpc.Status status = 3;

  // Indicates whether the route was found or not. Independent of status.
  RouteMatrixElementCondition condition = 9;

  // The travel distance of the route, in meters.
  int32 distance_meters = 4;

  // The length of time needed to navigate the route. If you set the
  // [routing_preference][google.maps.routing.v2.ComputeRouteMatrixRequest.routing_preference]
  // to `TRAFFIC_UNAWARE`, then this value is the same as `static_duration`. If
  // you set the `routing_preference` to either `TRAFFIC_AWARE` or
  // `TRAFFIC_AWARE_OPTIMAL`, then this value is calculated taking traffic
  // conditions into account.
  google.protobuf.Duration duration = 5;

  // The duration of traveling through the route without taking traffic
  // conditions into consideration.
  google.protobuf.Duration static_duration = 6;

  // Additional information about the route. For example: restriction
  // information and toll information
  RouteTravelAdvisory travel_advisory = 7;

  // In some cases when the server is not able to compute the route with the
  // given preferences for this particular origin/destination pair, it may
  // fall back to using a different mode of computation. When fallback mode is
  // used, this field contains detailed information about the fallback response.
  // Otherwise this field is unset.
  FallbackInfo fallback_info = 8;

  // Text representations of properties of the `RouteMatrixElement`.
  LocalizedValues localized_values = 10;
}

// The condition of the route being returned.
enum RouteMatrixElementCondition {
  // Only used when the `status` of the element is not OK.
  ROUTE_MATRIX_ELEMENT_CONDITION_UNSPECIFIED = 0;

  // A route was found, and the corresponding information was filled out for the
  // element.
  ROUTE_EXISTS = 1;

  // No route could be found. Fields containing route information, such as
  // `distance_meters` or `duration`, will not be filled out in the element.
  ROUTE_NOT_FOUND = 2;
}
