// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "TransitPreferencesProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// Preferences for `TRANSIT` based routes that influence the route that is
// returned.
message TransitPreferences {
  // A set of values used to specify the mode of transit.
  enum TransitTravelMode {
    // No transit travel mode specified.
    TRANSIT_TRAVEL_MODE_UNSPECIFIED = 0;

    // Travel by bus.
    BUS = 1;

    // Travel by subway.
    SUBWAY = 2;

    // Travel by train.
    TRAIN = 3;

    // Travel by light rail or tram.
    LIGHT_RAIL = 4;

    // Travel by rail. This is equivalent to a combination of `SUBWAY`, `TRAIN`,
    // and `LIGHT_RAIL`.
    RAIL = 5;
  }

  // Specifies routing preferences for transit routes.
  enum TransitRoutingPreference {
    // No preference specified.
    TRANSIT_ROUTING_PREFERENCE_UNSPECIFIED = 0;

    // Indicates that the calculated route should prefer limited amounts of
    // walking.
    LESS_WALKING = 1;

    // Indicates that the calculated route should prefer a limited number of
    // transfers.
    FEWER_TRANSFERS = 2;
  }

  // A set of travel modes to use when getting a `TRANSIT` route. Defaults to
  // all supported modes of travel.
  repeated TransitTravelMode allowed_travel_modes = 1;

  // A routing preference that, when specified, influences the `TRANSIT` route
  // returned.
  TransitRoutingPreference routing_preference = 2;
}
