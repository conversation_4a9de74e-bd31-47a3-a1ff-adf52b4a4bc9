// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.routing.v2;

import "google/protobuf/wrappers.proto";
import "google/type/latlng.proto";

option csharp_namespace = "Google.Maps.Routing.V2";
option go_package = "cloud.google.com/go/maps/routing/apiv2/routingpb;routingpb";
option java_multiple_files = true;
option java_outer_classname = "LocationProto";
option java_package = "com.google.maps.routing.v2";
option objc_class_prefix = "GMRV2";
option php_namespace = "Google\\Maps\\Routing\\V2";
option ruby_package = "Google::Maps::Routing::V2";

// Encapsulates a location (a geographic point, and an optional heading).
message Location {
  // The waypoint's geographic coordinates.
  google.type.LatLng lat_lng = 1;

  // The compass heading associated with the direction of the flow of traffic.
  // This value specifies the side of the road for pickup and drop-off. Heading
  // values can be from 0 to 360, where 0 specifies a heading of due North, 90
  // specifies a heading of due East, and so on. You can use this field only for
  // `DRIVE` and `TWO_WHEELER`
  // [`RouteTravelMode`][google.maps.routing.v2.RouteTravelMode].
  google.protobuf.Int32Value heading = 2;
}
