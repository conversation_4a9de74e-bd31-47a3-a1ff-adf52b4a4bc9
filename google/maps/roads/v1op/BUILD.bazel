# This file was automatically generated by BuildFileGenerator

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_gapic_library",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)
load("//google/maps:postprocessing.bzl", "maps_assembly_pkg")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "roads_proto",
    srcs = [
        "roads.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:resource_proto",
        "//google/type:latlng_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "roads_proto_with_info",
    deps = [
        ":roads_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "roads_java_proto",
    deps = [":roads_proto"],
)

java_grpc_library(
    name = "roads_java_grpc",
    srcs = [":roads_proto"],
    deps = [":roads_java_proto"],
)

java_gapic_library(
    name = "roads_java_gapic",
    srcs = [":roads_proto_with_info"],
    grpc_service_config = "roads_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "roads_v1op.yaml",
    test_deps = [
        ":roads_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":roads_java_proto",
    ],
)

java_gapic_test(
    name = "roads_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.roads.v1op.RoadsServiceClientTest",
    ],
    runtime_deps = ["roads_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-maps-roads-v1op-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":roads_java_gapic",
        ":roads_java_grpc",
        ":roads_java_proto",
        ":roads_proto",
    ],
)

maps_assembly_pkg(
    name = "google-maps-roads-v1op-java-postprocess",
    srcs = [":google-maps-roads-v1op-java"],
    language = "java",
)

go_proto_library(
    name = "roads_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/roads/apiv1op/roadspb",
    protos = [":roads_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "roads_go_gapic",
    srcs = [":roads_proto_with_info"],
    grpc_service_config = "roads_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/roads/apiv1op;roads",
    rest_numeric_enums = True,
    service_yaml = "roads_v1op.yaml",
    transport = "grpc+rest",
    deps = [
        ":roads_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapic-maps-roads-v1op-go",
    deps = [
        ":roads_go_gapic",
        ":roads_go_gapic_srcjar-snippets.srcjar",
        ":roads_go_gapic_srcjar-test.srcjar",
        ":roads_go_proto",
    ],
)

py_gapic_library(
    name = "roads_py_gapic",
    srcs = [":roads_proto"],
    grpc_service_config = "roads_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "roads_v1op.yaml",
    transport = "grpc",
)

py_test(
    name = "roads_py_gapic_test",
    srcs = [
        "roads_py_gapic_pytest.py",
        "roads_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":roads_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "roads-v1op-py",
    deps = [
        ":roads_py_gapic",
    ],
)

maps_assembly_pkg(
    name = "roads-v1op-py-postprocess",
    srcs = [":roads-v1op-py"],
    language = "py",
)

php_proto_library(
    name = "roads_php_proto",
    deps = [":roads_proto"],
)

# # Open Source Packages
# php_gapic_assembly_pkg(
#     name = "google-maps-roads-v1op-php",
#     deps = [
#         ":roads_php_gapic",
#         ":roads_php_proto",
#     ],
# )

nodejs_gapic_library(
    name = "roads_nodejs_gapic",
    src = ":roads_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "roads_grpc_service_config.json",
    package = "google.maps.roads.v1op",
    rest_numeric_enums = True,
    service_yaml = "roads_v1op.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "roads-v1op-nodejs",
    deps = [
        ":roads_nodejs_gapic",
        ":roads_proto",
    ],
)

ruby_proto_library(
    name = "roads_ruby_proto",
    deps = [":roads_proto"],
)

ruby_grpc_library(
    name = "roads_ruby_grpc",
    srcs = [":roads_proto"],
    deps = [":roads_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "roads_ruby_gapic",
    srcs = [":roads_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-roads-v1op"],
    rest_numeric_enums = True,
    service_yaml = "roads_v1op.yaml",
    transport = "grpc",
    deps = [
        ":roads_ruby_grpc",
        ":roads_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-maps-roads-v1op-ruby",
    deps = [
        ":roads_ruby_gapic",
        ":roads_ruby_grpc",
        ":roads_ruby_proto",
    ],
)

csharp_proto_library(
    name = "roads_csharp_proto",
    deps = [":roads_proto"],
)

csharp_grpc_library(
    name = "roads_csharp_grpc",
    srcs = [":roads_proto"],
    deps = [":roads_csharp_proto"],
)

csharp_gapic_library(
    name = "roads_csharp_gapic",
    srcs = [":roads_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "roads_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "roads_v1op.yaml",
    transport = "grpc",
    deps = [
        ":roads_csharp_grpc",
        ":roads_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-maps-roads-v1op-csharp",
    deps = [
        ":roads_csharp_gapic",
        ":roads_csharp_grpc",
        ":roads_csharp_proto",
    ],
)

cc_proto_library(
    name = "roads_cc_proto",
    deps = [":roads_proto"],
)

cc_grpc_library(
    name = "roads_cc_grpc",
    srcs = [":roads_proto"],
    generate_mocks = True,
    grpc_only = True,
    deps = [":roads_cc_proto"],
)

cc_gapic_library(
    name = "roads_cc_gapic",
    src = ":roads_proto_with_info",
    package = "google.maps.roads.v1op",
    deps = [
        ":roads_cc_grpc",
        ":roads_cc_proto",
    ],
)
