// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.roads.v1op;

import "google/api/client.proto";
import "google/protobuf/wrappers.proto";
import "google/type/latlng.proto";

option go_package = "cloud.google.com/go/maps/roads/apiv1op/roadspb;roadspb";
option java_multiple_files = true;
option java_outer_classname = "RoadsProto";
option java_package = "com.google.maps.roads.v1op";

// The Roads API maps one or more GPS coordinates to the geometry of the road
// and determines the speed limit along road segments.
service RoadsService {
  option (google.api.default_host) = "roads.googleapis.com";

  // This method takes a sequence of latitude,longitude points and snaps them to
  // the most likely road segments. Optionally returns additional points giving
  // the full road geometry. Also returns a place ID for each snapped point.
  rpc SnapToRoads(SnapToRoadsRequest) returns (SnapToRoadsResponse) {
    option (google.api.method_signature) = "path";
  }

  // This method takes a list of latitude,longitude points and snaps them each
  // to their nearest road. Also returns a place ID for each snapped point.
  rpc ListNearestRoads(ListNearestRoadsRequest)
      returns (ListNearestRoadsResponse) {
    option (google.api.method_signature) = "points";
  }
}

// A request to the SnapToRoads method, requesting that a sequence of points be
// snapped to road segments.
message SnapToRoadsRequest {
  // The path to be snapped as a series of lat, lng points. Specified as
  // a string of the format: lat,lng|lat,lng|...
  string path = 1;

  // Whether to interpolate the points to return full road geometry.
  bool interpolate = 2;

  // The asset ID of the asset to which this path relates. This is used for
  // abuse detection purposes for clients with asset-based SKUs.
  string asset_id = 3;

  // The type of travel being tracked. This will constrain the paths we snap to.
  TravelMode travel_mode = 4;
}

// An enum representing the mode of travel used for snapping.
enum TravelMode {
  TRAVEL_MODE_UNSPECIFIED = 0;

  DRIVING = 1;

  CYCLING = 2;

  WALKING = 3;
}

// A snapped point object, representing the result of snapping.
message SnappedPoint {
  // The lat,lng of the snapped location.
  google.type.LatLng location = 1;

  // The index into the original path of the equivalent pre-snapped point.
  // This allows for identification of points which have been interpolated if
  // this index is missing.
  google.protobuf.UInt32Value original_index = 2;

  // The place ID for this snapped location (road segment). These are the same
  // as are currently used by the Places API.
  string place_id = 3;
}

// The response from the SnapToRoads method, returning a sequence of snapped
// points.
message SnapToRoadsResponse {
  // A list of snapped points.
  repeated SnappedPoint snapped_points = 1;

  // User-visible warning message, if any, which can be shown alongside a valid
  // result.
  string warning_message = 2;
}

// A request to the ListNearestRoads method, requesting that a sequence of
// points be snapped individually to the road segment that each is closest to.
message ListNearestRoadsRequest {
  // The points to be snapped as a series of lat, lng points. Specified as
  // a string of the format: lat,lng|lat,lng|...
  string points = 1;

  // The type of travel being tracked. This will constrain the roads we snap to.
  TravelMode travel_mode = 2;
}

// The response from the ListNearestRoads method, returning a list of snapped
// points.
message ListNearestRoadsResponse {
  // A list of snapped points.
  repeated SnappedPoint snapped_points = 1;
}
