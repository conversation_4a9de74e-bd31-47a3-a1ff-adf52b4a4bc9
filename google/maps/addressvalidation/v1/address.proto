// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.addressvalidation.v1;

import "google/api/field_behavior.proto";
import "google/type/postal_address.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Maps.AddressValidation.V1";
option go_package = "cloud.google.com/go/maps/addressvalidation/apiv1/addressvalidationpb;addressvalidationpb";
option java_multiple_files = true;
option java_outer_classname = "AddressProto";
option java_package = "com.google.maps.addressvalidation.v1";
option objc_class_prefix = "GMPAVV1";
option php_namespace = "Google\\Maps\\AddressValidation\\V1";
option ruby_package = "Google::Maps::AddressValidation::V1";

// Details of the post-processed address. Post-processing includes
// correcting misspelled parts of the address, replacing incorrect parts, and
// inferring missing parts.
message Address {
  // The post-processed address, formatted as a single-line address following
  // the address formatting rules of the region where the address is located.
  string formatted_address = 2;

  // The post-processed address represented as a postal address.
  google.type.PostalAddress postal_address = 3;

  // Unordered list. The individual address components of the formatted and
  // corrected address, along with validation information. This provides
  // information on the validation status of the individual components.
  //
  // Address components are not ordered in a particular way. Do not make any
  // assumptions on the ordering of the address components in the list.
  repeated AddressComponent address_components = 4
      [(google.api.field_behavior) = UNORDERED_LIST];

  // The types of components that were expected to be present in a correctly
  // formatted mailing address but were not found in the input AND could
  // not be inferred. Components of this type are not present in
  // `formatted_address`, `postal_address`, or `address_components`. An
  // example might be `['street_number', 'route']` for an input like
  // "Boulder, Colorado, 80301, USA". The list of possible types can be found
  // [here](https://developers.google.com/maps/documentation/geocoding/requests-geocoding#Types).
  repeated string missing_component_types = 5;

  // The types of the components that are present in the `address_components`
  // but could not be confirmed to be correct. This field is provided for the
  // sake of convenience: its contents are equivalent to iterating through the
  // `address_components` to find the types of all the components where the
  // [confirmation_level][google.maps.addressvalidation.v1.AddressComponent.confirmation_level]
  // is not
  // [CONFIRMED][google.maps.addressvalidation.v1.AddressComponent.ConfirmationLevel.CONFIRMED]
  // or the
  // [inferred][google.maps.addressvalidation.v1.AddressComponent.inferred]
  // flag is not set to `true`. The list of possible types can be found
  // [here](https://developers.google.com/maps/documentation/geocoding/requests-geocoding#Types).
  repeated string unconfirmed_component_types = 6;

  // Any tokens in the input that could not be resolved. This might be an
  // input that was not recognized as a valid part of an address (for example
  // in an input like "123235253253 Main St, San Francisco, CA, 94105", the
  // unresolved tokens may look like `["123235253253"]` since that does not
  // look like a valid street number.
  repeated string unresolved_tokens = 7;
}

// Represents an address component, such as a street, city, or state.
message AddressComponent {
  // The different possible values for confirmation levels.
  enum ConfirmationLevel {
    // Default value. This value is unused.
    CONFIRMATION_LEVEL_UNSPECIFIED = 0;

    // We were able to verify that this component exists and makes sense in the
    // context of the rest of the address.
    CONFIRMED = 1;

    // This component could not be confirmed, but it is plausible that it
    // exists. For example, a street number within a known valid range of
    // numbers on a street where specific house numbers are not known.
    UNCONFIRMED_BUT_PLAUSIBLE = 2;

    // This component was not confirmed and is likely to be wrong. For
    // example, a neighborhood that does not fit the rest of the address.
    UNCONFIRMED_AND_SUSPICIOUS = 3;
  }

  // The name for this component.
  ComponentName component_name = 1;

  // The type of the address component. See
  // [Table 2: Additional types returned by the Places
  // service](https://developers.google.com/places/web-service/supported_types#table2)
  // for a list of possible types.
  string component_type = 2;

  // Indicates the level of certainty that we have that the component
  // is correct.
  ConfirmationLevel confirmation_level = 3;

  // Indicates that the component was not part of the input, but we
  // inferred it for the address location and believe it should be provided
  // for a complete address.
  bool inferred = 4;

  // Indicates a correction to a misspelling in the component name.  The API
  // does not always flag changes from one spelling variant to another, such as
  // when changing "centre" to "center". It also does not always flag common
  // misspellings, such as when changing "Amphitheater Pkwy" to "Amphitheatre
  // Pkwy".
  bool spell_corrected = 5;

  // Indicates the name of the component was replaced with a completely
  // different one, for example a wrong postal code being replaced with one that
  // is correct for the address. This is not a cosmetic change, the input
  // component has been changed to a different one.
  bool replaced = 6;

  // Indicates an address component that is not expected to be present in a
  // postal address for the given region. We have retained it only because it
  // was part of the input.
  bool unexpected = 7;
}

// A wrapper for the name of the component.
message ComponentName {
  // The name text. For example, "5th Avenue" for a street name or "1253" for a
  // street number.
  string text = 1;

  // The BCP-47 language code. This will not be present if the component name is
  // not associated with a language, such as a street number.
  string language_code = 2;
}
