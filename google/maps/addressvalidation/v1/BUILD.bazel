# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "addressvalidation_proto",
    srcs = [
        "address.proto",
        "address_validation_service.proto",
        "geocode.proto",
        "metadata.proto",
        "usps_data.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/geo/type:viewport_proto",
        "//google/type:latlng_proto",
        "//google/type:postal_address_proto",
    ],
)

proto_library_with_info(
    name = "addressvalidation_proto_with_info",
    deps = [
        ":addressvalidation_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "addressvalidation_java_proto",
    deps = [":addressvalidation_proto"],
)

java_grpc_library(
    name = "addressvalidation_java_grpc",
    srcs = [":addressvalidation_proto"],
    deps = [":addressvalidation_java_proto"],
)

java_gapic_library(
    name = "addressvalidation_java_gapic",
    srcs = [":addressvalidation_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "addressvalidation_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "addressvalidation_v1.yaml",
    test_deps = [
        ":addressvalidation_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":addressvalidation_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "addressvalidation_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.addressvalidation.v1.AddressValidationClientHttpJsonTest",
        "com.google.maps.addressvalidation.v1.AddressValidationClientTest",
    ],
    runtime_deps = [":addressvalidation_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-maps-addressvalidation-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":addressvalidation_java_gapic",
        ":addressvalidation_java_grpc",
        ":addressvalidation_java_proto",
        ":addressvalidation_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "addressvalidation_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/addressvalidation/apiv1/addressvalidationpb",
    protos = [":addressvalidation_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/geo/type:viewport_go_proto",
        "//google/type:latlng_go_proto",
        "//google/type:postaladdress_go_proto",
    ],
)

go_gapic_library(
    name = "addressvalidation_go_gapic",
    srcs = [":addressvalidation_proto_with_info"],
    grpc_service_config = "addressvalidation_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/addressvalidation/apiv1;addressvalidation",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "addressvalidation_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":addressvalidation_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-maps-addressvalidation-v1-go",
    deps = [
        ":addressvalidation_go_gapic",
        ":addressvalidation_go_gapic_srcjar-metadata.srcjar",
        ":addressvalidation_go_gapic_srcjar-snippets.srcjar",
        ":addressvalidation_go_gapic_srcjar-test.srcjar",
        ":addressvalidation_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
    "py_test",
)

py_import(
    name = "viewport",
    srcs = [
        "//google/geo/type:viewport_py_gapic",
    ],
)

py_gapic_library(
    name = "addressvalidation_py_gapic",
    srcs = [":addressvalidation_proto"],
    grpc_service_config = "addressvalidation_grpc_service_config.json",
    opt_args = ["proto-plus-deps=google.geo.type"],
    rest_numeric_enums = True,
    service_yaml = "addressvalidation_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":viewport",
    ],
)

py_test(
    name = "addressvalidation_py_gapic_test",
    srcs = [
        "addressvalidation_py_gapic_pytest.py",
        "addressvalidation_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [
        ":addressvalidation_py_gapic",
    ],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "maps-addressvalidation-v1-py",
    deps = [
        ":addressvalidation_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "addressvalidation_php_proto",
    deps = [":addressvalidation_proto"],
)

php_gapic_library(
    name = "addressvalidation_php_gapic",
    srcs = [":addressvalidation_proto_with_info"],
    grpc_service_config = "addressvalidation_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "addressvalidation_v1.yaml",
    transport = "grpc+rest",
    deps = [":addressvalidation_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-maps-addressvalidation-v1-php",
    deps = [
        ":addressvalidation_php_gapic",
        ":addressvalidation_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "addressvalidation_nodejs_gapic",
    package_name = "@googlemaps/addressvalidation",
    src = ":addressvalidation_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "addressvalidation_grpc_service_config.json",
    package = "google.maps.addressvalidation.v1",
    rest_numeric_enums = True,
    service_yaml = "addressvalidation_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "maps-addressvalidation-v1-nodejs",
    deps = [
        ":addressvalidation_nodejs_gapic",
        ":addressvalidation_proto",
        "//google/geo/type:viewport_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "addressvalidation_ruby_proto",
    deps = [":addressvalidation_proto"],
)

ruby_grpc_library(
    name = "addressvalidation_ruby_grpc",
    srcs = [":addressvalidation_proto"],
    deps = [":addressvalidation_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "addressvalidation_ruby_gapic",
    srcs = [":addressvalidation_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=addressvalidation.googleapis.com",
        "ruby-cloud-api-shortname=addressvalidation",
        "ruby-cloud-gem-name=google-maps-address_validation-v1",
        "ruby-cloud-product-url=https://developers.google.com/maps/documentation/address-validation/",
    ],
    grpc_service_config = "addressvalidation_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Address Validation is an API that accepts an address, identifies its components, validates them, normalizes the address for mailing and finds the best known location for it. It can help understand if an address refers to a real place. If the address does not refer to a real place, it can identify possibly wrong components, enabling users to correct them.",
    ruby_cloud_title = "Address Validation V1",
    service_yaml = "addressvalidation_v1.yaml",
    transport = "grpc",
    deps = [
        ":addressvalidation_ruby_grpc",
        ":addressvalidation_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-maps-addressvalidation-v1-ruby",
    deps = [
        ":addressvalidation_ruby_gapic",
        ":addressvalidation_ruby_grpc",
        ":addressvalidation_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "addressvalidation_csharp_proto",
    deps = [":addressvalidation_proto"],
)

csharp_grpc_library(
    name = "addressvalidation_csharp_grpc",
    srcs = [":addressvalidation_proto"],
    deps = [":addressvalidation_csharp_proto"],
)

csharp_gapic_library(
    name = "addressvalidation_csharp_gapic",
    srcs = [":addressvalidation_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "addressvalidation_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "addressvalidation_v1.yaml",
    transport = "grpc",
    deps = [
        ":addressvalidation_csharp_grpc",
        ":addressvalidation_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-maps-addressvalidation-v1-csharp",
    deps = [
        ":addressvalidation_csharp_gapic",
        ":addressvalidation_csharp_grpc",
        ":addressvalidation_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "addressvalidation_cc_proto",
    deps = [":addressvalidation_proto"],
)

cc_grpc_library(
    name = "addressvalidation_cc_grpc",
    srcs = [":addressvalidation_proto"],
    grpc_only = True,
    deps = [":addressvalidation_cc_proto"],
)
