// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.addressvalidation.v1;

option cc_enable_arenas = true;
option csharp_namespace = "Google.Maps.AddressValidation.V1";
option go_package = "cloud.google.com/go/maps/addressvalidation/apiv1/addressvalidationpb;addressvalidationpb";
option java_multiple_files = true;
option java_outer_classname = "MetadataProto";
option java_package = "com.google.maps.addressvalidation.v1";
option objc_class_prefix = "GMPAVV1";
option php_namespace = "Google\\Maps\\AddressValidation\\V1";
option ruby_package = "Google::Maps::AddressValidation::V1";

// The metadata for the address. `metadata` is not guaranteed to be fully
// populated for every address sent to the Address Validation API.
message AddressMetadata {
  // Indicates that this is the address of a business.
  // If unset, indicates that the value is unknown.
  optional bool business = 2;

  // Indicates that the address of a PO box.
  // If unset, indicates that the value is unknown.
  optional bool po_box = 3;

  // Indicates that this is the address of a residence.
  // If unset, indicates that the value is unknown.
  optional bool residential = 6;
}
