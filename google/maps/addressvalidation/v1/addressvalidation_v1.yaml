type: google.api.Service
config_version: 3
name: addressvalidation.googleapis.com
title: Address Validation API

apis:
- name: google.maps.addressvalidation.v1.AddressValidation

documentation:
  summary: |-
    The Address Validation API allows developers to verify the accuracy of
    addresses. Given an address, it returns information about the correctness
    of the components of the parsed address, a geocode, and a verdict on the
    deliverability of the parsed address.
  overview: '<!--#include file="/geo/platform/address_validation/g3doc/overview.md"-->'
