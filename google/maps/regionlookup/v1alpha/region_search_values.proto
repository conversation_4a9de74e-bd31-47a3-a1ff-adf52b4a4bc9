// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.regionlookup.v1alpha;

import "google/api/field_behavior.proto";
import "google/type/latlng.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Maps.RegionLookup.V1Alpha";
option go_package = "cloud.google.com/go/maps/regionlookup/apiv1alpha/regionlookuppb;regionlookuppb";
option java_multiple_files = true;
option java_outer_classname = "RegionSearchValuesProto";
option java_package = "com.google.maps.regionlookup.v1alpha";
option objc_class_prefix = "MRLV1A";
option php_namespace = "Google\\Maps\\RegionLookup\\V1alpha";

// Region Search Values.
//
// Desired search values of a single region.
//
// Location must be specified by one of the following: address, latlng or
// place_id. If none is specified, an INVALID_ARGUMENT error is returned.
// region_code must also be provided when address is specified.
//
// The fields address, latlng and place_id specify a location contained inside
// the region to match. For example if address is "1600 Amphitheatre Pkwy,
// Mountain View, CA 94043" the API returns the following matched_place_id
// results when the following place_types are specified:
//
// place_type:                   matched_place_id results:
// postal_code                   Place ID for "94043"
// administrative_area_level_1   Place ID for The State of California
// administrative_area_level_2   Place ID for Santa Clara County
// etc.
//
// More Examples:
//
// If latlng is "latitude: 37.4220656 longitude: -122.0862784" and place_type
// is "locality", the result contains the Place ID (of type "locality") for
// that location (the Place ID of Mountain View, CA, in this case).
//
// If place_id is "ChIJj61dQgK6j4AR4GeTYWZsKWw" (Place ID for Google office in
// Mountain view, CA) and place_type is "locality", the result contains the
// Place ID (of type "locality") for that location (the Place ID of Mountain
// View, CA, in this case).
//
// If no match is found, matched_place_id is not set.
//
// Candidates Place IDs are returned when a search finds multiple Place
// IDs for the location specified. For example if the API is searching for
// region Place IDs of type neighboorhood for a location that is contained
// within multiple neighboords. The Place Ids will be returned as candidates in
// the candidate_place_ids field.
//
// Next available tag: 10
message RegionSearchValue {
  // Possible place types to match to.
  enum PlaceType {
    // Default value. This value is unused.
    PLACE_TYPE_UNSPECIFIED = 0;

    // Postal code.
    POSTAL_CODE = 1;

    // Administrative area level 1 (State in the US).
    ADMINISTRATIVE_AREA_LEVEL_1 = 2;

    // Administrative area level 2 (County in the US).
    ADMINISTRATIVE_AREA_LEVEL_2 = 3;

    // Locality (City).
    LOCALITY = 4;

    // Neighborhood.
    NEIGHBORHOOD = 5;

    // Country.
    COUNTRY = 6;

    // Sublocality.
    SUBLOCALITY = 7;

    // Administrative area level 3.
    ADMINISTRATIVE_AREA_LEVEL_3 = 8;

    // Administrative area level 4.
    ADMINISTRATIVE_AREA_LEVEL_4 = 9;

    // School district.
    SCHOOL_DISTRICT = 10;
  }

  // The location must be specified by one of the following:
  oneof location {
    // The unstructured street address that is contained inside a region to
    // match. region_code is required when address is specified.
    string address = 1;

    // The latitude and longitude that is contained inside a region to match.
    google.type.LatLng latlng = 2;

    // The Place ID that is contained inside a region to match.
    string place_id = 3;
  }

  // Required. The type of the place to match.
  PlaceType place_type = 6 [(google.api.field_behavior) = REQUIRED];

  // The BCP-47 language code, such as "en-US" or "sr-Latn", corresponding to
  // the language in which the place name and address is requested. If none is
  // requested, then it defaults to English.
  string language_code = 7;

  // Two-letter ISO-3166 country/region code for the location you're trying to
  // match. region_code is required when address is specified.
  string region_code = 8;
}
