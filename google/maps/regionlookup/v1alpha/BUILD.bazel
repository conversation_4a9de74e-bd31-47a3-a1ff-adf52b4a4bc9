# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "regionlookup_proto",
    srcs = [
        "region_identifier.proto",
        "region_lookup_service.proto",
        "region_match.proto",
        "region_search_values.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/type:latlng_proto",
    ],
)

proto_library_with_info(
    name = "regionlookup_proto_with_info",
    deps = [
        ":regionlookup_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "regionlookup_java_proto",
    deps = [":regionlookup_proto"],
)

java_grpc_library(
    name = "regionlookup_java_grpc",
    srcs = [":regionlookup_proto"],
    deps = [":regionlookup_java_proto"],
)

java_gapic_library(
    name = "regionlookup_java_gapic",
    srcs = [":regionlookup_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "regionlookup_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "regionlookup_v1alpha.yaml",
    test_deps = [
        ":regionlookup_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":regionlookup_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "regionlookup_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.regionlookup.v1alpha.RegionLookupClientHttpJsonTest",
        "com.google.maps.regionlookup.v1alpha.RegionLookupClientTest",
    ],
    runtime_deps = [":regionlookup_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-maps-regionlookup-v1alpha-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":regionlookup_java_gapic",
        ":regionlookup_java_grpc",
        ":regionlookup_java_proto",
        ":regionlookup_proto",
    ],
)

go_proto_library(
    name = "regionlookup_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/regionlookup/apiv1alpha/regionlookuppb",
    protos = [":regionlookup_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:latlng_go_proto",
    ],
)

go_gapic_library(
    name = "regionlookup_go_gapic",
    srcs = [":regionlookup_proto_with_info"],
    grpc_service_config = "regionlookup_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/regionlookup/apiv1alpha;regionlookup",
    metadata = True,
    rest_numeric_enums = True,
    service_yaml = "regionlookup_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":regionlookup_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-maps-regionlookup-v1alpha-go",
    deps = [
        ":regionlookup_go_gapic",
        ":regionlookup_go_gapic_srcjar-metadata.srcjar",
        ":regionlookup_go_gapic_srcjar-snippets.srcjar",
        ":regionlookup_go_gapic_srcjar-test.srcjar",
        ":regionlookup_go_proto",
    ],
)

py_gapic_library(
    name = "regionlookup_py_gapic",
    srcs = [":regionlookup_proto"],
    grpc_service_config = "regionlookup_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "regionlookup_v1alpha.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "regionlookup_py_gapic_test",
    srcs = [
        "regionlookup_py_gapic_pytest.py",
        "regionlookup_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":regionlookup_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "maps-regionlookup-v1alpha-py",
    deps = [
        ":regionlookup_py_gapic",
    ],
)

php_proto_library(
    name = "regionlookup_php_proto",
    deps = [":regionlookup_proto"],
)

php_gapic_library(
    name = "regionlookup_php_gapic",
    srcs = [":regionlookup_proto_with_info"],
    grpc_service_config = "regionlookup_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "regionlookup_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [":regionlookup_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-maps-regionlookup-v1alpha-php",
    deps = [
        ":regionlookup_php_gapic",
        ":regionlookup_php_proto",
    ],
)

nodejs_gapic_library(
    name = "regionlookup_nodejs_gapic",
    package_name = "@google-cloud/regionlookup",
    src = ":regionlookup_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "regionlookup_grpc_service_config.json",
    package = "google.maps.regionlookup.v1alpha",
    rest_numeric_enums = True,
    service_yaml = "regionlookup_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "maps-regionlookup-v1alpha-nodejs",
    deps = [
        ":regionlookup_nodejs_gapic",
        ":regionlookup_proto",
    ],
)

ruby_proto_library(
    name = "regionlookup_ruby_proto",
    deps = [":regionlookup_proto"],
)

ruby_grpc_library(
    name = "regionlookup_ruby_grpc",
    srcs = [":regionlookup_proto"],
    deps = [":regionlookup_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "regionlookup_ruby_gapic",
    srcs = [":regionlookup_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-maps-regionlookup-v1alpha",
    ],
    grpc_service_config = "regionlookup_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "regionlookup_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":regionlookup_ruby_grpc",
        ":regionlookup_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-maps-regionlookup-v1alpha-ruby",
    deps = [
        ":regionlookup_ruby_gapic",
        ":regionlookup_ruby_grpc",
        ":regionlookup_ruby_proto",
    ],
)

csharp_proto_library(
    name = "regionlookup_csharp_proto",
    deps = [":regionlookup_proto"],
)

csharp_grpc_library(
    name = "regionlookup_csharp_grpc",
    srcs = [":regionlookup_proto"],
    deps = [":regionlookup_csharp_proto"],
)

csharp_gapic_library(
    name = "regionlookup_csharp_gapic",
    srcs = [":regionlookup_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "regionlookup_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "regionlookup_v1alpha.yaml",
    transport = "grpc+rest",
    deps = [
        ":regionlookup_csharp_grpc",
        ":regionlookup_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-maps-regionlookup-v1alpha-csharp",
    deps = [
        ":regionlookup_csharp_gapic",
        ":regionlookup_csharp_grpc",
        ":regionlookup_csharp_proto",
    ],
)

cc_proto_library(
    name = "regionlookup_cc_proto",
    deps = [":regionlookup_proto"],
)

cc_grpc_library(
    name = "regionlookup_cc_grpc",
    srcs = [":regionlookup_proto"],
    grpc_only = True,
    deps = [":regionlookup_cc_proto"],
)
