// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.regionlookup.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/maps/regionlookup/v1alpha/region_identifier.proto";
import "google/maps/regionlookup/v1alpha/region_match.proto";
import "google/maps/regionlookup/v1alpha/region_search_values.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Maps.RegionLookup.V1Alpha";
option go_package = "cloud.google.com/go/maps/regionlookup/apiv1alpha/regionlookuppb;regionlookuppb";
option java_multiple_files = true;
option java_outer_classname = "RegionLookupServiceProto";
option java_package = "com.google.maps.regionlookup.v1alpha";
option objc_class_prefix = "MRLV1A";
option php_namespace = "Google\\Maps\\RegionLookup\\V1alpha";

// Service definition for the Region Lookup API.
service RegionLookup {
  option (google.api.default_host) = "regionlookup.googleapis.com";

  // Lookup region RPC.
  //
  // Looks up a set of region Place IDs of types related to geographic
  // boundaries.
  //
  // The API looks up a region Place ID using the `RegionIdentifier` proto. See
  // `RegionIdentifier` for more details and examples.
  //
  // The following region place types are supported for look up: postal_code,
  // administrative_area_level_1, administrative_area_level_2, locality,
  // neighborhood, and country.
  rpc LookupRegion(LookupRegionRequest) returns (LookupRegionResponse) {
    option (google.api.http) = {
      post: "/v1alpha:lookupRegion"
      body: "*"
    };
  }

  // Search region RPC.
  //
  // Searches for a set of region Place IDs of types related to geographic
  // boundaries.
  //
  // Similar to `LookupRegion` RPC but instead of looking up Place IDs for the
  // given `RegionIdentifier`, the API searches for Region Place IDs by
  // considering all regions that are contained within a specified location. The
  // `RegionSearchValue` is used to specify the search values. See
  // `RegionSearchValue` for more details and examples.
  //
  // The following region place types are supported for searching: postal_code,
  // administrative_area_level_1, administrative_area_level_2, locality,
  // neighborhood, and country.
  rpc SearchRegion(SearchRegionRequest) returns (SearchRegionResponse) {
    option (google.api.http) = {
      post: "/v1alpha:searchRegion"
      body: "*"
    };
  }
}

// Lookup Region Request.
//
// Next available tag: 4
message LookupRegionRequest {
  // Each `RegionIdentifier` represents the desired fields used to lookup a
  // single region. See `RegionIdentifier` proto for more details and examples.
  repeated RegionIdentifier identifiers = 1;

  // The maximum number of matches to return. The service may return fewer than
  // this value.
  //
  // If unspecified, at most 50 matches will be returned. The maximum value is
  // 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `LookupRegion` call. Provide this to
  // retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `LookupRegion` must match
  // the call that provided the page token.
  string page_token = 3;
}

// Lookup Region Response.
//
// Next available tag: 3
message LookupRegionResponse {
  // Lookup region matches, one for each `RegionIdentifier` in
  // `LookupRegionRequest.identifiers`.
  repeated RegionMatch matches = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Search Region Request.
//
// Next available tag: 4
message SearchRegionRequest {
  // Each value represents desired search values of a single region to match.
  // The API tries to match them to Place IDs. See `RegionSearchValue`
  // proto for more info and examples.
  repeated RegionSearchValue search_values = 1;

  // The maximum number of matches to return. The service may return fewer than
  // this value.
  //
  // If unspecified, at most 50 matches will be returned. The maximum value is
  // 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `SearchRegion` call. Provide this to
  // retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `LookupRegion` must match
  // the call that provided the page token.
  string page_token = 3;
}

// Match Region Response.
//
// Next available tag: 3
message SearchRegionResponse {
  // Search region matches, one for each `RegionSearchValue` in
  // `SearchRegionRequest.search_values`.
  repeated RegionMatch matches = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
