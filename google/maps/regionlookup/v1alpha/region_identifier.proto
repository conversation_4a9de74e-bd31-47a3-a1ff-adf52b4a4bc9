// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.regionlookup.v1alpha;

import "google/api/field_behavior.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Maps.RegionLookup.V1Alpha";
option go_package = "cloud.google.com/go/maps/regionlookup/apiv1alpha/regionlookuppb;regionlookuppb";
option java_multiple_files = true;
option java_outer_classname = "RegionIdentifierProto";
option java_package = "com.google.maps.regionlookup.v1alpha";
option objc_class_prefix = "MRLV1A";
option php_namespace = "Google\\Maps\\RegionLookup\\V1alpha";

// Region Identifier.
//
// Identifies a region to look up.
//
// One of place or unit_code must be specified. If none is specified,
// an INVALID_ARGUMENT error is returned. region_code must also be specified
// except when place_type is "country".
//
// place and unit_code specify a location to match a Place ID to. For
// example if place is "California" and region_code "US" the API
// returns the following matched_place_id results when the following
// place_types are specified:
//
// place_type:                   matched_place_id results:
// administrative_area_level_1   Place ID for The State of California
// (All other supported types)   No Match
//
// If unit_code is "6" (FIPs code for California) and region_code is "US
// the API returns the following matched_place_id results when the
// following place_types are specified:
//
// place type:                   matched_place_id results:
// administrative_area_level_1   Place ID for The State of California
// (All other supported types)   No Match
//
// or if unit_code is "US" the API returns the following results when
// the following place_types are specified:
//
// place type:                   matched_place_id results:
// country                       Place ID for the United States
// (All other supported types)   No Match
//
// If no match is found, matched_place_id is not set.
//
// Candidate Place IDs are returned when a lookup finds a region with a
// different place_type then the one requested. For example if place is
// "California" and place_type is "country" the Place ID for The State of
// California is returned as a candidate in the candidate_place_ids field.
//
// Next available tag: 10
message RegionIdentifier {
  // Possible place types to match to.
  enum PlaceType {
    // Default value. This value is unused.
    PLACE_TYPE_UNSPECIFIED = 0;

    // Postal code.
    POSTAL_CODE = 1;

    // Administrative area level 1 (State in the US).
    ADMINISTRATIVE_AREA_LEVEL_1 = 2;

    // Administrative area level 2 (County in the US).
    ADMINISTRATIVE_AREA_LEVEL_2 = 3;

    // Locality (City).
    LOCALITY = 4;

    // Neighborhood.
    NEIGHBORHOOD = 5;

    // Country.
    COUNTRY = 6;

    // Sublocality.
    SUBLOCALITY = 7;

    // Administrative area level 3.
    ADMINISTRATIVE_AREA_LEVEL_3 = 8;

    // Administrative area level 4.
    ADMINISTRATIVE_AREA_LEVEL_4 = 9;

    // School district.
    SCHOOL_DISTRICT = 10;
  }

  // The location must be specified by one of the following:
  oneof location {
    // The name of the region to match to a Place ID.
    //
    // The place field is used in combination with place_type to look up
    // the region Place ID.
    //
    // For example:
    // If place_type is "locality", a valid place can be "Palo Alto, CA".
    //
    // If place_type is "postal_code", a valid place can be "94109".
    //
    // If place_type is "country", a valid place can be "United States".
    // etc.
    //
    // region_code is required when place is specified except when
    // place_type is "country".
    string place = 4;

    // The FIPs state or county codes (US only) or ISO-3166-1 country code to be
    // matched.
    //
    // The unit_code field is used in combination with place_type to look up
    // the region Place ID.
    //
    // For example:
    // If place_type is "country", a valid unit_code can be "US" (ISO-3166-1
    // Alpha-2 code for United States) or "BR" (ISO-3166-1 Alpha-2 code for
    // Brazil).
    //
    // If place_type is "administrative_area_level_1" (state) and region_code is
    // "US", a valid unit_code can be "6" (FIPs code for California) or
    // "12"(FIPs code for Florida).
    //
    // If place_type is "administrative_area_level_2" (county) and region_code
    // is "US", a valid unit_code can be "6001" (FIPs code for Alameda County in
    // California) or "12086" (FIPs code for Miami-Dade County in Florida).
    //
    // region_code is required when specifying a FIPs code. region_code is
    // ignored for ISO-3166-1 country codes.
    string unit_code = 5;
  }

  // Required. Place type to match.
  PlaceType place_type = 6 [(google.api.field_behavior) = REQUIRED];

  // The BCP-47 language code, such as "en-US" or "sr-Latn", corresponding to
  // the language in which the place name and address is requested. If none is
  // requested, then it defaults to English.
  string language_code = 7;

  // Two-letter ISO-3166 country/region code for the location you're trying to
  // match. region_code is optional if place_type is "country".
  string region_code = 8;
}
