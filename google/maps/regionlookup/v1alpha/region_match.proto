// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.regionlookup.v1alpha;

option cc_enable_arenas = true;
option csharp_namespace = "Google.Maps.RegionLookup.V1Alpha";
option go_package = "cloud.google.com/go/maps/regionlookup/apiv1alpha/regionlookuppb;regionlookuppb";
option java_multiple_files = true;
option java_outer_classname = "RegionMatchProto";
option java_package = "com.google.maps.regionlookup.v1alpha";
option objc_class_prefix = "MRLV1A";
option php_namespace = "Google\\Maps\\RegionLookup\\V1alpha";

// Region Match.
//
// Next available tag: 5
message RegionMatch {
  // Place ID of the region that is matched. If region is found, this field is
  // not set.
  string matched_place_id = 1;

  // Region candidate IDs. Up to three candidates may be returned.
  repeated string candidate_place_ids = 2;

  // Matching debug information for when no match is found.
  string debug_info = 3;
}
