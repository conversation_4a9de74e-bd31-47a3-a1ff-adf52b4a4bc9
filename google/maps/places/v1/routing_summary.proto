// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "RoutingSummaryProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// The duration and distance from the routing origin to a place in the
// response, and a second leg from that place to the destination, if requested.
// **Note:** Adding `routingSummaries` in the field mask without also including
// either the `routingParameters.origin` parameter or the
// `searchAlongRouteParameters.polyline.encodedPolyline` parameter in the
// request causes an error.
message RoutingSummary {
  // A leg is a single portion of a journey from one location to another.
  message Leg {
    // The time it takes to complete this leg of the trip.
    google.protobuf.Duration duration = 1;

    // The distance of this leg of the trip.
    int32 distance_meters = 2;
  }

  // The legs of the trip.
  //
  // When you calculate travel duration and distance from a set origin, `legs`
  // contains a single leg containing the duration and distance from the origin
  // to the destination.  When you do a search along route, `legs` contains two
  // legs: one from the origin to place, and one from the place to the
  // destination.
  repeated Leg legs = 1;

  // A link to show directions on Google Maps using the waypoints from the given
  // routing summary. The route generated by this link is not guaranteed to be
  // the same as the route used to generate the routing summary.
  // The link uses information provided in the request, from fields including
  // `routingParameters` and `searchAlongRouteParameters` when applicable, to
  // generate the directions link.
  string directions_uri = 2;
}
