// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "TravelModeProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// Travel mode options.
// These options map to what [Routes API
// offers](https://developers.google.com/maps/documentation/routes/reference/rest/v2/RouteTravelMode).
enum TravelMode {
  // No travel mode specified. Defaults to `DRIVE`.
  TRAVEL_MODE_UNSPECIFIED = 0;

  // Travel by passenger car.
  DRIVE = 1;

  // Travel by bicycle.  Not supported with `search_along_route_parameters`.
  BICYCLE = 2;

  // Travel by walking.  Not supported with `search_along_route_parameters`.
  WALK = 3;

  // Motorized two wheeled vehicles of all kinds such as scooters and
  // motorcycles. Note that this is distinct from the `BICYCLE` travel mode
  // which covers human-powered transport.  Not supported with
  // `search_along_route_parameters`. Only supported in those countries listed
  // at [Countries and regions supported for two-wheeled
  // vehicles](https://developers.google.com/maps/documentation/routes/coverage-two-wheeled).
  TWO_WHEELER = 4;
}
