// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/api/resource.proto";
import "google/maps/places/v1/attribution.proto";
import "google/protobuf/timestamp.proto";
import "google/type/localized_text.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "ReviewProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// Information about a review of a place.
message Review {
  option (google.api.resource) = {
    type: "places.googleapis.com/Review"
    pattern: "places/{place}/reviews/{review}"
    plural: "reviews"
    singular: "review"
  };

  // A reference representing this place review which may be used to look up
  // this place review again (also called the API "resource" name:
  // `places/{place_id}/reviews/{review}`).
  string name = 1;

  // A string of formatted recent time, expressing the review time relative
  // to the current time in a form appropriate for the language and country.
  string relative_publish_time_description = 2;

  // The localized text of the review.
  google.type.LocalizedText text = 9;

  // The review text in its original language.
  google.type.LocalizedText original_text = 12;

  // A number between 1.0 and 5.0, also called the number of stars.
  double rating = 7;

  // This review's author.
  AuthorAttribution author_attribution = 13;

  // Timestamp for the review.
  google.protobuf.Timestamp publish_time = 14;
}
