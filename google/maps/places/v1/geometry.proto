// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/api/field_behavior.proto";
import "google/type/latlng.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "GeometryProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// Circle with a LatLng as center and radius.
message Circle {
  // Required. Center latitude and longitude.
  //
  // The range of latitude must be within [-90.0, 90.0]. The range of the
  // longitude must be within [-180.0, 180.0].
  google.type.LatLng center = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Radius measured in meters. The radius must be within [0.0,
  // 50000.0].
  double radius = 2 [(google.api.field_behavior) = REQUIRED];
}
