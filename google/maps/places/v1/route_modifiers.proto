// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "RouteModifiersProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// Encapsulates a set of optional conditions to satisfy when calculating the
// routes.
message RouteModifiers {
  // Optional. When set to true, avoids toll roads where reasonable, giving
  // preference to routes not containing toll roads. Applies only to the `DRIVE`
  // and `TWO_WHEELER` [`TravelMode`][google.maps.places.v1.TravelMode].
  bool avoid_tolls = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. When set to true, avoids highways where reasonable, giving
  // preference to routes not containing highways. Applies only to the `DRIVE`
  // and `TWO_WHEELER` [`TravelMode`][google.maps.places.v1.TravelMode].
  bool avoid_highways = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. When set to true, avoids ferries where reasonable, giving
  // preference to routes not containing ferries. Applies only to the `DRIVE`
  // and `TWO_WHEELER` [`TravelMode`][google.maps.places.v1.TravelMode].
  bool avoid_ferries = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. When set to true, avoids navigating indoors where reasonable,
  // giving preference to routes not containing indoor navigation. Applies only
  // to the `WALK` [`TravelMode`][google.maps.places.v1.TravelMode].
  bool avoid_indoor = 4 [(google.api.field_behavior) = OPTIONAL];
}
