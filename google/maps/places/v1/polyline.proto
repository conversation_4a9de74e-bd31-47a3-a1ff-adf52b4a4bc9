// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "PolylineProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// A route polyline.  Only supports an [encoded
// polyline](https://developers.google.com/maps/documentation/utilities/polylinealgorithm),
// which can be passed as a string and includes compression with minimal
// lossiness. This is the Routes API default output.
message Polyline {
  // Encapsulates the type of polyline. Routes API output defaults to
  // `encoded_polyline`.
  oneof polyline_type {
    // An [encoded
    // polyline](https://developers.google.com/maps/documentation/utilities/polylinealgorithm),
    // as returned by the [Routes API by
    // default](https://developers.google.com/maps/documentation/routes/reference/rest/v2/TopLevel/computeRoutes#polylineencoding).
    // See the
    // [encoder](https://developers.google.com/maps/documentation/utilities/polylineutility)
    // and
    // [decoder](https://developers.google.com/maps/documentation/routes/polylinedecoder)
    // tools.
    string encoded_polyline = 1;
  }
}
