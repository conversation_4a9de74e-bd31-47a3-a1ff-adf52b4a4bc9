// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/maps/places/v1/reference.proto";
import "google/type/localized_text.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "ContentBlockProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// A block of content that can be served individually.
message ContentBlock {
  // The topic of the content, for example "overview" or "restaurant".
  string topic = 1;

  // Content related to the topic.
  google.type.LocalizedText content = 2;

  // Experimental: See
  // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
  // for more details.
  //
  // References that are related to this block of content.
  References references = 3;
}
