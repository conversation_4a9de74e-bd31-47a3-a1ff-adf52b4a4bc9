// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/maps/places/v1/attribution.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "PhotoProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// Information about a photo of a place.
message Photo {
  option (google.api.resource) = {
    type: "places.googleapis.com/Photo"
    pattern: "places/{place}/photos/{photo}"
    plural: "photos"
    singular: "photo"
  };

  // Identifier. A reference representing this place photo which may be used to
  // look up this place photo again (also called the API "resource" name:
  // `places/{place_id}/photos/{photo}`).
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // The maximum available width, in pixels.
  int32 width_px = 2;

  // The maximum available height, in pixels.
  int32 height_px = 3;

  // This photo's authors.
  repeated AuthorAttribution author_attributions = 4;
}
