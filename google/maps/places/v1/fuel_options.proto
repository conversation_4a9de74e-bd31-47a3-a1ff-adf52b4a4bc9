// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "FuelOptionsProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// The most recent information about fuel options in a gas station. This
// information is updated regularly.
message FuelOptions {
  // Fuel price information for a given type.
  message FuelPrice {
    // Types of fuel.
    enum FuelType {
      // Unspecified fuel type.
      FUEL_TYPE_UNSPECIFIED = 0;

      // Diesel fuel.
      DIESEL = 1;

      // Regular unleaded.
      REGULAR_UNLEADED = 2;

      // Midgrade.
      MIDGRADE = 3;

      // Premium.
      PREMIUM = 4;

      // SP 91.
      SP91 = 5;

      // SP 91 E10.
      SP91_E10 = 6;

      // SP 92.
      SP92 = 7;

      // SP 95.
      SP95 = 8;

      // SP95 E10.
      SP95_E10 = 9;

      // SP 98.
      SP98 = 10;

      // SP 99.
      SP99 = 11;

      // SP 100.
      SP100 = 12;

      // LPG.
      LPG = 13;

      // E 80.
      E80 = 14;

      // E 85.
      E85 = 15;

      // Methane.
      METHANE = 16;

      // Bio-diesel.
      BIO_DIESEL = 17;

      // Truck diesel.
      TRUCK_DIESEL = 18;
    }

    // The type of fuel.
    FuelType type = 1;

    // The price of the fuel.
    google.type.Money price = 2;

    // The time the fuel price was last updated.
    google.protobuf.Timestamp update_time = 3;
  }

  // The last known fuel price for each type of fuel this station has. There is
  // one entry per fuel type this station has. Order is not important.
  repeated FuelPrice fuel_prices = 1;
}
