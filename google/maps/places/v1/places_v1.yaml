type: google.api.Service
config_version: 3
name: places.googleapis.com
title: Places API (New)

apis:
- name: google.maps.places.v1.Places

documentation:
  summary: |-
    The Places API allows developers to access a variety of search and
    retrieval endpoints for a Place.
  overview: '<!--#include file="/geo/platform/places/g3doc/overview.md"-->'

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=188872&template=1815671
  documentation_uri: https://developers.google.com/maps/documentation/places/web-service/
  api_short_name: places
  github_label: 'api: places'
  doc_tag_prefix: places
  organization: GEO
  library_settings:
  - version: google.maps.places.v1
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://developers.google.com/maps/documentation/places/web-service/place-data-fields
