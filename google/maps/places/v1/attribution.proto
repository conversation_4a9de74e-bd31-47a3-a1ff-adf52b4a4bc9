// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "AttributionProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// Information about the author of the UGC data. Used in
// [Photo][google.maps.places.v1.Photo], and
// [Review][google.maps.places.v1.Review].
message AuthorAttribution {
  // Name of the author of the [Photo][google.maps.places.v1.Photo] or
  // [Review][google.maps.places.v1.Review].
  string display_name = 1;

  // URI of the author of the [Photo][google.maps.places.v1.Photo] or
  // [Review][google.maps.places.v1.Review].
  string uri = 2;

  // Profile photo URI of the author of the
  // [Photo][google.maps.places.v1.Photo] or
  // [Review][google.maps.places.v1.Review].
  string photo_uri = 3;
}
