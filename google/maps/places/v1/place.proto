// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/api/resource.proto";
import "google/geo/type/viewport.proto";
import "google/maps/places/v1/content_block.proto";
import "google/maps/places/v1/ev_charging.proto";
import "google/maps/places/v1/fuel_options.proto";
import "google/maps/places/v1/photo.proto";
import "google/maps/places/v1/price_range.proto";
import "google/maps/places/v1/reference.proto";
import "google/maps/places/v1/review.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/latlng.proto";
import "google/type/localized_text.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "PlaceProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// All the information representing a Place.
message Place {
  option (google.api.resource) = {
    type: "places.googleapis.com/Place"
    pattern: "places/{place_id}"
    plural: "places"
    singular: "place"
  };

  // The structured components that form the formatted address, if this
  // information is available.
  message AddressComponent {
    // The full text description or name of the address component. For example,
    // an address component for the country Australia may have a long_name of
    // "Australia".
    string long_text = 1;

    // An abbreviated textual name for the address component, if available. For
    // example, an address component for the country of Australia may have a
    // short_name of "AU".
    string short_text = 2;

    // An array indicating the type(s) of the address component.
    repeated string types = 3;

    // The language used to format this components, in CLDR notation.
    string language_code = 4;
  }

  // Plus code (http://plus.codes) is a location reference with two formats:
  // global code defining a 14mx14m (1/8000th of a degree) or smaller rectangle,
  // and compound code, replacing the prefix with a reference location.
  message PlusCode {
    // Place's global (full) code, such as "9FWM33GV+HQ", representing an
    // 1/8000 by 1/8000 degree area (~14 by 14 meters).
    string global_code = 1;

    // Place's compound code, such as "33GV+HQ, Ramberg, Norway", containing
    // the suffix of the global code and replacing the prefix with a formatted
    // name of a reference entity.
    string compound_code = 2;
  }

  // Information about business hour of the place.
  message OpeningHours {
    // A period the place remains in open_now status.
    message Period {
      // Status changing points.
      message Point {
        // A day of the week, as an integer in the range 0-6.  0 is Sunday, 1 is
        // Monday, etc.
        optional int32 day = 1;

        // The hour in 2 digits. Ranges from 00 to 23.
        optional int32 hour = 2;

        // The minute in 2 digits. Ranges from 00 to 59.
        optional int32 minute = 3;

        // Date in the local timezone for the place.
        google.type.Date date = 6;

        // Whether or not this endpoint was truncated. Truncation occurs when
        // the real hours are outside the times we are willing to return hours
        // between, so we truncate the hours back to these boundaries. This
        // ensures that at most 24 * 7 hours from midnight of the day of the
        // request are returned.
        bool truncated = 5;
      }

      // The time that the place starts to be open.
      Point open = 1;

      // The time that the place starts to be closed.
      Point close = 2;
    }

    // A type used to identify the type of secondary hours.
    enum SecondaryHoursType {
      // Default value when secondary hour type is not specified.
      SECONDARY_HOURS_TYPE_UNSPECIFIED = 0;

      // The drive-through hour for banks, restaurants, or pharmacies.
      DRIVE_THROUGH = 1;

      // The happy hour.
      HAPPY_HOUR = 2;

      // The delivery hour.
      DELIVERY = 3;

      // The takeout hour.
      TAKEOUT = 4;

      // The kitchen hour.
      KITCHEN = 5;

      // The breakfast hour.
      BREAKFAST = 6;

      // The lunch hour.
      LUNCH = 7;

      // The dinner hour.
      DINNER = 8;

      // The brunch hour.
      BRUNCH = 9;

      // The pickup hour.
      PICKUP = 10;

      // The access hours for storage places.
      ACCESS = 11;

      // The special hours for seniors.
      SENIOR_HOURS = 12;

      // The online service hours.
      ONLINE_SERVICE_HOURS = 13;
    }

    // Structured information for special days that fall within the period that
    // the returned opening hours cover. Special days are days that could impact
    // the business hours of a place, e.g. Christmas day.
    message SpecialDay {
      // The date of this special day.
      google.type.Date date = 1;
    }

    // Whether the opening hours period is currently active. For regular opening
    // hours and current opening hours, this field means whether the place is
    // open. For secondary opening hours and current secondary opening hours,
    // this field means whether the secondary hours of this place is active.
    optional bool open_now = 1;

    // The periods that this place is open during the week. The periods are in
    // chronological order, starting with Sunday in the place-local timezone. An
    // empty (but not absent) value indicates a place that is never open, e.g.
    // because it is closed temporarily for renovations.
    repeated Period periods = 2;

    // Localized strings describing the opening hours of this place, one string
    // for each day of the week.  Will be empty if the hours are unknown or
    // could not be converted to localized text. Example: "Sun: 18:00–06:00"
    repeated string weekday_descriptions = 3;

    // A type string used to identify the type of secondary hours.
    SecondaryHoursType secondary_hours_type = 4;

    // Structured information for special days that fall within the period that
    // the returned opening hours cover. Special days are days that could impact
    // the business hours of a place, e.g. Christmas day. Set for
    // current_opening_hours and current_secondary_opening_hours if there are
    // exceptional hours.
    repeated SpecialDay special_days = 5;

    // The next time the current opening hours period starts up to 7 days in the
    // future. This field is only populated if the opening hours period is not
    // active at the time of serving the request.
    google.protobuf.Timestamp next_open_time = 6;

    // The next time the current opening hours period ends up to 7 days in the
    // future. This field is only populated if the opening hours period is
    // active at the time of serving the request.
    google.protobuf.Timestamp next_close_time = 7;
  }

  // Business status for the place.
  enum BusinessStatus {
    // Default value. This value is unused.
    BUSINESS_STATUS_UNSPECIFIED = 0;

    // The establishment is operational, not necessarily open now.
    OPERATIONAL = 1;

    // The establishment is temporarily closed.
    CLOSED_TEMPORARILY = 2;

    // The establishment is permanently closed.
    CLOSED_PERMANENTLY = 3;
  }

  // Information about data providers of this place.
  message Attribution {
    // Name of the Place's data provider.
    string provider = 1;

    // URI to the Place's data provider.
    string provider_uri = 2;
  }

  // Payment options the place accepts.
  message PaymentOptions {
    // Place accepts credit cards as payment.
    optional bool accepts_credit_cards = 1;

    // Place accepts debit cards as payment.
    optional bool accepts_debit_cards = 2;

    // Place accepts cash only as payment. Places with this attribute may still
    // accept other payment methods.
    optional bool accepts_cash_only = 3;

    // Place accepts NFC payments.
    optional bool accepts_nfc = 4;
  }

  // Information about parking options for the place. A parking lot could
  // support more than one option at the same time.
  message ParkingOptions {
    // Place offers free parking lots.
    optional bool free_parking_lot = 1;

    // Place offers paid parking lots.
    optional bool paid_parking_lot = 2;

    // Place offers free street parking.
    optional bool free_street_parking = 3;

    // Place offers paid street parking.
    optional bool paid_street_parking = 4;

    // Place offers valet parking.
    optional bool valet_parking = 5;

    // Place offers free garage parking.
    optional bool free_garage_parking = 6;

    // Place offers paid garage parking.
    optional bool paid_garage_parking = 7;
  }

  // Place resource name and id of sub destinations that relate to the place.
  // For example, different terminals are different destinations of an airport.
  message SubDestination {
    // The resource name of the sub destination.
    string name = 1 [
      (google.api.resource_reference) = { type: "places.googleapis.com/Place" }
    ];

    // The place id of the sub destination.
    string id = 2;
  }

  // Information about the accessibility options a place offers.
  message AccessibilityOptions {
    // Place offers wheelchair accessible parking.
    optional bool wheelchair_accessible_parking = 1;

    // Places has wheelchair accessible entrance.
    optional bool wheelchair_accessible_entrance = 2;

    // Place has wheelchair accessible restroom.
    optional bool wheelchair_accessible_restroom = 3;

    // Place has wheelchair accessible seating.
    optional bool wheelchair_accessible_seating = 4;
  }

  // Experimental: See
  // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
  // for more details.
  //
  // AI-generated summary of the place.
  message GenerativeSummary {
    // The overview of the place.
    google.type.LocalizedText overview = 1;

    // The detailed description of the place.
    google.type.LocalizedText description = 2;

    // References that are used to generate the summary description.
    References references = 3;
  }

  // Experimental: See
  // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
  // for more details.
  //
  // AI-generated summary of the area that the place is in.
  message AreaSummary {
    // Content blocks that compose the area summary. Each block has a separate
    // topic about the area.
    repeated ContentBlock content_blocks = 4;
  }

  // Info about the place in which this place is located.
  message ContainingPlace {
    // The resource name of the place in which this place is located.
    string name = 1 [
      (google.api.resource_reference) = { type: "places.googleapis.com/Place" }
    ];

    // The place id of the place in which this place is located.
    string id = 2;
  }

  // This Place's resource name, in `places/{place_id}` format.  Can be used to
  // look up the Place.
  string name = 1;

  // The unique identifier of a place.
  string id = 2;

  // The localized name of the place, suitable as a short human-readable
  // description. For example, "Google Sydney", "Starbucks", "Pyrmont", etc.
  google.type.LocalizedText display_name = 31;

  // A set of type tags for this result. For example, "political" and
  // "locality". For the complete list of possible values, see Table A and Table
  // B at
  // https://developers.google.com/maps/documentation/places/web-service/place-types
  repeated string types = 5;

  // The primary type of the given result. This type must one of the Places API
  // supported types. For example, "restaurant", "cafe", "airport", etc.  A
  // place can only have a single primary type.  For the complete list of
  // possible values, see Table A and Table B at
  // https://developers.google.com/maps/documentation/places/web-service/place-types
  string primary_type = 50;

  // The display name of the primary type, localized to the request language if
  // applicable. For the complete list of possible values, see Table A and Table
  // B at
  // https://developers.google.com/maps/documentation/places/web-service/place-types
  google.type.LocalizedText primary_type_display_name = 32;

  // A human-readable phone number for the place, in national format.
  string national_phone_number = 7;

  // A human-readable phone number for the place, in international format.
  string international_phone_number = 8;

  // A full, human-readable address for this place.
  string formatted_address = 9;

  // A short, human-readable address for this place.
  string short_formatted_address = 51;

  // Repeated components for each locality level.
  // Note the following facts about the address_components[] array:
  // - The array of address components may contain more components than the
  // formatted_address.
  // - The array does not necessarily include all the political entities that
  // contain an address, apart from those included in the formatted_address. To
  // retrieve all the political entities that contain a specific address, you
  // should use reverse geocoding, passing the latitude/longitude of the address
  // as a parameter to the request.
  // - The format of the response is not guaranteed to remain the same between
  // requests. In particular, the number of address_components varies based on
  // the address requested and can change over time for the same address. A
  // component can change position in the array. The type of the component can
  // change. A particular component may be missing in a later response.
  repeated AddressComponent address_components = 10;

  // Plus code of the place location lat/long.
  PlusCode plus_code = 11;

  // The position of this place.
  google.type.LatLng location = 12;

  // A viewport suitable for displaying the place on an average-sized map. This
  // viewport should not be used as the physical boundary or the service area of
  // the business.
  google.geo.type.Viewport viewport = 13;

  // A rating between 1.0 and 5.0, based on user reviews of this place.
  double rating = 14;

  // A URL providing more information about this place.
  string google_maps_uri = 15;

  // The authoritative website for this place, e.g. a business' homepage.
  // Note that for places that are part of a chain (e.g. an IKEA store), this
  // will usually be the website for the individual store, not the overall
  // chain.
  string website_uri = 16;

  // List of reviews about this place, sorted by relevance. A maximum of 5
  // reviews can be returned.
  repeated Review reviews = 53;

  // The regular hours of operation.
  OpeningHours regular_opening_hours = 21;

  // Number of minutes this place's timezone is currently offset from UTC.
  // This is expressed in minutes to support timezones that are offset by
  // fractions of an hour, e.g. X hours and 15 minutes.
  optional int32 utc_offset_minutes = 22;

  // Information (including references) about photos of this place. A maximum of
  // 10 photos can be returned.
  repeated Photo photos = 54;

  // The place's address in adr microformat: http://microformats.org/wiki/adr.
  string adr_format_address = 24;

  BusinessStatus business_status = 25;

  // Price level of the place.
  PriceLevel price_level = 26;

  // A set of data provider that must be shown with this result.
  repeated Attribution attributions = 27;

  // The total number of reviews (with or without text) for this place.
  optional int32 user_rating_count = 28;

  // A truncated URL to an icon mask. User can access different icon type by
  // appending type suffix to the end (eg, ".svg" or ".png").
  string icon_mask_base_uri = 29;

  // Background color for icon_mask in hex format, e.g. #909CE1.
  string icon_background_color = 30;

  // Specifies if the business supports takeout.
  optional bool takeout = 33;

  // Specifies if the business supports delivery.
  optional bool delivery = 34;

  // Specifies if the business supports indoor or outdoor seating options.
  optional bool dine_in = 35;

  // Specifies if the business supports curbside pickup.
  optional bool curbside_pickup = 36;

  // Specifies if the place supports reservations.
  optional bool reservable = 38;

  // Specifies if the place serves breakfast.
  optional bool serves_breakfast = 39;

  // Specifies if the place serves lunch.
  optional bool serves_lunch = 40;

  // Specifies if the place serves dinner.
  optional bool serves_dinner = 41;

  // Specifies if the place serves beer.
  optional bool serves_beer = 42;

  // Specifies if the place serves wine.
  optional bool serves_wine = 43;

  // Specifies if the place serves brunch.
  optional bool serves_brunch = 44;

  // Specifies if the place serves vegetarian food.
  optional bool serves_vegetarian_food = 45;

  // The hours of operation for the next seven days (including today). The time
  // period starts at midnight on the date of the request and ends at 11:59 pm
  // six days later. This field includes the special_days subfield of all hours,
  // set for dates that have exceptional hours.
  OpeningHours current_opening_hours = 46;

  // Contains an array of entries for the next seven days including information
  // about secondary hours of a business. Secondary hours are different from a
  // business's main hours. For example, a restaurant can specify drive through
  // hours or delivery hours as its secondary hours. This field populates the
  // type subfield, which draws from a predefined list of opening hours types
  // (such as DRIVE_THROUGH, PICKUP, or TAKEOUT) based on the types of the
  // place. This field includes the special_days subfield of all hours, set for
  // dates that have exceptional hours.
  repeated OpeningHours current_secondary_opening_hours = 47;

  // Contains an array of entries for information about regular secondary hours
  // of a business. Secondary hours are different from a business's main hours.
  // For example, a restaurant can specify drive through hours or delivery hours
  // as its secondary hours. This field populates the type subfield, which draws
  // from a predefined list of opening hours types (such as DRIVE_THROUGH,
  // PICKUP, or TAKEOUT) based on the types of the place.
  repeated OpeningHours regular_secondary_opening_hours = 49;

  // Contains a summary of the place. A summary is comprised of a textual
  // overview, and also includes the language code for these if applicable.
  // Summary text must be presented as-is and can not be modified or altered.
  google.type.LocalizedText editorial_summary = 52;

  // Place provides outdoor seating.
  optional bool outdoor_seating = 55;

  // Place provides live music.
  optional bool live_music = 56;

  // Place has a children's menu.
  optional bool menu_for_children = 57;

  // Place serves cocktails.
  optional bool serves_cocktails = 58;

  // Place serves dessert.
  optional bool serves_dessert = 59;

  // Place serves coffee.
  optional bool serves_coffee = 60;

  // Place is good for children.
  optional bool good_for_children = 62;

  // Place allows dogs.
  optional bool allows_dogs = 63;

  // Place has restroom.
  optional bool restroom = 64;

  // Place accommodates groups.
  optional bool good_for_groups = 65;

  // Place is suitable for watching sports.
  optional bool good_for_watching_sports = 66;

  // Payment options the place accepts. If a payment option data is not
  // available, the payment option field will be unset.
  PaymentOptions payment_options = 67;

  // Options of parking provided by the place.
  ParkingOptions parking_options = 70;

  // A list of sub destinations related to the place.
  repeated SubDestination sub_destinations = 71;

  // Information about the accessibility options a place offers.
  optional AccessibilityOptions accessibility_options = 72;

  // The most recent information about fuel options in a gas station. This
  // information is updated regularly.
  FuelOptions fuel_options = 78;

  // Information of ev charging options.
  EVChargeOptions ev_charge_options = 79;

  // Experimental: See
  // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
  // for more details.
  //
  // AI-generated summary of the place.
  GenerativeSummary generative_summary = 80;

  // Experimental: See
  // https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
  // for more details.
  //
  // AI-generated summary of the area that the place is in.
  AreaSummary area_summary = 81;

  // List of places in which the current place is located.
  repeated ContainingPlace containing_places = 82;

  // Indicates whether the place is a pure service area business. Pure service
  // area business is a business that visits or delivers to customers directly
  // but does not serve customers at their business address. For example,
  // businesses like cleaning services or plumbers. Those businesses may not
  // have a physical address or location on Google Maps.
  optional bool pure_service_area_business = 83;

  // The price range associated with a Place.
  PriceRange price_range = 86;
}

// Price level of the place.
enum PriceLevel {
  // Place price level is unspecified or unknown.
  PRICE_LEVEL_UNSPECIFIED = 0;

  // Place provides free services.
  PRICE_LEVEL_FREE = 1;

  // Place provides inexpensive services.
  PRICE_LEVEL_INEXPENSIVE = 2;

  // Place provides moderately priced services.
  PRICE_LEVEL_MODERATE = 3;

  // Place provides expensive services.
  PRICE_LEVEL_EXPENSIVE = 4;

  // Place provides very expensive services.
  PRICE_LEVEL_VERY_EXPENSIVE = 5;
}
