// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/api/resource.proto";
import "google/maps/places/v1/review.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "ReferenceProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// Experimental: See
// https://developers.google.com/maps/documentation/places/web-service/experimental/places-generative
// for more details.
//
// Reference that the generative content is related to.
message References {
  // Reviews that serve as references.
  repeated Review reviews = 1;

  // The list of resource names of the referenced places. This name can be used
  // in other APIs that accept Place resource names.
  repeated string places = 2 [
    (google.api.resource_reference) = { type: "places.googleapis.com/Place" }
  ];
}
