// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.places.v1;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Maps.Places.V1";
option go_package = "cloud.google.com/go/maps/places/apiv1/placespb;placespb";
option java_multiple_files = true;
option java_outer_classname = "EvChargingProto";
option java_package = "com.google.maps.places.v1";
option objc_class_prefix = "GMPSV1";
option php_namespace = "Google\\Maps\\Places\\V1";

// Information about the EV Charge Station hosted in Place.
// Terminology follows
// https://afdc.energy.gov/fuels/electricity_infrastructure.html One port
// could charge one car at a time. One port has one or more connectors. One
// station has one or more ports.
message EVChargeOptions {
  // EV charging information grouped by [type, max_charge_rate_kw].
  // Shows EV charge aggregation of connectors that have the same type and max
  // charge rate in kw.
  message ConnectorAggregation {
    // The connector type of this aggregation.
    EVConnectorType type = 1;

    // The static max charging rate in kw of each connector in the aggregation.
    double max_charge_rate_kw = 2;

    // Number of connectors in this aggregation.
    int32 count = 3;

    // Number of connectors in this aggregation that are currently available.
    optional int32 available_count = 4;

    // Number of connectors in this aggregation that are currently out of
    // service.
    optional int32 out_of_service_count = 5;

    // The timestamp when the connector availability information in this
    // aggregation was last updated.
    google.protobuf.Timestamp availability_last_update_time = 6;
  }

  // Number of connectors at this station. However, because some ports can have
  // multiple connectors but only be able to charge one car at a time (e.g.) the
  // number of connectors may be greater than the total number of cars which can
  // charge simultaneously.
  int32 connector_count = 1;

  // A list of EV charging connector aggregations that contain connectors of the
  // same type and same charge rate.
  repeated ConnectorAggregation connector_aggregation = 2;
}

// See http://ieeexplore.ieee.org/stamp/stamp.jsp?arnumber=6872107 for
// additional information/context on EV charging connector types.
enum EVConnectorType {
  // Unspecified connector.
  EV_CONNECTOR_TYPE_UNSPECIFIED = 0;

  // Other connector types.
  EV_CONNECTOR_TYPE_OTHER = 1;

  // J1772 type 1 connector.
  EV_CONNECTOR_TYPE_J1772 = 2;

  // IEC 62196 type 2 connector. Often referred to as MENNEKES.
  EV_CONNECTOR_TYPE_TYPE_2 = 3;

  // CHAdeMO type connector.
  EV_CONNECTOR_TYPE_CHADEMO = 4;

  // Combined Charging System (AC and DC). Based on SAE.
  //  Type-1 J-1772 connector
  EV_CONNECTOR_TYPE_CCS_COMBO_1 = 5;

  // Combined Charging System (AC and DC). Based on Type-2
  // Mennekes connector
  EV_CONNECTOR_TYPE_CCS_COMBO_2 = 6;

  // The generic TESLA connector. This is NACS in the North America but can be
  // non-NACS in other parts of the world (e.g. CCS Combo 2 (CCS2) or GB/T).
  // This value is less representative of an actual connector type, and more
  // represents the ability to charge a Tesla brand vehicle at a Tesla owned
  // charging station.
  EV_CONNECTOR_TYPE_TESLA = 7;

  // GB/T type corresponds to the GB/T standard in China. This type covers all
  // GB_T types.
  EV_CONNECTOR_TYPE_UNSPECIFIED_GB_T = 8;

  // Unspecified wall outlet.
  EV_CONNECTOR_TYPE_UNSPECIFIED_WALL_OUTLET = 9;
}
