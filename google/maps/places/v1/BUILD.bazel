# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_import",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "places_proto",
    srcs = [
        "attribution.proto",
        "content_block.proto",
        "contextual_content.proto",
        "ev_charging.proto",
        "fuel_options.proto",
        "geometry.proto",
        "photo.proto",
        "place.proto",
        "places_service.proto",
        "polyline.proto",
        "price_range.proto",
        "reference.proto",
        "review.proto",
        "route_modifiers.proto",
        "routing_preference.proto",
        "routing_summary.proto",
        "travel_mode.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/geo/type:viewport_proto",
        "//google/type:date_proto",
        "//google/type:latlng_proto",
        "//google/type:localized_text_proto",
        "//google/type:money_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "places_proto_with_info",
    deps = [
        ":places_proto",
        "//google/cloud:common_resources_proto",
    ],
)

py_import(
    name = "viewport",
    srcs = [
        "//google/geo/type:viewport_py_gapic",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "places_java_proto",
    deps = [":places_proto"],
)

java_grpc_library(
    name = "places_java_grpc",
    srcs = [":places_proto"],
    deps = [":places_java_proto"],
)

java_gapic_library(
    name = "places_java_gapic",
    srcs = [":places_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "places_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "places_v1.yaml",
    test_deps = [
        ":places_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":places_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "places_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.places.v1.PlacesClientHttpJsonTest",
        "com.google.maps.places.v1.PlacesClientTest",
    ],
    runtime_deps = [":places_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-maps-places-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":places_java_gapic",
        ":places_java_grpc",
        ":places_java_proto",
        ":places_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "places_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/places/apiv1/placespb",
    protos = [":places_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/geo/type:viewport_go_proto",
        "//google/type:date_go_proto",
        "//google/type:latlng_go_proto",
        "//google/type:localized_text_go_proto",
        "//google/type:money_go_proto",
    ],
)

go_gapic_library(
    name = "places_go_gapic",
    srcs = [":places_proto_with_info"],
    grpc_service_config = "places_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/places/apiv1;places",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "places_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":places_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-maps-places-v1-go",
    deps = [
        ":places_go_gapic",
        ":places_go_gapic_srcjar-metadata.srcjar",
        ":places_go_gapic_srcjar-snippets.srcjar",
        ":places_go_gapic_srcjar-test.srcjar",
        ":places_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "places_py_gapic",
    srcs = [":places_proto"],
    grpc_service_config = "places_grpc_service_config.json",
    opt_args = ["proto-plus-deps=google.geo.type"],
    rest_numeric_enums = True,
    service_yaml = "places_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":viewport",
    ],
)

py_test(
    name = "places_py_gapic_test",
    srcs = [
        "places_py_gapic_pytest.py",
        "places_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":places_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "maps-places-v1-py",
    deps = [
        ":places_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "places_php_proto",
    deps = [":places_proto"],
)

php_gapic_library(
    name = "places_php_gapic",
    srcs = [":places_proto_with_info"],
    grpc_service_config = "places_grpc_service_config.json",
    migration_mode = "PRE_MIGRATION_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "places_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":places_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-maps-places-v1-php",
    deps = [
        ":places_php_gapic",
        ":places_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "places_nodejs_gapic",
    package_name = "@googlemaps/places",
    src = ":places_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "places_grpc_service_config.json",
    package = "google.maps.places.v1",
    rest_numeric_enums = True,
    service_yaml = "places_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "maps-places-v1-nodejs",
    deps = [
        ":places_nodejs_gapic",
        ":places_proto",
        "//google/geo/type:viewport_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "places_ruby_proto",
    deps = [":places_proto"],
)

ruby_grpc_library(
    name = "places_ruby_grpc",
    srcs = [":places_proto"],
    deps = [":places_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "places_ruby_gapic",
    srcs = [":places_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-maps-places-v1"],
    grpc_service_config = "places_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "places_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":places_ruby_grpc",
        ":places_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-maps-places-v1-ruby",
    deps = [
        ":places_ruby_gapic",
        ":places_ruby_grpc",
        ":places_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "places_csharp_proto",
    deps = [":places_proto"],
)

csharp_grpc_library(
    name = "places_csharp_grpc",
    srcs = [":places_proto"],
    deps = [":places_csharp_proto"],
)

csharp_gapic_library(
    name = "places_csharp_gapic",
    srcs = [":places_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "places_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "places_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":places_csharp_grpc",
        ":places_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-maps-places-v1-csharp",
    deps = [
        ":places_csharp_gapic",
        ":places_csharp_grpc",
        ":places_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "places_cc_proto",
    deps = [":places_proto"],
)

cc_grpc_library(
    name = "places_cc_grpc",
    srcs = [":places_proto"],
    grpc_only = True,
    deps = [":places_cc_proto"],
)
