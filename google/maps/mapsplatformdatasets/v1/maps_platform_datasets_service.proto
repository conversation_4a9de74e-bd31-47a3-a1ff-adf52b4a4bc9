// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.mapsplatformdatasets.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/maps/mapsplatformdatasets/v1/dataset.proto";
import "google/maps/mapsplatformdatasets/v1/maps_platform_datasets.proto";
import "google/protobuf/empty.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Maps.MapsPlatformDatasets.V1";
option go_package = "cloud.google.com/go/maps/mapsplatformdatasets/apiv1/mapsplatformdatasetspb;mapsplatformdatasetspb";
option java_multiple_files = true;
option java_outer_classname = "MapsPlatformDatasetsServiceProto";
option java_package = "com.google.maps.mapsplatformdatasets.v1";
option objc_class_prefix = "MDV1";
option php_namespace = "Google\\Maps\\MapsPlatformDatasets\\V1";

// Service definition for the Maps Platform Datasets API.
service MapsPlatformDatasets {
  option (google.api.default_host) = "mapsplatformdatasets.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Creates a new dataset for the specified project.
  rpc CreateDataset(CreateDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/datasets"
      body: "dataset"
    };
    option (google.api.method_signature) = "parent,dataset";
  }

  // Updates the metadata for the dataset.
  rpc UpdateDatasetMetadata(UpdateDatasetMetadataRequest) returns (Dataset) {
    option (google.api.http) = {
      patch: "/v1/{dataset.name=projects/*/datasets/*}"
      body: "dataset"
    };
    option (google.api.method_signature) = "dataset,update_mask";
  }

  // Gets the dataset.
  rpc GetDataset(GetDatasetRequest) returns (Dataset) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/datasets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Gets all the errors of a dataset.
  rpc FetchDatasetErrors(FetchDatasetErrorsRequest)
      returns (FetchDatasetErrorsResponse) {
    option (google.api.http) = {
      get: "/v1/{dataset=projects/*/datasets/*}:fetchDatasetErrors"
    };
    option (google.api.method_signature) = "dataset";
  }

  // Lists all the datasets for the specified project.
  rpc ListDatasets(ListDatasetsRequest) returns (ListDatasetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/datasets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes the specified dataset.
  rpc DeleteDataset(DeleteDatasetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/datasets/*}"
    };
    option (google.api.method_signature) = "name";
  }
}
