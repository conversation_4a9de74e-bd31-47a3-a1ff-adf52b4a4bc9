type: google.api.Service
config_version: 3
name: mapsplatformdatasets.googleapis.com
title: Maps Platform Datasets API

apis:
- name: google.maps.mapsplatformdatasets.v1.MapsPlatformDatasets

documentation:
  summary: mapsplatformdatasets.googleapis.com API.

authentication:
  rules:
  - selector: 'google.maps.mapsplatformdatasets.v1.MapsPlatformDatasets.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1356770
  documentation_uri: https://developers.google.com/maps/documentation/datasets
  api_short_name: mapsplatformdatasets
  github_label: 'api: mapsplatformdatasets'
  doc_tag_prefix: mapsplatformdatasets
  organization: GEO
  library_settings:
  - version: google.maps.mapsplatformdatasets.v1
    launch_stage: GA
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
