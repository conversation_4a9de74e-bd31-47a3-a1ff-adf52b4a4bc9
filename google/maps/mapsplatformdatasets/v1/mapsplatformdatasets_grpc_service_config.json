{"methodConfig": [{"name": [{"service": "google.maps.mapsplatformdatasets.v1.MapsPlatformDatasets", "method": "CreateDataset"}, {"service": "google.maps.mapsplatformdatasets.v1.MapsPlatformDatasets", "method": "ImportDataset"}, {"service": "google.maps.mapsplatformdatasets.v1.MapsPlatformDatasets", "method": "UpdateDatasetMetadata"}, {"service": "google.maps.mapsplatformdatasets.v1.MapsPlatformDatasets", "method": "DeleteDataset"}], "timeout": "60s"}, {"name": [{"service": "google.maps.mapsplatformdatasets.v1.MapsPlatformDatasets", "method": "DownloadDataset"}, {"service": "google.maps.mapsplatformdatasets.v1.MapsPlatformDatasets", "method": "GetDataset"}, {"service": "google.maps.mapsplatformdatasets.v1.MapsPlatformDatasets", "method": "ListDatasets"}, {"service": "google.maps.mapsplatformdatasets.v1.MapsPlatformDatasets", "method": "FetchDatasetErrors"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}