// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.mapsplatformdatasets.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/maps/mapsplatformdatasets/v1/data_source.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Maps.MapsPlatformDatasets.V1";
option go_package = "cloud.google.com/go/maps/mapsplatformdatasets/apiv1/mapsplatformdatasetspb;mapsplatformdatasetspb";
option java_multiple_files = true;
option java_outer_classname = "DatasetProto";
option java_package = "com.google.maps.mapsplatformdatasets.v1";
option objc_class_prefix = "MDV1";
option php_namespace = "Google\\Maps\\MapsPlatformDatasets\\V1";

// A representation of a dataset resource.
message Dataset {
  option (google.api.resource) = {
    type: "mapsplatformdatasets.googleapis.com/Dataset"
    pattern: "projects/{project}/datasets/{dataset}"
  };

  // Resource name.
  // Format: projects/{project}/datasets/{dataset_id}
  string name = 1;

  // Human readable name, shown in the console UI.
  //
  // Must be unique within a project.
  string display_name = 2;

  // A description of this dataset.
  string description = 3;

  // The version ID of the dataset.
  string version_id = 4;

  // Specified use case for this dataset.
  repeated Usage usage = 5;

  // Details about the source of the data for the dataset.
  oneof data_source {
    // A local file source for the dataset for a single upload.
    LocalFileSource local_file_source = 6;

    // A Google Cloud Storage file source for the dataset for a single upload.
    GcsSource gcs_source = 7;
  }

  // Output only. The status of this dataset version.
  Status status = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the dataset was first created.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when the dataset metadata was last updated.
  google.protobuf.Timestamp update_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Time when this version was created.
  google.protobuf.Timestamp version_create_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The description for this version of dataset. It is provided
  // when importing data to the dataset.
  string version_description = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Status of the dataset.
message Status {
  // A list of states for the dataset.
  enum State {
    // The state of this dataset is not set.
    STATE_UNSPECIFIED = 0;

    // Data is being imported to a dataset.
    STATE_IMPORTING = 1;

    // Importing data to a dataset succeeded.
    STATE_IMPORT_SUCCEEDED = 2;

    // Importing data to a dataset failed.
    STATE_IMPORT_FAILED = 3;

    // The dataset is in the process of getting deleted.
    STATE_DELETING = 4;

    // The deletion failed state. This state represents that dataset deletion
    // has failed. Deletion may be retried.
    STATE_DELETION_FAILED = 5;

    // Data is being processed.
    STATE_PROCESSING = 6;

    // The processing failed state. This state represents that processing has
    // failed and may report errors.
    STATE_PROCESSING_FAILED = 7;

    // This state is currently not used.
    STATE_NEEDS_REVIEW = 8;

    // The publishing state. This state represents the publishing is in
    // progress.
    STATE_PUBLISHING = 9;

    // The publishing failed states. This state represents that the publishing
    // failed. Publishing may be retried.
    STATE_PUBLISHING_FAILED = 10;

    // The completed state. This state represents the dataset being available
    // for its specific usage.
    STATE_COMPLETED = 11;
  }

  // State enum for status.
  State state = 1;

  // Error message indicating reason of failure. It is empty if the datasets is
  // not in a failed state.
  string error_message = 2;
}

// Usage specifies where the data is intended to be used to inform how to
// process the data.
enum Usage {
  // The usage of this dataset is not set.
  USAGE_UNSPECIFIED = 0;

  // This dataset will be used for data driven styling.
  USAGE_DATA_DRIVEN_STYLING = 1;
}
