// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.mapsplatformdatasets.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Maps.MapsPlatformDatasets.V1";
option go_package = "cloud.google.com/go/maps/mapsplatformdatasets/apiv1/mapsplatformdatasetspb;mapsplatformdatasetspb";
option java_multiple_files = true;
option java_outer_classname = "DataSourceProto";
option java_package = "com.google.maps.mapsplatformdatasets.v1";
option objc_class_prefix = "MDV1";
option php_namespace = "Google\\Maps\\MapsPlatformDatasets\\V1";

// The details about the data source when it is a local file.
message LocalFileSource {
  // The file name of the uploaded file.
  string filename = 1;

  // The format of the file that is being uploaded.
  FileFormat file_format = 2;
}

// The details about the data source when it is in Google Cloud Storage.
message GcsSource {
  // Source data URI. For example, `gs://my_bucket/my_object`.
  string input_uri = 1;

  // The file format of the Google Cloud Storage object. This is used mainly for
  // validation.
  FileFormat file_format = 2;
}

// The format of the file being uploaded.
enum FileFormat {
  // Unspecified file format.
  FILE_FORMAT_UNSPECIFIED = 0;

  // GeoJson file.
  FILE_FORMAT_GEOJSON = 1;

  // KML file.
  FILE_FORMAT_KML = 2;

  // CSV file.
  FILE_FORMAT_CSV = 3;
}
