// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.mapsplatformdatasets.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/maps/mapsplatformdatasets/v1/dataset.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Maps.MapsPlatformDatasets.V1";
option go_package = "cloud.google.com/go/maps/mapsplatformdatasets/apiv1/mapsplatformdatasetspb;mapsplatformdatasetspb";
option java_multiple_files = true;
option java_outer_classname = "MapsPlatformDatasetsProto";
option java_package = "com.google.maps.mapsplatformdatasets.v1";
option objc_class_prefix = "MDV1";
option php_namespace = "Google\\Maps\\MapsPlatformDatasets\\V1";

// Request to create a maps dataset.
message CreateDatasetRequest {
  // Required. Parent project that will own the dataset.
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The dataset version to create.
  Dataset dataset = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request to update the metadata fields of the dataset.
message UpdateDatasetMetadataRequest {
  // Required. Resource name of the dataset to update.
  // Format: projects/{project}/datasets/{dataset_id}
  Dataset dataset = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to be updated.
  //
  // The value "*" is used for full replacement (default).
  google.protobuf.FieldMask update_mask = 2;
}

// Request to get the specified dataset.
message GetDatasetRequest {
  // Required. Resource name.
  // Format: projects/{project}/datasets/{dataset_id}
  //
  //
  // Can also fetch some special versions by appending "@" and a tag.
  // Format: projects/{project}/datasets/{dataset_id}@{tag}
  //
  // Tag "active": The info of the latest completed version will be included,
  // and NOT_FOUND if the dataset does not have one.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "mapsplatformdatasets.googleapis.com/Dataset"
    }
  ];
}

// Request to list datasets for the project.
message ListDatasetsRequest {
  // Required. The name of the project to list all the datasets for.
  // Format: projects/{project}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // The maximum number of datasets to return per page.
  //
  // If unspecified (or zero), all datasets will be returned.
  int32 page_size = 2;

  // The page token, received from a previous ListDatasets call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3;

  // The tag that specifies the desired version for each dataset.
  //
  // Note that when pagination is also specified, some filtering can happen
  // after pagination, which may cause the response to contain fewer datasets
  // than the page size, even if it's not the last page.
  //
  // Tag "active": Each dataset in the response will include the info of its
  // latest completed version, and the dataset will be skipped if it does not
  // have one.
  string tag = 4;
}

// Response object of ListDatasets.
message ListDatasetsResponse {
  // All the datasets for the project.
  repeated Dataset datasets = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  //
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request to list detailed errors belonging to a dataset.
message FetchDatasetErrorsRequest {
  // Required. The name of the dataset to list all the errors for.
  // Format: projects/{project}/datasets/{dataset_id}
  string dataset = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "mapsplatformdatasets.googleapis.com/Dataset"
    }
  ];

  // The maximum number of errors to return per page.
  //
  // The maximum value is 500; values above 500 will be capped to 500.
  //
  // If unspecified, at most 50 errors will be returned.
  int32 page_size = 2;

  // The page token, received from a previous ListDatasetErrors call.
  // Provide this to retrieve the subsequent page.
  string page_token = 3;
}

// Response object of FetchDatasetErrors.
message FetchDatasetErrorsResponse {
  // A token that can be sent as `page_token` to retrieve the next page.
  //
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // The errors associated with a dataset.
  repeated google.rpc.Status errors = 3;
}

// Request to delete a dataset.
message DeleteDatasetRequest {
  // Required. The name of the dataset to delete.
  // Format: projects/{project}/datasets/{dataset_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "mapsplatformdatasets.googleapis.com/Dataset"
    }
  ];
}
