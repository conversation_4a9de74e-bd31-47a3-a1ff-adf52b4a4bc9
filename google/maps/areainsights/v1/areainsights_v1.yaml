type: google.api.Service
config_version: 3
name: areainsights.googleapis.com
title: Places Insights API

apis:
- name: google.maps.areainsights.v1.AreaInsights

documentation:
  summary: Places Insights API.

authentication:
  rules:
  - selector: google.maps.areainsights.v1.AreaInsights.ComputeInsights
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1624013&template=2026178
  documentation_uri: https://developers.google.com/maps/documentation/places-insights
  api_short_name: area-insights
  github_label: 'api: area_insights'
  doc_tag_prefix: area_insights
  organization: GEO
  library_settings:
  - version: google.maps.areainsights.v1
    launch_stage: BETA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
