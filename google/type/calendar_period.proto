// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.type;

option go_package = "google.golang.org/genproto/googleapis/type/calendarperiod;calendarperiod";
option java_multiple_files = true;
option java_outer_classname = "CalendarPeriodProto";
option java_package = "com.google.type";
option objc_class_prefix = "GTP";

// A `CalendarPeriod` represents the abstract concept of a time period that has
// a canonical start. Grammatically, "the start of the current
// `CalendarPeriod`." All calendar times begin at midnight UTC.
enum CalendarPeriod {
  // Undefined period, raises an error.
  CALENDAR_PERIOD_UNSPECIFIED = 0;

  // A day.
  DAY = 1;

  // A week. Weeks begin on Monday, following
  // [ISO 8601](https://en.wikipedia.org/wiki/ISO_week_date).
  WEEK = 2;

  // A fortnight. The first calendar fortnight of the year begins at the start
  // of week 1 according to
  // [ISO 8601](https://en.wikipedia.org/wiki/ISO_week_date).
  FORTNIGHT = 3;

  // A month.
  MONTH = 4;

  // A quarter. Quarters start on dates 1-Jan, 1-Apr, 1-Jul, and 1-Oct of each
  // year.
  QUARTER = 5;

  // A half-year. Half-years start on dates 1-Jan and 1-Jul.
  HALF = 6;

  // A year.
  YEAR = 7;
}
