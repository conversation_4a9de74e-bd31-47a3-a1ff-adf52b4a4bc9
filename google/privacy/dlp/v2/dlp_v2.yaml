type: google.api.Service
config_version: 3
name: dlp.googleapis.com
title: Sensitive Data Protection (DLP)

apis:
- name: google.cloud.location.Locations
- name: google.privacy.dlp.v2.DlpService

types:
- name: google.privacy.dlp.v2.DataProfileBigQueryRowSchema
- name: google.privacy.dlp.v2.DataProfilePubSubMessage
- name: google.privacy.dlp.v2.TransformationDetails

documentation:
  summary: |-
    Discover and protect your sensitive data. A fully managed service designed
    to help you discover, classify, and protect your valuable data assets with
    ease.

authentication:
  rules:
  - selector: 'google.privacy.dlp.v2.DlpService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
