// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.marketingplatform.admin.v1alpha;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/marketingplatform/admin/v1alpha/resources.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Google.Ads.MarketingPlatform.Admin.V1Alpha";
option go_package = "google.golang.org/genproto/googleapis/marketingplatform/admin/v1alpha;admin";
option java_multiple_files = true;
option java_outer_classname = "MarketingplatformAdminProto";
option java_package = "com.google.ads.marketingplatform.admin.v1alpha";
option php_namespace = "Google\\Ads\\MarketingPlatform\\Admin\\V1alpha";
option ruby_package = "Google::Ads::MarketingPlatform::Admin::V1alpha";
option (google.api.resource_definition) = {
  type: "analyticsadmin.googleapis.com/Property"
  pattern: "properties/{property}"
};

// Service Interface for the Google Marketing Platform Admin API.
service MarketingplatformAdminService {
  option (google.api.default_host) = "marketingplatformadmin.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/marketingplatformadmin.analytics.read,"
      "https://www.googleapis.com/auth/marketingplatformadmin.analytics.update";

  // Lookup for a single organization.
  rpc GetOrganization(GetOrganizationRequest) returns (Organization) {
    option (google.api.http) = {
      get: "/v1alpha/{name=organizations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the Google Analytics accounts link to the specified Google Marketing
  // Platform organization.
  rpc ListAnalyticsAccountLinks(ListAnalyticsAccountLinksRequest)
      returns (ListAnalyticsAccountLinksResponse) {
    option (google.api.http) = {
      get: "/v1alpha/{parent=organizations/*}/analyticsAccountLinks"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates the link between the Analytics account and the Google Marketing
  // Platform organization.
  //
  // User needs to be an org user, and admin on the Analytics account to create
  // the link. If the account is already linked to an organization, user needs
  // to unlink the account from the current organization, then try link again.
  rpc CreateAnalyticsAccountLink(CreateAnalyticsAccountLinkRequest)
      returns (AnalyticsAccountLink) {
    option (google.api.http) = {
      post: "/v1alpha/{parent=organizations/*}/analyticsAccountLinks"
      body: "analytics_account_link"
    };
    option (google.api.method_signature) = "parent,analytics_account_link";
  }

  // Deletes the AnalyticsAccountLink, which detaches the Analytics account from
  // the Google Marketing Platform organization.
  //
  // User needs to be an org user, and admin on the Analytics account in order
  // to delete the link.
  rpc DeleteAnalyticsAccountLink(DeleteAnalyticsAccountLinkRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha/{name=organizations/*/analyticsAccountLinks/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the service level for an Analytics property.
  rpc SetPropertyServiceLevel(SetPropertyServiceLevelRequest)
      returns (SetPropertyServiceLevelResponse) {
    option (google.api.http) = {
      post: "/v1alpha/{analytics_account_link=organizations/*/analyticsAccountLinks/*}:setPropertyServiceLevel"
      body: "*"
    };
    option (google.api.method_signature) = "analytics_account_link";
  }
}

// Various levels of service for Google Analytics.
enum AnalyticsServiceLevel {
  // Service level unspecified.
  ANALYTICS_SERVICE_LEVEL_UNSPECIFIED = 0;

  // The standard version of Google Analytics.
  ANALYTICS_SERVICE_LEVEL_STANDARD = 1;

  // The premium version of Google Analytics.
  ANALYTICS_SERVICE_LEVEL_360 = 2;
}

// Request message for GetOrganization RPC.
message GetOrganizationRequest {
  // Required. The name of the Organization to retrieve.
  // Format: organizations/{org_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "marketingplatformadmin.googleapis.com/Organization"
    }
  ];
}

// Request message for ListAnalyticsAccountLinks RPC.
message ListAnalyticsAccountLinksRequest {
  // Required. The parent organization, which owns this collection of Analytics
  // account links. Format: organizations/{org_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "marketingplatformadmin.googleapis.com/AnalyticsAccountLink"
    }
  ];

  // Optional. The maximum number of Analytics account links to return in one
  // call. The service may return fewer than this value.
  //
  // If unspecified, at most 50 Analytics account links will be returned. The
  // maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous ListAnalyticsAccountLinks
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListAnalyticsAccountLinks` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListAnalyticsAccountLinks RPC.
message ListAnalyticsAccountLinksResponse {
  // Analytics account links in this organization.
  repeated AnalyticsAccountLink analytics_account_links = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for CreateAnalyticsAccountLink RPC.
message CreateAnalyticsAccountLinkRequest {
  // Required. The parent resource where this Analytics account link will be
  // created. Format: organizations/{org_id}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "marketingplatformadmin.googleapis.com/AnalyticsAccountLink"
    }
  ];

  // Required. The Analytics account link to create.
  AnalyticsAccountLink analytics_account_link = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request message for DeleteAnalyticsAccountLink RPC.
message DeleteAnalyticsAccountLinkRequest {
  // Required. The name of the Analytics account link to delete.
  // Format:
  // organizations/{org_id}/analyticsAccountLinks/{analytics_account_link_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "marketingplatformadmin.googleapis.com/AnalyticsAccountLink"
    }
  ];
}

// Request message for SetPropertyServiceLevel RPC.
message SetPropertyServiceLevelRequest {
  // Required. The parent AnalyticsAccountLink scope where this property is in.
  // Format:
  // organizations/{org_id}/analyticsAccountLinks/{analytics_account_link_id}
  string analytics_account_link = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The Analytics property to change the ServiceLevel setting. This
  // field is the name of the Google Analytics Admin API property resource.
  //
  // Format: analyticsadmin.googleapis.com/properties/{property_id}
  string analytics_property = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "analyticsadmin.googleapis.com/Property"
    }
  ];

  // Required. The service level to set for this property.
  AnalyticsServiceLevel service_level = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for SetPropertyServiceLevel RPC.
message SetPropertyServiceLevelResponse {}
