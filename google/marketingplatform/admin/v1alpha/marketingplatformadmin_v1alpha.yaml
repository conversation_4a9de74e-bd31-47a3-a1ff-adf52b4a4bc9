type: google.api.Service
config_version: 3
name: marketingplatformadmin.googleapis.com
title: Google Marketing Platform Admin API

apis:
- name: google.marketingplatform.admin.v1alpha.MarketingplatformAdminService

documentation:
  summary: |-
    The Google Marketing Platform Admin API allows for programmatic access to
    the Google Marketing Platform configuration data. You can use the Google
    Marketing Platform Admin API to manage links between your Google Marketing
    Platform organization and Google Analytics accounts, and to set the
    service level of your GA4 properties.

authentication:
  rules:
  - selector: 'google.marketingplatform.admin.v1alpha.MarketingplatformAdminService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/marketingplatformadmin.analytics.update
  - selector: google.marketingplatform.admin.v1alpha.MarketingplatformAdminService.GetOrganization
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/marketingplatformadmin.analytics.read,
        https://www.googleapis.com/auth/marketingplatformadmin.analytics.update
  - selector: google.marketingplatform.admin.v1alpha.MarketingplatformAdminService.ListAnalyticsAccountLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/marketingplatformadmin.analytics.read,
        https://www.googleapis.com/auth/marketingplatformadmin.analytics.update

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=1603054
  documentation_uri: https://developers.google.com/analytics/devguides/config/gmp/v1
  api_short_name: marketingplatformadminapi
  github_label: 'api: library'
  doc_tag_prefix: library
  organization: ADS
  library_settings:
  - version: google.marketingplatform.admin.v1alpha
    launch_stage: ALPHA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
  proto_reference_documentation_uri: https://cloud.google.com/library/docs/reference/rpc
