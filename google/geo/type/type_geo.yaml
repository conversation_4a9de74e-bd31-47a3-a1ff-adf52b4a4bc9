type: google.api.Service
config_version: 1
name: type.geo.googleapis.com
title: Common Geo Types

types:
- name: google.geo.type.Viewport

documentation:
  summary: Defines common types for Google Geo APIs.
  overview: |-
    # Google Common Geo Types

    This package contains definitions of common types for Google Geo APIs. All
    types defined in this package are suitable for different APIs to exchange
    data, and will never break binary compatibility. They should have design
    quality
    comparable to major programming languages like Java and C#.
