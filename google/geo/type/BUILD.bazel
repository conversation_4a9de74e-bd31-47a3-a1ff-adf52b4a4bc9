load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_proto_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_proto_library",
)

package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
proto_library(
    name = "viewport_proto",
    srcs = ["viewport.proto"],
    deps = ["//google/type:latlng_proto"],
)

##############################################################################
# Java
##############################################################################
java_proto_library(
    name = "viewport_java_proto",
    deps = [
        ":viewport_proto",
    ],
)

# Please DO-NOT-REMOVE this section.
# This is required to generate java files for these protos.
# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-geo-type-java",
    transport = "grpc+rest",
    deps = [
        ":viewport_java_proto",
        ":viewport_proto",
    ],
)

##############################################################################
# Go
##############################################################################
go_proto_library(
    name = "viewport_go_proto",
    importpath = "google.golang.org/genproto/googleapis/geo/type/viewport",
    protos = [":viewport_proto"],
    deps = ["//google/type:latlng_go_proto"],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_proto_library",
)

py_gapic_library(
    name = "viewport_py_gapic",
    srcs = [":viewport_proto"],
    rest_numeric_enums = False,
    transport = "grpc",
)

py_proto_library(
    name = "viewport_py_proto",
    deps = [":viewport_proto"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "viewport-py",
    deps = [
        ":viewport_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_proto_library",
)

php_proto_library(
    name = "viewport_php_proto",
    deps = [":viewport_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "viewport-php",
    deps = [
        ":viewport_php_proto",
    ],
)

##############################################################################
# C++
##############################################################################
cc_proto_library(
    name = "viewport_cc_proto",
    deps = [":viewport_proto"],
)
