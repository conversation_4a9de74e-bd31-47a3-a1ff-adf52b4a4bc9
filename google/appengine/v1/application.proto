// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1;

import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.AppEngine.V1";
option go_package = "cloud.google.com/go/appengine/apiv1/appenginepb;appenginepb";
option java_multiple_files = true;
option java_outer_classname = "ApplicationProto";
option java_package = "com.google.appengine.v1";
option php_namespace = "Google\\Cloud\\AppEngine\\V1";
option ruby_package = "Google::Cloud::AppEngine::V1";

// An Application resource contains the top-level configuration of an App
// Engine application.
message Application {
  enum ServingStatus {
    // Serving status is unspecified.
    UNSPECIFIED = 0;

    // Application is serving.
    SERVING = 1;

    // Application has been disabled by the user.
    USER_DISABLED = 2;

    // Application has been disabled by the system.
    SYSTEM_DISABLED = 3;
  }

  // Identity-Aware Proxy
  message IdentityAwareProxy {
    // Whether the serving infrastructure will authenticate and
    // authorize all incoming requests.
    //
    // If true, the `oauth2_client_id` and `oauth2_client_secret`
    // fields must be non-empty.
    bool enabled = 1;

    // OAuth2 client ID to use for the authentication flow.
    string oauth2_client_id = 2;

    // OAuth2 client secret to use for the authentication flow.
    //
    // For security reasons, this value cannot be retrieved via the API.
    // Instead, the SHA-256 hash of the value is returned in the
    // `oauth2_client_secret_sha256` field.
    //
    // @InputOnly
    string oauth2_client_secret = 3;

    // Hex-encoded SHA-256 hash of the client secret.
    //
    // @OutputOnly
    string oauth2_client_secret_sha256 = 4;
  }

  enum DatabaseType {
    // Database type is unspecified.
    DATABASE_TYPE_UNSPECIFIED = 0;

    // Cloud Datastore
    CLOUD_DATASTORE = 1;

    // Cloud Firestore Native
    CLOUD_FIRESTORE = 2;

    // Cloud Firestore in Datastore Mode
    CLOUD_DATASTORE_COMPATIBILITY = 3;
  }

  // The feature specific settings to be used in the application. These define
  // behaviors that are user configurable.
  message FeatureSettings {
    // Boolean value indicating if split health checks should be used instead
    // of the legacy health checks. At an app.yaml level, this means defaulting
    // to 'readiness_check' and 'liveness_check' values instead of
    // 'health_check' ones. Once the legacy 'health_check' behavior is
    // deprecated, and this value is always true, this setting can
    // be removed.
    bool split_health_checks = 1;

    // If true, use [Container-Optimized OS](https://cloud.google.com/container-optimized-os/)
    // base image for VMs, rather than a base Debian image.
    bool use_container_optimized_os = 2;
  }

  // Full path to the Application resource in the API.
  // Example: `apps/myapp`.
  //
  // @OutputOnly
  string name = 1;

  // Identifier of the Application resource. This identifier is equivalent
  // to the project ID of the Google Cloud Platform project where you want to
  // deploy your application.
  // Example: `myapp`.
  string id = 2;

  // HTTP path dispatch rules for requests to the application that do not
  // explicitly target a service or version. Rules are order-dependent.
  // Up to 20 dispatch rules can be supported.
  repeated UrlDispatchRule dispatch_rules = 3;

  // Google Apps authentication domain that controls which users can access
  // this application.
  //
  // Defaults to open access for any Google Account.
  string auth_domain = 6;

  // Location from which this application runs. Application instances
  // run out of the data centers in the specified location, which is also where
  // all of the application's end user content is stored.
  //
  // Defaults to `us-central`.
  //
  // View the list of
  // [supported locations](https://cloud.google.com/appengine/docs/locations).
  string location_id = 7;

  // Google Cloud Storage bucket that can be used for storing files
  // associated with this application. This bucket is associated with the
  // application and can be used by the gcloud deployment commands.
  //
  // @OutputOnly
  string code_bucket = 8;

  // Cookie expiration policy for this application.
  google.protobuf.Duration default_cookie_expiration = 9;

  // Serving status of this application.
  ServingStatus serving_status = 10;

  // Hostname used to reach this application, as resolved by App Engine.
  //
  // @OutputOnly
  string default_hostname = 11;

  // Google Cloud Storage bucket that can be used by this application to store
  // content.
  //
  // @OutputOnly
  string default_bucket = 12;

  // The service account associated with the application.
  // This is the app-level default identity. If no identity provided during
  // create version, Admin API will fallback to this one.
  string service_account = 13;

  IdentityAwareProxy iap = 14;

  // The Google Container Registry domain used for storing managed build docker
  // images for this application.
  string gcr_domain = 16;

  // The type of the Cloud Firestore or Cloud Datastore database associated with
  // this application.
  DatabaseType database_type = 17;

  // The feature specific settings to be used in the application.
  FeatureSettings feature_settings = 18;
}

// Rules to match an HTTP request and dispatch that request to a service.
message UrlDispatchRule {
  // Domain name to match against. The wildcard "`*`" is supported if
  // specified before a period: "`*.`".
  //
  // Defaults to matching all domains: "`*`".
  string domain = 1;

  // Pathname within the host. Must start with a "`/`". A
  // single "`*`" can be included at the end of the path.
  //
  // The sum of the lengths of the domain and path may not
  // exceed 100 characters.
  string path = 2;

  // Resource ID of a service in this application that should
  // serve the matched request. The service must already
  // exist. Example: `default`.
  string service = 3;
}
