# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "appengine_proto",
    srcs = [
        "app_yaml.proto",
        "appengine.proto",
        "application.proto",
        "audit_data.proto",
        "certificate.proto",
        "deploy.proto",
        "deployed_files.proto",
        "domain.proto",
        "domain_mapping.proto",
        "firewall.proto",
        "instance.proto",
        "location.proto",
        "network_settings.proto",
        "operation.proto",
        "service.proto",
        "version.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "appengine_proto_with_info",
    deps = [
        ":appengine_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "appengine_java_proto",
    deps = [":appengine_proto"],
)

java_grpc_library(
    name = "appengine_java_grpc",
    srcs = [":appengine_proto"],
    deps = [":appengine_java_proto"],
)

java_gapic_library(
    name = "appengine_java_gapic",
    srcs = [":appengine_proto_with_info"],
    grpc_service_config = "appengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "appengine_v1.yaml",
    test_deps = [
        ":appengine_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":appengine_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "appengine_java_gapic_test_suite",
    test_classes = [
        "com.google.appengine.v1.ApplicationsClientHttpJsonTest",
        "com.google.appengine.v1.ApplicationsClientTest",
        "com.google.appengine.v1.AuthorizedCertificatesClientHttpJsonTest",
        "com.google.appengine.v1.AuthorizedCertificatesClientTest",
        "com.google.appengine.v1.AuthorizedDomainsClientHttpJsonTest",
        "com.google.appengine.v1.AuthorizedDomainsClientTest",
        "com.google.appengine.v1.DomainMappingsClientHttpJsonTest",
        "com.google.appengine.v1.DomainMappingsClientTest",
        "com.google.appengine.v1.FirewallClientHttpJsonTest",
        "com.google.appengine.v1.FirewallClientTest",
        "com.google.appengine.v1.InstancesClientHttpJsonTest",
        "com.google.appengine.v1.InstancesClientTest",
        "com.google.appengine.v1.ServicesClientHttpJsonTest",
        "com.google.appengine.v1.ServicesClientTest",
        "com.google.appengine.v1.VersionsClientHttpJsonTest",
        "com.google.appengine.v1.VersionsClientTest",
    ],
    runtime_deps = [":appengine_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-appengine-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":appengine_java_gapic",
        ":appengine_java_grpc",
        ":appengine_java_proto",
        ":appengine_proto",
    ],
)

go_proto_library(
    name = "appengine_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/appengine/apiv1/appenginepb",
    protos = [":appengine_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "appengine_go_gapic",
    srcs = [":appengine_proto_with_info"],
    grpc_service_config = "appengine_grpc_service_config.json",
    importpath = "cloud.google.com/go/appengine/apiv1;appengine",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "appengine_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":appengine_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-appengine-v1-go",
    deps = [
        ":appengine_go_gapic",
        ":appengine_go_gapic_srcjar-metadata.srcjar",
        ":appengine_go_gapic_srcjar-snippets.srcjar",
        ":appengine_go_gapic_srcjar-test.srcjar",
        ":appengine_go_proto",
    ],
)

py_gapic_library(
    name = "appengine_py_gapic",
    srcs = [":appengine_proto"],
    grpc_service_config = "appengine_grpc_service_config.json",
    opt_args = [
        "warehouse-package-name=google-cloud-appengine-admin",
        "python-gapic-namespace=google.cloud",
        "python-gapic-name=appengine_admin",
    ],
    rest_numeric_enums = True,
    service_yaml = "appengine_v1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "appengine_py_gapic_test",
    srcs = [
        "appengine_py_gapic_pytest.py",
        "appengine_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":appengine_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "google-cloud-appengine-v1-py",
    deps = [
        ":appengine_py_gapic",
    ],
)

php_proto_library(
    name = "appengine_php_proto",
    deps = [":appengine_proto"],
)

php_gapic_library(
    name = "appengine_php_gapic",
    srcs = [":appengine_proto_with_info"],
    grpc_service_config = "appengine_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "appengine_v1.yaml",
    transport = "grpc+rest",
    deps = [":appengine_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-appengine-v1-php",
    deps = [
        ":appengine_php_gapic",
        ":appengine_php_proto",
    ],
)

nodejs_gapic_library(
    name = "appengine_nodejs_gapic",
    package_name = "@google-cloud/appengine-admin",
    src = ":appengine_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "appengine_grpc_service_config.json",
    package = "google.appengine.v1",
    rest_numeric_enums = True,
    service_yaml = "appengine_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "appengine-v1-nodejs",
    deps = [
        ":appengine_nodejs_gapic",
        ":appengine_proto",
    ],
)

ruby_proto_library(
    name = "appengine_ruby_proto",
    deps = [":appengine_proto"],
)

ruby_grpc_library(
    name = "appengine_ruby_grpc",
    srcs = [":appengine_proto"],
    deps = [":appengine_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "appengine_ruby_gapic",
    srcs = [":appengine_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-app_engine-v1",
        "ruby-cloud-env-prefix=APP_ENGINE",
        "ruby-cloud-product-url=https://cloud.google.com/appengine/docs/admin-api/",
        "ruby-cloud-api-id=appengine.googleapis.com",
        "ruby-cloud-api-shortname=appengine",
    ],
    rest_numeric_enums = True,
    ruby_cloud_description = "The App Engine Admin API provisions and manages your App Engine applications.",
    ruby_cloud_title = "App Engine Admin V1",
    service_yaml = "appengine_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":appengine_ruby_grpc",
        ":appengine_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-appengine-v1-ruby",
    deps = [
        ":appengine_ruby_gapic",
        ":appengine_ruby_grpc",
        ":appengine_ruby_proto",
    ],
)

csharp_proto_library(
    name = "appengine_csharp_proto",
    deps = [":appengine_proto"],
)

csharp_grpc_library(
    name = "appengine_csharp_grpc",
    srcs = [":appengine_proto"],
    deps = [":appengine_csharp_proto"],
)

csharp_gapic_library(
    name = "appengine_csharp_gapic",
    srcs = [":appengine_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "appengine_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "appengine_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":appengine_csharp_grpc",
        ":appengine_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-appengine-v1-csharp",
    deps = [
        ":appengine_csharp_gapic",
        ":appengine_csharp_grpc",
        ":appengine_csharp_proto",
    ],
)

cc_proto_library(
    name = "appengine_cc_proto",
    deps = [":appengine_proto"],
)

cc_grpc_library(
    name = "appengine_cc_grpc",
    srcs = [":appengine_proto"],
    grpc_only = True,
    deps = [":appengine_cc_proto"],
)
