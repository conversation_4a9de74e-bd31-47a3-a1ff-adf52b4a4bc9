type: google.api.Service
config_version: 3
name: appengine.googleapis.com
title: App Engine Admin API

apis:
- name: google.appengine.v1.Applications
- name: google.appengine.v1.AuthorizedCertificates
- name: google.appengine.v1.AuthorizedDomains
- name: google.appengine.v1.DomainMappings
- name: google.appengine.v1.Firewall
- name: google.appengine.v1.Instances
- name: google.appengine.v1.Services
- name: google.appengine.v1.Versions

types:
- name: google.appengine.v1.LocationMetadata
- name: google.appengine.v1.OperationMetadataV1

documentation:
  summary: Provisions and manages developers' App Engine applications.
  overview: |-
    # Google App Engine Admin API

    ## Overview

    The Google App Engine Admin API is a RESTful API for managing App Engine
    applications. The Admin API provides programmatic access to several of the
    App Engine administrative operations that are found in the [Google Cloud
    Platform Console](https://console.cloud.google.com/appengine).

    ## Documentation

    [Google App Engine Admin API
    Documentation](https://cloud.google.com/appengine/docs/admin-api/)
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: 'google.appengine.v1.Applications.*'
    deadline: 30.0
  - selector: 'google.appengine.v1.AuthorizedCertificates.*'
    deadline: 30.0
  - selector: google.appengine.v1.AuthorizedCertificates.GetAuthorizedCertificate
    deadline: 60.0
  - selector: google.appengine.v1.AuthorizedDomains.ListAuthorizedDomains
    deadline: 30.0
  - selector: 'google.appengine.v1.DomainMappings.*'
    deadline: 30.0
  - selector: google.appengine.v1.DomainMappings.GetDomainMapping
    deadline: 60.0
  - selector: google.appengine.v1.DomainMappings.ListDomainMappings
    deadline: 60.0
  - selector: 'google.appengine.v1.Firewall.*'
    deadline: 30.0
  - selector: 'google.appengine.v1.Instances.*'
    deadline: 30.0
  - selector: google.appengine.v1.Instances.ListInstances
    deadline: 60.0
  - selector: 'google.appengine.v1.Services.*'
    deadline: 30.0
  - selector: 'google.appengine.v1.Versions.*'
    deadline: 30.0
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 30.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 30.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 30.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v1/{name=apps/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v1/{name=apps/*}/locations'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v1/{name=apps/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v1/{name=apps/*}/operations'

authentication:
  rules:
  - selector: 'google.appengine.v1.Applications.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.appengine.v1.Applications.GetApplication
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.appengine.v1.AuthorizedCertificates.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.appengine.v1.AuthorizedCertificates.GetAuthorizedCertificate
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.appengine.v1.AuthorizedCertificates.ListAuthorizedCertificates
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.appengine.v1.AuthorizedDomains.ListAuthorizedDomains
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.appengine.v1.DomainMappings.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.appengine.v1.DomainMappings.GetDomainMapping
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.appengine.v1.DomainMappings.ListDomainMappings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: 'google.appengine.v1.Firewall.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.appengine.v1.Firewall.GetIngressRule
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.appengine.v1.Firewall.ListIngressRules
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.appengine.v1.Instances.DebugInstance
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.appengine.v1.Instances.DeleteInstance
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.appengine.v1.Instances.GetInstance
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.appengine.v1.Instances.ListInstances
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.appengine.v1.Services.DeleteService
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.appengine.v1.Services.GetService
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.appengine.v1.Services.ListServices
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.appengine.v1.Services.UpdateService
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.appengine.v1.Versions.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.appengine.v1.Versions.GetVersion
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.appengine.v1.Versions.ListVersions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.longrunning.Operations.ListOperations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/appengine.admin,
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only
  - selector: google.longrunning.Operations.WaitOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
