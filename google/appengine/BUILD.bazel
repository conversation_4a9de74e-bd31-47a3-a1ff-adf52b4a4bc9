# This build file includes a target for the Ruby wrapper library for
# google-cloud-app_engine.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for appengine.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "appengine_ruby_wrapper",
    srcs = ["//google/appengine/v1:appengine_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-app_engine",
        "ruby-cloud-env-prefix=APP_ENGINE",
        "ruby-cloud-wrapper-of=v1:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/appengine/docs/admin-api/",
        "ruby-cloud-api-id=appengine.googleapis.com",
        "ruby-cloud-api-shortname=appengine",
    ],
    ruby_cloud_description = "The App Engine Admin API provisions and manages your App Engine applications.",
    ruby_cloud_title = "App Engine Admin",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-appengine-ruby",
    deps = [
        ":appengine_ruby_wrapper",
    ],
)
