// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1beta;

option csharp_namespace = "Google.Cloud.AppEngine.V1Beta";
option go_package = "google.golang.org/genproto/googleapis/appengine/v1beta;appengine";
option java_multiple_files = true;
option java_outer_classname = "DomainProto";
option java_package = "com.google.appengine.v1beta";
option php_namespace = "Google\\Cloud\\AppEngine\\V1beta";
option ruby_package = "Google::Cloud::AppEngine::V1beta";

// A domain that a user has been authorized to administer. To authorize use
// of a domain, verify ownership via
// [Search Console](https://search.google.com/search-console/welcome).
message AuthorizedDomain {
  // Full path to the `AuthorizedDomain` resource in the API. Example:
  // `apps/myapp/authorizedDomains/example.com`.
  //
  // @OutputOnly
  string name = 1;

  // Fully qualified domain name of the domain authorized for use. Example:
  // `example.com`.
  string id = 2;
}
