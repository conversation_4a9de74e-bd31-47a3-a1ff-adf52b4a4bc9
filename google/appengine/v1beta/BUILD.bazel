# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "appengine_proto",
    srcs = [
        "app_yaml.proto",
        "appengine.proto",
        "application.proto",
        "audit_data.proto",
        "certificate.proto",
        "deploy.proto",
        "domain.proto",
        "domain_mapping.proto",
        "firewall.proto",
        "instance.proto",
        "location.proto",
        "network_settings.proto",
        "operation.proto",
        "service.proto",
        "version.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "appengine_java_proto",
    deps = [":appengine_proto"],
)

java_grpc_library(
    name = "appengine_java_grpc",
    srcs = [":appengine_proto"],
    deps = [":appengine_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "appengine_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/appengine/v1beta",
    protos = [":appengine_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "appengine_moved_proto",
    srcs = [":appengine_proto"],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "appengine_py_proto",
    deps = [":appengine_moved_proto"],
)

py_grpc_library(
    name = "appengine_py_grpc",
    srcs = [":appengine_moved_proto"],
    deps = [":appengine_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "appengine_php_proto",
    deps = [":appengine_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "appengine_ruby_proto",
    deps = [":appengine_proto"],
)

ruby_grpc_library(
    name = "appengine_ruby_grpc",
    srcs = [":appengine_proto"],
    deps = [":appengine_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "appengine_csharp_proto",
    deps = [":appengine_proto"],
)

csharp_grpc_library(
    name = "appengine_csharp_grpc",
    srcs = [":appengine_proto"],
    deps = [":appengine_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
