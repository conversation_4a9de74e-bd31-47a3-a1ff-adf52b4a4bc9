// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1beta;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.AppEngine.V1Beta";
option go_package = "google.golang.org/genproto/googleapis/appengine/v1beta;appengine";
option java_multiple_files = true;
option java_outer_classname = "LocationProto";
option java_package = "com.google.appengine.v1beta";
option php_namespace = "Google\\Cloud\\AppEngine\\V1beta";
option ruby_package = "Google::Cloud::AppEngine::V1beta";

// Metadata for the given [google.cloud.location.Location][google.cloud.location.Location].
message LocationMetadata {
  // App Engine standard environment is available in the given location.
  //
  // @OutputOnly
  bool standard_environment_available = 2;

  // App Engine flexible environment is available in the given location.
  //
  // @OutputOnly
  bool flexible_environment_available = 4;

  // Output only. [Search API](https://cloud.google.com/appengine/docs/standard/python/search)
  // is available in the given location.
  bool search_api_available = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}
