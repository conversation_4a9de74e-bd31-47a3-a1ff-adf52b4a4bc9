// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.appengine.v1beta;

import "google/protobuf/duration.proto";

option csharp_namespace = "Google.Cloud.AppEngine.V1Beta";
option go_package = "google.golang.org/genproto/googleapis/appengine/v1beta;appengine";
option java_multiple_files = true;
option java_outer_classname = "DeployProto";
option java_package = "com.google.appengine.v1beta";
option php_namespace = "Google\\Cloud\\AppEngine\\V1beta";
option ruby_package = "Google::Cloud::AppEngine::V1beta";

// Code and application artifacts used to deploy a version to App Engine.
message Deployment {
  // Manifest of the files stored in Google Cloud Storage that are included
  // as part of this version. All files must be readable using the
  // credentials supplied with this call.
  map<string, FileInfo> files = 1;

  // The Docker image for the container that runs the version.
  // Only applicable for instances running in the App Engine flexible environment.
  ContainerInfo container = 2;

  // The zip file for this deployment, if this is a zip deployment.
  ZipInfo zip = 3;

  // Google Cloud Build build information. Only applicable for instances running
  // in the App Engine flexible environment.
  BuildInfo build = 5;

  // Options for any Google Cloud Build builds created as a part of this
  // deployment.
  //
  // These options will only be used if a new build is created, such as when
  // deploying to the App Engine flexible environment using files or zip.
  CloudBuildOptions cloud_build_options = 6;
}

// Single source file that is part of the version to be deployed. Each source
// file that is deployed must be specified separately.
message FileInfo {
  // URL source to use to fetch this file. Must be a URL to a resource in
  // Google Cloud Storage in the form
  // 'http(s)://storage.googleapis.com/\<bucket\>/\<object\>'.
  string source_url = 1;

  // The SHA1 hash of the file, in hex.
  string sha1_sum = 2;

  // The MIME type of the file.
  //
  // Defaults to the value from Google Cloud Storage.
  string mime_type = 3;
}

// Docker image that is used to create a container and start a VM instance for
// the version that you deploy. Only applicable for instances running in the App
// Engine flexible environment.
message ContainerInfo {
  // URI to the hosted container image in Google Container Registry. The URI
  // must be fully qualified and include a tag or digest.
  // Examples: "gcr.io/my-project/image:tag" or "gcr.io/my-project/image@digest"
  string image = 1;
}

// Google Cloud Build information.
message BuildInfo {
  // The Google Cloud Build id.
  // Example: "f966068f-08b2-42c8-bdfe-74137dff2bf9"
  string cloud_build_id = 1;
}

// Options for the build operations performed as a part of the version
// deployment. Only applicable for App Engine flexible environment when creating
// a version using source code directly.
message CloudBuildOptions {
  // Path to the yaml file used in deployment, used to determine runtime
  // configuration details.
  //
  // Required for flexible environment builds.
  //
  // See https://cloud.google.com/appengine/docs/standard/python/config/appref
  // for more details.
  string app_yaml_path = 1;

  // The Cloud Build timeout used as part of any dependent builds performed by
  // version creation. Defaults to 10 minutes.
  google.protobuf.Duration cloud_build_timeout = 2;
}

// The zip file information for a zip deployment.
message ZipInfo {
  // URL of the zip file to deploy from. Must be a URL to a resource in
  // Google Cloud Storage in the form
  // 'http(s)://storage.googleapis.com/\<bucket\>/\<object\>'.
  string source_url = 3;

  // An estimate of the number of files in a zip for a zip deployment.
  // If set, must be greater than or equal to the actual number of files.
  // Used for optimizing performance; if not provided, deployment may be slow.
  int32 files_count = 4;
}
