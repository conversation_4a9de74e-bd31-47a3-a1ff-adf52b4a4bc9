# This build file includes a target for the Ruby wrapper library for
# google-area120-tables.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for area120tables.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1alpha1 in this case.
ruby_cloud_gapic_library(
    name = "area120tables_ruby_wrapper",
    srcs = ["//google/area120/tables/v1alpha1:tables_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-area120-tables",
        "ruby-cloud-env-prefix=AREA120_TABLES",
        "ruby-cloud-wrapper-of=v1alpha1:0.7",
        "ruby-cloud-product-url=https://tables.area120.google.com/u/0/about#/",
        "ruby-cloud-api-id=area120tables.googleapis.com",
        "ruby-cloud-api-shortname=area120tables",
    ],
    ruby_cloud_description = "Using the Area 120 Tables API, you can query for tables, and update/create/delete rows within tables programmatically.",
    ruby_cloud_title = "Area 120 Tables",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-area120-tables-ruby",
    deps = [
        ":area120tables_ruby_wrapper",
    ],
)
