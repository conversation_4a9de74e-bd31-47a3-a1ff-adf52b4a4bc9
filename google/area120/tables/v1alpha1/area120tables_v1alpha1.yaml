type: google.api.Service
config_version: 3
name: area120tables.googleapis.com
title: Area120 Tables API

apis:
- name: google.area120.tables.v1alpha1.TablesService

authentication:
  rules:
  - selector: 'google.area120.tables.v1alpha1.TablesService.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive,
        https://www.googleapis.com/auth/drive.file,
        https://www.googleapis.com/auth/drive.readonly,
        https://www.googleapis.com/auth/spreadsheets,
        https://www.googleapis.com/auth/spreadsheets.readonly,
        https://www.googleapis.com/auth/tables
  - selector: google.area120.tables.v1alpha1.TablesService.BatchCreateRows
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive,
        https://www.googleapis.com/auth/drive.file,
        https://www.googleapis.com/auth/spreadsheets,
        https://www.googleapis.com/auth/tables
  - selector: google.area120.tables.v1alpha1.TablesService.BatchDeleteRows
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive,
        https://www.googleapis.com/auth/drive.file,
        https://www.googleapis.com/auth/spreadsheets,
        https://www.googleapis.com/auth/tables
  - selector: google.area120.tables.v1alpha1.TablesService.BatchUpdateRows
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive,
        https://www.googleapis.com/auth/drive.file,
        https://www.googleapis.com/auth/spreadsheets,
        https://www.googleapis.com/auth/tables
  - selector: google.area120.tables.v1alpha1.TablesService.CreateRow
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive,
        https://www.googleapis.com/auth/drive.file,
        https://www.googleapis.com/auth/spreadsheets,
        https://www.googleapis.com/auth/tables
  - selector: google.area120.tables.v1alpha1.TablesService.DeleteRow
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive,
        https://www.googleapis.com/auth/drive.file,
        https://www.googleapis.com/auth/spreadsheets,
        https://www.googleapis.com/auth/tables
  - selector: google.area120.tables.v1alpha1.TablesService.UpdateRow
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/drive,
        https://www.googleapis.com/auth/drive.file,
        https://www.googleapis.com/auth/spreadsheets,
        https://www.googleapis.com/auth/tables
