# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/gapic-generator/tree/master/rules_gapic/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "tables_proto",
    srcs = [
        "tables.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library_with_info(
    name = "tables_proto_with_info",
    deps = [
        ":tables_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "tables_java_proto",
    deps = [":tables_proto"],
)

java_grpc_library(
    name = "tables_java_grpc",
    srcs = [":tables_proto"],
    deps = [":tables_java_proto"],
)

java_gapic_library(
    name = "tables_java_gapic",
    srcs = [":tables_proto_with_info"],
    gapic_yaml = "language_gapic.yaml",
    grpc_service_config = "tables_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "area120tables_v1alpha1.yaml",
    test_deps = [
        ":tables_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":tables_java_proto",
    ],
)

java_gapic_test(
    name = "tables_java_gapic_test_suite",
    test_classes = [
        "com.google.area120.tables.v1alpha.TablesServiceClientHttpJsonTest",
        "com.google.area120.tables.v1alpha.TablesServiceClientTest",
    ],
    runtime_deps = [":tables_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-area120-tables-v1alpha1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":tables_java_gapic",
        ":tables_java_grpc",
        ":tables_java_proto",
        ":tables_proto",
    ],
)

go_proto_library(
    name = "tables_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/area120/tables/apiv1alpha1/tablespb",
    protos = [":tables_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "tables_go_gapic",
    srcs = [":tables_proto_with_info"],
    grpc_service_config = "tables_grpc_service_config.json",
    importpath = "cloud.google.com/go/area120/tables/apiv1alpha1;tables",
    release_level = "alpha",
    rest_numeric_enums = True,
    service_yaml = "area120tables_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":tables_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-area120-tables-v1alpha1-go",
    deps = [
        ":tables_go_gapic",
        ":tables_go_gapic_srcjar-snippets.srcjar",
        ":tables_go_gapic_srcjar-test.srcjar",
        ":tables_go_proto",
    ],
)

py_gapic_library(
    name = "tables_py_gapic",
    srcs = [":tables_proto"],
    grpc_service_config = "tables_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "area120tables_v1alpha1.yaml",
    transport = "grpc+rest",
)

py_test(
    name = "tables_py_gapic_test",
    srcs = [
        "tables_py_gapic_pytest.py",
        "tables_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":tables_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "area120-tables-v1alpha1-py",
    deps = [
        ":tables_py_gapic",
    ],
)

php_proto_library(
    name = "tables_php_proto",
    deps = [":tables_proto"],
)

php_gapic_library(
    name = "tables_php_gapic",
    srcs = [":tables_proto_with_info"],
    grpc_service_config = "tables_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "area120tables_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [":tables_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-area120-tables-v1alpha1-php",
    deps = [
        ":tables_php_gapic",
        ":tables_php_proto",
    ],
)

nodejs_gapic_library(
    name = "tables_nodejs_gapic",
    package_name = "@google/area120-tables",
    src = ":tables_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "tables_grpc_service_config.json",
    package = "google.area120.tables.v1alpha1",
    rest_numeric_enums = True,
    service_yaml = "area120tables_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "area120-tables-v1alpha1-nodejs",
    deps = [
        ":tables_nodejs_gapic",
        ":tables_proto",
    ],
)

ruby_proto_library(
    name = "tables_ruby_proto",
    deps = [":tables_proto"],
)

ruby_grpc_library(
    name = "tables_ruby_grpc",
    srcs = [":tables_proto"],
    deps = [":tables_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "tables_ruby_gapic",
    srcs = [":tables_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-area120-tables-v1alpha1",
        "ruby-cloud-env-prefix=AREA120_TABLES",
        "ruby-cloud-product-url=https://tables.area120.google.com/u/0/about#/",
        "ruby-cloud-api-id=area120tables.googleapis.com",
        "ruby-cloud-api-shortname=area120tables",
    ],
    grpc_service_config = "tables_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Using the Area 120 Tables API, you can query for tables, and update/create/delete rows within tables programmatically.",
    ruby_cloud_title = "Area 120 Tables V1alpha1",
    service_yaml = "area120tables_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":tables_ruby_grpc",
        ":tables_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-area120-tables-v1alpha1-ruby",
    deps = [
        ":tables_ruby_gapic",
        ":tables_ruby_grpc",
        ":tables_ruby_proto",
    ],
)

csharp_proto_library(
    name = "tables_csharp_proto",
    deps = [":tables_proto"],
)

csharp_grpc_library(
    name = "tables_csharp_grpc",
    srcs = [":tables_proto"],
    deps = [":tables_csharp_proto"],
)

csharp_gapic_library(
    name = "tables_csharp_gapic",
    srcs = [":tables_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "tables_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "area120tables_v1alpha1.yaml",
    transport = "grpc+rest",
    deps = [
        ":tables_csharp_grpc",
        ":tables_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-area120-tables-v1alpha1-csharp",
    deps = [
        ":tables_csharp_gapic",
        ":tables_csharp_grpc",
        ":tables_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
