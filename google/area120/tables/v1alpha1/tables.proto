// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.area120.tables.v1alpha1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

option go_package = "cloud.google.com/go/area120/tables/apiv1alpha1/tablespb;tablespb";
option java_multiple_files = true;
option java_outer_classname = "TablesProto";
option java_package = "com.google.area120.tables.v1alpha1";

// The Tables Service provides an API for reading and updating tables.
// It defines the following resource model:
//
// - The API has a collection of [Table][google.area120.tables.v1alpha1.Table]
//   resources, named `tables/*`
//
// - Each Table has a collection of [Row][google.area120.tables.v1alpha1.Row]
//   resources, named `tables/*/rows/*`
//
// - The API has a collection of
//   [Workspace][google.area120.tables.v1alpha1.Workspace]
//   resources, named `workspaces/*`.
service TablesService {
  option (google.api.default_host) = "area120tables.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/drive,"
      "https://www.googleapis.com/auth/drive.file,"
      "https://www.googleapis.com/auth/drive.readonly,"
      "https://www.googleapis.com/auth/spreadsheets,"
      "https://www.googleapis.com/auth/spreadsheets.readonly,"
      "https://www.googleapis.com/auth/tables";

  // Gets a table. Returns NOT_FOUND if the table does not exist.
  rpc GetTable(GetTableRequest) returns (Table) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=tables/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists tables for the user.
  rpc ListTables(ListTablesRequest) returns (ListTablesResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/tables"
    };
  }

  // Gets a workspace. Returns NOT_FOUND if the workspace does not exist.
  rpc GetWorkspace(GetWorkspaceRequest) returns (Workspace) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=workspaces/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists workspaces for the user.
  rpc ListWorkspaces(ListWorkspacesRequest) returns (ListWorkspacesResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/workspaces"
    };
  }

  // Gets a row. Returns NOT_FOUND if the row does not exist in the table.
  rpc GetRow(GetRowRequest) returns (Row) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=tables/*/rows/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists rows in a table. Returns NOT_FOUND if the table does not exist.
  rpc ListRows(ListRowsRequest) returns (ListRowsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=tables/*}/rows"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a row.
  rpc CreateRow(CreateRowRequest) returns (Row) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=tables/*}/rows"
      body: "row"
    };
    option (google.api.method_signature) = "parent,row";
  }

  // Creates multiple rows.
  rpc BatchCreateRows(BatchCreateRowsRequest) returns (BatchCreateRowsResponse) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=tables/*}/rows:batchCreate"
      body: "*"
    };
  }

  // Updates a row.
  rpc UpdateRow(UpdateRowRequest) returns (Row) {
    option (google.api.http) = {
      patch: "/v1alpha1/{row.name=tables/*/rows/*}"
      body: "row"
    };
    option (google.api.method_signature) = "row,update_mask";
  }

  // Updates multiple rows.
  rpc BatchUpdateRows(BatchUpdateRowsRequest) returns (BatchUpdateRowsResponse) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=tables/*}/rows:batchUpdate"
      body: "*"
    };
  }

  // Deletes a row.
  rpc DeleteRow(DeleteRowRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=tables/*/rows/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Deletes multiple rows.
  rpc BatchDeleteRows(BatchDeleteRowsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=tables/*}/rows:batchDelete"
      body: "*"
    };
  }
}

// Request message for TablesService.GetTable.
message GetTableRequest {
  // Required. The name of the table to retrieve.
  // Format: tables/{table}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "area120tables.googleapis.com/Table"
    }
  ];
}

// Request message for TablesService.ListTables.
message ListTablesRequest {
  // The maximum number of tables to return. The service may return fewer than
  // this value.
  //
  // If unspecified, at most 20 tables are returned. The maximum value is 100;
  // values above 100 are coerced to 100.
  int32 page_size = 1;

  // A page token, received from a previous `ListTables` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListTables` must match
  // the call that provided the page token.
  string page_token = 2;
}

// Response message for TablesService.ListTables.
message ListTablesResponse {
  // The list of tables.
  repeated Table tables = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is empty, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for TablesService.GetWorkspace.
message GetWorkspaceRequest {
  // Required. The name of the workspace to retrieve.
  // Format: workspaces/{workspace}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "area120tables.googleapis.com/Workspace"
    }
  ];
}

// Request message for TablesService.ListWorkspaces.
message ListWorkspacesRequest {
  // The maximum number of workspaces to return. The service may return fewer
  // than this value.
  //
  // If unspecified, at most 10 workspaces are returned. The maximum value is
  // 25; values above 25 are coerced to 25.
  int32 page_size = 1;

  // A page token, received from a previous `ListWorkspaces` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListWorkspaces` must
  // match the call that provided the page token.
  string page_token = 2;
}

// Response message for TablesService.ListWorkspaces.
message ListWorkspacesResponse {
  // The list of workspaces.
  repeated Workspace workspaces = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is empty, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for TablesService.GetRow.
message GetRowRequest {
  // Required. The name of the row to retrieve.
  // Format: tables/{table}/rows/{row}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "area120tables.googleapis.com/Row"
    }
  ];

  // Optional. Column key to use for values in the row.
  // Defaults to user entered name.
  View view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for TablesService.ListRows.
message ListRowsRequest {
  // Required. The parent table.
  // Format: tables/{table}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // The maximum number of rows to return. The service may return fewer than
  // this value.
  //
  // If unspecified, at most 50 rows are returned. The maximum value is 1,000;
  // values above 1,000 are coerced to 1,000.
  int32 page_size = 2;

  // A page token, received from a previous `ListRows` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListRows` must match
  // the call that provided the page token.
  string page_token = 3;

  // Optional. Column key to use for values in the row.
  // Defaults to user entered name.
  View view = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Raw text query to search for in rows of the table.
  // Special characters must be escaped. Logical operators and field specific
  // filtering not supported.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for TablesService.ListRows.
message ListRowsResponse {
  // The rows from the specified table.
  repeated Row rows = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is empty, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for TablesService.CreateRow.
message CreateRowRequest {
  // Required. The parent table where this row will be created.
  // Format: tables/{table}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The row to create.
  Row row = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Column key to use for values in the row.
  // Defaults to user entered name.
  View view = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for TablesService.BatchCreateRows.
message BatchCreateRowsRequest {
  // Required. The parent table where the rows will be created.
  // Format: tables/{table}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The request message specifying the rows to create.
  //
  // A maximum of 500 rows can be created in a single batch.
  repeated CreateRowRequest requests = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for TablesService.BatchCreateRows.
message BatchCreateRowsResponse {
  // The created rows.
  repeated Row rows = 1;
}

// Request message for TablesService.UpdateRow.
message UpdateRowRequest {
  // Required. The row to update.
  Row row = 1 [(google.api.field_behavior) = REQUIRED];

  // The list of fields to update.
  google.protobuf.FieldMask update_mask = 2;

  // Optional. Column key to use for values in the row.
  // Defaults to user entered name.
  View view = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for TablesService.BatchUpdateRows.
message BatchUpdateRowsRequest {
  // Required. The parent table shared by all rows being updated.
  // Format: tables/{table}
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The request messages specifying the rows to update.
  //
  // A maximum of 500 rows can be modified in a single batch.
  repeated UpdateRowRequest requests = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for TablesService.BatchUpdateRows.
message BatchUpdateRowsResponse {
  // The updated rows.
  repeated Row rows = 1;
}

// Request message for TablesService.DeleteRow
message DeleteRowRequest {
  // Required. The name of the row to delete.
  // Format: tables/{table}/rows/{row}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "area120tables.googleapis.com/Row"
    }
  ];
}

// Request message for TablesService.BatchDeleteRows
message BatchDeleteRowsRequest {
  // Required. The parent table shared by all rows being deleted.
  // Format: tables/{table}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "area120tables.googleapis.com/Table"
    }
  ];

  // Required. The names of the rows to delete. All rows must belong to the parent table
  // or else the entire batch will fail. A maximum of 500 rows can be deleted
  // in a batch.
  // Format: tables/{table}/rows/{row}
  repeated string names = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "area120tables.googleapis.com/Row"
    }
  ];
}

// A single table.
message Table {
  option (google.api.resource) = {
    type: "area120tables.googleapis.com/Table"
    pattern: "tables/{table}"
  };

  // The resource name of the table.
  // Table names have the form `tables/{table}`.
  string name = 1;

  // The human readable title of the table.
  string display_name = 2;

  // List of columns in this table.
  // Order of columns matches the display order.
  repeated ColumnDescription columns = 3;
}

// Details on a column in the table.
message ColumnDescription {
  // column name
  string name = 1;

  // Data type of the column
  // Supported types are auto_id, boolean, boolean_list, creator,
  // create_timestamp, date, dropdown, location, integer,
  // integer_list, number, number_list, person, person_list, tags, check_list,
  // text, text_list, update_timestamp, updater, relationship,
  // file_attachment_list.
  // These types directly map to the column types supported on Tables website.
  string data_type = 2;

  // Internal id for a column.
  string id = 3;

  // Optional. Range of labeled values for the column.
  // Some columns like tags and drop-downs limit the values to a set of
  // possible values. We return the range of values in such cases to help
  // clients implement better user data validation.
  repeated LabeledItem labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Additional details about a relationship column. Specified when data_type
  // is relationship.
  RelationshipDetails relationship_details = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Indicates that this is a lookup column whose value is derived from the
  // relationship column specified in the details. Lookup columns can not be
  // updated directly. To change the value you must update the associated
  // relationship column.
  LookupDetails lookup_details = 6 [(google.api.field_behavior) = OPTIONAL];
}

// A single item in a labeled column.
message LabeledItem {
  // Display string as entered by user.
  string name = 1;

  // Internal id associated with the item.
  string id = 2;
}

// Details about a relationship column.
message RelationshipDetails {
  // The name of the table this relationship is linked to.
  string linked_table = 1;
}

// Details about a lookup column whose value comes from the associated
// relationship.
message LookupDetails {
  // The name of the relationship column associated with the lookup.
  string relationship_column = 1;

  // The id of the relationship column.
  string relationship_column_id = 2;
}

// A single row in a table.
message Row {
  option (google.api.resource) = {
    type: "area120tables.googleapis.com/Row"
    pattern: "tables/{table}/rows/{row}"
  };

  // The resource name of the row.
  // Row names have the form `tables/{table}/rows/{row}`.
  // The name is ignored when creating a row.
  string name = 1;

  // The values of the row. This is a map of column key to value.
  // Key is user entered name(default) or the internal column id based on
  // the view in the request.
  map<string, google.protobuf.Value> values = 2;
}

// A single workspace.
message Workspace {
  option (google.api.resource) = {
    type: "area120tables.googleapis.com/Workspace"
    pattern: "workspaces/{workspace}"
  };

  // The resource name of the workspace.
  // Workspace names have the form `workspaces/{workspace}`.
  string name = 1;

  // The human readable title of the workspace.
  string display_name = 2;

  // The list of tables in the workspace.
  repeated Table tables = 3;
}

// Column identifier used for the values in the row.
enum View {
  // Defaults to user entered text.
  VIEW_UNSPECIFIED = 0;

  // Uses internally generated column id to identify values.
  COLUMN_ID_VIEW = 1;
}
