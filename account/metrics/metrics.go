package metrics

import (
	commonmetrics "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/metrics"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/metrics"
)

var (
	AccountMetricPoolUsernameResidueGauge commonmetrics.MetricGaugeHandler
	AccountMetricPoolUsernameTotalCount   commonmetrics.MetricGaugeHandler
)

func init() {
	gaugeVecOpts := commonmetrics.MetricGaugeVecOpts
	gaugeVecOpts.Name = metrics.ConstMetricsAccountPoolUsernameResidue + string(commonmetrics.ConstSystemMetricUnitTypeCounts)
	gaugeVecOpts.Help = "pool username residue gauge."
	gaugeVecOpts.Labels = []string{"parent_pool", "ch_pool"}
	AccountMetricPoolUsernameResidueGauge = commonmetrics.NewMetricGauge(&gaugeVecOpts)

	counterVecOpts := commonmetrics.MetricGaugeVecOpts
	counterVecOpts.Name = metrics.ConstMetricsAccountPoolUsername + string(commonmetrics.ConstSystemMetricUnitTypeCounts)
	counterVecOpts.Help = "pool username total gauge."
	counterVecOpts.Labels = []string{"parent_pool", "ch_pool"}
	AccountMetricPoolUsernameTotalCount = commonmetrics.NewMetricGauge(&counterVecOpts)
}
