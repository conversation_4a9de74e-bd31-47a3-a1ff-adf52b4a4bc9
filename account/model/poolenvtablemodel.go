package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

type DataColumn struct {
	Field string
	Value string
}

type DataRow []DataColumn

type (
	PoolEnvModel interface {
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		ExecSqlTX(ctx context.Context, session sqlx.Session, sql string) (sql.Result, error)

		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		Query(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*DataRow, error)
		QueryWithSql(ctx context.Context, query string) ([]*DataRow, error)

		QueryRowsCtx(ctx context.Context, v any, query string, args ...any) error
	}

	defaultPoolEnvModel struct {
		conn   sqlx.SqlConn
		table  string
		fields []string
	}

	customPoolEnvModel struct {
		*defaultPoolEnvModel
	}
)

func (m *defaultPoolEnvModel) Table() string {
	return m.table
}

func (m *defaultPoolEnvModel) Fields() []string {
	return m.fields
}

func (m *defaultPoolEnvModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *defaultPoolEnvModel) ExecSqlTX(ctx context.Context, session sqlx.Session, sql string) (sql.Result, error) {
	if session != nil {
		return session.ExecCtx(ctx, sql)
	} else {
		return m.conn.ExecCtx(ctx, sql)
	}
}

func (m *defaultPoolEnvModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(1)").Where("`occupy_state` = ?", "UNUSED").From(m.table)
}

func (m *defaultPoolEnvModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPoolEnvModel) Query(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*DataRow, error) {
	query, args, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*DataRow

	db, _ := m.conn.RawDB()
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}

	columns, _ := rows.Columns()
	count := len(columns)
	valueSlice := make([]any, count)
	valuePtrSlice := make([]any, count)

	for rows.Next() {
		var dataRow DataRow

		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			for i, col := range columns {
				val := valueSlice[i]
				// typeOfVal := reflect.TypeOf(val)
				// fmt.Println(typeOfVal.Name(), typeOfVal.Kind())

				v := ""
				byteSliceValue, isByteSlice := val.([]byte)
				if isByteSlice {
					v = string(byteSliceValue)
				} else {
					v = fmt.Sprintf("%v", val)
				}

				dataColumn := DataColumn{
					Field: col,
					Value: v,
				}
				dataRow = append(dataRow, dataColumn)
			}
			resp = append(resp, &dataRow)
		}
	}

	return resp, nil
}

func (m *customPoolEnvModel) QueryWithSql(ctx context.Context, query string) ([]*DataRow, error) {
	logx.WithContext(ctx).Infof("query sql: %s", query)

	var resp []*DataRow

	db, _ := m.conn.RawDB()
	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}

	columns, _ := rows.Columns()
	count := len(columns)
	valueSlice := make([]any, count)
	valuePtrSlice := make([]any, count)

	for rows.Next() {
		var dataRow DataRow

		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			for i, col := range columns {
				val := valueSlice[i]
				// typeOfVal := reflect.TypeOf(val)
				// fmt.Println(typeOfVal.Name(), typeOfVal.Kind())
				v := ""
				byteSliceValue, isByteSlice := val.([]byte)
				if isByteSlice {
					v = string(byteSliceValue)
				} else {
					v = fmt.Sprintf("%v", val)
				}

				dataColumn := DataColumn{
					Field: col,
					Value: v,
				}
				dataRow = append(dataRow, dataColumn)
			}

			resp = append(resp, &dataRow)
		}
	}

	return resp, nil
}

func NewPoolEnvTableModel(conn sqlx.SqlConn, table string, fields []string) PoolEnvModel {
	return &customPoolEnvModel{
		defaultPoolEnvModel: &defaultPoolEnvModel{
			conn:   conn,
			table:  table,
			fields: fields,
		},
	}
}

func (m *customPoolEnvModel) QueryRowsCtx(ctx context.Context, v any, query string, args ...any) error {
	return m.conn.QueryRowsCtx(ctx, v, query, args...)
}
