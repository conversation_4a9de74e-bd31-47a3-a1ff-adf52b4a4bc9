package model

import (
	"context"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var _ TExecuteRecordModel = (*customTExecuteRecordModel)(nil)

type (
	// TExecuteRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customTExecuteRecordModel.
	TExecuteRecordModel interface {
		tExecuteRecordModel
		types.DBModel

		SelectBuilder() squirrel.SelectBuilder
		SelectBuilderWithEmptyContent() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder

		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*TExecuteRecord, error)

		FindByExecuteId(ctx context.Context, executeId string) (*TExecuteRecord, error)
	}

	customTExecuteRecordModel struct {
		*defaultTExecuteRecordModel
	}
)

func (m *customTExecuteRecordModel) Table() string {
	return m.table
}

func (m *customTExecuteRecordModel) Fields() []string {
	return tExecuteRecordFieldNames
}

func (m *customTExecuteRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(tExecuteRecordFieldNames...).From(m.table)
}

func (m *customTExecuteRecordModel) SelectBuilderWithEmptyContent() squirrel.SelectBuilder {
	// TODO implement me
	panic("implement me")
}

func (m *customTExecuteRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(1)").From(m.table)
}

func (m *customTExecuteRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customTExecuteRecordModel) FindByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*TExecuteRecord, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TExecuteRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTExecuteRecordModel) FindByExecuteId(ctx context.Context, executeId string) (*TExecuteRecord, error) {
	query := fmt.Sprintf("select %s from %s where `execute_id` = ? limit 1", tExecuteRecordRows, m.table)
	var resp TExecuteRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, executeId)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

// NewTExecuteRecordModel returns a model for the database table.
func NewTExecuteRecordModel(conn sqlx.SqlConn) TExecuteRecordModel {
	return &customTExecuteRecordModel{
		defaultTExecuteRecordModel: newTExecuteRecordModel(conn),
	}
}
