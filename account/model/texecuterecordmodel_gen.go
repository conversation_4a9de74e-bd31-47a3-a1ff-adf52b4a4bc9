// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	tExecuteRecordFieldNames          = builder.RawFieldNames(&TExecuteRecord{})
	tExecuteRecordRows                = strings.Join(tExecuteRecordFieldNames, ",")
	tExecuteRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(tExecuteRecordFieldNames, "`id`", "`create_time`", "`update_time`"), ",")
	tExecuteRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(tExecuteRecordFieldNames, "`id`", "`create_time`", "`update_time`"), "=?,") + "=?"
)

type (
	tExecuteRecordModel interface {
		Insert(ctx context.Context, data *TExecuteRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*TExecuteRecord, error)
		Update(ctx context.Context, data *TExecuteRecord) error
		Delete(ctx context.Context, id int64) error
	}

	defaultTExecuteRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	TExecuteRecord struct {
		Id          int64          `db:"id"`
		ExecuteId   string         `db:"execute_id"`   // 执行记录ID
		PoolId      int64          `db:"pool_id"`      // 账户池模板id
		EnvId       int64          `db:"env_id"`       // 账户池环境id
		ExecuteWay  string         `db:"execute_way"`  // 操作类型(增、删、改)
		Description sql.NullString `db:"description"`  // 操作备注
		State       string         `db:"state"`        // 状态
		Result      sql.NullString `db:"result"`       // 最终结果
		CurrentJson sql.NullString `db:"current_json"` // 当前节点产生的一些数据，json格式
		CreatedBy   string         `db:"created_by"`   // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`   // 最近一次更新者的用户ID
		CreatedAt   time.Time      `db:"created_at"`   // 创建时间(戳)
		UpdatedAt   time.Time      `db:"updated_at"`   // 更新时间(戳)
	}
)

func newTExecuteRecordModel(conn sqlx.SqlConn) *defaultTExecuteRecordModel {
	return &defaultTExecuteRecordModel{
		conn:  conn,
		table: "`t_execute_record`",
	}
}

func (m *defaultTExecuteRecordModel) Insert(ctx context.Context, data *TExecuteRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, tExecuteRecordRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.ExecuteId, data.PoolId, data.EnvId, data.ExecuteWay, data.Description, data.State, data.Result, data.CurrentJson, data.CreatedBy, data.UpdatedBy, data.CreatedAt, data.UpdatedAt)
	return ret, err
}

func (m *defaultTExecuteRecordModel) FindOne(ctx context.Context, id int64) (*TExecuteRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", tExecuteRecordRows, m.table)
	var resp TExecuteRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTExecuteRecordModel) Update(ctx context.Context, data *TExecuteRecord) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, tExecuteRecordRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.ExecuteId, data.PoolId, data.EnvId, data.ExecuteWay, data.Description, data.State, data.Result, data.CurrentJson, data.CreatedBy, data.UpdatedBy, data.CreatedAt, data.UpdatedAt, data.Id)
	return err
}

func (m *defaultTExecuteRecordModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultTExecuteRecordModel) tableName() string {
	return m.table
}
