// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	tPoolColumnFieldNames          = builder.RawFieldNames(&TPoolColumn{})
	tPoolColumnRows                = strings.Join(tPoolColumnFieldNames, ",")
	tPoolColumnRowsExpectAutoSet   = strings.Join(stringx.Remove(tPoolColumnFieldNames, "`id`", "`created_at`", "`updated_at`"), ",")
	tPoolColumnRowsWithPlaceHolder = strings.Join(stringx.Remove(tPoolColumnFieldNames, "`id`", "`created_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	tPoolColumnModel interface {
		Insert(ctx context.Context, data *TPoolColumn) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*TPoolColumn, error)
		Update(ctx context.Context, data *TPoolColumn) error
		Delete(ctx context.Context, id int64) error
	}

	defaultTPoolColumnModel struct {
		conn  sqlx.SqlConn
		table string
	}

	TPoolColumn struct {
		Id              int64          `db:"id"`
		PoolId          int64          `db:"pool_id"`           // 模版池id
		TableName       string         `db:"table_name"`        // 表名
		ColumnId        string         `db:"column_id"`         // 字段别名
		ColumnName      string         `db:"column_name"`       // 字段名
		ColumnType      string         `db:"column_type"`       // 字段类型
		ColumnLength    sql.NullInt64  `db:"column_length"`     // 字段长度
		ColumnDefault   sql.NullString `db:"column_default"`    // 字段默认值
		ColumnAllowNull string         `db:"column_allow_null"` // 字段是否允许为空
		ColumnIsVisible string         `db:"column_is_visible"` // 字段是否可见
		ColumnComment   sql.NullString `db:"column_comment"`    // 字段注释
		ColumnIsUsing   string         `db:"column_is_using"`   // 字段是否使用中
		PrimaryKey      sql.NullString `db:"primary_key"`       // 字段是否为主键
		CreatedBy       string         `db:"created_by"`        // 创建者的用户ID
		UpdatedBy       string         `db:"updated_by"`        // 最近一次更新者的用户ID
		CreatedAt       time.Time      `db:"created_at"`        // 创建时间(戳)
		UpdatedAt       time.Time      `db:"updated_at"`        // 更新时间(戳)
	}
)

func newTPoolColumnModel(conn sqlx.SqlConn) *defaultTPoolColumnModel {
	return &defaultTPoolColumnModel{
		conn:  conn,
		table: "`t_pool_column`",
	}
}

func (m *defaultTPoolColumnModel) Insert(ctx context.Context, data *TPoolColumn) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, tPoolColumnRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.PoolId, data.TableName, data.ColumnId, data.ColumnName,
		data.ColumnType, data.ColumnLength, data.ColumnDefault, data.ColumnAllowNull, data.ColumnIsVisible,
		data.ColumnComment, data.ColumnIsUsing, data.PrimaryKey, data.CreatedBy, data.UpdatedBy)
	return ret, err
}

func (m *defaultTPoolColumnModel) FindOne(ctx context.Context, id int64) (*TPoolColumn, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", tPoolColumnRows, m.table)
	var resp TPoolColumn
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTPoolColumnModel) Update(ctx context.Context, data *TPoolColumn) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, tPoolColumnRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.PoolId, data.TableName, data.ColumnId, data.ColumnName,
		data.ColumnType, data.ColumnLength, data.ColumnDefault, data.ColumnAllowNull, data.ColumnIsVisible,
		data.ColumnComment, data.ColumnIsUsing, data.PrimaryKey, data.CreatedBy, data.UpdatedBy,
		data.Id)
	return err
}

func (m *defaultTPoolColumnModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultTPoolColumnModel) tableName() string {
	return m.table
}
