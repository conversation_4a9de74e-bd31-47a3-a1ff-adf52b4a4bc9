package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

var (
	_                      TPoolTableModel = (*customTPoolTableModel)(nil)
	tPoolTableInsertFields                 = stringx.Remove(
		tPoolTableFieldNames, "`id`", "`created_at`", "`updated_at`",
	)
)

type (
	// TPoolTableModel is an interface to be customized, add more methods here,
	// and implement the added methods in customTPoolTableModel.
	TPoolTableModel interface {
		tPoolTableModel

		InsertWithOutCreateAtAndUpdateAt(ctx context.Context, data *TPoolTable) (sql.Result, error)

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error

		InsertBuilder(data *TPoolTable) squirrel.InsertBuilder
		UpdateBuilder(data *TPoolTable) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder

		InsertTX(ctx context.Context, session sqlx.Session, insertBuilder squirrel.InsertBuilder) (sql.Result, error)
		InsertTX2(ctx context.Context, session sqlx.Session, sql string) (sql.Result, error)
		UpdateTX(ctx context.Context, session sqlx.Session, updateBuilder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateTX2(ctx context.Context, session sqlx.Session, updateSql string) (sql.Result, error)
		DeleteTX(ctx context.Context, session sqlx.Session, id int64) (sql.Result, error)

		IsPoolTemplateByPoolId(ctx context.Context, poolId int64) (bool, error)
		IsPoolTemplateByBelongProduct(ctx context.Context, belongProduct string) (bool, error)
		FindPoolTableNameByBelongProduct(ctx context.Context, belongProduct string) (string, error)
		FindPoolIdByBelongProduct(ctx context.Context, belongProduct string) (int64, error)
		FindPoolTableByBelongProduct(ctx context.Context, belongProduct string) (*TPoolTable, error)
		FindPoolTableByPoolId(ctx context.Context, poolId int64) (*TPoolTable, error)
		FindPoolEnvTablesByBelongProduct(ctx context.Context, belongProduct string) ([]*TPoolTable, error)
		FindPoolEnvTablesByPoolId(ctx context.Context, poolId int64) ([]*TPoolTable, error)
		FindPoolIdByPoolEnvTableName(ctx context.Context, poolEnvTableName string) (int64, error)
		FindPoolByPoolEnvTableName(ctx context.Context, poolEnvTableName string) (*TPoolTable, error)
		FindPoolEnvTableByPoolEnvTableName(ctx context.Context, poolEnvTableName string) (*TPoolTable, error)
		FindAllPoolTable(ctx context.Context) ([]*TPoolTable, error)
		FindAllPoolAndPoolEnvTableNameByPoolId(ctx context.Context, poolId int64) ([]string, error)
		FindAllPoolAndPoolEnvByPoolId(ctx context.Context, poolId int64) ([]*TPoolTable, error)
		FindPoolEnvTableByPoolIdEnvId(ctx context.Context, poolId, envId int64) (*TPoolTable, error)

		FindByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*TPoolTable, error)
		FindAllUsingPoolEnvTables(ctx context.Context) ([]*TPoolTable, error)
	}

	customTPoolTableModel struct {
		*defaultTPoolTableModel
	}
)

func (m *defaultTPoolTableModel) InsertWithOutCreateAtAndUpdateAt(ctx context.Context, data *TPoolTable) (
	sql.Result, error,
) {
	query := fmt.Sprintf(
		"insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, tPoolTableRowsExpectAutoSet,
	)
	ret, err := m.conn.ExecCtx(
		ctx, query, data.ParentPoolId, data.PoolName, data.TableName,
		data.PoolType, data.BelongProduct, data.CoolingTime, data.Description, data.IsUsing, data.CreatedBy,
		data.UpdatedBy,
	)
	return ret, err
}

func (m *defaultTPoolTableModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *defaultTPoolTableModel) InsertBuilder(data *TPoolTable) squirrel.InsertBuilder {
	zeroTime := time.Time{}
	if data.CreatedAt == zeroTime {
		return squirrel.Insert(m.table).Columns(tPoolTableInsertFields...).Values(
			data.ParentPoolId, data.PoolName, data.TableName, data.PoolType,
			data.BelongProduct, data.CoolingTime, data.Description, data.IsUsing, data.CreatedBy, data.UpdatedBy,
		)
	} else {
		return squirrel.Insert(m.table).Columns(tPoolTableInsertFields...).Columns("`created_at`").Values(
			data.ParentPoolId, data.PoolName, data.TableName, data.PoolType,
			data.BelongProduct, data.CoolingTime, data.Description, data.IsUsing, data.CreatedBy, data.UpdatedBy,
			data.CreatedAt,
		)
	}
}

func (m *defaultTPoolTableModel) UpdateBuilder(data *TPoolTable) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`pool_name`":   data.PoolName,
		"`description`": data.Description,
		"`updated_by`":  data.UpdatedBy,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *defaultTPoolTableModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(tPoolTableFieldNames...).From(m.table)
}

func (m *defaultTPoolTableModel) InsertTX(
	ctx context.Context, session sqlx.Session, insertBuilder squirrel.InsertBuilder,
) (sql.Result, error) {
	query, values, err := insertBuilder.ToSql()
	if err != nil {
		return nil, err
	}
	if session != nil {
		return session.ExecCtx(ctx, query, values...)
	} else {
		return m.conn.ExecCtx(ctx, query, values...)
	}
}

func (m *defaultTPoolTableModel) InsertTX2(ctx context.Context, session sqlx.Session, sql string) (sql.Result, error) {
	if session != nil {
		return session.ExecCtx(ctx, sql)
	} else {
		return m.conn.ExecCtx(ctx, sql)
	}
}

func (m *defaultTPoolTableModel) UpdateTX(
	ctx context.Context, session sqlx.Session, updateBuilder squirrel.UpdateBuilder,
) (sql.Result, error) {
	query, values, err := updateBuilder.ToSql()
	if err != nil {
		return nil, err
	}
	if session != nil {
		return session.ExecCtx(ctx, query, values...)
	} else {
		return m.conn.ExecCtx(ctx, query, values...)
	}
}

func (m *defaultTPoolTableModel) UpdateTX2(ctx context.Context, session sqlx.Session, updateSql string) (
	sql.Result, error,
) {
	if session != nil {
		return session.ExecCtx(ctx, updateSql)
	} else {
		return m.conn.ExecCtx(ctx, updateSql)
	}
}

func (m *defaultTPoolTableModel) DeleteTX(ctx context.Context, session sqlx.Session, id int64) (sql.Result, error) {
	//query, values, err := deleteBuilder.ToSql()
	//if err != nil {
	//	return nil, err
	//}
	//
	//query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	//
	//return m.conn.ExecCtx(ctx, query, values)
	return nil, nil
}

func (m *defaultTPoolTableModel) IsPoolTemplateByPoolId(ctx context.Context, poolId int64) (bool, error) {
	countBuilder := squirrel.Select("count(1)").From(m.table).Where("`id` = ? AND `parent_pool_id` is null", poolId)
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return false, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	var flag bool
	if resp != 0 {
		flag = true
	} else {
		flag = false
	}
	switch err {
	case nil:
		return flag, nil
	default:
		return flag, err
	}
}

func (m *defaultTPoolTableModel) IsPoolTemplateByBelongProduct(ctx context.Context, belongProduct string) (
	bool, error,
) {
	countBuilder := squirrel.Select("count(1)").From(m.table).Where(
		"`belong_product` = ? AND `parent_pool_id` is null", belongProduct,
	)
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return false, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	var flag bool
	if resp != 0 {
		flag = true
	} else {
		flag = false
	}
	switch err {
	case nil:
		return flag, nil
	default:
		return flag, err
	}
}

func (m *defaultTPoolTableModel) FindPoolTableNameByBelongProduct(ctx context.Context, belongProduct string) (
	string, error,
) {
	columns := []string{"table_name"}
	selectBuilder := squirrel.Select(columns...).From(m.table).
		Where("`belong_product` = ? AND `parent_pool_id` is null", belongProduct)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return "", err
	}

	var resp string
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	fmt.Println(err)
	switch err {
	case nil:
		return resp, nil
	default:
		return "", err
	}
}

func (m *defaultTPoolTableModel) FindPoolIdByBelongProduct(ctx context.Context, belongProduct string) (int64, error) {
	selectBuilder := squirrel.Select("id").From(m.table).
		Where("`belong_product` = ? AND `parent_pool_id` is null", belongProduct).Limit(1)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var poolId int64
	err = m.conn.QueryRowCtx(ctx, &poolId, query, values...)

	switch err {
	case nil:
		return poolId, nil
	default:
		return poolId, err
	}
}

func (m *defaultTPoolTableModel) FindPoolTableByBelongProduct(ctx context.Context, belongProduct string) (
	*TPoolTable, error,
) {
	selectBuilder := squirrel.Select(tPoolTableFieldNames...).From(m.table).
		Where("`belong_product` = ? AND `parent_pool_id` is null", belongProduct).Limit(1)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp TPoolTable
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)

	switch err {
	case nil:
		return &resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTPoolTableModel) FindPoolTableByPoolId(ctx context.Context, poolId int64) (*TPoolTable, error) {
	selectBuilder := squirrel.Select(tPoolTableFieldNames...).From(m.table).
		Where("`id` = ? AND `parent_pool_id` is null", poolId).Limit(1)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp TPoolTable
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)

	switch err {
	case nil:
		return &resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTPoolTableModel) FindPoolEnvTablesByBelongProduct(
	ctx context.Context, belongProduct string,
) ([]*TPoolTable, error) {
	selectBuilder := squirrel.Select(tPoolTableFieldNames...).From(m.table).
		Where(
			"`belong_product` = ? AND `is_using` = ? AND `parent_pool_id` is not null",
			belongProduct, int(common.YES),
		)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TPoolTable
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTPoolTableModel) FindPoolEnvTablesByPoolId(ctx context.Context, poolId int64) ([]*TPoolTable, error) {
	selectBuilder := squirrel.Select(tPoolTableFieldNames...).From(m.table).
		Where("`parent_pool_id` = ? AND `is_using` = ?", poolId, int(common.YES))
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TPoolTable
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTPoolTableModel) FindPoolIdByPoolEnvTableName(ctx context.Context, poolEnvTableName string) (
	int64, error,
) {
	selectPoolIdBuilder := squirrel.Select("parent_pool_id").
		From(m.table).Where("`table_name` = ? AND `parent_pool_id` is not null", poolEnvTableName)
	queryPoolId, values, err := selectPoolIdBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var poolId int64
	if err := m.conn.QueryRowCtx(ctx, &poolId, queryPoolId, values...); err != nil {
		return 0, err
	}
	return poolId, nil
}

func (m *defaultTPoolTableModel) FindPoolByPoolEnvTableName(ctx context.Context, poolEnvTableName string) (
	*TPoolTable, error,
) {
	poolId, _ := m.FindPoolIdByPoolEnvTableName(ctx, poolEnvTableName)

	selectBuilder := squirrel.Select(tPoolTableFieldNames...).From(m.table).Where("`id` = ? ", poolId).Limit(1)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp TPoolTable
	if err = m.conn.QueryRowCtx(ctx, &resp, query, values...); err != nil {
		return nil, err
	}

	return &resp, nil
}

func (m *defaultTPoolTableModel) FindPoolEnvTableByPoolEnvTableName(
	ctx context.Context, poolEnvTableName string,
) (*TPoolTable, error) {
	selectBuilder := squirrel.Select(tPoolTableFieldNames...).From(m.table).Where(
		"`table_name` = ? ", poolEnvTableName,
	).Limit(1)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp TPoolTable
	if err = m.conn.QueryRowCtx(ctx, &resp, query, values...); err != nil {
		return nil, err
	}

	return &resp, nil
}

func (m *defaultTPoolTableModel) FindAllPoolTable(ctx context.Context) ([]*TPoolTable, error) {
	selectBuilder := squirrel.Select(tPoolTableFieldNames...).From(m.table).Where("`parent_pool_id` is null ")
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TPoolTable
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTPoolTableModel) FindAllPoolAndPoolEnvTableNameByPoolId(ctx context.Context, poolId int64) (
	[]string, error,
) {
	selectBuilder := squirrel.Select("table_name").From(m.table).
		Where("`id` = ? or `parent_pool_id` = ?", poolId, poolId)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []string
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTPoolTableModel) FindAllPoolAndPoolEnvByPoolId(ctx context.Context, poolId int64) (
	[]*TPoolTable, error,
) {
	selectBuilder := squirrel.Select("table_name").From(m.table).
		Where("`pool_id` = ? or `parent_pool_id` = ?", poolId, poolId)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TPoolTable
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTPoolTableModel) FindPoolEnvTableByPoolIdEnvId(
	ctx context.Context, poolId, envId int64,
) (*TPoolTable, error) {
	selectBuilder := squirrel.Select(tPoolTableFieldNames...).From(m.table).
		Where("`id` = ? AND `parent_pool_id` = ? and is_using='YES'", envId, poolId).Limit(1)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp TPoolTable
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)

	switch err {
	case nil:
		return &resp, nil
	default:
		return nil, err
	}
}

func NewTPoolTableModel(conn sqlx.SqlConn) TPoolTableModel {
	return &customTPoolTableModel{
		defaultTPoolTableModel: newTPoolTableModel(conn),
	}
}

func (m *customTPoolTableModel) FindByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
	[]*TPoolTable, error,
) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TPoolTable
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTPoolTableModel) FindAllUsingPoolEnvTables(ctx context.Context) ([]*TPoolTable, error) {
	sb := squirrel.Select(tPoolTableFieldNames...).From(m.table).Where(
		"`parent_pool_id` is not null and `is_using` = ?", common.YES,
	)

	return m.FindByQuery(ctx, sb)
}
