package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ TPoolColumnModel = (*customTPoolColumnModel)(nil)

type (
	// TPoolColumnModel is an interface to be customized, add more methods here,
	// and implement the added methods in customTPoolColumnModel.
	TPoolColumnModel interface {
		tPoolColumnModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error

		UpdateTX(ctx context.Context, updateBuilder squirrel.UpdateBuilder) (sql.Result, error)

		FindAllColumnByPoolId(ctx context.Context, poolId int64) ([]*TPoolColumn, error)
		FindVisibleColumnByPoolId(ctx context.Context, poolId int64) ([]*TPoolColumn, error)

		FindAllColumnByTableName(ctx context.Context, tableName string) ([]*TPoolColumn, error)
		FindVisibleColumnByTableName(ctx context.Context, tableName string) ([]*TPoolColumn, error)

		FindAllColumnNameByTableName(ctx context.Context, tableName string) ([]string, error)
		FindVisibleColumnNameByTableName(ctx context.Context, tableName string) ([]string, error)

		FindAllColumnNameWithBackQuoteByTableName(ctx context.Context, tableName string) ([]string, error)
		FindVisibleColumnNameWithBackQuoteByTableName(ctx context.Context, tableName string) ([]string, error)

		FindAllColumnIdColumnTypeMapByTableName(ctx context.Context, tableName string) (map[string]string, error)
		FindVisibleColumnIdColumnTypeMapByTableName(ctx context.Context, tableName string) (map[string]string, error)

		FindColumnByPoolIdColumnId(ctx context.Context, poolId int64, columnId string) (*TPoolColumn, error)

		FindRecycleColumnByPoolId(ctx context.Context, poolId int64) ([]*TPoolColumn, error)
	}

	customTPoolColumnModel struct {
		*defaultTPoolColumnModel
	}
)

func (m *customTPoolColumnModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customTPoolColumnModel) UpdateTX(ctx context.Context, updateBuilder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := updateBuilder.ToSql()
	if err != nil {
		return nil, err
	}
	return m.conn.ExecCtx(ctx, query, values)
}

func (m *customTPoolColumnModel) FindAllColumnByPoolId(ctx context.Context, poolId int64) ([]*TPoolColumn, error) {
	selectBuilder := squirrel.Select(tPoolColumnFieldNames...).From(m.table).Where("`pool_id` = ? ", poolId)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TPoolColumn
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTPoolColumnModel) FindVisibleColumnByPoolId(ctx context.Context, poolId int64) ([]*TPoolColumn, error) {
	selectBuilder := squirrel.Select(tPoolColumnFieldNames...).From(m.table).Where("`pool_id` = ? and column_is_visible='YES'", poolId)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TPoolColumn
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTPoolColumnModel) FindAllColumnByTableName(ctx context.Context, tableName string) ([]*TPoolColumn, error) {
	selectBuilder := squirrel.Select(tPoolColumnFieldNames...).From(m.table).Where("`table_name` = ? ", tableName)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TPoolColumn
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTPoolColumnModel) FindVisibleColumnByTableName(ctx context.Context, tableName string) ([]*TPoolColumn, error) {
	selectBuilder := squirrel.Select(tPoolColumnFieldNames...).From(m.table).Where("`tableName` = ? and column_is_visible='YES'", tableName)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TPoolColumn
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTPoolColumnModel) FindAllColumnNameByTableName(ctx context.Context, tableName string) ([]string, error) {
	columns := []string{"column_id"}
	selectBuilder := squirrel.Select(columns...).From(m.table).
		Where("`table_name` = ? ", tableName).OrderBy("primary_key asc")
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []string
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTPoolColumnModel) FindVisibleColumnNameByTableName(ctx context.Context, tableName string) ([]string, error) {
	columns := []string{"column_id"}
	selectBuilder := squirrel.Select(columns...).From(m.table).
		Where("`table_name` = ? and column_is_visible='YES'", tableName).OrderBy("primary_key asc")
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []string
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTPoolColumnModel) FindAllColumnNameWithBackQuoteByTableName(ctx context.Context, tableName string) ([]string, error) {
	columns := []string{"column_id"}
	selectBuilder := squirrel.Select(columns...).From(m.table).
		Where("`table_name` = ?", tableName).OrderBy("primary_key asc")
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []string
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		for i, column := range resp {
			column = fmt.Sprintf("`%s`", column)
			resp[i] = column
		}
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTPoolColumnModel) FindVisibleColumnNameWithBackQuoteByTableName(ctx context.Context, tableName string) ([]string, error) {
	columns := []string{"column_id"}
	selectBuilder := squirrel.Select(columns...).From(m.table).
		Where("`table_name` = ? and column_is_visible='YES'", tableName).OrderBy("primary_key asc")
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []string
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		for i, column := range resp {
			column = fmt.Sprintf("`%s`", column)
			resp[i] = column
		}
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customTPoolColumnModel) FindAllColumnIdColumnTypeMapByTableName(ctx context.Context, tableName string) (map[string]string, error) {
	columnPtrSlice, err := m.FindAllColumnByTableName(ctx, tableName)
	if err != nil {
		return nil, err
	}
	resp := map[string]string{}
	for _, columnPtr := range columnPtrSlice {
		columnId := columnPtr.ColumnId
		columnType := columnPtr.ColumnType
		resp[columnId] = columnType
	}
	return resp, nil
}

func (m *customTPoolColumnModel) FindVisibleColumnIdColumnTypeMapByTableName(ctx context.Context, tableName string) (map[string]string, error) {
	columnPtrSlice, err := m.FindVisibleColumnByTableName(ctx, tableName)
	if err != nil {
		return nil, err
	}
	resp := map[string]string{}
	for _, columnPtr := range columnPtrSlice {
		columnId := columnPtr.ColumnId
		columnType := columnPtr.ColumnType
		resp[columnId] = columnType
	}
	return resp, nil
}

func (m *customTPoolColumnModel) FindColumnByPoolIdColumnId(ctx context.Context, poolId int64, columnId string) (*TPoolColumn, error) {
	selectBuilder := squirrel.Select(tPoolColumnFieldNames...).From(m.table).
		Where("`pool_id` = ? and column_id= ?", poolId, columnId).Limit(1)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp TPoolColumn
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	default:
		return nil, err
	}
}

func (m *customTPoolColumnModel) FindRecycleColumnByPoolId(ctx context.Context, poolId int64) ([]*TPoolColumn, error) {
	selectBuilder := squirrel.Select(tPoolColumnFieldNames...).From(m.table).Where("`pool_id` = ? and column_is_using= 'NO'", poolId)
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TPoolColumn
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

// NewTPoolColumnModel returns a model for the database table.
func NewTPoolColumnModel(conn sqlx.SqlConn) TPoolColumnModel {
	return &customTPoolColumnModel{
		defaultTPoolColumnModel: newTPoolColumnModel(conn),
	}
}
