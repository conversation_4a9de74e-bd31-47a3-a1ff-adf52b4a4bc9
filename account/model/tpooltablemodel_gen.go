// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	tPoolTableFieldNames          = builder.RawFieldNames(&TPoolTable{})
	tPoolTableRows                = strings.Join(tPoolTableFieldNames, ",")
	tPoolTableRowsExpectAutoSet   = strings.Join(stringx.Remove(tPoolTableFieldNames, "`id`", "`created_at`", "`updated_at`"), ",")
	tPoolTableRowsWithPlaceHolder = strings.Join(stringx.Remove(tPoolTableFieldNames, "`id`", "`created_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	tPoolTableModel interface {
		Insert(ctx context.Context, data *TPoolTable) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*TPoolTable, error)
		Update(ctx context.Context, data *TPoolTable) error
		Delete(ctx context.Context, id int64) error
	}

	defaultTPoolTableModel struct {
		conn  sqlx.SqlConn
		table string
	}

	TPoolTable struct {
		Id            int64          `db:"id"`             // 账户池id
		ParentPoolId  sql.NullInt64  `db:"parent_pool_id"` // 所属账户池模版id
		PoolName      string         `db:"pool_name"`      // 账户池名称
		TableName     string         `db:"table_name"`     // 账户池表名
		PoolType      string         `db:"pool_type"`      // 账户池类型
		BelongProduct string         `db:"belong_product"` // 账户池所属产品（项目）id
		CoolingTime   int64          `db:"cooling_time"`   // 池账号冷却时间（单位为秒）
		Description   sql.NullString `db:"description"`    // 账户池描述
		IsUsing       string         `db:"is_using"`       // 表是否使用
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间(戳)
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间(戳)
	}
)

func newTPoolTableModel(conn sqlx.SqlConn) *defaultTPoolTableModel {
	return &defaultTPoolTableModel{
		conn:  conn,
		table: "`t_pool_table`",
	}
}

func (m *defaultTPoolTableModel) Insert(ctx context.Context, data *TPoolTable) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, tPoolTableRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.ParentPoolId, data.PoolName, data.TableName, data.PoolType, data.BelongProduct, data.CoolingTime, data.Description, data.IsUsing, data.CreatedBy, data.UpdatedBy, data.CreatedAt, data.UpdatedAt)
	return ret, err
}

func (m *defaultTPoolTableModel) FindOne(ctx context.Context, id int64) (*TPoolTable, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", tPoolTableRows, m.table)
	var resp TPoolTable
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTPoolTableModel) Update(ctx context.Context, data *TPoolTable) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, tPoolTableRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.ParentPoolId, data.PoolName, data.TableName, data.PoolType, data.BelongProduct, data.CoolingTime, data.Description, data.IsUsing, data.CreatedBy, data.UpdatedBy, data.CreatedAt, data.UpdatedAt, data.Id)
	return err
}

func (m *defaultTPoolTableModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultTPoolTableModel) tableName() string {
	return m.table
}
