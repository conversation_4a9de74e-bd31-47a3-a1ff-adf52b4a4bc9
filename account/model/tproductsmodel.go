package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ TProductsModel = (*customTProductsModel)(nil)

	tProductsInsertFields = stringx.Remove(tProductsFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// TProductsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customTProductsModel.
	TProductsModel interface {
		tProductsModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *TProducts) squirrel.InsertBuilder
		UpdateBuilder(data *TProducts) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*TProducts, error)
	}

	customTProductsModel struct {
		*defaultTProductsModel
	}
)

// NewTProductsModel returns a model for the database table.
func NewTProductsModel(conn sqlx.SqlConn) TProductsModel {
	return &customTProductsModel{
		defaultTProductsModel: newTProductsModel(conn),
	}
}

func (m *customTProductsModel) Table() string {
	return m.table
}

func (m *customTProductsModel) Fields() []string {
	return tProductsFieldNames
}

func (m *customTProductsModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customTProductsModel) InsertBuilder(data *TProducts) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(tProductsInsertFields...).Values()
}

func (m *customTProductsModel) UpdateBuilder(data *TProducts) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customTProductsModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(tProductsFieldNames...).From(m.table)
}

func (m *customTProductsModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").From(m.table)
}

func (m *customTProductsModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customTProductsModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*TProducts, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TProducts
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
