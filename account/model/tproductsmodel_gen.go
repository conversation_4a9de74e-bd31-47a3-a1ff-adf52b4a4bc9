// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	tProductsTableName           = "`t_products`"
	tProductsFieldNames          = builder.RawFieldNames(&TProducts{})
	tProductsRows                = strings.Join(tProductsFieldNames, ",")
	tProductsRowsExpectAutoSet   = strings.Join(stringx.Remove(tProductsFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	tProductsRowsWithPlaceHolder = strings.Join(stringx.Remove(tProductsFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	tProductsModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *TProducts) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*TProducts, error)
		Update(ctx context.Context, session sqlx.Session, data *TProducts) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error

		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultTProductsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	TProducts struct {
		Id            int64  `db:"id"`             // 自增主键
		ProductName   string `db:"product_name"`   // 产品名称
		ProductType   int64  `db:"product_type"`   // 产品类型
		BelongProduct string `db:"belong_product"` // 所属产品
	}
)

func newTProductsModel(conn sqlx.SqlConn) *defaultTProductsModel {
	return &defaultTProductsModel{
		conn:  conn,
		table: "`t_products`",
	}
}

func (m *defaultTProductsModel) withSession(session sqlx.Session) *defaultTProductsModel {
	return &defaultTProductsModel{
		conn:  sqlx.NewSqlConnFromSession(session),
		table: "`t_products`",
	}
}

func (m *defaultTProductsModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultTProductsModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultTProductsModel) FindOne(ctx context.Context, id int64) (*TProducts, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", tProductsRows, m.table)
	var resp TProducts
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTProductsModel) Insert(ctx context.Context, session sqlx.Session, data *TProducts) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, tProductsRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.ProductName, data.ProductType, data.BelongProduct)
	}
	return m.conn.ExecCtx(ctx, query, data.ProductName, data.ProductType, data.BelongProduct)
}

func (m *defaultTProductsModel) Update(ctx context.Context, session sqlx.Session, data *TProducts) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, tProductsRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, data.ProductName, data.ProductType, data.BelongProduct, data.Id)
	}
	return m.conn.ExecCtx(ctx, query, data.ProductName, data.ProductType, data.BelongProduct, data.Id)
}

func (m *defaultTProductsModel) tableName() string {
	return m.table
}
