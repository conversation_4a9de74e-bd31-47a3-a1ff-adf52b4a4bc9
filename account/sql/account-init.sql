CREATE DATABASE IF NOT EXISTS `account` DEFAULT CHARSET = utf8mb4 COLLATE utf8mb4_general_ci;

USE `account`;

CREATE TABLE `account`.`t_pool_table`
(
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '账户池id',
    `parent_pool_id` int(11) NULL DEFAULT NULL COMMENT '所属账户池模版id',
    `pool_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账户池名称',
    `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账户池表名',
    `pool_type` enum('REPEATABLE','UNREPEATABLE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账户池类型',
    `belong_product` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账户池所属产品（项目）id',
    `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账户池描述',
    `is_using` enum('YES','NO') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表是否使用',
    `created_by` VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 224 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `account`.`t_pool_column`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `pool_id` int(11) NOT NULL COMMENT '模版池id',
    `table_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表名',
    `column_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段别名',
    `column_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段名',
    `column_type` enum('VARCHAR','INT','FLOAT','TINYINT','DATETIME','TIMESTAMP') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段类型',
    `column_length` int(11) NULL DEFAULT NULL COMMENT '字段长度',
    `column_default` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段默认值',
    `column_allow_null` enum('YES','NO') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段是否允许为空',
    `column_is_visible` enum('YES','NO') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段是否可见',
    `column_comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段注释',
    `column_is_using` enum('YES','NO') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段是否使用中',
    `primary_key` enum('YES','NO') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段是否为主键',
    `created_by` VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

CREATE TABLE `account`.`t_execute_record`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `execute_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行记录ID',
    `pool_id` int(11) NOT NULL COMMENT '账户池模板id',
    `env_id` int(11) NOT NULL COMMENT '账户池环境id',
    `execute_way` enum('ADD_DATA','DELETE_DATA','MODIFY_DATA') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型(增、删、改)',
    `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作备注',
    `state` enum('UNCHECKED','CHECKING','QUERYING','DETECTED','END_OF_QUERY','TO_BE_UPDATE','DATA_PROCESSING','FINISHING','TERMINATED','EXCEPTION','NONE_VALID_DATA') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态',
    `result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最终结果',
    `current_json` json NULL COMMENT '当前节点产生的一些数据，json格式',
    `created_by` VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by` VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(戳)',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间(戳)',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `执行execute_id唯一`(`execute_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;