package svc

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/config"
)

type ServiceContext struct {
	Config config.Config
	DB     sqlx.SqlConn
	Redis  *redis.Redis

	TPoolTableModel     model.TPoolTableModel
	TPoolColumnModel    model.TPoolColumnModel
	TExecuteRecordModel model.TExecuteRecordModel

	AccountConsumer *consumer.Consumer
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)

	return &ServiceContext{
		Config:              c,
		DB:                  sqlConn,
		Redis:               redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),
		TPoolTableModel:     model.NewTPoolTableModel(sqlConn),
		TPoolColumnModel:    model.NewTPoolColumnModel(sqlConn),
		TExecuteRecordModel: model.NewTExecuteRecordModel(sqlConn),
		AccountConsumer:     consumer.NewConsumer(c.AccountConsumer),
	}
}
