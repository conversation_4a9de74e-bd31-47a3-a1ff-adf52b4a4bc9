package perfData

import (
	"bytes"
	"context"
	"encoding/csv"
	"flag"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	xrate "golang.org/x/time/rate"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	httpx "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
	fasthttpx "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/nethttp"
)

var (
	routerURL   = flag.String("router_url", "", "router url, e.g. http://*************:8080")
	csvFilePath = flag.String("csv_file", "", "csv file, e.g. ./production.csv")
	envHost     = flag.String("env_host", "", "env host, e.g. lvs.52tt.com, testing-login.ttyuyin.com")
	workers     = flag.Int("workers", 10, "the number of worker")
	rate        = flag.Int("rate", 100, "rate limit, e.g. 100")

	limiter = xrate.NewLimiter(xrate.Every(time.Second/time.Duration(*rate)), 10)
)

type (
	perfData struct {
		Index            int64  `json:"index"`
		Account          string `json:"account"`
		Password         string `json:"password"`
		TID              string `json:"tid"`
		UID              int64  `json:"uid"`
		ChannelID        int64  `json:"channel_id"`
		ChannelDisplayID int64  `json:"channel_display_id"`
		ChannelName      string `json:"channel_name"`
	}

	authInfo struct {
		Alias       string `json:"alias"`
		UID         int64  `json:"uid"`
		UserAccount string `json:"user_account"`
		UserPhone   string `json:"user_phone"`
	}
	createClientData struct {
		CID       string `json:"cid"`
		CreatedAt int64  `json:"created_at"`
		LoginResp struct {
			AuthInfo authInfo `json:"auth_info"`
		} `json:"login_resp"`
	}
	createClientResp struct {
		Code    uint32           `json:"code"`
		Message string           `json:"message"`
		Data    createClientData `json:"data"`
	}

	channelInfo struct {
		ChannelID        int64  `json:"channel_id"`
		ChannelDisplayID int64  `json:"channel_display_id"`
		ChannelName      string `json:"channel_name"`
	}
	adminChannelList struct {
		AdminRole   int64         `json:"admin_role"`
		ChannelList []channelInfo `json:"channel_list"`
	}
	getAdminChannelListData struct {
		CallResp struct {
			Header map[string][]string `json:"header"` // 响应头
			Body   adminChannelList    `json:"body"`   // 响应体
			Status int32               `json:"status"` // 响应状态码
		} `json:"call_resp"`
	}
	getAdminChannelListResp struct {
		Code    uint32                  `json:"code"`
		Message string                  `json:"message"`
		Data    getAdminChannelListData `json:"data"`
	}
)

func TestConvertPerfDataToExcel(t *testing.T) {
	if *routerURL == "" {
		t.Fatal("not set the `router_host`")
	} else if *csvFilePath == "" {
		t.Fatal("not set the `csv_file`")
	} else if *envHost == "" {
		t.Fatal("not set the `env_host`")
	}

	routerFastHTTPClient := fasthttpx.NewClient(
		fasthttpx.ClientConf{
			BaseURL: *routerURL,
		},
	)
	routerNetHTTPClient := nethttp.NewClient(
		nethttp.ClientConf{
			BaseURL: *routerURL,
		},
	)

	csvFile, err := os.Open(*csvFilePath)
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		_ = csvFile.Close()
	}()

	var index int64 = 0
	reader := csv.NewReader(csvFile)
	reader.ReuseRecord = true

	sheet := "Sheet1"
	xlsFile := excelize.NewFile()
	defer func() {
		if err := xlsFile.Close(); err != nil {
			t.Error(err)
		}
	}()

	_ = mr.MapReduceVoid[perfData, perfData](
		func(source chan<- perfData) {
			for {
				record, err := reader.Read()
				if errors.Is(err, io.EOF) {
					break
				}
				if err != nil {
					t.Fatal(err)
				}
				index += 1
				if index == 1 {
					continue
				} else if len(record) < 4 {
					t.Errorf("skip record: %s", jsonx.MarshalIgnoreError(record))
					continue
				}

				source <- perfData{
					Index:    index,
					Account:  record[0],
					Password: record[1],
					TID:      record[2],
					UID:      cast.ToInt64(record[3]),
				}
			}
		}, func(item perfData, writer mr.Writer[perfData], cancel func(error)) {
			cid, err := createClient(routerFastHTTPClient, item.Account, item.Password)
			if err != nil {
				t.Errorf("failed to create client by api-proxy, account: %s, error: %+v", item.Account, err)
				return
			}
			defer func() {
				if err = deleteClient(routerNetHTTPClient, cid); err != nil {
					t.Errorf(
						"failed to delete client by api-proxy, account: %s, cid: %s, error: %+v",
						item.Account, cid, err,
					)
				}
			}()

			channels, err := getAdminChannels(routerFastHTTPClient, cid)
			if err != nil {
				t.Errorf(
					"failed to get admin channels by api-proxy, account: %s, cid: %s, error: %+v",
					item.Account, cid, err,
				)
				return
			} else if len(channels) == 0 {
				t.Errorf("not found any admin channels, account: %s, cid: %s", item.Account, cid)
				return
			} else if len(channels) > 1 {
				t.Logf(
					"got more than one admin channel, account: %s, cid: %s, channels: %s",
					item.Account, cid, jsonx.MarshalIgnoreError(channels),
				)
			}

			item.ChannelID = channels[0].ChannelID
			item.ChannelDisplayID = channels[0].ChannelDisplayID
			item.ChannelName = channels[0].ChannelName
			writer.Write(item)
		}, func(pipe <-chan perfData, cancel func(error)) {
			for item := range pipe {
				_ = xlsFile.SetCellStr(sheet, fmt.Sprintf("A%d", item.Index), item.Account)        // account
				_ = xlsFile.SetCellStr(sheet, fmt.Sprintf("B%d", item.Index), item.Password)       // password
				_ = xlsFile.SetCellStr(sheet, fmt.Sprintf("C%d", item.Index), item.TID)            // tid
				_ = xlsFile.SetCellInt(sheet, fmt.Sprintf("D%d", item.Index), int(item.UID))       // uid
				_ = xlsFile.SetCellInt(sheet, fmt.Sprintf("E%d", item.Index), int(item.ChannelID)) // channel_id
				_ = xlsFile.SetCellInt(
					sheet, fmt.Sprintf("F%d", item.Index), int(item.ChannelDisplayID),
				) // channel_display_id
				_ = xlsFile.SetCellStr(
					sheet, fmt.Sprintf("G%d", item.Index), item.ChannelName,
				) // channel_name

				t.Logf("write to excel: %s", jsonx.MarshalIgnoreError(item))
			}
		}, mr.WithWorkers(*workers),
	)

	headers := []string{
		"account", "password", "tid", "uid", "my_channel_id", "my_channel_display_id", "my_channel_name",
	}
	if err = xlsFile.SetSheetRow(sheet, "A1", &headers); err != nil {
		t.Errorf("failed to set sheet row, error: %+v", err)
	}
	_ = xlsFile.SetColWidth(sheet, "A", "B", 13)
	_ = xlsFile.SetColWidth(sheet, "C", "D", 10)
	_ = xlsFile.SetColWidth(sheet, "E", "E", 13)
	_ = xlsFile.SetColWidth(sheet, "F", "G", 20)
	filename := strings.TrimSuffix(filepath.Base(*csvFilePath), filepath.Ext(*csvFilePath))
	if err = xlsFile.SaveAs(filename + ".xlsx"); err != nil {
		t.Errorf("failed to save the excel, error: %+v", err)
	}
}

func createClient(client *fasthttpx.Client, account, password string) (string, error) {
	req := fasthttpx.NewRequest(
		fasthttpx.SetURL(client.BuildURL("/router/v1/client/create")),
		fasthttpx.SetMethod(http.MethodPost),
		fasthttpx.SetHeader(map[string][]string{"Content-Type": {"application/json"}}),
		fasthttpx.SetBody(
			[]byte(fmt.Sprintf(
				`{"type": "tt", "url": "tcp://%s:%s@%s", "custom_fields": {"client_version": "6.52.5"}}`,
				account, password, *envHost,
			)),
		),
	)
	defer fasthttpx.ReleaseRequest(req)

	resp := fasthttpx.AcquireResponse()
	defer fasthttpx.ReleaseResponse(resp)

	timeout := 10 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	if err := limiter.Wait(ctx); err != nil {
		return "", err
	}

	if err := client.Send(req, resp, timeout); err != nil {
		return "", err
	} else if resp.StatusCode() != http.StatusOK {
		return "", errors.Errorf("the status code is not OK, status code: %d, body: %s", resp.StatusCode(), resp.Body())
	}

	var resp_ createClientResp
	if err := jsonx.Unmarshal(resp.Body(), &resp_); err != nil {
		return "", err
	} else if resp_.Code != uint32(errorx.OK) {
		return "", errors.Errorf("the code is not OK, code: %d, message: %s", resp_.Code, resp_.Message)
	}

	return resp_.Data.CID, nil
}

func deleteClient(client *nethttp.Client, cid string) error {
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("cid", cid)
	_ = writer.Close()

	req := nethttp.NewRequest(
		nethttp.SetURL(httpx.BuildURL(*routerURL, "/router/v1/client/delete")),
		nethttp.SetMethod(http.MethodDelete),
		nethttp.SetHeaders(map[string][]string{"Content-Type": {writer.FormDataContentType()}}),
		nethttp.SetBody(payload.Bytes()),
	)

	timeout := 5 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	if err := limiter.Wait(ctx); err != nil {
		return err
	}

	resp, err := client.Send(req, timeout)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		return errors.Errorf("the status code is not OK, status code: %d, body: %s", resp.StatusCode, body)
	}

	return nil
}

func getAdminChannels(client *fasthttpx.Client, cid string) ([]channelInfo, error) {
	method := "ga.api.channel_logic_go.ChannelLogicGo.GetUserAdminChannelList"
	req := fasthttpx.NewRequest(
		fasthttpx.SetURL(client.BuildURL("/router/v1/common/api/call")),
		fasthttpx.SetMethod(http.MethodPost),
		fasthttpx.SetHeader(map[string][]string{"Content-Type": {"application/json"}}),
		fasthttpx.SetBody([]byte(fmt.Sprintf(`{"cid": "%s", "method": "%s", "body": {"admin_role": 1}}`, cid, method))),
	)
	defer fasthttpx.ReleaseRequest(req)

	resp := fasthttpx.AcquireResponse()
	defer fasthttpx.ReleaseResponse(resp)

	timeout := 10 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	if err := limiter.Wait(ctx); err != nil {
		return nil, err
	}

	if err := client.Send(req, resp, timeout); err != nil {
		return nil, err
	} else if resp.StatusCode() != http.StatusOK {
		return nil, errors.Errorf(
			"the status code is not OK, status code: %d, body: %s", resp.StatusCode(), resp.Body(),
		)
	}

	var resp_ getAdminChannelListResp
	if err := jsonx.Unmarshal(resp.Body(), &resp_); err != nil {
		return nil, err
	} else if resp_.Code != uint32(errorx.OK) {
		return nil, errors.Errorf("the code is not OK, code: %d, message: %s", resp_.Code, resp_.Message)
	}

	return resp_.Data.CallResp.Body.ChannelList, nil
}
