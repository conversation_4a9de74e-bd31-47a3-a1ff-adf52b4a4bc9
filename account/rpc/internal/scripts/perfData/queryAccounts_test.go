package perfData

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/client/account"
)

const (
	accountPodEndpoint = "10.64.189.4:8080"
	timeout            = 8 * time.Second
)

var accountRPC account.Account

func TestMain(m *testing.M) {
	accountRPC = account.NewAccount(
		zrpc.MustNewClient(
			zrpc.RpcClientConf{
				Endpoints: []string{accountPodEndpoint},
				NonBlock:  false,
				Timeout:   int64(timeout / time.Millisecond),
			},
		),
	)

	m.Run()
}

func TestQueryAccounts_BySingleRequest(t *testing.T) {
	items, err := acquireAccounts(t, accountRPC, 1000)
	if err != nil {
		t.Fatalf("failed to acquire accounts, error: %+v", err)
	}

	time.Sleep(5 * time.Second)

	err = releaseAccounts(t, accountRPC, items)
	if err != nil {
		t.Fatalf("failed to release accounts, error: %+v", err)
	}
}

func TestQueryAccounts_ByMultipleRequest(t *testing.T) {
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		threading.GoSafe(
			func() {
				defer wg.Done()

				items, err := acquireAccounts(t, accountRPC, 1000)
				if err != nil {
					t.Fatalf("failed to acquire accounts, error: %+v", err)
				}

				time.Sleep(5 * time.Second)

				err = releaseAccounts(t, accountRPC, items)
				if err != nil {
					t.Fatalf("failed to release accounts, error: %+v", err)
				}
			},
		)
	}

	wg.Wait()
}

func acquireAccounts(
	t *testing.T, client account.Account, expected int64,
) ([]*account.QueryAccountPoolEnvDataResponse_Account, error) {
	startedAt := time.Now()

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	resp, err := client.QueryAccountPoolEnvData(
		ctx, &account.QueryAccountPoolEnvDataRequest{
			PoolEnvTable:  "t_tt_perf_1724407644405",
			ExpectedCount: expected,
		},
	)
	if err != nil {
		return nil, err
	}

	t.Logf(
		"acquire accounts, match: %d, expected: %d, elapsed: %s",
		resp.GetMatchCount(), resp.GetExpectedCount(), time.Since(startedAt).String(),
	)
	return resp.GetMatchData(), nil
}

func releaseAccounts(
	t *testing.T, client account.Account, items []*account.QueryAccountPoolEnvDataResponse_Account,
) error {
	accounts := make([]*account.ReleaseTestAccountRequest_Account, 0, len(items))
	for _, item := range items {
		// t.Logf("account_%04d: %s", i, jsonx.MarshalIgnoreError(item))

		for _, col := range item.GetAccount() {
			if col.GetField() == "account" {
				accounts = append(
					accounts, &account.ReleaseTestAccountRequest_Account{
						Account:   col.GetValue(),
						LockValue: col.GetLockValue(),
					},
				)
				break
			}
		}
	}

	startedAt := time.Now()
	defer func() {
		t.Logf("release accounts, count: %d, elapsed: %s", len(accounts), time.Since(startedAt).String())
	}()

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	if _, err := client.ReleaseTestAccount(
		ctx, &account.ReleaseTestAccountRequest{
			ReleaseTasAccountArray: []*account.ReleaseTestAccountRequest_PoolAccount{
				{
					PoolEnvTable: "t_tt_perf_1724407644405",
					AccountArray: accounts,
				},
			},
		},
	); err != nil {
		return err
	}

	return nil
}
