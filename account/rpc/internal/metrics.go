package internal

import (
	"context"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
)

func MetricsHandler(serviceContext *svc.ServiceContext) {
	// 添加第一个
	threading.GoSafeCtx(
		context.Background(), func() {
			PoolAccountNotEnoughHandler(serviceContext)
		},
	)
	// ...
}

// PoolAccountNotEnoughHandler for example
// 添加到对应对应的包内
// SIGTERM 监控K8s容器退出
// SIGINT 用户执行退出任务
func PoolAccountNotEnoughHandler(serviceContext *svc.ServiceContext) {
exec:
	uuidService := utils.GenUUID("PoolAccountNotEnough-")
	logx.Infof("PoolAccountNotEnoughHandler init ,ser:%s", uuidService)
	ctx := context.TODO()
	key := redis.ConstRedisAccountNotEnoughHandlerKey
	logx.Debugf("PoolAccountNotEnoughHandler 争抢锁,ser:%s", uuidService)
	val, err := serviceContext.Redis.SetnxExCtx(ctx, key, uuidService, 86400)
	if err != nil {
		logx.Errorf("PoolAccountNotEnoughHandler error:%s,ser:%s", err, uuidService)
		return
	}
	if !val {
		logx.Infof("PoolAccountNotEnoughHandler 获取锁失败 已有服务启动,ser:%s", uuidService)
		ticker := time.NewTicker(10 * time.Second)
		countMax := 6
		count := 0
		for range ticker.C {
			count++
			logx.Infof("PoolAccountNotEnoughHandler wc ing , ser:%s , count:%d", uuidService, count)
			if count > countMax {
				return
			}
			exists, err := serviceContext.Redis.Exists(key)
			if err != nil {
				logx.Errorf("PoolAccountNotEnoughHandler error:%s", err)
				return
			}
			logx.Infof(
				"PoolAccountNotEnoughHandler wc ing , ser:%s , count:%d , watch lock result:%t", uuidService, count,
				exists,
			)
			if exists {
				continue
			}
			goto exec
		}
	}
	logx.Debugf("PoolAccountNotEnoughHandler 获得锁,ser:%s", uuidService)
	defer func() {
		logx.Debugf("PoolAccountNotEnoughHandler wg done(defer) 删除锁,ser:%s, 开始", uuidService)
		delKey(serviceContext, ctx, key, uuidService)
		logx.Debugf("PoolAccountNotEnoughHandler wg done(defer) 删除锁,ser:%s, 结束", uuidService)
	}()

	signalMains := make(chan os.Signal, 1)
	signal.Notify(signalMains, syscall.SIGTERM)
	go func() {
		logx.Debugf("关注 signalMains")
		for main := range signalMains {
			logx.Debugf("PoolAccountNotEnoughHandler signalMains 删除锁,signal:%+v,ser:%s, 开始", main, uuidService)
			delKey(serviceContext, ctx, key, uuidService)
			logx.Debugf(
				"PoolAccountNotEnoughHandler signalMains 删除锁,signal:%+v ,ser:%s, 结束", main, uuidService,
			)
		}
	}()

	var wg sync.WaitGroup
	// 业务指标获取数
	accountPool := getAccountPool(ctx, serviceContext)
	// 父
	for key, table := range accountPool {
		split := strings.Split(key, ":")
		if len(split) != 2 {
			continue
		}
		parentId := split[0]
		parentPoolName := split[1]
		for chKey, chPoolTable := range table {
			chSplit := strings.Split(chKey, ":")
			if len(chSplit) != 2 {
				continue
			}
			chId := chSplit[0]
			chPoolName := chSplit[1]

			hand := func() {
				defer func() {
					logx.Debugf("释放 ,wg.Done(),ser:%s", uuidService)
					wg.Done()
				}()

				signals := make(chan os.Signal, 1)
				// kll -USR1 p_id
				signal.Notify(signals, syscall.SIGINT)
				ticker := time.NewTicker(time.Second)
				for {
					select {
					case <-signals:
						logx.Infof(
							"SIGINT,任务退出,监控执行:父模版id-[%s],父模版名称-[%s],子池id-[%s],子池名称-[%s]",
							parentId, parentPoolName, chId,
							chPoolName,
						)
						return
					case <-ticker.C:
						threading.GoSafeCtx(
							ctx, func() {
								nextHandler(
									ctx, serviceContext, parentId, parentPoolName, chId, chPoolName, *chPoolTable,
								)
							},
						)
					}
				}
			}
			logx.Infof(
				"监控执行:父模版id-[%s],父模版名称-[%s],子池id-[%s],子池名称-[%s],", parentId, parentPoolName, chId,
				chPoolName,
			)
			wg.Add(1)
			logx.Debugf("PoolAccountNotEnoughHandler hand wg.Add(1),ser:%s", uuidService)
			threading.GoSafeCtx(
				ctx, func() {
					hand()
				},
			)
		}
	}
	logx.Debugf("PoolAccountNotEnoughHandler wg.Wait(),ser:%s", uuidService)
	wg.Wait()
	logx.Debugf("PoolAccountNotEnoughHandler wg.Wait() complete,ser:%s", uuidService)
}

func delKey(serviceContext *svc.ServiceContext, ctx context.Context, key, uuidService string) {
	// 不做原子性
	uuidServiceValue, err := serviceContext.Redis.GetCtx(ctx, key)
	logx.Debugf("PoolAccountNotEnoughHandler delKey[get],ser:%s, uuidServiceValue %v", uuidService, uuidServiceValue)
	if err != nil {
		logx.Errorf("PoolAccountNotEnoughHandler delKey[get], error:%s,ser:%s", err, uuidService)
		return
	}
	if uuidServiceValue == uuidService {
		b, err := serviceContext.Redis.DelCtx(ctx, key)
		logx.Debugf("PoolAccountNotEnoughHandler delKey[del],ser:%s, result %v", uuidService, b)
		if err != nil {
			logx.Errorf("PoolAccountNotEnoughHandler delKey[del] error:%s,ser:%s", err, uuidService)
			return
		}
	}
}

func nextHandler(
	ctx context.Context, serviceContext *svc.ServiceContext, parentId, parentPoolName, chId string,
	chPoolName string, table model.TPoolTable,
) {
	var countValue string
	var unUsedValue string
	poolEnvModel := model.NewPoolEnvTableModel(serviceContext.DB, "", []string{})
	// 计算总数
	selectBuilder := squirrel.Select("count(1) as counts").From(table.TableName)
	dataRows, err := poolEnvModel.Query(ctx, selectBuilder)
	if err != nil {
		return
	}
	for _, column := range *dataRows[0] {
		countValue = column.Value
	}
	// 计算剩余(未使用)
	selectBuilder1 := squirrel.Select("count(1) as counts").From(table.TableName).Where(
		"occupy_state = ?", "UNUSED",
	)
	dataRows1, err := poolEnvModel.Query(ctx, selectBuilder1)
	if err != nil {
		logx.Errorf("监控执行错误:%s", err)
		return
	}
	for _, column := range *dataRows1[0] {
		unUsedValue = column.Value
	}

	countFloat, err := strconv.ParseFloat(countValue, 64)
	if err != nil {
		logx.Errorf("监控执行错误:%s", err)
		return
	}
	unUsedFloat, err := strconv.ParseFloat(unUsedValue, 64)
	if err != nil {
		logx.Errorf("监控执行错误:%s", err)
		return
	}
	metrics.AccountMetricPoolUsernameResidueGauge.Set(
		unUsedFloat, []string{parentId + ":" + parentPoolName, chId + ":" + chPoolName}...,
	)
	metrics.AccountMetricPoolUsernameTotalCount.Set(
		countFloat, []string{parentId + ":" + parentPoolName, chId + ":" + chPoolName}...,
	)
}

// getAccountPool
// return: map[string1]map[string2]*model.TPoolTable string1=父Id:父PoolName string2=子Id:子PoolName
func getAccountPool(ctx context.Context, serviceContext *svc.ServiceContext) map[string]map[string]*model.TPoolTable {
	table, err := serviceContext.TPoolTableModel.FindAllPoolTable(ctx)
	if err != nil {
		return nil
	}
	// tmp := make(map[string]*model.TPoolTable)
	tmp2 := make(map[string]map[string]*model.TPoolTable)
	for _, poolTable := range table {
		if _, ok := tmp2[poolTable.PoolName]; !ok {
			tmp2[strconv.FormatInt(poolTable.Id, 10)+":"+poolTable.PoolName] = make(map[string]*model.TPoolTable, 0)
		}
		tables, err := serviceContext.TPoolTableModel.FindPoolEnvTablesByPoolId(ctx, poolTable.Id)
		if err != nil {
			logx.Errorf("error:%s", err)
			return nil
		}
		tmp3 := make(map[string]*model.TPoolTable, len(tables))
		for _, table := range tables {
			if _, ok := tmp3[strconv.FormatInt(table.Id, 10)+":"+table.PoolName]; !ok {
				tmp3[strconv.FormatInt(table.Id, 10)+":"+table.PoolName] = table
			}
		}
		tmp2[strconv.FormatInt(poolTable.Id, 10)+":"+poolTable.PoolName] = tmp3
	}
	return tmp2
}
