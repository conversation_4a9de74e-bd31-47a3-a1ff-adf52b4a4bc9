package internal

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/tasks"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

func InitOperations(svcCtx *svc.ServiceContext) {
	// 注册无需用户信息的gRPC方法
	RegisterNoNeedUserFullMethod()
	// 开启监控
	MetricsHandler(svcCtx)
	// 运行释放器（自动释放池账号）
	threading.GoSafe(NewReleaser(svcCtx).Run)
	// 注册任务类型并启动消费者
	registerTasksAndLaunchConsumer(svcCtx)
}

func registerTasksAndLaunchConsumer(svcCtx *svc.ServiceContext) {
	if err := svcCtx.AccountConsumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeAccountReleaseTestAccount, tasks.NewProcessorReleaseTestAccount(svcCtx),
		),
	); err != nil {
		logx.Errorf("failed to register tasks to account consumer, error: %+v", err)
	}

	threading.GoSafe(
		func() {
			logx.Info("starting the account consumer")
			svcCtx.AccountConsumer.Start()
		},
	)
}
