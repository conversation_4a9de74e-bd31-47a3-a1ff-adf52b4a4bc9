package accountlogic

import (
	"context"
	cryptorand "crypto/rand"
	"fmt"
	"math/big"
	mathrand "math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
)

var (
	rand = mathrand.New(mathrand.NewSource(time.Now().UnixNano()))

	errorOfPoolNotFound        = errors.New("账号池环境可能不存在或已经不可用")
	errorOfPoolColumnsNotFound = errors.New("账户池环境没有任何可用的字段")
	errorOfGetPoolColumnType   = errors.New("获取账号池环境字段名称及字段类型发生错误")
	errorOfInvalidPoolColumn   = errors.New("获取的字段不是全部都存在于表中！")
	errorOfGetPoolColumnName   = errors.New("获取账号池环境字段发生错误")
	errorOfQueryAccounts       = errors.New("筛选池账号发生异常")
	errorOfNoMatchingAccounts  = errors.New("没有筛选到任何账号")
	errorOfUpdateAccountState  = errors.New("修改池账号占用状态发生异常")
)

func random(max_ int64) int64 {
	n, err := cryptorand.Int(cryptorand.Reader, big.NewInt(max_))
	if err != nil {
		return rand.Int63n(max_)
	}

	return n.Int64()
}

type QueryAccountPoolEnvDataLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewQueryAccountPoolEnvDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *QueryAccountPoolEnvDataLogic {
	return &QueryAccountPoolEnvDataLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *QueryAccountPoolEnvDataLogic) QueryAccountPoolEnvData(in *pb.QueryAccountPoolEnvDataRequest) (
	*pb.QueryAccountPoolEnvDataResponse, error,
) {
	l.Infof("查询池账号的完整入参为: %s", protobuf.MarshalJSONIgnoreError(in))

	poolEnvTable := in.GetPoolEnvTable()
	selectedColumnIdArray := in.GetSelectedColumnIdArray()
	expectedCount := in.GetExpectedCount()
	allowLessThanExpectedCount := in.GetAllowLessThanExpectedCount()

	if allowLessThanExpectedCount == 0 {
		allowLessThanExpectedCount = 2
	}
	if expectedCount <= 0 {
		return nil, errors.New("`expected_count`必须为正整数")
	}
	if _, ok := common.YesOrNoName[allowLessThanExpectedCount]; !ok {
		return nil, errors.New("`allow_less_than_expected_count`必须为整数1或2")
	}

	// 筛选字段
	var selectedColumns []string
	if len(selectedColumnIdArray) == 0 {
		selectedColumns = []string{common.BuiltinTableFieldOfAccount, common.BuiltinTableFieldOfPassword}
	} else {
		selectedColumns = selectedColumnIdArray
	}

	// 校验并获取poolTemplate
	poolTemplate, err := l.checkAndGetData(poolEnvTable, selectedColumns)
	if err != nil {
		return nil, err
	}

	// 获取columnIdColumnTypeMap
	columnIdColumnTypeMap, err := l.getColumnIdColumnTypeMap(poolTemplate)
	if err != nil {
		return nil, err
	}

	// 池账号环境
	poolEnv, err := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolEnvTableName(l.ctx, poolEnvTable)
	if err != nil {
		l.Errorf("查询池账号环境发生异常: %+v", err)
		return nil, errors.New("查询池账号环境发生异常")
	}

	// 获取poolEnvModel
	fields, err := l.getFields(poolTemplate)
	if err != nil {
		return nil, err
	}
	fields = append(fields, sqlbuilder.BackQuoteField("", common.BuiltinTableFieldOfOccupyState)) // `occupy_state`
	if poolEnv.CoolingTime > 0 {
		fields = append(fields, sqlbuilder.BackQuoteField("", common.BuiltinTableFieldOfReturnTime)) // `return_time`
	}
	poolEnvModel := model.NewPoolEnvTableModel(l.svcCtx.DB, poolEnvTable, fields)

	// 构造查询条件（增加`occupy_state`、`return_time`相应的条件）
	selectedCondition := generateCondition(in.GetCondition(), poolEnv.CoolingTime)

	req := &acquireAccountsReq{
		in:                in,
		poolEnvModel:      poolEnvModel,
		selectedColumns:   selectedColumns,
		selectedCondition: selectedCondition,
		columnIDTypes:     columnIdColumnTypeMap,
	}
	if expectedCount < 10 {
		return l.acquireAccountsV1(req)
	}

	return l.acquireAccountsV2(req)
}

func (l *QueryAccountPoolEnvDataLogic) checkAndGetData(poolEnvTable string, selectedColumns []string) (
	*model.TPoolTable, error,
) {
	// 判断账号池环境是否能用
	poolTemplate, err := l.getPoolTemplate(poolEnvTable)
	if err != nil {
		return nil, err
	}

	// 判断账户池环境是否存在可用的字段
	existsColumnNameArray, err := l.getExistsColumnNameArray(poolTemplate)
	if err != nil {
		return nil, err
	}

	// 判断传递的字段是否有问题
	retainColumnIdList := []string{
		common.BuiltinTableFieldOfCreateTime,
		common.BuiltinTableFieldOfAcquireTime,
		common.BuiltinTableFieldOfReturnTime,
		common.BuiltinTableFieldOfCoolingTime,
		common.BuiltinTableFieldOfOccupyState,
		common.BuiltinTableFieldOfRelatedExecuteID,
	}
	selectedColumnIdMap, columnNameMap := make(map[string]string), make(map[string]string)
	for _, selectedColumn := range selectedColumns {
		selectedColumnIdMap[selectedColumn] = selectedColumn
	}
	for _, columnName := range existsColumnNameArray {
		columnNameMap[columnName] = columnName
	}
	for _, retainColumnId := range retainColumnIdList {
		if _, ok := selectedColumnIdMap[retainColumnId]; ok {
			return nil, errors.Errorf("获取的字段不能包含以下保留字段: %s", strings.Join(retainColumnIdList, ","))
		}
	}
	for _, selectedColumnId := range selectedColumns {
		if _, ok := columnNameMap[selectedColumnId]; !ok {
			return nil, errorOfInvalidPoolColumn
		}
	}

	return poolTemplate, nil
}

func (l *QueryAccountPoolEnvDataLogic) getPoolTemplate(poolEnvTable string) (*model.TPoolTable, error) {
	var poolTemplate model.TPoolTable
	templateTableIndex := strings.LastIndex(poolEnvTable, "_")
	templateTable := poolEnvTable[0:templateTableIndex]
	redisKey := fmt.Sprintf("pool_table_%s", templateTable)
	recordStr, redisErr := l.svcCtx.Redis.Get(redisKey)
	if redisErr == nil && recordStr != "" { // 从redis取到数据
		_ = jsonx.UnmarshalFromString(recordStr, &poolTemplate)
	} else { // 从数据库读取数据
		var err error
		poolTemplatePtr, err := l.svcCtx.TPoolTableModel.FindPoolByPoolEnvTableName(l.ctx, poolEnvTable)
		if err != nil || poolTemplatePtr == nil {
			l.Errorf("查询账号池模版表发生异常: %+v", err)
			return nil, errorOfPoolNotFound
		}

		poolTemplate = *poolTemplatePtr
		recordStr = jsonx.MarshalToStringIgnoreError(poolTemplate)
		// 从数据库拿数据成功要缓存到redis，失效时间为24小时
		_, err = redislock.NewRedisLockAndAcquire(
			l.svcCtx.Redis, redisKey, redislock.WithValue(recordStr), redislock.WithExpire(cacheExpireTimeOfOneDay),
		)
		if err != nil {
			l.Errorf("缓存账号池模版表对象到redis发生异常: %+v", err)
		}
	}

	return &poolTemplate, nil
}

func (l *QueryAccountPoolEnvDataLogic) getExistsColumnNameArray(poolTemplate *model.TPoolTable) ([]string, error) {
	var existsColumnNameArray []string
	poolTable := poolTemplate.TableName
	redisKey := fmt.Sprintf("pool_column_name_%s", poolTable)
	recordStr, redisErr := l.svcCtx.Redis.Get(redisKey)
	if redisErr == nil && recordStr != "" { // 从redis取到数据
		_ = jsonx.UnmarshalFromString(recordStr, &existsColumnNameArray)
	} else { // 从数据库读取数据
		var err error
		existsColumnNameArray, err = l.svcCtx.TPoolColumnModel.FindAllColumnNameByTableName(
			l.ctx, poolTemplate.TableName,
		)
		if err != nil || existsColumnNameArray == nil {
			l.Errorf("查询账号池表字段发生异常: %+v", err)
			return nil, errorOfPoolColumnsNotFound
		}

		recordStr = jsonx.MarshalToStringIgnoreError(existsColumnNameArray)
		// 从数据库拿数据成功要缓存到redis，失效时间为24小时
		_, err = redislock.NewRedisLockAndAcquire(
			l.svcCtx.Redis, redisKey, redislock.WithValue(recordStr), redislock.WithExpire(cacheExpireTimeOfOneDay),
		)
		if err != nil {
			l.Errorf("缓存账号池表字段对象到redis发生异常: %+v", err)
		}
	}

	return existsColumnNameArray, nil
}

func (l *QueryAccountPoolEnvDataLogic) getColumnIdColumnTypeMap(poolTemplate *model.TPoolTable) (
	map[string]string, error,
) {
	var columnIdColumnTypeMap map[string]string
	poolTable := poolTemplate.TableName
	redisKey := fmt.Sprintf("pool_column_id_column_type_%s", poolTable)
	recordStr, redisErr := l.svcCtx.Redis.Get(redisKey)
	if redisErr == nil && recordStr != "" { // 从redis取到数据
		_ = jsonx.UnmarshalFromString(recordStr, &columnIdColumnTypeMap)
	} else { // 从数据库读取数据
		var err error
		columnIdColumnTypeMap, err = l.svcCtx.TPoolColumnModel.FindAllColumnIdColumnTypeMapByTableName(
			l.ctx, poolTemplate.TableName,
		)
		if err != nil || columnIdColumnTypeMap == nil {
			l.Errorf("获取账号池环境字段名称及字段类型发生错误: %+v", err)
			return nil, errorOfGetPoolColumnType
		}

		recordStr = jsonx.MarshalToStringIgnoreError(columnIdColumnTypeMap)
		// 从数据库拿数据成功要缓存到redis，失效时间为24小时
		_, err = redislock.NewRedisLockAndAcquire(
			l.svcCtx.Redis, redisKey, redislock.WithValue(recordStr), redislock.WithExpire(cacheExpireTimeOfOneDay),
		)
		if err != nil {
			l.Errorf("缓存账号池表字段对象到redis发生异常: %+v", err)
		}
	}

	return columnIdColumnTypeMap, nil
}

func (l *QueryAccountPoolEnvDataLogic) getFields(poolTemplate *model.TPoolTable) ([]string, error) {
	var fields []string
	poolTable := poolTemplate.TableName
	redisKey := fmt.Sprintf("pool_fields_%s", poolTable)
	recordStr, redisErr := l.svcCtx.Redis.Get(redisKey)
	if redisErr == nil && recordStr != "" { // 从redis取到数据
		_ = jsonx.UnmarshalFromString(recordStr, &fields)
	} else { // 从数据库读取数据
		var err error
		fields, err = l.svcCtx.TPoolColumnModel.FindAllColumnNameWithBackQuoteByTableName(l.ctx, poolTemplate.TableName)
		if err != nil || fields == nil {
			l.Errorf("获取账号池环境字段发生错误: %+v", err)
			return nil, errorOfGetPoolColumnName
		}

		recordStr = jsonx.MarshalToStringIgnoreError(fields)
		// 从数据库拿数据成功要缓存到redis，失效时间为24小时
		_, err = redislock.NewRedisLockAndAcquire(
			l.svcCtx.Redis, redisKey, redislock.WithValue(recordStr), redislock.WithExpire(cacheExpireTimeOfOneDay),
		)
		if err != nil {
			l.Errorf("缓存账号池表字段对象到redis发生异常: %+v", err)
		}
	}

	return fields, nil
}

func (l *QueryAccountPoolEnvDataLogic) doQuery(
	poolEnvModel model.PoolEnvModel, selectBuilder squirrel.SelectBuilder,
) ([]*model.DataRow, error) {
	stmt, values, err := selectBuilder.ToSql()
	if err != nil {
		l.Errorf("查询池账号的sql语句错误, stmt: %s, values: %+v, error: %+v", stmt, values, err)
		return nil, errorOfQueryAccounts
	}
	l.Infof("查询池账号, stmt: %s, values: %+v", stmt, values)

	// 筛选账号
	dataRows, err := poolEnvModel.Query(l.ctx, selectBuilder)
	if err != nil {
		l.Errorf("查询池账号时发生异常, stmt: %s, values: %+v, error: %+v", stmt, values, err)
		return nil, errorOfQueryAccounts
	}

	return dataRows, nil
}

func (l *QueryAccountPoolEnvDataLogic) updateAccountOccupyState(
	poolEnvTable string, dataRows []*model.DataRow, occupyState common.EnvDataOccupyState, timestamp int64,
) error {
	count := len(dataRows)
	if count == 0 {
		return nil
	}

	updateTimestampField := common.BuiltinTableFieldOfAcquireTime
	if occupyState == common.OccupyStateUnused {
		updateTimestampField = common.BuiltinTableFieldOfReturnTime
	}
	if timestamp == 0 {
		timestamp = time.Now().UnixMilli()
	}

	accounts := make([]string, 0, count)
	for _, row := range dataRows {
		if row == nil || len(*row) == 0 {
			continue
		}

		for _, column := range *row {
			if column.Field == common.BuiltinTableFieldOfAccount {
				accounts = append(accounts, column.Value)
				break
			}
		}
	}

	stmt, values, err := squirrel.Update(poolEnvTable).
		SetMap(
			squirrel.Eq{
				common.BuiltinTableFieldOfOccupyState: occupyState,
				updateTimestampField:                  timestamp,
			},
		).
		Where(
			squirrel.Eq{
				common.BuiltinTableFieldOfAccount: accounts,
			},
		).
		ToSql()
	if err != nil {
		return err
	}

	db, _ := l.svcCtx.DB.RawDB()
	_, err = db.ExecContext(l.ctx, stmt, values...)
	return err
}

func getDataRowIndexMap(tmpDataRows []*model.DataRow, expectedCount int64) map[int64]int64 {
	dataRowIndexMap := make(map[int64]int64)
	totalCount := int64(len(tmpDataRows))
	left := expectedCount
	for {
		if left == 0 {
			break
		}
		for {
			n := random(totalCount)
			_, ok := dataRowIndexMap[n]
			if !ok {
				dataRowIndexMap[n] = n
				break
			}
		}
		left -= 1
	}

	return dataRowIndexMap
}

type handleAccountReq struct {
	PoolEnvTable      string
	PoolEnvModel      model.PoolEnvModel
	SelectedColumns   []string
	SelectedCondition *rpc.Condition

	ExpectedCount int64
	RetryCount    int64

	SuccessAccountList           *[]*model.DataRow
	SuccessAccountRedisLockList  *[]*redislock.RedisLock
	SuccessAccountRedisValueList *[]string
	ErrMsgList                   *[]string
}

func (l *QueryAccountPoolEnvDataLogic) handleAccount(req *handleAccountReq) {
	// 处理账号, 合适的账号会保存到successAccountList中, 错误信息会保存到errMsgList中
	for {
		// 递归退出情形1：剩余重试次数为0
		// 递归退出情形2：账号全部筛选成功
		if req.RetryCount == 0 || req.ExpectedCount == 0 {
			return
		}

		var dataRows []*model.DataRow

		// 获取expectedCount个池账号
		if len(req.SelectedColumns) <= 2 { // 筛选字段个数最多只有2个
			selectBuilder := squirrel.Select(req.SelectedColumns...).From(req.PoolEnvTable)
			selectBuilder = sqlbuilder.SearchOptions(
				selectBuilder, sqlbuilder.WithCondition(req.PoolEnvModel, req.SelectedCondition),
			)
			tmpDataRows, err := l.doQuery(req.PoolEnvModel, selectBuilder)
			if err != nil { // 递归退出情形3：筛选池账号发生异常
				return
			}

			if len(tmpDataRows) > 0 {
				// 从tmpDataRows中随机挑选expectedCount个账号
				dataRowIndexMap := getDataRowIndexMap(tmpDataRows, req.ExpectedCount)
				for k := range dataRowIndexMap {
					dataRows = append(dataRows, tmpDataRows[k])
				}
			}
		} else { // 筛选字段个数大于2个
			primaryColumnKey := req.SelectedColumns[0]
			selectBuilderPre := squirrel.Select(primaryColumnKey).From(req.PoolEnvTable)
			selectBuilderPre = sqlbuilder.SearchOptions(
				selectBuilderPre, sqlbuilder.WithCondition(req.PoolEnvModel, req.SelectedCondition),
			)
			tmpDataRows, err := l.doQuery(req.PoolEnvModel, selectBuilderPre)
			if err != nil { // 递归退出情形3：筛选池账号发生异常
				return
			}

			if len(tmpDataRows) > 0 {
				// 从tmpDataRows中随机挑选expectedCount个账号
				dataRowIndexMap := getDataRowIndexMap(tmpDataRows, req.ExpectedCount)
				var primaryValueSlice []string
				for k := range dataRowIndexMap {
					primaryValueSlice = append(primaryValueSlice, (*tmpDataRows[k])[0].Value)
				}
				realSelectBuilder := squirrel.Select(req.SelectedColumns...).From(req.PoolEnvTable)
				realSelectBuilder = realSelectBuilder.Where(squirrel.Eq{primaryColumnKey: primaryValueSlice})
				dataRows, err = l.doQuery(req.PoolEnvModel, realSelectBuilder)
				if err != nil { // 递归退出情形3：筛选池账号发生异常
					return
				}
			}
		}

		// 递归退出情形4：筛选到的池账号个数少于预期个数（说明账号池当前符合筛选条件的「未使用」的账号数量太少了）
		length := int64(len(dataRows))
		if length < req.ExpectedCount {
			errMsg := fmt.Sprintf(
				"符合筛选条件的池账号个数太少了，预期筛选%d个账号，实际符合的只剩%d个账号", req.ExpectedCount, length,
			)
			if len(*req.ErrMsgList) == 0 {
				*req.ErrMsgList = append(*req.ErrMsgList, errMsg)
			} else {
				(*req.ErrMsgList)[0] = errMsg
			}
			return
		}

		// 下次还需要处理的池账号个数
		newExpectedCount := req.ExpectedCount
		for i := range dataRows {
			row := *dataRows[i]
			primaryValue := row[0].Value
			currentTimestamp := time.Now().UnixMilli()

			lockKey := fmt.Sprintf("%s%s_%s", common.AccountRedisKeyPrefix, req.PoolEnvTable, primaryValue)
			lockValue := strconv.FormatInt(currentTimestamp, 10)
			// lockValue := utils.NewNanoId()

			l.Infof(
				"account查询到了池账号「%s」，准备上redis锁，key为「%s」，value为「%s」， 当前时间戳（纳秒）是：%d",
				primaryValue, lockKey, lockValue, time.Now().UnixNano(),
			)
			rLock, err := redislock.NewRedisLockAndAcquire(
				l.svcCtx.Redis, lockKey, redislock.WithValue(lockValue), redislock.WithExpire(cacheExpireTimeOfOneHour),
			)
			if err == nil {
				l.Infof("给池账号「%s」上redis锁成功, key为「%s」，value为「%s」", primaryValue, lockKey, lockValue)
				// 上锁成功 更新池账号占用状态
				updateRows := []*model.DataRow{&row}
				err = l.updateAccountOccupyState(
					req.PoolEnvTable, updateRows, common.OccupyStateUsing, currentTimestamp,
				)
				if err == nil {
					*req.SuccessAccountList = append(*req.SuccessAccountList, &row)
					*req.SuccessAccountRedisLockList = append(*req.SuccessAccountRedisLockList, rLock)
					*req.SuccessAccountRedisValueList = append(*req.SuccessAccountRedisValueList, lockValue)
					newExpectedCount--
				} else {
					releaseErr := rLock.Release()
					if releaseErr != nil {
						l.Warnf("释放池账号redis锁发生异常: %+v", releaseErr)
					}

					// 递归退出情形5：修改池账号占用状态发生异常
					l.Errorf("修改池账号占用状态发生异常: %+v", err)
					if len(*req.ErrMsgList) == 0 {
						*req.ErrMsgList = append(*req.ErrMsgList, "修改池账号占用状态发生异常")
					} else {
						(*req.ErrMsgList)[0] = "修改池账号占用状态发生异常"
					}
					return
				}
			} else {
				l.Infof("给池账号「%s」上redis锁失败, key为「%s」，value为「%s」", primaryValue, lockKey, lockValue)
			}
		}

		if newExpectedCount == 0 { // 账号全部上锁成功
			return
		} else if newExpectedCount == req.ExpectedCount { // 账号全部上锁不成功
			req.RetryCount = req.RetryCount - 1
		} else {
			req.RetryCount = 10
		}
		req.ExpectedCount = newExpectedCount
	}
}

func getResp(
	successAccountList []*model.DataRow, successAccountRedisValueList []string,
	columnIdColumnTypeMap map[string]string,
	expectedCount, usingCount int64,
) *pb.QueryAccountPoolEnvDataResponse {
	var matchData []*pb.QueryAccountPoolEnvDataResponse_Account

	for idx, dataRowPtr := range successAccountList {
		dataRow := *dataRowPtr
		var account pb.QueryAccountPoolEnvDataResponse_Account
		for _, dataColumn := range dataRow {
			field := dataColumn.Field
			Value := dataColumn.Value
			columnType := columnIdColumnTypeMap[field]
			c := pb.QueryAccountPoolEnvDataResponse_Column{
				Field:      field,
				Value:      Value,
				LockValue:  successAccountRedisValueList[idx],
				ColumnType: pb.ColumnType(pb.ColumnType_value[columnType]),
			}
			account.Account = append(account.Account, &c)
		}
		matchData = append(matchData, &account)
	}

	resp := &pb.QueryAccountPoolEnvDataResponse{
		ExpectedCount: expectedCount,
		MatchCount:    usingCount,
		MatchData:     matchData,
	}
	return resp
}

func (l *QueryAccountPoolEnvDataLogic) releaseAllAccountLock(successAccountRedisLockList []*redislock.RedisLock) {
	for _, lock := range successAccountRedisLockList {
		e := lock.Release()
		if e != nil {
			l.Warnf("释放redis锁发生异常: %+v", e)
		}
	}
}

type acquireAccountsReq struct {
	in                *pb.QueryAccountPoolEnvDataRequest
	poolEnvModel      model.PoolEnvModel
	selectedColumns   []string
	selectedCondition *rpc.Condition
	columnIDTypes     map[string]string
}

func (l *QueryAccountPoolEnvDataLogic) acquireAccountsV1(req *acquireAccountsReq) (
	*pb.QueryAccountPoolEnvDataResponse, error,
) {
	var (
		poolEnvTable  = req.in.GetPoolEnvTable()
		expectedCount = req.in.GetExpectedCount()

		// 筛选成功的池账号 和 筛选账号发生的错误
		successAccountList           = make([]*model.DataRow, 0, expectedCount)
		successAccountRedisLockList  = make([]*redislock.RedisLock, 0, expectedCount)
		successAccountRedisValueList = make([]string, 0, expectedCount)
		errMsgList                   = make([]string, 0, expectedCount)

		err error
	)

	// 获取账号池
	l.handleAccount(
		&handleAccountReq{
			PoolEnvTable:                 poolEnvTable,
			PoolEnvModel:                 req.poolEnvModel,
			SelectedColumns:              req.selectedColumns,
			SelectedCondition:            req.selectedCondition,
			ExpectedCount:                expectedCount,
			RetryCount:                   10,
			SuccessAccountList:           &successAccountList,
			SuccessAccountRedisLockList:  &successAccountRedisLockList,
			SuccessAccountRedisValueList: &successAccountRedisValueList,
			ErrMsgList:                   &errMsgList,
		},
	)

	// 筛选并上锁成功的账号个数
	usingCount := int64(len(successAccountList))

	// 筛选并上锁成功的账号个数与预期个数完全一样
	if usingCount == expectedCount {
		resp := getResp(
			successAccountList, successAccountRedisValueList, req.columnIDTypes, expectedCount, usingCount,
		)
		account := resp.GetMatchData()[0].GetAccount()[0].GetValue()
		l.Infof("account筛选到账号了: %s, 当前时间戳是: %d", account, time.Now().UnixMilli())
		return resp, nil
	}

	// 没有筛选到任何账号
	if usingCount == 0 {
		if len(errMsgList) > 0 {
			return nil, errors.New(errMsgList[0])
		}

		return nil, errorOfNoMatchingAccounts
	}

	// 筛选到部分账号要分情况
	// 1、设置了允许只返回部分账号
	if req.in.GetAllowLessThanExpectedCount() == int64(common.YES) {
		resp := getResp(
			successAccountList, successAccountRedisValueList, req.columnIDTypes, expectedCount, usingCount,
		)
		return resp, nil
	} else { // 2、设置了不允许只返回部分账号 需要释放redis锁和账户占用状态
		l.releaseAllAccountLock(successAccountRedisLockList)
		err = l.updateAccountOccupyState(poolEnvTable, successAccountList, common.OccupyStateUnused, 0)
		if err != nil {
			l.Errorf("释放池账号将账号更改为未使用状态发生错误: %+v", err)
		}
		return nil, errors.Errorf("筛选到的账号不足「%d」个", expectedCount)
	}
}

func (l *QueryAccountPoolEnvDataLogic) acquireAccountsV2(req *acquireAccountsReq) (
	*pb.QueryAccountPoolEnvDataResponse, error,
) {
	var (
		poolEnvTable  = req.in.GetPoolEnvTable()
		expectedCount = req.in.GetExpectedCount()
		timestamp     = time.Now().UnixMilli()
		lockValue     = strconv.FormatInt(timestamp, 10)

		rows []*model.DataRow
		err  error
	)

	key := fmt.Sprintf("%s:%s", common.ConstLockAcquireAccountTablePrefix, poolEnvTable)
	fn := func() error {
		sb := squirrel.Select(req.selectedColumns...).From(poolEnvTable)
		sb = sqlbuilder.SearchOptions(
			sb,
			sqlbuilder.WithCondition(req.poolEnvModel, req.selectedCondition),
			sqlbuilder.WithPagination(
				req.poolEnvModel, &rpc.Pagination{
					CurrentPage: 1,
					PageSize:    uint64(expectedCount),
				},
			),
			sqlbuilder.WithSort(
				req.poolEnvModel, rpc.ConvertSortFields(
					[]*rpc.SortField{
						{
							Field: common.BuiltinTableFieldOfReturnTime,
							Order: constants.ASC,
						},
					},
				),
			),
		)
		rows, err = l.doQuery(req.poolEnvModel, sb)
		if err != nil {
			return err
		}

		count := int64(len(rows))
		if count == 0 {
			// 当前匹配的账号数为零
			l.Errorf("当前匹配的账号数为零, table: %s", poolEnvTable)
			return errorOfNoMatchingAccounts
		}

		if count < expectedCount {
			// 当前匹配的账号数小于期望值
			if req.in.GetAllowLessThanExpectedCount() != int64(common.YES) {
				// 不允许少于期望值
				l.Errorf(
					"匹配的账号数量少于期望值, table: %s, 期望值: %d, 匹配值: %d", poolEnvTable, expectedCount, count,
				)
				return errors.Errorf("筛选到的账号不足「%d」个", expectedCount)
			}
		} else {
			// 当前匹配的账号数大于等于期望值
			rows = rows[0:expectedCount]
		}

		err = l.updateAccountOccupyState(poolEnvTable, rows, common.OccupyStateUsing, timestamp)
		if err != nil {
			l.Errorf("修改池账号占用状态发生异常, table: %s, error: %+v", poolEnvTable, err)
			return errorOfUpdateAccountState
		}

		return nil
	}
	if err = caller.LockWithOptionDo(
		l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeoutOfTenSecond),
	); err != nil {
		return nil, err
	}

	matchCount := int64(len(rows))
	accounts := make([]*pb.QueryAccountPoolEnvDataResponse_Account, 0, matchCount)
	for _, row := range rows {
		if row == nil {
			continue
		}

		columns := make([]*pb.QueryAccountPoolEnvDataResponse_Column, 0, len(*row))
		for _, column := range *row {
			columns = append(
				columns, &pb.QueryAccountPoolEnvDataResponse_Column{
					Field:      column.Field,
					Value:      column.Value,
					LockValue:  lockValue,
					ColumnType: pb.ColumnType(pb.ColumnType_value[req.columnIDTypes[column.Field]]),
				},
			)
		}

		accounts = append(
			accounts, &pb.QueryAccountPoolEnvDataResponse_Account{
				Account: columns,
			},
		)
	}
	l.Infof("占用账号成功, table: %s, count: %d", poolEnvTable, len(accounts))

	return &pb.QueryAccountPoolEnvDataResponse{
		ExpectedCount: expectedCount,
		MatchCount:    matchCount,
		MatchData:     accounts,
	}, nil
}

func generateCondition(inCondition *rpc.Condition, coolingTime int64) *rpc.Condition {
	unusedCondition := &rpc.Condition{
		// `occupy_state` = 1
		Single: &rpc.SingleCondition{
			Field:   common.BuiltinTableFieldOfOccupyState,
			Compare: constants.EQ,
			Other: &rpc.Other{
				Value: common.EnvDataOccupyStateName[int64(common.OccupyStateUnused)],
			},
		},
	}

	if coolingTime > 0 {
		latestReturnTime := time.Now().UnixMilli() - 1000*coolingTime
		returnTimeCondition := &rpc.Condition{
			// `return_time` IS NULL OR `return_time` < ?
			Group: &rpc.GroupCondition{
				Relationship: constants.OR,
				Conditions: []*rpc.Condition{
					{
						Single: &rpc.SingleCondition{
							Field:   common.BuiltinTableFieldOfReturnTime,
							Compare: constants.IsNull,
							Other: &rpc.Other{
								Value: "",
							},
						},
					},
					{
						Single: &rpc.SingleCondition{
							Field:   common.BuiltinTableFieldOfReturnTime,
							Compare: constants.LT,
							Other: &rpc.Other{
								Value: strconv.FormatInt(latestReturnTime, 10),
							},
						},
					},
				},
			},
		}

		// (原查询条件) AND `occupy_state` = 1 AND (`return_time` IS NULL OR `return_time` < ?)
		return &rpc.Condition{
			Group: &rpc.GroupCondition{
				Relationship: constants.AND,
				Conditions: []*rpc.Condition{
					inCondition,
					unusedCondition,
					returnTimeCondition,
				},
			},
		}
	}

	// (原查询条件) AND `occupy_state` = 1
	return &rpc.Condition{
		Group: &rpc.GroupCondition{
			Relationship: constants.AND,
			Conditions: []*rpc.Condition{
				inCondition,
				unusedCondition,
			},
		},
	}
}
