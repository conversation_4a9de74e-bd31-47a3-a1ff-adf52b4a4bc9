package accountlogic

import (
	"testing"

	"github.com/Masterminds/squirrel"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
)

type dummyTable struct {
	name   string
	fields []string
}

func (t *dummyTable) Table() string {
	return t.name
}

func (t *dummyTable) Fields() []string {
	out := make([]string, len(t.fields))
	copy(out, t.fields)
	return out
}

func Test_genCoolingTimeCondition(t *testing.T) {
	type args struct {
		inCondition *rpc.Condition
		coolingTime int64
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "coolingTime is 0",
			args: args{
				inCondition: nil,
				coolingTime: 0,
			},
		},
		{
			name: "coolingTime is 0",
			args: args{
				inCondition: &rpc.Condition{
					Single: &rpc.SingleCondition{
						Field:   "no_playmate",
						Compare: constants.EQ,
						Other: &rpc.Other{
							Value: "Y",
						},
					},
				},
				coolingTime: 0,
			},
		},
		{
			name: "coolingTime is not 0, with nil condition",
			args: args{
				inCondition: nil,
				coolingTime: 10,
			},
		},
		{
			name: "coolingTime is not 0, with single condition",
			args: args{
				inCondition: &rpc.Condition{
					Single: &rpc.SingleCondition{
						Field:   "no_playmate",
						Compare: constants.EQ,
						Other: &rpc.Other{
							Value: "Y",
						},
					},
				},
				coolingTime: 10,
			},
		},
		{
			name: "coolingTime is not 0, with group condition",
			args: args{
				inCondition: &rpc.Condition{
					Group: &rpc.GroupCondition{
						Relationship: constants.OR,
						Conditions: []*rpc.Condition{
							{
								Single: &rpc.SingleCondition{
									Field:   "no_playmate",
									Compare: constants.EQ,
									Other: &rpc.Other{
										Value: "Y",
									},
								},
							},
							{
								Single: &rpc.SingleCondition{
									Field:   "recommend_god",
									Compare: constants.EQ,
									Other: &rpc.Other{
										Value: "Y",
									},
								},
							},
						},
					},
				},
				coolingTime: 10,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := generateCondition(tt.args.inCondition, tt.args.coolingTime)
				t.Logf("%s", protobuf.MarshalJSONIgnoreError(got))

				table := "t"
				fields := []string{
					"`account`", "`password`", "`occupy_state`", "`return_time`",
					"`no_playmate`", "`recommend_god`",
				}

				sb := squirrel.Select("*").From(table)
				sb = sqlbuilder.SearchOptions(
					sb, sqlbuilder.WithCondition(
						&dummyTable{
							name:   table,
							fields: fields,
						}, got,
					),
				)
				stmt, values, err := sb.ToSql()
				if err != nil {
					t.Fatal(err)
				}

				t.Logf("stmt: %s\nvalues: %+v", stmt, values)
			},
		)
	}
}
