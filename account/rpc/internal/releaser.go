package internal

import (
	"context"
	"database/sql"
	"strconv"
	"sync"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/trace"
	oteltrace "go.opentelemetry.io/otel/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
	accountlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/logic/account"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
)

const (
	spanName = "account"

	defaultMaxOccupancyDuration = time.Hour
	expireOfReleaseAccountTask  = 30 * time.Second
)

var (
	_ IReleaser = (*NoopReleaser)(nil)
	_ IReleaser = (*Releaser)(nil)

	poolEnvDataFields = []string{
		"`" + common.BuiltinTableFieldOfAcquireTime + "`", // `acquire_time`
		"`" + common.BuiltinTableFieldOfReturnTime + "`",  // `return_time`
		"`" + common.BuiltinTableFieldOfOccupyState + "`", // `occupy_state`
		"`" + common.BuiltinTableFieldOfAccount + "`",     // `account`
	}
)

type (
	IReleaser interface {
		Run()
	}

	NoopReleaser struct{}

	Releaser struct {
		logx.Logger
		ctx    context.Context
		svcCtx *svc.ServiceContext

		closeOnce sync.Once
		quitCh    chan lang.PlaceholderType

		rules map[string]common.ReleaseRuleConfig
	}

	poolEnvData struct {
		AcquireTime sql.NullInt64 `json:"acquire_time"`
		ReturnTime  sql.NullInt64 `json:"return_time"`
		OccupyState string        `json:"occupy_state"`
		Account     string        `json:"account"`
	}
)

func NewReleaser(svcCtx *svc.ServiceContext) IReleaser {
	if svcCtx.Config.ReleaseRules == nil {
		return &NoopReleaser{}
	}

	ctx := context.Background()

	rules := make(map[string]common.ReleaseRuleConfig, len(svcCtx.Config.ReleaseRules))
	for _, rr := range svcCtx.Config.ReleaseRules {
		if rr.TableName == "" {
			continue
		}
		if rr.MaxOccupancyDuration < defaultMaxOccupancyDuration {
			rr.MaxOccupancyDuration = defaultMaxOccupancyDuration
		}

		rule, ok := rules[rr.TableName]
		if !ok || rr.MaxOccupancyDuration < rule.MaxOccupancyDuration {
			rules[rr.TableName] = rr
		}
	}

	r := &Releaser{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		quitCh: make(chan lang.PlaceholderType, 1),

		rules: rules,
	}

	proc.AddShutdownListener(r.Close)
	return r
}

func (r *NoopReleaser) Run() {}

func (r *Releaser) Close() {
	r.closeOnce.Do(
		func() {
			close(r.quitCh)
		},
	)
}

func (r *Releaser) Run() {
	ticker := timewheel.NewTicker(time.Minute)
	defer ticker.Stop()

	var (
		key = common.ConstLockReleaseAccount
		fn  = func() error {
			r.release()
			return nil
		}
	)

	for {
		select {
		case <-r.quitCh:
			r.Info("got a quit signal while releasing accounts that have been occupied for a long time")
			return
		case <-ticker.C:
			err := caller.LockWithOptionDo(
				r.svcCtx.Redis, key, fn, redislock.WithExpire(expireOfReleaseAccountTask),
			)
			if err != nil {
				if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
					r.Errorf("failed to acquire the redis lock with key[%s], error: %+v", key, err)
				} else {
					r.Debug("another service is releasing accounts")
				}
				continue
			}
		}
	}
}

func (r *Releaser) release() {
	ctx, cancel := context.WithTimeout(r.ctx, expireOfReleaseAccountTask)
	defer cancel()

	tracer := trace.TracerFromContext(ctx)
	ctx, span := tracer.Start(ctx, spanName, oteltrace.WithSpanKind(oteltrace.SpanKindClient))
	defer span.End()

	logic := accountlogic.NewReleaseTestAccountLogic(ctx, r.svcCtx)

	tables, err := r.svcCtx.TPoolTableModel.FindAllUsingPoolEnvTables(ctx)
	if err != nil {
		r.Errorf("failed to find using pool env tables, error: %+v", err)
		return
	} else if len(tables) == 0 {
		r.Debug("not found any using pool env tables")
		return
	}

	_ = mr.MapReduceVoid[*model.TPoolTable, *pb.ReleaseTestAccountRequest](
		func(source chan<- *model.TPoolTable) {
			for _, table := range tables {
				if table == nil {
					continue
				} else if !table.ParentPoolId.Valid || table.ParentPoolId.Int64 == 0 {
					continue
				} else if table.IsUsing != common.YesOrNoName[int64(common.YES)] {
					continue
				} else if table.TableName == "" {
					continue
				}

				source <- table
			}
		}, func(item *model.TPoolTable, writer mr.Writer[*pb.ReleaseTestAccountRequest], cancel func(error)) {
			if item == nil {
				return
			}

			accounts, err := r.search(ctx, item.TableName)
			if err != nil {
				r.Errorf(
					"failed to search accounts that have been occupied for a long time, table: %s, error: %+v",
					item.TableName, err,
				)
				return
			} else if len(accounts) == 0 {
				r.Debugf("no accounts found that need to be released, table: %s", item.TableName)
				return
			}

			releaseAccountChunkSize := 500
			count := (len(accounts) + releaseAccountChunkSize - 1) / releaseAccountChunkSize
			accountChunks := make([][]*poolEnvData, count)
			for i := 0; i < len(accounts); i += releaseAccountChunkSize {
				end := i + releaseAccountChunkSize
				if end > len(accounts) {
					end = len(accounts)
				}
				accountChunks[i/releaseAccountChunkSize] = accounts[i:end]
			}

			for _, chunkAccounts := range accountChunks {
				req := &pb.ReleaseTestAccountRequest{
					ReleaseTasAccountArray: []*pb.ReleaseTestAccountRequest_PoolAccount{
						{
							PoolEnvTable: item.TableName,
							AccountArray: make([]*pb.ReleaseTestAccountRequest_Account, 0, len(chunkAccounts)),
						},
					},
				}
				for _, chunkAccount := range chunkAccounts {
					var lockValue string
					if chunkAccount.AcquireTime.Valid {
						lockValue = strconv.FormatInt(chunkAccount.AcquireTime.Int64, 10)
					}

					req.ReleaseTasAccountArray[0].AccountArray = append(
						req.ReleaseTasAccountArray[0].AccountArray, &pb.ReleaseTestAccountRequest_Account{
							Account:   chunkAccount.Account,
							LockValue: lockValue,
						},
					)
				}
				writer.Write(req)
			}
		}, func(pipe <-chan *pb.ReleaseTestAccountRequest, cancel func(error)) {
			for item := range pipe {
				if item == nil {
					continue
				}

				if _, err := logic.ReleaseTestAccount(item); err != nil {
					r.Errorf(
						"failed to release accounts that have been occupied for a long time, req: %s, error: %+v",
						protobuf.MarshalJSONIgnoreError(item), err,
					)
				} else {
					r.Infof(
						"release accounts that have been occupied for a long time successfully, table: %s, count: %d",
						item.GetReleaseTasAccountArray()[0].GetPoolEnvTable(),
						len(item.GetReleaseTasAccountArray()[0].GetAccountArray()),
					)
				}
			}
		},
	)
}

func (r *Releaser) search(ctx context.Context, table string) ([]*poolEnvData, error) {
	maxOccupancyDuration := defaultMaxOccupancyDuration
	rule, ok := r.rules[table]
	if ok {
		maxOccupancyDuration = rule.MaxOccupancyDuration
	}

	poolEnvTableModel := model.NewPoolEnvTableModel(r.svcCtx.DB, table, poolEnvDataFields)
	query, values, err := squirrel.Select(poolEnvTableModel.Fields()...).
		From(poolEnvTableModel.Table()).
		Where(
			"`occupy_state` = ? and REPLACE(unix_timestamp(current_timestamp(3)), '.', '') - `acquire_time` > ?",
			common.OccupyStateUsing, maxOccupancyDuration.Milliseconds(),
		).
		ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*poolEnvData
	err = poolEnvTableModel.QueryRowsCtx(ctx, &resp, query, values...)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
