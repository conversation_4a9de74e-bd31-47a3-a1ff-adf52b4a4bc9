// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: account.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	accountlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/logic/account"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
)

type AccountServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedAccountServer
}

func NewAccountServer(svcCtx *svc.ServiceContext) *AccountServer {
	return &AccountServer{
		svcCtx: svcCtx,
	}
}

func (s *AccountServer) QueryAccountPoolEnvData(ctx context.Context, in *pb.QueryAccountPoolEnvDataRequest) (*pb.QueryAccountPoolEnvDataResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := accountlogic.NewQueryAccountPoolEnvDataLogic(ctx, s.svcCtx)

	return l.QueryAccountPoolEnvData(in)
}

func (s *AccountServer) ReleaseTestAccount(ctx context.Context, in *pb.ReleaseTestAccountRequest) (*pb.ReleaseTestAccountResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := accountlogic.NewReleaseTestAccountLogic(ctx, s.svcCtx)

	return l.ReleaseTestAccount(in)
}
