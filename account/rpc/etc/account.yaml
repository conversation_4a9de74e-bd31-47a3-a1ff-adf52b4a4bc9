Name: rpc.account
ListenOn: 127.0.0.1:20111
Timeout: 0

Log:
  ServiceName: rpc.account
  Encoding: plain
  Level: info
  Path: /app/logs/account

Prometheus:
  Host: 0.0.0.0
  Port: 20122
  Path: /metrics

Telemetry:
  Name: rpc.permission
  Endpoint: http://127.0.0.1:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

#Etcd:
#  Hosts:
#  - 127.0.0.1:2379
#  Key: rpc.account

DB:
  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/account?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Redis:
  Key: rpc.account
  Host: 127.0.0.1:6379
  Type: node
  Pass: Quwan@2020
  DB: 6

ReleaseRules:
  - TableName: t_tt_ui_1699353439549
    MaxOccupancyDuration: 5h
  - TableName: t_tt_ui_1721201180448
    MaxOccupancyDuration: 5h
  - TableName: t_tt_ui_1721199303089

AccountConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:account
  ConsumerTag: mqc:account
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 20
  MaxWorker: 40
