// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: account.proto

package account

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
)

type (
	QueryAccountPoolEnvDataRequest          = pb.QueryAccountPoolEnvDataRequest
	QueryAccountPoolEnvDataResponse         = pb.QueryAccountPoolEnvDataResponse
	QueryAccountPoolEnvDataResponse_Account = pb.QueryAccountPoolEnvDataResponse_Account
	QueryAccountPoolEnvDataResponse_Column  = pb.QueryAccountPoolEnvDataResponse_Column
	ReleaseTestAccountRequest               = pb.ReleaseTestAccountRequest
	ReleaseTestAccountRequest_Account       = pb.ReleaseTestAccountRequest_Account
	ReleaseTestAccountRequest_PoolAccount   = pb.ReleaseTestAccountRequest_PoolAccount
	ReleaseTestAccountResponse              = pb.ReleaseTestAccountResponse

	Account interface {
		QueryAccountPoolEnvData(ctx context.Context, in *QueryAccountPoolEnvDataRequest, opts ...grpc.CallOption) (*QueryAccountPoolEnvDataResponse, error)
		ReleaseTestAccount(ctx context.Context, in *ReleaseTestAccountRequest, opts ...grpc.CallOption) (*ReleaseTestAccountResponse, error)
	}

	defaultAccount struct {
		cli zrpc.Client
	}
)

func NewAccount(cli zrpc.Client) Account {
	return &defaultAccount{
		cli: cli,
	}
}

func (m *defaultAccount) QueryAccountPoolEnvData(ctx context.Context, in *QueryAccountPoolEnvDataRequest, opts ...grpc.CallOption) (*QueryAccountPoolEnvDataResponse, error) {
	client := pb.NewAccountClient(m.cli.Conn())
	return client.QueryAccountPoolEnvData(ctx, in, opts...)
}

func (m *defaultAccount) ReleaseTestAccount(ctx context.Context, in *ReleaseTestAccountRequest, opts ...grpc.CallOption) (*ReleaseTestAccountResponse, error) {
	client := pb.NewAccountClient(m.cli.Conn())
	return client.ReleaseTestAccount(ctx, in, opts...)
}
