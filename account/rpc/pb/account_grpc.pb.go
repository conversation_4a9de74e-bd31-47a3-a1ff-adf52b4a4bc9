// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.2
// source: account/account.proto

package pb

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Account_QueryAccountPoolEnvData_FullMethodName = "/account.Account/QueryAccountPoolEnvData"
	Account_ReleaseTestAccount_FullMethodName      = "/account.Account/ReleaseTestAccount"
)

// AccountClient is the client API for Account service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountClient interface {
	QueryAccountPoolEnvData(ctx context.Context, in *QueryAccountPoolEnvDataRequest, opts ...grpc.CallOption) (*QueryAccountPoolEnvDataResponse, error)
	ReleaseTestAccount(ctx context.Context, in *ReleaseTestAccountRequest, opts ...grpc.CallOption) (*ReleaseTestAccountResponse, error)
}

type accountClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountClient(cc grpc.ClientConnInterface) AccountClient {
	return &accountClient{cc}
}

func (c *accountClient) QueryAccountPoolEnvData(ctx context.Context, in *QueryAccountPoolEnvDataRequest, opts ...grpc.CallOption) (*QueryAccountPoolEnvDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryAccountPoolEnvDataResponse)
	err := c.cc.Invoke(ctx, Account_QueryAccountPoolEnvData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) ReleaseTestAccount(ctx context.Context, in *ReleaseTestAccountRequest, opts ...grpc.CallOption) (*ReleaseTestAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReleaseTestAccountResponse)
	err := c.cc.Invoke(ctx, Account_ReleaseTestAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountServer is the server API for Account service.
// All implementations must embed UnimplementedAccountServer
// for forward compatibility.
type AccountServer interface {
	QueryAccountPoolEnvData(context.Context, *QueryAccountPoolEnvDataRequest) (*QueryAccountPoolEnvDataResponse, error)
	ReleaseTestAccount(context.Context, *ReleaseTestAccountRequest) (*ReleaseTestAccountResponse, error)
	mustEmbedUnimplementedAccountServer()
}

// UnimplementedAccountServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAccountServer struct{}

func (UnimplementedAccountServer) QueryAccountPoolEnvData(context.Context, *QueryAccountPoolEnvDataRequest) (*QueryAccountPoolEnvDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryAccountPoolEnvData not implemented")
}
func (UnimplementedAccountServer) ReleaseTestAccount(context.Context, *ReleaseTestAccountRequest) (*ReleaseTestAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseTestAccount not implemented")
}
func (UnimplementedAccountServer) mustEmbedUnimplementedAccountServer() {}
func (UnimplementedAccountServer) testEmbeddedByValue()                 {}

// UnsafeAccountServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountServer will
// result in compilation errors.
type UnsafeAccountServer interface {
	mustEmbedUnimplementedAccountServer()
}

func RegisterAccountServer(s grpc.ServiceRegistrar, srv AccountServer) {
	// If the following call pancis, it indicates UnimplementedAccountServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Account_ServiceDesc, srv)
}

func _Account_QueryAccountPoolEnvData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryAccountPoolEnvDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).QueryAccountPoolEnvData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_QueryAccountPoolEnvData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).QueryAccountPoolEnvData(ctx, req.(*QueryAccountPoolEnvDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ReleaseTestAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseTestAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).ReleaseTestAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_ReleaseTestAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).ReleaseTestAccount(ctx, req.(*ReleaseTestAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Account_ServiceDesc is the grpc.ServiceDesc for Account service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Account_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "account.Account",
	HandlerType: (*AccountServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryAccountPoolEnvData",
			Handler:    _Account_QueryAccountPoolEnvData_Handler,
		},
		{
			MethodName: "ReleaseTestAccount",
			Handler:    _Account_ReleaseTestAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "account/account.proto",
}
