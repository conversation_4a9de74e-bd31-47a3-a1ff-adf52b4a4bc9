// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: account/account.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on QueryAccountPoolEnvDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryAccountPoolEnvDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryAccountPoolEnvDataRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// QueryAccountPoolEnvDataRequestMultiError, or nil if none found.
func (m *QueryAccountPoolEnvDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryAccountPoolEnvDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPoolEnvTable()) < 1 {
		err := QueryAccountPoolEnvDataRequestValidationError{
			field:  "PoolEnvTable",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryAccountPoolEnvDataRequestValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryAccountPoolEnvDataRequestValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryAccountPoolEnvDataRequestValidationError{
				field:  "Condition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetSelectedColumnIdArray()) > 0 {

		_QueryAccountPoolEnvDataRequest_SelectedColumnIdArray_Unique := make(map[string]struct{}, len(m.GetSelectedColumnIdArray()))

		for idx, item := range m.GetSelectedColumnIdArray() {
			_, _ = idx, item

			if _, exists := _QueryAccountPoolEnvDataRequest_SelectedColumnIdArray_Unique[item]; exists {
				err := QueryAccountPoolEnvDataRequestValidationError{
					field:  fmt.Sprintf("SelectedColumnIdArray[%v]", idx),
					reason: "repeated value must contain unique items",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			} else {
				_QueryAccountPoolEnvDataRequest_SelectedColumnIdArray_Unique[item] = struct{}{}
			}

			if utf8.RuneCountInString(item) < 1 {
				err := QueryAccountPoolEnvDataRequestValidationError{
					field:  fmt.Sprintf("SelectedColumnIdArray[%v]", idx),
					reason: "value length must be at least 1 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	}

	if val := m.GetExpectedCount(); val < 1 || val > 1000 {
		err := QueryAccountPoolEnvDataRequestValidationError{
			field:  "ExpectedCount",
			reason: "value must be inside range [1, 1000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAllowLessThanExpectedCount() < 0 {
		err := QueryAccountPoolEnvDataRequestValidationError{
			field:  "AllowLessThanExpectedCount",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return QueryAccountPoolEnvDataRequestMultiError(errors)
	}

	return nil
}

// QueryAccountPoolEnvDataRequestMultiError is an error wrapping multiple
// validation errors returned by QueryAccountPoolEnvDataRequest.ValidateAll()
// if the designated constraints aren't met.
type QueryAccountPoolEnvDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryAccountPoolEnvDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryAccountPoolEnvDataRequestMultiError) AllErrors() []error { return m }

// QueryAccountPoolEnvDataRequestValidationError is the validation error
// returned by QueryAccountPoolEnvDataRequest.Validate if the designated
// constraints aren't met.
type QueryAccountPoolEnvDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryAccountPoolEnvDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryAccountPoolEnvDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryAccountPoolEnvDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryAccountPoolEnvDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryAccountPoolEnvDataRequestValidationError) ErrorName() string {
	return "QueryAccountPoolEnvDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryAccountPoolEnvDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryAccountPoolEnvDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryAccountPoolEnvDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryAccountPoolEnvDataRequestValidationError{}

// Validate checks the field values on QueryAccountPoolEnvDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryAccountPoolEnvDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryAccountPoolEnvDataResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// QueryAccountPoolEnvDataResponseMultiError, or nil if none found.
func (m *QueryAccountPoolEnvDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryAccountPoolEnvDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExpectedCount

	// no validation rules for MatchCount

	for idx, item := range m.GetMatchData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryAccountPoolEnvDataResponseValidationError{
						field:  fmt.Sprintf("MatchData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryAccountPoolEnvDataResponseValidationError{
						field:  fmt.Sprintf("MatchData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryAccountPoolEnvDataResponseValidationError{
					field:  fmt.Sprintf("MatchData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QueryAccountPoolEnvDataResponseMultiError(errors)
	}

	return nil
}

// QueryAccountPoolEnvDataResponseMultiError is an error wrapping multiple
// validation errors returned by QueryAccountPoolEnvDataResponse.ValidateAll()
// if the designated constraints aren't met.
type QueryAccountPoolEnvDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryAccountPoolEnvDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryAccountPoolEnvDataResponseMultiError) AllErrors() []error { return m }

// QueryAccountPoolEnvDataResponseValidationError is the validation error
// returned by QueryAccountPoolEnvDataResponse.Validate if the designated
// constraints aren't met.
type QueryAccountPoolEnvDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryAccountPoolEnvDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryAccountPoolEnvDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryAccountPoolEnvDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryAccountPoolEnvDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryAccountPoolEnvDataResponseValidationError) ErrorName() string {
	return "QueryAccountPoolEnvDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e QueryAccountPoolEnvDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryAccountPoolEnvDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryAccountPoolEnvDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryAccountPoolEnvDataResponseValidationError{}

// Validate checks the field values on ReleaseTestAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReleaseTestAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseTestAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReleaseTestAccountRequestMultiError, or nil if none found.
func (m *ReleaseTestAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseTestAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetReleaseTasAccountArray()) > 10 {
		err := ReleaseTestAccountRequestValidationError{
			field:  "ReleaseTasAccountArray",
			reason: "value must contain no more than 10 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetReleaseTasAccountArray() {
		_, _ = idx, item

		if item == nil {
			err := ReleaseTestAccountRequestValidationError{
				field:  fmt.Sprintf("ReleaseTasAccountArray[%v]", idx),
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReleaseTestAccountRequestValidationError{
						field:  fmt.Sprintf("ReleaseTasAccountArray[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReleaseTestAccountRequestValidationError{
						field:  fmt.Sprintf("ReleaseTasAccountArray[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReleaseTestAccountRequestValidationError{
					field:  fmt.Sprintf("ReleaseTasAccountArray[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ReleaseTestAccountRequestMultiError(errors)
	}

	return nil
}

// ReleaseTestAccountRequestMultiError is an error wrapping multiple validation
// errors returned by ReleaseTestAccountRequest.ValidateAll() if the
// designated constraints aren't met.
type ReleaseTestAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseTestAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseTestAccountRequestMultiError) AllErrors() []error { return m }

// ReleaseTestAccountRequestValidationError is the validation error returned by
// ReleaseTestAccountRequest.Validate if the designated constraints aren't met.
type ReleaseTestAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseTestAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseTestAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseTestAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseTestAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseTestAccountRequestValidationError) ErrorName() string {
	return "ReleaseTestAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseTestAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseTestAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseTestAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseTestAccountRequestValidationError{}

// Validate checks the field values on ReleaseTestAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReleaseTestAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseTestAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReleaseTestAccountResponseMultiError, or nil if none found.
func (m *ReleaseTestAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseTestAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ReleaseTestAccountResponseMultiError(errors)
	}

	return nil
}

// ReleaseTestAccountResponseMultiError is an error wrapping multiple
// validation errors returned by ReleaseTestAccountResponse.ValidateAll() if
// the designated constraints aren't met.
type ReleaseTestAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseTestAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseTestAccountResponseMultiError) AllErrors() []error { return m }

// ReleaseTestAccountResponseValidationError is the validation error returned
// by ReleaseTestAccountResponse.Validate if the designated constraints aren't met.
type ReleaseTestAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseTestAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseTestAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseTestAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseTestAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseTestAccountResponseValidationError) ErrorName() string {
	return "ReleaseTestAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseTestAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseTestAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseTestAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseTestAccountResponseValidationError{}

// Validate checks the field values on QueryAccountPoolEnvDataResponse_Column
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *QueryAccountPoolEnvDataResponse_Column) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryAccountPoolEnvDataResponse_Column with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// QueryAccountPoolEnvDataResponse_ColumnMultiError, or nil if none found.
func (m *QueryAccountPoolEnvDataResponse_Column) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryAccountPoolEnvDataResponse_Column) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Field

	// no validation rules for Value

	// no validation rules for LockValue

	// no validation rules for ColumnType

	if len(errors) > 0 {
		return QueryAccountPoolEnvDataResponse_ColumnMultiError(errors)
	}

	return nil
}

// QueryAccountPoolEnvDataResponse_ColumnMultiError is an error wrapping
// multiple validation errors returned by
// QueryAccountPoolEnvDataResponse_Column.ValidateAll() if the designated
// constraints aren't met.
type QueryAccountPoolEnvDataResponse_ColumnMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryAccountPoolEnvDataResponse_ColumnMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryAccountPoolEnvDataResponse_ColumnMultiError) AllErrors() []error { return m }

// QueryAccountPoolEnvDataResponse_ColumnValidationError is the validation
// error returned by QueryAccountPoolEnvDataResponse_Column.Validate if the
// designated constraints aren't met.
type QueryAccountPoolEnvDataResponse_ColumnValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryAccountPoolEnvDataResponse_ColumnValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryAccountPoolEnvDataResponse_ColumnValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryAccountPoolEnvDataResponse_ColumnValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryAccountPoolEnvDataResponse_ColumnValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryAccountPoolEnvDataResponse_ColumnValidationError) ErrorName() string {
	return "QueryAccountPoolEnvDataResponse_ColumnValidationError"
}

// Error satisfies the builtin error interface
func (e QueryAccountPoolEnvDataResponse_ColumnValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryAccountPoolEnvDataResponse_Column.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryAccountPoolEnvDataResponse_ColumnValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryAccountPoolEnvDataResponse_ColumnValidationError{}

// Validate checks the field values on QueryAccountPoolEnvDataResponse_Account
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *QueryAccountPoolEnvDataResponse_Account) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryAccountPoolEnvDataResponse_Account with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// QueryAccountPoolEnvDataResponse_AccountMultiError, or nil if none found.
func (m *QueryAccountPoolEnvDataResponse_Account) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryAccountPoolEnvDataResponse_Account) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAccount() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryAccountPoolEnvDataResponse_AccountValidationError{
						field:  fmt.Sprintf("Account[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryAccountPoolEnvDataResponse_AccountValidationError{
						field:  fmt.Sprintf("Account[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryAccountPoolEnvDataResponse_AccountValidationError{
					field:  fmt.Sprintf("Account[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QueryAccountPoolEnvDataResponse_AccountMultiError(errors)
	}

	return nil
}

// QueryAccountPoolEnvDataResponse_AccountMultiError is an error wrapping
// multiple validation errors returned by
// QueryAccountPoolEnvDataResponse_Account.ValidateAll() if the designated
// constraints aren't met.
type QueryAccountPoolEnvDataResponse_AccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryAccountPoolEnvDataResponse_AccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryAccountPoolEnvDataResponse_AccountMultiError) AllErrors() []error { return m }

// QueryAccountPoolEnvDataResponse_AccountValidationError is the validation
// error returned by QueryAccountPoolEnvDataResponse_Account.Validate if the
// designated constraints aren't met.
type QueryAccountPoolEnvDataResponse_AccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryAccountPoolEnvDataResponse_AccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryAccountPoolEnvDataResponse_AccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryAccountPoolEnvDataResponse_AccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryAccountPoolEnvDataResponse_AccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryAccountPoolEnvDataResponse_AccountValidationError) ErrorName() string {
	return "QueryAccountPoolEnvDataResponse_AccountValidationError"
}

// Error satisfies the builtin error interface
func (e QueryAccountPoolEnvDataResponse_AccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryAccountPoolEnvDataResponse_Account.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryAccountPoolEnvDataResponse_AccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryAccountPoolEnvDataResponse_AccountValidationError{}

// Validate checks the field values on ReleaseTestAccountRequest_Account with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ReleaseTestAccountRequest_Account) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseTestAccountRequest_Account
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ReleaseTestAccountRequest_AccountMultiError, or nil if none found.
func (m *ReleaseTestAccountRequest_Account) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseTestAccountRequest_Account) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetAccount()) < 1 {
		err := ReleaseTestAccountRequest_AccountValidationError{
			field:  "Account",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetLockValue() != "" {

		if utf8.RuneCountInString(m.GetLockValue()) < 1 {
			err := ReleaseTestAccountRequest_AccountValidationError{
				field:  "LockValue",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ReleaseTestAccountRequest_AccountMultiError(errors)
	}

	return nil
}

// ReleaseTestAccountRequest_AccountMultiError is an error wrapping multiple
// validation errors returned by
// ReleaseTestAccountRequest_Account.ValidateAll() if the designated
// constraints aren't met.
type ReleaseTestAccountRequest_AccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseTestAccountRequest_AccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseTestAccountRequest_AccountMultiError) AllErrors() []error { return m }

// ReleaseTestAccountRequest_AccountValidationError is the validation error
// returned by ReleaseTestAccountRequest_Account.Validate if the designated
// constraints aren't met.
type ReleaseTestAccountRequest_AccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseTestAccountRequest_AccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseTestAccountRequest_AccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseTestAccountRequest_AccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseTestAccountRequest_AccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseTestAccountRequest_AccountValidationError) ErrorName() string {
	return "ReleaseTestAccountRequest_AccountValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseTestAccountRequest_AccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseTestAccountRequest_Account.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseTestAccountRequest_AccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseTestAccountRequest_AccountValidationError{}

// Validate checks the field values on ReleaseTestAccountRequest_PoolAccount
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ReleaseTestAccountRequest_PoolAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseTestAccountRequest_PoolAccount
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ReleaseTestAccountRequest_PoolAccountMultiError, or nil if none found.
func (m *ReleaseTestAccountRequest_PoolAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseTestAccountRequest_PoolAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPoolEnvTable()) < 1 {
		err := ReleaseTestAccountRequest_PoolAccountValidationError{
			field:  "PoolEnvTable",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetAccountArray()) > 1000 {
		err := ReleaseTestAccountRequest_PoolAccountValidationError{
			field:  "AccountArray",
			reason: "value must contain no more than 1000 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetAccountArray() {
		_, _ = idx, item

		if item == nil {
			err := ReleaseTestAccountRequest_PoolAccountValidationError{
				field:  fmt.Sprintf("AccountArray[%v]", idx),
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReleaseTestAccountRequest_PoolAccountValidationError{
						field:  fmt.Sprintf("AccountArray[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReleaseTestAccountRequest_PoolAccountValidationError{
						field:  fmt.Sprintf("AccountArray[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReleaseTestAccountRequest_PoolAccountValidationError{
					field:  fmt.Sprintf("AccountArray[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ReleaseTestAccountRequest_PoolAccountMultiError(errors)
	}

	return nil
}

// ReleaseTestAccountRequest_PoolAccountMultiError is an error wrapping
// multiple validation errors returned by
// ReleaseTestAccountRequest_PoolAccount.ValidateAll() if the designated
// constraints aren't met.
type ReleaseTestAccountRequest_PoolAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseTestAccountRequest_PoolAccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseTestAccountRequest_PoolAccountMultiError) AllErrors() []error { return m }

// ReleaseTestAccountRequest_PoolAccountValidationError is the validation error
// returned by ReleaseTestAccountRequest_PoolAccount.Validate if the
// designated constraints aren't met.
type ReleaseTestAccountRequest_PoolAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseTestAccountRequest_PoolAccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseTestAccountRequest_PoolAccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseTestAccountRequest_PoolAccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseTestAccountRequest_PoolAccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseTestAccountRequest_PoolAccountValidationError) ErrorName() string {
	return "ReleaseTestAccountRequest_PoolAccountValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseTestAccountRequest_PoolAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseTestAccountRequest_PoolAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseTestAccountRequest_PoolAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseTestAccountRequest_PoolAccountValidationError{}
