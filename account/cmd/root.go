package cmd

import "github.com/spf13/cobra"

const (
	rootCmdUse   = "account"
	rootCmdShort = "Account is one of the microservices of the Quality Platform"
	rootCmdLong  = `Account is one of the microservices of the Quality Platform. 
The main function is the management of the test resources, such as test cases, test suites, test plans...`
)

var (
	usageTemplate = `Usage:

{{- if .Runnable}}  {{.UseLine}}{{end}}
{{- if .HasAvailableSubCommands}}  {{.CommandPath}}{{- if .HasAvailableFlags}} [OPTIONS]{{end}} COMMAND
{{- else if .HasAvailableFlags}} [OPTIONS]
{{- end}}

{{with (or .Long .Short)}}{{ . | trim }}{{end}}

{{- if gt (len .<PERSON>) 0}}

Aliases:
  {{.NameAndAliases}}

{{- end}}

{{- if .HasExample}}

Examples:
{{.Example}}

{{- end}}

{{- if .HasAvailableLocalFlags}}

Options:
{{.LocalFlags.FlagUsages | trimTrailingWhitespaces}}

{{- end}}

{{- if .HasAvailableInheritedFlags}}

Global Options:
{{.InheritedFlags.FlagUsages | trimTrailingWhitespaces}}

{{- end}}

{{- if .HasAvailableSubCommands}}

Available Commands:
{{- range .Commands}}
{{- if (or .IsAvailableCommand (eq .Name "help"))}}
  {{rpad .Name .NamePadding }} {{.Short}}
{{- end}}
{{- end}}

{{- end}}

{{- if .HasHelpSubCommands}}

Additional help topics:
{{- range .Commands}}
{{- if .IsAdditionalHelpTopicCommand}}
  {{rpad .CommandPath .CommandPathPadding}} {{.Short}}
{{- end}}
{{- end}}

{{- end}}

{{- if .HasAvailableSubCommands}}

Use "{{.CommandPath}} COMMAND --help" for more information about a command.

{{- end}}
`

	helpTemplate = `
{{if or .Runnable .HasSubCommands}}{{.UsageString}}{{end}}`

	versionTemplate = `{{.Version}}`
)

func NewRootCommand() *cobra.Command {
	root := &cobra.Command{
		Use:                   rootCmdUse,
		Short:                 rootCmdShort,
		Long:                  rootCmdLong,
		SilenceUsage:          true,
		SilenceErrors:         true,
		TraverseChildren:      true,
		DisableFlagsInUseLine: true,
	}

	root.SetUsageTemplate(usageTemplate)
	root.SetHelpTemplate(helpTemplate)
	root.SetVersionTemplate(versionTemplate)

	return root
}
