package utils

import (
	"bufio"
	"context"
	"database/sql"
	"fmt"
	"io"
	"os"
	"path"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type PoolEnvDataExecCurrentJson struct {
	ExcelFileName string
	SqlFileName   string
	QuerySql      string
	ExpectedCount int64
	Way           string
	OkCount       int64
}

func finalAction(logger logx.Logger, ctx context.Context, db *sql.DB, envTableName, executeId, stateName, result string) {
	// 确保将流水管理的所有测试账号数据恢复为'未使用'状态
	updateSql := fmt.Sprintf(
		"update %s set occupy_state='%s' where related_execute_id='%s' and occupy_state!='%s'",
		envTableName, "UNUSED", executeId, "USING")
	_, err := db.ExecContext(ctx, updateSql)
	if err != nil {
		logger.Error("流水execute_id为：【%s】执行恢复关联池账号状态为【未使用】时发生异常：%s", executeId, err)
	}

	// 修改操作流水
	recordSql := "update t_execute_record set state=?, result=? where execute_id=?"
	_, err = db.ExecContext(ctx, recordSql, stateName, result, executeId)
	if err != nil {
		logger.Error("流水execute_id为：%s执行修改操作流水时发生异常：%s， "+
			"其中recordSql为：[%s], state未：【%s】, result为：【%s】",
			executeId, err, recordSql, stateName, result)
	}
}

func ExecuteSql(logger logx.Logger, svcCtx *svc.ServiceContext, sqlFileName, envTableName string, fields []string, executeId string) {
	// ctx跨协程使用会有问题，得重新赋值
	ctx := context.Background()

	// 创建db
	sqlConn := sqlx.NewMysql(svcCtx.Config.DB.DataSource)
	db, _ := sqlConn.RawDB()

	// 最终更新的数据
	stateName := "FINISHING"
	result := "数据完成变更"

	defer func() {
		if recoverError := recover(); recoverError != nil {
			logger.Error(fmt.Sprintf("[ExecuteSql]发生异常：%s", recoverError))
		}
		// 最终一定要确保将流水管理的所有测试账号数据恢复为'未使用'状态、修改操作流水
		finalAction(logger, ctx, db, envTableName, executeId, stateName, result)
	}()

	// 读取sql文件
	var sqlSlice []string
	sqlPath := path.Join(svcCtx.Config.FileStore.SqlPath, sqlFileName)
	f, _ := os.Open(sqlPath)
	bfRd := bufio.NewReader(f)
	for {
		sqlBytes, err := bfRd.ReadBytes('\n')
		if err == nil {
			sqlSlice = append(sqlSlice, string(sqlBytes))
		} else {
			if err == io.EOF {
				break
			} else {
				stateName = "EXCEPTION"
				result = fmt.Sprintf("读取sql文件 「%s」发生异常", sqlPath)
				logger.Error(fmt.Sprintf("读取sql文件 「%s」发生异常：%s", sqlPath, err))
				return
			}
		}
	}

	// 遍历执行sql
	poolEnvModel := model.NewPoolEnvTableModel(sqlConn, envTableName, fields)
	var sql string // 保存最后一次执行的sql
	err := poolEnvModel.Trans(ctx, func(context context.Context, session sqlx.Session) error {
		for _, sql = range sqlSlice {
			_, err := poolEnvModel.ExecSqlTX(ctx, session, sql)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		stateName = "EXCEPTION"
		result = fmt.Sprintf("执行sql文件「%s」发生异常", sqlPath)
		logger.Error(fmt.Sprintf("执行sql文件「%s」中的sql「%s」发生异常：%s", sqlPath, sql, err))
	}
}
