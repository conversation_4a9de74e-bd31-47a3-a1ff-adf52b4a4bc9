package utils

import (
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"path"
	"strings"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

type SaveExcelRequest struct {
	ExecuteId     string
	PoolId        int64
	EnvId         int64
	FileData      string
	ExcelFileName string
	Purpose       int64
}

func GetBackupExcelPath(excelDirPath, excelFileName string) (string, error) {
	// 获取或者生成备份的excel文件, 返回备份的excel路径
	slice := strings.Split(excelFileName, ".")
	backupExcelFileName := slice[0] + "_backup." + slice[1]
	excelPath := path.Join(excelDirPath, excelFileName)
	backupExcelFilePath := path.Join(excelDirPath, backupExcelFileName)
	srcFile, srcErr := os.Open(excelPath)
	if srcErr != nil {
		return "", srcErr
	}
	destFile, destErr := os.OpenFile(backupExcelFilePath, os.O_WRONLY|os.O_CREATE, os.ModePerm)
	if destErr != nil {
		return "", destErr
	}
	defer func() {
		if err := srcFile.Close(); err != nil {
			panic(err)
		}
		if err := destFile.Close(); err != nil {
			panic(err)
		}
	}()
	_, err := io.Copy(destFile, srcFile)
	if err != nil {
		return "", err
	}
	return backupExcelFilePath, nil
}

func SaveExcel(excelDirPath string, req *SaveExcelRequest) (string, string, error) {
	// https://segmentfault.com/a/1190000041576948
	// http://c.biancheng.net/view/5729.html
	// https://www.cnblogs.com/unqiang/p/6677208.html

	var excelFileName string
	var excelPath string

	fileData := req.FileData
	if fileData != "" { // 第1次上传
		poolId := req.PoolId
		envId := req.EnvId
		executeId := req.ExecuteId
		purpose := req.Purpose

		purposeStr := common.PoolEnvDataUploadPurposeName[purpose]
		excelFileName = fmt.Sprintf("%d_%d_%s_%s.xlsx", poolId, envId, executeId, purposeStr)
		excelPath = path.Join(excelDirPath, excelFileName)
		decodeBytes, err := base64.StdEncoding.DecodeString(fileData)
		if err != nil {
			return excelFileName, excelPath, errors.New(
				fmt.Sprintf(
					"excel文件生成的base64字符串decode发生错误：「%s」", err,
				),
			)
		}
		err = os.WriteFile(excelPath, decodeBytes, 0o600)
		if err != nil {
			return excelFileName, excelPath, errors.New(
				fmt.Sprintf(
					"excel文件生成的base64字符串写入xlsx文件发生错误：%s", err,
				),
			)
		}
	} else {
		excelFileName = req.ExcelFileName
		excelPath = path.Join(excelDirPath, excelFileName)
	}
	return excelFileName, excelPath, nil
}
