package utils

import (
	"fmt"
	"math"
	"regexp"
	"strconv"
	"strings"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

func CheckColumnId(columnId string) error {
	var err error
	retainColumnIds := []string{
		"create_time", "acquire_time", "return_time", "cooling_time", "occupy_state", "related_execute_id",
	}
	for _, id := range retainColumnIds {
		if columnId == id {
			err = fmt.Errorf("字段名%s是保留字段，请更换名称！", columnId)
			return err
		}
	}

	pattern := "(^_([a-zA-Z0-9]_?)*$)|(^[a-zA-Z](_?[a-zA-Z0-9])*_?$)"
	flag, err := regexp.MatchString(pattern, columnId)
	if !flag || err != nil {
		err = fmt.<PERSON><PERSON><PERSON>("字段名%s不符合规范，无法新增！", columnId)
		return err
	}

	if len(columnId) > 50 {
		err = fmt.<PERSON>rrorf("字段别名：%s长度不能超过50", columnId)
		return err
	}

	return err
}

func CheckColumnName(columnName string) error {
	var err error
	retainColumnNames := []string{
		"行数据创建时间", "行数据被获取时间", "行数据归还时间", "行数据占用状态", "关联的执行id",
	}
	for _, name := range retainColumnNames {
		if columnName == name {
			err = fmt.Errorf("字段名%s是保留字段，请更换名称！", columnName)
			return err
		}
	}

	if len(columnName) > 50 {
		err = fmt.Errorf("字段别名：%s长度不能超过50", columnName)
		return err
	}

	return err
}

func CheckColumnTypeAndLength(column *types.CreateAccountPoolColumnReq) error {
	var err error

	columnId := column.ColumnId
	columnType := column.ColumnType
	columnLength := column.ColumnLength

	columnTypeName := common.ColumnTypeName[columnType]
	columnTypeNameLower := strings.ToLower(columnTypeName)

	if common.ColumnType(columnType) == common.VARCHAR {
		if columnLength == 0 {
			err = fmt.Errorf("字段:`%s`类型为：%s，没有指定长度！", columnId, columnTypeNameLower)
			return err
		} else if columnLength > 255 {
			err = fmt.Errorf("字段:`%s`类型为：%s，长度不能超过255", columnId, columnTypeNameLower)
		}
	} else {
		if columnLength > 0 {
			err = fmt.Errorf("字段`%s`不能指定长度", columnId)
		}
	}
	return err
}

func CheckColumnDefault(column *types.CreateAccountPoolColumnReq) error {
	var err error

	columnId := column.ColumnId
	columnType := column.ColumnType
	columnTypeName := common.ColumnTypeName[columnType]
	columnTypeNameLower := strings.ToLower(columnTypeName)
	columnDefault := column.ColumnDefault

	if columnDefault == "" {
		return nil
	} else {
		columnLength := column.ColumnLength
		if columnLength != 0 {
			columnDefaultLength := int64(len(columnDefault))
			if columnDefaultLength > columnLength {
				err = fmt.Errorf("默认值长度为：%d, 超过了设定的字段长度:%d", columnDefaultLength, columnLength)
				return err
			}
		}
	}

	switch common.ColumnType(columnType) {
	case common.DATETIME:
		flag, _ := CheckColumnDml(column, columnDefault)
		if !flag {
			err = fmt.Errorf(
				"字段:`%s`类型为：%s，默认值应为严格的时间格式，形如'2021-01-01 01:01:01'（共19个数字）)", columnId,
				columnTypeNameLower,
			)
			return err
		}
	case common.TIMESTAMP:
		flag, _ := CheckColumnDml(column, columnDefault)
		if !flag {
			err = fmt.Errorf("字段:`%s`类型为：%s，默认值应为10位时间戳数字", columnId, columnTypeNameLower)
			return err
		}
	case common.INT:
		flag, _ := CheckColumnDml(column, columnDefault)
		if !flag {
			err = fmt.Errorf("字段:`%s`类型为：%s，默认值应该为整形数字", columnId, columnTypeNameLower)
			return err
		}
	case common.FLOAT:
		flag, _ := CheckColumnDml(column, columnDefault)
		if !flag {
			err = fmt.Errorf("字段:`%s`类型为：%s，默认值应该为浮点型数字", columnId, columnTypeNameLower)
			return err
		}
	case common.VARCHAR:
		if len(columnDefault) > 255 {
			err = fmt.Errorf("字段:`%s`类型为：%s，默认值不能超过最大长度255", columnId, columnTypeNameLower)
			return err
		}
	case common.TINYINT:
		if (columnDefault != "0") && (columnDefault != "1") {
			err = fmt.Errorf("字段:`%s`类型为：%s，默认值只能为0（表示false）或1（True）", columnId, columnTypeNameLower)
			return err
		}
	}

	return err
}

func CheckColumnAllowNull(column *types.CreateAccountPoolColumnReq) error {
	var err error
	columnId := column.ColumnId
	columnType := column.ColumnType
	columnAllowNull := column.ColumnAllowNull

	columnTypeEnum := common.ColumnType(columnType)
	if columnTypeEnum == common.DATETIME || columnTypeEnum == common.TIMESTAMP {
		if common.YesOrNo(columnAllowNull) == common.NO {
			err = fmt.Errorf("字段:`%s`表示时间，只能设置为允许为空", columnId)
		}
	}
	return err
}

func CheckColumnComment(column *types.CreateAccountPoolColumnReq) error {
	var err error
	columnId := column.ColumnId
	columnComment := column.ColumnComment
	if len(columnComment) > 255 {
		err = fmt.Errorf("字段:`%s`，注释不能超过最大长度255", columnId)
	}
	return err
}

func CheckColumnDml(column *types.CreateAccountPoolColumnReq, value string) (bool, string) {
	flag := true
	translateValue := strings.TrimSpace(value)
	retValue := translateValue

	columnType := column.ColumnType
	columnAllowNull := column.ColumnAllowNull

	if common.YesOrNo(columnAllowNull) == common.NO {
		if value == "" {
			flag = false
			retValue = fmt.Sprintf("%s字段值不能为空", column.ColumnId)
			return flag, retValue
		}
	} else {
		if value == "" {
			return flag, retValue
		}
	}

	switch common.ColumnType(columnType) {
	case common.VARCHAR:
		if int64(len(translateValue)) > column.ColumnLength {
			flag = false
			retValue = fmt.Sprintf("字段值%s超过设定的最大长度", value)
		}
	case common.INT:
		translateFloat, err1 := strconv.ParseFloat(translateValue, 64)
		translateInt, err2 := strconv.ParseInt(translateValue, 10, 64)
		if err1 != nil || err2 != nil {
			flag = false
			retValue = fmt.Sprintf("字段值：%s不是有效的整形数字", translateValue)
		} else {
			if int64(translateFloat) != translateInt {
				flag = false
				retValue = fmt.Sprintf("字段值：%s应该是整形数字", translateValue)
			} else if math.Abs(translateFloat) > 2147483647 {
				flag = false
				retValue = fmt.Sprintf("字段值：%s绝对值不能超过最大值2147483647", translateValue)
			}
		}
	case common.FLOAT:
		_, err := strconv.ParseFloat(translateValue, 64)
		if err != nil {
			flag = false
			retValue = fmt.Sprintf("字段值：%s不是有效的浮点型数字", translateValue)
		}
	case common.TINYINT:
		if translateValue != "0" && translateValue != "1" {
			flag = false
			retValue = fmt.Sprintf("字段值：%s不是有效的布尔型", translateValue)
		}
	case common.DATETIME:
		pattern := "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"
		matchFlag, err := regexp.MatchString(pattern, translateValue)
		if err != nil || !matchFlag {
			flag = false
			retValue = fmt.Sprintf(
				"字段值：%s不是一个严格的表示时间的字符串,确格式形如'2020-01-01 01:01:01', 一共有14个数字",
				translateValue,
			)
		} else {
			// 参考http://www.manongjc.com/detail/27-jnaohwndjhxklxi.html
			loc, _ := time.LoadLocation("Local") // 获取当地时区
			_, err := time.ParseInLocation("2006-01-02 15:04:05", translateValue, loc)
			if err != nil {
				flag = false
				retValue = fmt.Sprintf("字段值：%s不是一个有效的datetime类型", translateValue)
			}
		}
	case common.TIMESTAMP:
		pattern := "\\d{10}" // 秒级时间戳是10位数字
		matchFlag, err := regexp.MatchString(pattern, translateValue)
		if err != nil || !matchFlag {
			flag = false
			retValue = fmt.Sprintf("字段值：%s不是一个有效的（10位数字表示）时间戳", translateValue)
		} else {
			// 参考http://www.manongjc.com/detail/27-jnaohwndjhxklxi.html
			_, translateErr := strconv.ParseInt(translateValue, 10, 64)
			// time.Unix(translateIntValue, 0)
			if translateErr != nil {
				flag = false
				retValue = fmt.Sprintf("字段值：%s不是一个有效的（10位数字表示）时间戳", translateValue)
			}
		}
	}

	return flag, retValue
}

func CheckColumnDdl(column *types.CreateAccountPoolColumnReq) error {
	err1 := CheckColumnId(column.ColumnId)
	if err1 != nil {
		return err1
	}
	err2 := CheckColumnName(column.ColumnName)
	if err2 != nil {
		return err2
	}
	err3 := CheckColumnTypeAndLength(column)
	if err3 != nil {
		return err3
	}
	err4 := CheckColumnDefault(column)
	if err4 != nil {
		return err4
	}
	err5 := CheckColumnAllowNull(column)
	if err5 != nil {
		return err5
	}
	err6 := CheckColumnComment(column)
	if err6 != nil {
		return err6
	} else {
		return nil
	}
}
