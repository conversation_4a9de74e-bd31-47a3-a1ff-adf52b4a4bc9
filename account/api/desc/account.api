syntax = "v1"

info (
	title: "质量平台-账号池服务"
	desc: "质量平台中的账号池服务"
	author: "he<PERSON><PERSON><PERSON>"
	email: "heji<PERSON><PERSON>@52tt.com"
	version: "0.1.0"
)

import (
	"types.api"
)

@server (
	prefix: account/v1
	group: account
)

service account-api {
	// AccountPool相关的接口
	// 获取项目（产品）下拉框取
	@handler GetProductListHandler
	get /account_pool/product_list(GetProductListReq) returns (GetProductListResp)

    // 新增（产品）
    @handler CreateProductHandler
    post /account_pool/create_product(CreateProductReq) returns (CreateProductResp)

	// 账号池（模版）新增
	@handler CreateAccountPoolHandler
	post /account_pool/create(CreateAccountPoolReq) returns (CreateAccountPoolResp)

	// 账号池（模版）删除
	@handler DeleteAccountPoolHandler
	delete /account_pool/delete(DeleteAccountPoolReq) returns (DeleteAccountPoolResp)

	// 账号池（模版）修改
	@handler ModifyAccountPoolHandler
	put /account_pool/modify(ModifyAccountPoolReq) returns (ModifyAccountPoolResp)

	// 账号池（模版）列表
	@handler ListAccountPoolHandler
	get /account_pool/list(ListAccountPoolReq) returns (ListAccountPoolResp)

	// AccountPoolColumn相关的接口
	// 字段类型下拉框取值
	@handler GetAccountPoolColumnTypeHandler
	get /account_pool_column/column_type(GetAccountPoolColumnTypeReq) returns (GetAccountPoolColumnTypeResp)

	// 账号池字段新增
	@handler CreateAccountPoolColumnHandler
	post /account_pool_column/create(CreateAccountPoolColumnReq) returns (CreateAccountPoolColumnResp)

	// 账号池字段删除
	@handler DeleteAccountPoolColumnHandler
	delete /account_pool_column/delete(DeleteAccountPoolColumnReq) returns (DeleteAccountPoolColumnResp)

	// 账号池字段修改
	@handler ModifyAccountPoolColumnReqHandler
	put /account_pool_column/modify(ModifyAccountPoolColumnReq) returns (ModifyAccountPoolColumnResp)

	// 账号池字段列表(仅显示用户新增的可见的字段)
	@handler ListAccountPoolColumnHandler
	get /account_pool_column/list(ListAccountPoolColumnReq) returns (ListAccountPoolColumnResp)

	// 账号池字段列表(显示用户创建的所有字段)
	@handler ListAllAccountPoolColumnHandler
	get /account_pool_column/list_all(ListAllAccountPoolColumnReq) returns (ListAllAccountPoolColumnResp)

	// 获取产品账号池所有可用并且可见的字段
	@handler ListProductAccountPoolColumnHandler
	get /account_pool_column/list_product(ListProductAccountPoolColumnReq) returns (ListProductAccountPoolColumnResp)

	// 获取产品账号池所有可用的字段
	@handler ListProductAccountPoolAllColumnHandler
	get /account_pool_column/list_product_all(ListProductAccountPoolAllColumnReq) returns (ListProductAccountPoolAllColumnResp)

	// AccountPoolColumnRecycle相关的接口
	// 账号池回收字段列表
	@handler ListAccountPoolColumnRecycleHandler
	get /account_pool_column_recycle/list(ListAccountPoolColumnRecycleReq) returns (ListAccountPoolColumnRecycleResp)

	// 账号池回收字段真正删除
	@handler DeleteAccountPoolColumnRecycleHandler
	delete /account_pool_column_recycle/delete(DeleteAccountPoolColumnRecycleReq) returns (DeleteAccountPoolColumnRecycleResp)

	// 账号池字段恢复
	@handler RecoverAccountPoolColumnRecycleHandler
	put /account_pool_column_recycle/recover(RecoverAccountPoolColumnRecycleReq) returns (RecoverAccountPoolColumnRecycleResp)

	// AccountPoolEnv相关的接口
	// 账号池数据环境新增
	@handler CreateAccountPoolEnvHandler
	post /account_pool_env/create(CreateAccountPoolEnvReq) returns (CreateAccountPoolEnvResp)

	// 账号池数据环境删除
	@handler DeleteAccountPoolEnvHandler
	delete /account_pool_env/delete(DeleteAccountPoolEnvReq) returns (DeleteAccountPoolEnvResp)

	// 账号池数据环境修改
	@handler ModifyAccountPoolEnvHandler
	put /account_pool_env/modify(ModifyAccountPoolEnvReq) returns (ModifyAccountPoolEnvResp)

	// 账号池数据环境列表
	@handler ListAccountPoolEnvHandler
	get /account_pool_env/list(ListAccountPoolEnvReq) returns (ListAccountPoolEnvResp)

	// 获取产品所属（可用的）账号池数据环境列表
	@handler ListProductAccountPoolEnvHandler
	get /account_pool_env/list_product_account_pool_env(ListProductAccountPoolEnvReq) returns (ListProductAccountPoolEnvResp)

	// AccountPoolEnvData相关的接口
	// 账号池环境数据之文件上传并校验（异步）
	@handler UploadToCheckHandler
	post /account_pool_env_data/upload_to_check(UploadToCheckReq) returns (UploadToCheckResp)

	// 账号池环境数据之文件下载
	@handler DownloadHandler
	get /account_pool_env_data/download(DownloadReq) returns (DownloadResp)

	// 账号池环境数据新增（异步）
	@handler CreateAccountPoolEnvDataHandler
	post /account_pool_env_data/create(CreateAccountPoolEnvDataReq) returns (CreateAccountPoolEnvDataResp)

	// 账号池环境数据删除之筛选数据(异步)
	@handler QueryForDeleteAccountPoolEnvDataHandler
	post /account_pool_env_data/query_for_delete(QueryForDeleteAccountPoolEnvDataReq) returns (QueryForDeleteAccountPoolEnvDataResp)

	// 账号池环境数据删除（异步）
	@handler DeleteAccountPoolEnvDataHandler
	delete /account_pool_env_data/delete(DeleteAccountPoolEnvDataReq) returns (DeleteAccountPoolEnvDataResp)

	// 账号池环境数据修改之筛选数据（异步）
	@handler QueryForModifyAccountPoolEnvDataHandler
	post /account_pool_env_data/query_for_modify(QueryForModifyAccountPoolEnvDataReq) returns (QueryForModifyAccountPoolEnvDataResp)

	// 账号池环境数据修改（异步）
	@handler ModifyAccountPoolEnvDataHandler
	put /account_pool_env_data/modify(ModifyAccountPoolEnvDataReq) returns (ModifyAccountPoolEnvDataResp)

	// 取消账号池环境数据变更
	@handler CancelChangeHandler
	post /account_pool_env_data/cancel_change(CancelChangeReq) returns (CancelChangeResp)

	// 账号池环境数据操作流水列表
	@handler GetOperatorRecordListHandler
	get /account_pool_env_data/operate_record_list(GetOperatorRecordListReq) returns (GetOperatorRecordListResp)

	// 账号池环境数据变更重新检测（异步）
	@handler ReDetectHandler
	post /account_pool_env_data/re_detect(ReDetectReq) returns (ReDetectResp)

	// 获取一类产品各个账号池环境符合筛选条件的账户条数信息
	@handler GetAvailableCountInfoHandler
	post /account_pool_env_data/get_available_count_info(GetAvailableCountInfoReq) returns (GetAvailableCountInfoResp)

	// 筛选池账号
	@handler QueryAccountHandler
	post /account_pool_env_data/query_account(QueryAccountReq) returns (QueryAccountResp)

	// 释放池账号
	@handler ReleaseAccountHandler
	post /account_pool_env_data/release_account(ReleaseAccountReq) returns (ReleaseAccountResp)
}