syntax = "v1"

info (
    title: "质量平台账号池服务类型定义"
    desc: "质量平台账号池服务中http接口涉及的类型定义"
    author: "he<PERSON><PERSON><PERSON>"
    email: "<EMAIL>"
    version: "0.1.0"
)

// AccountPool相关的接口
// 获取项目（产品）下拉框取值
type (
	GetProductListReq {
	}
	GetProductListResp {
		ProductList     []*Product  `json:"product_list"`
	}
	Product {
	    ProductName     string      `json:"product_name"`
	    ProductType     int64       `json:"product_type"`
	    BelongProduct   string      `json:"belong_product"`
	}
    CreateProductReq {
        ProductName   string `json:"product_name" validate:"gte=1,lte=64" zh:"产品名称"`
	    ProductType   int64 `json:"product_type" validate:"gte=1,lte=254" zh:"产品类型"`
	    BelongProduct   string `json:"belong_product" validate:"gte=1,lte=64" zh:"所属产品"`
    }
    CreateProductResp {}
)

// 账户池（模版）新增
type (
	 CreateAccountPoolReq {
		PoolName        string     `json:"pool_name"`
		PoolType        int64      `json:"pool_type"`
		BelongProduct   string     `json:"belong_product"`
		CoolingTime     int64      `json:"cooling_time"`
		Description     string     `json:"description"`
	}
	CreateAccountPoolResp {
		PoolId          int64       `json:"pool_id"`
        ParentPoolId    int64       `json:"parent_pool_id"`
        PoolName        string      `json:"pool_name"`
        TableName       string      `json:"table_name"`
        PoolType        int64       `json:"pool_type"`
        BelongProduct   string      `json:"belong_product"`
        Description     string      `json:"description"`
        CreatedBy       UserInfo    `json:"created_by"`
        UpdatedBy       UserInfo    `json:"updated_by"`
        CreatedAt       int64       `json:"created_at"`
        UpdatedAt       int64       `json:"updated_at"`
	}
)

// 账户池（模版）删除
type (
    DeleteAccountPoolReq {
        PoolId          int64        `json:"pool_id"`
    }
    DeleteAccountPoolResp {
    }
)

// 账户池（模版）修改
type (
    ModifyAccountPoolReq {
        PoolId          int64         `json:"pool_id"`
        PoolName        string        `json:"pool_name"`
        Description     string        `json:"description"`
    }
    ModifyAccountPoolResp {
        PoolId          int64        `json:"pool_id"`
        ParentPoolId    int64        `json:"parent_pool_id"`
        PoolName        string       `json:"pool_name"`
        TableName       string       `json:"table_name"`
        PoolType        int64        `json:"pool_type"`
        BelongProduct   string       `json:"belong_product"`
        Description     string       `json:"description"`
        IsUsing         int64        `json:"is_using"`
        CreatedBy       UserInfo     `json:"created_by"`
        UpdatedBy       UserInfo     `json:"updated_by"`
        CreatedAt       int64        `json:"created_at"`
        UpdatedAt       int64        `json:"updated_at"`
    }
)

// 账户池（模版）列表
type (
    ListAccountPoolReq {
    }

    ListAccountPoolResp {
        AccountPoolList []*AccountPool `json:"account_pool_list"`
    }

    AccountPool {
        PoolId          int64        `json:"pool_id"`
        ParentPoolId    int64        `json:"parent_pool_id"`
        PoolName        string       `json:"pool_name"`
        TableName       string       `json:"table_name"`
        PoolType        int64        `json:"pool_type"`
        BelongProduct   string       `json:"belong_product"`
        Description     string       `json:"description"`
        IsUsing         int64        `json:"is_using"`
        CreatedBy       UserInfo     `json:"created_by"`
        UpdatedBy       UserInfo     `json:"updated_by"`
        CreatedAt       int64        `json:"created_at"`
        UpdatedAt       int64        `json:"updated_at"`
    }
)

// AccountPoolColumn相关的接口
// 字段类型下拉框取值
type (
	GetAccountPoolColumnTypeReq {
	}

	GetAccountPoolColumnTypeResp {
		ColumnType_list []*ColumnType `json:"column_type_list"`
	}

	ColumnType {
	    TypeId      int64        `json:"type_id"`
        TypeName    string       `json:"type_name"`
    }
)

// 账号池字段新增
type (
	CreateAccountPoolColumnReq {
    	PoolId          int64        `json:"pool_id"`
    	ColumnId        string       `json:"column_id"`
    	ColumnName      string       `json:"column_name"`
    	ColumnType      int64        `json:"column_type"`
    	ColumnLength    int64        `json:"column_length,omitempty,optional"`
    	ColumnDefault   string       `json:"column_default,omitempty,optional"`
    	ColumnAllowNull int64        `json:"column_allow_null"`
    	ColumnIsVisible int64        `json:"column_is_visible"`
    	ColumnComment   string       `json:"column_comment,omitempty,optional"`
    }

	CreateAccountPoolColumnResp {
		PoolId          int64        `json:"pool_id"`
        ColumnId        string       `json:"column_id"`
        ColumnName      string       `json:"column_name"`
        ColumnType      int64        `json:"column_type"`
        ColumnLength    int64        `json:"column_length"`
        ColumnDefault   string       `json:"column_default"`
        ColumnAllowNull int64        `json:"column_allow_null"`
        ColumnIsVisible int64        `json:"column_is_visible"`
        ColumnComment   string       `json:"column_comment"`
        PrimaryKey      int64        `json:"primary_key"`
        ColumnIsUsing   int64        `json:"column_is_using"`
        CreatedBy       UserInfo     `json:"created_by"`
        UpdatedBy       UserInfo     `json:"updated_by"`
        CreatedAt       int64        `json:"created_at"`
        UpdatedAt       int64        `json:"updated_at"`
	}
)

// 账号池字段删除
type (
    DeleteAccountPoolColumnReq {
        PoolId          int64        `json:"pool_id"`
        ColumnId        string       `json:"column_id"`
    }
    DeleteAccountPoolColumnResp {
    }
)

// 账号池字段修改
type (
	ModifyAccountPoolColumnReq {
	    PoolId          int64        `json:"pool_id"`
        ColumnId        string       `json:"column_id"`
        NewColumnId     string       `json:"new_column_id"`
        ColumnName      string       `json:"column_name"`
        ColumnType      int64        `json:"column_type"`
        ColumnLength    int64        `json:"column_length,omitempty,optional"`
        ColumnDefault   string       `json:"column_default,omitempty,optional"`
        ColumnAllowNull int64        `json:"column_allow_null"`
        ColumnIsVisible int64        `json:"column_is_visible"`
        ColumnComment   string       `json:"column_comment,omitempty,optional"`
	}
	ModifyAccountPoolColumnResp {
        PoolId          int64        `json:"pool_id"`
        ColumnId        string       `json:"column_id"`
        ColumnName      string       `json:"column_name"`
        ColumnType      int64        `json:"column_type"`
        ColumnLength    int64        `json:"column_length"`
        ColumnDefault   string       `json:"column_default"`
        ColumnAllowNull int64        `json:"column_allow_null"`
        ColumnIsVisible int64        `json:"column_is_visible"`
        ColumnComment   string       `json:"column_comment"`
        PrimaryKey      int64        `json:"primary_key"`
        ColumnIsUsing   int64        `json:"column_is_using"`
        CreatedBy       UserInfo     `json:"created_by"`
        UpdatedBy       UserInfo     `json:"updated_by"`
        CreatedAt       int64        `json:"created_at"`
        UpdatedAt       int64        `json:"updated_at"`
    }
)

// 账号池字段item
type (
    PoolColumnItem {
        PoolId          int64        `json:"pool_id"`
        ColumnId        string       `json:"column_id"`
        ColumnName      string       `json:"column_name"`
        ColumnType      int64        `json:"column_type"`
        ColumnLength    int64        `json:"column_length"`
        ColumnDefault   string       `json:"column_default"`
        ColumnAllowNull int64        `json:"column_allow_null"`
        ColumnIsVisible int64        `json:"column_is_visible"`
        ColumnComment   string       `json:"column_comment"`
        PrimaryKey      int64        `json:"primary_key"`
        ColumnIsUsing   int64        `json:"column_is_using"`
        CreatedBy       UserInfo     `json:"created_by"`
        UpdatedBy       UserInfo     `json:"updated_by"`
        CreatedAt       int64        `json:"created_at"`
        UpdatedAt       int64        `json:"updated_at"`
    }
)

// 账号池字段列表(仅显示用户新增的可见的字段)
type (
    ListAccountPoolColumnReq {
        PoolId          int64        `form:"pool_id"`
    }
    ListAccountPoolColumnResp {
        Items    []*PoolColumnItem   `json:"items"`
    }
)

// 账号池字段列表(显示用户创建的所有字)
type (
    ListAllAccountPoolColumnReq {
        PoolId          int64        `form:"pool_id"`
    }
    ListAllAccountPoolColumnResp {
        Items    []*PoolColumnItem   `json:"items"`
    }
)

// 获取产品账号池所有可用并且可见的字段
type (
    ListProductAccountPoolColumnReq {
        BelongProduct   string       `form:"belong_product"`
    }
    ListProductAccountPoolColumnResp {
        Items    []*PoolColumnItem   `json:"items"`
    }
)

// 获取产品账号池所有可用的字段
type (
    ListProductAccountPoolAllColumnReq {
        BelongProduct   string       `form:"belong_product"`
    }
    ListProductAccountPoolAllColumnResp {
        Items    []*PoolColumnItem   `json:"items"`
    }
)

// AccountPoolColumnRecycle相关的接口
// 账号池回收字段列表
type (
    ListAccountPoolColumnRecycleReq {
        PoolId          int64               `form:"pool_id"`
    }
    ListAccountPoolColumnRecycleResp {
        PoolColumnList []*PoolColumnItem    `json:"pool_column_list"`
    }
)

// 账号池回收字段真正删除
type (
    DeleteAccountPoolColumnRecycleReq {
        PoolId          int64        `json:"pool_id"`
        ColumnIdList    []*string    `json:"column_id_list"`
    }
    DeleteAccountPoolColumnRecycleResp {
    }
)

// 账号池字段恢复
type (
    RecoverAccountPoolColumnRecycleReq {
        PoolId          int64        `json:"pool_id"`
        ColumnIdList    []*string    `json:"column_id_list"`
    }
    RecoverAccountPoolColumnRecycleResp {
    }
)

// 账号池环境实体
type (
    PoolEnvItem {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        EnvName         string       `json:"env_name"`
        TotalCount      int64        `json:"total_count"`
        OccupyCount     int64        `json:"occupy_count"`
        AvailableCount  int64        `json:"available_count"`
        UpdatingCount   int64        `json:"updating_count"`
        DeletingCount   int64        `json:"deleting_count"`
        Description     string       `json:"description"`
        CreatedBy       UserInfo     `json:"created_by"`
        UpdatedBy       UserInfo     `json:"updated_by"`
        CreatedAt       int64        `json:"created_at"`
        UpdatedAt       int64        `json:"updated_at"`
    }
)

// AccountPoolEnv相关的接口
// 账号池数据环境新增
type (
    CreateAccountPoolEnvReq {
        PoolId          int64        `json:"pool_id"`
        EnvName         string       `json:"env_name"`
        CoolingTime     int64        `json:"cooling_time"`
        Description     string       `json:"description"`
    }
    CreateAccountPoolEnvResp {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        EnvName         string       `json:"env_name"`
        TotalCount      int64        `json:"total_count"`
        OccupyCount     int64        `json:"occupy_count"`
        AvailableCount  int64        `json:"available_count"`
        UpdatingCount   int64        `json:"updating_count"`
        DeletingCount   int64        `json:"deleting_count"`
        Description     string       `json:"description"`
        CreatedBy       UserInfo     `json:"created_by"`
        UpdatedBy       UserInfo     `json:"updated_by"`
        CreatedAt       int64        `json:"created_at"`
        UpdatedAt       int64        `json:"updated_at"`
    }
)

// 账号池数据环境删除
type (
    DeleteAccountPoolEnvReq {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
    }
    DeleteAccountPoolEnvResp {
    }
)

// 账号池数据环境修改
type (
    ModifyAccountPoolEnvReq {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        EnvName         string       `json:"env_name"`
        Description     string       `json:"description"`
    }
    ModifyAccountPoolEnvResp {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        EnvName         string       `json:"env_name"`
        TotalCount      int64        `json:"total_count"`
        OccupyCount     int64        `json:"occupy_count"`
        AvailableCount  int64        `json:"available_count"`
        UpdatingCount   int64        `json:"updating_count"`
        DeletingCount   int64        `json:"deleting_count"`
        Description     string       `json:"description"`
        CreatedBy       UserInfo     `json:"created_by"`
        UpdatedBy       UserInfo     `json:"updated_by"`
        CreatedAt       int64        `json:"created_at"`
        UpdatedAt       int64        `json:"updated_at"`
    }
)

// 账号池数据环境列表
type (
    ListAccountPoolEnvReq {
        PoolId          int64        `form:"pool_id"`
    }
    ListAccountPoolEnvResp {
        EnvList        []*EnvItem    `json:"env_list"`
    }
    EnvItem {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        EnvName         string       `json:"env_name"`
        TotalCount      int64        `json:"total_count"`
        OccupyCount     int64        `json:"occupy_count"`
        AvailableCount  int64        `json:"available_count"`
        UpdatingCount   int64        `json:"updating_count"`
        DeletingCount   int64        `json:"deleting_count"`
        Description     string       `json:"description"`
        CreatedBy       UserInfo     `json:"created_by"`
        UpdatedBy       UserInfo     `json:"updated_by"`
        CreatedAt       int64        `json:"created_at"`
        UpdatedAt       int64        `json:"updated_at"`
    }
)

// 获取产品所属（可用的）账号池数据环境列表
type (
    ListProductAccountPoolEnvReq {
        BelongProduct   string        `form:"belong_product"`
    }
    ListProductAccountPoolEnvResp {
        Items   []*ProductAccountPoolEnv    `json:"items"`
    }
    ProductAccountPoolEnv {
        Key             string        `json:"key"`
        Value           string        `json:"value"`
    }
)

// AccountPoolEnvData相关的接口
// 条件组
type GroupCondition {
    Relationship string              `json:"relationship"`
    Conditions   []interface{}       `json:"conditions"`
}

// 账号池环境数据之文件上传并校验（异步）
type (
    UploadToCheckReq {
        ExecuteId       string       `json:"execute_id,omitempty,optional"`
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        FileData        string       `json:"file_data,omitempty,optional"`
        ExcelFileName   string       `json:"excel_file_name,omitempty,optional"`
        Purpose         int64        `json:"purpose"`
    }
    UploadToCheckResp {
        ExecuteId       string       `json:"execute_id"`
    }
)

// 账号池环境数据之文件下载
type (
    DownloadReq {
        FileName        string       `form:"file_name"`
        ExecuteId       string       `form:"execute_id,omitempty,optional"`
    }
    DownloadResp {
        FileName        string       `json:"file_name"`
        FileContent     string       `json:"file_content"`
    }
)

// 账号池环境数据新增（异步）
type (
    CreateAccountPoolEnvDataReq {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        ExecuteId       string       `json:"execute_id"`
        Description     string       `json:"description"`
    }
    CreateAccountPoolEnvDataResp {
        ExecuteId       string       `json:"execute_id"`
    }
)

// 账号池环境数据删除之筛选数据(异步)
type (
    QueryForDeleteAccountPoolEnvDataReq {
        ExecuteId  		string		  	`json:"execute_id,omitempty,optional"`
        PoolId     		int64          	`json:"pool_id"`
        EnvId      		int64          	`json:"env_id"`
        QuerySql   		string		  	`json:"query_sql,omitempty,optional"`
        Condition  		*api.Condition 	`json:"condition"`
        ExpectedCount 	int64          	`json:"expected_count"`
    }
    QueryForDeleteAccountPoolEnvDataResp {
        ExecuteId 		string          `json:"execute_id"`
    }
)

// 账号池环境数据删除（异步）
type (
    DeleteAccountPoolEnvDataReq {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        ExecuteId       string       `json:"execute_id"`
        Description     string       `json:"description"`
    }
    DeleteAccountPoolEnvDataResp {
        ExecuteId       string       `json:"execute_id"`
    }
)

// 账号池环境数据修改之筛选数据（异步)
type (
    QueryForModifyAccountPoolEnvDataReq {
        ExecuteId			        string			    `json:"execute_id,omitempty,optional"`
        PoolId                      int64               `json:"pool_id"`
        EnvId                       int64               `json:"env_id"`
        QuerySql                    string			    `json:"query_sql,omitempty,optional"`
        Condition                   *api.Condition      `json:"condition"`
        SelectedColumnIdList        []string            `json:"selected_column_id_list"`
        ExpectedCount               int64               `json:"expected_count"`
    }
    QueryForModifyAccountPoolEnvDataResp {
        ExecuteId                   string              `json:"execute_id"`
    }
)

// 账号池环境数据修改（异步）
type (
    ModifyAccountPoolEnvDataReq {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        ExecuteId       string       `json:"execute_id"`
        Description     string       `json:"description"`
    }
    ModifyAccountPoolEnvDataResp {
        ExecuteId       string       `json:"execute_id"`
    }
)

// 取消账号池环境数据变更
type (
    CancelChangeReq {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        ExecuteId       string       `json:"execute_id"`
        Description     string       `json:"description"`
    }
    CancelChangeResp {
        ExecuteId       string       `json:"execute_id"`
    }
)

// 账号池环境数据操作流水列表
type (
    // 分页
    Pagination {
        CurrentPage uint64 `json:"current_page"`
        PageSize    uint64 `json:"page_size"`
    }
    GetOperatorRecordListReq {
        PoolId          int64        `json:"pool_id"`
        EnvId           int64        `json:"env_id"`
        Pagination      Pagination   `json:"pagination,omitempty,optional"`
    }
    GetOperatorRecordListResp {
        EnvName         string       `json:"env_name"`
        EnvRecordCount  int64        `json:"env_record_count"`
        EnvId           int64        `json:"env_id"`
        RecordList      []*Record    `json:"record_list"`
    }
    Record {
         ExecuteId       string       `json:"execute_id"`
         PoolId          int64        `json:"pool_id"`
         EnvId           int64        `json:"env_id"`
         ExecuteWay      int64        `json:"execute_way"`
         Description     string       `json:"description"`
         State           int64        `json:"state"`
         Result          string       `json:"result"`
         CurrentJson     string       `json:"current_json"`
         CreatedBy       UserInfo     `json:"created_by"`
         UpdatedBy       UserInfo     `json:"updated_by"`
         CreatedAt       int64        `json:"created_at"`
         UpdatedAt       int64        `json:"updated_at"`
    }
)

// 账号池环境数据变更重新检测（异步）
type (
    ReDetectReq {
        ExecuteId       string       `json:"execute_id"`
    }
    ReDetectResp {
        ExecuteId       string       `json:"execute_id"`
    }
)

// 获取一类产品各个账号池环境符合筛选条件的账户条数信息
type (
    GetAvailableCountInfoReq {
        BelongProduct   string     `json:"belong_product"`
        Condition       *GroupCondition `json:"condition"`
    }
    GetAvailableCountInfoResp {
        AvailableCountInfo   []*PoolAvailableCountInfo     `json:"available_count_info"`
    }
    PoolAvailableCountInfo {
        PoolEnvName     string     `json:"pool_env_name"`
        AvailableCount  string     `json:"available_count"`
    }
)


// 筛选池账号
type (
    QueryAccountReq {
        PoolEnvTable               string     `json:"pool_env_table" validate:"required" zh:"池账号表名"`
        Condition                  *Condition `json:"condition,omitempty,optional" zh:"查询条件"`
        SelectedColumnIdArray      []string   `json:"selected_column_id_array,omitempty,optional" zh:"筛选的字段列表，不传递则表示只筛选account和password字段"`
        ExpectedCount              int64      `json:"expected_count,default=1" validate:"gte=1,lte=1000" zh:"预期筛选数量，正整数，默认为1"`
        AllowLessThanExpectedCount int64      `json:"allow_less_than_expected_count,default=2" validate:"oneof=1 2" zh:"是否允许查询数据条数小于筛选数量，1为允许，2为不允许，默认为不允许"`
    }
    QueryAccountResp {
        ExpectedCount int64      `json:"expected_count"`
        MatchCount    int64      `json:"match_count"`
        MatchData     []*Account `json:"match_data"`
    }
    Account {
        Field      string `json:"field"`
        Value      string `json:"value"`
        LockValue  string `json:"lock_value"`
        ColumnType string `json:"columnType"`
    }
)

// 释放池账号
type (
    ReleaseAccountReq {
        ReleaseTasAccountArray []*ReleaseTasAccount `json:"release_tas_account_array" validate:"required,lte=10,dive,required" zh:"释放的池账号"`
    }
    ReleaseTasAccount {
        PoolEnvTable string            `json:"pool_env_table" validate:"required" zh:"池账号表名"`
        AccountArray []*ReleaseAccount `json:"account_array" validate:"required,lte=1000,dive,required" zh:"池账号account字段信息列表"`
    }
    ReleaseAccount {
        Account   string `json:"account" validate:"required"`
        LockValue string `json:"lock_value"`
    }

    ReleaseAccountResp {}
)
