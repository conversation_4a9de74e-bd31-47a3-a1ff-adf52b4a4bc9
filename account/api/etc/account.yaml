Name: api.account
Host: 0.0.0.0
Port: 20101
Verbose: true
MaxBytes: *********
Timeout: 0

Log:
  ServiceName: api.account
  Encoding: plain
  Level: info
  Path: /app/logs/account

Prometheus:
  Host: 0.0.0.0
  Port: 20121
  Path: /metrics

Telemetry:
  Name: api.permission
  Endpoint: http://127.0.0.1:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

DevServer:
  Enabled: true
  Port: 20131

DB:
  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/account?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
#  DataSource: probe:Quwan@2020_TTinternation@tcp(************:3306)/account?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai


Redis:
  Host: 127.0.0.1:6379
  Type: node
  Pass: Quwan@2020
  DB: 6

Cache:
  - Host: 127.0.0.1:6379
    Type: node
    Pass: Quwan@2020
    DB: 6

FileStore:
  excelPath: ${EXCEL_PATH}
  sqlPath: ${SQL_PATH}

UserRpc:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.user
  Endpoints:
    - 127.0.0.1:10111
  NonBlock: true
  Timeout: 0

AccountRpc:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.account
  Endpoints:
    - 127.0.0.1:20111
  NonBlock: true
  Timeout: 0
