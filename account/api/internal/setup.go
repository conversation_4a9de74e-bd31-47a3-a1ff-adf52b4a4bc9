package internal

import "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/middlewares"

var skipUrls = []string{
	"/account/v1/account_pool_env/list_product_account_pool_env",
	"/account/v1/account_pool_env_data/get_available_count_info",
	"/account/v1/account_pool_env_data/query_account",
	"/account/v1/account_pool_env_data/release_account",
}

func RegisterSkipUrls() {
	for _, url := range skipUrls {
		middlewares.SkipForUrl(url)
	}
}
