// Code generated by goctl. DO NOT EDIT.
package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type GetProductListReq struct {
}

type GetProductListResp struct {
	ProductList []*Product `json:"product_list"`
}

type Product struct {
	ProductName   string `json:"product_name"`
	ProductType   int64  `json:"product_type"`
	BelongProduct string `json:"belong_product"`
}

type CreateProductReq struct {
	ProductName   string `json:"product_name" validate:"gte=1,lte=64" zh:"产品名称"`
	ProductType   int64  `json:"product_type" validate:"gte=1,lte=254" zh:"产品类型"`
	BelongProduct string `json:"belong_product" validate:"gte=1,lte=64" zh:"所属产品"`
}

type CreateProductResp struct {
}

type CreateAccountPoolReq struct {
	PoolName      string `json:"pool_name"`
	PoolType      int64  `json:"pool_type"`
	BelongProduct string `json:"belong_product"`
	CoolingTime   int64  `json:"cooling_time"`
	Description   string `json:"description"`
}

type CreateAccountPoolResp struct {
	PoolId        int64              `json:"pool_id"`
	ParentPoolId  int64              `json:"parent_pool_id"`
	PoolName      string             `json:"pool_name"`
	TableName     string             `json:"table_name"`
	PoolType      int64              `json:"pool_type"`
	BelongProduct string             `json:"belong_product"`
	Description   string             `json:"description"`
	CreatedBy     *userinfo.UserInfo `json:"created_by"`
	UpdatedBy     *userinfo.UserInfo `json:"updated_by"`
	CreatedAt     int64              `json:"created_at"`
	UpdatedAt     int64              `json:"updated_at"`
}

type DeleteAccountPoolReq struct {
	PoolId int64 `json:"pool_id"`
}

type DeleteAccountPoolResp struct {
}

type ModifyAccountPoolReq struct {
	PoolId      int64  `json:"pool_id"`
	PoolName    string `json:"pool_name"`
	Description string `json:"description"`
}

type ModifyAccountPoolResp struct {
	PoolId        int64              `json:"pool_id"`
	ParentPoolId  int64              `json:"parent_pool_id"`
	PoolName      string             `json:"pool_name"`
	TableName     string             `json:"table_name"`
	PoolType      int64              `json:"pool_type"`
	BelongProduct string             `json:"belong_product"`
	Description   string             `json:"description"`
	IsUsing       int64              `json:"is_using"`
	CreatedBy     *userinfo.UserInfo `json:"created_by"`
	UpdatedBy     *userinfo.UserInfo `json:"updated_by"`
	CreatedAt     int64              `json:"created_at"`
	UpdatedAt     int64              `json:"updated_at"`
}

type ListAccountPoolReq struct {
}

type ListAccountPoolResp struct {
	AccountPoolList []*AccountPool `json:"account_pool_list"`
}

type AccountPool struct {
	PoolId        int64              `json:"pool_id"`
	ParentPoolId  int64              `json:"parent_pool_id"`
	PoolName      string             `json:"pool_name"`
	TableName     string             `json:"table_name"`
	PoolType      int64              `json:"pool_type"`
	BelongProduct string             `json:"belong_product"`
	Description   string             `json:"description"`
	IsUsing       int64              `json:"is_using"`
	CreatedBy     *userinfo.UserInfo `json:"created_by"`
	UpdatedBy     *userinfo.UserInfo `json:"updated_by"`
	CreatedAt     int64              `json:"created_at"`
	UpdatedAt     int64              `json:"updated_at"`
}

type GetAccountPoolColumnTypeReq struct {
}

type GetAccountPoolColumnTypeResp struct {
	ColumnType_list []*ColumnType `json:"column_type_list"`
}

type ColumnType struct {
	TypeId   int64  `json:"type_id"`
	TypeName string `json:"type_name"`
}

type CreateAccountPoolColumnReq struct {
	PoolId          int64  `json:"pool_id"`
	ColumnId        string `json:"column_id"`
	ColumnName      string `json:"column_name"`
	ColumnType      int64  `json:"column_type"`
	ColumnLength    int64  `json:"column_length,omitempty,optional"`
	ColumnDefault   string `json:"column_default,omitempty,optional"`
	ColumnAllowNull int64  `json:"column_allow_null"`
	ColumnIsVisible int64  `json:"column_is_visible"`
	ColumnComment   string `json:"column_comment,omitempty,optional"`
}

type CreateAccountPoolColumnResp struct {
	PoolId          int64              `json:"pool_id"`
	ColumnId        string             `json:"column_id"`
	ColumnName      string             `json:"column_name"`
	ColumnType      int64              `json:"column_type"`
	ColumnLength    int64              `json:"column_length"`
	ColumnDefault   string             `json:"column_default"`
	ColumnAllowNull int64              `json:"column_allow_null"`
	ColumnIsVisible int64              `json:"column_is_visible"`
	ColumnComment   string             `json:"column_comment"`
	PrimaryKey      int64              `json:"primary_key"`
	ColumnIsUsing   int64              `json:"column_is_using"`
	CreatedBy       *userinfo.UserInfo `json:"created_by"`
	UpdatedBy       *userinfo.UserInfo `json:"updated_by"`
	CreatedAt       int64              `json:"created_at"`
	UpdatedAt       int64              `json:"updated_at"`
}

type DeleteAccountPoolColumnReq struct {
	PoolId   int64  `json:"pool_id"`
	ColumnId string `json:"column_id"`
}

type DeleteAccountPoolColumnResp struct {
}

type ModifyAccountPoolColumnReq struct {
	PoolId          int64  `json:"pool_id"`
	ColumnId        string `json:"column_id"`
	NewColumnId     string `json:"new_column_id"`
	ColumnName      string `json:"column_name"`
	ColumnType      int64  `json:"column_type"`
	ColumnLength    int64  `json:"column_length,omitempty,optional"`
	ColumnDefault   string `json:"column_default,omitempty,optional"`
	ColumnAllowNull int64  `json:"column_allow_null"`
	ColumnIsVisible int64  `json:"column_is_visible"`
	ColumnComment   string `json:"column_comment,omitempty,optional"`
}

type ModifyAccountPoolColumnResp struct {
	PoolId          int64              `json:"pool_id"`
	ColumnId        string             `json:"column_id"`
	ColumnName      string             `json:"column_name"`
	ColumnType      int64              `json:"column_type"`
	ColumnLength    int64              `json:"column_length"`
	ColumnDefault   string             `json:"column_default"`
	ColumnAllowNull int64              `json:"column_allow_null"`
	ColumnIsVisible int64              `json:"column_is_visible"`
	ColumnComment   string             `json:"column_comment"`
	PrimaryKey      int64              `json:"primary_key"`
	ColumnIsUsing   int64              `json:"column_is_using"`
	CreatedBy       *userinfo.UserInfo `json:"created_by"`
	UpdatedBy       *userinfo.UserInfo `json:"updated_by"`
	CreatedAt       int64              `json:"created_at"`
	UpdatedAt       int64              `json:"updated_at"`
}

type PoolColumnItem struct {
	PoolId          int64              `json:"pool_id"`
	ColumnId        string             `json:"column_id"`
	ColumnName      string             `json:"column_name"`
	ColumnType      int64              `json:"column_type"`
	ColumnLength    int64              `json:"column_length"`
	ColumnDefault   string             `json:"column_default"`
	ColumnAllowNull int64              `json:"column_allow_null"`
	ColumnIsVisible int64              `json:"column_is_visible"`
	ColumnComment   string             `json:"column_comment"`
	PrimaryKey      int64              `json:"primary_key"`
	ColumnIsUsing   int64              `json:"column_is_using"`
	CreatedBy       *userinfo.UserInfo `json:"created_by"`
	UpdatedBy       *userinfo.UserInfo `json:"updated_by"`
	CreatedAt       int64              `json:"created_at"`
	UpdatedAt       int64              `json:"updated_at"`
}

type ListAccountPoolColumnReq struct {
	PoolId int64 `form:"pool_id"`
}

type ListAccountPoolColumnResp struct {
	Items []*PoolColumnItem `json:"items"`
}

type ListAllAccountPoolColumnReq struct {
	PoolId int64 `form:"pool_id"`
}

type ListAllAccountPoolColumnResp struct {
	Items []*PoolColumnItem `json:"items"`
}

type ListProductAccountPoolColumnReq struct {
	BelongProduct string `form:"belong_product"`
}

type ListProductAccountPoolColumnResp struct {
	Items []*PoolColumnItem `json:"items"`
}

type ListProductAccountPoolAllColumnReq struct {
	BelongProduct string `form:"belong_product"`
}

type ListProductAccountPoolAllColumnResp struct {
	Items []*PoolColumnItem `json:"items"`
}

type ListAccountPoolColumnRecycleReq struct {
	PoolId int64 `form:"pool_id"`
}

type ListAccountPoolColumnRecycleResp struct {
	PoolColumnList []*PoolColumnItem `json:"pool_column_list"`
}

type DeleteAccountPoolColumnRecycleReq struct {
	PoolId       int64     `json:"pool_id"`
	ColumnIdList []*string `json:"column_id_list"`
}

type DeleteAccountPoolColumnRecycleResp struct {
}

type RecoverAccountPoolColumnRecycleReq struct {
	PoolId       int64     `json:"pool_id"`
	ColumnIdList []*string `json:"column_id_list"`
}

type RecoverAccountPoolColumnRecycleResp struct {
}

type PoolEnvItem struct {
	PoolId         int64              `json:"pool_id"`
	EnvId          int64              `json:"env_id"`
	EnvName        string             `json:"env_name"`
	TotalCount     int64              `json:"total_count"`
	OccupyCount    int64              `json:"occupy_count"`
	AvailableCount int64              `json:"available_count"`
	UpdatingCount  int64              `json:"updating_count"`
	DeletingCount  int64              `json:"deleting_count"`
	Description    string             `json:"description"`
	CreatedBy      *userinfo.UserInfo `json:"created_by"`
	UpdatedBy      *userinfo.UserInfo `json:"updated_by"`
	CreatedAt      int64              `json:"created_at"`
	UpdatedAt      int64              `json:"updated_at"`
}

type CreateAccountPoolEnvReq struct {
	PoolId      int64  `json:"pool_id"`
	EnvName     string `json:"env_name"`
	CoolingTime int64  `json:"cooling_time"`
	Description string `json:"description"`
}

type CreateAccountPoolEnvResp struct {
	PoolId         int64              `json:"pool_id"`
	EnvId          int64              `json:"env_id"`
	EnvName        string             `json:"env_name"`
	CoolingTime    int64              `json:"cooling_time"`
	TotalCount     int64              `json:"total_count"`
	OccupyCount    int64              `json:"occupy_count"`
	AvailableCount int64              `json:"available_count"`
	UpdatingCount  int64              `json:"updating_count"`
	DeletingCount  int64              `json:"deleting_count"`
	Description    string             `json:"description"`
	CreatedBy      *userinfo.UserInfo `json:"created_by"`
	UpdatedBy      *userinfo.UserInfo `json:"updated_by"`
	CreatedAt      int64              `json:"created_at"`
	UpdatedAt      int64              `json:"updated_at"`
}

type DeleteAccountPoolEnvReq struct {
	PoolId int64 `json:"pool_id"`
	EnvId  int64 `json:"env_id"`
}

type DeleteAccountPoolEnvResp struct {
}

type ModifyAccountPoolEnvReq struct {
	PoolId      int64  `json:"pool_id"`
	EnvId       int64  `json:"env_id"`
	EnvName     string `json:"env_name"`
	CoolingTime int64  `json:"cooling_time"`
	Description string `json:"description"`
}

type ModifyAccountPoolEnvResp struct {
	PoolId         int64              `json:"pool_id"`
	EnvId          int64              `json:"env_id"`
	EnvName        string             `json:"env_name"`
	CoolingTime    int64              `json:"cooling_time"`
	TotalCount     int64              `json:"total_count"`
	OccupyCount    int64              `json:"occupy_count"`
	AvailableCount int64              `json:"available_count"`
	UpdatingCount  int64              `json:"updating_count"`
	DeletingCount  int64              `json:"deleting_count"`
	Description    string             `json:"description"`
	CreatedBy      *userinfo.UserInfo `json:"created_by"`
	UpdatedBy      *userinfo.UserInfo `json:"updated_by"`
	CreatedAt      int64              `json:"created_at"`
	UpdatedAt      int64              `json:"updated_at"`
}

type ListAccountPoolEnvReq struct {
	PoolId int64 `form:"pool_id"`
}

type ListAccountPoolEnvResp struct {
	EnvList []*EnvItem `json:"env_list"`
}

type EnvItem struct {
	PoolId         int64              `json:"pool_id"`
	EnvId          int64              `json:"env_id"`
	EnvName        string             `json:"env_name"`
	TotalCount     int64              `json:"total_count"`
	OccupyCount    int64              `json:"occupy_count"`
	AvailableCount int64              `json:"available_count"`
	UpdatingCount  int64              `json:"updating_count"`
	DeletingCount  int64              `json:"deleting_count"`
	Description    string             `json:"description"`
	CreatedBy      *userinfo.UserInfo `json:"created_by"`
	UpdatedBy      *userinfo.UserInfo `json:"updated_by"`
	CreatedAt      int64              `json:"created_at"`
	UpdatedAt      int64              `json:"updated_at"`
}

type ListProductAccountPoolEnvReq struct {
	BelongProduct string `form:"belong_product"`
}

type ListProductAccountPoolEnvResp struct {
	Items []*ProductAccountPoolEnv `json:"items"`
}

type ProductAccountPoolEnv struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type UploadToCheckReq struct {
	ExecuteId     string `json:"execute_id,omitempty,optional"`
	PoolId        int64  `json:"pool_id"`
	EnvId         int64  `json:"env_id"`
	FileData      string `json:"file_data,omitempty,optional"`
	ExcelFileName string `json:"excel_file_name,omitempty,optional"`
	Purpose       int64  `json:"purpose"`
}

type UploadToCheckResp struct {
	ExecuteId string `json:"execute_id"`
}

type DownloadReq struct {
	FileName  string `form:"file_name"`
	ExecuteId string `form:"execute_id,omitempty,optional"`
}

type DownloadResp struct {
	FileName    string `json:"file_name"`
	FileContent string `json:"file_content"`
}

type CreateAccountPoolEnvDataReq struct {
	PoolId      int64  `json:"pool_id"`
	EnvId       int64  `json:"env_id"`
	ExecuteId   string `json:"execute_id"`
	Description string `json:"description"`
}

type CreateAccountPoolEnvDataResp struct {
	ExecuteId string `json:"execute_id"`
}

type QueryForDeleteAccountPoolEnvDataReq struct {
	ExecuteId     string         `json:"execute_id,omitempty,optional"`
	PoolId        int64          `json:"pool_id"`
	EnvId         int64          `json:"env_id"`
	QuerySql      string         `json:"query_sql,omitempty,optional"`
	Condition     *api.Condition `json:"condition"`
	ExpectedCount int64          `json:"expected_count"`
}

type QueryForDeleteAccountPoolEnvDataResp struct {
	ExecuteId string `json:"execute_id"`
}

type DeleteAccountPoolEnvDataReq struct {
	PoolId      int64  `json:"pool_id"`
	EnvId       int64  `json:"env_id"`
	ExecuteId   string `json:"execute_id"`
	Description string `json:"description"`
}

type DeleteAccountPoolEnvDataResp struct {
	ExecuteId string `json:"execute_id"`
}

type QueryForModifyAccountPoolEnvDataReq struct {
	ExecuteId            string         `json:"execute_id,omitempty,optional"`
	PoolId               int64          `json:"pool_id"`
	EnvId                int64          `json:"env_id"`
	QuerySql             string         `json:"query_sql,omitempty,optional"`
	Condition            *api.Condition `json:"condition"`
	SelectedColumnIdList []string       `json:"selected_column_id_list"`
	ExpectedCount        int64          `json:"expected_count"`
}

type QueryForModifyAccountPoolEnvDataResp struct {
	ExecuteId string `json:"execute_id"`
}

type ModifyAccountPoolEnvDataReq struct {
	PoolId      int64  `json:"pool_id"`
	EnvId       int64  `json:"env_id"`
	ExecuteId   string `json:"execute_id"`
	Description string `json:"description"`
}

type ModifyAccountPoolEnvDataResp struct {
	ExecuteId string `json:"execute_id"`
}

type CancelChangeReq struct {
	PoolId      int64  `json:"pool_id"`
	EnvId       int64  `json:"env_id"`
	ExecuteId   string `json:"execute_id"`
	Description string `json:"description"`
}

type CancelChangeResp struct {
	ExecuteId string `json:"execute_id"`
}

type GetOperatorRecordListReq struct {
	PoolId     int64           `json:"pool_id"`
	EnvId      int64           `json:"env_id"`
	Pagination *api.Pagination `json:"pagination"`
}

type GetOperatorRecordListResp struct {
	EnvName        string    `json:"env_name"`
	EnvRecordCount int64     `json:"env_record_count"`
	EnvId          int64     `json:"env_id"`
	RecordList     []*Record `json:"record_list"`
}

type Record struct {
	ExecuteId   string             `json:"execute_id"`
	PoolId      int64              `json:"pool_id"`
	EnvId       int64              `json:"env_id"`
	ExecuteWay  int64              `json:"execute_way"`
	Description string             `json:"description"`
	State       int64              `json:"state"`
	Result      string             `json:"result"`
	CurrentJson string             `json:"current_json"`
	CreatedBy   *userinfo.UserInfo `json:"created_by"`
	UpdatedBy   *userinfo.UserInfo `json:"updated_by"`
	CreatedAt   int64              `json:"created_at"`
	UpdatedAt   int64              `json:"updated_at"`
}

type ReDetectReq struct {
	ExecuteId string `json:"execute_id"`
}

type ReDetectResp struct {
	ExecuteId string `json:"execute_id"`
}

type GetAvailableCountInfoReq struct {
	BelongProduct string         `json:"belong_product"`
	Condition     *api.Condition `json:"condition"`
}

type GetAvailableCountInfoResp struct {
	AvailableCountInfo []*PoolAvailableCountInfo `json:"available_count_info"`
}

type PoolAvailableCountInfo struct {
	PoolEnvName    string `json:"pool_env_name"`
	AvailableCount string `json:"available_count"`
}

type QueryAccountReq struct {
	PoolEnvTable               string         `json:"pool_env_table" validate:"required" zh:"池账号表名"`
	Condition                  *api.Condition `json:"condition" zh:"查询条件"`
	SelectedColumnIdArray      []string       `json:"selected_column_id_array,omitempty,optional" zh:"筛选的字段列表，不传递则表示只筛选account和password字段"`
	ExpectedCount              int64          `json:"expected_count,default=1" validate:"gte=1,lte=1000" zh:"预期筛选数量，正整数，默认为1"`
	AllowLessThanExpectedCount int64          `json:"allow_less_than_expected_count,default=2" validate:"oneof=1 2" zh:"是否允许查询数据条数小于筛选数量，1为允许，2为不允许，默认为不允许"`
}

type QueryAccountResp struct {
	ExpectedCount int64          `json:"expected_count"`
	MatchCount    int64          `json:"match_count"`
	MatchData     []*AccountData `json:"match_data"`
}

type AccountData struct {
	Account []*Column `json:"account"`
}

type Column struct {
	Field      string `json:"field"`
	Value      string `json:"value"`
	LockValue  string `json:"lock_value"`
	ColumnType int8   `json:"columnType"`
}

type ReleaseAccountReq struct {
	ReleaseTasAccountArray []*ReleaseTasAccount `json:"release_tas_account_array" validate:"required,lte=10,dive,required" zh:"释放的池账号"`
}

type ReleaseTasAccount struct {
	PoolEnvTable string            `json:"pool_env_table" validate:"required" zh:"池账号表名"`
	AccountArray []*ReleaseAccount `json:"account_array" validate:"required,lte=1000,dive,required" zh:"池账号account字段信息列表"`
}

type ReleaseAccount struct {
	Account   string `json:"account" validate:"required"`
	LockValue string `json:"lock_value"`
}

type ReleaseAccountResp struct{}
