package user

import (
	"context"

	"github.com/jinzhu/copier"
	"google.golang.org/grpc"
	"google.golang.org/grpc/encoding/gzip"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	userservice "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"
)

var Admin = &userinfo.UserInfo{
	Account:  "admin",
	Fullname: "admin",
}

func GetUser(userRpc userservice.UserService, ctx context.Context, account string) (*userinfo.UserInfo, error) {
	// 查询用户信息
	if account == "empty" {
		return Admin, nil
	}

	resp, err := userRpc.GetUser(ctx, &userpb.GetUserReq{Account: account}, grpc.UseCompressor(gzip.Name))
	if err != nil {
		return nil, err
	}

	var userInfo userinfo.UserInfo
	_ = copier.Copy(&userInfo, &resp.UserInfo)

	return &userInfo, nil
}
