package account_pool_env_data

import (
	"net/http"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/logic/account_pool_env_data"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
)

func CancelChangeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CancelChangeReq
		if err := httpx.Parse(r, &req); err != nil {
			response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ParseParamError, err.<PERSON>rror()), "failed to parse parameters, error: %+v", err))
			httpx.Error(w, err)
			return
		}

		if err := validator.New().StructCtx(r.Context(), req); err != nil {
			response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ValidateParamError, err.Error()), "failed to validate parameters, error: %+v", err))
			return
		}

		l := account_pool_env_data.NewCancelChangeLogic(r.Context(), svcCtx)
		resp, err := l.CancelChange(&req)
		response.MakeHttpResponse(r, w, resp, err)
	}
}
