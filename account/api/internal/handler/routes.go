// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	accountPool "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/handler/account_pool"
	accountPoolColumn "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/handler/account_pool_column"
	accountPoolColumnRecycle "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/handler/account_pool_column_recycle"
	accountPoolEnv "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/handler/account_pool_env"
	accountPoolEnvData "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/handler/account_pool_env_data"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/account_pool/product_list",
				Handler: accountPool.GetProductListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool/create_product",
				Handler: accountPool.CreateProductHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool/create",
				Handler: accountPool.CreateAccountPoolHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/account_pool/delete",
				Handler: accountPool.DeleteAccountPoolHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/account_pool/modify",
				Handler: accountPool.ModifyAccountPoolHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool/list",
				Handler: accountPool.ListAccountPoolHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool_column/column_type",
				Handler: accountPoolColumn.GetAccountPoolColumnTypeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool_column/create",
				Handler: accountPoolColumn.CreateAccountPoolColumnHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/account_pool_column/delete",
				Handler: accountPoolColumn.DeleteAccountPoolColumnHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/account_pool_column/modify",
				Handler: accountPoolColumn.ModifyAccountPoolColumnReqHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool_column/list",
				Handler: accountPoolColumn.ListAccountPoolColumnHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool_column/list_all",
				Handler: accountPoolColumn.ListAllAccountPoolColumnHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool_column/list_product",
				Handler: accountPoolColumn.ListProductAccountPoolColumnHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool_column/list_product_all",
				Handler: accountPoolColumn.ListProductAccountPoolAllColumnHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool_column_recycle/list",
				Handler: accountPoolColumnRecycle.ListAccountPoolColumnRecycleHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/account_pool_column_recycle/delete",
				Handler: accountPoolColumnRecycle.DeleteAccountPoolColumnRecycleHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/account_pool_column_recycle/recover",
				Handler: accountPoolColumnRecycle.RecoverAccountPoolColumnRecycleHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool_env/create",
				Handler: accountPoolEnv.CreateAccountPoolEnvHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/account_pool_env/delete",
				Handler: accountPoolEnv.DeleteAccountPoolEnvHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/account_pool_env/modify",
				Handler: accountPoolEnv.ModifyAccountPoolEnvHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool_env/list",
				Handler: accountPoolEnv.ListAccountPoolEnvHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool_env/list_product_account_pool_env",
				Handler: accountPoolEnv.ListProductAccountPoolEnvHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool_env_data/upload_to_check",
				Handler: accountPoolEnv.UploadToCheckHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool_env_data/download",
				Handler: accountPoolEnvData.DownloadHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool_env_data/create",
				Handler: accountPoolEnvData.CreateAccountPoolEnvDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool_env_data/query_for_delete",
				Handler: accountPoolEnvData.QueryForDeleteAccountPoolEnvDataHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/account_pool_env_data/delete",
				Handler: accountPoolEnvData.DeleteAccountPoolEnvDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool_env_data/query_for_modify",
				Handler: accountPoolEnvData.QueryForModifyAccountPoolEnvDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/account_pool_env_data/modify",
				Handler: accountPoolEnvData.ModifyAccountPoolEnvDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool_env_data/cancel_change",
				Handler: accountPoolEnvData.CancelChangeHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/account_pool_env_data/operate_record_list",
				Handler: accountPoolEnvData.GetOperatorRecordListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool_env_data/re_detect",
				Handler: accountPoolEnv.ReDetectHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool_env_data/get_available_count_info",
				Handler: accountPoolEnvData.GetAvailableCountInfoHandler(serverCtx),
			},

			{
				Method:  http.MethodPost,
				Path:    "/account_pool_env_data/query_account",
				Handler: accountPoolEnvData.QueryAccountHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/account_pool_env_data/release_account",
				Handler: accountPoolEnvData.ReleaseAccountHandler(serverCtx),
			},
		},
		rest.WithPrefix("/account/v1"),
	)
}
