package account_pool_column

import (
	"fmt"
	"strings"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

func GenerateColumnDescribe(req *types.CreateAccountPoolColumnReq) (string, string, string, string, string) {
	columnType := req.ColumnType
	var (
		columnTypeStr    string
		character        string
		nullStr          string
		columnDefaultStr string
		commentStr       string
	)

	switch common.ColumnType(columnType) {
	case common.DATETIME:
		columnTypeStr = "char(19)"
	case common.TIMESTAMP:
		columnTypeStr = "bigint"
	default:
		columnLength := req.ColumnLength
		columnTypeName := common.ColumnTypeName[columnType]
		columnTypeNameLower := strings.ToLower(columnTypeName)
		if columnLength != 0 {
			columnTypeStr = fmt.Sprintf("%s(%d)", columnTypeNameLower, columnLength)
		} else {
			columnTypeStr = columnTypeNameLower
		}
	}

	switch common.ColumnType(columnType) {
	case common.VARCHAR, common.DATETIME:
		character = "CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci"
	default:
		character = ""
	}

	if common.YesOrNo(req.ColumnAllowNull) == common.YES {
		nullStr = "NULL"
	} else {
		nullStr = "NOT NULL"
	}

	if req.ColumnDefault == "" {
		columnDefaultStr = ""
	} else {
		columnDefaultStr = fmt.Sprintf("DEFAULT '%v'", req.ColumnDefault)
	}

	if req.ColumnComment == "" {
		commentStr = ""
	} else {
		commentStr = fmt.Sprintf("COMMENT '%v'", req.ColumnComment)
	}

	return columnTypeStr, character, nullStr, columnDefaultStr, commentStr
}
