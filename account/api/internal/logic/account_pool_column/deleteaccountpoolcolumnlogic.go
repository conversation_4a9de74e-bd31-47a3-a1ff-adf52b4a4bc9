package account_pool_column

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

type DeleteAccountPoolColumnLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteAccountPoolColumnLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteAccountPoolColumnLogic {
	return &DeleteAccountPoolColumnLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteAccountPoolColumnLogic) DeleteAccountPoolColumn(req *types.DeleteAccountPoolColumnReq) (
	*types.DeleteAccountPoolColumnResp, error,
) {
	poolId := req.PoolId
	columnId := req.ColumnId
	column, err := l.svcCtx.TPoolColumnModel.FindColumnByPoolIdColumnId(l.ctx, poolId, columnId)
	if err != nil || column == nil {
		return nil, errors.New(fmt.Sprintf("找不到pool_id为:%d, column_id为%s的字段，无法删除", poolId, columnId))
	}

	primaryKeyInt64 := common.YesOrNoValue[column.PrimaryKey.String]

	if int64(common.YES) == primaryKeyInt64 {
		return nil, errors.New("主键字段不允许删除")
	}

	selectEnvRecordSql := fmt.Sprintf(
		"" +
			"select pool_name from t_pool_table " +
			"where id in (" +
			"	select r.env_id " +
			"	from t_execute_record r, t_pool_table t " +
			"	where r.pool_id=t.id and r.pool_id=? " +
			"	and state not in (?, ?, ?, ?)" +
			")",
	)

	db, _ := l.svcCtx.DB.RawDB()
	rows, err := db.QueryContext(
		l.ctx, selectEnvRecordSql, poolId, "FINISHING", "TERMINATED", "EXCEPTION", "NONE_VALID_DATA",
	)
	if err != nil {
		return nil, err
	}

	columns, _ := rows.Columns()
	count := len(columns)
	valueSlice := make([]any, count)
	valuePtrSlice := make([]any, count)

	var poolEnvNames []string
	for rows.Next() {
		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			// value := valueSlice[0].([]uint8)
			poolEnvName := fmt.Sprintf("'%s'", cast.ToString(valueSlice[0]))
			poolEnvNames = append(poolEnvNames, poolEnvName)
		}
	}

	if len(poolEnvNames) > 0 {
		poolEnvNameStr := strings.Join(poolEnvNames, "、")
		return nil, errors.New(
			fmt.Sprintf(
				"以下账户池环境: %s有未完成的流水，不能删除字段:%s", poolEnvNameStr, columnId,
			),
		)
	}

	updateSql := "update t_pool_column set column_is_using=? where pool_id=? and column_id=?"
	_, err2 := db.ExecContext(l.ctx, updateSql, "NO", poolId, columnId)
	if err2 != nil {
		return nil, errors.New(fmt.Sprintf("执行sql%s发生异常:%s", updateSql, err2))
	}

	// 删除字段成功后需要尝试删除关联子环境redis数据
	poolTable, err := l.svcCtx.TPoolTableModel.FindPoolTableByPoolId(l.ctx, poolId)
	if err != nil {
		logx.Error(fmt.Sprintf("查询账号池模板表发生错误:%s", err))
	} else {
		poolTableName := poolTable.TableName
		prefixSlice := []string{"pool_column_name", "pool_fields", "pool_column_id_column_type"}
		for _, prefix := range prefixSlice {
			redisKey := fmt.Sprintf("%s_%s", prefix, poolTableName)
			_, redisErr := l.svcCtx.Redis.Del(redisKey)
			if redisErr != nil {
				logx.Error(fmt.Sprintf("删除redis发生错误:%s", err))
			}
		}
	}

	return &types.DeleteAccountPoolColumnResp{}, nil
}
