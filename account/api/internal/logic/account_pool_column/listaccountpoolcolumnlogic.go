package account_pool_column

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/user"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

type ListAccountPoolColumnLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListAccountPoolColumnLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListAccountPoolColumnLogic {
	return &ListAccountPoolColumnLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListAccountPoolColumnLogic) ListAccountPoolColumn(req *types.ListAccountPoolColumnReq) (
	*types.ListAccountPoolColumnResp, error,
) {
	flag, err := l.svcCtx.TPoolTableModel.IsPoolTemplateByPoolId(l.ctx, req.PoolId)
	if err != nil || !flag {
		return nil, err
	}
	columns, err := l.svcCtx.TPoolColumnModel.FindVisibleColumnByPoolId(l.ctx, req.PoolId)
	if err != nil || !flag {
		return nil, err
	}

	var resp types.ListAccountPoolColumnResp

	// 用户映射
	userMap := make(map[string]*userinfo.UserInfo)
	var userErr error

	for _, column := range columns {
		createdBy, ok := userMap[column.CreatedBy]
		if !ok {
			createdBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, column.CreatedBy)
			if userErr != nil {
				err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
				return nil, err
			}
		}
		userMap[column.CreatedBy] = createdBy
		updatedBy, ok := userMap[column.UpdatedBy]
		if !ok {
			updatedBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, column.UpdatedBy)
			if userErr != nil {
				err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
				return nil, err
			}
		}
		userMap[column.UpdatedBy] = updatedBy
		item := types.PoolColumnItem{
			PoolId:          column.PoolId,
			ColumnId:        column.ColumnId,
			ColumnName:      column.ColumnName,
			ColumnType:      common.ColumnTypeValue[column.ColumnType],
			ColumnLength:    column.ColumnLength.Int64,
			ColumnDefault:   column.ColumnDefault.String,
			ColumnAllowNull: common.YesOrNoValue[column.ColumnAllowNull],
			ColumnIsVisible: common.YesOrNoValue[column.ColumnIsVisible],
			ColumnComment:   column.ColumnComment.String,
			PrimaryKey:      common.YesOrNoValue[column.PrimaryKey.String],
			ColumnIsUsing:   common.YesOrNoValue[column.ColumnIsUsing],
			CreatedBy:       createdBy,
			UpdatedBy:       updatedBy,
			CreatedAt:       column.CreatedAt.UnixMilli(),
			UpdatedAt:       column.UpdatedAt.UnixMilli(),
		}
		resp.Items = append(resp.Items, &item)
	}
	return &resp, nil
}
