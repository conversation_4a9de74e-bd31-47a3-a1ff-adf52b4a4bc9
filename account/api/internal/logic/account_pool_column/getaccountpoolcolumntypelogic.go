package account_pool_column

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
)

type GetAccountPoolColumnTypeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetAccountPoolColumnTypeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAccountPoolColumnTypeLogic {
	return &GetAccountPoolColumnTypeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAccountPoolColumnTypeLogic) GetAccountPoolColumnType(req *types.GetAccountPoolColumnTypeReq) (
	*types.GetAccountPoolColumnTypeResp, error,
) {
	varcharType := types.ColumnType{
		TypeId:   1,
		TypeName: "varchar",
	}
	intType := types.ColumnType{
		TypeId:   2,
		TypeName: "int",
	}
	floatType := types.ColumnType{
		TypeId:   3,
		TypeName: "float",
	}
	tinyintType := types.ColumnType{
		TypeId:   4,
		TypeName: "tinyint",
	}
	datetimeType := types.ColumnType{
		TypeId:   5,
		TypeName: "datetime",
	}
	timestampType := types.ColumnType{
		TypeId:   6,
		TypeName: "timestamp",
	}

	columnTypeList := []*types.ColumnType{
		&varcharType,
		&intType,
		&floatType,
		&tinyintType,
		&datetimeType,
		&timestampType,
	}

	return &types.GetAccountPoolColumnTypeResp{
		ColumnType_list: columnTypeList,
	}, nil
}
