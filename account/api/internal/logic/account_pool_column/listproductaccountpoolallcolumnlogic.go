package account_pool_column

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/user"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

type ListProductAccountPoolAllColumnLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListProductAccountPoolAllColumnLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ListProductAccountPoolAllColumnLogic {
	return &ListProductAccountPoolAllColumnLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListProductAccountPoolAllColumnLogic) ListProductAccountPoolAllColumn(req *types.ListProductAccountPoolAllColumnReq) (
	*types.ListProductAccountPoolAllColumnResp, error,
) {
	poolId, err := l.svcCtx.TPoolTableModel.FindPoolIdByBelongProduct(l.ctx, req.BelongProduct)
	if err != nil && err.Error() == "sql: no rows in result set" {
		return nil, fmt.Errorf("不存在belong_product为'%s'的账号池", req.BelongProduct)
	}
	columns, err := l.svcCtx.TPoolColumnModel.FindAllColumnByPoolId(l.ctx, poolId)
	if err != nil {
		return nil, err
	}

	var resp types.ListProductAccountPoolAllColumnResp

	// 用户映射
	userMap := make(map[string]*userinfo.UserInfo)
	var userErr error

	for _, column := range columns {
		createdBy, ok := userMap[column.CreatedBy]
		if !ok {
			createdBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, column.CreatedBy)
			if userErr != nil {
				err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
				return nil, err
			}
		}
		userMap[column.CreatedBy] = createdBy
		updatedBy, ok := userMap[column.UpdatedBy]
		if !ok {
			updatedBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, column.UpdatedBy)
			if userErr != nil {
				err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
				return nil, err
			}
		}
		item := types.PoolColumnItem{
			PoolId:          column.PoolId,
			ColumnId:        column.ColumnId,
			ColumnName:      column.ColumnName,
			ColumnType:      common.ColumnTypeValue[column.ColumnType],
			ColumnLength:    column.ColumnLength.Int64,
			ColumnDefault:   column.ColumnDefault.String,
			ColumnAllowNull: common.YesOrNoValue[column.ColumnAllowNull],
			ColumnIsVisible: common.YesOrNoValue[column.ColumnIsVisible],
			ColumnComment:   column.ColumnComment.String,
			PrimaryKey:      common.YesOrNoValue[column.PrimaryKey.String],
			ColumnIsUsing:   common.YesOrNoValue[column.ColumnIsUsing],
			CreatedBy:       createdBy,
			UpdatedBy:       updatedBy,
			CreatedAt:       column.CreatedAt.UnixMilli(),
			UpdatedAt:       column.UpdatedAt.UnixMilli(),
		}
		resp.Items = append(resp.Items, &item)
	}
	return &resp, nil
}
