package account_pool_column

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type CreateAccountPoolColumnLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateAccountPoolColumnLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAccountPoolColumnLogic {
	return &CreateAccountPoolColumnLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateAccountPoolColumnLogic) CreateAccountPoolColumn(req *types.CreateAccountPoolColumnReq) (
	resp *types.CreateAccountPoolColumnResp, err error,
) {
	err = utils.CheckColumnDdl(req)
	if err != nil {
		return nil, err
	}

	poolTable, err := l.svcCtx.TPoolTableModel.FindPoolTableByPoolId(l.ctx, req.PoolId)
	if err != nil || poolTable == nil {
		return nil, errors.New(fmt.Sprintf("找不到pool_id为:%d的账户池模版，无法新增字段", req.PoolId))
	}

	tableName := poolTable.TableName

	poolColumns, _ := l.svcCtx.TPoolColumnModel.FindAllColumnByPoolId(l.ctx, req.PoolId)
	columnId := req.ColumnId
	columnName := req.ColumnName
	for _, poolColumn := range poolColumns {
		if columnId == poolColumn.ColumnId || columnName == poolColumn.ColumnName {
			return nil, errors.New("不能新增已经存在的字段")
		}
	}

	columnTypeStr, character, nullStr, columnDefaultStr, commentStr := GenerateColumnDescribe(req)
	var primaryKey string
	var ddlList, redoDdlSqlList []string

	// 创建新的字段
	if len(poolColumns) == 0 {
		columnAllowNull := req.ColumnAllowNull
		if common.YesOrNo(columnAllowNull) == common.YES {
			return nil, errors.New("第一个字段为主键，不能设置为允许为空")
		}
		if common.ColumnType(req.ColumnType) != common.VARCHAR {
			return nil, errors.New("第一个字段为主键，只能设置为varchar类型")
		}
		primaryKey = common.YesOrNoName[int64(common.YES)]
		tableDdlSql := fmt.Sprintf(
			""+
				"CREATE TABLE `%s` ("+
				"   `id` int(11) NOT NULL AUTO_INCREMENT,"+
				"   `create_time` bigint NOT NULL COMMENT '行数据创建时间',"+
				"   `acquire_time` bigint NULL default NULL COMMENT '行数据被获取时间',"+
				"   `return_time` bigint NULL default NULL COMMENT '行数据被归还时间',"+
				"   `cooling_time` int(11) NULL default 0 COMMENT '池账号冷却时间',"+
				"	`occupy_state` enum('UNUSED', 'USING', 'TO_BE_UPDATE', 'TO_BE_DELETE') NOT NULL default 'UNUSED' COMMENT '行数据被占用状态',"+
				"	`related_execute_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL default NULL COMMENT '关联的执行id',"+
				"	`%s` %s %s %s %s %s, "+
				"	PRIMARY KEY (`id`) USING BTREE,"+
				"   UNIQUE INDEX `uix_account`(`%s`) USING BTREE "+
				") ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;",
			tableName, columnId, columnTypeStr, character, nullStr, columnDefaultStr, commentStr, columnId,
		)
		ddlList = append(ddlList, tableDdlSql)
		redoSql := fmt.Sprintf("DROP table`%s`", tableName)
		redoDdlSqlList = append(redoDdlSqlList, redoSql)
	} else {
		primaryKey = common.YesOrNoName[int64(common.NO)]
		tableDdlSql := fmt.Sprintf(
			""+
				"ALTER TABLE `%s` add `%s` %s %s %s %s",
			tableName, columnId, columnTypeStr, nullStr, columnDefaultStr, commentStr,
		)
		ddlList = append(ddlList, tableDdlSql)
		redoSql0 := fmt.Sprintf("ALTER TABLE `%s` drop column %s", tableName, columnId)
		redoDdlSqlList = append(redoDdlSqlList, redoSql0)

		poolEnvTables, _ := l.svcCtx.TPoolTableModel.FindPoolEnvTablesByPoolId(l.ctx, req.PoolId)
		for _, poolEnvTable := range poolEnvTables {
			envTableName := poolEnvTable.TableName
			envTableDdlSql := fmt.Sprintf(
				""+
					"ALTER TABLE `%s` add `%s` %s %s %s %s",
				envTableName, columnId, columnTypeStr, nullStr, columnDefaultStr, commentStr,
			)
			ddlList = append(ddlList, envTableDdlSql)
			redoSql1 := fmt.Sprintf("ALTER TABLE `%s` drop column %s", envTableName, columnId)
			redoDdlSqlList = append(redoDdlSqlList, redoSql1)
		}
	}

	db, _ := l.svcCtx.DB.RawDB()

	flag := true
	index_ := 0
	sql_ := ""
	var err_ error
	for index, sql := range ddlList {
		_, err := db.ExecContext(l.ctx, sql)
		if err != nil {
			flag = false
			index_ = index
			sql_ = sql
			err_ = err
			break
		}
	}

	if !flag {
		for i := 0; i < index_+1; i++ {
			_, _ = db.ExecContext(l.ctx, redoDdlSqlList[i])
		}
		return nil, errors.New(fmt.Sprintf("执行sql:%s发生异常:%s", sql_, err_))
	}

	loginUser := userinfo.FromContext(l.ctx)

	data := &model.TPoolColumn{
		PoolId:     req.PoolId,
		TableName:  tableName,
		ColumnId:   req.ColumnId,
		ColumnName: req.ColumnName,
		ColumnType: common.ColumnTypeName[req.ColumnType],
		ColumnLength: sql.NullInt64{
			Int64: req.ColumnLength,
			Valid: req.ColumnLength != 0,
		},
		ColumnDefault: sql.NullString{
			String: req.ColumnDefault,
			Valid:  req.ColumnDefault != "",
		},
		ColumnAllowNull: common.YesOrNoName[req.ColumnAllowNull],
		ColumnIsVisible: common.YesOrNoName[req.ColumnIsVisible],
		ColumnComment: sql.NullString{
			String: req.ColumnComment,
			Valid:  req.ColumnComment != "",
		},
		ColumnIsUsing: common.YesOrNoName[int64(common.YES)],
		PrimaryKey: sql.NullString{
			String: primaryKey,
			Valid:  primaryKey != "",
		},
		CreatedBy: loginUser.Account,
		UpdatedBy: loginUser.Account,
	}

	_, _ = l.svcCtx.TPoolColumnModel.Insert(l.ctx, data)
	column, _ := l.svcCtx.TPoolColumnModel.FindColumnByPoolIdColumnId(l.ctx, req.PoolId, req.ColumnId)

	// 创建字段成功后需要尝试删除关联子环境redis数据
	poolTableName := poolTable.TableName
	prefixSlice := []string{"pool_column_name", "pool_fields", "pool_column_id_column_type"}
	for _, prefix := range prefixSlice {
		redisKey := fmt.Sprintf("%s_%s", prefix, poolTableName)
		_, redisErr := l.svcCtx.Redis.Del(redisKey)
		if redisErr != nil {
			logx.Error(fmt.Sprintf("删除redis发生错误:%s", err))
		}
	}

	return &types.CreateAccountPoolColumnResp{
		PoolId:          column.PoolId,
		ColumnId:        column.ColumnId,
		ColumnName:      column.ColumnName,
		ColumnType:      common.YesOrNoValue[column.ColumnType],
		ColumnLength:    column.ColumnLength.Int64,
		ColumnDefault:   column.ColumnDefault.String,
		ColumnAllowNull: common.YesOrNoValue[column.ColumnAllowNull],
		ColumnIsVisible: common.YesOrNoValue[column.ColumnIsVisible],
		ColumnComment:   column.ColumnComment.String,
		PrimaryKey:      common.YesOrNoValue[column.PrimaryKey.String],
		ColumnIsUsing:   common.YesOrNoValue[column.ColumnIsUsing],
		CreatedBy:       loginUser,
		UpdatedBy:       loginUser,
		CreatedAt:       column.CreatedAt.UnixMilli(),
		UpdatedAt:       column.UpdatedAt.UnixMilli(),
	}, nil
}
