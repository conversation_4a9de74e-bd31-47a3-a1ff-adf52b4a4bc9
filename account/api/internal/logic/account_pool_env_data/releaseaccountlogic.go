package account_pool_env_data

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
)

type ReleaseAccountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReleaseAccountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReleaseAccountLogic {
	return &ReleaseAccountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReleaseAccountLogic) ReleaseAccount(req *types.ReleaseAccountReq) (resp *types.ReleaseAccountResp, err error) {
	in := &pb.ReleaseTestAccountRequest{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.AccountRpc.ReleaseTestAccount(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ReleaseAccountResp{}
	if err = utils.Copy(resp, out); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
