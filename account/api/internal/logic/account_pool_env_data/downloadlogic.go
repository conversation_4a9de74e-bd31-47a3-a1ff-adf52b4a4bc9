package account_pool_env_data

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/utils"
)

type DownloadLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDownloadLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DownloadLogic {
	return &DownloadLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DownloadLogic) Download(req *types.DownloadReq) (*types.DownloadResp, error) {
	fileName := req.FileName
	filePath := path.Join(l.svcCtx.Config.FileStore.ExcelPath, req.FileName)

	// 判断excel文件是否存在
	if _, err := os.Stat(filePath); err != nil {
		if os.IsNotExist(err) {
			return nil, errors.New(fmt.Sprintf("excel文件「%s」不存在", req.FileName))
		} else {
			return nil, err
		}
	}

	// 判断是下载的否为筛查出来修改的excel
	if req.ExecuteId != "" {
		db, _ := l.svcCtx.DB.RawDB()
		selectRecordSql := fmt.Sprintf("select current_json from t_execute_record where execute_id='%s'", req.ExecuteId)
		rows, err := db.QueryContext(l.ctx, selectRecordSql)
		if err != nil {
			return nil, errors.New(fmt.Sprintf("查找execute_id为「%s」的流水记录发生异常", req.ExecuteId))
		}
		valueSlice := make([]any, 1)
		valuePtrSlice := make([]any, 1)
		var excelFileName string
		for rows.Next() {
			valuePtrSlice[0] = &valueSlice[0]
			err := rows.Scan(valuePtrSlice...)
			if err == nil {
				// value := valueSlice[0].([]uint8)
				// valueStr := string(value)
				var currentJsonObj utils.PoolEnvDataExecCurrentJson
				_ = json.Unmarshal([]byte(cast.ToString(valueSlice[0])), &currentJsonObj)
				excelFileName = currentJsonObj.ExcelFileName
			}
		}
		if excelFileName == "" {
			return nil, errors.New(fmt.Sprintf("查找不到execute_id为「%s」的流水记录", req.ExecuteId))
		}
		excelDirPath := l.svcCtx.Config.FileStore.ExcelPath
		// 将之前筛选出来的excel改为backup
		_, err = utils.GetBackupExcelPath(excelDirPath, excelFileName)
		if err != nil {
			return nil, errors.New(fmt.Sprintf("将之前筛选出来的excel改为backup发生异常：「%s」", err))
		}
		// 更新流水为待更新状态
		updateRecordSql := fmt.Sprintf(
			"update t_execute_record set state='TO_BE_UPDATE' "+
				"where execute_id='%s'", req.ExecuteId,
		)
		_, err = db.ExecContext(l.ctx, updateRecordSql)
		if err != nil {
			return nil, errors.New(fmt.Sprintf("执行sql「%s」发生异常", updateRecordSql))
		}
	}

	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("读取excel「%s文件」发生错误：「%s」", req.FileName, err))
	}
	fileContent := base64.StdEncoding.EncodeToString(data)

	return &types.DownloadResp{
		FileName:    fileName,
		FileContent: fileContent,
	}, nil
}
