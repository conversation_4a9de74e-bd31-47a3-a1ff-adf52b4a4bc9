package account_pool_env_data

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type QueryForModifyAccountPoolEnvDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryForModifyAccountPoolEnvDataLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *QueryForModifyAccountPoolEnvDataLogic {
	return &QueryForModifyAccountPoolEnvDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func GenQueryModifySql(
	l *QueryForModifyAccountPoolEnvDataLogic, req *types.QueryForModifyAccountPoolEnvDataReq, primaryColumn string,
	envTableName string,
) (string, error) {
	// 生成查询sql语句

	// 判断传参
	selectedColumnIdList := req.SelectedColumnIdList
	if len(selectedColumnIdList) == 0 {
		return "", errors.New("必须先传递选取哪些字段进行修改")
	}
	columns, _ := l.svcCtx.TPoolColumnModel.FindAllColumnByPoolId(l.ctx, req.PoolId)
	existsColumnMap := make(map[string]string)
	for _, column := range columns {
		existsColumnMap[column.ColumnId] = column.ColumnId
	}
	if selectedColumnIdList[0] != primaryColumn {
		return "", errors.New("获取的第1个字段必须是主键字段")
	}
	retainColumnIdList := []string{
		"create_time", "acquire_time", "return_time", "cooling_time", "occupy_state", "related_execute_id",
	}
	selectedColumnIdMap := make(map[string]string)
	for _, selectedColumnId := range selectedColumnIdList {
		selectedColumnIdMap[selectedColumnId] = selectedColumnId
	}
	for _, retainColumnId := range retainColumnIdList {
		_, ok := selectedColumnIdMap[retainColumnId]
		if ok {
			return "", errors.New(
				fmt.Sprintf(
					"获取的字段不能包含以下保留字段：%s", strings.Join(retainColumnIdList, ","),
				),
			)
		}
	}
	for _, selectedColumnId := range selectedColumnIdList {
		_, ok := existsColumnMap[selectedColumnId]
		if !ok {
			return "", errors.New("获取的字段不是全部都存在于表中!")
		}
	}

	// 组装查询sql语句
	selectBuilder := squirrel.Select(selectedColumnIdList...).From(envTableName).
		Where("occupy_state='UNUSED'").Limit(uint64(req.ExpectedCount))
	pooTable, _ := l.svcCtx.TPoolTableModel.FindPoolTableByPoolId(l.ctx, req.PoolId)
	fields, _ := l.svcCtx.TPoolColumnModel.FindAllColumnNameWithBackQuoteByTableName(l.ctx, pooTable.TableName)
	poolEnvModel := model.NewPoolEnvTableModel(l.svcCtx.DB, envTableName, fields)
	if req.Condition != nil {
		selectBuilder = sqlbuilder.SearchOptions(selectBuilder, sqlbuilder.WithCondition(poolEnvModel, req.Condition))
	}

	sql, values, err := selectBuilder.ToSql()
	for _, value := range values {
		sql = strings.Replace(sql, "?", fmt.Sprintf("'%s'", value), 1)
	}

	if err != nil {
		// fmt.Println(err)
		return "", errors.New(fmt.Sprintf("组装sql发生错误:%s", err))
	}
	return sql, nil
}

func RealQueryPoolEnvDataForModify(
	l *QueryForModifyAccountPoolEnvDataLogic, req *types.QueryForModifyAccountPoolEnvDataReq, primaryColumn string,
) {
	// ctx跨协程使用会有问题，得重新赋值
	ctx := context.Background()
	l.ctx = ctx

	// 请求参数
	executeId := req.ExecuteId
	poolId := req.PoolId
	envId := req.EnvId
	expectedCount := req.ExpectedCount

	logger := l.Logger
	db, _ := l.svcCtx.DB.RawDB()

	var stateName string
	var result string

	// 更新操作记录
	defer func() {
		if err := recover(); err != nil {
			logger.Error(err)
		}

		resultRune := []rune(result)
		resultRuneLength := len(resultRune)
		if resultRuneLength < 255 {
			result = string(resultRune[0:resultRuneLength])
		} else {
			result = string(resultRune[0:255])
		}
		updateRecordSql := fmt.Sprintf(
			"update t_execute_record set state='%s',result='%s' where execute_id='%s'",
			stateName, result, executeId,
		)
		_, _ = db.ExecContext(l.ctx, updateRecordSql)
	}()

	// 筛选sql语句
	var querySql string
	var err error
	// 重新检测的才有query_sql
	envTable, _ := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, poolId, envId)
	envTableName := envTable.TableName
	if req.QuerySql == "" {
		querySql, err = GenQueryModifySql(l, req, primaryColumn, envTableName)
		if err != nil {
			stateName = "EXCEPTION"
			result = fmt.Sprintf("生成筛选sql语句发生异常:「%s」", err)
			return
		}
	} else {
		querySql = req.QuerySql
	}

	// 获取poolEnvModel
	poolEnv, _ := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, poolId, envId)
	// 获取池账号字段
	poolTemplate, _ := l.svcCtx.TPoolTableModel.FindPoolTableByPoolId(l.ctx, poolId)
	fields, _ := l.svcCtx.TPoolColumnModel.FindAllColumnNameWithBackQuoteByTableName(l.ctx, poolTemplate.TableName)
	// 筛选池账号数据
	poolEnvModel := model.NewPoolEnvTableModel(l.svcCtx.DB, poolEnv.TableName, fields)
	dataRows, err := poolEnvModel.QueryWithSql(l.ctx, querySql)
	if err != nil {
		stateName = "EXCEPTION"
		result = fmt.Sprintf("筛选池账号发生异常:「%s」", err)
		return
	}
	dataCount := len(dataRows)
	if dataCount == 0 {
		stateName = "NONE_VALID_DATA"
		result = "没有任何匹配的数据"
		return
	}

	// 待更新sql语句(更新池账号占用状态)
	var updateDataSqlList []string
	step := 1000
	start := 0
	end := step
	for {
		if start >= dataCount {
			break
		}
		if end > dataCount {
			end = dataCount
		}
		dataRow := dataRows[start:end]
		var primaryValueList []string
		for _, dataColumns := range dataRow {
			for _, dataColumn := range *dataColumns {
				if dataColumn.Field == primaryColumn {
					primaryValueList = append(primaryValueList, fmt.Sprintf("'%s'", dataColumn.Value))
					break
				}
			}
		}
		primaryColumnIdStr := strings.Join(primaryValueList, ",")
		updateDataSql := fmt.Sprintf(
			""+
				"update %s set occupy_state='TO_BE_UPDATE', related_execute_id='%s' "+
				"where `%s` in (%s)",
			envTableName, executeId, primaryColumn, primaryColumnIdStr,
		)
		updateDataSqlList = append(updateDataSqlList, updateDataSql)
		start += step
		end += step
	}
	var sql string // 保存最后一次执行的sql
	err = poolEnvModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			for _, sql = range updateDataSqlList {
				_, err := poolEnvModel.ExecSqlTX(ctx, session, sql)
				if err != nil {
					return err
				}
			}
			return nil
		},
	)
	const updateOccupyError = "更新池账号占用状态发生异常"
	if err != nil {
		stateName = "EXCEPTION"
		result = updateOccupyError
		return
	}
	stateName = "END_OF_QUERY"
	// 筛选到的池账号条数少于预期
	if int64(dataCount) < expectedCount {
		result = fmt.Sprintf("期望的条数为「%d」条，但实际匹配的条数只有「%d」条", expectedCount, dataCount)
	} else {
		result = "生成excel成功"
	}
	excelFileName := fmt.Sprintf("%d_%d_%s_%s.xlsx", poolId, envId, executeId, "MODIFY_DATA")
	currentJsonObj := utils.PoolEnvDataExecCurrentJson{
		ExcelFileName: excelFileName,
		QuerySql:      querySql,
		ExpectedCount: expectedCount,
		Way:           "QUERY_TO_MODIFY_DATA",
		OkCount:       int64(dataCount),
	}
	b, _ := json.Marshal(currentJsonObj)
	currentJsonStr := string(b)
	recordSql := "update t_execute_record set state=?, result=?, current_json=? where execute_id=?"
	_, err = db.ExecContext(l.ctx, recordSql, stateName, result, currentJsonStr, executeId)
	if err != nil {
		stateName = "EXCEPTION"
		result = updateOccupyError
		return
	}

	// 生成excel
	excelDirPath := l.svcCtx.Config.FileStore.ExcelPath
	excelPath := path.Join(excelDirPath, excelFileName)
	os.Remove(excelPath)
	// 新建一个book
	xlsxFile := excelize.NewFile()
	index := xlsxFile.NewSheet("Sheet1")
	// 写表头
	for i, selectedColumnId := range req.SelectedColumnIdList {
		columnLetter, _ := excelize.ColumnNumberToName(i + 1)
		axis, _ := excelize.JoinCellName(columnLetter, 1)
		_ = xlsxFile.SetCellValue("Sheet1", axis, selectedColumnId)
	}
	// 写数据
	for j, dataRow := range dataRows {
		for k, dataColumn := range *dataRow {
			columnLetter, _ := excelize.ColumnNumberToName(k + 1)
			axis, _ := excelize.JoinCellName(columnLetter, j+2)
			_ = xlsxFile.SetCellValue("Sheet1", axis, dataColumn.Value)
		}
	}
	// 保存excel
	xlsxFile.SetActiveSheet(index)
	err = xlsxFile.SaveAs(excelPath)
	if err != nil {
		stateName = "EXCEPTION"
		result = "生成excel文件失败"
	}
}

func (l *QueryForModifyAccountPoolEnvDataLogic) QueryForModifyAccountPoolEnvData(req *types.QueryForModifyAccountPoolEnvDataReq) (
	*types.QueryForModifyAccountPoolEnvDataResp, error,
) {
	// 请求参数
	poolId := req.PoolId
	envId := req.EnvId
	expectedCount := req.ExpectedCount

	if expectedCount <= 0 || expectedCount > 200000 {
		return nil, errors.New("单次最多只能筛选0~20万条数据")
	}

	// 主键字段
	db, _ := l.svcCtx.DB.RawDB()
	selectSql := fmt.Sprintf(
		"" +
			"select c.column_id " +
			"from t_pool_table t, t_pool_column c " +
			"where t.parent_pool_id=c.pool_id and t.parent_pool_id=? " +
			"and t.id=? and c.primary_key='YES'",
	)
	rows, err := db.Query(selectSql, poolId, envId)
	if err != nil {
		return nil, errors.New(
			fmt.Sprintf(
				"查找pool_id为：「%d」和env_id为：「%d」的池账号键字段发生错误：%s", poolId, envId, err,
			),
		)
	}
	columns, _ := rows.Columns()
	valueSlice := make([]any, 1)
	valuePtrSlice := make([]any, 1)
	var primaryColumnId string
	for rows.Next() {
		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			// value := valueSlice[0].([]uint8)
			primaryColumnId = cast.ToString(valueSlice[0])
		}
	}

	if primaryColumnId == "" {
		return nil, errors.New(fmt.Sprintf("找不到pool_id为：「%d」和env_id为：「%d」的池账号环境或主键字段", poolId, envId))
	}

	// 生成执行id
	if req.ExecuteId == "" {
		currentTimeStamp := time.Now().UnixMilli()
		executeId := qetutils.GenNanoId(strconv.FormatInt(currentTimeStamp, 10))
		req.ExecuteId = executeId
	}

	loginUser := userinfo.FromContext(l.ctx)

	// 生成或更新操作流水
	recordSql := fmt.Sprintf(
		""+
			"INSERT INTO t_execute_record "+
			"(execute_id, pool_id, env_id, execute_way, "+
			"description, state, result, current_json, "+
			"created_by, updated_by) "+
			"VALUES "+
			"("+
			"'%s', %d, %d, '%s', "+
			"'%s', '%s', '%s', '%s', "+
			"'%s', '%s') "+
			"ON DUPLICATE KEY UPDATE "+
			"state='%s', result='%s', "+
			"updated_by='%s' ",
		req.ExecuteId, req.PoolId, req.EnvId, "MODIFY_DATA",
		"None", "QUERYING", "None", "None",
		loginUser.Account, loginUser.Account,
		"QUERYING", "None",
		loginUser.Account,
	)
	recordSql = strings.ReplaceAll(recordSql, "'None',", "null,")
	recordSql = strings.ReplaceAll(recordSql, "None,", "null,")
	_, err = db.ExecContext(l.ctx, recordSql)
	if err != nil {
		return nil, errors.New("生成操作流水发生异常，请联系管理员")
	}

	// 开启协程
	go RealQueryPoolEnvDataForModify(l, req, primaryColumnId)

	resp := &types.QueryForModifyAccountPoolEnvDataResp{
		ExecuteId: req.ExecuteId,
	}
	return resp, nil
}
