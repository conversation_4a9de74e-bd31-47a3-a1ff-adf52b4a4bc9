package account_pool_env_data

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type QueryForDeleteAccountPoolEnvDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryForDeleteAccountPoolEnvDataLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *QueryForDeleteAccountPoolEnvDataLogic {
	return &QueryForDeleteAccountPoolEnvDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func GenQueryDeleteSql(
	l *QueryForDeleteAccountPoolEnvDataLogic, req *types.QueryForDeleteAccountPoolEnvDataReq, primaryColumn string,
	envTableName string,
) (string, error) {
	// 生成查询sql语句

	// 组装查询sql语句
	selectBuilder := squirrel.Select(primaryColumn).From(envTableName).
		Where("occupy_state !='TO_BE_DELETE'").Limit(uint64(req.ExpectedCount))
	pooTable, _ := l.svcCtx.TPoolTableModel.FindPoolTableByPoolId(l.ctx, req.PoolId)
	fields, _ := l.svcCtx.TPoolColumnModel.FindAllColumnNameWithBackQuoteByTableName(l.ctx, pooTable.TableName)
	poolEnvModel := model.NewPoolEnvTableModel(l.svcCtx.DB, envTableName, fields)
	if req.Condition != nil {
		selectBuilder = sqlbuilder.SearchOptions(selectBuilder, sqlbuilder.WithCondition(poolEnvModel, req.Condition))
	}
	sql, values, err := selectBuilder.ToSql()
	for _, value := range values {
		sql = strings.Replace(sql, "?", fmt.Sprintf("'%s'", value), 1)
	}
	if err != nil {
		fmt.Println(err)
		return "", errors.New("组装sql发生错误")
	}

	return sql, nil
}

func RealQueryPoolEnvDataForDelete(
	l *QueryForDeleteAccountPoolEnvDataLogic, req *types.QueryForDeleteAccountPoolEnvDataReq, primaryColumn string,
) {
	// ctx跨协程使用会有问题，得重新赋值
	ctx := context.Background()
	l.ctx = ctx

	// 请求参数
	executeId := req.ExecuteId
	poolId := req.PoolId
	envId := req.EnvId
	expectedCount := req.ExpectedCount

	logger := l.Logger
	db, _ := l.svcCtx.DB.RawDB()

	var stateName string
	var result string

	// 更新操作记录
	defer func() {
		if err := recover(); err != nil {
			logger.Error(err)
		}

		resultRune := []rune(result)
		resultRuneLength := len(resultRune)
		if resultRuneLength < 255 {
			result = string(resultRune[0:resultRuneLength])
		} else {
			result = string(resultRune[0:255])
		}
		updateRecordSql := fmt.Sprintf(
			"update t_execute_record set state='%s',result='%s' where execute_id='%s'",
			stateName, result, executeId,
		)
		_, _ = db.ExecContext(l.ctx, updateRecordSql)
	}()

	// 筛选sql语句
	var querySql string
	var err error
	// 重新检测的才有query_sql
	envTable, _ := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, poolId, envId)
	envTableName := envTable.TableName

	const exceptionState = "EXCEPTION"
	if req.QuerySql == "" {
		querySql, err = GenQueryDeleteSql(l, req, primaryColumn, envTableName)
		if err != nil {
			stateName = exceptionState
			result = "生成筛选sql语句发生异常"
			return
		}
	} else {
		querySql = req.QuerySql
	}

	// 获取poolEnvModel
	poolEnv, _ := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, poolId, envId)
	// 获取池账号字段
	poolTemplate, _ := l.svcCtx.TPoolTableModel.FindPoolTableByPoolId(l.ctx, poolId)
	fields, _ := l.svcCtx.TPoolColumnModel.FindAllColumnNameWithBackQuoteByTableName(l.ctx, poolTemplate.TableName)
	// 筛选池账号数据
	poolEnvModel := model.NewPoolEnvTableModel(l.svcCtx.DB, poolEnv.TableName, fields)
	dataRows, err := poolEnvModel.QueryWithSql(l.ctx, querySql)
	if err != nil {
		stateName = exceptionState
		result = fmt.Sprintf("筛选池账号发生异常:「%s」", err)
		return
	}
	dataCount := len(dataRows)

	// 判断删除的条数是否满足条件
	if dataCount == 0 {
		stateName = exceptionState
		result = "没有任何可以删除的池账号"
		return
	}
	stateName = "END_OF_QUERY"
	if int64(dataCount) < expectedCount {
		result = fmt.Sprintf("期望的条数为：「%d」条，但实际匹配的条数只有「%d」条", expectedCount, dataCount)
	}

	// 生成删除sql语句
	var deleteSqlList []string
	var updateDataSqlList []string

	step := 1000
	start := 0
	end := step

	for {
		if start >= dataCount {
			break
		}
		if end > dataCount {
			end = dataCount
		}
		dataRow := dataRows[start:end]
		var primaryValueList []string
		for _, dataColumns := range dataRow {
			for _, dataColumn := range *dataColumns {
				primaryValueList = append(primaryValueList, fmt.Sprintf("'%s'", dataColumn.Value))
				break
			}
		}
		primaryValueStr := strings.Join(primaryValueList, ",")
		deleteSql := fmt.Sprintf("delete from %s where %s in (%s)", envTableName, primaryColumn, primaryValueStr)
		deleteSqlList = append(deleteSqlList, deleteSql)
		updateDataSql := fmt.Sprintf(
			""+
				"update %s set occupy_state='TO_BE_DELETE', related_execute_id='%s' "+
				"where `%s` in (%s)",
			envTableName, executeId, primaryColumn, primaryValueStr,
		)
		updateDataSqlList = append(updateDataSqlList, updateDataSql)
		start += step
		end += step
	}

	// 保存sql文件
	sqlFileName := fmt.Sprintf("%d_%d_%s_%s.sql", req.PoolId, req.EnvId, req.ExecuteId, "DELETE_DATA")
	sqlPath := path.Join(l.svcCtx.Config.FileStore.SqlPath, sqlFileName)
	f, _ := os.OpenFile(sqlPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0o755)
	for _, sql := range deleteSqlList {
		_, _ = f.WriteString(sql)
		_, _ = f.WriteString("\r\n")
	}
	err = f.Close()
	if err != nil {
		fmt.Println("close sql file error")
	}

	// 将关联记录占用状态改为TO_BE_DELETE
	updateErr := poolEnvModel.Trans(
		ctx, func(context context.Context, session sqlx.Session) error {
			for _, sql := range updateDataSqlList {
				_, err := poolEnvModel.ExecSqlTX(ctx, session, sql)
				if err != nil {
					return err
				}
			}
			return nil
		},
	)
	if updateErr != nil {
		fmt.Println(updateErr)
	}

	// 更新操作流水
	currentJsonObj := utils.PoolEnvDataExecCurrentJson{
		SqlFileName:   sqlFileName,
		QuerySql:      querySql,
		ExpectedCount: expectedCount,
		OkCount:       int64(dataCount),
		Way:           "QUERY_TO_DELETE_DATA",
	}
	b, _ := json.Marshal(currentJsonObj)
	currentJsonStr := string(b)
	recordSql := "update t_execute_record set state=?, result=?, current_json=? where execute_id=?"
	_, err = db.ExecContext(l.ctx, recordSql, stateName, result, currentJsonStr, executeId)
	if err != nil {
		stateName = exceptionState
		result = "更新池账号占用状态发生异常"
		return
	}
}

func (l *QueryForDeleteAccountPoolEnvDataLogic) QueryForDeleteAccountPoolEnvData(req *types.QueryForDeleteAccountPoolEnvDataReq) (
	*types.QueryForDeleteAccountPoolEnvDataResp, error,
) {
	// 请求参数
	poolId := req.PoolId
	envId := req.EnvId

	if req.ExpectedCount <= 0 || req.ExpectedCount > 200000 {
		return nil, errors.New("单次最多只能筛选0~20万条数据")
	}

	// 判断账户池数据环境是否存在
	db, _ := l.svcCtx.DB.RawDB()
	selectSql := fmt.Sprintf(
		"" +
			"select t.table_name, c.column_id " +
			"from t_pool_table t, t_pool_column c " +
			"where t.parent_pool_id=c.pool_id and t.parent_pool_id=? " +
			"and t.id=? and c.primary_key='YES'",
	)
	rows, err := db.Query(selectSql, poolId, envId)
	if err != nil || rows == nil {
		return nil, errors.New(fmt.Sprintf("找不到pool_id为：「%d」和env_id为：「%d」的池账号环境或主键字段", poolId, envId))
	}
	columns, _ := rows.Columns()
	valueSlice := make([]any, 2)
	valuePtrSlice := make([]any, 2)

	var poolEnvName string
	var primaryColumnId string
	for rows.Next() {
		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			// value := valueSlice[0].([]uint8)
			poolEnvName = cast.ToString(valueSlice[0])
			// value2 := valueSlice[1].([]uint8)
			primaryColumnId = cast.ToString(valueSlice[1])
		}
	}
	if poolEnvName == "" || primaryColumnId == "" {
		return nil, errors.New(fmt.Sprintf("找不到pool_id为：「%d」和env_id为：「%d」的池账号环境或主键字段", poolId, envId))
	}

	// 生成执行id
	if req.ExecuteId == "" {
		currentTimeStamp := time.Now().UnixMilli()
		executeId := qetutils.GenNanoId(strconv.FormatInt(currentTimeStamp, 10))
		req.ExecuteId = executeId
	}

	loginUser := userinfo.FromContext(l.ctx)

	// 生成或更新操作流水
	recordSql := fmt.Sprintf(
		""+
			"INSERT INTO t_execute_record "+
			"(execute_id, pool_id, env_id, execute_way, "+
			"description, state, result, current_json, "+
			"created_by, updated_by) "+
			"VALUES "+
			"("+
			"'%s', %d, %d, '%s', "+
			"'%s', '%s', '%s', '%s', "+
			"'%s', '%s') "+
			"ON DUPLICATE KEY UPDATE "+
			"state='%s', result='%s', "+
			"updated_by='%s' ",
		req.ExecuteId, req.PoolId, req.EnvId, "DELETE_DATA",
		"None", "QUERYING", "None", "None",
		loginUser.Account, loginUser.Account,
		"QUERYING", "None",
		loginUser.Account,
	)
	recordSql = strings.ReplaceAll(recordSql, "'None',", "null,")
	recordSql = strings.ReplaceAll(recordSql, "None,", "null,")
	_, err = db.ExecContext(l.ctx, recordSql)
	if err != nil {
		return nil, errors.New("生成操作流水发生异常，请联系管理员")
	}

	// 开启协程
	go RealQueryPoolEnvDataForDelete(l, req, primaryColumnId)

	resp := &types.QueryForDeleteAccountPoolEnvDataResp{
		ExecuteId: req.ExecuteId,
	}
	return resp, nil
}
