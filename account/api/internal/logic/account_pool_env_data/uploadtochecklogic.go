package account_pool_env_data

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type UploadToCheckLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUploadToCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadToCheckLogic {
	return &UploadToCheckLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type UploadInterface interface {
	GetPoolEnvInfo()
	SaveAndGetExcelInfo()

	GetAndCheckExcelColumnIdList()

	FindAllColumnErrorInfo()
	GetColumnIndexLetterMap()
	GetColumnIdColumnModelMap()
	GetPrimaryColumnValueSet()

	JudgeToModifyExcel()
	GenerateSqlFileAndUpdateExecuteRecord()
}

type ExecuteRecordInterface interface {
	UpdateUploadExecuteRecord() error
	CommonErrorUpdateRecord(result string)
	UploadCommonErrorHandle(errMsg string)
}

type UploadToAddDataInterface interface {
	UploadInterface
	ExecuteRecordInterface

	GetOneRow(i int) (endFlag bool, columnValueMap map[string]string, rowErrorList []string)
	DealAddData()
	DealExcelSheet()

	Entrance()
}

type UploadToDeleteDataInterface interface {
	UploadInterface
	ExecuteRecordInterface

	GetOneRow(i int) (endFlag bool, columnValueMap map[string]string, rowErrorList []string)
	DealDeleteData()
	DealExcelSheet()

	Entrance()
}

type UploadToModifyDataInterface interface {
	UploadInterface
	ExecuteRecordInterface

	DealModifyDataPrimaryValue()
	IsNewRecord() bool
	UpdatePoolEnvData()
	UploadToModifyDataSpecialDeal() bool
	UploadRecoverEnvDataOccupyState()

	GetOneRow(i int) (endFlag bool, columnValueMap map[string]string, rowErrorList []string)
	DealModifyData()
	DealExcelSheet()

	Entrance()
}

type Upload struct {
	Logic *UploadToCheckLogic
	Req   *types.UploadToCheckReq

	EnvTableName    string
	EnvColumnList   []*model.TPoolColumn
	PrimaryColumnId string

	ExcelFileName          string
	ExcelPath              string
	ExcelFile              *excelize.File
	ExcelDataSheetName     string
	RowCount               int // excel数据行数
	ColumnCount            int // excel数据列数（不含"错误信息"列）
	AllPrimaryValueList    []string
	ExcelColumnIdList      []string
	ColumnIndexLetterMap   map[int]string
	ColumnIdColumnModelMap map[string]*model.TPoolColumn
	PrimaryColumnValueMap  map[string]string

	ValidateRowCount int
	OkCount          int
	SqlList          []string
	RowErrorMap      map[int]string
	Result           string

	SqlFileName string
}

func (upload *Upload) GetPoolEnvInfo() {
	logic := upload.Logic
	req := upload.Req
	envTable, _ := logic.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(logic.ctx, req.PoolId, req.EnvId)
	upload.EnvTableName = envTable.TableName
	envColumnList, err := logic.svcCtx.TPoolColumnModel.FindAllColumnByPoolId(logic.ctx, req.PoolId)
	if err != nil {
		upload.Logic.Logger.Error(
			fmt.Sprintf(
				"执行execute_id:「%s」获取账号池环境字段报错：「:%s」", upload.Req.ExecuteId, err,
			),
		)
		panic("获取账号池环境字段报错")
	} else {
		upload.EnvColumnList = envColumnList
		upload.PrimaryColumnId = envColumnList[0].ColumnId
	}
}

func (upload *Upload) SaveAndGetExcelInfo() {
	// 保存Excel文件
	saveExcelRequest := utils.SaveExcelRequest{}
	err := copier.Copy(&saveExcelRequest, upload.Req)
	if err != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("执行execute_id:「%s」拷贝excel数据文件报错:%s", upload.Req.ExecuteId, err))
		panic("拷贝excel数据文件报错")
	}
	excelFileName, excelPath, err := utils.SaveExcel(
		upload.Logic.svcCtx.Config.FileStore.ExcelPath, &saveExcelRequest,
	)
	if err != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("执行execute_id:「%s」保存excel文件报错:%s", upload.Req.ExecuteId, err))
		panic("保存excel文件报错")
	} else {
		upload.ExcelFileName = excelFileName
		upload.ExcelPath = excelPath
		excelFile, err := excelize.OpenFile(excelPath)
		if err != nil {
			upload.Logic.Logger.Error(
				fmt.Sprintf(
					"执行execute_id:「%s」打开excel文件「:%s」报错「:%s」", upload.Req.ExecuteId, excelPath, err,
				),
			)
			panic(fmt.Sprintf("打开excel文件「:%s」报错", excelPath))
		} else {
			sheetNameList := excelFile.GetSheetList()
			if len(sheetNameList) < 1 {
				panic("excel文件至少得有1个工作表")
			}
			upload.ExcelFile = excelFile
			upload.ExcelDataSheetName = sheetNameList[0]
		}
	}

	// 获取Excel信息
	rows, err := upload.ExcelFile.Rows(upload.ExcelDataSheetName)
	if err != nil {
		upload.Logic.Logger.Error(
			fmt.Sprintf(
				"执行execute_id:「%s」获取Excel文件「:%s」信息报错「:%s」", upload.Req.ExecuteId, excelPath, err,
			),
		)
		panic(fmt.Sprintf("获取Excel文件「%s」信息报错", excelPath))
	}

	// excel数据行数, 主键字段值
	var (
		allPrimaryValueList, notNullPrimaryValueList []string
	)
	cols, _ := upload.ExcelFile.Cols(upload.ExcelDataSheetName)

	// 处理第1列
	//if cols.Next() {
	//	firstColumnSlice, _ := cols.Rows()
	//	if firstColumnSlice[0] != upload.PrimaryColumnId {
	//		panic("excel中第1列必须是主键字段")
	//	}
	//	if upload.RowCount == 0 {
	//		upload.RowCount = len(firstColumnSlice) - 1
	//	}
	//	for _, columnValue := range firstColumnSlice[1:] {
	//		if columnValue != "" {
	//			notNullPrimaryValueList = append(notNullPrimaryValueList, columnValue)
	//		} else {
	//			columnValue = "*"
	//		}
	//		allPrimaryValueList = append(allPrimaryValueList, columnValue)
	//	}
	//}

	// 处理第1列
	columnIndex := 0
	for cols.Next() {
		if columnIndex != 0 {
			break
		}
		rows, _ := cols.Rows()
		if rows[0] != upload.PrimaryColumnId {
			panic("excel中第1列必须是主键字段")
		}
		if upload.RowCount == 0 {
			upload.RowCount = len(rows) - 1
		}
		for _, columnValue := range rows[1:] {
			if columnValue != "" {
				notNullPrimaryValueList = append(notNullPrimaryValueList, columnValue)
			} else {
				columnValue = "*"
			}
			allPrimaryValueList = append(allPrimaryValueList, columnValue)
		}
		columnIndex += 1
	}

	// excel主键值列表
	allPrimaryValueStr := strings.Join(allPrimaryValueList, "")
	allPrimaryValueStr = strings.TrimLeft(allPrimaryValueStr, "*")
	flag := strings.Contains(allPrimaryValueStr, "*")
	if flag || (len(notNullPrimaryValueList) != len(allPrimaryValueList)) {
		panic("导入数据不能有空行或重复行(第1列有重复值)")
	} else {
		upload.AllPrimaryValueList = allPrimaryValueList
	}

	// excel字段列数
	var columnCount int

	// 处理第1行
	if rows.Next() {
		columns, _ := rows.Columns()
		length := len(columns)
		if columns[length-1] == "错误信息" {
			columnCount = length - 1
		} else {
			columnCount = length
		}
	}
	upload.ColumnCount = columnCount
}

func (upload *Upload) GetAndCheckExcelColumnIdList() {
	rows, _ := upload.ExcelFile.Rows(upload.ExcelDataSheetName)

	// 获取excel表格字段
	for rows.Next() {
		excelColumnIdList, _ := rows.Columns()
		upload.ExcelColumnIdList = excelColumnIdList
		break
	}
	var errorColumnIndexList []string
	for index, columnId := range upload.ExcelColumnIdList {
		if columnId == "" {
			s := fmt.Sprintf("第%d列", index)
			errorColumnIndexList = append(errorColumnIndexList, s)
		}
	}
	errorColumnIndexStr := strings.Join(errorColumnIndexList, "、")
	if errorColumnIndexStr != "" {
		result := fmt.Sprintf("{%s}数据不合法，删除字段列请用excel自带的鼠标右键删除这种方法", errorColumnIndexStr)
		upload.CommonErrorUpdateRecord(result)
		panic(result)
	}
}

func (upload *Upload) QuickFindColumnErrorInfo() {
	// 简单检查excel字段错误，包含字段列和第1行数据校验， 目的是为了尽快解除池账号环境数据占用

	var errorMsgList []string

	retainColumnIdList := []string{
		"create_time", "acquire_time", "return_time", "cooling_time", "occupy_state", "related_execute_id",
	}
	retainColumnIdMap := make(map[string]string)
	for _, columnId := range retainColumnIdList {
		retainColumnIdMap[columnId] = columnId
	}
	envColumnMap := make(map[string]*model.TPoolColumn)
	for _, envColumn := range upload.EnvColumnList {
		envColumnMap[envColumn.ColumnId] = envColumn
	}
	for index, columnId := range upload.ExcelColumnIdList {
		_, ok := retainColumnIdMap[columnId]
		if ok {
			errorMsgList = append(errorMsgList, fmt.Sprintf("「%s」为保留字段，不允许导入", columnId))
			continue
		}
		poolColumn, ok := envColumnMap[columnId]
		if !ok {
			errorMsgList = append(errorMsgList, fmt.Sprintf("字段：「%s」不存在，不能导入", columnId))
		} else {
			columnIsUsing := poolColumn.ColumnIsUsing
			if columnIsUsing == "NO" {
				errorMsgList = append(errorMsgList, fmt.Sprintf("「%s」已经不可用，不能导入", columnId))
			} else {
				columnAllowNull := poolColumn.ColumnAllowNull
				columnLetter, _ := excelize.ColumnNumberToName(index + 1)
				axis, _ := excelize.JoinCellName(columnLetter, 2)
				firstDataRowColumnValue, _ := upload.ExcelFile.GetCellValue(upload.ExcelDataSheetName, axis)
				if columnAllowNull == "NO" && firstDataRowColumnValue == "" {
					errorMsgList = append(errorMsgList, fmt.Sprintf("字段：「%s」不能导入空值", columnId))
				}
			}
		}
	}

	// 字段有错误，整个流程结束
	if len(errorMsgList) > 0 {
		l := upload.Logic
		db, _ := l.svcCtx.DB.RawDB()
		errorStr := strings.Join(errorMsgList, ";")
		// currentTimeStamp := time.Now().UnixMilli()
		recordSql := fmt.Sprintf(
			"update t_execute_record set state='EXCEPTION', result='%s' where execute_id='%s'",
			errorStr, upload.Req.ExecuteId,
		)
		_, err := db.ExecContext(l.ctx, recordSql)
		if err != nil {
			panic(fmt.Sprintf("执行sql:%s发生异常：%s", recordSql, err))
		} else {
			panic(fmt.Sprintf("解析上传的excel文件发现以下错误：{%s}", errorStr))
		}
	}
}

func (upload *Upload) GetColumnIndexLetterMap() {
	columnIndexLetterMap := make(map[int]string)

	rows, _ := upload.ExcelFile.Rows(upload.ExcelDataSheetName)
	var count int
	for rows.Next() {
		row, _ := rows.Columns()
		count = len(row)
		break
	}
	for i := 0; i < count; i++ {
		letter, _ := excelize.ColumnNumberToName(i + 1)
		columnIndexLetterMap[i+1] = letter
	}

	upload.ColumnIndexLetterMap = columnIndexLetterMap
}

func (upload *Upload) GetColumnIdColumnModelMap() {
	l := upload.Logic

	columnIdColumnModelMap := make(map[string]*model.TPoolColumn)
	poolColumns, err := l.svcCtx.TPoolColumnModel.FindAllColumnByPoolId(l.ctx, upload.Req.PoolId)
	if err != nil {
		panic(err.Error())
	}
	poolColumnMap := make(map[string]*model.TPoolColumn)
	for _, poolColumn := range poolColumns {
		poolColumnMap[poolColumn.ColumnId] = poolColumn
	}
	for _, columnId := range upload.ExcelColumnIdList {
		column, ok := poolColumnMap[columnId]
		if ok {
			columnIdColumnModelMap[columnId] = column
		}
	}
	upload.ColumnIdColumnModelMap = columnIdColumnModelMap
}

func (upload *Upload) GetPrimaryColumnValueSet() {
	l := upload.Logic

	primaryColumnValueMap := make(map[string]string)

	db, _ := l.svcCtx.DB.RawDB()
	selectPrimaryIdSql := fmt.Sprintf("select %s primaryColumn from %s", upload.PrimaryColumnId, upload.EnvTableName)
	rows, _ := db.QueryContext(l.ctx, selectPrimaryIdSql)
	columns, _ := rows.Columns()
	count := len(columns)
	valueSlice := make([]any, count)
	valuePtrSlice := make([]any, count)
	for rows.Next() {
		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			// value := valueSlice[0].([]uint8)
			valueStr := cast.ToString(valueSlice[0]) // 主键一定是varchar类型
			primaryColumnValueMap[valueStr] = valueStr
		}
	}
	upload.PrimaryColumnValueMap = primaryColumnValueMap
}

func (upload *Upload) JudgeToModifyExcel() {
	errorColumnExcelIndex := upload.ColumnCount + 1

	// 修改excel文件（先删除作物信息列，然后再写入这一列）
	// "错误信息" 列索引，不存在也可以删除这一列
	letter, _ := excelize.ColumnNumberToName(errorColumnExcelIndex)
	removeErr := upload.ExcelFile.RemoveCol(upload.ExcelDataSheetName, letter)
	if removeErr != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("excel删除移除`错误信息`列发生异常：%s", removeErr))
		panic("excel删除移除`错误信息`列发生异常")
	}
	axis, _ := excelize.JoinCellName(letter, 1)
	err := upload.ExcelFile.SetCellValue(upload.ExcelDataSheetName, axis, "错误信息")
	if err != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("excel写入`错误信息`发生异常：%s", err))
		panic("excel写入`错误信息`列发生异常")
	}
	for rowIndex, value := range upload.RowErrorMap {
		axis_, _ := excelize.JoinCellName(letter, rowIndex)
		err := upload.ExcelFile.SetCellValue(upload.ExcelDataSheetName, axis_, value)
		if err != nil {
			upload.Logic.Logger.Error(fmt.Sprintf("excel写入`错误信息`列发生异常：%s", err))
			panic("excel写入`错误信息`列发生异常")
		}
	}
}

func (upload *Upload) GenerateSqlFileAndUpdateExecuteRecord() error {
	var err error

	l := upload.Logic
	req := upload.Req

	// 将错误信息吸入excel
	if len(upload.RowErrorMap) > 0 {
		upload.JudgeToModifyExcel()
	}

	if len(upload.SqlList) > 0 {
		// 生成sql文件名称
		sqlFileName := fmt.Sprintf(
			"%d_%d_%s_%s.sql",
			req.PoolId, req.EnvId, req.ExecuteId, common.PoolEnvDataUploadPurposeName[req.Purpose],
		)
		upload.SqlFileName = sqlFileName

		// 保存sql文件
		sqlPath := path.Join(l.svcCtx.Config.FileStore.SqlPath, sqlFileName)
		f, _ := os.OpenFile(sqlPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0o755)
		for _, sql := range upload.SqlList {
			_, _ = f.WriteString(sql)
			_, _ = f.WriteString("\r\n")
		}
		f.Close()

		// 更新上传操作流水
		err = upload.UpdateUploadExecuteRecord()
	} else {
		err = upload.UpdateUploadExecuteRecord()
	}

	return err
}

func (upload *Upload) CommonErrorUpdateRecord(result string) {
	db, _ := upload.Logic.svcCtx.DB.RawDB()

	resultRune := []rune(result)
	resultRuneLength := len(resultRune)
	if resultRuneLength < 255 {
		result = string(resultRune[0:resultRuneLength])
	} else {
		result = string(resultRune[0:255])
	}
	updateRecordSql := fmt.Sprintf(
		"update t_execute_record set state='EXCEPTION',result='%s' where execute_id='%s'",
		result, upload.Req.ExecuteId,
	)
	_, err := db.ExecContext(upload.Logic.ctx, updateRecordSql)
	if err != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("[CommonErrorUpdateRecord]执行sql:%s发生异常：%s", updateRecordSql, err))
	}
}

func (upload *Upload) UploadCommonErrorHandle(result string) {
	// 修改流水的current_json字段
	way := fmt.Sprintf("UPLOAD_TO_%s", common.PoolEnvDataUploadPurposeName[upload.Req.Purpose])

	currentJsonObj := utils.PoolEnvDataExecCurrentJson{
		ExcelFileName: upload.ExcelFileName,
		Way:           way,
	}
	jsons, _ := json.Marshal(currentJsonObj)
	currentJsonStr := string(jsons)

	recordSql := "Update `t_execute_record` set current_json=? where execute_id=?"

	db, _ := upload.Logic.svcCtx.DB.RawDB()
	_, err := db.ExecContext(upload.Logic.ctx, recordSql, currentJsonStr, upload.Req.ExecuteId)
	if err != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("[UploadCommonErrorHandle]执行sql:%s发生异常：%s", recordSql, err))
	} else {
		// 需要将数据改为「未使用」
		upload.CommonErrorUpdateRecord(result)
	}
}

func (upload *Upload) UpdateUploadExecuteRecord() error {
	way := fmt.Sprintf("UPLOAD_TO_%s", common.PoolEnvDataUploadPurposeName[upload.Req.Purpose])

	// current json
	currentJsonObj := utils.PoolEnvDataExecCurrentJson{
		ExcelFileName: upload.ExcelFileName,
		SqlFileName:   upload.SqlFileName,
		ExpectedCount: int64(upload.ValidateRowCount),
		OkCount:       int64(upload.OkCount),
		Way:           way,
	}

	l := upload.Logic

	// 判断是否为筛选修改的重新上传excel
	record, err := l.svcCtx.TExecuteRecordModel.FindByExecuteId(l.ctx, upload.Req.ExecuteId)
	if err != nil {
		return err
	}
	if record != nil {
		if record.CurrentJson.String != "" {
			var existCurrentJsonObj utils.PoolEnvDataExecCurrentJson
			err := json.Unmarshal([]byte(record.CurrentJson.String), &existCurrentJsonObj)
			if err != nil {
				return err
			} else {
				currentJsonObj.Way = existCurrentJsonObj.Way
				currentJsonObj.QuerySql = existCurrentJsonObj.QuerySql
			}
		}
	}

	b, err := json.Marshal(currentJsonObj)
	if err != nil {
		return err
	}
	currentJsonStr := string(b)
	// currentJsonStr := strconv.Quote(currentJsonStr0)

	// 更新操作流水
	var stateStr string
	if upload.OkCount > 0 {
		stateStr = "DETECTED"
	} else {
		stateStr = "NONE_VALID_DATA"
	}

	// currentTimeStamp := time.Now().UnixMilli()
	recordSql := "update t_execute_record set state=?, result=?, current_json=? where execute_id=?"
	db, _ := l.svcCtx.DB.RawDB()
	_, err = db.ExecContext(l.ctx, recordSql, stateStr, upload.Result, currentJsonStr, upload.Req.ExecuteId)

	return err
}

type UploadToAddData struct {
	Upload
}

func (upload *UploadToAddData) GetOneRow(i int) (
	endFlag bool, columnValueMap map[string]string, rowErrorList []string,
) {
	columnValueMap = make(map[string]string)

	// 解析1行excel数据
	for j := 1; j < upload.ColumnCount+1; j++ {
		columnId := upload.ExcelColumnIdList[j-1]
		columnLetter := upload.ColumnIndexLetterMap[j]
		axis, _ := excelize.JoinCellName(columnLetter, i)
		columnValue, _ := upload.ExcelFile.GetCellValue(upload.ExcelDataSheetName, axis)
		if (j == 1) && (columnValue == "") {
			// 主键字段值为空的直接结束，即跳过上传的excel最后的N行（空白行）
			endFlag = true
			break
		}
		columnModel := upload.ColumnIdColumnModelMap[columnId]
		column := &types.CreateAccountPoolColumnReq{
			PoolId:          columnModel.PoolId,
			ColumnId:        columnModel.ColumnId,
			ColumnName:      columnModel.ColumnName,
			ColumnType:      common.ColumnTypeValue[columnModel.ColumnType],
			ColumnLength:    columnModel.ColumnLength.Int64,
			ColumnDefault:   columnModel.ColumnDefault.String,
			ColumnAllowNull: common.YesOrNoValue[columnModel.ColumnAllowNull],
			ColumnIsVisible: common.YesOrNoValue[columnModel.ColumnIsVisible],
			ColumnComment:   columnModel.ColumnComment.String,
		}
		flag, retValue := utils.CheckColumnDml(column, columnValue)
		if flag {
			// 新增数据的第1个字段（主键）存在于数据库
			_, ok := upload.PrimaryColumnValueMap[retValue]
			if (j == 1) && ok {
				rowErrorList = append(
					rowErrorList, fmt.Sprintf("主键值[%s]已经存在于数据库，不能新增重复的记录", retValue),
				)
				break
			} else {
				columnValueMap[columnId] = retValue
			}
		} else {
			rowErrorList = append(rowErrorList, retValue)
		}
	}
	return endFlag, columnValueMap, rowErrorList
}

func (upload *UploadToAddData) DealAddData() {
	var (
		validateRowCount = upload.RowCount
		sqlList          []string
		rowErrorMap      map[int]string
		result           string
	)

	rowErrorMap = make(map[int]string)

	var rowValueList []string

	currentTimeStamp := time.Now().UnixMilli()

	var endFlag bool

	for i := 2; i <= upload.RowCount+1; i++ {
		if endFlag {
			validateRowCount = i - 2 - 1
			break
		}
		// var columnValueMap = make(map[string]string)
		// var rowErrorList []string

		// 解析1行excel数据
		endFlag, columnValueMap, rowErrorList := upload.GetOneRow(i)

		// 1行记录所有字段都符合规范
		if len(columnValueMap) == len(upload.ExcelColumnIdList) {
			var columnValues []string
			for _, columnId := range upload.ExcelColumnIdList {
				value := fmt.Sprintf("'%s'", columnValueMap[columnId])
				columnValues = append(columnValues, value)
			}
			columnStr := strings.Join(columnValues, ",")
			rowValue := fmt.Sprintf("(%d, %s)", currentTimeStamp, columnStr)
			rowValueList = append(rowValueList, rowValue)
		} else {
			rowErrorMap[i] = strings.Join(rowErrorList, ";")
		}

		if endFlag {
			break
		}
	}

	addCount := len(rowValueList)

	// 增加的字段个数越多，步长应该设置越小，避免单个sql过大而超过了mysql设置的max_allowed_packet
	var step int
	if upload.ColumnCount/5 >= 2 { // 字段个数 >= 10
		step = 100 // step = 20
	} else if upload.ColumnCount/5 == 1 { // 5 <= 字段个数 < 10
		step = 500 // step = 30
	} else { // 字段个数 < 5
		step = 1000
	}

	start := 0
	end := step
	columnStr := strings.Join(upload.ExcelColumnIdList, ", ")
	for {
		if start >= addCount {
			break
		}
		if end > addCount {
			end = addCount
		}
		valueList := rowValueList[start:end]

		valueStr := strings.Join(valueList, ",")
		sql := fmt.Sprintf("insert into %s (create_time, %s) values %s;", upload.EnvTableName, columnStr, valueStr)
		sql = strings.ReplaceAll(sql, "'None',", "null,")
		sql = strings.ReplaceAll(sql, "None,", "null,")
		sqlList = append(sqlList, sql)
		start += step
		end += step
	}
	if len(sqlList) == 0 {
		result = "excel中没有任何可以新增的记录"
	} else {
		result = "检查完上传的excel数据"
	}
	upload.ValidateRowCount = validateRowCount
	upload.OkCount = addCount
	upload.SqlList = sqlList
	upload.RowErrorMap = rowErrorMap
	upload.Result = result
}

func (upload *UploadToAddData) DealExcelSheet() {
	// 从数据库加载所有的主键值到一个set中
	upload.GetPrimaryColumnValueSet()
	upload.DealAddData()
}

func (upload *UploadToAddData) Entrance() {
	defer func() {
		var err error
		if upload.ExcelPath != "" {
			if err = upload.ExcelFile.SaveAs(upload.ExcelPath); err != nil {
				upload.Logic.Logger.Error(fmt.Sprintf("关闭excel文件:%s发生异常：%s", upload.ExcelPath, err))
			}
		}
	}()

	{ // code block first
		defer func() {
			if err := recover(); err != nil {
				result := fmt.Sprintf("%s", err)
				upload.UploadCommonErrorHandle(result)
			}
		}()

		// 获取账户池环境信息
		upload.GetPoolEnvInfo()
		// 保存excel文件并获取excel信息
		upload.SaveAndGetExcelInfo()
	}

	{ // code block second
		defer func() {
			if err := recover(); err != nil {
				upload.Logic.Logger.Error(err)
			}
		}()

		// 获取并检查excel_column_id_list, 即（excel表头)
		upload.GetAndCheckExcelColumnIdList()
	}

	{ // code block third
		defer func() {
			if err := recover(); err != nil {
				result := fmt.Sprintf("%s", err)
				upload.CommonErrorUpdateRecord(result)
			}
		}()
		// 快速找出excel表头和第1行数据字段错误信息
		upload.QuickFindColumnErrorInfo()
		// 字段索引与excel字母关系
		upload.GetColumnIndexLetterMap()
		// column_id与*model.TPoolColumn映射关系
		upload.GetColumnIdColumnModelMap()

		// 处理excel工作表数据
		upload.DealExcelSheet()
		err := upload.GenerateSqlFileAndUpdateExecuteRecord()
		if err != nil {
			panic(err.Error())
		}
	}
}

type UploadToDeleteData struct {
	Upload
}

func (upload *UploadToDeleteData) GetOneRow(i int) (
	endFlag bool, columnValueMap map[string]string, rowErrorList []string,
) {
	columnValueMap = make(map[string]string)

	// 解析1行excel数据

	for j := 1; j < upload.ColumnCount+1; j++ {
		columnId := upload.ExcelColumnIdList[j-1]
		columnLetter := upload.ColumnIndexLetterMap[j]
		axis, _ := excelize.JoinCellName(columnLetter, i)
		columnValue, _ := upload.ExcelFile.GetCellValue(upload.ExcelDataSheetName, axis)
		if (j == 1) && (columnValue == "") {
			// 主键字段值为空的直接结束，即跳过上传的excel最后的N行（空白行）
			endFlag = true
			break
		}
		columnModel := upload.ColumnIdColumnModelMap[columnId]
		column := &types.CreateAccountPoolColumnReq{
			PoolId:          columnModel.PoolId,
			ColumnId:        columnModel.ColumnId,
			ColumnName:      columnModel.ColumnName,
			ColumnType:      common.ColumnTypeValue[columnModel.ColumnType],
			ColumnLength:    columnModel.ColumnLength.Int64,
			ColumnDefault:   columnModel.ColumnDefault.String,
			ColumnAllowNull: common.YesOrNoValue[columnModel.ColumnAllowNull],
			ColumnIsVisible: common.YesOrNoValue[columnModel.ColumnIsVisible],
			ColumnComment:   columnModel.ColumnComment.String,
		}
		flag, retValue := utils.CheckColumnDml(column, columnValue)
		if flag {
			// 新增数据的第1个字段（主键）存在于数据库
			_, ok := upload.PrimaryColumnValueMap[retValue]
			if (j == 1) && !ok {
				rowErrorList = append(rowErrorList, fmt.Sprintf("数据库中找不到主键值为[%s]的记录", retValue))
				break
			} else {
				columnValueMap[columnId] = retValue
			}
		} else {
			rowErrorList = append(rowErrorList, retValue)
		}
	}
	return endFlag, columnValueMap, rowErrorList
}

func (upload *UploadToDeleteData) DealDeleteData() {
	var (
		validateRowCount = upload.RowCount
		sqlList          []string
		rowErrorMap      map[int]string
	)

	rowErrorMap = make(map[int]string)

	var primaryColumnValueList []string

	primaryColumnId := upload.ExcelColumnIdList[0]

	for i := 2; i <= upload.RowCount+1; i++ {
		// var columnValueMap = make(map[string]string)
		// var rowErrorList []string

		// 解析1行excel数据
		endFlag, columnValueMap, rowErrorList := upload.GetOneRow(i)

		if endFlag {
			validateRowCount = i - 2 - 1
			break
		}

		// 1行记录所有字段都符合规范
		if len(columnValueMap) == upload.ColumnCount {
			primaryColumnValue := fmt.Sprintf("'%s'", columnValueMap[primaryColumnId])
			primaryColumnValueList = append(primaryColumnValueList, primaryColumnValue)
		} else {
			rowErrorMap[i] = strings.Join(rowErrorList, ";")
		}
	}

	deleteCount := len(primaryColumnValueList)

	step := 1000
	start := 0
	end := step
	for {
		if start >= deleteCount {
			break
		}
		if end > deleteCount {
			end = deleteCount
		}
		valueList := primaryColumnValueList[start:end]
		valueStr := strings.Join(valueList, ",")
		sql := fmt.Sprintf("delete from %s where %s in (%s);", upload.EnvTableName, primaryColumnId, valueStr)
		sql = strings.ReplaceAll(sql, "'None',", "null,")
		sql = strings.ReplaceAll(sql, "None,", "null,")
		sqlList = append(sqlList, sql)
		start += step
		end += step
	}

	var result string
	if len(sqlList) == 0 {
		result = "excel中没有任何可以删除的记录"
	} else {
		result = "检查完上传的excel数据"
	}

	upload.ValidateRowCount = validateRowCount
	upload.OkCount = deleteCount
	upload.SqlList = sqlList
	upload.RowErrorMap = rowErrorMap
	upload.Result = result
}

func (upload *UploadToDeleteData) DealExcelSheet() {
	// 从数据库加载所有的主键值到一个set中
	upload.GetPrimaryColumnValueSet()

	if len(upload.PrimaryColumnValueMap) == 0 {
		upload.OkCount = 0
		upload.Result = "数据库不存在任何记录，操作失败"
		return
	}

	upload.DealDeleteData()
}

func (upload *UploadToDeleteData) Entrance() {
	defer func() {
		var err error
		if upload.ExcelPath != "" {
			if err = upload.ExcelFile.SaveAs(upload.ExcelPath); err != nil {
				upload.Logic.Logger.Error(fmt.Sprintf("关闭excel文件发生异常：「%s」", err))
			}
		}
	}()

	{ // code block first
		defer func() {
			if err := recover(); err != nil {
				result := fmt.Sprintf("%s", err)
				upload.UploadCommonErrorHandle(result)
			}
		}()

		// 获取账户池环境信息
		upload.GetPoolEnvInfo()
		// 保存excel文件并获取excel信息
		upload.SaveAndGetExcelInfo()
	}

	{ // code block second
		defer func() {
			if err := recover(); err != nil {
				upload.Logic.Logger.Error(err)
			}
		}()

		// 获取并检查excel_column_id_list, 即（excel表头)
		upload.GetAndCheckExcelColumnIdList()
	}

	{ // code block third
		defer func() {
			if err := recover(); err != nil {
				result := fmt.Sprintf("%s", err)
				upload.CommonErrorUpdateRecord(result)
			}
		}()
		// 快速找出excel表头和第1行数据字段错误信息
		upload.QuickFindColumnErrorInfo()
		// 字段索引与excel字母关系
		upload.GetColumnIndexLetterMap()
		// column_id与*model.TPoolColumn映射关系
		upload.GetColumnIdColumnModelMap()

		// 处理excel工作表数据
		upload.DealExcelSheet()
		err := upload.GenerateSqlFileAndUpdateExecuteRecord()
		if err != nil {
			panic(err.Error())
		}
	}
}

type UploadToModifyData struct {
	Upload
	BackupExcelPath string
	BackupExcelFile *excelize.File
}

func (upload *UploadToModifyData) DealModifyDataPrimaryValue() {
	l := upload.Logic

	newRecordFlag := upload.IsNewRecord()

	var selectPrimaryIdSql string
	if newRecordFlag { // 新开流水上传的excel, 修改的数据可以为"未使用"或者之前筛选出来的"待更新"数据
		selectPrimaryIdSql = fmt.Sprintf(
			"select %s as primaryColumn from %s where occupy_state in ('UNUSED', 'TO_BE_UPDATE')",
			upload.PrimaryColumnId, upload.EnvTableName,
		)
	} else { // 在筛选修改的流水继续上传的excel，上传数据必须是之前筛选出来的"待更新"数据的子集
		selectPrimaryIdSql = fmt.Sprintf(
			"select %s as primaryColumn from %s where occupy_state in ('TO_BE_UPDATE')",
			upload.PrimaryColumnId, upload.EnvTableName,
		)
	}
	db, _ := l.svcCtx.DB.RawDB()
	rows, err := db.QueryContext(l.ctx, selectPrimaryIdSql)
	if err != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("查询待更新账号数据发生数据库异常：%s", err))
		panic("查询待更新账号数据发生数据库异常")
	}
	columns, _ := rows.Columns()
	count := len(columns)
	valueSlice := make([]any, count)
	valuePtrSlice := make([]any, count)
	primaryColumnValueMap := make(map[string]string)
	for rows.Next() {
		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			// value := valueSlice[0].([]uint8)
			primaryColumnValue := cast.ToString(valueSlice[0]) // // 主键一定是varchar类型
			primaryColumnValueMap[primaryColumnValue] = primaryColumnValue
		}
	}

	rowPrimaryColumnErrorMap := make(map[int]string)
	for rowIndex, primaryValue := range upload.AllPrimaryValueList {
		_, ok := primaryColumnValueMap[primaryValue]
		if !ok {
			rowPrimaryColumnErrorMap[rowIndex+2] = fmt.Sprintf(
				"主键值为「%s」的账号数据不存在或者不是可修改状态", primaryValue,
			)
		}
	}
	upload.RowErrorMap = rowPrimaryColumnErrorMap
}

func (upload *UploadToModifyData) IsNewRecord() bool {
	record, err := upload.Logic.svcCtx.TExecuteRecordModel.FindByExecuteId(upload.Logic.ctx, upload.Req.ExecuteId)
	if err != nil {
		upload.Logic.Logger.Error(
			fmt.Sprintf(
				"查询执行记录execute_id:「%s」发生数据库异常：%s", upload.Req.ExecuteId, err,
			),
		)
		panic("查询执行记录发生数据库异常")
	}
	if record != nil && record.CurrentJson.String != "" {
		return false
	}
	return true
}

func (upload *UploadToModifyData) UpdatePoolEnvData() {
	l := upload.Logic
	req := upload.Req

	err := l.svcCtx.TPoolTableModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			var sqlList []string
			dataCount := len(upload.AllPrimaryValueList)

			step := 1000
			start := 0
			end := step

			for {
				if start >= dataCount {
					break
				}
				if end > dataCount {
					end = dataCount
				}
				primaryValueSlice_ := upload.AllPrimaryValueList[start:end]
				var primaryValueSlice []string
				for _, v := range primaryValueSlice_ {
					primaryValueSlice = append(primaryValueSlice, fmt.Sprintf("'%s'", v))
				}
				valueStr := strings.Join(primaryValueSlice, ",")

				sql := fmt.Sprintf(
					""+
						"update %s set related_execute_id='%s',occupy_state='%s' where `%s` in (%s)",
					upload.EnvTableName, req.ExecuteId, "TO_BE_UPDATE", upload.PrimaryColumnId, valueStr,
				)
				sql = strings.ReplaceAll(sql, "'None',", "null,")
				sql = strings.ReplaceAll(sql, "None,", "null,")
				sqlList = append(sqlList, sql)
				start += step
				end += step
			}
			sqlConn := sqlx.NewMysql(l.svcCtx.Config.DB.DataSource)
			poolEnvModel := model.NewPoolEnvTableModel(sqlConn, upload.EnvTableName, upload.ExcelColumnIdList)
			for _, updateSql := range sqlList {
				_, err := poolEnvModel.ExecSqlTX(l.ctx, session, updateSql)
				if err != nil {
					return err
				}
			}
			return nil
		},
	)
	if err != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("将关联的账号池数据更改为'待更新'状态发生错误:%s", err))
		panic("将关联的账号池数据更改为'待更新'状态发生错误")
	}
}

func (upload *UploadToModifyData) UploadToModifyDataSpecialDeal() bool {
	l := upload.Logic
	req := upload.Req

	// 流水记录
	record, err1 := l.svcCtx.TExecuteRecordModel.FindByExecuteId(l.ctx, req.ExecuteId)
	if err1 != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("查询流水记录发生异常:%s", err1))
		panic(fmt.Sprintf("查询流水记录「%s」发生异常", req.ExecuteId))
	}
	currentJsonString := record.CurrentJson.String
	var currentJsonObj utils.PoolEnvDataExecCurrentJson
	err2 := json.Unmarshal([]byte(currentJsonString), &currentJsonObj)
	if err2 != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("转换currentJsnn发生异常:%s", err2))
		panic("转换currentJson发生异常")
	}

	// 准备解析excel
	excelDirPath := upload.Logic.svcCtx.Config.FileStore.ExcelPath
	backupExcelPath, err3 := utils.GetBackupExcelPath(excelDirPath, currentJsonObj.ExcelFileName)
	if err3 != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("打开备份的Excel文件:「%s」发生异常:%s", backupExcelPath, err3))
		panic("打开备份的Excel文件发生异常")
	}
	upload.BackupExcelPath = backupExcelPath
	backupExcelFile, err4 := excelize.OpenFile(backupExcelPath)
	if err4 != nil {
		upload.Logic.Logger.Error(fmt.Sprintf("打开备份的Excel文件:「%s」发生异常:%s", backupExcelPath, err4))
		panic("打开备份的Excel文件发生异常")
	}
	upload.BackupExcelFile = backupExcelFile

	// 计算excel行数和列数
	backupSheetNameList := backupExcelFile.GetSheetList()
	dataBackupSheetName := backupSheetNameList[0]
	backupRows, _ := backupExcelFile.Rows(dataBackupSheetName)
	var backupColumnCount int
	for backupRows.Next() {
		backupRow, _ := backupRows.Columns()
		backupColumnCount = len(backupRow)
		break
	}
	backupCols, _ := backupExcelFile.Cols(upload.ExcelDataSheetName)
	var backupRowCount int
	for backupCols.Next() {
		backupCol, _ := backupCols.Rows()
		backupRowCount = len(backupCol) - 1
		// backupRowCount = len(backupCol)
		break
	}

	errorFlag := false
	okCount := currentJsonObj.OkCount
	if (okCount != int64(backupRowCount)) || (upload.ColumnCount != backupColumnCount) {
		errorFlag = true
	} else {
		for i := 2; i < backupRowCount; i++ {
			axis, _ := excelize.JoinCellName("A", i)
			originPrimaryValue, _ := upload.ExcelFile.GetCellValue(upload.ExcelDataSheetName, axis)
			newPrimaryValue, _ := backupExcelFile.GetCellValue(dataBackupSheetName, axis)
			if originPrimaryValue != newPrimaryValue {
				errorFlag = true
				break
			}
		}
	}

	if errorFlag {
		// 更新操作流水
		errorInfo := "上传的excel文件跟筛选出来的excel文件行数、列数或主键值至少有1个不一致"
		db, _ := l.svcCtx.DB.RawDB()
		// currentTimeStamp := time.Now().UnixMilli()
		recordSql := fmt.Sprintf(
			"update t_execute_record set state='UNCHECKED', result='%s' where execute_id='%s'",
			errorInfo, req.ExecuteId,
		)
		_, err := db.ExecContext(l.ctx, recordSql)
		if err != nil {
			upload.Logic.Logger.Error(fmt.Sprintf("更新执行记录为UNCHECKED发生异常:%s", err))
			panic("更新执行记录为UNCHECKED发生异常")
		}
	}
	return errorFlag
}

func (upload *UploadToModifyData) UploadRecoverEnvDataOccupyState() {
	// 上传excel后流水未能变为"已检测"状态，需要讲账号池中的数据恢复为"未使用状态"
	l := upload.Logic
	req := upload.Req

	record, err := upload.Logic.svcCtx.TExecuteRecordModel.FindByExecuteId(upload.Logic.ctx, req.ExecuteId)
	if err != nil || record == nil {
		l.Logger.Error(fmt.Sprintf("找不到execute_id为：「%s」的流水记录", req.ExecuteId))
		return
	}
	state := record.State

	if state == "EXCEPTION" || state == "NONE_VALID_DATA" {
		updateSql := fmt.Sprintf(
			""+
				"update %s set occupy_state='UNUSED' where related_execute_id='%s' and occupy_state != 'USING'",
			upload.EnvTableName, req.ExecuteId,
		)
		db, _ := l.svcCtx.DB.RawDB()
		_, err := db.ExecContext(l.ctx, updateSql)
		if err != nil {
			l.Logger.Error(fmt.Sprintf("执行sql：「%s」发生异常：「%s」", updateSql, err))
		}
	}
}

func (upload *UploadToModifyData) GetOneRow(i int) (
	endFlag bool, columnValueMap map[string]string, rowErrorList []string,
) {
	columnValueMap = make(map[string]string)

	// 解析1行excel数据
	for j := 1; j < upload.ColumnCount+1; j++ {
		columnId := upload.ExcelColumnIdList[j-1]
		columnLetter := upload.ColumnIndexLetterMap[j]
		axis, _ := excelize.JoinCellName(columnLetter, i)
		columnValue, _ := upload.ExcelFile.GetCellValue(upload.ExcelDataSheetName, axis)
		if (j == 1) && (columnValue == "") {
			// 主键字段值为空的直接结束，即跳过上传的excel最后的N行（空白行）
			endFlag = true
			break
		}
		columnModel := upload.ColumnIdColumnModelMap[columnId]
		column := &types.CreateAccountPoolColumnReq{
			PoolId:          columnModel.PoolId,
			ColumnId:        columnModel.ColumnId,
			ColumnName:      columnModel.ColumnName,
			ColumnType:      common.ColumnTypeValue[columnModel.ColumnType],
			ColumnLength:    columnModel.ColumnLength.Int64,
			ColumnDefault:   columnModel.ColumnDefault.String,
			ColumnAllowNull: common.YesOrNoValue[columnModel.ColumnAllowNull],
			ColumnIsVisible: common.YesOrNoValue[columnModel.ColumnIsVisible],
			ColumnComment:   columnModel.ColumnComment.String,
		}
		flag, retValue := utils.CheckColumnDml(column, columnValue)
		if flag {
			// 数据的第1个字段（主键）存在于数据库
			_, ok := upload.PrimaryColumnValueMap[retValue]
			if (j == 1) && !ok {
				rowErrorList = append(rowErrorList, fmt.Sprintf("数据库中找不到主键值为[%s]的记录", retValue))
				break
			} else {
				columnValueMap[columnId] = retValue
			}
		} else {
			rowErrorList = append(rowErrorList, retValue)
		}
	}
	return endFlag, columnValueMap, rowErrorList
}

func (upload *UploadToModifyData) DealModifyData() {
	var (
		validateRowCount = upload.RowCount
		sqlList          []string
		rowErrorMap      map[int]string
	)

	rowErrorMap = make(map[int]string)

	req := upload.Req

	primaryColumnId := upload.PrimaryColumnId

	setColumnValueStrMap := make(map[string][]string)

	for i := 2; i <= upload.RowCount+1; i++ {
		// var columnValueMap = make(map[string]string)
		// var rowErrorList []string

		// 解析1行excel数据
		endFlag, columnValueMap, rowErrorList := upload.GetOneRow(i)

		if endFlag {
			validateRowCount = i - 2 - 1
			break
		}

		// 1行记录所有字段都符合规范
		if len(columnValueMap) == len(upload.ExcelColumnIdList) {
			var setStrList []string
			for _, k := range upload.ExcelColumnIdList {
				if k != primaryColumnId {
					setStrList = append(setStrList, fmt.Sprintf("%s='%s'", k, columnValueMap[k]))
				}
			}
			setColumnValueStr := strings.Join(setStrList, ",")

			primaryColumnValue := fmt.Sprintf("'%s'", columnValueMap[primaryColumnId])
			_, ok := setColumnValueStrMap[setColumnValueStr]
			if !ok {
				var l []string
				setColumnValueStrMap[setColumnValueStr] = l
			}
			setColumnValueStrMap[setColumnValueStr] = append(
				setColumnValueStrMap[setColumnValueStr], primaryColumnValue,
			)
		} else {
			rowErrorMap[i] = strings.Join(rowErrorList, ";")
		}
		if len(setColumnValueStrMap) > 1000 {
			panic("一次不能修改超过1000种不同组合的字段属性值")
		}
	}

	// 修改的字段个数越多，步长应该设置越小，避免单个sql过大而超过了mysql设置的max_allowed_packet
	var step int
	if upload.ColumnCount/5 >= 2 { // 字段个数 >= 10
		step = 100
	} else if upload.ColumnCount/5 == 1 { // 5 <= 字段个数 < 10
		step = 500
	} else { // 字段个数 < 5
		step = 1000
	}

	modifyCount := 0
	for setColumnValueStr, primaryColumnValueList := range setColumnValueStrMap {
		currentLen := len(primaryColumnValueList)
		modifyCount += currentLen

		if currentLen > 1 {
			start := 0
			end := step
			for {
				if start >= currentLen {
					break
				}
				if end > currentLen {
					end = currentLen
				}
				primaryColumnValueList_ := primaryColumnValueList[start:end]
				primaryColumnValueStr := strings.Join(primaryColumnValueList_, ",")

				// 加上 occupy_state != EnvDataOccupyStateEnum.USING是为了避免用户上传的excel手动添加了被筛选出来用的数据
				sql := fmt.Sprintf(
					""+
						"update %s set %s, related_execute_id='%s' "+
						"where occupy_state='TO_BE_UPDATE' and `%s` in (%s);",
					upload.EnvTableName, setColumnValueStr, req.ExecuteId,
					primaryColumnId, primaryColumnValueStr,
				)
				sql = strings.ReplaceAll(sql, "'None',", "null,")
				sql = strings.ReplaceAll(sql, "None,", "null,")
				sqlList = append(sqlList, sql)
				start += step
				end += step
			}
		} else {
			primaryColumnValueStr := strings.Join(primaryColumnValueList, ",")
			sql := fmt.Sprintf(
				""+
					"update %s set %s, related_execute_id='%s' "+
					"where occupy_state='TO_BE_UPDATE' and `%s` = %s;",
				upload.EnvTableName, setColumnValueStr, req.ExecuteId,
				primaryColumnId, primaryColumnValueStr,
			)
			sql = strings.ReplaceAll(sql, "'None',", "null,")
			sql = strings.ReplaceAll(sql, "None,", "null,")
			sqlList = append(sqlList, sql)
		}
	}

	var result string
	if len(sqlList) == 0 {
		result = "excel中没有任何可以修改的记录"
	} else {
		result = "检查完上传的excel数据"
		updateOccupyStateSql := fmt.Sprintf(
			""+
				"update %s set occupy_state='UNUSED' where related_execute_id='%s' and occupy_state!='UNUSED';",
			upload.EnvTableName, req.ExecuteId,
		)
		sqlList = append(sqlList, updateOccupyStateSql)
	}
	upload.ValidateRowCount = validateRowCount
	upload.OkCount = modifyCount
	upload.SqlList = sqlList
	upload.RowErrorMap = rowErrorMap
	upload.Result = result
}

func (upload *UploadToModifyData) DealExcelSheet() {
	// 从数据库加载所有的主键值到一个set中
	upload.GetPrimaryColumnValueSet()

	if len(upload.PrimaryColumnValueMap) == 0 {
		upload.OkCount = 0
		upload.Result = "数据库不存在任何记录，操作失败"
	}

	upload.DealModifyData()
}

func (upload *UploadToModifyData) Entrance() {
	defer func() {
		var err error
		if upload.ExcelPath != "" {
			err = upload.ExcelFile.SaveAs(upload.ExcelPath)
			if err != nil {
				upload.Logic.Logger.Error(fmt.Sprintf("关闭excel文件「%s」发生异常：「%s」", upload.ExcelPath, err))
			}
		}
		if upload.BackupExcelPath != "" {
			if err := upload.BackupExcelFile.SaveAs(upload.BackupExcelPath); err != nil {
				upload.Logic.Logger.Error(fmt.Sprintf("关闭excel文件「%s」发生异常：「%s」", upload.BackupExcelPath, err))
			}
		}
	}()

	{ // code block first
		defer func() {
			if err := recover(); err != nil {
				result := fmt.Sprintf("%s", err)
				upload.UploadCommonErrorHandle(result)
				upload.UploadRecoverEnvDataOccupyState()
			}
		}()

		// 获取账户池环境信息
		upload.GetPoolEnvInfo()
		// 保存excel文件并获取excel信息
		upload.SaveAndGetExcelInfo()

		// 上传excel修改池账号数据特殊处理
		// excel主键列错误信息映射，主键不能重复
		upload.DealModifyDataPrimaryValue()
		if len(upload.RowErrorMap) == 0 {
			// 将池账号关联数据改为「待更新状态」
			upload.UpdatePoolEnvData()
		} else {
			upload.JudgeToModifyExcel()
			panic("excel中存在行数据不合规")
		}
	}

	{ // code block second
		defer func() {
			if err := recover(); err != nil {
				upload.Logic.Logger.Error(err)
				return
			}
		}()

		// 获取并检查excel_column_id_list, 即（excel表头)
		upload.GetAndCheckExcelColumnIdList()
	}

	{ // code block third
		defer func() {
			if err := recover(); err != nil {
				result := fmt.Sprintf("%s", err)
				upload.CommonErrorUpdateRecord(result)
			}
			upload.UploadRecoverEnvDataOccupyState()
		}()
		// 快速找出excel表头和第1行数据字段错误信息
		upload.QuickFindColumnErrorInfo()
		// 字段索引与excel字母关系
		upload.GetColumnIndexLetterMap()
		// column_id与*model.TPoolColumn映射关系
		upload.GetColumnIdColumnModelMap()

		// 重新导入修改数据需要判断导入的数据是不是跟之前筛选出来的记录完全匹配
		if !upload.IsNewRecord() {
			errorFlag := upload.UploadToModifyDataSpecialDeal()
			if !errorFlag {
				// 处理excel工作表数据
				upload.DealExcelSheet()
				err := upload.GenerateSqlFileAndUpdateExecuteRecord()
				if err != nil {
					panic(err)
				}
			}
		} else {
			// 处理excel工作表数据
			upload.DealExcelSheet()
			err := upload.GenerateSqlFileAndUpdateExecuteRecord()
			if err != nil {
				panic(err)
			}
		}
	}
}

func genOrUpdateOperateRecord(l *UploadToCheckLogic, req *types.UploadToCheckReq, userId string) error {
	// 生成或更新操作流水
	db, _ := l.svcCtx.DB.RawDB()

	recordSql := fmt.Sprintf(
		""+
			"INSERT INTO t_execute_record "+
			"(execute_id, pool_id, env_id, execute_way, "+
			"description, state, result, current_json, "+
			"created_by, updated_by) "+
			"VALUES "+
			"("+
			"'%s', %d, %d, '%s', "+
			"'%s', '%s', '%s', '%s', "+
			"'%s', '%s') "+
			"ON DUPLICATE KEY UPDATE "+
			"state='%s', result='%s', "+
			"updated_by='%s' ",
		req.ExecuteId, req.PoolId, req.EnvId, common.PoolEnvDataUploadPurposeName[req.Purpose],
		"None", "CHECKING", "None", "None",
		userId, userId,
		"CHECKING", "None",
		userId,
	)
	recordSql = strings.ReplaceAll(recordSql, "'None',", "null,")
	recordSql = strings.ReplaceAll(recordSql, "None,", "null,")
	_, err := db.ExecContext(l.ctx, recordSql)

	return err
}

func RealUploadToCheck(l *UploadToCheckLogic, req *types.UploadToCheckReq) {
	// ctx跨协程使用会有问题，得重新赋值
	ctx := context.Background()
	l.ctx = ctx

	switch common.PoolEnvDataUploadPurpose(req.Purpose) {
	case common.PurposeAddData:
		obj := UploadToAddData{
			Upload{Logic: l, Req: req},
		}
		obj.Entrance()
	case common.PurposeDeleteData:
		obj := UploadToDeleteData{
			Upload{Logic: l, Req: req},
		}
		obj.Entrance()
	case common.PurposeModifyData:
		obj := UploadToModifyData{
			Upload:          Upload{Logic: l, Req: req},
			BackupExcelPath: "",
			BackupExcelFile: nil,
		}
		obj.Entrance()
	}
}

func (l *UploadToCheckLogic) UploadToCheck(req *types.UploadToCheckReq) (resp *types.UploadToCheckResp, err error) {
	// 账号池数据环境之文件上传并校验（异步）

	// 判断传参是否合法
	tPoolTable, err := l.svcCtx.TPoolTableModel.FindOne(l.ctx, req.PoolId)
	if err != nil || tPoolTable == nil {
		return nil, errors.New(fmt.Sprintf("找不到poo_id为：「%d」的池账号模板环境，不能继续操作", req.PoolId))
	}
	envTable, err := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, req.PoolId, req.EnvId)
	if err != nil || envTable == nil {
		return nil, errors.New(fmt.Sprintf("找不到env_id为：「%d」的池账号环境，不能继续操作", req.EnvId))
	}
	fields, err := l.svcCtx.TPoolColumnModel.FindAllColumnNameWithBackQuoteByTableName(l.ctx, tPoolTable.TableName)
	if err != nil || fields == nil {
		return nil, errors.New(
			fmt.Sprintf(
				"找不到池账号模板表名为：「%s」的池账号字段，不能继续操作", tPoolTable.TableName,
			),
		)
	}

	// 生成执行id
	executeId := req.ExecuteId
	if executeId == "" { // 重新检测时不为空
		currentTimeStamp := time.Now().UnixMilli()
		executeId = qetutils.GenNanoId(strconv.FormatInt(currentTimeStamp, 10))
		req.ExecuteId = executeId
	}

	loginUser := userinfo.FromContext(l.ctx)

	// 生成或更新操作流水
	err = genOrUpdateOperateRecord(l, req, loginUser.Account)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("生成操作流水发生异常:%s", err))
	}

	// 开启协程 执行真正的 账号池数据环境文件上传并校验
	go RealUploadToCheck(l, req)

	return &types.UploadToCheckResp{ExecuteId: executeId}, nil
}
