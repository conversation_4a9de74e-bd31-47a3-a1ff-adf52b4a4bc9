package account_pool_env_data

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/utils"
)

type CreateAccountPoolEnvDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateAccountPoolEnvDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAccountPoolEnvDataLogic {
	return &CreateAccountPoolEnvDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateAccountPoolEnvDataLogic) CreateAccountPoolEnvData(req *types.CreateAccountPoolEnvDataReq) (
	resp *types.CreateAccountPoolEnvDataResp, err error,
) {
	// 请求参数
	executeId := req.ExecuteId
	description := req.Description

	// 判断传参是否合法
	record, err := l.svcCtx.TExecuteRecordModel.FindByExecuteId(l.ctx, executeId)
	if err != nil || record == nil {
		return nil, errors.New(fmt.Sprintf("找不到执行id为「%s」的记录", executeId))
	}

	var flag bool
	switch record.State {
	case "DETECTED", "END_OF_QUERY":
		flag = true
	}
	if !flag {
		return nil, errors.New(fmt.Sprintf("执行id[%s]关联的记录不能继续操作", executeId))
	}
	tPoolTable, err := l.svcCtx.TPoolTableModel.FindOne(l.ctx, req.PoolId)
	if err != nil || tPoolTable == nil {
		return nil, errors.New(fmt.Sprintf("找不到poo_id为：「%d」的池账号模板环境，不能继续操作", req.PoolId))
	}
	envTable, err := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, req.PoolId, req.EnvId)
	if err != nil || envTable == nil {
		return nil, errors.New(fmt.Sprintf("找不到env_id为：「%d」的池账号环境，不能继续操作", req.EnvId))
	}
	fields, err := l.svcCtx.TPoolColumnModel.FindAllColumnNameWithBackQuoteByTableName(l.ctx, tPoolTable.TableName)
	if err != nil || fields == nil {
		return nil, errors.New(
			fmt.Sprintf(
				"找不到池账号模板表名为：「%s」的池账号字段，不能继续操作", tPoolTable.TableName,
			),
		)
	}

	// 账号池环境第2步操作锁key
	lockKey := fmt.Sprintf("%d_%d_second", record.PoolId, record.EnvId)
	lockValue := lockKey
	lock := redislock.NewRedisLock(
		l.svcCtx.Redis, lockKey, redislock.WithValue(lockValue), redislock.WithExpire(36000*time.Second),
	)
	acquireErr := lock.Acquire()
	if acquireErr != nil {
		return nil, errors.New("当前账号池环境被占用，不可以执行新增数据操作，请稍后再试")
	}
	defer func() {
		_ = lock.Release()
	}()

	db, _ := l.svcCtx.DB.RawDB()
	// 将其他已筛选或者已检测的记录更改为 待检测（待筛选）
	updateRecordSql := fmt.Sprintf(
		""+
			"update t_execute_record set state='UNCHECKED' "+
			"where execute_id != '%s' and state in ('DETECTED', 'END_OF_QUERY')", executeId,
	)
	_, err1 := db.ExecContext(l.ctx, updateRecordSql)
	if err1 != nil {
		return nil, errors.New(fmt.Sprintf("执行sql【%s】发生异常", updateRecordSql))
	}

	// 判断sql文件是否存在
	currentJson := record.CurrentJson.String
	var currentJsonObj utils.PoolEnvDataExecCurrentJson
	_ = json.Unmarshal([]byte(currentJson), &currentJsonObj)
	sqlFileName := currentJsonObj.SqlFileName
	sqlPath := path.Join(l.svcCtx.Config.FileStore.SqlPath, sqlFileName)
	_, fileErr := os.Stat(sqlPath)
	if fileErr != nil { // 可能文件已经不存在
		return nil, errors.New(fmt.Sprintf("[%s]文件不存在，账户池环境数据新增失败", sqlFileName))
	}

	// 更新操作流水
	loginUser := userinfo.FromContext(l.ctx)
	// currentTimeStamp := time.Now().UnixMilli()
	updateRecordSql2 := "update t_execute_record set description=?, state=?, result=?, updated_by=? where execute_id=?"
	_, err2 := db.ExecContext(
		l.ctx, updateRecordSql2, description, "DATA_PROCESSING", "开始执行数据变更", loginUser.Account, executeId,
	)
	if err2 != nil {
		errorInfo := fmt.Sprintf("更新操作流水发生异常，其中sql为：【%s】，description为：【%s】, ", sqlFileName, description)
		return nil, errors.New(errorInfo)
	}

	// 启动协程执行sql文件
	go utils.ExecuteSql(l.Logger, l.svcCtx, sqlFileName, envTable.TableName, fields, executeId)

	return &types.CreateAccountPoolEnvDataResp{ExecuteId: executeId}, nil
}
