package account_pool_env_data

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strings"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/utils"
)

type CancelChangeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCancelChangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CancelChangeLogic {
	return &CancelChangeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CancelChangeLogic) CancelChange(req *types.CancelChangeReq) (resp *types.CancelChangeResp, err error) {
	// 请求参数
	executeId := req.ExecuteId
	description := req.Description

	// 池账号环境表名
	poolId := req.PoolId
	envId := req.EnvId
	envTable, err := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, poolId, envId)
	if err != nil || envTable == nil {
		return nil, errors.New(fmt.Sprintf("找不到pool_id为「%d」, env_id为「%d」的池账号环境", poolId, envId))
	}
	envTableName := envTable.TableName

	// 判断传参是否合法
	record, err := l.svcCtx.TExecuteRecordModel.FindByExecuteId(l.ctx, executeId)
	if err != nil || record == nil {
		return nil, errors.New(fmt.Sprintf("找不到执行id为[%s]的记录", executeId))
	}

	var flag bool
	switch record.State {
	case "UNCHECKED", "DETECTED", "END_OF_QUERY", "TO_BE_UPDATE":
		flag = true
	}
	if !flag {
		return nil, errors.New(fmt.Sprintf("执行id[%s]关联的记录不能继续操作", executeId))
	}

	currentJson := record.CurrentJson.String
	var currentJsonObj utils.PoolEnvDataExecCurrentJson
	_ = json.Unmarshal([]byte(currentJson), &currentJsonObj)

	db, _ := l.svcCtx.DB.RawDB()

	// 解除账户池环境数据占用
	updateEnvTableSql := fmt.Sprintf(
		"update %s set occupy_state='UNUSED', related_execute_id=null where related_execute_id='%s'",
		envTableName, executeId,
	)
	_, _ = db.ExecContext(l.ctx, updateEnvTableSql)

	// 删除sql文件
	sqlFileName := currentJsonObj.SqlFileName
	if sqlFileName != "" {
		sqlPath := path.Join(l.svcCtx.Config.FileStore.SqlPath, sqlFileName)
		_, fileErr := os.Stat(sqlPath)
		// os.IsNotExist(fileErr)
		if fileErr == nil {
			os.Remove(sqlPath)
		}
	}

	loginUser := userinfo.FromContext(l.ctx)
	// 修改流水为完成状态
	updateRecordSql := fmt.Sprintf(
		"update t_execute_record set state='TERMINATED', updated_by='%s', description='%s' where execute_id='%s'",
		loginUser.Account, description, executeId,
	)
	updateRecordSql = strings.ReplaceAll(updateRecordSql, "'None',", "null,")
	updateRecordSql = strings.ReplaceAll(updateRecordSql, "None,", "null,")
	_, err = db.ExecContext(l.ctx, updateRecordSql)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("执行sql「%s」发生异常", updateRecordSql))
	}

	return resp, nil
}
