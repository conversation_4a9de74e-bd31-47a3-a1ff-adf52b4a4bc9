package account_pool_env_data

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

type ReDetectLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReDetectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReDetectLogic {
	return &ReDetectLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReDetectLogic) ReDetect(req *types.ReDetectReq) (resp *types.ReDetectResp, err error) {
	// 请求参数
	executeId := req.ExecuteId

	// 判断传参是否合法
	selectBuilder := l.svcCtx.TExecuteRecordModel.SelectBuilder()
	selectBuilder = selectBuilder.Where(fmt.Sprintf("`execute_id` = '%s' and state = 'UNCHECKED'", executeId))
	records, err := l.svcCtx.TExecuteRecordModel.FindByQuery(l.ctx, selectBuilder)
	if len(records) == 0 || err != nil {
		return nil, fmt.Errorf("执行id:「%s」关联的记录不存在或者不能继续操作", executeId)
	}

	record := records[0]
	poolId := record.PoolId
	envId := record.EnvId

	currentJson := record.CurrentJson.String
	var currentJsonObj utils.PoolEnvDataExecCurrentJson
	err = json.Unmarshal([]byte(currentJson), &currentJsonObj)
	if err != nil {
		return nil, err
	}
	way := currentJsonObj.Way

	wayPurposeMap := map[string]string{
		"UPLOAD_TO_ADD_DATA":    "ADD_DATA",
		"UPLOAD_TO_DELETE_DATA": "DELETE_DATA",
		"UPLOAD_TO_MODIFY_DATA": "MODIFY_DATA",
	}

	switch way {
	case "UPLOAD_TO_ADD_DATA":
		purpose := wayPurposeMap[way]
		req := &types.UploadToCheckReq{
			ExecuteId:     executeId,
			PoolId:        poolId,
			EnvId:         envId,
			ExcelFileName: currentJsonObj.ExcelFileName,
			Purpose:       common.PoolEnvDataUploadPurposeValue[purpose],
		}
		ll := UploadToCheckLogic{
			ctx:    l.ctx,
			svcCtx: l.svcCtx,
		}
		_, _ = ll.UploadToCheck(req)
	case "UPLOAD_TO_DELETE_DATA":
		purpose := wayPurposeMap[way]
		req := &types.UploadToCheckReq{
			ExecuteId:     executeId,
			PoolId:        poolId,
			EnvId:         envId,
			ExcelFileName: currentJsonObj.ExcelFileName,
			Purpose:       common.PoolEnvDataUploadPurposeValue[purpose],
		}
		ll := UploadToCheckLogic{
			ctx:    l.ctx,
			svcCtx: l.svcCtx,
		}
		_, _ = ll.UploadToCheck(req)
	case "QUERY_TO_DELETE_DATA":
		req := &types.QueryForDeleteAccountPoolEnvDataReq{
			ExecuteId:     executeId,
			PoolId:        poolId,
			EnvId:         envId,
			QuerySql:      currentJsonObj.QuerySql,
			ExpectedCount: currentJsonObj.ExpectedCount,
		}
		ll := QueryForDeleteAccountPoolEnvDataLogic{
			ctx:    l.ctx,
			svcCtx: l.svcCtx,
		}
		_, _ = ll.QueryForDeleteAccountPoolEnvData(req)
	case "UPLOAD_TO_MODIFY_DATA":
		purpose := wayPurposeMap[way]
		req := &types.UploadToCheckReq{
			ExecuteId:     executeId,
			PoolId:        poolId,
			EnvId:         envId,
			ExcelFileName: currentJsonObj.ExcelFileName,
			Purpose:       common.PoolEnvDataUploadPurposeValue[purpose],
		}
		ll := UploadToCheckLogic{
			ctx:    l.ctx,
			svcCtx: l.svcCtx,
		}
		_, _ = ll.UploadToCheck(req)
	case "QUERY_TO_MODIFY_DATA":
		req := &types.QueryForModifyAccountPoolEnvDataReq{
			ExecuteId:     executeId,
			PoolId:        poolId,
			EnvId:         envId,
			QuerySql:      currentJsonObj.QuerySql,
			ExpectedCount: currentJsonObj.ExpectedCount,
		}
		ll := QueryForModifyAccountPoolEnvDataLogic{
			ctx:    l.ctx,
			svcCtx: l.svcCtx,
		}
		_, _ = ll.QueryForModifyAccountPoolEnvData(req)
	}

	return &types.ReDetectResp{ExecuteId: executeId}, nil
}
