package account_pool_env_data

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/user"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

type GetOperatorRecordListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOperatorRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOperatorRecordListLogic {
	return &GetOperatorRecordListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOperatorRecordListLogic) GetOperatorRecordList(req *types.GetOperatorRecordListReq) (
	resp *types.GetOperatorRecordListResp, err error,
) {
	// 请求参数
	poolId := req.PoolId
	envId := req.EnvId

	// 账户池环境名称
	envTable, err := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, poolId, envId)
	if err != nil || envTable == nil {
		return nil, errors.New(fmt.Sprintf("找不到pool_id为：%d, env_id为：%d的账户池", poolId, envId))
	}
	envName := envTable.PoolName

	// 查找账户池流水记录条数
	countBuilder := l.svcCtx.TExecuteRecordModel.SelectCountBuilder().Where(
		"`pool_id` = ? AND `env_id` = ?", poolId, envId,
	)
	count, err := l.svcCtx.TExecuteRecordModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		msg := fmt.Sprintf("查找pool_id为：「%d」,env_id为：「%d」的流水记录发生异常: %+v", poolId, envId, err)
		return nil, errors.New(msg)
	}

	// 默认排序字段
	var sortFields []*api.SortField
	sortField := api.SortField{
		Field: "updated_at",
		Order: "desc",
	}
	sortFields = append(sortFields, &sortField)

	// 分页查询流水记录
	selectBuilder := l.svcCtx.TExecuteRecordModel.SelectBuilder().Where("`pool_id` = ? AND `env_id` = ?", poolId, envId)
	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithPagination(l.svcCtx.TExecuteRecordModel, req.Pagination),
		sqlbuilder.WithSort(l.svcCtx.TExecuteRecordModel, sortFields),
	)
	tExecuteRecords, err := l.svcCtx.TExecuteRecordModel.FindByQuery(l.ctx, selectBuilder)
	if err != nil {
		msg := fmt.Sprintf("查找pool_id为：「%d」,env_id为：「%d」的流水记录发生异常: %+v", poolId, envId, err)
		return nil, errors.New(msg)
	}

	resp = &types.GetOperatorRecordListResp{
		EnvName:        envName,
		EnvRecordCount: count,
		EnvId:          envId,
	}

	// 避免返回给前端时为null
	resp.RecordList = make([]*types.Record, 0, len(tExecuteRecords))

	// 用户映射
	userMap := make(map[string]*userinfo.UserInfo)
	var userErr error

	for _, tExecuteRecord := range tExecuteRecords {
		createdBy, ok := userMap[tExecuteRecord.CreatedBy]
		if !ok {
			createdBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, tExecuteRecord.CreatedBy)
			if userErr != nil {
				err := errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
				return nil, err
			}
		}
		userMap[tExecuteRecord.CreatedBy] = createdBy
		updatedBy, ok := userMap[tExecuteRecord.UpdatedBy]
		if !ok {
			updatedBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, tExecuteRecord.UpdatedBy)
			if userErr != nil {
				err := errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
				return nil, err
			}
		}
		userMap[tExecuteRecord.UpdatedBy] = updatedBy
		r := &types.Record{
			ExecuteId:   tExecuteRecord.ExecuteId,
			PoolId:      tExecuteRecord.PoolId,
			EnvId:       tExecuteRecord.EnvId,
			ExecuteWay:  common.ExecuteWayValue[tExecuteRecord.ExecuteWay],
			Description: tExecuteRecord.Description.String,
			State:       common.EnvDataProcessStateValue[tExecuteRecord.State],
			Result:      tExecuteRecord.Result.String,
			CurrentJson: tExecuteRecord.CurrentJson.String,
			CreatedBy:   createdBy,
			UpdatedBy:   updatedBy,
			CreatedAt:   tExecuteRecord.CreatedAt.UnixMilli(),
			UpdatedAt:   tExecuteRecord.UpdatedAt.UnixMilli(),
		}
		resp.RecordList = append(resp.RecordList, r)
	}

	return resp, nil
}
