package account_pool_env_data

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type GetAvailableCountInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetAvailableCountInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAvailableCountInfoLogic {
	return &GetAvailableCountInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAvailableCountInfoLogic) GetAvailableCountInfo(req *types.GetAvailableCountInfoReq) (*types.GetAvailableCountInfoResp, error) {
	poolTemplateTable, err := l.svcCtx.TPoolTableModel.FindPoolTableNameByBelongProduct(l.ctx, req.BelongProduct)
	if err != nil {
		return nil, err
	}

	poolEnvTables, err := l.svcCtx.TPoolTableModel.FindPoolEnvTablesByBelongProduct(l.ctx, req.BelongProduct)
	if err != nil {
		return nil, err
	}

	fields, err := l.svcCtx.TPoolColumnModel.FindVisibleColumnNameWithBackQuoteByTableName(l.ctx, poolTemplateTable)
	if err != nil {
		return nil, err
	}

	resp := types.GetAvailableCountInfoResp{}
	for _, poolEnvTable := range poolEnvTables {
		fmt.Println(poolEnvTable)
		poolEnvModel := model.NewPoolEnvTableModel(l.svcCtx.DB, poolEnvTable.TableName, fields)
		fmt.Println(poolEnvModel)

		countBuilder := poolEnvModel.SelectCountBuilder()
		countBuilder = sqlbuilder.SearchOptions(countBuilder, sqlbuilder.WithCondition(poolEnvModel, req.Condition))

		count, err := poolEnvModel.FindCount(l.ctx, countBuilder)
		if err != nil {
			return nil, err
		}

		var availableCount string
		if count > 100 {
			availableCount = ">100"
		} else {
			availableCount = fmt.Sprintf("%d", count)
		}

		poolAvailableCountInfo := types.PoolAvailableCountInfo{
			PoolEnvName:    poolEnvTable.PoolName,
			AvailableCount: availableCount,
		}
		resp.AvailableCountInfo = append(resp.AvailableCountInfo, &poolAvailableCountInfo)
	}

	return &resp, nil
}
