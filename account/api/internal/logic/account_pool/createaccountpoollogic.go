package account_pool

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type CreateAccountPoolLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateAccountPoolLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAccountPoolLogic {
	return &CreateAccountPoolLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateAccountPoolLogic) CreateAccountPool(req *types.CreateAccountPoolReq) (
	*types.CreateAccountPoolResp, error,
) {
	loginUser := userinfo.FromContext(l.ctx)

	poolName := req.PoolName
	poolType := req.PoolType
	belongProduct := req.BelongProduct
	description := req.Description

	selectBuilder := l.svcCtx.TProductsModel.SelectBuilder()
	products, err := l.svcCtx.TProductsModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("查询产品列表发生错误：%s", err))
		return nil, err
	}

	var flag bool
	for _, product := range products {
		if product.BelongProduct == belongProduct {
			flag = true
			break
		}
	}
	if !flag {
		err = errorx.Err(errorx.NotExists, fmt.Sprintf("所属产品（项目）:「%s」不存在", belongProduct))
		return nil, errors.Wrapf(err, "所属产品（项目）:「%s」不存在", belongProduct)
	}

	flag, err2 := l.svcCtx.TPoolTableModel.IsPoolTemplateByBelongProduct(l.ctx, belongProduct)
	if err2 != nil {
		return nil, errorx.Err(errorx.DBError, fmt.Sprintf("查询belong_product发生错误：%s,请联系管理员", err2))
	}
	if flag {
		return nil, errors.New("同一个产品（项目）只能创建一个账户池模版")
	}

	if err = l.svcCtx.TPoolTableModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			var PoolTypeString string
			if poolType == 1 {
				PoolTypeString = "REPEATABLE"
			} else {
				PoolTypeString = "UNREPEATABLE"
			}

			tPoolTable := &model.TPoolTable{
				ParentPoolId:  sql.NullInt64{Valid: false},
				PoolName:      poolName,
				TableName:     fmt.Sprintf("t_%s", belongProduct),
				PoolType:      PoolTypeString,
				BelongProduct: belongProduct,
				Description:   sql.NullString{String: description, Valid: true},
				IsUsing:       "YES",
				CreatedBy:     loginUser.Account,
				UpdatedBy:     loginUser.Account,
			}

			insertBuilder := l.svcCtx.TPoolTableModel.InsertBuilder(tPoolTable)
			_, err = l.svcCtx.TPoolTableModel.InsertTX(l.ctx, session, insertBuilder)
			return err
		},
	); err != nil {
		return nil, err
	}

	poolTable, err3 := l.svcCtx.TPoolTableModel.FindPoolTableByBelongProduct(l.ctx, belongProduct)
	if err3 != nil {
		return nil, err3
	}

	return &types.CreateAccountPoolResp{
		PoolId:        poolTable.Id,
		ParentPoolId:  poolTable.ParentPoolId.Int64,
		PoolName:      poolTable.PoolName,
		TableName:     poolTable.TableName,
		PoolType:      req.PoolType,
		BelongProduct: poolTable.BelongProduct,
		Description:   poolTable.Description.String,
		CreatedBy:     loginUser,
		UpdatedBy:     loginUser,
		CreatedAt:     poolTable.CreatedAt.UnixMilli(),
		UpdatedAt:     poolTable.UpdatedAt.UnixMilli(),
	}, nil
}
