package account_pool

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type CreateProductLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateProductLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateProductLogic {
	return &CreateProductLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateProductLogic) CreateProduct(req *types.CreateProductReq) (resp *types.CreateProductResp, err error) {
	selectBuilder := l.svcCtx.TProductsModel.SelectBuilder()
	products, err := l.svcCtx.TProductsModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("查询产品列表发生错误：%s", err))
		return nil, err
	}

	productNameMap := make(map[string]string)
	productTypeMap := make(map[int64]int64)
	belongProductMap := make(map[string]string)
	for _, product := range products {
		productNameMap[product.ProductName] = product.ProductName
		productTypeMap[product.ProductType] = product.ProductType
		belongProductMap[product.BelongProduct] = product.BelongProduct
	}

	_, existsProductName := productNameMap[req.ProductName]
	_, existsProductType := productTypeMap[req.ProductType]
	_, existsBelongProduct := belongProductMap[req.BelongProduct]
	if existsProductName || existsProductType || existsBelongProduct {
		err = errorx.Err(errorx.ResetObjectFailure, "不能创建重复的产品类型")
		return nil, err
	}

	err = l.svcCtx.TProductsModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			_, err = l.svcCtx.TProductsModel.Insert(
				context, session, &model.TProducts{
					ProductName:   req.ProductName,
					ProductType:   req.ProductType,
					BelongProduct: req.BelongProduct,
				},
			)
			return err
		},
	)
	if err != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("创建新的产品类型发生错误：%s", err))
		return nil, err
	}

	return &types.CreateProductResp{}, nil
}
