package account_pool

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/user"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type ModifyAccountPoolLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewModifyAccountPoolLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyAccountPoolLogic {
	return &ModifyAccountPoolLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ModifyAccountPoolLogic) ModifyAccountPool(req *types.ModifyAccountPoolReq) (
	resp *types.ModifyAccountPoolResp, err error,
) {
	flag, err := l.svcCtx.TPoolTableModel.IsPoolTemplateByPoolId(l.ctx, req.PoolId)
	if err != nil {
		return nil, err
	}

	if !flag {
		return nil, fmt.Errorf("不存在pool_id为：%d的账户池模版，无法执行修改", req.PoolId)
	}

	loginUser := userinfo.FromContext(l.ctx)

	if err := l.svcCtx.TPoolTableModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			tPoolTable := &model.TPoolTable{
				Id:          req.PoolId,
				PoolName:    req.PoolName,
				Description: sql.NullString{String: req.Description, Valid: true},
				UpdatedBy:   loginUser.Account,
			}
			updateBuilder := l.svcCtx.TPoolTableModel.UpdateBuilder(tPoolTable)
			_, err := l.svcCtx.TPoolTableModel.UpdateTX(l.ctx, session, updateBuilder)
			return err
		},
	); err != nil {
		return nil, err
	}

	poolTable, _ := l.svcCtx.TPoolTableModel.FindPoolTableByPoolId(l.ctx, req.PoolId)

	var poolType int64
	if poolTable.PoolType == "REPEATABLE" {
		poolType = 1
	} else {
		poolType = 2
	}

	var createdBy *userinfo.UserInfo
	var userErr error
	createdBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, poolTable.CreatedBy)
	if userErr != nil {
		err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
		return nil, err
	}

	return &types.ModifyAccountPoolResp{
		PoolId:        poolTable.Id,
		ParentPoolId:  poolTable.ParentPoolId.Int64,
		PoolName:      poolTable.PoolName,
		TableName:     poolTable.TableName,
		PoolType:      poolType,
		BelongProduct: poolTable.BelongProduct,
		Description:   poolTable.Description.String,
		CreatedBy:     createdBy,
		UpdatedBy:     loginUser,
		CreatedAt:     poolTable.CreatedAt.UnixMilli(),
		UpdatedAt:     poolTable.UpdatedAt.UnixMilli(),
	}, nil
}
