package account_pool

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
)

type DeleteAccountPoolLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteAccountPoolLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteAccountPoolLogic {
	return &DeleteAccountPoolLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteAccountPoolLogic) DeleteAccountPool(req *types.DeleteAccountPoolReq) (
	resp *types.DeleteAccountPoolResp, err error,
) {
	// 查询出账号池模版表和账号池环境表(最后drop)
	tableNames, err := l.svcCtx.TPoolTableModel.FindAllPoolAndPoolEnvTableNameByPoolId(l.ctx, req.PoolId)
	if err != nil || tableNames == nil {
		return nil, errors.New(fmt.Sprintf("找不到pool_id为「%d」的池账号模板环境", req.PoolId))
	}

	// 所有池账号环境
	envTables, err := l.svcCtx.TPoolTableModel.FindPoolEnvTablesByPoolId(l.ctx, req.PoolId)
	if err != nil {
		return nil, err
	}
	length := len(envTables)
	if length > 0 {
		return nil, errors.New(fmt.Sprintf("不能直接删除账户池模版，因为它被%d个账户池环境引用)", length))
	}

	// 删除账号池字段、删除账号池模版表和账号池环境表、删除所有账户池环境流水
	if err := l.svcCtx.TPoolTableModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			var deleteSqlList []string
			// 删除账号池字段
			deleteColumnSql := fmt.Sprintf("delete from t_pool_column where pool_id=%d", req.PoolId)
			// 删除账号池模版表和账号池环境表
			deleteSqlList = append(deleteSqlList, deleteColumnSql)
			deleteTableSql := fmt.Sprintf(
				"delete from t_pool_table where (id=%d and parent_pool_id is null) or parent_pool_id=%d",
				req.PoolId, req.PoolId,
			)
			deleteSqlList = append(deleteSqlList, deleteTableSql)
			// 删除所有账户池环境流水
			deleteEnvRecordSql := fmt.Sprintf("delete from t_execute_record where pool_id=%d", req.PoolId)
			deleteSqlList = append(deleteSqlList, deleteEnvRecordSql)
			for _, sql := range deleteSqlList {
				_, err0 := session.ExecCtx(l.ctx, sql)
				if err0 != nil {
					return err0
				}
			}
			return nil
		},
	); err != nil {
		return nil, err
	}

	// drop查询出来的所有表
	db, _ := l.svcCtx.DB.RawDB()
	for _, tableName := range tableNames {
		dropTableSql := fmt.Sprintf("DROP TABLE IF EXISTS `%s`", tableName)
		_, err := db.ExecContext(l.ctx, dropTableSql)
		if err != nil {
			return nil, err
		}
	}

	// 删除池账号模版成功后需要尝试删除关联子环境redis数据
	poolTable := tableNames[0]
	prefixSlice := []string{"pool_table", "pool_id", "pool_column_name", "pool_fields", "pool_column_id_column_type"}
	for _, prefix := range prefixSlice {
		redisKey := fmt.Sprintf("%s_%s", prefix, poolTable)
		_, redisErr := l.svcCtx.Redis.Del(redisKey)
		if redisErr != nil {
			logx.Error(fmt.Sprintf("删除redis发生错误:%s", err))
		}
	}
	return resp, nil
}
