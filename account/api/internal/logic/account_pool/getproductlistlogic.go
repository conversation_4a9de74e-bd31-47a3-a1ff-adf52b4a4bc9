package account_pool

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
)

type GetProductListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetProductListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProductListLogic {
	return &GetProductListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetProductListLogic) GetProductList(req *types.GetProductListReq) (*types.GetProductListResp, error) {
	selectBuilder := l.svcCtx.TProductsModel.SelectBuilder()
	products, err := l.svcCtx.TProductsModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		err = errorx.Err(errorx.DBError, fmt.Sprintf("查询产品列表发生错误：%s", err))
		return nil, err
	}

	var productList []*types.Product
	for _, product := range products {
		productList = append(productList, &types.Product{
			ProductName:   product.ProductName,
			ProductType:   product.ProductType,
			BelongProduct: product.BelongProduct,
		})
	}

	return &types.GetProductListResp{
		ProductList: productList,
	}, nil
}
