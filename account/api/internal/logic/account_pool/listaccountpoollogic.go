package account_pool

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/user"
)

type ListAccountPoolLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListAccountPoolLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListAccountPoolLogic {
	return &ListAccountPoolLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListAccountPoolLogic) ListAccountPool(req *types.ListAccountPoolReq) (*types.ListAccountPoolResp, error) {
	poolTables, err := l.svcCtx.TPoolTableModel.FindAllPoolTable(l.ctx)
	if err != nil {
		return nil, err
	}

	// 用户映射
	userMap := make(map[string]*userinfo.UserInfo)
	var userErr error

	var resp types.ListAccountPoolResp
	for _, poolTable := range poolTables {
		var poolType int64
		if poolTable.PoolType == "REPEATABLE" {
			poolType = 1
		} else {
			poolType = 2
		}
		var isUsing int64
		if poolTable.IsUsing == "YES" {
			isUsing = 1
		} else {
			isUsing = 2
		}

		createdBy, ok := userMap[poolTable.CreatedBy]
		if !ok {
			createdBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, poolTable.CreatedBy)
			if userErr != nil {
				err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
				return nil, err
			}
			userMap[poolTable.CreatedBy] = createdBy
		}
		updatedBy, ok := userMap[poolTable.UpdatedBy]
		if !ok {
			updatedBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, poolTable.UpdatedBy)
			if userErr != nil {
				err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
				return nil, err
			}
			userMap[poolTable.UpdatedBy] = updatedBy
		}

		accountPool := types.AccountPool{
			PoolId:        poolTable.Id,
			ParentPoolId:  poolTable.ParentPoolId.Int64,
			PoolName:      poolTable.PoolName,
			TableName:     poolTable.TableName,
			PoolType:      poolType,
			BelongProduct: poolTable.BelongProduct,
			Description:   poolTable.Description.String,
			IsUsing:       isUsing,
			CreatedBy:     createdBy,
			UpdatedBy:     updatedBy,
			CreatedAt:     poolTable.CreatedAt.UnixMilli(),
			UpdatedAt:     poolTable.UpdatedAt.UnixMilli(),
		}
		resp.AccountPoolList = append(resp.AccountPoolList, &accountPool)
	}

	return &resp, nil
}
