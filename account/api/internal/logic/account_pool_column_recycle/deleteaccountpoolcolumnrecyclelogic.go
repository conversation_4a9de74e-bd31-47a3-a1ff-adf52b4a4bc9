package account_pool_column_recycle

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
)

type DeleteAccountPoolColumnRecycleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteAccountPoolColumnRecycleLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteAccountPoolColumnRecycleLogic {
	return &DeleteAccountPoolColumnRecycleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteAccountPoolColumnRecycleLogic) DeleteAccountPoolColumnRecycle(req *types.DeleteAccountPoolColumnRecycleReq) (
	*types.DeleteAccountPoolColumnRecycleResp, error,
) {
	poolId := req.PoolId
	columnIdList := req.ColumnIdList

	var columnIdList2 []string
	for _, columnId := range columnIdList {
		columnIdList2 = append(columnIdList2, fmt.Sprintf("'%s'", *columnId))
	}
	columnIdListStr := strings.Join(columnIdList2, ",")

	// 找出可以删除的字段
	db, _ := l.svcCtx.DB.RawDB()
	selectSql := fmt.Sprintf(
		""+
			"select column_id from t_pool_column "+
			"where pool_id=%d and column_is_using='NO' "+
			"and column_id in (%s)", poolId, columnIdListStr,
	)
	rows, _ := db.QueryContext(l.ctx, selectSql)
	columns, _ := rows.Columns()
	count := len(columns)
	valueSlice := make([]any, count)
	valuePtrSlice := make([]any, count)
	var realDeleteColumnIdList []string
	for rows.Next() {
		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			// value := valueSlice[0].([]uint8)
			// columnId := string(value)
			realDeleteColumnIdList = append(realDeleteColumnIdList, cast.ToString(valueSlice[0]))
		}
	}
	if len(realDeleteColumnIdList) == 0 {
		return nil, errors.New("没有可以删除的字段")
	}

	realDeleteColumnIdStr := strings.Join(realDeleteColumnIdList, "','")
	deleteSql := fmt.Sprintf(
		""+
			"delete from t_pool_column "+
			"where pool_id=%d and column_is_using='NO' "+
			"and column_id in ('%s') ",
		poolId, realDeleteColumnIdStr,
	)

	sqlList := []string{deleteSql}

	tableNames, _ := l.svcCtx.TPoolTableModel.FindAllPoolAndPoolEnvTableNameByPoolId(l.ctx, poolId)
	for _, tableName := range tableNames {
		for _, realDeleteColumnId := range realDeleteColumnIdList {
			ddlSql := fmt.Sprintf("ALTER TABLE %s drop column `%s`", tableName, realDeleteColumnId)
			sqlList = append(sqlList, ddlSql)
		}
	}

	for _, sql := range sqlList {
		_, err := db.ExecContext(l.ctx, sql)
		if err != nil {
			return nil, errors.New(fmt.Sprintf("执行sql:%s发生异常", sql))
		}
	}

	return &types.DeleteAccountPoolColumnRecycleResp{}, nil
}
