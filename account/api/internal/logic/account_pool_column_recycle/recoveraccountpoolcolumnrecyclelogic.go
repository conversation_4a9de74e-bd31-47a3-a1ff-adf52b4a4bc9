package account_pool_column_recycle

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
)

type RecoverAccountPoolColumnRecycleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRecoverAccountPoolColumnRecycleLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RecoverAccountPoolColumnRecycleLogic {
	return &RecoverAccountPoolColumnRecycleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RecoverAccountPoolColumnRecycleLogic) RecoverAccountPoolColumnRecycle(req *types.RecoverAccountPoolColumnRecycleReq) (
	*types.RecoverAccountPoolColumnRecycleResp, error,
) {
	poolId := req.PoolId
	columnIdList := req.ColumnIdList

	db, _ := l.svcCtx.DB.RawDB()

	var columnIds []string
	for _, columnId := range columnIdList {
		columnIds = append(columnIds, fmt.Sprintf("'%s'", *columnId))
	}
	columnIdStr := strings.Join(columnIds, ",")

	updateSql := fmt.Sprintf(
		""+
			"update t_pool_column set column_is_using='YES' "+
			"where pool_id=%d and column_id in (%s)",
		poolId, columnIdStr,
	)
	result, err := db.ExecContext(l.ctx, updateSql)
	fmt.Println(result)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("执行sql:%s发生异常", updateSql))
	}

	// 恢复字段成功后需要尝试删除关联子环境redis数据
	poolTable, err := l.svcCtx.TPoolTableModel.FindPoolTableByPoolId(l.ctx, poolId)
	if err != nil {
		logx.Error(fmt.Sprintf("查询账号池模板表发生错误:%s", err))
	} else {
		poolTableName := poolTable.TableName
		prefixSlice := []string{"pool_column_name", "pool_fields", "pool_column_id_column_type"}
		for _, prefix := range prefixSlice {
			redisKey := fmt.Sprintf("%s_%s", prefix, poolTableName)
			_, redisErr := l.svcCtx.Redis.Del(redisKey)
			if redisErr != nil {
				logx.Error(fmt.Sprintf("删除redis发生错误:%s", err))
			}
		}
	}

	return &types.RecoverAccountPoolColumnRecycleResp{}, nil
}
