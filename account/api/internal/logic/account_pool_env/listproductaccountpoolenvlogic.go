package account_pool_env

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
)

type ListProductAccountPoolEnvLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListProductAccountPoolEnvLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ListProductAccountPoolEnvLogic {
	return &ListProductAccountPoolEnvLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListProductAccountPoolEnvLogic) ListProductAccountPoolEnv(req *types.ListProductAccountPoolEnvReq) (
	*types.ListProductAccountPoolEnvResp, error,
) {
	belongProduct := req.BelongProduct
	poolEnvTables, err := l.svcCtx.TPoolTableModel.FindPoolEnvTablesByBelongProduct(l.ctx, belongProduct)
	if err != nil {
		return nil, err
	}

	var resp types.ListProductAccountPoolEnvResp
	for _, poolEnvTable := range poolEnvTables {
		poolEnv := types.ProductAccountPoolEnv{
			Key:   poolEnvTable.TableName,
			Value: poolEnvTable.PoolName,
		}
		resp.Items = append(resp.Items, &poolEnv)
	}
	return &resp, nil
}
