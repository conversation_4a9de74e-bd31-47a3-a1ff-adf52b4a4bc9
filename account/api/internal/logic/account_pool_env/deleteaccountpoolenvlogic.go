package account_pool_env

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
)

type DeleteAccountPoolEnvLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteAccountPoolEnvLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteAccountPoolEnvLogic {
	return &DeleteAccountPoolEnvLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteAccountPoolEnvLogic) DeleteAccountPoolEnv(req *types.DeleteAccountPoolEnvReq) (
	resp *types.DeleteAccountPoolEnvResp, err error,
) {
	envTable, err := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, req.PoolId, req.EnvId)
	if err != nil {
		return nil, errors.New("账户池环境不存在，删除失败")
	}

	db, _ := l.svcCtx.DB.RawDB()

	selectEnvRecordSql := fmt.Sprintf(
		""+
			"select count(1) recordCount from t_execute_record "+
			"where pool_id=%d and env_id=%d "+
			"and state in ('CHECKING','QUERYING','DATA_PROCESSING')",
		req.PoolId, req.EnvId,
	)

	rows, err := db.QueryContext(l.ctx, selectEnvRecordSql)
	if err != nil {
		return nil, err
	}
	columns, _ := rows.Columns()
	count := len(columns)
	valueSlice := make([]any, count)
	valuePtrSlice := make([]any, count)
	var recordCount int64
	for rows.Next() {
		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err != nil {
			// value := valueSlice[0].([]uint8)
			// valueStr := string(value)
			// recordCount, _ = strconv.ParseInt(valueStr, 0, 0)
			recordCount = cast.ToInt64(valueSlice[0])
		}
	}
	if recordCount > 0 {
		return nil, errors.New("账户池环境有未完成的流水，不可以直接删除环境")
	}

	envName := envTable.PoolName
	currentTimeStamp := time.Now().UnixMilli()
	newEnvName := fmt.Sprintf("%s_%d_backup", envName, currentTimeStamp)
	tableName := envTable.TableName
	newTableName := fmt.Sprintf("%s_backup", tableName)

	renameSql := fmt.Sprintf("rename table `%s` to `%s`", tableName, newTableName)
	_, err = db.ExecContext(l.ctx, renameSql)
	if err != nil {
		return nil, err
	}
	updateSql := "update t_pool_table set pool_name=?, table_name=?, is_using='NO' where parent_pool_id=? and id=?"
	_, err = db.ExecContext(l.ctx, updateSql, newEnvName, newTableName, req.PoolId, req.EnvId)
	if err != nil {
		return nil, err
	}

	return &types.DeleteAccountPoolEnvResp{}, nil
}
