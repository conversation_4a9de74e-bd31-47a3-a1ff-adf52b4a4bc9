package account_pool_env

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type CreateAccountPoolEnvLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateAccountPoolEnvLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAccountPoolEnvLogic {
	return &CreateAccountPoolEnvLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateAccountPoolEnvLogic) CreateAccountPoolEnv(req *types.CreateAccountPoolEnvReq) (
	resp *types.CreateAccountPoolEnvResp, err error,
) {
	if req.CoolingTime < 0 {
		return nil, errors.New("账号池冷却时间不能设置小于0")
	}

	poolTable, err := l.svcCtx.TPoolTableModel.FindPoolTableByPoolId(l.ctx, req.PoolId)
	if err != nil && err.Error() == "sql: no rows in result set" {
		return nil, errors.New(fmt.Sprintf("不存在pool_id为「%d」的账号池模板，不能新增池账号环境", req.PoolId))
	}
	poolColumns, err := l.svcCtx.TPoolColumnModel.FindAllColumnByPoolId(l.ctx, req.PoolId)
	if err != nil || poolColumns == nil {
		return nil, errors.New(fmt.Sprintf("pool_id为「%d」账号池模板还没有任何字段，不能新增池账号环境", req.PoolId))
	}

	tableName := poolTable.TableName
	belongProduct := poolTable.BelongProduct
	poolType := poolTable.PoolType

	db, _ := l.svcCtx.DB.RawDB()
	selectSql := fmt.Sprintf(
		"select count(1) total from t_pool_table where parent_pool_id=%d "+
			"and (pool_name='%s' or description='%s')", req.PoolId, req.EnvName, req.Description,
	)
	rows, _ := db.QueryContext(l.ctx, selectSql)
	columns, _ := rows.Columns()
	count := len(columns)
	valueSlice := make([]any, count)
	valuePtrSlice := make([]any, count)
	var total int64
	for rows.Next() {
		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			// value := valueSlice[0].([]uint8)
			// valueStr := string(value)
			// total, _ = strconv.ParseInt(valueStr, 0, 0)
			total = cast.ToInt64(valueSlice[0])
		}
	}
	if total > 0 {
		return nil, errors.New("不能创建重复的账户池环境")
	}

	currentTimeStamp := time.Now().UnixMilli()
	envTableName := fmt.Sprintf("%s_%d", tableName, currentTimeStamp)

	loginUser := userinfo.FromContext(l.ctx)

	data := &model.TPoolTable{
		ParentPoolId:  sql.NullInt64{Int64: req.PoolId, Valid: true},
		PoolName:      req.EnvName,
		TableName:     envTableName,
		PoolType:      poolType,
		BelongProduct: belongProduct,
		Description:   sql.NullString{String: req.Description, Valid: req.Description != ""},
		IsUsing:       "YES",
		CreatedBy:     loginUser.Account,
		UpdatedBy:     loginUser.Account,
	}

	result, err := l.svcCtx.TPoolTableModel.InsertWithOutCreateAtAndUpdateAt(l.ctx, data)
	if err != nil {
		fmt.Println(result)
		return nil, err
	}

	envId, _ := result.LastInsertId()

	ddlSql := fmt.Sprintf("create table `%s` like %s", envTableName, tableName)
	result2, err := db.ExecContext(l.ctx, ddlSql)
	if err != nil {
		fmt.Println(result2)
		return nil, err
	}

	envTable, _ := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, req.PoolId, envId)

	return &types.CreateAccountPoolEnvResp{
		PoolId:         envTable.ParentPoolId.Int64,
		EnvId:          envTable.Id,
		EnvName:        envTable.PoolName,
		CoolingTime:    req.CoolingTime,
		TotalCount:     0,
		OccupyCount:    0,
		AvailableCount: 0,
		UpdatingCount:  0,
		DeletingCount:  0,
		Description:    envTable.Description.String,
		CreatedBy:      loginUser,
		UpdatedBy:      loginUser,
		CreatedAt:      envTable.CreatedAt.UnixMilli(),
		UpdatedAt:      envTable.UpdatedAt.UnixMilli(),
	}, nil
}
