package account_pool_env

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/user"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
)

type ListAccountPoolEnvLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListAccountPoolEnvLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListAccountPoolEnvLogic {
	return &ListAccountPoolEnvLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListAccountPoolEnvLogic) ListAccountPoolEnv(req *types.ListAccountPoolEnvReq) (
	*types.ListAccountPoolEnvResp, error,
) {
	poolId := req.PoolId
	poolEnvTables, err := l.svcCtx.TPoolTableModel.FindPoolEnvTablesByPoolId(l.ctx, poolId)
	if err != nil {
		return nil, err
	}

	db, err := l.svcCtx.DB.RawDB()
	if err != nil {
		return nil, err
	}

	var resp types.ListAccountPoolEnvResp

	// 用户映射
	userMap := make(map[string]*userinfo.UserInfo)
	var userErr error

	for _, poolEnvTable := range poolEnvTables {
		envTableName := poolEnvTable.TableName
		selectSql := fmt.Sprintf(
			"select "+
				"count(1) totalCount, "+
				"count(case when occupy_state=? then 1 end) occupyCount, "+
				"count(case when occupy_state=? then 1 end) availableCount, "+
				"count(case when occupy_state=? then 1 end) updatingCount, "+
				"count(case when occupy_state=? then 1 end) deletingCount "+
				"from %s", envTableName,
		)
		rows, err := db.QueryContext(
			l.ctx, selectSql,
			int64(common.OccupyStateUsing),
			int64(common.OccupyStateUnused),
			int64(common.OccupyStateToBeUpdate),
			int64(common.OccupyStateToBeDelete),
		)
		if err != nil {
			return nil, err
		}
		columns, _ := rows.Columns()
		count := len(columns)
		valueSlice := make([]any, count)
		valuePtrSlice := make([]any, count)
		var totalCount int64
		var occupyCount int64
		var availableCount int64
		var updatingCount int64
		var deletingCount int64
		for rows.Next() {
			for i := range columns {
				valuePtrSlice[i] = &valueSlice[i]
			}
			err := rows.Scan(valuePtrSlice...)
			if err == nil {
				totalCount = cast.ToInt64(valueSlice[0])
				occupyCount = cast.ToInt64(valueSlice[1])
				availableCount = cast.ToInt64(valueSlice[2])
				updatingCount = cast.ToInt64(valueSlice[3])
				deletingCount = cast.ToInt64(valueSlice[4])
			}
		}

		createdBy, ok := userMap[poolEnvTable.CreatedBy]
		if !ok {
			createdBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, poolEnvTable.CreatedBy)
			if userErr != nil {
				err := errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
				return nil, err
			}
		}
		userMap[poolEnvTable.CreatedBy] = createdBy
		updatedBy, ok := userMap[poolEnvTable.UpdatedBy]
		if !ok {
			updatedBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, poolEnvTable.UpdatedBy)
			if userErr != nil {
				err := errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
				return nil, err
			}
		}

		envItem := types.EnvItem{
			PoolId:         poolEnvTable.ParentPoolId.Int64,
			EnvId:          poolEnvTable.Id,
			EnvName:        poolEnvTable.PoolName,
			TotalCount:     totalCount,
			OccupyCount:    occupyCount,
			AvailableCount: availableCount,
			UpdatingCount:  updatingCount,
			DeletingCount:  deletingCount,
			Description:    poolEnvTable.Description.String,
			CreatedBy:      createdBy,
			UpdatedBy:      updatedBy,
			CreatedAt:      poolEnvTable.CreatedAt.UnixMilli(),
			UpdatedAt:      poolEnvTable.UpdatedAt.UnixMilli(),
		}
		resp.EnvList = append(resp.EnvList, &envItem)
	}

	return &resp, nil
}
