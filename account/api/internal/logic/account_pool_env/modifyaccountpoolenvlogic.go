package account_pool_env

import (
	"context"
	"fmt"
	"strconv"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/user"
)

type ModifyAccountPoolEnvLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewModifyAccountPoolEnvLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyAccountPoolEnvLogic {
	return &ModifyAccountPoolEnvLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ModifyAccountPoolEnvLogic) ModifyAccountPoolEnv(req *types.ModifyAccountPoolEnvReq) (
	resp *types.ModifyAccountPoolEnvResp, err error,
) {
	poolId := req.PoolId
	envId := req.EnvId
	envName := req.EnvName
	coolingTime := req.CoolingTime
	description := req.Description

	envTable, err := l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, poolId, envId)
	if err != nil || envTable == nil {
		return nil, fmt.Errorf("pool_id为：%d， env_id为：%d的账户池环境不存在，修改失败", poolId, envId)
	}

	db, _ := l.svcCtx.DB.RawDB()

	// 更新池账号冷却时间
	if coolingTime < 0 {
		return nil, errors.New("池账号冷却时间不能设置为小于0")
	}

	tableDdlSql := "ALTER TABLE `?` ALTER COLUMN `cooling_time` SET DEFAULT ?"
	_, err = db.ExecContext(l.ctx, tableDdlSql, envTable.TableName, coolingTime)
	if err != nil {
		return nil, fmt.Errorf("pool_id为：%d， env_id为：%d更新池账号冷却时间发生错误：「%s」，修改失败", poolId, envId, err)
	}

	updateAccountSql := "update ? set cooling_time = ?"
	_, err = db.ExecContext(l.ctx, updateAccountSql, envTable.TableName, coolingTime)
	if err != nil {
		return nil, fmt.Errorf("pool_id为：%d， env_id为：%d更新池账号冷却时间发生错误：「%s」，修改失败", poolId, envId, err)
	}

	selectEnvSql := fmt.Sprintf(
		"" +
			"select count(1) total from t_pool_table " +
			"where (pool_name=? or description=?) and parent_pool_id=? and id !=?",
	)
	rows, err := db.QueryContext(l.ctx, selectEnvSql, envName, description, poolId, envId)
	if err != nil {
		return nil, err
	}
	columns, _ := rows.Columns()
	count := len(columns)
	valueSlice := make([]any, count)
	valuePtrSlice := make([]any, count)
	var total int64
	for rows.Next() {
		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			total = cast.ToInt64(valueSlice[0])
		}
	}
	if total != 0 {
		return nil, fmt.Errorf("%s 或 %s 已经被使用，不用用于命名新的池环境", envName, description)
	}

	updateSql := fmt.Sprintf(
		"" +
			"update t_pool_table set pool_name=?, description=? where parent_pool_id=? and id=?",
	)
	_, err = db.ExecContext(l.ctx, updateSql, envName, description, poolId, envId)
	if err != nil {
		return nil, err
	}

	selectEnvDataSql := fmt.Sprintf(
		""+
			"select "+
			"count(1) as totalCount, "+
			"count(case when occupy_state='USING' then 1 end)  as occupyCount, "+
			"count(case when occupy_state='UNUSED' then 1 end) as availableCount, "+
			"count(case when occupy_state='TO_BE_UPDATE' then 1 end) as updatingCount, "+
			"count(case when occupy_state='TO_BE_DELETE' then 1 end) as deletingCount "+
			"from %s",
		envTable.TableName,
	)
	rows, err1 := db.QueryContext(l.ctx, selectEnvDataSql)
	if err1 != nil {
		return nil, err1
	}
	columns1, _ := rows.Columns()
	count1 := len(columns1)
	valueSlice1 := make([]any, count1)
	valuePtrSlice1 := make([]any, count1)
	var totalCount int64
	var occupyCount int64
	var availableCount int64
	var updatingCount int64
	var deletingCount int64
	for rows.Next() {
		for i := range columns1 {
			valuePtrSlice1[i] = &valueSlice1[i]
		}
		err := rows.Scan(valuePtrSlice1...)
		if err == nil {
			totalCountValue := valueSlice1[0].([]uint8)
			totalCountValueStr := string(totalCountValue)
			totalCount, _ = strconv.ParseInt(totalCountValueStr, 0, 0)

			occupyCountValue := valueSlice1[1].([]uint8)
			occupyCountValueStr := string(occupyCountValue)
			occupyCount, _ = strconv.ParseInt(occupyCountValueStr, 0, 0)

			availableCountValue := valueSlice1[2].([]uint8)
			availableCountValueStr := string(availableCountValue)
			availableCount, _ = strconv.ParseInt(availableCountValueStr, 0, 0)

			updatingCountValue := valueSlice1[3].([]uint8)
			updatingCountValueStr := string(updatingCountValue)
			updatingCount, _ = strconv.ParseInt(updatingCountValueStr, 0, 0)

			deletingCountValue := valueSlice1[4].([]uint8)
			deletingCountValueStr := string(deletingCountValue)
			deletingCount, _ = strconv.ParseInt(deletingCountValueStr, 0, 0)
		}
	}

	envTable, _ = l.svcCtx.TPoolTableModel.FindPoolEnvTableByPoolIdEnvId(l.ctx, poolId, envId)

	var createdBy *userinfo.UserInfo
	var userErr error
	if envTable.CreatedBy == "empty" {
		createdBy = user.Admin
	} else {
		createdBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, envTable.CreatedBy)
		if userErr != nil {
			err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
			return nil, err
		}
	}

	loginUser := userinfo.FromContext(l.ctx)

	return &types.ModifyAccountPoolEnvResp{
		PoolId:         poolId,
		EnvId:          envId,
		EnvName:        envName,
		CoolingTime:    coolingTime,
		TotalCount:     totalCount,
		OccupyCount:    occupyCount,
		AvailableCount: availableCount,
		UpdatingCount:  updatingCount,
		DeletingCount:  deletingCount,
		Description:    description,
		CreatedBy:      createdBy,
		UpdatedBy:      loginUser,
		CreatedAt:      envTable.CreatedAt.UnixMilli(),
		UpdatedAt:      envTable.UpdatedAt.UnixMilli(),
	}, nil
}
