package svc

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	userService "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"

	accountService "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/client/account"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type ServiceContext struct {
	Config config.Config

	DB    sqlx.SqlConn
	Redis *redis.Redis

	TProductsModel      model.TProductsModel
	TPoolTableModel     model.TPoolTableModel
	TPoolColumnModel    model.TPoolColumnModel
	TExecuteRecordModel model.TExecuteRecordModel

	UserRpc    userService.UserService
	AccountRpc accountService.Account
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)

	return &ServiceContext{
		Config:              c,
		DB:                  sqlConn,
		Redis:               redis.MustNewRedis(c.Redis, redis.WithDB(c.Redis.DB)),
		TProductsModel:      model.NewTProductsModel(sqlConn),
		TPoolTableModel:     model.NewTPoolTableModel(sqlConn),
		TPoolColumnModel:    model.NewTPoolColumnModel(sqlConn),
		TExecuteRecordModel: model.NewTExecuteRecordModel(sqlConn),
		UserRpc:             userService.NewUserService(zrpc.MustNewClient(c.UserRpc, zrpc.WithUnaryClientInterceptor(clientinterceptors.UserInfoUnaryClientInterceptor))),
		AccountRpc:          accountService.NewAccount(zrpc.MustNewClient(c.AccountRpc, zrpc.WithUnaryClientInterceptor(clientinterceptors.UserInfoUnaryClientInterceptor))),
	}
}
