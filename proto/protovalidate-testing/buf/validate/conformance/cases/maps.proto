// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";

message MapNone {
  map<uint32, bool> val = 1;
}

message MapMin {
  map<int32, float> val = 1 [(buf.validate.field).map.min_pairs = 2];
}
message MapMax {
  map<int64, double> val = 1 [(buf.validate.field).map.max_pairs = 3];
}
message MapMinMax {
  map<string, bool> val = 1 [(buf.validate.field).map = {
    min_pairs: 2
    max_pairs: 4
  }];
}
message MapExact {
  map<uint64, string> val = 1 [(buf.validate.field).map = {
    min_pairs: 3
    max_pairs: 3
  }];
}

message MapKeys {
  map<sint64, string> val = 1 [(buf.validate.field).map.keys.sint64.lt = 0];
}
message MapValues {
  map<string, string> val = 1 [(buf.validate.field).map.values.string.min_len = 3];
}

message MapKeysPattern {
  map<string, string> val = 1 [(buf.validate.field).map.keys.string.pattern = "(?i)^[a-z0-9]+$"];
}
message MapValuesPattern {
  map<string, string> val = 1 [(buf.validate.field).map.values.string.pattern = "(?i)^[a-z0-9]+$"];
}

message MapRecursive {
  map<uint32, Msg> val = 1;
  message Msg {
    string val = 1 [(buf.validate.field).string.min_len = 3];
  }
}

message MapExactIgnore {
  map<uint64, string> val = 1 [
    (buf.validate.field).map = {
      min_pairs: 3
      max_pairs: 3
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message MultipleMaps {
  map<uint32, string> first = 1 [(buf.validate.field).map.keys.uint32.gt = 0];
  map<int32, bool> second = 2 [(buf.validate.field).map.keys.int32.lt = 0];
  map<int32, bool> third = 3 [(buf.validate.field).map.keys.int32.gt = 0];
}
