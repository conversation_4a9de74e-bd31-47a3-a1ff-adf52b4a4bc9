// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases;

import "buf/validate/conformance/cases/other_package/embed.proto";
import "buf/validate/validate.proto";

message TestMsg {
  string const = 1 [(buf.validate.field).string.const = "foo"];
  TestMsg nested = 2;
}

message MessageNone {
  NoneMsg val = 1;
  message NoneMsg {}
}

message MessageDisabled {
  option (buf.validate.message).disabled = true;
  uint64 val = 1 [(buf.validate.field).uint64.gt = 123];
}

message Message {
  TestMsg val = 1;
}
message MessageCrossPackage {
  other_package.Embed val = 1;
}
message MessageSkip {
  TestMsg val = 1 [(buf.validate.field).ignore = IGNORE_ALWAYS];
}
message MessageRequired {
  TestMsg val = 1 [(buf.validate.field).required = true];
}
message MessageRequiredButOptional {
  optional TestMsg val = 1 [(buf.validate.field).required = true];
}

message MessageRequiredOneof {
  oneof one {
    option (buf.validate.oneof).required = true;
    TestMsg val = 1 [(buf.validate.field).required = true];
  }
}

message MessageWith3dInside {}
