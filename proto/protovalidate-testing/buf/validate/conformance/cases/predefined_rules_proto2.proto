// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

extend buf.validate.FloatRules {
  optional float float_abs_range_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "float.abs_range.proto2"
    expression: "this >= -rule && this <= rule"
    message: "float value is out of range"
  }];
}

extend buf.validate.DoubleRules {
  optional double double_abs_range_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "double.abs_range.proto2"
    expression: "this >= -rule && this <= rule"
    message: "double value is out of range"
  }];
}

extend buf.validate.Int32Rules {
  repeated int32 int32_abs_in_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "int32.abs_in.proto2"
    expression: "this in rule || this in rule.map(n, -n)"
    message: "value must be in absolute value of list"
  }];
}

extend buf.validate.Int64Rules {
  repeated google.protobuf.Int64Value int64_abs_in_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "int64.abs_in.proto2"
    expression: "this in rule || this in rule.map(n, -n)"
    message: "value must be in absolute value of list"
  }];
}

extend buf.validate.UInt32Rules {
  optional bool uint32_even_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "uint32.even.proto2"
    expression: "this % 2u == 0u"
    message: "uint32 value is not even"
  }];
}

extend buf.validate.UInt64Rules {
  optional bool uint64_even_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "uint64.even.proto2"
    expression: "this % 2u == 0u"
    message: "uint64 value is not even"
  }];
}

extend buf.validate.SInt32Rules {
  optional bool sint32_even_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "sint32.even.proto2"
    expression: "this % 2 == 0"
    message: "sint32 value is not even"
  }];
}

extend buf.validate.SInt64Rules {
  optional bool sint64_even_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "sint64.even.proto2"
    expression: "this % 2 == 0"
    message: "sint64 value is not even"
  }];
}

extend buf.validate.Fixed32Rules {
  optional bool fixed32_even_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "fixed32.even.proto2"
    expression: "this % 2u == 0u"
    message: "fixed32 value is not even"
  }];
}

extend buf.validate.Fixed64Rules {
  optional bool fixed64_even_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "fixed64.even.proto2"
    expression: "this % 2u == 0u"
    message: "fixed64 value is not even"
  }];
}

extend buf.validate.SFixed32Rules {
  optional bool sfixed32_even_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "sfixed32.even.proto2"
    expression: "this % 2 == 0"
    message: "sfixed32 value is not even"
  }];
}

extend buf.validate.SFixed64Rules {
  optional bool sfixed64_even_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "sfixed64.even.proto2"
    expression: "this % 2 == 0"
    message: "sfixed64 value is not even"
  }];
}

extend buf.validate.BoolRules {
  optional bool bool_false_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "bool.false.proto2"
    expression: "this == false"
    message: "bool value is not false"
  }];
}

extend buf.validate.StringRules {
  optional bool string_valid_path_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "string.valid_path.proto2"
    expression: "!this.matches('^([^/.][^/]?|[^/][^/.]|[^/]{3,})(/([^/.][^/]?|[^/][^/.]|[^/]{3,}))*$') ? 'not a valid path: `%s`'.format([this]) : ''"
  }];
}

extend buf.validate.BytesRules {
  optional bool bytes_valid_path_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "bytes.valid_path.proto2"
    expression: "!string(this).matches('^([^/.][^/]?|[^/][^/.]|[^/]{3,})(/([^/.][^/]?|[^/][^/.]|[^/]{3,}))*$') ? 'not a valid path: `%s`'.format([this]) : ''"
  }];
}

extend buf.validate.EnumRules {
  optional bool enum_non_zero_proto2 = 1161 [(buf.validate.predefined).cel = {
    id: "enum.non_zero.proto2"
    expression: "int(this) != 0"
    message: "enum value is not non-zero"
  }];
}

extend buf.validate.RepeatedRules {
  optional bool repeated_at_least_five_proto2 = 1161 [(predefined).cel = {
    id: "repeated.at_least_five.proto2"
    expression: "uint(this.size()) >= 5u"
    message: "repeated field must have at least five values"
  }];
}

extend buf.validate.DurationRules {
  optional bool duration_too_long_proto2 = 1161 [(predefined).cel = {
    id: "duration.too_long.proto2"
    expression: "this <= duration('10s')"
    message: "duration can't be longer than 10 seconds"
  }];
}

extend buf.validate.TimestampRules {
  optional bool timestamp_in_range_proto2 = 1161 [(predefined).cel = {
    id: "timestamp.time_range.proto2"
    expression: "int(this) >= 1049587200 && int(this) <= 1080432000"
    message: "timestamp out of range"
  }];
}

message PredefinedFloatRuleProto2 {
  optional float val = 1 [(buf.validate.field).float.(float_abs_range_proto2) = 1.0];
}

message PredefinedDoubleRuleProto2 {
  optional double val = 1 [(buf.validate.field).double.(double_abs_range_proto2) = 1.0];
}

message PredefinedInt32RuleProto2 {
  optional int32 val = 1 [(buf.validate.field).int32.(int32_abs_in_proto2) = -2];
}

message PredefinedInt64RuleProto2 {
  optional int64 val = 1 [(buf.validate.field).int64.(int64_abs_in_proto2) = {value: -2}];
}

message PredefinedUInt32RuleProto2 {
  optional uint32 val = 1 [(buf.validate.field).uint32.(uint32_even_proto2) = true];
}

message PredefinedUInt64RuleProto2 {
  optional uint64 val = 1 [(buf.validate.field).uint64.(uint64_even_proto2) = true];
}

message PredefinedSInt32RuleProto2 {
  optional sint32 val = 1 [(buf.validate.field).sint32.(sint32_even_proto2) = true];
}

message PredefinedSInt64RuleProto2 {
  optional sint64 val = 1 [(buf.validate.field).sint64.(sint64_even_proto2) = true];
}

message PredefinedFixed32RuleProto2 {
  optional fixed32 val = 1 [(buf.validate.field).fixed32.(fixed32_even_proto2) = true];
}

message PredefinedFixed64RuleProto2 {
  optional fixed64 val = 1 [(buf.validate.field).fixed64.(fixed64_even_proto2) = true];
}

message PredefinedSFixed32RuleProto2 {
  optional sfixed32 val = 1 [(buf.validate.field).sfixed32.(sfixed32_even_proto2) = true];
}

message PredefinedSFixed64RuleProto2 {
  optional sfixed64 val = 1 [(buf.validate.field).sfixed64.(sfixed64_even_proto2) = true];
}

message PredefinedBoolRuleProto2 {
  optional bool val = 1 [(buf.validate.field).bool.(bool_false_proto2) = true];
}

message PredefinedStringRuleProto2 {
  optional string val = 1 [(buf.validate.field).string.(string_valid_path_proto2) = true];
}

message PredefinedBytesRuleProto2 {
  optional bytes val = 1 [(buf.validate.field).bytes.(bytes_valid_path_proto2) = true];
}

message PredefinedEnumRuleProto2 {
  enum EnumProto2 {
    ENUM_PROTO2_ZERO_UNSPECIFIED = 0;
    ENUM_PROTO2_ONE = 1;
  }
  optional EnumProto2 val = 1 [(buf.validate.field).enum.(enum_non_zero_proto2) = true];
}

message PredefinedRepeatedRuleProto2 {
  repeated uint64 val = 1 [(buf.validate.field).repeated.(repeated_at_least_five_proto2) = true];
}

message PredefinedDurationRuleProto2 {
  optional google.protobuf.Duration val = 1 [(buf.validate.field).duration.(duration_too_long_proto2) = true];
}

message PredefinedTimestampRuleProto2 {
  optional google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.(timestamp_in_range_proto2) = true];
}

message PredefinedWrappedFloatRuleProto2 {
  optional google.protobuf.FloatValue val = 1 [(buf.validate.field).float.(float_abs_range_proto2) = 1.0];
}

message PredefinedWrappedDoubleRuleProto2 {
  optional google.protobuf.DoubleValue val = 1 [(buf.validate.field).double.(double_abs_range_proto2) = 1.0];
}

message PredefinedWrappedInt32RuleProto2 {
  optional google.protobuf.Int32Value val = 1 [(buf.validate.field).int32.(int32_abs_in_proto2) = -2];
}

message PredefinedWrappedInt64RuleProto2 {
  optional google.protobuf.Int64Value val = 1 [(buf.validate.field).int64.(int64_abs_in_proto2) = {value: -2}];
}

message PredefinedWrappedUInt32RuleProto2 {
  optional google.protobuf.UInt32Value val = 1 [(buf.validate.field).uint32.(uint32_even_proto2) = true];
}

message PredefinedWrappedUInt64RuleProto2 {
  optional google.protobuf.UInt64Value val = 1 [(buf.validate.field).uint64.(uint64_even_proto2) = true];
}

message PredefinedWrappedBoolRuleProto2 {
  optional google.protobuf.BoolValue val = 1 [(buf.validate.field).bool.(bool_false_proto2) = true];
}

message PredefinedWrappedStringRuleProto2 {
  optional google.protobuf.StringValue val = 1 [(buf.validate.field).string.(string_valid_path_proto2) = true];
}

message PredefinedWrappedBytesRuleProto2 {
  optional google.protobuf.BytesValue val = 1 [(buf.validate.field).bytes.(bytes_valid_path_proto2) = true];
}

message PredefinedRepeatedWrappedFloatRuleProto2 {
  repeated google.protobuf.FloatValue val = 1 [(buf.validate.field).repeated.items.float.(float_abs_range_proto2) = 1.0];
}

message PredefinedRepeatedWrappedDoubleRuleProto2 {
  repeated google.protobuf.DoubleValue val = 1 [(buf.validate.field).repeated.items.double.(double_abs_range_proto2) = 1.0];
}

message PredefinedRepeatedWrappedInt32RuleProto2 {
  repeated google.protobuf.Int32Value val = 1 [(buf.validate.field).repeated.items.int32.(int32_abs_in_proto2) = -2];
}

message PredefinedRepeatedWrappedInt64RuleProto2 {
  repeated google.protobuf.Int64Value val = 1 [(buf.validate.field).repeated.items.int64.(int64_abs_in_proto2) = {value: -2}];
}

message PredefinedRepeatedWrappedUInt32RuleProto2 {
  repeated google.protobuf.UInt32Value val = 1 [(buf.validate.field).repeated.items.uint32.(uint32_even_proto2) = true];
}

message PredefinedRepeatedWrappedUInt64RuleProto2 {
  repeated google.protobuf.UInt64Value val = 1 [(buf.validate.field).repeated.items.uint64.(uint64_even_proto2) = true];
}

message PredefinedRepeatedWrappedBoolRuleProto2 {
  repeated google.protobuf.BoolValue val = 1 [(buf.validate.field).repeated.items.bool.(bool_false_proto2) = true];
}

message PredefinedRepeatedWrappedStringRuleProto2 {
  repeated google.protobuf.StringValue val = 1 [(buf.validate.field).repeated.items.string.(string_valid_path_proto2) = true];
}

message PredefinedRepeatedWrappedBytesRuleProto2 {
  repeated google.protobuf.BytesValue val = 1 [(buf.validate.field).repeated.items.bytes.(bytes_valid_path_proto2) = true];
}

message PredefinedAndCustomRuleProto2 {
  optional sint32 a = 1 [
    (field).cel = {
      id: "predefined_and_custom_rule_scalar_proto2"
      expression: "this > 24 ? '' : 'a must be greater than 24'"
    },
    (field).sint32.(sint32_even_proto2) = true
  ];

  optional Nested b = 2 [(field).cel = {
    id: "predefined_and_custom_rule_embedded_proto2"
    message: "b.c must be a multiple of 3"
    expression: "this.c % 3 == 0"
  }];

  message Nested {
    optional sint32 c = 1 [
      (field).cel = {
        id: "predefined_and_custom_rule_nested_proto2"
        expression: "this > 0 ? '' : 'c must be positive'"
      },
      (field).sint32.(sint32_even_proto2) = true
    ];
  }
}

message StandardPredefinedAndCustomRuleProto2 {
  optional sint32 a = 1 [
    (field).sint32.lt = 28,
    (field).sint32.(sint32_even_proto2) = true,
    (field).cel = {
      id: "standard_predefined_and_custom_rule_scalar_proto2"
      expression: "this > 24 ? '' : 'a must be greater than 24'"
    }
  ];
}
