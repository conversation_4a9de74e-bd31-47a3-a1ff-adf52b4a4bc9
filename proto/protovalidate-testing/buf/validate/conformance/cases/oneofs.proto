// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";

message TestOneofMsg {
  bool val = 1 [(buf.validate.field).bool.const = true];
}

message OneofNone {
  oneof o {
    string x = 1;
    int32 y = 2;
  }
}

message Oneof {
  oneof o {
    string x = 1 [(buf.validate.field).string.prefix = "foo"];
    int32 y = 2 [(buf.validate.field).int32.gt = 0];
    TestOneofMsg z = 3;
  }
}

message OneofRequired {
  oneof o {
    option (buf.validate.oneof).required = true;

    string x = 1;
    int32 y = 2;
    int32 name_with_underscores = 3;
    int32 under_and_1_number = 4;
  }
}

message OneofRequiredWithRequiredField {
  oneof o {
    option (buf.validate.oneof).required = true;

    string a = 1 [(buf.validate.field).required = true];
    string b = 2;
  }
}
