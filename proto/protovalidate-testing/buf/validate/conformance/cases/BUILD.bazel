# Copyright 2023 Buf Technologies, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@rules_buf//buf:defs.bzl", "buf_lint_test")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "buf_validate_conformance_cases_proto",
    srcs = [
        "bool.proto",
        "bytes.proto",
        "enums.proto",
        "filename-with-dash.proto",
        "ignore_empty_proto2.proto",
        "ignore_empty_proto3.proto",
        "ignore_empty_proto_editions.proto",
        "ignore_proto2.proto",
        "ignore_proto3.proto",
        "ignore_proto_editions.proto",
        "kitchen_sink.proto",
        "maps.proto",
        "messages.proto",
        "numbers.proto",
        "oneofs.proto",
        "predefined_rules_proto2.proto",
        "predefined_rules_proto3.proto",
        "predefined_rules_proto_editions.proto",
        "repeated.proto",
        "required_field_proto2.proto",
        "required_field_proto3.proto",
        "required_field_proto_editions.proto",
        "strings.proto",
        "wkt_any.proto",
        "wkt_duration.proto",
        "wkt_nested.proto",
        "wkt_timestamp.proto",
        "wkt_wrappers.proto",
    ],
    strip_import_prefix = "/proto/protovalidate-testing",
    visibility = ["//visibility:public"],
    deps = [
        "//proto/protovalidate-testing/buf/validate/conformance/cases/other_package:buf_validate_conformance_cases_other_package_proto",
        "//proto/protovalidate-testing/buf/validate/conformance/cases/yet_another_package:buf_validate_conformance_cases_yet_another_package_proto",
        "//proto/protovalidate/buf/validate:validate_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

buf_lint_test(
    name = "buf_validate_conformance_cases_proto_lint",
    config = "//proto/protovalidate-testing:buf.yaml",
    targets = [":buf_validate_conformance_cases_proto"],
)

cc_proto_library(
    name = "buf_validate_conformance_cases_proto_cc",
    visibility = ["//visibility:public"],
    deps = [":buf_validate_conformance_cases_proto"],
)
