// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";
import "google/protobuf/any.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

enum ComplexTestEnum {
  COMPLEX_TEST_ENUM_UNSPECIFIED = 0;
  COMPLEX_TEST_ENUM_ONE = 1;
  COMPLEX_TEST_ENUM_TWO = 2;
}

message ComplexTestMsg {
  string const = 1 [(buf.validate.field).string.const = "abcd"];
  ComplexTestMsg nested = 2;
  int32 int_const = 3 [(buf.validate.field).int32.const = 5];
  bool bool_const = 4 [(buf.validate.field).bool.const = false];
  google.protobuf.FloatValue float_val = 5 [(buf.validate.field).float.gt = 0];
  google.protobuf.Duration dur_val = 6 [
    (buf.validate.field).duration.lt = {seconds: 17},
    (buf.validate.field).required = true
  ];
  google.protobuf.Timestamp ts_val = 7 [(buf.validate.field).timestamp.gt = {seconds: 7}];
  ComplexTestMsg another = 8;
  float float_const = 9 [(buf.validate.field).float.lt = 8];
  double double_in = 10 [(buf.validate.field).double = {
    in: [
      456.789,
      123
    ]
  }];
  ComplexTestEnum enum_const = 11 [(buf.validate.field).enum.const = 2];
  google.protobuf.Any any_val = 12 [(buf.validate.field).any = {
    in: ["type.googleapis.com/google.protobuf.Duration"]
  }];
  repeated google.protobuf.Timestamp rep_ts_val = 13 [(buf.validate.field).repeated = {
    items: {
      timestamp: {
        gte: {nanos: 1000000}
      }
    }
  }];
  map<sint32, string> map_val = 14 [(buf.validate.field).map.keys.sint32.lt = 0];
  bytes bytes_val = 15 [(buf.validate.field).bytes.const = "\x00\x99"];
  oneof o {
    option (buf.validate.oneof).required = true;

    string x = 16;
    int32 y = 17;
  }
}

message KitchenSinkMessage {
  ComplexTestMsg val = 1;
}
