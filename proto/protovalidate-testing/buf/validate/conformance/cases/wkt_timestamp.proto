// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";

message TimestampNone {
  google.protobuf.Timestamp val = 1;
}
message TimestampRequired {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).required = true];
}
message TimestampConst {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.const = {seconds: 3}];
}

message TimestampLT {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.lt = {}];
}
message TimestampLTE {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.lte = {seconds: 1}];
}
message TimestampGT {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.gt = {nanos: 1000}];
}
message TimestampGTE {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.gte = {nanos: 1000000}];
}
message TimestampGTLT {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp = {
    gt: {}
    lt: {seconds: 1}
  }];
}
message TimestampExLTGT {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp = {
    lt: {}
    gt: {seconds: 1}
  }];
}
message TimestampGTELTE {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp = {
    gte: {seconds: 60}
    lte: {seconds: 3600}
  }];
}
message TimestampExGTELTE {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp = {
    lte: {seconds: 60}
    gte: {seconds: 3600}
  }];
}

message TimestampLTNow {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.lt_now = true];
}
message TimestampNotLTNow {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.lt_now = false];
}
message TimestampGTNow {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.gt_now = true];
}
message TimestampNotGTNow {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.gt_now = false];
}

message TimestampWithin {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.within.seconds = 3600];
}

message TimestampLTNowWithin {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp = {
    lt_now: true
    within: {seconds: 3600}
  }];
}
message TimestampGTNowWithin {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp = {
    gt_now: true
    within: {seconds: 3600}
  }];
}

message TimestampExample {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.example = {seconds: 3}];
}
