// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases.custom_constraints;

import "buf/validate/validate.proto";

enum Enum {
  ENUM_UNSPECIFIED = 0;
  ENUM_ONE = 1;
}

// A message that does not contain any expressions
message NoExpressions {
  int32 a = 1;
  Enum b = 2;
  Nested c = 3;

  message Nested {}
}

// A message with message-level custom expressions
message MessageExpressions {
  option (message).cel = {
    id: "message_expression_scalar"
    message: "a must be less than b"
    expression: "this.a < this.b"
  };
  option (message).cel = {
    id: "message_expression_enum"
    message: "c must not equal d"
    expression: "this.c != this.d"
  };
  option (message).cel = {
    id: "message_expression_embed"
    message: "e.a must equal f.a"
    expression: "this.e.a == this.f.a"
  };

  int32 a = 1;
  int32 b = 2;
  Enum c = 3;
  Enum d = 4;
  Nested e = 5;
  Nested f = 6;

  message Nested {
    option (message).cel = {
      id: "message_expression_nested"
      expression:
        "this.a > this.b ? ''"
        ": 'a must be greater than b'"
    };

    int32 a = 1;
    int32 b = 2;
  }
}

message FieldExpressions {
  int32 a = 1 [(field).cel = {
    id: "field_expression_scalar"
    expression:
      "this > 42 ? ''"
      ": 'a must be greater than 42'"
  }];
  Enum b = 2 [(field).cel = {
    id: "field_expression_enum"
    message: "b must be ~ONE"
    expression: "this == 1"
  }];
  Nested c = 3 [(field).cel = {
    id: "field_expression_embed"
    message: "c.a must be a multiple of 4"
    expression: "this.a % 4 == 0"
  }];
  int32 d = 4 [
    (field).cel = {
      id: "field_expression_scalar_multiple_1"
      expression:
        "this < 1 ? ''"
        ": 'd must be less than 1'"
    },
    (field).cel = {
      id: "field_expression_scalar_multiple_2"
      expression:
        "this < 2 ? ''"
        ": 'd must be less than 2'"
    }
  ];

  message Nested {
    int32 a = 1 [(field).cel = {
      id: "field_expression_nested"
      expression:
        "this > 0 ? ''"
        ": 'a must be positive'"
    }];
  }
}

message MissingField {
  option (message).cel = {
    id: "missing_field"
    message: "b must be positive"
    expression: "this.b > 0"
  };

  int32 a = 1;
}

message IncorrectType {
  option (message).cel = {
    id: "incorrect_type"
    message: "a must start with 'foo'"
    expression: "this.a.startsWith('foo')"
  };

  int32 a = 1;
}

message DynRuntimeError {
  option (message).cel = {
    id: "dyn_runtime_err"
    message: "dynamic type tries to use a non-existent field"
    expression: "dyn(this).b == 'foo'"
  };

  int32 a = 1;
}

message NowEqualsNow {
  option (message).cel = {
    id: "now_equals_now"
    message: "now should equal now within an expression"
    expression: "now == now"
  };
}
