// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";
import "google/protobuf/wrappers.proto";

message WrapperNone {
  google.protobuf.Int32Value val = 1;
}

message WrapperFloat {
  google.protobuf.FloatValue val = 1 [(buf.validate.field).float.gt = 0];
}
message WrapperDouble {
  google.protobuf.DoubleValue val = 1 [(buf.validate.field).double.gt = 0];
}
message WrapperInt64 {
  google.protobuf.Int64Value val = 1 [(buf.validate.field).int64.gt = 0];
}
message WrapperInt32 {
  google.protobuf.Int32Value val = 1 [(buf.validate.field).int32.gt = 0];
}
message WrapperUInt64 {
  google.protobuf.UInt64Value val = 1 [(buf.validate.field).uint64.gt = 0];
}
message WrapperUInt32 {
  google.protobuf.UInt32Value val = 1 [(buf.validate.field).uint32.gt = 0];
}
message WrapperBool {
  google.protobuf.BoolValue val = 1 [(buf.validate.field).bool.const = true];
}
message WrapperString {
  google.protobuf.StringValue val = 1 [(buf.validate.field).string.suffix = "bar"];
}
message WrapperBytes {
  google.protobuf.BytesValue val = 1 [(buf.validate.field).bytes.min_len = 3];
}
message WrapperRequiredString {
  google.protobuf.StringValue val = 1 [
    (buf.validate.field).string.const = "bar",
    (buf.validate.field).required = true
  ];
}
message WrapperRequiredEmptyString {
  google.protobuf.StringValue val = 1 [
    (buf.validate.field).string.const = "",
    (buf.validate.field).required = true
  ];
}
message WrapperOptionalUuidString {
  google.protobuf.StringValue val = 1 [
    (buf.validate.field).string.uuid = true,
    (buf.validate.field).required = false
  ];
}
message WrapperRequiredFloat {
  google.protobuf.FloatValue val = 1 [
    (buf.validate.field).float.gt = 0,
    (buf.validate.field).required = true
  ];
}
