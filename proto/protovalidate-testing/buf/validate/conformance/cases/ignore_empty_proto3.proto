// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";

message IgnoreEmptyProto3Scalar {
  int32 val = 1 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).int32.gt = 0
  ];
}

message IgnoreEmptyProto3OptionalScalar {
  optional int32 val = 1 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).int32.gt = 0
  ];
}

message IgnoreEmptyProto3Message {
  optional Msg val = 1 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).cel = {
      id: "ignore_empty.proto3.message"
      message: "foobar"
      expression: "this.val == 'foo'"
    }
  ];
  message Msg {
    string val = 1;
  }
}

message IgnoreEmptyProto3Oneof {
  oneof o {
    int32 val = 1 [
      (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
      (buf.validate.field).int32.gt = 0
    ];
  }
}

message IgnoreEmptyProto3Repeated {
  repeated int32 val = 1 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).repeated.min_items = 3
  ];
}

message IgnoreEmptyProto3Map {
  map<int32, int32> val = 1 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).map.min_pairs = 3
  ];
}

message IgnoreEmptyRepeatedItems {
  repeated int32 val = 1 [(buf.validate.field).repeated.items = {
    ignore: IGNORE_IF_UNPOPULATED
    int32: {gt: 0}
  }];
}

message IgnoreEmptyMapPairs {
  map<string, int32> val = 1 [
    (buf.validate.field).map.keys = {
      ignore: IGNORE_IF_UNPOPULATED
      string: {min_len: 3}
    },
    (buf.validate.field).map.values = {
      ignore: IGNORE_IF_UNPOPULATED
      int32: {gt: 0}
    }
  ];
}
