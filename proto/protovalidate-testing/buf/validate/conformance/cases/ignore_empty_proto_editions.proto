// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

edition = "2023";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";

message IgnoreEmptyEditionsScalarExplicitPresence {
  int32 val = 1 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).int32.gt = 0
  ];
}

message IgnoreEmptyEditionsScalarExplicitPresenceWithDefault {
  int32 val = 1 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).int32.gt = 0,
    default = 42
  ];
}

message IgnoreEmptyEditionsScalarImplicitPresence {
  int32 val = 1 [
    features.field_presence = IMPLICIT,
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).int32.gt = 0
  ];
}

message IgnoreEmptyEditionsScalarLegacyRequired {
  int32 val = 1 [
    features.field_presence = LEGACY_REQUIRED,
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).int32.gt = 0
  ];
}

message IgnoreEmptyEditionsScalarLegacyRequiredWithDefault {
  int32 val = 1 [
    features.field_presence = LEGACY_REQUIRED,
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).int32.gt = 0,
    default = 42
  ];
}

message IgnoreEmptyEditionsMessageExplicitPresence {
  Msg val = 1 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).cel = {
      id: "ignore_empty.editions.message"
      message: "foobar"
      expression: "this.val == 'foo'"
    }
  ];
  message Msg {
    string val = 1;
  }
}

message IgnoreEmptyEditionsMessageExplicitPresenceDelimited {
  Msg val = 1 [
    features.message_encoding = DELIMITED,
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).cel = {
      id: "ignore_empty.editions.message"
      message: "foobar"
      expression: "this.val == 'foo'"
    }
  ];
  message Msg {
    string val = 1;
  }
}

message IgnoreEmptyEditionsMessageLegacyRequired {
  Msg val = 1 [
    features.field_presence = LEGACY_REQUIRED,
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).cel = {
      id: "ignore_empty.editions.message"
      message: "foobar"
      expression: "this.val == 'foo'"
    }
  ];
  message Msg {
    string val = 1;
  }
}

message IgnoreEmptyEditionsMessageLegacyRequiredDelimited {
  Msg val = 1 [
    features.message_encoding = DELIMITED,
    features.field_presence = LEGACY_REQUIRED,
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).cel = {
      id: "ignore_empty.editions.message"
      message: "foobar"
      expression: "this.val == 'foo'"
    }
  ];
  message Msg {
    string val = 1;
  }
}

message IgnoreEmptyEditionsOneof {
  oneof o {
    int32 val = 1 [
      (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
      (buf.validate.field).int32.gt = 0
    ];
  }
}

message IgnoreEmptyEditionsRepeated {
  repeated int32 val = 1 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).repeated.min_items = 3
  ];
}

message IgnoreEmptyEditionsRepeatedExpanded {
  repeated int32 val = 1 [
    features.repeated_field_encoding = EXPANDED,
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).repeated.min_items = 3
  ];
}

message IgnoreEmptyEditionsMap {
  map<int32, int32> val = 1 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).map.min_pairs = 3
  ];
}
