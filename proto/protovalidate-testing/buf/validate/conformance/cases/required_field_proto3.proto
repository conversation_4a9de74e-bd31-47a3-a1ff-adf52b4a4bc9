// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";

message RequiredProto3Scalar {
  string val = 1 [(buf.validate.field).required = true];
}

message RequiredProto3OptionalScalar {
  optional string val = 1 [(buf.validate.field).required = true];
}

message RequiredProto3Message {
  Msg val = 1 [(buf.validate.field).required = true];
  message Msg {
    string val = 1;
  }
}

message RequiredProto3OneOf {
  oneof val {
    string a = 1 [(buf.validate.field).required = true];
    string b = 2;
  }
}

message RequiredProto3Repeated {
  repeated string val = 1 [(buf.validate.field).required = true];
}

message RequiredProto3Map {
  map<string, string> val = 1 [(buf.validate.field).required = true];
}
