// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

edition = "2023";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

extend buf.validate.FloatRules {
  float float_abs_range_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "float.abs_range.edition_2023"
    expression: "this >= -rule && this <= rule"
    message: "float value is out of range"
  }];
}

extend buf.validate.DoubleRules {
  double double_abs_range_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "double.abs_range.edition_2023"
    expression: "this >= -rule && this <= rule"
    message: "double value is out of range"
  }];
}

extend buf.validate.Int32Rules {
  repeated int32 int32_abs_in_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "int32.abs_in.edition_2023"
    expression: "this in rule || this in rule.map(n, -n)"
    message: "value must be in absolute value of list"
  }];
}

extend buf.validate.Int64Rules {
  repeated google.protobuf.Int64Value int64_abs_in_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "int64.abs_in.edition_2023"
    expression: "this in rule || this in rule.map(n, -n)"
    message: "value must be in absolute value of list"
  }];
}

extend buf.validate.UInt32Rules {
  bool uint32_even_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "uint32.even.edition_2023"
    expression: "this % 2u == 0u"
    message: "uint32 value is not even"
  }];
}

extend buf.validate.UInt64Rules {
  bool uint64_even_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "uint64.even.edition_2023"
    expression: "this % 2u == 0u"
    message: "uint64 value is not even"
  }];
}

extend buf.validate.SInt32Rules {
  bool sint32_even_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "sint32.even.edition_2023"
    expression: "this % 2 == 0"
    message: "sint32 value is not even"
  }];
}

extend buf.validate.SInt64Rules {
  bool sint64_even_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "sint64.even.edition_2023"
    expression: "this % 2 == 0"
    message: "sint64 value is not even"
  }];
}

extend buf.validate.Fixed32Rules {
  bool fixed32_even_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "fixed32.even.edition_2023"
    expression: "this % 2u == 0u"
    message: "fixed32 value is not even"
  }];
}

extend buf.validate.Fixed64Rules {
  bool fixed64_even_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "fixed64.even.edition_2023"
    expression: "this % 2u == 0u"
    message: "fixed64 value is not even"
  }];
}

extend buf.validate.SFixed32Rules {
  bool sfixed32_even_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "sfixed32.even.edition_2023"
    expression: "this % 2 == 0"
    message: "sfixed32 value is not even"
  }];
}

extend buf.validate.SFixed64Rules {
  bool sfixed64_even_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "sfixed64.even.edition_2023"
    expression: "this % 2 == 0"
    message: "sfixed64 value is not even"
  }];
}

extend buf.validate.BoolRules {
  bool bool_false_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "bool.false.edition_2023"
    expression: "this == false"
    message: "bool value is not false"
  }];
}

extend buf.validate.StringRules {
  bool string_valid_path_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "string.valid_path.edition_2023"
    expression: "!this.matches('^([^/.][^/]?|[^/][^/.]|[^/]{3,})(/([^/.][^/]?|[^/][^/.]|[^/]{3,}))*$') ? 'not a valid path: `%s`'.format([this]) : ''"
  }];
}

extend buf.validate.BytesRules {
  bool bytes_valid_path_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "bytes.valid_path.edition_2023"
    expression: "!string(this).matches('^([^/.][^/]?|[^/][^/.]|[^/]{3,})(/([^/.][^/]?|[^/][^/.]|[^/]{3,}))*$') ? 'not a valid path: `%s`'.format([this]) : ''"
  }];
}

extend buf.validate.EnumRules {
  bool enum_non_zero_edition_2023 = 1162 [(buf.validate.predefined).cel = {
    id: "enum.non_zero.edition_2023"
    expression: "int(this) != 0"
    message: "enum value is not non-zero"
  }];
}

extend buf.validate.RepeatedRules {
  bool repeated_at_least_five_edition_2023 = 1162 [(predefined).cel = {
    id: "repeated.at_least_five.edition_2023"
    expression: "uint(this.size()) >= 5u"
    message: "repeated field must have at least five values"
  }];
}

extend buf.validate.MapRules {
  bool map_at_least_five_edition_2023 = 1162 [(predefined).cel = {
    id: "map.at_least_five.edition_2023"
    expression: "uint(this.size()) >= 5u"
    message: "map must have at least five pairs"
  }];
}

extend buf.validate.DurationRules {
  bool duration_too_long_edition_2023 = 1162 [(predefined).cel = {
    id: "duration.too_long.edition_2023"
    expression: "this <= duration('10s')"
    message: "duration can't be longer than 10 seconds"
  }];
}

extend buf.validate.TimestampRules {
  bool timestamp_in_range_edition_2023 = 1162 [(predefined).cel = {
    id: "timestamp.time_range.edition_2023"
    expression: "int(this) >= 1049587200 && int(this) <= 1080432000"
    message: "timestamp out of range"
  }];
}

message PredefinedFloatRuleEdition2023 {
  float val = 1 [(buf.validate.field).float.(float_abs_range_edition_2023) = 1.0];
}

message PredefinedDoubleRuleEdition2023 {
  double val = 1 [(buf.validate.field).double.(double_abs_range_edition_2023) = 1.0];
}

message PredefinedInt32RuleEdition2023 {
  int32 val = 1 [(buf.validate.field).int32.(int32_abs_in_edition_2023) = -2];
}

message PredefinedInt64RuleEdition2023 {
  int64 val = 1 [(buf.validate.field).int64.(int64_abs_in_edition_2023) = {value: -2}];
}

message PredefinedUInt32RuleEdition2023 {
  uint32 val = 1 [(buf.validate.field).uint32.(uint32_even_edition_2023) = true];
}

message PredefinedUInt64RuleEdition2023 {
  uint64 val = 1 [(buf.validate.field).uint64.(uint64_even_edition_2023) = true];
}

message PredefinedSInt32RuleEdition2023 {
  sint32 val = 1 [(buf.validate.field).sint32.(sint32_even_edition_2023) = true];
}

message PredefinedSInt64RuleEdition2023 {
  sint64 val = 1 [(buf.validate.field).sint64.(sint64_even_edition_2023) = true];
}

message PredefinedFixed32RuleEdition2023 {
  fixed32 val = 1 [(buf.validate.field).fixed32.(fixed32_even_edition_2023) = true];
}

message PredefinedFixed64RuleEdition2023 {
  fixed64 val = 1 [(buf.validate.field).fixed64.(fixed64_even_edition_2023) = true];
}

message PredefinedSFixed32RuleEdition2023 {
  sfixed32 val = 1 [(buf.validate.field).sfixed32.(sfixed32_even_edition_2023) = true];
}

message PredefinedSFixed64RuleEdition2023 {
  sfixed64 val = 1 [(buf.validate.field).sfixed64.(sfixed64_even_edition_2023) = true];
}

message PredefinedBoolRuleEdition2023 {
  bool val = 1 [(buf.validate.field).bool.(bool_false_edition_2023) = true];
}

message PredefinedStringRuleEdition2023 {
  string val = 1 [(buf.validate.field).string.(string_valid_path_edition_2023) = true];
}

message PredefinedBytesRuleEdition2023 {
  bytes val = 1 [(buf.validate.field).bytes.(bytes_valid_path_edition_2023) = true];
}

message PredefinedEnumRuleEdition2023 {
  enum EnumEdition2023 {
    ENUM_EDITION2023_ZERO_UNSPECIFIED = 0;
    ENUM_EDITION2023_ONE = 1;
  }
  EnumEdition2023 val = 1 [(buf.validate.field).enum.(enum_non_zero_edition_2023) = true];
}

message PredefinedRepeatedRuleEdition2023 {
  repeated uint64 val = 1 [(buf.validate.field).repeated.(repeated_at_least_five_edition_2023) = true];
}

message PredefinedMapRuleEdition2023 {
  map<uint64, uint64> val = 1 [(buf.validate.field).map.(map_at_least_five_edition_2023) = true];
}

message PredefinedDurationRuleEdition2023 {
  google.protobuf.Duration val = 1 [(buf.validate.field).duration.(duration_too_long_edition_2023) = true];
}

message PredefinedTimestampRuleEdition2023 {
  google.protobuf.Timestamp val = 1 [(buf.validate.field).timestamp.(timestamp_in_range_edition_2023) = true];
}

message PredefinedWrappedFloatRuleEdition2023 {
  google.protobuf.FloatValue val = 1 [(buf.validate.field).float.(float_abs_range_edition_2023) = 1.0];
}

message PredefinedWrappedDoubleRuleEdition2023 {
  google.protobuf.DoubleValue val = 1 [(buf.validate.field).double.(double_abs_range_edition_2023) = 1.0];
}

message PredefinedWrappedInt32RuleEdition2023 {
  google.protobuf.Int32Value val = 1 [(buf.validate.field).int32.(int32_abs_in_edition_2023) = -2];
}

message PredefinedWrappedInt64RuleEdition2023 {
  google.protobuf.Int64Value val = 1 [(buf.validate.field).int64.(int64_abs_in_edition_2023) = {value: -2}];
}

message PredefinedWrappedUInt32RuleEdition2023 {
  google.protobuf.UInt32Value val = 1 [(buf.validate.field).uint32.(uint32_even_edition_2023) = true];
}

message PredefinedWrappedUInt64RuleEdition2023 {
  google.protobuf.UInt64Value val = 1 [(buf.validate.field).uint64.(uint64_even_edition_2023) = true];
}

message PredefinedWrappedBoolRuleEdition2023 {
  google.protobuf.BoolValue val = 1 [(buf.validate.field).bool.(bool_false_edition_2023) = true];
}

message PredefinedWrappedStringRuleEdition2023 {
  google.protobuf.StringValue val = 1 [(buf.validate.field).string.(string_valid_path_edition_2023) = true];
}

message PredefinedWrappedBytesRuleEdition2023 {
  google.protobuf.BytesValue val = 1 [(buf.validate.field).bytes.(bytes_valid_path_edition_2023) = true];
}

message PredefinedRepeatedWrappedFloatRuleEdition2023 {
  repeated google.protobuf.FloatValue val = 1 [(buf.validate.field).repeated.items.float.(float_abs_range_edition_2023) = 1.0];
}

message PredefinedRepeatedWrappedDoubleRuleEdition2023 {
  repeated google.protobuf.DoubleValue val = 1 [(buf.validate.field).repeated.items.double.(double_abs_range_edition_2023) = 1.0];
}

message PredefinedRepeatedWrappedInt32RuleEdition2023 {
  repeated google.protobuf.Int32Value val = 1 [(buf.validate.field).repeated.items.int32.(int32_abs_in_edition_2023) = -2];
}

message PredefinedRepeatedWrappedInt64RuleEdition2023 {
  repeated google.protobuf.Int64Value val = 1 [(buf.validate.field).repeated.items.int64.(int64_abs_in_edition_2023) = {value: -2}];
}

message PredefinedRepeatedWrappedUInt32RuleEdition2023 {
  repeated google.protobuf.UInt32Value val = 1 [(buf.validate.field).repeated.items.uint32.(uint32_even_edition_2023) = true];
}

message PredefinedRepeatedWrappedUInt64RuleEdition2023 {
  repeated google.protobuf.UInt64Value val = 1 [(buf.validate.field).repeated.items.uint64.(uint64_even_edition_2023) = true];
}

message PredefinedRepeatedWrappedBoolRuleEdition2023 {
  repeated google.protobuf.BoolValue val = 1 [(buf.validate.field).repeated.items.bool.(bool_false_edition_2023) = true];
}

message PredefinedRepeatedWrappedStringRuleEdition2023 {
  repeated google.protobuf.StringValue val = 1 [(buf.validate.field).repeated.items.string.(string_valid_path_edition_2023) = true];
}

message PredefinedRepeatedWrappedBytesRuleEdition2023 {
  repeated google.protobuf.BytesValue val = 1 [(buf.validate.field).repeated.items.bytes.(bytes_valid_path_edition_2023) = true];
}

message PredefinedAndCustomRuleEdition2023 {
  sint32 a = 1 [
    (field).cel = {
      id: "predefined_and_custom_rule_scalar_edition_2023"
      expression: "this > 24 ? '' : 'a must be greater than 24'"
    },
    (field).sint32.(sint32_even_edition_2023) = true
  ];

  Nested b = 2 [(field).cel = {
    id: "predefined_and_custom_rule_embedded_edition_2023"
    message: "b.c must be a multiple of 3"
    expression: "this.c % 3 == 0"
  }];

  message Nested {
    sint32 c = 1 [
      (field).cel = {
        id: "predefined_and_custom_rule_nested_edition_2023"
        expression: "this > 0 ? '' : 'c must be positive'"
      },
      (field).sint32.(sint32_even_edition_2023) = true
    ];
  }
}

message StandardPredefinedAndCustomRuleEdition2023 {
  sint32 a = 1 [
    (field).sint32.lt = 28,
    (field).sint32.(sint32_even_edition_2023) = true,
    (field).cel = {
      id: "standard_predefined_and_custom_rule_scalar_edition_2023"
      expression: "this > 24 ? '' : 'a must be greater than 24'"
    }
  ];
}
