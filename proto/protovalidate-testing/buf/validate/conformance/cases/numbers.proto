// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";

message FloatNone {
  float val = 1;
}
message FloatConst {
  float val = 1 [(buf.validate.field).float.const = 1.23];
}
message FloatIn {
  float val = 1 [(buf.validate.field).float = {
    in: [
      4.56,
      7.89
    ]
  }];
}
message FloatNotIn {
  float val = 1 [(buf.validate.field).float = {
    not_in: [0]
  }];
}
message FloatLT {
  float val = 1 [(buf.validate.field).float.lt = 0];
}
message FloatLTE {
  float val = 1 [(buf.validate.field).float.lte = 64];
}
message FloatGT {
  float val = 1 [(buf.validate.field).float.gt = 16];
}
message FloatGTE {
  float val = 1 [(buf.validate.field).float.gte = 8];
}
message FloatGTLT {
  float val = 1 [(buf.validate.field).float = {
    gt: 0
    lt: 10
  }];
}
message FloatExLTGT {
  float val = 1 [(buf.validate.field).float = {
    lt: 0
    gt: 10
  }];
}
message FloatGTELTE {
  float val = 1 [(buf.validate.field).float = {
    gte: 128
    lte: 256
  }];
}
message FloatExGTELTE {
  float val = 1 [(buf.validate.field).float = {
    lte: 128
    gte: 256
  }];
}
message FloatFinite {
  float val = 1 [(buf.validate.field).float.finite = true];
}
message FloatNotFinite {
  float val = 1 [(buf.validate.field).float.finite = false];
}
message FloatIgnore {
  float val = 1 [
    (buf.validate.field).float = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message FloatIncorrectType {
  float val = 1 [(buf.validate.field).double.gt = 0];
}

message FloatExample {
  float val = 1 [(buf.validate.field).float.example = 8];
}

message DoubleNone {
  double val = 1;
}
message DoubleConst {
  double val = 1 [(buf.validate.field).double.const = 1.23];
}
message DoubleIn {
  double val = 1 [(buf.validate.field).double = {
    in: [
      4.56,
      7.89
    ]
  }];
}
message DoubleNotIn {
  double val = 1 [(buf.validate.field).double = {
    not_in: [0]
  }];
}
message DoubleLT {
  double val = 1 [(buf.validate.field).double.lt = 0];
}
message DoubleLTE {
  double val = 1 [(buf.validate.field).double.lte = 64];
}
message DoubleGT {
  double val = 1 [(buf.validate.field).double.gt = 16];
}
message DoubleGTE {
  double val = 1 [(buf.validate.field).double.gte = 8];
}
message DoubleGTLT {
  double val = 1 [(buf.validate.field).double = {
    gt: 0
    lt: 10
  }];
}
message DoubleExLTGT {
  double val = 1 [(buf.validate.field).double = {
    lt: 0
    gt: 10
  }];
}
message DoubleGTELTE {
  double val = 1 [(buf.validate.field).double = {
    gte: 128
    lte: 256
  }];
}
message DoubleExGTELTE {
  double val = 1 [(buf.validate.field).double = {
    lte: 128
    gte: 256
  }];
}
message DoubleFinite {
  double val = 1 [(buf.validate.field).double.finite = true];
}
message DoubleNotFinite {
  double val = 1 [(buf.validate.field).double.finite = false];
}
message DoubleIgnore {
  double val = 1 [
    (buf.validate.field).double = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message DoubleIncorrectType {
  double val = 1 [(buf.validate.field).float.gt = 0];
}

message DoubleExample {
  double val = 1 [(buf.validate.field).double.example = 0];
}

message Int32None {
  int32 val = 1;
}
message Int32Const {
  int32 val = 1 [(buf.validate.field).int32.const = 1];
}
message Int32In {
  int32 val = 1 [(buf.validate.field).int32 = {
    in: [
      2,
      3
    ]
  }];
}
message Int32NotIn {
  int32 val = 1 [(buf.validate.field).int32 = {
    not_in: [0]
  }];
}
message Int32LT {
  int32 val = 1 [(buf.validate.field).int32.lt = 0];
}
message Int32LTE {
  int32 val = 1 [(buf.validate.field).int32.lte = 64];
}
message Int32GT {
  int32 val = 1 [(buf.validate.field).int32.gt = 16];
}
message Int32GTE {
  int32 val = 1 [(buf.validate.field).int32.gte = 8];
}
message Int32GTLT {
  int32 val = 1 [(buf.validate.field).int32 = {
    gt: 0
    lt: 10
  }];
}
message Int32ExLTGT {
  int32 val = 1 [(buf.validate.field).int32 = {
    lt: 0
    gt: 10
  }];
}
message Int32GTELTE {
  int32 val = 1 [(buf.validate.field).int32 = {
    gte: 128
    lte: 256
  }];
}
message Int32ExGTELTE {
  int32 val = 1 [(buf.validate.field).int32 = {
    lte: 128
    gte: 256
  }];
}
message Int32Ignore {
  int32 val = 1 [
    (buf.validate.field).int32 = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message Int32IncorrectType {
  int32 val = 1 [(buf.validate.field).float.gt = 0];
}

message Int32Example {
  int32 val = 1 [(buf.validate.field).int32.example = 10];
}

message Int64None {
  int64 val = 1;
}
message Int64Const {
  int64 val = 1 [(buf.validate.field).int64.const = 1];
}
message Int64In {
  int64 val = 1 [(buf.validate.field).int64 = {
    in: [
      2,
      3
    ]
  }];
}
message Int64NotIn {
  int64 val = 1 [(buf.validate.field).int64 = {
    not_in: [0]
  }];
}
message Int64LT {
  int64 val = 1 [(buf.validate.field).int64.lt = 0];
}
message Int64LTE {
  int64 val = 1 [(buf.validate.field).int64.lte = 64];
}
message Int64GT {
  int64 val = 1 [(buf.validate.field).int64.gt = 16];
}
message Int64GTE {
  int64 val = 1 [(buf.validate.field).int64.gte = 8];
}
message Int64GTLT {
  int64 val = 1 [(buf.validate.field).int64 = {
    gt: 0
    lt: 10
  }];
}
message Int64ExLTGT {
  int64 val = 1 [(buf.validate.field).int64 = {
    lt: 0
    gt: 10
  }];
}
message Int64GTELTE {
  int64 val = 1 [(buf.validate.field).int64 = {
    gte: 128
    lte: 256
  }];
}
message Int64ExGTELTE {
  int64 val = 1 [(buf.validate.field).int64 = {
    lte: 128
    gte: 256
  }];
}
message Int64Ignore {
  int64 val = 1 [
    (buf.validate.field).int64 = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}
message Int64BigConstraints {
  // Intentionally choose limits that are outside the range of both signed and unsigned 32-bit integers.
  int64 lt_pos = 1 [(buf.validate.field).int64.lt = 5444333222];
  int64 lt_neg = 2 [(buf.validate.field).int64.lt = -5444333222];
  int64 gt_pos = 3 [(buf.validate.field).int64.gt = 5444333222];
  int64 gt_neg = 4 [(buf.validate.field).int64.gt = -5444333222];
  int64 lte_pos = 5 [(buf.validate.field).int64.lte = 5444333222];
  int64 lte_neg = 6 [(buf.validate.field).int64.lte = -5444333222];
  int64 gte_pos = 7 [(buf.validate.field).int64.gte = 5444333222];
  int64 gte_neg = 8 [(buf.validate.field).int64.gte = -5444333222];
  int64 constant_pos = 9 [(buf.validate.field).int64.const = 5444333222];
  int64 constant_neg = 10 [(buf.validate.field).int64.const = -5444333222];
  int64 in = 11 [(buf.validate.field).int64 = {
    in: [
      5444333222,
      -5444333222
    ]
  }];
  int64 notin = 12 [(buf.validate.field).int64 = {
    not_in: [
      5444333222,
      -5444333222
    ]
  }];
}

message Int64IncorrectType {
  int64 val = 1 [(buf.validate.field).float.gt = 0];
}

message Int64Example {
  int64 val = 1 [(buf.validate.field).int64.example = 10];
}

message UInt32None {
  uint32 val = 1;
}
message UInt32Const {
  uint32 val = 1 [(buf.validate.field).uint32.const = 1];
}
message UInt32In {
  uint32 val = 1 [(buf.validate.field).uint32 = {
    in: [
      2,
      3
    ]
  }];
}
message UInt32NotIn {
  uint32 val = 1 [(buf.validate.field).uint32 = {
    not_in: [0]
  }];
}
message UInt32LT {
  uint32 val = 1 [(buf.validate.field).uint32.lt = 5];
}
message UInt32LTE {
  uint32 val = 1 [(buf.validate.field).uint32.lte = 64];
}
message UInt32GT {
  uint32 val = 1 [(buf.validate.field).uint32.gt = 16];
}
message UInt32GTE {
  uint32 val = 1 [(buf.validate.field).uint32.gte = 8];
}
message UInt32GTLT {
  uint32 val = 1 [(buf.validate.field).uint32 = {
    gt: 5
    lt: 10
  }];
}
message UInt32ExLTGT {
  uint32 val = 1 [(buf.validate.field).uint32 = {
    lt: 5
    gt: 10
  }];
}
message UInt32GTELTE {
  uint32 val = 1 [(buf.validate.field).uint32 = {
    gte: 128
    lte: 256
  }];
}
message UInt32ExGTELTE {
  uint32 val = 1 [(buf.validate.field).uint32 = {
    lte: 128
    gte: 256
  }];
}
message UInt32Ignore {
  uint32 val = 1 [
    (buf.validate.field).uint32 = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message UInt32IncorrectType {
  uint32 val = 1 [(buf.validate.field).float.gt = 0];
}

message UInt32Example {
  uint32 val = 1 [(buf.validate.field).uint32.example = 0];
}

message UInt64None {
  uint64 val = 1;
}
message UInt64Const {
  uint64 val = 1 [(buf.validate.field).uint64.const = 1];
}
message UInt64In {
  uint64 val = 1 [(buf.validate.field).uint64 = {
    in: [
      2,
      3
    ]
  }];
}
message UInt64NotIn {
  uint64 val = 1 [(buf.validate.field).uint64 = {
    not_in: [0]
  }];
}
message UInt64LT {
  uint64 val = 1 [(buf.validate.field).uint64.lt = 5];
}
message UInt64LTE {
  uint64 val = 1 [(buf.validate.field).uint64.lte = 64];
}
message UInt64GT {
  uint64 val = 1 [(buf.validate.field).uint64.gt = 16];
}
message UInt64GTE {
  uint64 val = 1 [(buf.validate.field).uint64.gte = 8];
}
message UInt64GTLT {
  uint64 val = 1 [(buf.validate.field).uint64 = {
    gt: 5
    lt: 10
  }];
}
message UInt64ExLTGT {
  uint64 val = 1 [(buf.validate.field).uint64 = {
    lt: 5
    gt: 10
  }];
}
message UInt64GTELTE {
  uint64 val = 1 [(buf.validate.field).uint64 = {
    gte: 128
    lte: 256
  }];
}
message UInt64ExGTELTE {
  uint64 val = 1 [(buf.validate.field).uint64 = {
    lte: 128
    gte: 256
  }];
}
message UInt64Ignore {
  uint64 val = 1 [
    (buf.validate.field).uint64 = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message UInt64IncorrectType {
  uint64 val = 1 [(buf.validate.field).float.gt = 0];
}

message UInt64Example {
  uint64 val = 1 [(buf.validate.field).uint64.example = 0];
}

message SInt32None {
  sint32 val = 1;
}
message SInt32Const {
  sint32 val = 1 [(buf.validate.field).sint32.const = 1];
}
message SInt32In {
  sint32 val = 1 [(buf.validate.field).sint32 = {
    in: [
      2,
      3
    ]
  }];
}
message SInt32NotIn {
  sint32 val = 1 [(buf.validate.field).sint32 = {
    not_in: [0]
  }];
}
message SInt32LT {
  sint32 val = 1 [(buf.validate.field).sint32.lt = 0];
}
message SInt32LTE {
  sint32 val = 1 [(buf.validate.field).sint32.lte = 64];
}
message SInt32GT {
  sint32 val = 1 [(buf.validate.field).sint32.gt = 16];
}
message SInt32GTE {
  sint32 val = 1 [(buf.validate.field).sint32.gte = 8];
}
message SInt32GTLT {
  sint32 val = 1 [(buf.validate.field).sint32 = {
    gt: 0
    lt: 10
  }];
}
message SInt32ExLTGT {
  sint32 val = 1 [(buf.validate.field).sint32 = {
    lt: 0
    gt: 10
  }];
}
message SInt32GTELTE {
  sint32 val = 1 [(buf.validate.field).sint32 = {
    gte: 128
    lte: 256
  }];
}
message SInt32ExGTELTE {
  sint32 val = 1 [(buf.validate.field).sint32 = {
    lte: 128
    gte: 256
  }];
}
message SInt32Ignore {
  sint32 val = 1 [
    (buf.validate.field).sint32 = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message SInt32IncorrectType {
  sint32 val = 1 [(buf.validate.field).float.gt = 0];
}

message SInt32Example {
  sint32 val = 1 [(buf.validate.field).sint32.example = 0];
}

message SInt64None {
  sint64 val = 1;
}
message SInt64Const {
  sint64 val = 1 [(buf.validate.field).sint64.const = 1];
}
message SInt64In {
  sint64 val = 1 [(buf.validate.field).sint64 = {
    in: [
      2,
      3
    ]
  }];
}
message SInt64NotIn {
  sint64 val = 1 [(buf.validate.field).sint64 = {
    not_in: [0]
  }];
}
message SInt64LT {
  sint64 val = 1 [(buf.validate.field).sint64.lt = 0];
}
message SInt64LTE {
  sint64 val = 1 [(buf.validate.field).sint64.lte = 64];
}
message SInt64GT {
  sint64 val = 1 [(buf.validate.field).sint64.gt = 16];
}
message SInt64GTE {
  sint64 val = 1 [(buf.validate.field).sint64.gte = 8];
}
message SInt64GTLT {
  sint64 val = 1 [(buf.validate.field).sint64 = {
    gt: 0
    lt: 10
  }];
}
message SInt64ExLTGT {
  sint64 val = 1 [(buf.validate.field).sint64 = {
    lt: 0
    gt: 10
  }];
}
message SInt64GTELTE {
  sint64 val = 1 [(buf.validate.field).sint64 = {
    gte: 128
    lte: 256
  }];
}
message SInt64ExGTELTE {
  sint64 val = 1 [(buf.validate.field).sint64 = {
    lte: 128
    gte: 256
  }];
}
message SInt64Ignore {
  sint64 val = 1 [
    (buf.validate.field).sint64 = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}
message SInt64IncorrectType {
  sint64 val = 1 [(buf.validate.field).float.gt = 0];
}

message SInt64Example {
  sint64 val = 1 [(buf.validate.field).sint64.example = 0];
}

message Fixed32None {
  fixed32 val = 1;
}
message Fixed32Const {
  fixed32 val = 1 [(buf.validate.field).fixed32.const = 1];
}
message Fixed32In {
  fixed32 val = 1 [(buf.validate.field).fixed32 = {
    in: [
      2,
      3
    ]
  }];
}
message Fixed32NotIn {
  fixed32 val = 1 [(buf.validate.field).fixed32 = {
    not_in: [0]
  }];
}
message Fixed32LT {
  fixed32 val = 1 [(buf.validate.field).fixed32.lt = 5];
}
message Fixed32LTE {
  fixed32 val = 1 [(buf.validate.field).fixed32.lte = 64];
}
message Fixed32GT {
  fixed32 val = 1 [(buf.validate.field).fixed32.gt = 16];
}
message Fixed32GTE {
  fixed32 val = 1 [(buf.validate.field).fixed32.gte = 8];
}
message Fixed32GTLT {
  fixed32 val = 1 [(buf.validate.field).fixed32 = {
    gt: 5
    lt: 10
  }];
}
message Fixed32ExLTGT {
  fixed32 val = 1 [(buf.validate.field).fixed32 = {
    lt: 5
    gt: 10
  }];
}
message Fixed32GTELTE {
  fixed32 val = 1 [(buf.validate.field).fixed32 = {
    gte: 128
    lte: 256
  }];
}
message Fixed32ExGTELTE {
  fixed32 val = 1 [(buf.validate.field).fixed32 = {
    lte: 128
    gte: 256
  }];
}
message Fixed32Ignore {
  fixed32 val = 1 [
    (buf.validate.field).fixed32 = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message Fixed32IncorrectType {
  fixed32 val = 1 [(buf.validate.field).float.gt = 0];
}

message Fixed32Example {
  fixed32 val = 1 [(buf.validate.field).fixed32.example = 0];
}

message Fixed64None {
  fixed64 val = 1;
}
message Fixed64Const {
  fixed64 val = 1 [(buf.validate.field).fixed64.const = 1];
}
message Fixed64In {
  fixed64 val = 1 [(buf.validate.field).fixed64 = {
    in: [
      2,
      3
    ]
  }];
}
message Fixed64NotIn {
  fixed64 val = 1 [(buf.validate.field).fixed64 = {
    not_in: [0]
  }];
}
message Fixed64LT {
  fixed64 val = 1 [(buf.validate.field).fixed64.lt = 5];
}
message Fixed64LTE {
  fixed64 val = 1 [(buf.validate.field).fixed64.lte = 64];
}
message Fixed64GT {
  fixed64 val = 1 [(buf.validate.field).fixed64.gt = 16];
}
message Fixed64GTE {
  fixed64 val = 1 [(buf.validate.field).fixed64.gte = 8];
}
message Fixed64GTLT {
  fixed64 val = 1 [(buf.validate.field).fixed64 = {
    gt: 5
    lt: 10
  }];
}
message Fixed64ExLTGT {
  fixed64 val = 1 [(buf.validate.field).fixed64 = {
    lt: 5
    gt: 10
  }];
}
message Fixed64GTELTE {
  fixed64 val = 1 [(buf.validate.field).fixed64 = {
    gte: 128
    lte: 256
  }];
}
message Fixed64ExGTELTE {
  fixed64 val = 1 [(buf.validate.field).fixed64 = {
    lte: 128
    gte: 256
  }];
}
message Fixed64Ignore {
  fixed64 val = 1 [
    (buf.validate.field).fixed64 = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message Fixed64IncorrectType {
  fixed64 val = 1 [(buf.validate.field).float.gt = 0];
}

message Fixed64Example {
  fixed64 val = 1 [(buf.validate.field).fixed64.example = 0];
}

message SFixed32None {
  sfixed32 val = 1;
}
message SFixed32Const {
  sfixed32 val = 1 [(buf.validate.field).sfixed32.const = 1];
}
message SFixed32In {
  sfixed32 val = 1 [(buf.validate.field).sfixed32 = {
    in: [
      2,
      3
    ]
  }];
}
message SFixed32NotIn {
  sfixed32 val = 1 [(buf.validate.field).sfixed32 = {
    not_in: [0]
  }];
}
message SFixed32LT {
  sfixed32 val = 1 [(buf.validate.field).sfixed32.lt = 0];
}
message SFixed32LTE {
  sfixed32 val = 1 [(buf.validate.field).sfixed32.lte = 64];
}
message SFixed32GT {
  sfixed32 val = 1 [(buf.validate.field).sfixed32.gt = 16];
}
message SFixed32GTE {
  sfixed32 val = 1 [(buf.validate.field).sfixed32.gte = 8];
}
message SFixed32GTLT {
  sfixed32 val = 1 [(buf.validate.field).sfixed32 = {
    gt: 0
    lt: 10
  }];
}
message SFixed32ExLTGT {
  sfixed32 val = 1 [(buf.validate.field).sfixed32 = {
    lt: 0
    gt: 10
  }];
}
message SFixed32GTELTE {
  sfixed32 val = 1 [(buf.validate.field).sfixed32 = {
    gte: 128
    lte: 256
  }];
}
message SFixed32ExGTELTE {
  sfixed32 val = 1 [(buf.validate.field).sfixed32 = {
    lte: 128
    gte: 256
  }];
}
message SFixed32Ignore {
  sfixed32 val = 1 [
    (buf.validate.field).sfixed32 = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message SFixed32IncorrectType {
  sfixed32 val = 1 [(buf.validate.field).float.gt = 0];
}

message SFixed32Example {
  sfixed32 val = 1 [(buf.validate.field).sfixed32.example = 0];
}

message SFixed64None {
  sfixed64 val = 1;
}
message SFixed64Const {
  sfixed64 val = 1 [(buf.validate.field).sfixed64.const = 1];
}
message SFixed64In {
  sfixed64 val = 1 [(buf.validate.field).sfixed64 = {
    in: [
      2,
      3
    ]
  }];
}
message SFixed64NotIn {
  sfixed64 val = 1 [(buf.validate.field).sfixed64 = {
    not_in: [0]
  }];
}
message SFixed64LT {
  sfixed64 val = 1 [(buf.validate.field).sfixed64.lt = 0];
}
message SFixed64LTE {
  sfixed64 val = 1 [(buf.validate.field).sfixed64.lte = 64];
}
message SFixed64GT {
  sfixed64 val = 1 [(buf.validate.field).sfixed64.gt = 16];
}
message SFixed64GTE {
  sfixed64 val = 1 [(buf.validate.field).sfixed64.gte = 8];
}
message SFixed64GTLT {
  sfixed64 val = 1 [(buf.validate.field).sfixed64 = {
    gt: 0
    lt: 10
  }];
}
message SFixed64ExLTGT {
  sfixed64 val = 1 [(buf.validate.field).sfixed64 = {
    lt: 0
    gt: 10
  }];
}
message SFixed64GTELTE {
  sfixed64 val = 1 [(buf.validate.field).sfixed64 = {
    gte: 128
    lte: 256
  }];
}
message SFixed64ExGTELTE {
  sfixed64 val = 1 [(buf.validate.field).sfixed64 = {
    lte: 128
    gte: 256
  }];
}
message SFixed64Ignore {
  sfixed64 val = 1 [
    (buf.validate.field).sfixed64 = {
      gte: 128
      lte: 256
    },
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  ];
}

message SFixed64IncorrectType {
  sfixed64 val = 1 [(buf.validate.field).float.gt = 0];
}

message SFixed64Example {
  sfixed64 val = 1 [(buf.validate.field).sfixed64.example = 0];
}

message Int64LTEOptional {
  optional int64 val = 1 [(buf.validate.field).int64.lte = 64];
}
