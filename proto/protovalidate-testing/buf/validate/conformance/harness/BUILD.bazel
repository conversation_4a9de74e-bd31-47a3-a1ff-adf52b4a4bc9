# Copyright 2023 Buf Technologies, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@rules_buf//buf:defs.bzl", "buf_lint_test")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "buf_validate_conformance_harness_proto",
    srcs = [
        "harness.proto",
        "results.proto",
    ],
    strip_import_prefix = "/proto/protovalidate-testing",
    visibility = ["//visibility:public"],
    deps = [
        "//proto/protovalidate/buf/validate:validate_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:descriptor_proto",
    ],
)

buf_lint_test(
    name = "buf_validate_conformance_harness_proto_lint",
    config = "//proto/protovalidate-testing:buf.yaml",
    targets = [":buf_validate_conformance_harness_proto"],
)

cc_proto_library(
    name = "buf_validate_conformance_harness_proto_cc",
    visibility = ["//visibility:public"],
    deps = [":buf_validate_conformance_harness_proto"],
)
