// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package tests.migrate;

import "google/protobuf/wrappers.proto";
import "validate/validate.proto";
import "buf/validate/validate.proto";

message FieldRequired {
  google.protobuf.BoolValue x = 1 [(validate.rules).message.required = true,(buf.validate.field).required = true];
  google.protobuf.Int32Value y = 2 [
    (validate.rules).message = { required: true },
    (buf.validate.field) = { required: true }
  ];
  repeated google.protobuf.StringValue z = 3 [
    (validate.rules).repeated.items = {
      message: { required: true },
      string: { min_len: 3 },
    },
    (buf.validate.field).repeated.items = { required: true,
      string: { min_len: 3 },
    }
  ];
}