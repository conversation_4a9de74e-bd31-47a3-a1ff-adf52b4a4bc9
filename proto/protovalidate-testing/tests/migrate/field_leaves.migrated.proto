// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax="proto3";

package tests.migrate;

import "validate/validate.proto";

import "buf/validate/validate.proto";

message FieldsLeaves {
  int32 x = 1 [(validate.rules).int32.gt = 0,(buf.validate.field).int32.gt = 0];
  repeated string y = 2 [
    (validate.rules).repeated.items.string.min_len = 3,
    (buf.validate.field).repeated.items.string.min_len = 3
  ];
  map<uint32, string> z = 3 [
    deprecated = true,
    (validate.rules).map.keys.uint32.lte = 100,
    (buf.validate.field).map.keys.uint32.lte = 100,
    (validate.rules).map.values.string.in = "foo",
    (buf.validate.field).map.values.string.in = "foo",
    (validate.rules).map.values.string.in = "bar",
    (buf.validate.field).map.values.string.in = "bar"
  ];
}