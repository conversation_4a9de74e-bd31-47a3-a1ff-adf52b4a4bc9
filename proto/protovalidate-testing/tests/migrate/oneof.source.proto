// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package tests.migrate;

import "validate/validate.proto";

message OneofOptionsRequired {
  oneof a {
    option (validate.required) = true;
    int32 x = 1;
  }
  oneof b {
    option (validate.required) = false;
    string y = 2;
  }
  oneof c {
    OneofOptionsRequired z = 3;
  }
}
