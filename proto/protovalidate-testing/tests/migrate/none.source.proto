// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// top-level comment
syntax = "proto3"; // trailing comment

/* some pathological comment
   behavior */ package tests.migrate;

import "google/protobuf/duration.proto";

// floating comment

// Foo is a message
message Foo {
  // Foo is deprecated
  option deprecated = true; // add 1970-01-01

  // a
  //
  int32           alpha = 1; /*
    More weird comment behavior
  */
  Bar             beta  = 2 [deprecated = true];
  repeated Bar    gamma = 3;
  map<int32, Bar> delta = 4;

  oneof union {
    string epsilon = 5;
    Bar    zeta    = 6;
  }

  message Bar { // nested message
    optional string x = 1;
  }

  enum Baz {
    BAZ_UNSPECIFIED = 0;
    BAZ_ONE         = 1;
  }
}

enum Fizz {
  FIZZ_UNSPECIFIED = 0;
  FIZZ_ONE = 1;
}

service Buzz {
  rpc Do(Foo) returns (Foo.Bar);
}
