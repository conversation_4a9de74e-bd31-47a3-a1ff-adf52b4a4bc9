// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package tests.migrate;

import "validate/validate.proto";

import "buf/validate/validate.proto";

message FieldSkipped {
  FieldSkipped x = 1 [(validate.rules).message.skip = true,(buf.validate.field).ignore = IGNORE_ALWAYS];
  FieldSkipped y = 2 [
    (validate.rules).message = { skip: false },
    (buf.validate.field) = { }
  ];
  repeated FieldSkipped z = 3 [
    (validate.rules).repeated.items = {
      message: {
        required: true;
        skip: true,
      }
    },
    (buf.validate.field).repeated.items = {
        required: true;
        ignore: IGNORE_ALWAYS,
    }
  ];
}