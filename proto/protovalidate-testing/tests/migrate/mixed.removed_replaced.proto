// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax="proto3";

package tests.migrate;

import "buf/validate/validate.proto";

message Mixed {
  option (buf.validate.message).disabled = true;

  oneof o {
    option (buf.validate.oneof).required = true;
    string x = 1;
  }

  int32 y = 2 [
    (buf.validate.field).int32.gt = 123
  ];
}