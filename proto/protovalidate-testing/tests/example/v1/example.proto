// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package tests.example.v1;

import "buf/validate/validate.proto";

message Person {
  uint64 id = 1 [(buf.validate.field).uint64.gt = 999];

  string email = 2 [(buf.validate.field).string.email = true];

  string name = 3 [(buf.validate.field).string = {
    pattern: "^[[:alpha:]]+( [[:alpha:]]+)*$",
    max_bytes: 256,
  }];

  Coordinates home = 4;
}

message Coordinates {
  double lat = 1 [(buf.validate.field).double = {
    gte: -90,
    lte: 90
  }];
  double lng = 2 [(buf.validate.field).double = {
    gte: -180,
    lte: 180
  }];
}
