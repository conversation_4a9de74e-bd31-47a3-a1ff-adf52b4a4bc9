version: v1
name: buf.build/bufbuild/protovalidate-testing
deps:
  - buf.build/bufbuild/protovalidate
build:
  excludes:
    - tests
breaking:
  use:
    - FIL<PERSON>
  ignore:
    - buf/validate/conformance
lint:
  use:
    - STANDARD
  except:
    - PROTOVALIDATE
  ignore_only:
    ENUM_NO_ALLOW_ALIAS:
      - buf/validate/conformance/cases/enums.proto
    FILE_LOWER_SNAKE_CASE:
      - buf/validate/conformance/cases/filename-with-dash.proto
    IMPORT_USED:
      - buf/validate/conformance/cases/subdirectory/in_subdirectory.proto
      - buf/validate/conformance/cases/filename-with-dash.proto
    PACKAGE_VERSION_SUFFIX:
      - buf/validate/conformance
