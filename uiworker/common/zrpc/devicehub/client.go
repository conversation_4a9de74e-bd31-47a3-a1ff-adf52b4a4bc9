package devicehub

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/client/deviceservice"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
)

type RPCClient struct {
	conf zrpc.RpcClientConf

	client deviceservice.DeviceService
}

func NewRPCClient(c zrpc.RpcClientConf) *RPCClient {
	return &RPCClient{
		conf: c,

		client: deviceservice.NewDeviceService(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (c *RPCClient) SearchDevices(
	ctx context.Context, req *devicehubpb.SearchDeviceReq, opts ...grpc.CallOption,
) (resp *devicehubpb.SearchDeviceResp, err error) {
	return c.client.SearchDevice(ctx, req, opts...)
}

// Deprecated: use `manager.RPCClient.AcquireProjectDevice` instead.
func (c *RPCClient) SearchAcquireDevice(
	ctx context.Context, req *devicehubpb.SearchAcquireDeviceReq, opts ...grpc.CallOption,
) (resp *devicehubpb.SearchAcquireDeviceResp, err error) {
	return c.client.SearchAcquireDevice(ctx, req, opts...)
}

// Deprecated: use `manager.RPCClient.ReleaseProjectDevice` instead.
func (c *RPCClient) ReleaseDevice(
	ctx context.Context, req *devicehubpb.ReleaseDeviceReq, opts ...grpc.CallOption,
) (resp *devicehubpb.ReleaseDeviceResp, err error) {
	return c.client.ReleaseDevice(ctx, req, opts...)
}
