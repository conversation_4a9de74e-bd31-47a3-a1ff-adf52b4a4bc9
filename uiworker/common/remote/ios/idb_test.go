package ios

import (
	"fmt"
	"strings"
	"testing"

	gid "github.com/electricbubble/gidevice"
	"github.com/electricbubble/gidevice/pkg/ipa"
	"github.com/zeromicro/go-zero/core/jsonx"
)

const (
	host    = "*************"
	port    = 27015
	timeout = 5 // unit second

	udid = "00008101-001D2CCC0ED8001E"

	ipaFile = "/Users/<USER>/Documents/App安装包/zhibo8_6.2.9.ipa"
	// TT: com.yiyou.enterprise.tt
	// zhibo8: com.zhibo8.tenyears
	bundleID = "com.yiyou.tt.autoui.WebDriverAgentRunner1.xctrunner"
)

func TestGIDLocalInstallApp(t *testing.T) {
	usb, err := gid.NewUsbmux()
	if err != nil {
		t.Fatalf("NewUsbmux: %+v", err)
	}

	device, err := usb.FindDeviceByUDID(udid)
	if err != nil {
		t.Fatalf("FindDeviceByUDID: %+v", err)
	}

	needToInstall := true

	info, err := ipa.Info(ipaFile)
	if err != nil {
		t.Fatalf("Info: %+v", err)
	}
	t.Logf("Info: %s", jsonx.MarshalToStringIgnoreError(info))

	apps, err := device.InstallationProxyBrowse(gid.WithBundleIDs(info.CFBundleIdentifier))
	if err != nil {
		t.Fatalf("InstallationProxyBrowse: %+v", err)
	}
	t.Logf("InstallationProxyBrowse: %s", jsonx.MarshalToStringIgnoreError(apps))

	if len(apps) > 0 {
		if apps[0].CFBundleVersion == info.CFBundleVersion {
			needToInstall = false
		} else {
			t.Logf("need to re-install the app: %s (%s => %s)", ipaFile, apps[0].CFBundleVersion, info.CFBundleVersion)
		}
	}

	if needToInstall {
		if err = device.AppInstall(ipaFile); err != nil {
			t.Fatalf("AppInstall: %+v", err)
		}
	} else {
		t.Logf("no need to install the app: %s", ipaFile)
	}
}

func TestGIDRemoteInstallApp(t *testing.T) {
	usb, err := gid.NewUsbmuxWithAddress(fmt.Sprintf("%s:%d", host, port))
	if err != nil {
		t.Fatalf("NewUsbmux: %+v", err)
	}

	device, err := usb.FindDeviceByUDID(udid)
	if err != nil {
		t.Fatalf("FindDeviceByUDID: %+v", err)
	}

	needToInstall := true

	info, err := ipa.Info(ipaFile)
	if err != nil {
		t.Fatalf("Info: %+v", err)
	}
	t.Logf("Info: %s", jsonx.MarshalToStringIgnoreError(info))

	apps, err := device.InstallationProxyBrowse(gid.WithBundleIDs(info.CFBundleIdentifier))
	if err != nil {
		t.Fatalf("InstallationProxyBrowse: %+v", err)
	}
	t.Logf("InstallationProxyBrowse: %s", jsonx.MarshalToStringIgnoreError(apps))

	if len(apps) > 0 {
		if apps[0].CFBundleVersion == info.CFBundleVersion {
			needToInstall = false
		} else {
			t.Logf("need to re-install the app: %s (%s => %s)", ipaFile, apps[0].CFBundleVersion, info.CFBundleVersion)
		}
	}

	if needToInstall {
		if err = device.AppKillByBundleID(info.CFBundleIdentifier); err != nil {
			t.Errorf("AppKillByBundleID: %+v", err)
		}
		if err = device.AppInstall(ipaFile); err != nil {
			t.Fatalf("AppInstall: %+v", err)
		}
	} else {
		t.Logf("no need to install the app: %s", ipaFile)
	}
}

func TestGIDRemoteUninstallApp(t *testing.T) {
	usb, err := gid.NewUsbmuxWithAddress(fmt.Sprintf("%s:%d", host, port))
	if err != nil {
		t.Fatalf("NewUsbmux: %+v", err)
	}

	device, err := usb.FindDeviceByUDID(udid)
	if err != nil {
		t.Fatalf("FindDeviceByUDID: %+v", err)
	}

	info, err := ipa.Info(ipaFile)
	if err != nil {
		t.Fatalf("Info: %+v", err)
	}
	t.Logf("Info: %s", jsonx.MarshalToStringIgnoreError(info))

	var exist bool

	apps, err := device.AppList()
	if err != nil {
		t.Fatalf("AppList: %+v", err)
	}
	for _, app := range apps {
		if strings.EqualFold(info.CFBundleIdentifier, app.CFBundleIdentifier) {
			exist = true
			break
		}
	}

	if exist {
		if err = device.AppUninstall(info.CFBundleIdentifier); err != nil {
			t.Fatalf("AppUninstall: %+v", err)
		}
	} else {
		t.Logf("the app is not installed, so there is no need to uninstall it: %s", ipaFile)
	}
}

func TestGIDRemoteStartApp(t *testing.T) {
	usb, err := gid.NewUsbmuxWithAddress(fmt.Sprintf("%s:%d", host, port))
	if err != nil {
		t.Fatalf("NewUsbmux: %+v", err)
	}

	device, err := usb.FindDeviceByUDID(udid)
	if err != nil {
		t.Fatalf("FindDeviceByUDID: %+v", err)
	}

	info, err := device.GetValue("", "")
	if err != nil {
		t.Logf("DeviceInfo: %+v", err)
	}
	t.Logf("DeviceInfo: %s", jsonx.MarshalIgnoreError(info))

	pid, err := device.AppLaunch(bundleID)
	if err != nil {
		t.Fatalf("AppLaunch: %+v", err)
	}
	t.Logf("AppLaunch: %s, %d", bundleID, pid)
}

func TestGIDRemoteStopApp(t *testing.T) {
	usb, err := gid.NewUsbmuxWithAddress(fmt.Sprintf("%s:%d", host, port))
	if err != nil {
		t.Fatalf("NewUsbmux: %+v", err)
	}

	device, err := usb.FindDeviceByUDID(udid)
	if err != nil {
		t.Fatalf("FindDeviceByUDID: %+v", err)
	}

	info, err := device.GetValue("", "")
	if err != nil {
		t.Logf("DeviceInfo: %+v", err)
	}
	t.Logf("DeviceInfo: %s", jsonx.MarshalIgnoreError(info))

	processes, err := device.AppRunningProcesses()
	if err != nil {
		t.Fatalf("AppRunningProcesses: %+v", err)
	}

	var p gid.Process
	for _, process := range processes {
		if process.BundleID == bundleID {
			p = process
		}

		if process.IsApplication {
			t.Logf(
				"name: %s, pid: %d, realAppName: %s, startDate: %s, bundleID: %s, displayName: %s",
				process.Name, process.Pid, process.RealAppName, process.StartDate, process.BundleID,
				process.DisplayName,
			)
		}
	}

	if err = device.AppKillByBundleID(bundleID); err != nil {
		t.Fatalf("AppKillByBundleID: %+v", err)
	}
	t.Logf("AppKillByBundleID: %s, %d", p.Name, p.Pid)
}

//func TestSIDInstallApp(t *testing.T) {
//	usb, err := sid.NewUsbmux()
//	if err != nil {
//		t.Fatalf("NewUsbmux: %+v", err)
//	}
//
//	devices, err := usb.Devices()
//	if err != nil {
//		t.Fatalf("Devices: %+v", err)
//	}
//
//	for _, device := range devices {
//		properties := device.Properties()
//		t.Logf("Properties: %s", jsonx.MarshalToStringIgnoreError(properties))
//	}
//}
//
//func TestSIDRemoteInstallApp(t *testing.T) {
//	device, err := sid.NewRemoteConnect(host, port, timeout)
//	if err != nil {
//		t.Fatalf("NewRemoteConnect: %+v", err)
//	}
//
//	properties := device.Properties()
//	t.Logf("Properties: %s", jsonx.MarshalToStringIgnoreError(properties))
//}
