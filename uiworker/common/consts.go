package common

import "time"

const (
	ConstLockClearWaitingTasks                 = "lock:waiting_tasks:delete"
	ConstChannelNameOfIdleDevicesNotifyByToken = "chan:uiworker:idleDevicesNotify"

	ConstExpireOfClearWaitingTasksTask = 5 * time.Minute

	ConstAppDownloadFolderName      = "app_downloads"
	ConstUITestFrameworkFolderName  = "uitest_framework"
	ConstUITestReportPathFolderName = "uitest_report"
	ConstUITestDataPathFolderName   = "uitest_data"

	ConstSlash       = "/"
	ConstSuffixOfGit = ".git"
	ConstSuffixOfApk = ".apk"
	ConstSuffixOfIpa = ".ipa"
)

const (
	DefaultPeriodOfWatchStopSignal = 5 * time.Second
	DefaultTimeoutOfSendTask       = 2 * time.Second
	DefaultTimeoutOfInvokeRPC      = 5 * time.Second
)

type SubTaskStatus string

const (
	ConstSubTaskStatusOfSuccess SubTaskStatus = "Success"
	ConstSubTaskStatusOfFailed  SubTaskStatus = "Failed"
)
