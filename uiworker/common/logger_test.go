package common

import (
	"context"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"go.uber.org/zap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
)

func TestStepLogger(t *testing.T) {
	var c service.ServiceConf
	conf.MustLoad("./mqc/etc/uiworker.yaml", &c)
	if err := c.SetUp(); err != nil {
		t.Fatal(err)
	}
	w := log.NewZapWriter(c.Log, zap.AddCaller(), zap.AddCallerSkip(2), zap.Development())
	log.SetWriter(w)

	ctx := context.Background()
	logger := NewStepLogger(ctx, c.Log)
	defer logger.Free()

	logger.Info("step logger")
	t.Logf("content1: %s", logger.Sync())
	t.Logf("content2: %s", logger.String())
}
