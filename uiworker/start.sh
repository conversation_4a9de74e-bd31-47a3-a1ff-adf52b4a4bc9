#!/bin/sh

base_path="/home/<USER>/uiworker"
project="uiworker"


#curl -X GET -s -H "Content-Type:application/json;charset=UTF-8" \
# -H "Authorization:${APOLLO_AUTH_TOKEN}" \
#  http://${APOLLO_ADDR}/openapi/v1/envs/dev/apps/probe-backend/clusters/${project}/namespaces/mqc.yaml | jq .items[0].value | sed 's/^"\|"$//g' | sed ':a;:N;$!ba;s/\\n/\n/g' \
#   > ${base_path}/mqc/etc/${project}.yaml

nohup ${base_path}/${project}.linux -f ${base_path}/${project}.yaml > ${base_path}/${project}.out 2>&1 &
