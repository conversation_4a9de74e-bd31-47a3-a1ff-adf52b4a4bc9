Name: uiworker

Log:
  ServiceName: mqc.uiworker
  Encoding: plain
  Level: info
  Path: /app/logs/uiworker

#Prometheus:
#  Host: 0.0.0.0
#  Port: 20921
#  Path: /metrics
#
#Telemetry:
#  Name: mqc.uiworker
#  Endpoint: http://127.0.0.1:14268/api/traces
#  Sampler: 1.0
#  Batcher: jaeger
#
#DevServer:
#  Enabled: true
#  Port: 20831

DispatcherRedis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 3

Redis:
  Key: mqc.uiworker
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 18

Cache:
  - Host: 127.0.0.1:6379
    Pass:
    DB: 18

DB:
  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/uiworker?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

DeviceHub:
  EndPoints:
    - probe-test.ttyuyin.com:8000
  NonBlock: true
  Timeout: 0

Manager:
  EndPoints:
    - probe-test.ttyuyin.com:8000
  NonBlock: true
  Timeout: 0

Reporter:
  Endpoints:
    - probe-test.ttyuyin.com:8000
  NonBlock: true
  Timeout: 0

UIWorkerConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:uiworker
  ConsumerTag: mqc:uiworker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 18
  MaxWorker: 0

DispatcherProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:dispatcher
  Db: 5

UIAndroidRealPhoneWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:ui_android_real_phone_worker
  Db: 18

UIAndroidCloudPhoneWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:ui_android_cloud_phone_worker
  Db: 18

UIIOSRealPhoneWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:ui_ios_real_phone_worker
  Db: 18

UIIOSCloudPhoneWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:ui_ios_cloud_phone_worker
  Db: 18

UIAndroidRealPhoneWorkerConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:ui_android_real_phone_worker
  ConsumerTag: mqc:ui_android_real_phone_worker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 18
  MaxWorker: 0

UIAndroidCloudPhoneWorkerConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:ui_android_cloud_phone_worker
  ConsumerTag: mqc:ui_android_cloud_phone_worker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 18
  MaxWorker: 0

UIIOSRealPhoneWorkerConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:ui_ios_real_phone_worker
  ConsumerTag: mqc:ui_ios_real_phone_worker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 18
  MaxWorker: 0

UIIOSCloudPhoneWorkerConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:ui_ios_cloud_phone_worker
  ConsumerTag: mqc:ui_ios_cloud_phone_worker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 18
  MaxWorker: 0

DeviceHubProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:devicehub
  Db: 17

ReporterProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:reporter
  Db: 5

PythonEnvs:
  - Version: "3.7.16"
    Path: "$HOME/.pyenv/versions/3.7.16/bin/python"
  - Version: "3.9.16"
    Path: "$HOME/.pyenv/versions/3.9.16/bin/python"

ProjectVenvs:
  - Name: "atx_test_project"
    Path: "./atx_test_project/venv"
  - Name: "ios_autotest"
    Path: "./ios_autotest/venv"

ManualConfirmationTexts:
  - 继续安装
  - 确定
  - 完成

WaitingTaskCleaner:
  CronExpression: "30 23 * * ?" # every day 23:30
  KeepDays: 7

PVCPath: ./pvc

ReportRemoteBaseURL: "127.0.0.1:5555"
