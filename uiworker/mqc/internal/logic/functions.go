package logic

import (
	"net/url"
	"strings"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common"
)

func GetNameFromGitURL(gitURL string) (string, error) {
	u, err := url.Parse(gitURL)
	if err != nil {
		return "", err
	}

	path := u.Path

	// 移除开头的斜杠
	path = strings.TrimLeft(path, common.ConstSlash)

	// 移除`.git`扩展名
	path = strings.TrimSuffix(path, common.ConstSuffixOfGit)

	// 分割路径，获取最后一个部分，即目录名称
	parts := strings.Split(path, common.ConstSlash)
	name := parts[len(parts)-1]

	return name, nil
}
