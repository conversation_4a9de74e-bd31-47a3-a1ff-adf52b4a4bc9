package uitest

import (
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

type Executor interface {
	SetState(State dispatcherpb.ComponentState)

	SetupSteps() []*Step
	TestSteps() []*Step
	TeardownSteps() []*Step

	GetDevices() []*devicehubpb.Device
	// Setup() (err error)
	// Run() (State dispatcherpb.ComponentState, err error)
	// Teardown()
}

type Step struct {
	Desc StepDesc
	Func func() error
}

type StepDesc struct {
	EN string
	ZH string
}
