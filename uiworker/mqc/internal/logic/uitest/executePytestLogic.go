package uitest

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/svc"
)

const (
	binDir    = "bin"
	pythonBin = "python"
	pipBin    = "pip"
	pytestBin = "pytest"

	taskNameOfCloneExecuteProject = "clone_execute_project"
	taskNameOfInitPythonEnv       = "init_python_env"
	taskNameOfDownloadAppPackage  = "download_app_package"

	timeoutOfCloneExecuteProject  = 5 * time.Minute
	timeoutOfInitPythonEnv        = 10 * time.Minute
	timeoutOfDownloadAppPackage   = 8 * time.Minute
	timeoutOfExecutePytestCommand = 20 * time.Minute
)

type pytestType = string

const (
	pytestTypeOfFailed     pytestType = "failed"
	pytestTypeOfPassed     pytestType = "passed"
	pytestTypeOfSkipped    pytestType = "skipped"
	pytestTypeOfDeselected pytestType = "deselected"
	pytestTypeOfXfailed    pytestType = "xfailed"
	pytestTypeOfXpassed    pytestType = "xpassed"
	pytestTypeOfWarning    pytestType = "warning"
	pytestTypeOfError      pytestType = "error"
	pytestTypeOfRerun      pytestType = "rerun"
)

type pytestExitCode = int

const (
	pytestExitCodeOfOK               pytestExitCode = iota // tests passed
	pytestExitCodeOfTestsFailed                            // tests failed
	pytestExitCodeOfInterrupted                            // pytest was interrupted
	pytestExitCodeOfInternalError                          // an internal error got in the way
	pytestExitCodeOfUsageError                             // pytest was misused
	pytestExitCodeOfNoTestsCollected                       // pytest couldn't find tests
)

type stepDesc = string

const (
	stepDescOfInitEnvEN stepDesc = "initialize the environment"
	stepDescOfInitEnvZH stepDesc = "初始化环境"

	stepDescOfCloneProjectEN stepDesc = "clone the test project"
	stepDescOfCloneProjectZH stepDesc = "克隆测试项目"

	stepDescOfInitPythonEnvEN stepDesc = "initialize the python virtual environment"
	stepDescOfInitPythonEnvZH stepDesc = "初始化Python虚拟环境"

	stepDescOfDownloadPackageEN stepDesc = "download the test package"
	stepDescOfDownloadPackageZH stepDesc = "下载测试包"

	stepDescOfAcquireDevicesEN stepDesc = "acquire devices"
	stepDescOfAcquireDevicesZH stepDesc = "获取设备"

	stepDescOfInstallAppEN stepDesc = "install the test package"
	stepDescOfInstallAppZH stepDesc = "安装测试包"

	stepDescOfExecutePytestEN stepDesc = "execute pytest command"
	stepDescOfExecutePytestZH stepDesc = "执行pytest命令"

	stepDescOfReleaseDevicesEN stepDesc = "release devices"
	stepDescOfReleaseDevicesZH stepDesc = "释放设备"
)

var (
	_ Executor = (*ExecutePytestLogic)(nil)

	pytestKnownTypes = []pytestType{
		pytestTypeOfFailed,
		pytestTypeOfPassed,
		pytestTypeOfSkipped,
		pytestTypeOfDeselected,
		pytestTypeOfXfailed,
		pytestTypeOfXpassed,
		pytestTypeOfWarning,
		pytestTypeOfError,
		pytestTypeOfRerun,
	}
	_pytestKnownTypesJoinStr = strings.Join(pytestKnownTypes, "|")
	/*
		Examples:
		============================ no tests ran in 0.02s =============================
		======================== 1 failed in 363.07s (0:06:03) =========================
		======================== 1 passed in 286.70s (0:04:46) =========================
		========================= 2 errors in 65.15s (0:01:05) =========================
		=================== 1 skipped, 1 rerun in 422.81s (0:07:02) ====================
		==================== 1 failed, 1 error in 263.06s (0:04:23) ====================
		=============== 1 passed, 1 error, 1 rerun in 525.97s (0:08:45) ================
		=============== 2 passed, 1 error, 1 rerun in 592.51s (0:09:52) ================
		================== 1 failed, 4 warnings in 107.02s (0:01:47) ===================
	*/
	pytestStatsLineRegexp = regexp.MustCompile(
		fmt.Sprintf(
			`^=+.*?(no tests ran|\d+ (%s)s*(, \d+ (%s)s*)*) in \d+\.\d+s.*(\(\d+:\d+:\d+\))?.*?=+$`,
			_pytestKnownTypesJoinStr, _pytestKnownTypesJoinStr,
		),
	)
)

type ExecutePytestLogic struct {
	*ExecuteBaseLogic

	initPythonEnvFlag    bool
	virtualEnvName       string
	virtualEnvPath       string
	virtualEnvPythonPath string
	virtualEnvPipPath    string
}

func NewExecutePytestLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
	stepLogger *common.StepLogger, taskInfo *dispatcherpb.UITestTaskInfo, stopCh <-chan lang.PlaceholderType,
) *ExecutePytestLogic {
	return &ExecutePytestLogic{
		ExecuteBaseLogic: NewExecuteBaseLogic(ctx, svcCtx, stepLogger, taskInfo, stopCh),
	}
}

func (l *ExecutePytestLogic) setVirtualEnvironment() {
	var (
		url     = l.taskInfo.GetTestFrameworkUrl()
		version = l.taskInfo.GetTestLanguageVersion()
	)

	l.virtualEnvName = fmt.Sprintf("%s_%s", pythonEnvPrefix, version)
	l.virtualEnvPath = filepath.Join(l.frameworkPath, l.virtualEnvName)
	l.virtualEnvPythonPath = filepath.Join(l.virtualEnvPath, binDir, pythonBin)
	l.virtualEnvPipPath = filepath.Join(l.virtualEnvPath, binDir, pipBin)

	name, err := logic.GetNameFromGitURL(url)
	if err != nil {
		l.logger.Warnf("failed to get project name from the framework url, url: %s, error: %+v", url, err)

		return
	}

	for _, venv := range l.svcCtx.Config.ProjectVenvs {
		if strings.EqualFold(venv.Name, name) {
			l.initPythonEnvFlag = true
			l.virtualEnvPath = venv.Path
			l.virtualEnvPythonPath = filepath.Join(venv.Path, binDir, pythonBin)
			l.virtualEnvPipPath = filepath.Join(venv.Path, binDir, pipBin)

			break
		}
	}
}

func (l *ExecutePytestLogic) setScriptPath() error {
	for _, env := range l.svcCtx.Config.PythonEnvs {
		if env.Version == l.taskInfo.GetTestLanguageVersion() {
			l.scriptPath = env.Path
			return nil
		}
	}

	return errors.Errorf("unsupported test language version: %s", l.taskInfo.GetTestLanguageVersion())
}

func (l *ExecutePytestLogic) initPythonEnv(ctx context.Context) (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()

		basePythonPath = l.scriptPath
		basePath       = l.frameworkPath
		envName        = l.virtualEnvName
		pipPath        = l.virtualEnvPipPath

		errCh = make(chan error, 1)

		commands      []string
		commandString string
	)

	envPath := filepath.Join(basePath, envName)
	if _, err = os.Stat(envPath); err != nil {
		if os.IsNotExist(err) {
			commands = []string{
				fmt.Sprintf("cd %s", basePath),
				fmt.Sprintf("%s -m venv %s", basePythonPath, envName),
				fmt.Sprintf("%s install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/", pipPath),
			}
		} else {
			return err
		}
	} else {
		commands = []string{
			fmt.Sprintf("%s install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/", pipPath),
		}
	}
	commandString = strings.Join(commands, " && ")

	fn := func() error {
		l.logger.Infof("begin to init python env, task_id: %s, target: %s, command: %q", taskID, target, commandString)

		cmd := utils.CommandContext(ctx, "sh", "-c", commandString)
		cmd.WaitDelay = time.Second
		_output, _err := cmd.CombinedOutput()
		if _err != nil {
			return errors.Errorf(
				"failed to init python env, task_id: %s, target: %s, command: %q, output: %s, error: %+v",
				taskID, target, commandString, _output, _err,
			)
		}

		l.logger.Infof(
			"finish to init python env, task_id: %s, target: %s, command: %q, output: %s",
			taskID, target, commandString, _output,
		)

		return nil
	}
	threading.GoSafe(
		func() {
			errCh <- fn()
		},
	)

	select {
	case <-l.stopCh:
		return errors.Errorf(
			"got a stop signal while initializing the python env, task_id: %s, target: %s, command: %q",
			taskID, target, commandString,
		)
	case err = <-errCh:
		return err
	}
}

func (l *ExecutePytestLogic) executePytest() (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		target = l.taskInfo.GetTestTarget()

		errCh = make(chan error, 1)

		commands      []string
		commandString string
		pytestCommand string
	)

	if len(l.devices) == 0 {
		return errors.Errorf("no devices to execute pytest, task_id: %s, target: %s", taskID, target)
	}

	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, timeoutOfExecutePytestCommand)
	defer cancel()

	appDownloadLink := l.taskInfo.GetAppDownloadLink()
	if len(appDownloadLink) == 0 {
		lockKey := fmt.Sprintf("lock::app_download_link:%s::%s", l.shortTaskID, taskID)
		appDownloadLink, err = l.svcCtx.Redis.GetCtx(l.ctx, lockKey)
		if err != nil {
			l.logger.Errorf(
				"failed to fetch app_download_link from redis cache, task_id: %s, lock_key: %s, error: %+v",
				taskID, lockKey, err,
			)
		}
	}

	testDataPath := filepath.Join(l.basePath, common.ConstUITestDataPathFolderName, l.shortTaskID)
	argsMap := map[string]string{
		constants.TestArgTaskId:              l.taskInfo.GetTaskId(),
		constants.TestArgTestDataSavePath:    testDataPath,
		constants.TestArgDeviceRemoteAddress: l.devices[0].GetRemoteAddress(),
		constants.TestArgUdid:                l.devices[0].GetUdid(),
		constants.TestArgAppDownloadLink:     appDownloadLink,
		constants.TestArgAppVersion:          l.taskInfo.GetAppVersion(),
	}

	pytestCommand = fmt.Sprintf("%s %s", pytestBin, l.taskInfo.GetTestTarget())
	for _, ta := range l.taskInfo.GetTestArgs() {
		for an, av := range argsMap {
			ta = strings.Replace(ta, an, av, -1)
		}

		pytestCommand = pytestCommand + " " + ta
	}

	commands = []string{
		"cd " + l.frameworkPath,
		l.virtualEnvPythonPath + " -m " + pytestCommand,
	}
	commandString = strings.Join(commands, " && ")

	// start to collect perf data
	l.startToCollectPerfData(l.devices[0])
	defer l.stopToCollectPerfData()

	fn := func() error {
		l.logger.Infof(
			"begin to execute the pytest command, task_id: %s, target: %s, command: %q", taskID, target, commandString,
		)
		cmd := utils.CommandContext(ctx, "sh", "-c", commandString)
		cmd.WaitDelay = time.Second
		_output, _err := cmd.CombinedOutput()

		// first check the output content, then check the result code
		statsLine := ""
		lines := strings.Split(string(_output), "\n")
		for _, line := range lines {
			if !pytestStatsLineRegexp.MatchString(line) {
				continue
			}

			statsLine = line
			break
		}

		if statsLine == "" {
			l.Warnf(
				"not found the pytest stats line, task_id: %s, target: %s, command: %q", taskID, target, commandString,
			)

			if _err != nil {
				exit := true

				var e *exec.ExitError
				if errors.As(_err, &e) {
					exitCode := e.ExitCode()
					if exitCode == pytestExitCodeOfOK || exitCode == pytestExitCodeOfNoTestsCollected {
						exit = false
					}
				}

				if exit {
					return errors.Errorf(
						"failed to execute the pytest command, task_id: %s, target: %s, command: %q, output: %s, error: %+v",
						taskID, target, commandString, _output, err,
					)
				}
			}

			return errors.Errorf(
				"since the pytest stats line was not found, the result of the pytest command cannot be determined, task_id: %s, target: %s, command: %q",
				taskID, target, commandString,
			)
		}

		l.logger.Infof(
			"finish to execute the pytest command, task_id: %s, target: %s, command: %q, stats: %s, output: \n%s",
			taskID, target, commandString, statsLine, _output,
		)

		if strings.Contains(statsLine, pytestTypeOfFailed) || strings.Contains(statsLine, pytestTypeOfError) {
			return errors.Errorf(
				"failed to execute the pytest command, task_id: %s, target: %s, command: %q, stats: %s",
				taskID, target, commandString, statsLine,
			)
		}

		return nil
	}
	threading.GoSafe(
		func() {
			errCh <- fn()
		},
	)

	select {
	case <-l.stopCh:
		return errors.Errorf(
			"got a stop signal while executing the pytest command, task_id: %s, target: %s, command: %q",
			taskID, target, commandString,
		)
	case err = <-errCh:
		return err
	}
}

func (l *ExecutePytestLogic) SetupSteps() []*Step {
	return []*Step{
		{
			Desc: StepDesc{
				EN: stepDescOfInitEnvEN,
				ZH: stepDescOfInitEnvZH,
			},
			Func: func() error {
				l.setVirtualEnvironment()
				return l.setScriptPath()
			},
		},
		{
			Desc: StepDesc{
				EN: stepDescOfCloneProjectEN,
				ZH: stepDescOfCloneProjectZH,
			},
			Func: func() error {
				// clone test project
				return l.ExecuteSubTaskOnce(
					fmt.Sprintf("%s:%s", taskNameOfCloneExecuteProject, l.shortTaskID),
					timeoutOfCloneExecuteProject,
					l.cloneExecuteProject,
				)
			},
		},
		{
			Desc: StepDesc{
				EN: stepDescOfInitPythonEnvEN,
				ZH: stepDescOfInitPythonEnvZH,
			},
			Func: func() error {
				if l.initPythonEnvFlag {
					l.logger.Infof(
						"no need to init python env cause by using specified venv, task_id: %s, target: %s, path: %s",
						l.taskInfo.GetTaskId(), l.taskInfo.GetTestTarget(), l.virtualEnvPath,
					)

					return nil
				}

				// init python env
				return l.ExecuteSubTaskOnce(
					fmt.Sprintf("%s:%s", taskNameOfInitPythonEnv, l.shortTaskID),
					timeoutOfInitPythonEnv,
					l.initPythonEnv,
				)
			},
		},
		{
			Desc: StepDesc{
				EN: stepDescOfDownloadPackageEN,
				ZH: stepDescOfDownloadPackageZH,
			},
			Func: func() error {
				// download test package
				return l.ExecuteSubTaskOnce(
					fmt.Sprintf("%s:%s", taskNameOfDownloadAppPackage, l.shortTaskID),
					timeoutOfDownloadAppPackage,
					l.downloadTestPackage,
				)
			},
		},
		{
			Desc: StepDesc{
				EN: stepDescOfAcquireDevicesEN,
				ZH: stepDescOfAcquireDevicesZH,
			},
			Func: l.acquireDevices, // 占用设备
		},
		{
			Desc: StepDesc{
				EN: stepDescOfInstallAppEN,
				ZH: stepDescOfInstallAppZH,
			},
			Func: l.installAppToDevices, // 安装测试包
		},
	}
}

func (l *ExecutePytestLogic) TestSteps() []*Step {
	return []*Step{
		{
			Desc: StepDesc{
				EN: stepDescOfExecutePytestEN,
				ZH: stepDescOfExecutePytestZH,
			},
			Func: l.executePytest,
		},
	}
}

func (l *ExecutePytestLogic) TeardownSteps() []*Step {
	return []*Step{
		{
			Desc: StepDesc{
				EN: stepDescOfReleaseDevicesEN,
				ZH: stepDescOfReleaseDevicesZH,
			},
			Func: func() error {
				return l.releaseDevices()
			},
		},
	}
}

func (l *ExecutePytestLogic) GetDevices() []*devicehubpb.Device {
	if l.ExecuteBaseLogic != nil {
		return l.ExecuteBaseLogic.devices
	}
	return nil
}
