package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	producerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	qettypes "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/types"
)

type Config struct {
	service.ServiceConf

	Redis           redis.RedisKeyConf
	DispatcherRedis redis.RedisConf
	Cache           cache.CacheConf
	DB              qettypes.DBConfig

	DeviceHub zrpc.RpcClientConf
	Manager   zrpc.RpcClientConf
	Reporter  zrpc.RpcClientConf

	UIWorkerConsumer                  consumerv2.Config
	DispatcherProducer                producerv2.Config
	UIAndroidRealPhoneWorkerProducer  producerv2.Config
	UIAndroidCloudPhoneWorkerProducer producerv2.Config
	UIIOSRealPhoneWorkerProducer      producerv2.Config
	UIIOSCloudPhoneWorkerProducer     producerv2.Config
	UIAndroidRealPhoneWorkerConsumer  consumerv2.Config
	UIAndroidCloudPhoneWorkerConsumer consumerv2.Config
	UIIOSRealPhoneWorkerConsumer      consumerv2.Config
	UIIOSCloudPhoneWorkerConsumer     consumerv2.Config
	DeviceHubProducer                 producerv2.Config
	ReporterProducer                  producerv2.Config

	PythonEnvs              []types.PythonEnv
	ProjectVenvs            []types.ProjectVenv
	ManualConfirmationTexts []string `json:",optional"` // Deprecated
	WaitingTaskCleaner      commontypes.ClearStrategy
	PVCPath                 string
	ReportRemoteBaseURL     string
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return constants.NoNeedToListen
}
