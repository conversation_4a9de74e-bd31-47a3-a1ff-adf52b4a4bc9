package internal

import (
	"database/sql"
	"testing"

	"github.com/zeromicro/go-zero/core/lang"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/model"
)

func TestAllocateDevices(t *testing.T) {
	tasks1 := []*model.WaitingTasks{
		{
			Token:   "token1",
			Devices: sql.NullString{String: `["device1"]`, Valid: true},
		},
		{
			Token:   "token2",
			Devices: sql.NullString{String: `["device1", "device2", "device3"]`, Valid: true},
		},
		{
			Token:   "token3",
			Devices: sql.NullString{String: `["device1"]`, Valid: false},
		},
		{
			Token:   "token4",
			Devices: sql.NullString{String: `["device2"]`, Valid: true},
		},
		{
			Token:   "token5",
			Devices: sql.NullString{String: `["device3"]`, Valid: true},
		},
	}
	tasks2 := []*model.WaitingTasks{
		{
			Token:   "token1",
			Devices: sql.NullString{String: `["device1", "device2"]`, Valid: true},
		},
		{
			Token:   "token2",
			Devices: sql.NullString{String: `["device2", "device3"]`, Valid: true},
		},
		{
			Token:   "token3",
			Devices: sql.NullString{String: `["device2", "device4"]`, Valid: true},
		},
		{
			Token:   "token4",
			Devices: sql.NullString{String: `["device3", "device4"]`, Valid: true},
		},
	}
	tasks3 := []*model.WaitingTasks{
		{
			Token:   "token1",
			Devices: sql.NullString{String: `["device1", "device2"]`, Valid: true},
		},
		{
			Token:   "token2",
			Devices: sql.NullString{String: `["device2", "device3"]`, Valid: true},
		},
		{
			Token:   "token3",
			Devices: sql.NullString{String: `["device1", "device2"]`, Valid: true},
		},
		{
			Token:   "token4",
			Devices: sql.NullString{String: `["device1"]`, Valid: false},
		},
	}
	tasks4 := []*model.WaitingTasks{
		{
			Token:   "token1",
			Devices: sql.NullString{String: `["device1", "device2", "device3"]`, Valid: true},
		},
		{
			Token:   "token2",
			Devices: sql.NullString{String: `["device1", "device2", "device3"]`, Valid: true},
		},
		{
			Token:   "token3",
			Devices: sql.NullString{String: `["device1", "device2", "device3"]`, Valid: true},
		},
	}
	cases := []struct {
		name    string
		tasks   []*model.WaitingTasks
		devices []string
		wants   map[string]lang.PlaceholderType
	}{
		{
			name:  "指定设备场景1",
			tasks: tasks1,
			devices: []string{
				"device1",
			},
			wants: map[string]lang.PlaceholderType{
				"token1_device1": lang.Placeholder,
			},
		},
		{
			name:  "派生设备场景1",
			tasks: tasks1,
			devices: []string{
				"device1",
				"device2",
			},
			wants: map[string]lang.PlaceholderType{
				"token1_device1": lang.Placeholder,
				"token2_device2": lang.Placeholder,
			},
		},
		{
			name:  "随机设备场景1",
			tasks: tasks1,
			devices: []string{
				"device1",
				"device4",
			},
			wants: map[string]lang.PlaceholderType{
				"token1_device1": lang.Placeholder,
				"token3_device4": lang.Placeholder,
			},
		},
		{
			name:  "指定设备场景2",
			tasks: tasks1,
			devices: []string{
				"device1",
				"device2",
				"device3",
				"device4",
			},
			wants: map[string]lang.PlaceholderType{
				"token1_device1": lang.Placeholder,
				"token2_device3": lang.Placeholder,
				"token3_device4": lang.Placeholder,
				"token4_device2": lang.Placeholder,
			},
		},
		{
			name:  "随机设备场景2",
			tasks: tasks1,
			devices: []string{
				"device2",
				"device3",
			},
			wants: map[string]lang.PlaceholderType{
				"token2_device3": lang.Placeholder,
				"token3_device2": lang.Placeholder,
			},
		},
		{
			name:  "派生设备场景2",
			tasks: tasks2,
			devices: []string{
				"device1",
				"device2",
				"device3",
				"device4",
			},
			wants: map[string]lang.PlaceholderType{
				"token1_device1": lang.Placeholder,
				"token2_device3": lang.Placeholder,
				"token3_device2": lang.Placeholder,
				"token4_device4": lang.Placeholder,
			},
		},
		{
			name:  "派生设备场景3",
			tasks: tasks3,
			devices: []string{
				"device1",
				"device2",
				"device3",
				"device4",
			},
			wants: map[string]lang.PlaceholderType{
				"token1_device1": lang.Placeholder,
				"token2_device3": lang.Placeholder,
				"token3_device2": lang.Placeholder,
				"token4_device4": lang.Placeholder,
			},
		},
		{
			name:  "派生设备场景4",
			tasks: tasks4,
			devices: []string{
				"device1",
				"device2",
				"device3",
			},
			wants: map[string]lang.PlaceholderType{
				"token1_device1": lang.Placeholder,
				"token2_device2": lang.Placeholder,
				"token3_device3": lang.Placeholder,
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			tokens := allocateDevices(c.tasks, c.devices)
			if len(tokens) != len(c.wants) {
				t.Errorf("want %d tokens, got %d tokens", len(c.wants), len(tokens))
			}
			for _, token := range tokens {
				if _, ok := c.wants[token]; !ok {
					t.Errorf("got token: %s, not in wants", token)
				}
			}
		})
	}
}
