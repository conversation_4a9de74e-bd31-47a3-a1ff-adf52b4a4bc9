package internal

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/svc"
)

var errorOfStopSignal = errors.New("got a signal to stop the listener")

type Listener struct {
	logx.Logger
	ctx    context.Context
	cancel context.CancelCauseFunc
	svcCtx *svc.ServiceContext
}

func newListener(svcCtx *svc.ServiceContext) *Listener {
	ctx, cancel := context.WithCancelCause(context.Background())
	l := &Listener{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		cancel: cancel,
		svcCtx: svcCtx,
	}

	proc.AddShutdownListener(l.stop)
	return l
}

func (l *Listener) stop() {
	l.Infof("stopping the listener at %s", time.Now().Format("2006-01-02 15:04:05"))
	l.cancel(errorOfStopSignal)
}

func (l *Listener) searchDevices() ([]condition, error) {
	var conditions []condition
	resp, err := l.svcCtx.DeviceHubRPC.SearchDevices(
		l.ctx, &pb.SearchDeviceReq{
			Condition: &rpc.Condition{
				Single: &rpc.SingleCondition{
					Field:   string(devicehubcommon.DeviceFieldOfState),
					Compare: constants.EQ,
					Other: &rpc.Other{
						Value: protobuf.GetEnumStringOf(pb.DeviceState_IDLE),
					},
				},
			},
		},
	)
	if err != nil {
		return conditions, err
	}
	l.Debugf("search idle devices successfully, total: %d", resp.GetTotalCount())

	var condition11, condition12, condition21, condition22 condition = condition{
		deviceType:   commonpb.DeviceType_REAL_PHONE,
		platformType: commonpb.PlatformType_ANDROID,
		count:        uint32(l.svcCtx.Config.UIAndroidRealPhoneWorkerConsumer.MaxWorker),
	}, condition{
		deviceType:   commonpb.DeviceType_REAL_PHONE,
		platformType: commonpb.PlatformType_IOS,
		count:        uint32(l.svcCtx.Config.UIIOSRealPhoneWorkerConsumer.MaxWorker),
	}, condition{
		deviceType:   commonpb.DeviceType_CLOUD_PHONE,
		platformType: commonpb.PlatformType_ANDROID,
		count:        uint32(l.svcCtx.Config.UIAndroidCloudPhoneWorkerConsumer.MaxWorker),
	}, condition{
		deviceType:   commonpb.DeviceType_CLOUD_PHONE,
		platformType: commonpb.PlatformType_IOS,
		count:        uint32(l.svcCtx.Config.UIIOSCloudPhoneWorkerConsumer.MaxWorker),
	}

	for _, device := range resp.GetItems() {
		if device.GetType() == commonpb.DeviceType_REAL_PHONE &&
			device.GetPlatform() == commonpb.PlatformType_ANDROID {
			condition11.devices = append(condition11.devices, device.GetUdid())
			condition11.count++
		} else if device.GetType() == commonpb.DeviceType_REAL_PHONE &&
			device.GetPlatform() == commonpb.PlatformType_IOS {
			condition12.devices = append(condition12.devices, device.GetUdid())
			condition12.count++
		} else if device.GetType() == commonpb.DeviceType_CLOUD_PHONE &&
			device.GetPlatform() == commonpb.PlatformType_ANDROID {
			condition21.devices = append(condition21.devices, device.GetUdid())
			condition21.count++
		} else if device.GetType() == commonpb.DeviceType_CLOUD_PHONE &&
			device.GetPlatform() == commonpb.PlatformType_IOS {
			condition22.devices = append(condition22.devices, device.GetUdid())
			condition22.count++
		}
	}

	for _, condition := range []condition{
		condition11, condition12, condition21, condition22,
	} {
		if len(condition.devices) > 0 {
			conditions = append(conditions, condition)
		}
	}

	return conditions, nil
}

func (l *Listener) listen() {
	l.Infof("starting the listener at %s", time.Now().Format("2006-01-02 15:04:05"))

	ps := l.svcCtx.RedisNode.Subscribe(l.ctx, devicehubcommon.ConstChannelNameOfIdleDevicesNotify)
	defer func(ps *redis.PubSub) {
		if err := ps.Close(); err != nil {
			l.Errorf("failed to close PubSub, error: %+v", err)
		}
	}(ps)

	ticker := timewheel.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		var (
			err        error
			conditions []condition
		)

		select {
		case msg, ok := <-ps.Channel():
			l.Infof("got a message from the channel of PubSub, message: %s, ok: %t", msg.String(), ok)
			if !ok {
				l.Error("the channel of PubSub has been closed")
				return
			}

			conditions, err = l.searchDevices()
			if err != nil {
				l.Errorf("failed to search idle devices, error: %+v", err)
				continue
			}
		case <-ticker.C:
			conditions, err = l.searchDevices()
			if err != nil {
				l.Errorf("failed to search idle devices, error: %+v", err)
				continue
			}
		case <-l.ctx.Done():
			l.Debugf("got a done signal while listening idle devices, error: %+v", context.Cause(l.ctx))
			return
		}

		threading.RunSafeCtx(
			l.ctx, func() {
				l.notify(conditions)
			},
		)
	}
}

type condition struct {
	deviceType   commonpb.DeviceType
	platformType commonpb.PlatformType
	devices      []string
	count        uint32
}

func (l *Listener) notify(conditions []condition) {
	if conditions == nil {
		return
	}

	_ = mr.MapReduceVoid[condition, string](
		func(source chan<- condition) {
			for _, c := range conditions {
				source <- c
			}
		}, func(item condition, writer mr.Writer[string], cancel func(error)) {
			tasks, err := l.svcCtx.WaitingTasksModel.FindTasksByPlatformDeviceType(
				l.ctx, int64(item.platformType.Number()), int64(item.deviceType.Number()), int64(item.count),
			)
			if err != nil {
				l.Errorf(
					"failed to find waiting task, type: %s, platform: %s, err: %+v",
					item.deviceType.String(), item.platformType.String(), err,
				)
				return
			}

			tokens := allocateDevices(tasks, item.devices)
			for _, token := range tokens {
				writer.Write(token)
			}
		}, func(pipe <-chan string, cancel func(error)) {
			for token := range pipe {
				l.publish(token)
			}
		},
	)
}

func (l *Listener) publish(token string) {
	channel := common.ConstChannelNameOfIdleDevicesNotifyByToken
	val, err := l.svcCtx.RedisNode.Publish(l.ctx, channel, token).Result()
	if err != nil {
		l.Errorf(
			"failed to send the info of idle devices to the channel, channel: %s, info: %s, error: %+v",
			channel, token, err,
		)
	} else {
		l.Infof(
			"succeeded to send the info of idle devices to the channel, channel: %s, info: %s, subscribers: %d",
			channel, token, val,
		)
	}
}

type simpleTask struct {
	token   string
	devices []string
}

func divideTasks2Device(tasks []*model.WaitingTasks) ([]simpleTask, map[string][]int) {
	var simpleTasks []simpleTask
	// map[device][]task_index
	deviceToTasks := make(map[string][]int)
	for i, task := range tasks {
		var _devices []string
		if task.Devices.Valid {
			_ = jsonx.UnmarshalFromString(task.Devices.String, &_devices)
		}
		for _, _device := range _devices {
			deviceToTasks[_device] = append(deviceToTasks[_device], i)
		}
		simpleTasks = append(simpleTasks, simpleTask{
			token:   task.Token,
			devices: _devices,
		})
	}
	return simpleTasks, deviceToTasks
}

func assignDevice2Task(
	simpleTasks []simpleTask, deviceToTasks map[string][]int, availableDevices map[string]lang.PlaceholderType,
) map[string]string {
	// map[token]device
	allocatedDevices := make(map[string]string)
	for i, simpleTask := range simpleTasks {
		if len(availableDevices) == 0 {
			break
		}

		var (
			allocatedTaskDevice  string
			availableTaskDevices []string
		)
		switch len(simpleTask.devices) {
		case 1: // 指定设备场景
			if _, ok := availableDevices[simpleTask.devices[0]]; ok {
				allocatedTaskDevice = simpleTask.devices[0]
			}
		case 0: // 随机设备场景
			for device := range availableDevices {
				availableTaskDevices = append(availableTaskDevices, device)
			}
			if len(availableTaskDevices) == 0 {
				break
			}
			fallthrough
		default: // 派生设备场景
			if len(availableTaskDevices) == 0 {
				for _, device := range simpleTask.devices {
					if _, ok := availableDevices[device]; ok {
						availableTaskDevices = append(availableTaskDevices, device)
					}
				}
			}
			if len(availableTaskDevices) == 0 {
				break
			}

			var devicesByUsed, devicesNoUsed []string
			for _, device := range availableTaskDevices {
				isUsedByLater := false
				for _, taskIndex := range deviceToTasks[device] {
					if taskIndex > i {
						isUsedByLater = true
						break
					}
				}
				if isUsedByLater {
					devicesByUsed = append(devicesByUsed, device)
				} else {
					devicesNoUsed = append(devicesNoUsed, device)
				}
			}

			// 查找后面没有在用的设备，优先分配使用
			if len(devicesNoUsed) > 0 {
				allocatedTaskDevice = devicesNoUsed[0]
				break
			}

			// 查找后面准备要用的设备，优先分配最后使用的设备
			maxIndex := -1
			for _, device := range devicesByUsed {
				currentMax := -1
				for _, taskIndex := range deviceToTasks[device] {
					if taskIndex > i && taskIndex > maxIndex {
						currentMax = taskIndex
					}
				}
				if currentMax > maxIndex {
					maxIndex = currentMax
					allocatedTaskDevice = device
				}
			}
		}
		if len(allocatedTaskDevice) > 0 {
			allocatedDevices[simpleTask.token] = allocatedTaskDevice
			delete(availableDevices, allocatedTaskDevice)
		}
	}

	return allocatedDevices
}

func allocateDevices(tasks []*model.WaitingTasks, devices []string) (tokens []string) {
	if len(tasks) == 0 || len(devices) == 0 {
		return tokens
	}

	simpleTasks, deviceToTasks := divideTasks2Device(tasks)

	// map[device]placeholder
	availableDevices := make(map[string]lang.PlaceholderType, len(devices))
	for _, device := range devices {
		availableDevices[device] = lang.Placeholder
	}

	allocatedDevices := assignDevice2Task(simpleTasks, deviceToTasks, availableDevices)

	for token, device := range allocatedDevices {
		tokens = append(tokens, token+"_"+device)
	}

	return tokens
}
