package internal

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/svc"
)

type Cleaner struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func newCleaner(svcCtx *svc.ServiceContext) *Cleaner {
	ctx := context.Background()
	return &Cleaner{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (c *Cleaner) clean(days int) error {
	ctx, cancel := context.WithTimeout(c.ctx, common.ConstExpireOfClearWaitingTasksTask)
	defer cancel()

	key := common.ConstLockClearWaitingTasks
	fn := func() error {
		return c.svcCtx.WaitingTasksModel.DeleteBeforeNDaysRecords(ctx, nil, days)
	}
	if err := caller.LockWithOptionDo(
		c.svcCtx.Redis, key, fn,
		redislock.WithExpire(common.ConstExpireOfClearWaitingTasksTask),
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			c.Errorf("failed to clear waiting tasks, key: %s, error: %+v", key, err)
			return err
		}
	} else {
		c.Infof("finished to clear waiting tasks, key: %s, days: %d", key, days)
	}

	return nil
}
