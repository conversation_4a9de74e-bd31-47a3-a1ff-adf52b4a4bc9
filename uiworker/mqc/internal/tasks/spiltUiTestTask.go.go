package tasks

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	producerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	uimanager "gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/manager"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/svc"
)

var _ base.Handler = (*ProcessorSpiltByPlatform)(nil)

type ProcessorSpiltByPlatform struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorSpiltByPlatform(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &ProcessorSpiltByPlatform{
		svcCtx: svcCtx,
	}
}

func (processor *ProcessorSpiltByPlatform) ProcessTask(ctx context.Context, task *base.Task) (
	result []byte, err error,
) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	consume, err := NewSpiltByPlatformTaskLogic(ctx, processor.svcCtx).Consume(task.Payload)
	if err != nil {
		logger.Errorf("processor error: %+v", err)
		return nil, err
	}

	return consume, nil
}

type SpiltByPlatformTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSpiltByPlatformTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SpiltByPlatformTaskLogic {
	return &SpiltByPlatformTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SpiltByPlatformTaskLogic) Consume(payload []byte) (result []byte, err error) {
	var (
		req  dispatcherpb.WorkerReq
		task *base.Task

		typeName string
		producer *producerv2.Producer

		conf consumerv2.Config
	)
	if err = protobuf.UnmarshalJSON(payload, &req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of ui test task, payload: %s, error: %+v",
			payload, err,
		)
	}

	deviceType := req.GetUiCase().GetMetaData().GetDeviceType()
	platformType := req.GetUiCase().GetMetaData().GetPlatformType()
	if deviceType == commonpb.DeviceType_REAL_PHONE && platformType == commonpb.PlatformType_ANDROID {
		typeName = constants.MQTaskTypeUIWorkerExecuteAndroidUiTest
		producer = l.svcCtx.UIAndroidRealPhoneWorkerProducer
		conf = l.svcCtx.Config.UIAndroidRealPhoneWorkerConsumer
	} else if deviceType == commonpb.DeviceType_CLOUD_PHONE && platformType == commonpb.PlatformType_ANDROID {
		typeName = constants.MQTaskTypeUIWorkerExecuteAndroidUiTest
		producer = l.svcCtx.UIAndroidCloudPhoneWorkerProducer
		conf = l.svcCtx.Config.UIAndroidCloudPhoneWorkerConsumer
	} else if deviceType == commonpb.DeviceType_REAL_PHONE && platformType == commonpb.PlatformType_IOS {
		typeName = constants.MQTaskTypeUIWorkerExecuteIOSUiTest
		producer = l.svcCtx.UIIOSRealPhoneWorkerProducer
		conf = l.svcCtx.Config.UIIOSRealPhoneWorkerConsumer
	} else if deviceType == commonpb.DeviceType_CLOUD_PHONE && platformType == commonpb.PlatformType_IOS {
		typeName = constants.MQTaskTypeUIWorkerExecuteIOSUiTest
		producer = l.svcCtx.UIIOSCloudPhoneWorkerProducer
		conf = l.svcCtx.Config.UIIOSCloudPhoneWorkerConsumer
	}

	if typeName == "" || producer == nil {
		return nil, errorx.Errorf(
			errorx.DoesNotSupport,
			"unsupported device type: %s, platform type: %s", deviceType.String(), platformType.String(),
		)
	}

	fn := func(queue string) error {
		task = base.NewTask(
			typeName, payload,
			base.WithMaxRetryOptions(0),
			base.WithTimeoutOptions(1*time.Hour),
			base.WithRetentionOptions(30*time.Minute),
			base.WithQueueOptions(queue),
		)
		_, err = producer.Send(l.ctx, task, mq.ConvertPbEnumerationToQueuePriority(req.GetPriorityType()))
		return err
	}

	var consumers []*uimanager.Consumer
	devices := req.GetUiCase().GetMetaData().GetDevices()
	if len(devices) > 0 {
		together := req.GetUiCase().GetMetaData().GetTogether()
		if together {
			consumers, err = l.startDeviceConsumers(devices, typeName, conf)
		} else {
			udid := req.GetNodeData().GetUiCase().GetUdid()
			consumers, err = l.startDeviceConsumers([]string{udid}, typeName, conf)
		}
		if err != nil {
			return nil, errorx.Errorf(
				errorx.NewObjectFailure,
				"add device consumers failed, error: %+v", err,
			)
		}
	}

	if len(consumers) > 0 {
		for _, consumer := range consumers {
			err = fn(consumer.GetQueueName())
		}
	} else {
		err = fn(conf.Queue)
	}

	return nil, err
}

func (l *SpiltByPlatformTaskLogic) startDeviceConsumers(
	devices []string, typeName string, conf consumerv2.Config,
) (consumers []*uimanager.Consumer, err error) {
	for _, device := range devices {
		consumer, ok := l.svcCtx.ConsumerManager.GetConsumer(device)
		if !ok {
			consumer, err = l.svcCtx.ConsumerManager.AddConsumer(
				device, conf,
				consumerv2.NewTaskHandlerOjb(
					typeName, NewProcessorExecuteUiTest(l.svcCtx),
				),
			)
			if err != nil {
				return consumers, err
			}
		}
		consumers = append(consumers, consumer)
	}
	return consumers, err
}
