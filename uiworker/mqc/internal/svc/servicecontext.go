package svc

import (
	"time"

	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/cronscheduler"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	commonredis "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis/task"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common/zrpc/devicehub"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common/zrpc/manager"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common/zrpc/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/config"
	uimanager "gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/manager"
)

type ServiceContext struct {
	Config config.Config

	DB              sqlx.SqlConn
	Redis           *redis.Redis
	RedisNode       red.UniversalClient
	DispatcherRedis red.UniversalClient
	Scheduler       *cronscheduler.Scheduler

	DeviceHubRPC *devicehub.RPCClient
	ManagerRPC   *manager.RPCClient
	ReporterRPC  *reporter.RPCClient

	UIWorkerConsumer                  *consumer.Consumer
	DispatcherProducer                *producer.Producer
	UIAndroidRealPhoneWorkerProducer  *producer.Producer
	UIAndroidCloudPhoneWorkerProducer *producer.Producer
	UIIOSRealPhoneWorkerProducer      *producer.Producer
	UIIOSCloudPhoneWorkerProducer     *producer.Producer
	UIAndroidRealPhoneWorkerConsumer  *consumer.Consumer
	UIAndroidCloudPhoneWorkerConsumer *consumer.Consumer
	UIIOSRealPhoneWorkerConsumer      *consumer.Consumer
	UIIOSCloudPhoneWorkerConsumer     *consumer.Consumer
	DeviceHubProducer                 *producer.Producer
	ReporterProducer                  *producer.Producer

	WaitingTasksModel model.WaitingTasksModel

	TaskInfoProcessor *task.InfoProcessor
	ConsumerManager   *uimanager.ConsumerManager
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	scheduler := cronscheduler.NewScheduler()
	scheduler.Scheduler.ChangeLocation(time.Local) // change to local timezone

	return &ServiceContext{
		Config: c,

		DB:              sqlConn,
		Redis:           redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),
		RedisNode:       qetredis.NewClient(c.Redis.RedisConf),
		DispatcherRedis: qetredis.NewClient(c.DispatcherRedis),
		Scheduler:       scheduler,

		DeviceHubRPC: devicehub.NewRPCClient(c.DeviceHub),
		ManagerRPC:   manager.NewRPCClient(c.Manager),
		ReporterRPC:  reporter.NewRPCClient(c.Reporter),

		UIWorkerConsumer:                  consumer.NewConsumer(c.UIWorkerConsumer),
		DispatcherProducer:                producer.NewProducer(c.DispatcherProducer),
		UIAndroidRealPhoneWorkerProducer:  producer.NewProducer(c.UIAndroidRealPhoneWorkerProducer),
		UIAndroidCloudPhoneWorkerProducer: producer.NewProducer(c.UIAndroidCloudPhoneWorkerProducer),
		UIIOSRealPhoneWorkerProducer:      producer.NewProducer(c.UIIOSRealPhoneWorkerProducer),
		UIIOSCloudPhoneWorkerProducer:     producer.NewProducer(c.UIIOSCloudPhoneWorkerProducer),
		UIAndroidRealPhoneWorkerConsumer:  consumer.NewConsumer(c.UIAndroidRealPhoneWorkerConsumer),
		UIAndroidCloudPhoneWorkerConsumer: consumer.NewConsumer(c.UIAndroidCloudPhoneWorkerConsumer),
		UIIOSRealPhoneWorkerConsumer:      consumer.NewConsumer(c.UIIOSRealPhoneWorkerConsumer),
		UIIOSCloudPhoneWorkerConsumer:     consumer.NewConsumer(c.UIIOSCloudPhoneWorkerConsumer),
		DeviceHubProducer:                 producer.NewProducer(c.DeviceHubProducer),
		ReporterProducer:                  producer.NewProducer(c.ReporterProducer),

		WaitingTasksModel: model.NewWaitingTasksModel(sqlConn, c.Cache),

		TaskInfoProcessor: task.NewTaskInfoProcessor(
			commonredis.NewRCacheService(
				c.DispatcherRedis, commonredis.DispatcherRedisKey,
			),
		),
		ConsumerManager: uimanager.NewConsumerManager(c.UIWorkerConsumer),
	}
}
