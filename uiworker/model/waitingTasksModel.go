package model

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	_ WaitingTasksModel = (*customWaitingTasksModel)(nil)

	waitingTasksInsertFields = stringx.Remove(
		waitingTasksFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// WaitingTasksModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWaitingTasksModel.
	WaitingTasksModel interface {
		waitingTasksModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *WaitingTasks) squirrel.InsertBuilder
		UpdateBuilder(data *WaitingTasks) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*WaitingTasks, error)

		FindTasksByPlatformDeviceType(ctx context.Context, platform, deviceType, count int64) ([]*WaitingTasks, error)
		RemoveByTaskID(ctx context.Context, session sqlx.Session, taskID string) (sql.Result, error)
		RemoveByToken(ctx context.Context, session sqlx.Session, token string) (sql.Result, error)
		UpdateAllToDeleted(ctx context.Context, session sqlx.Session) (sql.Result, error)
		FindAll(ctx context.Context) ([]*WaitingTasks, error)
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
	}

	customWaitingTasksModel struct {
		*defaultWaitingTasksModel

		conn sqlx.SqlConn
	}
)

// NewWaitingTasksModel returns a model for the database table.
func NewWaitingTasksModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) WaitingTasksModel {
	return &customWaitingTasksModel{
		defaultWaitingTasksModel: newWaitingTasksModel(conn, c, opts...),
		conn:                     conn,
	}
}

func (m *customWaitingTasksModel) Table() string {
	return m.table
}

func (m *customWaitingTasksModel) Fields() []string {
	return waitingTasksFieldNames
}

func (m *customWaitingTasksModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customWaitingTasksModel) InsertBuilder(data *WaitingTasks) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(waitingTasksInsertFields...).Values(
		data.Platform, data.DeviceType, data.Token, data.Devices, data.Deleted,
	)
}

func (m *customWaitingTasksModel) UpdateBuilder(data *WaitingTasks) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`platform`":    data.Platform,
		"`device_type`": data.DeviceType,
		"`token`":       data.Token,
		"`devices`":     data.Devices,
		"`deleted`":     data.Deleted,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customWaitingTasksModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(waitingTasksFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customWaitingTasksModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customWaitingTasksModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customWaitingTasksModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*WaitingTasks, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*WaitingTasks
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customWaitingTasksModel) FindTasksByPlatformDeviceType(
	ctx context.Context, platform, deviceType, count int64,
) ([]*WaitingTasks, error) {
	/*
		SQL:
		SELECT id, platform, device_type, token, devices
		FROM waiting_tasks
		WHERE platform = ?
		  AND device_type = ?
		  AND deleted = 0
		ORDER BY created_at DESC;
	*/
	fields := []string{
		"`id`",
		"`platform`",
		"`device_type`",
		"`token`",
		"`devices`",
		"`deleted`",
		"`created_at`",
		"`updated_at`",
		"`deleted_at`",
	}
	query, values, err := squirrel.Select(fields...).
		From(m.tableName()).Where(
		"`platform` = ? AND `device_type` = ? AND `deleted` = ?", platform, deviceType, constants.NotDeleted,
	).OrderBy("`created_at` ASC").Limit(uint64(count)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*WaitingTasks

	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customWaitingTasksModel) RemoveByTaskID(ctx context.Context, session sqlx.Session, taskID string) (
	sql.Result, error,
) {
	keys := m.getKeysByTaskID(ctx, taskID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_at`": time.Now(),
					},
				).Where(squirrel.Like{"`token`": taskID + "_%"}).ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customWaitingTasksModel) getKeysByTaskID(ctx context.Context, taskID string) []string {
	sb := m.SelectBuilder().Where(squirrel.Like{"`token`": taskID + "_%"})
	ts, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(ts))
	for _, t := range ts {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheUiworkerWaitingTasksIdPrefix, t.Id),
		)
	}

	return keys
}

func (m *customWaitingTasksModel) RemoveByToken(ctx context.Context, session sqlx.Session, token string) (
	sql.Result, error,
) {
	keys := m.getKeysByToken(ctx, token)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE waiting_tasks
				SET deleted = 1, deleted_at = NOW()
				WHERE token = ?;
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_at`": time.Now(),
					},
				).Where("`token` = ?", token).ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customWaitingTasksModel) getKeysByToken(ctx context.Context, token string) []string {
	sb := m.SelectBuilder().Where("`token` = ?", token)
	ts, err := m.FindNoCacheByQuery(ctx, sb)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(ts))
	for _, t := range ts {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheUiworkerWaitingTasksIdPrefix, t.Id),
		)
	}

	return keys
}

func (m *customWaitingTasksModel) UpdateAllToDeleted(ctx context.Context, session sqlx.Session) (sql.Result, error) {
	keys := m.getAllKeys(ctx)

	ub := squirrel.Update(m.table).
		SetMap(
			squirrel.Eq{
				"`deleted`":    constants.HasDeleted,
				"`deleted_at`": time.Now(),
			},
		).
		Where("`deleted` = ?", constants.NotDeleted)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := ub.ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, keys...,
	)
}

func (m *customWaitingTasksModel) getAllKeys(ctx context.Context) []string {
	data, err := m.FindAll(ctx)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(data))
	for _, d := range data {
		keys = append(
			keys,
			fmt.Sprintf("%s%v", cacheUiworkerWaitingTasksIdPrefix, d.Id),
		)
	}

	return keys
}

func (m *customWaitingTasksModel) FindAll(ctx context.Context) ([]*WaitingTasks, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder())
}

func (m *customWaitingTasksModel) DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error {
	keys := m.getKeysOfBeforeNDaysRecords(ctx, days)

	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			stmt, values, err := squirrel.Delete(m.table).
				Where("`created_at` < DATE_SUB(CURDATE(), INTERVAL ? DAY)", days).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}

			return m.conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
	return err
}

func (m *customWaitingTasksModel) getKeysOfBeforeNDaysRecords(ctx context.Context, days int) []string {
	rs, err := m.FindNoCacheByQuery(
		ctx,
		squirrel.Select(waitingTasksFieldNames...).Where("`created_at` < DATE_SUB(CURDATE(), INTERVAL ? DAY)", days),
	)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(rs))
	for _, r := range rs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheUiworkerWaitingTasksIdPrefix, r.Id))
	}

	return keys
}
