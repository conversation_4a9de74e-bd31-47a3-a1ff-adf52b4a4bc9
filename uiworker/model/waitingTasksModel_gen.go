// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	waitingTasksTableName           = "`waiting_tasks`"
	waitingTasksFieldNames          = builder.RawFieldNames(&WaitingTasks{})
	waitingTasksRows                = strings.Join(waitingTasksFieldNames, ",")
	waitingTasksRowsExpectAutoSet   = strings.Join(stringx.Remove(waitingTasksFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	waitingTasksRowsWithPlaceHolder = strings.Join(stringx.Remove(waitingTasksFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheUiworkerWaitingTasksIdPrefix = "cache:uiworker:waitingTasks:id:"
)

type (
	waitingTasksModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *WaitingTasks) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*WaitingTasks, error)
		Update(ctx context.Context, session sqlx.Session, data *WaitingTasks) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultWaitingTasksModel struct {
		sqlc.CachedConn
		table string
	}

	WaitingTasks struct {
		Id         int64          `db:"id"`          // 自增ID
		Platform   int64          `db:"platform"`    // 操作系统，安卓1，IOS2，鸿蒙3
		DeviceType int64          `db:"device_type"` // 设备类型，真机1，云手机2
		Token      string         `db:"token"`       // 上报/通知标识
		Devices    sql.NullString `db:"devices"`     // 指定设备（编号）
		Deleted    int64          `db:"deleted"`     // 逻辑删除标识（未删除、已删除）
		CreatedAt  time.Time      `db:"created_at"`  // 创建时间
		UpdatedAt  time.Time      `db:"updated_at"`  // 更新时间
		DeletedAt  sql.NullTime   `db:"deleted_at"`  // 删除时间
	}
)

func newWaitingTasksModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultWaitingTasksModel {
	return &defaultWaitingTasksModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`waiting_tasks`",
	}
}

func (m *defaultWaitingTasksModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	uiworkerWaitingTasksIdKey := fmt.Sprintf("%s%v", cacheUiworkerWaitingTasksIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, uiworkerWaitingTasksIdKey)
	return err
}

func (m *defaultWaitingTasksModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	uiworkerWaitingTasksIdKey := fmt.Sprintf("%s%v", cacheUiworkerWaitingTasksIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, uiworkerWaitingTasksIdKey)
	return err
}

func (m *defaultWaitingTasksModel) FindOne(ctx context.Context, id int64) (*WaitingTasks, error) {
	uiworkerWaitingTasksIdKey := fmt.Sprintf("%s%v", cacheUiworkerWaitingTasksIdPrefix, id)
	var resp WaitingTasks
	err := m.QueryRowCtx(ctx, &resp, uiworkerWaitingTasksIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", waitingTasksRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultWaitingTasksModel) Insert(ctx context.Context, session sqlx.Session, data *WaitingTasks) (sql.Result, error) {
	uiworkerWaitingTasksIdKey := fmt.Sprintf("%s%v", cacheUiworkerWaitingTasksIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, waitingTasksRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.Platform, data.DeviceType, data.Token, data.Devices, data.Deleted)
		}
		return conn.ExecCtx(ctx, query, data.Platform, data.DeviceType, data.Token, data.Devices, data.Deleted)
	}, uiworkerWaitingTasksIdKey)
}

func (m *defaultWaitingTasksModel) Update(ctx context.Context, session sqlx.Session, data *WaitingTasks) (sql.Result, error) {

	uiworkerWaitingTasksIdKey := fmt.Sprintf("%s%v", cacheUiworkerWaitingTasksIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, waitingTasksRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.Platform, data.DeviceType, data.Token, data.Devices, data.Deleted, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.Platform, data.DeviceType, data.Token, data.Devices, data.Deleted, data.Id)
	}, uiworkerWaitingTasksIdKey)
}

func (m *defaultWaitingTasksModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheUiworkerWaitingTasksIdPrefix, primary)
}

func (m *defaultWaitingTasksModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", waitingTasksRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultWaitingTasksModel) tableName() string {
	return m.table
}
