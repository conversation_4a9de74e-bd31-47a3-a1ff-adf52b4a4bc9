-- auto-generated definition
create schema uiworker collate utf8mb4_general_ci;

grant delete, insert, select, update on uiworker.* to probe;

use uiworker;
create table waiting_tasks
(
    id           int auto_increment COMMENT '自增ID',
    platform     tinyint(1)  not null comment '操作系统，安卓1，IOS2，鸿蒙3',
    device_type  tinyint(1)  not null comment '设备类型，真机1，云手机2',
    token        varchar(64) not null comment '上报/通知标识',
    `deleted`    TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` TIMESTAMP   NULL COMMENT '删除时间',
    constraint waiting_tasks_pk primary key (id)
) comment '等待设备任务表';
create unique index waiting_tasks_id_uindex on waiting_tasks (id);
create index waiting_tasks_platform_type_index on waiting_tasks (platform, device_type)