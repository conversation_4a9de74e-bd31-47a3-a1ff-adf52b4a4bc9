name: Create release draft

on:
  workflow_call:
    inputs:
      tag_name:
        description: Tag to create a release for.
        required: true
        type: string
  workflow_dispatch:
    inputs:
      tag_name:
        description: Tag to create a release for.
        required: true
        type: string
  push:
    tags:
      - v*

jobs:
  create-release-draft:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.tag_name || github.ref_name }}
      - name: Create release tarball
        run: .github/workflows/release_prep.sh ${{ github.event.inputs.tag_name || github.ref_name }}
      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          draft: true
          tag_name: ${{ github.event.inputs.tag_name || github.ref_name }}
          body_path: release_notes.md
          generate_release_notes: true
          files: |
            protovalidate-*.tar.gz

