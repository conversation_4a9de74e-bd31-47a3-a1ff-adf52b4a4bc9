---
name: DIREGAPIC Updater
on:  # yamllint disable-line rule:truthy
  schedule:
  - cron: '19 */8 * * *'
  workflow_dispatch:

jobs:
  regenerate-compute:
    if: github.repository == 'googleapis/googleapis'
    runs-on: ubuntu-latest
    container: gcr.io/gapic-images/googleapis:20230531
    steps:
    - name: Checkout master
      uses: actions/checkout@v3
      with:
        ref: master
    - name: Download discovery docs
      run: |
        curl https://raw.githubusercontent.com/googleapis/discovery-artifact-manager/refs/heads/master/discoveries/compute.v1.json --output google/cloud/compute/v1/compute.v1.json
        echo compute_revision=$(grep -oP '"revision":\s*"\d+"' google/cloud/compute/v1/compute.v1.json | grep -oP '\d+') >> $GITHUB_ENV
    - name: Regenerate API definitions
      run: |
        git config --global --add safe.directory /__w/googleapis/googleapis
        bazelisk build --experimental_convenience_symlinks=normal //google/cloud/compute/v1:compute_gen
        cp bazel-bin/google/cloud/compute/v1/compute_gen.proto google/cloud/compute/v1/compute.proto
        bazelisk build --experimental_convenience_symlinks=normal //google/cloud/compute/v1:compute_grpc_service_config_gen
        cp bazel-bin/google/cloud/compute/v1/compute_grpc_service_config_gen.json google/cloud/compute/v1/compute_grpc_service_config.json
        bazelisk build --experimental_convenience_symlinks=normal //google/cloud/compute/v1:compute_gapic_gen
        cp bazel-bin/google/cloud/compute/v1/compute_gapic_gen.yaml google/cloud/compute/v1/compute_gapic.yaml
        echo api_changes=$(git diff-index --shortstat HEAD) >> $GITHUB_ENV
    - name: Build GAPIC clients
      if: contains(env.api_changes, 'file')
      run: |
        bazelisk build --experimental_convenience_symlinks=normal //google/cloud/compute/v1/...
        bazelisk build --experimental_convenience_symlinks=normal //google/cloud/compute/v1/...
    - name: Create PR
      uses: googleapis/code-suggester@v2
      env:
        ACCESS_TOKEN: ${{ secrets.YOSHI_CODE_BOT_TOKEN }}
      with:
        command: pr
        upstream_owner: googleapis
        upstream_repo: googleapis
        title: 'feat: [DIREGAPIC] Update API definitions'
        description: 'feat: Update Compute Engine API to revision ${{ env.compute_revision }}'
        message: 'feat: Update Compute Engine API to revision ${{ env.compute_revision }}'
        primary: 'master'
        branch: diregapic
        git_dir: '.'
        force: true
        fork: true
