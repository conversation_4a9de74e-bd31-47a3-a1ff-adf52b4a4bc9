name: CI
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch: {} # support manual runs
permissions:
  contents: read
  pull-requests: write
jobs:
  ci:
    name: CI
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - name: Install bazelisk
        uses: bazelbuild/setup-bazelisk@v3
      - name: Mount bazel cache
        uses: actions/cache@v4
        with:
          path: "~/.cache/bazel"
          key: bazel
      - name: Install go
        uses: actions/setup-go@v5
        with:
          go-version: stable
          cache-dependency-path: go.work.sum
      - name: Install buf
        uses: bufbuild/buf-action@v1
        with:
          setup_only: true
      - name: Buf generate
        run: |
          mkdir -p .tmp/bin
          cp $(which buf) .tmp/bin/buf
          make checkgenerate
      - name: Test
        run: make test
      - name: Lint
        run: make lint-go
