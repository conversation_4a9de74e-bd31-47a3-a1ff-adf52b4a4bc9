name: Create release tag

on:
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name for release (e.g. "v1.0.0")'
        required: true

permissions:
  contents: write

jobs:
  create-release-tag:
    name: Create release tag
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
    
      - name: Create tag
        uses: actions/github-script@v7
        with:
          script: |
            const tag = '${{ github.event.inputs.tag_name }}';
            const toolsTag = `tools/${tag}`;
            const commitTag = await github.rest.git.createTag({
              owner: context.repo.owner,
              repo: context.repo.repo,
              tag,
              message: tag,
              object: context.sha,
              type: 'commit',
            });
            const toolsCommitTag = await github.rest.git.createTag({
              owner: context.repo.owner,
              repo: context.repo.repo,
              tag: toolsTag,
              message: toolsTag,
              object: context.sha,
              type: 'commit',
            });
            await github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: `refs/tags/${tag}`,
              sha: commitTag.data.sha,
            });
            await github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: `refs/tags/${toolsTag}`,
              sha: toolsCommitTag.data.sha,
            });

  create-release-draft:
    name: Start release automation
    uses: ./.github/workflows/create-release-draft.yaml
    with:
      tag_name: ${{ github.event.inputs.tag_name }}
