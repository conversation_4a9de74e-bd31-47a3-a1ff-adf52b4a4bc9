name: Generate API Index
on:  # yamllint disable-line rule:truthy
  push:
    branches: [master]
  workflow_dispatch:
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Setup .NET Core SDK
      uses: actions/setup-dotnet@v3
    - name: Checkout googleapis (this repository)
      uses: actions/checkout@v3
      with:
        path: googleapis
    - name: Checkout index generator
      uses: actions/checkout@v3
      with:
        repository: googleapis/googleapis-api-index-generator
        path: gen
    - name: Generate API index
      run: |
        gen/scripts/generate-schema.sh
        gen/scripts/generate-index.sh $PWD/googleapis
        cp gen/tmp/api-index-v1.json $PWD/googleapis
    - name: Create PR
      uses: googleapis/code-suggester@v2
      env:
        ACCESS_TOKEN: ${{ secrets.YOSHI_CODE_BOT_TOKEN }}
      with:
        command: pr
        upstream_owner: googleapis
        upstream_repo: googleapis
        title: 'chore: regenerate API index'
        description: 'chore: regenerate API index'
        message: 'chore: regenerate API index'
        primary: 'master'
        branch: api-index
        git_dir: 'googleapis/.'
        force: true
        fork: true
