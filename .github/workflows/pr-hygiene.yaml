name: PR Hygiene
# Prevent writing to the repository using the CI token.
# Ref: https://docs.github.com/en/actions/reference/workflow-syntax-for-github-actions#permissions
permissions:
  pull-requests: read
on:
  workflow_call:
  pull_request:
    # By default, a workflow only runs when a pull_request's activity type is opened,
    # synchronize, or reopened. We explicity override here so that PR titles are
    # re-linted when the PR text content is edited.
    types:
      - opened
      - edited
      - reopened
      - synchronize
jobs:
  lint-title:
    name: Lint title
    runs-on: ubuntu-latest
    steps:
      - name: Lint title
        uses: morrisoncole/pr-lint-action@v1.7.1
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          # https://regex101.com/r/I6oK5v/1
          title-regex: "^[A-Z].*[^.?!,\\-;:]$"
          on-failed-regex-fail-action: true
          on-failed-regex-create-review: false
          on-failed-regex-request-changes: false
          on-failed-regex-comment: "PR titles must start with a capital letter and not end with punctuation."
          on-succeeded-regex-dismiss-review-comment: "Thanks for helping keep our PR titles consistent!"
