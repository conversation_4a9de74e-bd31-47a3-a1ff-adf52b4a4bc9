---
name: Feature request
about: Suggest an idea for this project
title: "[Feature Request] "
labels: Feature
assignees: ''
---

**Feature description:** 
<!-- A clear and concise description of the feature you would like to see implemented in protovalidate.-->

**Problem it solves or use case:** 
<!-- Explain the problem this feature would solve or the use case it addresses, and how it would benefit users of protovalidate.-->

**Proposed implementation or solution:** 
<!-- If you have a suggestion on how this feature can be implemented or any ideas on the solution, please describe them here.-->

**Contribution:**
<!--Describe how you would like to contribute this feature to the project. If you are willing to implement the feature yourself, please indicate that here. If you are not willing to implement it yourself, please indicate if you would be willing to help others implement it.-->

**Examples or references:** 
<!-- If applicable, provide examples or references to similar features in other libraries or tools.-->

**Additional context:** 
<!-- Add any other context or information about the feature request here.-->
