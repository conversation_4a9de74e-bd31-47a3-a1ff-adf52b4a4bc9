---
name: Bug report
about: Create a report to help us improve
title: "[BUG] "
labels: Bug
assignees: ''
---

## Description
<!--Provide a clear and concise description of the bug. Be specific and include any relevant information about the issue, such as what you were trying to accomplish, what the expected behavior was, and how the actual behavior differed.-->

## Steps to Reproduce
<!--
1. First step to reproduce the bug
2. Second step to reproduce the bug
3. Third step to reproduce the bug
... additional steps as needed 
-->

## Expected Behavior

<!--Describe what you expected to happen when following the steps to reproduce the bug.-->

## Actual Behavior

<!--Describe what actually happened instead of the expected behavior.-->

## Screenshots/Logs

<!--If applicable, add screenshots and/or log files to help explain the bug.-->

## Environment

- **Operating System**: <!--[e.g., macOS, Windows, Linux]-->
- **Version**: <!--[e.g., macOS 10.15.7, Windows 10, Ubuntu 20.04]-->
- **Compiler/Toolchain**: <!--[e.g., GCC 9.3.0, Clang 10.0.0]-->
- **Protobuf Compiler & Version**: <!--[e.g. buf v1.17.0, protoc 3.17.3]-->
- **Protoc-gen-validate Version**: <!--[if applicable, e.g., v0.6.1]-->
- **Protovalidate Version**: <!--[if applicable, e.g., v1.0.2]-->

## Possible Solution
<!--If you have any suggestions on how the bug could be fixed or have identified the source of the problem, please provide your insights here.-->

## Additional Context
<!--Any other information, context, or relevant details that could be helpful for understanding and resolving the bug.-->

<!--Please make sure to provide as much information as possible to help the maintainers diagnose and fix the issue.-->
