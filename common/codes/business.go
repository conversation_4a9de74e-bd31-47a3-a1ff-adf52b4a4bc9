package codes

// 自定义业务错误码: 52000 - 53999

// `common`: 52000 - 52099
const (
	_ = MinCustomBusinessCode + baseIndexOfCommon + iota // 52000: min

	_ = MinCustomBusinessCode + baseIndexOfCommon + defaultInterval // 52099: max
)

// `account`: 52100 - 52199
const (
	_ = MinCustomBusinessCode + baseIndexOfAccount + iota // 52100: min

	_ = MinCustomBusinessCode + baseIndexOfAccount + defaultInterval // 52199: max
)

// `manager`: 52200 - 52299
const (
	_ = MinCustomBusinessCode + baseIndexOfManager + iota // 52200: min

	ParseInterfaceFileFailure       // 52201: 解析接口文件失败
	PlanHasDisableFailure           // 52202: 计划已经失效
	InsufficientProjectDevicesError // 52203: 项目设备不足

	_ = MinCustomBusinessCode + baseIndexOfManager + defaultInterval // 52299: max
)

// `dispatcher`: 52300 - 52399
const (
	_ = MinCustomBusinessCode + baseIndexOfDispatcher + iota // 52300: min

	TaskExecuteCaseFailure // 52301: 执行用例失败

	_ = MinCustomBusinessCode + baseIndexOfDispatcher + defaultInterval // 52399: max
)

// `apiworker`: 52400 - 52599
const (
	_ = MinCustomBusinessCode + baseIndexOfApiWorker + iota // 52400: min

	// 任务级别: 52400 - 52419
	TaskInvalidBaseUrl

	_ = MinCustomBusinessCode + baseIndexOfApiWorker + (defaultInterval*2 + 1) // 52599: max
)

const (
	// 节点级别(通用): 52420 - 52429
	NodeSkip = MinCustomBusinessCode + baseIndexOfApiWorker + 20 + iota
	NodeChildFailure
	NodeImportNotFound
	NodeExportNotFound
)

const (
	// 节点级别(断言): 52430 - 52439
	NodeAssertFailure = MinCustomBusinessCode + baseIndexOfApiWorker + 30 + iota
	NodeAssertFailureInvalidActual
	NodeAssertFailureInvalidExpected
	NodeAssertFailureCompareFailure
)

const (
	// 节点级别(数据处理): 52440 - 52449
	NodeFunctionArgsNotFound = MinCustomBusinessCode + baseIndexOfApiWorker + 40 + iota
	NodeFunctionExecFailure
)

const (
	// 节点级别(HTTP组件): 52450 - 52459
	NodeHTTPInvalidUrl = MinCustomBusinessCode + baseIndexOfApiWorker + 50 + iota
	NodeHTTPRequestFailure
	NodeHTTPAssertFailure
)

const (
	// 节点级别(账号池): 52460 - 52469
	NodeAccountNotEnv = MinCustomBusinessCode + baseIndexOfApiWorker + 60 + iota
	NodePoolAccountNotFound
)

const (
	// 节点级别(条件组件): 52470 - 52479
	NodeConditionLeftValueNotFound = MinCustomBusinessCode + baseIndexOfApiWorker + 70 + iota
	NodeConditionRightValueNotFound
	NodeConditionAssertFailure
)

const (
	// 节点级别(SQL组件): 52480 - 52489
	NodeSQLParamInvalid = MinCustomBusinessCode + baseIndexOfApiWorker + 80 + iota
	NodeSQLExecFailure
)

// `reporter`: 52600 - 52699
const (
	_ = MinCustomBusinessCode + baseIndexOfReporter + iota // 52600: min

	_ = MinCustomBusinessCode + baseIndexOfReporter + defaultInterval // 52699: max
)

// `worker`: 52700 - 52799
const (
	_ = MinCustomBusinessCode + baseIndexOfWorker + iota // 52700: min

	PlanMonitorTaskExecuteFailure // 52701: 计划监控任务执行失败
	ParsePythonProjectTaskFailure // 52702: 解析Python项目任务执行失败

	ClonePerfTestProtobufFailure // 52703: 克隆压测protobuf源码执行失败
	PullPerfTestProtobufFailure  // 52704: 拉取压测protobuf源码执行失败

	_ = MinCustomBusinessCode + baseIndexOfWorker + defaultInterval // 52799: max
)

// `relation`: 52800 - 52899
const (
	_ = MinCustomBusinessCode + baseIndexOfRelation + iota // 52800: min

	_ = MinCustomBusinessCode + baseIndexOfRelation + defaultInterval // 52899: max
)

// `devicehub`: 52900 - 52999
const (
	_ = MinCustomBusinessCode + baseIndexOfDeviceHub + iota // 52900: min

	AcquireDeviceFailure     // 52901: 获取设备失败
	ReleaseDeviceFailure     // 52902: 释放设备失败
	InsufficientDevicesError // 52903: 设备不足

	_ = MinCustomBusinessCode + baseIndexOfDeviceHub + defaultInterval // 52999: max
)

// `uiworker`: 53000 - 53099
const (
	_ = MinCustomBusinessCode + baseIndexOfUIWorker + iota // 53000: min

	ExecuteSubTaskFailure

	_ = MinCustomBusinessCode + baseIndexOfUIWorker + defaultInterval // 53099: max
)

// `uiworker`: 53000 - 53099
const (
	_ = MinCustomBusinessCode + baseIndexOfAi + iota // 53000: min

	ExternalFailure                   // 外部错误
	TheDoesNotBelongToTheOwnerFailure // 助手为个人模式，创建会话者不属于拥有者

	_ = MinCustomBusinessCode + baseIndexOfAi + defaultInterval // 53099: max
)
