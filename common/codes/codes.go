package codes

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

type Code = errorx.Code

const (
	MinCustomSystemCode   Code = 12000
	MaxCustomSystemCode   Code = 13999
	MinCustomBusinessCode Code = 52000
	MaxCustomBusinessCode Code = 53999

	defaultInterval = errorx.DefaultInterval

	baseIndexOfCommon     = 0
	baseIndexOfAccount    = 100
	baseIndexOfManager    = 200
	baseIndexOfDispatcher = 300
	baseIndexOfApiWorker  = 400
	baseIndexOfReporter   = 600
	baseIndexOfWorker     = 700
	baseIndexOfRelation   = 800
	baseIndexOfDeviceHub  = 900
	baseIndexOfUIWorker   = 1000
	baseIndexOfAi         = 1100
)
