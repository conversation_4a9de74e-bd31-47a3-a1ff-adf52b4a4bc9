package codes

// 自定义系统错误码: 12000 - 13999

// `common`: 12000 - 12099
const (
	_ = MinCustomSystemCode + baseIndexOfCommon + iota // 12000: min

	ParseOrRenderTemplateFailure // 12001: 解析或渲染模板失败
	CallTemplateFunctionFailure  // 12001: 调用模板函数失败

	_ = MinCustomSystemCode + baseIndexOfCommon + defaultInterval // 12099: max
)

// `account`: 12100 - 12199
const (
	_ = MinCustomSystemCode + baseIndexOfAccount + iota // 12100: min

	_ = MinCustomSystemCode + baseIndexOfAccount + defaultInterval // 12199: max
)

// `manager`: 12200 - 12299
const (
	_ = MinCustomSystemCode + baseIndexOfManager + iota // 12200: min

	GenerateApiExecutionDataFailure // 12201: 生成API执行数据失败
	StructDiffFailure               // 12202: 结构体差异对比失败
	BuiltinOperationFailure         // 12203: 内建操作失败
	HavelikedFailure                // 12203: 已经点赞不要重复操作

	_ = MinCustomSystemCode + baseIndexOfManager + defaultInterval // 12299: max
)

// `dispatcher`: 12300 - 12399
const (
	_ = MinCustomSystemCode + baseIndexOfDispatcher + iota // 12300: min

	TaskPanic
	TaskUnmarshalFailure
	TaskRecordFailure
	TaskPublishFailure
	TaskInvalid
	TaskDBFailure
	TaskNotifyFailure

	_ = MinCustomSystemCode + baseIndexOfDispatcher + defaultInterval // 12399: max
)

// `apiworker`: 12400 - 12499
const (
	_ = MinCustomSystemCode + baseIndexOfApiWorker + iota // 12400: min

	// 节点级别(通用): 12401 - 12419
	NodePanic
	NodeNotRegister
	NodeNotFunc
	NodeRecordFailure
	NodeWriteFileFailure
	NodeInvalid

	_ = MinCustomSystemCode + baseIndexOfApiWorker + defaultInterval // 12499: max
)

const (
	// 节点级别(账号池): 12420 - 12429
	NodePoolAccountReleaseFailure = MinCustomSystemCode + baseIndexOfApiWorker + 20 + iota
	NodePoolAccountQueryEnvFailure
	NodePoolAccountInvalidType
)

const (
	// 节点级别(HTTP): 12430 - 12439
	NodeHTTPBodyFailure = MinCustomSystemCode + baseIndexOfApiWorker + 30 + iota
)

// `reporter`: 12600 - 12699
const (
	_ = MinCustomSystemCode + baseIndexOfReporter + iota // 12600: min

	_ = MinCustomSystemCode + baseIndexOfReporter + defaultInterval // 12699: max
)

// `worker`: 12700 - 12799
const (
	_ = MinCustomSystemCode + baseIndexOfWorker + iota // 12700: min

	_ = MinCustomSystemCode + baseIndexOfWorker + defaultInterval // 12799: max
)
