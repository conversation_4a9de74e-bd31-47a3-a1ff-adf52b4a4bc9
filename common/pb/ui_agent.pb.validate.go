// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/ui_agent.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UIAgentComponentExpectation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentComponentExpectation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentComponentExpectation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentComponentExpectationMultiError, or nil if none found.
func (m *UIAgentComponentExpectation) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentComponentExpectation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	if m.GetImage() != "" {

		if !_UIAgentComponentExpectation_Image_Pattern.MatchString(m.GetImage()) {
			err := UIAgentComponentExpectationValidationError{
				field:  "Image",
				reason: "value does not match regex pattern \"(?:^ui_agent_image_id:.+?)\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return UIAgentComponentExpectationMultiError(errors)
	}

	return nil
}

// UIAgentComponentExpectationMultiError is an error wrapping multiple
// validation errors returned by UIAgentComponentExpectation.ValidateAll() if
// the designated constraints aren't met.
type UIAgentComponentExpectationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentComponentExpectationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentComponentExpectationMultiError) AllErrors() []error { return m }

// UIAgentComponentExpectationValidationError is the validation error returned
// by UIAgentComponentExpectation.Validate if the designated constraints
// aren't met.
type UIAgentComponentExpectationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentComponentExpectationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentComponentExpectationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentComponentExpectationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentComponentExpectationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentComponentExpectationValidationError) ErrorName() string {
	return "UIAgentComponentExpectationValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentComponentExpectationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentComponentExpectation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentComponentExpectationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentComponentExpectationValidationError{}

var _UIAgentComponentExpectation_Image_Pattern = regexp.MustCompile("(?:^ui_agent_image_id:.+?)")

// Validate checks the field values on UIAgentComponentStep with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentComponentStep) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentComponentStep with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentComponentStepMultiError, or nil if none found.
func (m *UIAgentComponentStep) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentComponentStep) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetContent()) < 1 {
		err := UIAgentComponentStepValidationError{
			field:  "Content",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetExpectation() == nil {
		err := UIAgentComponentStepValidationError{
			field:  "Expectation",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetExpectation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentStepValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentStepValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentStepValidationError{
				field:  "Expectation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetWaitingTime(); val < 0 || val > 300 {
		err := UIAgentComponentStepValidationError{
			field:  "WaitingTime",
			reason: "value must be inside range [0, 300]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UIAgentComponentStepMultiError(errors)
	}

	return nil
}

// UIAgentComponentStepMultiError is an error wrapping multiple validation
// errors returned by UIAgentComponentStep.ValidateAll() if the designated
// constraints aren't met.
type UIAgentComponentStepMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentComponentStepMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentComponentStepMultiError) AllErrors() []error { return m }

// UIAgentComponentStepValidationError is the validation error returned by
// UIAgentComponentStep.Validate if the designated constraints aren't met.
type UIAgentComponentStepValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentComponentStepValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentComponentStepValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentComponentStepValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentComponentStepValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentComponentStepValidationError) ErrorName() string {
	return "UIAgentComponentStepValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentComponentStepValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentComponentStep.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentComponentStepValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentComponentStepValidationError{}

// Validate checks the field values on UIAgentDevice with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UIAgentDevice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentDevice with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UIAgentDeviceMultiError, or
// nil if none found.
func (m *UIAgentDevice) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentDevice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Device.(type) {
	case *UIAgentDevice_ProjectDevice_:
		if v == nil {
			err := UIAgentDeviceValidationError{
				field:  "Device",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetProjectDevice() == nil {
			err := UIAgentDeviceValidationError{
				field:  "ProjectDevice",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProjectDevice()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentDeviceValidationError{
						field:  "ProjectDevice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentDeviceValidationError{
						field:  "ProjectDevice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProjectDevice()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentDeviceValidationError{
					field:  "ProjectDevice",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UIAgentDevice_UserDevice_:
		if v == nil {
			err := UIAgentDeviceValidationError{
				field:  "Device",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if m.GetUserDevice() == nil {
			err := UIAgentDeviceValidationError{
				field:  "UserDevice",
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUserDevice()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentDeviceValidationError{
						field:  "UserDevice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentDeviceValidationError{
						field:  "UserDevice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUserDevice()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentDeviceValidationError{
					field:  "UserDevice",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return UIAgentDeviceMultiError(errors)
	}

	return nil
}

// UIAgentDeviceMultiError is an error wrapping multiple validation errors
// returned by UIAgentDevice.ValidateAll() if the designated constraints
// aren't met.
type UIAgentDeviceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentDeviceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentDeviceMultiError) AllErrors() []error { return m }

// UIAgentDeviceValidationError is the validation error returned by
// UIAgentDevice.Validate if the designated constraints aren't met.
type UIAgentDeviceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentDeviceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentDeviceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentDeviceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentDeviceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentDeviceValidationError) ErrorName() string { return "UIAgentDeviceValidationError" }

// Error satisfies the builtin error interface
func (e UIAgentDeviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentDevice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentDeviceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentDeviceValidationError{}

// Validate checks the field values on UIAgentDevice_ProjectDevice with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentDevice_ProjectDevice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentDevice_ProjectDevice with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentDevice_ProjectDeviceMultiError, or nil if none found.
func (m *UIAgentDevice_ProjectDevice) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentDevice_ProjectDevice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _UIAgentDevice_ProjectDevice_DeviceType_NotInLookup[m.GetDeviceType()]; ok {
		err := UIAgentDevice_ProjectDeviceValidationError{
			field:  "DeviceType",
			reason: "value must not be in list [DT_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _UIAgentDevice_ProjectDevice_PlatformType_NotInLookup[m.GetPlatformType()]; ok {
		err := UIAgentDevice_ProjectDeviceValidationError{
			field:  "PlatformType",
			reason: "value must not be in list [PT_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetUdid()); l < 1 || l > 64 {
		err := UIAgentDevice_ProjectDeviceValidationError{
			field:  "Udid",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRemoteAddress()) < 1 {
		err := UIAgentDevice_ProjectDeviceValidationError{
			field:  "RemoteAddress",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetToken()); l < 1 || l > 64 {
		err := UIAgentDevice_ProjectDeviceValidationError{
			field:  "Token",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UIAgentDevice_ProjectDeviceMultiError(errors)
	}

	return nil
}

// UIAgentDevice_ProjectDeviceMultiError is an error wrapping multiple
// validation errors returned by UIAgentDevice_ProjectDevice.ValidateAll() if
// the designated constraints aren't met.
type UIAgentDevice_ProjectDeviceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentDevice_ProjectDeviceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentDevice_ProjectDeviceMultiError) AllErrors() []error { return m }

// UIAgentDevice_ProjectDeviceValidationError is the validation error returned
// by UIAgentDevice_ProjectDevice.Validate if the designated constraints
// aren't met.
type UIAgentDevice_ProjectDeviceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentDevice_ProjectDeviceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentDevice_ProjectDeviceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentDevice_ProjectDeviceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentDevice_ProjectDeviceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentDevice_ProjectDeviceValidationError) ErrorName() string {
	return "UIAgentDevice_ProjectDeviceValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentDevice_ProjectDeviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentDevice_ProjectDevice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentDevice_ProjectDeviceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentDevice_ProjectDeviceValidationError{}

var _UIAgentDevice_ProjectDevice_DeviceType_NotInLookup = map[DeviceType]struct{}{
	0: {},
}

var _UIAgentDevice_ProjectDevice_PlatformType_NotInLookup = map[PlatformType]struct{}{
	0: {},
}

// Validate checks the field values on UIAgentDevice_UserDevice with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentDevice_UserDevice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentDevice_UserDevice with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentDevice_UserDeviceMultiError, or nil if none found.
func (m *UIAgentDevice_UserDevice) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentDevice_UserDevice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _UIAgentDevice_UserDevice_DeviceType_NotInLookup[m.GetDeviceType()]; ok {
		err := UIAgentDevice_UserDeviceValidationError{
			field:  "DeviceType",
			reason: "value must not be in list [DT_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _UIAgentDevice_UserDevice_PlatformType_NotInLookup[m.GetPlatformType()]; ok {
		err := UIAgentDevice_UserDeviceValidationError{
			field:  "PlatformType",
			reason: "value must not be in list [PT_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetUdid() != "" {

		if utf8.RuneCountInString(m.GetUdid()) > 64 {
			err := UIAgentDevice_UserDeviceValidationError{
				field:  "Udid",
				reason: "value length must be at most 64 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if utf8.RuneCountInString(m.GetRemoteAddress()) < 1 {
		err := UIAgentDevice_UserDeviceValidationError{
			field:  "RemoteAddress",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UIAgentDevice_UserDeviceMultiError(errors)
	}

	return nil
}

// UIAgentDevice_UserDeviceMultiError is an error wrapping multiple validation
// errors returned by UIAgentDevice_UserDevice.ValidateAll() if the designated
// constraints aren't met.
type UIAgentDevice_UserDeviceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentDevice_UserDeviceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentDevice_UserDeviceMultiError) AllErrors() []error { return m }

// UIAgentDevice_UserDeviceValidationError is the validation error returned by
// UIAgentDevice_UserDevice.Validate if the designated constraints aren't met.
type UIAgentDevice_UserDeviceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentDevice_UserDeviceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentDevice_UserDeviceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentDevice_UserDeviceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentDevice_UserDeviceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentDevice_UserDeviceValidationError) ErrorName() string {
	return "UIAgentDevice_UserDeviceValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentDevice_UserDeviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentDevice_UserDevice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentDevice_UserDeviceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentDevice_UserDeviceValidationError{}

var _UIAgentDevice_UserDevice_DeviceType_NotInLookup = map[DeviceType]struct{}{
	0: {},
}

var _UIAgentDevice_UserDevice_PlatformType_NotInLookup = map[PlatformType]struct{}{
	0: {},
}
