// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/config.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GeneralConfigVar with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GeneralConfigVar) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GeneralConfigVar with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GeneralConfigVarMultiError, or nil if none found.
func (m *GeneralConfigVar) ValidateAll() error {
	return m.validate(true)
}

func (m *GeneralConfigVar) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Key

	// no validation rules for Value

	if len(errors) > 0 {
		return GeneralConfigVarMultiError(errors)
	}

	return nil
}

// GeneralConfigVarMultiError is an error wrapping multiple validation errors
// returned by GeneralConfigVar.ValidateAll() if the designated constraints
// aren't met.
type GeneralConfigVarMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GeneralConfigVarMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GeneralConfigVarMultiError) AllErrors() []error { return m }

// GeneralConfigVarValidationError is the validation error returned by
// GeneralConfigVar.Validate if the designated constraints aren't met.
type GeneralConfigVarValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GeneralConfigVarValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GeneralConfigVarValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GeneralConfigVarValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GeneralConfigVarValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GeneralConfigVarValidationError) ErrorName() string { return "GeneralConfigVarValidationError" }

// Error satisfies the builtin error interface
func (e GeneralConfigVarValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGeneralConfigVar.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GeneralConfigVarValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GeneralConfigVarValidationError{}

// Validate checks the field values on GeneralConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GeneralConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GeneralConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GeneralConfigMultiError, or
// nil if none found.
func (m *GeneralConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GeneralConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for BaseUrl

	// no validation rules for Verify

	for idx, item := range m.GetVariables() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GeneralConfigValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GeneralConfigValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GeneralConfigValidationError{
					field:  fmt.Sprintf("Variables[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GeneralConfigMultiError(errors)
	}

	return nil
}

// GeneralConfigMultiError is an error wrapping multiple validation errors
// returned by GeneralConfig.ValidateAll() if the designated constraints
// aren't met.
type GeneralConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GeneralConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GeneralConfigMultiError) AllErrors() []error { return m }

// GeneralConfigValidationError is the validation error returned by
// GeneralConfig.Validate if the designated constraints aren't met.
type GeneralConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GeneralConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GeneralConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GeneralConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GeneralConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GeneralConfigValidationError) ErrorName() string { return "GeneralConfigValidationError" }

// Error satisfies the builtin error interface
func (e GeneralConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGeneralConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GeneralConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GeneralConfigValidationError{}

// Validate checks the field values on AccountConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountConfigMultiError, or
// nil if none found.
func (m *AccountConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for ProductType

	// no validation rules for ProductName

	// no validation rules for PoolEnvTable

	// no validation rules for PoolEnvName

	if len(errors) > 0 {
		return AccountConfigMultiError(errors)
	}

	return nil
}

// AccountConfigMultiError is an error wrapping multiple validation errors
// returned by AccountConfig.ValidateAll() if the designated constraints
// aren't met.
type AccountConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountConfigMultiError) AllErrors() []error { return m }

// AccountConfigValidationError is the validation error returned by
// AccountConfig.Validate if the designated constraints aren't met.
type AccountConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountConfigValidationError) ErrorName() string { return "AccountConfigValidationError" }

// Error satisfies the builtin error interface
func (e AccountConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountConfigValidationError{}

// Validate checks the field values on GitConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GitConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GitConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GitConfigMultiError, or nil
// if none found.
func (m *GitConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GitConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Type

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Url

	// no validation rules for AccessToken

	// no validation rules for Branch

	if len(errors) > 0 {
		return GitConfigMultiError(errors)
	}

	return nil
}

// GitConfigMultiError is an error wrapping multiple validation errors returned
// by GitConfig.ValidateAll() if the designated constraints aren't met.
type GitConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GitConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GitConfigMultiError) AllErrors() []error { return m }

// GitConfigValidationError is the validation error returned by
// GitConfig.Validate if the designated constraints aren't met.
type GitConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GitConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GitConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GitConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GitConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GitConfigValidationError) ErrorName() string { return "GitConfigValidationError" }

// Error satisfies the builtin error interface
func (e GitConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGitConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GitConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GitConfigValidationError{}

// Validate checks the field values on ProtobufConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProtobufConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProtobufConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProtobufConfigMultiError,
// or nil if none found.
func (m *ProtobufConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ProtobufConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Name

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetGitConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProtobufConfigValidationError{
					field:  "GitConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProtobufConfigValidationError{
					field:  "GitConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGitConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProtobufConfigValidationError{
				field:  "GitConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ImportPath

	for idx, item := range m.GetDependencies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProtobufConfigValidationError{
						field:  fmt.Sprintf("Dependencies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProtobufConfigValidationError{
						field:  fmt.Sprintf("Dependencies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProtobufConfigValidationError{
					field:  fmt.Sprintf("Dependencies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProtobufConfigMultiError(errors)
	}

	return nil
}

// ProtobufConfigMultiError is an error wrapping multiple validation errors
// returned by ProtobufConfig.ValidateAll() if the designated constraints
// aren't met.
type ProtobufConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProtobufConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProtobufConfigMultiError) AllErrors() []error { return m }

// ProtobufConfigValidationError is the validation error returned by
// ProtobufConfig.Validate if the designated constraints aren't met.
type ProtobufConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProtobufConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProtobufConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProtobufConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProtobufConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProtobufConfigValidationError) ErrorName() string { return "ProtobufConfigValidationError" }

// Error satisfies the builtin error interface
func (e ProtobufConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProtobufConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProtobufConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProtobufConfigValidationError{}

// Validate checks the field values on ProtobufDependenceConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProtobufDependenceConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProtobufDependenceConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProtobufDependenceConfigMultiError, or nil if none found.
func (m *ProtobufDependenceConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ProtobufDependenceConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Name

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetGitConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProtobufDependenceConfigValidationError{
					field:  "GitConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProtobufDependenceConfigValidationError{
					field:  "GitConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGitConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProtobufDependenceConfigValidationError{
				field:  "GitConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ImportPath

	if len(errors) > 0 {
		return ProtobufDependenceConfigMultiError(errors)
	}

	return nil
}

// ProtobufDependenceConfigMultiError is an error wrapping multiple validation
// errors returned by ProtobufDependenceConfig.ValidateAll() if the designated
// constraints aren't met.
type ProtobufDependenceConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProtobufDependenceConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProtobufDependenceConfigMultiError) AllErrors() []error { return m }

// ProtobufDependenceConfigValidationError is the validation error returned by
// ProtobufDependenceConfig.Validate if the designated constraints aren't met.
type ProtobufDependenceConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProtobufDependenceConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProtobufDependenceConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProtobufDependenceConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProtobufDependenceConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProtobufDependenceConfigValidationError) ErrorName() string {
	return "ProtobufDependenceConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ProtobufDependenceConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProtobufDependenceConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProtobufDependenceConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProtobufDependenceConfigValidationError{}

// Validate checks the field values on PromptConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PromptConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PromptConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PromptConfigMultiError, or
// nil if none found.
func (m *PromptConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *PromptConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Purpose

	// no validation rules for Category

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Content

	if len(errors) > 0 {
		return PromptConfigMultiError(errors)
	}

	return nil
}

// PromptConfigMultiError is an error wrapping multiple validation errors
// returned by PromptConfig.ValidateAll() if the designated constraints aren't met.
type PromptConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PromptConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PromptConfigMultiError) AllErrors() []error { return m }

// PromptConfigValidationError is the validation error returned by
// PromptConfig.Validate if the designated constraints aren't met.
type PromptConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PromptConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PromptConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PromptConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PromptConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PromptConfigValidationError) ErrorName() string { return "PromptConfigValidationError" }

// Error satisfies the builtin error interface
func (e PromptConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPromptConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PromptConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PromptConfigValidationError{}

// Validate checks the field values on ApplicationConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ApplicationConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicationConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicationConfigMultiError, or nil if none found.
func (m *ApplicationConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicationConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for PlatformType

	// no validation rules for AppId

	// no validation rules for AppDownloadLink

	for idx, item := range m.GetPrompts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApplicationConfigValidationError{
						field:  fmt.Sprintf("Prompts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApplicationConfigValidationError{
						field:  fmt.Sprintf("Prompts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApplicationConfigValidationError{
					field:  fmt.Sprintf("Prompts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ApplicationConfigMultiError(errors)
	}

	return nil
}

// ApplicationConfigMultiError is an error wrapping multiple validation errors
// returned by ApplicationConfig.ValidateAll() if the designated constraints
// aren't met.
type ApplicationConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicationConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicationConfigMultiError) AllErrors() []error { return m }

// ApplicationConfigValidationError is the validation error returned by
// ApplicationConfig.Validate if the designated constraints aren't met.
type ApplicationConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicationConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicationConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicationConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicationConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicationConfigValidationError) ErrorName() string {
	return "ApplicationConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicationConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicationConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicationConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicationConfigValidationError{}
