// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/task.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PeriodicPlanTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PeriodicPlanTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PeriodicPlanTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PeriodicPlanTaskInfoMultiError, or nil if none found.
func (m *PeriodicPlanTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PeriodicPlanTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_PeriodicPlanTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := PeriodicPlanTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_PeriodicPlanTaskInfo_PlanId_Pattern.MatchString(m.GetPlanId()) {
		err := PeriodicPlanTaskInfoValidationError{
			field:  "PlanId",
			reason: "value does not match regex pattern \"(?:^plan|^ui_plan|^perf_plan|^stability_plan)_id:.+?\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CronExpression

	// no validation rules for PlanType

	if len(errors) > 0 {
		return PeriodicPlanTaskInfoMultiError(errors)
	}

	return nil
}

// PeriodicPlanTaskInfoMultiError is an error wrapping multiple validation
// errors returned by PeriodicPlanTaskInfo.ValidateAll() if the designated
// constraints aren't met.
type PeriodicPlanTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PeriodicPlanTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PeriodicPlanTaskInfoMultiError) AllErrors() []error { return m }

// PeriodicPlanTaskInfoValidationError is the validation error returned by
// PeriodicPlanTaskInfo.Validate if the designated constraints aren't met.
type PeriodicPlanTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PeriodicPlanTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PeriodicPlanTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PeriodicPlanTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PeriodicPlanTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PeriodicPlanTaskInfoValidationError) ErrorName() string {
	return "PeriodicPlanTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PeriodicPlanTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPeriodicPlanTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PeriodicPlanTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PeriodicPlanTaskInfoValidationError{}

var _PeriodicPlanTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _PeriodicPlanTaskInfo_PlanId_Pattern = regexp.MustCompile("(?:^plan|^ui_plan|^perf_plan|^stability_plan)_id:.+?")

// Validate checks the field values on ParsePythonProjectTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ParsePythonProjectTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParsePythonProjectTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ParsePythonProjectTaskInfoMultiError, or nil if none found.
func (m *ParsePythonProjectTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ParsePythonProjectTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ParsePythonProjectTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := ParsePythonProjectTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetConfig() == nil {
		err := ParsePythonProjectTaskInfoValidationError{
			field:  "Config",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ParsePythonProjectTaskInfoValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ParsePythonProjectTaskInfoValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ParsePythonProjectTaskInfoValidationError{
				field:  "Config",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _ParsePythonProjectTaskInfo_TriggerMode_InLookup[m.GetTriggerMode()]; !ok {
		err := ParsePythonProjectTaskInfoValidationError{
			field:  "TriggerMode",
			reason: "value must be in list [MANUAL INTERFACE]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetTriggerAccount()); l < 1 || l > 64 {
		err := ParsePythonProjectTaskInfoValidationError{
			field:  "TriggerAccount",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTriggerTime() <= 0 {
		err := ParsePythonProjectTaskInfoValidationError{
			field:  "TriggerTime",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ParsePythonProjectTaskInfoMultiError(errors)
	}

	return nil
}

// ParsePythonProjectTaskInfoMultiError is an error wrapping multiple
// validation errors returned by ParsePythonProjectTaskInfo.ValidateAll() if
// the designated constraints aren't met.
type ParsePythonProjectTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParsePythonProjectTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParsePythonProjectTaskInfoMultiError) AllErrors() []error { return m }

// ParsePythonProjectTaskInfoValidationError is the validation error returned
// by ParsePythonProjectTaskInfo.Validate if the designated constraints aren't met.
type ParsePythonProjectTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParsePythonProjectTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParsePythonProjectTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParsePythonProjectTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParsePythonProjectTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParsePythonProjectTaskInfoValidationError) ErrorName() string {
	return "ParsePythonProjectTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ParsePythonProjectTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParsePythonProjectTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParsePythonProjectTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParsePythonProjectTaskInfoValidationError{}

var _ParsePythonProjectTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _ParsePythonProjectTaskInfo_TriggerMode_InLookup = map[TriggerMode]struct{}{
	1: {},
	3: {},
}

// Validate checks the field values on ParsePythonProjectTaskResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ParsePythonProjectTaskResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParsePythonProjectTaskResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ParsePythonProjectTaskResultMultiError, or nil if none found.
func (m *ParsePythonProjectTaskResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ParsePythonProjectTaskResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ParsePythonProjectTaskResult_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := ParsePythonProjectTaskResultValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ParsePythonProjectTaskResult_ConfigId_Pattern.MatchString(m.GetConfigId()) {
		err := ParsePythonProjectTaskResultValidationError{
			field:  "ConfigId",
			reason: "value does not match regex pattern \"(?:^git_config_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRootNode() == nil {
		err := ParsePythonProjectTaskResultValidationError{
			field:  "RootNode",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRootNode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ParsePythonProjectTaskResultValidationError{
					field:  "RootNode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ParsePythonProjectTaskResultValidationError{
					field:  "RootNode",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRootNode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ParsePythonProjectTaskResultValidationError{
				field:  "RootNode",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetTriggerAccount()); l < 1 || l > 64 {
		err := ParsePythonProjectTaskResultValidationError{
			field:  "TriggerAccount",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTriggerTime() <= 0 {
		err := ParsePythonProjectTaskResultValidationError{
			field:  "TriggerTime",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ParsePythonProjectTaskResultMultiError(errors)
	}

	return nil
}

// ParsePythonProjectTaskResultMultiError is an error wrapping multiple
// validation errors returned by ParsePythonProjectTaskResult.ValidateAll() if
// the designated constraints aren't met.
type ParsePythonProjectTaskResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParsePythonProjectTaskResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParsePythonProjectTaskResultMultiError) AllErrors() []error { return m }

// ParsePythonProjectTaskResultValidationError is the validation error returned
// by ParsePythonProjectTaskResult.Validate if the designated constraints
// aren't met.
type ParsePythonProjectTaskResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParsePythonProjectTaskResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParsePythonProjectTaskResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParsePythonProjectTaskResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParsePythonProjectTaskResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParsePythonProjectTaskResultValidationError) ErrorName() string {
	return "ParsePythonProjectTaskResultValidationError"
}

// Error satisfies the builtin error interface
func (e ParsePythonProjectTaskResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParsePythonProjectTaskResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParsePythonProjectTaskResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParsePythonProjectTaskResultValidationError{}

var _ParsePythonProjectTaskResult_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _ParsePythonProjectTaskResult_ConfigId_Pattern = regexp.MustCompile("(?:^git_config_id:.+?)")

// Validate checks the field values on ProtobufTarget with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProtobufTarget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProtobufTarget with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProtobufTargetMultiError,
// or nil if none found.
func (m *ProtobufTarget) ValidateAll() error {
	return m.validate(true)
}

func (m *ProtobufTarget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPath()) < 1 {
		err := ProtobufTargetValidationError{
			field:  "Path",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetImportPaths()) > 0 {

		_ProtobufTarget_ImportPaths_Unique := make(map[string]struct{}, len(m.GetImportPaths()))

		for idx, item := range m.GetImportPaths() {
			_, _ = idx, item

			if _, exists := _ProtobufTarget_ImportPaths_Unique[item]; exists {
				err := ProtobufTargetValidationError{
					field:  fmt.Sprintf("ImportPaths[%v]", idx),
					reason: "repeated value must contain unique items",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			} else {
				_ProtobufTarget_ImportPaths_Unique[item] = struct{}{}
			}

			if utf8.RuneCountInString(item) < 1 {
				err := ProtobufTargetValidationError{
					field:  fmt.Sprintf("ImportPaths[%v]", idx),
					reason: "value length must be at least 1 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	}

	if len(m.GetExcludePaths()) > 0 {

		_ProtobufTarget_ExcludePaths_Unique := make(map[string]struct{}, len(m.GetExcludePaths()))

		for idx, item := range m.GetExcludePaths() {
			_, _ = idx, item

			if _, exists := _ProtobufTarget_ExcludePaths_Unique[item]; exists {
				err := ProtobufTargetValidationError{
					field:  fmt.Sprintf("ExcludePaths[%v]", idx),
					reason: "repeated value must contain unique items",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			} else {
				_ProtobufTarget_ExcludePaths_Unique[item] = struct{}{}
			}

			if utf8.RuneCountInString(item) < 1 {
				err := ProtobufTargetValidationError{
					field:  fmt.Sprintf("ExcludePaths[%v]", idx),
					reason: "value length must be at least 1 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	}

	if len(m.GetExcludeFiles()) > 0 {

		_ProtobufTarget_ExcludeFiles_Unique := make(map[string]struct{}, len(m.GetExcludeFiles()))

		for idx, item := range m.GetExcludeFiles() {
			_, _ = idx, item

			if _, exists := _ProtobufTarget_ExcludeFiles_Unique[item]; exists {
				err := ProtobufTargetValidationError{
					field:  fmt.Sprintf("ExcludeFiles[%v]", idx),
					reason: "repeated value must contain unique items",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			} else {
				_ProtobufTarget_ExcludeFiles_Unique[item] = struct{}{}
			}

			if utf8.RuneCountInString(item) < 1 {
				err := ProtobufTargetValidationError{
					field:  fmt.Sprintf("ExcludeFiles[%v]", idx),
					reason: "value length must be at least 1 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	}

	if len(errors) > 0 {
		return ProtobufTargetMultiError(errors)
	}

	return nil
}

// ProtobufTargetMultiError is an error wrapping multiple validation errors
// returned by ProtobufTarget.ValidateAll() if the designated constraints
// aren't met.
type ProtobufTargetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProtobufTargetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProtobufTargetMultiError) AllErrors() []error { return m }

// ProtobufTargetValidationError is the validation error returned by
// ProtobufTarget.Validate if the designated constraints aren't met.
type ProtobufTargetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProtobufTargetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProtobufTargetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProtobufTargetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProtobufTargetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProtobufTargetValidationError) ErrorName() string { return "ProtobufTargetValidationError" }

// Error satisfies the builtin error interface
func (e ProtobufTargetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProtobufTarget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProtobufTargetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProtobufTargetValidationError{}

// Validate checks the field values on PerfTestTaskInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfTestTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfTestTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfTestTaskInfoMultiError, or nil if none found.
func (m *PerfTestTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfTestTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_PerfTestTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := PerfTestTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_PerfTestTaskInfo_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := PerfTestTaskInfoValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_PerfTestTaskInfo_ExecuteId_Pattern.MatchString(m.GetExecuteId()) {
		err := PerfTestTaskInfoValidationError{
			field:  "ExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_PerfTestTaskInfo_SuiteExecuteId_Pattern.MatchString(m.GetSuiteExecuteId()) {
		err := PerfTestTaskInfoValidationError{
			field:  "SuiteExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_PerfTestTaskInfo_PlanExecuteId_Pattern.MatchString(m.GetPlanExecuteId()) {
		err := PerfTestTaskInfoValidationError{
			field:  "PlanExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _PerfTestTaskInfo_TriggerMode_InLookup[m.GetTriggerMode()]; !ok {
		err := PerfTestTaskInfoValidationError{
			field:  "TriggerMode",
			reason: "value must be in list [MANUAL SCHEDULE]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _PerfTestTaskInfo_ExecuteMode_NotInLookup[m.GetExecuteMode()]; ok {
		err := PerfTestTaskInfoValidationError{
			field:  "ExecuteMode",
			reason: "value must not be in list [PTEM_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _PerfTestTaskInfo_Protocol_NotInLookup[m.GetProtocol()]; ok {
		err := PerfTestTaskInfoValidationError{
			field:  "Protocol",
			reason: "value must not be in list [PROTOCOL_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _PerfTestTaskInfo_TargetEnv_NotInLookup[m.GetTargetEnv()]; ok {
		err := PerfTestTaskInfoValidationError{
			field:  "TargetEnv",
			reason: "value must not be in list [TE_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetProtobufTargets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfTestTaskInfoValidationError{
						field:  fmt.Sprintf("ProtobufTargets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfTestTaskInfoValidationError{
						field:  fmt.Sprintf("ProtobufTargets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfTestTaskInfoValidationError{
					field:  fmt.Sprintf("ProtobufTargets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetGeneralConfig() == nil {
		err := PerfTestTaskInfoValidationError{
			field:  "GeneralConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetGeneralConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "GeneralConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeneralConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfTestTaskInfoValidationError{
				field:  "GeneralConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// skipping validation for keepalive

	if m.GetPerfCasePath() != "" {

		if utf8.RuneCountInString(m.GetPerfCasePath()) < 1 {
			err := PerfTestTaskInfoValidationError{
				field:  "PerfCasePath",
				reason: "value length must be at least 1 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if utf8.RuneCountInString(m.GetPerfDataPath()) < 1 {
		err := PerfTestTaskInfoValidationError{
			field:  "PerfDataPath",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDuration() < 0 {
		err := PerfTestTaskInfoValidationError{
			field:  "Duration",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTimes() < 0 {
		err := PerfTestTaskInfoValidationError{
			field:  "Times",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPerfCase() == nil {
		err := PerfTestTaskInfoValidationError{
			field:  "PerfCase",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPerfCase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "PerfCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "PerfCase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfCase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfTestTaskInfoValidationError{
				field:  "PerfCase",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRateLimits()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "RateLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfTestTaskInfoValidationError{
					field:  "RateLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRateLimits()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfTestTaskInfoValidationError{
				field:  "RateLimits",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetTimeout() <= 0 {
		err := PerfTestTaskInfoValidationError{
			field:  "Timeout",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTotalOfVu() <= 0 {
		err := PerfTestTaskInfoValidationError{
			field:  "TotalOfVu",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTotalOfLg() <= 0 {
		err := PerfTestTaskInfoValidationError{
			field:  "TotalOfLg",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PerfTestTaskInfoMultiError(errors)
	}

	return nil
}

// PerfTestTaskInfoMultiError is an error wrapping multiple validation errors
// returned by PerfTestTaskInfo.ValidateAll() if the designated constraints
// aren't met.
type PerfTestTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfTestTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfTestTaskInfoMultiError) AllErrors() []error { return m }

// PerfTestTaskInfoValidationError is the validation error returned by
// PerfTestTaskInfo.Validate if the designated constraints aren't met.
type PerfTestTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfTestTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfTestTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfTestTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfTestTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfTestTaskInfoValidationError) ErrorName() string { return "PerfTestTaskInfoValidationError" }

// Error satisfies the builtin error interface
func (e PerfTestTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfTestTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfTestTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfTestTaskInfoValidationError{}

var _PerfTestTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _PerfTestTaskInfo_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")

var _PerfTestTaskInfo_ExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")

var _PerfTestTaskInfo_SuiteExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")

var _PerfTestTaskInfo_PlanExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")

var _PerfTestTaskInfo_TriggerMode_InLookup = map[TriggerMode]struct{}{
	1: {},
	2: {},
}

var _PerfTestTaskInfo_ExecuteMode_NotInLookup = map[PerfTaskExecutionMode]struct{}{
	0: {},
}

var _PerfTestTaskInfo_Protocol_NotInLookup = map[Protocol]struct{}{
	0: {},
}

var _PerfTestTaskInfo_TargetEnv_NotInLookup = map[TargetEnvironment]struct{}{
	0: {},
}

// Validate checks the field values on LarkChatDisbandedTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LarkChatDisbandedTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LarkChatDisbandedTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LarkChatDisbandedTaskInfoMultiError, or nil if none found.
func (m *LarkChatDisbandedTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LarkChatDisbandedTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	// no validation rules for Name

	// no validation rules for External

	if len(errors) > 0 {
		return LarkChatDisbandedTaskInfoMultiError(errors)
	}

	return nil
}

// LarkChatDisbandedTaskInfoMultiError is an error wrapping multiple validation
// errors returned by LarkChatDisbandedTaskInfo.ValidateAll() if the
// designated constraints aren't met.
type LarkChatDisbandedTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LarkChatDisbandedTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LarkChatDisbandedTaskInfoMultiError) AllErrors() []error { return m }

// LarkChatDisbandedTaskInfoValidationError is the validation error returned by
// LarkChatDisbandedTaskInfo.Validate if the designated constraints aren't met.
type LarkChatDisbandedTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LarkChatDisbandedTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LarkChatDisbandedTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LarkChatDisbandedTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LarkChatDisbandedTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LarkChatDisbandedTaskInfoValidationError) ErrorName() string {
	return "LarkChatDisbandedTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e LarkChatDisbandedTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLarkChatDisbandedTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LarkChatDisbandedTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LarkChatDisbandedTaskInfoValidationError{}

// Validate checks the field values on LarkChatUpdatedTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LarkChatUpdatedTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LarkChatUpdatedTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LarkChatUpdatedTaskInfoMultiError, or nil if none found.
func (m *LarkChatUpdatedTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LarkChatUpdatedTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	// no validation rules for Avatar

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for External

	if len(errors) > 0 {
		return LarkChatUpdatedTaskInfoMultiError(errors)
	}

	return nil
}

// LarkChatUpdatedTaskInfoMultiError is an error wrapping multiple validation
// errors returned by LarkChatUpdatedTaskInfo.ValidateAll() if the designated
// constraints aren't met.
type LarkChatUpdatedTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LarkChatUpdatedTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LarkChatUpdatedTaskInfoMultiError) AllErrors() []error { return m }

// LarkChatUpdatedTaskInfoValidationError is the validation error returned by
// LarkChatUpdatedTaskInfo.Validate if the designated constraints aren't met.
type LarkChatUpdatedTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LarkChatUpdatedTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LarkChatUpdatedTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LarkChatUpdatedTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LarkChatUpdatedTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LarkChatUpdatedTaskInfoValidationError) ErrorName() string {
	return "LarkChatUpdatedTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e LarkChatUpdatedTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLarkChatUpdatedTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LarkChatUpdatedTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LarkChatUpdatedTaskInfoValidationError{}

// Validate checks the field values on LarkChatBotDeletedTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LarkChatBotDeletedTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LarkChatBotDeletedTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LarkChatBotDeletedTaskInfoMultiError, or nil if none found.
func (m *LarkChatBotDeletedTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LarkChatBotDeletedTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatId

	// no validation rules for Name

	// no validation rules for External

	if len(errors) > 0 {
		return LarkChatBotDeletedTaskInfoMultiError(errors)
	}

	return nil
}

// LarkChatBotDeletedTaskInfoMultiError is an error wrapping multiple
// validation errors returned by LarkChatBotDeletedTaskInfo.ValidateAll() if
// the designated constraints aren't met.
type LarkChatBotDeletedTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LarkChatBotDeletedTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LarkChatBotDeletedTaskInfoMultiError) AllErrors() []error { return m }

// LarkChatBotDeletedTaskInfoValidationError is the validation error returned
// by LarkChatBotDeletedTaskInfo.Validate if the designated constraints aren't met.
type LarkChatBotDeletedTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LarkChatBotDeletedTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LarkChatBotDeletedTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LarkChatBotDeletedTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LarkChatBotDeletedTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LarkChatBotDeletedTaskInfoValidationError) ErrorName() string {
	return "LarkChatBotDeletedTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e LarkChatBotDeletedTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLarkChatBotDeletedTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LarkChatBotDeletedTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LarkChatBotDeletedTaskInfoValidationError{}

// Validate checks the field values on StabilityCaseTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityCaseTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityCaseTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityCaseTaskInfoMultiError, or nil if none found.
func (m *StabilityCaseTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityCaseTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_StabilityCaseTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := StabilityCaseTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StabilityCaseTaskInfo_PlanId_Pattern.MatchString(m.GetPlanId()) {
		err := StabilityCaseTaskInfoValidationError{
			field:  "PlanId",
			reason: "value does not match regex pattern \"(?:^stability_plan_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StabilityCaseTaskInfo_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := StabilityCaseTaskInfoValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StabilityCaseTaskInfo_ExecuteId_Pattern.MatchString(m.GetExecuteId()) {
		err := StabilityCaseTaskInfoValidationError{
			field:  "ExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StabilityCaseTaskInfo_PlanExecuteId_Pattern.MatchString(m.GetPlanExecuteId()) {
		err := StabilityCaseTaskInfoValidationError{
			field:  "PlanExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _StabilityCaseTaskInfo_TriggerMode_NotInLookup[m.GetTriggerMode()]; ok {
		err := StabilityCaseTaskInfoValidationError{
			field:  "TriggerMode",
			reason: "value must not be in list [NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAccountConfig() == nil {
		err := StabilityCaseTaskInfoValidationError{
			field:  "AccountConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityCaseTaskInfoValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityCaseTaskInfoValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityCaseTaskInfoValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _StabilityCaseTaskInfo_DeviceType_InLookup[m.GetDeviceType()]; !ok {
		err := StabilityCaseTaskInfoValidationError{
			field:  "DeviceType",
			reason: "value must be in list [REAL_PHONE]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _StabilityCaseTaskInfo_PlatformType_InLookup[m.GetPlatformType()]; !ok {
		err := StabilityCaseTaskInfoValidationError{
			field:  "PlatformType",
			reason: "value must be in list [ANDROID]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUdid()) < 1 {
		err := StabilityCaseTaskInfoValidationError{
			field:  "Udid",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPackageName()) < 1 {
		err := StabilityCaseTaskInfoValidationError{
			field:  "PackageName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAppDownloadLink() != "" {

		if uri, err := url.Parse(m.GetAppDownloadLink()); err != nil {
			err = StabilityCaseTaskInfoValidationError{
				field:  "AppDownloadLink",
				reason: "value must be a valid URI",
				cause:  err,
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		} else if !uri.IsAbs() {
			err := StabilityCaseTaskInfoValidationError{
				field:  "AppDownloadLink",
				reason: "value must be absolute",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(m.GetActivities()) > 0 {

		_StabilityCaseTaskInfo_Activities_Unique := make(map[string]struct{}, len(m.GetActivities()))

		for idx, item := range m.GetActivities() {
			_, _ = idx, item

			if _, exists := _StabilityCaseTaskInfo_Activities_Unique[item]; exists {
				err := StabilityCaseTaskInfoValidationError{
					field:  fmt.Sprintf("Activities[%v]", idx),
					reason: "repeated value must contain unique items",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			} else {
				_StabilityCaseTaskInfo_Activities_Unique[item] = struct{}{}
			}

			if utf8.RuneCountInString(item) < 1 {
				err := StabilityCaseTaskInfoValidationError{
					field:  fmt.Sprintf("Activities[%v]", idx),
					reason: "value length must be at least 1 runes",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}

	}

	if all {
		switch v := interface{}(m.GetCustomScript()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityCaseTaskInfoValidationError{
					field:  "CustomScript",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityCaseTaskInfoValidationError{
					field:  "CustomScript",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomScript()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityCaseTaskInfoValidationError{
				field:  "CustomScript",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetDuration() <= 0 {
		err := StabilityCaseTaskInfoValidationError{
			field:  "Duration",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetExecutedBy()); l < 1 || l > 64 {
		err := StabilityCaseTaskInfoValidationError{
			field:  "ExecutedBy",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return StabilityCaseTaskInfoMultiError(errors)
	}

	return nil
}

// StabilityCaseTaskInfoMultiError is an error wrapping multiple validation
// errors returned by StabilityCaseTaskInfo.ValidateAll() if the designated
// constraints aren't met.
type StabilityCaseTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityCaseTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityCaseTaskInfoMultiError) AllErrors() []error { return m }

// StabilityCaseTaskInfoValidationError is the validation error returned by
// StabilityCaseTaskInfo.Validate if the designated constraints aren't met.
type StabilityCaseTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityCaseTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityCaseTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityCaseTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityCaseTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityCaseTaskInfoValidationError) ErrorName() string {
	return "StabilityCaseTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityCaseTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityCaseTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityCaseTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityCaseTaskInfoValidationError{}

var _StabilityCaseTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?)")

var _StabilityCaseTaskInfo_PlanId_Pattern = regexp.MustCompile("(?:^stability_plan_id:.+?)")

var _StabilityCaseTaskInfo_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")

var _StabilityCaseTaskInfo_ExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")

var _StabilityCaseTaskInfo_PlanExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")

var _StabilityCaseTaskInfo_TriggerMode_NotInLookup = map[TriggerMode]struct{}{
	0: {},
}

var _StabilityCaseTaskInfo_DeviceType_InLookup = map[DeviceType]struct{}{
	1: {},
}

var _StabilityCaseTaskInfo_PlatformType_InLookup = map[PlatformType]struct{}{
	1: {},
}

// Validate checks the field values on SaveDevicePerfDataTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveDevicePerfDataTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveDevicePerfDataTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveDevicePerfDataTaskInfoMultiError, or nil if none found.
func (m *SaveDevicePerfDataTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveDevicePerfDataTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SaveDevicePerfDataTaskInfo_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SaveDevicePerfDataTaskInfo_ExecuteId_Pattern.MatchString(m.GetExecuteId()) {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "ExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SaveDevicePerfDataTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetUdid()); l < 1 || l > 64 {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "Udid",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _SaveDevicePerfDataTaskInfo_Usage_NotInLookup[m.GetUsage()]; ok {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "Usage",
			reason: "value must not be in list [DU_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _SaveDevicePerfDataTaskInfo_DataType_NotInLookup[m.GetDataType()]; ok {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "DataType",
			reason: "value must not be in list [PerfDataType_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInterval() <= 0 {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "Interval",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetSeries()); l < 1 || l > 16 {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "Series",
			reason: "value length must be between 1 and 16 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetUnit()); l < 0 || l > 8 {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "Unit",
			reason: "value length must be between 0 and 8 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetX()); l < 1 || l > 64 {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "X",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetY()); l < 1 || l > 64 {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "Y",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetExecutedBy()); l < 1 || l > 64 {
		err := SaveDevicePerfDataTaskInfoValidationError{
			field:  "ExecutedBy",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SaveDevicePerfDataTaskInfoMultiError(errors)
	}

	return nil
}

// SaveDevicePerfDataTaskInfoMultiError is an error wrapping multiple
// validation errors returned by SaveDevicePerfDataTaskInfo.ValidateAll() if
// the designated constraints aren't met.
type SaveDevicePerfDataTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveDevicePerfDataTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveDevicePerfDataTaskInfoMultiError) AllErrors() []error { return m }

// SaveDevicePerfDataTaskInfoValidationError is the validation error returned
// by SaveDevicePerfDataTaskInfo.Validate if the designated constraints aren't met.
type SaveDevicePerfDataTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveDevicePerfDataTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveDevicePerfDataTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveDevicePerfDataTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveDevicePerfDataTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveDevicePerfDataTaskInfoValidationError) ErrorName() string {
	return "SaveDevicePerfDataTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e SaveDevicePerfDataTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveDevicePerfDataTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveDevicePerfDataTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveDevicePerfDataTaskInfoValidationError{}

var _SaveDevicePerfDataTaskInfo_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")

var _SaveDevicePerfDataTaskInfo_ExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")

var _SaveDevicePerfDataTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _SaveDevicePerfDataTaskInfo_Usage_NotInLookup = map[DeviceUsage]struct{}{
	0: {},
}

var _SaveDevicePerfDataTaskInfo_DataType_NotInLookup = map[PerfDataType]struct{}{
	0: {},
}

// Validate checks the field values on SaveDeviceStepTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveDeviceStepTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveDeviceStepTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveDeviceStepTaskInfoMultiError, or nil if none found.
func (m *SaveDeviceStepTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveDeviceStepTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SaveDeviceStepTaskInfo_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := SaveDeviceStepTaskInfoValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SaveDeviceStepTaskInfo_ExecuteId_Pattern.MatchString(m.GetExecuteId()) {
		err := SaveDeviceStepTaskInfoValidationError{
			field:  "ExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SaveDeviceStepTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := SaveDeviceStepTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Udid

	if _, ok := _SaveDeviceStepTaskInfo_Usage_NotInLookup[m.GetUsage()]; ok {
		err := SaveDeviceStepTaskInfoValidationError{
			field:  "Usage",
			reason: "value must not be in list [DU_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Stage

	// no validation rules for Index

	// no validation rules for Name

	// no validation rules for Status

	// no validation rules for Content

	for idx, item := range m.GetArtifacts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SaveDeviceStepTaskInfoValidationError{
						field:  fmt.Sprintf("Artifacts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SaveDeviceStepTaskInfoValidationError{
						field:  fmt.Sprintf("Artifacts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SaveDeviceStepTaskInfoValidationError{
					field:  fmt.Sprintf("Artifacts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if l := utf8.RuneCountInString(m.GetExecutedBy()); l < 1 || l > 64 {
		err := SaveDeviceStepTaskInfoValidationError{
			field:  "ExecutedBy",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveDeviceStepTaskInfoValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveDeviceStepTaskInfoValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveDeviceStepTaskInfoValidationError{
				field:  "StartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaveDeviceStepTaskInfoValidationError{
					field:  "EndedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaveDeviceStepTaskInfoValidationError{
					field:  "EndedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaveDeviceStepTaskInfoValidationError{
				field:  "EndedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SaveDeviceStepTaskInfoMultiError(errors)
	}

	return nil
}

// SaveDeviceStepTaskInfoMultiError is an error wrapping multiple validation
// errors returned by SaveDeviceStepTaskInfo.ValidateAll() if the designated
// constraints aren't met.
type SaveDeviceStepTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveDeviceStepTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveDeviceStepTaskInfoMultiError) AllErrors() []error { return m }

// SaveDeviceStepTaskInfoValidationError is the validation error returned by
// SaveDeviceStepTaskInfo.Validate if the designated constraints aren't met.
type SaveDeviceStepTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveDeviceStepTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveDeviceStepTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveDeviceStepTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveDeviceStepTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveDeviceStepTaskInfoValidationError) ErrorName() string {
	return "SaveDeviceStepTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e SaveDeviceStepTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveDeviceStepTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveDeviceStepTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveDeviceStepTaskInfoValidationError{}

var _SaveDeviceStepTaskInfo_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")

var _SaveDeviceStepTaskInfo_ExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")

var _SaveDeviceStepTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _SaveDeviceStepTaskInfo_Usage_NotInLookup = map[DeviceUsage]struct{}{
	0: {},
}

// Validate checks the field values on UIAgentComponentTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UIAgentComponentTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentComponentTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentComponentTaskInfoMultiError, or nil if none found.
func (m *UIAgentComponentTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentComponentTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_UIAgentComponentTaskInfo_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UIAgentComponentTaskInfo_ExecuteId_Pattern.MatchString(m.GetExecuteId()) {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "ExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UIAgentComponentTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _UIAgentComponentTaskInfo_TriggerMode_NotInLookup[m.GetTriggerMode()]; ok {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "TriggerMode",
			reason: "value must not be in list [NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UIAgentComponentTaskInfo_ComponentId_Pattern.MatchString(m.GetComponentId()) {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "ComponentId",
			reason: "value does not match regex pattern \"(?:^ui_agent_component_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetComponentName()); l < 1 || l > 64 {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "ComponentName",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetApplicationConfig() == nil {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "ApplicationConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetApplicationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentTaskInfoValidationError{
					field:  "ApplicationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentTaskInfoValidationError{
					field:  "ApplicationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplicationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentTaskInfoValidationError{
				field:  "ApplicationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StepByStep

	if len(m.GetSteps()) < 1 {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "Steps",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetSteps() {
		_, _ = idx, item

		if item == nil {
			err := UIAgentComponentTaskInfoValidationError{
				field:  fmt.Sprintf("Steps[%v]", idx),
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentComponentTaskInfoValidationError{
						field:  fmt.Sprintf("Steps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentComponentTaskInfoValidationError{
						field:  fmt.Sprintf("Steps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentComponentTaskInfoValidationError{
					field:  fmt.Sprintf("Steps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetExpectation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentTaskInfoValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentTaskInfoValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentTaskInfoValidationError{
				field:  "Expectation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetVariables()) > 0 {

		for idx, item := range m.GetVariables() {
			_, _ = idx, item

			if item == nil {
				err := UIAgentComponentTaskInfoValidationError{
					field:  fmt.Sprintf("Variables[%v]", idx),
					reason: "value is required",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

			if all {
				switch v := interface{}(item).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, UIAgentComponentTaskInfoValidationError{
							field:  fmt.Sprintf("Variables[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, UIAgentComponentTaskInfoValidationError{
							field:  fmt.Sprintf("Variables[%v]", idx),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return UIAgentComponentTaskInfoValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}

	}

	if _, ok := _UIAgentComponentTaskInfo_ExecuteType_NotInLookup[m.GetExecuteType()]; ok {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "ExecuteType",
			reason: "value must not be in list [ET_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDevice() == nil {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "Device",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentTaskInfoValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentTaskInfoValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentTaskInfoValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Reinstall

	// no validation rules for Restart

	if l := utf8.RuneCountInString(m.GetExecutedBy()); l < 1 || l > 64 {
		err := UIAgentComponentTaskInfoValidationError{
			field:  "ExecutedBy",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UIAgentComponentTaskInfoMultiError(errors)
	}

	return nil
}

// UIAgentComponentTaskInfoMultiError is an error wrapping multiple validation
// errors returned by UIAgentComponentTaskInfo.ValidateAll() if the designated
// constraints aren't met.
type UIAgentComponentTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentComponentTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentComponentTaskInfoMultiError) AllErrors() []error { return m }

// UIAgentComponentTaskInfoValidationError is the validation error returned by
// UIAgentComponentTaskInfo.Validate if the designated constraints aren't met.
type UIAgentComponentTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentComponentTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentComponentTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentComponentTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentComponentTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentComponentTaskInfoValidationError) ErrorName() string {
	return "UIAgentComponentTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UIAgentComponentTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentComponentTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentComponentTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentComponentTaskInfoValidationError{}

var _UIAgentComponentTaskInfo_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")

var _UIAgentComponentTaskInfo_ExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")

var _UIAgentComponentTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?)")

var _UIAgentComponentTaskInfo_TriggerMode_NotInLookup = map[TriggerMode]struct{}{
	0: {},
}

var _UIAgentComponentTaskInfo_ComponentId_Pattern = regexp.MustCompile("(?:^ui_agent_component_id:.+?)")

var _UIAgentComponentTaskInfo_ExecuteType_NotInLookup = map[ExecuteType]struct{}{
	0: {},
}
