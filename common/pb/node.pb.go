// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/node.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NodeType int32

const (
	NodeType_NT_NULL      NodeType = 0 // NULL
	NodeType_NT_DIRECTORY NodeType = 1 // 目录
	NodeType_NT_FILE      NodeType = 2 // 文件
	NodeType_NT_PACKAGE   NodeType = 3 // 包
	NodeType_NT_MODULE    NodeType = 4 // 模块
	NodeType_NT_CLASS     NodeType = 5 // 类
	NodeType_NT_FUNCTION  NodeType = 6 // 函数
)

// Enum value maps for NodeType.
var (
	NodeType_name = map[int32]string{
		0: "NT_NULL",
		1: "NT_DIRECTORY",
		2: "NT_FILE",
		3: "NT_PACKAGE",
		4: "NT_MODULE",
		5: "NT_CLASS",
		6: "NT_FUNCTION",
	}
	NodeType_value = map[string]int32{
		"NT_NULL":      0,
		"NT_DIRECTORY": 1,
		"NT_FILE":      2,
		"NT_PACKAGE":   3,
		"NT_MODULE":    4,
		"NT_CLASS":     5,
		"NT_FUNCTION":  6,
	}
)

func (x NodeType) Enum() *NodeType {
	p := new(NodeType)
	*p = x
	return p
}

func (x NodeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_node_proto_enumTypes[0].Descriptor()
}

func (NodeType) Type() protoreflect.EnumType {
	return &file_common_node_proto_enumTypes[0]
}

func (x NodeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeType.Descriptor instead.
func (NodeType) EnumDescriptor() ([]byte, []int) {
	return file_common_node_proto_rawDescGZIP(), []int{0}
}

type Node struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Path          string                 `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`                       // 节点路径（相对于根路径）
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                       // 节点名称
	Alias         string                 `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`                     // 节点别名
	Type          NodeType               `protobuf:"varint,4,opt,name=type,proto3,enum=common.NodeType" json:"type,omitempty"` // 节点类型
	Tags          []string               `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty"`                       // 节点标签列表
	Children      []*Node                `protobuf:"bytes,6,rep,name=children,proto3" json:"children,omitempty"`               // 子节点列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Node) Reset() {
	*x = Node{}
	mi := &file_common_node_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_common_node_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_common_node_proto_rawDescGZIP(), []int{0}
}

func (x *Node) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *Node) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Node) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *Node) GetType() NodeType {
	if x != nil {
		return x.Type
	}
	return NodeType_NT_NULL
}

func (x *Node) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Node) GetChildren() []*Node {
	if x != nil {
		return x.Children
	}
	return nil
}

var File_common_node_proto protoreflect.FileDescriptor

var file_common_node_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0xa8, 0x01, 0x0a, 0x04,
	0x4e, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69,
	0x61, 0x73, 0x12, 0x24, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x28, 0x0a, 0x08,
	0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x63, 0x68,
	0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x2a, 0x74, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x54, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12,
	0x10, 0x0a, 0x0c, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x59, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x0e,
	0x0a, 0x0a, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x03, 0x12, 0x0d,
	0x0a, 0x09, 0x4e, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x0c, 0x0a,
	0x08, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x4e,
	0x54, 0x5f, 0x46, 0x55, 0x4e, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x42, 0x3c, 0x5a, 0x3a,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_common_node_proto_rawDescOnce sync.Once
	file_common_node_proto_rawDescData = file_common_node_proto_rawDesc
)

func file_common_node_proto_rawDescGZIP() []byte {
	file_common_node_proto_rawDescOnce.Do(func() {
		file_common_node_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_node_proto_rawDescData)
	})
	return file_common_node_proto_rawDescData
}

var file_common_node_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_common_node_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_common_node_proto_goTypes = []any{
	(NodeType)(0), // 0: common.NodeType
	(*Node)(nil),  // 1: common.Node
}
var file_common_node_proto_depIdxs = []int32{
	0, // 0: common.Node.type:type_name -> common.NodeType
	1, // 1: common.Node.children:type_name -> common.Node
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_common_node_proto_init() }
func file_common_node_proto_init() {
	if File_common_node_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_node_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_node_proto_goTypes,
		DependencyIndexes: file_common_node_proto_depIdxs,
		EnumInfos:         file_common_node_proto_enumTypes,
		MessageInfos:      file_common_node_proto_msgTypes,
	}.Build()
	File_common_node_proto = out.File
	file_common_node_proto_rawDesc = nil
	file_common_node_proto_goTypes = nil
	file_common_node_proto_depIdxs = nil
}
